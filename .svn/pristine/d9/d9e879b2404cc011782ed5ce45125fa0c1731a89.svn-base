package jp.ndsoft.carebase.cmn.api.report.service;

import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnIkensho1PByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnIkensho1POutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnIsiHeadPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnIsiHeadPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucIkensho1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucIkensho2SelectMapper;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServiceDbNoSaveData;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.report.dto.AttendingPhysician1H21ReportInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AttendingPhysician1H21ReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.model.AttendingPhysician1H21ReportParameterModel;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

/**
 * 
 * U01501_主治医意見書①(H21/4～)
 * 
 * <AUTHOR>
 */
@Service("AttendingPhysician1H21Report")
public class AttendingPhysician1H21ReportService
        extends
        PdfReportServiceImpl<AttendingPhysician1H21ReportParameterModel, AttendingPhysician1H21ReportServiceOutDto> {

    /**
     * ロガー.
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // 医師意見書ヘッダ情報取得
    @Autowired
    private CpnTucIkensho1SelectMapper cpnTucIkensho1SelectMapper;
    // 医師意見書データ①情報取得
    @Autowired
    private CpnTucIkensho2SelectMapper cpnTucIkensho2SelectMapper;
    // 利用者（年齢以外）情報取得
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;
    /** 設定の書込（NEXのパソコン単位の設定） */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;
    /** 設定の書込（NEXのパソコン単位の設定） */
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    /**
     * チェック仕様
     * 
     * @param inDto 入力データ
     * @throws Exception 例外
     */
    @Override
    protected void checkProcess(AttendingPhysician1H21ReportParameterModel inDto) throws Exception {
        // API仕様書のチェック仕様なし
        super.checkProcess(inDto);
    }

    /**
     * 帳票出力
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final AttendingPhysician1H21ReportParameterModel inDto,
            final AttendingPhysician1H21ReportServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final AttendingPhysician1H21ReportInDto reportParameter = (AttendingPhysician1H21ReportInDto) getReportParameters(
                inDto, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();
        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_U01501_ATTENDING_PHYSICIAN1_H21)));

        // コンパイル
        final JasperReport jasperFile = JasperCompileManager.compileReport(is);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile, reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(inDto, jasperPrint);

        super.setFilepath(inDto, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }

    /**
     * 帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(AttendingPhysician1H21ReportParameterModel inDto,
            AttendingPhysician1H21ReportServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);

        AttendingPhysician1H21ReportInDto model = getAttendingPhysician1H21ReportParameters(inDto);

        // ノート情報格納配列
        List<AttendingPhysician1H21ReportInDto> reportInfoList = new ArrayList<AttendingPhysician1H21ReportInDto>();

        reportInfoList.add(model);

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(reportInfoList);
        model.setDataSource(dataSource);

        LOG.info(Constants.END);
        return model;
    }

    /**
     * U01501_主治医意見書①(H21/4～)の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータ
     */
    private AttendingPhysician1H21ReportInDto getAttendingPhysician1H21ReportParameters(
            AttendingPhysician1H21ReportParameterModel inDto) {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 主治医意見書①帳票情報
        AttendingPhysician1H21ReportInDto model = new AttendingPhysician1H21ReportInDto();

        // DB未保存画面項目
        PrintReportServiceDbNoSaveData dbNoSaveData = inDto.getDbNoSaveData();
        // 記入日
        String createYmd = CommonConstants.BLANK_STRING;
        // リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ = "1" の場合、帳票用データを設定する。
        if (CommonConstants.STR_1.equals(dbNoSaveData != null ? dbNoSaveData.getEmptyFlg() : null)) {
            // 帳票用データを設定する
            getDefaultIkenshoInfo(model);

            // リクエストパラメータ.データ.印刷対象履歴.記入日が空の場合
            if (CommonConstants.BLANK_STRING.equals(inDto.getPrintSubjectHistory().getCreateYmd())) {
                // リクエストパラメータ.データ.システム日付を設定する
                createYmd = inDto.getSystemDate();
            } else {
                // リクエストパラメータ.データ.印刷対象履歴.記入日を設定する
                createYmd = inDto.getPrintSubjectHistory().getCreateYmd();
            }

            // 印刷用日付
            model.setCreateYmd(nds3GkFunc01Logic.blankDate(createYmd));

            // 利用者（年齢以外）情報の取得
            getDefaultUserInfo(model);
        }
        // リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ = "0" の場合、主治医意見書①情報の取得。
        else {
            // 3.1 医師意見書ヘッダ情報の取得
            createYmd = getIkenshoLetterInfo(inDto, model);

            // 3.2 医師意見書データの取得
            getIkenshoInfo(inDto, model);

            // 3.3 利用者（年齢以外）情報の取得
            getUserInfo(inDto, model, createYmd);

        }

        // 記入用シートを印刷するフラグ = リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ
        model.setEmptyFlg(dbNoSaveData != null ? CommonDtoUtil.strValToInt(dbNoSaveData.getEmptyFlg())
                : CommonConstants.NUMBER_ZERO);

        // 指定日印刷区分 = リクエストパラメータ.データ.印刷設定.日付表示有無
        Integer prnDate = CommonDtoUtil.strValToInt(inDto.getPrintSet().getPrnDate());

        // 指定日印刷区分
        model.setShiTeiKbn(prnDate);

        // リクエストパラメータ.データ.印刷設定.日付表示有無＝2:指定日印刷の場合
        if (CommonConstants.PRN_DATE_DESIGNATED_DAY_PRINTING.equals(prnDate)) {
            // 和暦日付
            model.setSelDate(getCmpS2wjEz(inDto.getDbNoSaveData().getSelectDate()));
        }
        // リクエストパラメータ.データ.印刷設定.日付表示有無＝3:日付空欄印刷の場合
        else if (CommonConstants.PRN_DATE_DAILY_PAYMENT_BLANK_PRINTING.equals(prnDate)) {
            // 印刷用日付
            model.setSelDate(nds3GkFunc01Logic.blankDate(inDto.getSystemDate()));
        }
        // リクエストパラメータ.データ.印刷設定.日付表示有無＝1:印刷しないの場合
        else {
            model.setSelDate(CommonConstants.BLANK_STRING);
        }

        // 事業所名 = リクエストパラメータ.データ.事業所名
        model.setJigyoKnj(inDto.getJigyoKnj());

        // API定義の処理「3.1」で取得した記入日が"2015/04/01"より前の場合
        if (CommonConstants.YMD_20150401.compareTo(createYmd) > 0) {
            // "主治医として、本意見書が介護サービス計画作成に利用されることに"
            model.setDoctorOpinionLetterLbl(CommonConstants.DOCTOR_OPINION_LETTER_LBL);
            // "(３) 認知症の周辺症状"
            model.setShuhenLbl(CommonConstants.SHUHEN_LBL);
            // 周辺症状説明左パッディング調整フラグ = 周辺症状ラベルが"(３) 認知症の行動・心理症状(BPSD)"の場合、1 以外の場合、0
            model.setShuhenLeftPaddingFlg(CommonConstants.NUMBER_ZERO);
        }
        // API定義の処理「3.1」で取得した記入日が"2015/04/01"以上、かつ、記入日が"2018/04/01"より前の場合
        else if (CommonConstants.YMD_20150401.compareTo(createYmd) <= 0
                && CommonConstants.CREATION_DATE_20180401.compareTo(createYmd) > 0) {
            // "主治医として、本意見書が介護サービス計画作成等に利用されることに"
            model.setDoctorOpinionLetterLbl(CommonConstants.DOCTOR_OPINION_LETTER_LBL_ESC);
            // "(３) 認知症の周辺症状"
            model.setShuhenLbl(CommonConstants.SHUHEN_LBL);
            // 周辺症状説明左パッディング調整フラグ = 周辺症状ラベルが"(３) 認知症の行動・心理症状(BPSD)"の場合、1 以外の場合、0
            model.setShuhenLeftPaddingFlg(CommonConstants.NUMBER_ZERO);
        }
        // API定義の処理「3.1」で取得した記入日が"2015/04/01"以上の場合
        else {
            // "主治医として、本意見書が介護サービス計画作成等に利用されることに"
            model.setDoctorOpinionLetterLbl(CommonConstants.DOCTOR_OPINION_LETTER_LBL_ESC);
            // "(３) 認知症の行動・心理症状(BPSD)"
            model.setShuhenLbl(CommonConstants.SHUHEN_LBL_BPSD);
            // 周辺症状説明左パッディング調整フラグ = 周辺症状ラベルが"(３) 認知症の行動・心理症状(BPSD)"の場合、1 以外の場合、0
            model.setShuhenLeftPaddingFlg(CommonConstants.NUMBER_ONE);
        }

        return model;
    }

    /**
     * 医師意見書ヘッダ情報取得
     * 
     * @param inDto 入力データ
     * @param model 帳票用データ詳細
     * @return 記入日
     */
    private String getIkenshoLetterInfo(
            AttendingPhysician1H21ReportParameterModel inDto,
            AttendingPhysician1H21ReportInDto model) {
        // 記入日
        String createYmd = CommonConstants.BLANK_STRING;
        // 医師意見書ヘッダ情報取得
        CpnIsiHeadPByCriteriaInEntity entity = new CpnIsiHeadPByCriteriaInEntity();
        // 医師意見書ID = リクエストパラメータ.データ.印刷対象履歴.履歴ID
        entity.setRirekiId(CommonDtoUtil.strValToInt(inDto.getPrintSubjectHistory().getRirekiId()));
        // 医師意見書ヘッダ情報
        List<CpnIsiHeadPOutEntity> list = cpnTucIkensho1SelectMapper.findCpnIsiHeadPByCriteria(entity);
        if (list != null && list.size() > 0) {
            // リクエストパラメータ.データ.DB未保存画面項目
            createYmd = list.get(CommonConstants.NUMBER_ZERO).getCreateYmd();

            // 和暦日付
            model.setCreateYmd(getCmpS2wjEz(createYmd));

        }
        return createYmd;
    }

    /**
     * 利用者（年齢以外）情報取得
     * 
     * @param inDto 入力データ
     * @param model 帳票用データ詳細
     */
    private void getUserInfo(
            AttendingPhysician1H21ReportParameterModel inDto,
            AttendingPhysician1H21ReportInDto model,
            String createYmd) {
        // 検索条件がなし、医師意見書データ①情報の取得終了
        String userId = inDto.getPrintSubjectHistory().getUserId();
        if (userId.isEmpty()) {
            getDefaultUserInfo(model);
            return;
        }
        // 利用者（年齢以外）情報取得
        RiyoshaKihonByCriteriaInEntity entity = new RiyoshaKihonByCriteriaInEntity();
        // 利用者ID
        entity.setAlUserid(CommonDtoUtil.strValToInt(userId));
        List<RiyoshaKihonOutEntity> userList = comTucUserSelectMapper.findRiyoshaKihonByCriteria(entity);
        if (userList != null && userList.size() > 0) {
            RiyoshaKihonOutEntity data = userList.get(0);
            // フリガナ（姓）
            model.setName1Kana(data.getName1Kana());
            // フリガナ（名）
            model.setName2Kana(data.getName2Kana());
            // 氏名（姓）
            model.setName1Knj(data.getName1Knj());
            // 氏名（名）
            model.setName2Knj(data.getName2Knj());
            // 生年月日 和暦日付
            model.setBirthdayYmd(getCmpS2wjEz(data.getBirthdayYmd()));
            // 年齢
            model.setAge(nds3GkFunc01Logic.getDateDiffYY(data.getBirthdayYmd(), createYmd));
            // 性別
            model.setSex(CommonDtoUtil.strValToInt(data.getSex()));
            // 郵便番号
            model.setZip(data.getZip().replaceAll("(\\d{3})(\\d{4})", "$1-$2"));
            // 住所
            model.setAddressKnj(data.getAddressKnj());
            // 電話番号
            model.setTel(data.getTel());
        }
    }

    /**
     * 医師意見書データ①情報取得
     * 
     * @param inDto 入力データ
     * @param model 帳票用データ詳細
     */
    private void getIkenshoInfo(
            AttendingPhysician1H21ReportParameterModel inDto,
            AttendingPhysician1H21ReportInDto model) {
        // 検索条件がなし、医師意見書データ①情報の取得終了
        String rirekiId = inDto.getPrintSubjectHistory().getRirekiId();
        if (rirekiId.isEmpty()) {
            getDefaultIkenshoInfo(model);
            return;
        }

        // 医師意見書データ①情報の取得
        CpnIkensho1PByCriteriaInEntity entity = new CpnIkensho1PByCriteriaInEntity();
        // 医師意見書ID = リクエストパラメータ.データ.印刷対象履歴.履歴ID
        entity.setRirekiId(CommonDtoUtil.strValToInt(inDto.getPrintSubjectHistory().getRirekiId()));
        // 医師意見書データ①情報
        List<CpnIkensho1POutEntity> list = cpnTucIkensho2SelectMapper
                .findCpnIkensho1PByCriteria(entity);
        if (list == null || list.size() == 0) {
            getDefaultIkenshoInfo(model);
        } else {
            // 印鑑欄情報の取得
            CpnIkensho1POutEntity ikensho1PData = list.get(0);
            // 医師名前
            model.setIshiNameKnj(ikensho1PData.getIshiNameKnj());
            // 医療機関名
            model.setIryouKikanKnj(ikensho1PData.getIryouKikanKnj());
            // 医療機関所在地
            model.setIryouKikanAddress(ikensho1PData.getIryouKikanAddress());
            // 医療機関TEL
            model.setIshiTel(ikensho1PData.getIshiTel());
            // 医療機関FAX
            model.setIshiFax(ikensho1PData.getIshiFax());
            // 医師意見
            model.setIshiDoui(CommonDtoUtil.shortValToInt(ikensho1PData.getIshiDoui()));
            // 最終診察日付 和暦日付
            model.setSinsatuYmd(getCmpS2wjEz(ikensho1PData.getSinsatuYmd()));
            // 意見書回数
            model.setIkenshoKaisuu(CommonDtoUtil.shortValToInt(ikensho1PData.getIkenshoKaisuu()));
            // 他科受診
            model.setElseJusinFlg(CommonDtoUtil.shortValToInt(ikensho1PData.getElseJusinFlg()));
            // 内科選択フラッグ
            model.setElseJusin1Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getElseJusin1Flg()));
            // 精神科選択フラッグ
            model.setElseJusin2Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getElseJusin2Flg()));
            // 外科選択フラッグ
            model.setElseJusin3Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getElseJusin3Flg()));
            // 整形外科選択フラッグ
            model.setElseJusin4Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getElseJusin4Flg()));
            // 脳神精外科選択フラッグ
            model.setElseJusin5Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getElseJusin5Flg()));
            // 皮膚科選択フラッグ
            model.setElseJusin6Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getElseJusin6Flg()));
            // 泌尿器科選択フラッグ
            model.setElseJusin7Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getElseJusin7Flg()));
            // 婦人科選択フラッグ
            model.setElseJusin8Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getElseJusin8Flg()));
            // 眼科選択フラッグ
            model.setElseJusin9Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getElseJusin9Flg()));
            // 耳鼻咽喉科選択フラッグ
            model.setElseJusin10Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getElseJusin10Flg()));
            // リハビリテーション科選択フラッグ
            model.setElseJusin11Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getElseJusin11Flg()));
            // 歯科選択フラッグ
            model.setElseJusin12Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getElseJusin12Flg()));
            // その他選択フラッグ
            model.setElseJusin13Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getElseJusin13Flg()));
            // その他科の名前
            model.setElseJusinKnj(ikensho1PData.getElseJusinKnj());
            // 診断名前1
            model.setSindanName1Knj(ikensho1PData.getSindanName1Knj());
            // 診断1の日付
            model.setSindan1Ymd(ikensho1PData.getSindan1Ymd());
            // 診断名前2
            model.setSindanName2Knj(ikensho1PData.getSindanName2Knj());
            // 診断2の日付
            model.setSindan2Ymd(ikensho1PData.getSindan2Ymd());
            // 診断名前3
            model.setSindanName3Knj(ikensho1PData.getSindanName3Knj());
            // 診断3の日付
            model.setSindan3Ymd(ikensho1PData.getSindan3Ymd());
            // 安定
            model.setAnteiFlg(CommonDtoUtil.shortValToInt(ikensho1PData.getAnteiFlg()));
            // 不安定内容
            model.setHuanteiKnj(ikensho1PData.getHuanteiKnj());
            // 生活機能低下原因
            model.setReasonMemoKnj(ikensho1PData.getReasonMemoKnj());
            // 特別医療関連行為1
            model.setTokubetu1Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getTokubetu1Flg()));
            // 特別医療関連行為2
            model.setTokubetu2Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getTokubetu2Flg()));
            // 特別医療関連行為3
            model.setTokubetu3Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getTokubetu3Flg()));
            // 特別医療関連行為4
            model.setTokubetu4Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getTokubetu4Flg()));
            // 特別医療関連行為5
            model.setTokubetu5Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getTokubetu5Flg()));
            // 特別医療関連行為6
            model.setTokubetu6Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getTokubetu6Flg()));
            // 特別医療関連行為7
            model.setTokubetu7Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getTokubetu7Flg()));
            // 特別医療関連行為8
            model.setTokubetu8Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getTokubetu8Flg()));
            // 特別医療関連行為9
            model.setTokubetu9Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getTokubetu9Flg()));
            // 特別医療関連行為10
            model.setTokubetu10Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getTokubetu10Flg()));
            // 特別医療関連行為11
            model.setTokubetu11Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getTokubetu11Flg()));
            // 特別医療関連行為12
            model.setTokubetu12Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getTokubetu12Flg()));
            // 障害高齢者の日常生活自立度
            model.setNeta1Cd(CommonDtoUtil.shortValToInt(ikensho1PData.getNeta1Cd()));
            // 認知症高齢者の日常生活自立度
            model.setNeta2Cd(CommonDtoUtil.shortValToInt(ikensho1PData.getNeta2Cd()));
            // 短期記憶
            model.setChukaku1Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getChukaku1Flg()));
            // 認知能力
            model.setChukaku2Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getChukaku2Flg()));
            // 伝達能力
            model.setChukaku3Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getChukaku3Flg()));
            // 周辺症状フラッグ
            model.setShuhenFlg(CommonDtoUtil.shortValToInt(ikensho1PData.getShuhenFlg()));
            // 周辺症状1選択フラッグ
            model.setShuhen1Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getShuhen1Flg()));
            // 周辺症状2選択フラッグ
            model.setShuhen2Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getShuhen2Flg()));
            // 周辺症状3選択フラッグ
            model.setShuhen3Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getShuhen3Flg()));
            // 周辺症状4選択フラッグ
            model.setShuhen4Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getShuhen4Flg()));
            // 周辺症状5選択フラッグ
            model.setShuhen5Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getShuhen5Flg()));
            // 周辺症状6選択フラッグ
            model.setShuhen6Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getShuhen6Flg()));
            // 周辺症状7選択フラッグ
            model.setShuhen7Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getShuhen7Flg()));
            // 周辺症状8選択フラッグ
            model.setShuhen8Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getShuhen8Flg()));
            // 周辺症状9選択フラッグ
            model.setShuhen9Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getShuhen9Flg()));
            // 周辺症状10選択フラッグ
            model.setShuhen10Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getShuhen10Flg()));
            // 周辺症状11選択フラッグ
            model.setShuhen11Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getShuhen11Flg()));
            // 周辺症状12その他選択フラッグ
            model.setShuhen12Flg(CommonDtoUtil.shortValToInt(ikensho1PData.getShuhen12Flg()));
            // 周辺症状12その他
            model.setShuhen12Knj(ikensho1PData.getShuhen12Knj());
            // 精神症状フラッグ
            model.setSeisinFlg(CommonDtoUtil.shortValToInt(ikensho1PData.getSeisinFlg()));
            // 症状名前
            model.setSeisinKnj(ikensho1PData.getSeisinKnj());
            // 受診フラグ
            model.setSeisinJusinFlg(CommonDtoUtil.shortValToInt(ikensho1PData.getSeisinJusinFlg()));
            // 受診科の名前
            model.setSeisinJusinKnj(ikensho1PData.getSeisinJusinKnj());
        }
    }

    /**
     * リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ = "1" の場合、帳票用データを設定する
     * 
     * @param model 帳票用データ詳細
     */
    private void getDefaultUserInfo(AttendingPhysician1H21ReportInDto model) {
        // フリガナ（姓）
        model.setName1Kana(CommonConstants.BLANK_STRING);
        // フリガナ（名）
        model.setName2Kana(CommonConstants.BLANK_STRING);
        // 氏名（姓）
        model.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        model.setName2Knj(CommonConstants.BLANK_STRING);
        // 生年月日
        model.setBirthdayYmd(CommonConstants.BLANK_STRING);
        // 年齢
        model.setAge(CommonConstants.INTEGER_NULL);
        // 性別
        model.setSex(CommonConstants.INTEGER_NULL);
        // 郵便番号
        model.setZip(CommonConstants.DEFAULT_ZIP);
        // 住所
        model.setAddressKnj(CommonConstants.BLANK_STRING);
        // 電話番号
        model.setTel(CommonConstants.BLANK_STRING);
    }

    /**
     * リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ = "1" の場合、帳票用データを設定する
     * 
     * @param model 帳票用データ詳細
     */
    private void getDefaultIkenshoInfo(AttendingPhysician1H21ReportInDto model) {
        // 医師意見
        model.setIshiDoui(CommonConstants.INTEGER_NULL);
        // 医師名前
        model.setIshiNameKnj(CommonConstants.BLANK_STRING);
        // 医療機関名
        model.setIryouKikanKnj(CommonConstants.BLANK_STRING);
        // 医療機関所在地
        model.setIryouKikanAddress(CommonConstants.BLANK_STRING);
        // 医療機関TEL
        model.setIshiTel(CommonConstants.BLANK_STRING);
        // 医療機関FAX
        model.setIshiFax(CommonConstants.BLANK_STRING);
        // 最終診察日付
        model.setSinsatuYmd(CommonConstants.BLANK_STRING);
        // 意見書回数
        model.setIkenshoKaisuu(CommonConstants.INTEGER_NULL);
        // 他科受診
        model.setElseJusinFlg(CommonConstants.INTEGER_NULL);
        // 内科選択フラッグ
        model.setElseJusin1Flg(CommonConstants.INTEGER_NULL);
        // 精神科選択フラッグ
        model.setElseJusin2Flg(CommonConstants.INTEGER_NULL);
        // 外科選択フラッグ
        model.setElseJusin3Flg(CommonConstants.INTEGER_NULL);
        // 整形外科選択フラッグ
        model.setElseJusin4Flg(CommonConstants.INTEGER_NULL);
        // 脳神精外科選択フラッグ
        model.setElseJusin5Flg(CommonConstants.INTEGER_NULL);
        // 皮膚科選択フラッグ
        model.setElseJusin6Flg(CommonConstants.INTEGER_NULL);
        // 泌尿器科選択フラッグ
        model.setElseJusin7Flg(CommonConstants.INTEGER_NULL);
        // 婦人科選択フラッグ
        model.setElseJusin8Flg(CommonConstants.INTEGER_NULL);
        // 眼科選択フラッグ
        model.setElseJusin9Flg(CommonConstants.INTEGER_NULL);
        // 耳鼻咽喉科選択フラッグ
        model.setElseJusin10Flg(CommonConstants.INTEGER_NULL);
        // リハビリテーション科選択フラッグ
        model.setElseJusin11Flg(CommonConstants.INTEGER_NULL);
        // 歯科選択フラッグ
        model.setElseJusin12Flg(CommonConstants.INTEGER_NULL);
        // その他選択フラッグ
        model.setElseJusin13Flg(CommonConstants.INTEGER_NULL);
        // その他科の名前
        model.setElseJusinKnj(CommonConstants.BLANK_STRING);
        // 診断名前1
        model.setSindanName1Knj(CommonConstants.BLANK_STRING);
        // 診断1の日付
        model.setSindan1Ymd(CommonConstants.BLANK_STRING);
        // 診断名前2
        model.setSindanName2Knj(CommonConstants.BLANK_STRING);
        // 診断2の日付
        model.setSindan2Ymd(CommonConstants.BLANK_STRING);
        // 診断名前3
        model.setSindanName3Knj(CommonConstants.BLANK_STRING);
        // 診断3の日付
        model.setSindan3Ymd(CommonConstants.BLANK_STRING);
        // 安定
        model.setAnteiFlg(CommonConstants.INTEGER_NULL);
        // 不安定内容
        model.setHuanteiKnj(CommonConstants.BLANK_STRING);
        // 生活機能低下原因
        model.setReasonMemoKnj(CommonConstants.BLANK_STRING);
        // 特別医療関連行為1
        model.setTokubetu1Flg(CommonConstants.INTEGER_NULL);
        // 特別医療関連行為2
        model.setTokubetu2Flg(CommonConstants.INTEGER_NULL);
        // 特別医療関連行為3
        model.setTokubetu3Flg(CommonConstants.INTEGER_NULL);
        // 特別医療関連行為4
        model.setTokubetu4Flg(CommonConstants.INTEGER_NULL);
        // 特別医療関連行為5
        model.setTokubetu5Flg(CommonConstants.INTEGER_NULL);
        // 特別医療関連行為6
        model.setTokubetu6Flg(CommonConstants.INTEGER_NULL);
        // 特別医療関連行為7
        model.setTokubetu7Flg(CommonConstants.INTEGER_NULL);
        // 特別医療関連行為8
        model.setTokubetu8Flg(CommonConstants.INTEGER_NULL);
        // 特別医療関連行為9
        model.setTokubetu9Flg(CommonConstants.INTEGER_NULL);
        // 特別医療関連行為10
        model.setTokubetu10Flg(CommonConstants.INTEGER_NULL);
        // 特別医療関連行為11
        model.setTokubetu11Flg(CommonConstants.INTEGER_NULL);
        // 特別医療関連行為12
        model.setTokubetu12Flg(CommonConstants.INTEGER_NULL);
        // 障害高齢者の日常生活自立度
        model.setNeta1Cd(CommonConstants.INTEGER_NULL);
        // 認知症高齢者の日常生活自立度
        model.setNeta2Cd(CommonConstants.INTEGER_NULL);
        // 短期記憶
        model.setChukaku1Flg(CommonConstants.INTEGER_NULL);
        // 認知能力
        model.setChukaku2Flg(CommonConstants.INTEGER_NULL);
        // 伝達能力
        model.setChukaku3Flg(CommonConstants.INTEGER_NULL);
        // 周辺症状フラッグ
        model.setShuhenFlg(CommonConstants.INTEGER_NULL);
        // 周辺症状1選択フラッグ
        model.setShuhen1Flg(CommonConstants.INTEGER_NULL);
        // 周辺症状2選択フラッグ
        model.setShuhen2Flg(CommonConstants.INTEGER_NULL);
        // 周辺症状3選択フラッグ
        model.setShuhen3Flg(CommonConstants.INTEGER_NULL);
        // 周辺症状4選択フラッグ
        model.setShuhen4Flg(CommonConstants.INTEGER_NULL);
        // 周辺症状5選択フラッグ
        model.setShuhen5Flg(CommonConstants.INTEGER_NULL);
        // 周辺症状6選択フラッグ
        model.setShuhen6Flg(CommonConstants.INTEGER_NULL);
        // 周辺症状7選択フラッグ
        model.setShuhen7Flg(CommonConstants.INTEGER_NULL);
        // 周辺症状8選択フラッグ
        model.setShuhen8Flg(CommonConstants.INTEGER_NULL);
        // 周辺症状9選択フラッグ
        model.setShuhen9Flg(CommonConstants.INTEGER_NULL);
        // 周辺症状10選択フラッグ
        model.setShuhen10Flg(CommonConstants.INTEGER_NULL);
        // 周辺症状11選択フラッグ
        model.setShuhen11Flg(CommonConstants.INTEGER_NULL);
        // 周辺症状12その他選択フラッグ
        model.setShuhen12Flg(CommonConstants.INTEGER_NULL);
        // 周辺症状12その他
        model.setShuhen12Knj(CommonConstants.BLANK_STRING);
        // 精神症状フラッグ
        model.setSeisinFlg(CommonConstants.INTEGER_NULL);
        // 症状名前
        model.setSeisinKnj(CommonConstants.BLANK_STRING);
        // 受診フラグ
        model.setSeisinJusinFlg(CommonConstants.INTEGER_NULL);
        // 受診科の名前
        model.setSeisinJusinKnj(CommonConstants.BLANK_STRING);
    }

    /**
     * 和暦変換処理
     * 共通関数補足の和暦日付
     * 
     * @param ymd 年月日
     */
    private String getCmpS2wjEz(String ymd) {
        String dateValue = (ymd != null && !ymd.trim().isEmpty()) ? ymd : CommonConstants.BLANK_STRING;
        return kghCmpF01Logic.getCmpS2wjEz(dateValue, CommonConstants.NUMBER_ONE);
    }
}
