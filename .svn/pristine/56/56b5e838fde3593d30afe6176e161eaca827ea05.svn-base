package jp.ndsoft.carebase.cmn.api.report.service;

import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnIkensho2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnIkensho2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnIsiHeadPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnIsiHeadPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucUserNameKnj2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucUserNameKnj2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucIkensho1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucIkensho3SelectMapper;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServiceDbNoSaveData;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.report.dto.AttendingPhysician2H21ReportInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AttendingPhysician2H21ReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.model.AttendingPhysician2H21ReportParameterModel;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * U01501_主治医意見書②(H21/4～)
 *
 * <AUTHOR>
 */
@Service("AttendingPhysician2H21Report")
public class AttendingPhysician2H21ReportService
        extends
        PdfReportServiceImpl<AttendingPhysician2H21ReportParameterModel, AttendingPhysician2H21ReportServiceOutDto> {

    /**
     * ロガー.
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // 医師意見書ヘッダ情報取得
    @Autowired
    private CpnTucIkensho1SelectMapper cpnTucIkensho1SelectMapper;
    // 医師意見書データ②情報取得
    @Autowired
    private CpnTucIkensho3SelectMapper cpnTucIkensho3SelectMapper;
    // 利用者（年齢以外）情報取得
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;
    /** 設定の書込（NEXのパソコン単位の設定） */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;
    /** 設定の書込（NEXのパソコン単位の設定） */
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    /**
     * チェック仕様
     *
     * @param inDto 入力データ
     * @throws Exception 例外
     */
    @Override
    protected void checkProcess(AttendingPhysician2H21ReportParameterModel inDto) throws Exception {
        // API仕様書のチェック仕様なし
        super.checkProcess(inDto);
    }

    /**
     * 帳票出力
     *
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final AttendingPhysician2H21ReportParameterModel inDto,
            final AttendingPhysician2H21ReportServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final AttendingPhysician2H21ReportInDto reportParameter = (AttendingPhysician2H21ReportInDto) getReportParameters(
                inDto, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(getFwProps(),
                        ReportConstants.JRXML_U01501_ATTENDING_PHYSICIAN2_H21)));

        // コンパイル
        final JasperReport jasperFile = JasperCompileManager.compileReport(is);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile,
                reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(inDto, jasperPrint);

        super.setFilepath(inDto, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }

    /**
     * 帳票パラメータ取得
     *
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(AttendingPhysician2H21ReportParameterModel inDto,
            AttendingPhysician2H21ReportServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);

        AttendingPhysician2H21ReportInDto model = getAttendingPhysician2H21ReportParameters(inDto);

        // ノート情報格納配列
        List<AttendingPhysician2H21ReportInDto> reportInfoList = new ArrayList<AttendingPhysician2H21ReportInDto>();

        reportInfoList.add(model);

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(reportInfoList);
        model.setDataSource(dataSource);

        LOG.info(Constants.END);
        return model;
    }

    /**
     * U01501_主治医意見書②(H21/4～)の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータ
     */
    private AttendingPhysician2H21ReportInDto getAttendingPhysician2H21ReportParameters(
            AttendingPhysician2H21ReportParameterModel inDto) {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 主治医意見書②帳票情報
        AttendingPhysician2H21ReportInDto model = new AttendingPhysician2H21ReportInDto();

        // 記入日
        String createYmd = CommonConstants.BLANK_STRING;
        // リクエストパラメータ.データ.DB未保存画面項目
        PrintReportServiceDbNoSaveData dbNoSaveData = inDto.getDbNoSaveData();
        // リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ = "1" の場合
        if (CommonConstants.STR_1.equals(dbNoSaveData != null ? dbNoSaveData.getEmptyFlg() : null)) {
            // 帳票用データを設定する
            getDefaultIkenshoInfo(model);

            // リクエストパラメータ.データ.印刷対象履歴.記入日が空の場合
            if (CommonConstants.BLANK_STRING.equals(inDto.getPrintSubjectHistory().getCreateYmd())) {
                // リクエストパラメータ.データ.システム日付を設定する
                createYmd = inDto.getSystemDate();
            } else {
                // リクエストパラメータ.データ.印刷対象履歴.記入日を設定する
                createYmd = inDto.getPrintSubjectHistory().getCreateYmd();

            }
            // 印刷用日付
            model.setCreateYmd(nds3GkFunc01Logic.blankDate(createYmd));

            // 利用者名
            model.setUserNameKnj(CommonConstants.BLANK_STRING);
            // 利用者長さ超長フラグ 0
            model.setUserNameLengthUltraLongFlg(CommonConstants.NUMBER_ZERO);

            // H30表示フラグ 1
            model.setH30VisibleFlg(CommonConstants.NUMBER_1);
            // 事業所名 ""
            model.setJigyoKnj(CommonConstants.BLANK_STRING);
        }
        // リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ = "0" の場合、主治医意見書①情報の取得。
        else {
            // 3.1 医師意見書ヘッダ情報の取得
            createYmd = getIkenshoLetterInfo(inDto, model);

            // 3.2 医師意見書データの取得
            getIkenshoInfo(inDto, model);

            // 3.3 利用者情報の取得
            getUserInfo(inDto, model);

            // 事業所名 = リクエストパラメータ.データ.事業所名
            model.setJigyoKnj(inDto.getJigyoKnj());
            // 記入日が"2018/10/01"以上の場合
            if (CommonConstants.YMD_20181001.compareTo(createYmd) <= 0) {
                // H30表示フラグ 1
                model.setH30VisibleFlg(CommonConstants.NUMBER_1);
            }
            // 以外の場合
            else {
                // H30表示フラグ 0
                model.setH30VisibleFlg(CommonConstants.NUMBER_0);
            }
        }

        // 指定日印刷区分 = リクエストパラメータ.データ.印刷設定.日付表示有無
        Integer prnDate = CommonDtoUtil.strValToInt(inDto.getPrintSet().getPrnDate());
        model.setShiTeiKbn(prnDate);

        // リクエストパラメータ.データ.印刷設定.日付表示有無＝2:指定日印刷の場合
        if (CommonConstants.PRN_DATE_DESIGNATED_DAY_PRINTING.equals(prnDate)) {
            // 和暦日付
            model.setSelDate(getCmpS2wjEz(inDto.getDbNoSaveData().getSelectDate()));
        }
        // リクエストパラメータ.データ.印刷設定.日付表示有無＝3:日付空欄印刷の場合
        else if (CommonConstants.PRN_DATE_DAILY_PAYMENT_BLANK_PRINTING.equals(prnDate)) {
            // 印刷用日付
            model.setSelDate(nds3GkFunc01Logic.blankDate(inDto.getSystemDate()));
        }
        // リクエストパラメータ.データ.印刷設定.日付表示有無＝1:印刷しないの場合
        else {
            model.setSelDate(CommonConstants.BLANK_STRING);
        }

        // 欄外利用者表示フラグ = リクエストパラメータ.データ.印刷設定.パラメータ05
        model.setUserNameFlg(CommonDtoUtil.strValToInt(inDto.getPrintSet().getParam05()));

        // 敬称
        // リクエストパラメータ.データ.印刷設定.パラメータ03 = "1" の場合
        if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam03())) {
            // 敬称 = リクエストパラメータ.データ.印刷設定.パラメータ04
            model.setKeisho(inDto.getPrintSet().getParam04());
        } else {
            // リクエストパラメータ.データ.初期設定マスタの情報.印刷帳票の敬称オプション＝1の場合、
            if (CommonConstants.STR_1.equals(inDto.getInitMasterObj().getKeishoFlg())) {
                // 敬称＝リクエストパラメータ.データ.初期設定マスタの情報.印刷帳票の敬称
                model.setKeisho(inDto.getInitMasterObj().getKeishoKnj());
            }
            // 以外の場合
            else {
                // "殿"
                model.setKeisho(CommonConstants.KEISHO_STR_TONO);
            }
        }

        // 記入日印刷フラグ = リクエストパラメータ.データ.印刷設定.パラメータ06
        model.setKinyuubiFlg(CommonDtoUtil.strValToInt(inDto.getPrintSet().getParam06()));

        // 記入用シートを印刷するフラグ = リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ
        model.setEmptyFlg(dbNoSaveData != null ? CommonDtoUtil.strValToInt(dbNoSaveData.getEmptyFlg())
                : CommonConstants.NUMBER_ZERO);

        return model;
    }

    /**
     * 医師意見書ヘッダ情報取得
     *
     * @param inDto 入力データ
     * @param model 帳票用データ詳細
     * @return 記入日
     */
    private String getIkenshoLetterInfo(
            AttendingPhysician2H21ReportParameterModel inDto,
            AttendingPhysician2H21ReportInDto model) {
        // 記入日
        String createYmd = CommonConstants.BLANK_STRING;
        // 医師意見書ヘッダ情報取得
        CpnIsiHeadPByCriteriaInEntity entity = new CpnIsiHeadPByCriteriaInEntity();
        // 医師意見書ID = リクエストパラメータ.データ.印刷対象履歴.履歴ID
        entity.setRirekiId(CommonDtoUtil.strValToInt(inDto.getPrintSubjectHistory().getRirekiId()));
        // 医師意見書ヘッダ情報
        List<CpnIsiHeadPOutEntity> list = cpnTucIkensho1SelectMapper.findCpnIsiHeadPByCriteria(entity);
        if (list != null && list.size() > 0) {
            // 記入日
            createYmd = list.get(CommonConstants.NUMBER_ZERO).getCreateYmd();

            // 和暦日付
            model.setCreateYmd(getCmpS2wjEz(createYmd));
        }
        return createYmd;
    }

    /**
     * 利用者情報取得
     *
     * @param inDto 入力データ
     * @param model 帳票用データ詳細
     */
    private void getUserInfo(
            AttendingPhysician2H21ReportParameterModel inDto,
            AttendingPhysician2H21ReportInDto model) {
        // 検索条件がなし、医師意見書データ②情報の取得終了
        String userId = inDto.getPrintSubjectHistory().getUserId();
        if (userId.isEmpty()) {
            // 利用者名
            model.setUserNameKnj(CommonConstants.BLANK_STRING);
            // 利用者長さ超長フラグ 0
            model.setUserNameLengthUltraLongFlg(CommonConstants.NUMBER_ZERO);
            return;
        }
        // 利用者情報取得
        ComTucUserNameKnj2ByCriteriaInEntity entity = new ComTucUserNameKnj2ByCriteriaInEntity();
        // 利用者ID
        entity.setLlTmp(CommonDtoUtil.strValToInt(userId));
        List<ComTucUserNameKnj2OutEntity> userList = comTucUserSelectMapper.findComTucUserNameKnj2ByCriteria(entity);
        if (userList != null && userList.size() > 0) {
            ComTucUserNameKnj2OutEntity data = userList.get(0);
            // 利用者名
            model.setUserNameKnj(data.getNameKnj());
            // 利用者名のバイト数が24より大きいの場合
            if (getByteLength(data.getNameKnj()) > CommonConstants.INT_24) {
                // 利用者長さ超長フラグ 1
                model.setUserNameLengthUltraLongFlg(CommonConstants.NUMBER_ONE);
            }
            // 以外の場合
            else {
                // 利用者長さ超長フラグ 0
                model.setUserNameLengthUltraLongFlg(CommonConstants.NUMBER_ZERO);
            }
        }
    }

    /**
     * 医師意見書データ②情報取得
     *
     * @param inDto 入力データ
     * @param model 帳票用データ詳細
     */
    private void getIkenshoInfo(
            AttendingPhysician2H21ReportParameterModel inDto,
            AttendingPhysician2H21ReportInDto model) {
        // 検索条件がなし、医師意見書データ②情報の取得終了
        String rirekiId = inDto.getPrintSubjectHistory().getRirekiId();
        if (rirekiId.isEmpty()) {
            getDefaultIkenshoInfo(model);
            return;
        }

        // リクエストパラメータ.記入用シートを印刷するフラグが0:チェックOFFの場合、下記の帳票データ情報を取得する
        // 医師意見書データ②情報の取得
        CpnIkensho2ByCriteriaInEntity entity = new CpnIkensho2ByCriteriaInEntity();
        // 医師意見書ID = リクエストパラメータ.データ.印刷対象履歴.履歴ID
        entity.setRirekiId(CommonDtoUtil.strValToInt(inDto.getPrintSubjectHistory().getRirekiId()));
        // 医師意見書データ②情報
        List<CpnIkensho2OutEntity> list = cpnTucIkensho3SelectMapper
                .findCpnIkensho2ByCriteria(entity);
        if (list == null || list.size() == 0) {
            getDefaultIkenshoInfo(model);
        } else {
            // 印鑑欄情報の取得
            CpnIkensho2OutEntity ikensho2PData = list.get(0);
            // 利き腕
            model.setKikiudeFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getKikiudeFlg()));
            // 身長
            model.setHeight(ikensho2PData.getHeight());
            // 体重
            model.setWeight(ikensho2PData.getWeight());
            // 体重の変化
            model.setWeightFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getWeightFlg()));
            // 四肢欠損選択フラグ
            model.setSin1Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin1Flg()));
            // 部位の名前
            model.setSin1BuiKnj(ikensho2PData.getSin1BuiKnj());
            // 麻痺選択フラグ
            model.setSin2Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin2Flg()));
            // 右上肢選択フラグ
            model.setSin2R1Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin2R1Flg()));
            // 右上肢程度選択フラッグ
            model.setSin2R1TeidoFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin2R1TeidoFlg()));
            // 右下肢選択フラグ
            model.setSin2R2Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin2R2Flg()));
            // 右下肢程度選択フラッグ
            model.setSin2R2TeidoFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin2R2TeidoFlg()));
            // 左上肢選択フラグ
            model.setSin2L1Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin2L1Flg()));
            // 左上肢程度選択フラッグ
            model.setSin2L1TeidoFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin2L1TeidoFlg()));
            // 左下肢選択フラグ
            model.setSin2L2Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin2L2Flg()));
            // 左下肢程度選択フラッグ
            model.setSin2L2TeidoFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin2L2TeidoFlg()));
            // その他選択フラッグ
            model.setSin2ElseFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin2ElseFlg()));
            // その他部位の名前
            model.setSin2ElseBuiKnj(ikensho2PData.getSin2ElseBuiKnj());
            // その他程度選択フラッグ
            model.setSin2ElseTeidoFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin2ElseTeidoFlg()));
            // 筋力の低下選択フラグ
            model.setSin3Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin3Flg()));
            // 部位の名前
            model.setSin3BuiKnj(ikensho2PData.getSin3BuiKnj());
            // 筋力の低下程度選択フラッグ
            model.setSin3TeidoFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin3TeidoFlg()));
            // 関節の拘縮選択フラグ
            model.setSin4Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin4Flg()));
            // 部位の名前
            model.setSin4BuiKnj(ikensho2PData.getSin4BuiKnj());
            // 関節の拘縮程度選択フラッグ
            model.setSin4TeidoFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin4TeidoFlg()));
            // 関節の痛み選択フラグ
            model.setSin5Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin5Flg()));
            // 部位の名前
            model.setSin5BuiKnj(ikensho2PData.getSin5BuiKnj());
            // 関節の痛み程度選択フラッグ
            model.setSin5TeidoFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin5TeidoFlg()));
            // 失調不随意運動選択フラグ
            model.setSin6Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin6Flg()));
            // 上肢 right
            model.setSin6JosiRightFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin6JosiRightFlg()));
            // 上肢 left
            model.setSin6JosiLeftFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin6JosiLeftFlg()));
            // 下肢 right
            model.setSin6KasiRightFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin6KasiRightFlg()));
            // 下肢 left
            model.setSin6KasiLeftFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin6KasiLeftFlg()));
            // 体幹 right
            model.setSin6TaikanRightFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin6TaikanRightFlg()));
            // 体幹 left
            model.setSin6TaikanLeftFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin6TaikanLeftFlg()));
            // 褥瘡選択フラグ
            model.setSin7Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin7Flg()));
            // 部位の名前
            model.setSin7BuiKnj(ikensho2PData.getSin7BuiKnj());
            // 褥瘡程度選択フラッグ
            model.setSin7TeidoFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin7TeidoFlg()));
            // その他皮膚疾患選択フラグ
            model.setSin8Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin8Flg()));
            // 部位の名前
            model.setSin8BuiKnj(ikensho2PData.getSin8BuiKnj());
            // その他皮膚疾患程度選択フラッグ
            model.setSin8TeidoFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getSin8TeidoFlg()));
            // 屋外歩行選択フラッグ
            model.setIdou1Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getIdou1Flg()));
            // 車いすの使用選択フラッグ
            model.setIdou2Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getIdou2Flg()));
            // 歩行補助具装具の使用選択フラッグ1
            model.setIdou31Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getIdou31Flg()));
            // 歩行補助具装具の使用選択フラッグ2
            model.setIdou32Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getIdou32Flg()));
            // 歩行補助具装具の使用選択フラッグ3
            model.setIdou33Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getIdou33Flg()));
            // 食事行為選択フラッグ
            model.setEiyo1Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getEiyo1Flg()));
            // 現在の栄養状態選択フラッグ
            model.setEiyoFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getEiyoFlg()));
            // 留意点の内容
            model.setEiyoPointKnj(ikensho2PData.getEiyoPointKnj());
            // 可能の状態1
            model.setJotai1Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getJotai1Flg()));
            // 可能の状態2
            model.setJotai2Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getJotai2Flg()));
            // 可能の状態3
            model.setJotai3Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getJotai3Flg()));
            // 可能の状態4
            model.setJotai4Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getJotai4Flg()));
            // 可能の状態5
            model.setJotai5Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getJotai5Flg()));
            // 可能の状態6
            model.setJotai6Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getJotai6Flg()));
            // 可能の状態7
            model.setJotai7Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getJotai7Flg()));
            // 可能の状態8
            model.setJotai8Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getJotai8Flg()));
            // 可能の状態9
            model.setJotai9Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getJotai9Flg()));
            // 可能の状態10
            model.setJotai10Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getJotai10Flg()));
            // 可能の状態11
            model.setJotai11Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getJotai11Flg()));
            // 可能の状態12
            model.setJotai12Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getJotai12Flg()));
            // 可能の状態13
            model.setJotai13Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getJotai13Flg()));
            // 可能の状態14
            model.setJotai14Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getJotai14Flg()));
            // 可能の状態14その他の内容
            model.setJotai14Knj(ikensho2PData.getJotai14Knj());
            // 方針の内容
            model.setHousinKnj(ikensho2PData.getHousinKnj());
            // 生活機能の維持･改善の見通し選択フラッグ
            model.setKitaiFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getKitaiFlg()));
            // 医学的管理の必要性1
            model.setKanri1Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri1Flg()));
            // 医学的管理の必要性1（必要性が特に高い）
            model.setKanri1TokFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri1TokFlg()));
            // 医学的管理の必要性2
            model.setKanri2Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri2Flg()));
            // 医学的管理の必要性2（必要性が特に高い）
            model.setKanri2TokFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri2TokFlg()));
            // 医学的管理の必要性3
            model.setKanri3Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri3Flg()));
            // 医学的管理の必要性3（必要性が特に高い）
            model.setKanri3TokFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri3TokFlg()));
            // 医学的管理の必要性4
            model.setKanri4Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri4Flg()));
            // 医学的管理の必要性4（必要性が特に高い）
            model.setKanri4TokFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri4TokFlg()));
            // 医学的管理の必要性5
            model.setKanri5Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri5Flg()));
            // 医学的管理の必要性5（必要性が特に高い）
            model.setKanri5TokFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri5TokFlg()));
            // 医学的管理の必要性6
            model.setKanri6Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri6Flg()));
            // 医学的管理の必要性6（必要性が特に高い）
            model.setKanri6TokFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri6TokFlg()));
            // 医学的管理の必要性7
            model.setKanri7Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri7Flg()));
            // 医学的管理の必要性7（必要性が特に高い）
            model.setKanri7TokFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri7TokFlg()));
            // 医学的管理の必要性8
            model.setKanri8Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri8Flg()));
            // 医学的管理の必要性8（必要性が特に高い）
            model.setKanri8TokFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri8TokFlg()));
            // 医学的管理の必要性9
            model.setKanri9Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri9Flg()));
            // 医学的管理の必要性9（必要性が特に高い）
            model.setKanri9TokFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri9TokFlg()));
            // 医学的管理の必要性10
            model.setKanri10Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri10Flg()));
            // 医学的管理の必要性10（必要性が特に高い）
            model.setKanri10TokFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri10TokFlg()));
            // その他の医療系サービスの内容
            model.setKanri10Knj(ikensho2PData.getKanri10Knj());
            // 留意事項1フラッグ
            model.setRyui1Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getRyui1Flg()));
            // 留意事項1の内容
            model.setRyui1Knj(ikensho2PData.getRyui1Knj());
            // 留意事項2フラッグ
            model.setRyui2Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getRyui2Flg()));
            // 留意事項2の内容
            model.setRyui2Knj(ikensho2PData.getRyui2Knj());
            // 留意事項3フラッグ
            model.setRyui3Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getRyui3Flg()));
            // 留意事項3の内容
            model.setRyui3Knj(ikensho2PData.getRyui3Knj());
            // 留意事項4フラッグ
            model.setRyui4Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getRyui4Flg()));
            // 留意事項4の内容
            model.setRyui4Knj(ikensho2PData.getRyui4Knj());
            // 留意事項5フラッグ
            model.setRyui5Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getRyui5Flg()));
            // 留意事項5の内容
            model.setRyui5Knj(ikensho2PData.getRyui5Knj());
            // 留意事項6その他の内容
            model.setRyui6Knj(ikensho2PData.getRyui6Knj());
            // 感染症の有無のフラッグ
            model.setKansenFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getKansenFlg()));
            // 感染の内容
            model.setKansenKnj(ikensho2PData.getKansenKnj());
            // 特記すべき事項
            model.setIkenKnj(ikensho2PData.getIkenKnj());
            // 医学的管理の必要性11
            model.setKanri11Flg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri11Flg()));
            // 医学的管理11（看護職員による相談支援）
            model.setKanri11TokFlg(CommonDtoUtil.shortValToInt(ikensho2PData.getKanri11TokFlg()));
        }
    }

    /**
     * リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ = "1" の場合、帳票用データを設定する
     *
     * @param model 帳票用データ詳細
     */
    private void getDefaultIkenshoInfo(AttendingPhysician2H21ReportInDto model) {
        // 利き腕
        model.setKikiudeFlg(CommonConstants.INTEGER_NULL);
        // 身長
        model.setHeight(CommonConstants.DOUBLE_NULL);
        // 体重
        model.setWeight(CommonConstants.DOUBLE_NULL);
        // 体重の変化
        model.setWeightFlg(CommonConstants.INTEGER_NULL);
        // 四肢欠損選択フラグ
        model.setSin1Flg(CommonConstants.INTEGER_NULL);
        // 部位の名前
        model.setSin1BuiKnj(CommonConstants.BLANK_STRING);
        // 麻痺選択フラグ
        model.setSin2Flg(CommonConstants.INTEGER_NULL);
        // 右上肢選択フラグ
        model.setSin2R1Flg(CommonConstants.INTEGER_NULL);
        // 右上肢程度選択フラッグ
        model.setSin2R1TeidoFlg(CommonConstants.INTEGER_NULL);
        // 右下肢選択フラグ
        model.setSin2R2Flg(CommonConstants.INTEGER_NULL);
        // 右下肢程度選択フラッグ
        model.setSin2R2TeidoFlg(CommonConstants.INTEGER_NULL);
        // 左上肢選択フラグ
        model.setSin2L1Flg(CommonConstants.INTEGER_NULL);
        // 左上肢程度選択フラッグ
        model.setSin2L1TeidoFlg(CommonConstants.INTEGER_NULL);
        // 左下肢選択フラグ
        model.setSin2L2Flg(CommonConstants.INTEGER_NULL);
        // 左下肢程度選択フラッグ
        model.setSin2L2TeidoFlg(CommonConstants.INTEGER_NULL);
        // その他選択フラッグ
        model.setSin2ElseFlg(CommonConstants.INTEGER_NULL);
        // その他部位の名前
        model.setSin2ElseBuiKnj(CommonConstants.BLANK_STRING);
        // その他程度選択フラッグ
        model.setSin2ElseTeidoFlg(CommonConstants.INTEGER_NULL);
        // 筋力の低下選択フラグ
        model.setSin3Flg(CommonConstants.INTEGER_NULL);
        // 部位の名前
        model.setSin3BuiKnj(CommonConstants.BLANK_STRING);
        // 筋力の低下程度選択フラッグ
        model.setSin3TeidoFlg(CommonConstants.INTEGER_NULL);
        // 関節の拘縮選択フラグ
        model.setSin4Flg(CommonConstants.INTEGER_NULL);
        // 部位の名前
        model.setSin4BuiKnj(CommonConstants.BLANK_STRING);
        // 関節の拘縮程度選択フラッグ
        model.setSin4TeidoFlg(CommonConstants.INTEGER_NULL);
        // 関節の痛み選択フラグ
        model.setSin5Flg(CommonConstants.INTEGER_NULL);
        // 部位の名前
        model.setSin5BuiKnj(CommonConstants.BLANK_STRING);
        // 関節の痛み程度選択フラッグ
        model.setSin5TeidoFlg(CommonConstants.INTEGER_NULL);
        // 失調不随意運動選択フラグ
        model.setSin6Flg(CommonConstants.INTEGER_NULL);
        // 上肢 right
        model.setSin6JosiRightFlg(CommonConstants.INTEGER_NULL);
        // 上肢 left
        model.setSin6JosiLeftFlg(CommonConstants.INTEGER_NULL);
        // 下肢 right
        model.setSin6KasiRightFlg(CommonConstants.INTEGER_NULL);
        // 下肢 left
        model.setSin6KasiLeftFlg(CommonConstants.INTEGER_NULL);
        // 体幹 right
        model.setSin6TaikanRightFlg(CommonConstants.INTEGER_NULL);
        // 体幹 left
        model.setSin6TaikanLeftFlg(CommonConstants.INTEGER_NULL);
        // 褥瘡選択フラグ
        model.setSin7Flg(CommonConstants.INTEGER_NULL);
        // 部位の名前
        model.setSin7BuiKnj(CommonConstants.BLANK_STRING);
        // 褥瘡程度選択フラッグ
        model.setSin7TeidoFlg(CommonConstants.INTEGER_NULL);
        // その他皮膚疾患選択フラグ
        model.setSin8Flg(CommonConstants.INTEGER_NULL);
        // 部位の名前
        model.setSin8BuiKnj(CommonConstants.BLANK_STRING);
        // その他皮膚疾患程度選択フラッグ
        model.setSin8TeidoFlg(CommonConstants.INTEGER_NULL);
        // 屋外歩行選択フラッグ
        model.setIdou1Flg(CommonConstants.INTEGER_NULL);
        // 車いすの使用選択フラッグ
        model.setIdou2Flg(CommonConstants.INTEGER_NULL);
        // 歩行補助具装具の使用選択フラッグ1
        model.setIdou31Flg(CommonConstants.INTEGER_NULL);
        // 歩行補助具装具の使用選択フラッグ2
        model.setIdou32Flg(CommonConstants.INTEGER_NULL);
        // 歩行補助具装具の使用選択フラッグ3
        model.setIdou33Flg(CommonConstants.INTEGER_NULL);
        // 食事行為選択フラッグ
        model.setEiyo1Flg(CommonConstants.INTEGER_NULL);
        // 現在の栄養状態選択フラッグ
        model.setEiyoFlg(CommonConstants.INTEGER_NULL);
        // 留意点の内容
        model.setEiyoPointKnj(CommonConstants.BLANK_STRING);
        // 可能の状態1
        model.setJotai1Flg(CommonConstants.INTEGER_NULL);
        // 可能の状態2
        model.setJotai2Flg(CommonConstants.INTEGER_NULL);
        // 可能の状態3
        model.setJotai3Flg(CommonConstants.INTEGER_NULL);
        // 可能の状態4
        model.setJotai4Flg(CommonConstants.INTEGER_NULL);
        // 可能の状態5
        model.setJotai5Flg(CommonConstants.INTEGER_NULL);
        // 可能の状態6
        model.setJotai6Flg(CommonConstants.INTEGER_NULL);
        // 可能の状態7
        model.setJotai7Flg(CommonConstants.INTEGER_NULL);
        // 可能の状態8
        model.setJotai8Flg(CommonConstants.INTEGER_NULL);
        // 可能の状態9
        model.setJotai9Flg(CommonConstants.INTEGER_NULL);
        // 可能の状態10
        model.setJotai10Flg(CommonConstants.INTEGER_NULL);
        // 可能の状態11
        model.setJotai11Flg(CommonConstants.INTEGER_NULL);
        // 可能の状態12
        model.setJotai12Flg(CommonConstants.INTEGER_NULL);
        // 可能の状態13
        model.setJotai13Flg(CommonConstants.INTEGER_NULL);
        // 可能の状態14
        model.setJotai14Flg(CommonConstants.INTEGER_NULL);
        // 可能の状態14その他の内容
        model.setJotai14Knj(CommonConstants.BLANK_STRING);
        // 方針の内容
        model.setHousinKnj(CommonConstants.BLANK_STRING);
        // 生活機能の維持･改善の見通し選択フラッグ
        model.setKitaiFlg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性1
        model.setKanri1Flg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性1（必要性が特に高い）
        model.setKanri1TokFlg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性2
        model.setKanri2Flg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性2（必要性が特に高い）
        model.setKanri2TokFlg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性3
        model.setKanri3Flg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性3（必要性が特に高い）
        model.setKanri3TokFlg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性4
        model.setKanri4Flg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性4（必要性が特に高い）
        model.setKanri4TokFlg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性5
        model.setKanri5Flg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性5（必要性が特に高い）
        model.setKanri5TokFlg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性6
        model.setKanri6Flg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性6（必要性が特に高い）
        model.setKanri6TokFlg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性7
        model.setKanri7Flg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性7（必要性が特に高い）
        model.setKanri7TokFlg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性8
        model.setKanri8Flg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性8（必要性が特に高い）
        model.setKanri8TokFlg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性9
        model.setKanri9Flg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性9（必要性が特に高い）
        model.setKanri9TokFlg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性10
        model.setKanri10Flg(CommonConstants.INTEGER_NULL);
        // 医学的管理の必要性10（必要性が特に高い）
        model.setKanri10TokFlg(CommonConstants.INTEGER_NULL);
        // その他の医療系サービスの内容
        model.setKanri10Knj(CommonConstants.BLANK_STRING);
        // 留意事項1フラッグ
        model.setRyui1Flg(CommonConstants.INTEGER_NULL);
        // 留意事項1の内容
        model.setRyui1Knj(CommonConstants.BLANK_STRING);
        // 留意事項2フラッグ
        model.setRyui2Flg(CommonConstants.INTEGER_NULL);
        // 留意事項2の内容
        model.setRyui2Knj(CommonConstants.BLANK_STRING);
        // 留意事項3フラッグ
        model.setRyui3Flg(CommonConstants.INTEGER_NULL);
        // 留意事項3の内容
        model.setRyui3Knj(CommonConstants.BLANK_STRING);
        // 留意事項4フラッグ
        model.setRyui4Flg(CommonConstants.INTEGER_NULL);
        // 留意事項4の内容
        model.setRyui4Knj(CommonConstants.BLANK_STRING);
        // 留意事項5フラッグ
        model.setRyui5Flg(CommonConstants.INTEGER_NULL);
        // 留意事項5の内容
        model.setRyui5Knj(CommonConstants.BLANK_STRING);
        // 留意事項6その他の内容
        model.setRyui6Knj(CommonConstants.BLANK_STRING);
        // 感染症の有無のフラッグ
        model.setKansenFlg(CommonConstants.INTEGER_NULL);
        // 感染の内容
        model.setKansenKnj(CommonConstants.BLANK_STRING);
        // 特記すべき事項
        model.setIkenKnj(CommonConstants.BLANK_STRING);
        // 医学的管理の必要性11
        model.setKanri11Flg(CommonConstants.INTEGER_NULL);
        // 医学的管理11（看護職員による相談支援）
        model.setKanri11TokFlg(CommonConstants.INTEGER_NULL);
    }

    /**
     * 和暦変換処理
     * 共通関数補足の和暦日付
     *
     * @param ymd 年月日
     */
    private String getCmpS2wjEz(String ymd) {
        String dateValue = (ymd != null && !ymd.trim().isEmpty()) ? ymd : CommonConstants.BLANK_STRING;
        return kghCmpF01Logic.getCmpS2wjEz(dateValue, CommonConstants.NUMBER_ONE);
    }

    /**
     * 文字列のバイト数を取得（Shift-JIS換算）
     */
    private int getByteLength(String str) {
        try {
            return str.getBytes(CommonConstants.Shift_JIS).length;
        } catch (Exception e) {
            return str.getBytes().length;
        }
    }

}
