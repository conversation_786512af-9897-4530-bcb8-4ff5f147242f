{"printWidth": 100, "tabWidth": 2, "useTabs": false, "semi": false, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": false, "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "filepath": "none", "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "endOfLine": "auto", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": true}