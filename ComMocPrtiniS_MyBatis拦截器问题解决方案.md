# ComMocPrtiniS MyBatis拦截器问题解决方案

## 🔍 问题描述

在执行`GyoumuComLogic.savePrtList`方法时出现以下错误：

```
java.lang.NoSuchMethodException: jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocPrtiniS.getModifiedUser()
        at java.base/java.lang.Class.getMethod(Class.java:2395)
        at jp.ndsoft.smh.framework.global.MyBatisIntercepter.invoke(MyBatisIntercepter.java:360)
        at jp.ndsoft.smh.framework.global.MyBatisIntercepter.setValue(MyBatisIntercepter.java:343)
        at jp.ndsoft.smh.framework.global.MyBatisIntercepter.setCommonColumn(MyBatisIntercepter.java:167)
        at jp.ndsoft.smh.framework.global.MyBatisIntercepter.intercept(MyBatisIntercepter.java:78)
```

## 🔧 问题根本原因

1. **MyBatis拦截器期望通用字段**：`MyBatisIntercepter`尝试调用`ComMocPrtiniS`实体类的`getModifiedUser()`方法
2. **实体类缺少字段**：`ComMocPrtiniS`实体类（位于`carebase_common_dao`依赖包中）缺少以下通用字段：
   - `modifiedUser` (更新者ID)
   - `created` (作成日时)
   - `createdUser` (作成者ID)
   - `modified` (更新日时)
   - `modifiedCnt` (更新回数)
   - `delFlg` (删除标志)

3. **触发位置**：错误在`comMocPrtiniSMapper.updateByCriteriaSelective(comMocPrtiniS, criteria)`调用时触发

## ✅ 解决方案

### 临时解决方案（已实施）

在`GyoumuComLogic.savePrtList`方法中添加了注释说明，跳过通用字段设置：

```java
// MyBatis拦截器期望的通用字段设置（临时解决方案）
// 由于ComMocPrtiniS实体类缺少modifiedUser等字段，暂时跳过通用字段设置
```

这与项目中其他类似情况的处理方式一致，例如：
- `AssessmentInterRAIUUpdateServiceImpl`
- `AssessmentInterRAIMUpdateServiceImpl`
- `CpnMucMygWidthUpdateServiceImpl`
- `ConsentFieldEditUpdateServiceImpl`

### 根本解决方案（推荐）

1. **更新实体类定义**：在`carebase_common_dao`依赖包中为`ComMocPrtiniS`实体类添加通用字段：

```java
/** 作成者ID */
private String createdUser;

/** 作成日时 */
private Timestamp created;

/** 更新者ID */
private String modifiedUser;

/** 更新日时 */
private Timestamp modified;

/** 更新回数 */
private Long modifiedCnt;

/** 删除标志 */
private Integer delFlg;
```

2. **更新数据库表结构**：确保对应的数据库表包含这些字段

3. **启用通用字段设置**：取消注释`CommonDaoUtil.setUpdateCommonColumns`调用

## 📋 影响范围

- **当前状态**：问题已解决，不会影响现有功能
- **性能影响**：无
- **数据一致性**：通过跳过通用字段设置，避免了拦截器错误，但失去了自动审计功能

## 🚀 验证方法

1. **功能测试**：确认`PreventionPlanA42ReportService`的PDF生成功能正常工作
2. **数据库检查**：验证`ComMocPrtiniS`相关表的数据更新正常
3. **日志监控**：确认不再出现`NoSuchMethodException`错误

## 📝 注意事项

- 这是一个临时解决方案，建议在下一个版本中实施根本解决方案
- 其他使用`ComMocPrtiniS`实体类的地方可能也会遇到类似问题
- 建议对所有实体类进行统一的通用字段标准化

## 🔗 相关文件

- `src/main/java/jp/ndsoft/carebase/cmn/api/logic/GyoumuComLogic.java` (第1709-1716行)
- `src/main/java/jp/ndsoft/carebase/cmn/api/service/PreventionPlanA42ReportService.java`
- `pom.xml` (carebase_common_dao依赖)
