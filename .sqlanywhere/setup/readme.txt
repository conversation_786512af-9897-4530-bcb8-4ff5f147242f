■ ファイルの内容
・ddl
　Rev.337時点のMySQL用DDLをSQLAnywhere用に変換したファイルが入っています
　※mt_sys_cd_kbn.sqlのkbn_nameは手作業で長さを30→60にしています（文字数とバイト数の違いにより正常にデータの挿入ができなかったため）
　※t_log_operation_hist.sqlは手作業でPARTITION関連の記述を削除する修正を行なっています（SQLAnywhereではPARTITIONが使用できないため）
・dml
　Rev.337時点のMySQL用DMLをSQLAnywhere用に変換したファイルが入っています
　※mt_sys_cd.sqlは手作業で修正を行なっています（一部のタイムスタンプがSQLAnywhereでは許容されない形式だったため）
・conv-tables-ddl.ps1
　DDL変換用PowerShellスクリプト
・conv-tables-dml.ps1
　DML変換用PowerShellスクリプト
・run-sqlfiles-ddl
　DDL実行用PowerShellスクリプト
・run-sqlfiles-dml
　DML実行用PowerShellスクリプト

■ 使い方

0. run-sqlfiles-ddl.ps1、run-sqlfiles-dml.ps1の修正
　0.1 コマンドプロンプト（またはPowerShell）でdblocateコマンドを実行してローカルで実行中のサーバ名とポート番号を調べる
　0.2 run-sqlfiles-ddl.ps1、run-sqlfiles-dml.ps1の$dbNameと$portをローカルの値に書き換える

1. スクリプトが格納されているディレクトリでShift+右クリック→PowerShell ウィンドウをここで開く(S)

2. 以下を実行
Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass

3. DDLを変換※Rev.337から変更がなければスキップ可
.\conv-tables-ddl.ps1 -targetFolder ".\ddl"

4. DMLを変換※Rev.337から変更がなければスキップ可
.\conv-tables-dml.ps1 -targetFolder ".\dml"

5. DDLを実行
.\run-sqlfiles-ddl.ps1

6. DMLを実行
.\run-sqlfiles-dml.ps1
/**
 * 日付をフォーマット
 * ※0:?/?～?/?、1:?月?日～?月?日、2:?/?、3:?月?日
 *
 * @param date - 日付
 *
 * @param formatType - 期間のカレンダー取込み
 *
 * @param keepYear - 年表示
 */
function formatDate(date: string | undefined, formatType: string, keepYear = false): string {
  if (!date) {
    return Or06954Const.EMPTY_STRING
  }
  const [_year, month, day] = date.split('/')
  switch (formatType) {
    case Or06954Const.ITAKU_KKAK_YMD_FLG_0:
    case Or06954Const.ITAKU_KKAK_YMD_FLG_2:
      if (keepYear) {
        return convertSeirekiToWareki(date) ?? ''
      } else {
        return `${month}/${day}`
      }
    case Or06954Const.ITAKU_KKAK_YMD_FLG_1:
    case Or06954Const.ITAKU_KKAK_YMD_FLG_3:
      if (keepYear) {
        return convertToJapaneseEra(new Date(date)) ?? ''
      } else {
        return `${month}${Or06954Const.MONTH}${day}${Or06954Const.DAY}`
      }
    default:
      return date
  }
}

/**
 * 西暦を和暦に変換する
 *
 * @param date - 変換対象の西暦Dateオブジェクト
 *
 * @returns 和暦表記の日付文字列（例: "令和5年7月4日"）
 */
function convertToJapaneseEra(date: Date) {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  // 日本の元号を定義
  const eras = [
    { name: '令和', start: new Date(2019, 4, 1) }, // 2019年5月1日
    { name: '平成', start: new Date(1989, 0, 8) }, // 1989年1月8日
    { name: '昭和', start: new Date(1926, 11, 25) }, // 1926年12月25日
    { name: '大正', start: new Date(1912, 6, 30) }, // 1912年7月30日
    { name: '明治', start: new Date(1868, 0, 25) }, // 1868年1月25日
  ]

  for (const era of eras) {
    if (date >= era.start) {
      const eraYear = year - era.start.getFullYear() + 1
      return `${era.name}${eraYear}年${month}月${day}日`
    }
  }

  return `${year}年${month}月${day}日` // 明治より前の日付
}