# PreventionPlanA42ReportService 使用方法

## 問題の説明

`PreventionPlanA42ReportService`で账票出力する際に以下の問題が発生していました：

1. **subreport1のみ設定**した場合：PDF出力でsubreport1は表示されるが、subreport2の部分が空白ページとして表示される
2. **subreport2のみ設定**した場合：PDF出力でsubreport2は表示されるが、subreport1の部分が空白ページとして表示される

## 解決方法

以下の新しいメソッドを追加しました：

### 1. 単一サブレポート出力メソッド

```java
/**
 * 単一のサブレポートのみを出力する（推奨）
 */
public void outputSingleSubreport(
    PreventionPlanA42ReportServiceParameterModel inDto,
    PreventionPlanA42ReportServiceOutDto outDto, 
    int subreportNumber) throws Exception
```

**使用例：**
```java
// サブレポート1のみを出力（空白ページなし）
service.outputSingleSubreport(inDto, outDto, 1);

// サブレポート2のみを出力（空白ページなし）
service.outputSingleSubreport(inDto, outDto, 2);
```

### 2. 選択的サブレポート出力メソッド

```java
/**
 * 選択的にサブレポートを出力する（空白ページを避ける）
 */
public void outputSelectiveSubreports(
    PreventionPlanA42ReportServiceParameterModel inDto,
    PreventionPlanA42ReportServiceOutDto outDto, 
    boolean showSubreport1, 
    boolean showSubreport2) throws Exception
```

**使用例：**
```java
// サブレポート1のみを表示
service.outputSelectiveSubreports(inDto, outDto, true, false);

// サブレポート2のみを表示
service.outputSelectiveSubreports(inDto, outDto, false, true);

// 両方のサブレポートを表示
service.outputSelectiveSubreports(inDto, outDto, true, true);
```

## 推奨される使用方法

### ケース1: 単一のサブレポートのみが必要な場合

```java
// 最もシンプルで効率的な方法
if (needSubreport1Only) {
    service.outputSingleSubreport(inDto, outDto, 1);
} else if (needSubreport2Only) {
    service.outputSingleSubreport(inDto, outDto, 2);
}
```

### ケース2: 条件に応じて動的に制御したい場合

```java
// より柔軟な制御が可能
boolean showSub1 = /* 条件判定 */;
boolean showSub2 = /* 条件判定 */;
service.outputSelectiveSubreports(inDto, outDto, showSub1, showSub2);
```

## 技術的な詳細

### 問題の原因
- `E00402_PreventionPlanA42Report_ALL.jrxml`テンプレートは2つの固定`<band>`を持っている
- 各bandは1ページのPDFに対応している
- データソースが空でもbandは描画されるため、空白ページが生成される

### 解決のアプローチ
1. **単一テンプレート使用**: `E00402_PreventionPlanA42Report_1.jrxml`または`E00402_PreventionPlanA42Report_2.jrxml`を直接使用
2. **空データソース制御**: 表示しないサブレポートには空のデータソースを設定

## 注意事項

- 既存の`outputPdf`メソッドは変更していないため、既存のコードに影響はありません
- 新しいメソッドは追加機能として提供されています
- パフォーマンス面では、単一テンプレートを使用する`outputSingleSubreport`が最も効率的です

## 使用例（コントローラーでの実装）

```java
@RestController
public class ReportController {
    
    @Autowired
    private PreventionPlanA42ReportService reportService;
    
    @PostMapping("/report/subreport1")
    public ResponseEntity<String> generateSubreport1(
            @RequestBody PreventionPlanA42ReportServiceParameterModel request) {
        try {
            PreventionPlanA42ReportServiceOutDto response = new PreventionPlanA42ReportServiceOutDto();
            
            // サブレポート1のみを出力（空白ページなし）
            reportService.outputSingleSubreport(request, response, 1);
            
            return ResponseEntity.ok(response.getPdfUpLoadPath());
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Error: " + e.getMessage());
        }
    }
    
    @PostMapping("/report/subreport2")
    public ResponseEntity<String> generateSubreport2(
            @RequestBody PreventionPlanA42ReportServiceParameterModel request) {
        try {
            PreventionPlanA42ReportServiceOutDto response = new PreventionPlanA42ReportServiceOutDto();
            
            // サブレポート2のみを出力（空白ページなし）
            reportService.outputSingleSubreport(request, response, 2);
            
            return ResponseEntity.ok(response.getPdfUpLoadPath());
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Error: " + e.getMessage());
        }
    }
}
```
