# ComMocPrtiniS 实体类源代码分析

## 🔍 当前实体类结构

基于反编译结果，`ComMocPrtiniS`实体类的结构如下：

### 继承关系
```java
public class ComMocPrtiniS extends ComMocPrtiniSKey {
    // 实现了 IEntity 接口（通过父类）
}

public class ComMocPrtiniSKey implements Serializable, IEntity {
    // 基础键字段
}
```

### 父类字段 (ComMocPrtiniSKey)
```java
private Integer shokuId;        // 职员ID
private String sysRyaku;        // 系统略称
private String sectionNo;       // 区段号
private Integer prtNo;          // 打印号
private Integer houjinId;       // 法人ID
private Integer shisetuId;      // 设施ID
private Integer svJigyoId;      // 服务事业ID
```

### 子类字段 (ComMocPrtiniS)
```java
private String sectionName;     // 区段名称
private String dwobject;        // DW对象
private Integer prtOrient;      // 打印方向
private Integer prtSize;        // 打印尺寸
private String listTitle;       // 列表标题
private String prtTitle;        // 打印标题
private Integer mTop;           // 上边距
private Integer mBottom;        // 下边距
private Integer mLeft;          // 左边距
private Integer mRight;         // 右边距
private Integer ruler;          // 标尺
private Integer prndate;        // 打印日期
private Integer prnshoku;       // 打印职员
private Integer serialFlg;      // 序列标志
private Integer modFlg;         // 修改标志
private Integer secFlg;         // 区段标志

// 参数字段 (param01 ~ param50)
private String param01;
private String param02;
// ... (param03 ~ param49)
private String param50;

private Integer handFlg;        // 手动标志
private Integer serialType;     // 序列类型
private String serialParam;     // 序列参数
private Integer zoomRate;       // 缩放比例
private Integer zoomRate2;      // 缩放比例2
private Integer serialHeight;   // 序列高度
private Integer serialPagelen;  // 序列页长
private Integer hsjId;          // HSJ ID
```

## ❌ 缺少的MyBatis拦截器期望字段

根据错误信息和其他实体类的模式，`ComMocPrtiniS`缺少以下通用审计字段：

```java
// 缺少的字段
private String createdUser;     // 作成者ID
private Timestamp created;      // 作成日时
private String modifiedUser;    // 更新者ID  ← 这是导致错误的字段
private Timestamp modified;     // 更新日时
private Long modifiedCnt;       // 更新回数
private Integer delFlg;         // 删除标志
```

## 🔧 问题根源

1. **MyBatis拦截器期望**：`MyBatisIntercepter`尝试调用`getModifiedUser()`方法
2. **实体类现状**：`ComMocPrtiniS`没有`modifiedUser`字段，因此没有对应的getter方法
3. **触发时机**：在执行`updateByCriteriaSelective`时，拦截器自动尝试设置审计字段

## ✅ 解决方案选项

### 选项1：修改实体类（推荐）
在`carebase_common_dao`项目中为`ComMocPrtiniS`添加通用审计字段：

```java
public class ComMocPrtiniS extends ComMocPrtiniSKey {
    // 现有字段...
    
    // 添加通用审计字段
    private String createdUser;
    private Timestamp created;
    private String modifiedUser;
    private Timestamp modified;
    private Long modifiedCnt;
    private Integer delFlg;
    
    // 对应的getter/setter方法
    public String getModifiedUser() { return modifiedUser; }
    public void setModifiedUser(String modifiedUser) { this.modifiedUser = modifiedUser; }
    // ... 其他getter/setter
}
```

### 选项2：跳过拦截器处理（已实施）
在业务代码中跳过`CommonDaoUtil.setUpdateCommonColumns`调用，避免触发拦截器。

## 📋 数据库表结构

需要确认对应的数据库表是否包含这些审计字段：
- `created_user`
- `created`
- `modified_user`
- `modified`
- `modified_cnt`
- `del_flg`

## 🚀 建议行动

1. **短期**：继续使用当前的跳过拦截器方案
2. **中期**：联系`carebase_common_dao`维护团队，添加通用审计字段
3. **长期**：统一所有实体类的审计字段标准

## 📝 相关文件位置

- **JAR包**：`./tmp2/carebase_common_dao_lib.jar`
- **类路径**：`jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocPrtiniS`
- **父类**：`jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocPrtiniSKey`
- **接口**：`jp.ndsoft.carebase.framework.base.entity.IEntity`
