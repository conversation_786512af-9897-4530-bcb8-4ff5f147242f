// package jp.ndsoft.carebase.cmn.api.service;

// import java.util.ArrayList;
// import java.util.List;

// import org.apache.commons.collections4.CollectionUtils;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
// import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCmoni1OutEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.DKghMstKrkMoniTitleByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMocFree1ByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMocFree1OutEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMocFree2NoFltOutEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFreeSncTaishouDataOutEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFreeTekiyoByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.DKghMstKrkMoniTitleOutEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KycTucHyouka1ByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KycTucHyouka1OutEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCmoni1ByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFreeTekiyoInfoByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFreeTekiyoInfoOutEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFreeTekiyoOutEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFreeSncTaishouDataByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMocFree2NoFltByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkSsmIntValueByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkSsmIntValueOutEntity;
// import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
// import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;

// import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01221DivisionNoForCompareBackup;
// import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01221DuplicateRowForBackup;
// import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01221MonitoringTitle;
// import jp.ndsoft.carebase.cmn.api.service.dto.MonitoringTitleMasterSelectServiceInDto;
// import jp.ndsoft.carebase.cmn.api.service.dto.MonitoringTitleMasterSelectServiceOutDto;
// import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCmoni1SelectMapper;
// import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFree1SelectMapper;
// import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFree2SelectMapper;
// import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFreeSncTaishouSelectMapper;
// import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFreeTekiyoSelectMapper;
// import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;
// import jp.ndsoft.carebase.common.dao.mysql.mapper.KycTucHyouka1SelectMapper;
// import jp.ndsoft.smh.framework.common.Constants;
// import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
// import jp.ndsoft.smh.framework.services.login.LoginServiceImpl;

// /**
//  * @since 2025.05.20
//  * <AUTHOR> 高金康
//  * @implNote GUI01221_モニタリングタイトルマスタ画面の初期情報を取得する。
//  */
// @Service
// public class MonitoringTitleMasterSelectServiceImpl extends
//         SelectServiceImpl<MonitoringTitleMasterSelectServiceInDto, MonitoringTitleMasterSelectServiceOutDto> {
//     /** ロガー. */
//     private static final Logger LOG = LoggerFactory.getLogger(LoginServiceImpl.class);

//     /** 初期設定マスタの整数情報取得 */
//     @Autowired
//     private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;

//     /** カスタマイズ帳票マスタヘッダ情報取得 */
//     @Autowired
//     private KghMocKrkFree1SelectMapper kghMocKrkFree1SelectMapper;

//     /** カスタマイズ帳票マスタデータ情報取得 */
//     @Autowired
//     private KghMocKrkFree2SelectMapper kghMocKrkFree2SelectMapper;

//     /** 27-30 カスタマイズ帳票進捗管理対象マスタデータ情報取得 */
//     @Autowired
//     private KghMocKrkFreeSncTaishouSelectMapper kghMocKrkFreeSncTaishouSelectMapper;

//     /** 27-18 カスタマイズ帳票適用マスタ情報取得 */
//     @Autowired
//     private KghMocKrkFreeTekiyoSelectMapper kghMocKrkFreeTekiyoSelectMapper;

//     /** 28-04 サービス評価表ヘッダ情報取得 */
//     @Autowired
//     private KycTucHyouka1SelectMapper kycTucHyouka1SelectMapper;

//     /** 28-01 モニタリング記録表ヘッダ */
//     @Autowired
//     private CpnTucCmoni1SelectMapper cpnTucCmoni1SelectMapper;

//     /**
//      * 画面表示情報取得
//      *
//      * @param inDto 画面初期情報取得の入力DTO.
//      * @return 画面初期情報取得OUT DTO
//      * @throws Exception Exception
//      */
//     @Override
//     protected MonitoringTitleMasterSelectServiceOutDto mainProcess(
//             final MonitoringTitleMasterSelectServiceInDto inDto) throws Exception {
//         LOG.info(Constants.START);

//         /*
//          * ===============1.単項目チェック以外の入力チェック===============
//          *
//          */
//         // 特になし

//         // GUI01221_モニタリングタイトルマスタ 初期情報取得出力DTO
//         MonitoringTitleMasterSelectServiceOutDto outDto = new MonitoringTitleMasterSelectServiceOutDto();
//         /*
//          * ===============2. 変数値の初期化を行う===============
//          *
//          */
//         // 変数.総進捗管理対象フラグ=0
//         String allSnsFlg = CommonConstants.ALL_SNS_FLG_OFF;
//         // 変数.適用されているマスタID=0（1:適用されている 0:適用されていない）
//         Integer tekeyouID = CommonDtoUtil.strValToInt(CommonConstants.APPLICABLE_OFF);
//         /*
//          * ===============3.変数.総進捗管理対象フラグの設定を行う===============
//          *
//          */
//         // 3.1. 下記の初期設定マスタの整数取得のDAOを利用し、初期設定マスタの整数情報を取得する
//         KghMocKrkSsmIntValueByCriteriaInEntity kghMocKrkSsmIntValueByCriteriaInEntity = new KghMocKrkSsmIntValueByCriteriaInEntity();
//         // 事業者ID(リクエストパラメータ.事業者ID)

//         kghMocKrkSsmIntValueByCriteriaInEntity.setSvJigyoId(inDto.getSvJigyoId());
//         // 初期設定マスタの整数情報を取得
//         List<KghMocKrkSsmIntValueOutEntity> kghMocKrkSsmIntValueList = this.kghMocKrkSsmSelectMapper
//                 .findKghMocKrkSsmIntValueByCriteria(kghMocKrkSsmIntValueByCriteriaInEntity);
//         // 3.2. 「3.1.」で取得した初期設定マスタの整数情報.整数 =2 の場合
//         if (CollectionUtils.isNotEmpty(kghMocKrkSsmIntValueList)) {
//             KghMocKrkSsmIntValueOutEntity kghMocKrkSsmIntValueOutEntity = kghMocKrkSsmIntValueList.get(0);
//             if ((kghMocKrkSsmIntValueOutEntity.getIntValue()) == CommonConstants.NUMBER_2) {
//                 allSnsFlg = (CommonConstants.ALL_SNS_FLG_ON);
//             } else {
//                 allSnsFlg = (CommonConstants.ALL_SNS_FLG_OFF);
//             }
//         }
//         outDto.setAllSncFlg(allSnsFlg);

//         /*
//          * ===============
//          * 4.カスタマイズ帳票マスタヘッダ取得のDAOを利用し、カスタマイズ帳票マスタヘッダ情報を取得する。===============
//          *
//          */
//         KghKrkMocFree1ByCriteriaInEntity kghKrkMocFree1ByCriteriaInEntity = new KghKrkMocFree1ByCriteriaInEntity();
//         // 機能区分（固定値 1:モニタリングタイトルマスタ）

//         kghKrkMocFree1ByCriteriaInEntity.setAiKinouKbn(CommonDtoUtil.intValToShort(CommonConstants.KINOUKBN_KINOU1));
//         // 様式区分(リクエストパラメータ.様式区分)

//         kghKrkMocFree1ByCriteriaInEntity.setAiYoushikiKbn(CommonDtoUtil.strValToShort(inDto.getYoushikiKbn()));
//         // カスタマイズ帳票マスタヘッダ情報取得
//         List<KghKrkMocFree1OutEntity> kghKrkMocFree1List = this.kghMocKrkFree1SelectMapper
//                 .findKghKrkMocFree1ByCriteria(kghKrkMocFree1ByCriteriaInEntity);
//         // 分割数比較用バックアップリスト
//         List<Gui01221DivisionNoForCompareBackup> gui01221dNFCBOutDto = new ArrayList<>();
//         if (CollectionUtils.isNotEmpty(kghKrkMocFree1List)) {
//             for (KghKrkMocFree1OutEntity kKMF1OutEntity : kghKrkMocFree1List) {
//                 // 分割数比較用バックアップリスト出力DTO
//                 Gui01221DivisionNoForCompareBackup dNFCBOutDto = new Gui01221DivisionNoForCompareBackup();
//                 // マスタヘッダID

//                 dNFCBOutDto.setFree1Id(CommonDtoUtil.objValToString(kKMF1OutEntity.getFree1Id()));

//                 // 行の分割数（上）

//                 dNFCBOutDto.setColumnCount(CommonDtoUtil.objValToString(kKMF1OutEntity.getColumnCount()));

//                 // 行の分割数（下）

//                 dNFCBOutDto.setColumnCount2(CommonDtoUtil.objValToString(kKMF1OutEntity.getColumnCount2()));
//                 // データリストを追加
//                 gui01221dNFCBOutDto.add(dNFCBOutDto);

//             }
//         }
//         outDto.setDivisionNoForCompareBackupList(gui01221dNFCBOutDto);

//         /*
//          * ===============5.
//          * カスタマイズ帳票マスタデータ取得のDAOを利用し、カスタマイズ帳票マスタデータ情報を取得する。===============
//          *
//          */
//         KghKrkMocFree2NoFltByCriteriaInEntity kghKrkMocFree2NoFltByCriteriaInEntity = new KghKrkMocFree2NoFltByCriteriaInEntity();
//         // カスタマイズ帳票マスタデータ情報取得
//         List<KghKrkMocFree2NoFltOutEntity> kghKrkMocFree2NoFltList = this.kghMocKrkFree2SelectMapper

//                 .findKghKrkMocFree2NoFltByCriteria(kghKrkMocFree2NoFltByCriteriaInEntity);
//         // 行複写用バックアップリスト
//         List<Gui01221DuplicateRowForBackup> gui01221dIRFBOutDto = new ArrayList<>();
//         if (CollectionUtils.isNotEmpty(kghKrkMocFree2NoFltList)) {
//             for (KghKrkMocFree2NoFltOutEntity kKkMF2NFltOutEntity : kghKrkMocFree2NoFltList) {
//                 // 行複写用バックアップリスト出力DTO
//                 Gui01221DuplicateRowForBackup dIRFBOutDto = new Gui01221DuplicateRowForBackup();
//                 // マスタヘッダID

//                 dIRFBOutDto.setFree1Id(CommonDtoUtil.objValToString(kKkMF2NFltOutEntity.getFree1Id()));

//                 // 区分id

//                 dIRFBOutDto.setKbnId(CommonDtoUtil.objValToString(kKkMF2NFltOutEntity.getKbnId()));

//                 // 項目ID（１～１５）

//                 dIRFBOutDto.setKoumokuId(CommonDtoUtil.objValToString(kKkMF2NFltOutEntity.getKoumokuId()));

//                 // 項目名

//                 dIRFBOutDto.setNameKnj(CommonDtoUtil.objValToString(kKkMF2NFltOutEntity.getNameKnj()));

//                 // 入力方法

//                 dIRFBOutDto.setInputKbn(CommonDtoUtil.objValToString(kKkMF2NFltOutEntity.getInputKbn()));

//                 // 連動区分

//                 dIRFBOutDto.setRendouKbn(CommonDtoUtil.objValToString(kKkMF2NFltOutEntity.getRendouKbn()));

//                 // 文字数

//                 dIRFBOutDto.setWidthCnt(CommonDtoUtil.objValToString(kKkMF2NFltOutEntity.getWidthCnt()));

//                 // データリストを追加
//                 gui01221dIRFBOutDto.add(dIRFBOutDto);
//             }
//         }
//         outDto.setDuplicateRowForBackupList(gui01221dIRFBOutDto);

//         /*
//          * =============== 6.比較用27-30
//          * // カスタマイズ帳票進捗管理対象マスタ情報を取得する。===============
//          *
//          */
//         // 6.1.変数.総進捗管理対象フラグ=1の場合 （1:総進捗管理対象 0:総進捗管理対象外）
//         List<KghMocKrkFreeSncTaishouDataOutEntity> kghMocKrkFreeSncTaishouDataByCriteriaList = new ArrayList<>();
//         if (allSnsFlg.equals(CommonConstants.ALL_SNS_FLG_ON)) {
//             // 下記の27-30 カスタマイズ帳票進捗管理対象マスタデータ取得のDAOを利用し、マスタヘッダIDリストを取得する。
//             KghMocKrkFreeSncTaishouDataByCriteriaInEntity kghMocKrkFreeSncTaishouDataByCriteriaInEntity = new KghMocKrkFreeSncTaishouDataByCriteriaInEntity();
//             // 事業者ID リクエストパラメータ.事業者ID

//             kghMocKrkFreeSncTaishouDataByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
//             kghMocKrkFreeSncTaishouDataByCriteriaList = kghMocKrkFreeSncTaishouSelectMapper

//                     .findKghMocKrkFreeSncTaishouDataByCriteria(kghMocKrkFreeSncTaishouDataByCriteriaInEntity);
//         }

//         /*
//          * =============== 7. 自事業者適用確認処理用の27-18
//          * カスタマイズ帳票適用マスタヘッダIDリストを取得する。===============
//          *
//          */
//         // 7.1.下記の27-18 カスタマイズ帳票適用マスタ取得のDAOを利用し、27-18 カスタマイズ帳票適用マスタ情報を取得する。
//         KghMocKrkFreeTekiyoInfoByCriteriaInEntity kghMocKrkFreeTekiyoInfoByCriteriaInEntity = new KghMocKrkFreeTekiyoInfoByCriteriaInEntity();
//         // 機能区分（固定値 1：モニタリングタイトルマスタ）

//         kghMocKrkFreeTekiyoInfoByCriteriaInEntity.setIiKinouKbn(CommonConstants.KINOUKBN_KINOU1_STRING);
//         // 様式区分(リクエストパラメータ.様式区分)

//         kghMocKrkFreeTekiyoInfoByCriteriaInEntity.setIiYoshikiKbn(inDto.getYoushikiKbn());
//         // 事業者ID(リクエストパラメータ.事業者ID)

//         kghMocKrkFreeTekiyoInfoByCriteriaInEntity.setIlSvJigyoId(inDto.getSvJigyoId());
//         // 27-18 カスタマイズ帳票適用マスタ情報取得
//         List<KghMocKrkFreeTekiyoInfoOutEntity> kghMocKrkFreeTekiyoInfoList = this.kghMocKrkFreeTekiyoSelectMapper

//                 .findKghMocKrkFreeTekiyoInfoByCriteria(kghMocKrkFreeTekiyoInfoByCriteriaInEntity);

//         // 7.2. 「7.1」で取得した27-18 カスタマイズ帳票適用マスタ情報が0件数ではないの場合
//         if (CollectionUtils.isNotEmpty(kghMocKrkFreeTekiyoInfoList)) {
//             KghMocKrkFreeTekiyoInfoOutEntity kghMocKrkFreeTekiyoInfoOutEntity = kghMocKrkFreeTekiyoInfoList.get(0);
//             tekeyouID = kghMocKrkFreeTekiyoInfoOutEntity.getFree1Id();
//         } else {
//             tekeyouID = CommonDtoUtil.strValToInt(CommonConstants.APPLICABLE_OFF);
//         }
//         /*
//          * =============== 8. 一覧用モニタリングタイトルリストの編集を行う。===============
//          *
//          */
//         // 8.1. 下記のカスタマイズ帳票マスタヘッダ取得のDAOを利用し、カスタマイズ帳票マスタヘッダ情報を取得する。
//         //
//         // ※下記処理の「モニタリングタイトル情報リスト」と統一するよう、当「カスタマイズ帳票マスタヘッダ情報」名を「モニタリングタイトル情報リスト」にする。
//         DKghMstKrkMoniTitleByCriteriaInEntity dKghMstKrkMoniTitleByCriteriaInEntity = new DKghMstKrkMoniTitleByCriteriaInEntity();

//         // 機能区分（固定値 1：モニタリングタイトルマスタ））

//         dKghMstKrkMoniTitleByCriteriaInEntity
//                 .setAiKinouKbn(CommonDtoUtil.intValToShort(CommonConstants.KINOUKBN_KINOU1));
//         // 様式区分(リクエストパラメータ.様式区分)

//         dKghMstKrkMoniTitleByCriteriaInEntity.setAiYoushikiKbn(CommonDtoUtil.strValToShort(inDto.getYoushikiKbn()));
//         // カスタマイズ帳票マスタヘッダ情報取得
//         List<DKghMstKrkMoniTitleOutEntity> dKghMstKrkMoniTitleList = this.kghMocKrkFree1SelectMapper

//                 .findDKghMstKrkMoniTitleByCriteria(dKghMstKrkMoniTitleByCriteriaInEntity);

//         // モニタリングタイトルリスト
//         List<Gui01221MonitoringTitle> gui01221mTOutDto = new ArrayList<>();
//         // 8.2.
//         // 「8.1.」で取得したモニタリングタイトル情報リストが0件数ではないの場合、一覧用モニタリングタイトルリストを繰り返して、情報を編集する
//         if (CollectionUtils.isNotEmpty(dKghMstKrkMoniTitleList)) {
//             for (DKghMstKrkMoniTitleOutEntity dKMTOutEntity : dKghMstKrkMoniTitleList) {
//                 // モニタリングタイトルリスト出力DTO
//                 Gui01221MonitoringTitle mtOutDto = new Gui01221MonitoringTitle();
//                 // マスタヘッダID

//                 mtOutDto.setFree1Id(CommonDtoUtil.objValToString(dKMTOutEntity.getFree1Id()));
//                 // 帳票タイトル

//                 mtOutDto.setTitleKnj(CommonDtoUtil.objValToString(dKMTOutEntity.getTitleKnj()));

//                 // 行の分割数（上）

//                 mtOutDto.setColumnCount(CommonDtoUtil.objValToString(dKMTOutEntity.getColumnCount()));
//                 // 行の分割数（下）

//                 mtOutDto.setColumnCount2(CommonDtoUtil.objValToString(dKMTOutEntity.getColumnCount2()));
//                 // 表示順

//                 mtOutDto.setSort(CommonDtoUtil.objValToString(dKMTOutEntity.getSort()));
//                 // 印刷文字サイズ

//                 mtOutDto.setFontSize(CommonDtoUtil.objValToString(dKMTOutEntity.getFontSize()));
//                 // 用紙サイズ

//                 mtOutDto.setYoushiSize(CommonDtoUtil.objValToString(dKMTOutEntity.getYoushiSize()));
//                 // 使用中フラグ

//                 mtOutDto.setUseFlg(CommonDtoUtil.objValToString(dKMTOutEntity.getUseFlg()));
//                 // 初期値作成フラグ

//                 mtOutDto.setDefUpdateFlg(CommonDtoUtil.objValToString(dKMTOutEntity.getDefUpdateFlg()));
//                 // 適用フラグ

//                 mtOutDto.setDmyTekiyouFlg(CommonDtoUtil.objValToString(dKMTOutEntity.getDmyTekiyouFlg()));
//                 // 他事業者適用フラグ

//                 mtOutDto.setDmyOtherTekiyouFlg(CommonDtoUtil.objValToString(dKMTOutEntity.getDmyOtherTekiyouFlg()));
//                 // 複写元マスタヘッダID

//                 mtOutDto.setDmyFree1Id(CommonDtoUtil.objValToString(dKMTOutEntity.getDmyFree1Id()));
//                 // ロックフラグ

//                 mtOutDto.setDmyLockFlg(CommonDtoUtil.objValToString(dKMTOutEntity.getDmyLockFlg()));
//                 // 進捗管理対象フラグ

//                 mtOutDto.setDmySncFlg(CommonDtoUtil.objValToString(dKMTOutEntity.getDmySncFlg()));
//                 // 固定様式区分

//                 mtOutDto.setKoteiKbn(CommonDtoUtil.objValToString(dKMTOutEntity.getKoteiKbn()));

//                 // 8.2.1. モニタリングタイトル情報.進捗管理対象フラグを設定する
//                 // 「6.」で取得した27-30 カスタマイズ帳票進捗管理対象マスタ情報が0件数ではないの場合
//                 // 且つ モニタリングタイトル情報リスト.マスタヘッダIDは「6.」27-30 カスタマイズ帳票進捗管理対象マスタ情報に存在する場合
//                 boolean exists = kghMocKrkFreeSncTaishouDataByCriteriaList.stream()
//                         .anyMatch(o -> dKMTOutEntity.getFree1Id().equals(o.getFree1Id()));

//                 if (exists) {
//                     // モニタリングタイトル情報リスト[ループ回数].進捗管理対象フラグ=1 （1:進捗管理対象）を設定する
//                     mtOutDto.setDmySncFlg(CommonConstants.DMY_SNS_FLG_ON);
//                 } else {
//                     // モニタリングタイトル情報リスト[ループ回数].進捗管理対象フラグ=0 （0:進捗管理対象外）を設定する
//                     mtOutDto.setDmySncFlg(CommonConstants.DMY_SNS_FLG_OFF);
//                 }

//                 // 8.2.2. モニタリングタイトル情報.ロックフラグを設定する。
//                 // モニタリングタイトル情報リスト[ループ回数].固定様式区分が”1” または ”2”の場合
//                 if (dKMTOutEntity.getKoteiKbn() == 1 || dKMTOutEntity.getKoteiKbn() == 2) {
//                     // モニタリングタイトル情報リスト[ループ回数].ロックフラグ=1 （1:ロック対象）を設定する
//                     mtOutDto.setDmyLockFlg(CommonConstants.DMY_LOCK_FLG_ON);
//                 } else {
//                     // モニタリングタイトル情報リスト[ループ回数].ロックフラグ=0 （0:ロック対象外）を設定する
//                     mtOutDto.setDmyLockFlg(CommonConstants.DMY_LOCK_FLG_OFF);
//                 }

//                 // 8.2.3. マスタIDの使用件数を取得し、使用中フラグを設定する。
//                 // 8.2.3.1. リクエストパラメータ.様式区分が’2”の場合
//                 if (CommonConstants.YOUSHIKI_KBN_2.equals(inDto.getYoushikiKbn())) {
//                     // 下記の28-04 サービス評価表ヘッダ情報取得のDAOを利用し、28-04 サービス評価表ヘッダ情報を取得する。
//                     KycTucHyouka1ByCriteriaInEntity kycTucHyouka1ByCriteriaInEntity = new KycTucHyouka1ByCriteriaInEntity();

//                     // マスタID(モニタリングタイトル情報リスト.マスタヘッダID)

//                     kycTucHyouka1ByCriteriaInEntity.setFree1Id(dKMTOutEntity.getFree1Id());
//                     // 28-04 サービス評価表ヘッダ情報取得
//                     KycTucHyouka1OutEntity kycTucHyouka1 = this.kycTucHyouka1SelectMapper
//                             .countKycTucHyouka1ByCriteria(kycTucHyouka1ByCriteriaInEntity);

//                     if (kycTucHyouka1.getCNT() > CommonConstants.NUMBER_ZERO) {
//                         // 「※使用中の場合、使用中フラグをONにする」
//                         mtOutDto.setUseFlg(CommonConstants.USE_FLG_USE);
//                     } else {
//                         // 「※未使用の場合、使用中フラグをOFFにする」
//                         mtOutDto.setUseFlg(CommonConstants.USE_FLG_NOT_USE);
//                     }

//                 } else {
//                     // 下記のモニタリング記録表ヘッダ情報取得のDAOを利用し、28-01 モニタリング記録表ヘッダ情報を取得する。
//                     CpnTucCmoni1ByCriteriaInEntity cpnTucCmoni1ByCriteriaInEntity = new CpnTucCmoni1ByCriteriaInEntity();

//                     // マスタID(モニタリングタイトル情報リスト.マスタヘッダID)

//                     cpnTucCmoni1ByCriteriaInEntity.setIlTitleId(dKMTOutEntity.getFree1Id());
//                     // 28-01 モニタリング記録表ヘッダ
//                     CpnTucCmoni1OutEntity cpnTucCmoni1 = this.cpnTucCmoni1SelectMapper
//                             .countCpnTucCmoni1ByCriteria(cpnTucCmoni1ByCriteriaInEntity);

//                     if (cpnTucCmoni1.getCnt() > CommonConstants.NUMBER_ZERO) {
//                         // 「※使用中の場合、使用中フラグをONにする」
//                         mtOutDto.setUseFlg(CommonConstants.USE_FLG_USE);
//                     } else {
//                         // 「※未使用の場合、使用中フラグをOFFにする」
//                         mtOutDto.setUseFlg(CommonConstants.USE_FLG_NOT_USE);

//                     }

//                 }

//                 // 8.2.4. 「変数.適用されているマスタID」を利用して、下記の処理を実施する。
//                 // 8.2.4.1. 「7.」で設定されている変数.適用されているマスタID と
//                 // モニタリングタイトル情報リスト[ループ回数].マスタヘッダID
//                 // は同じの場合。
//                 if (tekeyouID == dKMTOutEntity.getFree1Id()) {
//                     // モニタリングタイトル情報リスト[ループ回数].適用フラグ=1 （1:適用対象）を設定する
//                     mtOutDto.setDmyTekiyouFlg(CommonConstants.DMY_TEKIYOU_FLG_ON);

//                 } else {
//                     // モニタリングタイトル情報リスト[ループ回数].適用フラグ=0 （0:適用対象外）を設定する
//                     mtOutDto.setDmyTekiyouFlg(CommonConstants.DMY_TEKIYOU_FLG_OFF);

//                     // 8.2.4.2.2 .下記の27-18 カスタマイズ帳票適用マスタ取得のDAOを利用し、27-18
//                     // カスタマイズ帳票適用マスタ情報を取得する
//                     KghMocKrkFreeTekiyoByCriteriaInEntity kghMocKrkFreeTekiyoByCriteriaInEntity = new KghMocKrkFreeTekiyoByCriteriaInEntity();

//                     // マスタID(モニタリングタイトル情報リスト.マスタヘッダID)

//                     kghMocKrkFreeTekiyoByCriteriaInEntity.setLlFree1Id(dKMTOutEntity.getFree1Id());
//                     // 機能区分（固定値 1：モニタリングタイトルマスタ））

//                     kghMocKrkFreeTekiyoByCriteriaInEntity
//                             .setIiKinouKbn(CommonDtoUtil.intValToShort(CommonConstants.KINOUKBN_KINOU1));
//                     // 27-18 カスタマイズ帳票適用マスタ情報取得
//                     KghMocKrkFreeTekiyoOutEntity kghMocKrkFreeTekiyo = this.kghMocKrkFreeTekiyoSelectMapper

//                             .countKghMocKrkFreeTekiyoByCriteria(kghMocKrkFreeTekiyoByCriteriaInEntity);
//                     if (kghMocKrkFreeTekiyo.getCNT() > CommonConstants.NUMBER_ZERO) {
//                         // 「※適用されている場合、他事業者適用フラグをONにする」

//                         mtOutDto.setDmyOtherTekiyouFlg(CommonConstants.DMY_OTHER_TEKIYOU_FLG_ON);
//                     } else {
//                         // ※適用されていない場合、他事業者適用フラグをOFFにする

//                         mtOutDto.setDmyOtherTekiyouFlg(CommonConstants.DMY_OTHER_TEKIYOU_FLG_OFF);
//                     }
//                 }

//                 // 8.2.5. 「分割数（上）更新状態」の初期化を行う
//                 // 予防評価表タイトル情報リスト[ループ回数].分割数（上）更新状態=0

//                 mtOutDto.setColumnCountStatus(CommonConstants.COLUMN_COUNT_STATUS_OFF);

//                 // データリストを追加
//                 gui01221mTOutDto.add(mtOutDto);

//             }

//         }
//         outDto.setMonitoringTitleList(gui01221mTOutDto);

//         /*
//          * ===============9. 上記処理で取得した結果レスポンスを返却する。===============
//          *
//          */
//         // ※返却する情報の編集要領は「レスポンスパラメータ詳細」を参照
//         LOG.info(Constants.END);
//         return outDto;
//     }

// }
