package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServicePrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.V00231ServiceUseAnnexedTableUserInfo;
import lombok.Getter;
import lombok.Setter;

/**
 * 「U00891_計画書一括印刷」用の印刷対象履歴.
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class PlanAllPrintReportSubjectHistoryData extends PrintReportServicePrintSubjectHistory {
    /** UID. */
    private static final long serialVersionUID = 1L;

    /** 計画期間ID */
    private String sc1Id;

    /** 提供年月 */
    private String yymmYm;

    /** 利用者IDリスト */
    private List<V00231ServiceUseAnnexedTableUserInfo> userIdList;
}
