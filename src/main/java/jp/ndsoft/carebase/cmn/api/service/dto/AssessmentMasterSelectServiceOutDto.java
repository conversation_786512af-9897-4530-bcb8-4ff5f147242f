package jp.ndsoft.carebase.cmn.api.service.dto;

import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.03.04
 * <AUTHOR>
 * @apiNote GUI00626_アセスメントマスタ 初期情報取得サービス出力Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AssessmentMasterSelectServiceOutDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;
    /** Visio */
    private String visioValue;
    /** Visio分類３ */
    private String visioBunrui3;
    /** Visio更新区分 */
    private String visioUpdateKbn;
    /** 障害等の部位の摘要表示 */
    private String syougaiValue;
    /** 障害分類３ */
    private String syougaiBunrui3;
    /** 障害更新区分 */
    private String syougaiUpdateKbn;
    /** 全体のまとめ */
    private String zenntaiValue;
    /** 全体分類３ */
    private String zenntaiBunrui3;
    /** 全体更新区分 */
    private String zenntaiUpdateKbn;
}
