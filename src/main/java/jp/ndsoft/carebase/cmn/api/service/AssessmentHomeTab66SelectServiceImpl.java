package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.*;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00803CpnTucGdlKan16Info;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeTab66SelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeTab66SelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.*;
import jp.ndsoft.carebase.common.dao.mysql.mapper.*;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @since 2025.04.08
 * <AUTHOR>
 * @implNote GUI00803_［アセスメント］画面（居宅）（6⑥） 初期情報取得
 */
@Service
public class AssessmentHomeTab66SelectServiceImpl
        extends
        SelectServiceImpl<AssessmentHomeTab66SelectServiceInDto, AssessmentHomeTab66SelectServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）情報取得 */
    @Autowired
    private CpnTucGdl4Kan16H21SelectMapper cpnTucGdl4Kan16H21SelectMapper;

    /** ＧＬ＿⑥医療・健康関係（Ｒ３改訂）情報取得 */
    @Autowired
    private CpnTucGdl5Kan16R3SelectMapper cpnTucGdl5Kan16R3SelectMapper;

    /**
     * ［アセスメント］画面（居宅）（6⑥）初期情報取得を取得する
     * 
     * @param inDto ［アセスメント］画面（居宅）（6⑥）入力DTO.
     * @return ［アセスメント］画面（居宅）（6⑥）出力DTO
     */
    @Override
    protected AssessmentHomeTab66SelectServiceOutDto mainProcess(
            final AssessmentHomeTab66SelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        // 医療・健康関係情報（H21/4改訂、R3/4改訂）
        Gui00803CpnTucGdlKan16Info cpnTucGdlKan16Info = new Gui00803CpnTucGdlKan16Info();

        // ===============API-90.初期情報取得 START===============//
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ==============='2. 医療・健康関係情報の取得===============
         * 
         */
        // 2.1.
        // リクエストパラメータ詳細.改訂フラグが4（H21/4改訂）の場合、下記のＧＬ＿⑥医療・健康関係（H21/4改訂）情報取得のDAOを利用し、医療・健康関係情報を取得する。
        if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {

            // DAOパラメータを作成
            CpnTucGdl4Kan16H21ByCriteriaInEntity cpnTucGdl4Kan16H21ByCriteriaInEntity = new CpnTucGdl4Kan16H21ByCriteriaInEntity();
            // アセスメントID
            cpnTucGdl4Kan16H21ByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            cpnTucGdl4Kan16H21ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            // DAOを実行
            List<CpnTucGdl4Kan16H21OutEntity> cpnTucGdl4Kan16H21List = this.cpnTucGdl4Kan16H21SelectMapper
                    .findCpnTucGdl4Kan16H21ByCriteria(cpnTucGdl4Kan16H21ByCriteriaInEntity);

            if (CollectionUtils.isNotEmpty(cpnTucGdl4Kan16H21List)) {

                CpnTucGdl4Kan16H21OutEntity CpnTucGdl4Kan16H21 = cpnTucGdl4Kan16H21List
                        .get(0);
                // 医療・健康関係情報（H21/4改訂）set
                setcpnTucGdlKan16InfoH21(cpnTucGdlKan16Info, CpnTucGdl4Kan16H21);
            }
        }
        // 2.2. リクエストパラメータ詳細.改訂フラグが5（R3/4改訂）の場合、下記のXXXXのDAOを利用し、医療・健康関係情報を取得する。
        else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {
            // DAOパラメータを作成
            Gdl5Ass10R3ByCriteriaInEntity gdl5Ass10R3ByCriteriaInEntity = new Gdl5Ass10R3ByCriteriaInEntity();
            // アセスメントID
            gdl5Ass10R3ByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            gdl5Ass10R3ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            // DAOを実行
            List<Gdl5Ass10R3OutEntity> gdl5Ass10R3List = this.cpnTucGdl5Kan16R3SelectMapper
                    .findGdl5Ass10R3ByCriteria(gdl5Ass10R3ByCriteriaInEntity);

            if (CollectionUtils.isNotEmpty(gdl5Ass10R3List)) {

                Gdl5Ass10R3OutEntity CpnTucGdl4Kan16H21 = gdl5Ass10R3List
                        .get(0);
                // 医療・健康関係情報（R3/4改訂）set
                setcpnTucGdlKan16InfoR3(cpnTucGdlKan16Info, CpnTucGdl4Kan16H21);
            }

        }

        // ===============API-90.初期情報取得 END===============//

        // 戻り情報を設定
        AssessmentHomeTab66SelectServiceOutDto outDto = new AssessmentHomeTab66SelectServiceOutDto();

        // 医療・健康関係情報（H21/4改訂、R3/4改訂）リスト
        outDto.setCpnTucGdlKan16Info(cpnTucGdlKan16Info);
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 医療・健康関係情報（H21/4改訂） set
     * 
     * @param cpnTucGdlKan16Info --医療・健康関係情報（H21/4改訂）
     * @param CpnTucGdl4Kan16H21 --医療・健康関係情報（H21/4改訂）の取得
     */
    private void setcpnTucGdlKan16InfoH21(Gui00803CpnTucGdlKan16Info cpnTucGdlKan16Info,
            CpnTucGdl4Kan16H21OutEntity CpnTucGdl4Kan16H21) {
        // 家族実施1
        cpnTucGdlKan16Info.setFamJisshi1(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getFamJisshi1()));
        // 家族実施2
        cpnTucGdlKan16Info.setFamJisshi2(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getFamJisshi2()));
        // 家族実施3
        cpnTucGdlKan16Info.setFamJisshi3(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getFamJisshi3()));
        // 家族実施4
        cpnTucGdlKan16Info.setFamJisshi4(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getFamJisshi4()));
        // 家族実施5
        cpnTucGdlKan16Info.setFamJisshi5(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getFamJisshi5()));
        // 家族実施6
        cpnTucGdlKan16Info.setFamJisshi6(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getFamJisshi6()));
        // 認定項目6-1
        cpnTucGdlKan16Info.setBango61(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango61()));
        // 認定項目6-2
        cpnTucGdlKan16Info.setBango62(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango62()));
        // 認定項目6-3
        cpnTucGdlKan16Info.setBango63(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango63()));
        // 認定項目6-4
        cpnTucGdlKan16Info.setBango64(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango64()));
        // 認定項目6-5
        cpnTucGdlKan16Info.setBango65(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango65()));
        // 認定項目6-6
        cpnTucGdlKan16Info.setBango66(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango66()));
        // 認定項目6-7
        cpnTucGdlKan16Info.setBango67(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango67()));
        // 認定項目6-8
        cpnTucGdlKan16Info.setBango68(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango68()));
        // 認定項目6-9
        cpnTucGdlKan16Info.setBango69(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango69()));
        // 認定項目6-10
        cpnTucGdlKan16Info.setBango610(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango610()));
        // 認定項目6-11
        cpnTucGdlKan16Info.setBango611(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango611()));
        // 認定項目6-12
        cpnTucGdlKan16Info.setBango612(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango612()));
        // サービス実施1
        cpnTucGdlKan16Info.setSerJisshi1(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getSerJisshi1()));
        // サービス実施2
        cpnTucGdlKan16Info.setSerJisshi2(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getSerJisshi2()));
        // サービス実施3
        cpnTucGdlKan16Info.setSerJisshi3(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getSerJisshi3()));
        // サービス実施4
        cpnTucGdlKan16Info.setSerJisshi4(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getSerJisshi4()));
        // サービス実施5
        cpnTucGdlKan16Info.setSerJisshi5(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getSerJisshi5()));
        // サービス実施6
        cpnTucGdlKan16Info.setSerJisshi6(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getSerJisshi6()));
        // 希望1
        cpnTucGdlKan16Info.setKibo1(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKibo1()));
        // 希望2
        cpnTucGdlKan16Info.setKibo2(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKibo2()));
        // 希望3
        cpnTucGdlKan16Info.setKibo3(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKibo3()));
        // 希望4
        cpnTucGdlKan16Info.setKibo4(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKibo4()));
        // 希望5
        cpnTucGdlKan16Info.setKibo5(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKibo5()));
        // 希望6
        cpnTucGdlKan16Info.setKibo6(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKibo6()));
        // 要援助・計画1
        cpnTucGdlKan16Info.setKeikaku1(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKeikaku1()));
        // 要援助・計画2
        cpnTucGdlKan16Info.setKeikaku2(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKeikaku2()));
        // 要援助・計画3
        cpnTucGdlKan16Info.setKeikaku3(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKeikaku3()));
        // 要援助・計画4
        cpnTucGdlKan16Info.setKeikaku4(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKeikaku4()));
        // 要援助・計画5
        cpnTucGdlKan16Info.setKeikaku5(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKeikaku5()));
        // 要援助・計画6
        cpnTucGdlKan16Info.setKeikaku6(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKeikaku6()));
        // 現状（バイタルサインのチェック）
        cpnTucGdlKan16Info.setGenjo1(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo1()));
        // 現状（定期的な病状観察）
        cpnTucGdlKan16Info.setGenjo2(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo2()));
        // 現状（内服薬）
        cpnTucGdlKan16Info.setGenjo3(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo3()));
        // 現状（座薬（緩下剤、解熱剤等））
        cpnTucGdlKan16Info.setGenjo4(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo4()));
        // 現状（眼・耳・鼻等の外用薬の使用等）
        cpnTucGdlKan16Info.setGenjo5(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo5()));
        // 現状（温・冷あん法、湿布貼付等）
        cpnTucGdlKan16Info.setGenjo6(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo6()));
        // 現状（注射）
        cpnTucGdlKan16Info.setGenjo7(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo7()));
        // 現状（吸引）
        cpnTucGdlKan16Info.setGenjo8(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo8()));
        // 現状（吸入）
        cpnTucGdlKan16Info.setGenjo9(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo9()));
        // 現状（自己注射（インスリン療法））
        cpnTucGdlKan16Info.setGenjo10(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo10()));
        // 現状（経管栄養法）
        cpnTucGdlKan16Info.setGenjo11(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo11()));
        // 現状（中心静脈栄養法）
        cpnTucGdlKan16Info.setGenjo12(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo12()));
        // 現状（酸素療法）
        cpnTucGdlKan16Info.setGenjo13(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo13()));
        // 現状（人口呼吸療法）
        cpnTucGdlKan16Info.setGenjo14(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo14()));
        // 現状（気管カニューレ管理）
        cpnTucGdlKan16Info.setGenjo15(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo15()));
        // 現状（自己導尿）
        cpnTucGdlKan16Info.setGenjo16(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo16()));
        // 現状（自己腹膜灌流）
        cpnTucGdlKan16Info.setGenjo17(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo17()));
        // 現状（膀胱留置カテーテル管理）
        cpnTucGdlKan16Info.setGenjo18(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo18()));
        // 現状（人工肛門・人工膀胱管理）
        cpnTucGdlKan16Info.setGenjo19(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo19()));
        // 現状（疼痛管理）
        cpnTucGdlKan16Info.setGenjo20(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo20()));
        // 現状（褥瘡管理）
        cpnTucGdlKan16Info.setGenjo21(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo21()));
        // 計画（バイタルサインのチェック）
        cpnTucGdlKan16Info.setKkeikaku1(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku1()));
        // 計画（定期的な病状観察）
        cpnTucGdlKan16Info.setKkeikaku2(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku2()));
        // 計画（内服薬）
        cpnTucGdlKan16Info.setKkeikaku3(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku3()));
        // 計画（座薬（緩下剤、解熱剤等））
        cpnTucGdlKan16Info.setKkeikaku4(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku4()));
        // 計画（眼・耳・鼻等の外用薬の使用等）
        cpnTucGdlKan16Info.setKkeikaku5(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku5()));
        // 計画（温・冷あん法、湿布貼付等）
        cpnTucGdlKan16Info.setKkeikaku6(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku6()));
        // 計画（注射）
        cpnTucGdlKan16Info.setKkeikaku7(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku7()));
        // 計画（吸引）
        cpnTucGdlKan16Info.setKkeikaku8(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku8()));
        // 計画（吸入）
        cpnTucGdlKan16Info.setKkeikaku9(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku9()));
        // 計画（自己注射（インスリン療法））
        cpnTucGdlKan16Info.setKkeikaku10(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku10()));
        // 計画（経管栄養法）
        cpnTucGdlKan16Info.setKkeikaku11(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku11()));
        // 計画（中心静脈栄養法）
        cpnTucGdlKan16Info.setKkeikaku12(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku12()));
        // 計画（酸素療法）
        cpnTucGdlKan16Info.setKkeikaku13(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku13()));
        // 計画（人口呼吸療法）
        cpnTucGdlKan16Info.setKkeikaku14(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku14()));
        // 計画（気管カニューレ管理）
        cpnTucGdlKan16Info.setKkeikaku15(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku15()));
        // 計画（自己導尿）
        cpnTucGdlKan16Info.setKkeikaku16(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku16()));
        // 計画（自己腹膜灌流）
        cpnTucGdlKan16Info.setKkeikaku17(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku17()));
        // 計画（膀胱留置カテーテル管理）
        cpnTucGdlKan16Info.setKkeikaku18(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku18()));
        // 計画（人工肛門・人工膀胱管理）
        cpnTucGdlKan16Info.setKkeikaku19(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku19()));
        // 計画（疼痛管理）
        cpnTucGdlKan16Info.setKkeikaku20(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku20()));
        // 計画（褥瘡管理）
        cpnTucGdlKan16Info.setKkeikaku21(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku21()));
        // 特記（医療・健康）
        cpnTucGdlKan16Info.setMemo1Knj(CpnTucGdl4Kan16H21.getMemo1Knj());
    }

    /**
     * 医療・健康関係情報（R3/4改訂） set
     * 
     * @param cpnTucGdlKan16Info --医療・健康関係情報（R3/4改訂）
     * @param CpnTucGdl4Kan16H21 --医療・健康関係情報（R3/4改訂）の取得
     */
    private void setcpnTucGdlKan16InfoR3(Gui00803CpnTucGdlKan16Info cpnTucGdlKan16Info,
            Gdl5Ass10R3OutEntity CpnTucGdl4Kan16H21) {
        // 家族実施1
        cpnTucGdlKan16Info.setFamJisshi1(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getFamJisshi1()));
        // 家族実施2
        cpnTucGdlKan16Info.setFamJisshi2(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getFamJisshi2()));
        // 家族実施3
        cpnTucGdlKan16Info.setFamJisshi3(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getFamJisshi3()));
        // 家族実施4
        cpnTucGdlKan16Info.setFamJisshi4(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getFamJisshi4()));
        // 家族実施5
        cpnTucGdlKan16Info.setFamJisshi5(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getFamJisshi5()));
        // 家族実施6
        cpnTucGdlKan16Info.setFamJisshi6(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getFamJisshi6()));
        // 認定項目6-1
        cpnTucGdlKan16Info.setBango61(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango61()));
        // 認定項目6-2
        cpnTucGdlKan16Info.setBango62(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango62()));
        // 認定項目6-3
        cpnTucGdlKan16Info.setBango63(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango63()));
        // 認定項目6-4
        cpnTucGdlKan16Info.setBango64(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango64()));
        // 認定項目6-5
        cpnTucGdlKan16Info.setBango65(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango65()));
        // 認定項目6-6
        cpnTucGdlKan16Info.setBango66(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango66()));
        // 認定項目6-7
        cpnTucGdlKan16Info.setBango67(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango67()));
        // 認定項目6-8
        cpnTucGdlKan16Info.setBango68(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango68()));
        // 認定項目6-9
        cpnTucGdlKan16Info.setBango69(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango69()));
        // 認定項目6-10
        cpnTucGdlKan16Info.setBango610(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango610()));
        // 認定項目6-11
        cpnTucGdlKan16Info.setBango611(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango611()));
        // 認定項目6-12
        cpnTucGdlKan16Info.setBango612(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getBango612()));
        // サービス実施1
        cpnTucGdlKan16Info.setSerJisshi1(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getSerJisshi1()));
        // サービス実施2
        cpnTucGdlKan16Info.setSerJisshi2(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getSerJisshi2()));
        // サービス実施3
        cpnTucGdlKan16Info.setSerJisshi3(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getSerJisshi3()));
        // サービス実施4
        cpnTucGdlKan16Info.setSerJisshi4(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getSerJisshi4()));
        // サービス実施5
        cpnTucGdlKan16Info.setSerJisshi5(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getSerJisshi5()));
        // サービス実施6
        cpnTucGdlKan16Info.setSerJisshi6(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getSerJisshi6()));
        // 希望1
        cpnTucGdlKan16Info.setKibo1(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKibo1()));
        // 希望2
        cpnTucGdlKan16Info.setKibo2(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKibo2()));
        // 希望3
        cpnTucGdlKan16Info.setKibo3(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKibo3()));
        // 希望4
        cpnTucGdlKan16Info.setKibo4(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKibo4()));
        // 希望5
        cpnTucGdlKan16Info.setKibo5(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKibo5()));
        // 希望6
        cpnTucGdlKan16Info.setKibo6(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKibo6()));
        // 要援助・計画1
        cpnTucGdlKan16Info.setKeikaku1(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKeikaku1()));
        // 要援助・計画2
        cpnTucGdlKan16Info.setKeikaku2(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKeikaku2()));
        // 要援助・計画3
        cpnTucGdlKan16Info.setKeikaku3(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKeikaku3()));
        // 要援助・計画4
        cpnTucGdlKan16Info.setKeikaku4(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKeikaku4()));
        // 要援助・計画5
        cpnTucGdlKan16Info.setKeikaku5(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKeikaku5()));
        // 要援助・計画6
        cpnTucGdlKan16Info.setKeikaku6(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKeikaku6()));
        // 現状（バイタルサインのチェック）
        cpnTucGdlKan16Info.setGenjo1(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo1()));
        // 現状（定期的な病状観察）
        cpnTucGdlKan16Info.setGenjo2(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo2()));
        // 現状（内服薬）
        cpnTucGdlKan16Info.setGenjo3(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo3()));
        // 現状（座薬（緩下剤、解熱剤等））
        cpnTucGdlKan16Info.setGenjo4(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo4()));
        // 現状（眼・耳・鼻等の外用薬の使用等）
        cpnTucGdlKan16Info.setGenjo5(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo5()));
        // 現状（温・冷あん法、湿布貼付等）
        cpnTucGdlKan16Info.setGenjo6(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo6()));
        // 現状（注射）
        cpnTucGdlKan16Info.setGenjo7(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo7()));
        // 現状（吸引）
        cpnTucGdlKan16Info.setGenjo8(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo8()));
        // 現状（吸入）
        cpnTucGdlKan16Info.setGenjo9(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo9()));
        // 現状（自己注射（インスリン療法））
        cpnTucGdlKan16Info.setGenjo10(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo10()));
        // 現状（経管栄養法）
        cpnTucGdlKan16Info.setGenjo11(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo11()));
        // 現状（中心静脈栄養法）
        cpnTucGdlKan16Info.setGenjo12(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo12()));
        // 現状（酸素療法）
        cpnTucGdlKan16Info.setGenjo13(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo13()));
        // 現状（人口呼吸療法）
        cpnTucGdlKan16Info.setGenjo14(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo14()));
        // 現状（気管カニューレ管理）
        cpnTucGdlKan16Info.setGenjo15(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo15()));
        // 現状（自己導尿）
        cpnTucGdlKan16Info.setGenjo16(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo16()));
        // 現状（自己腹膜灌流）
        cpnTucGdlKan16Info.setGenjo17(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo17()));
        // 現状（膀胱留置カテーテル管理）
        cpnTucGdlKan16Info.setGenjo18(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo18()));
        // 現状（人工肛門・人工膀胱管理）
        cpnTucGdlKan16Info.setGenjo19(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo19()));
        // 現状（疼痛管理）
        cpnTucGdlKan16Info.setGenjo20(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo20()));
        // 現状（褥瘡管理）
        cpnTucGdlKan16Info.setGenjo21(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getGenjo21()));
        // 計画（バイタルサインのチェック）
        cpnTucGdlKan16Info.setKkeikaku1(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku1()));
        // 計画（定期的な病状観察）
        cpnTucGdlKan16Info.setKkeikaku2(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku2()));
        // 計画（内服薬）
        cpnTucGdlKan16Info.setKkeikaku3(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku3()));
        // 計画（座薬（緩下剤、解熱剤等））
        cpnTucGdlKan16Info.setKkeikaku4(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku4()));
        // 計画（眼・耳・鼻等の外用薬の使用等）
        cpnTucGdlKan16Info.setKkeikaku5(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku5()));
        // 計画（温・冷あん法、湿布貼付等）
        cpnTucGdlKan16Info.setKkeikaku6(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku6()));
        // 計画（注射）
        cpnTucGdlKan16Info.setKkeikaku7(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku7()));
        // 計画（吸引）
        cpnTucGdlKan16Info.setKkeikaku8(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku8()));
        // 計画（吸入）
        cpnTucGdlKan16Info.setKkeikaku9(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku9()));
        // 計画（自己注射（インスリン療法））
        cpnTucGdlKan16Info.setKkeikaku10(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku10()));
        // 計画（経管栄養法）
        cpnTucGdlKan16Info.setKkeikaku11(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku11()));
        // 計画（中心静脈栄養法）
        cpnTucGdlKan16Info.setKkeikaku12(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku12()));
        // 計画（酸素療法）
        cpnTucGdlKan16Info.setKkeikaku13(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku13()));
        // 計画（人口呼吸療法）
        cpnTucGdlKan16Info.setKkeikaku14(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku14()));
        // 計画（気管カニューレ管理）
        cpnTucGdlKan16Info.setKkeikaku15(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku15()));
        // 計画（自己導尿）
        cpnTucGdlKan16Info.setKkeikaku16(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku16()));
        // 計画（自己腹膜灌流）
        cpnTucGdlKan16Info.setKkeikaku17(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku17()));
        // 計画（膀胱留置カテーテル管理）
        cpnTucGdlKan16Info.setKkeikaku18(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku18()));
        // 計画（人工肛門・人工膀胱管理）
        cpnTucGdlKan16Info.setKkeikaku19(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku19()));
        // 計画（疼痛管理）
        cpnTucGdlKan16Info.setKkeikaku20(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku20()));
        // 計画（褥瘡管理）
        cpnTucGdlKan16Info.setKkeikaku21(CommonDtoUtil.objValToString(CpnTucGdl4Kan16H21.getKkeikaku21()));
        // 特記（医療・健康）
        cpnTucGdlKan16Info.setMemo1Knj(CpnTucGdl4Kan16H21.getMemo1Knj());
    }
}
