package jp.ndsoft.carebase.cmn.api.report.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.invoke.MethodHandles;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PreventionPlanA31ReportServiceAssryoData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PreventionPlanA31ReportServiceShienData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PreventionPlanA31ReportServiceSogoKadaiData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PreventionPlanA31ReportServiceSogoKadaiDataAndShien;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PreventionPlanA42ReportServiceList1Data;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PreventionPlanA42ReportServiceList2Data;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServiceDbNoSaveData;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkBase02Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.PreventionPlanA42ReportAllDto;
import jp.ndsoft.carebase.cmn.api.report.dto.PreventionPlanA42ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.PreventionPlanA42ReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.PreventionPlanA42ReportServiceParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPrnPreSrw1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinHogoJohoSetteiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinHogoJohoSetteiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KycKkak00ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KycKkak00OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KycKkakTko1LByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KycKkakTko1LOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KycKkakTko2KadaiSortByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KycKkakTko2KadaiSortOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KycKkakTko3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KycKkakTko3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSysiniSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMstChouhyouInkanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KycTucPlan11SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KycTucPlan12SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KycTucPlan13SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KycTucPlan14SelectMapper;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.exception.SystemException;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import jp.ndsoft.smh.framework.util.DateConverter;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;


/**
 *
 * E00402_介護予防サービス-支援計画表（A4横2枚）
 *
 * <AUTHOR>
 */
@Service("PreventionPlanA42Report")
public class PreventionPlanA42ReportService
        extends
        PdfReportServiceImpl<PreventionPlanA42ReportServiceParameterModel, PreventionPlanA42ReportServiceOutDto> {

    /**
     * ロガー.
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // 設定の書込（NEXのパソコン単位の設定）
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    // ケアプラン帳票印鑑欄情報取得
    @Autowired
    private CpnMstChouhyouInkanSelectMapper cpnMstChouhyouInkanSelectMapper;

    // 「目標とする生活」情報取得
    @Autowired
    private KycTucPlan11SelectMapper kycTucPlan11SelectMapper;

    // アセスメント領域と課題リスト取得
    @Autowired
    private KycTucPlan12SelectMapper kycTucPlan12SelectMapper;

    // 総合的課題と目標リスト取得
    @Autowired
    private KycTucPlan13SelectMapper kycTucPlan13SelectMapper;

    // 支援計画リスト取得
    @Autowired
    private KycTucPlan14SelectMapper kycTucPlan14SelectMapper;

    // 個人保護用情報の取得
    @Autowired
    private ComMocSysiniSelectMapper comMocSysiniSelectMapper;

    // 利用者（年齢以外）情報取得
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;

    /** Nds3GkBase02Logicロジッククラス */
    @Autowired
    private Nds3GkBase02Logic nds3GkBase02Logic;

    // フォントサイズ
    private static String fontSize = "9";

    /**
     * 帳票出力
     *
     *
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final PreventionPlanA42ReportServiceParameterModel inDto,
            final PreventionPlanA42ReportServiceOutDto outDto)
            throws Exception {
        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final PreventionPlanA42ReportServiceInDto reportParameter = (PreventionPlanA42ReportServiceInDto) getReportParameters(
                inDto, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // メインテンプレート（ALL）のリソースを取得
        InputStream mainIs = new FileInputStream(
                ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_E00402_PREVENTION_PLAN_A42_ALL));

        // サブレポートのコンパイル
        compileSubReports(reportParameter);

        // 4. 統合データソースの作成
        List<PreventionPlanA42ReportAllDto> combinedDataList = Arrays.asList(
                new PreventionPlanA42ReportAllDto(reportParameter.getDataSource(), reportParameter.getDataSource()));
     
        // 統合データソースの作成
        final JRBeanCollectionDataSource combinedDataSource = new JRBeanCollectionDataSource(combinedDataList);

        // メインテンプレートのコンパイル
        final JasperReport mainReport = JasperCompileManager.compileReport(mainIs);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(mainReport,
                reportParameter.getParameters(), combinedDataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(inDto, jasperPrint);

        super.setFilepath(inDto, outDto, pdf, pdf.getName());
        LOG.info(Constants.END);
    }

    /**
     * サブレポートをコンパイルしてパラメータに設定
     *
     * @param reportParameter レポートパラメータ
     * @throws Exception 例外
     */
    private void compileSubReports(PreventionPlanA42ReportServiceInDto reportParameter) throws Exception {
        // サブレポート1のコンパイル
        InputStream subIs1 = new FileInputStream(
                ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_E00402_PREVENTION_PLAN_A42_1));
        JasperReport subReport1 = JasperCompileManager.compileReport(subIs1);

        // サブレポート2のコンパイル
        InputStream subIs2 = new FileInputStream(
                ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_E00402_PREVENTION_PLAN_A42_2));
        JasperReport subReport2 = JasperCompileManager.compileReport(subIs2);

        // サブレポート2のサブレポート（Merge）のコンパイル
        InputStream subMergeIs = new FileInputStream(
                ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_E00402_PREVENTION_PLAN_A42_MERGE));
        JasperReport subMergeReport = JasperCompileManager.compileReport(subMergeIs);

        // パラメータにサブレポートを設定
        reportParameter.getParameters().put(ReportConstants.SUB_REPORT_1, subReport1);
        reportParameter.getParameters().put(ReportConstants.SUB_REPORT_2, subReport2);
        reportParameter.getParameters().put("SUBREPORT_MERGE", subMergeReport);

        // サブレポート用のデータソースを設定
        reportParameter.getParameters().put("SUBREPORT_1_DATASOURCE", reportParameter.getDataList1());
        reportParameter.getParameters().put("SUBREPORT_2_DATASOURCE", reportParameter.getDataList2());
        reportParameter.getParameters().put("SUBREPORT_MERGE_DATASOURCE", reportParameter.getSogoKadaiDataAndShienList());
    }

    /**
     * 帳票パラメータ取得
     *
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(PreventionPlanA42ReportServiceParameterModel inDto,
            PreventionPlanA42ReportServiceOutDto outDto) throws Exception {
        LOG.info(Constants.START);

        PreventionPlanA42ReportServiceInDto model = getPreventionPlanA42ReportParameters(inDto);
        // ノート情報格納配列
        List<PreventionPlanA42ReportServiceInDto> reportInfoList = new ArrayList<PreventionPlanA42ReportServiceInDto>();

        reportInfoList.add(model);

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(reportInfoList);
        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(model.getListDataList1());
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(model.getListDataList2());
        JRBeanCollectionDataSource dataSource3 = new JRBeanCollectionDataSource(model.getSogoKadaiDataAndShienList1());
        model.setDataSource(dataSource);
        model.setDataList1(dataSource1);
        model.setDataList2(dataSource2);
        model.setSogoKadaiDataAndShienList(dataSource3);
        LOG.info(Constants.END);
        return model;
    }

    /**
     * 介護予防サービス-支援計画表（A4横2枚）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータ
     */
    private PreventionPlanA42ReportServiceInDto getPreventionPlanA42ReportParameters(
            PreventionPlanA42ReportServiceParameterModel inDto) {

        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));
        // 主治医意見書①帳票情報
        PreventionPlanA42ReportServiceInDto model = new PreventionPlanA42ReportServiceInDto();

        // リクエストパラメータ.データ.DB未保存画面項目
        PrintReportServiceDbNoSaveData dbNoSaveData = inDto.getDbNoSaveData();

        // リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ = "1" の場合
        if (CommonConstants.STR_1.equals(dbNoSaveData != null ? dbNoSaveData.getEmptyFlg() : null)) {
            // 帳票用データを設定する
            getDefaultInfo(model);
            // リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ = "0" の場合
        } else {
            // ケアプラン帳票印鑑欄情報取得
            getChouhyouInkanInfo(inDto, model);
            // 目標とする生活情報の取得
            getKycTucPlanInfo(inDto, model);

            // リスト1、リスト2の初期化
            initListData(model);

            // アセスメント領域と課題リストの取得
            getAssryoDataList(inDto, model);
            // 総合的課題と目標リストの取得
            getSogoKadaiDataList(inDto, model);
            // 支援計画リストの取得
            getShienDataList(inDto, model);
            // 個人保護用情報の取得
            getKihonHoukyoInfo(inDto, model);
        }
        // リクエストパラメータ.データ.印刷設定.パラメータ18が1の場合、リクエストパラメータ.データ.データ.印刷設定.パラメータ19を設定
        if (ReportConstants.STR_1.equals(inDto.getPrintSet().getParam18())) {
            // 帳票タイトル1
            model.setTitle1(inDto.getPrintSet().getParam19() + ReportConstants.PAGE_1_OF_2);
            // 帳票タイトル2
            model.setTitle2(inDto.getPrintSet().getParam19() + ReportConstants.PAGE_2_OF_2);
        } else {
            // 帳票タイトル1
            model.setTitle1(inDto.getPrintSet().getPrtTitle() + ReportConstants.PAGE_1_OF_2);
            // 帳票タイトル2
            model.setTitle2(inDto.getPrintSet().getPrtTitle() + ReportConstants.PAGE_2_OF_2);
        }

        // 指定日印刷区分 = リクエストパラメータ.データ.印刷設定.日付表示有無
        Integer prnDate = CommonDtoUtil.strValToInt(inDto.getPrintSet().getPrnDate());

        // 指定日印刷区分
        // リクエストパラメータ.データ.印刷設定.日付表示有無を設定
        model.setShiTeiKubun(prnDate);

        // リクエストパラメータ.データ.印刷設定.日付表示有無＝2:指定日印刷の場合
        if (prnDate == CommonConstants.PRN_DATE_DESIGNATED_DAY_PRINTING) {
            // 和暦日付
            model.setSelectDate(get2Gengouj(inDto.getDbNoSaveData().getSelectDate()));
        }
        // リクエストパラメータ.データ.印刷設定.日付表示有無＝3:日付空欄印刷の場合
        else if (prnDate == CommonConstants.PRN_DATE_DAILY_PAYMENT_BLANK_PRINTING) {
            // 印刷用日付
            model.setSelectDate(nds3GkFunc01Logic.blankDate(inDto.getSystemDate()));
        }
        // リクエストパラメータ.データ.印刷設定.日付表示有無＝1:印刷しないの場合
        else {
            model.setSelectDate(CommonConstants.BLANK_STRING);
        }

        // 事業所名
        model.setJigyoKnj(inDto.getJigyoKnj());

        // 敬称
        // リクエストパラメータ.データ.印刷設定.パラメータ03が1の場合、リクエストパラメータ.データ.印刷設定.パラメータ04を設定
        if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam03())) {
            model.setKeishoKnj(inDto.getPrintSet().getParam04());
        }
        // 上記以外の場合
        else {
            // リクエストパラメータ.データ.初期設定マスタの情報.敬称オプションが1の場合、リクエストパラメータ.データ.初期設定マスタの情報.敬称を設定
            if (CommonConstants.STR_1.equals(inDto.getInitMasterObj().getKeishoFlg())) {
                model.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
            }
            // 上記以外の場合、"殿"を設定
            else {
                model.setKeishoKnj(CommonConstants.KEISHO_STR_TONO);
            }
        }

        // 記入用シートを印刷するフラグ = リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ
        model.setEmptyFlg(dbNoSaveData != null ? CommonDtoUtil.strValToInt(dbNoSaveData.getEmptyFlg())
                : CommonConstants.NUMBER_ZERO);

        // 印刷フォント
        // リクエストパラメータ.データ.初期設定マスタの情報.印刷フォントを設定
        String printFontValue = inDto.getInitMasterObj().getPrintFont();
        // 0:9pt 1:10pt 2:11pt 3:12pt 4:8pt
        switch (printFontValue) {
            case "0":
                fontSize = "9";
                break;
            case "1":
                fontSize = "10";
                break;
            case "2":
                fontSize = "11";
                break;
            case "3":
                fontSize = "12";
                break;
            case "4":
                fontSize = "8";
                break;
            default:
                fontSize = "9";
                break;
        }
        // フォントサイズ
        model.setPrintFont(fontSize);

        // 要支援3名
        // リクエストパラメータ.データ.印刷設定.パラメータ17が1の場合、"事業対象者"を設定
        if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam17())) {
            model.setYoshien3Name(CommonConstants.YOKAIKNJ_1);
        }
        // 上記以外の場合、"地域支援対象"を設定
        else {
            model.setYoshien3Name(CommonConstants.YOSHINEN_3_NAME_2);
        }

        // 健康の状態についてラベル名
        // リクエストパラメータ.データ.印刷設定.パラメータ17が1の場合、"健康状態について：~r~n□主治医意見書、健診結果、観察結果等を踏まえた留意点"を設定
        if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam17())) {
            model.setKenkoRyuitenKnjLabel(ReportConstants.KENKO_RYUITEN_KNJ_LABEL_1);
        }
        // 上記以外の場合、"健康状態について：~r~n□主治医意見書、生活機能評価等を踏まえた留意点"を設定
        else {
            model.setKenkoRyuitenKnjLabel(ReportConstants.KENKO_RYUITEN_KNJ_LABEL_2);
        }

        // 本人等のセリフケアや家族の支援、インフォーマルサービスラベル名
        // リクエストパラメータ.データ.印刷設定.パラメータ17が1の場合、"本人等のｾﾙﾌｹｱや家[改行]族の支援、ｲﾝﾌｫｰﾏﾙ[改行]ｻｰﾋﾞｽ（民間ｻｰﾋﾞｽ）"を設定
        if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam17())) {
            model.setInformalKnjLabel(ReportConstants.INFORMAL_KNJ_LABEL_1);
        }
        // 上記以外の場合、"本人等のｾﾙﾌｹｱ[改行]や家族の支援、[改行]ｲﾝﾌｫｰﾏﾙｻｰﾋﾞｽ"を設定
        else {
            model.setInformalKnjLabel(ReportConstants.INFORMAL_KNJ_LABEL_2);
        }

        // サービス1ラベル
        // リクエストパラメータ.データ.印刷設定.パラメータ17が1の場合、"介護保険ｻｰﾋﾞｽ"を設定
        if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam17())) {
            model.setService1Label(ReportConstants.SERVICE_1_LABEL_1);
        }
        // 上記以外の場合、"介護保険"を設定
        else {
            model.setService1Label(ReportConstants.SERVICE_1_LABEL_2);
        }

        // サービス2ラベル
        // リクエストパラメータ.データ.印刷設定.パラメータ17が1の場合、"又は地域支援事業"を設定
        if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam17())) {
            model.setService2Label(ReportConstants.SERVICE_2_LABEL_1);
        }
        // 上記以外の場合、"サービス"を設定
        else {
            model.setService2Label(ReportConstants.SERVICE_2_LABEL_2);
        }

        // サービス3ラベル
        // リクエストパラメータ.データ.印刷設定.パラメータ17が1の場合、"（総合事業のｻｰﾋﾞｽ）"を設定
        if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam17())) {
            model.setService3Label(ReportConstants.SERVICE_3_LABEL_1);
        }
        // 上記以外の場合、"または地域支援事業"を設定
        else {
            model.setService3Label(ReportConstants.SERVICE_3_LABEL_2);
        }

        // 支援事業ラベル
        // リクエストパラメータ.データ.印刷設定.パラメータ17が1の場合、"支援事業[改行]（利用先）"を設定
        if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam17())) {
            model.setShienJigyoLabel(ReportConstants.SHIEN_JIGYO_LABEL_1);
        }
        // 上記以外の場合、"支援事業"を設定
        else {
            model.setShienJigyoLabel(ReportConstants.SHIEN_JIGYO_LABEL_2);
        }

        // 同意情報を印刷するフラグ
        // リクエストパラメータ.データ.印刷設定.パラメータ12を設定
        model.setDoiFlg(CommonDtoUtil.strValToInt(inDto.getPrintSet().getParam12()));

        // 必要な事業プログラムに○印をつけるフラグ
        // リクエストパラメータ.データ.印刷設定.パラメータ13を設定
        model.setPrgFlg(CommonDtoUtil.strValToInt(inDto.getPrintSet().getParam13()));

        // 支援計画の開始位置を調整するフラグ
        // リクエストパラメータ.データ.印刷設定.パラメータ14を設定
        model.setStFlg(CommonDtoUtil.strValToInt(inDto.getPrintSet().getParam14()));

        // 総合的課題〜目標の下線を印刷しないフラグ
        // リクエストパラメータ.データ.印刷設定.パラメータ15を設定
        model.setUlFlg(CommonDtoUtil.strValToInt(inDto.getPrintSet().getParam15()));

        // 印刷確認、押印欄を印刷しないフラグ
        // リクエストパラメータ.データ.印刷設定.パラメータ20を設定
        model.setR3Flg(CommonDtoUtil.strValToInt(inDto.getPrintSet().getParam20()));

        // H27年様式で印刷するフラグ
        // リクエストパラメータ.データ.印刷設定.パラメータ17を設定
        model.setH21Flg(CommonDtoUtil.strValToInt(inDto.getPrintSet().getParam17()));

        // 同意者内容1
        // リクエストパラメータ.データ.印刷設定.パラメータ09が0、且つ、リクエストパラメータ.データ.印刷設定.パラメータ10が0の場合、"上記計画について、同意いたします。"を設定
        if (CommonConstants.STR_0.equals(inDto.getPrintSet().getParam09())
                && CommonConstants.STR_0.equals(inDto.getPrintSet().getParam10())) {
            model.setDoiContent1(ReportConstants.DOI_CONTENT_1);
        }
        // 上記以外の場合、リクエストパラメータ.データ.印刷設定.パラメータ09を設定
        else {
            model.setDoiContent1(inDto.getPrintSet().getParam09());
        }

        // 同意者内容2
        // リクエストパラメータ.データ.印刷設定.パラメータ10を設定
        model.setDoiContent2(inDto.getPrintSet().getParam10());
        return model;
    }

    /**
     * リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ = "1" の場合、帳票用データを設定する
     *
     * @param model 帳票用データ詳細
     */
    private void getDefaultInfo(PreventionPlanA42ReportServiceInDto model) {
        // 利用者ID
        model.setUserId(CommonConstants.BLANK_STRING);
        // 利用者名
        model.setUserName(CommonConstants.BLANK_STRING);
        // 印鑑欄表示フラグ
        model.setInkanShowFlg(CommonConstants.NUMBER_ZERO);
        // 印鑑1～印鑑15に""を設定する
        getDefaultHanko1Knj(model);
        // 「目標とする生活」情報
        getDefaultKycTucPlanInfo(model);
        // リスト1、リスト2の初期化
        initListData(model);
    }

    /**
     * リスト1、リスト2の初期化
     *
     * @param model 帳票用データ詳細
     */
    private void initListData(PreventionPlanA42ReportServiceInDto model) {
        // リスト1
        List<PreventionPlanA42ReportServiceList1Data> listDataList1 = new ArrayList<>();
        // リスト1のデータ
        PreventionPlanA42ReportServiceList1Data listData1 = new PreventionPlanA42ReportServiceList1Data();
        // アセスメント領域と課題リスト
        List<PreventionPlanA31ReportServiceAssryoData> assryoDataList1 = new ArrayList<>();
        // アセスメント領域と課題リストのデータ
        listData1.setAssryoDataList1(assryoDataList1);
        // アセスメント領域と課題リストのデータソース
        listData1.setAssryoDataList(new JRBeanCollectionDataSource(assryoDataList1));
        // 総合的課題と目標リスト
        List<PreventionPlanA31ReportServiceSogoKadaiData> sogoKadaiDataList1 = new ArrayList<>();
        // 総合的課題と目標リストのデータ
        listData1.setSogoKadaiDataList1(sogoKadaiDataList1);
        // 総合的課題と目標リストのデータソース
        listData1.setSogoKadaiDataList(new JRBeanCollectionDataSource(sogoKadaiDataList1));
        // リスト1のデータをリスト1に追加
        listDataList1.add(listData1);
        // リスト1を設定
        model.setListDataList1(listDataList1);
        // リスト1のデータソースを設定
        model.setDataList1(new JRBeanCollectionDataSource(model.getListDataList1()));
        // リスト2
        List<PreventionPlanA42ReportServiceList2Data> listDataList2 = new ArrayList<>();
        // リスト2のデータ
        PreventionPlanA42ReportServiceList2Data listData2 = new PreventionPlanA42ReportServiceList2Data();
        // 総合的課題と目標リスト
        List<PreventionPlanA31ReportServiceSogoKadaiData> sogoKadaiDataList2 = new ArrayList<>();
        // 総合的課題と目標リストのデータ
        listData2.setSogoKadaiDataList1(sogoKadaiDataList2);
        // 総合的課題と目標リストのデータソース
        listData2.setSogoKadaiDataList(new JRBeanCollectionDataSource(sogoKadaiDataList2));
        // 支援計画リスト
        List<PreventionPlanA31ReportServiceShienData> shienDataList2 = new ArrayList<>();
        // 支援計画リストのデータ
        listData2.setShienDataList1(shienDataList2);
        // 支援計画リストのデータソース
        listData2.setShienDataList(new JRBeanCollectionDataSource(shienDataList2));
        // リスト2のデータをリスト2に追加
        listDataList2.add(listData2);
        // リスト2を設定
        model.setListDataList2(listDataList2);
        // リスト2のデータソースを設定
        model.setDataList2(new JRBeanCollectionDataSource(model.getListDataList2()));
        // 総合的課題と目標リストと支援計画リスト
        List<PreventionPlanA31ReportServiceSogoKadaiDataAndShien> sogoKadaiDataAndShienList1 = new ArrayList<>();
        // 総合的課題と目標リストと支援計画リストを設定
        model.setSogoKadaiDataAndShienList1(sogoKadaiDataAndShienList1);
        // 総合的課題と目標リストと支援計画リストのデータソース
        model.setSogoKadaiDataAndShienList(new JRBeanCollectionDataSource(sogoKadaiDataAndShienList1));
    }

    /**
     * 印鑑欄表示区分に0:非表示を設定、
     * 印鑑1～印鑑15に""を設定する
     *
     * @param model 帳票用データ詳細
     */
    private void getDefaultHanko1Knj(PreventionPlanA42ReportServiceInDto model) {
        // 印鑑1
        model.setHanko1Knj(CommonConstants.BLANK_STRING);
        // 印鑑2
        model.setHanko2Knj(CommonConstants.BLANK_STRING);
        // 印鑑3
        model.setHanko3Knj(CommonConstants.BLANK_STRING);
        // 印鑑4
        model.setHanko4Knj(CommonConstants.BLANK_STRING);
        // 印鑑5
        model.setHanko5Knj(CommonConstants.BLANK_STRING);
        // 印鑑6
        model.setHanko6Knj(CommonConstants.BLANK_STRING);
        // 印鑑7
        model.setHanko7Knj(CommonConstants.BLANK_STRING);
        // 印鑑8
        model.setHanko8Knj(CommonConstants.BLANK_STRING);
        // 印鑑9
        model.setHanko9Knj(CommonConstants.BLANK_STRING);
        // 印鑑10
        model.setHanko10Knj(CommonConstants.BLANK_STRING);
        // 印鑑11
        model.setHanko11Knj(CommonConstants.BLANK_STRING);
        // 印鑑12
        model.setHanko12Knj(CommonConstants.BLANK_STRING);
        // 印鑑13
        model.setHanko13Knj(CommonConstants.BLANK_STRING);
        // 印鑑14
        model.setHanko14Knj(CommonConstants.BLANK_STRING);
        // 印鑑15
        model.setHanko15Knj(CommonConstants.BLANK_STRING);
    }

    /**
     * 「目標とする生活」情報
     *
     * @param model 帳票用データ詳細
     */
    private void getDefaultKycTucPlanInfo(PreventionPlanA42ReportServiceInDto model) {
        // 作成日
        model.setCreateYmd(CommonConstants.BLANK_STRING);
        // 初回作成日
        model.setShokaiYmd(CommonConstants.BLANK_STRING);
        // 支援センターの作成者
        model.setCenterShokuKnj(CommonConstants.BLANK_STRING);
        // 委託先の作成者
        model.setItakuShokuKnj(CommonConstants.BLANK_STRING);
        // 認定日
        model.setNinteiYmd(CommonConstants.BLANK_STRING);
        // 有効開始日
        model.setYukoSYmd(CommonConstants.BLANK_STRING);
        // 有効終了日
        model.setYukoEYmd(CommonConstants.BLANK_STRING);
        // 計画区分
        model.setKubun(CommonConstants.NUMBER_9);
        // 認定区分
        model.setNintei(CommonConstants.NUMBER_9);
        // 要支援区分
        model.setYoshienKbn(CommonConstants.NUMBER_9);
        // 目標とする生活（1日）
        model.setMokuhyoDayKnj(CommonConstants.BLANK_STRING);
        // 目標とする生活（1年）
        model.setMokuhyoYearKnj(CommonConstants.BLANK_STRING);
        // 健康状態について
        model.setKenkoRyuitenKnj(CommonConstants.BLANK_STRING);
        // 妥当な支援の実施に向けた方針
        model.setDatoHoshinKnj(CommonConstants.BLANK_STRING);
        // 総合的な方針
        model.setSogoHoshinKnj(CommonConstants.BLANK_STRING);
        // 必要な事業プログラム（運動不足）
        model.setProgram1Flg(CommonConstants.NUMBER_9);
        // 必要な事業プログラム（栄養改善）
        model.setProgram2Flg(CommonConstants.NUMBER_9);
        // 必要な事業プログラム（口腔内ケア）
        model.setProgram3Flg(CommonConstants.NUMBER_9);
        // 必要な事業プログラム（閉じこもり予防）
        model.setProgram4Flg(CommonConstants.NUMBER_9);
        // 必要な事業プログラム（物忘れ予防）
        model.setProgram5Flg(CommonConstants.NUMBER_9);
        // 必要な事業プログラム（うつ予防）
        model.setProgram6Flg(CommonConstants.NUMBER_9);
        // 地域包括支援センターの意見
        model.setCenterIkenKnj(CommonConstants.BLANK_STRING);
        // 地域包括支援センター確認印
        model.setCenterKakuninFlg(CommonConstants.NUMBER_9);
        // 同意日
        model.setDoiYmd(CommonConstants.BLANK_STRING);
        // 同意者
        model.setDoiKnj(CommonConstants.BLANK_STRING);
        // 担当地域包括支援センター
        model.setChiJigyoKnj(CommonConstants.BLANK_STRING);
        // 委託の場合の作成事業者所在
        model.setItkJigyoKnj(CommonConstants.BLANK_STRING);
        // 必要な事業プログラム（運動不足）チェック数
        model.setProgram1Cnt(CommonConstants.BLANK_STRING);
        // 必要な事業プログラム（栄養改善）チェック数
        model.setProgram2Cnt(CommonConstants.BLANK_STRING);
        // 必要な事業プログラム（口腔内ケア）チェック数
        model.setProgram3Cnt(CommonConstants.BLANK_STRING);
        // 必要な事業プログラム（閉じこもり予防）チェック数
        model.setProgram4Cnt(CommonConstants.BLANK_STRING);
        // 必要な事業プログラム（物忘れ予防）チェック数
        model.setProgram5Cnt(CommonConstants.BLANK_STRING);
        // 必要な事業プログラム（うつ予防）チェック数
        model.setProgram6Cnt(CommonConstants.BLANK_STRING);
        // 利用サービス（介護保険）
        model.setService1Flg(CommonConstants.NUMBER_9);
        // 利用サービス（地域支援事業）
        model.setService2Flg(CommonConstants.NUMBER_9);
    }

    /**
     * ケアプラン帳票印鑑欄情報取得
     *
     * @param inDto 入力データ
     * @param model 帳票用データ詳細
     */
    private void getChouhyouInkanInfo(
            PreventionPlanA42ReportServiceParameterModel inDto,
            PreventionPlanA42ReportServiceInDto model) {
        // 法人ID = リクエストパラメータ.事業者情報.法人ID
        String houjinId = inDto.getJigyoInfo().getHoujinId();
        // 施設ID = リクエストパラメータ.事業者情報.施設ID
        String shisetuId = inDto.getJigyoInfo().getShisetuId();
        // 事業所ID = リクエストパラメータ.事業者情報.事業所ID
        String jigyoId = inDto.getJigyoInfo().getSvJigyoId();
        // 帳票セクション番号 = リクエストパラメータ.事業者情報.プロファイル
        String profile = inDto.getPrintSet().getProfile();
        // 検索条件がなし、ケアプラン帳票印鑑欄情報取得終了
        if (houjinId.isEmpty() || shisetuId.isEmpty() || jigyoId.isEmpty() || profile.isEmpty()) {
            // 介護予防サービス-支援計画表情報.印鑑欄表示フラグ = 0:印鑑欄非表示
            model.setInkanShowFlg(CommonConstants.NUMBER_ZERO);
            // 印鑑1～印鑑15に""を設定する
            getDefaultHanko1Knj(model);
            return;
        }
        // リクエストパラメータ.記入用シートを印刷するフラグが0:チェックOFFの場合、下記の帳票データ情報を取得する
        // ケアプラン帳票印鑑欄情報取得
        KghCpnMstChouhyouInkanPrnByCriteriaInEntity entity = new KghCpnMstChouhyouInkanPrnByCriteriaInEntity();
        // 法人ID
        entity.setAnKey1(CommonDtoUtil.strValToInt(houjinId));
        // 施設ID
        entity.setAnKey2(CommonDtoUtil.strValToInt(shisetuId));
        // 事業所ID
        entity.setAnKey3(CommonDtoUtil.strValToInt(jigyoId));
        // 帳票セクション番号
        entity.setAsSec(profile);
        List<KghCpnMstChouhyouInkanPrnOutEntity> list = cpnMstChouhyouInkanSelectMapper
                .findKghCpnMstChouhyouInkanPrnByCriteria(entity);
        if (list == null || list.size() == 0) {
            getDefaultInfo(model);
        } else {
            // 印鑑欄情報の取得
            KghCpnMstChouhyouInkanPrnOutEntity chouhyouInkaData = list.get(0);
            // 印鑑欄表示区分
            Integer hyoujiKbn = Integer.valueOf(chouhyouInkaData.getHyoujiKbn());

            // 印鑑欄表示フラグ
            model.setInkanShowFlg(CommonConstants.NUMBER_ZERO);
            // 検索結果がある、且つ、検索結果.印鑑欄表示区分が"1"の場合：
            if (hyoujiKbn.equals(CommonConstants.NUMBER_ONE)) {
                // 介護予防サービス-支援計画表情報.印鑑欄表示フラグ = 1:印鑑欄表示
                model.setInkanShowFlg(CommonConstants.NUMBER_ONE);
                // 印鑑1
                model.setHanko1Knj(chouhyouInkaData.getHanko1Knj());
                // 印鑑2
                model.setHanko2Knj(chouhyouInkaData.getHanko2Knj());
                // 印鑑3
                model.setHanko3Knj(chouhyouInkaData.getHanko3Knj());
                // 印鑑4
                model.setHanko4Knj(chouhyouInkaData.getHanko4Knj());
                // 印鑑5
                model.setHanko5Knj(chouhyouInkaData.getHanko5Knj());
                // 印鑑6
                model.setHanko6Knj(chouhyouInkaData.getHanko6Knj());
                // 印鑑7
                model.setHanko7Knj(chouhyouInkaData.getHanko7Knj());
                // 印鑑8
                model.setHanko8Knj(chouhyouInkaData.getHanko8Knj());
                // 印鑑9
                model.setHanko9Knj(chouhyouInkaData.getHanko9Knj());
                // 印鑑10
                model.setHanko10Knj(chouhyouInkaData.getHanko10Knj());
                // 印鑑11
                model.setHanko11Knj(chouhyouInkaData.getHanko11Knj());
                // 印鑑12
                model.setHanko12Knj(chouhyouInkaData.getHanko12Knj());
                // 印鑑13
                model.setHanko13Knj(chouhyouInkaData.getHanko13Knj());
                // 印鑑14
                model.setHanko14Knj(chouhyouInkaData.getHanko14Knj());
                // 印鑑15
                model.setHanko15Knj(chouhyouInkaData.getHanko15Knj());
            }
            // 上記以外の場合
            else if (hyoujiKbn.equals(CommonConstants.NUMBER_ZERO)) {
                // 介護予防サービス-支援計画表情報.印鑑欄表示フラグ = 0:印鑑欄非表示
                model.setInkanShowFlg(CommonConstants.NUMBER_ZERO);
                // 印鑑1～印鑑15に""を設定する
                getDefaultHanko1Knj(model);
            }
        }
    }

    /**
     * 「目標とする生活」情報を取得
     *
     * @param inDto 入力データ
     * @param model 帳票用データ詳細
     */
    private void getKycTucPlanInfo(
            PreventionPlanA42ReportServiceParameterModel inDto,
            PreventionPlanA42ReportServiceInDto model) {

        // 計画表ＩＤ = リクエストパラメータ.印刷対象履歴.履歴ID
        String rirekiId = inDto.getPrintSubjectHistory().getRirekiId();

        // 検索条件がなし、ケアプラン帳票印鑑欄情報取得終了
        if (rirekiId.isEmpty()) {
            getDefaultKycTucPlanInfo(model);
            return;
        }
        // 「目標とする生活」情報取得
        KycKkak00ByCriteriaInEntity entity = new KycKkak00ByCriteriaInEntity();
        // 計画表ＩＤ
        entity.setP11id(CommonDtoUtil.strValToInt(rirekiId));
        List<KycKkak00OutEntity> list = kycTucPlan11SelectMapper
                .findKycKkak00ByCriteria(entity);
        if (list == null || list.size() == 0) {
            getDefaultKycTucPlanInfo(model);
        } else {
            // 「目標とする生活」情報の取得
            KycKkak00OutEntity kycKkakData = list.get(0);
            // 作成日
            model.setCreateYmd(get2Gengouj(kycKkakData.getCreateYmd()));
            // 初回作成日
            model.setShokaiYmd(get2Gengouj(kycKkakData.getShokaiYmd()));
            // 支援センターの作成者
            model.setCenterShokuKnj(kycKkakData.getCenterShokuKnj());
            // 委託先の作成者
            model.setItakuShokuKnj(kycKkakData.getItakuShokuKnj());
            // 認定日
            model.setNinteiYmd(get2Gengouj(kycKkakData.getNinteiYmd()));
            // 有効開始日
            model.setYukoSYmd(get2Gengouj(kycKkakData.getYukoSYmd()));
            // 有効終了日
            model.setYukoEYmd(get2Gengouj(kycKkakData.getYukoEYmd()));
            // 計画区分
            model.setKubun(Integer.valueOf(kycKkakData.getKubun()));
            // 認定区分
            model.setNintei(Integer.valueOf(kycKkakData.getNintei()));
            // 要支援区分
            model.setYoshienKbn(Integer.valueOf(kycKkakData.getYoshienKbn()));
            // 目標とする生活（1日）
            model.setMokuhyoDayKnj(kycKkakData.getMokuhyoDayKnj());
            // 目標とする生活（1年）
            model.setMokuhyoYearKnj(kycKkakData.getMokuhyoYearKnj());
            // 健康状態について
            model.setKenkoRyuitenKnj(kycKkakData.getKenkoRyuitenKnj());
            // 妥当な支援の実施に向けた方針
            model.setDatoHoshinKnj(kycKkakData.getDatoHoshinKnj());
            // 総合的な方針
            model.setSogoHoshinKnj(kycKkakData.getSogoHoshinKnj());
            // 必要な事業プログラム（運動不足）
            model.setProgram1Flg(Integer.valueOf(kycKkakData.getProgram1Flg()));
            // 必要な事業プログラム（栄養改善）
            model.setProgram2Flg(Integer.valueOf(kycKkakData.getProgram2Flg()));
            // 必要な事業プログラム（口腔内ケア）
            model.setProgram3Flg(Integer.valueOf(kycKkakData.getProgram3Flg()));
            // 必要な事業プログラム（排尿管理）
            model.setProgram4Flg(Integer.valueOf(kycKkakData.getProgram4Flg()));
            // 必要な事業プログラム（物忘れ予防）
            model.setProgram5Flg(Integer.valueOf(kycKkakData.getProgram5Flg()));
            // 必要な事業プログラム（うつ予防）
            model.setProgram6Flg(Integer.valueOf(kycKkakData.getProgram6Flg()));
            // 地域包括支援センターの意見
            model.setCenterIkenKnj(kycKkakData.getCenterIkenKnj());
            // 地域包括支援センター確認印
            model.setCenterKakuninFlg(Integer.valueOf(kycKkakData.getCenterKakuninFlg()));
            // 同意日
            model.setDoiYmd(get2Gengouj(kycKkakData.getDoiYmd()));
            // 同意者
            model.setDoiKnj(kycKkakData.getDoiKnj());
            // 担当地域包括支援センター
            model.setChiJigyoKnj(kycKkakData.getChiJigyoKnj());
            // 委託の場合の作成事業者所在
            model.setItkJigyoKnj(kycKkakData.getItkJigyoKnj());
            // 必要な事業プログラム（運動不足）チェック数
            model.setProgram1Cnt(CommonDtoUtil.objValToString(kycKkakData.getProgram1Cnt()) + ReportConstants.SLASH_5);
            // 必要な事業プログラム（栄養改善）チェック数
            model.setProgram2Cnt(CommonDtoUtil.objValToString(kycKkakData.getProgram2Cnt()) + ReportConstants.SLASH_2);
            // 必要な事業プログラム（口腔内ケア）チェック数
            model.setProgram3Cnt(CommonDtoUtil.objValToString(kycKkakData.getProgram3Cnt()) + ReportConstants.SLASH_3);
            // 必要な事業プログラム（排尿管理）チェック数
            model.setProgram4Cnt(CommonDtoUtil.objValToString(kycKkakData.getProgram4Cnt()) + ReportConstants.SLASH_2);
            // 必要な事業プログラム（物忘れ予防）チェック数
            model.setProgram5Cnt(CommonDtoUtil.objValToString(kycKkakData.getProgram5Cnt()) + ReportConstants.SLASH_3);
            // 必要な事業プログラム（うつ予防）チェック数
            model.setProgram6Cnt(CommonDtoUtil.objValToString(kycKkakData.getProgram6Cnt()) + ReportConstants.SLASH_5);
            // 利用サービス（介護保険）
            model.setService1Flg(Integer.valueOf(kycKkakData.getService1Flg()));
            // 利用サービス（地域支援事業）
            model.setService2Flg(Integer.valueOf(kycKkakData.getService2Flg()));
        }
    }

    /**
     * アセスメント領域と課題リストの取得
     *
     * @param inDto 入力データ
     * @param model 帳票用データ詳細
     */
    private void getAssryoDataList(
            PreventionPlanA42ReportServiceParameterModel inDto,
            PreventionPlanA42ReportServiceInDto model) {
        // 計画表ＩＤ = リクエストパラメータ.印刷対象履歴.履歴ID
        String rirekiId = inDto.getPrintSubjectHistory().getRirekiId();
        // 検索条件がなし、ケアプラン帳票印鑑欄情報取得終了
        if (rirekiId.isEmpty()) {
            return;
        }

        // アセスメント領域と課題リスト取得
        KycKkakTko1LByCriteriaInEntity entity = new KycKkakTko1LByCriteriaInEntity();
        // 計画表ＩＤ
        entity.setPln11(CommonDtoUtil.strValToInt(rirekiId));
        List<KycKkakTko1LOutEntity> list = kycTucPlan12SelectMapper
                .findKycKkakTko1LByCriteria(entity);
        // アセスメント領域と課題リスト
        if (list.size() > 0) {
            List<PreventionPlanA31ReportServiceAssryoData> assryoDataList = new ArrayList<>();
            // ※ソート順：アセスメント領域ID 昇順
            List<KycKkakTko1LOutEntity> returnList = list.stream()
                    .sorted(Comparator.comparing(KycKkakTko1LOutEntity::getAssryoId))
                    .limit(4)
                    .collect(Collectors.toList());

            for (int i = 0; i < returnList.size(); i++) {
                PreventionPlanA31ReportServiceAssryoData assryoData = new PreventionPlanA31ReportServiceAssryoData();
                // アセスメント領域ID ＝ ※1を参照。※1は下記の通り。
                // ※1
                // 一件目 = "運動・移動について"
                // 二件目 = "日常生活（家庭生活）について"
                // 三件目 = "社会参加、対人関係・「改行」コミュニケーションについて"
                // 四件目 = "健康管理について"
                assryoData.setAssryoId(ReportConstants.ASSRYO_ID_LIST.get(i));
                // アセスメント領域と現在の状況 ＝・処理3.3で取得した1~4件のレコード.アセスメント領域と現在の状況を設定
                assryoData.setJokyoKnj(returnList.get(i).getJokyoKnj());
                // 本人・家族の意欲・意向 ＝ ・処理3.3で取得した1~4件のレコード.本人・家族の意欲・意向を設定
                assryoData.setIyokuikouKnj(returnList.get(i).getIyokuikouKnj());
                // 領域における課題（背景・原因） ＝ ※2を参照
                // ※2
                // 領域における課題（背景・原因） = ・処理3.3で取得した1~4件のレコード.領域における課題（背景・原因）フラグが1の場合、"■有 □無"
                if (returnList.get(i).getKadaiFlg() == 1) {
                    assryoData.setKadaiKnj(ReportConstants.KADAI_KN_FLAG_2);
                }
                // ・処理3.3で取得した1~4件のレコード.領域における課題（背景・原因）フラグが2の場合、"□有 ■無"
                else if (returnList.get(i).getKadaiFlg() == 2) {
                    assryoData.setKadaiKnj(ReportConstants.KADAI_KN_FLAG_3);
                }
                // ・上記以外の場合、"□有 □無"
                else {
                    assryoData.setKadaiKnj(ReportConstants.KADAI_KN_FLAG_1);
                }
                assryoDataList.add(assryoData);
            }

            // リスト1
            model.getListDataList1().get(0).setAssryoDataList1(assryoDataList);
            model.getListDataList1().get(0).setAssryoDataList(new JRBeanCollectionDataSource(assryoDataList));
        }
    }

    /**
     * 総合的課題と目標リストの取得
     *
     * @param inDto 入力データ
     * @param model 帳票用データ詳細
     */
    private void getSogoKadaiDataList(
            PreventionPlanA42ReportServiceParameterModel inDto,
            PreventionPlanA42ReportServiceInDto model) {
        // 計画表ＩＤ = リクエストパラメータ.印刷対象履歴.履歴ID
        String rirekiId = inDto.getPrintSubjectHistory().getRirekiId();

        // 検索条件がなし、ケアプラン帳票印鑑欄情報取得終了
        if (rirekiId.isEmpty()) {
            return;
        }

        // 総合的課題と目標リストの取得
        KycKkakTko2KadaiSortByCriteriaInEntity entity = new KycKkakTko2KadaiSortByCriteriaInEntity();
        // 計画表ＩＤ
        entity.setPln11(CommonDtoUtil.strValToInt(rirekiId));
        List<KycKkakTko2KadaiSortOutEntity> list = kycTucPlan13SelectMapper
                .findKycKkakTko2KadaiSortByCriteria(entity);
        // 総合的課題と目標リスト
        if (list.size() > 0) {
            List<PreventionPlanA31ReportServiceSogoKadaiData> sogoKadaiDataList = new ArrayList<>();
            // ※ソート順：課題番号 昇順
            List<KycKkakTko2KadaiSortOutEntity> returnList = list.stream()
                    .sorted(Comparator.comparing(KycKkakTko2KadaiSortOutEntity::getKadaiNo))
                    .collect(Collectors.toList());

            for (int i = 0; i < returnList.size(); i++) {
                PreventionPlanA31ReportServiceSogoKadaiData sogoKadaiData = new PreventionPlanA31ReportServiceSogoKadaiData();
                // リクエストパラメータ.データ.印刷設定.パラメータ15が0の場合(総合的課題〜目標の下線を印刷する)
                if (CommonConstants.STR_0.equals(inDto.getPrintSet().getParam15())) {
                    // 総合的課題番号
                    sogoKadaiData.setSogoKadaiNo(CommonDtoUtil.objValToString(returnList.get(i).getSogoKadaiNo()));
                    // 総合的課題
                    sogoKadaiData.setSogoKadaiKnj(returnList.get(i).getSogoKadaiKnj());
                    // 課題に対する目標と具体策の提案
                    sogoKadaiData.setSogoTeianKnj(returnList.get(i).getSogoTeianKnj());
                    // 具体策についての意向 本人・家族
                    sogoKadaiData.setSogoIkouKnj(returnList.get(i).getSogoIkouKnj());
                    // 目標
                    sogoKadaiData.setSogoMokuhyoKnj(returnList.get(i).getSogoMokuhyoKnj());
                    // 課題番号
                    sogoKadaiData.setKadaiNo(CommonDtoUtil.objValToString(returnList.get(i).getKadaiNo()));
                }
                // 上記以外の場合(総合的課題〜目標の下線を印刷しない)
                else {
                    // 総合的課題番号 +[改行]+全角スペース
                    sogoKadaiData.setSogoKadaiNo(CommonDtoUtil.objValToString(returnList.get(i).getSogoKadaiNo())
                            + ReportConstants.LINE_BREAKS
                            + CommonConstants.FULL_WIDTH_SPACE);
                    // 総合的課題 +[改行]+全角スペース
                    sogoKadaiData.setSogoKadaiKnj(returnList.get(i).getSogoKadaiKnj()
                            + ReportConstants.LINE_BREAKS
                            + CommonConstants.FULL_WIDTH_SPACE);
                    // 課題に対する目標と具体策の提案 +[改行]+全角スペース
                    sogoKadaiData.setSogoTeianKnj(returnList.get(i).getSogoTeianKnj()
                            + ReportConstants.LINE_BREAKS
                            + CommonConstants.FULL_WIDTH_SPACE);
                    // 具体策についての意向 本人・家族 +[改行]+全角スペース
                    sogoKadaiData.setSogoIkouKnj(returnList.get(i).getSogoIkouKnj()
                            + ReportConstants.LINE_BREAKS
                            + CommonConstants.FULL_WIDTH_SPACE);
                    // 目標 +[改行]+全角スペース
                    sogoKadaiData.setSogoMokuhyoKnj(returnList.get(i).getSogoMokuhyoKnj()
                            + ReportConstants.LINE_BREAKS
                            + CommonConstants.FULL_WIDTH_SPACE);
                    // 課題番号 +[改行]+全角スペース
                    sogoKadaiData.setKadaiNo(CommonDtoUtil.objValToString(returnList.get(i).getKadaiNo())
                            + ReportConstants.LINE_BREAKS
                            + CommonConstants.FULL_WIDTH_SPACE);
                }
                sogoKadaiDataList.add(sogoKadaiData);
            }

            // リスト1
            model.getListDataList1().get(0).setSogoKadaiDataList1(sogoKadaiDataList);
            model.getListDataList1().get(0).setSogoKadaiDataList(new JRBeanCollectionDataSource(sogoKadaiDataList));

            // リクエストパラメータ.データ.印刷設定.パラメータ14が0の場合、設定
            if (CommonConstants.STR_0.equals(inDto.getPrintSet().getParam14())) {
                // リスト2
                model.getListDataList2().get(0).setSogoKadaiDataList1(sogoKadaiDataList);
                model.getListDataList2().get(0).setSogoKadaiDataList(new JRBeanCollectionDataSource(sogoKadaiDataList));
            }

        }
    }

    /**
     * 支援計画リストの取得
     *
     * @param inDto 入力データ
     * @param model 帳票用データ詳細
     */
    private void getShienDataList(
            PreventionPlanA42ReportServiceParameterModel inDto,
            PreventionPlanA42ReportServiceInDto model) {
        // 計画表ＩＤ = リクエストパラメータ.印刷対象履歴.履歴ID
        String rirekiId = inDto.getPrintSubjectHistory().getRirekiId();
        // 検索条件がなし、ケアプラン帳票印鑑欄情報取得終了
        if (rirekiId.isEmpty()) {
            return;
        }
        // 「支援計画」情報取得
        KycKkakTko3ByCriteriaInEntity entity = new KycKkakTko3ByCriteriaInEntity();
        // 計画表ＩＤ
        entity.setPln11(CommonDtoUtil.strValToInt(rirekiId));
        List<KycKkakTko3OutEntity> list = kycTucPlan14SelectMapper
                .findKycKkakTko3ByCriteria(entity);
        // 支援計画リスト
        if (list.size() > 0) {
            List<PreventionPlanA31ReportServiceShienData> shienDataList = new ArrayList<>();
            List<PreventionPlanA31ReportServiceSogoKadaiData> sogoKadaiDataList = model.getListDataList1().get(0)
                    .getSogoKadaiDataList1();
            // ※ソート順：総合的課題番号 昇順、表示順 昇順、ＩＤ 昇順
            shienDataList = (list.stream()
                    .sorted(Comparator.comparing(KycKkakTko3OutEntity::getSogoKadaiNo)
                            .thenComparing(KycKkakTko3OutEntity::getSeqNo)
                            .thenComparing(KycKkakTko3OutEntity::getPlan11Id))
                    .map(info -> {
                        PreventionPlanA31ReportServiceShienData shienData = new PreventionPlanA31ReportServiceShienData();
                        // 期間の設定
                        // 期間 = 上記処理3.5で取得した支援計画リスト.期間（本人）を設定する
                        String kikanKnj = info.getKikanKnj();
                        // 期間開始日（本人）
                        String kikanFromYmd = info.getKikanSYmd();
                        // 期間終了日（本人）
                        String kikanToYmd = info.getKikanEYmd();
                        // リクエストパラメータ.データ.初期設定マスタの情報.期間の管理フラグが0:日付で管理の場合
                        if (CommonConstants.PERIOD_NO_MANAGE_FLG.equals(inDto.getInitMasterObj().getKikanFlg())) {
                            // 期間のカレンダー取込フラグ
                            String kikanYmdFlg = inDto.getInitMasterObj().getKikanYmdFlg();
                            // ①、リクエストパラメータ.データ.初期設定マスタの情報.期間のカレンダー取込フラグが1:?月?日～?月?日の場合
                            if (CommonConstants.PERIOD_CALENDAR_FLG_1.equals(kikanYmdFlg)) {
                                // 処理3.5で取得した支援計画リスト.期間開始日（本人）、期間終了日（本人）は下記関数により、表示の形を転換する
                                // 共通関数補足の「5. あらゆる日付を日本語元号付き日付に変換する」を参照
                                // 期間開始日（本人）
                                kikanFromYmd = get2Warekij(kikanFromYmd);
                                // 期間終了日（本人）
                                kikanToYmd = get2Warekij(kikanToYmd);
                            }
                            // リクエストパラメータ.データ.初期設定マスタの情報.期間のカレンダー取込フラグが0:?/?～?/? の場合
                            else if (CommonConstants.PERIOD_CALENDAR_FLG_0.equals(kikanYmdFlg)) {
                                // 処理3.5で取得した支援計画リスト.期間開始日（本人）、期間終了日（本人）は下記により、表示の形を再設定する
                                // 共通関数補足の「7. 西暦日付文字列を和暦日付文字列に変換する」を参照
                                // 共通関数補足の「6. 和暦の先頭０の数字をスペースに変換する」を参照
                                // 期間開始日（本人）
                                kikanFromYmd = zeroToSpace(kikanFromYmd);
                                // 期間終了日（本人）
                                kikanToYmd = zeroToSpace(kikanToYmd);
                            }
                            // リクエストパラメータ.データ.初期設定マスタの情報.期間のカレンダー取込フラグが3:?月?日の場合
                            else if (CommonConstants.PERIOD_CALENDAR_FLG_3.equals(kikanYmdFlg)) {
                                // 処理3.5で取得した支援計画リスト.期間開始日（本人）は下記関数により、表示の形を転換する
                                // 共通関数補足の「5. あらゆる日付を日本語元号付き日付に変換する」を参照
                                // 期間開始日（本人）
                                kikanFromYmd = get2Warekij(kikanFromYmd);
                            }
                            // リクエストパラメータ.データ.初期設定マスタの情報.期間のカレンダー取込フラグが2:?/? の場合
                            else if (CommonConstants.PERIOD_CALENDAR_FLG_2.equals(kikanYmdFlg)) {
                                // 処理3.5で取得した支援計画リスト.期間開始日（本人）は下記関数により、表示の形を転換する
                                // 共通関数補足の「7. 西暦日付文字列を和暦日付文字列に変換する」を参照
                                // 共通関数補足の「6. 和暦の先頭０の数字をスペースに変換する」を参照
                                // 期間開始日（本人）
                                kikanFromYmd = zeroToSpace(kikanFromYmd);
                            }

                            // ②、期間の再設定
                            // リクエストパラメータ.データ.初期設定マスタの情報.期間のカレンダー取込フラグが0:?/?～?/? 、1:?月?日～?月?日の場合
                            if (CommonConstants.PERIOD_CALENDAR_FLG_0.equals(kikanYmdFlg)
                                    || CommonConstants.PERIOD_CALENDAR_FLG_1.equals(kikanYmdFlg)) {
                                // 期間 = ①で設定した期間開始日+"~r~n" + "～" + "~r~n"+ ①で設定した期間終了日+期間
                                // ※ 開始日、又は、終了日は空の場合、空文字で設定
                                kikanKnj = kikanFromYmd + CommonConstants.STR_LINE_BREAK
                                        + CommonConstants.RANGE_SEPARATOR
                                        + CommonConstants.STR_LINE_BREAK + kikanToYmd + kikanKnj;
                            }
                            // リクエストパラメータ.データ.初期設定マスタの情報.期間のカレンダー取込フラグが2:?/? 、3:?月?日の場合
                            else if (CommonConstants.PERIOD_CALENDAR_FLG_2.equals(kikanYmdFlg)
                                    || CommonConstants.PERIOD_CALENDAR_FLG_3.equals(kikanYmdFlg)) {
                                // 期間 = ①で設定した期間開始日+期間
                                // ※ 開始日、又は、終了日は空の場合、空文字で設定
                                kikanKnj = kikanFromYmd + kikanKnj;
                            }
                        }
                        // 支援計画リストの下記項目設定
                        // 目標についての支援のポイント ＝ 処理3.5で取得した支援計画リスト.総合的課題
                        shienData.setShienPointKnj(info.getShienPointKnj());
                        // 具体的な支援の内容（本人） ＝ 処理3.5で取得した支援計画リスト.具体的な支援の内容（本人）
                        shienData.setInfoServiceKnj(info.getInfoServiceKnj());
                        // 具体的な支援の内容（保険・地域） ＝ 処理3.5で取得した支援計画リスト.具体的な支援の内容（保険・地域）
                        shienData.setHokenServiceKnj(info.getHokenServiceKnj());
                        // サービス種別（本人） ＝ 処理3.5で取得した支援計画リスト.サービス種別（本人）
                        shienData.setSvShubetuKnj(info.getSvShubetuKnj());
                        // 事業所（本人） ＝ 処理3.5で取得した支援計画リスト.事業所（本人）
                        shienData.setSvJigyoKnj(info.getSvJigyoKnj());
                        // 期間（本人） ＝ 処理3.5.1. 期間の設定を参照
                        shienData.setKikanKnj(kikanKnj);
                        // 課題番号 ＝ 下記※を参照
                        // ※ 処理3.4. で取得した総合的課題と目標リストから総合的課題番号 = 処理3.5で取得した支援計画リスト.総合的課題番号のデータ.課題番号を設定する
                        Optional<PreventionPlanA31ReportServiceSogoKadaiData> sogoKadaiData = sogoKadaiDataList
                                .stream()
                                .filter(item -> item.getKadaiNo().split(ReportConstants.LINE_BREAKS
                                        + CommonConstants.FULL_WIDTH_SPACE)[0]
                                        .equals(CommonDtoUtil.objValToString(info.getSogoKadaiNo())))
                                .findFirst();
                        if (sogoKadaiData.isPresent()) {
                            shienData.setKadaiNo(sogoKadaiData.get().getKadaiNo());
                        }

                        // 総合的課題番号
                        shienData.setSogoKadaiNo(CommonDtoUtil.objValToString(info.getSogoKadaiNo()));
                        return shienData;
                    }).collect(Collectors.toList()));

            // リクエストパラメータ.データ.印刷設定.パラメータ14が1、且つ、処理3.4で取得した総合的課題と目標リストは取得できた場合、総合的課題と目標リストと支援計画リストの設定
            if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam14())
                    && sogoKadaiDataList.size() > 0) {
                // 総合的課題と目標リストと支援計画リスト
                List<PreventionPlanA31ReportServiceSogoKadaiDataAndShien> sogoKadaiDataAndShienList = new ArrayList<>();
                sogoKadaiDataAndShienList = (shienDataList.stream().map(info -> {
                    PreventionPlanA31ReportServiceSogoKadaiDataAndShien sogoKadaiDataAndShien = new PreventionPlanA31ReportServiceSogoKadaiDataAndShien();
                    // 総合的課題番号
                    sogoKadaiDataAndShien.setSogoKadaiNo(info.getSogoKadaiNo());
                    // 目標についての支援のポイント ＝ 当該行. 支援計画リストの同名項目
                    sogoKadaiDataAndShien.setShienPointKnj(info.getShienPointKnj());
                    // 具体的な支援の内容（本人） ＝ 当該行. 支援計画リストの同名項目
                    sogoKadaiDataAndShien.setInfoServiceKnj(info.getInfoServiceKnj());
                    // 具体的な支援の内容（保険・地域） ＝ 当該行. 支援計画リストの同名項目
                    sogoKadaiDataAndShien.setHokenServiceKnj(info.getHokenServiceKnj());
                    // サービス種別（本人） ＝ 当該行. 支援計画リストの同名項目
                    sogoKadaiDataAndShien.setSvShubetuKnj(info.getSvShubetuKnj());
                    // 事業所（本人） ＝当該行. 支援計画リストの同名項目
                    sogoKadaiDataAndShien.setSvJigyoKnj(info.getSvJigyoKnj());
                    // 期間（本人） ＝ 当該行.期間の設定を参照
                    sogoKadaiDataAndShien.setKikanKnj(info.getKikanKnj());

                    // 支援計画リスト ＝ 当該行.総合的課題番号により、処理3.4. 総合的課題と目標リストから総合的課題番号 = 当該行.総合的課題番号のデータ.総合的課題
                    List<PreventionPlanA31ReportServiceSogoKadaiData> sogoKadaiData = sogoKadaiDataList.stream()
                            .filter(item -> item.getKadaiNo().split(ReportConstants.LINE_BREAKS
                                    + CommonConstants.FULL_WIDTH_SPACE)[0].equals(info.getSogoKadaiNo()))
                            .collect(Collectors.toList());
                    if (sogoKadaiData.size() > 0) {
                        // 総合的課題と目標リストと支援計画リストの下記項目設定
                        // 総合的課題
                        sogoKadaiDataAndShien.setSogoKadaiKnj(sogoKadaiData.get(0).getSogoKadaiKnj());
                        // 課題に対する目標と具体策の提案
                        sogoKadaiDataAndShien.setSogoTeianKnj(sogoKadaiData.get(0).getSogoTeianKnj());
                        // 具体策についての意向 本人・家族
                        sogoKadaiDataAndShien.setSogoIkouKnj(sogoKadaiData.get(0).getSogoIkouKnj());
                        // 目標
                        sogoKadaiDataAndShien.setSogoMokuhyoKnj(sogoKadaiData.get(0).getSogoMokuhyoKnj());
                        // 課題番号
                        sogoKadaiDataAndShien.setKadaiNo(sogoKadaiData.get(0).getKadaiNo());
                    }
                    return sogoKadaiDataAndShien;
                }).collect(Collectors.toList()));
                model.setSogoKadaiDataAndShienList1(sogoKadaiDataAndShienList);
                model.setSogoKadaiDataAndShienList(new JRBeanCollectionDataSource(sogoKadaiDataAndShienList));
            }
        }
    }

    /**
     * 個人保護用情報の取得
     *
     * @param inDto 入力データ
     * @param model 帳票用データ詳細
     */
    private void getKihonHoukyoInfo(
            PreventionPlanA42ReportServiceParameterModel inDto,
            PreventionPlanA42ReportServiceInDto model) {

        List<KojinHogoJohoSetteiOutEntity> johoOutList = comMocSysiniSelectMapper
                .findKojinHogoJohoSetteiByCriteria(new KojinHogoJohoSetteiByCriteriaInEntity());

        // 処理「3.6.1.」で取得した値は1:表示の場合、個人情報設定フラグを取得
        if (CollectionUtils.isNotEmpty(johoOutList)
                && ReportConstants.STR_1.equals(johoOutList.getFirst().getNewParam())) {
            // 共通関数補足の「3. 個人情報設定フラグの取得」を参照
            F3gkGetProfileInDto f3gkInDto = new F3gkGetProfileInDto();
            // 職員ＩＤ = リクエストパラメータ.データ.DB未保存画面項目.職員ID
            f3gkInDto.setShokuId(CommonDtoUtil.strValToInt(inDto.getDbNoSaveData().getShokuId()));
            // 法人ＩＤ = 0
            f3gkInDto.setHoujinId(CommonConstants.INT_0);
            // 施設ＩＤ = 0
            f3gkInDto.setShisetuId(CommonConstants.INT_0);
            // 事業所ＩＤ = 0
            f3gkInDto.setSvJigyoId(CommonConstants.INT_0);
            // 機能名 = "prt"
            f3gkInDto.setKinounameKnj(ReportConstants.KINOU_NAME_PRT);
            // セクション = 一件目の印刷設定情報リスト.プロファイル
            f3gkInDto.setSectionKnj(inDto.getPrintSet().getProfile());
            // キー = "kojinhogo_flg"
            f3gkInDto.setKeyKnj(ReportConstants.S_KOJINHOGO_FLG);
            // パラメータ = 1
            f3gkInDto.setAsDefault(CommonConstants.STR_1);
            // システムコード = リクエストパラメータ.データ.DB未保存画面項目システムコード
            f3gkInDto.setGsyscd(inDto.getDbNoSaveData().getSysCode());
            // 更新回数 = 0
            f3gkInDto.setModifiedCnt(CommonConstants.STR_0);

            // 個人情報設定フラグ
            String lsParam = nds3GkFunc01Logic.getF3gkProfile(f3gkInDto);

            // 要支援区分
            // 個人情報設定フラグは0以外の場合、9を設定
            if (!ReportConstants.STR_0.equals(lsParam)) {
                // 要支援区分
                model.setYoshienKbn(CommonConstants.NUMBER_9);
            }
            if (CommonConstants.EMPTY_FLG_OFF.equals(inDto.getDbNoSaveData().getEmptyFlg())) {
                // 処理「3.6.2.」で取得した値は1の場合、個人情報保護(利用者名置換)を取得する。
                if (ReportConstants.STR_1.equals(lsParam)) {

                    // 利用者ID = リクエストパラメータ.データ.印刷対象履歴.利用者ID
                    String userId = inDto.getPrintSubjectHistory().getUserId();

                    // 検索条件がなし、個人情報保護(利用者名置換)取得終了
                    if (userId.isEmpty()) {
                        return;
                    }
                    // 個人情報保護(利用者名置換)を取得する。
                    String fullName = nds3GkBase02Logic.getKojinhogoNokado(CommonDtoUtil.strValToInt(userId));
                    model.setUserName(fullName);
                } else {
                    // 利用者情報の取得
                    getUserInfo(inDto, model);
                }

            }
        }
    }

    /**
     * 利用者情報の取得
     *
     * @param inDto 入力データ
     * @param model 帳票用データ詳細
     */
    private void getUserInfo(
            PreventionPlanA42ReportServiceParameterModel inDto,
            PreventionPlanA42ReportServiceInDto model) {
        // 利用者ID = リクエストパラメータ.データ.印刷対象履歴.利用者ID
        String userId = inDto.getPrintSubjectHistory().getUserId();

        KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
        // 利用者ID
        kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(userId);
        List<KghCpnRaiMonKentPrnPreSrw1OutEntity> userList = comTucUserSelectMapper
                .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
        if (userList != null && userList.size() > 0) {
            KghCpnRaiMonKentPrnPreSrw1OutEntity data = userList.get(0);
            // 利用者名
            model.setUserName(data.getFullName());
        }
    }

    /**
     * 和暦変換処理
     * 共通関数補足の和暦日付
     *
     * @param ymd 年月日
     */
    private String get2Gengouj(String ymd) {
        String dateValue = (ymd != null && !ymd.trim().isEmpty()) ? ymd : CommonConstants.BLANK_STRING;
        return nds3GkFunc01Logic.get2Gengouj(CommonConstants.NUMBER_ONE, dateValue);
    }

    /**
     * あらゆる日付を日本語元号付き日付に変換する
     *
     * @param ymd 年月日
     */
    private String get2Warekij(String ymd) {
        String dateValue = (ymd != null && !ymd.trim().isEmpty()) ? ymd : CommonConstants.BLANK_STRING;
        return nds3GkFunc01Logic.get2Warekij(dateValue, CommonConstants.NUMBER_ONE);
    }

    /**
     * 西暦日付文字列を和暦日付文字列に変換する
     * 和暦の先頭０の数字をスペースに変換する
     *
     * @param ymd 年月日
     */
    private String zeroToSpace(String ymd) {
        String dateValue = (ymd != null && !ymd.trim().isEmpty()) ? ymd : CommonConstants.BLANK_STRING;
        try {
            dateValue = DateConverter.toEra(
                    CommonConstants.DATE_FORMAT_YYYY_MM_DD_JP, CommonConstants.DATE_FORMAT_YYYY_MM_DD,
                    dateValue);
        } catch (IllegalArgumentException | SystemException | ParseException e) {
            e.printStackTrace();
        }
        return nds3GkFunc01Logic.zeroToSpace(dateValue);
    }

}
