package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00772SelectSubInfoI1;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentInterRAII1InitSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentInterRAII1InitSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.InterRaiAsensIShikkanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.InterRaiAsensIShikkanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.InterRaiAsensIShikkanSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.04.14
 * <AUTHOR>
 * @apiNote GUI00772_アセスメント(インターライ)画面I-1
 */
@Service
public class AssessmentInterRAII1InitSelectServiceImpl extends
        SelectServiceImpl<AssessmentInterRAII1InitSelectServiceInDto, AssessmentInterRAII1InitSelectServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** interRAIｱｾｽﾒﾝﾄI疾患情報取得 */
    @Autowired
    private InterRaiAsensIShikkanSelectMapper interRaiAsensIShikkanSelectMapper;

    /**
     * アセスメント(インターライ)画面I-1の初期処理する。
     * 
     * @param inDto 初期処理サービス入力Dto
     * @return 初期処理の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected AssessmentInterRAII1InitSelectServiceOutDto mainProcess(AssessmentInterRAII1InitSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);

        // 戻り情報
        AssessmentInterRAII1InitSelectServiceOutDto outDto = new AssessmentInterRAII1InitSelectServiceOutDto();

        // サブ情報（I2）
        GUI00772SelectSubInfoI1 subInfoI1 = new GUI00772SelectSubInfoI1();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. 疾患情報の取得===============
         * 
         */
        // 2.1. 下記のinterRAIｱｾｽﾒﾝﾄI疾患情報取得のDAOを利用し、サブ情報（I-1）を取得する。
        // DAOパラメータを作成
        InterRaiAsensIShikkanByCriteriaInEntity interRaiAsensIShikkanByCriteriaInEntity = new InterRaiAsensIShikkanByCriteriaInEntity();

        // アセスメントID
        interRaiAsensIShikkanByCriteriaInEntity.setAnRaiId(CommonDtoUtil.strValToInt(inDto.getRaiId()));
        List<InterRaiAsensIShikkanOutEntity> interRaiAsensIShikkanList = this.interRaiAsensIShikkanSelectMapper
                .findInterRaiAsensIShikkanByCriteria(interRaiAsensIShikkanByCriteriaInEntity);

        // アセスメント_I疾患（診断）情報取得を保持

        if (CollectionUtils.isNotEmpty(interRaiAsensIShikkanList)) {
            subInfoI1 = this.setSubInfoI1(interRaiAsensIShikkanList.get(0));
        }

        // サブ情報（I2）
        outDto.setSubInfoI1(subInfoI1);

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * サブ情報（C）を設定する
     * 
     * @param interRaiAsensumentCNinshiki サブ情報（C）
     * @return サブ情報（C）
     */
    private GUI00772SelectSubInfoI1 setSubInfoI1(InterRaiAsensIShikkanOutEntity interRaiAsensIShikkan) {
        // サブ情報（I2）
        GUI00772SelectSubInfoI1 subInfoI1 = new GUI00772SelectSubInfoI1();

        // アセスメントID
        subInfoI1.setRaiId(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getRaiId()));
        // 疾患_筋骨系_大腿骨骨折
        subInfoI1.setI1A(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1A()));
        // 疾患_筋骨系_その他の骨折
        subInfoI1.setI1B(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1B()));
        // 疾患_神経系_アルツハイマー病
        subInfoI1.setI1C(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1C()));
        // 疾患_神経系_認知症
        subInfoI1.setI1D(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1D()));
        // 疾患_神経系_片麻痺
        subInfoI1.setI1E(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1E()));
        // 疾患_神経系_多発性硬化症
        subInfoI1.setI1F(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1F()));
        // 疾患_神経系_対麻痺
        subInfoI1.setI1G(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1G()));
        // 疾患_神経系_パーキンソン病
        subInfoI1.setI1H(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1H()));
        // 疾患_神経系_四肢麻痺
        subInfoI1.setI1I(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1I()));
        // 疾患_神経系_脳卒中/脳血管障害
        subInfoI1.setI1J(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1J()));
        // 疾患_心肺系_CHD
        subInfoI1.setI1K(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1K()));
        // 疾患_心肺系_COPD
        subInfoI1.setI1L(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1L()));
        // 疾患_心肺系_CHF
        subInfoI1.setI1M(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1M()));
        // 疾患_心肺系_高血圧症
        subInfoI1.setI1N(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1N()));
        // 疾患_精神_不安症
        subInfoI1.setI1O(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1O()));
        // 疾患_精神_双極性障害
        subInfoI1.setI1P(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1P()));
        // 疾患_精神_うつ
        subInfoI1.setI1Q(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1Q()));
        // 疾患_精神_統合失調症
        subInfoI1.setI1R(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1R()));
        // 疾患_感染症_肺炎
        subInfoI1.setI1S(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1S()));
        // 疾患_感染症_UTI
        subInfoI1.setI1T(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1T()));
        // 疾患_その他_がん
        subInfoI1.setI1U(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1U()));
        // 疾患_その他_糖尿病
        subInfoI1.setI1V(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1V()));
        // i1_メモ
        subInfoI1.setI1MemoKnj(interRaiAsensIShikkan.getI1MemoKnj());
        // i1_メモフォント
        subInfoI1.setI1MemoFont(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1MemoFont()));
        // i1_メモ色
        subInfoI1.setI1MemoColor(CommonDtoUtil.objValToString(interRaiAsensIShikkan.getI1MemoColor()));

        return subInfoI1;

    }

}
