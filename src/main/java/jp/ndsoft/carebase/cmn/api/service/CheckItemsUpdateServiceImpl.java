package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckItem;
import jp.ndsoft.carebase.cmn.api.service.dto.CheckItemsUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.CheckItemsUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.KghTucCheckDataMapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghTucCheckHeadMapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghTucKrkKikanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTucCheckData;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTucCheckDataCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTucCheckHead;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTucCheckHeadCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTucKrkKikan;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * @since 2025.06.03
 * <AUTHOR>
 * @implNote GUI00892_チェック項目画面 データ保存
 */
@Service
public class CheckItemsUpdateServiceImpl extends
        UpdateServiceImpl<CheckItemsUpdateServiceInDto, CheckItemsUpdateServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 記録共通期間 */
    @Autowired
    private KghTucKrkKikanMapper kghTucKrkKikanMapper;

    /** チェック項目ヘッダ */
    @Autowired
    private KghTucCheckHeadMapper kghTucCheckHeadMapper;

    /** チェック項目情報 */
    @Autowired
    private KghTucCheckDataMapper kghTucCheckDataMapper;

    /**
     * データ保存
     * 
     * @param inDto データ保存サービス入力Dto
     * @return データ保存サービス出力Dto
     * @throws Exception Exception
     */
    @Override
    protected CheckItemsUpdateServiceOutDto mainProcess(CheckItemsUpdateServiceInDto inDto) throws Exception {

        LOG.info(Constants.START);

        CheckItemsUpdateServiceOutDto outDto = new CheckItemsUpdateServiceOutDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2.計画対象期間の保存処理===============
         * 
         */
        // 変数.計画対象期間ID
        String sc1Id = null;
        // 2.1.リクエストパラメータ.計画対象期間IDがnullの場合、【27-06記録共通期間】情報を登録する。
        if (StringUtil.isEmpty(inDto.getSc1Id())) {
            sc1Id = this.insertKikan(inDto);

        } else {
            // 2.2 変数.計画対象期間IDにリクエストパラメータ.計画対象期間IDを設定する
            sc1Id = inDto.getSc1Id();
        }

        /*
         * ===============3. チェック項目ヘッダの保存処理===============
         * 
         */
        // 変数.履歴ID
        String assId = null;
        // 3.1. リクエストパラメータ.履歴更新区分が"C":新規の場合、【32-01 チェック項目ヘッダ】情報を登録する。
        // 履歴ＩＤ
        if (CommonDtoUtil.isHistoryCreate(inDto)) {
            assId = this.insertKghTucCheckHead(inDto, sc1Id);

        }
        // 3.2. リクエストパラメータ.履歴更新区分が"U":更新の場合、 チェック項目詳細情報分で【32-02 チェック項目データ】情報を更新する。
        else if (CommonDtoUtil.isHistoryUpdate(inDto)) {

            // 変数.履歴IDにリクエストパラメータ.履歴IDを設定する
            assId = inDto.getAssId();

        }
        // 3.3. リクエストパラメータ.履歴更新区分が"D":削除の場合、【32-01 チェック項目ヘッダ】情報を削除更新する。
        else if (CommonDtoUtil.isHistoryDelete(inDto)) {
            this.deleteKghTucCheckHead(inDto, sc1Id);

        }

        /*
         * ===============4. チェック項目情報の保存処理==============
         * 
         */
        if (CollectionUtils.isNotEmpty(inDto.getCheckItemiList())) {
            for (Gui00892CheckItem checkItemInfo : inDto.getCheckItemiList()) {
                // 4.1. リクエストパラメータ.更新区分が"C":新規の場合、 チェック項目リスト分で【32-02 チェック項目データ】情報を登録する。
                if (CommonDtoUtil.isHistoryCreate(inDto)) {
                    this.insertKghTucCheckData(inDto, checkItemInfo, sc1Id, assId);
                }
                // 4.2. リクエストパラメータ.更新区分が"U":更新の場合、 チェック項目リスト分で【32-02 チェック項目データ】情報を更新する。
                else if (CommonDtoUtil.isHistoryUpdate(inDto)) {
                    int count = this.updateKghTucCheckData(inDto, checkItemInfo, sc1Id);
                    if (count <= 0) {
                        throw new ExclusiveException();
                    }

                }
                // 4.3. リクエストパラメータ.更新区分が"D":削除、チェック項目リスト分で【32-02 チェック項目データ】情報を削除更新する。
                else if (CommonDtoUtil.isHistoryDelete(inDto)) {
                    this.deleteKghTucCheckData(inDto, checkItemInfo, sc1Id);
                }
            }

        }

        LOG.info(Constants.END);

        return outDto;
    }

    /**
     * 計画対象期間の保存処理
     * 
     * @param inDto データ保存サービス入力Dto
     * @return 計画対象期間ID
     */
    private String insertKikan(CheckItemsUpdateServiceInDto inDto) throws Exception {
        // 計画対象期間ID
        String sc1Id = null;

        KghTucKrkKikan kghTucKrkKikan = new KghTucKrkKikan();
        // 法人ID
        kghTucKrkKikan.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        kghTucKrkKikan.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        kghTucKrkKikan.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getJigyoId()));
        // 利用者ID
        kghTucKrkKikan.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 種別ID
        kghTucKrkKikan.setSyubetsuId(CommonDtoUtil.strValToInt(inDto.getSyubetsuId()));
        // 開始日

        // DAOを実行
        this.kghTucKrkKikanMapper.insertSelectiveAndReturn(kghTucKrkKikan);

        sc1Id = CommonDtoUtil.objValToString(kghTucKrkKikan.getSc1Id());

        return sc1Id;
    }

    /**
     * チェック項目ヘッダを登録する。
     * 
     * @param inDto データ保存サービス入力Dto
     * @param sc1Id 計画期間ＩＤ
     * @return 履歴ＩＤ
     */
    private String insertKghTucCheckHead(CheckItemsUpdateServiceInDto inDto, String sc1Id) throws Exception {
        String assId = null;

        KghTucCheckHead kghTucCheckHead = new KghTucCheckHead();
        // 法人ＩＤ
        kghTucCheckHead.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ＩＤ
        kghTucCheckHead.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ＩＤ
        kghTucCheckHead.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getJigyoId()));
        // 利用者ID
        kghTucCheckHead.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 計画期間ＩＤ
        kghTucCheckHead.setSc1Id(CommonDtoUtil.strValToInt(sc1Id));
        // 様式ＩＤ
        kghTucCheckHead.setCstId(CommonDtoUtil.strValToInt(inDto.getCstId()));
        // 作成日
        kghTucCheckHead.setCreateYmd(inDto.getCreateYmd());
        // 作成者
        kghTucCheckHead.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));

        this.kghTucCheckHeadMapper.insertSelectiveAndReturn(kghTucCheckHead);

        assId = CommonDtoUtil.objValToString(kghTucCheckHead.getAssId());

        return assId;

    }

    /**
     * チェック項目ヘッダを削除更新する。
     * 
     * @param inDto データ保存サービス入力Dto
     * @return 更新件数
     */
    private void deleteKghTucCheckHead(CheckItemsUpdateServiceInDto inDto, String sc1Id) {

        KghTucCheckHeadCriteria criteria = new KghTucCheckHeadCriteria();

        // 法人ID
        criteria.createCriteria().andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getJigyoId()))
                // 利用者ID
                .andUseridEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1Id))
                // 履歴ID
                .andUseridEqualTo(CommonDtoUtil.strValToInt(inDto.getAssId()))
                // 様式ＩＤ
                .andCstIdEqualTo(CommonDtoUtil.strValToInt(inDto.getCstId()));

        this.kghTucCheckHeadMapper.deleteByCriteria(criteria);
    }

    /**
     * チェック項目情報を登録する。
     * 
     * @param inDto         データ保存サービス入力Dto
     * @param checkItemInfo チェック項目詳細情報
     * @param sc1Id         変数.計画期間ＩＤ
     * @param assId         変数.履歴ＩＤ
     */
    private void insertKghTucCheckData(CheckItemsUpdateServiceInDto inDto, Gui00892CheckItem checkItemInfo,
            String sc1Id, String assId) {

        KghTucCheckData kghTucCheckData = new KghTucCheckData();
        // 法人ＩＤ
        kghTucCheckData.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ＩＤ
        kghTucCheckData.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ＩＤ
        kghTucCheckData.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getJigyoId()));
        // 利用者ID
        kghTucCheckData.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 計画期間ＩＤ
        kghTucCheckData.setSc1Id(CommonDtoUtil.strValToInt(sc1Id));
        // 履歴ＩＤ
        kghTucCheckData.setAssId(CommonDtoUtil.strValToInt(assId));
        // 項目ＩＤ
        kghTucCheckData.setKomokuId(CommonDtoUtil.strValToInt(checkItemInfo.getKoumokuId()));
        // 細目ＩＤ
        kghTucCheckData.setSaimokuId(CommonDtoUtil.strValToInt(checkItemInfo.getSaimokuId()));
        // 内容ＩＤ
        kghTucCheckData.setNaiyoId(CommonDtoUtil.strValToInt(checkItemInfo.getNaiyoId()));
        // 課題対象フラグ
        kghTucCheckData.setKadaiFlg(CommonDtoUtil.strValToInt(checkItemInfo.getKadaiFlg()));
        // 備考
        kghTucCheckData.setBiko(checkItemInfo.getBiko());

        this.kghTucCheckDataMapper.insertSelective(kghTucCheckData);

    }

    /**
     * チェック項目情報を更新する。
     * 
     * @param inDto         データ保存サービス入力Dto
     * @param checkItemInfo チェック項目詳細情報
     * @param sc1Id         計画期間ＩＤ
     */
    private int updateKghTucCheckData(CheckItemsUpdateServiceInDto inDto, Gui00892CheckItem checkItemInfo,
            String sc1Id) {
        KghTucCheckData kghTucCheckData = new KghTucCheckData();
        // 内容ＩＤ
        kghTucCheckData.setNaiyoId(CommonDtoUtil.strValToInt(checkItemInfo.getNaiyoId()));
        // 課題対象フラグ
        kghTucCheckData.setKadaiFlg(CommonDtoUtil.strValToInt(checkItemInfo.getKadaiFlg()));
        // 備考
        kghTucCheckData.setBiko(checkItemInfo.getBiko());

        KghTucCheckDataCriteria criteria = new KghTucCheckDataCriteria();

        // 法人ID
        criteria.createCriteria().andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getJigyoId()))
                // 利用者ID
                .andUseridEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1Id))
                // 履歴ID
                .andAssIdEqualTo(CommonDtoUtil.strValToInt(inDto.getAssId()))
                // 項目ID
                .andKomokuIdEqualTo(CommonDtoUtil.strValToInt(checkItemInfo.getKoumokuId()))
                // 細目ID
                .andSaimokuIdEqualTo(CommonDtoUtil.strValToInt(checkItemInfo.getSaimokuId()));

        int count = this.kghTucCheckDataMapper.updateByCriteriaSelective(kghTucCheckData, criteria);

        return count;
    }

    /**
     * チェック項目情報を削除更新する。
     * 
     * @param inDto         データ保存サービス入力Dto
     * @param checkItemInfo チェック項目詳細情報
     * @param sc1Id         計画期間ＩＤ
     */
    private void deleteKghTucCheckData(CheckItemsUpdateServiceInDto inDto, Gui00892CheckItem checkItemInfo,
            String sc1Id) {

        KghTucCheckDataCriteria criteria = new KghTucCheckDataCriteria();

        // 法人ID
        criteria.createCriteria().andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getJigyoId()))
                // 利用者ID
                .andUseridEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1Id))
                // 履歴ID
                .andAssIdEqualTo(CommonDtoUtil.strValToInt(inDto.getAssId()))
                // 項目ID
                .andKomokuIdEqualTo(CommonDtoUtil.strValToInt(checkItemInfo.getKoumokuId()))
                // 細目ID
                .andSaimokuIdEqualTo(CommonDtoUtil.strValToInt(checkItemInfo.getSaimokuId()));

        this.kghTucCheckDataMapper.deleteByCriteria(criteria);

    }

}
