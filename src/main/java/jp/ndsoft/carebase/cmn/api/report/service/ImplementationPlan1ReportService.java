package jp.ndsoft.carebase.cmn.api.report.service;

import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.controllers.report.model.Jigyosyo;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.DCpnSypKkak211H21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.DCpnSypKkak211H21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.DsCpnSypKkak21pRirekiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.DsCpnSypKkak21pRirekiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMstChouhyouInkanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucSypKkak211SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucSypKkak212kkak213SelectMapper;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ImplementationPlan1ReportServiceDataInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServiceDbNoSaveData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ImplementationPlan1ReportServiceInitMasterObj;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServicePrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServicePrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.ShokuinInfoLogic;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.model.ImplementationPlan1ReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.report.dto.ImplementationPlan1ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ImplementationPlan1ReportServiceOutDto;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 * U0P150_（Ⅱ）実施計画～①(H21改訂版)
 * 
 * <AUTHOR>
 */
@Service("ImplementationPlan1Report")
public class ImplementationPlan1ReportService
        extends PdfReportServiceImpl<ImplementationPlan1ReportParameterModel, ImplementationPlan1ReportServiceOutDto> {

    /**
     * ロガー.
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // 印鑑欄情報を取得
    @Autowired
    private CpnMstChouhyouInkanSelectMapper cpnMstChouhyouInkanSelectMapper;
    // 履歴情報を取得
    @Autowired
    private CpnTucSypKkak211SelectMapper cpnTucSypKkak211SelectMapper;
    // 「課題目標期間」リストを取得する
    @Autowired
    private CpnTucSypKkak212kkak213SelectMapper cpnTucSypKkak212kkak213SelectMapper;
    // 職員基本情報取得
    @Autowired
    private ShokuinInfoLogic shokuinInfoLogic;
    // 利用者（年齢以外）情報取得
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;
    /** 設定の書込（NEXのパソコン単位の設定） */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /**
     * 帳票出力
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final ImplementationPlan1ReportParameterModel inDto,
            final ImplementationPlan1ReportServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final ImplementationPlan1ReportServiceInDto reportParameter = (ImplementationPlan1ReportServiceInDto) getReportParameters(
                inDto, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        String jrxmlFileName = ReportConstants.JRXML_U0P150_IMPLEMENTATIONPLAN1_H21_ON;
        // 記入用シート区分がOFFの場合、印鑑欄表示区分により、帳票を設定
        if (CommonConstants.STR_0.equals(reportParameter.getEmptyFlg())) {
            // 印鑑欄表示区分に0:非表示の場合
            if (CommonConstants.STR_0.equals(reportParameter.getHyoujiKbn())) {
                jrxmlFileName = ReportConstants.JRXML_U0P150_IMPLEMENTATIONPLAN1_H21_OFF_HIHYOUJI;
            } else {
                // 印鑑欄表示
                jrxmlFileName = ReportConstants.JRXML_U0P150_IMPLEMENTATIONPLAN1_H21_OFF_HYOUJI;
            }
        }

        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                ReportUtil.getReportJrxmlFile(getFwProps(), jrxmlFileName));

        // コンパイル
        final JasperReport jasperFile = JasperCompileManager.compileReport(is);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile, reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(inDto, jasperPrint);

        super.setFilepath(inDto, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }

    /**
     * 帳票パラメータ取得
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(
            ImplementationPlan1ReportParameterModel inDto,
            ImplementationPlan1ReportServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);
        // 帳票用データ詳細
        ImplementationPlan1ReportServiceInDto reportInfo = getU0P150ReportParameters(inDto);
        // ノート情報格納配列
        List<ImplementationPlan1ReportServiceInDto> reportInfoList = new ArrayList<ImplementationPlan1ReportServiceInDto>();
        reportInfoList.add(reportInfo);
        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(reportInfoList);
        reportInfo.setDataSource(dataSource);

        LOG.info(Constants.END);
        return reportInfo;
    }

    /**
     * U0P150_（Ⅱ）実施計画～①(H21改訂版)の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータ
     */
    private ImplementationPlan1ReportServiceInDto getU0P150ReportParameters(
            ImplementationPlan1ReportParameterModel inDto) {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 実施計画①帳票情報
        ImplementationPlan1ReportServiceInDto reportInfo = new ImplementationPlan1ReportServiceInDto();

        // 2.1. 印鑑欄情報の取得
        getInkanInfo(inDto, reportInfo);

        // 2.2. 履歴情報の取得
        getRirekiInfo(inDto, reportInfo);

        // 2.3. 明細リストの取得
        getDataList(inDto, reportInfo);

        // データ.DB未保存画面項目.記入用シートを印刷するフラグ
        String inEmptyFlg = CommonConstants.BLANK_STRING;
        // パラメータ.データのDB未保存画面項目がありの場合
        PrintReportServiceDbNoSaveData inDbNoSaveData = inDto.getDbNoSaveData();
        if (inDbNoSaveData != null) {
            // 記入用シートを印刷するフラグ
            inEmptyFlg = inDbNoSaveData.getEmptyFlg();
        }

        // パラメータ.印刷設定
        PrintReportServicePrintSet inPrintSet = inDto.getPrintSet();
        // データ.印刷設定.日付表示有無
        String inPrnDate = CommonConstants.BLANK_STRING;
        // パラメータ.印刷設定がありの場合
        if (inPrintSet != null) {
            // 日付表示有無
            inPrnDate = inPrintSet.getPrnDate();
            // 帳票タイトル
            reportInfo.setTitle(inPrintSet.getPrtTitle());
            // 作成者表示区分
            reportInfo.setShokuNameFlg(inPrintSet.getParam05());
        }

        // 記入用シート区分
        reportInfo.setEmptyFlg(inEmptyFlg);

        // 印刷するフラグ(emptyFlg)が1:チェックONの場合 or 日付表示有無が「1:印刷しない」の場合、true
        boolean shouldHideDate = CommonConstants.STR_1.equals(inEmptyFlg) || CommonConstants.STR_1.equals(inPrnDate);

        // 日付表示区分
        if (shouldHideDate) {
            reportInfo.setBlankdateFlg(CommonConstants.STR_0);
        } else {
            reportInfo.setBlankdateFlg(CommonConstants.STR_1);
        }

        // 日付
        if (shouldHideDate) {
            reportInfo.setBlankdate(CommonConstants.BLANK_STRING);
        } else if (CommonConstants.STR_2.equals(inPrnDate)) {
            // 共通関数補足の「2.1.」の和暦日付:DB未保存画面項目.指定日
            reportInfo.setBlankdate(get2Gengouj(
                    inDbNoSaveData == null ? CommonConstants.BLANK_STRING : inDbNoSaveData.getSelectDate()));
        } else if (CommonConstants.STR_3.equals(inPrnDate)) {
            // 共通関数補足の「1.1.」の印刷用日付:システム日付
            reportInfo.setBlankdate(nds3GkFunc01Logic.blankDate(inDto.getAppYmd()));
        }

        // 実施計画①帳票情報.事業所名
        reportInfo.setJigyoKnj(inDto.getJigyoKnj());

        // 氏名
        // データ.記入用シートを印刷するフラグ(emptyFlg)が1:チェックONの場合、空白
        if (CommonConstants.STR_1.equals(inEmptyFlg)) {
            reportInfo.setFullName(CommonConstants.BLANK_STRING);
        } else if (inDto.getPrintSubjectHistory() != null) {
            // 印刷対象履歴.利用者IDの氏名
            reportInfo.setFullName(getFullName(inDto.getPrintSubjectHistory().getUserId()));
        }

        // ・データ.印刷設定.パラメータ03が1:チェックONの場合
        if (inPrintSet != null && CommonConstants.STR_1.equals(inPrintSet.getParam03())) {
            // 敬称 ← データ.印刷設定.パラメータ04
            reportInfo.setKeisho(inPrintSet.getParam04());
        } else if (inDto.getInitMasterObj() != null
                && CommonConstants.STR_1.equals(inDto.getInitMasterObj().getKeishoFlg())) {
            // ・データ.初期設定マスタの情報.敬称オプションが1:チェックONの場合、敬称 ← データ.初期設定マスタの情報.敬称 //
            reportInfo.setKeisho(inDto.getInitMasterObj().getKeishoKnj());
        } else {
            // 以外の場合、""
            reportInfo.setKeisho(CommonConstants.KEISHO_STR_TONO);
        }

        // ページング表示区分:印刷オプション.記入用シートを印刷するフラグが1:チェックONの場合、0:非表示外の場合、以外の場合、1:表示
        reportInfo.setPagingFlg(CommonConstants.STR_1.equals(inEmptyFlg)
                ? CommonConstants.STR_0
                : CommonConstants.STR_1);

        return reportInfo;
    }

    /**
     * 印鑑欄情報の取得
     * 
     * @param inDto      入力データ
     * @param reportInfo 帳票用データ詳細
     */
    private void getInkanInfo(
            ImplementationPlan1ReportParameterModel inDto,
            ImplementationPlan1ReportServiceInDto reportInfo) {
        // 記入用シートを印刷するフラグ(emptyFlg)が1:チェックONの場合、印鑑欄表示区分に0:非表示を設定、印鑑1～印鑑15に""を設定する
        PrintReportServiceDbNoSaveData dbNoSaveData = inDto.getDbNoSaveData();
        if (CommonConstants.STR_1.equals(dbNoSaveData != null ? dbNoSaveData.getEmptyFlg() : null)) {
            // 印鑑欄表示区分に0:非表示を設定、印鑑1～印鑑15に""を設定する
            setDefaultInkanInfo(reportInfo);
            return;
        }
        // 検索条件がなし、印鑑欄情報の取得終了
        Jigyosyo jigyoDataInfo = inDto.getJigyoInfo();
        if (jigyoDataInfo == null
                || jigyoDataInfo.getHoujinId().isEmpty()
                || jigyoDataInfo.getShisetuId().isEmpty()
                || jigyoDataInfo.getSvJigyoId().isEmpty()) {
            return;
        }

        // リクエストパラメータ.記入用シートを印刷するフラグが0:チェックOFFの場合、下記の帳票データ情報を取得する
        // 印鑑欄情報の取得
        KghCpnMstChouhyouInkanPrnByCriteriaInEntity entity = new KghCpnMstChouhyouInkanPrnByCriteriaInEntity();
        // 法人ID
        entity.setAnKey1(CommonDtoUtil.strValToInt(jigyoDataInfo.getHoujinId()));
        // 施設ID
        entity.setAnKey2(CommonDtoUtil.strValToInt(jigyoDataInfo.getShisetuId()));
        // 事業所ID
        entity.setAnKey3(CommonDtoUtil.strValToInt(jigyoDataInfo.getSvJigyoId()));
        // 帳票セクション番号
        entity.setAsSec(ReportConstants.REPORT_CODE_IMPLEMENTATION_PLAN1);
        // 印鑑欄情報
        List<KghCpnMstChouhyouInkanPrnOutEntity> list = cpnMstChouhyouInkanSelectMapper
                .findKghCpnMstChouhyouInkanPrnByCriteria(entity);
        if (list == null || list.size() == 0) {
            return;
        }

        // 印鑑欄情報の取得
        KghCpnMstChouhyouInkanPrnOutEntity inkanData = list.get(0);
        // 印鑑欄表示区分
        reportInfo.setHyoujiKbn(CommonDtoUtil.objValToString(inkanData.getHyoujiKbn()));
        // 印鑑1
        reportInfo.setHanko1Knj(inkanData.getHanko1Knj());
        // 印鑑2
        reportInfo.setHanko2Knj(inkanData.getHanko2Knj());
        // 印鑑3
        reportInfo.setHanko3Knj(inkanData.getHanko3Knj());
        // 印鑑4
        reportInfo.setHanko4Knj(inkanData.getHanko4Knj());
        // 印鑑5
        reportInfo.setHanko5Knj(inkanData.getHanko5Knj());
        // 印鑑6
        reportInfo.setHanko6Knj(inkanData.getHanko6Knj());
        // 印鑑7
        reportInfo.setHanko7Knj(inkanData.getHanko7Knj());
        // 印鑑8
        reportInfo.setHanko8Knj(inkanData.getHanko8Knj());
        // 印鑑9
        reportInfo.setHanko9Knj(inkanData.getHanko9Knj());
        // 印鑑10
        reportInfo.setHanko10Knj(inkanData.getHanko10Knj());
        // 印鑑11
        reportInfo.setHanko11Knj(inkanData.getHanko11Knj());
        // 印鑑12
        reportInfo.setHanko12Knj(inkanData.getHanko12Knj());
        // 印鑑13
        reportInfo.setHanko13Knj(inkanData.getHanko13Knj());
        // 印鑑14
        reportInfo.setHanko14Knj(inkanData.getHanko14Knj());
        // 印鑑15
        reportInfo.setHanko15Knj(inkanData.getHanko15Knj());
    }

    /**
     * 印鑑欄表示区分に0:非表示を設定、印鑑1～印鑑15に""を設定する
     * 
     * @param reportInfo 帳票用データ詳細
     */
    private void setDefaultInkanInfo(ImplementationPlan1ReportServiceInDto reportInfo) {
        // 印鑑欄表示区分
        reportInfo.setHyoujiKbn(CommonConstants.STR_0);
        // 印鑑1
        reportInfo.setHanko1Knj(CommonConstants.BLANK_STRING);
        // 印鑑2
        reportInfo.setHanko2Knj(CommonConstants.BLANK_STRING);
        // 印鑑3
        reportInfo.setHanko3Knj(CommonConstants.BLANK_STRING);
        // 印鑑4
        reportInfo.setHanko4Knj(CommonConstants.BLANK_STRING);
        // 印鑑5
        reportInfo.setHanko5Knj(CommonConstants.BLANK_STRING);
        // 印鑑6
        reportInfo.setHanko6Knj(CommonConstants.BLANK_STRING);
        // 印鑑7
        reportInfo.setHanko7Knj(CommonConstants.BLANK_STRING);
        // 印鑑8
        reportInfo.setHanko8Knj(CommonConstants.BLANK_STRING);
        // 印鑑9
        reportInfo.setHanko9Knj(CommonConstants.BLANK_STRING);
        // 印鑑10
        reportInfo.setHanko10Knj(CommonConstants.BLANK_STRING);
        // 印鑑11
        reportInfo.setHanko11Knj(CommonConstants.BLANK_STRING);
        // 印鑑12
        reportInfo.setHanko12Knj(CommonConstants.BLANK_STRING);
        // 印鑑13
        reportInfo.setHanko13Knj(CommonConstants.BLANK_STRING);
        // 印鑑14
        reportInfo.setHanko14Knj(CommonConstants.BLANK_STRING);
        // 印鑑15
        reportInfo.setHanko15Knj(CommonConstants.BLANK_STRING);
    }

    /**
     * 履歴情報の取得
     * 
     * @param inDto      入力データ
     * @param reportInfo 帳票用データ詳細
     */
    private void getRirekiInfo(
            ImplementationPlan1ReportParameterModel inDto,
            ImplementationPlan1ReportServiceInDto reportInfo) {
        // 履歴情報項目のデフォルト設定
        // 作成者
        reportInfo.setShokuName(CommonConstants.BLANK_STRING);
        // 作成日
        reportInfo.setCreateYmd(CommonConstants.REPORT_YMD_DEFAUL_STRING);
        // ケースNo.
        reportInfo.setCaseNo(CommonConstants.BLANK_STRING);
        // 初回作成日
        reportInfo.setShokaiYmd(CommonConstants.REPORT_YMD_DEFAUL_STRING);
        // 留意点
        reportInfo.setRyuiKnj(CommonConstants.BLANK_STRING);
        // 改訂フラグ
        reportInfo.setKaiteiFlg(CommonConstants.BLANK_STRING);

        // 記入用シートを印刷するフラグ(emptyFlg)が1:チェックONの場合
        PrintReportServiceDbNoSaveData dbNoSaveData = inDto.getDbNoSaveData();
        if (CommonConstants.STR_1.equals(dbNoSaveData != null ? dbNoSaveData.getEmptyFlg() : null)) {
            return;
        }

        // 印刷対象履歴がなし又は検索条件がなし、履歴情報の取得終了
        PrintReportServicePrintSubjectHistory printSubjectHistory = inDto.getPrintSubjectHistory();
        if (printSubjectHistory == null || printSubjectHistory.getRirekiId().isEmpty()) {
            return;
        }

        // 履歴情報の取得
        DsCpnSypKkak21pRirekiByCriteriaInEntity entity = new DsCpnSypKkak21pRirekiByCriteriaInEntity();
        // 実施計画書～①ヘッダＩＤ
        entity.setRirekiId(CommonDtoUtil.strValToInt(printSubjectHistory.getRirekiId()));
        // 履歴情報の取得
        List<DsCpnSypKkak21pRirekiOutEntity> list = cpnTucSypKkak211SelectMapper
                .findDsCpnSypKkak21pRirekiByCriteria(entity);
        if (list == null || list.size() == 0) {
            return;
        }
        // 履歴情報
        DsCpnSypKkak21pRirekiOutEntity data = list.get(0);
        // 作成者
        PrintReportServicePrintSet inPrintSet = inDto.getPrintSet();
        // データ.印刷設定.パラメータ05が1:チェックONの場合
        if (inPrintSet != null && CommonConstants.STR_1.equals(inPrintSet.getParam05())) {
            // API定義の処理「2.4.1. 」の職員名（姓） ＋ " " ＋ 職員名（名）
            reportInfo.setShokuName(
                    shokuinInfoLogic.getShokuNameKnj(CommonDtoUtil.objValToString(data.getShokuId())));
        }

        // 作成日:共通関数補足の「2.1.」の和暦日付
        reportInfo.setCreateYmd(get2Gengouj(data.getCreateYmd()));
        // ケースNo.
        reportInfo.setCaseNo(CommonDtoUtil.strNullToEmpty(data.getCaseNo()));

        // 初回作成日:共通関数補足の「2.1.」の和暦日付 ※共通関数の戻り値が""の場合、" 年  月  日"を設定する
        String shokaiYmd = get2Gengouj(data.getShokaiYmd());
        if (CommonConstants.BLANK_STRING.equals(shokaiYmd)) {
            shokaiYmd = CommonConstants.REPORT_YMD_DEFAUL_STRING;
        }
        reportInfo.setShokaiYmd(shokaiYmd);

        // 留意点
        reportInfo.setRyuiKnj(CommonDtoUtil.strNullToEmpty(data.getRyuiKnj()));
        // 改訂フラグ
        reportInfo.setKaiteiFlg(CommonDtoUtil.objValToString(data.getKaiteiFlg()));
    }

    /**
     * 明細リストを取得する
     * 
     * @param inDto      入力データ
     * @param reportInfo 実施計画①帳票情報
     */
    private void getDataList(
            ImplementationPlan1ReportParameterModel inDto,
            ImplementationPlan1ReportServiceInDto reportInfo) {
        // データ.記入用シートを印刷するフラグ(emptyFlg)が1:チェックONの場合
        if (inDto.getDbNoSaveData() != null
                && CommonConstants.STR_1.equals(inDto.getDbNoSaveData().getEmptyFlg())) {
            // 帳票表示用明細リストに4件仮データを追加する。データの各項目が空を設定する
            setDefaultDataList(reportInfo);
            return;
        }

        // 明細リスト
        List<ImplementationPlan1ReportServiceDataInfo> dataList = new ArrayList<>();
        // 印刷対象履歴がなし又は検索条件がなし、履歴情報の取得終了
        PrintReportServicePrintSubjectHistory printSubjectHistory = inDto.getPrintSubjectHistory();
        if (printSubjectHistory != null && printSubjectHistory.getRirekiId() != null
                && !printSubjectHistory.getRirekiId().isEmpty()) {
            // 明細リストの取得
            DCpnSypKkak211H21ByCriteriaInEntity entity = new DCpnSypKkak211H21ByCriteriaInEntity();
            // 実施計画書～①ヘッダＩＤ
            entity.setRirekiId(CommonDtoUtil.strValToInt(printSubjectHistory.getRirekiId()));
            // 「課題目標期間」リストを取得する
            List<DCpnSypKkak211H21OutEntity> dataInfoList = cpnTucSypKkak212kkak213SelectMapper
                    .findDCpnSypKkak211H21ByCriteria(entity);
            if (dataInfoList != null && dataInfoList.size() > 0) {
                // 明細行合併区分：デフォルト設定「0:合併なし」
                String dataMergeFlg = CommonConstants.STR_0;
                // ・データ.初期設定マスタの情報.「実施計画～①：内容が空白の場合省略」が1:する、または、データ.初期設定マスタの情報.「実施計画～①：内容が同じ場合省略」が1:するの場合：1:合併あり
                if (inDto.getInitMasterObj() != null && (CommonConstants.STR_1
                        .equals(inDto.getInitMasterObj().getPkkak21Keisen1Flg())
                        || CommonConstants.STR_1
                                .equals(inDto.getInitMasterObj().getPkkak21Keisen2Flg()))) {
                    // 1:合併あり
                    dataMergeFlg = CommonConstants.STR_1;
                }

                // 初期設定マスタの情報
                ImplementationPlan1ReportServiceInitMasterObj initMasterObj = inDto.getInitMasterObj();
                // データ.初期設定マスタの情報.「実施計画～①：内容が空白の場合省略」が1:するの場合
                boolean pkkak21Keisen1Flg = (initMasterObj != null
                        && CommonConstants.STR_1.equals(initMasterObj.getPkkak21Keisen1Flg())) ? true : false;
                // 帳票表示用明細リスト ＝ API定義の処理「2.3.」のアウトプットを参照
                for (int i = 0; i < dataInfoList.size(); i++) {
                    // 帳票表示用明細
                    DCpnSypKkak211H21OutEntity data = dataInfoList.get(i);
                    // 帳票表示用明細
                    ImplementationPlan1ReportServiceDataInfo outDataInfo = new ImplementationPlan1ReportServiceDataInfo();

                    // 具体な課題
                    // ・データ.初期設定マスタの情報.「実施計画～①：内容が空白の場合省略」が1:するの場合、かつ、帳票表示用明細リストの具体的課題が空白の場合、
                    if (pkkak21Keisen1Flg && (data.getKadaiKnj() == null || data.getKadaiKnj().isEmpty()) && i > 0) {
                        // 帳票表示用明細リストの前件の具体的課題
                        outDataInfo.setKadaiKnj(dataList.get(i - 1).getKadaiKnj());
                    } else {
                        // 帳票表示用明細リストの具体的課題
                        outDataInfo.setKadaiKnj(CommonDtoUtil.strNullToEmpty(data.getKadaiKnj()));
                    }
                    // 生活目標
                    if (pkkak21Keisen1Flg && (data.getMokuhyoKnj() == null || data.getMokuhyoKnj().isEmpty())
                            && i > 0) {
                        // 帳票表示用明細リストの前件の目標
                        outDataInfo.setMokuhyoKnj(dataList.get(i - 1).getMokuhyoKnj());
                    } else {
                        // 帳票表示用明細リストの目標
                        outDataInfo.setMokuhyoKnj(CommonDtoUtil.strNullToEmpty(data.getMokuhyoKnj()));
                    }

                    // 目標期間(達成時期)
                    outDataInfo.setKikanKnj(CommonDtoUtil.strNullToEmpty(data.getKikanKnj()));
                    // 処遇の内容
                    outDataInfo.setNaiyoKnj(CommonDtoUtil.strNullToEmpty(data.getNaiyoKnj()));
                    // 担当者
                    outDataInfo.setTantoShokuKnj(CommonDtoUtil.strNullToEmpty(data.getTantoShokuKnj()));
                    // 明細行合併区分
                    outDataInfo.setDataMergeFlg(dataMergeFlg);
                    dataList.add(outDataInfo);
                }
            }
        }
        JRBeanCollectionDataSource reportDataList = new JRBeanCollectionDataSource(dataList);
        reportInfo.setDataList(reportDataList);
    }

    /**
     * 帳票表示用明細リストに4件仮データを追加する。データの各項目が空を設定する
     * 
     * @param reportInfo 実施計画①帳票情報
     */
    private void setDefaultDataList(ImplementationPlan1ReportServiceInDto reportInfo) {
        // 明細リスト
        List<ImplementationPlan1ReportServiceDataInfo> dataList = new ArrayList<>(4);
        // 帳票表示用明細リストに4件仮データを追加する。データの各項目が空を設定する
        for (int i = 0; i < 4; i++) {
            ImplementationPlan1ReportServiceDataInfo outDataInfo = new ImplementationPlan1ReportServiceDataInfo();
            // 具体な課題
            outDataInfo.setKadaiKnj(CommonConstants.BLANK_STRING);
            // 生活目標
            outDataInfo.setMokuhyoKnj(CommonConstants.BLANK_STRING);
            // 目標期間(達成時期)
            outDataInfo.setKikanKnj(CommonConstants.BLANK_STRING);
            // 処遇の内容
            outDataInfo.setNaiyoKnj(CommonConstants.BLANK_STRING);
            // 担当者
            outDataInfo.setTantoShokuKnj(CommonConstants.BLANK_STRING);
            // 明細行合併区分：0:合併なし
            outDataInfo.setDataMergeFlg(CommonConstants.STR_0);
            dataList.add(outDataInfo);
        }

        JRBeanCollectionDataSource reportDataList = new JRBeanCollectionDataSource(dataList);
        reportInfo.setDataList(reportDataList);
    }

    /**
     * 利用者IDの氏名の取得
     * 
     * @param userId 利用者ID
     */
    private String getFullName(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return CommonConstants.BLANK_STRING;
        }
        String fullName = CommonConstants.BLANK_STRING;
        // 利用者（年齢以外）情報取得
        RiyoshaKihonByCriteriaInEntity entity = new RiyoshaKihonByCriteriaInEntity();
        // 利用者ID
        entity.setAlUserid(CommonDtoUtil.strValToInt(userId));
        List<RiyoshaKihonOutEntity> userList = comTucUserSelectMapper.findRiyoshaKihonByCriteria(entity);
        if (userList != null && userList.size() > 0) {
            RiyoshaKihonOutEntity data = userList.get(0);
            fullName = data.getName1Knj() + CommonConstants.BLANK_SPACE + data.getName2Knj();
        }
        return fullName;
    }

    /**
     * 和暦変換処理
     * 共通関数補足の和暦日付(※共通関数の戻り値が""の場合、" 年  月  日"を設定する)
     * 
     * @param ymd 年月日
     */
    private String get2Gengouj(String ymd) {
        String dateValue = (ymd != null && !ymd.trim().isEmpty()) ? ymd : CommonConstants.BLANK_STRING;
        return nds3GkFunc01Logic.get2Gengouj(CommonConstants.NUMBER_ONE, dateValue);
    }
}
