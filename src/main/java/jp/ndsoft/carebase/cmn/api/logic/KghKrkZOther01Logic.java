package jp.ndsoft.carebase.cmn.api.logic;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 機能名からシステム種別取得
 * 
 * <AUTHOR>
 */
@Component
public class KghKrkZOther01Logic {

    /** メニュー: [win1][3GK]日誌複写ﾀﾞｲｱﾛｸﾞ */
    private static final String MENU_WIN_3GK_LOG_CPY_DIALOG = "[win1][3GK]日誌複写ﾀﾞｲｱﾛｸﾞ";
    /** メニュー: [win1][3GK][会議録]会議録複写ﾀﾞｲｱﾛｸ */
    private static final String MENU_WIN_3GK_CONFERENCE_CPY_DIALOG = "[win1][3GK][会議録]会議録複写ﾀﾞｲｱﾛｸ";
    /** メニュー: [mnu2][3GK]訪問看護計画書 */
    private static final String MENU_3GK_NURSING_PLAN = "[mnu2][3GK]訪問看護計画書";
    /** メニュー: "[mnu3][3GK]ﾘﾊﾋﾞﾘ情報提供書" */
    private static final String MENU_3GK_INFO_SERVICE_BOOK = "[mnu3][3GK]ﾘﾊﾋﾞﾘ情報提供書";
    /** メニュー: "[mnu3][3GK]ﾘﾊﾋﾞﾘ情報提供書" */
    private static final String MENU_3GK_INFO_SERVICE = "[mnu2][3GK]ﾘﾊﾋﾞﾘ情報提供";
    /** メニュー: "[mnu3][3GK]栄養情報提供書" */
    private static final String MENU_3GK_NURTRITION_BOOK = "[mnu3][3GK]栄養情報提供書";
    /** メニュー: "[mnu3][3GK]栄養情報提供書" */
    private static final String MENU_3GK_NURTRITION = "[mnu3][3GK]栄養情報提供";

    /** メニュー: "[mnu3][3GK]口腔ｹｱ情報提供" */
    private static final String MENU_3GK_MOUSE_BOOK = "[mnu3][3GK]口腔ｹｱ情報提供書";
    /** メニュー: "[mnu3][3GK]口腔ｹｱ情報提供" */
    private static final String MENU_3GK_MOUSE = "[mnu3][3GK]口腔ｹｱ情報提供";

    /** メニュー: "[mnu3][3GK]褥瘡情報提供書" */
    private static final String MENU_3GK_PRESSURE_ULCER_BOOK = "[mnu3][3GK]褥瘡情報提供書";
    /** メニュー: "[mnu3][3GK]褥瘡情報提供" */
    private static final String MENU_3GK_PRESSURE_ULCER = "[mnu3][3GK]褥瘡情報提供";

    /** 経過管理 */
    private static final String PROGRESS_MANAGE = "経過管理";
    /** ケアプランメニュー */
    private static final List<String> ARRAY_MENU_CAREPLAN = Arrays.asList("[mnu3][3GK]進捗管理2",
            "[mnu3][3GK][包括]ｱｾｽﾒﾝﾄ", "[win1][3GK][包括]ｱｾｽﾒﾝﾄ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][包括]検討表", "[win1][3GK][包括]検討表複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][GDL]ｱｾｽﾒﾝﾄ", "[win1][3GK][GDL]ｱｾｽﾒﾝﾄ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][hc2.0]ｱｾｽﾒﾝﾄ", "[win1][3GK][hc2.0]ｱｾｽﾒﾝﾄ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][hc2.0]選定表",
            "[mnu3][3GK][hc2.0]検討表", "[win1][3GK][hc2.0]検討表複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][hc2.0]該当表",
            "[mnu3][3GK][hc2.0]誘因項目",
            "[mnu3][3GK][hc2.0]相談受付", "[win1][3GK][hc2.0]相談受付複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][MDS2.1]ｱｾｽﾒﾝﾄ", "[win1][3GK][MDS2.1]ｱｾｽﾒﾝﾄ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][MDS2.1]選定表",
            "[mnu3][3GK][MDS2.1]検討表", "[win1][3GK][MDS2.1]検討表複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][MDS2.1]該当表", "[mnu3][3GK][MDS2.1]誘因項目",
            "[mnu3][3GK][syp]ﾌｪｰｽｼｰﾄ", "[win1][3GK][syp]ﾌｪｰｽｼｰﾄ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][syp]ｱｾｽﾒﾝﾄ", "[win1][3GK][syp]ｱｾｽﾒﾝﾄ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][cen]A-1", "[mnu3][3GK][cen]A-2", "[mnu3][3GK][cen]A-3",
            "[mnu3][3GK][cen]A-4", "[mnu3][3GK][cen]B-1", "[mnu3][3GK][cen]B-2",
            "[mnu3][3GK][cen]B-3", "[mnu3][3GK][cen]B-4", "[mnu3][3GK][cen]C-1-1",
            "[mnu3][3GK][cen]C-1-2", "[mnu3][3GK][cen]D-1", "[mnu3][3GK][cen]D-2",
            "[mnu3][3GK][cen]D-3", "[mnu3][3GK][cen]D-4", "[mnu3][3GK][cen]D-5",
            "[mnu3][3GK][cen]E", "[mnu3][3GK]24Hｼｰﾄ", "[win1][3GK]24Hシート複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]24Hｼｰﾄ(ﾋｱﾘﾝｸﾞｼｰﾄ)", "[mnu3][3GK][free]ﾌﾘｰｱｾｽﾒﾝﾄ",
            "[mnu3][3GK][free]課題分析", "[win1][3GK][free]課題分析複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]計画書(1)", "[win1][3GK]計画書(1)複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]計画書(2)", "[win1][3GK]計画書(2)複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]週間計画", "[win1][3GK]週間計画複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]計画書(1)①", "[mnu3][3GK]計画書(1)②", "[mnu3][3GK][GDL]計画書(1)<ｻｰﾋﾞｽ>旧様式",
            "[mnu3][3GK]計画書(1)③", "[win1][3GK]計画書(1)③複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]計画書(1)④", "[win1][3GK]計画書(1)④複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]計画書(2)旧様式", "[mnu3][3GK]日課計画", "[win1][3GK]日課計画複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]総合計画", "[win1][3GK]総合計画複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]実施計画～①", "[win1][3GK]実施計画～①複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]実施計画～②", "[win1][3GK]実施計画～②複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]実施計画～③", "[win1][3GK]実施計画～③複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]日課表", "[win1][3GK]日課表複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]週間表", "[win1][3GK]週間表複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]月間・年間表", "[win1][3GK]月間・年間表複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]ｹｱﾌﾟﾗﾝ基本情報", "[win1][3GK]ｹｱﾌﾟﾗﾝ基本情報複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]基本ﾁｪｯｸ", "[win1][3GK]基本ﾁｪｯｸ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]計画表", "[win1][3GK]計画表複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]ｹｱﾌﾟﾗﾝ興味・関心ﾁｪｯｸｼｰﾄ", "[win1][3GK]ｹｱﾌﾟﾗﾝ興味・関心ﾁｪｯｸｼｰﾄ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][会議録]会議録", "[mnu3][3GK][会議録]照会内容", "[win1][3GK][会議録]照会内容複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]利用票", "[mnu3][3GK]ｶﾚﾝﾀﾞｰ", "[mnu3][3GK]給付状況", "[mnu3][3GK]利用票利用者", "[mnu3][3GK]提供事業所",
            "[mnu3][3GK]ｼﾐｭﾚｰｼｮﾝ", "[mnu3][3GK]ﾓﾆﾀﾘﾝｸﾞ", "[win1][3GK]ﾓﾆﾀﾘﾝｸﾞ複写ﾀﾞｲｱﾛｸﾞ", "[mnu3][3GK]評価表",
            "[win1][3GK]評価表複写ﾀﾞｲｱﾛｸﾞ", "[mnu3][3GK]計画実施",
            "[win1][3GK]計画実施複写ﾀﾞｲｱﾛｸﾞ", "[mnu3][3GK][支援経過]支援経過", "[mnu3][3GK]調査票", "[win1][3GK]調査票複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]主治医", "[win1][3GK]主治医複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][委託先連携]出力", "[mnu3][3GK][委託先連携]取込", "[mnu3][3GK][委託先連携]予防台帳取込", "[mnu3][3GK][委託先連携]予防利用票出力",
            "[mnu3][3GK]ｹｱﾌﾟﾗﾝ統計", "[mnu3][3GK]ｹｱﾌﾟﾗﾝCSV",
            "[win1][3GK][会議録]会議録内容取込ﾀﾞｲｱﾛｸﾞ", "[mnu3][3GK][free]ﾌｪｰｽｼｰﾄ", "[mnu3][3GK][free]ﾁｪｯｸ項目",
            "[mnu3][3GK][free]課題立案", "[win1][3GK][free]ﾁｪｯｸ項目複写ﾀﾞｲｱﾛｸﾞ", "[win1][3GK][free]課題立案複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][myg]情報収集", "[win1][3GK][myg]情報収集複写ﾀﾞｲｱﾛｸﾞ", "[mnu3][3GK][myg]課題検討",
            "[win1][3GK][myg]課題検討複写ﾀﾞｲｱﾛｸﾞ", "[mnu3][3GK]課題整理総括", "[win1][3GK]課題整理総括複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]新評価表", "[win1][3GK]新評価表複写ﾀﾞｲｱﾛｸﾞ", "[mnu3][3GK][ITR]ｱｾｽﾒﾝﾄ", "[win1][3GK][ITR]ｱｾｽﾒﾝﾄ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][ITR]選定表", "[mnu3][3GK][ITR]検討表", "[win1][3GK][ITR]検討表複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK][ITR]該当表", "[mnu3][3GK][ITR]誘因項目", "[mnu3][3GK]入院時情報提供書", "[win1][3GK]入院時情報提供書複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]退院・退所情報記録書", "[win1][3GK]退院・退所情報記録書複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]退居・退所時情報提供書", "[win1][3GK]退居・退所時情報提供書複写ﾀﾞｲｱﾛｸﾞ");
    /** 個別計画 */
    private static final List<String> ARRAY_MENU_SPEC_PLAN = Arrays.asList("[mnu3][3GK]個別援助経過管理2", "[mnu3][3GK]評価",
            "[mnu3][3GK]個別計画ﾓﾆﾀﾘﾝｸﾞ",
            "[mnu3][3GK]入庫処理", "[mnu3][3GK]出庫処理", "[mnu3][3GK]用具点検", "[mnu3][3GK]同意書", "[mnu3][3GK]訪問看護情報提供書",
            "[mnu3][3GK]訪問看護報告書",
            "[mnu3][3GK]訪問看護計画書", "[mnu3][3GK]訪問看護指示", "[mnu3][3GK]内容作成", "[mnu3][3GK]個別援助計画書",
            "[mnu3][3GK]個別援助ｶﾝﾌｧﾚﾝｽ", "[mnu3][3GK]個別援助ｱｾｽﾒﾝﾄ",
            "[mnu3][3GK]ふくせん基本情報", "[mnu3][3GK]ふくせん選定提案", "[mnu3][3GK]ふくせん利用計画", "[mnu3][3GK]ふくせんﾓﾆﾀﾘﾝｸﾞ",
            "[mnu3][3GK]個別機能訓練計画_様式１", "[win1][3GK]個別機能訓練計画複写ﾀﾞｲｱﾛｸﾞ_様式１",
            "[mnu3][3GK]個別機能訓練計画_様式２", "[win1][3GK]個別機能訓練計画複写ﾀﾞｲｱﾛｸﾞ_様式２",
            "[mnu3][3GK]個別機能訓練計画_様式３", "[win1][3GK]個別機能訓練計画複写ﾀﾞｲｱﾛｸﾞ_様式３",
            "[mnu3][3GK]個別機能訓練計画_様式４", "[win1][3GK]個別機能訓練計画複写ﾀﾞｲｱﾛｸﾞ_様式４",
            "[win1][3GK]計画書取込（個別機能訓練計画書）",
            "[mnu3][3GK]LIFE個別機能訓練計画_様式１", "[mnu3][3GK]LIFE個別機能訓練計画_様式２", "[mnu3][3GK]LIFE個別機能訓練計画_様式３",
            "[mnu3][3GK]LIFE個別援助計画書", "[win1][3GK]個別援助計画書取込ﾀﾞｲｱﾛｸﾞ",
            "[win1][3GK]個別機能訓練計画書取込_R6（一体的計画書）", "[win1][3GK]生活機能チェックシート取込_R6（一体的計画書）");
    /** 計画書（固定） */
    private static final List<String> ARRAY_MENU_PLAN = Arrays.asList(
            "[mnu3][3GK]ﾘﾊﾋﾞﾘｱｾｽﾒﾝﾄ", "[win1][3GK]ﾘﾊﾋﾞﾘｱｾｽﾒﾝﾄ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]ﾘﾊﾋﾞﾘｶﾝﾌｧﾚﾝｽ", "[mnu3][3GK]ﾘﾊﾋﾞﾘ計画書", "[mnu3][3GK]ﾘﾊﾋﾞﾘｽｹｼﾞｭｰﾙ", "[mnu3][3GK]ﾘﾊﾋﾞﾘ詳細計画",
            "[mnu3][3GK]ﾘﾊﾋﾞﾘ報告書",
            "[mnu3][3GK]ﾘﾊﾋﾞﾘ情報提供書", "[mnu3][3GK]ﾘﾊﾋﾞﾘ基本情報", "[mnu3][3GK]ﾘﾊﾋﾞﾘ統計", "[mnu3][3GK]ﾘﾊﾋﾞﾘCSV",
            "[mnu3][3GK]報告書【未使用】", "[mnu3][3GK]ﾘﾊﾋﾞﾘ経過管理2", "[mnu3][3GK]計画書（固定）",
            "[mnu3][3GK]LIFE計画書（リハビリ）", "[win1][3GK]計画書取込（リハビリテーション計画書）",
            "[win1][3GK]リハビリテーション計画書取込_R6（一体的計画書）");
    /** 栄養 */
    private static final List<String> ARRAY_MENU_NUTRITION = Arrays.asList(
            "[mnu3][3GK]栄養ｽｸﾘｰﾆﾝｸﾞ", "[win1][3GK]栄養ｽｸﾘｰﾆﾝｸﾞ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]栄養ｱｾｽﾒﾝﾄ", "[mnu3][3GK]栄養ﾌﾟﾛｾｽ", "[win1][3GK]栄養ｱｾｽﾒﾝﾄ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]栄養ｶﾝﾌｧﾚﾝｽ", "[win1][3GK]栄養ｶﾝﾌｧﾚﾝｽ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]栄養ｹｱ計画書", "[win1][3GK]栄養ｹｱ計画書複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]栄養ﾓﾆﾀﾘﾝｸﾞ",
            "[mnu3][3GK]栄養情報提供書", "[mnu3][3GK]経過記録", "[mnu3][3GK]栄養基本情報", "[mnu3][3GK]栄養統計", "[mnu3][3GK]栄養CSV",
            "[mnu3][3GK]栄養ｹｱ経過管理2",
            "[win1][3GK]栄養ﾌﾟﾛｾｽ複写ﾀﾞｲｱﾛｸﾞ", "[mnu3][3GK]栄養ｹｱ経口移行・維持計画", "[win1][3GK]栄養ｹｱ経口移行・維持計画複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]LIFE栄養ﾌﾟﾛｾｽ",
            "[mnu3][3GK]LIFE栄養ｹｱ計画書", "[win1][3GK]計画書取込（栄養ケア計画書）",
            "[win1][3GK]栄養プロセス取込_R6（一体的計画書）"
    );
    /** 口腔 */
    private static final List<String> ARRAY_MENU_MOUSE = Arrays.asList(
            "[mnu3][3GK]口腔ｹｱ経過管理2", "[mnu3][3GK]口腔CSV", "[mnu3][3GK]口腔統計",
            "[mnu3][3GK]口腔基本情報",
            "[mnu3][3GK]口腔ｹｱ情報提供書",
            "[mnu3][3GK]口腔報告書",
            "[mnu3][3GK]口腔ｹｱ詳細計画",
            "[mnu3][3GK]口腔ｹｱ計画書",
            "[mnu3][3GK]口腔ｶﾝﾌｧﾚﾝｽ",
            "[mnu3][3GK]口腔ｱｾｽﾒﾝﾄ", "[win1][3GK]口腔ｱｾｽﾒﾝﾄ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]口腔ｽｸﾘｰﾆﾝｸﾞ", "[win1][3GK]口腔ｽｸﾘｰﾆﾝｸﾞ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]LIFE口腔報告書", "[mnu3][3GK]LIFE口腔ｹｱ計画書",
            "[win1][3GK]計画書取込（口腔ケア報告書）", "[win1][3GK]計画書取込（口腔ケア計画書）",
            "[mnu3][3GK]口腔衛生管理加算", "[mnu3][3GK]口腔衛生管理加算（LIFE）", "[win1][3GK]口腔衛生管理加算複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]口腔機能向上加算", "[mnu3][3GK]口腔機能向上加算（LIFE）", "[win1][3GK]口腔機能向上加算複写ﾀﾞｲｱﾛｸﾞ",
            "[win1][3GK]口腔衛生管理加算取込_R6（一体的計画書）", "[win1][3GK]口腔機能向上加算取込_R6（一体的計画書）"
    );
    /** 褥瘡 */
    private static final List<String> ARRAY_MENU_PRESSURE_ULCER = Arrays.asList(
            "[mnu3][3GK]褥瘡ｱｾｽﾒﾝﾄ", "[win1][3GK]褥瘡ｱｾｽﾒﾝﾄ複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]褥瘡ｶﾝﾌｧﾚﾝｽ",
            "[mnu3][3GK]褥瘡ｹｱ計画書",
            "[mnu3][3GK]褥瘡ｹｱ詳細計画",
            "[mnu3][3GK]褥瘡評価", "[win1][3GK]褥瘡評価複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]褥瘡報告書",
            "[mnu3][3GK]褥瘡情報提供書",
            "[mnu3][3GK]褥瘡基本情報",
            "[mnu3][3GK]褥瘡統計",
            "[mnu3][3GK]褥瘡CSV",
            "[mnu3][3GK]褥瘡ｹｱ経過管理2",
            "[mnu3][3GK]LIFE褥瘡マネジメント加算", "[mnu3][3GK]LIFE褥瘡対策指導管理",
            "[mnu3][3GK]褥瘡ﾏﾈ加算（LIFE）", "[mnu3][3GK]褥瘡対策管理（LIFE）",
            "[mnu3][3GK]褥瘡ﾏﾈ加算", "[mnu3][3GK]褥瘡対策管理",
            "[win1][3GK]褥瘡ﾏﾈ加算複写ﾀﾞｲｱﾛｸﾞ", "[win1][3GK]褥瘡対策管理複写ﾀﾞｲｱﾛｸﾞ"
    );
    /** 個別計画 */
    private static final List<String> ARRAY_MENU_SPEC_PLAN_INFO = Arrays.asList("[mnu3][3GK]訪問看護情報提供書",
            "[mnu3][3GK]訪問看護報告書", "[mnu3][3GK]訪問看護計画書", "[mnu3][3GK]訪問看護指示");
    /** 個別計画からの計画取込 */
    private static final List<String> ARRAY_MENU_CAREPLAN2_INFO = Arrays.asList("[win1][3GK]計画書(2)取込ﾀﾞｲｱﾛｸﾞ",
            "[win1][3GK]計画表取込ﾀﾞｲｱﾛｸﾞ",
            "[win1][3GK]実施計画～①取込ﾀﾞｲｱﾛｸﾞ", "[win1][3GK]実施計画～②取込ﾀﾞｲｱﾛｸﾞ", "[win1][3GK]実施計画～③取込ﾀﾞｲｱﾛｸﾞ");
    /** 24H */
    private static final List<String> ARRAY_MENU_24H_INFO = Arrays.asList("[mnu3][3GK][24H]聞き取りｼｰﾄ",
            "[mnu3][3GK][24H]24Hｼｰﾄ",
            "[mnu3][3GK][24H]24Hｼｰﾄ一覧表",
            "[win1][3GK][24H]聞き取りｼｰﾄ複写ﾀﾞｲｱﾛｸﾞ",
            "[win1][3GK][24H]24Hｼｰﾄ複写ﾀﾞｲｱﾛｸﾞ");
    /** ﾘﾊﾋﾞﾘ計画書を追加 */
    private static final List<String> ARRAY_MENU_INFO_SERVICE = Arrays.asList("[mnu3][3GK][R4]進捗管理",
            "[mnu3][3GK][R4]ﾌﾟﾚｲﾝﾃｰｸ",
            "[mnu3][3GK][R4]A-1",
            "[mnu3][3GK][R4]A-2",
            "[mnu3][3GK][R4]A-3",
            "[mnu3][3GK][R4]総合計画書",
            "[mnu3][3GK][R4]ADL比較一覧",
            "[mnu3][3GK][R4]実施状況確認",
            "[mnu3][3GK][R4]ﾘﾊ実施計画書",
            "[mnu3][3GK][R4]外部連携",
            "[mnu3][3GK][R4]ﾘﾊﾋﾞﾘ計画書",
            "[win1][3GK][R4]ﾌﾟﾚｲﾝﾃｰｸ複写ﾀﾞｲｱﾛｸﾞ",
            "[win1][3GK][R4]A-1複写ﾀﾞｲｱﾛｸﾞ",
            "[win1][3GK][R4]A-2複写ﾀﾞｲｱﾛｸﾞ",
            "[win1][3GK][R4]A-3複写ﾀﾞｲｱﾛｸﾞ",
            "[win1][3GK][R4]総合計画書複写ﾀﾞｲｱﾛｸﾞ",
            "[win1][3GK][R4]ﾘﾊ実施計画書複写ﾀﾞｲｱﾛｸﾞ",
            "[mnu3][3GK]LIFEﾘﾊﾋﾞﾘ計画書（R4）");
    /** [実施記録]生活支援記録, [実施記録]看護記録 */
    private static final List<String> ARRAY_MENU_RECORD = Arrays.asList("[実施記録]生活支援記録", "[実施記録]看護記録");
    /** [実施記録]生活支援記録, [実施記録]看護記録 */
    private static final List<String> ARRAY_MENU_CAREPLAN_1_2 = Arrays.asList("[実施記録]生活支援記録", "[実施記録]看護記録");
    /** システム種別ID: 2 */
    private static final Integer SYUBETU_2 = 2;
    /** システム種別ID: 3 */
    private static final Integer SYUBETU_3 = 3;
    /** システム種別ID: 4 */
    private static final Integer SYUBETU_4 = 4;
    /** システム種別ID: 5 */
    private static final Integer SYUBETU_5 = 5;
    /** システム種別ID: 6 */
    private static final Integer SYUBETU_6 = 6;
    /** システム種別ID: 7 */
    private static final Integer SYUBETU_7 = 7;
    /** システム種別ID: 12 */
    private static final Integer SYUBETU_12 = 12;
    /** システム種別ID: 13 */
    private static final Integer SYUBETU_13 = 13;
    /** システム種別ID: 0 */
    private static final Integer SYUBETU_0 = 0;

    /**
     * 機能名からシステム種別取得
     * 
     * @param menu2Knj  メニュー２名称
     * @param menu3Knj  メニュー３名称
     * @param freeParam 自由パラメータ
     * @return システム種別ID
     */
    public Integer getKghKrkSyubetu(String menu2Knj, String menu3Knj, String freeParam) {
        String lsMenu3Knj = StringUtils.EMPTY;
        // システム種別ID
        int syubetu = 0;
        if (Arrays.asList(MENU_WIN_3GK_CONFERENCE_CPY_DIALOG, MENU_WIN_3GK_LOG_CPY_DIALOG).contains(menu3Knj)) {
            // 日誌複写の場合は val_s から取得
            lsMenu3Knj = freeParam;
        } else {
            // 日誌複写以外はそのまま使用
            lsMenu3Knj = menu3Knj;
        }
        // ケアプランメニュー
        if (ARRAY_MENU_CAREPLAN.contains(lsMenu3Knj)) {
            syubetu = SYUBETU_2;
        } else if (ARRAY_MENU_SPEC_PLAN.contains(lsMenu3Knj)) {
            // 個別計画
            if (ARRAY_MENU_SPEC_PLAN_INFO.contains(menu3Knj)) {
                syubetu = SYUBETU_3;
            } else {
                syubetu = SYUBETU_0;
            }
            if (MENU_3GK_NURSING_PLAN.equals(menu2Knj)) {
                syubetu = SYUBETU_0;
            }
        } else if (ARRAY_MENU_PLAN.contains(lsMenu3Knj)) {
            // 計画書（固定）
            if (MENU_3GK_INFO_SERVICE_BOOK.equals(menu3Knj)) {
                syubetu = SYUBETU_0;
            } else {
                syubetu = SYUBETU_4;
            }
            if (MENU_3GK_INFO_SERVICE.equals(menu2Knj)) {
                syubetu = SYUBETU_0;
            }
        } else if (ARRAY_MENU_NUTRITION.contains(lsMenu3Knj)) {
            // 栄養
            if (MENU_3GK_NURTRITION_BOOK.equals(menu3Knj)) {
                syubetu = SYUBETU_0;
            } else {
                syubetu = SYUBETU_5;
            }
            if (MENU_3GK_NURTRITION.equals(menu2Knj)) {
                syubetu = SYUBETU_0;
            }
        } else if (ARRAY_MENU_MOUSE.contains(lsMenu3Knj)) {
            // 口腔
            if (MENU_3GK_MOUSE_BOOK.equals(menu3Knj)) {
                syubetu = SYUBETU_0;
            } else {
                syubetu = SYUBETU_6;
            }
            if (MENU_3GK_MOUSE.equals(menu2Knj)) {
                syubetu = SYUBETU_0;
            }
        } else if (ARRAY_MENU_PRESSURE_ULCER.contains(lsMenu3Knj)) {
            // 褥瘡
            if (MENU_3GK_PRESSURE_ULCER_BOOK.equals(menu3Knj)) {
                syubetu = SYUBETU_0;
            } else {
                syubetu = SYUBETU_7;
            }
            if (MENU_3GK_PRESSURE_ULCER.equals(menu2Knj)) {
                syubetu = SYUBETU_0;
            }
        } else if (ARRAY_MENU_CAREPLAN2_INFO.contains(lsMenu3Knj)) {
            // 個別計画からの計画取込
            syubetu = SYUBETU_2;
        } else if (ARRAY_MENU_24H_INFO.contains(lsMenu3Knj)) {
            syubetu = SYUBETU_12;
        } else if (ARRAY_MENU_INFO_SERVICE.contains(lsMenu3Knj)) {
            // LIFEﾘﾊﾋﾞﾘ計画書（R4）
            syubetu = SYUBETU_13;
        }
        if (StringUtils.isNoneEmpty(menu2Knj)) {
            String keyword = StringUtils.substring(menu2Knj, 12);
            if (StringUtils.isNotEmpty(keyword)) {
                // 進捗管理から呼ばれた場合
                if (PROGRESS_MANAGE.equals(keyword)) {
                    syubetu = SYUBETU_2;
                }
            } else if (ARRAY_MENU_RECORD.contains(keyword)) {
                // 個人カルテから呼ばれた場合
                if (ARRAY_MENU_CAREPLAN_1_2.contains(StringUtils.substring(keyword, 12, 6))) {
                    syubetu = SYUBETU_2;
                }
            }
        }

        return syubetu;
    }

}
