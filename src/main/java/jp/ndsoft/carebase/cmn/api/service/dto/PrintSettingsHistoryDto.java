package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 印刷対象履歴Dto
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class PrintSettingsHistoryDto extends CpnTucIkensho2PrintRirekiDataDto {

    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 帳票種類 */
    private String prtIdx;

    /** セクション番号 */
    private String sectionNo;

    /** 帳票番号 */
    private String prtNo;

    /** プロファイル */
    private String profile;

    /** 計画期間ID */
    private String sc1Id;

    /** 当該年月 */
    private String tougaiYm;

    /** 様式CD */
    private String yousikiCd;

    /** サービス利用票・別表ヘッダ */
    private List<CmnTucPlanDto> cmnTucPlan;
}
