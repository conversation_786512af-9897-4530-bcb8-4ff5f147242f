package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.05.12
 * <AUTHOR>
 * @description GUI00904_課題立案様式設定マスタ参照DTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Gui00904kghMocKrkFree2InDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    // 項目名
    @NotEmpty
    private String nameKnj;
    // 入力値変更フラグ
    private String inputKbnUpdateFlag;
    // 入力値
    private String inputKbn;
    // 連動項目
    private String rendouKbn;
    // 文字数
    private String widthCnt;
}
