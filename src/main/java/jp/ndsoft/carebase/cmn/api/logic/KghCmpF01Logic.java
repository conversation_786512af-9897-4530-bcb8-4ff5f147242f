package jp.ndsoft.carebase.cmn.api.logic;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import io.grpc.netty.shaded.io.netty.util.internal.StringUtil;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.dto.GengouInfoExpandOutDto;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.ComMhcItemuseMapper;
import jp.ndsoft.carebase.common.dao.mybatis.ComMhcItemuseSougouMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMhcItemuse;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMhcItemuseCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMhcItemuseSougou;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMhcItemuseSougouCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChikuSogoShienSentaMeishoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChikuSogoShienSentaMeishoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcTermSvtype35TermidByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcTermSvtype35TermidOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcTermTermidByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcTermTermidOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMscKaigoHokenjaDataByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMscKaigoHokenjaDataOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMscSvjigyoNameFirstSvKindCdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnYokaip1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnYokaip1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnYokaip2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnYokaip2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnYokaip3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnYokaip3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpncCks11ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpncCks11OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyoCdNumberByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyoCdNumberOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyoKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyoKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyoRyakuKnjRirekiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyoRyakuKnjRirekiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoSaabisu2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoSaabisu2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkJikoHoukokuOpe2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkJikoHoukokuOpe2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KikanYmdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KikanYmdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ScodeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ScodeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SeishoMeiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SeishoMeiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinFullNameByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinFullNameOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvItemcodeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvItemcodeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvScodeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvScodeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TantoIdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TantoIdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TantouIdUserByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TantouIdUserOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TelFaxByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TelFaxOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YokaigoJotaiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YokaigoJotaiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSougouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcTermSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcTermTermidSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscKaigoHokenjaSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoNameFirstSvKindCdSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoRirekiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscYokaigoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucKaigoNinteiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucTantoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks11SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnYokaip3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucKrkKikanSelectMapper;

/**
 * KghCmpF01Logicクラス
 * 
 * <AUTHOR>
 */
@Component
public class KghCmpF01Logic {

    /** 月開始日 */
    private static final String DEFAULT_START_DAY = "/01";
    /** 月終了日 */
    private static final String DEFAULT_END_DAY = "/31";
    /** エラーコート */
    private static final Integer INVALID_ITEM_CODE = 0;
    /** 期間ID: 2003/03以前 */
    private static final Integer TERM_ID_1 = 1;
    /** 期間ID: 2005/09以前 */
    private static final Integer TERM_ID_2 = 2;
    /** 期間ID: 2006/03以前 */
    private static final Integer TERM_ID_3 = 3;
    /** 期間ID: 2009/03以前 */
    private static final Integer TERM_ID_4 = 4;
    /** 期間ID: 2012/03以前 */
    private static final Integer TERM_ID_5 = 5;
    /** 期間ID: 2014/03以前 */
    private static final Integer TERM_ID_6 = 6;
    /** 期間ID: 2015/03以前 */
    private static final Integer TERM_ID_7 = 7;
    /** 期間ID: 2017/03以前 */
    private static final Integer TERM_ID_8 = 8;
    /** 期間ID: 2018/03以前 */
    private static final Integer TERM_ID_9 = 9;
    /** 期間ID: 2019/09以前 */
    private static final Integer TERM_ID_10 = 10;
    /** 期間ID: 2021/03以前 */
    private static final Integer TERM_ID_11 = 11;
    /** 期間ID: 2022/09以前 */
    private static final Integer TERM_ID_12 = 12;
    /** 期間ID: 2024/03以前 */
    private static final Integer TERM_ID_13 = 13;
    /** 期間ID: 2024/05以前 */
    private static final Integer TERM_ID_14 = 14;
    /** 期間ID: それ以降 */
    private static final Integer TERM_ID_15 = 15;
    /** 境界値: 2003/03 */
    private static final String BOUNDARY_2003_03 = "2003/03";
    /** 境界値: 2005/09 */
    private static final String BOUNDARY_2005_09 = "2005/09";
    /** 境界値: 2006/03 */
    private static final String BOUNDARY_2006_03 = "2006/03";
    /** 境界値: 2009/03 */
    private static final String BOUNDARY_2009_03 = "2009/03";
    /** 境界値: 2012/03 */
    private static final String BOUNDARY_2012_03 = "2012/03";
    /** 境界値: 2014/03 */
    private static final String BOUNDARY_2014_03 = "2014/03";
    /** 境界値: 2015/03 */
    private static final String BOUNDARY_2015_03 = "2015/03";
    /** 境界値: 2017/03 */
    private static final String BOUNDARY_2017_03 = "2017/03";
    /** 境界値: 2018/03 */
    private static final String BOUNDARY_2018_03 = "2018/03";
    /** 境界値: 2019/09 */
    private static final String BOUNDARY_2019_09 = "2019/09";
    /** 境界値: 2021/03 */
    private static final String BOUNDARY_2021_03 = "2021/03";
    /** 境界値: 2022/09 */
    private static final String BOUNDARY_2022_09 = "2022/09";
    /** 境界値: 2024/03 */
    private static final String BOUNDARY_2024_03 = "2024/03";
    /** 境界値: 2024/05 */
    private static final String BOUNDARY_2024_05 = "2024/05";
    /** scode: 23360 */
    private static final String ENVIRONMENTAL_CRITERIA_PREFIX = "23360";
    /** scode: 26360 */
    private static final String EQUIPMENT_CRITERIA_PREFIX = "26360";
    /** scode suffix: 100 */
    private static final String REPLACEMENT_SUFFIX_100 = "100";
    /** scode suffix: 000 */
    private static final String REPLACEMENT_SUFFIX_000 = "000";
    /** "0" */
    private static final String STRING_ZERO = "0";
    /** サービス種類コード: 00 */
    private static final String SV_TYPE_00 = "00";
    /** サービス種類コード: 61 */
    private static final String SV_TYPE_61 = "61";
    /** サービス種類コード: 65 */
    private static final String SV_TYPE_65 = "65";
    /** サービス種類コード: 81 */
    private static final String SV_TYPE_81 = "81";
    /** サービス種類コード: 99 */
    private static final String SV_TYPE_99 = "99";
    /** サービス種類コード: A1 */
    private static final String SV_TYPE_A1 = "A1";
    /** サービス種類コード: A5 */
    private static final String SV_TYPE_A5 = "A5";
    /** サービス種類コード: A9 */
    private static final String SV_TYPE_A9 = "A9";
    /** 日付: 2018/04/01 */
    private static final String DATE_20180401 = "2018/04/01";
    /** 日付: 2021/04/01 */
    private static final String DATE_20210401 = "2021/04/01";
    /** 文字列:要支援 */
    public static final String STR_YOSHIEN = "要支援";
    /** 文字列:（見込み） */
    public static final String STR_MIKOMI = "（見込み）";
    /** 日付: 2006/04/01 */
    public static final String DATE_20060401 = "2006/04/01";
    /** 介護・予防サービス項目の取得のソート */
    private static final String COM_MHC_ITEMUSE_SORT = "sv_jigyo_id DESC";
    /** 文字列:fax: */
    public static final String STR_FAX = "fax:";
    /** 文字列:tel: */
    public static final String STR_TEL = "tel:";
    /** 改行コード（CR） */
    private static final String CARRIAGE_RETURN = "\r";
    /** ComTucKaigoNinteiSelectMapper: 介護認定情報取得 */
    @Autowired
    private ComTucKaigoNinteiSelectMapper comTucKaigoNinteiSelectMapper;
    /** CpnYokaip3SelectMapper: 認定調査票情報取得 */
    @Autowired
    private CpnYokaip3SelectMapper cpnYokaip3SelectMapper;
    /** CpnTucCks11SelectMapper: 計画書（１）情報取得 */
    @Autowired
    private CpnTucCks11SelectMapper cpnTucCks11SelectMapper;
    /** KghTucKrkKikanSelectMapper: 記録共通期間取得 */
    @Autowired
    private KghTucKrkKikanSelectMapper kghTucKrkKikanSelectMapper;
    /** ComMscYokaigoSelectMapper: 要介護状態マスタ（４－４）取得 */
    @Autowired
    private ComMscYokaigoSelectMapper comMscYokaigoSelectMapper;
    /** ComMhcItemuseSelectMapper: 介護サービス費適用正式名称取得する */
    @Autowired
    private ComMhcItemuseSelectMapper comMhcItemuseSelectMapper;
    /** 介護サービス費マスタ（２０－３）情報取得 */
    @Autowired
    private ComMhcItemSelectMapper comMhcItemSelectMapper;
    /** NdsMidaLogicクラス */
    @Autowired
    private NdsMidaLogic ndsMidaLogic;
    /** サービス事業者マスタ情報取得 */
    @Autowired
    private ComMscSvjigyoSelectMapper comMscSvjigyoSelectMapper;
    /** サービス事業者マスタ情報取得 */
    @Autowired
    ComMscSvjigyoRirekiSelectMapper comMscSvjigyoRirekiSelectMapper;
    /** サービス種別IDを取得 */
    @Autowired
    private ComMscSvjigyoNameFirstSvKindCdSelectMapper comMscSvjigyoNameFirstSvKindCdSelectMapper;
    /** サービス種別IDを取得 */
    @Autowired
    private ComMhcTermSelectMapper comMhcTermSelectMapper;
    /** サービス種別IDを取得 */
    @Autowired
    private ComMhcTermTermidSelectMapper comMhcTermTermidSelectMapper;
    /** 総合事業サービス項目取得 */
    @Autowired
    private ComMhcItemuseSougouMapper comMhcItemuseSougouMapper;
    /** 介護・予防サービス項目取得 */
    @Autowired
    private ComMhcItemuseMapper comMhcItemuseMapper;
    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;
    /** 保険者名称情報を取得する。 */
    @Autowired
    private ComMscKaigoHokenjaSelectMapper comMscKaigoHokenjaSelectMapper;
    /** 20-20 介護予防・日常生活支援総合事業サービス費適用マスタ */
    @Autowired
    private ComMhcItemuseSougouSelectMapper comMhcItemuseSougouSelectMapper;
    /** 1-17.担当ケアマネ履歴管理テーブル */
    @Autowired
    private ComTucTantoSelectMapper comTucTantoSelectMapper;
    /** 利用者基本（1-6） */
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;
    /** 03-01 職員基本 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /**
     * 要介護度の比較を行う
     *
     * @param alMae 変更前要介護度（yokai_kbn）
     * @param alAto 変更後要介護度（yokai_kbn）
     * @return 1=変更後要介護度が大きい、0=要介護度が同じ、-1=変更前要介護度が大きい
     */
    public Integer diffYokaiKbn(Integer alMae, Integer alAto) {
        // 初期値設定
        Integer result = 0;
        Integer beforeLevel = Optional.ofNullable(alMae).orElse(0);
        Integer afterLevel = Optional.ofNullable(alAto).orElse(0);

        // 変更前要介護度の重み付け
        long beforeWeight = calculateCareWeight(beforeLevel);

        // 変更後要介護度の重み付け
        long afterWeight = calculateCareWeight(afterLevel);

        // 比較結果の判定
        if (afterWeight > beforeWeight) {
            result = 1; // 変更後要介護度が大きい
        } else if (afterWeight < beforeWeight) {
            result = -1; // 変更前要介護度が大きい
        } else {
            result = 0; // 同じ
        }

        return result;
    }

    /**
     * 要介護度の重み付け計算
     * 
     * @param level 要介護度
     * @return 重み付けされた値
     */
    private Integer calculateCareWeight(Integer level) {
        switch (level) {
        case 1: // 非該当
            return level;

        case 21: // 事業対象者
            return 23;

        case 2: // 経過的要介護
        case 3: // 要介護1
        case 4: // 要介護2
        case 5: // 要介護3
        case 6: // 要介護4
        case 7: // 要介護5
            Integer weight = level * 10;
            if (level == 2) {
                weight += 3;
            }
            return weight;

        default: // 要支援1・2
            return level * 2;
        }
    }

    /**
     * 支援事業所名を得る
     *
     * @param shienId 支援事業所ID
     * @return 作成された名（取得できない場合は空文字）
     */
    public String getShienNameRyaku(Integer shienId) {
        String result = "";
        ChikuSogoShienSentaMeishoByCriteriaInEntity criteria = new ChikuSogoShienSentaMeishoByCriteriaInEntity();
        criteria.setAlSvJigyoId(shienId);
        List<ChikuSogoShienSentaMeishoOutEntity> items = comMscSvjigyoSelectMapper
                .findChikuSogoShienSentaMeishoByCriteria(criteria);
        if (items != null) {
            result = items.stream().findFirst().map(ChikuSogoShienSentaMeishoOutEntity::getJigyoRyakuKnj).orElse("");
        }
        return result;
    }

    /**
     * 指定月内でscodeが有効かどうかを判定する
     *
     * @param alSvj 提供事業所id
     * @param asScd 有効かどうかの調査対象となる サービスコード
     * @param asYm  有効かどうかの調査対象となる提供年月（yyyy/mm）
     * @return 有効であれば 項目コード を返す。無効なら0を返す
     */
    public Integer getScodeInYm(Integer alSvj, String asScd, String asYm) {
        // 日付範囲の生成
        String lsYm1 = asYm + DEFAULT_START_DAY;
        String lsYm2 = asYm + DEFAULT_END_DAY;

        // 検索条件設定
        SvItemcodeByCriteriaInEntity params = new SvItemcodeByCriteriaInEntity();
        params.setAlSvj(CommonDtoUtil.objValToString(alSvj));
        params.setAsScd(asScd);
        params.setLsYm1(lsYm1);
        params.setLsYm2(lsYm2);

        // データベース検索実行
        List<SvItemcodeOutEntity> results = comMhcItemuseSelectMapper.findSvItemcodeByCriteria(params);
        if (CollectionUtils.isNotEmpty(results)) {
            String itemCode = results.getFirst().getItemcode();
            if (Objects.isNull(itemCode)) {
                return INVALID_ITEM_CODE;
            }
            // dbはintです。
            return Integer.parseInt(itemCode);
        }
        return INVALID_ITEM_CODE;
    }

    /**
     * nds_truncate : 指定小数点以下を切り捨てる事に特化
     * 
     * @param ad0 数値
     * @param ai0 小数点後保つの数量
     * @return 転換後の数値
     * <AUTHOR>
     */
    public Double ndsTruncate(Double ad0, Integer ai0) {
        // 入力値のnullチェック
        if (ad0 == null) {
            return null;
        }
        // ai0がnullの場合は0として扱う
        int digits = (ai0 == null) ? CommonConstants.INT_0 : ai0;
        // 整数処理（ai0 ≦ 0 の場合）
        if (digits <= CommonConstants.INT_0) {
            return (ad0 >= CommonConstants.INT_0) ? Math.floor(ad0) : Math.ceil(ad0);
        }
        // BigDecimalを使用した精密な小数点以下切り捨て処理
        return BigDecimal.valueOf(ad0).setScale(digits, RoundingMode.DOWN).doubleValue();
    }

    /**
     * nds_up_val : 絶対値での小数点以下切り上げ
     * 
     * @param ad0 切り上げ入力1
     * @param ai0 切り上げ入力2
     * @return 運行結果
     * <AUTHOR>
     */
    public Double getNdsUpVal(Double ad0, Integer ai0) {
        // 入力値のnullチェック
        if (ad0 == null) {
            return CommonConstants.DOUBLE_ZERO;
        }
        // ai0がnullの場合は0として扱う
        int digits = (ai0 == null) ? CommonConstants.INT_0 : Math.max(ai0, CommonConstants.INT_0);
        // ゼロの場合はそのまま返す
        if (ad0 == CommonConstants.DOUBLE_ZERO) {
            return CommonConstants.DOUBLE_ZERO;
        }
        // 絶対値切り上げ処理
        BigDecimal bd = BigDecimal.valueOf(ad0);
        // 切り上げモードの決定:
        // 正の数 → 正の無限大方向 (UP)
        // 負の数 → 負の無限大方向 (UP) で絶対値が増加
        RoundingMode mode = (ad0 > CommonConstants.INT_0) ? RoundingMode.UP : RoundingMode.UP;
        return bd.setScale(digits, mode).doubleValue();
    }

    /**
     * nds_round : 小数点以下を四捨五入に特化
     * 
     * @param ad0 特化前double値
     * @return 特化の後ろdouble値
     * <AUTHOR>
     */
    public Double ndsRound(Double ad0) {
        if (ad0 == null)
            return 0.0;
        if (Double.isNaN(ad0))
            return Double.NaN;
        if (Double.isInfinite(ad0))
            return ad0;
        return (double) Math.round(ad0);
    }

    /**
     * 指定した月内でsv_item_cdが有効かどうか
     *
     * @param alSvj  提供事業所id
     * @param alItem 有効かどうかの調査対象となる sv_item_cd
     * @param asYm   有効かどうかの調査対象となる提供年月（yyyy/mm）
     * @return 有効であれば 項目コード を返す。無効なら0を返す
     */
    public String getItemCodeInYm(Integer alSvj, Integer alItem, String asYm) {
        // 日付範囲の生成
        String lsYm1 = asYm + DEFAULT_START_DAY;
        String lsYm2 = asYm + DEFAULT_END_DAY;
        String ret = StringUtils.EMPTY;
        // 検索条件設定
        SvScodeByCriteriaInEntity params = new SvScodeByCriteriaInEntity();
        params.setAlSvj(CommonDtoUtil.objValToString(alSvj));
        params.setAlItm(CommonDtoUtil.objValToString(alItem));
        params.setLsYm1(lsYm1);
        params.setLsYm2(lsYm2);

        // データベース検索実行
        List<SvScodeOutEntity> results = comMhcItemuseSelectMapper.findSvScodeByCriteria(params);
        if (CollectionUtils.isNotEmpty(results)) {
            ret = results.getFirst().getScode();
        }
        if (Objects.isNull(ret)) {
            ret = StringUtils.EMPTY;
        }
        return ret;
    }

    /**
     * 短期療養itemcodeコンバーター
     * 
     * @param asFYm 複写元年月（yyyy/MM）
     * @param asTYm 複写先年月（yyyy/MM）
     * @param alSvj 対象のサービス提供事業所id
     * @param alItm 対象となる sv_item_cd
     * @return 短期療養itemcode
     */
    public long getTermid4TankiCnv(String asFYm, String asTYm, Integer alSvj, Integer alItm) {
        // 初期化処理
        String lsScode = getItemCodeInYm(alSvj, alItm, asTYm);

        // ステップ1: 複写先での有効性チェック
        if (StringUtils.isEmpty(lsScode)) {
            lsScode = fetchScodeFromDatabase(alItm);
            if (StringUtils.isEmpty(lsScode)) {
                return INVALID_ITEM_CODE;
            }
            return processScodeConversion(alSvj, asTYm, alItm, lsScode);
        }
        return alItm;
    }

    /**
     * 支援事業者名を取得する
     * 
     * @param svjigyoId 支援事業者ID
     * @return 事業者名 (取得できない場合は空文字列)
     */
    public String getSvjigyoName(Integer svjigyoId) {
        // 事業者名
        String jigyoName = StringUtils.EMPTY;
        // 検索条件の設定
        JigyoKnjByCriteriaInEntity criteria = new JigyoKnjByCriteriaInEntity();
        criteria.setAlShien(CommonDtoUtil.objValToString(svjigyoId));
        // 事業者名を取得
        List<JigyoKnjOutEntity> jigyoNameList = comMscSvjigyoSelectMapper.findJigyoKnjByCriteria(criteria);
        // NULLチェック
        if (CollectionUtils.isNotEmpty(jigyoNameList)) {
            jigyoName = jigyoNameList.getFirst().getJigyoKnj();
        }
        return StringUtils.defaultIfEmpty(jigyoName, StringUtils.EMPTY);
    }

    /**
     * com_mhc_itemからscode取得
     * 
     * @param itemCode itemcode
     * @return scode
     */
    private String fetchScodeFromDatabase(Integer itemCode) {
        ScodeByCriteriaInEntity params = new ScodeByCriteriaInEntity();
        params.setAlItm(CommonDtoUtil.objValToString(itemCode));
        params.setLlTerm(CommonDtoUtil.objValToString(TERM_ID_4));

        List<ScodeOutEntity> results = comMhcItemSelectMapper.findScodeByCriteria(params);
        return results.stream().findFirst().map(ScodeOutEntity::getScode).orElse(StringUtils.EMPTY);
    }

    /**
     * scode変換処理
     * 
     * @param alSvj   対象のサービス提供事業所id
     * @param asTYm   複写先年月（yyyy/MM）
     * @param alItm   対象となる sv_item_cd
     * @param lsScode scode
     * @return scode
     */
    private Integer processScodeConversion(Integer alSvj, String asTYm, Integer alItm, String lsScode) {
        Integer convertedItem = getScodeInYm(alSvj, lsScode, asTYm);

        if (convertedItem > 0) {
            return convertedItem;
        }
        return handleSpecialCases(alSvj, asTYm, lsScode);
    }

    /**
     * 特別なケース処理（診療所環境基準減算関連）
     * 
     * @param alSvj   対象のサービス提供事業所id
     * @param asTYm   複写先年月（yyyy/MM）
     * @param lsScode scode
     * @return scode
     */
    private Integer handleSpecialCases(Integer alSvj, String asTYm, String lsScode) {
        if (lsScode.startsWith(ENVIRONMENTAL_CRITERIA_PREFIX) || lsScode.startsWith(EQUIPMENT_CRITERIA_PREFIX)) {
            String sixthChar = ndsMidaLogic.fNdsMida(lsScode, 6, 1);
            String modifiedScode = lsScode.substring(0, 5)
                    + (sixthChar.equals(STRING_ZERO) ? REPLACEMENT_SUFFIX_100 : REPLACEMENT_SUFFIX_000);

            Integer newItemCode = getScodeInYm(alSvj, modifiedScode, asTYm);
            if (newItemCode > 0) {
                return newItemCode;
            }
        }
        return INVALID_ITEM_CODE;
    }

    /**
     * サービス項目を取得(居宅介護支援事業所のみ)
     * 
     * @param svjId    サービス事業者ID
     * @param itemCode 項目コード
     * @param date     日付（yyyy/MM/dd）
     * @return サービス項目
     */
    public String getItemItemnameKnj(Integer svjId, Integer itemCode, String date) {
        String itemName = null;
        String svType = null; // サービス種別ID
        Integer termId = null;

        ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity firstSvKindCriteria = new ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity();
        firstSvKindCriteria.setAlSvj(svjId);
        List<ComMscSvjigyoNameFirstSvKindCdOutEntity> firstSvKindList = comMscSvjigyoNameFirstSvKindCdSelectMapper
                .findComMscSvjigyoNameFirstSvKindCdByCriteria(firstSvKindCriteria);
        if (CollectionUtils.isNotEmpty(firstSvKindList) && firstSvKindList.getFirst() != null) {
            svType = firstSvKindList.getFirst().getSvKindCd();
        }

        if (svType == null) {
            svType = SV_TYPE_00;
        }

        if (!(svType.compareTo(SV_TYPE_81) >= 0 && svType.compareTo(SV_TYPE_99) <= 0)) {
            // サービス有効期間IDを取得
            if ((date.compareTo(DATE_20180401) >= 0 && (SV_TYPE_61.equals(svType) || SV_TYPE_65.equals(svType)))
                    || (date.compareTo(DATE_20210401) >= 0
                            && (SV_TYPE_A1.equals(svType) || SV_TYPE_A5.equals(svType)))) {
                ComMhcTermSvtype35TermidByCriteriaInEntity svtype35Criteria = new ComMhcTermSvtype35TermidByCriteriaInEntity();
                svtype35Criteria.setDateYmd(date);
                List<ComMhcTermSvtype35TermidOutEntity> svtype35TermidOutList = comMhcTermSelectMapper
                        .findComMhcTermSvtype35TermidByCriteria(svtype35Criteria);
                if (CollectionUtils.isNotEmpty(svtype35TermidOutList)) {
                    termId = svtype35TermidOutList.getFirst().getTermid();
                }
            } else {
                ComMhcTermTermidByCriteriaInEntity termidCriteria = new ComMhcTermTermidByCriteriaInEntity();
                termidCriteria.setAlSvj(svjId);
                termidCriteria.setAsDate(date);
                List<ComMhcTermTermidOutEntity> termidOutList = comMhcTermTermidSelectMapper
                        .findComMhcTermTermidByCriteria(termidCriteria);
                if (CollectionUtils.isNotEmpty(termidOutList)) {
                    termId = termidOutList.getFirst().getTermid();
                }
            }

            if (termId == null) {
                termId = 0;
            }
        } else {
            termId = 0;
        }

        // サービス項目を取得
        // サービス種類CDで処理を分岐
        if (svType == SV_TYPE_A1 || svType == SV_TYPE_A5 || svType == SV_TYPE_A9) {
            // 総合事業サービス
            ComMhcItemuseSougouCriteria criteria = new ComMhcItemuseSougouCriteria();
            criteria.createCriteria().andItemcodeEqualTo(itemCode).andTermidEqualTo(termId).andDelFlgEqualTo(0);
            List<ComMhcItemuseSougou> comMhcItemuseSougouList = comMhcItemuseSougouMapper.selectByCriteria(criteria);
            if (CollectionUtils.isNotEmpty(comMhcItemuseSougouList)) {
                itemName = comMhcItemuseSougouList.getFirst().getItemnameKnj();
            }
        } else {
            // 介護・予防サービス
            // 横だしサービス
            ComMhcItemuseCriteria criteria = new ComMhcItemuseCriteria();
            List<Integer> svJigyoIds = new ArrayList<>();
            svJigyoIds.add(0);
            svJigyoIds.add(svjId);
            criteria.createCriteria().andSvJigyoIdIn(svJigyoIds).andItemcodeEqualTo(itemCode).andTermidEqualTo(termId)
                    .andDelFlgEqualTo(0);
            // SORT
            criteria.setOrderByClause(COM_MHC_ITEMUSE_SORT);
            List<ComMhcItemuse> comMhcItemuseList = comMhcItemuseMapper.selectByCriteria(criteria);
            if (CollectionUtils.isNotEmpty(comMhcItemuseList)) {
                itemName = comMhcItemuseList.getFirst().getItemnameKnj();
            }
        }

        if (itemName == null) {
            itemName = StringUtils.EMPTY;
        }

        return itemName;
    }

    /**
     * 西暦→和暦変換
     * 
     * @param seireki とにかく正しくなっていることと"/"付きであること
     * @param mode    mode 0：0を0で返す, 1：0を空白で返す
     * @return 和暦文字列
     */
    public String getCmpS2wjEz(String seireki, Integer mode) {

        String gengo, wareki, warekiRtn;

        if (seireki == null || seireki.length() < 4) {
            // 文字列が４文字より小さかったら終了（空文字を返す）
            return "";
        }

        gengo = nds3GkFunc01Logic.stringDateWareki(seireki, "gg");
        switch (seireki.length()) {
        case 4:
            wareki = nds3GkFunc01Logic.stringDateWareki(seireki, "nn");
            break;
        case 7:
            wareki = nds3GkFunc01Logic.stringDateWareki(seireki, "nn年mm");
            break;
        case 10:
            wareki = nds3GkFunc01Logic.stringDateWareki(seireki, "nn年mm月dd日");
            break;
        default:
            return "";
        }
        if (mode == 1) { // 0を空白で
            if (wareki.charAt(0) == '0') {
                wareki = " " + wareki.substring(1);
            }
            if (wareki.length() >= 4 && wareki.charAt(3) == '0') {
                wareki = wareki.substring(0, 3) + " " + wareki.substring(4);
            }
            if (wareki.length() >= 7 && wareki.charAt(6) == '0') {
                wareki = wareki.substring(0, 6) + " " + wareki.substring(7);
            }
        }

        warekiRtn = gengo + wareki;

        return warekiRtn;
    }

    /**
     * 西暦→和暦変換
     * 
     * @param seireki とにかく正しくなっていることと"/"付きであること
     * @param mode    mode 0：0を0で返す, 1：0を空白で返す
     * @return 和暦文字列
     */
    public String getCmpS2wEz(String seireki, Integer mode) {

        String gengo, wareki, warekiRtn;

        if (seireki == null || seireki.length() < 4) {
            // 文字列が４文字より小さかったら終了（空文字を返す）
            return "";
        }

        gengo = nds3GkFunc01Logic.stringDateWareki(seireki, "G");
        switch (seireki.length()) {
        case 4:
            wareki = nds3GkFunc01Logic.stringDateWareki(seireki, "nn");
            break;
        case 7:
            wareki = nds3GkFunc01Logic.stringDateWareki(seireki, "nn/mm");
            break;
        case 10:
            wareki = nds3GkFunc01Logic.stringDateWareki(seireki, "nn/mm/dd");
            break;
        default:
            return "";
        }
        if (mode == 1) { // 0を空白で
            if (wareki.charAt(0) == '0') {
                wareki = " " + wareki.substring(1);
            }
            if (wareki.length() >= 4 && wareki.charAt(3) == '0') {
                wareki = wareki.substring(0, 3) + " " + wareki.substring(4);
            }
            if (wareki.length() >= 7 && wareki.charAt(6) == '0') {
                wareki = wareki.substring(0, 6) + " " + wareki.substring(7);
            }
        }

        warekiRtn = gengo + wareki;

        return warekiRtn;
    }

    /**
     * 指定バイト数ごと改行コードを挿入する（印刷用）
     * 
     * @param asSrc
     * @param alWidth
     * @return 改行後文字列
     */
    public String getCmpLtextNewLine(String asSrc, Integer alWidth) throws UnsupportedEncodingException {
        if (asSrc == null) {
            return "";
        }
        // 文字列リストを取得する。
        List<String> lines = calculateStringsPostLineBreaks(asSrc, alWidth);

        return String.join("\r", lines);
    }

    /**
     * 複数行のテキストかラムでのワードラップ他の表示をエミュレートし単一行文字配列に分解し、1行ごと改行する
     * 
     * @param asSrc
     * @param alWidth
     * @return 改行後文字列
     */
    public String getCmpLtextNewLine2(String asSrc, Integer alWidth) throws UnsupportedEncodingException {
        if (asSrc == null) {
            return "";
        }
        // 文字列リストを取得する。
        List<String> lines = calculateStringsPostLineBreaks(asSrc, alWidth);

        StringBuilder line = new StringBuilder();
        lines.forEach(item -> {
            line.append(item).append("\r");
        });
        return line.toString();

    }

    /**
     * 改行後文字列リストを取得する。
     * 
     * @param asSrc
     * @param alWidth
     * @return 改行後文字列
     */
    private List<String> calculateStringsPostLineBreaks(String asSrc, Integer alWidth)
            throws UnsupportedEncodingException {
        // 解析後行カウンタ
        Integer afterParseCounter = 0;
        List<String> resultChangedList = new ArrayList<>();
        // 行バッファ
        StringBuilder lsBuff = new StringBuilder();
        // 解析文字カウンタ
        Integer parseTextCounter = 1;
        // 文字列ポインタ（バイト）
        Integer textPointer = 1;
        // 半角ワード開始位置
        Integer wordStartPosition = 0;

        while (parseTextCounter <= asSrc.length()) {
            // 解析１文字
            String oneLetter = asSrc.substring((parseTextCounter - 1), parseTextCounter);

            // 文字が2バイト文字（漢字）であるかどうかを確認する
            if (isShiftJISLeadByte(oneLetter.charAt(0)) || lenA(oneLetter).equals(-1)) {
                // ２バイト漢字
                if (textPointer >= alWidth) {
                    afterParseCounter++;
                    resultChangedList.add(lsBuff.toString());
                    lsBuff = new StringBuilder();
                    textPointer = 1;
                }
                wordStartPosition = 0;
                lsBuff.append(oneLetter);
                textPointer += 2;
            } else {
                // 1バイト文字の処理
                switch (oneLetter) {
                case "\n", "\t", "\u000b", "\f", "\b" -> { // 制御文字
                    wordStartPosition = 0; // 半角ワード解除
                    break;

                }

                case "\r" -> { // 改行
                    afterParseCounter++;
                    resultChangedList.add(lsBuff.toString());
                    lsBuff = new StringBuilder();
                    textPointer = 1;
                    // 半角ワード解除
                    wordStartPosition = 0;
                    break;
                }

                // 半角空白
                case " " -> {
                    if (textPointer >= alWidth + 1) {
                        afterParseCounter++;
                        resultChangedList.add(lsBuff.toString());
                        lsBuff = new StringBuilder();
                        textPointer = 1;
                    } else {
                        textPointer++;
                        lsBuff.append(CommonConstants.BLANK_SPACE);
                    }
                    // 半角ワード解除
                    wordStartPosition = 0;
                    break;
                }
                // 半角文字
                default -> {
                    if (wordStartPosition == 0) {
                        // 半角ワード開始
                        wordStartPosition = textPointer;
                    }

                    // 最大幅＋１カラムのチェック
                    if (textPointer > alWidth) {
                        if (wordStartPosition == 1) {
                            // データを捨てる
                            textPointer++;
                        } else {
                            afterParseCounter++;
                            resultChangedList.add(leftA(lsBuff.toString(), wordStartPosition - 1));
                            lsBuff = new StringBuilder(midA(lsBuff.toString(), wordStartPosition));
                            textPointer = lsBuff.toString().getBytes(CommonConstants.Shift_JIS).length + 1;
                            wordStartPosition = 1;
                            textPointer++;
                            lsBuff.append(oneLetter);
                        }
                    } else {
                        textPointer++;
                        lsBuff.append(oneLetter);
                    }
                }
                }
            }
            parseTextCounter++;
        }

        if (lsBuff.length() > 0) {
            afterParseCounter++;
            resultChangedList.add(lsBuff.toString());
        }

        return resultChangedList;
    }

    /**
     * 複数行のテキストかラムでのワードラップ他の 表示をエミュレートし単一行文字配列に分解する
     * 
     * @param originalString    解析元文字列
     * @param columnWidth       カラム幅(半角)
     * @param resultChangedList 変換結果文字配列
     * @return 変換後の有効な行数
     * @throws UnsupportedEncodingException
     */
    public Pair<Integer, List<String>> cmpLtext2Rows(String originalString, Integer columnWidth)
            throws UnsupportedEncodingException {

        // 解析後行カウンタ
        Integer afterParseCounter = 0;
        List<String> resultChangedList = new ArrayList<>();
        // 行バッファ
        StringBuilder lsBuff = new StringBuilder();
        // 解析文字カウンタ
        Integer parseTextCounter = 1;
        // 文字列ポインタ（バイト）
        Integer textPointer = 1;
        // 半角ワード開始位置
        Integer wordStartPosition = 0;

        if (StringUtil.isNullOrEmpty(originalString)) {
            return Pair.of(CommonConstants.NUMBER_0, resultChangedList);
        }

        while (parseTextCounter <= originalString.length()) {

            // 解析１文字
            String oneLetter = originalString.substring((parseTextCounter - 1), parseTextCounter);

            // 特殊文字は２バイトとして扱う
            if (isShiftJISLeadByte(oneLetter.charAt(0)) || lenA(oneLetter).equals(-1)) {
                // ２バイト漢字
                if (textPointer >= columnWidth) {
                    afterParseCounter++;
                    resultChangedList.add(lsBuff.toString());
                    lsBuff = new StringBuilder();
                    textPointer = 1;
                }
                wordStartPosition = 0;
                lsBuff.append(oneLetter);
                textPointer += 2;
            } else {
                // １バイト文字
                switch (oneLetter) {
                case "\n", "\t", "\u000b", "\f", "\b" -> { // 制御文字
                    wordStartPosition = 0; // 半角ワード解除
                }
                case "\r" -> { // 改行
                    afterParseCounter++;
                    resultChangedList.add(lsBuff.toString());
                    lsBuff = new StringBuilder();
                    textPointer = 1;
                    // 半角ワード解除
                    wordStartPosition = 0;
                }
                // 半角空白
                case " " -> {
                    if (textPointer >= columnWidth + 1) {
                        afterParseCounter++;
                        resultChangedList.add(lsBuff.toString());
                        lsBuff = new StringBuilder();
                        textPointer = 1;
                    } else {
                        textPointer++;
                        lsBuff.append(CommonConstants.BLANK_SPACE);
                    }
                    // 半角ワード解除
                    wordStartPosition = 0;
                }
                // 半角文字
                default -> {
                    if (wordStartPosition == 0) {
                        // 半角ワード開始
                        wordStartPosition = textPointer;
                    }

                    // 最大幅＋１カラムのチェック
                    if (textPointer > columnWidth) {
                        if (wordStartPosition == 1) {
                            // データを捨てる
                            textPointer++;
                        } else {
                            afterParseCounter++;
                            resultChangedList.add(leftA(lsBuff.toString(), wordStartPosition - 1));
                            lsBuff = new StringBuilder(midA(lsBuff.toString(), wordStartPosition));
                            textPointer = lsBuff.toString().getBytes(CommonConstants.Shift_JIS).length + 1;
                            wordStartPosition = 1;
                            textPointer++;
                            lsBuff.append(oneLetter);
                        }
                    } else {
                        textPointer++;
                        lsBuff.append(oneLetter);
                    }
                }
                }
            }
            parseTextCounter++;
        }

        if (lsBuff.length() > 0) {
            afterParseCounter++;
            resultChangedList.add(lsBuff.toString());
        }

        return Pair.of(afterParseCounter, resultChangedList);
    }

    /**
     * 指定された文字が Shift_JIS エンコーディングにおいて 2バイト文字の先頭バイト（リードバイト）かどうかを判定します。
     *
     * Shift_JIS のリードバイトは以下の範囲です： 0x81～0x9F (129～159) 0xE0～0xFC (224～252)
     *
     * @param c 判定対象の文字
     * @return リードバイトなら true、それ以外は false
     */
    public static boolean isShiftJISLeadByte(char c) {
        try {
            byte[] bytes = String.valueOf(c).getBytes(CommonConstants.Shift_JIS);
            if (bytes.length == 0)
                return false;
            int b = bytes[0] & 0xFF;
            return (b >= 129 && b <= 159) || (b >= 224 && b <= 252);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * * 文字列を Shift_JIS エンコーディングでバイト配列に変換し、 先頭から指定したバイト数分だけ取り出して文字列に変換して返します。
     * 
     * @param str
     * @param byteLength
     * @return
     * @throws UnsupportedEncodingException
     */
    public String leftA(String str, int byteLength) throws UnsupportedEncodingException {
        if (str == null || byteLength <= 0)
            return "";

        byte[] bytes = str.getBytes(CommonConstants.Shift_JIS);
        if (bytes.length <= byteLength)
            return str;

        byte[] subBytes = new byte[byteLength];
        System.arraycopy(bytes, 0, subBytes, 0, byteLength);

        String result = new String(subBytes, CommonConstants.Shift_JIS);

        while (!result.equals(new String(result.getBytes(CommonConstants.Shift_JIS), CommonConstants.Shift_JIS))) {
            result = result.substring(0, result.length() - 1);
        }

        return result;
    }

    /**
     * 文字列を Shift_JIS エンコーディングでバイト配列に変換し、 指定したバイト位置から末尾までを取り出し、再び文字列に変換して返します。
     * 
     * @param asString
     * @param alPos
     * @return
     * @throws UnsupportedEncodingException
     */
    public String midA(String asString, int alPos) throws UnsupportedEncodingException {
        if (asString == null || asString.isEmpty() || alPos < 1) {
            return "";
        }

        byte[] bytes = asString.getBytes(CommonConstants.Shift_JIS);

        if (bytes.length > 0 && alPos > 1) {
            int leftLen = alPos - 1;
            if (leftLen > bytes.length) {
                leftLen = bytes.length;
            }
            byte[] leftBytes = new byte[leftLen];
            System.arraycopy(bytes, 0, leftBytes, 0, leftLen);

            String leftStr = new String(leftBytes, CommonConstants.Shift_JIS);
            byte[] leftStrBytes = leftStr.getBytes(CommonConstants.Shift_JIS);
            if (leftStrBytes.length != leftLen) {
                alPos = alPos - 1;
            }
        }

        int startIndex = alPos - 1;
        if (startIndex > bytes.length) {
            return "";
        }

        byte[] subBytes = new byte[bytes.length - startIndex];
        System.arraycopy(bytes, startIndex, subBytes, 0, subBytes.length);

        return new String(subBytes, CommonConstants.Shift_JIS);
    }

    /**
     * 文字列を Shift_JIS エンコーディングでバイト配列に変換し、 指定した開始バイト位置から終了バイト位置までの範囲を切り出して
     * 文字列に変換して返します。
     * 
     * @param asString
     * @param alPos
     * @param alLen
     * @return
     * @throws UnsupportedEncodingException
     */
    public String midA(String asString, int alPos, int alLen) throws UnsupportedEncodingException {
        if (asString == null || asString.isEmpty() || alPos < 1) {
            return "";
        }

        byte[] bytes = asString.getBytes(CommonConstants.Shift_JIS);

        // LenA(as_string) > 0 AND al_pos > 1 のチェック
        if (bytes.length > 0 && alPos > 1) {
            int leftLen = alPos - 1;
            if (leftLen > bytes.length) {
                leftLen = bytes.length;
            }
            byte[] leftBytes = new byte[leftLen];
            System.arraycopy(bytes, 0, leftBytes, 0, leftLen);

            // Shift_JISとしてデコードして再エンコードしたバイト長を比較し、
            // 部分的にマルチバイト文字を切っている場合は位置を1つ戻す
            String leftStr = new String(leftBytes, CommonConstants.Shift_JIS);
            byte[] leftStrBytes = leftStr.getBytes(CommonConstants.Shift_JIS);
            if (leftStrBytes.length != leftLen) {
                alPos = alPos - 1;
            }
        }

        int startIndex = alPos - 1;
        if (startIndex > bytes.length) {
            return "";
        }
        int endIndex = startIndex + alLen;
        if (endIndex > bytes.length) {
            endIndex = bytes.length;
        }
        byte[] subBytes = new byte[endIndex - startIndex];
        System.arraycopy(bytes, startIndex, subBytes, 0, endIndex - startIndex);

        // Shift_JISとして文字列に変換（末尾の不完全なマルチバイト文字は自動的に無視される）
        return new String(subBytes, CommonConstants.Shift_JIS);
    }

    /**
     * 指定した文字列を Shift_JIS エンコーディングでバイト配列に変換し、 そのバイト数を返します。変換できない文字（例えば絵文字など）が含まれる場合は
     * -1 を返します。
     * 
     * @param str
     * @return
     */
    public Integer lenA(String str) {
        try {
            byte[] bytes = str.getBytes(CommonConstants.Shift_JIS);
            return bytes.length;
        } catch (Exception e) {
            return -1;
        }
    }

    /**
     * プレビュー用元号判断関数
     * 
     * @param asDate 日付（西暦"/"付きであること）
     * @return 元号区分
     */
    public Integer getDspS2gen(String asDate) {
        Integer liRet = CommonConstants.INT_0;

        String lsWareki = nds3GkFunc01Logic.get2Warekij(asDate, CommonConstants.INT_1);
        if (StringUtils.isEmpty(lsWareki)) {
            return liRet;
        }
        // 最初の２バイトをとり、それが元号の場合、処理せず
        String lsGen = lsWareki.substring(0, 2);
        // 元号変換
        switch (lsGen) {
        case CommonConstants.STR_MEIJI:
            liRet = CommonConstants.INT_1;
            break;
        case CommonConstants.STR_TAISHO:
            liRet = CommonConstants.INT_2;
            break;
        case CommonConstants.STR_SHOWA:
            liRet = CommonConstants.INT_3;
            break;
        case CommonConstants.STR_HEISEI:
            liRet = CommonConstants.INT_4;
            break;
        default:
            // 年号マスタから取得した年号をグローバル変数に移送する
            GengouInfoExpandOutDto outDto = nds3GkFunc01Logic.gengouInfoExpand();
            /** 元号２ */
            List<String> gsGengou2List = outDto.getGsGengou2List();
            for (int i = 0; i < gsGengou2List.size(); i++) {
                if (lsGen.equals(gsGengou2List.get(i))) {
                    liRet = i;
                    break;
                }
            }
            break;
        }
        return liRet;
    }

    /**
     * 要介護度文字列を取得
     * 
     * @param svJigyoId 事業所ID
     * @param userId    利用者ID
     * @param joken     条件区分 (1-4)
     * @param sc1Id     期間ID
     * @param ymd       基準日 (yyyy/MM/dd形式)
     * @return 要介護度文字列 (取得失敗時は空文字)
     */
    public String getCmpYokai(Integer svjigyoId, Integer userId, Integer joken, Integer sc1Id, String ymd) {
        // 要介護見込みフラグ
        boolean mikomi = false;
        // 要介護度
        Integer yokaiKbn = null;
        // 要介護名称
        String yokaiKnj = "";
        // 開始日
        String startYmd = "";
        // 終了日
        String endYmd = "";

        // 条件に従ってデータ取得
        switch (joken) {
        case 1:
            // 介護認定情報Entity
            CpnYokaip1ByCriteriaInEntity cpnYokaip1ByCriteriaInEntity = new CpnYokaip1ByCriteriaInEntity();
            // 利用者ID
            cpnYokaip1ByCriteriaInEntity.setAlUserid(userId);
            // 認定有効日
            cpnYokaip1ByCriteriaInEntity.setAsYmd(ymd);
            // 介護認定情報取得
            List<CpnYokaip1OutEntity> cpnYokaip1OutList = this.comTucKaigoNinteiSelectMapper
                    .findCpnYokaip1ByCriteria(cpnYokaip1ByCriteriaInEntity);
            if (CollectionUtils.isNotEmpty(cpnYokaip1OutList)) {
                // 要介護度
                yokaiKbn = cpnYokaip1OutList.get(0).getYokaiKbn();
            }
            break;
        case 2:
            // 記録共通期間Entity
            KikanYmdByCriteriaInEntity kikanYmdByCriteriaInEntity = new KikanYmdByCriteriaInEntity();
            // 期間ID
            kikanYmdByCriteriaInEntity.setAlSc1Id(sc1Id);
            // 記録共通期間情報取得
            List<KikanYmdOutEntity> kikanYmdOutList = this.kghTucKrkKikanSelectMapper
                    .findKikanYmdByCriteria(kikanYmdByCriteriaInEntity);
            if (CollectionUtils.isNotEmpty(kikanYmdOutList)) {
                // 開始日
                startYmd = kikanYmdOutList.get(0).getStartYmd();
                // 終了日
                endYmd = kikanYmdOutList.get(0).getEndYmd();
                // 介護認定情報Entity
                CpnYokaip2ByCriteriaInEntity cpnYokaip2ByCriteriaInEntity = new CpnYokaip2ByCriteriaInEntity();
                // 利用者ID
                cpnYokaip2ByCriteriaInEntity.setAlUserid(userId);
                // 認定有効開始日表示用
                cpnYokaip2ByCriteriaInEntity.setAsStartYmd(startYmd);
                // 認定有効終了日表示用
                cpnYokaip2ByCriteriaInEntity.setAsEndYmd(endYmd);
                // 介護認定情報取得
                List<CpnYokaip2OutEntity> cpnYokaip2OutList = this.comTucKaigoNinteiSelectMapper
                        .findCpnYokaip2ByCriteria(cpnYokaip2ByCriteriaInEntity);
                if (CollectionUtils.isNotEmpty(cpnYokaip2OutList)) {
                    // 要介護度
                    yokaiKbn = cpnYokaip2OutList.get(0).getYokaiKbn();
                }
            }
            break;
        case 3:
            // 認定調査票Entity
            CpnYokaip3ByCriteriaInEntity cpnYokaip3ByCriteriaInEntity = new CpnYokaip3ByCriteriaInEntity();
            // 事業者ID
            cpnYokaip3ByCriteriaInEntity.setAlSvjigyoId(svjigyoId);
            // 利用者ID
            cpnYokaip3ByCriteriaInEntity.setAlUserId(userId);
            // 実施日
            cpnYokaip3ByCriteriaInEntity.setAsYmd(ymd);
            // 認定調査票情報取得
            List<CpnYokaip3OutEntity> cpnYokaip3OutList = this.cpnYokaip3SelectMapper
                    .findCpnYokaip3ByCriteria(cpnYokaip3ByCriteriaInEntity);
            if (CollectionUtils.isNotEmpty(cpnYokaip3OutList)) {
                // 要介護度 = 要介護状態確定
                yokaiKbn = cpnYokaip3OutList.get(0).getYokaiKbn2();
                if (yokaiKbn == null || yokaiKbn == 0) {
                    // 要介護度 = 要介護状態見込み
                    yokaiKbn = cpnYokaip3OutList.get(0).getYokaiKbn1();
                    // 要介護見込みフラグ
                    mikomi = true;
                }
            }
            break;
        case 4:
            // 計画書（１）Entity
            CpncCks11ByCriteriaInEntity cpncCks11ByCriteriaInEntity = new CpncCks11ByCriteriaInEntity();
            // 計画期間ID
            cpncCks11ByCriteriaInEntity.setAlSc1Id(sc1Id);
            // 事業者ID
            cpncCks11ByCriteriaInEntity.setAlSvJigyoId(svjigyoId);
            // 利用者ID
            cpncCks11ByCriteriaInEntity.setAlUserId(userId);
            // 計画書（１）情報取得
            List<CpncCks11OutEntity> cpncCks11OutList = this.cpnTucCks11SelectMapper
                    .findCpncCks11ByCriteria(cpncCks11ByCriteriaInEntity);
            if (CollectionUtils.isNotEmpty(cpncCks11OutList)) {
                // 要介護度
                yokaiKbn = cpncCks11OutList.get(0).getYokaiKbn();
                // 要介護見込みフラグ
                mikomi = cpncCks11OutList.get(0).getMikomiFlg() == ReportConstants.MIKOMI_FLG_TRUE;
            }
            break;
        default:
            return "";
        }

        if (yokaiKbn == null || yokaiKbn == 0) {
            return "";
        }

        // 要介護状態Entity
        YokaigoJotaiByCriteriaInEntity yokaKnjByCriteriaInEntity = new YokaigoJotaiByCriteriaInEntity();
        // 要介護状態区分
        yokaKnjByCriteriaInEntity.setAiYokai(CommonDtoUtil.objValToString(yokaiKbn));
        // 要介護状態取得
        List<YokaigoJotaiOutEntity> yokaKnjOutEntity = this.comMscYokaigoSelectMapper
                .findYokaigoJotaiByCriteria(yokaKnjByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(yokaKnjOutEntity)) {
            // 要介護名称
            yokaiKnj = yokaKnjOutEntity.get(0).getYokaiKnj();
            if (yokaiKnj == null) {
                // 要介護名称 = ""
                yokaiKnj = "";
            }
        }

        // 2006/04/01以前の処理
        if (ReportConstants.YOKAI_KNJ_2.equals(yokaiKnj) && ymd.compareTo(DATE_20060401) < 0) {
            yokaiKnj = STR_YOSHIEN;
        }

        // 見込み処理
        if (mikomi && !yokaiKnj.isEmpty()) {
            yokaiKnj += STR_MIKOMI;
        }

        return yokaiKnj;
    }

    /**
     * 保険者名を得る
     *
     * @param kHokenCd 保険コード
     * @return 保険者名
     */
    public String getHokenjaName(Integer kHokenCd) {
        ComMscKaigoHokenjaDataByCriteriaInEntity comMscKaigoHokenjaDataByCriteriaInEntity = new ComMscKaigoHokenjaDataByCriteriaInEntity();
        // 保険者ID
        comMscKaigoHokenjaDataByCriteriaInEntity.setLlKHokenCd(CommonDtoUtil.objValToString(kHokenCd));
        // 介護保険者マスタ（４－３）取得情報
        List<ComMscKaigoHokenjaDataOutEntity> comMscKaigoHokenjaDataOutEntityList = this.comMscKaigoHokenjaSelectMapper
                .findComMscKaigoHokenjaDataByCriteria(comMscKaigoHokenjaDataByCriteriaInEntity);

        String kHokenKnj = CommonConstants.BLANK_STRING;
        if (!CollectionUtils.isNullOrEmpty(comMscKaigoHokenjaDataOutEntityList)) {
            if (Objects.nonNull(comMscKaigoHokenjaDataOutEntityList.get(0).getKHokenKnj())) {
                kHokenKnj = comMscKaigoHokenjaDataOutEntityList.get(0).getKHokenKnj();
            }
        }

        return kHokenKnj;
    }

    /**
     * 支援事業者FAX番号を得る
     *
     * @param alShien 支援事業者
     * @return 連絡先
     */
    public String cmpShienFax(Integer alShien) {
        TelFaxByCriteriaInEntity telFaxByCriteriaInEntity = new TelFaxByCriteriaInEntity();
        telFaxByCriteriaInEntity.setLlSvJigyoId(CommonDtoUtil.objValToString(alShien));
        // サービス事業者マスタ情報取得
        List<TelFaxOutEntity> telFaxOutList = comMscSvjigyoSelectMapper.findTelFaxByCriteria(telFaxByCriteriaInEntity);

        String lsWork = StringUtils.EMPTY;
        if (!CollectionUtils.isEmpty(telFaxOutList) && StringUtils.isNotEmpty(telFaxOutList.getFirst().getFax())) {
            lsWork = telFaxOutList.getFirst().getFax();
        }
        // 連絡先
        String lsjsyo = STR_FAX + lsWork;

        return lsjsyo;
    }

    /**
     * 支援事業者電話番号を得る
     *
     * @param alShien 支援事業者
     * @return 連絡先
     */
    public String cmpShienTel(Integer alShien) {
        TelFaxByCriteriaInEntity telFaxByCriteriaInEntity = new TelFaxByCriteriaInEntity();
        telFaxByCriteriaInEntity.setLlSvJigyoId(CommonDtoUtil.objValToString(alShien));
        // サービス事業者マスタ情報取得
        List<TelFaxOutEntity> telFaxOutList = comMscSvjigyoSelectMapper.findTelFaxByCriteria(telFaxByCriteriaInEntity);

        String lsWork = StringUtils.EMPTY;
        if (!CollectionUtils.isEmpty(telFaxOutList) && StringUtils.isNotEmpty(telFaxOutList.getFirst().getFax())) {
            lsWork = telFaxOutList.getFirst().getTel();
        }
        // 連絡先
        String lsjsyo = STR_TEL + lsWork;

        return lsjsyo;
    }

    /**
     * f_cmp_issunday_s : 休日を判定する
     * 
     * @param asDate 対象年月日(yyyy/mm/dd)
     * @param aiType どのレベルの休日を返させるか １ : 日曜日だけ ２ : 祝祭日のみ （予定） ３ : 日曜と祝祭日 （予定） ４ :
     *               業務休業日のみ（予定） ５ : 日曜と休業日 （予定） ６ : 祝祭日と休業日（予定） ７ : 日曜・祝祭日・休業日（予定）
     *               （イメージは３ビットマスク : 001(2), 010(2), 011(2)... ）
     * @return TRUE : 休 日、FALSE : 平 常
     * <AUTHOR>
     */
    public boolean chkIssundayS(String asDate, Integer aiType) {
        // Caution! : ここで内部的に yyyymmdd となる : Caution!
        String retDate = nds3GkFunc01Logic.getChangeSeireki(asDate);
        if (StringUtils.isEmpty(retDate)) {
            return false;
        }
        // 日曜日確認 by ai_type
        if (aiType == CommonConstants.NUMBER_1 || aiType == CommonConstants.NUMBER_3
                || aiType == CommonConstants.NUMBER_7) {
            Integer liRst = nds3GkFunc01Logic.getWeekDay(asDate);
            if (liRst != null && liRst == 0) {
                // 日曜だったら、TRUE。
                return true;
            }
        }
        return false;
    }

    /**
     * f_cmp_get_item_formalname2 : サービス項目を取得
     * 
     * @param alSvj  サービス事業者ID
     * @param alItm  項目コード
     * @param asDate 日付（yyyy/MM/dd）
     * @return なまえ
     * <AUTHOR>
     */
    public String getItemFormalname2(Integer alSvj, Integer alItm, String asDate) {
        String lsF = StringUtils.EMPTY;
        String lsSvtype = StringUtils.EMPTY;
        Integer llTermid = this
                .getTermidEasy(StringUtils.substring(asDate, CommonConstants.NUMBER_0, CommonConstants.NUMBER_7));
        ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity firstSvKindCriteria = new ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity();
        // サービス事業者ID
        firstSvKindCriteria.setAlSvj(alSvj);
        // DAOを実行
        List<ComMscSvjigyoNameFirstSvKindCdOutEntity> firstSvKindList = comMscSvjigyoNameFirstSvKindCdSelectMapper
                .findComMscSvjigyoNameFirstSvKindCdByCriteria(firstSvKindCriteria);
        // チェック
        if (!CollectionUtils.isNullOrEmpty(firstSvKindList)) {
            lsSvtype = firstSvKindList.getFirst().getSvKindCd();
            if (lsSvtype == null) {
                lsSvtype = CommonConstants.SV_TYPE_00;
            }
        }
        // サービス種類CDで処理を分岐
        if (CommonConstants.SV_KIND_CD_SOUKOU.contains(lsSvtype)) {
            // 総合事業サービス
            SeishoMeiByCriteriaInEntity seishoMeiByCriteriaInEntity = new SeishoMeiByCriteriaInEntity();
            // 項目コード
            seishoMeiByCriteriaInEntity.setAlItm(CommonDtoUtil.objValToString(alItm));
            // 日付（yyyy/MM/dd）
            seishoMeiByCriteriaInEntity.setAsDate(asDate);
            // DAOを実行
            List<SeishoMeiOutEntity> seishoMeiList = comMhcItemuseSougouSelectMapper
                    .findSeishoMeiByCriteria(seishoMeiByCriteriaInEntity);
            // チェック
            if (!CollectionUtils.isNullOrEmpty(seishoMeiList)) {
                lsF = seishoMeiList.getFirst().getFormalnameKnj();
            }
        } else {
            // 介護・予防サービス
            // 横だしサービス
            KaigoSaabisu2ByCriteriaInEntity kaigoSaabisu2ByCriteriaInEntity = new KaigoSaabisu2ByCriteriaInEntity();
            // 有効期間ID
            kaigoSaabisu2ByCriteriaInEntity.setLlTermidList(Arrays.asList(llTermid));
            // 項目コード
            kaigoSaabisu2ByCriteriaInEntity.setAlItm(alItm);
            // サービス事業者ID
            kaigoSaabisu2ByCriteriaInEntity.setAlSvjList(Arrays.asList(alSvj));
            // 日付（yyyy/MM/dd）
            kaigoSaabisu2ByCriteriaInEntity.setAsDate(asDate);
            // DAOを実行
            List<KaigoSaabisu2OutEntity> kaigoSaabisu2List = comMhcItemuseSelectMapper
                    .findKaigoSaabisu2ByCriteria(kaigoSaabisu2ByCriteriaInEntity);
            // チェック
            if (!CollectionUtils.isNullOrEmpty(kaigoSaabisu2List)) {
                lsF = kaigoSaabisu2List.getFirst().getFormalnameKnj();
            }
        }
        if (lsF == null) {
            lsF = StringUtils.EMPTY;
        }
        return lsF;
    }

    /**
     * 年月から termid を取得する（簡易版）.
     * 
     * @param ym 対象の年月 (形式: "YYYY/MM")
     * @return termid
     */
    public Integer getTermidEasy(String ym) {
        // 戻り値初期化
        Integer termId = TERM_ID_15;

        // 入力チェック
        if (StringUtils.isEmpty(ym)) {
            return termId;
        }

        // 期間判定
        if (ym.compareTo(BOUNDARY_2003_03) <= 0) {
            termId = TERM_ID_1;
        } else if (ym.compareTo(BOUNDARY_2005_09) <= 0) {
            termId = TERM_ID_2;
        } else if (ym.compareTo(BOUNDARY_2006_03) <= 0) {
            termId = TERM_ID_3;
        } else if (ym.compareTo(BOUNDARY_2009_03) <= 0) {
            termId = TERM_ID_4;
        } else if (ym.compareTo(BOUNDARY_2012_03) <= 0) {
            termId = TERM_ID_5;
        } else if (ym.compareTo(BOUNDARY_2014_03) <= 0) {
            termId = TERM_ID_6;
        } else if (ym.compareTo(BOUNDARY_2015_03) <= 0) {
            termId = TERM_ID_7;
        } else if (ym.compareTo(BOUNDARY_2017_03) <= 0) {
            termId = TERM_ID_8;
        } else if (ym.compareTo(BOUNDARY_2018_03) <= 0) {
            termId = TERM_ID_9;
        } else if (ym.compareTo(BOUNDARY_2019_09) <= 0) {
            termId = TERM_ID_10;
        } else if (ym.compareTo(BOUNDARY_2021_03) <= 0) {
            termId = TERM_ID_11;
        } else if (ym.compareTo(BOUNDARY_2022_09) <= 0) {
            termId = TERM_ID_12;
        } else if (ym.compareTo(BOUNDARY_2024_03) <= 0) {
            termId = TERM_ID_13;
        } else if (ym.compareTo(BOUNDARY_2024_05) <= 0) {
            termId = TERM_ID_14;
        }
        return termId;
    }

    /**
     * 文章中の改行コードを取り除く
     * 
     * @param text 入力文字列
     * @return 改行除去後の文字列
     */
    public String cnvkaigyo(String text) {
        if (StringUtils.isEmpty(text)) {
            return text;
        }

        String result = StringUtils.replace(text, CommonConstants.NEWLINE_CHARACTOR, CommonConstants.BLANK_STRING);
        result = StringUtils.replace(result, CARRIAGE_RETURN, CommonConstants.BLANK_STRING);
        return result;
    }

    /**
     * 年月計算（簡易版）.
     * 
     * @param asYymm yyyymmdd
     * @param asOp   操作 + or -
     * @param asNn   nnヶ月
     * @return 計算済み年月
     */
    public String calcYymmEasy(String asYymm, String asOp, Integer asNn) {
        int yy, mm, tmp;
        String result;

        if (asOp.equals(CommonConstants.AS_OP_ADD)) {
            yy = Integer.parseInt(asYymm.substring(0, 4));
            mm = Integer.parseInt(asYymm.substring(4, 6));

            mm += asNn;
            yy += (mm / 12);
            mm = mm % 12;

            if (mm == 0) {
                yy--;
                mm = 12;
            }

            result = String.format(CommonConstants.FORMAT_04D_02D, yy, mm);

        } else if (asOp.equals(CommonConstants.AS_OP_SUB)) {
            yy = Integer.parseInt(asYymm.substring(0, 4));
            mm = Integer.parseInt(asYymm.substring(4, 6));

            if (mm >= asNn) {
                mm -= asNn;
            } else {
                tmp = asNn / 12;
                yy -= tmp;
                int remainder = asNn % 12;

                if (mm > remainder) {
                    mm -= remainder;
                } else {
                    yy--;
                    mm = (12 + mm) - remainder;
                }
            }

            if (mm == 0) {
                yy--;
                mm = 12;
            }

            result = String.format(CommonConstants.FORMAT_04D_02D, yy, mm);

        } else {
            result = asYymm;
        }

        return result;
    }

    /**
     * f_cmp_shien_number : 支援事業者名を得る
     * 
     * @param alShien サービス事業者ID
     * @return 作成された名
     * <AUTHOR>
     */
    public String getCmpShienNumber(Integer alSvj) {
        String lsWork = StringUtils.EMPTY;
        JigyoCdNumberByCriteriaInEntity jigyoCdNumberByCriteriaInEntity = new JigyoCdNumberByCriteriaInEntity();
        // サービス事業者ID
        jigyoCdNumberByCriteriaInEntity.setIlSvJigyoId(alSvj);
        // DAOを実行
        List<JigyoCdNumberOutEntity> jigyoCdNumberList = comMscSvjigyoSelectMapper
                .findJigyoCdNumberByCriteria(jigyoCdNumberByCriteriaInEntity);
        // チェック
        if (CollectionUtils.isNotEmpty(jigyoCdNumberList)) {
            lsWork = jigyoCdNumberList.getFirst().getJigyoNumber();
            if (lsWork == null) {
                lsWork = StringUtils.EMPTY;
            } else {
                if (lsWork.length() < CommonConstants.INT_10) {
                    lsWork = CommonConstants.STR_0.repeat(CommonConstants.INT_10 - lsWork.length()) + lsWork;
                }
            }
        }
        return lsWork;
    }

    /**
     * f_cmp_get_item_formalname_30 : サービス項目を取得＋３０日超!?
     * 
     * @param alSvj  サービス事業者ID
     * @param alItm  項目コード
     * @param asDate 日付（yyyy/MM/dd）
     * @param ai30   ３０日超過か！？
     * @return なまえ
     * <AUTHOR>
     */
    public String getItemFormalname30(Integer alSvj, Integer alItm, String asDate, Integer ai30) {
        String lsF = getItemFormalname2(alSvj, alItm, asDate);
        if (ai30 == 1) {
            lsF = CommonConstants.STR_THIRDTH_DAYS_MORE + lsF;
        }
        return lsF;
    }

    /**
     * f_cmp_get_tanto_name_from_userid : 利用者idから担当ケアマネの名前を取得する
     * 
     * @param alUser  利用者id
     * @param asYmdSt 検索期間・開始日
     * @param asYmdEd 検索期間・終了日
     * @return 担当ケアマネの名前
     * <AUTHOR>
     */
    public String getCmpTantoNameFromUserid(Integer alUser, String asYmdSt, String asYmdEd) {
        Integer llTanto = CommonConstants.INT_0;
        TantoIdByCriteriaInEntity tantoIdByCriteriaInEntity = new TantoIdByCriteriaInEntity();
        // 利用者id
        tantoIdByCriteriaInEntity.setAlUser(alUser);
        // 検索期間・開始日
        tantoIdByCriteriaInEntity.setAsYmdSt(asYmdSt);
        // 検索期間・終了日
        tantoIdByCriteriaInEntity.setAsYmdEd(asYmdEd);
        // ①担当ｹｱﾏﾈ履歴管理テーブルを検索
        List<TantoIdOutEntity> tantoIdList = comTucTantoSelectMapper.findTantoIdByCriteria(tantoIdByCriteriaInEntity);
        // チェック
        if (CollectionUtils.isNotEmpty(tantoIdList)) {
            llTanto = tantoIdList.getFirst().getTantoId();
            if (llTanto == null) {
                llTanto = CommonConstants.INT_0;
            }
        }
        if (llTanto == null || llTanto <= CommonConstants.INT_0) {
            // 履歴が無いなら、
            // ②利用者情報テーブルの担当ｹｱﾏﾈIDを検索
            TantouIdUserByCriteriaInEntity tantouIdUserByCriteriaInEntity = new TantouIdUserByCriteriaInEntity();
            tantouIdUserByCriteriaInEntity.setLlUserid(alUser);
            List<TantouIdUserOutEntity> tantouIdUserList = comTucUserSelectMapper
                    .findTantouIdUserByCriteria(tantouIdUserByCriteriaInEntity);
            // チェック
            if (CollectionUtils.isNotEmpty(tantouIdUserList)) {
                llTanto = tantouIdUserList.getFirst().getTantoId();
                if (llTanto == null) {
                    llTanto = CommonConstants.INT_0;
                }
            }
        }
        String lsName = StringUtils.EMPTY;
        if (llTanto > CommonConstants.INT_0) {
            lsName = getCmpTantoName(llTanto);
        }
        return lsName;
    }

    /**
     * f_cmp_shien_name_ryaku2 : 支援事業者名を得る
     * 
     * @param alShien サービス事業者ID
     * @return 戻り値 String 作成された名
     * <AUTHOR>
     */
    public String getCmpShienNameRyaku2A(Integer alShien) {
        ChikuSogoShienSentaMeishoByCriteriaInEntity jigyoRyakuKnj2ByCriteriaInEntity = new ChikuSogoShienSentaMeishoByCriteriaInEntity();
        jigyoRyakuKnj2ByCriteriaInEntity.setAlSvJigyoId(alShien);
        List<ChikuSogoShienSentaMeishoOutEntity> jigyoRyakuKnj2List = comMscSvjigyoSelectMapper
                .findChikuSogoShienSentaMeishoByCriteria(jigyoRyakuKnj2ByCriteriaInEntity);
        if (jigyoRyakuKnj2List.size() > CommonConstants.NUMBER_0) {
            ChikuSogoShienSentaMeishoOutEntity obj = jigyoRyakuKnj2List.get(CommonConstants.NUMBER_0);
            if (StringUtils.isEmpty(obj.getJigyoRyakuKnj())) {
                return StringUtils.EMPTY;
            }
            return obj.getJigyoRyakuKnj();
        }
        return StringUtils.EMPTY;
    }

    /**
     * f_cmp_shien_name_ryaku2 : 支援事業者名を得る
     * 
     * @param alShien    サービス事業者ID
     * @param asStartYmd 開始日付
     * @param asEndYmd   終了日付
     * @return 戻り値 String 作成された名
     * <AUTHOR>
     */
    public String getCmpShienNameRyaku2B(Integer alShien, String asStartYmd, String asEndYmd) {
        JigyoRyakuKnjRirekiByCriteriaInEntity jigyoRyakuKnjRirekiByCriteriaInEntity = new JigyoRyakuKnjRirekiByCriteriaInEntity();
        jigyoRyakuKnjRirekiByCriteriaInEntity.setAlShien(CommonDtoUtil.objValToString(alShien));
        jigyoRyakuKnjRirekiByCriteriaInEntity.setAsStartYmd(asStartYmd);
        jigyoRyakuKnjRirekiByCriteriaInEntity.setAsEndYmd(asEndYmd);
        List<JigyoRyakuKnjRirekiOutEntity> jigyoRyakuKnjRirekiList = comMscSvjigyoRirekiSelectMapper
                .findJigyoRyakuKnjRirekiByCriteria(jigyoRyakuKnjRirekiByCriteriaInEntity);
        if (jigyoRyakuKnjRirekiList.size() > CommonConstants.NUMBER_0) {
            JigyoRyakuKnjRirekiOutEntity obj = jigyoRyakuKnjRirekiList.get(CommonConstants.NUMBER_0);
            if (StringUtils.isEmpty(obj.getJigyoRyakuKnj())) {
                return StringUtils.EMPTY;
            }
            return obj.getJigyoRyakuKnj();

        } else {
            ChikuSogoShienSentaMeishoByCriteriaInEntity jigyoRyakuKnj2ByCriteriaInEntity = new ChikuSogoShienSentaMeishoByCriteriaInEntity();
            jigyoRyakuKnj2ByCriteriaInEntity.setAlSvJigyoId(alShien);
            List<ChikuSogoShienSentaMeishoOutEntity> jigyoRyakuKnj2List = comMscSvjigyoSelectMapper
                    .findChikuSogoShienSentaMeishoByCriteria(jigyoRyakuKnj2ByCriteriaInEntity);
            if (jigyoRyakuKnj2List.size() > CommonConstants.NUMBER_0) {
                ChikuSogoShienSentaMeishoOutEntity obj = jigyoRyakuKnj2List.get(CommonConstants.NUMBER_0);
                if (StringUtils.isEmpty(obj.getJigyoRyakuKnj())) {
                    return StringUtils.EMPTY;
                }
                return obj.getJigyoRyakuKnj();
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * f_cmp_get_tanto_name : 担当ケアマネの名前を取得する
     * 
     * @param alTanto 担当ケアマネID
     * @return 担当ケアマネの名前
     * <AUTHOR>
     */
    public String getCmpTantoName(Integer alTanto) {
        String lsN1 = StringUtils.EMPTY;
        String lsN2 = StringUtils.EMPTY;
        ShokuinFullNameByCriteriaInEntity comMscShokuinTantoByCriteriaInEntity = new ShokuinFullNameByCriteriaInEntity();
        // 担当ケアマネID
        comMscShokuinTantoByCriteriaInEntity.setAlShoku(CommonDtoUtil.objValToString(alTanto));
        // DAOを実行
        List<ShokuinFullNameOutEntity> comMscShokuinTantoList = comMscShokuinSelectMapper
                .findShokuinFullNameByCriteria(comMscShokuinTantoByCriteriaInEntity);
        // チェック
        if (CollectionUtils.isNotEmpty(comMscShokuinTantoList)) {
            lsN1 = comMscShokuinTantoList.getFirst().getShokuin1Knj();
            lsN2 = comMscShokuinTantoList.getFirst().getShokuin2Knj();
            if (lsN1 == null) {
                lsN1 = StringUtils.EMPTY;
            }
            if (lsN2 == null) {
                lsN2 = StringUtils.EMPTY;
            }
        }
        String lsName = StringUtils.EMPTY;
        if (StringUtils.isNotEmpty(lsN1) && StringUtils.isNotEmpty(lsN2)) {
            lsName = lsN1 + CommonConstants.BLANK_SPACE + lsN2;
        } else {
            lsName = lsN1 + lsN2;
        }
        return lsName;
    }

    /**
     * f_cmp_get_user_name : 利用者名を得る.
     * 
     * @param alUid 利用ID
     * @return 作成された名
     * <AUTHOR>
     */
    public String getUserName(Integer alUid) {
        // 検索条件
        KghKrkJikoHoukokuOpe2ByCriteriaInEntity kghKrkJikoHoukokuOpe2ByCriteriaInEntity = new KghKrkJikoHoukokuOpe2ByCriteriaInEntity();
        // 利用ID
        kghKrkJikoHoukokuOpe2ByCriteriaInEntity.setId(alUid);
        // 利用者名
        List<KghKrkJikoHoukokuOpe2OutEntity> kghKrkJikoHoukokuOpe2OutList = comTucUserSelectMapper
                .findKghKrkJikoHoukokuOpe2ByCriteria(kghKrkJikoHoukokuOpe2ByCriteriaInEntity);
        String lsN1 = StringUtils.EMPTY;
        String lsN2 = StringUtils.EMPTY;
        if (CollectionUtils.isNotEmpty(kghKrkJikoHoukokuOpe2OutList)) {
            lsN1 = StringUtils.isEmpty(kghKrkJikoHoukokuOpe2OutList.getFirst().getName1Knj()) ? StringUtils.EMPTY
                    : kghKrkJikoHoukokuOpe2OutList.getFirst().getName1Knj();
            lsN2 = StringUtils.isEmpty(kghKrkJikoHoukokuOpe2OutList.getFirst().getName2Knj()) ? StringUtils.EMPTY
                    : kghKrkJikoHoukokuOpe2OutList.getFirst().getName2Knj();
        }
        return lsN1 + CommonConstants.BLANK_SPACE + lsN2;
    }

    /**
     * f_cmp_shien_name.srf : 支援事業所名を取得る
     *
     * @param alShien 支援事業所ID
     * @return 支援事業所名
     */
    public String getShienName(Integer alShien) {
        String lsWork = StringUtils.EMPTY;
        JigyoKnjByCriteriaInEntity entity = new JigyoKnjByCriteriaInEntity();
        entity.setAlShien(Integer.toString(alShien));
        List<JigyoKnjOutEntity> entityList = this.comMscSvjigyoSelectMapper.findJigyoKnjByCriteria(entity);
        if (CollectionUtils.isNotEmpty(entityList)) {
            lsWork = entityList.get(0).getJigyoKnj();
        }
        return lsWork;
    }
}
