package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.07.18
 * <AUTHOR>
 * @implNote GUI00815_印刷設定 初期情報取得サービス入力Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AssessmentHomePrintSettingsInitialUpdateServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 事業者ID */
    @NotEmpty
    private String svJigyoId;

    /** 利用者ID */
    private String userId;

    /** システム略称 */
    @NotEmpty
    private String sysRyaku;

    /** 法人ID */
    @NotEmpty
    private String houjinId;

    /** 施設ID */
    @NotEmpty
    private String shisetuId;

    /** メニュー２名称 */
    @NotEmpty
    private String menu2Knj;

    /** メニュー３名称 */
    @NotEmpty
    private String menu3Knj;

    /** セクション名 */
    @NotEmpty
    private String sectionName;

    /** システムコード */
    @NotEmpty
    private String gsysCd;

    /** 職員ID */
    @NotEmpty
    private String shokuId;

    /** インデックス */
    @NotEmpty
    private String index;

    /** 個人情報使用フラグ */
    @NotEmpty
    private String kojinhogoUsedFlg;

    /** 個人情報番号 */
    @NotEmpty
    private String sectionAddNo;

    /** 改訂範囲 */
    @NotEmpty
    private String revisionRange;

    /** 履歴選択フラグ */
    @NotEmpty
    private String historySelectFlag;

}
