package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jakarta.validation.Valid;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.05.12
 * <AUTHOR>
 * @description GUI00904_課題立案様式設定マスタ画面の初期情報取得出力Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class IssuesPlanningStyleSettingsMasterSelectServiceOutDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 課題立案様式名情報リスト */
    private List<Gui00904kghMocKrkFree11OutDto> kghMocKrkFree1TitleInfoList;

    /** 課題立案様式設定情報（画面生成用データ） */
    @Valid
    private Gui00904kghMocKrkFree12OutDto kghMocKrkFree1Info;

    /** 課題立案表データリスト */
    @Valid
    private List<Gui00904kghMocKrkFree2OutDto> kghMocKrkFree2InfoList;

    /** 使用フラグ */
    private String useFlg;
}
