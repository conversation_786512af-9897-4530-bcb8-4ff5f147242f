package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01211ChoPrt;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01211KHoken;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01211OutPrint;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01211Tanto;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkSetProfileInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.GetPrinterSettingsListInfoSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.GetPrinterSettingsListInfoSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.LedgerInitializeDataComSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.LedgerInitializeDataComSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PrintSettingsScreenInitialInfoSelectGUI01211ServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PrintSettingsScreenInitialInfoSelectGUI01211ServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucFaxSoufujoMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucFaxSoufujo;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnFaxSoufujo4DefaultByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnFaxSoufujo4DefaultOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocPrtiniByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocPrtiniOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocPrtiniSByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocPrtiniSOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghNumberComUserHokenjaFltByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghNumberComUserHokenjaFltOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinHogoJohoSetteiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinHogoJohoSetteiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinNameNumberByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinNameNumberOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucFaxSoufujoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocPrtiniSSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocPrtiniSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSysiniSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscKaigoHokenjaSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * API定義書_APINo(1208)_印刷設定画面初期
 * 
 * <AUTHOR>
 * 
 */
@Service
public class PrintSettingsScreenInitialInfoSelectGUI01211ServiceImpl extends
        SelectServiceImpl<PrintSettingsScreenInitialInfoSelectGUI01211ServiceInDto, PrintSettingsScreenInitialInfoSelectGUI01211ServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 52-01 （全体）設定情報テーブル情報取得 */
    @Autowired
    private ComMocSysiniSelectMapper comMocSysiniSelectMapper;
    @Autowired
    private ComMocPrtiniSelectMapper comMocPrtiniSelectMapper;
    @Autowired
    private CmnTucFaxSoufujoSelectMapper cmnTucFaxSoufujoSelectMapper;
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** 職員氏名及び番号情報取得 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    @Autowired
    private ComMscKaigoHokenjaSelectMapper comMscKaigoHokenjaSelectMapper;

    /** 印刷iniファイルデータ初期値テーブル情報取得 */
    @Autowired
    private ComMocPrtiniSSelectMapper comMocPrtiniSSelectMapper;

    /** 印刷iniファイルデータ初期値テーブル情報取得 */
    @Autowired
    private CmnTucFaxSoufujoMapper cmnTucFaxSoufujoMapper;

    /** API定義書_APINo(1209)_印刷設定画面一覧情報取得 */
    @Autowired
    private GetPrinterSettingsListInfoSelectServiceImpl getPrinterSettingsListInfoSelectServiceImpl;
    // API定義書_APINo(1009)_帳票イニシャライズデータ取得
    @Autowired
    private LedgerInitializeDataComSelectServiceImpl ledgerInitializeDataComSelectServiceImpl;

    /**
     * API定義書_APINo(1208)_印刷設定画面初期
     * 
     * @param inDto 印刷設定画面初期情報取得の入力DTO.
     * @return 印刷設定画面初期情報取得取得のOUT DTO
     * @throws Exception Exception
     */
    @Override
    protected PrintSettingsScreenInitialInfoSelectGUI01211ServiceOutDto mainProcess(
            PrintSettingsScreenInitialInfoSelectGUI01211ServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        PrintSettingsScreenInitialInfoSelectGUI01211ServiceOutDto outDto = new PrintSettingsScreenInitialInfoSelectGUI01211ServiceOutDto();
        LedgerInitializeDataComSelectServiceOutDto ledgerOutdto = new LedgerInitializeDataComSelectServiceOutDto();

        // 2. 画面制御フラグ
        // 2.1. 設定情報を取得する。
        List<KojinHogoJohoSetteiOutEntity> kojinHogoJohoSetteiList = comMocSysiniSelectMapper
                .findKojinHogoJohoSetteiByCriteria(new KojinHogoJohoSetteiByCriteriaInEntity());
        // 2.1.1. 上記OUTPUT情報.値= 1 の場合、
        if (CollectionUtils.isNotEmpty(kojinHogoJohoSetteiList)
                && CommonConstants.STR_1.equals(kojinHogoJohoSetteiList.getFirst().getNewParam())) {
            // レスポンスパラメータ.個人保護フラグ＝処理2.1.で取得した.値
            outDto.setKojinHogoFlg(kojinHogoJohoSetteiList.getFirst().getNewParam());
        } else {
            // 2.1.2. 上記以外の場合、
            // レスポンスパラメータ.伏字フラグ＝1
            outDto.setAmikakeFlg(CommonConstants.STR_1);
        }

        // 2.2. 文書番号フラグを設定する。
        // レスポンスパラメータ.文書番号フラグ＝0
        outDto.setMonjiNoFlg(CommonConstants.STR_0);

        // 3.印刷設定初期値情報の取得
        // 3.1.下記の24-04 印刷iniファイルデータ初期値テーブル情報取得のDAOを利用し、印刷iniファイルデータ初期値情報を取得する。
        ComMocPrtiniByCriteriaInEntity comMocPrtiniByCriteriaInEntity = new ComMocPrtiniByCriteriaInEntity();
        comMocPrtiniByCriteriaInEntity.setSysRyaku(inDto.getSysRyaku()); // リクエストパラメータ.システム略称
        comMocPrtiniByCriteriaInEntity.setSectionName(inDto.getSectionName()); // リクエストパラメータ.セクション名
        List<ComMocPrtiniOutEntity> comMocPrtiniList = comMocPrtiniSelectMapper
                .findComMocPrtiniByCriteria(comMocPrtiniByCriteriaInEntity);

        List<ComMocPrtiniOutEntity> entities = new ArrayList<ComMocPrtiniOutEntity>();

        // 3.2.印刷設定初期値情報リストの設定
        // 【変数】.セクション番号
        String sectionNo;
        List<Gui01211ChoPrt> choPrts = new ArrayList<Gui01211ChoPrt>();

        Gui01211ChoPrt gui01211ChoPrtDef = new Gui01211ChoPrt();
        // 3.2.「3.1.」で取得した場合
        if (comMocPrtiniList.size() != 0) {

            // 「3.1. 」で取得した出力帳票印刷情報リストはセクション番号がV00241或いはV00242の場合でフィルターする。
            entities = comMocPrtiniList.stream().filter(s -> CommonConstants.SECTION_V00241.equals(s.getSectionNo())
                    || CommonConstants.SECTION_V00242.equals(s.getSectionNo())).collect(Collectors.toList());
            // 変数.セクション番号 = 「2.1. 」で取得した出力帳票印刷情報リストの一件目.セクション番号
            sectionNo = entities.get(0).getSectionNo();

        } else {

            // 3.3.「3.1.」で取得できない場合、印刷設定情報を設定して出力帳票印刷情報リストに追加する。
            sectionNo = CommonConstants.SECTION_NO_INIT;
            gui01211ChoPrtDef.setChoPro(CommonConstants.PRO_FILE_NAME_INIT);
            gui01211ChoPrtDef.setDwobject(CommonConstants.BLANK_STRING);
            gui01211ChoPrtDef.setPrtOrient(CommonConstants.BLANK_STRING);
            gui01211ChoPrtDef.setPrtSize(CommonConstants.STR_0);
            gui01211ChoPrtDef.setPrtTitle(CommonConstants.PRO_FILE_NAME_INIT);
            gui01211ChoPrtDef.setPrndate(CommonConstants.STR_0);
            gui01211ChoPrtDef.setPrnshoku(CommonConstants.STR_0);
            gui01211ChoPrtDef.setSerialFlg(CommonConstants.STR_0);
            gui01211ChoPrtDef.setModFlg(CommonConstants.STR_0);
            gui01211ChoPrtDef.setSecFlg(CommonConstants.STR_0);
            gui01211ChoPrtDef.setSerialHeight(CommonConstants.STR_0);
            choPrts.add(gui01211ChoPrtDef);
            outDto.setChoPrtList(choPrts);
        }

        // 3.4.下記の印刷iniファイルデータ保存テーブル情報取得のDAOを利用し、印刷設定保存情報リストを取得する。
        ComMocPrtiniSByCriteriaInEntity prtiniSaveInfoByCriteriaInEntity = new ComMocPrtiniSByCriteriaInEntity();
        prtiniSaveInfoByCriteriaInEntity.setHid(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        prtiniSaveInfoByCriteriaInEntity.setSid(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        prtiniSaveInfoByCriteriaInEntity.setJid(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        prtiniSaveInfoByCriteriaInEntity.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
        prtiniSaveInfoByCriteriaInEntity.setSectionNo(sectionNo);
        List<ComMocPrtiniSOutEntity> saveEntites = comMocPrtiniSSelectMapper
                .findComMocPrtiniSByCriteria(prtiniSaveInfoByCriteriaInEntity);

        // 3.5.「3.1.」で取得できる場合、取得した出力帳票印刷情報リストの件数分、出力帳票印刷情報リストを設定する。
        for (int i = 1; i <= entities.size(); i++) {
            Gui01211ChoPrt gui01211ChoPrt = new Gui01211ChoPrt();
            ComMocPrtiniOutEntity comMocPrtiniInfo = entities.get(i - 1);

            gui01211ChoPrt.setPrtNo(CommonDtoUtil.objValToString(comMocPrtiniInfo.getPrtNo()));
            // 該当レコード.プロファイル＝リクエストパラメータ.システム略称 +該当レコード.セクション番号+ "P"＋該当レコード.帳票番号の3桁に0で前方補完
            gui01211ChoPrt.setChoPro(
                    this.setProfile(inDto.getSysRyaku(), comMocPrtiniInfo.getSectionNo(), comMocPrtiniInfo.getPrtNo()));

            // '3.5.1. 「3.4. 」で取得した印刷設定保存情報リストに帳票番号と該当レコード.帳票番号が一致するデータがある場合、
            int index = IntStream.range(0, saveEntites.size())
                    .filter(j -> comMocPrtiniInfo.getPrtNo().equals(saveEntites.get(j).getPrtNo())).findFirst()
                    .orElse(-1);

            if (index != -1) {
                ComMocPrtiniSOutEntity prtiniSaveInfo = saveEntites.get(index);
                // 印刷設定保存情報.帳票タイトルがある場合、印刷設定情報.帳票タイトル＝印刷設定保存情報.帳票タイトル
                if (!StringUtil.isEmpty(prtiniSaveInfo.getPrtTitle())) {
                    gui01211ChoPrt.setPrtTitle(prtiniSaveInfo.getPrtTitle());
                    gui01211ChoPrt.setDefPrtTitle(comMocPrtiniInfo.getDefPrtTitle());
                }

                // 日付表示有無
                if (prtiniSaveInfo.getPrndate() == null) {
                    // 印刷設定保存情報.日付表示有無がない場合、印刷設定情報.日付表示有無＝2
                    gui01211ChoPrt.setPrndate(CommonConstants.PRN_DATE_DESIGNATED);

                } else {
                    // 印刷設定保存情報.日付表示有無がある場合、印刷設定情報.日付表示有無＝印刷設定保存情報.日付表示有無
                    gui01211ChoPrt.setPrndate(CommonDtoUtil.objValToString(prtiniSaveInfo.getPrndate()));

                }

                // 職員表示有無
                if (prtiniSaveInfo.getPrnshoku() == null) {
                    // 印刷設定保存情報.職員表示有無がない場合、印刷設定情報.職員表示有無＝1
                    gui01211ChoPrt.setPrnshoku(CommonConstants.PRN_SHOKU_EXIST);

                } else {
                    // 印刷設定保存情報.職員表示有無がある場合、印刷設定情報.職員表示有無＝印刷設定保存情報.職員表示有無
                    gui01211ChoPrt.setPrnshoku(CommonDtoUtil.objValToString(prtiniSaveInfo.getPrnshoku()));

                }
                gui01211ChoPrt.setSectionNo(saveEntites.get(index).getSectionNo());

            }
            choPrts.add(gui01211ChoPrt);
            // レスポンスパラメータ.出力帳票印刷情報リストに出力帳票印刷情報リストを設定する
            outDto.setChoPrtList(choPrts);
        }

        // 【変数】.セクション
        String section = CommonConstants.BLANK_STRING;
        // 4. [3.1]で取得した出力帳票印刷情報リスト件数>0の場合、下記処理を行う
        if (outDto.getChoPrtList() != null && !outDto.getChoPrtList().isEmpty()) {
            // '① 出力帳票印刷情報リスト.プリントナンバーがリクエストパラメータ.選択帳票番号同じの場合、該当レコードのプロファイルを設定する
            for (Gui01211ChoPrt dto : outDto.getChoPrtList()) {
                if (dto.getPrtNo() != null && dto.getPrtNo().equals(inDto.getChoIndex())) {
                    // 変数.セクション名 = レスポンスパラメータ.出力帳票印刷情報リストに該当レコード.プロファイル
                    section = dto.getChoPro();
                    break;
                } else {
                    // 【変数】.セクション = 出力帳票印刷情報リスト.1件目.プロファイル
                    section = outDto.getChoPrtList().get(0).getChoPro();
                }
            }
        }

        // 帳票イニシャライズデータを保存する。
        initializeDataSave(inDto.getShokuId(), CommonConstants.STR_0, CommonConstants.STR_0, CommonConstants.STR_0,
                outDto.getKojinHogoFlg(), inDto.getSysCd(), section);
        
        
        // 4.2 帳票イニシャライズデータの取得を行う
        // 「API定義書_APINo(1009)_帳票イニシャライズデータ取得.xlsx」を参照する
        LedgerInitializeDataComSelectServiceInDto ledgerInDto = new LedgerInitializeDataComSelectServiceInDto();
        ledgerInDto.setSysCd(inDto.getSysCd());
        ledgerInDto.setShokuId(inDto.getShokuId());
        ledgerInDto.setKinounameKnj(inDto.getKinounameKnj());
        ledgerInDto.setKojinhogoFlg(inDto.getKojinhogoFlg());
        ledgerInDto.setSectionAddNo(section);
        ledgerInDto.setSectionKnj(inDto.getSectionName());

        try {
            ledgerOutdto = ledgerInitializeDataComSelectServiceImpl.mainProcess(ledgerInDto);
        } catch (Exception e) {
            e.printStackTrace();
        }
        outDto.setIniDataList(ledgerOutdto.getIniDataObject());
        
        // 5.事業者毎印刷で印刷する帳票の設定
        // 一時変数：連続＝{ 0, 0, 0, 0, 0, 0 }
        List<String> contList = new ArrayList<String>();
        contList.add(CommonConstants.STR_0);
        contList.add(CommonConstants.STR_0);
        contList.add(CommonConstants.STR_0);
        contList.add(CommonConstants.STR_0);
        contList.add(CommonConstants.STR_0);
        contList.add(CommonConstants.STR_0);

        // 一時変数：連続タイトル＝{ "サービス提供依頼書", "サービス提供利用者一覧", "サービス提供票" , "サービス提供票別表" ,
        // "事業所別利用者一覧", "提供票送付状" }
        List<String> contTitleList = new ArrayList<String>();
        contTitleList.add(CommonConstants.PRINT_TITLE_IRAI); // サービス提供依頼書
        contTitleList.add(CommonConstants.PRINT_TITLE_USER); // サービス提供利用者一覧
        contTitleList.add(CommonConstants.PRINT_TITLE_TICKET); // サービス提供票
        contTitleList.add(CommonConstants.PRINT_TITLE_OTHERTICKET); // サービス提供票別表
        contTitleList.add(CommonConstants.PRINT_TITLE_SVUSER); // 事業所別利用者一覧
        contTitleList.add(CommonConstants.PRINT_TITLE_COVERLETTER); // 提供票送付状

        // 一時変数：連続カラム＝{ "def_sel_irais", "def_sel_riyou", "def_sel_teiky",
        // "def_sel_tbetu", "def_sel_1ran","def_sel_souhu" }
        List<String> contColumnList = new ArrayList<String>();
        contColumnList.add(CommonConstants.DEF_SEL_IRAIS);
        contColumnList.add(CommonConstants.DEF_SEL_RIYOU);
        contColumnList.add(CommonConstants.DEF_SEL_TEIKY);
        contColumnList.add(CommonConstants.DEF_SEL_TBETU);
        contColumnList.add(CommonConstants.DEF_SEL_1RAN);
        contColumnList.add(CommonConstants.DEF_SEL_SOUHU);

        // 5.1 レスポンスパラメータ.出力帳票印刷情報リスト.出力帳票名が非空の場合、
        for (Gui01211ChoPrt choPrt : outDto.getChoPrtList()) {
            if (!StringUtil.isEmpty(choPrt.getDefPrtTitle())) {
                // 5.1.1 1から一時変数：連続タイトルの件数をループする、下記処理を実行する。
                List<Gui01211OutPrint> outPrintList = new ArrayList<Gui01211OutPrint>();
                for (int i = 0; i < contTitleList.size(); i++) {
                    // 5.1.1.1 共通メソッドsetF3gkProfile()メソッドを実行する。
                    F3gkGetProfileInDto searchCondition = new F3gkGetProfileInDto();
                    // 職員ＩＤ
                    searchCondition.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
                    // 法人ＩＤ
                    searchCondition.setHoujinId(CommonConstants.INT_0);
                    // 施設ＩＤ
                    searchCondition.setShisetuId(CommonConstants.INT_0);
                    // 事業所ＩＤ
                    searchCondition.setSvJigyoId(CommonConstants.INT_0);
                    // 画面名
                    searchCondition.setKinounameKnj(CommonConstants.CMN_RITEI_PRINT_OPTION);
                    // セクション
                    searchCondition.setSectionKnj(CommonConstants.CMN_TEIKYO_SELECTS);
                    // キー
                    searchCondition.setKeyKnj(contColumnList.get(i));
                    // パラメータ
                    // システムコード
                    searchCondition.setGsyscd(inDto.getSysCd());
                    // 初期値
                    searchCondition.setAsDefault(CommonConstants.BLANK_STRING);
                    String searchResult = this.nds3GkFunc01Logic.getF3gkProfile(searchCondition);

                    // ①一時変数：連続のループ行＝上記OUTPUT情報.パラメータ (※NULLの場合、0)
                    contList.set(i, varToVarIsNull(searchResult));

                    // ②レスポンスパラメータ.出力帳票リストの行追加
                    Gui01211OutPrint outPrint = new Gui01211OutPrint();
                    outPrint.setChoNo(contList.get(i));
                    outPrint.setChoKnj(contTitleList.get(i));
                    outPrintList.add(outPrint);
                }
                outDto.setOutPrintList(outPrintList);
                // 5.2 上記以外の場合、
            } else {
                // 5.2.1 1から一時変数：連続タイトルの件数をループする、下記処理を実行する。
                List<Gui01211OutPrint> outPrintList = new ArrayList<Gui01211OutPrint>();
                for (int i = 0; i < contTitleList.size(); i++) {
                    // レスポンスパラメータ.出力帳票リストの行追加
                    Gui01211OutPrint outPrint = new Gui01211OutPrint();
                    outPrint.setChoNo(contList.get(i));
                    outPrint.setChoKnj(contTitleList.get(i));
                    outPrintList.add(outPrint);
                }
                outDto.setOutPrintList(outPrintList);
            }
        }

        // 5.3 処理年月を設定する
        // レスポンスパラメータ.処理年月=リクエストパラメータ.提供年月
        outDto.setShoriYm(inDto.getYymmYm());

        // 6 レスポンスパラメータ.事業所選択情報リストとレスポンスパラメータ.利用者情報リストとレスポンスパラメータ.画面表示フラグを設定する。
        // API定義書_APINo(1209)_印刷設定画面一覧情報取得.xlsx
        GetPrinterSettingsListInfoSelectServiceInDto getPrinterInDoto = new GetPrinterSettingsListInfoSelectServiceInDto();
        GetPrinterSettingsListInfoSelectServiceOutDto getPrinterOutDoto = new GetPrinterSettingsListInfoSelectServiceOutDto();
        getPrinterInDoto.setShienId(inDto.getShienId());
        getPrinterInDoto.setYymmYm(inDto.getYymmYm());
        getPrinterInDoto.setTantoId(inDto.getTantoId());
        getPrinterInDoto.setKensakuFlg(CommonConstants.STR_1);

        try {
            getPrinterOutDoto = getPrinterSettingsListInfoSelectServiceImpl.mainProcess(getPrinterInDoto);
            outDto.setJigyouInfoList(getPrinterOutDoto.getJigyouInfoList());
            outDto.setRiyoushaInfoList(getPrinterOutDoto.getRiyoushaInfoList());
            outDto.setGameHyojiFlg(getPrinterOutDoto.getGameHyojiFlg());
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 7.画面表示フラグで送付状マスタにデフォルトが無ければ作成する
        CmnFaxSoufujo4DefaultByCriteriaInEntity cmnFaxSoufujo4DefaultByCriteriaInEntity = new CmnFaxSoufujo4DefaultByCriteriaInEntity();
        // 支援事業所番号
        cmnFaxSoufujo4DefaultByCriteriaInEntity.setAlShi(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // サービス事業者ID
        cmnFaxSoufujo4DefaultByCriteriaInEntity.setAlSvj(0);
        // 処理年月
        cmnFaxSoufujo4DefaultByCriteriaInEntity.setAsYm(CommonConstants.DEFAULT);

        List<CmnFaxSoufujo4DefaultOutEntity> defaultOutEntityList = cmnTucFaxSoufujoSelectMapper
                .findCmnFaxSoufujo4DefaultByCriteria(cmnFaxSoufujo4DefaultByCriteriaInEntity);

        // 7.1 上記取得データの件数が0の場合、新規データを登録する
        if (CollectionUtils.isEmpty(defaultOutEntityList)) {
            CmnTucFaxSoufujo cmnTucFaxSoufujo = new CmnTucFaxSoufujo();
            // 支援事業所番号
            cmnTucFaxSoufujo.setShienId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // サービス事業者ID
            cmnTucFaxSoufujo.setSvJigyoId(CommonConstants.INT_0);
            // 処理年月
            cmnTucFaxSoufujo.setYymmYm(CommonConstants.DEFAULT);
            // 利用者ﾘｽﾄ(０：追加しない、１：追加する)
            cmnTucFaxSoufujo.setUserlistFlg(CommonConstants.INT_0);
            // タイトル
            cmnTucFaxSoufujo.setSubjectKnj(CommonConstants.SUBJECT_TITLE);
            // 送付状内容
            cmnTucFaxSoufujo.setSoufujoKnj(CommonConstants.COVER_LETTER);
            cmnTucFaxSoufujoMapper.insertSelective(cmnTucFaxSoufujo);
        }

        // 8. 職員基本の情報を取得する、レスポンスパラメータ.担当リストを設定する。
        ShokuinNameNumberByCriteriaInEntity inEntity = new ShokuinNameNumberByCriteriaInEntity();
        // 職員氏名情報を取得する
        List<ShokuinNameNumberOutEntity> shokuinInfoList = comMscShokuinSelectMapper
                .findShokuinNameNumberByCriteria(inEntity);
        // 上記結果をレスポンスパラメータ.担当リストに設定する
        List<Gui01211Tanto> tantoList = new ArrayList<Gui01211Tanto>();
        for (ShokuinNameNumberOutEntity outEntity : shokuinInfoList) {
            Gui01211Tanto gui01211Tanto = new Gui01211Tanto();
            gui01211Tanto.setChkShokuId(CommonDtoUtil.objValToString(outEntity.getChkShokuId()));
            gui01211Tanto.setShokuin1Knj(gui01211Tanto.getShokuin1Knj());
            gui01211Tanto.setShokuin2Knj(gui01211Tanto.getShokuin2Knj());
            tantoList.add(gui01211Tanto);
        }
        outDto.setTantoList(tantoList);

        // 9. 保険者情報を取得する、レスポンスパラメータ.保険者番号リストを設定する。
        List<KghNumberComUserHokenjaFltOutEntity> kghNumberComUserHokenjaFltOutEntities = comMscKaigoHokenjaSelectMapper
                .findKghNumberComUserHokenjaFltByCriteria(new KghNumberComUserHokenjaFltByCriteriaInEntity());
        // 上記結果をレスポンスパラメータ.保険者番号リストに設定する
        List<Gui01211KHoken> kHokenList = new ArrayList<Gui01211KHoken>();
        for (KghNumberComUserHokenjaFltOutEntity outEntity : kghNumberComUserHokenjaFltOutEntities) {
            Gui01211KHoken gui01211KHoken = new Gui01211KHoken();
            // counter
            gui01211KHoken.setCounter(CommonDtoUtil.objValToString(outEntity.getCounter()));
            // 保険者ID
            gui01211KHoken.setKHokenCd(CommonDtoUtil.objValToString(outEntity.getKHokenCd()));
            // 保険者名称
            gui01211KHoken.setKHokenKnj(outEntity.getKHokenKnj());
            // 保険者番号
            gui01211KHoken.setKHokenNo(outEntity.getKHokenNo());
            // 職員ID
            gui01211KHoken.setShokuId(CommonDtoUtil.objValToString(outEntity.getShokuId()));
            // 更新日時
            gui01211KHoken.setTimeStmp(CommonDtoUtil.objValToString(outEntity.getTimeStmp()));
            // 削除フラグ
            gui01211KHoken.setDelFlg(CommonDtoUtil.objValToString(outEntity.getDelFlg()));
            // 市区町村名
            gui01211KHoken.setCityNameKnj(outEntity.getCityNameKnj());
            // 市区町村長名
            gui01211KHoken.setCityChiefKnj(outEntity.getCityChiefKnj());
            // 郵便番号
            gui01211KHoken.setZip(outEntity.getZip());
            // 県コード
            gui01211KHoken.setKencode(CommonDtoUtil.objValToString(outEntity.getKencode()));
            // 市町村コード
            gui01211KHoken.setCitycode(CommonDtoUtil.objValToString(outEntity.getCitycode()));
            // 地区コード
            gui01211KHoken.setAreacode(CommonDtoUtil.objValToString(outEntity.getAreacode()));
            // 住所
            gui01211KHoken.setAddressKnj(outEntity.getAddressKnj());
            // 電話番号
            gui01211KHoken.setTel(outEntity.getTel());
            // 表示順
            gui01211KHoken.setSort(CommonDtoUtil.objValToString(outEntity.getSort()));
            kHokenList.add(gui01211KHoken);
        }
        outDto.setKHokenList(kHokenList);

        // 10. レスポンスに返却する
        return outDto;
    }

    /**
     * 値がNULLの場合、戻り""
     * 
     * @param var
     * @return var | ""
     */
    private String varToVarIsNull(String var) {
        return StringUtil.isEmpty(var) ? CommonConstants.STR_0 : var;
    }

    private String setProfile(String sysRyaku, String sectionNo, int prtNo) {
        return sysRyaku + sectionNo + CommonConstants.P_STRING
                + String.format(CommonConstants.ZERO_THREE_STRING, prtNo);
    }

    /**
     * 関数_帳票イニシャライズデータ保存
     * 
     * @param shokuId        職員ID
     * @param amikakeFlg     伏字チェック
     * @param monjiNoFlg     文書番号チェック
     * @param kojinHogoCheck 個人保護チェック
     * @param kojinHogoFlg   個人保護フラグ
     * @param sysCd          システムコード
     * @param section        セクション
     * @return void
     */
    public void initializeDataSave(String shokuId, String amikakeFlg, String monjiNoFlg, String kojinHogoCheck,
            String kojinHogoFlg, String sysCd, String section) {

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        // 2.帳票イニシャライズデータ保存を行う
        // (1)引き渡しパラメータ.伏字チェック＝1の場合、
        if (CommonConstants.STR_1.equals(amikakeFlg)) {
            // (1)-1 共通メソッドsetF3gkProfile()メソッドを実行する。
            F3gkSetProfileInDto f3gkSetProfileInDto = new F3gkSetProfileInDto();
            // 職員ＩＤ
            f3gkSetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(shokuId));
            // 法人ＩＤ
            f3gkSetProfileInDto.setHoujinId(CommonConstants.INT_0);
            // 施設ＩＤ
            f3gkSetProfileInDto.setShisetuId(CommonConstants.INT_0);
            // 事業所ＩＤ
            f3gkSetProfileInDto.setSvJigyoId(CommonConstants.INT_0);
            // 画面名
            f3gkSetProfileInDto.setKinounameKnj(CommonConstants.PRT_STRING);
            // セクション
            f3gkSetProfileInDto.setSectionKnj(section);
            // キー
            f3gkSetProfileInDto.setKeyKnj(CommonConstants.S_AMIKAKE_FLG);
            // パラメータ
            f3gkSetProfileInDto.setParam(CommonConstants.STR_1);
            // システムコード
            f3gkSetProfileInDto.setGsyscd(sysCd);
            nds3GkFunc01Logic.setF3gkProfile(f3gkSetProfileInDto);
        } else {
            // 引き渡しパラメータ.伏字チェック<>1の場合、
            // (2)-1 共通メソッドsetF3gkProfile()メソッドを実行する。
            F3gkSetProfileInDto f3gkSetProfileInDto = new F3gkSetProfileInDto();
            // 職員ＩＤ
            f3gkSetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(shokuId));
            // 法人ＩＤ
            f3gkSetProfileInDto.setHoujinId(CommonConstants.INT_0);
            // 施設ＩＤ
            f3gkSetProfileInDto.setShisetuId(CommonConstants.INT_0);
            // 事業所ＩＤ
            f3gkSetProfileInDto.setSvJigyoId(CommonConstants.INT_0);
            // 画面名
            f3gkSetProfileInDto.setKinounameKnj(CommonConstants.PRT_STRING);
            // セクション
            f3gkSetProfileInDto.setSectionKnj(section);
            // キー
            f3gkSetProfileInDto.setKeyKnj(CommonConstants.S_AMIKAKE_FLG);
            // パラメータ
            f3gkSetProfileInDto.setParam(CommonConstants.STR_0);
            // システムコード
            f3gkSetProfileInDto.setGsyscd(sysCd);
            nds3GkFunc01Logic.setF3gkProfile(f3gkSetProfileInDto);
        }

        // (3) 引き渡しパラメータ.文書番号チェック＝1の場合、
        if (CommonConstants.STR_1.equals(monjiNoFlg)) {
            // (3)-1 共通メソッドsetF3gkProfile()メソッドを実行する。
            F3gkSetProfileInDto f3gkSetProfileInDto = new F3gkSetProfileInDto();
            // 職員ＩＤ
            f3gkSetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(shokuId));
            // 法人ＩＤ
            f3gkSetProfileInDto.setHoujinId(CommonConstants.INT_0);
            // 施設ＩＤ
            f3gkSetProfileInDto.setShisetuId(CommonConstants.INT_0);
            // 事業所ＩＤ
            f3gkSetProfileInDto.setSvJigyoId(CommonConstants.INT_0);
            // 画面名
            f3gkSetProfileInDto.setKinounameKnj(CommonConstants.PRT_STRING);
            // セクション
            f3gkSetProfileInDto.setSectionKnj(section);
            // キー
            f3gkSetProfileInDto.setKeyKnj(CommonConstants.S_ISO9001_FLG);
            // パラメータ
            f3gkSetProfileInDto.setParam(CommonConstants.STR_1);
            // システムコード
            f3gkSetProfileInDto.setGsyscd(sysCd);
            nds3GkFunc01Logic.setF3gkProfile(f3gkSetProfileInDto);
        } else {
            // (4)-1 共通メソッドsetF3gkProfile()メソッドを実行する。
            F3gkSetProfileInDto f3gkSetProfileInDto = new F3gkSetProfileInDto();
            // 職員ＩＤ
            f3gkSetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(shokuId));
            // 法人ＩＤ
            f3gkSetProfileInDto.setHoujinId(CommonConstants.INT_0);
            // 施設ＩＤ
            f3gkSetProfileInDto.setShisetuId(CommonConstants.INT_0);
            // 事業所ＩＤ
            f3gkSetProfileInDto.setSvJigyoId(CommonConstants.INT_0);
            // 画面名
            f3gkSetProfileInDto.setKinounameKnj(CommonConstants.PRT_STRING);
            // セクション
            f3gkSetProfileInDto.setSectionKnj(section);
            // キー
            f3gkSetProfileInDto.setKeyKnj(CommonConstants.S_ISO9001_FLG);
            // パラメータ
            f3gkSetProfileInDto.setParam(CommonConstants.STR_0);
            // システムコード
            f3gkSetProfileInDto.setGsyscd(sysCd);
            nds3GkFunc01Logic.setF3gkProfile(f3gkSetProfileInDto);
        }

        // (5) 引き渡しパラメータ.個人保護フラグ＝1の場合、
        if (CommonConstants.STR_1.equals(kojinHogoFlg)) {
            // (5)-1 引き渡しパラメータ.個人保護チェック=1の場合、
            if (CommonConstants.STR_1.equals(kojinHogoCheck)) {
                // (5)-1 共通メソッドsetF3gkProfile()メソッドを実行する。
                F3gkSetProfileInDto f3gkSetProfileInDto = new F3gkSetProfileInDto();
                // 職員ＩＤ
                f3gkSetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(shokuId));
                // 法人ＩＤ
                f3gkSetProfileInDto.setHoujinId(CommonConstants.INT_0);
                // 施設ＩＤ
                f3gkSetProfileInDto.setShisetuId(CommonConstants.INT_0);
                // 事業所ＩＤ
                f3gkSetProfileInDto.setSvJigyoId(CommonConstants.INT_0);
                // 画面名
                f3gkSetProfileInDto.setKinounameKnj(CommonConstants.PRT_STRING);
                // セクション
                f3gkSetProfileInDto.setSectionKnj(section);
                // キー
                f3gkSetProfileInDto.setKeyKnj(CommonConstants.S_KOJINHOGO_FLG);
                // パラメータ
                f3gkSetProfileInDto.setParam(CommonConstants.STR_1);
                // システムコード
                f3gkSetProfileInDto.setGsyscd(sysCd);
                nds3GkFunc01Logic.setF3gkProfile(f3gkSetProfileInDto);
            }
        } else {
            // (6)-1 共通メソッドsetF3gkProfile()メソッドを実行する。
            F3gkSetProfileInDto f3gkSetProfileInDto = new F3gkSetProfileInDto();
            // 職員ＩＤ
            f3gkSetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(shokuId));
            // 法人ＩＤ
            f3gkSetProfileInDto.setHoujinId(CommonConstants.INT_0);
            // 施設ＩＤ
            f3gkSetProfileInDto.setShisetuId(CommonConstants.INT_0);
            // 事業所ＩＤ
            f3gkSetProfileInDto.setSvJigyoId(CommonConstants.INT_0);
            // 画面名
            f3gkSetProfileInDto.setKinounameKnj(CommonConstants.PRT_STRING);
            // セクション
            f3gkSetProfileInDto.setSectionKnj(section);
            // キー
            f3gkSetProfileInDto.setKeyKnj(CommonConstants.S_KOJINHOGO_FLG);
            // パラメータ
            f3gkSetProfileInDto.setParam(CommonConstants.STR_0);
            // システムコード
            f3gkSetProfileInDto.setGsyscd(sysCd);
            nds3GkFunc01Logic.setF3gkProfile(f3gkSetProfileInDto);
        }
    }

}