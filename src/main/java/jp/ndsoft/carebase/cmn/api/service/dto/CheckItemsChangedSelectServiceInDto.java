package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.06.18
 * <AUTHOR>
 * @implNote GUI00892_チェック項目画面 初期情報取得サービス入力Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CheckItemsChangedSelectServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 事業者ID */
    @NotEmpty
    private String svJigyoId;

    /** 利用者ID */
    @NotEmpty
    private String userId;

    /** 法人ID */
    @NotEmpty
    private String houjinId;

    /** 施設ID */
    @NotEmpty
    private String shisetuId;

    /** 様式ID */
    @NotEmpty
    private String youshikiId;

    /** 計画期間ID */
    @NotEmpty
    private String sc1Id;

    /** 履歴ID */
    @NotEmpty
    private String assId;

}
