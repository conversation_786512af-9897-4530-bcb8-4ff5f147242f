package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00799BasicActionNinteiFlg4Info;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00799BasicActionNinteiFlg5Info;
import jp.ndsoft.carebase.cmn.api.logic.AssessmentHomeLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeSaveServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeTab61InsertServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeTab61InsertServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.*;
import jp.ndsoft.carebase.common.dao.mybatis.entity.*;
import jp.ndsoft.smh.framework.global.base.service.InsertServiceImpl;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * @since 2025.03.25
 * <AUTHOR>
 * @apiNote Gui00799_［アセスメント］画面（居宅）（6①） データ保存
 */
@Service
public class AssessmentHomeTab61InsertServiceImpl
        extends
        InsertServiceImpl<AssessmentHomeTab61InsertServiceInDto, AssessmentHomeTab61InsertServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 【ＧＬ＿居宅アセスメント履歴】情報を登録DAO */
    @Autowired
    private CpnTucGdlRirekiMapper cpnTucGdlRirekiMapper;

    /** 【ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）DAO */
    @Autowired
    private CpnTucGdl4Kan11H21Mapper cpnTucGdl4Kan11H21Mapper;

    /** ＧＬ＿①基本（身体機能・起居）動作（R３改訂） */
    @Autowired
    private CpnTucGdl5Kan11R3Mapper cpnTucGdl5Kan11R3Mapper;

    /** ［アセスメント］画面（居宅）画面のロジッククラス */
    @Autowired
    private AssessmentHomeLogic assessmentHomeLogic;

    @Autowired
    private PlatformTransactionManager transactionManager;

    /**
     * 「［アセスメント］画面（居宅）（6①）」画面のデータ保存する
     * 
     * @param inDto アセスメント込データ保存入力DTO
     * @return アセスメントデータ保存出力DTO
     */
    @Override
    protected AssessmentHomeTab61InsertServiceOutDto mainProcess(AssessmentHomeTab61InsertServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        AssessmentHomeTab61InsertServiceOutDto outDto = new AssessmentHomeTab61InsertServiceOutDto();
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        /*
         * ===============2. バリデーションチェックを行う。===============
         * 
         */
        boolean resFlg = assessmentHomeLogic.documentPerformValidationCheck(loginDto);
        // 2.2.4. 【変数】.処理結果が「false：失敗」の場合
        if (!resFlg) {
            // 2.2.4. 【変数】.処理結果が「false：失敗」の場合、下記返却する情報を設定して、後続処理を飛ばして、API処理を正常終了とする。
            // レスポンス.計画期間ID ： リクエストパラメータ.計画期間ID
            outDto.setSc1Id(loginDto.getSc1Id());
            // レスポンス.アセスメントID ： リクエストパラメータ.アセスメントID
            outDto.setGdlId(loginDto.getGdlId());
            // レスポンス.エラー区分: "1
            outDto.setErrKbn(CommonConstants.E_DOC_CHECK_ERR);
            return outDto;
        }
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transactionB = transactionManager.getTransaction(def);
        try {
            // ［アセスメント］画面（居宅）（6①） データ保存
            outDto = mainProcessMealUpdate(inDto);
            transactionManager.commit(transactionB);
        } catch (Exception e) {
            transactionManager.rollback(transactionB);
            throw e;
        }

        // e-文書_ケアチェック表１のリクエストパラメータ
        TransactionStatus transactionC = transactionManager.getTransaction(def);
        try {
            mainProcessEdocOut(inDto, outDto);
            transactionManager.commit(transactionC);
        } catch (Exception e) {
            transactionManager.rollback(transactionC);
            throw e;
        }

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * API定義 (e文書出力)
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected void mainProcessEdocOut(
            AssessmentHomeTab61InsertServiceInDto inDto, AssessmentHomeTab61InsertServiceOutDto outDto)
            throws Exception {
        // 入力dtoをLoginDtoにコピーする
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);

        /*
         * ===============e文書出力===============
         * 
         */
        // e文書出力
        String errKbn = assessmentHomeLogic.getPrint(loginDto, outDto.getSc1Id());
        outDto.setErrKbn(errKbn);
    }

    /**
     * ［アセスメント］画面（居宅）（6①） データ保存
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected AssessmentHomeTab61InsertServiceOutDto mainProcessMealUpdate(
            AssessmentHomeTab61InsertServiceInDto inDto)
            throws Exception {

        // 戻り情報を設定
        AssessmentHomeTab61InsertServiceOutDto outDto = new AssessmentHomeTab61InsertServiceOutDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2.計画対象期間の保存処理================
         * 
         */
        // 2.1 リクエストパラメータ.計画対象期間IDがnullの場合、【27-06 記録共通期間】情報を登録する。
        // DAOパラメータを作成
        AssessmentHomeSaveServiceInDto loginDto = this.setAssessmentHomeSaveServiceInDto(inDto);

        if (inDto.getSc1Id() == null || StringUtil.isEmpty(inDto.getSc1Id())) {
            // 計画対象期間の保存処理
            this.assessmentHomeLogic.insertKghTucKrkKikan(loginDto);
            // 変数.計画対象期間ID=採番した期間ID
            inDto.setSc1Id(loginDto.getSc1Id());
        }

        /*
         * =====3.リクエストパラメータ.削除処理区分が2:画面を履歴ごと削除するの場合、下記テーブルデータを更新する。==========
         * 
         */
        if (CommonConstants.DELETE_KBN_2.equals(inDto.getDeleteProcessKbn())) {
            assessmentHomeLogic.homeLogicsyuri(loginDto, CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        }
        /*
         * =====4. リクエストパラメータ.削除処理区分が1:画面のみ削除するの場合==========
         * 
         */
        else if (CommonConstants.DELETE_KBN_1.equals(inDto.getDeleteProcessKbn())) {
            // 4.1. リクエストパラメータ.改定フラグが4「H21/４改訂版」の場合、下記対象テーブルのサブ情報をロジック的に削除する。
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                // ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）に削除する。
                this.deleteKan11H21(inDto);

            } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                // 4.2. リクエストパラメータ.改定フラグが5「R3/４改訂版」の場合、下記対象テーブルのサブ情報をロジック的に削除する。
                // ＧＬ＿①基本（身体機能・起居）動作（R３改訂）に削除する。
                this.deleteKan11R3(inDto);
            }

            // 4.3. 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
            this.clearRrk(inDto);

        } else {
            /*
             * ===============5.以外の場合===============
             * 
             */
            // 5.1. 履歴情報の保存処理
            // 5.1.1. リクエストパラメータ.履歴更新区分が"C":新規の場合、【ＧＬ＿居宅アセスメント履歴】情報を登録する。
            if (CommonDtoUtil.isHistoryCreate(inDto)) {
                // 履歴新規作成し、新規作成の履歴IDを返却する
                Integer newGdlId = this.insertRrk(inDto);
                inDto.setGdlId(CommonDtoUtil.objValToString(newGdlId));
                loginDto.setGdlId(CommonDtoUtil.objValToString(newGdlId));

            } else if (CommonDtoUtil.isHistoryUpdate(inDto)) {
                // 5.1.2. リクエストパラメータ.履歴更新区分が"U":更新の場合、【ＧＬ＿居宅アセスメント履歴】情報を更新する。
                this.updateRrk(inDto);

            }

            // 5.2. サブ情報の保存処理
            // 5.1. リクエストパラメータ.更新区分が"C":新規の場合、基本動作情報を登録する。
            if (CommonDtoUtil.isCreate(inDto)) {
                // 5.1.1. リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、【ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）】情報を登録する。
                if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                    this.insertKan11H21(inDto);

                } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                    // 5.1.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【ＧＬ＿①基本（身体機能・起居）動作（R３改訂）】情報を登録する。
                    this.insertKan11R3(inDto);
                }
            }
            // 5.2. リクエストパラメータ.更新区分が"U":更新の場合、基本動作情報を更新する。
            else if (CommonDtoUtil.isUpdate(inDto)) {
                // 3.2.1. リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、【ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）】情報を更新する。
                if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                    this.updateKan11H21(inDto);

                } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                    // 3.2.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【ＧＬ＿①基本（身体機能・起居）動作（R３改訂）】情報を更新する。
                    this.updateKan11R3(inDto);
                }
            }

            // 5.3. リクエストパラメータ.【課題と目標リスト】の件数分、【ＧＬ＿課題と目標】情報を保存する。
            assessmentHomeLogic.homeLogicCpnTucGdlKadai(loginDto, CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        }

        /*
         * ===============6. レスポンスを返却===============
         * 
         */
        outDto.setSc1Id(inDto.getSc1Id());
        outDto.setGdlId(inDto.getGdlId());

        return outDto;
    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を登録する。
     * 関数名：insertRrk
     * 
     * @param inDto 更新パラメータ
     * @return 履歴ID
     */
    private Integer insertRrk(AssessmentHomeTab61InsertServiceInDto inDto) throws Exception {
        // ＧＬ＿居宅アセスメント履歴パラメータを作成
        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();

        // 計画期間ID
        cpnTucGdlRireki.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // 法人ID
        cpnTucGdlRireki.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        cpnTucGdlRireki.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        cpnTucGdlRireki.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ＩＤ
        cpnTucGdlRireki.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // アセスメント実施日
        cpnTucGdlRireki.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRireki.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // 本人の基本動作等1
        cpnTucGdlRireki.setAss6(CommonConstants.MARU);

        // リクエストパラメータ.改定フラグが4「H21/４改訂版」または5「R3/４改訂版」の場合、CommonConstants.DASHを設定する、以外の場合設定しない
        if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())
                || CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
            // Ｊの状態
            cpnTucGdlRireki.setAss12(CommonConstants.DASH);
            // 本人の基本動作等8
            cpnTucGdlRireki.setAss13(CommonConstants.DASH);
        }

        // 改定フラグ
        cpnTucGdlRireki.setNinteiFormF(CommonDtoUtil.strValToInt(inDto.getNinteiFormF()));

        this.cpnTucGdlRirekiMapper.insertSelectiveAndReturn(cpnTucGdlRireki);

        // 履歴ID採番
        Integer newGdlId = cpnTucGdlRireki.getGdlId();

        return newGdlId;
    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
     * 関数名：updateRrk
     * 
     * @param inDto 更新パラメータ
     */
    private void updateRrk(AssessmentHomeTab61InsertServiceInDto inDto) {
        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();
        // アセスメント実施日
        cpnTucGdlRireki.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRireki.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // 本人の基本動作等1
        cpnTucGdlRireki.setAss6(CommonConstants.MARU);
        // Ｊの状態
        cpnTucGdlRireki.setAss12(CommonConstants.DASH);
        // 本人の基本動作等8
        cpnTucGdlRireki.setAss13(CommonConstants.DASH);

        // ＧＬ＿居宅アセスメント履歴パラメータを作成
        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                // 利用者ＩＤ
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 法人ID
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        // DAOを実行
        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRireki, criteria);
    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
     * 関数名：clearRrk
     * 
     * @param inDto 更新パラメータ
     */
    private void clearRrk(AssessmentHomeTab61InsertServiceInDto inDto) {
        // DAOパラメータを作成
        // 3.1.居宅アセスメント履歴を更新する
        // ＧＬ＿居宅アセスメント履歴パラメータを作成
        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                // 利用者ＩＤ
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 法人ID
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        // ｻｰﾋﾞｽ利用状況
        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();
        cpnTucGdlRireki.setAss6(StringUtils.EMPTY);

        // DAOを実行
         this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRireki, criteria);

    }

    /**
     * 【ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）】情報を登録する。
     * 関数名：insertKan11H21
     * 
     * @param inDto 更新パラメータ
     * @return 結果結果
     */
    private void insertKan11H21(AssessmentHomeTab61InsertServiceInDto inDto) throws Exception {

        // ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）】パラメータを作成
        CpnTucGdl4Kan11H21 insertCpn4 = this.setInsertCpn4(inDto.getBasicAction4Info());
        // 計画対象期間ID
        insertCpn4.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // 履歴ID
        insertCpn4.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // DAOを実行
        this.cpnTucGdl4Kan11H21Mapper.insertSelective(insertCpn4);

    }

    /**
     * 【ＧＬ＿①基本（身体機能・起居）動作（R３改訂）】情報を登録する。
     * insertKan11R3
     * 
     * @param inDto 更新パラメータ
     * @return 結果結果
     */
    private void insertKan11R3(AssessmentHomeTab61InsertServiceInDto inDto) throws Exception {
        // 【ＧＬ＿①基本（身体機能・起居）動作（R３改訂）】パラメータを作成
        CpnTucGdl5Kan11R3 insertCpn5 = this.setInsertCpn5(inDto.getBasicAction5Info());
        // 計画対象期間ID
        insertCpn5.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // 採番されたアセスメントID
        insertCpn5.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));

        // DAOを実行
        this.cpnTucGdl5Kan11R3Mapper.insertSelective(insertCpn5);

    }

    /**
     * 【ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）】情報を更新する
     * updateKan11H21
     * 
     * @param inDto 更新パラメータ
     */
    private void updateKan11H21(AssessmentHomeTab61InsertServiceInDto inDto) throws Exception {
        // パラメータを作成
        CpnTucGdl4Kan11H21Criteria criteria = new CpnTucGdl4Kan11H21Criteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画対象期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        CpnTucGdl4Kan11H21 updateCpn4 = this.setInsertCpn4(inDto.getBasicAction4Info());

        // DAOを実行
        this.cpnTucGdl4Kan11H21Mapper.updateByCriteriaSelective(updateCpn4, criteria);

    }

    /**
     * 【ＧＬ＿①基本（身体機能・起居）動作（R３改訂）】情報を更新する
     * updateKan11R3
     * 
     * @param inDto 更新パラメータ
     */
    private void updateKan11R3(AssessmentHomeTab61InsertServiceInDto inDto) throws Exception {
        // パラメータを作成
        CpnTucGdl5Kan11R3Criteria criteria = new CpnTucGdl5Kan11R3Criteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto
                .getGdlId()))
                // 計画対象期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        CpnTucGdl5Kan11R3 updateCpn5 = this.setInsertCpn5(inDto.getBasicAction5Info());

        // DAOを実行
        this.cpnTucGdl5Kan11R3Mapper.updateByCriteriaSelective(updateCpn5, criteria);

    }

    /**
     * ＧＬ＿①基本（身体機能・起居）動作を削除する
     * updateKan11R3
     * 
     * @param inDto 更新パラメータ
     */
    private void deleteKan11H21(AssessmentHomeTab61InsertServiceInDto inDto) throws Exception {
        CpnTucGdl4Kan11H21Criteria cpnTucGdl4Kan11H21Criteria = new CpnTucGdl4Kan11H21Criteria();
        cpnTucGdl4Kan11H21Criteria.createCriteria()
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        this.cpnTucGdl4Kan11H21Mapper.deleteByCriteria(cpnTucGdl4Kan11H21Criteria);
    }

    /**
     * ＧＬ＿①基本（身体機能・起居）動作（R３改訂）を削除する
     * updateKan11R3
     * 
     * @param inDto 更新パラメータ
     */
    private void deleteKan11R3(AssessmentHomeTab61InsertServiceInDto inDto) throws Exception {
        CpnTucGdl5Kan11R3Criteria cpnTucGdl5Kan11R3Criteria = new CpnTucGdl5Kan11R3Criteria();
        cpnTucGdl5Kan11R3Criteria.createCriteria()
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        this.cpnTucGdl5Kan11R3Mapper.deleteByCriteria(cpnTucGdl5Kan11R3Criteria);

    }

    /**
     * 【27-06記録共通期間】情報を登録パラメータを設定する
     * 
     * @param inDto 施設マスタ
     * @return 【27-06記録共通期間】データ保存入力DTO.
     */
    private AssessmentHomeSaveServiceInDto setAssessmentHomeSaveServiceInDto(
            AssessmentHomeTab61InsertServiceInDto inDto) {
        AssessmentHomeSaveServiceInDto assessmentHomeSaveServiceInDto = new AssessmentHomeSaveServiceInDto();
        // 法人ID
        assessmentHomeSaveServiceInDto.setHoujinId(inDto.getHoujinId());
        // 施設ID
        assessmentHomeSaveServiceInDto.setShisetuId(inDto.getShisetuId());
        // 事業者ID
        assessmentHomeSaveServiceInDto.setSvJigyoId(inDto.getSvJigyoId());
        // 利用者ID
        assessmentHomeSaveServiceInDto.setUserId(inDto.getUserId());
        // 種別ID
        assessmentHomeSaveServiceInDto.setSyubetsuId(inDto.getSyubetsuId());
        // 作成日
        assessmentHomeSaveServiceInDto.setKijunbiYmd(inDto.getKijunbiYmd());

        return assessmentHomeSaveServiceInDto;

    }

    /**
     * ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）】情報を登録パラメータを作成
     * 
     * @param basicAction4Info 基本動作改訂フラグ4情報
     * @return ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）】情報を登録パラメータ
     */
    private CpnTucGdl4Kan11H21 setInsertCpn4(Gui00799BasicActionNinteiFlg4Info basicAction4Info) {
        CpnTucGdl4Kan11H21 insertCpn4 = new CpnTucGdl4Kan11H21();

        // 認定項目1-1_1(麻痺等（複数可）ない） */
        insertCpn4.setBango1C1I1(CommonDtoUtil.strValToInt(basicAction4Info.getBango01011()));
        // 認定項目1-1_2(麻痺等（複数可）左上肢） */
        insertCpn4.setBango1C1I2(CommonDtoUtil.strValToInt(basicAction4Info.getBango01012()));
        // 認定項目1-1_3(麻痺等（複数可）右上肢） */
        insertCpn4.setBango1C1I3(CommonDtoUtil.strValToInt(basicAction4Info.getBango01013()));
        // 認定項目1-1_4(麻痺等（複数可）左下肢） */
        insertCpn4.setBango1C1I4(CommonDtoUtil.strValToInt(basicAction4Info.getBango114()));
        // 認定項目1-1_5(麻痺等（複数可）右下肢） */
        insertCpn4.setBango1C1I5(CommonDtoUtil.strValToInt(basicAction4Info.getBango115()));
        // 認定項目1-1_6(麻痺等（複数可）その他） */
        insertCpn4.setBango1C1I6(CommonDtoUtil.strValToInt(basicAction4Info.getBango116()));
        // 認定項目1-2_1(拘縮（複数可）なし） */
        insertCpn4.setBango1C2I1(CommonDtoUtil.strValToInt(basicAction4Info.getBango121()));
        // 認定項目1-2_2(拘縮（複数可）肩関節） */
        insertCpn4.setBango1C2I2(CommonDtoUtil.strValToInt(basicAction4Info.getBango122()));
        // 認定項目1-2_3(拘縮（複数可）股関節） */
        insertCpn4.setBango1C2I3(CommonDtoUtil.strValToInt(basicAction4Info.getBango123()));
        // 認定項目1-2_4(拘縮（複数可）膝関節） */
        insertCpn4.setBango1C2I4(CommonDtoUtil.strValToInt(basicAction4Info.getBango124()));
        // 認定項目1-2_5(拘縮（複数可）その他（四肢の欠損）） */
        insertCpn4.setBango1C2I5(CommonDtoUtil.strValToInt(basicAction4Info.getBango125()));
        // 認定項目1-3（寝返り） */
        insertCpn4.setBango1C3(CommonDtoUtil.strValToInt(basicAction4Info.getBango13()));
        // 認定項目1-4（起き上がり） */
        insertCpn4.setBango1C4(CommonDtoUtil.strValToInt(basicAction4Info.getBango14()));
        // 認定項目1-5（座位保持） */
        insertCpn4.setBango1C5(CommonDtoUtil.strValToInt(basicAction4Info.getBango15()));
        // 認定項目1-6（両足での立位保持） */
        insertCpn4.setBango1C6(CommonDtoUtil.strValToInt(basicAction4Info.getBango16()));
        // 認定項目1-7（歩行） */
        insertCpn4.setBango1C7(CommonDtoUtil.strValToInt(basicAction4Info.getBango17()));
        // 認定項目1-8（立ち上がり） */
        insertCpn4.setBango1C8(CommonDtoUtil.strValToInt(basicAction4Info.getBango18()));
        // 認定項目1-9（片足での立位保持） */
        insertCpn4.setBango1C9(CommonDtoUtil.strValToInt(basicAction4Info.getBango19()));
        // 認定項目1-10（洗身） */
        insertCpn4.setBango1C10(CommonDtoUtil.strValToInt(basicAction4Info.getBango110()));
        // 認定項目1-11（つめ切り） */
        insertCpn4.setBango1C11(CommonDtoUtil.strValToInt(basicAction4Info.getBango111()));
        // 認定項目1-12（視力） */
        insertCpn4.setBango1C12(CommonDtoUtil.strValToInt(basicAction4Info.getBango112()));
        // 認定項目1-13（聴力） */
        insertCpn4.setBango1C13(CommonDtoUtil.strValToInt(basicAction4Info.getBango113()));
        // 認定項目1-14_1(関節の動き（複数可）ない） */
        insertCpn4.setBango1C14I1(CommonDtoUtil.strValToInt(basicAction4Info.getBango1141()));
        // 認定項目1-14_2(関節の動き（複数可）肩関節） */
        insertCpn4.setBango1C14I2(CommonDtoUtil.strValToInt(basicAction4Info.getBango1142()));
        // 認定項目1-14_3(関節の動き（複数可）肘関節） */
        insertCpn4.setBango1C14I3(CommonDtoUtil.strValToInt(basicAction4Info.getBango1143()));
        // 認定項目1-14_4(関節の動き（複数可）股関節） */
        insertCpn4.setBango1C14I4(CommonDtoUtil.strValToInt(basicAction4Info.getBango1144()));
        // 認定項目1-14_5(関節の動き（複数可）膝関節） */
        insertCpn4.setBango1C14I5(CommonDtoUtil.strValToInt(basicAction4Info.getBango1145()));
        // 認定項目1-14_6(関節の動き（複数可）足関節） */
        insertCpn4.setBango1C14I6(CommonDtoUtil.strValToInt(basicAction4Info.getBango1146()));
        // 認定項目1-14_7(関節の動き（複数可）その他（四肢の欠損）） */
        insertCpn4.setBango1C14I7(CommonDtoUtil.strValToInt(basicAction4Info.getBango1147()));
        // 家族実施1-1_1（体位変換介助） */
        insertCpn4.setFamJisshi111(CommonDtoUtil.strValToInt(basicAction4Info.getFamJisshi111()));
        // 家族実施1-1_2（起居介助） */
        insertCpn4.setFamJisshi112(CommonDtoUtil.strValToInt(basicAction4Info.getFamJisshi112()));
        // 家族実施1-10_1（準備後始末） */
        insertCpn4.setFamJisshi1101(CommonDtoUtil.strValToInt(basicAction4Info.getFamJisshi1101()));
        // 家族実施1-10_2（移乗移動介助） */
        insertCpn4.setFamJisshi1102(CommonDtoUtil.strValToInt(basicAction4Info.getFamJisshi1102()));
        // 家族実施1-10_3（洗身介助） */
        insertCpn4.setFamJisshi1103(CommonDtoUtil.strValToInt(basicAction4Info.getFamJisshi1103()));
        // 家族実施1-10_4（洗髪介助） */
        insertCpn4.setFamJisshi1104(CommonDtoUtil.strValToInt(basicAction4Info.getFamJisshi1104()));
        // 家族実施1-10_5（清拭部分浴） */
        insertCpn4.setFamJisshi1105(CommonDtoUtil.strValToInt(basicAction4Info.getFamJisshi1105()));
        // 家族実施1-10_6（褥瘡皮膚疾患の対応） */
        insertCpn4.setFamJisshi1106(CommonDtoUtil.strValToInt(basicAction4Info.getFamJisshi1106()));
        // サービス実施1-1_1（体位変換介助） */
        insertCpn4.setSerJisshi111(CommonDtoUtil.strValToInt(basicAction4Info.getSerJisshi111()));
        // サービス実施1-1_2（起居介助） */
        insertCpn4.setSerJisshi112(CommonDtoUtil.strValToInt(basicAction4Info.getSerJisshi112()));
        // サービス実施1-10_1（準備後始末） */
        insertCpn4.setSerJisshi1101(CommonDtoUtil.strValToInt(basicAction4Info.getSerJisshi1101()));
        // サービス実施1-10_2（移乗移動介助） */
        insertCpn4.setSerJisshi1102(CommonDtoUtil.strValToInt(basicAction4Info.getSerJisshi1102()));
        // サービス実施1-10_3（洗身介助） */
        insertCpn4.setSerJisshi1103(CommonDtoUtil.strValToInt(basicAction4Info.getSerJisshi1103()));
        // サービス実施1-10_4（洗髪介助） */
        insertCpn4.setSerJisshi1104(CommonDtoUtil.strValToInt(basicAction4Info.getSerJisshi1104()));
        // サービス実施1-10_5（清拭部分浴） */
        insertCpn4.setSerJisshi1105(CommonDtoUtil.strValToInt(basicAction4Info.getSerJisshi1105()));
        // サービス実施1-10_6（褥瘡皮膚疾患の対応） */
        insertCpn4.setSerJisshi1106(CommonDtoUtil.strValToInt(basicAction4Info.getSerJisshi1106()));
        // 希望1-1_1（体位変換介助） */
        insertCpn4.setKibo111(CommonDtoUtil.strValToInt(basicAction4Info.getKibo111()));
        // 希望1-1_2（起居介助） */
        insertCpn4.setKibo112(CommonDtoUtil.strValToInt(basicAction4Info.getKibo112()));
        // 希望1-10_1（準備後始末） */
        insertCpn4.setKibo1101(CommonDtoUtil.strValToInt(basicAction4Info.getKibo1101()));
        // 希望1-10_2（移乗移動介助） */
        insertCpn4.setKibo1102(CommonDtoUtil.strValToInt(basicAction4Info.getKibo1102()));
        // 希望1-10_3（洗身介助） */
        insertCpn4.setKibo1103(CommonDtoUtil.strValToInt(basicAction4Info.getKibo1103()));
        // 希望1-10_4（洗髪介助） */
        insertCpn4.setKibo1104(CommonDtoUtil.strValToInt(basicAction4Info.getKibo1104()));
        // 希望1-10_5（清拭部分浴） */
        insertCpn4.setKibo1105(CommonDtoUtil.strValToInt(basicAction4Info.getKibo1105()));
        // 希望1-10_6（褥瘡皮膚疾患の対応） */
        insertCpn4.setKibo1106(CommonDtoUtil.strValToInt(basicAction4Info.getKibo1106()));
        // 要援助計画1-1_1（体位変換介助） */
        insertCpn4.setKeikaku111(CommonDtoUtil.strValToInt(basicAction4Info.getKeikaku111()));
        // 要援助計画1-1_2（起居介助） */
        insertCpn4.setKeikaku112(CommonDtoUtil.strValToInt(basicAction4Info.getKeikaku112()));
        // 要援助計画1-10_1（準備後始末） */
        insertCpn4.setKeikaku1101(CommonDtoUtil.strValToInt(basicAction4Info.getKeikaku1101()));
        // 要援助計画1-10_2（移乗移動介助） */
        insertCpn4.setKeikaku1102(CommonDtoUtil.strValToInt(basicAction4Info.getKeikaku1102()));
        // 要援助計画1-10_3（洗身介助） */
        insertCpn4.setKeikaku1103(CommonDtoUtil.strValToInt(basicAction4Info.getKeikaku1103()));
        // 要援助計画1-10_4（洗髪介助） */
        insertCpn4.setKeikaku1104(CommonDtoUtil.strValToInt(basicAction4Info.getKeikaku1104()));
        // 要援助計画1-10_5（清拭部分浴） */
        insertCpn4.setKeikaku1105(CommonDtoUtil.strValToInt(basicAction4Info.getKeikaku1105()));
        // 要援助計画1-10_6（褥瘡皮膚疾患の対応） */
        insertCpn4.setKeikaku1106(CommonDtoUtil.strValToInt(basicAction4Info.getKeikaku1106()));
        // リハビリの必要性 */
        insertCpn4.setHitsuyoSei(CommonDtoUtil.strValToInt(basicAction4Info.getHitsuyoSei()));
        // 現状(移乗移動）1 */
        insertCpn4.setIGenjo1(CommonDtoUtil.strValToInt(basicAction4Info.getIGenjo1()));
        // 現状(移乗移動）2 */
        insertCpn4.setIGenjo2(CommonDtoUtil.strValToInt(basicAction4Info.getIGenjo2()));
        // 計画（移乗移動）1 */
        insertCpn4.setIKeikaku1(CommonDtoUtil.strValToInt(basicAction4Info.getIKeikaku1()));
        // 計画（移乗移動）2 */
        insertCpn4.setIKeikaku2(CommonDtoUtil.strValToInt(basicAction4Info.getIKeikaku2()));
        // 現状(洗身）1 */
        insertCpn4.setSGenjo1(CommonDtoUtil.strValToInt(basicAction4Info.getSGenjo1()));
        // 現状(洗身）2 */
        insertCpn4.setSGenjo2(CommonDtoUtil.strValToInt(basicAction4Info.getSGenjo2()));
        // 計画（洗身）1 */
        insertCpn4.setSKeikaku1(CommonDtoUtil.strValToInt(basicAction4Info.getSKeikaku1()));
        // 計画（洗身）2 */
        insertCpn4.setSKeikaku2(CommonDtoUtil.strValToInt(basicAction4Info.getSKeikaku2()));
        // 眼鏡使用 */
        insertCpn4.setEyesShu1(CommonDtoUtil.strValToInt(basicAction4Info.getEyesShu1()));
        // コンタクト使用 */
        insertCpn4.setEyesShu2(CommonDtoUtil.strValToInt(basicAction4Info.getEyesShu2()));
        // 補聴器使用 */
        insertCpn4.setEyesShu3(CommonDtoUtil.strValToInt(basicAction4Info.getEyesShu3()));
        // 電話 */
        insertCpn4.setTelUmu(CommonDtoUtil.strValToInt(basicAction4Info.getTelUmu()));
        // 言語障害有無 */
        insertCpn4.setGengoUmu(CommonDtoUtil.strValToInt(basicAction4Info.getGengoUmu()));
        // 言語障害名 */
        insertCpn4.setGengoKnj(basicAction4Info.getGengoKnj());
        // コミュニケーション器具有無 */
        insertCpn4.setSiyouKiguUmu(CommonDtoUtil.strValToInt(basicAction4Info.getSiyouKiguUmu()));
        // 使用器具名 */
        insertCpn4.setSiyouKiguMeiKnj(basicAction4Info.getSiyouKiguMeiKnj());
        // 特記1（体位変換起居） */
        insertCpn4.setMemo1Knj(basicAction4Info.getMemo1Knj());
        // 特記2（入浴） */
        insertCpn4.setMemo2Knj(basicAction4Info.getMemo2Knj());
        // 特記3（コミュニケーション） */
        insertCpn4.setMemo3Knj(basicAction4Info.getMemo3Knj());

        return insertCpn4;

    }

    /**
     * 【ＧＬ＿①基本（身体機能・起居）動作（R３改訂）】情報を登録パラメータを作成
     * 
     * @param basicAction4Info 基本動作改訂フラグ4情報
     * @return 【ＧＬ＿①基本（身体機能・起居）動作（R３改訂）】情報を登録パラメータ
     */
    private CpnTucGdl5Kan11R3 setInsertCpn5(Gui00799BasicActionNinteiFlg5Info basicAction5Info) {
        CpnTucGdl5Kan11R3 insertCpn5 = new CpnTucGdl5Kan11R3();

        // 認定項目1-1_1 */
        insertCpn5.setBango1C1I1(CommonDtoUtil.strValToInt(basicAction5Info.getBango01011()));
        // 認定項目1-1_2 */
        insertCpn5.setBango1C1I2(CommonDtoUtil.strValToInt(basicAction5Info.getBango01012()));
        // 認定項目1-1_3 */
        insertCpn5.setBango1C1I3(CommonDtoUtil.strValToInt(basicAction5Info.getBango01013()));
        // 認定項目1-1_4 */
        insertCpn5.setBango1C1I4(CommonDtoUtil.strValToInt(basicAction5Info.getBango114()));
        // 認定項目1-1_5 */
        insertCpn5.setBango1C1I5(CommonDtoUtil.strValToInt(basicAction5Info.getBango115()));
        // 認定項目1-1_6 */
        insertCpn5.setBango1C1I6(CommonDtoUtil.strValToInt(basicAction5Info.getBango116()));
        // 認定項目1-2_1 */
        insertCpn5.setBango1C2I1(CommonDtoUtil.strValToInt(basicAction5Info.getBango121()));
        // 認定項目1-2_2 */
        insertCpn5.setBango1C2I2(CommonDtoUtil.strValToInt(basicAction5Info.getBango122()));
        // 認定項目1-2_3 */
        insertCpn5.setBango1C2I3(CommonDtoUtil.strValToInt(basicAction5Info.getBango123()));
        // 認定項目1-2_4 */
        insertCpn5.setBango1C2I4(CommonDtoUtil.strValToInt(basicAction5Info.getBango124()));
        // 認定項目1-2_5 */
        insertCpn5.setBango1C2I5(CommonDtoUtil.strValToInt(basicAction5Info.getBango125()));
        // 認定項目1-3 */
        insertCpn5.setBango1C3(CommonDtoUtil.strValToInt(basicAction5Info.getBango13()));
        // 認定項目1-4 */
        insertCpn5.setBango1C4(CommonDtoUtil.strValToInt(basicAction5Info.getBango14()));
        // 認定項目1-5 */
        insertCpn5.setBango1C5(CommonDtoUtil.strValToInt(basicAction5Info.getBango15()));
        // 認定項目1-6 */
        insertCpn5.setBango1C6(CommonDtoUtil.strValToInt(basicAction5Info.getBango16()));
        // 認定項目1-7 */
        insertCpn5.setBango1C7(CommonDtoUtil.strValToInt(basicAction5Info.getBango17()));
        // 認定項目1-8 */
        insertCpn5.setBango1C8(CommonDtoUtil.strValToInt(basicAction5Info.getBango18()));
        // 認定項目1-9 */
        insertCpn5.setBango1C9(CommonDtoUtil.strValToInt(basicAction5Info.getBango19()));
        // 認定項目1-10 */
        insertCpn5.setBango1C10(CommonDtoUtil.strValToInt(basicAction5Info.getBango110()));
        // 認定項目1-11 */
        insertCpn5.setBango1C11(CommonDtoUtil.strValToInt(basicAction5Info.getBango111()));
        // 認定項目1-12 */
        insertCpn5.setBango1C12(CommonDtoUtil.strValToInt(basicAction5Info.getBango112()));
        // 認定項目1-13 */
        insertCpn5.setBango1C13(CommonDtoUtil.strValToInt(basicAction5Info.getBango113()));
        // 認定項目1-14_1 */
        insertCpn5.setBango1C14I1(CommonDtoUtil.strValToInt(basicAction5Info.getBango1141()));
        // 認定項目1-14_2 */
        insertCpn5.setBango1C14I2(CommonDtoUtil.strValToInt(basicAction5Info.getBango1142()));
        // 認定項目1-14_3 */
        insertCpn5.setBango1C14I3(CommonDtoUtil.strValToInt(basicAction5Info.getBango1143()));
        // 認定項目1-14_4 */
        insertCpn5.setBango1C14I4(CommonDtoUtil.strValToInt(basicAction5Info.getBango1144()));
        // 認定項目1-14_5 */
        insertCpn5.setBango1C14I5(CommonDtoUtil.strValToInt(basicAction5Info.getBango1145()));
        // 認定項目1-14_6 */
        insertCpn5.setBango1C14I6(CommonDtoUtil.strValToInt(basicAction5Info.getBango1146()));
        // 認定項目1-14_7 */
        insertCpn5.setBango1C14I7(CommonDtoUtil.strValToInt(basicAction5Info.getBango1147()));
        // 家族実施111 */
        insertCpn5.setFamJisshi111(CommonDtoUtil.strValToInt(basicAction5Info.getFamJisshi111()));
        // 家族実施112 */
        insertCpn5.setFamJisshi112(CommonDtoUtil.strValToInt(basicAction5Info.getFamJisshi112()));
        // 家族実施1101 */
        insertCpn5.setFamJisshi1101(CommonDtoUtil.strValToInt(basicAction5Info.getFamJisshi1101()));
        // 家族実施1102 */
        insertCpn5.setFamJisshi1102(CommonDtoUtil.strValToInt(basicAction5Info.getFamJisshi1102()));
        // 家族実施1103 */
        insertCpn5.setFamJisshi1103(CommonDtoUtil.strValToInt(basicAction5Info.getFamJisshi1103()));
        // 家族実施1104 */
        insertCpn5.setFamJisshi1104(CommonDtoUtil.strValToInt(basicAction5Info.getFamJisshi1104()));
        // 家族実施1105 */
        insertCpn5.setFamJisshi1105(CommonDtoUtil.strValToInt(basicAction5Info.getFamJisshi1105()));
        // 家族実施1106 */
        insertCpn5.setFamJisshi1106(CommonDtoUtil.strValToInt(basicAction5Info.getFamJisshi1106()));
        // サービス実施1-1_1 */
        insertCpn5.setSerJisshi111(CommonDtoUtil.strValToInt(basicAction5Info.getSerJisshi111()));
        // サービス実施1-1_2 */
        insertCpn5.setSerJisshi112(CommonDtoUtil.strValToInt(basicAction5Info.getSerJisshi112()));
        // 家族実施1-10_1 */
        insertCpn5.setSerJisshi1101(CommonDtoUtil.strValToInt(basicAction5Info.getSerJisshi1101()));
        // 家族実施1-10_2 */
        insertCpn5.setSerJisshi1102(CommonDtoUtil.strValToInt(basicAction5Info.getSerJisshi1102()));
        // 家族実施1-10_3 */
        insertCpn5.setSerJisshi1103(CommonDtoUtil.strValToInt(basicAction5Info.getSerJisshi1103()));
        // 家族実施1-10_4 */
        insertCpn5.setSerJisshi1104(CommonDtoUtil.strValToInt(basicAction5Info.getSerJisshi1104()));
        // 家族実施1-10_5 */
        insertCpn5.setSerJisshi1105(CommonDtoUtil.strValToInt(basicAction5Info.getSerJisshi1105()));
        // 家族実施1-10_6 */
        insertCpn5.setSerJisshi1106(CommonDtoUtil.strValToInt(basicAction5Info.getSerJisshi1106()));
        // 希望1-1_1 */
        insertCpn5.setKibo111(CommonDtoUtil.strValToInt(basicAction5Info.getKibo111()));
        // 希望1-1_2 */
        insertCpn5.setKibo112(CommonDtoUtil.strValToInt(basicAction5Info.getKibo112()));
        // 希望1-10_1 */
        insertCpn5.setKibo1101(CommonDtoUtil.strValToInt(basicAction5Info.getKibo1101()));
        // 希望1-10_2 */
        insertCpn5.setKibo1102(CommonDtoUtil.strValToInt(basicAction5Info.getKibo1102()));
        // 希望1-10_3 */
        insertCpn5.setKibo1103(CommonDtoUtil.strValToInt(basicAction5Info.getKibo1103()));
        // 希望1-10_4 */
        insertCpn5.setKibo1104(CommonDtoUtil.strValToInt(basicAction5Info.getKibo1104()));
        // 希望1-10_5 */
        insertCpn5.setKibo1105(CommonDtoUtil.strValToInt(basicAction5Info.getKibo1105()));
        // 希望1-10_6 */
        insertCpn5.setKibo1106(CommonDtoUtil.strValToInt(basicAction5Info.getKibo1106()));
        // 要援助・計画1-1_1 */
        insertCpn5.setKeikaku111(CommonDtoUtil.strValToInt(basicAction5Info.getKeikaku111()));
        // 要援助・計画1-1_2 */
        insertCpn5.setKeikaku112(CommonDtoUtil.strValToInt(basicAction5Info.getKeikaku112()));
        // 要援助・計画1-10_1 */
        insertCpn5.setKeikaku1101(CommonDtoUtil.strValToInt(basicAction5Info.getKeikaku1101()));
        // 要援助・計画1-10_2 */
        insertCpn5.setKeikaku1102(CommonDtoUtil.strValToInt(basicAction5Info.getKeikaku1102()));
        // 要援助・計画1-10_3 */
        insertCpn5.setKeikaku1103(CommonDtoUtil.strValToInt(basicAction5Info.getKeikaku1103()));
        // 要援助・計画1-10_4 */
        insertCpn5.setKeikaku1104(CommonDtoUtil.strValToInt(basicAction5Info.getKeikaku1104()));
        // 要援助・計画1-10_5 */
        insertCpn5.setKeikaku1105(CommonDtoUtil.strValToInt(basicAction5Info.getKeikaku1105()));
        // 要援助・計画1-10_6 */
        insertCpn5.setKeikaku1106(CommonDtoUtil.strValToInt(basicAction5Info.getKeikaku1106()));
        // リハビリの必要性 */
        insertCpn5.setHitsuyoSei(CommonDtoUtil.strValToInt(basicAction5Info.getHitsuyoSei()));
        // 現状(移乗移動）1 */
        insertCpn5.setIGenjo1(CommonDtoUtil.strValToInt(basicAction5Info.getIGenjo1()));
        // 現状(移乗移動）2 */
        insertCpn5.setIGenjo2(CommonDtoUtil.strValToInt(basicAction5Info.getIGenjo2()));
        // 計画（移乗移動）1 */
        insertCpn5.setIKeikaku1(CommonDtoUtil.strValToInt(basicAction5Info.getIKeikaku1()));
        // 計画（移乗移動）2 */
        insertCpn5.setIKeikaku2(CommonDtoUtil.strValToInt(basicAction5Info.getIKeikaku2()));
        // 現状(洗身）1 */
        insertCpn5.setSGenjo1(CommonDtoUtil.strValToInt(basicAction5Info.getSGenjo1()));
        // 現状(洗身）2 */
        insertCpn5.setSGenjo2(CommonDtoUtil.strValToInt(basicAction5Info.getSGenjo2()));
        // 計画（洗身）1 */
        insertCpn5.setSKeikaku1(CommonDtoUtil.strValToInt(basicAction5Info.getSKeikaku1()));
        // 計画（洗身）2 */
        insertCpn5.setSKeikaku2(CommonDtoUtil.strValToInt(basicAction5Info.getSKeikaku2()));
        // 眼鏡使用 */
        insertCpn5.setEyesShu1(CommonDtoUtil.strValToInt(basicAction5Info.getEyesShu1()));
        // コンタクト使用 */
        insertCpn5.setEyesShu2(CommonDtoUtil.strValToInt(basicAction5Info.getEyesShu2()));
        // 補聴器使用 */
        insertCpn5.setEyesShu3(CommonDtoUtil.strValToInt(basicAction5Info.getEyesShu3()));
        // 電話 */
        insertCpn5.setTelUmu(CommonDtoUtil.strValToInt(basicAction5Info.getTelUmu()));
        // 言語障害有無 */
        insertCpn5.setGengoUmu(CommonDtoUtil.strValToInt(basicAction5Info.getGengoUmu()));
        // 言語障害名 */
        insertCpn5.setGengoKnj(basicAction5Info.getGengoKnj());
        // コミュニケーション器具有無 */
        insertCpn5.setSiyouKiguUmu(CommonDtoUtil.strValToInt(basicAction5Info.getSiyouKiguUmu()));
        // 使用器具名 */
        insertCpn5.setSiyouKiguMeiKnj(basicAction5Info.getSiyouKiguMeiKnj());
        // 特記1 */
        insertCpn5.setMemo1Knj(basicAction5Info.getMemo1Knj());
        // 特記2 */
        insertCpn5.setMemo2Knj(basicAction5Info.getMemo2Knj());
        // 特記3 */
        insertCpn5.setMemo3Knj(basicAction5Info.getMemo3Knj());

        return insertCpn5;

    }
}
