package jp.ndsoft.carebase.cmn.api.service;

import java.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.lang.invoke.MethodHandles;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00805CpnMucGdlKoumoku;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00805Gdl4NeceH21;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00805Gdl4NeceR3;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeTab7MatomeSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeTab7MatomeSelectServiceOutDto;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl4Ass122H21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl4Ass122H30ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl4Ass122H30OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl4Ass122H21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4NeceH21ListByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4NeceH21ListOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucGdlKoumokuSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4NeceH21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4NeceH21FewItemListByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4NeceH21FewItemListOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass12R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass12R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass122R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass122R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5NeceR3InfoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5NeceR3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.Gdl4Ass122H21InfoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.Gdl4Ass122H30InfoSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMucGdlKoumokuOutEntity;
import jp.ndsoft.carebase.common.dao.mybatis.CpnMucGdlKoumokuMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnMucGdlKoumoku;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMucGdlKoumokuByCriteriaInEntity;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;

/**
 * @since 2025.04.11
 * <AUTHOR>
 * @implNote GUI00805_［アセスメント］画面（居宅）（7まとめ）初期情報取得
 */

@Service
public class AssessmentHomeTab7MatomeSelectServiceImpl extends
        SelectServiceImpl<AssessmentHomeTab7MatomeSelectServiceInDto, AssessmentHomeTab7MatomeSelectServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報取得 **/
    @Autowired
    private Gdl4Ass122H30InfoSelectMapper gdl4Ass122H30InfoSelectMapper;
    /** ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報取得 **/
    @Autowired
    private Gdl4Ass122H21InfoSelectMapper gdl4Ass122H21InfoSelectMapper;
    /** ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報を取得する **/
    @Autowired
    private CpnTucGdl4NeceH21SelectMapper cpnTucGdl4NeceH21SelectMapper;
    /** ＧＬ＿全体のまとめ・特記事項（Ｒ3改訂）情報を取得する **/
    @Autowired
    private CpnTucGdl5NeceR3InfoSelectMapper cpnTucGdl5NeceR3InfoSelectMapper;
    /** ＧＬ＿全体のまとめ・特記事項（Ｒ3改訂）情報を取得する **/
    @Autowired
    private CpnTucGdl5NeceR3SelectMapper cpnTucGdl5NeceR3SelectMapper;
    /** 状況別まとめマスタ情報取得 **/
    @Autowired
    private CpnMucGdlKoumokuSelectMapper cpnMucGdlKoumokuSelectMapper;
    /** ＧＬ＿状況別まとめマスタ **/
    @Autowired
    private CpnMucGdlKoumokuMapper cpnMucGdlKoumokuMapper;

    /**
     * ［アセスメント］画面（居宅）（7まとめ）初期情報取得
     * 
     * @param inDto ［アセスメント］画面（居宅）（7まとめ）入力Dto
     * @return ［アセスメント］画面（居宅）（7まとめ）出力DTO
     */
    @Override
    protected AssessmentHomeTab7MatomeSelectServiceOutDto mainProcess(
            final AssessmentHomeTab7MatomeSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        // 戻り情報初期化
        // 状況別まとめマスタ情報リスト
        List<Gui00805CpnMucGdlKoumoku> cpnMucGdlKoumokuList = new ArrayList<Gui00805CpnMucGdlKoumoku>();
        // ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト
        List<Gui00805Gdl4NeceH21> gdl4NeceH21List = new ArrayList<Gui00805Gdl4NeceH21>();
        // ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト
        List<Gui00805Gdl4NeceR3> gdl4NeceR3List = new ArrayList<Gui00805Gdl4NeceR3>();
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. リクエストパラメータ.改定フラグが4「H21/４改訂版」の場合===============
         * 
         */
        if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
            // リクエストパラメータ.改定フラグが4「H21/４改訂版」の場合
            if (CommonConstants.MATOME_FLAG_SITUATION_INPUT.equals(inDto.getMatomeFlg())) {
                // 2.1 リクエストパラメータ.まとめフラグが2「状況別入力」の場合、
                if (CommonConstants.CREATION_DATE_20180401.compareTo(inDto.getKijunbiYmd()) <= 0) {
                    // 2.1.1 リクエストパラメータ.作成日>='2018/04/01'の場合、
                    // ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）DAOを利用し、ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報を取得する

                    // DAOパラメータを作成
                    Gdl4Ass122H30ByCriteriaInEntity gdl4Ass122H30ByCriteriaEntity = new Gdl4Ass122H30ByCriteriaInEntity();
                    // アセスメントID
                    gdl4Ass122H30ByCriteriaEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
                    // 計画期間ID
                    gdl4Ass122H30ByCriteriaEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
                    // ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報を取得する
                    List<Gdl4Ass122H30OutEntity> gdl4Ass122H30ByCriteria1List = this.gdl4Ass122H30InfoSelectMapper
                            .findGdl4Ass122H30ByCriteria(gdl4Ass122H30ByCriteriaEntity);
                    // 取得した初期設定情報を繰り返して、下記処理を行う
                    this.processGLSummaryAndNotesList1(gdl4Ass122H30ByCriteria1List, gdl4NeceH21List);

                } else {
                    // ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報取得DAOを利用し、ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報を取得する
                    // DAOパラメータを作成
                    Gdl4Ass122H21ByCriteriaInEntity gdl4Ass122H21ByCriteriaInEntity = new Gdl4Ass122H21ByCriteriaInEntity();
                    // アセスメントID
                    gdl4Ass122H21ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
                    // 計画期間ID
                    gdl4Ass122H21ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
                    // ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報を取得する
                    List<Gdl4Ass122H21OutEntity> gdl4Ass122H21OutEntityList = this.gdl4Ass122H21InfoSelectMapper
                            .findGdl4Ass122H21ByCriteria(gdl4Ass122H21ByCriteriaInEntity);
                    // 取得した初期設定情報を繰り返して、下記処理を行う
                    this.processGLSummaryAndNotesList2(gdl4Ass122H21OutEntityList, gdl4NeceH21List);

                }
            } else {
                // 2.2 リクエストパラメータ.まとめフラグが2「状況別入力」以外の場合、
                if (CommonConstants.CREATION_DATE_20180401.compareTo(inDto.getKijunbiYmd()) <= 0) {
                    // 2.2.1 リクエストパラメータ.作成日>='2018/04/01'の場合、
                    // ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）DAOを利用し、ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報を取得する
                    CpnTucGdl4NeceH21ListByCriteriaInEntity cpnTucGdl4NeceH21ListByCriteriaInEntity = new CpnTucGdl4NeceH21ListByCriteriaInEntity();
                    // アセスメントID
                    cpnTucGdl4NeceH21ListByCriteriaInEntity
                            .setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
                    // 計画期間ID
                    cpnTucGdl4NeceH21ListByCriteriaInEntity
                            .setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
                    // ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報を取得する
                    List<CpnTucGdl4NeceH21ListOutEntity> cpnTucGdl4NeceH21ListOutList = this.cpnTucGdl4NeceH21SelectMapper
                            .findCpnTucGdl4NeceH21ListByCriteria(cpnTucGdl4NeceH21ListByCriteriaInEntity);
                    // 取得した初期設定情報を繰り返して、下記処理を行う
                    this.processGLSummaryAndNotesList3(cpnTucGdl4NeceH21ListOutList, gdl4NeceH21List);

                } else {
                    // ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報取得DAOを利用し、ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報を取得する
                    CpnTucGdl4NeceH21FewItemListByCriteriaInEntity cpnTucGdl4NeceH21FewItemListByCriteriaInEntity = new CpnTucGdl4NeceH21FewItemListByCriteriaInEntity();
                    cpnTucGdl4NeceH21FewItemListByCriteriaInEntity
                            .setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
                    cpnTucGdl4NeceH21FewItemListByCriteriaInEntity
                            .setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
                    List<CpnTucGdl4NeceH21FewItemListOutEntity> cpnTucGdl4NeceH21FewItemListOutList = this.cpnTucGdl4NeceH21SelectMapper
                            .findCpnTucGdl4NeceH21FewItemListByCriteria(cpnTucGdl4NeceH21FewItemListByCriteriaInEntity);
                    // 取得した初期設定情報を繰り返して、下記処理を行う
                    this.processGLSummaryAndNotesList4(cpnTucGdl4NeceH21FewItemListOutList, gdl4NeceH21List);

                }
            }
        } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
            /*
             * ===============3. リクエストパラメータ.改定フラグが5「R3/４改訂版」の場合===============
             * 
             */
            // 3. リクエストパラメータ.改定フラグが5「R3/４改訂版」の場合
            if (CommonConstants.MATOME_FLAG_SITUATION_INPUT.equals(inDto.getMatomeFlg())) {
                // 3.1 リクエストパラメータ.まとめフラグが2「状況別入力」の場合、
                // 3.1.1 ＧＬ＿全体のまとめ・特記事項（Ｒ3改訂）DAOを利用し、ＧＬ＿全体のまとめ・特記事項（Ｒ3改訂）情報を取得する
                Gdl5Ass122R3ByCriteriaInEntity gdl5Ass122R3ByCriteriaInEntity = new Gdl5Ass122R3ByCriteriaInEntity();
                // 計画期間ID
                gdl5Ass122R3ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
                // アセスメントID
                gdl5Ass122R3ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
                List<Gdl5Ass122R3OutEntity> gdl5Ass122R3OutEntityList = this.cpnTucGdl5NeceR3InfoSelectMapper
                        .findGdl5Ass122R3ByCriteria(gdl5Ass122R3ByCriteriaInEntity);
                // 取得した初期設定情報を繰り返して、下記処理を行う
                this.processGLSummaryAndNotesR3List(gdl5Ass122R3OutEntityList, gdl4NeceR3List);
            } else {
                // 3.2 リクエストパラメータ.まとめフラグが2「状況別入力」以外の場合、
                // 3.2.1 ＧＬ＿全体のまとめ・特記事項（Ｒ3改訂）DAOを利用し、ＧＬ＿全体のまとめ・特記事項（Ｒ3改訂）情報を取得する
                // 3.1.1 ＧＬ＿全体のまとめ・特記事項（Ｒ3改訂）DAOを利用し、ＧＬ＿全体のまとめ・特記事項（Ｒ3改訂）情報を取得する
                Gdl5Ass12R3ByCriteriaInEntity gdl5Ass12R3ByCriteriaInEntity = new Gdl5Ass12R3ByCriteriaInEntity();
                // 計画期間ID
                gdl5Ass12R3ByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
                // アセスメントID
                gdl5Ass12R3ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
                List<Gdl5Ass12R3OutEntity> gdl5Ass12R3OutEntityList = this.cpnTucGdl5NeceR3SelectMapper
                        .findGdl5Ass12R3ByCriteria(gdl5Ass12R3ByCriteriaInEntity);
                // 取得した初期設定情報を繰り返して、下記処理を行う
                this.processGLSummaryAndNotesR3List2(gdl5Ass12R3OutEntityList, gdl4NeceR3List);

            }
        }
        /*
         * ========4.「API定義書_APINo(194)_新規情報取得」を参照して、状況別まとめマスタ情報を取得する。=========
         * 
         */
        // 状況別まとめマスタ取得情報DAOを利用し、状況別まとめマスタ情報を取得する。
        CpnMucGdlKoumokuByCriteriaInEntity cpnMucGdlKoumokuByCriteriaInEntity = new CpnMucGdlKoumokuByCriteriaInEntity();
        // リクエストパラメータ.法人ID
        cpnMucGdlKoumokuByCriteriaInEntity.setHid(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // リクエストパラメータ.施設ID
        cpnMucGdlKoumokuByCriteriaInEntity.setSid(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // リクエストパラメータ.事業者ID
        cpnMucGdlKoumokuByCriteriaInEntity.setJid(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 状況別まとめマスタ
        List<CpnMucGdlKoumokuOutEntity> cpnMucGdlKoumokuOutEntityList = new ArrayList<>();

        for (int statusSummaryMasterFlag = 0; statusSummaryMasterFlag < 1;) {
            // 状況別まとめマスタ処理flg=0 データなし
            // 状況別まとめマスタ情報を取得する。
            cpnMucGdlKoumokuOutEntityList = this.cpnMucGdlKoumokuSelectMapper
                    .findCpnMucGdlKoumokuByCriteria(cpnMucGdlKoumokuByCriteriaInEntity);
            if (CollectionUtils.isEmpty(cpnMucGdlKoumokuOutEntityList)) {
                // 上記情報取得できない場合、

                for (int i = 0; i < CommonConstants.MASTER_REGISTRATION_ID_LIST.size();) {
                    CpnMucGdlKoumoku cpnMucGdlKoumoku = new CpnMucGdlKoumoku();

                    // マスタＩＤ
                    cpnMucGdlKoumoku.setMastId(
                            CommonDtoUtil.strValToInt(CommonConstants.MASTER_REGISTRATION_ID_LIST.get(i)));
                    // 法人ID
                    cpnMucGdlKoumoku.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
                    // 施設ID
                    cpnMucGdlKoumoku.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                    // 事業者ID
                    cpnMucGdlKoumoku.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                    // 項目
                    cpnMucGdlKoumoku.setKoumokuKnj(CommonConstants.MASTER_REGISTRATION_ITEM_LIST.get(i));
                    // 中止フラグ
                    cpnMucGdlKoumoku.setStopFlg(CommonDtoUtil.strValToInt(CommonConstants.STOP_FLG_0));
                    // ロックフラグ
                    cpnMucGdlKoumoku.setLockFlg(CommonConstants.NUMBER_ONE);
                    // 表示順
                    i++;
                    cpnMucGdlKoumoku.setSort(i);

                    // 状況別まとめマスタ登録情報を登録する。
                    this.cpnMucGdlKoumokuMapper.insertSelectiveAndReturn(cpnMucGdlKoumoku);
                }
            } else {
                // 状況別まとめマスタ処理flg=1 データあり ループを終了する。
                statusSummaryMasterFlag++;
            }
        }

        cpnMucGdlKoumokuOutEntityList.forEach(item -> {
            if (CommonConstants.STOP_FLG_0.equals(CommonDtoUtil.objValToString(item.getStopFlg()))) {
                // 上記取得した状況別まとめマスタ情報から中止フラグ=0のデータを取得して
                Gui00805CpnMucGdlKoumoku gui00805CpnMucGdlKoumoku = new Gui00805CpnMucGdlKoumoku();
                // カウンター
                gui00805CpnMucGdlKoumoku.setId(CommonDtoUtil.objValToString(item.getId()));
                // マスタID
                gui00805CpnMucGdlKoumoku.setMastId(CommonDtoUtil.objValToString(item.getMastId()));
                // 法人ID
                gui00805CpnMucGdlKoumoku.setHoujinId(CommonDtoUtil.objValToString(item.getHoujinId()));
                // 施設ID
                gui00805CpnMucGdlKoumoku.setShisetuId(CommonDtoUtil.objValToString(item.getShisetuId()));
                // 事業者ID
                gui00805CpnMucGdlKoumoku.setSvJigyoId(CommonDtoUtil.objValToString(item.getSvJigyoId()));
                // 項目
                gui00805CpnMucGdlKoumoku.setKoumokuKnj(item.getKoumokuKnj());
                // 中止フラグ
                gui00805CpnMucGdlKoumoku.setStopFlg(CommonDtoUtil.objValToString(item.getStopFlg()));
                // ロックフラグ
                gui00805CpnMucGdlKoumoku.setLockFlg(CommonDtoUtil.objValToString(item.getLockFlg()));
                // 表示順
                gui00805CpnMucGdlKoumoku.setSort(CommonDtoUtil.objValToString(item.getSort()));
                // 状況別まとめマスタ情報リスト
                cpnMucGdlKoumokuList.add(gui00805CpnMucGdlKoumoku);
            }
        });
        // マスタIDにより昇順をソートする。
        Collections.sort(cpnMucGdlKoumokuList,
                Comparator.comparingInt(item -> CommonDtoUtil.strValToInt(item.getMastId())));
        /*
         * ===============5. 上記処理で取得した結果レスポンスを返却する。===============
         * 
         */

        // 戻り情報を設定
        AssessmentHomeTab7MatomeSelectServiceOutDto outDto = new AssessmentHomeTab7MatomeSelectServiceOutDto();
        // 状況別まとめマスタ情報リスト
        outDto.setCpnMucGdlKoumokuList(cpnMucGdlKoumokuList);
        // ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト
        outDto.setGdl4NeceH21List(gdl4NeceH21List);
        // ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リス
        outDto.setGdl4NeceR3List(gdl4NeceR3List);
        LOG.info(Constants.END);
        return outDto;

    }

    /**
     * ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リストを編集する
     * 
     * @param1 gdl4Ass122H30ByCriteria1List［ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）DAO
     * @param2 gdl4NeceH21List ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト
     */
    private void processGLSummaryAndNotesList1(List<Gdl4Ass122H30OutEntity> gdl4Ass122H30ByCriteria1List,
            List<Gui00805Gdl4NeceH21> gdl4NeceH21List) {
        // 处理 GL_全体のまとめ・特記事項（H21改訂）リスト 的逻辑

        // 取得した初期設定情報を繰り返して、下記処理を行う
        gdl4Ass122H30ByCriteria1List.forEach(item -> {
            // 取得したデータから停止フラグが0のデータを取得して、変数.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リストに設定する
            if (CommonConstants.STOP_FLG_0.equals(CommonDtoUtil.objValToString(item.getStopFlg()))) {
                Gui00805Gdl4NeceH21 gui00805Gdl4NeceH21 = new Gui00805Gdl4NeceH21();
                // アセスメントID
                gui00805Gdl4NeceH21.setGdlId(CommonDtoUtil.objValToString(item.getGdlId()));
                // 計画期間ID
                gui00805Gdl4NeceH21.setSc1Id(CommonDtoUtil.objValToString(item.getSc1Id()));
                // 特記事項
                gui00805Gdl4NeceH21.setMemoKnj(item.getMemoKnj());
                // 必要性1
                gui00805Gdl4NeceH21.setHitsuyo1(CommonDtoUtil.objValToString(item.getHitsuyo1()));
                // 必要性2
                gui00805Gdl4NeceH21.setHitsuyo2(CommonDtoUtil.objValToString(item.getHitsuyo2()));
                // マスタID
                gui00805Gdl4NeceH21.setMastId(CommonDtoUtil.objValToString(item.getMastId()));
                // 法人ID
                gui00805Gdl4NeceH21.setHoujinId(CommonDtoUtil.objValToString(item.getHoujinId()));
                // 施設ID
                gui00805Gdl4NeceH21.setShisetuId(CommonDtoUtil.objValToString(item.getShisetuId()));
                // 事業者ID
                gui00805Gdl4NeceH21.setSvJigyoId(CommonDtoUtil.objValToString(item.getSvJigyoId()));
                // 利用者ID
                gui00805Gdl4NeceH21.setUserId(CommonDtoUtil.objValToString(item.getUserId()));
                // 項目
                gui00805Gdl4NeceH21.setKoumokuKnj(item.getKoumokuKnj());
                // 表示順
                gui00805Gdl4NeceH21.setSort(CommonDtoUtil.objValToString(item.getSort()));
                // カウンター
                gui00805Gdl4NeceH21.setId(CommonDtoUtil.objValToString(item.getId()));
                // 中止フラグ 0：適用 1：中止
                gui00805Gdl4NeceH21.setStopFlg(CommonDtoUtil.objValToString(item.getStopFlg()));
                // 氏名
                gui00805Gdl4NeceH21.setNameKnj(item.getNameKnj());
                // 本人との関係
                gui00805Gdl4NeceH21.setKankeiKnj(item.getKankeiKnj());
                // 電話番号
                gui00805Gdl4NeceH21.setTel(item.getTel());
                // FAX
                gui00805Gdl4NeceH21.setFax(item.getFax());
                // メールアドレス
                gui00805Gdl4NeceH21.setEMail(item.getEMail());
                // 備考（災害時）
                gui00805Gdl4NeceH21.setBikoSaigaiKnj(item.getBikoSaigaiKnj());
                // 備考（権利擁護）
                gui00805Gdl4NeceH21.setBikoKenriKnj(item.getBikoKenriKnj());
                // ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報を追加する。
                gdl4NeceH21List.add(gui00805Gdl4NeceH21);
            }

        });
    }

    /**
     * ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リストを編集する
     * 
     * @param1 gdl4Ass122H21OutEntityList［ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）DAO
     * @param2 gdl4NeceH21List ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト
     */
    private void processGLSummaryAndNotesList2(List<Gdl4Ass122H21OutEntity> gdl4Ass122H21OutEntityList,
            List<Gui00805Gdl4NeceH21> gdl4NeceH21List) {

        gdl4Ass122H21OutEntityList.forEach(item -> {
            // 取得したデータから停止フラグが0のデータを取得して、変数.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リストに設定する
            if (CommonConstants.STOP_FLG_0.equals(CommonDtoUtil.objValToString(item.getStopFlg()))) {
                Gui00805Gdl4NeceH21 gui00805Gdl4NeceH21 = new Gui00805Gdl4NeceH21();
                // アセスメントID
                gui00805Gdl4NeceH21.setGdlId(CommonDtoUtil.objValToString(item.getGdlId()));
                // 計画期間ID
                gui00805Gdl4NeceH21.setSc1Id(CommonDtoUtil.objValToString(item.getSc1Id()));
                // 特記事項
                gui00805Gdl4NeceH21.setMemoKnj(item.getMemoKnj());
                // 必要性1
                gui00805Gdl4NeceH21.setHitsuyo1(CommonDtoUtil.objValToString(item.getHitsuyo1()));
                // 必要性2
                gui00805Gdl4NeceH21.setHitsuyo2(CommonDtoUtil.objValToString(item.getHitsuyo2()));
                // マスタID
                gui00805Gdl4NeceH21.setMastId(CommonDtoUtil.objValToString(item.getMastId()));
                // 法人ID
                gui00805Gdl4NeceH21.setHoujinId(CommonDtoUtil.objValToString(item.getHoujinId()));
                // 施設ID
                gui00805Gdl4NeceH21.setShisetuId(CommonDtoUtil.objValToString(item.getShisetuId()));
                // 事業者ID
                gui00805Gdl4NeceH21.setSvJigyoId(CommonDtoUtil.objValToString(item.getSvJigyoId()));
                // 利用者ID
                gui00805Gdl4NeceH21.setUserId(CommonDtoUtil.objValToString(item.getUserId()));
                // 項目KNJ
                gui00805Gdl4NeceH21.setKoumokuKnj(item.getKoumokuKnj());
                // ソート
                gui00805Gdl4NeceH21.setSort(CommonDtoUtil.objValToString(item.getSort()));
                // ID
                gui00805Gdl4NeceH21.setId(CommonDtoUtil.objValToString(item.getId()));
                // 停止フラグ
                gui00805Gdl4NeceH21.setStopFlg(CommonDtoUtil.objValToString(item.getStopFlg()));
                // dmySelect1
                gui00805Gdl4NeceH21.setDmySelect1(CommonDtoUtil.objValToString(item.getDmySelect1()));
                // dmySelect2
                gui00805Gdl4NeceH21.setDmySelect2(CommonDtoUtil.objValToString(item.getDmySelect2()));
                // ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報を追加する。
                gdl4NeceH21List.add(gui00805Gdl4NeceH21);
            }
        });
    }

    /**
     * ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リストを編集する
     * 
     * @param1 gdl4Ass122H21OutEntityList［ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）DAO
     * @param2 gdl4NeceH21List ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト
     */
    private void processGLSummaryAndNotesList3(List<CpnTucGdl4NeceH21ListOutEntity> cpnTucGdl4NeceH21ListOutList,
            List<Gui00805Gdl4NeceH21> gdl4NeceH21List) {
        cpnTucGdl4NeceH21ListOutList.forEach(item -> {
            // 取得したデータからマスタIDが1のデータを取得して、変数.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リストに設定する
            if (CommonConstants.MAST_ID == item.getMastId()) {
                Gui00805Gdl4NeceH21 gui00805Gdl4NeceH21 = new Gui00805Gdl4NeceH21();
                // アセスメントID
                gui00805Gdl4NeceH21.setGdlId(CommonDtoUtil.objValToString(item.getGdlId()));
                // 計画期間ID
                gui00805Gdl4NeceH21.setSc1Id(CommonDtoUtil.objValToString(item.getSc1Id()));
                // マスタID
                gui00805Gdl4NeceH21.setMastId(CommonDtoUtil.objValToString(item.getMastId()));
                // 法人ID
                gui00805Gdl4NeceH21.setHoujinId(CommonDtoUtil.objValToString(item.getHoujinId()));
                // 施設ID
                gui00805Gdl4NeceH21.setShisetuId(CommonDtoUtil.objValToString(item.getShisetuId()));
                // 事業者ID
                gui00805Gdl4NeceH21.setSvJigyoId(CommonDtoUtil.objValToString(item.getSvJigyoId()));
                // 利用者ID
                gui00805Gdl4NeceH21.setUserId(CommonDtoUtil.objValToString(item.getUserId()));
                // 特記事項
                gui00805Gdl4NeceH21.setMemoKnj(item.getMemoKnj());
                // 必要性1
                gui00805Gdl4NeceH21.setHitsuyo1(CommonDtoUtil.objValToString(item.getHitsuyo1()));
                // 必要性2
                gui00805Gdl4NeceH21.setHitsuyo2(CommonDtoUtil.objValToString(item.getHitsuyo2()));
                // 氏名
                gui00805Gdl4NeceH21.setNameKnj(item.getNameKnj());
                // 本人との関係
                gui00805Gdl4NeceH21.setKankeiKnj(item.getKankeiKnj());
                // 電話番号
                gui00805Gdl4NeceH21.setTel(item.getTel());
                // FAX
                gui00805Gdl4NeceH21.setFax(item.getFax());
                // メールアドレス
                gui00805Gdl4NeceH21.setEMail(item.getEMail());
                // 備考（災害時）
                gui00805Gdl4NeceH21.setBikoSaigaiKnj(item.getBikoSaigaiKnj());
                // 備考（権利擁護）
                gui00805Gdl4NeceH21.setBikoKenriKnj(item.getBikoKenriKnj());
                // ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報を追加する。
                gdl4NeceH21List.add(gui00805Gdl4NeceH21);
            }
        });
    }

    /**
     * ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リストを編集する
     * 
     * @param1 gdl4Ass122H21OutEntityList［ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）DAO
     * @param2 gdl4NeceH21List ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト
     */
    private void processGLSummaryAndNotesList4(
            List<CpnTucGdl4NeceH21FewItemListOutEntity> cpnTucGdl4NeceH21FewItemListOutList,
            List<Gui00805Gdl4NeceH21> gdl4NeceH21List) {
        cpnTucGdl4NeceH21FewItemListOutList.forEach(item -> {
            // 取得したデータからマスタIDが1のデータを取得して、変数.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リストに設定する
            if (CommonConstants.MAST_ID == item.getMastId()) {
                Gui00805Gdl4NeceH21 gui00805Gdl4NeceH21 = new Gui00805Gdl4NeceH21();
                // アセスメントID
                gui00805Gdl4NeceH21.setGdlId(CommonDtoUtil.objValToString(item.getGdlId()));
                // 計画期間ID
                gui00805Gdl4NeceH21.setSc1Id(CommonDtoUtil.objValToString(item.getSc1Id()));
                // 特記事項
                gui00805Gdl4NeceH21.setMemoKnj(item.getMemoKnj());
                // 必要性1
                gui00805Gdl4NeceH21.setHitsuyo1(CommonDtoUtil.objValToString(item.getHitsuyo1()));
                // 必要性2
                gui00805Gdl4NeceH21.setHitsuyo2(CommonDtoUtil.objValToString(item.getHitsuyo2()));
                // マスタID
                gui00805Gdl4NeceH21.setMastId(CommonDtoUtil.objValToString(item.getMastId()));
                // 法人ID
                gui00805Gdl4NeceH21.setHoujinId(CommonDtoUtil.objValToString(item.getHoujinId()));
                // 施設ID
                gui00805Gdl4NeceH21.setShisetuId(CommonDtoUtil.objValToString(item.getShisetuId()));
                // 事業者ID
                gui00805Gdl4NeceH21.setSvJigyoId(CommonDtoUtil.objValToString(item.getSvJigyoId()));
                // 利用者ID
                gui00805Gdl4NeceH21.setUserId(CommonDtoUtil.objValToString(item.getUserId()));
                // ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報を追加する。
                gdl4NeceH21List.add(gui00805Gdl4NeceH21);
            }
        });

    }

    /**
     * ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リストを編集する
     * 
     * @param1 gdl5Ass122R3OutEntityList ＧＬ＿全体のまとめ・特記事項（Ｒ3改訂）情報DAO
     * @param2 gdl4NeceR3Listt 変数.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト
     */
    private void processGLSummaryAndNotesR3List(List<Gdl5Ass122R3OutEntity> gdl5Ass122R3OutEntityList,
            List<Gui00805Gdl4NeceR3> gdl4NeceR3List) {
        gdl5Ass122R3OutEntityList.forEach(item -> {
            // ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リストに設定する
            if (CommonConstants.STOP_FLG_0.equals(CommonDtoUtil.objValToString(item.getStopFlg()))) {
                // データから中止フラグ ＝0のデータを取得して、変数.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リストに設定する
                Gui00805Gdl4NeceR3 gui00805Gdl4NeceR3 = new Gui00805Gdl4NeceR3();
                // アセスメントID
                gui00805Gdl4NeceR3.setGdlId(CommonDtoUtil.objValToString(item.getGdlId()));
                // 計画期間ID
                gui00805Gdl4NeceR3.setSc1Id(CommonDtoUtil.objValToString(item.getSc1Id()));
                // 特記事項
                gui00805Gdl4NeceR3.setMemoKnj(item.getMemoKnj());
                // 必要性1
                gui00805Gdl4NeceR3.setHitsuyo1(CommonDtoUtil.objValToString(item.getHitsuyo1()));
                // 必要性2
                gui00805Gdl4NeceR3.setHitsuyo2(CommonDtoUtil.objValToString(item.getHitsuyo2()));
                // マスタID
                gui00805Gdl4NeceR3.setMastId(CommonDtoUtil.objValToString(item.getMastId()));
                // 法人ID
                gui00805Gdl4NeceR3.setHoujinId(CommonDtoUtil.objValToString(item.getHoujinId()));
                // 施設ID
                gui00805Gdl4NeceR3.setShisetuId(CommonDtoUtil.objValToString(item.getShisetuId()));
                // 事業者ID
                gui00805Gdl4NeceR3.setSvJigyoId(CommonDtoUtil.objValToString(item.getSvJigyoId()));
                // 利用者ID
                gui00805Gdl4NeceR3.setUserId(CommonDtoUtil.objValToString(item.getUserId()));
                // 項目
                gui00805Gdl4NeceR3.setKoumokuKnj(item.getKoumokuKnj());
                // 表示順
                gui00805Gdl4NeceR3.setSort(CommonDtoUtil.objValToString(item.getSort()));
                // カウンター
                gui00805Gdl4NeceR3.setId(CommonDtoUtil.objValToString(item.getId()));
                // 中止フラグ
                gui00805Gdl4NeceR3.setStopFlg(CommonDtoUtil.objValToString(item.getStopFlg()));
                // 氏名
                gui00805Gdl4NeceR3.setNameKnj(item.getNameKnj());
                // 本人との関係
                gui00805Gdl4NeceR3.setKankeiKnj(item.getKankeiKnj());
                // 電話番号
                gui00805Gdl4NeceR3.setTel(item.getTel());
                // FAX
                gui00805Gdl4NeceR3.setFax(item.getFax());
                // メールアドレス
                gui00805Gdl4NeceR3.setEMail(item.getEMail());
                // 備考（災害時）
                gui00805Gdl4NeceR3.setBikoSaigaiKnj(item.getBikoSaigaiKnj());
                // 備考（権利擁護）
                gui00805Gdl4NeceR3.setBikoKenriKnj(item.getBikoKenriKnj());
                // 個別避難計画策定の有無
                gui00805Gdl4NeceR3.setKobetsuhinan(CommonDtoUtil.objValToString(item.getKobetsuhinan()));
                // 変数.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リストに設定する
                gdl4NeceR3List.add(gui00805Gdl4NeceR3);
            }

        });
    }

    /**
     * ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リストを編集する
     * 
     * @param1 gdl5Ass12R3OutEntityList ＧＬ＿全体のまとめ・特記事項（Ｒ3改訂）情報DAO
     * @param2 gdl4NeceR3Listt 変数.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト
     */
    private void processGLSummaryAndNotesR3List2(List<Gdl5Ass12R3OutEntity> gdl5Ass12R3OutEntityList,
            List<Gui00805Gdl4NeceR3> gdl4NeceR3List) {
        gdl5Ass12R3OutEntityList.forEach(item -> {
            if (CommonConstants.MAST_ID == item.getMastId()) {
                // データからマスタID＝１のデータを取得して、変数.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リストに設定する
                Gui00805Gdl4NeceR3 gui00805Gdl4NeceR3 = new Gui00805Gdl4NeceR3();
                // アセスメントID
                gui00805Gdl4NeceR3.setGdlId(CommonDtoUtil.objValToString(item.getGdlId()));
                // 計画期間ID
                gui00805Gdl4NeceR3.setSc1Id(CommonDtoUtil.objValToString(item.getSc1Id()));
                // マスタID
                gui00805Gdl4NeceR3.setMastId(CommonDtoUtil.objValToString(item.getMastId()));
                // 法人ID
                gui00805Gdl4NeceR3.setHoujinId(CommonDtoUtil.objValToString(item.getHoujinId()));
                // 施設ID
                gui00805Gdl4NeceR3.setShisetuId(CommonDtoUtil.objValToString(item.getShisetuId()));
                // 事業者ID
                gui00805Gdl4NeceR3.setSvJigyoId(CommonDtoUtil.objValToString(item.getSvJigyoId()));
                // 利用者ID
                gui00805Gdl4NeceR3.setUserId(CommonDtoUtil.objValToString(item.getUserId()));
                // 特記事項
                gui00805Gdl4NeceR3.setMemoKnj(item.getMemoKnj());
                // 必要性1
                gui00805Gdl4NeceR3.setHitsuyo1(CommonDtoUtil.objValToString(item.getHitsuyo1()));
                // 必要性2
                gui00805Gdl4NeceR3.setHitsuyo2(CommonDtoUtil.objValToString(item.getHitsuyo2()));
                // 氏名
                gui00805Gdl4NeceR3.setNameKnj(item.getNameKnj());
                // 本人との関係
                gui00805Gdl4NeceR3.setKankeiKnj(item.getKankeiKnj());
                // 電話番号
                gui00805Gdl4NeceR3.setTel(item.getTel());
                // FAX
                gui00805Gdl4NeceR3.setFax(item.getFax());
                // メールアドレス
                gui00805Gdl4NeceR3.setEMail(item.getEMail());
                // 備考（災害時）
                gui00805Gdl4NeceR3.setBikoSaigaiKnj(item.getBikoSaigaiKnj());
                // 備考（権利擁護）
                gui00805Gdl4NeceR3.setBikoKenriKnj(item.getBikoKenriKnj());
                // 個別避難計画策定の有無
                gui00805Gdl4NeceR3.setKobetsuhinan(CommonDtoUtil.objValToString(item.getKobetsuhinan()));
                // 変数.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リストに設定する
                gdl4NeceR3List.add(gui00805Gdl4NeceR3);
            }
        });
    }

}
