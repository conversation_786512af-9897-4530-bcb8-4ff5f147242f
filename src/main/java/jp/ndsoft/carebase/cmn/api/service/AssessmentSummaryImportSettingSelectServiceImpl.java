package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

// import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentSummaryImportSettingSelectInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentSummaryImportSettingSelectOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui00827ParameterListSelectOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.ParamKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ParamKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSysCIniSelectMapper;
// import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
// import jp.ndsoft.carebase.common.dao.mysql.entity.ParamKnjByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.ParamKnjOutEntity;
// import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSysCIniSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025/05/13
 * <AUTHOR> THANH PHONG
 * @implNote GUI00827_「アセスメント総括取込み設定」画面
 */
@Service
public class AssessmentSummaryImportSettingSelectServiceImpl extends
        SelectServiceImpl<AssessmentSummaryImportSettingSelectInDto, AssessmentSummaryImportSettingSelectOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 24-03 iniファイルデータ保存テーブル情報取得 */
    @Autowired
    private ComMocSysCIniSelectMapper comMocSysCIniSelectMapper;

    @Override
    protected AssessmentSummaryImportSettingSelectOutDto mainProcess(AssessmentSummaryImportSettingSelectInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        AssessmentSummaryImportSettingSelectOutDto outDto = new AssessmentSummaryImportSettingSelectOutDto();
        List<Gui00827ParameterListSelectOutDto> importList = new ArrayList<>();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /** 2. アセスメント総括取込み設定情報を取得する */
        // 2.1. 以下のDAOを利用し、アセスメント総括取込み設定情報を取得する
        for (String keyKnj : inDto.getKeyKnjList()) {
            ParamKnjByCriteriaInEntity paramKnjByCriteriaInEntity = new ParamKnjByCriteriaInEntity();
            // システムコード
            paramKnjByCriteriaInEntity.setLsSyscd(inDto.getSyscd());
            // 職員ID
            paramKnjByCriteriaInEntity.setAlShokuId(inDto.getShokuId());
            // 法人ID
            paramKnjByCriteriaInEntity.setAlHid(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
            // 施設ID
            paramKnjByCriteriaInEntity.setAlSid(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            // 事業所ID
            paramKnjByCriteriaInEntity.setAlJid(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 機能名
            paramKnjByCriteriaInEntity.setAsKinouname(inDto.getKinounameKnj());
            // セクション
            paramKnjByCriteriaInEntity.setAsSection(inDto.getSectionKnj());
            // 2.2. 上記2.1.にて取得した結果から、以下のキーリストに存在するキーのパラメータ値を取込情報リストに設定する
            // ・領域名称：ryoiki_flg
            // ・分類名称：bunrui_flg
            // ・項目名称：koumoku_flg
            // ・計画優先度：yusen_flg
            // ・囲み1：kakomi1
            // ・囲み2：kakomi2
            // ・囲み3：kakomi3
            // ・囲み4：kakomi4
            // ・囲み5：kakomi5
            // ・囲み6：kakomi6
            // ・領域名称チェック：ryoiki1_flg
            // ・分類名称チェック：bunrui1_flg
            // ・項目名称チェック：koumoku1_flg
            // ・選択1：sentaku1_flg
            // ・選択2：sentaku2_flg
            // ・文章1：bunsyo1_flg
            // ・文章2：bunsyo2_flg
            // ・区切1：kugiri1
            // ・区切2：kugiri2
            // ・区切3：kugiri3
            // ・区切4：kugiri4
            // ・改行1：kugiri1_flg
            // ・改行2：kugiri2_flg
            paramKnjByCriteriaInEntity.setAsKey(keyKnj);

            List<ParamKnjOutEntity> paramKnjOutEntities = comMocSysCIniSelectMapper
                    .findParamKnjByCriteria(paramKnjByCriteriaInEntity);

            if (CollectionUtils.isNotEmpty(paramKnjOutEntities)) {
                var paramKnjOutEntity = paramKnjOutEntities.getFirst();
                Gui00827ParameterListSelectOutDto importListSelectOutDto = new Gui00827ParameterListSelectOutDto();
                importListSelectOutDto.setKeyKnj(keyKnj);
                importListSelectOutDto.setParamKnj(paramKnjOutEntity.getParamKnj());
                importList.add(importListSelectOutDto);
            }
        }
        outDto.setParamList(importList);
        return outDto;
    }

}
