package jp.ndsoft.carebase.cmn.api.report.logic;

import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonDateParts;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonShiTeiDateParts;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportMoniitiranData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportMoniitiranDetailData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportMonitoringRecordPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportMonitoringRecordPrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportSokatuHenko;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.JigyoRirekiInfoDto;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.MonitoringRecordListReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.MonitoringRecordReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.model.MonitoringRecordReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.service.ShoninSetReportService;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmpMstShoninByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmpMstShoninOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMoni1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMoni1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMoni2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMoni2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMoniDddwByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMoniDddwOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinFullNameByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinFullNameOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YoushikiNoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YoushikiNoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocPrtYoushikiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMstChouhyouInkanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucHcf1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucShoninSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCmoni1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCmoni2SelectMapper;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.design.JasperDesign;

/**
 * @since 2025.08.11
 * <AUTHOR>
 * @description U01020_モニタリング記録表一覧 帳票出力
 */
@Component
public class MonitoringRecordLogic {
    /** 28-01 モニタリング記録表ヘッダ情報取得 */
    @Autowired
    private CpnTucCmoni1SelectMapper cpnTucCmoni1SelectMapper;

    /** 28-02 モニタリング記録表データ情報取得 */
    @Autowired
    private CpnTucCmoni2SelectMapper cpnTucCmoni2SelectMapper;

    /** 職員情報一覧情報取得 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /** 利用者基本（１－６）の情報取得 */
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;

    /** 印鑑欄設定情報取得 */
    @Autowired
    private CpnMstChouhyouInkanSelectMapper cpnMstChouhyouInkanSelectMapper;

    /** 事業履歴より事業所名を取得する */
    @Autowired
    private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;

    /** 条件、日付より要介護度文字列を取得する */
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    /** 文書番号情報取得 */
    @Autowired
    private ComMocPrtYoushikiSelectMapper comMocPrtYoushikiSelectMapper;

    /** 文書番号情報取得 */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** 承認欄設定クラス */
    @Autowired
    private ShoninSetReportService shoninSetReportService;

    /** 承認欄マスタ情報取得 */
    @Autowired
    private CpnMucShoninSelectMapper cpnMucShoninSelectMapper;

    /** モニタリング記録表_DDDW */
    @Autowired
    private CpnMucHcf1SelectMapper cpnMucHcf1SelectMapper;

    /**
     * U01020_モニタリング記録表一覧パラメータ取得
     * 
     * @param model        入力データ
     * @param jasperDesign 帳票データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public MonitoringRecordListReportServiceInDto getMonitoringRecordListReportParameters(
            MonitoringRecordReportParameterModel model, JasperDesign jasperDesign) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));
        // 帳票用データ詳細
        MonitoringRecordListReportServiceInDto infoInDto = new MonitoringRecordListReportServiceInDto();

        // 【リクエストパラメータ】.印刷オプション
        ReportMonitoringRecordPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();

        // 印刷対象履歴リスト
        ReportMonitoringRecordPrintSubjectHistory printSubjectHistory = model.getPrintSubjectHistoryList().get(0);

        // 【リクエストパラメータ】.記入用シートを印刷するフラグ
        Boolean isEmptyFlg = false;

        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg()) || CommonDtoUtil.checkStringEqual(
                printOption.getEmptyFlg(), CommonConstants.STR_1)) {
            isEmptyFlg = true;
        }

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (isEmptyFlg) {
            infoInDto = this.setMonitoringRecordListDefReportParams(model);

        }
        // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
        else {
            infoInDto = this.setMonitoringRecordListReportParams(model);
        }

        // 指定日印刷区分
        // リクエストパラメータ.データ.印刷設定.指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        // 敬称名
        // ・リクエストパラメータ.データ.印刷オプション. 敬称を変更する＝1の場合
        if (CommonDtoUtil.checkStringEqual(printOption.getKeishoFlg(), CommonConstants.STR_0)) {
            // リクエストパラメータ.データ.システム設定敬称を変更するフラグ = 0 の場合
            if (CommonDtoUtil.checkStringEqual(model.getStKeishoFlg(), CommonConstants.STR_0)) {
                // 敬称名＝"殿"
                infoInDto.setKeishoName(CommonConstants.KEISHO_STR_TONO);
            }
            // リクエストパラメータ.データ.システム設定敬称を変更するフラグ = 1 の場合
            else if (CommonDtoUtil.checkStringEqual(model.getStKeishoFlg(), CommonConstants.STR_1)) {
                // 敬称名 = リクエストパラメータ.データ.初期設定マスタの情報の敬称
                infoInDto.setKeishoName(model.getStKeisho());
            }

        } else {
            // 敬称名＝リクエストパラメータ.データ.印刷オプション. 敬称名
            infoInDto.setKeishoName(printOption.getKeisho());

        }

        // 記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(isEmptyFlg);

        // 一覧表示するフラグ
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getIchiranDispFlg())) {
            infoInDto.setIchiranHyoujiFlg(CommonConstants.NUMBER_1);
        } else {
            infoInDto.setIchiranHyoujiFlg(CommonConstants.NUMBER_0);
        }

        // 文字サイズ
        // ・リクエストパラメータ.データ.印刷設定.一覧表示するフラグ (ichiranHyoujiFlg)=1の場合、
        if (CommonDtoUtil.checkStringEqual(printOption.getIchiranFlg(), CommonConstants.STR_0)) {
            infoInDto.setMojiSize(CommonConstants.STR_12);

        } else {
            // ・上記以外の場合
            switch (printSubjectHistory.getMojiSize()) {
                // ・"0"の場合、文字サイズ＝"9"
                case CommonConstants.FONT_SIZE_9PT:
                    infoInDto.setMojiSize(CommonConstants.STR_9);
                    break;
                // ・"1"の場合、文字サイズ＝"10"
                case CommonConstants.FONT_SIZE_10PT:
                    infoInDto.setMojiSize(CommonConstants.STR_10);
                    break;
                // ・"2"の場合、文字サイズ＝"11"
                case CommonConstants.FONT_SIZE_11PT:
                    infoInDto.setMojiSize(CommonConstants.STR_11);
                    break;
                // ・"3"の場合、文字サイズ＝"12"
                case CommonConstants.FONT_SIZE_12PT:
                    infoInDto.setMojiSize(CommonConstants.STR_12);
                    break;
                // ・以外の場合、文字サイズ＝"11"
                default:
                    infoInDto.setMojiSize(CommonConstants.STR_11);
                    break;
            }

        }

        // 承認欄を印刷するフラグ
        infoInDto.setSyoninPrintFlg(CommonDtoUtil.strValToInt(printOption.getShoninFlg()));

        // 敬称を変更するフラグ
        infoInDto.setKeishoFlg(CommonDtoUtil.strValToInt(printOption.getKeishoFlg()));

        /*
         * =============4. 承認欄を設定する==============
         * 
         */
        JRBeanCollectionDataSource subReportDataDs = null;

        // 承認欄
        if (CommonDtoUtil.strValToInt(printOption.getShoninFlg()) > CommonConstants.NUMBER_0) {
            HashMap<String, Object> shonin = shoninSetReportService.getShoninSetReport(jasperDesign,
                    ReportConstants.INT_1, model.getJigyoInfo().getSvJigyoId(),
                    model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection());

            subReportDataDs = (JRBeanCollectionDataSource) shonin
                    .get(ReportConstants.SUBREPORTDATADS);

        }

        // 承認欄subReportDataDs
        infoInDto.setSubReportDataDs(subReportDataDs);

        return infoInDto;

    }

    /**
     * 記入用シートを印刷するフラグ = true U01020_モニタリング記録表一覧パラメータ取得
     * 
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private MonitoringRecordListReportServiceInDto setMonitoringRecordListDefReportParams(
            MonitoringRecordReportParameterModel model) {
        MonitoringRecordListReportServiceInDto infoInDto = new MonitoringRecordListReportServiceInDto();
        // 指定日
        // 指定日（年号）
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);

        // 事業所名
        infoInDto.setJigyouName(CommonConstants.BLANK_STRING);

        // 要介護度
        infoInDto.setYokaigoDo(CommonConstants.BLANK_STRING);

        // 利用者名
        infoInDto.setRiyoushaNm(CommonConstants.BLANK_STRING);

        // 作成年月日
        // 作成年月日（年号）
        infoInDto.setCreateYmdGG(CommonConstants.FULL_WIDTH_SPACE);
        // 作成年月日（年）
        infoInDto.setCreateYmdYY(CommonConstants.FULL_WIDTH_SPACE);
        // 作成年月日（月）
        infoInDto.setCreateYmdMM(CommonConstants.FULL_WIDTH_SPACE);
        // 作成年月日（日）
        infoInDto.setCreateYmdDD(CommonConstants.FULL_WIDTH_SPACE);

        // 作成者
        infoInDto.setShokuName(CommonConstants.BLANK_STRING);

        // 印鑑欄表示フラグ
        infoInDto.setInkanFlg(CommonConstants.NUMBER_0);

        // 印鑑1~印鑑15
        infoInDto.setHanko1Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko2Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko3Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko4Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko5Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko6Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko7Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko8Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko9Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko10Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko11Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko12Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko13Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko14Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko15Knj(CommonConstants.BLANK_STRING);

        // モニタリング記録表一覧詳細情報のリスト
        List<ReportMoniitiranData> moniitiranDataList = new ArrayList<ReportMoniitiranData>();
        JRBeanCollectionDataSource moniitiranDataSource = new JRBeanCollectionDataSource(moniitiranDataList);
        infoInDto.setMoniitiranDataList(moniitiranDataSource);

        // 総括・計画の変更等リスト
        List<ReportSokatuHenko> sokatuHenkoList = new ArrayList<ReportSokatuHenko>();
        ReportSokatuHenko sokatuHenko = new ReportSokatuHenko();

        String blankStr = this.getSoukatuKnjAndHenkouKnj(model);
        // 総括
        sokatuHenko.setSoukatuKnj(blankStr);
        // 計画の変更等
        sokatuHenko.setHenkouKnj(blankStr);
        // 再アセスメントの必要
        sokatuHenko.setRetryChk(CommonConstants.BLANK_STRING);
        // 実施予定日
        // 実施予定日（年号）
        sokatuHenko.setYoteiYmdGG(CommonConstants.FULL_WIDTH_SPACE);
        // 実施予定日（年）
        sokatuHenko.setYoteiYmdYY(CommonConstants.FULL_WIDTH_SPACE);
        // 実施予定日（月）
        sokatuHenko.setYoteiYmdMM(CommonConstants.FULL_WIDTH_SPACE);
        // 実施予定日（日）
        sokatuHenko.setYoteiYmdDD(CommonConstants.FULL_WIDTH_SPACE);

        sokatuHenkoList.add(sokatuHenko);

        JRBeanCollectionDataSource sokatuHenkoDataSource = new JRBeanCollectionDataSource(sokatuHenkoList);
        infoInDto.setSokatuHenkoList(sokatuHenkoDataSource);

        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;

    }

    /**
     * 記入用シートを印刷するフラグ = false U01020_モニタリング記録表一覧パラメータ取得
     * 
     * @param model 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private MonitoringRecordListReportServiceInDto setMonitoringRecordListReportParams(
            MonitoringRecordReportParameterModel model)
            throws Exception {

        // 【リクエストパラメータ】.印刷オプション
        ReportMonitoringRecordPrintOption printOption = model.getPrintOption();
        // 印刷対象履歴
        ReportMonitoringRecordPrintSubjectHistory printSubjectHistory = model.getPrintSubjectHistoryList().get(0);

        /*
         * =============2.1.モニタリング記録一覧のヘッダ情報を取得する。==============
         * 
         */
        // 2.1.下記のモニタリング記録表ヘッダ情報取得のDAOを利用し、モニタリング記録一覧のヘッダ情報を取得する。
        List<CpnMoni1OutEntity> cpnMoni1List = this.findCpnMoni1ByCriteria(
                printSubjectHistory.getSc1Id(),
                printSubjectHistory.getRirekiId());

        /*
         * =============2.2.モニタリング記録表データ情報を取得する。==============
         * 
         */
        // 2.2. 下記のモニタリング記録表データ情報取得DAOを利用し、モニタリング記録表データ情報を取得する。
        List<CpnMoni2OutEntity> cpnMoni2List = this.findCpnMoni2ByCriteria(printSubjectHistory.getRirekiId());

        // 作成者
        String shokuName = null;

        // 利用者名
        String riyoushaNm = null;

        // 変数.カラムCD幅 | 変数.カラム幅
        int[] columnWidth = new int[] {};

        // 変数.カラム総括部幅
        int[] sokatuHenkoColumnWidth = new int[] {};

        // 印鑑欄設定情報
        List<KghCpnMstChouhyouInkanPrnOutEntity> kghCpnMstChouhyouInkanPrnList = new ArrayList<>();

        // モニタリング記録表_DDDW
        List<CpnMoniDddwOutEntity> cpnMoniDddwList = new ArrayList<>();

        // モニタリング記録表一覧詳細情報のリスト
        List<ReportMoniitiranData> moniitiranDataList = new ArrayList<>();

        // 2.3. 「2.2.」で取得したモニタリング記録表一覧情報の件数をワークモニタリング記録表一覧情報.総件数に設定する。
        // 2.3.1. 総件数 > 0 件の場合。
        if (CollectionUtils.isNotEmpty(cpnMoni2List)) {
            /*
             * =============2.4.実行確認情報を取得する。==============
             * 
             */
            // 2.4 下記の文字列入力支援取得のDAOを利用し、モニタリングDDWS情報を取得する。
            // ①下記の文字列入力支援取得のDAOを利用し、モニタリングDDWS情報を取得する。
            CpnMoniDddwByCriteriaInEntity cpnMoniDddwByCriteriaInEntity = new CpnMoniDddwByCriteriaInEntity();
            // 区分フラグ
            cpnMoniDddwByCriteriaInEntity.setKbnFlg(CommonDtoUtil.checkStringEqual(model.getSys3ryaku(),
                    CommonConstants.SYS3RYAKU_CPN) ? CommonConstants.NUMBER_1 : CommonConstants.NUMBER_2);
            cpnMoniDddwList = this.cpnMucHcf1SelectMapper.findCpnMoniDddwByCriteria(cpnMoniDddwByCriteriaInEntity);

            // 2.5. 作成者を取得する。
            shokuName = this.findShokuinNameKnj2ByCriteria(cpnMoni1List.get(0).getShokuId());

            // 2.6. 利用者基本情報を取得する。
            riyoushaNm = this.findUserNenreiIgaiByCriteria(
                    CommonDtoUtil.strValToInt(model.getPrintSubjectHistoryList().get(0).getUserId()));

            // 2.7 処理「2.2」でデータを取得できる場合、帳票用データ詳細.モニタリング記録表一覧詳細情報のリストを作成する。
            // 2.7.1 1行あたり表示桁数について、 変数.カラム幅を設定する
            // ・リクエストパラメータ.データ.印刷設定.一覧表示するフラグ = "1"(一覧表示する)場合、
            if (CommonDtoUtil.checkStringEqual(printOption.getIchiranDispFlg(), CommonConstants.STR_1)) {
                switch (printSubjectHistory.getMojiSize()) {
                    // ・リクエストパラメータ.データ.印刷設定.設定する文字サイズ＝"0"の場合、
                    case CommonConstants.FONT_SIZE_9PT:
                        columnWidth = new int[] { 20, 20, 20, 20, 26, 26 };

                        sokatuHenkoColumnWidth = new int[] { 68, 84 };
                        break;
                    // ・リクエストパラメータ.データ.印刷設定.設定する文字サイズ＝"1"の場合、
                    case CommonConstants.FONT_SIZE_10PT:
                        columnWidth = new int[] { 18, 18, 18, 18, 22, 22 };

                        sokatuHenkoColumnWidth = new int[] { 62, 76 };
                        break;
                    // ・リクエストパラメータ.データ.印刷設定.設定する文字サイズ＝"2"の場合、
                    case CommonConstants.FONT_SIZE_11PT:
                        columnWidth = new int[] { 16, 16, 16, 18, 20, 20 };

                        sokatuHenkoColumnWidth = new int[] { 56, 70 };
                        break;
                    // ・リクエストパラメータ.データ.印刷設定.設定する文字サイズ＝"3"の場合、
                    case CommonConstants.FONT_SIZE_12PT:
                        columnWidth = new int[] { 14, 14, 14, 14, 18, 18 };

                        sokatuHenkoColumnWidth = new int[] { 52, 64 };

                        break;
                    default:
                        columnWidth = new int[] { 16, 16, 16, 16, 20, 20 };

                        sokatuHenkoColumnWidth = new int[] { 56, 70 };

                        break;
                }

            } else {
                columnWidth = new int[] { 14, 14, 14, 14, 18, 18 };

                sokatuHenkoColumnWidth = new int[] { 52, 64 };

            }

            // 2.7.2 モニタリング記録表一覧データ情報件数分、情報レコードを繰り返して、帳票用データ詳細.モニタリング記録表一覧詳細情報のリストを作成する。
            moniitiranDataList = this.setMoniitiranDataList(cpnMoni2List, cpnMoniDddwList, columnWidth);

            /*
             * =============3. 印鑑欄設定情報を取得する。==============
             * 
             */
            kghCpnMstChouhyouInkanPrnList = this
                    .findKghCpnMstChouhyouInkanPrnByCriteria(printOption.getEmptyFlg(),
                            model.getJigyoInfo().getHoujinId(), model.getJigyoInfo().getShisetuId(),
                            model.getJigyoInfo().getSvJigyoId(),
                            printSubjectHistory.getChoPrtList().get(0).getSection().substring(0, 9));

        }

        // 帳票用データ詳細を設定する。
        // 帳票用データ詳細
        MonitoringRecordListReportServiceInDto infoInDto = this.setMonitoringRecordListInfoInDto(model, cpnMoni1List,
                moniitiranDataList, riyoushaNm, shokuName, kghCpnMstChouhyouInkanPrnList, cpnMoni2List, columnWidth,
                sokatuHenkoColumnWidth);

        return infoInDto;

    }

    /**
     * U01020_モニタリング記録表パラメータ取得
     * 
     * @param model        入力データ
     * @param jasperDesign 帳票データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public MonitoringRecordReportServiceInDto getMonitoringRecordReportParameters(
            MonitoringRecordReportParameterModel model, JasperDesign jasperDesign) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        MonitoringRecordReportServiceInDto infoInDto = new MonitoringRecordReportServiceInDto();

        // 【リクエストパラメータ】.印刷オプション
        ReportMonitoringRecordPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();

        ReportMonitoringRecordPrintSubjectHistory printSubjectHistory = model.getPrintSubjectHistoryList().get(0);

        // 【リクエストパラメータ】.記入用シートを印刷するフラグ
        Boolean isEmptyFlg = false;

        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg()) || CommonDtoUtil.checkStringEqual(
                printOption.getEmptyFlg(), CommonConstants.STR_1)) {
            isEmptyFlg = true;
        }

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (isEmptyFlg) {
            infoInDto = this.setMonitoringRecordDefReportParams(model);

        }
        // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
        else {
            infoInDto = this.setMonitoringRecordReportParams(model);
        }

        // 記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(isEmptyFlg);

        // 帳票タイトル
        infoInDto.setTitle(printSubjectHistory.getChoPrtList().get(0).getPrtTitle());

        // 指定日印刷区分
        infoInDto.setPrDate(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        // 事業所名
        JigyoRirekiInfoDto retDto = kghKrkZCpnFuncLogic.getJigyoRirekiKnj(
                CommonDtoUtil.strValToInt(model.getJigyoInfo().getSvJigyoId()),
                model.getAppYmd());
        String jigyoName = retDto.getJigyoRirekiRyakuKnj();
        infoInDto.setJigyoName(jigyoName);

        // 敬称名
        // ・リクエストパラメータ.データ.印刷オプション.敬称を変更するフラグ = "false" の場合 、
        if (CommonDtoUtil.checkStringEqual(printOption.getKeishoFlg(), CommonConstants.STR_FALSE)) {
            // ・リクエストパラメータ.データ.システム設定敬称を変更するフラグ = "true" の場合
            if (CommonDtoUtil.checkStringEqual(model.getStKeishoFlg(), CommonConstants.STR_TRUE)) {
                // 敬称 = リクエストパラメータ.データ.初期設定マスタの情報の敬称
                infoInDto.setKeisho(model.getStKeisho());
            }

        } else {
            // 敬称= リクエストパラメータ.データ.印刷オプション.敬称の内容
            infoInDto.setKeisho(printOption.getKeisho());

        }

        // 長期目標フラグ
        infoInDto.setChoukiFlg(CommonDtoUtil.strValToInt(model.getCmoniChoukiFlg()));

        // 一覧印刷する
        infoInDto.setIchiranFlg(CommonDtoUtil.strValToInt(printOption.getIchiranFlg()));

        // 番号フラグ
        infoInDto.setBangouFlg(CommonDtoUtil.strValToInt(model.getCmoniBangouFlg()));

        // 文字サイズ
        // ・リクエストパラメータ.データ.印刷オプション.一覧印刷する = "0"(一覧表示しない)場合
        if (CommonDtoUtil.checkStringEqual(printOption.getIchiranFlg(), CommonConstants.STR_0)) {
            infoInDto.setMojiSize(CommonConstants.STR_12);

        } else {
            // ・上記以外の場合
            switch (printSubjectHistory.getMojiSize()) {
                // ・"0"の場合、文字サイズ＝"9"
                case CommonConstants.FONT_SIZE_9PT:
                    infoInDto.setMojiSize(CommonConstants.STR_9);
                    break;
                // ・"1"の場合、文字サイズ＝"10"
                case CommonConstants.FONT_SIZE_10PT:
                    infoInDto.setMojiSize(CommonConstants.STR_10);
                    break;
                // ・"2"の場合、文字サイズ＝"11"
                case CommonConstants.FONT_SIZE_11PT:
                    infoInDto.setMojiSize(CommonConstants.STR_11);
                    break;
                // ・"3"の場合、文字サイズ＝"12"
                case CommonConstants.FONT_SIZE_12PT:
                    infoInDto.setMojiSize(CommonConstants.STR_12);
                    break;
                // ・以外の場合、文字サイズ＝"11"
                default:
                    infoInDto.setMojiSize(CommonConstants.STR_11);
                    break;
            }

        }

        // 承認欄を印刷するフラグ
        infoInDto.setShoninFlg(CommonDtoUtil.strValToInt(printOption.getShoninFlg()));

        // 4.承認欄を設定する。
        String subReportPath = null;
        JRBeanCollectionDataSource subReportDataDs = null;

        // リクエストパラメータ.データ.印刷オプション.承認欄を印刷するフラグ="1"の場合、承認欄設定の共通関数で承認欄を設定する
        if (CommonDtoUtil.checkStringEqual(printOption.getShoninFlg(), CommonConstants.STR_1)) {
            HashMap<String, Object> shonin = shoninSetReportService.getShoninSetReport(jasperDesign,
                    ReportConstants.INT_1, model.getJigyoInfo().getSvJigyoId(),
                    printSubjectHistory.getChoPrtList().get(0).getSection());

            subReportPath = (String) shonin.get(ReportConstants.SUBREPORTPATH);
            subReportDataDs = (JRBeanCollectionDataSource) shonin
                    .get(ReportConstants.SUBREPORTDATADS);

        }

        // 承認欄テンプレートパス
        infoInDto.setSubReportPath(subReportPath);

        // 承認欄subReportDataDs
        infoInDto.setSubReportDataDs(subReportDataDs);

        return infoInDto;

    }

    /**
     * 記入用シートを印刷するフラグ = true U01020_モニタリング記録表パラメータ取得
     * 
     * @param model 一覧印刷するフラグ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private MonitoringRecordReportServiceInDto setMonitoringRecordDefReportParams(
            MonitoringRecordReportParameterModel model) {
        MonitoringRecordReportServiceInDto infoInDto = new MonitoringRecordReportServiceInDto();
        // 指定日
        // 指定日（年号）
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);

        // 要介護度
        infoInDto.setYokaigoDo(CommonConstants.BLANK_STRING);

        // 利用者名
        infoInDto.setRiyoushaNm(CommonConstants.BLANK_STRING);

        // 作成年月日
        // 作成年月日（年号）
        infoInDto.setCreateYmdGG(CommonConstants.FULL_WIDTH_SPACE);
        // 作成年月日（年）
        infoInDto.setCreateYmdYY(CommonConstants.FULL_WIDTH_SPACE);
        // 作成年月日（月）
        infoInDto.setCreateYmdMM(CommonConstants.FULL_WIDTH_SPACE);
        // 作成年月日（日）
        infoInDto.setCreateYmdDD(CommonConstants.FULL_WIDTH_SPACE);

        // 作成者
        infoInDto.setShokuName(CommonConstants.BLANK_STRING);

        // 印鑑欄表示フラグ
        infoInDto.setHyoujiKbn(CommonConstants.NUMBER_0);

        // 印鑑1~印鑑15
        infoInDto.setHanko1Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko2Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko3Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko4Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko5Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko6Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko7Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko8Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko9Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko10Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko11Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko12Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko13Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko14Knj(CommonConstants.BLANK_STRING);
        infoInDto.setHanko15Knj(CommonConstants.BLANK_STRING);

        // 総括リスト
        List<ReportSokatuHenko> soukatuList = new ArrayList<ReportSokatuHenko>();
        ReportSokatuHenko sokatuHenko = new ReportSokatuHenko();
        String blankStr = this.getSoukatuKnjAndHenkouKnj(model);
        // 総括
        sokatuHenko.setSoukatuKnj(blankStr);
        // 計画の変更等
        sokatuHenko.setHenkouKnj(blankStr);
        // 再アセスメントの必要
        sokatuHenko.setRetryChk(CommonConstants.BLANK_STRING);
        // 実施予定日
        // 実施予定日（年号）
        sokatuHenko.setYoteiYmdGG(CommonConstants.FULL_WIDTH_SPACE);
        // 実施予定日（年）
        sokatuHenko.setYoteiYmdYY(CommonConstants.FULL_WIDTH_SPACE);
        // 実施予定日（月）
        sokatuHenko.setYoteiYmdMM(CommonConstants.FULL_WIDTH_SPACE);
        // 実施予定日（日）
        sokatuHenko.setYoteiYmdDD(CommonConstants.FULL_WIDTH_SPACE);

        soukatuList.add(sokatuHenko);

        JRBeanCollectionDataSource soukatuListDataSource = new JRBeanCollectionDataSource(soukatuList);
        infoInDto.setSoukatuList(soukatuListDataSource);

        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        // モニタリング記録表一覧詳細情報のリスト
        List<ReportMoniitiranDetailData> moniitiranDetailDataList = new ArrayList<ReportMoniitiranDetailData>();
        // ・リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true かつ
        // リクエストパラメータ.データ.印刷オプション.一覧印刷するフラグ＝"0"の場合、2行の空データを追加する
        if (CommonDtoUtil.checkStringEqual(model.getPrintOption().getIchiranFlg(), CommonConstants.STR_0)) {
            for (int index = 0; index < 2; index++) {
                ReportMoniitiranDetailData reportMoniitiranDetailData = this.setReportMoniitiranDetailData();
                moniitiranDetailDataList.add(reportMoniitiranDetailData);
            }

        }
        // ・リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true かつ
        // リクエストパラメータ.データ.印刷オプション.一覧印刷するフラグ＝"1"の場合、1行の空データを追加する
        else if (CommonDtoUtil.checkStringEqual(model.getPrintOption().getIchiranFlg(), CommonConstants.STR_1)) {
            ReportMoniitiranDetailData reportMoniitiranDetailData = this.setReportMoniitiranDetailData();
            moniitiranDetailDataList.add(reportMoniitiranDetailData);

        }
        JRBeanCollectionDataSource moniitiranDataSource = new JRBeanCollectionDataSource(moniitiranDetailDataList);
        infoInDto.setList(moniitiranDataSource);

        return infoInDto;

    }

    /**
     * 記入用シートを印刷するフラグ = false U01020_モニタリング記録表パラメータ取得
     * 
     * @param model 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private MonitoringRecordReportServiceInDto setMonitoringRecordReportParams(
            MonitoringRecordReportParameterModel model) throws Exception {
        MonitoringRecordReportServiceInDto infoInDto = new MonitoringRecordReportServiceInDto();

        // 【リクエストパラメータ】.印刷オプション
        ReportMonitoringRecordPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.印刷対象履歴リスト
        ReportMonitoringRecordPrintSubjectHistory printSubjectHistory = model.getPrintSubjectHistoryList().get(0);

        /*
         * =============2.1.モニタリング記録一覧のヘッダ情報を取得する。==============
         * 
         */
        // 2.1.下記のモニタリング記録表ヘッダ情報取得のDAOを利用し、モニタリング記録一覧のヘッダ情報を取得する。
        List<CpnMoni1OutEntity> cpnMoni1List = this.findCpnMoni1ByCriteria(
                printSubjectHistory.getSc1Id(),
                printSubjectHistory.getRirekiId());

        /*
         * =============「2.1.」で取得した情報の件数>0の場合、利用者名、作成者とモニタリング記録表データ情報を取得する。=============
         * 
         */
        // 利用者
        String riyoushaNm = null;

        // 作成者
        String shokuName = null;

        // 変数.カラムCD幅
        int[] cdColumnWidth = new int[] {};

        // 変数.カラム総括部幅
        int[] soukatuColumnWidth = new int[] {};

        // 変数.カラム幅
        int[] columnWidth = new int[] {};

        // モニタリング記録表詳細情報
        List<CpnMoni2OutEntity> cpnMoni2List = new ArrayList<>();

        // モニタリング記録表詳細情報のリスト
        List<ReportMoniitiranDetailData> list = new ArrayList<>();

        // モニタリング記録表_DDDW
        List<CpnMoniDddwOutEntity> cpnMoniDddwList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(cpnMoni1List)) {
            // 2.2.1 利用者名を取得する。
            riyoushaNm = this.findUserNenreiIgaiByCriteria(
                    CommonDtoUtil.strValToInt(printSubjectHistory.getUserId()));

            // 2.2.2. 作成者を取得する。
            shokuName = this.findShokuinNameKnj2ByCriteria(cpnMoni1List.get(0).getShokuId());

            // 2.2.3. モニタリング記録表データ情報を取得する。
            cpnMoni2List = this.findCpnMoni2ByCriteria(printSubjectHistory.getRirekiId());

            // 2.2.4.「2.2.3」で取得した件数＞0場合、帳票用データ詳細.モニタリング記録表詳細情報のリストの設定する。
            if (!CollectionUtils.isNullOrEmpty(cpnMoni2List)) {
                // ①下記の文字列入力支援取得のDAOを利用し、モニタリングDDWS情報を取得する。
                CpnMoniDddwByCriteriaInEntity cpnMoniDddwByCriteriaInEntity = new CpnMoniDddwByCriteriaInEntity();
                // 区分フラグ
                cpnMoniDddwByCriteriaInEntity.setKbnFlg(CommonDtoUtil.checkStringEqual(model.getSys3ryaku(),
                        CommonConstants.SYS3RYAKU_CPN) ? CommonConstants.NUMBER_1 : CommonConstants.NUMBER_2);
                cpnMoniDddwList = this.cpnMucHcf1SelectMapper.findCpnMoniDddwByCriteria(cpnMoniDddwByCriteriaInEntity);

                // ②1行あたり表示桁数について、 変数.カラム幅を設定する
                // ・リクエストパラメータ.データ.印刷オプション.一覧印刷する = "0"(一覧表示しない)場合
                if (CommonDtoUtil.checkStringEqual(printOption.getIchiranFlg(), CommonConstants.STR_0)) {
                    // 変数.カラムCD幅= {16,20,17,17,18,19}
                    cdColumnWidth = new int[] { 16, 20, 17, 17, 18, 19 };

                    // 変数.カラム総括部幅＝{62,76}
                    soukatuColumnWidth = new int[] { 62, 76 };

                    int[][] columnWidths = new int[][] {
                            { 28, 26, 26, 28, 16, 16, 16, 16, 20, 20 },
                            { 28, 28, 28, 28, 16, 16, 16, 16, 20, 20 },
                            { 38, 0, 34, 38, 16, 16, 16, 16, 20, 20 },
                            { 38, 0, 38, 38, 16, 16, 16, 16, 20, 20 }
                    };
                    // 長期目標フラグと番号フラグより変数.カラムCD幅を設定する
                    columnWidth = this.setColumnWidth(model.getCmoniChoukiFlg(), model.getCmoniBangouFlg(),
                            columnWidths);

                }
                // ・上記以外の場合、リクエストパラメータ.データ.印刷対象履歴リスト.文字サイズは
                else {
                    int[][] columnWidths = new int[][] {};

                    switch (printSubjectHistory.getMojiSize()) {
                        // ・"0"の場合、
                        case CommonConstants.FONT_SIZE_FLG_0:
                            // 変数.カラムCD幅= {12,12,14,14,14,14}
                            cdColumnWidth = new int[] { 12, 12, 14, 14, 14, 14 };

                            // 変数.カラム総括部幅＝{68,84}
                            soukatuColumnWidth = new int[] { 68, 84 };

                            columnWidths = new int[][] {
                                    { 28, 26, 26, 28, 16, 16, 16, 16, 20, 20 },
                                    { 28, 28, 28, 28, 16, 16, 16, 16, 20, 20 },
                                    { 38, 0, 34, 38, 16, 16, 16, 16, 20, 20 },
                                    { 38, 0, 38, 38, 16, 16, 16, 16, 20, 20 } };
                            // 長期目標フラグと番号フラグより変数.カラムCD幅を設定する
                            columnWidth = this.setColumnWidth(model.getCmoniChoukiFlg(), model.getCmoniBangouFlg(),
                                    columnWidths);
                            break;
                        // ・"1"の場合、
                        case CommonConstants.FONT_SIZE_FLG_1:
                            // 変数.カラムCD幅= {10,10,12,12,12,12}
                            cdColumnWidth = new int[] { 10, 10, 12, 12, 12, 12 };

                            // 変数.カラム総括部幅＝{62,76}
                            soukatuColumnWidth = new int[] { 62, 76 };

                            columnWidths = new int[][] {
                                    { 10, 10, 10, 8, 10, 10, 12, 12, 12, 12 },
                                    { 12, 10, 10, 10, 10, 10, 12, 12, 12, 12 },
                                    { 12, 0, 14, 12, 10, 10, 12, 12, 12, 12 },
                                    { 16, 0, 16, 16, 12, 12, 14, 14, 14, 14 } };

                            // 長期目標フラグと番号フラグより変数.カラムCD幅を設定する
                            columnWidth = this.setColumnWidth(model.getCmoniChoukiFlg(), model.getCmoniBangouFlg(),
                                    columnWidths);
                            break;
                        // ・"2"の場合、
                        case CommonConstants.FONT_SIZE_FLG_2:
                            // 変数.カラムCD幅= {10,10,12,12,12,12}
                            cdColumnWidth = new int[] { 10, 10, 12, 12, 12, 12 };

                            // 変数.カラム総括部幅＝{56,70}
                            soukatuColumnWidth = new int[] { 56, 70 };

                            columnWidths = new int[][] {
                                    { 10, 10, 10, 8, 10, 10, 12, 12, 12, 12 },
                                    { 12, 10, 10, 10, 10, 10, 12, 12, 12, 12 },
                                    { 12, 0, 14, 12, 10, 10, 12, 12, 12, 12 },
                                    { 14, 0, 14, 14, 10, 10, 12, 12, 12, 12 } };

                            // 長期目標フラグと番号フラグより変数.カラムCD幅を設定する
                            columnWidth = this.setColumnWidth(model.getCmoniChoukiFlg(), model.getCmoniBangouFlg(),
                                    columnWidths);
                            break;
                        // ・"3"の場合、
                        case CommonConstants.FONT_SIZE_FLG_3:
                            // 変数.カラムCD幅= {8,8,10,10,10,10}
                            cdColumnWidth = new int[] { 8, 8, 10, 10, 10, 10 };

                            // 変数.カラム総括部幅＝{52,64}
                            soukatuColumnWidth = new int[] { 52, 64 };

                            columnWidths = new int[][] {
                                    { 8, 8, 8, 6, 8, 8, 10, 10, 10, 10 },
                                    { 10, 8, 8, 8, 8, 8, 10, 10, 10, 10 },
                                    { 10, 0, 12, 10, 8, 8, 10, 10, 10, 10 },
                                    { 12, 0, 12, 12, 10, 10, 10, 10, 10, 10 } };

                            // 長期目標フラグと番号フラグより変数.カラムCD幅を設定する
                            columnWidth = this.setColumnWidth(model.getCmoniChoukiFlg(), model.getCmoniBangouFlg(),
                                    columnWidths);
                            break;
                        // ・以外の場合、
                        default:
                            // 変数.カラムCD幅= {10,10,12,12,12,12}
                            cdColumnWidth = new int[] { 10, 10, 12, 12, 12, 12 };

                            // 変数.カラム総括部幅＝{56,70}
                            soukatuColumnWidth = new int[] { 56, 70 };

                            columnWidths = new int[][] {
                                    { 10, 10, 10, 8, 10, 10, 12, 12, 12, 12 },
                                    { 12, 10, 10, 10, 10, 10, 12, 12, 12, 12 },
                                    { 12, 0, 14, 12, 10, 10, 12, 12, 12, 12 },
                                    { 14, 0, 14, 14, 10, 10, 12, 12, 12, 12 } };

                            // 長期目標フラグと番号フラグより変数.カラムCD幅を設定する
                            columnWidth = this.setColumnWidth(model.getCmoniChoukiFlg(), model.getCmoniBangouFlg(),
                                    columnWidths);
                            break;
                    }

                }

                // ③モニタリング記録表データ情報件数分、情報レコードを繰り返して、下記処理を行う、帳票用データ詳細.モニタリング記録表詳細情報のリストの設定する。
                list = this.setReportMoniitiranDetailDatalist(cpnMoni2List, cpnMoniDddwList, model,
                        columnWidth, cdColumnWidth);

            }

        }

        /*
         * =============「3. 印鑑欄設定情報を取得する。=============
         * 
         */
        List<KghCpnMstChouhyouInkanPrnOutEntity> kghCpnMstChouhyouInkanPrnList = this
                .findKghCpnMstChouhyouInkanPrnByCriteria(
                        printOption.getEmptyFlg(),
                        model.getJigyoInfo().getHoujinId(),
                        model.getJigyoInfo().getShisetuId(),
                        model.getJigyoInfo().getSvJigyoId(),
                        printSubjectHistory.getChoPrtList().get(0).getSection().substring(0, 9));

        infoInDto = this.setMonitoringRecordInfoInDto(model, cpnMoni1List, riyoushaNm, shokuName,
                kghCpnMstChouhyouInkanPrnList, list, soukatuColumnWidth);

        return infoInDto;

    }

    /**
     * 指定日を取得する
     * 
     * @param shiTeiKubun 指定日印刷区分
     * @param shiTeiDate  西暦日付
     * @param systemDate  システム日付
     * @return 指定日
     * @throws Exception 例外
     */
    private ReportCommonShiTeiDateParts getShiTeiDateGG(String shiTeiKubun, String shiTeiDate, String systemDate) {
        // 指定日（年号）=""
        String shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（年）=""
        String shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（月）=""
        String shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（日）=""
        String shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日印刷区分判定
        switch (shiTeiKubun) {
            case ReportConstants.PRINTDATE_KBN_SHINAI:
                // 指定日印刷区分 = 1 の場合
                // 指定日（年号）=""
                shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（年）=""
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）=""
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）=""
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            case ReportConstants.PRINTDATE_KBN_PRINT:
                // 指定日印刷区分 = 2 の場合
                // 西暦日付を和暦日付に変換する
                List<String> dateParts = ReportUtil.getLocalDateToJapanDateTimeFormat(shiTeiDate);
                shiTeiDateGG = dateParts.get(ReportConstants.SHITEIDATE_GG);
                // 指定日（年）=""
                shiTeiDateYY = dateParts.get(ReportConstants.SHITEIDATE_YY);
                // 指定日（月）=""
                shiTeiDateMM = dateParts.get(ReportConstants.SHITEIDATE_MM);
                // 指定日（日）=""
                shiTeiDateDD = dateParts.get(ReportConstants.SHITEIDATE_DD);
                break;
            case ReportConstants.PRINTDATE_KBN_BLANKDATE:
                // 指定日印刷区分 = 3 の場合
                // 指定日の空欄表示処理
                DateTimeFormatter inputFormatter = DateTimeFormatter
                        .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
                DateTimeFormatter outputFormatter = DateTimeFormatter
                        .ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
                LocalDateTime dateTime = LocalDateTime.parse(systemDate, inputFormatter);
                String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
                // 指定日（年号）=""
                shiTeiDateGG = blankDate.substring(0, 2);
                // 指定日（年）=""
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）=""
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）=""
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            default:
                // 指定日（年号）=""
                shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（年）=""
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）=""
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）=""
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
        }
        return new ReportCommonShiTeiDateParts(shiTeiDateGG, shiTeiDateYY, shiTeiDateMM, shiTeiDateDD);
    }

    /**
     * 28-01 モニタリング記録表ヘッダ情報取得
     * 
     * @param sc1Id    計画期間ID
     * @param cmoni1Id ヘッダID
     * @return モニタリング記録表ヘッダ情報取得
     */
    private List<CpnMoni1OutEntity> findCpnMoni1ByCriteria(String sc1Id, String cmoni1Id) {
        CpnMoni1ByCriteriaInEntity cpnMoni1ByCriteriaInEntity = new CpnMoni1ByCriteriaInEntity();
        // 計画期間ID
        cpnMoni1ByCriteriaInEntity
                .setSc1(CommonDtoUtil.strValToInt(sc1Id));
        // ヘッダID
        cpnMoni1ByCriteriaInEntity.setCmoni1(
                CommonDtoUtil.strValToInt(cmoni1Id));
        return this.cpnTucCmoni1SelectMapper.findCpnMoni1ByCriteria(cpnMoni1ByCriteriaInEntity);

    }

    /**
     * 28-02 モニタリング記録表データ情報取得
     * 
     * @param cmoni1Id ヘッダID
     * @return モニタリング記録表データ情報
     */
    private List<CpnMoni2OutEntity> findCpnMoni2ByCriteria(String cmoni1Id) {
        CpnMoni2ByCriteriaInEntity cpnMoni2ByCriteriaInEntity = new CpnMoni2ByCriteriaInEntity();
        // ヘッダID
        cpnMoni2ByCriteriaInEntity.setCmoni1(CommonDtoUtil.strValToInt(cmoni1Id));
        return this.cpnTucCmoni2SelectMapper.findCpnMoni2ByCriteria(cpnMoni2ByCriteriaInEntity);

    }

    /**
     * 作成者を取得する。
     * 
     * @param shokuId 作成者
     * @return 作成者
     */
    private String findShokuinNameKnj2ByCriteria(int shokuId) {
        String shokuName = null;

        // 2.9. 下記の職員基本情報取得DAOを利用し、職員情報一覧情報取得する。
        ShokuinFullNameByCriteriaInEntity shokuinFullNameByCriteriaInEntity = new ShokuinFullNameByCriteriaInEntity();
        // 職員ID
        shokuinFullNameByCriteriaInEntity.setAlShoku(CommonDtoUtil.objValToString(shokuId));
        List<ShokuinFullNameOutEntity> shokuinNameList = comMscShokuinSelectMapper
                .findShokuinFullNameByCriteria(shokuinFullNameByCriteriaInEntity);

        if (CollectionUtils.isNotEmpty(shokuinNameList)) {
            shokuName = shokuinNameList.get(0).getShokuin1Knj() + CommonConstants.BLANK_SPACE
                    + shokuinNameList.get(0).getShokuin2Knj();
        }

        return shokuName;
    }

    /**
     * 利用者基本情報を取得する。
     * 
     * @param userId 利用者ID
     * @return 利用者名
     */
    private String findUserNenreiIgaiByCriteria(int userId) {
        String riyoushaNm = null;
        RiyoshaKihonByCriteriaInEntity riyoshaKihonByCriteriaInEntity = new RiyoshaKihonByCriteriaInEntity();
        // 利用者ID
        riyoshaKihonByCriteriaInEntity.setAlUserid(userId);
        List<RiyoshaKihonOutEntity> list = this.comTucUserSelectMapper.findRiyoshaKihonByCriteria(
                riyoshaKihonByCriteriaInEntity);

        if (CollectionUtils.isNotEmpty(list)) {
            riyoushaNm = list.get(0).getName1Knj() + CommonConstants.BLANK_SPACE
                    + list.get(0).getName2Knj();
        }

        return riyoushaNm;

    }

    /**
     * 印鑑欄設定情報を取得する。
     * 
     * @param emptyFlg  記入用シートを印刷するフラグ
     * @param houjinId  法人ID
     * @param shisetuId 施設ID
     * @param svJigyoId 事業所ID
     * @param section   帳票セクション番号
     * @return 印鑑欄設定情報
     */
    private List<KghCpnMstChouhyouInkanPrnOutEntity> findKghCpnMstChouhyouInkanPrnByCriteria(
            String emptyFlg,
            String houjinId,
            String shisetuId,
            String svJigyoId,
            String section) {
        List<KghCpnMstChouhyouInkanPrnOutEntity> list = new ArrayList<>();
        // クエストパラメータ.記入用シートを印刷するフラグ = false
        // の場合、印鑑欄設定情報の取得
        if (CommonDtoUtil.checkStringEqual(emptyFlg, CommonConstants.STR_FALSE)) {
            KghCpnMstChouhyouInkanPrnByCriteriaInEntity kghCpnMstChouhyouInkanPrnByCriteriaInEntity = new KghCpnMstChouhyouInkanPrnByCriteriaInEntity();

            // 法人ID
            kghCpnMstChouhyouInkanPrnByCriteriaInEntity
                    .setAnKey1(CommonDtoUtil.strValToInt(houjinId));
            // 施設ID
            kghCpnMstChouhyouInkanPrnByCriteriaInEntity
                    .setAnKey2(CommonDtoUtil.strValToInt(shisetuId));
            // 事業所ID
            kghCpnMstChouhyouInkanPrnByCriteriaInEntity
                    .setAnKey3(CommonDtoUtil.strValToInt(svJigyoId));
            // 帳票セクション番号
            kghCpnMstChouhyouInkanPrnByCriteriaInEntity.setAsSec(section);

            list = this.cpnMstChouhyouInkanSelectMapper
                    .findKghCpnMstChouhyouInkanPrnByCriteria(
                            kghCpnMstChouhyouInkanPrnByCriteriaInEntity);

        }
        return list;

    }

    /**
     * 帳票用データ詳細を設定する。
     * 
     * @param model                         入力データ
     * @param cpnMoni1List                  API定義の処理「2.1」
     * @param moniitiranDataList            モニタリング記録表一覧詳細情報のリスト
     * @param riyoushaNm                    利用者名
     * @param shokuName                     作成者
     * @param kghCpnMstChouhyouInkanPrnList API定義の処理「3」
     * @param cpnMoni2List                  API定義の処理「2.2」
     * @param columnWidth                   変数.カラム幅
     * @param sokatuHenkoColumnWidth        変数.カラム総括部幅
     * @return 帳票用データ詳細
     */
    private MonitoringRecordListReportServiceInDto setMonitoringRecordListInfoInDto(
            MonitoringRecordReportParameterModel model,
            List<CpnMoni1OutEntity> cpnMoni1List,
            List<ReportMoniitiranData> moniitiranDataList,
            String riyoushaNm,
            String shokuName,
            List<KghCpnMstChouhyouInkanPrnOutEntity> kghCpnMstChouhyouInkanPrnList,
            List<CpnMoni2OutEntity> cpnMoni2List,
            int[] columnWidth,
            int[] sokatuHenkoColumnWidth) throws Exception {
        MonitoringRecordListReportServiceInDto infoInDto = new MonitoringRecordListReportServiceInDto();

        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 印刷オプション
        ReportMonitoringRecordPrintOption printOption = model.getPrintOption();

        // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDateGG(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        // 指定日（年号）
        infoInDto.setShiTeiDateGG(ReportUtil.nullToEmpty(shiTeiDateParts.getShiTeiDateGG()));
        // 指定日（年）
        infoInDto.setShiTeiDateYY(ReportUtil.nullToEmpty(shiTeiDateParts.getShiTeiDateYY()));
        // 指定日（月）
        infoInDto.setShiTeiDateMM(ReportUtil.nullToEmpty(shiTeiDateParts.getShiTeiDateMM()));
        // 指定日（日）
        infoInDto.setShiTeiDateDD(ReportUtil.nullToEmpty(shiTeiDateParts.getShiTeiDateDD()));

        // 事業所名
        JigyoRirekiInfoDto retDto = kghKrkZCpnFuncLogic.getJigyoRirekiKnj(
                CommonDtoUtil.strValToInt(model.getJigyoInfo().getSvJigyoId()),
                model.getAppYmd());
        String jigyoName = retDto.getJigyoRirekiRyakuKnj();

        infoInDto.setJigyouName(jigyoName);

        // 要介護度
        String createYmd = CommonConstants.BLANK_STRING;

        if (!CollectionUtils.isNullOrEmpty(cpnMoni1List)) {
            createYmd = cpnMoni1List.get(0).getCreateYmd();
        }
        String yokaigoDo = this.kghCmpF01Logic.getCmpYokai(
                CommonDtoUtil.strValToInt(model.getJigyoInfo().getSvJigyoId()),
                CommonDtoUtil.strValToInt(model.getPrintSubjectHistoryList().get(0).getUserId()),
                CommonDtoUtil.strValToInt(printOption.getYokai()),
                CommonDtoUtil.strValToInt(model.getPrintSubjectHistoryList().get(0).getSc1Id()),
                createYmd);
        infoInDto.setYokaigoDo(ReportUtil.nullToEmpty(yokaigoDo));

        // 利用者名
        infoInDto.setRiyoushaNm(ReportUtil.nullToEmpty(riyoushaNm));

        // 作成年月日
        if (StringUtils.isNotEmpty(createYmd)) {
            String baseDate = kghCmpF01Logic.getCmpS2wjEz(createYmd, CommonConstants.NUMBER_1);
            ReportCommonDateParts baseDateParts = this.getDate(baseDate, false);
            // 作成年月日（年号）
            infoInDto.setCreateYmdGG(baseDateParts.getDateGG());
            // 作成年月日（年）
            infoInDto.setCreateYmdYY(baseDateParts.getDateYY());
            // 作成年月日（月）
            infoInDto.setCreateYmdMM(baseDateParts.getDateMM());
            // 作成年月日（日）
            infoInDto.setCreateYmdDD(baseDateParts.getDateDD());
        } else {
            // 作成年月日（年号）
            infoInDto.setCreateYmdGG(CommonConstants.FULL_WIDTH_SPACE);
            // 作成年月日（年）
            infoInDto.setCreateYmdYY(CommonConstants.FULL_WIDTH_SPACE);
            // 作成年月日（月）
            infoInDto.setCreateYmdMM(CommonConstants.FULL_WIDTH_SPACE);
            // 作成年月日（日）
            infoInDto.setCreateYmdDD(CommonConstants.FULL_WIDTH_SPACE);

        }

        // 作成者
        infoInDto.setShokuName(ReportUtil.nullToEmpty(shokuName));

        if (CollectionUtils.isNotEmpty(kghCpnMstChouhyouInkanPrnList)) {
            KghCpnMstChouhyouInkanPrnOutEntity kghCpnMstChouhyouInkanPrn = kghCpnMstChouhyouInkanPrnList.get(0);

            // 印鑑欄表示フラグ
            infoInDto.setInkanFlg(
                    CommonDtoUtil.strValToInt(CommonDtoUtil.objValToString(kghCpnMstChouhyouInkanPrn.getHyoujiKbn())));

            // 印鑑1~印鑑15
            infoInDto.setHanko1Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko1Knj()));
            infoInDto.setHanko2Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko2Knj()));
            infoInDto.setHanko3Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko3Knj()));
            infoInDto.setHanko4Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko4Knj()));
            infoInDto.setHanko5Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko5Knj()));
            infoInDto.setHanko6Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko6Knj()));
            infoInDto.setHanko7Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko7Knj()));
            infoInDto.setHanko8Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko8Knj()));
            infoInDto.setHanko9Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko9Knj()));
            infoInDto.setHanko10Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko10Knj()));
            infoInDto.setHanko11Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko11Knj()));
            infoInDto.setHanko12Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko12Knj()));
            infoInDto.setHanko13Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko13Knj()));
            infoInDto.setHanko14Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko14Knj()));
            infoInDto.setHanko15Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko15Knj()));
        }

        // モニタリング記録表一覧詳細情報のリスト
        JRBeanCollectionDataSource moniitiranDataSource = new JRBeanCollectionDataSource(moniitiranDataList);
        infoInDto.setMoniitiranDataList(moniitiranDataSource);

        // 総括・計画の変更等リスト
        List<ReportSokatuHenko> sokatuHenkoList = new ArrayList<ReportSokatuHenko>();
        if (CollectionUtils.isNotEmpty(cpnMoni1List)) {
            cpnMoni1List.forEach(cpnMoni1 -> {
                ReportSokatuHenko reportSokatuHenko = new ReportSokatuHenko();
                // 総括
                reportSokatuHenko.setSoukatuKnj(cpnMoni1.getSoukatuKnj());
                // 計画の変更等
                reportSokatuHenko.setHenkouKnj(cpnMoni1.getHenkouKnj());
                // 再アセスメントの必要
                reportSokatuHenko.setRetryChk(CommonDtoUtil.objValToString(cpnMoni1.getRetryChk()));
                // 実施予定日 TODO
                // reportSokatuHenko.setYoteiYmd(cpnMoni1.getYoteiYmd());

            });
        }

        JRBeanCollectionDataSource sokatuHenkoDataSource = new JRBeanCollectionDataSource(sokatuHenkoList);
        infoInDto.setSokatuHenkoList(sokatuHenkoDataSource);

        // 文書管理番号
        String bunsyoKanriNo = this.findYoushikiNoByCriteria(model.getSyscd(),
                model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getShokuId(),
                model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection(),
                model.getJigyoInfo().getSvJigyoId());
        infoInDto.setBunsyoKanriNo(ReportUtil.nullToEmpty(bunsyoKanriNo));

        return infoInDto;

    }

    /**
     * 文書番号情報取得処理
     * 
     * @param syscd     システムコード
     * @param shokuId   職員ID
     * @param section   セクション
     * @param svJigyoId サービス事業者ID
     * @return 文書番号
     */
    private String findYoushikiNoByCriteria(String syscd, String shokuId, String section, String svJigyoId) {
        String bunsyoKanriNo = null;
        // 4. 文書番号情報取得処理
        // 4.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01Logic 関数名：GetF3GkProfile
        // を利用し、設定の読込（システム環境以外の設定）情報を取得する。
        F3gkGetProfileInDto f3gkGetProfileInDto = new F3gkGetProfileInDto();
        // システムコード
        f3gkGetProfileInDto.setGsyscd(syscd);
        // 職員ＩＤ
        f3gkGetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(shokuId));
        // 法人ＩＤ
        f3gkGetProfileInDto.setHoujinId(CommonConstants.INT_0);
        // 施設ＩＤ
        f3gkGetProfileInDto.setShisetuId(CommonConstants.INT_0);
        // 事業所ＩＤ
        f3gkGetProfileInDto.setSvJigyoId(CommonConstants.INT_0);
        // 画面名
        f3gkGetProfileInDto.setKinounameKnj(CommonConstants.KINOU_NAME_PRT);
        // セクション
        f3gkGetProfileInDto.setSectionKnj(section);
        // キー
        f3gkGetProfileInDto.setKeyKnj(CommonConstants.ISO9001_FLG);
        // パラメータ
        f3gkGetProfileInDto.setAsDefault(CommonConstants.STR_0);

        String memoKbn = this.nds3GkFunc01Logic.getF3gkProfile(f3gkGetProfileInDto);

        // 4.2. 設定の読込（システム環境以外の設定）情報取得の戻り値は"1"（正常参照）の場合、下記の24-06
        // 帳票書式番号設定マスタ情報取得のDAOを利用し、文書番号を取得する。
        if (CommonDtoUtil.checkStringEqual(memoKbn, CommonConstants.STR_1)) {
            YoushikiNoByCriteriaInEntity youshikiNoByCriteriaInEntity = new YoushikiNoByCriteriaInEntity();
            // サービス事業者ID
            youshikiNoByCriteriaInEntity.setLlSvJigyoId(svJigyoId);
            // セクション
            youshikiNoByCriteriaInEntity.setAsSection(section);
            List<YoushikiNoOutEntity> list = this.comMocPrtYoushikiSelectMapper
                    .findYoushikiNoByCriteria(youshikiNoByCriteriaInEntity);

            if (CollectionUtils.isNotEmpty(list)) {
                bunsyoKanriNo = list.get(0).getYoushikiNo();

            }

        }

        return bunsyoKanriNo;

    }

    /**
     * 日付を取得する
     * 
     * @param shiTeiDate 西暦日付
     * @param flg        簡易版
     * @return 日付
     * @throws Exception 例外
     */
    private ReportCommonDateParts getDate(String date, Boolean flg) {
        // 日付（年号）=""
        String dateGG = CommonConstants.FULL_WIDTH_SPACE;
        // 日付（年）=""
        String dateYY = CommonConstants.FULL_WIDTH_SPACE;
        // 日付（月）=""
        String dateMM = CommonConstants.FULL_WIDTH_SPACE;
        // 日付（日）=""
        String dateDD = CommonConstants.FULL_WIDTH_SPACE;

        if (StringUtils.isNotEmpty(date)) {
            if (flg) {
                dateGG = date.substring(CommonConstants.INT_0, CommonConstants.INT_1);
                dateYY = date.substring(CommonConstants.INT_1, CommonConstants.INT_3);
                dateMM = date.substring(CommonConstants.INT_4, CommonConstants.INT_6);
                dateDD = date.substring(CommonConstants.INT_7, CommonConstants.INT_9);
            } else {
                dateGG = date.substring(CommonConstants.INT_0, CommonConstants.INT_2);
                dateYY = date.substring(CommonConstants.INT_2, CommonConstants.INT_4);
                dateMM = date.substring(CommonConstants.INT_5, CommonConstants.INT_7);
                dateDD = date.substring(CommonConstants.INT_8, CommonConstants.INT_10);
            }
        }

        return new ReportCommonDateParts(dateGG, dateYY, dateMM, dateDD);
    }

    /**
     * テキスト変換
     * 
     * @param cpnMoni2    解析元文字列
     * @param columnWidth カラム幅(半角)
     * @return 変換結果文字
     * @throws Exception 例外
     */
    private String textChanged(String text, int columnWidth) throws UnsupportedEncodingException {
        String kakuninKnj = null;
        var pair = kghCmpF01Logic.cmpLtext2Rows(text, columnWidth);

        kakuninKnj = CommonConstants.BLANK_STRING;
        for (int i = 0; i < pair.getRight().size(); i++) {
            String str = pair.getRight().get(i);
            if (i == pair.getRight().size() - 1) {
                kakuninKnj += str;
            } else {
                kakuninKnj += str + ReportConstants.LINE_BREAKS;
            }
        }

        return kakuninKnj;
    }

    /**
     * 長期目標フラグと番号フラグより変数.カラムCD幅を設定する
     * 
     * @param cmoniChoukiFlg 長期目標フラグ
     * @param cmoniBangouFlg 番号フラグ
     * @param columnWidths   カラムCD幅
     * @return 変数.カラムCD幅
     */
    private int[] setColumnWidth(String cmoniChoukiFlg, String cmoniBangouFlg, int[][] columnWidths) {
        int[] columnWidth = new int[] {};
        // ・リクエストパラメータ.データ.長期目標フラグ="1"
        if (CommonDtoUtil.checkStringEqual(cmoniChoukiFlg, CommonConstants.STR_1)) {
            // ・リクエストパラメータ.データ.印刷オプション.番号フラグ＝"1":
            if (CommonDtoUtil.checkStringEqual(cmoniBangouFlg, CommonConstants.STR_1)) {
                columnWidth = columnWidths[0];

            }
            // ・リクエストパラメータ.データ.印刷オプション.番号フラグ＝"0":
            else if (CommonDtoUtil.checkStringEqual(cmoniBangouFlg, CommonConstants.STR_0)) {
                columnWidth = columnWidths[1];
            }

        }
        // ・リクエストパラメータ.データ.長期目標フラグ="0"
        else if (CommonDtoUtil.checkStringEqual(cmoniChoukiFlg, CommonConstants.STR_0)) {
            // ・リクエストパラメータ.データ.印刷オプション.番号フラグ＝"1":
            if (CommonDtoUtil.checkStringEqual(cmoniBangouFlg, CommonConstants.STR_1)) {
                columnWidth = columnWidths[2];
            }
            // ・リクエストパラメータ.データ.印刷オプション.番号フラグ＝"0":
            else if (CommonDtoUtil.checkStringEqual(cmoniBangouFlg, CommonConstants.STR_0)) {
                columnWidth = columnWidths[3];
            }
        }

        return columnWidth;

    }

    /**
     * モニタリング記録表詳細情報のリスト
     * 
     * @param cpnMoni2List    モニタリング記録表データ情報
     * @param cpnMoniDddwList モニタリング記録表_DDDW
     * @param model           入力データ
     * @param columnWidth     カラム幅
     * @param cdColumnWidth   CDカラム幅
     * @return モニタリング記録表詳細情報のリスト
     * @throws Exception 例外
     */
    private List<ReportMoniitiranDetailData> setReportMoniitiranDetailDatalist(List<CpnMoni2OutEntity> cpnMoni2List,
            List<CpnMoniDddwOutEntity> cpnMoniDddwList,
            MonitoringRecordReportParameterModel model,
            int[] columnWidth,
            int[] cdColumnWidth)
            throws UnsupportedEncodingException {
        List<ReportMoniitiranDetailData> list = new ArrayList<>();

        // 変数.課題
        String kadaiKnj = CommonConstants.BLANK_SPACE;
        // 変数.長期目標
        String choukiKnj = CommonConstants.BLANK_SPACE;
        // 変数.短期目標="";
        String tankiKnj = CommonConstants.BLANK_SPACE;

        // 実行の確認ＣＤ
        String kakuninCd = CommonConstants.BLANK_STRING;
        // 確認方法ＣＤ
        String houhouCd = CommonConstants.BLANK_STRING;
        // 本人の意見ＣＤ
        String ikenHonCd = CommonConstants.BLANK_STRING;
        // 家族の意見ＣＤ
        String ikenKazCd = CommonConstants.BLANK_STRING;
        // ニーズ充足度ＣＤ
        String jusokuCd = CommonConstants.BLANK_STRING;
        // 今後の対応ＣＤ
        String taiouCd = CommonConstants.BLANK_STRING;

        for (int index = 0; index < cpnMoni2List.size(); index++) {
            CpnMoni2OutEntity cpnMoni2OutEntity = cpnMoni2List.get(index);

            ReportMoniitiranDetailData reportMoniitiranDetailData = new ReportMoniitiranDetailData();
            // モニタリング記録表詳細情報のリスト[変数.index].課題の下線フラグ＝1
            reportMoniitiranDetailData.setKadaiBottomLineFlg(CommonConstants.NUMBER_1);
            // モニタリング記録表詳細情報のリスト[変数.index].長期目標の下線フラグ=1
            reportMoniitiranDetailData.setChoukiBottomLineFlg(CommonConstants.NUMBER_1);
            // モニタリング記録表詳細情報のリスト[変数.index].短期目標の下線フラグ=1
            reportMoniitiranDetailData.setTankiBottomLineFlg(CommonConstants.NUMBER_1);

            // 課題について、
            // ・（リクエストパラメータ.データ.空白は省略＝1 かつ 情報レコード.課題＝空白）又は（リクエストパラメータ.データ.同一は省略＝1 かつ
            // 情報レコード.課題＝変数.課題）の場合、
            if ((CommonDtoUtil.checkStringEqual(model.getCmoniKeisen1Flg(), CommonConstants.STR_1)
                    && StringUtils.isEmpty(cpnMoni2OutEntity.getKadaiKnj()))
                    || (CommonDtoUtil.checkStringEqual(model.getCmoniKeisen2Flg(), CommonConstants.STR_1)
                            && CommonDtoUtil.checkStringEqual(cpnMoni2OutEntity.getKadaiKnj(), kadaiKnj))) {
                // 変数.index>0の場合、

                if (index > 0) {
                    // モニタリング記録表詳細情報のリスト[変数.index-1].課題の下線フラグ＝0
                    list.get(index - 1).setKadaiBottomLineFlg(CommonConstants.NUMBER_0);
                    // モニタリング記録表詳細情報のリスト[変数.index].課題 = ””
                    reportMoniitiranDetailData.setKadaiKnj(CommonConstants.BLANK_SPACE);

                }
            } else {
                // モニタリング記録表詳細情報のリスト[変数.index].課題= 共通関数補足の7.1のcmpLtext2Rows(情報レコード.課題,
                // 変数.カラム幅[0])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                reportMoniitiranDetailData.setKadaiKnj(ReportUtil.nullToEmpty(
                        this.textChanged(cpnMoni2OutEntity.getKadaiKnj(), columnWidth[0])));

            }

            // 長期目標について、
            // ・（リクエストパラメータ.データ.空白は省略＝1 かつ 情報レコード.長期目標＝空白）又は（リクエストパラメータ.データ.同一は省略＝1 かつ
            // 情報レコード.長期目標＝変数.長期目標）の場合、
            if ((CommonDtoUtil.checkStringEqual(model.getCmoniKeisen1Flg(), CommonConstants.STR_1)
                    && StringUtils.isEmpty(cpnMoni2OutEntity.getChoukiKnj()))
                    || (CommonDtoUtil.checkStringEqual(model.getCmoniKeisen2Flg(), CommonConstants.STR_1)
                            && CommonDtoUtil.checkStringEqual(cpnMoni2OutEntity.getChoukiKnj(), choukiKnj))) {
                // 変数.index>0の場合、
                if (index > 0) {
                    // モニタリング記録表詳細情報のリスト[変数.index-1].長期目標の下線フラグ＝0
                    list.get(index - 1).setChoukiBottomLineFlg(CommonConstants.NUMBER_0);
                    // モニタリング記録表詳細情報のリスト[変数.index].長期目標 = ””
                    reportMoniitiranDetailData.setChoukiKnj(CommonConstants.BLANK_SPACE);

                }
            } else {
                // リクエストパラメータ.データ.長期目標フラグ="0"の場合、モニタリング記録表詳細情報のリスト[変数.index].長期目標 = ””
                if (CommonDtoUtil.checkStringEqual(model.getCmoniChoukiFlg(), CommonConstants.STR_0)) {
                    reportMoniitiranDetailData.setChoukiKnj(CommonConstants.BLANK_SPACE);
                } else {
                    // モニタリング記録表詳細情報のリスト[変数.index].長期目標= 共通関数補足の7.1のcmpLtext2Rows(情報レコード.長期目標,
                    // 変数.カラム幅[0])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                    reportMoniitiranDetailData.setChoukiKnj(ReportUtil.nullToEmpty(
                            this.textChanged(cpnMoni2OutEntity.getChoukiKnj(), columnWidth[1])));
                }

            }

            // 短期目標について、
            // ・（リクエストパラメータ.データ.空白は省略＝1 かつ 情報レコード.短期目標＝空白）又は（リクエストパラメータ.データ.同一は省略＝1 かつ
            // 情報レコード.短期目標＝変数.短期目標）の場合、
            if ((CommonDtoUtil.checkStringEqual(model.getCmoniKeisen1Flg(), CommonConstants.STR_1)
                    && StringUtils.isEmpty(cpnMoni2OutEntity.getTankiKnj()))
                    || (CommonDtoUtil.checkStringEqual(model.getCmoniKeisen2Flg(), CommonConstants.STR_1)
                            && CommonDtoUtil.checkStringEqual(cpnMoni2OutEntity.getTankiKnj(), tankiKnj))) {
                // 変数.index>0の場合、
                if (index > 0) {
                    // モニタリング記録表詳細情報のリスト[変数.index-1].短期目標の下線フラグ＝0
                    list.get(index - 1).setTankiBottomLineFlg(CommonConstants.NUMBER_0);
                    // モニタリング記録表詳細情報のリスト[変数.index].短期目標 = ””
                    reportMoniitiranDetailData.setTankiKnj(CommonConstants.BLANK_SPACE);

                }
            } else {
                // モニタリング記録表詳細情報のリスト[変数.index].短期目標= 共通関数補足の7.1のcmpLtext2Rows(情報レコード.短期目標,
                // 変数.カラム幅[0])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                reportMoniitiranDetailData.setTankiKnj(ReportUtil.nullToEmpty(
                        this.textChanged(cpnMoni2OutEntity.getTankiKnj(), columnWidth[2])));

            }

            // 変数.課題=情報レコード.課題
            kadaiKnj = cpnMoni2OutEntity.getKadaiKnj();
            // 変数.長期目標=情報レコード.長期目標
            choukiKnj = cpnMoni2OutEntity.getChoukiKnj();
            // 変数.短期目標=情報レコード.短期目標
            tankiKnj = cpnMoni2OutEntity.getTankiKnj();

            // モニタリング記録表詳細情報のリスト[変数.index].課題番号 = 情報レコード.課題番号
            reportMoniitiranDetailData
                    .setKadaiNo(ReportUtil.nullToEmpty(CommonDtoUtil.objValToString(cpnMoni2OutEntity.getKadaiNo())));
            // モニタリング記録表詳細情報のリスト[変数.index].介護番号 = 情報レコード.介護番号
            reportMoniitiranDetailData
                    .setServNo(ReportUtil.nullToEmpty(CommonDtoUtil.objValToString(cpnMoni2OutEntity.getServNo())));
            // モニタリング記録表詳細情報のリスト[変数.index].サービス内容 = 共通関数補足の7.1のcmpLtext2Rows(情報レコード.サービス内容,
            // 変数.カラム幅[3])を行う、戻り値.変換結果文字配列リストを改行で結合する。
            reportMoniitiranDetailData.setKaigoKnj(
                    ReportUtil.nullToEmpty(this.textChanged(cpnMoni2OutEntity.getKaigoKnj(), columnWidth[3])));

            if (!CollectionUtils.isNullOrEmpty(cpnMoniDddwList)) {
                for (CpnMoniDddwOutEntity cpnMoniDddw : cpnMoniDddwList) {
                    // 実行の確認ＣＤ
                    // 上記①取得したデータから入力区分＝"16"と区分コード＝情報レコード.実行の確認CDを絞込む、内容を取得する。取得できない場合、内容=""
                    // 共通関数補足の7.1のcmpLtext2Rows(内容, 変数.カラムCD幅[0])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                    if (cpnMoniDddw.getCf1Kbn() == CommonConstants.INT_16 && cpnMoniDddw.getKbnCd() == cpnMoni2OutEntity.getKakuninCd()) {
                        kakuninCd = this.textChanged(cpnMoniDddw.getTextKnj(), cdColumnWidth[0]);
                    }

                    // 確認方法ＣＤ
                    // 上記①取得したデータから入力区分＝"20"と区分コード＝情報レコード.実行の確認CDを絞込む、内容を取得する。取得できない場合、内容=""
                    // 共通関数補足の7.1のcmpLtext2Rows(内容, 変数.カラムCD幅[1])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                    if (cpnMoniDddw.getCf1Kbn() == CommonConstants.INT_20 && cpnMoniDddw.getKbnCd() == cpnMoni2OutEntity.getHouhouCd()) {
                        houhouCd = this.textChanged(cpnMoniDddw.getTextKnj(), cdColumnWidth[1]);
                    }

                    // 本人の意見ＣＤ
                    // 上記①取得したデータから入力区分＝"17"と区分コード＝情報レコード.本人の意見CDを絞込む、内容を取得する。取得できない場合、内容=""
                    // 共通関数補足の7.1のcmpLtext2Rows(内容, 変数.カラムCD幅[2])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                    if (cpnMoniDddw.getCf1Kbn() == CommonConstants.INT_17 && cpnMoniDddw.getKbnCd() == cpnMoni2OutEntity.getIkenHonCd()) {
                        ikenHonCd = this.textChanged(cpnMoniDddw.getTextKnj(), cdColumnWidth[2]);
                    }

                    // 家族の意見ＣＤ
                    // 上記①取得したデータから入力区分＝"17"と区分コード＝情報レコード.家族の意見CDを絞込む、内容を取得する。取得できない場合、内容=""
                    // 共通関数補足の7.1のcmpLtext2Rows(内容, 変数.カラムCD幅[3])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                    if (cpnMoniDddw.getCf1Kbn() == CommonConstants.INT_17 && cpnMoniDddw.getKbnCd() == cpnMoni2OutEntity.getIkenKazCd()) {
                        ikenKazCd = this.textChanged(cpnMoniDddw.getTextKnj(), cdColumnWidth[3]);
                    }

                    // ニーズ充足度ＣＤ
                    // 上記①取得したデータから入力区分＝"18"と区分コード＝情報レコード.ニーズ充足度ＣＤを絞込む、内容を取得する。取得できない場合、内容=""
                    // 共通関数補足の7.1のcmpLtext2Rows(内容, 変数.カラムCD幅[4])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                    if (cpnMoniDddw.getCf1Kbn() == CommonConstants.INT_18 && cpnMoniDddw.getKbnCd() == cpnMoni2OutEntity.getJusokuCd()) {
                        jusokuCd = this.textChanged(cpnMoniDddw.getTextKnj(), cdColumnWidth[4]);
                    }

                    // 今後の対応ＣＤ
                    // 上記①取得したデータから入力区分＝"19"と区分コード＝情報レコード.今後の対応CDを絞込む、内容を取得する。取得できない場合、内容=""
                    // 共通関数補足の7.1のcmpLtext2Rows(内容, 変数.カラムCD幅[5])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                    if (cpnMoniDddw.getCf1Kbn() == CommonConstants.INT_19 && cpnMoniDddw.getKbnCd() == cpnMoni2OutEntity.getTaiouCd()) {
                        taiouCd = this.textChanged(cpnMoniDddw.getTextKnj(), cdColumnWidth[5]);
                    }
                }
            }

            // 実行の確認ＣＤ
            reportMoniitiranDetailData.setKakuninCd(kakuninCd);

            // 実行の確認
            // 共通関数補足の7.1のcmpLtext2Rows(情報レコード.実行の確認,変数.カラム幅[4])を行う、戻り値.変換結果文字配列リストを改行で結合する。
            String kakuninKnj = this.textChanged(cpnMoni2OutEntity.getKakuninKnj(), columnWidth[4]);
            reportMoniitiranDetailData.setKakuninKnj(kakuninKnj);

            // 確認方法ＣＤ
            reportMoniitiranDetailData.setHouhouCd(houhouCd);

            // 確認方法
            // 共通関数補足の7.1のcmpLtext2Rows(情報レコード.確認方法,変数.カラム幅[5])を行う、戻り値.変換結果文字配列リストを改行で結合する。
            String houhouKnj = this.textChanged(cpnMoni2OutEntity.getHouhouKnj(), columnWidth[5]);
            reportMoniitiranDetailData.setHouhouKnj(houhouKnj);

            // 確認期日
            if (StringUtils.isNotEmpty(cpnMoni2OutEntity.getKakuninYmd())) {
                String baseDate = kghCmpF01Logic.getCmpS2wEz(cpnMoni2OutEntity.getKakuninYmd(),
                        CommonConstants.NUMBER_1);
                ReportCommonDateParts baseDateParts = this.getDate(baseDate, true);
                // 確認期日（年号）
                reportMoniitiranDetailData.setKakuninYmdGG(baseDateParts.getDateGG());
                // 確認期日（年）
                reportMoniitiranDetailData.setKakuninYmdYY(baseDateParts.getDateYY());
                // 確認期日（月）
                reportMoniitiranDetailData.setKakuninYmdMM(baseDateParts.getDateMM());
                // 確認期日（日）
                reportMoniitiranDetailData.setKakuninYmdDD(baseDateParts.getDateDD());
            } else {
                // 確認期日（年号）
                reportMoniitiranDetailData.setKakuninYmdGG(CommonConstants.FULL_WIDTH_SPACE);
                // 確認期日（年）
                reportMoniitiranDetailData.setKakuninYmdYY(CommonConstants.FULL_WIDTH_SPACE);
                // 確認期日（月）
                reportMoniitiranDetailData.setKakuninYmdMM(CommonConstants.FULL_WIDTH_SPACE);
                // 確認期日（日）
                reportMoniitiranDetailData.setKakuninYmdDD(CommonConstants.FULL_WIDTH_SPACE);
            }

            // 本人の意見ＣＤ
            reportMoniitiranDetailData.setIkenHonCd(ikenHonCd);

            // 本人の意見
            // 共通関数補足の7.1のcmpLtext2Rows(情報レコード.本人の意見,変数.カラム幅[6])を行う、戻り値.変換結果文字配列リストを改行で結合する。
            String ikenHonKnj = this.textChanged(cpnMoni2OutEntity.getIkenHonKnj(), columnWidth[6]);
            reportMoniitiranDetailData.setIkenHonKnj(ikenHonKnj);

            // 家族の意見ＣＤ
            reportMoniitiranDetailData.setIkenKazCd(ikenKazCd);

            // 家族の意見
            // 共通関数補足の7.1のcmpLtext2Rows(情報レコード.家族の意見,変数.カラム幅[7])を行う、戻り値.変換結果文字配列リストを改行で結合する。
            String ikenKazKnj = this.textChanged(cpnMoni2OutEntity.getIkenKazKnj(), columnWidth[7]);
            reportMoniitiranDetailData.setIkenKazKnj(ikenKazKnj);

            // ニーズ充足度ＣＤ
            reportMoniitiranDetailData.setJusokuCd(jusokuCd);

            // ニーズ充足度
            // 共通関数補足の7.1のcmpLtext2Rows(情報レコード.ニーズ充足度,変数.カラム幅[8])を行う、戻り値.変換結果文字配列リストを改行で結合する。
            String jusokuKnj = this.textChanged(cpnMoni2OutEntity.getJusokuKnj(), columnWidth[8]);
            reportMoniitiranDetailData.setJusokuKnj(jusokuKnj);

            // 今後の対応ＣＤ
            reportMoniitiranDetailData.setTaiouCd(taiouCd);

            // 今後の対応
            // 共通関数補足の7.1のcmpLtext2Rows(情報レコード.今後の対応,変数.カラム幅[9])を行う、戻り値.変換結果文字配列リストを改行で結合する。
            String taiouKnj = this.textChanged(cpnMoni2OutEntity.getTaiouKnj(), columnWidth[9]);
            reportMoniitiranDetailData.setTaiouKnj(taiouKnj);

            list.add(reportMoniitiranDetailData);

        }

        return list;

    }

    /**
     * 帳票用データ詳細を設定する。
     * 
     * @param model                         入力データ
     * @param cpnMoni1List                  API定義の処理「2.1」
     * @param riyoushaNm                    利用者名
     * @param shokuName                     作成者
     * @param kghCpnMstChouhyouInkanPrnList API定義の処理「3」
     * @param list                          モニタリング記録表詳細情報のリスト
     * @param soukatuColumnWidth            変数.カラム総括部幅
     * @throws Exception 例外
     */
    private MonitoringRecordReportServiceInDto setMonitoringRecordInfoInDto(MonitoringRecordReportParameterModel model,
            List<CpnMoni1OutEntity> cpnMoni1List, String riyoushaNm, String shokuName,
            List<KghCpnMstChouhyouInkanPrnOutEntity> kghCpnMstChouhyouInkanPrnList,
            List<ReportMoniitiranDetailData> list, int[] soukatuColumnWidth) throws Exception {
        MonitoringRecordReportServiceInDto infoInDto = new MonitoringRecordReportServiceInDto();

        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();

        // 印刷オプション
        ReportMonitoringRecordPrintOption printOption = model.getPrintOption();

        // 印刷対象履歴
        ReportMonitoringRecordPrintSubjectHistory printSubjectHistory = model
                .getPrintSubjectHistoryList().get(0);

        // 指定日
        // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDateGG(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        // 指定日（年号）
        infoInDto.setShiTeiDateGG(ReportUtil.nullToEmpty(shiTeiDateParts.getShiTeiDateGG()));
        // 指定日（年）
        infoInDto.setShiTeiDateYY(ReportUtil.nullToEmpty(shiTeiDateParts.getShiTeiDateYY()));
        // 指定日（月）
        infoInDto.setShiTeiDateMM(ReportUtil.nullToEmpty(shiTeiDateParts.getShiTeiDateMM()));
        // 指定日（日）
        infoInDto.setShiTeiDateDD(ReportUtil.nullToEmpty(shiTeiDateParts.getShiTeiDateDD()));

        // 要介護度
        String createYmd = CommonConstants.BLANK_STRING;
        if (!CollectionUtils.isNullOrEmpty(cpnMoni1List)) {
            createYmd = cpnMoni1List.get(0).getCreateYmd();

        }
        String yokaigoDo = this.kghCmpF01Logic.getCmpYokai(
                CommonDtoUtil.strValToInt(model.getJigyoInfo().getSvJigyoId()),
                CommonDtoUtil.strValToInt(printSubjectHistory.getUserId()),
                CommonDtoUtil.strValToInt(printOption.getYokai()),
                CommonDtoUtil.strValToInt(printSubjectHistory.getSc1Id()),
                createYmd);
        infoInDto.setYokaigoDo(ReportUtil.nullToEmpty(yokaigoDo));

        // 利用者名
        infoInDto.setRiyoushaNm(ReportUtil.nullToEmpty(riyoushaNm));

        // 作成年月日
        if (StringUtils.isNotEmpty(createYmd)) {
            String baseDate = kghCmpF01Logic.getCmpS2wjEz(createYmd, CommonConstants.NUMBER_1);
            ReportCommonDateParts baseDateParts = this.getDate(baseDate, false);
            // 作成年月日（年号）
            infoInDto.setCreateYmdGG(ReportUtil.nullToEmpty(baseDateParts.getDateGG()));
            // 作成年月日（年）
            infoInDto.setCreateYmdYY(ReportUtil.nullToEmpty(baseDateParts.getDateYY()));
            // 作成年月日（月）
            infoInDto.setCreateYmdMM(ReportUtil.nullToEmpty(baseDateParts.getDateMM()));
            // 作成年月日（日）
            infoInDto.setCreateYmdDD(ReportUtil.nullToEmpty(baseDateParts.getDateDD()));
        } else {
            // 作成年月日（年号）
            infoInDto.setCreateYmdGG(CommonConstants.FULL_WIDTH_SPACE);
            // 作成年月日（年）
            infoInDto.setCreateYmdYY(CommonConstants.FULL_WIDTH_SPACE);
            // 作成年月日（月）
            infoInDto.setCreateYmdMM(CommonConstants.FULL_WIDTH_SPACE);
            // 作成年月日（日）
            infoInDto.setCreateYmdDD(CommonConstants.FULL_WIDTH_SPACE);

        }

        // 作成者
        infoInDto.setShokuName(ReportUtil.nullToEmpty(shokuName));

        if (!CollectionUtils.isNullOrEmpty(kghCpnMstChouhyouInkanPrnList)) {
            KghCpnMstChouhyouInkanPrnOutEntity kghCpnMstChouhyouInkanPrn = kghCpnMstChouhyouInkanPrnList.get(0);
            // 印鑑欄表示フラグ
            infoInDto.setHyoujiKbn(
                    CommonDtoUtil.strValToInt(CommonDtoUtil.objValToString(kghCpnMstChouhyouInkanPrn.getHyoujiKbn())));
            // 印鑑1~印鑑15
            infoInDto.setHanko1Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko1Knj()));
            infoInDto.setHanko2Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko2Knj()));
            infoInDto.setHanko3Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko3Knj()));
            infoInDto.setHanko4Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko4Knj()));
            infoInDto.setHanko5Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko5Knj()));
            infoInDto.setHanko6Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko6Knj()));
            infoInDto.setHanko7Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko7Knj()));
            infoInDto.setHanko8Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko8Knj()));
            infoInDto.setHanko9Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko9Knj()));
            infoInDto.setHanko10Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko10Knj()));
            infoInDto.setHanko11Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko11Knj()));
            infoInDto.setHanko12Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko12Knj()));
            infoInDto.setHanko13Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko13Knj()));
            infoInDto.setHanko14Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko14Knj()));
            infoInDto.setHanko15Knj(ReportUtil.nullToEmpty(kghCpnMstChouhyouInkanPrn.getHanko15Knj()));
        } else {
            // 印鑑欄表示フラグ
            infoInDto.setHyoujiKbn(CommonConstants.NUMBER_0);

            // 印鑑1~印鑑15
            infoInDto.setHanko1Knj(CommonConstants.BLANK_STRING);
            infoInDto.setHanko2Knj(CommonConstants.BLANK_STRING);
            infoInDto.setHanko3Knj(CommonConstants.BLANK_STRING);
            infoInDto.setHanko4Knj(CommonConstants.BLANK_STRING);
            infoInDto.setHanko5Knj(CommonConstants.BLANK_STRING);
            infoInDto.setHanko6Knj(CommonConstants.BLANK_STRING);
            infoInDto.setHanko7Knj(CommonConstants.BLANK_STRING);
            infoInDto.setHanko8Knj(CommonConstants.BLANK_STRING);
            infoInDto.setHanko9Knj(CommonConstants.BLANK_STRING);
            infoInDto.setHanko10Knj(CommonConstants.BLANK_STRING);
            infoInDto.setHanko11Knj(CommonConstants.BLANK_STRING);
            infoInDto.setHanko12Knj(CommonConstants.BLANK_STRING);
            infoInDto.setHanko13Knj(CommonConstants.BLANK_STRING);
            infoInDto.setHanko14Knj(CommonConstants.BLANK_STRING);
            infoInDto.setHanko15Knj(CommonConstants.BLANK_STRING);

        }

        // 総括リスト
        List<ReportSokatuHenko> soukatuList = new ArrayList<ReportSokatuHenko>();
        ReportSokatuHenko sokatuHenko = new ReportSokatuHenko();
        if (!CollectionUtils.isNullOrEmpty(cpnMoni1List)) {
            // 総括
            String soukatuKnj = this.textChanged(cpnMoni1List.get(0).getSoukatuKnj(), soukatuColumnWidth[0]);
            sokatuHenko.setSoukatuKnj(soukatuKnj);

            // 計画の変更等
            String henkouKnj = this.textChanged(cpnMoni1List.get(0).getHenkouKnj(), soukatuColumnWidth[1]);
            sokatuHenko.setHenkouKnj(henkouKnj);

            // 再アセスメントの必要
            sokatuHenko.setRetryChk(cpnMoni1List.get(0).getRetryKnj());

            // 実施予定日
            if (StringUtils.isNotEmpty(cpnMoni1List.get(0).getShokaiYmd())) {
                String baseDate = kghCmpF01Logic.getCmpS2wEz(cpnMoni1List.get(0).getShokaiYmd(),
                        CommonConstants.NUMBER_1);
                ReportCommonDateParts baseDateParts = this.getDate(baseDate, true);
                // 実施予定日（年号）
                sokatuHenko.setYoteiYmdGG(baseDateParts.getDateGG());
                // 実施予定日（年）
                sokatuHenko.setYoteiYmdYY(baseDateParts.getDateYY());
                // 実施予定日（月）
                sokatuHenko.setYoteiYmdMM(baseDateParts.getDateMM());
                // 実施予定日（日）
                sokatuHenko.setYoteiYmdDD(baseDateParts.getDateDD());
            } else {
                // 実施予定日（年号）
                sokatuHenko.setYoteiYmdGG(CommonConstants.FULL_WIDTH_SPACE);
                // 実施予定日（年）
                sokatuHenko.setYoteiYmdYY(CommonConstants.FULL_WIDTH_SPACE);
                // 実施予定日（月）
                sokatuHenko.setYoteiYmdMM(CommonConstants.FULL_WIDTH_SPACE);
                // 実施予定日（日）
                sokatuHenko.setYoteiYmdDD(CommonConstants.FULL_WIDTH_SPACE);
            }
            soukatuList.add(sokatuHenko);

        }

        JRBeanCollectionDataSource soukatuListDataSource = new JRBeanCollectionDataSource(soukatuList);
        infoInDto.setSoukatuList(soukatuListDataSource);

        // 文書管理番号
        String bunsyoKanriNo = this.findYoushikiNoByCriteria(model.getSyscd(),
                model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getShokuId(),
                model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection(),
                model.getJigyoInfo().getSvJigyoId());
        infoInDto.setBunsyoKanriNo(ReportUtil.nullToEmpty(bunsyoKanriNo));

        // モニタリング記録表一覧詳細情報のリスト
        List<ReportMoniitiranDetailData> moniitiranDetailDataList = list;

        JRBeanCollectionDataSource moniitiranDataSource = new JRBeanCollectionDataSource(moniitiranDetailDataList);
        infoInDto.setList(moniitiranDataSource);

        return infoInDto;

    }

    /**
     * 総括と計画の変更等設定する。
     * 
     * @param model 一覧印刷するフラグ
     * @return 総括と計画の変更
     */
    private String getSoukatuKnjAndHenkouKnj(MonitoringRecordReportParameterModel model) {
        // 4.2.リクエストパラメータ.記入用シートを印刷するフラグ = true の場合、総括と計画の変更等設定する。
        // 4.2.1. リクエストパラメータ.データ.印刷オプション.承認欄を印刷するフラグ="1"とリクエストパラメータ.記入用シートを印刷するフラグ =
        // true の場合、承認欄設定の共通関数で承認欄を設定する
        int row = 0;

        if (CommonDtoUtil.checkStringEqual(model.getPrintOption().getShoninFlg(), CommonConstants.STR_1)) {
            CmpMstShoninByCriteriaInEntity cmpMstShoninByCriteriaInEntity = new CmpMstShoninByCriteriaInEntity();
            // ｻｰﾋﾞｽ事業者ID
            cmpMstShoninByCriteriaInEntity.setJigyoId(CommonDtoUtil.strValToInt(model.getJigyoInfo().getSvJigyoId()));
            // フラグ
            // リクエストパラメータ.データ.帳票毎の承認欄の保持=1の場合は１、以外は0
            cmpMstShoninByCriteriaInEntity.setAlFlg(
                    CommonDtoUtil.checkStringEqual(model.getShoninFlg(), CommonConstants.STR_1)
                            ? CommonConstants.NUMBER_1
                            : CommonConstants.NUMBER_0);
            // 帳票コード
            cmpMstShoninByCriteriaInEntity.setAsCode(
                    CommonDtoUtil.checkStringEqual(model.getShoninFlg(), CommonConstants.STR_1)
                            ? model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection()
                            : CommonConstants.BLANK_SPACE);
            // 承認欄方向
            cmpMstShoninByCriteriaInEntity.setAiType(CommonConstants.NUMBER_1);

            List<CmpMstShoninOutEntity> list = this.cpnMucShoninSelectMapper
                    .findCmpMstShoninByCriteria(cmpMstShoninByCriteriaInEntity);

            // 4.2.2.変数.DispKbn＝「4.2.1.」で取得した承認欄マスタ情報.DispKbn、取得できない場合、変数.DispKbn=2。
            Integer dispKbn = null;

            if (CollectionUtils.isNotEmpty(list)) {
                dispKbn = list.get(0).getDispKbn();

            } else {
                dispKbn = CommonConstants.NUMBER_2;
            }

            // 変数.DispKbnより、総括、計画の変更等を設定する。
            // ・リクエストパラメータ.データ.印刷オプション.一覧印刷する = "1"(一覧表示)場合
            if (CommonDtoUtil.checkStringEqual(model.getPrintOption().getIchiranFlg(), CommonConstants.STR_1)) {
                // 変数.DispKbn=1、総括、計画の変更等＝4行の空白行(" \r\n \r\n \r\n \r\n ")
                if (CommonConstants.NUMBER_1 == dispKbn) {
                    row = 4;
                }
                // 変数.DispKbn=2、総括、計画の変更等＝3行の空白行(" \r\n \r\n \r\n ")
                else if (CommonConstants.NUMBER_2 == dispKbn) {
                    row = 3;
                }
                // 変数.DispKbn=3、総括、計画の変更等＝2行の空白行(" \r\n \r\n ")
                else if (CommonConstants.NUMBER_3 == dispKbn) {
                    row = 2;
                }
                // 変数.DispKbn=4、総括、計画の変更等＝””
                else if (CommonConstants.NUMBER_4 == dispKbn) {
                    row = 0;
                }

            } else {
                // 変数.DispKbn=1、総括、計画の変更等＝3行の空白行(" \r\n \r\n \r\n ")
                if (CommonConstants.NUMBER_1 == dispKbn) {
                    row = 3;
                }
                // 変数.DispKbn=2、総括、計画の変更等＝3行の空白行(" \r\n \r\n \r\n ")
                else if (CommonConstants.NUMBER_2 == dispKbn) {
                    row = 3;
                }
                // 変数.DispKbn=3、総括、計画の変更等＝2行の空白行(" \r\n \r\n ")
                else if (CommonConstants.NUMBER_3 == dispKbn) {
                    row = 2;
                }
                // 変数.DispKbn=4、総括、計画の変更等＝1行の空白行(" \r\n ")
                else if (CommonConstants.NUMBER_4 == dispKbn) {
                    row = 1;
                }

            }

        }
        // 4.2.2. リクエストパラメータ.データ.印刷オプション.承認欄を印刷するフラグ="0"とリクエストパラメータ.記入用シートを印刷するフラグ =
        // true の場合、総括、計画の変更等＝4行の空白行(" \r\n \r\n \r\n \r\n ")
        else if (CommonDtoUtil.checkStringEqual(model.getPrintOption().getShoninFlg(), CommonConstants.STR_0)) {
            row = 4;
        }

        String blankStr = CommonConstants.BLANK_STRING;
        for (int i = 0; i < row; i++) {
            blankStr += ReportConstants.LINE_BREAKS;
        }

        return blankStr;

    }

    /**
     * モニタリング記録表一覧詳細情報のリストを設定する。
     * 
     * @param cpnMoni2List    モニタリング記録表データ情報取得
     * @param cpnMoniDddwList モニタリング記録表_DDDW
     * @param columnWidth     変数.カラム幅
     * @return モニタリング記録表一覧詳細情報リスト
     */
    private List<ReportMoniitiranData> setMoniitiranDataList(List<CpnMoni2OutEntity> cpnMoni2List,
            List<CpnMoniDddwOutEntity> cpnMoniDddwList, int[] columnWidth) throws Exception {
        List<ReportMoniitiranData> moniitiranDataList = new ArrayList<ReportMoniitiranData>();

        if (CollectionUtils.isNotEmpty(cpnMoni2List)) {
            for (CpnMoni2OutEntity cpnMoni2OutEntity : cpnMoni2List) {
                ReportMoniitiranData reportMoniitiranData = new ReportMoniitiranData();
                // 課題番号
                reportMoniitiranData.setKaidaiNo(ReportUtil.nullToEmpty(cpnMoni2OutEntity.getKadaiNo()));
                // 番号
                reportMoniitiranData.setServNo(ReportUtil.nullToEmpty(cpnMoni2OutEntity.getServNo()));

                // 実行の確認ＣＤ
                String kakuninCd = CommonConstants.BLANK_STRING;
                // 確認方法ＣＤ
                String houhouCd = CommonConstants.BLANK_STRING;
                // 本人の意見ＣＤ
                String ikenHonCd = CommonConstants.BLANK_STRING;
                // 家族の意見ＣＤ
                String ikenKazCd = CommonConstants.BLANK_STRING;
                // ニーズ充足度ＣＤ
                String jusokuCd = CommonConstants.BLANK_STRING;
                // 今後の対応ＣＤ
                String taiouCd = CommonConstants.BLANK_STRING;

                if (!CollectionUtils.isNullOrEmpty(cpnMoniDddwList)) {
                    for (CpnMoniDddwOutEntity cpnMoniDddw : cpnMoniDddwList) {
                        // 上記①取得したデータから入力区分＝"16"と区分コード＝情報レコード.実行の確認CDを絞込む、内容を取得する。取得できない場合、内容=""
                        // 共通関数補足の7.1のcmpLtext2Rows(内容, 変数.カラムCD幅[0])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                        if (cpnMoniDddw.getCf1Kbn() == CommonConstants.INT_16 && cpnMoniDddw.getKbnCd() == cpnMoni2OutEntity.getKakuninCd()) {
                            kakuninCd = this.textChanged(cpnMoniDddw.getTextKnj(), columnWidth[0]);
                        }

                        // 確認方法ＣＤ
                        // 上記①取得したデータから入力区分＝"20"と区分コード＝情報レコード.実行の確認CDを絞込む、内容を取得する。取得できない場合、内容=""
                        // 共通関数補足の7.1のcmpLtext2Rows(内容, 変数.カラムCD幅[1])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                        if (cpnMoniDddw.getCf1Kbn() == CommonConstants.INT_20 && cpnMoniDddw.getKbnCd() == cpnMoni2OutEntity.getHouhouCd()) {
                            houhouCd = this.textChanged(cpnMoniDddw.getTextKnj(), columnWidth[1]);
                        }

                        // 本人の意見ＣＤ
                        // 上記①取得したデータから入力区分＝"17"と区分コード＝情報レコード.本人の意見CDを絞込む、内容を取得する。取得できない場合、内容=""
                        // 共通関数補足の7.1のcmpLtext2Rows(内容, 変数.カラムCD幅[2])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                        if (cpnMoniDddw.getCf1Kbn() == CommonConstants.INT_17
                                && cpnMoniDddw.getKbnCd() == cpnMoni2OutEntity.getIkenHonCd()) {
                            ikenHonCd = this.textChanged(cpnMoniDddw.getTextKnj(), columnWidth[2]);
                        }

                        // 家族の意見ＣＤ
                        // 上記①取得したデータから入力区分＝"17"と区分コード＝情報レコード.家族の意見CDを絞込む、内容を取得する。取得できない場合、内容=""
                        // 共通関数補足の7.1のcmpLtext2Rows(内容, 変数.カラムCD幅[3])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                        if (cpnMoniDddw.getCf1Kbn() == CommonConstants.INT_17 && cpnMoniDddw.getKbnCd() == cpnMoni2OutEntity.getIkenKazCd()) {
                            ikenKazCd = this.textChanged(cpnMoniDddw.getTextKnj(), columnWidth[3]);
                        }

                        // ニーズ充足度ＣＤ
                        // 上記①取得したデータから入力区分＝"18"と区分コード＝情報レコード.ニーズ充足度ＣＤを絞込む、内容を取得する。取得できない場合、内容=""
                        // 共通関数補足の7.1のcmpLtext2Rows(内容, 変数.カラムCD幅[4])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                        if (cpnMoniDddw.getCf1Kbn() == CommonConstants.INT_18 && cpnMoniDddw.getKbnCd() == cpnMoni2OutEntity.getJusokuCd()) {
                            jusokuCd = this.textChanged(cpnMoniDddw.getTextKnj(), columnWidth[4]);
                        }

                        // 今後の対応ＣＤ
                        // 上記①取得したデータから入力区分＝"19"と区分コード＝情報レコード.今後の対応CDを絞込む、内容を取得する。取得できない場合、内容=""
                        // 共通関数補足の7.1のcmpLtext2Rows(内容, 変数.カラムCD幅[5])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                        taiouCd = CommonConstants.BLANK_STRING;
                        if (cpnMoniDddw.getCf1Kbn() == CommonConstants.INT_19 && cpnMoniDddw.getKbnCd() == cpnMoni2OutEntity.getTaiouCd()) {
                            taiouCd = this.textChanged(cpnMoniDddw.getTextKnj(), columnWidth[5]);
                        }
                    }
                }
                reportMoniitiranData.setKakuninCd(kakuninCd);

                // 実行の確認
                // 共通関数補足の7.1のcmpLtext2Rows(情報レコード.実行の確認,変数.カラム幅[0])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                String kakuninKnj = this.textChanged(cpnMoni2OutEntity.getKakuninKnj(), columnWidth[0]);
                reportMoniitiranData.setKakuninKnj(kakuninKnj);

                // 確認方法ＣＤ
                reportMoniitiranData.setHouhouCd(houhouCd);

                // 確認方法
                // 共通関数補足の7.1のcmpLtext2Rows(情報レコード.確認方法,変数.カラム幅[1])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                String houhouKnj = this.textChanged(cpnMoni2OutEntity.getHouhouKnj(), columnWidth[1]);
                reportMoniitiranData.setHouhouKnj(houhouKnj);

                // 確認期日
                if (StringUtils.isNotEmpty(cpnMoni2OutEntity.getKakuninYmd())) {
                    String baseDate = kghCmpF01Logic.getCmpS2wEz(cpnMoni2OutEntity.getKakuninYmd(),
                            CommonConstants.NUMBER_1);
                    ReportCommonDateParts baseDateParts = this.getDate(baseDate, true);
                    // 確認期日（年号）
                    reportMoniitiranData.setKakuninYmdGG(baseDateParts.getDateGG());
                    // 確認期日（年）
                    reportMoniitiranData.setKakuninYmdYY(baseDateParts.getDateYY());
                    // 確認期日（月）
                    reportMoniitiranData.setKakuninYmdMM(baseDateParts.getDateMM());
                    // 確認期日（日）
                    reportMoniitiranData.setKakuninYmdDD(baseDateParts.getDateDD());
                } else {
                    // 確認期日（年号）
                    reportMoniitiranData.setKakuninYmdGG(CommonConstants.FULL_WIDTH_SPACE);
                    // 確認期日（年）
                    reportMoniitiranData.setKakuninYmdYY(CommonConstants.FULL_WIDTH_SPACE);
                    // 確認期日（月）
                    reportMoniitiranData.setKakuninYmdMM(CommonConstants.FULL_WIDTH_SPACE);
                    // 確認期日（日）
                    reportMoniitiranData.setKakuninYmdDD(CommonConstants.FULL_WIDTH_SPACE);
                }

                // 本人の意見ＣＤ
                reportMoniitiranData.setIkenHonCd(ikenHonCd);

                // 本人の意見
                // 共通関数補足の7.1のcmpLtext2Rows(情報レコード.本人の意見,変数.カラム幅[2])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                String ikenHonKnj = this.textChanged(cpnMoni2OutEntity.getIkenHonKnj(), columnWidth[2]);
                reportMoniitiranData.setIkenHonKnj(ikenHonKnj);

                reportMoniitiranData.setIkenKazCd(ikenKazCd);

                // 家族の意見
                // 共通関数補足の7.1のcmpLtext2Rows(情報レコード.家族の意見,変数.カラム幅[3])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                String ikenKazKnj = this.textChanged(cpnMoni2OutEntity.getIkenKazKnj(), columnWidth[3]);
                reportMoniitiranData.setIkenKazKnj(ikenKazKnj);

                // ニーズ充足度ＣＤ
                reportMoniitiranData.setJusokuCd(jusokuCd);

                // ニーズ充足度
                // 共通関数補足の7.1のcmpLtext2Rows(情報レコード.ニーズ充足度,変数.カラム幅[4])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                String jusokuKnj = this.textChanged(cpnMoni2OutEntity.getJusokuKnj(), columnWidth[4]);
                reportMoniitiranData.setJusokuKnj(jusokuKnj);

                // 今後の対応ＣＤ
                reportMoniitiranData.setTaiouCd(taiouCd);

                // 今後の対応
                // 共通関数補足の7.1のcmpLtext2Rows(情報レコード.今後の対応,変数.カラム幅[5])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                String taiouKnj = this.textChanged(cpnMoni2OutEntity.getTaiouKnj(), columnWidth[5]);
                reportMoniitiranData.setTaiouKnj(taiouKnj);

                moniitiranDataList.add(reportMoniitiranData);
            }

        }
        return moniitiranDataList;

    }

    /**
     * モニタリング記録表詳細情報のリストを設定する。
     * 
     * @return モニタリング記録表一覧詳細情報リスト
     */
    private ReportMoniitiranDetailData setReportMoniitiranDetailData() {
        ReportMoniitiranDetailData reportMoniitiranDetailData = new ReportMoniitiranDetailData();
        reportMoniitiranDetailData.setKadaiNo(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setKadaiKnj(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setChoukiKnj(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setTankiKnj(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setServNo(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setKaigoKnj(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setKakuninCd(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setKakuninKnj(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setHouhouCd(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setHouhouKnj(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setKakuninYmdGG(CommonConstants.FULL_WIDTH_SPACE);
        reportMoniitiranDetailData.setKakuninYmdYY(CommonConstants.FULL_WIDTH_SPACE);
        reportMoniitiranDetailData.setKakuninYmdMM(CommonConstants.FULL_WIDTH_SPACE);
        reportMoniitiranDetailData.setKakuninYmdDD(CommonConstants.FULL_WIDTH_SPACE);
        reportMoniitiranDetailData.setIkenHonCd(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setIkenHonKnj(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setIkenKazCd(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setIkenKazKnj(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setJusokuCd(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setJusokuKnj(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setTaiouCd(CommonConstants.BLANK_STRING);
        reportMoniitiranDetailData.setTaiouKnj(CommonConstants.BLANK_STRING);

        return reportMoniitiranDetailData;
    }
}
