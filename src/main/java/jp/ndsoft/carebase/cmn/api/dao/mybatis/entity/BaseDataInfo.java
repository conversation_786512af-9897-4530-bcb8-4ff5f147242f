package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.09.01
 * <AUTHOR>
 *         V00261_限度額超過利用者・算定エラー利用者一覧の基データリスト
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BaseDataInfo implements Serializable {
    /** UID. */
    private static final long serialVersionUID = 1L;

    /** データタイプ */
    private Integer datatype;

    /** 支援ID */
    private Integer shienId;

    /** 表示フラグ */
    private String dispFlg;

    /** ユーザーID */
    private Integer userId;

    /** 種類限度超過 */
    private String stensuOv;

    /** 種類限度内 */
    private String stensu;

    /** 区分限度超過 */
    private String ktensuOv;

    /** 区分限度内 */
    private String ktensu;

    /** エラー内容 */
    private String errKnj;

    /** エラーCD */
    private Integer errCode;

    /** 保険者名 */
    private String khokenKnj;

    /** 被保険者番号 */
    private String hhokenNo;

    /** 変更日 */
    private String henkouYmd;
}
