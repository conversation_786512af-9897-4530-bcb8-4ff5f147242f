package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jp.ndsoft.carebase.cmn.api.report.dto.DailyRoutinePlanReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.KyotakuServiceReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.Plan1kReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.Plan1sReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ServiceUseAnnexedTableReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ShisetsuServiceReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ShuukanServiceR34ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ShuukanServiceReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.TeikyohyoReportServiceInDto;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * 「U00891_計画書一括印刷」用の帳票データ取得の出力DTO.
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class PlanAllPrintReportDataSelectOutDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** U0081K_居宅サービス計画書（１） */
    private Plan1kReportServiceInDto data1;

    /** U0081S_施設サービス計画書（１） */
    private Plan1sReportServiceInDto data2;

    /** U0082K_居宅サービス計画書（２） */
    private ShisetsuServiceReportServiceInDto data3;

    /** U0082S_施設サービス計画書（２） */
    private KyotakuServiceReportServiceInDto data4;

    /** U00852_週間サービス計画表（R34改訂） */
    private ShuukanServiceR34ReportServiceInDto data5;

    /** U00861_日課計画表 */
    private DailyRoutinePlanReportServiceInDto data6;

    /** V00241_サービス利用票 */
    private TeikyohyoReportServiceInDto data7;

    /** V00231_サービス提供票別表 */
    private List<ServiceUseAnnexedTableReportServiceInDto> data8;

    /** U00852_週間サービス計画表 */
    private ShuukanServiceReportServiceInDto data9;
}
