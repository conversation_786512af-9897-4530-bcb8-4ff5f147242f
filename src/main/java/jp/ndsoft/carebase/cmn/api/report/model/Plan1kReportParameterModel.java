package jp.ndsoft.carebase.cmn.api.report.model;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Plan1kReportServiceDbNoSaveData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServicePrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServicePrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.service.dto.Plan1InitMasterData;
import jp.ndsoft.smh.framework.global.report.dto.FixedReportInDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * U0081K_居宅サービス計画書（１）
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Plan1kReportParameterModel extends FixedReportInDto {
    /** UID. */
    private static final long serialVersionUID = 1L;

    /** 事業所名 */
    private String jigyoKnj;

    /** システム日付（アプリ用） */
    private String appYmd;

    /** 初期設定マスタの情報 */
    private Plan1InitMasterData initMasterObj;

    /** 印刷設定 */
    private PrintReportServicePrintSet printSet;

    /** DB未保存画面項目 */
    private Plan1kReportServiceDbNoSaveData dbNoSaveData;

    /** 印刷対象履歴 */
    private PrintReportServicePrintSubjectHistory printSubjectHistory;
}