package jp.ndsoft.carebase.cmn.api.service;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00905IssuesPlanningStyleTitleMasterSelectData;
import jp.ndsoft.carebase.cmn.api.service.dto.IssuesPlanningStyleTitleSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.IssuesPlanningStyleTitleSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFreeTekiyoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFreeTekiyoInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFreeTekiyoInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFreeTekiyoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghMstKrkKraTitleByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghMstKrkKraTitleOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucFreeassKra1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucFreeassKra1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFree1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFreeTekiyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucFreeassKra1SelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import jp.ndsoft.smh.framework.services.login.LoginServiceImpl;

/**
 * @since 2025.05.13
 * <AUTHOR> 盧青陽
 * @implNote GUI00905_課題立案様式タイトルマスタ画面の初期情報を取得する。
 */

@Service
public class IssuesPlanningStyleTitleSelectServiceImpl extends
        SelectServiceImpl<IssuesPlanningStyleTitleSelectServiceInDto, IssuesPlanningStyleTitleSelectServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(LoginServiceImpl.class);

    /**
     * ケアの提供場所マスタのSQL実行.
     */
    @Autowired
    private KghMocKrkFree1SelectMapper kghMocKrkFree1SelectMapper;
    /**
     * ケアの提供場所マスタのSQL実行.
     */
    @Autowired
    private KghMocKrkFreeTekiyoSelectMapper kghMocKrkFreeTekiyoSelectMapper;
    /**
     * ケアの提供場所マスタのSQL実行.
     */
    @Autowired
    private KghTucFreeassKra1SelectMapper kghTucFreeassKra1SelectMapper;

    /**
     * 画面表示情報取得
     * 
     * @param inDto 画面表示情報取得の入力DTO.
     * @throws Exception Exception
     */
    @Override
    protected IssuesPlanningStyleTitleSelectServiceOutDto mainProcess(
            IssuesPlanningStyleTitleSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        // GUI00905_課題立案様式タイトルマスタ画面の入力DTO
        IssuesPlanningStyleTitleSelectServiceOutDto outDto = new IssuesPlanningStyleTitleSelectServiceOutDto();

        // 課題立案様式タイトルマスタリストリスト
        List<Gui00905IssuesPlanningStyleTitleMasterSelectData> gui00905IssuesPlanningStyleTitleList = new ArrayList<Gui00905IssuesPlanningStyleTitleMasterSelectData>();

        /*
         * =============== 2. 「課題立案様式タイトルマスタ」画面情報を取得する。===============
         * 
         */
        // 2.1 下記の情報取得のDAOを利用し
        KghMstKrkKraTitleByCriteriaInEntity kghMstKrkKraTitleByCriteriaInEntity = new KghMstKrkKraTitleByCriteriaInEntity();
        // 様式区分(リクエストパラメータ.様式区分)
        kghMstKrkKraTitleByCriteriaInEntity.setAiYoushikiKbn(CommonDtoUtil.strValToInt(inDto.getYoushikiKbn()));
        // 機能区分(リクエストパラメータ.機能区分)
        kghMstKrkKraTitleByCriteriaInEntity.setAiKinouKbn(CommonDtoUtil.strValToInt(inDto.getKinouKbn()));

        // 「カスタマイズ帳票マスタヘッダ」情報を取得する。
        List<KghMstKrkKraTitleOutEntity> kghMstKrkKraTitleList = kghMocKrkFree1SelectMapper
                .findKghMstKrkKraTitleByCriteria(kghMstKrkKraTitleByCriteriaInEntity);

        // 2.2 上記処理に取得データの件数 > 0の場合、下記処理を行う。
        if (CollectionUtils.isNotEmpty(kghMstKrkKraTitleList) && kghMstKrkKraTitleList.size() > 0) {
            // 2.2.1 下記の情報取得のDAOを利用し、
            KghMocKrkFreeTekiyoInfoByCriteriaInEntity kghMocKrkFreeTekiyoInfoByCriteriaInEntity = new KghMocKrkFreeTekiyoInfoByCriteriaInEntity();
            // 事業所ID(リクエストパラメータ.事業所ID)
            kghMocKrkFreeTekiyoInfoByCriteriaInEntity.setIlSvJigyoId(inDto.getSvJigyoId());
            // 機能区分(リクエストパラメータ.機能区分)
            kghMocKrkFreeTekiyoInfoByCriteriaInEntity.setIiKinouKbn(inDto.getKinouKbn());
            // 様式区分(リクエストパラメータ.様式区分)
            kghMocKrkFreeTekiyoInfoByCriteriaInEntity.setIiYoshikiKbn(inDto.getYoushikiKbn());

            List<KghMocKrkFreeTekiyoInfoOutEntity> kghMocKrkFreeTekiyoInfoOutList = this.kghMocKrkFreeTekiyoSelectMapper
                    .findKghMocKrkFreeTekiyoInfoByCriteria(kghMocKrkFreeTekiyoInfoByCriteriaInEntity);

            KghMocKrkFreeTekiyoInfoOutEntity kghMocKrkFreeTekiyoInfoOutEntity = new KghMocKrkFreeTekiyoInfoOutEntity();
            // if (CollectionUtils.isNotEmpty(kghMocKrkFreeTekiyoInfoOutList)) {
            //     kghMocKrkFreeTekiyoInfoOutEntity = kghMocKrkFreeTekiyoInfoOutList.get(0);
            // }
            kghMocKrkFreeTekiyoInfoOutEntity.setFree1Id(6);

            // 2.2.2 処理2.1の取得データを繰り返して、処理2.2.2～2.2.5を実施する。
            // GUI00905_課題立案様式タイトルマスタ画面出力DTO
            for (KghMstKrkKraTitleOutEntity kMKKTOutEntity : kghMstKrkKraTitleList) {
                Gui00905IssuesPlanningStyleTitleMasterSelectData iPSTMast = new Gui00905IssuesPlanningStyleTitleMasterSelectData();
                // マスタヘッダID
                iPSTMast.setFree1Id(CommonDtoUtil.objValToString(kMKKTOutEntity.getFree1Id()));
                // 機能区分
                iPSTMast.setKinouKbn(CommonDtoUtil.objValToString(kMKKTOutEntity.getKinouKbn()));
                // 様式区分
                iPSTMast.setYoushikiKbn(CommonDtoUtil.objValToString(kMKKTOutEntity.getYoushikiKbn()));
                // 帳票タイトル
                iPSTMast.setTitleKnj(kMKKTOutEntity.getTitleKnj());
                // 行の分割数（上）
                iPSTMast.setColumnCount(CommonDtoUtil.objValToString(kMKKTOutEntity.getColumnCount()));
                // 表示順
                iPSTMast.setSort(CommonDtoUtil.objValToString(kMKKTOutEntity.getSort()));
                // 印刷文字サイズ
                iPSTMast.setFontSize(CommonDtoUtil.objValToString(kMKKTOutEntity.getFontSize()));
                // 用紙サイズ
                iPSTMast.setYoushiSize(CommonDtoUtil.objValToString(kMKKTOutEntity.getYoushiSize()));
                // 自由に使用
                iPSTMast.setFreeKbn(CommonDtoUtil.objValToString(kMKKTOutEntity.getFreeKbn()));
                // 切替区分
                iPSTMast.setKirikaeKbn(CommonDtoUtil.objValToString(kMKKTOutEntity.getKirikaeKbn()));
                // アップデートフラグ
                iPSTMast.setDefUpdateFlg(CommonDtoUtil.objValToString(kMKKTOutEntity.getDefUpdateFlg()));
                // フリーID
                iPSTMast.setDmyFree1Id(CommonDtoUtil.objValToString(kMKKTOutEntity.getDmyFree1Id()));
                // 初期表示の行の分割数（上）
                iPSTMast.setInitColumnCount(CommonDtoUtil.objValToString(kMKKTOutEntity.getColumnCount()));

                // 下記の情報取得のDAOを利用し
                KghTucFreeassKra1ByCriteriaInEntity kghTucFreeassKra1ByCriteriaInEntity = new KghTucFreeassKra1ByCriteriaInEntity();
                // マスタヘッダID(処理2.1で取得データ.マスタヘッダID)
                kghTucFreeassKra1ByCriteriaInEntity.setIlTitleId(kMKKTOutEntity.getFree1Id());

                // 「32-07 課題立案ヘッダ」情報を取得する。
                KghTucFreeassKra1OutEntity kghTucFreeassKra1OutEntity = this.kghTucFreeassKra1SelectMapper
                        .countKghTucFreeassKra1ByCriteria(kghTucFreeassKra1ByCriteriaInEntity);

                // 2.2.3 上記処理2.2.2の取得データ > 0の場合
                if (kghTucFreeassKra1OutEntity.getCNT() > 0) {
                    // 「1」を返却情報の使用フラグに設定して
                    iPSTMast.setUseFlg(CommonConstants.USE_FLG_USE);
                }
                // 、逆に、「0」を返却情報の使用フラグに設定する
                else {
                    iPSTMast.setUseFlg(CommonConstants.USE_FLG_NOT_USE);
                }

                // 2.2.4 処理2.1で取得データ.マスタヘッダID = 処理2.2.1で取得データ.マスタヘッダIDの場合
                if (iPSTMast.getFree1Id()
                        .equals(CommonDtoUtil.objValToString(kghMocKrkFreeTekiyoInfoOutEntity.getFree1Id()))) {
                    // ・「1」を返却情報の適用フラグに設定
                    iPSTMast.setTekiyouFlg(CommonConstants.TEKIYO_FLG_CHECK_ON);
                    // ・「0」を返却情報の他適用フラグに設定
                    iPSTMast.setOtherTekiyouFlg(CommonConstants.OTHER_TEKIYO_FLG_NOT_APPLICABLE);
                }
                // 2.2.5 処理2.1で取得データ.マスタヘッダID <> 処理2.2.1で取得データ.マスタヘッダIDの場合
                else {
                    // ******* 下記の情報取得のDAOを利用し、
                    KghMocKrkFreeTekiyoByCriteriaInEntity kghMocKrkFreeTekiyoByCriteriaInEntity = new KghMocKrkFreeTekiyoByCriteriaInEntity();
                    // マスタヘッダID(処理2.1で取得データ.マスタヘッダID)
                    kghMocKrkFreeTekiyoByCriteriaInEntity.setLlFree1Id(kMKKTOutEntity.getFree1Id());
                    // 機能区分(リクエストパラメータ.機能区分)
                    kghMocKrkFreeTekiyoByCriteriaInEntity
                            .setIiKinouKbn(CommonDtoUtil.strValToInt(inDto.getKinouKbn()));
                    // 「27-18 カスタマイズ帳票適用マスタ」情報のデータ件数を取得する。
                    KghMocKrkFreeTekiyoOutEntity kghMocKrkFreeTekiyoOutEntity = this.kghMocKrkFreeTekiyoSelectMapper
                            .countKghMocKrkFreeTekiyoByCriteria(kghMocKrkFreeTekiyoByCriteriaInEntity);
                    // ******* 処理*******の取得データ件数 > 0の場合
                    if (kghMocKrkFreeTekiyoOutEntity.getCNT() > 0) {
                        // ・「0」を返却情報の適用フラグに設定
                        iPSTMast.setTekiyouFlg(CommonConstants.TEKIYO_FLG_CHECK_OFF);
                        // ・「1」を返却情報の他適用フラグに設定
                        iPSTMast.setOtherTekiyouFlg(CommonConstants.OTHER_TEKIYO_FLG_APPLICABLE);
                    }
                    // ******* 上記以外の場合
                    else {
                        // ・「0」を返却情報の適用フラグに設定
                        iPSTMast.setTekiyouFlg(CommonConstants.TEKIYO_FLG_CHECK_OFF);
                        // ・「0」を返却情報の他適用フラグに設定
                        iPSTMast.setOtherTekiyouFlg(CommonConstants.OTHER_TEKIYO_FLG_NOT_APPLICABLE);
                    }
                }
                // 課題立案表データリストを追加
                gui00905IssuesPlanningStyleTitleList.add(iPSTMast);
            }
        }
        /*
         * ===============3. 上記処理で取得した結果レスポンスを返却する。===============
         * 
         */
        outDto.setIssuesPlanningStyleTitleMasterList(gui00905IssuesPlanningStyleTitleList);
        // ※返却する情報の編集要領は「レスポンスパラメータ詳細」を参照
        LOG.info(Constants.END);
        return outDto;
    };
}
