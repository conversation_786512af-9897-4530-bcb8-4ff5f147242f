package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01048Output;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI01048_有効期間外サービス検索_集計の出力Dto
 * 
 * <AUTHOR> 李晨昊
 */
@Getter
@Setter
public class ValidWithOutPeriodServiceSelectServiceOutDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** OUTPUTリスト */
    private List<Gui01048Output> outputLst;
}
