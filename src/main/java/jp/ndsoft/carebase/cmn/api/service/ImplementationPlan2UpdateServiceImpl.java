package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import io.micrometer.common.util.StringUtils;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.GyoumuComLogic;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComElectronicSaveInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComElectronicSaveInitMasterData;
import jp.ndsoft.carebase.cmn.api.service.dto.ImplementationPlan2ChokiData;
import jp.ndsoft.carebase.cmn.api.service.dto.ImplementationPlan2TankiData;
import jp.ndsoft.carebase.cmn.api.service.dto.ImplementationPlan2UpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.ImplementationPlan2UpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucSypKkak221Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucSypKkak222Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucSypKkak223Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghTucKrkKikanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucSypKkak221;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucSypKkak221Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucSypKkak222;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucSypKkak222Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucSypKkak223;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucSypKkak223Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTucKrkKikan;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * GUI00955_実施計画～② 実施計画～②情報更新サービス.
 *
 * <AUTHOR>
 */
@Service
public class ImplementationPlan2UpdateServiceImpl
        extends UpdateServiceImpl<ImplementationPlan2UpdateServiceInDto, ImplementationPlan2UpdateServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 操作区分 3:削除 */
    private static final String OPER_FLAG_3 = "3";
    /** [mnu3][3GK]実施計画～② */
    private static final String MENU3_KNJ = "[mnu3][3GK]実施計画～②";

    /** 採番 */
    // @Autowired
    // private Numbering numbering;
    /** 27-06 記録共通期間（kgh_tuc_krk_kikan） */
    @Autowired
    private KghTucKrkKikanMapper kghTucKrkKikanMapper;

    /** 28-38 実施計画書～②（cpn_tuc_syp_kkak22_1） */
    @Autowired
    private CpnTucSypKkak221Mapper cpnTucSypKkak221Mapper;

    /** 実施計画書～②（課題と目標）（cpn_tuc_syp_kkak22_2） */
    @Autowired
    private CpnTucSypKkak222Mapper cpnTucSypKkak222Mapper;

    /** 実施計画書～②（計画内容）（cpn_tuc_syp_kkak22_3） */
    @Autowired
    private CpnTucSypKkak223Mapper cpnTucSypKkak223Mapper;

    /** 業務共通用 */
    @Autowired
    private GyoumuComLogic gyoumuComLogic;

    /**
     * チェック
     * 
     * @param inDto GUI00955_実施計画～② 実施計画～②情報更新の入力DTO
     */
    @Override
    protected void checkProcess(final ImplementationPlan2UpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // 共通部品権限チェック
        gyoumuComLogic.checkCommon(inDto, MENU3_KNJ);
        LOG.info(Constants.END);
    }

    /**
     * 実施計画～②情報更新処理
     * 
     * @param inDto GUI00955_実施計画～② 実施計画～②情報更新の入力DTO
     * @return GUI00955_実施計画～② 実施計画～②情報更新の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected ImplementationPlan2UpdateServiceOutDto mainProcess(
            ImplementationPlan2UpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // DTOOUT情報
        ImplementationPlan2UpdateServiceOutDto outDto = new ImplementationPlan2UpdateServiceOutDto();

        // 3.削除処理の実施
        deleteInfo(inDto, outDto);

        // 4.登録更新処理の実施
        updateInfo(inDto, outDto);

        // 5. 電子保存
        gyoumuComLogic.print(getPrintInDto(inDto), MENU3_KNJ);

        // 6. 上記処理で取得した結果レスポンスを返却する。
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * パラメータを取得する。
     * 
     * @param inDto inデータ
     * @return パラメータ
     */
    private GyoumuComElectronicSaveInDto getPrintInDto(ImplementationPlan2UpdateServiceInDto inDto) {
        GyoumuComElectronicSaveInDto param = new GyoumuComElectronicSaveInDto();
        // ログイン職員ＩＤ
        param.setLoginShokuinId(inDto.getLoginShokuinId());
        // 操作区分
        param.setOperaFlg(inDto.getOperaFlg());
        // 施設ID
        param.setShisetuId(inDto.getShisetuId());
        // 計画期間ID
        param.setSc1Id(inDto.getSc1Id());
        // 利用者ID
        param.setUserId(inDto.getUserId());
        // 履歴ID
        param.setRirekiId(inDto.getRirekiId());
        // 事業所ID
        param.setSvJigyoId(inDto.getSvJigyoId());
        // 法人ID
        param.setHoujinId(inDto.getHoujinId());
        // 事業所CD
        param.setSvJigyoCd(inDto.getSvJigyoCd());
        // システム日付
        param.setSysYmd(inDto.getSysYmd());
        // サービス事業者名称
        param.setSvJigyoKnj(inDto.getSvJigyoKnj());
        // 初期設定マスタの情報
        GyoumuComElectronicSaveInitMasterData initMasterData = new GyoumuComElectronicSaveInitMasterData();
        if (inDto.getInitMasterObj() != null) {
            // パッケージプラン改訂フラグ
            initMasterData.setPkaiteiFlg(inDto.getInitMasterObj().getPkaiteiFlg());
            // ケアプラン方式
            initMasterData.setCpnFlg(inDto.getInitMasterObj().getCpnFlg());
            // 敬称オプション
            initMasterData.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
            // 敬称
            initMasterData.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
        }
        param.setInitMasterObj(initMasterData);
        return param;
    }

    /**
     * 削除処理
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @throws ExclusiveException Exception
     */
    private void deleteInfo(
            ImplementationPlan2UpdateServiceInDto inDto,
            ImplementationPlan2UpdateServiceOutDto outDto) throws ExclusiveException {

        // 操作区分
        String operaFlg = inDto.getOperaFlg();
        // リクエストパラメータ.操作区分が「3:削除」以外の場合、「3.削除処理の実施」処理終了
        if (!OPER_FLAG_3.equals(operaFlg)) {
            return;
        }

        // 保存用「実施計画②履歴」情報.更新区分がD:削除の場合、下記の削除処理を行う
        if (CommonDtoUtil.isDelete(inDto.getRirekiObj())) {
            final CpnTucSypKkak221Criteria criteria = new CpnTucSypKkak221Criteria();
            // BigInteger modifiedCnt = new
            // BigInteger(inDto.getRirekiObj().getModifiedCnt());
            criteria.createCriteria()
                    .andKkak22IdEqualTo(CommonDtoUtil.strValToInt(inDto.getRirekiObj().getRirekiId()));
            // .andModifiedCntEqualTo(modifiedCnt)
            // .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
            // final CpnTucSypKkak221 row = new CpnTucSypKkak221();
            // 削除（論理）時の共通カラム値設定処理
            // CommonDaoUtil.setDeleteCommonColumns(row, modifiedCnt);
            cpnTucSypKkak221Mapper.deleteByCriteria(criteria);
            // int updateCnt = cpnTucSypKkak221Mapper.updateByCriteriaSelective(row,
            // criteria);
            // if (updateCnt <= 0) {
            // throw new ExclusiveException();
            // }
        }
    }

    /**
     * 更新処理
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @throws ExclusiveException Exception
     */
    private void updateInfo(
            ImplementationPlan2UpdateServiceInDto inDto,
            ImplementationPlan2UpdateServiceOutDto outDto) throws Exception {

        // 計画対象期間の保存
        saveKikanInfo(inDto, outDto);

        // リクエストパラメータ.操作区分が「3:削除」の場合、「4.登録更新処理の実施」処理終了
        if (OPER_FLAG_3.equals(inDto.getOperaFlg())) {
            return;
        }

        // 履歴の保存
        saveRirekiInfo(inDto, outDto);

        // 長期と短期の保存
        saveLongShortInfo(inDto, outDto);
    }

    /**
     * 計画対象期間の保存
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @throws ExclusiveException Exception
     */
    private void saveKikanInfo(
            ImplementationPlan2UpdateServiceInDto inDto,
            ImplementationPlan2UpdateServiceOutDto outDto) throws Exception {
        // リクエストパラメータ.計画期間ＩＤ
        String sc1Id = inDto.getSc1Id();
        // リクエストパラメータ.計画対象期間IDが空白または0の場合、下記の新規登録処理を行う
        if (sc1Id.isBlank() || CommonConstants.STR_0.equals(sc1Id)) {

            final KghTucKrkKikan kghTucKrkKikan = new KghTucKrkKikan();
            // リクエストパラメータ.法人ＩＤ
            kghTucKrkKikan.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
            // リクエストパラメータ.施設ＩＤ
            kghTucKrkKikan.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            // リクエストパラメータ.事業所ＩＤ
            kghTucKrkKikan.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // リクエストパラメータ.利用者ＩＤ
            kghTucKrkKikan.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
            // リクエストパラメータ.種別ＩＤ
            kghTucKrkKikan.setSyubetsuId(CommonDtoUtil.strValToInt(inDto.getSyubetuId()));
            // リクエストパラメータ.保存用「計画対象期間」情報.開始日
            kghTucKrkKikan.setStartYmd(inDto.getKikanObj().getStartYmd());
            // リクエストパラメータ.保存用「計画対象期間」情報.終了日
            kghTucKrkKikan.setEndYmd(inDto.getKikanObj().getEndYmd());
            // 登録時の共通カラム値設定処理
            // CommonDaoUtil.setInsertCommonColumns(kghTucKrkKikan);
            // kghTucKrkKikan.setSc1Id(numbering.getNumber(AppUtil.getKeiyakushaId(),
            // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.KGH_TUC_KRK_KIKAN_SC1_ID.getIntValue()));

            this.kghTucKrkKikanMapper.insertSelectiveAndReturn(kghTucKrkKikan);
            // 最新の期間ID設定
            outDto.setSc1IdNew(String.valueOf(kghTucKrkKikan.getSc1Id()));
        }
    }

    /**
     * 履歴の保存
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @throws ExclusiveException Exception
     */
    private void saveRirekiInfo(
            ImplementationPlan2UpdateServiceInDto inDto,
            ImplementationPlan2UpdateServiceOutDto outDto) throws Exception {

        String sc1Id = inDto.getSc1Id();
        // リクエストパラメータ.計画対象期間IDが空白または0の場合
        if (sc1Id.isBlank() || CommonConstants.STR_0.equals(sc1Id)) {
            sc1Id = outDto.getSc1IdNew();
        }
        final CpnTucSypKkak221 cpnTucSypKkak221 = new CpnTucSypKkak221();
        // 作成日
        cpnTucSypKkak221.setCreateYmd(inDto.getRirekiObj().getCreateYmd());
        // 作成者
        cpnTucSypKkak221.setShokuId(CommonDtoUtil.strValToInt(inDto.getRirekiObj().getShokuId()));
        // ケースNo.
        cpnTucSypKkak221.setCaseNo(inDto.getRirekiObj().getCaseNo());
        // 改訂フラグ
        cpnTucSypKkak221.setKaiteiFlg(CommonDtoUtil.strValToInt(inDto.getRirekiObj().getKaiteiFlg()));
        // 処遇実施上の留意点
        cpnTucSypKkak221.setRyuiKnj(inDto.getRirekiObj().getRyuiKnj());
        // 障害加算フラグ
        cpnTucSypKkak221.setShogaiKasanFlg(CommonDtoUtil.strValToInt(inDto.getRirekiObj().getShogaiKasanFlg()));
        // 初回作成日
        cpnTucSypKkak221.setShokaiYmd(inDto.getRirekiObj().getShokaiYmd());
        // 保存用「実施計画②履歴」情報.更新区分がC:新規の場合、下記の新規登録処理を行う
        if (CommonDtoUtil.isCreate(inDto.getRirekiObj())) {
            // 計画期間ID
            cpnTucSypKkak221.setSc1Id(CommonDtoUtil.strValToInt(sc1Id));
            // 法人ID
            cpnTucSypKkak221.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
            // 施設ID
            cpnTucSypKkak221.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            // 事業者ID
            cpnTucSypKkak221.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 利用者ＩＤ
            cpnTucSypKkak221.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
            // 新規時の共通カラム値設定処理
            // CommonDaoUtil.setInsertCommonColumns(cpnTucSypKkak221);
            // cpnTucSypKkak221.setKkak22Id(numbering.getNumber(AppUtil.getKeiyakushaId(),
            // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.CPN_TUC_SYP_KKAK22_1_KKAK22_ID.getIntValue()));

            cpnTucSypKkak221Mapper.insertSelectiveAndReturn(cpnTucSypKkak221);
            outDto.setRirekiIdNew(String.valueOf(cpnTucSypKkak221.getKkak22Id()));
        } else if (CommonDtoUtil.isUpdate(inDto.getRirekiObj())) {
            // BigInteger modifiedCnt = new
            // BigInteger(inDto.getRirekiObj().getModifiedCnt());
            // 実施計画書～②ヘッダＩＤ
            cpnTucSypKkak221.setKkak22Id(CommonDtoUtil.strValToInt(inDto.getRirekiObj().getRirekiId()));
            // 更新時の共通カラム値設定処理
            // CommonDaoUtil.setUpdateCommonColumns(cpnTucSypKkak221, modifiedCnt);
            // 保存用「実施計画②履歴」情報.更新区分がU:更新の場合
            final CpnTucSypKkak221Criteria criteria = new CpnTucSypKkak221Criteria();
            criteria.createCriteria()
                    .andKkak22IdEqualTo(CommonDtoUtil.strValToInt(inDto.getRirekiObj().getRirekiId()));
            // .andModifiedCntEqualTo(modifiedCnt)
            // .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
            int count = cpnTucSypKkak221Mapper.updateByCriteriaSelective(cpnTucSypKkak221, criteria);
            if (count <= 0) {
                throw new ExclusiveException();
            }
        }
    }

    /**
     * 長期と短期の保存
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @throws ExclusiveException Exception
     */
    private void saveLongShortInfo(
            ImplementationPlan2UpdateServiceInDto inDto,
            ImplementationPlan2UpdateServiceOutDto outDto) throws Exception {
        String rirekiId = inDto.getRirekiId();
        // リクエストパラメータ.履歴ＩＤが空白または0の場合
        if (rirekiId.isBlank() || CommonConstants.STR_0.equals(rirekiId)) {
            rirekiId = outDto.getRirekiIdNew();
        }
        List<ImplementationPlan2ChokiData> longList = inDto.getChokiDataList().stream()
                .filter(x -> !CommonDtoUtil.isDelete(x)).collect(Collectors.toList());
        for (int i = 0; i < longList.size(); i++) {
            ImplementationPlan2ChokiData data = longList.get(i);
            // 保存用「長期」リスト.表示順
            data.setSeq(String.valueOf(i + 1));
        }
        List<ImplementationPlan2TankiData> shortList = inDto.getTankiDataList().stream()
                .filter(x -> !CommonDtoUtil.isDelete(x)).collect(Collectors.toList());
        for (int i = 0; i < shortList.size(); i++) {
            ImplementationPlan2TankiData data = shortList.get(i);
            // 保存用「長期」リスト.表示順
            data.setSeq(String.valueOf(i + 1));
        }
        for (ImplementationPlan2ChokiData data : inDto.getChokiDataList()) {
            final CpnTucSypKkak222 cpnTucSypKkak222 = new CpnTucSypKkak222();
            // リクエストパラメータ.保存用「長期」リスト.履歴ＩＤ
            cpnTucSypKkak222.setKkak22Id(CommonDtoUtil.strValToInt(rirekiId));
            // リクエストパラメータ.保存用「長期」リスト.課題番号
            cpnTucSypKkak222.setKadaiNo(CommonDtoUtil.strValToInt(data.getKadaiNo()));
            // リクエストパラメータ.保存用「長期」リスト.課題番号（表示用）
            cpnTucSypKkak222
                    .setKadaiBangou(StringUtils.isEmpty(
                            data.getKadaiBangou()) ? null : CommonDtoUtil.strValToInt(data.getKadaiBangou()));
            // リクエストパラメータ.保存用「長期」リスト.課題
            cpnTucSypKkak222.setKadaiKnj(data.getKadaiKnj());
            // リクエストパラメータ.保存用「長期」リスト.長期目標
            cpnTucSypKkak222.setChokiKnj(data.getChokiKnj());
            // リクエストパラメータ.保存用「長期」リスト.長期達成時期
            cpnTucSypKkak222.setChokiJikiKnj(data.getChokiJikiKnj());
            // リクエストパラメータ.保存用「長期」リスト.表示順
            cpnTucSypKkak222.setSeq(CommonDtoUtil.strValToInt(data.getSeq()));
            // 保存用「長期」リスト.更新区分がC:新規の場合、下記の新規登録処理を行う
            if (CommonDtoUtil.isCreate(data)) {
                // リクエストパラメータ.利用者ＩＤ
                cpnTucSypKkak222.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
                // 登録時の共通カラム値設定処理
                // CommonDaoUtil.setInsertCommonColumns(cpnTucSypKkak222);
                // cpnTucSypKkak222.setId(numbering.getNumber(AppUtil.getKeiyakushaId(),
                // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.CPN_TUC_SYP_KKAK22_2_ID.getIntValue()));

                cpnTucSypKkak222Mapper.insertSelective(cpnTucSypKkak222);
            } else if (CommonDtoUtil.isUpdate(data)) {
                // BigInteger modifiedCnt = new BigInteger(data.getModifiedCnt());
                // if (CommonDtoUtil.isUpdate(data)) {
                // // 更新時の共通カラム値設定処理
                // CommonDaoUtil.setUpdateCommonColumns(cpnTucSypKkak222, modifiedCnt);
                // } else {
                // // 削除（論理）時の共通カラム値設定処理
                // CommonDaoUtil.setDeleteCommonColumns(cpnTucSypKkak222, modifiedCnt);
                // }
                final CpnTucSypKkak222Criteria criteria = new CpnTucSypKkak222Criteria();
                criteria.createCriteria()
                        .andIdEqualTo(CommonDtoUtil.strValToInt(data.getId()));
                // .andModifiedCntEqualTo(modifiedCnt)
                // .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
                int count = cpnTucSypKkak222Mapper.updateByCriteriaSelective(cpnTucSypKkak222, criteria);
                if (count <= 0) {
                    throw new ExclusiveException();
                }
            } else if (CommonDtoUtil.isDelete(data)) {
                // 保存用「長期」リスト.更新区分がD:削除の場合、下記の削除処理を行う
                final CpnTucSypKkak222Criteria criteria = new CpnTucSypKkak222Criteria();
                criteria.createCriteria()
                        .andIdEqualTo(CommonDtoUtil.strValToInt(data.getId()));
                cpnTucSypKkak222Mapper.deleteByCriteria(criteria);
            }
        }
        for (ImplementationPlan2TankiData data : inDto.getTankiDataList()) {
            final CpnTucSypKkak223 cpnTucSypKkak223 = new CpnTucSypKkak223();
            // リクエストパラメータ.保存用「長期」リスト.履歴ＩＤ
            cpnTucSypKkak223.setKkak22Id(CommonDtoUtil.strValToInt(rirekiId));
            // リクエストパラメータ.保存用「短期」リスト.課題番号
            cpnTucSypKkak223.setKadaiNo(CommonDtoUtil.strValToInt(data.getKadaiNo()));
            // リリクエストパラメータ.保存用「短期」リスト.短期目標
            cpnTucSypKkak223.setTankiKnj(data.getTankiKnj());
            // リクエストパラメータ.保存用「短期」リスト.短期達成時期
            cpnTucSypKkak223.setTankiJikiKnj(data.getTankiJikiKnj());
            // リクエストパラメータ.保存用「短期」リスト.サービス内容
            cpnTucSypKkak223.setSvNaiyoKnj(data.getSvNaiyoKnj());
            // リクエストパラメータ.保存用「短期」リスト.サービス種別
            cpnTucSypKkak223.setSvShuKnj(data.getSvShuKnj());
            // リクエストパラメータ.保存用「短期」リスト.提供事業所名
            cpnTucSypKkak223.setJigyoNameKnj(data.getJigyoNameKnj());
            // リクエストパラメータ.保存用「短期」リスト.頻度及び期間
            cpnTucSypKkak223.setKikanKnj(data.getKikanKnj());
            // リクエストパラメータ.保存用「短期」リスト.表示順
            cpnTucSypKkak223.setSeq(CommonDtoUtil.strValToInt(data.getSeq()));
            // 保存用「短期」リスト.更新区分がC:新規の場合、下記の新規登録処理を行う
            if (CommonDtoUtil.isCreate(data)) {
                // リクエストパラメータ.利用者ＩＤ
                cpnTucSypKkak223.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
                // 登録時の共通カラム値設定処理
                // CommonDaoUtil.setInsertCommonColumns(cpnTucSypKkak223);
                // cpnTucSypKkak223.setId(numbering.getNumber(AppUtil.getKeiyakushaId(),
                // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.CPN_TUC_SYP_KKAK22_3_ID.getIntValue()));

                cpnTucSypKkak223Mapper.insertSelective(cpnTucSypKkak223);
            } else if (CommonDtoUtil.isUpdate(data)) {
                // BigInteger modifiedCnt = new BigInteger(data.getModifiedCnt());
                // if (CommonDtoUtil.isUpdate(data)) {
                // // 更新時の共通カラム値設定処理
                // CommonDaoUtil.setUpdateCommonColumns(cpnTucSypKkak223, modifiedCnt);
                // } else {
                // // 削除（論理）時の共通カラム値設定処理
                // CommonDaoUtil.setDeleteCommonColumns(cpnTucSypKkak223, modifiedCnt);
                // }
                final CpnTucSypKkak223Criteria criteria = new CpnTucSypKkak223Criteria();
                criteria.createCriteria()
                        .andIdEqualTo(CommonDtoUtil.strValToInt(data.getId()));
                // .andModifiedCntEqualTo(modifiedCnt)
                // .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
                int count = cpnTucSypKkak223Mapper.updateByCriteriaSelective(cpnTucSypKkak223, criteria);
                if (count <= 0) {
                    throw new ExclusiveException();
                }
            } else if (CommonDtoUtil.isDelete(data)) {
                // 保存用「短期」リスト.更新区分がD:削除の場合、下記の削除処理を行う
                final CpnTucSypKkak223Criteria criteria = new CpnTucSypKkak223Criteria();
                criteria.createCriteria()
                        .andIdEqualTo(CommonDtoUtil.strValToInt(data.getId()));
                cpnTucSypKkak223Mapper.deleteByCriteria(criteria);
            }
        }
    }
}
