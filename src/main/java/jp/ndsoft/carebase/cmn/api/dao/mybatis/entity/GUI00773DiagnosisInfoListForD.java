package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GUI00773_アセスメント(インターライ)画面I-2
 * 
 * @description
 *              削除の疾患（診断）情報リスト
 *              削除の疾患（診断）情報リストエンティティ
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GUI00773DiagnosisInfoListForD implements Serializable, IEntity {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** ID */
    @NotEmpty
    private String id;

}
