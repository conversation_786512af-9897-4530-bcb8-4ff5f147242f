package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.*;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentMasterSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentMasterSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.*;
import jp.ndsoft.carebase.common.dao.mysql.mapper.*;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @since 2025.03.04
 * <AUTHOR>
 * @apiNote GUI00626_アセスメントマスタ 初期情報取得
 */
@Service
public class AssessmentMasterSelectServiceImpl
        extends SelectServiceImpl<AssessmentMasterSelectServiceInDto, AssessmentMasterSelectServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    /** 初期設定マスタ情報取得DAO */
    @Autowired
    private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;

    /**
     * 「アセスメントマスタ」画面の初期情報を取得する
     * 
     * @param inDto 初期設定マスタ情報取込入力DTO.
     * @return 初期設定マスタ取込初期情報出力DTO
     */
    @Override
    protected AssessmentMasterSelectServiceOutDto mainProcess(AssessmentMasterSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);

        // 戻り情報を設定
        AssessmentMasterSelectServiceOutDto outDto = new AssessmentMasterSelectServiceOutDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. 初期設定情報の取得===============
         * 
         */
        // DAOパラメータを作成
        KrkSsmInfoByCriteriaInEntity krkSsmInfoByCriteriaInEntity = new KrkSsmInfoByCriteriaInEntity();
        krkSsmInfoByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        krkSsmInfoByCriteriaInEntity.setBunrui1Id(CommonDtoUtil.strValToInt(inDto.getBunrui1Id()));
        krkSsmInfoByCriteriaInEntity.setBunrui2Id(CommonDtoUtil.strValToInt(inDto.getBunrui2Id()));
        krkSsmInfoByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // DAOを実行
        List<KrkSsmInfoOutEntity> krkSsmInfoList = this.kghMocKrkSsmSelectMapper
                .findKrkSsmInfoByCriteria(krkSsmInfoByCriteriaInEntity);
        // 取得した初期設定情報を繰り返して、下記処理を行う
        krkSsmInfoList.forEach(item -> {
            // 初期設定情報.分類３ = 1のデータがある場合
            if (item.getBunrui3Id() == CommonConstants.BUNRUI3_ID_1) {
                // Visio
                outDto.setVisioValue(CommonDtoUtil.objValToString(item.getIntValue()));
                // Visio分類３
                outDto.setVisioBunrui3(CommonDtoUtil.objValToString(item.getBunrui3Id()));
                // Visio更新区分 = 1
                outDto.setVisioUpdateKbn(CommonConstants.UPDATE_KBN_1);
            } else if (item.getBunrui3Id() == CommonConstants.BUNRUI3_ID_2) {
                // 初期設定情報.分類３ = 2のデータがある場合
                // 障害等の部位の摘要表示
                outDto.setSyougaiValue(CommonDtoUtil.objValToString(item.getIntValue()));
                // 障害分類３
                outDto.setSyougaiBunrui3(CommonDtoUtil.objValToString(item.getBunrui3Id()));
                // 障害更新区分 = 1
                outDto.setSyougaiUpdateKbn(CommonConstants.UPDATE_KBN_1);
            } else if (item.getBunrui3Id() == CommonConstants.BUNRUI3_ID_3) {
                // 初期設定情報.分類３ = 3のデータがある場合
                // 全体のまとめ
                outDto.setZenntaiValue(CommonDtoUtil.objValToString(item.getIntValue()));
                // 全体分類３
                outDto.setZenntaiBunrui3(CommonDtoUtil.objValToString(item.getBunrui3Id()));
                // 全体更新区分 = 1
                outDto.setZenntaiUpdateKbn(CommonConstants.UPDATE_KBN_1);
            }
        });
        // 初期設定情報.分類３ = 1のデータがない場合
        if (StringUtils.isBlank(outDto.getVisioBunrui3())) {
            // Visio = 0
            outDto.setVisioValue(CommonConstants.VISIO_MAKE_INVALID);
            // Visio分類3 = 1
            outDto.setVisioBunrui3(CommonDtoUtil.objValToString(CommonConstants.BUNRUI3_ID_1));
            // Visio更新区分 = 0
            outDto.setVisioUpdateKbn(CommonConstants.UPDATE_KBN_0);
        }
        // 初期設定情報.分類３ = 2のデータがない場合
        if (StringUtils.isBlank(outDto.getSyougaiBunrui3())) {
            // 障害等の部位の摘要表示 = 0
            outDto.setSyougaiValue(CommonConstants.HANDYCAP_DISPLAY_NOT_MAKE);
            // 障害分類3 = 2
            outDto.setSyougaiBunrui3(CommonDtoUtil.objValToString(CommonConstants.BUNRUI3_ID_2));
            // 障害更新区分 = 0
            outDto.setSyougaiUpdateKbn(CommonConstants.UPDATE_KBN_0);
        }
        // 初期設定情報.分類３ = 3のデータがない場合
        if (StringUtils.isBlank(outDto.getZenntaiBunrui3())) {
            // 全体のまとめ = 1
            outDto.setZenntaiValue(CommonConstants.SUMMARY_FREE_INPUT);
            // 全体分類3 = 3
            outDto.setZenntaiBunrui3(CommonDtoUtil.objValToString(CommonConstants.BUNRUI3_ID_3));
            // 全体更新区分 = 0
            outDto.setZenntaiUpdateKbn(CommonConstants.UPDATE_KBN_0);
        }

        /**
         * ===============3. 上記処理で取得した結果レスポンスを返却する。===============
         */
        LOG.info(Constants.END);

        return outDto;
    }
}
