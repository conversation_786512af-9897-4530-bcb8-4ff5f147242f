package jp.ndsoft.carebase.cmn.api.logic.dto;

import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Gui01169Meisai extends IDtoImpl {
    /** 明細リスト */
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;
    /** 支援事業者ID */
    private String shienId;
    /** 利用者ID */
    private String userid;
    /** サービス提供年月 */
    private String yymmYm;
    /** サービス提供年月（変更日） */
    private String yymmD;
    /** サービス事業者ID */
    private String svJigyoId;
    /** サービス項目ID */
    private String svItemCd;
    /** 枝番 */
    private String edaNo;
    /** サービス単位数 */
    private String tensu;
    /** 回数 */
    private String kaisu;
    /** 合計単位数 */
    private String svTensu;
    /** 種類限度外単位数 */
    private String sTensuOver;
    /** 種類限度額内単位数 */
    private String sTensu;
    /** 区分支給限度外単位数 */
    private String kTensuOver;
    /** 区分支給限度内単位数 */
    private String kTensu;
    /** 単位数単価 */
    private String tanka;
    /** 利用者負担額・保険給付対象分 */
    private String hutanH;
    /** 利用者負担額・全額自己負担分 */
    private String hutanJ;
    /** サービス種類CD */
    private String svtype;
    /** サービス項目CD */
    private String svcode;
    /** 合計フラグ */
    private String totalF;
    /** 費用総額 */
    private String hiyouSougaku;
    /** 給付率 */
    private String kyufuRitu;
    /** 介護保険給付額 */
    private String hKyufugaku;
    /** 加算フラグ */
    private String kasanF;
    /** サービスコード */
    private String scode;
    /** 割引率 */
    private String waribikiRitu;
    /** 割引後単位数 */
    private String waribikiTen;
    /** ３０ 日超過フラグ */
    private String ov30Fl;
    /** 定額利用者負担単価金額 */
    private String tHutanTanka;
    /** 給付率差異フラグ */
    private String kyufuDiffF;
    /** サービス内容種類 */
    private String dmyFormalnameKnj;
    /** 事業所番号 */
    private String dmyJigyoNumber;
    /** 計算表示順 */
    private String dmyCalcSort;
    /** 事業所名 */
    private String jigyonameRyaku;
    /** サービス種類CD・ソート用 */
    private String dmySvtype;
    /** レンタルフラグ */
    private String isRentalFlag;
    /** 総合区分 */
    private String cSougouKbn;
    /** 単位数単価表示フラグ */
    private String tankaDisp;
    /** 給付率表示フラグ */
    private String kyufuRituDisp;
    /** 計算・合計単位数 */
    private String cSvTensu;
    /** 計算・種類限度外単位数 */
    private String cSTensuOver;
    /** 計算・種類限度額内単位数 */
    private String cSTensu;
    /** 計算・区分支給限度外単位数 */
    private String cKTensuOver;
    /** 計算・区分支給限度内単位数 */
    private String cKTensu;
    /** 計算・費用総額 */
    private String cHiyouSougaku;
    /** 計算・介護保険給付額 */
    private String cHKyufugaku;
    /** 計算・利用者負担額・保険給付対象分 */
    private String cHutanH;
    /** 福祉用具貸与マスタID */
    private String fygId;
    /** 用具名称 */
    private String fygShouhinKnj;
    /** TAISコード */
    private String fygTekiyouCode;
    /** 更新区分（C:新規、U:更新、D:削除） */
    private String updateKbn;
}
