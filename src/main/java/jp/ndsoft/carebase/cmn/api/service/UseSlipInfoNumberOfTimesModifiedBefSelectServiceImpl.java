package jp.ndsoft.carebase.cmn.api.service;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.KghCmn03gFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnF01Logic;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Riyou;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Riyou704Info;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Riyou898Info;
import jp.ndsoft.carebase.cmn.api.service.dto.UseSlipInfoNumberOfTimesModifiedBefSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.UseSlipInfoNumberOfTimesModifiedBefSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * GUI01149_利用票 APINo(898)_利用票画面回数ダブルクリック変更前のチェック情報を取得
 * 
 * <AUTHOR>
 */
@Service
public class UseSlipInfoNumberOfTimesModifiedBefSelectServiceImpl extends
        SelectServiceImpl<UseSlipInfoNumberOfTimesModifiedBefSelectServiceInDto, UseSlipInfoNumberOfTimesModifiedBefSelectServiceOutDto> {

    /** サービス種類から総合事業かどうかをチェックする */
    @Autowired
    private KghCmnF01Logic kghCmnF01Logic;

    /** KghCmn03gFunc01Logicロジッククラス */
    @Autowired
    private KghCmn03gFunc01Logic kghCmn03gFunc01Logic;

    /** 利用票画面情報週間取込後処理 */
    @Autowired
    private UseSlipInitInfoSelectServiceImpl useSlipInitInfoSelectServiceImpl;

    /**
     * 利用票画面回数ダブルクリック変更前のチェック情報取得
     * 
     * @param inDto 変更前のチェック情報の入力DTO.
     * @return 変更前のチェック情報OUT DTO
     * @throws Exception Exception
     */
    @Override
    protected UseSlipInfoNumberOfTimesModifiedBefSelectServiceOutDto mainProcess(
            UseSlipInfoNumberOfTimesModifiedBefSelectServiceInDto inDto) throws Exception {
        UseSlipInfoNumberOfTimesModifiedBefSelectServiceOutDto outDto = new UseSlipInfoNumberOfTimesModifiedBefSelectServiceOutDto();
        // 2.利用票・別表の回数ダブルクリック変更前のチェック情報を取得
        Gui01149Riyou898Info gui01149Riyou898Info = new Gui01149Riyou898Info();

        // レスポンスパラメータ.利用票明細情報 = リクエストパラメータ.利用票明細情報
        List<Gui01149Riyou> newRiyouList = new ArrayList<>();
        newRiyouList.addAll(inDto.getRiyouList());
        gui01149Riyou898Info.setRiyouList(newRiyouList);
        // 2.2.サービス種類・scode・合成識別区分を元に、加算かどうかのチェックをおこなう

        int index = CommonDtoUtil.strValToInt(inDto.getLineNo());
        Gui01149Riyou inDataRiyou = inDto.getRiyouList().get(index);
        boolean kasanServiceFlag = kghCmnF01Logic.chkKasanService2(inDataRiyou.getSvtype(),
                inDataRiyou.getScode().substring(0, Math.min(6, inDataRiyou.getScode().length())),
                inDataRiyou.getGouseiSikKbn());
        gui01149Riyou898Info.setKasanService(CommonDtoUtil.objValToString(kasanServiceFlag));
        // 2.3.リクエストパラメータ.サービス種別コード = "14" or "64"の場合、提供時間（開始～終了）から、訪問リハビリの回数を取得する
        if (CommonConstants.SV_TYPE_14.equals(inDataRiyou.getSvtype())
                || CommonConstants.SV_TYPE_64.equals(inDataRiyou.getSvtype())) {
            Integer kaisuCnt = kghCmn03gFunc01Logic.getKaisuFor1464(inDataRiyou.getGouseiSikKbn(),
                    inDataRiyou.getScode(), inDataRiyou.getSvStartTime(), inDataRiyou.getSvEndTime());
            gui01149Riyou898Info.setKaisu(CommonDtoUtil.objValToString(kaisuCnt));
        }
        for (Gui01149Riyou item : inDto.getRiyouList()) {
            // 2.4.レスポンスパラメータ.加算サービスフラグ = falseの場合、親レコードに予実が入力されたら子レコードにも予実をセットする
            if (!kasanServiceFlag) {
                // 【変数】.加算サービス
                boolean vaKasanServiceFlag = kghCmnF01Logic.chkKasanService2(item.getSvtype(),
                        item.getScode().substring(0, Math.min(6, item.getScode().length())), item.getGouseiSikKbn());
                // 【変数】.加算サービス = trueの場合
                if (vaKasanServiceFlag) {
                    // YearMonth yearMonth = YearMonth.parse(inDto.getTeikyouYm());
                    // String strTeikyouYm =
                    // yearMonth.format(DateTimeFormatter.ofPattern("yyyy/MM")).toString();
                    String strTeikyouYm = inDto.getTeikyouYm();
                    // ％の加算処理
                    long kasan = kghCmnF01Logic.chkIsAdderPercent(
                            item.getScode().substring(0, Math.min(6, item.getScode().length())),
                            strTeikyouYm.concat(CommonConstants.STRING_FIRST_DAY));
                    // 共生型サービスかどうかを判定する
                    Integer result = kghCmnF01Logic.getKyoseiGenzan(
                            strTeikyouYm.concat(CommonConstants.STRING_FIRST_DAY),
                            item.getScode().substring(0, Math.min(6, item.getScode().length())));
                    // 【変数】.加算 > 0 and 【変数】.共生型判定 = 0 の場合
                    if (kasan > 0 && result == 0) {
                        // 次のレコードを行う
                        continue;
                    }
                    Method method = item.getClass()
                            .getMethod(("set".concat(StringUtils.capitalize(inDto.getProItem()))), String.class);
                    if (CommonConstants.SV_TYPE_14.equals(item.getSvtype())
                            || CommonConstants.SV_TYPE_64.equals(item.getSvtype())) {
                        // 該当行.サービスコード(先頭６桁) = "146101" or "646101" or "145010" or "645010"の場合
                        if (Arrays.asList(CommonConstants.SC_CODE_FIRST_SIX_DIGITS_STRINGS)
                                .contains(item.getScode().substring(0, Math.min(6, item.getScode().length())))) {
                            method.invoke(item, inDto.getProValue());
                        } else {
                            // リクエストパラメータ.選択の項目の値 > 0 の場合
                            if (CommonDtoUtil.strValToInt(inDto.getProValue()) > BigDecimal.ZERO.intValue()) {
                                method.invoke(item, CommonDtoUtil.objValToString(BigDecimal.ONE));
                            } else {
                                method.invoke(item, CommonDtoUtil.objValToString(BigDecimal.ZERO));
                            }
                        }
                    } else {
                        method.invoke(item, inDto.getProValue());
                    }
                }
            }
            gui01149Riyou898Info.getRiyouList().add(item);
        }

        // 2.5.選択の行.サービス種別コードが以下のいずれか場合
        if (Arrays.asList(CommonConstants.SV_TYPE_ONE_STRINGS).contains(inDataRiyou.getSvtype())) {
            // 2.5.1.ヘッダの色を変更する
            Gui01149Riyou704Info gui01149Riyou704Info = useSlipInitInfoSelectServiceImpl
                    .headerColorChangeProcess(inDto.getRiyouProcessObject(), gui01149Riyou898Info.getRiyouList());
            gui01149Riyou898Info.setRiyouList(gui01149Riyou704Info.getRiyouList());
        }

        // 2.6. 選択の行.サービス種別コードが以下のいずれか場合
        if (Arrays.asList(CommonConstants.SV_TYPE_TWO_STRINGS).contains(inDataRiyou.getSvtype())) {
            // 2.6.1.リクエストパラメータ.選択の項目 > 0 の場合
            if (CommonDtoUtil.strValToInt(inDto.getProValue()) > 0) {
                Integer dateOfEntryAndExitFlag = useSlipInitInfoSelectServiceImpl.distinguishDateOfEntryAndExit(
                        inDto.getProItem().substring(inDto.getProItem().length() - 2),
                        inDto.getRiyouProcessObject().get(index).getShortRiyou());
                // 変数.入退所日の判別 = 4の場合
                if (dateOfEntryAndExitFlag != null && dateOfEntryAndExitFlag == 4) {
                    gui01149Riyou898Info.setMessageFlag(CommonConstants.MESSAGE_FLAG_ONE_STR);
                }
            }
        }
        List<Gui01149Riyou898Info> riyou898Info = new ArrayList<>();
        riyou898Info.add(gui01149Riyou898Info);
        outDto.setRiyou898Info(riyou898Info);

        return outDto;
    }
}
