package jp.ndsoft.carebase.cmn.api.service.dto;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.*;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

/**
 * GUI01014_計画書（2）複写情報取得の出力Dto
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class CarePlan2DuplicateSelectServiceOutDto
        extends IDtoImpl {
    /**
     * serialVersionUID.
     */
    private static final long serialVersionUID = 1L;

    /**
     * 計画書（２）リスト
     */
    private List<Gui01014Keikasyo2> keikasyo2List = Collections.emptyList();

    /**
     * 保険サービスリスト
     */
    private List<Gui01014Hoken> hokenList = Collections.emptyList();

    /**
     * 月日指定リスト
     */
    private List<Gui01014Tukihi> tukihiList = Collections.emptyList();

}
