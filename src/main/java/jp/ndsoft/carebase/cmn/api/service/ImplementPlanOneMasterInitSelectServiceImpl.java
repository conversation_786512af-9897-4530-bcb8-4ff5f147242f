package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.SvJigyoInfoLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.ImplementPlanOneMasterInitSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.ImplementPlanOneMasterInitSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * GUI00945_実施計画①マスタ 初期情報取得
 *
 * <AUTHOR>
 */
@Service
public class ImplementPlanOneMasterInitSelectServiceImpl
        extends
        SelectServiceImpl<ImplementPlanOneMasterInitSelectServiceInDto, ImplementPlanOneMasterInitSelectServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // 初期設定マスタ情報取得
    @Autowired
    private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;
    // 事業所名取得
    @Autowired
    private SvJigyoInfoLogic svJigyoInfoLogic;

    /**
     * 初期情報を取得する。
     * 
     * @param inDto 初期情報取得サービス入力Dto
     * @return 初期情報取得の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected ImplementPlanOneMasterInitSelectServiceOutDto mainProcess(
            ImplementPlanOneMasterInitSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // DTOOUT情報
        ImplementPlanOneMasterInitSelectServiceOutDto outDto = new ImplementPlanOneMasterInitSelectServiceOutDto();
        // 単項目チェック以外の入力チェック 特になし

        // 初期設定マスタ情報の取得
        KrkSsmInfoByCriteriaInEntity entity = new KrkSsmInfoByCriteriaInEntity();
        // // 施設ID
        entity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // // 事業者ID
        entity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 分類1
        entity.setBunrui1Id(CommonConstants.BUNRUI1_ID_2);
        // 分類2
        entity.setBunrui2Id(CommonConstants.BUNRUI2_ID_14);
        List<KrkSsmInfoOutEntity> list = kghMocKrkSsmSelectMapper.findKrkSsmInfoByCriteria(entity);
        // 下記レスポンス項目に空文字を初期化する。
        // 期間のカレンダー取込
        outDto.setPeriod(CommonConstants.VAL_PKKAK21_YMD_FLG);
        // 期間のカレンダー取込分類3
        outDto.setPeriodBunrui3(CommonConstants.BLANK_STRING);
        // 内容が空白の場合省略
        outDto.setContentsBlank(CommonConstants.VAL_SSM_PKKAK21_KEISEN1_FLG);
        // 内容が空白の場合省略分類3
        outDto.setContentsBlankBunrui3(CommonConstants.BLANK_STRING);
        // 内容が同じ場合省略
        outDto.setContentsSame(CommonConstants.VAL_SSM_PKKAK21_KEISEN2_FLG);
        // 内容が同じ場合省略分類3
        outDto.setContentsSameBunrui3(CommonConstants.BLANK_STRING);

        // 取得した初期設定マスタ情報中に、下記処理を行う
        for (KrkSsmInfoOutEntity data : list) {
            if (CommonConstants.BUNRUI3_ID_3.equals(data.getBunrui3Id())) {
                // 期間のカレンダー取込
                outDto.setPeriodBunrui3(CommonDtoUtil.objValToString(CommonConstants.BUNRUI3_ID_3));
                outDto.setPeriod(CommonDtoUtil.objValToString(data.getIntValue()));
                // outDto.setPeriodModifiedCnt(CommonDtoUtil.objValToString(data.getModifiedCnt()));
                outDto.setPeriodModifiedCnt(CommonConstants.STR_0);
            } else if (CommonConstants.BUNRUI3_ID_4.equals(data.getBunrui3Id())) {
                // 内容が空白の場合省略
                outDto.setContentsBlankBunrui3(CommonDtoUtil.objValToString(CommonConstants.BUNRUI3_ID_4));
                outDto.setContentsBlank(CommonDtoUtil.objValToString(data.getIntValue()));
                // outDto.setContentsBlankModifiedCnt(CommonDtoUtil.objValToString(data.getModifiedCnt()));
                outDto.setContentsBlankModifiedCnt(CommonConstants.STR_0);
            } else if (CommonConstants.BUNRUI3_ID_5.equals(data.getBunrui3Id())) {
                // 内容が同じ場合省略
                outDto.setContentsSameBunrui3(CommonDtoUtil.objValToString(CommonConstants.BUNRUI3_ID_5));
                outDto.setContentsSame(CommonDtoUtil.objValToString(data.getIntValue()));
                // outDto.setContentsSameModifiedCnt(CommonDtoUtil.objValToString(data.getModifiedCnt()));
                outDto.setContentsSameModifiedCnt(CommonConstants.STR_0);
            }
        }
        // リクエストパラメータ.適用事業所IDリストの事業所名取得
        outDto.setSvJigyoListInfo(svJigyoInfoLogic.getSvJigyoInfoList(inDto.getSvJigyoIdList()));

        LOG.info(Constants.END);
        return outDto;
    }
}
