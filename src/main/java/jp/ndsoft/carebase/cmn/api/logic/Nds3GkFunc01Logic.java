package jp.ndsoft.carebase.cmn.api.logic;

import java.lang.reflect.InvocationTargetException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import io.grpc.netty.shaded.io.netty.util.internal.StringUtil;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.dto.AgeYmOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.ChkDateOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.DateDiffOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkSetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GengouInfoExpandOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GetProfile2InDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.HolidayArrayOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.SepaOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.ComMocGengouMapper;
import jp.ndsoft.carebase.common.dao.mybatis.ComMocSysCIniMapper;
import jp.ndsoft.carebase.common.dao.mybatis.ComMocSysiniSMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocGengou;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocGengouCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocSysCIni;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocSysCIniCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocSysiniS;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocSysiniSCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMscHoliday1monByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMscHoliday1monOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucJigyoRest1monByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucJigyoRest1monOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.DefParamKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.DefParamKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyoKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyoKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyoshoMeishoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyoshoMeishoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ParamKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ParamKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SysCIniByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SysCIniOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSetteiShokuDefSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSysCIniSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscHolidaySelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoRirekiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucJigyoRestSelectMapper;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * Nds3GkFunc01Logicロジッククラス
 * 
 * <AUTHOR>
 */
@Component
public class Nds3GkFunc01Logic {
    /** 4 */
    private static final Integer INTEGER_4 = 4;
    /** 100 */
    private static final Integer INTEGER_100 = 100;
    /** 400 */
    private static final Integer INTEGER_400 = 400;
    /** 月の最大日数配列（非閏年） */
    private static final int[] MONTH_DAYS = { 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31 };
    /** 閏年2月の日数 */
    private static final int LEAP_FEBRUARY_DAYS = 29;
    /** 通常2月の日数 */
    private static final int NORMAL_FEBRUARY_DAYS = 28;
    /** 日付フォーマット用ゼロパディング書式 */
    private static final String YEAR_FORMAT = "%04d";
    /** 月のフォーマット用ゼロパディング書式 */
    private static final String MONTH_DAY_FORMAT = "%02d";
    /** 最小年（1868年） */
    private static final Integer MIN_YEAR = 1868;
    /** 最小月（7月） */
    private static final Integer MIN_MONTH = 7;
    /** 最小日（8日） */
    private static final Integer MIN_DAY = 8;
    /** 年: 1868 */
    private static final Integer YEAR_2087 = 2087;
    /** 年 + 98 */
    private static final Integer YEAR_ADD_98 = 98;
    /** 小文字'a'のASCIIコード値（定数） */
    private static final Integer LOWER_CASE_A_ASCII = 97;
    /** 小文字'z'のASCIIコード値（定数） */
    private static final Integer LOWER_CASE_Z_ASCII = 122;
    /** 大文字と小文字のASCIIコードの差（定数） */
    private static final Integer CASE_CONVERSION_DIFFERENCE = 32;
    /** 和暦文字列許可文字（大文字） */
    private static final char[] VALID_GENGOU = { 'M', 'T', 'S', 'H' };
    /** クエリーのソート */
    private static final String COM_MOC_GENGOU_SORT = "yyyy ASC, mm ASC, dd ASC";
    /** -1 */
    private static final Integer INVALID_SEPA_CNT = -1;
    /** 元号アルファベット略称（H, S, T, M） */
    private static final String[] GG_G = { "H", "S", "T", "M" };
    /** 元号開始年（西暦） */
    private static final int[] START_YEARS = { 1989, 1926, 1912, 1868 };
    /** 画面名: 共通 */
    private static final String COMMON = "共通";
    /** セクション名: 表示設定 */
    private static final String DISPLAY_SETTING = "表示設定";
    /** キー 名: ENTER_FLG */
    private static final String ENTER_FLG = "ENTER_FLG";
    /** キー 名: SCROLL */
    private static final String SCROLL = "SCROLL";
    /** キー 名: メニュー */
    private static final String MENU = "メニュー";
    /** キー 名: メニュー前回最後に起動していた画面 */
    private static final String LAST_MENU = "メニュー前回最後に起動していた画面";
    /** セクション名: 利用者リスト */
    private static final String USER_LIST = "利用者リスト";
    /** キー 名: 機能間の表示設定 */
    private static final String INTER_FUNCTION_DISPLAY = "機能間の表示設定";
    /** 画面名: スケジュール */
    private static final String SCHEDULE = "スケジュール";
    /** セクション名: スケジュール */
    private static final String SDL_DEFAULT = "SDL_DEFAULT";
    /** キー 名: WEEKSTART */
    private static final String WEEKSTART = "WEEKSTART";
    /** キー 名: SGF_RENDOU */
    private static final String SGF_RENDOU = "SGF_RENDOU";
    /** キー 名: TUT_SANTEI */
    private static final String TUT_SANTEI = "TUT_SANTEI";
    /** キー 名: TUT_RENRAKU */
    private static final String TUT_RENRAKU = "TUT_RENRAKU";
    /** キー 名: HKN_JYUUFUKU */
    private static final String HKN_JYUUFUKU = "HKN_JYUUFUKU";
    /** 画面名: KDS */
    private static final String KDS = "KDS";
    /** セクション名: DENSO_SYSTEM */
    private static final String DENSO_SYSTEM = "DENSO_SYSTEM";
    /** キー: PATH */
    private static final String PATH = "PATH";
    /** キー: HKN_SHOKUIN_CNT */
    private static final String HKN_SHOKUIN_CNT = "HKN_SHOKUIN_CNT";
    /** システムコード */
    private static final String GSYSCD = "71101";
    /** パラメータ 1:正常 */
    private static final Integer PARAM_NORMAL = 1;
    /** パラメータ -1:不正 */
    private static final Integer PARAM_UNNORMAL = -1;
    /** システム略：NDS */
    private static final String SYS_NDS = "NDS";
    // 年号マッピング表（頭文字 -> 完整年号）
    private static final Map<Character, String> ERA_MAP = new HashMap<>();
    static {
        ERA_MAP.put('M', "明治");
        ERA_MAP.put('T', "大正");
        ERA_MAP.put('S', "昭和");
        ERA_MAP.put('H', "平成");
        ERA_MAP.put('R', "令和");
        // 拡張可能な他の年号
    }

    @Autowired
    private ComMocGengouMapper comMocGengouMapper;
    /** 24-03 iniファイルデータ保存テーブル情報取得 */
    @Autowired
    private ComMocSysCIniSelectMapper comMocSysCIniSelectMapper;
    /** 24-03 iniファイルデータ保存テーブル */
    @Autowired
    private ComMocSysCIniMapper comMocSysCIniMapper;
    /** 職員ごと保存する設定の初期値設定マス タパラメータ */
    @Autowired
    private ComMocSetteiShokuDefSelectMapper comMocSetteiShokuDefSelectMapper;
    /**  */
    @Autowired
    private ComTucJigyoRestSelectMapper comTucJigyoRestSelectMapper;
    /** */
    @Autowired
    private ComMscHolidaySelectMapper comMscHolidaySelectMapper;
    /** */
    @Autowired
    private ComMscSvjigyoSelectMapper comMscSvjigyoSelectMapper;
    /** */
    @Autowired
    private ComMscSvjigyoRirekiSelectMapper comMscSvjigyoRirekiSelectMapper;
    /** 設定(職員単位)の参照 */
    @Autowired
    private ComMocSysiniSMapper comMocSysiniSMapper;
    /** NdsPosALogicクラス */
    @Autowired
    private NdsPosALogic ndsPosALogic;
    /** NdsMidaLogicクラス */
    @Autowired
    private NdsMidaLogic ndsMidaLogic;

    /**
     * 設定の読込（システム環境以外の設定）
     * 
     * @param in 入力情報リスト
     * @return パラメータリスト
     */
    public List<String> getF3gkProfileList(List<F3gkGetProfileInDto> ins) {
        return ins.stream().map(i -> getF3gkProfile(i)).toList();
    }

    /**
     * 文字列の年月日のｎヶ月前（後）を返す
     *
     * @param srcDate     変換前の年月日(和暦or西暦)
     * @param relativeNum ｎヶ月後（前）の値。正の場合はｎヶ月後、負の場合はｎヶ月前
     * @return 変換後の年月日(yyyymmdd)、異常の場合は空文字列
     */
    public String getRelativeMonth(String srcDate, int relativeNum) {
        // 西暦に変換 (yyyymmdd)
        String chgDate = getChangeSeireki(srcDate);
        if (chgDate == null || chgDate.isEmpty()) {
            return "";
        }

        // 年のみ？
        if (chgDate.length() < 5) {
            return "";
        }

        // 範囲チェック (明治1年～平成99年)
        if (!chkDate2(chgDate)) {
            return "";
        }

        // 増減なしなら chgDate を返す
        if (relativeNum == 0) {
            return chgDate;
        }

        try {
            DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate date = LocalDate.parse(chgDate, format);
            LocalDate relativeDate = date.plusMonths(relativeNum);

            // yyyymmdd形式で作成
            String workStr = relativeDate.format(format);
            return workStr;

        } catch (DateTimeException e) {
            return "";
        }
    }

    /**
     * 引数で渡された日付の曜日を返す
     * 
     * @param as_date 処理したい日付(yyyymmdd or yyyy/mm/dd)
     * @return 曜日フラグ 0～6(0:日曜日 1:月曜日 2:火曜日, 3:水曜日, 4:木曜日, 5:金曜日, 6:土曜日) null:エラー
     */
    public Integer getWeekDay(String as_date) {
        try {
            String dateStr = as_date.replace("/", "");
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
            return date.getDayOfWeek().getValue() % 7;
        } catch (Exception e) {
        }
        return null;
    }

    /**
     * 指定年の『春分の日』か『秋分の日』を計算して返す.
     *
     * @param year 指定年（1900～2099）
     * @param type １ : 春分の日を計算； ２ : 秋分の日を計算； ０ : 両方を返す。
     * @return ０以上 : 春分の日 もしくは 秋分の日。両方の場合には、100で割った答えが 春分の日。100で割った余りが 秋分の日； -1 :
     *         エラー
     * <AUTHOR> 張瑞濤.
     */
    public Integer retShunShuu(Integer year, Integer type) {
        // 年（計算用）
        Integer di = 0;
        // 4の倍数（計算用）
        Integer de = 0;
        // 判断結果コード
        Integer ret = -1;
        // 春分の日
        Integer li3 = 0;
        // 秋分の日
        Integer li9 = 0;

        // 計算年は（1980年～2099年まで）の場合
        if (year >= 1980 && year <= 2099) {
            // 1980 -> 2099
            di = year - 1980;
            de = di / 4;

            // 春分の日を計算
            li3 = ((242194 * di) + 20843100) / 1000000;
            li3 -= de;
            // 秋分の日を計算
            li9 = ((242194 * di) + 23248800) / 1000000;
            li9 -= de;

            // 計算年は（1900年～1979年まで）の場合
        } else if (year >= 1900 && year <= 1979) {
            // 1900 -> 1979
            di = year - 1983;
            de = di / 4;
            di = year - 1980;

            // 春分の日を計算
            li3 = ((242194 * di) + 20835700) / 1000000;
            li3 -= de;
            // 秋分の日を計算
            li9 = ((242194 * di) + 23258800) / 1000000;
            li9 -= de;
        }
        // 計算種類の判定
        switch (type) {
        case 1:
            // 計算種類：１（春分の日を計算）
            ret = li3;
            break;
        // 計算種類：２（秋分の日を計算）
        case 2:
            ret = li9;
            break;
        // 計算種類：０（両方を返す）
        case 0:
            ret = li3 * 100;
            ret += li9;
            break;
        default:
            break;
        }
        return ret;
    }

    /**
     * 指定した事業者の指定月の休館日・休業日情報を配列にして返す.
     *
     * @param yymm    指定年月.
     * @param alJigyo 事業所ＩＤ.
     * @return 休館日・休業日情報 １～31日
     * <AUTHOR> 王耀峰.
     */
    public List<Integer> getRestdayArray(String yymm, Integer jigyo) {
        List<Integer> result = null;

        String strYyyyMm = cnvYmd(yymm);
        if (strYyyyMm == null) {
            return result;
        }

        // 指定月判定 (失敗の場合 処理終了、NULLを戻り)
        strYyyyMm = getChangeSeireki(yymm);
        if (strYyyyMm == null) {
            return result;
        }

        // 指定年月の"/"を除く
        strYyyyMm = strYyyyMm.replaceAll("/", "");
        // 年月を取得
        // 年月 6桁以外の場合 (失敗の場合 処理終了、NULLを戻り)
        if (strYyyyMm.length() < 6) {
            return result;
        }

        // 年と月を取得
        int intYyyy = Integer.valueOf(strYyyyMm.substring(0, 4));
        int intMm = Integer.valueOf(strYyyyMm.substring(4, 6));

        // if (liy >= 1868 and lim > 0 and lim < 13 ) {
        // 年月下記ようにチェックする (失敗の場合 処理終了、NULLを戻り)
        // 年 は 1868以前
        // 月 は 1~12以外
        if (intYyyy <= 1868 || intMm <= 0 || intMm >= 13) {
            return result;
        }

        // DB検索の引数生成
        String strYyyyMmFirstDay = String.format("%d/%02d/01", intYyyy, intMm);

        // 指定事業者判定 (指定事業者 <= 0 の場合 処理終了、NULLを戻り)
        if (jigyo <= 0) {
            return result;
        }
        ComTucJigyoRest1monByCriteriaInEntity criteria = new ComTucJigyoRest1monByCriteriaInEntity();
        criteria.setAlSvj(jigyo);
        criteria.setAsYmd(strYyyyMmFirstDay);
        List<ComTucJigyoRest1monOutEntity> tucJigyoRestList = comTucJigyoRestSelectMapper
                .findComTucJigyoRest1monByCriteria(criteria);

        if (tucJigyoRestList != null && tucJigyoRestList.size() > 0) {
            result = new ArrayList<Integer>();
            for (ComTucJigyoRest1monOutEntity entity : tucJigyoRestList) {

                for (int i = 1; i <= 31; i++) {
                    String val = null;
                    try {
                        val = BeanUtils.getProperty(entity, "day" + i);
                    } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
                    }
                    // DAY1~31
                    if (val != null && Integer.parseInt(val) > 0) {
                        result.add(Integer.valueOf(1));
                    } else {
                        result.add(Integer.valueOf(0));
                    }
                }
            }
        }
        return result;
    }

    /**
     * 指定月の祝祭日・休館日・休業日情報取得.
     *
     * @param yymm  指定年月.
     * @param jigyo 事業所ＩＤ.
     * @return 休館日・休業日情報配列.
     * @throws Exception 例外.
     * <AUTHOR> 王耀峰.
     */
    public List<HolidayArrayOutDto> getHolidayArray(final String yymm, Integer jigyo) {

        int[] liDm = { 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31 };

        List<HolidayArrayOutDto> result = null;

        // 指定月判定 (失敗の場合 処理終了、NULLを戻り)
        String strYyyyMm = getChangeSeireki(yymm);
        if (strYyyyMm == null) {
            return result;
        }

        // 指定年月の"/"を除く
        strYyyyMm = strYyyyMm.replaceAll("/", "");
        // 年月を取得
        // 年月 6桁以外の場合 (失敗の場合 処理終了、NULLを戻り)
        if (strYyyyMm.length() < 6) {
            return result;
        }

        // 年月を取得
        strYyyyMm = strYyyyMm.substring(0, 6);

        // 年と月を取得
        int intYyyy;
        int intMm;
        intYyyy = Integer.parseInt(strYyyyMm.substring(0, 4));
        intMm = Integer.parseInt(strYyyyMm.substring(4, 6));

        // if (liy >= 1868 and lim > 0 and lim < 13 ) {
        // 年月下記ようにチェックする (失敗の場合 処理終了、NULLを戻り)
        // 年 は 1868以前
        // 月 は 1~12以外
        if (intYyyy <= 1868 || intMm <= 0 || intMm >= 13) {
            return result;
        }

        // DB検索の引数生成
        String strYyyyMmFirstDay = String.format("%d/%02d/01", intYyyy, intMm);
        strYyyyMm = strYyyyMmFirstDay;
        // 閏年チェック 閏年の場合、２月は29日を設定する
        if (chkUru(Integer.valueOf(intYyyy))) {
            liDm[1] = 29;
        }

        // 月初の曜日を取得
        int liw = 0;
        liw = getWeekDay(strYyyyMmFirstDay);
        // strYyyyMmFirstDay を使える

        // 配列を初期化
        result = new ArrayList<HolidayArrayOutDto>();
        result.add(new HolidayArrayOutDto()); // dummy
        for (int i = 1; i <= 31; i++) {
            int lir = i;
            if (lir > liDm[intMm - 1]) {
                lir = 0;
            }
            HolidayArrayOutDto gholidayArrayOutDto = new HolidayArrayOutDto();
            // 日付設定
            gholidayArrayOutDto.setDay(lir);
            // 曜日設定
            gholidayArrayOutDto.setWeek(liw);
            // 休日設定
            gholidayArrayOutDto.setHoliday(0);
            // 休日設定
            gholidayArrayOutDto.setInfo("");
            // 指定事業者休日設定
            gholidayArrayOutDto.setIrest(0);
            liw++;
            if (liw == 7) {
                liw = 0;
            }
            result.add(gholidayArrayOutDto);
        }
        // 祝祭日・振替休日 情報取得
        ComMscHoliday1monByCriteriaInEntity criteria = new ComMscHoliday1monByCriteriaInEntity();
        criteria.setAiY(intYyyy);
        criteria.setAiM(intMm);
        List<ComMscHoliday1monOutEntity> holidayList = comMscHolidaySelectMapper
                .findComMscHoliday1monByCriteria(criteria);

        // 祝祭日情報取得
        if (holidayList != null && holidayList.size() > 0) {
            for (ComMscHoliday1monOutEntity entity : holidayList) {
                // type_flg 分類フラグ
                int liHt = 0;
                // m_number 第ｎ週
                int liHmn = -1;
                // week 曜日
                int liHw = -1;
                // day 日
                int liHd = 0;
                // comment_knj コメント
                String lsHcm = "";

                // type_flg 分類フラグ
                if (entity.getTypeFlg() != null) {
                    liHt = entity.getTypeFlg().intValue();
                }

                // m_number 第ｎ週
                if (entity.getMNumber() != null) {
                    liHmn = entity.getMNumber().intValue();
                }

                // week 曜日
                if (entity.getWeek() != null) {
                    liHw = entity.getWeek().intValue();
                }

                // day 日
                if (entity.getDay() != null) {
                    liHd = entity.getDay().intValue();
                }

                // comment_knj コメント
                if (entity.getCommentKnj() != null) {
                    lsHcm = entity.getCommentKnj();
                }

                // 変数 祝祭日
                int lid = 1;
                // 祝日フラグをチェック
                if (liHt == 0) {
                    // 祝祭日
                    lid = 1;

                    if (liHmn == -1 && liHw == -1) {
                        // 日付指定
                        lid = liHd;
                    } else if (liHmn > 0 && liHmn < 6 && liHw >= 0 && liHw <= 6) {
                        // 第(liHmn)週(liHw)曜日 指定
                        int lic = 0;
                        for (int i = 1; i <= 31; i++) {
                            // (liHw)曜日チェック
                            if (result.get(i).getWeek() == liHw) {
                                lic++;
                                // 第(liHmn)週チェック
                                if (liHmn == lic) {
                                    lid = i;
                                    break;
                                }
                            }
                        }
                    }

                } else if (liHt == 1 || liHt == 2) {
                    // 春分の日・秋分の日
                    lid = retShunShuu(intYyyy, liHt);
                    // lid = f_ret_shun_shuu( liy , liHt )
                    if (lid == -1) {
                        lid = 0;
                    }
                } // 祝日フラグをチェック End if

                // 情報を構造体にセット
                if (lid > 0 && lid <= 31) {
                    result.get(lid).setHoliday(1);
                    result.get(lid).setInfo(lsHcm);
                }
            }

            /*
             * 振替休日チェック START
             * ・国民の休日チェック-------------------------------------------------------------------
             * --------
             *
             */
            boolean lbKokumin = false;
            Boolean lbFri = false;
            if (intYyyy > 2006) {
                // 2007/01/01 施行の祝日法改正対応 START
                for (int lid = 1; lid <= 31; lid++) {
                    HolidayArrayOutDto day = result.get(lid);
                    // 有効日のみ
                    if (day.getDay() > 0) {
                        // 振替休日を探すSTART ：法改正により次の平日が見つかるまで
                        if (day.getWeek() != 0 && day.getHoliday() == 0 && lbFri == true) {
                            day.setHoliday(1);
                            day.setInfo("振替休日");
                            lbFri = false;
                        } // 振替休日を探す END

                        // 国民の休日を探す //2008/12/08 Mami.O
                        if (day.getWeek() != 0 && day.getHoliday() == 0 && lbKokumin == true) {
                            day.setHoliday(1);
                            day.setInfo("国民の休日");
                            lbKokumin = false;
                        }
                        // 振替休日の対象となる日曜かつ祝日を探す
                        if (day.getWeek() == 0 && day.getHoliday() == 1) {
                            lbFri = true;
                        }
                        // 国民の休日の対象となる祝日を探す //2008/12/08 Mami.O
                        if (day.getWeek() != 0 && day.getHoliday() == 1 && !day.getInfo().equals("振替休日")) {
                            if (lid + 2 <= 31) {
                                if (result.get(lid + 2).getWeek() != 0 && result.get(lid + 2).getHoliday() == 1
                                        && !result.get(lid + 2).getInfo().equals("振替休日")) {
                                    lbKokumin = true;
                                } else {
                                    // lbKokumin = false;
                                }
                            }
                        }
                    } // End if // 有効日のみ
                } // 振替休日チェック Next

            } else {
                // 法改正以前の振替休日
                for (int lid = 1; lid <= 31; lid++) {
                    HolidayArrayOutDto day = result.get(lid);
                    if (day.getDay() > 0) {
                        // 有効日のみ
                        // 振替休日を探す
                        if (day.getWeek() == 1 && day.getHoliday() == 0 && lbFri == true) {
                            day.setHoliday(1);
                            day.setInfo("振替休日");
                        } // End if 振替休日を探す

                        lbFri = false;
                        // 振替休日の対象となる日曜かつ祝日を探す
                        if (day.getWeek() == 0 && day.getHoliday() == 1) {
                            lbFri = true;
                        } // End if 振替休日の対象となる日曜かつ祝日を探す
                    } // End if 有効日のみ
                } // Next 振替休日チェック
            } // End if 振替休日チェック END
        } // End if
          // 祝祭日・振替休日 情報取得 ------------------------------------------------------------
          // 検索結果あるの判定
        /*
         * 指定事業者判定----------------------------------------------------------------------
         * ----------
         */
        // 指定事業者判定 (指定事業者 <= 0 の場合 処理終了、NULLを戻り)
        if (jigyo > 0) {
            // 指定年月の"/"を除く
            strYyyyMm = strYyyyMm.replaceAll("/", "");
            // if al_jigyo > 0 Then
            // 指定した事業者の指定月の休館日・休業日情報を配列にして返す
            List<Integer> resultRestdayArray = getRestdayArray(strYyyyMm, jigyo);
            // 取得成功時のみ
            if (resultRestdayArray != null) {
                for (int i = 1; i <= 31; i++) {
                    // ある日だけ
                    if (result.get(i).getDay() > 0) {
                        result.get(i).setIrest(resultRestdayArray.get(i - 1).intValue());
                    }
                } // Next
            } // End if 取得成功時のみ
        }

        // 指定事業者判定 CLOSE必要ありません。
        result.remove(0);// remove dummy
        return result;
    }

    /**
     * 西暦(年月日)に変換して返す
     * 
     * ★変換失敗の場合、戻り値【null】 ★日付： 和暦 or 西暦
     *
     * @param asDate 和暦または西暦の日付文字列 (入力)
     * @return 西暦形式の日付文字列 (yyyymmdd) または null (変換失敗時)
     */
    public String getChangeSeireki(String asDate) {
        if (asDate == null || asDate.isEmpty()) {
            return null;
        }

        String lsDate = null;
        String lsSeireki = null;

        // 全角元号（ＹＹ年ＭＭ月ＤＤ日）→ 半角 (Gyy/mm/dd)
        lsDate = cnv2han(0, asDate);
        if (lsDate == null) {
            lsDate = asDate;
        }

        // 日付を標準形式に変換
        lsDate = getCnvYymmdd(lsDate);
        if (StringUtils.isNotEmpty(lsDate)) {
            // 和暦かどうかを判定
            if (StringUtils.isNotEmpty(chkIswareki(lsDate))) {
                // 和暦を西暦に変換
                lsSeireki = retW2s(lsDate);
                if (StringUtils.isNotEmpty(lsSeireki)) {
                    // 西暦形式に変換
                    if (StringUtils.isNotEmpty(cnvYmd(lsSeireki))) {
                        return lsSeireki;
                    } else {
                        return null;
                    }
                } else {
                    return null;
                }
            } else {
                // 西暦形式の日付をチェック
                if (chkDate(lsDate).isResult()) {
                    if (lsDate.length() == 8) {
                        if (StringUtils.isNotEmpty(cnvYmd(lsDate))) {
                            return lsDate;
                        } else {
                            return null;
                        }
                    } else {
                        if (StringUtils.isNotEmpty(cnvYmd(lsDate))) {
                            return lsDate;
                        } else {
                            return null;
                        }
                    }
                } else {
                    return null;
                }
            }
        } else {
            return null;
        }
    }

    /**
     * 全角日付変更（和暦）-> 半角日付（和暦Gyy/mm/dd）
     *
     * @param aiMode    0: H09/10/05 のようにゼロ付き, 1: H9/10/5 のようにゼロ無し
     * @param asZenDate 全角日付文字列（例: "元号ＹＹ年ＭＭ月ＤＤ日"）
     * @return 半角日付文字列Gyy/mm/dd（例: "H09/10/05"）または null（変換失敗時）
     */
    public String cnv2han(Integer aiMode, String asZenDate) {
        if (asZenDate == null || asZenDate.isEmpty()) {
            return null;
        }

        String lsGen = "";
        String lsYear = "";
        String lsMonth = "";
        String lsDay = "";

        // 入力日付の分割
        lsGen = asZenDate.substring(0, 2); // 元号取得
        int llPos1 = 2;
        int llPos2 = asZenDate.indexOf("年", llPos1);

        if (llPos2 < 0) {
            return null; // 年が見つからない場合
        }

        lsYear = asZenDate.substring(llPos1, llPos2).trim();
        llPos1 = llPos2 + 1;

        llPos2 = asZenDate.indexOf("月", llPos1);
        if (llPos2 >= 0) {
            lsMonth = asZenDate.substring(llPos1, llPos2).trim();
            llPos1 = llPos2 + 1;
        }

        llPos2 = asZenDate.indexOf("日", llPos1);
        if (llPos2 >= 0) {
            lsDay = asZenDate.substring(llPos1, llPos2).trim();
        }

        // 元号変換
        switch (lsGen) {
        case "明治":
            lsGen = "M";
            break;
        case "大正":
            lsGen = "T";
            break;
        case "昭和":
            lsGen = "S";
            break;
        case "平成":
            lsGen = "H";
            break;
        case "令和":
            lsGen = "R";
            break;
        default:
            return null; // 未知の元号
        }

        // 数字の先頭にゼロを付けるモード
        if (aiMode == 0) {
            lsYear = String.format("%02d", Integer.parseInt(lsYear));
            if (!lsMonth.isEmpty()) {
                lsMonth = String.format("%02d", Integer.parseInt(lsMonth));
            }
            if (!lsDay.isEmpty()) {
                lsDay = String.format("%02d", Integer.parseInt(lsDay));
            }
        }

        // 半角日付の組み立て
        StringBuilder asHanDate = new StringBuilder(lsGen).append(getNumZen2Han(lsYear));
        if (!lsMonth.isEmpty()) {
            asHanDate.append("/").append(getNumZen2Han(lsMonth));
        }
        if (!lsDay.isEmpty()) {
            asHanDate.append("/").append(getNumZen2Han(lsDay));
        }

        return asHanDate.toString();
    }

    /**
     * 引数で渡された日付中、全角数字を半角数字に変更する
     *
     * @param asZenDate 変換前の全角数字文字列
     * @return 変換後の半角数字文字列 (変換失敗時は null を返す)
     */
    public String getNumZen2Han(String asZenDate) {
        if (asZenDate == null || asZenDate.isEmpty()) {
            return null;
        }

        String oldStr = "１２３４５６７８９０";
        String[] newStr = { "1", "2", "3", "4", "5", "6", "7", "8", "9", "0" };
        StringBuilder lsChange = new StringBuilder();

        for (int i = 0; i < asZenDate.length(); i++) {
            String mistring = asZenDate.substring(i, i + 1);
            int srchPos = oldStr.indexOf(mistring);

            if (srchPos >= 0) {
                // 全角数字を半角数字に変換
                lsChange.append(newStr[srchPos]);
            } else if ("0123456789".contains(mistring)) {
                // すでに半角数字の場合はそのまま追加
                lsChange.append(mistring);
            } else {
                // 数字以外の文字が含まれている場合は変換失敗
                return null;
            }
        }

        return lsChange.toString();
    }

    /**
     * ソース文字列の左端から n バイト分の 文字列を返します
     * 
     * @param data 切り取る文字列データ
     * @param cnt  切り取るbyte数
     * @return 切り取ったデータ
     */
    public String f3gkLeftc(String data, int cnt) {
        Charset charset = StandardCharsets.UTF_8;
        byte[] bytes = data.getBytes(charset);
        if (bytes.length <= cnt) {
            return data;
        }
        int endIndex = cnt;
        while (endIndex > 0 && (bytes[endIndex] & 0xC0) == 0x80) {
            endIndex--;
        }
        // ソース文字列の左端から n バイト分の 文字列を返します
        return new String(bytes, 0, endIndex, charset);
    }

    /**
     * 閏年チェック
     * 
     * @param year 検査対象の年
     * @return true:閏年、false:閏年ではない
     */
    public boolean chkUru(final Integer year) {
        // 閏年条件1: 4で割り切れ、かつ100で割り切れない年
        final boolean isOrdinaryLeapYear = (year % INTEGER_4 == 0) && (year % INTEGER_100 != 0);
        // 閏年条件2: 400で割り切れる年
        final boolean isSpecialLeapYear = (year % INTEGER_400 == 0);
        // いずれかの条件を満たす場合閏年と判定
        return isOrdinaryLeapYear || isSpecialLeapYear;
    }

    /**
     * 月末日チェック
     * 
     * @param dateStr 日付
     * @return 日付
     */
    public ChkDateOutDto chkDate(String dateStr) {
        ChkDateOutDto outDto = new ChkDateOutDto();
        // 入力値長さチェックと基本日付チェック
        if (dateStr.length() > 8 || !chkDate2(dateStr.toString())) {
            outDto.setDate(dateStr);
            outDto.setResult(false);
            return outDto;
        }
        // 年月日分解
        int year = Integer.parseInt(dateStr.substring(0, 4));
        int month = CommonConstants.INT_0;
        if (dateStr.length() > CommonConstants.INT_4) {
            month = Integer.parseInt(dateStr.substring(4, 6));
        }
        int day = CommonConstants.INT_0;
        if (dateStr.length() > CommonConstants.INT_6) {
            day = Integer.parseInt(dateStr.substring(6, 8));
        }

        // 月範囲チェック
        if (month < 1 || month > 12) {
            outDto.setDate(dateStr);
            outDto.setResult(false);
            return outDto;
        }
        // 月の上限下限を設定
        if (year > CommonConstants.INT_0 && month < CommonConstants.INT_13 && month > CommonConstants.INT_0
                && day < CommonConstants.INT_0) {
            // 閏年判定
            int[] adjustedMonthDays = Arrays.copyOf(MONTH_DAYS, MONTH_DAYS.length);
            if (chkUru(year)) {
                adjustedMonthDays[1] = LEAP_FEBRUARY_DAYS;
            } else {
                adjustedMonthDays[1] = NORMAL_FEBRUARY_DAYS;
            }
            // 日数チェック
            int maxDay = adjustedMonthDays[month - 1];
            if (day < 1 || day > maxDay) {
                // 日付自動修正
                String correctedDate = String.format(YEAR_FORMAT, year) + String.format(MONTH_DAY_FORMAT, month)
                        + String.format(MONTH_DAY_FORMAT, maxDay);
                StringBuilder builder = new StringBuilder(dateStr);
                builder.replace(0, dateStr.length(), correctedDate);
                outDto.setDate(dateStr);
                outDto.setResult(false);
                return outDto;
            }
            outDto.setDate(dateStr);
            outDto.setResult(true);
            return outDto;
        } else {
            if (month > CommonConstants.INT_12) {
                outDto.setDate(dateStr);
                outDto.setResult(false);
                return outDto;
            }
            outDto.setDate(dateStr);
            outDto.setResult(true);
            return outDto;
        }
    }

    /**
     * 日付上限(2087/12/31)下限(1867/7/8)チェック
     * 
     * @param dateStr 日付(yyyymmdd or yyyymm or yyyy)
     * @return チェック結果 True：成功 False：失敗
     */
    public boolean chkDate2(String dateStr) {
        // 入力値の長さチェック
        final int length = dateStr.length();
        // 年、月、日初期化
        int year = 0;
        int month = 0;
        int day = 0;
        switch (length) {
        case 8: // YYYYMMDD形式
            year = Integer.parseInt(dateStr.substring(0, 4));
            month = Integer.parseInt(dateStr.substring(4, 6));
            day = Integer.parseInt(dateStr.substring(6, 8));
            break;
        case 6: // YYYYMM形式
            year = Integer.parseInt(dateStr.substring(0, 4));
            month = Integer.parseInt(dateStr.substring(4, 6));
            break;
        case 4: // YYYY形式
            year = Integer.parseInt(dateStr.substring(0, 4));
            break;
        default:
            return false;
        }

        // 年範囲チェック
        if (!isValidYearRange(year)) {
            return false;
        }

        // 月日詳細チェック
        return switch (length) {
        case 8 -> checkFullDate(year, month, day);
        case 6 -> !(year == MIN_YEAR && month < MIN_MONTH);
        case 4 -> true;
        default -> false;
        };
    }

    /**
     * 年範囲チェック（改元対応）
     * 
     * @param year チェック対象年
     * @return 有効範囲内の場合true
     */
    private boolean isValidYearRange(int year) {
        final int maxYear = calculateMaxYear();
        return year >= MIN_YEAR && year <= maxYear;
    }

    /**
     * 最大年計算（年号配列から算出）
     * 
     * @return 許容最大年
     */
    private int calculateMaxYear() {
        ComMocGengouCriteria criteria = new ComMocGengouCriteria();
        // SORT
        criteria.setOrderByClause(COM_MOC_GENGOU_SORT);
        List<ComMocGengou> comMocGengou = comMocGengouMapper.selectByCriteria(criteria);
        if (CollectionUtils.isEmpty(comMocGengou)) {
            return YEAR_2087;
        }
        List<Integer> giGengouYyyy = comMocGengou.stream().map(ComMocGengou::getYyyy).collect(Collectors.toList());
        return giGengouYyyy.get(giGengouYyyy.size() - 1) + YEAR_ADD_98;
    }

    /**
     * 年月日チェック
     * 
     * @param year  年
     * @param month 月
     * @param day   日
     * @return チェック結果
     */
    private boolean checkFullDate(int year, int month, int day) {
        // 1868年7月8日以前チェック
        if (year == MIN_YEAR) {
            if (month < MIN_MONTH) {
                return false;
            }
            if (month == MIN_MONTH && day < MIN_DAY) {
                return false;
            }
        }
        return true;
    }

    /**
     * 和暦チェック
     * 
     * @param dateStr チェック対象日付文字列
     * @return チェックエラーの場合、戻り値【null】
     */
    public String chkIswareki(String dateStr) {
        if (dateStr.length() == 0) {
            return null;
        }
        StringBuilder date = new StringBuilder(dateStr);
        char firstChar = Character.toUpperCase(dateStr.charAt(0));
        boolean found = false;
        // 基本文字チェック
        for (char validChar : VALID_GENGOU) {
            if (firstChar == validChar) {
                date.setCharAt(0, validChar);
                found = true;
                break;
            }
        }
        if (found) {
            return date.toString();
        }

        // 改元対応チェック
        // gs_gengou3
        ComMocGengouCriteria criteria = new ComMocGengouCriteria();
        // SORT
        criteria.setOrderByClause(COM_MOC_GENGOU_SORT);
        List<ComMocGengou> comMocGengou = comMocGengouMapper.selectByCriteria(criteria);
        if (CollectionUtils.isEmpty(comMocGengou)) {
            return null;
        }
        List<String> gsGengou3List = comMocGengou.stream().map(ComMocGengou::getGengou3).collect(Collectors.toList());
        for (String gengou3 : gsGengou3List) {
            if (gengou3.equalsIgnoreCase(String.valueOf(firstChar))) {
                date.setCharAt(0, gengou3.charAt(0));
                return date.toString();
            }
        }
        return null;
    }

    /**
     * 記入用シート印刷時、処理日から印刷日を返す PowerBuilderのf_blankdate相当
     * 
     * @param asYmd 日付
     * @return 印刷用日付（例：平成 年 月 日）
     */
    public String blankDate(String asYmd) {
        // 年号（和暦）を取得
        String lsGengouYmd = stringDateWareki(asYmd, "gg");
        // フォーマットを追加
        lsGengouYmd = lsGengouYmd + "  年  月  日";
        return lsGengouYmd;
    }

    /**
     * 元号・和暦変換
     * 
     * @param asDate  日付（西暦 or 和暦 or TODAY）
     * @param asParam 変換パターン
     * @return 変換後文字列
     */
    public String stringDateWareki(String asDate, String asParam) {
        String lsReturn = "";

        // 文字列の適正判断
        if (asDate == null || asDate.isEmpty() || asDate.length() > 11) {
            return "";
        }

        // "TODAY"対応
        if ("TODAY".equalsIgnoreCase(fNdSUpper(asDate))) {
            asDate = LocalDate.now().format(DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD));
        } else {
            // スペースを0に変換
            asDate = sp2zero(asDate);
            if (Character.isDigit(asDate.charAt(0))) {
                switch (asDate.length()) {
                case 4 -> asDate += "/01/01";
                case 7 -> asDate += "/01";
                }
            } else {
                switch (asDate.length()) {
                case 3 -> asDate += "/01/01";
                case 6 -> asDate += "/01";
                }
            }
        }

        // 入力引数(as_date)のチェックと"/"・"-"・"."文字の削除
        String lsDate = getCnvYymmdd(asDate);
        if (lsDate == null) {
            return "";
        }
        String lsSeireki;
        String warekiCheck = chkIswareki(lsDate);
        if (warekiCheck != null) {
            lsSeireki = retW2s(lsDate);
            if (lsSeireki == null) {
                return "";
            }
        } else {
            lsSeireki = lsDate;
        }

        // 和暦に変換
        if (chkDate(lsSeireki).isResult()) {
            int liY = Integer.parseInt(ndsMidaLogic.fNdsMida(lsSeireki, 1, 4));
            int liM = Integer.parseInt(ndsMidaLogic.fNdsMida(lsSeireki, 5, 2));
            int liD = Integer.parseInt(ndsMidaLogic.fNdsMida(lsSeireki, 7, 2));

            // 元号情報取得
            List<ComMocGengou> gengouList = comMocGengouMapper.selectByCriteria(new ComMocGengouCriteria());
            if (CollectionUtils.isEmpty(gengouList)) {
                return "";
            }
            int liCnt = gengouList.size();
            for (int liIdx = liCnt - 1; liIdx >= 0; liIdx--) {
                ComMocGengou gengou = gengouList.get(liIdx);
                int ggY = gengou.getYyyy();
                int ggM = gengou.getMm();
                int ggD = gengou.getDd();

                if (ggY > liY)
                    continue;
                if (ggY == liY && ggM > liM)
                    continue;
                if (ggY == liY && ggM == liM && ggD > liD)
                    continue;

                lsReturn = asParam;

                // 和暦年、月、日編集
                String lsWarekiYy = String.valueOf(liY - ggY + 1);
                String lsMm = String.valueOf(liM);
                String lsDd = String.valueOf(liD);

                // 元号全角２文字
                Integer liRet = ndsPosALogic.fNdsPosa(lsReturn, "gg");
                while (liRet > 0) {
                    lsReturn = replaceA(lsReturn, liRet, 2, gengou.getGengou2Knj());
                    liRet = ndsPosALogic.fNdsPosa(lsReturn, "gg");
                }

                // 元号全角1文字
                liRet = ndsPosALogic.fNdsPosa(lsReturn, "g");
                while (liRet > 0) {
                    lsReturn = replaceA(lsReturn, liRet, 1, gengou.getGengou1Knj());
                    liRet = ndsPosALogic.fNdsPosa(lsReturn, "g");
                }

                // 元号半角１文字
                liRet = ndsPosALogic.fNdsPosa(lsReturn, "G");
                while (liRet > 0) {
                    lsReturn = replaceA(lsReturn, liRet, 1, gengou.getGengou3());
                    liRet = ndsPosALogic.fNdsPosa(lsReturn, "G", liRet + 1);
                }

                // 和暦年前ゼロ埋め
                liRet = ndsPosALogic.fNdsPosa(lsReturn, "nn");
                String lsSpace = (lsWarekiYy.length() == 1) ? "0" : "";
                while (liRet > 0) {
                    lsReturn = replaceA(lsReturn, liRet, 2, lsSpace + lsWarekiYy);
                    liRet = ndsPosALogic.fNdsPosa(lsReturn, "nn");
                }

                // 和暦年前空白埋め
                liRet = ndsPosALogic.fNdsPosa(lsReturn, "sn");
                lsSpace = (lsWarekiYy.length() == 1) ? " " : "";
                while (liRet > 0) {
                    lsReturn = replaceA(lsReturn, liRet, 2, lsSpace + lsWarekiYy);
                    liRet = ndsPosALogic.fNdsPosa(lsReturn, "sn");
                }

                // 和暦年
                liRet = ndsPosALogic.fNdsPosa(lsReturn, "n");
                while (liRet > 0) {
                    if (liRet == lsReturn.length()) {
                        lsReturn = ndsMidaLogic.fNdsMida(lsReturn, 1, liRet - 1) + lsWarekiYy;
                    } else {
                        lsReturn = ndsMidaLogic.fNdsMida(lsReturn, 1, liRet - 1) + lsWarekiYy
                                + ndsMidaLogic.fNdsMida(lsReturn, liRet + 1);
                    }
                    liRet = ndsPosALogic.fNdsPosa(lsReturn, "n");
                }

                // 月前ゼロ埋め
                liRet = ndsPosALogic.fNdsPosa(lsReturn, "mm");
                lsSpace = (lsMm.length() == 1) ? "0" : "";
                while (liRet > 0) {
                    lsReturn = replaceA(lsReturn, liRet, 2, lsSpace + lsMm);
                    liRet = ndsPosALogic.fNdsPosa(lsReturn, "mm");
                }

                // 月前空白埋め
                liRet = ndsPosALogic.fNdsPosa(lsReturn, "sm");
                lsSpace = (lsMm.length() == 1) ? " " : "";
                while (liRet > 0) {
                    lsReturn = replaceA(lsReturn, liRet, 2, lsSpace + lsMm);
                    liRet = ndsPosALogic.fNdsPosa(lsReturn, "sm");
                }

                // 月
                liRet = ndsPosALogic.fNdsPosa(lsReturn, "m");
                while (liRet > 0) {
                    if (liRet == lsReturn.length()) {
                        lsReturn = ndsMidaLogic.fNdsMida(lsReturn, 1, liRet - 1) + lsMm;
                    } else {
                        lsReturn = ndsMidaLogic.fNdsMida(lsReturn, 1, liRet - 1) + lsMm
                                + ndsMidaLogic.fNdsMida(lsReturn, liRet + 1);
                    }
                    liRet = ndsPosALogic.fNdsPosa(lsReturn, "m");
                }

                // 日前ゼロ埋め
                liRet = ndsPosALogic.fNdsPosa(lsReturn, "dd");
                lsSpace = (lsDd.length() == 1) ? "0" : "";
                while (liRet > 0) {
                    lsReturn = replaceA(lsReturn, liRet, 2, lsSpace + lsDd);
                    liRet = ndsPosALogic.fNdsPosa(lsReturn, "dd");
                }

                // 日前空白埋め
                liRet = ndsPosALogic.fNdsPosa(lsReturn, "sd");
                lsSpace = (lsDd.length() == 1) ? " " : "";
                while (liRet > 0) {
                    lsReturn = replaceA(lsReturn, liRet, 2, lsSpace + lsDd);
                    liRet = ndsPosALogic.fNdsPosa(lsReturn, "sd");
                }

                // 日
                liRet = ndsPosALogic.fNdsPosa(lsReturn, "d");
                while (liRet > 0) {
                    if (liRet == lsReturn.length()) {
                        lsReturn = ndsMidaLogic.fNdsMida(lsReturn, 1, liRet - 1) + lsDd;
                    } else {
                        lsReturn = ndsMidaLogic.fNdsMida(lsReturn, 1, liRet - 1) + lsDd
                                + ndsMidaLogic.fNdsMida(lsReturn, liRet + 1);
                    }
                    liRet = ndsPosALogic.fNdsPosa(lsReturn, "d");
                }

                break; // 最初に該当した元号で抜ける
            }
        }

        return lsReturn;
    }

    /**
     * 指定位置から指定長さ分を置換する
     */
    private String replaceA(String src, int pos, int len, String rep) {
        // pos: 1-based
        int start = Math.max(pos - 1, 0);
        int end = Math.min(start + len, src.length());
        return src.substring(0, start) + rep + src.substring(end);
    }

    /**
     * 和暦の数字部分の先頭スペースを０に変換する
     * 
     * @param asGymd asGymd - "gyy/mm/dd" または "gyymmdd"、"yyyy/mm/dd" または "yyyymmdd"
     *               形式の文字列
     * @return "gyy/mm/dd" 形式の文字列。エラー時は空文字列を返す。
     */
    public String sp2zero(String asGymd) {
        int liYy = 0, liMm = 0, liDd = 0, liNn = 0;
        String lsGg = "";
        String lsDate = "";

        // 日付セパレータチェック
        SepaOutDto result = chksepa2(asGymd);
        if (result == null)
            return "";
        liNn = result.getSeparatorCnt();
        lsGg = result.getGengo();
        liYy = result.getYear();
        liMm = result.getMonth();
        liDd = result.getDay();

        switch (liNn) {
        case -1:
            return "";
        case 0:
            if (lsGg.length() > 0 && asGymd.length() > 7) {
                return "";
            }
            break;
        default:
            break;
        }

        // 編集
        if (liYy > 0) {
            lsDate = lsGg + String.format("%02d", liYy);
        }
        if (liMm > 0) {
            lsDate += "/" + String.format("%02d", liMm);
        }
        if (liDd > 0) {
            lsDate += "/" + String.format("%02d", liDd);
        }

        return lsDate;
    }

    /**
     * 日付セパレータチェック
     * 
     * @param dateStr 日付 yyyy/MM/dd型
     * @return セパレータ個数 日付中の '/'の個数を得る
     */
    public SepaOutDto chksepa2(String dateStr) {
        String trimDate = dateStr.replaceAll(" ", "0");
        SepaOutDto outDto = chksepa(trimDate);

        return outDto;
    }

    /**
     * 日付セパレータチェック.
     *
     * @param date 日付.
     * @return 日付情報出力DTO.
     * <AUTHOR> 于占玖.
     */
    public SepaOutDto chksepa(String date) {

        String lsGg = "";
        String newData;
        Integer[] liNum = new Integer[3];
        // 和暦フラグ
        Integer flg = 0;
        // 元号
        String asGg;
        // 年
        Integer asYy = 0;
        // 月
        Integer asMm = 0;
        // 日
        Integer asDd = 0;

        // 和暦チェック
        newData = chkIswareki(date);
        if (newData != null) {
            flg = 1;
            lsGg = newData.substring(0, 1);
        } else {
            newData = date;
        }

        char[] wk1 = newData.toCharArray();
        Integer k = 0;

        for (int i = flg; i < newData.length(); i++) {
            if (wk1[i] == "/".charAt(0) || wk1[i] == "-".charAt(0) || wk1[i] == ",".charAt(0)
                    || wk1[i] == ".".charAt(0)) {
                k++;
                if (k > 2) {
                    return null;
                }
            }
        }

        // セパレータあり
        if (k > 0) {
            String[] dateList = newData.substring(flg).split("/|-|\\.|,");
            if (k == 1) {
                if (NumberUtils.isParsable(dateList[0]) && NumberUtils.isParsable(dateList[1])
                        && (dateList[0].length() == 4 || dateList[0].length() == 2) && dateList[1].length() == 2) {
                    liNum[0] = Integer.valueOf(dateList[0]);
                    liNum[1] = Integer.valueOf(dateList[1]);
                } else {
                    return null;
                }
            } else if (k == 2) {
                if (NumberUtils.isParsable(dateList[0]) && NumberUtils.isParsable(dateList[1])
                        && NumberUtils.isParsable(dateList[2])
                        && (dateList[0].length() == 4 || dateList[0].length() == 2) && dateList[1].length() == 2
                        && dateList[2].length() == 2) {
                    liNum[0] = Integer.valueOf(dateList[0]);
                    liNum[1] = Integer.valueOf(dateList[1]);
                    liNum[2] = Integer.valueOf(dateList[2]);
                } else {
                    return null;
                }
            }
        }

        if (k == 0) {
            // 和暦
            if (lsGg.length() == 1) {
                asGg = lsGg;
                asYy = Integer.parseInt(newData.substring(1, 3));
                if (newData.length() >= 5) {
                    asMm = Integer.parseInt(newData.substring(3, 5));
                }
                if (newData.length() > 5) {
                    asDd = Integer.parseInt(newData.substring(5));
                }

            } else {
                asGg = lsGg;
                asYy = Integer.parseInt(newData.substring(0, 4));
                if (newData.length() >= 6) {
                    asMm = Integer.parseInt(newData.substring(4, 6));
                }
                if (newData.length() > 6) {
                    asDd = Integer.parseInt(newData.substring(6));
                }
            }
        } else {
            asGg = lsGg;
            if (k == 1) {
                if (liNum[0] == 0 || liNum[1] == 0) {
                    return null;
                } else {
                    asYy = liNum[0];
                    asMm = liNum[1];
                }
            } else if (k == 2) {
                if (liNum[0] == 0 || liNum[1] == 0 || liNum[2] == 0) {
                    return null;
                } else {
                    asYy = liNum[0];
                    asMm = liNum[1];
                    asDd = liNum[2];
                }
            }
        }

        if (asMm > 12 || asDd > 31) {
            return null;
        }

        SepaOutDto outDto = new SepaOutDto();
        outDto.setSeparatorCnt(k);
        outDto.setGengo(asGg);
        outDto.setYear(asYy);
        outDto.setMonth(asMm);
        outDto.setDay(asDd);
        return outDto;
    }

    /**
     * 和暦→西暦変換
     * 
     * @param wareki 和暦
     * @return 変換後の西暦
     */
    public String retW2s(String wareki) {
        // 入力値チェック
        if (StringUtils.isEmpty(wareki) || StringUtils.length(wareki) < 2) {
            return null;
        }
        // 西暦
        StringBuilder seireki = new StringBuilder();
        // 元号部分抽出
        String gengou = wareki.substring(0, 1);
        String datePart = wareki.substring(1);
        int length = datePart.length();

        // 有効長チェック
        if (length != 2 && length != 4 && length != 6) {
            return null;
        }

        // 元号インデックス検索
        int gengouIndex = -1;
        for (int i = 0; i < GG_G.length; i++) {
            if (GG_G[i].equals(gengou)) {
                gengouIndex = i;
                break;
            }
        }
        ComMocGengouCriteria criteria = new ComMocGengouCriteria();
        // SORT
        criteria.setOrderByClause(COM_MOC_GENGOU_SORT);
        List<ComMocGengou> comMocGengou = comMocGengouMapper.selectByCriteria(criteria);
        if (CollectionUtils.isEmpty(comMocGengou)) {
            return null;
        }
        List<String> gsGengou3List = comMocGengou.stream().map(ComMocGengou::getGengou3).collect(Collectors.toList());
        List<Integer> gsGengouYyyyList = comMocGengou.stream().map(ComMocGengou::getYyyy).collect(Collectors.toList());
        // 改元対応チェック
        int newGengouIndex = -1;
        if (gengouIndex == -1) {
            for (int i = 0; i < gsGengou3List.size(); i++) {
                if (gsGengou3List.get(i).equals(gengou)) {
                    newGengouIndex = i;
                    break;
                }
            }
            if (newGengouIndex == -1) {
                return null;
            }
        }

        // 年数計算
        int warekiYear;
        try {
            warekiYear = Integer.parseInt(datePart.substring(0, 2));
        } catch (NumberFormatException e) {
            return null;
        }

        // 西暦年計算
        int seirekiYear;
        if (gengouIndex != -1) {
            seirekiYear = START_YEARS[gengouIndex] + warekiYear - 1;
        } else {
            seirekiYear = gsGengouYyyyList.get(newGengouIndex) + warekiYear - 1;
        }

        // 日付部品組み立て
        StringBuilder convertedDate = new StringBuilder();
        convertedDate.append(String.format(YEAR_FORMAT, seirekiYear));

        if (length >= 4) {
            int month = Integer.parseInt(datePart.substring(2, 4));
            convertedDate.append(String.format(MONTH_DAY_FORMAT, month));
        }

        if (length >= 6) {
            int day = Integer.parseInt(datePart.substring(4, 6));
            convertedDate.append(String.format(MONTH_DAY_FORMAT, day));
        }

        // 日付妥当性チェック
        ChkDateOutDto chkDateOutDto = chkDate(convertedDate.toString());
        if (chkDateOutDto.isResult()) {
            seireki = new StringBuilder(chkDateOutDto.getDate());
        } else {
            return null;
        }
        return seireki.toString();
    }

    /**
     * 日付の書式変換
     * 
     * @param date 日付 yyyy/mm/dd or yy/mm/dd (Val)
     * @return 変換失敗の場合、戻り値【null】
     */
    public String getCnvYymmdd(String date) {
        String result = null;
        // f_chksepa の戻り値 予想 ChkSepaOutDto
        SepaOutDto gchkSepaOutDto = chksepa(date);
        if (gchkSepaOutDto == null) {
            return result;
        }

        // f_chksepa の戻り値 予想 ChkSepaOutDto
        if (gchkSepaOutDto.getSeparatorCnt() == -1) {
            return result;
        }

        if (gchkSepaOutDto.getSeparatorCnt() == 0) {
            if ((gchkSepaOutDto.getGengo() == null || gchkSepaOutDto.getGengo().trim().length() == 0)
                    && date.length() > 8) {
                return result;
            }

            if ((gchkSepaOutDto.getGengo() != null && gchkSepaOutDto.getGengo().trim().length() > 0)
                    && date.length() > 7) {
                return result;
            }
        }

        if (gchkSepaOutDto.getGengo() != null && gchkSepaOutDto.getGengo().trim().length() == 1) {
            if (gchkSepaOutDto.getYear() > 0) {
                result = gchkSepaOutDto.getGengo()
                        + ("00" + gchkSepaOutDto.getYear()).substring(("00" + gchkSepaOutDto.getYear()).length() - 2);
            }
        } else {
            if (gchkSepaOutDto.getYear() > 0) {
                result = ("0000" + gchkSepaOutDto.getYear())
                        .substring(("0000" + gchkSepaOutDto.getYear()).length() - 4);
            }
        }
        if (gchkSepaOutDto.getMonth() > 0) {
            result += ("00" + gchkSepaOutDto.getMonth()).substring(("00" + gchkSepaOutDto.getMonth()).length() - 2);
        }
        if (gchkSepaOutDto.getDay() > 0) {
            result += ("00" + gchkSepaOutDto.getDay()).substring(("00" + gchkSepaOutDto.getDay()).length() - 2);
        }

        if (!(result != null && result.length() >= 3 && result.length() <= 8)) {
            // このケースあり得ないので、JUNITテストできません。
            result = null;
        }
        return result;
    }

    /**
     * 入力引数に月の最終日を返す
     * 
     * @param date 最終日を求めたい日付(YYYY/MM/DD)
     * @return 最終日(YYYY/MM/DD)
     */
    public String getTukimatu(String date) {
        // 入力値変換処理
        StringBuilder convertedDate = new StringBuilder(getCnvYymmdd(date));
        if (StringUtils.isEmpty(convertedDate)) {
            return null;
        }
        // 和暦→西暦変換
        StringBuilder seirekiDate = new StringBuilder();
        String warekiRtnStr = chkIswareki(convertedDate.toString());
        if (StringUtils.isEmpty(warekiRtnStr)) {
            seirekiDate = convertedDate;
        } else {
            convertedDate = new StringBuilder(warekiRtnStr);
            if (StringUtils.isNotEmpty(convertedDate.toString())) {
                if (StringUtils.isEmpty(retW2s(convertedDate.toString()))) {
                    return null;
                }
            } else {
                seirekiDate = convertedDate;
            }
        }
        // 年月日分解
        String dateStr = seirekiDate.toString();
        if (dateStr.length() < 8) {
            return null;
        }
        int year = Integer.parseInt(dateStr.substring(0, 4));
        int month = Integer.parseInt(dateStr.substring(4, 6));
        // 年チェック
        if (!chkDate2(String.valueOf(year))) {
            return null;
        }
        // 閏年チェック
        int[] adjustedDays = Arrays.copyOf(MONTH_DAYS, MONTH_DAYS.length);
        if (chkUru(year)) {
            adjustedDays[1] = LEAP_FEBRUARY_DAYS;
        }
        // 月末日生成
        String formattedDate = String.format(YEAR_FORMAT, year) + String.format(MONTH_DAY_FORMAT, month)
                + String.format(MONTH_DAY_FORMAT, adjustedDays[month - 1]);
        return formattedDate;
    }

    /**
     * 日付の書式変換 yyyyMM => yyyy/MM yyyyMMdd => yyyy/MM/dd
     * 
     * @param inputDate 日付
     * @return 変換失敗の場合、NULLを戻る
     */
    public String cnvYmd(String inputDate) {
        String result = null;
        // 入力値NULL
        if (inputDate == null) {
            return result;
        }

        // f_chksepa の戻り値 予想 ChkSepaOutDto
        SepaOutDto gchkSepaOutDto = chksepa(inputDate);
        if (gchkSepaOutDto == null) {
            return result;
        }

        if (gchkSepaOutDto.getSeparatorCnt() == INVALID_SEPA_CNT) {
            return result;
        }

        // 和暦 ★今回対応不要
        if (gchkSepaOutDto.getGengo() != null && gchkSepaOutDto.getGengo().trim().length() > 0) {
            return result;
        }

        String tempString = "";
        if (gchkSepaOutDto.getYear() > 0) {
            tempString = "0000" + gchkSepaOutDto.getYear();
            result = tempString.substring(tempString.length() - 4, tempString.length());
        } else {
            return result;
        }
        if (gchkSepaOutDto.getMonth() > 0) {
            tempString = "00" + gchkSepaOutDto.getMonth();
            result = result + "/" + tempString.substring(tempString.length() - 2, tempString.length());
        }
        if (gchkSepaOutDto.getDay() > 0) {
            tempString = "00" + gchkSepaOutDto.getDay();
            result = result + "/" + tempString.substring(tempString.length() - 2, tempString.length());
        }

        // case 3, 4, 6, 7, 9, 10
        // 和暦 例：H13
        // 和暦 例：H13/12
        // 和暦 例：H13/12/01
        if (result.length() == 3 || result.length() == 6 || result.length() == 9) {
            result = null;
            // YYYY
            // YYYY/MM
            // YYYY/MM/DD
        } else if (result.length() != 4 && result.length() != 7 && result.length() != 10) {
            result = null;
        }
        return result;
    }

    /**
     * 設定の書込（NEXのパソコン単位の設定）関数
     * 
     * @param inDto サービス入力Dto
     * @return パラメータ 1:正常 -1:不正
     */

    public Integer setF3gkProfile(F3gkSetProfileInDto inDto) {
        Integer liChk;
        Integer liRet;
        String lsSyscd;
        /** 職員ＩＤ */
        Integer shokuId = inDto.getShokuId();
        /** 法人ＩＤ */
        Integer houjinId = inDto.getHoujinId();
        /** 施設ＩＤ */
        Integer shisetuId = inDto.getShisetuId();
        /** 事業所ID */
        Integer svJigyoId = inDto.getSvJigyoId();
        /** 画面名 */
        String kinounameKnj = inDto.getKinounameKnj();
        /** セクション */
        String sectionKnj = inDto.getSectionKnj();
        /** キー */
        String keyKnj = inDto.getKeyKnj();
        /** パラメータ */
        String param = inDto.getParam();
        /** システムコード */
        String gsyscd = Objects.toString(inDto.getGsyscd(), "");

        // 文字列がスペースをクリアします
        lsSyscd = gsyscd.trim();
        kinounameKnj = kinounameKnj.trim();
        sectionKnj = sectionKnj.trim();
        keyKnj = keyKnj.trim();
        param = param.trim();

        liRet = PARAM_UNNORMAL;

        // 空ではありません
        if (Objects.isNull(shisetuId) || Objects.isNull(houjinId) || Objects.isNull(shisetuId)
                || Objects.isNull(svJigyoId) || StringUtil.isNullOrEmpty(lsSyscd)
                || StringUtil.isNullOrEmpty(kinounameKnj) || StringUtil.isNullOrEmpty(sectionKnj)
                || StringUtil.isNullOrEmpty(keyKnj) || StringUtil.isNullOrEmpty(param)) {
            // --パラメータ不正
            liRet = PARAM_UNNORMAL;
        } else {
            // >>パラメータ補正
            if (StringUtil.isNullOrEmpty(lsSyscd)) {
                lsSyscd = gsyscd;
            } else {
                lsSyscd = lsSyscd.toUpperCase();
            }
            kinounameKnj = kinounameKnj.toUpperCase();
            sectionKnj = sectionKnj.toUpperCase();
            keyKnj = keyKnj.toUpperCase();

            // DAOパラメータを作成
            SysCIniByCriteriaInEntity sysCIniByCriteriaInEntity = new SysCIniByCriteriaInEntity();
            sysCIniByCriteriaInEntity.setSysCd(lsSyscd);
            sysCIniByCriteriaInEntity.setShokuId(shokuId);
            sysCIniByCriteriaInEntity.setHoujinId(houjinId);
            sysCIniByCriteriaInEntity.setShisetuId(shisetuId);
            sysCIniByCriteriaInEntity.setSvJigyoId(svJigyoId);
            sysCIniByCriteriaInEntity.setKinounameKnj(kinounameKnj);
            sysCIniByCriteriaInEntity.setSectionKnj(sectionKnj);
            sysCIniByCriteriaInEntity.setKeyKnj(keyKnj);

            try {
                // DAOを実行
                List<SysCIniOutEntity> sysCIniOutList = this.comMocSysCIniSelectMapper
                        .findSysCIniByCriteria(sysCIniByCriteriaInEntity);

                liChk = sysCIniOutList.size();
                ComMocSysCIni comMocSysCIniRecord = new ComMocSysCIni();
                // >>挿入or上書クエリ作成
                if (liChk == 0) {
                    comMocSysCIniRecord.setSysCd(lsSyscd);
                    comMocSysCIniRecord.setShokuId(shokuId);
                    comMocSysCIniRecord.setHoujinId(houjinId);
                    comMocSysCIniRecord.setShisetuId(shisetuId);
                    comMocSysCIniRecord.setSvJigyoId(svJigyoId);
                    comMocSysCIniRecord.setKinounameKnj(kinounameKnj);
                    comMocSysCIniRecord.setSectionKnj(sectionKnj);
                    comMocSysCIniRecord.setKeyKnj(keyKnj);
                    comMocSysCIniRecord.setParamKnj(param);
                    this.comMocSysCIniMapper.insertSelective(comMocSysCIniRecord);
                    liRet = PARAM_NORMAL;
                } else {
                    Optional<SysCIniOutEntity> sysCIniOutOptional = sysCIniOutList.stream().findFirst();
                    if (sysCIniOutOptional.isPresent()) {

                        String asParam = sysCIniOutOptional.get().getParamKnj();
                        if (StringUtils.compare(param, asParam) != CommonConstants.NUMBER_ZERO) {
                            comMocSysCIniRecord.setParamKnj(param);
                            final ComMocSysCIniCriteria comMocSysCIniCriteria = new ComMocSysCIniCriteria();
                            comMocSysCIniCriteria.createCriteria().andSysCdEqualTo(lsSyscd).andShokuIdEqualTo(shokuId)
                                    .andHoujinIdEqualTo(houjinId).andShisetuIdEqualTo(shisetuId)
                                    .andSvJigyoIdEqualTo(svJigyoId).andKinounameKnjEqualTo(kinounameKnj)
                                    .andKeyKnjEqualTo(keyKnj).andSectionKnjEqualTo(sectionKnj);
                            Integer cnt = this.comMocSysCIniMapper.updateByCriteriaSelective(comMocSysCIniRecord,
                                    comMocSysCIniCriteria);
                            if (cnt == 0) {
                                throw new ExclusiveException();
                            }
                            liRet = PARAM_NORMAL;
                        } else {
                            // 変更なし
                            liRet = PARAM_NORMAL;
                        }
                    }
                }

            } catch (Exception e) {
                liRet = PARAM_UNNORMAL;
            }
        }
        return liRet;
    }

    /**
     * 職員ごと保存する設定項目の初期値を取得する関数
     * 
     * @param kinounameKnj 画面名
     * @param sectionKnj   セクション
     * @param keyKnj       キー
     * @param asDefault    初期値
     * @return パラメータ 1:正常 -1:不正
     */
    public String getF3gkProfileShokuDef(String kinounameKnj, String sectionKnj, String asDefault, String keyKnj) {

        String lsParam = null;

        kinounameKnj = kinounameKnj.trim();
        sectionKnj = sectionKnj.trim();
        keyKnj = keyKnj.trim();
        asDefault = asDefault.trim();

        if (StringUtil.isNullOrEmpty(sectionKnj) || StringUtil.isNullOrEmpty(keyKnj)) {
            // --パラメータ不正
            lsParam = asDefault;
        } else {
            // >>パラメータ補正
            sectionKnj = sectionKnj.toUpperCase();
            keyKnj = keyKnj.toUpperCase();

            // DAOパラメータを作成
            DefParamKnjByCriteriaInEntity defParamKnjByCriteriaInEntity = new DefParamKnjByCriteriaInEntity();
            defParamKnjByCriteriaInEntity.setAsKey(keyKnj);
            defParamKnjByCriteriaInEntity.setAsKinouname(kinounameKnj);
            defParamKnjByCriteriaInEntity.setAsSection(sectionKnj);

            List<DefParamKnjOutEntity> defParamKnjOutList = new ArrayList<DefParamKnjOutEntity>();

            try {
                // DAOを実行
                defParamKnjOutList = this.comMocSetteiShokuDefSelectMapper
                        .findDefParamKnjByCriteria(defParamKnjByCriteriaInEntity);
                Optional<DefParamKnjOutEntity> defParamKnjOutOptional = defParamKnjOutList.stream().findFirst();
                if (defParamKnjOutOptional.isPresent() && Objects.nonNull(defParamKnjOutOptional.get().getParamKnj())) {
                    lsParam = defParamKnjOutOptional.get().getParamKnj();
                } else {
                    // --登録値不正
                    lsParam = asDefault;
                }
            } catch (Exception e) {
                // --検索err
                lsParam = asDefault;
            }

        }

        return lsParam;
    }

    /**
     * 設定の読込（システム環境以外の設定）関数
     * 
     * @param inDto サービス入力Dto
     * @return パラメータ 1:正常 -1:不正
     */

    public String getF3gkProfile(F3gkGetProfileInDto inDto) {

        String lsParam = null;
        String lsSyscd;

        /** 職員ＩＤ */
        Integer shokuId = inDto.getShokuId();
        /** 法人ＩＤ */
        Integer houjinId = inDto.getHoujinId();
        /** 施設ＩＤ */
        Integer shisetuId = inDto.getShisetuId();
        /** 事業所ID */
        Integer svJigyoId = inDto.getSvJigyoId();
        /** 画面名 */
        String kinounameKnj = inDto.getKinounameKnj();
        /** セクション */
        String sectionKnj = inDto.getSectionKnj();
        /** キー */
        String keyKnj = inDto.getKeyKnj();
        /** パラメータ */
        String asDefault = inDto.getAsDefault();
        /** システムコード */
        String gsyscd = Objects.toString(inDto.getGsyscd(), "");
        /** 利用請求集計中フラグ */
        String gbRshuukei = inDto.getGbRshuukei();

        // 文字列がスペースをクリアします
        lsSyscd = StringUtil.isNullOrEmpty(gsyscd) ? gsyscd : gsyscd.trim();
        kinounameKnj = StringUtil.isNullOrEmpty(kinounameKnj) ? kinounameKnj : kinounameKnj.trim();
        sectionKnj = StringUtil.isNullOrEmpty(sectionKnj) ? sectionKnj : sectionKnj.trim();
        keyKnj = StringUtil.isNullOrEmpty(keyKnj) ? keyKnj : keyKnj.trim();
        asDefault = StringUtil.isNullOrEmpty(asDefault) ? asDefault : asDefault.trim();
        if (Objects.isNull(shokuId) || Objects.isNull(shisetuId) || Objects.isNull(houjinId)
                || Objects.isNull(svJigyoId) || StringUtil.isNullOrEmpty(sectionKnj)
                || StringUtil.isNullOrEmpty(keyKnj)) {
            // --パラメータ不正
            lsParam = asDefault;
        } else {
            // >>パラメータ補正
            if (StringUtil.isNullOrEmpty(lsSyscd)) {
                lsSyscd = GSYSCD;

            } else {
                lsSyscd = lsSyscd.toUpperCase();
            }
            sectionKnj = sectionKnj.toUpperCase();
            keyKnj = keyKnj.toUpperCase();
            kinounameKnj = kinounameKnj.toUpperCase();

            // DAOパラメータを作成
            ParamKnjByCriteriaInEntity paramKnjByCriteriaInEntity = new ParamKnjByCriteriaInEntity();
            paramKnjByCriteriaInEntity.setLsSyscd(lsSyscd);
            paramKnjByCriteriaInEntity.setAlShokuId(CommonDtoUtil.objValToString(shokuId));
            paramKnjByCriteriaInEntity.setAlHid(houjinId);
            paramKnjByCriteriaInEntity.setAlSid(shisetuId);
            paramKnjByCriteriaInEntity.setAlJid(svJigyoId);
            paramKnjByCriteriaInEntity.setAsKinouname(kinounameKnj);
            paramKnjByCriteriaInEntity.setAsSection(sectionKnj);
            paramKnjByCriteriaInEntity.setAsKey(keyKnj);

            // DAOを実行
            List<ParamKnjOutEntity> ParamKnjOutList = this.comMocSysCIniSelectMapper
                    .findParamKnjByCriteria(paramKnjByCriteriaInEntity);

            if (ParamKnjOutList.size() > 0) {
                lsParam = ParamKnjOutList.stream().findFirst().map(ParamKnjOutEntity::getParamKnj).orElse(asDefault);
            } else {
                // --検索err
                // 職員ごと保存の設定項目は初期値を別テーブルから取得する(デフォルト値の置き換え）
                // 前回最後に起動していた画面
                // 利用者リスト表示設定の保持
                if ((kinounameKnj.equals(COMMON) && sectionKnj.equals(DISPLAY_SETTING) && keyKnj.equals(ENTER_FLG))
                        || (kinounameKnj.equals(COMMON) && sectionKnj.equals(DISPLAY_SETTING) && keyKnj.equals(SCROLL))
                        || (kinounameKnj.equals(COMMON) && sectionKnj.equals(DISPLAY_SETTING) && keyKnj.equals(MENU))
                        || (kinounameKnj.equals(COMMON) && sectionKnj.equals(DISPLAY_SETTING)
                                && keyKnj.equals(LAST_MENU))
                        || (kinounameKnj.equals(COMMON) && sectionKnj.equals(USER_LIST)
                                && keyKnj.equals(INTER_FUNCTION_DISPLAY))
                        || (kinounameKnj.equals(SCHEDULE) && sectionKnj.equals(SDL_DEFAULT) && keyKnj.equals(WEEKSTART))
                        || (kinounameKnj.equals(SCHEDULE) && sectionKnj.equals(SDL_DEFAULT)
                                && keyKnj.equals(SGF_RENDOU))
                        || (kinounameKnj.equals(SCHEDULE) && sectionKnj.equals(SDL_DEFAULT)
                                && keyKnj.equals(TUT_SANTEI))
                        || (kinounameKnj.equals(SCHEDULE) && sectionKnj.equals(SDL_DEFAULT)
                                && keyKnj.equals(TUT_RENRAKU))
                        || (kinounameKnj.equals(SCHEDULE) && sectionKnj.equals(SDL_DEFAULT)
                                && keyKnj.equals(HKN_JYUUFUKU))
                        || (kinounameKnj.equals(KDS) && sectionKnj.equals(DENSO_SYSTEM) && keyKnj.equals(PATH))
                        || (kinounameKnj.equals(SCHEDULE) && sectionKnj.equals(SDL_DEFAULT)
                                && keyKnj.equals(HKN_SHOKUIN_CNT))) {
                    asDefault = this.getF3gkProfileShokuDef(kinounameKnj, sectionKnj, asDefault, keyKnj);
                }
                lsParam = asDefault;
                // 利用請求集計中フラグ true(1)：集計中 false(0)：集計中以外
                if (CommonConstants.STR_ZERO.equals(gbRshuukei)) {
                    F3gkSetProfileInDto f3gkSetProfileInDto = new F3gkSetProfileInDto();
                    f3gkSetProfileInDto.setShokuId(shokuId);
                    f3gkSetProfileInDto.setHoujinId(houjinId);
                    f3gkSetProfileInDto.setShisetuId(shisetuId);
                    f3gkSetProfileInDto.setSvJigyoId(svJigyoId);
                    f3gkSetProfileInDto.setKinounameKnj(kinounameKnj);
                    f3gkSetProfileInDto.setSectionKnj(sectionKnj);
                    f3gkSetProfileInDto.setKeyKnj(keyKnj);
                    f3gkSetProfileInDto.setParam(asDefault);
                    f3gkSetProfileInDto.setGsyscd(lsSyscd);
                    this.setF3gkProfile(f3gkSetProfileInDto);
                }
            }
        }

        return lsParam;
    }

    /**
     * 日付の差を計算する（PowerBuilderのf_DateDiff相当）
     * 
     * @param birth 誕生日 yyyy/MM/dd型
     * @param sdate 判断日 yyyy/MM/dd型
     * @return 年月日の差を得る
     */
    public DateDiffOutDto getDateDiff(String birth, String sdate) {
        String ymd1 = getChangeSeireki(birth);
        String ymd2 = getChangeSeireki(sdate);
        String y1Str = StringUtils.mid(ymd1, 0, 4);
        String m1Str = StringUtils.mid(ymd1, 4, 2);
        String d1Str = StringUtils.mid(ymd1, 6, 2);
        String y2Str = StringUtils.mid(ymd2, 0, 4);
        String m2Str = StringUtils.mid(ymd2, 4, 2);
        String d2Str = StringUtils.mid(ymd2, 6, 2);
        // 不正日付のチェック
        if (y1Str == null || m1Str == null || d1Str == null)
            return null;
        if (y2Str == null || m2Str == null || d2Str == null)
            return null;
        int y1 = Integer.valueOf(y1Str);
        int m1 = Integer.valueOf(m1Str);
        int d1 = Integer.valueOf(d1Str);
        int y2 = Integer.valueOf(y2Str);
        int m2 = Integer.valueOf(m2Str);
        int d2 = Integer.valueOf(d2Str);
        // 日付の期間を返す
        int[] lcLast = { 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31 };
        boolean lbLast1 = false;
        boolean lbLast2 = false;

        // 不正日付のチェック
        if (y1 <= 0 || m1 <= 0 || m1 > 12 || d1 <= 0)
            return null;
        if (y2 <= 0 || m2 <= 0 || m2 > 12 || d2 <= 0)
            return null;

        // 月末チェック
        if (chkUru(y1) && m1 == 2) {
            if (d1 == 29)
                lbLast1 = true;
        } else {
            if (d1 == lcLast[(int) m1 - 1])
                lbLast1 = true;
        }
        if (chkUru(y2) && m2 == 2) {
            if (d2 == 29)
                lbLast2 = true;
        } else {
            if (d2 == lcLast[(int) m2 - 1])
                lbLast2 = true;
        }

        // 両方とも月末か？
        if (lbLast1 && lbLast2) {
            if (m1 == 2 && m2 == 2 && d1 == 29 && d2 == 28) {
                d2 += 28;
                m2--;
            } else if (m1 == 2 && m2 == 2 && d1 == 28 && d2 == 29) {
                // 何もしない（toは29日のまま）
            } else {
                d2 = d1;
            }
        } else {
            if (d1 > d2) {
                m2--;
                if (m2 < 1) {
                    y2--;
                    m2 = 12;
                }
                if (y2 < 1) {
                    return null; // from > to
                }
                d2 = d2 + lcLast[(int) m1 - 1];
                if (chkUru(y1) && m1 == 2) {
                    d2++;
                }
            }
        }

        if (m1 > m2) {
            y2--;
            m2 = m2 + 12;
        }
        if (y1 > y2) {
            return null; // from > to
        }

        int y3 = (int) (y2 - y1);
        int m3 = (int) (m2 - m1);
        int d3 = (int) (d2 - d1);

        if (y3 <= 0 && m3 <= 0 && d3 <= 0) {
            return new DateDiffOutDto(0, 0, 0);
        }

        return new DateDiffOutDto(y3, m3, d3);
    }

    /**
     * 年月の差を返す（年齢）（PowerBuilderのf_getageym相当）
     * 
     * @param birthDate  誕生日 yyyy/MM/dd型
     * @param targetDate 現在日付 yyyy/MM/dd型
     * @return 年齢フォーマット文字列
     */
    public AgeYmOutDto getAgeYm(String birthDate, String targetDate) {
        birthDate = convertFormat(birthDate);
        targetDate = convertFormat(targetDate);
        DateDiffOutDto diff = getDateDiff(birthDate, targetDate);
        return new AgeYmOutDto(diff.getY3(), diff.getM3());
    }

    /**
     * yyyy/MM/dd形式に変換
     * 
     * @param date 日付
     * @return 日付(yyyy/MM/dd)
     */
    private String convertFormat(String date) {
        // まずyyyy/MM/dd形式として解析を試みる
        try {
            // 正しい形式の場合は変換せずに返す
            LocalDate.parse(date, DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD));
            return date;
        } catch (DateTimeParseException e1) {
            // 基本ISO形式(yyyyMMdd)で解析を試みる
            try {
                LocalDate retDate = LocalDate.parse(date, DateTimeFormatter.BASIC_ISO_DATE);
                // yyyy/MM/dd形式に変換
                return retDate.format(DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD));
            } catch (DateTimeParseException e2) {
                // 両方の形式で失敗した場合は例外をスロー
                throw new IllegalArgumentException("無効な日付形式: " + date);
            }
        }
    }

    /**
     * 年の差を得る（PowerBuilderのf_getdatediffyy相当）
     * 
     * @param birthDate  生年月日 yyyy/MM/dd型
     * @param targetDate アセスメント実施日 yyyy/MM/dd型
     * @return 年の差
     */
    public Integer getDateDiffYY(String birthDate, String targetDate) {
        DateDiffOutDto diff = getDateDiff(birthDate, targetDate);
        return diff.getY3();
    }

    /**
     * 職員ID指定して、設定(職員単位)の参照
     * 
     * @param shokuId 職員ID
     * @param sys     システム略
     * @param section セクション名
     * @param key     キー名
     * @param def     初期値
     * @return 参照値
     */
    public String getProfileSBase(Integer shokuId, String sys, String section, String key, String def) {
        String param = StringUtils.EMPTY;

        sys = StringUtils.trim(sys);
        section = StringUtils.trim(section);
        key = StringUtils.trim(key);

        if (shokuId == null || StringUtils.isEmpty(section) || StringUtils.isEmpty(key)) {
            // パラメータ不正
            param = def;
        } else {
            // パラメータ補正
            if (StringUtils.isEmpty(sys)) {
                sys = SYS_NDS;
            } else {
                sys = sys.toUpperCase();
            }
            section = section.toUpperCase();
            key = key.toUpperCase();

            // 検索条件の設定
            ComMocSysiniSCriteria criteria = new ComMocSysiniSCriteria();
            criteria.createCriteria().andIdEqualTo(shokuId);
            List<ComMocSysiniS> resultList = comMocSysiniSMapper.selectByCriteria(criteria);

            List<ComMocSysiniS> comMocSysiniSList = new ArrayList<>();
            for (ComMocSysiniS comMocSysiniS : resultList) {
                if (StringUtils.defaultIfEmpty(comMocSysiniS.getSysRyaku(), StringUtils.EMPTY).toUpperCase().equals(sys)
                        && StringUtils.defaultIfEmpty(comMocSysiniS.getSectionName(), StringUtils.EMPTY).toUpperCase()
                                .equals(section)
                        && StringUtils.defaultIfEmpty(comMocSysiniS.getKeyName(), StringUtils.EMPTY).toUpperCase()
                                .equals(key)) {
                    comMocSysiniSList.add(comMocSysiniS);
                }
            }
            if (CollectionUtils.isEmpty(comMocSysiniSList)) {
                // 検索err
                param = def;
                // 初期値の書込
                setProfileSBase(shokuId, sys, section, key, param);
            } else {
                // 正常参照
                param = comMocSysiniSList.getFirst().getParam();
                if (param == null) {
                    // 登録値不正
                    param = def;
                }
            }
        }
        return param;
    }

    /**
     * 職員ID指定して、設定(職員単位)の書込
     * 
     * @param shokuId 職員ID
     * @param sys     システム略
     * @param section セクション名
     * @param key     キー名
     * @param param   パラメータ
     * @return 運行結果 1:正常 2:不正
     */
    public Integer setProfileSBase(Integer shokuId, String sys, String section, String key, String param) {
        Integer result = 0;

        sys = StringUtils.trim(sys);
        section = StringUtils.trim(section);
        key = StringUtils.trim(key);
        param = StringUtils.trim(param);
        if (shokuId == null || StringUtils.isEmpty(section) || StringUtils.isEmpty(key) || StringUtils.isEmpty(param)) {
            // パラメータ不正
            result = 2;
        } else {
            // パラメータ補正
            if (StringUtils.isEmpty(sys)) {
                sys = SYS_NDS;
            } else {
                sys = sys.toUpperCase();
            }
            section = section.toUpperCase();
            key = key.toUpperCase();

            // 検索条件の設定
            ComMocSysiniSCriteria criteria = new ComMocSysiniSCriteria();
            criteria.createCriteria().andIdEqualTo(shokuId);
            List<ComMocSysiniS> resultList = comMocSysiniSMapper.selectByCriteria(criteria);

            // レコードの存在確認
            List<ComMocSysiniS> comMocSysiniSList = new ArrayList<>();
            for (ComMocSysiniS comMocSysiniS : resultList) {
                if (StringUtils.defaultIfEmpty(comMocSysiniS.getSysRyaku(), StringUtils.EMPTY).toUpperCase().equals(sys)
                        && StringUtils.defaultIfEmpty(comMocSysiniS.getSectionName(), StringUtils.EMPTY).toUpperCase()
                                .equals(section)
                        && StringUtils.defaultIfEmpty(comMocSysiniS.getKeyName(), StringUtils.EMPTY).toUpperCase()
                                .equals(key)) {
                    comMocSysiniSList.add(comMocSysiniS);
                }
            }
            Integer count = 0;
            if (CollectionUtils.isEmpty(comMocSysiniSList)) {
                ComMocSysiniS insertComMocSysiniS = new ComMocSysiniS();
                insertComMocSysiniS.setId(shokuId);
                insertComMocSysiniS.setSysRyaku(sys);
                insertComMocSysiniS.setSectionName(section);
                insertComMocSysiniS.setKeyName(key);
                insertComMocSysiniS.setParam(param);
                count = comMocSysiniSMapper.insertSelective(insertComMocSysiniS);
            } else {
                for (ComMocSysiniS comMocSysiniS : comMocSysiniSList) {
                    comMocSysiniS.setParam(param);
                    count += comMocSysiniSMapper.updateByPrimaryKeySelective(comMocSysiniS);
                }
            }
            if (count == 0) {
                result = 2;
            } else {
                result = 1;
            }
        }
        return result;
    }

    /**
     * 設定(職員単位)の参照
     * 
     * @param shokuId 職員ID
     * @param sys     システム略
     * @param section セクション名
     * @param key     キー名
     * @param param   パラメータ
     * @return 設定(職員単位)の参照
     */
    public String getProfileS(Integer shokuinId, String sysRyaku, String section, String key, String def) {

        return getProfileSBase(shokuinId, sysRyaku, section, key, def);
    }

    /**
     * 設定(職員単位)の書込み
     * 
     * @param shokuId 職員ID
     * @param sys     システム略
     * @param section セクション名
     * @param key     キー名
     * @param param   パラメータ
     * @return 運行結果 1:正常 2:不正
     */
    public Integer setProfileS(Integer shokuinId, String sysRyaku, String section, String key, String def) {

        return setProfileSBase(shokuinId, sysRyaku, section, key, def);
    }

    /**
     * 設定の読込（システム環境以外の設定）（初期値書き込み無し用）
     * 
     * @param inDto 設定の読込（システム環境以外の設定）の入力Dto
     * @return 設定値を返す
     */
    public String getProfile2(GetProfile2InDto inDto) {
        String paramValue = null;
        String sysCd = StringUtils.trim(inDto.getGsyscd());
        String kinouName = StringUtils.trim(inDto.getKinouName());
        String section = StringUtils.trim(inDto.getSection());
        String key = StringUtils.trim(inDto.getKey());
        String defValue = StringUtils.trim(inDto.getDefValue());

        if (inDto.getShokuId() == null || inDto.getHid() == null || inDto.getSid() == null || inDto.getJid() == null
                || StringUtils.isEmpty(section) || StringUtils.isEmpty(key)) {
            // パラメータ不正
            paramValue = defValue;
        } else {
            // パラメータ補正
            if (StringUtils.isEmpty(sysCd)) {
                sysCd = GSYSCD;
            } else {
                sysCd = sysCd.toUpperCase();
            }
            section = section.toUpperCase();
            key = key.toUpperCase();

            // DAOパラメータを作成
            ParamKnjByCriteriaInEntity criteria = new ParamKnjByCriteriaInEntity();
            criteria.setAlShokuId(CommonDtoUtil.objValToString(inDto.getShokuId()));
            criteria.setAlHid(inDto.getHid());
            criteria.setAlSid(inDto.getSid());
            criteria.setAlJid(inDto.getJid());
            criteria.setAsKinouname(kinouName);
            criteria.setAsSection(section);
            criteria.setAsKey(key);

            // DAOを実行
            List<ParamKnjOutEntity> ParamKnjOutList = this.comMocSysCIniSelectMapper.findParamKnjByCriteria(criteria);

            if (CollectionUtils.isNotEmpty(ParamKnjOutList)) {
                paramValue = ParamKnjOutList.getFirst().getParamKnj();
            }

            if (StringUtils.isEmpty(paramValue)) {
                // 登録値不正
                paramValue = defValue;
            }
        }

        return paramValue;
    }

    /**
     * 文字列を大文字に変換するメソッド
     * 
     * @param inputString 変換対象文字列
     * @return 変換後の大文字文字列
     */
    public String fNdSUpper(final String inputString) {
        if (inputString == null) {
            return null;
        }
        final StringBuilder resultBuilder = new StringBuilder(inputString.length());
        for (final char currentChar : inputString.toCharArray()) {
            final int asciiValue = (int) currentChar;
            // 小文字判定と変換処理
            if (asciiValue >= LOWER_CASE_A_ASCII && asciiValue <= LOWER_CASE_Z_ASCII) {
                final char upperChar = (char) (asciiValue - CASE_CONVERSION_DIFFERENCE);
                resultBuilder.append(upperChar);
            } else {
                resultBuilder.append(currentChar);
            }
        }
        return resultBuilder.toString();
    }

    /**
     * 年月日を日本語元号付きの年月日に変換
     * 
     * @param mode    0: ゼロ埋め形式（平成09年）, 1: スペース形式（平成 9年）
     * @param dateYmd 入力日付 (YYYY/MM/DD や YYYY-MM-DD などの形式)
     * @return 形式化された日付文字列、失敗した場合は空文字列
     */
    public String get2Gengouj(int mode, String dateYmd) {
        // 1. 和暦形式への変換
        String normalizedDate = dateYmd.replaceAll("[^0-9]", "");
        String warekiDate = convertToWareki(normalizedDate);
        if (warekiDate == null || warekiDate.isEmpty()) {
            return "";
        }

        // 2. 年号の取得
        char eraCode = warekiDate.charAt(0);
        String eraName = ERA_MAP.get(eraCode);
        if (eraName == null) {
            return "";
        }

        // 3. 年月日の抽出
        String year = warekiDate.length() == 2 ? warekiDate.substring(1, 2)
                : warekiDate.length() == 3 ? warekiDate.substring(1, 3) : "";
        String month = normalizedDate.length() == 6 || normalizedDate.length() == 8 ? normalizedDate.substring(4, 6)
                : "";
        String day = normalizedDate.length() == 8 ? normalizedDate.substring(6, 8) : "";

        // 4. フォーマットパターンの処理
        if (mode == 1) {
            if (warekiDate != null && warekiDate.length() == 2) {
                if (Integer.valueOf(year) < 10) {
                    year = " " + year;
                }
            }
            month = formatSingleDigit(month);
            day = formatSingleDigit(day);
        }

        // 5. 文字列年月日
        StringBuilder result = new StringBuilder(eraName);
        if (!year.isEmpty()) {
            result.append(year).append("年");
            if (!month.isEmpty()) {
                result.append(month).append("月");
                if (!day.isEmpty()) {
                    result.append(day).append("日");
                }
            }
        }

        return result.toString();
    }

    private String convertToWareki(String normalized) {
        // 実際の実装または既存ライブラリの呼び出し
        if (normalized != null && !normalized.isEmpty()) {
            if (normalized.length() < 8)
                return "";

            int year = Integer.parseInt(normalized.substring(0, 4));
            if (year >= 2019)
                return "R" + (year - 2018); // 令和
            else if (year >= 1989)
                return "H" + (year - 1988); // 平成
            else if (year >= 1926)
                return "S" + (year - 1925); // 昭和
            else if (year >= 1912)
                return "T" + (year - 1911); // 大正
            else if (year >= 1868)
                return "M" + (year - 1867); // 明治
            return "";
        } else {
            return "";
        }
    }

    private String formatSingleDigit(String numStr) {
        if (numStr != null && numStr.length() == 2 && numStr.startsWith("0")) {
            return " " + numStr.charAt(1);
        }
        return numStr;
    }

    /**
     * 年号マスタから取得した年号をグローバル変数に移送する
     * 
     * @return 年号出力 Dto
     */
    public GengouInfoExpandOutDto gengouInfoExpand() {
        GengouInfoExpandOutDto outDto = new GengouInfoExpandOutDto();
        // gs_gengou3
        ComMocGengouCriteria criteria = new ComMocGengouCriteria();
        // SORT
        criteria.setOrderByClause(COM_MOC_GENGOU_SORT);
        List<ComMocGengou> comMocGengouList = comMocGengouMapper.selectByCriteria(criteria);
        /** 元号１ */
        List<String> gsGengou1List = new ArrayList<>();
        /** 元号２ */
        List<String> gsGengou2List = new ArrayList<>();
        /** 元号３ */
        List<String> gsGengou3List = new ArrayList<>();
        /** 元号開始年 */
        List<Integer> giGengouYyyyList = new ArrayList<>();
        /** 元号開始月 */
        List<Integer> giGengouMmList = new ArrayList<>();
        /** 元号開始日 */
        List<Integer> giGengouDdList = new ArrayList<>();
        /** 元号コード */
        List<String> giGengouCdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(comMocGengouList)) {
            for (ComMocGengou gengou : comMocGengouList) {
                gsGengou1List.add(gengou.getGengou1Knj());
                gsGengou2List.add(gengou.getGengou2Knj());
                gsGengou3List.add(gengou.getGengou3());
                giGengouYyyyList.add(gengou.getYyyy());
                giGengouMmList.add(gengou.getMm());
                giGengouDdList.add(gengou.getDd());
                giGengouCdList.add(gengou.getGengouCd());
            }
        }
        outDto.setGsGengou1List(gsGengou1List);
        outDto.setGsGengou2List(gsGengou2List);
        outDto.setGsGengou3List(gsGengou3List);
        outDto.setGiGengouYyyyList(giGengouYyyyList);
        outDto.setGiGengouMmList(giGengouMmList);
        outDto.setGiGengouDdList(giGengouDdList);
        outDto.setGiGengouCdList(giGengouCdList);
        return outDto;
    }

    /**
     * あらゆる日付を日本語元号付き日付に変換する
     * 
     * @param asDate   和暦 or 西暦
     * @param asWareki 元号ＹＹ年ＭＭ月ＤＤ日
     * @param mode     数字の表現 0: 09年09月09日 1: 9年 9月 9日
     * @return 年号出力 Dto
     */
    public String get2Warekij(String asDate, int mode) {
        String asWareki = CommonConstants.BLANK_STRING;
        // 文字列の適正判断
        if (StringUtils.isEmpty(asDate) || asDate.length() > CommonConstants.INT_11) {
            return asWareki;
        }
        // 最初の２バイトをとり、それが元号の場合、処理せず
        String lsGen = asDate.substring(0, 2);
        int liCnt = asDate.indexOf(CommonConstants.STRING_YEAR);
        int liCnt2 = asDate.indexOf(CommonConstants.STRING_MONTH);
        int liCnt3 = asDate.indexOf(CommonConstants.STRING_DAY);

        // 元号変換
        switch (lsGen) {
        case CommonConstants.STR_MEIJI:
        case CommonConstants.STR_TAISHO:
        case CommonConstants.STR_SHOWA:
        case CommonConstants.STR_HEISEI:
            if (mode == CommonConstants.INT_0) {
                asWareki = lsGen + String.format("%02d", CommonDtoUtil.strValToInt(asDate.substring(2, liCnt).trim()))
                        + CommonConstants.STRING_YEAR;
                if (liCnt2 > 0) {
                    asWareki = asWareki
                            + String.format("%02d",
                                    CommonDtoUtil.strValToInt(asDate.substring(liCnt + 1, liCnt2).trim()))
                            + CommonConstants.STRING_MONTH;
                }
                if (liCnt2 > 0) {
                    asWareki = asWareki
                            + String.format("%02d",
                                    CommonDtoUtil.strValToInt(asDate.substring(liCnt2 + 1, liCnt3).trim()))
                            + CommonConstants.STRING_DAY;
                }
            } else {
                asWareki = lsGen + String.format("%2d", CommonDtoUtil.strValToInt(asDate.substring(2, liCnt).trim()))
                        + CommonConstants.STRING_YEAR;
                if (liCnt2 > 0) {
                    asWareki = asWareki
                            + String.format("%2d",
                                    CommonDtoUtil.strValToInt(asDate.substring(liCnt + 1, liCnt2).trim()))
                            + CommonConstants.STRING_MONTH;
                }
                if (liCnt2 > 0) {
                    asWareki = asWareki
                            + String.format("%2d",
                                    CommonDtoUtil.strValToInt(asDate.substring(liCnt2 + 1, liCnt3).trim()))
                            + CommonConstants.STRING_DAY;
                }
            }
            return asWareki;
        default:
            // 年号マスタから取得した年号をグローバル変数に移送する
            GengouInfoExpandOutDto outDto = this.gengouInfoExpand();
            /** 元号１ */
            List<String> gsGengou1List = outDto.getGsGengou1List();
            /** 元号２ */
            List<String> gsGengou2List = outDto.getGsGengou2List();
            for (int i = 0; i < gsGengou1List.size(); i++) {
                if (lsGen.equals(gsGengou2List.get(i))) {
                    if (mode == CommonConstants.INT_0) {
                        asWareki = lsGen
                                + String.format("%02d", CommonDtoUtil.strValToInt(asDate.substring(2, liCnt).trim()))
                                + CommonConstants.STRING_YEAR;
                        if (liCnt2 > 0) {
                            asWareki = asWareki
                                    + String.format("%02d",
                                            CommonDtoUtil.strValToInt(asDate.substring(liCnt + 1, liCnt2).trim()))
                                    + CommonConstants.STRING_MONTH;
                        }
                        if (liCnt2 > 0) {
                            asWareki = asWareki
                                    + String.format("%02d",
                                            CommonDtoUtil.strValToInt(asDate.substring(liCnt2 + 1, liCnt3).trim()))
                                    + CommonConstants.STRING_DAY;
                        }
                    } else {
                        asWareki = lsGen
                                + String.format("%2d", CommonDtoUtil.strValToInt(asDate.substring(2, liCnt).trim()))
                                + CommonConstants.STRING_YEAR;
                        if (liCnt2 > 0) {
                            asWareki = asWareki
                                    + String.format("%2d",
                                            CommonDtoUtil.strValToInt(asDate.substring(liCnt + 1, liCnt2).trim()))
                                    + CommonConstants.STRING_MONTH;
                        }
                        if (liCnt2 > 0) {
                            asWareki = asWareki
                                    + String.format("%2d",
                                            CommonDtoUtil.strValToInt(asDate.substring(liCnt2 + 1, liCnt3).trim()))
                                    + CommonConstants.STRING_DAY;
                        }
                    }
                    return asWareki;
                }
            }
        }

        String lsSeireki = CommonConstants.BLANK_STRING;
        // 入力引数(as_date)のチェックと"/"・"-"・"."文字の削除
        String lsDate = this.getCnvYymmdd(asDate);
        if (StringUtils.isEmpty(lsDate)) {
            return asWareki;
        }

        lsSeireki = lsDate; // 和暦ではい場合
        // 和暦チェック
        lsDate = this.chkIswareki(lsDate);
        if (StringUtils.isNotEmpty(lsDate)) {
            // 和暦→西暦変換
            lsSeireki = this.retW2s(lsDate);
            if (StringUtils.isEmpty(lsSeireki)) {
                return asWareki;
            }
        }

        // 和暦に変換
        ChkDateOutDto chkDto = this.chkDate(lsSeireki);
        if (chkDto.isResult()) {
            // 西暦→和暦変換(年月日)
            String lsWareki = this.retS2w(lsSeireki);
            if (StringUtils.isEmpty(lsWareki)) {
                return asWareki;
            }
            // 日付の書式変換
            lsDate = this.cnvYmd2(lsWareki);
            if (StringUtils.isEmpty(lsDate)) {
                return asWareki;
            }
            // Gyy/mm/dd形式 -> 元号YY年MM月DD日変換
            lsDate = this.cnv2zenh(lsDate, mode);
            asWareki = lsDate;
        } else {
            return asWareki;
        }
        return asWareki;
    }

    /**
     * 西暦→和暦変換(年月日)
     * 
     * @param asSeireki yyyymmdd
     * @return 和暦
     */
    public String retS2w(String asSeireki) {
        String asWareki = null;
        String lsSeireki = asSeireki;
        ChkDateOutDto chkDto = chkDate(lsSeireki);
        if (StringUtils.isEmpty(chkDto.getDate())) {
            return asWareki;
        }
        // li_yy = long(Mid(as_seireki,1,4))
        int liYY = CommonDtoUtil.strValToInt(asSeireki.substring(0, 4).trim());
        // li_mm = long(Mid(as_seireki,5,2))
        int liMM = CommonConstants.INT_0;
        if (asSeireki.length() > CommonConstants.INT_4) {
            liMM = CommonDtoUtil.strValToInt(asSeireki.substring(4, 6).trim());
        }
        // li_dd = long(Mid(as_seireki,7,2))
        int liDD = CommonConstants.INT_0;
        if (asSeireki.length() > CommonConstants.INT_6) {
            liDD = CommonDtoUtil.strValToInt(asSeireki.substring(6, 8).trim());
        }

        switch (asSeireki.length()) {
        case CommonConstants.INT_4:
            lsSeireki = String.format("%04d", liYY) + "/12/31";
            asWareki = this.stringDateWareki(lsSeireki, "Gnn");
            break;
        case CommonConstants.INT_6:
            lsSeireki = String.format("%04d", liYY) + "/" + String.format("%02d", liMM) + "/28";
            asWareki = this.stringDateWareki(lsSeireki, "Gnnmm");
            break;
        case CommonConstants.INT_8:
            lsSeireki = String.format("%04d", liYY) + "/" + String.format("%02d", liMM) + "/"
                    + String.format("%02d", liDD);
            asWareki = this.stringDateWareki(lsSeireki, "Gnnmmdd");
            break;
        default:
            asWareki = CommonConstants.BLANK_STRING;
            break;
        }
        return asWareki;
    }

    /**
     * 半角日付（和暦） -> 全角日付変更（和暦）
     * 
     * @param asDate (Gyy/mm/dd)
     * @return 全角日付
     */
    public String cnv2zenh(String asDate, int mode) {
        String lsGen = CommonConstants.BLANK_STRING;
        String lsYear = CommonConstants.BLANK_STRING;
        String lsMonth = CommonConstants.BLANK_STRING;
        String lsDay = CommonConstants.BLANK_STRING;

        switch (asDate.length()) {
        case CommonConstants.INT_3:
            lsGen = asDate.substring(CommonConstants.INT_0, CommonConstants.INT_1).trim();
            lsYear = asDate.substring(CommonConstants.INT_1, CommonConstants.INT_3).trim();
            break;
        case CommonConstants.INT_6:
            lsGen = asDate.substring(CommonConstants.INT_0, CommonConstants.INT_1).trim();
            lsYear = asDate.substring(CommonConstants.INT_1, CommonConstants.INT_3).trim();
            lsMonth = asDate.substring(CommonConstants.INT_4, CommonConstants.INT_6).trim();
            break;
        case CommonConstants.INT_9:
            lsGen = asDate.substring(CommonConstants.INT_0, CommonConstants.INT_1).trim();
            lsYear = asDate.substring(CommonConstants.INT_1, CommonConstants.INT_3).trim();
            lsMonth = asDate.substring(CommonConstants.INT_4, CommonConstants.INT_6).trim();
            lsDay = asDate.substring(CommonConstants.INT_7, CommonConstants.INT_9).trim();
            break;
        default:
            return null;
        }

        if (mode == 1) {
            lsYear = String.format("%2d", CommonDtoUtil.strValToInt(lsYear));
            lsMonth = String.format("%2d", CommonDtoUtil.strValToInt(lsMonth));
            lsDay = String.format("%2d", CommonDtoUtil.strValToInt(lsDay));
        }

        switch (lsGen) {
        case CommonConstants.STR_MEIJI_RYAKU:
            lsGen = CommonConstants.STR_MEIJI;
            break;
        case CommonConstants.STR_TAISHO_RYAKU:
            lsGen = CommonConstants.STR_TAISHO;
            break;
        case CommonConstants.STR_SHOWA_RYAKU:
            lsGen = CommonConstants.STR_SHOWA;
            break;
        case CommonConstants.STR_HEISEI_RYAKU:
            lsGen = CommonConstants.STR_HEISEI;
            break;
        default:
            // 年号マスタから取得した年号をグローバル変数に移送する
            GengouInfoExpandOutDto outDto = this.gengouInfoExpand();
            /** 元号２ */
            List<String> gsGengou2List = outDto.getGsGengou2List();
            /** 元号３ */
            List<String> gsGengou3List = outDto.getGsGengou3List();
            Boolean isExist = Boolean.FALSE;
            for (int i = 0; 1 < gsGengou3List.size(); i++) {
                if (lsGen.equals(gsGengou3List.get(i))) {
                    lsGen = gsGengou2List.get(i);
                    isExist = Boolean.TRUE;
                    break;
                }
            }
            if (!isExist) {
                lsGen = CommonConstants.QUESTION_MARK_4;
            }
            break;
        }

        String asZen = CommonConstants.BLANK_STRING;
        switch (asDate.length()) {
        case CommonConstants.INT_3:
            asZen = lsGen + lsYear + CommonConstants.STRING_YEAR;
            break;
        case CommonConstants.INT_6:
            asZen = lsGen + lsYear + CommonConstants.STRING_YEAR + lsMonth + CommonConstants.STRING_MONTH;
            break;
        case CommonConstants.INT_9:
            asZen = lsGen + lsYear + CommonConstants.STRING_YEAR + lsMonth + CommonConstants.STRING_MONTH + lsDay
                    + CommonConstants.STRING_DAY;
            break;
        default:
            return null;
        }
        return asZen;
    }

    /**
     * 日付の書式変換 yyyyMM => yyyy/MM yyyyMMdd => yyyy/MM/dd
     * 
     * @param inputDate 日付
     * @return 変換失敗の場合、NULLを戻る
     */
    public String cnvYmd2(String inputDate) {
        String result = null;
        // 入力値NULL
        if (inputDate == null) {
            return result;
        }

        // f_chksepa の戻り値 予想 ChkSepaOutDto
        SepaOutDto gchkSepaOutDto = chksepa(inputDate);
        if (gchkSepaOutDto == null) {
            return result;
        }

        if (gchkSepaOutDto.getSeparatorCnt() == INVALID_SEPA_CNT) {
            return result;
        }

        if (gchkSepaOutDto.getGengo().length() == CommonConstants.INT_1) {
            if (gchkSepaOutDto.getYear() > 0) {
                result = gchkSepaOutDto.getGengo() + String.format("%02d", gchkSepaOutDto.getYear());
            }
        } else {
            if (gchkSepaOutDto.getYear() > 0) {
                result = String.format("%04d", gchkSepaOutDto.getYear());
            }
        }

        if (gchkSepaOutDto.getMonth() > 0) {
            result = result + "/" + String.format("%02d", gchkSepaOutDto.getMonth());
        }
        if (gchkSepaOutDto.getDay() > 0) {
            result = result + "/" + String.format("%02d", gchkSepaOutDto.getDay());
        }

        // case 3, 4, 6, 7, 9, 10
        // 和暦 例：H13
        // 和暦 例：H13/12
        // 和暦 例：H13/12/01
        if (result != null) {
            switch (result.length()) {
            case CommonConstants.INT_3:
            case CommonConstants.INT_4:
            case CommonConstants.INT_6:
            case CommonConstants.INT_7:
            case CommonConstants.INT_9:
            case CommonConstants.INT_10:
                break;
            default:
                result = null;
                break;
            }
        }
        return result;
    }

    /**
     * 文字列の右側から指定された長さの部分文字列を抽出する。
     * 
     * @param str 文字列
     * @param len 指定された長さ
     * @return 抽出後文字列
     * @throws Exception 例外
     */
    public String getNdsRighta(String str, Integer len) throws Exception {
        byte[] bytes = str.getBytes(CommonConstants.Shift_JIS);

        if (bytes.length <= len) {
            return str;
        }

        byte[] subBytes = new byte[len];
        System.arraycopy(bytes, bytes.length - len, subBytes, 0, len);

        return new String(subBytes, CommonConstants.Shift_JIS);
    }

    /**
     * 開始・終了の期間の日数を計算する
     * 
     * @param ymdF 開始日(yyyy/MM/dd)
     * @param ymdT 終了日(yyyy/MM/dd)
     * @return 日数
     */
    public static Integer calcNissuu(String ymdF, String ymdT) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        LocalDate dateFrom = LocalDate.parse(ymdF, formatter);
        LocalDate dateTo = LocalDate.parse(ymdT, formatter);

        // 開始・終了の期間の日数を返す
        return CommonDtoUtil.strValToInt(CommonDtoUtil.objValToString(ChronoUnit.DAYS.between(dateFrom, dateTo)));
    }

    /**
     * 半角カタカナ ->全角平仮名 変換
     * 
     * @param astrZen 変換前全角数字文字列
     * @return 変換後半角数字文字列
     */
    public String han2zenKana(String astrZen) {
        // 全角ひらがな
        String zenh[] = { "　", "あ", "い", "う", "え", "お", "ぁ", "ぃ", "ぅ", "ぇ", "ぉ", "か", "き", "く", "け", "こ", "が", "ぎ", "ぐ",
                "げ", "ご", "さ", "し", "す", "せ", "そ", "ざ", "じ", "ず", "ぜ", "ぞ", "た", "ち", "つ", "て", "と", "だ", "ぢ", "づ", "で",
                "ど", "っ", "な", "に", "ぬ", "ね", "の", "は", "ひ", "ふ", "へ", "ほ", "ば", "び", "ぶ", "べ", "ぼ", "ぱ", "ぴ", "ぷ", "ぺ",
                "ぽ", "ま", "み", "む", "め", "も", "や", "ゆ", "よ", "ゃ", "ゅ", "ょ", "ら", "り", "る", "れ", "ろ", "わ", "を", "ん", "ゎ",
                "ゐ", "ゑ", "ー", "ﾟ", "ﾞ" };
        // 全角カタカナ
        String zenk[] = { "　", "ア", "イ", "ウ", "エ", "オ", "ァ", "ィ", "ゥ", "ェ", "ォ", "カ", "キ", "ク", "ケ", "コ", "ガ", "ギ", "グ",
                "ゲ", "ゴ", "サ", "シ", "ス", "セ", "ソ", "ザ", "ジ", "ズ", "ゼ", "ゾ", "タ", "チ", "ツ", "テ", "ト", "ダ", "ヂ", "ヅ", "デ",
                "ド", "ッ", "ナ", "ニ", "ヌ", "ネ", "ノ", "ハ", "ヒ", "フ", "ヘ", "ホ", "バ", "ビ", "ブ", "ベ", "ボ", "パ", "ピ", "プ", "ペ",
                "ポ", "マ", "ミ", "ム", "メ", "モ", "ヤ", "ユ", "ヨ", "ャ", "ュ", "ョ", "ラ", "リ", "ル", "レ", "ロ", "ワ", "ヲ", "ン", "ヮ",
                "ヰ", "ヱ", "ー", "ﾟ", "ﾞ" };

        // 半角カタカナ
        String hank[] = { " ", "ｱ", "ｲ", "ｳ", "ｴ", "ｵ", "ｧ", "ｨ", "ｩ", "ｪ", "ｫ", "ｶ", "ｷ", "ｸ", "ｹ", "ｺ", "ｶﾞ", "ｷﾞ",
                "ｸﾞ", "ｹﾞ", "ｺﾞ", "ｻ", "ｼ", "ｽ", "ｾ", "ｿ", "ｻﾞ", "ｼﾞ", "ｽﾞ", "ｾﾞ", "ｿﾞ", "ﾀ", "ﾁ", "ﾂ", "ﾃ", "ﾄ", "ﾀﾞ",
                "ﾁﾞ", "ﾂﾞ", "ﾃﾞ", "ﾄﾞ", "ｯ", "ﾅ", "ﾆ", "ﾇ", "ﾈ", "ﾉ", "ﾊ", "ﾋ", "ﾌ", "ﾍ", "ﾎ", "ﾊﾞ", "ﾋﾞ", "ﾌﾞ", "ﾍﾞ",
                "ﾎﾞ", "ﾊﾟ", "ﾋﾟ", "ﾌﾟ", "ﾍﾟ", "ﾎﾟ", "ﾏ", "ﾐ", "ﾑ", "ﾒ", "ﾓ", "ﾔ", "ﾕ", "ﾖ", "ｬ", "ｭ", "ｮ", "ﾗ", "ﾘ",
                "ﾙ", "ﾚ", "ﾛ", "ﾜ", "ｦ", "ﾝ", "ゎ", "ｲ", "ｴ", "ｰ", "ﾟ", "ﾞ" };
        // 全角英語数字記号
        String zene[] = { "ａ", "Ａ", "ｂ", "Ｂ", "ｃ", "Ｃ", "ｄ", "Ｄ", "ｅ", "Ｅ", "ｆ", "Ｆ", "ｇ", "Ｇ", "ｈ", "Ｈ", "ｉ", "Ｉ", "ｊ",
                "Ｊ", "ｋ", "Ｋ", "ｌ", "Ｌ", "ｍ", "Ｍ", "ｎ", "Ｎ", "ｏ", "Ｏ", "ｐ", "Ｐ", "ｑ", "Ｑ", "ｒ", "Ｒ", "ｓ", "Ｓ", "ｔ", "Ｔ",
                "ｕ", "Ｕ", "ｖ", "Ｖ", "ｗ", "Ｗ", "ｘ", "Ｘ", "ｙ", "Ｙ", "ｚ", "Ｚ", "１", "２", "３", "４", "５", "６", "７", "８", "９",
                "０", "（", "）", "「", "」", "｛", "｝", "［", "］", "―", "－", "‐" };

        // 半角英語数字記号
        String hane[] = { "a", "A", "b", "B", "c", "C", "d", "D", "e", "E", "f", "F", "g", "G", "h", "H", "i", "I", "j",
                "J", "k", "K", "l", "L", "m", "M", "n", "N", "o", "O", "p", "P", "q", "Q", "r", "R", "s", "S", "t", "T",
                "u", "U", "v", "V", "w", "W", "x", "X", "y", "Y", "z", "Z", "1", "2", "3", "4", "5", "6", "7", "8", "9",
                "0", "(", ")", "｢", "｣", "{", "}", "[", "]", "-", "-", "-" };

        // パラメータ
        String astrHan = "";
        Integer llStart = 0;
        Integer llCount = 0;
        String lsOne = "";
        Integer llNo = 0;
        Integer lli = 0;
        Integer llMax = 87;
        Integer llMax2 = 73;
        String name[] = new String[zenh.length];

        // 引数で渡された文字列の空白削除
        String lsMoji = astrZen.trim();
        // 引数の最初の文字から順番に半角カタカナ変換
        Integer llKaZu = lsMoji.length();

        while (llStart < llKaZu) {
            if (llStart + 1 < llKaZu && ("ﾞ".equals(lsMoji.substring(llStart + 1, llStart + 2))
                    || "ﾟ".equals(lsMoji.substring(llStart + 1, llStart + 2)))) {
                lsOne = lsMoji.substring(llStart, llStart + 2);
                llStart++;
            } else {
                lsOne = lsMoji.substring(llStart, llStart + 1);
            }

            boolean found = false;

            // 半角カナを全角ひらがな変換
            for (llNo = 0; llNo < llMax && !found; llNo++) {
                if (lsOne.equals(hank[llNo])) {
                    name[llCount] = zenh[llNo];
                    llCount++;
                    found = true;
                }
            }
            if (found) {
                llStart++;
                continue;
            }

            // 全角カナを全角ひらがな変換
            for (llNo = 0; llNo < llMax && !found; llNo++) {
                if (lsOne.equals(zenk[llNo])) {
                    name[llCount] = zenh[llNo];
                    llCount++;
                    found = true;
                }
            }
            if (found) {
                llStart++;
                continue;
            }

            // 半角英語を全角英語変換
            for (llNo = 0; llNo < llMax2 && !found; llNo++) {
                if (lsOne.equals(hane[llNo])) {
                    name[llCount] = zene[llNo];
                    llCount++;
                    found = true;
                }
            }
            if (found) {
                llStart++;
                continue;
            }

            // 全角全角変換
            for (llNo = 0; llNo < llMax && !found; llNo++) {
                if (lsOne.equals(zenk[llNo])) {
                    name[llCount] = zenk[llNo];
                    llCount++;
                    found = true;
                }
            }
            if (found) {
                llStart++;
                continue;
            }

            // 全角英語を全角変換
            for (llNo = 0; llNo < llMax2 && !found; llNo++) {
                if (lsOne.equals(zene[llNo])) {
                    name[llCount] = zene[llNo];
                    llCount++;
                    found = true;
                }
            }
            if (found) {
                llStart++;
                continue;
            }

            // その他
            name[llCount] = "?";
            llCount++;
            llStart++;
        }

        // 合并转换后的字符
        StringBuilder hanname = new StringBuilder();
        for (lli = 0; lli < llCount; lli++) {
            if (name[lli] != null) {
                hanname.append(name[lli]);
            }
        }
        astrHan = hanname.toString();
        return astrHan;
    }

    /**
     * f_RelativeDay : 文字列の年月日のｎ日前（後）を返します
     * 
     * @param srcDate     変換前の年月日(西暦・和暦)
     * @param relativeNum ｎ日後（前）の値 正の場合はｎ日後、負の場合はｎ日前
     * @return 正常の場合は変換後の年月日(yyyymmdd)、異常の場合は""を返します
     * <AUTHOR>
     */
    public String getRelativeDay(String srcDate, Integer relativeNum) {
        // 西暦に変換 (yyyymmdd)
        String schgDate = this.getChangeSeireki(srcDate);
        if (StringUtils.isEmpty(schgDate)) {
            return StringUtils.EMPTY;
        }
        // 日がない？
        if (StringUtils.length(schgDate) < 8) {
            return StringUtils.EMPTY;
        }
        // yyyymmdd->yyyy/mm/dd
        String wkDate = this.cnvYmd(schgDate);
        if (StringUtils.isEmpty(wkDate)) {
            return StringUtils.EMPTY;
        }
        // 一旦、date 型に変換する
        LocalDate hireDate;
        try {
            hireDate = LocalDate.parse(wkDate, DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD));
        } catch (Exception e) {
            return StringUtils.EMPTY;
        }
        // 増減なしなら Schg_date を返す
        // 日後(前)を求める
        LocalDate dchgDate = hireDate.plusDays(relativeNum);
        schgDate = dchgDate.format(DateTimeFormatter.BASIC_ISO_DATE);
        // 範囲チェック ( 明治１年～平成９９年 )
        if (!this.chkDate2(schgDate)) {
            return StringUtils.EMPTY;
        }
        return schgDate;
    }

    /**
     * f_zero2sp : 和暦の先頭０の数字をスペースに変換する
     * 
     * @param strDate 変換前の年月日(西暦・和暦)
     * @return 正常の場合は変換後の年月日(yyyymmdd)、異常の場合は""を返します
     * <AUTHOR>
     */
    public String zeroToSpace(String strDate) {
        // 変換後の年月日
        String convertedDate = CommonConstants.EMPTY_STRING;
        // 日付中の '/'の個数
        Integer separatorCnt = CommonConstants.INT_0;
        // 和暦チェック
        // 和暦の場合
        if (StringUtils.isNotEmpty(chkIswareki(strDate))) {
            // 日付中の '/'の個数を得る
            SepaOutDto gchkSepaOutDto = chksepa(strDate);
            separatorCnt = gchkSepaOutDto.getSeparatorCnt();
            if (CommonConstants.INT_MINUS_1 == separatorCnt) {
                return CommonConstants.EMPTY_STRING;
            } else if (CommonConstants.INT_0 == separatorCnt) {
                // 元号の長さ>0 且つ変換前の年月日の長さ>7の場合
                if (gchkSepaOutDto.getGengo().length() > 0 && strDate.length() > 7) {
                    return CommonConstants.EMPTY_STRING;
                }
            }
            // 編集
            // 年の値 > 0の場合
            if (gchkSepaOutDto.getYear() > 0) {
                // 年の値が2桁の場合
                if (gchkSepaOutDto.getYear() > 9) {
                    convertedDate = gchkSepaOutDto.getGengo() + CommonDtoUtil.objValToString(gchkSepaOutDto.getYear());
                    // 年の値が1桁の場合
                } else {
                    convertedDate = gchkSepaOutDto.getGengo() + CommonConstants.BLANK_SPACE
                            + CommonDtoUtil.objValToString(gchkSepaOutDto.getYear());
                }
            }
            // 月の値 > 0の場合
            if (gchkSepaOutDto.getMonth() > 0) {
                // 月の値が2桁の場合
                if (gchkSepaOutDto.getMonth() > 9) {
                    convertedDate = convertedDate + CommonConstants.STRING_SLASH
                            + CommonDtoUtil.objValToString(gchkSepaOutDto.getMonth());
                    // 月の値が1桁の場合
                } else {
                    convertedDate = convertedDate + CommonConstants.STRING_SLASH + CommonConstants.BLANK_SPACE
                            + CommonDtoUtil.objValToString(gchkSepaOutDto.getMonth());
                }
            }
            // 日の値 > 0の場合
            if (gchkSepaOutDto.getDay() > 0) {
                // 日の値が2桁の場合
                if (gchkSepaOutDto.getDay() > 9) {
                    convertedDate = convertedDate + CommonConstants.STRING_SLASH
                            + CommonDtoUtil.objValToString(gchkSepaOutDto.getDay());
                    // 日の値が1桁の場合
                } else {
                    convertedDate = convertedDate + CommonConstants.STRING_SLASH + CommonConstants.BLANK_SPACE
                            + CommonDtoUtil.objValToString(gchkSepaOutDto.getDay());
                }
            }
        }

        return convertedDate;
    }

    /**
     * 西暦日付を和暦日付に変換(西暦→和暦)
     * 
     * @param yyyymmdd 西暦日付(yyyy/mm/dd)
     * @return 和暦日付リスト
     * @throws Exception 例外
     */
    public List<String> getFs2gKantanRtn(String yyyymmdd) throws Exception {
        List<String> result = new ArrayList<>();
        // 年号を取得
        String lsGengo = CommonConstants.BLANK_STRING;
        // 年を取得
        String lsYy = CommonConstants.BLANK_STRING;
        // 月を取得
        String lsMm = CommonConstants.BLANK_STRING;
        // 日を取得
        String lsDd = CommonConstants.BLANK_STRING;

        int liIdx;
        int liGpos;

        // 引数チェックはヌルまたは空文字列のとき空文字列を返す だけ
        if (StringUtils.isBlank(yyyymmdd)) {
            return result;
        }

        // 月の0→" "に
        if (CommonConstants.STR_0.equals(this.ndsMidaLogic.fNdsMida(yyyymmdd, 6, 1))) {
            lsMm = CommonConstants.BLANK_SPACE + this.ndsMidaLogic.fNdsMida(yyyymmdd, 7, 1);
        } else {
            lsMm = this.ndsMidaLogic.fNdsMida(yyyymmdd, 6, 2);
        }

        // 日の0→" "に
        if (CommonConstants.STR_0.equals(this.ndsMidaLogic.fNdsMida(yyyymmdd, 9, 1))) {
            lsDd = CommonConstants.BLANK_SPACE + this.getNdsRighta(yyyymmdd, 1);
        } else {
            lsDd = this.getNdsRighta(yyyymmdd, 2);
        }

        // 平成超のチェック
        liGpos = -1;

        GengouInfoExpandOutDto outDto = this.gengouInfoExpand();
        // 元号開始年
        List<Integer> giGengouYyyy = outDto.getGiGengouYyyyList();
        // 元号開始月
        List<Integer> giGengouMm = outDto.getGiGengouMmList();
        // 元号開始日
        List<Integer> giGengouDd = outDto.getGiGengouDdList();
        // 元号３
        List<String> gsGengou3 = outDto.getGsGengou3List();
        // 西暦日付年
        Integer yyyy = Integer.valueOf(StringUtils.substring(yyyymmdd, 0, 4));

        for (liIdx = giGengouYyyy.size() - 1; liIdx >= 0; liIdx--) {
            if (giGengouYyyy.get(liIdx) <= 1989) {
                // 平成以前は従来のチェックを行う
                break;
            }
            // 平成超の場合の元号位置チェック
            if (giGengouYyyy.get(liIdx) > yyyy) {
                continue;
            } else if (giGengouYyyy.get(liIdx) == yyyy) {
                if (giGengouMm.get(liIdx) > Integer.valueOf(lsMm)) {
                    continue;
                } else if (giGengouMm.get(liIdx) == Integer.valueOf(lsMm)) {
                    if (giGengouDd.get(liIdx) > Integer.valueOf(lsDd)) {
                        continue;
                    }
                }
            }
            liGpos = liIdx;
            break;
        }

        // 西暦日付
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
        LocalDate date = LocalDate.parse(yyyymmdd, dateFormatter);
        // 西暦１９８９年
        LocalDate dateH = LocalDate.parse("1989/01/08", dateFormatter);
        // 西暦１９２６年
        LocalDate dateS = LocalDate.parse("1926/12/25", dateFormatter);
        // 西暦１９１２年
        LocalDate dateT = LocalDate.parse("1912/07/30", dateFormatter);

        if (liGpos > 0) {
            lsYy = String.valueOf(yyyy - giGengouYyyy.get(liGpos) + 1);
            if (lsYy.length() == 1) {
                lsYy = CommonConstants.BLANK_SPACE + lsYy;
            }
            lsGengo = gsGengou3.get(liGpos);

        } else if (date.isEqual(dateH) || date.isAfter(dateH)) {
            // 西暦１９８９年－平成０１年(1/8～)
            lsYy = String.valueOf(Integer.valueOf(StringUtils.left(yyyymmdd, 4)) - 1988);
            if (StringUtils.length(lsYy) == 1) {
                lsYy = CommonConstants.BLANK_SPACE + lsYy;
            }
            lsGengo = "H";
        } else if (date.isEqual(dateS) || date.isAfter(dateS)) {
            // 西暦１９２６年－昭和０１年(12/25～)
            lsYy = String.valueOf(Integer.valueOf(StringUtils.left(yyyymmdd, 4)) - 1925);
            if (StringUtils.length(lsYy) == 1) {
                lsYy = CommonConstants.BLANK_SPACE + lsYy;
            }
            lsGengo = "S";
        } else if (date.isEqual(dateT) || date.isAfter(dateT)) {
            // 西暦１９１２年－大正０１年(7/30～)
            lsYy = String.valueOf(Integer.valueOf(StringUtils.left(yyyymmdd, 4)) - 1911);
            if (StringUtils.length(lsYy) == 1) {
                lsYy = CommonConstants.BLANK_SPACE + lsYy;
            }
            lsGengo = "T";
        } else {
            // 西暦１８６８年－明治０１年(9/8～)
            lsYy = String.valueOf(Integer.valueOf(StringUtils.left(yyyymmdd, 4)) - 1867);
            if (StringUtils.length(lsYy) == 1) {
                lsYy = CommonConstants.BLANK_SPACE + lsYy;
            }
            lsGengo = "M";
        }
        result.add(lsGengo);
        result.add(lsYy);
        result.add(lsMm);
        result.add(lsDd);
        return result;
    }

    /**
     * f3gk_get_jigyo_name : 事業者名を得る（履歴対応版）
     * 
     * @param alSvj 対象の事業所id
     * @param asYmd 履歴検索年月日（yyyy/mm/dd）
     * @return 事業所名
     * <AUTHOR>
     */
    public String getJigyoName(Integer alSvj, String asYmd) {
        String lsWork = StringUtils.EMPTY;
        String lsDdd = StringUtils.EMPTY;
        JigyoKnjByCriteriaInEntity jigyoKnj2ByCriteriaInEntity = new JigyoKnjByCriteriaInEntity();
        // 対象の事業所id
        jigyoKnj2ByCriteriaInEntity.setAlShien(CommonDtoUtil.objValToString(alSvj));
        // DAOを実行
        List<JigyoKnjOutEntity> jigyoKnj2List = comMscSvjigyoSelectMapper
                .findJigyoKnjByCriteria(jigyoKnj2ByCriteriaInEntity);
        // チェック
        if (CollectionUtils.isNotEmpty(jigyoKnj2List)) {
            lsWork = jigyoKnj2List.getFirst().getJigyoKnj();
            if (lsWork == null) {
                lsWork = StringUtils.EMPTY;
            }
        }
        JigyoshoMeishoByCriteriaInEntity jigyoshoMeishoByCriteriaInEntity = new JigyoshoMeishoByCriteriaInEntity();
        // 対象の事業所id
        jigyoshoMeishoByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(alSvj));
        // 履歴検索年月日（yyyy/mm/dd）
        jigyoshoMeishoByCriteriaInEntity.setAsYmd(asYmd);
        // DAOを実行
        List<JigyoshoMeishoOutEntity> jigyoshoMeishoList = comMscSvjigyoRirekiSelectMapper
                .findJigyoshoMeishoByCriteria(jigyoshoMeishoByCriteriaInEntity);
        // チェック
        if (CollectionUtils.isNotEmpty(jigyoshoMeishoList)) {
            lsDdd = jigyoshoMeishoList.getFirst().getJigyoKnj();
            if (lsDdd == null) {
                lsDdd = lsWork;
            }
        } else {
            lsDdd = lsWork;
        }
        return lsDdd;
    }

    /**
     * f3gk_rtn_s2w : 表示用 西暦→和暦
     * 
     * @param yyyymmdd 日付(yyyy/mm/dd)
     * @return 変換した日付(Gyy/mm/dd)
     * <AUTHOR>
     */
    public String getRtnS2w(String yyyymmdd) {
        // 空チェック
        if (StringUtils.isEmpty(yyyymmdd)) {
            return StringUtils.EMPTY;
        }
        // 年号マスタから取得した年号をグローバル変数に移送する
        GengouInfoExpandOutDto gengouInfoExpandOutDto = this.gengouInfoExpand();
        // 元号開始年
        List<Integer> giGengouYyyy = gengouInfoExpandOutDto.getGiGengouYyyyList();
        // 元号開始月
        List<Integer> giGengouMm = gengouInfoExpandOutDto.getGiGengouMmList();
        // 元号開始日
        List<Integer> giGengouDd = gengouInfoExpandOutDto.getGiGengouDdList();
        // 元号3
        List<String> gsGengou3 = gengouInfoExpandOutDto.getGsGengou3List();
        String lsYy, lsMm, lsDd;
        Integer liGpos;
        // 月の0→" "に
        if (CommonConstants.STR_0
                .equals(StringUtils.substring(yyyymmdd, CommonConstants.INT_5, CommonConstants.INT_6))) {
            lsMm = CommonConstants.SPACE_STRING
                    + StringUtils.substring(yyyymmdd, CommonConstants.INT_6, CommonConstants.INT_7);
        } else {
            lsMm = StringUtils.substring(yyyymmdd, CommonConstants.INT_5, CommonConstants.INT_7);
        }
        // 日の0→" "に
        if (CommonConstants.STR_0
                .equals(StringUtils.substring(yyyymmdd, CommonConstants.INT_8, CommonConstants.INT_9))) {
            lsDd = CommonConstants.SPACE_STRING
                    + StringUtils.substring(yyyymmdd, CommonConstants.INT_8, CommonConstants.INT_9);
        } else {
            lsDd = StringUtils.substring(yyyymmdd, CommonConstants.INT_8, CommonConstants.INT_10);
        }
        // 平成超のチェック
        liGpos = CommonConstants.INT_MINUS_1;
        int currentYear = Integer
                .parseInt(StringUtils.substring(yyyymmdd, CommonConstants.INT_0, CommonConstants.INT_4));
        int currentMonth = Integer.parseInt(lsMm.trim());
        int currentDay = Integer.parseInt(lsDd.trim());
        for (int liIdx = giGengouYyyy.size() - CommonConstants.INT_1; liIdx >= CommonConstants.INT_0; liIdx--) {
            if (giGengouYyyy.get(liIdx) <= CommonConstants.YMD_1989) {
                // 平成以前は従来のチェックを行う
                break;
            }
            // 平成超の場合の元号位置チェック
            if (giGengouYyyy.get(liIdx) > currentYear) {
                continue;
            } else if (giGengouYyyy.get(liIdx) == currentYear) {
                if (giGengouMm.get(liIdx) > currentMonth) {
                    continue;
                } else if (giGengouMm.get(liIdx) == currentMonth) {
                    if (giGengouDd.get(liIdx) > currentDay) {
                        continue;
                    }
                }
            }
            liGpos = liIdx;
            break;
        }
        if (liGpos >= CommonConstants.INT_0) {
            lsYy = String.valueOf(currentYear - giGengouYyyy.get(liGpos) + CommonConstants.INT_1);
            if (StringUtils.length(lsYy) == CommonConstants.INT_1)
                lsYy = CommonConstants.SPACE_STRING + lsYy;
            lsYy = gsGengou3.get(liGpos) + lsYy;
        } else if (yyyymmdd.compareTo(CommonConstants.YMD_19890108) >= CommonConstants.INT_0) {
            // 西暦１９８９年－平成０１年(1/8～)
            lsYy = CommonConstants.STR_HEISEI_RYAKU + formatYear(currentYear - CommonConstants.YMD_1988);
        } else if (yyyymmdd.compareTo(CommonConstants.YMD_19261225) >= CommonConstants.INT_0) {
            // 西暦１９２６年－昭和０１年(12/25～)
            lsYy = CommonConstants.STR_SHOWA_RYAKU + formatYear(currentYear - CommonConstants.YMD_1925);
        } else if (yyyymmdd.compareTo(CommonConstants.YMD_19120730) >= CommonConstants.INT_0) {
            // 西暦１９１２年－大正０１年(7/30～)
            lsYy = CommonConstants.STR_TAISHO_RYAKU + formatYear(currentYear - CommonConstants.YMD_1911);
        } else {
            // 西暦１８６８年－明治０１年(9/8～)
            lsYy = CommonConstants.STR_MEIJI_RYAKU + formatYear(currentYear - CommonConstants.YMD_1867);
        }
        return lsYy + CommonConstants.STRING_SLASH + lsMm + CommonConstants.STRING_SLASH + lsDd;
    }

    // 年を2桁フォーマット（1桁ならスペース追加）
    private String formatYear(int year) {
        return (year < CommonConstants.INT_10) ? CommonConstants.SPACE_STRING + year : String.valueOf(year);
    }

    /**
     * 全角と半角を指定に従い変換する
     * 
     * @param asInput 変換する文字列
     * @param aiMode  変換モード 0 : 全角を半角にする(変更できない文字はそのまま) 1 : 同上 アルファベットを大文字に
     *                ひらがなもカタカナに 2 : 半角を全角にする 3 : 全角英数を半角に、半角カナを全角に
     * 
     * @return FB で使用可能な文字列かを返す(モード0,1 の場合のみ)
     * <AUTHOR>
     */
    public Boolean fzenHanKai(String asInput, StringBuffer asOutput, long aiMode) {
        // 定数
        final String CLS_SYMBOL_ZEN = "　（），－．／" + "・ー゛゜" + "・ー゛゜" + "！”＃＄％＆’＊＋：；＜＝＞？＠［￥］＾＿‘｛｜｝～";
        final String CLS_SYMBOL_HAN = " (),-./" + "･ｰﾞﾟ" + "｡｢｣､｡｢｣､" + "!~\"#$%&'*+:;<=>?@[\\]^_`{|}~~";
        final String CLS_HIRA_SEI = "をぁぃぅぇぉゃゅょっーあいうえおかきくけこさしすせそ" + "をぁぃぅぇぉゃゅょっーあいうえおかきくけこさしすせそ";
        final String CLS_HIRA_DAK = "がぎぐげござじずぜぞだぢづでど　　　　　ばびぶべぼ";
        final String CLS_HIRA_HAN = "ぱぴぷぺぽ";
        final String CLS_KATA_SEI = "ヲァィゥェォャュョッーアイウエオカキクケコサシスセソ" + "タチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワン";
        final String CLS_KATA_DAK = "ヴ　　ガギグゲゴザジズゼゾダヂヅデド　　　　　バビブベボ";
        final String CLS_KATA_HAN = "パピプペポ";
        final String CLS_HIRA_ZEN = "ゎゐゑ";
        final String CLS_KATA_ZEN = "ヮヰヱヵ";
        final String CLS_KANA_HAN = "ﾜｲｴｶ";
        final String CLS_ASCII = "　！”＃＄％＆’（）＊＋，－．／０１２３４５６７８９：；＜＝＞？" + "＠ＡＢＣＤＥＦＧＨＩＪＫＬＭＮＯＰＱＲＳＴＵＶＷＸＹＺ［￥］＾＿"
                + "‘ａｂｃｄｅｆｇｈｉｊｋｌｍｎｏｐｑｒｓｔｕｖｗｘｙｚ｛｜｝～";

        // 変数
        String lsTrg; // 検査対象文字列バッファ
        String lsMake = ""; // 作成文字列
        String lsTemp; // 検査用文字列
        Boolean lbOK = true; // 返り値（FB使用可能文字）
        int liCode; // 計算用コード

        if (Objects.isNull(asInput))
            return false;
        lsTrg = asInput;

        while (!lsTrg.isEmpty()) {
            lsTemp = lsTrg.substring(0, 1);
            lsTrg = lsTrg.length() > 1 ? lsTrg.substring(1) : "";

            if (lsTemp.getBytes().length == 1) { // 1 byte code
                if (aiMode == 0 || aiMode == 1) { // 全角⇒半角
                    liCode = (int) lsTemp.charAt(0);
                    if (liCode >= 32 && liCode <= 47 && (liCode == 32 || liCode == 40 || liCode == 41 || liCode == 44
                            || liCode == 45 || liCode == 46 || liCode == 47)) {
                        lsMake += lsTemp;
                    } else if ((liCode >= 48 && liCode <= 57) || (liCode >= 65 && liCode <= 90)) { // 0-9, A-Z
                        lsMake += lsTemp;
                    } else if (liCode >= 97 && liCode <= 122) { // a-z
                        if (aiMode == 0) {
                            lsMake += lsTemp;
                        } else {
                            lsMake += (char) (liCode - 32);
                        }
                    } else if (liCode >= 165 && liCode <= 223) { // kana
                        lsMake += lsTemp;
                    } else {
                        lsMake += lsTemp;
                        lbOK = false;
                    }
                } else { // 半角⇒全角
                    liCode = (int) lsTemp.charAt(0);
                    if (liCode >= 32 && liCode <= 126) { // Ascii Code
                        if (aiMode == 2) {
                            lsMake += CLS_ASCII.charAt(liCode - 32);
                        } else {
                            lsMake += lsTemp;
                        }
                    } else if (liCode == 165) { // ･
                        lsMake += "・";
                    } else if (liCode >= 166 && liCode <= 221) { // kana
                        lsTemp = !lsTrg.isEmpty() ? lsTrg.substring(0, 1) : "";
                        if (lsTemp.equals("゛") || lsTemp.equals("ﾞ")) {
                            if (liCode >= 179 && liCode <= 206) {
                                lsTemp = CLS_KATA_DAK.substring(liCode - 179, liCode - 178);
                                if (!lsTemp.equals("　")) { // 濁点付き
                                    lsMake += lsTemp;
                                    lsTrg = lsTrg.length() > 1 ? lsTrg.substring(1) : "";
                                } else {
                                    lsMake += CLS_KATA_SEI.charAt(liCode - 166);
                                }
                            } else {
                                lsMake += CLS_KATA_SEI.charAt(liCode - 166);
                            }
                        } else if (lsTemp.equals("゜") || lsTemp.equals("ﾟ")) {
                            if (liCode >= 202 && liCode <= 206) {
                                lsTemp = CLS_KATA_HAN.substring(liCode - 202, liCode - 201);
                                if (!lsTemp.equals("　")) { // 半濁点付き
                                    lsMake += lsTemp;
                                    lsTrg = lsTrg.length() > 1 ? lsTrg.substring(1) : "";
                                } else {
                                    lsMake += CLS_KATA_SEI.charAt(liCode - 166);
                                }
                            } else {
                                lsMake += CLS_KATA_SEI.charAt(liCode - 166);
                            }
                        } else {
                            lsMake += CLS_KATA_SEI.charAt(liCode - 166);
                        }
                    } else if (liCode == 222) { // ﾞ
                        lsMake += "゛";
                    } else if (liCode == 223) { // ﾟ
                        lsMake += "゜";
                    } else if (liCode >= 161 && liCode <= 164) { // 。「」、
                        liCode = ndsPosALogic.fNdsPosa(CLS_SYMBOL_HAN, lsTemp);
                        if (liCode != 0) {
                            lsMake += CLS_SYMBOL_ZEN.charAt(liCode);
                        }
                        lbOK = false;
                    } else {
                        lsMake += lsTemp;
                        lbOK = false;
                    }
                }
            } else { // 2 byte code
                liCode = Integer.valueOf(lsTemp.charAt(0));
                if (liCode == 129) { // 記号
                    if (aiMode == 2) {
                        lsMake += lsTemp;
                    } else {
                        liCode = CLS_SYMBOL_ZEN.indexOf(lsTemp);
                        if (liCode != -1) {
                            if (aiMode == 3) {
                                if (liCode > 7 && liCode <= 15) { // カナ領域
                                    lsMake += lsTemp;
                                } else {
                                    lsMake += ndsMidaLogic.fNdsMida(CLS_SYMBOL_HAN, liCode, 1);
                                }
                            } else {
                                lsMake += ndsMidaLogic.fNdsMida(CLS_SYMBOL_HAN, liCode, 1);
                                if (liCode <= 11) { // 11 以下は FB 使用可
                                    lbOK = false;
                                }
                            }
                        } else {
                            lsMake += lsTemp;
                            lbOK = false;
                        }
                    }
                } else if (liCode == 130) { // 英数かな
                    liCode = CLS_ASCII.indexOf(lsTemp);
                    if (liCode >= 17 && liCode <= 26) { // 0-9
                        if (aiMode == 2) {
                            lsMake += lsTemp;
                        } else {
                            lsMake += (char) (liCode - 17 + 48);
                        }
                    } else if (liCode >= 34 && liCode <= 59) { // A-Z
                        if (aiMode == 2) {
                            lsMake += lsTemp;
                        } else {
                            lsMake += (char) (liCode - 34 + 65);
                        }
                    } else if (liCode >= 66 && liCode <= 91) { // a-z
                        if (aiMode == 2) {
                            lsMake += lsTemp;
                        } else if (aiMode == 1) { // 大文字変換
                            lsMake += (char) (liCode - 66 + 65);
                        } else {
                            lsMake += (char) (liCode - 66 + 97);
                            lbOK = false;
                        }
                    } else { // かな
                        if (aiMode == 0 || aiMode == 2 || aiMode == 3) {
                            lsMake += lsTemp;
                            lbOK = false;
                        } else {
                            liCode = CLS_HIRA_SEI.indexOf(lsTemp);
                            if (liCode != -1) {
                                lsMake += (char) (liCode + 166);
                            } else {
                                liCode = CLS_HIRA_DAK.indexOf(lsTemp);
                                if (liCode != -1) { // 濁点付き
                                    lsMake += (char) (liCode + 182) + "ﾞ";
                                } else {
                                    liCode = CLS_HIRA_HAN.indexOf(lsTemp);
                                    if (liCode != -1) { // 半濁点付き
                                        lsMake += (char) (liCode + 202) + "ﾟ";
                                    } else {
                                        liCode = CLS_HIRA_ZEN.indexOf(lsTemp);
                                        if (liCode != -1) { // 旧カナなど
                                            lsMake += ndsMidaLogic.fNdsMida(CLS_KANA_HAN, liCode, 1);
                                        } else {
                                            lsMake += lsTemp;
                                            lbOK = false;
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else if (liCode == 131) { // カナ他
                    if (aiMode == 2 || aiMode == 3) {
                        lsMake += lsTemp;
                    } else {
                        liCode = CLS_KATA_SEI.indexOf(lsTemp);
                        if (liCode != -1) {
                            lsMake += (char) (liCode + 166);
                        } else {
                            liCode = CLS_KATA_DAK.indexOf(lsTemp);
                            if (liCode != -1) { // 濁点付き
                                lsMake += (char) (liCode + 179) + "ﾞ";
                            } else {
                                liCode = CLS_KATA_HAN.indexOf(lsTemp);
                                if (liCode != -1) { // 半濁点付き
                                    lsMake += (char) (liCode + 202) + "ﾟ";
                                } else {
                                    if (aiMode == 0) {
                                        lsMake += lsTemp;
                                        lbOK = false;
                                    } else {
                                        liCode = CLS_KATA_ZEN.indexOf(lsTemp);
                                        if (liCode != -1) { // 旧カナなど
                                            lsMake += CLS_KANA_HAN.charAt(liCode);
                                        } else {
                                            lsMake += lsTemp;
                                            lbOK = false;
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else { // その他
                    lsMake += lsTemp;
                    lbOK = false;
                }
            }
        }
        asOutput.setLength(0); // 出力バッファをクリア
        asOutput.append(lsMake);
        return lbOK; // FB で使用可能文字かどうか
    }

    /**
     * f3gk_asp : ASPかどうかの判断をする
     * 
     * @param gbOnpreMulti オンプレマルチ環境
     * @return true: ASPである false: ASPでない
     * <AUTHOR>
     */
    public boolean isF3gkAsp(Boolean gbOnpreMulti) {
        String llRet = System.getenv(CommonConstants.NDSMTODBC_MAIN);
        if (StringUtils.isNotEmpty(llRet)) {
            if (gbOnpreMulti) {
                return false;// 環境変数アリだが（OLP構成だが）オンプレミス動作とする
            } else {
                return true;
            }
        }
        return false;
    }
}