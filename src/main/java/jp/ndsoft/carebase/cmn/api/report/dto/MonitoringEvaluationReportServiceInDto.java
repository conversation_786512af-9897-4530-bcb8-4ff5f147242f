package jp.ndsoft.carebase.cmn.api.report.dto;

import java.util.Map;

import jp.ndsoft.smh.framework.global.report.dto.FixedReportInDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * @since 2025.08.07
 * @description
 *              U01020_モニタリング・評価表（H21改訂版） 帳票入力 InDto
 * <AUTHOR> 張婧
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class MonitoringEvaluationReportServiceInDto extends FixedReportInDto {
    /** UID. */
    private static final long serialVersionUID = 1L;
    /** データソース */
    private JRBeanCollectionDataSource dataSource;
    /** パラメータ */
    private Map<String, Object> parameters;

    /** 記入用シートを印刷するフラグ */
    private Boolean emptyFlg;
    /** 帳票タイトル */
    private String title;
    /** 指定日（年号） */
    private String shiTeiDateGG;
    /** 指定日（年） */
    private String shiTeiDateYY;
    /** 指定日（月） */
    private String shiTeiDateMM;
    /** 指定日（日）  */
    private String shiTeiDateDD;
    /** 事業所名 */
    private String jigyoName;
    /** 利用者氏名 */
    private String fullName;
    /** 敬称 */
    private String keisho;
    /** ケース番号 */
    private String caseNo;
    /** 初回作成年月日（年号） */
    private String shokaiYmdGG;
    /** 初回作成年月日（年） */
    private String shokaiYmdYY;
    /** 初回作成年月日（月） */
    private String shokaiYmdMM;
    /** 初回作成年月日（日） */
    private String shokaiYmdDD;
    /** 作成年月日（年号） */
    private String createYmdGG;
    /** 作成年月日（年） */
    private String createYmdYY;
    /** 作成年月日（月） */
    private String createYmdMM;
    /** 作成年月日（日） */
    private String createYmdDD;
    /** 印鑑欄表示区分 */
    private Integer hankoHyoujiKbn;
    /** 印鑑1 */
    private String hanko1Knj;
    /** 印鑑2 */
    private String hanko2Knj;
    /** 印鑑3 */
    private String hanko3Knj;
    /** 印鑑4 */
    private String hanko4Knj;
    /** 印鑑5 */
    private String hanko5Knj;
    /** 印鑑6 */
    private String hanko6Knj;
    /** 印鑑7 */
    private String hanko7Knj;
    /** 印鑑8 */
    private String hanko8Knj;
    /** 印鑑9 */
    private String hanko9Knj;
    /** 印鑑10 */
    private String hanko10Knj;
    /** 印鑑11 */
    private String hanko11Knj;
    /** 印鑑12 */
    private String hanko12Knj;
    /** 印鑑13 */
    private String hanko13Knj;
    /** 印鑑14 */
    private String hanko14Knj;
    /** 印鑑15 */
    private String hanko15Knj;
    /** モニタリングリスト */
    private JRBeanCollectionDataSource moniList;
    /** 新たなニーズ・再アセスメントリスト */
    private JRBeanCollectionDataSource needsRetryList;
    /** 承認欄 */
    private JRBeanCollectionDataSource subReportDataDs;
    /** 承認欄印刷区分 */
    private Integer shoninKbn;
    /** 承認欄情報テンプレートパス */
    private String subReportPath;
    /** 文書管理番号 */
    private String bunsyoKanriNo;

}
