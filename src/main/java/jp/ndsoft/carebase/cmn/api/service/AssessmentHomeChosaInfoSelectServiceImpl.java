package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.*;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00802CpnTucCsc3H21Info;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeChosaInfoSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeChosaInfoSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.*;
import jp.ndsoft.carebase.common.dao.mysql.mapper.*;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import jp.ndsoft.smh.framework.common.Constants;

import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @since 2025.04.02
 * <AUTHOR>
 * @implNote GUI00802_［アセスメント］画面（居宅）（6⑤）調査票情報取得
 */
@Service
public class AssessmentHomeChosaInfoSelectServiceImpl
        extends
        SelectServiceImpl<AssessmentHomeChosaInfoSelectServiceInDto, AssessmentHomeChosaInfoSelectServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）情報取得 */
    @Autowired
    private CpnTucCsc3H21SelectMapper cpnTucCsc3H21SelectMapper;

    /**
     * ［アセスメント］画面（居宅）（6⑤）調査票情報取得
     * 
     * @param inDto ［アセスメント］画面（居宅）（6⑤）入力DTO.
     * @return ［アセスメント］画面（居宅）（6⑤）出力DTO
     */
    @Override
    protected AssessmentHomeChosaInfoSelectServiceOutDto mainProcess(
            final AssessmentHomeChosaInfoSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        // Ｈ２１改訂版認定調査票（基本）情報
        Gui00802CpnTucCsc3H21Info cpnTucCsc3H21Info = new Gui00802CpnTucCsc3H21Info();

        // ===============API-81.調査票情報取得 START===============//
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2.下記のＨ２１改訂版認定調査票（基本）情報取得のDAOを利用し、Ｈ２１改訂版認定調査票（基本）情報を取得する。======
         * =========
         * 
         */
        // DAOパラメータを作成
        CpnTucCsc3H21ByCriteriaInEntity cpnTucCsc3H21ByCriteriaInEntity = new CpnTucCsc3H21ByCriteriaInEntity();
        // 調査票ID
        cpnTucCsc3H21ByCriteriaInEntity.setCschId(CommonDtoUtil.strValToInt(inDto.getCschId()));
        cpnTucCsc3H21ByCriteriaInEntity.setSc1Id(CommonConstants.BLANK_STRING);
        // DAOを実行
        List<CpnTucCsc3H21OutEntity> cpnTucCsc3H21List = this.cpnTucCsc3H21SelectMapper
                .findCpnTucCsc3H21ByCriteria(cpnTucCsc3H21ByCriteriaInEntity);

        Optional<CpnTucCsc3H21OutEntity> cpnTucCsc3H21Optional = cpnTucCsc3H21List.stream()
                .findFirst();

        if (cpnTucCsc3H21Optional.isPresent()) {
            BeanUtils.copyProperties(cpnTucCsc3H21Info, cpnTucCsc3H21Optional.get());
            cpnTucCsc3H21Info.setBango110(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C10()));
            cpnTucCsc3H21Info.setBango111(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C1I1()));
            cpnTucCsc3H21Info.setBango112(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C1I2()));
            cpnTucCsc3H21Info.setBango113(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C1I3()));
            cpnTucCsc3H21Info.setBango114(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C1I4()));
            cpnTucCsc3H21Info.setBango115(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C1I5()));
            cpnTucCsc3H21Info.setBango116(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C1I6()));
            cpnTucCsc3H21Info.setBango121(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C2I1()));
            cpnTucCsc3H21Info.setBango122(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C2I2()));
            cpnTucCsc3H21Info.setBango123(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C2I3()));
            cpnTucCsc3H21Info.setBango124(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C2I4()));
            cpnTucCsc3H21Info.setBango125(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C2I5()));
            cpnTucCsc3H21Info.setBango13(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C3()));
            cpnTucCsc3H21Info.setBango14(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C4()));
            cpnTucCsc3H21Info.setBango15(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C5()));
            cpnTucCsc3H21Info.setBango16(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C6()));
            cpnTucCsc3H21Info.setBango17(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C7()));
            cpnTucCsc3H21Info.setBango18(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C8()));
            cpnTucCsc3H21Info.setBango19(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C9()));
            cpnTucCsc3H21Info.setBango110(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C10()));
            cpnTucCsc3H21Info.setBango111s(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C11()));
            cpnTucCsc3H21Info.setBango112s(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C12()));
            cpnTucCsc3H21Info.setBango113s(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango1C13()));
            cpnTucCsc3H21Info.setBango21(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango2C1()));
            cpnTucCsc3H21Info.setBango22(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango2C2()));
            cpnTucCsc3H21Info.setBango23(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango2C3()));
            cpnTucCsc3H21Info.setBango24(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango2C4()));
            cpnTucCsc3H21Info.setBango25(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango2C5()));
            cpnTucCsc3H21Info.setBango26(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango2C6()));
            cpnTucCsc3H21Info.setBango27(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango2C7()));
            cpnTucCsc3H21Info.setBango28(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango2C8()));
            cpnTucCsc3H21Info.setBango29(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango2C9()));
            cpnTucCsc3H21Info.setBango210(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango2C10()));
            cpnTucCsc3H21Info.setBango211(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango2C11()));
            cpnTucCsc3H21Info.setBango212(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango2C12()));
            cpnTucCsc3H21Info.setBango31(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C1()));
            cpnTucCsc3H21Info.setBango32(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C2()));
            cpnTucCsc3H21Info.setBango33(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C3()));
            cpnTucCsc3H21Info.setBango34(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C4()));
            cpnTucCsc3H21Info.setBango35(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C5()));
            cpnTucCsc3H21Info.setBango36(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C6()));
            cpnTucCsc3H21Info.setBango37(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C7()));
            cpnTucCsc3H21Info.setBango38(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C8()));
            cpnTucCsc3H21Info.setBango39(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C9()));
            cpnTucCsc3H21Info.setBango39(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C9()));
            cpnTucCsc3H21Info.setBango39(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C9()));
            cpnTucCsc3H21Info.setBango39(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C9()));
            cpnTucCsc3H21Info.setBango39(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C9()));
            cpnTucCsc3H21Info.setBango39(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C9()));
            cpnTucCsc3H21Info.setBango39(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C9()));
            cpnTucCsc3H21Info.setBango39(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango3C9()));
            cpnTucCsc3H21Info.setBango41(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C1()));
            cpnTucCsc3H21Info.setBango42(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C2()));
            cpnTucCsc3H21Info.setBango43(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C3()));
            cpnTucCsc3H21Info.setBango44(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C4()));
            cpnTucCsc3H21Info.setBango45(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C5()));
            cpnTucCsc3H21Info.setBango46(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C6()));
            cpnTucCsc3H21Info.setBango47(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C7()));
            cpnTucCsc3H21Info.setBango48(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C8()));
            cpnTucCsc3H21Info.setBango49(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C9()));
            cpnTucCsc3H21Info.setBango410(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C10()));
            cpnTucCsc3H21Info.setBango411(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C11()));
            cpnTucCsc3H21Info.setBango412(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C12()));
            cpnTucCsc3H21Info.setBango413(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C13()));
            cpnTucCsc3H21Info.setBango414(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C14()));
            cpnTucCsc3H21Info.setBango415(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango4C15()));
            cpnTucCsc3H21Info.setBango51(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango5C1()));
            cpnTucCsc3H21Info.setBango52(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango5C2()));
            cpnTucCsc3H21Info.setBango53(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango5C3()));
            cpnTucCsc3H21Info.setBango54(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango5C4()));
            cpnTucCsc3H21Info.setBango55(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango5C5()));
            cpnTucCsc3H21Info.setBango56(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango5C6()));
            cpnTucCsc3H21Info.setBango61(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango6C1()));
            cpnTucCsc3H21Info.setBango62(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango6C2()));
            cpnTucCsc3H21Info.setBango63(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango6C3()));
            cpnTucCsc3H21Info.setBango64(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango6C4()));
            cpnTucCsc3H21Info.setBango65(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango6C5()));
            cpnTucCsc3H21Info.setBango66(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango6C6()));
            cpnTucCsc3H21Info.setBango67(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango6C7()));
            cpnTucCsc3H21Info.setBango68(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango6C8()));
            cpnTucCsc3H21Info.setBango69(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango6C9()));
            cpnTucCsc3H21Info.setBango610(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango6C10()));
            cpnTucCsc3H21Info.setBango611(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango6C11()));
            cpnTucCsc3H21Info.setBango612(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango6C12()));
            cpnTucCsc3H21Info.setBango71(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango7C1()));
            cpnTucCsc3H21Info.setBango72(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getBango7C2()));
            cpnTucCsc3H21Info.setSc1Id(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getSc1Id()));
            cpnTucCsc3H21Info.setCschId(CommonDtoUtil.objValToString(cpnTucCsc3H21Optional.get().getCschId()));
        }

        // ===============API-81.初期情報取得 END===============//

        // 戻り情報を設定
        AssessmentHomeChosaInfoSelectServiceOutDto outDto = new AssessmentHomeChosaInfoSelectServiceOutDto();

        // Ｈ２１改訂版認定調査票（基本）情報リスト
        outDto.setCpnTucCsc3H21Info(cpnTucCsc3H21Info);

        LOG.info(Constants.END);

        return outDto;
    }
}
