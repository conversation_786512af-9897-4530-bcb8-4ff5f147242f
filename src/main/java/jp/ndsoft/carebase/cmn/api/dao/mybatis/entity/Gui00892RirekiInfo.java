package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import jp.ndsoft.smh.framework.global.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GUI00892_チェック項目画面
 * 
 * @description
 *              チェック項目ヘッダ履歴情報
 *              チェック項目ヘッダ履歴情報エンティティ
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Gui00892RirekiInfo implements Serializable, IEntity {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 法人ID */
    private String houjinId;

    /** 施設ID */
    private String shisetuId;

    /** 事業者ID */
    private String svJigyoId;

    /** 利用者ID */
    private String userId;

    /** 計画期間ID */
    private String sc1Id;

    /** 履歴ID */
    private String assId;

    /** 様式ID */
    private String cstId;

    /** 作成日 */
    private String createYmd;

    /** 作成者 */
    private String shokuId;

    /** 履歴番号 */
    private String krirekiNo;

    /** 履歴総件数 */
    private String rowCount;

}
