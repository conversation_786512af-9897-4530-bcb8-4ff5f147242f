package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI01038_週間計画
 * 
 * @description
 *              週間計画詳細リスト
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class Gui01038Cks52 extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** ダミーデータID */
    private String dmyKs52Id;

    /** ダミーサービス項目 */
    private String dmySvItem;

    /** wN1 */
    @JsonProperty("wN1")
    private String wN1;

    /** wN2 */
    @JsonProperty("wN2")
    private String wN2;

    /** wN3 */
    @JsonProperty("wN3")
    private String wN3;

    /** wN4 */
    @JsonProperty("wN4")
    private String wN4;

    /** wN5 */
    @JsonProperty("wN5")
    private String wN5;

    /** wN6 */
    @JsonProperty("wN6")
    private String wN6;

    /** wN7 */
    @JsonProperty("wN7")
    private String wN7;

    /** wN8 */
    @JsonProperty("wN8")
    private String wN8;

    /** wN9 */
    @JsonProperty("wN9")
    private String wN9;

    /** ダミー頻度 */
    private String dmyHindo;

    /** 週間計画ID */
    private String ks51Id;

    /** 詳細データID */
    private String ks52Id;

    /** 曜日 */
    private String youbi;

    /** 開始時間 */
    private String kaishiJikan;

    /** 終了時間 */
    private String shuuryouJikan;

    /** 内容 */
    private String naiyouKnj;

    /** 文字サイズ */
    private String fontSize;

    /** 表示モード */
    private String dispMode;

    /** 文字位置 */
    private String alignment;

    /** サービス種類 */
    private String svShuruiCd;

    /** サービス項目（台帳） */
    private String svItemCd;

    /** サービス事業者ID */
    private String svJigyoId;

    /** サービス種類名称 */
    private String svShuruiKnj;

    /** サービス項目名称 */
    private String svItemKnj;

    /** サービス事業者名称 */
    private String svJigyoKnj;

    /** サービス事業者略称 */
    private String svJigyoRyoku;

    /** 文字カラー */
    private String fontColor;

    /** 背景カラー */
    private String backColor;

    /** 時間表示区分 */
    private String timeKbn;

    /** 週単位以外のｻｰﾋﾞｽ区分 */
    private String igaiKbn;

    /** 週単位以外文字 */
    private String igaiMoji;

    /** 週単位以外のｻｰﾋﾞｽ（日付指定） */
    private String igaiDate;

    /** 週単位以外のｻｰﾋﾞｽ（曜日指定） */
    private String igaiWeek;

    /** 福祉用具貸与の単価 */
    private String svTani;

    /** 福祉用具貸与マスタID */
    private String fygId;

    /** 枠外表示するかのフラグ */
    private String wakugaiFlg;

    /** 週間計画加算リスト */
    private List<Gui01038Cks55> cks55List;

    /** 週間計画担当者リスト */
    private List<Gui01038Cks56> cks56List;

    /** 週間計画月日リスト */
    private List<Gui01038Cks57> cks57List;

    /** 週間計画隔週リスト */
    private List<Gui01038Cks58> cks58List;

    /** 連動項目リスト */
    private List<Gui01038Cks59> cks59List;

    /** 更新区分 */
    private String updateKbn;

}
