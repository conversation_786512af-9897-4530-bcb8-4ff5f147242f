package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00779SubInfoO;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentInterRAIOInitSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentInterRAIOInitSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucRaiRrkAssKeaByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucRaiRrkAssKeaOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucRaiRrkAssKeaSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.04.14
 * <AUTHOR>
 * @apiNote GUI00779_アセスメント(インターライ)画面O 初期情報取得
 */
@Service
public class AssessmentInterRAIOInitSelectServiceImpl extends
        SelectServiceImpl<AssessmentInterRAIOInitSelectServiceInDto, AssessmentInterRAIOInitSelectServiceOutDto> {
            
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /**サブ情報（O）取得*/
    @Autowired
    private CpnTucRaiRrkAssKeaSelectMapper cpnTucRaiRrkAssKeaSelectMapper;

    @Override
    protected AssessmentInterRAIOInitSelectServiceOutDto mainProcess(AssessmentInterRAIOInitSelectServiceInDto inDto)
            throws Exception {

        LOG.info(Constants.START);
        // 戻り情報を設定
        AssessmentInterRAIOInitSelectServiceOutDto out = new AssessmentInterRAIOInitSelectServiceOutDto();

        // サブ情報（O）
        GUI00779SubInfoO gUI00779SubInfoO = new GUI00779SubInfoO();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. サブ情報（O）取得===============
         * 
         */
        // 2.1. 下記のinterRAIｱｾｽﾒﾝﾄE認知情報取得のDAOを利用し、サブ情報（O）を取得する。
        CpnTucRaiRrkAssKeaByCriteriaInEntity cpnTucRaiRrkAssKeaInEntity = new CpnTucRaiRrkAssKeaByCriteriaInEntity();
        // アセスメントID
        cpnTucRaiRrkAssKeaInEntity.setAnRaiId(CommonDtoUtil.strValToInt(inDto.getRaiId()));
        // DAOを実行
        List<CpnTucRaiRrkAssKeaOutEntity> interRaiAsesumentAKihonList = this.cpnTucRaiRrkAssKeaSelectMapper
                .findCpnTucRaiRrkAssKeaByCriteria(cpnTucRaiRrkAssKeaInEntity);

        if (interRaiAsesumentAKihonList != null && interRaiAsesumentAKihonList.size() > 0) {
            // サブ情報（O）の編集
            BeanUtils.copyProperties(gUI00779SubInfoO, interRaiAsesumentAKihonList.get(0));
        }
        // サブ情報（O）
        out.setSubInfoO(gUI00779SubInfoO);
        LOG.info(Constants.END);
        return out;
    }
}
