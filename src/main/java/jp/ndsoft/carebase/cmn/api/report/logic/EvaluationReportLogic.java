package jp.ndsoft.carebase.cmn.api.report.logic;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.threeten.bp.LocalDateTime;
import org.threeten.bp.format.DateTimeFormatter;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonShiTeiDateParts;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportEvaluationKoumokuknjInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportEvaluationSoukatuknjInfo;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpU01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.EvaluationReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.model.EvaluationReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.ColumnCountFontSizeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ColumnCountFontSizeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMoni1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMoni1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMocFree2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMocFree2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMoniSvCustomByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMoniSvCustomOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YoushikiNoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YoushikiNoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocPrtYoushikiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMstChouhyouInkanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCmoni1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCmoni3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFree1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFree2SelectMapper;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @since 2025.08.18
 * <AUTHOR>
 * @description U01900_評価表 帳票出力
 */
@Component
public class EvaluationReportLogic {
    /* モニタリング記録表ヘッダ取得情報 */
    @Autowired
    private CpnTucCmoni1SelectMapper cpnTucCmoni1SelectMapper;
    /* 事業履歴より事業所名を取得する */
    @Autowired
    private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;
    @Autowired
    private KghCmpU01Logic kghCmpU01Logic;
    /* カスタマイズ帳票マスタヘッダデータ取得情報 */
    @Autowired
    private KghMocKrkFree2SelectMapper kghMocKrkFree2SelectMapper;
    /* モニタリングデータ（カスタマイズ版）情報取得 */
    @Autowired
    private CpnTucCmoni3SelectMapper cpnTucCmoni3SelectMapper;
    /* 印鑑欄設定情報取得 */
    @Autowired
    private CpnMstChouhyouInkanSelectMapper cpnMstChouhyouInkanSelectMapper;
    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;
    /** 文書番号情報取得 */
    @Autowired
    private ComMocPrtYoushikiSelectMapper comMocPrtYoushikiSelectMapper;
    @Autowired
    private KghMocKrkFree1SelectMapper kghMocKrkFree1SelectMapper;

    /**
     * 評価表パラメータ取得
     * 
     * @param model 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public EvaluationReportServiceInDto getEvaluationReportParameters(EvaluationReportParameterModel model)
            throws Exception {
        EvaluationReportServiceInDto inDto = new EvaluationReportServiceInDto();
        /* 帳票タイトル */
        inDto.setTitle(ReportConstants.TITLE_EVALUATION_REPORT);
        /* 事業所名 */
        // 事業所名を取得処理
        Integer svJigyoId = model.getJigyoInfo().getSvJigyoId().isEmpty() ? null
                : Integer.parseInt(model.getJigyoInfo().getSvJigyoId());
        String jigyoName = kghKrkZCpnFuncLogic.getJigyoRirekiKnj(svJigyoId, model.getAppYmd()).getJigyoRirekiRyakuKnj();
        inDto.setJigyoName(jigyoName);
        /* 敬称 */
        Boolean keishoFlg = false;
        if (CommonConstants.STR_1.equals(model.getPrintOption().getKeishoFlg())
                || CommonConstants.STR_TRUE.equals(model.getPrintOption().getKeishoFlg())) {
            keishoFlg = true;
        }
        if (keishoFlg) {
            inDto.setKeisho(model.getPrintOption().getKeisho());
        } else {
            // システム設定敬称を変更するフラグ
            String stKeishoFlg = model.getStKeishoFlg();
            // 初期設定マスタの情報の敬称
            String stKeisho = model.getStKeisho();
            Boolean sysKeishoFlg = false;
            if (CommonConstants.STR_1.equals(stKeishoFlg)
                    || CommonConstants.STR_TRUE.equals(stKeishoFlg)) {
                sysKeishoFlg = true;
            }
            if (sysKeishoFlg) {
                inDto.setKeisho(stKeisho);
            } else {
                inDto.setKeisho(ReportConstants.KEISHO_TONO);
            }
        }
        /* 総括部分を印刷するチェックフラグ */
        Boolean soukatsuFlg = false;
        if (CommonConstants.STR_1.equals(model.getPrintOption().getSoukatsuFlg())
                || CommonConstants.STR_TRUE.equals(model.getPrintOption().getSoukatsuFlg())) {
            soukatsuFlg = true;
        }
        inDto.setSoukatsuFlg(soukatsuFlg);
        /* 再アセスメントの必要性を印刷しないチェックフラグ */
        Boolean retryAssessmentPrintFlg = false;
        if (CommonConstants.STR_1.equals(model.getPrintOption().getRetryAssessmentPrintFlg())
                || CommonConstants.STR_TRUE.equals(model.getPrintOption().getRetryAssessmentPrintFlg())) {
            retryAssessmentPrintFlg = true;
        }
        inDto.setRetryAssessmentPrintFlg(retryAssessmentPrintFlg);
        /* 内容が同じ場合省略フラグ */
        Boolean hykkeisen1Flg = false;
        if (CommonConstants.STR_1.equals(model.getHykkeisen1Flg())
                || CommonConstants.STR_TRUE.equals(model.getHykkeisen1Flg())) {
            hykkeisen1Flg = true;
        }
        inDto.setHykkeisen1Flg(hykkeisen1Flg);
        /* 内容が空白の場合省略フラグ */
        Boolean hykkeisen2Flg = false;
        if (CommonConstants.STR_1.equals(model.getHykkeisen2Flg())
                || CommonConstants.STR_TRUE.equals(model.getHykkeisen2Flg())) {
            hykkeisen2Flg = true;
        }
        inDto.setHykkeisen2Flg(hykkeisen2Flg);
        /* 指定日印刷区分 */
        inDto.setShiTeiKubun(Integer.parseInt(model.getPrintSet().getShiTeiKubun()));
        /* 記入用シートを印刷するフラグ */
        Boolean emptyFlg = false;
        if (CommonConstants.STR_1.equals(model.getPrintOption().getEmptyFlg())
                || CommonConstants.STR_TRUE.equals(model.getPrintOption().getEmptyFlg())) {
            emptyFlg = true;
        }
        inDto.setEmptyFlg(emptyFlg);
        /*
         * ================== 1. 様式設定処理を呼び出し、様式を取得する ==================
         */
        // 1.表示件数の取得、フォントサイズの取得
        // 1-1 共通定数を定義する
        // ォントサイズ[] = { 9,10,11,12 }
        int[] fontSizeInts = new int[] { 9, 10, 11, 12 };

        // 1-2. 下記のアDAOを利用し、カスタマイズ帳票マスタヘッダデータ情報を取得する
        Integer fontSize = 3;
        // 行の分割数（上）
        Integer naiyoColumn = 7;
        ColumnCountFontSizeByCriteriaInEntity columnCountFontSizeByCriteriaInEntity = new ColumnCountFontSizeByCriteriaInEntity();
        columnCountFontSizeByCriteriaInEntity.setIiFree1Flg(CommonConstants.INT_MINUS_1);
        List<ColumnCountFontSizeOutEntity> lstColumnCountFontSizeOutEntity = kghMocKrkFree1SelectMapper
                .findColumnCountFontSizeByCriteria(columnCountFontSizeByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(lstColumnCountFontSizeOutEntity)) {
            var columnCountFontSizeOutEntity = lstColumnCountFontSizeOutEntity.getFirst();
            fontSize = columnCountFontSizeOutEntity.getFontSize();
            naiyoColumn = columnCountFontSizeOutEntity.getColumnCount();
        }
        inDto.setNaiyoColumn(naiyoColumn);

        // 1-3. フォントサイズ変換
        // 1-3-1. 処理1-2取得した印刷文字サイズ(font_size)より、下記分岐処理を行う
        switch (fontSize.intValue()) {
            case CommonConstants.INT_0:
                // 0(9ptの場合)、表示内容フォントサイズ(naiyoFontSize)が「9」を設定
                inDto.setNaiyoFontSize(String.valueOf(fontSizeInts[0]));
                break;
            case CommonConstants.INT_1:
                // 1(10ptの場合)、表示内容フォントサイズ(naiyoFontSize)が「10」を設定
                inDto.setNaiyoFontSize(String.valueOf(fontSizeInts[1]));
                break;
            case CommonConstants.INT_2:
                // 2(11ptの場合)、表示内容フォントサイズ(naiyoFontSize)が「11」を設定
                inDto.setNaiyoFontSize(String.valueOf(fontSizeInts[2]));
                break;
            case CommonConstants.INT_3:
                // 3(12ptの場合)、表示内容フォントサイズ(naiyoFontSize)が「11」を設定
                inDto.setNaiyoFontSize(String.valueOf(fontSizeInts[2]));
                break;
            default:
                // 上記以外の場合、表示内容フォントサイズ(naiyoFontSize)が「11」を設定
                inDto.setNaiyoFontSize(String.valueOf(fontSizeInts[2]));
                break;
        }

        /*
         * 1-4 様式の取得
         */
        List<Integer> koumokuknjWithList = new ArrayList<>();
        // 1-4-1. 下記のアDAOを利用し、カスタマイズ帳票マスタヘッダデータ情報を取得する
        KghKrkMocFree2ByCriteriaInEntity kghKrkMocFree2ByCriteriaInEntity = new KghKrkMocFree2ByCriteriaInEntity();
        kghKrkMocFree2ByCriteriaInEntity.setAiFree1Id(CommonConstants.INT_MINUS_1);
        kghKrkMocFree2ByCriteriaInEntity.setAiKbnId((CommonConstants.INT_1));
        List<KghKrkMocFree2OutEntity> lstKghKrkMocFree2OutEntity = kghMocKrkFree2SelectMapper
                .findKghKrkMocFree2ByCriteria(kghKrkMocFree2ByCriteriaInEntity);
        // 1-4-2. 取得した結果をループし、文字数(widthCnt)を各列文字幅リスト(koumokuknjWithList)に格納する。
        if (lstKghKrkMocFree2OutEntity.size() > 0) {
            for (KghKrkMocFree2OutEntity entity : lstKghKrkMocFree2OutEntity) {
                koumokuknjWithList.add(entity.getWidthCnt());
            }
        }
        /*
         * 1-5. ケアマネ_モニタリング_評価表_備考印刷がチェックオンの場合、下記処理を行う
         */
        // 1-5-1. 下記のアDAOを利用し、様式情報を取得する
        kghKrkMocFree2ByCriteriaInEntity = new KghKrkMocFree2ByCriteriaInEntity();
        kghKrkMocFree2ByCriteriaInEntity.setAiFree1Id(CommonConstants.INT_MINUS_1);
        kghKrkMocFree2ByCriteriaInEntity.setAiKbnId(CommonConstants.INT_2);
        lstKghKrkMocFree2OutEntity = kghMocKrkFree2SelectMapper
                .findKghKrkMocFree2ByCriteria(kghKrkMocFree2ByCriteriaInEntity);

        if (lstKghKrkMocFree2OutEntity.size() > 0) {
            // 1-5-2. 取得した結果の文字数(widthCnt)を各列文字幅リスト(koumokuknjWithList)に格納する。
            for (KghKrkMocFree2OutEntity entity : lstKghKrkMocFree2OutEntity) {
                koumokuknjWithList.add(entity.getWidthCnt());
            }
            // 1-5-3.
            inDto.setSoukatsuItemName(lstKghKrkMocFree2OutEntity.get(0).getNameKnj());
        }

        inDto.setKoumokuknjWithList(koumokuknjWithList);
        /*
         * ================== 2.リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合、
         * 結果レスポンスを返却する。※返却する情報の編集要領は「レスポンスパラメータ詳細」を参照する。
         * リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false の場合、ヘッダー情報の取得。
         * ==================
         */
        if (emptyFlg) {
            inDto = getEvaluationDefParameters(model, inDto);
        } else {
            inDto = getEvaluationParameters(model, inDto);
        }

        return inDto;
    }

    /**
     * リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
     * 
     * @param model 入力データ
     * @param inDto PDF帳票出力詳細データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private EvaluationReportServiceInDto getEvaluationDefParameters(EvaluationReportParameterModel model,
            EvaluationReportServiceInDto inDto) {
        /* 指定日 */
        inDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        inDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        inDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        inDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        /* 利用者名 */
        inDto.setRiyoushaNm(CommonConstants.EMPTY_STRING);
        /* 作成年月日 */
        inDto.setCreateYmdGG(CommonConstants.FULL_WIDTH_SPACE);
        inDto.setCreateYmdYY(CommonConstants.FULL_WIDTH_SPACE);
        inDto.setCreateYmdMM(CommonConstants.FULL_WIDTH_SPACE);
        inDto.setCreateYmdDD(CommonConstants.FULL_WIDTH_SPACE);
        /* 印鑑欄表示区分 */
        inDto.setHyoujiKbn(CommonConstants.INT_0);
        /* 印鑑1 */
        inDto.setHanko1Knj(CommonConstants.EMPTY_STRING);
        /* 印鑑2 */
        inDto.setHanko2Knj(CommonConstants.EMPTY_STRING);
        /* 印鑑3 */
        inDto.setHanko3Knj(CommonConstants.EMPTY_STRING);
        /* 印鑑4 */
        inDto.setHanko4Knj(CommonConstants.EMPTY_STRING);
        /* 印鑑5 */
        inDto.setHanko5Knj(CommonConstants.EMPTY_STRING);
        /* 印鑑6 */
        inDto.setHanko6Knj(CommonConstants.EMPTY_STRING);
        /* 印鑑7 */
        inDto.setHanko7Knj(CommonConstants.EMPTY_STRING);
        /* 印鑑8 */
        inDto.setHanko8Knj(CommonConstants.EMPTY_STRING);
        /* 印鑑9 */
        inDto.setHanko9Knj(CommonConstants.EMPTY_STRING);
        /* 印鑑10 */
        inDto.setHanko10Knj(CommonConstants.EMPTY_STRING);
        /* 印鑑11 */
        inDto.setHanko11Knj(CommonConstants.EMPTY_STRING);
        /* 印鑑12 */
        inDto.setHanko12Knj(CommonConstants.EMPTY_STRING);
        /* 印鑑13 */
        inDto.setHanko13Knj(CommonConstants.EMPTY_STRING);
        /* 印鑑14 */
        inDto.setHanko14Knj(CommonConstants.EMPTY_STRING);
        /* 印鑑15 */
        inDto.setHanko15Knj(CommonConstants.EMPTY_STRING);
        /* 表示内容格納リスト */
        List<ReportEvaluationKoumokuknjInfo> evaluationKoumokuknjInfoList = new ArrayList<>();
        Boolean soukatsuFlg = false;
        String emptyString = CommonConstants.EMPTY_STRING;
        if (CommonConstants.STR_1.equals(model.getPrintOption().getSoukatsuFlg())
                || CommonConstants.STR_TRUE.equals(model.getPrintOption().getSoukatsuFlg())) {
            soukatsuFlg = true;
        }
        if (!soukatsuFlg) {
            // 32空行を設定
            for (int i = 1; i < 32; i++) {
                emptyString += "\r\n";
            }
        } else {
            // 26空行を設定
            for (int i = 1; i < 26; i++) {
                emptyString += "\r\n";
            }
        }
        ReportEvaluationKoumokuknjInfo emptyKoumokuknjInfo = new ReportEvaluationKoumokuknjInfo();
        emptyKoumokuknjInfo.setKoumoku01Knj(emptyString);
        evaluationKoumokuknjInfoList.add(emptyKoumokuknjInfo);
        JRBeanCollectionDataSource evaluationKoumokuknjDs = new JRBeanCollectionDataSource(
                evaluationKoumokuknjInfoList);
        inDto.setKoumokuknjList(evaluationKoumokuknjDs);
        /* 総括表示内容格納リスト */
        emptyString = CommonConstants.EMPTY_STRING;
        List<ReportEvaluationSoukatuknjInfo> evaluationSoukatuknjInfoList = new ArrayList<>();
        if (soukatsuFlg) {
            // 3空行を設定
            for (int i = 1; i < 3; i++) {
                emptyString += "\r\n";
            }
        }
        ReportEvaluationSoukatuknjInfo evaluationSoukatuknjInfo = new ReportEvaluationSoukatuknjInfo();
        evaluationSoukatuknjInfo.setBiko(emptyString);
        evaluationSoukatuknjInfoList.add(evaluationSoukatuknjInfo);
        JRBeanCollectionDataSource evaluationSoukatuknjDs = new JRBeanCollectionDataSource(
                evaluationSoukatuknjInfoList);
        inDto.setSoukatuknjList(evaluationSoukatuknjDs);
        /* 再アセスメント予定日 */
        inDto.setYoteiYmdGG(CommonConstants.FULL_WIDTH_SPACE);
        inDto.setYoteiYmdYY(CommonConstants.FULL_WIDTH_SPACE);
        inDto.setYoteiYmdMM(CommonConstants.FULL_WIDTH_SPACE);
        inDto.setYoteiYmdDD(CommonConstants.FULL_WIDTH_SPACE);
        /* 実施予定日 */
        inDto.setYoteiYmdGG(CommonConstants.EMPTY_STRING);
        /* 文書管理番号 */
        inDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);
        return inDto;
    }

    /**
     * リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
     * 
     * @param model 入力データ
     * @param inDto PDF帳票出力詳細データ
     * @return PDF帳票パラメータ
     * @throws SecurityException
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     * @throws UnsupportedEncodingException
     * @throws Exception                    例外
     */
    private EvaluationReportServiceInDto getEvaluationParameters(EvaluationReportParameterModel model,
            EvaluationReportServiceInDto inDto)
            throws NoSuchMethodException, SecurityException, IllegalAccessException, InvocationTargetException,
            UnsupportedEncodingException {
        /*
         * 2.1. 下記のモニタリング記録表ヘッダ情報取得のDAOを利用し、モニタリング記録表ヘッダ情報を取得する。
         */
        CpnMoni1ByCriteriaInEntity cpnMoni1ByCriteriaInEntity = new CpnMoni1ByCriteriaInEntity();
        String strCmoni1Id = model.getPrintSubjectHistoryList().get(0).getCmoni1Id();
        String strSc1Id = model.getPrintSubjectHistoryList().get(0).getSc1Id();
        Integer cmoni1Id = null;
        Integer sc1Id = null;
        if (strCmoni1Id != null && !strCmoni1Id.trim().isEmpty()) {
            cmoni1Id = Integer.parseInt(strCmoni1Id);
        }
        if (strSc1Id != null && !strSc1Id.trim().isEmpty()) {
            sc1Id = Integer.parseInt(strSc1Id);
        }
        cpnMoni1ByCriteriaInEntity.setCmoni1(cmoni1Id);
        cpnMoni1ByCriteriaInEntity.setSc1(sc1Id);
        List<CpnMoni1OutEntity> lstCpnMoni1OutEntity = cpnTucCmoni1SelectMapper
                .findCpnMoni1ByCriteria(cpnMoni1ByCriteriaInEntity);
        /*
         * 2-2. 作成日の設定
         */
        String createymd = CommonConstants.EMPTY_STRING;
        String yoteiymd = CommonConstants.EMPTY_STRING;
        // 2-2-1. 処理2-1取得した結果の件数 > 0 の場合、た作成日(create_ymd)に設定する
        // 上記以外の場合、作成日を空にする
        if (lstCpnMoni1OutEntity.size() > 0) {
            createymd = lstCpnMoni1OutEntity.get(0).getCreateYmd();
            // 再アセスメントの必要
            Integer retryChk = lstCpnMoni1OutEntity.get(0).getRetryChk();
            inDto.setRetryChk(retryChk);
            // 再アセスメント予定日
            yoteiymd = lstCpnMoni1OutEntity.get(0).getYoteiYmd();
        }
        yoteiymd = kghCmpF01Logic.getCmpS2wEz(yoteiymd, CommonConstants.INT_1);
        if (!yoteiymd.isEmpty()) {
            inDto.setYoteiYmdGG(StringUtils.mid(yoteiymd, 0, 1));
            inDto.setYoteiYmdYY(StringUtils.mid(yoteiymd, 1, 2));
            inDto.setYoteiYmdMM(StringUtils.mid(yoteiymd, 4, 2));
            inDto.setYoteiYmdDD(StringUtils.mid(yoteiymd, 7, 2));
        } else {
            inDto.setYoteiYmdGG(CommonConstants.FULL_WIDTH_SPACE);
            inDto.setYoteiYmdYY(CommonConstants.FULL_WIDTH_SPACE);
            inDto.setYoteiYmdMM(CommonConstants.FULL_WIDTH_SPACE);
            inDto.setYoteiYmdDD(CommonConstants.FULL_WIDTH_SPACE);
        }
        // 2-2-2. 共通関数2-1 を呼び出しgetCmpS2wjEz(処理2-2-1取得した日付,1)、作成日を和暦に変換する
        createymd = kghCmpF01Logic.getCmpS2wjEz(createymd, CommonConstants.INT_1);
        if (!createymd.isEmpty()) {
            List<String> lstCreateYMD = ReportUtil.getJapaneseDateConvertList(createymd, CommonConstants.INT_1);
            inDto.setCreateYmdGG(lstCreateYMD.get(CommonConstants.INT_0));
            inDto.setCreateYmdYY(lstCreateYMD.get(CommonConstants.INT_1));
            inDto.setCreateYmdMM(lstCreateYMD.get(CommonConstants.INT_2));
            inDto.setCreateYmdDD(lstCreateYMD.get(CommonConstants.INT_3));
        } else {
            inDto.setCreateYmdGG(CommonConstants.FULL_WIDTH_SPACE);
            inDto.setCreateYmdYY(CommonConstants.FULL_WIDTH_SPACE);
            inDto.setCreateYmdMM(CommonConstants.FULL_WIDTH_SPACE);
            inDto.setCreateYmdDD(CommonConstants.FULL_WIDTH_SPACE);
        }

        /*
         * 2-3. 利用者名取得
         */
        // 共通関数3-1を呼び出して、利用者名を取得する
        String strUserId = model.getPrintSubjectHistoryList().get(0).getUserId();
        String riyoushaNm = CommonConstants.EMPTY_STRING;
        Integer userId = null;
        if (strUserId != null && !strUserId.trim().isEmpty()) {
            userId = Integer.parseInt(strSc1Id);
        }
        riyoushaNm = kghCmpU01Logic.ufGetName(userId);
        inDto.setRiyoushaNm(riyoushaNm);

        /*
         * 2-4. 利用者名のフォント設定
         */
        String riyoushaNmFont = CommonConstants.STR_11;
        // 2-4-1. 処理2-3取得した利用者名より、利用者名のフォントを設定する
        int riyoushaNmLength = riyoushaNm.length();
        if (riyoushaNmLength > 30) {
            riyoushaNmFont = CommonConstants.STR_7;
        } else if (riyoushaNmLength > 21) {
            riyoushaNmFont = CommonConstants.STR_9;
        }
        inDto.setRiyoushaNmFont(riyoushaNmFont);

        /*
         * 2-5. データ表示処理
         */
        // ======================= 別紙様式データ取得 =======================
        /*
         * 1-1. 様式データの取得
         * 1-1-1. 下記のアDAOを利用し、カスタマイズ帳票マスタヘッダデータ情報を取得する
         */
        KghKrkMocFree2ByCriteriaInEntity kghKrkMocFree2ByCriteriaInEntity = new KghKrkMocFree2ByCriteriaInEntity();
        kghKrkMocFree2ByCriteriaInEntity.setAiFree1Id(CommonConstants.INT_MINUS_1);
        kghKrkMocFree2ByCriteriaInEntity.setAiKbnId(CommonConstants.INT_1);
        List<KghKrkMocFree2OutEntity> lstKghKrkMocFree2OutEntity = kghMocKrkFree2SelectMapper
                .findKghKrkMocFree2ByCriteria(kghKrkMocFree2ByCriteriaInEntity);

        /*
         * 1-2. 別紙様式設定処理シートの処理1-2取得した行の分割数（上）(column_count)までループし、下記処理を行う
         */        
        int column_count = 7;
        ColumnCountFontSizeByCriteriaInEntity columnCountFontSizeByCriteriaInEntity = new ColumnCountFontSizeByCriteriaInEntity();
        columnCountFontSizeByCriteriaInEntity.setIiFree1Flg(CommonConstants.INT_MINUS_1);
        List<ColumnCountFontSizeOutEntity> lstColumnCountFontSizeOutEntity = kghMocKrkFree1SelectMapper
                .findColumnCountFontSizeByCriteria(columnCountFontSizeByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(lstColumnCountFontSizeOutEntity)) {
            var columnCountFontSizeOutEntity = lstColumnCountFontSizeOutEntity.getFirst();
            column_count = columnCountFontSizeOutEntity.getColumnCount();
        }

        // 変数リスト.最大バイト数 = 処理1-1-1取得した文字数(width_cnt)の二倍
        List<Integer> lstWidthCnt = new ArrayList<>();
        // 変数リスト.入力区分 = 処理1-1-1取得した入力方法(input_kbn)
        List<Integer> lstInputKbn = new ArrayList<>();
        for (KghKrkMocFree2OutEntity entity : lstKghKrkMocFree2OutEntity) {
            Integer widthCnt = entity.getWidthCnt() != null ? entity.getWidthCnt() * 2 : 0;
            lstWidthCnt.add(widthCnt);
            lstInputKbn.add(entity.getInputKbn());
        }

        /*
         * 1-3. 表示データの取得
         */
        // 1-3-1. 下記DAOを利用し、モニタリングデータ（カスタマイズ版）情報取得する
        KghKrkMoniSvCustomByCriteriaInEntity kghKrkMoniSvCustomByCriteriaInEntity = new KghKrkMoniSvCustomByCriteriaInEntity();
        kghKrkMoniSvCustomByCriteriaInEntity.setAiCmoni1Id(cmoni1Id);
        // 上部
        kghKrkMoniSvCustomByCriteriaInEntity.setAiKbnId(CommonConstants.INT_1);
        List<KghKrkMoniSvCustomOutEntity> lstKghKrkMoniSvCustomOutEntity = cpnTucCmoni3SelectMapper
                .findKghKrkMoniSvCustomByCriteria(kghKrkMoniSvCustomByCriteriaInEntity);

        // 1-3-2. 取得結果件数>0の場合、表示順(sort)でソート(昇順)する
        if (lstKghKrkMoniSvCustomOutEntity.size() > 0) {
            lstKghKrkMoniSvCustomOutEntity = lstKghKrkMoniSvCustomOutEntity.stream()
                    .sorted(Comparator.comparing(KghKrkMoniSvCustomOutEntity::getSort))
                    .collect(Collectors.toList());
        }

        // 1-4. ループ1：処理1-3取得結果までをループし、下記処理を行う
        List<ReportEvaluationKoumokuknjInfo> lstReportEvaluationKoumokuknjInfo = new ArrayList<>();
        for (int loop1 = 0; loop1 < lstKghKrkMoniSvCustomOutEntity.size(); loop1++) {
            KghKrkMoniSvCustomOutEntity entity = lstKghKrkMoniSvCustomOutEntity.get(loop1);
            ReportEvaluationKoumokuknjInfo koumokuknjInfo = new ReportEvaluationKoumokuknjInfo();
            Class<?> outEntityClazz = entity.getClass();
            Class<?> koumokuknjInfoClazz = koumokuknjInfo.getClass();
            // 1-4-1. ループ2：別紙様式設定処理シートの処理1-2取得した行の分割数（上）(column_count)までループし、最大行数取得処理を行う
            for (int loop2 = 0; loop2 < column_count; loop2++) {
                String strloop = CommonConstants.EMPTY_STRING;
                if (loop2 < 10) {
                    strloop = CommonConstants.STR_0 + (loop2 + 1);
                } else {
                    strloop = String.valueOf(loop2 + 1);
                }
                Method getKomokuKnjMethod = outEntityClazz.getMethod("getKoumoku" + strloop + "Knj");
                Method getKomokuCodMethod = outEntityClazz.getMethod("getKoumoku" + strloop + "Cod");
                Method getKomokuYmdMethod = outEntityClazz.getMethod("getKoumoku" + strloop + "Ymd");
                Method setKomokuKnjMethod = koumokuknjInfoClazz.getMethod("setKoumoku" + strloop + "Knj", String.class);
                Method getKomokuKnjInfoMethod = koumokuknjInfoClazz.getMethod("getKoumoku" + strloop + "Knj");
                String komokuKnj = (String) getKomokuKnjMethod.invoke(entity);
                int komokuCod = (Integer) getKomokuCodMethod.invoke(entity);
                String komokuYmd = (String) getKomokuYmdMethod.invoke(entity);
                // 1-4-1-1. 表示文字を取得処理を行う
                switch (lstInputKbn.get(loop2)) {
                    /*
                     * 1-4-1-1-1. 変数リスト.入力区分[ループ2変数]が「1」文章 或いは「2」数値 或いは 「4」マスタ＋文章の場合、下記処理を行う
                     * ・処理1-3で取得した結果に index=ループ1変数 の "komoku" + ループ2変数(二桁) + "_knj"
                     * を表示内容として取得し、表示内容格納リスト[ループ2変数] に 設定する
                     */
                    case 1, 2, 4:
                        setKomokuKnjMethod.invoke(koumokuknjInfo, komokuKnj);
                        break;
                    /*
                     * 1-4-1-1-2. 変数リスト.入力区分[ループ2変数]が「3」マスタの場合、下記処理を行う
                     * ・処理1-3で取得した結果に index=ループ1変数 の "komoku" + ループ2変数(二桁) + "_cod" を取得する
                     * ・マークに変換し、表示内容格納リスト[ループ2変数]の"komoku" + ループ2変数(二桁) + "_knj" に 設定する
                     * ※"◎","○","△", "×1", "×2"変換処理
                     */
                    case 3:
                        switch (komokuCod) {
                            case 1:
                                setKomokuKnjMethod.invoke(koumokuknjInfo, ReportConstants.STR_EVALUATION_CIRCLE);
                                break;
                            case 2:
                                setKomokuKnjMethod.invoke(koumokuknjInfo, ReportConstants.STR_EVALUATION_NAUGHT);
                                break;
                            case 3:
                                setKomokuKnjMethod.invoke(koumokuknjInfo, ReportConstants.STR_EVALUATION_TRIANGLE);
                                break;
                            case 4:
                                setKomokuKnjMethod.invoke(koumokuknjInfo, ReportConstants.STR_EVALUATION_TIMESONE);
                                break;
                            case 5:
                                setKomokuKnjMethod.invoke(koumokuknjInfo, ReportConstants.STR_EVALUATION_TIMESTWO);
                                break;
                        }
                        break;
                    /*
                     * 1-4-1-1-3. 変数リスト.入力区分[ループ2変数]が「5」日付の場合、下記処理を行う
                     * ・処理1-3で取得した結果に index=ループ1変数 の "komoku" + ループ2変数(二桁) + "_ymd" を表示内容として取得する
                     * ・共通関数1-1呼び出しgetCmpS2wEz(当処理取得した日付,1)、和暦に変換し、表示内容格納リスト[ループ2変数] に 設定する
                     */
                    case 5:
                        String ymd = kghCmpF01Logic.getCmpS2wEz(komokuYmd, CommonConstants.INT_1);
                        setKomokuKnjMethod.invoke(koumokuknjInfo, ymd);
                        break;
                }
                // 1-4-1-1-4.
                // 共通関数補足の4.1cmpLtext2Rows(表示内容格納リスト[ループ2変数],変数リスト.最大バイト数[ループ2変数])を行う、戻り値.変換結果文字配列リストを改行で結合する。
                int maxWidth = lstWidthCnt.get(loop2);
                String komokuKnjInfo = (String) getKomokuKnjInfoMethod.invoke(koumokuknjInfo);
                var pair = kghCmpF01Logic.cmpLtext2Rows(komokuKnjInfo, maxWidth);
                List<String> lstResult = pair.getRight();
                String result = CommonConstants.EMPTY_STRING;
                for (int i = 0; i < lstResult.size(); i++) {
                    if (i == lstResult.size() - 1) {
                        result += lstResult.get(i);
                    } else {
                        result += lstResult.get(i) + ReportConstants.LINE_BREAKS;
                    }
                }
                setKomokuKnjMethod.invoke(koumokuknjInfo, result);
            }
            lstReportEvaluationKoumokuknjInfo.add(koumokuknjInfo);
        }
        JRBeanCollectionDataSource koumokuknjInfoDs = new JRBeanCollectionDataSource(lstReportEvaluationKoumokuknjInfo);
        inDto.setKoumokuknjList(koumokuknjInfoDs);

        /*
         * 1-5. リクエストパラメータ.データ.印刷オプション.総括部分を印刷するチェックフラグがチェックオンの場合、下記処理を行う
         */
        // 1-5-1. 下記のアDAOを利用し、カスタマイズ帳票マスタヘッダデータ情報を取得する
        kghKrkMocFree2ByCriteriaInEntity = new KghKrkMocFree2ByCriteriaInEntity();
        kghKrkMocFree2ByCriteriaInEntity.setAiFree1Id(CommonConstants.INT_MINUS_1);
        kghKrkMocFree2ByCriteriaInEntity.setAiKbnId(CommonConstants.INT_2);
        lstKghKrkMocFree2OutEntity = kghMocKrkFree2SelectMapper
                .findKghKrkMocFree2ByCriteria(kghKrkMocFree2ByCriteriaInEntity);

        // 1-5-2. 変数.最大バイト数 = 処理1-5-1取得した文字数(width_cnt)の二倍に設定し、総括項目ヘッダー幅に設定する
        int maxWidth = 0;
        if (lstKghKrkMocFree2OutEntity.size() > 0) {
            maxWidth = lstKghKrkMocFree2OutEntity.get(0).getWidthCnt() * 2;
        }

        // 1-5-3. 下記DAOを利用し、モニタリングデータ（カスタマイズ版）情報取得する
        kghKrkMoniSvCustomByCriteriaInEntity = new KghKrkMoniSvCustomByCriteriaInEntity();
        kghKrkMoniSvCustomByCriteriaInEntity.setAiCmoni1Id(cmoni1Id);
        // 下部
        kghKrkMoniSvCustomByCriteriaInEntity.setAiKbnId(CommonConstants.INT_2);
        lstKghKrkMoniSvCustomOutEntity = cpnTucCmoni3SelectMapper
                .findKghKrkMoniSvCustomByCriteria(kghKrkMoniSvCustomByCriteriaInEntity);

        // 1-5-4.
        // 共通関数補足の4.1cmpLtext2Rows(処理1-5-2取得したkoumoku01_knj,変数.最大バイト数)を行う、戻り値.変換結果文字配列リストを改行で結合して、総括表示内容格納リスト(soukatuknjlist) に 格納する
        List<ReportEvaluationSoukatuknjInfo> lstReportEvaluationSoukatuknjInfo = new ArrayList<>();
        if (lstKghKrkMoniSvCustomOutEntity.size() > 0) {
            ReportEvaluationSoukatuknjInfo info = new ReportEvaluationSoukatuknjInfo();
            String koumoku01Knj = lstKghKrkMoniSvCustomOutEntity.get(0).getKoumoku01Knj();
            var pair = kghCmpF01Logic.cmpLtext2Rows(koumoku01Knj, maxWidth);
            List<String> lstResult = pair.getRight();
            String result = CommonConstants.EMPTY_STRING;
            for (int i = 0; i < lstResult.size(); i++) {
                if (i == lstResult.size() - 1) {
                    result += lstResult.get(i);
                } else {
                    result += lstResult.get(i) + ReportConstants.LINE_BREAKS;
                }
            }
            info.setBiko(result);
            lstReportEvaluationSoukatuknjInfo.add(info);
        }
        JRBeanCollectionDataSource soukatuknjInfoDs = new JRBeanCollectionDataSource(lstReportEvaluationSoukatuknjInfo);
        inDto.setSoukatuknjList(soukatuknjInfoDs);

        /*
         * ==================== 3. 印鑑欄設定情報を取得する。 ====================
         */
        // 3.1.リクエストパラメータ.印鑑欄を表示するフラグ = trueとリクエストパラメータ.記入用シートを印刷するフラグ = false
        // の場合、印鑑欄設定情報の取得
        // 下記の28-xx ケアプラン帳票印鑑欄情報取得のDAOを利用し、印鑑欄設定情報を取得する。
        KghCpnMstChouhyouInkanPrnByCriteriaInEntity kghCpnMstChouhyouInkanPrnByCriteriaInEntity = new KghCpnMstChouhyouInkanPrnByCriteriaInEntity();
        // 法人ID
        String strHoujinId = model.getJigyoInfo().getHoujinId();
        Integer houjinId = strHoujinId != null && !strHoujinId.isEmpty() ? Integer.parseInt(strHoujinId)
                : CommonConstants.INT_0;
        kghCpnMstChouhyouInkanPrnByCriteriaInEntity.setAnKey1(houjinId);
        // 施設ID
        String strShisetuId = model.getJigyoInfo().getShisetuId();
        Integer shisetuId = strShisetuId != null && !strShisetuId.isEmpty() ? Integer.parseInt(strShisetuId)
                : CommonConstants.INT_0;
        kghCpnMstChouhyouInkanPrnByCriteriaInEntity.setAnKey2(shisetuId);
        // 事業所ID
        String strSvJigyoId = model.getJigyoInfo().getSvJigyoId();
        Integer svJigyoId = strSvJigyoId != null && !strSvJigyoId.isEmpty() ? Integer.parseInt(strSvJigyoId)
                : CommonConstants.INT_0;
        kghCpnMstChouhyouInkanPrnByCriteriaInEntity.setAnKey3(svJigyoId);
        // 帳票セクション番号
        kghCpnMstChouhyouInkanPrnByCriteriaInEntity
                .setAsSec(model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection());
        var lstKghCpnMstChouhyouInkanPrnOutEntity = cpnMstChouhyouInkanSelectMapper
                .findKghCpnMstChouhyouInkanPrnByCriteria(kghCpnMstChouhyouInkanPrnByCriteriaInEntity);
        if (lstKghCpnMstChouhyouInkanPrnOutEntity.size() > 0) {
            var hyoujiEntity = lstKghCpnMstChouhyouInkanPrnOutEntity.get(0);
            // 印鑑欄表示区分
            inDto.setHyoujiKbn(hyoujiEntity.getHyoujiKbn());
            // 印鑑1
            inDto.setHanko1Knj(hyoujiEntity.getHanko1Knj());
            // 印鑑2
            inDto.setHanko2Knj(hyoujiEntity.getHanko2Knj());
            // 印鑑3
            inDto.setHanko3Knj(hyoujiEntity.getHanko3Knj());
            // 印鑑4
            inDto.setHanko4Knj(hyoujiEntity.getHanko4Knj());
            // 印鑑5
            inDto.setHanko5Knj(hyoujiEntity.getHanko5Knj());
            // 印鑑6
            inDto.setHanko6Knj(hyoujiEntity.getHanko6Knj());
            // 印鑑7
            inDto.setHanko7Knj(hyoujiEntity.getHanko7Knj());
            // 印鑑8
            inDto.setHanko8Knj(hyoujiEntity.getHanko8Knj());
            // 印鑑9
            inDto.setHanko9Knj(hyoujiEntity.getHanko9Knj());
            // 印鑑10
            inDto.setHanko10Knj(hyoujiEntity.getHanko10Knj());
            // 印鑑11
            inDto.setHanko11Knj(hyoujiEntity.getHanko11Knj());
            // 印鑑12
            inDto.setHanko12Knj(hyoujiEntity.getHanko12Knj());
            // 印鑑13
            inDto.setHanko13Knj(hyoujiEntity.getHanko13Knj());
            // 印鑑14
            inDto.setHanko14Knj(hyoujiEntity.getHanko14Knj());
            // 印鑑15
            inDto.setHanko15Knj(hyoujiEntity.getHanko15Knj());
            // 指定日設定
            String shiTeiKubun = model.getPrintSet().getShiTeiKubun();
            String shiTeiDate = model.getPrintSet().getShiTeiDate();
            String systemDate = model.getSystemDate();
            ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(shiTeiKubun, shiTeiDate, systemDate);
            // 指定日（年号）
            inDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
            // 指定日（年）
            inDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
            // 指定日（月）
            inDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
            // 指定日（日）
            inDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());
        }
        // 文書管理番号
        inDto.setBunsyoKanriNo(
                this.getBunsyoKanriNo(model.getSyscd(),
                        model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getShokuId(),
                        model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection(),
                        strSvJigyoId));

        return inDto;
    }

    /**
     * 指定日を取得する
     * 
     * @param shiTeiKubun 指定日印刷区分
     * @param shiTeiDate  西暦日付
     * @param systemDate  システム日付
     * @throws Exception 例外
     */
    private ReportCommonShiTeiDateParts getShiTeiDate(String shiTeiKubun, String shiTeiDate, String systemDate) {
        // 指定日（年号）=""
        String shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（年）=""
        String shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（月）=""
        String shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（日）=""
        String shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日印刷区分判定
        switch (shiTeiKubun) {
            case ReportConstants.PRINTDATE_KBN_SHINAI:
                // 指定日印刷区分 = 1 の場合
                // 指定日（年号）=""
                shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（年）=""
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）=""
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）=""
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            case ReportConstants.PRINTDATE_KBN_PRINT:
                // 指定日印刷区分 = 2 の場合
                // 西暦日付を和暦日付に変換する
                List<String> dateParts = ReportUtil.getLocalDateToJapanDateTimeFormat(shiTeiDate);
                shiTeiDateGG = dateParts.get(ReportConstants.SHITEIDATE_GG);
                // 指定日（年）=""
                shiTeiDateYY = dateParts.get(ReportConstants.SHITEIDATE_YY);
                // 指定日（月）=""
                shiTeiDateMM = dateParts.get(ReportConstants.SHITEIDATE_MM);
                // 指定日（日）=""
                shiTeiDateDD = dateParts.get(ReportConstants.SHITEIDATE_DD);
                break;
            case ReportConstants.PRINTDATE_KBN_BLANKDATE:
                // 指定日印刷区分 = 3 の場合
                // 指定日の空欄表示処理
                DateTimeFormatter inputFormatter = DateTimeFormatter
                        .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
                DateTimeFormatter outputFormatter = DateTimeFormatter
                        .ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
                LocalDateTime dateTime = LocalDateTime.parse(systemDate, inputFormatter);
                String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
                // 指定日（年号）=""
                shiTeiDateGG = blankDate.substring(0, 2);
                // 指定日（年）=""
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）=""
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）=""
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            default:
                // 指定日（年号）=""
                shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（年）=""
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）=""
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）=""
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
        }
        return new ReportCommonShiTeiDateParts(shiTeiDateGG, shiTeiDateYY, shiTeiDateMM, shiTeiDateDD);
    }

    /**
     * 文書番号情報取得処理。
     * 
     * @param sysCode    システムコード
     * @param shokuId    職員ID
     * @param sectionKnj セクション
     * @param svJigyoId  サービス事業者ID
     * @return アセスメント種別
     */
    public String getBunsyoKanriNo(String sysCode, String shokuId, String sectionKnj, String svJigyoId) {
        String bunsyoKanriNo = CommonConstants.BLANK_STRING;
        // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01Logic 関数名：GetF3GkProfile を利用し、
        // 設定の読込（システム環境以外の設定）情報を取得する。
        F3gkGetProfileInDto f3gkGetProfileInDto = new F3gkGetProfileInDto();
        f3gkGetProfileInDto.setGsyscd(sysCode);
        f3gkGetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(shokuId));
        f3gkGetProfileInDto.setHoujinId(CommonConstants.HOUJIN_ID_0);
        f3gkGetProfileInDto.setShisetuId(CommonConstants.SHISETU_ID);
        f3gkGetProfileInDto.setSvJigyoId(CommonConstants.SV_JIGYO_ID_0);
        f3gkGetProfileInDto.setKinounameKnj(CommonConstants.KINOU_NAME_PRT);
        f3gkGetProfileInDto.setSectionKnj(sectionKnj);
        f3gkGetProfileInDto.setKeyKnj(CommonConstants.ISO9001_FLG);
        f3gkGetProfileInDto.setAsDefault(CommonConstants.STR_DEFAULTDE_0);
        String f3gkProfile = nds3GkFunc01Logic.getF3gkProfile(f3gkGetProfileInDto);

        // 2.2. 設定の読込（システム環境以外の設定）情報取得の戻り値は"1"（正常参照）の場合、下記の24-06
        // 帳票書式番号設定マスタ情報取得のDAOを利用し、文書番号を取得する。
        if (CommonConstants.STR_1.equalsIgnoreCase(f3gkProfile)) {
            YoushikiNoByCriteriaInEntity youshikiNoByCriteriaInEntity = new YoushikiNoByCriteriaInEntity();
            youshikiNoByCriteriaInEntity.setAsSection(sectionKnj);
            youshikiNoByCriteriaInEntity.setLlSvJigyoId(svJigyoId);
            List<YoushikiNoOutEntity> youshikiNoOutList = comMocPrtYoushikiSelectMapper
                    .findYoushikiNoByCriteria(youshikiNoByCriteriaInEntity);
            if (CollectionUtils.isNotEmpty(youshikiNoOutList)) {
                bunsyoKanriNo = ReportUtil.nullToEmpty(youshikiNoOutList.get(0).getYoushikiNo());
            }
        }
        return bunsyoKanriNo;
    }
}