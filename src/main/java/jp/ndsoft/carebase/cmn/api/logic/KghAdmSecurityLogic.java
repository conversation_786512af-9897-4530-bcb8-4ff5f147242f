package jp.ndsoft.carebase.cmn.api.logic;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GetUseKinouJigyoDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KghAdmSecSettingDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkChkLicenseOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KengenKinouSingleInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KengenSvjigyoInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KengenSvjigyoOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KinouIdOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KinouSecurityDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.StrKghAdmSecurityUseDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.AdmSecKengenFuncGrpSvJigyoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AdmSecKengenFuncGrpSvJigyoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AdmSecKengenShkSvjigyoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AdmSecKengenShkSvjigyoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GroupId054ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GroupId054OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GroupKinoKenkenByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GroupKinoKenkenOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GroupKumiAwaseKinoKenkenByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GroupKumiAwaseKinoKenkenOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GroupKumiawaseByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GroupKumiawaseOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.HojinNaigaiKubunByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.HojinNaigaiKubunOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KenkenFlgKinoKensuGroupByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KenkenFlgKinoKensuGroupOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KenkenFlgKinoKensukoujinByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KenkenFlgKinoKensukoujinOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecGroupShk2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecGroupShk2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecGroupShkByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecGroupShkOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecKengenFuncGrpUseKinou22DSByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecKengenFuncGrpUseKinou22DSOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecKengenFuncGrpUseKinou32AuthFlgByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecKengenFuncGrpUseKinou32AuthFlgOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecKengenFuncGrpUseKinou3ChkByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecKengenFuncGrpUseKinou3ChkOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecKengenFuncGrpUseKinou3KinoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecKengenFuncGrpUseKinou3KinoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecKengenFuncGrpUseKinouDSByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecKengenFuncGrpUseKinouDSOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecKengenFuncShkUseKinou4ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecKengenFuncShkUseKinou4OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecKengenFuncShkUseKinouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecKengenFuncShkUseKinouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecurity6ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecurity6OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecurityByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SystemKubunKinouIDByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SystemKubunKinouIDOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TopKghAdmSecurityByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TopKghAdmSecurityOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinKinoKensuGroupKbn0ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinKinoKensuGroupKbn0OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinKinoKensuGroupKbn2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinKinoKensuGroupKbn2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinKinoKensuNameByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinKinoKensuNameOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinKinoKensuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinYuukoAdminFlgByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinYuukoAdminFlgOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.OyaKinouIDKinouknjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.OyaKinouIDKinouknjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ParentIdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ParentIdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinKinoKenkenByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinKinoKenkenOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SysCdKbnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SysCdKbnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghAdmSecurityOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghComGetMenuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KinoKubenKinoIDByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KinoKubenKinoIDOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinKinoKensuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocKinouNameSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSecGroupApplyIdSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSecGroupApplyInfoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSecSettingSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSecShkKinouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocShkUseKinou4SecKengenInfoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocShkUseKinouSecKengenInfoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscPasswordSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.GroupKinouKengenKansuuyo3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.GroupKinouKengenKansuuyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.GroupKyoutuuKinoKengen2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.GroupKumiawaseKinoKengen2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.HojinNaigaiKubenSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.GroupKyoutuuKinoKengen5SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.GroupKyoutuuKinoKengenSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.GroupKumiawaseKinoKengen5SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.GroupKumiawaseKinoKengenSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KojinKinoKengenSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.SecKengenFuncGrpUseKinou2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ShozokuSecurityGroupSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSecGroupKumiawaseSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghAdmSecKengenShkSvjigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KinouPermissionFunc2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KinouPermissionFuncSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghAdmSecKengenFuncGrpSvJigyoSelectMapper;

/**
 * KghAdmSecurityLogicロジッククラス
 * 
 * <AUTHOR>
 */
@Component
public class KghAdmSecurityLogic {
    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;
    /** KghAdmSecPafLogicロジッククラス */
    @Autowired
    private KghAdmSecPafLogic kghAdmSecPafLogic;
    /** KghBase01Logicロジッククラス */
    @Autowired
    private KghBase01Logic kghBase01Logic;
    /** 機能管理情報取得のマッパーです. */
    @Autowired
    private ComMocKinouNameSelectMapper comMocKinouNameSelectMapper;
    /** 03-02 職員アカウント（パスワード）情報取得のマッパーです. */
    @Autowired
    private ComMscPasswordSelectMapper comMscPasswordSelectMapper;
    @Autowired
    private ComMocSecGroupApplyInfoSelectMapper comMocSecGroupApplyInfoSelectMapper;
    @Autowired
    /** セキュリティ基本設定情報取得のマッパーです. */
    private ComMocSecSettingSelectMapper comMocSecSettingSelectMapper;
    /** 個人機能権限情報取得のマッパーです. */
    @Autowired
    private KojinKinoKengenSelectMapper kojinKinoKengenSelectMapper;
    /** グループ共通機能権限2情報取得のマッパーです. */
    @Autowired
    private GroupKyoutuuKinoKengen2SelectMapper groupKyoutuuKinoKengen2SelectMapper;
    /** グループ共通機能権限情報取得のマッパーです. */
    @Autowired
    private GroupKyoutuuKinoKengenSelectMapper groupKyoutuuKinoKengenSelectMapper;
    /** グループ組み合わせ機能権限2情報取得のマッパーです. */
    @Autowired
    private GroupKumiawaseKinoKengen2SelectMapper groupKumiawaseKinoKengen2SelectMapper;
    /** グループ組み合わせ機能権限情報取得のマッパーです. */
    @Autowired
    private GroupKumiawaseKinoKengenSelectMapper groupKumiawaseKinoKengenSelectMapper;
    /** 法人内外区分情報取得のマッパーです. */
    @Autowired
    private HojinNaigaiKubenSelectMapper hojinNaigaiKubenSelectMapper;
    /** セキュリティ権限（職員-機能権限）情報取得のマッパーです. */
    @Autowired
    private ComMocSecShkKinouSelectMapper comMocSecShkKinouSelectMapper;
    /** グループ共通機能権限5情報取得のマッパーです. */
    @Autowired
    private GroupKyoutuuKinoKengen5SelectMapper groupKyoutuuKinoKengen5SelectMapper;
    /** グループ組み合わせ機能権限5情報取得のマッパーです. */
    @Autowired
    private GroupKumiawaseKinoKengen5SelectMapper groupKumiawaseKinoKengen5SelectMapper;
    /** 職員所属セキュリティグループID件数情報取得のマッパーです. */
    @Autowired
    private ComMocSecGroupApplyIdSelectMapper comMocSecGroupApplyIdSelectMapper;
    /** 職員所属セキュリティグループ組合件数情報取得のマッパーです. */
    @Autowired
    private ComMocSecGroupKumiawaseSelectMapper comMocSecGroupKumiawaseSelectMapper;
    /** 職員事業所権限情報取得のマッパーです. */
    @Autowired
    private KghAdmSecKengenShkSvjigyoSelectMapper kghAdmSecKengenShkSvjigyoSelectMapper;
    /** 関数用グループ事業所権限情報取得のマッパーです. */
    @Autowired
    private KghAdmSecKengenFuncGrpSvJigyoSelectMapper kghAdmSecKengenFuncGrpSvJigyoSelectMapper;
    /** 事業者セキュリティ権限機能. */
    @Autowired
    private ComMocShkUseKinouSecKengenInfoSelectMapper comMocShkUseKinouSecKengenInfoSelectMapper;
    /** 事業者セキュリティ権限機能4. */
    @Autowired
    private ComMocShkUseKinou4SecKengenInfoSelectMapper comMocShkUseKinou4SecKengenInfoSelectMapper;
    /** 所属グループ(DS) */
    @Autowired
    private ShozokuSecurityGroupSelectMapper shozokuSecurityGroupSelectMapper;
    /** セキュリティ権限機能(関数用)情報取得 */
    @Autowired
    private KinouPermissionFuncSelectMapper KinouPermissionFuncSelectMapper;
    /** 事業機能管理 */
    @Autowired
    private KinouPermissionFunc2SelectMapper kinouPermissionFunc2SelectMapper;

    /**  */
    @Autowired
    private SecKengenFuncGrpUseKinou2SelectMapper secKengenFuncGrpUseKinou2SelectMapper;
    /**  */
    @Autowired
    private GroupKinouKengenKansuuyoSelectMapper groupKinouKengenKansuuyoSelectMapper;

    /**  */
    @Autowired
    private GroupKinouKengenKansuuyo3SelectMapper groupKinouKengenKansuuyo3SelectMapper;

    /**
     * ログインした職員のシステムで、利用可能な事業所かチェックする.
     *
     * <p>
     * 【7】
     *
     * @param shokuId 職員ID.
     * @param sysCd   システムコード.
     * @param kinouId 機能ID.
     * @param jigyoId サービス事業者ID.
     * @return チェック結果.
     * @throws Exception 例外.
     * <AUTHOR>
     */
    public int chkUseJigyo(int shokuId, String sysCd, int kinouId, int jigyoId)
            throws Exception {
        // 戻り値の初期値をセットする
        int result = CommonConstants.INT_0;

        // 権限チェック
        // 管理者権限を取得する
        int passKengen = ufPassKengen(shokuId);

        // 上記取得した管理者権限フラグが「1：あり」の場合
        if (passKengen == CommonConstants.INT_1) {
            // 【戻リ値】.チェック結果に「1：使用可」を設定して返却する
            result = CommonConstants.INT_1;
            return result;
        }

        // 【潜在バグ】他事業所の権限を解放する
        HojinNaigaiKubunByCriteriaInEntity inEntity1 = new HojinNaigaiKubunByCriteriaInEntity();
        // 事業所ID
        inEntity1.setAlJigyo(CommonDtoUtil.objValToString(jigyoId));
        List<HojinNaigaiKubunOutEntity> hojinNaigaiKubunOutEntityList = hojinNaigaiKubenSelectMapper
                .findHojinNaigaiKubunByCriteria(inEntity1);
        int inoutKbn = CommonConstants.INT_0;
        if (CollectionUtils.isNotEmpty(hojinNaigaiKubunOutEntityList)) {
            inoutKbn = hojinNaigaiKubunOutEntityList.get(0).getInoutKbn();
        }

        // 上記で取得した法人内外区分が「0」の場合
        if (inoutKbn == CommonConstants.INT_0) {
            // 【戻り値】.チェック結果に「1：使用可能」を設定して返却する
            result = CommonConstants.INT_1;
            ;
            return result;
        }

        // 職員IDが0の場合は強制的に個人単位で取得する
        if (shokuId == 0) {
            return CommonConstants.INT_1;
        }

        // セキュリティ基本の設定を取得する
        KghAdmSecSettingDto secSetting = getSecSetting();
        // 上記取得したシステム別事業所制限が「0：なし」の場合
        if (secSetting.getSystemSvjigyoFlg() == CommonConstants.INT_0) {
            kinouId = CommonConstants.INT_0;
        } else {
            if (!CommonConstants.SYS_CD_71101.equals(sysCd)) {
                kinouId = CommonConstants.INT_0;
            }
        }

        int isFind = CommonConstants.INT_0;
        // 上記取得した結果.セキュリティ単位が「1：個人」の場合
        if (secSetting.getSecUnit() == CommonConstants.INT_1) {
            AdmSecKengenShkSvjigyoByCriteriaInEntity inEntity = new AdmSecKengenShkSvjigyoByCriteriaInEntity();
            // 職員ID
            inEntity.setChkShokuId(shokuId);
            // システムコード
            inEntity.setSysCd(sysCd);
            // 機能区分
            inEntity.setKinouKbn(CommonConstants.INT_1);
            // 機能ID
            inEntity.setKinouId(kinouId);
            // 職員事業所権限
            List<AdmSecKengenShkSvjigyoOutEntity> outEntityList = kghAdmSecKengenShkSvjigyoSelectMapper
                    .findAdmSecKengenShkSvjigyoByCriteria(inEntity);
            if (CollectionUtils.isNotEmpty(outEntityList)) {
                for (int i = 0; i < outEntityList.size(); i++) {
                    AdmSecKengenShkSvjigyoOutEntity item = outEntityList.get(i);
                    if (item.getChk() == CommonConstants.INT_1 && item.getSvJigyoId() == jigyoId) {
                        isFind = CommonConstants.INT_1;
                        break;
                    }
                }
            }
            // 上記取得した結果.セキュリティ単位が「2：グループ(共通)」或いは「3：グループ(組み合わせ)」の場合
        } else if (secSetting.getSecUnit() == CommonConstants.INT_2
                || secSetting.getSecUnit() == CommonConstants.INT_3) {
            AdmSecKengenFuncGrpSvJigyoByCriteriaInEntity inEntity = new AdmSecKengenFuncGrpSvJigyoByCriteriaInEntity();
            // 職員ID
            inEntity.setChkShokuId(shokuId);
            // システムコード
            inEntity.setSysCd(sysCd);
            // 機能区分
            inEntity.setKinouKbn(CommonConstants.INT_1);
            // 機能ID
            inEntity.setKinouId(kinouId);
            // 関数用グループ事業所権限
            List<AdmSecKengenFuncGrpSvJigyoOutEntity> outEntityList = kghAdmSecKengenFuncGrpSvJigyoSelectMapper
                    .findAdmSecKengenFuncGrpSvJigyoByCriteria(inEntity);
            if (CollectionUtils.isNotEmpty(outEntityList)) {
                for (int i = 0; i < outEntityList.size(); i++) {
                    AdmSecKengenFuncGrpSvJigyoOutEntity item = outEntityList.get(i);
                    // グループ(単一)
                    if (secSetting.getSecUnit() == CommonConstants.INT_2) {
                        if (item.getSecGroupKbn() == CommonConstants.INT_0
                                && item.getChk() == CommonConstants.INT_1
                                && item.getSvJigyoId() == jigyoId) {
                            isFind = CommonConstants.INT_1;
                            break;
                        }
                        // グループ(複数)
                    } else if (secSetting.getSecUnit() == CommonConstants.INT_3) {
                        if (item.getSecGroupKbn() == CommonConstants.INT_3
                                && item.getChk() == CommonConstants.INT_1
                                && item.getSvJigyoId() == jigyoId) {
                            isFind = CommonConstants.INT_1;
                            break;
                        }
                    }
                }
            }
            // 職員事業所又は関数用グループ事業所権限がある場合
            if (isFind > 0) {
                result = CommonConstants.INT_1;
                // 職員事業所又は関数用グループ事業所権限がない場合
            } else {
                result = CommonConstants.INT_0;
            }
        }
        return result;
    }

    /**
     * 指定した機能が利用可能の判断(機能IDバージョン).
     *
     * <p>
     * 【16】
     *
     * @param shokuId   職員ID.
     * @param sysCd     システムコード.
     * @param svJigyoId 事業所ID.
     * @param kinouName 機能名.
     * @return チェック結果(0:使用不可、1:閲覧、2:印刷、3:外部出力、9:登録).
     * @throws Exception 例外.
     * <AUTHOR>
     */
    public int chkUseKinou(int shokuId, String sysCd, int svJigyoId, String kinouName) throws Exception {
        // 初期化処理
        // システム区分を取得する。
        String ryaku = StringUtils.EMPTY;
        int comMocKinouNameKinouId = CommonConstants.INT_0;

        return CommonConstants.INT_9;

        // // 第１階層がシステム管理／職員管理／国保請求／本部請求でない場合は新しいコードを実行（機能管理(com_moc_kinou_name)）
        // SystemKubunKinouIDByCriteriaInEntity inEntity = new SystemKubunKinouIDByCriteriaInEntity();
        // // 機能名 = 【引数】.機能名
        // inEntity.setAsKinouName(kinouName);
        // // システムコード = 【引数】.システムコード
        // // TODO：検索条件項目が無くなったため、一旦TODOとする
        // // inEntity.setSysCd(sysCd);
        // List<SystemKubunKinouIDOutEntity> systemKubunKinouIDOutEntityList = comMocKinouNameSelectMapper
        //         .findSystemKubunKinouIDByCriteria(inEntity);
        // if (CollectionUtils.isNotEmpty(systemKubunKinouIDOutEntityList)) {
        //     // TODO:OutEntityに項目が存在しないため、一旦ＴＯＤＯとする
        //     // ryaku = systemKubunKinouIDOutEntityList.get(0).getSysCdKbn();
        //     // comMocKinouNameKinouId = systemKubunKinouIDOutEntityList.get(0).getKinouId();
        // }

        // // 高速化フラグを取得する。
        // int spdupChkKengen = getSpdupChkKengen(sysCd);

        // // 第３階層の機能IDを取得する。
        // // 下記の処理を行う。取得した機能IDリストから1番目機能IDを取得して、第３階層の機能IDとする。
        // List<String> kinouNameList = new ArrayList<String>();
        // kinouNameList.add(kinouName);
        // KinouIdOutDto kinouIdOutDto = kghAdmSecPafLogic.getKinouId(CommonConstants.INT_3, kinouNameList);
        // int kinouId = CommonConstants.INT_0;
        // if (CollectionUtils.isNotEmpty(kinouIdOutDto.getKinouIdList())) {
        //     kinouId = kinouIdOutDto.getKinouIdList().get(0);
        // }

        // // 第１階層の機能IDを取得する。
        // int parentId = kghAdmSecPafLogic.getParentKinouId(kinouId);

        // // 高速化権限チェック
        // // 上記取得した高速化フラグが「2：高速化」、且つ上記取得した取得フラグが「1：取得」の場合
        // // 且つ上記取得した第１階層の機能IDが「10000000」、「20000000」、「48000000」、「50000000」以外の場合、下記の処理を行う。
        // if (spdupChkKengen == CommonConstants.INT_2
        //         && kinouIdOutDto.getKinouIdGetFlg() == CommonConstants.INT_1
        //         && (parentId != CommonConstants.INT_10000000
        //                 && parentId != CommonConstants.INT_20000000
        //                 && parentId != CommonConstants.INT_48000000
        //                 && parentId != CommonConstants.INT_50000000)) {
        //     // 下記の高速化権限チェックを行う。
        //     KengenKinouSingleInDto kengenKinouSingleInDto = new KengenKinouSingleInDto();
        //     // 事業所IDリスト = 【引数】.事業所ID
        //     List<Integer> svJigyoIdList = new ArrayList<Integer>();
        //     svJigyoIdList.add(svJigyoId);
        //     kengenKinouSingleInDto.setSvJigyoIdList(svJigyoIdList);
        //     // 機能区分 = 3
        //     kengenKinouSingleInDto.setKinouKbn(CommonConstants.INT_3);
        //     // 機能ID = 【引数】.機能ID
        //     kengenKinouSingleInDto.setKinouId(kinouId);
        //     // 期待する最低の権限 = 0
        //     kengenKinouSingleInDto.setAuthFm(CommonConstants.INT_0);
        //     // 期待する最高の権限 = 9
        //     kengenKinouSingleInDto.setAuthTo(CommonConstants.INT_9);
        //     // ログイン職員ID = 【引数】.職員ID
        //     kengenKinouSingleInDto.setShokuinId(shokuId);
        //     int retKengenKinouSingleRet = kghAdmSecPafLogic.retKengenKinouSingle(kengenKinouSingleInDto);
        //     // 【戻リ値】.チェック結果に上記取得したチェック結果を設定して返却する。
        //     return retKengenKinouSingleRet;
        // }

        // // 非高速化チェック
        // // ライセンスのチェックを行う
        // boolean chkUseLisenceRet = chkUseLisence(comMocKinouNameKinouId);
        // // 上記取得したチェック結果が「false」の場合、【戻リ値】.チェック結果に「0：使用不可」を設定して返却する。
        // if (!chkUseLisenceRet) {
        //     return CommonConstants.INT_0;
        // }

        // // 管理者権限を取得する
        // int passKengen = ufPassKengen(shokuId);
        // // 上記取得した管理者権限フラグが「1：あり」の場合
        // // 【戻リ値】.チェック結果に「9：登録」を設定して返却する。
        // if (passKengen == CommonConstants.INT_1) {
        //     return CommonConstants.INT_9;
        //     // 上記以外の場合
        //     // 上記取得したシステム区分が「SYS」の場合、【戻リ値】.チェック結果に「1：閲覧」を設定して返却する。
        // } else {
        //     if (CommonConstants.SYS_CD_KBN_SYS.equals(ryaku)) {
        //         return CommonConstants.INT_1;
        //     }
        // }

        // // セキュリティ基本の設定を取得する。
        // KghAdmSecSettingDto secSetting = getSecSetting();

        // // 変数.サービス事業者IDを設定する。
        // int tempSvJigyoId = CommonConstants.INT_0;
        // // 上記取得した結果.事業所別機能制限が「0：なし」の場合
        // // 変数.サービス事業者ID = 0
        // if (secSetting.getSvjigyoKinouFlg() == CommonConstants.INT_0) {
        //     tempSvJigyoId = CommonConstants.INT_0;
        //     // 上記以外の場合
        // } else {
        //     // 【引数】.システムコードが「51201」 、「51301」、「51501」 の場合
        //     if (CommonConstants.SYS_CD_51201.equals(sysCd)
        //             || CommonConstants.SYS_CD_51301.equals(sysCd)
        //             || CommonConstants.SYS_CD_51501.equals(sysCd)) {
        //         tempSvJigyoId = CommonConstants.INT_0;
        //         // 上記以外の場合
        //     } else {
        //         // 上記取得したシステム区分が「JIN」の場合
        //         // 変数.サービス事業者ID = 0
        //         if (CommonConstants.SYS_CD_KBN_JIN.equals(ryaku)) {
        //             tempSvJigyoId = CommonConstants.INT_0;
        //             // 上記以外の場合
        //             // 変数.サービス事業者ID = 【引数】.事業所ID
        //         } else {
        //             tempSvJigyoId = svJigyoId;
        //         }
        //     }
        // }

        // // 【引数】.事業所IDの再設定
        // // 上記取得したシステム区分が「JIN」或いは「KNM」の場合、【引数】.事業所ID = 0
        // if (CommonConstants.SYS_CD_KBN_JIN.equals(ryaku)
        //         || CommonConstants.SYS_CD_KBN_KNM.equals(ryaku)) {
        //     svJigyoId = CommonConstants.INT_0;
        // }

        // // 変数.セキュリティグループ件数
        // int tempGroupCount = CommonConstants.INT_0;
        // // 権限フラグ(機能)
        // int authFlg = 0;
        // // セキュリティグループ件数(機能)
        // int rwc = 0;

        // // 職員IDが0の場合は強制的に個人単位で取得する
        // if (shokuId == 0) {
        //     return CommonConstants.INT_9;
        // }

        // // セキュリティ基本の設定、サービス事業者IDによって、下記の機能権限を取得する。
        // // 上記取得した結果.セキュリティ単位が「1：個人」の場合
        // if (secSetting.getSecUnit() == CommonConstants.INT_1) {
        //     // 変数.セキュリティグループ件数 = 1
        //     tempGroupCount = CommonConstants.INT_1;

        //     // 権限フラグ(機能)、セキュリティグループ件数(機能)を取得する
        //     KojinKinoKensuNameByCriteriaInEntity inEntity2 = new KojinKinoKensuNameByCriteriaInEntity();
        //     // 職員ID
        //     inEntity2.setAlShokuin(shokuId);
        //     // システムコード
        //     inEntity2.setAsSysCd(sysCd);
        //     // 事業所ID
        //     inEntity2.setLlAlSvJigyoId(tempSvJigyoId);
        //     // 機能名
        //     inEntity2.setAsKinouName(kinouName);
        //     // SecSettingの機能権限初期値
        //     inEntity2.setDefaultAuthKinou(CommonDtoUtil.intValToBigInt(secSetting.getDefaultAuthKinou()));

        //     List<KojinKinoKensuNameOutEntity> kojinKinoKensuNameOutEntityList = kojinKinoKengenSelectMapper
        //             .findKojinKinoKensuNameByCriteria(inEntity2);
        //     if (CollectionUtils.isNotEmpty(kojinKinoKensuNameOutEntityList)) {
        //         authFlg = kojinKinoKensuNameOutEntityList.get(0).getAuthFlg();
        //         rwc = kojinKinoKensuNameOutEntityList.get(0).getKensu();
        //     }
        // }
        // // 上記取得した結果.セキュリティ単位が「2：グループ(共通)」の場合
        // if (secSetting.getSecUnit() == CommonConstants.INT_2) {
        //     GroupId054ByCriteriaInEntity inEntity3 = new GroupId054ByCriteriaInEntity();
        //     // 職員ID
        //     inEntity3.setShokuinId(CommonDtoUtil.objValToString(shokuId));

        //     // 職員所属セキュリティグループID件数情報取得
        //     GroupId054OutEntity groupId054OutEntity = comMocSecGroupApplyIdSelectMapper
        //             .countGroupId054ByCriteria(inEntity3);
        //     int groupCnt = CommonDtoUtil.strValToInt(groupId054OutEntity.getCnt());
        //     // 変数.セキュリティグループ件数 = 上記取得した件数
        //     tempGroupCount = groupCnt;

        //     // 検索条件設定
        //     KojinKinoKensuGroupKbn0ByCriteriaInEntity inEntity4 = new KojinKinoKensuGroupKbn0ByCriteriaInEntity();
        //     // 職員ID
        //     inEntity4.setChkShokuId(shokuId);
        //     // 事業所ID
        //     inEntity4.setSvJigyoId(tempSvJigyoId);
        //     // 機能名
        //     inEntity4.setKinouKnj(kinouName);
        //     // システムコード
        //     inEntity4.setSysCd(sysCd);
        //     // 権限フラグ(機能)、セキュリティグループ件数(機能)を取得する
        //     List<KojinKinoKensuGroupKbn0OutEntity> kojinKinoKensuGroupKbn0OutEntityList = groupKyoutuuKinoKengen2SelectMapper
        //             .findKojinKinoKensuGroupKbn0ByCriteria(inEntity4);
        //     if (CollectionUtils.isNotEmpty(kojinKinoKensuGroupKbn0OutEntityList)) {
        //         authFlg = kojinKinoKensuGroupKbn0OutEntityList.get(0).getAuthFlg();
        //         rwc = kojinKinoKensuGroupKbn0OutEntityList.get(0).getKensu();
        //     }

        // }

        // // 上記取得した結果.セキュリティ単位が「3：グループ(組み合わせ)」の場合
        // if (secSetting.getSecUnit() == CommonConstants.INT_3) {
        //     // 検索条件設定
        //     GroupKumiawaseByCriteriaInEntity inEntity5 = new GroupKumiawaseByCriteriaInEntity();
        //     // 職員ID
        //     inEntity5.setShokuinId(CommonDtoUtil.objValToString(shokuId));
        //     // 職員所属セキュリティグループ組合件数情報取得
        //     GroupKumiawaseOutEntity groupKumiawaseOutEntity = comMocSecGroupKumiawaseSelectMapper
        //             .countGroupKumiawaseByCriteria(inEntity5);
        //     tempGroupCount = CommonDtoUtil.strValToInt(groupKumiawaseOutEntity.getCnt());

        //     // 検索条件設定
        //     KojinKinoKensuGroupKbn2ByCriteriaInEntity inEntity6 = new KojinKinoKensuGroupKbn2ByCriteriaInEntity();
        //     // 職員ID
        //     inEntity6.setChkShokuId(shokuId);
        //     // 事業所ID
        //     inEntity6.setSvJigyoId(tempSvJigyoId);
        //     // 機能名
        //     inEntity6.setKinouKnj(kinouName);
        //     // システムコード
        //     inEntity6.setSysCd(sysCd);
        //     // SecSettingの機能権限初期値
        //     inEntity6.setSecSettingDefaultAuthKinou(CommonDtoUtil.intValToBigInt(secSetting.getDefaultAuthKinou()));
        //     // 権限フラグ(機能)、セキュリティグループ件数(機能)を取得する
        //     List<KojinKinoKensuGroupKbn2OutEntity> kojinKinoKensuGroupKbn2OutEntityList = groupKumiawaseKinoKengen2SelectMapper
        //             .findKojinKinoKensuGroupKbn2ByCriteria(inEntity6);
        //     if (CollectionUtils.isNotEmpty(kojinKinoKensuGroupKbn2OutEntityList)) {
        //         authFlg = kojinKinoKensuGroupKbn2OutEntityList.get(0).getAuthFlg();
        //         rwc = kojinKinoKensuGroupKbn2OutEntityList.get(0).getKensu();
        //     }
        // }

        // // 「事業所・機能」権限をチェックする
        // // 事業所権限のチェック
        // // 機能区分=1の機能IDを取得する
        // int tempKinouId = 0;

        // // 検索条件設定
        // OyaKinouIDKinouknjByCriteriaInEntity inEntity7 = new OyaKinouIDKinouknjByCriteriaInEntity();
        // inEntity7.setAsKinouName(kinouName);

        // List<OyaKinouIDKinouknjOutEntity> parentIdList = comMocKinouNameSelectMapper
        //         .findOyaKinouIDKinouknjByCriteria(inEntity7);
        // if (CollectionUtils.isNotEmpty(parentIdList)) {
        //     // 検索条件設定
        //     ParentIdByCriteriaInEntity inEntity8 = new ParentIdByCriteriaInEntity();
        //     // 職員ID
        //     inEntity8.setAlKinou(CommonDtoUtil.objValToString(parentIdList.get(0).getParentId()));
        //     // 01-02 機能管理(com_moc_kinou_name)を検索
        //     List<ParentIdOutEntity> kinouIdList = comMocKinouNameSelectMapper.findParentIdByCriteria(inEntity8);

        //     if (CollectionUtils.isNotEmpty(kinouIdList)) {
        //         tempKinouId = kinouIdList.get(0).getParentId();
        //     }
        // }

        // // 事業所権限チェックを行う
        // // 【引数】.事業所IDが「0」ではない場合、下記の事業所権限チェックを行う。
        // if (svJigyoId != CommonConstants.INT_0) {
        //     int chkUseJigyoRet = chkUseJigyo(shokuId, sysCd, tempKinouId, svJigyoId);
        //     // 上記取得したチェック結果が「0」の場合
        //     if (chkUseJigyoRet == CommonConstants.INT_0) {
        //         // 【戻リ値】.チェック結果に「0：使用不可」を設定して返却する。
        //         return CommonConstants.INT_0;
        //     }
        // }

        // // 個人・グループ(共通)・グループ(組み合わせ)の機能権限チェック
        // // 上記取得したセキュリティグループ件数が0件の場合
        // // 【戻リ値】.チェック結果に「0：使用不可」を設定して返却する。
        // if (tempGroupCount > CommonConstants.INT_0) {
        //     return CommonConstants.INT_0;
        //     // 上記取得したセキュリティグループ件数が上記取得したセキュリティグループ件数(機能)より大きい場合、且つ上記取得した結果.機能権限初期値が「9：登録」の場合
        //     // 【戻リ値】.チェック結果に「9：登録」を設定して返却する。
        // } else if (tempGroupCount > rwc
        //         && secSetting.getDefaultAuthKinou() == CommonConstants.INT_9) {
        //     return CommonConstants.INT_9;
        //     // 上記取得した権限フラグがNULLの場合
        //     // 【戻リ値】.チェック結果に上記取得した結果.機能権限初期値を設定して返却する。
        // } else if (authFlg == 0) {
        //     return secSetting.getDefaultAuthKinou();
        //     // 上記取得した権限フラグ(機能)が0より大きい場合
        //     // 【戻リ値】.チェック結果に上記取得した権限フラグ(機能)を設定して返却する。
        // } else if (authFlg > CommonConstants.INT_0) {
        //     return authFlg;
        //     // 上記以外の場合
        //     // 【戻リ値】.チェック結果に「0：使用不可」を設定して返却する。
        // } else {
        //     return CommonConstants.INT_0;
        // }
    }

    /**
     * 指定した機能が利用可能か判断する(機能IDバージョン).
     *
     * <p>
     * 【19】
     *
     * @param shokuId   職員ID
     * @param sysCd     システムコード
     * @param svJigyoId 事業所ID
     * @param kinouId   機能ID
     * @return 機能権限情報
     * <AUTHOR>
     */
    public KinouSecurityDto chkUseKinou2(int shokuId, String sysCd, int svJigyoId, int kinouId) throws Exception {
        KinouSecurityDto result = new KinouSecurityDto();
        // (1) 初期化処理
        int svJigyoIdUse = svJigyoId;
        // (1)-1 システム区分を取得する
        // ※（SYS：システム管理ﾒﾆｭｰ、JIN：職員管理ﾒﾆｭｰ、3GK：介護保険ﾒﾆｭｰ、KNM：勤怠管理ﾒﾆｭｰ）
        // 初期化処理
        // システム区分を取得する。
        String sysCdKbn = StringUtils.EMPTY;
        // 01-02 機能管理(com_moc_kinou_name)
        KghAdmSecurity6ByCriteriaInEntity inEntity = new KghAdmSecurity6ByCriteriaInEntity();
        inEntity.setAlKinou(CommonDtoUtil.objValToString(kinouId));
        inEntity.setAsSysCd(sysCd);

        List<KghAdmSecurity6OutEntity> kghAdmSecurity6OutEntityList = comMocKinouNameSelectMapper
                .findKghAdmSecurity6ByCriteria(inEntity);
        if (CollectionUtils.isNotEmpty(kghAdmSecurity6OutEntityList)) {
            sysCdKbn = kghAdmSecurity6OutEntityList.get(0).getSysCdKbn();
        }

        // (1)-2 第１階層の機能IDを取得する
        // 第１階層の機能ID
        int parentId = kghAdmSecPafLogic.getParentKinouId(kinouId);

        // (2) 高速化権限チェック
        // 上記(1)で取得した第１階層の機能IDが「10000000」、「20000000」、「48000000」、「50000000」以外の場合、下記の処理を行う。
        if (parentId != CommonConstants.INT_10000000
                && parentId != CommonConstants.INT_20000000
                && parentId != CommonConstants.INT_48000000
                && parentId != CommonConstants.INT_50000000) {

            // (2)-1 下記の高速化権限チェックを行う

            // リストに【引数】.事業所IDを追加する
            List<Integer> svJigyoIdUseList = new ArrayList<Integer>();
            svJigyoIdUseList.add(svJigyoIdUse);
            // @param svJigyoIdUseList 事業所IDリスト リストに【引数】.事業所IDを追加する
            // @param kinouKbn 機能区分 3
            // @param kinouId 機能ID 【引数】.機能ID
            // @param shokuinId ログイン職員ID 【引数】.職員ID
            StrKghAdmSecurityUseDto resultStrKghAdmSecurityUseDto = kghAdmSecPafLogic.retKengenKinouUse(
                    svJigyoIdUseList, CommonConstants.INT_3, kinouId, shokuId);

            // (2)-2 機能権限情報を変換する
            result = setKinou2SecurityDto(resultStrKghAdmSecurityUseDto);
            return result;
        }

        // (3) 非高速化チェック
        // (3)-1 ライセンスのチェックを行う
        // 渡された機能IDのライセンスのチェックを行う
        boolean checkFlg = chkUseLisence(kinouId);

        // 上記取得したチェック結果が「false」の場合、下記のように機能権限情報を変換する。
        if (checkFlg == false) {
            StrKghAdmSecurityUseDto all0StrKghAdmSecurityUseDto = new StrKghAdmSecurityUseDto();
            // 閲覧権限 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse1(CommonConstants.INT_0);
            // 印刷権限 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse2(CommonConstants.INT_0);
            // 外部出力権限 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse3(CommonConstants.INT_0);
            // 権限4 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse4(CommonConstants.INT_0);
            // 権限5 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse5(CommonConstants.INT_0);
            // 権限6 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse6(CommonConstants.INT_0);
            // 権限7 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse7(CommonConstants.INT_0);
            // 権限8 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse8(CommonConstants.INT_0);
            // 登録権限 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse9(CommonConstants.INT_0);
            // 変換した機能権限情報を【戻り値】.機能権限情報に設定して返却する
            result = setKinou2SecurityDto(all0StrKghAdmSecurityUseDto);
            return result;
        }

        // (3)-2 管理者権限を取得する
        int adminKengen = ufPassKengen(shokuId);

        // 上記取得した管理者権限フラグが「1：権限あり」の場合、【戻り値】.機能権限情報に下記のように取得して返却する。
        if (adminKengen == CommonConstants.INT_1) {
            StrKghAdmSecurityUseDto all1StrKghAdmSecurityUseDto = new StrKghAdmSecurityUseDto();
            // 閲覧権限 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse1(CommonConstants.INT_1);
            // 印刷権限 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse2(CommonConstants.INT_1);
            // 外部出力権限 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse3(CommonConstants.INT_1);
            // 権限4 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse4(CommonConstants.INT_1);
            // 権限5 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse5(CommonConstants.INT_1);
            // 権限6 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse6(CommonConstants.INT_1);
            // 権限7 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse7(CommonConstants.INT_1);
            // 権限8 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse8(CommonConstants.INT_1);
            // 登録権限 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse9(CommonConstants.INT_1);
            // 変換した機能権限情報を【戻り値】.機能権限情報に設定して返却する
            result = setKinou2SecurityDto(all1StrKghAdmSecurityUseDto);
            return result;
            // 上記以外の場合
        } else {
            // 上記(1)で取得したシステム区分が「SYS」の場合、【戻り値】.機能権限情報に下記のように取得して返却する。
            if (CommonConstants.SYS_CD_KBN_SYS.equals(sysCdKbn)) {
                StrKghAdmSecurityUseDto user1StrKghAdmSecurityUseDto = new StrKghAdmSecurityUseDto();
                // 閲覧権限 1:権限あり
                user1StrKghAdmSecurityUseDto.setUse1(CommonConstants.INT_1);
                // 印刷権限 0:権限なし
                user1StrKghAdmSecurityUseDto.setUse2(CommonConstants.INT_0);
                // 外部出力権限 0:権限なし
                user1StrKghAdmSecurityUseDto.setUse3(CommonConstants.INT_0);
                // 権限4 0:権限なし
                user1StrKghAdmSecurityUseDto.setUse4(CommonConstants.INT_0);
                // 権限5 0:権限なし
                user1StrKghAdmSecurityUseDto.setUse5(CommonConstants.INT_0);
                // 権限6 0:権限なし
                user1StrKghAdmSecurityUseDto.setUse6(CommonConstants.INT_0);
                // 権限7 0:権限なし
                user1StrKghAdmSecurityUseDto.setUse7(CommonConstants.INT_0);
                // 権限8 0:権限なし
                user1StrKghAdmSecurityUseDto.setUse8(CommonConstants.INT_0);
                // 登録権限 0:権限なし
                user1StrKghAdmSecurityUseDto.setUse9(CommonConstants.INT_0);
                // 変換した機能権限情報を【戻り値】.機能権限情報に設定して返却する
                result = setKinou2SecurityDto(user1StrKghAdmSecurityUseDto);
                return result;
            }
        }

        // (3)-3 セキュリティ基本の設定を取得する
        KghAdmSecSettingDto resultSecSettingOutDto = getSecSetting();

        // (3)-4 サービス事業者IDを設定する
        // 上記(3)-3で取得した結果.事業所別機能制限が「0：なし」の場合
        int jigyoIdTemp = 0;
        if (resultSecSettingOutDto.getSvjigyoKinouFlg() == CommonConstants.INT_0) {
            // サービス事業者ID = 0
            jigyoIdTemp = CommonConstants.INT_0;

            // 上記以外の場合
        } else {
            // 【引数】.システムコードが「51201」 、「51301」、「51501」 の場合
            if (sysCd == CommonConstants.SYS_CD_51201
                    || sysCd == CommonConstants.SYS_CD_51301
                    || sysCd == CommonConstants.SYS_CD_51501) {
                // システムコード（gsyscd）が71101以外の分岐についは、NEXTで使用していないので、実装不要
                // 上記以外の場合
            } else {
                // 上記(1)で取得したシステム区分が「JIN」の場合
                if (CommonConstants.SYS_CD_KBN_JIN.equals(sysCdKbn)) {
                    // サービス事業者ID ＝ 0
                    jigyoIdTemp = CommonConstants.INT_0;

                    // 上記以外の場合
                } else {
                    // サービス事業者ID ＝ 【引数】.事業所ID
                    jigyoIdTemp = svJigyoIdUse;
                }
            }
        }

        // (3)-5【引数】.事業所IDの再設定
        // 上記(1)で取得したシステム区分が「JIN」或いは「KNM」の場合
        if (CommonConstants.SYS_CD_KBN_JIN.equals(sysCdKbn)
                || CommonConstants.SYS_CD_KBN_KNM.equals(sysCdKbn)) {
            // 【引数】.事業所ID ＝ 0
            svJigyoIdUse = CommonConstants.INT_0;
        }

        // セキュリティグループ件数(機能)
        int rwc = 0;

        // (3)-6 検索結果
        StrKghAdmSecurityUseDto searchResultStrKghAdmSecurityUseDto = new StrKghAdmSecurityUseDto();

        // (3)-6 セキュリティ基本の設定、サービス事業者IDによって、下記の機能権限を取得する。機能権限チェックを行う。
        int tempGroupCount = CommonConstants.INT_0;
        // (3)-6-1 上記(3)-3で取得した結果.セキュリティ単位が「1：個人」の場合
        if (resultSecSettingOutDto.getSecUnit() == CommonConstants.INT_1) {
            // セキュリティグループ件数を設定する
            // セキュリティグループ件数 = 1
            tempGroupCount = CommonConstants.INT_1;

            // 権限1～9、セキュリティグループ件数(機能)を取得する
            // Mapper実行 getPersonalKinouAuth
            // 権限フラグ(機能)、セキュリティグループ件数(機能)を取得する
            ShokuinKinoKenkenByCriteriaInEntity inEntity2 = new ShokuinKinoKenkenByCriteriaInEntity();
            // 職員ID = 【引数】.職員ID
            inEntity2.setAlShokuin(shokuId);
            // システムコード = 【引数】.システムコード
            inEntity2.setAsSysCd(sysCd);
            // サービス事業者ID 上記(3)-4で取得したサービス事業者ID jigyoIdTemp
            inEntity2.setLlAlSvJigyoId(jigyoIdTemp);
            // 機能（ID・名称） = 【引数】.機能ID
            inEntity2.setAlKinou(kinouId);

            List<ShokuinKinoKenkenOutEntity> shokuinKinoKenkenOutEntityList = comMocSecShkKinouSelectMapper
                    .findShokuinKinoKenkenByCriteria(inEntity2);
            if (CollectionUtils.isNotEmpty(shokuinKinoKenkenOutEntityList)) {
                // 閲覧権限
                searchResultStrKghAdmSecurityUseDto.setUse1(shokuinKinoKenkenOutEntityList.get(0).getUse1());
                // 印刷権限
                searchResultStrKghAdmSecurityUseDto.setUse2(shokuinKinoKenkenOutEntityList.get(0).getUse2());
                // 外部出力権限
                searchResultStrKghAdmSecurityUseDto.setUse3(shokuinKinoKenkenOutEntityList.get(0).getUse3());
                // 権限4
                searchResultStrKghAdmSecurityUseDto.setUse4(shokuinKinoKenkenOutEntityList.get(0).getUse4());
                // 権限5
                searchResultStrKghAdmSecurityUseDto.setUse5(shokuinKinoKenkenOutEntityList.get(0).getUse5());
                // 権限6
                searchResultStrKghAdmSecurityUseDto.setUse6(shokuinKinoKenkenOutEntityList.get(0).getUse6());
                // 権限7
                searchResultStrKghAdmSecurityUseDto.setUse7(shokuinKinoKenkenOutEntityList.get(0).getUse7());
                // 権限8
                searchResultStrKghAdmSecurityUseDto.setUse8(shokuinKinoKenkenOutEntityList.get(0).getUse8());
                // 登録権限
                searchResultStrKghAdmSecurityUseDto.setUse9(shokuinKinoKenkenOutEntityList.get(0).getUse9());
                // セキュリティグループ件数(機能)
                rwc = shokuinKinoKenkenOutEntityList.get(0).getKensu();
            }

            // (3)-6-2 上記(3)-3で取得した結果.セキュリティ単位が「2：グループ(共通)」或いは「3：グループ(組み合わせ)」の場合
        } else if (resultSecSettingOutDto.getSecUnit() == CommonConstants.INT_2) {
            GroupId054ByCriteriaInEntity inEntity3 = new GroupId054ByCriteriaInEntity();
            // 職員ID
            inEntity3.setShokuinId(CommonDtoUtil.objValToString(shokuId));

            // 職員所属セキュリティグループID件数情報取得
            GroupId054OutEntity groupId054OutEntity = comMocSecGroupApplyIdSelectMapper
                    .countGroupId054ByCriteria(inEntity3);

            // 変数.セキュリティグループ件数 = 上記取得した件数
            tempGroupCount = CommonDtoUtil.strValToInt(groupId054OutEntity.getCnt());

            // 検索条件設定
            GroupKinoKenkenByCriteriaInEntity inEntity4 = new GroupKinoKenkenByCriteriaInEntity();
            // 職員ID
            inEntity4.setChkShokuin(shokuId);
            // 機能ID
            inEntity4.setKinouId(kinouId);
            // 事業所ID
            inEntity4.setSvJigyoId(jigyoIdTemp);
            // システムコード
            inEntity4.setSysCd(sysCd);
            // SecSettingの機能権限初期値
            inEntity4.setDefaultAuthKinou(CommonDtoUtil.intValToBigInt(resultSecSettingOutDto.getDefaultAuthKinou()));
            // 権限フラグ(機能)、セキュリティグループ件数(機能)を取得する
            List<GroupKinoKenkenOutEntity> groupKinoKenkenOutEntityList = groupKyoutuuKinoKengen5SelectMapper
                    .findGroupKinoKenkenByCriteria(inEntity4);
            if (CollectionUtils.isNotEmpty(groupKinoKenkenOutEntityList)) {
                // 閲覧権限
                searchResultStrKghAdmSecurityUseDto.setUse1(groupKinoKenkenOutEntityList.get(0).getUse1());
                // 印刷権限
                searchResultStrKghAdmSecurityUseDto.setUse2(groupKinoKenkenOutEntityList.get(0).getUse2());
                // 外部出力権限
                searchResultStrKghAdmSecurityUseDto.setUse3(groupKinoKenkenOutEntityList.get(0).getUse3());
                // 権限4
                searchResultStrKghAdmSecurityUseDto.setUse4(groupKinoKenkenOutEntityList.get(0).getUse4());
                // 権限5
                searchResultStrKghAdmSecurityUseDto.setUse5(groupKinoKenkenOutEntityList.get(0).getUse5());
                // 権限6
                searchResultStrKghAdmSecurityUseDto.setUse6(groupKinoKenkenOutEntityList.get(0).getUse6());
                // 権限7
                searchResultStrKghAdmSecurityUseDto.setUse7(groupKinoKenkenOutEntityList.get(0).getUse7());
                // 権限8
                searchResultStrKghAdmSecurityUseDto.setUse8(groupKinoKenkenOutEntityList.get(0).getUse8());
                // 登録権限
                searchResultStrKghAdmSecurityUseDto.setUse9(groupKinoKenkenOutEntityList.get(0).getUse9());

                // セキュリティグループ件数(機能)
                rwc = groupKinoKenkenOutEntityList.get(0).getKensu();
            }
        } else if (resultSecSettingOutDto.getSecUnit() == CommonConstants.INT_3) {
            // 検索条件設定
            GroupKumiawaseByCriteriaInEntity inEntity5 = new GroupKumiawaseByCriteriaInEntity();
            // 職員ID
            inEntity5.setShokuinId(CommonDtoUtil.objValToString(shokuId));
            // 職員所属セキュリティグループ組合件数を取得
            GroupKumiawaseOutEntity groupKumiawaseOutEntity = comMocSecGroupKumiawaseSelectMapper
                    .countGroupKumiawaseByCriteria(inEntity5);
            // グループ組合件数を取得
            tempGroupCount = CommonDtoUtil.strValToInt(groupKumiawaseOutEntity.getCnt());

            // 検索条件設定
            GroupKumiAwaseKinoKenkenByCriteriaInEntity inEntity6 = new GroupKumiAwaseKinoKenkenByCriteriaInEntity();
            // 職員ID
            inEntity6.setChkShokuin(shokuId);
            // 機能ID
            inEntity6.setKinouId(kinouId);
            // 事業所ID
            inEntity6.setSvJigyoId(jigyoIdTemp);
            // システムコード
            inEntity6.setSysCd(sysCd);
            // SecSettingの機能権限初期値
            inEntity6.setLiDefaultAuthKinou(CommonDtoUtil.intValToBigInt(resultSecSettingOutDto.getDefaultAuthKinou()));
            // グループ組み合わせ機能権限5情報取得
            List<GroupKumiAwaseKinoKenkenOutEntity> groupKumiAwaseKinoKenkenOutEntityList = groupKumiawaseKinoKengen5SelectMapper
                    .findGroupKumiAwaseKinoKenkenByCriteria(inEntity6);
            if (CollectionUtils.isNotEmpty(groupKumiAwaseKinoKenkenOutEntityList)) {
                // 閲覧権限
                searchResultStrKghAdmSecurityUseDto.setUse1(groupKumiAwaseKinoKenkenOutEntityList.get(0).getUse1());
                // 印刷権限
                searchResultStrKghAdmSecurityUseDto.setUse2(groupKumiAwaseKinoKenkenOutEntityList.get(0).getUse2());
                // 外部出力権限
                searchResultStrKghAdmSecurityUseDto.setUse3(groupKumiAwaseKinoKenkenOutEntityList.get(0).getUse3());
                // 権限4
                searchResultStrKghAdmSecurityUseDto.setUse4(groupKumiAwaseKinoKenkenOutEntityList.get(0).getUse4());
                // 権限5
                searchResultStrKghAdmSecurityUseDto.setUse5(groupKumiAwaseKinoKenkenOutEntityList.get(0).getUse5());
                // 権限6
                searchResultStrKghAdmSecurityUseDto.setUse6(groupKumiAwaseKinoKenkenOutEntityList.get(0).getUse6());
                // 権限7
                searchResultStrKghAdmSecurityUseDto.setUse7(groupKumiAwaseKinoKenkenOutEntityList.get(0).getUse7());
                // 権限8
                searchResultStrKghAdmSecurityUseDto.setUse8(groupKumiAwaseKinoKenkenOutEntityList.get(0).getUse8());
                // 登録権限
                searchResultStrKghAdmSecurityUseDto.setUse9(groupKumiAwaseKinoKenkenOutEntityList.get(0).getUse9());

                // セキュリティグループ件数(機能)
                rwc = groupKumiAwaseKinoKenkenOutEntityList.get(0).getKensu();
            }
        }

        // (3)-6-3 機能権限情報を変換する
        // 上記取得したセキュリティグループ件数が0件の場合、
        if (tempGroupCount == CommonConstants.INT_0) {
            StrKghAdmSecurityUseDto all0StrKghAdmSecurityUseDto = new StrKghAdmSecurityUseDto();
            // 閲覧権限 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse1(CommonConstants.INT_0);
            // 印刷権限 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse2(CommonConstants.INT_0);
            // 外部出力権限 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse3(CommonConstants.INT_0);
            // 権限4 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse4(CommonConstants.INT_0);
            // 権限5 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse5(CommonConstants.INT_0);
            // 権限6 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse6(CommonConstants.INT_0);
            // 権限7 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse7(CommonConstants.INT_0);
            // 権限8 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse8(CommonConstants.INT_0);
            // 登録権限 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse9(CommonConstants.INT_0);
            // 変換した機能権限情報を【戻り値】.機能権限情報に設定して返却する
            result = setKinou2SecurityDto(all0StrKghAdmSecurityUseDto);
            return result;
        }

        // 上記取得したセキュリティグループ件数が1件以上ですが
        // 上記取得した結果.セキュリティグループ件数(機能)が0件の場合
        if (tempGroupCount > CommonConstants.INT_1
                && rwc == CommonConstants.INT_0) {
            StrKghAdmSecurityUseDto defaultStrKghAdmSecurityUseDto = new StrKghAdmSecurityUseDto();

            // 閲覧権限 0:権限なし
            defaultStrKghAdmSecurityUseDto.setUse1(CommonConstants.INT_0);
            // 印刷権限 0:権限なし
            defaultStrKghAdmSecurityUseDto.setUse2(CommonConstants.INT_0);
            // 外部出力権限 0:権限なし
            defaultStrKghAdmSecurityUseDto.setUse3(CommonConstants.INT_0);
            // 権限4 0:権限なし
            defaultStrKghAdmSecurityUseDto.setUse4(CommonConstants.INT_0);
            // 権限5 0:権限なし
            defaultStrKghAdmSecurityUseDto.setUse5(CommonConstants.INT_0);
            // 権限6 0:権限なし
            defaultStrKghAdmSecurityUseDto.setUse6(CommonConstants.INT_0);
            // 権限7 0:権限なし
            defaultStrKghAdmSecurityUseDto.setUse7(CommonConstants.INT_0);
            // 権限8 0:権限なし
            defaultStrKghAdmSecurityUseDto.setUse8(CommonConstants.INT_0);
            // 登録権限 0:権限なし
            defaultStrKghAdmSecurityUseDto.setUse9(CommonConstants.INT_0);

            if (resultSecSettingOutDto.getDefaultAuthKinou() > CommonConstants.INT_0) {
                // 閲覧権限 上記(3)-3で取得した結果.機能権限初期値
                defaultStrKghAdmSecurityUseDto.setUse1(CommonConstants.INT_1);
                // 印刷権限 上記(3)-3で取得した結果.機能権限初期値
                defaultStrKghAdmSecurityUseDto.setUse2(CommonConstants.INT_1);
                // 外部出力権限 上記(3)-3で取得した結果.機能権限初期値
                defaultStrKghAdmSecurityUseDto.setUse3(CommonConstants.INT_1);
                // 権限4 上記(3)-3で取得した結果.機能権限初期値
                defaultStrKghAdmSecurityUseDto.setUse4(CommonConstants.INT_1);
                // 権限5 上記(3)-3で取得した結果.機能権限初期値
                defaultStrKghAdmSecurityUseDto.setUse5(CommonConstants.INT_1);
                // 権限6 上記(3)-3で取得した結果.機能権限初期値
                defaultStrKghAdmSecurityUseDto.setUse6(CommonConstants.INT_1);
                // 権限7 上記(3)-3で取得した結果.機能権限初期値
                defaultStrKghAdmSecurityUseDto.setUse7(CommonConstants.INT_1);
                // 権限8 上記(3)-3で取得した結果.機能権限初期値
                defaultStrKghAdmSecurityUseDto.setUse8(CommonConstants.INT_1);
                // 登録権限 上記(3)-3で取得した結果.機能権限初期値
                defaultStrKghAdmSecurityUseDto.setUse9(CommonConstants.INT_1);
            }
            // 変換した機能権限情報を【戻り値】.機能権限情報に設定して返却する
            result = setKinou2SecurityDto(defaultStrKghAdmSecurityUseDto);
            return result;
        }
        // 上記取得したセキュリティグループ件数が1件以上ですが
        // 上記取得した結果.セキュリティグループ件数(機能)が1件以上の場合
        if (tempGroupCount >= CommonConstants.INT_1
                && rwc >= CommonConstants.INT_1) {
            // 機能権限情報を設定する
            result = setKinou2SecurityDto(searchResultStrKghAdmSecurityUseDto);
        }

        // (3)-7 「事業所・機能」権限をチェックする
        // (3)-7-1 事業所権限を取得する
        // (3)-7-1-1 事業所権限の初期化処理
        // 事業所権限に1を設定する
        int tempKinouId = CommonConstants.INT_1;

        // (3)-7-1-2 【引数】.事業所IDが「0」ではない場合、下記の事業所権限を取得する
        if (svJigyoIdUse != CommonConstants.INT_0) {
            // 機能区分=2の機能IDを取得する
            // 検索条件設定
            ParentIdByCriteriaInEntity inEntity7 = new ParentIdByCriteriaInEntity();
            // 機能ID
            inEntity7.setAlKinou(CommonDtoUtil.objValToString(kinouId));

            // 01-02 機能管理(com_moc_kinou_name)
            List<ParentIdOutEntity> parentIdList = comMocKinouNameSelectMapper.findParentIdByCriteria(inEntity7);
            if (CollectionUtils.isNotEmpty(parentIdList)) {
                // 検索条件設定
                ParentIdByCriteriaInEntity inEntity8 = new ParentIdByCriteriaInEntity();
                // 機能ID
                inEntity8.setAlKinou(CommonDtoUtil.objValToString(parentIdList.get(0).getParentId()));

                // 機能管理情報取得
                List<ParentIdOutEntity> kinouIdList = comMocKinouNameSelectMapper.findParentIdByCriteria(inEntity8);

                if (CollectionUtils.isNotEmpty(kinouIdList)) {
                    tempKinouId = kinouIdList.get(0).getParentId();
                }
            }
            // 事業所IDが0でない場合のみ事業所権限をチェック
            if (svJigyoId != 0) {
                // 事業所権限のチェック
                tempKinouId = chkUseJigyo(shokuId, sysCd, tempKinouId, svJigyoIdUse);
            }
        }

        // (3)-7-2 個人・グループ(共通)・グループ(組み合わせ)の機能権限チェック
        // 上記(3)-6で取得したセキュリティグループ件数が0件の場合
        // 上記(3)-6で変換した機能権限情報を【戻り値】.機能権限情報に設定して返却する ※0:権限なし
        if (tempGroupCount == CommonConstants.INT_0) {
            return result;

            // 上記(3)-6で取得したセキュリティグループ件数が1件以上ですが
            // 上記(3)-6で取得した結果.セキュリティグループ件数(機能)が1件以上の場合
        } else if (tempGroupCount >= CommonConstants.INT_1
                && rwc >= CommonConstants.INT_1) {
            return result;

            // 【引数】.事業所IDが「0」ではない場合、且つ、上記(3)-7-1取得した事業所権限が「0」の場合
            // 上記変換した機能権限情報を【戻り値】.機能権限情報に設定して返却する。
            /// ※上記(3)-3で取得した結果.機能権限初期値
        } else if (svJigyoIdUse != CommonConstants.INT_0
                && tempKinouId == CommonConstants.INT_0) {
            StrKghAdmSecurityUseDto defaultStrKghAdmSecurityUseDto = new StrKghAdmSecurityUseDto();
            // 閲覧権限 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse1(resultSecSettingOutDto.getDefaultAuthKinou());
            // 印刷権限 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse2(resultSecSettingOutDto.getDefaultAuthKinou());
            // 外部出力権限 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse3(resultSecSettingOutDto.getDefaultAuthKinou());
            // 権限4 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse4(resultSecSettingOutDto.getDefaultAuthKinou());
            // 権限5 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse5(resultSecSettingOutDto.getDefaultAuthKinou());
            // 権限6 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse6(resultSecSettingOutDto.getDefaultAuthKinou());
            // 権限7 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse7(resultSecSettingOutDto.getDefaultAuthKinou());
            // 権限8 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse8(resultSecSettingOutDto.getDefaultAuthKinou());
            // 登録権限 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse9(resultSecSettingOutDto.getDefaultAuthKinou());
            // 変換した機能権限情報を【戻り値】.機能権限情報に設定して返却する
            result = setKinou2SecurityDto(defaultStrKghAdmSecurityUseDto);
            return result;

            // 上記(3)-6で取得したセキュリティグループ件数が上記(3)-6で取得したセキュリティグループ件数(機能)より大きい場合
            // 且つ上記(3)-3で取得した結果.機能権限初期値が「9：登録」の場合
        } else if (tempGroupCount > rwc
                && resultSecSettingOutDto.getDefaultAuthKinou() == CommonConstants.INT_9) {
            StrKghAdmSecurityUseDto all1StrKghAdmSecurityUseDto = new StrKghAdmSecurityUseDto();
            // 閲覧権限 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse1(CommonConstants.INT_1);
            // 印刷権限 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse2(CommonConstants.INT_1);
            // 外部出力権限 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse3(CommonConstants.INT_1);
            // 権限4 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse4(CommonConstants.INT_1);
            // 権限5 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse5(CommonConstants.INT_1);
            // 権限6 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse6(CommonConstants.INT_1);
            // 権限7 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse7(CommonConstants.INT_1);
            // 権限8 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse8(CommonConstants.INT_1);
            // 登録権限 1:権限あり
            all1StrKghAdmSecurityUseDto.setUse9(CommonConstants.INT_1);
            // 変換した機能権限情報を【戻り値】.機能権限情報に設定して返却する
            result = setKinou2SecurityDto(all1StrKghAdmSecurityUseDto);
            return result;
        } else {
            StrKghAdmSecurityUseDto defaultStrKghAdmSecurityUseDto = new StrKghAdmSecurityUseDto();
            // 閲覧権限 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse1(resultSecSettingOutDto.getDefaultAuthKinou());
            // 印刷権限 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse2(resultSecSettingOutDto.getDefaultAuthKinou());
            // 外部出力権限 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse3(resultSecSettingOutDto.getDefaultAuthKinou());
            // 権限4 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse4(resultSecSettingOutDto.getDefaultAuthKinou());
            // 権限5 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse5(resultSecSettingOutDto.getDefaultAuthKinou());
            // 権限6 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse6(resultSecSettingOutDto.getDefaultAuthKinou());
            // 権限7 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse7(resultSecSettingOutDto.getDefaultAuthKinou());
            // 権限8 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse8(resultSecSettingOutDto.getDefaultAuthKinou());
            // 登録権限 上記(3)-3で取得した結果.機能権限初期値
            defaultStrKghAdmSecurityUseDto.setUse9(resultSecSettingOutDto.getDefaultAuthKinou());
            // 変換した機能権限情報を【戻り値】.機能権限情報に設定して返却する
            result = setKinou2SecurityDto(defaultStrKghAdmSecurityUseDto);
        }
        return result;
    }

    /**
     * 指定した機能が利用可能か判断する(機能IDバージョン).
     *
     * <p>
     * 【20】
     *
     * @param shokuId   職員ID.
     * @param sysCd     システムコード.
     * @param svJigyoId 事業所ID.
     * @param kinouName 機能名.
     * @return 機能権限情報.
     * <AUTHOR>
     */
    public KinouSecurityDto chkUseKinou2(int shokuId, String sysCd, int svJigyoId, String kinouName) throws Exception {
        KinouSecurityDto result = new KinouSecurityDto();

        // 機能名リスト
        List<String> kinouNameList = new ArrayList<String>();
        // 【引数】.機能名 => リスト
        kinouNameList.add(kinouName);
        // (1)-1 第３階層の機能IDを取得する。
        KinouIdOutDto resultKinouIdOutDto = kghAdmSecPafLogic.getKinouId(CommonConstants.INT_3, kinouNameList);

        // 機能IDリスト
        List<Integer> kinouIdList = resultKinouIdOutDto.getKinouIdList();
        // 取得フラグ
        Integer kinouIdGetFlg = resultKinouIdOutDto.getKinouIdGetFlg();

        // (2) 権限チェック
        // (2)-1 上記(1)で取得した取得フラグが1：取得の場合、【戻り値】.機能権限情報に下記取得した機能権限情報を設定して返却する。
        if (kinouIdGetFlg == CommonConstants.INT_1
                && CollectionUtils.isNotEmpty(kinouIdList)) {
            return chkUseKinou2(shokuId, sysCd, svJigyoId, kinouIdList.get(0));

            // (2)-2 上記以外の場合、戻り値】.機能権限情報が下記のように設定して返却する。
        } else {
            StrKghAdmSecurityUseDto all0StrKghAdmSecurityUseDto = new StrKghAdmSecurityUseDto();
            // 閲覧権限 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse1(CommonConstants.INT_0);
            // 印刷権限 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse2(CommonConstants.INT_0);
            // 外部出力権限 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse3(CommonConstants.INT_0);
            // 権限4 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse4(CommonConstants.INT_0);
            // 権限5 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse5(CommonConstants.INT_0);
            // 権限6 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse6(CommonConstants.INT_0);
            // 権限7 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse7(CommonConstants.INT_0);
            // 権限8 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse8(CommonConstants.INT_0);
            // 登録権限 0:権限なし
            all0StrKghAdmSecurityUseDto.setUse9(CommonConstants.INT_0);
            // 変換した機能権限情報を【戻り値】.機能権限情報に設定して返却する
            result = setKinou2SecurityDto(all0StrKghAdmSecurityUseDto);
        }
        return result;
    }

    /**
     * 機能・マスタ・その他の分岐用関数(機能ID・事業所単一).
     *
     * <p>
     * 【1】
     *
     * @param shokuId   職員ID.
     * @param sysCd     システムコード.
     * @param svJigyoId 事業所ID.
     * @param kinouId   機能ID.
     * @return 機能権限情報.
     * @throws Exception 例外.
     */
    public KinouSecurityDto chkUseByKinouId(
            Integer shokuId,
            String sysCd,
            Integer svJigyoId,
            Integer kinouId)
            throws Exception {
        KinouSecurityDto result = new KinouSecurityDto();
        // 【戻り値】.機能権限情報.閲覧権限 ＝ 【引数】.閲覧権限
        result.setUse1(1);
        // 【戻り値】.機能権限情報.印刷権限 ＝ 【引数】.印刷権限
        result.setUse2(1);
        // 【戻り値】.機能権限情報.外部出力権限 ＝ 【引数】.外部出力権限
        result.setUse3(1);
        // 【戻り値】.機能権限情報.登録権限 ＝ 【引数】.登録権限
        result.setUse9(1);
        return result;
    }

    /**
     * セキュリティ基本の設定を取得する.
     *
     * <p>
     * 【23】
     *
     * @return セキュリティ基本設定.
     * <AUTHOR>
     */
    public KghAdmSecSettingDto getSecSetting() throws Exception {
        KghAdmSecSettingDto result = new KghAdmSecSettingDto();

        // (1) テーブル「02-01 セキュリティ基本設定」からセキュリティ基本のデータを取得する
        KghAdmSecurityByCriteriaInEntity inEntity = new KghAdmSecurityByCriteriaInEntity();
        KghAdmSecurityOutEntity kghAdmSecurityOutEntity = comMocSecSettingSelectMapper
                .countKghAdmSecurityByCriteria(inEntity);

        // 上記取得した結果がある場合
        if (CommonConstants.STR_0.equals(kghAdmSecurityOutEntity.getCount())) {
            // セキュリティ単位(1:個人)
            result.setSecUnit(CommonConstants.INT_1);
            // 自己パスワード変更(0:なし)
            result.setPassmodifyFlg(CommonConstants.INT_0);
            // パスワード有効期間(0:無期限)
            result.setPassvalidTerm(CommonConstants.INT_0);
            // パスワード有効期限切れ時のログインフラグ(0:不可)
            result.setPassvalidFlg(CommonConstants.INT_0);
            // アカウントの記憶(0:なし)
            result.setAccountMemory(CommonConstants.INT_0);
            // ログイン画面でのアカウント情報記憶フラグ (0:しない)
            // 【戻り値】.システム別事業所制限フラグ
            result.setSystemSvjigyoFlg(CommonConstants.INT_0);
            // 事業所別機能制限(0:なし)
            result.setSvjigyoKinouFlg(CommonConstants.INT_0);
            // システム権限初期値(0:なし)
            result.setDefaultAuthSystem(CommonConstants.INT_0);
            // 機能権限初期値(9:あり)
            result.setDefaultAuthKinou(CommonConstants.INT_9);
            // 事業所権限初期値(1:あり)
            result.setDefaultAuthSvjigyo(CommonConstants.INT_1);
            // 部門権限初期値(1:あり)
            result.setDefaultAuthOrg(CommonConstants.INT_1);
        } else if (kghAdmSecurityOutEntity.getCount().compareTo(CommonConstants.STR_0) > 0) {
            // 検索条件設定
            TopKghAdmSecurityByCriteriaInEntity inEntity2 = new TopKghAdmSecurityByCriteriaInEntity();
            // セキュリティ基本設定情報取得
            List<TopKghAdmSecurityOutEntity> topKghAdmSecurityOutEntityList = comMocSecSettingSelectMapper
                    .findTopKghAdmSecurityByCriteria(inEntity2);

            TopKghAdmSecurityOutEntity outEntity = new TopKghAdmSecurityOutEntity();
            if (CollectionUtils.isNotEmpty(topKghAdmSecurityOutEntityList)) {
                // 1件名のレコードを取得
                outEntity = topKghAdmSecurityOutEntityList.get(0);
            }

            // セキュリティ単位
            result.setSecUnit(CommonDtoUtil.strValToInt(outEntity.getSecUnit()));
            // パスワード変更フラグ
            result.setPassmodifyFlg(outEntity.getPassmodifyFlg().intValue());
            // パスワード有効期間
            result.setPassvalidTerm(outEntity.getPassvalidTerm().intValue());
            // パスワード有効期限切れ時のログインフラグ
            result.setPassvalidFlg(outEntity.getPassvalidFlg().intValue());
            // アカウントの記憶
            result.setAccountMemory(outEntity.getAccountMemory().intValue());
            // ログイン画面でのアカウント情報記憶フラグ
            // 【戻り値】.システム別事業所制限フラグ
            result.setSystemSvjigyoFlg(outEntity.getSystemSvjigyoFlg().intValue());
            // 事業所別機能制限
            result.setSvjigyoKinouFlg(outEntity.getSvjigyoKinouFlg().intValue());
            // システム権限初期値
            result.setDefaultAuthSystem(outEntity.getDefaultAuthSystem().intValue());
            // 機能権限初期値
            result.setDefaultAuthKinou(outEntity.getDefaultAuthKinou().intValue());
            // 事業所権限初期値
            result.setDefaultAuthSvjigyo(outEntity.getDefaultAuthSvjigyo().intValue());
            // 部門権限初期値
            result.setDefaultAuthOrg(outEntity.getDefaultAuthOrg().intValue());
            // 管理者以外へのシステム管理権限の付与
            result.setAdminFlg(outEntity.getAdminFlg());
        }
        return result;
    }

    /**
     * 指定した機能が利用可能か判断する(機能IDバージョン).
     *
     * <p>
     * 【26】
     *
     * @param shokuId 職員ID.
     * @return 管理権限フラグ.
     * <AUTHOR>
     */
    public int ufPassKengen(int shokuId) throws Exception {
        // 管理権限フラグ 0：なし、1：あり 、-1：取得失敗
        int result = CommonConstants.INT_0;

        // (1) admin職員のチェック
        // 【引数】.職員IDが「0：管理者権限」の場合
        if (shokuId == CommonConstants.INT_0) {
            // 【引数】.職員IDが「0：管理者権限」の場合
            // 【戻リ値】.管理権限フラグに「1：あり」を設定して返却する。
            // ※admin職員は管理者権限がある
            result = CommonConstants.INT_1;
            return result;
        }

        // (2) セキュリティ基本の設定を取得する。
        KghAdmSecSettingDto resultSecSettingOutDto = getSecSetting();

        // セキュリティ単位 取得できない場合
        if (resultSecSettingOutDto == null) {
            return result;
        }

        // セキュリティ単位
        int secUnit = resultSecSettingOutDto.getSecUnit();

        // (3) 管理権限を取得する。
        // 上記取得した結果.セキュリティ単位が「1：個人」の場合
        if (secUnit == CommonConstants.INT_1) {
            // 管理権限フラグ
            int adminFlg = 0;
            // 検索条件設定
            KojinYuukoAdminFlgByCriteriaInEntity inEntity = new KojinYuukoAdminFlgByCriteriaInEntity();
            inEntity.setAlShokuId(CommonDtoUtil.objValToString(shokuId));
            // 03-02 職員アカウント（パスワード）情報取得
            List<KojinYuukoAdminFlgOutEntity> kojinYuukoAdminFlgOutEntityList = comMscPasswordSelectMapper
                    .findKojinYuukoAdminFlgByCriteria(inEntity);

            // 上記取得した結果がある場合
            if (CollectionUtils.isNotEmpty(kojinYuukoAdminFlgOutEntityList)) {
                adminFlg = kojinYuukoAdminFlgOutEntityList.get(0).getAdminFlg();
            }

            // 上記取得した結果がない場合
            if (adminFlg == 0) {
                // 【戻リ値】.管理権限フラグ = -1：取得失敗
                result = CommonConstants.INT_MINUS_1;

                // 上記取得した結果がある場合
            } else {
                // 【戻リ値】.管理権限フラグ = 上記(3)で取得した結果.管理権限フラグ
                result = adminFlg;
            }
            // 上記取得した結果.セキュリティ単位が「2：グループ(共通)」或いは「3：グループ(組み合わせ)」の場合
        } else if (secUnit == CommonConstants.INT_2) {
            // 検索条件設定
            KghAdmSecGroupShk2ByCriteriaInEntity inEntity2 = new KghAdmSecGroupShk2ByCriteriaInEntity();
            // 機能ID
            inEntity2.setChkShokuId(shokuId);
            // 職員所属セキュリティグループ情報を取得
            List<KghAdmSecGroupShk2OutEntity> kghAdmSecGroupShk2OutEntityList = comMocSecGroupApplyInfoSelectMapper
                    .findKghAdmSecGroupShk2ByCriteria(inEntity2);

            // 上記取得した結果がある場合
            if (CollectionUtils.isNotEmpty(kghAdmSecGroupShk2OutEntityList)) {
                KghAdmSecGroupShk2OutEntity kghAdmSecGroupShk2OutEntity = kghAdmSecGroupShk2OutEntityList.get(0);
                if (kghAdmSecGroupShk2OutEntity.getSecGroupKbn() == CommonConstants.SEC_GROUP_KBN_0
                        && kghAdmSecGroupShk2OutEntity.getAdminFlg() == CommonConstants.INT_1) {
                    result = CommonConstants.INT_1;
                } else {
                    result = CommonConstants.INT_0;
                }
            } else {
                result = CommonConstants.INT_0;
            }
        } else if (secUnit == CommonConstants.INT_3) {
            // 検索条件設定
            KghAdmSecGroupShk2ByCriteriaInEntity inEntity3 = new KghAdmSecGroupShk2ByCriteriaInEntity();
            // 機能ID
            inEntity3.setChkShokuId(shokuId);
            // 職員所属セキュリティグループ情報を取得
            List<KghAdmSecGroupShk2OutEntity> kghAdmSecGroupShk2OutEntityList = comMocSecGroupApplyInfoSelectMapper
                    .findKghAdmSecGroupShk2ByCriteria(inEntity3);

            // 上記取得した結果がある場合
            if (CollectionUtils.isNotEmpty(kghAdmSecGroupShk2OutEntityList)) {
                KghAdmSecGroupShk2OutEntity kghAdmSecGroupShk2OutEntity = kghAdmSecGroupShk2OutEntityList.get(0);
                if (kghAdmSecGroupShk2OutEntity.getSecGroupKbn() == CommonConstants.SEC_GROUP_KBN_1
                        && kghAdmSecGroupShk2OutEntity.getAdminFlg() == CommonConstants.INT_1) {
                    result = CommonConstants.INT_1;
                } else {
                    result = CommonConstants.INT_0;
                }
            } else {
                result = CommonConstants.INT_0;
            }
        }
        return result;
    }

    /**
     * 渡された機能IDのライセンスのチェックを行う.
     *
     * <p>
     * 【29】
     *
     * @param kinouId 機能ID.
     * @return チェック結果.
     * <AUTHOR>
     */
    public boolean chkUseLisence(int kinouId) throws Exception {
        boolean result = false;
        // 機能情報リストを取得する。
        List<KghComGetMenuOutEntity> comMocKinouNameList = getKinouNameList();
        // 機能情報リスト取得失敗の場合
        if (CollectionUtils.isEmpty(comMocKinouNameList)) {
            return result;
        }

        // 上記取得した機能情報リスト
        for (int i = 0; i < comMocKinouNameList.size(); i++) {
            // 上記(1)で取得した機能情報リストに【引数】.機能IDが存在する場合
            if (comMocKinouNameList.get(i).getKinouId() == kinouId) {
                // 【戻り値】.チェック結果 = true:取得成功
                result = true;
                return result;
            }
        }
        // 【戻り値】.チェック結果 = false:取得失敗
        return result;
    }

    /**
     * 機能利用可能判断(機能名バージョン)
     * 機能・マスタ・その他の分岐用関数(機能名/事業所配列)
     * long uf_use_chk (long al_shoku_id, string as_sys_cd, long al_sv_jigyo_id,
     * string as_kinou_name)
     * 
     * @param shokuId   職員ID
     * @param sysCd     システムコード
     * @param svJigyoId 事業所ID
     * @param kinouName 機能名
     * @return 機能利用可能判断((機能名バージョン)
     */
    public Integer chkUseByKinouName(Integer shokuId, String sysCd,
            Integer svJigyoId, String kinouName) {
        return 1;
    }

    /**
     * 高速化フラグを取得する.
     *
     * <p>
     * 【31】
     *
     * @param sysCd システムコード.
     * @return 高速化フラグ.
     */
    private int getSpdupChkKengen(String sysCd) throws Exception {
        F3gkGetProfileInDto inDto = new F3gkGetProfileInDto();
        // f3gk_getprofile( 0, 0, 0, 0, "スイッチ", "速度改善", "権限チェック", "0" )
        // システムコード
        inDto.setGsyscd(sysCd);
        // 職員ID
        inDto.setShokuId(Integer.valueOf(CommonConstants.STR_ZERO));
        // 法人ID
        inDto.setHoujinId(Integer.valueOf(CommonConstants.STR_ZERO));
        // 施設ID
        inDto.setShisetuId(Integer.valueOf(CommonConstants.STR_ZERO));
        // 事業所ID
        inDto.setSvJigyoId(Integer.valueOf(CommonConstants.STR_ZERO));
        // 画面名 スイッチ
        inDto.setKinounameKnj(CommonConstants.STR_KINOUNAMEKNJ);
        // セクション
        inDto.setSectionKnj(CommonConstants.STR_SPEED_IMPROVE);
        // キー
        inDto.setKeyKnj(CommonConstants.STR_SEC_CHK);
        // 初期値
        inDto.setAsDefault(CommonConstants.STR_ZERO);

        int result = Integer.parseInt(nds3GkFunc01Logic.getF3gkProfile(inDto));

        return result;
    }

    /**
     * 機能権限情報を設定する.
     *
     * @param inDto 機能権限1～9権限設定.
     * @return 機能権限情報DTO.
     */
    private KinouSecurityDto setKinou2SecurityDto(StrKghAdmSecurityUseDto inDto) {
        KinouSecurityDto result = new KinouSecurityDto();
        // (1) 【戻り値】.機能権限情報の初期化処理
        // 【戻り値】.機能権限情報.閲覧権限 ＝ 【引数】.閲覧権限
        result.setUse1(inDto.getUse1());
        // 【戻り値】.機能権限情報.印刷権限 ＝ 【引数】.印刷権限
        result.setUse2(inDto.getUse2());
        // 【戻り値】.機能権限情報.外部出力権限 ＝ 【引数】.外部出力権限
        result.setUse3(inDto.getUse3());
        // 【戻り値】.機能権限情報.登録権限 ＝ 【引数】.登録権限
        result.setUse9(inDto.getUse9());
        // 【戻り値】.機能権限情報.権限フラグ ＝ 「0:権限なし」
        result.setAuthFlg(CommonConstants.INT_0);

        // (2) 権限フラグの判定処理
        // (2)-1 【引数】.登録権限が「1:権限あり」の場合
        if (inDto.getUse9() == CommonConstants.INT_1) {
            // 【戻り値】.機能権限情報.権限フラグ＝「9:登録」
            result.setAuthFlg(CommonConstants.INT_9);
            return result;
        }

        // (2)-2 【引数】.外部出力権限が「1:権限あり」の場合
        if (inDto.getUse3() == CommonConstants.INT_1) {
            // 【戻り値】.機能権限情報.権限フラグ＝「3:外部出力」
            result.setAuthFlg(CommonConstants.INT_3);
            return result;
        }

        // (2)-3 【引数】.印刷権限が「1:権限あり」の場合
        if (inDto.getUse2() == CommonConstants.INT_1) {
            // 【戻り値】.機能権限情報.権限フラグ＝「2:印刷」
            result.setAuthFlg(CommonConstants.INT_2);
            return result;
        }

        // (2)-4 【引数】.閲覧権限が「1:権限あり」の場合
        if (inDto.getUse1() == CommonConstants.INT_1) {
            // 【戻り値】.機能権限情報.権限フラグ＝「1:閲覧」
            result.setAuthFlg(CommonConstants.INT_1);
            return result;
        }
        return result;
    }

    /**
     * 機能情報リストを取得する.
     *
     * @return 機能情報リスト.
     */
    private List<KghComGetMenuOutEntity> getKinouNameList() throws Exception {
        List<KghComGetMenuOutEntity> result = new ArrayList<KghComGetMenuOutEntity>();

        F3gkChkLicenseOutDto resultF3gkChkLicenseOutDto = kghBase01Logic.getF3gkChkLicense();
        result = resultF3gkChkLicenseOutDto.getComMocKinouName();
        return result;
    }

    /**
     * 指定した機能が利用可能か判断する(機能IDバージョン/事業所配列バージョン)
     * 
     * @param shokuId       職員ID
     * @param sysCd         システムコード
     * @param svJigyoIdList 事業所IDリスト
     * @param kinouId       機能ID
     * @param btnName       ボタン名
     * @return チェック結果(0:使用不可、1:閲覧、2:印刷、3:外部出力、9:登録).
     * <AUTHOR>
     * @throws Exception
     */
    public Integer chkUseKinou(Integer shokuId, String sysCd, List<Integer> svJigyoIdList, Integer kinouId,
            String btnName)
            throws Exception {
        if (svJigyoIdList.size() <= CommonConstants.INT_1) {
            if (svJigyoIdList.isEmpty()) {
                svJigyoIdList.add(CommonConstants.INT_0);
                return chkUseKinou(shokuId, sysCd, svJigyoIdList.get(0), kinouId, btnName);
            }
        }

        // -----------------------------------------------------------------
        // 変数
        // -----------------------------------------------------------------
        Integer parentId;
        String ryaku = StringUtils.EMPTY;
        Integer mm = CommonConstants.INT_0;
        Integer svJigyoFlg = CommonConstants.INT_0;
        List<Integer> kinouIdList = new ArrayList<>();
        List<Integer> jgyGroupIdList = new ArrayList<>();
        List<Integer> groupIdList = new ArrayList<>();

        // 高速化フラグを取得する。
        int spdupChkKengen = getSpdupChkKengen(sysCd);
        if (spdupChkKengen == CommonConstants.INT_2) {
            // 第１階層がシステム管理／職員管理／国保請求／本部請求でない場合は新しいコードを実行
            parentId = kghAdmSecPafLogic.getParentKinouId(kinouId);
            // 上記取得した第１階層の機能IDが「10000000」、「20000000」、「48000000」、「50000000」以外の場合、下記の処理を行う。
            if (parentId != CommonConstants.INT_10000000
                    && parentId != CommonConstants.INT_20000000
                    && parentId != CommonConstants.INT_48000000
                    && parentId != CommonConstants.INT_50000000) {
                // 下記の高速化権限チェックを行う。
                KengenKinouSingleInDto kengenKinouSingleInDto = new KengenKinouSingleInDto();
                kengenKinouSingleInDto.setSvJigyoIdList(svJigyoIdList);
                // 機能区分 = 3
                kengenKinouSingleInDto.setKinouKbn(CommonConstants.INT_3);
                // 機能ID = 【引数】.機能ID
                kengenKinouSingleInDto.setKinouId(kinouId);
                // 期待する最低の権限 = 0
                kengenKinouSingleInDto.setAuthFm(CommonConstants.INT_0);
                // 期待する最高の権限 = 9
                kengenKinouSingleInDto.setAuthTo(CommonConstants.INT_9);
                // ログイン職員ID = 【引数】.職員ID
                kengenKinouSingleInDto.setShokuinId(shokuId);
                int retKengenKinouSingleRet = kghAdmSecPafLogic.retKengenKinouSingle(kengenKinouSingleInDto);
                // 【戻リ値】.チェック結果に上記取得したチェック結果を設定して返却する。
                return retKengenKinouSingleRet;
            }
        }
        // ライセンスのチェックを行う
        boolean chkUseLisenceRet = chkUseLisence(kinouId);
        // 上記取得したチェック結果が「false」の場合、【戻リ値】.チェック結果に「0：使用不可」を設定して返却する。
        if (!chkUseLisenceRet) {
            return CommonConstants.INT_0;
        }
        // // 職員IDが0の場合は管理者権限
        if (shokuId == CommonConstants.INT_0) {
            // 管理者権限を取得する
            return CommonConstants.INT_9;
        }
        // 事業所ID配列のチェック
        if (sysCd.equals(CommonConstants.SYSTEM_CODE_71101)) {
            if (CollectionUtils.isEmpty(svJigyoIdList)) {
                return CommonConstants.INT_0;
            }
        }

        // セキュリティ基本の設定を取得
        int passKengen = ufPassKengen(shokuId);
        // 上記取得した管理者権限フラグが「1：あり」の場合
        // 【戻リ値】.チェック結果に「9：登録」を設定して返却する。
        if (passKengen == CommonConstants.INT_1) {
            return CommonConstants.INT_9;
            // 上記以外の場合
            // 上記取得したシステム区分が「SYS」の場合、【戻リ値】.チェック結果に「1：閲覧」を設定して返却する。
        } else {
            if (CommonConstants.SYS_MANAGE.equals(btnName)) {
                // 「職員管理＞職員基本＞システム管理」から起動した場合
                return CommonConstants.INT_9;
            } else {
                SysCdKbnByCriteriaInEntity sysCdKbnByCriteriaInEntity = new SysCdKbnByCriteriaInEntity();
                // システムコード
                sysCdKbnByCriteriaInEntity.setAsSysCd(sysCd);
                // 機能ID
                sysCdKbnByCriteriaInEntity.setAlKinou(CommonDtoUtil.objValToString(kinouId));
                // システム区分を取得する
                List<SysCdKbnOutEntity> sysCdKbnByCriteria = comMocKinouNameSelectMapper
                        .findSysCdKbnByCriteria(sysCdKbnByCriteriaInEntity);
                if (CollectionUtils.isNotEmpty(sysCdKbnByCriteria)) {
                    // 上記取得したシステム区分
                    ryaku = sysCdKbnByCriteria.getFirst().getSysCdKbn();
                    // 上記取得したシステム区分が「SYS」の場合
                    if (CommonConstants.SYS_CD_KBN_SYS.equals(ryaku)) {
                        // 【戻リ値】.チェック結果に「1：閲覧」を設定して返却する。
                        return CommonConstants.INT_1;
                    }
                }
            }
        }

        KghAdmSecSettingDto secSetting = getSecSetting();
        // 変数.サービス事業者IDリスト
        List<Integer> tempSvJigyoIdList = new ArrayList<>();
        if (secSetting.getSvjigyoKinouFlg() == CommonConstants.INT_0) {
            // 上記取得したシステム区分が「JIN」の場合
            if (CommonConstants.SYS_CD_KBN_JIN.equals(ryaku)) {
                tempSvJigyoIdList.set(0, CommonConstants.INT_0);
                svJigyoIdList = tempSvJigyoIdList;
                svJigyoFlg = CommonConstants.INT_1;
                // 上記以外の場合
                // 変数.サービス事業者IDリスト = 【引数】.事業所IDリスト
            } else {
                tempSvJigyoIdList = svJigyoIdList;
            }
        } else {
            // 【引数】.システムコードが「51201」 、「51301」、「51501」 の場合
            if (CommonConstants.SYS_CD_51201.equals(sysCd)
                    || CommonConstants.SYS_CD_51301.equals(sysCd)
                    || CommonConstants.SYS_CD_51501.equals(sysCd)) {
                tempSvJigyoIdList.add(CommonConstants.INT_0);
                svJigyoIdList = tempSvJigyoIdList;
                svJigyoFlg = CommonConstants.INT_1;
            } else {
                // 上記取得したシステム区分が「JIN」の場合
                if (CommonConstants.SYS_CD_KBN_JIN.equals(ryaku)) {
                    tempSvJigyoIdList.set(0, CommonConstants.INT_0);
                    svJigyoIdList = tempSvJigyoIdList;
                    svJigyoFlg = CommonConstants.INT_1;
                    // 上記以外の場合
                    // 変数.サービス事業者IDリスト = 【引数】.事業所IDリスト
                } else {
                    // 渡された事業所IDのチェックを行い、0があれば抜く
                    for (int i = 0; i < svJigyoIdList.size(); i++) {
                        if (svJigyoIdList.get(i) != CommonConstants.INT_0) {
                            mm += CommonConstants.INT_1;
                            tempSvJigyoIdList.set(mm, svJigyoIdList.get(i));
                        }
                    }
                    // 事業所ID配列のチェック
                    if (CollectionUtils.isEmpty(tempSvJigyoIdList)) {
                        // 事業所ID配列が空の場合は0を返す
                        return CommonConstants.INT_0;
                    }
                }
            }
        }

        if (svJigyoFlg == CommonConstants.INT_1) {
            return chkUseKinou(shokuId, sysCd, tempSvJigyoIdList.get(0), kinouId, btnName);
        }
        // 機能マスタの件数読み込み
        kinouIdList.add(kinouId);
        List<KghAdmSecKengenFuncShkUseKinouOutEntity> kghAdmSecKengenFuncShkUseKinouOutEntityList = new ArrayList<>();
        switch (secSetting.getSecUnit()) {
            case CommonConstants.INT_1:// 個人
                if (secSetting.getSvjigyoKinouFlg() == CommonConstants.INT_0) {
                    KghAdmSecKengenFuncShkUseKinouByCriteriaInEntity kghAdmSecKengenFuncShkUseKinouByCriteriaInEntity = new KghAdmSecKengenFuncShkUseKinouByCriteriaInEntity();
                    // システムコード
                    kghAdmSecKengenFuncShkUseKinouByCriteriaInEntity.setAsSysCd(sysCd);
                    // システム区分
                    kghAdmSecKengenFuncShkUseKinouByCriteriaInEntity.setAsSysCdKbn(ryaku);
                    // 職員ID
                    kghAdmSecKengenFuncShkUseKinouByCriteriaInEntity.setChkShokuId(shokuId);
                    // 事業所IDリスト
                    kghAdmSecKengenFuncShkUseKinouByCriteriaInEntity.setSvJigyoIdList(tempSvJigyoIdList);
                    // 機能IDリスト
                    kghAdmSecKengenFuncShkUseKinouByCriteriaInEntity.setKinouIdList(kinouIdList);
                    // システムサービス事業フラグ
                    kghAdmSecKengenFuncShkUseKinouByCriteriaInEntity
                            .setSystemSvjigyoFlg(secSetting.getSystemSvjigyoFlg());
                    // デフォルト権限サービス事業
                    kghAdmSecKengenFuncShkUseKinouByCriteriaInEntity
                            .setDefaultAuthSvjigyo(secSetting.getDefaultAuthSvjigyo());
                    // デフォルト権限機能
                    kghAdmSecKengenFuncShkUseKinouByCriteriaInEntity
                            .setDefaultAuthKinou(secSetting.getDefaultAuthKinou());
                    // 2:機能権限IDの取得
                    kghAdmSecKengenFuncShkUseKinouOutEntityList = comMocShkUseKinouSecKengenInfoSelectMapper
                            .findKghAdmSecKengenFuncShkUseKinouByCriteria(
                                    kghAdmSecKengenFuncShkUseKinouByCriteriaInEntity);

                } else {
                    KghAdmSecKengenFuncShkUseKinou4ByCriteriaInEntity kghAdmSecKengenFuncShkUseKinou4ByCriteriaInEntity = new KghAdmSecKengenFuncShkUseKinou4ByCriteriaInEntity();
                    // システムコード
                    kghAdmSecKengenFuncShkUseKinou4ByCriteriaInEntity.setAsSysCd(sysCd);
                    // システム区分
                    kghAdmSecKengenFuncShkUseKinou4ByCriteriaInEntity.setAsSysCdKbn(ryaku);
                    // 職員ID
                    kghAdmSecKengenFuncShkUseKinou4ByCriteriaInEntity.setChkShokuId(shokuId);
                    // 事業所IDリスト
                    kghAdmSecKengenFuncShkUseKinou4ByCriteriaInEntity.setSvJigyoIdList(tempSvJigyoIdList);
                    // 機能IDリスト
                    kghAdmSecKengenFuncShkUseKinou4ByCriteriaInEntity.setKinouIdList(kinouIdList);
                    // システムサービス事業フラグ
                    kghAdmSecKengenFuncShkUseKinou4ByCriteriaInEntity
                            .setSystemSvjigyoFlg(secSetting.getSystemSvjigyoFlg());
                    // デフォルト権限サービス事業
                    kghAdmSecKengenFuncShkUseKinou4ByCriteriaInEntity
                            .setDefaultAuthSvjigyo(secSetting.getDefaultAuthSvjigyo());
                    // デフォルト権限機能
                    kghAdmSecKengenFuncShkUseKinou4ByCriteriaInEntity
                            .setDefaultAuthKinou(secSetting.getDefaultAuthKinou());
                    List<KghAdmSecKengenFuncShkUseKinou4OutEntity> kghAdmSecKengenFuncShkUseKinou4ByCriteria = comMocShkUseKinou4SecKengenInfoSelectMapper
                            .findKghAdmSecKengenFuncShkUseKinou4ByCriteria(
                                    kghAdmSecKengenFuncShkUseKinou4ByCriteriaInEntity);
                    kghAdmSecKengenFuncShkUseKinouOutEntityList = kghAdmSecKengenFuncShkUseKinou4ByCriteria.stream()
                            .map(e -> {
                                KghAdmSecKengenFuncShkUseKinouOutEntity outEntity = new KghAdmSecKengenFuncShkUseKinouOutEntity();
                                outEntity.setSvJigyoId(e.getSvJigyoId());
                                outEntity.setChk(e.getChk());
                                outEntity.setKinouKbn1(e.getKinouKbn1());
                                outEntity.setKinouIdKbn1(e.getKinouIdKbn1());
                                outEntity.setKinouKbn2(e.getKinouKbn2());
                                outEntity.setKinouIdKbn2(e.getKinouIdKbn2());
                                outEntity.setKinouKbn3(e.getKinouKbn3());
                                outEntity.setKinouIdKbn3(e.getKinouIdKbn3());
                                outEntity.setSort1(e.getSort1());
                                outEntity.setSort2(e.getSort2());
                                outEntity.setSort3(e.getSort3());
                                return outEntity;
                            }).toList();
                }
                break;
            case CommonConstants.INT_2:// グループ
            case CommonConstants.INT_3:
                List<KghAdmSecGroupShkOutEntity> kghAdmSecGroupShkOutEntityList = new ArrayList<>();
                if (spdupChkKengen == CommonConstants.INT_0) {
                    KghAdmSecGroupShkByCriteriaInEntity kghAdmSecGroupShkByCriteriaInEntity = new KghAdmSecGroupShkByCriteriaInEntity();
                    kghAdmSecGroupShkOutEntityList = shozokuSecurityGroupSelectMapper
                    .findKghAdmSecGroupShkByCriteria(kghAdmSecGroupShkByCriteriaInEntity);
                } else {
                    KghAdmSecGroupShk2ByCriteriaInEntity kghAdmSecGroupShk2ByCriteriaInEntity = new KghAdmSecGroupShk2ByCriteriaInEntity();
                    // 職員ID
                    kghAdmSecGroupShk2ByCriteriaInEntity.setChkShokuId(shokuId);
                    List<KghAdmSecGroupShk2OutEntity> kghAdmSecGroupShk2ByCriteria = comMocSecGroupApplyInfoSelectMapper
                            .findKghAdmSecGroupShk2ByCriteria(kghAdmSecGroupShk2ByCriteriaInEntity);
                    kghAdmSecGroupShkOutEntityList = kghAdmSecGroupShk2ByCriteria.stream()
                    .map(e -> {
                    KghAdmSecGroupShkOutEntity outEntity = new KghAdmSecGroupShkOutEntity();
                    outEntity.setSecGroupId(e.getSecGroupId());
                    outEntity.setChkShokuId(e.getChkShokuId());
                    outEntity.setShokuId(e.getShokuId());
                    outEntity.setTimeStmp(e.getTimeStmp());
                    outEntity.setDelFlg(e.getDelFlg());
                    outEntity.setSecGroupKbn(e.getSecGroupKbn());
                    outEntity.setAdminFlg(e.getAdminFlg());
                    outEntity.setOtherRead(e.getOtherRead());
                    outEntity.setSort(e.getSort());
                    outEntity.setNoticeFlg(e.getNoticeFlg());
                    return outEntity;
                    }).toList();
                }
                AtomicInteger groupKbn = new AtomicInteger(CommonConstants.SEC_GROUP_KBN_0);
                // 共通
                if (secSetting.getSecUnit() == CommonConstants.INT_2) {
                    groupKbn.set(CommonConstants.SEC_GROUP_KBN_0);
                }
                // 組合せ 事業所は3
                else if (secSetting.getSecUnit() == CommonConstants.INT_3) {
                    groupKbn.set(CommonConstants.INT_3);
                }

                kghAdmSecGroupShkOutEntityList = kghAdmSecGroupShkOutEntityList.stream()
                    .filter(p -> shokuId == p.getShokuId() 
                            && groupKbn.get() == p.getSecGroupKbn()
                            && CommonConstants.DEL_FLG_0 == p.getDelFlg())
                .toList();
                if (CollectionUtils.isNotEmpty(kghAdmSecGroupShkOutEntityList)) {
                    jgyGroupIdList = kghAdmSecGroupShkOutEntityList.stream().map(KghAdmSecGroupShkOutEntity::getSecGroupId).toList();
                    // 共通
                    if (secSetting.getSecUnit() == CommonConstants.INT_2) {
                        groupKbn.set(CommonConstants.SEC_GROUP_KBN_0);
                    }
                    // 組合せ 機能は2
                    else if (secSetting.getSecUnit() == CommonConstants.INT_3) {
                        groupKbn.set(CommonConstants.INT_2);
                    }
                    kghAdmSecGroupShkOutEntityList = kghAdmSecGroupShkOutEntityList.stream()
                        .filter(p -> shokuId == p.getShokuId() 
                                && groupKbn.get() == p.getSecGroupKbn()
                                && CommonConstants.DEL_FLG_0 == p.getDelFlg()).toList();
                    if (CollectionUtils.isNotEmpty(kghAdmSecGroupShkOutEntityList)) {
                        groupIdList = kghAdmSecGroupShkOutEntityList.stream().map(KghAdmSecGroupShkOutEntity::getSecGroupId).toList();
                    } else {
                        return CommonConstants.INT_0;
                    }
                    } else {
                        return CommonConstants.INT_0;
                    }
                if (secSetting.getSecUnit() == CommonConstants.INT_2) {
                    KghAdmSecKengenFuncGrpUseKinouDSByCriteriaInEntity kghAdmSecKengenFuncGrpUseKinouByCriteriaInEntity = new KghAdmSecKengenFuncGrpUseKinouDSByCriteriaInEntity();
                    // システムコード
                    kghAdmSecKengenFuncGrpUseKinouByCriteriaInEntity.setAsSysCd(sysCd);
                    // システム区分
                    kghAdmSecKengenFuncGrpUseKinouByCriteriaInEntity.setAsSysCdKbn(ryaku);
                    // セキュリティグループカウント
                    kghAdmSecKengenFuncGrpUseKinouByCriteriaInEntity.setSecGroupCount(groupIdList.size());
                    // 事業所IDリスト
                    kghAdmSecKengenFuncGrpUseKinouByCriteriaInEntity.setSvJigyoIdList(tempSvJigyoIdList);
                    // 機能IDリスト
                    kghAdmSecKengenFuncGrpUseKinouByCriteriaInEntity.setKinouIdList(kinouIdList);
                    // システムサービス事業フラグ
                    kghAdmSecKengenFuncGrpUseKinouByCriteriaInEntity
                            .setSystemSvjigyoFlg(secSetting.getSystemSvjigyoFlg());
                    // デフォルト権限サービス事業
                    kghAdmSecKengenFuncGrpUseKinouByCriteriaInEntity
                            .setDefaultAuthSvjigyo(secSetting.getDefaultAuthSvjigyo());
                    // デフォルト権限機能
                    kghAdmSecKengenFuncGrpUseKinouByCriteriaInEntity
                            .setDefaultAuthKinou(secSetting.getDefaultAuthKinou());
                    // セキュリティグループIDリスト
                    kghAdmSecKengenFuncGrpUseKinouByCriteriaInEntity
                            .setSecGroupIdList(groupIdList);
                    // 事業セキュリティグループカウント
                    kghAdmSecKengenFuncGrpUseKinouByCriteriaInEntity
                            .setJigyoSecGroupCount(jgyGroupIdList.size());
                    // 事業所セキュリティグループIDリスト
                    kghAdmSecKengenFuncGrpUseKinouByCriteriaInEntity
                            .setJigyoSecGroupIdList(jgyGroupIdList);

                    // 2:機能権限IDの取得
                    List<KghAdmSecKengenFuncGrpUseKinouDSOutEntity> kghAdmSecKengenFuncGrpUseKinouList = KinouPermissionFuncSelectMapper
                            .findKghAdmSecKengenFuncGrpUseKinouDSByCriteria(
                                    kghAdmSecKengenFuncGrpUseKinouByCriteriaInEntity);
                    kghAdmSecKengenFuncShkUseKinouOutEntityList = kghAdmSecKengenFuncGrpUseKinouList.stream()
                            .map(e -> {
                                KghAdmSecKengenFuncShkUseKinouOutEntity outEntity = new KghAdmSecKengenFuncShkUseKinouOutEntity();
                                outEntity.setSvJigyoId(e.getSvJigyoId());
                                outEntity.setChk(e.getChk());
                                outEntity.setKinouKbn1(e.getKinouKbnA());
                                outEntity.setKinouIdKbn1(e.getKinouIdA());
                                outEntity.setKinouKbn2(e.getKinouKbnB());
                                outEntity.setKinouIdKbn2(e.getKinouIdB());
                                outEntity.setKinouKbn3(e.getKinouKbnC());
                                outEntity.setKinouIdKbn3(e.getKinouIdC());
                                outEntity.setSort1(e.getSort());
                                outEntity.setSort2(e.getSort2());
                                outEntity.setSort3(e.getSort3());
                                return outEntity;
                            }).toList();
                } else if (secSetting.getSecUnit() == CommonConstants.INT_3) {
                    KghAdmSecKengenFuncGrpUseKinou3KinoByCriteriaInEntity kghAdmSecKengenFuncGrpUseKinou3ByCriteriaInEntity = new KghAdmSecKengenFuncGrpUseKinou3KinoByCriteriaInEntity();
                    // システムコード
                    kghAdmSecKengenFuncGrpUseKinou3ByCriteriaInEntity.setAsSysCd(sysCd);
                    // システム区分
                    kghAdmSecKengenFuncGrpUseKinou3ByCriteriaInEntity.setAsSysCdKbn(ryaku);
                    // セキュリティグループカウント
                    kghAdmSecKengenFuncGrpUseKinou3ByCriteriaInEntity.setSecGroupCount(groupIdList.size());
                    // 事業所IDリスト
                    kghAdmSecKengenFuncGrpUseKinou3ByCriteriaInEntity.setSvJigyoIdList(tempSvJigyoIdList);
                    // 機能IDリスト
                    kghAdmSecKengenFuncGrpUseKinou3ByCriteriaInEntity.setKinouIdList(kinouIdList);
                    // システムサービス事業フラグ
                    kghAdmSecKengenFuncGrpUseKinou3ByCriteriaInEntity
                            .setSystemSvjigyoFlg(secSetting.getSystemSvjigyoFlg());
                    // デフォルト権限サービス事業
                    kghAdmSecKengenFuncGrpUseKinou3ByCriteriaInEntity
                            .setDefaultAuthSvjigyo(secSetting.getDefaultAuthSvjigyo());
                    // デフォルト権限機能
                    kghAdmSecKengenFuncGrpUseKinou3ByCriteriaInEntity
                            .setDefaultAuthKinou(secSetting.getDefaultAuthKinou());
                    // セキュリティグループIDリスト
                    kghAdmSecKengenFuncGrpUseKinou3ByCriteriaInEntity
                            .setSecGroupIdList(groupIdList);
                    // 事業セキュリティグループカウント
                    kghAdmSecKengenFuncGrpUseKinou3ByCriteriaInEntity
                            .setJigyoSecGroupCount(jgyGroupIdList.size());
                    // 事業所セキュリティグループIDリスト
                    kghAdmSecKengenFuncGrpUseKinou3ByCriteriaInEntity
                            .setJigyoSecGroupIdList(jgyGroupIdList);
                    List<KghAdmSecKengenFuncGrpUseKinou3KinoOutEntity> kghAdmSecKengenFuncGrpUseKinou3List = kinouPermissionFunc2SelectMapper
                            .findKghAdmSecKengenFuncGrpUseKinou3KinoByCriteria(
                                    kghAdmSecKengenFuncGrpUseKinou3ByCriteriaInEntity);
                    kghAdmSecKengenFuncShkUseKinouOutEntityList = kghAdmSecKengenFuncGrpUseKinou3List.stream()
                            .map(e -> {
                                KghAdmSecKengenFuncShkUseKinouOutEntity outEntity = new KghAdmSecKengenFuncShkUseKinouOutEntity();
                                outEntity.setSvJigyoId(e.getSvJigyoId());
                                outEntity.setChk(e.getChk());
                                outEntity.setKinouKbn1(e.getKinouKbnA());
                                outEntity.setKinouIdKbn1(e.getKinouIdA());
                                outEntity.setKinouKbn2(e.getKinouKbnB());
                                outEntity.setKinouIdKbn2(e.getKinouIdB());
                                outEntity.setKinouKbn3(e.getKinouKbnC());
                                outEntity.setKinouIdKbn3(e.getKinouIdC());
                                outEntity.setSort1(e.getSort());
                                outEntity.setSort2(e.getSort2());
                                outEntity.setSort3(e.getSort3());
                                return outEntity;
                            }).toList();
                }
                break;
            default:
                break;
        }

        kghAdmSecKengenFuncShkUseKinouOutEntityList.stream()
                .sorted(Comparator.comparing(KghAdmSecKengenFuncShkUseKinouOutEntity::getSort1)
                        .thenComparing(KghAdmSecKengenFuncShkUseKinouOutEntity::getKinouKbn1)
                        .thenComparing(KghAdmSecKengenFuncShkUseKinouOutEntity::getSort2)
                        .thenComparing(KghAdmSecKengenFuncShkUseKinouOutEntity::getKinouKbn2)
                        .thenComparing(KghAdmSecKengenFuncShkUseKinouOutEntity::getSort3)
                        .thenComparing(KghAdmSecKengenFuncShkUseKinouOutEntity::getKinouKbn3)
                        .thenComparing(KghAdmSecKengenFuncShkUseKinouOutEntity::getSvJigyoId)
                        .thenComparing(KghAdmSecKengenFuncShkUseKinouOutEntity::getChk));

        if (CollectionUtils.isEmpty(kghAdmSecKengenFuncShkUseKinouOutEntityList)) {
            return CommonConstants.INT_0;
        }

        int minCompChk = kghAdmSecKengenFuncShkUseKinouOutEntityList.stream()
                .mapToInt(KghAdmSecKengenFuncShkUseKinouOutEntity::getChk)
                .min()
                .getAsInt();
        return minCompChk;
    }

    /**
     * 指定した機能が利用可能か判断する(機能IDバージョン)
     * 
     * @param shokuId   職員ID
     * @param sysCd     システムコード
     * @param svJigyoId 事業所ID
     * @param kinouId   機能ID
     * @param btnName   ボタン名
     * @return チェック結果(0:使用不可、1:閲覧、2:印刷、3:外部出力、9:登録).
     * <AUTHOR>
     * @throws Exception
     */
    public Integer chkUseKinou(Integer shokuId, String sysCd, Integer svJigyoId, Integer kinouId, String btnName)
            throws Exception {
        // -----------------------------------------------------------------
        // 変数
        // -----------------------------------------------------------------
        Integer parentId;
        String ryaku = StringUtils.EMPTY;
        // 高速化フラグを取得する。
        int spdupChkKengen = getSpdupChkKengen(sysCd);
        if (spdupChkKengen == CommonConstants.INT_2) {
            // 第１階層がシステム管理／職員管理／国保請求／本部請求でない場合は新しいコードを実行
            parentId = kghAdmSecPafLogic.getParentKinouId(kinouId);
            // 上記取得した第１階層の機能IDが「10000000」、「20000000」、「48000000」、「50000000」以外の場合、下記の処理を行う。
            if (parentId != CommonConstants.INT_10000000
                    && parentId != CommonConstants.INT_20000000
                    && parentId != CommonConstants.INT_48000000
                    && parentId != CommonConstants.INT_50000000) {
                // 下記の高速化権限チェックを行う。
                KengenKinouSingleInDto kengenKinouSingleInDto = new KengenKinouSingleInDto();
                // 事業所IDリスト = 【引数】.事業所ID
                List<Integer> svJigyoIdList = new ArrayList<Integer>();
                svJigyoIdList.add(svJigyoId);
                kengenKinouSingleInDto.setSvJigyoIdList(svJigyoIdList);
                // 機能区分 = 3
                kengenKinouSingleInDto.setKinouKbn(CommonConstants.INT_3);
                // 機能ID = 【引数】.機能ID
                kengenKinouSingleInDto.setKinouId(kinouId);
                // 期待する最低の権限 = 0
                kengenKinouSingleInDto.setAuthFm(CommonConstants.INT_0);
                // 期待する最高の権限 = 9
                kengenKinouSingleInDto.setAuthTo(CommonConstants.INT_9);
                // ログイン職員ID = 【引数】.職員ID
                kengenKinouSingleInDto.setShokuinId(shokuId);
                int retKengenKinouSingleRet = kghAdmSecPafLogic.retKengenKinouSingle(kengenKinouSingleInDto);
                // 【戻リ値】.チェック結果に上記取得したチェック結果を設定して返却する。
                return retKengenKinouSingleRet;
            }
        }

        // ライセンスのチェックを行う
        boolean chkUseLisenceRet = chkUseLisence(kinouId);
        // 上記取得したチェック結果が「false」の場合、【戻リ値】.チェック結果に「0：使用不可」を設定して返却する。
        if (!chkUseLisenceRet) {
            return CommonConstants.INT_0;
        }

        // 管理者権限を取得する
        int passKengen = ufPassKengen(shokuId);
        // 上記取得した管理者権限フラグが「1：あり」の場合
        // 【戻リ値】.チェック結果に「9：登録」を設定して返却する。
        if (passKengen == CommonConstants.INT_1) {
            return CommonConstants.INT_9;
            // 上記以外の場合
            // 上記取得したシステム区分が「SYS」の場合、【戻リ値】.チェック結果に「1：閲覧」を設定して返却する。
        } else {
            if (CommonConstants.SYS_MANAGE.equals(btnName)) {
                // 「職員管理＞職員基本＞システム管理」から起動した場合
                return CommonConstants.INT_9;
            } else {
                SysCdKbnByCriteriaInEntity sysCdKbnByCriteriaInEntity = new SysCdKbnByCriteriaInEntity();
                // システムコード
                sysCdKbnByCriteriaInEntity.setAsSysCd(sysCd);
                // 機能ID
                sysCdKbnByCriteriaInEntity.setAlKinou(CommonDtoUtil.objValToString(kinouId));
                // システム区分を取得する
                List<SysCdKbnOutEntity> sysCdKbnByCriteria = comMocKinouNameSelectMapper
                        .findSysCdKbnByCriteria(sysCdKbnByCriteriaInEntity);
                if (CollectionUtils.isNotEmpty(sysCdKbnByCriteria)) {
                    // 上記取得したシステム区分
                    ryaku = sysCdKbnByCriteria.getFirst().getSysCdKbn();
                    // 上記取得したシステム区分が「SYS」の場合
                    if (CommonConstants.SYS_CD_KBN_SYS.equals(ryaku)) {
                        // 【戻リ値】.チェック結果に「1：閲覧」を設定して返却する。
                        return CommonConstants.INT_1;
                    }
                }
            }
        }

        KghAdmSecSettingDto secSetting = getSecSetting();

        // 変数.サービス事業者IDを設定する。
        Integer tempSvJigyoId;
        // 上記取得した結果.事業所別機能制限が「0：なし」の場合
        // 変数.サービス事業者ID = 0
        if (secSetting.getSvjigyoKinouFlg() == CommonConstants.INT_0) {
            tempSvJigyoId = CommonConstants.INT_0;
            // 上記以外の場合
        } else {
            // 【引数】.システムコードが「51201」 、「51301」、「51501」 の場合
            if (CommonConstants.SYS_CD_51201.equals(sysCd)
                    || CommonConstants.SYS_CD_51301.equals(sysCd)
                    || CommonConstants.SYS_CD_51501.equals(sysCd)) {
                tempSvJigyoId = CommonConstants.INT_0;
                // 上記以外の場合
            } else {
                // 上記取得したシステム区分が「JIN」の場合
                // 変数.サービス事業者ID = 0
                if (CommonConstants.SYS_CD_KBN_JIN.equals(ryaku)) {
                    tempSvJigyoId = CommonConstants.INT_0;
                    // 上記以外の場合
                    // 変数.サービス事業者ID = 【引数】.事業所ID
                } else {
                    tempSvJigyoId = svJigyoId;
                }
            }
        }

        // 【引数】.事業所IDの再設定
        // 上記取得したシステム区分が「JIN」或いは「KNM」の場合、【引数】.事業所ID = 0
        if (CommonConstants.SYS_CD_KBN_JIN.equals(ryaku)
                || CommonConstants.SYS_CD_KBN_KNM.equals(ryaku)) {
            // 職員管理・勤務管理は事業所IDに関わらずメニューを出す
            svJigyoId = CommonConstants.INT_0;
        }

        // 変数.セキュリティグループ件数
        int tempGroupCount = CommonConstants.INT_0;
        // 権限フラグ(機能)
        Integer authFlg = CommonConstants.INT_0;
        // セキュリティグループ件数(機能)
        int rwc = CommonConstants.INT_0;

        // // 職員IDが0の場合は管理者権限
        if (shokuId == CommonConstants.INT_0) {
            return CommonConstants.INT_9;
        }

        // -----------------------------------------------------------------
        // 機能マスタの件数読み込み
        // -----------------------------------------------------------------
        switch (secSetting.getSecUnit()) {
            case CommonConstants.INT_1: // 個人
                KojinKinoKensuByCriteriaInEntity kojinKinoKensuByCriteriaInEntity = new KojinKinoKensuByCriteriaInEntity();
                // 職員ID
                kojinKinoKensuByCriteriaInEntity.setAlShokuin(shokuId);
                // 事業所ID
                kojinKinoKensuByCriteriaInEntity.setLlAlSvJigyoId(tempSvJigyoId);
                // 機能ID
                kojinKinoKensuByCriteriaInEntity.setAlKinou(kinouId);
                // システムコード
                kojinKinoKensuByCriteriaInEntity.setAsSysCd(sysCd);
                // セキュリティ基本設定の機能権限初期値
                kojinKinoKensuByCriteriaInEntity.setDefaultAuthKinou(CommonDtoUtil.intValToBigInt(secSetting.getDefaultAuthKinou()));
                List<KojinKinoKensuOutEntity> kojinKinoKensuByCriteria = comMocSecShkKinouSelectMapper
                        .findKojinKinoKensuByCriteria(kojinKinoKensuByCriteriaInEntity);
                if (CollectionUtils.isNotEmpty(kojinKinoKensuByCriteria)) {
                    // 権限フラグ(機能)
                    authFlg = kojinKinoKensuByCriteria.getFirst().getAuthFlg();
                    // セキュリティグループ件数(機能)
                    rwc = kojinKinoKensuByCriteria.getFirst().getKensu();
                    // authFlg = null;
                    // 変数.セキュリティグループ件数 = 1
                    tempGroupCount = CommonConstants.INT_1;
                    if (Objects.isNull(authFlg) && tempGroupCount > CommonConstants.INT_0) {
                        authFlg = secSetting.getDefaultAuthKinou();
                    } else {
                        if (Objects.nonNull(authFlg) && authFlg <= CommonConstants.INT_0) {
                            authFlg = CommonConstants.INT_0;
                        }
                    }
                }

                break;
            case CommonConstants.INT_2: // グループ(共通)
                GroupId054ByCriteriaInEntity inEntity3 = new GroupId054ByCriteriaInEntity();
                // 職員ID
                inEntity3.setShokuinId(CommonDtoUtil.objValToString(shokuId));
                // 職員所属セキュリティグループID件数情報取得
                GroupId054OutEntity groupId054OutEntity = comMocSecGroupApplyIdSelectMapper
                        .countGroupId054ByCriteria(inEntity3);
                // 変数.セキュリティグループ件数 = 上記取得した件数
                tempGroupCount = CommonDtoUtil.strValToInt(groupId054OutEntity.getCnt());
                // 検索条件設定
                KenkenFlgKinoKensukoujinByCriteriaInEntity inEntity4 = new KenkenFlgKinoKensukoujinByCriteriaInEntity();
                // 職員ID
                inEntity4.setAlShokuin(CommonDtoUtil.objValToString(shokuId));
                // 事業所ID
                inEntity4.setLlAlSvJigyoId(CommonDtoUtil.objValToString(tempSvJigyoId));
                // システムコード
                inEntity4.setAsSysCd(sysCd);
                // 機能ID
                inEntity4.setAlKinou(CommonDtoUtil.objValToString(kinouId));
                // セキュリティ基本設定の機能権限初期値
                inEntity4.setDefaultAuthKinou(CommonDtoUtil.objValToString(secSetting.getDefaultAuthKinou()));
                // 権限フラグ(機能)、セキュリティグループ件数(機能)を取得する
                List<KenkenFlgKinoKensukoujinOutEntity> kenkenFlgKinoKensukoujinByCriteria = groupKyoutuuKinoKengenSelectMapper
                        .findKenkenFlgKinoKensukoujinByCriteria(inEntity4);
                if (CollectionUtils.isNotEmpty(kenkenFlgKinoKensukoujinByCriteria)) {
                    authFlg = CommonDtoUtil.strValToInt(kenkenFlgKinoKensukoujinByCriteria.getFirst().getAuthFlg());
                    rwc = CommonDtoUtil.strValToInt(kenkenFlgKinoKensukoujinByCriteria.getFirst().getKensu());
                }

                if (Objects.isNull(authFlg) && tempGroupCount > CommonConstants.INT_0) {
                    authFlg = secSetting.getDefaultAuthKinou();
                } else {
                    if (Objects.nonNull(authFlg) && authFlg <= CommonConstants.INT_0) {
                        authFlg = CommonConstants.INT_0;
                    }
                }
                break;
            case CommonConstants.INT_3: // グループ(組み合わせ)
                // 検索条件設定
                GroupKumiawaseByCriteriaInEntity inEntity5 = new GroupKumiawaseByCriteriaInEntity();
                // 職員ID
                inEntity5.setShokuinId(CommonDtoUtil.objValToString(shokuId));
                // 職員所属セキュリティグループ組合件数情報取得
                GroupKumiawaseOutEntity groupKumiawaseOutEntity = comMocSecGroupKumiawaseSelectMapper
                        .countGroupKumiawaseByCriteria(inEntity5);
                tempGroupCount = CommonDtoUtil.strValToInt(groupKumiawaseOutEntity.getCnt());
                // 検索条件設定
                KenkenFlgKinoKensuGroupByCriteriaInEntity inEntity6 = new KenkenFlgKinoKensuGroupByCriteriaInEntity();
                // 職員ID
                inEntity6.setChkShokuId(shokuId);
                // 事業所ID
                inEntity6.setSvJigyoId(tempSvJigyoId);
                // システムコード
                inEntity6.setSysCd(sysCd);
                // 機能ID
                inEntity6.setKinouId(kinouId);
                // SecSettingの機能権限初期値
                inEntity6.setDefaultAuthKinou(CommonDtoUtil.intValToBigInt(secSetting.getDefaultAuthKinou()));
                // 権限フラグ(機能)、セキュリティグループ件数(機能)を取得する
                List<KenkenFlgKinoKensuGroupOutEntity> kojinKinoKensuGroupKbnOutEntityList = groupKumiawaseKinoKengenSelectMapper
                        .findKenkenFlgKinoKensuGroupByCriteria(inEntity6);
                if (CollectionUtils.isNotEmpty(kojinKinoKensuGroupKbnOutEntityList)) {
                    authFlg = kojinKinoKensuGroupKbnOutEntityList.getFirst().getAuthFlg();
                    rwc = kojinKinoKensuGroupKbnOutEntityList.getFirst().getKensu();
                }
                if (Objects.isNull(authFlg) && tempGroupCount > CommonConstants.INT_0) {
                    authFlg = secSetting.getDefaultAuthKinou();
                } else {
                    if (Objects.nonNull(authFlg) && authFlg <= CommonConstants.INT_0) {
                        authFlg = CommonConstants.INT_0;
                    }
                }
            default:
                break;
        }

        // 「事業所・機能」権限をチェックする
        // 事業所権限のチェック
        // 機能区分=1の機能IDを取得する
        int tempKinouId = 0;

        // 検索条件設定
        ParentIdByCriteriaInEntity inEntity7 = new ParentIdByCriteriaInEntity();
        inEntity7.setAlKinou(CommonDtoUtil.objValToString(shokuId));

        List<ParentIdOutEntity> parentIdList = comMocKinouNameSelectMapper
                .findParentIdByCriteria(inEntity7);
        if (CollectionUtils.isNotEmpty(parentIdList)) {
            parentId = parentIdList.getFirst().getParentId();
            // 検索条件設定
            // 機能ID
            inEntity7.setAlKinou(CommonDtoUtil.objValToString(parentId));
            // 01-02 機能管理(com_moc_kinou_name)を検索
            List<ParentIdOutEntity> kinouIdList = comMocKinouNameSelectMapper.findParentIdByCriteria(inEntity7);

            if (CollectionUtils.isNotEmpty(kinouIdList)) {
                tempKinouId = kinouIdList.getFirst().getParentId();
            }
        }

        // 事業所権限チェックを行う
        // 【引数】.事業所IDが「0」ではない場合、下記の事業所権限チェックを行う。
        if (svJigyoId != CommonConstants.INT_0) {
            int chkUseJigyoRet = chkUseJigyo(shokuId, sysCd, tempKinouId, svJigyoId);
            // 上記取得したチェック結果が「0」の場合
            if (chkUseJigyoRet == CommonConstants.INT_0) {
                // 【戻リ値】.チェック結果に「0：使用不可」を設定して返却する。
                return CommonConstants.INT_0;
            }
        }

        if (tempGroupCount == CommonConstants.INT_0) {
            authFlg = CommonConstants.INT_0;
        } else {
            if (tempGroupCount > rwc) {
                if (secSetting.getDefaultAuthKinou() == CommonConstants.INT_9) {
                    authFlg = secSetting.getDefaultAuthKinou();
                }
            }
        }
        return authFlg;
    }

    /**
     * セキュリティにより最終的に使用できる事業所を返す
     * uf_use_kinou_jigyo
     * 
     * @param shokuId        職員ID.
     * @param asSysCd        システムコード.
     * @param alSvJigyoId    事業所IDリスト.
     * @param asSysRyaku     システム略称.
     * @param spdupChkKengen 高速化フラグを
     * @param ilLic          機能情報リスト
     * @param alKinouId      機能IDの配列を返す
     * @return 本当に使える事業所ID.
     * <AUTHOR>
     */
    public List<Integer> getUseKinouJigyo(Integer shokuId, String asSysCd, Integer spdupChkKengen,
            List<Integer> alSvJigyoId, String asSysRyaku, List<Integer> ilLic,
            List<KghComGetMenuOutEntity> comMocKinouNameList, Integer secsetUnit,
            Integer secsetFlg, Integer secsetDefjigyo, Integer secsetBetjigyo, Boolean gbSpUser01,
            Integer secsetBetkinou, Integer secsetDefkinou,
            Integer adminFlg)
            throws Exception {
        List<Integer> llSvjigyoIdPaf = new ArrayList<>();
        List<Integer> alOKid = new ArrayList<>();
        List<Integer> llAlSvJigyoId = new ArrayList<>();
        Integer llSvjigyo0Flg = CommonConstants.NUMBER_0;
        GetUseKinouJigyoDto sreachDto = new GetUseKinouJigyoDto();
        Integer llSvJigyoId = CommonConstants.NUMBER_0;
        Integer llOldSvJigyoId = CommonConstants.NUMBER_0;
        List<Integer> llJgyGroupId = new ArrayList<>();
        List<Integer> llGroupId = new ArrayList<>();
        KengenSvjigyoOutDto resDto = new KengenSvjigyoOutDto();
        if (spdupChkKengen == CommonConstants.NUMBER_2) {
            if (kghAdmSecPafLogic.chkAdmin(shokuId) == CommonConstants.INT_1) {
                alOKid = alSvJigyoId;
            } else {
                if (ilLic.size() <= 0) {
                    Boolean res = useLisence(comMocKinouNameList, ilLic);
                    if (res == false) {
                        return null;
                    }
                }
                llSvjigyoIdPaf = alSvJigyoId;

                KengenSvjigyoInDto inDto = new KengenSvjigyoInDto();
                inDto.setLicId(ilLic);
                inDto.setSecsetFlg(secsetFlg);
                inDto.setAdminFlg(adminFlg);
                inDto.setSvjIdList(llSvjigyoIdPaf);
                inDto.setSecsetUnit(secsetUnit);
                // 職員id
                inDto.setShokuId(shokuId);
                // 特定ユーザフラグ
                inDto.setGbSpUser01(gbSpUser01);
                inDto.setSecsetDefjigyo(secsetDefjigyo);
                inDto.setSecsetBetjigyo(secsetBetjigyo);
                inDto.setSecsetBetkinou(secsetBetkinou);
                inDto.setSecsetDefkinou(secsetDefkinou);

                resDto = kghAdmSecPafLogic.getKengenSvjigyo(inDto);
                // ll_ret = ln_secpaf.uf_get_kengen_svjigyo( il_lic, ll_svjigyo_id_paf )
                if (resDto.getRslt() == CommonConstants.INT_1) {
                    alOKid = llSvjigyoIdPaf;
                }
            }
            // メニュー別事業所制限する場合で、且つ使用可能な事業所が存在しない時は初版からの仕様通り事業所リストを解放する
            if (resDto.getRslt() != CommonConstants.MESSAGE_KBN_NE_TWO) {
                return null;
            }
        }
        // セキュリティ基本の設定を取得
        KghAdmSecSettingDto secSetting = getSecSetting();
        int llSecUnit = secSetting.getSecUnit();
        if (ilLic.size() <= 0) {
            Boolean res = useLisence(comMocKinouNameList, ilLic);
            if (res == false) {
                return null;
            }
        }
        /* 給与・人事 or 事業所別機能権限=OFF なら、al_sv_jigyo_id = 0に書き換える */
        if (secSetting.getSvjigyoKinouFlg() == CommonConstants.NUMBER_0
                && secSetting.getSystemSvjigyoFlg() == CommonConstants.NUMBER_0) {
            // メニュー別事業所制限：しない 事業所別機能制限：しない
            llAlSvJigyoId.add(CommonConstants.NUMBER_0);
            llSvjigyo0Flg = CommonConstants.NUMBER_1;
        } else {
            if (CommonConstants.SYS_CD_51201.equals(asSysCd) || CommonConstants.SYS_CD_51301.equals(asSysCd)
                    || CommonConstants.SYS_CD_51501.equals(asSysCd)) {
                // メニュー別事業所制限：しない 事業所別機能制限：しない
                llAlSvJigyoId.add(CommonConstants.NUMBER_0);
                llSvjigyo0Flg = CommonConstants.NUMBER_1;
            } else {
                if (CommonConstants.SYS_CD_KBN_JIN.equals(asSysRyaku)) {

                } else {
                    if (alSvJigyoId.size() == CommonConstants.NUMBER_0) {
                        llAlSvJigyoId = alSvJigyoId;
                        llSvjigyo0Flg = CommonConstants.NUMBER_0;
                    }
                }
            }
        }
        if (secSetting.getSvjigyoKinouFlg() == CommonConstants.NUMBER_0
                && secSetting.getSystemSvjigyoFlg() == CommonConstants.NUMBER_1) {
            // メニュー別事業所制限：する 事業所別機能制限：しない
            llSvjigyo0Flg = CommonConstants.NUMBER_2;
        }
        // セキュリティ基本の設定を取得
        int llAdminFlg = ufPassKengen(shokuId); // 管理者権限の取得
        if (llAdminFlg == CommonConstants.NUMBER_1) {
            alOKid = alSvJigyoId;
            return null;
        }
        if (llSvjigyo0Flg == CommonConstants.NUMBER_1) {
            switch (llSecUnit) {
                case CommonConstants.INT_1:// 個人
                    // 事業所数32767超で強制終了してしまう
                    List<AdmSecKengenShkSvjigyoOutEntity> admSecKengenShkSvjigyoList = new ArrayList<>();
                    if (alSvJigyoId.size() > CommonConstants.NUMBER_0) {
                        AdmSecKengenShkSvjigyoByCriteriaInEntity inEntity = new AdmSecKengenShkSvjigyoByCriteriaInEntity();
                        // システムコード
                        inEntity.setSysCd(asSysCd);
                        // 記載者ID
                        inEntity.setChkShokuId(shokuId);
                        // 機能区分
                        inEntity.setKinouKbn(CommonConstants.NUMBER_1);
                        // 機能ID
                        inEntity.setKinouId(CommonConstants.NUMBER_0);
                        admSecKengenShkSvjigyoList = kghAdmSecKengenShkSvjigyoSelectMapper
                                .findAdmSecKengenShkSvjigyoByCriteria(inEntity);
                    }
                    if (admSecKengenShkSvjigyoList.size() > CommonConstants.NUMBER_0) {
                        for (int i = 0; i < admSecKengenShkSvjigyoList.size(); i++) {
                            // List<Integer> alOKidが空の配列にならないようにする。
                            if (alOKid.size() == CommonConstants.NUMBER_0) {
                                alOKid.add(llSvJigyoId);
                            } else if (alOKid.size() == i) {
                                alOKid.add(llSvJigyoId);
                            } else {
                                alOKid.set(i, llSvJigyoId);
                            }
                        }
                    }
                    break;
                case CommonConstants.INT_2, CommonConstants.INT_3:
                    AdmSecKengenFuncGrpSvJigyoByCriteriaInEntity inEntity = new AdmSecKengenFuncGrpSvJigyoByCriteriaInEntity();
                    // 職員ID
                    inEntity.setChkShokuId(shokuId);
                    // システムコード
                    inEntity.setSysCd(asSysCd);
                    // 機能区分
                    inEntity.setKinouKbn(CommonConstants.INT_1);
                    // 機能ID
                    inEntity.setKinouId(CommonConstants.NUMBER_0);
                    // 関数用グループ事業所権限
                    List<AdmSecKengenFuncGrpSvJigyoOutEntity> outEntityList = new ArrayList<>();
                    if (alSvJigyoId.size() > CommonConstants.NUMBER_0) {
                        outEntityList = kghAdmSecKengenFuncGrpSvJigyoSelectMapper
                                .findAdmSecKengenFuncGrpSvJigyoByCriteria(inEntity);
                    }
                    if (llSecUnit == CommonConstants.NUMBER_2 && outEntityList.size() > 0) {
                        outEntityList = outEntityList.stream()
                                .filter(entity -> CommonConstants.NUMBER_0 == entity.getSecGroupKbn()).toList();
                    } else if (outEntityList.size() > 0) {
                        outEntityList = outEntityList.stream()
                                .filter(entity -> CommonConstants.NUMBER_3 == entity.getSecGroupKbn()).toList();
                    }
                    if (outEntityList.size() > CommonConstants.NUMBER_0) {
                        for (int i = 0; i < outEntityList.size(); i++) {
                            llSvJigyoId = outEntityList.get(i).getSvJigyoId();
                            if (llOldSvJigyoId != llSvJigyoId) {
                                // List<Integer> alOKidが空の配列にならないようにする。
                                if (alOKid.size() == CommonConstants.NUMBER_0) {
                                    alOKid.add(llSvJigyoId);
                                } else if (alOKid.size() == i) {
                                    alOKid.add(llSvJigyoId);
                                } else {
                                    alOKid.set(i, llSvJigyoId);
                                }
                            }
                            llOldSvJigyoId = llSvJigyoId;
                        }
                    }
                    break;
                default:
                    break;
            }
            if (alOKid.size() > CommonConstants.NUMBER_0) {
                return null;
            }
        }
        // 機能マスタの件数読み込み
        if (llSecUnit == CommonConstants.INT_1) {
            sreachDto = getUseKinouJigyoDto(llSecUnit, llSvjigyo0Flg, asSysRyaku, asSysCd,
                    llAlSvJigyoId, ilLic, secSetting.getSystemSvjigyoFlg(),
                    secSetting.getDefaultAuthKinou(),
                    secSetting.getDefaultAuthSvjigyo(), CommonConstants.INTEGER_NULL,
                    CommonConstants.INTEGER_NULL,
                    CommonConstants.INTEGER_NULL);
        } else {
            // 所属グループ(事業所)のIDを求める
            KghAdmSecGroupShkByCriteriaInEntity inEntity = new KghAdmSecGroupShkByCriteriaInEntity();
            List<KghAdmSecGroupShkOutEntity> KghAdmSecGroupShkList = shozokuSecurityGroupSelectMapper.findKghAdmSecGroupShkByCriteria(inEntity);
            List<KghAdmSecGroupShkOutEntity> fiterList = new ArrayList<>();
            if (llSecUnit == CommonConstants.NUMBER_2) {// 共通
                fiterList = KghAdmSecGroupShkList.stream()
                    .filter(entity -> shokuId == entity.getChkShokuId() 
                    && entity.getSecGroupKbn() == CommonConstants.NUMBER_0 
                    && entity.getDelFlg() == CommonConstants.NUMBER_0)
                .collect(Collectors.toList());
            } else if (llSecUnit == CommonConstants.NUMBER_3) { // 組合せ 事業所は3
                fiterList = KghAdmSecGroupShkList.stream()
                    .filter(entity -> shokuId == entity.getChkShokuId() 
                    && entity.getSecGroupKbn() == CommonConstants.NUMBER_3 
                    && entity.getDelFlg() == CommonConstants.NUMBER_0)
                .collect(Collectors.toList());
            }
            if (fiterList.size() > CommonConstants.NUMBER_0) {
                for (KghAdmSecGroupShkOutEntity obj : fiterList) {
                    llJgyGroupId.add(obj.getSecGroupId());
                }
                if (llSecUnit == CommonConstants.NUMBER_2) {// 共通
                    fiterList = KghAdmSecGroupShkList.stream()
                        .filter(entity -> shokuId == entity.getChkShokuId() 
                        && entity.getSecGroupKbn() == CommonConstants.NUMBER_0 
                        && entity.getDelFlg() == CommonConstants.NUMBER_0)
                    .collect(Collectors.toList());
                } else if (llSecUnit == CommonConstants.NUMBER_3) {// 組合せ 事業所は3
                    fiterList = KghAdmSecGroupShkList.stream()
                        .filter(entity -> shokuId == entity.getChkShokuId() 
                        && entity.getSecGroupKbn() == CommonConstants.NUMBER_2 
                        && entity.getDelFlg() == CommonConstants.NUMBER_0)
                    .collect(Collectors.toList());
                }
                if (fiterList.size() > CommonConstants.NUMBER_0) {
                    for (KghAdmSecGroupShkOutEntity obj : fiterList) {
                        llGroupId.add(obj.getSecGroupId());
                    }
                }
            }
            if (llSecUnit == CommonConstants.NUMBER_2) { // 2:機能権限IDの取得
                sreachDto = getUseKinouJigyoDto(llSecUnit, llSvjigyo0Flg, asSysRyaku, asSysCd,
                        llAlSvJigyoId, ilLic, secSetting.getSystemSvjigyoFlg(),
                        secSetting.getDefaultAuthKinou(),
                        secSetting.getDefaultAuthSvjigyo(), CommonConstants.NUMBER_0, llJgyGroupId.size(),
                        llGroupId.size());
            } else if (llSecUnit == CommonConstants.NUMBER_3) { // 2:機能権限IDの取得
                sreachDto = getUseKinouJigyoDto(llSecUnit, llSvjigyo0Flg,
                        asSysRyaku, asSysCd,
                        llAlSvJigyoId, ilLic, secSetting.getSystemSvjigyoFlg(),
                        secSetting.getDefaultAuthKinou(),
                        secSetting.getDefaultAuthSvjigyo(), CommonConstants.NUMBER_0, llJgyGroupId.size(),
                        llGroupId.size());
            }
        }
        /* 事業所IDの取得 */
        Integer llKinouRow = sreachDto.getRowCount();
        if (llKinouRow == CommonConstants.NUMBER_0) {
            return null; // 一つも機能が使用できない場合はfalseを返す
        }
        if (llAdminFlg != CommonConstants.NUMBER_1) {
            if (sreachDto.getSvJigyoIdsWhereChkNotZero() <= 0) {
                return null;
                // 一つも機能が使用できない場合はfalseを返す
            }
        }
        if (alOKid.size() <= 0) {
            return null;
        }
        for (int i = 0; i < sreachDto.getRowCount(); i++) {
            Integer llData = sreachDto.getChkByIndex(i);// 管理者権限があれば9に変える
            if (llAdminFlg == CommonConstants.INT_1) {
                llData = CommonConstants.NUMBER_9;
            }
            if (llData != null && llData != 0) {
                llSvJigyoId = sreachDto.getSvJigyoIdByIndex(i);
                if (alOKid.size() == CommonConstants.NUMBER_0) {
                    alOKid.add(llSvJigyoId);
                } else if (alOKid.size() == i) {
                    alOKid.add(llSvJigyoId);
                } else {
                    alOKid.set(i, llSvJigyoId);
                }
            }
        }
        return alOKid;
    }

    public GetUseKinouJigyoDto getUseKinouJigyoDto(Integer llSecUnit, Integer llSvjigyo0Flg, String asSysCd,
            String asSysRyaku,
            List<Integer> llAlSvJigyoId, List<Integer> ilLic, Integer systemSvjigyoFlg, Integer defaultAuthKinou,
            Integer defaultAuthSvjigyo, Integer num1, Integer num2, Integer num3) {
        GetUseKinouJigyoDto sreachDto = new GetUseKinouJigyoDto();
        switch (llSecUnit) {
            case CommonConstants.INT_1:// 個人
                if (llSvjigyo0Flg == CommonConstants.INT_2) {
                    KghAdmSecKengenFuncGrpUseKinou3KinoByCriteriaInEntity inEntity = new KghAdmSecKengenFuncGrpUseKinou3KinoByCriteriaInEntity();

                    // システムコード
                    inEntity.setAsSysCd(asSysCd);
                    // 事業所リスト
                    inEntity.setSvJigyoIdList(llAlSvJigyoId);
                    // 機能IDリスト
                    inEntity.setKinouIdList(ilLic);
                    // システム略称
                    inEntity.setAsSysCdKbn(asSysRyaku);
                    // システム別事業所制限
                    inEntity.setSystemSvjigyoFlg(systemSvjigyoFlg);
                    // 機能権限初期値
                    inEntity.setDefaultAuthKinou(defaultAuthKinou);
                    // 事業所権限初期値
                    inEntity.setDefaultAuthSvjigyo(defaultAuthSvjigyo);
                    if (num2 != CommonConstants.INTEGER_NULL)
                        // 列の数
                        inEntity.setJigyoSecGroupCount(num2);
                    if (num3 != CommonConstants.INTEGER_NULL)
                        // 列の数
                        inEntity.setSecGroupCount(num3);
                    List<KghAdmSecKengenFuncGrpUseKinou3KinoOutEntity> KghAdmSecKengenFuncGrpUseKinou3KinoList = kinouPermissionFunc2SelectMapper
                            .findKghAdmSecKengenFuncGrpUseKinou3KinoByCriteria(inEntity);
                    sreachDto.setKghAdmSecKengenFuncGrpUseKinou3KinoList(KghAdmSecKengenFuncGrpUseKinou3KinoList);
                } else {
                    KinoKubenKinoIDByCriteriaInEntity inEntity = new KinoKubenKinoIDByCriteriaInEntity();
                    // システムコード
                    inEntity.setAsSysCd(asSysCd);
                    // 事業所リスト
                    inEntity.setJigyoSecGroupId(llAlSvJigyoId);
                    // 機能IDリスト
                    inEntity.setKinouId(ilLic);
                    // システム略称
                    inEntity.setAsSysCdKbn(asSysRyaku);
                    // システム別事業所制限
                    inEntity.setSystemSvjigyoFlg(systemSvjigyoFlg);
                    // 機能権限初期値
                    inEntity.setDefaultAuthKinou(defaultAuthKinou);
                    // 事業所権限初期値
                    inEntity.setDefaultAuthSvjigyo(defaultAuthSvjigyo);
                    List<KinoKubenKinoIDOutEntity> KinoKubenKinoIDList = secKengenFuncGrpUseKinou2SelectMapper
                            .findKinoKubenKinoIDByCriteria(inEntity);
                    sreachDto.setKinoKubenKinoIDList(KinoKubenKinoIDList);
                }
                break;
            case CommonConstants.INT_2, CommonConstants.INT_3:
                if (llSvjigyo0Flg == CommonConstants.INT_2) {
                    if (defaultAuthSvjigyo == CommonConstants.NUMBER_0
                            && defaultAuthKinou == CommonConstants.NUMBER_0) {
                        KghAdmSecKengenFuncGrpUseKinou32AuthFlgByCriteriaInEntity inEntity = new KghAdmSecKengenFuncGrpUseKinou32AuthFlgByCriteriaInEntity();
                        // システムコード
                        inEntity.setSysCd(asSysCd);
                        // 事業所リスト
                        inEntity.setJigyoSecGroupId(llAlSvJigyoId);
                        // 機能IDリスト
                        inEntity.setKinouId(ilLic);
                        // システム略称
                        inEntity.setSysCdKbn(asSysRyaku);
                        // システム別事業所制限
                        inEntity.setSystemSvjigyoFlg(systemSvjigyoFlg);
                        // 事業所権限初期値
                        inEntity.setDefaultAuthSvjigyo(defaultAuthSvjigyo);
                        List<KghAdmSecKengenFuncGrpUseKinou32AuthFlgOutEntity> KghAdmSecKengenFuncGrpUseKinou32AuthFlgList = groupKinouKengenKansuuyoSelectMapper
                                .findKghAdmSecKengenFuncGrpUseKinou32AuthFlgByCriteria(
                                        inEntity);
                        sreachDto.setKghAdmSecKengenFuncGrpUseKinou32AuthFlgList(
                                KghAdmSecKengenFuncGrpUseKinou32AuthFlgList);
                    } else {
                        KghAdmSecKengenFuncGrpUseKinou3KinoByCriteriaInEntity inEntity = new KghAdmSecKengenFuncGrpUseKinou3KinoByCriteriaInEntity();
                        // システムコード
                        inEntity.setAsSysCd(asSysCd);
                        // 事業所リスト
                        inEntity.setSvJigyoIdList(llAlSvJigyoId);
                        // 機能IDリスト
                        inEntity.setKinouIdList(ilLic);
                        // システム略称
                        inEntity.setAsSysCdKbn(asSysRyaku);
                        // システム別事業所制限
                        inEntity.setSystemSvjigyoFlg(systemSvjigyoFlg);
                        // 機能権限初期値
                        inEntity.setDefaultAuthKinou(defaultAuthKinou);
                        // 事業所権限初期値
                        inEntity.setDefaultAuthSvjigyo(defaultAuthSvjigyo);
                        if (num2 != CommonConstants.INTEGER_NULL)
                            // 列の数
                            inEntity.setJigyoSecGroupCount(num2);
                        if (num3 != CommonConstants.INTEGER_NULL)
                            // 列の数
                            inEntity.setSecGroupCount(num3);
                        List<KghAdmSecKengenFuncGrpUseKinou3KinoOutEntity> KghAdmSecKengenFuncGrpUseKinou3KinoList = kinouPermissionFunc2SelectMapper
                                .findKghAdmSecKengenFuncGrpUseKinou3KinoByCriteria(inEntity);
                        sreachDto.setKghAdmSecKengenFuncGrpUseKinou3KinoList(KghAdmSecKengenFuncGrpUseKinou3KinoList);
                    }
                } else {
                    if (defaultAuthSvjigyo == CommonConstants.NUMBER_0
                            && defaultAuthKinou == CommonConstants.NUMBER_0) {
                        KghAdmSecKengenFuncGrpUseKinou22DSByCriteriaInEntity inEntity = new KghAdmSecKengenFuncGrpUseKinou22DSByCriteriaInEntity();
                        // システムコード
                        inEntity.setSysCd(asSysCd);
                        // 事業所リスト
                        inEntity.setJigyoSecGroupId(llAlSvJigyoId);
                        // システム略称
                        inEntity.setSysCdKbn(asSysRyaku);
                        // 機能IDリスト
                        inEntity.setKinouId(ilLic);
                        // システム別事業所制限
                        inEntity.setSystemSvjigyoFlg(systemSvjigyoFlg);
                        // 機能権限初期値
                        inEntity.setDefaultAuthKinou(defaultAuthKinou);
                        // 事業所権限初期値
                        inEntity.setDefaultAuthSvjigyo(defaultAuthSvjigyo);
                        List<KghAdmSecKengenFuncGrpUseKinou22DSOutEntity> KghAdmSecKengenFuncGrpUseKinou22DSList = groupKinouKengenKansuuyo3SelectMapper
                                .findKghAdmSecKengenFuncGrpUseKinou22DSByCriteria(
                                        inEntity);
                        sreachDto.setKghAdmSecKengenFuncGrpUseKinou22DSList(KghAdmSecKengenFuncGrpUseKinou22DSList);
                    } else {
                        KinoKubenKinoIDByCriteriaInEntity inEntity = new KinoKubenKinoIDByCriteriaInEntity();
                        // システムコード
                        inEntity.setAsSysCd(asSysCd);
                        // 事業所リスト
                        inEntity.setJigyoSecGroupId(llAlSvJigyoId);
                        // システム略称
                        inEntity.setAsSysCdKbn(asSysRyaku);
                        // 機能IDリスト
                        inEntity.setKinouId(ilLic);
                        // システム別事業所制限
                        inEntity.setSystemSvjigyoFlg(systemSvjigyoFlg);
                        // 機能権限初期値
                        inEntity.setDefaultAuthKinou(defaultAuthKinou);
                        // 事業所権限初期値
                        inEntity.setDefaultAuthSvjigyo(defaultAuthSvjigyo);
                        List<KinoKubenKinoIDOutEntity> KinoKubenKinoIDList = secKengenFuncGrpUseKinou2SelectMapper
                                .findKinoKubenKinoIDByCriteria(inEntity);
                        sreachDto.setKinoKubenKinoIDList(KinoKubenKinoIDList);
                    }
                }
                break;
            default:
                break;
        }
        return sreachDto;
    }

    /**
     * ライセンスのチェックを行い、製品ID配列と機能ID配列をそれぞれ返す
     * 関数名：uf_use_lisence
     * 
     * @param comMocKinouNameList 機能情報リスト
     * @param alKinouId           機能IDの配列を返す
     * @return boolean true:取得成功 false:取得失敗
     * <AUTHOR>
     */
    public boolean useLisence(List<KghComGetMenuOutEntity> comMocKinouNameList, List<Integer> alKinouId) {
        // refに万が一値が渡ってきてしまった場合の初期化処理
        alKinouId = new ArrayList<>();

        if (CollectionUtils.isEmpty(comMocKinouNameList)) {
            // ライセンスなし
            return false;
        }

        List<KghComGetMenuOutEntity> comMocKinouNameSortList = comMocKinouNameList.stream()
                .sorted(Comparator
                        .comparing(KghComGetMenuOutEntity::getKinouId))
                .collect(Collectors.toList());
        // 機能ID
        Integer llKinouId = CommonConstants.INT_0;
        for (KghComGetMenuOutEntity kghComGetMenuOutEntity : comMocKinouNameSortList) {
            if (kghComGetMenuOutEntity.getKinouId() != llKinouId) {
                alKinouId.add(kghComGetMenuOutEntity.getKinouId());
                llKinouId = kghComGetMenuOutEntity.getKinouId();
            }
        }
        return true;
    }
}
