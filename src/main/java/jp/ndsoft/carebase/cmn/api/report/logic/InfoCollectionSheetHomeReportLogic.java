package jp.ndsoft.carebase.cmn.api.report.logic;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.InfoCollectionReportCommonPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSet;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionAllHomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionMedicationHomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet10HomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet11HomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet12Dto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet12HomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet13HomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet14HomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet1HomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet2HomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet3Dto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet3HomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet4Dto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet4HomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet5HomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet6HomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet7HomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet8HomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheet9HomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheetDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheetMedOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionSheetOthersHomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.model.InfoCollectionSheetMedicationReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.InfoCollectionSheetReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoShoDrugByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoShoDrugOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPrnPreSrw1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.MygAssH3101pByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.MygAssH3101pOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvJigyoNameByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvJigyoNameOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TucMygAss1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TucMygAss1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TucMygAss3ByCritieaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TucMygAss3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucMygAss1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucMygAss3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.MygAssH3101pSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ShoMstDrug2SelectMapper;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * <AUTHOR>
 * @description 情報収集シート(居宅) 帳票出力 ロジッククラス
 * @since 2025.07.07
 */
@Component
public class InfoCollectionSheetHomeReportLogic {
    // 情報収集シート情報
    @Autowired
    private MygAssH3101pSelectMapper mygAssH3101pSelectMapper;

    // アセスメントのための情報収集シートヘッダ情報
    @Autowired
    private CpnTucMygAss1SelectMapper cpnTucMygAss1SelectMapper;

    // 職員基本情報
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    // 利用者基本情報
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;

    // サービス事業者マスタ情報取得
    @Autowired
    private ComMscSvjigyoSelectMapper comMscSvjigyoSelectMapper;

    // 服薬状況シート情報
    @Autowired
    private CpnTucMygAss3SelectMapper cpnTucMygAss3SelectMapper;

    // 薬剤リスト情報
    @Autowired
    private ShoMstDrug2SelectMapper shoMstDrug2SelectMapper;

    /**
     * Nds3GkFunc01Logicロジッククラス
     */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /**
     * U06061_情報収集シート（1）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet1HomeReportServiceInDto getU06061Sheet1ReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.valueOf(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.valueOf(inDto.getLevel1Id());

        // infoInDtoを設定する。
        InfoCollectionSheet1HomeReportServiceInDto infoInDto = new InfoCollectionSheet1HomeReportServiceInDto();
        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheet1(ass1Id, kaiteiKbn, level1Id, mstKbn, printOption.getEmptyFlg());

        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ

        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);
        // 注釈表示フラグ
        infoInDto.setChushakuFlg(
                CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getChushakuFlg()) ? Boolean.TRUE : Boolean.FALSE);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * U06061_情報収集シート（1）帳票詳細情報リスト取得
     *
     * @param ass1Id    ヘッダID
     * @param kaiteiKbn 改訂区分
     * @param level1Id  第1階層ID
     * @param mstKbn    マスタ区分
     * @param emptyFlg  記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    private InfoCollectionSheet1HomeReportServiceInDto getReportParamsFromInfoSheet1(Integer ass1Id, Integer kaiteiKbn,
            Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {
        // 帳票用データ詳細
        InfoCollectionSheet1HomeReportServiceInDto infoInDto = new InfoCollectionSheet1HomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteiKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheetDto> list = new ArrayList<InfoCollectionSheetDto>();
        List<InfoCollectionSheetDto> list2 = new ArrayList<InfoCollectionSheetDto>();
        if (mygAssH3101pOutEntityList != null && mygAssH3101pOutEntityList.size() > 0) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // Ass3の第3階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                        sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                        list.add(sheetDto);
                    }
                    InfoCollectionSheetDto sheetDto2 = new InfoCollectionSheetDto();
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto2.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    // 第3階層書式1が"1"の場合
                    if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                        sheetDto2
                                .setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                        sheetDto2.setLevel3KoumokuNoFont(
                                CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    }
                    list.add(sheetDto2);
                } else {
                    InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（6）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet6HomeReportServiceInDto getU06061Sheet6ReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.valueOf(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.valueOf(inDto.getLevel1Id());

        // infoInDtoを設定する。
        InfoCollectionSheet6HomeReportServiceInDto infoInDto = new InfoCollectionSheet6HomeReportServiceInDto();
        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheet6(ass1Id, kaiteiKbn, level1Id, mstKbn, printOption.getEmptyFlg());

        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ

        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * U06061_情報収集シート（6）帳票詳細情報リスト取得
     *
     * @param ass1Id    ヘッダID
     * @param kaiteiKbn 改訂区分
     * @param level1Id  第1階層ID
     * @param mstKbn    マスタ区分
     * @param emptyFlg  記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    private InfoCollectionSheet6HomeReportServiceInDto getReportParamsFromInfoSheet6(Integer ass1Id, Integer kaiteiKbn,
            Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {
        // 帳票用データ詳細
        InfoCollectionSheet6HomeReportServiceInDto infoInDto = new InfoCollectionSheet6HomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteiKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheetDto> list = new ArrayList<InfoCollectionSheetDto>();
        List<InfoCollectionSheetDto> list2 = new ArrayList<InfoCollectionSheetDto>();
        if (mygAssH3101pOutEntityList != null && mygAssH3101pOutEntityList.size() > 0) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // Ass3の第3階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                        sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                        list.add(sheetDto);
                    }
                    InfoCollectionSheetDto sheetDto2 = new InfoCollectionSheetDto();
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto2.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    // 第3階層書式1が"1"の場合
                    if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                        sheetDto2
                                .setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                        sheetDto2.setLevel3KoumokuNoFont(
                                CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    }
                    // 書式2フラグ
                    sheetDto2.setShosiki2Flg(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getShosiki2Flg()));
                    list.add(sheetDto2);
                } else {
                    InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（7）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet7HomeReportServiceInDto getU06061Sheet7ReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.valueOf(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.valueOf(inDto.getLevel1Id());

        // infoInDtoを設定する。
        InfoCollectionSheet7HomeReportServiceInDto infoInDto = new InfoCollectionSheet7HomeReportServiceInDto();
        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheet7(ass1Id, kaiteiKbn, level1Id, mstKbn, printOption.getEmptyFlg());

        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ

        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * U06061_情報収集シート（7）帳票詳細情報リスト取得
     *
     * @param ass1Id    ヘッダID
     * @param kaiteiKbn 改訂区分
     * @param level1Id  第1階層ID
     * @param mstKbn    マスタ区分
     * @param emptyFlg  記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    private InfoCollectionSheet7HomeReportServiceInDto getReportParamsFromInfoSheet7(Integer ass1Id, Integer kaiteiKbn,
            Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {
        // 帳票用データ詳細
        InfoCollectionSheet7HomeReportServiceInDto infoInDto = new InfoCollectionSheet7HomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteiKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheetDto> list = new ArrayList<InfoCollectionSheetDto>();
        List<InfoCollectionSheetDto> list2 = new ArrayList<InfoCollectionSheetDto>();
        if (mygAssH3101pOutEntityList != null && mygAssH3101pOutEntityList.size() > 0) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // Ass3の第3階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                        sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                        list.add(sheetDto);
                    }
                    InfoCollectionSheetDto sheetDto2 = new InfoCollectionSheetDto();
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto2.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    // 第3階層書式1が"1"の場合
                    if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                        sheetDto2
                                .setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                        sheetDto2.setLevel3KoumokuNoFont(
                                CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    }
                    // 書式2フラグ
                    sheetDto2.setShosiki2Flg(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getShosiki2Flg()));
                    list.add(sheetDto2);
                } else {
                    InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（8）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet8HomeReportServiceInDto getU06061Sheet8ReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.valueOf(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.valueOf(inDto.getLevel1Id());

        // infoInDtoを設定する。
        InfoCollectionSheet8HomeReportServiceInDto infoInDto = new InfoCollectionSheet8HomeReportServiceInDto();
        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheet8(ass1Id, kaiteiKbn, level1Id, mstKbn, printOption.getEmptyFlg());

        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ

        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * U06061_情報収集シート（8）帳票詳細情報リスト取得
     *
     * @param ass1Id    ヘッダID
     * @param kaiteiKbn 改訂区分
     * @param level1Id  第1階層ID
     * @param mstKbn    マスタ区分
     * @param emptyFlg  記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    private InfoCollectionSheet8HomeReportServiceInDto getReportParamsFromInfoSheet8(Integer ass1Id, Integer kaiteiKbn,
            Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {
        // 帳票用データ詳細
        InfoCollectionSheet8HomeReportServiceInDto infoInDto = new InfoCollectionSheet8HomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteiKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheetDto> list = new ArrayList<InfoCollectionSheetDto>();
        List<InfoCollectionSheetDto> list2 = new ArrayList<InfoCollectionSheetDto>();
        if (mygAssH3101pOutEntityList != null && mygAssH3101pOutEntityList.size() > 0) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // Ass3の第3階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                        sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                        list.add(sheetDto);
                    }
                    InfoCollectionSheetDto sheetDto2 = new InfoCollectionSheetDto();
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto2.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    // 第3階層書式1が"1"の場合
                    if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                        sheetDto2
                                .setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                        sheetDto2.setLevel3KoumokuNoFont(
                                CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    }
                    // 書式2フラグ
                    sheetDto2.setShosiki2Flg(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getShosiki2Flg()));
                    list.add(sheetDto2);
                } else {
                    InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（9）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet9HomeReportServiceInDto getU06061Sheet9ReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.valueOf(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.valueOf(inDto.getLevel1Id());

        // infoInDtoを設定する。
        InfoCollectionSheet9HomeReportServiceInDto infoInDto = new InfoCollectionSheet9HomeReportServiceInDto();
        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheet9(ass1Id, kaiteiKbn, level1Id, mstKbn, printOption.getEmptyFlg());

        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ

        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * U06061_情報収集シート（9）帳票詳細情報リスト取得
     *
     * @param ass1Id    ヘッダID
     * @param kaiteiKbn 改訂区分
     * @param level1Id  第1階層ID
     * @param mstKbn    マスタ区分
     * @param emptyFlg  記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    private InfoCollectionSheet9HomeReportServiceInDto getReportParamsFromInfoSheet9(Integer ass1Id, Integer kaiteiKbn,
            Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {
        // 帳票用データ詳細
        InfoCollectionSheet9HomeReportServiceInDto infoInDto = new InfoCollectionSheet9HomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteiKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheetDto> list = new ArrayList<InfoCollectionSheetDto>();
        List<InfoCollectionSheetDto> list2 = new ArrayList<InfoCollectionSheetDto>();
        if (mygAssH3101pOutEntityList != null && mygAssH3101pOutEntityList.size() > 0) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // Ass3の第3階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                        sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                        list.add(sheetDto);
                    }
                    InfoCollectionSheetDto sheetDto2 = new InfoCollectionSheetDto();
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto2.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    // 第3階層書式1が"1"の場合
                    if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                        sheetDto2
                                .setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                        sheetDto2.setLevel3KoumokuNoFont(
                                CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    }
                    // 書式2フラグ
                    sheetDto2.setShosiki2Flg(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getShosiki2Flg()));
                    list.add(sheetDto2);
                } else {
                    InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（10）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet10HomeReportServiceInDto getU06061Sheet10ReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.valueOf(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.valueOf(inDto.getLevel1Id());

        // infoInDtoを設定する。
        InfoCollectionSheet10HomeReportServiceInDto infoInDto = new InfoCollectionSheet10HomeReportServiceInDto();
        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheet10(ass1Id, kaiteiKbn, level1Id, mstKbn, printOption.getEmptyFlg());

        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ

        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * U06061_情報収集シート（10）帳票詳細情報リスト取得
     *
     * @param ass1Id    ヘッダID
     * @param kaiteiKbn 改訂区分
     * @param level1Id  第1階層ID
     * @param mstKbn    マスタ区分
     * @param emptyFlg  記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    private InfoCollectionSheet10HomeReportServiceInDto getReportParamsFromInfoSheet10(Integer ass1Id,
            Integer kaiteiKbn, Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {
        // 帳票用データ詳細
        InfoCollectionSheet10HomeReportServiceInDto infoInDto = new InfoCollectionSheet10HomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteiKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheetDto> list = new ArrayList<InfoCollectionSheetDto>();
        List<InfoCollectionSheetDto> list2 = new ArrayList<InfoCollectionSheetDto>();
        if (mygAssH3101pOutEntityList != null && mygAssH3101pOutEntityList.size() > 0) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // Ass3の第3階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                        sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                        list.add(sheetDto);
                    }
                    InfoCollectionSheetDto sheetDto2 = new InfoCollectionSheetDto();
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto2.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    // 第3階層書式1が"1"の場合
                    if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                        sheetDto2
                                .setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                        sheetDto2.setLevel3KoumokuNoFont(
                                CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    }
                    // 書式2フラグ
                    sheetDto2.setShosiki2Flg(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getShosiki2Flg()));
                    list.add(sheetDto2);
                } else {
                    InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（11）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet11HomeReportServiceInDto getU06061Sheet11ReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 帳票用データ詳細
        InfoCollectionSheet11HomeReportServiceInDto infoInDto = new InfoCollectionSheet11HomeReportServiceInDto();

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.valueOf(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.valueOf(inDto.getLevel1Id());

        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheet11(ass1Id, kaiteiKbn, level1Id, mstKbn, printOption.getEmptyFlg());

        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ
        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * U06061_情報収集シート（11）帳票詳細情報リスト取得
     *
     * @param ass1Id    ヘッダID
     * @param kaiteiKbn 改訂区分
     * @param level1Id  第1階層ID
     * @param mstKbn    マスタ区分
     * @param emptyFlg  記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet11HomeReportServiceInDto getReportParamsFromInfoSheet11(Integer ass1Id, Integer kaiteiKbn,
            Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {
        // 帳票用データ詳細
        InfoCollectionSheet11HomeReportServiceInDto infoInDto = new InfoCollectionSheet11HomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteiKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheetDto> list = new ArrayList<InfoCollectionSheetDto>();
        List<InfoCollectionSheetDto> list2 = new ArrayList<InfoCollectionSheetDto>();
        if (mygAssH3101pOutEntityList != null && mygAssH3101pOutEntityList.size() > 0) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // Ass3の第3階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                        sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                        list.add(sheetDto);
                    }
                    InfoCollectionSheetDto sheetDto2 = new InfoCollectionSheetDto();
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto2.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    // 第3階層書式1が"1"の場合
                    if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                        sheetDto2
                                .setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                        sheetDto2.setLevel3KoumokuNoFont(
                                CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    }
                    // 書式2フラグ
                    sheetDto2.setShosiki2Flg(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getShosiki2Flg()));
                    list.add(sheetDto2);
                } else {
                    InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（14）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet14HomeReportServiceInDto getU06061Sheet14ReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 帳票用データ詳細
        InfoCollectionSheet14HomeReportServiceInDto infoInDto = new InfoCollectionSheet14HomeReportServiceInDto();

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.valueOf(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.valueOf(inDto.getLevel1Id());

        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheet14(ass1Id, kaiteiKbn, level1Id, mstKbn, printOption.getEmptyFlg());
        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ

        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * U06061_情報収集シート（14）帳票詳細情報リスト取得
     *
     * @param ass1Id    ヘッダID
     * @param kaiteiKbn 改訂区分
     * @param level1Id  第1階層ID
     * @param mstKbn    マスタ区分
     * @param emptyFlg  記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet14HomeReportServiceInDto getReportParamsFromInfoSheet14(Integer ass1Id, Integer kaiteiKbn,
            Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {
        // 帳票用データ詳細
        InfoCollectionSheet14HomeReportServiceInDto infoInDto = new InfoCollectionSheet14HomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteiKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheetDto> list = new ArrayList<InfoCollectionSheetDto>();
        List<InfoCollectionSheetDto> list2 = new ArrayList<InfoCollectionSheetDto>();
        if (mygAssH3101pOutEntityList != null && mygAssH3101pOutEntityList.size() > 0) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // Ass3の第3階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                        sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                        list.add(sheetDto);
                    }
                    InfoCollectionSheetDto sheetDto2 = new InfoCollectionSheetDto();
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto2.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    // 第3階層書式1が"1"の場合
                    if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                        sheetDto2
                                .setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                        sheetDto2.setLevel3KoumokuNoFont(
                                CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    }
                    // 書式2フラグ
                    sheetDto2.setShosiki2Flg(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getShosiki2Flg()));
                    list.add(sheetDto2);
                } else {
                    InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（その他）の帳票パラメータ取得
     *
     * @param inDto
     * @return
     * @throws Exception
     */
    public InfoCollectionSheetOthersHomeReportServiceInDto getU06061SheetOthersReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 帳票用データ詳細
        InfoCollectionSheetOthersHomeReportServiceInDto infoInDto = new InfoCollectionSheetOthersHomeReportServiceInDto();

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.parseInt(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.parseInt(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.parseInt(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.parseInt(inDto.getLevel1Id());

        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheetOthers(ass1Id, kaiteiKbn, level1Id, mstKbn,
                printOption.getEmptyFlg());
        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ

        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（その他）帳票詳細情報リスト取得
     *
     * @param ass1Id   ヘッダID
     * @param kaiteKbn 改訂区分
     * @param level1Id 第1階層ID
     * @param mstKbn   マスタ区分
     * @param emptyFlg 記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    public InfoCollectionSheetOthersHomeReportServiceInDto getReportParamsFromInfoSheetOthers(Integer ass1Id,
            Integer kaiteKbn, Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {
        // 帳票用データ詳細
        InfoCollectionSheetOthersHomeReportServiceInDto infoInDto = new InfoCollectionSheetOthersHomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheetDto> list = new ArrayList<InfoCollectionSheetDto>();
        List<InfoCollectionSheetDto> list2 = new ArrayList<InfoCollectionSheetDto>();
        if (mygAssH3101pOutEntityList != null && mygAssH3101pOutEntityList.size() > 0) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // Ass3の第3階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                        sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                        list.add(sheetDto);
                    }
                    InfoCollectionSheetDto sheetDto2 = new InfoCollectionSheetDto();
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto2.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    // 第3階層書式1が"1"の場合
                    if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                        sheetDto2
                                .setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                        sheetDto2.setLevel3KoumokuNoFont(
                                CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    }
                    list.add(sheetDto2);
                } else {
                    InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（2）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet2HomeReportServiceInDto getU06061Sheet2ReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 帳票用データ詳細
        InfoCollectionSheet2HomeReportServiceInDto infoInDto = new InfoCollectionSheet2HomeReportServiceInDto();

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.valueOf(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.valueOf(inDto.getLevel1Id());

        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheet2(ass1Id, kaiteiKbn, level1Id, mstKbn, printOption.getEmptyFlg());

        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ

        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（2）帳票詳細情報リスト取得
     *
     * @param ass1Id    ヘッダID
     * @param kaiteiKbn 改訂区分
     * @param level1Id  第1階層ID
     * @param mstKbn    マスタ区分
     * @param emptyFlg  記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    private InfoCollectionSheet2HomeReportServiceInDto getReportParamsFromInfoSheet2(Integer ass1Id, Integer kaiteiKbn,
            Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {
        // 帳票用データ詳細
        InfoCollectionSheet2HomeReportServiceInDto infoInDto = new InfoCollectionSheet2HomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteiKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheetDto> list = new ArrayList<InfoCollectionSheetDto>();
        List<InfoCollectionSheetDto> list2 = new ArrayList<InfoCollectionSheetDto>();
        if (mygAssH3101pOutEntityList != null && mygAssH3101pOutEntityList.size() > 0) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // Ass3の第3階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                        sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                        list.add(sheetDto);
                    }
                    InfoCollectionSheetDto sheetDto2 = new InfoCollectionSheetDto();
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto2.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    // 第3階層書式1が"1"の場合
                    if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                        sheetDto2
                                .setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                        sheetDto2.setLevel3KoumokuNoFont(
                                CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    }
                    list.add(sheetDto2);
                } else {
                    InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（3）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet3HomeReportServiceInDto getU06061Sheet3ReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {

        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 帳票用データ詳細
        InfoCollectionSheet3HomeReportServiceInDto infoInDto = new InfoCollectionSheet3HomeReportServiceInDto();

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.valueOf(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.valueOf(inDto.getLevel1Id());

        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheet3(ass1Id, kaiteiKbn, level1Id, mstKbn, printOption.getEmptyFlg());
        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ

        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * U06061_情報収集シート（4）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet4HomeReportServiceInDto getU06061Sheet4ReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {

        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 帳票用データ詳細
        InfoCollectionSheet4HomeReportServiceInDto infoInDto = new InfoCollectionSheet4HomeReportServiceInDto();

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.valueOf(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.valueOf(inDto.getLevel1Id());

        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheet4(ass1Id, kaiteiKbn, level1Id, mstKbn, printOption.getEmptyFlg());
        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ

        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * U06061_情報収集シート（5）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet5HomeReportServiceInDto getU06061Sheet5ReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {

        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 帳票用データ詳細
        InfoCollectionSheet5HomeReportServiceInDto infoInDto = new InfoCollectionSheet5HomeReportServiceInDto();

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.valueOf(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.valueOf(inDto.getLevel1Id());

        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheet5(ass1Id, kaiteiKbn, level1Id, mstKbn, printOption.getEmptyFlg());
        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ

        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * U06061_情報収集シート（4）帳票詳細情報リスト取得
     *
     * @param ass1Id    ヘッダID
     * @param kaiteiKbn 改訂区分
     * @param level1Id  第1階層ID
     * @param mstKbn    マスタ区分
     * @param emptyFlg  記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    private InfoCollectionSheet4HomeReportServiceInDto getReportParamsFromInfoSheet4(Integer ass1Id, Integer kaiteiKbn,
            Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {
        // 帳票用データ詳細
        InfoCollectionSheet4HomeReportServiceInDto infoInDto = new InfoCollectionSheet4HomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteiKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheet4Dto> list = new ArrayList<InfoCollectionSheet4Dto>();
        List<InfoCollectionSheet4Dto> list2 = new ArrayList<InfoCollectionSheet4Dto>();
        if (mygAssH3101pOutEntityList != null && mygAssH3101pOutEntityList.size() > 0) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // Ass3の第3階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        InfoCollectionSheet4Dto sheetDto = new InfoCollectionSheet4Dto();
                        sheetDto.setLevel2KnjLabel(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                        sheetDto.setLevel3KoumokuNo("");
                        sheetDto.setLevel3KoumokuNoFont("");

                        list.add(sheetDto);
                    }
                    InfoCollectionSheet4Dto sheetDto2 = new InfoCollectionSheet4Dto();
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto2.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    // 第3階層書式1が"1"の場合
                    if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                        sheetDto2
                                .setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                        sheetDto2.setLevel3KoumokuNoFont(
                                CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    }
                    if ((mygAssH3101pOutEntity.getShosiki2Flg() == CommonConstants.NUMBER_7)
                            && (mygAssH3101pOutEntity.getAss3Level3Id() != CommonConstants.NUMBER_5)
                            && (mygAssH3101pOutEntity.getKoumokuNo() == ReportConstants.NUMBER_60)) {
                        sheetDto2.setLevel3KoumokuNo("");
                        sheetDto2.setLevel3KoumokuNoFont("");
                    }
                    list.add(sheetDto2);
                } else {
                    InfoCollectionSheet4Dto sheetDto = new InfoCollectionSheet4Dto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（5）帳票詳細情報リスト取得
     *
     * @param ass1Id    ヘッダID
     * @param kaiteiKbn 改訂区分
     * @param level1Id  第1階層ID
     * @param mstKbn    マスタ区分
     * @param emptyFlg  記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    private InfoCollectionSheet5HomeReportServiceInDto getReportParamsFromInfoSheet5(Integer ass1Id, Integer kaiteiKbn,
            Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {
        // 帳票用データ詳細
        InfoCollectionSheet5HomeReportServiceInDto infoInDto = new InfoCollectionSheet5HomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteiKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheetDto> list = new ArrayList<InfoCollectionSheetDto>();
        List<InfoCollectionSheetDto> list2 = new ArrayList<InfoCollectionSheetDto>();
        if (mygAssH3101pOutEntityList != null && mygAssH3101pOutEntityList.size() > 0) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // Ass3の第3階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                        sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                        list.add(sheetDto);
                    }
                    InfoCollectionSheetDto sheetDto2 = new InfoCollectionSheetDto();
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto2.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    // 第3階層書式1が"1"の場合
                    if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                        sheetDto2
                                .setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                        sheetDto2.setLevel3KoumokuNoFont(
                                CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    }
                    // 書式2フラグ
                    sheetDto2.setShosiki2Flg(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getShosiki2Flg()));
                    list.add(sheetDto2);
                } else {
                    InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（3）帳票詳細情報リスト取得
     *
     * @param ass1Id    ヘッダID
     * @param kaiteiKbn 改訂区分
     * @param level1Id  第1階層ID
     * @param mstKbn    マスタ区分
     * @param emptyFlg  記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    private InfoCollectionSheet3HomeReportServiceInDto getReportParamsFromInfoSheet3(Integer ass1Id, Integer kaiteiKbn,
            Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {

        // 帳票用データ詳細
        InfoCollectionSheet3HomeReportServiceInDto infoInDto = new InfoCollectionSheet3HomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteiKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheet3Dto> list = new ArrayList<InfoCollectionSheet3Dto>();
        List<InfoCollectionSheet3Dto> list2 = new ArrayList<InfoCollectionSheet3Dto>();
        if (mygAssH3101pOutEntityList != null && !mygAssH3101pOutEntityList.isEmpty()) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // Ass3の第3階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        InfoCollectionSheet3Dto sheetDto = new InfoCollectionSheet3Dto();
                        sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                        list.add(sheetDto);

                        // 宮城アセスメントマスタ第３階層の第１階層IDが"3"の場合
                        if (mygAssH3101pOutEntity.getAss3Level1Id() == CommonConstants.NUMBER_3) {
                            InfoCollectionSheet3Dto sheetDto2 = new InfoCollectionSheet3Dto();
                            sheetDto2.setGenjyouTitle(ReportConstants.GENJYOU_TITLE);
                            sheetDto2.setKonnanTitle(ReportConstants.KONNAN_TITLE);
                            list.add(sheetDto2);
                        }
                    }

                    InfoCollectionSheet3Dto sheetDto3 = new InfoCollectionSheet3Dto();
                    // 第3階層書式1が"1"の場合
                    if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                        sheetDto3
                                .setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto3.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                        // リクエストパラメータ.記入用シート印刷フラグが"0"の場合
                        if (CommonConstants.STR_0.equals(emptyFlg)) {
                            sheetDto3.setGenjyou(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 現状
                            sheetDto3.setKonnan(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getKonnanLevel()));
                            // 困難度
                            sheetDto3.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        }
                        // レイアウト調整用
                        sheetDto3.setGenjyou(this.strNullToFullSpace(sheetDto3.getGenjyou()));
                        sheetDto3.setKonnan(this.strNullToFullSpace(sheetDto3.getKonnan()));

                    } else {
                        sheetDto3.setLevel3KoumokuNoFont(
                                CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto3.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                        // リクエストパラメータ.記入用シート印刷フラグが"0"の場合
                        if (CommonConstants.STR_0.equals(emptyFlg)) {
                            sheetDto3.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                            sheetDto3.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        }
                        // レイアウト調整用
                        sheetDto3.setMemo1Knj(this.strNullToFullSpace(sheetDto3.getMemo1Knj()));
                    }
                    list.add(sheetDto3);
                } else {
                    InfoCollectionSheet3Dto sheetDto = new InfoCollectionSheet3Dto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // リクエストパラメータ.記入用シート印刷フラグが"0"の場合
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（13）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet13HomeReportServiceInDto getU06061Sheet13ReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 帳票用データ詳細
        InfoCollectionSheet13HomeReportServiceInDto infoInDto = new InfoCollectionSheet13HomeReportServiceInDto();

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.valueOf(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.valueOf(inDto.getLevel1Id());

        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheet13(ass1Id, kaiteiKbn, level1Id, mstKbn, printOption.getEmptyFlg());

        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ
        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * U06061_情報収集シート（13）帳票詳細情報リスト取得
     *
     * @param ass1Id    ヘッダID
     * @param kaiteiKbn 改訂区分
     * @param level1Id  第1階層ID
     * @param mstKbn    マスタ区分
     * @param emptyFlg  記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet13HomeReportServiceInDto getReportParamsFromInfoSheet13(Integer ass1Id, Integer kaiteiKbn,
            Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {
        // 帳票用データ詳細
        InfoCollectionSheet13HomeReportServiceInDto infoInDto = new InfoCollectionSheet13HomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteiKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheetDto> list = new ArrayList<InfoCollectionSheetDto>();
        List<InfoCollectionSheetDto> list2 = new ArrayList<InfoCollectionSheetDto>();
        if (mygAssH3101pOutEntityList != null && mygAssH3101pOutEntityList.size() > 0) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // Ass3の第3階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                        sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                        list.add(sheetDto);
                    }
                    InfoCollectionSheetDto sheetDto2 = new InfoCollectionSheetDto();
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto2.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    // 第3階層書式1が"1"の場合
                    if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                        sheetDto2
                                .setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                        sheetDto2.setLevel3KoumokuNoFont(
                                CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                        sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    }
                    // 書式2フラグ
                    sheetDto2.setShosiki2Flg(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getShosiki2Flg()));
                    list.add(sheetDto2);
                } else {
                    InfoCollectionSheetDto sheetDto = new InfoCollectionSheetDto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(CommonDtoUtil.strNullToEmpty(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * 指定日を取得する
     *
     * @param shiTeiKubun 指定日印刷区分
     * @param shiTeiDate  指定日(西暦日付)
     * @param systemDate  システム日付
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private String getShiTeiDate(String shiTeiKubun, String shiTeiDate, String systemDate) {
        String blankDate = CommonConstants.BLANK_STRING;
        switch (shiTeiKubun) {
            case ReportConstants.PRINTDATE_KBN_SHINAI:
                // 指定日印刷区分 = 1 の場合、指定日=""
                blankDate = CommonConstants.BLANK_STRING;
                break;
            case ReportConstants.PRINTDATE_KBN_PRINT:
                // 指定日印刷区分 = 2 の場合
                // 西暦日付を和暦日付に変換する
                blankDate = ReportUtil.getLocalDateToJapaneseDateTimeFormat(shiTeiDate);
                break;
            case ReportConstants.PRINTDATE_KBN_BLANKDATE:
                // 指定日印刷区分 = 3 の場合
                // 指定日の空欄表示処理
                DateTimeFormatter inputFormatter = DateTimeFormatter
                        .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
                DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
                LocalDateTime dateTime = LocalDateTime.parse(systemDate, inputFormatter);
                blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
                break;
            default:
                blankDate = CommonConstants.BLANK_STRING;
                break;
        }

        return blankDate;
    }

    /**
     * U06061_情報収集シート（12）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet12HomeReportServiceInDto getU06061Sheet12ReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 帳票用データ詳細
        InfoCollectionSheet12HomeReportServiceInDto infoInDto = new InfoCollectionSheet12HomeReportServiceInDto();

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.マスタ区分
        Integer mstKbn = Integer.valueOf(inDto.getMstKbn());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());
        // 【リクエストパラメータ】.第1階層ID
        Integer level1Id = Integer.valueOf(inDto.getLevel1Id());

        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoSheet12(ass1Id, kaiteiKbn, level1Id, mstKbn, printOption.getEmptyFlg());

        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && tucMygAss1OutEntityList.size() > 0) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && shokuinOutEntityList.size() > 0) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.get(0);
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && kghCpnRaiMonKentPrnPreSrw1OutEntityList.size() > 0) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && svJigyoNameOutEntityList.size() > 0) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.get(0).getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ

        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * 帳票詳細情報リスト取得
     *
     * @param ass1Id    ヘッダID
     * @param kaiteiKbn 改訂区分
     * @param level1Id  第1階層ID
     * @param mstKbn    マスタ区分
     * @param emptyFlg  記入用シート印刷フラグ
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    public InfoCollectionSheet12HomeReportServiceInDto getReportParamsFromInfoSheet12(Integer ass1Id, Integer kaiteiKbn,
            Integer level1Id, Integer mstKbn, String emptyFlg) throws Exception {
        // 帳票用データ詳細
        InfoCollectionSheet12HomeReportServiceInDto infoInDto = new InfoCollectionSheet12HomeReportServiceInDto();

        // 2. 詳細情報を検索
        MygAssH3101pByCriteriaInEntity mygAssH3101pByCriteriaInEntity = new MygAssH3101pByCriteriaInEntity();
        mygAssH3101pByCriteriaInEntity.setAss1Id(ass1Id);
        mygAssH3101pByCriteriaInEntity.setKaiteiKbn(kaiteiKbn);
        mygAssH3101pByCriteriaInEntity.setLevel1Id(level1Id);
        mygAssH3101pByCriteriaInEntity.setMstKbn(mstKbn);
        List<MygAssH3101pOutEntity> mygAssH3101pOutEntityList = mygAssH3101pSelectMapper
                .findMygAssH3101pByCriteria(mygAssH3101pByCriteriaInEntity);

        // 7. 詳細情報リストと詳細情報2を設定
        List<InfoCollectionSheet12Dto> list = new ArrayList<InfoCollectionSheet12Dto>();
        List<InfoCollectionSheet12Dto> list2 = new ArrayList<InfoCollectionSheet12Dto>();
        if (mygAssH3101pOutEntityList != null && mygAssH3101pOutEntityList.size() > 0) {
            // 一級メニュー
            infoInDto.setLevel1Knj(mygAssH3101pOutEntityList.get(0).getLevel1Knj());
            // 7.1. 上記2で取得した情報収集シート（12）の情報リストをループする
            for (int i = 0; i < mygAssH3101pOutEntityList.size(); i++) {
                MygAssH3101pOutEntity mygAssH3101pOutEntity = mygAssH3101pOutEntityList.get(i);
                // 7.1.1. 上記2で取得した情報収集シート（12）の情報リスト.意向フラグが"0"の場合
                if (mygAssH3101pOutEntity.getIkouFlg() != null
                        && mygAssH3101pOutEntity.getIkouFlg() == CommonConstants.NUMBER_ZERO) {
                    // 7.1.1.1. 上記2で取得した情報収集シート（12）の情報リスト.宮城アセスメントマスタ第３階層の第３階層IDが"1"の場合
                    if (mygAssH3101pOutEntity.getAss3Level3Id() == CommonConstants.NUMBER_1) {
                        // 上記2で取得した情報収集シート（12）の情報リスト.宮城アセスメントマスタ第３階層の第２階層IDが”1”の場合
                        if (mygAssH3101pOutEntity.getAss3Level2Id() == CommonConstants.NUMBER_1) {
                            InfoCollectionSheet12Dto sheetDto = new InfoCollectionSheet12Dto();
                            sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                            sheetDto.setMainKaigo(ReportConstants.MAIN_KAIGO_TITLE);// "主介護者"
                            sheetDto.setSubKaigo(ReportConstants.SUB_KAIGO_TITLE);// "副介護者"
                            list.add(sheetDto);

                            InfoCollectionSheet12Dto sheetDto2 = new InfoCollectionSheet12Dto();
                            // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                            if (CommonConstants.STR_0.equals(emptyFlg)) {
                                sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                                sheetDto2.setMainKaigo(strNullToFullSpace(mygAssH3101pOutEntity.getMemo1Knj()));// 主介護者
                                sheetDto2.setSubKaigo(strNullToFullSpace(mygAssH3101pOutEntity.getKonnanLevel()));// 副介護者
                                sheetDto2.setShosiki2Flg(
                                        CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getShosiki2Flg()));// 書式2フラグ
                            }
                            // 第3階層書式1が"1"の場合
                            if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                                sheetDto2.setLevel3KoumokuNo(
                                        CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                                sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                            } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                                sheetDto2.setLevel3KoumokuNoFont(
                                        CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                                sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                            }
                            // 書式2フラグ
                            sheetDto2.setShosiki2Flg(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getShosiki2Flg()));
                            list.add(sheetDto2);
                        } else {
                            // ②、上記以外の場合
                            InfoCollectionSheet12Dto sheetDto = new InfoCollectionSheet12Dto();
                            sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                            list.add(sheetDto);

                            InfoCollectionSheet12Dto sheetDto2 = new InfoCollectionSheet12Dto();
                            // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                            if (CommonConstants.STR_0.equals(emptyFlg)) {
                                sheetDto2.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                                sheetDto2.setMemo1Knj(strNullToFullSpace(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                            }
                            // 第3階層書式1が"1"の場合
                            if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                                sheetDto2.setLevel3KoumokuNo(
                                        CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                                sheetDto2.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                            } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                                sheetDto2.setLevel3KoumokuNoFont(
                                        CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                                sheetDto2.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                            }
                            list.add(sheetDto2);
                        }
                    } else {
                        // 7.1.1.2. 上記以外の場合
                        // ①、上記2で取得した情報収集シート（12）の情報リスト.宮城アセスメントマスタ第３階層の第２階層IDが”1”の場合
                        if (mygAssH3101pOutEntity.getAss3Level2Id() == CommonConstants.NUMBER_1) {
                            InfoCollectionSheet12Dto sheetDto = new InfoCollectionSheet12Dto();
                            // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                            if (CommonConstants.STR_0.equals(emptyFlg)) {
                                sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                                sheetDto.setMainKaigo(strNullToFullSpace(mygAssH3101pOutEntity.getMemo1Knj()));// 主介護者
                                sheetDto.setSubKaigo(strNullToFullSpace(mygAssH3101pOutEntity.getKonnanLevel()));// 副介護者
                                sheetDto.setShosiki2Flg(
                                        CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getShosiki2Flg()));// 書式2フラグ
                            }
                            // 第3階層書式1が"1"の場合
                            if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                                sheetDto.setLevel3KoumokuNo(
                                        CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                                sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                            } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                                sheetDto.setLevel3KoumokuNoFont(
                                        CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                                sheetDto.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                            }
                            list.add(sheetDto);
                        } else {
                            // ②、上記以外の場合
                            InfoCollectionSheet12Dto sheetDto = new InfoCollectionSheet12Dto();
                            // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                            if (CommonConstants.STR_0.equals(emptyFlg)) {
                                sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                                sheetDto.setMemo1Knj(strNullToFullSpace(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                            }
                            // 第3階層書式1が"1"の場合
                            if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_1) {
                                sheetDto.setLevel3KoumokuNo(
                                        CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                                sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                            } else if (mygAssH3101pOutEntity.getShosiki1Flg() == CommonConstants.NUMBER_2) {
                                sheetDto.setLevel3KoumokuNoFont(
                                        CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                                sheetDto.setLevel3KnjFont(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                            }
                            list.add(sheetDto);
                        }
                    }
                } else {
                    // 7.1.2. 上記以外の場合
                    InfoCollectionSheet12Dto sheetDto = new InfoCollectionSheet12Dto();
                    sheetDto.setLevel2Knj(mygAssH3101pOutEntity.getLevel2Knj());// 二級メニュー
                    sheetDto.setLevel3KoumokuNo(CommonDtoUtil.objValToString(mygAssH3101pOutEntity.getKoumokuNo()));// 項目No
                    sheetDto.setLevel3Knj(mygAssH3101pOutEntity.getLevel3Knj()); // 情報項目
                    // 記入用シート印刷フラグが"0"の場合、検討と具体的状況の設定
                    if (CommonConstants.STR_0.equals(emptyFlg)) {
                        sheetDto.setKento(getKentoKnj(mygAssH3101pOutEntity.getKentoFlg()));// 検討
                        sheetDto.setMemo1Knj(strNullToFullSpace(mygAssH3101pOutEntity.getMemo1Knj()));// 具体的状況
                    }
                    // 困難度
                    list2.add(sheetDto);
                }
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(list2);
        infoInDto.setList(dataSource1);
        infoInDto.setList2(dataSource2);
        return infoInDto;
    }

    /**
     * U06061_情報収集シート（服薬状況）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionMedicationHomeReportServiceInDto getU0601SheetMedicationReportParameters(
    		InfoCollectionSheetMedicationReportParameterModel inDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 帳票用データ詳細
        InfoCollectionMedicationHomeReportServiceInDto infoInDto = new InfoCollectionMedicationHomeReportServiceInDto();

        // 【リクエストパラメータ】.印刷オプション
        InfoCollectionReportCommonPrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = inDto.getPrintSet();
        // 【リクエストパラメータ】.ヘッダID
        Integer ass1Id = Integer.valueOf(inDto.getAss1Id());
        // 【リクエストパラメータ】.改訂区分
        Integer kaiteiKbn = Integer.valueOf(inDto.getKaiteiKbn());

        // 印刷情報の詳細リスト取得
        infoInDto = this.getReportParamsFromInfoMed(ass1Id);

        // 3. アセスメントのための情報収集シートヘッダ情報の取得
        TucMygAss1ByCriteriaInEntity tucMygAss1ByCriteriaInEntity = new TucMygAss1ByCriteriaInEntity();
        tucMygAss1ByCriteriaInEntity.setAiAss1Id(ass1Id);
        List<TucMygAss1OutEntity> tucMygAss1OutEntityList = cpnTucMygAss1SelectMapper
                .findTucMygAss1ByCriteria(tucMygAss1ByCriteriaInEntity);
        if (tucMygAss1OutEntityList != null && !tucMygAss1OutEntityList.isEmpty()) {
            // アセスメントのための情報収集シートヘッダ情報
            TucMygAss1OutEntity tucMygAss1OutEntity = tucMygAss1OutEntityList.get(0);
            // 基準日
            if (tucMygAss1OutEntity.getCreateYmd() != null && !tucMygAss1OutEntity.getCreateYmd().isEmpty()) {
                String createYmd = tucMygAss1OutEntity.getCreateYmd();
                if (createYmd.contains(CommonConstants.STR_DELIMITER)) {
                    createYmd = createYmd.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(createYmd));
            }

            // 4. 職員基本情報の取得
            if (tucMygAss1OutEntity.getShokuId() != null) {
                ShokuinByCriteriaInEntity shokuinByCriteriaInEntity = new ShokuinByCriteriaInEntity();
                shokuinByCriteriaInEntity.setShokuId(tucMygAss1OutEntity.getShokuId());
                List<ShokuinOutEntity> shokuinOutEntityList = comMscShokuinSelectMapper
                        .findShokuinByCriteria(shokuinByCriteriaInEntity);
                if (shokuinOutEntityList != null && !shokuinOutEntityList.isEmpty()) {
                    ShokuinOutEntity shokuinOutEntity = shokuinOutEntityList.getFirst();
                    String shokuin1Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin1Knj());
                    String shokuin2Knj = CommonDtoUtil.strNullToEmpty(shokuinOutEntity.getShokuin2Knj());
                    // 担当者名
                    infoInDto.setTantoNm(shokuin1Knj + CommonConstants.BLANK_SPACE + shokuin2Knj);
                }
            }

            // 5. 利用者基本情報の取得
            KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw1OutEntity> kghCpnRaiMonKentPrnPreSrw1OutEntityList = comTucUserSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw1ByCriteria(kghCpnRaiMonKentPrnPreSrw1ByCriteriaInEntity);
            if (kghCpnRaiMonKentPrnPreSrw1OutEntityList != null && !kghCpnRaiMonKentPrnPreSrw1OutEntityList.isEmpty()) {
                // 利用者名
                infoInDto.setRiyoushaNm(kghCpnRaiMonKentPrnPreSrw1OutEntityList.get(0).getFullName());
            }

            // 6. サービス事業者マスタ情報取得
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(tucMygAss1OutEntity.getSvJigyoId()));
            List<SvJigyoNameOutEntity> svJigyoNameOutEntityList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            if (svJigyoNameOutEntityList != null && !svJigyoNameOutEntityList.isEmpty()) {
                // 事業所名
                infoInDto.setJigyoName(svJigyoNameOutEntityList.getFirst().getJigyoRyakuKnj());
            }
        }
        infoInDto.setTantoFont(this.setNameFont(infoInDto.getTantoNm())); // 担当者名のフォントサイズ
        infoInDto.setRiyoushaNmFont(this.setNameFont(infoInDto.getRiyoushaNm())); // 利用者名のフォントサイズ

        // 敬称
        infoInDto.setKeisho(CommonConstants.STR_1.equals(printOption.getKeishoFlg()) ? printOption.getKeisho()
                : CommonConstants.STR_LIKE);
        // 改訂区分
        infoInDto.setKaiteiKbn(kaiteiKbn);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = 1 の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            // 【リクエストパラメータ】.事業者名
            String jigyoName = inDto.getJigyoName();
            // 事業所名
            infoInDto.setJigyoName(jigyoName);

            // 8. システムの空欄表示処理
            DateTimeFormatter inputFormatter = DateTimeFormatter
                    .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDateTime dateTime = LocalDateTime.parse(inDto.getSystemDate(), inputFormatter);
            String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
            // 基準日
            infoInDto.setCreateYmd(blankDate.replace(CommonConstants.SPACE_STRING, CommonConstants.FULL_WIDTH_SPACE));
        } else {
            // 指定日
            infoInDto.setShiTeiDate(
                    this.getShiTeiDate(printSet.getShiTeiKubun(), printSet.getShiTeiDate(), inDto.getSystemDate()));
        }
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * 帳票詳細情報リスト取得(服薬状況)
     *
     * @param ass1Id ヘッダID
     * @return 帳票詳細情報リスト
     * @throws Exception 例外
     */
    public InfoCollectionMedicationHomeReportServiceInDto getReportParamsFromInfoMed(Integer ass1Id) throws Exception {
        // 帳票用データ詳細
        InfoCollectionMedicationHomeReportServiceInDto infoInDto = new InfoCollectionMedicationHomeReportServiceInDto();

        // 2.情報収集シート（服薬状況）の情報リストを取得する。
        // 検索条件
        TucMygAss3ByCritieaInEntity tucMygAss3ByCritieaInEntity = new TucMygAss3ByCritieaInEntity();
        // ヘッダID
        tucMygAss3ByCritieaInEntity.setAlAss1Id(ass1Id);
        List<TucMygAss3OutEntity> tucMygAss3OutEntityList = cpnTucMygAss3SelectMapper
                .findTucMygAss3ByCritiea(tucMygAss3ByCritieaInEntity);

        // 7. 詳細情報リストを設定
        List<InfoCollectionSheetMedOutDto> list = new ArrayList<>();

        // 薬剤リスト取得
        KaigoShoDrugByCriteriaInEntity shoMstDrug2InfoByCriteriaInEntity = new KaigoShoDrugByCriteriaInEntity();
        List<KaigoShoDrugOutEntity> shoMstDrug2InfoOutEntityList = shoMstDrug2SelectMapper
                .findKaigoShoDrugByCriteria(shoMstDrug2InfoByCriteriaInEntity);

        if (tucMygAss3OutEntityList != null && !tucMygAss3OutEntityList.isEmpty()) {

            for (TucMygAss3OutEntity entity : tucMygAss3OutEntityList) {
                InfoCollectionSheetMedOutDto infoDto = new InfoCollectionSheetMedOutDto();
                // 量
                infoDto.setRyouKnj(entity.getRyouKnj());
                // 効能
                infoDto.setKounouKnj(entity.getKounouKnj());
                // 薬剤名
                infoDto.setDmyYakuzaiName(CommonConstants.BLANK_STRING);
                if (shoMstDrug2InfoOutEntityList != null && !shoMstDrug2InfoOutEntityList.isEmpty()) {
                    for (KaigoShoDrugOutEntity drugEntity : shoMstDrug2InfoOutEntityList) {
                        if (drugEntity.getDrugId().equals(entity.getYakuzaiId())) {
                            // 薬剤名
                            infoDto.setDmyYakuzaiName(drugEntity.getDrugKnj());
                            break;
                        }
                    }
                }
                list.add(infoDto);
            }
        } else {
            // 10個空項目を作成
            for (int i = 0; i < 10; i++) {
                InfoCollectionSheetMedOutDto infoDto = new InfoCollectionSheetMedOutDto();
                // 量
                infoDto.setRyouKnj(CommonConstants.BLANK_STRING);
                // 効能
                infoDto.setKounouKnj(CommonConstants.BLANK_STRING);
                // 薬剤名
                infoDto.setDmyYakuzaiName(CommonConstants.BLANK_STRING);
                list.add(infoDto);
            }
        }

        JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(list);
        infoInDto.setList(dataSource1);

        return infoInDto;
    }

    /**
     * U06061_情報収集シート（全て）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public InfoCollectionAllHomeReportServiceInDto getU06061AllReportParameters(
            InfoCollectionSheetReportParameterModel inDto) throws Exception {
        // 帳票用データ詳細
        InfoCollectionAllHomeReportServiceInDto allHomeReportInfo = new InfoCollectionAllHomeReportServiceInDto();

        // シート (1)
        inDto.setLevel1Id(CommonConstants.STR_1);
        InfoCollectionSheet1HomeReportServiceInDto sheet1Info = getU06061Sheet1ReportParameters(inDto);
        // 注釈表示フラグ
        allHomeReportInfo.setChushakuFlg(sheet1Info.getChushakuFlg());
        // ヘッダー情報
        allHomeReportInfo.setKaiteiKbn(sheet1Info.getKaiteiKbn());
        allHomeReportInfo.setShiTeiDate(sheet1Info.getShiTeiDate());
        allHomeReportInfo.setShiTeiKubun(sheet1Info.getShiTeiKubun());
        allHomeReportInfo.setJigyoName(sheet1Info.getJigyoName());
        allHomeReportInfo.setRiyoushaNm(sheet1Info.getRiyoushaNm());
        allHomeReportInfo.setRiyoushaNmFont(sheet1Info.getRiyoushaNmFont());
        allHomeReportInfo.setCreateYmd(sheet1Info.getCreateYmd());
        allHomeReportInfo.setKeisho(sheet1Info.getKeisho());
        allHomeReportInfo.setTantoNm(sheet1Info.getTantoNm());
        allHomeReportInfo.setTantoFont(sheet1Info.getTantoFont());
        // allHomeReportInfo.setLevel1Knj(sheet1Info.getLevel1Knj());
        allHomeReportInfo.setLevel1Knj("");
        allHomeReportInfo.setList1(sheet1Info.getList());
        allHomeReportInfo.setList1_1(sheet1Info.getList2());
        // シート (6)
        inDto.setLevel1Id(CommonConstants.STR_2);
        InfoCollectionSheet2HomeReportServiceInDto sheet2Info = getU06061Sheet2ReportParameters(inDto);
        allHomeReportInfo.setList2(sheet2Info.getList());
        allHomeReportInfo.setList2_1(sheet2Info.getList2());

        // シート (3)
        inDto.setLevel1Id(CommonConstants.STR_3);
        InfoCollectionSheet3HomeReportServiceInDto sheet3Info = getU06061Sheet3ReportParameters(inDto);
        allHomeReportInfo.setList3(sheet3Info.getList());
        allHomeReportInfo.setList3_1(sheet3Info.getList2());

        // シート (4)
        inDto.setLevel1Id(CommonConstants.STR_4);
        InfoCollectionSheet4HomeReportServiceInDto sheet4Info = getU06061Sheet4ReportParameters(inDto);
        allHomeReportInfo.setList4(sheet4Info.getList());
        allHomeReportInfo.setList4_1(sheet4Info.getList2());

        // シート (5)
        inDto.setLevel1Id(CommonConstants.STR_5);
        InfoCollectionSheet5HomeReportServiceInDto sheet5Info = getU06061Sheet5ReportParameters(inDto);
        allHomeReportInfo.setList5(sheet5Info.getList());
        allHomeReportInfo.setList5_1(sheet5Info.getList2());

        // シート (6)
        inDto.setLevel1Id(CommonConstants.STR_6);
        InfoCollectionSheet6HomeReportServiceInDto sheet6Info = getU06061Sheet6ReportParameters(inDto);
        allHomeReportInfo.setList6(sheet6Info.getList());
        allHomeReportInfo.setList6_1(sheet6Info.getList2());

        // シート (7)
        inDto.setLevel1Id(CommonConstants.STR_7);
        InfoCollectionSheet7HomeReportServiceInDto sheet7Info = getU06061Sheet7ReportParameters(inDto);
        allHomeReportInfo.setList7(sheet7Info.getList());
        allHomeReportInfo.setList7_1(sheet7Info.getList2());

        // シート (8)
        inDto.setLevel1Id(CommonConstants.STR_8);
        InfoCollectionSheet8HomeReportServiceInDto sheet8Info = getU06061Sheet8ReportParameters(inDto);
        allHomeReportInfo.setList8(sheet8Info.getList());
        allHomeReportInfo.setList8_1(sheet8Info.getList2());

        // シート (9)
        inDto.setLevel1Id(CommonConstants.STR_9);
        InfoCollectionSheet9HomeReportServiceInDto sheet9Info = getU06061Sheet9ReportParameters(inDto);
        allHomeReportInfo.setList9(sheet9Info.getList());
        allHomeReportInfo.setList9_1(sheet9Info.getList2());

        // シート (10)
        inDto.setLevel1Id(CommonConstants.STR_10);
        InfoCollectionSheet10HomeReportServiceInDto sheet10Info = getU06061Sheet10ReportParameters(inDto);
        allHomeReportInfo.setList10(sheet10Info.getList());
        allHomeReportInfo.setList10_1(sheet10Info.getList2());

        // シート (11)
        inDto.setLevel1Id(CommonConstants.STR_11);
        InfoCollectionSheet11HomeReportServiceInDto sheet11Info = getU06061Sheet11ReportParameters(inDto);
        allHomeReportInfo.setList11(sheet11Info.getList());
        allHomeReportInfo.setList11_1(sheet11Info.getList2());

        // シート (12)
        inDto.setLevel1Id(CommonConstants.STR_12);
        InfoCollectionSheet12HomeReportServiceInDto sheet12Info = getU06061Sheet12ReportParameters(inDto);
        allHomeReportInfo.setList12(sheet12Info.getList());
        allHomeReportInfo.setList12_1(sheet12Info.getList2());

        // シート (13)
        inDto.setLevel1Id(ReportConstants.STR_13);
        InfoCollectionSheet13HomeReportServiceInDto sheet13Info = getU06061Sheet13ReportParameters(inDto);
        allHomeReportInfo.setList13(sheet13Info.getList());
        allHomeReportInfo.setList13_1(sheet13Info.getList2());

        // シート (14)
        inDto.setLevel1Id(CommonConstants.STR_14);
        InfoCollectionSheet14HomeReportServiceInDto sheet14Info = getU06061Sheet14ReportParameters(inDto);
        allHomeReportInfo.setList14(sheet14Info.getList());
        allHomeReportInfo.setList14_1(sheet14Info.getList2());

        // シート (その他)
        inDto.setLevel1Id(CommonConstants.STR_15);
        InfoCollectionSheetOthersHomeReportServiceInDto sheetOthersInfo = getU06061SheetOthersReportParameters(inDto);
        allHomeReportInfo.setList15(sheetOthersInfo.getList());
        allHomeReportInfo.setList15_1(sheetOthersInfo.getList2());

        InfoCollectionSheetMedicationReportParameterModel medModel = new InfoCollectionSheetMedicationReportParameterModel();
        medModel.setPrintOption(inDto.getPrintOption());
        medModel.setPrintSet(inDto.getPrintSet());
        medModel.setAss1Id(inDto.getAss1Id());
        medModel.setMstKbn(inDto.getMstKbn());
        medModel.setKaiteiKbn(inDto.getKaiteiKbn());
        medModel.setJigyoName(inDto.getJigyoName());
        
        // シート (服薬状況)
        InfoCollectionMedicationHomeReportServiceInDto sheetMedInfo = getU0601SheetMedicationReportParameters(medModel);
        allHomeReportInfo.setList16(sheetMedInfo.getList());
        allHomeReportInfo.setList16_1(sheetMedInfo.getList2());

        return allHomeReportInfo;
    }

    /**
     * フォントサイズ設定
     * 
     * @param name 名称
     * @return String フォントサイズ
     */
    private String setNameFont(String name) {
        String retFont = CommonConstants.STR_1;
        // NULLの場合
        if (name == null) {
            return retFont;
        }
        int strLen = name.getBytes().length;
        if (strLen > CommonConstants.INT_22 && strLen <= CommonConstants.INT_24) {
            retFont = CommonConstants.STR_2;
        } else if (strLen > CommonConstants.INT_24 && strLen <= CommonConstants.INT_26) {
            retFont = CommonConstants.STR_3;
        } else if (strLen > CommonConstants.INT_26 && strLen <= CommonConstants.INT_32) {
            retFont = CommonConstants.STR_4;
        } else if (strLen > CommonConstants.INT_32) {
            retFont = CommonConstants.STR_5;
        }
        return retFont;
    }

    /**
     * 検討項目の設定
     * 
     * @param kentoFlg 検討フラグ
     * @return kentoKng 検討
     */
    public String getKentoKnj(Integer kentoFlg) {
        String kentoKng = CommonConstants.BLANK_STRING;
        if (kentoFlg != null) {
            if (kentoFlg == CommonConstants.INT_0) {
                kentoKng = ReportConstants.KENTO_FLG_0;
            } else if (kentoFlg == CommonConstants.INT_1) {
                kentoKng = ReportConstants.KENTO_FLG_1;
            }
        }
        return kentoKng;
    }

    /**
     * 項目が空の場合、全角スペースに変更
     * 
     * @param value データ
     * @return 変更後データ
     */
    private String strNullToFullSpace(String value) {
        if (value == null || value.equals("")) {
            value = CommonConstants.FULL_WIDTH_SPACE;
        }
        return value;
    }
}