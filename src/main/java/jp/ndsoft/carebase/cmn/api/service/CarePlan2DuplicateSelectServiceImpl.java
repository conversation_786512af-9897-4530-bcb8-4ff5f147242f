package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.*;
import jp.ndsoft.carebase.cmn.api.service.dto.CarePlan2DuplicateSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.CarePlan2DuplicateSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.*;
import jp.ndsoft.carebase.common.dao.mysql.mapper.*;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.invoke.MethodHandles;
import java.util.*;
import java.util.stream.Collectors;

/**
 * GUI01014_計画書（２）複写情報取得サービス.
 *
 * <AUTHOR>
 */
@Service
public class CarePlan2DuplicateSelectServiceImpl
        extends SelectServiceImpl<CarePlan2DuplicateSelectServiceInDto, CarePlan2DuplicateSelectServiceOutDto> {
    /**
     * ロガー
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /**
     * 計画書（２）詳細情報を取得
     */
    @Autowired
    private CpnTucCks22SelectMapper cpnTucCks22SelectMapper;

    /**
     * 保険サービス情報を取得
     */
    @Autowired
    private CpnTucCks211SelectMapper cpnTucCks211SelectMapper;

    /**
     * 月日指定情報を取得
     */
    @Autowired
    private CpnTucCks212SelectMapper cpnTucCks212SelectMapper;

    /**
     * サービス曜日情報を取得
     */
    @Autowired
    private CpnTucCks221SelectMapper cpnTucCks221SelectMapper;

    /**
     * サービス曜日情報を取得
     */
    @Autowired
    private CpnTucCks222SelectMapper cpnTucCks222SelectMapper;

    /**
     * CpnTucCks224SelectMapper
     */
    @Autowired
    private CpnTucCks224SelectMapper cpnTucCks224SelectMapper;

    /**
     * 「計画書（２）」画面の複写情報を取得する。
     *
     * @param inDto 計画書（２）複写情報取得サービス入力Dto
     * @return 計画書（２）複写情報取得の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected CarePlan2DuplicateSelectServiceOutDto mainProcess(CarePlan2DuplicateSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // 1.単項目チェック以外の入力チェック
        // 特になし
        // INPUT情報
        CpnTucCks22Ns1SortInfoByCriteriaInEntity cpnTucCks22Nk1InfoByCriteriaInEntity = new CpnTucCks22Ns1SortInfoByCriteriaInEntity();
        // 計画書（２）ID
        cpnTucCks22Nk1InfoByCriteriaInEntity.setKs21(CommonDtoUtil.strValToInt(inDto.getKs21Id()));

        // 計画書（２）複写情報取得の出力Dto
        CarePlan2DuplicateSelectServiceOutDto outDto = new CarePlan2DuplicateSelectServiceOutDto();
        // 2. 計画書（２）詳細データを取得する。
        // 2.1. 計画書（２）詳細情報を取得する。
        List<CpnTucCks22Ns1SortInfoOutEntity> cpnTucCks22Nk1Info = cpnTucCks22SelectMapper
                .findCpnTucCks22Ns1SortInfoByCriteria(cpnTucCks22Nk1InfoByCriteriaInEntity);
        // 計画書（２）の編集
        outDto.setKeikasyo2List(keikasyo2DataEditProc(cpnTucCks22Nk1Info));
        // 2.2. リクエストパラメータ.計画書様式が居宅の場合
        if (CommonConstants.CKS_FLG_HOME.equals(inDto.getCksflg())) {
            // INPUT情報
            CpnTucCks211InfoByCriteriaInEntity cpnTucCks211InfoByCriteriaInEntity = new CpnTucCks211InfoByCriteriaInEntity();
            Keikaku2TukihiInfoByCriteriaInEntity keikaku2TukihiInfoByCriteriaInEntity = new Keikaku2TukihiInfoByCriteriaInEntity();
            // 計画書（２）ID
            cpnTucCks211InfoByCriteriaInEntity.setAiKs21Id(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
            keikaku2TukihiInfoByCriteriaInEntity.setAiKs21Id(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
            // 2.2.1. 保険サービス情報を取得する。
            List<CpnTucCks211InfoOutEntity> cpnTucCks211Info = cpnTucCks211SelectMapper
                    .findCpnTucCks211InfoByCriteria(cpnTucCks211InfoByCriteriaInEntity);
            // 2.2.2. 月日指定情報を取得する。
            List<Keikaku2TukihiInfoOutEntity> keikaku2TukihiInfo = cpnTucCks212SelectMapper
                    .findKeikaku2TukihiInfoByCriteria(keikaku2TukihiInfoByCriteriaInEntity);
            // 保険サービス情報の編集
            outDto.setHokenList(hokenDataEditProc(cpnTucCks211Info));
            // 月日指定情報の編集
            outDto.setTukihiList(tukihiDataEditProc(keikaku2TukihiInfo));
        } // 2.3. リクエストパラメータ.計画書様式が施設の場合
        else if (CommonConstants.CKS_FLG_FACILITY.equals(inDto.getCksflg())) {
            // INPUT情報
            CpnTucCks221InfoByCriteriaInEntity cpnTucCks221InfoByCriteriaInEntity = new CpnTucCks221InfoByCriteriaInEntity();
            CpnTucCks222InfoByCriteriaInEntity cpnTucCks222InfoByCriteriaInEntity = new CpnTucCks222InfoByCriteriaInEntity();
            // 計画書（２）ID
            cpnTucCks221InfoByCriteriaInEntity.setAiKs21Id(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
            cpnTucCks222InfoByCriteriaInEntity.setAiKs21Id(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
            // 2.3.1. サービス曜日情報を取得する。
            List<CpnTucCks221InfoOutEntity> cpnTucCks221Info = cpnTucCks221SelectMapper
                    .findCpnTucCks221InfoByCriteria(cpnTucCks221InfoByCriteriaInEntity);
            // 2.3.2. サービス曜日情報を取得する。
            List<CpnTucCks222InfoOutEntity> cpnTucCks222Info = cpnTucCks222SelectMapper
                    .findCpnTucCks222InfoByCriteria(cpnTucCks222InfoByCriteriaInEntity);
            // サービス曜日情報の編集
            List<Gui01014Yobi> yobiList = yobiDataEditProc(cpnTucCks221Info);
            // 担当者情報の編集
            List<Gui01014Tanto> tantoList = tantoDataEditProc(cpnTucCks222Info);

            for (Gui01014Keikasyo2 kasyo2Info : outDto.getKeikasyo2List()) {
                // サービス曜日リスト
                List<Gui01014Yobi> filterYobiList = yobiList.stream()
                        // 計画書（２）行データID ＝ 計画書（２）リスト.カウンター
                        .filter(item -> item.getKs22Id().equals(kasyo2Info.getKs22Id())
                                // 計画書（２）ID = 計画書（２）リスト.計画書（２）ID
                                && item.getKs21Id().equals(kasyo2Info.getKs21Id()))
                        .collect(Collectors.toList());
                kasyo2Info.setYobiList(filterYobiList);

                // 担当者リスト
                List<Gui01014Tanto> filterTantoList = tantoList.stream()
                        // 計画書（２）行データID ＝ 計画書（２）リスト.カウンター
                        .filter(item -> item.getKs22Id().equals(kasyo2Info.getKs22Id())
                                // 計画書（２）ID = 計画書（２）リスト.計画書（２）ID
                                && item.getKs21Id().equals(kasyo2Info.getKs21Id()))
                        .collect(Collectors.toList());
                kasyo2Info.setTantoList(filterTantoList);
            }
        }

        // 2.4. リクエストパラメータ.記録との連携が「1」:記録との連携を行うの場合
        if (CommonConstants.STR_1.equals(inDto.getKirokuRenkeiFlg())) {
            CpnTucCks224InfoByCriteriaInEntity cpnTucCks224InfoIn = new CpnTucCks224InfoByCriteriaInEntity();
            cpnTucCks224InfoIn.setKs21Id(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
            List<CpnTucCks224InfoOutEntity> cpnTucCks224InfoOuts = cpnTucCks224SelectMapper.findCpnTucCks224InfoByCriteria(
                    cpnTucCks224InfoIn);

            Map<String, CpnTucCks224InfoOutEntity> cpnTucCks224InfoMap = new HashMap<>();
            cpnTucCks224InfoOuts.forEach((cpnTucCks224Info) -> {
                cpnTucCks224InfoMap.put(CommonDtoUtil.objValToString(cpnTucCks224Info.getKs22Id()),
                        cpnTucCks224Info);
            });

            outDto.getKeikasyo2List().forEach((keikasyo2) -> {
                CpnTucCks224InfoOutEntity cpnTucCks224Info = cpnTucCks224InfoMap.get(keikasyo2.getKs22Id());
                if (cpnTucCks224Info != null) {
                    keikasyo2.setNikkaId(CommonDtoUtil.objValToString(cpnTucCks224Info.getNikkaId()));
                }

                // 介護番号
                String kaigoNo = keikasyo2.getKaigoNo();
                Integer kaigoNoInt = CommonDtoUtil.strValToInt(kaigoNo);
                if (kaigoNoInt != null) {
                    kaigoNoInt += CommonConstants.INT_1;
                    keikasyo2.setKaigoNo(CommonDtoUtil.objValToString(kaigoNoInt));
                }
            });
        }

        // 3. 上記処理で取得した結果レスポンスを返却する。
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 計画書（２）の編集
     *
     * @param cpnTucCks22Nk1Info 計画書（２）詳細情報
     * @return keikasyo2List 計画書（２）リスト
     */
    private List<Gui01014Keikasyo2> keikasyo2DataEditProc(List<CpnTucCks22Ns1SortInfoOutEntity> cpnTucCks22Nk1Info) {
        // 計画書（２）リスト
        List<Gui01014Keikasyo2> keikasyo2List = new ArrayList<>();
        for (CpnTucCks22Ns1SortInfoOutEntity cpnTucCks22Nk1 : cpnTucCks22Nk1Info) {
            // 計画書（２）
            Gui01014Keikasyo2 keikasyo2 = new Gui01014Keikasyo2();
            // カウンター
            keikasyo2.setKs22Id(cpnTucCks22Nk1.getKs22Id() == null ? null : cpnTucCks22Nk1.getKs22Id().toString());
            // 計画書（２）ID
            keikasyo2.setKs21Id(cpnTucCks22Nk1.getKs21Id() == null ? null : cpnTucCks22Nk1.getKs21Id().toString());
            // 具体的
            keikasyo2.setGutaitekiKnj(cpnTucCks22Nk1.getGutaitekiKnj());
            // 長期
            keikasyo2.setChoukiKnj(cpnTucCks22Nk1.getChoukiKnj());
            // 短期
            keikasyo2.setTankiKnj(cpnTucCks22Nk1.getTankiKnj());
            // 介護
            keikasyo2.setKaigoKnj(cpnTucCks22Nk1.getKaigoKnj());
            // サービス種
            keikasyo2.setSvShuKnj(cpnTucCks22Nk1.getSvShuKnj());
            // 頻度
            keikasyo2.setHindoKnj(cpnTucCks22Nk1.getHindoKnj());
            // 期間
            keikasyo2.setKikanKnj(cpnTucCks22Nk1.getKikanKnj());
            // 通番
            keikasyo2.setSeq(cpnTucCks22Nk1.getSeq() == null ? null : cpnTucCks22Nk1.getSeq().toString());
            // 課題番号
            keikasyo2.setKadaiNo(cpnTucCks22Nk1.getKadaiNo() == null ? null : cpnTucCks22Nk1.getKadaiNo().toString());
            // 介護番号
            keikasyo2.setKaigoNo(cpnTucCks22Nk1.getKaigoNo() == null ? null : cpnTucCks22Nk1.getKaigoNo().toString());
            // 長期期間
            keikasyo2.setChoKikanKnj(cpnTucCks22Nk1.getChoKikanKnj());
            // 短期期間
            keikasyo2.setTanKikanKnj(cpnTucCks22Nk1.getTanKikanKnj());
            // 給付対象
            keikasyo2.setHkyuKbn(cpnTucCks22Nk1.getHkyuKbn() == null ? null : cpnTucCks22Nk1.getHkyuKbn().toString());
            // サービス事業者ＣＤ
            keikasyo2
                    .setJigyouId(cpnTucCks22Nk1.getJigyouId() == null ? null : cpnTucCks22Nk1.getJigyouId().toString());
            // サービス事業者名
            keikasyo2.setJigyoNameKnj(cpnTucCks22Nk1.getJigyoNameKnj());
            // 給付文字
            keikasyo2.setHkyuKnj(cpnTucCks22Nk1.getHkyuKnj());
            // 長期期間開始日
            keikasyo2.setChoSYmd(cpnTucCks22Nk1.getChoSYmd());
            // 長期期間終了日
            keikasyo2.setChoEYmd(cpnTucCks22Nk1.getChoEYmd());
            // 短期期間開始日
            keikasyo2.setTanSYmd(cpnTucCks22Nk1.getTanSYmd());
            // 短期期間終了日
            keikasyo2.setTanEYmd(cpnTucCks22Nk1.getTanEYmd());
            // 期間開始日
            keikasyo2.setKikanSYmd(cpnTucCks22Nk1.getKikanSYmd());
            // 期間終了日
            keikasyo2.setKikanEYmd(cpnTucCks22Nk1.getKikanEYmd());
            keikasyo2List.add(keikasyo2);
        }
        return keikasyo2List;
    }

    /**
     * 保険サービス情報の編集
     *
     * @param cpnTucCks211Info 保険サービス情報
     * @return hokenList 保険サービス情報リスト
     */
    private List<Gui01014Hoken> hokenDataEditProc(List<CpnTucCks211InfoOutEntity> cpnTucCks211Info) {
        // 保険サービス情報リスト
        List<Gui01014Hoken> hokenList = new ArrayList<>();
        for (CpnTucCks211InfoOutEntity cpnTucCks211 : cpnTucCks211Info) {
            // 保険サービス情報
            Gui01014Hoken hoken = new Gui01014Hoken();
            // カウンター
            hoken.setKs211Id(cpnTucCks211.getKs211Id() == null ? null : cpnTucCks211.getKs211Id().toString());
            // 計画書（２）ID
            hoken.setKs21Id(cpnTucCks211.getKs21Id() == null ? null : cpnTucCks211.getKs21Id().toString());
            // 曜日
            hoken.setYoubi(cpnTucCks211.getYoubi());
            // 週単位以外のｻｰﾋﾞｽ区分
            hoken.setIgaiKbn(cpnTucCks211.getIgaiKbn() == null ? null : cpnTucCks211.getIgaiKbn().toString());
            // 週単位以外のｻｰﾋﾞｽ（日付指定）
            hoken.setIgaiDate(cpnTucCks211.getIgaiDate());
            // 週単位以外のｻｰﾋﾞｽ（曜日指定）
            hoken.setIgaiWeek(cpnTucCks211.getIgaiWeek());
            // 居宅：開始時間
            hoken.setKaishiJikan(cpnTucCks211.getKaishiJikan());
            // 居宅：終了時間
            hoken.setShuuryouJikan(cpnTucCks211.getShuuryouJikan());
            // 居宅：サービス種類
            hoken.setSvShuruiCd(cpnTucCks211.getSvShuruiCd());
            // 居宅：サービス項目（台帳）
            hoken.setSvItemCd(cpnTucCks211.getSvItemCd() == null ? null : cpnTucCks211.getSvItemCd().toString());
            // 居宅：サービス事業者CD
            hoken.setSvJigyoId(cpnTucCks211.getSvJigyoId() == null ? null : cpnTucCks211.getSvJigyoId().toString());
            // 居宅：福祉用具貸与単位
            hoken.setTanka(cpnTucCks211.getTanka() == null ? null : cpnTucCks211.getTanka().toString());
            // 福祉用具貸与商品コード
            hoken.setFygId(cpnTucCks211.getFygId() == null ? null : cpnTucCks211.getFygId().toString());
            // 合成識別区分
            hoken.setGouseiSikKbn(cpnTucCks211.getGouseiSikKbn());
            // 加算フラグ
            hoken.setKasanFlg(cpnTucCks211.getKasanFlg() == null ? null : cpnTucCks211.getKasanFlg().toString());
            // 親レコード番号
            hoken.setOyaLineNo(cpnTucCks211.getOyaLineNo() == null ? null : cpnTucCks211.getOyaLineNo().toString());
            hokenList.add(hoken);
        }
        return hokenList;
    }

    /**
     * 月日指定情報の編集
     *
     * @param keikaku2TukihiInfo 月日指定情報
     * @return tukihiList 月日指定情報リスト
     */
    private List<Gui01014Tukihi> tukihiDataEditProc(List<Keikaku2TukihiInfoOutEntity> keikaku2TukihiInfo) {
        // 月日指定情報リスト
        List<Gui01014Tukihi> tukihiList = new ArrayList<>();
        for (Keikaku2TukihiInfoOutEntity keikaku2Tukihi : keikaku2TukihiInfo) {
            // 月日指定情報
            Gui01014Tukihi tukihi = new Gui01014Tukihi();
            // カウンター
            tukihi.setKs212Id(keikaku2Tukihi.getKs212Id() == null ? null : keikaku2Tukihi.getKs212Id().toString());
            // 計画書（２）ID
            tukihi.setKs21Id(keikaku2Tukihi.getKs21Id() == null ? null : keikaku2Tukihi.getKs21Id().toString());
            // 月日指定開始日
            tukihi.setStartYmd(keikaku2Tukihi.getStartYmd());
            // 月日指定終了日
            tukihi.setEndYmd(keikaku2Tukihi.getEndYmd());
            tukihiList.add(tukihi);
        }
        return tukihiList;
    }

    /**
     * サービス曜日情報の編集
     *
     * @param cpnTucCks221Info サービス曜日情報
     * @return yobiList サービス曜日リスト
     */
    private List<Gui01014Yobi> yobiDataEditProc(List<CpnTucCks221InfoOutEntity> cpnTucCks221Info) {
        // サービス曜日リスト
        List<Gui01014Yobi> yobiList = new ArrayList<>();
        for (CpnTucCks221InfoOutEntity cpnTucCks221 : cpnTucCks221Info) {
            // サービス曜日
            Gui01014Yobi yobi = new Gui01014Yobi();
            // カウンター
            yobi.setKs221Id(cpnTucCks221.getKs221Id() == null ? null : cpnTucCks221.getKs221Id().toString());
            // 計画書（２）行データID
            yobi.setKs22Id(cpnTucCks221.getKs22Id() == null ? null : cpnTucCks221.getKs22Id().toString());
            // 計画書（２）ID
            yobi.setKs21Id(cpnTucCks221.getKs21Id() == null ? null : cpnTucCks221.getKs21Id().toString());
            // 曜日
            yobi.setYoubi(cpnTucCks221.getYoubi());
            // 週単位以外のｻｰﾋﾞｽ区分
            yobi.setIgaiKbn(cpnTucCks221.getIgaiKbn() == null ? null : cpnTucCks221.getIgaiKbn().toString());
            // 週単位以外のｻｰﾋﾞｽ（日付指定）
            yobi.setIgaiDate(cpnTucCks221.getIgaiDate());
            // 週単位以外のｻｰﾋﾞｽ（曜日指定）
            yobi.setIgaiWeek(cpnTucCks221.getIgaiWeek());
            // 居宅：開始時間
            yobi.setKaishiJikan(cpnTucCks221.getKaishiJikan());
            // 居宅：終了時間
            yobi.setShuuryouJikan(cpnTucCks221.getShuuryouJikan());
            // 居宅：サービス種類
            yobi.setSvShuruiCd(cpnTucCks221.getSvShuruiCd());
            // 居宅：サービス項目（台帳）
            yobi.setSvItemCd(cpnTucCks221.getSvItemCd() == null ? null : cpnTucCks221.getSvItemCd().toString());
            // 居宅：サービス事業者CD
            yobi.setSvJigyoId(cpnTucCks221.getSvJigyoId() == null ? null : cpnTucCks221.getSvJigyoId().toString());
            // 居宅：福祉用具貸与単位
            yobi.setTanka(cpnTucCks221.getTanka() == null ? null : cpnTucCks221.getTanka().toString());
            // 福祉用具貸与商品コード
            yobi.setFygId(cpnTucCks221.getFygId() == null ? null : cpnTucCks221.getFygId().toString());
            // 合成識別区分
            yobi.setGouseiSikKbn(cpnTucCks221.getGouseiSikKbn());
            // 加算フラグ
            yobi.setKasanFlg(cpnTucCks221.getKasanFlg() == null ? null : cpnTucCks221.getKasanFlg().toString());
            yobiList.add(yobi);
        }
        return yobiList;
    }

    /**
     * 担当者情報の編集
     *
     * @param cpnTucCks222Info サービス曜日情報
     * @return tantoList 担当者情報リスト
     */
    private List<Gui01014Tanto> tantoDataEditProc(List<CpnTucCks222InfoOutEntity> cpnTucCks222Info) {
        // 担当者情報リスト
        List<Gui01014Tanto> tantoList = new ArrayList<>();
        for (CpnTucCks222InfoOutEntity cpnTucCks222 : cpnTucCks222Info) {
            // 担当者情報
            Gui01014Tanto tanto = new Gui01014Tanto();
            // カウンター
            tanto.setKs222Id(cpnTucCks222.getKs222Id() == null ? null : cpnTucCks222.getKs222Id().toString());
            // 計画書（２）行データID
            tanto.setKs22Id(cpnTucCks222.getKs22Id() == null ? null : cpnTucCks222.getKs22Id().toString());
            // 計画書（２）ID
            tanto.setKs21Id(cpnTucCks222.getKs21Id() == null ? null : cpnTucCks222.getKs21Id().toString());
            // 施設：職種（担当者）
            tanto.setShokushuId(cpnTucCks222.getShokushuId() == null ? null : cpnTucCks222.getShokushuId().toString());
            tantoList.add(tanto);
        }
        return tantoList;
    }
}
