package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.GyoumuComLogic;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComElectronicSaveInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComElectronicSaveInitMasterData;
import jp.ndsoft.carebase.cmn.api.service.dto.MonthlyYearlyTableMonthlyDataDto;
import jp.ndsoft.carebase.cmn.api.service.dto.MonthlyYearlyTableUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.MonthlyYearlyTableUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucSypNenkan1Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucSypNenkan2Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghTucKrkKikanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucSypNenkan1;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucSypNenkan1Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucSypNenkan2;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucSypNenkan2Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTucKrkKikan;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * GUI00939_年間・月間
 * 情報更新サービス
 *
 * <AUTHOR>
 */
@Service
public class MonthlyYearlyTableUpdateServiceImpl
        extends UpdateServiceImpl<MonthlyYearlyTableUpdateServiceInDto, MonthlyYearlyTableUpdateServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    /** 操作区分 3:削除 */
    private static final String OPER_FLAG_3 = "3";
    /** [mnu3][3GK]月間・年間表 */
    private static final String MENU3_KNJ = "[mnu3][3GK]月間・年間表";

    @Autowired
    private CpnTucSypNenkan1Mapper cpnTucSypNenkan1Mapper;
    @Autowired
    private CpnTucSypNenkan2Mapper cpnTucSypNenkan2Mapper;
    @Autowired
    private KghTucKrkKikanMapper kghTucKrkKikanMapper;

    /** 業務共通用 */
    @Autowired
    private GyoumuComLogic gyoumuComLogic;

    /**
     * チェック
     * 
     * @param inDto GUI00939_年間・月間情報更新の入力DTO
     */
    @Override
    protected void checkProcess(final MonthlyYearlyTableUpdateServiceInDto inDto)
            throws Exception {
        // 共通部品権限チェック
        gyoumuComLogic.checkCommon(inDto, MENU3_KNJ);
    }

    /**
     * 
     * 年間・月間情報更新処理
     * 
     * @param inDto
     * @return
     * @throws Exception Exception
     */
    @Override
    protected MonthlyYearlyTableUpdateServiceOutDto mainProcess(MonthlyYearlyTableUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // DTOOUT情報
        MonthlyYearlyTableUpdateServiceOutDto outDto = new MonthlyYearlyTableUpdateServiceOutDto();

        // 3.削除処理の実施
        deleteInfo(inDto, outDto);
        // 4.登録更新処理の実施
        updateInfo(inDto, outDto);
        // 5. 電子保存
        gyoumuComLogic.print(getPrintInDto(inDto), MENU3_KNJ);
        // 6. 上記処理で取得した結果レスポンスを返却する。
        return outDto;
    }

    /**
     * パラメータを取得する。
     * 
     * @param inDto inデータ
     * @return パラメータ
     */
    private GyoumuComElectronicSaveInDto getPrintInDto(MonthlyYearlyTableUpdateServiceInDto inDto) {
        GyoumuComElectronicSaveInDto param = new GyoumuComElectronicSaveInDto();
        // ログイン職員ＩＤ
        param.setLoginShokuinId(inDto.getLoginShokuinId());
        // 操作区分
        param.setOperaFlg(inDto.getOperaFlg());
        // 施設ID
        param.setShisetuId(inDto.getShisetuId());
        // 計画期間ID
        param.setSc1Id(inDto.getSc1Id());
        // 利用者ID
        param.setUserId(inDto.getUserId());
        // 履歴ID
        param.setRirekiId(inDto.getRirekiId());
        // 事業所ID
        param.setSvJigyoId(inDto.getSvJigyoId());
        // 法人ID
        param.setHoujinId(inDto.getHoujinId());
        // 事業所CD
        param.setSvJigyoCd(inDto.getSvJigyoCd());
        // システム日付
        param.setSysYmd(inDto.getSysYmd());
        // サービス事業者名称
        param.setSvJigyoKnj(inDto.getSvJigyoKnj());
        // 初期設定マスタの情報
        GyoumuComElectronicSaveInitMasterData initMasterData = new GyoumuComElectronicSaveInitMasterData();
        if (inDto.getInitMasterObj() != null) {
            // ケアプラン方式
            initMasterData.setCpnFlg(inDto.getInitMasterObj().getCpnFlg());
            // パッケージプラン改訂フラグ
            initMasterData.setPkaiteiFlg(inDto.getInitMasterObj().getPkaiteiFlg());
            // 敬称オプション
            initMasterData.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
            // 敬称
            initMasterData.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
        }
        param.setInitMasterObj(initMasterData);
        return param;
    }

    /**
     * 削除処理
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @throws ExclusiveException
     */
    private void deleteInfo(MonthlyYearlyTableUpdateServiceInDto inDto,
            MonthlyYearlyTableUpdateServiceOutDto outDto) throws ExclusiveException {

        // 操作区分
        String operaFlg = inDto.getOperaFlg();
        // リクエストパラメータ.操作区分が「3:削除」以外の場合、「3.削除処理の実施」処理終了
        if (!OPER_FLAG_3.equals(operaFlg)) {
            return;
        }

        // 月間・年間表データの削除
        // deleteMonthlyYearlyInfo(inDto);
        // 月間・年間表ヘッダの削除
        deleteRirekiInfo(inDto);
    }

    /**
     * 「月間・年間表履歴」の削除処理
     * 
     * @param inDto inデータ
     * @throws ExclusiveException
     */
    private void deleteRirekiInfo(MonthlyYearlyTableUpdateServiceInDto inDto) throws ExclusiveException {
        // 保存用「月間・年間表履歴」情報.更新区分がD:削除の場合、下記の削除処理を行う
        // 更新回数更新
        int count;
        // BigInteger modifiedCnt = new
        // BigInteger(inDto.getRirekiObj().getModifiedCnt());
        // 削除フラグ更新
        final CpnTucSypNenkan1Criteria cpnTucSypNenkan1Criteria = new CpnTucSypNenkan1Criteria();
        cpnTucSypNenkan1Criteria.createCriteria()
                .andNenkan1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getRirekiObj().getRirekiId()));
        // .andModifiedCntEqualTo(
        // modifiedCnt)
        // .andDelFlgEqualTo(Constants.DELL_FLG_OFF);

        // 削除フラグ設定
        // final CpnTucSypNenkan1 cpnTucSypNenkan1 = new CpnTucSypNenkan1();

        // 削除（論理）時の共通カラム値設定処理
        // CommonDaoUtil.setDeleteCommonColumns(cpnTucSypNenkan1, modifiedCnt);

        // count =
        // this.cpnTucSypNenkan1Mapper.updateByCriteriaSelective(cpnTucSypNenkan1,
        // cpnTucSypNenkan1Criteria);
        count = this.cpnTucSypNenkan1Mapper.deleteByCriteria(cpnTucSypNenkan1Criteria);
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 月間・年間表データの削除
     * TODO
     * 
     * @param inDto inデータ
     * @throws ExclusiveException
     */
    // private void deleteMonthlyYearlyInfo(MonthlyYearlyTableUpdateServiceInDto
    // inDto) throws ExclusiveException {
    // // 保存用「月間・年間表履歴」情報.更新区分がD:削除の場合、下記の削除処理を行う
    // int count = 0;
    // List<MonthlyYearlyTableMonthlyDataDto> entityList = inDto.getDataList();
    // if (inDto.getDataList().size() > 0) {
    // for (MonthlyYearlyTableMonthlyDataDto entity : entityList) {
    // // 更新回数更新
    // // BigInteger modifiedCnt = new BigInteger(entity.getModifiedCnt());
    // // 削除フラグ更新
    // final CpnTucSypNenkan2Criteria cpnTucSypNenkan2Criteria = new
    // CpnTucSypNenkan2Criteria();
    // cpnTucSypNenkan2Criteria.createCriteria()
    // .andIdEqualTo(CommonDtoUtil
    // .strValToInt(entity.getId()));
    // // .andModifiedCntEqualTo(
    // // modifiedCnt)
    // // .andDelFlgEqualTo(Constants.DELL_FLG_OFF);

    // // 削除フラグ設定
    // // final CpnTucSypNenkan2 cpnTucSypNenkan2 = new CpnTucSypNenkan2();

    // // 削除（論理）時の共通カラム値設定処理
    // // CommonDaoUtil.setDeleteCommonColumns(cpnTucSypNenkan2, modifiedCnt);

    // // count =
    // // this.cpnTucSypNenkan2Mapper.updateByCriteriaSelective(cpnTucSypNenkan2,
    // // cpnTucSypNenkan2Criteria);
    // count =
    // this.cpnTucSypNenkan2Mapper.deleteByCriteria(cpnTucSypNenkan2Criteria);
    // if (count <= 0) {
    // throw new ExclusiveException();
    // }
    // }
    // }
    // }

    /**
     * 更新処理
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @throws ExclusiveException
     */
    private void updateInfo(MonthlyYearlyTableUpdateServiceInDto inDto,
            MonthlyYearlyTableUpdateServiceOutDto outDto) throws Exception {

        // 操作区分
        String operaFlg = inDto.getOperaFlg();
        // リクエストパラメータ.操作区分が「3:削除」の場合、「4.登録更新処理の実施」処理終了
        if (OPER_FLAG_3.equals(operaFlg)) {
            return;
        }
        // 計画対象期間の保存(計画対象期間IDが空白または0の場合)
        saveKikanInfo(inDto, outDto);

        // 履歴の保存
        saveRirekiInfo(inDto, outDto);

        // 月間・年間表データの保存
        saveMontylyYearlyInfo(inDto, outDto);
    }

    /**
     * 計画対象期間の保存
     * 
     * @param inDto inデータ
     */
    private void saveKikanInfo(MonthlyYearlyTableUpdateServiceInDto inDto,
            MonthlyYearlyTableUpdateServiceOutDto outDto) throws Exception {
        // リクエストパラメータ.計画期間ＩＤ
        String sc1Id = inDto.getSc1Id();
        // リクエストパラメータ.計画対象期間IDが空白または0の場合、下記の新規登録処理を行う
        if (CommonConstants.BLANK_STRING.equals(sc1Id) || CommonConstants.STR_0.equals(sc1Id)) {

            // 更新区分がC:新規の場合、登録のEntity設定
            final KghTucKrkKikan kghTucKrkKikan = new KghTucKrkKikan();
            // 法人ID
            kghTucKrkKikan.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
            // 施設ID
            kghTucKrkKikan.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            // 事業者ID
            kghTucKrkKikan.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 利用者ID
            kghTucKrkKikan.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
            // 種別ID
            kghTucKrkKikan.setSyubetsuId(CommonDtoUtil.strValToInt(inDto.getSyubetuId()));
            // 開始日
            kghTucKrkKikan.setStartYmd(inDto.getRirekiObj().getCreateYmd());
            // 終了日
            // kghTucKrkKikan.setEndYmd(inDto.getKikanObj().getEndYmd());

            // 削除（論理）時の共通カラム値設定処理
            // CommonDaoUtil.setInsertCommonColumns(kghTucKrkKikan);
            // kghTucKrkKikan.setSc1Id(numbering.getNumber(AppUtil.getKeiyakushaId(),
            // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.KGH_TUC_KRK_KIKAN_SC1_ID.getIntValue()));

            this.kghTucKrkKikanMapper.insertSelectiveAndReturn(kghTucKrkKikan);
            // 最新の期間ID設定
            outDto.setSc1IdNew(String.valueOf(kghTucKrkKikan.getSc1Id()));
        }
    }

    /**
     * 履歴の保存
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @throws ExclusiveException
     */
    private void saveRirekiInfo(MonthlyYearlyTableUpdateServiceInDto inDto,
            MonthlyYearlyTableUpdateServiceOutDto outDto) throws Exception {
        int count = 0;
        String sc1Id = inDto.getSc1Id();
        if (CommonConstants.BLANK_STRING.equals(sc1Id) || CommonConstants.STR_0.equals(sc1Id)) {
            sc1Id = outDto.getSc1IdNew();
        }
        // 保存用「月間・年間表履歴」情報.更新区分がC:新規の場合、下記の新規登録処理を行う
        if (CommonDtoUtil.isCreate(inDto.getRirekiObj())) {
            // 月間・年間表ヘッダ 新規
            // 新規のデータ設定
            final CpnTucSypNenkan1 cpnTucSypNenkan1 = new CpnTucSypNenkan1();
            // 計画期間ID
            cpnTucSypNenkan1.setSc1Id(CommonDtoUtil.strValToInt(sc1Id));
            // 法人ID
            cpnTucSypNenkan1.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
            // 施設ID
            cpnTucSypNenkan1.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            // 事業者ID
            cpnTucSypNenkan1.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 利用者ＩＤ
            cpnTucSypNenkan1.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
            // 作成日
            cpnTucSypNenkan1.setCreateYmd(inDto.getRirekiObj().getCreateYmd());
            // 作成者
            cpnTucSypNenkan1.setShokuId(CommonDtoUtil.strValToInt(inDto.getRirekiObj().getShokuId()));
            // ケースNo.
            cpnTucSypNenkan1.setCaseNo(inDto.getRirekiObj().getCaseNo());
            // 年度
            cpnTucSypNenkan1.setNendoY(inDto.getRirekiObj().getNendoY());
            // 年間行事の実施に関わる総合的支援の視点
            cpnTucSypNenkan1.setSogoShitenKnj(inDto.getRirekiObj().getSogoShitenKnj());
            // 改訂フラグ
            cpnTucSypNenkan1.setKaiteiFlg(CommonDtoUtil.strValToInt(inDto.getRirekiObj().getKaiteiFlg()));

            // 新規時の共通カラム値設定処理
            // CommonDaoUtil.setInsertCommonColumns(cpnTucSypNenkan1);
            // cpnTucSypNenkan1.setNenkan1Id(numbering.getNumber(AppUtil.getKeiyakushaId(),
            // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.CPN_TUC_SYP_NENKAN1_NENKAN1_ID.getIntValue()));

            count = this.cpnTucSypNenkan1Mapper.insertSelectiveAndReturn(cpnTucSypNenkan1);
            outDto.setRirekiIdNew(String.valueOf(cpnTucSypNenkan1.getNenkan1Id()));
        } else {
            // 月間・年間表ヘッダ 更新
            // 更新回数
            // BigInteger modifiedCnt = new
            // BigInteger(inDto.getRirekiObj().getModifiedCnt());
            // 更新のデータ設定
            final CpnTucSypNenkan1 cpnTucSypNenkan1 = new CpnTucSypNenkan1();

            final CpnTucSypNenkan1Criteria cpnTucSypNenkan1Criteria = new CpnTucSypNenkan1Criteria();
            cpnTucSypNenkan1Criteria.createCriteria()
                    .andNenkan1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getRirekiId()));
            // .andModifiedCntEqualTo(modifiedCnt);
            // 作成日
            cpnTucSypNenkan1.setCreateYmd(inDto.getRirekiObj().getCreateYmd());
            // 作成者
            // cpnTucSypNenkan1.setCreatedUser(inDto.getRirekiObj().getShokuId());
            cpnTucSypNenkan1.setShokuId(CommonDtoUtil.strValToInt(inDto.getRirekiObj().getShokuId()));
            // ケースNo.
            cpnTucSypNenkan1.setCaseNo(inDto.getRirekiObj().getCaseNo());
            // 年度
            cpnTucSypNenkan1.setNendoY(inDto.getRirekiObj().getNendoY());
            // 年間行事の実施に関わる総合的支援の視点
            cpnTucSypNenkan1.setSogoShitenKnj(inDto.getRirekiObj().getSogoShitenKnj());
            // 改訂フラグ
            cpnTucSypNenkan1.setKaiteiFlg(CommonDtoUtil.strValToInt(inDto.getRirekiObj().getKaiteiFlg()));

            // 更新時の共通カラム値設定処理
            // CommonDaoUtil.setUpdateCommonColumns(cpnTucSypNenkan1, modifiedCnt);

            count = this.cpnTucSypNenkan1Mapper.updateByCriteriaSelective(cpnTucSypNenkan1, cpnTucSypNenkan1Criteria);
            if (count <= 0) {
                throw new ExclusiveException();
            }
        }

    }

    /**
     * 月間・年間表データの保存
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @throws ExclusiveException
     */
    private void saveMontylyYearlyInfo(MonthlyYearlyTableUpdateServiceInDto inDto,
            MonthlyYearlyTableUpdateServiceOutDto outDto) throws Exception {
        int count = 0;

        // 履歴ＩＤ設定
        String rirekiId = inDto.getRirekiId();
        // リクエストパラメータ.履歴ＩＤが空白または0の場合、新規採番の月間年間計画ID（履歴ID）設定
        if (CommonConstants.BLANK_STRING.equals(rirekiId) || CommonConstants.STR_0.equals(rirekiId)) {
            rirekiId = outDto.getRirekiIdNew();
        }
        // 画面からの月間・年間表データ取得
        List<MonthlyYearlyTableMonthlyDataDto> entityList = inDto.getDataList();
        if (inDto.getDataList().size() > 0) {
            for (MonthlyYearlyTableMonthlyDataDto entity : entityList) {
                // 更新回数
                // BigInteger modifiedCnt = new BigInteger(entity.getModifiedCnt());
                // 更新区分の判断
                if (CommonDtoUtil.isCreate(entity)) {
                    // 保存用「月間情報」リスト.更新区分がC:新規の場合、下記の新規登録処理を行う

                    // 新規のEntity Value設定
                    final CpnTucSypNenkan2 cpnTucSypNenkan2 = new CpnTucSypNenkan2();

                    cpnTucSypNenkan2.setNenkan1Id(CommonDtoUtil.strValToInt(rirekiId));
                    cpnTucSypNenkan2.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
                    cpnTucSypNenkan2.setMm(CommonDtoUtil.strValToInt(entity.getMm()));
                    // 行事の内容
                    cpnTucSypNenkan2.setNaiyoKnj(entity.getNaiyoKnj());
                    // 実施計画上の位置付・目的
                    cpnTucSypNenkan2.setMokutekiKnj(entity.getMokutekiKnj());
                    cpnTucSypNenkan2.setSeq(CommonDtoUtil.strValToInt(entity.getSeq()));

                    // 更新時の共通カラム値設定処理
                    // CommonDaoUtil.setInsertCommonColumns(cpnTucSypNenkan2);
                    // cpnTucSypNenkan2.setId(numbering.getNumber(AppUtil.getKeiyakushaId(),
                    // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.CPN_TUC_SYP_NENKAN2_ID.getIntValue()));

                    this.cpnTucSypNenkan2Mapper.insertSelective(cpnTucSypNenkan2);
                } else if (CommonDtoUtil.isUpdate(entity)) {
                    // 保存用「月間情報」リスト.更新区分がU:更新の場合、下記の更新処理を行う

                    // 更新条件設定
                    final CpnTucSypNenkan2Criteria cpnTucSypNenkan2Criteria = new CpnTucSypNenkan2Criteria();
                    cpnTucSypNenkan2Criteria.createCriteria()
                            .andIdEqualTo(CommonDtoUtil.strValToInt(entity.getId())); // ID
                    // .andModifiedCntEqualTo(modifiedCnt); // 更新回数

                    // 更新Entity Value設定
                    final CpnTucSypNenkan2 cpnTucSypNenkan2 = new CpnTucSypNenkan2();
                    // 行事の内容
                    cpnTucSypNenkan2.setNaiyoKnj(entity.getNaiyoKnj());
                    // 実施計画上の位置付・目的
                    cpnTucSypNenkan2.setMokutekiKnj(entity.getMokutekiKnj());

                    // 更新時の共通カラム値設定処理
                    // CommonDaoUtil.setUpdateCommonColumns(cpnTucSypNenkan2, modifiedCnt);

                    count = this.cpnTucSypNenkan2Mapper.updateByCriteriaSelective(cpnTucSypNenkan2,
                            cpnTucSypNenkan2Criteria);
                    if (count <= 0) {
                        throw new ExclusiveException();
                    }
                } else if (CommonDtoUtil.isDelete(entity)) {
                    // 保存用「月間情報」リスト.更新区分がU:更新の場合、下記の更新処理を行う

                    // 更新条件（削除）設定
                    final CpnTucSypNenkan2Criteria cpnTucSypNenkan2Criteria = new CpnTucSypNenkan2Criteria();
                    cpnTucSypNenkan2Criteria.createCriteria()
                            .andIdEqualTo(CommonDtoUtil.strValToInt(entity.getId())); // ID
                    // .andModifiedCntEqualTo(modifiedCnt); // 更新回数

                    // 削除フラグ設定
                    // final CpnTucSypNenkan2 cpnTucSypNenkan2 = new CpnTucSypNenkan2();

                    // 削除（論理）時の共通カラム値設定処理
                    // CommonDaoUtil.setDeleteCommonColumns(cpnTucSypNenkan2, modifiedCnt);

                    // count =
                    // this.cpnTucSypNenkan2Mapper.updateByCriteriaSelective(cpnTucSypNenkan2,
                    // cpnTucSypNenkan2Criteria);
                    count = this.cpnTucSypNenkan2Mapper.deleteByCriteria(cpnTucSypNenkan2Criteria);
                    if (count <= 0) {
                        throw new ExclusiveException();
                    }
                }
            }
        }

    }
}
