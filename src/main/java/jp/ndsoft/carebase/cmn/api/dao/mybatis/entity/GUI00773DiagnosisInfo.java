package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GUI00773_アセスメント(インターライ)画面I-2
 * 
 * @description
 *              疾患（診断）情報リスト
 *              疾患（診断）情報リストエンティティ
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GUI00773DiagnosisInfo implements Serializable, IEntity {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** ID */
    private String id;

    /** 表示順 */
    private String sort;

    /** 診断名 */
    private String shindanKnj;

    /** 疾患コード */
    private String shikkanCd;

    /** ICD-CMコード1 */
    private String icdCmCode1;

    /** ICD-CMコード2 */
    private String icdCmCode2;

    /** 更新区分 */
    @NotEmpty
    private String diagnosisUpdateKbn;    

}
