package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentMasterUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentMasterUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkSsmMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsm;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsmCriteria;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.smh.framework.util.AppUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @since 2025.04.23
 * <AUTHOR>
 * @apiNote GUI00626_「アセスメントマスタ」画面の入力情報を保存する
 */
@Service
public class AssessmentMasterUpdateServiceImpl
        extends UpdateServiceImpl<AssessmentMasterUpdateServiceInDto, AssessmentMasterUpdateServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    /** 入力情報保存Mapper */
    @Autowired
    private KghMocKrkSsmMapper kghMocKrkSsmMapper;

    /**
     * 「アセスメントマスタ」画面の入力情報を保存する
     * 
     * @param inDto 初期設定マスタ情報取込入力DTO.
     * @return 初期設定マスタ取込初期情報出力DTO
     */
    @Override
    protected AssessmentMasterUpdateServiceOutDto mainProcess(AssessmentMasterUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);

        // 戻り情報を設定
        AssessmentMasterUpdateServiceOutDto outDto = new AssessmentMasterUpdateServiceOutDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. バリデーションチェックを行う===============
         * 
         */
        // なし

        /*
         * ===============3. 更新変数設定===============
         * 
         */
        // 3.1. Visioを更新する
        setInsertValue(CommonDtoUtil.strValToInt(inDto.getVisioValue()),
                CommonDtoUtil.strValToInt(inDto.getVisioBunrui3()),
                inDto.getVisioUpdateKbn(), inDto);
        // 3.2. 障害等の部位の摘要表示を更新する
        setInsertValue(CommonDtoUtil.strValToInt(inDto.getSyougaiValue()),
                CommonDtoUtil.strValToInt(inDto.getSyougaiBunrui3()),
                inDto.getSyougaiUpdateKbn(), inDto);
        // 3.3. 全体のまとめを更新する
        setInsertValue(CommonDtoUtil.strValToInt(inDto.getZenntaiValue()),
                CommonDtoUtil.strValToInt(inDto.getZenntaiBunrui3()),
                inDto.getZenntaiUpdateKbn(), inDto);

        /**
         * ===============5. レスポンスを返却する。===============
         */
        LOG.info(Constants.END);
        outDto.setUpdateSuccessFlag(true);
        return outDto;
    }

    /**
     * 更新変数設定
     * 
     * @param intValue    変数.整数
     * @param bunrui3Id   変数.分類３
     * @param modifiedCnt 変数.更新回数
     * @param updateKbn   変数.更新区分
     * @param inDto       入力情報保存サービス入力Dto
     * @throws ExclusiveException 排他制御エラー
     */
    private void setInsertValue(Integer intValue, Integer bunrui3Id,
            String updateKbn, AssessmentMasterUpdateServiceInDto inDto)
            throws ExclusiveException {
        // ■更新条件
        KghMocKrkSsmCriteria kghMocKrkSsmCriteria = new KghMocKrkSsmCriteria();
        // Input
        KghMocKrkSsm kghMocKrkSsm = new KghMocKrkSsm();
        // 変数.更新区分 ＝1:更新の場合、
        if (CommonDtoUtil.checkStringEqual(updateKbn, CommonConstants.UPDATE_KBN_1)) {
            // 「4.2. 更新処理」を行う
            kghMocKrkSsmCriteria.createCriteria()
                    .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()))
                    .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                    .andBunrui1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getBunrui1Id()))
                    .andBunrui2IdEqualTo(CommonDtoUtil.strValToInt(inDto.getBunrui2Id()))
                    .andBunrui3IdEqualTo(bunrui3Id);
            // 整数＝変数.整数
            kghMocKrkSsm.setIntValue(intValue);
            // time_stmp＝システム日付
            kghMocKrkSsm.setTimeStmp(AppUtil.getSystemTimeStamp());
        

            // DAOを実行
            int count = kghMocKrkSsmMapper.updateByCriteriaSelective(kghMocKrkSsm, kghMocKrkSsmCriteria);
            if (count <= CommonConstants.DATABASE_PROCESSING_FAILED) {
                // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
                throw new ExclusiveException();
            }
        }
        // 更新区分 ＝0:新規の場合、
        else if (CommonDtoUtil.checkStringEqual(updateKbn, CommonConstants.UPDATE_KBN_0)) {
            // 「4.1. 新規処理」を行う
            // 施設ID＝リクエストパラメータ.施設ID
            kghMocKrkSsm.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            // 事業者ID＝リクエストパラメータ.事業所ID
            kghMocKrkSsm.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 分類１＝リクエストパラメータ.分類１
            kghMocKrkSsm.setBunrui1Id(CommonDtoUtil.strValToInt(inDto.getBunrui1Id()));
            // 分類２＝リクエストパラメータ.分類２
            kghMocKrkSsm.setBunrui2Id(CommonDtoUtil.strValToInt(inDto.getBunrui2Id()));
            // 分類３＝変数.分類３
            kghMocKrkSsm.setBunrui3Id(bunrui3Id);
            // 整数＝変数.整数
            kghMocKrkSsm.setIntValue(intValue);
            // 小数点付＝0
            kghMocKrkSsm.setDoubleValue(CommonConstants.DOUBLE_ZERO);
            // 文字列１＝""
            kghMocKrkSsm.setText1Knj(CommonConstants.EMPTY_STRING);
            // タイムスタンプ＝システム日付
            kghMocKrkSsm.setTimeStmp(AppUtil.getSystemTimeStamp());

            kghMocKrkSsmMapper.insertSelective(kghMocKrkSsm);
        }
    }
}
