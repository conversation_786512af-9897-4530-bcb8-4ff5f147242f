package jp.ndsoft.carebase.cmn.api.logic;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794NewInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794NewInfoFukushiInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794NewInfoKaigoNinteiInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794NewInfoRyokuInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794NewInfoShishoInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794NewInfoTucNetaInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonHomeAssessmentAllChoPrt;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportHomeAssessmentAllPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportHomeAssessmentAllPrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeSaveServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeSaveServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.FreeAssessmentFacePrintSettingsUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KadaiInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.PrintSettingInfoDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.SmglYmdInDto;
import jp.ndsoft.carebase.cmn.api.report.model.EdocumentDeleteReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.HomeAssessmentSheetAllReportParameterModel;
import jp.ndsoft.carebase.cmn.api.util.CmnStorageServiceUtil;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDaoUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4FaceH21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Fam11H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Hou11H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kan11H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kan12H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kan13H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kan15H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kan16H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kio11H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4MedH21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4NeceH21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4SchdayH21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4SerH21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5FaceR3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Fam11R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Hou11R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kan11R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kan12R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kan13R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kan15R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kan16R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kio11R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5MedR3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5NeceR3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5SchdayR3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5SerR3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdlKadaiMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdlRirekiMapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghTucKrkKikanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4FaceH21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4FaceH21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Fam11H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Fam11H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Hou11H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Hou11H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan11H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan11H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan12H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan12H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan13H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan13H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan15H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan15H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan16H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan16H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kio11H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kio11H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4MedH21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4MedH21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4NeceH21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4NeceH21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4SchdayH21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4SchdayH21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4SerH21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4SerH21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5FaceR3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5FaceR3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Fam11R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Fam11R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Hou11R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Hou11R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan11R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan11R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan12R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan12R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan13R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan13R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan15R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan15R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan16R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan16R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kio11R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kio11R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5MedR3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5MedR3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5NeceR3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5NeceR3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5SchdayR3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5SchdayR3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5SerR3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5SerR3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlKadai;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlKadaiCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRireki;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRirekiCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTucKrkKikan;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucKaigoNinteiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucKaigoNinteiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucNetaByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucNetaOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucTechouInfoKindCdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucTechouInfoKindCdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4FaceH21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4FaceH21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Fam11H21SelSienTokki2KnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Fam11H21SelSienTokki2KnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Hou11H21SelSetsubi5ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Hou11H21SelSetsubi5OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Kan11H21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Kan11H21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Kan12H21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Kan12H21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Kan13H21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Kan13H21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Kan15H21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Kan15H21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Kan16H21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Kan16H21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Kio11H21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4Kio11H21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4MedH21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4MedH21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4NeceH21ListByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4NeceH21ListOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4SchdayH21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4SchdayH21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4SerH21ListByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4SerH21ListOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdlRirekiMinAsJisshiDateYmdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdlRirekiMinAsJisshiDateYmdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass10R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass10R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass11R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass11R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass12R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass12R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass13R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass13R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass1R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass1R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass2R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass2R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass3R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass3R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass4R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass4R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass5R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass5R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass6R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass6R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass7R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass7R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass8R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass8R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass9R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass9R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KadaiTourokuSc1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KadaiTourokuSc1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPrnPreSrw2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucKrkKikanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucKrkKikanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KyotakuRirekiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KyotakuRirekiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucKaigoNinteiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucNetaSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucTechouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4FaceH21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4Fam11H21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4Hou11H21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4Kan11H21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4Kan12H21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4Kan13H21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4Kan15H21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4Kan16H21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4Kio11H21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4MedH21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4NeceH21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4SchdayH21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4SerH21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5FaceR3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5Fam11R3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5Hou11R3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5Kan11R3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5Kan12R3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5Kan13R3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5Kan15R3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5Kan16R3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5Kio11R3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5MedR3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5NeceR3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5SchdayR3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5SerR3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdlKadaiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdlRirekiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucKrkKikanSelectMapper;
import jp.ndsoft.carebase.common.util.CareBaseMComSeqMgrCodeConstants;
import jp.ndsoft.smh.framework.services.util.Numbering;
import jp.ndsoft.smh.framework.util.AppUtil;
import jp.ndsoft.smh.framework.util.DateUtil;

/**
 * ［アセスメント］画面（居宅）画面のロジッククラス
 * 
 * <AUTHOR>
 */
@Component
public class AssessmentHomeLogic {
    /** 採番 */
    @Autowired
    private Numbering numbering;
    /** 【27-06 記録共通期間】情報を登録DAO */
    @Autowired
    private KghTucKrkKikanMapper kghTucKrkKikanMapper;

    /** 初回実施日を取得 */
    @Autowired
    private CpnTucGdlRirekiSelectMapper cpnTucGdlRirekiSelectMapper;

    /** 要介護状態マスタ情報を取得 */
    @Autowired
    private ComTucKaigoNinteiSelectMapper comTucKaigoNinteiSelectMapper;

    /** 寝たきり痴呆ランク（１－１３）情報を取得 */
    @Autowired
    private ComTucNetaSelectMapper comTucNetaSelectMapper;

    /** 身障、療育と精神障害福祉手帳情報を取得 */
    @Autowired
    private ComTucTechouSelectMapper comTucTechouSelectMapper;

    /** 課題と目標一覧情報を取得 */
    @Autowired
    private CpnTucGdlKadaiSelectMapper cpnTucGdlKadaiSelectMapper;

    /** 作成者情報を取得 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /** 計画期間情報を取得 */
    @Autowired
    private KghTucKrkKikanSelectMapper kghTucKrkKikanSelectMapper;

    /** 【ＧＬ＿居宅アセスメント履歴テーブル（cpn_tuc_gdl_rireki） */
    @Autowired
    private CpnTucGdlRirekiMapper cpnTucGdlRirekiMapper;
    // 補足_サブ情報（Ｈ２１改訂）取得
    /** フェースシート（H21/４改訂版） */
    @Autowired
    private CpnTucGdl4FaceH21SelectMapper cpnTucGdl4FaceH21SelectMapper;
    @Autowired
    private CpnTucGdl4FaceH21Mapper cpnTucGdl4FaceH21Mapper;
    /** 家族状況とインフォーマルな支援の状況（Ｈ２１改訂） */
    @Autowired
    private CpnTucGdl4Fam11H21SelectMapper cpnTucGdl4Fam11H21SelectMapper;
    @Autowired
    private CpnTucGdl4Fam11H21Mapper cpnTucGdl4Fam11H21Mapper;
    /** ＧＬ＿サービス利用状況（Ｈ２１改訂）一覧情報取得 */
    @Autowired
    private CpnTucGdl4SerH21SelectMapper cpnTucGdl4SerH21SelectMapper;
    @Autowired
    private CpnTucGdl4SerH21Mapper cpnTucGdl4SerH21Mapper;
    /** 住宅等の状況（Ｈ２１改訂） */
    @Autowired
    private CpnTucGdl4Hou11H21SelectMapper cpnTucGdl4Hou11H21SelectMapper;
    @Autowired
    private CpnTucGdl4Hou11H21Mapper cpnTucGdl4Hou11H21Mapper;
    /** ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）取得情報 */
    @Autowired
    private CpnTucGdl4Kio11H21SelectMapper cpnTucGdl4Kio11H21SelectMapper;
    @Autowired
    private CpnTucGdl4Kio11H21Mapper cpnTucGdl4Kio11H21Mapper;
    /** ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）情報取得 */
    @Autowired
    private CpnTucGdl4Kan11H21SelectMapper cpnTucGdl4Kan11H21SelectMapper;
    @Autowired
    private CpnTucGdl4Kan11H21Mapper cpnTucGdl4Kan11H21Mapper;
    /** ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）情報取得 */
    @Autowired
    private CpnTucGdl4Kan12H21SelectMapper cpnTucGdl4Kan12H21SelectMapper;
    @Autowired
    private CpnTucGdl4Kan12H21Mapper cpnTucGdl4Kan12H21Mapper;
    /** ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）情報取得 */
    @Autowired
    private CpnTucGdl4Kan13H21SelectMapper cpnTucGdl4Kan13H21SelectMapper;
    @Autowired
    private CpnTucGdl4Kan13H21Mapper cpnTucGdl4Kan13H21Mapper;

    /** ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）情報取得 */
    @Autowired
    private CpnTucGdl4Kan15H21SelectMapper cpnTucGdl4Kan15H21SelectMapper;
    @Autowired
    private CpnTucGdl4Kan15H21Mapper cpnTucGdl4Kan15H21Mapper;
    /** ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）情報取得 */
    @Autowired
    private CpnTucGdl4Kan16H21SelectMapper cpnTucGdl4Kan16H21SelectMapper;
    @Autowired
    private CpnTucGdl4Kan16H21Mapper cpnTucGdl4Kan16H21Mapper;
    /** ＧＬ＿介護に関する医師の意見（Ｈ２１改訂）取得情報 */
    @Autowired
    private CpnTucGdl4MedH21SelectMapper cpnTucGdl4MedH21SelectMapper;
    @Autowired
    private CpnTucGdl4MedH21Mapper cpnTucGdl4MedH21Mapper;
    /** ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）一覧情報取得 */
    @Autowired
    private CpnTucGdl4NeceH21SelectMapper cpnTucGdl4NeceH21SelectMapper;
    @Autowired
    private CpnTucGdl4NeceH21Mapper cpnTucGdl4NeceH21Mapper;
    /** ＧＬ＿１日のスケジュール（Ｈ２１改訂）取得情報 */
    @Autowired
    private CpnTucGdl4SchdayH21SelectMapper cpnTucGdl4SchdayH21SelectMapper;
    @Autowired
    private CpnTucGdl4SchdayH21Mapper cpnTucGdl4SchdayH21Mapper;
    // 補足_サブ情報（R３改訂）取得
    /** GL＿フェースシート（R3改訂）情報取得 */
    @Autowired
    private CpnTucGdl5FaceR3SelectMapper cpnTucGdl5FaceR3SelectMapper;
    @Autowired
    private CpnTucGdl5FaceR3Mapper cpnTucGdl5FaceR3Mapper;
    /** GL＿家族状況とインフォーマルな支援の状況（R3改訂）情報取得 */
    @Autowired
    private CpnTucGdl5Fam11R3SelectMapper cpnTucGdl5Fam11R3SelectMapper;
    @Autowired
    private CpnTucGdl5Fam11R3Mapper cpnTucGdl5Fam11R3Mapper;
    /** サービス利用状況（R３改訂） */
    @Autowired
    private CpnTucGdl5SerR3SelectMapper cpnTucGdl5SerR3SelectMapper;
    @Autowired
    private CpnTucGdl5SerR3Mapper cpnTucGdl5SerR3Mapper;
    /** GL_住宅等の状況（R3改訂）情報取得 */
    @Autowired
    private CpnTucGdl5Hou11R3SelectMapper cpnTucGdl5Hou11R3SelectMapper;
    @Autowired
    private CpnTucGdl5Hou11R3Mapper cpnTucGdl5Hou11R3Mapper;
    /** ＧＬ＿本人の健康状態・受診等の状況（Ｒ3改訂）情報取得 */
    @Autowired
    private CpnTucGdl5Kio11R3SelectMapper cpnTucGdl5Kio11R3SelectMapper;
    @Autowired
    private CpnTucGdl5Kio11R3Mapper cpnTucGdl5Kio11R3Mapper;
    /** ＧＬ＿①基本（身体機能・起居）動作（Ｒ3改訂）情報取得 */
    @Autowired
    private CpnTucGdl5Kan11R3SelectMapper cpnTucGdl5Kan11R3SelectMapper;
    @Autowired
    private CpnTucGdl5Kan11R3Mapper cpnTucGdl5Kan11R3Mapper;
    /** ＧＬ＿②生活機能（食事・排泄）（R3改訂） */
    @Autowired
    private CpnTucGdl5Kan12R3SelectMapper cpnTucGdl5Kan12R3SelectMapper;
    @Autowired
    private CpnTucGdl5Kan12R3Mapper cpnTucGdl5Kan12R3Mapper;
    /** ＧＬ＿③認知機能・④精神・行動障害（Ｒ3改訂）情報取得 */
    @Autowired
    private CpnTucGdl5Kan13R3SelectMapper cpnTucGdl5Kan13R3SelectMapper;
    @Autowired
    private CpnTucGdl5Kan13R3Mapper cpnTucGdl5Kan13R3Mapper;
    /** ＧＬ＿⑤社会生活（への適応）力（Ｒ３改訂）情報取得 */
    @Autowired
    private CpnTucGdl5Kan15R3SelectMapper cpnTucGdl5Kan15R3SelectMapper;
    @Autowired
    private CpnTucGdl5Kan15R3Mapper cpnTucGdl5Kan15R3Mapper;
    /** ＧＬ＿⑥医療・健康関係（Ｒ３改訂）情報取得 */
    @Autowired
    private CpnTucGdl5Kan16R3SelectMapper cpnTucGdl5Kan16R3SelectMapper;
    @Autowired
    private CpnTucGdl5Kan16R3Mapper cpnTucGdl5Kan16R3Mapper;
    /** ＧＬ＿介護に関する医師の意見（Ｒ3改訂）情報取得 */
    @Autowired
    private CpnTucGdl5MedR3SelectMapper cpnTucGdl5MedR3SelectMapper;
    @Autowired
    private CpnTucGdl5MedR3Mapper cpnTucGdl5MedR3Mapper;
    /** ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）一覧情報取得 */
    @Autowired
    private CpnTucGdl5NeceR3SelectMapper cpnTucGdl5NeceR3SelectMapper;
    @Autowired
    private CpnTucGdl5NeceR3Mapper cpnTucGdl5NeceR3Mapper;
    /** ＧＬ＿1日のスケジュール（Ｒ3改訂）情報取得 */
    @Autowired
    private CpnTucGdl5SchdayR3SelectMapper cpnTucGdl5SchdayR3SelectMapper;
    @Autowired
    private CpnTucGdl5SchdayR3Mapper cpnTucGdl5SchdayR3Mapper;
    /** ＧＬ＿課題と目標テーブル */
    @Autowired
    private CpnTucGdlKadaiMapper cpnTucGdlKadaiMapper;

    @Autowired
    private KghSmglHistoryLogic kghSmglHistoryLogic;

    @Autowired
    private KghKrkNokikanOpeLogic kghKrkNokikanOpeLogic;

    /** PrintSettingLogicクラス */
    @Autowired
    private PrintSettingLogic printSettingLogic;

    @SuppressWarnings("rawtypes")
    @Autowired
    private CmnStorageServiceUtil cmnStorageServiceUtil;

    /**
     * 「［アセスメント］画面（居宅）（1）」画面の新規表示情報を取得する。
     * newInfoSub
     * 
     * @param houjinId   法人ID
     * @param shisetuId  施設ID
     * @param svJigyoId  事業者ID
     * @param userid     利用者ID
     * @param kijunbiYmd 基準日
     * @return 新規表示情報
     */

    public Gui00794NewInfo newInfoSub(String houjinId,
            String shisetuId, String svJigyoId, String userid, String kijunbiYmd) {
        Gui00794NewInfo newInfoOut = new Gui00794NewInfo();
        // 2. 初回実施日を取得する。
        CpnTucGdlRirekiMinAsJisshiDateYmdByCriteriaInEntity cpnTucGdlRirekiMinAsJisshiDateYmdByIn = new CpnTucGdlRirekiMinAsJisshiDateYmdByCriteriaInEntity();
        // 法人ID
        cpnTucGdlRirekiMinAsJisshiDateYmdByIn.setHoujinId(houjinId);
        // 施設ID
        cpnTucGdlRirekiMinAsJisshiDateYmdByIn.setShisetuId(shisetuId);
        // 事業者ID
        cpnTucGdlRirekiMinAsJisshiDateYmdByIn.setSvJigyoId(svJigyoId);
        // 利用者ID
        cpnTucGdlRirekiMinAsJisshiDateYmdByIn.setUserid(userid);

        List<CpnTucGdlRirekiMinAsJisshiDateYmdOutEntity> cpnTucGdlRirekiMinAsJisshiDateYmdList = cpnTucGdlRirekiSelectMapper
                .findCpnTucGdlRirekiMinAsJisshiDateYmdByCriteria(
                        cpnTucGdlRirekiMinAsJisshiDateYmdByIn);
        if (!CollectionUtils.isNullOrEmpty(cpnTucGdlRirekiMinAsJisshiDateYmdList) &&
                cpnTucGdlRirekiMinAsJisshiDateYmdList.get(0) != null) {
            // 初回実施日を取得する
            newInfoOut.setMinAsJisshiDateYmd(
                    cpnTucGdlRirekiMinAsJisshiDateYmdList.get(0).getMinAsJisshiDateYmd());
        } else {
            // 2.2. 取得できない場合、リクエストパラメータ.基準日を返却する。
            newInfoOut.setMinAsJisshiDateYmd(kijunbiYmd);
        }

        // 3. 下記の介護認定情報取得のDAOを利用し、要介護認定情報を取得する
        ComTucKaigoNinteiByCriteriaInEntity comTucKaigoNinteiByCriteriaIn = new ComTucKaigoNinteiByCriteriaInEntity();
        // 利用者ID
        comTucKaigoNinteiByCriteriaIn.setAlUserid(CommonDtoUtil.strValToInt(userid));
        List<ComTucKaigoNinteiOutEntity> comTucKaigoNinteiOutList = this.comTucKaigoNinteiSelectMapper
                .findComTucKaigoNinteiByCriteria(comTucKaigoNinteiByCriteriaIn);
        // 要介護状態マスタ情報取得
        if (!CollectionUtils.isNullOrEmpty(comTucKaigoNinteiOutList)) {
            // 介護認定情報
            Gui00794NewInfoKaigoNinteiInfo kaigoNinteiInfo = new Gui00794NewInfoKaigoNinteiInfo();
            // 要介護状態区分
            kaigoNinteiInfo.setYokaiKbn(
                    CommonDtoUtil.objValToString(comTucKaigoNinteiOutList.get(0).getYokaiKbn()));
            // 認定年月日
            kaigoNinteiInfo.setNinteiYmd(comTucKaigoNinteiOutList.get(0).getNinteiYmd());
            // 介護認定情報
            newInfoOut.setKaigoNinteiInfo(kaigoNinteiInfo);
        }
        // 4. 下記の寝たきり痴呆ランク（１－１３）情報取得のDAOを利用し、寝たきり痴呆ランク情報を取得する
        ComTucNetaByCriteriaInEntity comTucNetaByCriteriaIn = new ComTucNetaByCriteriaInEntity();
        // 利用者ID
        comTucNetaByCriteriaIn.setUserId(CommonDtoUtil.strValToInt(userid));
        // 寝たきり痴呆ランク（１－１３）情報取得
        List<ComTucNetaOutEntity> comTucNetaOutList = comTucNetaSelectMapper
                .findComTucNetaByCriteria(comTucNetaByCriteriaIn);
        if (!CollectionUtils.isNullOrEmpty(comTucNetaOutList)) {
            // 寝たきり痴呆ランク情報
            Gui00794NewInfoTucNetaInfo tucNetaInfo = new Gui00794NewInfoTucNetaInfo();
            // 判定日
            tucNetaInfo.setChkYmd(comTucNetaOutList.get(0).getChkYmd());
            // 寝たきりランクコード
            tucNetaInfo.setNeta1Cd(CommonDtoUtil.objValToString(comTucNetaOutList.get(0).getNeta1Cd()));
            // 痴呆ランクコード
            tucNetaInfo.setNeta2Cd(CommonDtoUtil.objValToString(comTucNetaOutList.get(0).getNeta2Cd()));
            // OUT
            newInfoOut.setTucNetaInfo(tucNetaInfo);
        }
        // 5. 手帳情報取得する
        ComTucTechouInfoKindCdByCriteriaInEntity comTucTechouInfoKindCdByCriteriaIn = new ComTucTechouInfoKindCdByCriteriaInEntity();
        // 利用者ID
        comTucTechouInfoKindCdByCriteriaIn.setUserId(CommonDtoUtil.strValToInt(userid));
        // 交付年月日
        comTucTechouInfoKindCdByCriteriaIn.setDate(kijunbiYmd);
        // 身障、療育と精神障害福祉手帳情報取得
        List<ComTucTechouInfoKindCdOutEntity> comTucTechouInfoKindCdOutList = comTucTechouSelectMapper
                .findComTucTechouInfoKindCdByCriteria(comTucTechouInfoKindCdByCriteriaIn);
        if (!CollectionUtils.isNullOrEmpty(comTucTechouInfoKindCdOutList)) {
            // 身障手帳情報
            Gui00794NewInfoShishoInfo shishoInfo = new Gui00794NewInfoShishoInfo();
            // 療育手帳情報
            Gui00794NewInfoRyokuInfo ryokuInfo = new Gui00794NewInfoRyokuInfo();
            // 神障害福祉手帳情報
            Gui00794NewInfoFukushiInfo fukushiInfo = new Gui00794NewInfoFukushiInfo();
            Boolean TKBN_5_FLG = true;
            Boolean TKBN_2_FLG = true;
            Boolean TKBN_3_FLG = true;
            for (ComTucTechouInfoKindCdOutEntity itemOutEntity : comTucTechouInfoKindCdOutList) {
                // 5.2. 「5.1.」で取得した手帳情報リストから手帳区分が5（身障）の身障手帳情報を取得する
                if (CommonConstants.TKBN_5.equals(itemOutEntity.getTkbn()) && TKBN_5_FLG == true) {
                    // 身障手帳情報
                    // 一件目のFLG
                    TKBN_5_FLG = false;
                    // 交付年月日getYmd
                    shishoInfo.setGetYmd(itemOutEntity.getGetYmd());
                    // 等級
                    shishoInfo.setTTokyu(CommonDtoUtil.objValToString(itemOutEntity.getTTokyu()));
                    // 備考
                    shishoInfo.setBikoKnj(itemOutEntity.getBikoKnj());
                    // OUT
                    newInfoOut.setShishoInfo(shishoInfo);
                }
                // 5.3. 「5.1.」で取得した手帳情報リストから手帳区分が2（療育）の療育手帳情報を取得する
                if (CommonConstants.TKBN_2.equals(itemOutEntity.getTkbn()) && TKBN_2_FLG == true) {
                    // 療育手帳情報
                    // 一件目のFLG
                    TKBN_2_FLG = false;
                    // 交付年月日getYmd
                    ryokuInfo.setGetYmd(itemOutEntity.getGetYmd());
                    // 等級
                    ryokuInfo.setTTokyu(CommonDtoUtil.objValToString(itemOutEntity.getTTokyu()));
                    // 合併障害
                    ryokuInfo.setGappeiShougaiKnj(itemOutEntity.getGappeiShougaiKnj());
                    // OUT
                    newInfoOut.setRyokuInfo(ryokuInfo);
                }
                // 5.4. 「5.1.」で取得した手帳情報リストから手帳区分が3（精神障害福祉）の精神障害福祉手帳情報を取得する
                if (CommonConstants.TKBN_3.equals(itemOutEntity.getTkbn()) && TKBN_3_FLG == true) {
                    // 神障害福祉手帳情報
                    // 一件目のFLG
                    TKBN_3_FLG = false;
                    // 交付年月日getYmd
                    fukushiInfo.setGetYmd(itemOutEntity.getGetYmd());
                    // 等級
                    fukushiInfo.setTTokyu(CommonDtoUtil.objValToString(itemOutEntity.getTTokyu()));
                    // 備考
                    fukushiInfo.setBikoKnj(itemOutEntity.getBikoKnj());
                    // OUT
                    newInfoOut.setFukushiInfo(fukushiInfo);
                }
            }
        }
        return newInfoOut;
    }

    /**
     * 計画期間情報を取得
     * 
     * @param svJigyoId  事業者ID
     * @param userId     利用者ID
     * @param syubetsuId 種別ID
     * @param shisetuId  施設ID
     * @return 計画期間情報取得
     */
    public List<KghTucKrkKikanOutEntity> findKghTucKrkKikan(Integer svJigyoId, Integer userId, Integer syubetsuId,
            Integer shisetuId) {

        // 2.1. 下記の記録共通期間取得のDAOを利用し、計画期間情報を取得する。
        KghTucKrkKikanByCriteriaInEntity kghTucKrkKikanByCriteriain = new KghTucKrkKikanByCriteriaInEntity();

        // 事業者ID
        kghTucKrkKikanByCriteriain.setJId(svJigyoId);
        // 利用者ID
        kghTucKrkKikanByCriteriain.setUId(userId);
        // 種別ID
        kghTucKrkKikanByCriteriain.setSyubetsuId(syubetsuId);
        // 施設ID
        kghTucKrkKikanByCriteriain.setSId(shisetuId);

        List<KghTucKrkKikanOutEntity> kghTucKrkKikanList = kghTucKrkKikanSelectMapper
                .findKghTucKrkKikanByCriteria(kghTucKrkKikanByCriteriain);
        return kghTucKrkKikanList;
    }

    /**
     * 居宅アセスメント履歴情報を取得
     * 
     * @param sc1Id     計画期間ID
     * @param svJigyoId 事業者ID
     * @param userId    利用者ID
     * @return 居宅アセスメント履歴情報を取得
     */
    public List<KyotakuRirekiOutEntity> findKyotakuRireki(Integer sc1Id, Integer svJigyoId, Integer userId) {
        KyotakuRirekiByCriteriaInEntity inEntity = new KyotakuRirekiByCriteriaInEntity();
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);
        // 事業者ID
        inEntity.setJId(svJigyoId);
        // 利用者ID
        inEntity.setUId(userId);
        List<KyotakuRirekiOutEntity> outEntityList = cpnTucGdlRirekiSelectMapper
                .findKyotakuRirekiByCriteria(inEntity);
        return outEntityList;
    }

    /**
     * 作成者情報を取得
     * 
     * @param chkShokuId 職員ID
     * @return 作成者情報を取得
     */
    public String findKghCpnRaiMonKentPrnPreSrw2(Integer chkShokuId) {
        KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity inEntity = new KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity();
        String shokuinName = "";
        // 職員ID
        inEntity.setLlTmp(CommonDtoUtil.objValToString(chkShokuId));
        List<KghCpnRaiMonKentPrnPreSrw2OutEntity> outEntityList = comMscShokuinSelectMapper
                .findKghCpnRaiMonKentPrnPreSrw2ByCriteria(inEntity);
        if (outEntityList != null && outEntityList.size() > 0) {
            shokuinName = outEntityList.get(0).getShokuinName();
        }
        return shokuinName;
    }

    /**
     * 課題と目標情報取得
     * 
     * @param sc1Id 計画期間ID
     * @param gdlId アセスメントID
     * @return 課題と目標情報リスト
     */
    public List<KadaiTourokuSc1OutEntity> findKadaiTourokuSc1(Integer sc1Id, Integer gdlId) {
        KadaiTourokuSc1ByCriteriaInEntity inEntity = new KadaiTourokuSc1ByCriteriaInEntity();
        inEntity.setGdl(gdlId);
        inEntity.setSc1(sc1Id);
        List<KadaiTourokuSc1OutEntity> outEntityList = cpnTucGdlKadaiSelectMapper
                .findKadaiTourokuSc1ByCriteria(inEntity);
        return outEntityList;
    }

    /**
     * 「アセスメント番号」と「アセスメント名称」
     * 
     * @param assNo アセスメント番号
     * @return アセスメント名称
     */
    public String assNoToAssName(Integer assNo) {
        HashMap<Integer, String> assNoMap = new HashMap<>();
        assNoMap.put(1, "１~r~nﾌｪｰｽｼｰﾄ");
        assNoMap.put(2, "２~r~n家族と支援");
        assNoMap.put(3, "３~r~nｻｰﾋﾞｽ利用");
        assNoMap.put(4, "４~r~n住居");
        assNoMap.put(5, "５~r~n健康状態");
        assNoMap.put(6, "６-①~r~n基本動作");
        assNoMap.put(7, "６-②~r~n生活機能");
        assNoMap.put(8, "６-③④~r~n認知機能~r~n精神・行動");
        assNoMap.put(9, "６-⑤~r~n社会生活力");
        assNoMap.put(10, "６-⑥~r~n医療・健康");
        assNoMap.put(11, "６~r~n医師の意見");
        assNoMap.put(12, "７~r~n全体のまとめ");
        assNoMap.put(13, "７~r~nスケジュール");
        return assNoMap.get(assNo);
    }

    /**
     * タブIDと帳票番号
     * 
     * @param tabId タブID
     * @return 帳票番号
     */
    public String getIndex(String tabId) {
        HashMap<String, String> tabIdMap = new HashMap<>();
        tabIdMap.put("1", "1");
        tabIdMap.put("2", "2");
        tabIdMap.put("3", "3");
        tabIdMap.put("4", "3");
        tabIdMap.put("5", "4");
        tabIdMap.put("6", "5");
        tabIdMap.put("7", "6");
        tabIdMap.put("8", "7");
        tabIdMap.put("9", "8");
        tabIdMap.put("10", "9");
        tabIdMap.put("11", "9");
        tabIdMap.put("12", "10");
        tabIdMap.put("13", "11");
        return tabIdMap.get(tabId);
    }

    /**
     * 計画対象期間の保存処理
     * 関数名：insertKikan
     *
     * @param inDto 登録パラメータ
     * @return 結果ステータス
     */
    public AssessmentHomeSaveServiceOutDto insertKikan(AssessmentHomeSaveServiceInDto inDto)
            throws Exception {
        KghTucKrkKikan kghTucKrkKikan = new KghTucKrkKikan();
        // 法人ID
        kghTucKrkKikan.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        kghTucKrkKikan.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        kghTucKrkKikan.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        kghTucKrkKikan.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 種別ID
        kghTucKrkKikan.setSyubetsuId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 開始日
        kghTucKrkKikan.setStartYmd(inDto.getKijunbiYmd());

        // DAOを実行
        Integer count = kghTucKrkKikanMapper.insertSelectiveAndReturn(kghTucKrkKikan);

        // 戻り情報を設定
        AssessmentHomeSaveServiceOutDto outDto = new AssessmentHomeSaveServiceOutDto();
        // ステータス
        // 更新成功の場合、「1」を設定する
        // 更新失敗の場合、「0」を設定する
        if (count > 0) {
            outDto.setStatus(CommonConstants.SUCCESS);
            // 採番した期間IDをInDtoに設定する
            inDto.setSc1Id(String.valueOf(kghTucKrkKikan.getSc1Id()));
        } else {
            outDto.setStatus(CommonConstants.FAILURE);
        }
        return outDto;
    }

    /**
     * ＧＬ＿居宅アセスメント履歴情報を削除する。
     * 関数名：deleteCpnTucGdlRireki
     *
     * @param inDto リクエストパラメータ
     * @param sc1Id 期間ID
     * @return 更新件数
     */
    public Integer deleteCpnTucGdlRireki(AssessmentHomeSaveServiceInDto inDto,
            Integer sc1Id) {

        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 事業者ID＝リクエストパラメータ.事業者ID
        // 利用者ＩＤ＝リクエストパラメータ.利用者ＩＤ
        // 法人ID＝リクエストパラメータ.法人ID
        // 施設ID＝リクエストパラメータ.施設ID

        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();

        criteria.createCriteria()
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                .andSc1IdEqualTo(sc1Id)
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        // DAOを実行
        int cont = cpnTucGdlRirekiMapper.deleteByCriteria(criteria);

        return cont;
    }

    /**
     * 居宅アセスメント履歴を更新する。
     * 関数名：homeLogicsyuri
     *
     * @param inDto リクエストパラメータ
     * @param sc1Id 期間ID
     */
    public String homeLogicsyuri(AssessmentHomeSaveServiceInDto inDto, Integer sc1Id) {
        Integer gdlId;
        Integer cont;
        // ステータス
        String status = CommonConstants.SUCCESS;

        gdlId = CommonDtoUtil.strValToInt(inDto.getGdlId());

        // 3.1.居宅アセスメント履歴を更新する
        cont = deleteCpnTucGdlRireki(inDto, sc1Id);
        if (cont <= 0) {
            return CommonConstants.FAILURE;

        }

        if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
            // 3.2.1.リクエストパラメータ.改定フラグが4（H21/4改訂版）の場合
            // 3.2.1.1. 上記対象テーブル一覧に記載された対象テーブルの更新回数を取得する。
            // 3.2.1.2. 上記対象テーブル一覧に記載された対象テーブルを更新する。

            // 1ＧＬ＿フェースシート（Ｈ２１改訂）
            CpnTucGdl4FaceH21OutEntity out01H21 = findCpnTucGdl4FaceH21(gdlId, sc1Id);
            if (out01H21 != null) {
                cont = deleteCpnTucGdl4FaceH21(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }

            // 2ＧＬ＿家族状況とインフォーマルな支援の状況（Ｈ２１改訂）
            CpnTucGdl4Fam11H21SelSienTokki2KnjOutEntity out02H21 = findCpnTucGdl4Fam11H21SelSienTokki2Knj(
                    gdlId, sc1Id);
            if (out02H21 != null) {
                cont = deleteCpnTucGdl4Fam11H21(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 3ＧＬ＿サービス利用状況（Ｈ２１改訂）
            CpnTucGdl4SerH21ListOutEntity out03H21 = findCpnTucGdl4SerH21List(gdlId,
                    sc1Id);
            if (out03H21 != null) {
                cont = deleteCpnTucGdl4SerH21(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 4ＧＬ＿住宅等の状況（Ｈ２１改訂）
            CpnTucGdl4Hou11H21SelSetsubi5OutEntity out04H21 = findCpnTucGdl4Hou11H21SelSetsubi5(
                    gdlId, sc1Id);
            if (out04H21 != null) {
                cont = deleteCpnTucGdl4Hou11H21SelSetsubi5(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 5ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）
            CpnTucGdl4Kio11H21OutEntity out05H21 = findCpnTucGdl4Kio11H21(gdlId, sc1Id);
            if (out05H21 != null) {
                cont = deleteCpnTucGdl4Kio11H21(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 6ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）
            CpnTucGdl4Kan11H21OutEntity out06H21 = findCpnTucGdl4Kan11H21(gdlId, sc1Id);
            if (out06H21 != null) {
                cont = deleteCpnTucGdl4Kan11H21(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 7ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）
            CpnTucGdl4Kan12H21OutEntity out07H21 = findCpnTucGdl4Kan12H21(gdlId, sc1Id);
            if (out07H21 != null) {
                cont = deleteCpnTucGdl4Kan12H21(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 8ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）
            CpnTucGdl4Kan13H21OutEntity out08H21 = findCpnTucGdl4Kan13H21(gdlId, sc1Id);
            if (out08H21 != null) {
                cont = deleteCpnTucGdl4Kan13H21(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 9ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）
            CpnTucGdl4Kan15H21OutEntity out09H21 = findCpnTucGdl4Kan15H21(gdlId, sc1Id);
            if (out09H21 != null) {
                cont = deleteCpnTucGdl4Kan15H21(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 10ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）
            CpnTucGdl4Kan16H21OutEntity out10H21 = findCpnTucGdl4Kan16H21(gdlId, sc1Id);
            if (out10H21 != null) {
                cont = deleteCpnTucGdl4Kan16H21(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 11ＧＬ＿介護に関する医師の意見（Ｈ２１改訂）
            CpnTucGdl4MedH21OutEntity out11H21 = findCpnTucGdl4MedH21(gdlId, sc1Id);
            if (out11H21 != null) {
                cont = deleteCpnTucGdl4MedH21(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 12ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）
            // 3.2.1.3. 上記で取得したＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リストの件数分、【ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）】を更新する。
            List<CpnTucGdl4NeceH21ListOutEntity> out12H21List = findCpnTucGdl4NeceH21(gdlId, sc1Id);
            if (out12H21List != null && out12H21List.size() > 0) {
                for (CpnTucGdl4NeceH21ListOutEntity out12H21 : out12H21List) {
                    // アセスメントID gdlId
                    // 計画期間ID sc1Id

                    cont = deleteCpnTucGdl4NeceH21(gdlId, sc1Id, out12H21.getMastId(),
                            out12H21.getHoujinId(),
                            out12H21.getShisetuId(), out12H21.getSvJigyoId(),
                            out12H21.getUserId());
                    if (cont <= 0) {
                        return CommonConstants.FAILURE;
                    }
                }
            }
            // 13ＧＬ＿１日のスケジュール（Ｈ２１改訂）
            List<CpnTucGdl4SchdayH21OutEntity> out13H21List = findCpnTucGdl4SchdayH21(gdlId, sc1Id);
            if (out13H21List != null && out13H21List.size() > 0) {
                for (CpnTucGdl4SchdayH21OutEntity out13H21 : out13H21List) {
                    cont = deleteCpnTucGdl4SchdayH21(gdlId, sc1Id, out13H21.getSeq());
                    if (cont <= 0) {
                        return CommonConstants.FAILURE;
                    }
                }
            }

        } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
            // 3.2.2.リクエストパラメータ.改定フラグが5（R3/4改訂版）の場合
            // 3.2.2.1. 上記対象テーブル一覧に記載された対象テーブルの更新回数を取得する。
            // 3.2.2.2. 上記対象テーブル一覧に記載された対象テーブルを更新する。
            // 1ＧＬ＿フェースシート（R３改訂）
            Gdl5Ass1R3OutEntity out1R3 = findGdl5Ass1R3(gdlId, sc1Id);
            if (out1R3 != null) {
                cont = deleteGdl5Ass1R3(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 2ＧＬ＿家族状況とインフォーマルな支援の状況（R３改訂）
            Gdl5Ass2R3OutEntity out2R3 = findGdl5Ass2R3(gdlId, sc1Id);
            if (out2R3 != null) {
                cont = deleteGdl5Ass2R3(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 3ＧＬ＿サービス利用状況（R３改訂）
            Gdl5Ass3R3OutEntity out3R3 = findGdl5Ass3R3(gdlId, sc1Id);
            if (out3R3 != null) {
                cont = deleteGdl5Ass3R3(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 4ＧＬ＿住宅等の状況（R３改訂）
            Gdl5Ass4R3OutEntity out4R3 = findGdl5Ass4R3(gdlId, sc1Id);
            if (out4R3 != null) {
                cont = deleteGdl5Ass4R3(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 5ＧＬ＿本人の健康状態・受診等の状況（R３改訂）
            Gdl5Ass5R3OutEntity out5R3 = findGdl5Ass5R3(gdlId, sc1Id);
            if (out5R3 != null) {
                cont = deleteGdl5Ass5R3(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 6ＧＬ＿①基本（身体機能・起居）動作（R３改訂）
            Gdl5Ass6R3OutEntity out6R3 = findGdl5Ass6R3(gdlId, sc1Id);
            if (out6R3 != null) {
                cont = deleteGdl5Ass6R3(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 7ＧＬ＿②生活機能（食事・排泄）（R３改訂）

            Gdl5Ass7R3OutEntity out7R3 = findGdl5Ass7R3(gdlId, sc1Id);
            if (out7R3 != null) {
                cont = deleteGdl5Ass7R3(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 8ＧＬ＿③認知機能・④精神・行動障害（R３改訂）
            Gdl5Ass8R3OutEntity out8R3 = findGdl5Ass8R3(gdlId, sc1Id);
            if (out8R3 != null) {
                cont = deleteGdl5Ass8R3(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 9ＧＬ＿⑤社会生活（への適応）力（R３改訂）
            Gdl5Ass9R3OutEntity out9R3 = findGdl5Ass9R3(gdlId, sc1Id);
            if (out9R3 != null) {
                cont = deleteGdl5Ass9R3(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 10ＧＬ＿⑥医療・健康関係（R３改訂）
            Gdl5Ass10R3OutEntity out10R3 = findGdl5Ass10R3(gdlId, sc1Id);
            if (out10R3 != null) {
                cont = deleteGdl5Ass10R3(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 11ＧＬ＿介護に関する医師の意見（R３改訂）
            Gdl5Ass11R3OutEntity out11R3 = findGdl5Ass11R3(gdlId, sc1Id);
            if (out11R3 != null) {
                cont = deleteGdl5Ass11R3(gdlId, sc1Id);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
            // 12ＧＬ＿全体のまとめ・特記事項（R３改訂）
            List<Gdl5Ass12R3OutEntity> out12R3List = findGdl5Ass12R3(gdlId, sc1Id);
            if (out12R3List != null && out12R3List.size() > 0) {
                for (Gdl5Ass12R3OutEntity out12R3 : out12R3List) {
                    cont = deleteGdl5Ass12R3(gdlId, sc1Id, out12R3.getMastId(),
                            out12R3.getHoujinId(),
                            out12R3.getShisetuId(), out12R3.getSvJigyoId(),
                            out12R3.getUserId());
                    if (cont <= 0) {
                        return CommonConstants.FAILURE;
                    }
                }
            }
            // 13ＧＬ＿１日のスケジュール（R３改訂）
            List<Gdl5Ass13R3OutEntity> out13R3List = findGdl5Ass13R3(gdlId, sc1Id);
            if (out13R3List != null && out13R3List.size() > 0) {
                for (Gdl5Ass13R3OutEntity out13R3 : out13R3List) {
                    cont = deleteGdl5Ass13R3(gdlId, sc1Id, out13R3.getSeq());
                    if (cont <= 0) {
                        return CommonConstants.FAILURE;
                    }
                }
            }
        }
        // 3.3. リクエストパラメータ.【課題と目標リスト】の件数分、【ＧＬ＿課題と目標】情報を更新する
        // 課題と目標情報リスト
        if (inDto.getKadaiList() != null) {
            for (KadaiInDto kadaiInDto : inDto.getKadaiList()) {
                // アセスメントID=リクエストパラメータ.アセスメントID
                // 計画期間ID＝変数.計画対象期間ID
                // リクエストパラメータ.【課題と目標リスト】
                cont = deleteCpnTucGdlKadai(gdlId, sc1Id, kadaiInDto);
                if (cont <= 0) {
                    return CommonConstants.FAILURE;
                }
            }
        }
        return status;
    }

    /**
     * 【ＧＬ＿課題と目標】情報を保存する。。
     * 関数名：homeLogicsyuri
     *
     * @param inDto リクエストパラメータ
     * @param gdlId アセスメントID
     * @param sc1Id 期間ID
     * @throws Exception
     */
    public String homeLogicCpnTucGdlKadai(AssessmentHomeSaveServiceInDto inDto,
            Integer sc1Id) throws Exception {
        Integer gdlId;
        Integer cont;
        // ステータス
        String status = CommonConstants.SUCCESS;

        gdlId = CommonDtoUtil.strValToInt(inDto.getGdlId());

        // 5.3.リクエストパラメータ.【課題と目標リスト】の件数分、【ＧＬ＿課題と目標】情報を保存する。
        if (inDto.getKadaiList() != null)

        {
            for (KadaiInDto kadaiInDto : inDto.getKadaiList()) {
                if ((kadaiInDto.getId() != null && !kadaiInDto.getId().isEmpty())
                        && (CommonDtoUtil.isDelete(kadaiInDto))) {
                    // 5.3.1.リクエストパラメータ.【課題と目標リスト】.idが空白ではなく、リクエストパラメータ.【課題と目標リスト】.更新区分が"D":削除の場合、【ＧＬ＿課題と目標】情報を更新する。

                    // アセスメントID=リクエストパラメータ.アセスメントID
                    // 計画期間ID＝変数.計画対象期間ID
                    // リクエストパラメータ.【課題と目標リスト】
                    cont = deleteCpnTucGdlKadai(CommonDtoUtil.strValToInt(inDto.getGdlId()),
                            CommonDtoUtil.strValToInt(inDto.getSc1Id()), kadaiInDto);
                    if (cont <= 0) {
                        status = CommonConstants.FAILURE;
                    }
                }
                if (CommonDtoUtil.isCreate(kadaiInDto)) {
                    // 5.3.2.リクエストパラメータ.【課題と目標リスト】.更新区分が"C":登録の場合、【ＧＬ＿課題と目標】情報を登録する。
                    cont = insertCpnTucGdlKadai(gdlId, sc1Id, kadaiInDto);
                    if (cont <= 0) {
                        status = CommonConstants.FAILURE;
                    }
                }
                if (CommonDtoUtil.isUpdate(kadaiInDto)) {
                    // 5.3.3.リクエストパラメータ.【課題と目標リスト】.更新区分が"U":更新の場合、【ＧＬ＿課題と目標】情報を更新する。
                    cont = updateCpnTucGdlKadai(gdlId, sc1Id, kadaiInDto);
                    if (cont <= 0) {
                        status = CommonConstants.FAILURE;
                    }
                }
            }
        }
        return status;
    }

    /**
     * フェースシート（H21/４改訂版）情報を取得する。
     * 関数名：findCpnTucGdl4FaceH21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return フェースシート（H21/４改訂版）
     */
    private CpnTucGdl4FaceH21OutEntity findCpnTucGdl4FaceH21(Integer gdlId,
            Integer sc1Id) {
        CpnTucGdl4FaceH21ByCriteriaInEntity inEntity = new CpnTucGdl4FaceH21ByCriteriaInEntity();
        CpnTucGdl4FaceH21OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<CpnTucGdl4FaceH21OutEntity> OutList = cpnTucGdl4FaceH21SelectMapper
                .findCpnTucGdl4FaceH21ByCriteria(inEntity);
        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * 家族状況とインフォーマルな支援の状況（Ｈ２１改訂）情報を取得する。
     * 関数名：findCpnTucGdl4Fam11H21SelSienTokki2Knj
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 家族状況とインフォーマルな支援の状況（Ｈ２１改訂）
     */
    private CpnTucGdl4Fam11H21SelSienTokki2KnjOutEntity findCpnTucGdl4Fam11H21SelSienTokki2Knj(Integer gdlId,
            Integer sc1Id) {
        CpnTucGdl4Fam11H21SelSienTokki2KnjByCriteriaInEntity inEntity = new CpnTucGdl4Fam11H21SelSienTokki2KnjByCriteriaInEntity();
        CpnTucGdl4Fam11H21SelSienTokki2KnjOutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<CpnTucGdl4Fam11H21SelSienTokki2KnjOutEntity> OutList = cpnTucGdl4Fam11H21SelectMapper
                .findCpnTucGdl4Fam11H21SelSienTokki2KnjByCriteria(inEntity);
        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿サービス利用状況（Ｈ２１改訂）一覧情を取得する。
     * 関数名：findCpnTucGdl4SerH21List
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿サービス利用状況（Ｈ２１改訂）一覧情
     */
    private CpnTucGdl4SerH21ListOutEntity findCpnTucGdl4SerH21List(Integer gdlId,
            Integer sc1Id) {
        CpnTucGdl4SerH21ListByCriteriaInEntity inEntity = new CpnTucGdl4SerH21ListByCriteriaInEntity();
        CpnTucGdl4SerH21ListOutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSclId(sc1Id);

        List<CpnTucGdl4SerH21ListOutEntity> OutList = cpnTucGdl4SerH21SelectMapper
                .findCpnTucGdl4SerH21ListByCriteria(inEntity);
        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿サービス利用状況（Ｈ２１改訂）情報を取得する。
     * 関数名：findCpnTucGdl4Hou11H21SelSetsubi5
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 住宅等の状況（Ｈ２１改訂）
     */
    private CpnTucGdl4Hou11H21SelSetsubi5OutEntity findCpnTucGdl4Hou11H21SelSetsubi5(Integer gdlId, Integer sc1Id) {
        CpnTucGdl4Hou11H21SelSetsubi5ByCriteriaInEntity inEntity = new CpnTucGdl4Hou11H21SelSetsubi5ByCriteriaInEntity();
        CpnTucGdl4Hou11H21SelSetsubi5OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setHh(sc1Id);

        List<CpnTucGdl4Hou11H21SelSetsubi5OutEntity> OutList = cpnTucGdl4Hou11H21SelectMapper
                .findCpnTucGdl4Hou11H21SelSetsubi5ByCriteria(inEntity);
        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）情報を取得する。
     * 関数名：findCpnTucGdl4Kio11H21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）
     */
    private CpnTucGdl4Kio11H21OutEntity findCpnTucGdl4Kio11H21(Integer gdlId,
            Integer sc1Id) {
        CpnTucGdl4Kio11H21ByCriteriaInEntity inEntity = new CpnTucGdl4Kio11H21ByCriteriaInEntity();
        CpnTucGdl4Kio11H21OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<CpnTucGdl4Kio11H21OutEntity> OutList = cpnTucGdl4Kio11H21SelectMapper
                .findCpnTucGdl4Kio11H21ByCriteria(inEntity);
        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）情報を取得する。
     * 関数名：findCpnTucGdl4Kan11H21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）
     */
    private CpnTucGdl4Kan11H21OutEntity findCpnTucGdl4Kan11H21(Integer gdlId,
            Integer sc1Id) {
        CpnTucGdl4Kan11H21ByCriteriaInEntity inEntity = new CpnTucGdl4Kan11H21ByCriteriaInEntity();
        CpnTucGdl4Kan11H21OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<CpnTucGdl4Kan11H21OutEntity> OutList = cpnTucGdl4Kan11H21SelectMapper
                .findCpnTucGdl4Kan11H21ByCriteria(inEntity);
        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）情報を取得する。
     * 関数名：findCpnTucGdl4Kan12H21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）
     */
    private CpnTucGdl4Kan12H21OutEntity findCpnTucGdl4Kan12H21(Integer gdlId,
            Integer sc1Id) {
        CpnTucGdl4Kan12H21ByCriteriaInEntity inEntity = new CpnTucGdl4Kan12H21ByCriteriaInEntity();
        CpnTucGdl4Kan12H21OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<CpnTucGdl4Kan12H21OutEntity> OutList = cpnTucGdl4Kan12H21SelectMapper
                .findCpnTucGdl4Kan12H21ByCriteria(inEntity);
        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）情報を取得する。
     * 関数名：findCpnTucGdl4Kan13H21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）
     */
    private CpnTucGdl4Kan13H21OutEntity findCpnTucGdl4Kan13H21(Integer gdlId,
            Integer sc1Id) {
        CpnTucGdl4Kan13H21ByCriteriaInEntity inEntity = new CpnTucGdl4Kan13H21ByCriteriaInEntity();
        CpnTucGdl4Kan13H21OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<CpnTucGdl4Kan13H21OutEntity> OutList = cpnTucGdl4Kan13H21SelectMapper
                .findCpnTucGdl4Kan13H21ByCriteria(inEntity);
        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）情報を取得する。
     * 関数名：findCpnTucGdl4Kan15H21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）
     */
    private CpnTucGdl4Kan15H21OutEntity findCpnTucGdl4Kan15H21(Integer gdlId,
            Integer sc1Id) {
        CpnTucGdl4Kan15H21ByCriteriaInEntity inEntity = new CpnTucGdl4Kan15H21ByCriteriaInEntity();
        CpnTucGdl4Kan15H21OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<CpnTucGdl4Kan15H21OutEntity> OutList = cpnTucGdl4Kan15H21SelectMapper
                .findCpnTucGdl4Kan15H21ByCriteria(inEntity);
        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）情報を取得する。
     * 関数名：findCpnTucGdl4Kan16H21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）
     */
    private CpnTucGdl4Kan16H21OutEntity findCpnTucGdl4Kan16H21(Integer gdlId,
            Integer sc1Id) {
        CpnTucGdl4Kan16H21ByCriteriaInEntity inEntity = new CpnTucGdl4Kan16H21ByCriteriaInEntity();
        CpnTucGdl4Kan16H21OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<CpnTucGdl4Kan16H21OutEntity> OutList = cpnTucGdl4Kan16H21SelectMapper
                .findCpnTucGdl4Kan16H21ByCriteria(inEntity);
        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿介護に関する医師の意見（Ｈ２１改訂）情報を取得する。
     * 関数名：findCpnTucGdl4MedH21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿介護に関する医師の意見（Ｈ２１改訂）
     */
    private CpnTucGdl4MedH21OutEntity findCpnTucGdl4MedH21(Integer gdlId, Integer sc1Id) {
        CpnTucGdl4MedH21ByCriteriaInEntity inEntity = new CpnTucGdl4MedH21ByCriteriaInEntity();
        CpnTucGdl4MedH21OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<CpnTucGdl4MedH21OutEntity> OutList = cpnTucGdl4MedH21SelectMapper
                .findCpnTucGdl4MedH21ByCriteria(inEntity);
        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報を取得する。
     * 関数名：findCpnTucGdl4NeceH21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）
     */
    private List<CpnTucGdl4NeceH21ListOutEntity> findCpnTucGdl4NeceH21(Integer gdlId, Integer sc1Id) {
        CpnTucGdl4NeceH21ListByCriteriaInEntity inEntity = new CpnTucGdl4NeceH21ListByCriteriaInEntity();
        // アセスメントID
        inEntity.setAlGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<CpnTucGdl4NeceH21ListOutEntity> OutList = cpnTucGdl4NeceH21SelectMapper
                .findCpnTucGdl4NeceH21ListByCriteria(inEntity);
        return OutList;
    }

    /**
     * ＧＬ＿１日のスケジュール（Ｈ２１改訂）情報を取得する。
     * 関数名：findCpnTucGdl4SchdayH21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿１日のスケジュール（Ｈ２１改訂）
     */
    private List<CpnTucGdl4SchdayH21OutEntity> findCpnTucGdl4SchdayH21(Integer gdlId, Integer sc1Id) {
        CpnTucGdl4SchdayH21ByCriteriaInEntity inEntity = new CpnTucGdl4SchdayH21ByCriteriaInEntity();
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setSc1Id(sc1Id);

        List<CpnTucGdl4SchdayH21OutEntity> OutList = cpnTucGdl4SchdayH21SelectMapper
                .findCpnTucGdl4SchdayH21ByCriteria(inEntity);
        return OutList;
    }

    /**
     * フェースシート（H21/４改訂版）情報を削除する。
     * 関数名：deleteCpnTucGdl4FaceH21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    public int deleteCpnTucGdl4FaceH21(Integer gdlId, Integer sc1Id) {
        CpnTucGdl4FaceH21Criteria criteria = new CpnTucGdl4FaceH21Criteria();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID

        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl4FaceH21Mapper.deleteByCriteria(criteria);
        return cont;
    }

    /**
     * 家族状況とインフォーマルな支援の状況（Ｈ２１改訂）情報を削除（論理）する。
     * 関数名：deleteCpnTucGdl4Fam11H21
     *
     * @param gdlId       アセスメントID
     * @param sc1Id       計画期間ID
     * @param modifiedCnt 更新回数
     * @return 更新件数
     */
    private int deleteCpnTucGdl4Fam11H21(Integer gdlId, Integer sc1Id) {

        CpnTucGdl4Fam11H21Criteria criteria = new CpnTucGdl4Fam11H21Criteria();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID

        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl4Fam11H21Mapper.deleteByCriteria(criteria);
        return cont;

    }

    /**
     * ＧＬ＿サービス利用状況（Ｈ２１改訂）一覧情を削除（論理）する。
     * 関数名：deleteCpnTucGdl4SerH21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteCpnTucGdl4SerH21(Integer gdlId, Integer sc1Id) {

        CpnTucGdl4SerH21Criteria criteria = new CpnTucGdl4SerH21Criteria();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID

        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl4SerH21Mapper.deleteByCriteria(criteria);
        return cont;

    }

    /**
     * ＧＬ＿サービス利用状況（Ｈ２１改訂）情報を削除（論理）する。
     * 関数名：deleteCpnTucGdl4Hou11H21SelSetsubi5
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteCpnTucGdl4Hou11H21SelSetsubi5(Integer gdlId, Integer sc1Id) {

        CpnTucGdl4Hou11H21Criteria criteria = new CpnTucGdl4Hou11H21Criteria();
        CpnTucGdl4Hou11H21 inEntity = new CpnTucGdl4Hou11H21();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl4Hou11H21Mapper.deleteByCriteria(criteria);
        return cont;
    }

    /**
     * ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）情報を削除（論理）する。
     * 関数名：deleteCpnTucGdl4Kio11H21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteCpnTucGdl4Kio11H21(Integer gdlId, Integer sc1Id) {
        CpnTucGdl4Kio11H21Criteria criteria = new CpnTucGdl4Kio11H21Criteria();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID

        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl4Kio11H21Mapper.deleteByCriteria(criteria);
        return cont;
    }

    /**
     * ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）情報を削除（論理）する。
     * 関数名：deleteCpnTucGdl4Kan11H21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteCpnTucGdl4Kan11H21(Integer gdlId, Integer sc1Id) {

        CpnTucGdl4Kan11H21Criteria criteria = new CpnTucGdl4Kan11H21Criteria();
        CpnTucGdl4Kan11H21 inEntity = new CpnTucGdl4Kan11H21();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID

        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl4Kan11H21Mapper.deleteByCriteria(criteria);
        return cont;
    }

    /**
     * ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）情報を削除（論理）する。
     * 関数名：deleteCpnTucGdl4Kan12H21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteCpnTucGdl4Kan12H21(Integer gdlId, Integer sc1Id) {
        CpnTucGdl4Kan12H21Criteria criteria = new CpnTucGdl4Kan12H21Criteria();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID

        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl4Kan12H21Mapper.deleteByCriteria(criteria);
        return cont;
    }

    /**
     * ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）情報を削除（論理）する。
     * 関数名：deleteCpnTucGdl4Kan13H21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteCpnTucGdl4Kan13H21(Integer gdlId, Integer sc1Id) {

        CpnTucGdl4Kan13H21Criteria criteria = new CpnTucGdl4Kan13H21Criteria();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID

        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl4Kan13H21Mapper.deleteByCriteria(criteria);
        return cont;
    }

    /**
     * ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）情報を削除（論理）する。
     * 関数名：deleteCpnTucGdl4Kan15H21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteCpnTucGdl4Kan15H21(Integer gdlId, Integer sc1Id) {
        CpnTucGdl4Kan15H21Criteria criteria = new CpnTucGdl4Kan15H21Criteria();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID

        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl4Kan15H21Mapper.deleteByCriteria(criteria);
        return cont;
    }

    /**
     * ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）情報を 削除（論理）する。
     * 関数名：deleteCpnTucGdl4Kan16H21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteCpnTucGdl4Kan16H21(Integer gdlId, Integer sc1Id) {
        CpnTucGdl4Kan16H21Criteria criteria = new CpnTucGdl4Kan16H21Criteria();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl4Kan16H21Mapper.deleteByCriteria(criteria);
        return cont;
    }

    /**
     * ＧＬ＿介護に関する医師の意見（Ｈ２１改訂）情報を 削除（論理）する。
     * 関数名：deleteCpnTucGdl4MedH21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteCpnTucGdl4MedH21(Integer gdlId, Integer sc1Id) {
        CpnTucGdl4MedH21Criteria criteria = new CpnTucGdl4MedH21Criteria();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl4MedH21Mapper.deleteByCriteria(criteria);
        return cont;
    }

    /**
     * ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）情報を 削除（論理）する。
     * 関数名：deleteCpnTucGdl4NeceH21
     *
     * @param gdlId     アセスメントID
     * @param sc1Id     計画期間ID
     * @param mastId    マスタID
     * @param houjinId  法人ID
     * @param shisetuId 施設ID
     * @param svJigyoId 事業者ID
     * @param userId    利用者ID
     * @return 更新件数
     */
    private int deleteCpnTucGdl4NeceH21(Integer gdlId, Integer sc1Id, Integer mastId, Integer houjinId,
            Integer shisetuId, Integer svJigyoId, Integer userId) {
        CpnTucGdl4NeceH21Criteria criteria = new CpnTucGdl4NeceH21Criteria();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // マスタID＝上記で取得したＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト.マスタID
        // 法人ID＝上記で取得したＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト.法人ID
        // 施設ID＝上記で取得したＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト.施設ID
        // 事業者ID＝上記で取得したＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト.事業者ID
        // 利用者ID＝上記で取得したＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト.利用者ID
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id)
                .andMastIdEqualTo(mastId)
                .andHoujinIdEqualTo(houjinId)
                .andShisetuIdEqualTo(shisetuId)
                .andSvJigyoIdEqualTo(svJigyoId)
                .andUserIdEqualTo(userId);
        // DAOを実行
        int cont = cpnTucGdl4NeceH21Mapper.deleteByCriteria(criteria);
        return cont;
    }

    /**
     * ＧＬ＿１日のスケジュール（Ｈ２１改訂）情報を 削除（論理）する。
     * 関数名：deleteCpnTucGdl4SchdayH21
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @param seq   通番
     * @return 更新件数
     */
    private int deleteCpnTucGdl4SchdayH21(Integer gdlId, Integer sc1Id, Integer seq) {
        CpnTucGdl4SchdayH21Criteria criteria = new CpnTucGdl4SchdayH21Criteria();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 通番＝上記で取得したＧＬ＿１日のスケジュール（Ｈ２１改訂）リスト.通番
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id)
                .andSeqEqualTo(seq);
        // DAOを実行
        int cont = cpnTucGdl4SchdayH21Mapper.deleteByCriteria(criteria);
        return cont;
    }

    /**
     * GL＿フェースシート（R3改訂）情報を取得する。
     * 関数名：findGdl5Ass1R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return GL＿フェースシート（R3改訂）
     */
    private Gdl5Ass1R3OutEntity findGdl5Ass1R3(Integer gdlId, Integer sc1Id) {
        Gdl5Ass1R3ByCriteriaInEntity inEntity = new Gdl5Ass1R3ByCriteriaInEntity();
        Gdl5Ass1R3OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<Gdl5Ass1R3OutEntity> OutList = cpnTucGdl5FaceR3SelectMapper.findGdl5Ass1R3ByCriteria(inEntity);

        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * GL＿家族状況とインフォーマルな支援の状況（R3改訂）情報を取得する。
     * 関数名：findGdl5Ass2R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return GL＿家族状況とインフォーマルな支援の状況（R3改訂）
     */
    private Gdl5Ass2R3OutEntity findGdl5Ass2R3(Integer gdlId, Integer sc1Id) {
        Gdl5Ass2R3ByCriteriaInEntity inEntity = new Gdl5Ass2R3ByCriteriaInEntity();
        Gdl5Ass2R3OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<Gdl5Ass2R3OutEntity> OutList = cpnTucGdl5Fam11R3SelectMapper.findGdl5Ass2R3ByCriteria(inEntity);

        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * サービス利用状況（R３改訂）情報を取得する。
     * 関数名：findGdl5Ass3R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return サービス利用状況（R３改訂）
     */
    private Gdl5Ass3R3OutEntity findGdl5Ass3R3(Integer gdlId, Integer sc1Id) {
        Gdl5Ass3R3ByCriteriaInEntity inEntity = new Gdl5Ass3R3ByCriteriaInEntity();
        Gdl5Ass3R3OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSclId(sc1Id);

        List<Gdl5Ass3R3OutEntity> OutList = cpnTucGdl5SerR3SelectMapper.findGdl5Ass3R3ByCriteria(inEntity);

        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * GL_住宅等の状況（R3改訂）情報を取得する。
     * 関数名：findGdl5Ass4R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return GL_住宅等の状況（R3改訂）
     */
    private Gdl5Ass4R3OutEntity findGdl5Ass4R3(Integer gdlId, Integer sc1Id) {
        Gdl5Ass4R3ByCriteriaInEntity inEntity = new Gdl5Ass4R3ByCriteriaInEntity();
        Gdl5Ass4R3OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<Gdl5Ass4R3OutEntity> OutList = cpnTucGdl5Hou11R3SelectMapper.findGdl5Ass4R3ByCriteria(inEntity);

        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿本人の健康状態・受診等の状況（Ｒ3改訂）情報を取得する。
     * 関数名：findGdl5Ass5R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿本人の健康状態・受診等の状況（Ｒ3改訂）
     */
    private Gdl5Ass5R3OutEntity findGdl5Ass5R3(Integer gdlId, Integer sc1Id) {
        Gdl5Ass5R3ByCriteriaInEntity inEntity = new Gdl5Ass5R3ByCriteriaInEntity();
        Gdl5Ass5R3OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<Gdl5Ass5R3OutEntity> OutList = cpnTucGdl5Kio11R3SelectMapper.findGdl5Ass5R3ByCriteria(inEntity);

        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿①基本（身体機能・起居）動作（Ｒ3改訂）情報を取得する。
     * 関数名：findGdl5Ass6R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿①基本（身体機能・起居）動作（Ｒ3改訂）
     */
    private Gdl5Ass6R3OutEntity findGdl5Ass6R3(Integer gdlId, Integer sc1Id) {
        Gdl5Ass6R3ByCriteriaInEntity inEntity = new Gdl5Ass6R3ByCriteriaInEntity();
        Gdl5Ass6R3OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<Gdl5Ass6R3OutEntity> OutList = cpnTucGdl5Kan11R3SelectMapper.findGdl5Ass6R3ByCriteria(inEntity);

        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿②生活機能（食事・排泄）（R3改訂）情報を取得する。
     * 関数名：findGdl5Ass7R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿②生活機能（食事・排泄）（R3改訂）
     */
    private Gdl5Ass7R3OutEntity findGdl5Ass7R3(Integer gdlId, Integer sc1Id) {
        Gdl5Ass7R3ByCriteriaInEntity inEntity = new Gdl5Ass7R3ByCriteriaInEntity();
        Gdl5Ass7R3OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<Gdl5Ass7R3OutEntity> OutList = cpnTucGdl5Kan12R3SelectMapper.findGdl5Ass7R3ByCriteria(inEntity);

        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿③認知機能・④精神・行動障害（Ｒ3改訂）情報を取得する。
     * 関数名：findGdl5Ass8R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿③認知機能・④精神・行動障害（Ｒ3改訂）
     */
    private Gdl5Ass8R3OutEntity findGdl5Ass8R3(Integer gdlId, Integer sc1Id) {
        Gdl5Ass8R3ByCriteriaInEntity inEntity = new Gdl5Ass8R3ByCriteriaInEntity();
        Gdl5Ass8R3OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<Gdl5Ass8R3OutEntity> OutList = cpnTucGdl5Kan13R3SelectMapper.findGdl5Ass8R3ByCriteria(inEntity);

        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿⑤社会生活（への適応）力（Ｒ３改訂）情報を取得する。
     * 関数名：findGdl5Ass9R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿⑤社会生活（への適応）力（Ｒ３改訂）
     */
    private Gdl5Ass9R3OutEntity findGdl5Ass9R3(Integer gdlId, Integer sc1Id) {
        Gdl5Ass9R3ByCriteriaInEntity inEntity = new Gdl5Ass9R3ByCriteriaInEntity();
        Gdl5Ass9R3OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<Gdl5Ass9R3OutEntity> OutList = cpnTucGdl5Kan15R3SelectMapper.findGdl5Ass9R3ByCriteria(inEntity);

        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿⑥医療・健康関係（Ｒ３改訂）情報を取得する。
     * 関数名：findGdl5Ass10R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿⑥医療・健康関係（Ｒ３改訂）
     */
    private Gdl5Ass10R3OutEntity findGdl5Ass10R3(Integer gdlId, Integer sc1Id) {
        Gdl5Ass10R3ByCriteriaInEntity inEntity = new Gdl5Ass10R3ByCriteriaInEntity();
        Gdl5Ass10R3OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<Gdl5Ass10R3OutEntity> OutList = cpnTucGdl5Kan16R3SelectMapper.findGdl5Ass10R3ByCriteria(inEntity);

        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿介護に関する医師の意見（Ｒ3改訂）情報を取得する。
     * 関数名：findGdl5Ass11R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿介護に関する医師の意見（Ｒ3改訂）
     */
    private Gdl5Ass11R3OutEntity findGdl5Ass11R3(Integer gdlId, Integer sc1Id) {
        Gdl5Ass11R3ByCriteriaInEntity inEntity = new Gdl5Ass11R3ByCriteriaInEntity();
        Gdl5Ass11R3OutEntity outEntity = null;
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<Gdl5Ass11R3OutEntity> OutList = cpnTucGdl5MedR3SelectMapper.findGdl5Ass11R3ByCriteria(inEntity);

        if (OutList != null && OutList.size() > 0) {
            outEntity = OutList.get(0);
        }
        return outEntity;
    }

    /**
     * ＧＬ＿全体のまとめ・特記事項（R３改訂）一覧情情報を取得する。
     * 関数名：findGdl5Ass12R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿全体のまとめ・特記事項（R３改訂）一覧情
     */
    private List<Gdl5Ass12R3OutEntity> findGdl5Ass12R3(Integer gdlId, Integer sc1Id) {
        Gdl5Ass12R3ByCriteriaInEntity inEntity = new Gdl5Ass12R3ByCriteriaInEntity();
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setAlSc1Id(sc1Id);

        List<Gdl5Ass12R3OutEntity> OutList = cpnTucGdl5NeceR3SelectMapper.findGdl5Ass12R3ByCriteria(inEntity);
        return OutList;
    }

    /**
     * ＧＬ＿1日のスケジュール（Ｒ3改訂）情報を取得する。
     * 関数名：findGdl5Ass13R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return ＧＬ＿1日のスケジュール（Ｒ3改訂）
     */
    private List<Gdl5Ass13R3OutEntity> findGdl5Ass13R3(Integer gdlId, Integer sc1Id) {
        Gdl5Ass13R3ByCriteriaInEntity inEntity = new Gdl5Ass13R3ByCriteriaInEntity();
        // アセスメントID
        inEntity.setGdlId(gdlId);
        // 計画期間ID
        inEntity.setSc1Id(sc1Id);

        List<Gdl5Ass13R3OutEntity> OutList = cpnTucGdl5SchdayR3SelectMapper.findGdl5Ass13R3ByCriteria(inEntity);
        return OutList;
    }

    /**
     * GL＿フェースシート（R3改訂）情報を削除（論理）する。
     * 関数名：deleteGdl5Ass1R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    public int deleteGdl5Ass1R3(Integer gdlId, Integer sc1Id) {
        CpnTucGdl5FaceR3Criteria criteria = new CpnTucGdl5FaceR3Criteria();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl5FaceR3Mapper.deleteByCriteria(criteria);
        return cont;

    }

    /**
     * GL＿家族状況とインフォーマルな支援の状況（R3改訂）情報を削除（論理）する。
     * 関数名：deleteGdl5Ass2R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteGdl5Ass2R3(Integer gdlId, Integer sc1Id) {
        CpnTucGdl5Fam11R3Criteria criteria = new CpnTucGdl5Fam11R3Criteria();
        CpnTucGdl5Fam11R3 inEntity = new CpnTucGdl5Fam11R3();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 削除フラグ=0（未削除データ）
        // 更新回数＝上記で取得した各テーブルことの更新回数
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl5Fam11R3Mapper.updateByCriteriaSelective(inEntity,
                criteria);
        return cont;
    }

    /**
     * サービス利用状況（R３改訂）情報を削除（論理）する。
     * 関数名：deleteGdl5Ass3R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteGdl5Ass3R3(Integer gdlId, Integer sc1Id) {
        CpnTucGdl5SerR3Criteria criteria = new CpnTucGdl5SerR3Criteria();
        CpnTucGdl5SerR3 inEntity = new CpnTucGdl5SerR3();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 削除フラグ=0（未削除データ）
        // 更新回数＝上記で取得した各テーブルことの更新回数
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl5SerR3Mapper.updateByCriteriaSelective(inEntity,
                criteria);
        return cont;
    }

    /**
     * GL_住宅等の状況（R3改訂）情報を削除（論理）する。
     * 関数名：deleteGdl5Ass4R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteGdl5Ass4R3(Integer gdlId, Integer sc1Id) {
        CpnTucGdl5Hou11R3Criteria criteria = new CpnTucGdl5Hou11R3Criteria();
        CpnTucGdl5Hou11R3 inEntity = new CpnTucGdl5Hou11R3();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 削除フラグ=0（未削除データ）
        // 更新回数＝上記で取得した各テーブルことの更新回数
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl5Hou11R3Mapper.updateByCriteriaSelective(inEntity,
                criteria);
        return cont;
    }

    /**
     * ＧＬ＿本人の健康状態・受診等の状況（Ｒ3改訂）情報を削除（論理）する。
     * 関数名：deleteGdl5Ass5R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteGdl5Ass5R3(Integer gdlId, Integer sc1Id) {
        CpnTucGdl5Kio11R3Criteria criteria = new CpnTucGdl5Kio11R3Criteria();
        CpnTucGdl5Kio11R3 inEntity = new CpnTucGdl5Kio11R3();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 削除フラグ=0（未削除データ）
        // 更新回数＝上記で取得した各テーブルことの更新回数
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl5Kio11R3Mapper.updateByCriteriaSelective(inEntity,
                criteria);
        return cont;
    }

    /**
     * ＧＬ＿①基本（身体機能・起居）動作（Ｒ3改訂）情報を削除（論理）する。
     * 関数名：deleteGdl5Ass6R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteGdl5Ass6R3(Integer gdlId, Integer sc1Id) {
        CpnTucGdl5Kan11R3Criteria criteria = new CpnTucGdl5Kan11R3Criteria();
        CpnTucGdl5Kan11R3 inEntity = new CpnTucGdl5Kan11R3();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 削除フラグ=0（未削除データ）
        // 更新回数＝上記で取得した各テーブルことの更新回数
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl5Kan11R3Mapper.updateByCriteriaSelective(inEntity,
                criteria);
        return cont;
    }

    /**
     * ＧＬ＿②生活機能（食事・排泄）（R3改訂）情報を削除（論理）する。
     * 関数名：deleteGdl5Ass7R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteGdl5Ass7R3(Integer gdlId, Integer sc1Id) {
        CpnTucGdl5Kan12R3Criteria criteria = new CpnTucGdl5Kan12R3Criteria();
        CpnTucGdl5Kan12R3 inEntity = new CpnTucGdl5Kan12R3();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 削除フラグ=0（未削除データ）
        // 更新回数＝上記で取得した各テーブルことの更新回数
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl5Kan12R3Mapper.updateByCriteriaSelective(inEntity,
                criteria);
        return cont;
    }

    /**
     * ＧＬ＿③認知機能・④精神・行動障害（Ｒ3改訂）情報を削除（論理）する。
     * 関数名：deleteGdl5Ass8R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteGdl5Ass8R3(Integer gdlId, Integer sc1Id) {
        CpnTucGdl5Kan13R3Criteria criteria = new CpnTucGdl5Kan13R3Criteria();
        CpnTucGdl5Kan13R3 inEntity = new CpnTucGdl5Kan13R3();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 削除フラグ=0（未削除データ）
        // 更新回数＝上記で取得した各テーブルことの更新回数
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl5Kan13R3Mapper.updateByCriteriaSelective(inEntity,
                criteria);
        return cont;
    }

    /**
     * ＧＬ＿⑤社会生活（への適応）力（Ｒ３改訂）情報を削除（論理）する。
     * 関数名：deleteGdl5Ass9R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteGdl5Ass9R3(Integer gdlId, Integer sc1Id) {
        CpnTucGdl5Kan15R3Criteria criteria = new CpnTucGdl5Kan15R3Criteria();
        CpnTucGdl5Kan15R3 inEntity = new CpnTucGdl5Kan15R3();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 削除フラグ=0（未削除データ）
        // 更新回数＝上記で取得した各テーブルことの更新回数
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl5Kan15R3Mapper.updateByCriteriaSelective(inEntity,
                criteria);
        return cont;
    }

    /**
     * ＧＬ＿⑥医療・健康関係（Ｒ３改訂）情報を削除（論理）する。
     * 関数名：deleteGdl5Ass10R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteGdl5Ass10R3(Integer gdlId, Integer sc1Id) {
        CpnTucGdl5Kan16R3Criteria criteria = new CpnTucGdl5Kan16R3Criteria();
        CpnTucGdl5Kan16R3 inEntity = new CpnTucGdl5Kan16R3();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 削除フラグ=0（未削除データ）
        // 更新回数＝上記で取得した各テーブルことの更新回数
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl5Kan16R3Mapper.updateByCriteriaSelective(inEntity,
                criteria);
        return cont;
    }

    /**
     * ＧＬ＿介護に関する医師の意見（Ｒ3改訂）情報を削除（論理）する。
     * 関数名：deleteGdl5Ass11R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @return 更新件数
     */
    private int deleteGdl5Ass11R3(Integer gdlId, Integer sc1Id) {
        CpnTucGdl5MedR3Criteria criteria = new CpnTucGdl5MedR3Criteria();
        CpnTucGdl5MedR3 inEntity = new CpnTucGdl5MedR3();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 削除フラグ=0（未削除データ）
        // 更新回数＝上記で取得した各テーブルことの更新回数
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);
        // DAOを実行
        int cont = cpnTucGdl5MedR3Mapper.updateByCriteriaSelective(inEntity,
                criteria);
        return cont;
    }

    /**
     * ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）一覧情情報を削除（論理）する。
     * 関数名：deleteGdl5Ass12R3
     *
     * @param gdlId     アセスメントID
     * @param sc1Id     計画期間ID
     * @param mastId    マスタID
     * @param houjinId  法人ID
     * @param shisetuId 施設ID
     * @param svJigyoId 事業者ID
     * @param userId    利用者ID
     * @return 更新件数
     */
    private int deleteGdl5Ass12R3(Integer gdlId, Integer sc1Id, Integer mastId,
            Integer houjinId, Integer shisetuId,
            Integer svJigyoId, Integer userId) {
        CpnTucGdl5NeceR3Criteria criteria = new CpnTucGdl5NeceR3Criteria();
        CpnTucGdl5NeceR3 inEntity = new CpnTucGdl5NeceR3();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 削除フラグ=0（未削除データ）
        // マスタID＝上記で取得したＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト.マスタID
        // 法人ID＝上記で取得したＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト.法人ID
        // 施設ID＝上記で取得したＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト.施設ID
        // 事業者ID＝上記で取得したＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト.事業者ID
        // 利用者ID＝上記で取得したＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト.利用者ID
        // 更新回数＝上記で取得したＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト.更新回数

        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id)
                .andMastIdEqualTo(mastId)
                .andHoujinIdEqualTo(houjinId)
                .andShisetuIdEqualTo(shisetuId)
                .andSvJigyoIdEqualTo(svJigyoId)
                .andUserIdEqualTo(userId);
        // DAOを実行
        int cont = cpnTucGdl5NeceR3Mapper.updateByCriteriaSelective(inEntity,
                criteria);
        return cont;
    }

    /**
     * ＧＬ＿1日のスケジュール（Ｒ3改訂）情報を削除（論理）する。
     * 関数名：deleteGdl5Ass13R3
     *
     * @param gdlId アセスメントID
     * @param sc1Id 計画期間ID
     * @param seq   通番
     * @return 更新件数
     */
    private int deleteGdl5Ass13R3(Integer gdlId, Integer sc1Id, Integer seq) {
        CpnTucGdl5SchdayR3Criteria criteria = new CpnTucGdl5SchdayR3Criteria();
        CpnTucGdl5SchdayR3 inEntity = new CpnTucGdl5SchdayR3();
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 削除フラグ=0（未削除データ）
        // 通番＝リスト.通番
        // 更新回数＝上記で取得した各テーブルことの更新回数
        criteria.createCriteria()
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id)
                .andSeqEqualTo(seq);
        // DAOを実行
        int cont = cpnTucGdl5SchdayR3Mapper.updateByCriteriaSelective(inEntity,
                criteria);
        return cont;
    }

    /**
     * 1.1.ＧＬ＿課題と目標テーブル（cpn_tuc_gdl_kadai）の削除（論理）する
     * 関数名：deleteCpnTucGdlKadai
     *
     * @param inDtoInfo ＧＬ＿課題と目標
     * @param gdlId     「5.1.1.」で採番したアセスメントID
     * @param sc1Id     計画対象期間ID
     * @return 更新件数
     */

    private int deleteCpnTucGdlKadai(Integer gdlId, Integer sc1Id, KadaiInDto inDto) {
        CpnTucGdlKadaiCriteria criteria = new CpnTucGdlKadaiCriteria();
        // id＝リクエストパラメータ.【課題と目標リスト】.id
        // アセスメントID=リクエストパラメータ.アセスメントID
        // 計画期間ID＝変数.計画対象期間ID
        // 削除フラグ=0（未削除データ）
        // 更新回数＝リクエストパラメータ.【課題と目標リスト】.更新回数
        criteria.createCriteria()
                .andIdEqualTo(CommonDtoUtil.strValToInt(inDto.getId()))
                .andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);

        // DAOを実行
        int cont = cpnTucGdlKadaiMapper.deleteByCriteria(criteria);
        return cont;
    }

    /**
     * 1.1.ＧＬ＿課題と目標テーブル（cpn_tuc_gdl_kadai）の登録詳細
     * 関数名：insertCpnTucGdlKadai
     *
     * @param inDtoInfo ＧＬ＿課題と目標
     * @param gdlId     「5.1.1.」で採番したアセスメントID
     * @param sc1Id     計画対象期間ID
     * @return 結果ステータス
     */

    private int insertCpnTucGdlKadai(Integer gdlId, Integer sc1Id, KadaiInDto inDto) throws Exception {
        CpnTucGdlKadai inEntity = new CpnTucGdlKadai();

        // 「5.1.1.」で採番したアセスメントID
        inEntity.setGdlId(gdlId);
        // sc1_id 計画期間ID 【変数】.計画対象期間ID
        inEntity.setSc1Id(sc1Id);
        // アセスメント番号→リクエストパラメータ.課題と目標情報リスト.アセスメント番号
        inEntity.setAssNo(CommonDtoUtil.strValToInt(inDto.getAssNo()));
        // 課題→リクエストパラメータ.課題と目標情報リスト.課題
        inEntity.setKadaiKnj(inDto.getKadaiKnj());
        // 長期→リクエストパラメータ.課題と目標情報リスト.長期
        inEntity.setChoukiKnj(inDto.getChoukiKnj());
        // 短期→リクエストパラメータ.課題と目標情報リスト.短期
        inEntity.setTankiKnj(inDto.getTankiKnj());
        // 連番→リクエストパラメータ.課題と目標情報リスト.連番
        inEntity.setSeq(CommonDtoUtil.strValToInt(inDto.getSeq()));
        int cont = cpnTucGdlKadaiMapper.insertSelectiveAndReturn(inEntity);

        return cont;
    }

    /**
     * 1.1.ＧＬ＿課題と目標テーブル（cpn_tuc_gdl_kadai）の更新詳細
     * 関数名：updateCpnTucGdlKadai
     *
     * @param gdlId     アセスメントID
     * @param sc1Id     計画対象期間ID
     * @param inDtoInfo ＧＬ＿課題と目標
     * @return 更新件数
     */

    private int updateCpnTucGdlKadai(Integer gdlId, Integer sc1Id, KadaiInDto inDto) {
        CpnTucGdlKadai inEntity = new CpnTucGdlKadai();
        CpnTucGdlKadaiCriteria criteria = new CpnTucGdlKadaiCriteria();
        // id＝リクエストパラメータ.【課題と目標リスト】.id
        // アセスメントID=リクエストパラメータ.アセスメントID
        // 計画期間ID＝変数.計画対象期間ID
        // 削除フラグ=0（未削除データ）
        // 更新回数＝リクエストパラメータ.【課題と目標リスト】.更新回数
        criteria.createCriteria()
                .andIdEqualTo(CommonDtoUtil.strValToInt(inDto.getId())).andGdlIdEqualTo(gdlId)
                .andSc1IdEqualTo(sc1Id);

        // 課題→リクエストパラメータ.課題と目標情報リスト.課題
        inEntity.setKadaiKnj(inDto.getKadaiKnj());
        // 長期→リクエストパラメータ.課題と目標情報リスト.長期
        inEntity.setChoukiKnj(inDto.getChoukiKnj());
        // 短期→リクエストパラメータ.課題と目標情報リスト.短期
        inEntity.setTankiKnj(inDto.getTankiKnj());
        // 連番→リクエストパラメータ.課題と目標情報リスト.連番
        inEntity.setSeq(CommonDtoUtil.strValToInt(inDto.getSeq()));

        // DAOを実行
        int cont = cpnTucGdlKadaiMapper.updateByCriteriaSelective(inEntity,
                criteria);
        return cont;
    }

    /**
     * 【27-06記録共通期間】情報を登録する。
     * 関数名：insertKghTucKrkKikan
     * 
     * @param inDto 登録パラメータ
     * @param sc1Id 期間ID
     * @return 結果ステータス
     * @throws Exception
     */
    public String insertKghTucKrkKikan(AssessmentHomeSaveServiceInDto inDto) throws Exception {

        // DAOパラメータを作成
        KghTucKrkKikan kghTucKrkKikan = new KghTucKrkKikan();
        // 法人ID
        kghTucKrkKikan.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        kghTucKrkKikan.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        kghTucKrkKikan.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        kghTucKrkKikan.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 種別ID
        kghTucKrkKikan.setSyubetsuId(CommonDtoUtil.strValToInt(inDto.getSyubetsuId()));
        // 開始日
        kghTucKrkKikan.setStartYmd(inDto.getKijunbiYmd());

        // DAOを実行
        int count = kghTucKrkKikanMapper.insertSelectiveAndReturn(kghTucKrkKikan);

        if (count > 0) {
            inDto.setSc1Id(CommonDtoUtil.objValToString(kghTucKrkKikan.getSc1Id()));
            return CommonConstants.SUCCESS;
        } else {
            return CommonConstants.FAILURE;
        }

    }

    /**
     * 2. バリデーションチェックを行う。
     * 
     * @param loginDto 入力情報 共通DTO
     * @return Boolean 処理結果
     */
    public Boolean documentPerformValidationCheck(AssessmentHomeSaveServiceInDto loginDto) throws Exception {

        // 2.1. 下記の共通関数「e-文書法区分取得」を利用し、【変数】.電子ファイル保存設定フラグを設定する
        boolean kghComEBunshoKbn = kghSmglHistoryLogic.getKghComEBunshoKbn();
        // 2.2. 【変数】.電子ファイル保存設定フラグが「True：適用する」の場合
        if (kghComEBunshoKbn) {
            // 2.2.1. 変数.削除フラグを設定する
            // 変数.削除フラグ=0
            int delFlg = CommonConstants.INT_0;
            // ・リクエストパラメータ.削除処理区分が「2:画面を履歴ごと削除する」の場合、変数.削除フラグ=2
            if (CommonConstants.DELETE_TEI_KBN_2.equals(loginDto.getDeleteKbn())) {
                delFlg = CommonConstants.INT_2;
            }
            // ・リクエストパラメータ.削除処理区分が「0:削除しない」の場合、且つ、リクエストパラメータ.更新区分が「C：新規」の場合、変数.削除フラグ=1
            if (CommonConstants.STR_0.equals(loginDto.getDeleteKbn()) && CommonDtoUtil.isCreate(loginDto)) {
                delFlg = CommonConstants.INT_1;
            }

            // 2.2.2. 下記の居宅アセスメント履歴情報取得のDAOを利用し、居宅アセスメント履歴情報リストを取得する。
            KyotakuRirekiByCriteriaInEntity inEntity = new KyotakuRirekiByCriteriaInEntity();
            // 計画期間ID
            inEntity.setAlSc1Id(CommonDtoUtil.strValToInt(loginDto.getSc1Id()));
            // 事業者ID
            inEntity.setJId(CommonDtoUtil.strValToInt(loginDto.getSvJigyoId()));
            // 利用者ID
            inEntity.setUId(CommonDtoUtil.strValToInt(loginDto.getUserId()));
            List<KyotakuRirekiOutEntity> outEntityList = cpnTucGdlRirekiSelectMapper
                    .findKyotakuRirekiByCriteria(inEntity);

            // 2.2.3. 共通関数「e-文書の履歴保存のチェック」を使用してe-文書の履歴保存のチェックを行う
            // 入力情報 -
            SmglYmdInDto smglYmdInDto = new SmglYmdInDto();
            // 履歴番号 リクエストパラメータ.履歴番号
            smglYmdInDto.setKrirekiNo(CommonDtoUtil.strValToInt(loginDto.getKrirekiNo()));
            // 処理年月 -
            // ks21作成日 -
            // 作成日 リクエストパラメータ.作成日
            smglYmdInDto.setCreateYmd(loginDto.getKijunbiYmd());
            // 当該年月 -
            // 機能ID リクエストパラメータ.機能ID
            // 削除フラグ 【変数】.削除フラグ
            // 履歴リスト 【変数】.履歴情報リスト
            List<Object> krireki = new ArrayList<>();
            krireki.addAll(outEntityList);
            Boolean chkSmglYmd = kghKrkNokikanOpeLogic.chkSmglYmd(CommonDtoUtil.strValToInt(loginDto.getKinoId()),
                    delFlg, smglYmdInDto, krireki);
            return chkSmglYmd;
        }
        return Boolean.TRUE;
    }

    /**
     * e文書出力
     * 
     * @param loginDto 入力情報 共通DTO
     * @param sc1Id    計画区分
     * @return String エラー区分
     */
    @SuppressWarnings("unchecked")
    public String getPrint(AssessmentHomeSaveServiceInDto loginDto, String sc1Id) throws Exception {
        // リクエストパラメータ.e文書削除用パラメータ
        EdocumentDeleteReportParameterModel edocumentDeleteUseParam = loginDto.getEdocumentDeleteUseParam();
        /*
         * ===============1. e-文書法区分を取得する===============
         * 
         */
        // 1.1. 共通関数「e-文書法区分取得」を使用してe-文書法区分を取得する
        boolean kghComEBunshoKbn = kghSmglHistoryLogic.getKghComEBunshoKbn();

        // 1.2. 「1.1.」で取得したe-文書法区分が「false：適用しない」の場合、「API定義」の4の処理へジャンプする。
        if (!kghComEBunshoKbn) {
            return CommonConstants.BLANK_STRING;
        }

        // 2.1. 印刷設定情報を取得する
        // ※API定義書_APINo(913)_利用者切替APIの「４.印刷設定初期値情報の取得」を参照
        // セクション名
        String sectionName = CommonConstants.BLANK_STRING;
        if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())) {
            sectionName = CommonConstants.ASSESSMENT_H21_4;
        } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(loginDto.getNinteiFormF())) {
            sectionName = CommonConstants.ASSESSMENT_R3_4;
        }

        List<PrintSettingInfoDto> saveBeforePrtList = new ArrayList<PrintSettingInfoDto>();

        // 2.印刷設定情報リストを取得する。
        Pair<Boolean, List<PrintSettingInfoDto>> resultPrtList = this.printSettingLogic.getInitPrtList(
                loginDto.getSysRyaku(),
                sectionName,
                loginDto.getShokuId(),
                loginDto.getHoujinId(),
                loginDto.getShisetuId(),
                loginDto.getSvJigyoId());

        saveBeforePrtList = resultPrtList.getRight();

        /*
         * ===============3.印刷設定保存情報を保存する===============
         * 
         */
        String index = getIndex(loginDto.getTabId());
        // 3.印刷設定保存情報を保存する
        FreeAssessmentFacePrintSettingsUpdateServiceOutDto freeAssessmentFacePrintSettingsUpdateServiceOutDto = this.printSettingLogic
                .insertPrtList(
                        saveBeforePrtList,
                        loginDto.getSysRyaku(),
                        sectionName,
                        loginDto.getShokuId(),
                        loginDto.getHoujinId(),
                        loginDto.getShisetuId(),
                        loginDto.getSvJigyoId(),
                        index,
                        loginDto.getSysCd(),
                        CommonConstants.STR_0,
                        CommonConstants.STR_0);

        // 印刷設定情報リスト
        List<PrintSettingInfoDto> printSettingInfoList = freeAssessmentFacePrintSettingsUpdateServiceOutDto
                .getPrtList();

        // 2.2. 「2.1.」で取得した印刷設定情報リスト.プロファイルが「”No”」の場合、レスポンス.エラー区分が「2」を設定し、
        // 「API定義」の4の処理へジャンプする。
        if (CommonConstants.PRO_FILE_NAME_INIT.equals(printSettingInfoList.get(0).getProfile())) {
            return CommonConstants.E_DOC_OUT_ERR;
        }

        // 3. リクエストパラメータ.削除処理区分が「2:画面を履歴ごと削除する」の場合、
        // 「2.1. 」で取得した印刷設定情報リストの件数分、下記の処理を行う。
        if (CommonConstants.DELETE_TEI_KBN_2.equals(loginDto.getDeleteKbn())) {
            for (PrintSettingInfoDto dto : printSettingInfoList) {
                // 3.1. 該当レコード.帳票リスト名が「”アセスメント表（全て）”」の場合、該当レコードを処理対象外である
                if (CommonConstants.PRT_TITLE_ASSESSMENT_ALL.equals(dto.getListTitle())) {
                    continue;
                }
                // 3.2. 下記の関数「S3e文書保存処理共通関数」を行う
                // edocumentDeleteUseParam
                // 帳票ID reportId ”edocumentDeleteReport”
                edocumentDeleteUseParam.setReportId(CommonConstants.EDOCUMENT_DELETE_REPORT);
                // 契約者ID keiyakushaId -
                // アップロードファイルサーバー uploadFileServer -
                // 事業者情報 jigyoInfo -
                // 法人ID houjinId -
                // 施設ID shisetuId -
                // サービス事業者ID svJigyoId -
                // サービス事業者コード svJigyoCd -
                // システム日付 systemDate -
                // データ inData -
                // 帳票区分 ledgerKbn 1
                edocumentDeleteUseParam.setLedgerKbn(CommonConstants.INT_1);
                // 帳票名 ledgerName 繰り返したレコードの出力帳票名
                edocumentDeleteUseParam.setLedgerName(dto.getDefPrtTitle());
                // 計画対象期間番号 planningPeriodNumber "リクエストパラメータ.期間対象フラグが「0：対象管理しない」の場合、""000""を設定
                // 計画対象期間（自）～（至） planningPeriodStartEnd
                // "リクエストパラメータ.期間対象フラグが「0：対象管理しない」の場合、""""を設定
                if (CommonConstants.PLANNING_PERIOD_FLG_SUBJECT_MANAGEMENT_NO == CommonDtoUtil
                        .strValToInt(loginDto.getKikanFlg())) {
                    edocumentDeleteUseParam.setPlanningPeriodNumber(CommonConstants.PLAN_NUMBER);
                    edocumentDeleteUseParam.setPlanningPeriodStartToEnd(CommonConstants.BLANK_STRING);
                } else {
                    // 上記以外の場合、リクエストパラメータ.計画対象期間番号（数字が3桁未満の場合、先頭「0」を埋め）を設定"
                    // 上記以外の場合、「リクエストパラメータ.開始日 + ""～"" +リクエストパラメータ.終了日」を設定"
                    edocumentDeleteUseParam
                            .setPlanningPeriodNumber(
                                    String.format(CommonConstants.PLAN_NUMBER_REGEX,
                                            loginDto.getPlanningPeriodNo()));
                    edocumentDeleteUseParam.setPlanningPeriodStartToEnd(
                            loginDto.getStartYmd() + CommonConstants.RANGE_SEPARATOR
                                    + loginDto.getEndYmd());
                }
                // 処理年月 processDate -
                // 提供年月 offerDate -
                // 保険者 insurer -
                // 保険者番号 insurerNumber -
                // 被保険者番号 insuredPersonNumber -
                // 履歴番号 historyNumber リクエストパラメータ.履歴番号
                // リクエストパラメータ.当履歴ページ番号 ※数字が5桁未満の場合、先頭「0」を埋め
                String krirekiNo = String.format(CommonConstants.ZERO_FIVE_STRING,
                        Integer.parseInt(loginDto.getKrirekiNo()));
                edocumentDeleteUseParam.setHistoryNumber(krirekiNo);
                // 10.2.3.1.2. 下記の関数「S3e文書保存処理共通関数」を行う uploadToS3EBunsho
                // 帳票ID ”edocumentDeleteReport”
                // 帳票サービスInDto "リクエストパラメータ.e文書削除用パラメータ
                // ログインID リクエストパラメータ.ログインID
                // E文書更新区分 "D"
                // 履歴番号 リクエストパラメータ.履歴番号
                // 計画対象期間ID 【変数】.計画期間ID
                try {
                    cmnStorageServiceUtil.uploadToS3EDocument(CommonConstants.EDOCUMENT_DELETE_REPORT,
                            edocumentDeleteUseParam, loginDto.getLoginId(), CommonConstants.TAB_NAME_D,
                            loginDto.getKrirekiNo(), sc1Id);
                } catch (Exception e) {
                    // 3.3.上記の処理に異常が発生した場合、レスポンス.エラー区分が「2」を設定し、「API定義」の4の処理へジャンプする。
                    return CommonConstants.E_DOC_OUT_ERR;
                }

            }

        }

        // 4. リクエストパラメータ.削除処理区分が「1:画面のみ削除する」の場合、下記の関数「S3e文書保存処理共通関数」を行う。
        if (CommonConstants.DELETE_TEI_KBN_1.equals(loginDto.getDeleteKbn())) {
            // 4.1. シート「補足」のNo.1を参考し、リクエストパラメータ.タブIDに対す帳票番号を帳票番号リストに追加する。
            // ・リクエストパラメータ.タブIDが「3」の場合、帳票番号リストに帳票番号「2」を追加する
            List<String> list = new ArrayList<>();
            if (CommonConstants.TAB_ID_3.equals(loginDto.getTabId())) {
                list.add(CommonConstants.TAB_ID_2);
            }
            // 4.2. 「4.1」で作成した帳票番号リストの件数分、下記の関数「S3e文書保存処理共通関数」を行う。
            for (String no : list) {
                // edocumentDeleteUseParam
                // 帳票ID reportId ”edocumentDeleteReport”
                edocumentDeleteUseParam.setReportId(CommonConstants.EDOCUMENT_DELETE_REPORT);
                // 契約者ID keiyakushaId -
                // アップロードファイルサーバー uploadFileServer -
                // 事業者情報 jigyoInfo -
                // 法人ID houjinId -
                // 施設ID shisetuId -
                // サービス事業者ID svJigyoId -
                // サービス事業者コード svJigyoCd -
                // システム日付 systemDate -
                // データ inData -
                // 帳票区分 ledgerKbn 1
                edocumentDeleteUseParam.setLedgerKbn(CommonConstants.INT_1);
                // 帳票名 ledgerName 「2.1. 」で取得した印刷設定情報リストに該当帳票番号と一致するレコード.帳票リスト名
                PrintSettingInfoDto dto = printSettingInfoList.stream()
                        .filter(o -> o.getPrtNo().equals(no)).findFirst().orElse(null);
                String prtName = CommonConstants.BLANK_STRING;
                if (Objects.nonNull(dto)) {
                    prtName = dto.getListName();
                }
                edocumentDeleteUseParam.setLedgerName(prtName);
                // 計画対象期間番号 planningPeriodNumber "リクエストパラメータ.期間対象フラグが「0：対象管理しない」の場合、""000""を設定
                // 計画対象期間（自）～（至） planningPeriodStartEnd
                // "リクエストパラメータ.期間対象フラグが「0：対象管理しない」の場合、""""を設定
                if (CommonConstants.PLANNING_PERIOD_FLG_SUBJECT_MANAGEMENT_NO == CommonDtoUtil
                        .strValToInt(loginDto.getKikanFlg())) {
                    edocumentDeleteUseParam.setPlanningPeriodNumber(CommonConstants.PLAN_NUMBER);
                    edocumentDeleteUseParam.setPlanningPeriodStartToEnd(CommonConstants.BLANK_STRING);
                } else {
                    // 上記以外の場合、リクエストパラメータ.計画対象期間番号（数字が3桁未満の場合、先頭「0」を埋め）を設定"
                    // 上記以外の場合、「リクエストパラメータ.開始日 + ""～"" +リクエストパラメータ.終了日」を設定"
                    edocumentDeleteUseParam
                            .setPlanningPeriodNumber(
                                    String.format(CommonConstants.PLAN_NUMBER_REGEX,
                                            loginDto.getPlanningPeriodNo()));
                    edocumentDeleteUseParam.setPlanningPeriodStartToEnd(
                            loginDto.getStartYmd() + CommonConstants.RANGE_SEPARATOR
                                    + loginDto.getEndYmd());
                }
                // 処理年月 processDate -
                // 提供年月 offerDate -
                // 保険者 insurer -
                // 保険者番号 insurerNumber -
                // 被保険者番号 insuredPersonNumber -
                // 履歴番号 historyNumber リクエストパラメータ.履歴番号
                // リクエストパラメータ.当履歴ページ番号 ※数字が5桁未満の場合、先頭「0」を埋め
                String krirekiNo = String.format(CommonConstants.ZERO_FIVE_STRING,
                        Integer.parseInt(loginDto.getKrirekiNo()));
                edocumentDeleteUseParam.setHistoryNumber(krirekiNo);
                // 10.2.3.1.2. 下記の関数「S3e文書保存処理共通関数」を行う uploadToS3EBunsho
                // 帳票ID ”edocumentDeleteReport”
                // 帳票サービスInDto "リクエストパラメータ.e文書削除用パラメータ
                // ログインID リクエストパラメータ.ログインID
                // E文書更新区分 "D"
                // 履歴番号 リクエストパラメータ.履歴番号
                // 計画対象期間ID 【変数】.計画期間ID
                try {
                    cmnStorageServiceUtil.uploadToS3EDocument(CommonConstants.EDOCUMENT_DELETE_REPORT,
                            edocumentDeleteUseParam, loginDto.getLoginId(), CommonConstants.TAB_NAME_D,
                            loginDto.getKrirekiNo(), sc1Id);
                } catch (Exception e) {
                    // 4.3.上記の処理に異常が発生した場合、レスポンス.エラー区分が「2」を設定し、「API定義」の4の処理へジャンプする。
                    return CommonConstants.E_DOC_OUT_ERR;
                }
            }
        }

        // 5. リクエストパラメータ.削除処理区分が「0:削除しない」以外の場合、下記の処理を行う。
        if (!CommonConstants.STR_0.equals(loginDto.getDeleteKbn())) {
            // 5.1. リクエストパラメータ.e文書用パラメータの「データ」部を編集する
            HomeAssessmentSheetAllReportParameterModel edocumentUseParam = loginDto.getEdocumentUseParam();
            // ガイドラインまとめ リクエストパラメータ.ガイドラインまとめ
            edocumentUseParam.setGdlMatomeFlg(loginDto.getMatomeFlg());
            // 事業者名 リクエストパラメータ.事業者名
            edocumentUseParam.setSvJigyoKnj(loginDto.getSvJigyoKnj());
            // システムコード リクエストパラメータ.システムコード
            edocumentUseParam.setSyscd(loginDto.getSysCd());
            // 印刷設定
            ReportCommonPrintSet printSet = edocumentUseParam.getPrintSet();

            // ※ シート「補足」のNo.1を参考し、
            // 「2.1. 」で取得した印刷設定情報リストにリクエストパラメータ.タブIDに対す帳票番号と一致するレコード.日付表示有無
            PrintSettingInfoDto dto = printSettingInfoList.stream()
                    .filter(o -> o.getPrtNo().equals(index)).findFirst().orElse(new PrintSettingInfoDto());

            // 指定日印刷区分 shiTeiKubun 【変数】.印刷設定情報.日付表示有無
            printSet.setShiTeiKubun(dto.getPrnDate());
            // 指定日 shiTeiDate システム日付
            printSet.setShiTeiDate(DateUtil.formatDateToString(AppUtil.getSystemTimeStamp(), null));

            // 印刷オプション printOption -
            ReportHomeAssessmentAllPrintOption printOption = edocumentUseParam.getPrintOption();
            // 記入用シートを印刷するフラグ false
            printOption.setEmptyFlg(CommonConstants.STR_FALSE);
            // 特記事項を別紙に印刷するフラグ パラメータ16
            printOption.setSpecialnoteAppendix(dto.getParam16());
            // 特記事項の印刷枠の高さ設定フラグ パラメータ12
            printOption.setSpecialnoteLine(dto.getParam12());
            // 特記事項の印刷枠の最小行数 パラメータ13
            printOption.setSpecialnoteLineHeight(dto.getParam13());
            // まとめの印刷枠の高さ設定フラグ パラメータ14
            printOption.setSummaryLine(dto.getParam14());
            // まとめの印刷枠の最小行数
            printOption.setSummaryLineHeight(dto.getParam15());
            // (状況別)空白の項目も印刷する
            printOption.setBlankItem(dto.getParam11());
            List<ReportHomeAssessmentAllPrintSubjectHistory> printSubjectHistoryList = edocumentUseParam
                    .getPrintSubjectHistoryList();
            for (ReportHomeAssessmentAllPrintSubjectHistory history : printSubjectHistoryList) {
                // 利用者ID
                history.setUserId(loginDto.getUserId());
                // 利用者名
                history.setUserName(loginDto.getUserName());
                // 期間ID
                history.setSc1Id(loginDto.getSc1Id());
                // 開始日
                history.setStartYmd(loginDto.getStartYmd());
                // 終了日
                history.setEndYmd(loginDto.getEndYmd());
                // 作成日
                history.setAsJisshiDateYmd(loginDto.getKijunbiYmd());
                // 作成者ID
                history.setShokuId(loginDto.getSakuseiId());
                // アセスメントID
                String gdlId = loginDto.getGdlId();
                // リクエストパラメータ.履歴更新区分が「C:新規」の場合、採番したアセスメントID
                // リクエストパラメータ.履歴更新区分が「C:新規」以外の場合、：リクエストパラメータ.アセスメントID
                if (CommonDtoUtil.isHistoryCreate(loginDto)) {
                    gdlId = CommonDtoUtil.objValToString(numbering.getNumber(AppUtil.getKeiyakushaId(),
                            CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.CPN_TUC_GDL_RIREKI_GDL_ID
                                    .getIntValue()));
                }
                history.setGdlId(gdlId);
                // 改定フラグ
                history.setNinteiFormF(loginDto.getNinteiFormF());
                // 出力帳票印刷情報リスト choPrtList -
                List<ReportCommonHomeAssessmentAllChoPrt> choPrtList = history.getChoPrtList();
                for (ReportCommonHomeAssessmentAllChoPrt choPrt : choPrtList) {
                    // 職員ID shokuId リクエストパラメータ.ログインID
                    choPrt.setShokuId(loginDto.getShokuId());
                    // システム略称 sysRyaku リクエストパラメータ.システム略称
                    choPrt.setSysRyaku(loginDto.getSysRyaku());
                    // 出力帳票名 defPrtTitle 【変数】.印刷設定情報.出力帳票名
                    choPrt.setDefPrtTitle(dto.getDefPrtTitle());

                    // 帳票タイトル prtTitle 【変数】.印刷設定情報.帳票タイトル
                    choPrt.setPrtTitle(dto.getPrtTitle());

                    // セクション section 【変数】.印刷設定情報.セクション番号
                    choPrt.setSection(dto.getSectionNo());

                    // 帳票番号 prtNo 【変数】.印刷設定情報.帳票番号
                    choPrt.setPrtNo(dto.getPrtNo());

                    // プロファイル choPro 【変数】.印刷設定情報.プロファイル
                    choPrt.setChoPro(dto.getProfile());

                    // セクション名 sectionName セクション名：シート「補足」のNo.3を参考し、リクエストパラメータ.改訂フラグに対すセクション名
                    choPrt.setSectionName(sectionName);

                    // オブジェクト名 dwobject 【変数】.印刷設定情報.オブジェクト名
                    choPrt.setDwobject(dto.getDwobject());

                    // 用紙向き prtOrient 【変数】.印刷設定情報.用紙向き
                    choPrt.setPrtOrient(dto.getPrtOrient());

                    // 用紙サイズ prtSize 【変数】.印刷設定情報.用紙サイズ
                    choPrt.setPrtSize(dto.getPrtSize());

                    // 帳票リスト名 listTitle 【変数】.印刷設定情報.帳票リスト名
                    choPrt.setListTitle(dto.getListTitle());

                    // 上余白 mtop 【変数】.印刷設定情報.上余白
                    choPrt.setMtop(dto.getMtop());

                    // 下余白 mbottom 【変数】.印刷設定情報.下余白
                    choPrt.setMbottom(dto.getMbottom());

                    // 左余白 mleft 【変数】.印刷設定情報.左余白
                    choPrt.setMleft(dto.getMleft());

                    // 右余白 mright 【変数】.印刷設定情報.右余白
                    choPrt.setMright(dto.getMright());

                    // ルーラ表示有無 ruler 【変数】.印刷設定情報.ルーラ表示有無
                    choPrt.setRuler(dto.getRuler());

                    // 日付表示有無 prndate 【変数】.印刷設定情報.日付表示有無
                    choPrt.setPrndate(dto.getPrnDate());

                    // 職員表示有無 prnshoku 【変数】.印刷設定情報.職員表示有無
                    choPrt.setPrnshoku(dto.getPrnshoku());

                    // シリアルフラグ serialFlg 【変数】.印刷設定情報.シリアルフラグ
                    choPrt.setSerialFlg(dto.getSerialFlg());

                    // モードフラグ modFlg 【変数】.印刷設定情報.モードフラグ
                    choPrt.setModFlg(dto.getModFlg());

                    // セクションフラグ secFlg 【変数】.印刷設定情報.セクションフラグ
                    choPrt.setSecFlg(dto.getSecFlg());

                    // 高さ serialHeight 【変数】.印刷設定情報.高さ
                    choPrt.setSerialHeight(dto.getSerialHeight());

                    // 印刷行数 serialPagelen 【変数】.印刷設定情報.印刷行数
                    choPrt.setSerialPagelen(dto.getSerialPagelen());

                    // 表示内拡大率 zoomRate 【変数】.印刷設定情報.表示内拡大率
                    choPrt.setZoomRate(dto.getZoomRate());

                    // パラメータ01 param01 【変数】.印刷設定情報.パラメータ01
                    choPrt.setParam01(dto.getParam01());

                    // パラメータ02 param02 【変数】.印刷設定情報.パラメータ02
                    choPrt.setParam02(dto.getParam02());

                    // パラメータ03 param03 【変数】.印刷設定情報.パラメータ03
                    choPrt.setParam03(dto.getParam03());

                    // パラメータ04 param04 【変数】.印刷設定情報.パラメータ04
                    choPrt.setParam04(dto.getParam04());

                    // パラメータ05 param05 【変数】.印刷設定情報.パラメータ05
                    choPrt.setParam05(dto.getParam05());

                    // パラメータ06 param06 【変数】.印刷設定情報.パラメータ06
                    choPrt.setParam06(dto.getParam06());

                    // パラメータ07 param07 【変数】.印刷設定情報.パラメータ07
                    choPrt.setParam07(dto.getParam07());

                    // パラメータ08 param08 【変数】.印刷設定情報.パラメータ08
                    choPrt.setParam08(dto.getParam08());

                    // パラメータ09 param09 【変数】.印刷設定情報.パラメータ09
                    choPrt.setParam09(dto.getParam09());

                    // パラメータ10 param10 【変数】.印刷設定情報.パラメータ10
                    choPrt.setParam10(dto.getParam10());

                    // パラメータ11 param11 【変数】.印刷設定情報.パラメータ11
                    choPrt.setParam11(dto.getParam11());

                    // パラメータ12 param12 【変数】.印刷設定情報.パラメータ12
                    choPrt.setParam12(dto.getParam12());

                    // パラメータ13 param13 【変数】.印刷設定情報.パラメータ13
                    choPrt.setParam13(dto.getParam13());

                    // パラメータ14 param14 【変数】.印刷設定情報.パラメータ14
                    choPrt.setParam14(dto.getParam14());

                    // パラメータ15 param15 【変数】.印刷設定情報.パラメータ15
                    choPrt.setParam15(dto.getParam15());

                    // パラメータ16 param16 【変数】.印刷設定情報.パラメータ16
                    choPrt.setParam16(dto.getParam16());

                    // パラメータ17 param17 【変数】.印刷設定情報.パラメータ17
                    choPrt.setParam17(dto.getParam17());

                    // パラメータ18 param18 【変数】.印刷設定情報.パラメータ18
                    choPrt.setParam18(dto.getParam18());

                    // パラメータ19 param19 【変数】.印刷設定情報.パラメータ19
                    choPrt.setParam19(dto.getParam19());

                    // パラメータ20 param20 【変数】.印刷設定情報.パラメータ20
                    choPrt.setParam20(dto.getParam20());

                    // パラメータ21 param21 【変数】.印刷設定情報.パラメータ21
                    choPrt.setParam21(dto.getParam21());

                    // パラメータ22 param22 【変数】.印刷設定情報.パラメータ22
                    choPrt.setParam22(dto.getParam22());

                    // パラメータ23 param23 【変数】.印刷設定情報.パラメータ23
                    choPrt.setParam23(dto.getParam23());

                    // パラメータ24 param24 【変数】.印刷設定情報.パラメータ24
                    choPrt.setParam24(dto.getParam24());

                    // パラメータ25 param25 【変数】.印刷設定情報.パラメータ25
                    choPrt.setParam25(dto.getParam25());

                    // パラメータ26 param26 【変数】.印刷設定情報.パラメータ26
                    choPrt.setParam26(dto.getParam26());

                    // パラメータ27 param27 【変数】.印刷設定情報.パラメータ27
                    choPrt.setParam27(dto.getParam27());

                    // パラメータ28 param28 【変数】.印刷設定情報.パラメータ28
                    choPrt.setParam28(dto.getParam28());

                    // パラメータ29 param29 【変数】.印刷設定情報.パラメータ29
                    choPrt.setParam29(dto.getParam29());

                    // パラメータ30 param30 【変数】.印刷設定情報.パラメータ30
                    choPrt.setParam30(dto.getParam30());

                    // パラメータ31 param31 【変数】.印刷設定情報.パラメータ31
                    choPrt.setParam31(dto.getParam31());

                    // パラメータ32 param32 【変数】.印刷設定情報.パラメータ32
                    choPrt.setParam32(dto.getParam32());

                    // パラメータ33 param33 【変数】.印刷設定情報.パラメータ33
                    choPrt.setParam33(dto.getParam33());

                    // パラメータ34 param34 【変数】.印刷設定情報.パラメータ34
                    choPrt.setParam34(dto.getParam34());

                    // パラメータ35 param35 【変数】.印刷設定情報.パラメータ35
                    choPrt.setParam35(dto.getParam35());

                    // パラメータ36 param36 【変数】.印刷設定情報.パラメータ36
                    choPrt.setParam36(dto.getParam36());

                    // パラメータ37 param37 【変数】.印刷設定情報.パラメータ37
                    choPrt.setParam37(dto.getParam37());

                    // パラメータ38 param38 【変数】.印刷設定情報.パラメータ38
                    choPrt.setParam38(dto.getParam38());

                    // パラメータ39 param39 【変数】.印刷設定情報.パラメータ39
                    choPrt.setParam39(dto.getParam39());

                    // パラメータ40 param40 【変数】.印刷設定情報.パラメータ40
                    choPrt.setParam40(dto.getParam40());

                    // パラメータ41 param41 【変数】.印刷設定情報.パラメータ41
                    choPrt.setParam41(dto.getParam41());

                    // パラメータ42 param42 【変数】.印刷設定情報.パラメータ42
                    choPrt.setParam42(dto.getParam42());

                    // パラメータ43 param43 【変数】.印刷設定情報.パラメータ43
                    choPrt.setParam43(dto.getParam43());

                    // パラメータ44 param44 【変数】.印刷設定情報.パラメータ44
                    choPrt.setParam44(dto.getParam44());

                    // パラメータ45 param45 【変数】.印刷設定情報.パラメータ45
                    choPrt.setParam45(dto.getParam45());

                    // パラメータ46 param46 【変数】.印刷設定情報.パラメータ46
                    choPrt.setParam46(dto.getParam46());

                    // パラメータ47 param47 【変数】.印刷設定情報.パラメータ47
                    choPrt.setParam47(dto.getParam47());

                    // パラメータ48 param48 【変数】.印刷設定情報.パラメータ48
                    choPrt.setParam48(dto.getParam48());

                    // パラメータ49 param49 【変数】.印刷設定情報.パラメータ49
                    choPrt.setParam49(dto.getParam49());

                    // パラメータ50 param50 【変数】.印刷設定情報.パラメータ50
                    choPrt.setParam50(dto.getParam50());
                }
            }

            // 5.2. 帳票IDリストを作成する。
            // 5.2.1. 以下のパラメータに基づいて対応する帳票IDを帳票IDリストに追加する。
            // リクエストパラメータ.タブID
            // リクエストパラメータ.改訂フラグ
            // リクエストパラメータ.作成日

            List<String> prnList = new ArrayList<>();
            // 帳票番号 1
            if (CommonConstants.STR_1.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) < CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_LIFEASSESSMENT1FACESHEETH21REPORT);
            }

            if (CommonConstants.STR_1.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) >= CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_LIFEASSESSMENT1FACESHEETH30REPORT);
            }

            if (CommonConstants.STR_1.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(loginDto.getNinteiFormF())) {
                prnList.add(CommonConstants.STR_LIFEASSESSMENT1FACESHEETR3REPORT);
            }
            // 帳票番号 2
            if (CommonConstants.STR_2.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) < CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_LIFEASSESSMENT23H21REPORT);
            }

            if (CommonConstants.STR_2.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) >= CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_LIFEASSESSMENT23H30REPORT);
            }

            if (CommonConstants.STR_2.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(loginDto.getNinteiFormF())) {
                prnList.add(CommonConstants.STR_LIFEASSESSMENT23R3REPORT);
            }
            // 帳票番号 3
            if (CommonConstants.STR_3.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) < CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_LIFEASSESSMENT34H21REPORT);
            }

            if (CommonConstants.STR_3.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) >= CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_LIFEASSESSMENT34H30REPORT);
            }

            if (CommonConstants.STR_3.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(loginDto.getNinteiFormF())) {
                prnList.add(CommonConstants.STR_LIFEASSESSMENT34R3REPORT);
            }

            // 帳票番号 4
            if (CommonConstants.STR_4.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) < CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_LIFEASSESSMENT5H21REPORT);
            }

            if (CommonConstants.STR_4.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) >= CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_LIFEASSESSMENT5H30REPORT);
            }

            if (CommonConstants.STR_4.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(loginDto.getNinteiFormF())) {
                prnList.add(CommonConstants.STR_LIFEASSESSMENT5R3REPORT);
            }

            // 帳票番号 5
            if (CommonConstants.STR_5.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) < CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT1H21REPORT);
            }

            if (CommonConstants.STR_5.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) >= CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT1H30REPORT);
            }

            if (CommonConstants.STR_5.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(loginDto.getNinteiFormF())) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT1R3REPORT);
            }

            // 帳票番号 6
            if (CommonConstants.STR_6.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) < CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT2REPORTSERVICE);
            }

            if (CommonConstants.STR_6.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) >= CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT2R3REPORTSERVICE);
            }

            if (CommonConstants.STR_6.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(loginDto.getNinteiFormF())) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT2R3REPORTSERVICE);
            }

            // 帳票番号 7
            if (CommonConstants.STR_7.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) < CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT34H21REPORT);
            }

            if (CommonConstants.STR_7.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) >= CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT34H30REPORT);
            }

            if (CommonConstants.STR_7.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(loginDto.getNinteiFormF())) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT34R3REPORT);
            }

            // 帳票番号 8
            if (CommonConstants.STR_8.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) < CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT5REPORT);
            }

            if (CommonConstants.STR_8.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) >= CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT5R3REPORT);
            }

            if (CommonConstants.STR_8.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(loginDto.getNinteiFormF())) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT5R3REPORT);
            }

            // 帳票番号 9
            if (CommonConstants.STR_9.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) < CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT6REPORT);
            }

            if (CommonConstants.STR_9.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) >= CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT6R3REPORT);
            }

            if (CommonConstants.STR_9.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(loginDto.getNinteiFormF())) {
                prnList.add(CommonConstants.STR_CAREASSESSMENT6R3REPORT);
            }

            // 帳票番号 10
            if (CommonConstants.STR_10.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) < CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_ONEDAYSCHEDULEH21REPORT);
            }

            if (CommonConstants.STR_10.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) >= CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_ONEDAYSCHEDULEH30REPORT);
            }

            if (CommonConstants.STR_10.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(loginDto.getNinteiFormF())) {
                prnList.add(CommonConstants.STR_ONEDAYSCHEDULER3REPORT);
            }

            // 帳票番号 11
            if (CommonConstants.STR_11.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) < CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_HOMEOVERALLSUMMARYH21REPORT);
            }

            if (CommonConstants.STR_11.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                    && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) >= CommonConstants.INT_0) {
                prnList.add(CommonConstants.STR_HOMEOVERALLSUMMARYH30REPORT);
            }

            if (CommonConstants.STR_11.equals(index)
                    && CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(loginDto.getNinteiFormF())) {
                prnList.add(CommonConstants.STR_HOMEOVERALLSUMMARYR3REPORT);
            }

            // 5.2.2. リクエストパラメータ.タブIDが「3」の場合、以下のパラメータに基づいて対応する帳票IDを帳票IDリストに追加する。
            if (CommonConstants.TAB_ID_3.equals(loginDto.getTabId())) {
                // 帳票番号が「2」
                // リクエストパラメータ.改訂フラグ
                // リクエストパラメータ.作成日
                if (CommonConstants.STR_2.equals(index)
                        && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                        && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) < CommonConstants.INT_0) {
                    prnList.add(CommonConstants.STR_LIFEASSESSMENT23H21REPORT);
                }

                if (CommonConstants.STR_2.equals(index)
                        && CommonConstants.KAI_TEI_FLAG_H21_4.equals(loginDto.getNinteiFormF())
                        && loginDto.getKijunbiYmd().compareTo(CommonConstants.DATA_20180401) >= CommonConstants.INT_0) {
                    prnList.add(CommonConstants.STR_LIFEASSESSMENT23H30REPORT);
                }

                if (CommonConstants.STR_2.equals(index)
                        && CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(loginDto.getNinteiFormF())) {
                    prnList.add(CommonConstants.STR_LIFEASSESSMENT23R3REPORT);
                }
            }

            // 5.3. 「5.2」で作成した帳票IDリストの件数分、e文書を出力する。
            for (String str : prnList) {

                // 5.3.1. リクエストパラメータ.e文書用パラメータの「帳票ID」が該当帳票IDを設定する
                edocumentUseParam.setReportId(str);
                // 帳票ID 該当帳票ID
                // 帳票サービスInDto リクエストパラメータ.e文書用パラメータ
                // ログインID リクエストパラメータ.ログインID
                // E文書更新区分 リクエストパラメータ.更新区分
                // 履歴番号 リクエストパラメータ.履歴番号
                // 計画対象期間ID 【変数】.計画期間ID
                try {
                    cmnStorageServiceUtil.uploadToS3EDocument(str,
                            edocumentUseParam, loginDto.getLoginId(), loginDto.getUpdateKbn(),
                            loginDto.getKrirekiNo(), sc1Id);
                } catch (Exception e) {
                    // 5.3.4.上記の処理に異常が発生した場合、レスポンス.エラー区分が「2」を設定し、「API定義」の4の処理へジャンプする。
                    return CommonConstants.E_DOC_OUT_ERR;
                }

            }

        }
        return CommonConstants.BLANK_STRING;
    }
}
