package jp.ndsoft.carebase.cmn.api.logic;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.dto.GetProfile2InDto;
import jp.ndsoft.carebase.common.dao.mybatis.ComMscSvjigyoNameMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMscSvjigyoName;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMscSvjigyoNameCriteria;

/**
 * N3gkComBaseOperateFuncLogicロジッククラス
 * 
 * <AUTHOR>
 */
@Component
public class N3gkComBaseOperateFuncLogic {

    /** 画面名: R4 */
    private static final String KINOU_NAME_R4 = "R4";
    /** Section: DEFAULT_MENU */
    private static final String SECTION_DEFAULT_MENU = "DEFAULT_MENU";
    /** Key */
    private static final String KEY = "事業種別開放";
    /** param_knj: 0 */
    private static final String PARAM_KNJ_DEF = "0";
    /** param_knj: 1 */
    private static final String PARAM_KNJ_1 = "1";
    /** サービス事業者コードマスタ(15-8) */
    @Autowired
    private ComMscSvjigyoNameMapper comMscSvjigyoNameMapper;
    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;
    /** KghkrkBase01Logicロジッククラス */
    // @Autowired
    private KghBase01Logic kghBase01Logic;

    /**
     * 特殊な設定：all→全事業種別ok
     * 関数名：uf_com_kinou_jcds
     * 
     * @param kinouId     機能ID
     * @param ulistRetPtn ai_ulist_ret_ptn
     * @param gsyscd      システムコード
     * 
     * @return 事業所のjcd
     */
    public List<String> setComKinouJcds(Integer kinouId, Integer ulistRetPtn, String gsyscd) {
        // 戻り値変数を定義
        List<String> jcds = new ArrayList<>();
        GetProfile2InDto getProfile2Dto = new GetProfile2InDto();
        getProfile2Dto.setShokuId(0);
        getProfile2Dto.setHid(0);
        getProfile2Dto.setSid(0);
        getProfile2Dto.setJid(0);
        getProfile2Dto.setKinouName(KINOU_NAME_R4);
        getProfile2Dto.setSection(SECTION_DEFAULT_MENU);
        getProfile2Dto.setKey(KEY);
        getProfile2Dto.setDefValue(PARAM_KNJ_DEF);
        getProfile2Dto.setGsyscd(gsyscd);
        String paramKnj = nds3GkFunc01Logic.getProfile2(getProfile2Dto);
        boolean jigyoOpenFlg = paramKnj.equals(PARAM_KNJ_1);

        if (ulistRetPtn == -1) {
            // 不可
            jcds.add(StringUtils.EMPTY);
        } else if (ulistRetPtn == 0) {
            // 抽出不要
            jcds.add(CommonConstants.JCD_ALL);
        } else if (ulistRetPtn == 1 || ulistRetPtn == 3) {
            // 全(初期フィルタ不要）
            jcds.add(CommonConstants.JCD_ALL);
        } else if (ulistRetPtn == 2) {
            // 適用事業所の利用者 今日有効な、適用事業所の利用登録
            jcds.add(CommonConstants.JCD_ALL);
        } else if (ulistRetPtn == 4) {
            // 2と同じで、「予定者」フィルタを出す
            jcds.add(CommonConstants.JCD_ALL);
        } else if (ulistRetPtn == 5) {
            // 9と同じで、「予定者」フィルタを出す
            jcds.addAll(List.of(CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_61084,
                    CommonConstants.JCD_71084, CommonConstants.JCD_30010, CommonConstants.JCD_50010,
                    CommonConstants.JCD_61104));
        } else if (ulistRetPtn == 6) {
            // 請求の提供票 未使用？
            jcds.addAll(List.of(CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032, CommonConstants.JCD_42032,
                    CommonConstants.JCD_63022, CommonConstants.JCD_73022, CommonConstants.JCD_21053,
                    CommonConstants.JCD_41053, CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_71053,
                    CommonConstants.JCD_21010, CommonConstants.JCD_41010, CommonConstants.JCD_61010,
                    CommonConstants.JCD_21020, CommonConstants.JCD_41020, CommonConstants.JCD_21040,
                    CommonConstants.JCD_41040, CommonConstants.JCD_21030,
                    CommonConstants.JCD_41030, CommonConstants.JCD_61084, CommonConstants.JCD_71084,
                    CommonConstants.JCD_21070, CommonConstants.JCD_41070, CommonConstants.JCD_23010,
                    CommonConstants.JCD_43010, CommonConstants.JCD_A1010,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_61113, CommonConstants.JCD_63084,
                    CommonConstants.JCD_73084, CommonConstants.JCD_63104, CommonConstants.JCD_22042,
                    CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 7) {
            // 今月有効な適用事業所のケアマネ利用登録
            jcds.addAll(List.of(CommonConstants.JCD_30010, CommonConstants.JCD_50010));
        } else if (ulistRetPtn == 8) {
            // 今月有効な適用事業所のケアマネ利用登録
            jcds.addAll(List.of(CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_30010,
                    CommonConstants.JCD_50010, CommonConstants.JCD_61104));
        } else if (ulistRetPtn == 9) {
            // 今月有効な適用事業所のケアマネ利用登録
            jcds.addAll(List.of(CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_61084,
                    CommonConstants.JCD_71084, CommonConstants.JCD_30010, CommonConstants.JCD_50010,
                    CommonConstants.JCD_61104));
        } else if (ulistRetPtn == 10) {
            // 今月有効な適用事業所の看護利用登録
            jcds.addAll(List.of(CommonConstants.JCD_21030, CommonConstants.JCD_41030));
        } else if (ulistRetPtn == 11) {
            // 今月有効な適用事業所の貸与利用登録+「★その他★」としてuserid = 0扱いの行をリスト一番上に表示。
            jcds.addAll(List.of(CommonConstants.JCD_21070, CommonConstants.JCD_41070));
        } else if (ulistRetPtn == 12) {
            // 10と同じで、「予定者」フィルタを出す
            jcds.addAll(List.of(CommonConstants.JCD_21030, CommonConstants.JCD_41030));
        } else if (ulistRetPtn == 13) {
            // 今月有効な適用事業所の老健入所、療養短期、予防療養短期
            jcds.addAll(List.of(CommonConstants.JCD_10021, CommonConstants.JCD_22022, CommonConstants.JCD_42022,
                    CommonConstants.JCD_10041, CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 14) {
            // 今月有効な適用事業所の療養入所、療養短期、予防療養短期
            jcds.addAll(List.of(CommonConstants.JCD_10031, CommonConstants.JCD_22032, CommonConstants.JCD_42032));
        } else if (ulistRetPtn == 15) {
            // 今月有効な適用事業所の老健入所、療養短期、予防療養短期且つ施設区分マスタの項目ID＝1がⅡ型又はⅢ型老健※
            jcds.addAll(List.of(CommonConstants.JCD_10021, CommonConstants.JCD_22022, CommonConstants.JCD_42022));
        } else if (ulistRetPtn == 16) {
            // 今月有効または前月退所の適用事業所の利用登録（ケアマネ以外）
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_63061, CommonConstants.JCD_10021,
                    CommonConstants.JCD_10031, CommonConstants.JCD_22012, CommonConstants.JCD_42012,
                    CommonConstants.JCD_22022, CommonConstants.JCD_22032, CommonConstants.JCD_42022,
                    CommonConstants.JCD_42032, CommonConstants.JCD_21053, CommonConstants.JCD_41053,
                    CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_71053, CommonConstants.JCD_21010,
                    CommonConstants.JCD_21020, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_61010, CommonConstants.JCD_41010,
                    CommonConstants.JCD_41020, CommonConstants.JCD_23021, CommonConstants.JCD_63022,
                    CommonConstants.JCD_43021, CommonConstants.JCD_73022, CommonConstants.JCD_23031,
                    CommonConstants.JCD_63031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_21030,
                    CommonConstants.JCD_41030, CommonConstants.JCD_21070, CommonConstants.JCD_41070,
                    CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_23040, CommonConstants.JCD_43040, CommonConstants.JCD_23050,
                    CommonConstants.JCD_43050, CommonConstants.JCD_23062, CommonConstants.JCD_61090,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112,
                    CommonConstants.JCD_A1010, CommonConstants.JCD_A5013, CommonConstants.JCD_A9010,
                    CommonConstants.JCD_61113, CommonConstants.JCD_63084, CommonConstants.JCD_73084,
                    CommonConstants.JCD_63104, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
            // 横だし事業所のjcdを取得する（全部付加される）
            if (CommonConstants.SYSTEM_CODE_71101.equals(gsyscd) && kinouId != 47016003) {
                getYokodasiJcds(jcds);
            }
        } else if (ulistRetPtn == 17) {
            // 今月有効且つ本部グループで未入金請求書 未使用？
            jcds.add(StringUtils.EMPTY);
        } else if (ulistRetPtn == 18) {
            // 今月有効または前月退所の適用事業所の利用登録（施設系：グケ 認知短期）光熱費
            jcds.addAll(List.of(CommonConstants.JCD_23021, CommonConstants.JCD_43021, CommonConstants.JCD_23031,
                    CommonConstants.JCD_43031, CommonConstants.JCD_63031, CommonConstants.JCD_63022,
                    CommonConstants.JCD_73022, CommonConstants.JCD_23062, CommonConstants.JCD_63112));
        } else if (ulistRetPtn == 19) {
            // 今月有効の適用事業所の利用登録（施設系：ケ） 事務費
            jcds.addAll(List.of(CommonConstants.JCD_23031, CommonConstants.JCD_63031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_23062, CommonConstants.JCD_63112));
        } else if (ulistRetPtn == 20) {
            // 今月有効または前月退所の適用事業所の利用登録 サービス費
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_10041));
        } else if (ulistRetPtn == 21) {
            // 今月有効または前月退所の適用事業所の利用登録 提供票（利用状況の提供票画面：貸与、ケアマネ除く）
            jcds.addAll(List.of(CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032, CommonConstants.JCD_42032,
                    CommonConstants.JCD_63022, CommonConstants.JCD_73022, CommonConstants.JCD_21053,
                    CommonConstants.JCD_41053, CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_71053,
                    CommonConstants.JCD_21010, CommonConstants.JCD_41010, CommonConstants.JCD_61010,
                    CommonConstants.JCD_21020, CommonConstants.JCD_41020, CommonConstants.JCD_21040,
                    CommonConstants.JCD_41040, CommonConstants.JCD_21030,
                    CommonConstants.JCD_41030, CommonConstants.JCD_61084, CommonConstants.JCD_71084,
                    CommonConstants.JCD_21070, CommonConstants.JCD_41070, CommonConstants.JCD_23010,
                    CommonConstants.JCD_43010, CommonConstants.JCD_23062,
                    CommonConstants.JCD_61090, CommonConstants.JCD_61104, CommonConstants.JCD_63112,
                    CommonConstants.JCD_A1010, CommonConstants.JCD_A5013, CommonConstants.JCD_61113,
                    CommonConstants.JCD_63084, CommonConstants.JCD_73084,
                    CommonConstants.JCD_63104, CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 22) {
            // 入金 ds抽出
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_63061, CommonConstants.JCD_10021,
                    CommonConstants.JCD_10031, CommonConstants.JCD_22012, CommonConstants.JCD_42012,
                    CommonConstants.JCD_22022, CommonConstants.JCD_22032, CommonConstants.JCD_42022,
                    CommonConstants.JCD_42032, CommonConstants.JCD_21053, CommonConstants.JCD_41053,
                    CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_71053, CommonConstants.JCD_21010,
                    CommonConstants.JCD_21020, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_61010, CommonConstants.JCD_41010,
                    CommonConstants.JCD_41020, CommonConstants.JCD_23021, CommonConstants.JCD_63022,
                    CommonConstants.JCD_43021, CommonConstants.JCD_73022, CommonConstants.JCD_23031,
                    CommonConstants.JCD_63031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_21030,
                    CommonConstants.JCD_41030, CommonConstants.JCD_21070, CommonConstants.JCD_41070,
                    CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_23040, CommonConstants.JCD_43040, CommonConstants.JCD_23050,
                    CommonConstants.JCD_43050, CommonConstants.JCD_23062, CommonConstants.JCD_61090,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112,
                    CommonConstants.JCD_A1010, CommonConstants.JCD_A5013, CommonConstants.JCD_A9010,
                    CommonConstants.JCD_61113, CommonConstants.JCD_63084, CommonConstants.JCD_73084,
                    CommonConstants.JCD_63104, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
            // 横だし事業所のjcdを取得する（全部付加される）
            if (CommonConstants.SYSTEM_CODE_71101.equals(gsyscd)) {
                getYokodasiJcds(jcds);
            }
        } else if (ulistRetPtn == 23) {
            // 今月有効の適用事業所の利用登録 予防短期 (予防通所介護 予防通所リハ 予防訪問介護)
            jcds.addAll(List.of(CommonConstants.JCD_41053, CommonConstants.JCD_41063, CommonConstants.JCD_41010,
                    CommonConstants.JCD_21030, CommonConstants.JCD_61090, CommonConstants.JCD_A1010,
                    CommonConstants.JCD_A5013));
        } else if (ulistRetPtn == 24) {
            // 情報提供日今月有効な適用事業所の特定施設入居者生活介護（外部利用型含まない）,介護予防特定施設入居者生活介護（外部利用型含まない※
            jcds.addAll(List.of(CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031));
        } else if (ulistRetPtn == 25) {
            // リストなし 通所介護(0?)
            jcds.add(CommonConstants.JCD_21053);
        } else if (ulistRetPtn == 26) {
            // リストなし 小規模、予防小規模(0?)、密着通所介護
            jcds.addAll(List.of(CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_61104,
                    CommonConstants.JCD_61113));
        } else if (ulistRetPtn == 27) {
            // 本部入金 ds抽出
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_63061, CommonConstants.JCD_10021,
                    CommonConstants.JCD_10031, CommonConstants.JCD_22012, CommonConstants.JCD_42012,
                    CommonConstants.JCD_22022, CommonConstants.JCD_22032, CommonConstants.JCD_42022,
                    CommonConstants.JCD_42032, CommonConstants.JCD_21053, CommonConstants.JCD_41053,
                    CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_71053, CommonConstants.JCD_21010,
                    CommonConstants.JCD_21020, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_61010, CommonConstants.JCD_41010,
                    CommonConstants.JCD_41020, CommonConstants.JCD_23021, CommonConstants.JCD_63022,
                    CommonConstants.JCD_43021, CommonConstants.JCD_73022, CommonConstants.JCD_23031,
                    CommonConstants.JCD_63031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_21030,
                    CommonConstants.JCD_41030, CommonConstants.JCD_21070, CommonConstants.JCD_41070,
                    CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_23040, CommonConstants.JCD_43040, CommonConstants.JCD_23050,
                    CommonConstants.JCD_43050, CommonConstants.JCD_23062, CommonConstants.JCD_61090,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112,
                    CommonConstants.JCD_A1010, CommonConstants.JCD_A5013, CommonConstants.JCD_A9010,
                    CommonConstants.JCD_61113, CommonConstants.JCD_63084, CommonConstants.JCD_73084,
                    CommonConstants.JCD_63104, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
            // 横だし事業所のjcdを取得する（全部付加される）
            if (CommonConstants.SYSTEM_CODE_71101.equals(gsyscd)) {
                getYokodasiJcds(jcds);
            }
        } else if (ulistRetPtn == 28) {
            // 他科受診 保健施設 短期老健 予防短期老健 療養施設 短期医養 予防短期医療
            jcds.addAll(List.of(CommonConstants.JCD_10021, CommonConstants.JCD_22022, CommonConstants.JCD_42022,
                    CommonConstants.JCD_10031, CommonConstants.JCD_22032, CommonConstants.JCD_42032,
                    CommonConstants.JCD_10041, CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 29) {
            // 未使用
            jcds.add(StringUtils.EMPTY);
        } else if (ulistRetPtn == 30) {
            // 薬剤費 保健施設 通所リハ 短期老健 予防通所リハ 予防短期老健
            jcds.addAll(List.of(CommonConstants.JCD_10021, CommonConstants.JCD_21063, CommonConstants.JCD_22022,
                    CommonConstants.JCD_41063, CommonConstants.JCD_42022, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 31) {
            // 欠食状況 福祉施設 短期生活 予防短期生活 密着福祉施設 保健施設 短期老健 予防短期老健 療養施設
            // 短期医養 予防短期医療 認知対応 認知短期 予防認知対応 予防認知短期 特定入居 予防特定入居 密着特定入居
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_23062,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112,
                    CommonConstants.JCD_63084, CommonConstants.JCD_73084, CommonConstants.JCD_63104,
                    CommonConstants.JCD_10041, CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 32) {
            // 利用料 横だし含むケアマネ以外 90000以降すべてを抽出して返すか、90000を返して戻された方で判断する。
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_63061, CommonConstants.JCD_10021,
                    CommonConstants.JCD_10031, CommonConstants.JCD_22012, CommonConstants.JCD_42012,
                    CommonConstants.JCD_22022, CommonConstants.JCD_22032, CommonConstants.JCD_42022,
                    CommonConstants.JCD_42032, CommonConstants.JCD_21053, CommonConstants.JCD_41053,
                    CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_71053, CommonConstants.JCD_21010,
                    CommonConstants.JCD_21020, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_61010, CommonConstants.JCD_41010,
                    CommonConstants.JCD_41020, CommonConstants.JCD_23021, CommonConstants.JCD_63022,
                    CommonConstants.JCD_43021, CommonConstants.JCD_73022, CommonConstants.JCD_23031,
                    CommonConstants.JCD_63031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_21030,
                    CommonConstants.JCD_41030, CommonConstants.JCD_21070, CommonConstants.JCD_41070,
                    CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_23040, CommonConstants.JCD_43040, CommonConstants.JCD_23050,
                    CommonConstants.JCD_43050, CommonConstants.JCD_23062, CommonConstants.JCD_61090,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112,
                    CommonConstants.JCD_A1010, CommonConstants.JCD_A5013, CommonConstants.JCD_A9010,
                    CommonConstants.JCD_61113, CommonConstants.JCD_63084, CommonConstants.JCD_73084,
                    CommonConstants.JCD_63104, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
            // 横だし事業所のjcdを取得する（全部付加される）
            if (CommonConstants.SYSTEM_CODE_71101.equals(gsyscd)) {
                getYokodasiJcds(jcds);
            }
        } else if (ulistRetPtn == 33) {
            // 月次情報※ 通所介護 短期生活 予防通所介護 予防短期生活 認知通所 予防認知通所 通所リハ 短期老健
            // 予防通所リハ 予防短期老健 短期医養 予防短期医療 小規模 予防小規模 認知短期 予防認知短期 特定入居
            // 予防特定入居 訪問介護 訪問入浴介護 訪問看護 訪問リハビリテーション 居宅療養管理指導 介護予防訪問介護
            // 介護予防訪問入浴介護 介護予防訪問看護 介護予防訪問リハビリテーション 介護予防居宅療養管理指導
            // 夜間対応型訪問介護 福祉用具貸与 予防用具貸与
            jcds.addAll(List.of(CommonConstants.JCD_43031, CommonConstants.JCD_22012, CommonConstants.JCD_42012,
                    CommonConstants.JCD_22022, CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_21053, CommonConstants.JCD_41053,
                    CommonConstants.JCD_21063, CommonConstants.JCD_41063, CommonConstants.JCD_61053,
                    CommonConstants.JCD_71053, CommonConstants.JCD_21010,
                    CommonConstants.JCD_41010, CommonConstants.JCD_61010, CommonConstants.JCD_21020,
                    CommonConstants.JCD_41020, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_21030, CommonConstants.JCD_41030,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_21070,
                    CommonConstants.JCD_41070, CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_23031, CommonConstants.JCD_63022,
                    CommonConstants.JCD_73022, CommonConstants.JCD_23062, CommonConstants.JCD_61090,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112, CommonConstants.JCD_23021,
                    CommonConstants.JCD_43021, CommonConstants.JCD_63031,
                    CommonConstants.JCD_A1010, CommonConstants.JCD_A5013, CommonConstants.JCD_61113,
                    CommonConstants.JCD_63084, CommonConstants.JCD_73084, CommonConstants.JCD_63104,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 34) {
            // 今月有効の適用事業所の利用登録（ケアマネ以外）
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053,
                    CommonConstants.JCD_71053, CommonConstants.JCD_21010, CommonConstants.JCD_41010,
                    CommonConstants.JCD_61010, CommonConstants.JCD_21020, CommonConstants.JCD_41020,
                    CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_21030, CommonConstants.JCD_41030, CommonConstants.JCD_61084,
                    CommonConstants.JCD_71084, CommonConstants.JCD_21070, CommonConstants.JCD_41070,
                    CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_23062, CommonConstants.JCD_61090, CommonConstants.JCD_61104,
                    CommonConstants.JCD_63112, CommonConstants.JCD_A1010, CommonConstants.JCD_A5013,
                    CommonConstants.JCD_61113, CommonConstants.JCD_63084,
                    CommonConstants.JCD_73084, CommonConstants.JCD_63104, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
            // 横だし事業所のjcdを取得する（全部付加される）
            if (CommonConstants.SYSTEM_CODE_71101.equals(gsyscd) && kinouId == 45036003) {
                getYokodasiJcds(jcds);
            }
        } else if (ulistRetPtn == 35) {
            // 高額介護サービス費 老健、ケアハウス、特養
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_63031, CommonConstants.JCD_10041));
        } else if (ulistRetPtn == 36) {
            // 老健療養短期、介護請求基本 訪問介護、予防訪問介護、夜間訪問介護老人保健施設、老人福祉施設、療養型施設、
            // 密着福祉施設居宅療養管理、予防居宅療養、予防訪問リハ、訪問リハビリ特定施設入居、予防特定入居、密着特定入居、
            // 予防認知短期、認知症対応型、予防認知対応、認知短期利用 福祉用具貸与、予防福祉用具貸与看護、予防看護
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22022, CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022,
                    CommonConstants.JCD_73022, CommonConstants.JCD_21010, CommonConstants.JCD_41010,
                    CommonConstants.JCD_61010, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_21030, CommonConstants.JCD_41030,
                    CommonConstants.JCD_21070, CommonConstants.JCD_41070, CommonConstants.JCD_23010,
                    CommonConstants.JCD_43010, CommonConstants.JCD_23062, CommonConstants.JCD_61090,
                    CommonConstants.JCD_63112, CommonConstants.JCD_21063,
                    CommonConstants.JCD_22012, CommonConstants.JCD_10041, CommonConstants.JCD_22042,
                    CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 37) {
            // 総合情報,部屋移動、月間在室状況、外泊登録
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_63061, CommonConstants.JCD_22012,
                    CommonConstants.JCD_42012, CommonConstants.JCD_10021, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_10031, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_23031, CommonConstants.JCD_63031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_23062, CommonConstants.JCD_63112, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 38) {
            // 請求＞事業所加減算＞定超・欠員
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053,
                    CommonConstants.JCD_71053, CommonConstants.JCD_61084, CommonConstants.JCD_71084,
                    CommonConstants.JCD_23062, CommonConstants.JCD_61104, CommonConstants.JCD_63112,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_61113,
                    CommonConstants.JCD_63084, CommonConstants.JCD_73084, CommonConstants.JCD_63104,
                    CommonConstants.JCD_10041, CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 39) {
            // 通所ｽｹｼﾞｭｰﾙ、通所パターン
            jcds.addAll(List.of(CommonConstants.JCD_21053, CommonConstants.JCD_21063, CommonConstants.JCD_41053,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053, CommonConstants.JCD_71053,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_61113));
        } else if (ulistRetPtn == 40) {
            // 勤務時間、稼働時間、重複確認、職員自動配置、職員ｽｹｼﾞｭｰﾙ、利用者ｽｹｼﾞｭｰﾙ、自立支援連動、派遣ﾊﾟﾀｰﾝ
            jcds.addAll(List.of(CommonConstants.JCD_21010, CommonConstants.JCD_41010, CommonConstants.JCD_61010,
                    CommonConstants.JCD_21020, CommonConstants.JCD_41020, CommonConstants.JCD_21030,
                    CommonConstants.JCD_41030, CommonConstants.JCD_61090, CommonConstants.JCD_A1010));
            // 利用ｽｹｼﾞｭｰﾙ
            if (CommonConstants.SYSTEM_CODE_71101.equals(gsyscd) && CommonConstants.AL_KINOU_ID_44005001 == kinouId) {
                if (!kghBase01Logic.chkSeihin(CommonConstants.INT_5)) {
                    if (kghBase01Logic.chkSeihin(CommonConstants.GROUP_NUM_62)
                            || kghBase01Logic.chkSeihin(CommonConstants.GROUP_NUM_63)) {
                        // 訪問看護のみ
                        jcds.addAll(List.of(CommonConstants.JCD_21030));
                    }
                }
            }
        } else if (ulistRetPtn == 41) {
            // 預り金 入所系
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_23062, CommonConstants.JCD_63112, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 42) {
            // 小規模
            jcds.addAll(List.of(CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_61104,
                    CommonConstants.JCD_63084, CommonConstants.JCD_73084, CommonConstants.JCD_63104));
        } else if (ulistRetPtn == 43) {
            // 今月有効の適用事業所の利用登録（施設系：グケ 認知）一時金
            jcds.addAll(List.of(CommonConstants.JCD_23021, CommonConstants.JCD_43021, CommonConstants.JCD_23031,
                    CommonConstants.JCD_43031, CommonConstants.JCD_63031, CommonConstants.JCD_63022,
                    CommonConstants.JCD_73022));
        } else if (ulistRetPtn == 45) {
            // 個人加算
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053,
                    CommonConstants.JCD_71053, CommonConstants.JCD_21030, CommonConstants.JCD_41030,
                    CommonConstants.JCD_61084, CommonConstants.JCD_30010, CommonConstants.JCD_50010,
                    CommonConstants.JCD_61104, CommonConstants.JCD_A5013,
                    CommonConstants.JCD_61113, CommonConstants.JCD_10041, CommonConstants.JCD_22042,
                    CommonConstants.JCD_42042, CommonConstants.JCD_23062, CommonConstants.JCD_63112,
                    CommonConstants.JCD_63084, CommonConstants.JCD_71084,
                    CommonConstants.JCD_73084, CommonConstants.JCD_63104));
        } else if (ulistRetPtn == 46) {
            // 未使用？
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_63061, CommonConstants.JCD_22012,
                    CommonConstants.JCD_42012, CommonConstants.JCD_10021, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_10031,
                    CommonConstants.JCD_22032, CommonConstants.JCD_42032, CommonConstants.JCD_23021,
                    CommonConstants.JCD_43021, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_23031, CommonConstants.JCD_63031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_23062, CommonConstants.JCD_63112, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 47) {
            // デイ系（ケア記録）
            jcds.addAll(List.of(CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053, CommonConstants.JCD_71053,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_61113));
        } else if (ulistRetPtn == 48) {
            // 未使用？
            jcds.add(CommonConstants.JCD_50010);
        } else if (ulistRetPtn == 49) {
            // その他（ケア記録）
            jcds.addAll(List.of(CommonConstants.JCD_21010, CommonConstants.JCD_41010, CommonConstants.JCD_61010,
                    CommonConstants.JCD_21020, CommonConstants.JCD_41020, CommonConstants.JCD_21040,
                    CommonConstants.JCD_41040, CommonConstants.JCD_21030, CommonConstants.JCD_41030,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_21070,
                    CommonConstants.JCD_41070, CommonConstants.JCD_23010,
                    CommonConstants.JCD_43010, CommonConstants.JCD_30010, CommonConstants.JCD_50010,
                    CommonConstants.JCD_61090, CommonConstants.JCD_61104, CommonConstants.JCD_A1010,
                    CommonConstants.JCD_63084, CommonConstants.JCD_73084,
                    CommonConstants.JCD_63104));

            if (CommonConstants.SYSTEM_CODE_71101.equals(gsyscd)) {
                getYokodasiJcds(jcds);
            }
        } else if (ulistRetPtn == 50) {
            // 未使用？
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_63061, CommonConstants.JCD_22012,
                    CommonConstants.JCD_42012, CommonConstants.JCD_10021, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_10031,
                    CommonConstants.JCD_22032, CommonConstants.JCD_42032, CommonConstants.JCD_23021,
                    CommonConstants.JCD_43021, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_23031, CommonConstants.JCD_63031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053, CommonConstants.JCD_71053,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_21070,
                    CommonConstants.JCD_41070, CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_10041, CommonConstants.JCD_22042,
                    CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 51) {
            jcds.add(StringUtils.EMPTY);
        } else if (ulistRetPtn == 52) {
            jcds.addAll(List.of(CommonConstants.JCD_21010, CommonConstants.JCD_41010, CommonConstants.JCD_61010,
                    CommonConstants.JCD_61090, CommonConstants.JCD_A1010));
        } else if (ulistRetPtn == 53) {
            jcds.add(StringUtils.EMPTY);
        } else if (ulistRetPtn == 54) {
            jcds.add(StringUtils.EMPTY);
        } else if (ulistRetPtn == 55) {
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_63061, CommonConstants.JCD_10021,
                    CommonConstants.JCD_10031, CommonConstants.JCD_22012, CommonConstants.JCD_42012,
                    CommonConstants.JCD_22022, CommonConstants.JCD_22032, CommonConstants.JCD_42022,
                    CommonConstants.JCD_42032, CommonConstants.JCD_21053, CommonConstants.JCD_41053,
                    CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_71053, CommonConstants.JCD_21010,
                    CommonConstants.JCD_21020, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_61010, CommonConstants.JCD_41010,
                    CommonConstants.JCD_41020, CommonConstants.JCD_23021, CommonConstants.JCD_63022,
                    CommonConstants.JCD_43021, CommonConstants.JCD_73022, CommonConstants.JCD_23031,
                    CommonConstants.JCD_63031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_21030,
                    CommonConstants.JCD_41030, CommonConstants.JCD_21070, CommonConstants.JCD_41070,
                    CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_23040, CommonConstants.JCD_43040, CommonConstants.JCD_23050,
                    CommonConstants.JCD_43050, CommonConstants.JCD_23062, CommonConstants.JCD_61090,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112,
                    CommonConstants.JCD_30010, CommonConstants.JCD_50010, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
            // 横だし事業所のjcdを取得する（全部付加される）
            if (CommonConstants.SYSTEM_CODE_71101.equals(gsyscd)) {
                getYokodasiJcds(jcds);
            }
        } else if (ulistRetPtn == 56) {
            // 未使用？
            jcds.addAll(List.of(
                    CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_63031, CommonConstants.JCD_22012, CommonConstants.JCD_42012,
                    CommonConstants.JCD_22022, CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022,
                    CommonConstants.JCD_73022, CommonConstants.JCD_21053, CommonConstants.JCD_41053,
                    CommonConstants.JCD_21063, CommonConstants.JCD_41063, CommonConstants.JCD_61053,
                    CommonConstants.JCD_71053, CommonConstants.JCD_21010,
                    CommonConstants.JCD_41010, CommonConstants.JCD_61010, CommonConstants.JCD_21020,
                    CommonConstants.JCD_41020, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_21030, CommonConstants.JCD_41030,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_21070,
                    CommonConstants.JCD_41070, CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_30010, CommonConstants.JCD_23062,
                    CommonConstants.JCD_61090, CommonConstants.JCD_61104, CommonConstants.JCD_63112,
                    CommonConstants.JCD_A1010, CommonConstants.JCD_A5013, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 57) {
            jcds.add(StringUtils.EMPTY);
        } else if (ulistRetPtn == 58) {
            jcds.add(StringUtils.EMPTY);
        } else if (ulistRetPtn == 59) {
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_21030, CommonConstants.JCD_41030, CommonConstants.JCD_23062,
                    CommonConstants.JCD_61090, CommonConstants.JCD_61104,
                    CommonConstants.JCD_63112, CommonConstants.JCD_63104, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 60) {
            // 未使用？
            jcds.addAll(List.of(
                    CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_63031, CommonConstants.JCD_23062, CommonConstants.JCD_63112,
                    CommonConstants.JCD_10041));
        } else if (ulistRetPtn == 61) {
            jcds.add(CommonConstants.JCD_21010);
        } else if (ulistRetPtn == 62) {
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_21030, CommonConstants.JCD_41030, CommonConstants.JCD_61084,
                    CommonConstants.JCD_71084, CommonConstants.JCD_23062,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112, CommonConstants.JCD_63084,
                    CommonConstants.JCD_73084, CommonConstants.JCD_63104, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 63) {
            jcds.add(StringUtils.EMPTY);
        } else if (ulistRetPtn == 64) {
            // 34のnyuusyoreg版
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_63061, CommonConstants.JCD_10021,
                    CommonConstants.JCD_10031, CommonConstants.JCD_22012, CommonConstants.JCD_42012,
                    CommonConstants.JCD_22022, CommonConstants.JCD_22032, CommonConstants.JCD_42022,
                    CommonConstants.JCD_42032, CommonConstants.JCD_21053, CommonConstants.JCD_41053,
                    CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_71053, CommonConstants.JCD_21010,
                    CommonConstants.JCD_21020, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_61010, CommonConstants.JCD_41010,
                    CommonConstants.JCD_41020, CommonConstants.JCD_23021, CommonConstants.JCD_63022,
                    CommonConstants.JCD_43021, CommonConstants.JCD_73022, CommonConstants.JCD_23031,
                    CommonConstants.JCD_63031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_21030,
                    CommonConstants.JCD_41030, CommonConstants.JCD_21070, CommonConstants.JCD_41070,
                    CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_23040, CommonConstants.JCD_43040, CommonConstants.JCD_23050,
                    CommonConstants.JCD_43050, CommonConstants.JCD_23062, CommonConstants.JCD_61090,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112,
                    CommonConstants.JCD_A1010, CommonConstants.JCD_A5013, CommonConstants.JCD_A9010,
                    CommonConstants.JCD_61113, CommonConstants.JCD_63084, CommonConstants.JCD_73084,
                    CommonConstants.JCD_63104, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
            // 横だし事業所のjcdを取得する（全部付加される）
            if (CommonConstants.SYSTEM_CODE_71101.equals(gsyscd)) {
                getYokodasiJcds(jcds);
            }
        } else if (ulistRetPtn == 65) {
            // 41のnyuusyoreg版
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_22022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42012, CommonConstants.JCD_42022, CommonConstants.JCD_42032,
                    CommonConstants.JCD_63061, CommonConstants.JCD_10041, CommonConstants.JCD_22042,
                    CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 66 || ulistRetPtn == 67) {
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_21070,
                    CommonConstants.JCD_41070, CommonConstants.JCD_23062,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112, CommonConstants.JCD_63084,
                    CommonConstants.JCD_73084, CommonConstants.JCD_63104, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 68) {
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053,
                    CommonConstants.JCD_71053, CommonConstants.JCD_21010, CommonConstants.JCD_41010,
                    CommonConstants.JCD_61010, CommonConstants.JCD_21020, CommonConstants.JCD_41020,
                    CommonConstants.JCD_21030, CommonConstants.JCD_41030,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_23062,
                    CommonConstants.JCD_61090, CommonConstants.JCD_61104, CommonConstants.JCD_63112,
                    CommonConstants.JCD_A1010, CommonConstants.JCD_A5013,
                    CommonConstants.JCD_61113, CommonConstants.JCD_63084, CommonConstants.JCD_73084,
                    CommonConstants.JCD_63104, CommonConstants.JCD_10041, CommonConstants.JCD_22042,
                    CommonConstants.JCD_42042, CommonConstants.JCD_21040));
        } else if (ulistRetPtn == 69) {
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053,
                    CommonConstants.JCD_71053, CommonConstants.JCD_21010, CommonConstants.JCD_41010,
                    CommonConstants.JCD_61010, CommonConstants.JCD_21020, CommonConstants.JCD_41020,
                    CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_21030, CommonConstants.JCD_41030, CommonConstants.JCD_61084,
                    CommonConstants.JCD_71084, CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_23062, CommonConstants.JCD_61090,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112, CommonConstants.JCD_A1010,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_61113, CommonConstants.JCD_63084,
                    CommonConstants.JCD_73084, CommonConstants.JCD_63104,
                    CommonConstants.JCD_10041, CommonConstants.JCD_22042, CommonConstants.JCD_42042));
            // 横だし事業所のjcdを取得する（全部付加される）
            if (CommonConstants.SYSTEM_CODE_71101.equals(gsyscd)) {
                getYokodasiJcds(jcds);
            }
        } else if (ulistRetPtn == 70) {
            jcds.addAll(List.of(CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053, CommonConstants.JCD_71053,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_61113, CommonConstants.JCD_61084,
                    CommonConstants.JCD_71084, CommonConstants.JCD_61104, CommonConstants.JCD_63084,
                    CommonConstants.JCD_73084, CommonConstants.JCD_63104));
        } else if (ulistRetPtn == 71) {
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_22012, CommonConstants.JCD_23031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_42012, CommonConstants.JCD_22022, CommonConstants.JCD_42022,
                    CommonConstants.JCD_22032, CommonConstants.JCD_42032,
                    CommonConstants.JCD_63022, CommonConstants.JCD_73022, CommonConstants.JCD_63031,
                    CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053,
                    CommonConstants.JCD_71053, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_23010,
                    CommonConstants.JCD_43010, CommonConstants.JCD_23062,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112, CommonConstants.JCD_61113,
                    CommonConstants.JCD_63084, CommonConstants.JCD_73084, CommonConstants.JCD_63104,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 72) {
            // 未使用？
            jcds.addAll(List.of(
                    CommonConstants.JCD_10011, CommonConstants.JCD_63061, CommonConstants.JCD_22012,
                    CommonConstants.JCD_42012, CommonConstants.JCD_10021, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_10031, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_63022, CommonConstants.JCD_73022, CommonConstants.JCD_23031,
                    CommonConstants.JCD_63031, CommonConstants.JCD_43031, CommonConstants.JCD_21053,
                    CommonConstants.JCD_41053, CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_71053, CommonConstants.JCD_21040,
                    CommonConstants.JCD_41040, CommonConstants.JCD_61084, CommonConstants.JCD_71084,
                    CommonConstants.JCD_23010, CommonConstants.JCD_43010, CommonConstants.JCD_23062,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112, CommonConstants.JCD_A5013,
                    CommonConstants.JCD_10041, CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 73) {
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053,
                    CommonConstants.JCD_71053, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_21030, CommonConstants.JCD_41030, CommonConstants.JCD_61084,
                    CommonConstants.JCD_71084, CommonConstants.JCD_23010,
                    CommonConstants.JCD_43010, CommonConstants.JCD_23062, CommonConstants.JCD_61104,
                    CommonConstants.JCD_63112, CommonConstants.JCD_A5013, CommonConstants.JCD_61113,
                    CommonConstants.JCD_63084, CommonConstants.JCD_73084,
                    CommonConstants.JCD_63104, CommonConstants.JCD_10041, CommonConstants.JCD_22042,
                    CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 74) {
            // 未使用？
            jcds.addAll(List.of(
                    CommonConstants.JCD_10011, CommonConstants.JCD_63061, CommonConstants.JCD_22012,
                    CommonConstants.JCD_42012, CommonConstants.JCD_10021, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022,
                    CommonConstants.JCD_10031, CommonConstants.JCD_22032, CommonConstants.JCD_42032,
                    CommonConstants.JCD_23021, CommonConstants.JCD_43021, CommonConstants.JCD_63022,
                    CommonConstants.JCD_73022,
                    CommonConstants.JCD_23031, CommonConstants.JCD_63031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_71053, CommonConstants.JCD_21040,
                    CommonConstants.JCD_41040, CommonConstants.JCD_61084, CommonConstants.JCD_71084,
                    CommonConstants.JCD_23010,
                    CommonConstants.JCD_43010, CommonConstants.JCD_23062, CommonConstants.JCD_61104,
                    CommonConstants.JCD_63112, CommonConstants.JCD_A5013, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042,
                    CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 75) {
            // 11と同じで、「予定者」フィルタを出す
            jcds.addAll(List.of(CommonConstants.JCD_21070, CommonConstants.JCD_41070));
        } else if (ulistRetPtn == 76) {
            // 34と同じで、「予定者」フィルタを出す
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053,
                    CommonConstants.JCD_71053, CommonConstants.JCD_21010, CommonConstants.JCD_41010,
                    CommonConstants.JCD_61010, CommonConstants.JCD_21020, CommonConstants.JCD_41020,
                    CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_21030, CommonConstants.JCD_41030, CommonConstants.JCD_61084,
                    CommonConstants.JCD_71084, CommonConstants.JCD_21070, CommonConstants.JCD_41070,
                    CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_23062, CommonConstants.JCD_61090, CommonConstants.JCD_61104,
                    CommonConstants.JCD_63112, CommonConstants.JCD_A1010, CommonConstants.JCD_A5013,
                    CommonConstants.JCD_61113, CommonConstants.JCD_63084,
                    CommonConstants.JCD_73084, CommonConstants.JCD_63104, CommonConstants.JCD_10041,
                    CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 77) {
            // 38と同じで、「予定者」フィルタを出す
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053,
                    CommonConstants.JCD_71053, CommonConstants.JCD_61084, CommonConstants.JCD_71084,
                    CommonConstants.JCD_23062, CommonConstants.JCD_61104, CommonConstants.JCD_63112,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_61113,
                    CommonConstants.JCD_63084, CommonConstants.JCD_73084, CommonConstants.JCD_63104,
                    CommonConstants.JCD_10041, CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 78) {
            // 48と同じで、「予定者」フィルタを出す
            jcds.add(CommonConstants.JCD_50010);
        } else if (ulistRetPtn == 79) {
            // 空床確保状況（短期入所生活のみ使用可）
            jcds.add(CommonConstants.JCD_22012);
        } else if (ulistRetPtn == 80) {
            // 訪問看護のみ
            jcds.add(CommonConstants.JCD_21030);
        } else if (ulistRetPtn == 81) {
            // 定期巡回・随時対応型訪問介護看護、複合型
            jcds.addAll(List.of(CommonConstants.JCD_61090, CommonConstants.JCD_61104));
        } else if (ulistRetPtn == 82) {
            // 今月有効な適用事業所の看護利用登録
            jcds.addAll(List.of(CommonConstants.JCD_21030, CommonConstants.JCD_41030));
        } else if (ulistRetPtn == 83) {
            // サポート薬局
            jcds.addAll(List.of(CommonConstants.JCD_23010, CommonConstants.JCD_43010));
        } else if (ulistRetPtn == 84) {
            // 国保請求用新設
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_23062, CommonConstants.JCD_63112, CommonConstants.JCD_21053,
                    CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053, CommonConstants.JCD_71053,
                    CommonConstants.JCD_21010, CommonConstants.JCD_41010, CommonConstants.JCD_61010,
                    CommonConstants.JCD_21020, CommonConstants.JCD_41020,
                    CommonConstants.JCD_21040, CommonConstants.JCD_41040, CommonConstants.JCD_21030,
                    CommonConstants.JCD_41030, CommonConstants.JCD_61090, CommonConstants.JCD_61084,
                    CommonConstants.JCD_71084, CommonConstants.JCD_61104,
                    CommonConstants.JCD_21070, CommonConstants.JCD_41070, CommonConstants.JCD_23010,
                    CommonConstants.JCD_43010, CommonConstants.JCD_23040, CommonConstants.JCD_43040,
                    CommonConstants.JCD_23050, CommonConstants.JCD_43050,
                    CommonConstants.JCD_30010, CommonConstants.JCD_50010, CommonConstants.JCD_A1010,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_A9010, CommonConstants.JCD_61113,
                    CommonConstants.JCD_63084, CommonConstants.JCD_73084,
                    CommonConstants.JCD_63104, CommonConstants.JCD_10041, CommonConstants.JCD_22042,
                    CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 85) {
            if (jigyoOpenFlg) {
                jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                        CommonConstants.JCD_10041, CommonConstants.JCD_63061, CommonConstants.JCD_23021,
                        CommonConstants.JCD_43021, CommonConstants.JCD_23031, CommonConstants.JCD_43031,
                        CommonConstants.JCD_63031, CommonConstants.JCD_22012, CommonConstants.JCD_42012,
                        CommonConstants.JCD_22022, CommonConstants.JCD_42022,
                        CommonConstants.JCD_22032, CommonConstants.JCD_42032, CommonConstants.JCD_22042,
                        CommonConstants.JCD_42042, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                        CommonConstants.JCD_23062, CommonConstants.JCD_63112,
                        CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_A5013,
                        CommonConstants.JCD_61113, CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                        CommonConstants.JCD_61053, CommonConstants.JCD_71053));
            } else {
                jcds.addAll(List.of(CommonConstants.JCD_10021, CommonConstants.JCD_22022, CommonConstants.JCD_42022,
                        CommonConstants.JCD_21063, CommonConstants.JCD_41063));
            }
        } else if (ulistRetPtn == 86) {
            // 請求共通
            jcds.addAll(List.of(CommonConstants.JCD_10021, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_22022, CommonConstants.JCD_42022, CommonConstants.JCD_63022,
                    CommonConstants.JCD_73022, CommonConstants.JCD_21053, CommonConstants.JCD_41053,
                    CommonConstants.JCD_21063, CommonConstants.JCD_41063, CommonConstants.JCD_61053,
                    CommonConstants.JCD_71053, CommonConstants.JCD_21010,
                    CommonConstants.JCD_41010, CommonConstants.JCD_21020, CommonConstants.JCD_41020,
                    CommonConstants.JCD_21070, CommonConstants.JCD_41070, CommonConstants.JCD_30010,
                    CommonConstants.JCD_10011, CommonConstants.JCD_63061,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_23031,
                    CommonConstants.JCD_43031, CommonConstants.JCD_63031, CommonConstants.JCD_23062,
                    CommonConstants.JCD_63112, CommonConstants.JCD_A1010,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_21030, CommonConstants.JCD_41030,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_61104,
                    CommonConstants.JCD_61113, CommonConstants.JCD_63084,
                    CommonConstants.JCD_73084, CommonConstants.JCD_63104));
        } else if (ulistRetPtn == 87) {
            // ﾘﾊﾋﾞﾘ一覧記録(その他)用
            jcds.addAll(List.of(CommonConstants.JCD_21040, CommonConstants.JCD_41040, CommonConstants.JCD_61084,
                    CommonConstants.JCD_71084, CommonConstants.JCD_61104, CommonConstants.JCD_23010,
                    CommonConstants.JCD_43010, CommonConstants.JCD_63084, CommonConstants.JCD_73084,
                    CommonConstants.JCD_63104));
        } else if (ulistRetPtn == 88) {
            // 口腔ｹｱ一覧記録(その他)用
            jcds.addAll(List.of(CommonConstants.JCD_21040, CommonConstants.JCD_41040, CommonConstants.JCD_21030,
                    CommonConstants.JCD_41030, CommonConstants.JCD_61084, CommonConstants.JCD_71084,
                    CommonConstants.JCD_61104, CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_63084, CommonConstants.JCD_73084, CommonConstants.JCD_63104));
        } else if (ulistRetPtn == 89) {
            // 褥瘡ｹｱ一覧記録(その他)用
            jcds.addAll(List.of(CommonConstants.JCD_21030, CommonConstants.JCD_41030, CommonConstants.JCD_61084,
                    CommonConstants.JCD_71084, CommonConstants.JCD_61104, CommonConstants.JCD_63084,
                    CommonConstants.JCD_73084, CommonConstants.JCD_63104));
        } else if (ulistRetPtn == 90) {
            jcds.addAll(List.of(CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_30010,
                    CommonConstants.JCD_50010));
        } else if (ulistRetPtn == 91) {
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_23062, CommonConstants.JCD_63112, CommonConstants.JCD_21053,
                    CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053, CommonConstants.JCD_71053,
                    CommonConstants.JCD_21010, CommonConstants.JCD_41010, CommonConstants.JCD_61010,
                    CommonConstants.JCD_61090, CommonConstants.JCD_21020,
                    CommonConstants.JCD_41020, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_21030, CommonConstants.JCD_41030, CommonConstants.JCD_61084,
                    CommonConstants.JCD_71084, CommonConstants.JCD_61104,
                    CommonConstants.JCD_21070, CommonConstants.JCD_41070, CommonConstants.JCD_23010,
                    CommonConstants.JCD_43010, CommonConstants.JCD_23040, CommonConstants.JCD_43040,
                    CommonConstants.JCD_23050, CommonConstants.JCD_43050,
                    CommonConstants.JCD_30010, CommonConstants.JCD_50010, CommonConstants.JCD_A1010,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_A9010, CommonConstants.JCD_63084,
                    CommonConstants.JCD_73084, CommonConstants.JCD_63104,
                    CommonConstants.JCD_61113, CommonConstants.JCD_10041, CommonConstants.JCD_22042,
                    CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 92) {
            if (kinouId == 45028001) {
                // 利用者管理＞健康管理＞検査
                jcds.add(CommonConstants.JCD_ALL);
            } else {
                jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                        CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                        CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                        CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                        CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                        CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                        CommonConstants.JCD_23062, CommonConstants.JCD_63112, CommonConstants.JCD_21053,
                        CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                        CommonConstants.JCD_41063, CommonConstants.JCD_61053, CommonConstants.JCD_71053,
                        CommonConstants.JCD_21010, CommonConstants.JCD_41010, CommonConstants.JCD_61010,
                        CommonConstants.JCD_61090, CommonConstants.JCD_21020,
                        CommonConstants.JCD_41020, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                        CommonConstants.JCD_21030, CommonConstants.JCD_41030, CommonConstants.JCD_61084,
                        CommonConstants.JCD_71084, CommonConstants.JCD_61104,
                        CommonConstants.JCD_21070, CommonConstants.JCD_41070, CommonConstants.JCD_23010,
                        CommonConstants.JCD_43010, CommonConstants.JCD_23040, CommonConstants.JCD_43040,
                        CommonConstants.JCD_23050, CommonConstants.JCD_43050,
                        CommonConstants.JCD_30010, CommonConstants.JCD_50010, CommonConstants.JCD_A1010,
                        CommonConstants.JCD_A5013, CommonConstants.JCD_A9010, CommonConstants.JCD_61113,
                        CommonConstants.JCD_63084, CommonConstants.JCD_73084,
                        CommonConstants.JCD_63104, CommonConstants.JCD_10041, CommonConstants.JCD_22042,
                        CommonConstants.JCD_42042));

                if (CommonConstants.SYSTEM_CODE_71101.equals(gsyscd)) {
                    // 請求→介護請求→介護請求以外は横出し可
                    getYokodasiJcds(jcds);
                }
            }
        } else if (ulistRetPtn == 93) {
            jcds.addAll(List.of(CommonConstants.JCD_21020, CommonConstants.JCD_41020));
        } else if (ulistRetPtn == 94) {
            // 外部連携＞ｼﾆｱﾒｲﾄ連携専用
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_63061, CommonConstants.JCD_10021,
                    CommonConstants.JCD_10031, CommonConstants.JCD_22012, CommonConstants.JCD_42012,
                    CommonConstants.JCD_22022, CommonConstants.JCD_22032, CommonConstants.JCD_42022,
                    CommonConstants.JCD_42032, CommonConstants.JCD_21053, CommonConstants.JCD_41053,
                    CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_71053, CommonConstants.JCD_21010,
                    CommonConstants.JCD_21020, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_61010, CommonConstants.JCD_41010,
                    CommonConstants.JCD_41020, CommonConstants.JCD_23021, CommonConstants.JCD_63022,
                    CommonConstants.JCD_43021, CommonConstants.JCD_73022, CommonConstants.JCD_23031,
                    CommonConstants.JCD_63031, CommonConstants.JCD_43031,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_21030,
                    CommonConstants.JCD_41030, CommonConstants.JCD_21070, CommonConstants.JCD_41070,
                    CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_23040, CommonConstants.JCD_43040, CommonConstants.JCD_23050,
                    CommonConstants.JCD_43050, CommonConstants.JCD_23062, CommonConstants.JCD_61090,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112,
                    CommonConstants.JCD_10041, CommonConstants.JCD_22042, CommonConstants.JCD_42042));

            // 横だし事業所のjcdを取得する（全部付加される）
            if (CommonConstants.SYSTEM_CODE_71101.equals(gsyscd) && kinouId != 47016003) {
                getYokodasiJcds(jcds);
            }
        } else if (ulistRetPtn == 95) {
            // 栄養ケア＞栄養個人設定
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053,
                    CommonConstants.JCD_71053, CommonConstants.JCD_61084, CommonConstants.JCD_71084,
                    CommonConstants.JCD_23062, CommonConstants.JCD_61104, CommonConstants.JCD_63112,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_61113,
                    CommonConstants.JCD_63084, CommonConstants.JCD_73084, CommonConstants.JCD_63104,
                    CommonConstants.JCD_10041, CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 96) {
            // ○○○＞情報提供専用
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_23062, CommonConstants.JCD_63112, CommonConstants.JCD_21053,
                    CommonConstants.JCD_41053, CommonConstants.JCD_21063,
                    CommonConstants.JCD_41063, CommonConstants.JCD_61053, CommonConstants.JCD_71053,
                    CommonConstants.JCD_21010, CommonConstants.JCD_41010, CommonConstants.JCD_61010,
                    CommonConstants.JCD_21020, CommonConstants.JCD_41020,
                    CommonConstants.JCD_21040, CommonConstants.JCD_41040, CommonConstants.JCD_21030,
                    CommonConstants.JCD_41030, CommonConstants.JCD_61090, CommonConstants.JCD_61084,
                    CommonConstants.JCD_71084, CommonConstants.JCD_61104,
                    CommonConstants.JCD_21070, CommonConstants.JCD_41070, CommonConstants.JCD_23010,
                    CommonConstants.JCD_43010, CommonConstants.JCD_23040, CommonConstants.JCD_43040,
                    CommonConstants.JCD_23050, CommonConstants.JCD_43050,
                    CommonConstants.JCD_30010, CommonConstants.JCD_50010, CommonConstants.JCD_A1010,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_A9010, CommonConstants.JCD_61113,
                    CommonConstants.JCD_63084, CommonConstants.JCD_73084,
                    CommonConstants.JCD_63104, CommonConstants.JCD_10041, CommonConstants.JCD_22042,
                    CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 97) {
            // 請求＞事業所加減算＞算定日管理
            jcds.addAll(List.of(CommonConstants.JCD_21053, CommonConstants.JCD_21063, CommonConstants.JCD_61113,
                    CommonConstants.JCD_23031, CommonConstants.JCD_63031));
        } else if (ulistRetPtn == 98) {
            // R4＞ﾘﾊﾋﾞﾘ計画書
            if (jigyoOpenFlg) {
                jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                        CommonConstants.JCD_10041, CommonConstants.JCD_63061, CommonConstants.JCD_23021,
                        CommonConstants.JCD_43021, CommonConstants.JCD_23031, CommonConstants.JCD_43031,
                        CommonConstants.JCD_63031, CommonConstants.JCD_22012, CommonConstants.JCD_42012,
                        CommonConstants.JCD_22022, CommonConstants.JCD_42022,
                        CommonConstants.JCD_22032, CommonConstants.JCD_42032, CommonConstants.JCD_22042,
                        CommonConstants.JCD_42042, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                        CommonConstants.JCD_23062, CommonConstants.JCD_63112,
                        CommonConstants.JCD_21053, CommonConstants.JCD_41053, CommonConstants.JCD_A5013,
                        CommonConstants.JCD_61113, CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                        CommonConstants.JCD_61053, CommonConstants.JCD_71053,
                        CommonConstants.JCD_21040, CommonConstants.JCD_41040));
            } else {
                jcds.addAll(List.of(CommonConstants.JCD_10021, CommonConstants.JCD_22022, CommonConstants.JCD_42022,
                        CommonConstants.JCD_21063, CommonConstants.JCD_41063, CommonConstants.JCD_21040,
                        CommonConstants.JCD_41040));
            }
        } else if (ulistRetPtn == 99) {
            // 請求＞敷金管理
            jcds.addAll(List.of(CommonConstants.JCD_23031, CommonConstants.JCD_63031, CommonConstants.JCD_43031));
        } else if (ulistRetPtn == 100) {
            // 請求＞算定＞特別診療費
            jcds.addAll(List.of(CommonConstants.JCD_10041, CommonConstants.JCD_22042, CommonConstants.JCD_42042));
        } else if (ulistRetPtn == 101) {
            // LIFE＞自立支援促進加算
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10041,
                    CommonConstants.JCD_63061));
        } else if (ulistRetPtn == 102) {
            // LIFE＞薬剤管理調整加算
            jcds.addAll(List.of(CommonConstants.JCD_10021, CommonConstants.JCD_10041));
        } else if (ulistRetPtn == 103) {
            // LIFE＞ADL維持等加算
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_63061, CommonConstants.JCD_23031,
                    CommonConstants.JCD_63031, CommonConstants.JCD_21053, CommonConstants.JCD_61113,
                    CommonConstants.JCD_61053));
        } else if (ulistRetPtn == 104) {
            // LIFE＞科学的介護加算
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10041,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_21053, CommonConstants.JCD_A5013, CommonConstants.JCD_61113,
                    CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_71053, CommonConstants.JCD_61084,
                    CommonConstants.JCD_71084, CommonConstants.JCD_61104));
        } else if (ulistRetPtn == 105) {
            // LIFE＞薬剤管理調整加算から表示するダイアログ用
            jcds.add(CommonConstants.JCD_10021);
        } else if (ulistRetPtn == 106) {
            // LIFE＞LIFE進捗管理＞栄養
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10041,
                    CommonConstants.JCD_21053, CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_61104, CommonConstants.JCD_61113,
                    CommonConstants.JCD_63061, CommonConstants.JCD_71053, CommonConstants.JCD_A5013));
        } else if (ulistRetPtn == 107) {
            // LIFE＞LIFE進捗管理＞口腔機能向上
            jcds.addAll(List.of(CommonConstants.JCD_21053, CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_61104, CommonConstants.JCD_61113,
                    CommonConstants.JCD_71053, CommonConstants.JCD_A5013));
        } else if (ulistRetPtn == 108) {
            // LIFE＞LIFE進捗管理＞個別機能訓練
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_21053, CommonConstants.JCD_23031,
                    CommonConstants.JCD_43031, CommonConstants.JCD_61053, CommonConstants.JCD_61113,
                    CommonConstants.JCD_63031, CommonConstants.JCD_63061, CommonConstants.JCD_71053));
        } else if (ulistRetPtn == 109) {
            // LIFE＞LIFE進捗管理＞ﾘﾊﾋﾞﾘﾏﾈｼﾞﾒﾝﾄ
            jcds.addAll(List.of(CommonConstants.JCD_10021, CommonConstants.JCD_10041, CommonConstants.JCD_21040,
                    CommonConstants.JCD_21063, CommonConstants.JCD_41040, CommonConstants.JCD_41063));
        } else if (ulistRetPtn == 110) {
            // LIFE＞LIFE進捗管理＞排せつ支援
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10041,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63061));
        } else if (ulistRetPtn == 111) {
            // LIFE＞LIFE進捗管理＞利用者情報 等
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10031,
                    CommonConstants.JCD_63061, CommonConstants.JCD_23021, CommonConstants.JCD_43021,
                    CommonConstants.JCD_23031, CommonConstants.JCD_43031, CommonConstants.JCD_63031,
                    CommonConstants.JCD_22012, CommonConstants.JCD_42012, CommonConstants.JCD_22022,
                    CommonConstants.JCD_42022, CommonConstants.JCD_22032,
                    CommonConstants.JCD_42032, CommonConstants.JCD_63022, CommonConstants.JCD_73022,
                    CommonConstants.JCD_21053, CommonConstants.JCD_21063, CommonConstants.JCD_41063,
                    CommonConstants.JCD_61053, CommonConstants.JCD_71053,
                    CommonConstants.JCD_21010, CommonConstants.JCD_61010, CommonConstants.JCD_21020,
                    CommonConstants.JCD_41020, CommonConstants.JCD_21040, CommonConstants.JCD_41040,
                    CommonConstants.JCD_21030, CommonConstants.JCD_41030,
                    CommonConstants.JCD_61084, CommonConstants.JCD_71084, CommonConstants.JCD_21070,
                    CommonConstants.JCD_41070, CommonConstants.JCD_23010, CommonConstants.JCD_43010,
                    CommonConstants.JCD_23062, CommonConstants.JCD_61090,
                    CommonConstants.JCD_61104, CommonConstants.JCD_63112, CommonConstants.JCD_A1010,
                    CommonConstants.JCD_A5013, CommonConstants.JCD_61113, CommonConstants.JCD_63084,
                    CommonConstants.JCD_73084, CommonConstants.JCD_63104,
                    CommonConstants.JCD_10041, CommonConstants.JCD_22042, CommonConstants.JCD_42042,
                    CommonConstants.JCD_30010));
        } else if (ulistRetPtn == 112) {
            // LIFE＞LIFE進捗管理＞褥瘡ﾏﾈｼﾞﾒﾝﾄ
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_61104,
                    CommonConstants.JCD_63061));
        } else if (ulistRetPtn == 113) {
            // LIFE＞LIFE進捗管理＞褥瘡対策指導
            jcds.add(CommonConstants.JCD_10041);
        } else if (ulistRetPtn == 114) {
            // ケアマネ＞情報連携＞退居・退所時情報提供書
            jcds.addAll(List.of(CommonConstants.JCD_10011, CommonConstants.JCD_10021, CommonConstants.JCD_10041,
                    CommonConstants.JCD_23021, CommonConstants.JCD_23031, CommonConstants.JCD_43021,
                    CommonConstants.JCD_43031, CommonConstants.JCD_63031, CommonConstants.JCD_63061));
        } else {
            jcds.add(CommonConstants.JCD_ALL);
        }

        return jcds;
    }

    /**
     * 横だし事業所のjcdを取得する（全部付加される）
     * 
     * @param jcds 事業所のjcd
     */
    private void getYokodasiJcds(List<String> jcds) {
        ComMscSvjigyoNameCriteria criteria = new ComMscSvjigyoNameCriteria();
        criteria.createCriteria().andSvKbnEqualTo(9);
        List<ComMscSvjigyoName> resultList = comMscSvjigyoNameMapper.selectByCriteria(criteria);
        if (CollectionUtils.isNotEmpty(resultList)) {
            for (ComMscSvjigyoName result : resultList) {
                jcds.add(result.getSvJigyoCd());
            }
        }
    }
}
