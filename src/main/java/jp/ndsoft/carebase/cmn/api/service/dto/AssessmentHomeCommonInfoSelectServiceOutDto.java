package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794CommonInformationGdlKadai;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794CommonInformationHistoryInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794CommonInformationPlanPeriodInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Seishin;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Teido;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Toukyuu;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Yokaigo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Zokugara;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.04.17
 * <AUTHOR>
 * @description GUI00794_［アセスメント］画面（居宅）（1）
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class AssessmentHomeCommonInfoSelectServiceOutDto extends IDtoImpl {

    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 計画期間情報 */
    private Gui00794CommonInformationPlanPeriodInfo planPeriodInfo;

    /** 種別ID */
    @NotEmpty
    private String syubetsuId;

    /** 期間管理フラグ */
    @NotEmpty
    private String kikanFlg;

    /** 履歴情報 */
    private Gui00794CommonInformationHistoryInfo historyInfo;

    /** 課題と目標情報リスト */
    private List<Gui00794CommonInformationGdlKadai> gdlKadaiList;

    /** 続柄マスタ情報リスト */
    private List<Gui00794Zokugara> zokugaraList;

    /** 要介護状態情報リスト */
    private List<Gui00794Yokaigo> yokaigoList;

    /** 身障手帳等級情報リスト */
    private List<Gui00794Toukyuu> toukyuuList;

    /** 療育手帳程度情報リスト */
    private List<Gui00794Teido> teidoList;

    /** 精神等級情報リスト */
    private List<Gui00794Seishin> seishinList;    

}
