package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.*;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00808OperationInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.SpecialNoteMatterSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.SpecialNoteMatterSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.*;
import jp.ndsoft.carebase.common.dao.mysql.mapper.*;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import io.micrometer.common.util.StringUtils;

/** 
 * @since 2025.03.06
 * <AUTHOR>
 * @implNote GUI00808_特記事項選択情報取込 初期情報取得
 */
@Service
public class SpecialNoteMatterSelectServiceImpl extends SelectServiceImpl<SpecialNoteMatterSelectServiceInDto, SpecialNoteMatterSelectServiceOutDto> {
   private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

   /** ＧＬ＿①基本動作関係情報取得DAO */
   @Autowired
   private CpnTucGdlKan11SelectMapper cpnTucGdlKan11SelectMapper;
   /** ＧＬ＿②入浴関係情報取得DAO */
   @Autowired
   private CpnTucGdlKan12SelectMapper cpnTucGdlKan12SelectMapper;
   /** ＧＬ＿③排泄関係情報取得DAO */
   @Autowired
   private CpnTucGdlKan13SelectMapper cpnTucGdlKan13SelectMapper;
   /** ＧＬ＿④食事関係情報取得DAO */
   @Autowired
   private CpnTucGdlKan14SelectMapper cpnTucGdlKan14SelectMapper;
   /** ＧＬ＿⑤生活管理関係情報取得DAO */
   @Autowired
   private CpnTucGdlKan15SelectMapper cpnTucGdlKan15SelectMapper;
   /** ＧＬ＿⑥コミュニケーション・連携・社会活動関係情報取得DAO */
   @Autowired
   private CpnTucGdlKan16SelectMapper cpnTucGdlKan16SelectMapper;
   /** ＧＬ＿⑦問題行動関係情報取得DAO */
   @Autowired
   private CpnTucGdlKan17SelectMapper cpnTucGdlKan17SelectMapper;
   /** ＧＬ＿⑧医療・健康関係情報取得DAO */
   @Autowired
   private CpnTucGdlKan18SelectMapper cpnTucGdlKan18SelectMapper;
   /** ＧＬ＿①基本動作関係（改訂）情報取得DAO */
   @Autowired
   private CpnTucGdl2Kan11SelectMapper cpnTucGdl2Kan11SelectMapper;
   /** ＧＬ＿②入浴関係（改訂）情報取得DAO */
   @Autowired
   private CpnTucGdl2Kan12SelectMapper cpnTucGdl2Kan12SelectMapper;
   /** ＧＬ＿④食事関係（改訂）情報取得DAO */
   @Autowired
   private CpnTucGdl2Kan14SelectMapper cpnTucGdl2Kan14SelectMapper;
   /** ＧＬ＿③排泄関係（改訂）情報取得DAO */
   @Autowired
   private CpnTucGdl2Kan13SelectMapper cpnTucGdl2Kan13SelectMapper;
   /** ＧＬ＿⑤生活管理関係（改訂）情報取得DAO */
   @Autowired
   private CpnTucGdl2Kan15SelectMapper cpnTucGdl2Kan15SelectMapper;
   /** ＧＬ＿⑥コミュニケーション・連携・社会活動関係（改訂）情報取得DAO */
   @Autowired
   private CpnTucGdl2Kan16SelectMapper cpnTucGdl2Kan16SelectMapper;
   /** ＧＬ＿⑦問題行動関係（改訂）情報取得DAO */
   @Autowired
   private CpnTucGdl2Kan17SelectMapper cpnTucGdl2Kan17SelectMapper;
   /** ＧＬ＿⑧医療・健康関係（改訂）情報取得DAO */
   @Autowired
   private CpnTucGdl2Kan18SelectMapper cpnTucGdl2Kan18SelectMapper;
   /** ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）情報取得DAO */
   @Autowired
   private CpnTucGdl4Kan11H21SelectMapper cpnTucGdl4Kan11H21SelectMapper;
   /** ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）情報取得DAO */
   @Autowired
   private CpnTucGdl4Kan12H21SelectMapper cpnTucGdl4Kan12H21SelectMapper;
   /** ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）情報取得DAO */
   @Autowired
   private CpnTucGdl4Kan13H21SelectMapper cpnTucGdl4Kan13H21SelectMapper;
   /** ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）情報取得DAO */
   @Autowired
   private CpnTucGdl4Kan15H21SelectMapper cpnTucGdl4Kan15H21SelectMapper;
   /** ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）情報取得DAO */
   @Autowired
   private CpnTucGdl4Kan16H21SelectMapper cpnTucGdl4Kan16H21SelectMapper;

   /**
    * 「特記事項選択情報取込」画面の初期情報を取得する
    * 
    * @param inDto 特記事項選択情報取込入力DTO.
    * @return 特記事項選択情報取込初期情報出力DTO
    */
   @Override
   protected SpecialNoteMatterSelectServiceOutDto mainProcess(SpecialNoteMatterSelectServiceInDto inDto) throws Exception {
      LOG.info("START");

      // オペレーション情報リスト
      List<Gui00808OperationInfo> operationInfoList = new ArrayList<Gui00808OperationInfo>();

      /*
       * ===============1.単項目チェック以外の入力チェック===============
       * 
       */
      // 特になし

      /*
       * ===============2. 特記事項選択の取得===============
       * 
       */
      // 2.1. リクエストパラメータ.改訂フラグ＝1(改訂前) の場合、特記事項情報を取得する。
      if(CommonConstants.REVISION_FLAG_1.equals(inDto.getRevisionFlag())) {
         // 2.1.1. ＧＬ＿①基本動作関係情報を取得する。------START`
         // 2.1.1.1. 下記のＧＬ＿①基本動作関係情報取得DAOを利用し、ＧＬ＿①基本動作関係の項目内容を取得する。
         // DAOパラメータを作成
         GdlKan11ByCriteriaInEntity gdlKan11ByCriteriaInEntity = new GdlKan11ByCriteriaInEntity();
         gdlKan11ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdlKan11ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<GdlKan11OutEntity> gdlKan11OutEntityList = cpnTucGdlKan11SelectMapper.findGdlKan11ByCriteria(gdlKan11ByCriteriaInEntity);

         // 2.1.1.2. 上記取得したOUTPUT情報.特記がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdlKan11OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemo3Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-①基本動作関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_BASIC_MOTION_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo3Knj());
                  // オペレーション情報リストの項目IDを固定値「1」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_1);
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.1.1. ＧＬ＿①基本動作関係情報を取得する。------END

         // 2.1.2. ＧＬ＿②入浴関係情報を取得する。------START
         // 2.1.2.1. 下記のＧＬ＿②入浴関係情報取得DAOを利用し、ＧＬ＿②入浴関係の項目内容を取得する。
         // DAOパラメータを作成
         GdlKan12ByCriteriaInEntity gdlKan12ByCriteriaInEntity = new GdlKan12ByCriteriaInEntity();
         gdlKan12ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdlKan12ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<GdlKan12OutEntity> gdlKan12OutEntityList = cpnTucGdlKan12SelectMapper.findGdlKan12ByCriteria(gdlKan12ByCriteriaInEntity);

         gdlKan12OutEntityList.forEach(item -> {
            if(null != item) {
               // 2.1.2.2. 上記取得したOUTPUT情報.特記事項3がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
               if(StringUtils.isNotBlank(item.getMemo3Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-②入浴関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_BATHING_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項3に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo3Knj());
                  // オペレーション情報リストの項目IDを固定値「2」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_2);
                  
   
                  operationInfoList.add(gui00808OperationInfo);
               }
               // 2.1.2.3. 上記取得したOUTPUT情報.特記事項4がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
               if(StringUtils.isNotBlank(item.getMemo4Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-②入浴関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_BATHING_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項4に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo4Knj());
                  // オペレーション情報リストの項目IDを固定値「3」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_3);
                  
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.1.2. ＧＬ＿②入浴関係情報を取得する。------END

         // 2.1.3. ＧＬ＿③排泄関係情報を取得する。------START
         // 2.1.3.1. 下記のＧＬ＿③排泄関係情報取得DAOを利用し、ＧＬ＿③排泄関係の項目内容を取得する。
         // DAOパラメータを作成
         GdlKan13ByCriteriaInEntity gdlKan13ByCriteriaInEntity = new GdlKan13ByCriteriaInEntity();
         gdlKan13ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdlKan13ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<GdlKan13OutEntity> gdlKan13OutEntityList = cpnTucGdlKan13SelectMapper.findGdlKan13ByCriteria(gdlKan13ByCriteriaInEntity);

         //2.1.3.2. 上記取得したOUTPUT情報.特記事項3がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdlKan13OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemo3Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-③排泄関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_EXCRETION_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項3に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo3Knj());
                  // オペレーション情報リストの項目IDを固定値「4」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_4);
                  
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.1.3. ＧＬ＿③排泄関係情報を取得する。------END

         // 2.1.4. ＧＬ＿④食事関係情報を取得する。------START
         // 2.1.4.1. 下記のＧＬ＿④食事関係情報取得DAOを利用し、ＧＬ＿④食事関係の項目内容を取得する。
         // DAOパラメータを作成
         GdlKan14ByCriteriaInEntity gdlKan14ByCriteriaInEntity = new GdlKan14ByCriteriaInEntity();
         gdlKan14ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdlKan14ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<GdlKan14OutEntity>  gdlKan14OutEntityList = cpnTucGdlKan14SelectMapper.findGdlKan14ByCriteria(gdlKan14ByCriteriaInEntity);

         // 2.1.4.2. 上記取得したOUTPUT情報.特記事項がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdlKan14OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemoKnj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-④食事関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_MEAL_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項に設定する
                  gui00808OperationInfo.setItemContents(item.getMemoKnj());
                  // オペレーション情報リストの項目IDを固定値「5」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_5);
      
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.1.4. ＧＬ＿④食事関係情報を取得する。------END

         // 2.1.5. ＧＬ＿⑤生活管理関係情報を取得する。------START
         // 2.1.5.1. 下記のＧＬ＿⑤生活管理関係情報取得DAOを利用し、ＧＬ＿⑤生活管理関係の項目内容を取得する。
         // DAOパラメータを作成
         GdlKan15ByCriteriaInEntity gdlKan15ByCriteriaInEntity = new GdlKan15ByCriteriaInEntity();
         gdlKan15ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdlKan15ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<GdlKan15OutEntity> gdlKan15OutEntityList = cpnTucGdlKan15SelectMapper.findGdlKan15ByCriteria(gdlKan15ByCriteriaInEntity);

         // 2.1.5.2. 上記取得したOUTPUT情報.特記事項がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdlKan15OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemo3Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-⑤生活管理関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_LIEFT_MANAGEMENT_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo3Knj());
                  // オペレーション情報リストの項目IDを固定値「6」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_6);
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.1.5. ＧＬ＿⑤生活管理関係情報を取得する。------END

         // 2.1.6. ＧＬ＿⑥コミュニケーション・連携・社会活動関係情報を取得する。------START
         // 2.1.6.1. 下記のＧＬ＿⑥コミュニケーション・連携・社会活動関係情報取得DAOを利用し、ＧＬ＿⑥コミュニケーション・連携・社会活動関係の項目内容を取得する。
         // DAOパラメータを作成
         GdlKan16ByCriteriaInEntity gdlKan16ByCriteriaInEntity = new GdlKan16ByCriteriaInEntity();
         gdlKan16ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdlKan16ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<GdlKan16OutEntity> gdlKan16OutEntityList = cpnTucGdlKan16SelectMapper.findGdlKan16ByCriteria(gdlKan16ByCriteriaInEntity);

         // 2.1.6.2. 上記取得したOUTPUT情報.特記事項がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdlKan16OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemo1Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-⑥コミュニケーション・連携・社会活動関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_COMMUNICATION_COOPERATION_SOCIETY_ACTIVITYES_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo1Knj());
                  // オペレーション情報リストの項目IDを固定値「7」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_7);
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.1.6. ＧＬ＿⑥コミュニケーション・連携・社会活動関係情報を取得する。------END

         // 2.1.7. ＧＬ＿⑦問題行動関係情報を取得する。------START
         // 2.1.7.1. 下記のＧＬ＿⑦問題行動関係情報取得DAOを利用し、ＧＬ＿⑦問題行動関係の項目内容を取得する。
         // DAOパラメータを作成
         GdlKan17ByCriteriaInEntity gdlKan17ByCriteriaInEntity = new GdlKan17ByCriteriaInEntity();
         gdlKan17ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdlKan17ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<GdlKan17OutEntity> gdlKan17OutEntityList = cpnTucGdlKan17SelectMapper.findGdlKan17ByCriteria(gdlKan17ByCriteriaInEntity);

         // 2.1.7.2. 上記取得したOUTPUT情報.特記事項がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdlKan17OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemoKnj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-⑦問題行動関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_PROBLEM_ACTION_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項に設定する
                  gui00808OperationInfo.setItemContents(item.getMemoKnj());
                  // オペレーション情報リストの項目IDを固定値「8」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_8);
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.1.7. ＧＬ＿⑦問題行動関係情報を取得する。------END

         // 2.1.8. ＧＬ＿⑧医療・健康関係情報を取得する。------START
         // 2.1.8.1. 下記のＧＬ＿⑧医療・健康関係情報取得DAOを利用し、ＧＬ＿⑧医療・健康関係の項目内容を取得する。
         // DAOパラメータを作成
         GdlKan18ByCriteriaInEntity gdlKan18ByCriteriaInEntity = new GdlKan18ByCriteriaInEntity();
         gdlKan18ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdlKan18ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<GdlKan18OutEntity> gdlKan18OutEntityList = cpnTucGdlKan18SelectMapper.findGdlKan18ByCriteria(gdlKan18ByCriteriaInEntity);

         // 2.1.8.2. 上記取得したOUTPUT情報.特記事項がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdlKan18OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemo4Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-⑧医療・健康関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_MEDICAL_CARE_HEALTH_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo4Knj());
                  // オペレーション情報リストの項目IDを固定値「9」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_9);
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.1.8. ＧＬ＿⑧医療・健康関係情報を取得する。------END
       } 
       // 2.2. リクエストパラメータ.改訂フラグ＝2(H15/4改訂版),3(H18/4改訂版) の場合、特記事項情報を取得する。
       else if(CommonConstants.REVISION_FLAG_2.equals(inDto.getRevisionFlag()) || CommonConstants.REVISION_FLAG_3.equals(inDto.getRevisionFlag())) {
         // 2.2.1. ＧＬ＿①基本動作関係（改訂）情報を取得する。------START
         // 2.2.1.1. 下記のＧＬ＿①基本動作関係（改訂）情報取得DAOを利用し、ＧＬ＿①基本動作関係（改訂）の項目内容を取得する。
         // DAOパラメータを作成
         Gdl2Kan11ByCriteriaInEntity gdl2Kan11ByCriteriaInEntity = new Gdl2Kan11ByCriteriaInEntity();
         gdl2Kan11ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdl2Kan11ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<Gdl2Kan11OutEntity> gdl2Kan11OutEntityList = cpnTucGdl2Kan11SelectMapper.findGdl2Kan11ByCriteria(gdl2Kan11ByCriteriaInEntity);

         // 2.2.1.2. 上記取得したOUTPUT情報.特記がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdl2Kan11OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemo3Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-①基本動作関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_BASIC_MOTION_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo3Knj());
                  // オペレーション情報リストの項目IDを固定値「1」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_1);
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.2.1. ＧＬ＿①基本動作関係（改訂）情報を取得する。------END
         
         // 2.2.2. ＧＬ＿②入浴関係（改訂）情報を取得する。------START
         // 2.2.2.1. 下記のＧＬ＿②入浴関係（改訂）情報取得DAOを利用し、ＧＬ＿②入浴関係（改訂）の項目内容を取得する。
         // DAOパラメータを作成
         Gdl2Kan12ByCriteriaInEntity gdl2Kan12ByCriteriaInEntity = new Gdl2Kan12ByCriteriaInEntity();
         gdl2Kan12ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdl2Kan12ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<Gdl2Kan12OutEntity> gdl2Kan12OutEntityList = cpnTucGdl2Kan12SelectMapper.findGdl2Kan12ByCriteria(gdl2Kan12ByCriteriaInEntity);

         gdl2Kan12OutEntityList.forEach(item -> {
            if(null != item) {
               // 2.2.2.2. 上記取得したOUTPUT情報.特記事項3がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
               if(StringUtils.isNotBlank(item.getMemo3Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-②入浴関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_BATHING_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項3に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo3Knj());
                  // オペレーション情報リストの項目IDを固定値「2」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_2);
   
                  operationInfoList.add(gui00808OperationInfo);
               }

               // 2.2.2.3. 上記取得したOUTPUT情報.特記事項4がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
               if(StringUtils.isNotBlank(item.getMemo4Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-②入浴関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_BATHING_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項4に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo4Knj());
                  // オペレーション情報リストの項目IDを固定値「3」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_3);
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.2.2. ＧＬ＿②入浴関係（改訂）情報を取得する。------END
         
         // 2.2.3. ＧＬ＿④食事関係（改訂）情報を取得する。------START
         // 2.2.3.1. 下記のＧＬ＿④食事関係（改訂）情報取得DAOを利用し、ＧＬ＿④食事関係（改訂）の項目内容を取得する。
         // DAOパラメータを作成
         Gdl2Kan14ByCriteriaInEntity gdl2Kan14ByCriteriaInEntity = new Gdl2Kan14ByCriteriaInEntity();
         gdl2Kan14ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdl2Kan14ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<Gdl2Kan14OutEntity> gdl2Kan14OutEntityList = cpnTucGdl2Kan14SelectMapper.findGdl2Kan14ByCriteria(gdl2Kan14ByCriteriaInEntity);

         // 2.2.3.2. 上記取得したOUTPUT情報.特記事項がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdl2Kan14OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemoKnj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-③食事関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER__3_MEAL_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項に設定する
                  gui00808OperationInfo.setItemContents(item.getMemoKnj());
                  // オペレーション情報リストの項目IDを固定値「4」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_4);
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.2.3. ＧＬ＿④食事関係（改訂）情報を取得する。------END
         
         // 2.2.4. ＧＬ＿③排泄関係（改訂）情報を取得する。------START
         // 2.2.4.1. 下記のＧＬ＿③排泄関係（改訂）情報取得DAOを利用し、ＧＬ＿③排泄関係（改訂）の項目内容を取得する。
         // DAOパラメータを作成
         Gdl2Kan13ByCriteriaInEntity gdl2Kan13ByCriteriaInEntity = new Gdl2Kan13ByCriteriaInEntity();
         gdl2Kan13ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdl2Kan13ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<Gdl2Kan13OutEntity> gdl2Kan13OutEntityList = cpnTucGdl2Kan13SelectMapper.findGdl2Kan13ByCriteria(gdl2Kan13ByCriteriaInEntity);

         // 2.2.4.2. 上記取得したOUTPUT情報.特記事項がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdl2Kan13OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemo3Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-④排泄関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_4_EXCRETION_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo3Knj());
                  // オペレーション情報リストの項目IDを固定値「5」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_5);
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.2.4. ＧＬ＿③排泄関係（改訂）情報を取得する。------END
         
         // 2.2.5. ＧＬ＿⑤生活管理関係（改訂）情報を取得する。------START
         // 2.2.5.1. 下記のＧＬ＿⑤生活管理関係（改訂）情報取得DAOを利用し、ＧＬ＿⑤生活管理関係（改訂）の項目内容を取得する。
         // DAOパラメータを作成
         Gdl2Kan15ByCriteriaInEntity gdl2Kan15ByCriteriaInEntity = new Gdl2Kan15ByCriteriaInEntity();
         gdl2Kan15ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdl2Kan15ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<Gdl2Kan15OutEntity> gdl2Kan15OutEntityList = cpnTucGdl2Kan15SelectMapper.findGdl2Kan15ByCriteria(gdl2Kan15ByCriteriaInEntity);

         // 2.2.5.2. 上記取得したOUTPUT情報.特記事項がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdl2Kan15OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemo3Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-⑤生活管理関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_LIEFT_MANAGEMENT_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo3Knj());
                  // オペレーション情報リストの項目IDを固定値「6」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_6);
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.2.5. ＧＬ＿⑤生活管理関係（改訂）情報を取得する。------END
         
         // 2.2.6. ＧＬ＿⑥コミュニケーション・連携・社会活動関係（改訂）情報を取得する。------START
         // 2.2.6.1. 下記のＧＬ＿⑥コミュニケーション・連携・社会活動関係（改訂）情報取得DAOを利用し、ＧＬ＿⑥コミュニケーション・連携・社会活動関係（改訂）の項目内容を取得する。
         // DAOパラメータを作成
         Gdl2Kan16ByCriteriaInEntity gdl2Kan16ByCriteriaInEntity = new Gdl2Kan16ByCriteriaInEntity();
         gdl2Kan16ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdl2Kan16ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<Gdl2Kan16OutEntity> gdl2Kan16OutEntityList = cpnTucGdl2Kan16SelectMapper.findGdl2Kan16ByCriteria(gdl2Kan16ByCriteriaInEntity);

         // 2.2.6.2. 上記取得したOUTPUT情報.特記事項がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdl2Kan16OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemo1Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-⑥コミュニケーション・連携・社会活動関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_COMMUNICATION_COOPERATION_SOCIETY_ACTIVITYES_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo1Knj());
                  // オペレーション情報リストの項目IDを固定値「7」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_7);
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.2.6. ＧＬ＿⑥コミュニケーション・連携・社会活動関係（改訂）情報を取得する。------END
         
         // 2.2.7. ＧＬ＿⑦問題行動関係（改訂）情報を取得する。------START
         // 2.2.7.1. 下記のＧＬ＿⑦問題行動関係（改訂）情報取得DAOを利用し、ＧＬ＿⑦問題行動関係（改訂）の項目内容を取得する。
         // DAOパラメータを作成
         Gdl2Kan17ByCriteriaInEntity gdl2Kan17ByCriteriaInEntity = new Gdl2Kan17ByCriteriaInEntity();
         gdl2Kan17ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdl2Kan17ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<Gdl2Kan17OutEntity> gdl2Kan17OutEntityList = cpnTucGdl2Kan17SelectMapper.findGdl2Kan17ByCriteria(gdl2Kan17ByCriteriaInEntity);

         // 2.2.7.2. 上記取得したOUTPUT情報.特記事項がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdl2Kan17OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemoKnj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-⑦行動上の障害・精神症状関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_ACTION_HANDYCAP_MENTAL_SYMPTOMS_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項に設定する
                  gui00808OperationInfo.setItemContents(item.getMemoKnj());
                  // オペレーション情報リストの項目IDを固定値「8」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_8);
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.2.7. ＧＬ＿⑦問題行動関係（改訂）情報を取得する。------END
         
         // 2.2.8. ＧＬ＿⑧医療・健康関係（改訂）情報を取得する。------START
         // 2.2.8.1. 下記のＧＬ＿⑧医療・健康関係（改訂）情報取得DAOを利用し、ＧＬ＿⑧医療・健康関係（改訂）の項目内容を取得する。
         // DAOパラメータを作成
         Gdl2Kan18ByCriteriaInEntity gdl2Kan18ByCriteriaInEntity = new Gdl2Kan18ByCriteriaInEntity();
         gdl2Kan18ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdl2Kan18ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<Gdl2Kan18OutEntity> gdl2Kan18OutEntityList = cpnTucGdl2Kan18SelectMapper.findGdl2Kan18ByCriteria(gdl2Kan18ByCriteriaInEntity);

         // 2.2.8.2. 上記取得したOUTPUT情報.特記事項がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdl2Kan18OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemo4Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"6-⑧医療・健康関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_MEDICAL_CARE_HEALTH_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記事項に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo4Knj());
                  // オペレーション情報リストの項目IDを固定値「9」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_9);
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.2.8. ＧＬ＿⑧医療・健康関係（改訂）情報を取得する。------END
       } 
       // 2.3. リクエストパラメータ.改訂フラグ＝1,2,3ではない(H21/4改訂版) の場合、特記事項情報を取得する。
       else if(!CommonConstants.REVISION_FLAG_1.equals(inDto.getRevisionFlag()) && !CommonConstants.REVISION_FLAG_2.equals(inDto.getRevisionFlag()) && !CommonConstants.REVISION_FLAG_3.equals(inDto.getRevisionFlag()) && !CommonConstants.REVISION_FLAG_4.equals(inDto.getRevisionFlag())) {
         // 2.3.1. ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）情報を取得する。------START
         // 2.3.1.1. 下記のＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）情報取得DAOを利用し、ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）の項目内容を取得する。
         // DAOパラメータを作成
         Gdl4Kan11H21ByCriteriaInEntity gdl4Kan11H21ByCriteriaInEntity = new Gdl4Kan11H21ByCriteriaInEntity();
         gdl4Kan11H21ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdl4Kan11H21ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<Gdl4Kan11H21OutEntity> gdl4Kan11H21OutEntityList = cpnTucGdl4Kan11H21SelectMapper.findGdl4Kan11H21ByCriteria(gdl4Kan11H21ByCriteriaInEntity);

         gdl4Kan11H21OutEntityList.forEach(item -> {
            if(null != item) {
               // 2.3.1.2. 上記取得したOUTPUT情報.特記1（体位変換・起居）がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
               if(StringUtils.isNotBlank(item.getMemo1Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"６－①基本(身体機能・起居)動作 [体位変換・起居]"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_BASIC_ACTION_POSITION_CHANGE_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記1（体位変換・起居）に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo1Knj());
                  // オペレーション情報リストの項目IDを固定値「1」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_1);
   
                  operationInfoList.add(gui00808OperationInfo);
               }

               // 2.3.1.3. 上記取得したOUTPUT情報.特記2（入浴）がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
               if(StringUtils.isNotBlank(item.getMemo2Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"６－①基本(身体機能・起居)動作 [入浴]"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_BASIC_ACTION_BATHING_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記2（入浴）に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo2Knj());
                  // オペレーション情報リストの項目IDを固定値「2」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_2);
   
                  operationInfoList.add(gui00808OperationInfo);
               }

               // 2.3.1.4. 上記取得したOUTPUT情報.特記3（コミュニケーション）がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
               if(StringUtils.isNotBlank(item.getMemo3Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"６－①基本(身体機能・起居)動作 [コミュニケーションの状況・方法]"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_BASIC_COMMUNICATION_SITUATION_METHOD_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記3（コミュニケーション）に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo3Knj());
                  // オペレーション情報リストの項目IDを固定値「3」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_3);
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.3.1. ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）情報を取得する。------END
         
         // 2.3.2. ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）情報を取得する。------START
         // 2.3.2.1. 下記のＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）情報取得DAOを利用し、ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）の項目内容を取得する。
         // DAOパラメータを作成
         Gdl4Kan12H21ByCriteriaInEntity gdl4Kan12H21ByCriteriaInEntity = new Gdl4Kan12H21ByCriteriaInEntity();
         gdl4Kan12H21ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdl4Kan12H21ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<Gdl4Kan12H21OutEntity> gdl4Kan12H21OutEntityList = cpnTucGdl4Kan12H21SelectMapper.findGdl4Kan12H21ByCriteria(gdl4Kan12H21ByCriteriaInEntity);

         gdl4Kan12H21OutEntityList.forEach(item -> {
            if(null != item) {
               // 2.3.2.2. 上記取得したOUTPUT情報.特記1（食事）がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
               if(StringUtils.isNotBlank(item.getMemo1Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"６－②生活機能（食事・排泄等） [食事]"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_LIFE_FUNCTION_MEAL_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記1（食事）に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo1Knj());
                  // オペレーション情報リストの項目IDを固定値「4」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_4);

   
                  operationInfoList.add(gui00808OperationInfo);
               }

               // 2.3.2.3. 上記取得したOUTPUT情報.特記2（排泄）がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
               if(StringUtils.isNotBlank(item.getMemo2Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"６－②生活機能（食事・排泄等） [排泄等]"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_LIFE_FUNCTION_EXCRETION_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記2（排泄）に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo2Knj());
                  // オペレーション情報リストの項目IDを固定値「5」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_5);

   
                  operationInfoList.add(gui00808OperationInfo);
               }

               // 2.3.2.4. 上記取得したOUTPUT情報.特記3（外出）がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
               if(StringUtils.isNotBlank(item.getMemo3Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"６－②生活機能（食事・排泄等） [外出]"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_LIFE_FUNCTION_GOING_OUT_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記3（外出）に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo3Knj());
                  // オペレーション情報リストの項目IDを固定値「6」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_6);

   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.3.2. ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）情報を取得する。------END
         
         // 2.3.3. ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）情報を取得する。------START
         // 2.3.3.1. 下記のＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）情報取得DAOを利用し、ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）の項目内容を取得する。
         // DAOパラメータを作成
         Gdl4Kan13H21ByCriteriaInEntity gdl4Kan13H21ByCriteriaInEntity = new Gdl4Kan13H21ByCriteriaInEntity();
         gdl4Kan13H21ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdl4Kan13H21ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<Gdl4Kan13H21OutEntity> gdl4Kan13H21OutEntityList = cpnTucGdl4Kan13H21SelectMapper.findGdl4Kan13H21ByCriteria(gdl4Kan13H21ByCriteriaInEntity);

         // 2.3.3.2. 上記取得したOUTPUT情報.特記、解決すべき課題などがnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdl4Kan13H21OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemo1Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"６－③認知機能、④精神・行動障害"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_COGNITIVE_FUNCTION_MENTAL_ACTION_HANDYCAP_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記、解決すべき課題などに設定する
                  gui00808OperationInfo.setItemContents(item.getMemo1Knj());
                  // オペレーション情報リストの項目IDを固定値「7」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_7);

   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.3.3. ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）情報を取得する。------END
         
         // 2.3.4. ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）情報を取得する。------START
         // 2.3.4.1. 下記のＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）情報取得DAOを利用し、ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）の項目内容を取得する。
         // DAOパラメータを作成
         Gdl4Kan15H21ByCriteriaInEntity gdl4Kan15H21ByCriteriaInEntity = new Gdl4Kan15H21ByCriteriaInEntity();
         gdl4Kan15H21ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdl4Kan15H21ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<Gdl4Kan15H21OutEntity> gdl4Kan15H21OutEntity = cpnTucGdl4Kan15H21SelectMapper.findGdl4Kan15H21ByCriteria(gdl4Kan15H21ByCriteriaInEntity);

         // 2.3.4.2. 上記取得したOUTPUT情報.特記、解決すべき課題などがnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdl4Kan15H21OutEntity.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemo1Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"６－⑤社会生活（への適応）力"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_SOCIETY_LIFE_ADAPTATION_TIKARA_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記、解決すべき課題などに設定する
                  gui00808OperationInfo.setItemContents(item.getMemo1Knj());
                  // オペレーション情報リストの項目IDを固定値「8」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_8);
               
   
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.3.4. ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）情報を取得する。------END
         
         // 2.3.5. ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）情報を取得する。------START
         // 2.3.5.1. 下記のＧＬ＿⑥医療・健康関係（Ｈ２１改訂）情報取得DAOを利用し、ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）の項目内容を取得する。
         // DAOパラメータを作成
         Gdl4Kan16H21ByCriteriaInEntity gdl4Kan16H21ByCriteriaInEntity = new Gdl4Kan16H21ByCriteriaInEntity();
         gdl4Kan16H21ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
         gdl4Kan16H21ByCriteriaInEntity.setAlGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
         // DAOを実行
         List<Gdl4Kan16H21OutEntity> gdl4Kan16H21OutEntityList = cpnTucGdl4Kan16H21SelectMapper.findGdl4Kan16H21ByCriteria(gdl4Kan16H21ByCriteriaInEntity);

         // 2.3.5.2. 上記取得したOUTPUT情報.特記（医療・健康）がnull以外、かつ、空以外の場合、オペレーション情報リストに下記レコードを追加する。
         gdl4Kan16H21OutEntityList.forEach(item -> {
            if(null != item) {
               if(StringUtils.isNotBlank(item.getMemo1Knj())) {
                  Gui00808OperationInfo gui00808OperationInfo = new Gui00808OperationInfo();
                  // オペレーション情報リストの項目タイトルを固定値「"６－⑥医療･健康関係"」に設定する
                  gui00808OperationInfo.setItemTitle(CommonConstants.SPECICAL_NOTE_MATTER_MEDICAL_6_CARE_HEALTH_RELATION);
                  // オペレーション情報リストの項目内容をOUTPUT情報.特記（医療・健康）に設定する
                  gui00808OperationInfo.setItemContents(item.getMemo1Knj());
                  // オペレーション情報リストの項目IDを固定値「9」に設定する
                  gui00808OperationInfo.setItemId(CommonConstants.SPECICAL_NOTE_MATTER_ITEM_ID_9);
                
                  operationInfoList.add(gui00808OperationInfo);
               }
            }
         });
         // 2.3.5. ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）情報を取得する。------END
       }

       /*
       * ===============3. 上記処理で取得した結果レスポンスを返却する。===============
       * 
       */
      // 戻り情報を設定
      SpecialNoteMatterSelectServiceOutDto outDto = new SpecialNoteMatterSelectServiceOutDto();
      // オペレーション情報
      outDto.setOperationInfoList(operationInfoList);
      
      LOG.info("END");

      return outDto;
   }
}
