package jp.ndsoft.carebase.cmn.api.report.logic;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ImplementationMonitoringReportPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ImplementationMonitoringReportPrintOptionPrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ImplementationMonitoringReportPrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportImplementationMonitoringInfo;
import jp.ndsoft.carebase.cmn.api.logic.KghCmn03gFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.HolidayArrayOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.JigyoRirekiInfoDto;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.ImplementationMonitoringReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.model.ImplementationMonitoringReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmpComShokuinByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmpComShokuinOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmpMstShoninByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmpMstShoninOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnKkjKjisshi1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnKkjKjisshi1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnKkjKjisshi2PByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnKkjKjisshi2POutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnKkjMstByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnKkjMstOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinFullNameByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinFullNameOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.UserinfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.UserinfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YoushikiNoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YoushikiNoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocPrtYoushikiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnKkjKjisshi2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucHcf1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucShoninSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KeikakuJisshihyouKihonRirekiSelectMapper;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * U01600~u01607_実施モニタリング表(計画実施表) 帳票出力
 *
 * <AUTHOR>
 * @since 2025.08.13
 */
@Component
public class ImplementationMonitoringReportLogic {

    private final String CPN = "CPN";
    private final String CMN = "CMN";
    private final String BIKOUKNJ_KEY = "bikouKnj";

    /** 計画実施表基本履歴情報取得 */
    @Autowired
    private KeikakuJisshihyouKihonRirekiSelectMapper keikakuJisshihyouKihonRirekiSelectMapper;

    /** 計画実施表基本データ取得 */
    @Autowired
    private CpnKkjKjisshi2SelectMapper cpnKkjKjisshi2SelectMapper;

    /** 03-01 職員基本取得情報 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /** 文字列入力支援取得情報 */
    @Autowired
    private CpnMucHcf1SelectMapper cpnMucHcf1SelectMapper;

    /** 利用者基本（１－６）の情報取得 */
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;

    @Autowired
    private CpnMucShoninSelectMapper cpnMucShoninSelectMapper;

    /** 記入用シート印刷時、処理日から印刷日を返す */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** がかったるい（yyyy/mm/dd 型で返してくれない）から被せる. */
    @Autowired
    private KghCmn03gFunc01Logic kghCmn03gFunc01Logic;

    /** 事業履歴より事業所名を取得する */
    @Autowired
    private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;

    /** 複数行のテキストかラムでのワードラップ他の 表示をエミュレートし単一行文字配列に分解する */
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    /** 文書番号を取得する */
    @Autowired
    private ComMocPrtYoushikiSelectMapper comMocPrtYoushikiSelectMapper;

    public ImplementationMonitoringReportServiceInDto getImplementationMonitoringParameters(
            ImplementationMonitoringReportParameterModel model) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 【リクエストパラメータ】.印刷設定
        ImplementationMonitoringReportPrintSet printSet = model.getPrintSet();

        // 【リクエストパラメータ】.印刷オプション
        ImplementationMonitoringReportPrintOption printOption = model.getPrintOption();

        // 【リクエストパラメータ】.印刷対象履歴リスト
        ImplementationMonitoringReportPrintOptionPrintSubjectHistory history = model.getPrintSubjectHistoryList()
                .get(CommonConstants.INT_0);

        // 帳票用データ詳細
        ImplementationMonitoringReportServiceInDto infoInDto = new ImplementationMonitoringReportServiceInDto();

        /*
         * 1.単項目チェック以外の入力チェック
         * 特になし
         */

        /*
         * 2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。※返却する情報の編集要領は「レスポンスパラメータ詳細」を参照する。
         * リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、実施モニタリング表(計画実施表(番号あり・担当者あり))情報の取得。
         */

        if (CommonConstants.STR_TRUE.equals(printOption.getEmptyFlg())) {
            // 記入用シートを印刷するフラグ = true の場合
            infoInDto = getDefaultParamters(model, printOption, history);
            infoInDto.setEmptyFlg(Boolean.TRUE);
        } else {
            // 記入用シートを印刷するフラグ = false の場合
            infoInDto = getImplementationMonitoringParameters(history, model, printSet, printOption);
        }

        // 帳票タイトル
        infoInDto.setTitle(history.getChoPrtList().get(CommonConstants.INT_0).getPrtTitle());
        // 初期設定マスタの情報のケアプラン方式
        infoInDto.setCpnFlg(CommonDtoUtil.strValToInt(model.getCpnFlg()));
        // 初期設定マスタの情報の計画書様式
        infoInDto.setCksFlg(CommonDtoUtil.strValToInt(model.getCksFlg()));
        // 指定日印刷区分
        infoInDto.setPrDate(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // 事業者名
        String lastDayOfMonth = kghCmn03gFunc01Logic.getTukimatu(model.getAppYmd() + CommonConstants.STRING_LAST_DAY);
        JigyoRirekiInfoDto jigyoInfo = kghKrkZCpnFuncLogic
                .getJigyoRirekiKnj(CommonDtoUtil.strValToInt(model.getJigyoInfo().getSvJigyoId()), lastDayOfMonth);
        infoInDto.setJigyoName(jigyoInfo.getJigyoRirekiRyakuKnj());

        // 敬称
        if (CommonConstants.STR_TRUE.equals(printOption.getTitleOfHonorFlg())) {
            infoInDto.setKeisho(ReportUtil.nullToEmpty(printOption.getTitleOfHonor()));
        } else if (CommonConstants.STR_FALSE.equals(printOption.getTitleOfHonorFlg())
                && CommonConstants.STR_TRUE.equals(model.getStKeishoFlg())) {
            infoInDto.setKeisho(ReportUtil.nullToEmpty(model.getStKeisho()));
        } else if (CommonConstants.STR_FALSE.equals(model.getStKeishoFlg())) {
            infoInDto.setKeisho(CommonConstants.KEISHO_STR_TONO);
        }

        // 計画作成日印刷区分
        infoInDto.setIiSakuFlg(CommonDtoUtil.strValToInt(printSet.getPrintKubun()));
        // 作成者フラグ
        infoInDto.setLbSaku(CommonDtoUtil.strValToInt(printOption.getPlanAuthorPrintFlg()));
        // 担当者表示フラグ
        infoInDto.setKkjTantoFlg(CommonDtoUtil.strValToInt(model.getKkjTantoFlg()));
        // 頻度表示フラグ
        infoInDto.setKkjHindoFlg(CommonDtoUtil.strValToInt(model.getKkjHindoFlg()));
        // 計画取込内容同じフラグ
        infoInDto.setIsSameOmitted(CommonConstants.STR_TRUE.equals(model.getIsSameOmitted()));
        // 計画取込内容空白フラグ
        infoInDto.setIsBlankOmitted(CommonConstants.STR_TRUE.equals(model.getIsSameOmitted()));
        // モノクロ印刷モードフラグ
        infoInDto.setLsFlg(CommonDtoUtil.strValToInt(printOption.getMonochromePrintFlg()));
        // 計画取込区分
        infoInDto.setKkjTorikomi(CommonDtoUtil.strValToInt(model.getKkjTorikomi()));
        // 印刷枠の高さを最小フラグ
        infoInDto.setPrintFrameSizeFlg(CommonDtoUtil.strValToInt(printOption.getPrintFrameSizeFlg()));
        // 最小行入力
        infoInDto.setLiRow(CommonDtoUtil.strValToInt(printOption.getPrintFrameSize()));
        // 記入フラグ
        infoInDto.setKinyuFlg(CommonDtoUtil.strValToInt(printOption.getRemarksPrintFlg()));
        // 記録との連携フラグ
        infoInDto.setCbKrkRenkeiFlg(CommonDtoUtil.strValToInt(model.getCbKrkRenkeiFlg()));
        // 今月の総括フラグ
        infoInDto.setBikouFlg(CommonDtoUtil.strValToInt(printOption.getSummaryPrintFlg()));
        // 承認欄表示フラグ
        infoInDto.setSyoninFlg(CommonDtoUtil.strValToInt(printOption.getApprovalPrintFlg()));

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false の場合
     * 
     * @param history     印刷対象履歴リスト
     * @param model       リクエストパラメータ
     * @param printSet    印刷設定
     * @param printOption 印刷オプション
     * @return 処理結果
     */
    private ImplementationMonitoringReportServiceInDto getImplementationMonitoringParameters(
            ImplementationMonitoringReportPrintOptionPrintSubjectHistory history,
            ImplementationMonitoringReportParameterModel model,
            ImplementationMonitoringReportPrintSet printSet,
            ImplementationMonitoringReportPrintOption printOption) throws Exception {

        // 計画取込区分
        String kkjTorikomi = model.getKkjTorikomi();
        // 担当者表示フラグ
        String kkjTantoFlg = model.getKkjTantoFlg();
        // 頻度表示フラグ
        String kkjHindoFlg = model.getKkjHindoFlg();
        // システム略称
        String sys3ryaku = model.getSys3ryaku();
        // 番号区分
        String kkjBangouFlg = model.getKkjBangouFlg();
        // サービス事業者ID
        String svJigyoId = model.getJigyoInfo().getSvJigyoId();
        // システムコード
        String syscd = model.getSyscd();
        // 利用者ID
        String userId = history.getUserId();
        // 職員ID
        String shoukuId = history.getChoPrtList().get(CommonConstants.INT_0).getShokuId();
        // セクション
        String section = history.getChoPrtList().get(CommonConstants.INT_0).getSection();
        // 印刷する要介護度
        String kaigodo = printOption.getKaigodo();

        // 帳票用データ詳細
        ImplementationMonitoringReportServiceInDto infoInDto = new ImplementationMonitoringReportServiceInDto();

        // 指定日
        if (CommonConstants.STR_2.equals(printSet.getShiTeiKubun())) {
            List<String> shiteYmd = ReportUtil.getLocalDateToJapanDateTimeFormat(printSet.getShiTeiDate());
            if (CollectionUtils.isNotEmpty(shiteYmd)) {
                infoInDto.setShiTeiDateGG(shiteYmd.get(CommonConstants.INT_0));
                infoInDto.setShiTeiDateYY(shiteYmd.get(CommonConstants.INT_1));
                infoInDto.setShiTeiDateMM(shiteYmd.get(CommonConstants.INT_2));
                infoInDto.setShiTeiDateDD(shiteYmd.get(CommonConstants.INT_3));
            } else {
                infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
                infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
                infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
                infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
            }
        }
        if (CommonConstants.STR_3.equals(printSet.getShiTeiKubun())) {
            String blankDate = nds3GkFunc01Logic.blankDate(LocalDate.now().toString());
            infoInDto.setShiTeiDateGG(
                    blankDate.substring(CommonConstants.INT_0, blankDate.indexOf(CommonConstants.FULL_WIDTH_SPACE)));
            infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
            infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
            infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        }
        if (CommonConstants.STR_1.equals(printSet.getShiTeiKubun())) {
            infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
            infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
            infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
            infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        }

        // 文書管理番号
        F3gkGetProfileInDto param = new F3gkGetProfileInDto();
        // システムコード
        param.setGsyscd(syscd);
        // 職員ID
        param.setShokuId(CommonDtoUtil.strValToInt(shoukuId));
        // 法人ID
        param.setHoujinId(CommonConstants.INT_0);
        // 施設ID
        param.setShisetuId(CommonConstants.INT_0);
        // 事業所ID
        param.setSvJigyoId(CommonConstants.INT_0);
        // 機能名
        param.setKinounameKnj(CommonConstants.KINOU_NAME_PRT);
        // セクション
        param.setSectionKnj(section);
        // キー
        param.setKeyKnj(CommonConstants.ISO9001_FLG);
        // 初期値
        param.setAsDefault(CommonConstants.STR_0);
        String param1 = nds3GkFunc01Logic.getF3gkProfile(param);
        if (CommonConstants.STR_1.equals(param1)) {
            // 設定の読込（システム環境以外の設定）情報取得の戻り値は"1"（正常参照）の場合
            YoushikiNoByCriteriaInEntity youshikiNoByCriteriaInParam = new YoushikiNoByCriteriaInEntity();
            youshikiNoByCriteriaInParam.setLlSvJigyoId(svJigyoId);
            youshikiNoByCriteriaInParam.setAsSection(section);
            // 帳票書式番号設定マスタ情報取得のDAOを利用し、文書番号を取得する
            List<YoushikiNoOutEntity> youshikiNoByCriteriaInList = comMocPrtYoushikiSelectMapper
                    .findYoushikiNoByCriteria(youshikiNoByCriteriaInParam);
            if (CollectionUtils.isNotEmpty(youshikiNoByCriteriaInList)) {
                infoInDto.setBunsyoKanriNo(
                        ReportUtil.nullToEmpty(youshikiNoByCriteriaInList.get(CommonConstants.INT_0).getYoushikiNo()));
            } else {
                infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);
            }
        }

        /* 2.1. 下記計画実施表基本履歴情報取得DAOを利用し、計画実施表ヘッダ情報取得し、ヘッダ情報リストを作成する。 */
        // 計画実施表基本履歴情報取得
        CpnKkjKjisshi1ByCriteriaInEntity cpnKkjKjisshi1Param = new CpnKkjKjisshi1ByCriteriaInEntity();
        cpnKkjKjisshi1Param.setAlKjisshi1Id(history.getKjisshi1Id());
        List<CpnKkjKjisshi1OutEntity> cpnKkjKjisshi1List = keikakuJisshihyouKihonRirekiSelectMapper
                .findCpnKkjKjisshi1ByCriteria(cpnKkjKjisshi1Param);

        /* 2.2. 下記データ情報取得DAOを利用し、実施モニタリング表データ情報取得し、ヘッダ情報リストを作成する。 */
        // 計画実施表基本データ取得
        CpnKkjKjisshi2PByCriteriaInEntity cpnKkjKjisshi2Param = new CpnKkjKjisshi2PByCriteriaInEntity();
        cpnKkjKjisshi2Param.setAlKjisshi1Id(history.getKjisshi1Id());
        List<CpnKkjKjisshi2POutEntity> cpnKkjKjisshi2pList = cpnKkjKjisshi2SelectMapper
                .findCpnKkjKjisshi2PByCriteria(cpnKkjKjisshi2Param);

        /* 2.3. 下記の職員基本情報取得のDAOを利用し、記入者名を取得する。 */
        // 2.3.1 下記の職員基本情報取得のDAOを利用し、職員名漢字情報を取得する。
        CmpComShokuinByCriteriaInEntity CmpComShokuinParam = new CmpComShokuinByCriteriaInEntity();
        List<CmpComShokuinOutEntity> CmpComShokuinList = comMscShokuinSelectMapper
                .findCmpComShokuinByCriteria(CmpComShokuinParam);

        // 変数:作成者
        Integer sakuseId = CommonConstants.INT_0;
        // 変数:処理年月
        String syoriYm = CommonConstants.BLANK_STRING;

        // 2.3.2. 下記処理を行う、当月日数1の記入者 ~当月日数31の記入者を設定する。
        List<Map.Entry<String, String>> dayKinyuBikouKnjInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cpnKkjKjisshi1List)) {
            CpnKkjKjisshi1OutEntity cpnKkjKjisshi1 = cpnKkjKjisshi1List.get(CommonConstants.INT_0);
            sakuseId = cpnKkjKjisshi1.getShokuId();
            syoriYm = cpnKkjKjisshi1.getSyoriYm();
            for (int i = CommonConstants.INT_1; i <= CommonConstants.INT_31; i++) {
                String index = i < CommonConstants.INT_10 ? CommonConstants.STR_0 + i : CommonDtoUtil.objValToString(i);
                // 職員IDを取得
                Method getDayKinyu = cpnKkjKjisshi1.getClass()
                        .getMethod("getDay" + index + "Kinyu");
                Integer dayKinyuId = (Integer) getDayKinyu.invoke(cpnKkjKjisshi1);
                // 備考を取得
                Method getDayBikou = cpnKkjKjisshi1.getClass()
                        .getMethod("getDay" + index + "BikouKnj");
                String dayBikou = ReportUtil.nullToEmpty(getDayBikou.invoke(cpnKkjKjisshi1));

                // 職員名（姓）と職員名（名）を取得
                String shokuName = CommonConstants.BLANK_STRING;
                Optional<CmpComShokuinOutEntity> shokuInfo = CmpComShokuinList.stream()
                        .filter(item -> item.getChkShokuId() == dayKinyuId).findFirst();
                if (shokuInfo.isPresent()) {
                    shokuName = ReportUtil
                            .nullToEmpty(shokuInfo.get().getShokuin1Knj() + shokuInfo.get().getShokuin2Knj());
                }

                // 今月日数の記入者/備考
                Map.Entry<String, String> dayBikouKnjInfo = new AbstractMap.SimpleEntry<>(shokuName, dayBikou);

                dayKinyuBikouKnjInfoList.add(dayBikouKnjInfo);
            }
            // 今月日数の記入者／備考リスト
            infoInDto.setDayKinyuBikouKnjList(dayKinyuBikouKnjInfoList);

            // 処理年月
            List<String> syoriYmd = ReportUtil
                    .getLocalDateToJapanDateTimeFormat(syoriYm + CommonConstants.STRING_FIRST_DAY);
            if (CollectionUtils.isNotEmpty(syoriYmd)) {
                infoInDto.setSyoriYmGG(syoriYmd.get(CommonConstants.INT_0));
                infoInDto.setSyoriYmYY(syoriYmd.get(CommonConstants.INT_1));
                infoInDto.setSyoriYmMM(syoriYmd.get(CommonConstants.INT_2));
            } else {
                infoInDto.setSyoriYmGG(CommonConstants.FULL_WIDTH_SPACE);
                infoInDto.setSyoriYmYY(CommonConstants.FULL_WIDTH_SPACE);
                infoInDto.setSyoriYmMM(CommonConstants.FULL_WIDTH_SPACE);
            }

            // 計画作成日
            List<String> createYmd = ReportUtil.getLocalDateToJapanDateTimeFormat(cpnKkjKjisshi1.getCreateYmd());
            if (CollectionUtils.isNotEmpty(createYmd)) {
                infoInDto.setCreateYmdGG(createYmd.get(CommonConstants.INT_0));
                infoInDto.setCreateYmdYY(createYmd.get(CommonConstants.INT_1));
                infoInDto.setCreateYmdMM(createYmd.get(CommonConstants.INT_2));
                infoInDto.setCreateYmdDD(createYmd.get(CommonConstants.INT_3));
            } else {
                infoInDto.setCreateYmdGG(CommonConstants.FULL_WIDTH_SPACE);
                infoInDto.setCreateYmdYY(CommonConstants.FULL_WIDTH_SPACE);
                infoInDto.setCreateYmdMM(CommonConstants.FULL_WIDTH_SPACE);
                infoInDto.setCreateYmdDD(CommonConstants.FULL_WIDTH_SPACE);
            }

            List<HolidayArrayOutDto> HolidayArray = nds3GkFunc01Logic.getHolidayArray(cpnKkjKjisshi1.getSyoriYm(),
                    CommonConstants.INT_0);
            if (CollectionUtils.isNotEmpty(HolidayArray)) {
                // 休日フラグリストの休日フラグ
                List<Integer> holidayFlgList = new ArrayList<>();
                for (HolidayArrayOutDto holiday : HolidayArray) {
                    Integer holidayFlg = CommonConstants.INT_0;
                    if (holiday.getHoliday() == CommonConstants.INT_1 || holiday.getWeek() == CommonConstants.INT_0) {
                        // 稼働フラグ ＝ 1 または 曜日 = 0
                        holidayFlg = CommonConstants.INT_1;
                    } else if (holiday.getHoliday() != CommonConstants.INT_1
                            && holiday.getWeek() != CommonConstants.INT_0) {
                        // 稼働フラグ <> 1 または 曜日 <> 0
                        holidayFlg = CommonConstants.INT_2;
                    } else {
                        // 上記以外の場合
                        holidayFlg = CommonConstants.INT_0;
                    }
                    holidayFlgList.add(holidayFlg);
                }

                // 休日フラグリスト
                infoInDto.setHolidayFlgList(holidayFlgList);
            }

            // 備考
            infoInDto.setBikouKnj(cpnKkjKjisshi1.getBikouKnj());
        }

        /*
         * 3. テキスト変換
         */
        // 変数:計画取込
        String torikomiKnj = CommonConstants.BLANK_STRING;
        // 変数:番号
        String bango = CommonConstants.BLANK_STRING;
        // 変数:担当者
        String tantoKnj = CommonConstants.BLANK_STRING;
        // 変数:頻度
        String hindoKnj = CommonConstants.BLANK_STRING;

        // 計画取込印刷文字数＝24
        Integer torikomiKnjFontCnt = CommonConstants.INT_24;
        // 担当者印刷文字数＝16
        Integer tantoKnjFontCnt = CommonConstants.INT_16;
        // 頻度印刷文字数
        Integer hindoKnjFontCnt = CommonConstants.INT_8;
        // 3.1. 番号と計画取込データ設定
        List<ReportImplementationMonitoringInfo> detailInfoList = new ArrayList<>();
        ReportImplementationMonitoringInfo implementationMonitoringInfo = new ReportImplementationMonitoringInfo();
        // 属性のデフォルト値設定
        setPropertyDefaultValue(implementationMonitoringInfo);
        if (CollectionUtils.isNotEmpty(cpnKkjKjisshi2pList)) {
            // 計画実施表基本データがありの場合
            CpnKkjKjisshi2POutEntity cpnKkjKjisshi2p = cpnKkjKjisshi2pList.get(CommonConstants.INT_0);
            switch (kkjTorikomi) {
                case CommonConstants.STR_2:
                    // リクエストパラメータ.データ.計画取込区分 = 2(サービス内容)の場合
                    // 計画取込 = 「2.2」の介護
                    torikomiKnj = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getKs22KaigoKnj());
                    // 番号 = 「2.2」の介護番号
                    bango = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getKs22KaigoNo());
                    break;
                case CommonConstants.STR_3:
                    // リクエストパラメータ.データ.計画取込区分 = 3(サービス内容)の場合
                    // 計画取込 = 「2.2」の長期
                    torikomiKnj = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getKs22ChoukiKnj());
                    // 番号 = 「2.2」の課題番号
                    bango = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getKs22KadaiNo());
                    break;
                case CommonConstants.STR_4:
                    // リクエストパラメータ.データ.計画取込区分 = 4(サービス内容)の場合
                    // 計画取込 = 「2.2」の短期
                    torikomiKnj = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getKs22TankiKnj());
                    // 番号 = 「2.2」の課題番号
                    bango = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getKs22KadaiNo());
                    break;
                case CommonConstants.STR_1:
                    // リクエストパラメータ.データ.計画取込区分 = 2(サービス内容)の場合
                    // 計画取込 = 「2.2」の具体的
                    torikomiKnj = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getKs22GutaitekiKnj());
                    // 番号 = 「2.2」の課題番号
                    bango = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getKs22KadaiNo());
                    break;
            }

            /* 印字文字数取得 */
            // 計画取込印刷文字数＝24
            torikomiKnjFontCnt = CommonConstants.INT_24;

            /* 3.2. 担当者・頻度の表示設定 */
            // 3.2.1. リクエストパラメータ.データ.担当者表示フラグ = 1 且つ リクエストパラメータ.データ.頻度表示フラグ =
            // 1の場合、担当者・頻度を表示する。
            if (CommonConstants.STR_1.equals(kkjTantoFlg) && CommonConstants.STR_1.equals(kkjHindoFlg)) {

                if (CommonDtoUtil.strValToInt(cpnKkjKjisshi2p.getKs22Id()) > CommonConstants.INT_0) {
                    // 「2.2」の計画書(2)データID > 0の場合
                    // 担当者 ＝「2.2」のサービス種
                    tantoKnj = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getKs22SvShuKnj());
                    // 頻度 ＝「2.2」の頻度
                    hindoKnj = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getKs22HindoKnj());
                } else {
                    // 上記以外の場合
                    // 担当者 ＝「2.2」の担当者
                    tantoKnj = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getTantoKnj());
                    // 頻度 ＝「2.2」の頻度
                    hindoKnj = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getHindoKnj());
                }

                // 印字文字数取得
                // 計画取込データ印刷文字数＝16
                torikomiKnjFontCnt = CommonConstants.INT_16;
                // 担当者印刷文字数＝16
                tantoKnjFontCnt = CommonConstants.INT_16;
                // 頻度印刷文字数
                hindoKnjFontCnt = CommonConstants.INT_8;
            }

            // 3.2.2. リクエストパラメータ.データ.担当者表示フラグ = 1 且つ リクエストパラメータ.データ.頻度表示フラグ = 0の場合、担当者を表示する。
            if (CommonConstants.STR_1.equals(kkjTantoFlg) && CommonConstants.STR_0.equals(kkjHindoFlg)) {
                if (CommonDtoUtil.strValToInt(cpnKkjKjisshi2p.getKs22Id()) > CommonConstants.INT_0) {
                    // 「2.2」の計画書(2)データID > 0の場合
                    // 担当者 ＝「2.2」のサービス種
                    tantoKnj = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getKs22SvShuKnj());
                } else {
                    // 上記以外の場合
                    // 担当者 ＝「2.2」の担当者
                    tantoKnj = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getTantoKnj());
                }

                // 担当者印刷文字数＝16
                tantoKnjFontCnt = CommonConstants.INT_16;
            }

            // 3.2.3. リクエストパラメータ.データ.担当者表示フラグ = 0 且つ リクエストパラメータ.データ.頻度表示フラグ = 1の場合、頻度を表示する。
            if (CommonConstants.STR_0.equals(kkjTantoFlg) && CommonConstants.STR_1.equals(kkjHindoFlg)) {
                if (CommonDtoUtil.strValToInt(cpnKkjKjisshi2p.getKs22Id()) > CommonConstants.INT_0) {
                    // 「2.2」の計画書(2)データID > 0の場合
                    hindoKnj = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getKs22HindoKnj());
                } else {
                    // 上記以外の場合
                    hindoKnj = ReportUtil.nullToEmpty(cpnKkjKjisshi2p.getHindoKnj());
                }

                // 頻度印刷文字数
                hindoKnjFontCnt = CommonConstants.INT_16;
            }
        }

        // 番号
        implementationMonitoringInfo.setBango(bango);

        /* 3.3. 実施モニタリング表明細情報を設定する */
        // 変換後の有効な行数取得する
        // 計画取込について
        Pair<Integer, List<String>> torikomuKnjPair = kghCmpF01Logic.cmpLtext2Rows(torikomiKnj, torikomiKnjFontCnt);
        StringBuffer torikomuKnjBuffer = new StringBuffer();
        for (int index = CommonConstants.INT_0; index < torikomuKnjPair.getRight().size(); index++) {
            torikomuKnjBuffer.append(torikomuKnjPair.getRight().get(index));
            if (index < torikomuKnjPair.getRight().size() - CommonConstants.INT_1) {
                torikomuKnjBuffer.append(ReportConstants.LINE_BREAKS);
            }
        }
        implementationMonitoringInfo.setTorikomiKnj(ReportUtil.nullToEmpty(torikomuKnjBuffer.toString()));

        // 担当者について
        Pair<Integer, List<String>> tantoKnjPair = kghCmpF01Logic.cmpLtext2Rows(tantoKnj, tantoKnjFontCnt);
        StringBuffer tantoKnjBuffer = new StringBuffer();
        for (int index = CommonConstants.INT_0; index < tantoKnjPair.getRight().size(); index++) {
            tantoKnjBuffer.append(tantoKnjPair.getRight().get(index));
            if (index < tantoKnjPair.getRight().size() - CommonConstants.INT_1) {
                tantoKnjBuffer.append(ReportConstants.LINE_BREAKS);
            }
        }
        implementationMonitoringInfo.setTantoKnj(ReportUtil.nullToEmpty(tantoKnjBuffer.toString()));

        // 頻度について
        Pair<Integer, List<String>> hindoKnjPair = kghCmpF01Logic.cmpLtext2Rows(hindoKnj, hindoKnjFontCnt);
        StringBuffer hindoKnjBuffer = new StringBuffer();
        for (int index = CommonConstants.INT_0; index < hindoKnjPair.getRight().size(); index++) {
            hindoKnjBuffer.append(hindoKnjPair.getRight().get(index));
            if (index < hindoKnjPair.getRight().size() - CommonConstants.INT_1) {
                hindoKnjBuffer.append(ReportConstants.LINE_BREAKS);
            }
        }
        implementationMonitoringInfo.setHindoKnj(ReportUtil.nullToEmpty(hindoKnjBuffer.toString()));

        /* 4. 帳票用データ詳細.実施モニタリング表詳細情報のリストの当月日数の記号を設定する。 */
        // 4.1. 下記の文字列入力支援取得のDAOを利用し、実施モニタリングDDWS情報を取得する。
        CpnKkjMstByCriteriaInEntity cpnComMastByCriteriaInParam = new CpnKkjMstByCriteriaInEntity();
        // リクエストパラメータ.データ.システム略称 = "CPN"の場合は１、リクエストパラメータ.データ.システム略称 = "CMN"の場合2
        int flg = CPN.equals(sys3ryaku) ? CommonConstants.INT_1
                : CMN.equals(sys3ryaku) ? CommonConstants.INT_2 : CommonConstants.INT_1;
        cpnComMastByCriteriaInParam.setAlKbnFlg(flg);
        List<CpnKkjMstOutEntity> cpnComMastByCriteriaInList = cpnMucHcf1SelectMapper
                .findCpnKkjMstByCriteria(cpnComMastByCriteriaInParam);

        // 4.2. 下記処理を行う、当月日数 1の記号 ~当月日数31の記号を設定する。
        if (CollectionUtils.isNotEmpty(cpnComMastByCriteriaInList) && CollectionUtils.isNotEmpty(cpnKkjKjisshi2pList)) {
            CpnKkjKjisshi2POutEntity cpnKkjKjisshi2p = cpnKkjKjisshi2pList.get(CommonConstants.INT_0);
            for (int i = CommonConstants.INT_1; i <= CommonConstants.INT_31; i++) {
                // 「2.2」.day + iを取得
                Method getDay = cpnKkjKjisshi2p.getClass().getMethod("getDay" + i);
                short day = (short) getDay.invoke(cpnKkjKjisshi2p);

                // 入力区分＝30 と 区分コード＝「2.2」.day + i を絞込む
                Optional<CpnKkjMstOutEntity> cpnComMast = cpnComMastByCriteriaInList.stream()
                        .filter(item -> item.getCf1Kbn() == CommonConstants.INT_30 &&
                                item.getKbnCd() == day)
                        .findFirst();
                // 当月日数[i]の記号
                if (cpnComMast.isPresent()) {
                    Method setDay = implementationMonitoringInfo.getClass().getMethod("setDay" +
                            i);
                    setDay.invoke(implementationMonitoringInfo, ReportUtil.nullToEmpty(cpnComMast.get().getTextKnj()));
                }
            }
        }

        // 実施モニタリング表詳細情報リスト
        detailInfoList.add(implementationMonitoringInfo);
        infoInDto.setDetailInfoList(new JRBeanCollectionDataSource(detailInfoList));

        /* 5. 曜日を設定する */
        List<String> weekList = new ArrayList<>();
        String week = CommonConstants.BLANK_STRING;
        if (null != syoriYm) {
            // 入力引数に月の最終日を返す
            String lastDayOfMonth = nds3GkFunc01Logic.getTukimatu(syoriYm + CommonConstants.STRING_FIRST_DAY);
            // 日数表示フラグ
            switch (CommonDtoUtil.strValToInt(lastDayOfMonth)) {
                case CommonConstants.INT_28:
                    infoInDto.setDayFlg(CommonConstants.INT_1);
                    break;
                case CommonConstants.INT_29:
                    infoInDto.setDayFlg(CommonConstants.INT_2);
                    break;
                case CommonConstants.INT_30:
                    infoInDto.setDayFlg(CommonConstants.INT_3);
                    break;
            }

            String day = nds3GkFunc01Logic.getNdsRighta(lastDayOfMonth, CommonConstants.INT_2);
            if (CommonDtoUtil.strValToInt(day) >= CommonConstants.INT_1
                    && CommonDtoUtil.strValToInt(day) <= CommonConstants.INT_31) {
                List<HolidayArrayOutDto> HolidayList = nds3GkFunc01Logic.getHolidayArray(syoriYm,
                        CommonConstants.INT_0);
                int index = CommonConstants.INT_1;
                for (HolidayArrayOutDto holiday : HolidayList) {
                    if (index >= CommonConstants.INT_31) {
                        break;
                    }
                    switch (holiday.getWeek()) {
                        case CommonConstants.INT_0:
                            // 日
                            week = CommonConstants.STRING_DAY;
                            break;
                        case CommonConstants.INT_1:
                            // 月
                            week = CommonConstants.STR_MONDAY;
                            break;
                        case CommonConstants.INT_2:
                            // 火
                            week = CommonConstants.STR_THUSDAY;
                            break;
                        case CommonConstants.INT_3:
                            // 水
                            week = CommonConstants.STR_WEDNESDAY;
                            break;
                        case CommonConstants.INT_4:
                            // 木
                            week = CommonConstants.STR_THURSDAY;
                            break;
                        case CommonConstants.INT_5:
                            // 金
                            week = CommonConstants.STR_FRIDAY;
                            break;
                        case CommonConstants.INT_6:
                            // 土
                            week = CommonConstants.STR_SATURDAY;
                            break;
                    }
                    weekList.add(week);
                    index++;
                }
                infoInDto.setWeekList(weekList);
            }

            // 要介護度
            String cpnFlg = model.getCpnFlg();
            if (!CommonDtoUtil.checkStringEqual(cpnFlg, CommonConstants.STR_5)) {
                // リクエストパラメータ.データ.ケアプラン方式 <> 5の場合
                // 条件、日付より要介護度文字列を取得する
                String yaokaigoDo = kghCmpF01Logic.getCmpYokai(CommonDtoUtil.strValToInt(svJigyoId),
                        CommonDtoUtil.strValToInt(userId), CommonDtoUtil.strValToInt(kaigodo), CommonConstants.INT_0,
                        lastDayOfMonth);
                infoInDto.setYokaigoDo(ReportUtil.nullToEmpty(yaokaigoDo));
            }
        }

        /* 6. 帳票用データ詳細.今月の総括1行あたり表示桁数を設定する。 */
        Integer columnWidth = 140;
        if (CommonConstants.STR_1.equals(kkjBangouFlg)) {
            // リクエストパラメータ.番号区分 = 1の場合
            if (CommonConstants.STR_0.equals(kkjTantoFlg) && CommonConstants.STR_0.equals(kkjHindoFlg)) {
                // リクエストパラメータ.担当者表示フラグ = 0 且つ リクエストパラメータ.頻度表示フラグ = 0の場合
                columnWidth = 122;
            }
        } else {
            // 上記以外の場合
            columnWidth = 140;
        }
        List<Map<String, String>> bikouKnjList = new ArrayList<>();
        for (int i = CommonConstants.INT_1; i <= CommonConstants.INT_31; i++) {
            String num = i < CommonConstants.INT_10 ? CommonConstants.STR_0 + i : CommonDtoUtil.objValToString(i);
            // 備考を取得
            Method getDayBikou = infoInDto.getClass()
                    .getMethod("getDay" + num + "BikouKnj");

            String dayBikou = (String) getDayBikou.invoke(infoInDto);

            // 複数行のテキストかラムでのワードラップ他の 表示をエミュレートし単一行文字配列に分解する
            Pair<Integer, List<String>> naiyoPair = kghCmpF01Logic.cmpLtext2Rows(dayBikou, columnWidth);
            StringBuffer bikouKnj = new StringBuffer();
            for (int index = CommonConstants.INT_0; index < naiyoPair.getRight().size(); index++) {
                bikouKnj.append(naiyoPair.getRight().get(index));
                if (index < naiyoPair.getRight().size() - CommonConstants.INT_1) {
                    bikouKnj.append(ReportConstants.LINE_BREAKS);
                }
            }
            Map<String, String> bikouKnjMap = new HashMap<>();
            bikouKnjMap.put(BIKOUKNJ_KEY, bikouKnj.toString());
            bikouKnjList.add(bikouKnjMap);
        }
        // 今月の総括リスト
        infoInDto.setBikouList(new JRBeanCollectionDataSource(bikouKnjList));

        /*
         * 7. 利用者基本情報を取得する。
         */
        /* 7.1. 利用者基本情報の取得 */
        // 下記の利用者基本（１－６）情報取得のDAOを利用し、利用者の情報を取得する。
        UserinfoByCriteriaInEntity userInfoParam = new UserinfoByCriteriaInEntity();
        userInfoParam.setUser(CommonDtoUtil.strValToInt(userId));
        List<UserinfoOutEntity> userInfoList = comTucUserSelectMapper.findUserinfoByCriteria(userInfoParam);

        // 利用者名
        if (CollectionUtils.isNotEmpty(userInfoList)) {
            String riyouShaNm = ReportUtil
                    .nullToEmpty(
                            userInfoList.get(CommonConstants.INT_0).getName1Knj() + CommonConstants.FULL_WIDTH_SPACE
                                    + userInfoList.get(CommonConstants.INT_0).getName2Knj());
            infoInDto.setRiyoushaNm(riyouShaNm);
        } else {
            infoInDto.setRiyoushaNm(CommonConstants.BLANK_STRING);
        }

        /*
         * 8. 計画作成者を取得する。
         */
        /* 5.1 下記の職員基本情報取得のDAOを利用し、職員名漢字情報を取得する。 */
        ShokuinFullNameByCriteriaInEntity shokuinFullNameParam = new ShokuinFullNameByCriteriaInEntity();
        shokuinFullNameParam.setAlShoku(CommonDtoUtil.objValToString(sakuseId));
        List<ShokuinFullNameOutEntity> shokuinFullNameList = comMscShokuinSelectMapper
                .findShokuinFullNameByCriteria(shokuinFullNameParam);

        // 計画作成者
        if (CollectionUtils.isNotEmpty(shokuinFullNameList)) {
            String fullName = ReportUtil.nullToEmpty(shokuinFullNameList.get(CommonConstants.INT_0).getShokuin1Knj()
                    + CommonConstants.FULL_WIDTH_SPACE
                    + shokuinFullNameList.get(CommonConstants.INT_0).getShokuin2Knj());
            infoInDto.setShokuName(fullName);
        } else {
            infoInDto.setShokuName(CommonConstants.BLANK_STRING);
        }

        // 結果を返し
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合
     * 
     * @param cpnFlg 要介護度
     * @return デフォルト値設定の結果
     */
    private ImplementationMonitoringReportServiceInDto getDefaultParamters(
            ImplementationMonitoringReportParameterModel model,
            ImplementationMonitoringReportPrintOption printOption,
            ImplementationMonitoringReportPrintOptionPrintSubjectHistory history) {

        // 要介護度
        String cpnFlg = model.getCpnFlg();
        // ｻｰﾋﾞｽ事業者ID
        String svJigyoId = model.getJigyoInfo().getSvJigyoId();
        // フラグ
        Integer flg = ReportConstants.INT_0;
        // 帳票コード
        String ticketCd = CommonConstants.BLANK_STRING;
        if (CommonConstants.STR_1.equals(model.getShoninFlg())) {
            // リクエストパラメータ.データ.帳票毎の承認欄の保持=1の場合は１
            flg = ReportConstants.INT_1;
            // リクエストパラメータ.データ.帳票毎の承認欄の保持=1の場合はリクエストパラメータ.データ.出力帳票印刷情報リスト[0].セクション
            if (history != null && CollectionUtils.isNotEmpty(history.getChoPrtList())) {
                ticketCd = ReportUtil.nullToEmpty(history.getChoPrtList().get(CommonConstants.INT_0).getSection());
            }
        }

        // 今月の総括を印刷するフラグ
        String summaryPrintFlg = printOption.getSummaryPrintFlg();
        // 承認欄を印刷するフラグ
        String approvalPrintFlg = printOption.getApprovalPrintFlg();
        // 日々の備考欄を印刷するフラグ
        String remarksPrintFlg = printOption.getRemarksPrintFlg();

        ImplementationMonitoringReportServiceInDto infoDto = new ImplementationMonitoringReportServiceInDto();
        // 指定日
        infoDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        infoDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        infoDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        infoDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);

        // 要介護度
        if (!CommonDtoUtil.checkStringEqual(cpnFlg, CommonConstants.STR_5)) {
            infoDto.setYokaigoDo(CommonConstants.BLANK_STRING);
        }

        // 日数表示フラグ
        infoDto.setDayFlg(CommonConstants.INT_0);

        // 利用者名
        infoDto.setRiyoushaNm(CommonConstants.BLANK_STRING);
        // 処理年月
        infoDto.setSyoriYmGG(CommonConstants.FULL_WIDTH_SPACE);
        infoDto.setSyoriYmYY(CommonConstants.FULL_WIDTH_SPACE);
        infoDto.setSyoriYmMM(CommonConstants.FULL_WIDTH_SPACE);
        // 計画作成日
        infoDto.setCreateYmdGG(CommonConstants.FULL_WIDTH_SPACE);
        infoDto.setCreateYmdYY(CommonConstants.FULL_WIDTH_SPACE);
        infoDto.setCreateYmdMM(CommonConstants.FULL_WIDTH_SPACE);
        infoDto.setCreateYmdDD(CommonConstants.FULL_WIDTH_SPACE);
        // 計画作成者
        infoDto.setShokuName(CommonConstants.BLANK_STRING);

        // 今月日数の記入者／備考リスト
        List<Map.Entry<String, String>> dayKinyuBikouKnjInfoList = new ArrayList<>();
        for (int i = CommonConstants.INT_1; i <= CommonConstants.INT_31; i++) {
            dayKinyuBikouKnjInfoList
                    .add(new AbstractMap.SimpleEntry<>(CommonConstants.BLANK_STRING, CommonConstants.BLANK_STRING));
        }
        infoDto.setDayKinyuBikouKnjList(dayKinyuBikouKnjInfoList);

        // 備考
        infoDto.setBikouKnj(CommonConstants.BLANK_STRING);

        // 今月の総括リスト
        List<Map<String, String>> bikouKnjList = new ArrayList<>();
        Map<String, String> bikouKnjMap = new HashMap<>();
        StringBuffer bikouKnj = new StringBuffer();
        for (int i = CommonConstants.INT_0; i < CommonConstants.INT_8; i++) {
            bikouKnj.append(CommonConstants.BLANK_STRING);
            if (i < CommonConstants.INT_7) {
                bikouKnj.append(ReportConstants.LINE_BREAKS);
            }
        }
        bikouKnjMap.put(BIKOUKNJ_KEY, bikouKnj.toString());
        bikouKnjList.add(bikouKnjMap);
        infoDto.setBikouList(new JRBeanCollectionDataSource(bikouKnjList));

        // 当月の週間ラベル
        List<String> weekList = new ArrayList<>();
        for (int i = CommonConstants.INT_1; i <= CommonConstants.INT_31; i++) {
            weekList.add(CommonConstants.BLANK_STRING);
        }
        infoDto.setWeekList(weekList);

        // 文書管理番号
        infoDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        // 10.リクエストパラメータ.記入用シートを印刷するフラグ = true の場合、帳票行数設定する。
        // 10.1. 帳票行数初期値：33
        int lineCnt = 33;
        int emptyLineCnt = CommonConstants.INT_6;
        // 実施モニタリング表詳細情報リスト
        List<ReportImplementationMonitoringInfo> detailInfoEmptyList = new ArrayList<>();
        ReportImplementationMonitoringInfo detailInfo = new ReportImplementationMonitoringInfo();
        setPropertyDefaultValue(detailInfo);
        /*
         * 10.2. リクエストパラメータ.データ.印刷オプション.今月の総括を印刷するフラグ
         * ＝"1"且つリクエストパラメータ.データ.印刷オプション.承認欄を印刷するフラグ ＝ "1" の場合
         */
        if (CommonConstants.STR_1.equals(summaryPrintFlg) && CommonConstants.STR_1.equals(approvalPrintFlg)) {
            lineCnt = CommonConstants.INT_18;
            // 実施モニタリング表詳細情報リスト空白行数：6
            emptyLineCnt = CommonConstants.INT_6;
        }

        // リクエストパラメータ.データ.印刷オプション.日々の備考欄を印刷するフラグ
        // ＝"1"且つリクエストパラメータ.データ.印刷オプション.承認欄を印刷するフラグ ＝"1"の場合
        if (CommonConstants.STR_1.equals(remarksPrintFlg) && CommonConstants.STR_1.equals(approvalPrintFlg)) {
            /*
             * 10.3.1. リクエストパラメータ.データ.印刷オプション.承認欄を印刷するフラグ="1"とリクエストパラメータ.記入用シートを印刷するフラグ =
             * true の場合、承認欄設定の共通関数で承認欄を設定する
             */
            // 下記の承認欄マスタ情報取得のDAOを利用し、承認欄マスタ情報を取得する。
            CmpMstShoninByCriteriaInEntity cmpMstShoninParam = new CmpMstShoninByCriteriaInEntity();
            cmpMstShoninParam.setJigyoId(CommonDtoUtil.strValToInt(svJigyoId));
            cmpMstShoninParam.setAlFlg(flg);
            cmpMstShoninParam.setAsCode(ticketCd);
            cmpMstShoninParam.setAiType(CommonConstants.INT_1);
            List<CmpMstShoninOutEntity> cmpMstShoninList = cpnMucShoninSelectMapper
                    .findCmpMstShoninByCriteria(cmpMstShoninParam);

            // 10.3.2
            // 変数.DispKbn=2
            int dispKbn = ReportConstants.INT_2;
            if (CollectionUtils.isNotEmpty(cmpMstShoninList)) {
                dispKbn = cmpMstShoninList.get(CommonConstants.INT_0).getDispKbn();
            }

            // 変数.DispKbnより、総括、実施モニタリング表明細等を設定する。
            if (dispKbn == ReportConstants.INT_4) {
                lineCnt = CommonConstants.INT_15;
            } else {
                lineCnt = CommonConstants.INT_18;
                if (!CollectionUtils.isNotEmpty(detailInfoEmptyList)) {
                    // 実施モニタリング表詳細情報リスト空白行数：6
                    emptyLineCnt = CommonConstants.INT_6;
                }
            }
        }

        /*
         * 10.4.
         * リクエストパラメータ.データ.印刷オプション.承認欄を印刷するフラグ="0"とリクエストパラメータ.データ.印刷オプション.日々の備考欄を印刷するフラグ
         * = "1" の場合
         */
        if (CommonConstants.STR_0.equals(approvalPrintFlg) && CommonConstants.STR_1.equals(remarksPrintFlg)) {
            lineCnt = CommonConstants.INT_21;
            // 実施モニタリング表詳細情報リスト空白行数：7
            emptyLineCnt = CommonConstants.INT_7;
        }

        /*
         * 10.5.
         * リクエストパラメータ.データ.印刷オプション.承認欄を印刷するフラグ="0"とリクエストパラメータ.データ.印刷オプション.今月の総括を印刷するフラグ =
         * 1 の場合、
         */
        if (CommonConstants.STR_0.equals(approvalPrintFlg) && CommonConstants.STR_1.equals(summaryPrintFlg)) {
            lineCnt = CommonConstants.INT_24;
            // 実施モニタリング表詳細情報リスト空白行数：8
            emptyLineCnt = CommonConstants.INT_8;
        }

        /*
         * 10.6. リクエストパラメータ.データ.印刷オプション.承認欄を印刷するフラグ="1"の場合、
         */
        if (CommonConstants.STR_1.equals(approvalPrintFlg)) {
            // リクエストパラメータ.データ.印刷オプション.今月の総括を印刷するフラグ = "0"
            // 且つリクエストパラメータ.データ.印刷オプション.日々の備考欄を印刷するフラグ = "0" の場合、
            if (CommonConstants.STR_0.equals(remarksPrintFlg) && CommonConstants.STR_0.equals(summaryPrintFlg)) {
                lineCnt = CommonConstants.INT_27;
                // 実施モニタリング表詳細情報リスト空白行数：9
                emptyLineCnt = CommonConstants.INT_9;
            }
        }

        /*
         * 10.7. リクエストパラメータ.データ.印刷オプション.承認欄を印刷するフラグ="0"の場合、
         */
        if (CommonConstants.STR_0.equals(approvalPrintFlg)) {
            // リクエストパラメータ.データ.印刷オプション.今月の総括を印刷するフラグ =
            // "0"且つリクエストパラメータ.データ.印刷オプション.日々の備考欄を印刷するフラグ = "0" の場合、
            if (CommonConstants.STR_0.equals(remarksPrintFlg) && CommonConstants.STR_0.equals(summaryPrintFlg)) {
                // 実施モニタリング表詳細情報リスト空白行数：11
                emptyLineCnt = CommonConstants.INT_11;
            }
        }

        // 実施モニタリング表詳細情報リスト空白行
        for (int i = CommonConstants.INT_0; i < emptyLineCnt; i++) {
            detailInfoEmptyList.add(detailInfo);
        }

        infoDto.setDetailInfoList(new JRBeanCollectionDataSource(detailInfoEmptyList));
        // 最小行入力 TODO
        infoDto.setLiRow(lineCnt);

        // 結果を返し
        return infoDto;
    }

    /**
     * 属性のデフォルト値設定
     * 
     * @param obj
     */
    public void setPropertyDefaultValue(Object obj) {
        if (Objects.isNull(obj)) {
            return;
        }

        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            try {
                Class<?> fieldType = field.getType();
                field.setAccessible(true);
                if (fieldType == String.class) {
                    setDefaultValue(obj, field, CommonConstants.BLANK_STRING);
                } else if (fieldType == Integer.class) {
                    setDefaultValue(obj, field, CommonConstants.INT_0);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * デフォルト値設定
     * 
     * @param obj
     * @param field
     * @param defaultValue
     */
    private static void setDefaultValue(Object obj, Field field, Object defaultValue) throws Exception {
        String fieldName = field.getName();
        String setterName = "set" + fieldName.substring(CommonConstants.INT_0, CommonConstants.INT_1).toUpperCase()
                + fieldName.substring(CommonConstants.INT_1);
        Method setterMethod = obj.getClass().getMethod(setterName, field.getType());
        if (setterMethod != null) {
            setterMethod.invoke(obj, defaultValue);
        } else {
            field.set(obj, defaultValue);
        }
    }
}
