package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00796SerInfo;
import jp.ndsoft.carebase.cmn.api.logic.AssessmentHomeLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeSaveServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeServiceUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeServiceUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4SerH21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5SerR3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdlRirekiMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4SerH21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4SerH21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5SerR3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5SerR3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRireki;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRirekiCriteria;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * @since 2025.03.28
 * <AUTHOR> 陳帆
 * @apiNote GUI00796_［アセスメント］画面（居宅）（3） データ保存
 */
@Service
public class AssessmentHomeServiceUpdateServiceImpl
        extends
        UpdateServiceImpl<AssessmentHomeServiceUpdateServiceInDto, AssessmentHomeServiceUpdateServiceOutDto> {

    private static Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** ＧＬ＿居宅アセスメント履歴 */
    @Autowired
    private CpnTucGdlRirekiMapper cpnTucGdlRirekiMapper;

    /** ＧＬ＿サービス利用状況（Ｈ２１改訂） */
    @Autowired
    private CpnTucGdl4SerH21Mapper cpnTucGdl4SerH21Mapper;

    /** ＧＬ＿サービス利用状況（R３改訂） */
    @Autowired
    private CpnTucGdl5SerR3Mapper cpnTucGdl5SerR3Mapper;

    /** ［アセスメント］画面（居宅）画面のロジッククラス */
    @Autowired
    private AssessmentHomeLogic assessmentHomeLogic;

    @Autowired
    private PlatformTransactionManager transactionManager;    

    /**
     * GUI00796_［アセスメント］画面（居宅）（3）のデータ保存
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    @Override
    protected AssessmentHomeServiceUpdateServiceOutDto mainProcess(AssessmentHomeServiceUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        AssessmentHomeServiceUpdateServiceOutDto outDto = new AssessmentHomeServiceUpdateServiceOutDto();
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        /*
         * ===============2. バリデーションチェックを行う。===============
         * 
         */
        boolean resFlg = assessmentHomeLogic.documentPerformValidationCheck(loginDto);
        // 2.2.4. 【変数】.処理結果が「false：失敗」の場合
        if (!resFlg) {
            // 2.2.4. 【変数】.処理結果が「false：失敗」の場合、下記返却する情報を設定して、後続処理を飛ばして、API処理を正常終了とする。
            // レスポンス.計画期間ID ： リクエストパラメータ.計画期間ID
            outDto.setSc1Id(loginDto.getSc1Id());
            // レスポンス.アセスメントID ： リクエストパラメータ.アセスメントID
            outDto.setGdlId(loginDto.getGdlId());
            // レスポンス.エラー区分: "1
            outDto.setErrKbn(CommonConstants.E_DOC_CHECK_ERR);
            return outDto;
        }
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transactionB = transactionManager.getTransaction(def);
        try {
            // ［アセスメント］画面（居宅）（3） データ保存
            outDto = mainProcessMealUpdate(inDto);
            transactionManager.commit(transactionB);
        } catch (Exception e) {
            transactionManager.rollback(transactionB);
            throw e;
        }

        // e-文書_ケアチェック表１のリクエストパラメータ
        TransactionStatus transactionC = transactionManager.getTransaction(def);
        try {
            mainProcessEdocOut(inDto, outDto);
            transactionManager.commit(transactionC);
        } catch (Exception e) {
            transactionManager.rollback(transactionC);
            throw e;
        }

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * API定義 (e文書出力)
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected void mainProcessEdocOut(
            AssessmentHomeServiceUpdateServiceInDto inDto, AssessmentHomeServiceUpdateServiceOutDto outDto)
            throws Exception {
        // 入力dtoをLoginDtoにコピーする
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);

        /*
         * ===============e文書出力===============
         * 
         */
        // e文書出力
        String errKbn = assessmentHomeLogic.getPrint(loginDto, outDto.getSc1Id());
        outDto.setErrKbn(errKbn);
    }

    /**
     * ［アセスメント］画面（居宅）（3） データ保存
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected AssessmentHomeServiceUpdateServiceOutDto mainProcessMealUpdate(
            AssessmentHomeServiceUpdateServiceInDto inDto)
            throws Exception {
        AssessmentHomeServiceUpdateServiceOutDto outDto = new AssessmentHomeServiceUpdateServiceOutDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. 計画対象期間の保存処理===============
         * 
         */
        // 2.1. リクエストパラメータ.計画対象期間IDがnullの場合、【27-06 記録共通期間】情報を登録する。
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        loginDto = this.setAssessmentHomeSaveServiceInDto(inDto);

        if (inDto.getSc1Id() == null || StringUtil.isEmpty(inDto.getSc1Id())) {
            this.assessmentHomeLogic.insertKghTucKrkKikan(loginDto);
            // 変数.計画対象期間ID
            inDto.setSc1Id(loginDto.getSc1Id());
        }

        // 「2.」で処理した変数.計画対象期間ID
        outDto.setSc1Id(inDto.getSc1Id());

        /*
         * ===============3.リクエストパラメータ.削除処理区分が2:画面を履歴ごと削除するの場合、下記テーブルデータを更新する==========
         * 
         * 
         */
        if (CommonConstants.DELETE_KBN_2.equals(inDto.getDeleteKbn())) {
            assessmentHomeLogic.homeLogicsyuri(loginDto, CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        } else if (CommonConstants.DELETE_KBN_1.equals(inDto.getDeleteKbn())) {
            /*
             * ===============4.リクエストパラメータ.削除処理区分が1:画面のみ削除するの場合===============
             * 
             */
            // 4.1. リクエストパラメータ.改定フラグが4「H21/４改訂版」の場合、【ＧＬ＿サービス利用状況（Ｈ２１改訂）】情報を更新する。
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                this.deleteSerH21(inDto);

            } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                // 4.2. リクエストパラメータ.改定フラグが5「R3/４改訂版」の場合、【ＧＬ＿サービス利用状況（R３改訂）】情報を更新する。
                this.deleteSerR3(inDto);
            }

            // 4.3. 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
            this.clearRrk(inDto);

        } else {
            /*
             * ===============5.以外の場合===============
             * 
             */
            // 5.1. 履歴情報の保存処理
            // 5.1.1. リクエストパラメータ.履歴更新区分が"C":新規の場合、【ＧＬ＿居宅アセスメント履歴】情報を登録する。
            if (CommonDtoUtil.isHistoryCreate(inDto)) {
                this.insertRrk(inDto);

                // 登録の場合：「5.1.1.」で採番したアセスメントID
                outDto.setGdlId(inDto.getGdlId());

            } else if (CommonDtoUtil.isHistoryCreate(inDto)) {
                // 5.1.2. リクエストパラメータ.履歴更新区分が"U":更新の場合、【ＧＬ＿居宅アセスメント履歴】情報を更新する。
                this.updateRrk(inDto);

                // 更新の場合：リクエストパラメータ.アセスメントID
                outDto.setGdlId(inDto.getGdlId());

            }

            // 5.2. ｻｰﾋﾞｽ利用状況情報の保存処理
            // 5.2.1. リクエストパラメータ.更新区分が"C":新規の場合、ｻｰﾋﾞｽ利用状況情報を登録する。
            if (CommonDtoUtil.isCreate(inDto)) {
                // 5.2.1.1. リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、【ＧＬ＿サービス利用状況（Ｈ２１改訂）】情報を登録する。
                if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                    this.insertSerH21(inDto);

                } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                    // 5.2.1.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【ＧＬ＿サービス利用状況（R３改訂）】情報を登録する。
                    this.insertSerR3(inDto);

                }
            } else if (CommonDtoUtil.isUpdate(inDto)) {
                // 5.2.2. リクエストパラメータ.更新区分が"U":更新の場合、ｻｰﾋﾞｽ利用状況情報を更新する。
                // 5.2.2.1. リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、【ＧＬ＿サービス利用状況（Ｈ２１改訂）】情報を更新する。
                if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                    this.updateSerH21(inDto);

                } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                    // 5.2.2.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【ＧＬ＿サービス利用状況（R３改訂）】情報を更新する。
                    this.updateSerR3(inDto);
                }

            }

            // 5.3. リクエストパラメータ.【課題と目標リスト】の件数分、【ＧＬ＿課題と目標】情報を保存する。
            assessmentHomeLogic.homeLogicCpnTucGdlKadai(loginDto, CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        }

        return outDto;
    }

    /**
     * 【ＧＬ＿サービス利用状況（Ｈ２１改訂）】情報を削除更新する。
     * 関数名：deleteSerH21
     * 
     * @param inDto 更新パラメータ
     * @return 結果件数
     */
    private void deleteSerH21(AssessmentHomeServiceUpdateServiceInDto inDto) {
        // ＧＬ＿サービス利用状況（Ｈ２１改訂）パラメータを作成
        CpnTucGdl4SerH21Criteria cpnTucGdl4SerH21Criteria = new CpnTucGdl4SerH21Criteria();
        cpnTucGdl4SerH21Criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

       this.cpnTucGdl4SerH21Mapper.deleteByCriteria(cpnTucGdl4SerH21Criteria);


    }

    /**
     * 【ＧＬ＿サービス利用状況（R３改訂）】情報を削除更新する。
     * 関数名：deleteSerR3
     * 
     * @param inDto 更新パラメータ
     * @return 結果件数
     */
    private void deleteSerR3(AssessmentHomeServiceUpdateServiceInDto inDto) {
        // ＧＬ＿サービス利用状況（R３改訂）
        CpnTucGdl5SerR3Criteria cpnTucGdl5SerR3Criteria = new CpnTucGdl5SerR3Criteria();
        cpnTucGdl5SerR3Criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        this.cpnTucGdl5SerR3Mapper.deleteByCriteria(cpnTucGdl5SerR3Criteria);


    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
     * 関数名：clearRrk
     * 
     * @param inDto 更新パラメータ
     * @return 結果件数
     */
    private void clearRrk(AssessmentHomeServiceUpdateServiceInDto inDto) {
        // DAOパラメータを作成
        // 3.1.居宅アセスメント履歴を更新する
        // ＧＬ＿居宅アセスメント履歴パラメータを作成
        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                // 利用者ＩＤ
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 法人ID
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        // ｻｰﾋﾞｽ利用状況
        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();
        cpnTucGdlRireki.setAss3(StringUtils.EMPTY);

        // DAOを実行
        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRireki, criteria);

    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を登録する。
     * 関数名：insertRrk
     * 
     * @param inDto 更新パラメータ
     */
    private void insertRrk(AssessmentHomeServiceUpdateServiceInDto inDto) throws Exception {
        // ＧＬ＿居宅アセスメント履歴パラメータを作成
        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();

        // 計画期間ID
        cpnTucGdlRireki.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // 法人ID
        cpnTucGdlRireki.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        cpnTucGdlRireki.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        cpnTucGdlRireki.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ＩＤ
        cpnTucGdlRireki.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // アセスメント実施日
        cpnTucGdlRireki.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRireki.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // 本人の基本動作等1
        cpnTucGdlRireki.setAss3(CommonConstants.MARU);
        // Ｊの状態
        cpnTucGdlRireki.setAss12(CommonConstants.DASH);
        // 本人の基本動作等8
        cpnTucGdlRireki.setAss13(CommonConstants.DASH);
        // 改定フラグ
        cpnTucGdlRireki.setNinteiFormF(CommonDtoUtil.strValToInt(inDto.getNinteiFormF()));

        this.cpnTucGdlRirekiMapper.insertSelectiveAndReturn(cpnTucGdlRireki);

        // 変数.計画対象期間ID=採番したアセスメントID
        inDto.setGdlId(CommonDtoUtil.objValToString(cpnTucGdlRireki.getGdlId()));

    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
     * 関数名：updateRrk
     * 
     * @param inDto 更新パラメータ
     * @return 結果件数
     */
    private void updateRrk(AssessmentHomeServiceUpdateServiceInDto inDto) {
        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                // 利用者ＩＤ
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 法人ID
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        // 削除フラグ設定
        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();
        // アセスメント実施日
        cpnTucGdlRireki.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRireki.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // 記載者ID
        cpnTucGdlRireki.setAss3(CommonConstants.MARU);
        // Ｊの状態
        cpnTucGdlRireki.setAss12(CommonConstants.DASH);
        // 本人の基本動作等8
        cpnTucGdlRireki.setAss13(CommonConstants.DASH);

        // DAOを実行
        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRireki, criteria);

    }

    /**
     * 【ＧＬ＿サービス利用状況（Ｈ２１改訂）】情報を登録する。
     * 関数名：insertSerH21
     * 
     * @param inDto 更新パラメータ
     */
    private void insertSerH21(AssessmentHomeServiceUpdateServiceInDto inDto) throws Exception {
        CpnTucGdl4SerH21 cpnTucGdl4SerH21 = new CpnTucGdl4SerH21();
        // ＧＬ＿居宅アセスメンＧＬ＿サービス利用状況（Ｈ２１改訂）パラメータを作成
        cpnTucGdl4SerH21 = this.setCpnTucGdl4SerH21(inDto.getSerInfo(), true);

        // アセスメントID
        cpnTucGdl4SerH21.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // 計画期間ID
        cpnTucGdl4SerH21.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        this.cpnTucGdl4SerH21Mapper.insertSelective(cpnTucGdl4SerH21);

    }

    /**
     * 【ＧＬ＿サービス利用状況（R３改訂）】情報を登録する。
     * 関数名：insertSerR3
     * 
     * @param inDto 更新パラメータ
     */
    private void insertSerR3(AssessmentHomeServiceUpdateServiceInDto inDto) throws Exception {
        // ＧＬ＿サービス利用状況（R３改訂）パラメータを作成
        // ＧＬ＿居宅アセスメンＧＬ＿サービス利用状況（Ｈ２１改訂）パラメータを作成
        CpnTucGdl5SerR3 cpnTucGdl5SerR3 = new CpnTucGdl5SerR3();
        cpnTucGdl5SerR3 = this.setCpnTucGdl5SerR3(inDto.getSerInfo(), true);
        // アセスメントID
        cpnTucGdl5SerR3.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // 計画期間ID
        cpnTucGdl5SerR3.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        this.cpnTucGdl5SerR3Mapper.insertSelective(cpnTucGdl5SerR3);

    }

    /**
     * 【ＧＬ＿サービス利用状況（Ｈ２１改訂）】情報を更新する。
     * 関数名：updateSerH21
     * 
     * @param inDto 更新パラメータ
     * @return 結果件数
     */
    private void updateSerH21(AssessmentHomeServiceUpdateServiceInDto inDto) throws Exception {
        // ＧＬ＿サービス利用状況（Ｈ２１改訂）パラメータを作成
        CpnTucGdl4SerH21Criteria cpnTucGdl4SerH21Criteria = new CpnTucGdl4SerH21Criteria();
        cpnTucGdl4SerH21Criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        // 更新値設定
        CpnTucGdl4SerH21 cpnTucGdl4SerH21 = new CpnTucGdl4SerH21();
        cpnTucGdl4SerH21 = this.setCpnTucGdl4SerH21(inDto.getSerInfo(), false);

        this.cpnTucGdl4SerH21Mapper.updateByCriteriaSelective(
                cpnTucGdl4SerH21,
                cpnTucGdl4SerH21Criteria);
    }

    /**
     * 【ＧＬ＿サービス利用状況（R３改訂）】情報を更新する。
     * 関数名：updateSerR3
     * 
     * @param inDto 更新パラメータ
     * @return 結果件数
     */
    private void updateSerR3(AssessmentHomeServiceUpdateServiceInDto inDto) throws Exception {
        // ＧＬ＿サービス利用状況（Ｈ２１改訂）パラメータを作成
        CpnTucGdl5SerR3Criteria cpnTucGdl5SerR3Criteria = new CpnTucGdl5SerR3Criteria();
        cpnTucGdl5SerR3Criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        // 更新値設定
        CpnTucGdl5SerR3 cpnTucGdl5SerR3 = new CpnTucGdl5SerR3();
        cpnTucGdl5SerR3 = this.setCpnTucGdl5SerR3(inDto.getSerInfo(), false);

        this.cpnTucGdl5SerR3Mapper.updateByCriteriaSelective(
                cpnTucGdl5SerR3,
                cpnTucGdl5SerR3Criteria);
    }

    /**
     * 【27-06記録共通期間】情報を登録パラメータを設定する
     * 
     * @param inDto 施設マスタ
     * @return 【27-06記録共通期間】データ保存入力DTO.
     */
    private AssessmentHomeSaveServiceInDto setAssessmentHomeSaveServiceInDto(
            AssessmentHomeServiceUpdateServiceInDto inDto) {
        AssessmentHomeSaveServiceInDto assessmentHomeSaveServiceInDto = new AssessmentHomeSaveServiceInDto();
        // 法人ID
        assessmentHomeSaveServiceInDto.setHoujinId(inDto.getHoujinId());
        // 施設ID
        assessmentHomeSaveServiceInDto.setShisetuId(inDto.getShisetuId());
        // 事業者ID
        assessmentHomeSaveServiceInDto.setSvJigyoId(inDto.getSvJigyoId());
        // 利用者ID
        assessmentHomeSaveServiceInDto.setUserId(inDto.getUserId());
        // 種別ID
        assessmentHomeSaveServiceInDto.setSyubetsuId(inDto.getSyubetsuId());
        // 作成日
        assessmentHomeSaveServiceInDto.setKijunbiYmd(inDto.getKijunbiYmd());

        return assessmentHomeSaveServiceInDto;

    }

    /**
     * ＧＬ＿居宅アセスメンＧＬ＿サービス利用状況（Ｈ２１改訂）パラメータを作成
     * 
     * @param serInfo  ｻｰﾋﾞｽ利用状況情報
     * @param isInsert 登録/更新
     * 
     * @return ＧＬ＿居宅アセスメンＧＬ＿サービス利用状況（Ｈ２１改訂）パラメータ
     */
    private CpnTucGdl4SerH21 setCpnTucGdl4SerH21(Gui00796SerInfo serInfo, Boolean isInsert) {
        CpnTucGdl4SerH21 cpnTucGdl4SerH21 = new CpnTucGdl4SerH21();

        // 在宅サービス利用1
        cpnTucGdl4SerH21.setSer1Cd(CommonDtoUtil.strValToInt(serInfo.getSer1Cd()));
        // 在宅サービス利用2
        cpnTucGdl4SerH21.setSer2Cd(CommonDtoUtil.strValToInt(serInfo.getSer2Cd()));
        // 在宅サービス利用3
        cpnTucGdl4SerH21.setSer3Cd(CommonDtoUtil.strValToInt(serInfo.getSer3Cd()));
        // 在宅サービス利用4
        cpnTucGdl4SerH21.setSer4Cd(CommonDtoUtil.strValToInt(serInfo.getSer4Cd()));
        // 在宅サービス利用5
        cpnTucGdl4SerH21.setSer5Cd(CommonDtoUtil.strValToInt(serInfo.getSer5Cd()));
        // 在宅サービス利用6
        cpnTucGdl4SerH21.setSer6Cd(CommonDtoUtil.strValToInt(serInfo.getSer6Cd()));
        // 在宅サービス利用7
        cpnTucGdl4SerH21.setSer7Cd(CommonDtoUtil.strValToInt(serInfo.getSer7Cd()));
        // 在宅サービス利用8
        cpnTucGdl4SerH21.setSer8Cd(CommonDtoUtil.strValToInt(serInfo.getSer8Cd()));
        // 在宅サービス利用9
        cpnTucGdl4SerH21.setSer9Cd(CommonDtoUtil.strValToInt(serInfo.getSer9Cd()));
        // 在宅サービス利用10
        cpnTucGdl4SerH21.setSer10Cd(CommonDtoUtil.strValToInt(serInfo.getSer10Cd()));
        // 在宅サービス利用11
        cpnTucGdl4SerH21.setSer11Cd(CommonDtoUtil.strValToInt(serInfo.getSer11Cd()));
        // 在宅サービス利用12
        cpnTucGdl4SerH21.setSer12Cd(CommonDtoUtil.strValToInt(serInfo.getSer12Cd()));
        // 在宅サービス利用13
        cpnTucGdl4SerH21.setSer13Cd(CommonDtoUtil.strValToInt(serInfo.getSer13Cd()));
        // 在宅サービス利用14
        cpnTucGdl4SerH21.setSer14Cd(CommonDtoUtil.strValToInt(serInfo.getSer14Cd()));
        // 在宅サービス利用15
        cpnTucGdl4SerH21.setSer15Cd(CommonDtoUtil.strValToInt(serInfo.getSer15Cd()));
        if (isInsert) {
            // 在宅サービス利用16
            cpnTucGdl4SerH21.setSer16Cd(0);
        }
        // 在宅サービス利用17
        cpnTucGdl4SerH21.setSer17Cd(CommonDtoUtil.strValToInt(serInfo.getSer17Cd()));
        // 在宅サービス利用18
        cpnTucGdl4SerH21.setSer18Cd(CommonDtoUtil.strValToInt(serInfo.getSer18Cd()));
        // 在宅サービス利用19
        cpnTucGdl4SerH21.setSer19Cd(CommonDtoUtil.strValToInt(serInfo.getSer19Cd()));
        // 在宅サービス利用20
        cpnTucGdl4SerH21.setSer20Cd(CommonDtoUtil.strValToInt(serInfo.getSer20Cd()));
        // 在宅サービス利用21
        cpnTucGdl4SerH21.setSer21Cd(CommonDtoUtil.strValToInt(serInfo.getSer21Cd()));
        // 在宅サービス利用22
        cpnTucGdl4SerH21.setSer22Cd(CommonDtoUtil.strValToInt(serInfo.getSer22Cd()));
        // 在宅サービス利用23
        cpnTucGdl4SerH21.setSer23Cd(CommonDtoUtil.strValToInt(serInfo.getSer23Cd()));
        // 在宅サービス利用24
        cpnTucGdl4SerH21.setSer24Cd(CommonDtoUtil.strValToInt(serInfo.getSer24Cd()));
        // 在宅サービス利用25
        cpnTucGdl4SerH21.setSer25Cd(CommonDtoUtil.strValToInt(serInfo.getSer25Cd()));
        // 在宅サービス利用26
        cpnTucGdl4SerH21.setSer26Cd(CommonDtoUtil.strValToInt(serInfo.getSer26Cd()));
        // 在宅サービス利用27
        cpnTucGdl4SerH21.setSer27Cd(CommonDtoUtil.strValToInt(serInfo.getSer27Cd()));
        // 在宅サービス利用28
        cpnTucGdl4SerH21.setSer28Cd(CommonDtoUtil.strValToInt(serInfo.getSer28Cd()));
        // 在宅サービス利用29
        if (serInfo.getSer29Cd() == null || StringUtil.isEmpty(serInfo.getSer29Cd())) {
            cpnTucGdl4SerH21.setSer29Cd(0);
        } else {
            cpnTucGdl4SerH21.setSer29Cd(CommonDtoUtil.strValToInt(serInfo.getSer29Cd()));

        }
        // 在宅サービス利用30
        if (serInfo.getSer30Cd() == null || StringUtil.isEmpty(serInfo.getSer30Cd())) {
            cpnTucGdl4SerH21.setSer30Cd(0);
        } else {
            cpnTucGdl4SerH21.setSer30Cd(CommonDtoUtil.strValToInt(serInfo.getSer30Cd()));

        }

        // 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ)の利用回数 */
        cpnTucGdl4SerH21.setKaisu1(CommonDtoUtil.strValToInt(serInfo.getKaisu1()));
        // (介護予防)訪問入浴介護の利用回数 */
        cpnTucGdl4SerH21.setKaisu2(CommonDtoUtil.strValToInt(serInfo.getKaisu2()));
        // (介護予防)訪問看護の利用回数 */
        cpnTucGdl4SerH21.setKaisu3(CommonDtoUtil.strValToInt(serInfo.getKaisu3()));
        // (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝの利用回数 */
        cpnTucGdl4SerH21.setKaisu4(CommonDtoUtil.strValToInt(serInfo.getKaisu4()));
        // (介護予防)居宅療養管理指導の利用回数 */
        cpnTucGdl4SerH21.setKaisu5(CommonDtoUtil.strValToInt(serInfo.getKaisu5()));
        // 通所介護（ﾃﾞｲｻｰﾋﾞｽ）の利用回数 */
        cpnTucGdl4SerH21.setKaisu6(CommonDtoUtil.strValToInt(serInfo.getKaisu6()));
        // (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ）の利用回数 */
        cpnTucGdl4SerH21.setKaisu7(CommonDtoUtil.strValToInt(serInfo.getKaisu7()));
        // (介護予防)福祉用具貸与の利用回数 */
        cpnTucGdl4SerH21.setKaisu8(CommonDtoUtil.strValToInt(serInfo.getKaisu8()));
        // (介護予防)短期入所生活介護(特養等）の利用回数 */
        cpnTucGdl4SerH21.setKaisu9(CommonDtoUtil.strValToInt(serInfo.getKaisu9()));
        // (介護予防)短期入所生活介護(老健・診療所）の利用回数 */
        cpnTucGdl4SerH21.setKaisu10(CommonDtoUtil.strValToInt(serInfo.getKaisu10()));
        // 認知症対応型共同生活介護（回数） */
        cpnTucGdl4SerH21.setKaisu11(CommonDtoUtil.strValToInt(serInfo.getKaisu11()));
        // (介護予防)特定施設入所生活介護の利用回数 */
        cpnTucGdl4SerH21.setKaisu12(CommonDtoUtil.strValToInt(serInfo.getKaisu12()));
        // 特定(介護予防)福祉用具販売の利用回数 */
        cpnTucGdl4SerH21.setKaisu13(CommonDtoUtil.strValToInt(serInfo.getKaisu13()));
        // 住宅改修の利用回数 */
        cpnTucGdl4SerH21.setKaisu15(CommonDtoUtil.strValToInt(serInfo.getKaisu15()));
        // 生活支援員の訪問の利用回数 */
        cpnTucGdl4SerH21.setKaisu16(CommonDtoUtil.strValToInt(serInfo.getKaisu16()));
        // ふれあい・いきいきサロンの利用回数 */
        cpnTucGdl4SerH21.setKaisu17(CommonDtoUtil.strValToInt(serInfo.getKaisu17()));
        // 配食サービスの利用回数 */
        cpnTucGdl4SerH21.setKaisu18(CommonDtoUtil.strValToInt(serInfo.getKaisu18()));
        // 洗濯サービスの利用回数 */
        cpnTucGdl4SerH21.setKaisu19(CommonDtoUtil.strValToInt(serInfo.getKaisu19()));
        // 移動または外出支援の利用回数 */
        cpnTucGdl4SerH21.setKaisu20(CommonDtoUtil.strValToInt(serInfo.getKaisu20()));
        // 友愛訪問の利用回数 */
        cpnTucGdl4SerH21.setKaisu21(CommonDtoUtil.strValToInt(serInfo.getKaisu21()));
        // 老人福祉センターの利用回数 */
        cpnTucGdl4SerH21.setKaisu22(CommonDtoUtil.strValToInt(serInfo.getKaisu22()));
        // 老人憩いの家の利用回数 */
        cpnTucGdl4SerH21.setKaisu23(CommonDtoUtil.strValToInt(serInfo.getKaisu23()));
        // ガイドヘルパーの利用回数 */
        cpnTucGdl4SerH21.setKaisu24(CommonDtoUtil.strValToInt(serInfo.getKaisu24()));
        // 追加サービス1の利用回数 */
        cpnTucGdl4SerH21.setKaisu25(CommonDtoUtil.strValToInt(serInfo.getKaisu25()));
        // 追加サービス2の利用回数 */
        cpnTucGdl4SerH21.setKaisu26(CommonDtoUtil.strValToInt(serInfo.getKaisu26()));
        // 追加サービス3の利用回数 */
        cpnTucGdl4SerH21.setKaisu28(CommonDtoUtil.strValToInt(serInfo.getKaisu28()));
        // 追加サービス4の利用回数 */
        cpnTucGdl4SerH21.setKaisu29(CommonDtoUtil.strValToInt(serInfo.getKaisu29()));

        // 市町村特別給付メモ */
        cpnTucGdl4SerH21.setTokKyuKnj(serInfo.getTokKyuKnj());
        // 追加サービス1メモ */
        cpnTucGdl4SerH21.setAddSer1Knj(serInfo.getAddSer1Knj());
        // 追加サービス2メモ */
        cpnTucGdl4SerH21.setAddSer2Knj(serInfo.getAddSer2Knj());
        // 追加サービス3メモ */
        cpnTucGdl4SerH21.setAddSer3Knj(serInfo.getAddSer3Knj());
        // 追加サービス4メモ */
        cpnTucGdl4SerH21.setAddSer4Knj(serInfo.getAddSer4Knj());

        // 身障／補助具・日常生活用具メモ */
        cpnTucGdl4SerH21.setYouguKnj(serInfo.getYouguKnj());
        // 施設名 */
        cpnTucGdl4SerH21.setShisetsuNameKnj(serInfo.getShisetsuNameKnj());
        // 郵便番号 */
        cpnTucGdl4SerH21.setShisetsuZip(serInfo.getShisetsuZip());
        // 電話番号 */
        cpnTucGdl4SerH21.setShisetsuTel(serInfo.getShisetsuTel());
        // 住所（以前：備考） */
        cpnTucGdl4SerH21.setShisetsuMemoKnj(serInfo.getShisetsuMemoKnj());
        // 利用施設種別 */
        cpnTucGdl4SerH21.setShisetsuShu(CommonDtoUtil.strValToInt(serInfo.getShisetsuShu()));

        // 老齢関係 */
        cpnTucGdl4SerH21.setSeido1(CommonDtoUtil.strValToInt(serInfo.getSeido1()));
        // 障害関係 */
        cpnTucGdl4SerH21.setSeido2(CommonDtoUtil.strValToInt(serInfo.getSeido2()));
        // 遺族・寡婦 */
        cpnTucGdl4SerH21.setSeido3(CommonDtoUtil.strValToInt(serInfo.getSeido3()));
        // 恩給 */
        cpnTucGdl4SerH21.setSeido4(CommonDtoUtil.strValToInt(serInfo.getSeido4()));
        // 特別障害者手当 */
        cpnTucGdl4SerH21.setSeido5(CommonDtoUtil.strValToInt(serInfo.getSeido5()));
        // 生活保護 */
        cpnTucGdl4SerH21.setSeido6(CommonDtoUtil.strValToInt(serInfo.getSeido6()));
        // 生活福祉資金貸付 */
        cpnTucGdl4SerH21.setSeido7(CommonDtoUtil.strValToInt(serInfo.getSeido7()));
        // 高齢者住宅整備資金貸付 */
        cpnTucGdl4SerH21.setSeido8(CommonDtoUtil.strValToInt(serInfo.getSeido8()));
        // 日常生活自立支援事業 */
        cpnTucGdl4SerH21.setSeido9(CommonDtoUtil.strValToInt(serInfo.getSeido9()));
        // 成年後見人制度 */
        cpnTucGdl4SerH21.setSeido10(CommonDtoUtil.strValToInt(serInfo.getSeido10()));
        // 国保 */
        cpnTucGdl4SerH21.setSeido11(CommonDtoUtil.strValToInt(serInfo.getSeido11()));
        // 組合保健 */
        cpnTucGdl4SerH21.setSeido12(CommonDtoUtil.strValToInt(serInfo.getSeido12()));
        // 国公共済 */
        cpnTucGdl4SerH21.setSeido13(CommonDtoUtil.strValToInt(serInfo.getSeido13()));
        // 私立学校共済 */
        cpnTucGdl4SerH21.setSeido14(CommonDtoUtil.strValToInt(serInfo.getSeido14()));
        // 協会けんぼ（旧・政管健保） */
        cpnTucGdl4SerH21.setSeido15(CommonDtoUtil.strValToInt(serInfo.getSeido15()));
        // 日雇い */
        cpnTucGdl4SerH21.setSeido16(CommonDtoUtil.strValToInt(serInfo.getSeido16()));
        // 地方共済 */
        cpnTucGdl4SerH21.setSeido17(CommonDtoUtil.strValToInt(serInfo.getSeido17()));
        // 船員 */
        cpnTucGdl4SerH21.setSeido18(CommonDtoUtil.strValToInt(serInfo.getSeido18()));
        // 労災保険 */
        cpnTucGdl4SerH21.setSeido19(CommonDtoUtil.strValToInt(serInfo.getSeido19()));
        // 制度利用20
        if (serInfo.getSeido20() == null || StringUtil.isEmpty(serInfo.getSeido20())) {
            cpnTucGdl4SerH21.setSeido20(0);
        } else {
            cpnTucGdl4SerH21.setSeido20(CommonDtoUtil.strValToInt(serInfo.getSeido20()));
        }

        if (isInsert) {
            // 制度利用21
            cpnTucGdl4SerH21.setSeido21(0);
            // 制度利用22
            cpnTucGdl4SerH21.setSeido22(0);
            // 制度利用24
            cpnTucGdl4SerH21.setSeido24(0);
            // 制度利用25
            cpnTucGdl4SerH21.setSeido25(0);
            // 制度利用26
            cpnTucGdl4SerH21.setSeido26(0);
            // 制度利用27
            cpnTucGdl4SerH21.setSeido27(0);
            // 制度利用28
            cpnTucGdl4SerH21.setSeido28(0);
            // 制度利用29
            cpnTucGdl4SerH21.setSeido29(0);

        }

        // 制度利用23
        if (serInfo.getSeido23() == null || StringUtil.isEmpty(serInfo.getSeido23())) {
            cpnTucGdl4SerH21.setSeido23(0);
        } else {
            cpnTucGdl4SerH21.setSeido23(CommonDtoUtil.strValToInt(serInfo.getSeido23()));

        }

        // 制度利用ﾒﾓ1
        cpnTucGdl4SerH21.setRiyoMemo1Knj(serInfo.getRiyoMemo1Knj());
        // 制度利用ﾒﾓ2
        cpnTucGdl4SerH21.setRiyoMemo2Knj(serInfo.getRiyoMemo2Knj());
        // 制度利用ﾒﾓ3
        cpnTucGdl4SerH21.setRiyoMemo3Knj(serInfo.getRiyoMemo3Knj());
        // 制度利用ﾒﾓ4
        cpnTucGdl4SerH21.setRiyoMemo4Knj(serInfo.getRiyoMemo4Knj());

        // 後見人等
        cpnTucGdl4SerH21.setKokenKnj(serInfo.getKokenKnj());
        // 成年後見人制度
        cpnTucGdl4SerH21.setSeinenKoukenKbn(CommonDtoUtil.strValToInt(serInfo.getSeinenKoukenKbn()));

        // 夜間対応訪問介護
        cpnTucGdl4SerH21.setSer31Cd(CommonDtoUtil.strValToInt(serInfo.getSer31Cd()));
        // 認知症対応型通所介護
        cpnTucGdl4SerH21.setSer32Cd(CommonDtoUtil.strValToInt(serInfo.getSer32Cd()));
        // 小規模多機能居宅介護
        cpnTucGdl4SerH21.setSer33Cd(CommonDtoUtil.strValToInt(serInfo.getSer33Cd()));
        // 地域密着型特定施設入居者生活介護
        cpnTucGdl4SerH21.setSer34Cd(CommonDtoUtil.strValToInt(serInfo.getSer34Cd()));
        // 地域密着型介護老人福祉施設入居者生活介護
        cpnTucGdl4SerH21.setSer35Cd(CommonDtoUtil.strValToInt(serInfo.getSer35Cd()));

        // 夜間対応訪問介護（回数）
        cpnTucGdl4SerH21.setKaisu31(CommonDtoUtil.strValToInt(serInfo.getKaisu31()));
        // 認知症対応型通所介護（回数）
        cpnTucGdl4SerH21.setKaisu32(CommonDtoUtil.strValToInt(serInfo.getKaisu32()));
        // 小規模多機能居宅介護（回数）
        cpnTucGdl4SerH21.setKaisu33(CommonDtoUtil.strValToInt(serInfo.getKaisu33()));
        // 地域密着型特定施設入居者生活介護（回数）
        cpnTucGdl4SerH21.setKaisu34(CommonDtoUtil.strValToInt(serInfo.getKaisu34()));
        // 地域密着型介護老人福祉施設入居者生活介護（回数）
        cpnTucGdl4SerH21.setKaisu35(CommonDtoUtil.strValToInt(serInfo.getKaisu35()));

        // サービス利用状況記載日
        cpnTucGdl4SerH21.setSerYmd(serInfo.getSerYmd());

        // 複合型サービス
        if (serInfo.getSer36Cd() == null || StringUtil.isEmpty(serInfo.getSer36Cd())) {
            cpnTucGdl4SerH21.setSer36Cd(0);
        } else {
            cpnTucGdl4SerH21.setSer36Cd(CommonDtoUtil.strValToInt(serInfo.getSer36Cd()));

        }
        // 定期巡回・随時対応型訪問介護看護
        if (serInfo.getSer37Cd() == null || StringUtil.isEmpty(serInfo.getSer37Cd())) {
            cpnTucGdl4SerH21.setSer37Cd(0);
        } else {
            cpnTucGdl4SerH21.setSer37Cd(CommonDtoUtil.strValToInt(serInfo.getSer37Cd()));

        }

        // 複合型サービス（回数）
        cpnTucGdl4SerH21.setKaisu36(CommonDtoUtil.strValToInt(serInfo.getKaisu36()));
        // 定期巡回・随時対応型訪問介護看護（回数）
        cpnTucGdl4SerH21.setKaisu37(CommonDtoUtil.strValToInt(serInfo.getKaisu37()));

        // 制度利用30（後期高齢者医療）
        if (serInfo.getSeido30() == null || StringUtil.isEmpty(serInfo.getSeido30())) {
            cpnTucGdl4SerH21.setSeido30(0);
        } else {
            cpnTucGdl4SerH21.setSeido30(CommonDtoUtil.strValToInt(serInfo.getSeido30()));

        }
        // 制度利用31（その他1）
        if (serInfo.getSeido31() == null || StringUtil.isEmpty(serInfo.getSeido31())) {
            cpnTucGdl4SerH21.setSeido31(0);
        } else {
            cpnTucGdl4SerH21.setSeido31(CommonDtoUtil.strValToInt(serInfo.getSeido31()));

        }
        // 制度利用32（その他2）
        if (serInfo.getSeido32() == null || StringUtil.isEmpty(serInfo.getSeido32())) {
            cpnTucGdl4SerH21.setSeido32(0);
        } else {
            cpnTucGdl4SerH21.setSeido32(CommonDtoUtil.strValToInt(serInfo.getSeido32()));

        }
        // 制度利用33（その他3）
        if (serInfo.getSeido33() == null || StringUtil.isEmpty(serInfo.getSeido33())) {
            cpnTucGdl4SerH21.setSeido33(0);
        } else {
            cpnTucGdl4SerH21.setSeido33(CommonDtoUtil.strValToInt(serInfo.getSeido33()));

        }

        // 制度利用ﾒﾓ5（その他1（メモ））
        cpnTucGdl4SerH21.setRiyoMemo5Knj(serInfo.getRiyoMemo5Knj());
        // 制度利用ﾒﾓ6（その他2（メモ））
        cpnTucGdl4SerH21.setRiyoMemo6Knj(serInfo.getRiyoMemo6Knj());
        // 制度利用ﾒﾓ7（その他3（メモ））
        cpnTucGdl4SerH21.setRiyoMemo7Knj(serInfo.getRiyoMemo7Knj());

        return cpnTucGdl4SerH21;

    }

    /**
     * ＧＬ＿居宅アセスメンＧＬ＿サービス利用状況（Ｈ２１改訂）パラメータを作成
     * 
     * @param serInfo  ｻｰﾋﾞｽ利用状況情報
     * @param isInsert 登録/更新
     * @return ＧＬ＿居宅アセスメンＧＬ＿サービス利用状況（Ｈ２１改訂）パラメータ
     */
    private CpnTucGdl5SerR3 setCpnTucGdl5SerR3(Gui00796SerInfo serInfo, Boolean isInsert) {
        CpnTucGdl5SerR3 cpnTucGdl5SerR3 = new CpnTucGdl5SerR3();

        // 在宅サービス利用1
        cpnTucGdl5SerR3.setSer1Cd(CommonDtoUtil.strValToInt(serInfo.getSer1Cd()));
        // 在宅サービス利用2
        cpnTucGdl5SerR3.setSer2Cd(CommonDtoUtil.strValToInt(serInfo.getSer2Cd()));
        // 在宅サービス利用3
        cpnTucGdl5SerR3.setSer3Cd(CommonDtoUtil.strValToInt(serInfo.getSer3Cd()));
        // 在宅サービス利用4
        cpnTucGdl5SerR3.setSer4Cd(CommonDtoUtil.strValToInt(serInfo.getSer4Cd()));
        // 在宅サービス利用5
        cpnTucGdl5SerR3.setSer5Cd(CommonDtoUtil.strValToInt(serInfo.getSer5Cd()));
        // 在宅サービス利用6
        cpnTucGdl5SerR3.setSer6Cd(CommonDtoUtil.strValToInt(serInfo.getSer6Cd()));
        // 在宅サービス利用7
        cpnTucGdl5SerR3.setSer7Cd(CommonDtoUtil.strValToInt(serInfo.getSer7Cd()));
        // 在宅サービス利用8
        cpnTucGdl5SerR3.setSer8Cd(CommonDtoUtil.strValToInt(serInfo.getSer8Cd()));
        // 在宅サービス利用9
        cpnTucGdl5SerR3.setSer9Cd(CommonDtoUtil.strValToInt(serInfo.getSer9Cd()));
        // 在宅サービス利用10
        cpnTucGdl5SerR3.setSer10Cd(CommonDtoUtil.strValToInt(serInfo.getSer10Cd()));
        // 在宅サービス利用11
        cpnTucGdl5SerR3.setSer11Cd(CommonDtoUtil.strValToInt(serInfo.getSer11Cd()));
        // 在宅サービス利用12
        cpnTucGdl5SerR3.setSer12Cd(CommonDtoUtil.strValToInt(serInfo.getSer12Cd()));
        // 在宅サービス利用13
        cpnTucGdl5SerR3.setSer13Cd(CommonDtoUtil.strValToInt(serInfo.getSer13Cd()));
        // 在宅サービス利用14
        cpnTucGdl5SerR3.setSer14Cd(CommonDtoUtil.strValToInt(serInfo.getSer14Cd()));
        // 在宅サービス利用15
        cpnTucGdl5SerR3.setSer15Cd(CommonDtoUtil.strValToInt(serInfo.getSer15Cd()));
        // 在宅サービス利用16
        if (isInsert) {
            cpnTucGdl5SerR3.setSer16Cd(0);
        }
        // 在宅サービス利用17
        cpnTucGdl5SerR3.setSer17Cd(CommonDtoUtil.strValToInt(serInfo.getSer17Cd()));
        // 在宅サービス利用18
        cpnTucGdl5SerR3.setSer18Cd(CommonDtoUtil.strValToInt(serInfo.getSer18Cd()));
        // 在宅サービス利用19
        cpnTucGdl5SerR3.setSer19Cd(CommonDtoUtil.strValToInt(serInfo.getSer19Cd()));
        // 在宅サービス利用20
        cpnTucGdl5SerR3.setSer20Cd(CommonDtoUtil.strValToInt(serInfo.getSer20Cd()));
        // 在宅サービス利用21
        cpnTucGdl5SerR3.setSer21Cd(CommonDtoUtil.strValToInt(serInfo.getSer21Cd()));
        // 在宅サービス利用22
        cpnTucGdl5SerR3.setSer22Cd(CommonDtoUtil.strValToInt(serInfo.getSer22Cd()));
        // 在宅サービス利用23
        cpnTucGdl5SerR3.setSer23Cd(CommonDtoUtil.strValToInt(serInfo.getSer23Cd()));
        // 在宅サービス利用24
        cpnTucGdl5SerR3.setSer24Cd(CommonDtoUtil.strValToInt(serInfo.getSer24Cd()));
        // 在宅サービス利用25
        cpnTucGdl5SerR3.setSer25Cd(CommonDtoUtil.strValToInt(serInfo.getSer25Cd()));
        // 在宅サービス利用26
        cpnTucGdl5SerR3.setSer26Cd(CommonDtoUtil.strValToInt(serInfo.getSer26Cd()));
        // 在宅サービス利用27
        cpnTucGdl5SerR3.setSer27Cd(CommonDtoUtil.strValToInt(serInfo.getSer27Cd()));
        // 在宅サービス利用28
        cpnTucGdl5SerR3.setSer28Cd(CommonDtoUtil.strValToInt(serInfo.getSer28Cd()));

        if (isInsert) {
            // 在宅サービス利用29
            cpnTucGdl5SerR3.setSer29Cd(0);
            // 在宅サービス利用30
            cpnTucGdl5SerR3.setSer30Cd(0);
        }

        // 夜間対応訪問介護
        cpnTucGdl5SerR3.setSer31Cd(CommonDtoUtil.strValToInt(serInfo.getSer31Cd()));
        // 認知症対応型通所介護
        cpnTucGdl5SerR3.setSer32Cd(CommonDtoUtil.strValToInt(serInfo.getSer32Cd()));
        // 小規模多機能居宅介護
        cpnTucGdl5SerR3.setSer33Cd(CommonDtoUtil.strValToInt(serInfo.getSer33Cd()));
        // 複合型サービス
        cpnTucGdl5SerR3.setSer36Cd(CommonDtoUtil.strValToInt(serInfo.getSer36Cd()));
        // 定期巡回・随時対応型訪問介護看護
        cpnTucGdl5SerR3.setSer37Cd(CommonDtoUtil.strValToInt(serInfo.getSer37Cd()));

        // 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ)の利用回数 */
        cpnTucGdl5SerR3.setKaisu1(CommonDtoUtil.strValToInt(serInfo.getKaisu1()));
        // (介護予防)訪問入浴介護の利用回数 */
        cpnTucGdl5SerR3.setKaisu2(CommonDtoUtil.strValToInt(serInfo.getKaisu2()));
        // (介護予防)訪問看護の利用回数 */
        cpnTucGdl5SerR3.setKaisu3(CommonDtoUtil.strValToInt(serInfo.getKaisu3()));
        // (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝの利用回数 */
        cpnTucGdl5SerR3.setKaisu4(CommonDtoUtil.strValToInt(serInfo.getKaisu4()));
        // (介護予防)居宅療養管理指導の利用回数 */
        cpnTucGdl5SerR3.setKaisu5(CommonDtoUtil.strValToInt(serInfo.getKaisu5()));
        // 通所介護（ﾃﾞｲｻｰﾋﾞｽ）の利用回数 */
        cpnTucGdl5SerR3.setKaisu6(CommonDtoUtil.strValToInt(serInfo.getKaisu6()));
        // (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ）の利用回数 */
        cpnTucGdl5SerR3.setKaisu7(CommonDtoUtil.strValToInt(serInfo.getKaisu7()));
        // (介護予防)福祉用具貸与の利用回数 */
        cpnTucGdl5SerR3.setKaisu8(CommonDtoUtil.strValToInt(serInfo.getKaisu8()));
        // (介護予防)短期入所生活介護(特養等）の利用回数 */
        cpnTucGdl5SerR3.setKaisu9(CommonDtoUtil.strValToInt(serInfo.getKaisu9()));
        // (介護予防)短期入所生活介護(老健・診療所）の利用回数 */
        cpnTucGdl5SerR3.setKaisu10(CommonDtoUtil.strValToInt(serInfo.getKaisu10()));
        // 認知症対応型共同生活介護（回数） */
        cpnTucGdl5SerR3.setKaisu11(CommonDtoUtil.strValToInt(serInfo.getKaisu11()));
        // (介護予防)特定施設入所生活介護の利用回数 */
        cpnTucGdl5SerR3.setKaisu12(CommonDtoUtil.strValToInt(serInfo.getKaisu12()));
        // 特定(介護予防)福祉用具販売の利用回数 */
        cpnTucGdl5SerR3.setKaisu13(CommonDtoUtil.strValToInt(serInfo.getKaisu13()));
        // 住宅改修の利用回数 */
        cpnTucGdl5SerR3.setKaisu15(CommonDtoUtil.strValToInt(serInfo.getKaisu15()));
        // 生活支援員の訪問の利用回数 */
        cpnTucGdl5SerR3.setKaisu16(CommonDtoUtil.strValToInt(serInfo.getKaisu16()));
        // ふれあい・いきいきサロンの利用回数 */
        cpnTucGdl5SerR3.setKaisu17(CommonDtoUtil.strValToInt(serInfo.getKaisu17()));
        // 配食サービスの利用回数 */
        cpnTucGdl5SerR3.setKaisu18(CommonDtoUtil.strValToInt(serInfo.getKaisu18()));
        // 洗濯サービスの利用回数 */
        cpnTucGdl5SerR3.setKaisu19(CommonDtoUtil.strValToInt(serInfo.getKaisu19()));
        // 移動または外出支援の利用回数 */
        cpnTucGdl5SerR3.setKaisu20(CommonDtoUtil.strValToInt(serInfo.getKaisu20()));
        // 友愛訪問の利用回数 */
        cpnTucGdl5SerR3.setKaisu21(CommonDtoUtil.strValToInt(serInfo.getKaisu21()));
        // 老人福祉センターの利用回数 */
        cpnTucGdl5SerR3.setKaisu22(CommonDtoUtil.strValToInt(serInfo.getKaisu22()));
        // 老人憩いの家の利用回数 */
        cpnTucGdl5SerR3.setKaisu23(CommonDtoUtil.strValToInt(serInfo.getKaisu23()));
        // ガイドヘルパーの利用回数 */
        cpnTucGdl5SerR3.setKaisu24(CommonDtoUtil.strValToInt(serInfo.getKaisu24()));
        // 追加サービス1の利用回数 */
        cpnTucGdl5SerR3.setKaisu25(CommonDtoUtil.strValToInt(serInfo.getKaisu25()));
        // 追加サービス2の利用回数 */
        cpnTucGdl5SerR3.setKaisu26(CommonDtoUtil.strValToInt(serInfo.getKaisu26()));
        // 夜間対応訪問介護（回数）
        cpnTucGdl5SerR3.setKaisu31(CommonDtoUtil.strValToInt(serInfo.getKaisu31()));
        // 認知症対応型通所介護（回数）
        cpnTucGdl5SerR3.setKaisu32(CommonDtoUtil.strValToInt(serInfo.getKaisu32()));
        // 小規模多機能居宅介護（回数）
        cpnTucGdl5SerR3.setKaisu33(CommonDtoUtil.strValToInt(serInfo.getKaisu33()));
        // 複合型サービス（回数）
        cpnTucGdl5SerR3.setKaisu36(CommonDtoUtil.strValToInt(serInfo.getKaisu36()));
        // 定期巡回・随時対応型訪問介護看護（回数）
        cpnTucGdl5SerR3.setKaisu37(CommonDtoUtil.strValToInt(serInfo.getKaisu37()));

        // 市町村特別給付メモ */
        cpnTucGdl5SerR3.setTokKyuKnj(serInfo.getTokKyuKnj());
        // 追加サービス1メモ */
        cpnTucGdl5SerR3.setAddSer1Knj(serInfo.getAddSer1Knj());
        // 追加サービス2メモ */
        cpnTucGdl5SerR3.setAddSer2Knj(serInfo.getAddSer2Knj());

        // 身障／補助具・日常生活用具メモ */
        cpnTucGdl5SerR3.setYouguKnj(serInfo.getYouguKnj());
        // 施設名 */
        cpnTucGdl5SerR3.setShisetsuNameKnj(serInfo.getShisetsuNameKnj());
        // 郵便番号 */
        cpnTucGdl5SerR3.setShisetsuZip(serInfo.getShisetsuZip());
        // 電話番号 */
        cpnTucGdl5SerR3.setShisetsuTel(serInfo.getShisetsuTel());
        // 住所（以前：備考） */
        cpnTucGdl5SerR3.setShisetsuMemoKnj(serInfo.getShisetsuMemoKnj());
        // 利用施設種別 */
        cpnTucGdl5SerR3.setShisetsuShu(CommonDtoUtil.strValToInt(serInfo.getShisetsuShu()));

        // 老齢関係 */
        cpnTucGdl5SerR3.setSeido1(CommonDtoUtil.strValToInt(serInfo.getSeido1()));
        // 障害関係 */
        cpnTucGdl5SerR3.setSeido2(CommonDtoUtil.strValToInt(serInfo.getSeido2()));
        // 遺族・寡婦 */
        cpnTucGdl5SerR3.setSeido3(CommonDtoUtil.strValToInt(serInfo.getSeido3()));
        // 恩給 */
        cpnTucGdl5SerR3.setSeido4(CommonDtoUtil.strValToInt(serInfo.getSeido4()));
        // 特別障害者手当 */
        cpnTucGdl5SerR3.setSeido5(CommonDtoUtil.strValToInt(serInfo.getSeido5()));
        // 生活保護 */
        cpnTucGdl5SerR3.setSeido6(CommonDtoUtil.strValToInt(serInfo.getSeido6()));
        // 生活福祉資金貸付 */
        cpnTucGdl5SerR3.setSeido7(CommonDtoUtil.strValToInt(serInfo.getSeido7()));
        // 高齢者住宅整備資金貸付 */
        cpnTucGdl5SerR3.setSeido8(CommonDtoUtil.strValToInt(serInfo.getSeido8()));
        // 日常生活自立支援事業 */
        cpnTucGdl5SerR3.setSeido9(CommonDtoUtil.strValToInt(serInfo.getSeido9()));
        // 成年後見人制度 */
        cpnTucGdl5SerR3.setSeido10(CommonDtoUtil.strValToInt(serInfo.getSeido10()));
        // 国保 */
        cpnTucGdl5SerR3.setSeido11(CommonDtoUtil.strValToInt(serInfo.getSeido11()));
        // 組合保健 */
        cpnTucGdl5SerR3.setSeido12(CommonDtoUtil.strValToInt(serInfo.getSeido12()));
        // 国公共済 */
        cpnTucGdl5SerR3.setSeido13(CommonDtoUtil.strValToInt(serInfo.getSeido13()));
        // 私立学校共済 */
        cpnTucGdl5SerR3.setSeido14(CommonDtoUtil.strValToInt(serInfo.getSeido14()));
        // 協会けんぼ（旧・政管健保） */
        cpnTucGdl5SerR3.setSeido15(CommonDtoUtil.strValToInt(serInfo.getSeido15()));
        // 日雇い */
        cpnTucGdl5SerR3.setSeido16(CommonDtoUtil.strValToInt(serInfo.getSeido16()));
        // 地方共済 */
        cpnTucGdl5SerR3.setSeido17(CommonDtoUtil.strValToInt(serInfo.getSeido17()));
        // 船員 */
        cpnTucGdl5SerR3.setSeido18(CommonDtoUtil.strValToInt(serInfo.getSeido18()));
        // 労災保険 */
        cpnTucGdl5SerR3.setSeido19(CommonDtoUtil.strValToInt(serInfo.getSeido19()));

        if (isInsert) {
            // 制度利用20
            cpnTucGdl5SerR3.setSeido20(0);
            // 制度利用21
            cpnTucGdl5SerR3.setSeido21(0);
            // 制度利用22
            cpnTucGdl5SerR3.setSeido22(0);
            // 制度利用23
            cpnTucGdl5SerR3.setSeido23(0);
            // 制度利用24
            cpnTucGdl5SerR3.setSeido24(0);
            // 制度利用25
            cpnTucGdl5SerR3.setSeido25(0);
            // 制度利用26
            cpnTucGdl5SerR3.setSeido26(0);
            // 制度利用27
            cpnTucGdl5SerR3.setSeido27(0);
            // 制度利用28
            cpnTucGdl5SerR3.setSeido28(0);
            // 制度利用29
            cpnTucGdl5SerR3.setSeido29(0);
        }

        // 制度利用30（後期高齢者医療）
        cpnTucGdl5SerR3.setSeido30(CommonDtoUtil.strValToInt(serInfo.getSeido30()));
        // 制度利用31（その他1）
        cpnTucGdl5SerR3.setSeido31(CommonDtoUtil.strValToInt(serInfo.getSeido31()));
        // 制度利用32（その他2）
        cpnTucGdl5SerR3.setSeido32(CommonDtoUtil.strValToInt(serInfo.getSeido32()));
        // 制度利用33（その他3）
        cpnTucGdl5SerR3.setSeido33(CommonDtoUtil.strValToInt(serInfo.getSeido33()));

        // 制度利用ﾒﾓ1
        cpnTucGdl5SerR3.setRiyoMemo1Knj(serInfo.getRiyoMemo1Knj());
        // 制度利用ﾒﾓ2
        cpnTucGdl5SerR3.setRiyoMemo2Knj(serInfo.getRiyoMemo2Knj());
        // 制度利用ﾒﾓ3
        cpnTucGdl5SerR3.setRiyoMemo3Knj(serInfo.getRiyoMemo3Knj());
        // 制度利用ﾒﾓ4
        cpnTucGdl5SerR3.setRiyoMemo4Knj(serInfo.getRiyoMemo4Knj());
        // 制度利用ﾒﾓ5（その他1（メモ））
        cpnTucGdl5SerR3.setRiyoMemo5Knj(serInfo.getRiyoMemo5Knj());
        // 制度利用ﾒﾓ6（その他2（メモ））
        cpnTucGdl5SerR3.setRiyoMemo6Knj(serInfo.getRiyoMemo6Knj());
        // 制度利用ﾒﾓ7（その他3（メモ））
        cpnTucGdl5SerR3.setRiyoMemo7Knj(serInfo.getRiyoMemo7Knj());

        // 後見人等
        cpnTucGdl5SerR3.setKokenKnj(serInfo.getKokenKnj());
        // 成年後見人制度
        cpnTucGdl5SerR3.setSeinenKoukenKbn(CommonDtoUtil.strValToInt(serInfo.getSeinenKoukenKbn()));

        // サービス利用状況記載日
        cpnTucGdl5SerR3.setSerYmd(serInfo.getSerYmd());

        // (介護予防)訪問型サービス
        cpnTucGdl5SerR3.setSer38Cd(CommonDtoUtil.strValToInt(serInfo.getSer38Cd()));
        // (介護予防)通所型サービス
        cpnTucGdl5SerR3.setSer39Cd(CommonDtoUtil.strValToInt(serInfo.getSer39Cd()));
        // その他の生活支援サービス
        cpnTucGdl5SerR3.setSer40Cd(CommonDtoUtil.strValToInt(serInfo.getSer40Cd()));
        // (介護予防)訪問型サービスの利用回数
        cpnTucGdl5SerR3.setKaisu38(CommonDtoUtil.strValToInt(serInfo.getKaisu38()));
        // (介護予防)通所型サービスの利用回数
        cpnTucGdl5SerR3.setKaisu39(CommonDtoUtil.strValToInt(serInfo.getKaisu39()));
        // その他の生活支援サービス（回数）
        cpnTucGdl5SerR3.setKaisu40(CommonDtoUtil.strValToInt(serInfo.getKaisu40()));
        // その他の生活支援サービス（名称）
        cpnTucGdl5SerR3.setSer40NameKnj(serInfo.getSer40NameKnj());

        return cpnTucGdl5SerR3;

    }

}