package jp.ndsoft.carebase.cmn.api.service.dto;

import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * @since 2025.04.24
 * <AUTHOR> Bui The Khang
 * @implNote GUI01023_課題整理総括取込画面
 */
@Getter
@Setter
public class Gui01023IssueContentSelectOutDto extends IDtoImpl {

    /** serialVersionUID */
    private static final long serialVersionUID = 1L;
    // 連番
    private String kss3Id;

    // ヘッダID
    private String kss1Id;

    // 見通し
    private String mitosiKnj;

    // 生活全般の解決すべき課題
    private String kadaiKnj;

    // 優先順位
    private String yusenNo;

    // 表示順
    private String sort;

    // 更新回数
    // private String modifiedCnt;

}
