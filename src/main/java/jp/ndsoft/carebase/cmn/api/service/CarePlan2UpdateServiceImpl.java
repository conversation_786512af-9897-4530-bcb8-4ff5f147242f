package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.*;
import jp.ndsoft.carebase.cmn.api.service.dto.CarePlan2UpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.CarePlan2UpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.*;
import jp.ndsoft.carebase.common.dao.mybatis.entity.*;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.InsertServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.invoke.MethodHandles;

/**
 * GUI01014_計画書（2）情報保存
 *
 * <AUTHOR> 李晨昊
 */
@Service
public class CarePlan2UpdateServiceImpl
        extends InsertServiceImpl<CarePlan2UpdateServiceInDto, CarePlan2UpdateServiceOutDto> {
    /**
     * ロガー
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    /**
     * 28-20 計画書（2）ヘッダ
     */
    @Autowired
    private CpnTucCks21Mapper cpnTucCks21Mapper;
    /**
     * 28-20 計画書（2）サービス
     */
    @Autowired
    private CpnTucCks211Mapper cpnTucCks211Mapper;
    /**
     * 28-66 計画書（2）（月日指定）
     */
    @Autowired
    private CpnTucCks212Mapper cpnTucCks212Mapper;
    /**
     * 28-22 計画書（2）サービス、曜日
     */
    @Autowired
    private CpnTucCks221Mapper cpnTucCks221Mapper;
    /**
     * 28-21 計画書（2）データ
     */
    @Autowired
    private CpnTucCks22Mapper cpnTucCks22Mapper;
    /**
     * 28-23 計画書（2）担当者
     */
    @Autowired
    private CpnTucCks222Mapper cpnTucCks222Mapper;
    /**
     * 計画書（2）連動項目
     */
    @Autowired
    private CpnTucCks224Mapper cpnTucCks224Mapper;

    /**
     * 計画書（2）情報保存
     *
     * @param inDto 計画書（2）情報保存の入力DTO.
     * @return 計画書（2）情報保存のOUT DTO
     * @throws Exception Exception
     */
    @Override
    protected CarePlan2UpdateServiceOutDto mainProcess(CarePlan2UpdateServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        // 3.履歴情報保存
        boolean doFourProc = hisInfoSaveProc(inDto);

        if (doFourProc) {
            // 4.リクエストパラメータ.計画書(2)リストより繰り返し、計画書（２）情報を保存する。
            for (Gui01014Keikasyo2Upd kasyo2Info : inDto.getKeikasyo2List()) {
                // 計画書（２）情報を保存する。
                editKerkasyo2Info(inDto, kasyo2Info);
            }

            // 5.リクエストパラメータ.計画書様式が居宅の場合、居宅の計画書（２）情報を保存する。
            cksHomeSaveProc(inDto);
        }

        LOG.info(Constants.END);
        return new CarePlan2UpdateServiceOutDto();
    }

    /**
     * 3.履歴情報保存
     *
     * @param inDto 入力Dto
     * @throws Exception Exception
     */
    private boolean hisInfoSaveProc(CarePlan2UpdateServiceInDto inDto) throws Exception {
        // 【変数】.計画対象期間ID
        String chgSc1Id = StringUtils.EMPTY;
        // 3.1. 【変数】.計画対象期間IDの編集
        if (inDto.getSc1Id() == null) {
            // 3.1.1. リクエストパラメータ.計画対象期間IDがNULLの場合、【変数】.計画対象期間IDが上記2.1.で登録された期間IDで設定する。
            chgSc1Id = StringUtils.EMPTY;
        } else {
            // 3.1.2. リクエストパラメータ.計画対象期間IDがNULLではない場合、【変数】.計画対象期間IDがリクエストパラメータ.計画対象期間IDで設定する。
            chgSc1Id = inDto.getSc1Id();
        }
        // 3.2. リクエストパラメータ.履歴更新区分が新規の場合、28-20 計画書（２）ヘッダ：新／旧共用を新規登録する。
        if (CommonConstants.UPDATE_KBN_C.equals(inDto.getHistoryUpdateKbn()) && StringUtils.isNotEmpty(chgSc1Id)) {
            cks21RgtrProc(inDto, chgSc1Id);
        } // 3.3. リクエストパラメータ.履歴更新区分が更新の場合、28-20 計画書（２）ヘッダ：新／旧共用を更新する。
        else if (CommonConstants.UPDATE_KBN_U.equals(inDto.getHistoryUpdateKbn()) && StringUtils.isNotEmpty(chgSc1Id)) {
            cks21UpdProc(inDto, chgSc1Id);
        } // 3.4. リクエストパラメータ.履歴更新区分が削除の場合
        else if (CommonConstants.UPDATE_KBN_D.equals(inDto.getHistoryUpdateKbn())) {
            // 3.4.1. 28-20 計画書（２）ヘッダ：新／旧共用を削除する。
            cks21DelProc(inDto);

            // リクエストパラメータ.計画書様式が居宅の場合
            if (CommonConstants.CKS_FLG_HOME.equals(inDto.getCksflg())) {
                // 3.4.2. 、28-20 計画書（２）サービスを削除する。
                // 3.4.3. リクエストパラメータ.計画書様式が居宅の場合、28-66 計画書（２）（月日指定）を削除する。
                cks211DelProc(inDto);
            }
            // リクエストパラメータ.計画書様式が施設の場合
            else if (CommonConstants.CKS_FLG_FACILITY.equals(inDto.getCksflg())) {
                // 3.4.4. リクエストパラメータ.計画書様式が施設の場合、28-22 計画書（２）サービス、曜日を削除する。
                // 3.4.5. リクエストパラメータ.計画書様式が施設の場合、28-23 計画書（２）担当者（施設様式で使用）を削除する。
                cks221DelProc(inDto);
            }
            // 3.4.6. 処理5へ進む。
            return false;
        }
        return true;
    }

    /**
     * 3.2. リクエストパラメータ.履歴更新区分が新規の場合、28-20 計画書（２）ヘッダ：新／旧共用を新規登録する。
     *
     * @param inDto    入力Dto
     * @param chgSc1Id 【変数】.計画対象期間ID
     * @throws Exception Exception
     */
    private void cks21RgtrProc(CarePlan2UpdateServiceInDto inDto, String chgSc1Id) throws Exception {
        CpnTucCks21 insInfo = new CpnTucCks21();
        // 計画期間ID 【変数】.計画対象期間ID
        insInfo.setSc1Id(CommonDtoUtil.strValToInt(chgSc1Id));
        // 法人ID リクエストパラメータ.法人ID
        insInfo.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID リクエストパラメータ.施設ID
        insInfo.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // サービス事業者ID リクエストパラメータ.事業者ID
        insInfo.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID リクエストパラメータ.利用者ID
        insInfo.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 作成日 リクエストパラメータ.作成日
        insInfo.setCreateYmd(inDto.getCreateYmd());
        // 作成者 リクエストパラメータ.作成者
        insInfo.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
        // 有効期間ID リクエストパラメータ.有効期間ID
        insInfo.setTermid(CommonDtoUtil.strValToInt(inDto.getTermid()));
        cpnTucCks21Mapper.insertSelectiveAndReturn(insInfo);
        inDto.getKeikasyo2List().forEach(x -> x.setKs21Id(CommonDtoUtil.objValToString(insInfo.getKs21Id())));
    }

    /**
     * 3.3. リクエストパラメータ.履歴更新区分が更新の場合、28-20 計画書（２）ヘッダ：新／旧共用を更新する。
     *
     * @param inDto    入力Dto
     * @param chgSc1Id 【変数】.計画対象期間ID
     * @throws ExclusiveException
     */
    private void cks21UpdProc(CarePlan2UpdateServiceInDto inDto, String chgSc1Id)
            throws ExclusiveException {
        CpnTucCks21 updInfo = new CpnTucCks21();
        // 計画期間ID 【変数】.計画対象期間ID
        updInfo.setSc1Id(CommonDtoUtil.strValToInt(chgSc1Id));
        // 作成日 リクエストパラメータ.作成日
        updInfo.setCreateYmd(inDto.getCreateYmd());
        // 作成者 リクエストパラメータ.作成者
        updInfo.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
        // 有効期間ID リクエストパラメータ.有効期間ID
        updInfo.setTermid(CommonDtoUtil.strValToInt(inDto.getTermid()));

        // 更新条件キー
        CpnTucCks21Criteria whereInfo = new CpnTucCks21Criteria();
        whereInfo.createCriteria().andKs21IdEqualTo(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
        Integer updNum = cpnTucCks21Mapper.updateByCriteriaSelective(updInfo, whereInfo);
        if (updNum <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 3.4.1. 28-20 計画書（２）ヘッダ：新／旧共用を削除する。
     *
     * @param inDto 入力Dto
     * @throws ExclusiveException
     */
    private void cks21DelProc(CarePlan2UpdateServiceInDto inDto) throws ExclusiveException {
        // 更新条件キー
        CpnTucCks21Criteria whereInfo = new CpnTucCks21Criteria();
        whereInfo.createCriteria().andKs21IdEqualTo(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
        Integer updNum = cpnTucCks21Mapper.deleteByCriteria(whereInfo);
        if (updNum <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 3.4.2. リクエストパラメータ.計画書様式が居宅の場合、28-20 計画書（２）サービスを削除する。 3.4.3.
     * リクエストパラメータ.計画書様式が居宅の場合、28-66 計画書（２）（月日指定）を削除する。
     *
     * @param inDto 入力Dto
     * @throws ExclusiveException
     */
    private void cks211DelProc(CarePlan2UpdateServiceInDto inDto) throws ExclusiveException {
        CpnTucCks211Criteria where211 = new CpnTucCks211Criteria();
        where211.createCriteria().andKs21IdEqualTo(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
        Integer upd211Num = cpnTucCks211Mapper.deleteByCriteria(where211);
        if (upd211Num <= 0) {
            throw new ExclusiveException();
        }

        CpnTucCks212Criteria where212 = new CpnTucCks212Criteria();
        where212.createCriteria().andKs21IdEqualTo(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
        Integer upd212Num = cpnTucCks212Mapper.deleteByCriteria(where212);
        if (upd212Num <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 3.4.4. リクエストパラメータ.計画書様式が施設の場合、28-22 計画書（２）サービス、曜日を削除する。 3.4.5.
     * リクエストパラメータ.計画書様式が施設の場合、28-23 計画書（２）担当者（施設様式で使用）を削除する。
     *
     * @param inDto 入力Dto
     */
    private void cks221DelProc(CarePlan2UpdateServiceInDto inDto) {
        CpnTucCks221Criteria where221 = new CpnTucCks221Criteria();
        where221.createCriteria().andKs21IdEqualTo(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
        cpnTucCks221Mapper.deleteByCriteria(where221);
        // データなしの可能性があるため、ExclusiveExceptionチェックが不要

        CpnTucCks222Criteria where222 = new CpnTucCks222Criteria();
        where222.createCriteria().andKs21IdEqualTo(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
        cpnTucCks222Mapper.deleteByCriteria(where222);
        // データなしの可能性があるため、ExclusiveExceptionチェックが不要
    }

    /**
     * 計画書（２）情報を保存する。
     *
     * @param inDto      入力Dto
     * @param kasyo2Info 計画書(2)データー
     * @throws ExclusiveException
     */
    private void editKerkasyo2Info(CarePlan2UpdateServiceInDto inDto, Gui01014Keikasyo2Upd kasyo2Info)
            throws Exception {
        // 4.1. リクエストパラメータ.計画書(2)リスト.更新区分が新規の場合、28-21 計画書（２）データ：新／旧共用を新規登録する。
        if (CommonConstants.UPDATE_KBN_C.equals(kasyo2Info.getUpdateKbn())) {
            cks22RgtrProc(kasyo2Info);

            // 4.1.2. リクエストパラメータ.記録との連携が「1」:記録との連携を行うの場合、計画書（2）連動項目を新規登録／更新する
            if (CommonConstants.STR_1.equals(inDto.getKirokuRenkeiFlg())) {
                CpnTucCks224 cpnTucCks224 = new CpnTucCks224();
                cpnTucCks224.setKs21Id(CommonDtoUtil.strValToInt(kasyo2Info.getKs21Id()));
                cpnTucCks224.setKs22Id(CommonDtoUtil.strValToInt(kasyo2Info.getKs22Id()));
                cpnTucCks224.setNikkaId(CommonDtoUtil.strValToInt(kasyo2Info.getNikkaId()));
                cpnTucCks224Mapper.insertSelective(cpnTucCks224);
            }
        } // 4.2. リクエストパラメータ.計画書(2)リスト.更新区分が更新の場合、28-21 計画書（２）データ：新／旧共用を更新する。
        else if (CommonConstants.UPDATE_KBN_U.equals(kasyo2Info.getUpdateKbn())) {
            cks22UpdProc(kasyo2Info);

            // 4.2.1. リクエストパラメータ.記録との連携が「1」:記録との連携を行うの場合、計画書（2）連動項目を新規登録／更新する。
            if (CommonConstants.STR_1.equals(inDto.getKirokuRenkeiFlg())) {
                CpnTucCks224 cpnTucCks224 = new CpnTucCks224();
                cpnTucCks224.setNikkaId(CommonDtoUtil.strValToInt(kasyo2Info.getNikkaId()));

                CpnTucCks224Criteria cpnTucCks224Criteria = new CpnTucCks224Criteria();
                cpnTucCks224Criteria.createCriteria()
                        .andKs21IdEqualTo(CommonDtoUtil.strValToInt(kasyo2Info.getKs21Id()))
                        .andKs22IdEqualTo(CommonDtoUtil.strValToInt(kasyo2Info.getKs22Id()));
                int updCnt = cpnTucCks224Mapper.updateByCriteriaSelective(cpnTucCks224, cpnTucCks224Criteria);
                if (updCnt <= CommonConstants.INT_0) {
                    cpnTucCks224.setKs21Id(CommonDtoUtil.strValToInt(kasyo2Info.getKs21Id()));
                    cpnTucCks224.setKs22Id(CommonDtoUtil.strValToInt(kasyo2Info.getKs22Id()));
                    cpnTucCks224Mapper.insertSelective(cpnTucCks224);
                }
            }
        } // 4.3. リクエストパラメータ.計画書(2)リスト.更新区分が削除の場合、28-21 計画書（２）データ：新／旧共用を削除する。
        else if (CommonConstants.UPDATE_KBN_D.equals(kasyo2Info.getUpdateKbn())) {
            cks22DelProc(kasyo2Info);

            // 4.3.1. リクエストパラメータ.記録との連携が「1」:記録との連携を行うの場合、計画書（2）連動項目を物理削除する
            if (CommonConstants.STR_1.equals(inDto.getKirokuRenkeiFlg())) {
                CpnTucCks224Criteria cpnTucCks224Criteria = new CpnTucCks224Criteria();
                cpnTucCks224Criteria.createCriteria()
                        .andKs21IdEqualTo(CommonDtoUtil.strValToInt(kasyo2Info.getKs21Id()))
                        .andKs22IdEqualTo(CommonDtoUtil.strValToInt(kasyo2Info.getKs22Id()));
                cpnTucCks224Mapper.deleteByCriteria(cpnTucCks224Criteria);
            }
        }

        // リクエストパラメータ.計画書様式が施設の場合
        if (CommonConstants.CKS_FLG_FACILITY.equals(inDto.getCksflg())) {
            // 4.4. リクエストパラメータ.計画書様式が施設の場合
            // 4.4.1.28-22 計画書（２）サービス、曜日を削除する。
            if (CollectionUtils.isNotEmpty(kasyo2Info.getYobiList())) {
                // ■更新条件
                CpnTucCks221Criteria cks221WhereInfo = new CpnTucCks221Criteria();
                cks221WhereInfo.createCriteria()
                        // 計画書（2）行データID＝リクエストパラメータ.計画書（２）リスト.カウンター
                        .andKs22IdEqualTo(CommonDtoUtil.strValToInt(kasyo2Info.getKs22Id()))
                        // 計画書（２）ID＝リクエストパラメータ.計画書（２）ID
                        .andKs21IdEqualTo(CommonDtoUtil.strValToInt(kasyo2Info.getKs21Id()));
                Integer cks221DelCnt = cpnTucCks221Mapper.deleteByCriteria(cks221WhereInfo);
                if (cks221DelCnt <= 0) {
                    throw new ExclusiveException();
                }
            }

            // 4.4.2.リクエストパラメータ.計画書(2)リスト.更新区分が削除以外の場合、サービス曜日リストより繰り返す、28-22
            // 計画書（２）サービス、曜日を登録する。
            if (!CommonConstants.UPDATE_KBN_D.equals(kasyo2Info.getUpdateKbn())) {
                for (Gui01014YobiUpd yobiInfo : kasyo2Info.getYobiList()) {
                    CpnTucCks221 cks221RgtrInfo = new CpnTucCks221();
                    // ks22_id 計画書（2）行データID リクエストパラメータ.計画書（２）リスト.計画書（２）ID
                    cks221RgtrInfo.setKs22Id(CommonDtoUtil.strValToInt(kasyo2Info.getKs21Id()));
                    // ks21_id 計画書（2）ID リクエストパラメータ.計画書（２）ID
                    cks221RgtrInfo.setKs21Id(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
                    // youbi 曜日 リクエストパラメータ.保険サービスリスト.曜日
                    cks221RgtrInfo.setYoubi(yobiInfo.getYoubi());
                    // igai_kbn 週単位以外のｻｰﾋﾞｽ区分 リクエストパラメータ.保険サービスリスト.居宅：開始時間
                    cks221RgtrInfo.setIgaiKbn(CommonDtoUtil.strValToInt(yobiInfo.getKaishiJikan()));
                    // igai_date 週単位以外のｻｰﾋﾞｽ（日付指定） リクエストパラメータ.保険サービスリスト.居宅：終了時間
                    cks221RgtrInfo.setIgaiDate(yobiInfo.getShuuryouJikan());
                    cpnTucCks221Mapper.insertSelective(cks221RgtrInfo);
                }
            }

            // 4.5. リクエストパラメータ.計画書様式が施設の場合
            // 4.5.1. 28-23 計画書（２）担当者（施設様式で使用）を削除する。
            if (CollectionUtils.isNotEmpty(kasyo2Info.getTantoList())) {
                // ■更新条件
                CpnTucCks222Criteria cks222WhereInfo = new CpnTucCks222Criteria();
                cks222WhereInfo.createCriteria()
                        // 計画書（2）行データID＝リクエストパラメータ.計画書（２）リスト.カウンター
                        .andKs22IdEqualTo(CommonDtoUtil.strValToInt(kasyo2Info.getKs22Id()))
                        // 計画書（2）ID＝リクエストパラメータ.計画書（２）ID
                        .andKs21IdEqualTo(CommonDtoUtil.strValToInt(kasyo2Info.getKs21Id()));

                Integer cks222DelCnt = cpnTucCks222Mapper.deleteByCriteria(cks222WhereInfo);
                if (cks222DelCnt <= 0) {
                    throw new ExclusiveException();
                }
            }

            // 4.7.2.リクエストパラメータ.計画書(2)リスト.更新区分が削除以外の場合、担当者リストより繰り返す、28-23
            // 計画書（２）担当者（施設様式で使用）を登録する。
            if (!CommonConstants.UPDATE_KBN_D.equals(kasyo2Info.getUpdateKbn())) {
                for (Gui01014TantoUpd tantoInfo : kasyo2Info.getTantoList()) {
                    CpnTucCks222 cks222RgtrInfo = new CpnTucCks222();
                    // 計画書（2）行データID リクエストパラメータ.計画書（２）リスト.計画書（２）ID
                    cks222RgtrInfo.setKs22Id(CommonDtoUtil.strValToInt(kasyo2Info.getKs21Id()));
                    // 計画書（2）ID リクエストパラメータ.計画書（２）ID
                    cks222RgtrInfo.setKs21Id(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
                    // 施設:職種（担当者） 施設：職種（担当者）
                    cks222RgtrInfo.setShokushuId(CommonDtoUtil.strValToInt(tantoInfo.getShokushuId()));
                    cpnTucCks222Mapper.insertSelective(cks222RgtrInfo);
                }
            }
        }
    }

    /**
     * 4.1. リクエストパラメータ.計画書(2)リスト.更新区分が新規の場合、28-21 計画書（２）データ：新／旧共用を新規登録する。
     *
     * @param kasyo2Info 計画書(2)データー
     */
    private void cks22RgtrProc(Gui01014Keikasyo2Upd kasyo2Info) throws Exception {
        CpnTucCks22 cks22InsInfo = new CpnTucCks22();
        // 計画書（2）ID リクエストパラメータ.計画書（２）リスト.計画書（2）ID
        cks22InsInfo.setKs21Id(CommonDtoUtil.strValToInt(kasyo2Info.getKs21Id()));
        // 具体的 リクエストパラメータ.計画書（２）リスト.具体的
        cks22InsInfo.setGutaitekiKnj(kasyo2Info.getGutaitekiKnj());
        // 長期 リクエストパラメータ.計画書（２）リスト.長期
        cks22InsInfo.setChoukiKnj(kasyo2Info.getChoukiKnj());
        // 短期 リクエストパラメータ.計画書（２）リスト.短期
        cks22InsInfo.setTankiKnj(kasyo2Info.getTankiKnj());
        // 介護 リクエストパラメータ.計画書（２）リスト.介護
        cks22InsInfo.setKaigoKnj(kasyo2Info.getKaigoKnj());
        // サービス種 リクエストパラメータ.計画書（２）リスト.サービス種
        cks22InsInfo.setSvShuKnj(kasyo2Info.getSvShuKnj());
        // 頻度 リクエストパラメータ.計画書（２）リスト.頻度
        cks22InsInfo.setHindoKnj(kasyo2Info.getHindoKnj());
        // 期間 リクエストパラメータ.計画書（２）リスト.期間
        cks22InsInfo.setKikanKnj(kasyo2Info.getKikanKnj());
        // 通番 リクエストパラメータ.計画書（２）リスト.通番
        cks22InsInfo.setSeq(CommonDtoUtil.strValToInt(kasyo2Info.getSeq()));
        // 課題番号 リクエストパラメータ.計画書（２）リスト.課題番号
        cks22InsInfo.setKadaiNo(CommonDtoUtil.strValToInt(kasyo2Info.getKadaiNo()));
        // 介護番号 リクエストパラメータ.計画書（２）リスト.介護番号
        cks22InsInfo.setKaigoNo(CommonDtoUtil.strValToInt(kasyo2Info.getKaigoNo()));
        // 長期期間 リクエストパラメータ.計画書（２）リスト.長期期間
        cks22InsInfo.setChoKikanKnj(kasyo2Info.getChoKikanKnj());
        // 短期期間 リクエストパラメータ.計画書（２）リスト.短期期間
        cks22InsInfo.setTanKikanKnj(kasyo2Info.getTanKikanKnj());
        // 給付対象 リクエストパラメータ.計画書（２）リスト.給付対象
        cks22InsInfo.setHkyuKbn(CommonDtoUtil.strValToInt(kasyo2Info.getHkyuKbn()));
        // サービス事業者ＣＤ リクエストパラメータ.計画書（２）リスト.サービス事業者ＣＤ
        cks22InsInfo.setJigyouId(CommonDtoUtil.strValToInt(kasyo2Info.getJigyouId()));
        // サービス事業者名 リクエストパラメータ.計画書（２）リスト.サービス事業者名
        cks22InsInfo.setJigyoNameKnj(kasyo2Info.getJigyoNameKnj());
        // 給付文字 リクエストパラメータ.計画書（２）リスト.給付文字
        cks22InsInfo.setHkyuKnj(kasyo2Info.getHkyuKnj());
        // 長期期間開始日 リクエストパラメータ.計画書（２）リスト.長期期間開始日
        cks22InsInfo.setChoSYmd(kasyo2Info.getChoSYmd());
        // 長期期間終了日 リクエストパラメータ.計画書（２）リスト.長期期間終了日
        cks22InsInfo.setChoEYmd(kasyo2Info.getChoEYmd());
        // 短期期間開始日 リクエストパラメータ.計画書（２）リスト.更新区分
        cks22InsInfo.setTanSYmd(kasyo2Info.getTanSYmd());
        // 短期期間終了日 リクエストパラメータ.計画書（２）リスト.更新区分
        cks22InsInfo.setTanEYmd(kasyo2Info.getTanEYmd());
        // 期間開始日 リクエストパラメータ.計画書（２）リスト.期間開始日
        cks22InsInfo.setKikanSYmd(kasyo2Info.getKikanSYmd());
        // 期間終了日 リクエストパラメータ.計画書（２）リスト.期間終了日
        cks22InsInfo.setKikanEYmd(kasyo2Info.getKikanEYmd());

        cpnTucCks22Mapper.insertSelectiveAndReturn(cks22InsInfo);
        // 4.1.1. 採番されたカウンターをリクエストパラメータ.計画書（２）リスト.カウンターを更新する。
        kasyo2Info.setKs22Id(CommonDtoUtil.objValToString(cks22InsInfo.getKs22Id()));
    }

    /**
     * 4.2. リクエストパラメータ.計画書(2)リスト.更新区分が更新の場合、28-21 計画書（２）データ：新／旧共用を更新する。
     *
     * @param kasyo2Info 計画書(2)データー
     * @throws ExclusiveException
     */
    private void cks22UpdProc(Gui01014Keikasyo2Upd kasyo2Info) throws ExclusiveException {
        CpnTucCks22 cks22UpdInfo = new CpnTucCks22();
        // 具体的 リクエストパラメータ.計画書（２）リスト.具体的
        cks22UpdInfo.setGutaitekiKnj(kasyo2Info.getGutaitekiKnj());
        // 長期 リクエストパラメータ.計画書（２）リスト.長期
        cks22UpdInfo.setChoukiKnj(kasyo2Info.getChoukiKnj());
        // 短期 リクエストパラメータ.計画書（２）リスト.短期
        cks22UpdInfo.setTankiKnj(kasyo2Info.getTankiKnj());
        // 介護 リクエストパラメータ.計画書（２）リスト.介護
        cks22UpdInfo.setKaigoKnj(kasyo2Info.getKaigoKnj());
        // サービス種 リクエストパラメータ.計画書（２）リスト.サービス種
        cks22UpdInfo.setSvShuKnj(kasyo2Info.getSvShuKnj());
        // 頻度 リクエストパラメータ.計画書（２）リスト.頻度
        cks22UpdInfo.setHindoKnj(kasyo2Info.getHindoKnj());
        // 期間 リクエストパラメータ.計画書（２）リスト.期間
        cks22UpdInfo.setKikanKnj(kasyo2Info.getKikanKnj());
        // 通番 リクエストパラメータ.計画書（２）リスト.通番
        cks22UpdInfo.setSeq(CommonDtoUtil.strValToInt(kasyo2Info.getSeq()));
        // 課題番号 リクエストパラメータ.計画書（２）リスト.課題番号
        cks22UpdInfo.setKadaiNo(CommonDtoUtil.strValToInt(kasyo2Info.getKadaiNo()));
        // 介護番号 リクエストパラメータ.計画書（２）リスト.介護番号
        cks22UpdInfo.setKaigoNo(CommonDtoUtil.strValToInt(kasyo2Info.getKaigoNo()));
        // 長期期間 リクエストパラメータ.計画書（２）リスト.長期期間
        cks22UpdInfo.setChoKikanKnj(kasyo2Info.getChoKikanKnj());
        // 短期期間 リクエストパラメータ.計画書（２）リスト.短期期間
        cks22UpdInfo.setTanKikanKnj(kasyo2Info.getTanKikanKnj());
        // 給付対象 リクエストパラメータ.計画書（２）リスト.給付対象
        cks22UpdInfo.setHkyuKbn(CommonDtoUtil.strValToInt(kasyo2Info.getHkyuKbn()));
        // サービス事業者ＣＤ リクエストパラメータ.計画書（２）リスト.サービス事業者ＣＤ
        cks22UpdInfo.setJigyouId(CommonDtoUtil.strValToInt(kasyo2Info.getJigyouId()));
        // サービス事業者名 リクエストパラメータ.計画書（２）リスト.サービス事業者名
        cks22UpdInfo.setJigyoNameKnj(kasyo2Info.getJigyoNameKnj());
        // 給付文字 リクエストパラメータ.計画書（２）リスト.給付文字
        cks22UpdInfo.setHkyuKnj(kasyo2Info.getHkyuKnj());
        // 長期期間開始日 リクエストパラメータ.計画書（２）リスト.長期期間開始日
        cks22UpdInfo.setChoSYmd(kasyo2Info.getChoSYmd());
        // 長期期間終了日 リクエストパラメータ.計画書（２）リスト.長期期間終了日
        cks22UpdInfo.setChoEYmd(kasyo2Info.getChoEYmd());
        // 短期期間開始日 リクエストパラメータ.計画書（２）リスト.更新区分
        cks22UpdInfo.setTanSYmd(kasyo2Info.getTanSYmd());
        // 短期期間終了日 リクエストパラメータ.計画書（２）リスト.更新区分
        cks22UpdInfo.setTanEYmd(kasyo2Info.getTanEYmd());
        // 期間開始日 リクエストパラメータ.計画書（２）リスト.期間開始日
        cks22UpdInfo.setKikanSYmd(kasyo2Info.getKikanSYmd());
        // 期間終了日 リクエストパラメータ.計画書（２）リスト.期間終了日
        cks22UpdInfo.setKikanEYmd(kasyo2Info.getKikanEYmd());

        // 更新条件キー
        CpnTucCks22Criteria whereCks22Info = new CpnTucCks22Criteria();
        whereCks22Info.createCriteria().andKs22IdEqualTo(CommonDtoUtil.strValToInt(kasyo2Info.getKs22Id()))
                .andKs21IdEqualTo(CommonDtoUtil.strValToInt(kasyo2Info.getKs21Id()));
        Integer updCnt = cpnTucCks22Mapper.updateByCriteriaSelective(cks22UpdInfo, whereCks22Info);
        if (updCnt <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 4.3. リクエストパラメータ.計画書(2)リスト.更新区分が削除の場合、28-21 計画書（２）データ：新／旧共用を削除する。
     *
     * @param kasyo2Info 計画書(2)データー
     * @throws ExclusiveException
     */
    private void cks22DelProc(Gui01014Keikasyo2Upd kasyo2Info) throws ExclusiveException {
        // 更新条件キー
        CpnTucCks22Criteria whereCks22Info = new CpnTucCks22Criteria();
        whereCks22Info.createCriteria().andKs22IdEqualTo(CommonDtoUtil.strValToInt(kasyo2Info.getKs22Id()))
                .andKs21IdEqualTo(CommonDtoUtil.strValToInt(kasyo2Info.getKs21Id()));
        Integer updCnt = cpnTucCks22Mapper.deleteByCriteria(whereCks22Info);
        if (updCnt <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 5.リクエストパラメータ.計画書様式が居宅の場合、居宅の計画書（２）情報を保存する。
     *
     * @param inDto 計画書（2）情報保存の入力DTO.
     * @throws Exception
     */
    private void cksHomeSaveProc(CarePlan2UpdateServiceInDto inDto) throws Exception {
        // リクエストパラメータ.計画書様式が居宅の場合
        if (CommonConstants.CKS_FLG_HOME.equals(inDto.getCksflg())) {
            // 5.1. 居宅の計画書（２）情報を削除する。
            // ■削除条件
            CpnTucCks211Criteria cks211WhereInfo = new CpnTucCks211Criteria();
            cks211WhereInfo.createCriteria()
                    // 計画書（2）ID = リクエストパラメータ.計画書（２）ID
                    .andKs21IdEqualTo(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
            cpnTucCks211Mapper.deleteByCriteria(cks211WhereInfo);
            // データなしの可能性があるため、ExclusiveExceptionチェックが不要

            // 5.1.2. 28-66計画書（２）（月日指定）を削除する。
            // ■削除条件
            CpnTucCks212Criteria cks212WhereInfo = new CpnTucCks212Criteria();
            cks212WhereInfo.createCriteria()
                    // 計画書（2）ID = リクエストパラメータ.計画書（２）ID
                    .andKs21IdEqualTo(CommonDtoUtil.strValToInt(inDto.getKs21Id()));
            cpnTucCks212Mapper.deleteByCriteria(cks212WhereInfo);

            // 5.2. 保険サービスリストより繰り返す。
            // 5.2.1. 28-20計画書（２）サービスを登録する。
            for (Gui01014HokenUpd hokenInfo : inDto.getHokenList()) {
                cks211RgtrProc(hokenInfo);
            }

            // 5.3. 月日指定リストより繰り返す。
            // 5.3.1. 28-66計画書（２）（月日指定）を登録する。
            for (Gui01014TukihiUpd tukihiInfo : inDto.getTukihiList()) {
                cks212EditProc(tukihiInfo);
            }
        }
    }

    /**
     * 5.2.1. 28-20計画書（２）サービスを登録する。
     *
     * @param hokenInfo 保険サービスデーター
     */
    private void cks211RgtrProc(Gui01014HokenUpd hokenInfo) throws Exception {
        CpnTucCks211 cks211Info = new CpnTucCks211();
        // 計画書（2）ID リクエストパラメータ.保険サービスリスト.計画書（２）ID
        cks211Info.setKs21Id(CommonDtoUtil.strValToInt(hokenInfo.getKs21Id()));
        // 曜日 リクエストパラメータ.保険サービスリスト.曜日
        cks211Info.setYoubi(hokenInfo.getYoubi());
        // 週単位以外のｻｰﾋﾞｽ区分 リクエストパラメータ.保険サービスリスト.週単位以外のｻｰﾋﾞｽ区分
        cks211Info.setIgaiKbn(CommonDtoUtil.strValToInt(hokenInfo.getIgaiKbn()));
        // 週単位以外のｻｰﾋﾞｽ（日付指定） リクエストパラメータ.保険サービスリスト.週単位以外のｻｰﾋﾞｽ（日付指定）
        cks211Info.setIgaiDate(hokenInfo.getIgaiDate());
        // 週単位以外のｻｰﾋﾞｽ（曜日指定） リクエストパラメータ.保険サービスリスト.週単位以外のｻｰﾋﾞｽ（曜日指定）
        cks211Info.setIgaiWeek(hokenInfo.getIgaiWeek());
        // 居宅:開始時間 リクエストパラメータ.保険サービスリスト.居宅：開始時間
        cks211Info.setKaishiJikan(hokenInfo.getKaishiJikan());
        // 居宅:終了時間 リクエストパラメータ.保険サービスリスト.居宅：終了時間
        cks211Info.setShuuryouJikan(hokenInfo.getShuuryouJikan());
        // 居宅:サービス種類 リクエストパラメータ.保険サービスリスト.居宅：サービス種類
        cks211Info.setSvShuruiCd(hokenInfo.getSvShuruiCd());
        // 居宅:サービス項目（台帳） リクエストパラメータ.保険サービスリスト.居宅：サービス項目（台帳）
        cks211Info.setSvItemCd(CommonDtoUtil.strValToInt(hokenInfo.getSvItemCd()));
        // サービス事業者ID リクエストパラメータ.保険サービスリスト.居宅：サービス事業者CD
        cks211Info.setSvJigyoId(CommonDtoUtil.strValToInt(hokenInfo.getSvJigyoId()));
        // 居宅:福祉用具貸与単位 リクエストパラメータ.保険サービスリスト.居宅：福祉用具貸与単位
        cks211Info.setTanka(Double.valueOf(hokenInfo.getTanka()));
        // 福祉用具貸与商品コード リクエストパラメータ.保険サービスリスト.福祉用具貸与商品コード
        cks211Info.setFygId(CommonDtoUtil.strValToInt(hokenInfo.getFygId()));
        // 合成識別区分 リクエストパラメータ.保険サービスリスト.合成識別区分
        cks211Info.setGouseiSikKbn(hokenInfo.getGouseiSikKbn());
        // 加算フラグ リクエストパラメータ.保険サービスリスト.加算フラグ
        cks211Info.setKasanFlg(CommonDtoUtil.strValToInt(hokenInfo.getKasanFlg()));
        // 親レコード番号 リクエストパラメータ.保険サービスリスト.親レコード番号
        cks211Info.setOyaLineNo(CommonDtoUtil.strValToInt(hokenInfo.getOyaLineNo()));
        cpnTucCks211Mapper.insertSelective(cks211Info);
    }

    /**
     * 5.3.1. 28-66計画書（２）（月日指定）を登録する。
     *
     * @param tukihiInfo 月日指定データー
     */
    private void cks212EditProc(Gui01014TukihiUpd tukihiInfo) throws Exception {
        CpnTucCks212 cks212Info = new CpnTucCks212();
        // 計画書（2）ID リクエストパラメータ.月日指定リスト.詳細ID
        cks212Info.setKs21Id(CommonDtoUtil.strValToInt(tukihiInfo.getKs211Id()));
        // 詳細ID リクエストパラメータ.月日指定リスト.詳細ID
        cks212Info.setKs211Id(CommonDtoUtil.strValToInt(tukihiInfo.getKs211Id()));
        // 開始日 リクエストパラメータ.月日指定リスト.月日指定開始日
        cks212Info.setStartYmd(tukihiInfo.getStartYmd());
        // 終了日 リクエストパラメータ.月日指定リスト.月日指定終了日
        cks212Info.setEndYmd(tukihiInfo.getEndYmd());
        cpnTucCks212Mapper.insertSelective(cks212Info);
    }

}
