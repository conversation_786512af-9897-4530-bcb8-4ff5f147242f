package jp.ndsoft.carebase.cmn.api.service;


import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01268CpnTucCsc12;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01268CpnTucCsc2;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01268CpnTucCsc22;
import jp.ndsoft.carebase.cmn.api.service.dto.GeneralSituationSurveyDataUpdateInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.GeneralSituationSurveyDataUpdateOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.NtokkiSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.SpecialnoteMatterInfoUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.SpecialnoteMatterInfoUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCsc4Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCschMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCsc4;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCsc4Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCsch;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCschCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChotokkiResH21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChotokkiResH21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCsc4SelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * GUI01269_特記事項情報を保存する
 *
 * <AUTHOR>
 */
@Service
public class SpecialnoteMatterInfoUpdateServiceImpl extends
        UpdateServiceImpl<SpecialnoteMatterInfoUpdateServiceInDto, SpecialnoteMatterInfoUpdateServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 認定調査票ヘッダMapper */
    @Autowired
    private CpnTucCschMapper cpnTucCschMapper;

    /** 特記事項Mapper */
    @Autowired
    private CpnTucCsc4Mapper cpnTucCsc4Mapper;

    /** 認定調査票（特記事項）の情報を取得するMapper */
    @Autowired
    private CpnTucCsc4SelectMapper cpnTucCsc4SelectMapper;

    /** 概況調査のデータ保存Mapper */
    @Autowired
    private GeneralSituationSurveyDataUpdateServiceImpl generalSituationSurveyDataUpdateService;

    /**
     * 特記事項情報を保存する
     * 
     * 
     * @param inDto 特記事項情報保存報取取得入力Dto
     * @return 特記事項情報保存報取出力Dto
     * @throws Exception
     */
    @Override
    public SpecialnoteMatterInfoUpdateServiceOutDto mainProcess(SpecialnoteMatterInfoUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);

        // 認定調査票履歴 情報
        CpnTucCsch cpnTucCsch = new CpnTucCsch();
        cpnTucCsch.setSc1Id(CommonDtoUtil.strValToInt(inDto.getScId()));
        cpnTucCsch.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHojinId()));
        cpnTucCsch.setShisetuId(CommonDtoUtil.strValToInt(inDto.getScId()));
        cpnTucCsch.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        cpnTucCsch.setChkShokuId(CommonDtoUtil.strValToInt(inDto.getChkShokuId()));
        cpnTucCsch.setJisshiDateYmd(inDto.getJisshiDateYmd());
        cpnTucCsch.setNinteiFlg(CommonConstants.INT_4);
        cpnTucCsch.setIkenshoId(CommonDtoUtil.strValToInt(inDto.getIkenshoId()));
        cpnTucCsch.setCschId(CommonDtoUtil.strValToInt(inDto.getCschId()));
        cpnTucCsch.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getJigyoId()));

        // 特記事項List
        List<NtokkiSelectServiceInDto> tokkiList = inDto.getTokkiList();
        List<CpnTucCsc4> cpnTucCsc4List = new ArrayList<>();
        tokkiList.forEach(tokkiDto -> {
            CpnTucCsc4 cpnTucCsc4 = new CpnTucCsc4();
            cpnTucCsc4.setCounter(CommonDtoUtil.strValToInt(tokkiDto.getCounter()));
            cpnTucCsc4.setSc1Id(CommonDtoUtil.strValToInt(inDto.getScId()));
            cpnTucCsc4.setCschId(CommonDtoUtil.strValToInt(inDto.getCschId()));
            cpnTucCsc4.setN1tCd(CommonDtoUtil.strValToInt(tokkiDto.getN1tCd()));
            cpnTucCsc4.setN2tCd(tokkiDto.getN2tCd());
            cpnTucCsc4.setMemoKnj(tokkiDto.getMemoKnj());
            cpnTucCsc4.setSeqNo(CommonDtoUtil.strValToInt(tokkiDto.getSeqNo()));
            cpnTucCsc4List.add(cpnTucCsc4);
        });

        // 2.1 リクエストパラメータ.削除フラグは0 かつ リクエストパラメータ.調査票IDは0 の場合
        if (CommonDtoUtil.strValToInt(inDto.getDeleteFlg()) == CommonConstants.DELETE_FLG_0
                && CommonDtoUtil.strValToInt(inDto.getCschId()) == 0) { // 登録の場合

            // 2.1.1 認定調査票履歴を登録する。
            // GUI01268の「認定調査票情報保存」機能を呼び出し…
            GeneralSituationSurveyDataUpdateInDto generalSituationSurveyDataUpdateInDto = new GeneralSituationSurveyDataUpdateInDto();
            // パラメータ設定....
            List<Gui01268CpnTucCsc2> cpnTucCscList = new ArrayList<>();
            Gui01268CpnTucCsc2 ObjectcpnTucCsc = new Gui01268CpnTucCsc2();

            // 計画期間ID：inDtoからscIdを取得してsc1Idに設定
            ObjectcpnTucCsc.setSc1Id(inDto.getScId());

            // 事業者ID：inDtoから対応するフィールドを取得して設定
            ObjectcpnTucCsc.setSvjigyoId(inDto.getJigyoId());

            // 法人ID：inDtoから対応するフィールドを取得して設定
            ObjectcpnTucCsc.setHoujinId(inDto.getHojinId());

            // 施設ID：inDtoから対応するフィールドを取得して設定
            ObjectcpnTucCsc.setShisetuId(inDto.getShisetuId());

            // 利用者ID：inDtoから対応するフィールドを取得して設定
            ObjectcpnTucCsc.setUserId(inDto.getUserId());

            // 記入者コード：inDtoから対応するフィールドを取得して設定
            ObjectcpnTucCsc.setChkShokuId(inDto.getChkShokuId());

            // 実施日：inDtoから対応するフィールドを取得して設定
            ObjectcpnTucCsc.setJisshiDateYmd(inDto.getJisshiDateYmd());

            // 調査票改訂フラグ：inDtoから対応するフィールドを取得して設定
            ObjectcpnTucCsc.setNinteiFlg(inDto.getNinteiFlg());

            // 調査票ID：inDtoから対応するフィールドを取得して設定
            ObjectcpnTucCsc.setCschId(inDto.getCschId());

            // 医師意見書ID：固定値：NULL
            ObjectcpnTucCsc.setIkenshoId(null);

            // 履歴番号：inDtoから対応するフィールドを取得して設定
            ObjectcpnTucCsc.setRirekiNo(inDto.getRirekiNo());

            cpnTucCscList.add(ObjectcpnTucCsc);

            generalSituationSurveyDataUpdateInDto.setCpnTucCscList(cpnTucCscList);

            // 機能ID:inDtoから対応するフィールドを取得して設定
            generalSituationSurveyDataUpdateInDto.setKinouId(inDto.getKinouId());

            List<Gui01268CpnTucCsc12> cpnTucCsc12List = new ArrayList<>();
            Gui01268CpnTucCsc12 csc12NewObj = new Gui01268CpnTucCsc12();
            csc12NewObj.setChiikiCd(null); // 地域コード
            csc12NewObj.setTaishoushaCd(null); // 調査対象者コード
            csc12NewObj.setWhereCd(null); // 実施場所
            csc12NewObj.setWhereMemoKnj(null); // 実施場所その他
            csc12NewObj.setOldNintei(null); // 過去の認定
            csc12NewObj.setOldNinteiYmd(null); // 前回認定日
            csc12NewObj.setOldNinteiDo1(null); // 前回認定結果
            csc12NewObj.setOldNinteiDo2(null); // 前回認定結果（要介護度）
            csc12NewObj.setKankeiId(null); // 関係者連番
            csc12NewObj.setRenrakuNameKnj(null); // 連絡先氏名
            csc12NewObj.setRenrakuZcode(null); // 連絡先続柄
            csc12NewObj.setAddressKnj(null); // 連絡先住所
            csc12NewObj.setRenrakuZip(null); // 連絡先〒
            csc12NewObj.setRenrakuTel(null); // 連絡先ＴＥＬ
            csc12NewObj.setKencode(null); // 県コード
            csc12NewObj.setCitycode(null); // 市町村コード
            csc12NewObj.setAreacode(null); // 地区コード
            csc12NewObj.setAdl1Id(null); // 障害老人ADL
            csc12NewObj.setAdl2Id(null); // 痴呆老人ADL
            csc12NewObj.setShisetsuShu(null); // 利用施設種別
            csc12NewObj.setShisetsuId(null); // 利用施設ＩＤ
            csc12NewObj.setShisetuKnj(null); // 施設名
            csc12NewObj.setMemoKnj(null); // 内容・特記事項
            csc12NewObj.setYokaiKbn1(null); // 要介護状態見込み
            csc12NewObj.setYokaiKbn2(null); // 要介護状態確定
            csc12NewObj.setNinteiYmd(null); // 認定年月日
            csc12NewObj.setSvJigyoId(null); // サービス事業者ID
            csc12NewObj.setNinteiShinseiYmd(null); // 認定申請日
            csc12NewObj.setShisetsuNameKnj(null); // 利用施設名
            csc12NewObj.setShisetsuZip(null); // 郵便番号
            csc12NewObj.setShisetsuAddressKnj(null);// 施設住所
            csc12NewObj.setShisetsuTel(null); // 電話番号
            csc12NewObj.setOldNinteiDo3(null); // 前回認定結果（要支援度）
            cpnTucCsc12List.add(csc12NewObj);
            generalSituationSurveyDataUpdateInDto.setCpnTucCsc12List(cpnTucCsc12List);

            List<Gui01268CpnTucCsc22> cpnTucCsc22List = new ArrayList<>();
            Gui01268CpnTucCsc22 csc22NewObj = new Gui01268CpnTucCsc22();
            csc22NewObj.setService1Cd(null); // サービス利用ＣＤ１
            csc22NewObj.setService2Cd(null); // サービス利用ＣＤ2
            csc22NewObj.setService3Cd(null); // サービス利用ＣＤ3
            csc22NewObj.setService4Cd(null); // サービス利用ＣＤ4
            csc22NewObj.setService5Cd(null); // サービス利用ＣＤ5
            csc22NewObj.setService6Cd(null); // サービス利用ＣＤ6
            csc22NewObj.setService7Cd(null); // サービス利用ＣＤ7
            csc22NewObj.setService8Cd(null); // サービス利用ＣＤ8
            csc22NewObj.setService9Cd(null); // サービス利用ＣＤ９
            csc22NewObj.setService10Cd(null); // サービス利用ＣＤ１０
            csc22NewObj.setService11Cd(null); // サービス利用ＣＤ１１
            csc22NewObj.setService12Cd(null); // サービス利用ＣＤ１２
            csc22NewObj.setService13Cd(null); // サービス利用ＣＤ１３
            csc22NewObj.setService14Cd(null); // サービス利用ＣＤ１４
            csc22NewObj.setService15Cd(null); // サービス利用ＣＤ１５
            csc22NewObj.setService16Cd(null); // サービス利用ＣＤ１６
            csc22NewObj.setKaisuu1(null); // 利用回数１
            csc22NewObj.setKaisuu2(null); // 利用回数２
            csc22NewObj.setKaisuu3(null); // 利用回数３
            csc22NewObj.setKaisuu4(null); // 利用回数４
            csc22NewObj.setKaisuu5(null); // 利用回数５
            csc22NewObj.setKaisuu6(null); // 利用回数６
            csc22NewObj.setKaisuu7(null); // 利用回数７
            csc22NewObj.setKaisuu8(null); // 利用回数８
            csc22NewObj.setKaisuu9(null); // 利用回数９
            csc22NewObj.setKaisuu10(null); // 利用回数１０
            csc22NewObj.setKaisuu11(null); // 利用回数１１
            csc22NewObj.setKaisuu12(null); // 利用回数１２
            csc22NewObj.setKaisuu13(null); // 利用回数１３
            csc22NewObj.setKaishuUmu(null); // 改修あり・なし
            csc22NewObj.setMemo1Knj(null); // 特別給付
            csc22NewObj.setMemo2Knj(null); // 給付外サービス
            csc22NewObj.setService17Cd(null); // サービス利用ＣＤ１７
            csc22NewObj.setService18Cd(null); // サービス利用ＣＤ１８
            csc22NewObj.setService19Cd(null); // サービス利用ＣＤ１９
            csc22NewObj.setService20Cd(null); // サービス利用ＣＤ２０
            csc22NewObj.setKaisuu17(null); // 利用回数１７
            csc22NewObj.setKaisuu18(null); // 利用回数１８
            csc22NewObj.setKaisuu19(null); // 利用回数１９
            csc22NewObj.setKaisuu20(null); // 利用回数２０
            csc22NewObj.setKaisuu21(null); // 利用回数２１
            csc22NewObj.setService21Cd(null); // サービス利用ＣＤ２1
            csc22NewObj.setService22Cd(null); // サービス利用ＣＤ２2
            csc22NewObj.setService23Cd(null); // サービス利用ＣＤ２3
            csc22NewObj.setKaisuu22(null); // 利用回数２2
            csc22NewObj.setKaisuu23(null); // 利用回数２3
            cpnTucCsc22List.add(csc22NewObj);
            generalSituationSurveyDataUpdateInDto.setCpnTucCsc22List(cpnTucCsc22List);

            GeneralSituationSurveyDataUpdateOutDto outdto = generalSituationSurveyDataUpdateService
                    .mainProcess(generalSituationSurveyDataUpdateInDto);

            AtomicInteger seqNoCounter = new AtomicInteger(CommonConstants.INT_1);
            // 2.1.2 認定調査票（特記事項）を登録する。
            if (!CollectionUtils.isEmpty(cpnTucCsc4List)) {
                cpnTucCsc4List.forEach(cpnTucCsc4Dto -> {
                    // cschIdの値は「generalSituationSurveyDataUpdateService.mainProcess」の戻り値から取って行きます
                    cpnTucCsc4Dto.setCschId(CommonDtoUtil.strValToInt(outdto.getCschId()));
                    cpnTucCsc4Dto.setSc1Id(CommonDtoUtil.strValToInt(inDto.getScId()));
                    cpnTucCsc4Dto.setN1tCd(CommonDtoUtil.strValToInt(inDto.getTokkiList().get(0).getN1tCd()));
                    cpnTucCsc4Dto.setN2tCd(inDto.getTokkiList().get(0).getN2tCd());
                    cpnTucCsc4Dto.setMemoKnj(inDto.getTokkiList().get(0).getMemoKnj());
                    cpnTucCsc4Dto.setSeqNo(seqNoCounter.get());
                    seqNoCounter.incrementAndGet();

                    cpnTucCsc4Mapper.insertSelective(cpnTucCsc4Dto);
                });
            }
        } else if (CommonDtoUtil.strValToInt(inDto.getDeleteFlg()) == CommonConstants.DELETE_FLG_0
                && CommonDtoUtil.strValToInt(inDto.getCschId()) != 0) { // 更新の場合
        	// 2.2.2.1　元特記事項リストを取得する。
        	ChotokkiResH21ByCriteriaInEntity chotokkiResH21ByCriteriaInEntity = new ChotokkiResH21ByCriteriaInEntity();
        	chotokkiResH21ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getScId()));
        	chotokkiResH21ByCriteriaInEntity.setAlCschId(CommonDtoUtil.strValToInt(inDto.getCschId()));
        	List<ChotokkiResH21OutEntity> chotokkiResH21OutEntities = cpnTucCsc4SelectMapper.findChotokkiResH21ByCriteria(chotokkiResH21ByCriteriaInEntity);
        	   
        	// 2.2.2.2 上記処理2.2.2.1取得した元特記事項リストとリクエストパラメータ.特記事項リストを比較し   
        	   
        	   
        	   
        	   
        	   
//            // 2.2.1 認定調査票履歴を更新する
//            CpnTucCschCriteria cpnTucCschCriteria = new CpnTucCschCriteria();
//             cpnTucCschCriteria.createCriteria()
//             .andCschIdEqualTo(CommonDtoUtil.strValToInt(inDto.getCschId()));
//            int updCount = cpnTucCschMapper.updateByCriteria(cpnTucCsch, cpnTucCschCriteria);
//            if (updCount <= 0) {
//                throw new ExclusiveException();
//            }
//
//            // 2.2.2 認定調査票（特記事項）を更新する。
//         
//         
//            Map<Integer, List<ChotokkiResH21OutEntity>> chotokkiResH21Groups = chotokkiResH21OutEntities.stream()
//                    .collect(Collectors.groupingBy(dto -> dto.getCounter()));
//
//            for (int i = 0; i < cpnTucCsc4List.size(); i++) {
//                CpnTucCsc4 cpnTucCsc4 = cpnTucCsc4List.get(i);
//
//                // List<ChotokkiResH21OutEntity> currentList =
//                // chotokkiResH21Groups.get(cpnTucCsc4.getCounter());
//                List<ChotokkiResH21OutEntity> currentList = null;
//                if (cpnTucCsc4 != null) {
//                    Integer counterBigInt = cpnTucCsc4.getCounter();
//                    if (counterBigInt != null) {
//                        if (counterBigInt.compareTo(Integer.MAX_VALUE) <= 0
//                                && CommonConstants.INT_0 >= 0) {
//                            int counter = counterBigInt.intValue();
//                            currentList = chotokkiResH21Groups.get(counter);
//                        }
//                    }
//                }
//
//                // ①リクエストパラメータ.カウンターが処理2.2.2.1取得した元特記事項リストに存在しない場合、認定調査票（特記事項）を登録する
//                if (null == currentList || CollectionUtils.isEmpty(currentList)) {
//                    cpnTucCsc4Mapper.insertSelective(cpnTucCsc4);
//                } else { // ③リクエストパラメータ.カウンターが処理2.2.2.1取得した元特記事項リストに存在する場合、認定調査票（特記事項）を更新する
//                    CpnTucCsc4Criteria cpnTucCsc4Criteria = new CpnTucCsc4Criteria();
//                     cpnTucCsc4Criteria.createCriteria()
//                     .andCounterEqualTo(cpnTucCsc4.getCounter());
//                    int updateCount = cpnTucCsc4Mapper.updateByCriteriaSelective(cpnTucCsc4, cpnTucCsc4Criteria);
////                    if (updateCount <= 0) {
////                        throw new ExclusiveException();
////                    }
//                    // 更新したデータの値をNULLに設定して、処理2.2.2.1削除するデータから除く
//                    // chotokkiResH21Groups.remove(cpnTucCsc4.getCounter());
//                    if (cpnTucCsc4 != null) {
//                        Integer counterBigInt = cpnTucCsc4.getCounter();
//                        if (counterBigInt != null) {
//                            if (counterBigInt.compareTo(Integer.MAX_VALUE) <= 0
//                                    && counterBigInt.compareTo(CommonConstants.INT_0) >= 0) {
//                                int counter = counterBigInt.intValue();
//                                if (counter < chotokkiResH21Groups.size()) {
//                                    chotokkiResH21Groups.remove(counter);
//                                }
//                            }
//                        }
//                    }
//                }
//
//                // ②処理2.2.2.1取得した元特記事項リスト.カウンターがリクエストパラメータ.カウンターに存在しない場合、認定調査票（特記事項）を論理削除する
//                for(Map.Entry<Integer, List<ChotokkiResH21OutEntity>> item :chotokkiResH21Groups.entrySet()) {
//            	  List<ChotokkiResH21OutEntity> curList = item.getValue();
//            	  ChotokkiResH21OutEntity chotokkiResH21OutEntity = curList.get(0);
//            	  if (cpnTucCsc4List.stream().noneMatch(a -> a.getCounter().equals(chotokkiResH21OutEntity.getCounter()))) {
//        			  CpnTucCsc4Criteria criteria = new CpnTucCsc4Criteria();
//        			  criteria.createCriteria().andCounterEqualTo(chotokkiResH21OutEntity.getCounter());
//        			  // 削除（論理）時の共通カラム値設定処理
//        			  int delCount = cpnTucCsc4Mapper.deleteByCriteria(criteria);
//        			  if (delCount <= 0) {
//        				  throw new ExclusiveException();
//        			  }
//            	  }
//            	  
//                }
//            }

        } else if (CommonDtoUtil.strValToInt(inDto.getDeleteFlg()) != CommonConstants.DELETE_FLG_0
                && CommonDtoUtil.strValToInt(inDto.getCschId()) != CommonConstants.INT_0) {
            // 削除の場合
            if (CommonDtoUtil.strValToInt(inDto.getDeleteFlg()) == CommonConstants.DEF_UPDATE_FLG_1) {
                // 2.3.1 リクエストパラメータ.削除フラグは1の場合 削除時(論理削除)
                for (int i = 0; i < cpnTucCsc4List.size(); i++) {
                    CpnTucCsc4 cpnTucCsc4 = cpnTucCsc4List.get(i);
                    CpnTucCsc4Criteria cpnTucCsc4Criteria = new CpnTucCsc4Criteria();
                     cpnTucCsc4Criteria.createCriteria()
                     .andCschIdEqualTo(cpnTucCsc4.getCschId());
                    // 削除（論理）時の共通カラム値設定処理
                    int delCount = cpnTucCsc4Mapper.updateByCriteriaSelective(cpnTucCsc4, cpnTucCsc4Criteria);
                    if (delCount <= 0) {
                        throw new ExclusiveException();
                    }
                }
            } else if (CommonDtoUtil.strValToInt(inDto.getDeleteFlg()) == CommonConstants.DEF_UPDATE_FLG_2) {
                // 2.3.2 リクエストパラメータ.削除フラグ は 2の場合

                // 「API定義書_APINo(609)_概況調査のデータ保存.xlsx」の「API定義 (2)」の認定調査票履歴を全削除するメソッドを呼び出し

                // パラメータ設定....
                String sc1Id = inDto.getScId(); // 計画期間ID
                String svjigyoId = inDto.getJigyoId(); // 事業者ID
                String houjinId = inDto.getHojinId(); // 法人ID
                String shisetuId = inDto.getShisetuId(); // 施設ID
                String userId = inDto.getUserId(); // 利用者ID
                String deleteFlg = inDto.getDeleteFlg(); // 削除フラグ
                String chkShokuId = inDto.getChkShokuId(); // 記入者コード
                String jisshiDateYmd = inDto.getJisshiDateYmd(); // 実施日
                String ninteiFlg = inDto.getNinteiFlg(); // 調査票改訂フラグ
                String cschId = inDto.getCschId(); // 調査票ID
                String ikenshoId = inDto.getIkenshoId(); // 医師意見書ID
                String rirekiNo = inDto.getRirekiNo(); // 履歴番号
                String kinouId = inDto.getKinouId(); // 機能ID

                List<Gui01268CpnTucCsc2> cpnTucCscList = new ArrayList<>();

                Gui01268CpnTucCsc2 cpnTucCsc = new Gui01268CpnTucCsc2();

                cpnTucCsc.setSc1Id(inDto.getScId()); // 計画期間ID
                cpnTucCsc.setSvjigyoId(inDto.getJigyoId()); // 事業者ID
                cpnTucCsc.setHoujinId(inDto.getHojinId()); // 法人ID
                cpnTucCsc.setShisetuId(inDto.getShisetuId()); // 施設ID
                cpnTucCsc.setUserId(inDto.getUserId()); // 利用者ID
                cpnTucCsc.setChkShokuId(inDto.getChkShokuId()); // 記入者コード
                cpnTucCsc.setJisshiDateYmd(inDto.getJisshiDateYmd()); // 実施日
                cpnTucCsc.setNinteiFlg(inDto.getNinteiFlg()); // 調査票改訂フラグ
                cpnTucCsc.setCschId(inDto.getCschId()); // 調査票ID
                cpnTucCsc.setIkenshoId(inDto.getIkenshoId()); // 医師意見書ID
                cpnTucCsc.setRirekiNo(inDto.getRirekiNo()); // 履歴番号

                cpnTucCscList.add(cpnTucCsc);

                generalSituationSurveyDataUpdateService.ninteiChousahyouSave(
                        sc1Id, // 1. 計画期間ID
                        svjigyoId, // 2. 事業者ID
                        houjinId, // 3. 法人ID
                        shisetuId, // 4. 施設ID
                        userId, // 5. 利用者ID
                        deleteFlg, // 6. 削除フラグ
                        chkShokuId, // 7. 記入者コード
                        jisshiDateYmd, // 8. 実施日
                        ninteiFlg, // 9. 調査票改訂フラグ
                        cschId, // 10. 調査票ID
                        ikenshoId, // 11. 医師意見書ID
                        rirekiNo, // 12. 履歴番号
                        kinouId, // 13. 機能ID
                        cpnTucCscList // 15. 列表参数
                );

            }

        }

        LOG.info(Constants.END);
        return new SpecialnoteMatterInfoUpdateServiceOutDto();
    }
}
