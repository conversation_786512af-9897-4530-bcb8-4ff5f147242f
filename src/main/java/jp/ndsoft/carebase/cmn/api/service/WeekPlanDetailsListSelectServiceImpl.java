package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038CarePlan;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks52;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks54;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks55;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks56;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks57;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks58;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks59;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Service;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038YearMonth;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnWeekLogic;
import jp.ndsoft.carebase.cmn.api.logic.dto.Cks58InDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.TermId2DateLogicOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekPlanDetailsListSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekPlanDetailsListSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.Cks52InfoForShisetuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Cks52InfoForShisetuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Cks54InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Cks54InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Cks56InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Cks56InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Cks57InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Cks57InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCks224InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCks224InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekIgaiHindoHanniKakusyuiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekIgaiHindoHanniKakusyuiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekInputKasanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekInputKasanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekSvShurui1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekSvShurui1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpncCks22ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpncCks22OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoDataServiceInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoDataServiceInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoServiceDataMdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoServiceDataMdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2HeadInfoListByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2HeadInfoListOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2ServiceInfoDmyByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2ServiceInfoDmyOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2ServiceYobiInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2ServiceYobiInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2TantouInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2TantouInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2TukihiInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2TukihiInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSvselSelJigyoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSvselSelJigyoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KyccTucPlan11ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KyccTucPlan11OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnSvselSelJigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoNameSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks211SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks212SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks221SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks222SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks224SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks52SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks54SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks55SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks56SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks57SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks58SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KeikakushoKyouyouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KycTucPlan11SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KycTucPlan15SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KycTucPlan16SelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * GUI01038_週間計画詳細リスト取得
 * APINo(378)_週間計画詳細リスト取得
 * 
 * <AUTHOR>
 */
@Service
public class WeekPlanDetailsListSelectServiceImpl
        extends SelectServiceImpl<WeekPlanDetailsListSelectServiceInDto, WeekPlanDetailsListSelectServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 計画書（２）ヘッダ情報取得する */
    @Autowired
    private CpnTucCks21SelectMapper cpnTucCks21SelectMapper;

    /** 計画書（２）リスト取得する */
    @Autowired
    private KeikakushoKyouyouSelectMapper keikakushoKyouyouSelectMapper;

    /** 計画書（２）サービス曜日取得する */
    @Autowired
    private CpnTucCks221SelectMapper cpnTucCks221SelectMapper;

    /** 計画書（２）担当者取得する */
    @Autowired
    private CpnTucCks222SelectMapper cpnTucCks222SelectMapper;

    /** 計画書（２）の最新履歴情報取得する */
    @Autowired
    private KycTucPlan11SelectMapper kycTucPlan11SelectMapper;

    /** サービスリスト取得する */
    @Autowired
    private KycTucPlan15SelectMapper kycTucPlan15SelectMapper;

    /** 日月情報リスト取得する */
    @Autowired
    private KycTucPlan16SelectMapper kycTucPlan16SelectMapper;

    /** サービスリスト取得する */
    @Autowired
    private CpnTucCks211SelectMapper cpnTucCks211SelectMapper;

    /** 日月情報リスト取得する */
    @Autowired
    private CpnTucCks212SelectMapper cpnTucCks212SelectMapper;

    /** サービス種類名称取得する */
    @Autowired
    private ComMscSvjigyoNameSelectMapper comMscSvjigyoNameSelectMapper;

    /** サービス事業名称、略称取得する */
    @Autowired
    private CmnSvselSelJigyoSelectMapper cmnSvselSelJigyoSelectMapper;

    /** 週間計画詳細情報取得する */
    @Autowired
    private CpnTucCks52SelectMapper cpnTucCks52SelectMapper;

    /** 週間計画表（加算データ）情報取得する */
    @Autowired
    private CpnTucCks55SelectMapper cpnTucCks55SelectMapper;

    /** 週間計画表（担当者）情報取得する */
    @Autowired
    private CpnTucCks56SelectMapper cpnTucCks56SelectMapper;

    /** 週間計画（月日指定）情報取得する */
    @Autowired
    private CpnTucCks57SelectMapper cpnTucCks57SelectMapper;

    /** 週間計画表（隔週）情報取得する */
    @Autowired
    private CpnTucCks58SelectMapper cpnTucCks58SelectMapper;

    /** 週間計画表（日常生活データ）情報取得する */
    @Autowired
    private CpnTucCks54SelectMapper cpnTucCks54SelectMapper;

    /** 記録との連携情報取得 */
    @Autowired
    private CpnTucCks224SelectMapper cpnTucCks224SelectMapper;

    /** KghKrkZCpnFuncロジッククラス */
    @Autowired
    private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;

    /** 西暦→和暦関数（簡易版） */
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    /** サービス項目名称を取得する */
    @Autowired
    private KghKrkZCpnWeekLogic kghKrkZCpnWeekLogic;

    /** APINo(523)_取込情報編集 */
    @Autowired
    private WeekPlanImportInfoEditSelectServiceImpl weekPlanImportInfoEditSelectServiceImpl;

    /**
     * 週間計画詳細リスト取得
     * 
     * @param inDto 週間計画詳細リスト取得の入力Dto
     * @return outDto 週間計画詳細リスト取得の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected WeekPlanDetailsListSelectServiceOutDto mainProcess(WeekPlanDetailsListSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // 週間計画詳細リスト取得の出力Dto
        WeekPlanDetailsListSelectServiceOutDto outDto = new WeekPlanDetailsListSelectServiceOutDto();
        // 1.単項目チェック以外の入力チェック
        // 2. リクエストパラメータ.週間計画IDがブランクの場合 かつ リクエストパラメータ.取込元が"1:計画書から取込する"の場合
        if (StringUtils.isEmpty(inDto.getKs51Id())
                && CommonConstants.TORIKOMI_MOTO_ONE.equals(inDto.getTorikomiMoto())) {
            // ・レスポンスパラメータ.モード ＝ "1"(取込)
            outDto.setMode(CommonConstants.MOCK_ONE);
            // ※「API定義6」シートを参照する
            api6DataExProc(inDto, outDto);
        } else {
            // 3. 上記以外の場合、【週間計画詳細情報取得】データ情報を取得する
            // ・レスポンスパラメータ.モード ＝ "2"(正常)
            outDto.setMode(CommonConstants.MOCK_TWO);
            // ※「API定義5」シートを参照する
            api5DataExProc(inDto, outDto);
        }
        // 4. 上記処理で取得した結果レスポンスを返却する。
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * API定義2
     * 
     * @param inDto  週間計画詳細リスト取得の入力Dto
     * @param outDto 週間計画詳細リスト取得の出力Dto
     * @throws Exception
     */
    private void api2DataExProc(WeekPlanDetailsListSelectServiceInDto inDto,
            WeekPlanDetailsListSelectServiceOutDto outDto) {
        // 1. 初期データを取込む
        // ・変数.計画書リスト
        List<Gui01038CarePlan> planList = new ArrayList<>();
        // ・変数.サービスリスト
        List<Gui01038Service> serviceList = new ArrayList<>();
        // ・変数.担当者リスト
        List<Keikaku2TantouInfoOutEntity> userList = new ArrayList<>();
        // ・変数.月日リスト
        List<Gui01038YearMonth> monthDayList = new ArrayList<>();
        // ・変数.取込有効期間ID
        Integer termid;
        // ・変数.履歴ID
        Integer historyId = CommonConstants.INTEGER_NULL;
        // 1.1.「リクエストパラメータ.Sys略称が"CMN"以外の場合」、
        // または「リクエストパラメータ.Sys略称が"CMN"の場合、
        // 且つリクエストパラメータ.計画書仕様が"1（施設）"の場合」
        if (!CommonConstants.SYS_KNJ_CMN.equals(inDto.getSys3Ryaku())
                || (CommonConstants.SYS_KNJ_CMN.equals(inDto.getSys3Ryaku())
                        && CommonConstants.CKS_FLG_FACILITY.equals(inDto.getCksFlg()))) {
            // INPUT情報
            Keikaku2HeadInfoListByCriteriaInEntity keikakushoHeadByCriteriaInEntity = new Keikaku2HeadInfoListByCriteriaInEntity();
            // 計画期間ID
            keikakushoHeadByCriteriaInEntity.setSc1(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
            // 事業者ID
            keikakushoHeadByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 利用者ID
            keikakushoHeadByCriteriaInEntity.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
            // 1.1.1. 計画書（２）の最新履歴情報を取得する
            List<Keikaku2HeadInfoListOutEntity> keikakushoHeadOutList = cpnTucCks21SelectMapper
                    .findKeikaku2HeadInfoListByCriteria(keikakushoHeadByCriteriaInEntity);
            // 1.1.2. 上記1.1.1で取得した計画書（２）の最新履歴情報の1件目.有効期間IDが"5"の場合
            if (CollectionUtils.isEmpty(keikakushoHeadOutList)
                    || keikakushoHeadOutList.getFirst().getSc1Id() == CommonConstants.SC_1_ID_5) {
                // ・初期取込処理終了とする
                return;
            } else {
                // 1.1.3. 上記以外の場合
                // ・変数.履歴ID ＝ 1.1.1で取得した計画書（２）の最新履歴情報の1件目.計画書（2）ID
                historyId = keikakushoHeadOutList.getFirst().getKs21Id();
                // ・変数.取込有効期間ID ＝ 1.1.1で取得した計画書（２）の最新履歴情報の1件目.有効期間ID
                termid = keikakushoHeadOutList.getFirst().getTermid();
            }
            // INPUT情報
            CpncCks22ByCriteriaInEntity cpncCks22ByCriteriaInEntity = new CpncCks22ByCriteriaInEntity();
            // 計画書2ID
            cpncCks22ByCriteriaInEntity.setAlSc1Id(historyId);
            // 1.1.4. 計画書（２）リストを取得する
            List<CpncCks22OutEntity> cpncCks22OutList = keikakushoKyouyouSelectMapper
                    .findCpncCks22ByCriteria(cpncCks22ByCriteriaInEntity);
            // 1.1.4.1. 上記1.1.4で取得した計画書（２）リストの件数が0の場合
            if (CollectionUtils.isEmpty(cpncCks22OutList)) {
                // 1.1.4.1.1. リクエストパラメータ.期間管理フラグがfalse（期間管理しない）の場合
                if (CommonConstants.KIKANKANRI_FLG_FALSE.equals(inDto.getKikanFlag())) {
                    // ①. 上記1.1.1で取得した計画書（２）の最新履歴情報をループする
                    for (Keikaku2HeadInfoListOutEntity keikakushoHeadOutEntity : keikakushoHeadOutList) {
                        // ・ループ該当行.計画書（２）IDを変数.履歴IDに設定する
                        historyId = keikakushoHeadOutEntity.getKs21Id();
                        // 計画書2ID
                        cpncCks22ByCriteriaInEntity.setAlSc1Id(historyId);
                        // ・上記1.1.4の計画書（２）リストの取得処理を再実施する
                        cpncCks22OutList = keikakushoKyouyouSelectMapper
                                .findCpncCks22ByCriteria(cpncCks22ByCriteriaInEntity);
                        // ・上記取得した計画書（2）リストの件数が1件以上の場合、ループ終了する。
                        if (CollectionUtils.size(cpncCks22OutList) >= 1) {
                            break;
                        }
                    }
                }
            }
            // 1.1.5. 他項目の取得する
            // 1.1.5.1. 上記1.1.4で取得した計画書（２）リストをループする
            for (CpncCks22OutEntity cpncCks22OutEntity : cpncCks22OutList) {
                // ①. 計画書（２）リスト.計画書（２）IDがNULL以外の場合、かつ計画書（２）リスト.カウンターがNULL以外の場合
                if (cpncCks22OutEntity.getKs21Id() != null && cpncCks22OutEntity.getKs22Id() != null) {
                    // 変数.居宅開始時間
                    String startTime;
                    // 変数.居宅終了時間
                    String endTime;
                    // INPUT情報
                    Keikaku2ServiceYobiInfoByCriteriaInEntity keikaku2ServiceYobiInfoByCriteriaInEntity = new Keikaku2ServiceYobiInfoByCriteriaInEntity();
                    // 計画書2行データID
                    keikaku2ServiceYobiInfoByCriteriaInEntity.setAlKs22Id(cpncCks22OutEntity.getKs22Id());
                    // 計画書2ID
                    keikaku2ServiceYobiInfoByCriteriaInEntity.setAlKs21Id(cpncCks22OutEntity.getKs21Id());
                    // ①-1. 計画書（２）サービス曜日の取得
                    List<Keikaku2ServiceYobiInfoOutEntity> keikaku2ServiceYobiInfoOutList = cpnTucCks221SelectMapper
                            .findKeikaku2ServiceYobiInfoByCriteria(keikaku2ServiceYobiInfoByCriteriaInEntity);
                    // ①-1-1. 変数.居宅開始時間の設定
                    // ・上記取得した居宅開始時間がNULLの場合、"00:00"を設定する
                    if (keikaku2ServiceYobiInfoOutList.getFirst().getKaishiJikan() == null) {
                        startTime = CommonConstants.SV_JI_KAN_0000;
                    } else {
                        startTime = keikaku2ServiceYobiInfoOutList.getFirst().getKaishiJikan();
                    }
                    // ①-1-2. 変数.居宅終了時間の設定
                    // ・上記取得した居宅終了時間がNULLの場合、"00:00"を設定する
                    if (keikaku2ServiceYobiInfoOutList.getFirst().getShuuryouJikan() == null) {
                        endTime = CommonConstants.SV_JI_KAN_0000;
                    } else {
                        endTime = keikaku2ServiceYobiInfoOutList.getFirst().getShuuryouJikan();
                    }
                    // INPUT情報
                    Keikaku2TantouInfoByCriteriaInEntity keikaku2TantouInfoByCriteriaInEntity = new Keikaku2TantouInfoByCriteriaInEntity();
                    // 計画書2行データID
                    keikaku2TantouInfoByCriteriaInEntity.setAlKs22Id(cpncCks22OutEntity.getKs22Id());
                    // 計画書2ID
                    keikaku2TantouInfoByCriteriaInEntity.setAlKs21Id(cpncCks22OutEntity.getKs21Id());
                    // ①-2. 計画書（２）担当者の取得
                    List<Keikaku2TantouInfoOutEntity> keikaku2TantouInfoOutList = cpnTucCks222SelectMapper
                            .findKeikaku2TantouInfoByCriteria(keikaku2TantouInfoByCriteriaInEntity);
                    // ①-3. 上記①-2で取得した 計画書（２）担当者の件数が1件以上の場合、かつ
                    // (変数.居宅開始時間が"00：00"以外の場合、または変数.居宅終了時間が"00：00"以外の場合、または①-1で取得した計画書（２）曜日.曜日が”0000000”以外の場合)
                    if (CollectionUtils.size(keikaku2TantouInfoOutList) >= 1 && (!CommonConstants.SV_JI_KAN_0000
                            .equals(startTime) || !CommonConstants.SV_JI_KAN_0000.equals(endTime)
                            || !CommonConstants.YOUBI_0.equals(keikaku2ServiceYobiInfoOutList.getFirst().getYoubi()))) {
                        // ・変数.計画書リスト ＝ 上記1.1.4で取得した計画書（２）リスト
                        Gui01038CarePlan plan = new Gui01038CarePlan();
                        // サービス種
                        plan.setSvShuKnj(cpncCks22OutEntity.getSvShuKnj());
                        // サービス事業所名
                        plan.setJigyoNameKnj(cpncCks22OutEntity.getJigyoNameKnj());
                        // 介護
                        plan.setKaigoKnj(cpncCks22OutEntity.getKaigoKnj());
                        planList.add(plan);
                        // ・変数.サービスリスト ＝ 上記①-1で取得した計画書（２）サービス曜日
                        for (Keikaku2ServiceYobiInfoOutEntity keikaku2ServiceYobiInfoOut : keikaku2ServiceYobiInfoOutList) {
                            Gui01038Service service = new Gui01038Service();
                            // 詳細ID
                            service.setSyosaiId(CommonDtoUtil.objValToString(keikaku2ServiceYobiInfoOut.getKs21Id()));
                            // サービス項目（台帳）
                            service.setSvItemCd(CommonDtoUtil.objValToString(keikaku2ServiceYobiInfoOut.getSvItemCd()));
                            // サービス事業者CD
                            service.setSvJigyoId(
                                    CommonDtoUtil.objValToString(keikaku2ServiceYobiInfoOut.getSvJigyoId()));
                            // 開始時間
                            service.setKaishiJikan(keikaku2ServiceYobiInfoOut.getKaishiJikan());
                            // 終了時間
                            service.setShuuryouJikan(keikaku2ServiceYobiInfoOut.getShuuryouJikan());
                            // 加算フラグ
                            service.setKasanFlg(CommonDtoUtil.objValToString(keikaku2ServiceYobiInfoOut.getKasanFlg()));
                            // サービス種類CD
                            service.setSvShuruiCd(keikaku2ServiceYobiInfoOut.getSvShuruiCd());
                            // 福祉用具貸与の単価
                            service.setSvTani(CommonDtoUtil.objValToString(keikaku2ServiceYobiInfoOut.getTanka()));
                            // 福祉用具貸与マスタID
                            service.setFygId(CommonDtoUtil.objValToString(keikaku2ServiceYobiInfoOut.getFygId()));
                            // 曜日
                            service.setYoubi(keikaku2ServiceYobiInfoOut.getYoubi());
                            // 週単位以外のｻｰﾋﾞｽ区分
                            service.setIgaiKbn(CommonDtoUtil.objValToString(keikaku2ServiceYobiInfoOut.getIgaiKbn()));
                            // 週単位以外のｻｰﾋﾞｽ（日付指定）
                            service.setIgaiDate(keikaku2ServiceYobiInfoOut.getIgaiDate());
                            // 週単位以外のｻｰﾋﾞｽ（曜日指定）
                            service.setIgaiWeek(keikaku2ServiceYobiInfoOut.getIgaiWeek());
                            serviceList.add(service);
                        }
                        // ・変数.担当者リスト ＝ 上記①-2で取得した計画書（２）担当者情報
                        userList = keikaku2TantouInfoOutList;
                    }
                }
            }
        } else {
            // 1.2. 上記以外の場合
            // 1.2.1. リクエストパラメータ.事業所CDが"50010 (予防介護支援)"の場合
            if (CommonConstants.DEF_SV_JIGYO_CD_50010.equals(inDto.getDefSvJigyoCd())) {
                // 変数.居宅開始時間
                String startTime = StringUtils.EMPTY;
                // 変数.居宅終了時間
                String endTime = StringUtils.EMPTY;
                // 変数.曜日
                String youbi = StringUtils.EMPTY;
                // INPUT情報
                KyccTucPlan11ByCriteriaInEntity kyccTucPlan11ByCriteriaInEntity = new KyccTucPlan11ByCriteriaInEntity();
                // 計画期間ID
                kyccTucPlan11ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
                // 委託先事業者ID
                kyccTucPlan11ByCriteriaInEntity.setAlSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                // 利用者ID
                kyccTucPlan11ByCriteriaInEntity.setAlUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
                // 1.2.1.1. 計画書（２）の最新履歴情報を取得する
                List<KyccTucPlan11OutEntity> kyccTucPlan11OutList = kycTucPlan11SelectMapper
                        .findKyccTucPlan11ByCriteria(kyccTucPlan11ByCriteriaInEntity);
                // 1.2.1.2. リクエストパラメータ.現在有効期間IDが上記1.2.1.1で取得した最新履歴情報の1件目.有効期間IDより小さい場合
                // またはリクエストパラメータ.現在有効期間IDが上記1.2.1.1で取得した最新履歴情報の1件目.有効期間ID＋1より大きい場合
                if (CommonDtoUtil.strValToInt(inDto.getTermid()) < kyccTucPlan11OutList.getFirst().getTermid()
                        || CommonDtoUtil.strValToInt(inDto.getTermid()) > kyccTucPlan11OutList.getFirst().getTermid()
                                + 1) {
                    // ・初期取込処理終了とする。
                    return;
                } else {
                    // 1.2.1.3. 上記以外の場合
                    // ・変数.履歴ID ＝ 上記1.2.1.1で取得した最新履歴情報の1件目.計画表ID
                    historyId = kyccTucPlan11OutList.getFirst().getPlan11Id();
                    // ・変数.取込有効期間ID ＝ 上記1.2.1.1で取得した最新履歴情報の1件目.有効期間ID
                    termid = kyccTucPlan11OutList.getFirst().getTermid();
                }
                // INPUT情報
                KaigoDataServiceInfoByCriteriaInEntity kaigoDataServiceInfoByCriteriaInEntity = new KaigoDataServiceInfoByCriteriaInEntity();
                // 計画表ID
                kaigoDataServiceInfoByCriteriaInEntity.setAlPlan11Id(historyId);
                // *******. サービスリストの取得
                List<KaigoDataServiceInfoOutEntity> KaigoDataServiceInfoByCriteriaOutList = kycTucPlan15SelectMapper
                        .findKaigoDataServiceInfoByCriteria(kaigoDataServiceInfoByCriteriaInEntity);
                // ①. 変数.居宅開始時間の設定
                // ・上記取得した居宅開始時間がNULL、"77:77"(加算サービス)、"88:88"（短期サービス）、"99:99"（福祉用具貸与系サービス）の場合、
                if (KaigoDataServiceInfoByCriteriaOutList.getFirst().getKaishiJikan() == null
                        || CommonConstants.KAISHI_JIKAN_7
                                .equals(KaigoDataServiceInfoByCriteriaOutList.getFirst().getKaishiJikan())
                        || CommonConstants.KAISHI_JIKAN_8
                                .equals(KaigoDataServiceInfoByCriteriaOutList.getFirst().getKaishiJikan())
                        || CommonConstants.KAISHI_JIKAN_9
                                .equals(KaigoDataServiceInfoByCriteriaOutList.getFirst().getKaishiJikan())) {
                    // "00:00"を設定する
                } else {
                    // ・上記以外の場合、上記取得した居宅開始時間を設定する
                    startTime = KaigoDataServiceInfoByCriteriaOutList.getFirst().getKaishiJikan();
                }
                // ②. 変数.居宅終了時間の設定
                // ・上記取得した居宅終了時間がNULL、"77:77"(加算サービス)、"88:88"（短期サービス）、"99:99"（福祉用具貸与系サービス）の場合、
                if (KaigoDataServiceInfoByCriteriaOutList.getFirst().getShuuryouJikan() == null
                        || CommonConstants.SHUURYOU_JIKAN_7
                                .equals(KaigoDataServiceInfoByCriteriaOutList.getFirst().getShuuryouJikan())
                        || CommonConstants.SHUURYOU_JIKAN_8
                                .equals(KaigoDataServiceInfoByCriteriaOutList.getFirst().getShuuryouJikan())
                        || CommonConstants.SHUURYOU_JIKAN_9
                                .equals(KaigoDataServiceInfoByCriteriaOutList.getFirst().getShuuryouJikan())) {
                    // "00:00"を設定する
                } else {
                    // ・上記以外の場合、上記取得した居宅終了時間を設定する
                    endTime = KaigoDataServiceInfoByCriteriaOutList.getFirst().getShuuryouJikan();
                }
                // ③. 変数.曜日の設定
                // ・上記取得した曜日がNULL、”0000000”の場合、
                if (KaigoDataServiceInfoByCriteriaOutList.getFirst().getYoubi() == null ||
                        CommonConstants.YOUBI_0
                                .equals(KaigoDataServiceInfoByCriteriaOutList.getFirst().getYoubi())) {
                    // ""を設定する
                } else {
                    // ・上記以外の場合、上記取得した曜日を設定する
                    youbi = KaigoDataServiceInfoByCriteriaOutList.getFirst().getYoubi();
                }

                // 1.2.1.5. 上記*******で取得したサービスリストをループして、日月情報の取得する
                // ①. サービスリスト.計画表IDがNULL以外の場合
                List<KaigoServiceDataMdOutEntity> kaigoServiceDataMdOutList = new ArrayList<>();
                if (KaigoDataServiceInfoByCriteriaOutList.getFirst().getPlan11Id() != null) {
                    // INPUT情報
                    KaigoServiceDataMdByCriteriaInEntity kaigoServiceDataMdByCriteriaInEntity = new KaigoServiceDataMdByCriteriaInEntity();
                    // 計画表ID
                    kaigoServiceDataMdByCriteriaInEntity
                            .setAiPlan11Id(KaigoDataServiceInfoByCriteriaOutList.getFirst().getPlan11Id());
                    kaigoServiceDataMdOutList = kycTucPlan16SelectMapper
                            .findKaigoServiceDataMdByCriteria(kaigoServiceDataMdByCriteriaInEntity);
                }
                // 引数リストの編集を行う
                // ①. 変数.居宅開始時間が"00：00"以外の場合、または変数.居宅終了時間が"00：00"以外、または変数.曜日が""以外の場合
                if (!CommonConstants.SV_JI_KAN_0000.equals(startTime) || !CommonConstants.SV_JI_KAN_0000.equals(endTime)
                        || !CommonConstants.BLANK_STRING.equals(youbi)) {
                    // ・変数.サービスリスト ＝ 上記*******で取得したサービスリスト
                    for (KaigoDataServiceInfoOutEntity kaigoDataServiceInfoByCriteriaOutEntity : KaigoDataServiceInfoByCriteriaOutList) {
                        Gui01038Service service = new Gui01038Service();
                        // 詳細ID
                        service.setSyosaiId(
                                CommonDtoUtil.objValToString(kaigoDataServiceInfoByCriteriaOutEntity.getPlan11Id()));
                        // サービス項目（台帳）
                        service.setSvItemCd(
                                CommonDtoUtil.objValToString(kaigoDataServiceInfoByCriteriaOutEntity.getSvItemCd()));
                        // サービス事業者CD
                        service.setSvJigyoId(
                                CommonDtoUtil.objValToString(kaigoDataServiceInfoByCriteriaOutEntity.getSvJigyoId()));
                        // 開始時間
                        service.setKaishiJikan(kaigoDataServiceInfoByCriteriaOutEntity.getKaishiJikan());
                        // 終了時間
                        service.setShuuryouJikan(kaigoDataServiceInfoByCriteriaOutEntity.getShuuryouJikan());
                        // 加算フラグ
                        service.setKasanFlg(
                                CommonDtoUtil.objValToString(kaigoDataServiceInfoByCriteriaOutEntity.getKasanFlg()));
                        // サービス種類CD
                        service.setSvShuruiCd(kaigoDataServiceInfoByCriteriaOutEntity.getSvShuruiCd());
                        // 福祉用具貸与の単価
                        service.setSvTani(
                                CommonDtoUtil.objValToString(kaigoDataServiceInfoByCriteriaOutEntity.getTanka()));
                        // 福祉用具貸与マスタID
                        service.setFygId(
                                CommonDtoUtil.objValToString(kaigoDataServiceInfoByCriteriaOutEntity.getFygId()));
                        // 曜日
                        service.setYoubi(kaigoDataServiceInfoByCriteriaOutEntity.getYoubi());
                        // 週単位以外のｻｰﾋﾞｽ区分
                        service.setIgaiKbn(
                                CommonDtoUtil.objValToString(kaigoDataServiceInfoByCriteriaOutEntity.getIgaiKbn()));
                        // 週単位以外のｻｰﾋﾞｽ（日付指定）
                        service.setIgaiDate(kaigoDataServiceInfoByCriteriaOutEntity.getIgaiDate());
                        // 週単位以外のｻｰﾋﾞｽ（曜日指定）
                        service.setIgaiWeek(kaigoDataServiceInfoByCriteriaOutEntity.getIgaiWeek());
                        serviceList.add(service);
                    }
                    // ・変数.月日リスト ＝ 上記1.2.1.5で取得した日月情報
                    for (KaigoServiceDataMdOutEntity kaigoServiceDataMdOut : kaigoServiceDataMdOutList) {
                        Gui01038YearMonth monthDay = new Gui01038YearMonth();
                        // 詳細ID
                        monthDay.setSyosaiId(CommonDtoUtil.objValToString(kaigoServiceDataMdOut.getPlan15Id()));
                        // 開始日
                        monthDay.setStartYmd(kaigoServiceDataMdOut.getStartYmd());
                        // 終了日
                        monthDay.setEndYmd(kaigoServiceDataMdOut.getEndYmd());
                        monthDayList.add(monthDay);
                    }
                }
            } else {
                // 1.2.2. 上記以外の場合
                // 変数.居宅開始時間
                String startTime;
                // 変数.居宅終了時間
                String endTime;
                // 変数.曜日
                String youbi;
                // INPUT情報
                Keikaku2HeadInfoListByCriteriaInEntity keikakushoHeadByCriteriaInEntity = new Keikaku2HeadInfoListByCriteriaInEntity();
                // 計画期間ID
                keikakushoHeadByCriteriaInEntity.setSc1(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
                // 事業者ID
                keikakushoHeadByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                // 利用者ID
                keikakushoHeadByCriteriaInEntity.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
                // 1.2.2.1. 計画書（２）の最新履歴情報を取得する
                List<Keikaku2HeadInfoListOutEntity> keikakushoHeadOutList = cpnTucCks21SelectMapper
                        .findKeikaku2HeadInfoListByCriteria(keikakushoHeadByCriteriaInEntity);
                // 1.2.2.2. リクエストパラメータ.現在有効期間IDが上記1.2.2.1で取得した最新履歴情報の1件目.有効期間IDより小さい場合
                // またはリクエストパラメータ.現在有効期間IDが上記1.2.2.1で取得した最新履歴情報の1件目.有効期間ID＋1より大きい場合
                if (CommonDtoUtil.strValToInt(inDto.getTermid()) < keikakushoHeadOutList.getFirst().getTermid()
                        || CommonDtoUtil.strValToInt(inDto.getTermid()) > keikakushoHeadOutList.getFirst().getTermid()
                                + 1) {
                    return;
                } else {
                    // 1.2.2.3. 上記以外の場合
                    // ・変数.履歴ID ＝ 上記1.2.2.1で取得した最新履歴情報の1件目.計画書（２）ID
                    historyId = keikakushoHeadOutList.getFirst().getKs21Id();
                    // ・変数.取込有効期間ID ＝ 上記1.2.2.1で取得した最新履歴情報の1件目.有効期間ID
                    termid = keikakushoHeadOutList.getFirst().getTermid();
                }
                // INPUT情報
                Keikaku2ServiceInfoDmyByCriteriaInEntity keikaku2ServiceInfoDmyByCriteriaInEntity = new Keikaku2ServiceInfoDmyByCriteriaInEntity();
                // 計画書2ID
                keikaku2ServiceInfoDmyByCriteriaInEntity.setAlKs21Id(historyId);
                // 1.2.2.4. サービスリストの取得
                List<Keikaku2ServiceInfoDmyOutEntity> keikaku2ServiceInfoDmyOutList = cpnTucCks211SelectMapper
                        .findKeikaku2ServiceInfoDmyByCriteria(keikaku2ServiceInfoDmyByCriteriaInEntity);
                // ①. 変数.居宅開始時間の設定
                // ・上記取得した居宅開始時間がNULL、"77:77"(加算サービス)、"88:88"（短期サービス）、"99:99"（福祉用具貸与系サービス）の場合、
                if (keikaku2ServiceInfoDmyOutList.getFirst().getKaishiJikan() == null
                        || CommonConstants.KAISHI_JIKAN_7
                                .equals(keikaku2ServiceInfoDmyOutList.getFirst().getKaishiJikan())
                        || CommonConstants.KAISHI_JIKAN_8
                                .equals(keikaku2ServiceInfoDmyOutList.getFirst().getKaishiJikan())
                        || CommonConstants.KAISHI_JIKAN_9
                                .equals(keikaku2ServiceInfoDmyOutList.getFirst().getKaishiJikan())) {
                    // "00:00"を設定する
                    startTime = CommonConstants.SV_JI_KAN_0000;
                } else {
                    // ・上記以外の場合、上記取得した居宅開始時間を設定する
                    startTime = keikaku2ServiceInfoDmyOutList.getFirst().getKaishiJikan();
                }
                // ②. 変数.居宅終了時間の設定
                // ・上記取得した居宅終了時間がNULL、"77:77"(加算サービス)、"88:88"（短期サービス）、"99:99"（福祉用具貸与系サービス）の場合、
                if (keikaku2ServiceInfoDmyOutList.getFirst().getShuuryouJikan() == null
                        || CommonConstants.SHUURYOU_JIKAN_7
                                .equals(keikaku2ServiceInfoDmyOutList.getFirst().getShuuryouJikan())
                        || CommonConstants.SHUURYOU_JIKAN_8
                                .equals(keikaku2ServiceInfoDmyOutList.getFirst().getShuuryouJikan())
                        || CommonConstants.SHUURYOU_JIKAN_9
                                .equals(keikaku2ServiceInfoDmyOutList.getFirst().getShuuryouJikan())) {
                    // "00:00"を設定する
                    endTime = CommonConstants.SV_JI_KAN_0000;
                } else {
                    // ・上記以外の場合、上記取得した居宅終了時間を設定する
                    endTime = keikaku2ServiceInfoDmyOutList.getFirst().getShuuryouJikan();
                }
                // ③. 変数.曜日の設定
                // ・上記取得した曜日がNULL、”0000000”の場合、
                if (keikaku2ServiceInfoDmyOutList.getFirst().getYoubi() == null
                        || CommonConstants.YOUBI_0.equals(keikaku2ServiceInfoDmyOutList.getFirst().getYoubi())) {
                    // ""を設定する
                    youbi = CommonConstants.BLANK_STRING;
                } else {
                    // ・上記以外の場合、上記取得した曜日を設定する
                    youbi = keikaku2ServiceInfoDmyOutList.getFirst().getYoubi();
                }
                List<Keikaku2TukihiInfoOutEntity> keikaku2TukihiInfoOutList = new ArrayList<>();
                // 1.2.2.5. 上記1.2.2.4で取得したサービスリストをループして、日月情報の取得する
                for (Keikaku2ServiceInfoDmyOutEntity keikaku2ServiceInfoDmyOutEntity : keikaku2ServiceInfoDmyOutList) {
                    // ①. サービスリスト.計画書（２）IDがNULL以外の場合
                    if (keikaku2ServiceInfoDmyOutEntity.getKs21Id() != null) {
                        // INPUT情報
                        Keikaku2TukihiInfoByCriteriaInEntity keikaku2TukihiInfoByCriteriaInEntity = new Keikaku2TukihiInfoByCriteriaInEntity();
                        // 計画書2ID
                        keikaku2TukihiInfoByCriteriaInEntity.setAiKs21Id(keikaku2ServiceInfoDmyOutEntity.getKs21Id());
                        keikaku2TukihiInfoOutList = cpnTucCks212SelectMapper
                                .findKeikaku2TukihiInfoByCriteria(keikaku2TukihiInfoByCriteriaInEntity);
                    }
                }
                // 1.2.3. 引数リストの編集を行う
                // ①. 変数.居宅開始時間が"00：00"以外の場合、または変数.居宅終了時間が"00：00"以外、または変数.曜日が""以外の場合
                if (!CommonConstants.SV_JI_KAN_0000.equals(startTime) || !CommonConstants.SV_JI_KAN_0000.equals(endTime)
                        || !CommonConstants.BLANK_STRING.equals(youbi)) {
                    // ・変数.サービスリスト ＝ 上記1.2.2.4で取得したサービスリスト
                    for (Keikaku2ServiceInfoDmyOutEntity keikaku2ServiceInfoDmyOutEntity : keikaku2ServiceInfoDmyOutList) {
                        Gui01038Service service = new Gui01038Service();
                        // 詳細ID
                        service.setSyosaiId(CommonDtoUtil.objValToString(keikaku2ServiceInfoDmyOutEntity.getKs21Id()));
                        // サービス項目（台帳）
                        service.setSvItemCd(
                                CommonDtoUtil.objValToString(keikaku2ServiceInfoDmyOutEntity.getSvItemCd()));
                        // サービス事業者CD
                        service.setSvJigyoId(
                                CommonDtoUtil.objValToString(keikaku2ServiceInfoDmyOutEntity.getSvJigyoId()));
                        // 開始時間
                        service.setKaishiJikan(keikaku2ServiceInfoDmyOutEntity.getKaishiJikan());
                        // 終了時間
                        service.setShuuryouJikan(keikaku2ServiceInfoDmyOutEntity.getShuuryouJikan());
                        // 加算フラグ
                        service.setKasanFlg(
                                CommonDtoUtil.objValToString(keikaku2ServiceInfoDmyOutEntity.getKasanFlg()));
                        // サービス種類CD
                        service.setSvShuruiCd(keikaku2ServiceInfoDmyOutEntity.getSvShuruiCd());
                        // 福祉用具貸与の単価
                        service.setSvTani(CommonDtoUtil.objValToString(keikaku2ServiceInfoDmyOutEntity.getTanka()));
                        // 福祉用具貸与マスタID
                        service.setFygId(CommonDtoUtil.objValToString(keikaku2ServiceInfoDmyOutEntity.getFygId()));
                        // 曜日
                        service.setYoubi(keikaku2ServiceInfoDmyOutEntity.getYoubi());
                        // 週単位以外のｻｰﾋﾞｽ区分
                        service.setIgaiKbn(CommonDtoUtil.objValToString(keikaku2ServiceInfoDmyOutEntity.getIgaiKbn()));
                        // 週単位以外のｻｰﾋﾞｽ（日付指定）
                        service.setIgaiDate(keikaku2ServiceInfoDmyOutEntity.getIgaiDate());
                        // 週単位以外のｻｰﾋﾞｽ（曜日指定）
                        service.setIgaiWeek(keikaku2ServiceInfoDmyOutEntity.getIgaiWeek());
                        serviceList.add(service);
                    }
                    // ・変数.月日リスト ＝ 上記1.2.2.5で取得した日月情報
                    for (Keikaku2TukihiInfoOutEntity keikaku2TukihiInfoOutEntity : keikaku2TukihiInfoOutList) {
                        Gui01038YearMonth monthDay = new Gui01038YearMonth();
                        // 詳細ID
                        monthDay.setSyosaiId(CommonDtoUtil.objValToString(keikaku2TukihiInfoOutEntity.getKs211Id()));
                        // 開始日
                        monthDay.setStartYmd(keikaku2TukihiInfoOutEntity.getStartYmd());
                        // 終了日
                        monthDay.setEndYmd(keikaku2TukihiInfoOutEntity.getEndYmd());
                        monthDayList.add(monthDay);
                    }
                }
            }
        }
        // 2.
        // 変数.サービスリストがNULL以外の場合、
        // または変数.月日リストがNULL以外の場合、
        // または変数.計画書リストがNULL以外の場合、
        // または変数.担当者リストがNULL以外の場合、
        // 取込データの編集を行う
        if (CollectionUtils.isEmpty(serviceList) || CollectionUtils.isEmpty(monthDayList)
                || CollectionUtils.isEmpty(planList) || CollectionUtils.isEmpty(userList)) {
            // 2.1. リクエストパラメータ.計画書様式が"1 (施設)"の場合
            if (CommonConstants.CKS_FLG_FACILITY.equals(inDto.getCksFlg())) {
                List<Gui01038Cks52> cks52List = new ArrayList<>();
                for (Gui01038Service service : serviceList) {
                    Gui01038Cks52 cks52 = new Gui01038Cks52();
                    // 2.1.1. 時間処理
                    // 2.1.1.1. 変数.サービスリスト.居宅開始時間がNULL、"77:77"、"88:88"、"99:99" の場合
                    if (service.getKaishiJikan() == null
                            || CommonConstants.KAISHI_JIKAN_7.equals(service.getKaishiJikan())
                            || CommonConstants.KAISHI_JIKAN_8.equals(service.getKaishiJikan())
                            || CommonConstants.KAISHI_JIKAN_9.equals(service.getKaishiJikan())) {
                        // OUTPUT情報. 週間計画詳細リスト.開始時間 = "00:00"
                        cks52.setKaishiJikan(CommonConstants.SV_JI_KAN_0000);
                    }
                    // 2.1.1.2. 変数.サービスリスト.居宅終了時間がNULL、"77:77"、"88:88"、"99:99" の場合
                    if (service.getShuuryouJikan() == null
                            || CommonConstants.SHUURYOU_JIKAN_7.equals(service.getShuuryouJikan())
                            || CommonConstants.SHUURYOU_JIKAN_8.equals(service.getShuuryouJikan())
                            || CommonConstants.SHUURYOU_JIKAN_9.equals(service.getShuuryouJikan())) {
                        // OUTPUT情報. 週間計画詳細リスト.終了時間 = "00:00"
                        cks52.setShuuryouJikan(CommonConstants.SV_JI_KAN_0000);
                    }
                    // 2.1.1.3. 変数.サービスリスト.居宅開始時間が"00:00"の場合、かつ変数.サービスリスト.居宅終了時間が"00:00" の場合
                    if (CommonConstants.SV_JI_KAN_0000.equals(service.getKaishiJikan())
                            && CommonConstants.SV_JI_KAN_0000.equals(service.getShuuryouJikan())) {
                        // OUTPUT情報. 週間計画詳細リスト.開始時間 = "04:00"
                        cks52.setKaishiJikan(CommonConstants.SV_JI_KAN_0400);
                        // OUTPUT情報. 週間計画詳細リスト.終了時間 = "05:00"
                        cks52.setShuuryouJikan(CommonConstants.SV_JI_KAN_0500);
                    }
                    // 2.1.2. 曜日と週単位以外処理
                    // 2.1.2.1. 曜日の設定
                    // ・変数.サービスリスト.曜日がNULL、"0000000"の場合
                    if (service.getYoubi() == null || service.getYoubi().equals(CommonConstants.YOUBI_0)) {
                        // OUTPUT情報.週間計画詳細リスト.曜日 ＝ 1111111（月～日）
                        cks52.setYoubi(CommonConstants.YOUBI_1);
                    } else {
                        // ・上記以外場合
                        // OUTPUT情報.週間計画詳細リスト.曜日 ＝ 変数.サービスリスト.曜日
                        cks52.setYoubi(service.getYoubi());
                    }
                    // 2.1.2.2. 週単位以外の場合
                    // ・変数.サービスリスト.曜日が"9999999"以外の場合
                    if (!service.getYoubi().equals(CommonConstants.YOUBI_9)) {
                        // ・OUTPUT情報. 週間計画詳細リスト.週単位以外のｻｰﾋﾞｽ区分 ＝ 変数.サービスリスト.週単位以外のｻｰﾋﾞｽ区分
                        cks52.setIgaiKbn(service.getIgaiKbn());
                        // ・OUTPUT情報. 週間計画詳細リスト.週単位以外のｻｰﾋﾞｽ（日付指定） ＝ 変数.サービスリスト.週単位以外のｻｰﾋﾞｽ（日付指定）
                        cks52.setIgaiDate(service.getIgaiDate());
                        // ・OUTPUT情報. 週間計画詳細リスト.週単位以外のｻｰﾋﾞｽ（曜日指定） ＝ 変数.サービスリスト.週単位以外のｻｰﾋﾞｽ（曜日指定）
                        cks52.setIgaiWeek(service.getIgaiWeek());
                        // ・変数.サービスリスト.週単位以外のｻｰﾋﾞｽ区分が1の場合
                        if (CommonConstants.IGAI_KBN_STR_ONE.equals(service.getIgaiKbn())) {
                            // ・下記関数「f_cpn_week_date_to_moji」をよびだし、戻り値を変数.週単位以外文字に設定する
                            String lsDate = kghKrkZCpnFuncLogic.getWeekDateToMoji(service.getIgaiDate());
                            // ・OUTPUT情報.週間計画詳細リスト.週単位以外文字 ＝ 変数.週単位以外文字
                            cks52.setIgaiMoji(lsDate);
                        } else if (CommonConstants.IGAI_KBN_STR_TWO.equals(service.getIgaiKbn())) {
                            // ・変数.サービスリスト.週単位以外のｻｰﾋﾞｽ区分が2の場合
                            // ① 下記関数「f_cpn_week_week_to_moji」を呼び出し、変数.週単位以外文字を戻り値に設定する
                            String lsWeek = kghKrkZCpnFuncLogic.getWeekToMoji(service.getIgaiWeek());
                            // ・OUTPUT情報.週間計画詳細リスト.週単位以外文字 ＝ 変数.週単位以外文字
                            cks52.setIgaiMoji(lsWeek);
                        }
                    }
                    // 2.1.2.3. 週間計画担当者リストの設定を行う
                    // ・OUTPUT情報. 週間計画詳細リスト.内容 ＝ 変数.計画書リスト.介護
                    cks52.setNaiyouKnj(planList.getFirst().getKaigoKnj());
                    // ・変数.担当者リストがNULL以外の場合、なたは変数.担当者リストが空以外の場合
                    if (userList != null && !CollectionUtils.isEmpty(userList)) {
                        // INPUT情報
                        Cks56InfoByCriteriaInEntity cks56InfoByCriteriaInEntity = new Cks56InfoByCriteriaInEntity();
                        // 週間計画ID
                        cks56InfoByCriteriaInEntity.setKs51Id(CommonDtoUtil.strValToInt(inDto.getKs51Id()));
                        // ・週間計画表（担当者）情報の取得する
                        List<Cks56InfoOutEntity> cks56InfoOutList = cpnTucCks56SelectMapper
                                .findCks56InfoByCriteria(cks56InfoByCriteriaInEntity);
                        // 週間計画担当者リスト
                        List<Gui01038Cks56> cks56List = new ArrayList<>();
                        for (Cks56InfoOutEntity cks56InfoOutEntity : cks56InfoOutList) {
                            Gui01038Cks56 gui01038Cks56 = new Gui01038Cks56();
                            // 担当者名称
                            gui01038Cks56
                                    .setShokushuKnj(CommonDtoUtil.objValToString(cks56InfoOutEntity.getRyakuKnj()));
                            // 職種
                            gui01038Cks56
                                    .setShokushuId(CommonDtoUtil.objValToString(cks56InfoOutEntity.getShokushuId()));
                            cks56List.add(gui01038Cks56);
                        }
                    }
                    cks52List.add(cks52);
                }
                outDto.setCks52List(cks52List);
            } else if (CommonConstants.CKS_FLG_HOME.equals(inDto.getCksFlg())) {
                // 2.2. リクエストパラメータ.計画書様式が"2 (居宅)"の場合
                List<Gui01038Cks52> cks52List = new ArrayList<>();
                // 2.2.1. リクエストパラメータ.Sys略称が”CMN”の場合
                if (CommonConstants.SYS_KNJ_CMN.equals(inDto.getSys3Ryaku())) {
                    // 週間計画計取込情報(居宅)取得する
                    // 「API定義書_APINo(523)_取込情報編集.xlsx」の「API定義1」シート
                    cks52List = weekPlanImportInfoEditSelectServiceImpl.api1DataExProc(serviceList,
                            monthDayList, new ArrayList<>(),
                            termid,
                            CommonDtoUtil.strValToInt(inDto.getTermid()), inDto.getTougaiYm());

                } else {
                    // 週間計画計取込情報(居宅)取得する
                    // 「API定義書_APINo(523)_取込情報編集.xlsx」の「API定義1」シート
                    cks52List = weekPlanImportInfoEditSelectServiceImpl.api1DataExProc(serviceList, new ArrayList<>(),
                            planList,
                            termid,
                            CommonDtoUtil.strValToInt(inDto.getTermid()), inDto.getTougaiYm());
                }
                outDto.setCks52List(cks52List);
            }
            // 2.3. 記録との連携情報リスト取得
            // 2.3.1. 記録との連携情報を取得する。
            CpnTucCks224InfoByCriteriaInEntity cksInDto = new CpnTucCks224InfoByCriteriaInEntity();
            // 計画書（２）ID 変数.履歴ID
            cksInDto.setKs21Id(historyId);
            List<CpnTucCks224InfoOutEntity> cksOutList = cpnTucCks224SelectMapper
                    .findCpnTucCks224InfoByCriteria(cksInDto);
            // 2.3.2. 上記で取得した記録との連携情報リストと変数.サービスリストとマッピングしたデータをレスポンスパラメータ.連動項目リストにセットする。
            if (CollectionUtils.isNotEmpty(outDto.getCks52List()) && CollectionUtils.isNotEmpty(cksOutList)) {
                // ※マッピングキー：計画書（２）行データID／カウンター、計画書（２）ID／計画書（２）ID
                for (CpnTucCks224InfoOutEntity cksOutInfo : cksOutList) {
                    String cksKey = StringUtils.join(CommonConstants.STR_POUND_KEY, cksOutInfo.getKs22Id(),
                            cksOutInfo.getKs21Id());
                    for (Gui01038Cks52 cks52 : outDto.getCks52List()) {
                        String cks52Key = StringUtils.join(CommonConstants.STR_POUND_KEY, cks52.getKs52Id(),
                                cks52.getKs51Id());
                        if (cksKey.equals(cks52Key)) {
                            Gui01038Cks59 cks59 = new Gui01038Cks59();
                            // 連動項目データID
                            cks59.setKs59Id(CommonDtoUtil.objValToString(cksOutInfo.getKs224Id()));
                            // 詳細データID
                            cks59.setKs52Id(cks52.getKs52Id());
                            // 連動項目ID
                            cks59.setNikkaId(CommonDtoUtil.objValToString(cksOutInfo.getNikkaId()));
                            // 連動項目リスト
                            cks52.setCks59List(Arrays.asList(cks59));
                        }
                    }
                }
            }
        }
    }

    /**
     * API定義5
     * 
     * @param inDto  週間計画詳細リスト取得の入力Dto
     * @param outDto 週間計画詳細リスト取得の出力Dto
     * @throws Exception
     */
    private void api5DataExProc(WeekPlanDetailsListSelectServiceInDto inDto,
            WeekPlanDetailsListSelectServiceOutDto outDto) {
        // 1.単項目チェック以外の入力チェック
        // 2. 【週間計画詳細情報取得】データ情報を取得する
        // 2.1. 関数「f_cpn_week_termid2date」を呼び出し、ｻｰﾋﾞｽ検索の開始日とｻｰﾋﾞｽ検索の終了日を取得する
        TermId2DateLogicOutDto termId2DateLogicOutDto = kghKrkZCpnFuncLogic.getTermid2date(
                CommonDtoUtil.strValToInt(inDto.getSc1Id()), CommonConstants.BLANK_STRING,
                CommonConstants.BLANK_STRING);
        // 2.1.1.変数の設定
        // 変数.サービス検索の開始日 = 戻り値.ｻｰﾋﾞｽ検索の開始日
        String startDt = termId2DateLogicOutDto.getSYmd();
        // 変数.サービス検索の終了日 = 戻り値.ｻｰﾋﾞｽ検索の終了日
        String endDt = termId2DateLogicOutDto.getEYmd();
        // INPUT情報
        CpnWeekSvShurui1ByCriteriaInEntity svjigyoNameInfoByCriteriaInEntity = new CpnWeekSvShurui1ByCriteriaInEntity();
        // 2.2. サービス種類名称の取得
        List<CpnWeekSvShurui1OutEntity> svjigyoNameInfoOutList = comMscSvjigyoNameSelectMapper
                .findCpnWeekSvShurui1ByCriteria(svjigyoNameInfoByCriteriaInEntity);
        // INPUT情報
        KghCmnSvselSelJigyoByCriteriaInEntity kghCmnSvselSelJigyoByCriteriaInEntity = new KghCmnSvselSelJigyoByCriteriaInEntity();
        // 2.3. サービス事業名情報リストの取得
        List<KghCmnSvselSelJigyoOutEntity> kghCmnSvselSelJigyoOutList = cmnSvselSelJigyoSelectMapper
                .findKghCmnSvselSelJigyoByCriteria(kghCmnSvselSelJigyoByCriteriaInEntity);
        // 2.3.1. 上記取得したサービス事業名情報リストを繰り返する
        // 2.3.1.1. サービス事業名情報.★サービス種類コード→4-5が'51' ,'52' ,'53' ,'54' ,'43' , '46'の場合
        // ・このレコードを削除する
        kghCmnSvselSelJigyoOutList = kghCmnSvselSelJigyoOutList.stream()
                .filter(o -> CommonConstants.SV_KIND_LIST3.contains(o.getSvKindCd())).toList();
        // INPUT情報
        Cks52InfoForShisetuByCriteriaInEntity cks52InfoForShisetuByCriteriaInEntity = new Cks52InfoForShisetuByCriteriaInEntity();
        // 週間計画ID
        cks52InfoForShisetuByCriteriaInEntity.setKs51(CommonDtoUtil.strValToInt(inDto.getKs51Id()));
        // 2.4. 詳細データの取得する
        List<Cks52InfoForShisetuOutEntity> cks52InfoForShisetuOutList = cpnTucCks52SelectMapper
                .findCks52InfoForShisetuByCriteria(cks52InfoForShisetuByCriteriaInEntity);
        // ※戻り値をレスポンスパラメータ.週間計画詳細リストに設定する
        List<Gui01038Cks52> cks52List = new ArrayList<>();
        for (Cks52InfoForShisetuOutEntity cks52InfoForShisetuOutEntity : cks52InfoForShisetuOutList) {
            // レスポンスパラメータ.週間計画詳細
            Gui01038Cks52 gui01038Cks52 = new Gui01038Cks52();
            // ダミーデータID
            gui01038Cks52.setDmyKs52Id(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getDmyKs52Id()));
            // ダミーサービス項目
            gui01038Cks52.setDmySvItem(cks52InfoForShisetuOutEntity.getDmySvItem());
            // wN1
            gui01038Cks52.setWN1(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getWN1()));
            // wN2
            gui01038Cks52.setWN2(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getWN2()));
            // wN3
            gui01038Cks52.setWN3(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getWN3()));
            // wN4
            gui01038Cks52.setWN4(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getWN4()));
            // wN5
            gui01038Cks52.setWN5(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getWN5()));
            // wN6
            gui01038Cks52.setWN6(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getWN6()));
            // wN7
            gui01038Cks52.setWN7(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getWN7()));
            // wN8
            gui01038Cks52.setWN8(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getWN8()));
            // wN9
            gui01038Cks52.setWN9(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getWN9()));
            // ダミー頻度
            gui01038Cks52.setDmyHindo(cks52InfoForShisetuOutEntity.getDmyHindo());
            // 週間計画ID
            gui01038Cks52.setKs51Id(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getKs51Id()));
            // 詳細データID
            gui01038Cks52.setKs52Id(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getKs52Id()));
            // 曜日
            gui01038Cks52.setYoubi(cks52InfoForShisetuOutEntity.getYoubi());
            // 開始時間
            gui01038Cks52.setKaishiJikan(cks52InfoForShisetuOutEntity.getKaishiJikan());
            // 終了時間
            gui01038Cks52.setShuuryouJikan(cks52InfoForShisetuOutEntity.getShuuryouJikan());
            // 内容
            gui01038Cks52.setNaiyouKnj(cks52InfoForShisetuOutEntity.getNaiyouKnj());
            // 文字サイズ
            gui01038Cks52.setFontSize(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getFontSize()));
            // 表示モード
            gui01038Cks52.setDispMode(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getDispMode()));
            // 文字位置
            gui01038Cks52.setAlignment(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getAlignment()));
            // サービス種類
            gui01038Cks52.setSvShuruiCd(cks52InfoForShisetuOutEntity.getSvShuruiCd());
            // サービス項目（台帳）
            gui01038Cks52.setSvItemCd(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getSvItemCd()));
            // サービス事業者ID
            gui01038Cks52.setSvJigyoId(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getSvJigyoId()));
            // サービス種類名称
            gui01038Cks52.setSvShuruiKnj(svjigyoNameInfoOutList.getFirst().getRuakuKnj());
            // サービス事業者名称
            gui01038Cks52.setSvJigyoKnj(kghCmnSvselSelJigyoOutList.getFirst().getJigyoKnj());
            // サービス事業者略称
            gui01038Cks52.setSvJigyoRyoku(kghCmnSvselSelJigyoOutList.getFirst().getJigyoRyakuKnj());
            // 文字カラー
            gui01038Cks52.setFontColor(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getFontColor()));
            // 背景カラー
            gui01038Cks52.setBackColor(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getBackColor()));
            // 時間表示区分
            gui01038Cks52.setTimeKbn(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getTimeKbn()));
            // 週単位以外のｻｰﾋﾞｽ区分
            gui01038Cks52.setIgaiKbn(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getIgaiKbn()));
            // 週単位以外のｻｰﾋﾞｽ（日付指定）
            gui01038Cks52.setIgaiDate(cks52InfoForShisetuOutEntity.getIgaiDate());
            // 週単位以外のｻｰﾋﾞｽ（曜日指定）
            gui01038Cks52.setIgaiWeek(cks52InfoForShisetuOutEntity.getIgaiWeek());
            // 福祉用具貸与の単価
            gui01038Cks52.setSvTani(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getSvTani()));
            // 福祉用具貸与マスタID
            gui01038Cks52.setFygId(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getFygId()));
            // 枠外表示するかのフラグ
            gui01038Cks52.setWakugaiFlg(CommonDtoUtil.objValToString(cks52InfoForShisetuOutEntity.getWakugaiFlg()));
            cks52List.add(gui01038Cks52);
        }
        // 2.5. 上記2.4で取得した週間計画詳細情報の件数が0件以上の場合
        if (CollectionUtils.isNotEmpty(cks52InfoForShisetuOutList)) {
            // INPUT情報
            CpnWeekInputKasanByCriteriaInEntity cks55InfoByCriteriaInEntity = new CpnWeekInputKasanByCriteriaInEntity();
            // 週間計画ID
            cks55InfoByCriteriaInEntity.setKs51Id(CommonDtoUtil.strValToInt(inDto.getKs51Id()));
            // 2.5.1. 週間計画表（加算データ）情報の取得
            List<CpnWeekInputKasanOutEntity> cks55InfoOutList = cpnTucCks55SelectMapper
                    .findCpnWeekInputKasanByCriteria(cks55InfoByCriteriaInEntity);
            // ・変数.週間計画表（加算データ）情報に戻り値を設定する
            // INPUT情報
            Cks56InfoByCriteriaInEntity cks56InfoByCriteriaInEntity = new Cks56InfoByCriteriaInEntity();
            // 週間計画ID
            cks56InfoByCriteriaInEntity.setKs51Id(CommonDtoUtil.strValToInt(inDto.getKs51Id()));
            // 2.5.2. 週間計画表（担当者）情報の取得
            List<Cks56InfoOutEntity> cks56InfoOutList = cpnTucCks56SelectMapper
                    .findCks56InfoByCriteria(cks56InfoByCriteriaInEntity);
            // ・変数.週間計画表（担当者）情報に戻り値を設定する
            // INPUT情報
            Cks57InfoByCriteriaInEntity cks57InfoByCriteriaInEntity = new Cks57InfoByCriteriaInEntity();
            // 週間計画ID
            cks57InfoByCriteriaInEntity.setKs51Id(CommonDtoUtil.strValToInt(inDto.getKs51Id()));
            // 2.5.3. 週間計画（月日指定）情報の取得
            List<Cks57InfoOutEntity> cks57InfoOutList = cpnTucCks57SelectMapper
                    .findCks57InfoByCriteria(cks57InfoByCriteriaInEntity);
            // ・変数.週間計画（月日指定）情報に戻り値を設定する
            // INPUT情報
            CpnWeekIgaiHindoHanniKakusyuiByCriteriaInEntity cks58InfoByCriteriaInEntity = new CpnWeekIgaiHindoHanniKakusyuiByCriteriaInEntity();
            // 週間計画ID
            cks58InfoByCriteriaInEntity.setAlKs51Id(CommonDtoUtil.strValToInt(inDto.getKs51Id()));
            // 2.5.4. 週間計画表（隔週）情報の取得
            List<CpnWeekIgaiHindoHanniKakusyuiOutEntity> cks58InfoOutList = cpnTucCks58SelectMapper
                    .findCpnWeekIgaiHindoHanniKakusyuiByCriteria(cks58InfoByCriteriaInEntity);
            // ・変数.週間計画（隔週）情報に戻り値を設定する
            // 2.5.5. 週間計画表（連動項目）情報の取得 TODO

            // 2.5.6. レスポンスパラメータ.週間計画詳細リストを繰り返す
            for (Gui01038Cks52 gui01038Cks52 : cks52List) {
                // 2.5.6.1. サービス項目名称の取得
                // サービス項目名称を取得する
                String svItemKnj = kghKrkZCpnWeekLogic.getName(CommonDtoUtil.strValToInt(gui01038Cks52.getSvJigyoId()),
                        CommonDtoUtil.strValToInt(gui01038Cks52.getSvItemCd()), startDt, endDt);
                // ・レスポンスパラメータ.週間計画詳細リスト.サービス項目名称 ＝ 上記取得したサービス項目名称
                gui01038Cks52.setSvItemKnj(svItemKnj);
                // 週間計画加算リスト
                List<Gui01038Cks55> cks55List = new ArrayList<>();
                // 2.5.6.2. 変数.週間計画表（加算データ）情報を繰り返す
                for (CpnWeekInputKasanOutEntity cks55InfoOutEntity : cks55InfoOutList) {
                    // ・レスポンスパラメータ.週間計画詳細リスト.詳細データIDが変数.週間計画表（加算データ）情報.詳細データIDの場合
                    if (gui01038Cks52.getKs52Id()
                            .equals(CommonDtoUtil.objValToString(cks55InfoOutEntity.getKs52Id()))) {
                        Gui01038Cks55 gui01038Cks55 = new Gui01038Cks55();
                        // 加算データID
                        gui01038Cks55.setKs55Id(CommonDtoUtil.objValToString(cks55InfoOutEntity.getKs55Id()));
                        // 週間計画ID
                        gui01038Cks55.setKs51Id(CommonDtoUtil.objValToString(cks55InfoOutEntity.getKs55Id()));
                        // 詳細データID
                        gui01038Cks55.setKs52Id(CommonDtoUtil.objValToString(cks55InfoOutEntity.getKs52Id()));
                        // 加算サービス事業者ID
                        gui01038Cks55.setSvJigyoId(CommonDtoUtil.objValToString(cks55InfoOutEntity.getSvJigyoId()));
                        // 加算サービス項目ID
                        gui01038Cks55.setSvItemCd(CommonDtoUtil.objValToString(cks55InfoOutEntity.getSvItemCd()));
                        // Dmy詳細データID
                        gui01038Cks55.setDmyKs52Id(CommonDtoUtil.objValToString(cks55InfoOutEntity.getDmyKs52Id()));
                        // 回数
                        gui01038Cks55.setKaisuu(CommonDtoUtil.objValToString(cks55InfoOutEntity.getKaisuu()));
                        // 福祉用具貸与の単価
                        gui01038Cks55.setSvTani(CommonDtoUtil.objValToString(cks55InfoOutEntity.getSvTani()));
                        // 福祉用具貸与マスタID
                        gui01038Cks55.setFygId(CommonDtoUtil.objValToString(cks55InfoOutEntity.getFygId()));
                        // 正式名称漢字
                        gui01038Cks55.setFormalnameKnj(cks55InfoOutEntity.getFormalnameKnj());
                        cks55List.add(gui01038Cks55);
                    }
                }
                gui01038Cks52.setCks55List(cks55List);
                // 週間計画担当者リスト
                List<Gui01038Cks56> cks56List = new ArrayList<>();
                // 2.5.6.3. 変数.週間計画表（担当者）情報を繰り返す
                for (Cks56InfoOutEntity cks56InfoOutEntity : cks56InfoOutList) {
                    Gui01038Cks56 gui01038Cks56 = new Gui01038Cks56();
                    // 担当者データID
                    gui01038Cks56.setKs56Id(CommonDtoUtil.objValToString(cks56InfoOutEntity.getKs56Id()));
                    // 詳細データID
                    gui01038Cks56.setKs52Id(CommonDtoUtil.objValToString(cks56InfoOutEntity.getKs52Id()));
                    // 担当者名称
                    gui01038Cks56.setShokushuKnj(CommonDtoUtil.objValToString(cks56InfoOutEntity.getRyakuKnj()));
                    // 職種
                    gui01038Cks56.setShokushuId(CommonDtoUtil.objValToString(cks56InfoOutEntity.getShokushuId()));
                    cks56List.add(gui01038Cks56);
                }
                gui01038Cks52.setCks56List(cks56List);
                // 週間計画月日リスト
                List<Gui01038Cks57> cks57List = new ArrayList<>();
                // 2.5.6.4. 変数.週間計画（月日指定）情報を繰り返す
                for (Cks57InfoOutEntity cks57InfoOutEntity : cks57InfoOutList) {
                    Gui01038Cks57 gui01038Cks57 = new Gui01038Cks57();
                    // カウンター
                    gui01038Cks57.setKs57Id(CommonDtoUtil.objValToString(cks57InfoOutEntity.getKs57Id()));
                    // 詳細データID
                    gui01038Cks57.setKs52Id(CommonDtoUtil.objValToString(cks57InfoOutEntity.getKs52Id()));
                    // 月日指定_開始日
                    gui01038Cks57.setStartYmd(cks57InfoOutEntity.getStartYmd());
                    // 月日指定_終了日
                    gui01038Cks57.setEndYmd(cks57InfoOutEntity.getEndYmd());
                    cks57List.add(gui01038Cks57);
                }
                gui01038Cks52.setCks57List(cks57List);
                // 2.5.6.5. 変数.週間計画表（連動項目）情報を繰り返す TODO

                // 週間計画隔週リスト
                List<Gui01038Cks58> cks58List = new ArrayList<>();
                // 2.5.6.6. 変数.週間計画表（隔週）情報を繰り返す
                for (CpnWeekIgaiHindoHanniKakusyuiOutEntity cks58InfoOutEntity : cks58InfoOutList) {
                    Gui01038Cks58 gui01038Cks58 = new Gui01038Cks58();
                    // 隔週データのプライマリID
                    gui01038Cks58.setKs58Id(CommonDtoUtil.objValToString(cks58InfoOutEntity.getKs58Id()));
                    // 詳細データID
                    gui01038Cks58.setKs52Id(CommonDtoUtil.objValToString(cks58InfoOutEntity.getKs52Id()));
                    // 隔週基準年月日
                    gui01038Cks58.setKakusyuuYmd(cks58InfoOutEntity.getKakusyuuYmd());
                    // 隔週週間隔
                    gui01038Cks58
                            .setKakusyuuKankaku(CommonDtoUtil.objValToString(cks58InfoOutEntity.getKakusyuuKankaku()));
                    // 曜日区分
                    gui01038Cks58.setYoubi(CommonDtoUtil.objValToString(cks58InfoOutEntity.getYoubi()));
                    cks58List.add(gui01038Cks58);
                }
                gui01038Cks52.setCks58List(cks58List);
                // 2.5.6.7. レスポンスパラメータ.週間計画詳細リスト.曜日が”9999999”の場合
                if (CommonConstants.YOUBI_9.equals(gui01038Cks52.getYoubi())) {
                    // 2.5.6.7.1. レスポンスパラメータ.週間計画詳細リスト.週単位以外のｻｰﾋﾞｽ区分が"1"場合
                    if (CommonConstants.IGAI_KBN_STR_ONE.equals(gui01038Cks52.getIgaiKbn())) {
                        // ① 下記関数「f_cpn_week_date_to_moji」を呼び出し、変数.週単位以外文字を戻り値に設定する
                        gui01038Cks52.setIgaiMoji(kghKrkZCpnFuncLogic.getWeekDateToMoji(gui01038Cks52.getIgaiKbn()));
                    }
                    // 2.5.6.7.2. レスポンスパラメータ.週間計画詳細リスト.週単位以外のｻｰﾋﾞｽ区分が"2"場合
                    if (CommonConstants.IGAI_KBN_STR_TWO.equals(gui01038Cks52.getIgaiKbn())) {
                        // ① 下記関数「f_cpn_week_week_to_moji」を呼び出し、変数.週単位以外文字を戻り値に設定する
                        gui01038Cks52.setIgaiMoji(kghKrkZCpnFuncLogic.getWeekDateToMoji(gui01038Cks52.getIgaiKbn()));
                    }
                    // 2.5.6.7.3. レスポンスパラメータ.週間計画詳細リスト.週単位以外のｻｰﾋﾞｽ区分が"3"場合
                    if (CommonConstants.IGAI_KBN_STR_THREE.equals(gui01038Cks52.getIgaiKbn())) {
                        // ① レスポンスパラメータ.週間計画詳細リスト.週間計画月日リストを繰り返し、下記戻る値を「、」で連結して、変数.週単位以外文字を設定する。
                        String igaiMoji = StringUtils.EMPTY;
                        for (Gui01038Cks57 cks57 : gui01038Cks52.getCks57List()) {
                            // ※下記関数二つ「f_cmp_s2w_ez」を呼び出す戻り値を「 ～"」で連結する。
                            String warekiStrRtn = kghCmpF01Logic.getCmpS2wEz(cks57.getStartYmd(),
                                    CommonConstants.INT_1);
                            String warekiEndRtn = kghCmpF01Logic.getCmpS2wEz(cks57.getEndYmd(), CommonConstants.INT_1);
                            if (igaiMoji.equals(StringUtils.EMPTY)) {
                                igaiMoji = igaiMoji + warekiStrRtn + CommonConstants.RANGE_SEPARATOR + warekiEndRtn;
                            } else {
                                igaiMoji = igaiMoji + CommonConstants.STRING_COMMA + warekiStrRtn
                                        + CommonConstants.RANGE_SEPARATOR + warekiEndRtn;
                            }
                        }
                        gui01038Cks52.setIgaiMoji(igaiMoji);
                    }
                    // 2.5.6.7.4. レスポンスパラメータ.週間計画詳細リスト.週単位以外のｻｰﾋﾞｽ区分が"4"場合
                    // f_cpn_week_kakusyuu_to_moji
                    if (CommonConstants.IGAI_KBN_STR_FOUR.equals(gui01038Cks52.getIgaiKbn())) {
                        // 隔週リスト
                        List<Cks58InDto> cks58InDtoList = new ArrayList<>();
                        for (Gui01038Cks58 cks58 : gui01038Cks52.getCks58List()) {
                            Cks58InDto cks58InDto = new Cks58InDto();
                            // 隔週基準年月日
                            cks58InDto.setKakusyuuYmd(cks58.getKakusyuuYmd());
                            // 隔週週間隔
                            cks58InDto.setKakusyuuKankaku(cks58.getKakusyuuKankaku());
                            // 曜日区分
                            cks58InDto.setYoubi(cks58.getYoubi());
                            cks58InDtoList.add(cks58InDto);
                        }
                        // ① 下記関数「f_cpn_week_kakusyuu_to_moji」を呼び出し、変数.週単位以外文字を戻り値に設定する
                        String lsData = kghKrkZCpnFuncLogic.getWeekKakusyuuToMoji(cks58InDtoList);
                        // 2.5.6.7.5. レスポンスパラメータ.週間計画詳細リスト.週単位以外文字 ＝ 変数.週単位以外文字
                        gui01038Cks52.setIgaiMoji(lsData);
                    }
                }
            }
        }
        // 週間計画詳細リスト
        outDto.setCks52List(cks52List);
        // 3. 下記の引数より、週間計画日常リスト取得を呼出し
        // INPUT情報
        Cks54InfoByCriteriaInEntity cks54InfoByCriteriaInEntity = new Cks54InfoByCriteriaInEntity();
        // 週間計画ID
        cks54InfoByCriteriaInEntity.setKs51Id(CommonDtoUtil.strValToInt(inDto.getKs51Id()));
        List<Cks54InfoOutEntity> cks54InfoOutList = cpnTucCks54SelectMapper
                .findCks54InfoByCriteria(cks54InfoByCriteriaInEntity);
        // 返却する情報の編集
        List<Gui01038Cks54> cks54List = new ArrayList<>();
        for (Cks54InfoOutEntity cks54InfoOutEntity : cks54InfoOutList) {
            Gui01038Cks54 cks54 = new Gui01038Cks54();
            // 週間計画ID
            cks54.setKs51Id(CommonDtoUtil.objValToString(cks54InfoOutEntity.getKs51Id()));
            // データID
            cks54.setKs54Id(CommonDtoUtil.objValToString(cks54InfoOutEntity.getKs54Id()));
            // 連番
            cks54.setSeq(CommonDtoUtil.objValToString(cks54InfoOutEntity.getSeq()));
            // 主な日常生活上の活動
            cks54.setNichijoKnj(cks54InfoOutEntity.getNichijoKnj());
            cks54List.add(cks54);
        }
        // 週間計画日常リスト
        outDto.setCks54List(cks54List);
    }

    /**
     * API定義6
     * 
     * @param inDto  週間計画詳細リスト取得の入力Dto
     * @param outDto 週間計画詳細リスト取得の出力Dto
     * @throws Exception
     */
    private void api6DataExProc(WeekPlanDetailsListSelectServiceInDto inDto,
            WeekPlanDetailsListSelectServiceOutDto outDto) {
        // 1.単項目チェック以外の入力チェック
        // 2. 初期データを取込む
        // 2.1. リクエストパラメータ.取込権限フラグが0以外の場合
        if (!CommonConstants.TORI_AUTU_FLG_ZERO.equals(inDto.getToriAutuFlg())) {
            // 2.2.1. データ取込
            // ※「API定義2」シートを参照する
            api2DataExProc(inDto, outDto);
        }
    }
}
