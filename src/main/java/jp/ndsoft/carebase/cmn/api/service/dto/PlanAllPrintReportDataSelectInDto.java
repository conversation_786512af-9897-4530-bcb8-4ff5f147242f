package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServicePrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.V00231ServiceUseAnnexedTableUserInfo;
import jp.ndsoft.smh.framework.controllers.report.model.Jigyosyo;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * 「U00891_計画書一括印刷」用の帳票データ取得の入力DTO.
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class PlanAllPrintReportDataSelectInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 帳票ID */
    private String reportId;

    /** 契約者ID */
    private String keiyakushaId;

    /** システム日付 */
    private String systemDate;

    /** 事業者情報 */
    private Jigyosyo jigyoInfo;

    /** 事業所名 */
    private String jigyoKnj;

    /** システム日付（アプリ用） */
    private String appYmd;

    /** 初期設定マスタの情報 */
    private PlanAllPrintReportInitMasterData initMasterObj;

    /** 印刷設定 */
    private PrintReportServicePrintSet printSet;

    /** DB未保存画面項目 */
    private PlanAllPrintReportDbNoSaveData dbNoSaveData;

    /** 印刷対象履歴 */
    private PlanAllPrintReportSubjectHistoryData printSubjectHistory;

    /** 伏字印刷区分 */
    private String fuseji;
}
