package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00844PrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00844User;
import jp.ndsoft.carebase.cmn.api.service.dto.SelectionTablePrintSettingsSubjectSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.SelectionTablePrintSettingsSubjectSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonSentPrnReqRrkByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonSentPrnReqRrkOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghCpnRaiMonSentPrnReqRrkSelSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * @since 2025.06.14
 * <AUTHOR> 劉婷婷
 * @implNote GUI00844_印刷設定 印刷対象一覧情報取得
 */
@Service
public class SelectionTablePrintSettingsSubjectSelectServiceImpl extends
        SelectServiceImpl<SelectionTablePrintSettingsSubjectSelectServiceInDto, SelectionTablePrintSettingsSubjectSelectServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** インターライ方式印刷リクエスト履歴取得 */
    @Autowired
    private KghCpnRaiMonSentPrnReqRrkSelSelectMapper kghCpnRaiMonSentPrnReqRrkSelSelectMapper;

    /**
     * 印刷対象一覧情報取得
     * 
     * @param inDto 印刷対象一覧情報取得サービス入力Dto
     * @return 印刷対象一覧情報取得サービス出力Dto
     * @throws Exception Exception
     */
    @Override
    protected SelectionTablePrintSettingsSubjectSelectServiceOutDto mainProcess(
            SelectionTablePrintSettingsSubjectSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        SelectionTablePrintSettingsSubjectSelectServiceOutDto outDto = new SelectionTablePrintSettingsSubjectSelectServiceOutDto();
        // 印刷対象履歴リスト
        List<Gui00844PrintSubjectHistory> printSubjectHistoryList = new ArrayList<Gui00844PrintSubjectHistory>();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2.印刷対象履歴一覧編集===============
         * 
         */
        // 2.1.リクエストパラメータ.利用者リスト件数＞0件の場合、リクエストパラメータ.利用者リスト件数分、繰り返して、利用者の複数印刷処理を行う
        if (CollectionUtils.isNotEmpty(inDto.getUserList())) {
            for (Gui00844User user : inDto.getUserList()) {
                // 2.1.1 下記のインターライ方式印刷リクエスト履歴取得DAOを利用し、選定表履歴一覧を取得する。
                KghCpnRaiMonSentPrnReqRrkByCriteriaInEntity kghCpnRaiMonSentPrnReqRrkByCriteriaInEntity = new KghCpnRaiMonSentPrnReqRrkByCriteriaInEntity();
                // 事業者ID
                kghCpnRaiMonSentPrnReqRrkByCriteriaInEntity
                        .setAnSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                // 利用者ID
                kghCpnRaiMonSentPrnReqRrkByCriteriaInEntity.setAnUserid(CommonDtoUtil.strValToInt(user.getUserId()));

                List<KghCpnRaiMonSentPrnReqRrkOutEntity> kghCpnRaiMonSentPrnReqRrkList = this.kghCpnRaiMonSentPrnReqRrkSelSelectMapper
                        .findKghCpnRaiMonSentPrnReqRrkByCriteria(kghCpnRaiMonSentPrnReqRrkByCriteriaInEntity);

                // 2.1.3. データソート処理
                // 2.1.3.1. 「2.1.1.」で取得する履歴情報件数>1件の場合、
                // ソートキー："cap_date_ymd desc, rai_id desc"
                if (CollectionUtils.isNotEmpty(kghCpnRaiMonSentPrnReqRrkList)) {
                    kghCpnRaiMonSentPrnReqRrkList.sort(Comparator
                            .comparing(KghCpnRaiMonSentPrnReqRrkOutEntity::getCapDateYmd,
                                    Comparator.nullsLast(Comparator.reverseOrder()))
                            .thenComparing(KghCpnRaiMonSentPrnReqRrkOutEntity::getRaiId,
                                    Comparator.nullsLast(Comparator.reverseOrder())));

                    // 2.1.4. ソート結果から リクエストパラメータ.基準日以前最新のデータを検索「※「選定日 <= リクエストパラメータ.基準日」
                    // のレコードの1件目を取得する」
                    List<KghCpnRaiMonSentPrnReqRrkOutEntity> filteredList = kghCpnRaiMonSentPrnReqRrkList
                            .stream()
                            .filter(kghCpnRaiMonSentPrnReqRrk -> !StringUtil
                                    .isEmpty(kghCpnRaiMonSentPrnReqRrk.getCapDateYmd())
                                    && kghCpnRaiMonSentPrnReqRrk.getCapDateYmd().compareTo(inDto.getKijunbi()) <= 0)
                            .collect(Collectors.toList());

                    // ①検索結果がある場合、下記の値を設定する
                    if (CollectionUtils.isNotEmpty(filteredList)) {
                        Gui00844PrintSubjectHistory pintSubjectHistory = new Gui00844PrintSubjectHistory();
                        // 利用者ID
                        pintSubjectHistory.setUserId(user.getUserId());
                        // 利用者名
                        pintSubjectHistory.setUserName(user.getUserName());
                        // アセスメントID
                        pintSubjectHistory.setRaiId(CommonDtoUtil.objValToString(filteredList.get(0).getRaiId()));
                        // 計画期間ID
                        pintSubjectHistory.setSc1Id(CommonDtoUtil.objValToString(filteredList.get(0).getSc1Id()));
                        // 選定日
                        pintSubjectHistory.setCapDateYmd(filteredList.get(0).getCapDateYmd());
                        // 結果
                        pintSubjectHistory.setResult(CommonConstants.BLANK_STRING);

                        printSubjectHistoryList.add(pintSubjectHistory);
                    } else {
                        Gui00844PrintSubjectHistory pintSubjectHistory = new Gui00844PrintSubjectHistory();
                        // 利用者ID
                        pintSubjectHistory.setUserId(user.getUserId());
                        // 利用者名
                        pintSubjectHistory.setUserName(user.getUserName());

                        // ②検索結果がない場合、
                        // 結果
                        pintSubjectHistory.setResult(CommonConstants.ASS_PRT_RRK_NONE_MSG);

                        printSubjectHistoryList.add(pintSubjectHistory);
                    }

                } else {
                    // 2.1.2. 「2.1.1.」で取得する履歴情報件数=0件の場合、印刷対象履歴リストを設定して、continue で次のループ処理に移る
                    Gui00844PrintSubjectHistory pintSubjectHistory = new Gui00844PrintSubjectHistory();
                    // 利用者ID
                    pintSubjectHistory.setUserId(user.getUserId());
                    // 利用者名
                    pintSubjectHistory.setUserName(user.getUserName());
                    // 結果
                    pintSubjectHistory.setResult(CommonConstants.ASS_PRT_RRK_NONE_MSG);

                    printSubjectHistoryList.add(pintSubjectHistory);

                }
            }

        }

        outDto.setPrintSubjectHistoryList(printSubjectHistoryList);

        LOG.info(Constants.END);

        return outDto;
    }

}
