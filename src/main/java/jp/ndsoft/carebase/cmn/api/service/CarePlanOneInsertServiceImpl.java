package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jp.ndsoft.carebase.cmn.api.service.dto.CarePlanOneBunrui3InsertDto;
import jp.ndsoft.carebase.cmn.api.service.dto.CarePlanOneInsertServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.CarePlanOneInsertServiceOutDto;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkSsmMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsm;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsmCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;
import jp.ndsoft.smh.framework.global.base.service.InsertServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * GUI00934_計画書（1）マスタ情報保存サービス.
 *
 * <AUTHOR>
 * 
 */
@Service
public class CarePlanOneInsertServiceImpl
        extends InsertServiceImpl<CarePlanOneInsertServiceInDto, CarePlanOneInsertServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    /** 分類1：2 */
    private static final Integer BUNRUI_1_ID = 2;
    /** 分類2：3 */
    private static final Integer BUNRUI_2_ID = 3;
    @Autowired
    private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;
    @Autowired
    private KghMocKrkSsmMapper kghMocKrkSsmMapper;

    /**
     * GUI00934_計画書（1）マスタ情報保存
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    @Override
    protected CarePlanOneInsertServiceOutDto mainProcess(CarePlanOneInsertServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        CarePlanOneInsertServiceOutDto outDto = new CarePlanOneInsertServiceOutDto();
        // 計画書（1）の初期設定マスタ情報取得
        KrkSsmInfoByCriteriaInEntity krkSsmInfoByCriteriaInEntity = new KrkSsmInfoByCriteriaInEntity();
        // 事業者ID
        krkSsmInfoByCriteriaInEntity.setSvJigyoId(Integer.valueOf(inDto.getJigyoId()));
        // 施設ID
        krkSsmInfoByCriteriaInEntity.setShisetuId(Integer.valueOf(inDto.getShisetuId()));
        // 分類1：2
        krkSsmInfoByCriteriaInEntity.setBunrui1Id(BUNRUI_1_ID);
        // 分類2：3
        krkSsmInfoByCriteriaInEntity.setBunrui2Id(BUNRUI_2_ID);
        List<KrkSsmInfoOutEntity> krkSsmInfoOutEntityList = kghMocKrkSsmSelectMapper
                .findKrkSsmInfoByCriteria(krkSsmInfoByCriteriaInEntity);
        for (CarePlanOneBunrui3InsertDto x : inDto.getBunrui3SetList()) {
            if (x.getBunrui3Id().isEmpty() || krkSsmInfoOutEntityList.stream()
                    .filter(y -> y.getBunrui3Id().equals(Integer.valueOf(x.getBunrui3Id()))).count() <= 0) {
                KghMocKrkSsm kghMocKrkSsm = new KghMocKrkSsm();
                kghMocKrkSsm.setBunrui1Id(BUNRUI_1_ID);
                kghMocKrkSsm.setBunrui2Id(BUNRUI_2_ID);
                kghMocKrkSsm.setShisetuId(Integer.valueOf(inDto.getShisetuId()));
                kghMocKrkSsm.setSvJigyoId(Integer.valueOf(inDto.getJigyoId()));
                kghMocKrkSsm.setBunrui3Id(Integer.valueOf(x.getBunrui3Id()));
                kghMocKrkSsm.setIntValue(Integer.valueOf(x.getBunrui3Value()));
                // CommonDaoUtil.setInsertCommonColumns(kghMocKrkSsm);
                int count = kghMocKrkSsmMapper.insertSelective(kghMocKrkSsm);
                if (count <= 0) {
                    throw new ExclusiveException();
                }
            } else {
                KrkSsmInfoOutEntity krkSsmInfoOutEntity = krkSsmInfoOutEntityList.stream()
                        .filter(y -> y.getBunrui3Id().equals(Integer.valueOf(x.getBunrui3Id()))).findFirst().get();
                // if (!StringUtils.hasText(x.getModifiedCnt())) {
                // throw new ExclusiveException();
                // }
                KghMocKrkSsmCriteria criteria = new KghMocKrkSsmCriteria();
                criteria.createCriteria().andBunrui1IdEqualTo(krkSsmInfoOutEntity.getBunrui1Id())
                        .andBunrui2IdEqualTo(krkSsmInfoOutEntity.getBunrui2Id()).andBunrui3IdEqualTo(
                                krkSsmInfoOutEntity.getBunrui3Id())
                        .andShisetuIdEqualTo(krkSsmInfoOutEntity.getShisetuId()).andSvJigyoIdEqualTo(
                                krkSsmInfoOutEntity.getSvJigyoId());
                KghMocKrkSsm kghMocKrkSsm = new KghMocKrkSsm();
                kghMocKrkSsm.setBunrui1Id(krkSsmInfoOutEntity.getBunrui1Id());
                kghMocKrkSsm.setBunrui2Id(krkSsmInfoOutEntity.getBunrui2Id());
                kghMocKrkSsm.setBunrui3Id(krkSsmInfoOutEntity.getBunrui3Id());
                kghMocKrkSsm.setShisetuId(krkSsmInfoOutEntity.getShisetuId());
                kghMocKrkSsm.setSvJigyoId(krkSsmInfoOutEntity.getSvJigyoId());
                kghMocKrkSsm.setIntValue(Integer.valueOf(x.getBunrui3Value()));

                // CommonDaoUtil.setUpdateCommonColumns(kghMocKrkSsm, new
                // BigInteger(x.getModifiedCnt()));
                int count = kghMocKrkSsmMapper.updateByCriteriaSelective(kghMocKrkSsm, criteria);
                if (count <= 0) {
                    throw new ExclusiveException();
                }
            }
        }
        outDto.setStatus(!StringUtils.hasText(outDto.getStatus()) || outDto.getStatus().equals("1") ? "1" : "0");
        LOG.info(Constants.END);
        return outDto;
    }

}
