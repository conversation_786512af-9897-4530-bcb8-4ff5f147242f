package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00774SubInfoJ;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentInterRAIJInitSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentInterRAIJInitSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssessmentHealthStatusInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssessmentHealthStatusInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.AssessmentHealthStatusInfoSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.04.15
 * <AUTHOR>
 * @apiNote GUI00774_アセスメント(インターライ)画面J 初期情報取得
 */
@Service
public class AssessmentInterRAIJInitSelectServiceImpl extends
        SelectServiceImpl<AssessmentInterRAIJInitSelectServiceInDto, AssessmentInterRAIJInitSelectServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // サブ情報（J）取得
    @Autowired
    private AssessmentHealthStatusInfoSelectMapper assessmentHealthStatusInfoSelectMapper;

    @Override
    protected AssessmentInterRAIJInitSelectServiceOutDto mainProcess(AssessmentInterRAIJInitSelectServiceInDto inDto)
            throws Exception {

        LOG.info(Constants.START);

        // 戻り情報を設定
        AssessmentInterRAIJInitSelectServiceOutDto out = new AssessmentInterRAIJInitSelectServiceOutDto();

        // サブ情報（J）
        GUI00774SubInfoJ subInfoJ = new GUI00774SubInfoJ();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. サブ情報（J）取得===============
         * 
         */
        // 下記のinterRAIアセスメント.J健康状態情報取得のDAOを利用し、サブ情報（J）を取得する。
        // 検索条件
        AssessmentHealthStatusInfoByCriteriaInEntity assessmentHealthStatusInfoByCriteriaInEntity = new AssessmentHealthStatusInfoByCriteriaInEntity();
        // アセスメントID
        assessmentHealthStatusInfoByCriteriaInEntity.setAnRaiId(CommonDtoUtil.strValToInt(inDto.getRaiId()));
        // DAOを実行
        List<AssessmentHealthStatusInfoOutEntity> list = this.assessmentHealthStatusInfoSelectMapper
                .findAssessmentHealthStatusInfoByCriteria(assessmentHealthStatusInfoByCriteriaInEntity);

        // サブ情報（J）の編集
        if (CollectionUtils.isNotEmpty(list)) {
            AssessmentHealthStatusInfoOutEntity entity = list.get(0);
            // アセスメントID
            subInfoJ.setRaiId(CommonDtoUtil.objValToString(entity.getRaiId()));
            // 転倒
            subInfoJ.setJ1(CommonDtoUtil.objValToString(entity.getJ1()));
            // 最近の転倒
            subInfoJ.setJ2(CommonDtoUtil.objValToString(entity.getJ2()));
            // 問題頻度_バランス_立位
            subInfoJ.setJ3A(CommonDtoUtil.objValToString(entity.getJ3A()));
            // 問題頻度_バランス_方向転換
            subInfoJ.setJ3B(CommonDtoUtil.objValToString(entity.getJ3B()));
            // 問題頻度_バランス_めまい
            subInfoJ.setJ3C(CommonDtoUtil.objValToString(entity.getJ3C()));
            // 問題頻度_バランス_歩行
            subInfoJ.setJ3D(CommonDtoUtil.objValToString(entity.getJ3D()));
            // 問題頻度_心肺_胸痛
            subInfoJ.setJ3E(CommonDtoUtil.objValToString(entity.getJ3E()));
            // 問題頻度_心肺_気道
            subInfoJ.setJ3F(CommonDtoUtil.objValToString(entity.getJ3F()));
            // 問題頻度_精神_思考
            subInfoJ.setJ3G(CommonDtoUtil.objValToString(entity.getJ3G()));
            // 問題頻度_精神_妄想
            subInfoJ.setJ3H(CommonDtoUtil.objValToString(entity.getJ3H()));
            // 問題頻度_精神_幻覚
            subInfoJ.setJ3I(CommonDtoUtil.objValToString(entity.getJ3I()));
            // 問題頻度_神経_失語症
            subInfoJ.setJ3J(CommonDtoUtil.objValToString(entity.getJ3J()));
            // 問題頻度_消化器系_胃酸
            subInfoJ.setJ3K(CommonDtoUtil.objValToString(entity.getJ3K()));
            // 問題頻度_消化器系_便秘
            subInfoJ.setJ3L(CommonDtoUtil.objValToString(entity.getJ3L()));
            // 問題頻度_消化器系_下痢
            subInfoJ.setJ3M(CommonDtoUtil.objValToString(entity.getJ3M()));
            // 問題頻度_消化器系_嘔吐
            subInfoJ.setJ3N(CommonDtoUtil.objValToString(entity.getJ3N()));
            // 問題頻度_睡眠障害_入眠
            subInfoJ.setJ3O(CommonDtoUtil.objValToString(entity.getJ3O()));
            // 問題頻度_睡眠障害_過多
            subInfoJ.setJ3P(CommonDtoUtil.objValToString(entity.getJ3P()));
            // 問題頻度_その他_誤嚥
            subInfoJ.setJ3Q(CommonDtoUtil.objValToString(entity.getJ3Q()));
            // 問題頻度_その他_発熱
            subInfoJ.setJ3R(CommonDtoUtil.objValToString(entity.getJ3R()));
            // 問題頻度_その他_出血
            subInfoJ.setJ3S(CommonDtoUtil.objValToString(entity.getJ3S()));
            // 問題頻度_その他_不衛生
            subInfoJ.setJ3T(CommonDtoUtil.objValToString(entity.getJ3T()));
            // 問題頻度_その他_末梢浮腫
            subInfoJ.setJ3U(CommonDtoUtil.objValToString(entity.getJ3U()));
            // 呼吸困難
            subInfoJ.setJ4(CommonDtoUtil.objValToString(entity.getJ4()));
            // 疲労感
            subInfoJ.setJ5(CommonDtoUtil.objValToString(entity.getJ5()));
            // 痛み_頻度
            subInfoJ.setJ6A(CommonDtoUtil.objValToString(entity.getJ6A()));
            // 痛み_程度
            subInfoJ.setJ6B(CommonDtoUtil.objValToString(entity.getJ6B()));
            // 痛み_持続性
            subInfoJ.setJ6C(CommonDtoUtil.objValToString(entity.getJ6C()));
            // 痛み_突発痛
            subInfoJ.setJ6D(CommonDtoUtil.objValToString(entity.getJ6D()));
            // 痛み_コントロール
            subInfoJ.setJ6E(CommonDtoUtil.objValToString(entity.getJ6E()));
            // 状態の不安定性_症状
            subInfoJ.setJ7A(CommonDtoUtil.objValToString(entity.getJ7A()));
            // 状態の不安定性_発性
            subInfoJ.setJ7B(CommonDtoUtil.objValToString(entity.getJ7B()));
            // 状態の不安定性_末期の疾患
            subInfoJ.setJ7C(CommonDtoUtil.objValToString(entity.getJ7C()));
            // 主観的健康感
            subInfoJ.setJ8(CommonDtoUtil.objValToString(entity.getJ8()));
            // 喫煙と飲酒_喫煙
            subInfoJ.setJ9A(CommonDtoUtil.objValToString(entity.getJ9A()));
            // 喫煙と飲酒_飲酒
            subInfoJ.setJ9B(CommonDtoUtil.objValToString(entity.getJ9B()));
            // j1_メモ
            subInfoJ.setJ1MemoKnj(entity.getJ1MemoKnj());
            // j1_メモフォント
            subInfoJ.setJ1MemoFont(CommonDtoUtil.objValToString(entity.getJ1MemoFont()));
            // j1_メモ色
            subInfoJ.setJ1MemoColor(CommonDtoUtil.objValToString(entity.getJ1MemoColor()));
            // j2_メモ
            subInfoJ.setJ2MemoKnj(entity.getJ2MemoKnj());
            // j2_メモフォント
            subInfoJ.setJ2MemoFont(CommonDtoUtil.objValToString(entity.getJ2MemoFont()));
            // j2_メモ色
            subInfoJ.setJ2MemoColor(CommonDtoUtil.objValToString(entity.getJ2MemoColor()));
            // j3_メモ
            subInfoJ.setJ3MemoKnj(entity.getJ3MemoKnj());
            // j3_メモフォント
            subInfoJ.setJ3MemoFont(CommonDtoUtil.objValToString(entity.getJ3MemoFont()));
            // j3_メモ色
            subInfoJ.setJ3MemoColor(CommonDtoUtil.objValToString(entity.getJ3MemoColor()));
            // j4_メモ
            subInfoJ.setJ4MemoKnj(entity.getJ4MemoKnj());
            // j4_メモフォント
            subInfoJ.setJ4MemoFont(CommonDtoUtil.objValToString(entity.getJ4MemoFont()));
            // j4_メモ色
            subInfoJ.setJ4MemoColor(CommonDtoUtil.objValToString(entity.getJ4MemoColor()));
            // j5_メモ
            subInfoJ.setJ5MemoKnj(entity.getJ5MemoKnj());
            // j5_メモフォント
            subInfoJ.setJ5MemoFont(CommonDtoUtil.objValToString(entity.getJ5MemoFont()));
            // j5_メモ色
            subInfoJ.setJ5MemoColor(CommonDtoUtil.objValToString(entity.getJ5MemoColor()));
            // j6_a_メモ
            subInfoJ.setJ6AMemoKnj(entity.getJ6AMemoKnj());
            // j6_a_メモフォント
            subInfoJ.setJ6AMemoFont(CommonDtoUtil.objValToString(entity.getJ6AMemoFont()));
            // j6_a_メモ色
            subInfoJ.setJ6AMemoColor(CommonDtoUtil.objValToString(entity.getJ6AMemoColor()));
            // j6_b_メモ
            subInfoJ.setJ6BMemoKnj(entity.getJ6BMemoKnj());
            // j6_b_メモフォント
            subInfoJ.setJ6BMemoFont(CommonDtoUtil.objValToString(entity.getJ6BMemoFont()));
            // j6_b_メモ色
            subInfoJ.setJ6BMemoColor(CommonDtoUtil.objValToString(entity.getJ6BMemoColor()));
            // j6_c_メモ
            subInfoJ.setJ6CMemoKnj(entity.getJ6CMemoKnj());
            // j6_c_メモフォント
            subInfoJ.setJ6CMemoFont(CommonDtoUtil.objValToString(entity.getJ6CMemoFont()));
            // j6_c_メモ色
            subInfoJ.setJ6CMemoColor(CommonDtoUtil.objValToString(entity.getJ6CMemoColor()));
            // j6_d_メモ
            subInfoJ.setJ6DMemoKnj(entity.getJ6DMemoKnj());
            // j6_d_メモフォント
            subInfoJ.setJ6DMemoFont(CommonDtoUtil.objValToString(entity.getJ6DMemoFont()));
            // j6_d_メモ色
            subInfoJ.setJ6DMemoColor(CommonDtoUtil.objValToString(entity.getJ6DMemoColor()));
            // j6_e_メモ
            subInfoJ.setJ6EMemoKnj(entity.getJ6EMemoKnj());
            // j6_e_メモフォント
            subInfoJ.setJ6EMemoFont(CommonDtoUtil.objValToString(entity.getJ6EMemoFont()));
            // j6_e_メモ色
            subInfoJ.setJ6EMemoColor(CommonDtoUtil.objValToString(entity.getJ6EMemoColor()));
            // j7_メモ
            subInfoJ.setJ7MemoKnj(entity.getJ7MemoKnj());
            // j7_メモフォント
            subInfoJ.setJ7MemoFont(CommonDtoUtil.objValToString(entity.getJ7MemoFont()));
            // j7_メモ色
            subInfoJ.setJ7MemoColor(CommonDtoUtil.objValToString(entity.getJ7MemoColor()));
            // j8_メモ
            subInfoJ.setJ8MemoKnj(entity.getJ8MemoKnj());
            // j8_メモフォント
            subInfoJ.setJ8MemoFont(CommonDtoUtil.objValToString(entity.getJ8MemoFont()));
            // j8_メモ色
            subInfoJ.setJ8MemoColor(CommonDtoUtil.objValToString(entity.getJ8MemoColor()));
            // j9_a_メモ
            subInfoJ.setJ9AMemoKnj(entity.getJ9AMemoKnj());
            // j9_a_メモフォント
            subInfoJ.setJ9AMemoFont(CommonDtoUtil.objValToString(entity.getJ9AMemoFont()));
            // j9_a_メモ色
            subInfoJ.setJ9AMemoColor(CommonDtoUtil.objValToString(entity.getJ9AMemoColor()));
            // j9_b_メモ
            subInfoJ.setJ9BMemoKnj(entity.getJ9BMemoKnj());
            // j9_b_メモフォント
            subInfoJ.setJ9BMemoFont(CommonDtoUtil.objValToString(entity.getJ9BMemoFont()));
            // j9_b_メモ色
            subInfoJ.setJ9BMemoColor(CommonDtoUtil.objValToString(entity.getJ9BMemoColor()));
        }
        out.setSubInfoJ(subInfoJ);

        LOG.info(Constants.END);
        return out;
    }
}
