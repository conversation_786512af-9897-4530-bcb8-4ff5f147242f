package jp.ndsoft.carebase.cmn.api.report.dto;

import jp.ndsoft.smh.framework.global.report.dto.FixedReportInDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * U00891_計画書一括印刷
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PlanAllPrintReportServiceInDto extends FixedReportInDto {
    /** UID. */
    private static final long serialVersionUID = 1L;
    /** U0081K_居宅サービス計画書（１） */
    private JRBeanCollectionDataSource dataSource1;
    /** U0081S_施設サービス計画書(1) */
    private JRBeanCollectionDataSource dataSource2;
    /** U0082S_施設サービス計画書（２） */
    private JRBeanCollectionDataSource dataSource3;
    /** U0082K_居宅サービス計画書（２） */
    private JRBeanCollectionDataSource dataSource4;
    /** U00852_週間サービス計画表（R34改訂） */
    private JRBeanCollectionDataSource dataSource5;
    /** U00861_日課計画表出力 */
    private JRBeanCollectionDataSource dataSource6;
    /** V00231_サービス利用票 */
    private JRBeanCollectionDataSource dataSource7;
    /** V00231_サービス利用票別表 */
    private JRBeanCollectionDataSource dataSource8;
    /** 帳票出力のデータソース */
    private JRBeanCollectionDataSource dataSourceAll;

}
