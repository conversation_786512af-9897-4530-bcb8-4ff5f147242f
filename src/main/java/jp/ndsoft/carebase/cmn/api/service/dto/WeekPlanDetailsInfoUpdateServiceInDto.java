package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks51;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks52;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks54;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038DeleteKasanId;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038DeleteLinkedItemId;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038DeleteTanId;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038DeleteTukihiId;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI01038_週間計画詳細情報保存の入力Dto
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class WeekPlanDetailsInfoUpdateServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 法人ID */
    @NotEmpty
    private String houjinId;

    /** 施設ID */
    @NotEmpty
    private String shisetuId;

    /** 利用者ID */
    @NotEmpty
    private String userId;

    /** 事業者ID */
    @NotEmpty
    private String svJigyoId;

    /** 種別ID */
    @NotEmpty
    private String syubetsuId;

    /** 期間対象フラグ */
    @NotEmpty
    private String lbKikan;

    /** 履歴フラグ */
    @NotEmpty
    private String rirekiFlg;

    /** 担当者退避IDリスト */
    @Valid
    private List<Gui01038DeleteTanId> deleteTanIdList;

    /** 加算退避IDリスト */
    @Valid
    private List<Gui01038DeleteKasanId> deleteKasanIdList;

    /** 隔週退避IDリスト */
    @Valid
    private List<Gui01038DeleteTukihiId> deleteTukihiIdList;

    /** 連動項目退避IDリスト */
    @Valid
    private List<Gui01038DeleteLinkedItemId> deleteLinkedItemIdList;

    /** 計画期間ID */
    @NotEmpty
    private String sc1Id;

    /** 週間計画履歴リスト */
    @NotEmpty
    private List<Gui01038Cks51> cks51List;

    /** 週間計画日常リスト */
    @NotEmpty
    private List<Gui01038Cks54> cks54List;

    /** 週間計画詳細リスト */
    @NotEmpty
    private List<Gui01038Cks52> cks52List;

}
