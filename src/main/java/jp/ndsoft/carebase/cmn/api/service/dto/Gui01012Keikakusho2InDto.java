package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * @since 2025.05.05
 * <AUTHOR> TRAN VAN THAO
 * @implNote Gui01012Keikakusho2InDto
 */
@Getter
@Setter
public class Gui01012Keikakusho2InDto extends IDtoImpl {

    // serialVersionUID.
    private static final long serialVersionUID = 1L;

    // 分類３
    @NotEmpty
    private String bunrui3Id;

    // 整数
    @NotEmpty
    private String intValue;

    // 更新回数
    // @NotEmpty
    // private String modifiedCnt;

    // 更新区分
    private String updateKbn;

}
