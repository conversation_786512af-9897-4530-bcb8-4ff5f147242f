package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01167Micchaku;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI01167_地域密着型介護サービス有効期間情報取得OutDto
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class AreaCloseContactTypeNursingCareServiceValidPeriodInfoSelectServiceOutDto extends IDtoImpl {
    // serialVersionUID.
    private static final long serialVersionUID = 1L;
    // 保険者名称
    @JsonProperty("kHokenKnj")
    private String kHokenKnj;
    // 事業名
    private String jigyoKnj;
    // 地域密着型介護サービス有効期間情報リスト
    private List<Gui01167Micchaku> micchakuList = new ArrayList<>();

}
