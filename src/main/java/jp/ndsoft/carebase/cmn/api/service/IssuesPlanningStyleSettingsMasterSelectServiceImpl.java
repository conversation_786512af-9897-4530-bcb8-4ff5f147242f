package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui00904kghMocKrkFree11OutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui00904kghMocKrkFree12OutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui00904kghMocKrkFree2OutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.IssuesPlanningStyleSettingsMasterSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.IssuesPlanningStyleSettingsMasterSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFree1Data1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFree1Data1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFree2Data1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFree2Data1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFreeTekiyoInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFreeTekiyoInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghStringMoniTitleKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghStringMoniTitleKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucFreeassKra1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFree1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFree2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFreeTekiyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucFreeassKra1SelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.05.12
 * <AUTHOR>
 * @implNote GUI00904_課題立案様式設定マスタ画面 初期情報取得
 */
@Service
public class IssuesPlanningStyleSettingsMasterSelectServiceImpl extends
        SelectServiceImpl<IssuesPlanningStyleSettingsMasterSelectServiceInDto, IssuesPlanningStyleSettingsMasterSelectServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // 下記27-01 カスタマイズ帳票マスタヘッダ情報取得のDAO
    @Autowired
    private KghMocKrkFree1SelectMapper kghMocKrkFree1SelectMapper;

    // 下記カスタマイズ帳票適用マスタ情報取得のDAO
    @Autowired
    private KghMocKrkFreeTekiyoSelectMapper kghMocKrkFreeTekiyoSelectMapper;

    // 下記27-01 カスタマイズ帳票マスタヘッダデータ情報取得のDAO
    @Autowired
    private KghMocKrkFree2SelectMapper kghMocKrkFree2SelectMapper;

    // 下記32-07 課題立案ヘッダ個数取得のDAO
    @Autowired
    private KghTucFreeassKra1SelectMapper kghTucFreeassKra1SelectMapper;

    /**
     * 課題立案様式設定マスタ初期情報取得
     * 
     * @param inDto 課題立案様式設定マスタ画面の初期情報取得入力Dto
     * @return 課題立案様式設定マスタ画面の初期情報取得出力Dto
     * @throws Exception Exception
     */
    @Override
    protected IssuesPlanningStyleSettingsMasterSelectServiceOutDto mainProcess(
            IssuesPlanningStyleSettingsMasterSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2.課題立案様式名初期処理の情報を取得する===============
         * 
         */
        // 2.1. 下記のカスタマイズ帳票マスタヘッダ情報取得のDAOを利用し、課題立案様式名情報リストを取得する。
        // 検索パラメータ作成
        KghStringMoniTitleKnjByCriteriaInEntity kghStringMoniTitleKnjByCriteriaInEntity = new KghStringMoniTitleKnjByCriteriaInEntity();
       // 機能区分
       kghStringMoniTitleKnjByCriteriaInEntity.setAiKinouKbn(CommonDtoUtil.strValToInt(inDto.getKinouKbn()));
       // 様式区分
       kghStringMoniTitleKnjByCriteriaInEntity
               .setAiYoushikiKbn(CommonDtoUtil.strValToInt(inDto.getYoushikiKbn()));
        // 課題立案様式名情報リスト
        List<KghStringMoniTitleKnjOutEntity> kghStringMoniTitleKnjOutEntities = kghMocKrkFree1SelectMapper
                .findKghStringMoniTitleKnjByCriteria(kghStringMoniTitleKnjByCriteriaInEntity);

        // 2.2. 下記の様式名リスト情報取得のDAOを利用し、様式名適用のマスタヘッダIDを取得する。
       // 検索パラメータ作成
       KghMocKrkFreeTekiyoInfoByCriteriaInEntity kghMocKrkFreeTekiyoInfoByCriteriaInEntity = new KghMocKrkFreeTekiyoInfoByCriteriaInEntity();
        // 事業所ID
        kghMocKrkFreeTekiyoInfoByCriteriaInEntity.setIlSvJigyoId(inDto.getSvJigyoId());
        // 機能区分
        kghMocKrkFreeTekiyoInfoByCriteriaInEntity.setIiKinouKbn(inDto.getKinouKbn());
        // 様式区分
        kghMocKrkFreeTekiyoInfoByCriteriaInEntity
                .setIiYoshikiKbn(inDto.getYoushikiKbn());
        // 様式名適用のマスタヘッダIDを取得する
        KghMocKrkFreeTekiyoInfoOutEntity kghMocKrkFreeTekiyoInfoOutEntity = new KghMocKrkFreeTekiyoInfoOutEntity();
        List<KghMocKrkFreeTekiyoInfoOutEntity> kghMocKrkFreeTekiyoInfoOutEntities = kghMocKrkFreeTekiyoSelectMapper
                .findKghMocKrkFreeTekiyoInfoByCriteria(kghMocKrkFreeTekiyoInfoByCriteriaInEntity);
        // 様式名適用のマスタヘッダIDを設定
        // if (!CollectionUtils.isNullOrEmpty(kghMocKrkFreeTekiyoInfoOutEntities)) {
        //     kghMocKrkFreeTekiyoInfoOutEntity = kghMocKrkFreeTekiyoInfoOutEntities.get(0);
        // }
        kghMocKrkFreeTekiyoInfoOutEntity.setFree1Id(6);

        /*
         * ===============3.様式名適用のマスタヘッダIDを取得後で、課題立案様式設定情報初期処理の情報を取得する===============
         * 
         */
        // 3.1. 下記のカスタマイズ帳票マスタヘッダ情報取得のDAOを利用し、課題立案様式設定情報（画面生成用データ）を取得する。
        // 課題立案様式設定情報（画面生成用データ）を取得する。
        // 検索パラメータ作成
        KghMocKrkFree1Data1ByCriteriaInEntity kghMocKrkFree1Data1ByCriteriaInEntity = new KghMocKrkFree1Data1ByCriteriaInEntity();
        // マスタヘッダID
        kghMocKrkFree1Data1ByCriteriaInEntity.setFree1Id(kghMocKrkFreeTekiyoInfoOutEntity.getFree1Id());
        // 課題立案様式設定情報
        KghMocKrkFree1Data1OutEntity kghMocKrkFree1Data1OutEntity = new KghMocKrkFree1Data1OutEntity();
        List<KghMocKrkFree1Data1OutEntity> kghMocKrkFree1Data1OutEntities = kghMocKrkFree1SelectMapper
                .findKghMocKrkFree1Data1ByCriteria(kghMocKrkFree1Data1ByCriteriaInEntity);
        // 課題立案様式設定情報を設定
        if (kghMocKrkFree1Data1OutEntities != null && kghMocKrkFree1Data1OutEntities.size() > CommonConstants.NUMBER_0) {
            kghMocKrkFree1Data1OutEntity = kghMocKrkFree1Data1OutEntities.get(0);
        }

        // 3.2. カスタマイズ帳票マスタヘッダデータ情報取得のDAOを利用し、課題立案表データリストを取得する。
        // 3.2.1. 3.1で取得した行の分割数（上）の件数分、 課題立案表データを繰り返し取得する。
        // 行数：１～行の分割数（上）
        // 行数を繰り返し、課題立案表データを取得する
        // 課題立案表データリスト
        List<KghMocKrkFree2Data1OutEntity> kghMocKrkFree2Data1OutEntities = new ArrayList<>();

        if (kghMocKrkFree1Data1OutEntity.getColumnCount() != null
                && kghMocKrkFree1Data1OutEntity.getColumnCount() > CommonConstants.NUMBER_0) {
            for (int index = 1; index <= kghMocKrkFree1Data1OutEntity.getColumnCount(); index++) {
                // 検索パラメータ作成
                KghMocKrkFree2Data1ByCriteriaInEntity kghMocKrkFree2Data1ByCriteriaInEntity = new KghMocKrkFree2Data1ByCriteriaInEntity();
                // マスタヘッダID
                kghMocKrkFree2Data1ByCriteriaInEntity
                        .setFree1Id(kghMocKrkFreeTekiyoInfoOutEntity.getFree1Id());
                // 項目ID
                kghMocKrkFree2Data1ByCriteriaInEntity.setKoumokuId(index);
                // 課題立案表データリストを追加
                kghMocKrkFree2Data1OutEntities.addAll(kghMocKrkFree2SelectMapper
                        .findKghMocKrkFree2Data1ByCriteria(
                                kghMocKrkFree2Data1ByCriteriaInEntity));
            }
        }

        // 3.3. 下記の課題立案ヘッダ情報取得のDAOを利用し、使用フラグを設定する。
        // 3.3.1.課題立案情報の存在を判定するために、課題立案ヘッダのカウント数を取得する
        // 32-07 課題立案ヘッダ個数取得
        KghTucFreeassKra1ByCriteriaInEntity kghTucFreeassKra1ByCriteriaInEntity = new KghTucFreeassKra1ByCriteriaInEntity();
        // 課題立案様式ID
        kghTucFreeassKra1ByCriteriaInEntity.setIlTitleId(kghMocKrkFreeTekiyoInfoOutEntity.getFree1Id());
        // カウント数
        int count = kghTucFreeassKra1SelectMapper
                .countKghTucFreeassKra1ByCriteria(kghTucFreeassKra1ByCriteriaInEntity)
                .getCNT();

        // 課題立案様式名情報リスト
        List<Gui00904kghMocKrkFree11OutDto> kghMocKrkFree1TitleInfoList = new ArrayList<>();
        for (KghStringMoniTitleKnjOutEntity entity : kghStringMoniTitleKnjOutEntities) {
            Gui00904kghMocKrkFree11OutDto gui00904kghMocKrkFree11 = new Gui00904kghMocKrkFree11OutDto();
            // マスタヘッダID
            gui00904kghMocKrkFree11.setFree1Id(CommonDtoUtil.objValToString(entity.getFree1Id()));
            // 帳票タイトル
            gui00904kghMocKrkFree11.setTitleKnj(entity.getTitleKnj());
            // 帳票タイトルID組合せ
            gui00904kghMocKrkFree11.setDmyTitleKnj(entity.getDmyTitleKnj());
            // 課題立案様式名情報リストを追加
            kghMocKrkFree1TitleInfoList.add(gui00904kghMocKrkFree11);
        }

        // 課題立案様式設定情報（画面生成用データ）
        Gui00904kghMocKrkFree12OutDto kghMocKrkFree1Info = new Gui00904kghMocKrkFree12OutDto();
        // 行の分割数（上）
        kghMocKrkFree1Info.setColumnCount(
                CommonDtoUtil.objValToString(kghMocKrkFree1Data1OutEntity.getColumnCount()));
        // 印刷文字サイズ
        kghMocKrkFree1Info
                .setFontSize(CommonDtoUtil.objValToString(kghMocKrkFree1Data1OutEntity.getFontSize()));

        // 課題立案表データリスト
        List<Gui00904kghMocKrkFree2OutDto> kghMocKrkFree2InfoList = new ArrayList<>();
        for (KghMocKrkFree2Data1OutEntity entity : kghMocKrkFree2Data1OutEntities) {
            Gui00904kghMocKrkFree2OutDto gui00904kghMocKrkFree2 = new Gui00904kghMocKrkFree2OutDto();
            // 項目名
            gui00904kghMocKrkFree2.setNameKnj(entity.getNameKnj());
            // 入力値変更フラグ
            gui00904kghMocKrkFree2.setInputKbnUpdateFlag(CommonConstants.INPUT_MODIFIED_0);
            // 入力方法
            gui00904kghMocKrkFree2.setInputKbn(CommonDtoUtil.objValToString(entity.getInputKbn()));
            // 連動区分
            gui00904kghMocKrkFree2.setRendouKbn(CommonDtoUtil.objValToString(entity.getRendouKbn()));
            // 文字数
            gui00904kghMocKrkFree2.setWidthCnt(CommonDtoUtil.objValToString(entity.getWidthCnt()));
            // 課題立案表データリストを追加
            kghMocKrkFree2InfoList.add(gui00904kghMocKrkFree2);
        }

        /*
         * ===============4. 上記処理で取得した結果レスポンスを返却する。===============
         * 
         */

        // 戻り情報
        IssuesPlanningStyleSettingsMasterSelectServiceOutDto outDto = new IssuesPlanningStyleSettingsMasterSelectServiceOutDto();
        // 課題立案様式設定マスタ一覧情報リストを設定する
        outDto.setKghMocKrkFree1TitleInfoList(kghMocKrkFree1TitleInfoList);
        // 課題立案様式設定情報（画面生成用データ）を設定
        outDto.setKghMocKrkFree1Info(kghMocKrkFree1Info);
        // 課題立案表データリストを設定
        outDto.setKghMocKrkFree2InfoList(kghMocKrkFree2InfoList);
        // 使用フラグを設定
        outDto.setUseFlg(count > CommonConstants.NUMBER_0 ? CommonConstants.USE_FLG_USE : CommonConstants.USE_FLG_NOT_USE);
        LOG.info(Constants.END);
        return outDto;
    }
}
