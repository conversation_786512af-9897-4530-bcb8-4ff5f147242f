package jp.ndsoft.carebase.cmn.api.logic;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.dto.LeavingInfoRecordPrintSettingsPeriodHistoryDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnTaiKirokuReqRirekiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnTaiKirokuReqRirekiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinInfoListSpaceSortByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinInfoListSpaceSortOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.TaiJohoKirokuRirekiSelectMapper;

/**
 * GUI01310_印刷設定 利用者切替共通クラス
 * 
 * <AUTHOR>
 */
@Component
public class LeavingInfoRecordPrintSettingsLogic {
    /** 退院退所情報記録履歴を取得する */
    @Autowired
    private TaiJohoKirokuRirekiSelectMapper taiJohoKirokuRirekiSelectMapper;

    /** 職員一覧取得 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /***
     * API定義書_APINo(1168)_利用者切替の2. 退院退所情報記録履歴
     * 
     * @param svJigyoId 事業者ID
     * @param userId    利用者ID
     * @param kikanFlg  期間管理フラグ
     * 
     * @return アセスメント履歴リスト
     */
    public List<LeavingInfoRecordPrintSettingsPeriodHistoryDto> getPeriodHistoryList(String svJigyoId, String userId,
            String kikanFlg) {
        // 2.1. リクエストパラメータ.利用者IDが０以外の場合、下記のアセスメント履歴一覧取得DAOを利用し、退院退所情報記録履歴を取得する。
        KghCpnTaiKirokuReqRirekiByCriteriaInEntity kghCpnTaiKirokuReqRirekiByCriteriaInEntity = new KghCpnTaiKirokuReqRirekiByCriteriaInEntity();
        // 事業者ID
        kghCpnTaiKirokuReqRirekiByCriteriaInEntity.setAnJigyoId(CommonDtoUtil.strValToInt(svJigyoId));
        // 利用者ID
        kghCpnTaiKirokuReqRirekiByCriteriaInEntity.setAnUserid(CommonDtoUtil.strValToInt(userId));

        List<KghCpnTaiKirokuReqRirekiOutEntity> kghCpnTaiKirokuReqRirekiList = this.taiJohoKirokuRirekiSelectMapper
                .findKghCpnTaiKirokuReqRirekiByCriteria(kghCpnTaiKirokuReqRirekiByCriteriaInEntity);

        // 2.2.1.下記の職員一覧取得DAOを利用し、職員一覧を取得する。
        ShokuinInfoListSpaceSortByCriteriaInEntity shokuinInfoListSpaceSortByCriteriaInEntity = new ShokuinInfoListSpaceSortByCriteriaInEntity();

        List<ShokuinInfoListSpaceSortOutEntity> shokuinInfoList = this.comMscShokuinSelectMapper
                .findShokuinInfoListSpaceSortByCriteria(shokuinInfoListSpaceSortByCriteriaInEntity);

        // 2.2.2.上記2.1設定したアセスメント履歴一覧再編集処理
        List<LeavingInfoRecordPrintSettingsPeriodHistoryDto> periodHistoryList = new ArrayList<LeavingInfoRecordPrintSettingsPeriodHistoryDto>();

        // (2).計画期間管理フラグよりアセスメント履歴一覧を下記ソート処理する
        // ①.リクエストパラメータ.期間管理フラグは（1：管理する）の場合
        // ソートキー：
        // 開始日 降序,
        // 終了日 降序,
        // 期間ID 降序,
        // 記入日 降序,
        // 履歴ID 降序
        if (CollectionUtils.isNotEmpty(kghCpnTaiKirokuReqRirekiList)) {
            if (CommonDtoUtil.checkStringEqual(kikanFlg, CommonConstants.PERIOD_MANAGE_FLG)) {
                kghCpnTaiKirokuReqRirekiList.sort(Comparator
                        .comparing(
                                KghCpnTaiKirokuReqRirekiOutEntity::getStartYmd,
                                Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(
                                KghCpnTaiKirokuReqRirekiOutEntity::getEndYmd,
                                Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(
                                KghCpnTaiKirokuReqRirekiOutEntity::getSc1Id,
                                Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(
                                KghCpnTaiKirokuReqRirekiOutEntity::getCreateYmd,
                                Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(
                                KghCpnTaiKirokuReqRirekiOutEntity::getRecId,
                                Comparator.nullsLast(Comparator.reverseOrder())));
            } else {
                // ②.リクエストパラメータ.期間管理フラグは（0：管理しない）の場合
                // ソートキー：
                // 記入日 降序,
                // 履歴ID降序
                kghCpnTaiKirokuReqRirekiList.sort(Comparator
                        .comparing(
                                KghCpnTaiKirokuReqRirekiOutEntity::getCreateYmd,
                                Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(
                                KghCpnTaiKirokuReqRirekiOutEntity::getRecId,
                                Comparator.nullsLast(Comparator.reverseOrder())));

            }

            for (KghCpnTaiKirokuReqRirekiOutEntity kghCpnTaiKirokuReqRireki : kghCpnTaiKirokuReqRirekiList) {
                // (1).作成者を設定
                // アセスメント履歴一覧.利用者IDと上記2.2.1取得した職員基本一覧情報.職員ID紐づく、対象レコードを抽出する
                // アセスメント履歴一覧.作成者＝対象レコード.職員名（姓）+ " "+対象レコード.職員名（名）
                String chkShokuId = CommonConstants.BLANK_STRING;

                for (ShokuinInfoListSpaceSortOutEntity shokuinInfo : shokuinInfoList) {
                    if (kghCpnTaiKirokuReqRireki.getChkShokuId() == shokuinInfo.getChkShokuId()) {
                        chkShokuId = shokuinInfo.getShokuin1Knj()
                                + CommonConstants.BLANK_SPACE
                                + shokuinInfo.getShokuin2Knj();
                        break;
                    }
                }

                // アセスメント履歴を設定する。
                LeavingInfoRecordPrintSettingsPeriodHistoryDto leavingInfoRecordPrintSettingsPeriodHistoryDto = this
                        .setPeriodHistory(kghCpnTaiKirokuReqRireki, chkShokuId);

                periodHistoryList.add(leavingInfoRecordPrintSettingsPeriodHistoryDto);

            }

        }

        return periodHistoryList;

    }

    /**
     * アセスメント履歴を設定する。
     * 
     * @param kghCpnRaiMonKentPrnReqRrkSel アセスメント履歴情報
     * @param chkShokuId                   作成者
     * 
     * @return インターライ方式履歴
     */
    private LeavingInfoRecordPrintSettingsPeriodHistoryDto setPeriodHistory(
            KghCpnTaiKirokuReqRirekiOutEntity kghCpnTaiKirokuReqRireki, String chkShokuId) {
        LeavingInfoRecordPrintSettingsPeriodHistoryDto leavingInfoRecordPrintSettingsPeriodHistoryDto = new LeavingInfoRecordPrintSettingsPeriodHistoryDto();

        // 期間ID
        leavingInfoRecordPrintSettingsPeriodHistoryDto
                .setSc1Id(CommonDtoUtil.objValToString(kghCpnTaiKirokuReqRireki.getSc1Id()));
        // 開始日
        leavingInfoRecordPrintSettingsPeriodHistoryDto.setStartYmd(kghCpnTaiKirokuReqRireki.getStartYmd());
        // 終了日
        leavingInfoRecordPrintSettingsPeriodHistoryDto.setEndYmd(kghCpnTaiKirokuReqRireki.getEndYmd());

        // 選択
        leavingInfoRecordPrintSettingsPeriodHistoryDto
                .setSel(CommonDtoUtil.objValToString(kghCpnTaiKirokuReqRireki.getSel()));
        // 履歴ID
        leavingInfoRecordPrintSettingsPeriodHistoryDto
                .setRecId(CommonDtoUtil.objValToString(kghCpnTaiKirokuReqRireki.getRecId()));

        // 記入日
        leavingInfoRecordPrintSettingsPeriodHistoryDto
                .setCreateYmd(CommonDtoUtil.objValToString(kghCpnTaiKirokuReqRireki.getCreateYmd()));

        // 作成者
        leavingInfoRecordPrintSettingsPeriodHistoryDto.setChkShokuId(chkShokuId);

        return leavingInfoRecordPrintSettingsPeriodHistoryDto;
    }
}