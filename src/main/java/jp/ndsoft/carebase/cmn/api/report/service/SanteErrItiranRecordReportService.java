package jp.ndsoft.carebase.cmn.api.report.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.invoke.MethodHandles;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.BaseDataInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ItiranDetailInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.SanteErrItiranRecordReportPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.SanteErrItiranRecordReportPrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.SanteErrItiranRecordReportSubjectHistory;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.SanteErrItiranRecordReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.SanteErrItiranRecordReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.model.SanteErrItiranRecordReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnAllPlanCalcPrnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnAllPlanCalcPrnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YoushikiNoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YoushikiNoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocPrtYoushikiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * V00261_限度額超過利用者・算定エラー利用者一覧
 *
 * <AUTHOR>
 * @since 2025.09.01
 */
@Service("SanteErrItiranRecordReport")
public class SanteErrItiranRecordReportService extends
        PdfReportServiceImpl<SanteErrItiranRecordReportParameterModel, SanteErrItiranRecordReportServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** KghCmpF01Logicクラス */
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    /** サービス種類から総合事業かどうかをチェックする */
    private KghCmnF01Logic kghCmnF01Logic;

    @Autowired
    private ComMocPrtYoushikiSelectMapper comMocPrtYoushikiSelectMapper;

    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;

    /**
     * 帳票パラメータ取得
     *
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(SanteErrItiranRecordReportParameterModel model,
            SanteErrItiranRecordReportServiceOutDto outDto) throws Exception {
        LOG.info(Constants.START);

        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 【リクエストパラメータ】.印刷設定
        SanteErrItiranRecordReportPrintSet printSet = model.getPrintSet();

        // 【リクエストパラメータ】.印刷オプション
        SanteErrItiranRecordReportPrintOption printOption = model.getPrintOption();

        // 【リクエストパラメータ】.印刷対象履歴リスト
        SanteErrItiranRecordReportSubjectHistory printSubjectHistory = model.getPrintSubjectHistoryList().get(0);

        // 【リクエストパラメータ】.基データリスト
        List<BaseDataInfo> baseDataList = model.getBaseDataList();

        // 帳票用データ詳細
        SanteErrItiranRecordReportServiceInDto infoInDto = new SanteErrItiranRecordReportServiceInDto();

        // 1.帳票タイトル
        // リクエストパラメータ.データ.印刷対象履歴リスト[0].出力帳票印刷情報リスト[0].帳票タイトル
        String title = printSubjectHistory.getChoPrtList().get(0).getPrtTitle();
        infoInDto.setTitle(title);

        // 2.指定日印刷区分
        // リクエストパラメータ.データ.印刷設定.指定日印刷区分
        Integer shiTeiKubun = CommonDtoUtil.strValToInt(printSet.getShiTeiKubun());
        infoInDto.setShiTeiKubun(shiTeiKubun);

        // 3.出力日
        String shiteiDateGG = "";
        String shiteiDateYY = "";
        String shiteiDateMM = "";
        String shiteiDateDD = "";
        switch (shiTeiKubun) {
            case ReportConstants.PRINTDATE_KBN_SHINAI_1:
                // 印刷しないの場合
                break;
            case ReportConstants.PRINTDATE_KBN_PRINT_2:
                // 指定日印刷の場合
                // 共通関数補足の「2.1」を行う,パラメータ.西暦日付にリクエストパラメータ.印刷設定.指定日を設定
                List<String> shiTeiDateList = ReportUtil.getLocalDateToJapanDateTimeFormat(printSet.getShiTeiDate());
                if (CollectionUtils.isNotEmpty(shiTeiDateList)) {
                    shiteiDateGG = shiTeiDateList.get(ReportConstants.SHITEIDATE_GG);
                    shiteiDateYY = shiTeiDateList.get(ReportConstants.SHITEIDATE_YY);
                    shiteiDateMM = shiTeiDateList.get(ReportConstants.SHITEIDATE_MM);
                    shiteiDateDD = shiTeiDateList.get(ReportConstants.SHITEIDATE_DD);
                }
                break;
            case ReportConstants.PRINTDATE_KBN_BLANKDATE_3:
                // 日付空欄印刷の場合
                // 共通関数補足の「1.1」のシステム日付元号をshiTeiDateGGに設定,shiTeiDateYY、shiTeiDateMM、shiTeiDateDDに""を設定
                shiteiDateGG = this.nds3GkFunc01Logic.blankDate(model.getSystemDate());
                shiteiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                shiteiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                shiteiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            default:
                break;
        }
        infoInDto.setShiTeiDateGG(shiteiDateGG);
        infoInDto.setShiTeiDateYY(shiteiDateYY);
        infoInDto.setShiTeiDateMM(shiteiDateMM);
        infoInDto.setShiTeiDateDD(shiteiDateDD);

        // 4.支援事業者名
        // 共通関数補足の処理「8.1.」の事業名
        String jigyoName = this.kghCmpF01Logic.getShienName(baseDataList.get(0).getShienId());
        infoInDto.setJigyoName(jigyoName);

        // API定義の「3」処理を行う。
        // 3. リクエストパラメータ.データ.基データリストを絞込み、下記処理を行う。
        // 3.1. リクエストパラメータ.データ.基データリストの件数判断処理を行う。
        if (CollectionUtils.isNullOrEmpty(baseDataList)) {
            // リクエストパラメータ.データ.基データリストの件数＜1の場合、処理を終了する。
            return infoInDto;
        }
        // 3.2. 基本データリスト.データタイプ＝2、3で下記処理を繰り返し、帳票用データ詳細を編集する。

        // 種類限度額
        Integer[] shuNum = {0};

        // 3.2.1. 下記条件で基本データリストを絞込み、データタイプ＝2の基本データリストを作成する。
        List<BaseDataInfo> baseDataType2List = baseDataList.stream()
                .filter(entity -> CommonConstants.INT_2 == entity.getDatatype()).toList();

        // 作成した基本データリストを引数とし渡して、「3.3.」処理へ、帳票用データ詳細.一覧詳細リストを作成する。
        List<ItiranDetailInfo> itiranDetailList = getDetailInfos(baseDataType2List, CommonConstants.INT_2, shuNum);

        // 3.2.2. 下記条件で基本データリストを絞込み、データタイプ＝3の基本データリストを作成する。
        List<BaseDataInfo> baseDataType3List = baseDataList.stream()
                .filter(entity -> CommonConstants.INT_3 == entity.getDatatype()).toList();

        // 作成した基本データリストを引数とし渡して、「3.3.」処理へ、帳票用データ詳細.一覧詳細リスト１を作成する。
        List<ItiranDetailInfo> itiranDetailList1 = getDetailInfos(baseDataType3List, CommonConstants.INT_3, shuNum);

        // 3.3.3. 変更日(開始)と変更日(終了)の編集処理
        List<ItiranDetailInfo> allDetailList = new ArrayList<>();
        allDetailList.addAll(itiranDetailList);
        allDetailList.addAll(itiranDetailList1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD);
        // 帳票用データ詳細.変更日(開始)＝帳票用データ詳細.一覧詳細リスト.変更日の最小値
        Optional<LocalDate> minHenkoYmd = allDetailList.stream()
                .map(obj -> LocalDate.parse(obj.getHenkoYmd(), formatter)).min(LocalDate::compareTo);

        // 帳票用データ詳細.変更日(終了)＝帳票用データ詳細.一覧詳細リスト.変更日の最大値
        Optional<LocalDate> maxHenkoYmd = allDetailList.stream()
                .map(obj -> LocalDate.parse(obj.getHenkoYmd(), formatter)).max(LocalDate::compareTo);

        // 5.変更日(開始)
        String henkouStartYmdGG = "";
        String henkouStartYmdYY = "";
        String henkouStartYmdMM = "";
        // API定義の「3.3.3.」の変更日(開始)非空の場合
        if (minHenkoYmd.isPresent()) {
            // 共通関数補足の「3.2」を行う、パラメータ.西暦日付にAPI定義の「3.3.3.」の変更日(開始)を設定する
            List<String> henkouStartDateList = ReportUtil
                    .getLocalDateToJapanDateTimeFormat(minHenkoYmd.get().format(formatter));
            if (CollectionUtils.isNotEmpty(henkouStartDateList)) {
                henkouStartYmdGG = henkouStartDateList.get(ReportConstants.SHITEIDATE_GG);
                henkouStartYmdYY = henkouStartDateList.get(ReportConstants.SHITEIDATE_YY);
                henkouStartYmdMM = henkouStartDateList.get(ReportConstants.SHITEIDATE_MM);
            }
        } else {
            // API定義の「3.3.3.」の変更日(開始)＝空白の場合、henkouStartYmdGG、henkouStartYmdYY、henkouStartYmdMMに""を設定
            henkouStartYmdGG = CommonConstants.BLANK_STRING;
            henkouStartYmdYY = CommonConstants.BLANK_STRING;
            henkouStartYmdMM = CommonConstants.BLANK_STRING;
        }
        infoInDto.setHenkouStartYmdGG(henkouStartYmdGG);
        infoInDto.setHenkouStartYmdYY(henkouStartYmdYY);
        infoInDto.setHenkouStartYmdMM(henkouStartYmdMM);

        // 6.変更日(終了)
        String henkouEndYmdGG = "";
        String henkouEndYmdYY = "";
        String henkouEndYmdMM = "";
        // API定義の「3.3.3.」の変更日(終了)非空の場合
        if (maxHenkoYmd.isPresent()) {
            // 共通関数補足の「3.2」を行う、パラメータ.西暦日付にAPI定義の「3.3.3.」の変更日(終了)を設定する
            List<String> henkouEndDateList = ReportUtil
                    .getLocalDateToJapanDateTimeFormat(maxHenkoYmd.get().format(formatter));
            if (CollectionUtils.isNotEmpty(henkouEndDateList)) {
                henkouEndYmdGG = henkouEndDateList.get(ReportConstants.SHITEIDATE_GG);
                henkouEndYmdYY = henkouEndDateList.get(ReportConstants.SHITEIDATE_YY);
                henkouEndYmdMM = henkouEndDateList.get(ReportConstants.SHITEIDATE_MM);
            }
        } else {
            // API定義の「3.3.3.」の変更日(終了)＝空白の場合、henkouStartYmdGG、henkouStartYmdYY、henkouStartYmdMMに""を設定
            henkouEndYmdGG = CommonConstants.BLANK_STRING;
            henkouEndYmdYY = CommonConstants.BLANK_STRING;
            henkouEndYmdMM = CommonConstants.BLANK_STRING;
        }
        infoInDto.setHenkouEndYmdGG(henkouEndYmdGG);
        infoInDto.setHenkouEndYmdYY(henkouEndYmdYY);
        infoInDto.setHenkouEndYmdMM(henkouEndYmdMM);

        // 7.変更日期間表示フラグ
        Integer henkouYmdDispFlg = CommonConstants.INT_0;
        // 帳票用データ詳細.変更日(開始)の年月＜＞帳票用データ詳細.変更日(終了)の年月の場合
        if ((!infoInDto.getHenkouStartYmdYY().equals(infoInDto.getHenkouEndYmdYY()))
                || !(infoInDto.getHenkouStartYmdMM().equals(infoInDto.getHenkouEndYmdMM()))) {
            // 帳票用データ詳細.変更日期間表示フラグ＝1
            henkouYmdDispFlg = CommonConstants.INT_1;
        } else {
            // 上記以外の場合
            // 帳票用データ詳細.変更日期間表示フラグ＝0
            henkouYmdDispFlg = CommonConstants.INT_0;
        }
        infoInDto.setHenkouYmdDispFlg(henkouYmdDispFlg);

        // 8.一覧詳細リスト
        infoInDto.setItiranDetailList(new JRBeanCollectionDataSource(itiranDetailList));

        // 9.一覧詳細リスト１
        infoInDto.setItiranDetailList1(new JRBeanCollectionDataSource(itiranDetailList1));

        // 10.文書管理番号
        String bunsyoKanriNo = "";
        // 共通関数補足の「4.1」の設定の読込（システム環境以外の設定）情報を取得する
        F3gkGetProfileInDto inDto = new F3gkGetProfileInDto();
        // システムコード = リクエストパラメータ.データ.システムコード
        inDto.setGsyscd(model.getSyscd());
        // 職員ID = リクエストパラメータ.データ.印刷対象履歴リスト[0].出力帳票印刷情報リスト[0].職員ID
        inDto.setShokuId(CommonDtoUtil.strValToInt(printSubjectHistory.getChoPrtList().get(0).getShokuId()));
        // 法人ID = 0
        inDto.setHoujinId(CommonConstants.INT_0);
        // 施設ID = 0
        inDto.setShisetuId(CommonConstants.INT_0);
        // 事業所ID = 0
        inDto.setSvJigyoId(CommonConstants.INT_0);
        // 機能名 = "PRT"
        inDto.setKinounameKnj(CommonConstants.KINOU_NAME_PRT);
        // セクション = リクエストパラメータ.データ.出力帳票印刷情報リスト[0].セクション
        inDto.setSectionKnj(printSubjectHistory.getChoPrtList().get(0).getSection());
        // キー = ISO9001_FLG
        inDto.setKeyKnj(CommonConstants.KEY_ISO9001_FLG);
        // 初期値 = "0"
        inDto.setAsDefault(CommonConstants.STR_0);
        String result = this.nds3GkFunc01Logic.getF3gkProfile(inDto);
        // 共通関数補足の「4.2」設定の読込（システム環境以外の設定）情報取得の戻り値は"1"（正常参照）の場合、下記の24-06
        // 帳票書式番号設定マスタ情報取得のDAOを利用し、文書番号を取得する。
        if (CommonConstants.STR_1.equals(result)) {
            YoushikiNoByCriteriaInEntity entity = new YoushikiNoByCriteriaInEntity();
            // サービス事業者ID = リクエストパラメータ.事業者情報.サービス事業者ID
            entity.setAsSection(printSubjectHistory.getChoPrtList().get(0).getSection());
            // セクション = リクエストパラメータ.データ.出力帳票印刷情報リスト[0].セクション
            entity.setLlSvJigyoId(model.getJigyoInfo().getSvJigyoId());
            List<YoushikiNoOutEntity> entityList = this.comMocPrtYoushikiSelectMapper.findYoushikiNoByCriteria(entity);
            if (CollectionUtils.isNotEmpty(entityList)) {
                bunsyoKanriNo = entityList.get(0).getYoushikiNo();
            }
        }
        infoInDto.setBunsyoKanriNo(bunsyoKanriNo);

        // 11.種類限度表示フラグ
        Integer shuGendoDispFlg = 0;
        // 3.3.2. 種類限度表示フラグの編集処理
        // 変数.種類限度額＞0の場合
        if (shuNum[0] > 0) {
            // 帳票用データ詳細.種類限度表示フラグ＝1
            shuGendoDispFlg = CommonConstants.INT_1;
        } else {
            // 上記以外の場合
            // 帳票用データ詳細.種類限度表示フラグ＝0
            shuGendoDispFlg = CommonConstants.INT_0;
        }
        infoInDto.setShuGendoDispFlg(shuGendoDispFlg);

        // 12.フル表示フラグ
        Integer fullDispFlg = 0;
        // 3.2.3. 、帳票用データ詳細.一覧詳細リストの件数＞0 かつ 帳票用データ詳細.一覧詳細リスト１の件数＞0の場合
        if (CollectionUtils.isNotEmpty(itiranDetailList) && CollectionUtils.isNotEmpty(itiranDetailList1)) {
            // 帳票用データ詳細.フル表示フラグ＝1
            fullDispFlg = CommonConstants.INT_1;
        }
        infoInDto.setFullDispFlg(fullDispFlg);

        // 13.表示フラグ
        Integer dispFlg = 0;
        if (CollectionUtils.isNotEmpty(itiranDetailList) && CollectionUtils.isNullOrEmpty(itiranDetailList1)){
            dispFlg =  CommonConstants.INT_2;
        }else if(CollectionUtils.isNotEmpty(itiranDetailList1) && CollectionUtils.isNullOrEmpty(itiranDetailList)){
            dispFlg =  CommonConstants.INT_3;
        }
        infoInDto.setDispFlg(dispFlg);

        // ノート情報格納配列
        List<SanteErrItiranRecordReportServiceInDto> infoInDtoList = new ArrayList<SanteErrItiranRecordReportServiceInDto>();

        infoInDtoList.add(infoInDto);

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(infoInDtoList);
        infoInDto.setDataSource(dataSource);

        LOG.info(Constants.END);
        return infoInDto;
    }

    /**
     * 帳票出力
     *
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final SanteErrItiranRecordReportParameterModel model,
            final SanteErrItiranRecordReportServiceOutDto outDto) throws Exception {

        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final SanteErrItiranRecordReportServiceInDto reportParameter = (SanteErrItiranRecordReportServiceInDto) getReportParameters(
                model, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(getFwProps(),
                        ReportConstants.JRXML_V00261_SANTEERR_ITIRAN_RECORD_REPORT)));

        // コンパイル
        final JasperReport jasperFile = JasperCompileManager.compileReport(is);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile,
                reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(model, jasperPrint);

        /*
         * ===============6.レスポンスを返却===============
         *
         */
        super.setFilepath(model, outDto, pdf, pdf.getName());
        LOG.info(Constants.END);
    }

    /**
     * 帳票用データ詳細を取得する。
     * 3.3. 帳票用データ詳細を編集する。
     * 
     * @param baseDataList 基データリスト
     * @param dispFlag     表示フラグ
     * @param shuNum       種類限度額
     * @return 一覧詳細リスト
     */
    private List<ItiranDetailInfo> getDetailInfos(List<BaseDataInfo> baseDataList, Integer dispFlag, Integer[] shuNum) {
        List<ItiranDetailInfo> detailInfos = new ArrayList<>();
        Integer j = 0;
        // 3.3.1 基データリスト件数分で基データレコードを繰り返して、下記処理を行う。
        for (j = 0; j < baseDataList.size(); j++) {
            // ①基データリスト[変数.j].ユーザーID ＞ 0の場合、下記処理を行う。
            Integer userId = baseDataList.get(j).getUserId();
            if (userId > 0) {
                // ①-1 利用者情報リストから利用者IDを取得する。
                CmnAllPlanCalcPrnByCriteriaInEntity entity = new CmnAllPlanCalcPrnByCriteriaInEntity();
                List<Integer> idList = new ArrayList<Integer>();
                idList.add(userId);
                entity.setAlUidList(idList);
                List<CmnAllPlanCalcPrnOutEntity> entityList = this.comTucUserSelectMapper.findCmnAllPlanCalcPrnByCriteria(entity);

                // ①-2 データ編集
                ItiranDetailInfo detailInfo = new ItiranDetailInfo();

                // 帳票用データ詳細.一覧リスト[変数.index].支援ID＝基データリスト[変数.j].支援ID
                detailInfo.setShienId(baseDataList.get(j).getShienId());

                // 帳票用データ詳細.一覧リスト[変数.index].表示フラグ＝変数.表示フラグ
                detailInfo.setDispFlg(dispFlag);

                // 帳票用データ詳細.一覧リスト[変数.index].ユーザーID＝基データリスト[変数.j].ユーザーID
                detailInfo.setUserId(baseDataList.get(j).getUserId());

                // 帳票用データ詳細.一覧リスト[変数.index].種類限度超過＝基データリスト[変数.j].種類限度超過,フォーマット(###,###,###)
                detailInfo.setShuOver(String.format("%,d", CommonDtoUtil.strValToInt(baseDataList.get(j).getStensuOv())));

                // 帳票用データ詳細.一覧リスト[変数.index].種類限度内＝基データリスト[変数.j].種類限度内,フォーマット(###,###,###)
                detailInfo.setShuNai(String.format("%,d", CommonDtoUtil.strValToInt(baseDataList.get(j).getStensu())));

                // 帳票用データ詳細.一覧リスト[変数.index].区分限度超過＝基データリスト[変数.j].区分限度超過,フォーマット(###,###,###)
                detailInfo.setKbnOver(String.format("%,d", CommonDtoUtil.strValToInt(baseDataList.get(j).getKtensuOv())));

                // 帳票用データ詳細.一覧リスト[変数.index].区分限度内＝基データリスト[変数.j].区分限度内,フォーマット(###,###,###)
                detailInfo.setKbnNai(String.format("%,d", CommonDtoUtil.strValToInt(baseDataList.get(j).getKtensu())));

                // 帳票用データ詳細.一覧リスト[変数.index].エラー内容＝基データリスト[変数.j].エラー内容
                detailInfo.setErrKnj(baseDataList.get(j).getErrKnj());

                // 帳票用データ詳細.一覧リスト[変数.index].エラーCD＝基データリスト[変数.j].エラーCD
                detailInfo.setErrCode(baseDataList.get(j).getErrCode());

                // 帳票用データ詳細.一覧リスト[変数.index].保険者名＝基データリスト[変数.j].保険者名
                detailInfo.setHokenKnj(baseDataList.get(j).getKhokenKnj());

                // 帳票用データ詳細.一覧リスト[変数.index].被保険者番号＝基データリスト[変数.j].被保険者番号
                detailInfo.setHiHoken(baseDataList.get(j).getHhokenNo());

                // 帳票用データ詳細.一覧リスト[変数.index].変更日＝基データリスト[変数.j].変更日
                detailInfo.setHenkoYmd(baseDataList.get(j).getHenkouYmd());

                // 変数.種類限度額＝変数.種類限度額＋基データリスト[変数.j].種類限度超過
                shuNum[0] = shuNum[0] +CommonDtoUtil.strValToInt(baseDataList.get(j).getStensuOv());

                // ①-3 「①-1」で利用者IDが取得できる場合
                Boolean hasData = CollectionUtils.isNotEmpty(entityList);
                if (hasData) {
                    // 帳票用データ詳細.一覧リスト[変数.index].利用者番号＝利用者情報リスト.利用者番号
                    detailInfo.setSelfId(entityList.get(0).getSelfId());

                    // 帳票用データ詳細.一覧リスト[変数.index].性別の編集
                    Integer sex = entityList.get(0).getSex();
                    switch (sex) {
                        case 0:
                            // 利用者情報リスト.性別＝0の場合
                            // 帳票用データ詳細.一覧リスト[変数.index].性別＝"女"
                            detailInfo.setSex(CommonConstants.STR_FEMALE);
                            break;
                        case 1:
                            // 利用者情報リスト.性別＝1の場合
                            // 帳票用データ詳細.一覧リスト[変数.index].性別＝"男"
                            detailInfo.setSex(CommonConstants.STR_MALE);
                            break;
                        case 2:
                            // 利用者情報リスト.性別＝2の場合
                            // 帳票用データ詳細.一覧リスト[変数.index].性別＝"?"
                            detailInfo.setSex("?");
                            break;
                        default:
                            // 上記以外の場合
                            // 帳票用データ詳細.一覧リスト[変数.index].性別＝""
                            detailInfo.setSex(CommonConstants.BLANK_STRING);
                            break;
                    }

                    // 帳票用データ詳細.一覧リスト[変数.index].フリガナ（姓）＝利用者情報リスト.フリガナ（姓）
                    detailInfo.setName1Kana(entityList.get(0).getName1Kana());

                    // 帳票用データ詳細.一覧リスト[変数.index].フリガナ（名）＝利用者情報リスト.フリガナ（名）
                    detailInfo.setName2Kana(entityList.get(0).getName2Kana());

                    // 帳票用データ詳細.一覧リスト[変数.index].氏名（姓）＝利用者情報リスト.氏名（姓）
                    detailInfo.setName1Knj(entityList.get(0).getName1Knj());

                    // 帳票用データ詳細.一覧リスト[変数.index].氏名（名）＝利用者情報リスト.氏名（名）
                    detailInfo.setName2Knj(entityList.get(0).getName2Knj());

                    // 帳票用データ詳細.一覧リスト[変数.index].利用者氏名＝利用者情報リスト.氏名（姓）＋" "＋利用者情報リスト.氏名（名）
                    detailInfo.setUserName(detailInfo.getName1Knj() + CommonConstants.FULL_WIDTH_SPACE + detailInfo.getName2Knj());

                    // 帳票用データ詳細.一覧リスト[変数.index].フルフリガナ＝利用者情報リスト.フリガナ（姓）＋" "＋利用者情報リスト.フリガナ（名）
                    detailInfo.setFullKana(detailInfo.getName1Kana() + CommonConstants.FULL_WIDTH_SPACE + detailInfo.getName2Kana());

                    // 帳票用データ詳細.一覧リスト[変数.index].生年月日＝利用者情報リスト.生年月日
                    detailInfo.setBirthdayYmd(entityList.get(0).getBirthdayYmd());
                } else {
                    // 帳票用データ詳細.一覧リスト[変数.index].利用者番号＝共通関数補足の5.1のgetUserSelfId(基データリスト[変数.j].ユーザーID)
                    Integer selfId = this.kghCmnF01Logic.getUserSelfId(baseDataList.get(j).getUserId());
                    detailInfo.setSelfId(selfId);

                    // 帳票用データ詳細.一覧リスト[変数.index].性別＝"?"
                    detailInfo.setSex("?");

                    // 帳票用データ詳細.一覧リスト[変数.index].フリガナ（姓）＝共通関数補足の6.1のgetUserNameKana(基データリスト[変数.j].ユーザーID)
                    String nameKana = this.kghCmnF01Logic.getUserNameKana(baseDataList.get(j).getUserId());
                    detailInfo.setName1Kana(nameKana);

                    // 帳票用データ詳細.一覧リスト[変数.index].フリガナ（名）＝""
                    detailInfo.setName2Kana(CommonConstants.BLANK_STRING);

                    // 帳票用データ詳細.一覧リスト[変数.index].氏名（姓）＝共通関数補足の7.1のgetUserName(基データリスト[変数.j].ユーザーID)
                    String nameKnj = this.kghCmpF01Logic.getUserName(baseDataList.get(j).getUserId());
                    detailInfo.setName1Knj(nameKnj);

                    // 帳票用データ詳細.一覧リスト[変数.index].氏名（名）＝""
                    detailInfo.setName2Knj(CommonConstants.BLANK_STRING);

                    // 帳票用データ詳細.一覧リスト[変数.index].利用者氏名＝帳票用データ詳細.一覧リスト[変数.index].氏名（姓）＋"
                    // "＋帳票用データ詳細.一覧リスト[変数.index].氏名（名）
                    detailInfo.setUserName(
                            detailInfo.getName1Knj() + CommonConstants.FULL_WIDTH_SPACE + detailInfo.getName2Knj());

                    // 帳票用データ詳細.一覧リスト[変数.index].フルフリガナ＝帳票用データ詳細.一覧リスト[変数.index].フリガナ（姓）＋"
                    // "＋帳票用データ詳細.一覧リスト[変数.index].フリガナ（名）
                    detailInfo.setFullKana(
                            detailInfo.getName1Kana() + CommonConstants.FULL_WIDTH_SPACE + detailInfo.getName2Kana());

                    // 帳票用データ詳細.一覧リスト[変数.index].生年月日＝""
                    detailInfo.setBirthdayYmd(CommonConstants.BLANK_STRING);
                }
                detailInfos.add(detailInfo);
            }
        }

        // 3.3.4. 帳票用データ詳細リストのソート処理
        // ソート項目：
        // 帳票用データ詳細.一覧リスト.表示フラグ 昇順
        // 帳票用データ詳細.一覧リスト.フルフリガナ 昇順
        // 帳票用データ詳細.一覧リスト.利用者番号 昇順
        // 帳票用データ詳細.一覧リスト.ユーザーID 昇順
        // 帳票用データ詳細.一覧リスト.変更日 昇順
        // 帳票用データ詳細.一覧リスト.被保険者番号 昇順
        // 帳票用データ詳細.一覧リスト.保険者名 昇順
        List<ItiranDetailInfo> orderdDetailInfos = detailInfos.stream().sorted(
                Comparator.comparing(ItiranDetailInfo::getDispFlg)
                        .thenComparing(Comparator.comparing(ItiranDetailInfo::getFullKana))
                        .thenComparing(Comparator.comparing(ItiranDetailInfo::getSelfId))
                        .thenComparing(Comparator.comparing(ItiranDetailInfo::getUserId))
                        .thenComparing(Comparator.comparing(ItiranDetailInfo::getHenkoYmd))
                        .thenComparing(Comparator.comparing(ItiranDetailInfo::getHiHoken))
                        .thenComparing(Comparator.comparing(ItiranDetailInfo::getHokenKnj)))
                .toList();
        return orderdDetailInfos;
    }

}