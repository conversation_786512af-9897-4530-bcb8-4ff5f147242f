package jp.ndsoft.carebase.cmn.api.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.GyoumuComLogic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComPrintSettingsInputDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComPrintSettingsSaveDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComSystemIniDto;
import jp.ndsoft.carebase.cmn.api.report.dto.UserInfoDto;
import jp.ndsoft.carebase.cmn.api.service.dto.CmnTucPlanDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanAllPrintSettingsSubjectSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanAllPrintSettingsSubjectSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PrintSettingsHistoryDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekRirekiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekRirekiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.IkkatuPrintDayByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.IkkatuPrintDayOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.IkkatuPrintKkak1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.IkkatuPrintKkak1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.IkkatuPrintKkak2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.IkkatuPrintKkak2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.IkkatuPrintWeekByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.IkkatuPrintWeekOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnPlanAllCalcUListByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnPlanAllCalcUListOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinHogoJohoSetteiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinHogoJohoSetteiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSysiniSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks51SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.IkkatuPrintDaySelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.IkkatuPrintKkak1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.IkkatuPrintKkak2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.IkkatuPrintWeekSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ServiceRiyouhyouInfoSelectMapper;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * GUI04513_印刷設定
 * APINo(1265)_初期情報取得
 *
 * <AUTHOR>
 */
@Service
public class PlanAllPrintSettingsSubjectSelectServiceImpl extends
        SelectServiceImpl<PlanAllPrintSettingsSubjectSelectServiceInDto, PlanAllPrintSettingsSubjectSelectServiceOutDto> {

    /** 事業所CD: "30010","50010","61084","23031","61104","71084","43031" */
    private static final List<String> OFFICE_CD_LIST = Arrays.asList("30010", "50010", "61084", "23031", "61104",
            "71084", "43031");

    /** 初期設定マスタ.計画書様式: 1 */
    public static final String VAL_CLS_FLG_1 = "1";
    /** 初期設定マスタ.計画書様式: 2 */
    public static final String VAL_CLS_FLG_2 = "2";
    /** char: 1 */
    public static final char CHAR_1 = '1';
    /** 帳票種類: 1 */
    public static final String PRTIDX_1 = "1";
    /** 帳票種類: 2 */
    public static final String PRTIDX_2 = "2";
    /** 帳票種類: 5 */
    public static final String PRTIDX_5 = "5";
    /** 帳票種類: 6 */
    public static final String PRTIDX_6 = "6";
    /** 帳票種類: 7 */
    public static final String PRTIDX_7 = "7";
    /** 帳票種類: 8 */
    public static final String PRTIDX_8 = "8";
    /** 事業所コード: 30010 */
    public static final String JCD_30010 = "30010";
    /** 事業所コード: 50010 */
    public static final String JCD_50010 = "50010";
    /** セクションNOのU0081S */
    private static final String SECTION_NO_U0081S = "U0081S";
    /** セクションNOのU0081K */
    private static final String SECTION_NO_U0081K = "U0081K";
    /** セクションNOのU0082S */
    private static final String SECTION_NO_U0082S = "U0082S";
    /** セクションNOのU0082K */
    private static final String SECTION_NO_U0082K = "U0082K";
    /** セクションNOのU00852 */
    private static final String SECTION_NO_U00852 = "U00852";
    /** セクションNOのU00861 */
    private static final String SECTION_NO_U00861 = "U00861";
    /** セクションNOのV00231 */
    private static final String SECTION_NO_V00231 = "V00231";
    /** 帳票番号: 1 */
    public static final String PRTNO_1 = "1";
    /** 帳票番号: 3 */
    public static final String PRTNO_3 = "3";
    /** 帳票番号: 4 */
    public static final String PRTNO_4 = "4";
    /** 文字列"P" */
    private static final String STR_P = "P";
    /** 文字列"履歴がありません" */
    private static final String STR_RESULT = "履歴がありません";
    /** 画面名 */
    private static final String SCREEN_NAME = "PRT";
    /** 伏字設定 */
    private static final String AMIKAKE_FLG = "amikake_flg";
    /** 個人情報設定 */
    private static final String KOJINHOGO_FLG = "kojinhogo_flg";
    /** 初期値 */
    private static final String DEFAULT = "1";
    /** システムコード */
    private static final String SYSTEM_CODE = "71101";

    @Autowired
    private ServiceRiyouhyouInfoSelectMapper serviceRiyouhyouInfoSelectMapper;

    @Autowired
    private IkkatuPrintKkak1SelectMapper kkatuPrintKkak1SelectMapper;

    @Autowired
    private IkkatuPrintKkak2SelectMapper kkatuPrintKkak2SelectMapper;

    @Autowired
    private IkkatuPrintWeekSelectMapper kkatuPrintWeekSelectMapper;

    @Autowired
    private IkkatuPrintDaySelectMapper kkatuPrintDaySelectMapper;

    @Autowired
    private CpnTucCks51SelectMapper cpnTucCks51SelectMapper;

    /** 業務共通用 */
    @Autowired
    private GyoumuComLogic gyoumuComLogic;

    /** 新書式の場合、個人情報保護設定の取得 */
    @Autowired
    private ComMocSysiniSelectMapper comMocSysiniSelectMapper;

    /** 個人情報保護フラグの取得関数 */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /**
     * 初期情報取得
     * 
     * @param inDto 初期情報取得の入力DTO.
     * @return 初期情報取得の出力DTO
     * @throws Exception Exception
     */
    @Override
    protected PlanAllPrintSettingsSubjectSelectServiceOutDto mainProcess(
            PlanAllPrintSettingsSubjectSelectServiceInDto inDto)
            throws Exception {
        PlanAllPrintSettingsSubjectSelectServiceOutDto outDto = new PlanAllPrintSettingsSubjectSelectServiceOutDto();
        List<PrintSettingsHistoryDto> prtHistoryList = new ArrayList<PrintSettingsHistoryDto>();
        // 単項目チェック以外の入力チェック
        // 特になし

        // 利用者選択複数時の印刷対象履歴情報リストの取得
        // 印刷対象取得の準備
        // 帳票選択状態文字列の取得
        String reportSelectionText = "";
        // リクエストパラメータ.事業所CDが"30010","50010","61084","23031","61104","71084","43031"の場合
        if (OFFICE_CD_LIST.contains(inDto.getSvJigyoCD())) {
            reportSelectionText = inDto.getPrtList().get(0).getParam13();
        } else {
            // リクエストパラメータ.初期設定マスタの情報.計画書様式が1:施設の場合
            if (VAL_CLS_FLG_1.equals(inDto.getInitMasterObj().getCksFlg())) {
                reportSelectionText = inDto.getPrtList().get(0).getParam11();
            } else {
                reportSelectionText = inDto.getPrtList().get(0).getParam12();
            }
        }
        // 利用票、利用票別表の印刷用履歴情報取得
        KghCmnPlanAllCalcUListByCriteriaInEntity kghCmnPlanAllCalcUListByCriteriaInEntity = new KghCmnPlanAllCalcUListByCriteriaInEntity();
        // 支援事業者ID
        kghCmnPlanAllCalcUListByCriteriaInEntity.setAlShien(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // サービス提供年月
        kghCmnPlanAllCalcUListByCriteriaInEntity.setAsYymmYm(inDto.getSvYm());
        List<KghCmnPlanAllCalcUListOutEntity> kghCmnPlanAllCalcUList = serviceRiyouhyouInfoSelectMapper
                .findKghCmnPlanAllCalcUListByCriteria(
                        kghCmnPlanAllCalcUListByCriteriaInEntity);
        kghCmnPlanAllCalcUList.sort((o1, o2) -> {
            String dateStr1 = o1.getYymmD();
            String dateStr2 = o2.getYymmD();
            return dateStr2.compareTo(dateStr1);
        });

        // リクエストパラメータ.利用者リストよって、下記の処理を繰り返す
        if (inDto.getUserList().size() > 0) {
            for (UserInfoDto userInfo : inDto.getUserList()) {
                PrintSettingsHistoryDto prtHistory = new PrintSettingsHistoryDto();
                // 帳票選択状態文字列の1番目文字が1:チェックONの場合、計画書（１）の印刷対象履歴リストを取得
                if (reportSelectionText.charAt(0) == CHAR_1) {
                    IkkatuPrintKkak1ByCriteriaInEntity ikkatuPrintKkak1ByCriteriaInEntity = new IkkatuPrintKkak1ByCriteriaInEntity();
                    // 利用者ID
                    ikkatuPrintKkak1ByCriteriaInEntity.setUserid(userInfo.getUserid());
                    // 事業者ID
                    ikkatuPrintKkak1ByCriteriaInEntity.setDefSvJigyoId(inDto.getSvJigyoId());
                    // 作成日
                    ikkatuPrintKkak1ByCriteriaInEntity.setIsYyyymmdd(inDto.getKijunbiYmd());
                    List<IkkatuPrintKkak1OutEntity> kkatuPrintKkak1List = kkatuPrintKkak1SelectMapper
                            .findIkkatuPrintKkak1ByCriteria(ikkatuPrintKkak1ByCriteriaInEntity);
                    // 帳票種類
                    prtHistory.setPrtIdx(PRTIDX_1);
                    // セクション番号
                    // リクエストパラメータ.事業所CDが"30010","50010"の場合
                    if (JCD_30010.equals(inDto.getSvJigyoCD()) || JCD_50010.equals(inDto.getSvJigyoCD())) {
                        prtHistory.setSectionNo(SECTION_NO_U0081K);
                    }
                    // リクエストパラメータ.初期設定マスタの情報.計画書様式が1:施設の場合
                    else if (VAL_CLS_FLG_1.equals(inDto.getInitMasterObj().getCksFlg())) {
                        prtHistory.setSectionNo(SECTION_NO_U0081S);
                    } else {
                        prtHistory.setSectionNo(SECTION_NO_U0081K);
                    }
                    // 帳票番号
                    prtHistory.setPrtNo(PRTNO_1);
                    // 履歴ID
                    if (kkatuPrintKkak1List.size() == 1) {
                        prtHistory.setRirekiId(CommonDtoUtil.objValToString(kkatuPrintKkak1List.get(0).getKs11Id()));
                        // 結果内容
                        prtHistory.setResult(STR_RESULT);
                    }
                }
                // 帳票選択状態文字列の2件目が1:チェックONの場合、計画書（２）の印刷対象履歴リストを取得
                if (reportSelectionText.charAt(1) == CHAR_1) {
                    IkkatuPrintKkak2ByCriteriaInEntity ikkatuPrintKkak2ByCriteriaInEntity = new IkkatuPrintKkak2ByCriteriaInEntity();
                    // 利用者ID
                    ikkatuPrintKkak2ByCriteriaInEntity.setUserid(userInfo.getUserid());
                    // 事業者ID
                    ikkatuPrintKkak2ByCriteriaInEntity.setDefSvJigyoId(inDto.getSvJigyoId());
                    // 作成日
                    ikkatuPrintKkak2ByCriteriaInEntity.setIsYyyymmdd(inDto.getKijunbiYmd());
                    List<IkkatuPrintKkak2OutEntity> kkatuPrintKkak2List = kkatuPrintKkak2SelectMapper
                            .findIkkatuPrintKkak2ByCriteria(
                                    ikkatuPrintKkak2ByCriteriaInEntity);
                    // 帳票種類
                    prtHistory.setPrtIdx(PRTIDX_2);
                    // セクション番号
                    // リクエストパラメータ.事業所CDが"30010","50010"の場合
                    if (JCD_30010.equals(inDto.getSvJigyoCD()) || JCD_50010.equals(inDto.getSvJigyoCD())) {
                        prtHistory.setSectionNo(SECTION_NO_U0082K);
                    }
                    // リクエストパラメータ.初期設定マスタの情報.計画書様式が1:施設の場合
                    else if (VAL_CLS_FLG_1.equals(inDto.getInitMasterObj().getCksFlg())) {
                        prtHistory.setSectionNo(SECTION_NO_U0082S);
                    } else {
                        prtHistory.setSectionNo(SECTION_NO_U0082K);
                    }
                    // 帳票番号
                    prtHistory.setPrtNo(PRTNO_1);
                    // 履歴ID
                    if (kkatuPrintKkak2List.size() == 1) {
                        prtHistory.setRirekiId(CommonDtoUtil.objValToString(kkatuPrintKkak2List.get(0).getKs21Id()));
                        // 結果内容
                        prtHistory.setResult(STR_RESULT);
                    }
                }
                // 帳票選択状態文字列の3件目が1:チェックONの場合、週間計画の印刷対象履歴リストを取得
                if (reportSelectionText.charAt(2) == CHAR_1) {
                    IkkatuPrintWeekByCriteriaInEntity ikkatuPrintWeekByCriteriaInEntity = new IkkatuPrintWeekByCriteriaInEntity();
                    // 利用者ID
                    ikkatuPrintWeekByCriteriaInEntity.setUserid(userInfo.getUserid());
                    // 事業者ID
                    ikkatuPrintWeekByCriteriaInEntity.setDefSvJigyoId(inDto.getSvJigyoId());
                    // 作成日
                    ikkatuPrintWeekByCriteriaInEntity.setIsYyyymmdd(inDto.getKijunbiYmd());
                    List<IkkatuPrintWeekOutEntity> kkatuPrintWeekList = kkatuPrintWeekSelectMapper
                            .findIkkatuPrintWeekByCriteria(
                                    ikkatuPrintWeekByCriteriaInEntity);
                    // 帳票種類
                    prtHistory.setPrtIdx(PRTIDX_5);
                    // セクション番号
                    prtHistory.setSectionNo(SECTION_NO_U00852);
                    // 帳票番号
                    prtHistory.setPrtNo(PRTNO_1);
                    // 履歴ID
                    if (kkatuPrintWeekList.size() == 1) {
                        prtHistory.setRirekiId(CommonDtoUtil.objValToString(kkatuPrintWeekList.get(0).getKs51Id()));
                        // 結果内容
                        prtHistory.setResult(STR_RESULT);
                    }
                }
                // 帳票選択状態文字列の4件目が1:チェックONの場合、日課計画の印刷対象履歴リストを取得
                if (reportSelectionText.charAt(3) == CHAR_1) {
                    IkkatuPrintDayByCriteriaInEntity ikkatuPrintDayByCriteriaInEntity = new IkkatuPrintDayByCriteriaInEntity();
                    // 利用者ID
                    ikkatuPrintDayByCriteriaInEntity.setUserid(userInfo.getUserid());
                    // 事業者ID
                    ikkatuPrintDayByCriteriaInEntity.setDefSvJigyoId(inDto.getSvJigyoId());
                    // 作成日
                    ikkatuPrintDayByCriteriaInEntity.setIsYyyymmdd(inDto.getKijunbiYmd());
                    List<IkkatuPrintDayOutEntity> kkatuPrintDayList = kkatuPrintDaySelectMapper
                            .findIkkatuPrintDayByCriteria(
                                    ikkatuPrintDayByCriteriaInEntity);
                    // 帳票種類
                    prtHistory.setPrtIdx(PRTIDX_6);
                    // セクション番号
                    prtHistory.setSectionNo(SECTION_NO_U00861);
                    // 帳票番号
                    prtHistory.setPrtNo(PRTNO_1);
                    // 履歴ID
                    if (kkatuPrintDayList.size() == 1) {
                        prtHistory.setRirekiId(CommonDtoUtil.objValToString(kkatuPrintDayList.get(0).getNikka1Id()));
                        // 結果内容
                        prtHistory.setResult(STR_RESULT);
                        CpnWeekRirekiByCriteriaInEntity cpnWeekRirekiByCriteriaInEntity = new CpnWeekRirekiByCriteriaInEntity();
                        cpnWeekRirekiByCriteriaInEntity.setKs51(kkatuPrintDayList.get(0).getNikka1Id());
                        List<CpnWeekRirekiOutEntity> cpnWeekRirekiOutList = cpnTucCks51SelectMapper
                                .findCpnWeekRirekiByCriteria(
                                        cpnWeekRirekiByCriteriaInEntity);
                        if (cpnWeekRirekiOutList.size() > 0) {
                            // 計画期間ID
                            prtHistory.setSc1Id(CommonDtoUtil.objValToString(cpnWeekRirekiOutList.get(0).getSc1Id()));
                            // 当該年月
                            prtHistory.setTougaiYm(cpnWeekRirekiOutList.get(0).getTougaiYm());
                            // 様式CD
                            prtHistory.setYousikiCd(
                                    CommonDtoUtil.objValToString(cpnWeekRirekiOutList.get(0).getYousikiCd()));
                        }
                    }
                }
                // 帳票選択状態文字列の5件目が1:チェックONの場合、利用票の印刷対象履歴リストを取得
                if (reportSelectionText.charAt(4) == CHAR_1) {
                    List<KghCmnPlanAllCalcUListOutEntity> userPrintHistoryList = kghCmnPlanAllCalcUList.stream()
                            .filter((x) -> x.getUserid().equals(CommonDtoUtil.strValToInt(userInfo.getUserid())))
                            .toList();
                    // 帳票種類
                    prtHistory.setPrtIdx(PRTIDX_7);
                    // セクション番号
                    prtHistory.setSectionNo(SECTION_NO_V00231);
                    // 帳票番号
                    prtHistory.setPrtNo(PRTNO_3);
                    // サービス利用票・別表ヘッダ
                    prtHistory.setCmnTucPlan(userPrintHistoryList.stream().map((x) -> {
                        CmnTucPlanDto cmnTucPlan = new CmnTucPlanDto();
                        // 利用者番号
                        cmnTucPlan.setSelf_id(CommonDtoUtil.objValToString(x.getSelfId()));
                        // フリガナ（姓）
                        cmnTucPlan.setName1_kana(x.getName1Kana());
                        // フリガナ（名）
                        cmnTucPlan.setName2_kana(x.getName2Kana());
                        // 氏名（姓）
                        cmnTucPlan.setName1_knj(x.getName1Knj());
                        // 氏名（名）
                        cmnTucPlan.setName2_knj(x.getName2Knj());
                        // 性別
                        cmnTucPlan.setSex(CommonDtoUtil.objValToString(x.getSex()));
                        // 利用者ID
                        cmnTucPlan.setUserid(CommonDtoUtil.objValToString(x.getUserid()));
                        // 被保険者番号
                        cmnTucPlan.setH_hoken_no(x.getHHokenNo());
                        // 支援事業者ID
                        cmnTucPlan.setShien_id(CommonDtoUtil.objValToString(x.getShienId()));
                        // サービス提供年月
                        cmnTucPlan.setYymm_ym(x.getYymmYm());
                        // サービス提供年月（変更日）
                        cmnTucPlan.setYymm_d(x.getYymmD());
                        // 予定済みフラグ
                        cmnTucPlan.setYotei_zumi_flg(CommonDtoUtil.objValToString(x.getYoteiZumiFlg()));
                        // 実績済みフラグ
                        cmnTucPlan.setJisseki_zumi_flg(CommonDtoUtil.objValToString(x.getJissekiZumiFlg()));
                        // 確定済みフラグ
                        cmnTucPlan.setKakutei_zumi_flg(CommonDtoUtil.objValToString(x.getKakuteiZumiFlg()));
                        // 保険者番号
                        cmnTucPlan.setK_hoken_cd(CommonDtoUtil.objValToString(x.getKHokenCd()));
                        // 要介護度
                        cmnTucPlan.setYokai_kbn(CommonDtoUtil.objValToString(x.getYokaiKbn()));
                        // 要介護度（変更後）
                        cmnTucPlan.setHen_yokai_kbn(CommonDtoUtil.objValToString(x.getHenYokaiKbn()));
                        return cmnTucPlan;
                    }).toList());
                    if (userPrintHistoryList.size() > 0) {
                        // 結果内容
                        prtHistory.setResult(STR_RESULT);
                    }
                }
                // 帳票選択状態文字列の6件目が1:チェックONの場合、利用票別表の印刷対象履歴リストを取得
                if (reportSelectionText.charAt(5) == CHAR_1) {
                    List<KghCmnPlanAllCalcUListOutEntity> userOtherPrintHistoryList = kghCmnPlanAllCalcUList.stream()
                            .filter((x) -> x.getUserid().equals(CommonDtoUtil.strValToInt(userInfo.getUserid())))
                            .toList();
                    // 帳票種類
                    prtHistory.setPrtIdx(PRTIDX_8);
                    // セクション番号
                    prtHistory.setSectionNo(SECTION_NO_V00231);
                    // 帳票番号
                    prtHistory.setPrtNo(PRTNO_4);
                    // サービス利用票・別表ヘッダ
                    prtHistory.setCmnTucPlan(userOtherPrintHistoryList.stream().map((x) -> {
                        CmnTucPlanDto cmnTucPlan = new CmnTucPlanDto();
                        // 利用者番号
                        cmnTucPlan.setSelf_id(CommonDtoUtil.objValToString(x.getSelfId()));
                        // フリガナ（姓）
                        cmnTucPlan.setName1_kana(x.getName1Kana());
                        // フリガナ（名）
                        cmnTucPlan.setName2_kana(x.getName2Kana());
                        // 氏名（姓）
                        cmnTucPlan.setName1_knj(x.getName1Knj());
                        // 氏名（名）
                        cmnTucPlan.setName2_knj(x.getName2Knj());
                        // 性別
                        cmnTucPlan.setSex(CommonDtoUtil.objValToString(x.getSex()));
                        // 利用者ID
                        cmnTucPlan.setUserid(CommonDtoUtil.objValToString(x.getUserid()));
                        // 被保険者番号
                        cmnTucPlan.setH_hoken_no(x.getHHokenNo());
                        // 支援事業者ID
                        cmnTucPlan.setShien_id(CommonDtoUtil.objValToString(x.getShienId()));
                        // サービス提供年月
                        cmnTucPlan.setYymm_ym(x.getYymmYm());
                        // サービス提供年月（変更日）
                        cmnTucPlan.setYymm_d(x.getYymmD());
                        // 予定済みフラグ
                        cmnTucPlan.setYotei_zumi_flg(CommonDtoUtil.objValToString(x.getYoteiZumiFlg()));
                        // 実績済みフラグ
                        cmnTucPlan.setJisseki_zumi_flg(CommonDtoUtil.objValToString(x.getJissekiZumiFlg()));
                        // 確定済みフラグ
                        cmnTucPlan.setKakutei_zumi_flg(CommonDtoUtil.objValToString(x.getKakuteiZumiFlg()));
                        // 保険者番号
                        cmnTucPlan.setK_hoken_cd(CommonDtoUtil.objValToString(x.getKHokenCd()));
                        // 要介護度
                        cmnTucPlan.setYokai_kbn(CommonDtoUtil.objValToString(x.getYokaiKbn()));
                        // 要介護度（変更後）
                        cmnTucPlan.setHen_yokai_kbn(CommonDtoUtil.objValToString(x.getHenYokaiKbn()));
                        return cmnTucPlan;
                    }).toList());
                    if (userOtherPrintHistoryList.size() > 0) {
                        // 結果内容
                        prtHistory.setResult(STR_RESULT);
                    }
                }
                // プロファイル "3GK" +セクション番号+ "P"＋ 帳票番号の3桁に0で前方補完
                prtHistory.setProfile(CommonConstants.SYS_CD_KBN_3GK.concat(
                        prtHistory.getSectionNo()).concat(STR_P)
                        .concat(String.format("%3s", prtHistory.getPrtNo()).replace(' ', '0')));
                // 利用者ID
                prtHistory.setUserId(userInfo.getUserid());
                // 利用者名
                prtHistory.setUserName(userInfo.getUsernameKnj());
                prtHistoryList.add(prtHistory);
            }
        }
        outDto.setPrtHistoryList(prtHistoryList);
        // 印刷設定情報リストの取得
        List<GyoumuComPrintSettingsSaveDto> prtList = new ArrayList<GyoumuComPrintSettingsSaveDto>();
        // システムINI情報リスト
        List<GyoumuComSystemIniDto> sysIniInfoList = new ArrayList<GyoumuComSystemIniDto>();
        for (int i = 0; i < reportSelectionText.length(); i++) {
            // 各帳票のINPUT情報の作成
            GyoumuComPrintSettingsInputDto prtInfoIn = new GyoumuComPrintSettingsInputDto();
            // セクション番号
            String sectionNo = "";
            // 帳票番号
            String prtNo = "";
            switch (i) {
                case 0:
                    // リクエストパラメータ.初期設定マスタの情報.計画書様式が1:施設の場合
                    if (VAL_CLS_FLG_1.equals(inDto.getInitMasterObj().getCksFlg())) {
                        sectionNo = SECTION_NO_U0081S;
                    } else {
                        sectionNo = SECTION_NO_U0081K;
                    }
                    prtNo = PRTNO_1;
                    break;
                case 1:
                    // リクエストパラメータ.初期設定マスタの情報.計画書様式が1:施設の場合
                    if (VAL_CLS_FLG_1.equals(inDto.getInitMasterObj().getCksFlg())) {
                        sectionNo = SECTION_NO_U0082S;
                    } else {
                        sectionNo = SECTION_NO_U0082K;
                    }
                    prtNo = PRTNO_1;
                    break;
                case 2:
                    sectionNo = SECTION_NO_U00852;
                    prtNo = PRTNO_1;
                    break;
                case 3:
                    sectionNo = SECTION_NO_U00861;
                    prtNo = PRTNO_1;
                    break;
                case 4:
                    sectionNo = SECTION_NO_V00231;
                    prtNo = PRTNO_3;
                    break;
                case 5:
                    sectionNo = SECTION_NO_V00231;
                    prtNo = PRTNO_4;
                    break;
                default:
                    break;
            }
            prtInfoIn.setSectionNo(sectionNo);
            // 帳票番号
            prtInfoIn.setPrtNo(prtNo);
            // 職員ID
            prtInfoIn.setShokuId(inDto.getShokuId());
            // 法人ID
            prtInfoIn.setHoujinId(inDto.getHoujinId());
            // 施設ID
            prtInfoIn.setShisetuId(inDto.getShisetuId());
            // 事業所ID
            prtInfoIn.setSvJigyoId(inDto.getSvJigyoId());
            // インデックス
            prtInfoIn.setIndex(prtNo);
            GyoumuComPrintSettingsSaveDto printSettingsSave = gyoumuComLogic.getPrtInfo(prtInfoIn);
            prtList.add(printSettingsSave);

            // システムINI情報リストの取得
            GyoumuComSystemIniDto systemIni = new GyoumuComSystemIniDto();
            // 個人情報設定表示フラグの取得
            KojinHogoJohoSetteiByCriteriaInEntity kojinHogoJohoSetteiByCriteriaInEntity = new KojinHogoJohoSetteiByCriteriaInEntity();
            List<KojinHogoJohoSetteiOutEntity> kojinHogoJohoSetteiList = comMocSysiniSelectMapper
                    .findKojinHogoJohoSetteiByCriteria(kojinHogoJohoSetteiByCriteriaInEntity);
            // 個人情報設定表示フラグ
            String kojinhogoDisplayFlg = kojinHogoJohoSetteiList.getFirst().getNewParam();
            // システムINI情報リストの取得
            F3gkGetProfileInDto profileInDto = new F3gkGetProfileInDto();
            // 職員ＩＤ
            profileInDto.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
            // 法人ＩＤ
            profileInDto.setHoujinId(0);
            // 施設ＩＤ
            profileInDto.setShisetuId(0);
            // 事業所ＩＤ
            profileInDto.setSvJigyoId(0);
            // 画面名
            profileInDto.setKinounameKnj(SCREEN_NAME);
            // セクション
            profileInDto.setSectionKnj(printSettingsSave.getProfile());
            // キー
            profileInDto.setKeyKnj(AMIKAKE_FLG);
            // 初期値
            profileInDto.setAsDefault(DEFAULT);
            // システムコード
            profileInDto.setGsyscd(SYSTEM_CODE);
            String amikakeFlg = nds3GkFunc01Logic.getF3gkProfile(profileInDto);
            // 個人情報設定の取得
            profileInDto.setKeyKnj(KOJINHOGO_FLG);
            String kojinhogoFlg = nds3GkFunc01Logic.getF3gkProfile(profileInDto);
            // プロファイル
            systemIni.setProfile(printSettingsSave.getProfile());
            if (i < 3) {
                // 「4.1. 個人情報設定表示フラグの取得」で取得した「値」が1の場合、0；以外の場合、1
                if (CommonConstants.STR_1.equals(kojinhogoDisplayFlg)) {
                    // 氏名伏字設定表示フラグ
                    systemIni.setAmikakeDisplayFlg(CommonConstants.STR_0);
                    // 個人情報設定表示フラグ
                    systemIni.setKojinhogoDisplayFlg(CommonConstants.STR_0);
                    // 氏名伏字設定フラグ
                    systemIni.setAmikakeFlg(CommonConstants.STR_0);
                    // 個人情報設定フラグ
                    systemIni.setKojinhogoFlg(CommonConstants.STR_0);
                } else {
                    // 氏名伏字設定表示フラグ
                    systemIni.setAmikakeDisplayFlg(CommonConstants.STR_1);
                    // 個人情報設定表示フラグ
                    systemIni.setKojinhogoDisplayFlg(CommonConstants.STR_1);
                    // 氏名伏字設定フラグ
                    systemIni.setAmikakeFlg(amikakeFlg);
                    // 個人情報設定フラグ
                    systemIni.setKojinhogoFlg(kojinhogoFlg);
                }
            } else {
                // 氏名伏字設定表示フラグ
                systemIni.setAmikakeDisplayFlg(CommonConstants.STR_0);
                // 個人情報設定表示フラグ
                systemIni.setKojinhogoDisplayFlg(CommonConstants.STR_0);
                // 氏名伏字設定フラグ
                systemIni.setAmikakeFlg(CommonConstants.STR_0);
                // 個人情報設定フラグ
                systemIni.setKojinhogoFlg(CommonConstants.STR_0);
            }
            // 文章管理設定表示フラグ
            systemIni.setIso9001DisplayFlg(CommonConstants.STR_0);
            // 文章管理設定フラグ
            systemIni.setIso9001Flg(CommonConstants.STR_0);
            sysIniInfoList.add(systemIni);
        }
        outDto.setPrtList(prtList);
        outDto.setSelectedReport(reportSelectionText);
        outDto.setSysIniInfoList(sysIniInfoList);
        return outDto;
    }
}
