package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.*;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * GUI01014_計画書（2）有効期間ID取得の入力Dto
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class CarePlan2UpdateServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 計画対象期間更新区分 */
    @NotEmpty
    private String updateKbn;
    /** 計画対象期間ID */
    @NotEmpty
    private String sc1Id;
    /** 法人ID */
    @NotEmpty
    private String houjinId;
    /** 施設ID */
    @NotEmpty
    private String shisetuId;
    /** 利用者ID */
    @NotEmpty
    private String userId;
    /** 事業者ID */
    @NotEmpty
    private String svJigyoId;
    /** 種別ID */
    @NotEmpty
    private String syubetuId;
    /** 履歴更新区分 */
    @NotEmpty
    private String historyUpdateKbn;
    /** 計画書（２）ID */
    @NotEmpty
    private String ks21Id;
    /** 作成日 */
    @NotEmpty
    private String createYmd;
    /** 作成者ID */
    @NotEmpty
    private String shokuId;
    /** 有効期間ID */
    @NotEmpty
    private String termid;
    /** 計画書様式 */
    @NotEmpty
    private String cksflg;
    /** 計画書（２）リスト */
    @NotNull
    private List<Gui01014Keikasyo2Upd> keikasyo2List;
    /** 保険サービスリスト */
    @NotNull
    private List<Gui01014HokenUpd> hokenList;
    /** 月日指定リスト */
    @NotNull
    private List<Gui01014TukihiUpd> tukihiList;
    /** 記録との連携 */
    @NotEmpty
    private String kirokuRenkeiFlg;
}
