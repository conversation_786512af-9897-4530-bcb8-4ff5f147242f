package jp.ndsoft.carebase.cmn.api.report.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.HospitalizationInfoOffer2ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.HospitalizationTimeInfoOfferPlanServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.logic.HospitalizationTimeInfoOfferPlanReportLogic;
import jp.ndsoft.carebase.cmn.api.report.model.HospitalizationTimeOfferAllReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * @since 2025.07.15
 * <AUTHOR>
 *         U06091_入院時情報提供書② 帳票出力
 */
@Service("HospitalizationInfoOffer2Report")
public class HospitalizationInfoOffer2ReportService
        extends
        PdfReportServiceImpl<HospitalizationTimeOfferAllReportParameterModel, HospitalizationTimeInfoOfferPlanServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** ケアチェック表7 情報取得 */
    @Autowired
    private HospitalizationTimeInfoOfferPlanReportLogic reportLogic;

    /**
     * 帳票パラメータ取得
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(HospitalizationTimeOfferAllReportParameterModel model,
            HospitalizationTimeInfoOfferPlanServiceOutDto outDto)
            throws Exception {
        LOG.info(Constants.START);

        // ノート情報格納配列
        List<HospitalizationInfoOffer2ReportServiceInDto> HospitalizationInfoOffer22InfoList = new ArrayList<HospitalizationInfoOffer2ReportServiceInDto>();
        // U06091_入院時情報提供書②パラメータ取得
        HospitalizationInfoOffer2ReportServiceInDto infoInDto = reportLogic
                .getHospitalizationInfoOffer2ReportParameters(model, outDto);

        HospitalizationInfoOffer22InfoList.add(infoInDto);

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(
                HospitalizationInfoOffer22InfoList);
        infoInDto.setDataSource(dataSource);

        LOG.info(Constants.END);

        return infoInDto;
    }

    /**
     * 帳票出力
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final HospitalizationTimeOfferAllReportParameterModel model,
            final HospitalizationTimeInfoOfferPlanServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final HospitalizationInfoOffer2ReportServiceInDto reportParameter = (HospitalizationInfoOffer2ReportServiceInDto) getReportParameters(
                model, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(getFwProps(),
                        ReportConstants.JRXML_HOSPITALIZATION_INFO_2)));

        // コンパイル
        final JasperReport jasperFile = JasperCompileManager.compileReport(is);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile,
                reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(model, jasperPrint);

        /*
         * =============== 6.レスポンスを返却===============
         * 
         */
        super.setFilepath(model, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }
}
