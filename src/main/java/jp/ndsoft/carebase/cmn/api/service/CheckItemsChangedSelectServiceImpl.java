package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckItemInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckNaiyou;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckPoint;
import jp.ndsoft.carebase.cmn.api.service.dto.CheckItemsChangedSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.CheckItemsChangedSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChkMainInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChkMainInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkCheckNaiyouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkCheckNaiyouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ChkMainInfoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocCheckNaiyouSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.06.03
 * <AUTHOR>
 * @implNote GUI00892_チェック項目画面 情報取得
 */
@Service
public class CheckItemsChangedSelectServiceImpl extends
        SelectServiceImpl<CheckItemsChangedSelectServiceInDto, CheckItemsChangedSelectServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** チェック項目評価内容マスタ情報取得 */
    @Autowired
    private KghMocCheckNaiyouSelectMapper kghMocCheckNaiyouSelectMapper;

    /** チェック項目詳細情報を取得する */
    @Autowired
    private ChkMainInfoSelectMapper chkMainInfoSelectMapper;

    /**
     * 情報取得
     * 
     * @param inDto 情報取得サービス入力Dto
     * @return 情報取得サービス出力Dto
     * @throws Exception Exception
     */
    @Override
    protected CheckItemsChangedSelectServiceOutDto mainProcess(CheckItemsChangedSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);

        CheckItemsChangedSelectServiceOutDto outdDto = new CheckItemsChangedSelectServiceOutDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2.期間管理フラグを取得する。===============
         * 
         */

        // チェック項目評価内容マスタ情報
        HashMap<String, List<Gui00892CheckNaiyou>> checkNaiyouInfoMap = new HashMap<>();

        // 評価点数リスト
        HashMap<String, List<Gui00892CheckPoint>> checkPointsMap = new HashMap<>();

        // 2.1. 下記のDAOを利用してチェック項目評価内容マスタ情報を取得する。
        List<KghKrkCheckNaiyouOutEntity> checkNaiyouInfo = this.getCheckNaiyouInfo(inDto, checkNaiyouInfoMap,
                checkPointsMap);

        // 2.2. 下記のDAOを利用してチェック項目詳細情報を取得する。
        List<Gui00892CheckItemInfo> checkItemInfo = this.getCheckItemInfo(inDto, checkNaiyouInfo, checkNaiyouInfoMap,
                checkPointsMap);

        /*
         * ===============3. レスポンスを返却する。===============
         * 
         */
        // チェック項目詳細情報
        outdDto.setCheckItemInfo(checkItemInfo);

        LOG.info(Constants.END);
        return outdDto;
    }

    /**
     * チェック項目評価内容マスタ情報を取得する。
     * 
     * @param inDto              初期情報取得サービス入力Dto
     * @param checkNaiyouInfoMap チェック内容マスタ情報Map
     * @param checkPointsMap     評価点数リストMap
     * 
     * @return チェック項目評価内容マスタ情報
     */
    private List<KghKrkCheckNaiyouOutEntity> getCheckNaiyouInfo(CheckItemsChangedSelectServiceInDto inDto,
            HashMap<String, List<Gui00892CheckNaiyou>> checkNaiyouInfoMap,
            HashMap<String, List<Gui00892CheckPoint>> checkPointsMap) {

        KghKrkCheckNaiyouByCriteriaInEntity kghKrkCheckNaiyouByCriteriaInEntity = new KghKrkCheckNaiyouByCriteriaInEntity();
        // 法人ID
        kghKrkCheckNaiyouByCriteriaInEntity.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        kghKrkCheckNaiyouByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        kghKrkCheckNaiyouByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 様式ID
        kghKrkCheckNaiyouByCriteriaInEntity.setYoushikiId(CommonDtoUtil.strValToInt(inDto.getYoushikiId()));

        List<KghKrkCheckNaiyouOutEntity> kghKrkCheckNaiyouList = this.kghMocCheckNaiyouSelectMapper
                .findKghKrkCheckNaiyouByCriteria(kghKrkCheckNaiyouByCriteriaInEntity);

        if (CollectionUtils.isNotEmpty(kghKrkCheckNaiyouList)) {
            kghKrkCheckNaiyouList.forEach(kghKrkCheckNaiyou -> {
                // 5.1.1.下記処理中の変数初期化を行う
                Gui00892CheckNaiyou checkNaiyouInfoItem = new Gui00892CheckNaiyou();
                // 5.1.3.取得したチェック項目評価内容マスタ情報件数分で項目ID,細目ID毎に下記を行う

                // 内容ID
                checkNaiyouInfoItem.setNaiyouId(CommonDtoUtil.objValToString(kghKrkCheckNaiyou.getNaiyouId()));
                // 内容名称
                checkNaiyouInfoItem.setNaiyouKnj(CommonDtoUtil.objValToString(kghKrkCheckNaiyou.getNaiyouKnj()));
                // 評価点数を新規作成して下記項目値を設定する
                Gui00892CheckPoint checkPoint = new Gui00892CheckPoint();
                // 内容ID
                checkPoint.setNaiyouId(CommonDtoUtil.objValToString(kghKrkCheckNaiyou.getNaiyouId()));
                // 評価点数
                checkPoint.setPoint(CommonDtoUtil.objValToString(kghKrkCheckNaiyou.getPoint()));
                String key = String.format("%d%d", kghKrkCheckNaiyou.getKoumokuId(), kghKrkCheckNaiyou.getSaimokuId());
                // マップに追加する
                checkNaiyouInfoMap.putIfAbsent(key, new ArrayList<>());
                checkNaiyouInfoMap.get(key).add(checkNaiyouInfoItem);
                checkPointsMap.putIfAbsent(key, new ArrayList<>());
                checkPointsMap.get(key).add(checkPoint);
            });

        }
        return kghKrkCheckNaiyouList;

    }

    /**
     * チェック項目詳細情報を取得する。
     * 
     * @param inDto               初期情報取得サービス入力Dto
     * @param checkNaiyouInfoList チェック内容マスタ情報
     * @param checkNaiyouInfoMap  チェック内容マスタ情報Map
     * @param checkPointsMap      評価点数リストMap
     * @return チェック項目詳細情報
     */
    private List<Gui00892CheckItemInfo> getCheckItemInfo(CheckItemsChangedSelectServiceInDto inDto,
            List<KghKrkCheckNaiyouOutEntity> checkNaiyouInfoList,
            Map<String, List<Gui00892CheckNaiyou>> checkNaiyouInfoMap,
            Map<String, List<Gui00892CheckPoint>> checkPointsMap) {
        // チェック項目詳細情報
        List<Gui00892CheckItemInfo> checkItemInfo = new ArrayList<Gui00892CheckItemInfo>();

        ChkMainInfoByCriteriaInEntity chkMainInfoByCriteriaInEntity = new ChkMainInfoByCriteriaInEntity();
        // 法人ID
        chkMainInfoByCriteriaInEntity.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        chkMainInfoByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        chkMainInfoByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        chkMainInfoByCriteriaInEntity.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 計画期間ID
        chkMainInfoByCriteriaInEntity.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // 履歴ID
        chkMainInfoByCriteriaInEntity.setAssId(CommonDtoUtil.strValToInt(inDto.getAssId()));

        List<ChkMainInfoOutEntity> chkMainInfoList = this.chkMainInfoSelectMapper
                .findChkMainInfoByCriteria(chkMainInfoByCriteriaInEntity);

        if (CollectionUtils.isNotEmpty(chkMainInfoList)) {
            chkMainInfoList.forEach(chkMainInfo -> {
                Gui00892CheckItemInfo checkItemInfoItem = new Gui00892CheckItemInfo();
                // 履歴ID
                checkItemInfoItem.setAssId(CommonDtoUtil.objValToString(chkMainInfo.getAssId()));
                // 作成日
                checkItemInfoItem.setCreateYmd(chkMainInfo.getCreateYmd());
                // 項目名称
                checkItemInfoItem.setKoumokuKnj(chkMainInfo.getKoumokuKnj());
                // 細目名称
                checkItemInfoItem.setSaimokuKnj(chkMainInfo.getSaimokuKnj());
                // 課題対象フラグ
                checkItemInfoItem.setKadaiFlg(CommonDtoUtil.objValToString(chkMainInfo.getKadaiFlg()));
                // 備考
                checkItemInfoItem.setBiko(chkMainInfo.getBiko());
                // 項目ID
                checkItemInfoItem.setKoumokuId(CommonDtoUtil.objValToString(chkMainInfo.getKoumokuId()));
                // 細目ID
                checkItemInfoItem.setSaimokuId(CommonDtoUtil.objValToString(chkMainInfo.getSaimokuId()));
                // 内容ID
                checkItemInfoItem.setNaiyoId(CommonDtoUtil.objValToString(chkMainInfo.getNaiyoId()));
                // 5.2.1.1.チェック項目詳細情報の内容IDの値が空白またはNULLの場合
                if (Objects.isNull(chkMainInfo.getNaiyoId())) {
                    // チェック項目詳細情報.点数=""
                    checkItemInfoItem.setPoint("");
                    // チェック項目詳細情報.内容名称=""
                    checkItemInfoItem.setNaiyouKnj("");
                }
                // 5.2.1.2.チェック項目詳細情報の内容IDの値が上記以外の場合
                else {
                    // チェック項目詳細情報の項目ID、細目ID、内容IDとチェック項目評価内容マスタ情報の項目ID、細目ID、内容IDと同じのチェック項目評価内容マスタ情報を取得する
                    checkNaiyouInfoList.stream().filter(naiyou -> naiyou.getKoumokuId() == chkMainInfo.getKoumokuId()
                            && naiyou.getSaimokuId() == chkMainInfo.getSaimokuId()
                            && naiyou.getNaiyouId() == chkMainInfo.getNaiyoId())
                            .findFirst().ifPresent(matchedNaiyou -> {
                                // チェック項目詳細情報の下記項目に値設定する
                                // チェック項目詳細情報.点数=チェック項目評価内容マスタ情報.点数
                                checkItemInfoItem.setPoint(CommonDtoUtil.objValToString(matchedNaiyou.getPoint()));
                                // チェック項目詳細情報.内容名称=チェック項目評価内容マスタ情報.内容名称
                                checkItemInfoItem
                                        .setNaiyouKnj(CommonDtoUtil.objValToString(matchedNaiyou.getNaiyouKnj()));
                            });
                    // 5.2.1.3.チェック項目詳細情報の項目ID、細目IDにより変数.評価内容マップから評価内容リストを取得する
                    // チェック項目詳細情報.評価内容リストに取得した評価内容リストを設定する
                    String key = String.format("%d%d", chkMainInfo.getKoumokuId(), chkMainInfo.getSaimokuId());
                    checkItemInfoItem.setCheckNaiyouList(checkNaiyouInfoMap.getOrDefault(key, new ArrayList<>()));
                    // 5.2.1.4チェック項目詳細情報の項目ID、細目IDにより変数.評価点数マップから評価点数リストを取得する
                    // チェック項目詳細情報.評価点数リストに取得した評価点数リストを設定する
                    checkItemInfoItem.setCheckPointList(checkPointsMap.getOrDefault(key, new ArrayList<>()));
                }

                checkItemInfo.add(checkItemInfoItem);

            });
        }
        return checkItemInfo;
    }

}
