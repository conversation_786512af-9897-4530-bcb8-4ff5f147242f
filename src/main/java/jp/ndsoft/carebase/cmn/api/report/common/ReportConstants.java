package jp.ndsoft.carebase.cmn.api.report.common;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class ReportConstants {
    /**
     * アセスメント表帳票タイトル
     */
    public static final String ASSESSMENT_TITLE = "インターライ方式ケアアセスメント表";

    /**
     * ケアチェック表帳票タイトル
     */
    public static final String CARE_CHECK_TITLE = "ケアチェック表";

    /**
     * ケアチェック表帳票サブタイトル
     */
    public static final String CARE_CHECK_SABU_TITLE = "包括的自立支援プログラム";

    /**
     * 問題領域検討表帳票タイトル
     */
    public static final String PROBLEM_AREA_ASSESSMENT_TITLE = "問題領域検討表";
    /**
     * 入院時情報提供書①タイトル
     */
    public static final String HOSPITALIZATION_TIME_INFO_OFFER_PLAN_TITLE = "入院時情報提供書①";
    /**
     * アセスメント表（領域）
     */
    public static final String ASSESSMENT_REGION_TITLE = "養護老人ホームパッケージプラン：アセスメントシート";
    /**
     * CAP検討用紙帳票タイトル
     */
    public static final String CPA_CONSIDER_BLAM_FORM = "CAP検討用紙";
    /**
     * 
     * CAP課題と目標：CAP
     */
    public static final String CPA_ISSUES_GOAL_CPA = "【CAP";
    /**
     * 
     * CAP課題と目標：課題と目標
     */
    public static final String CPA_ISSUES_GOAL_ISSUES_GOAL = "】課題と目標";
    /**
     * 居宅版
     */
    public static final String STR_HOMEEDITION = "居宅版";

    /**
     * 施設版
     */
    public static final String STR_FACILITYEDITION = "施設版";

    /**
     * 高齢者住宅版
     */
    public static final String STR_SENIORHOUSINGEDITION = "高齢者住宅版";

    /**
     * 黒
     */
    public static final String COLOR_BLANK = "#000000";

    /**
     * 白
     */
    public static final String COLOR_WHITE = "#FFFFFF";
    /**
     * 水色
     */
    public static final String COLOR_LIGHTBLUE = "#00FFFF";

    /**
     * 指定日印刷区分 1:印刷しない
     */
    public static final String PRINTDATE_KBN_SHINAI = "1";

    /**
     * 指定日印刷区分 2:指定日印刷
     */
    public static final String PRINTDATE_KBN_PRINT = "2";

    /**
     * 指定日印刷区分 3:日付空欄印刷
     */
    public static final String PRINTDATE_KBN_BLANKDATE = "3";

    /**
     * 作成日印刷区分 1:作成年月日を印刷する
     */
    public static final String CREATEYMD_KBN_PRINT = "1";

    /**
     * 作成日印刷区分 2:空白
     */
    public static final String CREATEYMD_KBN_BLANKDATE = "2";

    /**
     * 作成日印刷区分 3:作成年月日を印刷しない
     */
    public static final String CREATEYMD_KBN_SHINAI = "3";

    /**
     * 作成年月日ラベル
     */
    public static final String STR_LABEL_CREATEYMD = "作成年月日";

    /**
     * 指定日印刷区分 1:印刷しない
     */
    public static final int PRINTDATE_KBN_SHINAI_1 = 1;

    /**
     * 指定日印刷区分 2:指定日印刷
     */
    public static final int PRINTDATE_KBN_PRINT_2 = 2;

    /**
     * 指定日印刷区分 3:日付空欄印刷
     */
    public static final int PRINTDATE_KBN_BLANKDATE_3 = 3;

    /**
     * アセスメント表の種類 1:居宅版
     */
    public static final int ASSESSMENT_TYPE_HOMEEDITION = 1;

    /**
     * アセスメント表の種類 2:施設版
     */
    public static final int ASSESSMENT_TYPE_FACILITYEDITION = 2;

    /**
     * アセスメント表の種類 3:高齢者住宅版
     */
    public static final int ASSESSMENT_TYPE_SENIORHOUSINGEDITION = 3;

    /**
     * 課題チェック表示切替 0:非表示
     */
    public static final String KIRIKAE_KBN_HIHYOUJI = "0";

    /**
     * 課題チェック表示切替 1:表示
     */
    public static final String KIRIKAE_KBN_HYOUJI = "1";

    /**
     * 用紙サイズ 1:A4縦
     */
    public static final String YOUSHI_SIZE_A4_TATE = "1";

    /**
     * 用紙サイズ 2:A4横
     */
    public static final String YOUSHI_SIZE_A4_YOKO = "2";

    /**
     * 用紙サイズ 3:A5縦
     */
    public static final String YOUSHI_SIZE_A5_TATE = "3";

    /**
     * 用紙サイズ 4:A5横
     */
    public static final String YOUSHI_SIZE_A5_YOKO = "4";

    /**
     * 用紙サイズ 5:A3縦
     */
    public static final String YOUSHI_SIZE_A3_TATE = "5";

    /**
     * 用紙サイズ 6:A3横
     */
    public static final String YOUSHI_SIZE_A3_YOKO = "6";

    /**
     * 日付フォーマット（yyyy-MM-dd HH:mm:ss）
     */
    public static final String DATE_FORMAT_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日付フォーマット（yyyy-MM-dd）
     */
    public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";

    /**
     * 和暦日付フォーマット（Gyy年MM月dd日）
     */
    public static final String DATE_FORMAT_GYY_MM_DD = "Gyy年MM月dd日";

    /**
     * 和暦日付フォーマット（Gy年M月d日）
     */
    public static final String DATE_FORMAT_GY_M_D = "G y年 M月 d日";

    /**
     * 空年月日: 年 月 日
     */
    public static final String DATE_NULL_YMD = "　　   年   月   日";

    /**
     * 年
     */
    public static final String DATE_YEAR = "年";

    /**
     * 月
     */
    public static final String DATE_MONTH = "月";

    /**
     * 日
     */
    public static final String DATE_DAY = "日";

    /**
     * 歳
     */
    public static final String STR_AGE = "歳";

    /**
     * 指定日（0:年号）
     */
    public static final Integer SHITEIDATE_GG = 0;

    /**
     * 指定日（1:年）
     */
    public static final Integer SHITEIDATE_YY = 1;

    /**
     * 指定日（2:月）
     */
    public static final Integer SHITEIDATE_MM = 2;

    /**
     * 指定日（3:日）
     */
    public static final Integer SHITEIDATE_DD = 3;
    /**
     * 日付（0:年号）
     */
    public static final Integer DATE_GG = 0;

    /**
     * 日付（1:年）
     */
    public static final Integer DATE_YY = 1;

    /**
     * 日付（2:月）
     */
    public static final Integer DATE_MM = 2;

    /**
     * 日付（3:日）
     */
    public static final Integer DATE_DD = 3;

    /**
     * 数字: 13
     */
    public static final int INT_13 = 13;

    /**
     * 数字: 14
     */
    public static final int INT_14 = 14;

    /**
     * 数字: 15
     */
    public static final int INT_15 = 15;

    /**
     * 数字: 17
     */
    public static final int INT_17 = 17;

    /**
     * 数字: 26
     */
    public static final int INT_26 = 26;

    /**
     * 数字: 34
     */
    public static final int INT_34 = 34;

    /**
     * 数字: 120
     */
    public static final int INT_120 = 120;

    /**
     * 数字: 2018
     */
    public static final int INT_2018 = 2018;
    /**
     * 全角数字: １
     */
    public static final String CERTIFIED_YOKAI_１ = "１";
    /**
     * 全角数字: ２
     */
    public static final String CERTIFIED_YOKAI_２ = "２";
    /**
     * 全角数字: ３
     */
    public static final String CERTIFIED_YOKAI_３ = "３";
    /**
     * 全角数字: ４
     */
    public static final String CERTIFIED_YOKAI_４ = "４";
    /**
     * 全角数字: ５
     */
    public static final String CERTIFIED_YOKAI_５ = "５";
    /**
     * カラム幅(半角)=94
     */
    public static final Integer TOKKI_FLG_94 = 94;
    /**
     * カラム幅(半角)=10
     */
    public static final Integer TOKKI_FLG_10 = 10;
    /**
     * カラム幅(半角)=34
     */
    public static final Integer TOKKI_FLG_34 = 34;
    /**
     * カラム幅(半角)=28
     */
    public static final Integer TOKKI_FLG_28 = 28;

    /**
     * fontSize:6
     */
    public static final String FONT_SIZE_6 = "6";

    /**
     * fontSize:7
     */
    public static final String FONT_SIZE_7 = "7";

    /**
     * fontSize:8
     */
    public static final String FONT_SIZE_8 = "8";

    /**
     * fontSize:9
     */
    public static final String FONT_SIZE_9 = "9";

    /**
     * fontSize:10
     */
    public static final String FONT_SIZE_10 = "10";

    /**
     * fontSize:11
     */
    public static final String FONT_SIZE_11 = "11";

    /**
     * fontSize:12
     */
    public static final String FONT_SIZE_12 = "12";
    /**
     * 桁数:46
     */
    public static final Integer DIGIT_COUNT_46 = 46;
    /**
     * 桁数:37
     */
    public static final Integer DIGIT_COUNT_37 = 37;
    /**
     * 桁数:35
     */
    public static final Integer DIGIT_COUNT_35 = 35;
    /**
     * 桁数:31
     */
    public static final Integer DIGIT_COUNT_31 = 31;
    /**
     * 桁数:27
     */
    public static final Integer DIGIT_COUNT_27 = 27;
    /**
     * 桁数:22
     */
    public static final Integer DIGIT_COUNT_22 = 22;
    /**
     * 桁数:26
     */
    public static final Integer DIGIT_COUNT_26 = 27;
    /**
     * 桁数:32
     */
    public static final Integer DIGIT_COUNT_32 = 32;
    /**
     * 桁数:64
     */
    public static final Integer DIGIT_COUNT_64 = 64;
    /**
     * 桁数:44
     */
    public static final Integer DIGIT_COUNT_44 = 44;
    /**
     * 桁数:180
     */
    public static final Integer DIGIT_COUNT_180 = 180;
    /**
     * 桁数:270
     */
    public static final Integer DIGIT_COUNT_270 = 270;

    /**
     * 桁数:34
     */
    public static final Integer DIGIT_COUNT_34 = 34;

    /**
     * 明治
     */
    public static final String MEIJI = "明治";
    /**
     * 明治
     */
    public static final String TAISHO = "大正";
    /**
     * 明治
     */
    public static final String SHOWA = "昭和";
    /**
     * 値：FontSize
     */
    public static final String FORMAT_FONTSIZE = "FontSize";

    /**
     * 値：getCn
     */
    public static final String FORMAT_GETCN = "getCn";

    /**
     * 値：setCn
     */
    public static final String FORMAT_SETCN = "setCn";

    /**
     * 値：setCb
     */
    public static final String FORMAT_SETCB = "setCb";

    /**
     * 値：getSyokujiCn
     */
    public static final String FORMAT_GETSYOKUJICN = "getSyokujiCn";

    /**
     * 値：setHcc21Cn
     */
    public static final String FORMAT_SETHCC21CN = "setHcc21Cn";

    /**
     * 値：getHaisetuCn
     */
    public static final String FORMAT_GETHAISETUCN = "getHaisetuCn";

    /**
     * 値：setHcc22Cn
     */
    public static final String FORMAT_SETHCC22CN = "setHcc22Cn";

    /**
     * 値：getNyuyokuCn
     */
    public static final String FORMAT_GETNYUYOKUCN = "getNyuyokuCn";

    /**
     * 値：setHcc23Cn
     */
    public static final String FORMAT_SETHCC23CN = "setHcc23Cn";

    /**
     * 値：getSenmenCn
     */
    public static final String FORMAT_GETSENMENCN = "getSenmenCn";

    /**
     * 値：setHcc24Cn
     */
    public static final String FORMAT_SETHCC24CN = "setHcc24Cn";

    /**
     * 値：getKihonCn
     */
    public static final String FORMAT_GETKIHONCN = "getKihonCn";

    /**
     * 値：setHcc25Cn
     */
    public static final String FORMAT_SETHCC25CN = "setHcc25Cn";

    /**
     * 値：getIryoCn
     */
    public static final String FORMAT_GETIRYOCN = "getIryoCn";

    /**
     * 値：setHcc26Cn
     */
    public static final String FORMAT_SETHCC26CN = "setHcc26Cn";

    /**
     * 値：getSinriCn
     */
    public static final String FORMAT_GETSINRICN = "getSinriCn";

    /**
     * 値：setHcc27Cn
     */
    public static final String FORMAT_SETHCC27CN = "setHcc27Cn";

    /**
     * 値：setDmy
     */
    public static final String FORMAT_SETDMY = "setDmy";

    /**
     * 値：get
     */
    public static final String FORMAT_GET = "get";

    /**
     * 値：set
     */
    public static final String FORMAT_SET = "set";

    /**
     * 値：Cn
     */
    public static final String FORMAT_CN = "Cn";

    /**
     * 値：Cb
     */
    public static final String FORMAT_CB = "Cb";

    /**
     * 値：hcc21Cn
     */
    public static final String FORMAT_HCC21CN = "Hcc21Cn";

    /**
     * 値：hcc22Cn
     */
    public static final String FORMAT_HCC22CN = "Hcc22Cn";

    /**
     * 値：hcc23Cn
     */
    public static final String FORMAT_HCC23CN = "Hcc23Cn";

    /**
     * 値：hcc24Cn
     */
    public static final String FORMAT_HCC24CN = "Hcc24Cn";

    /**
     * 値：hcc25Cn
     */
    public static final String FORMAT_HCC25CN = "Hcc25Cn";

    /**
     * 値：hcc26Cn
     */
    public static final String FORMAT_HCC26CN = "Hcc26Cn";

    /**
     * 値：hcc27Cn
     */
    public static final String FORMAT_HCC27CN = "Hcc27Cn";

    /**
     * 値：0Knj
     */
    public static final String FORMAT_0_KNJ = "0Knj";

    /**
     * 値：2Knj
     */
    public static final String FORMAT_2_KNJ = "2Knj";

    /**
     * String：→
     */
    public static final String STR_YAJIRUSHI = "→";

    /**
     * String：★
     */
    public static final String STR_STAR_RPTIME = "★";

    /**
     * 改行
     */
    public static final String LINE_BREAKS = "\r\n";

    /**
     * 記入用シートを印刷するフラグ 0:チェックOFF
     */
    public static final String EMPTY_FLG_FALSE = "0";

    /**
     * 記入用シートを印刷するフラグ 1:チェックON
     */
    public static final String EMPTY_FLG_TRUE = "1";

    /**
     * 印刷時に色をつけるフラグ 0:チェックOFF
     */
    public static final String COLOR_FLG_FALSE = "0";

    /**
     * 印刷時に色をつけるフラグ 1:チェックON
     */
    public static final String COLOR_FLG_TRUE = "1";

    /**
     * 選定アセスメント種別 1:居宅版
     */
    public static final int CAP_TYPE_HOMEEDITION = 1;

    /**
     * 選定アセスメント種別 2:施設版
     */
    public static final int CAP_TYPE_FACILITYEDITION = 2;

    /**
     * 選定アセスメント種別 3:高齢者住宅版
     */
    public static final int CAP_TYPE_SENIORHOUSINGEDITION = 3;

    /**
     * 選定アセスメント種別名 1:居
     */
    public static final String CAP_TYPE_KNJ_HOMEEDITION = "居";

    /**
     * 選定アセスメント種別名 2:施
     */
    public static final String CAP_TYPE_KNJ_FACILITYEDITION = "施";

    /**
     * 選定アセスメント種別名 3:高
     */
    public static final String CAP_TYPE_KNJ_SENIORHOUSINGEDITION = "高";

    /**
     * 要介護見込みフラグ 0:false
     */
    public static final Integer MIKOMI_FLG_FALSE = 0;

    /**
     * 要介護見込みフラグ 1:true
     */
    public static final Integer MIKOMI_FLG_TRUE = 1;

    /**
     * 要介護度名称 2:経過的要介護
     */
    public static final String YOKAI_KNJ_2 = "経過的要介護";

    /**
     * 別紙参照
     */
    public static final String ATTACHED_DOCUMENT = "別紙参照";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ａ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_A = "U06080_Assessment_Sheet_A.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｔ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_T = "U06080_Assessment_Sheet_T.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｃ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_C = "U06080_Assessment_Sheet_C.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｄ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_D = "U06080_Assessment_Sheet_D.jrxml";

    /**
     * jrxmファイルパス（U06100_退院・退所情報記録書）
     */
    public static final String JRXML_HOSPITAL_LEAVING_INFO_RECORD_DOC = "U06100_Hospital_Leaving_Info_Record_Doc.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ⅴ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_V = "U06080_Assessment_Sheet_PUV.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｏ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_O = "U06080_Assessment_Sheet_O.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｐ）(Q)）
     */
    public static final String JRXML_ASSESSMENT_SHEET_PQ = "U06080_Assessment_Sheet_PQ.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｐ）(R)）
     */
    public static final String JRXML_ASSESSMENT_SHEET_PR = "U06080_Assessment_Sheet_PR.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｐ）(U)（Ⅴ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_PUV = "U06080_Assessment_Sheet_PUV.jrxml";

    /**
     * jrxmファイルパス（U06085_CAP検討用紙）
     */
    public static final String JRXML_CONSIDER_BLANK_FORM = "U06085_CAPConsider_Blank_Form.jrxml";

    /**
     * jrxmファイルパス（U06085_CAPサマリー表）
     */
    public static final String JRXML_CAPSUMMARY_SHEET = "U06085_CAPSummary_Sheet.jrxml";

    /**
     * jrxmファイルパス（U0P132_フェースシート①_H21）
     */
    public static final String JRXML_FACE_SHEET_1 = "U0P132_FaceSheet1.jrxml";

    /**
     * jrxmファイルパス（U0P132_フェースシート②_H21）
     */
    public static final String JRXML_FACE_SHEET_2 = "U0P132_FaceSheet2.jrxml";

    /**
     * jrxmファイルパス（U0P132_フェースシート③_H21）
     */
    public static final String JRXML_FACE_SHEET_3 = "U0P132_FaceSheet3.jrxml";

    /**
     * jrxmファイルパス（U0P132_フェースシート④_H21）
     */
    public static final String JRXML_FACE_SHEET_4 = "U0P132_FaceSheet4.jrxml";
    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｉ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_I = "U06080_Assessment_Sheet_I.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（F））
     */
    public static final String JRXML_ASSESSMENT_SHEET_F = "U06080_Assessment_Sheet_F.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（G））
     */
    public static final String JRXML_ASSESSMENT_SHEET_G = "U06080_Assessment_Sheet_G.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（H））
     */
    public static final String JRXML_ASSESSMENT_SHEET_H = "U06080_Assessment_Sheet_H.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｋ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_K = "U06080_Assessment_Sheet_K.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｍ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_M = "U06080_Assessment_Sheet_M.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｊ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_J = "U06080_Assessment_Sheet_J.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｅ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_E = "U06080_Assessment_Sheet_E.jrxml";
    /**
     * /** jrxmファイルパス（U06080_アセスメント表（Ｎ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_N = "U06080_Assessment_Sheet_N.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（S））
     */
    public static final String JRXML_ASSESSMENT_SHEET_S = "U06080_Assessment_Sheet_S.jrxml";
    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｒ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_R = "U06080_Assessment_Sheet_PR.jrxml";

    /**
     * jrxmファイルパス（U06081_問題領域選定表）
     */
    public static final String JRXML_PROBLEMREGION_SELECTIONSHEET = "U06081_ProblemRegion_SelectionSheet.jrxml";

    /**
     * jrxmファイルパス（U06083_問題領域該当表）
     */
    public static final String JRXML_PROBLEMREGION_GAITOUSHEET = "U06083_ProblemRegion_GaitouSheet.jrxml";

    /**
     * jrxmファイルパス（U06010_ケアチェック表１）
     */
    public static final String JRXML_CARE_CHECK_1 = "U06010_Care_Check1.jrxml";

    /**
     * jrxmファイルパス（U06010_ケアチェック表２）
     */
    public static final String JRXML_CARE_CHECK_2 = "U06010_Care_Check2.jrxml";

    /**
     * jrxmファイルパス（U06010_ケアチェック表３）
     */
    public static final String JRXML_CARE_CHECK_3 = "U06010_Care_Check3.jrxml";

    /**
     * jrxmファイルパス（U06010_ケアチェック表２）
     */
    public static final String JRXML_CARE_CHECK_4 = "U06010_Care_Check4.jrxml";

    /**
     * jrxmファイルパス（U06010_ケアチェック表５）
     */
    public static final String JRXML_CARE_CHECK_5 = "U06010_Care_Check5.jrxml";

    /**
     * jrxmファイルパス（U06010_ケアチェック表６）
     */
    public static final String JRXML_CARE_CHECK_6 = "U06010_Care_Check6.jrxml";

    /**
     * jrxmファイルパス（U06010_ケアチェック要約表）
     */
    public static final String JRXML_CARE_CHECK_SUMMARY = "U06010_Care_Check_Summary.jrxml";

    /**
     * jrxmファイルパス（U06010_ケアチェック表７）
     */
    public static final String JRXML_CARE_CHECK_7 = "U06010_Care_Check7.jrxml";

    /**
     * jrxmファイルパス（U06010_具体的内容と対応するケア項目）
     */
    public static final String CONCRETECONTENTS_CAREITEM = "U06010_ConcreteContents_CareItem.jrxml";

    /**
     * jrxmファイルパス（U0P092_（Ⅵ）自立支援・介護サービス担当者会議（評価会議））
     */
    public static final String JRXML_INDEPENDENCE_SUPPORT_NURSING_CARE_SERVICE_MANAGER_MEETING_EVALUATION_MEETING_VI = "U0P092_Indep_Support_Nurs_Care_Meeting_6.jrxml";

    /**
     * jrxmファイルパス（U0P149_自立支援計画書(兼介護予防)特定施設サービス計画書）
     */
    public static final String JRXML_INDEPENDENCE_SUPPORT_CARE_PLAN_SPECIFIC_FACILITY_SHEET = "U0P149_Independence_Support_Care_Plan_Specific_Facility_Sheet.jrxml";

    /**
     * CAP選定機能尺度名
     */
    public static final String[] KGH_CPN_RAI_MON_SENT_CPS = { "障害なし", "境界的である", "軽度の障害がある", "中程度の障害がある", "やや重度の障害がある",
            "重度の障害がある", "最重度の障害がある" };
    public static final String[] KGH_CPN_RAI_MON_SENT_ADLH = { "自立", "見守り必要", "限定援助", "広範援助Ⅰ", "広範援助Ⅱ", "最大援助",
            "全面依存" };
    public static final String[] KGH_CPN_RAI_MON_SENT_PS = { "痛みなし", "軽度の痛み", "中程度の痛み", "重度の痛み", "激しく耐え難い痛み" };
    public static final String[] KGH_CPN_RAI_MON_SENT_PTN_1 = { "―", "該当", "" };
    public static final String[] KGH_CPN_RAI_MON_SENT_PTN_3_03 = { "―", "維持", "改善", "" };
    public static final String[] KGH_CPN_RAI_MON_SENT_PTN_3_06 = { "―", "ADL非自立", "ADL自立", "" };
    public static final String[] KGH_CPN_RAI_MON_SENT_PTN_3_07 = { "―", "モニター", "悪化予防", "" };
    public static final String[] KGH_CPN_RAI_MON_SENT_PTN_3_09 = { "―", "改善", "悪化予防", "" };
    public static final String[] KGH_CPN_RAI_MON_SENT_PTN_2 = { "―", "中リスク", "高リスク", "" };
    public static final String[] KGH_CPN_RAI_MON_SENT_PTN_3_11 = { "―", "毎日ではない", "毎日見られる", "" };
    public static final String[] KGH_CPN_RAI_MON_SENT_PTN_3_18 = { "―", "治癒目的", "悪化予防", "発生予防", "" };
    public static final String[] KGH_CPN_RAI_MON_SENT_PTN_3_22 = { "―", "認知重度", "認知軽度", "" };
    public static final String[] KGH_CPN_RAI_MON_SENT_PTN_3_23 = { "―", "診察あり", "診察なし", "" };
    public static final String[] KGH_CPN_RAI_MON_SENT_PTN_3_26 = { "―", "―", "悪化予防", "改善", "" };
    public static final String[] KGH_CPN_RAI_MON_SENT_PTN_3_27 = { "―", "悪化予防", "改善", "" };

    /**
     * アセスメント表帳票タイトル
     */
    public static final String INCENTIVESITEM_TITLE = "誘因項目";
    /**
     * 可動域制限タイトル
     */
    public static final String RANGE_RESTRICTION = "可動域制限";
    /**
     * 自発運動タイトル
     */
    public static final String SPONTANEITY_EXERCISE = "自発運動";
    /**
     * 現状タイトル
     */
    public static final String GENJYOU_TITLE = "現状";
    /**
     * 困難度タイトル
     */
    public static final String KONNAN_TITLE = "困難度";
    /**
     * 履歴1回分
     */
    public static final String HIST_1_KAIBUN = "1";
    /**
     * 履歴3回分
     */
    public static final String HIST_3_KAIBUN = "0";
    /**
     * アセスメント表（領域）_選択項目を囲む色(黒色)
     */
    public static final String SHEETREGION_RANGECOLOR_BLANK = "1";
    /**
     * アセスメント表（領域）_選択項目を囲む色(水色)
     */
    public static final String SHEETREGION_RANGECOLOR_LIGHTBLUE = "2";
    /**
     * アセスメント表（領域）_選択項目を囲む色(白色)
     */
    public static final String SHEETREGION_RANGECOLOR_WHITE = "3";
    /**
     * pdfEncoding
     */
    public static final String PDF_ENCODING = "UniJIS-UCS2-HW-H (Japanese)";
    /**
     * pdfFontName
     */
    public static final String PDF_FONTNAME = "HeiseiKakuGo-W5";
    /**
     * ipaexg
     */
    public static final String PDF_FONTGOTHIC = "IPAexGothic";
    /**
     * ipaexm
     */
    public static final String PDF_FONTMINCHO = "IPAexMincho";
    /**
     * BorderStyle
     */
    public static final String PDF_STYLE_BORDER = "BorderStyle";
    /**
     * アセスメント表（領域）_障害者老人日常生活自立度ラベル
     */
    public static final String STR_HANDYCAPSENIOR = "障害者老人日常生活自立度：";
    /**
     * アセスメント表（領域）_認知症老人日常生活自立度ラベル
     */
    public static final String STR_COGNITIVESENIOR = "認知症老人日常生活自立度：";

    /**
     * jrxmファイルパス（U06060_情報収集シート（1））
     */
    public static final String JRXML_INFOCOLLECTION_SHEET_1 = "U06060_InfoCollectionSheet_1.jrxml";
    /**
     * jrxmファイルパス（U06060_情報収集シート（2））
     */
    public static final String JRXML_INFOCOLLECTION_SHEET_2 = "U06060_InfoCollectionSheet_2.jrxml";
    /**
     * jrxmファイルパス（U06060_情報収集シート（3））
     */
    public static final String JRXML_INFOCOLLECTION_SHEET_3 = "U06060_InfoCollectionSheet_3.jrxml";
    /**
     * jrxmファイルパス（U06060_情報収集シート（4））
     */
    public static final String JRXML_INFOCOLLECTION_SHEET_4 = "U06060_InfoCollectionSheet_4.jrxml";
    /**
     * jrxmファイルパス（U06060_情報収集シート（5））
     */
    public static final String JRXML_INFOCOLLECTION_SHEET_5 = "U06060_InfoCollectionSheet_5.jrxml";
    /**
     * jrxmファイルパス（U06060_情報収集シート（6））
     */
    public static final String JRXML_INFOCOLLECTION_SHEET_6 = "U06060_InfoCollectionSheet_6.jrxml";

    /**
     * jrxmファイルパス（U06060_情報収集シート（7））
     */
    public static final String JRXML_INFOCOLLECTION_SHEET_7 = "U06060_InfoCollectionSheet_7.jrxml";

    /**
     * jrxmファイルパス（U06060_情報収集シート（8））
     */
    public static final String JRXML_INFOCOLLECTION_SHEET_8 = "U06060_InfoCollectionSheet_8.jrxml";

    /**
     * jrxmファイルパス（U06060_情報収集シート（9））
     */
    public static final String JRXML_INFOCOLLECTION_SHEET_9 = "U06060_InfoCollectionSheet_9.jrxml";

    /**
     * jrxmファイルパス（U06060_情報収集シート（10））
     */
    public static final String JRXML_INFOCOLLECTION_SHEET_10 = "U06060_InfoCollectionSheet_10.jrxml";

    /**
     * jrxmファイルパス（U06060_情報収集シート（11））
     */
    public static final String JRXML_INFOCOLLECTION_SHEET_11 = "U06060_InfoCollectionSheet_11.jrxml";

    /**
     * jrxmファイルパス（U06060_情報収集シート（12））
     */
    public static final String JRXML_INFOCOLLECTION_SHEET_12 = "U06060_InfoCollectionSheet_12.jrxml";

    /**
     * jrxmファイルパス（U06060_情報収集シート（13））
     */
    public static final String JRXML_INFOCOLLECTION_SHEET_13 = "U06060_InfoCollectionSheet_13.jrxml";

    /**
     * jrxmファイルパス（U06060_情報収集シート（14））
     */
    public static final String JRXML_INFOCOLLECTION_SHEET_14 = "U06060_InfoCollectionSheet_14.jrxml";
    /**
     * jrxmファイルパス（U06060_情報収集シート（その他））
     */
    public static final String JRXML_INFOCOLLECTION_FACILITY_SHEET_OTHERS = "U06060_InfoCollectionSheet_Other.jrxml";

    /**
     * jrxmファイルパス（U06060_情報収集シート（服薬状況））
     */
    public static final String JRXML_INFOCOLLECTION_FACILITY_SHEET_MEDICATION = "U06060_InfoCollectionSheet_Med.jrxml";

    /**
     * jrxmファイルパス（U06060_情報収集シート（全て））
     */
    public static final String JRXML_INFOCOLLECTION_FACILITY_ALL = "U06060_InfoCollectionSheet_Facility_All.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（2））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_2 = "U06061_InfoCollectionSheet_2.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（3））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_3 = "U06061_InfoCollectionSheet_3.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（4））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_4 = "U06061_InfoCollectionSheet_4.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（5））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_5 = "U06061_InfoCollectionSheet_5.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（6））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_6 = "U06061_InfoCollectionSheet_6.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（7））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_7 = "U06061_InfoCollectionSheet_7.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（8））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_8 = "U06061_InfoCollectionSheet_8.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（9））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_9 = "U06061_InfoCollectionSheet_9.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（10））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_10 = "U06061_InfoCollectionSheet_10.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（11））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_11 = "U06061_InfoCollectionSheet_11.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（12））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_12 = "U06061_InfoCollectionSheet_12.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（13））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_13 = "U06061_InfoCollectionSheet_13.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（14））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_14 = "U06061_InfoCollectionSheet_14.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（その他））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_OTHERS = "U06061_InfoCollectionSheet_Other.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（服薬状況））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_SHEET_MEDICATION = "U06061_InfoCollectionSheet_Med.jrxml";

    /**
     * jrxmファイルパス（U06061_情報収集シート（全て））
     */
    public static final String JRXML_INFOCOLLECTION_HOME_ALL = "U06061_InfoCollectionSheet_Home_All.jrxml";

    /**
     * jrxmファイルパス（V00241_サービス提供票）
     */
    public static final String JRXML_TEIKYOHYOREPORT = "V00241_teikyohyoReport.jrxml";

    /**
     * jrxmファイルパス（V00231_サービス利用票）
     */
    public static final String JRXML_RIYOUHYOREPORT = "V00231_ServiceRiteiReport.jrxml";

    /**
     * String:13
     */
    public static final String STR_13 = "13";

    /**
     * Int:60
     */
    public static final Integer NUMBER_60 = 60;

    /**
     * jrxmファイルパス（U06084_誘因項目）
     */
    public static final String JRXML_INCENTIVES_ITEM = "U06084_Incentives_Item.jrxml";

    /**
     * jrxmファイルパス（U06026_ケアアセスメント(1)_H21）
     */
    public static final String JRXML_CARE_ASSESSMENT1_H21 = "U06026_CareAssessment1_H21.jrxml";
    /**
     * jrxmファイルパス（U06026_ケアアセスメント(2)_H21）
     */
    public static final String JRXML_CARE_ASSESSMENT2_H21 = "U06026_CareAssessment2.jrxml";
    /**
     * jrxmファイルパス（U06026_ケアアセスメント(5)_H21）
     */
    public static final String JRXML_CARE_ASSESSMENT5 = "U06026_CareAssessment5.jrxml";
    /**
     * jrxmファイルパス（U06026_ケアアセスメント(6)_H21）
     */
    public static final String JRXML_CARE_ASSESSMENT6 = "U06026_CareAssessment6.jrxml";

    /**
     * U06026_ケアアセスメント(6)_H21:意見メモ
     */
    public static final String CARE_ASSESSMENT6_IKENMEMOKNJ_1_STRING = "※計画をする際には主治医の意見を求める場合あり";

    /**
     * U06026_ケアアセスメント(6)_H21:意見メモ
     */
    public static final String CARE_ASSESSMENT6_IKENMEMOKNJ_2_STRING = "※計画をする際には主治医の意見を求める必要あり";

    /**
     * jrxmファイルパス（U06200_ケアアセスメント(6)_R3）
     */
    public static final String JRXML_CARE_ASSESSMENT6_R3 = "U06200_CareAssessment6_R3.jrxml";

    /**
     * jrxmファイルパス（U06028_生活アセスメント(5)_H30）
     */
    public static final String JRXML_LIFE_ASSESSMENT5_H30 = "U06028_LifeAssessment5_H30.jrxml";

    /**
     * jrxmファイルパス（個人別チェック項目一覧表(TYPE2））
     */
    public static final String JRXML_PERSONAL_CHECK_LIST_TABLE_TYPE2 = "U06051_Personal_Check_List_Table_Type2.jrxml";

    /**
     * jrxmファイルパス（個人別チェック項目一覧表(TYPE1））
     */
    public static final String JRXML_PERSONAL_CHECK_LIST_TABLE_TYPE1 = "U06051_Personal_Check_List_Table_Type1.jrxml";

    /**
     * jrxmファイルパス（U01700_課題分析）
     */
    public static final String JRXML_ISSUES_ANALYSIS = "U01700_IssuesAnalysis.jrxml";

    /**
     * jrxmファイルパス（U06200_生活アセスメント(5)_R3）
     */
    public static final String JRXML_LIFE_ASSESSMENT5_R3 = "U06200_LifeAssessment5_R3.jrxml";

    /**
     * jrxmファイルパス（U06026_生活アセスメント(5)_H21）
     */
    public static final String JRXML_LIFE_ASSESSMENT5_H21 = "U06026_LifeAssessment5_H21.jrxml";

    /**
     * jrxmファイルパス（U06026_ケアアセスメント(34)_H21）
     */
    public static final String JRXML_CARE_ASSESSMENT34_H21 = "U06026_CareAssessment34_H21.jrxml";

    /**
     * jrxmファイルパス（U06028_ケアアセスメント(34)_H30）
     */
    public static final String JRXML_CARE_ASSESSMENT34_H30 = "U06028_CareAssessment34_H30.jrxml";

    /**
     * jrxmファイルパス（U06200_ケアアセスメント(34)_R3）
     */
    public static final String JRXML_CARE_ASSESSMENT34_R3 = "U06200_CareAssessment34_R3.jrxml";

    /**
     * jrxmファイルパス（U06200_ケアアセスメント(1)_R3）
     */
    public static final String JRXML_CARE_ASSESSMENT1_R3 = "U06200_CareAssessment1_R3.jrxml";

    /**
     * jrxmファイルパス（U06200_ケアアセスメント(1)_R3）
     */
    public static final String JRXML_CARE_ASSESSMENT2_R3 = "U06200_CareAssessment2_R3.jrxml";

    /**
     * jrxmファイルパス（U06091_入院時情報提供書①）
     */
    public static final String JRXML_HOSPITALIZATION_INFO_1 = "U06091_Hospitalization_Info_Provision_Document1.jrxml";

    /**
     * jrxmファイルパス（U06091_入院時情報提供書①）
     */
    public static final String JRXML_HOSPITALIZATION_INFO_2 = "U06091_Hospitalization_Info_Provision_Document2.jrxml";

    /**
     * jrxmファイルパス（U06200_ケアアセスメント(5)_R3）
     */
    public static final String JRXML_CARE_ASSESSMENT5_R3 = "U06200_CareAssessment5_R3.jrxml";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｂ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_B = "U06080_Assessment_Sheet_B.jrxml";

    /**
     * jrxmファイルパス（U06028_ケアアセスメント(1)_H30）
     */
    public static final String JRXML_CARE_ASSESSMENT1_H30 = "U06028_CareAssessment1_H30.jrxml";

    /**
     * jrxmファイルパス（E00700_興味・関心チェックシート）
     */
    public static final String JRXML_INTEREST_CHECK_SHEET = "E00700_Interest_Check_Sheet.jrxml";

    /**
     * 生活アセスメント(1)フェースシート_H21帳票タイトル
     */
    public static final String ASSESSMENT_FACE_SHEET_TITLE = "フェースシート";

    /**
     * jrxmファイルパス（U06026_生活アセスメント(1)フェースシート_H21）
     */
    public static final String JRXML_LIFEASSESSMENT1_FACESHEET_H21 = "U06026_LifeAssessment1_FaceSheet_H21.jrxml";

    /**
     * jrxmファイルパス（U06028_生活アセスメント(1)フェースシート_H30）
     */
    public static final String JRXML_LIFEASSESSMENT1_FACESHEET_H30 = "U06028_LifeAssessment1_FaceSheet_H30.jrxml";

    /**
     * jrxmファイルパス（U01010_介護支援経過）
     */
    public static final String JRXML_SPECIALIST_PASSAGE = "U01010_Specialist_Passage.jrxml";

    /**
     * jrxmファイルパス（U01012_介護支援経過(R34改訂)）
     */
    public static final String JRXML_SPECIALIST_PASSAGE_R34 = "U01012_Specialist_Passage_R34.jrxml";

    /**
     * jrxmファイルパス（U01010_介護支援経過日別一覧）
     */
    public static final String JRXML_SPECIALIST_PASSAGE_BY_DATE = "U01010_Specialist_Passage_By_Date.jrxml";

    /**
     * jrxmファイルパス（U06200_生活アセスメント(1)フェースシート_R3）
     */
    public static final String JRXML_LIFEASSESSMENT1_FACESHEET_R3 = "U06200_LifeAssessment1_FaceSheet_R3.jrxml";

    /**
     * jrxmファイルパス（U0082K_居宅サービス計画書（２））
     */
    public static final String JRXML_KYOTAKU_SERVICE_REPORT = "U0082K_KyotakuServiceReport.jrxml";

    /**
     * jrxmファイルパス（U0082S_施設サービス計画書（２））
     */
    public static final String JRXML_SHISETSU_SERVICE_REPORT = "U0082S_ShisetsuServiceReport.jrxml";

    /**
     * jrxmファイルパス（U0082S_施設サービス計画書（２））
     */
    public static final String JRXML_USEANNEXED_TABLE_251_SERVICE_REPORT_1 = "V00251_useAnnexedTable251Report(1).jrxml";

    /**
     * jrxmファイルパス（U0082S_施設サービス計画書（２））
     */
    public static final String JRXML_USEANNEXED_TABLE_251_SERVICE_REPORT_2 = "V00251_useAnnexedTable251Report(2).jrxml";

    /**
     * 処理日:"2021/04"
     */
    public static final String DMY_TIME_202104 = "2021/04";

    /**
     * jrxmファイルパス（e-文書削除）
     */
    public static final String JRXML_EDOCUMENTDELETE = "EdocumentDelete.jrxml";

    /**
     * CAPサマリー表帳票タイトル
     */
    public static final String CAP_SUMMARY_TITLE = "CAPサマリー表";
    /**
     * CAP選定条件ID 1を設定
     */
    public static final Integer AN_TRIGER1 = 1;

    /**
     * CAP選定条件ID 0を設定
     */
    public static final Integer AN_TRIGER0 = 0;

    /**
     * 様式フラグ 固定値：”1”
     */
    public static final Integer YOUSHIKI_FLG = 1;
    /**
     * 機能面
     */
    public static final String FUNCTION_SCREEN = "機能面";
    /**
     * 精神面
     */
    public static final String MENTAL_SCREEN = "精神面";
    /**
     * 社会面
     */
    public static final String SOCIETY_SCREEN = "社会面";
    /**
     * 臨床面
     */
    public static final String CLINICAL_SCREEN = "臨床面";

    /**
     * グループID_1
     */
    public static final Integer GROUP_ID_1 = 1;
    /**
     * グループID_2
     */
    public static final Integer GROUP_ID_2 = 2;
    /**
     * グループID_3
     */
    public static final Integer GROUP_ID_3 = 3;
    /**
     * グループID_4
     */
    public static final Integer GROUP_ID_4 = 4;

    /**
     * 点
     */
    public static final String POINT = "点";

    /**
     * jrxmファイルパス（U06080_アセスメント表（Ｌ））
     */
    public static final String JRXML_ASSESSMENT_SHEET_L = "U06080_Assessment_Sheet_L.jrxml";
    /**
     * 別紙出力フラグ-出力
     */
    public static final String TOKKI_FLG_TRUE = "1";
    /**
     * 別紙出力フラグ-出力しない
     */
    public static final String TOKKI_FLG_FALSE = "0";
    /**
     * 別紙出力フラグKEY
     */
    public static final String TOKKI_FLG_KEY = "TokkiFlg";
    /**
     * 別紙出力-ヘッダ
     */
    public static final String TOKKI_NAME_KEY = "TokkiName";
    /**
     * 別紙出力-メモ
     */
    public static final String TOKKI_MEMO_KEY = "TokkiMemo";

    /**
     * jrxmファイルパス（E00200_利用者基本情報-基本情報）
     */
    public static final String JRXML_USER_BASIC_INFO = "E00200_User_Basic_Info.jrxml";

    /**
     * 問題領域検討表帳票タイトル
     */
    public static final String PROBLEM_REGION_CONSIDER_TITLE = "問題領域検討表";

    /**
     * 問題領域該当表帳票タイトル
     */
    public static final String PROBLEM_REGION_GAITO_TITLE = "問題領域該当表";

    /**
     * CAP課題と目標:【CAP
     */
    public static final String ISSUES_GOAL_L_MESSAGE = "【CAP ";

    /**
     * CAP課題と目標:】課題と目標
     */
    public static final String ISSUES_GOAL_R_MESSAGE = "】課題と目標";

    /**
     * jrxmファイルパス（U06082_問題領域検討表）
     */
    public static final String JRXML_PROBLEMREGION_CONSIDER_TABLE = "U06082_ProblemRegion_ConsiderTable.jrxml";

    /**
     * 作成日 '2012/04/01'
     */
    public static final String CREATION_DATE_20120401 = "2012/04/01";

    /**
     * 月日 '/04/01'
     */
    public static final String MONTH_DATE_0401 = "/04/01";

    /**
     * 年号パータン 'gg'
     */
    public static final String STR_GG = "gg";

    /**
     * 年号パータン 'gg sn'
     */
    public static final String STR_GG_OR_SN = "gg sn";

    /**
     * 固定文字： 年度
     */
    public static final String STR_NENDO = "　　年度";

    /**
     * jrxmファイルパス（U06026_生活アセスメント(3)(4)）
     */
    public static final String JRXML_LIFE_ASSESSMENT34_H21 = "U06026_LifeAssessment34_H21.jrxml";

    /**
     * jrxmファイルパス（U06028_生活アセスメント(3)(4)）
     */
    public static final String JRXML_LIFE_ASSESSMENT34_H30 = "U06028_LifeAssessment34_H30.jrxml";

    /**
     * 障害支援区分→
     */
    public static final String SYOUGAI_SUPPORT_KBN = "障害支援区分→";

    /**
     * jrxmファイルパス（E00200_利用者基本情報-基本情報以外）
     */
    public static final String JRXML_USER_NON_BASIC_INFO = "E00200_User_Non_Basic_Info.jrxml";

    /**
     * 日付フォーマット（yyyy/MM/dd）
     */
    public static final String DATE_FORMAT_YYYY_MM_DD_SLASH = "yyyy/MM/dd";

    /**
     * jrxmファイルパス（U06070_ケアプラン策定のための課題検討用紙）
     */
    public static final String JRXML_CARE_PLAN_ISSUE_REVIEW_FORM = "U06070_Care_Plan_Issue_Review_Form_Info.jrxml";

    /**
     * jrxmファイルパス（U06200_１日のスケジュール）
     */
    public static final String JRXML_DAILY_SCHEDULE_R3 = "U06200_DailySchedule_R3.jrxml";

    /**
     * jrxmファイルパス（U06026_１日のスケジュール）
     */
    public static final String JRXML_DAILY_SCHEDULE_H21 = "U06026_DailySchedule_H21.jrxml";

    /**
     * jrxmファイルパス（U06028_１日のスケジュール）
     */
    public static final String JRXML_DAILY_SCHEDULE_H30 = "U06028_DailySchedule_H30.jrxml";

    /**
     * jrxmファイルパス（U06026_全体のまとめ_H21）ガイドラインまとめ = 1
     */
    public static final String JRXML_HOME_OVERALL_SUMMARY_H21_1 = "U06026_HomeOverallSummaryH21_1.jrxml";
    /**
     * jrxmファイルパス（U06026_全体のまとめ_H21）ガイドラインまとめ = 2
     */
    public static final String JRXML_HOME_OVERALL_SUMMARY_H21_2 = "U06026_HomeOverallSummaryH21_2.jrxml";

    /**
     * jrxmファイルパス（U06028_全体のまとめ_H30）ガイドラインまとめ = 1
     */
    public static final String JRXML_HOME_OVERALL_SUMMARY_H30_1 = "U06028_HomeOverallSummaryH30_1.jrxml";
    /**
     * jrxmファイルパス（U06028_全体のまとめ_H30）ガイドラインまとめ = 2
     */
    public static final String JRXML_HOME_OVERALL_SUMMARY_H30_2 = "U06028_HomeOverallSummaryH30_2.jrxml";

    /**
     * jrxmファイルパス（U06200_全体のまとめ_R3）ガイドラインまとめ = 1
     */
    public static final String JRXML_HOME_OVERALL_SUMMARY_R3_1 = "U06200_HomeOverallSummaryR3_1.jrxml";

    /**
     * jrxmファイルパス（U06200_全体のまとめ_R3）ガイドラインまとめ = 2
     */
    public static final String JRXML_HOME_OVERALL_SUMMARY_R3_2 = "U06200_HomeOverallSummaryR3_2.jrxml";

    /**
     * ガイドラインまとめ "1"
     */
    public static final String GDL_MATOME_FLG_1 = "1";

    /**
     * ガイドラインまとめ "2"
     */
    public static final String GDL_MATOME_FLG_2 = "2";

    /**
     * 空白の項目も印刷するチェックボックス 1
     */
    public static final String BLANK_ITRM_1 = "1";

    /**
     * 空白の項目も印刷するチェックボックス 0
     */
    public static final String BLANK_ITRM_0 = "0";

    /**
     * 固定値 "レ"
     */
    public static final String STRING_RE = "レ";

    /**
     * 固定値 "○"
     */
    public static final String STRING_MARU = "○";
    /**
     * 固定値 "レ○"
     */
    public static final String STRING_REMARU = "レ○";

    /**
     * jrxmファイルパス（U06061_情報収集シート（1））
     */
    public static final String U06061_JRXML_INFOCOLLECTION_SHEET_1 = "U06061_InfoCollectionSheet_1.jrxml";
    /**
     * jrxmファイルパス（U06028_生活アセスメント(3)(4)）
     */
    public static final String JRXML_LIFE_ASSESSMENT34_R3 = "U06200_LifeAssessment34_R3.jrxml";

    /**
     * 介護者
     */
    public static final String KAIGO_FLG_MSG = "※";

    /**
     * 職の有無:有
     */
    public static final String SHOKUGYO_1_MSG = "有";

    /**
     * 職の有無:無
     */
    public static final String SHOKUGYO_2_MSG = "無";

    /**
     * 健康手帳の有無:有
     */
    public static final String KENKOTECHOUMU_PRESENCE = "有";

    /**
     * 健康手帳の有無:無
     */
    public static final String KENKOTECHOUMU_NON = "無";

    /**
     * jrxmファイルパス（U06028_生活アセスメント(2)(3)）
     */
    public static final String JRXML_LIFE_ASSESSMENT23_H30 = "U06028_LifeAssessment23_H30.jrxml";

    /**
     * U04004_認定調査票(基本調査１)(H24.4～).jrxml
     */
    public static final String U04004_JRXML_BASICSURVEY_SHEET_1 = "U04004_Basic_Survey_One_Report.jrxml";

    /**
     * U04004_認定調査票(基本調査２).jrxml
     */
    public static final String U04004_JRXML_BASICSURVEY_SHEET_2 = "U04004_Basic_Survey_Two_Report.jrxml";
    /**
     * U04004_認定調査票(基本調査３).jrxml
     */
    public static final String U04004_JRXML_BASICSURVEY_SHEET_3 = "U04004_Basic_Survey_Three_Report.jrxml";
    /**
     * U04004_認定調査票(基本調査４).jrxml
     */
    public static final String U04004_JRXML_BASICSURVEY_SHEET_4 = "U04004_Basic_Survey_Four_Report.jrxml";
    /**
     * U04004_認定調査票(基本調査５).jrxml
     */
    public static final String U04004_JRXML_BASICSURVEY_SHEET_5 = "U04004_Basic_Survey_Five_Report.jrxml";
    /**
     * U04004_認定調査票(特記事項)(H24.4～).jrxml
     */
    public static final String U04004_JRXML_SPECIALNOTEMIND = "U04004_Special_Note_Mind_H24_Report.jrxml";
    /**
     * U04004_認定調査票(特記事項.別様式)(H24.4～).jrxml
     */
    public static final String U04004_SPECIAL_NOTE_MIND_BY_STYLE = "U04004_Special_Note_Mind_By_Style_Report.jrxml";
    /**
     * U04004_要介護度による集計表(H24/4～).jrxml
     */
    public static final String U04004_YOKAIGODO_SHUKEI_H24 = "U04004_yokaigodoShukeiH24.jrxml";
    /**
     * U04005_認定調査票(基本調査１)(R3.4～).jrxml
     */
    public static final String U04005_JRXML_BASICSURVEYR34_SHEET_1 = "U04005_Basic_Survey_One_R34_Report.jrxml";
    /**
     * U04005_認定調査票(基本調査２)(R3.4～).jrxml
     */
    public static final String U04005_JRXML_BASICSURVEYR34_SHEET_2 = "U04005_Basic_Survey_Two_R34_Report.jrxml";
    /**
     * U04005_認定調査票(基本調査３)(R3.4～).jrxml
     */
    public static final String U04005_JRXML_BASICSURVEYR34_SHEET_3 = "U04005_Basic_Survey_Three_R34_Report.jrxml";
    /**
     * U04005_認定調査票(基本調査４)(R3.4～).jrxml
     */
    public static final String U04005_JRXML_BASICSURVEYR34_SHEET_4 = "U04005_Basic_Survey_Four_R34_Report.jrxml";
    /**
     * U04005_認定調査票(基本調査５)(R3.4～).jrxml
     */
    public static final String U04005_JRXML_BASICSURVEYR34_SHEET_5 = "U04005_Basic_Survey_Five_R34_Report.jrxml";
    /**
     * U04005_認定調査票(特記事項)(R3.4～).jrxml
     */
    public static final String U04005_JRXML_SPECIALNOTEMIND = "U04005_Special_Note_Mind_R34_Report.jrxml";
    /**
     * U04005_認定調査票(特記事項.別様式)(R3.4～).jrxml
     */
    public static final String U04005_SPECIAL_NOTE_MIND_BY_STYLE_R34 = "U04005_Special_Note_Mind_By_Style_R34_Report.jrxml";
    /**
     * U04005_要介護度による集計表(R3/4～)
     */
    public static final String U04005_YOKAIGODO_SHUKEI_R34 = "U04005_yokaigodoShukeiR3.jrxml";

    /**
     * jrxmファイルパス（U06026_生活アセスメント(2)(3)）
     */
    public static final String JRXML_LIFE_ASSESSMENT23_H21 = "U06026_LifeAssessment23_H21.jrxml";

    /**
     * jrxmファイルパス（U06200_生活アセスメント(2)(3)）
     */
    public static final String JRXML_LIFE_ASSESSMENT23_R3 = "U06200_LifeAssessment23_R3.jrxml";

    /**
     * jrxmファイルパス（U00921_サービス担当者に対する照会(依頼)内容）
     */
    public static final String JRXML_SERVICE_MANAGER_INQUIRY_CONTENTS = "U00921_Service_Manager_Inquiry_Contents.jrxml";

    /**
     * jrxmファイルパス（U0P650_アセスメント総括表）
     */
    public static final String JRXML_ASSESSMENT_SUMMARY_TABLE = "U0P650_AssessmentSummaryTable.jrxml";

    /**
     * 領域区分:"A"
     */
    public static final String RYOIKI_KBN_A = "A";

    /**
     * 領域区分:"B"
     */
    public static final String RYOIKI_KBN_B = "B";

    /**
     * 領域区分:"C"
     */
    public static final String RYOIKI_KBN_C = "C";

    /**
     * 領域区分:"D"
     */
    public static final String RYOIKI_KBN_D = "D";

    /**
     * 帳票コード：（Ⅱ）実施計画～①(H21改訂版)
     */
    public static final String REPORT_CODE_IMPLEMENTATION_PLAN1 = "3GKU0P150P001";

    /**
     * 帳票コード：（Ⅱ）実施計画～③(H21改訂版
     * 
     */
    public static final String REPORT_CODE_IMPLEMENTATION_PLAN3 = "3GKU0P152P001";

    /**
     * jrxmファイルパス（U00911_サービス担当者会議の要点）
     */
    public static final String JRXML_SERVICE_MANAGER_MEETING_MAINT_POINT_U00911 = "U00911_Service_Manager_Meeting_Main_Point.jrxml";

    /** 和暦 年/月（日なし）: 例）R6/6 */
    public static final int JAPANESE_ERA_YEAR_MONTH_SLASH = 1;

    /** 和暦 年 / 月（スペースとスラッシュ区切り）: 例）R6 /6 */
    public static final int JAPANESE_ERA_YEAR_MONTH_FORMATTED = 2;

    /** 和暦 年月日（漢字）: 例）R6年07月09日 */
    public static final int JAPANESE_ERA_FULL_KANJI = 3;

    /** 和暦 年/月/日（パディングあり）: 例）R 6/ 7/ 9 */
    public static final int JAPANESE_ERA_FULL_PADDED = 4;

    /** 和暦 年/月/日（パディングなし）: 例）R6/7/9 */
    public static final int JAPANESE_ERA_FULL_UNPADDED = 5;

    /**
     * jrxmファイルパス（API定義書_E00510_サービス担当者会議の要点）
     */
    public static final String JRXML_E00510_SERVICE_MANAGER_MEETING_MAIN_POINT = "E00510_Service_Manager_Meeting_Main_Point.jrxml";

    /**
     * 元号:"昭"
     */
    public static final String GENGOU_1 = "昭";

    /**
     * 元号:"大"
     */
    public static final String GENGOU_2 = "大";

    /**
     * 元号:"明"
     */
    public static final String GENGOU_3 = "明";

    /**
     * 課題番号の"①"
     */
    public static final String KADAI_BANGOU_STR_1 = "①";

    /**
     * 課題番号の"②"
     */
    public static final String KADAI_BANGOU_STR_2 = "②";

    /**
     * 課題番号の"③"
     */
    public static final String KADAI_BANGOU_STR_3 = "③";

    /**
     * 課題番号の"④"
     */
    public static final String KADAI_BANGOU_STR_4 = "④";

    /**
     * 課題番号の"⑤"
     */
    public static final String KADAI_BANGOU_STR_5 = "⑤";

    /**
     * 課題番号の"⑥"
     */
    public static final String KADAI_BANGOU_STR_6 = "⑥";

    /**
     * 課題番号の"⑦"
     */
    public static final String KADAI_BANGOU_STR_7 = "⑦";

    /**
     * 課題番号の"⑧"
     */
    public static final String KADAI_BANGOU_STR_8 = "⑧";

    /**
     * 課題番号の"⑨"
     */
    public static final String KADAI_BANGOU_STR_9 = "⑨";

    /**
     * 課題番号の"⑩"
     */
    public static final String KADAI_BANGOU_STR_10 = "⑩";

    /**
     * 課題番号の"⑪"
     */
    public static final String KADAI_BANGOU_STR_11 = "⑪";

    /**
     * 課題番号の"⑫"
     */
    public static final String KADAI_BANGOU_STR_12 = "⑫";

    /**
     * 課題番号の"⑬"
     */
    public static final String KADAI_BANGOU_STR_13 = "⑬";

    /**
     * 課題番号の"⑭"
     */
    public static final String KADAI_BANGOU_STR_14 = "⑭";

    /**
     * 課題番号の"⑮"
     */
    public static final String KADAI_BANGOU_STR_15 = "⑮";

    /**
     * 課題番号の"⑯"
     */
    public static final String KADAI_BANGOU_STR_16 = "⑯";

    /**
     * 課題番号の"⑰"
     */
    public static final String KADAI_BANGOU_STR_17 = "⑰";

    /**
     * 課題番号の"⑱"
     */
    public static final String KADAI_BANGOU_STR_18 = "⑱";

    /**
     * 課題番号の"⑲"
     */
    public static final String KADAI_BANGOU_STR_19 = "⑲";

    /**
     * 課題番号の"⑳"
     */
    public static final String KADAI_BANGOU_STR_20 = "⑳";
    /**
     * 区分_文字列(変更不可)
     */
    public static final Integer CHK_KBN_LETTER_COLUMNS = 1;
    /**
     * 区分_選択項目
     */
    public static final Integer CHK_KBN_SELECT_ITEM = 2;
    /**
     * 区分_文章入力項目
     */
    public static final Integer CHK_KBN_SENTENCE_INPUT_ITEM = 3;
    /**
     * TODAY
     */
    public static final String STR_TODAY = "TODAY";
    /**
     * jrxmファイルパス（U0P650_アセスメント表（領域A））
     */
    public static final String U0P650_AssessmentSheetRegionA = "U0P650_Assessment_regionA.jrxml";
    /**
     * jrxmファイルパス（U0P650_アセスメント表（領域B））
     */
    public static final String U0P650_AssessmentSheetRegionB = "U0P650_Assessment_regionB.jrxml";
    /**
     * jrxmファイルパス（U0P650_アセスメント表（領域C））
     */
    public static final String U0P650_AssessmentSheetRegionC = "U0P650_Assessment_regionC.jrxml";
    /**
     * jrxmファイルパス（U0P650_アセスメント表（領域D））
     */
    public static final String U0P650_AssessmentSheetRegionD = "U0P650_Assessment_regionD.jrxml";

    /**
     * jrxmファイルパス（API定義書_U01012_介護支援経過日別一覧）
     */
    public static final String JRXML_U01012_NURSING_CARE_SUPPORT_DAILY_LIST = "U01012_NursingCareSupportDailyList.jrxml";

    /**
     * jrxmファイルパス（U01501_主治医意見書①）
     */
    public static final String JRXML_U01501_ATTENDING_PHYSICIAN1_H21 = "U01501_AttendingPhysician1_H21.jrxml";

    /**
     * jrxmファイルパス（U01501_主治医意見書②）
     */
    public static final String JRXML_U01501_ATTENDING_PHYSICIAN2_H21 = "U01501_AttendingPhysician2_H21.jrxml";

    /**
     * jrxmファイルパス（U01501_主治医意見書(一括)）
     */
    public static final String JRXML_U01501_ATTENDING_PHYSICIANALL_H21 = "U01501_AttendingPhysician_All_H21.jrxml";

    /**
     * チェック項目利用者一覧表帳票タイトル
     */
    public static final String CHECK_ITEM_USER_TITLE = "チェック項目利用者一覧表";

    /**
     * jrxmファイルパス（E00300_基本チェックリスト）
     */
    public static final String JRXML_BASIC_CHECKLIST = "E00300_Basic_Checklist.jrxml";

    /**
     * V00251_サービス利用票.jrxml
     */
    public static final String V00251_JRXML_SIMULATIONRITEI = "V00251_SimulationRiteiReport.jrxml";

    /**
     * V00241_サービス提供利用者一覧.jrxml
     */
    public static final String V00241_JRXML_SERVICEUSERLIST = "V00241_ServiceUserListReport.jrxml";

    /**
     * V00241_サービス提供依頼書.jrxml
     */
    public static final String V00241_JRXML_TEIKYOIRAI = "V00241_TeikyoiraiReport.jrxml";

    /**
     * V00241_提供票送付状.jrxml
     */
    public static final String V00241_JRXML_TEIKYOSOUFU = "V00241_TeikyoSoufuReport.jrxml";

    /**
     * jrxmファイルパス（API定義書_E00510_介護予防支援経過記録）
     */
    public static final String JRXML_E00500_PREVENT_CARE_ELAPSED_SUPPORT_RECORD = "E00500_Prevent_Care_Elapsed_Support_Record.jrxml";

    /**
     * U04004_要介護度一覧表(H24.4～).jrxml
     */
    public static final String U04004_JRXML_LEVELOF = "U04004_要介護度一覧表(H24.4～).jrxml";
    /**
     * 検討項目：×
     */
    public static final String KENTO_FLG_0 = "×";

    /**
     * 検討項目：○
     */
    public static final String KENTO_FLG_1 = "○";

    /**
     * jrxmファイルパス（API定義書_U04004_調査内容一覧票(H24.4～)）
     */
    public static final String JRXML_U04004_SURVEY_CONTENTS_LIST = "U04004_surveyContentsListH24Report.jrxml";

    /**
     * jrxmファイルパス（API定義書_V00340_給付状況一覧）
     */
    public static final String JRXML_V00340_BENEFIT_STATUS_LIST = "V00340_benefitStatusListReport.jrxml";
    /**
     * jrxmファイルパス（V00340_給付状況一覧(個別)）
     */
    public static final String JRXML_KYUUFUJYOUKYOU1RAN_KOBETSU = "V00340_benefitStatusKobetsuListReport.jrxml";

    /**
     * jrxmファイルパス（U04004_要介護度一覧表(H24.4～)）
     */
    public static final String JRXML_U04004_LEVELOF = "U04004_LevelOfCareRequiredListReport.jrxml";

    /**
     * jrxmファイルパス（U04005_要介護度一覧表(R3.4～)）
     */
    public static final String JRXML_U04005_LEVELOF = "U04005_LevelOfCareRequiredListR34Report.jrxml";

    /**
     * jrxmファイルパス（U04004_調査員別調査対象者一覧表(H24.4～)）
     */
    public static final String JRXML_U04004_INVESTIGATORS = "U04004_InvestigatorsListH24Report.jrxml";

    /**
     * jrxmファイルパス（U04005_調査員別調査対象者一覧表(R3.4～)）
     */
    public static final String JRXML_U04005_INVESTIGATORS = "U04005_InvestigatorsListR34Report.jrxml";

    /**
     * jrxmファイルパス（E00300_基本チェックリスト）
     */
    public static final String JRXML_BASIC_CHECKLIST_NO_ITEM = "E00300_Basic_Checklist_No_Item.jrxml";

    /**
     * 主介護者タイトル
     */
    public static final String MAIN_KAIGO_TITLE = "主介護者";

    /**
     * 副介護者タイト
     */
    public static final String SUB_KAIGO_TITLE = "副介護者";

    /**
     * 機能名:PRT
     */
    public static final String KINOU_NAME_PRT = "PRT";

    /**
     * 帳票セクション番号
     * 
     * 3GKU0082KP001
     */
    public static final String REPORT_NUMBER_3GKU0082KP001 = "3GKU0082KP001";

    /**
     * 帳票セクション番号
     * 
     * 3GKU0082SP001
     */
    public static final String REPORT_NUMBER_3GKU0082SP001 = "3GKU0082SP001";

    /**
     * キー (KOJINHOGO_FLG)
     */
    public static final String S_KOJINHOGO_FLG = "kojinhogo_flg";

    /**
     * String "0"
     */
    public static final String STR_0 = "0";

    /**
     * String "1"
     */
    public static final String STR_1 = "1";

    /**
     * String "2"
     */
    public static final String STR_2 = "2";

    /**
     * String "3"
     */
    public static final String STR_3 = "3";

    /**
     * Double 0
     */
    public static final Double DOUBLE_ZERO = 0.0;

    /**
     * Integer 0
     */
    public static final Integer NUMBER_0 = 0;

    /**
     * Integer 1
     */
    public static final Integer INT_1 = 1;

    /**
     * true
     */
    public static final String STR_TRUE = "true";

    /**
     * false
     */
    public static final String STR_FALSE = "false";

    /**
     * 殿
     */
    public static final String KEISHO_TONO = "殿";

    /**
     * 23031
     */
    public static final String STR_23031 = "23031";

    /**
     * 43031
     */
    public static final String STR_43031 = "43031";

    /**
     * 63031
     */
    public static final String STR_63031 = "63031";

    /**
     * 特定施設サービス利用票別表
     */
    public static final String REPORT_TITLE_TABLE_251_1 = "特定施設サービス利用票別表";

    /**
     * サービス利用票別表
     */
    public static final String REPORT_TITLE_TABLE_251_2 = "サービス利用票別表";

    /**
     * 定数:"/"
     */
    public static final String STR_DELIMITER = "/";

    /**
     * 左括弧の定数
     */
    public static final String LEFT_PARENTHESIS = "(";

    /**
     * 右括弧の定数
     */
    public static final String RIGHT_PARENTHESIS = ")";

    /**
     * "合計"
     */
    public static final String STR_SUM = "合計";

    /**
     * 日:01
     */
    public static final String DAY_01 = "01";

    /**
     * 文字列:"/01"
     */
    public static final String STRING_FIRST_DAY = "/01";

    /**
     * 見通しの桁数（144）
     */
    public static final int ASSESSMENTSHEET_REGION_MAXLINELENGTH = 144;

    /**
     * jrxmファイルパス（U01900_評価表）
     */
    public static final String JRXML_EVALUATION_REPORT = "U01900_evaluationReport.jrxml";
    /**
     * jrxmファイルパス（U04004_認定調査票(概況調査)(H24.4～)）
     */
    public static final String JRXML_LIFE_GENERAL_SITUATION_SURVEY = "U04004_GeneralSituationSurvey.jrxml";

    /**
     * jrxmファイルパス（U04005_認定調査票(概況調査)(R3.4～)）
     */
    public static final String JRXML_LIFE_GENERAL_SITUATION_SURVEY_R34 = "U04005_GeneralSituationSurveyR34.jrxml";

    /**
     * jrxmファイルパス（V00231_利用者別利用事業所一覧）
     */
    public static final String V00231_JRXML_USEROFFICELIST = "V00231_UserOfficeListReport.jrxml";

    /**
     * jrxmファイルパス（U0P145_DailyTaskTable_A3）
     */
    public static final String U0P145_DAILY_TASK_TABLE_A3 = "U0P145_DailyTaskTable_A3.jrxml";

    /**
     * jrxmファイルパス（U0P145_DailyTaskTable_A4）
     */
    public static final String U0P145_DAILY_TASK_TABLE_A4 = "U0P145_DailyTaskTable_A4.jrxml";

    /**
     * jrxmファイルパス（U0081K_Plan1Report）
     */
    public static final String U0081K_PLAN1_REPORT = "U0081K_Plan1Report.jrxml";

    /**
     * jrxmファイルパス（U0081S_Plan1Report）
     */
    public static final String U0081S_PLAN1_REPORT = "U0081S_Plan1Report.jrxml";

    /**
     * jrxmファイルパス（U0P157_MONTHLY_YEARLY_REPORT）
     */
    public static final String U0P157_MONTHLY_YEARLY_REPORT = "U0P157_MonthlyYearlyReport.jrxml";

    /**
     * jrxmファイルパス（U00861_DAILY_ROUTINE_PLAN_REPORT）
     */
    public static final String U00861_DAILY_ROUTINE_PLAN_REPORT = "U00861_DailyRoutinePlanReport.jrxml";

    /**
     * jrxmファイルパス（V00231_サービス利用票確認表）
     */
    public static final String V00231_RIYOU_KAKUNIN_REPORT = "V00231_RiyouKakuninReport.jrxml";

    /**
     * jrxmファイルパス（U04004_１次判定調査票(H24.4～)）
     */
    public static final String U04004_FIRST_DECISION_SURVEY_H24_REPORT = "U04004_firstDecisionSurveyH24.jrxml";

    /**
     * jrxmファイルパス（U04005_１次判定調査票(R3.4～)）
     */
    public static final String U04005_FIRST_DECISION_SURVEY_H34_REPORT = "U04005_firstDecisionSurveyR34.jrxml";

    /**
     * jrxmファイルパス（V00241_サービス提供票別表）
     */
    public static final String V00241_TEIKYOHYOBETU = "V00241_teikyohyobetuReport_1.jrxml";

    /**
     * jrxmファイルパス（V00241_サービス提供票別表）
     */
    public static final String V00241_TEIKYOHYOBETU_2 = "V00241_teikyohyobetuReport_2.jrxml";

    /**
     * jrxmファイルパス（U06053_個人別課題立案票A3_1）
     */
    public static final String U06053_PERSONALIZED_TASKPROPOSAL_FORMA3_1 = "U06053_PersonalizedTaskProposalFormA3_1.jrxml";

    /**
     * jrxmファイルパス（U06053_個人別課題立案票A3_2）
     */
    public static final String U06053_PERSONALIZED_TASKPROPOSAL_FORMA3_2 = "U06053_PersonalizedTaskProposalFormA3_2.jrxml";

    /**
     * jrxmファイルパス（U06053_個人別課題立案票A4_1）
     */
    public static final String U06053_PERSONALIZED_TASKPROPOSAL_FORMA4_1 = "U06053_PersonalizedTaskProposalFormA4_1.jrxml";

    /**
     * jrxmファイルパス（U06053_個人別課題立案票A4_2）
     */
    public static final String U06053_PERSONALIZED_TASKPROPOSAL_FORMA4_2 = "U06053_PersonalizedTaskProposalFormA4_2.jrxml";

    /**
     * jrxmファイルパス（U0P150_（Ⅱ）実施計画～①(H21改訂版)_記入用）
     */
    public static final String JRXML_U0P150_IMPLEMENTATIONPLAN1_H21_ON = "U0P150_ImplementationPlan1_H21_ON.jrxml";
    /**
     * jrxmファイルパス（U0P150_（Ⅱ）実施計画～①(H21改訂版)_印鑑欄表示）
     */
    public static final String JRXML_U0P150_IMPLEMENTATIONPLAN1_H21_OFF_HYOUJI = "U0P150_ImplementationPlan1_H21_OFF_HYOUJI.jrxml";
    /**
     * jrxmファイルパス（U0P150_（Ⅱ）実施計画～①(H21改訂版)_印鑑欄非表示）
     */
    public static final String JRXML_U0P150_IMPLEMENTATIONPLAN1_H21_OFF_HIHYOUJI = "U0P150_ImplementationPlan1_H21_OFF_HIHYOUJI.jrxml";

    /**
     * jrxmファイルパス（U0P151_（Ⅱ）実施計画～②(H21改訂版)_記入用）
     */
    public static final String JRXML_U0P151_IMPLEMENTATIONPLAN2_H21_ON = "U0P151_ImplementationPlan2_H21_ON.jrxml";
    /**
     * jrxmファイルパス（U0P151_（Ⅱ）実施計画～②(H21改訂版)_印鑑欄表示）
     */
    public static final String JRXML_U0P151_IMPLEMENTATIONPLAN2_H21_OFF_HYOUJI = "U0P151_ImplementationPlan2_H21_OFF_Hyouji.jrxml";
    /**
     * jrxmファイルパス（U0P151_（Ⅱ）実施計画～②(H21改訂版)_印鑑欄非表示）
     */
    public static final String JRXML_U0P151_IMPLEMENTATIONPLAN2_H21_OFF_HIHYOUJI = "U0P151_ImplementationPlan2_H21_OFF_HiHyouji.jrxml";

    /**
     * jrxmファイルパス（U01502_主治医意見書①_R3）
     */
    public static final String U01502_ATTENDING_PHYSICIAN1_R3 = "U01502_AttendingPhysician1_R3.jrxml";

    /**
     * jrxmファイルパス（承認欄設定）
     */
    public static final String SHONINSET_REPORT = "SubReport_ShoninInfo.jrxml";

    /**
     * JASPERファイルパス（承認欄設定）
     */
    public static final String SHONINSET_JASPER = "SubReport_ShoninInfo.jasper";

    /**
     * "."（U06010_ケアチェック表５）
     */
    public static final String DECIMAL_POINT = ".";

    /**
     * （U06010_ケアチェック表１（食事、水分摂取等に関するケア））
     */
    public static final String B1_CD_1 = "1";

    /**
     * （U06010_ケアチェック表２（排泄に関するケア））
     */
    public static final String B1_CD_2 = "2";

    /**
     * （U06010_ケアチェック表３（入浴、清拭等に関するケア））
     */
    public static final String B1_CD_3 = "3";

    /**
     * （U06010_ケアチェック表４（洗面、口腔清潔、整容、更衣に関するケア））
     */
    public static final String B1_CD_4 = "4";

    /**
     * （U06010_ケアチェック表５（基本動作介助、リハビリテーション等に関するケア））
     */
    public static final String B1_CD_5 = "5";

    /**
     * （U06010_ケアチェック表６（医療、健康に関するケア））
     */
    public static final String B1_CD_6 = "6";

    /**
     * （U06010_ケアチェック表７（心理・社会面等に関するケア））
     */
    public static final String B1_CD_7 = "7";

    /**
     * 非該当
     */
    public static final String CARE_LEVEL_0 = "非該当";
    /**
     * 要支援1
     */
    public static final String CARE_LEVEL_1 = "要支援1";
    /**
     * 要支援２
     */
    public static final String CARE_LEVEL_2 = "要支援２";
    /**
     * 要介護3
     */
    public static final String CARE_LEVEL_3 = "要介護3";
    /**
     * 要介護4
     */
    public static final String CARE_LEVEL_4 = "要介護4";
    /**
     * 要介護５
     */
    public static final String CARE_LEVEL_5 = "要介護５";
    /**
     * 経過的要介護
     */
    public static final String CARE_LEVEL_6 = "経過的要介護";
    /**
     * 要介護1
     */
    public static final String CARE_LEVEL_7 = "要介護1";
    /**
     * 要介護2
     */
    public static final String CARE_LEVEL_8 = "要介護2";
    /**
     * 見込み
     */
    public static final String SERIES_0 = "見込み";
    /**
     * 確定
     */
    public static final String SERIES_1 = "確定";

    /**
     * 平成 9年 9月 9日 に変換する : 1
     */
    public static final Integer HEISERI_9_SEP_9_1 = 1;

    /**
     * サービス提供年月日 : "/28"
     */
    public static final String YYMM_D_28 = "/28";

    /**
     * 計算フラグ : 0
     */
    public static final Integer CACL_YOJI_FLG_0 = 0;

    /**
     * 数字: 0
     */
    public static final int INT_0 = 0;

    /**
     * 固定値: "0"
     */
    public static final String STRING_0 = "0";

    /**
     * 値: "riyousha_Nm"
     */
    public static final String STRING_RIYOUSHA_NM = "riyousha_Nm";

    /**
     * 値: "sho_Ku_Id"
     */
    public static final String STRING_SHOKU_ID = "sho_Ku_Id";

    /**
     * 値: "name_kana"
     */
    public static final String STRING_NAME_KANA = "name_kana";

    /**
     * 値: "birthday"
     */
    public static final String STRING_BIRTHDAY = "birthday";

    /**
     * 値: "name_knj"
     */
    public static final String STRING_NAME_KNJ = "name_knj";

    /**
     * 値: "tel"
     */
    public static final String STRING_TEL = "tel";

    /**
     * 値: "address"
     */
    public static final String STRING_ADDRESS = "address";

    /**
     * 値: "h_hoken_no"
     */
    public static final String STRING_H_HOKEN_NO = "h_hoken_no";

    /**
     * 値: "hhno"
     */
    public static final String STRING_HHNO = "hhno";

    /**
     * 値: "3GKV00231P008"
     */
    public static final String STRING_3GKV00231P008 = "3GKV00231P008";

    /**
     * 伏字フラグ: "1"
     */
    public static final String FUSEJI_FLG_1 = "1";

    /**
     * 年度印刷フラグ: "1"
     */
    public static final String YEAR_PRINT_FLG_ON = "1";

    /**
     * 年度印刷フラグ: "0"
     */
    public static final String YEAR_PRINT_FLG_OFF = "0";

    /**
     * jrxmファイルパス（U00852_週間サービス計画表）
     */
    public static final String JRXML_U00852_SHUUKAN = "U00852_ShuukanServiceReport.jrxml";

    /**
     * jrxmファイルパス（週間サービス計画表（R3/4改訂））
     */
    public static final String JRXML_U00852_SHUUKANR34 = "U00852_shuukanServiceR34Report.jrxml";

    /**
     * jrxmファイルパス（V00231_サービス利用票別表） サービス提供年月が2021年4月以上の場合
     */
    public static final String JRXML_V00231_SERVICE_USE_ANNEXED_TABLE_REPORT_1 = "V00231_serviceUseAnnexedTableReport(1).jrxml";

    /**
     * jrxmファイルパス（V00231_サービス利用票別表） 以外の場合
     */
    public static final String JRXML_V00231_SERVICE_USE_ANNEXED_TABLE_REPORT_2 = "V00231_serviceUseAnnexedTableReport(2).jrxml";

    /**
     * フォーマット（"###,###,##0"）
     */
    public static final String DECIMAL_FORMAT = "###,###,##0";

    /**
     * jrxmファイルパス（V00231_利用サービス カレンダー表）
     */
    public static final String JRXML_V00231_USE_SERVICE_CALENDAR_REPORT = "V00231_UseServiceCalendarReport.jrxml";

    /**
     * jrxmファイルパス（V00231_利用事業所 カレンダー表）
     */
    public static final String JRXML_V00231_USE_OFFICE_CALENDAR_REPORT = "V00231_UseOfficeCalendarReport.jrxml";

    /**
     * jrxmファイルパス（V00241_事業者毎印刷）
     */
    public static final String JRXML_V00241_BUSINESS_PER_PRINT = "V00241_BusinessPerPrintReport.jrxml";

    /**
     * jrxmファイルパス（V00241_事業所別利用者一覧）
     */
    public static final String JRXML_V00241_OFFICE_USER_LIST = "V00241_OfficeUserListReport.jrxml";

    /**
     * jrxmファイルパス（U04005_調査内容一覧票(R3.4～)）
     */
    public static final String JRXML_U04005_SURVEY_CONTENTS_LIST_R3 = "U04005_SurveyContentsListR3.jrxml";

    /**
     * jrxmファイルパス（V00231_利用者毎印刷）
     */
    public static final String V00231_JRXML_USER_PER_PRINT_REPORT = "V00231_UserPerPrintReport.jrxml";

    /**
     * jrxmファイルパス（U00501_在宅復帰および在宅支援の検討）
     */
    public static final String U00501_BEING_AT_HOME_SUPPORT_CONSIDER_REPORT = "U00501_BeingAtHomeSupportConsiderReport.jrxml";

    /**
     * 数値: 2
     */
    public static final Integer INT_2 = 2;

    /**
     * 数値: 3
     */
    public static final Integer INT_3 = 3;

    /**
     * 数値: 4
     */
    public static final Integer INT_4 = 4;

    /**
     * サブレポート1
     */
    public static final String SUB_REPORT_1 = "subreport1";

    /**
     * サブレポート2
     */
    public static final String SUB_REPORT_2 = "subreport2";

    /**
     * サブレポート2
     */
    public static final String SUB_REPORT_3 = "subreport3";

    /**
     * SUB_REPORT_2_PARAMS
     */
    public static final String SUB_REPORT_2_PARAMS = "SUB_REPORT_2_PARAMS";

    /**
     * jrxmファイルパス U0P148_週間表
     */
    public static final String U0P148_JRXML_SHUUKAN = "U0P148_ShuuKanReport.jrxml";

    /**
     * ／５
     */
    public static final String SLASH_5 = "／５";

    /**
     * ／２
     */
    public static final String SLASH_2 = "／２";

    /**
     * ／３
     */
    public static final String SLASH_3 = "／３";

    /**
     * 基本チェックリストの（該当した項目数）／（質問項目数）を記入して下さい。
     */
    public static final String PROGRAM_NAME_1 = "基本チェックリストの（該当した項目数）／（質問項目数）を記入して下さい。\r\n地域支援事業の場合は必要な事業プログラムの枠内の数字に○印をつけて下さい。";

    /**
     * 基本チェックリストの（該当した質問項目数）／（質問項目数）をお書き下さい。
     */
    public static final String PROGRAM_NAME_2 = "基本チェックリストの（該当した質問項目数）／（質問項目数）をお書き下さい。\r\n地域支援事業の場合は必要な事業プログラムの枠内の数字に○印をつけて下さい。";

    /**
     * 健康状態について：\r\n□主治医意見書、健診結果、観察結果等を踏まえた留意点
     */
    public static final String KENKO_RYUITEN_KNJ_LABEL_1 = "健康状態について：\r\n□主治医意見書、健診結果、観察結果等を踏まえた留意点";
    /**
     * 健康状態について：\r\n□主治医意見書、生活機能評価等を踏まえた留意点
     */
    public static final String KENKO_RYUITEN_KNJ_LABEL_2 = "健康状態について：\r\n□主治医意見書、生活機能評価等を踏まえた留意点";

    /**
     * 総合的な方針：生活不活発病の改善予防のポイント
     */
    public static final String SOGO_HOSHIN_KNJ_LABEL_1 = "総合的な方針：生活不活発病の改善予防のポイント";

    /**
     * 総合的な方針：生活不活発病の改善・予防のポイント
     */
    public static final String SOGO_HOSHIN_KNJ_LABEL_2 = "総合的な方針：生活不活発病の改善・予防のポイント";

    /**
     * 【本来行うべき支援が実施できない場合】\r\n 妥当な支援の実施に向けた方針
     */
    public static final String DATO_HOSHIN_KNJ_LABEL_1 = "【本来行うべき支援が実施できない場合】\r\n 妥当な支援の実施に向けた方針";

    /**
     * 本人等のｾﾙﾌｹｱや家\r\n族の支援、ｲﾝﾌｫｰﾏﾙ\r\nｻｰﾋﾞｽ（民間ｻｰﾋﾞｽ）
     */
    public static final String INFORMAL_KNJ_LABEL_1 = "本人等のｾﾙﾌｹｱや家\r\n族の支援、ｲﾝﾌｫｰﾏﾙ\r\nｻｰﾋﾞｽ（民間ｻｰﾋﾞｽ）";

    /**
     * 本人等のｾﾙﾌｹｱ\r\nや家族の支援、\r\nｲﾝﾌｫｰﾏﾙｻｰﾋﾞｽ
     */
    public static final String INFORMAL_KNJ_LABEL_2 = "本人等のｾﾙﾌｹｱ\r\nや家族の支援、\r\nｲﾝﾌｫｰﾏﾙｻｰﾋﾞｽ";

    /**
     * 介護保険ｻｰﾋﾞｽ
     */
    public static final String SERVICE_1_LABEL_1 = "介護保険ｻｰﾋﾞｽ";

    /**
     * 介護保険
     */
    public static final String SERVICE_1_LABEL_2 = "介護保険";

    /**
     * 又は地域支援事業
     */
    public static final String SERVICE_2_LABEL_1 = "又は地域支援事業";

    /**
     * サービス
     */
    public static final String SERVICE_2_LABEL_2 = "サービス";

    /**
     * （総合事業のｻｰﾋﾞｽ）
     */
    public static final String SERVICE_3_LABEL_1 = "（総合事業のｻｰﾋﾞｽ）";

    /**
     * または地域支援事業
     */
    public static final String SERVICE_3_LABEL_2 = "または地域支援事業";

    /**
     * 支援事業\r\n（利用先）
     */
    public static final String SHIEN_JIGYO_LABEL_1 = "支援事業\r\n（利用先）";

    /**
     * 支援事業
     */
    public static final String SHIEN_JIGYO_LABEL_2 = "支援事業";
    /**
     * 地域包括支援\r\nセンター意見
     */
    public static final String CENTER_IKEN_LABEL_1 = "地域包括支援\r\nセンター意見";

    /**
     * 地域包括支援\r\nセンター
     */
    public static final String CENTER_IKEN_LABEL_2 = "地域包括支援\r\nセンター";

    /**
     * 上記計画について、同意いたします
     */
    public static final String DOI_CONTENT_1 = "上記計画について、同意いたします";

    /**
     * 空白スペース
     */
    public static final String BLANK_SPACE = " ";

    /**
     * ケアプラン方式:1 包括的自立支援プログラム
     */
    public static final String CPN_FLG_1 = "1";

    /**
     * ケアプラン方式:2 居宅サービス計画ガイドライン
     */
    public static final String CPN_FLG_2 = "2";

    /**
     * ケアプラン方式:5 新型養護老人ホームパッケージプラン
     */
    public static final String CPN_FLG_5 = "5";

    /**
     * ケアプラン方式:8 情報収集・課題検討シート
     */
    public static final String CPN_FLG_8 = "8";

    /**
     * ケアプラン方式:9 インターライ方式
     */
    public static final String CPN_FLG_9 = "9";

    /**
     * 記号 :
     */
    public static final String SYMBOL_COLON = ":";

    /**
     * 要介護度のチェック値
     */
    public static final List<String> YOKAIGODO_CHECK_VALUES = Arrays.asList("1", "2", "3", "4", "5");

    /**
     * 現在の状況のマッピング
     */
    public static final Map<String, String> NOW_STATUS_VALUS = Map.ofEntries(Map.entry("1", "介護老人福祉施設"),
            Map.entry("2", "介護老人保健施設"), Map.entry("3", "介護療養型医療施設"), Map.entry("4", "その他の施設"),
            Map.entry("5", "認知症対応型共同生活介護適用施設(ｸﾞﾙｰﾌﾟﾎｰﾑ)"), Map.entry("7", "医療機関(医療保険適用療養病床)"),
            Map.entry("8", "医療機関(療養病床以外)"), Map.entry("9", "介護医療院"), Map.entry("10", "養護老人ﾎｰﾑ"),
            Map.entry("11", "軽費老人ﾎｰﾑ"), Map.entry("12", "有料老人ﾎｰﾑ"), Map.entry("13", "ｻｰﾋﾞｽ付き高齢者向け住宅"));

    /**
     * 障害高齢者自立度のマッピング
     */
    public static final Map<Integer, String> SHOGAI_KOUREISHA_JIRITUDO_VALUES = Map.ofEntries(Map.entry(1, "自立"),
            Map.entry(2, "Ｊ１"), Map.entry(3, "Ｊ２"), Map.entry(4, "Ａ１"), Map.entry(5, "Ａ２"), Map.entry(6, "Ｂ１"),
            Map.entry(7, "Ｂ２"), Map.entry(8, "Ｃ１"), Map.entry(9, "Ｃ２"));

    /**
     * 認知症高齢者自立度のマッピング
     */
    public static final Map<Integer, String> NINCHISHO_KOUREISHA_JIRITUDO_VALUES = Map.ofEntries(Map.entry(1, "自立"),
            Map.entry(2, "Ⅰ"), Map.entry(3, "Ⅱa"), Map.entry(4, "Ⅱb"), Map.entry(5, "Ⅲa"), Map.entry(6, "Ⅲb"),
            Map.entry(7, "Ⅳ"), Map.entry(8, "Ｍ"));

    /**
     * 前回結果印刷フラグ 「1:チェックON」
     */
    public static final String LAST_RESULT_PRINT_FLG_CHECK_ON = "1";

    /**
     * （１／２）
     */
    public static final String PAGE_1_OF_2 = "（１／２）";
    /**
     * （２／２）
     */
    public static final String PAGE_2_OF_2 = "（２／２）";

    /**
     * 分
     */
    public static final String MINUTE = "分";

    /**
     * セクション:
     */
    public static final String SECTION_3GKV00231P003 = "3GKV00231P003";

    /**
     * セクション:
     */
    public static final String SECTION_3GKV00231P004 = "3GKV00231P004";

    /**
     * セクション:
     */
    public static final String SECTION_3GKV00241P004 = "3GKV00241P004";

    /**
     * セクション:
     */
    public static final String SECTION_3GKV00241P002 = "3GKV00241P002";

    /**
     * 元号全角１文字
     */
    public static final String ERA_FULLWIDTH_1 = "g";

    /**
     * 専門員番号
     */
    public static final String SENMONIN_BANGO = "専門員番号";

    /**
     * 特定施設サービス利用票（兼 特定施設サービス計画）
     */
    public static final String TITLE_TOKUTEI_SV_RIYOHYO = "特定施設サービス利用票（兼 特定施設サービス計画）";
    /**
     * 特定施設サービス利用票（兼 特定施設サービス計画）
     */
    public static final String TITLE_EVALUATION_REPORT = "評　価　表";

    /**
     * 特定施設サービス提供票（兼 特定施設サービス計画）
     */
    public static final String TITLE_TOKUTEI_SV_TEIKYO = "特定施設サービス提供票（兼 特定施設サービス計画）";

    /**
     * サービス利用票（兼 予防サービス計画）
     */
    public static final String TITLE_SV_RIYOHYO1 = "サービス利用票（兼 予防サービス計画）";

    /**
     * サービス利用票（兼 居宅サービス計画）
     */
    public static final String TITLE_SV_RIYOHYO2 = "サービス利用票（兼 居宅サービス計画）";

    /**
     * 地域包括支援センター番号 :
     */
    public static final String CHIIKI_SHIEN_CENTER_BANGO = "地域包括支援センター番号 : ";

    /**
     * 特定施設 → 利用者
     */
    public static final String TOKUTEI_SHISETSU_RIYOUSHA = "特定施設 → 利用者";

    /**
     * 小規模多機能型 → 利用者
     */
    public static final String SHOKIBO_RIYOUSHA = "小規模多機能型 → 利用者";

    /**
     * 予防小規模多機能型 → 利用者
     */
    public static final String YOBOU_SHOKIBO_RIYOUSHA = "予防小規模多機能型 → 利用者";

    /**
     * 複合型サービス → 利用者
     */
    public static final String FUKUGOU_RIYOUSHA = "複合型サービス → 利用者";

    /**
     * 地域包括支援ｾﾝﾀｰ → 利用者
     */
    public static final String CHIIKI_RIYOUSHA = "地域包括支援ｾﾝﾀｰ → 利用者";

    /**
     * 居宅介護支援事業者 → 利用者
     */
    public static final String SHIENJIGYO_RIYOUSHA = "居宅介護支援事業者 → 利用者";

    /**
     * 特定施設→受託居宅サービス事業者
     */
    public static final String TOKUTEISHISETSU_SVJIGYOSHA = "特定施設→受託居宅サービス事業者";

    /**
     * 規模多機能型 → サービス事業者
     */
    public static final String KIBO_SVJIGYOSHA = "規模多機能型 → サービス事業者";

    /**
     * 予防小規模多機能型 → サービス事業者
     */
    public static final String YOBO_SVJIGYOSHA = "予防小規模多機能型 → サービス事業者";

    /**
     * 複合型サービス → サービス事業者
     */
    public static final String FUKUGOU_SVJIGYOSHA = "複合型サービス → サービス事業者";

    /**
     * 地域包括支援ｾﾝﾀｰ → サービス事業者
     */
    public static final String CHIIKI_SVJIGYOSHA = "地域包括支援ｾﾝﾀｰ → サービス事業者";

    /**
     * 居宅介護支援事業者 → サービス事業者
     */
    public static final String SHIENJIGYOUSHA_SVJIGYOSHA = "居宅介護支援事業者 → サービス事業者";

    /**
     * 特定施設サービス計画作成
     */
    public static final String SPECIFICFACILITYLABEL1 = "特定施設サービス計画作成";

    /**
     * 特定施設サービス計画作成
     */
    public static final String SPECIFICFACILITYLABEL2 = "小規模多機能型事業者事業所名";

    /**
     * 小規模多機能型事業者\r\n事業所名
     */
    public static final String SPECIFICFACILITYLABEL3 = "小規模多機能型事業者\r\n事業所名";

    /**
     * 複合型サービス\r\n事業所名
     */
    public static final String SPECIFICFACILITYLABEL4 = "複合型サービス\r\n事業所名";

    /**
     * 介護予防支援\r\n事業者事業所名
     */
    public static final String SPECIFICFACILITYLABEL5 = "介護予防支援\r\n事業者事業所名";

    /**
     * 介護予防\r\n支援事業者\r\n事業所名
     */
    public static final String SPECIFICFACILITYLABEL6 = "介護予防\r\n支援事業者\r\n事業所名";

    /**
     * 居宅介護支援\r\n事業者事業所名
     */
    public static final String SPECIFICFACILITYLABEL7 = "居宅介護支援\r\n事業者事業所名";

    /**
     * 居宅介護\r\n支援事業者\r\n事業所名
     */
    public static final String SPECIFICFACILITYLABEL8 = "居宅介護\r\n支援事業者\r\n事業所名";

    /**
     * jrxmファイルパス（U01502_主治医意見書②_R3）
     */
    public static final String U01502_ATTENDING_PHYSICIAN2_R3 = "U01502_AttendingPhysician2_R3.jrxml";

    /**
     * 認定済・申請中
     */
    public static final String SHINSEI_TITLE = "認定済・申請中";

    /**
     * 「（１／３）」
     */
    public static final String A43_TITLE = "（１／３）";

    /**
     * 「（２／３）」
     */
    public static final String A43_TITLE_2 = "（２／３）";

    /**
     * 「（３／３）」
     */
    public static final String A43_TITLE_3 = "（３／３）";

    /**
     * 「確認」
     */
    public static final String KAKUNIN_IN_KNJ_1 = "確認";

    /**
     * 「確認印」
     */
    public static final String KAKUNIN_IN_KNJ_2 = "確認印";

    /**
     * フィールド: date
     */
    public static final String FIELD_DATE = "dateStr";

    /**
     * フィールド: dayOfWeek
     */
    public static final String FIELD_DAYOFWEEK = "dayOfWeek";

    /**
     * フィールド: backgroundColor
     */
    public static final String FIELD_BACKGROUNDCOLOR = "backgroundColor";

    /**
     * フィールド: displayFlg
     */
    public static final String FIELD_DISPLAYFLG = "displayFlg";

    /**
     * フィールド: day
     */
    public static final String FIELD_DAY = "day";

    /**
     * フィールド: daj
     */
    public static final String FIELD_DAJ = "daj";

    /**
     * フィールド: bgColor
     */
    public static final String FIELD_BGCOLOR = "bgColor";

    /**
     * フィールド: bgColor
     */
    public static final String FIELD_BKCOLOR = "bkColor";

    /**
     * フィールド: set
     */
    public static final String FIELD_SET = "set";

    /**
     * String: ◎
     */
    public static final String STR_EVALUATION_CIRCLE = "◎";

    /**
     * String: ○
     */
    public static final String STR_EVALUATION_NAUGHT = "○";

    /**
     * String: △
     */
    public static final String STR_EVALUATION_TRIANGLE = "△";

    /**
     * String: ×1
     */
    public static final String STR_EVALUATION_TIMESONE = "×1";

    /**
     * String: ×2
     */
    public static final String STR_EVALUATION_TIMESTWO = "×2";

    /**
     * jrxmファイルパス（V00241_提供票宛名シール(2×6)）
     */
    public static final String JRXML_TEIKYO_12 = "V00241_TeikyoAtesaki12Report.jrxml";

    /**
     * jrxmファイルパス（V00241_提供票宛名シール(3×6)）
     */
    public static final String JRXML_TEIKYO_18 = "V00241_TeikyoAtesaki18Report.jrxml";

    /**
     * 改定フラグ・H21
     */
    public static final String KAITEI_H21_FLG4 = "4";
    /**
     * 改定フラグ・R3
     */
    public static final String KAITEI_R3_FLG5 = "5";

    /**
     * jrxmファイルパス（U06050_TYPE1_ADL評価健康診断検査設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_ADL = "U06050_Assessment_FaceSheet_TYPE1_adl.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_負担限度設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_FTN = "U06050_Assessment_FaceSheet_TYPE1_ftn.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_介護保険設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_HOK1 = "U06050_Assessment_FaceSheet_TYPE1_hok1.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_主保健設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_HOK2 = "U06050_Assessment_FaceSheet_TYPE1_hok2.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_老人保健設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_HOK3 = "U06050_Assessment_FaceSheet_TYPE1_hok3.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_公費設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_HOK4 = "U06050_Assessment_FaceSheet_TYPE1_hok4.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_JTZ人体図設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_JTZ = "U06050_Assessment_FaceSheet_TYPE1_jtz.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_住環境設定1）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_JUK1 = "U06050_Assessment_FaceSheet_TYPE1_juk1.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_住環境設定2）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_JUK2 = "U06050_Assessment_FaceSheet_TYPE1_juk2.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_親族関係設定1）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_KAZ1 = "U06050_Assessment_FaceSheet_TYPE1_kaz1.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_親族関係設定2）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_KAZ2 = "U06050_Assessment_FaceSheet_TYPE1_kaz2.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_親族関係設定3）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_KAZ3 = "U06050_Assessment_FaceSheet_TYPE1_kaz3.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_親族関係設定4）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_KAZ4 = "U06050_Assessment_FaceSheet_TYPE1_kaz4.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_基本状況設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_KHJ = "U06050_Assessment_FaceSheet_TYPE1_khj.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_基本情報設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_KHN = "U06050_Assessment_FaceSheet_TYPE1_khn.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_既往歴設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_KIO = "U06050_Assessment_FaceSheet_TYPE1_kio.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_健康診断設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_KKS = "U06050_Assessment_FaceSheet_TYPE1_kks.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_検査表示設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_KNS = "U06050_Assessment_FaceSheet_TYPE1_kns.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_口座設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_KOZ = "U06050_Assessment_FaceSheet_TYPE1_koz.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_自立度設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_RAN = "U06050_Assessment_FaceSheet_TYPE1_ran.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_私物設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_SBT = "U06050_Assessment_FaceSheet_TYPE1_sbt.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_生活歴設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_SKT = "U06050_Assessment_FaceSheet_TYPE1_skt.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_サービス設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_SRV = "U06050_Assessment_FaceSheet_TYPE1_srv.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_趣味嗜好設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_SYM = "U06050_Assessment_FaceSheet_TYPE1_sym.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_その他手帳設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_TEC_ET = "U06050_Assessment_FaceSheet_TYPE1_tec_et.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_原爆手帳設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_TEC_GE = "U06050_Assessment_FaceSheet_TYPE1_tec_ge.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_年金手帳設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_TEC_NE = "U06050_Assessment_FaceSheet_TYPE1_tec_ne.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_療育手帳設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_TEC_RY = "U06050_Assessment_FaceSheet_TYPE1_tec_ry.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_精神手帳設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_TEC_SE = "U06050_Assessment_FaceSheet_TYPE1_tec_se.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE1_身障手帳設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE1_TEC_SI = "U06050_Assessment_FaceSheet_TYPE1_tec_si.jrxml";

    /**
     * jrxmファイルパス（U06050_TYPE2_ADL評価健康診断検査設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_ADL = "U06050_Assessment_FaceSheet_TYPE2_adl.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_負担限度設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_FTN = "U06050_Assessment_FaceSheet_TYPE2_ftn.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_介護保険設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_HOK1 = "U06050_Assessment_FaceSheet_TYPE2_hok1.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_主保健設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_HOK2 = "U06050_Assessment_FaceSheet_TYPE2_hok2.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_老人保健設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_HOK3 = "U06050_Assessment_FaceSheet_TYPE2_hok3.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_公費設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_HOK4 = "U06050_Assessment_FaceSheet_TYPE2_hok4.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_JTZ人体図設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_JTZ = "U06050_Assessment_FaceSheet_TYPE2_jtz.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_住環境設定1）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_JUK1 = "U06050_Assessment_FaceSheet_TYPE2_juk1.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_住環境設定2）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_JUK2 = "U06050_Assessment_FaceSheet_TYPE2_juk2.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_親族関係設定1）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_KAZ1 = "U06050_Assessment_FaceSheet_TYPE2_kaz1.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_親族関係設定2）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_KAZ2 = "U06050_Assessment_FaceSheet_TYPE2_kaz2.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_親族関係設定3）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_KAZ3 = "U06050_Assessment_FaceSheet_TYPE2_kaz3.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_親族関係設定4）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_KAZ4 = "U06050_Assessment_FaceSheet_TYPE2_kaz4.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_基本状況設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_KHJ = "U06050_Assessment_FaceSheet_TYPE2_khj.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_基本情報設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_KHN = "U06050_Assessment_FaceSheet_TYPE2_khn.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_既往歴設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_KIO = "U06050_Assessment_FaceSheet_TYPE2_kio.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_健康診断設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_KKS = "U06050_Assessment_FaceSheet_TYPE2_kks.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_検査表示設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_KNS = "U06050_Assessment_FaceSheet_TYPE2_kns.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_口座設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_KOZ = "U06050_Assessment_FaceSheet_TYPE2_koz.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_自立度設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_RAN = "U06050_Assessment_FaceSheet_TYPE2_ran.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_私物設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_SBT = "U06050_Assessment_FaceSheet_TYPE2_sbt.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_生活歴設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_SKT = "U06050_Assessment_FaceSheet_TYPE2_skt.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_サービス設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_SRV = "U06050_Assessment_FaceSheet_TYPE2_srv.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_趣味嗜好設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_SYM = "U06050_Assessment_FaceSheet_TYPE2_sym.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_その他手帳設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_TEC_ET = "U06050_Assessment_FaceSheet_TYPE2_tec_et.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_原爆手帳設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_TEC_GE = "U06050_Assessment_FaceSheet_TYPE2_tec_ge.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_年金手帳設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_TEC_NE = "U06050_Assessment_FaceSheet_TYPE2_tec_ne.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_療育手帳設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_TEC_RY = "U06050_Assessment_FaceSheet_TYPE2_tec_ry.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_精神手帳設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_TEC_SE = "U06050_Assessment_FaceSheet_TYPE2_tec_se.jrxml";
    /**
     * jrxmファイルパス（U06050_TYPE2_身障手帳設定）
     */
    public static final String U06050_ASSESSMENT_FACESHEET_TYPE2_TEC_SI = "U06050_Assessment_FaceSheet_TYPE2_tec_si.jrxml";

    /**
     * U06050_出力対象接頭辞定数
     */
    public static final String OUTPUT_PREFIX = "d_kgh_krd_face";

    /**
     * U06050_出力対象接尾辞定数
     */
    public static final String OUTPUT_SUFFIX = "rep";

    /**
     * U06050_出力対象共通キー
     */
    public static final String JRDS_TITLE_KEY = "titleFlg";

    /**
     * U06050_出力対象口座情報リルトキー
     */
    public static final String JRDS_KOZLIST_KEY = "kozList";

    /**
     * U06050_出力対象住環境情報リルトキー
     */
    public static final String JRDS_JUKLIST_KEY = "jukList";

    /**
     * U06050_出力対象既往歴情報リルトキー
     */
    public static final String JRDS_KIOLIST_KEY = "kioList";

    /**
     * U06050_出力対象年金手帳情報リルトキー
     */
    public static final String JRDS_TECNELIST_KEY = "tecneList";

    /**
     * U06050_出力対象サービス情報リルトキー
     */
    public static final String JRDS_SRVLIST_KEY = "srvList";

    /**
     * U06050_出力対象基本状況情報リルトキー
     */
    public static final String JRDS_KHJLIST_KEY = "khjList";

    /**
     * U06050_出力対象私物情報リルトキー
     */
    public static final String JRDS_SBTLIST_KEY = "sbtList";

    /**
     * U06050_出力対象生活歴情報リルトキー
     */
    public static final String JRDS_SKTLIST_KEY = "sktList";

    /**
     * U06050_出力対象趣味嗜好情報リルトキー
     */
    public static final String JRDS_SYMLIST_KEY = "symList";

    /**
     * U06050_出力対象主保健情報リルトキー
     */
    public static final String JRDS_HOK2LIST_KEY = "hok2List";

    /**
     * U06050_出力対象ADL評価情報リルトキー
     */
    public static final String JRDS_ADLLIST_KEY = "adlList";

    /**
     * U06050_出力対象健康診断情報リルトキー
     */
    public static final String JRDS_KKSLIST_KEY = "kksList";

    /**
     * U06050_出力対象検査情報リルトキー
     */
    public static final String JRDS_KNSLIST_KEY = "knsList";

    /**
     * U06050_出力対象その他手帳情報リルトキー
     */
    public static final String JRDS_TECETLIST_KEY = "tecetList";

    /** U06050_アセスメントフェースシート 固定値："親族･関係者" */
    public static final String KAZOKU_STRING_1 = "親族･関係者";

    /** U06050_アセスメントフェースシート 固定値："親族関係" */
    public static final String KAZOKU_STRING_2 = "親族関係";

    /** U06050_アセスメントフェースシート 固定値："家族" */
    public static final String KAZOKU_STRING_3 = "家族";

    /** U06050_アセスメントフェースシート 固定値："医療保険" */
    public static final String HOSPITAL_HOKEN_STRING = "医療保険";

    /** U06050_アセスメントフェースシート 固定値："主保険" */
    public static final String MAIN_HOKEN_STRING = "主保険";

    /** U06050_アセスメントフェースシート 固定値："口座" */
    public static final String KOUZA_STRING = "口座";

    /** U06050_アセスメントフェースシート 固定値："負担限度額" */
    public static final String BURDEN_LIMIT = "負担限度額";

    /** U06050_アセスメントフェースシート 固定値："[mnu3][3GK]利用者管理口座" */
    public static final String KINOU_NAME_1 = "[mnu3][3GK]利用者管理口座";

    /** U06050_アセスメントフェースシート 固定値："[mnu3][3GK]口座" */
    public static final String KINOU_NAME_2 = "[mnu3][3GK]口座";

    /** U06050_アセスメントフェースシート 固定値："[mnu3][3GK]預り金口座" */
    public static final String KINOU_NAME_3 = "[mnu3][3GK]預り金口座";

    /** U06050_アセスメントフェースシート 固定値："[mnu3][3GK]負担限度額" */
    public static final String KINOU_NAME_4 = "[mnu3][3GK]負担限度額";

    /** U06050_アセスメントフェースシート 固定値："[mnu3][3GK]負担限度額2" */
    public static final String KINOU_NAME_5 = "[mnu3][3GK]負担限度額2";

    /** U06050_アセスメントフェースシート 固定値："[mnu3][3GK]" */
    public static final String KINOU_NAME_LABEL = "[mnu3][3GK]";

    /** U06050_アセスメントフェースシート 固定値："生活" */
    public static final String LIFE_LABEL = "生活";

    /** U06050_アセスメントフェースシート 固定値："生活歴" */
    public static final String LIFE_HISTORY_LABEL = "生活歴";

    /** U06050_アセスメントフェースシート 固定値："健康診断リスト" */
    public static final String HEALTH_DIAGNOSIS_LIST_LABEL = "健康診断リスト";

    /** U06050_アセスメントフェースシート 固定値："検査リスト" */
    public static final String INSPECTION_LIST_LABEL = "検査リスト";

    /** U06050_アセスメントフェースシート 固定値："(RH+)" */
    public static final String RHKNJ_1 = "(RH+)";

    /** U06050_アセスメントフェースシート 固定値："(RH-)" */
    public static final String RHKNJ_2 = "(RH-)";

    /** U06050_アセスメントフェースシート 固定値："問題なし" */
    public static final String LEVELKNJ_1 = "問題なし";

    /** U06050_アセスメントフェースシート 固定値："やや問題" */
    public static final String LEVELKNJ_2 = "やや問題";

    /** U06050_アセスメントフェースシート 固定値："問題あり" */
    public static final String LEVELKNJ_3 = "問題あり";

    /** U06050_アセスメントフェースシート 固定値："非常に問題" */
    public static final String LEVELKNJ_4 = "非常に問題";

    /** U06050_アセスメントフェースシート 固定値："要対応" */
    public static final String LEVELKNJ_5 = "要対応";

    /** U06050_アセスメントフェースシート 固定値："給付率" */
    public static final String PAYOUT_RATE = "給付率";

    /** U06050_アセスメントフェースシート 固定値："給付種類" */
    public static final String PAYOUT_TYPE = "給付種類";

    /** U06050_アセスメントフェースシート 固定値："認定済み" */
    public static final String APPROVED_STRING = "認定済み";

    /** U06050_アセスメントフェースシート 固定値："申請中" */
    public static final String UNDER_REVIEW_STRING = "申請中";

    /** U06050_アセスメントフェースシート 固定値："継続" */
    public static final String CONTINUATION_STRING = "継続";

    /** U06050_アセスメントフェースシート 固定値："職務上" */
    public static final String LINE_OF_DUTY_STRING = "職務上";

    /** U06050_アセスメントフェースシート 固定値："下船３ヶ月以内" */
    public static final String WITHIN_THREE_MONTH_AFTER_DISEMBARKATION_STRING = "下船３ヶ月以内";

    /** U06050_アセスメントフェースシート 固定値："通勤災害" */
    public static final String COMMUTING_INJURY_STRING = "通勤災害";

    /** U06050_アセスメントフェースシート 固定値："（単独）" */
    public static final String HOKENKNJ_1 = "（単独）";

    /** U06050_アセスメントフェースシート 固定値："（併用）" */
    public static final String HOKENKNJ_2 = "（併用）";

    /** U06050_アセスメントフェースシート 固定値："通院" */
    public static final String NYUTUKBNKNJ_1 = "通院";

    /** U06050_アセスメントフェースシート 固定値："入院" */
    public static final String NYUTUKBNKNJ_2 = "入院";

    /** U06050_アセスメントフェースシート 固定値："往診" */
    public static final String NYUTUKBNKNJ_3 = "往診";

    /** U06050_アセスメントフェースシート 固定値："なし" */
    public static final String NYUTUKBNKNJ_4 = "なし";

    /** U06050_アセスメントフェースシート 固定値："(主)" */
    public static final String MAINKBNKNJ_1 = "(主)";

    /** U06050_アセスメントフェースシート 固定値："(副)" */
    public static final String MAINKBNKNJ_2 = "(副)";

    /** U06050_アセスメントフェースシート 固定値："継続" */
    public static final String ENDKBNKNJ_1 = "継続";

    /** U06050_アセスメントフェースシート 固定値："退院" */
    public static final String ENDKBNKNJ_2 = "退院";

    /** U06050_アセスメントフェースシート 固定値："完治" */
    public static final String ENDKBNKNJ_3 = "完治";

    /** U06050_アセスメントフェースシート 固定値："終了" */
    public static final String ENDKBNKNJ_4 = "終了";

    /** U06050_アセスメントフェースシート 固定値："死亡" */
    public static final String ENDKBNKNJ_5 = "死亡";

    /** U06050_アセスメントフェースシート 固定値："(凡例)" */
    public static final String HANREI_STRING = "(凡例)";

    /** U06050_アセスメントフェースシート 固定値："本人" */
    public static final String HONNIN_STRING = "本人";

    /** U06050_アセスメントフェースシート 固定値："月支給" */
    public static final String MONTHLY_SALARY_STRING = "月支給";

    /** U06050_アセスメントフェースシート 固定値："評価  ：" */
    public static final String KEISEKI_LABEL_STRING = "評価  ：";

    /** U06050_アセスメントフェースシート 固定値："使用用具：" */
    public static final String YONGUKNJ_LABEL_STRING = "使用用具：";

    /** U06050_アセスメントフェースシート 固定値："介護内容：" */
    public static final String KAIGONAIYO_LABEL_STRING = "介護内容：";

    /** U06050_アセスメントフェースシート 固定値："【目標】" */
    public static final String TARGET_LABEL_STRING = "【目標】";

    /** U06050_アセスメントフェースシート 固定値："【実行】" */
    public static final String JIKKOU_LABEL_STRING = "【実行】";

    /** U06050_アセスメントフェースシート 固定値："【能力】" */
    public static final String NORYO_LABEL_STRING = "【能力】";

    /**
     * 計画実施表(番号あり)
     */
    public static final String U01600_IMPLEMENTATION_MONITORING_NOARI_REPORT = "U01600_implementationMonitoringNoAriReport.jrxml";
    /**
     * 計画実施表(番号なし)
     */
    public static final String U01601_IMPLEMENTATION_MONITORING_NONASHI_REPORT = "U01601_implementationMonitoringNoNashiReport.jrxml";
    /**
     * 計画実施表(番号あり・担当者あり)
     */
    public static final String U01602_IMPLEMENTATION_MONITORING_NOARI_MANAGERARI_REPORT = "U01602_implementationMonitoringNoAriManagerAriReport.jrxml";
    /**
     * 計画実施表(番号なし・担当者あり)
     */
    public static final String U01603_IMPLEMENTATION_MONITORING_NONASHI_MANAGERARI_REPORT = "U01603_implementationMonitoringNoNashiManagerAriReport.jrxml";
    /**
     * 計画実施表(番号あり・頻度あり)
     */
    public static final String U01604_IMPLEMENTATION_MONITORING_NOARI_FREQUENCYARI_REPORT = "U01604_implementationMonitoringNoAriFrequencyAriReport.jrxml";
    /**
     * 計画実施表(番号なし・頻度あり)
     */
    public static final String U01605_IMPLEMENTATION_MONITORING_NONASHI_FREQUENCYARI_REPORT = "U01605_implementationMonitoringNoNashiFrequencyAriReport.jrxml";
    /**
     * 計画実施表(番号あり・担当者あり・頻度あり)
     */
    public static final String U01606_IMPLEMENTATION_MONITORING_NOARI_MANAGERARI_FREQUENCYARI_REPORT = "U01606_implementationMonitoringNoAriManagerAriFrequencyAriReport.jrxml";
    /**
     * 計画実施表(番号なし・担当者あり・頻度あり)
     */
    public static final String U01607_IMPLEMENTATION_MONITORING_NONASHI_MANAGERARI_FREQUENCYARI_REPORT = "U01607_implementationMonitoringNoNashiManagerAriFrequencyAriReport.jrxml";

    /**
     * jrxmファイルパス（U01502_主治医意見書(一括)_R3）
     */
    public static final String U01502_ATTENDING_PHYSICIAN_ALL_R3 = "U01502_AttendingPhysician_ALL_R3.jrxml";

    /**
     * セクション: 3GKV00241P006
     */
    public static final String SECTION_3GKV00231P006 = "3GKV00241P006";

    /**
     * 承認欄: subReportPath
     */
    public static final String SUBREPORTPATH = "subReportPath";

    /**
     * 承認欄: SUBREPORTDATADS
     */
    public static final String SUBREPORTDATADS = "subReportDataDs";

    /**
     * jrxmファイルパス（U0P152_（Ⅱ）実施計画～③(H21改訂版) ）
     */
    public static final String JRXML_U0P152_IMPLEMENTATIONPLAN3_H21 = "U0P152_ImplementationPlan3_H21.jrxml";

    /**
     * V00231_サービス利用 カレンダー表.jrxml
     */
    public static final String V00231_JRXML_SERVICEUSAGECALENDAR = "V00231_ServiceUsageCalendarReport.jrxml";

    /**
     * V00231_サービス利用 カレンダー表(A3).jrxml
     */
    public static final String V00231_JRXML_SERVICEUSAGECALENDAR_A3 = "V00231_ServiceUsageCalendarA3Report.jrxml";

    /**
     * アセスメント領域ID
     * 一件目 = "運動・移動について"
     * 二件目 = "日常生活（家庭生活）について"
     * 三件目 = "社会参加、対人関係・「改行」コミュニケーションについて"
     * 四件目 = "健康管理について"
     */
    public static final List<String> ASSRYO_ID_LIST = Arrays.asList("運動・移動について", "日常生活（家庭生活）について",
            "社会参加、対人関係・\r\nコミュニケーションについて", "健康管理について");

    /**
     * アセスメント領域ID
     * 一件目 = "運動・移動について"
     * 二件目 = "日常生活（家庭生活）について"
     * 三件目 = "社会参加、対人関係・コミュニケーションについて"
     * 四件目 = "健康管理について"
     */
    public static final List<String> ASSRYO_ID_LIST_E00402 = Arrays.asList("運動・移動について", "日常生活（家庭生活）について",
            "社会参加、対人関係・コミュニケーションについて", "健康管理について");
    /**
     * アセスメント領域ID
     * 一件目 = "運動・移動について"
     * 二件目 = "日常生活（家庭生活）について"
     * 三件目 = "社会参加､対人関係･ｺﾐｭﾆｹｰｼｮﾝについて"
     * 四件目 = "健康管理について"
     * 五件目 = "その他の事項について"
     */
    public static final List<String> ASSRYO_ID_LIST_A43 = Arrays.asList("運動・移動について", "日常生活（家庭生活）について",
            "社会参加､対人関係･ｺﾐｭﾆｹｰｼｮﾝについて", "健康管理について", "その他の事項について");

    /**
     * 領域における課題（背景・原因）フラグ "□有 □無"
     */
    public static final String KADAI_KN_FLAG_1 = "□有    □無";

    /**
     * 領域における課題（背景・原因）フラグ "■有 □無"
     */
    public static final String KADAI_KN_FLAG_2 = "■有    □無";

    /**
     * 領域における課題（背景・原因）フラグ "□有 ■無"
     */
    public static final String KADAI_KN_FLAG_3 = "□有    ■無";

    /**
     * 本人の取組
     */
    public static final String SV_TITLE = "本人の取組";

    /**
     * 家族・地域の[改行]支援、民間[改行]ｻｰﾋﾞｽ等
     */
    public static final String SV_TITLE_KAZOKU = "家族・地域の\r\n支援、民間\r\nｻｰﾋﾞｽ等";

    /**
     * 介護保険ｻｰﾋﾞｽ[改行]地域支援事業[改行]区市町村ｻｰﾋﾞ
     */
    public static final String SV_TITLE_HOKEN = "介護保険ｻｰﾋﾞｽ\r\n地域支援事業\r\n区市町村ｻｰﾋﾞ";

    /**
     * jrxmファイルパス（U06051_チェック項目利用者一覧表 ）
     */
    public static final String CHECK_ITEM_USER_LIST_REPORT = "U06051_Check_Item_User_List.jrxml";

    /**
     * U06051_チェック項目利用者一覧表 LINE_WIDTHなし
     */
    public static final String JASPER_LINE_WIDTH_0 = "0.0";

    /**
     * U06051_チェック項目利用者一覧表 LINE_WIDTHあり
     */
    public static final String JASPER_LINE_WIDTH = "0.25";

    /**
     * U06051_チェック項目利用者一覧表 固定値："評価項目"
     */
    public static final String KOUMOKU_TITLE = "評価項目";

    /**
     * U06051_チェック項目利用者一覧表 固定値："評価細目"
     */
    public static final String SAIMOKU_TITLE = "細目名称";

    /**
     * U06051_チェック項目利用者一覧表 利用者情報設定処理（最大１０件）
     */
    public static final Integer CHECK_ITEM_USER_LIST_MAX_COLUMN = 10;

    /**
     * jrxmファイルパス（E00400_介護予防サービス-支援計画表（A3横1枚）_開始位置調整あり）
     */
    public static final String JRXML_E00400_PREVENTION_PLAN_A31_REPORT_START = "E00400_PreventionPlanA31Report_StartPositionAdjustment.jrxml";

    /**
     * jrxmファイルパス（E00400_介護予防サービス-支援計画表（A3横1枚）_開始位置調整なし）
     */
    public static final String JRXML_E00400_PREVENTION_PLAN_A31_REPORT_START_NONE = "E00400_PreventionPlanA31Report_StartPositionAdjustmentNone.jrxml";

    /**
     * jrxmファイルパス（E00400_介護予防サービス-支援計画表（A3横1枚）_開始位置調整なし_印鑑欄なし）
     */
    public static final String JRXML_E00400_PREVENTION_PLAN_A31_REPORT_START_INKAN_NONE = "E00400_PreventionPlanA31Report_StartPositionAdjustmentNone_InkanNone.jrxml";

    /**
     * jrxmファイルパス（E00400_介護予防サービス-支援計画表（A3横1枚）_開始位置調整あり_印鑑欄なし）
     */
    public static final String JRXML_E00400_PREVENTION_PLAN_A31_REPORT_START_INKAN = "E00400_PreventionPlanA31Report_StartPositionAdjustment_Inkan.jrxml";

    /**
     * jrxmファイルパス（E00400_介護予防サービス-支援計画表（A3横1枚）_Merge）
     */
    public static final String JRXML_E00400_PREVENTION_PLAN_MERGE_A4_LANDSCAPE = "E00400_PreventionPlanA31Report_Merge.jrxml";

    /**
     * jrxmファイルパス（E00400_介護予防サービス-支援計画表（A3横1枚）_Empty）
     */
    public static final String JRXML_E00400_PREVENTION_PLAN_EMPTY = "E00400_PreventionPlanA31Report_Empty.jrxml";

    /**
     * jrxmファイルパス（E00402_介護予防サービス-支援計画表（A4横2枚）_1）
     */
    public static final String JRXML_E00402_PREVENTION_PLAN_A42_1 = "E00402_PreventionPlanA42Report_1.jrxml";

    /**
     * jrxmファイルパス（E00402_介護予防サービス-支援計画表（A4横2枚）_2）
     */
    public static final String JRXML_E00402_PREVENTION_PLAN_A42_2 = "E00402_PreventionPlanA42Report_2.jrxml";

    /**
     * jrxmファイルパス（E00402_介護予防サービス-支援計画表（A4横2枚）_merge）
     */
    public static final String JRXML_E00402_PREVENTION_PLAN_A42_MERGE = "E00402_PreventionPlanA42Report_Merge.jrxml";

    /**
     * jrxmファイルパス（E00402_介護予防サービス-支援計画表（A4横2枚）_all）
     */
    public static final String JRXML_E00402_PREVENTION_PLAN_A42_ALL = "E00402_PreventionPlanA42Report_ALL.jrxml";

    /**
     * jrxmファイルパス（E00401_介護予防サービス-支援計画表（A4横3枚）_1）
     */
    public static final String JRXML_E00401_PREVENTION_PLAN_A43_1 = "E00401_PreventionPlanA43Report_1.jrxml";

    /**
     * jrxmファイルパス（E00401_介護予防サービス-支援計画表（A4横3枚）_2）
     */
    public static final String JRXML_E00401_PREVENTION_PLAN_A43_2 = "E00401_PreventionPlanA43Report_2.jrxml";

    /**
     * jrxmファイルパス（E00401_介護予防サービス-支援計画表（A4横3枚）_3_開始位置調整あり）
     */
    public static final String JRXML_E00401_PREVENTION_PLAN_A43_3_START_INKAN = "E00401_PreventionPlanA43Report_3_StartPositionAdjustment_Inkan.jrxml";

    /**
     * jrxmファイルパス（E00401_介護予防サービス-支援計画表（A4横3枚）_3_開始位置調整なし）
     */
    public static final String JRXML_E00401_PREVENTION_PLAN_A43_3_START_NONE = "E00401_PreventionPlanA43Report_3_StartPositionAdjustmentNone.jrxml";

    /**
     * jrxmファイルパス（E00401_介護予防サービス-支援計画表（A4横3枚）_1_empty）
     */
    public static final String JRXML_E00401_PREVENTION_PLAN_A43_1_EMPTY = "E00401_PreventionPlanA43Report_1_Empty.jrxml";

    /**
     * jrxmファイルパス（E00401_介護予防サービス-支援計画表（A4横3枚）_2_empty）
     */
    public static final String JRXML_E00401_PREVENTION_PLAN_A43_2_EMPTY = "E00401_PreventionPlanA43Report_2_Empty.jrxml";

    /**
     * jrxmファイルパス（E00401_介護予防サービス-支援計画表（A4横3枚）_3_empty）
     */
    public static final String JRXML_E00401_PREVENTION_PLAN_A43_3_EMPTY = "E00401_PreventionPlanA43Report_3_Empty.jrxml";

    /**
     * jrxmファイルパス（E00401_介護予防サービス-支援計画表（A4横3枚）_all）
     */
    public static final String JRXML_E00401_PREVENTION_PLAN_A43_ALL = "E00401_PreventionPlanA43Report_ALL.jrxml";

    /**
     * U01800_課題整理総括表 固定値："課題整理総括表"
     */
    public static final String ORGANIZING_ISSUES = "課題整理総括表";

    /**
     * U01800_課題整理総括表 固定値："自立 見守り 一部介助 全介助"
     */
    public static final String INDEPENDENCE_HELP = "自立 見守り 一部介助 全介助";

    /**
     * U01800_課題整理総括表 固定値："支障なし 支障あり"
     */
    public static final String OBSTACLE = "支障なし 支障あり";

    /**
     * U01020_モニタリング・評価表（H21改訂版） 固定値："(Ⅷ)処遇計画モニタリング・評価表"
     */
    public static final String MONITORING_EVALUATION = "(Ⅷ)処遇計画モニタリング・評価表";

    /**
     * U01020_モニタリング・評価表（H21改訂版） 固定値："(Ⅸ)特定施設サービス計画／介護予防特定施設サービス計画モニタリング・評価表"
     */
    public static final String SERVICE_PLAN_MONITORING_EVALUATION = "(Ⅸ)特定施設サービス計画／介護予防特定施設サービス計画モニタリング・評価表";

    /**
     * U01020_モニタリング・評価表（H21改訂版） 固定値："新たなニーズの発生："
     */
    public static final String STR_NEWS = "新たなニーズの発生：";

    /**
     * U01020_モニタリング・評価表（H21改訂版） 固定値："再アセスメントの必要性："
     */
    public static final String STR_ASSESSMENT = "再アセスメントの必要性：";

    /**
     * jrxmファイルパス（E00600_介護予防支援-サービス評価表）
     */
    public static final String JRXML_E00600_PREVENTIVECARE = "E00600_preventiveCareSupportServiceEvaluationReport.jrxml";

    /**
     * jrxmファイルパス（U01020_モニタリング・評価表（H21改訂版））
     */
    public static final String JRXML_U01020_MONITORINGEVALUATION = "U01020_MonitoringEvaluationReport.jrxml";

    /**
     * セクション:
     */
    public static final String SECTION_E00600 = "E00600";

    /**
     * 総合的課題と目標リストと支援計画リスト
     */
    public static final List<Integer> PREVENTION_PLAN_COLUMN_WIDTH_LIST = Arrays.asList(823, 803, 748, 680, 582, 454,
            434, 349, 262,
            177, 112,
            56);

    /**
     * 総合的課題と目標リストと支援計画リスト_E00402
     */
    public static final List<Integer> PREVENTION_PLAN_E00402_COLUMN_WIDTH_LIST = Arrays.asList(776, 756, 623, 603, 488,
            367,
            246, 148, 75);

    /**
     * E00600_介護予防支援-サービス評価表 固定値："介護予防支援-サービス評価表"
     */
    public static final String STR_PREVENTIVECARE = "介護予防支援-サービス評価表";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："□ プラン継続"
     */
    public static final String STR_OFF_PLAN_CONTINUATION = "□ プラン継続";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："■ プラン継続"
     */
    public static final String STR_ON_PLAN_CONTINUATION = "■ プラン継続";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："□ プラン変更"
     */
    public static final String STR_OFF_PLAN_CHANGE = "□ プラン変更";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："■ プラン変更"
     */
    public static final String STR_ON_PLAN_CHANGE = "■ プラン変更";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："□ 終了"
     */
    public static final String STR_OFF_END = "□ 終了";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："■ 終了"
     */
    public static final String STR_ON_END = "■ 終了";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："□ 介護給付"
     */
    public static final String STR_OFF_ASSESSMENT = "□ 介護給付";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："■ 介護給付"
     */
    public static final String STR_ON_ASSESSMENT = "■ 介護給付";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："□ 介護予防特定高齢者施策"
     */
    public static final String STR_OFF_ASSESSMENT_YOBO = "□ 介護予防特定高齢者施策";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："■ 介護予防特定高齢者施策"
     */
    public static final String STR_ON_ASSESSMENT_YOBO = "■ 介護予防特定高齢者施策";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："□ 介護予防・生活支援サービス事業"
     */
    public static final String STR_OFF_ASSESSMENT_YOBO_SUPPORT = "□ 介護予防・生活支援サービス事業";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："■ 介護予防・生活支援サービス事業"
     */
    public static final String STR_ON_ASSESSMENT_YOBO_SUPPORT = "■ 介護予防・生活支援サービス事業";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："□ 介護予防一般高齢者施策"
     */
    public static final String STR_OFF_ASSESSMENT_YOBO_GENERAL = "□ 介護予防一般高齢者施策";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："■ 介護予防一般高齢者施策"
     */
    public static final String STR_ON_ASSESSMENT_YOBO_GENERAL = "■ 介護予防一般高齢者施策";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："□ 一般介護予防事業"
     */
    public static final String STR_OFF_GENERAL_ASSESSMENT_YOBO = "□ 一般介護予防事業";

    /**
     * E00600_介護予防支援-サービス評価表 固定値："■ 一般介護予防事業"
     */
    public static final String STR_ON_GENERAL_ASSESSMENT_YOBO = "■ 一般介護予防事業";

    /**
     * jrxmファイルパス（U0P102_（Ⅶ）処遇・支援経過：具体的なサービス提供の内容（兼処遇日誌））
     */
    public static final String JRXML_SUPPORT_ELAPSED_CONCRETE_SERVICE_OFFER_CONTENTS = "U0P102_Syogu_Shienkeika_Content_Service_Provision.jrxml";

    /**
     * jrxmファイルパス（U01020_モニタリング記録表）
     */
    public static final String JRXML_U01020_MONITORINGRECORD = "U01020_MonitoringRecordReport.jrxml";

    /**
     * jrxmファイルパス（U01020_モニタリング記録表一覧）
     */
    public static final String JRXML_U01020_MONITORINGRECORDLIST = "U01020_MonitoringRecordListReport.jrxml";

    /**
     * jrxmファイルパス（U00912_サービス担当者会議の要点）
     */
    public static final String JRXML_SERVICE_MANAGER_MEETING_MAINT_POINT_U00912 = "U00912_Service_Manager_Meeting_Main_Point.jrxml";

    /**
     * jrxmファイルパス（U00912_サービス担当者会議の要点）
     */
    public static final String JRXML_SERVICE_MANAGER_MEETING_MAINT_POINT_U00912_VERTICAL = "U00912_Service_Manager_Meeting_Main_Point_Vertical.jrxml";

    /**
     * 課題分析帳票タイトル（U01700_課題分析）
     */
    public static final String ISSUES_ANALYSIS_TITLE = "課題分析";

    /**
     * 別紙のタイトル（U06091_入院時情報提供書②）
     */
    public static final String HOSPITAL_INFO_OFFER2_TITLE = "入院時情報提供書②";

    /**
     * jrxmファイルパス（V00261_限度額超過利用者・算定エラー利用者一覧）
     */
    public static final String JRXML_V00261_SANTEERR_ITIRAN_RECORD_REPORT = "V00261_limitExceededUsercCalculationErrorListReport.jrxml";
}
