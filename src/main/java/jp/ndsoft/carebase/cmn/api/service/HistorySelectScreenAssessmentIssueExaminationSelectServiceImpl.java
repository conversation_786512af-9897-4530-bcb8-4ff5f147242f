package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui00656RirekiOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.HistorySelectScreenAssessmentIssueExaminationSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.HistorySelectScreenAssessmentIssueExaminationSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucMygKky1InfoFilterMstKbnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucMygKky1InfoFilterMstKbnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinInfoListSpaceSortByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinInfoListSpaceSortOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucMygKky1SelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.05.26
 * <AUTHOR> BUI VIET ANH
 * @implNote GUI00656_［履歴選択］画面 ｱｾｽﾒﾝﾄ課題検討
 */
@Service
public class HistorySelectScreenAssessmentIssueExaminationSelectServiceImpl extends
        SelectServiceImpl<HistorySelectScreenAssessmentIssueExaminationSelectServiceInDto, HistorySelectScreenAssessmentIssueExaminationSelectServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 28-xx ケアプラン策定のための課題検討用紙 ヘッダ取得 DAO */
    @Autowired
    private CpnTucMygKky1SelectMapper cpnTucMygKky1SelectMapper;

    /** 職員氏名情報情報取得 DAO */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /**
     * 履歴選択
     * 
     * @param inDto 履歴選択の入力DTO.
     * @return 履歴選択 OUT DTO
     * @throws Exception Exception
     */
    @Override
    protected HistorySelectScreenAssessmentIssueExaminationSelectServiceOutDto mainProcess(
            HistorySelectScreenAssessmentIssueExaminationSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        // DTOOUT情報
        HistorySelectScreenAssessmentIssueExaminationSelectServiceOutDto outDto = new HistorySelectScreenAssessmentIssueExaminationSelectServiceOutDto();

        /** 履歴情報リスト */
        List<Gui00656RirekiOutDto> rirekiList = new ArrayList<>();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. アセスメント課題検討履歴情報を取得する。===============
         * 
         */
        CpnTucMygKky1InfoFilterMstKbnByCriteriaInEntity cpnTucMygKky1InfoFilterMstKbnByCriteria = new CpnTucMygKky1InfoFilterMstKbnByCriteriaInEntity();
        cpnTucMygKky1InfoFilterMstKbnByCriteria.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        cpnTucMygKky1InfoFilterMstKbnByCriteria.setJId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        cpnTucMygKky1InfoFilterMstKbnByCriteria.setUId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        cpnTucMygKky1InfoFilterMstKbnByCriteria.setMstKbn(CommonDtoUtil.strValToInt(inDto.getMstKbn()));

        List<CpnTucMygKky1InfoFilterMstKbnOutEntity> cpnTucMygKkyInfoList = cpnTucMygKky1SelectMapper
                .findCpnTucMygKky1InfoFilterMstKbnByCriteria(cpnTucMygKky1InfoFilterMstKbnByCriteria);

        /*
         * ===============3. 職員氏名情報の取得===============
         * 
         */
        List<ShokuinInfoListSpaceSortOutEntity> shokuinInfoListSpaceSortList = comMscShokuinSelectMapper
                .findShokuinInfoListSpaceSortByCriteria(new ShokuinInfoListSpaceSortByCriteriaInEntity());

        /*
         * ===============4. 上記の処理で取得したアセスメント課題検討履歴情報、職員基本情報をマッピングし、職員氏名情報の「職員名（姓）+'
         * '+職員名（名）」を履歴情報リストの作成者に設定する。===============
         * 
         */
        for (CpnTucMygKky1InfoFilterMstKbnOutEntity cpnTucMygKkyInfoItem : cpnTucMygKkyInfoList) {
            Gui00656RirekiOutDto rirekiOut = new Gui00656RirekiOutDto();

            /** 履歴ID */
            rirekiOut.setKky1Id(CommonDtoUtil.objValToString(cpnTucMygKkyInfoItem.getKky1Id()));
            /** 計画期間ID */
            rirekiOut.setSc1Id(CommonDtoUtil.objValToString(cpnTucMygKkyInfoItem.getSc1Id()));
            /** 法人ID */
            rirekiOut.setHoujinId(CommonDtoUtil.objValToString(cpnTucMygKkyInfoItem.getHoujinId()));
            /** 施設ID */
            rirekiOut.setShisetuId(CommonDtoUtil.objValToString(cpnTucMygKkyInfoItem.getShisetuId()));
            /** 事業者ID */
            rirekiOut.setSvJigyoId(CommonDtoUtil.objValToString(cpnTucMygKkyInfoItem.getSvJigyoId()));
            /** 利用者ID */
            rirekiOut.setUserid(CommonDtoUtil.objValToString(cpnTucMygKkyInfoItem.getUserid()));
            /** マスタ区分 */
            rirekiOut.setMstKbn(CommonDtoUtil.objValToString(cpnTucMygKkyInfoItem.getMstKbn()));
            /** アセスメント基準日 */
            rirekiOut.setCreateYmd(cpnTucMygKkyInfoItem.getCreateYmd());
            /** 担当者ID(職員ID) */
            rirekiOut.setShokuId(CommonDtoUtil.objValToString(cpnTucMygKkyInfoItem.getShokuId()));
            /** 改訂区分 */
            rirekiOut.setKaiteiKbn(CommonDtoUtil.objValToString(cpnTucMygKkyInfoItem.getKaiteiKbn()));
            /** 作成者 */
            for (ShokuinInfoListSpaceSortOutEntity shokuinItem : shokuinInfoListSpaceSortList) {
                // マッピング条件：職員氏名情報.職員ID = アセスメント課題検討履歴情報.担当者ID
                if (Objects.equals(
                        CommonDtoUtil.objValToString(shokuinItem.getChkShokuId()),
                        CommonDtoUtil.objValToString(cpnTucMygKkyInfoItem.getShokuId()))) {
                    rirekiOut.setCreateName(
                            shokuinItem.getShokuin1Knj() + CommonConstants.BLANK_SPACE + shokuinItem.getShokuin2Knj());
                    break;
                }
            }

            rirekiList.add(rirekiOut);
        }

        /*
         * ===============5. 上記処理で取得した結果レスポンスを返却する。===============
         * 
         */
        outDto.setRirekiList(rirekiList);
        LOG.info(Constants.END);
        return outDto;
    }
}
