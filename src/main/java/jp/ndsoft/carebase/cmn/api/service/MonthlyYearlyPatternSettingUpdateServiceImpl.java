package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jp.ndsoft.carebase.cmn.api.service.dto.MonthlyYearlyPatternSettingMonthDataInfoDto;
import jp.ndsoft.carebase.cmn.api.service.dto.MonthlyYearlyPatternSettingUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.MonthlyYearlyPatternSettingUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.MonthlyYearlyPatternSettingYearDataInfoDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnMucSypNenkanPtn1Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnMucSypNenkanPtn2Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnMucSypNenkanPtn1;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnMucSypNenkanPtn1Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnMucSypNenkanPtn2;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnMucSypNenkanPtn2Criteria;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * GUI00940_月間・年間表パターン設定 情報保存
 * 
 * <AUTHOR> 王利峰
 */
@Service
public class MonthlyYearlyPatternSettingUpdateServiceImpl
        extends
        UpdateServiceImpl<MonthlyYearlyPatternSettingUpdateServiceInDto, MonthlyYearlyPatternSettingUpdateServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private CpnMucSypNenkanPtn1Mapper cpnMucSypNenkanPtn1Mapper;

    @Autowired
    private CpnMucSypNenkanPtn2Mapper cpnMucSypNenkanPtn2Mapper;

    /**
     * パターン（グループ）情報保存
     * 
     * @param inDto パターン（グループ）情報保存の入力DTO.
     * @return パターン（グループ）情報保存のOUT DTO
     * @throws Exception Exception
     */
    @Override
    protected MonthlyYearlyPatternSettingUpdateServiceOutDto mainProcess(
            MonthlyYearlyPatternSettingUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);

        // 1.単項目チェック以外の入力チェック
        // なし

        // 2. 月間・年間表パターンマスタヘッダの更新処理
        updateYearData(inDto.getSogoObj());

        // 3. 詳細リストの更新・登録処理
        updateMonthData(inDto.getDataList());
        LOG.info(Constants.END);
        return new MonthlyYearlyPatternSettingUpdateServiceOutDto();
    }

    /**
     * 年間表の更新
     * 
     * @param year 更新用 年間表データ
     * @throws ExclusiveException
     */
    private void updateYearData(MonthlyYearlyPatternSettingYearDataInfoDto year) throws ExclusiveException {
        // 更新回数
        // BigInteger updateCount = CommonDtoUtil.strValToBigInteger(year.getSogoShitenKnjModifiedCnt());
        // 年間表の更新データ設定
        CpnMucSypNenkanPtn1 data = new CpnMucSypNenkanPtn1();
        data.setSogoShitenKnj(year.getSogoShitenKnj());

        // 年間表の更新条件設定
        CpnMucSypNenkanPtn1Criteria whereItem = new CpnMucSypNenkanPtn1Criteria();
        whereItem.createCriteria().andNenkan1IdEqualTo(CommonDtoUtil.strValToInt(year.getNenkan1Id()));
                // .andModifiedCntEqualTo(updateCount)
                // .andDelFlgEqualTo(
                //         Constants.DELL_FLG_OFF);

        // 更新時の共通カラム値設定処理
        // CommonDaoUtil.setUpdateCommonColumns(data, updateCount);
        // 年間表パターンマスタヘッダの更新
        int count = cpnMucSypNenkanPtn1Mapper.updateByCriteriaSelective(data, whereItem);

        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 
     * 月間表の更新
     * 
     * @param monthList 更新用 月間行事データ
     * @throws ExclusiveException
     */
    private void updateMonthData(List<MonthlyYearlyPatternSettingMonthDataInfoDto> monthList)
            throws ExclusiveException {
        for (MonthlyYearlyPatternSettingMonthDataInfoDto month : monthList) {
            // 「月間行事」リスト.更新区分がU:更新の場合
            if (CommonDtoUtil.isUpdate(month)) {
                // 更新回数
                // BigInteger updateCount = CommonDtoUtil.strValToBigInteger(month.getModifiedCnt());
                // 詳細リストの更新
                // 月間表の更新データ設定
                CpnMucSypNenkanPtn2 data = new CpnMucSypNenkanPtn2();
                // 行事の内容
                data.setNaiyoKnj(month.getNaiyoKnj());
                // 実施計画上の位置付・目的
                data.setMokutekiKnj(month.getMokutekiKnj());

                // 月間表の更新条件設定
                CpnMucSypNenkanPtn2Criteria whereItem = new CpnMucSypNenkanPtn2Criteria();
                whereItem.createCriteria().andIdEqualTo(CommonDtoUtil.strValToInt(month.getId()));
                        // .andModifiedCntEqualTo(updateCount)
                        // .andDelFlgEqualTo(
                        //         Constants.DELL_FLG_OFF);

                // 更新時の共通カラム値設定処理
                // CommonDaoUtil.setUpdateCommonColumns(data, updateCount);
                // 月間表パターンマスタヘッダの更新
                int count = cpnMucSypNenkanPtn2Mapper.updateByCriteriaSelective(data, whereItem);

                // 更新失敗の場合
                if (count <= 0) {
                    throw new ExclusiveException();
                }

                // 「月間行事」リスト.更新区分がC:新規の場合
            } else if (CommonDtoUtil.isCreate(month)) {
                // 詳細リストの新規

                // 月間表の更新データ設定
                CpnMucSypNenkanPtn2 data = new CpnMucSypNenkanPtn2();
                // 月間・年間計画ID
                data.setNenkan1Id(CommonDtoUtil.strValToInt(month.getNenkan1Id()));
                // 月
                data.setMm(CommonDtoUtil.strValToInt(month.getMm()));
                // 行事の内容
                data.setNaiyoKnj(month.getNaiyoKnj());
                // 実施計画上の位置付・目的
                data.setMokutekiKnj(month.getMokutekiKnj());
                // 表示順
                data.setSeq(CommonDtoUtil.strValToInt(month.getSeq()));

                // 更新時の共通カラム値設定処理
                // CommonDaoUtil.setInsertCommonColumns(data);
                // 月間表パターンマスタヘッダの更新
                cpnMucSypNenkanPtn2Mapper.insertSelective(data);
            }
        }
    }
}
