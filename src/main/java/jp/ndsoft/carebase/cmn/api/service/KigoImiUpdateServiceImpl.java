package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jp.ndsoft.carebase.cmn.api.service.dto.KigoImiUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.KigoImiUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucHcc1Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc1;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc1Criteria;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.smh.framework.util.AppUtil;

/**
 * @since 2025.04.23
 * <AUTHOR> DAO VAN DUONG
 * @implNote GUI00835_記号と意味 画面 DTO.
 */
@Service
@Transactional
public class KigoImiUpdateServiceImpl
        extends UpdateServiceImpl<KigoImiUpdateServiceInDto, KigoImiUpdateServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 日時フォーマットのパターンを定義 */
    private static String PATTERN_FORMAT_DATETIME = "yyyy-MM-dd";

    /** CpnTucHcc1MapperDAO */
    @Autowired
    private CpnTucHcc1Mapper cpnTucHcc1Mapper;

    /**
     * ［フェースシート（パッケージプラン）］③
     * 
     * @param inDto ［フェースシート（パッケージプラン）］③の入力DTO.
     * @return［フェースシート（パッケージプラン）］③OUT DTO
     * @throws Exception Exception
     */
    @Override
    protected KigoImiUpdateServiceOutDto mainProcess(KigoImiUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // DTOOUT情報
        KigoImiUpdateServiceOutDto outDto = new KigoImiUpdateServiceOutDto();
        // 更新件数
        int count = 0;

        // タイムスタンプをLocalDateTimeに変換
        LocalDateTime localDateTime = AppUtil.getSystemTimeStamp().toLocalDateTime();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. バリデーションチェックを行う===============
         * 
         */
        // 特になし

        /*
         * ===============3.家族マスタリストの保存処理===============
         * 
         */
        // 3.1.1.INPUT.更新区分が"C"の場合新規を行う
        if (CommonDtoUtil.isCreate(inDto)) {
            final CpnTucHcc1 cpnTucHcc1 = new CpnTucHcc1();
            // ケアチェックID
            cpnTucHcc1.setCc1Id(CommonDtoUtil.strValToInt(inDto.getCc1Id()));
            // 計画期間ID
            cpnTucHcc1.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
            // 法人ID
            cpnTucHcc1.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
            // 施設ID
            cpnTucHcc1.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            // 事業者ID
            cpnTucHcc1.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 作成日
            cpnTucHcc1.setCreateYmd(localDateTime.format(DateTimeFormatter.ofPattern(PATTERN_FORMAT_DATETIME)));
            // 利用者ID
            cpnTucHcc1.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
            // 職員ID
            cpnTucHcc1.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
            // 記号と意味
            cpnTucHcc1.setKoumoku1Knj(inDto.getKoumoku1Knj());
            // 記号と意味
            cpnTucHcc1.setKoumoku2Knj(inDto.getKoumoku2Knj());
            // 記号と意味
            cpnTucHcc1.setKoumoku3Knj(inDto.getKoumoku3Knj());
            // 記号と意味
            cpnTucHcc1.setKoumoku4Knj(inDto.getKoumoku4Knj());
            // 記号と意味
            cpnTucHcc1.setKoumoku5Knj(inDto.getKoumoku5Knj());

            // DAOを実行
            // CommonDaoUtil.setInsertCommonColumns(cpnTucHcc1);
            count = cpnTucHcc1Mapper.insertSelective(cpnTucHcc1);

        }
        // 3.1.2.INPUT.更新区分が"U"の場合更新を行う
        if (CommonDtoUtil.isUpdate(inDto)) {
            final CpnTucHcc1 cpnTucHcc1 = new CpnTucHcc1();
            // ケアチェックID
            cpnTucHcc1.setCc1Id(CommonDtoUtil.strValToInt(inDto.getCc1Id()));
            // 計画期間ID
            cpnTucHcc1.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
            // 法人ID
            cpnTucHcc1.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
            // 施設ID
            cpnTucHcc1.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            // 事業者ID
            cpnTucHcc1.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 利用者ID
            cpnTucHcc1.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
            // 記号と意味
            cpnTucHcc1.setKoumoku1Knj(inDto.getKoumoku1Knj());
            // 記号と意味
            cpnTucHcc1.setKoumoku2Knj(inDto.getKoumoku2Knj());
            // 記号と意味
            cpnTucHcc1.setKoumoku3Knj(inDto.getKoumoku3Knj());
            // 記号と意味
            cpnTucHcc1.setKoumoku4Knj(inDto.getKoumoku4Knj());
            // 記号と意味
            cpnTucHcc1.setKoumoku5Knj(inDto.getKoumoku5Knj());

            // 更新条件
            final CpnTucHcc1Criteria criteria = new CpnTucHcc1Criteria();
            criteria.createCriteria().andCc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getCc1Id()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
                    // .andDelFlgEqualTo(Constants.DELL_FLG_OFF)
                    // .andModifiedCntEqualTo(new BigInteger(inDto.getModifiedCnt()));

            // DAOを実行
            // CommonDaoUtil.setUpdateCommonColumns(cpnTucHcc1, new BigInteger(inDto.getModifiedCnt()));
            count = cpnTucHcc1Mapper.updateByCriteriaSelective(cpnTucHcc1, criteria);

        }

        // ステータス(更新成功:'1' 更新失敗:'0')
        outDto.setStatus(count > 0 ? CommonDtoUtil.objValToString(Constants.VALID_YES)
                : CommonDtoUtil.objValToString(Constants.VALID_NO));

        // 更新失敗の場合
        if (CommonDtoUtil.objValToString(Constants.VALID_NO).equals(outDto.getStatus())) {
            // 3.2.エラーが発生する場合、ロールバックする。レスポンスパラメータのステータスに「０」を設定して、Exceptionをスローする
            throw new ExclusiveException();
        }
        LOG.info(Constants.END);
        return outDto;
    }
}
