package jp.ndsoft.carebase.cmn.api.report.logic;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.MonitoringEvaluationReportPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonDateParts;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonShiTeiDateParts;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.U01020Moni;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.U01020NeedsRetry;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkBase02Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.JigyoRirekiInfoDto;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.MonitoringEvaluationReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.model.MonitoringEvaluationReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMoni1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMoni1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMoniSyp2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMoniSyp2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.UserinfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.UserinfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YoushikiNoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YoushikiNoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocPrtYoushikiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMstChouhyouInkanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCmoni1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucSypCmoni2SelectMapper;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * @since 2025.08.07
 * <AUTHOR> 張婧
 * @description U01020_モニタリング・評価表（H21改訂版） 帳票出力
 */
@Component
public class MonitoringEvaluationReportLogic {

    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** Nds3GkBase02Logicロジッククラス */
    @Autowired
    Nds3GkBase02Logic nds3GkBase02Logic;

    /** KghCmnF01Logicロジッククラス */
    @Autowired
    KghCmnF01Logic kghCmnF01Logic;

    /** KghCmpF01Logicクラス */
    @Autowired
    KghCmpF01Logic kghCmpF01Logic;

    /** モニタリング・評価表（H21改訂版）のヘッダ情報取得 */
    @Autowired
    CpnTucCmoni1SelectMapper cpnTucCmoni1SelectMapper;

    /** 利用者の情報取得 */
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;

    /** ケアプラン帳票印鑑欄情報取得 */
    @Autowired
    private CpnMstChouhyouInkanSelectMapper cpnMstChouhyouInkanSelectMapper;

    /** 事業所名取得関数 */
    @Autowired
    private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;

    /** 文書番号情報取得 */
    @Autowired
    private ComMocPrtYoushikiSelectMapper comMocPrtYoushikiSelectMapper;

    /** モニタリング記録表_パッケージプランH21情報取得 */
    @Autowired
    private CpnTucSypCmoni2SelectMapper cpnTucSypCmoni2SelectMapper;

    /**
     * モニタリング・評価表（H21改訂版）情報の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public MonitoringEvaluationReportServiceInDto getMonitoringEvaluationReportParameters(
            MonitoringEvaluationReportParameterModel inDto)
            throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 帳票用データ詳細
        MonitoringEvaluationReportServiceInDto infoInDto = new MonitoringEvaluationReportServiceInDto();

        // モニタリング記録表ヘッダ情報取得
        List<CpnMoni1OutEntity> cpnMoni1OutEntityList = new ArrayList<>();

        // 利用者基本（１－６）の情報取得
        List<UserinfoOutEntity> userinfoOutEntities = new ArrayList<>();

        // 【リクエストパラメータ】.印刷オプション
        MonitoringEvaluationReportPrintOption printOption = inDto.getPrintOption();

        // 印鑑欄設定情報
        List<KghCpnMstChouhyouInkanPrnOutEntity> chouOutList = new ArrayList<>();

        // モニタリング記録表_パッケージプランH21情報取得
        List<CpnMoniSyp2OutEntity> cpnMoniSyp2List = new ArrayList<>();

        // 2. リクエストパラメータ.印刷オプション.記入用シートを印刷するフラグ = true の場合、下記の処理6から行う。
        // リクエストパラメータ.印刷オプション.記入用シートを印刷するフラグ = false の場合、モニタリング・評価表（H21改訂版）情報の取得。
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg()) || CommonDtoUtil.checkStringEqual(
                printOption.getEmptyFlg(), CommonConstants.STR_1)) {

            // 2. モニタリング・評価表（H21改訂版）情報の取得。
            // 2.1. 下記のモニタリング記録表ヘッダ情報取得のDAOを利用し、モニタリング・評価表（H21改訂版）のヘッダ情報を取得する。
            CpnMoni1ByCriteriaInEntity cpnMoni1ByCriteriaInEntity = new CpnMoni1ByCriteriaInEntity();
            // 計画期間ID
            cpnMoni1ByCriteriaInEntity
                    .setSc1(CommonDtoUtil.strValToInt(inDto.getPrintSubjectHistoryList().get(0).getSc1Id()));
            // ヘッダID
            cpnMoni1ByCriteriaInEntity
                    .setCmoni1(CommonDtoUtil.strValToInt(inDto.getPrintSubjectHistoryList().get(0).getRirekiId()));

            cpnMoni1OutEntityList = cpnTucCmoni1SelectMapper
                    .findCpnMoni1ByCriteria(cpnMoni1ByCriteriaInEntity);

            // 2.2. 下記のモニタリング・評価表情報取得のDAOを利用し、モニタリング・評価表（H21改訂版）の明細情報を取得する。
            CpnMoniSyp2ByCriteriaInEntity cpnMoniSyp2ByCriteriaInEntity = new CpnMoniSyp2ByCriteriaInEntity();
            // ヘッダID
            cpnMoniSyp2ByCriteriaInEntity
                    .setCmoni1Id(CommonDtoUtil.strValToInt(inDto.getPrintSubjectHistoryList().get(0).getRirekiId()));

            cpnMoniSyp2List = this.cpnTucSypCmoni2SelectMapper
                    .findCpnMoniSyp2ByCriteria(cpnMoniSyp2ByCriteriaInEntity);

            if (!CollectionUtils.isNullOrEmpty(cpnMoniSyp2List)) {
                // 3. 利用者基本情報を取得する。
                // 下記の利用者基本（１－６）情報取得のDAOを利用し、利用者の情報を取得する。
                UserinfoByCriteriaInEntity userinfoByCriteria = new UserinfoByCriteriaInEntity();
                userinfoByCriteria
                        .setUser(CommonDtoUtil.strValToInt(inDto.getPrintSubjectHistoryList().get(0).getUserId()));

                userinfoOutEntities = comTucUserSelectMapper
                        .findUserinfoByCriteria(userinfoByCriteria);

                // 4. 印鑑欄設定情報を取得する。
                // リクエストパラメータ.記入用シートを印刷するフラグ = falseの場合、印鑑欄設定情報の取得
                // 下記の28-xx ケアプラン帳票印鑑欄情報取得のDAOを利用し、印鑑欄設定情報を取得する。
                KghCpnMstChouhyouInkanPrnByCriteriaInEntity prnInRntity = new KghCpnMstChouhyouInkanPrnByCriteriaInEntity();
                // 法人ID リクエストパラメータ.法人ID
                prnInRntity.setAnKey1(CommonDtoUtil.strValToInt(inDto.getJigyoInfo().getHoujinId()));
                // 施設ID リクエストパラメータ.施設ID
                prnInRntity.setAnKey2(CommonDtoUtil.strValToInt(inDto.getJigyoInfo().getShisetuId()));
                // 事業所ID リクエストパラメータ.事業所ID
                prnInRntity.setAnKey3(CommonDtoUtil.strValToInt(inDto.getJigyoInfo().getSvJigyoId()));
                // 帳票セクション番号 リクエストパラメータ.データ.出力帳票印刷情報リスト[0].セクション
                prnInRntity.setAsSec(inDto.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection());
                // 印鑑欄設定情報取得
                chouOutList = cpnMstChouhyouInkanSelectMapper
                        .findKghCpnMstChouhyouInkanPrnByCriteria(prnInRntity);

            }

        }

        // 6. 上記編集した帳票用データを設定する。
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg()) || CommonDtoUtil.checkStringEqual(
                printOption.getEmptyFlg(), CommonConstants.STR_1)) {
            infoInDto = this.setMonitoringEvaluationDefReportParams(inDto);
            infoInDto.setEmptyFlg(true);
        } else {
            infoInDto = this.setMonitoringEvaluationReportParams(inDto, cpnMoni1OutEntityList, userinfoOutEntities,
                    chouOutList, cpnMoniSyp2List);
            infoInDto.setEmptyFlg(false);
        }

        // 事業所名
        // 6. 事業所名情報を取得する。
        JigyoRirekiInfoDto cpnFuncOutInfo = kghKrkZCpnFuncLogic.getJigyoRirekiKnj(
                CommonDtoUtil.strValToInt(inDto.getJigyoInfo().getSvJigyoId()), inDto.getAppYmd());
        // 事業所名＝共通関数補足の処理「6」の事業所略称
        infoInDto.setJigyoName(cpnFuncOutInfo.getJigyoRirekiRyakuKnj());

        // 敬称
        String keisho = CommonConstants.BLANK_STRING;
        // ・リクエストパラメータ.データ.印刷オプション.敬称を変更するフラグ = false の場合
        if (CommonConstants.STR_FALSE.equals(inDto.getPrintOption().getTitleOfHonorFlg())) {
            // ・リクエストパラメータ.データ.システム設定敬称を変更するフラグ = false の場合
            if (CommonConstants.STR_FALSE.equals(inDto.getStKeishoFlg())) {
                // 敬称 = "殿"
                keisho = ReportConstants.KEISHO_TONO;
                // ・リクエストパラメータ.データ.システム設定敬称を変更するフラグ = true の場合
            } else {
                // 敬称 = リクエストパラメータ.データ.システム設定敬称の内容
                keisho = ReportUtil.nullToEmpty(inDto.getStKeisho());
            }
            // ・リクエストパラメータ.データ.印刷オプション.敬称を変更するフラグ = true の場合
        } else {
            // 敬称 = リクエストパラメータ.データ.印刷オプション.敬称の内容
            keisho = ReportUtil.nullToEmpty(inDto.getPrintOption().getTitleOfHonorName());
        }
        infoInDto.setKeisho(keisho);

        // 承認欄印刷区分
        infoInDto.setShoninKbn(CommonDtoUtil.strValToInt(printOption.getShoninFlg()));
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true ニタリング・評価表（H21改訂版）パラメータ取得
     * 
     * @param model        入力データ
     * @param jasperDesign 帳票データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private MonitoringEvaluationReportServiceInDto setMonitoringEvaluationDefReportParams(
            MonitoringEvaluationReportParameterModel inDto) {
        // 帳票用データ詳細
        MonitoringEvaluationReportServiceInDto infoInDto = new MonitoringEvaluationReportServiceInDto();

        // リクエストパラメータ.データ.印刷オプション.モニタリングフラグ = 1 の場合、
        if (CommonConstants.STR_1.equals(inDto.getPrintOption().getMoniFlg())) {
            // 帳票タイトル＝"(Ⅷ)処遇計画モニタリング・評価表"
            infoInDto.setTitle(ReportConstants.MONITORING_EVALUATION);
            // リクエストパラメータ.データ.印刷オプション.モニタリングフラグ = 2 の場合、
        } else if (CommonConstants.STR_2.equals(inDto.getPrintOption().getMoniFlg())) {
            // 帳票タイトル＝"(Ⅸ)特定施設サービス計画／介護予防特定施設サービス計画モニタリング・評価表"
            infoInDto.setTitle(ReportConstants.SERVICE_PLAN_MONITORING_EVALUATION);
            // 上記以外の場合、
        } else {
            // 帳票タイトル＝””
            infoInDto.setTitle(CommonConstants.BLANK_STRING);
        }

        // 指定日（年号）
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);

        // 利用者氏名
        infoInDto.setFullName(CommonConstants.BLANK_STRING);

        // ケース番号
        infoInDto.setCaseNo(CommonConstants.BLANK_STRING);

        // 初回作成年月日（年号）
        infoInDto.setShokaiYmdGG(CommonConstants.FULL_WIDTH_SPACE);
        // 初回作成年月日（年）
        infoInDto.setShokaiYmdYY(CommonConstants.FULL_WIDTH_SPACE);
        // 初回作成年月日（月）
        infoInDto.setShokaiYmdMM(CommonConstants.FULL_WIDTH_SPACE);
        // 初回作成年月日（日）
        infoInDto.setShokaiYmdDD(CommonConstants.FULL_WIDTH_SPACE);

        // 作成(変更)年月日（年号）
        infoInDto.setCreateYmdGG(CommonConstants.FULL_WIDTH_SPACE);
        // 作成(変更)年月日（年）
        infoInDto.setCreateYmdYY(CommonConstants.FULL_WIDTH_SPACE);
        // 作成(変更)年月日（月）
        infoInDto.setCreateYmdMM(CommonConstants.FULL_WIDTH_SPACE);
        // 作成(変更)年月日（日）
        infoInDto.setCreateYmdDD(CommonConstants.FULL_WIDTH_SPACE);

        // 印鑑欄表示区分
        infoInDto.setHankoHyoujiKbn(CommonConstants.NUMBER_0);

        // 印鑑1
        infoInDto.setHanko1Knj(CommonConstants.BLANK_STRING);
        // 印鑑2
        infoInDto.setHanko2Knj(CommonConstants.BLANK_STRING);
        // 印鑑3
        infoInDto.setHanko3Knj(CommonConstants.BLANK_STRING);
        // 印鑑4
        infoInDto.setHanko4Knj(CommonConstants.BLANK_STRING);
        // 印鑑5
        infoInDto.setHanko5Knj(CommonConstants.BLANK_STRING);
        // 印鑑6
        infoInDto.setHanko6Knj(CommonConstants.BLANK_STRING);
        // 印鑑7
        infoInDto.setHanko7Knj(CommonConstants.BLANK_STRING);
        // 印鑑8
        infoInDto.setHanko8Knj(CommonConstants.BLANK_STRING);
        // 印鑑9
        infoInDto.setHanko9Knj(CommonConstants.BLANK_STRING);
        // 印鑑10
        infoInDto.setHanko10Knj(CommonConstants.BLANK_STRING);
        // 印鑑11
        infoInDto.setHanko11Knj(CommonConstants.BLANK_STRING);
        // 印鑑12
        infoInDto.setHanko12Knj(CommonConstants.BLANK_STRING);
        // 印鑑13
        infoInDto.setHanko13Knj(CommonConstants.BLANK_STRING);
        // 印鑑14
        infoInDto.setHanko14Knj(CommonConstants.BLANK_STRING);
        // 印鑑15
        infoInDto.setHanko15Knj(CommonConstants.BLANK_STRING);

        List<U01020Moni> moniList = new ArrayList<>();
        U01020Moni moni = new U01020Moni();
        // モニタリング・目標
        moni.setMokuhyoKnj(CommonConstants.BLANK_STRING);
        // モニタリング・対象期間
        moni.setKikanKnj(CommonConstants.BLANK_STRING);
        // モニタリング・サービスの実施状況
        moni.setJyoukyouKnj(CommonConstants.BLANK_STRING);
        // 評価・計画作成担当者・目標達成状況
        moni.setTantoHyouka(CommonConstants.BLANK_STRING);
        // 評価・計画作成担当者・その理由
        moni.setTantoHyoukaKnj(CommonConstants.BLANK_STRING);
        // 評価・利用者・目標達成状況
        moni.setUserHyouka(CommonConstants.BLANK_STRING);
        // 評価・利用者・その理由
        moni.setUserHyoukaKnj(CommonConstants.BLANK_STRING);
        // 今後の対応
        moni.setTaiou(CommonConstants.BLANK_STRING);
        // その理由
        moni.setTaiouKnj(CommonConstants.BLANK_STRING);
        moniList.add(moni);

        JRBeanCollectionDataSource moniListDataSource = new JRBeanCollectionDataSource(moniList);
        infoInDto.setMoniList(moniListDataSource);

        List<U01020NeedsRetry> needsRetryList = new ArrayList<>();
        U01020NeedsRetry needsRetry = new U01020NeedsRetry();
        // 新たなニーズ・再アセスメントリスト(1).ラベル名
        needsRetry.setLevelName(ReportConstants.STR_NEWS);
        // 新たなニーズ・再アセスメントリスト(1).有無
        needsRetry.setUmuFlg(null);
        // 新たなニーズ・再アセスメントリスト(1).計画の変更等・必要性
        needsRetry.setHenkouRetryKnj(
                ReportConstants.LINE_BREAKS + CommonConstants.BLANK_SPACE + ReportConstants.LINE_BREAKS);
        U01020NeedsRetry needsRetry2 = new U01020NeedsRetry();
        // 新たなニーズ・再アセスメントリスト(2).ラベル名
        needsRetry2.setLevelName(ReportConstants.STR_ASSESSMENT);
        // 新たなニーズ・再アセスメントリスト(2).有無
        needsRetry2.setUmuFlg(null);
        // 新たなニーズ・再アセスメントリスト(2).計画の変更等・必要性
        needsRetry2.setHenkouRetryKnj(
                ReportConstants.LINE_BREAKS + CommonConstants.BLANK_SPACE + ReportConstants.LINE_BREAKS);

        needsRetryList.add(needsRetry);
        needsRetryList.add(needsRetry2);

        JRBeanCollectionDataSource needsRetryListDataSource = new JRBeanCollectionDataSource(needsRetryList);
        infoInDto.setNeedsRetryList(needsRetryListDataSource);

        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;

    }

    /**
     * 記入用シートを印刷するフラグ = false ニタリング・評価表（H21改訂版）パラメータ取得
     * 
     * @param inDto                 入力データ
     * @param cpnMoni1OutEntityList モニタリング記録表ヘッダ情報取得
     * @param userinfoOutEntities   利用者基本（１－６）の情報取得
     * @param chouOutList           印鑑欄設定情報
     * @param cpnMoniSyp2List       モニタリング記録表_パッケージプランH21情報取得
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private MonitoringEvaluationReportServiceInDto setMonitoringEvaluationReportParams(
            MonitoringEvaluationReportParameterModel inDto, List<CpnMoni1OutEntity> cpnMoni1OutEntityList,
            List<UserinfoOutEntity> userinfoOutEntities, List<KghCpnMstChouhyouInkanPrnOutEntity> chouOutList,
            List<CpnMoniSyp2OutEntity> cpnMoniSyp2List) {
        // 帳票用データ詳細
        MonitoringEvaluationReportServiceInDto infoInDto = new MonitoringEvaluationReportServiceInDto();

        // 計画の変更等・必要性
        String henkouKnj = CommonConstants.BLANK_STRING;
        // 再アセスメントの必要性
        String retryKnj = CommonConstants.BLANK_STRING;
        if (!CollectionUtils.isNullOrEmpty(cpnMoni1OutEntityList)) {
            // API定義の処理「2.1」のモニタリングフラグ= 1 の場合、
            if (CommonConstants.INT_1 == cpnMoni1OutEntityList.get(0).getMoniFlg()) {
                // 帳票タイトル＝"(Ⅷ)処遇計画モニタリング・評価表"
                infoInDto.setTitle(ReportConstants.MONITORING_EVALUATION);
                // API定義の処理「2.1」のモニタリングフラグ= 2 の場合、
            } else if (CommonConstants.INT_2 == cpnMoni1OutEntityList.get(0).getMoniFlg()) {
                // 帳票タイトル＝"(Ⅸ)特定施設サービス計画／介護予防特定施設サービス計画モニタリング・評価表"
                infoInDto.setTitle(ReportConstants.SERVICE_PLAN_MONITORING_EVALUATION);
                // 上記以外の場合、
            } else {
                // 帳票タイトル＝””
                infoInDto.setTitle(CommonConstants.BLANK_STRING);
            }

            // ケース番号
            infoInDto.setCaseNo(cpnMoni1OutEntityList.get(0).getCaseNo());

            // 4. 初回作成日の和暦変換処理
            String shokaiDate = kghCmpF01Logic.getCmpS2wjEz(cpnMoni1OutEntityList.get(0).getShokaiYmd(),
                    CommonConstants.NUMBER_1);
            ReportCommonDateParts shokaiDateParts = this.getDate(shokaiDate);
            // 初回作成年月日（年号）
            infoInDto.setShokaiYmdGG(shokaiDateParts.getDateGG());
            // 初回作成年月日（年）
            infoInDto.setShokaiYmdYY(shokaiDateParts.getDateYY());
            // 初回作成年月日（月）
            infoInDto.setShokaiYmdMM(shokaiDateParts.getDateMM());
            // 初回作成年月日（日）
            infoInDto.setShokaiYmdDD(shokaiDateParts.getDateDD());

            // 3. 作成(変更)年月日の和暦変換処理
            String createDate = kghCmpF01Logic.getCmpS2wjEz(cpnMoni1OutEntityList.get(0).getCreateYmd(),
                    CommonConstants.NUMBER_1);
            ReportCommonDateParts createDateParts = this.getDate(createDate);
            // 作成(変更)年月日（年号）
            infoInDto.setCreateYmdGG(createDateParts.getDateGG());
            // 作成(変更)年月日（年）
            infoInDto.setCreateYmdYY(createDateParts.getDateYY());
            // 作成(変更)年月日（月）
            infoInDto.setCreateYmdMM(createDateParts.getDateMM());
            // 作成(変更)年月日（日）
            infoInDto.setCreateYmdDD(createDateParts.getDateDD());

            // 計画の変更等・必要性
            henkouKnj = cpnMoni1OutEntityList.get(0).getHenkouKnj();

            // 再アセスメントの必要性
            retryKnj = cpnMoni1OutEntityList.get(0).getRetryKnj();

            // 上記以外の場合、
        } else {
            // 帳票タイトル＝””
            infoInDto.setTitle(CommonConstants.BLANK_STRING);
            // ケース番号
            infoInDto.setCaseNo(CommonConstants.BLANK_STRING);

            // 初回作成年月日（年号）
            infoInDto.setShokaiYmdGG(CommonConstants.FULL_WIDTH_SPACE);
            // 初回作成年月日（年）
            infoInDto.setShokaiYmdYY(CommonConstants.FULL_WIDTH_SPACE);
            // 初回作成年月日（月）
            infoInDto.setShokaiYmdMM(CommonConstants.FULL_WIDTH_SPACE);
            // 初回作成年月日（日）
            infoInDto.setShokaiYmdDD(CommonConstants.FULL_WIDTH_SPACE);

            // 作成(変更)年月日（年号）
            infoInDto.setCreateYmdGG(CommonConstants.FULL_WIDTH_SPACE);
            // 作成(変更)年月日（年）
            infoInDto.setCreateYmdYY(CommonConstants.FULL_WIDTH_SPACE);
            // 作成(変更)年月日（月）
            infoInDto.setCreateYmdMM(CommonConstants.FULL_WIDTH_SPACE);
            // 作成(変更)年月日（日）
            infoInDto.setCreateYmdDD(CommonConstants.FULL_WIDTH_SPACE);
        }

        // 1. 指定日の空欄表示処理
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(inDto.getPrintSet().getShiTeiKubun(),
                inDto.getPrintSet().getShiTeiDate(), inDto.getSystemDate());
        // 指定日（年号）
        infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
        // 指定日（年）
        infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
        // 指定日（月）
        infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
        // 指定日（日）
        infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

        // 利用者氏名
        // 変数.利用者名
        String shokuKnj = CommonConstants.BLANK_STRING;
        if (!CollectionUtils.isNullOrEmpty(userinfoOutEntities)) {
            // 氏名（姓）
            String name1Knj = ReportUtil.nullToEmpty(userinfoOutEntities.get(0).getName1Knj());
            // 氏名（名）
            String name2Knj = ReportUtil.nullToEmpty(userinfoOutEntities.get(0).getName2Knj());
            shokuKnj = (name1Knj + CommonConstants.BLANK_SPACE + name2Knj).trim();
        }
        // 利用者氏名＝API定義の処理「3.1」の氏名（姓）+ " " + API定義の処理「3.1」の氏名（名）
        infoInDto.setFullName(shokuKnj);

        // 印鑑欄設定情報取得できる
        if (!CollectionUtils.isNullOrEmpty(chouOutList)) {
            // 印鑑欄表示区分
            infoInDto.setHankoHyoujiKbn(
                    CommonDtoUtil.strValToInt(CommonDtoUtil.objValToString(chouOutList.get(0).getHyoujiKbn())));

            // 印鑑1
            infoInDto.setHanko1Knj(chouOutList.get(0).getHanko1Knj());
            // 印鑑2
            infoInDto.setHanko2Knj(chouOutList.get(0).getHanko2Knj());
            // 印鑑3
            infoInDto.setHanko3Knj(chouOutList.get(0).getHanko3Knj());
            // 印鑑4
            infoInDto.setHanko4Knj(chouOutList.get(0).getHanko4Knj());
            // 印鑑5
            infoInDto.setHanko5Knj(chouOutList.get(0).getHanko5Knj());
            // 印鑑6
            infoInDto.setHanko6Knj(chouOutList.get(0).getHanko6Knj());
            // 印鑑7
            infoInDto.setHanko7Knj(chouOutList.get(0).getHanko7Knj());
            // 印鑑8
            infoInDto.setHanko8Knj(chouOutList.get(0).getHanko8Knj());
            // 印鑑9
            infoInDto.setHanko9Knj(chouOutList.get(0).getHanko9Knj());
            // 印鑑10
            infoInDto.setHanko10Knj(chouOutList.get(0).getHanko10Knj());
            // 印鑑11
            infoInDto.setHanko11Knj(chouOutList.get(0).getHanko11Knj());
            // 印鑑12
            infoInDto.setHanko12Knj(chouOutList.get(0).getHanko12Knj());
            // 印鑑13
            infoInDto.setHanko13Knj(chouOutList.get(0).getHanko13Knj());
            // 印鑑14
            infoInDto.setHanko14Knj(chouOutList.get(0).getHanko14Knj());
            // 印鑑15
            infoInDto.setHanko15Knj(chouOutList.get(0).getHanko15Knj());
        } else {
            // 印鑑欄表示区分
            infoInDto.setHankoHyoujiKbn(CommonConstants.NUMBER_0);
            // 印鑑1
            infoInDto.setHanko1Knj(CommonConstants.BLANK_STRING);
            // 印鑑2
            infoInDto.setHanko2Knj(CommonConstants.BLANK_STRING);
            // 印鑑3
            infoInDto.setHanko3Knj(CommonConstants.BLANK_STRING);
            // 印鑑4
            infoInDto.setHanko4Knj(CommonConstants.BLANK_STRING);
            // 印鑑5
            infoInDto.setHanko5Knj(CommonConstants.BLANK_STRING);
            // 印鑑6
            infoInDto.setHanko6Knj(CommonConstants.BLANK_STRING);
            // 印鑑7
            infoInDto.setHanko7Knj(CommonConstants.BLANK_STRING);
            // 印鑑8
            infoInDto.setHanko8Knj(CommonConstants.BLANK_STRING);
            // 印鑑9
            infoInDto.setHanko9Knj(CommonConstants.BLANK_STRING);
            // 印鑑10
            infoInDto.setHanko10Knj(CommonConstants.BLANK_STRING);
            // 印鑑11
            infoInDto.setHanko11Knj(CommonConstants.BLANK_STRING);
            // 印鑑12
            infoInDto.setHanko12Knj(CommonConstants.BLANK_STRING);
            // 印鑑13
            infoInDto.setHanko13Knj(CommonConstants.BLANK_STRING);
            // 印鑑14
            infoInDto.setHanko14Knj(CommonConstants.BLANK_STRING);
            // 印鑑15
            infoInDto.setHanko15Knj(CommonConstants.BLANK_STRING);
        }

        List<U01020Moni> moniList = new ArrayList<>();
        if (!CollectionUtils.isNullOrEmpty(cpnMoniSyp2List)) {
            cpnMoniSyp2List.forEach(cpnMoniSyp2 -> {
                U01020Moni moni = new U01020Moni();
                // モニタリング・目標
                moni.setMokuhyoKnj(ReportUtil.nullToEmpty(cpnMoniSyp2.getMokuhyoKnj()));
                // モニタリング・対象期間
                moni.setKikanKnj(ReportUtil.nullToEmpty(cpnMoniSyp2.getKikanKnj()));
                // モニタリング・サービスの実施状況
                moni.setJyoukyouKnj(ReportUtil.nullToEmpty(cpnMoniSyp2.getJyoukyouKnj()));
                // 評価・計画作成担当者・目標達成状況
                moni.setTantoHyouka(ReportUtil.nullToEmpty(cpnMoniSyp2.getTantoHyoukaCd()));
                // 評価・計画作成担当者・その理由
                moni.setTantoHyoukaKnj(ReportUtil.nullToEmpty(cpnMoniSyp2.getTantoHyoukaKnj()));
                // 評価・利用者・目標達成状況
                moni.setUserHyouka(ReportUtil.nullToEmpty(cpnMoniSyp2.getUserHyoukaCd()));
                // 評価・利用者・その理由
                moni.setUserHyoukaKnj(ReportUtil.nullToEmpty(cpnMoniSyp2.getUserHyoukaKnj()));
                // 今後の対応
                moni.setTaiou(ReportUtil.nullToEmpty(cpnMoniSyp2.getTaiouCd()));
                // その理由
                moni.setTaiouKnj(ReportUtil.nullToEmpty(cpnMoniSyp2.getTaiouKnj()));
                moniList.add(moni);
            });

        }
        JRBeanCollectionDataSource moniListDataSource = new JRBeanCollectionDataSource(moniList);

        infoInDto.setMoniList(moniListDataSource);

        List<U01020NeedsRetry> needsRetryList = new ArrayList<>();
        U01020NeedsRetry needsRetry = new U01020NeedsRetry();
        // 新たなニーズラベル=”新たなニーズの発生：”
        needsRetry.setLevelName(ReportConstants.STR_NEWS);
        // 新たなニーズ発生の有無=API定義の処理「2.2」の新たなニーズ発生の有無
        needsRetry.setUmuFlg(null);
        // 計画の変更等=API定義の処理「2.1」の計画の変更等
        needsRetry.setHenkouRetryKnj(henkouKnj);
        U01020NeedsRetry needsRetry2 = new U01020NeedsRetry();
        // 再アセスメントラベル=”再アセスメントの必要性：”
        needsRetry2.setLevelName(ReportConstants.STR_ASSESSMENT);
        // 再アセスメントの必要=API定義の処理「2.2」の再アセスメントの必要
        needsRetry2.setUmuFlg(null);
        // 再アセスメントの必要性=API定義の処理「2.1」の再アセスメントの必要性
        needsRetry2.setHenkouRetryKnj(retryKnj);

        needsRetryList.add(needsRetry);
        needsRetryList.add(needsRetry2);

        JRBeanCollectionDataSource needsRetryListDataSource = new JRBeanCollectionDataSource(needsRetryList);
        infoInDto.setNeedsRetryList(needsRetryListDataSource);

        // 文書管理番号=共通関数補足の処理「2.2」の文書番号

        infoInDto.setBunsyoKanriNo(
                this.getBunsyoKanriNo(inDto.getSyscd(),
                        inDto.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getShokuId(),
                        inDto.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection(),
                        inDto.getJigyoInfo().getSvJigyoId()));

        return infoInDto;
    }

    /**
     * 指定日を取得する
     * 
     * @param shiTeiKubun 指定日印刷区分
     * @param shiTeiDate  西暦日付
     * @param systemDate  システム日付
     * @throws Exception 例外
     */
    private ReportCommonShiTeiDateParts getShiTeiDate(String shiTeiKubun, String shiTeiDate, String systemDate) {
        // 指定日（年号）全角フレーム
        String shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（年）全角フレーム
        String shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（月）全角フレーム
        String shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（日）全角フレーム
        String shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日印刷区分判定
        switch (shiTeiKubun) {
            case ReportConstants.PRINTDATE_KBN_SHINAI:
                // 指定日印刷区分 = 1 の場合
                // 指定日（年号）全角フレーム
                shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（年）全角フレーム
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）全角フレーム
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）全角フレーム
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            case ReportConstants.PRINTDATE_KBN_PRINT:
                // 指定日印刷区分 = 2 の場合
                // 西暦日付を和暦日付に変換する
                List<String> dateParts = ReportUtil.getLocalDateToJapanDateTimeFormat(shiTeiDate);
                // 指定日（年号）
                shiTeiDateGG = dateParts.get(ReportConstants.SHITEIDATE_GG);
                // 指定日（年）
                shiTeiDateYY = dateParts.get(ReportConstants.SHITEIDATE_YY);
                // 指定日（月）
                shiTeiDateMM = dateParts.get(ReportConstants.SHITEIDATE_MM);
                // 指定日（日）
                shiTeiDateDD = dateParts.get(ReportConstants.SHITEIDATE_DD);
                break;
            case ReportConstants.PRINTDATE_KBN_BLANKDATE:
                // 指定日印刷区分 = 3 の場合
                // 指定日の空欄表示処理
                DateTimeFormatter inputFormatter = DateTimeFormatter
                        .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
                DateTimeFormatter outputFormatter = DateTimeFormatter
                        .ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
                LocalDateTime dateTime = LocalDateTime.parse(systemDate, inputFormatter);
                String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
                // 指定日（年号）
                shiTeiDateGG = blankDate.substring(0, 2);
                // 指定日（年）全角フレーム
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）全角フレーム
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）全角フレーム
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            default:
                // 指定日（年号）全角フレーム
                shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（年）全角フレーム
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）全角フレーム
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）全角フレーム
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
        }
        return new ReportCommonShiTeiDateParts(shiTeiDateGG, shiTeiDateYY, shiTeiDateMM, shiTeiDateDD);
    }

    /**
     * 日付を取得する
     * 
     * @param shiTeiDate 西暦日付
     * @throws Exception 例外
     */
    private ReportCommonDateParts getDate(String date) {
        // 日付（年号）=""
        String dateGG = CommonConstants.BLANK_STRING;
        // 日付（年）=""
        String dateYY = CommonConstants.BLANK_STRING;
        // 日付（月）=""
        String dateMM = CommonConstants.BLANK_STRING;
        // 日付（日）=""
        String dateDD = CommonConstants.BLANK_STRING;
        if (StringUtils.isNotEmpty(date)) {
            dateGG = date.substring(CommonConstants.INT_0, CommonConstants.INT_2);
            dateYY = date.substring(CommonConstants.INT_2, CommonConstants.INT_4);
            dateMM = date.substring(CommonConstants.INT_5, CommonConstants.INT_7);
            dateDD = date.substring(CommonConstants.INT_8, CommonConstants.INT_10);
        }
        return new ReportCommonDateParts(dateGG, dateYY, dateMM, dateDD);
    }

    /**
     * 文書番号情報取得処理。
     * 
     * @param sysCode    システムコード
     * @param shokuId    職員ID
     * @param sectionKnj セクション
     * @param svJigyoId  サービス事業者ID
     * @return アセスメント種別
     */
    public String getBunsyoKanriNo(String sysCode, String shokuId, String sectionKnj, String svJigyoId) {
        String bunsyoKanriNo = CommonConstants.BLANK_STRING;
        // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01Logic 関数名：GetF3GkProfile を利用し、
        // 設定の読込（システム環境以外の設定）情報を取得する。
        F3gkGetProfileInDto f3gkGetProfileInDto = new F3gkGetProfileInDto();
        f3gkGetProfileInDto.setGsyscd(sysCode);
        f3gkGetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(shokuId));
        f3gkGetProfileInDto.setHoujinId(CommonConstants.HOUJIN_ID_0);
        f3gkGetProfileInDto.setShisetuId(CommonConstants.SHISETU_ID);
        f3gkGetProfileInDto.setSvJigyoId(CommonConstants.SV_JIGYO_ID_0);
        f3gkGetProfileInDto.setKinounameKnj(CommonConstants.KINOU_NAME_PRT);
        f3gkGetProfileInDto.setSectionKnj(sectionKnj);
        f3gkGetProfileInDto.setKeyKnj(CommonConstants.ISO9001_FLG);
        f3gkGetProfileInDto.setAsDefault(CommonConstants.STR_DEFAULTDE_0);
        String f3gkProfile = nds3GkFunc01Logic.getF3gkProfile(f3gkGetProfileInDto);

        // 2.2. 設定の読込（システム環境以外の設定）情報取得の戻り値は"1"（正常参照）の場合、下記の24-06
        // 帳票書式番号設定マスタ情報取得のDAOを利用し、文書番号を取得する。
        if (CommonConstants.STR_1.equals(f3gkProfile)) {
            YoushikiNoByCriteriaInEntity youshikiNoByCriteriaInEntity = new YoushikiNoByCriteriaInEntity();
            youshikiNoByCriteriaInEntity.setAsSection(sectionKnj);
            youshikiNoByCriteriaInEntity.setLlSvJigyoId(svJigyoId);
            List<YoushikiNoOutEntity> youshikiNoOutList = comMocPrtYoushikiSelectMapper
                    .findYoushikiNoByCriteria(youshikiNoByCriteriaInEntity);
            if (youshikiNoOutList != null && youshikiNoOutList.size() > CommonConstants.INT_0) {
                bunsyoKanriNo = youshikiNoOutList.get(0).getYoushikiNo();
            }
        }
        return bunsyoKanriNo;
    }
}
