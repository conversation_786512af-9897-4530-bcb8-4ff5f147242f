package jp.ndsoft.carebase.cmn.api.report.dto;

import lombok.Data;

/**
 * 
 * 情報収集シートの詳細リスト
 * 
 * <AUTHOR>
 */
@Data
public class InfoCollectionSheet2Dto {

    /** 二級メニュー */
    private String level2Knj;
    /** 三級メニューID */
    private String level3KoumokuNo;
    /** 情報項目 */
    private String level3Knj;
    /** 検討 */
    private String kento;
    /** 具体的状況 */
    private String memo1Knj;
    /** 書式2フラグ */
    private String shosiki2Flg;
    /** 三級メニューIDのフォント */
    private String level3KoumokuNoFont;
    /** 情報項目のフォント */
    private String level3KnjFont;
    /** 可動域の制限タイトル */
    private String kadou1;
    /** 可動域制限タイトル */
    private String kadou2;
    /** 自発運動タイトル */
    private String kadou3;
    /** 可動域の制限のNo. */
    private String kadou1KoumokuNo;
    /** 可動域の制限の情報項目 */
    private String kadou1Level3Knj;
    /** 可動域制限の具体的状況 */
    private String kadou2Memo1Knj;
    /** 自発運動の具体的状況 */
    private String kadou3Memo1Knj;

    public InfoCollectionSheet2Dto() {
        /** 二級メニュー */
        this.level2Knj = "";
        /** 三級メニューID */
        this.level3KoumokuNo = null;
        /** 三級メニューIDのフォント */
        this.level3KoumokuNoFont = null;
        /** 情報項目 */
        this.level3Knj = "";
        /** 情報項目のフォント */
        this.level3KnjFont = "";
        /** 具体的状況 */
        this.memo1Knj = "";
        /** 書式2フラグ */
        this.shosiki2Flg = "";
        /** 検討 */
        this.kento = "";
        /** 可動域の制限タイトル */
        this.kadou1 = "";
        /** 可動域制限タイトル */
        this.kadou2 = "";
        /** 自発運動タイトル */
        this.kadou3 = "";
        /** 可動域の制限のNo. */
        this.kadou1KoumokuNo = "";
        /** 可動域の制限の情報項目 */
        this.kadou1Level3Knj = "";
        /** 可動域制限の具体的状況 */
        this.kadou2Memo1Knj = "";
        /** 自発運動の具体的状況 */
        this.kadou3Memo1Knj = "";
    }
}