package jp.ndsoft.carebase.cmn.api.report.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.InfoCollectionReportCommonPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentSheetPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonCareCheckPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonCareCheckPrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonChoPrt;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonHomeAssessmentAllChoPrt;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportHomeAssessmentAllPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportHomeAssessmentAllPrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentSheetRegionPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentSheetRegionPrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetAReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetBReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetCReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetDReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetEReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetFReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetGReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetHReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetIReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetJReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetKReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetLReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetMReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetNReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetOReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetPReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetQReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetRReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetRegionReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetSReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetTReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetUVReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSummaryTableReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareAssessment1H21ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareAssessment1H30ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareAssessment1R3ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareAssessment2R3ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareAssessment2ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareAssessment34H21ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareAssessment34H30ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareAssessment34R3ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareAssessment5R3ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareAssessment5ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareAssessment6R3ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareAssessment6ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareCheckTable1ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareCheckTable2ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareCheckTable3ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareCheckTable4ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareCheckTable5ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareCheckTable6ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareCheckTable7ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.CareCheckTableAllReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.HomeOverallSummaryH21ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.HomeOverallSummaryH30ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.HomeOverallSummaryR3ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.IssuesAnalysisAssessmentAllReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.IssuesAnalysisReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.IssuesAnalysisReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.LifeAssessment1FaceSheetH21ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.LifeAssessment1FaceSheetH30ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.LifeAssessment1FaceSheetR3ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.LifeAssessment23H21ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.LifeAssessment23H30ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.LifeAssessment23R3ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.LifeAssessment34H21ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.LifeAssessment34H30ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.LifeAssessment34R3ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.LifeAssessment5H21ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.LifeAssessment5H30ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.LifeAssessment5R3ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.OneDayScheduleR3ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetRegionReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.OneDayScheduleReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionAllHomeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.InfoCollectionAllFacilityReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.logic.AssessmentHomeReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.AssessmentSheetReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.CareCheckTableReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.InfoCollectionSheetFacilityReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.InfoCollectionSheetHomeReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.IssuesAnalysisReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.AssessmentSheetRegionReportLogic;
import jp.ndsoft.carebase.cmn.api.report.model.AssessmentSheetReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.CareCheckTableAllReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.HomeAssessmentSheetAllReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.InfoCollectionSheetReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.AssessmentSheetRegionReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.IssuesAnalysisReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssTypeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssTypeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnGdlAssRirekiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnGdlAssRirekiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdlRirekiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucRaiRrkSelectMapper;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * @since 2025.07.24
 * <AUTHOR> 張奇
 * @description U01700_課題分析（アセスメントの全てと一括印刷）の帳票出力
 */
@Service("IssuesAnalysisAssessmentAllReport")
public class IssuesAnalysisAssessmentAllReportService extends
        PdfReportServiceImpl<IssuesAnalysisReportParameterModel, IssuesAnalysisReportServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** アセスメント表（全て）情報取得 */
    @Autowired
    private AssessmentSheetReportLogic assessmentSheetReportLogic;

    /** ケアチェック表（全て）情報取得 */
    @Autowired
    private CareCheckTableReportLogic careCheckTableReportLogic;

    /** アセスメント(居宅)（全て）情報取得 */
    @Autowired
    private AssessmentHomeReportLogic assessmentHomeReportLogic;

    /** アセスメント種別取得 */
    @Autowired
    private CpnTucRaiRrkSelectMapper cpnTucRaiRrkSelectMapper;

    /** ＧＬ＿居宅アセスメント履歴情報取得 */
    @Autowired
    private CpnTucGdlRirekiSelectMapper cpnTucGdlRirekiSelectMapper;

    /** 課題分析情報取得 */
    @Autowired
    private IssuesAnalysisReportLogic issuesAnalysisReportLogic;

    // 情報収集シート(居宅)帳票出力ロジック
    @Autowired
    private InfoCollectionSheetHomeReportLogic infoCollectionSheetHomeReportLogic;

    // 情報収集シート(施設)帳票出力ロジック
    @Autowired
    private InfoCollectionSheetFacilityReportLogic infoCollectionSheetFacilityReportLogic;

    /** アセスメント表（領域）（全て）情報取得 */
    @Autowired
    private AssessmentSheetRegionReportLogic assessmentSheetRegionReportLogic;

    /*
     * ===============1.単項目チェック以外の入力チェック===============
     * 
     */
    // 特になし

    /**
     * 帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(IssuesAnalysisReportParameterModel model,
            IssuesAnalysisReportServiceOutDto outDto) throws Exception {
        LOG.info(Constants.START);

        // 課題分析（アセスメントの全てと一括印刷）
        IssuesAnalysisAssessmentAllReportServiceInDto allInDto = new IssuesAnalysisAssessmentAllReportServiceInDto();

        if (ReportConstants.CPN_FLG_5.equals(model.getCpnFlg())) {
            AssessmentSheetRegionReportServiceOutDto AssessmentSheetRegionOutDto = new AssessmentSheetRegionReportServiceOutDto();

            AssessmentSheetRegionReportParameterModel assessmentSheetRegionModel = new AssessmentSheetRegionReportParameterModel();

            // 【リクエストパラメータ】.印刷設定
            ReportCommonPrintSet printSet = new ReportCommonPrintSet();
            // 【リクエストパラメータ】.印刷オプション
            ReportAssessmentSheetRegionPrintOption printOption = new ReportAssessmentSheetRegionPrintOption();
            // 【リクエストパラメータ】.印刷対象履歴リスト
            List<ReportAssessmentSheetRegionPrintSubjectHistory> historyList = new ArrayList<ReportAssessmentSheetRegionPrintSubjectHistory>();
            ReportAssessmentSheetRegionPrintSubjectHistory history = new ReportAssessmentSheetRegionPrintSubjectHistory();
            List<ReportCommonChoPrt> reportCommonChoPrtList = new ArrayList<ReportCommonChoPrt>();
            ReportCommonChoPrt reportCommonChoPrt = new ReportCommonChoPrt();
            // 事業者情報
            assessmentSheetRegionModel.setJigyoInfo(model.getJigyoInfo());
            // 事業者名
            assessmentSheetRegionModel.setSvJigyoKnj(model.getSvJigyoKnj());
            // システムコード
            assessmentSheetRegionModel.setSyscd(model.getSyscd());

            // 印刷設定
            // 指定日印刷区分
            printSet.setShiTeiKubun(ReportConstants.PRINTDATE_KBN_PRINT);
            // 指定日
            printSet.setShiTeiDate(model.getPrintSet().getShiTeiDate());
            assessmentSheetRegionModel.setPrintSet(printSet);

            // 印刷オプション
            // 記入用シートを印刷するフラグ
            printOption.setEmptyFlg(model.getPrintOption().getEmptyFlg());
            // 選択履歴のデータのみを印刷する
            printOption.setSelectedOnlyFlg(CommonConstants.BLANK_STRING);
            // 選択項目を囲む色
            printOption.setRangeColor(CommonConstants.BLANK_STRING);
            // 履歴情報を印刷する
            printOption.setPrintHistoryFlg(CommonConstants.BLANK_STRING);
            // 説明文を印刷する
            printOption.setDescriptionPrintFlg(CommonConstants.BLANK_STRING);
            assessmentSheetRegionModel.setPrintOption(printOption);

            // 印刷対象履歴リスト
            // 期間ID
            history.setSc1Id(model.getAccessmentInfo().getSc1Id());
            // 利用者ID
            history.setUserId(model.getPrintSubjectHistoryList().get(0).getUserId());
            // ケース番号
            history.setCaseNo(CommonConstants.BLANK_STRING);
            // 改定フラグ
            history.setKaiteiFlg(model.getAccessmentInfo().getLparm());
            // アセスメント履歴ID
            history.setAssId(model.getAccessmentInfo().getRirekiId());
            historyList.add(history);

            // 職員ID
            reportCommonChoPrt
                    .setShokuId(model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getShokuId());
            // セクション
            reportCommonChoPrt
                    .setSection(model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection());
            reportCommonChoPrtList.add(reportCommonChoPrt);
            history.setChoPrtList(reportCommonChoPrtList);
            assessmentSheetRegionModel.setPrintSubjectHistoryList(historyList);

            // U0P650_アセスメント表（領域A）帳票用データ詳細
            assessmentSheetRegionModel.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0)
                    .setSection(CommonConstants.STR_1);
            AssessmentSheetRegionReportServiceInDto regionAInfoInDto = this.assessmentSheetRegionReportLogic
                    .getRegionReportParameters(assessmentSheetRegionModel,
                            AssessmentSheetRegionOutDto);
            List<AssessmentSheetRegionReportServiceInDto> regionAInfoList = new ArrayList<AssessmentSheetRegionReportServiceInDto>();
            regionAInfoList.add(regionAInfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceRegionA = new JRBeanCollectionDataSource(regionAInfoList);
            allInDto.setAssessmentSheetRegionAReport(dataSourceRegionA);

            // U0P650_アセスメント表（領域B）帳票用データ詳細
            assessmentSheetRegionModel.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0)
                    .setSection(CommonConstants.STR_2);
            AssessmentSheetRegionReportServiceInDto regionBInfoInDto = this.assessmentSheetRegionReportLogic
                    .getRegionReportParameters(assessmentSheetRegionModel,
                            AssessmentSheetRegionOutDto);
            List<AssessmentSheetRegionReportServiceInDto> regionBInfoList = new ArrayList<AssessmentSheetRegionReportServiceInDto>();
            regionBInfoList.add(regionBInfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceRegionB = new JRBeanCollectionDataSource(regionBInfoList);
            allInDto.setAssessmentSheetRegionBReport(dataSourceRegionB);

            // U0P650_アセスメント表（領域C）帳票用データ詳細
            assessmentSheetRegionModel.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0)
                    .setSection(CommonConstants.STR_3);
            AssessmentSheetRegionReportServiceInDto regionCInfoInDto = this.assessmentSheetRegionReportLogic
                    .getRegionReportParameters(assessmentSheetRegionModel,
                            AssessmentSheetRegionOutDto);
            List<AssessmentSheetRegionReportServiceInDto> regionCInfoList = new ArrayList<AssessmentSheetRegionReportServiceInDto>();
            regionCInfoList.add(regionCInfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceRegionC = new JRBeanCollectionDataSource(regionCInfoList);
            allInDto.setAssessmentSheetRegionCReport(dataSourceRegionC);

            // U0P650_アセスメント表（領域D）帳票用データ詳細
            assessmentSheetRegionModel.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0)
                    .setSection(CommonConstants.STR_4);
            AssessmentSheetRegionReportServiceInDto regionDInfoInDto = this.assessmentSheetRegionReportLogic
                    .getRegionReportParameters(assessmentSheetRegionModel,
                            AssessmentSheetRegionOutDto);
            List<AssessmentSheetRegionReportServiceInDto> regionDInfoList = new ArrayList<AssessmentSheetRegionReportServiceInDto>();
            regionDInfoList.add(regionDInfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceRegionD = new JRBeanCollectionDataSource(regionDInfoList);
            allInDto.setAssessmentSheetRegionDReport(dataSourceRegionD);

            // U0P650_アセスメント総括表 帳票用データ詳細
            AssessmentSummaryTableReportServiceInDto summaryTableInfoInDto = this.assessmentSheetRegionReportLogic
                    .getAssessmentSummaryTableParameters(assessmentSheetRegionModel, AssessmentSheetRegionOutDto);
            List<AssessmentSummaryTableReportServiceInDto> summaryTableInfoList = new ArrayList<AssessmentSummaryTableReportServiceInDto>();
            summaryTableInfoList.add(summaryTableInfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceSummaryTable = new JRBeanCollectionDataSource(summaryTableInfoList);
            allInDto.setAssessmentSummaryTableReport(dataSourceSummaryTable);

        }

        if (ReportConstants.CPN_FLG_8.equals(model.getCpnFlg())) {
            InfoCollectionSheetReportParameterModel infoCollectionSheetReportParameterModel = new InfoCollectionSheetReportParameterModel();

            // 【リクエストパラメータ】.印刷設定
            ReportCommonPrintSet printSet = new ReportCommonPrintSet();
            // 【リクエストパラメータ】.印刷オプション
            InfoCollectionReportCommonPrintOption printOption = new InfoCollectionReportCommonPrintOption();
            // 事業者情報
            infoCollectionSheetReportParameterModel.setJigyoInfo(model.getJigyoInfo());
            // 事業者名
            infoCollectionSheetReportParameterModel.setJigyoName(model.getSvJigyoKnj());
            // システム日付
            infoCollectionSheetReportParameterModel.setSystemDate(model.getSystemDate());

            // ヘッダID
            infoCollectionSheetReportParameterModel.setAss1Id(model.getAccessmentInfo().getRirekiId());
            // マスタ区分
            infoCollectionSheetReportParameterModel.setMstKbn(model.getAccessmentInfo().getLparm());
            // 改訂区分(0:初版、1:H31/1)
            infoCollectionSheetReportParameterModel.setKaiteiKbn(model.getAccessmentInfo().getArgI());
            // 第1階層ID
            infoCollectionSheetReportParameterModel.setLevel1Id(CommonConstants.STR_ZERO);

            // 印刷設定
            // 指定日印刷区分
            printSet.setShiTeiKubun(ReportConstants.PRINTDATE_KBN_PRINT);
            // 指定日
            printSet.setShiTeiDate(model.getPrintSet().getShiTeiDate());
            infoCollectionSheetReportParameterModel.setPrintSet(printSet);

            // 印刷オプション
            // 記入用シートを印刷するフラグ
            printOption.setEmptyFlg(model.getPrintOption().getEmptyFlg());
            // 敬称変更フラグ
            printOption.setKeishoFlg(model.getPrintOption().getKeishoFlg());
            // 敬称
            printOption.setKeisho(model.getPrintOption().getKeisho());
            // 注釈印刷フラグ
            printOption.setChushakuFlg(CommonConstants.BLANK_STRING);
            infoCollectionSheetReportParameterModel.setPrintOption(printOption);

            if (ReportConstants.STR_2.equals(model.getAccessmentInfo().getLparm())) {

                InfoCollectionAllHomeReportServiceInDto reportInfo = infoCollectionSheetHomeReportLogic
                        .getU06061AllReportParameters(infoCollectionSheetReportParameterModel);

                // ノート情報格納配列
                List<InfoCollectionAllHomeReportServiceInDto> infoCollectionAllReportInfoList = new ArrayList<InfoCollectionAllHomeReportServiceInDto>();
                infoCollectionAllReportInfoList.add(reportInfo);

                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(infoCollectionAllReportInfoList);
                allInDto.setInfoCollectionSheetHomeReport(dataSource);
            }
            if (ReportConstants.STR_1.equals(model.getAccessmentInfo().getLparm())) {

                InfoCollectionAllFacilityReportServiceInDto reportInfo = infoCollectionSheetFacilityReportLogic
                        .getU06060AllReportParameters(infoCollectionSheetReportParameterModel);

                // ノート情報格納配列
                List<InfoCollectionAllFacilityReportServiceInDto> infoCollectionAllReportInfoList = new ArrayList<InfoCollectionAllFacilityReportServiceInDto>();
                infoCollectionAllReportInfoList.add(reportInfo);

                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(infoCollectionAllReportInfoList);
                allInDto.setInfoCollectionSheetFacilityReport(dataSource);
            }
        }

        if (ReportConstants.CPN_FLG_2.equals(model.getCpnFlg())) {
            HomeAssessmentSheetAllReportParameterModel homeAssessmentSheetAllmodel = new HomeAssessmentSheetAllReportParameterModel();

            // 【リクエストパラメータ】.印刷設定
            ReportCommonPrintSet printSet = new ReportCommonPrintSet();
            // 【リクエストパラメータ】.印刷オプション
            ReportHomeAssessmentAllPrintOption printOption = new ReportHomeAssessmentAllPrintOption();
            // 【リクエストパラメータ】.印刷対象履歴リスト
            List<ReportHomeAssessmentAllPrintSubjectHistory> historyList = new ArrayList<ReportHomeAssessmentAllPrintSubjectHistory>();
            ReportHomeAssessmentAllPrintSubjectHistory history = new ReportHomeAssessmentAllPrintSubjectHistory();
            List<ReportCommonHomeAssessmentAllChoPrt> reportCommonChoPrtList = new ArrayList<ReportCommonHomeAssessmentAllChoPrt>();
            ReportCommonHomeAssessmentAllChoPrt reportCommonChoPrt = new ReportCommonHomeAssessmentAllChoPrt();
            // 事業者情報
            homeAssessmentSheetAllmodel.setJigyoInfo(model.getJigyoInfo());
            // 事業者名
            homeAssessmentSheetAllmodel.setSvJigyoKnj(model.getSvJigyoKnj());
            // システム日付
            homeAssessmentSheetAllmodel.setSystemDate(model.getSystemDate());
            // システムコード
            homeAssessmentSheetAllmodel.setSyscd(model.getSyscd());
            // ガイドラインまとめ
            homeAssessmentSheetAllmodel.setGdlMatomeFlg(model.getAccessmentInfo().getGdlMatomeFlg());

            // 印刷設定
            // 指定日印刷区分
            printSet.setShiTeiKubun(ReportConstants.PRINTDATE_KBN_PRINT);
            // 指定日
            printSet.setShiTeiDate(model.getPrintSet().getShiTeiDate());
            homeAssessmentSheetAllmodel.setPrintSet(printSet);

            // 印刷オプション
            // 記入用シートを印刷するフラグ
            printOption.setEmptyFlg(model.getPrintOption().getEmptyFlg());
            // 特記事項を別紙に印刷するチェックボックス
            printOption.setSpecialnoteAppendix(CommonConstants.BLANK_STRING);
            // 特記事項印刷線の高さチェックボックス
            printOption.setSpecialnoteLine(CommonConstants.BLANK_STRING);
            // 特記事項最小行入力
            printOption.setSpecialnoteLineHeight(CommonConstants.BLANK_STRING);
            // まとめ印刷線の高最小行チェックボックス
            printOption.setSummaryLine(CommonConstants.BLANK_STRING);
            // まとめ最小行入力
            printOption.setSummaryLineHeight(CommonConstants.BLANK_STRING);
            // (状況別)空白の項目も印刷するチェックボックス
            printOption.setBlankItem(CommonConstants.BLANK_STRING);
            homeAssessmentSheetAllmodel.setPrintOption(printOption);

            // 印刷対象履歴リスト
            // 期間ID
            history.setSc1Id(model.getAccessmentInfo().getSc1Id());
            // 利用者ID
            history.setUserId(model.getPrintSubjectHistoryList().get(0).getUserId());
            // 作成日
            history.setAsJisshiDateYmd(model.getPrintSubjectHistoryList().get(0).getAssDateYmd());
            // アセスメントID
            history.setGdlId(model.getAccessmentInfo().getRirekiId());

            historyList.add(history);

            // 職員ID
            reportCommonChoPrt
                    .setShokuId(model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getShokuId());
            // セクション
            reportCommonChoPrt
                    .setSection(model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection());
            // 帳票番号
            reportCommonChoPrt
                    .setPrtNo(model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getPrtNo());
            reportCommonChoPrtList.add(reportCommonChoPrt);
            history.setChoPrtList(reportCommonChoPrtList);
            homeAssessmentSheetAllmodel.setPrintSubjectHistoryList(historyList);

            String h30Kubun = ReportConstants.STR_0;
            if ("2018/04/01".compareTo(model.getAccessmentInfo().getSparm()) > 0) {
                h30Kubun = ReportConstants.STR_1;
            } else {
                h30Kubun = ReportConstants.STR_0;
            }
            // 改定フラグ
            String ninteiFormF = CommonConstants.BLANK_STRING;
            CpnGdlAssRirekiByCriteriaInEntity inEntity = new CpnGdlAssRirekiByCriteriaInEntity();
            inEntity.setAlSc1Id(CommonDtoUtil.strValToInt(model.getAccessmentInfo().getSc1Id()));
            List<CpnGdlAssRirekiOutEntity> CpnGdlAssRirekiOutEntities = cpnTucGdlRirekiSelectMapper
                    .findCpnGdlAssRirekiByCriteria(inEntity);
            if (CollectionUtils.isNotEmpty(CpnGdlAssRirekiOutEntities)) {
                ninteiFormF = CommonDtoUtil.objValToString(CpnGdlAssRirekiOutEntities.getFirst().getNinteiFormF());
            }
            if (ReportConstants.KAITEI_H21_FLG4.equals(ninteiFormF)
                    && ReportConstants.STR_0.equals(h30Kubun)) {
                // U06026_生活アセスメント(1)フェースシート_H21
                LifeAssessment1FaceSheetH21ReportServiceInDto lifeAssessment1infoInDto = this.assessmentHomeReportLogic
                        .getFaceSheetH21ReportParameters(homeAssessmentSheetAllmodel);
                List<LifeAssessment1FaceSheetH21ReportServiceInDto> lifeAssessment1infoList = new ArrayList<LifeAssessment1FaceSheetH21ReportServiceInDto>();
                lifeAssessment1infoList.add(lifeAssessment1infoInDto);
                // // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareLifeAssessment1 = new JRBeanCollectionDataSource(
                        lifeAssessment1infoList);
                allInDto.setLifeAssessment1FaceSheetH21Report(dataSourcecareLifeAssessment1);

                // U06026_生活アセスメント(2)(3)_H21
                LifeAssessment23H21ReportServiceInDto lifeAssessment23infoInDto = this.assessmentHomeReportLogic
                        .getLifeAssessment23H21ReportParameters(homeAssessmentSheetAllmodel);
                List<LifeAssessment23H21ReportServiceInDto> lifeAssessment23infoList = new ArrayList<LifeAssessment23H21ReportServiceInDto>();
                lifeAssessment23infoList.add(lifeAssessment23infoInDto);
                // // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareLifeAssessment23 = new JRBeanCollectionDataSource(
                        lifeAssessment23infoList);
                allInDto.setLifeAssessment23H21Report(dataSourcecareLifeAssessment23);

                // U06026_生活アセスメント(3)(4) _H21
                LifeAssessment34H21ReportServiceInDto lifeAssessment34infoInDto = this.assessmentHomeReportLogic
                        .getLifeAssessment34H21ReportParameters(homeAssessmentSheetAllmodel);
                List<LifeAssessment34H21ReportServiceInDto> lifeAssessment34infoList = new ArrayList<LifeAssessment34H21ReportServiceInDto>();
                lifeAssessment34infoList.add(lifeAssessment34infoInDto);
                // // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareLifeAssessment34 = new JRBeanCollectionDataSource(
                        lifeAssessment34infoList);
                allInDto.setLifeAssessment34H21Report(dataSourcecareLifeAssessment34);

                // U06026_生活アセスメント(5)_H21
                LifeAssessment5H21ReportServiceInDto lifeAssessment5infoInDto = this.assessmentHomeReportLogic
                        .getLifeAssessment5H21ReportParameters(homeAssessmentSheetAllmodel);
                List<LifeAssessment5H21ReportServiceInDto> lifeAssessment5infoList = new ArrayList<LifeAssessment5H21ReportServiceInDto>();
                lifeAssessment5infoList.add(lifeAssessment5infoInDto);
                // // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareLifeAssessment5 = new JRBeanCollectionDataSource(
                        lifeAssessment5infoList);
                allInDto.setLifeAssessment5H21Report(dataSourcecareLifeAssessment5);

                // U06026_ケアアセスメント(1)_H21
                CareAssessment1H21ReportServiceInDto careAssessment1infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment1H21ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment1H21ReportServiceInDto> careAssessment1infoList = new ArrayList<CareAssessment1H21ReportServiceInDto>();
                careAssessment1infoList.add(careAssessment1infoInDto);
                // // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment1 = new JRBeanCollectionDataSource(
                        careAssessment1infoList);
                allInDto.setCareAssessment1H21Report(dataSourcecareAssessment1);

                // U06026_ケアアセスメント(2)_H21
                CareAssessment2ReportServiceInDto careAssessment2infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment2H21ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment2ReportServiceInDto> careAssessment2infoList = new ArrayList<CareAssessment2ReportServiceInDto>();
                careAssessment2infoList.add(careAssessment2infoInDto);
                // // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment2 = new JRBeanCollectionDataSource(
                        careAssessment2infoList);
                allInDto.setCareAssessment2Report(dataSourcecareAssessment2);

                // U06026_ケアアセスメント(3)(4)_H21
                CareAssessment34H21ReportServiceInDto careAssessment34infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment34H21ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment34H21ReportServiceInDto> careAssessment34infoList = new ArrayList<CareAssessment34H21ReportServiceInDto>();
                careAssessment34infoList.add(careAssessment34infoInDto);
                // // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment34 = new JRBeanCollectionDataSource(
                        careAssessment34infoList);
                allInDto.setCareAssessment34H21Report(dataSourcecareAssessment34);

                // U06026_ケアアセスメント(5)_H21
                CareAssessment5ReportServiceInDto careAssessment5infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment5ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment5ReportServiceInDto> careAssessment5infoList = new ArrayList<CareAssessment5ReportServiceInDto>();
                careAssessment5infoList.add(careAssessment5infoInDto);
                // // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment5 = new JRBeanCollectionDataSource(
                        careAssessment5infoList);
                allInDto.setCareAssessment5Report(dataSourcecareAssessment5);

                // U06026_ケアアセスメント(6)_H21
                CareAssessment6ReportServiceInDto careAssessment6infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment6ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment6ReportServiceInDto> careAssessment6infoList = new ArrayList<CareAssessment6ReportServiceInDto>();
                careAssessment6infoList.add(careAssessment6infoInDto);
                // // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment6 = new JRBeanCollectionDataSource(
                        careAssessment6infoList);
                allInDto.setCareAssessment6Report(dataSourcecareAssessment6);

                // U06026_全体のまとめ_H21
                HomeOverallSummaryH21ReportServiceInDto homeOverallSummaryinfoInDto = this.assessmentHomeReportLogic
                        .getHomeOverallSummaryH21ReportParameters(homeAssessmentSheetAllmodel);
                List<HomeOverallSummaryH21ReportServiceInDto> homeOverallSummaryinfoList = new ArrayList<HomeOverallSummaryH21ReportServiceInDto>();
                homeOverallSummaryinfoList.add(homeOverallSummaryinfoInDto);
                // // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareHomeOverallSummary = new JRBeanCollectionDataSource(
                        homeOverallSummaryinfoList);
                allInDto.setHomeOverallSummaryH21Report(dataSourcecareHomeOverallSummary);

                // U06026_１日のスケジュール_H21 帳票用データ詳細
                OneDayScheduleReportServiceInDto oneDayinfoInDto = this.assessmentHomeReportLogic
                        .getOneDayScheduleReportParameters(homeAssessmentSheetAllmodel);
                List<OneDayScheduleReportServiceInDto> oneDayInfoList = new ArrayList<OneDayScheduleReportServiceInDto>();
                oneDayInfoList.add(oneDayinfoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourceOneDay = new JRBeanCollectionDataSource(oneDayInfoList);
                allInDto.setOneDayScheduleReport(dataSourceOneDay);

            }
            if (ReportConstants.KAITEI_H21_FLG4.equals(ninteiFormF)
                    && ReportConstants.STR_1.equals(h30Kubun)) {

                // U06028_生活アセスメント(1)フェースシート_H30
                LifeAssessment1FaceSheetH30ReportServiceInDto lifeAssessment1infoInDto = this.assessmentHomeReportLogic
                        .getFaceSheetH30ReportParameters(homeAssessmentSheetAllmodel);
                List<LifeAssessment1FaceSheetH30ReportServiceInDto> lifeAssessment1infoList = new ArrayList<LifeAssessment1FaceSheetH30ReportServiceInDto>();
                lifeAssessment1infoList.add(lifeAssessment1infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareLifeAssessment1 = new JRBeanCollectionDataSource(
                        lifeAssessment1infoList);
                allInDto.setLifeAssessment1FaceSheetH30Report(dataSourcecareLifeAssessment1);

                // U06028_生活アセスメント(2)(3)_H30
                LifeAssessment23H30ReportServiceInDto lifeAssessment23infoInDto = this.assessmentHomeReportLogic
                        .getLifeAssessment23H30ReportParameters(homeAssessmentSheetAllmodel);
                List<LifeAssessment23H30ReportServiceInDto> lifeAssessment23infoList = new ArrayList<LifeAssessment23H30ReportServiceInDto>();
                lifeAssessment23infoList.add(lifeAssessment23infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareLifeAssessment23 = new JRBeanCollectionDataSource(
                        lifeAssessment23infoList);
                allInDto.setLifeAssessment23H30Report(dataSourcecareLifeAssessment23);

                // U06028_生活アセスメント(3)(4) _H30
                LifeAssessment34H30ReportServiceInDto lifeAssessment34infoInDto = this.assessmentHomeReportLogic
                        .getLifeAssessment34H30ReportParameters(homeAssessmentSheetAllmodel);
                List<LifeAssessment34H30ReportServiceInDto> lifeAssessment34infoList = new ArrayList<LifeAssessment34H30ReportServiceInDto>();
                lifeAssessment34infoList.add(lifeAssessment34infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareLifeAssessment34 = new JRBeanCollectionDataSource(
                        lifeAssessment34infoList);
                allInDto.setLifeAssessment34H30Report(dataSourcecareLifeAssessment34);

                // U06028_生活アセスメント(5)_H30
                LifeAssessment5H30ReportServiceInDto lifeAssessment5infoInDto = this.assessmentHomeReportLogic
                        .getLifeAssessment5H30ReportParameters(homeAssessmentSheetAllmodel);
                List<LifeAssessment5H30ReportServiceInDto> lifeAssessment5infoList = new ArrayList<LifeAssessment5H30ReportServiceInDto>();
                lifeAssessment5infoList.add(lifeAssessment5infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareLifeAssessment5 = new JRBeanCollectionDataSource(
                        lifeAssessment5infoList);
                allInDto.setLifeAssessment5H30Report(dataSourcecareLifeAssessment5);
                // U06028_ケアアセスメント(1)_H30
                CareAssessment1H30ReportServiceInDto careAssessment1infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment1H30ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment1H30ReportServiceInDto> careAssessment1infoList = new ArrayList<CareAssessment1H30ReportServiceInDto>();
                careAssessment1infoList.add(careAssessment1infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment1 = new JRBeanCollectionDataSource(
                        careAssessment1infoList);
                allInDto.setCareAssessment1H30Report(dataSourcecareAssessment1);

                // U06028_ケアアセスメント(2)_H30
                CareAssessment2ReportServiceInDto careAssessment2infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment2H21ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment2ReportServiceInDto> careAssessment2infoList = new ArrayList<CareAssessment2ReportServiceInDto>();
                careAssessment2infoList.add(careAssessment2infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment2 = new JRBeanCollectionDataSource(
                        careAssessment2infoList);
                allInDto.setCareAssessment2Report(dataSourcecareAssessment2);

                // U06028_ケアアセスメント(3)(4)_H30
                CareAssessment34H30ReportServiceInDto careAssessment34infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment34H30ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment34H30ReportServiceInDto> careAssessment34infoList = new ArrayList<CareAssessment34H30ReportServiceInDto>();
                careAssessment34infoList.add(careAssessment34infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment34 = new JRBeanCollectionDataSource(
                        careAssessment34infoList);
                allInDto.setCareAssessment34H30Report(dataSourcecareAssessment34);

                // U06028_ケアアセスメント(5)_H30
                CareAssessment5ReportServiceInDto careAssessment5infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment5ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment5ReportServiceInDto> careAssessment5infoList = new ArrayList<CareAssessment5ReportServiceInDto>();
                careAssessment5infoList.add(careAssessment5infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment5 = new JRBeanCollectionDataSource(
                        careAssessment5infoList);
                allInDto.setCareAssessment5Report(dataSourcecareAssessment5);

                // U06028_ケアアセスメント(6)_H30
                CareAssessment6ReportServiceInDto careAssessment6infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment6ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment6ReportServiceInDto> careAssessment6infoList = new ArrayList<CareAssessment6ReportServiceInDto>();
                careAssessment6infoList.add(careAssessment6infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment6 = new JRBeanCollectionDataSource(
                        careAssessment6infoList);
                allInDto.setCareAssessment6Report(dataSourcecareAssessment6);

                // U06028_全体のまとめ_H30
                HomeOverallSummaryH30ReportServiceInDto homeOverallSummaryinfoInDto = this.assessmentHomeReportLogic
                        .getHomeOverallSummaryH30ReportParameters(homeAssessmentSheetAllmodel);
                List<HomeOverallSummaryH30ReportServiceInDto> homeOverallSummaryinfoList = new ArrayList<HomeOverallSummaryH30ReportServiceInDto>();
                homeOverallSummaryinfoList.add(homeOverallSummaryinfoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareHomeOverallSummary = new JRBeanCollectionDataSource(
                        homeOverallSummaryinfoList);
                allInDto.setHomeOverallSummaryH30Report(dataSourcecareHomeOverallSummary);

                // U06028_１日のスケジュール_H30 帳票用データ詳細
                OneDayScheduleReportServiceInDto oneDayinfoInDto = this.assessmentHomeReportLogic
                        .getOneDayScheduleReportParameters(homeAssessmentSheetAllmodel);
                List<OneDayScheduleReportServiceInDto> oneDayInfoList = new ArrayList<OneDayScheduleReportServiceInDto>();
                oneDayInfoList.add(oneDayinfoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourceOneDay = new JRBeanCollectionDataSource(oneDayInfoList);
                allInDto.setOneDayScheduleReport(dataSourceOneDay);
            }
            if (ReportConstants.KAITEI_R3_FLG5.equals(ninteiFormF)) {
                // U06200_生活アセスメント(1)フェースシート_R3
                LifeAssessment1FaceSheetR3ReportServiceInDto lifeAssessment1infoInDto = this.assessmentHomeReportLogic
                        .getFaceSheetR3ReportParameters(homeAssessmentSheetAllmodel);
                List<LifeAssessment1FaceSheetR3ReportServiceInDto> lifeAssessment1infoList = new ArrayList<LifeAssessment1FaceSheetR3ReportServiceInDto>();
                lifeAssessment1infoList.add(lifeAssessment1infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareLifeAssessment1 = new JRBeanCollectionDataSource(
                        lifeAssessment1infoList);
                allInDto.setLifeAssessment1FaceSheetR3Report(dataSourcecareLifeAssessment1);

                // U06200_生活アセスメント(2)(3)_R3
                LifeAssessment23R3ReportServiceInDto lifeAssessment23infoInDto = this.assessmentHomeReportLogic
                        .getLifeAssessment23R3ReportParameters(homeAssessmentSheetAllmodel);
                List<LifeAssessment23R3ReportServiceInDto> lifeAssessment23infoList = new ArrayList<LifeAssessment23R3ReportServiceInDto>();
                lifeAssessment23infoList.add(lifeAssessment23infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareLifeAssessment23 = new JRBeanCollectionDataSource(
                        lifeAssessment23infoList);
                allInDto.setLifeAssessment23R3Report(dataSourcecareLifeAssessment23);

                // U06200_生活アセスメント(3)(4) _R3
                LifeAssessment34R3ReportServiceInDto lifeAssessment34infoInDto = this.assessmentHomeReportLogic
                        .getLifeAssessment34R3ReportParameters(homeAssessmentSheetAllmodel);
                List<LifeAssessment34R3ReportServiceInDto> lifeAssessment34infoList = new ArrayList<LifeAssessment34R3ReportServiceInDto>();
                lifeAssessment34infoList.add(lifeAssessment34infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareLifeAssessment34 = new JRBeanCollectionDataSource(
                        lifeAssessment34infoList);
                allInDto.setLifeAssessment34R3Report(dataSourcecareLifeAssessment34);

                // U06200_生活アセスメント(5)_R3
                LifeAssessment5R3ReportServiceInDto lifeAssessment5infoInDto = this.assessmentHomeReportLogic
                        .getLifeAssessment5R3ReportParameters(homeAssessmentSheetAllmodel);
                List<LifeAssessment5R3ReportServiceInDto> lifeAssessment5infoList = new ArrayList<LifeAssessment5R3ReportServiceInDto>();
                lifeAssessment5infoList.add(lifeAssessment5infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareLifeAssessment5 = new JRBeanCollectionDataSource(
                        lifeAssessment5infoList);
                allInDto.setLifeAssessment5R3Report(dataSourcecareLifeAssessment5);

                // U06200_ケアアセスメント(1)_R3
                CareAssessment1R3ReportServiceInDto careAssessment1infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment1R3ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment1R3ReportServiceInDto> careAssessment1infoList = new ArrayList<CareAssessment1R3ReportServiceInDto>();
                careAssessment1infoList.add(careAssessment1infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment1 = new JRBeanCollectionDataSource(
                        careAssessment1infoList);
                allInDto.setCareAssessment1R3Report(dataSourcecareAssessment1);

                // U06200_ケアアセスメント(2)_R3
                CareAssessment2R3ReportServiceInDto careAssessment2infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment2R3ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment2R3ReportServiceInDto> careAssessment2infoList = new ArrayList<CareAssessment2R3ReportServiceInDto>();
                careAssessment2infoList.add(careAssessment2infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment2 = new JRBeanCollectionDataSource(
                        careAssessment2infoList);
                allInDto.setCareAssessment2R3Report(dataSourcecareAssessment2);

                // U06200_ケアアセスメント(3)(4)_R3
                CareAssessment34R3ReportServiceInDto careAssessment34infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment34R3ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment34R3ReportServiceInDto> careAssessment34infoList = new ArrayList<CareAssessment34R3ReportServiceInDto>();
                careAssessment34infoList.add(careAssessment34infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment34 = new JRBeanCollectionDataSource(
                        careAssessment34infoList);
                allInDto.setCareAssessment34R3Report(dataSourcecareAssessment34);

                // U06200_ケアアセスメント(5)_R3
                CareAssessment5R3ReportServiceInDto careAssessment5infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment5R3ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment5R3ReportServiceInDto> careAssessment5infoList = new ArrayList<CareAssessment5R3ReportServiceInDto>();
                careAssessment5infoList.add(careAssessment5infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment5 = new JRBeanCollectionDataSource(
                        careAssessment5infoList);
                allInDto.setCareAssessment5R3Report(dataSourcecareAssessment5);

                // U06200_ケアアセスメント(6)_R3
                CareAssessment6R3ReportServiceInDto careAssessment6infoInDto = this.assessmentHomeReportLogic
                        .getCareAssessment6R3ReportParameters(homeAssessmentSheetAllmodel);
                List<CareAssessment6R3ReportServiceInDto> careAssessment6infoList = new ArrayList<CareAssessment6R3ReportServiceInDto>();
                careAssessment6infoList.add(careAssessment6infoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareAssessment6 = new JRBeanCollectionDataSource(
                        careAssessment6infoList);
                allInDto.setCareAssessment6R3Report(dataSourcecareAssessment6);

                // U06200_全体のまとめ_R3
                HomeOverallSummaryR3ReportServiceInDto homeOverallSummaryinfoInDto = this.assessmentHomeReportLogic
                        .getHomeOverallSummaryR3ReportParameters(homeAssessmentSheetAllmodel);
                List<HomeOverallSummaryR3ReportServiceInDto> homeOverallSummaryinfoList = new ArrayList<HomeOverallSummaryR3ReportServiceInDto>();
                homeOverallSummaryinfoList.add(homeOverallSummaryinfoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourcecareHomeOverallSummary = new JRBeanCollectionDataSource(
                        homeOverallSummaryinfoList);
                allInDto.setHomeOverallSummaryR3Report(dataSourcecareHomeOverallSummary);

                // U06200_１日のスケジュール_R3 帳票用データ詳細
                OneDayScheduleR3ReportServiceInDto oneDayinfoInDto = this.assessmentHomeReportLogic
                        .getOneDayScheduleR3ReportParameters(homeAssessmentSheetAllmodel);
                List<OneDayScheduleR3ReportServiceInDto> oneDayInfoList = new ArrayList<OneDayScheduleR3ReportServiceInDto>();
                oneDayInfoList.add(oneDayinfoInDto);
                // 帳票用のDataSourceを設定する
                JRBeanCollectionDataSource dataSourceOneDay = new JRBeanCollectionDataSource(oneDayInfoList);
                allInDto.setOneDayScheduleR3Report(dataSourceOneDay);
            }
        }

        if (ReportConstants.CPN_FLG_1.equals(model.getCpnFlg())) {

            CareCheckTableAllReportServiceOutDto careCheckTableoutDto = new CareCheckTableAllReportServiceOutDto();

            CareCheckTableAllReportParameterModel careCheckTablemodel = new CareCheckTableAllReportParameterModel();

            // 【リクエストパラメータ】.印刷設定
            ReportCommonPrintSet printSet = new ReportCommonPrintSet();
            // 【リクエストパラメータ】.印刷オプション
            ReportCommonCareCheckPrintOption printOption = new ReportCommonCareCheckPrintOption();
            // 【リクエストパラメータ】.印刷対象履歴リスト
            List<ReportCommonCareCheckPrintSubjectHistory> historyList = new ArrayList<ReportCommonCareCheckPrintSubjectHistory>();
            ReportCommonCareCheckPrintSubjectHistory history = new ReportCommonCareCheckPrintSubjectHistory();
            List<ReportCommonChoPrt> reportCommonChoPrtList = new ArrayList<ReportCommonChoPrt>();
            ReportCommonChoPrt reportCommonChoPrt = new ReportCommonChoPrt();
            // 事業者情報
            careCheckTablemodel.setJigyoInfo(model.getJigyoInfo());
            // 事業者名
            careCheckTablemodel.setSvJigyoKnj(model.getSvJigyoKnj());
            // システム日付
            careCheckTablemodel.setSystemDate(model.getSystemDate());
            // システムコード
            careCheckTablemodel.setSyscd(model.getSyscd());

            // 印刷設定
            // 指定日印刷区分
            printSet.setShiTeiKubun(ReportConstants.PRINTDATE_KBN_PRINT);
            // 指定日
            printSet.setShiTeiDate(model.getPrintSet().getShiTeiDate());
            careCheckTablemodel.setPrintSet(printSet);

            // 印刷オプション
            // 印刷する要介護度
            printOption.setLevelOfCareRequired(CommonConstants.BLANK_STRING);
            // 記入用シートを印刷するフラグ
            printOption.setEmptyFlg(model.getPrintOption().getEmptyFlg());
            // 具体的内容と対応するケア項目の文字サイズ
            printOption.setLetterFontSize(CommonConstants.BLANK_STRING);
            careCheckTablemodel.setPrintOption(printOption);

            // 印刷対象履歴リスト
            history.setSc1Id(model.getAccessmentInfo().getSc1Id());
            history.setCc1Id(model.getAccessmentInfo().getRirekiId());
            history.setUserId(model.getPrintSubjectHistoryList().get(0).getUserId());
            // 記号意味（印刷）フラグ
            history.setCckKigoPrtFlg(model.getAccessmentInfo().getCckKigoPrtFlg());
            historyList.add(history);
            // 職員ID
            reportCommonChoPrt
                    .setShokuId(model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getShokuId());
            // セクション
            reportCommonChoPrt
                    .setSection(model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection());
            reportCommonChoPrtList.add(reportCommonChoPrt);
            history.setChoPrtList(reportCommonChoPrtList);
            careCheckTablemodel.setPrintSubjectHistoryList(historyList);

            // U06010_ケアチェック表１帳票用データ詳細
            List<ReportCommonCareCheckPrintSubjectHistory> printSubjectList = careCheckTablemodel
                    .getPrintSubjectHistoryList();
            ReportCommonCareCheckPrintSubjectHistory PrintSubjectHistory = printSubjectList.get(0);
            PrintSubjectHistory.setB1Cd(ReportConstants.B1_CD_1);
            printSubjectList.set(0, PrintSubjectHistory);
            careCheckTablemodel.setPrintSubjectHistoryList(printSubjectList);
            CareCheckTable1ReportServiceInDto careCheckTable1infoInDto = this.careCheckTableReportLogic
                    .getCareCheck1Parameters(
                            careCheckTablemodel,
                            careCheckTableoutDto);
            List<CareCheckTable1ReportServiceInDto> careCheckTable1InfoList = new ArrayList<CareCheckTable1ReportServiceInDto>();
            careCheckTable1InfoList.add(careCheckTable1infoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(careCheckTable1InfoList);
            allInDto.setCareCheckTable1Report(dataSource1);

            // U06010_ケアチェック表２帳票用データ詳細
            PrintSubjectHistory.setB1Cd(ReportConstants.B1_CD_2);
            printSubjectList.set(0, PrintSubjectHistory);
            careCheckTablemodel.setPrintSubjectHistoryList(printSubjectList);
            CareCheckTable2ReportServiceInDto careCheckTable2infoInDto = this.careCheckTableReportLogic
                    .getCareCheck2Parameters(
                            careCheckTablemodel,
                            careCheckTableoutDto);
            List<CareCheckTable2ReportServiceInDto> careCheckTable2InfoList = new ArrayList<CareCheckTable2ReportServiceInDto>();
            careCheckTable2InfoList.add(careCheckTable2infoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(careCheckTable2InfoList);
            allInDto.setCareCheckTable2Report(dataSource2);

            // U06010_ケアチェック表３帳票用データ詳細
            PrintSubjectHistory.setB1Cd(ReportConstants.B1_CD_3);
            printSubjectList.set(0, PrintSubjectHistory);
            careCheckTablemodel.setPrintSubjectHistoryList(printSubjectList);
            CareCheckTable3ReportServiceInDto careCheckTable3infoInDto = this.careCheckTableReportLogic
                    .getCareCheck3Parameters(
                            careCheckTablemodel,
                            careCheckTableoutDto);
            List<CareCheckTable3ReportServiceInDto> careCheckTable3InfoList = new ArrayList<CareCheckTable3ReportServiceInDto>();
            careCheckTable3InfoList.add(careCheckTable3infoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSource3 = new JRBeanCollectionDataSource(careCheckTable3InfoList);
            allInDto.setCareCheckTable3Report(dataSource3);

            // U06010_ケアチェック表４帳票用データ詳細
            PrintSubjectHistory.setB1Cd(ReportConstants.B1_CD_4);
            printSubjectList.set(0, PrintSubjectHistory);
            careCheckTablemodel.setPrintSubjectHistoryList(printSubjectList);
            CareCheckTable4ReportServiceInDto careCheckTable4infoInDto = this.careCheckTableReportLogic
                    .getCareCheck4Parameters(
                            careCheckTablemodel,
                            careCheckTableoutDto);
            List<CareCheckTable4ReportServiceInDto> careCheckTable4InfoList = new ArrayList<CareCheckTable4ReportServiceInDto>();
            careCheckTable4InfoList.add(careCheckTable4infoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSource4 = new JRBeanCollectionDataSource(careCheckTable4InfoList);
            allInDto.setCareCheckTable4Report(dataSource4);

            // U06010_ケアチェック表５帳票用データ詳細
            PrintSubjectHistory.setB1Cd(ReportConstants.B1_CD_5);
            printSubjectList.set(0, PrintSubjectHistory);
            careCheckTablemodel.setPrintSubjectHistoryList(printSubjectList);
            CareCheckTable5ReportServiceInDto careCheckTable5infoInDto = this.careCheckTableReportLogic
                    .getCareCheck5Parameters(
                            careCheckTablemodel,
                            careCheckTableoutDto);
            List<CareCheckTable5ReportServiceInDto> careCheckTable5InfoList = new ArrayList<CareCheckTable5ReportServiceInDto>();
            careCheckTable5InfoList.add(careCheckTable5infoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSource5 = new JRBeanCollectionDataSource(careCheckTable5InfoList);
            allInDto.setCareCheckTable5Report(dataSource5);

            // U06010_ケアチェック表６帳票用データ詳細
            PrintSubjectHistory.setB1Cd(ReportConstants.B1_CD_6);
            printSubjectList.set(0, PrintSubjectHistory);
            careCheckTablemodel.setPrintSubjectHistoryList(printSubjectList);
            CareCheckTable6ReportServiceInDto careCheckTable6infoInDto = this.careCheckTableReportLogic
                    .getCareCheck6Parameters(
                            careCheckTablemodel,
                            careCheckTableoutDto);
            List<CareCheckTable6ReportServiceInDto> careCheckTable6InfoList = new ArrayList<CareCheckTable6ReportServiceInDto>();
            careCheckTable6InfoList.add(careCheckTable6infoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSource6 = new JRBeanCollectionDataSource(careCheckTable6InfoList);
            allInDto.setCareCheckTable6Report(dataSource6);

            // U06010_ケアチェック表７帳票用データ詳細
            PrintSubjectHistory.setB1Cd(ReportConstants.B1_CD_7);
            printSubjectList.set(0, PrintSubjectHistory);
            careCheckTablemodel.setPrintSubjectHistoryList(printSubjectList);
            CareCheckTable7ReportServiceInDto careCheckTable7infoInDto = this.careCheckTableReportLogic
                    .getCareCheck7Parameters(
                            careCheckTablemodel,
                            careCheckTableoutDto);
            List<CareCheckTable7ReportServiceInDto> careCheckTable7InfoList = new ArrayList<CareCheckTable7ReportServiceInDto>();
            careCheckTable7InfoList.add(careCheckTable7infoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSource7 = new JRBeanCollectionDataSource(careCheckTable7InfoList);
            allInDto.setCareCheckTable7Report(dataSource7);
        }

        if (ReportConstants.CPN_FLG_9.equals(model.getCpnFlg())) {
            // アセスメントの全て出力Dto
            AssessmentSheetReportServiceOutDto AssessmentSheetOutDto = new AssessmentSheetReportServiceOutDto();

            // アセスメントの全てModel
            AssessmentSheetReportParameterModel assessmentSheetModel = new AssessmentSheetReportParameterModel();
            // 【リクエストパラメータ】.印刷設定
            ReportCommonPrintSet printSet = new ReportCommonPrintSet();
            // 【リクエストパラメータ】.印刷オプション
            ReportAssessmentSheetPrintOption printOption = new ReportAssessmentSheetPrintOption();
            // 【リクエストパラメータ】.印刷対象履歴リスト
            List<ReportCommonPrintSubjectHistory> historyList = new ArrayList<ReportCommonPrintSubjectHistory>();
            ReportCommonPrintSubjectHistory history = new ReportCommonPrintSubjectHistory();
            List<ReportCommonChoPrt> reportCommonChoPrtList = new ArrayList<ReportCommonChoPrt>();
            ReportCommonChoPrt reportCommonChoPrt = new ReportCommonChoPrt();
            // 事業者情報
            assessmentSheetModel.setJigyoInfo(model.getJigyoInfo());
            // 事業者名
            assessmentSheetModel.setSvJigyoKnj(model.getSvJigyoKnj());
            // 契約者ID
            assessmentSheetModel.setKeiyakushaId(model.getKeiyakushaId());
            // システム日付
            assessmentSheetModel.setSystemDate(model.getSystemDate());
            // システムコード
            assessmentSheetModel.setSyscd(model.getSyscd());

            // 印刷設定
            // 指定日印刷区分
            printSet.setShiTeiKubun(ReportConstants.PRINTDATE_KBN_PRINT);
            // 指定日
            printSet.setShiTeiDate(model.getPrintSet().getShiTeiDate());
            assessmentSheetModel.setPrintSet(printSet);

            // 印刷オプション
            // 記入用シートアセスメント種別
            printOption.setKinyuAssType(model.getAccessmentInfo().getAiRadio());
            // 記入用シートを印刷するフラグ
            printOption.setEmptyFlg(model.getPrintOption().getEmptyFlg());
            assessmentSheetModel.setPrintOption(printOption);

            // 印刷対象履歴リスト
            // アセスメントID
            history.setRaiId(model.getAccessmentInfo().getRirekiId());
            historyList.add(history);
            // 職員ID
            reportCommonChoPrt
                    .setShokuId(model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getShokuId());
            // セクション
            reportCommonChoPrt
                    .setSection(model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection());
            reportCommonChoPrtList.add(reportCommonChoPrt);
            history.setChoPrtList(reportCommonChoPrtList);
            assessmentSheetModel.setPrintSubjectHistoryList(historyList);

            // U06080_アセスメント表（Ａ）帳票用データ詳細
            AssessmentSheetAReportServiceInDto sheetAinfoInDto = this.assessmentSheetReportLogic
                    .getAReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetAReportServiceInDto> assessmentSheetAInfoList = new ArrayList<AssessmentSheetAReportServiceInDto>();
            assessmentSheetAInfoList.add(sheetAinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceA = new JRBeanCollectionDataSource(assessmentSheetAInfoList);
            allInDto.setAssessmentSheetAReport(dataSourceA);

            // U06080_アセスメント表（Ｂ）帳票用データ詳細
            AssessmentSheetBReportServiceInDto sheetBinfoInDto = this.assessmentSheetReportLogic
                    .getBReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetBReportServiceInDto> assessmentSheetBInfoList = new ArrayList<AssessmentSheetBReportServiceInDto>();
            assessmentSheetBInfoList.add(sheetBinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceB = new JRBeanCollectionDataSource(assessmentSheetBInfoList);
            allInDto.setAssessmentSheetBReport(dataSourceB);

            // U06080_アセスメント表（Ｃ）帳票用データ詳細
            AssessmentSheetCReportServiceInDto sheetCinfoInDto = this.assessmentSheetReportLogic
                    .getCReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetCReportServiceInDto> assessmentSheetCInfoList = new ArrayList<AssessmentSheetCReportServiceInDto>();
            assessmentSheetCInfoList.add(sheetCinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceC = new JRBeanCollectionDataSource(assessmentSheetCInfoList);
            allInDto.setAssessmentSheetCReport(dataSourceC);

            // U06080_アセスメント表（Ｄ）帳票用データ詳細
            AssessmentSheetDReportServiceInDto sheetDinfoInDto = this.assessmentSheetReportLogic
                    .getDReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetDReportServiceInDto> assessmentSheetDInfoList = new ArrayList<AssessmentSheetDReportServiceInDto>();
            assessmentSheetDInfoList.add(sheetDinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceD = new JRBeanCollectionDataSource(assessmentSheetDInfoList);
            allInDto.setAssessmentSheetDReport(dataSourceD);

            // U06080_アセスメント表（Ｅ）帳票用データ詳細
            AssessmentSheetEReportServiceInDto sheetEinfoInDto = this.assessmentSheetReportLogic
                    .getEReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetEReportServiceInDto> assessmentSheetEInfoList = new ArrayList<AssessmentSheetEReportServiceInDto>();
            assessmentSheetEInfoList.add(sheetEinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceE = new JRBeanCollectionDataSource(assessmentSheetEInfoList);
            allInDto.setAssessmentSheetEReport(dataSourceE);

            // U06080_アセスメント表（Ｆ）帳票用データ詳細
            AssessmentSheetFReportServiceInDto sheetFinfoInDto = this.assessmentSheetReportLogic
                    .getFReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetFReportServiceInDto> assessmentSheetFInfoList = new ArrayList<AssessmentSheetFReportServiceInDto>();
            assessmentSheetFInfoList.add(sheetFinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceF = new JRBeanCollectionDataSource(assessmentSheetFInfoList);
            allInDto.setAssessmentSheetFReport(dataSourceF);

            // U06080_アセスメント表（Ｇ）帳票用データ詳細
            AssessmentSheetGReportServiceInDto sheetGinfoInDto = this.assessmentSheetReportLogic
                    .getGReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetGReportServiceInDto> assessmentSheetGInfoList = new ArrayList<AssessmentSheetGReportServiceInDto>();
            assessmentSheetGInfoList.add(sheetGinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceG = new JRBeanCollectionDataSource(assessmentSheetGInfoList);
            allInDto.setAssessmentSheetGReport(dataSourceG);

            // U06080_アセスメント表（Ｈ）帳票用データ詳細
            AssessmentSheetHReportServiceInDto sheetHinfoInDto = this.assessmentSheetReportLogic
                    .getHReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetHReportServiceInDto> assessmentSheetHInfoList = new ArrayList<AssessmentSheetHReportServiceInDto>();
            assessmentSheetHInfoList.add(sheetHinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceH = new JRBeanCollectionDataSource(assessmentSheetHInfoList);
            allInDto.setAssessmentSheetHReport(dataSourceH);

            // U06080_アセスメント表（Ｉ）帳票用データ詳細
            AssessmentSheetIReportServiceInDto sheetIinfoInDto = this.assessmentSheetReportLogic
                    .getIReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetIReportServiceInDto> assessmentSheetIInfoList = new ArrayList<AssessmentSheetIReportServiceInDto>();
            assessmentSheetIInfoList.add(sheetIinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceI = new JRBeanCollectionDataSource(assessmentSheetIInfoList);
            allInDto.setAssessmentSheetIReport(dataSourceI);

            // U06080_アセスメント表（Ｊ）帳票用データ詳細
            AssessmentSheetJReportServiceInDto sheetJinfoInDto = this.assessmentSheetReportLogic
                    .getJReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetJReportServiceInDto> assessmentSheetJInfoList = new ArrayList<AssessmentSheetJReportServiceInDto>();
            assessmentSheetJInfoList.add(sheetJinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceJ = new JRBeanCollectionDataSource(assessmentSheetJInfoList);
            allInDto.setAssessmentSheetJReport(dataSourceJ);

            // U06080_アセスメント表（Ｋ）帳票用データ詳細
            AssessmentSheetKReportServiceInDto sheetKinfoInDto = this.assessmentSheetReportLogic
                    .getKReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetKReportServiceInDto> assessmentSheetKInfoList = new ArrayList<AssessmentSheetKReportServiceInDto>();
            assessmentSheetKInfoList.add(sheetKinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceK = new JRBeanCollectionDataSource(assessmentSheetKInfoList);
            allInDto.setAssessmentSheetKReport(dataSourceK);

            // U06080_アセスメント表（Ｌ）帳票用データ詳細
            AssessmentSheetLReportServiceInDto sheetLinfoInDto = this.assessmentSheetReportLogic
                    .getLReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetLReportServiceInDto> assessmentSheetLInfoList = new ArrayList<AssessmentSheetLReportServiceInDto>();
            assessmentSheetLInfoList.add(sheetLinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceL = new JRBeanCollectionDataSource(assessmentSheetLInfoList);
            allInDto.setAssessmentSheetLReport(dataSourceL);

            // U06080_アセスメント表（Ｍ）帳票用データ詳細
            AssessmentSheetMReportServiceInDto sheetMinfoInDto = this.assessmentSheetReportLogic
                    .getMReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetMReportServiceInDto> assessmentSheetMInfoList = new ArrayList<AssessmentSheetMReportServiceInDto>();
            assessmentSheetMInfoList.add(sheetMinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceM = new JRBeanCollectionDataSource(assessmentSheetMInfoList);
            allInDto.setAssessmentSheetMReport(dataSourceM);

            // U06080_アセスメント表（Ｎ）帳票用データ詳細
            AssessmentSheetNReportServiceInDto sheetNinfoInDto = this.assessmentSheetReportLogic
                    .getNReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetNReportServiceInDto> assessmentSheetNInfoList = new ArrayList<AssessmentSheetNReportServiceInDto>();
            assessmentSheetNInfoList.add(sheetNinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceN = new JRBeanCollectionDataSource(assessmentSheetNInfoList);
            allInDto.setAssessmentSheetNReport(dataSourceN);

            // U06080_アセスメント表（Ｏ）帳票用データ詳細
            AssessmentSheetOReportServiceInDto sheetOinfoInDto = this.assessmentSheetReportLogic
                    .getOReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetOReportServiceInDto> assessmentSheetOInfoList = new ArrayList<AssessmentSheetOReportServiceInDto>();
            assessmentSheetOInfoList.add(sheetOinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceO = new JRBeanCollectionDataSource(assessmentSheetOInfoList);
            allInDto.setAssessmentSheetOReport(dataSourceO);

            // U06080_アセスメント表（Ｐ）帳票用データ詳細
            AssessmentSheetPReportServiceInDto sheetPinfoInDto = this.assessmentSheetReportLogic
                    .getPReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetPReportServiceInDto> assessmentSheetPInfoList = new ArrayList<AssessmentSheetPReportServiceInDto>();
            assessmentSheetPInfoList.add(sheetPinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceP = new JRBeanCollectionDataSource(assessmentSheetPInfoList);
            allInDto.setAssessmentSheetPReport(dataSourceP);

            // U06080_アセスメント表（Ｑ）帳票用データ詳細
            AssessmentSheetQReportServiceInDto sheetQinfoInDto = this.assessmentSheetReportLogic
                    .getQReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetQReportServiceInDto> assessmentSheetQInfoList = new ArrayList<AssessmentSheetQReportServiceInDto>();
            assessmentSheetQInfoList.add(sheetQinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceQ = new JRBeanCollectionDataSource(assessmentSheetQInfoList);
            allInDto.setAssessmentSheetQReport(dataSourceQ);

            // U06080_アセスメント表（Ｒ）帳票用データ詳細
            AssessmentSheetRReportServiceInDto sheetRinfoInDto = this.assessmentSheetReportLogic
                    .getRReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetRReportServiceInDto> assessmentSheetRInfoList = new ArrayList<AssessmentSheetRReportServiceInDto>();
            assessmentSheetRInfoList.add(sheetRinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceR = new JRBeanCollectionDataSource(assessmentSheetRInfoList);
            allInDto.setAssessmentSheetRReport(dataSourceR);

            // U06080_アセスメント表（Ｓ）帳票用データ詳細
            AssessmentSheetSReportServiceInDto sheetSinfoInDto = this.assessmentSheetReportLogic
                    .getSReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetSReportServiceInDto> assessmentSheetSInfoList = new ArrayList<AssessmentSheetSReportServiceInDto>();
            assessmentSheetSInfoList.add(sheetSinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceS = new JRBeanCollectionDataSource(assessmentSheetSInfoList);
            allInDto.setAssessmentSheetSReport(dataSourceS);

            // U06080_アセスメント表（Ｔ）帳票用データ詳細
            AssessmentSheetTReportServiceInDto sheetTinfoInDto = this.assessmentSheetReportLogic
                    .getTReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetTReportServiceInDto> assessmentSheetTInfoList = new ArrayList<AssessmentSheetTReportServiceInDto>();
            assessmentSheetTInfoList.add(sheetTinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceT = new JRBeanCollectionDataSource(assessmentSheetTInfoList);
            allInDto.setAssessmentSheetTReport(dataSourceT);

            // U06080_アセスメント表（ＵＶ）帳票用データ詳細
            AssessmentSheetUVReportServiceInDto sheetUVinfoInDto = this.assessmentSheetReportLogic
                    .getUVReportParameters(assessmentSheetModel, AssessmentSheetOutDto);
            List<AssessmentSheetUVReportServiceInDto> assessmentSheetUVInfoList = new ArrayList<AssessmentSheetUVReportServiceInDto>();
            assessmentSheetUVInfoList.add(sheetUVinfoInDto);
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSourceUV = new JRBeanCollectionDataSource(
                    assessmentSheetUVInfoList);
            allInDto.setAssessmentSheetUVReport(dataSourceUV);
        }
        // U01700_課題分析（アセスメントの全てと一括印刷）帳票用データ詳細
        IssuesAnalysisReportServiceInDto issuesAnalysisInDto = this.issuesAnalysisReportLogic
                .getIssuesAnalysisReportParameters(model, outDto);
        List<IssuesAnalysisReportServiceInDto> issuesAnalysisInfoList = new ArrayList<IssuesAnalysisReportServiceInDto>();
        issuesAnalysisInfoList.add(issuesAnalysisInDto);
        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSourceissuesAnalysis = new JRBeanCollectionDataSource(
                issuesAnalysisInfoList);
        allInDto.setIssuesAnalysisReport(dataSourceissuesAnalysis);

        LOG.info(Constants.END);
        return allInDto;
    }

    /**
     * 帳票出力
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final IssuesAnalysisReportParameterModel model,
            final IssuesAnalysisReportServiceOutDto outDto)
            throws Exception {
        LOG.info(Constants.START);

        List<JasperPrint> list = new ArrayList<>();
        List<JasperPrint> list2 = new ArrayList<>();

        // 帳票に出力するデータ群の取得
        final IssuesAnalysisAssessmentAllReportServiceInDto reportParameter = (IssuesAnalysisAssessmentAllReportServiceInDto) getReportParameters(
                model, outDto);

        if (ReportConstants.CPN_FLG_5.equals(model.getCpnFlg())) {
            // U0P650_アセスメント表（領域A）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceRegionA = reportParameter.getAssessmentSheetRegionAReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isRegionA = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.U0P650_AssessmentSheetRegionA)));
            // コンパイル
            final JasperReport jasperFileRegionA = JasperCompileManager.compileReport(isRegionA);
            // 帳票出力
            final JasperPrint jasperPrintRegionA = JasperFillManager.fillReport(jasperFileRegionA,
                    reportParameter.getParameters(),
                    dataSourceRegionA);
            list2.add(jasperPrintRegionA);

            // U0P650_アセスメント表（領域B）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceRegionB = reportParameter.getAssessmentSheetRegionBReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isRegionB = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.U0P650_AssessmentSheetRegionB)));
            // コンパイル
            final JasperReport jasperFileRegionB = JasperCompileManager.compileReport(isRegionB);
            // 帳票出力
            final JasperPrint jasperPrintRegionB = JasperFillManager.fillReport(jasperFileRegionB,
                    reportParameter.getParameters(),
                    dataSourceRegionB);
            list2.add(jasperPrintRegionB);

            // U0P650_アセスメント表（領域C）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceRegionC = reportParameter.getAssessmentSheetRegionCReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isRegionC = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.U0P650_AssessmentSheetRegionC)));
            // コンパイル
            final JasperReport jasperFileRegionC = JasperCompileManager.compileReport(isRegionC);
            // 帳票出力
            final JasperPrint jasperPrintRegionC = JasperFillManager.fillReport(jasperFileRegionC,
                    reportParameter.getParameters(),
                    dataSourceRegionC);
            list2.add(jasperPrintRegionC);

            // U0P650_アセスメント表（領域D）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceRegionD = reportParameter.getAssessmentSheetRegionDReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isRegionD = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.U0P650_AssessmentSheetRegionD)));
            // コンパイル
            final JasperReport jasperFileRegionD = JasperCompileManager.compileReport(isRegionD);
            // 帳票出力
            final JasperPrint jasperPrintRegionD = JasperFillManager.fillReport(jasperFileRegionD,
                    reportParameter.getParameters(),
                    dataSourceRegionD);
            list2.add(jasperPrintRegionD);

            // U0P650_アセスメント総括表
            // 報告先分の帳票データを2PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceSummaryTable = reportParameter.getAssessmentSummaryTableReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isSummaryTable = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_ASSESSMENT_SUMMARY_TABLE)));
            // コンパイル
            final JasperReport jasperFileSummaryTable = JasperCompileManager.compileReport(isSummaryTable);
            // 帳票出力
            final JasperPrint jasperPrintSummaryTable = JasperFillManager.fillReport(jasperFileSummaryTable,
                    reportParameter.getParameters(),
                    dataSourceSummaryTable);
            list2.add(jasperPrintSummaryTable);

            JasperPrint reslut1 = JasperUtil.concatReports(list2);

            // PDFファイルの出力
            final File pdf1 = JasperUtil.outputPdf(model, reslut1);

            /*
             * ===============6.レスポンスを返却===============
             * 
             */
            super.setFilepath(model, outDto, pdf1, pdf1.getName());
        }

        if (ReportConstants.CPN_FLG_8.equals(model.getCpnFlg())) {

            if (ReportConstants.STR_2.equals(model.getAccessmentInfo().getLparm())) {
                // 報告先分の帳票データを1PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceInfoCollectionSheetHome = reportParameter
                        .getInfoCollectionSheetHomeReport();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isInfoCollectionSheetHome = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_INFOCOLLECTION_HOME_ALL)));
                // コンパイル
                final JasperReport jasperFile = JasperCompileManager.compileReport(isInfoCollectionSheetHome);

                // 帳票出力
                final JasperPrint jasperPrintInfoCollectionSheetHome = JasperFillManager.fillReport(jasperFile,
                        reportParameter.getParameters(),
                        dataSourceInfoCollectionSheetHome);
                list.add(jasperPrintInfoCollectionSheetHome);
            }
            if (ReportConstants.STR_1.equals(model.getAccessmentInfo().getLparm())) {
                // 報告先分の帳票データを1PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceInfoCollectionSheetFacility = reportParameter
                        .getInfoCollectionSheetFacilityReport();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isInfoCollectionSheetFacility = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(),
                                ReportConstants.JRXML_INFOCOLLECTION_FACILITY_ALL)));
                // コンパイル
                final JasperReport jasperFileInfoCollectionSheetFacility = JasperCompileManager
                        .compileReport(isInfoCollectionSheetFacility);
                // 帳票出力
                final JasperPrint jasperPrintInfoCollectionSheetFacility = JasperFillManager.fillReport(
                        jasperFileInfoCollectionSheetFacility,
                        reportParameter.getParameters(),
                        dataSourceInfoCollectionSheetFacility);
                list.add(jasperPrintInfoCollectionSheetFacility);
            }
        }

        if (ReportConstants.CPN_FLG_2.equals(model.getCpnFlg())) {
            String h30Kubun = ReportConstants.STR_0;
            if ("2018/04/01".compareTo(model.getAccessmentInfo().getSparm()) > 0) {
                h30Kubun = ReportConstants.STR_1;
            } else {
                h30Kubun = ReportConstants.STR_0;
            }
            // 改定フラグ
            String ninteiFormF = CommonConstants.BLANK_STRING;
            CpnGdlAssRirekiByCriteriaInEntity inEntity = new CpnGdlAssRirekiByCriteriaInEntity();
            inEntity.setAlSc1Id(CommonDtoUtil.strValToInt(model.getAccessmentInfo().getSc1Id()));
            List<CpnGdlAssRirekiOutEntity> CpnGdlAssRirekiOutEntities = cpnTucGdlRirekiSelectMapper
                    .findCpnGdlAssRirekiByCriteria(inEntity);
            if (CollectionUtils.isNotEmpty(CpnGdlAssRirekiOutEntities)) {
                ninteiFormF = CommonDtoUtil.objValToString(CpnGdlAssRirekiOutEntities.getFirst().getNinteiFormF());
            }
            if (ReportConstants.KAITEI_H21_FLG4.equals(ninteiFormF)
                    && ReportConstants.STR_0.equals(h30Kubun)) {
                // U06026_生活アセスメント(1)フェースシート_H21
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceLifeAssessment1 = reportParameter
                        .getLifeAssessment1FaceSheetH21Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isLifeAssessment1 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(),
                                ReportConstants.JRXML_LIFEASSESSMENT1_FACESHEET_H21)));
                // コンパイル
                final JasperReport jasperFileLifeAssessment1 = JasperCompileManager.compileReport(isLifeAssessment1);
                // 帳票出力
                final JasperPrint jasperPrintLifeAssessment1 = JasperFillManager.fillReport(jasperFileLifeAssessment1,
                        reportParameter.getParameters(),
                        dataSourceLifeAssessment1);
                list.add(jasperPrintLifeAssessment1);
                // U06026_生活アセスメント(2)(3)_H21
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceLifeAssessment23 = reportParameter
                        .getLifeAssessment23H21Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isLifeAssessment23 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_LIFE_ASSESSMENT23_H21)));
                // コンパイル
                final JasperReport jasperFileLifeAssessment23 = JasperCompileManager.compileReport(isLifeAssessment23);
                // 帳票出力
                final JasperPrint jasperPrintLifeAssessment23 = JasperFillManager.fillReport(jasperFileLifeAssessment23,
                        reportParameter.getParameters(),
                        dataSourceLifeAssessment23);
                list.add(jasperPrintLifeAssessment23);
                // U06026_生活アセスメント(3)(4)_H21
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceLifeAssessment34 = reportParameter
                        .getLifeAssessment34H21Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isLifeAssessment34 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_LIFE_ASSESSMENT34_H21)));
                // コンパイル
                final JasperReport jasperFileLifeAssessment34 = JasperCompileManager.compileReport(isLifeAssessment34);
                // 帳票出力
                final JasperPrint jasperPrintLifeAssessment34 = JasperFillManager.fillReport(jasperFileLifeAssessment34,
                        reportParameter.getParameters(),
                        dataSourceLifeAssessment34);
                list.add(jasperPrintLifeAssessment34);
                // U06026_生活アセスメント(5)_H21
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceLifeAssessment5 = reportParameter
                        .getLifeAssessment5H21Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isLifeAssessment5 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_LIFE_ASSESSMENT5_H21)));
                // コンパイル
                final JasperReport jasperFileLifeAssessment5 = JasperCompileManager.compileReport(isLifeAssessment5);
                // 帳票出力
                final JasperPrint jasperPrintLifeAssessment5 = JasperFillManager.fillReport(jasperFileLifeAssessment5,
                        reportParameter.getParameters(),
                        dataSourceLifeAssessment5);
                list.add(jasperPrintLifeAssessment5);
                // U06026_ケアアセスメント(1)_H21
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment1 = reportParameter
                        .getCareAssessment1H21Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment1 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT1_H21)));
                // コンパイル
                final JasperReport jasperFileCareAssessment1 = JasperCompileManager.compileReport(isCareAssessment1);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment1 = JasperFillManager.fillReport(jasperFileCareAssessment1,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment1);
                list.add(jasperPrintCareAssessment1);
                // U06026_ケアアセスメント(2)_H21
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment2 = reportParameter.getCareAssessment2Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment2 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT2_H21)));
                // コンパイル
                final JasperReport jasperFileCareAssessment2 = JasperCompileManager.compileReport(isCareAssessment2);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment2 = JasperFillManager.fillReport(jasperFileCareAssessment2,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment2);
                list.add(jasperPrintCareAssessment2);
                // U06026_ケアアセスメント(3)(4)_H21
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment34 = reportParameter
                        .getCareAssessment34H21Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment34 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT34_H21)));
                // コンパイル
                final JasperReport jasperFileCareAssessment34 = JasperCompileManager.compileReport(isCareAssessment34);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment34 = JasperFillManager.fillReport(jasperFileCareAssessment34,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment34);
                list.add(jasperPrintCareAssessment34);
                // U06026_ケアアセスメント(5)_H21
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment5 = reportParameter.getCareAssessment5Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment5 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT5)));
                // コンパイル
                final JasperReport jasperFileCareAssessment5 = JasperCompileManager.compileReport(isCareAssessment5);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment5 = JasperFillManager.fillReport(jasperFileCareAssessment5,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment5);
                list.add(jasperPrintCareAssessment5);
                // U06026_ケアアセスメント(6)_H21
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment6 = reportParameter.getCareAssessment6Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment6 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT6)));
                // コンパイル
                final JasperReport jasperFileCareAssessment6 = JasperCompileManager.compileReport(isCareAssessment6);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment6 = JasperFillManager.fillReport(jasperFileCareAssessment6,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment6);
                list.add(jasperPrintCareAssessment6);

                // U06026_全体のまとめ_H21
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceHomeOverallSummary = reportParameter
                        .getHomeOverallSummaryH21Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isHomeOverallSummary = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(),
                                ReportConstants.JRXML_HOME_OVERALL_SUMMARY_H21_2)));
                if (ReportConstants.GDL_MATOME_FLG_1.equals(model.getAccessmentInfo().getGdlMatomeFlg())) {
                    isHomeOverallSummary = new FileInputStream(
                            (ReportUtil.getReportJrxmlFile(getFwProps(),
                                    ReportConstants.JRXML_HOME_OVERALL_SUMMARY_H21_1)));
                }
                // コンパイル
                final JasperReport jasperFileHomeOverallSummary = JasperCompileManager
                        .compileReport(isHomeOverallSummary);
                // 帳票出力
                final JasperPrint jasperHomeOverallSummary = JasperFillManager.fillReport(jasperFileHomeOverallSummary,
                        reportParameter.getParameters(),
                        dataSourceHomeOverallSummary);
                list.add(jasperHomeOverallSummary);

                // U06026_１日のスケジュール_H21
                // 報告先分の帳票データを1PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceOneDay = reportParameter.getOneDayScheduleReport();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isOneDay = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_DAILY_SCHEDULE_H21)));
                // コンパイル
                final JasperReport jasperFileOneDay = JasperCompileManager.compileReport(isOneDay);
                // 帳票出力
                final JasperPrint jasperPrintOneDay = JasperFillManager.fillReport(jasperFileOneDay,
                        reportParameter.getParameters(),
                        dataSourceOneDay);
                list.add(jasperPrintOneDay);
            }
            if (ReportConstants.KAITEI_H21_FLG4.equals(ninteiFormF)
                    && ReportConstants.STR_1.equals(h30Kubun)) {

                // U06028_生活アセスメント(1)フェースシート_H30
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceLifeAssessment1 = reportParameter
                        .getLifeAssessment1FaceSheetH30Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isLifeAssessment1 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(),
                                ReportConstants.JRXML_LIFEASSESSMENT1_FACESHEET_H30)));
                // コンパイル
                final JasperReport jasperFileLifeAssessment1 = JasperCompileManager.compileReport(isLifeAssessment1);
                // 帳票出力
                final JasperPrint jasperPrintLifeAssessment1 = JasperFillManager.fillReport(jasperFileLifeAssessment1,
                        reportParameter.getParameters(),
                        dataSourceLifeAssessment1);
                list.add(jasperPrintLifeAssessment1);

                // U06028_生活アセスメント(2)(3)_H30
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceLifeAssessment23 = reportParameter
                        .getLifeAssessment23H30Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isLifeAssessment23 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_LIFE_ASSESSMENT23_H30)));
                // コンパイル
                final JasperReport jasperFileLifeAssessment23 = JasperCompileManager.compileReport(isLifeAssessment23);
                // 帳票出力
                final JasperPrint jasperPrintLifeAssessment23 = JasperFillManager.fillReport(jasperFileLifeAssessment23,
                        reportParameter.getParameters(),
                        dataSourceLifeAssessment23);
                list.add(jasperPrintLifeAssessment23);

                // U06028_生活アセスメント(3)(4)_H30
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceLifeAssessment34 = reportParameter
                        .getLifeAssessment34H30Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isLifeAssessment34 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_LIFE_ASSESSMENT34_H30)));
                // コンパイル
                final JasperReport jasperFileLifeAssessment34 = JasperCompileManager.compileReport(isLifeAssessment34);
                // 帳票出力
                final JasperPrint jasperPrintLifeAssessment34 = JasperFillManager.fillReport(jasperFileLifeAssessment34,
                        reportParameter.getParameters(),
                        dataSourceLifeAssessment34);
                list.add(jasperPrintLifeAssessment34);

                // U06028_生活アセスメント(5)_H30
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceLifeAssessment5 = reportParameter
                        .getLifeAssessment5H30Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isLifeAssessment5 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_LIFE_ASSESSMENT5_H30)));
                // コンパイル
                final JasperReport jasperFileLifeAssessment5 = JasperCompileManager.compileReport(isLifeAssessment5);
                // 帳票出力
                final JasperPrint jasperPrintLifeAssessment5 = JasperFillManager.fillReport(jasperFileLifeAssessment5,
                        reportParameter.getParameters(),
                        dataSourceLifeAssessment5);
                list.add(jasperPrintLifeAssessment5);

                // U06028_ケアアセスメント(1)_H30
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment1 = reportParameter
                        .getCareAssessment1H30Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment1 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT1_H30)));
                // コンパイル
                final JasperReport jasperFileCareAssessment1 = JasperCompileManager.compileReport(isCareAssessment1);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment1 = JasperFillManager.fillReport(jasperFileCareAssessment1,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment1);
                list.add(jasperPrintCareAssessment1);

                // U06028_ケアアセスメント(2)_H30
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment2 = reportParameter.getCareAssessment2Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment2 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT2_H21)));
                // コンパイル
                final JasperReport jasperFileCareAssessment2 = JasperCompileManager.compileReport(isCareAssessment2);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment2 = JasperFillManager.fillReport(jasperFileCareAssessment2,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment2);
                list.add(jasperPrintCareAssessment2);

                // U06028_ケアアセスメント(3)(4)_H30
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment34 = reportParameter
                        .getCareAssessment34H30Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment34 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT34_H30)));
                // コンパイル
                final JasperReport jasperFileCareAssessment34 = JasperCompileManager.compileReport(isCareAssessment34);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment34 = JasperFillManager.fillReport(jasperFileCareAssessment34,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment34);
                list.add(jasperPrintCareAssessment34);

                // U06028_ケアアセスメント(5)_H30
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment5 = reportParameter.getCareAssessment5Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment5 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT5)));
                // コンパイル
                final JasperReport jasperFileCareAssessment5 = JasperCompileManager.compileReport(isCareAssessment5);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment5 = JasperFillManager.fillReport(jasperFileCareAssessment5,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment5);
                list.add(jasperPrintCareAssessment5);

                // U06028_ケアアセスメント(6)_H30
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment6 = reportParameter.getCareAssessment6Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment6 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT6)));
                // コンパイル
                final JasperReport jasperFileCareAssessment6 = JasperCompileManager.compileReport(isCareAssessment6);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment6 = JasperFillManager.fillReport(jasperFileCareAssessment6,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment6);
                list.add(jasperPrintCareAssessment6);

                // U06028_全体のまとめ_H30
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceHomeOverallSummary = reportParameter
                        .getHomeOverallSummaryH30Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isHomeOverallSummary = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(),
                                ReportConstants.JRXML_HOME_OVERALL_SUMMARY_H30_2)));
                if (ReportConstants.GDL_MATOME_FLG_1.equals(model.getAccessmentInfo().getGdlMatomeFlg())) {
                    isHomeOverallSummary = new FileInputStream(
                            (ReportUtil.getReportJrxmlFile(getFwProps(),
                                    ReportConstants.JRXML_HOME_OVERALL_SUMMARY_H30_1)));
                }
                // コンパイル
                final JasperReport jasperFileHomeOverallSummary = JasperCompileManager
                        .compileReport(isHomeOverallSummary);
                // 帳票出力
                final JasperPrint jasperHomeOverallSummary = JasperFillManager.fillReport(jasperFileHomeOverallSummary,
                        reportParameter.getParameters(),
                        dataSourceHomeOverallSummary);
                list.add(jasperHomeOverallSummary);

                // U06028_１日のスケジュール_H30
                // 報告先分の帳票データを1PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceOneDay = reportParameter.getOneDayScheduleReport();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isOneDay = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_DAILY_SCHEDULE_H30)));
                // コンパイル
                final JasperReport jasperFileOneDay = JasperCompileManager.compileReport(isOneDay);
                // 帳票出力
                final JasperPrint jasperPrintOneDay = JasperFillManager.fillReport(jasperFileOneDay,
                        reportParameter.getParameters(),
                        dataSourceOneDay);
                list.add(jasperPrintOneDay);
            }

            if (ReportConstants.KAITEI_R3_FLG5.equals(ninteiFormF)) {

                // U06200_生活アセスメント(1)フェースシート_R3
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceLifeAssessment1 = reportParameter
                        .getLifeAssessment1FaceSheetR3Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isLifeAssessment1 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(),
                                ReportConstants.JRXML_LIFEASSESSMENT1_FACESHEET_R3)));
                // コンパイル
                final JasperReport jasperFileLifeAssessment1 = JasperCompileManager.compileReport(isLifeAssessment1);
                // 帳票出力
                final JasperPrint jasperPrintLifeAssessment1 = JasperFillManager.fillReport(jasperFileLifeAssessment1,
                        reportParameter.getParameters(),
                        dataSourceLifeAssessment1);
                list.add(jasperPrintLifeAssessment1);
                // U06200_生活アセスメント(2)(3)_R3
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceLifeAssessment23 = reportParameter
                        .getLifeAssessment23R3Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isLifeAssessment23 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_LIFE_ASSESSMENT23_R3)));
                // コンパイル
                final JasperReport jasperFileLifeAssessment23 = JasperCompileManager.compileReport(isLifeAssessment23);
                // 帳票出力
                final JasperPrint jasperPrintLifeAssessment23 = JasperFillManager.fillReport(jasperFileLifeAssessment23,
                        reportParameter.getParameters(),
                        dataSourceLifeAssessment23);
                list.add(jasperPrintLifeAssessment23);
                // U06200_生活アセスメント(3)(4)_R3
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceLifeAssessment34 = reportParameter
                        .getLifeAssessment34R3Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isLifeAssessment34 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_LIFE_ASSESSMENT34_R3)));
                // コンパイル
                final JasperReport jasperFileLifeAssessment34 = JasperCompileManager.compileReport(isLifeAssessment34);
                // 帳票出力
                final JasperPrint jasperPrintLifeAssessment34 = JasperFillManager.fillReport(jasperFileLifeAssessment34,
                        reportParameter.getParameters(),
                        dataSourceLifeAssessment34);
                list.add(jasperPrintLifeAssessment34);
                // U06200_生活アセスメント(5)_R3
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceLifeAssessment5 = reportParameter
                        .getLifeAssessment5R3Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isLifeAssessment5 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_LIFE_ASSESSMENT5_R3)));
                // コンパイル
                final JasperReport jasperFileLifeAssessment5 = JasperCompileManager.compileReport(isLifeAssessment5);
                // 帳票出力
                final JasperPrint jasperPrintLifeAssessment5 = JasperFillManager.fillReport(jasperFileLifeAssessment5,
                        reportParameter.getParameters(),
                        dataSourceLifeAssessment5);
                list.add(jasperPrintLifeAssessment5);

                // U06200_ケアアセスメント(1)_R3
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment1 = reportParameter
                        .getCareAssessment1R3Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment1 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT1_R3)));
                // コンパイル
                final JasperReport jasperFileCareAssessment1 = JasperCompileManager.compileReport(isCareAssessment1);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment1 = JasperFillManager.fillReport(jasperFileCareAssessment1,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment1);
                list.add(jasperPrintCareAssessment1);
                // U06200_ケアアセスメント(2)_R3
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment2 = reportParameter
                        .getCareAssessment2R3Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment2 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT2_R3)));
                // コンパイル
                final JasperReport jasperFileCareAssessment2 = JasperCompileManager.compileReport(isCareAssessment2);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment2 = JasperFillManager.fillReport(jasperFileCareAssessment2,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment2);
                list.add(jasperPrintCareAssessment2);
                // U06200_ケアアセスメント(3)(4)_R3
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment34 = reportParameter
                        .getCareAssessment34R3Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment34 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT34_R3)));
                // コンパイル
                final JasperReport jasperFileCareAssessment34 = JasperCompileManager.compileReport(isCareAssessment34);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment34 = JasperFillManager.fillReport(jasperFileCareAssessment34,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment34);
                list.add(jasperPrintCareAssessment34);
                // U06200_ケアアセスメント(5)_R3
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment5 = reportParameter
                        .getCareAssessment5R3Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment5 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT5_R3)));
                // コンパイル
                final JasperReport jasperFileCareAssessment5 = JasperCompileManager.compileReport(isCareAssessment5);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment5 = JasperFillManager.fillReport(jasperFileCareAssessment5,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment5);
                list.add(jasperPrintCareAssessment5);
                // U06200_ケアアセスメント(6)_R3
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceCareAssessment6 = reportParameter
                        .getCareAssessment6R3Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isCareAssessment6 = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_ASSESSMENT6_R3)));
                // コンパイル
                final JasperReport jasperFileCareAssessment6 = JasperCompileManager.compileReport(isCareAssessment6);
                // 帳票出力
                final JasperPrint jasperPrintCareAssessment6 = JasperFillManager.fillReport(jasperFileCareAssessment6,
                        reportParameter.getParameters(),
                        dataSourceCareAssessment6);
                list.add(jasperPrintCareAssessment6);

                // U06200_全体のまとめ_R3
                // 報告先分の帳票データを2PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceHomeOverallSummary = reportParameter
                        .getHomeOverallSummaryR3Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isHomeOverallSummary = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_HOME_OVERALL_SUMMARY_R3_2)));
                if (ReportConstants.GDL_MATOME_FLG_1.equals(model.getAccessmentInfo().getGdlMatomeFlg())) {
                    isHomeOverallSummary = new FileInputStream(
                            (ReportUtil.getReportJrxmlFile(getFwProps(),
                                    ReportConstants.JRXML_HOME_OVERALL_SUMMARY_R3_1)));
                }
                // コンパイル
                final JasperReport jasperFileHomeOverallSummary = JasperCompileManager
                        .compileReport(isHomeOverallSummary);
                // 帳票出力
                final JasperPrint jasperHomeOverallSummary = JasperFillManager.fillReport(jasperFileHomeOverallSummary,
                        reportParameter.getParameters(),
                        dataSourceHomeOverallSummary);
                list.add(jasperHomeOverallSummary);

                // U06200_１日のスケジュール_R3
                // 報告先分の帳票データを1PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceOneDay = reportParameter.getOneDayScheduleR3Report();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isOneDay = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_DAILY_SCHEDULE_R3)));
                // コンパイル
                final JasperReport jasperFileOneDay = JasperCompileManager.compileReport(isOneDay);
                // 帳票出力
                final JasperPrint jasperPrintOneDay = JasperFillManager.fillReport(jasperFileOneDay,
                        reportParameter.getParameters(),
                        dataSourceOneDay);
                list.add(jasperPrintOneDay);
            }
        }
        if (ReportConstants.CPN_FLG_1.equals(model.getCpnFlg())) {
            // U06010_ケアチェック表１
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSource1 = reportParameter.getCareCheckTable1Report();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream is1 = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_CHECK_1)));
            // コンパイル
            final JasperReport jasperFile1 = JasperCompileManager.compileReport(is1);
            // 帳票出力
            final JasperPrint jasperPrint1 = JasperFillManager.fillReport(jasperFile1, reportParameter.getParameters(),
                    dataSource1);
            list.add(jasperPrint1);

            // U06010_ケアチェック表２
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSource2 = reportParameter.getCareCheckTable2Report();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream is2 = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_CHECK_2)));
            // コンパイル
            final JasperReport jasperFile2 = JasperCompileManager.compileReport(is2);
            // 帳票出力
            final JasperPrint jasperPrint2 = JasperFillManager.fillReport(jasperFile2, reportParameter.getParameters(),
                    dataSource2);
            list.add(jasperPrint2);

            // U06010_ケアチェック表３
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSource3 = reportParameter.getCareCheckTable3Report();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream is3 = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_CHECK_3)));
            // コンパイル
            final JasperReport jasperFile3 = JasperCompileManager.compileReport(is3);
            // 帳票出力
            final JasperPrint jasperPrint3 = JasperFillManager.fillReport(jasperFile3, reportParameter.getParameters(),
                    dataSource3);
            list.add(jasperPrint3);

            // U06010_ケアチェック表４
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSource4 = reportParameter.getCareCheckTable4Report();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream is4 = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_CHECK_4)));
            // コンパイル
            final JasperReport jasperFile4 = JasperCompileManager.compileReport(is4);
            // 帳票出力
            final JasperPrint jasperPrint4 = JasperFillManager.fillReport(jasperFile4, reportParameter.getParameters(),
                    dataSource4);
            list.add(jasperPrint4);

            // U06010_ケアチェック表５
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSource5 = reportParameter.getCareCheckTable5Report();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream is5 = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_CHECK_5)));
            // コンパイル
            final JasperReport jasperFile5 = JasperCompileManager.compileReport(is5);
            // 帳票出力
            final JasperPrint jasperPrint5 = JasperFillManager.fillReport(jasperFile5, reportParameter.getParameters(),
                    dataSource5);
            list.add(jasperPrint5);

            // U06010_ケアチェック表６
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSource6 = reportParameter.getCareCheckTable6Report();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream is6 = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_CHECK_6)));
            // コンパイル
            final JasperReport jasperFile6 = JasperCompileManager.compileReport(is6);
            // 帳票出力
            final JasperPrint jasperPrint6 = JasperFillManager.fillReport(jasperFile6, reportParameter.getParameters(),
                    dataSource6);
            list.add(jasperPrint6);

            // U06010_ケアチェック表７
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSource7 = reportParameter.getCareCheckTable7Report();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream is7 = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_CARE_CHECK_7)));
            // コンパイル
            final JasperReport jasperFile7 = JasperCompileManager.compileReport(is7);
            // 帳票出力
            final JasperPrint jasperPrint7 = JasperFillManager.fillReport(jasperFile7, reportParameter.getParameters(),
                    dataSource7);
            list.add(jasperPrint7);
        }

        if (ReportConstants.CPN_FLG_9.equals(model.getCpnFlg())) {
            Integer assType = CommonDtoUtil.strValToInt(model.getAccessmentInfo().getAiRadio());
            if (ReportConstants.ASSESSMENT_TYPE_FACILITYEDITION != assType
                    && ReportConstants.ASSESSMENT_TYPE_HOMEEDITION != assType) {
                assType = ReportConstants.ASSESSMENT_TYPE_HOMEEDITION;
                AssTypeByCriteriaInEntity assTypeByCriteriaInEntity = new AssTypeByCriteriaInEntity();
                assTypeByCriteriaInEntity.setIlRirekiId(CommonDtoUtil
                        .strValToInt(model.getAccessmentInfo().getRirekiId()));
                List<AssTypeOutEntity> assTypeOutList = cpnTucRaiRrkSelectMapper
                        .findAssTypeByCriteria(assTypeByCriteriaInEntity);
                if (assTypeOutList != null && assTypeOutList.size() > 0) {
                    assType = assTypeOutList.get(0).getAssType();
                }
            }

            // U06080_アセスメント表（Ａ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceA = reportParameter.getAssessmentSheetAReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isA = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_ASSESSMENT_SHEET_A)));
            // コンパイル
            final JasperReport jasperFileA = JasperCompileManager.compileReport(isA);
            // 帳票出力
            final JasperPrint jasperPrintA = JasperFillManager.fillReport(jasperFileA, reportParameter.getParameters(),
                    dataSourceA);
            list.add(jasperPrintA);

            // U06080_アセスメント表（Ｂ）
            // 報告先分の帳票データを2PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceB = reportParameter.getAssessmentSheetBReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isB = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_B)));
            // コンパイル
            final JasperReport jasperFileB = JasperCompileManager.compileReport(isB);
            // 帳票出力
            final JasperPrint jasperPrintB = JasperFillManager.fillReport(jasperFileB,
                    reportParameter.getParameters(),
                    dataSourceB);
            list.add(jasperPrintB);

            // U06080_アセスメント表（Ｃ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceC = reportParameter.getAssessmentSheetCReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isC = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_C)));
            // コンパイル
            final JasperReport jasperFileC = JasperCompileManager.compileReport(isC);
            // 帳票出力
            final JasperPrint jasperPrintC = JasperFillManager.fillReport(jasperFileC,
                    reportParameter.getParameters(),
                    dataSourceC);
            list.add(jasperPrintC);

            // U06080_アセスメント表（Ｄ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceD = reportParameter.getAssessmentSheetDReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isD = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_D)));
            // コンパイル
            final JasperReport jasperFileD = JasperCompileManager.compileReport(isD);
            // 帳票出力
            final JasperPrint jasperPrintD = JasperFillManager.fillReport(jasperFileD,
                    reportParameter.getParameters(),
                    dataSourceD);
            list.add(jasperPrintD);

            // U06080_アセスメント表（Ｅ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceE = reportParameter.getAssessmentSheetEReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isE = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_E)));
            // コンパイル
            final JasperReport jasperFileE = JasperCompileManager.compileReport(isE);
            // 帳票出力
            final JasperPrint jasperPrintE = JasperFillManager.fillReport(jasperFileE,
                    reportParameter.getParameters(),
                    dataSourceE);
            list.add(jasperPrintE);

            // U06080_アセスメント表（Ｆ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceF = reportParameter.getAssessmentSheetFReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isF = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_F)));
            // コンパイル
            final JasperReport jasperFileF = JasperCompileManager.compileReport(isF);
            // 帳票出力
            final JasperPrint jasperPrintF = JasperFillManager.fillReport(jasperFileF,
                    reportParameter.getParameters(),
                    dataSourceF);
            list.add(jasperPrintF);

            // U06080_アセスメント表（Ｇ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceG = reportParameter.getAssessmentSheetGReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isG = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_G)));
            // コンパイル
            final JasperReport jasperFileG = JasperCompileManager.compileReport(isG);
            // 帳票出力
            final JasperPrint jasperPrintG = JasperFillManager.fillReport(jasperFileG,
                    reportParameter.getParameters(),
                    dataSourceG);
            list.add(jasperPrintG);

            // U06080_アセスメント表（Ｈ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceH = reportParameter.getAssessmentSheetHReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isH = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_H)));
            // コンパイル
            final JasperReport jasperFileH = JasperCompileManager.compileReport(isH);
            // 帳票出力
            final JasperPrint jasperPrintH = JasperFillManager.fillReport(jasperFileH,
                    reportParameter.getParameters(),
                    dataSourceH);
            list.add(jasperPrintH);

            // U06080_アセスメント表（Ｉ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceI = reportParameter.getAssessmentSheetIReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isI = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_I)));
            // コンパイル
            final JasperReport jasperFileI = JasperCompileManager.compileReport(isI);
            // 帳票出力
            final JasperPrint jasperPrintI = JasperFillManager.fillReport(jasperFileI,
                    reportParameter.getParameters(),
                    dataSourceI);
            list.add(jasperPrintI);

            // U06080_アセスメント表（Ｊ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceJ = reportParameter.getAssessmentSheetJReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isJ = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_J)));
            // コンパイル
            final JasperReport jasperFileJ = JasperCompileManager.compileReport(isJ);
            // 帳票出力
            final JasperPrint jasperPrintJ = JasperFillManager.fillReport(jasperFileJ,
                    reportParameter.getParameters(),
                    dataSourceJ);
            list.add(jasperPrintJ);

            // U06080_アセスメント表（Ｋ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceK = reportParameter.getAssessmentSheetKReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isK = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_K)));
            // コンパイル
            final JasperReport jasperFileK = JasperCompileManager.compileReport(isK);
            // 帳票出力
            final JasperPrint jasperPrintK = JasperFillManager.fillReport(jasperFileK,
                    reportParameter.getParameters(),
                    dataSourceK);
            list.add(jasperPrintK);

            // U06080_アセスメント表（Ｌ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceL = reportParameter.getAssessmentSheetLReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isL = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_L)));
            // コンパイル
            final JasperReport jasperFileL = JasperCompileManager.compileReport(isL);
            // 帳票出力
            final JasperPrint jasperPrintL = JasperFillManager.fillReport(jasperFileL,
                    reportParameter.getParameters(),
                    dataSourceL);
            list.add(jasperPrintL);

            // U06080_アセスメント表（Ｍ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceM = reportParameter.getAssessmentSheetMReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isM = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_M)));
            // コンパイル
            final JasperReport jasperFileM = JasperCompileManager.compileReport(isM);
            // 帳票出力
            final JasperPrint jasperPrintM = JasperFillManager.fillReport(jasperFileM,
                    reportParameter.getParameters(),
                    dataSourceM);
            list.add(jasperPrintM);

            // U06080_アセスメント表（Ｎ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceN = reportParameter.getAssessmentSheetNReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isN = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_N)));
            // コンパイル
            final JasperReport jasperFileN = JasperCompileManager.compileReport(isN);
            // 帳票出力
            final JasperPrint jasperPrintN = JasperFillManager.fillReport(jasperFileN,
                    reportParameter.getParameters(),
                    dataSourceN);
            list.add(jasperPrintN);

            // U06080_アセスメント表（Ｏ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceO = reportParameter.getAssessmentSheetOReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isO = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_O)));
            // コンパイル
            final JasperReport jasperFileO = JasperCompileManager.compileReport(isO);
            // 帳票出力
            final JasperPrint jasperPrintO = JasperFillManager.fillReport(jasperFileO,
                    reportParameter.getParameters(),
                    dataSourceO);
            list.add(jasperPrintO);

            // U06080_アセスメント表（Ｐ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceP = reportParameter.getAssessmentSheetPReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isP = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_PQ)));
            // コンパイル
            final JasperReport jasperFileP = JasperCompileManager.compileReport(isP);
            // 帳票出力
            final JasperPrint jasperPrintP = JasperFillManager.fillReport(jasperFileP,
                    reportParameter.getParameters(),
                    dataSourceP);
            list.add(jasperPrintP);

            // U06080_アセスメント表（Ｑ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceQ = reportParameter.getAssessmentSheetQReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isQ = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_PQ)));
            // コンパイル
            final JasperReport jasperFileQ = JasperCompileManager.compileReport(isQ);
            // 帳票出力
            final JasperPrint jasperPrintQ = JasperFillManager.fillReport(jasperFileQ,
                    reportParameter.getParameters(),
                    dataSourceQ);
            list.add(jasperPrintQ);
            if (ReportConstants.ASSESSMENT_TYPE_FACILITYEDITION == assType) {
                // U06080_アセスメント表（Ｒ）
                // 報告先分の帳票データを1PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceR = reportParameter.getAssessmentSheetRReport();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isR = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(),
                                ReportConstants.JRXML_ASSESSMENT_SHEET_R)));
                // コンパイル
                final JasperReport jasperFileR = JasperCompileManager.compileReport(isR);
                // 帳票出力
                final JasperPrint jasperPrintR = JasperFillManager.fillReport(jasperFileR,
                        reportParameter.getParameters(),
                        dataSourceR);
                list.add(jasperPrintR);
            }
            if (ReportConstants.ASSESSMENT_TYPE_HOMEEDITION == assType) {
                // U06080_アセスメント表（Ｓ）
                // 報告先分の帳票データを1PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceS = reportParameter.getAssessmentSheetSReport();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isS = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(),
                                ReportConstants.JRXML_ASSESSMENT_SHEET_S)));
                // コンパイル
                final JasperReport jasperFileS = JasperCompileManager.compileReport(isS);
                // 帳票出力
                final JasperPrint jasperPrintS = JasperFillManager.fillReport(jasperFileS,
                        reportParameter.getParameters(),
                        dataSourceS);
                list.add(jasperPrintS);

                // U06080_アセスメント表（Ｔ）
                // 報告先分の帳票データを1PDFデータに集約する
                final JRBeanCollectionDataSource dataSourceT = reportParameter.getAssessmentSheetTReport();
                // 帳票レイアウトファイルのリソースを取得する
                InputStream isT = new FileInputStream(
                        (ReportUtil.getReportJrxmlFile(getFwProps(),
                                ReportConstants.JRXML_ASSESSMENT_SHEET_T)));
                // コンパイル
                final JasperReport jasperFileT = JasperCompileManager.compileReport(isT);
                // 帳票出力
                final JasperPrint jasperPrintT = JasperFillManager.fillReport(jasperFileT,
                        reportParameter.getParameters(),
                        dataSourceT);
                list.add(jasperPrintT);
            }
            // U06080_アセスメント表（ＵＶ）
            // 報告先分の帳票データを1PDFデータに集約する
            final JRBeanCollectionDataSource dataSourceUV = reportParameter.getAssessmentSheetUVReport();
            // 帳票レイアウトファイルのリソースを取得する
            InputStream isUV = new FileInputStream(
                    (ReportUtil.getReportJrxmlFile(getFwProps(),
                            ReportConstants.JRXML_ASSESSMENT_SHEET_PUV)));
            // コンパイル
            final JasperReport jasperFileUV = JasperCompileManager.compileReport(isUV);
            // 帳票出力
            final JasperPrint jasperPrintUV = JasperFillManager.fillReport(jasperFileUV,
                    reportParameter.getParameters(),
                    dataSourceUV);
            list.add(jasperPrintUV);

        }
        // U01700_課題分析（アセスメントの全てと一括印刷）
        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSourceIssuesAnalysis = reportParameter.getIssuesAnalysisReport();
        // 帳票レイアウトファイルのリソースを取得する
        InputStream isIssuesAnalysis = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(getFwProps(),
                        ReportConstants.JRXML_ISSUES_ANALYSIS)));
        // コンパイル
        final JasperReport jasperFileIssuesAnalysis = JasperCompileManager.compileReport(isIssuesAnalysis);
        // 帳票出力
        final JasperPrint jasperPrintIssuesAnalysis = JasperFillManager.fillReport(jasperFileIssuesAnalysis,
                reportParameter.getParameters(),
                dataSourceIssuesAnalysis);
        list.add(jasperPrintIssuesAnalysis);

        JasperPrint reslut = JasperUtil.concatReports(list);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(model, reslut);

        /*
         * ===============6.レスポンスを返却===============
         * 
         */
        super.setFilepath(model, outDto, pdf, pdf.getName());
        LOG.info(Constants.END);
    }
}
