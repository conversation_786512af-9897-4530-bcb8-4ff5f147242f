package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.logic.AssessmentHomePrintSettingsLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomePrintSettinguserSwitchingSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomePrintSettinguserSwitchingSelectServiceOutDto;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.07.18
 * <AUTHOR>
 * @implNote GUI00815_印刷設定 利用者切替
 */
@Service
public class AssessmentHomePrintSettinguserSwitchingSelectServiceImpl extends
        SelectServiceImpl<AssessmentHomePrintSettinguserSwitchingSelectServiceInDto, AssessmentHomePrintSettinguserSwitchingSelectServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** GUI00815_印刷設定 利用者切替共通クラス */
    @Autowired
    private AssessmentHomePrintSettingsLogic assessmentHomePrintSettingsLogic;

    /**
     * 利用者切替
     * 
     * @param inDto 利用者切替サービス入力Dto
     * @return 利用者切替サービス出力Dto
     * @throws Exception Exception
     */
    @Override
    protected AssessmentHomePrintSettinguserSwitchingSelectServiceOutDto mainProcess(
            AssessmentHomePrintSettinguserSwitchingSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        // 利用者切替共通
        AssessmentHomePrintSettinguserSwitchingSelectServiceOutDto outDto = this.assessmentHomePrintSettingsLogic
                .userSwitchingSelect(inDto);

        LOG.info(Constants.END);

        return outDto;

    }

}
