package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00978Info;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00978Kasan;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00978Kikan;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00978Rireki;
import jp.ndsoft.carebase.cmn.api.logic.KghAdmSecurityLogic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnWeekLogic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZSnc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghkrkBase01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.TermId2DateLogicOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekTableImageInitSelectInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekTableImageInitSelectOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmpComShokuinByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmpComShokuinOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnSypDayWeekNaiyoInfo1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnSypDayWeekNaiyoInfo1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnSypWeekLeH21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnSypWeekLeH21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucSypWeek1InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucSypWeek1InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekSvShurui1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekSvShurui1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekSvjigyoDdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekSvjigyoDdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KeikakuKikanInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KeikakuKikanInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnPlnGetCpnWeekPlansKsn4ppByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnPlnGetCpnWeekPlansKsn4ppOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucKrkKikanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucKrkKikanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoNameSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucSypNaiyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucSypWeek1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucSypWeek2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucSypWeek3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KeikakuJigosyaNameSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucKrkKikanSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * GUI00978_［週間表イメージ］週間表初期情報取得サービス.
 *
 * <AUTHOR>
 */
@Service
public class WeekTableImageInitSelectServiceImpl
		extends SelectServiceImpl<WeekTableImageInitSelectInDto, WeekTableImageInitSelectOutDto> {
	/** ロガー */
	private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

	/** 共通部品「f_kgh_krk_get_kikan」 */
	@Autowired
	private KghKrkZSnc01Logic kghKrkZSnc01Logic;

	/** 共通関数KghKrkZCpnFuncLogic */
	@Autowired
	private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;

	/** KghkrkBase01Logicロジッククラス */
	@Autowired
	private KghkrkBase01Logic kghkrkBase01Logic;

	// 計画対象期間情報取得
	@Autowired
	private KghTucKrkKikanSelectMapper kghTucKrkKikanSelectMapper;

	// 職員基本情報取得
	@Autowired
	private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

	// 週間表ヘッダ(パッケージプラン)履歴情報取得
	@Autowired
	private CpnTucSypWeek1SelectMapper cpnTucSypWeek1SelectMapper;

	// サービス種類名称の取得
	@Autowired
	private ComMscSvjigyoNameSelectMapper comMscSvjigyoNameSelectMapper;

	// 日課・週間内容マスタ情報の取得
	@Autowired
	private CpnMucSypNaiyoSelectMapper cpnMucSypNaiyoSelectMapper;

	// 詳細データの抽出
	@Autowired
	private CpnTucSypWeek2SelectMapper cpnTucSypWeek2SelectMapper;

	// サービス事業名称、略称の取得
	@Autowired
	private KeikakuJigosyaNameSelectMapper keikakuJigosyaNameSelectMapper;

	// 週間表（加算データ）情報の取得
	@Autowired
	private CpnTucSypWeek3SelectMapper cpnTucSypWeek3SelectMapper;

	@Autowired
	private KghKrkZCpnWeekLogic kghKrkZCpnWeekLogic;

	@Autowired
	private KghAdmSecurityLogic kghAdmSecurityLogic;

	/**
	 * ［週間表パターン（設定）］初期情報取得
	 * 
	 * @param inDto ［週間表パターン（設定）］初期情報取得の入力DTO.
	 * @return ［週間表パターン（設定）］初期情報取得の出力Dto
	 * @throws Exception Exception
	 */
	@Override
	protected WeekTableImageInitSelectOutDto mainProcess(WeekTableImageInitSelectInDto inDto) throws Exception {
		LOG.info(Constants.START);

		WeekTableImageInitSelectOutDto outDto = new WeekTableImageInitSelectOutDto();

		// 【変数】.期間ID
		String sc1Id = CommonConstants.STR_0;

		// 期間管理情報を取得
		boolean kikanFlg = kghKrkZSnc01Logic.getKghKrkKikan(CommonDtoUtil.strValToInt(inDto.getSyubetsuId()),
				CommonDtoUtil.strValToInt(inDto.getSvJigyoId()), CommonDtoUtil.strValToInt(inDto.getShisetuId()));
		outDto.setKikanFlag(kikanFlg ? CommonConstants.STR_1 : CommonConstants.STR_0);

		// 有効期間IDを取得
		Integer newTermId = kghKrkZCpnFuncLogic.getNewTermId();
		outDto.setTermNewId(CommonDtoUtil.objValToString(newTermId));

		// 2.3. レスポンスパラメータ.期間管理フラグ ＝ true:期間管理するの場合
		if (kikanFlg) {
			List<Gui00978Kikan> kikanList = this.getKikan(inDto.getShisetuId(), inDto.getUserId(), inDto.getSvJigyoId(),
					inDto.getSyubetsuId());

			if (!kikanList.isEmpty()) {
				outDto.setTaishokikanList(kikanList);
				outDto.setKikanIndex(CommonDtoUtil.objValToString(kikanList.size()));
				outDto.setKikanAllCount(CommonDtoUtil.objValToString(kikanList.size()));
				// 指定した機能が利用可能か判断する(機能IDバージョン)
				int sc1IdCnt = kghkrkBase01Logic.verifyKghKrkSc1id(CommonDtoUtil.strValToInt(inDto.getSyubetsuId()),
						CommonDtoUtil.strValToInt(inDto.getSc1Id()));

				//2.3.2. 上記2.3.1で戻り値.count≦0 の場合
				if (sc1IdCnt <= 0) {
					sc1Id = CommonConstants.STR_0;
					//2.3.3. 上記以外の場合
				} else {
					sc1Id = inDto.getSc1Id();
				}
				//2.3.4. 計画対象期間情報を取得する
				KeikakuKikanInfoByCriteriaInEntity entity = new KeikakuKikanInfoByCriteriaInEntity();
				entity.setAlSc1Id(CommonDtoUtil.strValToInt(sc1Id));
				List<KeikakuKikanInfoOutEntity> list = kghTucKrkKikanSelectMapper.findKeikakuKikanInfoByCriteria(entity);
				
				//2.3.5. 上記2.3.4で取得した件数が0件の場合
				if (list.isEmpty()) {
					//2.3.5.1.　戻り値.計画対象期間リスト.件数 > 0 件の場合
					if (!kikanList.isEmpty()) {
						//【変数】.期間ID = 上記取得した計画対象期間リストの一行目.期間ID
						sc1Id = kikanList.get(0).getSc1Id();
					}
				}
			}
		} else {
			sc1Id = CommonConstants.STR_0;
		}

		// 週間表ヘッダ(パッケージプラン)履歴情報取得
		List<Gui00978Rireki> rirekiList = new ArrayList<>();
		rirekiList = this.getRirekiInfo(sc1Id, inDto.getUserId(), inDto.getSvJigyoId());
		outDto.setWeek1List(rirekiList);
		if (!rirekiList.isEmpty()) {
			outDto.setRirekiIndex(CommonDtoUtil.objValToString(rirekiList.size()));
			outDto.setRirekiAllCount(CommonDtoUtil.objValToString(rirekiList.size()));
		}

		// 4.1. 上記処理3.履歴リスト.件数 ＞ 0 件の場合、【週間表詳細情報取得】データ情報を取得する
		if (!rirekiList.isEmpty()) {
			// ｻｰﾋﾞｽ検索の開始、終了をtermidから取得
			TermId2DateLogicOutDto termId2DateLogicOutDto = kghKrkZCpnFuncLogic
					.getTermid2date(CommonDtoUtil.strValToInt(rirekiList.get(0).getSc1Id()), null, null);
			// 週間表詳細情報取得
			outDto.setWeek2List(this.getInfo(rirekiList.get(0).getWeek1Id(), termId2DateLogicOutDto.getSYmd(),
					termId2DateLogicOutDto.getEYmd()));
		}

		// 5. 「日課取込」、「利用表取込」のセキュリティをチェック
		// 5.1. 「日課取込」チェック
		// 5.2. 「利用表取込」チェック
		Integer ret = kghAdmSecurityLogic.chkUseByKinouName(CommonDtoUtil.strValToInt(inDto.getShokuinId()),
				CommonConstants.SYSTEM_CODE_71101, CommonDtoUtil.strValToInt(inDto.getSvJigyoId()),
				CommonConstants.KINOU_NAME_DAILY_SCHEDULE);
		if (ret == CommonConstants.INT_0) {
			outDto.setDayFlg(CommonConstants.STR_0);
			outDto.setRiyoFlg(CommonConstants.STR_0);
		}

		LOG.info(Constants.END);
		return outDto;
	}

	/**
	 * 計画対象期間情報を取得する
	 * 
	 * @param shisetsuId
	 * @param userId
	 * @param svJigyoId
	 * @param syubetsuId
	 * 
	 * @return
	 */
	public List<Gui00978Kikan> getKikan(String shisetsuId, String userId, String svJigyoId, String syubetsuId) {

		List<Gui00978Kikan> kikanList = new ArrayList<>();

		KghTucKrkKikanByCriteriaInEntity entity = new KghTucKrkKikanByCriteriaInEntity();
		entity.setSId(CommonDtoUtil.strValToInt(shisetsuId));
		entity.setUId(CommonDtoUtil.strValToInt(userId));
		entity.setJId(CommonDtoUtil.strValToInt(svJigyoId));
		entity.setSyubetsuId(CommonDtoUtil.strValToInt(syubetsuId));

		List<KghTucKrkKikanOutEntity> list = kghTucKrkKikanSelectMapper.findKghTucKrkKikanByCriteria(entity);

		list.forEach(l -> {
			Gui00978Kikan g = new Gui00978Kikan();
			g.setStartYmd(l.getStartYmd());
			g.setEndYmd(l.getEndYmd());
			g.setSc1Id(CommonDtoUtil.objValToString(l.getSc1Id()));
			kikanList.add(g);
		});

		return kikanList;
	}

	/**
	 * 週間表ヘッダ(パッケージプラン)履歴情報取得
	 * 
	 * @param sc1Id
	 * @param userId
	 * @param svJigyoId
	 * @return
	 */
	public List<Gui00978Rireki> getRirekiInfo(String sc1Id, String userId, String svJigyoId) {

		List<Gui00978Rireki> list = new ArrayList<>();

		Map<Integer, String> shokuInfoMap = new HashMap<>();

		// 2.1 職員基本情報を取得
		List<CmpComShokuinOutEntity> cmpComShokuinOutEntityList = comMscShokuinSelectMapper
				.findCmpComShokuinByCriteria(new CmpComShokuinByCriteriaInEntity());

		cmpComShokuinOutEntityList.forEach(c -> {
			shokuInfoMap.put(c.getChkShokuId(), c.getShokuin1Knj() + CommonConstants.BLANK_SPACE + c.getShokuin2Knj());
		});

		// 2.2 週間表ヘッダ(パッケージプラン)履歴情報を取得
		CpnTucSypWeek1InfoByCriteriaInEntity entity = new CpnTucSypWeek1InfoByCriteriaInEntity();
		entity.setSc1(CommonDtoUtil.strValToInt(sc1Id));
		entity.setUserid(CommonDtoUtil.strValToInt(userId));
		entity.setSvJigyoId(CommonDtoUtil.strValToInt(svJigyoId));
		List<CpnTucSypWeek1InfoOutEntity> cpnTucSypWeek1InfoOutEntityList = cpnTucSypWeek1SelectMapper
				.findCpnTucSypWeek1InfoByCriteria(entity);

		cpnTucSypWeek1InfoOutEntityList.forEach(c -> {
			Gui00978Rireki r = new Gui00978Rireki();
			// 週間表ID
			r.setWeek1Id(CommonDtoUtil.objValToString(c.getWeek1Id()));
			// 期間ID
			r.setSc1Id(CommonDtoUtil.objValToString(c.getSc1Id()));
			// 作成日
			r.setCreateYmd(c.getCreateYmd());
			// 職員ID
			r.setShokuId(CommonDtoUtil.objValToString(c.getShokuId()));
			// 作成者
			r.setShokuKnj(shokuInfoMap.get(c.getShokuId()));
			// ケースNo.
			r.setCaseNo(c.getCaseNo());
			// 当該年月
			r.setTougaiYm(c.getTougaiYm());
			// 有効期間ID
			r.setTermid(CommonDtoUtil.objValToString(c.getTermid()));
			// 改訂フラグ
			r.setKaiteiFlg(CommonDtoUtil.objValToString(c.getKaiteiFlg()));
			// 週単位以外サービス
			r.setWIgaiKnj(c.getWIgaiKnj());

			list.add(r);
		});

		return list;
	}

	/**
	 * 週間表詳細情報取得
	 * 
	 * @param week1Id
	 * @param sYmd
	 * @param eYmd
	 * @return
	 */
	public List<Gui00978Info> getInfo(String week1Id, String sYmd, String eYmd) {

		List<Gui00978Info> list = new ArrayList<>();

		// サービス種類名称の取得
		List<CpnWeekSvShurui1OutEntity> cpnWeekSvShurui1OutEntityList = comMscSvjigyoNameSelectMapper
				.findCpnWeekSvShurui1ByCriteria(new CpnWeekSvShurui1ByCriteriaInEntity());

		// 日課・週間内容マスタ情報の取得
		List<CpnSypDayWeekNaiyoInfo1OutEntity> cpnSypDayWeekNaiyoInfo1OutEntityList = cpnMucSypNaiyoSelectMapper
				.findCpnSypDayWeekNaiyoInfo1ByCriteria(new CpnSypDayWeekNaiyoInfo1ByCriteriaInEntity());

		// 2.3 詳細データの抽出
		CpnSypWeekLeH21ByCriteriaInEntity cpnSypWeekLeH21ByCriteriaInEntity = new CpnSypWeekLeH21ByCriteriaInEntity();
		cpnSypWeekLeH21ByCriteriaInEntity.setRireki(CommonDtoUtil.strValToInt(week1Id));
		List<CpnSypWeekLeH21OutEntity> cpnSypWeekLeH21OutEntityList = cpnTucSypWeek2SelectMapper
				.findCpnSypWeekLeH21ByCriteria(cpnSypWeekLeH21ByCriteriaInEntity);

		if (!CollectionUtils.isEmpty(cpnSypWeekLeH21OutEntityList)) {
			for (CpnSypWeekLeH21OutEntity e : cpnSypWeekLeH21OutEntityList) {
				Gui00978Info info = new Gui00978Info();
				// 詳細ID
				info.setWeek2Id(CommonDtoUtil.objValToString(e.getWeek2Id()));
				// 週間表ID
				info.setWeek1Id(CommonDtoUtil.objValToString(e.getWeek1Id()));
				// 利用者ＩＤ
				info.setUserid(CommonDtoUtil.objValToString(e.getUserid()));
				// 曜日
				info.setYoubi(e.getYoubi());
				// 開始時間
				info.setKaishiJikan(e.getKaishiJikan());
				// 終了時間
				info.setShuuryouJikan(e.getShuuryouJikan());
				// 内容CD
				info.setNaiyoCd(CommonDtoUtil.objValToString(e.getNaiyoCd()));
				// 内容
				info.setNaiyoKnj(cpnSypDayWeekNaiyoInfo1OutEntityList.stream()
						.filter(c -> c.getNaiyoCd() == CommonDtoUtil.strValToInt(info.getNaiyoCd()))
						.map(c -> c.getNaiyoKnj()).findFirst().orElse(null));
				// メモ
				info.setMemoKnj(e.getMemoKnj());
				// 文字サイズ
				info.setFontSize(CommonDtoUtil.objValToString(e.getFontSize()));
				// 表示モード
				info.setDispMode(CommonDtoUtil.objValToString(e.getDispMode()));
				// 文字位置
				info.setAlignment(CommonDtoUtil.objValToString(e.getAlignment()));
				// サービス種類
				info.setSvShuruiCd(e.getSvShuruiCd());
				// サービス項目（台帳）
				info.setSvItemCd(CommonDtoUtil.objValToString(e.getSvItemCd()));
				// サービス事業者CD
				info.setSvJigyoId(CommonDtoUtil.objValToString(e.getSvJigyoId()));
				// サービス種類名称
				info.setSvShuruiKnj(cpnWeekSvShurui1OutEntityList.stream()
						.filter(c -> StringUtils.equals(c.getSvKindCd(), info.getSvShuruiCd()))
						.map(c -> c.getRuakuKnj()).findFirst().orElse(null));
				// 文字カラー
				info.setFontColor(CommonDtoUtil.objValToString(e.getFontColor()));
				// 背景カラー
				info.setBackColor(CommonDtoUtil.objValToString(e.getBackColor()));
				// 時間表示区分
				info.setTimeKbn(CommonDtoUtil.objValToString(e.getTimeKbn()));
				// 週単位以外のサービス区分
				info.setIgaiKbn(CommonDtoUtil.objValToString(e.getIgaiKbn()));
				// 週単位以外のサービス（日付指定）
				info.setIgaiDate(e.getIgaiDate());
				// 週単位以外のサービス（曜日指定）
				info.setIgaiWeek(e.getIgaiWeek());
				// 福祉用具貸与の単価
				info.setSvTani(CommonDtoUtil.objValToString(e.getSvTani()));
				// 福祉用具貸与マスタID
				info.setFygId(CommonDtoUtil.objValToString(e.getFygId()));
				// 枠外表示するかのフラグ
				info.setWakugaiFlg(CommonDtoUtil.objValToString(e.getWakugaiFlg()));

				list.add(info);
			}

			int svShuruiCds[] = cpnSypWeekLeH21OutEntityList.stream()
					.filter(c -> !StringUtils.isEmpty(c.getSvShuruiCd()))
					.mapToInt(c -> CommonDtoUtil.strValToInt(c.getSvShuruiCd())).toArray();
			Arrays.sort(svShuruiCds);
			String minSvShuruiCd = svShuruiCds.length > CommonConstants.INT_0
					? CommonDtoUtil.objValToString(svShuruiCds[0])
					: CommonConstants.BLANK_STRING;
			String maxSvShuruiCd = svShuruiCds.length > CommonConstants.INT_0
					? CommonDtoUtil.objValToString(svShuruiCds[svShuruiCds.length - 1])
					: CommonConstants.BLANK_STRING;

			// サービス事業名称、略称の取得
			CpnWeekSvjigyoDdByCriteriaInEntity svjigyoInfoDetailByCriteriaInEntity = new CpnWeekSvjigyoDdByCriteriaInEntity();
			svjigyoInfoDetailByCriteriaInEntity.setSvCd(minSvShuruiCd);
			svjigyoInfoDetailByCriteriaInEntity.setSvCd2(maxSvShuruiCd);
			List<CpnWeekSvjigyoDdOutEntity> svjigyoInfoDetailOutEntityList = keikakuJigosyaNameSelectMapper
					.findCpnWeekSvjigyoDdByCriteria(svjigyoInfoDetailByCriteriaInEntity);

			list.forEach(g -> {
				svjigyoInfoDetailOutEntityList.stream().filter(
						s -> StringUtils.equals(CommonDtoUtil.objValToString(s.getSvJigyoId()), g.getSvJigyoId()))
						.findFirst().ifPresent(o -> {
							// サービス事業者名称
							g.setSvJigyoKnj(o.getJigyoKnj());
							// サービス事業者略称
							g.setSvJigyoRks(o.getRuakuKnj());
						});
			});

			// 2.4 上記2.3取得したOUTPUT情報を繰り返して、サービス項目名称の取得
            for (int i = 0; i < list.size(); i++) {
    			List<Gui00978Kasan> kasanList = new ArrayList<>();
            	if (list.get(i).getSvShuruiCd() != null && list.get(i).getSvShuruiCd().length() > 0 ) {
            		list.get(i).setSvItemKnj(kghKrkZCpnWeekLogic.getName(CommonDtoUtil.strValToInt(list.get(i).getSvShuruiCd()),
    						CommonDtoUtil.strValToInt(list.get(i).getSvItemCd()), sYmd, eYmd));
            	}
            	String strSvShuruiCd = list.get(i).getSvShuruiCd();
            	// 3. 上記2.3取得したOUTPUT情報を繰り返して、週間表（加算データ）情報の検索処理を行う
				KghCmnPlnGetCpnWeekPlansKsn4ppByCriteriaInEntity entity = new KghCmnPlnGetCpnWeekPlansKsn4ppByCriteriaInEntity();
				entity.setWeek1(CommonDtoUtil.strValToInt(week1Id));
				entity.setWeek2(CommonDtoUtil.strValToInt(list.get(i).getWeek2Id()));
				List<KghCmnPlnGetCpnWeekPlansKsn4ppOutEntity> kghCmnPlnGetCpnWeekPlansKsn4ppOutEntityList = cpnTucSypWeek3SelectMapper
						.findKghCmnPlnGetCpnWeekPlansKsn4ppByCriteria(entity);
				kghCmnPlnGetCpnWeekPlansKsn4ppOutEntityList.forEach(k -> {
					Gui00978Kasan kasan = new Gui00978Kasan();
					kasan.setId(CommonDtoUtil.objValToString(k.getId()));
					kasan.setSvJigyoId(CommonDtoUtil.objValToString(k.getSvJigyoId()));
					kasan.setSvItemCd(CommonDtoUtil.objValToString(k.getSvItemCd()));
					kasan.setKaisuu(CommonDtoUtil.objValToString(k.getKaisuu()));
					kasan.setSvTani(CommonDtoUtil.objValToString(k.getSvTani()));
					kasan.setFygId(CommonDtoUtil.objValToString(k.getFygId()));

	            	if (strSvShuruiCd != null && strSvShuruiCd.length() > 0 ) {
						// 抽出データを繰り返して、加算サービス名を取得
						kasan.setSvKasanKnj(kghKrkZCpnWeekLogic.getName(CommonDtoUtil.strValToInt(strSvShuruiCd),
								CommonDtoUtil.strValToInt(kasan.getSvItemCd()), sYmd, eYmd));
	            	}
					kasanList.add(kasan);
				});

				list.get(i).setWeek3List(kasanList);

				// 4. 返却情報を繰り返して、週以外、月日、曜日の文字転換
				// 4.1 返却情報.詳細リスト.週単位以外のサービス区分がある場合（NULL以外の場合）
				if (!StringUtils.isEmpty(list.get(i).getIgaiKbn())) {
					// 4.1.1 返却情報.詳細リスト.曜日が”99999999”の場合
					if (CommonConstants.YOUBI_8SIZE_9.equals(list.get(i).getYoubi())) {
						// ① 返却情報.詳細リスト.週単位以外のサービス区分が1　且つ　返却情報.詳細リスト.週単位以外のサービス（日付指定）が"0000000000000000000000000000000"以外の場合、
						// 下記共通関数「f_cpn_week_date_to_moji」を呼び出す
						if (CommonConstants.IGAI_KBN_STR_ONE.equals(list.get(i).getIgaiKbn()) && 
							!CommonConstants.I_GAI_DATE.equals(list.get(i).getIgaiDate())) {
							list.get(i).setIgaiMoji(kghKrkZCpnFuncLogic.getWeekDateToMoji(list.get(i).getIgaiDate()));
							// ② 返却情報.詳細リスト.週単位以外のサービス区分が2　且つ　返却情報.詳細リスト.週単位以外のサービス（曜日指定）が"00000000000000000000000000000000000"以外）の場合、
							// 下記共通関数「f_cpn_week_week_to_moji」を呼び出す
						} else if (CommonConstants.IGAI_KBN_STR_TWO.equals(list.get(i).getIgaiKbn()) &&
								!CommonConstants.I_GAI_DATE.equals(list.get(i).getIgaiWeek())) {
							list.get(i).setIgaiMoji(kghKrkZCpnFuncLogic.getWeekToMoji(list.get(i).getIgaiDate()));
						}
					}
				}
            }
		}

		return list;
	}
}
