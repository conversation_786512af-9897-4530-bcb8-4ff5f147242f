package jp.ndsoft.carebase.cmn.api.service.dto;

import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * @since 2025/05/13
 * <AUTHOR> THANH PHONG
 * @implNote GUI00827_「アセスメント総括取込み設定」画面
 */
@Getter
@Setter
public class Gui00827ParameterListUpdateInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** キー */
    private String keyKnj;

    /** パラメータ */
    private String paramKnj;

    /** 更新回数 */
    // private String modifiedCnt;
}
