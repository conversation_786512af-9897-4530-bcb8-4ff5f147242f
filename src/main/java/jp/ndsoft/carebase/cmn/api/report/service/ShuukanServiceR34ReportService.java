package jp.ndsoft.carebase.cmn.api.report.service;

import java.io.File;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.report.dto.ShuukanServiceR34ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ShuukanServiceR34ReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.logic.ShuukanServiceR34ReportLogic;
import jp.ndsoft.carebase.cmn.api.report.model.ShuukanServiceR34ReportParameterModel;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * 
 * U00852_週間サービス計画表（R34改訂）
 * 
 * <AUTHOR>
 */
@Service("ShuukanServiceR34Report")
public class ShuukanServiceR34ReportService
        extends PdfReportServiceImpl<ShuukanServiceR34ReportParameterModel, ShuukanServiceR34ReportServiceOutDto> {

    /**
     * ロガー.
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // 情報収集シート帳票出力ロジック
    @Autowired
    private ShuukanServiceR34ReportLogic shuukanSvcRptLogic;

    /**
     * 帳票パラメータ取得
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(ShuukanServiceR34ReportParameterModel model,
            ShuukanServiceR34ReportServiceOutDto outDto) throws Exception {

        LOG.info(Constants.START);

        // // 帳票用データ詳細
        ShuukanServiceR34ReportServiceInDto infoInDto = shuukanSvcRptLogic
                .getShuukanServiceR34ReportParameters(model);

        // ノート情報格納配列
        List<ShuukanServiceR34ReportServiceInDto> shuukanServiceR34ReportInfoList = new ArrayList<ShuukanServiceR34ReportServiceInDto>();
        shuukanServiceR34ReportInfoList.add(infoInDto);

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(shuukanServiceR34ReportInfoList);
        infoInDto.setDataSource(dataSource);

        LOG.info(Constants.END);
        return infoInDto;
    }

    /**
     * 帳票出力
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final ShuukanServiceR34ReportParameterModel model,
            final ShuukanServiceR34ReportServiceOutDto outDto) throws Exception {

        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final ShuukanServiceR34ReportServiceInDto reportParameter = (ShuukanServiceR34ReportServiceInDto) getReportParameters(
                model, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // コンパイル
        final JasperReport jasperFile = shuukanSvcRptLogic.getJasperReport(getFwProps(), reportParameter, model);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile, reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(model, jasperPrint);

        super.setFilepath(model, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }

}
