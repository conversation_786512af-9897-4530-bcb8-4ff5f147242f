package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI01278_基本調査４
 * 
 * @description
 *              基本調査４初期情報取得
 *              基本調査４の情報エンティティ
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class Gui01278BasicSurvey4InfoIn extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 第3群外出 */
    private String bango39;

    /** 第4群被害的 */
    private String bango41;

    /** 第4群作話 */
    private String bango42;

    /** 第4群感情が不安定 */
    private String bango43;

    /** 第4群昼夜逆転 */
    private String bango44;

    /** 第4群同じ話をする */
    private String bango45;

    /** 第4群大声をだす */
    private String bango46;

    /** 第4群介護に抵抗 */
    private String bango47;

    /** 第4群落ち着きなし */
    private String bango48;

    /** 第4群一人で出たがる */
    private String bango49;

    /** 第4群収集癖 */
    private String bango410;

    /** 第4群物や衣類を壊す */
    private String bango411;

    /** 第4群物忘れ */
    private String bango412;
}
