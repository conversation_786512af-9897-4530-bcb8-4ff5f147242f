package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00805Gdl4NeceH21;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00805Gdl4NeceR3;
import jp.ndsoft.carebase.cmn.api.logic.AssessmentHomeLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeSaveServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeTab7MatomeUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeTab7MatomeUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4NeceH21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5NeceR3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdlRirekiMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4NeceH21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4NeceH21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5NeceR3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5NeceR3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRireki;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRirekiCriteria;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
/**
 * @since 2025.04.11
 * <AUTHOR>
 * @implNote GUI00805_［アセスメント］画面（居宅）（7まとめ）データ保存
 */
@Service
public class AssessmentHomeTab7MatomeUpdateServiceImpl extends
        UpdateServiceImpl<AssessmentHomeTab7MatomeUpdateServiceInDto, AssessmentHomeTab7MatomeUpdateServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 居宅アセスメント履歴の更新詳細 DAO */
    @Autowired
    private CpnTucGdlRirekiMapper cpnTucGdlRirekiMapper;
    /** ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂） */
    @Autowired
    private CpnTucGdl4NeceH21Mapper cpnTucGdl4NeceH21Mapper;
    /** ＧＬ＿全体のまとめ・特記事項（R３改訂） */
    @Autowired
    private CpnTucGdl5NeceR3Mapper cpnTucGdl5NeceR3Mapper;
    /** ［アセスメント］画面（居宅）画面のロジッククラス */
    @Autowired
    private AssessmentHomeLogic assessmentHomeLogic;

    @Autowired
    private PlatformTransactionManager transactionManager;

    /**
     * ［アセスメント］画面（居宅）（7まとめ）データ保存
     * 
     * @param inDto ［アセスメント］画面（居宅）（7まとめ）入力Dto
     * @return ［アセスメント］画面（居宅）（7まとめ）出力DTO
     */
    @Override
    protected AssessmentHomeTab7MatomeUpdateServiceOutDto mainProcess(
            final AssessmentHomeTab7MatomeUpdateServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        AssessmentHomeTab7MatomeUpdateServiceOutDto outDto = new AssessmentHomeTab7MatomeUpdateServiceOutDto();
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        /*
         * ===============2. バリデーションチェックを行う。===============
         * 
         */
        boolean resFlg = assessmentHomeLogic.documentPerformValidationCheck(loginDto);
        // 2.2.4. 【変数】.処理結果が「false：失敗」の場合
        if (!resFlg) {
            // 2.2.4. 【変数】.処理結果が「false：失敗」の場合、下記返却する情報を設定して、後続処理を飛ばして、API処理を正常終了とする。
            // レスポンス.計画期間ID ： リクエストパラメータ.計画期間ID
            outDto.setSc1Id(loginDto.getSc1Id());
            // レスポンス.アセスメントID ： リクエストパラメータ.アセスメントID
            outDto.setGdlId(loginDto.getGdlId());
            // レスポンス.エラー区分: "1
            outDto.setErrKbn(CommonConstants.E_DOC_CHECK_ERR);
            return outDto;
        }
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transactionB = transactionManager.getTransaction(def);
        try {
            // ［アセスメント］画面（居宅）（7まとめ） データ保存
            outDto = mainProcessMealUpdate(inDto);
            transactionManager.commit(transactionB);
        } catch (Exception e) {
            transactionManager.rollback(transactionB);
            throw e;
        }

        // e-文書_ケアチェック表１のリクエストパラメータ
        TransactionStatus transactionC = transactionManager.getTransaction(def);
        try {
            mainProcessEdocOut(inDto, outDto);
            transactionManager.commit(transactionC);
        } catch (Exception e) {
            transactionManager.rollback(transactionC);
            throw e;
        }

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * API定義 (e文書出力)
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected void mainProcessEdocOut(
            AssessmentHomeTab7MatomeUpdateServiceInDto inDto, AssessmentHomeTab7MatomeUpdateServiceOutDto outDto)
            throws Exception {
        // 入力dtoをLoginDtoにコピーする
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);

        /*
         * ===============e文書出力===============
         * 
         */
        // e文書出力
        String errKbn = assessmentHomeLogic.getPrint(loginDto, outDto.getSc1Id());
        outDto.setErrKbn(errKbn);
    }

    /**
     * ［アセスメント］画面（居宅）（7まとめ） データ保存
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected AssessmentHomeTab7MatomeUpdateServiceOutDto mainProcessMealUpdate(
            AssessmentHomeTab7MatomeUpdateServiceInDto inDto)
            throws Exception {
        AssessmentHomeTab7MatomeUpdateServiceOutDto outDto = new AssessmentHomeTab7MatomeUpdateServiceOutDto();
        // 履歴ID設定
        outDto.setGdlId(inDto.getRaiId());
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);
        // 履歴ID設定
        loginDto.setGdlId(inDto.getRaiId());
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        /*
         * ===============2. 計画対象期間の保存処理===============
         * 
         */
        // 変数.計画対象期間ID
        Integer sc1Id = CommonConstants.NUMBER_ZERO;
        if (inDto.getSc1Id() == null) {
            // 2.1.リクエストパラメータ.計画対象期間IDがnullの場合、【27-06記録共通期間】情報を登録する。
            if (CommonConstants.SUCCESS.equals(this.assessmentHomeLogic.insertKghTucKrkKikan(loginDto))) {
                sc1Id = CommonDtoUtil.strValToInt(loginDto.getSc1Id());
            }
        } else {
            // 変数.計画対象期間ID=リクエストパラメータ.計画対象期間ID
            sc1Id = CommonDtoUtil.strValToInt(inDto.getSc1Id());
        }
        final Integer sc1IdTmp = sc1Id;
        if (CommonConstants.DELETE_TEI_KBN_2.equals(inDto.getDeleteKbn())) {
            // 3.リクエストパラメータ.削除処理区分が2:画面を履歴ごと削除するの場合、下記テーブルデータを更新する。
            // 3.サブ情報を更新する
            this.assessmentHomeLogic.homeLogicsyuri(loginDto, sc1IdTmp);
        } else if (CommonConstants.DELETE_TEI_KBN_1.equals(inDto.getDeleteKbn())) {
            /*
             * ===============4. リクエストパラメータ詳細.削除処理区分が1:画面のみ削除するの場合===============
             * 
             */
            // リクエストパラメータ.改定が4（Ｈ21/４改訂版）
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {
                for (Gui00805Gdl4NeceH21 item : inDto.getGdl4NeceH21List()) {

                    final CpnTucGdl4NeceH21Criteria cpnTucGdl4NeceH21Criteria = new CpnTucGdl4NeceH21Criteria();
                    // ID＝リクエストパラメータ.アセスメントID
                    // 計画期間ID＝【変数】.計画対象期間ID
                    // マスタID＝リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．マスタID
                    // 法人ID＝リクエストパラメータ.法人ID
                    // 施設ID＝リクエストパラメータ.施設ID
                    // 事業者ID＝リクエストパラメータ.事業者ID
                    // 利用者ＩＤ＝リクエストパラメータ.利用者ＩＤ
                    // 削除フラグ = 0（未削除データ）
                    // 更新回数 = リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．更新回数

                    cpnTucGdl4NeceH21Criteria.createCriteria()
                            .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                            .andSc1IdEqualTo(sc1IdTmp)
                            .andSvJigyoIdEqualTo(
                                    CommonDtoUtil.strValToInt(item.getMastId()))
                            .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                            .andHoujinIdEqualTo(
                                    CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                            .andShisetuIdEqualTo(
                                    CommonDtoUtil.strValToInt(inDto.getShisetuId()));

                    if (CommonConstants.NUMBER_ZERO >= this.cpnTucGdl4NeceH21Mapper
                            .deleteByCriteria(cpnTucGdl4NeceH21Criteria)) {
                        throw new ExclusiveException();
                    }

                }

            } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {
                // 4.2 リクエストパラメータ.改定フラグが5（R3/４改訂版）
                // アセスメントID＝リクエストパラメータ.アセスメントID
                // 計画期間ID＝【変数】.計画対象期間ID
                // マスタID＝リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．マスタID
                // 法人ID＝リクエストパラメータ.法人ID
                // 施設ID＝リクエストパラメータ.施設ID
                // 事業者ID＝リクエストパラメータ.事業者ID
                // 利用者ＩＤ＝リクエストパラメータ.利用者ＩＤ
                // 削除フラグ = 0（未削除データ）
                // 更新回数 = リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．更新回数
                for (Gui00805Gdl4NeceR3 item : inDto.getGdl4NeceR3List()) {

                    final CpnTucGdl5NeceR3Criteria cpnTucGdl5NeceR3Criteria = new CpnTucGdl5NeceR3Criteria();
                    cpnTucGdl5NeceR3Criteria.createCriteria()
                            .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                            .andSc1IdEqualTo(sc1IdTmp)
                            .andSvJigyoIdEqualTo(
                                    CommonDtoUtil.strValToInt(item.getMastId()))
                            .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                            .andHoujinIdEqualTo(
                                    CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                            .andShisetuIdEqualTo(
                                    CommonDtoUtil.strValToInt(inDto.getShisetuId()));

                    if (CommonConstants.NUMBER_ZERO >= this.cpnTucGdl5NeceR3Mapper
                            .deleteByCriteria(cpnTucGdl5NeceR3Criteria)) {
                        throw new ExclusiveException();
                    }
                }

            }
            // 4.3. 【ＧＬ＿居宅アセスメント履歴】情報を更新する。

            CpnTucGdlRireki cpnTucGdlRirekiRecord = new CpnTucGdlRireki();
            cpnTucGdlRirekiRecord.setAss14(CommonConstants.BLANK_STRING);
            // ■更新条件
            // アセスメントID＝リクエストパラメータ.アセスメントID
            // 計画期間ID＝【変数】.計画対象期間ID
            // 事業者ID＝リクエストパラメータ.事業者ID
            // 利用者ＩＤ＝リクエストパラメータ.利用者ＩＤ
            // 法人ID＝リクエストパラメータ.法人ID
            // 施設ID＝リクエストパラメータ.施設ID
            // 削除フラグ = 0（未削除データ）
            // 更新回数＝リクエストパラメータ.履歴更新回数
            final CpnTucGdlRirekiCriteria cpnTucGdlRirekiCriteria = new CpnTucGdlRirekiCriteria();
            cpnTucGdlRirekiCriteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                    .andSc1IdEqualTo(sc1IdTmp)
                    .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                    .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                    .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                    .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            if (CommonConstants.NUMBER_ZERO >= this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(
                    cpnTucGdlRirekiRecord,
                    cpnTucGdlRirekiCriteria)) {
                throw new ExclusiveException();
            }

        } else {
            /*
             * ===============5. 以外の場合===============
             * 
             */
            // 5.1. 履歴情報の保存処理
            // 変数.新規履歴ID

            if (CommonDtoUtil.isHistoryCreate(inDto)) {
                // 5.1.1. リクエストパラメータ.履歴更新区分が"C":新規の場合、【ＧＬ＿居宅アセスメント履歴】情報を登録する。
                CpnTucGdlRireki cpnTucGdlRirekiRecord = new CpnTucGdlRireki();
                // 計画期間ID←【変数】.計画対象期間ID
                cpnTucGdlRirekiRecord.setSc1Id(sc1IdTmp);
                // 法人ID←リクエストパラメータ.法人ID
                cpnTucGdlRirekiRecord
                        .setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
                // 施設ID←リクエストパラメータ.施設ID
                cpnTucGdlRirekiRecord.setShisetuId(
                        CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                // 事業者ID←リクエストパラメータ.事業者ID
                cpnTucGdlRirekiRecord.setSvJigyoId(
                        CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                // 利用者ＩＤ←リクエストパラメータ.利用者ID
                cpnTucGdlRirekiRecord.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
                // アセスメント実施日←リクエストパラメータ.作成日
                cpnTucGdlRirekiRecord.setAsJisshiDateYmd(inDto.getKijunbiYmd());
                // 記載者ID←リクエストパラメータ.作成者ID
                cpnTucGdlRirekiRecord
                        .setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
                // Ｊの状態←"―"
                cpnTucGdlRirekiRecord.setAss12(CommonConstants.DASH);
                // 本人の基本動作等8←"―"
                cpnTucGdlRirekiRecord.setAss13(CommonConstants.DASH);
                // 全体のまとめ･特記事項←"●"
                cpnTucGdlRirekiRecord.setAss15(CommonConstants.MARU);
                // 改定フラグ←リクエストパラメータ.改定フラグ
                cpnTucGdlRirekiRecord.setNinteiFormF(
                        CommonDtoUtil.strValToInt(inDto.getNinteiFlg()));

                this.cpnTucGdlRirekiMapper.insertSelectiveAndReturn(cpnTucGdlRirekiRecord);

                outDto.setGdlId(CommonDtoUtil.objValToString(cpnTucGdlRirekiRecord.getGdlId()));

            } else if (CommonDtoUtil.isHistoryUpdate(inDto)) {
                // 5.1.2. リクエストパラメータ.履歴更新区分が"U":更新の場合
                // ＧＬ＿居宅アセスメント履歴】情報を更新する。
                // ＧＬ＿居宅アセスメント履歴
                CpnTucGdlRireki cpnTucGdlRirekiRecord = new CpnTucGdlRireki();
                // 全体のまとめ･特記事項←"●"
                cpnTucGdlRirekiRecord.setAss15(CommonConstants.MARU);
                // Ｊの状態←"―"
                cpnTucGdlRirekiRecord.setAss12(CommonConstants.DASH);
                // 本人の基本動作等8←"―"
                cpnTucGdlRirekiRecord.setAss13(CommonConstants.DASH);
                // アセスメント実施日←リクエストパラメータ.作成日
                cpnTucGdlRirekiRecord.setAsJisshiDateYmd(inDto.getKijunbiYmd());
                // 記載者ID←リクエストパラメータ.作成者ID
                cpnTucGdlRirekiRecord
                        .setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));

                final CpnTucGdlRirekiCriteria cpnTucGdlRirekiCriteria = new CpnTucGdlRirekiCriteria();
                // アセスメントID=リクエストパラメータ.アセスメントID
                // 計画期間ID＝変数.計画対象期間ID
                // 事業者ID＝リクエストパラメータ.事業者ID
                // 利用者ＩＤ＝リクエストパラメータ.利用者ＩＤ
                // 法人ID＝リクエストパラメータ.法人ID
                // 施設ID＝リクエストパラメータ.施設ID
                // 削除フラグ = 0（未削除データ）
                // 更新回数 = リクエストパラメータ.履歴更新回数

                cpnTucGdlRirekiCriteria.createCriteria()
                        .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                        .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()))
                        .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                        .andUserIdEqualTo(
                                CommonDtoUtil.strValToInt(inDto.getUserId()))
                        .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                        .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                if (CommonConstants.NUMBER_ZERO >= this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(
                        cpnTucGdlRirekiRecord,
                        cpnTucGdlRirekiCriteria)) {
                    throw new ExclusiveException();
                }
            }
            // 5.2. ＧＬ＿全体のまとめ・特記事項情報の保存処理u
            // 5.2.1. リクエストパラメータ.更新区分が"C":新規の場合、ＧＬ＿全体のまとめ・特記事項情報を登録する。
            if (CommonDtoUtil.isCreate(inDto)) {
                if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {
                    // 5.2.1.1. リクエストパラメータ.改定フラグが4（Ｈ21/４改訂版）の場合、【ＧＬ＿全体のまとめ・特記事項（Ｈ21改訂）】情報を登録する。
                    for (int i = 0; i < inDto.getGdl4NeceH21List().size(); i++) {
                        CpnTucGdl4NeceH21 cpnTucGdl4NeceH21 = new CpnTucGdl4NeceH21();

                        // リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．マスタID
                        cpnTucGdl4NeceH21.setMastId(
                                CommonDtoUtil.strValToInt(
                                        inDto.getGdl4NeceH21List().get(i).getMastId()));
                        // ※3：まとめフラグが「2：状況別入力」の場合、一件目データのみを設定する。
                        if (CommonConstants.MATOME_FLAG_SITUATION_INPUT.equals(inDto.getMatomeFlg())) {
                            // 特記事項←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．特記事項
                            cpnTucGdl4NeceH21.setMemoKnj(inDto.getGdl4NeceH21List().get(0).getMemoKnj());
                        } else {
                            // 特記事項←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．特記事項
                            cpnTucGdl4NeceH21.setMemoKnj(inDto.getGdl4NeceH21List().get(i).getMemoKnj());
                        }
                        // 計画期間ID←リクエストパラメータ.計画期間ID
                        cpnTucGdl4NeceH21.setSc1Id(
                                CommonDtoUtil.strValToInt(inDto.getSc1Id()));
                        // アセスメントID←リクエストパラメータ.アセスメントID
                        cpnTucGdl4NeceH21.setGdlId(
                                CommonDtoUtil.strValToInt(inDto.getRaiId()));
                        // 法人ID←リクエストパラメータ.法人ID
                        cpnTucGdl4NeceH21.setHoujinId(
                                CommonDtoUtil.strValToInt(inDto.getHoujinId()));
                        // 施設ID←リクエストパラメータ.施設ID
                        cpnTucGdl4NeceH21.setShisetuId(
                                CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                        // 事業者ID←リクエストパラメータ.事業者ID
                        cpnTucGdl4NeceH21.setSvJigyoId(
                                CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                        // 利用者ID←リクエストパラメータ.利用者ID
                        cpnTucGdl4NeceH21.setUserId(
                                CommonDtoUtil.strValToInt(inDto.getUserId()));
                        // 必要性1←"リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．必要性1
                        cpnTucGdl4NeceH21.setHitsuyo1(
                                CommonDtoUtil.strValToInt(
                                        inDto.getGdl4NeceH21List().get(0).getHitsuyo1()));
                        // 必要性2←"リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．必要性2
                        cpnTucGdl4NeceH21.setHitsuyo2(
                                CommonDtoUtil.strValToInt(
                                        inDto.getGdl4NeceH21List().get(0).getHitsuyo2()));
                        // 氏名←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．氏名
                        cpnTucGdl4NeceH21.setNameKnj(inDto.getGdl4NeceH21List().get(0).getNameKnj());
                        // 本人との関係←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．本人との関係
                        cpnTucGdl4NeceH21.setKankeiKnj(inDto.getGdl4NeceH21List().get(0).getKankeiKnj());
                        // 電話番号←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．電話番号
                        cpnTucGdl4NeceH21.setTel(inDto.getGdl4NeceH21List().get(0).getTel());
                        // FAX←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．FAX
                        cpnTucGdl4NeceH21.setFax(inDto.getGdl4NeceH21List().get(0).getFax());
                        // メールアドレス←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．メールアドレス
                        cpnTucGdl4NeceH21.setEMail(inDto.getGdl4NeceH21List().get(0).getEMail());
                        // 備考（災害時）←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．備考（災害時）
                        cpnTucGdl4NeceH21.setBikoSaigaiKnj(inDto.getGdl4NeceH21List().get(0).getBikoSaigaiKnj());
                        // 備考（権利擁護）←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．備考（権利擁護）
                        cpnTucGdl4NeceH21.setBikoKenriKnj(inDto.getGdl4NeceH21List().get(0).getBikoKenriKnj());

                        this.cpnTucGdl4NeceH21Mapper
                                .insertSelective(cpnTucGdl4NeceH21);
                    }

                } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {
                    // 5.2.1.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）】情報を登録する。

                    for (int i = 0; i < inDto.getGdl4NeceR3List().size(); i++) {
                        CpnTucGdl5NeceR3 cpnTucGdl5NeceR3 = new CpnTucGdl5NeceR3();

                        // リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．マスタID
                        cpnTucGdl5NeceR3.setMastId(
                                CommonDtoUtil.strValToInt(
                                        inDto.getGdl4NeceR3List().get(i).getMastId()));
                        // ※3：まとめフラグが「2：状況別入力」の場合、一件目データのみを設定する。
                        if (CommonConstants.MATOME_FLAG_SITUATION_INPUT.equals(inDto.getMatomeFlg())) {
                            // 特記事項←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．特記事項
                            cpnTucGdl5NeceR3.setMemoKnj(inDto.getGdl4NeceR3List().get(0).getMemoKnj());
                        } else {
                            // 特記事項←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．特記事項
                            cpnTucGdl5NeceR3.setMemoKnj(inDto.getGdl4NeceR3List().get(i).getMemoKnj());
                        }

                        // 計画期間ID←リクエストパラメータ.計画期間ID
                        cpnTucGdl5NeceR3.setSc1Id(
                                CommonDtoUtil.strValToInt(inDto.getSc1Id()));
                        // アセスメントID←リクエストパラメータ.アセスメントID
                        cpnTucGdl5NeceR3.setGdlId(
                                CommonDtoUtil.strValToInt(inDto.getRaiId()));
                        // 法人ID←リクエストパラメータ.法人ID
                        cpnTucGdl5NeceR3.setHoujinId(
                                CommonDtoUtil.strValToInt(inDto.getHoujinId()));
                        // 施設ID←リクエストパラメータ.施設ID
                        cpnTucGdl5NeceR3.setShisetuId(
                                CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                        // 事業者ID←リクエストパラメータ.事業者ID
                        cpnTucGdl5NeceR3.setSvJigyoId(
                                CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                        // 利用者ID←リクエストパラメータ.利用者ID
                        cpnTucGdl5NeceR3.setUserId(
                                CommonDtoUtil.strValToInt(inDto.getUserId()));
                        // 必要性1←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．必要性1
                        cpnTucGdl5NeceR3.setHitsuyo1(
                                CommonDtoUtil.strValToInt(inDto.getGdl4NeceR3List().get(0).getHitsuyo1()));
                        // 必要性2←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．必要性2
                        cpnTucGdl5NeceR3.setHitsuyo2(
                                CommonDtoUtil.strValToInt(inDto.getGdl4NeceR3List().get(0).getHitsuyo2()));
                        // 氏名←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．氏名
                        cpnTucGdl5NeceR3.setNameKnj(inDto.getGdl4NeceR3List().get(0).getNameKnj());
                        // 本人との関係←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．本人との関係
                        cpnTucGdl5NeceR3.setKankeiKnj(inDto.getGdl4NeceR3List().get(0).getKankeiKnj());
                        // 電話番号←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．電話番号
                        cpnTucGdl5NeceR3.setTel(inDto.getGdl4NeceR3List().get(0).getTel());
                        // FAX←リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．FAX
                        cpnTucGdl5NeceR3.setFax(inDto.getGdl4NeceR3List().get(0).getFax());
                        // メールアドレス←リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．メールアドレス
                        cpnTucGdl5NeceR3.setEMail(inDto.getGdl4NeceR3List().get(0).getEMail());
                        // 備考（災害時）←リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．備考（災害時）
                        cpnTucGdl5NeceR3.setBikoSaigaiKnj(inDto.getGdl4NeceR3List().get(0).getBikoSaigaiKnj());
                        // 備考（権利擁護）←リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．備考（権利擁護）
                        cpnTucGdl5NeceR3.setBikoKenriKnj(inDto.getGdl4NeceR3List().get(0).getBikoKenriKnj());
                        // 個別避難計画策定の有無←リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．個別避難計画策定の有無
                        cpnTucGdl5NeceR3.setKobetsuhinan(
                                CommonDtoUtil.strValToInt(
                                        inDto.getGdl4NeceR3List().get(0).getKobetsuhinan()));

                        this.cpnTucGdl5NeceR3Mapper
                                .insertSelective(cpnTucGdl5NeceR3);
                    }
                }
            } else if (CommonDtoUtil.isUpdate(inDto)) {
                // 5.2.2. リクエストパラメータ.更新区分が"U":更新の場合、ＧＬ＿全体のまとめ・特記事項情報を更新する。
                if (CommonConstants.KAI_TEI_FLAG_H21_4
                        .equals(inDto.getNinteiFlg())) {
                    // 5.2.2.1. リクエストパラメータ.改定フラグが4（Ｈ21/４改訂版）の場合、【ＧＬ＿全体のまとめ・特記事項（Ｈ21改訂）】情報を更新する。
                    for (int i = 0; i < inDto.getGdl4NeceH21List().size(); i++) {
                        CpnTucGdl4NeceH21 cpnTucGdl4NeceH21Record = new CpnTucGdl4NeceH21();
                        // ※3：まとめフラグが「2：状況別入力」の場合、一件目データのみを設定する。
                        if (CommonConstants.MATOME_FLAG_SITUATION_INPUT.equals(inDto.getMatomeFlg())) {
                            // 特記事項←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．特記事項
                            cpnTucGdl4NeceH21Record.setMemoKnj(inDto.getGdl4NeceH21List().get(0).getMemoKnj());
                        } else {
                            // 特記事項←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．特記事項
                            cpnTucGdl4NeceH21Record.setMemoKnj(inDto.getGdl4NeceH21List().get(i).getMemoKnj());
                        }

                        // 必要性1←"リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．必要性1
                        cpnTucGdl4NeceH21Record.setHitsuyo1(
                                CommonDtoUtil.strValToInt(
                                        inDto.getGdl4NeceH21List().get(0).getHitsuyo1()));
                        // 必要性2←"リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．必要性2
                        cpnTucGdl4NeceH21Record.setHitsuyo2(
                                CommonDtoUtil.strValToInt(
                                        inDto.getGdl4NeceH21List().get(0).getHitsuyo2()));
                        // 氏名←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．氏名
                        cpnTucGdl4NeceH21Record.setNameKnj(inDto.getGdl4NeceH21List().get(0).getNameKnj());
                        // 本人との関係←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．本人との関係
                        cpnTucGdl4NeceH21Record.setKankeiKnj(inDto.getGdl4NeceH21List().get(0).getKankeiKnj());
                        // 電話番号←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．電話番号
                        cpnTucGdl4NeceH21Record.setTel(inDto.getGdl4NeceH21List().get(0).getTel());
                        // FAX←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．FAX
                        cpnTucGdl4NeceH21Record.setFax(inDto.getGdl4NeceH21List().get(0).getFax());
                        // メールアドレス←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．メールアドレス
                        cpnTucGdl4NeceH21Record.setEMail(inDto.getGdl4NeceH21List().get(0).getEMail());
                        // 備考（災害時）←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．備考（災害時）
                        cpnTucGdl4NeceH21Record
                                .setBikoSaigaiKnj(inDto.getGdl4NeceH21List().get(0).getBikoSaigaiKnj());
                        // 備考（権利擁護）←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．備考（権利擁護）
                        cpnTucGdl4NeceH21Record.setBikoKenriKnj(inDto.getGdl4NeceH21List().get(0).getBikoKenriKnj());

                        final CpnTucGdl4NeceH21Criteria cpnTucGdl4NeceH21Criteria = new CpnTucGdl4NeceH21Criteria();
                        // ID＝リクエストパラメータ.アセスメントID
                        // 計画期間ID＝【変数】.計画対象期間ID
                        // マスタID＝リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．マスタID
                        // 法人ID＝リクエストパラメータ.法人ID
                        // 施設ID＝リクエストパラメータ.施設ID
                        // 事業者ID＝リクエストパラメータ.事業者ID
                        // 利用者ＩＤ＝リクエストパラメータ.利用者ＩＤ
                        // 削除フラグ = 0（未削除データ）
                        // 更新回数 = リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト．更新回数
                        cpnTucGdl4NeceH21Criteria.createCriteria()
                                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                                .andSc1IdEqualTo(sc1IdTmp)
                                .andSvJigyoIdEqualTo(Integer
                                        .parseInt(inDto.getGdl4NeceH21List().get(i).getMastId()))
                                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                                .andHoujinIdEqualTo(
                                        CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                                .andShisetuIdEqualTo(
                                        CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                        if (CommonConstants.NUMBER_ZERO >= this.cpnTucGdl4NeceH21Mapper
                                .updateByCriteriaSelective(
                                        cpnTucGdl4NeceH21Record,
                                        cpnTucGdl4NeceH21Criteria)) {
                            throw new ExclusiveException();
                        }
                    }
                } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {
                    // 5.2.2.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）】情報を更新する。

                    for (int i = 0; i < inDto.getGdl4NeceR3List().size(); i++) {
                        CpnTucGdl5NeceR3 cpnTucGdl5NeceR3Record = new CpnTucGdl5NeceR3();
                        // ※3：まとめフラグが「2：状況別入力」の場合、一件目データのみを設定する。
                        if (CommonConstants.MATOME_FLAG_SITUATION_INPUT.equals(inDto.getMatomeFlg())) {
                            // 特記事項←リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．特記事項
                            cpnTucGdl5NeceR3Record.setMemoKnj(inDto.getGdl4NeceR3List().get(0).getMemoKnj());
                        } else {
                            // 特記事項←リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．特記事項
                            cpnTucGdl5NeceR3Record.setMemoKnj(inDto.getGdl4NeceR3List().get(i).getMemoKnj());
                        }
                        // 必要性1←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．必要性1
                        cpnTucGdl5NeceR3Record.setHitsuyo1(
                                CommonDtoUtil.strValToInt(inDto.getGdl4NeceR3List().get(0).getHitsuyo1()));
                        // 必要性2←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．必要性2
                        cpnTucGdl5NeceR3Record.setHitsuyo2(
                                CommonDtoUtil.strValToInt(inDto.getGdl4NeceR3List().get(0).getHitsuyo2()));
                        // 氏名←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．氏名
                        cpnTucGdl5NeceR3Record.setNameKnj(inDto.getGdl4NeceR3List().get(0).getNameKnj());
                        // 本人との関係←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．本人との関係
                        cpnTucGdl5NeceR3Record.setKankeiKnj(inDto.getGdl4NeceR3List().get(0).getKankeiKnj());
                        // 電話番号←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．電話番号
                        cpnTucGdl5NeceR3Record.setTel(inDto.getGdl4NeceR3List().get(0).getTel());
                        // FAX←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．FAX
                        cpnTucGdl5NeceR3Record.setFax(inDto.getGdl4NeceR3List().get(0).getFax());
                        // メールアドレス←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．メールアドレス
                        cpnTucGdl5NeceR3Record.setEMail(inDto.getGdl4NeceR3List().get(0).getEMail());
                        // 備考（災害時）←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．備考（災害時）
                        cpnTucGdl5NeceR3Record.setBikoSaigaiKnj(inDto.getGdl4NeceR3List().get(0).getBikoSaigaiKnj());
                        // 備考（権利擁護）←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．備考（権利擁護）
                        cpnTucGdl5NeceR3Record.setBikoKenriKnj(inDto.getGdl4NeceR3List().get(0).getBikoKenriKnj());
                        // 個別避難計画策定の有無←リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．個別避難計画策定の有無
                        cpnTucGdl5NeceR3Record.setKobetsuhinan(CommonDtoUtil
                                .strValToInt(inDto.getGdl4NeceR3List().get(0).getKobetsuhinan()));

                        // アセスメントID＝リクエストパラメータ.アセスメントID
                        // 計画期間ID＝【変数】.計画対象期間ID
                        // マスタID＝リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．マスタID
                        // 法人ID＝リクエストパラメータ.法人ID
                        // 施設ID＝リクエストパラメータ.施設ID
                        // 事業者ID＝リクエストパラメータ.事業者ID
                        // 利用者ＩＤ＝リクエストパラメータ.利用者ＩＤ
                        // 削除フラグ = 0（未削除データ）
                        // 更新回数＝リクエストパラメータ.リクエストパラメータ.ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト．更新回数
                        final CpnTucGdl5NeceR3Criteria cpnTucGdl5NeceR3Criteria = new CpnTucGdl5NeceR3Criteria();
                        cpnTucGdl5NeceR3Criteria.createCriteria()
                                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                                .andSc1IdEqualTo(sc1IdTmp)
                                .andSvJigyoIdEqualTo(Integer
                                        .parseInt(inDto.getGdl4NeceR3List().get(i).getMastId()))
                                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                                .andHoujinIdEqualTo(
                                        CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                                .andShisetuIdEqualTo(
                                        CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                        if (CommonConstants.NUMBER_ZERO >= this.cpnTucGdl5NeceR3Mapper
                                .updateByCriteriaSelective(
                                        cpnTucGdl5NeceR3Record,
                                        cpnTucGdl5NeceR3Criteria)) {
                            throw new ExclusiveException();
                        }

                    }
                }
            }

            // 5.3.リクエストパラメータ.【課題と目標リスト】の件数分、【ＧＬ＿課題と目標】情報を保存する。
            if (CommonConstants.FAILURE
                    .equals(assessmentHomeLogic.homeLogicCpnTucGdlKadai(loginDto, sc1IdTmp))) {
                throw new ExclusiveException();
            }

        }

        /*
         * ===============6. レスポンスを返却する。===============
         * 
         */
        // 期間ID
        outDto.setSc1Id(CommonDtoUtil.objValToString(sc1IdTmp));

        LOG.info(Constants.END);
        return outDto;

    }

}
