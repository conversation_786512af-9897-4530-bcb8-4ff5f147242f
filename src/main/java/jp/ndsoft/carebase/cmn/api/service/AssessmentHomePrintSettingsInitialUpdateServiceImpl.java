package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.logic.AssessmentHomePrintSettingsLogic;
import jp.ndsoft.carebase.cmn.api.logic.PrintSettingLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomePrintSettingsInitialUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomePrintSettingsInitialUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomePrintSettinguserSwitchingSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomePrintSettinguserSwitchingSelectServiceOutDto;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;

/**
 * @since 2025.07.18
 * <AUTHOR>
 * @implNote GUI00815_印刷設定 初期情報取得
 */
@Service
public class AssessmentHomePrintSettingsInitialUpdateServiceImpl extends
        UpdateServiceImpl<AssessmentHomePrintSettingsInitialUpdateServiceInDto, AssessmentHomePrintSettingsInitialUpdateServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 印刷設定共通クラス */
    @Autowired
    private PrintSettingLogic printSettingLogic;

    /** GUI00815_印刷設定 利用者切替共通クラス */
    @Autowired
    private AssessmentHomePrintSettingsLogic assessmentHomePrintSettingsLogic;

    /**
     * 初期情報取得
     * 
     * @param inDto 初期情報取得サービス入力Dto
     * @return 初期情報取得サービス出力Dto
     * @throws Exception Exception
     */
    @Override
    protected AssessmentHomePrintSettingsInitialUpdateServiceOutDto mainProcess(
            AssessmentHomePrintSettingsInitialUpdateServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        AssessmentHomePrintSettingsInitialUpdateServiceOutDto outDto = new AssessmentHomePrintSettingsInitialUpdateServiceOutDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2.期間管理フラグの取得を行う。===============
         * 
         */

        // 共通「API定義書_APINo(959)_初期情報取得.xlsx」の「5」を参照
        // API定義書_APINo(959)_初期情報取得.xlsx」の「5.」
        // 5.下記の共通関数「期間管理するかしないかを判断する」を利用し、期間管理フラグの取得を行う。
        String kikanFlg = this.printSettingLogic.getKghKrkKikan(
                inDto.getMenu2Knj(),
                inDto.getMenu3Knj(),
                inDto.getSvJigyoId(),
                inDto.getShisetuId());

        /*
         * ===============3.印刷設定初期値情報とアセスメント履歴一覧の取得===============
         * 
         */
        // 共通「API定義書_APINo(913)_利用者切替.xlsx」の「2.」～「4.」を参照
        AssessmentHomePrintSettinguserSwitchingSelectServiceInDto userSwitchingSelectServiceInDto = new AssessmentHomePrintSettinguserSwitchingSelectServiceInDto();

        // 利用者ID */
        userSwitchingSelectServiceInDto.setUserId(inDto.getUserId());
        // システム略称 */
        userSwitchingSelectServiceInDto.setSysRyaku(inDto.getSysRyaku());
        // セクション名 */
        userSwitchingSelectServiceInDto.setSectionName(inDto.getSectionName());
        // 法人ID */
        userSwitchingSelectServiceInDto.setHoujinId(inDto.getHoujinId());
        // 施設ID */
        userSwitchingSelectServiceInDto.setShisetuId(inDto.getShisetuId());
        // 期間管理フラグ */
        userSwitchingSelectServiceInDto.setKikanFlg(kikanFlg);
        // 職員ID */
        userSwitchingSelectServiceInDto.setShokuId(inDto.getShokuId());
        // 事業者ID */
        userSwitchingSelectServiceInDto.setSvJigyoId(inDto.getSvJigyoId());
        // 改訂範囲 */
        userSwitchingSelectServiceInDto.setRevisionRange(inDto.getRevisionRange());
        // 履歴選択フラグ */
        userSwitchingSelectServiceInDto.setHistorySelectFlag(inDto.getHistorySelectFlag());
        // インデックス */
        userSwitchingSelectServiceInDto.setIndex(inDto.getIndex());
        // 個人情報使用フラグ */
        userSwitchingSelectServiceInDto.setKojinhogoUsedFlg(inDto.getKojinhogoUsedFlg());
        // 個人情報番号 */
        userSwitchingSelectServiceInDto.setSectionAddNo(inDto.getSectionAddNo());
        // システムコード */
        userSwitchingSelectServiceInDto.setGsysCd(inDto.getGsysCd());

        AssessmentHomePrintSettinguserSwitchingSelectServiceOutDto userSwitchingSelectServiceOutDto = this.assessmentHomePrintSettingsLogic
                .userSwitchingSelect(userSwitchingSelectServiceInDto);

        /*
         * ===============5. レスポンスを返却する。===============
         * 
         */
        // 印刷設定情報リスト
        outDto.setPrtList(userSwitchingSelectServiceOutDto.getPrtList());
        // システムINI情報
        outDto.setSysIniInfo(userSwitchingSelectServiceOutDto.getSysIniInfo());
        // 期間履歴情報リスト
        outDto.setPeriodHistoryList(userSwitchingSelectServiceOutDto.getPeriodHistoryList());
        // 期間管理フラグ
        outDto.setKikanFlg(kikanFlg);

        LOG.info(Constants.END);

        return outDto;
    }

}
