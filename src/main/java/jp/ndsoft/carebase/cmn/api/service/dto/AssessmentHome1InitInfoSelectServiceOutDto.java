package jp.ndsoft.carebase.cmn.api.service.dto;


import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Gdl4FaceH21Info;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Gdl4FaceH30Info;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Gdl5FaceInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794NewInfo;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.04.17
 * <AUTHOR>
 * @description GUI00794_［アセスメント］画面（居宅）（1） 初期情報取得サービス出力Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AssessmentHome1InitInfoSelectServiceOutDto extends IDtoImpl {
    /**
     * serialVersionUID.
     */
    private static final long serialVersionUID = 1L;

    /** フェースシート（Ｈ30)情報 */
    private Gui00794Gdl4FaceH30Info gdl4FaceH30Info;

    /** フェースシート（Ｈ21)情報 */
    private Gui00794Gdl4FaceH21Info gdl4FaceH21Info;

    /** フェースシート（R3）情報 */
    private Gui00794Gdl5FaceInfo gdl5FaceInfo;

    /** 相談受付者名 */
    private String shokuinName;

    /** 新規表示情報 */
    private Gui00794NewInfo newInfo;

}
