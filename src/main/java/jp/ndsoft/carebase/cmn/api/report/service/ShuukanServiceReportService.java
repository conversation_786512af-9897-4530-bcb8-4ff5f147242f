package jp.ndsoft.carebase.cmn.api.report.service;

import java.io.File;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.report.dto.ShuukanServiceReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ShuukanServiceReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.logic.ShuukanServiceReportLogic;
import jp.ndsoft.carebase.cmn.api.report.model.ShuukanServiceReportParameterModel;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * 
 * U00852_週間サービス計画表
 * 
 * <AUTHOR>
 */
@Service("ShuukanServiceReport")
public class ShuukanServiceReportService
        extends PdfReportServiceImpl<ShuukanServiceReportParameterModel, ShuukanServiceReportServiceOutDto> {

    /**
     * ロガー.
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // 週間サービス計画表帳票出力ロジック
    @Autowired
    private ShuukanServiceReportLogic shuukanServiceReportLogic;

    /**
     * 帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(ShuukanServiceReportParameterModel model,
            ShuukanServiceReportServiceOutDto outDto) throws Exception {

        LOG.info(Constants.START);

        // infoInDtoを設定する。
        ShuukanServiceReportServiceInDto infoInDto = shuukanServiceReportLogic.getU00852ReportParameters(model);

        // ノート情報格納配列
        List<ShuukanServiceReportServiceInDto> shuukanServiceInfoList = new ArrayList<ShuukanServiceReportServiceInDto>();
        shuukanServiceInfoList.add(infoInDto);

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(shuukanServiceInfoList);
        infoInDto.setDataSource(dataSource);

        LOG.info(Constants.END);
        return infoInDto;
    }

    /**
     * 帳票出力
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final ShuukanServiceReportParameterModel model,
            final ShuukanServiceReportServiceOutDto outDto) throws Exception {

        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final ShuukanServiceReportServiceInDto reportParameter = (ShuukanServiceReportServiceInDto) getReportParameters(
                model, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // コンパイル
        final JasperReport jasperFile = shuukanServiceReportLogic.getJasperReport(getFwProps(), reportParameter, model);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile, reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(model, jasperPrint);

        super.setFilepath(model, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }

}
