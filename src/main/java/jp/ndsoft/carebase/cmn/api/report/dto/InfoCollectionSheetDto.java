package jp.ndsoft.carebase.cmn.api.report.dto;

import lombok.Data;

/**
 * 
 * 情報収集シートの詳細リスト
 * 
 * <AUTHOR>
 */
@Data
public class InfoCollectionSheetDto {

    /** 二級メニュー */
    private String level2Knj;
    /** 三級メニューID */
    private String level3KoumokuNo;
    /** 情報項目 */
    private String level3Knj;
    /** 検討 */
    private String kento;
    /** 具体的状況 */
    private String memo1Knj;
    /** 三級メニューIDのフォント */
    private String level3KoumokuNoFont;
    /** 情報項目のフォント */
    private String level3KnjFont;
    /** 書式2フラグ */
    private String shosiki2Flg;

    public InfoCollectionSheetDto() {
        /** 二級メニュー */
        this.level2Knj = "";
        /** 三級メニューID */
        this.level3KoumokuNo = "";
        /** 三級メニューIDのフォント */
        this.level3KoumokuNoFont = "";
        /** 情報項目 */
        this.level3Knj = "";
        /** 情報項目のフォント */
        this.level3KnjFont = "";
        /** 具体的状況 */
        this.memo1Knj = "";
        /** 検討 */
        this.kento = "";
    }
}