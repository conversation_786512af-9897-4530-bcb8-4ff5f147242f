package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.service.dto.IntegratedPlanMasterUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.IntegratedPlanMasterUpdateServiceOutDto;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkSsmMapper;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsm;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsmCriteria;

/**
 * GUI00970_総合計画マスタ情報保存サービス.
 *
 * <AUTHOR>
 */
@Service
public class IntegratedPlanMasterUpdateServiceImpl
        extends UpdateServiceImpl<IntegratedPlanMasterUpdateServiceInDto, IntegratedPlanMasterUpdateServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;
    @Autowired
    private KghMocKrkSsmMapper kghMocKrkSsmMapper;

    /**
     * 保存処理
     * 
     * @param inDto
     * @return
     * @throws Exception Exception
     */
    @Override
    protected IntegratedPlanMasterUpdateServiceOutDto mainProcess(IntegratedPlanMasterUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // DTOOUT情報
        IntegratedPlanMasterUpdateServiceOutDto outDto = new IntegratedPlanMasterUpdateServiceOutDto();

        // 総合計画マスタ情報取得
        KrkSsmInfoByCriteriaInEntity inEntity = new KrkSsmInfoByCriteriaInEntity();
        inEntity.setShisetuId(Integer.valueOf(inDto.getShisetuId()));
        inEntity.setSvJigyoId(Integer.valueOf(inDto.getSvJigyoId()));
        inEntity.setBunrui1Id(CommonConstants.BUNRUI1_ID_2);
        inEntity.setBunrui2Id(CommonConstants.BUNRUI2_ID_13);
        List<KrkSsmInfoOutEntity> list = kghMocKrkSsmSelectMapper.findKrkSsmInfoByCriteria(inEntity);
        boolean insertFlg = true;
        if (!list.isEmpty() && list.size() > 0) {
            for (KrkSsmInfoOutEntity entity : list) {
                if (CommonConstants.NUMBER_ONE.equals(entity.getBunrui3Id())) {
                    insertFlg = false;
                    break;
                }
            }
        }

        KghMocKrkSsm kghMocKrkSsm = new KghMocKrkSsm();
        // 登録処理を行う
        if (insertFlg) {
            kghMocKrkSsm.setShisetuId(Integer.valueOf(inDto.getShisetuId()));
            kghMocKrkSsm.setSvJigyoId(Integer.valueOf(inDto.getSvJigyoId()));
            kghMocKrkSsm.setBunrui1Id(CommonConstants.BUNRUI1_ID_2);
            kghMocKrkSsm.setBunrui2Id(CommonConstants.BUNRUI2_ID_13);
            kghMocKrkSsm.setBunrui3Id(CommonConstants.NUMBER_ONE);
            kghMocKrkSsm.setIntValue(Integer.valueOf(inDto.getYokaigo()));
            kghMocKrkSsm.setDoubleValue(CommonConstants.DOUBLE_ZERO);
            kghMocKrkSsm.setText1Knj(null);

            // CommonDaoUtil.setInsertCommonColumns(kghMocKrkSsm);
            kghMocKrkSsmMapper.insertSelective(kghMocKrkSsm);
        } else {
            // BigInteger modifiedCnt = new BigInteger(inDto.getYokaigoModifiedCnt());
            KghMocKrkSsmCriteria criteria = new KghMocKrkSsmCriteria();
            criteria.createCriteria()
                    .andShisetuIdEqualTo(Integer.valueOf(inDto.getShisetuId()))
                    .andSvJigyoIdEqualTo(Integer.valueOf(inDto.getSvJigyoId()))
                    .andBunrui1IdEqualTo(
                            CommonConstants.BUNRUI1_ID_2)
                    .andBunrui2IdEqualTo(
                            CommonConstants.BUNRUI2_ID_13)
                    .andBunrui3IdEqualTo(CommonConstants.NUMBER_ONE);
            // .andModifiedCntEqualTo(modifiedCnt);

            kghMocKrkSsm.setShisetuId(Integer.valueOf(inDto.getShisetuId()));
            kghMocKrkSsm.setSvJigyoId(Integer.valueOf(inDto.getSvJigyoId()));
            kghMocKrkSsm.setBunrui1Id(CommonConstants.BUNRUI1_ID_2);
            kghMocKrkSsm.setBunrui2Id(CommonConstants.BUNRUI2_ID_13);
            kghMocKrkSsm.setBunrui3Id(CommonConstants.NUMBER_ONE);
            kghMocKrkSsm.setIntValue(Integer.valueOf(inDto.getYokaigo()));
            // CommonDaoUtil.setUpdateCommonColumns(kghMocKrkSsm, modifiedCnt);
            int count = kghMocKrkSsmMapper.updateByCriteriaSelective(kghMocKrkSsm, criteria);
            if (count <= 0) {
                throw new ExclusiveException();
            }
        }
        LOG.info(Constants.END);
        return outDto;
    }
}
