package jp.ndsoft.carebase.cmn.api.service.dto;

import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * @since 2025.04.24
 * <AUTHOR> Bui The Khang
 * @implNote GUI01023_課題整理総括取込画面
 */
@Getter
@Setter
public class Gui01023PlanPeriodInfoOutDto extends IDtoImpl {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;
    // ヘッダID
    private String kss1Id;

    // 計画期間ID
    private String sc1Id;

    // 法人ID
    private String houjinId;

    // 施設ID
    private String shisetuId;

    // 事業者ID
    private String svJigyoId;

    // 利用者ID
    private String userid;

    // 作成日
    private String createYmd;

    // 職員ID
    private String shokuId;

    // 様式
    private String youshikiId;

    // 作成者氏名
    private String shokuName;

    // 更新回数
    // private String modifiedCnt;

}
