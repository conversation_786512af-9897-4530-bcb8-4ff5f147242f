package jp.ndsoft.carebase.cmn.api.report.service;

import java.io.File;
import java.lang.invoke.MethodHandles;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.report.dto.ReportCommonGetOptionDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ServiceUseAnnexedTableReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ServiceUseAnnexedTableReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.logic.ServiceUseAnnexedTableReportLogic;
import jp.ndsoft.carebase.cmn.api.report.model.ServiceUseAnnexedTableReportParameterModel;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * @since 2025.07.21
 * <AUTHOR> V00231_サービス利用票別表
 */
@Service("ServiceUseAnnexedTableReport")
public class ServiceUseAnnexedTableReportService extends
        PdfReportServiceImpl<ServiceUseAnnexedTableReportParameterModel, ServiceUseAnnexedTableReportServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** サービス利用票別表 帳票出力ロジック */
    @Autowired
    ServiceUseAnnexedTableReportLogic serviceUseAnnexedTableReportLogic;

    /** 印刷オプション */
    private ReportCommonGetOptionDto printOption;

    /**
     * 帳票パラメータ取得
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(ServiceUseAnnexedTableReportParameterModel model,
            ServiceUseAnnexedTableReportServiceOutDto outDto) throws Exception {
        LOG.info(Constants.START);

        // 帳票用データ詳細
        ServiceUseAnnexedTableReportServiceInDto infoInDto = new ServiceUseAnnexedTableReportServiceInDto();

        printOption = new ReportCommonGetOptionDto();

        // ノート情報格納配列
        List<ServiceUseAnnexedTableReportServiceInDto> surveyContentsListInfoList = serviceUseAnnexedTableReportLogic
                .getV00231ServiceUseAnnexedTableReportParameters(model, printOption);

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(surveyContentsListInfoList);
        infoInDto.setDataSource(dataSource);
        LOG.info(Constants.END);

        return infoInDto;
    }

    /**
     * 帳票出力
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final ServiceUseAnnexedTableReportParameterModel model,
            final ServiceUseAnnexedTableReportServiceOutDto outDto) throws Exception {

        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final ServiceUseAnnexedTableReportServiceInDto reportParameter = (ServiceUseAnnexedTableReportServiceInDto) getReportParameters(
                model, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // コンパイル
        final JasperReport jasperFile = serviceUseAnnexedTableReportLogic.getJasperReport(getFwProps(), reportParameter,
                model,
                printOption);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile, reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(model, jasperPrint);

        super.setFilepath(model, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }
}
