package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.service.dto.CareCheckMasterSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.CareCheckMasterSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.05.20
 * <AUTHOR> BUI HAI KIEN
 * @implNote GUI00830_ケアチェックマスタ画面 初期情報取得.
 */
@Service
public class CareCheckMasterSelectServiceImpl
        extends
        SelectServiceImpl<CareCheckMasterSelectServiceInDto, CareCheckMasterSelectServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // 分類2 ＝ 2
    private static final int BUNRUI2 = 2;

    // 分類3 ＝ 2
    private static final int BUNRUI3_2 = 2;

    // 分類3 ＝ 3
    private static final int BUNRUI3_3 = 3;

    // 分類3 ＝ 4
    private static final int BUNRUI3_4 = 4;

    // 分類3 ＝ 5
    private static final int BUNRUI3_5 = 5;

    // 分類3 ＝ 6
    private static final int BUNRUI3_6 = 6;

    // 分類3 ＝ 7
    private static final int BUNRUI3_7 = 7;

    // 分類3 ＝ 8
    private static final int BUNRUI3_8 = 8;

    // 画面記号 ＝ 0
    private static final String DEFAULT_PAGE_VALUE = "0";

    // 印刷記号 ＝ 1
    private static final String DEFAULT_PRING_VALUE = "1";

    /** ｱｾｽﾒﾝﾄ検討表(包括)履歴情報を取得する */
    @Autowired
    private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;

    /**
     * ケアチェックマスタ情報取得
     * 
     * @param inDto GUI00830_ケアチェックマスタ画面 情報入力DTO.
     * @return GUI00830_ケアチェックマスタ画面 情報出力DTO
     * @throws Exception Exception
     */
    @Override
    protected CareCheckMasterSelectServiceOutDto mainProcess(
            CareCheckMasterSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // DTOOUT情報
        CareCheckMasterSelectServiceOutDto outDto = new CareCheckMasterSelectServiceOutDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2.初期設定マスタの検索処理を行う。===============
         * 
         */
        KrkSsmInfoByCriteriaInEntity krkSsmInfoByCriteriaInEntity = new KrkSsmInfoByCriteriaInEntity();
        // 施設ID
        krkSsmInfoByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        krkSsmInfoByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 分類１
        krkSsmInfoByCriteriaInEntity.setBunrui1Id(BUNRUI2);
        // 分類２
        krkSsmInfoByCriteriaInEntity.setBunrui2Id(BUNRUI2);

        List<KrkSsmInfoOutEntity> krkSsmInfoOutEntities = kghMocKrkSsmSelectMapper
                .findKrkSsmInfoByCriteria(krkSsmInfoByCriteriaInEntity);

        /*
         * ===============3. レスポンスパラメータ各項目の設定を行う。===============
         * 
         */
        // 3.1.レスポンスパラメータ以下項目の初期値を設定する
        // 施設ID
        outDto.setShisetuId(inDto.getShisetuId());
        // 事業者ID
        outDto.setSvJigyoId(inDto.getSvJigyoId());
        // 画面記号
        outDto.setPageValue(DEFAULT_PAGE_VALUE);
        // 印刷記号
        outDto.setPrintValue(DEFAULT_PRING_VALUE);

        // 3.2.処理(2)から取得したデータ件数>０の場合、レスポンスパラメータ各項目の設定処理を行う
        if (krkSsmInfoOutEntities != null && !krkSsmInfoOutEntities.isEmpty()) {
            for (KrkSsmInfoOutEntity entity : krkSsmInfoOutEntities) {
                Integer bunrui3Id = entity.getBunrui3Id();
                if (bunrui3Id == null) {
                    continue;
                }

                switch (bunrui3Id) {
                    case BUNRUI3_2:
                        outDto.setPageValue(CommonDtoUtil.objValToString(entity.getIntValue()));
                        break;

                    case BUNRUI3_3:
                        outDto.setPrintValue(
                                CommonDtoUtil.objValToString(entity.getIntValue()));
                        break;

                    case BUNRUI3_4:
                    case BUNRUI3_5:
                    case BUNRUI3_6:
                    case BUNRUI3_7:
                    case BUNRUI3_8:
                        String text = entity.getText1Knj();

                        if (bunrui3Id.equals(BUNRUI3_4)) {
                            outDto.setPrint1(text);
                        } else if (bunrui3Id.equals(BUNRUI3_5)) {
                            outDto.setPrint2(text);
                        } else if (bunrui3Id.equals(BUNRUI3_6)) {
                            outDto.setPrint3(text);
                        } else if (bunrui3Id.equals(BUNRUI3_7)) {
                            outDto.setPrint4(text);
                        } else if (bunrui3Id.equals(BUNRUI3_8)) {
                            outDto.setPrint5(text);
                        }
                        break;

                    default:
                        break;
                }
            }
        }

        LOG.info(Constants.END);
        // 4. レスポンスを返却する。
        return outDto;
    }
}
