package jp.ndsoft.carebase.cmn.api.logic;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.dto.AssessmentHomePrintSettingsPeriodHistoryDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.PrintSettingInfoDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.SysIniInfoDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomePrintSettinguserSwitchingSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomePrintSettinguserSwitchingSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.FreeAssessmentFacePrintSettingsUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMucShokuinKanaKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMucShokuinKanaKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KikanCpnTucGdlRirekiInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KikanCpnTucGdlRirekiInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KikanCpnTucGdlRirekiSelectMapper;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * GUI00815_印刷設定 利用者切替共通クラス
 * 
 * <AUTHOR>
 */
@Component
public class AssessmentHomePrintSettingsLogic {
    /** 印刷設定共通クラス */
    @Autowired
    private PrintSettingLogic printSettingLogic;

    /** 計画期間の居宅アセスメント履歴取得 */
    @Autowired
    private KikanCpnTucGdlRirekiSelectMapper kikanCpnTucGdlRirekiSelectMapper;

    /** 職員一覧取得 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /**
     * API定義書_APINo(913)_利用者切替.取得の「2.」～「4.」
     * 
     * @param inDto GUI00815_印刷設定 アセスメント履歴一覧情報取得サービス入力Dto
     * @return GUI00815_印刷設定 アセスメント履歴一覧情報取得サービス出力Dto
     * @throws Exception 例外
     */
    public AssessmentHomePrintSettinguserSwitchingSelectServiceOutDto userSwitchingSelect(
            AssessmentHomePrintSettinguserSwitchingSelectServiceInDto inDto) throws Exception {
        AssessmentHomePrintSettinguserSwitchingSelectServiceOutDto outDto = new AssessmentHomePrintSettinguserSwitchingSelectServiceOutDto();

        // アセスメント履歴リスト
        List<AssessmentHomePrintSettingsPeriodHistoryDto> periodHistoryList = new ArrayList<>();

        // 2. 計画期間の居宅アセスメント履歴取得
        // 2.1. 下記の計画期間の居宅アセスメント履歴一覧取得DAOを利用し、ケアチェック表と共通期間一覧を取得する。
        List<KikanCpnTucGdlRirekiInfoOutEntity> kikanCpnTucGdlRirekiInfoList = this
                .findKikanCpnTucGdlRirekiInfoByCriteria(inDto.getSvJigyoId(), inDto.getUserId());

        // 2.2.リクエストパラメータ詳細．改訂範囲＝2の場合、上記2.1取得した結果を下記条件を満たしたレコードをレスポンスパラメータ.アセスメント履歴一覧に保存する
        // ・絞込み条件： OUTPUT情報.改定フラグ＞＝4（4：H21）
        if (CommonDtoUtil.checkStringEqual(inDto.getRevisionRange(), CommonConstants.STR_2)) {
            kikanCpnTucGdlRirekiInfoList = kikanCpnTucGdlRirekiInfoList.stream()
                    .filter(kikanCpnTucGdlRirekiInfo -> ObjectUtils
                            .isNotEmpty(kikanCpnTucGdlRirekiInfo.getNinteiFormF())
                            && kikanCpnTucGdlRirekiInfo.getNinteiFormF() >= CommonDtoUtil
                                    .strValToInt(CommonConstants.KAI_TEI_FLAG_H21_4))
                    .collect(Collectors.toList());
        }

        // 2.3.下記の職員一覧取得DAOを利用し、職員一覧を取得する。
        List<CpnMucShokuinKanaKnjOutEntity> shokuinInfoList = this.findCpnMucShokuinKanaKnjByCriteria();

        // 2.4.上記2.2設定したアセスメント履歴一覧再編集処理
        if (CollectionUtils.isNotEmpty(kikanCpnTucGdlRirekiInfoList)) {
            // (2).計画期間管理フラグよりアセスメント履歴一覧を下記ソート処理する
            // ①.リクエストパラメータ.期間管理フラグは（1：管理する）の場合
            if (CommonDtoUtil.checkStringEqual(inDto.getKikanFlg(), CommonConstants.PERIOD_MANAGE_FLG)) {
                // ソートキー： 開始日 降序,
                // 終了日 降序,
                // 期間ID 降序,
                // アセスメント実施日 降序,
                // アセスメントID 降序
                kikanCpnTucGdlRirekiInfoList.sort(Comparator
                        .comparing(
                                KikanCpnTucGdlRirekiInfoOutEntity::getStartYmd,
                                Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(
                                KikanCpnTucGdlRirekiInfoOutEntity::getEndYmd,
                                Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(
                                KikanCpnTucGdlRirekiInfoOutEntity::getSc1Id,
                                Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(
                                KikanCpnTucGdlRirekiInfoOutEntity::getAsJisshiDateYmd,
                                Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(
                                KikanCpnTucGdlRirekiInfoOutEntity::getGdlId,
                                Comparator.nullsLast(Comparator.reverseOrder())));
            } else {
                // ②.期間管理フラグが「0:管理しない」の場合
                // ソート順：アセスメント実施日 降序、アセスメントID 降序
                kikanCpnTucGdlRirekiInfoList.sort(Comparator
                        .comparing(
                                KikanCpnTucGdlRirekiInfoOutEntity::getAsJisshiDateYmd,
                                Comparator.nullsLast(Comparator.reverseOrder()))
                        .thenComparing(
                                KikanCpnTucGdlRirekiInfoOutEntity::getGdlId,
                                Comparator.nullsLast(Comparator.reverseOrder())));

            }

            // (1).作成者を設定
            // アセスメント履歴一覧.利用者IDと上記2.3取得した職員基本一覧情報.職員ID紐づく、対象レコードを抽出する
            // アセスメント履歴一覧.作成者＝対象レコード.職員名（姓）+ " "+対象レコード.職員名（名）
            // 2.3.2.上記「2.3.1.」で取得情報件数分、OUTPUT情報を作成する
            for (KikanCpnTucGdlRirekiInfoOutEntity kikanCpnTucGdlRirekiInfo : kikanCpnTucGdlRirekiInfoList) {
                // 上記「2.3.1.」でソートした情報.検討者が上記2.2.OUTPUT情報.職員ID一致の場合、作成者に職員名（姓）+ " "+職員名（名）を取得する
                String shokuinKnj = CommonConstants.BLANK_STRING;

                for (CpnMucShokuinKanaKnjOutEntity shokuinInfo : shokuinInfoList) {
                    if (kikanCpnTucGdlRirekiInfo.getShokuId() == shokuinInfo.getChkShokuId()) {
                        shokuinKnj = shokuinInfo.getShokuin1Knj()
                                + CommonConstants.BLANK_SPACE
                                + shokuinInfo.getShokuin2Knj();
                        break;
                    }
                }

                // 課題検討履歴を設定する。
                AssessmentHomePrintSettingsPeriodHistoryDto periodHistoryDto = this
                        .setPeriodHistory(kikanCpnTucGdlRirekiInfo, shokuinKnj);

                periodHistoryList.add(periodHistoryDto);

            }

        }

        // 3. 計画期間の居宅アセスメント履歴取得
        String sectionName = CommonConstants.EMPTY_STRING;
        if (StringUtil.isNotEmpty(inDto.getSectionName())) {
            sectionName = inDto.getSectionName();
        } else {
            // 3.1変数.セクション名計算
            sectionName = this.getSectionName(inDto.getHistorySelectFlag(), periodHistoryList);
        }

        List<PrintSettingInfoDto> saveBeforePrtList = new ArrayList<PrintSettingInfoDto>();
        // ４.印刷設定初期値情報の取得
        // API定義書_APINo(959)_初期情報取得.xlsx」の「2.」
        // 2.印刷設定情報リストを取得する。
        Pair<Boolean, List<PrintSettingInfoDto>> resultPrtList = this.printSettingLogic.getInitPrtList(
                inDto.getSysRyaku(),
                sectionName,
                inDto.getShokuId(),
                inDto.getHoujinId(),
                inDto.getShisetuId(),
                inDto.getSvJigyoId());

        if (resultPrtList.getLeft() == true) {
            saveBeforePrtList = resultPrtList.getRight();

            // 印刷設定情報リスト
            outDto.setPrtList(saveBeforePrtList);

            return outDto;

        } else {
            saveBeforePrtList = resultPrtList.getRight();
        }

        // API定義書_APINo(959)_初期情報取得.xlsx」の「3.」
        // 3.印刷設定保存情報を保存する
        FreeAssessmentFacePrintSettingsUpdateServiceOutDto freeAssessmentFacePrintSettingsUpdateServiceOutDto = this.printSettingLogic
                .insertPrtList(
                        saveBeforePrtList,
                        inDto.getSysRyaku(),
                        sectionName,
                        inDto.getShokuId(),
                        inDto.getHoujinId(),
                        inDto.getShisetuId(),
                        inDto.getSvJigyoId(),
                        inDto.getIndex(),
                        inDto.getGsysCd(),
                        inDto.getKojinhogoUsedFlg(),
                        inDto.getSectionAddNo());

        List<PrintSettingInfoDto> printSettingInfoList = freeAssessmentFacePrintSettingsUpdateServiceOutDto
                .getPrtList();

        SysIniInfoDto sysIniInfo = freeAssessmentFacePrintSettingsUpdateServiceOutDto.getSysIniInfo();

        // 印刷設定情報リスト
        outDto.setPrtList(printSettingInfoList);
        // システムINI情報
        outDto.setSysIniInfo(sysIniInfo);
        // アセスメント履歴リスト
        outDto.setPeriodHistoryList(periodHistoryList);

        return outDto;

    }

    /**
     * ケアチェック表と共通期間一覧を取得する。
     * 
     * @param svJigyoId 事業者ID
     * @param userId    利用者ID
     * 
     * @return 「ケアチェック表と共通期間一覧
     */
    private List<KikanCpnTucGdlRirekiInfoOutEntity> findKikanCpnTucGdlRirekiInfoByCriteria(String svJigyoId,
            String userId) {
        KikanCpnTucGdlRirekiInfoByCriteriaInEntity kikanCpnTucGdlRirekiInfoByCriteriaInEntity = new KikanCpnTucGdlRirekiInfoByCriteriaInEntity();
        // 事業者ID
        kikanCpnTucGdlRirekiInfoByCriteriaInEntity.setSvJigyo(CommonDtoUtil.strValToInt(svJigyoId));
        // 利用者ID
        kikanCpnTucGdlRirekiInfoByCriteriaInEntity.setUser(CommonDtoUtil.strValToInt(userId));

        return this.kikanCpnTucGdlRirekiSelectMapper.findKikanCpnTucGdlRirekiInfoByCriteria(
                kikanCpnTucGdlRirekiInfoByCriteriaInEntity);

    }

    /**
     * 職員一覧を取得する。
     * 
     * @return 職員一覧
     */
    private List<CpnMucShokuinKanaKnjOutEntity> findCpnMucShokuinKanaKnjByCriteria() {
        CpnMucShokuinKanaKnjByCriteriaInEntity cpnMucShokuinKanaKnjByCriteriaInEntity = new CpnMucShokuinKanaKnjByCriteriaInEntity();
        return this.comMscShokuinSelectMapper
                .findCpnMucShokuinKanaKnjByCriteria(cpnMucShokuinKanaKnjByCriteriaInEntity);

    }

    /**
     * インターライ方式履歴を設定する。
     * 
     * @param kikanCpnTucGdlRirekiInfo アセスメント履歴
     * @param shokuinKnj               作成者
     * 
     * @return アセスメント履歴
     */
    private AssessmentHomePrintSettingsPeriodHistoryDto setPeriodHistory(
            KikanCpnTucGdlRirekiInfoOutEntity kikanCpnTucGdlRirekiInfo, String shokuinKnj) {
        AssessmentHomePrintSettingsPeriodHistoryDto periodHistoryDto = new AssessmentHomePrintSettingsPeriodHistoryDto();
        // 期間ID */
        periodHistoryDto.setSc1Id(CommonDtoUtil.objValToString(kikanCpnTucGdlRirekiInfo.getSc1Id()));
        // 開始日 */
        periodHistoryDto.setStartYmd(kikanCpnTucGdlRirekiInfo.getStartYmd());
        // 終了日 */
        periodHistoryDto.setEndYmd(kikanCpnTucGdlRirekiInfo.getEndYmd());
        // 選択 */
        periodHistoryDto.setSel(CommonDtoUtil.objValToString(kikanCpnTucGdlRirekiInfo.getSel()));
        // アセスメントID */
        periodHistoryDto.setGdlId(CommonDtoUtil.objValToString(kikanCpnTucGdlRirekiInfo.getGdlId()));
        // 利用者ID */
        periodHistoryDto.setUserId(CommonDtoUtil.objValToString(kikanCpnTucGdlRirekiInfo.getUserId()));
        // アセスメント実施日 */
        periodHistoryDto.setAsJisshiDateYmd(kikanCpnTucGdlRirekiInfo.getAsJisshiDateYmd());
        // 記載者ID */
        periodHistoryDto.setShokuId(CommonDtoUtil.objValToString(kikanCpnTucGdlRirekiInfo.getShokuId()));
        // 改定フラグ */
        periodHistoryDto.setNinteiFormF(CommonDtoUtil.objValToString(kikanCpnTucGdlRirekiInfo.getNinteiFormF()));
        // 作成者 */
        periodHistoryDto.setShokuinKnj(shokuinKnj);

        return periodHistoryDto;

    }

    /**
     * インターライ方式履歴を設定する。
     * 
     * @param historySelectFlag 履歴選択フラグ
     * @param periodHistoryList アセスメント履歴一覧リスト
     * 
     * @return アセスメント履歴
     */
    private String getSectionName(String historySelectFlag,
            List<AssessmentHomePrintSettingsPeriodHistoryDto> periodHistoryList) {
        // 3.1.1変数.調査票改訂フラグを設定する。
        String ninteiFormF = null;

        // リクエスト.履歴選択フラグが1（複数）の場合、
        if (CommonDtoUtil.checkStringEqual(historySelectFlag, CommonConstants.HISTORY_SELECT_FLAG_PLURAL)) {
            ninteiFormF = CommonConstants.REVISION_FLAG_4;

        }
        // リクエスト.履歴選択フラグが0（単一）の場合、
        else if (CommonDtoUtil.checkStringEqual(historySelectFlag, CommonConstants.HISTORY_SELECT_FLAG_SINGLE)) {
            // 2.4で取得した アセスメント履歴一覧リストのサイズが0件以上の場合、
            if (CollectionUtils.isNotEmpty(periodHistoryList)) {
                // 2.4で取得した アセスメント履歴一覧リストの1件目.改訂フラグは変数.調査票改訂フラグに設定する。
                ninteiFormF = periodHistoryList.get(0).getNinteiFormF();

            } else {
                // ・変数.調査票改訂フラグ＝５（5：R3/4改訂）
                ninteiFormF = CommonConstants.REVISION_FLAG_5;

            }
        }

        String sectionName = null;

        // ・変数.調査票改訂フラグが4(H21/4改訂版)の場合、
        if (CommonDtoUtil.checkStringEqual(ninteiFormF, CommonConstants.REVISION_FLAG_4)) {
            // ・変数.セクション名＝”居宅アセスメント表（H21/4改訂）”
            sectionName = CommonConstants.ASSESSMENT_H21_4;
            // 2.4で取得した アセスメント履歴一覧リストのサイズが0件以上の場合、
            if (CollectionUtils.isNotEmpty(periodHistoryList)) {
                // 2.4で取得した アセスメント履歴一覧リストの1件目.アセスメント実施日>="2018/04/01"
                if (StringUtil.isNotEmpty(periodHistoryList.get(0).getAsJisshiDateYmd())
                        && periodHistoryList.get(0).getAsJisshiDateYmd()
                                .compareTo(CommonConstants.CREATION_DATE_20180401) >= 0) {
                    // ・変数.セクション名＝”居宅アセスメント表（H30/4改訂）”
                    sectionName = CommonConstants.ASSESSMENT_H30_4;

                }
            }

        }
        // ・変数.調査票改訂フラグが5(R3/4改訂版)の場合、
        else if (CommonDtoUtil.checkStringEqual(ninteiFormF, CommonConstants.REVISION_FLAG_5)) {
            // ・変数.セクション名＝”居宅アセスメント表（R3/4改訂”
            sectionName = CommonConstants.ASSESSMENT_R3_4;

        }

        return sectionName;

    }

}
