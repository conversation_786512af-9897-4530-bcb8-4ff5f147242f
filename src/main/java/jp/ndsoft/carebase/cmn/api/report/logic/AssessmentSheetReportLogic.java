package jp.ndsoft.carebase.cmn.api.report.logic;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonChoPrt;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentSheetPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonShiTeiDateParts;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.U06080DiagnosisInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.YaKuzaiList;
import jp.ndsoft.carebase.cmn.api.logic.ColorConvertLogic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetAReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetBReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetCReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetDReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetEReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetFReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetGReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetHReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetIReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetJReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetKReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetLReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetMReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetNReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetOReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetPReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetQReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetRReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetSReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetTReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentSheetUVReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.model.AssessmentSheetReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssTypeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssTypeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.DrugClassificationMasterInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.DrugClassificationMasterInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssAPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssAPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssB1PByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssB1POutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssCPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssCPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssDPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssDPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssEPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssEPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssFPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssFPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssGPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssGPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssHPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssHPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssIDataByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssIDataOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssIPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssIPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssJPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssJPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssKPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssKPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssLPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssLPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssMPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssMPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssNDataByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssNDataOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssOPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssOPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssPqPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssPqPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssPrPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssPrPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssPuvPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssPuvPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssSPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssSPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssTPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssTPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssUvPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiAssUvPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinInfoListSpaceSortByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinInfoListSpaceSortOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.UserinfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.UserinfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YoushikiNoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YoushikiNoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocPrtYoushikiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucRaiRrkSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.DrugClassificationMasterInfoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghCpnRaiAssIDataSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghCpnRaiAssNDataSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssASelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssB1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssCSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssDSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssESelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssFSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssGSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssHSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssISelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssJSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssKSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssLSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssMSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssOSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssPQSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssPRSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssPUVSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssSSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssTSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RaiRrkKghCpnRaiAssUvSelectMapper;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * @since 2025.06.20
 * <AUTHOR>
 * @description U06080_アセスメント表 帳票出力
 */
@Component
public class AssessmentSheetReportLogic {

    /** アセスメント表（Ａ）情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssASelectMapper raiRrkKghCpnRaiAssASelectMapper;

    /** アセスメント表（Ｂ）情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssB1SelectMapper raiRrkKghCpnRaiAssB1SelectMapper;

    /** アセスメント表（Ｃ）情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssCSelectMapper raiRrkKghCpnRaiAssCSelectMapper;

    /** アセスメント表（Ｄ）情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssDSelectMapper raiRrkKghCpnRaiAssDSelectMapper;

    /** アセスメント表（Ｅ）情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssESelectMapper raiRrkKghCpnRaiAssESelectMapper;

    /** アセスメント表（Ｆ）情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssFSelectMapper raiRrkKghCpnRaiAssFSelectMapper;

    /** アセスメント表（Ｇ）情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssGSelectMapper raiRrkKghCpnRaiAssGSelectMapper;

    /** アセスメント表（Ｈ）情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssHSelectMapper raiRrkKghCpnRaiAssHSelectMapper;

    /** アセスメント表（Ｉ）データ情報を取得 */
    @Autowired
    private KghCpnRaiAssIDataSelectMapper kghCpnRaiAssIDataSelectMapper;
    /** アセスメント表（Ｉ）情報を取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssISelectMapper raiRrkKghCpnRaiAssISelectMapper;

    /** アセスメント表（Ｊ）情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssJSelectMapper raiRrkKghCpnRaiAssJSelectMapper;

    /** アセスメント表（Ｋ）情報を取得する */
    @Autowired
    private RaiRrkKghCpnRaiAssKSelectMapper raiRrkKghCpnRaiAssKSelectMapper;

    /** アセスメント表（Ｌ）情報を取得する */
    @Autowired
    private RaiRrkKghCpnRaiAssLSelectMapper raiRrkKghCpnRaiAssLSelectMapper;

    /** アセスメント表（Ｍ）情報を取得する */
    @Autowired
    private RaiRrkKghCpnRaiAssMSelectMapper raiRrkKghCpnRaiAssMSelectMapper;

    /** アセスメント表（Ｎ）データ情報を取得する */
    @Autowired
    private KghCpnRaiAssNDataSelectMapper kghCpnRaiAssNDataSelectMapper;
    /** アセスメント表（Ｎ）_薬剤分類マスタ情報を取得する */
    @Autowired
    private DrugClassificationMasterInfoSelectMapper drugClassificationMasterInfoSelectMapper;

    /** アセスメント表（Ｏ）情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssOSelectMapper raiRrkKghCpnRaiAssOSelectMapper;

    /** アセスメント表(P)(Q)情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssPQSelectMapper raiRrkKghCpnRaiAssPQSelectMapper;

    /** アセスメント表（Ｒ）情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssPRSelectMapper raiRrkKghCpnRaiAssPRSelectMapper;

    /** アセスメント表（Ｓ）情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssSSelectMapper raiRrkKghCpnRaiAssSSelectMapper;

    /** アセスメント表（Ｔ）情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssTSelectMapper raiRrkKghCpnRaiAssTSelectMapper;

    /** アセスメント表（Ⅴ）情報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssUvSelectMapper raiRrkKghCpnRaiAssUvSelectMapper;

    /** アセスメント表(P)(U)(V)報取得 */
    @Autowired
    private RaiRrkKghCpnRaiAssPUVSelectMapper raiRrkKghCpnRaiAssPUVSelectMapper;

    /** 職員情報一覧情報取得 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /** 利用者の情報取得 */
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;

    /** アセスメント種別取得 */
    @Autowired
    private CpnTucRaiRrkSelectMapper cpnTucRaiRrkSelectMapper;

    /** 文書番号情報取得 */
    @Autowired
    private ComMocPrtYoushikiSelectMapper comMocPrtYoushikiSelectMapper;

    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** RGB処理 */
    @Autowired
    private ColorConvertLogic colorConvertLogic;

    /**
     * U06080_アセスメント表（Ａ）帳票パラメータ取得
     * 
     * @param model  パラメータモデル（入力元データ）
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetAReportServiceInDto getAReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        AssessmentSheetAReportServiceInDto infoInDto = new AssessmentSheetAReportServiceInDto();

        /*
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、アセスメント表（Ａ）情報の取得。===============
         */
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            infoInDto = this.getADefReportParams(printSet.getShiTeiKubun(), printOption.getKinyuAssType());

            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.TRUE);
        } else {
            // 【リクエストパラメータ】.印刷対象履歴リスト
            ReportCommonPrintSubjectHistory printSubjectHistory = model.getPrintSubjectHistoryList().get(0);
            Boolean colorFlg = false;
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg())
                    || CommonConstants.STR_1.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getAReportParamsFromAssA(printSubjectHistory, printSet.getShiTeiKubun(),
                    model.getSystemDate(), printSet.getShiTeiDate(), colorFlg,
                    model.getSyscd(), model.getJigyoInfo().getSvJigyoId());

            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.FALSE);
        }
        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 事業所名
        infoInDto.setJigyoKnj(ReportUtil.nullToEmpty(model.getSvJigyoKnj()));
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ａ）帳票パラメータ取得
     * 
     * @param shiTeiKubun  指定日印刷区分
     * @param kinyuAssType アセスメント種別
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetAReportServiceInDto getADefReportParams(String shiTeiKubun, String kinyuAssType)
            throws Exception {
        // 帳票用データ詳細
        AssessmentSheetAReportServiceInDto infoInDto = new AssessmentSheetAReportServiceInDto();
        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(kinyuAssType)));
        // 調査アセスメント種別
        infoInDto.setSyubetu(CommonDtoUtil.strValToInt(kinyuAssType));
        // 指定日（年号）
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // フリガナ（姓）
        infoInDto.setName1Kana(CommonConstants.BLANK_STRING);
        // フリガナ（名）
        infoInDto.setName2Kana(CommonConstants.BLANK_STRING);
        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // 性別
        infoInDto.setSex(CommonConstants.BLANK_STRING);
        // 生年月日（年号）
        infoInDto.setBirthdayYmdGG(CommonConstants.FULL_WIDTH_SPACE + CommonConstants.FULL_WIDTH_SPACE);
        // 生年月日（年）
        infoInDto.setBirthdayYmdYY(CommonConstants.FULL_WIDTH_SPACE);
        // 生年月日（月）
        infoInDto.setBirthdayYmdMM(CommonConstants.FULL_WIDTH_SPACE);
        // 生年月日（日）
        infoInDto.setBirthdayYmdDD(CommonConstants.FULL_WIDTH_SPACE);
        // 年齢
        infoInDto.setNenrei(CommonConstants.BLANK_STRING);
        // a4_メモ
        infoInDto.setA4MemoKnj(CommonConstants.BLANK_STRING);
        // a4_メモフォント
        infoInDto.setA4MemoFont(CommonConstants.STR_12);
        // a4_メモ色
        infoInDto.setA4MemoColor(CommonConstants.BLANK_STRING);
        // 婚姻状況
        infoInDto.setA4(CommonConstants.BLANK_STRING);
        // 被保険者番号
        infoInDto.setHhokenNo(CommonConstants.BLANK_STRING);
        // 事業所番号
        infoInDto.setJigyoNumber(CommonConstants.BLANK_STRING);
        // a7_メモ
        infoInDto.setA7MemoKnj(CommonConstants.BLANK_STRING);
        // a7_メモフォント
        infoInDto.setA7MemoFont(CommonConstants.STR_12);
        // a7_メモ色
        infoInDto.setA7MemoColor(CommonConstants.BLANK_STRING);
        // 要介護度
        infoInDto.setA7(CommonConstants.BLANK_STRING);
        // a8_メモ
        infoInDto.setA8MemoKnj(CommonConstants.BLANK_STRING);
        // a8_メモフォント
        infoInDto.setA8MemoFont(CommonConstants.STR_12);
        // a8_メモ色
        infoInDto.setA8MemoColor(CommonConstants.BLANK_STRING);
        // アセスメントの理由
        infoInDto.setA8(CommonConstants.BLANK_STRING);
        // 調査日（年号）
        infoInDto.setAssDateYmdGG(CommonConstants.FULL_WIDTH_SPACE + CommonConstants.FULL_WIDTH_SPACE);
        // 調査日（年）
        infoInDto.setAssDateYmdYY(CommonConstants.FULL_WIDTH_SPACE);
        // 調査日（月）
        infoInDto.setAssDateYmdMM(CommonConstants.FULL_WIDTH_SPACE);
        // 調査日（日）
        infoInDto.setAssDateYmdDD(CommonConstants.FULL_WIDTH_SPACE);
        // 本人のケアの目標
        infoInDto.setA10Knj(CommonConstants.BLANK_STRING);
        // a11_メモ
        infoInDto.setA11MemoKnj(CommonConstants.BLANK_STRING);
        // a11_メモフォント
        infoInDto.setA11MemoFont(CommonConstants.STR_12);
        // a11_メモ色
        infoInDto.setA11MemoColor(CommonConstants.BLANK_STRING);
        // アセスメント時の居住場所
        infoInDto.setA11(CommonConstants.BLANK_STRING);
        // a12_a_メモ
        infoInDto.setA12AMemoKnj(CommonConstants.BLANK_STRING);
        // a12_a_メモフォント
        infoInDto.setA12AMemoFont(CommonConstants.STR_12);
        // a12_a_メモ色
        infoInDto.setA12AMemoColor(CommonConstants.BLANK_STRING);
        // 同居形態_同居者
        infoInDto.setA12A(CommonConstants.BLANK_STRING);
        // a12_b_メモ
        infoInDto.setA12BMemoKnj(CommonConstants.BLANK_STRING);
        // a12_b_メモフォント
        infoInDto.setA12BMemoFont(CommonConstants.STR_12);
        // a12_b_メモ色
        infoInDto.setA12BMemoColor(CommonConstants.BLANK_STRING);
        // 同居形態_変化
        infoInDto.setA12B(CommonConstants.BLANK_STRING);
        // a12_c_メモ
        infoInDto.setA12CMemoKnj(CommonConstants.BLANK_STRING);
        // a12_c_メモフォント
        infoInDto.setA12CMemoFont(CommonConstants.STR_12);
        // a12_c_メモ色
        infoInDto.setA12CMemoColor(CommonConstants.BLANK_STRING);
        // 同居形態_他の居住
        infoInDto.setA12C(CommonConstants.BLANK_STRING);
        // a13_メモ
        infoInDto.setA13MemoKnj(CommonConstants.BLANK_STRING);
        // a13_メモフォント
        infoInDto.setA13MemoFont(CommonConstants.STR_12);
        // a13_メモ色
        infoInDto.setA13MemoColor(CommonConstants.BLANK_STRING);
        // 退院後の経過期間
        infoInDto.setA13(CommonConstants.BLANK_STRING);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ａ）帳票パラメータ取得
     * 
     * @param history     印刷対象履歴
     * @param shiTeiKubun 指定日印刷区分
     * @param systemDate  システム日付
     * @param shiTeiDate  指定日
     * @param colorFlg    印刷時に色をつけるフラグ
     * @param syscd       システムコード
     * @param svJigyoId   サービス事業者ID
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetAReportServiceInDto getAReportParamsFromAssA(
            ReportCommonPrintSubjectHistory history,
            String shiTeiKubun, String systemDate, String shiTeiDate, Boolean colorFlg, String syscd, String svJigyoId)
            throws Exception {
        // 帳票用データ詳細
        AssessmentSheetAReportServiceInDto infoInDto = new AssessmentSheetAReportServiceInDto();
        // 2.1. アセスメント表（Ａ）情報を取得する。
        KghCpnRaiAssAPByCriteriaInEntity kghCpnRaiAssAPByCriteriaInEntity = new KghCpnRaiAssAPByCriteriaInEntity();
        kghCpnRaiAssAPByCriteriaInEntity
                .setAnRaiId(CommonDtoUtil.strValToInt(history.getRaiId()));
        List<KghCpnRaiAssAPOutEntity> assAList = raiRrkKghCpnRaiAssASelectMapper
                .findKghCpnRaiAssAPByCriteria(kghCpnRaiAssAPByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(assAList)) {
            // 総件数 > 0 件の場合
            KghCpnRaiAssAPOutEntity entity = assAList.get(0);
            /*
             * =============== アセスメント種別の取得 ===============
             * 
             */
            Pair<Integer, String> syubetPair = this
                    .getSyubetuByRaiId(CommonDtoUtil.strValToInt(history.getRaiId()));

            /*
             * =============== 帳票用データを設定 ===============
             * 
             */
            // 調査アセスメント種別
            infoInDto.setSyubetu(syubetPair.getLeft());
            // 調査アセスメント種別名
            infoInDto.setSyubetuKnj(syubetPair.getRight());
            // 指定日（年号）
            ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(shiTeiKubun, shiTeiDate, systemDate);
            infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
            // 指定日（年）
            infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
            // 指定日（月）
            infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
            // 指定日（日）
            infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());
            // フリガナ（姓）
            infoInDto.setName1Kana(ReportUtil.nullToEmpty(entity.getName1Kana()));
            // フリガナ（名）
            infoInDto.setName2Kana(ReportUtil.nullToEmpty(entity.getName2Kana()));
            // 氏名（姓）
            infoInDto.setName1Knj(ReportUtil.nullToEmpty(entity.getName1Knj()));
            // 氏名（名）
            infoInDto.setName2Knj(ReportUtil.nullToEmpty(entity.getName2Knj()));
            // 性別
            infoInDto.setSex(ReportUtil.nullToEmpty(entity.getSex()));
            List<String> dateList = this.getDate(ReportUtil.nullToEmpty(entity.getBirthdayYmd()));
            // 生年月日（年号）
            infoInDto.setBirthdayYmdGG(dateList.get(0));
            // 生年月日（年）
            infoInDto.setBirthdayYmdYY(dateList.get(1));
            // 生年月日（月）
            infoInDto.setBirthdayYmdMM(dateList.get(2));
            // 生年月日（日）
            infoInDto.setBirthdayYmdDD(dateList.get(3));

            // 年齢
            String nenNei = CommonDtoUtil
                    .objValToString(nds3GkFunc01Logic.getDateDiffYY(entity.getBirthdayYmd(), entity.getAssDateYmd()));
            infoInDto.setNenrei(nenNei);
            // a4_メモ
            infoInDto.setA4MemoKnj(ReportUtil.nullToEmpty(entity.getA4MemoKnj()));
            // a4_メモフォント
            infoInDto.setA4MemoFont(getMemoFont(entity.getA4MemoFont()));
            // a4_メモ色
            infoInDto.setA4MemoColor(getMemoColor(entity.getA4MemoColor(), colorFlg));
            // 婚姻状況
            infoInDto.setA4(ReportUtil.nullToEmpty(entity.getA4()));
            // 被保険者番号
            infoInDto.setHhokenNo(ReportUtil.nullToEmpty(entity.getHHokenNo()));
            // 事業所番号
            infoInDto.setJigyoNumber(ReportUtil.nullToEmpty(entity.getJigyoNumber()));
            // a7_メモ
            infoInDto.setA7MemoKnj(ReportUtil.nullToEmpty(entity.getA7MemoKnj()));
            // a7_メモフォント
            infoInDto.setA7MemoFont(getMemoFont(entity.getA7MemoFont()));
            // a7_メモ色
            infoInDto.setA7MemoColor(getMemoColor(entity.getA7MemoColor(), colorFlg));
            // 要介護度
            infoInDto.setA7(ReportUtil.nullToEmpty(entity.getA7()));
            // a8_メモ
            infoInDto.setA8MemoKnj(ReportUtil.nullToEmpty(entity.getA8MemoKnj()));
            // a8_メモフォント
            infoInDto.setA8MemoFont(getMemoFont(entity.getA8MemoFont()));
            // a8_メモ色
            infoInDto.setA8MemoColor(getMemoColor(entity.getA8MemoColor(), colorFlg));
            // アセスメントの理由
            infoInDto.setA8(ReportUtil.nullToEmpty(entity.getA8()));

            List<String> assDateList = this.getDate(ReportUtil.nullToEmpty(entity.getAssDateYmd()));
            // 調査日（年号）
            infoInDto.setAssDateYmdGG(assDateList.get(0));
            // 調査日（年）
            infoInDto.setAssDateYmdYY(assDateList.get(1));
            // 調査日（月）
            infoInDto.setAssDateYmdMM(assDateList.get(2));
            // 調査日（日）
            infoInDto.setAssDateYmdDD(assDateList.get(3));

            // 本人のケアの目標
            infoInDto.setA10Knj(ReportUtil.nullToEmpty(entity.getA10Knj()));
            // a11_メモ
            infoInDto.setA11MemoKnj(ReportUtil.nullToEmpty(entity.getA11MemoKnj()));
            // a11_メモフォント
            infoInDto.setA11MemoFont(getMemoFont(entity.getA11MemoFont()));
            // a11_メモ色
            infoInDto.setA11MemoColor(getMemoColor(entity.getA11MemoColor(), colorFlg));
            // アセスメント時の居住場所
            infoInDto.setA11(ReportUtil.nullToEmpty(entity.getA11()));
            // a12_a_メモ
            infoInDto.setA12AMemoKnj(ReportUtil.nullToEmpty(entity.getA12AMemoKnj()));
            // a12_a_メモフォント
            infoInDto.setA12AMemoFont(getMemoFont(entity.getA12AMemoFont()));
            // a12_a_メモ色
            infoInDto.setA12AMemoColor(getMemoColor(entity.getA12AMemoColor(), colorFlg));
            // 同居形態_同居者
            infoInDto.setA12A(ReportUtil.nullToEmpty(entity.getA12A()));
            // a12_b_メモ
            infoInDto.setA12BMemoKnj(ReportUtil.nullToEmpty(entity.getA12BMemoKnj()));
            // a12_b_メモフォント
            infoInDto.setA12BMemoFont(getMemoFont(entity.getA12BMemoFont()));
            // a12_b_メモ色
            infoInDto.setA12BMemoColor(getMemoColor(entity.getA12BMemoColor(), colorFlg));
            // 同居形態_変化
            infoInDto.setA12B(ReportUtil.nullToEmpty(entity.getA12B()));
            // a12_c_メモ
            infoInDto.setA12CMemoKnj(ReportUtil.nullToEmpty(entity.getA12CMemoKnj()));
            // a12_c_メモフォント
            infoInDto.setA12CMemoFont(getMemoFont(entity.getA12CMemoFont()));
            // a12_c_メモ色
            infoInDto.setA12CMemoColor(getMemoColor(entity.getA12CMemoColor(), colorFlg));
            // 同居形態_他の居住
            infoInDto.setA12C(ReportUtil.nullToEmpty(entity.getA12C()));
            // a13_メモ
            infoInDto.setA13MemoKnj(ReportUtil.nullToEmpty(entity.getA13MemoKnj()));
            // a13_メモフォント
            infoInDto.setA13MemoFont(getMemoFont(entity.getA13MemoFont()));
            // a13_メモ色
            infoInDto.setA13MemoColor(getMemoColor(entity.getA13MemoColor(), colorFlg));
            // 退院後の経過期間
            infoInDto.setA13(ReportUtil.nullToEmpty(entity.getA13()));
            // 出力帳票印刷情報
            ReportCommonChoPrt choPrtList = history.getChoPrtList().get(0);
            // 文書管理番号
            infoInDto.setBunsyoKanriNo(
                    this.getBunsyoKanriNo(syscd, choPrtList.getShokuId(), choPrtList.getSection(), svJigyoId));
        }

        return infoInDto;
    }

    /**
     * U06080_アセスメント表（Ｂ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetBReportServiceInDto getBReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {

        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        // infoInDtoを設定する。
        AssessmentSheetBReportServiceInDto infoInDto = new AssessmentSheetBReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        // 帳票タイトル ← "インターライ方式ケアアセスメント表"
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 指定日印刷区分 ← リクエストパラメータ.データ.印刷オプション.指定日印刷区分
        infoInDto.setPrDate(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // 事業者名 ← リクエストパラメータ.データ.事業者名
        infoInDto.setJigyousha(ReportUtil.nullToEmpty(model.getSvJigyoKnj()));

        /*
         * ===============2. リクエストパラメータ.記入用シートを印刷するフラグ = true の場合、
         * 結果レスポンスを返却する。===============
         * 
         */
        Boolean isEmptyFlg = false;
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            isEmptyFlg = true;
        }
        // 記入用シートを印刷するフラグ リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(isEmptyFlg);
        // リクエストパラメータ.記入用シートを印刷するフラグ = true の場合
        if (isEmptyFlg) {

            infoInDto = this.getBDefReportParams(model, outDto, infoInDto);

            /*
             * ===============リクエストパラメータ.記入用シートを印刷するフラグ = false の場合、
             * アセスメント表（Ｂ）情報の取得。===============
             * 
             */
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getBReportParamsFromAssB(model, outDto, infoInDto);
        }
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｂ）帳票パラメータ取得
     * 
     * @param model     入力データ
     * @param outDto    出力データ
     * @param infoInDto PDF帳票出力詳細データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetBReportServiceInDto getBDefReportParams(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto, AssessmentSheetBReportServiceInDto infoInDto) throws Exception {
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType())));

        // 調査アセスメント種別 ← リクエストパラメータ.データ.印刷オプション.記入用シートアセスメント種別
        infoInDto.setAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType()));
        // 指定日（年号） 全角フレーム
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年） 全角フレーム
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月） 全角フレーム
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日） 全角フレーム
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // b1_メモ
        infoInDto.setB1MemoKnj(CommonConstants.BLANK_STRING);
        // b1_メモフォント
        infoInDto.setB1MemoFont(CommonConstants.STR_12);
        // b1_メモ色
        infoInDto.setB1MemoColor(CommonConstants.BLANK_STRING);
        // 入所に対して本人の意思度合
        infoInDto.setB1(CommonConstants.BLANK_STRING);
        // 受付日（年号）
        infoInDto.setB2YmdGG(CommonConstants.FULL_WIDTH_SPACE + CommonConstants.FULL_WIDTH_SPACE);
        // 受付日（年）
        infoInDto.setB2YmdYY(CommonConstants.FULL_WIDTH_SPACE);
        // 受付日（月）
        infoInDto.setB2YmdMM(CommonConstants.FULL_WIDTH_SPACE);
        // 受付日（日）
        infoInDto.setB2YmdDD(CommonConstants.FULL_WIDTH_SPACE);
        // 相談受付時までの経過
        infoInDto.setB3Knj(CommonConstants.BLANK_STRING);
        // 相談受付内容
        infoInDto.setB4Knj(CommonConstants.BLANK_STRING);
        // b5_メモ
        infoInDto.setB5MemoKnj(CommonConstants.BLANK_STRING);
        // b5_メモフォント
        infoInDto.setB5MemoFont(CommonConstants.STR_12);
        // b5_メモ色
        infoInDto.setB5MemoColor(CommonConstants.BLANK_STRING);
        // 利用歴_介護施設、療養病院/病棟
        infoInDto.setB5A(CommonConstants.BLANK_STRING);
        // 利用歴_認知症対応型共同生活介護、小規模多機能型居宅介護
        infoInDto.setB5B(CommonConstants.BLANK_STRING);
        // 利用歴_高齢者住宅-有料老人ホーム
        infoInDto.setB5C(CommonConstants.BLANK_STRING);
        // 利用歴_精神科病院、精神科病棟
        infoInDto.setB5D(CommonConstants.BLANK_STRING);
        // 利用歴_精神障害者施設
        infoInDto.setB5E(CommonConstants.BLANK_STRING);
        // 利用歴_知的障害者施設
        infoInDto.setB5F(CommonConstants.BLANK_STRING);
        // b6_メモ
        infoInDto.setB6MemoKnj(CommonConstants.BLANK_STRING);
        // b6_メモフォント
        infoInDto.setB6MemoFont(CommonConstants.STR_12);
        // b6_メモ色
        infoInDto.setB6MemoColor(CommonConstants.BLANK_STRING);
        // 入所直前の居住場所
        infoInDto.setB6A(CommonConstants.BLANK_STRING);
        // 通常の居住場所
        infoInDto.setB6B(CommonConstants.BLANK_STRING);
        // b7_メモ
        infoInDto.setB7MemoKnj(CommonConstants.BLANK_STRING);
        // b7_メモフォント
        infoInDto.setB7MemoFont(CommonConstants.STR_12);
        // b7_メモ色
        infoInDto.setB7MemoColor(CommonConstants.BLANK_STRING);
        // 入所前の同居形態
        infoInDto.setB7(CommonConstants.BLANK_STRING);
        // b8_メモ
        infoInDto.setB8MemoKnj(CommonConstants.BLANK_STRING);
        // b8_メモフォント
        infoInDto.setB8MemoFont(CommonConstants.STR_12);
        // b8_メモ色
        infoInDto.setB8MemoColor(CommonConstants.BLANK_STRING);
        // 精神疾患歴
        infoInDto.setB8(CommonConstants.BLANK_STRING);
        // b9_メモ
        infoInDto.setB9MemoKnj(CommonConstants.BLANK_STRING);
        // b9_メモフォント
        infoInDto.setB9MemoFont(CommonConstants.STR_12);
        // b9_メモ色
        infoInDto.setB9MemoColor(CommonConstants.BLANK_STRING);
        // 教育歴
        infoInDto.setB9(CommonConstants.BLANK_STRING);
        // b10_メモ
        infoInDto.setB10MemoKnj(CommonConstants.BLANK_STRING);
        // b10_メモフォント
        infoInDto.setB10MemoFont(CommonConstants.STR_12);
        // b10_メモ色
        infoInDto.setB10MemoColor(CommonConstants.BLANK_STRING);
        // 医療機関受診時の送迎
        infoInDto.setB10(CommonConstants.BLANK_STRING);
        // b11_メモ
        infoInDto.setB11MemoKnj(CommonConstants.BLANK_STRING);
        // b11_メモフォント
        infoInDto.setB11MemoFont(CommonConstants.STR_12);
        // b11_メモ色
        infoInDto.setB11MemoColor(CommonConstants.BLANK_STRING);
        // 受診中の付き添いが必要
        infoInDto.setB11(CommonConstants.BLANK_STRING);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;

    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｂ）帳票パラメータ取得
     * 
     * @param model     入力データ
     * @param outDto    出力データ
     * @param infoInDto PDF帳票出力詳細データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetBReportServiceInDto getBReportParamsFromAssB(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto, AssessmentSheetBReportServiceInDto infoInDto) throws Exception {
        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();

        // アセスメント表（Ｂ）情報の取得。
        // アセスメント表（Ｂ）情報取得のINPUT情報初期化
        KghCpnRaiAssB1PByCriteriaInEntity kghCpnRaiAssB1PByCriteriaInEntity = new KghCpnRaiAssB1PByCriteriaInEntity();
        // アセスメントID←リクエストパラメータ.データ.印刷対象履歴リスト[0].アセスメントID
        kghCpnRaiAssB1PByCriteriaInEntity
                .setAnRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
        // 2.1. 下記のアセスメント表（Ｂ）情報取得のDAOを利用し、アセスメント表（Ｂ）情報を取得する。
        List<KghCpnRaiAssB1POutEntity> kghCpnRaiAssB1POutEntityList = raiRrkKghCpnRaiAssB1SelectMapper
                .findKghCpnRaiAssB1PByCriteria(kghCpnRaiAssB1PByCriteriaInEntity);

        /*
         * ===============2.2. 「2.1.」で取得したアセスメント表（Ｂ）情報の件数 > 0 件の場合
         * 処理を続ける、===============
         * 
         */

        if (CollectionUtils.isNotEmpty(kghCpnRaiAssB1POutEntityList)) {
            // アセスメント表（Ｂ）情報の一件目を取得する
            KghCpnRaiAssB1POutEntity entity = kghCpnRaiAssB1POutEntityList.get(0);
            /**
             * ===============3.利用者基本情報の取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());
            // 氏名（姓）
            infoInDto.setName1Knj(userPair.getLeft());
            // 氏名（名）
            infoInDto.setName2Knj(userPair.getRight());

            /*
             * =============== 4. アセスメント種別情報を取得する。===============
             * 
             */
            Pair<Integer, String> syubetuPair = this
                    .getSyubetuByRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
            // 調査アセスメント種別名
            infoInDto.setSyubetuKnj(syubetuPair.getRight());
            // 調査アセスメント種別
            infoInDto.setAssType(syubetuPair.getLeft());

            /*
             * ===============5.上記編集した帳票用データを設定する。===============
             * 
             */

            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ
            Boolean colorFlg = false;
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg()) ||
                    CommonConstants.STR_1.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }

            // b1_メモ
            infoInDto.setB1MemoKnj(ReportUtil.nullToEmpty(entity.getB1MemoKnj()));
            // b1_メモフォント
            infoInDto.setB1MemoFont(getMemoFont(entity.getB1MemoFont()));
            // b1_メモ色
            infoInDto.setB1MemoColor(getMemoColor(entity.getB1MemoColor(), colorFlg));
            // 入所に対して本人の意思度合
            infoInDto.setB1(ReportUtil.nullToEmpty(entity.getB1()));
            List<String> b2YmdList = this.getDate(ReportUtil.nullToEmpty(entity.getB2Ymd()));
            // 受付日（年号）
            infoInDto.setB2YmdGG(b2YmdList.get(0));
            // 受付日（年）
            infoInDto.setB2YmdYY(b2YmdList.get(1));
            // 受付日（月）
            infoInDto.setB2YmdMM(b2YmdList.get(2));
            // 受付日（日）
            infoInDto.setB2YmdDD(b2YmdList.get(3));
            // 相談受付時までの経過
            infoInDto.setB3Knj(ReportUtil.nullToEmpty(entity.getB3Knj()));
            // 相談受付内容
            infoInDto.setB4Knj(ReportUtil.nullToEmpty(entity.getB4Knj()));
            // b5_メモ
            infoInDto.setB5MemoKnj(ReportUtil.nullToEmpty(entity.getB5MemoKnj()));
            // b5_メモフォント
            infoInDto.setB5MemoFont(getMemoFont(entity.getB5MemoFont()));
            // b5_メモ色
            infoInDto.setB5MemoColor(getMemoColor(entity.getB5MemoColor(), colorFlg));
            // 利用歴_介護施設、療養病院/病棟
            infoInDto.setB5A(ReportUtil.nullToEmpty(entity.getB5A()));
            // 利用歴_認知症対応型共同生活介護、小規模多機能型居宅介護
            infoInDto.setB5B(ReportUtil.nullToEmpty(entity.getB5B()));
            // 利用歴_高齢者住宅-有料老人ホーム
            infoInDto.setB5C(ReportUtil.nullToEmpty(entity.getB5C()));
            // 利用歴_精神科病院、精神科病棟
            infoInDto.setB5D(ReportUtil.nullToEmpty(entity.getB5D()));
            // 利用歴_精神障害者施設
            infoInDto.setB5E(ReportUtil.nullToEmpty(entity.getB5E()));
            // 利用歴_知的障害者施設
            infoInDto.setB5F(ReportUtil.nullToEmpty(entity.getB5F()));
            // b6_メモ
            infoInDto.setB6MemoKnj(ReportUtil.nullToEmpty(entity.getB6MemoKnj()));
            // b6_メモフォント
            infoInDto.setB6MemoFont(getMemoFont(entity.getB6MemoFont()));
            // b6_メモ色
            infoInDto.setB6MemoColor(getMemoColor(entity.getB6MemoColor(), colorFlg));
            // 入所直前の居住場所
            infoInDto.setB6A(ReportUtil.nullToEmpty(entity.getB6A()));
            // 通常の居住場所
            infoInDto.setB6B(ReportUtil.nullToEmpty(entity.getB6B()));
            // b7_メモ
            infoInDto.setB7MemoKnj(ReportUtil.nullToEmpty(entity.getB7MemoKnj()));
            // b7_メモフォント
            infoInDto.setB7MemoFont(getMemoFont(entity.getB7MemoFont()));
            // b7_メモ色
            infoInDto.setB7MemoColor(getMemoColor(entity.getB7MemoColor(), colorFlg));
            // 入所前の同居形態
            infoInDto.setB7(ReportUtil.nullToEmpty(entity.getB7()));
            // b8_メモ
            infoInDto.setB8MemoKnj(ReportUtil.nullToEmpty(entity.getB8MemoKnj()));
            // b8_メモフォント
            infoInDto.setB8MemoFont(getMemoFont(entity.getB8MemoFont()));
            // b8_メモ色
            infoInDto.setB8MemoColor(getMemoColor(entity.getB8MemoColor(), colorFlg));
            // 精神疾患歴
            infoInDto.setB8(ReportUtil.nullToEmpty(entity.getB8()));
            // b9_メモ
            infoInDto.setB9MemoKnj(ReportUtil.nullToEmpty(entity.getB9MemoKnj()));
            // b9_メモフォント
            infoInDto.setB9MemoFont(getMemoFont(entity.getB9MemoFont()));
            // b9_メモ色
            infoInDto.setB9MemoColor(getMemoColor(entity.getB9MemoColor(), colorFlg));
            // 教育歴
            infoInDto.setB9(ReportUtil.nullToEmpty(entity.getB9()));
            // b10_メモ
            infoInDto.setB10MemoKnj(ReportUtil.nullToEmpty(entity.getB10MemoKnj()));
            // b10_メモフォント
            infoInDto.setB10MemoFont(getMemoFont(entity.getB10MemoFont()));
            // b10_メモ色
            infoInDto.setB10MemoColor(getMemoColor(entity.getB10MemoColor(), colorFlg));
            // 医療機関受診時の送迎
            infoInDto.setB10(ReportUtil.nullToEmpty(entity.getB10()));
            // b11_メモ
            infoInDto.setB11MemoKnj(ReportUtil.nullToEmpty(entity.getB11MemoKnj()));
            // b11_メモフォント
            infoInDto.setB11MemoFont(getMemoFont(entity.getB11MemoFont()));
            // b11_メモ色
            infoInDto.setB11MemoColor(getMemoColor(entity.getB11MemoColor(), colorFlg));
            // 受診中の付き添いが必要
            infoInDto.setB11(ReportUtil.nullToEmpty(entity.getB11()));

        }

        // 指定日（年号）
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
        // 指定日（年）
        infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
        // 指定日（月）
        infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
        // 指定日（日）
        infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());
        // 2.文書番号情報の取得
        /*
         * ===============共通関数補足 2.文書番号情報取得処理===============
         * 
         */
        // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01 関数名：GetF3GkProfile
        // を利用し、設定の読込（システム環境以外の設定）情報を取得する。
        // 【リクエストパラメータ】.システムコード
        String syscd = model.getSyscd();
        // 職員ID←リクエストパラメータ.データ.印刷対象履歴リスト[0].出力帳票印刷情報リスト[0].職員ID
        String shokuId = printSubjectHistoryList.get(0).getChoPrtList().get(0).getShokuId();
        // セクション←リクエストパラメータ.データ.出力帳票印刷情報リスト[0].セクション
        String section = printSubjectHistoryList.get(0).getChoPrtList().get(0).getSection();
        // サービス事業者ID←リクエストパラメータ.事業者情報.事業者ID
        String svJigyoId = model.getJigyoInfo().getSvJigyoId();

        String bunsyoKanriNo = this.getBunsyoKanriNo(syscd, shokuId, section, svJigyoId);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(ReportUtil.nullToEmpty(bunsyoKanriNo));

        return infoInDto;

    }

    /**
     * U06080_アセスメント表（Ｃ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    public AssessmentSheetCReportServiceInDto getCReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        AssessmentSheetCReportServiceInDto infoInDto = new AssessmentSheetCReportServiceInDto();

        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、アセスメント表（Ｃ）情報の取得。===============
         * 
         */
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.記入用シートを印刷するフラグ
        Boolean isEmptyFlg = false;
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            isEmptyFlg = true;
        }
        // 指定日印刷区分
        infoInDto.setSelDatePrintKbn(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // 事業者名
        infoInDto.setJigyoKnj(model.getSvJigyoKnj());
        // 記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(isEmptyFlg);
        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (isEmptyFlg) {
            this.setCDefReportParams(model, outDto, infoInDto);
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            this.setCReportParamsFromAssC(model, outDto, infoInDto);
        }

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｃ）帳票パラメータ取得
     * 
     * @param model     入力データ
     * @param outDto    出力データ
     * @param infoInDto 帳票用データ詳細
     * @throws Exception 例外
     */
    private void setCDefReportParams(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto, AssessmentSheetCReportServiceInDto infoInDto) throws Exception {

        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType())));
        // 調査アセスメント種別
        infoInDto.setAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType()));
        // 指定日（年号）
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // c1_メモ
        infoInDto.setC1MemoKnj(CommonConstants.BLANK_STRING);
        // c1_メモフォント
        infoInDto.setC1MemoFont(CommonConstants.STR_12);
        // c1_メモ色
        infoInDto.setC1MemoColor(CommonConstants.BLANK_STRING);
        // 日常の意思決定の認知能力
        infoInDto.setC1(CommonConstants.BLANK_STRING);
        // c2_メモ
        infoInDto.setC2MemoKnj(CommonConstants.BLANK_STRING);
        // c2_メモフォント
        infoInDto.setC2MemoFont(CommonConstants.STR_12);
        // c2_メモ色
        infoInDto.setC2MemoColor(CommonConstants.BLANK_STRING);
        // 短期記憶
        infoInDto.setC2A(CommonConstants.BLANK_STRING);
        // 長期記憶
        infoInDto.setC2B(CommonConstants.BLANK_STRING);
        // 手続き記憶
        infoInDto.setC2C(CommonConstants.BLANK_STRING);
        // 状況記憶
        infoInDto.setC2D(CommonConstants.BLANK_STRING);
        // c3_メモ
        infoInDto.setC3MemoKnj(CommonConstants.BLANK_STRING);
        // c3_メモフォント
        infoInDto.setC3MemoFont(CommonConstants.STR_12);
        // c3_メモ色
        infoInDto.setC3MemoColor(CommonConstants.BLANK_STRING);
        // せん妄の兆候_注意
        infoInDto.setC3A(CommonConstants.BLANK_STRING);
        // せん妄の兆候_会話
        infoInDto.setC3B(CommonConstants.BLANK_STRING);
        // せん妄の兆候_精神機能
        infoInDto.setC3C(CommonConstants.BLANK_STRING);
        // c4_メモ
        infoInDto.setC4MemoKnj(CommonConstants.BLANK_STRING);
        // c4_メモフォント
        infoInDto.setC4MemoFont(CommonConstants.STR_12);
        // c4_メモ色
        infoInDto.setC4MemoColor(CommonConstants.BLANK_STRING);
        // 精神状態の急な変化
        infoInDto.setC4(CommonConstants.BLANK_STRING);
        // c5_メモ
        infoInDto.setC5MemoKnj(CommonConstants.BLANK_STRING);
        // c5_メモフォント
        infoInDto.setC5MemoFont(CommonConstants.STR_12);
        // c5_メモ色
        infoInDto.setC5MemoColor(CommonConstants.BLANK_STRING);
        // 意思決定能力の変化
        infoInDto.setC5(CommonConstants.BLANK_STRING);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｃ）帳票パラメータ取得
     * 
     * @param model     入力データ
     * @param outDto    出力データ
     * @param infoInDto 帳票用データ詳細
     * @throws Exception 例外
     */
    private void setCReportParamsFromAssC(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto, AssessmentSheetCReportServiceInDto infoInDto) throws Exception {

        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        // 【リクエストパラメータ】.印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();
        // 2.1. アセスメント表（Ｃ）情報を取得する。
        KghCpnRaiAssCPByCriteriaInEntity kghCpnRaiAssCPByCriteriaInEntity = new KghCpnRaiAssCPByCriteriaInEntity();
        kghCpnRaiAssCPByCriteriaInEntity.setAnRaiId(Integer.valueOf(printSubjectHistoryList.get(0).getRaiId()));
        List<KghCpnRaiAssCPOutEntity> KghCpnRaiAssCPOutEntities = raiRrkKghCpnRaiAssCSelectMapper
                .findKghCpnRaiAssCPByCriteria(kghCpnRaiAssCPByCriteriaInEntity);
        // 2.2.「2.1.」で取得したアセスメント表（Ｃ）情報の件数をワークアセスメント表（Ｃ）情報.総件数に設定する。
        if (CollectionUtils.isNotEmpty(KghCpnRaiAssCPOutEntities)) {
            // 2.2.1 総件数 > 0 件の場合
            KghCpnRaiAssCPOutEntity entity = KghCpnRaiAssCPOutEntities.get(0);
            /*
             * ===============3. 利用者基本情報を取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());

            /*
             * ===============4.アセスメント種別の取得===============
             * 
             */
            Pair<Integer, String> syubetuKnj = this.getSyubetuByRaiId(
                    CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));

            /*
             * =============== 5.帳票用データを設定===============
             * 
             */
            // 調査アセスメント種別名
            infoInDto.setSyubetuKnj(syubetuKnj.getRight());
            // 調査アセスメント種別
            infoInDto.setAssType(syubetuKnj.getLeft());
            // 指定日（年号）=""
            ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                    printSet.getShiTeiDate(), model.getSystemDate());
            infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
            // 指定日（年）
            infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
            // 指定日（月）
            infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
            // 指定日（日）
            infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());
            // 氏名（姓）
            infoInDto.setName1Knj(userPair.getLeft());
            // 氏名（名）
            infoInDto.setName2Knj(userPair.getRight());
            // c1_メモ
            infoInDto.setC1MemoKnj(ReportUtil.nullToEmpty(entity.getC1MemoKnj()));
            // c1_メモフォント
            infoInDto.setC1MemoFont(getMemoFont(entity.getC1MemoFont()));
            // リクエストパラメータ.印刷時に色をつけるフラグ
            Boolean colorFlg = false;
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg())
                    || CommonConstants.STR_1.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }
            // c1_メモ色
            infoInDto.setC1MemoColor(getMemoColor(entity.getC1MemoColor(), colorFlg));
            // 日常の意思決定の認知能力
            infoInDto.setC1(ReportUtil.nullToEmpty(entity.getC1()));

            // c2_メモ
            infoInDto.setC2MemoKnj(ReportUtil.nullToEmpty(entity.getC2MemoKnj()));
            // c2_メモフォント
            infoInDto.setC2MemoFont(getMemoFont(entity.getC2MemoFont()));
            // c2_メモ色
            infoInDto.setC2MemoColor(getMemoColor(entity.getC2MemoColor(), colorFlg));
            // 短期記憶
            infoInDto.setC2A(ReportUtil.nullToEmpty(entity.getC2A()));
            // 長期記憶
            infoInDto.setC2B(ReportUtil.nullToEmpty(entity.getC2B()));
            // 手続き記憶
            infoInDto.setC2C(ReportUtil.nullToEmpty(entity.getC2C()));
            // 状況記憶
            infoInDto.setC2D(ReportUtil.nullToEmpty(entity.getC2D()));

            // c3_メモ
            infoInDto.setC3MemoKnj(ReportUtil.nullToEmpty(entity.getC3MemoKnj()));
            // c3_メモフォント
            infoInDto.setC3MemoFont(getMemoFont(entity.getC3MemoFont()));
            // c3_メモ色
            infoInDto.setC3MemoColor(getMemoColor(entity.getC3MemoColor(), colorFlg));
            // せん妄の兆候_注意
            infoInDto.setC3A(ReportUtil.nullToEmpty(entity.getC3A()));
            // せん妄の兆候_会話
            infoInDto.setC3B(ReportUtil.nullToEmpty(entity.getC3B()));
            // せん妄の兆候_精神機能
            infoInDto.setC3C(ReportUtil.nullToEmpty(entity.getC3C()));

            // c4_メモ
            infoInDto.setC4MemoKnj(ReportUtil.nullToEmpty(entity.getC4MemoKnj()));
            // c4_メモフォント
            infoInDto.setC4MemoFont(getMemoFont(entity.getC4MemoFont()));
            // c4_メモ色
            infoInDto.setC4MemoColor(getMemoColor(entity.getC4MemoColor(), colorFlg));
            // 精神状態の急な変化
            infoInDto.setC4(ReportUtil.nullToEmpty(entity.getC4()));

            // c5_メモ
            infoInDto.setC5MemoKnj(ReportUtil.nullToEmpty(entity.getC5MemoKnj()));
            // c5_メモフォント
            infoInDto.setC5MemoFont(getMemoFont(entity.getC5MemoFont()));
            // c5_メモ色
            infoInDto.setC5MemoColor(getMemoColor(entity.getC5MemoColor(), colorFlg));
            // 意思決定能力の変化
            infoInDto.setC5(ReportUtil.nullToEmpty(entity.getC5()));

            // 文書管理番号
            infoInDto.setBunsyoKanriNo(getBunsyoKanriNo(model.getSyscd(),
                    printSubjectHistoryList.get(0).getChoPrtList().get(0).getShokuId(),
                    printSubjectHistoryList.get(0).getChoPrtList().get(0).getSection(),
                    model.getJigyoInfo().getSvJigyoId()));
        }
    }

    /**
     * U06080_アセスメント表（Ｄ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetDReportServiceInDto getDReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        AssessmentSheetDReportServiceInDto infoInDto = new AssessmentSheetDReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、アセスメント表（Ｄ）情報の取得。===============
         * 
         */
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            infoInDto = this.getDDefReportParams(model, outDto);
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.TRUE);
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getDReportParamsFromAssD(model, outDto);
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.FALSE);
        }
        // 指定日印刷区分
        infoInDto.setPrDate(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｄ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetDReportServiceInDto getDDefReportParams(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetDReportServiceInDto infoInDto = new AssessmentSheetDReportServiceInDto();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(Integer.valueOf(
                StringUtils.isEmpty(printOption.getKinyuAssType().trim()) ? CommonConstants.STR_ZERO
                        : printOption.getKinyuAssType())));

        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 事業者名
        infoInDto.setJigyosha(model.getSvJigyoKnj());
        // 指定日（年号）=""
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）=""
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）=""
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）=""
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // d1_メモ
        infoInDto.setD1MemoKnj(CommonConstants.BLANK_STRING);
        // d1_メモフォント
        infoInDto.setD1MemoFont(CommonConstants.STR_12);
        // d1_メモ色
        infoInDto.setD1MemoColor(CommonConstants.BLANK_STRING);
        // 理解させる能力
        infoInDto.setD1(CommonConstants.BLANK_STRING);
        // d2_メモ
        infoInDto.setD2MemoKnj(CommonConstants.BLANK_STRING);
        // d2_メモフォント
        infoInDto.setD2MemoFont(CommonConstants.STR_12);
        // d2_メモ色
        infoInDto.setD2MemoColor(CommonConstants.BLANK_STRING);
        // 理解できる能力
        infoInDto.setD2(CommonConstants.BLANK_STRING);
        // d3_a_メモ
        infoInDto.setD3AMemoKnj(CommonConstants.BLANK_STRING);
        // d3_a_メモフォント
        infoInDto.setD3AMemoFont(CommonConstants.STR_12);
        // d3_a_メモ色
        infoInDto.setD3AMemoColor(CommonConstants.BLANK_STRING);
        // 聴力
        infoInDto.setD3A(CommonConstants.BLANK_STRING);
        // d3_b_メモ
        infoInDto.setD3BMemoKnj(CommonConstants.BLANK_STRING);
        // d3_b_メモフォント
        infoInDto.setD3BMemoFont(CommonConstants.STR_12);
        // d3_b_メモ色
        infoInDto.setD3BMemoColor(CommonConstants.BLANK_STRING);
        // 補聴器の使用
        infoInDto.setD3B(CommonConstants.BLANK_STRING);
        // d4_a_メモ
        infoInDto.setD4AMemoKnj(CommonConstants.BLANK_STRING);
        // d4_a_メモフォント
        infoInDto.setD4AMemoFont(CommonConstants.STR_12);
        // d4_a_メモ色
        infoInDto.setD4AMemoColor(CommonConstants.BLANK_STRING);
        // 視力
        infoInDto.setD4A(CommonConstants.BLANK_STRING);
        // d4_b_メモ
        infoInDto.setD4BMemoKnj(CommonConstants.BLANK_STRING);
        // d4_b_メモフォント
        infoInDto.setD4BMemoFont(CommonConstants.STR_12);
        // d4_b_メモ色
        infoInDto.setD4BMemoColor(CommonConstants.BLANK_STRING);
        // 眼鏡コンタクトレンズ拡大鏡の使用
        infoInDto.setD4B(CommonConstants.BLANK_STRING);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｄ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetDReportServiceInDto getDReportParamsFromAssD(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetDReportServiceInDto infoInDto = new AssessmentSheetDReportServiceInDto();

        // 【リクエストパラメータ】.印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        // 2.1. アセスメント表（Ｄ）情報を取得する。
        KghCpnRaiAssDPByCriteriaInEntity kghCpnRaiAssDPByCriteriaInEntity = new KghCpnRaiAssDPByCriteriaInEntity();
        kghCpnRaiAssDPByCriteriaInEntity
                .setAnRaiId(Integer.valueOf(printSubjectHistoryList.get(0).getRaiId()));
        List<KghCpnRaiAssDPOutEntity> KghCpnRaiAssDPOutEntities = raiRrkKghCpnRaiAssDSelectMapper
                .findKghCpnRaiAssDPByCriteria(kghCpnRaiAssDPByCriteriaInEntity);
        // 2.2.「2.1.」で取得したアセスメント表（Ｄ）情報の件数をワークアセスメント表（Ｄ）情報.総件数に設定する。
        if (KghCpnRaiAssDPOutEntities != null && KghCpnRaiAssDPOutEntities.size() > 0) {
            // 2.2.1 総件数 > 0 件の場合
            KghCpnRaiAssDPOutEntity entity = KghCpnRaiAssDPOutEntities.get(0);
            /*
             * ===============3. 利用者基本情報を取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());
            /*
             * ===============4.アセスメント種別の取得===============
             * 
             */
            Pair<Integer, String> syubetPair = this
                    .getSyubetuByRaiId(Integer.valueOf(printSubjectHistoryList.get(0).getRaiId()));

            /*
             * =============== 5.帳票用データを設定===============
             * 
             */
            // 指定日印刷区分
            infoInDto.setPrDate(Integer.valueOf(printSet.getShiTeiKubun()));
            // 帳票タイトル
            infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
            // 記入用フラグ
            infoInDto.setEmptyFlg(CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg()));
            // 事業者名
            infoInDto.setJigyosha(ReportUtil.nullToEmpty(model.getSvJigyoKnj()));
            // 氏名（姓）
            infoInDto.setName1Knj(ReportUtil.nullToEmpty(userPair.getLeft()));
            // 氏名（名）
            infoInDto.setName2Knj(ReportUtil.nullToEmpty(userPair.getRight()));
            // 指定日（年号）=""
            ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                    printSet.getShiTeiDate(), model.getSystemDate());
            infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
            // 指定日（年）=""
            infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
            // 指定日（月）=""
            infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
            // 指定日（日）=""
            infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());
            // アセスメント種別
            infoInDto.setSyubetuKnj(syubetPair.getRight());
            // d1_メモ
            infoInDto.setD1MemoKnj(ReportUtil.nullToEmpty(entity.getD1MemoKnj()));
            // d1_メモフォント
            infoInDto.setD1MemoFont(getMemoFont(entity.getD1MemoFont()));
            // d1_メモ色
            Boolean colorFlg = false;
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg())
                    || CommonConstants.STR_1.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }
            infoInDto.setD1MemoColor(getMemoColor(entity.getD1MemoColor(), colorFlg));
            // 理解させる能力
            infoInDto.setD1(ReportUtil.nullToEmpty(entity.getD1()));
            // d2_メモ
            infoInDto.setD2MemoKnj(ReportUtil.nullToEmpty(entity.getD2MemoKnj()));
            // d2_メモフォント
            infoInDto.setD2MemoFont(getMemoFont(entity.getD2MemoFont()));
            // d2_メモ色
            infoInDto.setD2MemoColor(getMemoColor(entity.getD2MemoColor(), colorFlg));
            // 理解できる能力
            infoInDto.setD2(ReportUtil.nullToEmpty(entity.getD2()));
            // d3_a_メモ
            infoInDto.setD3AMemoKnj(ReportUtil.nullToEmpty(entity.getD3AMemoKnj()));
            // d3_a_メモフォント
            infoInDto.setD3AMemoFont(getMemoFont(entity.getD3AMemoFont()));
            // d3_a_メモ色
            infoInDto.setD3AMemoColor(getMemoColor(entity.getD3AMemoColor(), colorFlg));
            // 聴力
            infoInDto.setD3A(ReportUtil.nullToEmpty(entity.getD3A()));
            // d3_b_メモ
            infoInDto.setD3BMemoKnj(ReportUtil.nullToEmpty(entity.getD3BMemoKnj()));
            // d3_b_メモフォント
            infoInDto.setD3BMemoFont(getMemoFont(entity.getD3BMemoFont()));
            // d3_b_メモ色
            infoInDto.setD3BMemoColor(getMemoColor(entity.getD3BMemoColor(), colorFlg));
            // 補聴器の使用
            infoInDto.setD3B(ReportUtil.nullToEmpty(entity.getD3B()));
            // d4_a_メモ
            infoInDto.setD4AMemoKnj(ReportUtil.nullToEmpty(entity.getD4AMemoKnj()));
            // d4_a_メモフォント
            infoInDto.setD4AMemoFont(getMemoFont(entity.getD4AMemoFont()));
            // d4_a_メモ色
            infoInDto.setD4AMemoColor(getMemoColor(entity.getD4AMemoColor(), colorFlg));
            // 視力
            infoInDto.setD4A(ReportUtil.nullToEmpty(entity.getD4A()));
            // d4_b_メモ
            infoInDto.setD4BMemoKnj(ReportUtil.nullToEmpty(entity.getD4BMemoKnj()));
            // d4_b_メモフォント
            infoInDto.setD4BMemoFont(getMemoFont(entity.getD4BMemoFont()));
            // d4_b_メモ色
            infoInDto.setD4BMemoColor(getMemoColor(entity.getD4BMemoColor(), colorFlg));
            // 眼鏡コンタクトレンズ拡大鏡の使用
            infoInDto.setD4B(ReportUtil.nullToEmpty(entity.getD4B()));
            /*
             * ===============共通関数補足 2.文書番号情報取得処理===============
             * 
             */
            // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01 関数名：GetF3GkProfile
            // を利用し、設定の読込（システム環境以外の設定）情報を取得する。
            // 【リクエストパラメータ】.システムコード
            String syscd = model.getSyscd();
            // 職員ID
            String shokuId = printSubjectHistoryList.get(0).getChoPrtList().get(0).getShokuId();
            // セクション
            String section = printSubjectHistoryList.get(0).getChoPrtList().get(0).getSection();
            // 【リクエストパラメータ】.事業者ID
            String svJigyoId = model.getJigyoInfo().getSvJigyoId();

            String bunsyoKanriNo = this.getBunsyoKanriNo(syscd, shokuId, section, svJigyoId);
            // 文書管理番号
            infoInDto.setBunsyoKanriNo(ReportUtil.nullToEmpty(bunsyoKanriNo));
        }

        return infoInDto;
    }

    /**
     * U06080_アセスメント表（Ｅ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetEReportServiceInDto getEReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        AssessmentSheetEReportServiceInDto infoInDto = new AssessmentSheetEReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、アセスメント表（Ｅ）情報の取得。===============
         * 
         */
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷対象履歴リスト
        ReportCommonPrintSubjectHistory printSubjectHistory = model.getPrintSubjectHistoryList().get(0);
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            infoInDto = this.getEDefReportParams(printOption.getKinyuAssType());
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.TRUE);
        } else {
            Boolean colorFlg = false;
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg())
                    || CommonConstants.STR_1.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getEReportParamsFromAssE(printSubjectHistory, printSet.getShiTeiKubun(),
                    model.getSystemDate(), printSet.getShiTeiDate(), colorFlg, model.getSyscd(),
                    model.getJigyoInfo().getSvJigyoId());
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.FALSE);
        }

        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 事業者名
        infoInDto.setJigyousha(model.getSvJigyoKnj());
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｅ）帳票パラメータ取得
     * 
     * @param kinyuAssType アセスメント種別
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetEReportServiceInDto getEDefReportParams(String kinyuAssType) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetEReportServiceInDto infoInDto = new AssessmentSheetEReportServiceInDto();

        // アセスメント種別
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(kinyuAssType)));
        // 指定日（年号）=""
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）=""
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）=""
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）=""
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // e1_メモ
        infoInDto.setE1MemoKnj(CommonConstants.BLANK_STRING);
        // e1_メモフォント
        infoInDto.setE1MemoFont(CommonConstants.STR_12);
        // e1_メモ色
        infoInDto.setE1MemoColor(CommonConstants.BLANK_STRING);
        // 気分の兆候_否定
        infoInDto.setE1A(CommonConstants.BLANK_STRING);
        // 気分の兆候_怒り
        infoInDto.setE1B(CommonConstants.BLANK_STRING);
        // 気分の兆候_恐れ
        infoInDto.setE1C(CommonConstants.BLANK_STRING);
        // 気分の兆候_不調
        infoInDto.setE1D(CommonConstants.BLANK_STRING);
        // 気分の兆候_不安
        infoInDto.setE1E(CommonConstants.BLANK_STRING);
        // 気分の兆候_悲しみ、苦悩、心配
        infoInDto.setE1F(CommonConstants.BLANK_STRING);
        // 気分の兆候_泣く
        infoInDto.setE1G(CommonConstants.BLANK_STRING);
        // 気分の兆候_ひどいことが起こりそう
        infoInDto.setE1H(CommonConstants.BLANK_STRING);
        // 気分の兆候_興味
        infoInDto.setE1I(CommonConstants.BLANK_STRING);
        // 気分の兆候_社会的交流の減少
        infoInDto.setE1J(CommonConstants.BLANK_STRING);
        // 気分の兆候_快感喪失
        infoInDto.setE1K(CommonConstants.BLANK_STRING);
        // e2_メモ
        infoInDto.setE2MemoKnj(CommonConstants.BLANK_STRING);
        // e2_メモフォント
        infoInDto.setE2MemoFont(CommonConstants.STR_12);
        // e2_メモ色
        infoInDto.setE2MemoColor(CommonConstants.BLANK_STRING);
        // 利用者自身_興味や喜び
        infoInDto.setE2A(CommonConstants.BLANK_STRING);
        // 利用者自身_不安
        infoInDto.setE2B(CommonConstants.BLANK_STRING);
        // 利用者自身_絶望
        infoInDto.setE2C(CommonConstants.BLANK_STRING);
        // e3_a_メモ
        infoInDto.setE3MemoKnj(CommonConstants.BLANK_STRING);
        // e3_a_メモフォント
        infoInDto.setE3MemoFont(CommonConstants.STR_12);
        // e3_a_メモ色
        infoInDto.setE3MemoColor(CommonConstants.BLANK_STRING);
        // 行動の問題_徘徊
        infoInDto.setE3A(CommonConstants.BLANK_STRING);
        // 行動の問題_暴言
        infoInDto.setE3B(CommonConstants.BLANK_STRING);
        // 行動の問題_暴行
        infoInDto.setE3C(CommonConstants.BLANK_STRING);
        // 行動の問題_社会的迷惑行為
        infoInDto.setE3D(CommonConstants.BLANK_STRING);
        // 行動の問題_性的行動や脱衣
        infoInDto.setE3E(CommonConstants.BLANK_STRING);
        // 行動の問題_抵抗
        infoInDto.setE3F(CommonConstants.BLANK_STRING);
        // 行動の問題_退居・家出
        infoInDto.setE3G(CommonConstants.BLANK_STRING);
        // e4_a_メモ
        infoInDto.setE4MemoKnj(CommonConstants.BLANK_STRING);
        // e4_a_メモフォント
        infoInDto.setE4MemoFont(CommonConstants.STR_12);
        // e4_a_メモ色
        infoInDto.setE4MemoColor(CommonConstants.BLANK_STRING);
        // 生活満足度
        infoInDto.setE4(CommonConstants.BLANK_STRING);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｅ）帳票パラメータ取得
     * 
     * @param history     印刷対象履歴
     * @param shiTeiKubun 指定日印刷区分
     * @param systemDate  システム日付
     * @param shiTeiDate  西暦日付
     * @param colorFlg    印刷時に色をつけるフラグ
     * @param syscd       システムコード
     * @param svJigyoId   サービス事業者ID
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetEReportServiceInDto getEReportParamsFromAssE(ReportCommonPrintSubjectHistory history,
            String shiTeiKubun,
            String systemDate, String shiTeiDate, Boolean colorFlg, String syscd, String svJigyoId) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetEReportServiceInDto infoInDto = new AssessmentSheetEReportServiceInDto();

        // 2.1. アセスメント表（Ｅ）情報を取得する。
        KghCpnRaiAssEPByCriteriaInEntity kghCpnRaiAssEPByCriteriaInEntity = new KghCpnRaiAssEPByCriteriaInEntity();
        kghCpnRaiAssEPByCriteriaInEntity.setAnRaiId(Integer.valueOf(history.getRaiId()));
        List<KghCpnRaiAssEPOutEntity> assEList = raiRrkKghCpnRaiAssESelectMapper
                .findKghCpnRaiAssEPByCriteria(kghCpnRaiAssEPByCriteriaInEntity);
        // 2.2.「2.1.」で取得したアセスメント表（Ｅ）情報の件数をワークアセスメント表（Ｅ）情報.総件数に設定する。
        if (CollectionUtils.isNotEmpty(assEList)) {
            // 2.2.1 総件数 > 0 件の場合
            KghCpnRaiAssEPOutEntity entity = assEList.get(0);
            /*
             * ===============3. 利用者基本情報を取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());

            /*
             * ===============4.アセスメント種別の取得===============
             * 
             */
            Pair<Integer, String> syubetPair = this.getSyubetuByRaiId(Integer.valueOf(history.getRaiId()));

            /*
             * =============== 5.帳票用データを設定===============
             * 
             */
            // アセスメント種別
            infoInDto.setSyubetuKnj(syubetPair.getRight());
            // 指定日（年号）=""
            ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(shiTeiKubun, shiTeiDate, systemDate);
            infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
            // 指定日（年）=""
            infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
            // 指定日（月）=""
            infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
            // 指定日（日）=""
            infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());
            // 氏名（姓）
            infoInDto.setName1Knj(userPair.getLeft());
            // 氏名（名）
            infoInDto.setName2Knj(userPair.getRight());
            // e1_メモ
            infoInDto.setE1MemoKnj(ReportUtil.nullToEmpty(entity.getE1MemoKnj()));
            // 気分の兆候_否定
            infoInDto.setE1A(ReportUtil.nullToEmpty(entity.getE1A()));
            // 気分の兆候_怒り
            infoInDto.setE1B(ReportUtil.nullToEmpty(entity.getE1B()));
            // 気分の兆候_恐れ
            infoInDto.setE1C(ReportUtil.nullToEmpty(entity.getE1C()));
            // 気分の兆候_不調
            infoInDto.setE1D(ReportUtil.nullToEmpty(entity.getE1D()));
            // 気分の兆候_不安
            infoInDto.setE1E(ReportUtil.nullToEmpty(entity.getE1E()));
            // 気分の兆候_悲しみ、苦悩、心配
            infoInDto.setE1F(ReportUtil.nullToEmpty(entity.getE1F()));
            // 気分の兆候_泣く
            infoInDto.setE1G(ReportUtil.nullToEmpty(entity.getE1G()));
            // 気分の兆候_ひどいことが起こりそう
            infoInDto.setE1H(ReportUtil.nullToEmpty(entity.getE1H()));
            // 気分の兆候_興味
            infoInDto.setE1I(ReportUtil.nullToEmpty(entity.getE1I()));
            // 気分の兆候_社会的交流の減少
            infoInDto.setE1J(ReportUtil.nullToEmpty(entity.getE1J()));
            // 気分の兆候_快感喪失
            infoInDto.setE1K(ReportUtil.nullToEmpty(entity.getE1K()));
            // e2_メモ
            infoInDto.setE2MemoKnj(ReportUtil.nullToEmpty(entity.getE2MemoKnj()));
            // 利用者自身_興味や喜び
            infoInDto.setE2A(ReportUtil.nullToEmpty(entity.getE2A()));
            // 利用者自身_不安
            infoInDto.setE2B(ReportUtil.nullToEmpty(entity.getE2B()));
            // 利用者自身_絶望
            infoInDto.setE2C(ReportUtil.nullToEmpty(entity.getE2C()));
            // e3_メモ
            infoInDto.setE3MemoKnj(ReportUtil.nullToEmpty(entity.getE3MemoKnj()));
            // 行動の問題_徘徊
            infoInDto.setE3A(ReportUtil.nullToEmpty(entity.getE3A()));
            // 行動の問題_暴言
            infoInDto.setE3B(ReportUtil.nullToEmpty(entity.getE3B()));
            // 行動の問題_暴行
            infoInDto.setE3C(ReportUtil.nullToEmpty(entity.getE3C()));
            // 行動の問題_社会的迷惑行為
            infoInDto.setE3D(ReportUtil.nullToEmpty(entity.getE3D()));
            // 行動の問題_性的行動や脱衣
            infoInDto.setE3E(ReportUtil.nullToEmpty(entity.getE3E()));
            // 行動の問題_抵抗
            infoInDto.setE3F(ReportUtil.nullToEmpty(entity.getE3F()));
            // 行動の問題_退居・家出
            infoInDto.setE3G(ReportUtil.nullToEmpty(entity.getE3G()));
            // e4_メモ
            infoInDto.setE4MemoKnj(ReportUtil.nullToEmpty(entity.getE4MemoKnj()));
            // 生活満足度
            infoInDto.setE4(ReportUtil.nullToEmpty(entity.getE4()));
            // e1_メモフォント
            infoInDto.setE1MemoFont(getMemoFont(entity.getE1MemoFont()));
            // e2_メモフォント
            infoInDto.setE2MemoFont(getMemoFont(entity.getE2MemoFont()));
            // e3_a_メモフォント
            infoInDto.setE3MemoFont(getMemoFont(entity.getE3MemoFont()));
            // e4_メモフォント
            infoInDto.setE4MemoFont(getMemoFont(entity.getE4MemoFont()));
            // e1_メモ色
            infoInDto.setE1MemoColor(getMemoColor(entity.getE1MemoColor(), colorFlg));
            // e2_メモ色
            infoInDto.setE2MemoColor(getMemoColor(entity.getE2MemoColor(), colorFlg));
            // e3_メモ色
            infoInDto.setE3MemoColor(getMemoColor(entity.getE3MemoColor(), colorFlg));
            // e4_メモ色
            infoInDto.setE4MemoColor(getMemoColor(entity.getE4MemoColor(), colorFlg));
            // 出力帳票印刷情報
            ReportCommonChoPrt choPrtList = history.getChoPrtList().get(0);
            // 文書管理番号
            infoInDto.setBunsyoKanriNo(
                    this.getBunsyoKanriNo(syscd, choPrtList.getShokuId(), choPrtList.getSection(), svJigyoId));
        }

        return infoInDto;
    }

    /**
     * U06080_アセスメント表（Ｆ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    public AssessmentSheetFReportServiceInDto getFReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        AssessmentSheetFReportServiceInDto infoInDto = new AssessmentSheetFReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、アセスメント表（F）情報の取得。===============
         * 
         */
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        Boolean isEmptyFlg = false;
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            isEmptyFlg = true;
        }
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (isEmptyFlg) {
            infoInDto = this.getFDefReportParams(model, outDto);
            // 調査アセスメント種別
            infoInDto.setAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType()));
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getFReportParamsFromAssF(model, outDto);
        }
        // 記入用シートを印刷するフラグ リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(isEmptyFlg);
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // 【リクエストパラメータ】.事業者名
        String svJigyoKnj = model.getSvJigyoKnj();
        // 事業者名
        infoInDto.setJigyousha(ReportUtil.nullToEmpty(svJigyoKnj));

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｆ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetFReportServiceInDto getFDefReportParams(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetFReportServiceInDto infoInDto = new AssessmentSheetFReportServiceInDto();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        String wkAssType = printOption.getKinyuAssType();

        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(wkAssType)));

        // 調査アセスメント種別 ← リクエストパラメータ.データ.印刷オプション.記入用シートアセスメント種別
        infoInDto.setAssType(CommonDtoUtil.strValToInt(wkAssType));

        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);

        // 指定日（年号）
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);

        // 指定日（年）
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);

        // 指定日（月）
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);

        // 指定日（日）
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);

        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);

        // 氏名、（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);

        // f1_メモ
        infoInDto.setF1MemoKnj(CommonConstants.BLANK_STRING);

        // f1_メモフォント
        infoInDto.setF1MemoFont(CommonConstants.STR_12);

        // f1_メモ色
        infoInDto.setF1MemoColor(CommonConstants.BLANK_STRING);

        // 社会関係_活動への参加
        infoInDto.setF1A(CommonConstants.BLANK_STRING);

        // 社会関係_訪問
        infoInDto.setF1B(CommonConstants.BLANK_STRING);

        // 社会関係_その他の交流
        infoInDto.setF1C(CommonConstants.BLANK_STRING);

        // 社会関係_葛藤や怒り
        infoInDto.setF1D(CommonConstants.BLANK_STRING);

        // 社会関係_恐れ
        infoInDto.setF1E(CommonConstants.BLANK_STRING);

        // 社会関係_虐待
        infoInDto.setF1F(CommonConstants.BLANK_STRING);

        // f2_メモ
        infoInDto.setF2MemoKnj(CommonConstants.BLANK_STRING);

        // f2_メモフォント
        infoInDto.setF2MemoFont(CommonConstants.STR_12);

        // f2_メモ色
        infoInDto.setF2MemoColor(CommonConstants.BLANK_STRING);

        // 孤独
        infoInDto.setF2(CommonConstants.BLANK_STRING);

        // f3_メモ
        infoInDto.setF3MemoKnj(CommonConstants.BLANK_STRING);

        // f3_メモフォント
        infoInDto.setF3MemoFont(CommonConstants.STR_12);

        // f3_メモ色
        infoInDto.setF3MemoColor(CommonConstants.BLANK_STRING);

        // 社会的活動の変化
        infoInDto.setF3(CommonConstants.BLANK_STRING);

        // f4_メモ
        infoInDto.setF4MemoKnj(CommonConstants.BLANK_STRING);

        // f4_メモフォント
        infoInDto.setF4MemoFont(CommonConstants.STR_12);

        // f4_メモ色
        infoInDto.setF4MemoColor(CommonConstants.BLANK_STRING);

        // 一人きりでいる時間
        infoInDto.setF4(CommonConstants.BLANK_STRING);

        // f5_メモ
        infoInDto.setF5MemoKnj(CommonConstants.BLANK_STRING);

        // f5_メモフォント
        infoInDto.setF5MemoFont(CommonConstants.STR_12);

        // f5_メモ色
        infoInDto.setF5MemoColor(CommonConstants.BLANK_STRING);

        // 自発性・参加_他者
        infoInDto.setF5A(CommonConstants.BLANK_STRING);

        // 自発性・参加_活動
        infoInDto.setF5B(CommonConstants.BLANK_STRING);

        // 自発性・参加_グループ活動
        infoInDto.setF5C(CommonConstants.BLANK_STRING);

        // 自発性・参加_施設内
        infoInDto.setF5D(CommonConstants.BLANK_STRING);

        // 自発性・参加_他者との交流
        infoInDto.setF5E(CommonConstants.BLANK_STRING);

        // 自発性・参加_肯定
        infoInDto.setF5F(CommonConstants.BLANK_STRING);

        // 自発性・参加_日課の変化
        infoInDto.setF5G(CommonConstants.BLANK_STRING);

        // f6_メモ
        infoInDto.setF6MemoKnj(CommonConstants.BLANK_STRING);

        // f6_メモフォント
        infoInDto.setF6MemoFont(CommonConstants.STR_12);

        // f6_メモ色
        infoInDto.setF6MemoColor(CommonConstants.BLANK_STRING);

        // 対人関係_ほかの利用者
        infoInDto.setF6A(CommonConstants.BLANK_STRING);

        // 対人関係_ケアスタッフ
        infoInDto.setF6B(CommonConstants.BLANK_STRING);

        // 対人関係_対応
        infoInDto.setF6C(CommonConstants.BLANK_STRING);

        // 対人関係_家族や近い友人
        infoInDto.setF6D(CommonConstants.BLANK_STRING);

        // f7_メモ
        infoInDto.setF7MemoKnj(CommonConstants.BLANK_STRING);

        // f7_メモフォント
        infoInDto.setF7MemoFont(CommonConstants.STR_12);

        // f7_メモ色
        infoInDto.setF7MemoColor(CommonConstants.BLANK_STRING);

        // 大きなストレス
        infoInDto.setF7(CommonConstants.BLANK_STRING);

        // f8_メモ
        infoInDto.setF8MemoKnj(CommonConstants.BLANK_STRING);

        // f8_メモフォント
        infoInDto.setF8MemoFont(CommonConstants.STR_12);

        // f8_メモ色
        infoInDto.setF8MemoColor(CommonConstants.BLANK_STRING);

        // 強み_前向き
        infoInDto.setF8A(CommonConstants.BLANK_STRING);

        // 強み_生活
        infoInDto.setF8B(CommonConstants.BLANK_STRING);

        // 強み_家族
        infoInDto.setF8C(CommonConstants.BLANK_STRING);

        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｆ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetFReportServiceInDto getFReportParamsFromAssF(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetFReportServiceInDto infoInDto = new AssessmentSheetFReportServiceInDto();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.システムコード
        String syscd = model.getSyscd();
        // 【リクエストパラメータ】.事業者ID
        String svJigyoId = model.getJigyoInfo().getSvJigyoId();

        // 【リクエストパラメータ】.印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();

        // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
        // 2.1リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false の場合、アセスメント表（Ｆ）情報の取得。
        KghCpnRaiAssFPByCriteriaInEntity kghCpnRaiAssFPByCriteriaInEntity = new KghCpnRaiAssFPByCriteriaInEntity();
        // アセスメントID
        kghCpnRaiAssFPByCriteriaInEntity
                .setAnRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
        // DAOを実行
        List<KghCpnRaiAssFPOutEntity> KghCpnRaiAssFPOutEntitise = this.raiRrkKghCpnRaiAssFSelectMapper
                .findKghCpnRaiAssFPByCriteria(kghCpnRaiAssFPByCriteriaInEntity);

        /*
         * ===============3. 利用者基本情報を取得===============
         * 
         */
        // 3. 「2.」で取得したワークアセスメント表（Ｆ）情報.総件数 > 0 件の場合、利用者基本情報を取得
        KghCpnRaiAssFPOutEntity entity = KghCpnRaiAssFPOutEntitise.get(0);
        Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());

        /*
         * ===============4.アセスメント種別の取得===============
         * 
         */
        Pair<Integer, String> syubetuPair = this
                .getSyubetuByRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(syubetuPair.getRight());
        // 調査アセスメント種別
        infoInDto.setAssType(syubetuPair.getLeft());

        /*
         * ===============5.指定日の空欄表示処理===============
         * 
         */
        // 指定日（年号）
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
        // 指定日（年）
        infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
        // 指定日（月）
        infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
        // 指定日（日）
        infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());
        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);

        // 氏名（姓）=API定義の処理「3.1」の氏名（姓）
        infoInDto.setName1Knj(userPair.getKey());
        // 氏名（名）=API定義の処理「3.1」の氏名（名）
        infoInDto.setName2Knj(userPair.getValue());

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false の場合、f1_メモ=API定義の処理「2.1」のf1_メモ
        infoInDto.setF1MemoKnj(ReportUtil.nullToEmpty(entity.getF1MemoKnj()));

        // 印刷オプション.印刷時に色をつけるフラグ
        Boolean colorFlg = false;
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg())
                || CommonConstants.STR_1.equals(printOption.getColorFlg())) {
            colorFlg = true;
        }

        // f1_メモ色
        infoInDto.setF1MemoColor(getMemoColor(entity.getF1MemoColor(), colorFlg));
        // f2_メモ色
        infoInDto.setF2MemoColor(getMemoColor(entity.getF2MemoColor(), colorFlg));
        // f3_メモ色
        infoInDto.setF3MemoColor(getMemoColor(entity.getF3MemoColor(), colorFlg));
        // f4_メモ色
        infoInDto.setF4MemoColor(getMemoColor(entity.getF4MemoColor(), colorFlg));
        // f5_メモ色
        infoInDto.setF5MemoColor(getMemoColor(entity.getF5MemoColor(), colorFlg));
        // f6_メモ色
        infoInDto.setF6MemoColor(getMemoColor(entity.getF6MemoColor(), colorFlg));
        // f7_メモ色
        infoInDto.setF7MemoColor(getMemoColor(entity.getF7MemoColor(), colorFlg));
        // f8_メモ色
        infoInDto.setF8MemoColor(getMemoColor(entity.getF8MemoColor(), colorFlg));
        // f1_メモフォント
        infoInDto.setF1MemoFont(getMemoFont(entity.getF1MemoFont()));
        // f2_メモフォント
        infoInDto.setF2MemoFont(getMemoFont(entity.getF2MemoFont()));
        // f3_メモフォント
        infoInDto.setF3MemoFont(getMemoFont(entity.getF3MemoFont()));
        // f4_メモフォント
        infoInDto.setF4MemoFont(getMemoFont(entity.getF4MemoFont()));
        // f5_メモフォント
        infoInDto.setF5MemoFont(getMemoFont(entity.getF5MemoFont()));
        // f6_メモフォント
        infoInDto.setF6MemoFont(getMemoFont(entity.getF6MemoFont()));
        // f7_メモフォント
        infoInDto.setF7MemoFont(getMemoFont(entity.getF7MemoFont()));
        // f8_メモフォント
        infoInDto.setF8MemoFont(getMemoFont(entity.getF8MemoFont()));

        // 社会関係_活動への参加
        infoInDto.setF1A(ReportUtil.nullToEmpty(entity.getF1A()));

        // 社会関係_訪問
        infoInDto.setF1B(ReportUtil.nullToEmpty(entity.getF1B()));

        // 社会関係_その他の交流
        infoInDto.setF1C(ReportUtil.nullToEmpty(entity.getF1C()));

        // 社会関係_葛藤や怒り
        infoInDto.setF1D(ReportUtil.nullToEmpty(entity.getF1D()));

        // 社会関係_恐れ
        infoInDto.setF1E(ReportUtil.nullToEmpty(entity.getF1E()));

        // 社会関係_虐待
        infoInDto.setF1F(ReportUtil.nullToEmpty(entity.getF1F()));

        // f2_メモ
        infoInDto.setF2MemoKnj(ReportUtil.nullToEmpty(entity.getF2MemoKnj()));

        // 孤独
        infoInDto.setF2(ReportUtil.nullToEmpty(entity.getF2()));

        // f3_メモ
        infoInDto.setF3MemoKnj(ReportUtil.nullToEmpty(entity.getF3MemoKnj()));

        // 社会的活動の変化
        infoInDto.setF3(ReportUtil.nullToEmpty(entity.getF3()));

        // f4_メモ
        infoInDto.setF4MemoKnj(ReportUtil.nullToEmpty(entity.getF4MemoKnj()));

        // 一人きりでいる時間
        infoInDto.setF4(ReportUtil.nullToEmpty(entity.getF4()));

        // f5_メモ
        infoInDto.setF5MemoKnj(ReportUtil.nullToEmpty(entity.getF5MemoKnj()));

        // 自発性・参加_他者
        infoInDto.setF5A(ReportUtil.nullToEmpty(entity.getF5A()));

        // 自発性・参加_活動
        infoInDto.setF5B(ReportUtil.nullToEmpty(entity.getF5B()));

        // 自発性・参加_グループ活動
        infoInDto.setF5C(ReportUtil.nullToEmpty(entity.getF5C()));

        // 自発性・参加_施設内
        infoInDto.setF5D(ReportUtil.nullToEmpty(entity.getF5D()));

        // 自発性・参加_他者との交流
        infoInDto.setF5E(ReportUtil.nullToEmpty(entity.getF5E()));

        // 自発性・参加_肯定
        infoInDto.setF5F(ReportUtil.nullToEmpty(entity.getF5F()));

        // 自発性・参加_日課の変化
        infoInDto.setF5G(ReportUtil.nullToEmpty(entity.getF5G()));

        // f6_メモ
        infoInDto.setF6MemoKnj(ReportUtil.nullToEmpty(entity.getF6MemoKnj()));

        // 対人関係_ほかの利用者
        infoInDto.setF6A(ReportUtil.nullToEmpty(entity.getF6A()));

        // 対人関係_ケアスタッフ
        infoInDto.setF6B(ReportUtil.nullToEmpty(entity.getF6B()));

        // 対人関係_対応
        infoInDto.setF6C(ReportUtil.nullToEmpty(entity.getF6C()));

        // 対人関係_家族や近い友人
        infoInDto.setF6D(ReportUtil.nullToEmpty(entity.getF6D()));

        // f7_メモ
        infoInDto.setF7MemoKnj(ReportUtil.nullToEmpty(entity.getF7MemoKnj()));

        // 大きなストレス
        infoInDto.setF7(ReportUtil.nullToEmpty(entity.getF7()));

        // f7_メモ
        infoInDto.setF8MemoKnj(ReportUtil.nullToEmpty(entity.getF8MemoKnj()));

        // 強み_前向き
        infoInDto.setF8A(ReportUtil.nullToEmpty(entity.getF8A()));

        // 強み_生活
        infoInDto.setF8B(ReportUtil.nullToEmpty(entity.getF8B()));

        // 強み_家族
        infoInDto.setF8C(ReportUtil.nullToEmpty(entity.getF8C()));

        /*
         * ===============共通関数補足 2.文書番号情報取得処理===============
         * 
         */
        // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01 関数名：GetF3GkProfile
        // を利用し、設定の読込（システム環境以外の設定）情報を取得する。
        // 【リクエストパラメータ】.システムコード
        String sysCode = syscd;
        // 職員ID←リクエストパラメータ.データ.印刷対象履歴リスト[0].出力帳票印刷情報リスト[0].職員ID
        String shokuId = printSubjectHistoryList.get(0).getChoPrtList().get(0).getShokuId();
        // セクション←リクエストパラメータ.データ.出力帳票印刷情報リスト[0].セクション
        String sectionKnj = printSubjectHistoryList.get(0).getChoPrtList().get(0).getSection();

        String bunsyoKanriNo = this.getBunsyoKanriNo(sysCode, shokuId, sectionKnj, svJigyoId);
        // 文書番号情報の取得
        infoInDto.setBunsyoKanriNo(ReportUtil.nullToEmpty(bunsyoKanriNo));

        return infoInDto;
    }

    /**
     * U06080_アセスメント表（Ｇ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    public AssessmentSheetGReportServiceInDto getGReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        AssessmentSheetGReportServiceInDto infoInDto = new AssessmentSheetGReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、アセスメント表（F）情報の取得。===============
         * 
         */
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            infoInDto = this.getGDefReportParams(model, outDto);
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.TRUE);
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getGReportParamsFromAssG(model, outDto);
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.FALSE);
        }

        // 【リクエストパラメータ】.事業者名
        String svJigyoKnj = model.getSvJigyoKnj();
        // 事業者名
        infoInDto.setJigyousha(svJigyoKnj);
        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｇ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetGReportServiceInDto getGDefReportParams(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetGReportServiceInDto infoInDto = new AssessmentSheetGReportServiceInDto();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        // リクエストパラメータ.記入用シートを印刷するフラグ = true の場合
        String wkAssType = printOption.getKinyuAssType();

        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(wkAssType)));

        // 調査アセスメント種別
        infoInDto.setAssType(CommonDtoUtil.strValToInt(wkAssType));

        // アセスメント種別情報を取得
        infoInDto.setAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType()));

        // 指定日（年号）
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);

        // 指定日（年）
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);

        // 指定日（月）
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);

        // 指定日（日）
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);

        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);

        // 氏名、（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);

        // g1_メモ
        infoInDto.setG1MemoKnj(CommonConstants.BLANK_STRING);

        // g1_メモフォント
        infoInDto.setG1MemoFont(CommonConstants.STR_12);

        // g1_メモ色
        infoInDto.setG1MemoColor(CommonConstants.BLANK_STRING);

        // IADL_食事の用意_実施
        infoInDto.setG1AA(CommonConstants.BLANK_STRING);

        // IADL_家事一般_実施
        infoInDto.setG1BA(CommonConstants.BLANK_STRING);

        // IADL_金銭管理_実施
        infoInDto.setG1CA(CommonConstants.BLANK_STRING);

        // IADL_薬の管理_実施
        infoInDto.setG1DA(CommonConstants.BLANK_STRING);

        // IADL_電話の利用_実施
        infoInDto.setG1EA(CommonConstants.BLANK_STRING);

        // IADL_階段_実施
        infoInDto.setG1FA(CommonConstants.BLANK_STRING);

        // IADL_買い物_実施
        infoInDto.setG1GA(CommonConstants.BLANK_STRING);

        // IADL_外出_実施
        infoInDto.setG1HA(CommonConstants.BLANK_STRING);

        // IADL_食事の用意_能力
        infoInDto.setG1AB(CommonConstants.BLANK_STRING);

        // IADL_家事一般_能力
        infoInDto.setG1BB(CommonConstants.BLANK_STRING);

        // IADL_金銭管理_能力
        infoInDto.setG1CB(CommonConstants.BLANK_STRING);

        // IADL_薬の管理_能力
        infoInDto.setG1DB(CommonConstants.BLANK_STRING);

        // IADL_電話の利用_能力
        infoInDto.setG1EB(CommonConstants.BLANK_STRING);

        // IADL_階段_能力
        infoInDto.setG1FB(CommonConstants.BLANK_STRING);

        // IADL_買い物_能力
        infoInDto.setG1GB(CommonConstants.BLANK_STRING);

        // IADL_外出_能力
        infoInDto.setG1HB(CommonConstants.BLANK_STRING);

        // g2_メモ
        infoInDto.setG2MemoKnj(CommonConstants.BLANK_STRING);

        // g2_メモフォント
        infoInDto.setG2MemoFont(CommonConstants.STR_12);

        // g2_メモ色
        infoInDto.setG2MemoColor(CommonConstants.BLANK_STRING);

        // ADL_入浴
        infoInDto.setG2A(CommonConstants.BLANK_STRING);

        // ADL_個人衛生
        infoInDto.setG2B(CommonConstants.BLANK_STRING);

        // ADL_上半身の更衣
        infoInDto.setG2C(CommonConstants.BLANK_STRING);

        // ADL_下半身の更衣
        infoInDto.setG2D(CommonConstants.BLANK_STRING);

        // ADL_歩行
        infoInDto.setG2E(CommonConstants.BLANK_STRING);

        // ADL_移動
        infoInDto.setG2F(CommonConstants.BLANK_STRING);

        // ADL_トイレへの移乗
        infoInDto.setG2G(CommonConstants.BLANK_STRING);

        // ADL_トイレの使用
        infoInDto.setG2H(CommonConstants.BLANK_STRING);

        // ADL_ベッド上の可動性
        infoInDto.setG2I(CommonConstants.BLANK_STRING);

        // ADL_食事
        infoInDto.setG2J(CommonConstants.BLANK_STRING);

        // g3_a_メモ
        infoInDto.setG3AMemoKnj(CommonConstants.BLANK_STRING);

        // g3_a_メモフォント
        infoInDto.setG3AMemoFont(CommonConstants.STR_12);

        // g3_a_メモ色
        infoInDto.setG3AMemoColor(CommonConstants.BLANK_STRING);

        // 主な室内移動手段
        infoInDto.setG3A(CommonConstants.BLANK_STRING);

        // g3_b_メモ
        infoInDto.setG3BMemoKnj(CommonConstants.BLANK_STRING);

        // g3_b_メモフォント
        infoInDto.setG3BMemoFont(CommonConstants.STR_12);

        // g3_b_メモ色
        infoInDto.setG3BMemoColor(CommonConstants.BLANK_STRING);

        // 4メートルの歩行時間
        infoInDto.setG3B(CommonConstants.BLANK_STRING);

        // g3_c_メモ
        infoInDto.setG3CMemoKnj(CommonConstants.BLANK_STRING);

        // g3_c_メモフォント
        infoInDto.setG3CMemoFont(CommonConstants.STR_12);

        // g3_c_メモ色
        infoInDto.setG3CMemoColor(CommonConstants.BLANK_STRING);

        // 歩行距離
        infoInDto.setG3C(CommonConstants.BLANK_STRING);

        // g3_d_メモ
        infoInDto.setG3DMemoKnj(CommonConstants.BLANK_STRING);

        // g3_d_メモフォント
        infoInDto.setG3DMemoFont(CommonConstants.STR_12);

        // g3_d_メモ色
        infoInDto.setG3DMemoColor(CommonConstants.BLANK_STRING);

        // 車いす自操距離
        infoInDto.setG3D(CommonConstants.BLANK_STRING);

        // g4_a_メモ
        infoInDto.setG4AMemoKnj(CommonConstants.BLANK_STRING);

        // g4_a_メモフォント
        infoInDto.setG4AMemoFont(CommonConstants.STR_12);

        // g4_a_メモ色
        infoInDto.setG4AMemoColor(CommonConstants.BLANK_STRING);

        // 活動状況_体を動かした時間
        infoInDto.setG4A(CommonConstants.BLANK_STRING);

        // g4_b_メモ
        infoInDto.setG4BMemoKnj(CommonConstants.BLANK_STRING);

        // g4_b_メモフォント
        infoInDto.setG4BMemoFont(CommonConstants.STR_12);

        // g4_b_メモ色
        infoInDto.setG4BMemoColor(CommonConstants.BLANK_STRING);

        // 活動状況_外に出た日数
        infoInDto.setG4B(CommonConstants.BLANK_STRING);

        // g5_メモ
        infoInDto.setG5MemoKnj(CommonConstants.BLANK_STRING);

        // g5_メモフォント
        infoInDto.setG5MemoFont(CommonConstants.STR_12);

        // g5_メモ色
        infoInDto.setG5MemoColor(CommonConstants.BLANK_STRING);

        // 身体機能_本人
        infoInDto.setG5A(CommonConstants.BLANK_STRING);

        // 身体機能_ケアスタッフ
        infoInDto.setG5B(CommonConstants.BLANK_STRING);

        // g6_メモ
        infoInDto.setG6MemoKnj(CommonConstants.BLANK_STRING);

        // g6_メモフォント
        infoInDto.setG6MemoFont(CommonConstants.STR_12);

        // g6_メモ色
        infoInDto.setG6MemoColor(CommonConstants.BLANK_STRING);

        // ADLの変化
        infoInDto.setG6(CommonConstants.BLANK_STRING);

        // g7_メモ
        infoInDto.setG7MemoKnj(CommonConstants.BLANK_STRING);

        // g7_メモフォント
        infoInDto.setG7MemoFont(CommonConstants.STR_12);

        // g7_メモ色
        infoInDto.setG7MemoColor(CommonConstants.BLANK_STRING);

        // 自動車の運転
        infoInDto.setG7A(CommonConstants.BLANK_STRING);

        // 自動車の運転_制限
        infoInDto.setG7B(CommonConstants.BLANK_STRING);

        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｇ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetGReportServiceInDto getGReportParamsFromAssG(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetGReportServiceInDto infoInDto = new AssessmentSheetGReportServiceInDto();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.システムコード
        String syscd = model.getSyscd();
        // 【リクエストパラメータ】.事業者ID
        String svJigyoId = model.getJigyoInfo().getSvJigyoId();

        // 【リクエストパラメータ】.印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();

        // 2.リクエストパラメータ.記入用シートを印刷するフラグ = false の場合、アセスメント表（Ｇ）情報を取得
        KghCpnRaiAssGPByCriteriaInEntity kghCpnRaiAssGPByCriteriaInEntity = new KghCpnRaiAssGPByCriteriaInEntity();
        // アセスメントID
        kghCpnRaiAssGPByCriteriaInEntity
                .setAnRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
        // DAOを実行
        List<KghCpnRaiAssGPOutEntity> kghCpnRaiAssGPOutEntitise = this.raiRrkKghCpnRaiAssGSelectMapper
                .findKghCpnRaiAssGPByCriteria(kghCpnRaiAssGPByCriteriaInEntity);
        // 2.2. 「2.1」で取得したアセスメント表（Ｇ）情報の件数をワークアセスメント表（Ｇ）情報.総件数に設定
        if (CollectionUtils.isNotEmpty(kghCpnRaiAssGPOutEntitise)) {
            KghCpnRaiAssGPOutEntity entity = kghCpnRaiAssGPOutEntitise.get(0);
            /*
             * ===============3. 利用者基本情報を取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());

            /*
             * ===============4.アセスメント種別の取得===============
             * 
             */
            Pair<Integer, String> syubetuPair = this
                    .getSyubetuByRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
            // 調査アセスメント種別名
            infoInDto.setSyubetuKnj(syubetuPair.getRight());
            // 調査アセスメント種別
            infoInDto.setAssType(syubetuPair.getLeft());

            /*
             * =============== 5.帳票用データを設定===============
             * 
             */
            // 指定日（年号）
            ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                    printSet.getShiTeiDate(), model.getSystemDate());
            infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
            // 指定日（年）
            infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
            // 指定日（月）
            infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
            // 指定日（日）
            infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

            // 氏名（姓）=API定義の処理「3.1」の氏名（姓）
            infoInDto.setName1Knj(ReportUtil.nullToEmpty(userPair.getKey()));
            // 氏名（名）=API定義の処理「3.1」の氏名（名）
            infoInDto.setName2Knj(ReportUtil.nullToEmpty(userPair.getValue()));

            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ
            Boolean colorFlg = false;
            if (ReportConstants.COLOR_FLG_TRUE.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }
            // g1_メモ
            infoInDto.setG1MemoKnj(ReportUtil.nullToEmpty(entity.getG1MemoKnj()));
            // g1_メモフォント
            infoInDto.setG1MemoFont(getMemoFont(entity.getG1MemoFont()));
            // g1_メモ色
            infoInDto.setG1MemoColor(getMemoColor(entity.getG1MemoColor(), colorFlg));

            // IADL_食事の用意_実施
            infoInDto.setG1AA(ReportUtil.nullToEmpty(entity.getG1AA()));
            // IADL_家事一般_実施
            infoInDto.setG1BA(ReportUtil.nullToEmpty(entity.getG1BA()));
            // IADL_金銭管理_実施
            infoInDto.setG1CA(ReportUtil.nullToEmpty(entity.getG1CA()));
            // IADL_薬の管理_実施
            infoInDto.setG1DA(ReportUtil.nullToEmpty(entity.getG1DA()));
            // IADL_電話の利用_実施
            infoInDto.setG1EA(ReportUtil.nullToEmpty(entity.getG1EA()));
            // IADL_階段_実施
            infoInDto.setG1FA(ReportUtil.nullToEmpty(entity.getG1FA()));
            // IADL_買い物_実施
            infoInDto.setG1GA(ReportUtil.nullToEmpty(entity.getG1GA()));
            // IADL_外出_実施
            infoInDto.setG1HA(ReportUtil.nullToEmpty(entity.getG1HA()));
            // IADL_食事の用意_能力
            infoInDto.setG1AB(ReportUtil.nullToEmpty(entity.getG1AB()));
            // IADL_家事一般_能力
            infoInDto.setG1BB(ReportUtil.nullToEmpty(entity.getG1BB()));
            // IADL_金銭管理_能力
            infoInDto.setG1CB(ReportUtil.nullToEmpty(entity.getG1CB()));
            // IADL_薬の管理_能力
            infoInDto.setG1DB(ReportUtil.nullToEmpty(entity.getG1DB()));
            // IADL_電話の利用_能力
            infoInDto.setG1EB(ReportUtil.nullToEmpty(entity.getG1EB()));
            // IADL_階段_能力
            infoInDto.setG1FB(ReportUtil.nullToEmpty(entity.getG1FB()));
            // IADL_買い物_能力
            infoInDto.setG1GB(ReportUtil.nullToEmpty(entity.getG1GB()));
            // IADL_外出_能力
            infoInDto.setG1HB(ReportUtil.nullToEmpty(entity.getG1HB()));

            // g2_メモ
            infoInDto.setG2MemoKnj(ReportUtil.nullToEmpty(entity.getG2MemoKnj()));
            // g2_メモフォント
            infoInDto.setG2MemoFont(getMemoFont(entity.getG2MemoFont()));
            // g2_メモ色
            infoInDto.setG2MemoColor(getMemoColor(entity.getG2MemoColor(), colorFlg));

            // ADL_入浴
            infoInDto.setG2A(ReportUtil.nullToEmpty(entity.getG2A()));
            // ADL_個人衛生
            infoInDto.setG2B(ReportUtil.nullToEmpty(entity.getG2B()));
            // ADL_上半身の更衣
            infoInDto.setG2C(ReportUtil.nullToEmpty(entity.getG2C()));
            // ADL_下半身の更衣
            infoInDto.setG2D(ReportUtil.nullToEmpty(entity.getG2D()));
            // ADL_歩行
            infoInDto.setG2E(ReportUtil.nullToEmpty(entity.getG2E()));
            // ADL_移動
            infoInDto.setG2F(ReportUtil.nullToEmpty(entity.getG2F()));
            // ADL_トイレへの移乗
            infoInDto.setG2G(ReportUtil.nullToEmpty(entity.getG2G()));
            // ADL_トイレの使用
            infoInDto.setG2H(ReportUtil.nullToEmpty(entity.getG2H()));
            // ADL_ベッド上の可動性
            infoInDto.setG2I(ReportUtil.nullToEmpty(entity.getG2I()));
            // ADL_食事
            infoInDto.setG2J(ReportUtil.nullToEmpty(entity.getG2J()));

            // g3_a_メモ
            infoInDto.setG3AMemoKnj(ReportUtil.nullToEmpty(entity.getG3AMemoKnj()));
            // g3_a_メモフォント
            infoInDto.setG3AMemoFont(getMemoFont(entity.getG3AMemoFont()));
            // g3_a_メモ色
            infoInDto.setG3AMemoColor(getMemoColor(entity.getG3AMemoColor(), colorFlg));

            // 主な室内移動手段
            infoInDto.setG3A(ReportUtil.nullToEmpty(entity.getG3A()));

            // g3_b_メモ
            infoInDto.setG3BMemoKnj(ReportUtil.nullToEmpty(entity.getG3BMemoKnj()));
            // g3_b_メモフォント
            infoInDto.setG3BMemoFont(getMemoFont(entity.getG3BMemoFont()));
            // g3_b_メモ色
            infoInDto.setG3BMemoColor(getMemoColor(entity.getG3BMemoColor(), colorFlg));

            // 4メートルの歩行時間
            infoInDto.setG3B(ReportUtil.nullToEmpty(entity.getG3B()));

            // g3_c_メモ
            infoInDto.setG3CMemoKnj(ReportUtil.nullToEmpty(entity.getG3CMemoKnj()));
            // g3_c_メモフォント
            infoInDto.setG3CMemoFont(getMemoFont(entity.getG3CMemoFont()));
            // g3_c_メモ色
            infoInDto.setG3CMemoColor(getMemoColor(entity.getG3CMemoColor(), colorFlg));

            // 歩行距離
            infoInDto.setG3C(ReportUtil.nullToEmpty(entity.getG3C()));

            // g3_d_メモ
            infoInDto.setG3DMemoKnj(ReportUtil.nullToEmpty(entity.getG3DMemoKnj()));
            // g3_d_メモフォント
            infoInDto.setG3DMemoFont(getMemoFont(entity.getG3DMemoFont()));
            // g3_d_メモ色
            infoInDto.setG3DMemoColor(getMemoColor(entity.getG3DMemoColor(), colorFlg));

            // 車いす自操距離
            infoInDto.setG3D(ReportUtil.nullToEmpty(entity.getG3D()));

            // g4_a_メモ
            infoInDto.setG4AMemoKnj(ReportUtil.nullToEmpty(entity.getG4AMemoKnj()));
            // g4_a_メモフォント
            infoInDto.setG4AMemoFont(getMemoFont(entity.getG4AMemoFont()));
            // g4_a_メモ色
            infoInDto.setG4AMemoColor(getMemoColor(entity.getG4AMemoColor(), colorFlg));

            // 活動状況_体を動かした時間
            infoInDto.setG4A(ReportUtil.nullToEmpty(entity.getG4A()));

            // g4_b_メモ
            infoInDto.setG4BMemoKnj(ReportUtil.nullToEmpty(entity.getG4BMemoKnj()));
            // g4_b_メモフォント
            infoInDto.setG4BMemoFont(getMemoFont(entity.getG4BMemoFont()));
            // g4_b_メモ色
            infoInDto.setG4BMemoColor(getMemoColor(entity.getG4BMemoColor(), colorFlg));

            // 活動状況_外に出た日数
            infoInDto.setG4B(ReportUtil.nullToEmpty(entity.getG4B()));

            // g5_メモ
            infoInDto.setG5MemoKnj(ReportUtil.nullToEmpty(entity.getG5MemoKnj()));
            // g5_メモフォント
            infoInDto.setG5MemoFont(getMemoFont(entity.getG5MemoFont()));
            // g5_メモ色
            infoInDto.setG5MemoColor(getMemoColor(entity.getG5MemoColor(), colorFlg));

            // 身体機能_本人
            infoInDto.setG5A(ReportUtil.nullToEmpty(entity.getG5A()));
            // 身体機能_ケアスタッフ
            infoInDto.setG5B(ReportUtil.nullToEmpty(entity.getG5B()));

            // g6_メモ
            infoInDto.setG6MemoKnj(ReportUtil.nullToEmpty(entity.getG5MemoKnj()));
            // g6_メモフォント
            infoInDto.setG6MemoFont(getMemoFont(entity.getG6MemoFont()));
            // g6_メモ色
            infoInDto.setG6MemoColor(getMemoColor(entity.getG6MemoColor(), colorFlg));

            // ADLの変化
            infoInDto.setG6(ReportUtil.nullToEmpty(entity.getG6()));

            // g7_メモ
            infoInDto.setG7MemoKnj(ReportUtil.nullToEmpty(entity.getG7MemoKnj()));
            // g7_メモフォント
            infoInDto.setG7MemoFont(getMemoFont(entity.getG7MemoFont()));
            // g7_メモ色
            infoInDto.setG7MemoColor(getMemoColor(entity.getG7MemoColor(), colorFlg));

            // 身体機能_本人
            infoInDto.setG7A(ReportUtil.nullToEmpty(entity.getG7A()));
            // 身体機能_ケアスタッフ
            infoInDto.setG7B(ReportUtil.nullToEmpty(entity.getG7B()));

            // 文書番号情報の取得
            String sysCode = syscd;
            String shokuId = printSubjectHistoryList.get(0).getChoPrtList().get(0).getShokuId();
            String sectionKnj = printSubjectHistoryList.get(0).getChoPrtList().get(0).getSection();
            String bunsyoKanriNo = this.getBunsyoKanriNo(sysCode, shokuId, sectionKnj, svJigyoId);

            infoInDto.setBunsyoKanriNo(bunsyoKanriNo);

        }

        return infoInDto;
    }

    /**
     * U06080_アセスメント表（Ｈ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetHReportServiceInDto getHReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        AssessmentSheetHReportServiceInDto infoInDto = new AssessmentSheetHReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、アセスメント表（F）情報の取得。===============
         * 
         */
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            infoInDto = this.getHDefReportParams(model, outDto);
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.TRUE);
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getHReportParamsFromAssH(model, outDto);
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.FALSE);
        }

        // 事業所名
        infoInDto.setJigyousha(model.getSvJigyoKnj());
        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｈ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetHReportServiceInDto getHDefReportParams(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetHReportServiceInDto infoInDto = new AssessmentSheetHReportServiceInDto();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        // リクエストパラメータ.記入用シートを印刷するフラグ = true の場合
        String wkAssType = printOption.getKinyuAssType();

        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(wkAssType)));

        // 調査アセスメント種別
        infoInDto.setSyubetu(CommonDtoUtil.strValToInt(wkAssType));

        // 指定日（年号）
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // h1_メモ
        infoInDto.setH1MemoKnj(CommonConstants.BLANK_STRING);
        // h1_メモフォント
        infoInDto.setH1MemoFont(CommonConstants.STR_12);
        // h1_メモ色
        infoInDto.setH1MemoColor(CommonConstants.BLANK_STRING);
        // 尿失禁
        infoInDto.setH1(CommonConstants.BLANK_STRING);
        // h2_メモ
        infoInDto.setH2MemoKnj(CommonConstants.BLANK_STRING);
        // h2_メモフォント
        infoInDto.setH2MemoFont(CommonConstants.STR_12);
        // h2_メモ色
        infoInDto.setH2MemoColor(CommonConstants.BLANK_STRING);
        // 尿失禁器材
        infoInDto.setH2(CommonConstants.BLANK_STRING);
        // h3_メモ
        infoInDto.setH3MemoKnj(CommonConstants.BLANK_STRING);
        // h3_メモフォント
        infoInDto.setH3MemoFont(CommonConstants.STR_12);
        // h3_メモ色
        infoInDto.setH3MemoColor(CommonConstants.BLANK_STRING);
        // 便失禁
        infoInDto.setH3(CommonConstants.BLANK_STRING);
        // h4_メモ
        infoInDto.setH4MemoKnj(CommonConstants.BLANK_STRING);
        // h4_メモフォント
        infoInDto.setH4MemoFont(CommonConstants.STR_12);
        // h4_メモ色
        infoInDto.setH4MemoColor(CommonConstants.BLANK_STRING);
        // オムツやパッドの使用
        infoInDto.setH4(CommonConstants.BLANK_STRING);
        // h5_メモ
        infoInDto.setH5MemoKnj(CommonConstants.BLANK_STRING);
        // h5_メモフォント
        infoInDto.setH5MemoFont(CommonConstants.STR_12);
        // h5_メモ色
        infoInDto.setH5MemoColor(CommonConstants.BLANK_STRING);
        // ストーマ
        infoInDto.setH5(CommonConstants.BLANK_STRING);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;

    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｈ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetHReportServiceInDto getHReportParamsFromAssH(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetHReportServiceInDto infoInDto = new AssessmentSheetHReportServiceInDto();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.システムコード
        String syscd = model.getSyscd();
        // 【リクエストパラメータ】.事業者ID
        String svJigyoId = model.getJigyoInfo().getSvJigyoId();

        // 【リクエストパラメータ】.印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();

        // 2.リクエストパラメータ.記入用シートを印刷するフラグ = false の場合、アセスメント表（Ｇ）情報を取得
        KghCpnRaiAssHPByCriteriaInEntity kghCpnRaiAssHPByCriteriaInEntity = new KghCpnRaiAssHPByCriteriaInEntity();
        // アセスメントID
        kghCpnRaiAssHPByCriteriaInEntity
                .setAnRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
        // DAOを実行
        List<KghCpnRaiAssHPOutEntity> KghCpnRaiAssHPOutEntitise = this.raiRrkKghCpnRaiAssHSelectMapper
                .findKghCpnRaiAssHPByCriteria(kghCpnRaiAssHPByCriteriaInEntity);
        // 2.2. 「2.1」で取得したアセスメント表（Ｇ）情報の件数をワークアセスメント表（Ｇ）情報.総件数に設定
        if (CollectionUtils.isNotEmpty(KghCpnRaiAssHPOutEntitise)) {
            KghCpnRaiAssHPOutEntity entity = KghCpnRaiAssHPOutEntitise.get(0);
            /*
             * ===============3. 利用者基本情報を取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());

            /*
             * ===============4.アセスメント種別の取得===============
             * 
             */
            Pair<Integer, String> syubetuPair = this
                    .getSyubetuByRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
            // 調査アセスメント種別名
            infoInDto.setSyubetuKnj(syubetuPair.getRight());
            // 調査アセスメント種別
            infoInDto.setSyubetu(syubetuPair.getLeft());

            /*
             * =============== 5.帳票用データを設定===============
             * 
             */

            // 指定日（年号）
            ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                    printSet.getShiTeiDate(), model.getSystemDate());
            infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
            // 指定日（年）
            infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
            // 指定日（月）
            infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
            // 指定日（日）
            infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

            // 氏名（姓）=API定義の処理「3.1」の氏名（姓）
            infoInDto.setName1Knj(ReportUtil.nullToEmpty(userPair.getKey()));
            // 氏名（名）=API定義の処理「3.1」の氏名（名）
            infoInDto.setName2Knj(ReportUtil.nullToEmpty(userPair.getValue()));

            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ
            Boolean colorFlg = false;
            if (ReportConstants.COLOR_FLG_TRUE.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }
            // h1_メモ
            infoInDto.setH1MemoKnj(ReportUtil.nullToEmpty(entity.getH1MemoKnj()));
            // h1_メモフォント
            infoInDto.setH1MemoFont(getMemoFont(entity.getH1MemoFont()));
            // h1_メモ色
            infoInDto.setH1MemoColor(getMemoColor(entity.getH1MemoColor(), colorFlg));

            // 尿失禁
            infoInDto.setH1(ReportUtil.nullToEmpty(entity.getH1()));

            // h2_メモ
            infoInDto.setH2MemoKnj(ReportUtil.nullToEmpty(entity.getH2MemoKnj()));
            // h2_メモフォント
            infoInDto.setH2MemoFont(getMemoFont(entity.getH2MemoFont()));
            // h2_メモ色
            infoInDto.setH2MemoColor(getMemoColor(entity.getH2MemoColor(), colorFlg));

            // 尿失禁器材
            infoInDto.setH2(ReportUtil.nullToEmpty(entity.getH2()));

            // h3メモ
            infoInDto.setH3MemoKnj(ReportUtil.nullToEmpty(entity.getH3MemoKnj()));
            // h3_メモフォント
            infoInDto.setH3MemoFont(getMemoFont(entity.getH3MemoFont()));
            // h3_メモ色
            infoInDto.setH3MemoColor(getMemoColor(entity.getH3MemoColor(), colorFlg));

            // 便失禁
            infoInDto.setH3(ReportUtil.nullToEmpty(entity.getH3()));

            // h4メモ
            infoInDto.setH4MemoKnj(ReportUtil.nullToEmpty(entity.getH4MemoKnj()));
            // h4_メモフォント
            infoInDto.setH4MemoFont(getMemoFont(entity.getH4MemoFont()));
            // h4_メモ色
            infoInDto.setH4MemoColor(getMemoColor(entity.getH4MemoColor(), colorFlg));

            // オムツやパッドの使用
            infoInDto.setH4(ReportUtil.nullToEmpty(entity.getH4()));

            // h5メモ
            infoInDto.setH5MemoKnj(ReportUtil.nullToEmpty(entity.getH5MemoKnj()));
            // h5_メモフォント
            infoInDto.setH5MemoFont(getMemoFont(entity.getH5MemoFont()));
            // h5_メモ色
            infoInDto.setH5MemoColor(getMemoColor(entity.getH5MemoColor(), colorFlg));

            // ストーマ
            infoInDto.setH5(ReportUtil.nullToEmpty(entity.getH5()));

            // 文書番号情報の取得
            String sysCode = syscd;
            String shokuId = printSubjectHistoryList.get(0).getChoPrtList().get(0).getShokuId();
            String sectionKnj = printSubjectHistoryList.get(0).getChoPrtList().get(0).getSection();
            String bunsyoKanriNo = this.getBunsyoKanriNo(sysCode, shokuId, sectionKnj, svJigyoId);

            infoInDto.setBunsyoKanriNo(bunsyoKanriNo);

        }

        return infoInDto;
    }

    /**
     * U06080_アセスメント表（Ｉ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetIReportServiceInDto getIReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {

        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        // infoInDtoを設定する。
        AssessmentSheetIReportServiceInDto infoInDto = new AssessmentSheetIReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        // 帳票タイトル "インターライ方式ケアアセスメント表"
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 指定日印刷区分 リクエストパラメータ.データ.印刷オプション.指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // 事業者名 リクエストパラメータ.データ.事業者名
        infoInDto.setJigyousha(ReportUtil.nullToEmpty(model.getSvJigyoKnj()));

        /*
         * ===============2. リクエストパラメータ.記入用シートを印刷するフラグ = true の場合、
         * 結果レスポンスを返却する。===============
         * 
         */
        Boolean isEmptyFlg = false;
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            isEmptyFlg = true;
        }
        // 記入用シートを印刷するフラグ リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(isEmptyFlg);
        // リクエストパラメータ.記入用シートを印刷するフラグ = true の場合
        if (isEmptyFlg) {

            infoInDto = this.getIDefReportParams(model, outDto, infoInDto);

            /*
             * ===============リクエストパラメータ.記入用シートを印刷するフラグ = false の場合、
             * アセスメント表（Ｉ）情報の取得。===============
             * 
             */
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getIReportParamsFromAssI(model, outDto, infoInDto);
        }
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｉ）帳票パラメータ取得
     * 
     * @param model     入力データ
     * @param outDto    出力データ
     * @param infoInDto PDF帳票出力詳細データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetIReportServiceInDto getIDefReportParams(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto, AssessmentSheetIReportServiceInDto infoInDto) throws Exception {
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);
        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType())));

        // 指定日（年号） 全角フレーム
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年） 全角フレーム
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月） 全角フレーム
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日） 全角フレーム
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）=""
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）=""
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // i1_メモ=""
        infoInDto.setI1MemoKnj(CommonConstants.BLANK_STRING);
        // i1_メモフォント="12"
        infoInDto.setI1MemoFont(CommonConstants.STR_12);
        // i1_メモ色=""
        infoInDto.setI1MemoColor(CommonConstants.BLANK_STRING);
        // 疾患_筋骨系_大腿骨骨折=""
        infoInDto.setI1A(CommonConstants.BLANK_STRING);
        // 疾患_筋骨系_その他の骨折=""
        infoInDto.setI1B(CommonConstants.BLANK_STRING);
        // 疾患_神経系_アルツハイマー病=""
        infoInDto.setI1C(CommonConstants.BLANK_STRING);
        // 疾患_神経系_認知症=""
        infoInDto.setI1D(CommonConstants.BLANK_STRING);
        // 疾患_神経系_片麻痺=""
        infoInDto.setI1E(CommonConstants.BLANK_STRING);
        // 疾患_神経系_多発性硬化症=""
        infoInDto.setI1F(CommonConstants.BLANK_STRING);
        // 疾患_神経系_対麻痺=""
        infoInDto.setI1G(CommonConstants.BLANK_STRING);
        // 疾患_神経系_パーキンソン病=""
        infoInDto.setI1H(CommonConstants.BLANK_STRING);
        // 疾患_神経系_四肢麻痺=""
        infoInDto.setI1I(CommonConstants.BLANK_STRING);
        // 疾患_神経系_脳卒中/脳血管障害=""
        infoInDto.setI1J(CommonConstants.BLANK_STRING);
        // 疾患_心肺系_CHD=""
        infoInDto.setI1K(CommonConstants.BLANK_STRING);
        // 疾患_心肺系_COPD=""
        infoInDto.setI1L(CommonConstants.BLANK_STRING);
        // 疾患_心肺系_CHF=""
        infoInDto.setI1M(CommonConstants.BLANK_STRING);
        // 疾患_心肺系_高血圧症=""
        infoInDto.setI1N(CommonConstants.BLANK_STRING);
        // 疾患_精神_不安症=""
        infoInDto.setI1O(CommonConstants.BLANK_STRING);
        // 疾患_精神_双極性障害=""
        infoInDto.setI1P(CommonConstants.BLANK_STRING);
        // 疾患_精神_うつ=""
        infoInDto.setI1Q(CommonConstants.BLANK_STRING);
        // 疾患_精神_統合失調症=""
        infoInDto.setI1R(CommonConstants.BLANK_STRING);
        // 疾患_感染症_肺炎=""
        infoInDto.setI1S(CommonConstants.BLANK_STRING);
        // 疾患_感染症_UTI=""
        infoInDto.setI1T(CommonConstants.BLANK_STRING);
        // 疾患_その他_がん=""
        infoInDto.setI1U(CommonConstants.BLANK_STRING);
        // 疾患_その他_糖尿病=""
        infoInDto.setI1V(CommonConstants.BLANK_STRING);
        // i2_メモ=""
        infoInDto.setI2MemoKnj(CommonConstants.BLANK_STRING);
        // i2_メモフォント="12"
        infoInDto.setI2MemoFont(CommonConstants.STR_12);
        // i2_メモ色=""
        infoInDto.setI2MemoColor(CommonConstants.BLANK_STRING);

        // 疾患（診断）情報リスト
        // 30行の空データを追加する
        List<U06080DiagnosisInfo> diagnosisInfoList = new ArrayList<U06080DiagnosisInfo>();
        List<U06080DiagnosisInfo> emptydiagnosisInfoList = Collections.nCopies(30, new U06080DiagnosisInfo());
        diagnosisInfoList.addAll(emptydiagnosisInfoList);
        JRBeanCollectionDataSource wkdiagnosisInfoList = new JRBeanCollectionDataSource(diagnosisInfoList);
        infoInDto.setDiagnosisInfoList(wkdiagnosisInfoList);

        return infoInDto;

    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｉ）帳票パラメータ取得
     * 
     * @param model     入力データ
     * @param outDto    出力データ
     * @param infoInDto PDF帳票出力詳細データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetIReportServiceInDto getIReportParamsFromAssI(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto, AssessmentSheetIReportServiceInDto infoInDto) throws Exception {
        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();

        // アセスメント表（Ｉ）情報の取得。
        // アセスメント表(I)データ情報取得のINPUT情報初期化
        KghCpnRaiAssIDataByCriteriaInEntity kghCpnRaiAssIDataByCriteriaInEntity = new KghCpnRaiAssIDataByCriteriaInEntity();
        // アセスメントID←リクエストパラメータ.データ.印刷対象履歴リスト[0].アセスメントID
        kghCpnRaiAssIDataByCriteriaInEntity
                .setAnRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
        // 2.1. 下記のアセスメント＿I疾患情報取得のDAOを利用し、アセスメント表(I)データ情報を取得し、疾患（診断）情報リストを作成する。
        List<KghCpnRaiAssIDataOutEntity> kghCpnRaiAssIDataOutEntityList = kghCpnRaiAssIDataSelectMapper
                .findKghCpnRaiAssIDataByCriteria(kghCpnRaiAssIDataByCriteriaInEntity);
        // 取得したアセスメント表(I)データ情報から疾患（診断）情報リストの作成について、
        // レコード数 = (int) Math.ceil((double) 取得した件数 / 30) * 30
        // に作成する、レコード数を空のデータで追加して、補足する。
        if (CollectionUtils.isNotEmpty(kghCpnRaiAssIDataOutEntityList)) {
            int recordCount = (int) Math.ceil((double) kghCpnRaiAssIDataOutEntityList.size() / 30) * 30;
            // 疾患（診断）情報リスト
            List<U06080DiagnosisInfo> diagnosisInfoList = new ArrayList<U06080DiagnosisInfo>();

            for (int i = 0; i < kghCpnRaiAssIDataOutEntityList.size(); i++) {

                U06080DiagnosisInfo U06080DiagnosisInfo = new U06080DiagnosisInfo();
                // API定義の処理「2.1.」疾患（診断）情報リストの診断名
                U06080DiagnosisInfo
                        .setShindanKnj(ReportUtil.nullToEmpty(kghCpnRaiAssIDataOutEntityList.get(i).getShindanKnj()));
                // API定義の処理「2.1.」疾患（診断）情報リストの疾患コード
                U06080DiagnosisInfo
                        .setShikkanCd(ReportUtil.nullToEmpty(kghCpnRaiAssIDataOutEntityList.get(i).getShikkanCd()));
                // API定義の処理「2.1.」疾患（診断）情報リストのICD-CMコード1
                U06080DiagnosisInfo
                        .setIcdCmCode1(ReportUtil.nullToEmpty(kghCpnRaiAssIDataOutEntityList.get(i).getIcdCmCode1()));
                // API定義の処理「2.1.」疾患（診断）情報リストのICD-CMコード2
                U06080DiagnosisInfo
                        .setIcdCmCode2(ReportUtil.nullToEmpty(kghCpnRaiAssIDataOutEntityList.get(i).getIcdCmCode2()));
                diagnosisInfoList.add(U06080DiagnosisInfo);
            }
            while (diagnosisInfoList.size() < recordCount) {
                diagnosisInfoList.add(new U06080DiagnosisInfo());
            }
            JRBeanCollectionDataSource wkdiagnosisInfoList = new JRBeanCollectionDataSource(diagnosisInfoList);
            infoInDto.setDiagnosisInfoList(wkdiagnosisInfoList);
        } else {
            // 疾患（診断）情報リスト
            // 30行の空データを追加する
            List<U06080DiagnosisInfo> diagnosisInfoList = new ArrayList<U06080DiagnosisInfo>();
            // レコード数 = (int) Math.ceil((double) 取得した件数 / 30) * 30
            // に作成する、レコード数を空のデータで追加して、補足する。
            List<U06080DiagnosisInfo> emptydiagnosisInfoList = Collections.nCopies(30, new U06080DiagnosisInfo());
            diagnosisInfoList.addAll(emptydiagnosisInfoList);
            JRBeanCollectionDataSource wkdiagnosisInfoList = new JRBeanCollectionDataSource(diagnosisInfoList);
            infoInDto.setDiagnosisInfoList(wkdiagnosisInfoList);
        }

        // アセスメント表（Ｉ）情報取得のINPUT情報初期化
        KghCpnRaiAssIPByCriteriaInEntity kghCpnRaiAssIPByCriteriaInEntity = new KghCpnRaiAssIPByCriteriaInEntity();
        // アセスメントID←リクエストパラメータ.データ.印刷対象履歴リスト[0].アセスメントID
        kghCpnRaiAssIPByCriteriaInEntity
                .setAnRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
        // 2.2. 下記のアセスメント表（Ｉ）情報取得のDAOを利用し、アセスメント表（Ｉ）情報を取得する。
        List<KghCpnRaiAssIPOutEntity> kghCpnRaiAssIPOutEntityList = raiRrkKghCpnRaiAssISelectMapper
                .findKghCpnRaiAssIPByCriteria(kghCpnRaiAssIPByCriteriaInEntity);

        /*
         * ===============「2.2.」で取得したアセスメント表（Ｉ）情報の件数.総件数 > 0 件の場合、
         * 利用者基本（１－６）情報、アセスメント種別を取得する。===============
         * 
         */

        // 「2.2.」で取得したアセスメント表（Ｉ）情報の件数.総件数 > 0 件の場合，
        if (CollectionUtils.isNotEmpty(kghCpnRaiAssIPOutEntityList)) {
            // ワークアセスメント表（Ｉ）情報の一件目を取得する
            KghCpnRaiAssIPOutEntity entity = kghCpnRaiAssIPOutEntityList.get(0);

            /*
             * ===============3.1. 利用者基本（１－６）情報の取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());
            // 氏名（姓）
            infoInDto.setName1Knj(ReportUtil.nullToEmpty(userPair.getLeft()));
            // 氏名（名）
            infoInDto.setName2Knj(ReportUtil.nullToEmpty(userPair.getRight()));

            /*
             * ===============3.2. アセスメント種別情報を取得する===============
             * 
             */
            Pair<Integer, String> syubetuPair = this
                    .getSyubetuByRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
            // 調査アセスメント種別名
            infoInDto.setSyubetuKnj(syubetuPair.getRight());

            /*
             * ===============4.上記編集した帳票用データを設定する。===============
             * 
             */
            // i1_メモ
            infoInDto.setI1MemoKnj(ReportUtil.nullToEmpty(entity.getI1MemoKnj()));
            // i1_メモフォント
            infoInDto.setI1MemoFont(getMemoFont(entity.getI1MemoFont()));

            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            Boolean colorFlg = false;
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg()) ||
                    CommonConstants.STR_1.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }
            // i1_メモ色
            infoInDto.setI1MemoColor(getMemoColor(entity.getI1MemoColor(), colorFlg));
            // 疾患_筋骨系_大腿骨骨折
            infoInDto.setI1A(ReportUtil.nullToEmpty(entity.getI1A()));
            // 疾患_筋骨系_その他の骨折
            infoInDto.setI1B(ReportUtil.nullToEmpty(entity.getI1B()));
            // 疾患_神経系_アルツハイマー病
            infoInDto.setI1C(ReportUtil.nullToEmpty(entity.getI1C()));
            // 疾患_神経系_認知症
            infoInDto.setI1D(ReportUtil.nullToEmpty(entity.getI1D()));
            // 疾患_神経系_片麻痺
            infoInDto.setI1E(ReportUtil.nullToEmpty(entity.getI1E()));
            // 疾患_神経系_多発性硬化症
            infoInDto.setI1F(ReportUtil.nullToEmpty(entity.getI1F()));
            // 疾患_神経系_対麻痺
            infoInDto.setI1G(ReportUtil.nullToEmpty(entity.getI1G()));
            // 疾患_神経系_パーキンソン病
            infoInDto.setI1H(ReportUtil.nullToEmpty(entity.getI1H()));
            // 疾患_神経系_四肢麻痺
            infoInDto.setI1I(ReportUtil.nullToEmpty(entity.getI1I()));
            // 疾患_神経系_脳卒中/脳血管障害
            infoInDto.setI1J(ReportUtil.nullToEmpty(entity.getI1J()));
            // 疾患_心肺系_CHD
            infoInDto.setI1K(ReportUtil.nullToEmpty(entity.getI1K()));
            // 疾患_心肺系_COPD
            infoInDto.setI1L(ReportUtil.nullToEmpty(entity.getI1L()));
            // 疾患_心肺系_CHF
            infoInDto.setI1M(ReportUtil.nullToEmpty(entity.getI1M()));
            // 疾患_心肺系_高血圧症
            infoInDto.setI1N(ReportUtil.nullToEmpty(entity.getI1N()));
            // 疾患_精神_不安症
            infoInDto.setI1O(ReportUtil.nullToEmpty(entity.getI1O()));
            // 疾患_精神_双極性障害
            infoInDto.setI1P(ReportUtil.nullToEmpty(entity.getI1P()));
            // 疾患_精神_うつ
            infoInDto.setI1Q(ReportUtil.nullToEmpty(entity.getI1Q()));
            // 疾患_精神_統合失調症
            infoInDto.setI1R(ReportUtil.nullToEmpty(entity.getI1R()));
            // 疾患_感染症_肺炎
            infoInDto.setI1S(ReportUtil.nullToEmpty(entity.getI1S()));
            // 疾患_感染症_UTI
            infoInDto.setI1T(ReportUtil.nullToEmpty(entity.getI1T()));
            // 疾患_その他_がん
            infoInDto.setI1U(ReportUtil.nullToEmpty(entity.getI1U()));
            // 疾患_その他_糖尿病
            infoInDto.setI1V(ReportUtil.nullToEmpty(entity.getI1V()));
            // i2_メモ
            infoInDto.setI2MemoKnj(ReportUtil.nullToEmpty(entity.getI2MemoKnj()));
            // i2_メモフォント
            infoInDto.setI2MemoFont(getMemoFont(entity.getI2MemoFont()));
            // i2_メモ色
            infoInDto.setI2MemoColor(getMemoColor(entity.getI2MemoColor(), colorFlg));
        }

        // 指定日（年号）
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
        // 指定日（年）
        infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
        // 指定日（月）
        infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
        // 指定日（日）
        infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

        // 2.文書番号情報の取得
        /*
         * ===============共通関数補足 2.文書番号情報取得処理===============
         * 
         */
        // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01 関数名：GetF3GkProfile
        // を利用し、設定の読込（システム環境以外の設定）情報を取得する。
        // 【リクエストパラメータ】.システムコード
        String syscd = model.getSyscd();
        // 職員ID←リクエストパラメータ.データ.印刷対象履歴リスト[0].出力帳票印刷情報リスト[0].職員ID
        String shokuId = printSubjectHistoryList.get(0).getChoPrtList().get(0).getShokuId();
        // セクション←リクエストパラメータ.データ.出力帳票印刷情報リスト[0].セクション
        String section = printSubjectHistoryList.get(0).getChoPrtList().get(0).getSection();
        // サービス事業者ID←リクエストパラメータ.事業者情報.事業者ID
        String svJigyoId = model.getJigyoInfo().getSvJigyoId();

        String bunsyoKanriNo = this.getBunsyoKanriNo(syscd, shokuId, section, svJigyoId);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(ReportUtil.nullToEmpty(bunsyoKanriNo));

        return infoInDto;

    }

    /**
     * U06080_アセスメント表（Ｊ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    public AssessmentSheetJReportServiceInDto getJReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        AssessmentSheetJReportServiceInDto infoInDto = new AssessmentSheetJReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、アセスメント表（Ｊ）情報の取得。===============
         * 
         */
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            infoInDto = this.getJDefReportParams(model, outDto);
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.TRUE);
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getJReportParamsFromAssJ(model, outDto);
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.FALSE);
        }
        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(Integer.valueOf(model.getPrintSet().getShiTeiKubun()));
        // 事業者名
        infoInDto.setJigyoKnj(model.getSvJigyoKnj());
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｊ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetJReportServiceInDto getJDefReportParams(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetJReportServiceInDto infoInDto = new AssessmentSheetJReportServiceInDto();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // アセスメント種別
        infoInDto.setSyubetuKnj(getSyubetuByAssType(
                CommonDtoUtil.strValToInt(
                        CommonDtoUtil.strNullToEmpty(printOption.getKinyuAssType()).isEmpty() ? CommonConstants.STR_ZERO
                                : printOption.getKinyuAssType())));
        // 指定日（年号）
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // j1_メモ
        infoInDto.setJ1MemoKnj(CommonConstants.BLANK_STRING);
        // j1_メモフォント
        infoInDto.setJ1MemoFont(CommonConstants.STR_12);
        // j1_メモ色
        infoInDto.setJ1MemoColor(CommonConstants.BLANK_STRING);
        // 転倒
        infoInDto.setJ1(CommonConstants.BLANK_STRING);
        // j2_メモ
        infoInDto.setJ2MemoKnj(CommonConstants.BLANK_STRING);
        // j2_メモフォント
        infoInDto.setJ2MemoFont(CommonConstants.STR_12);
        // j2_メモ色
        infoInDto.setJ2MemoColor(CommonConstants.BLANK_STRING);
        // 最近の転倒
        infoInDto.setJ2(CommonConstants.BLANK_STRING);
        // j3_メモ
        infoInDto.setJ3MemoKnj(CommonConstants.BLANK_STRING);
        // j3_メモフォント
        infoInDto.setJ3MemoFont(CommonConstants.STR_12);
        // j3_メモ色
        infoInDto.setJ3MemoColor(CommonConstants.BLANK_STRING);
        // 問題頻度_バランス_立位
        infoInDto.setJ3A(CommonConstants.BLANK_STRING);
        // 問題頻度_バランス_方向転換
        infoInDto.setJ3B(CommonConstants.BLANK_STRING);
        // 問題頻度_バランス_めまい
        infoInDto.setJ3C(CommonConstants.BLANK_STRING);
        // 問題頻度_バランス_歩行
        infoInDto.setJ3D(CommonConstants.BLANK_STRING);
        // 問題頻度_心肺_胸痛
        infoInDto.setJ3E(CommonConstants.BLANK_STRING);
        // 問題頻度_心肺_気道
        infoInDto.setJ3F(CommonConstants.BLANK_STRING);
        // 問題頻度_精神_思考
        infoInDto.setJ3G(CommonConstants.BLANK_STRING);
        // 問題頻度_精神_妄想
        infoInDto.setJ3H(CommonConstants.BLANK_STRING);
        // 問題頻度_精神_幻覚
        infoInDto.setJ3I(CommonConstants.BLANK_STRING);
        // 問題頻度_神経_失語症
        infoInDto.setJ3J(CommonConstants.BLANK_STRING);
        // 問題頻度_消化器系_胃酸
        infoInDto.setJ3K(CommonConstants.BLANK_STRING);
        // 問題頻度_消化器系_便秘
        infoInDto.setJ3L(CommonConstants.BLANK_STRING);
        // 問題頻度_消化器系_下痢
        infoInDto.setJ3M(CommonConstants.BLANK_STRING);
        // 問題頻度_消化器系_嘔吐
        infoInDto.setJ3N(CommonConstants.BLANK_STRING);
        // 問題頻度_睡眠障害_入眠
        infoInDto.setJ3O(CommonConstants.BLANK_STRING);
        // 問題頻度_睡眠障害_過多
        infoInDto.setJ3P(CommonConstants.BLANK_STRING);
        // 問題頻度_その他_誤嚥
        infoInDto.setJ3Q(CommonConstants.BLANK_STRING);
        // 問題頻度_その他_発熱
        infoInDto.setJ3R(CommonConstants.BLANK_STRING);
        // 問題頻度_その他_出血
        infoInDto.setJ3S(CommonConstants.BLANK_STRING);
        // 問題頻度_その他_不衛生
        infoInDto.setJ3T(CommonConstants.BLANK_STRING);
        // 問題頻度_その他_末梢浮腫
        infoInDto.setJ3U(CommonConstants.BLANK_STRING);
        // j4_メモ
        infoInDto.setJ4MemoKnj(CommonConstants.BLANK_STRING);
        // j4_メモフォント
        infoInDto.setJ4MemoFont(CommonConstants.STR_12);
        // j4_メモ色
        infoInDto.setJ4MemoColor(CommonConstants.BLANK_STRING);
        // 呼吸困難
        infoInDto.setJ4(CommonConstants.BLANK_STRING);
        // j5_メモ
        infoInDto.setJ5MemoKnj(CommonConstants.BLANK_STRING);
        // j5_メモフォント
        infoInDto.setJ5MemoFont(CommonConstants.STR_12);
        // j5_メモ色
        infoInDto.setJ5MemoColor(CommonConstants.BLANK_STRING);
        // 疲労感
        infoInDto.setJ5(CommonConstants.BLANK_STRING);
        // j6_a_メモ
        infoInDto.setJ6AMemoKnj(CommonConstants.BLANK_STRING);
        // j6_a_メモフォント
        infoInDto.setJ6AMemoFont(CommonConstants.STR_12);
        // j6_a_メモ色
        infoInDto.setJ6AMemoColor(CommonConstants.BLANK_STRING);
        // 痛み_頻度
        infoInDto.setJ6A(CommonConstants.BLANK_STRING);
        // j6_b_メモ
        infoInDto.setJ6BMemoKnj(CommonConstants.BLANK_STRING);
        // j6_b_メモフォント
        infoInDto.setJ6BMemoFont(CommonConstants.STR_12);
        // j6_b_メモ色
        infoInDto.setJ6BMemoColor(CommonConstants.BLANK_STRING);
        // 痛み_程度
        infoInDto.setJ6B(CommonConstants.BLANK_STRING);
        // j6_c_メモ
        infoInDto.setJ6CMemoKnj(CommonConstants.BLANK_STRING);
        // j6_c_メモフォント
        infoInDto.setJ6CMemoFont(CommonConstants.STR_12);
        // j6_c_メモ色
        infoInDto.setJ6CMemoColor(CommonConstants.BLANK_STRING);
        // 痛み_持続性
        infoInDto.setJ6C(CommonConstants.BLANK_STRING);
        // j6_d_メモ
        infoInDto.setJ6DMemoKnj(CommonConstants.BLANK_STRING);
        // j6_d_メモフォント
        infoInDto.setJ6DMemoFont(CommonConstants.STR_12);
        // j6_d_メモ色
        infoInDto.setJ6DMemoColor(CommonConstants.BLANK_STRING);
        // 痛み_突発痛
        infoInDto.setJ6D(CommonConstants.BLANK_STRING);
        // j6_e_メモ
        infoInDto.setJ6EMemoKnj(CommonConstants.BLANK_STRING);
        // j6_e_メモフォント
        infoInDto.setJ6EMemoFont(CommonConstants.STR_12);
        // j6_e_メモ色
        infoInDto.setJ6EMemoColor(CommonConstants.BLANK_STRING);
        // 痛み_コントロール
        infoInDto.setJ6E(CommonConstants.BLANK_STRING);
        // j7_メモ
        infoInDto.setJ7MemoKnj(CommonConstants.BLANK_STRING);
        // j7_メモフォント
        infoInDto.setJ7MemoFont(CommonConstants.STR_12);
        // j7_メモ色
        infoInDto.setJ7MemoColor(CommonConstants.BLANK_STRING);
        // 状態の不安定性_症状
        infoInDto.setJ7A(CommonConstants.BLANK_STRING);
        // 状態の不安定性_発性
        infoInDto.setJ7B(CommonConstants.BLANK_STRING);
        // 状態の不安定性_末期の疾患
        infoInDto.setJ7C(CommonConstants.BLANK_STRING);
        // j8_メモ
        infoInDto.setJ8MemoKnj(CommonConstants.BLANK_STRING);
        // j8_メモフォント
        infoInDto.setJ8MemoFont(CommonConstants.STR_12);
        // j8_メモ色
        infoInDto.setJ8MemoColor(CommonConstants.BLANK_STRING);
        // 主観的健康感
        infoInDto.setJ8(CommonConstants.BLANK_STRING);
        // j9_a_メモ
        infoInDto.setJ9AMemoKnj(CommonConstants.BLANK_STRING);
        // j9_a_メモフォント
        infoInDto.setJ9AMemoFont(CommonConstants.STR_12);
        // j9_a_メモ色
        infoInDto.setJ9AMemoColor(CommonConstants.BLANK_STRING);
        // 喫煙と飲酒_喫煙
        infoInDto.setJ9A(CommonConstants.BLANK_STRING);
        // j9_b_メモ
        infoInDto.setJ9BMemoKnj(CommonConstants.BLANK_STRING);
        // j9_b_メモフォント
        infoInDto.setJ9BMemoFont(CommonConstants.STR_12);
        // j9_b_メモ色
        infoInDto.setJ9BMemoColor(CommonConstants.BLANK_STRING);
        // 喫煙と飲酒_飲酒
        infoInDto.setJ9B(CommonConstants.BLANK_STRING);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｊ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetJReportServiceInDto getJReportParamsFromAssJ(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetJReportServiceInDto infoInDto = new AssessmentSheetJReportServiceInDto();

        // 【リクエストパラメータ】.印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 指定日（年号）
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
        // 指定日（年）
        infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
        // 指定日（月）
        infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
        // 指定日（日）
        infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());
        // 2.1. アセスメント表（Ｊ）情報を取得する。
        KghCpnRaiAssJPByCriteriaInEntity kghCpnRaiAssJPByCriteriaInEntity = new KghCpnRaiAssJPByCriteriaInEntity();
        kghCpnRaiAssJPByCriteriaInEntity
                .setAnRaiId(CommonDtoUtil.strValToInt(
                        printSubjectHistoryList.get(0).getRaiId()));
        List<KghCpnRaiAssJPOutEntity> kghCpnRaiAssJPOutEntities = raiRrkKghCpnRaiAssJSelectMapper
                .findKghCpnRaiAssJPByCriteria(kghCpnRaiAssJPByCriteriaInEntity);

        // 2.2.「2.1.」で取得したアセスメント表（Ｊ）情報の件数をワークアセスメント表（Ｊ）情報.総件数に設定する。
        if (CollectionUtils.isNotEmpty(kghCpnRaiAssJPOutEntities)) {
            // 2.2.1 総件数 > 0 件の場合
            KghCpnRaiAssJPOutEntity entity = kghCpnRaiAssJPOutEntities.get(0);
            /*
             * ===============3. 利用者基本情報を取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());
            // 氏名（姓）
            infoInDto.setName1Knj(ReportUtil.nullToEmpty(userPair.getLeft()));
            // 氏名（名）
            infoInDto.setName2Knj(ReportUtil.nullToEmpty(userPair.getRight()));
            /*
             * ===============4.アセスメント種別の取得===============
             * 
             */
            String syubetuKnj = this.getSyubetuByRaiId(
                    CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId())).getRight();
            /*
             * =============== 5.帳票用データを設定===============
             * 
             */
            // アセスメント種別
            infoInDto.setSyubetuKnj(syubetuKnj);
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ
            Boolean colorFlg = false;
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg())
                    || CommonConstants.STR_1.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }
            // j1_メモ
            infoInDto.setJ1MemoKnj(ReportUtil.nullToEmpty(entity.getJ1MemoKnj()));
            // j1_メモフォント
            infoInDto.setJ1MemoFont(getMemoFont(entity.getJ1MemoFont()));
            // j1_メモ色
            infoInDto.setJ1MemoColor(getMemoColor(entity.getJ1MemoColor(), colorFlg));
            // 転倒
            infoInDto.setJ1(ReportUtil.nullToEmpty(entity.getJ1()));
            // j2_メモ
            infoInDto.setJ2MemoKnj(ReportUtil.nullToEmpty(entity.getJ2MemoKnj()));
            // j2_メモフォント
            infoInDto.setJ2MemoFont(getMemoFont(entity.getJ2MemoFont()));
            // j2_メモ色
            infoInDto.setJ2MemoColor(getMemoColor(entity.getJ2MemoColor(), colorFlg));
            // 最近の転倒
            infoInDto.setJ2(ReportUtil.nullToEmpty(entity.getJ2()));
            // j3_メモ
            infoInDto.setJ3MemoKnj(ReportUtil.nullToEmpty(entity.getJ3MemoKnj()));
            // j3_メモフォント
            infoInDto.setJ3MemoFont(getMemoFont(entity.getJ3MemoFont()));
            // j3_メモ色
            infoInDto.setJ3MemoColor(getMemoColor(entity.getJ3MemoColor(), colorFlg));
            // 問題頻度_バランス_立位
            infoInDto.setJ3A(ReportUtil.nullToEmpty(entity.getJ3A()));
            // 問題頻度_バランス_方向転換
            infoInDto.setJ3B(ReportUtil.nullToEmpty(entity.getJ3B()));
            // 問題頻度_バランス_めまい
            infoInDto.setJ3C(ReportUtil.nullToEmpty(entity.getJ3C()));
            // 問題頻度_バランス_歩行
            infoInDto.setJ3D(ReportUtil.nullToEmpty(entity.getJ3D()));
            // 問題頻度_心肺_胸痛
            infoInDto.setJ3E(ReportUtil.nullToEmpty(entity.getJ3E()));
            // 問題頻度_心肺_気道
            infoInDto.setJ3F(ReportUtil.nullToEmpty(entity.getJ3F()));
            // 問題頻度_精神_思考
            infoInDto.setJ3G(ReportUtil.nullToEmpty(entity.getJ3G()));
            // 問題頻度_精神_妄想
            infoInDto.setJ3H(ReportUtil.nullToEmpty(entity.getJ3H()));
            // 問題頻度_精神_幻覚
            infoInDto.setJ3I(ReportUtil.nullToEmpty(entity.getJ3I()));
            // 問題頻度_神経_失語症
            infoInDto.setJ3J(ReportUtil.nullToEmpty(entity.getJ3J()));
            // 問題頻度_消化器系_胃酸
            infoInDto.setJ3K(ReportUtil.nullToEmpty(entity.getJ3K()));
            // 問題頻度_消化器系_便秘
            infoInDto.setJ3L(ReportUtil.nullToEmpty(entity.getJ3L()));
            // 問題頻度_消化器系_下痢
            infoInDto.setJ3M(ReportUtil.nullToEmpty(entity.getJ3M()));
            // 問題頻度_消化器系_嘔吐
            infoInDto.setJ3N(ReportUtil.nullToEmpty(entity.getJ3N()));
            // 問題頻度_睡眠障害_入眠
            infoInDto.setJ3O(ReportUtil.nullToEmpty(entity.getJ3O()));
            // 問題頻度_睡眠障害_過多
            infoInDto.setJ3P(ReportUtil.nullToEmpty(entity.getJ3P()));
            // 問題頻度_その他_誤嚥
            infoInDto.setJ3Q(ReportUtil.nullToEmpty(entity.getJ3Q()));
            // 問題頻度_その他_発熱
            infoInDto.setJ3R(ReportUtil.nullToEmpty(entity.getJ3R()));
            // 問題頻度_その他_出血
            infoInDto.setJ3S(ReportUtil.nullToEmpty(entity.getJ3S()));
            // 問題頻度_その他_不衛生
            infoInDto.setJ3T(ReportUtil.nullToEmpty(entity.getJ3T()));
            // 問題頻度_その他_末梢浮腫
            infoInDto.setJ3U(ReportUtil.nullToEmpty(entity.getJ3U()));
            // j4_メモ
            infoInDto.setJ4MemoKnj(ReportUtil.nullToEmpty(entity.getJ4MemoKnj()));
            // j4_メモフォント
            infoInDto.setJ4MemoFont(getMemoFont(entity.getJ4MemoFont()));
            // j4_メモ色
            infoInDto.setJ4MemoColor(getMemoColor(entity.getJ4MemoColor(), colorFlg));
            // 呼吸困難
            infoInDto.setJ4(ReportUtil.nullToEmpty(entity.getJ4()));
            // j5_メモ
            infoInDto.setJ5MemoKnj(ReportUtil.nullToEmpty(entity.getJ5MemoKnj()));
            // j5_メモフォント
            infoInDto.setJ5MemoFont(getMemoFont(entity.getJ5MemoFont()));
            // j5_メモ色
            infoInDto.setJ5MemoColor(getMemoColor(entity.getJ5MemoColor(), colorFlg));
            // 疲労感
            infoInDto.setJ5(ReportUtil.nullToEmpty(entity.getJ5()));
            // j6_a_メモ
            infoInDto.setJ6AMemoKnj(ReportUtil.nullToEmpty(entity.getJ6AMemoKnj()));
            // j6_a_メモフォント
            infoInDto.setJ6AMemoFont(getMemoFont(entity.getJ6AMemoFont()));
            // j6_a_メモ色
            infoInDto.setJ6AMemoColor(getMemoColor(entity.getJ6AMemoColor(), colorFlg));
            // 痛み_頻度
            infoInDto.setJ6A(ReportUtil.nullToEmpty(entity.getJ6A()));
            // j6_b_メモ
            infoInDto.setJ6BMemoKnj(ReportUtil.nullToEmpty(entity.getJ6BMemoKnj()));
            // j6_b_メモフォント
            infoInDto.setJ6BMemoFont(getMemoFont(entity.getJ6BMemoFont()));
            // j6_b_メモ色
            infoInDto.setJ6BMemoColor(getMemoColor(entity.getJ6BMemoColor(), colorFlg));
            // 痛み_程度
            infoInDto.setJ6B(ReportUtil.nullToEmpty(entity.getJ6B()));
            // j6_c_メモ
            infoInDto.setJ6CMemoKnj(ReportUtil.nullToEmpty(entity.getJ6CMemoKnj()));
            // j6_c_メモフォント
            infoInDto.setJ6CMemoFont(getMemoFont(entity.getJ6CMemoFont()));
            // j6_c_メモ色
            infoInDto.setJ6CMemoColor(getMemoColor(entity.getJ6CMemoColor(), colorFlg));
            // 痛み_持続性
            infoInDto.setJ6C(ReportUtil.nullToEmpty(entity.getJ6C()));
            // j6_d_メモ
            infoInDto.setJ6DMemoKnj(ReportUtil.nullToEmpty(entity.getJ6DMemoKnj()));
            // j6_d_メモフォント
            infoInDto.setJ6DMemoFont(getMemoFont(entity.getJ6DMemoFont()));
            // j6_d_メモ色
            infoInDto.setJ6DMemoColor(getMemoColor(entity.getJ6DMemoColor(), colorFlg));
            // 痛み_突発痛
            infoInDto.setJ6D(ReportUtil.nullToEmpty(entity.getJ6D()));
            // j6_e_メモ
            infoInDto.setJ6EMemoKnj(ReportUtil.nullToEmpty(entity.getJ6EMemoKnj()));
            // j6_e_メモフォント
            infoInDto.setJ6EMemoFont(getMemoFont(entity.getJ6EMemoFont()));
            // j6_e_メモ色
            infoInDto.setJ6EMemoColor(getMemoColor(entity.getJ6EMemoColor(), colorFlg));
            // 痛み_コントロール
            infoInDto.setJ6E(ReportUtil.nullToEmpty(entity.getJ6E()));
            // j7_メモ
            infoInDto.setJ7MemoKnj(ReportUtil.nullToEmpty(entity.getJ7MemoKnj()));
            // j7_メモフォント
            infoInDto.setJ7MemoFont(getMemoFont(entity.getJ7MemoFont()));
            // j7_メモ色
            infoInDto.setJ7MemoColor(getMemoColor(entity.getJ7MemoColor(), colorFlg));
            // 状態の不安定性_症状
            infoInDto.setJ7A(ReportUtil.nullToEmpty(entity.getJ7A()));
            // 状態の不安定性_発性
            infoInDto.setJ7B(ReportUtil.nullToEmpty(entity.getJ7B()));
            // 状態の不安定性_末期の疾患
            infoInDto.setJ7C(ReportUtil.nullToEmpty(entity.getJ7C()));
            // j8_メモ
            infoInDto.setJ8MemoKnj(ReportUtil.nullToEmpty(entity.getJ8MemoKnj()));
            // j8_メモフォント
            infoInDto.setJ8MemoFont(getMemoFont(entity.getJ8MemoFont()));
            // j8_メモ色
            infoInDto.setJ8MemoColor(getMemoColor(entity.getJ8MemoColor(), colorFlg));
            // 主観的健康感
            infoInDto.setJ8(ReportUtil.nullToEmpty(entity.getJ8()));
            // j9_a_メモ
            infoInDto.setJ9AMemoKnj(ReportUtil.nullToEmpty(entity.getJ9AMemoKnj()));
            // j9_a_メモフォント
            infoInDto.setJ9AMemoFont(getMemoFont(entity.getJ9AMemoFont()));
            // j9_a_メモ色
            infoInDto.setJ9AMemoColor(getMemoColor(entity.getJ9AMemoColor(), colorFlg));
            // 喫煙と飲酒_喫煙
            infoInDto.setJ9A(ReportUtil.nullToEmpty(entity.getJ9A()));
            // j9_b_メモ
            infoInDto.setJ9BMemoKnj(ReportUtil.nullToEmpty(entity.getJ9BMemoKnj()));
            // j9_b_メモフォント
            infoInDto.setJ9BMemoFont(getMemoFont(entity.getJ9BMemoFont()));
            // j9_b_メモ色
            infoInDto.setJ9BMemoColor(getMemoColor(entity.getJ9BMemoColor(), colorFlg));
            // 喫煙と飲酒_飲酒
            infoInDto.setJ9B(ReportUtil.nullToEmpty(entity.getJ9B()));
            /*
             * =============== 共通関数補足 2.文書番号情報取得処理===============
             * 
             */
            // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01 関数名：GetF3GkProfile
            // を利用し、設定の読込（システム環境以外の設定）情報を取得する。
            // 【リクエストパラメータ】.システムコード
            String syscd = model.getSyscd();
            // 職員ID
            String shokuId = printSubjectHistoryList.get(0).getChoPrtList().get(0).getShokuId();
            // セクション
            String section = printSubjectHistoryList.get(0).getChoPrtList().get(0).getSection();
            // 【リクエストパラメータ】.事業者ID
            String svJigyoId = model.getJigyoInfo().getSvJigyoId();

            String bunsyoKanriNo = this.getBunsyoKanriNo(syscd, shokuId, section, svJigyoId);
            // 文書管理番号
            infoInDto.setBunsyoKanriNo(bunsyoKanriNo);
        }
        return infoInDto;
    }

    /**
     * U06080_アセスメント表（Ｋ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetKReportServiceInDto getKReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {

        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        // infoInDtoを設定する。
        AssessmentSheetKReportServiceInDto infoInDto = new AssessmentSheetKReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        // 帳票タイトル ← "インターライ方式ケアアセスメント表"
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 指定日印刷区分 ← リクエストパラメータ.データ.印刷オプション.指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // 事業者名 ← リクエストパラメータ.データ.事業者名
        infoInDto.setJigyousha(ReportUtil.nullToEmpty(model.getSvJigyoKnj()));

        /*
         * ===============2. リクエストパラメータ.記入用シートを印刷するフラグ = true の場合、
         * 結果レスポンスを返却する。===============
         * 
         */
        Boolean isEmptyFlg = false;
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            isEmptyFlg = true;
        }
        // 記入用シートを印刷するフラグ リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(isEmptyFlg);
        // リクエストパラメータ.記入用シートを印刷するフラグ = true の場合
        if (isEmptyFlg) {

            infoInDto = this.getKDefReportParams(model, outDto, infoInDto);

            /*
             * ===============リクエストパラメータ.記入用シートを印刷するフラグ = false の場合、
             * アセスメント表（Ｋ）情報の取得。===============
             * 
             */
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getKReportParamsFromAssK(model, outDto, infoInDto);
        }
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｋ）帳票パラメータ取得
     * 
     * @param model     入力データ
     * @param outDto    出力データ
     * @param infoInDto PDF帳票出力詳細データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetKReportServiceInDto getKDefReportParams(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto, AssessmentSheetKReportServiceInDto infoInDto) throws Exception {
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType())));

        // 調査アセスメント種別 ← リクエストパラメータ.データ.印刷オプション.記入用シートアセスメント種別
        infoInDto.setAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType()));
        // 指定日（年号） 全角フレーム
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年） 全角フレーム
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月） 全角フレーム
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日） 全角フレーム
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）=""
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）=""
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // 身長（cm）=""
        infoInDto.setK1A(CommonConstants.BLANK_STRING);
        // 体重（kg）=""
        infoInDto.setK1B(CommonConstants.BLANK_STRING);
        // 栄養上の問題_体重減少=""
        infoInDto.setK2A(CommonConstants.BLANK_STRING);
        // 栄養上の問題_脱水=""
        infoInDto.setK2B(CommonConstants.BLANK_STRING);
        // 栄養上の問題_水分摂取=""
        infoInDto.setK2C(CommonConstants.BLANK_STRING);
        // 栄養上の問題_水分排泄量=""
        infoInDto.setK2D(CommonConstants.BLANK_STRING);
        // 栄養摂取の方法=""
        infoInDto.setK3(CommonConstants.BLANK_STRING);
        // 経静脈／経管栄養摂取量=""
        infoInDto.setK4(CommonConstants.BLANK_STRING);
        // 歯科口腔_義歯=""
        infoInDto.setK5A(CommonConstants.BLANK_STRING);
        // 歯科口腔_正常でない=""
        infoInDto.setK5B(CommonConstants.BLANK_STRING);
        // 歯科口腔_痛み/不快感=""
        infoInDto.setK5C(CommonConstants.BLANK_STRING);
        // 歯科口腔_口の渇き=""
        infoInDto.setK5D(CommonConstants.BLANK_STRING);
        // 歯科口腔_咀嚼困難=""
        infoInDto.setK5E(CommonConstants.BLANK_STRING);
        // 歯科口腔_歯肉=""
        infoInDto.setK5F(CommonConstants.BLANK_STRING);
        // 栄養管理_食物形態の加工=""
        infoInDto.setK6A(CommonConstants.BLANK_STRING);
        // 栄養管理_低塩分=""
        infoInDto.setK6B(CommonConstants.BLANK_STRING);
        // 栄養管理_カロリー制限=""
        infoInDto.setK6C(CommonConstants.BLANK_STRING);
        // 栄養管理_低脂肪=""
        infoInDto.setK6D(CommonConstants.BLANK_STRING);
        // 栄養管理_その他=""
        infoInDto.setK6E(CommonConstants.BLANK_STRING);
        // k1_メモ=""
        infoInDto.setK1MemoKnj(CommonConstants.BLANK_STRING);
        // k1_メモフォント="12"
        infoInDto.setK1MemoFont(CommonConstants.STR_12);
        // k1_メモ色=""
        infoInDto.setK1MemoColor(CommonConstants.BLANK_STRING);
        // k2_メモ=""
        infoInDto.setK2MemoKnj(CommonConstants.BLANK_STRING);
        // k2_メモフォント="12"
        infoInDto.setK2MemoFont(CommonConstants.STR_12);
        // k2_メモ色=""
        infoInDto.setK2MemoColor(CommonConstants.BLANK_STRING);
        // k3_メモ=""
        infoInDto.setK3MemoKnj(CommonConstants.BLANK_STRING);
        // k3_メモフォント="12"
        infoInDto.setK3MemoFont(CommonConstants.STR_12);
        // k3_メモ色=""
        infoInDto.setK3MemoColor(CommonConstants.BLANK_STRING);
        // k4_メモ=""
        infoInDto.setK4MemoKnj(CommonConstants.BLANK_STRING);
        // k4_メモフォント="12"
        infoInDto.setK4MemoFont(CommonConstants.STR_12);
        // k4_メモ色=""
        infoInDto.setK4MemoColor(CommonConstants.BLANK_STRING);
        // k5_メモ=""
        infoInDto.setK5MemoKnj(CommonConstants.BLANK_STRING);
        // k5_メモフォント="12"
        infoInDto.setK5MemoFont(CommonConstants.STR_12);
        // k5_メモ色=""
        infoInDto.setK5MemoColor(CommonConstants.BLANK_STRING);
        // k6_メモ=""
        infoInDto.setK6MemoKnj(CommonConstants.BLANK_STRING);
        // k6_メモフォント="12"
        infoInDto.setK6MemoFont(CommonConstants.STR_12);
        // k6_メモ色=""
        infoInDto.setK6MemoColor(CommonConstants.BLANK_STRING);
        // 文書管理番号=""
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;

    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｋ）帳票パラメータ取得
     * 
     * @param model     入力データ
     * @param outDto    出力データ
     * @param infoInDto PDF帳票出力詳細データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetKReportServiceInDto getKReportParamsFromAssK(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto, AssessmentSheetKReportServiceInDto infoInDto) throws Exception {
        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();

        // アセスメント表（Ｋ）情報の取得。
        // アセスメント表（Ｋ）情報取得のINPUT情報初期化
        KghCpnRaiAssKPByCriteriaInEntity kghCpnRaiAssKPByCriteriaInEntity = new KghCpnRaiAssKPByCriteriaInEntity();
        // アセスメントID←リクエストパラメータ.データ.印刷対象履歴リスト[0].アセスメントID
        kghCpnRaiAssKPByCriteriaInEntity
                .setAnRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
        // 2.1. 下記のアセスメント表（Ｋ）情報取得のDAOを利用し、アセスメント表（Ｋ）情報を取得する。
        List<KghCpnRaiAssKPOutEntity> kghCpnRaiAssKPOutEntityList = raiRrkKghCpnRaiAssKSelectMapper
                .findKghCpnRaiAssKPByCriteria(kghCpnRaiAssKPByCriteriaInEntity);

        /*
         * ===============2.2. 「2.1.」で取得したアセスメント表（Ｋ）情報の件数 > 0 件の場合
         * 処理を続ける、===============
         * 
         */

        if (CollectionUtils.isNotEmpty(kghCpnRaiAssKPOutEntityList)) {
            // アセスメント表（Ｋ）情報の一件目を取得する
            KghCpnRaiAssKPOutEntity entity = kghCpnRaiAssKPOutEntityList.get(0);
            /**
             * ===============3.利用者基本情報の取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());
            // 氏名（姓）
            infoInDto.setName1Knj(userPair.getLeft());
            // 氏名（名）
            infoInDto.setName2Knj(userPair.getRight());

            /*
             * =============== 4. アセスメント種別情報を取得する。===============
             * 
             */
            Pair<Integer, String> syubetuPair = this
                    .getSyubetuByRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
            // 調査アセスメント種別名
            infoInDto.setSyubetuKnj(syubetuPair.getRight());
            // 調査アセスメント種別
            infoInDto.setAssType(syubetuPair.getLeft());

            /*
             * ===============5.上記編集した帳票用データを設定する。===============
             * 
             */

            // 身長（cm）
            infoInDto.setK1A(ReportUtil.nullToEmpty(entity.getK1A()));
            // 体重（kg）
            infoInDto.setK1B(ReportUtil.nullToEmpty(entity.getK1B()));
            // 栄養上の問題_体重減少
            infoInDto.setK2A(ReportUtil.nullToEmpty(entity.getK2A()));
            // 栄養上の問題_脱水
            infoInDto.setK2B(ReportUtil.nullToEmpty(entity.getK2B()));
            // 栄養上の問題_水分摂取
            infoInDto.setK2C(ReportUtil.nullToEmpty(entity.getK2C()));
            // 栄養上の問題_水分排泄量
            infoInDto.setK2D(ReportUtil.nullToEmpty(entity.getK2D()));
            // 栄養摂取の方法
            infoInDto.setK3(ReportUtil.nullToEmpty(entity.getK3()));
            // 経静脈／経管栄養摂取量
            infoInDto.setK4(ReportUtil.nullToEmpty(entity.getK4()));
            // 歯科口腔_義歯
            infoInDto.setK5A(ReportUtil.nullToEmpty(entity.getK5A()));
            // 歯科口腔_正常でない
            infoInDto.setK5B(ReportUtil.nullToEmpty(entity.getK5B()));
            // 歯科口腔_痛み/不快感
            infoInDto.setK5C(ReportUtil.nullToEmpty(entity.getK5C()));
            // 歯科口腔_口の渇き
            infoInDto.setK5D(ReportUtil.nullToEmpty(entity.getK5D()));
            // 歯科口腔_咀嚼困難
            infoInDto.setK5E(ReportUtil.nullToEmpty(entity.getK5E()));
            // 歯科口腔_歯肉
            infoInDto.setK5F(ReportUtil.nullToEmpty(entity.getK5F()));
            // 栄養管理_食物形態の加工
            infoInDto.setK6A(ReportUtil.nullToEmpty(entity.getK6A()));
            // 栄養管理_低塩分
            infoInDto.setK6B(ReportUtil.nullToEmpty(entity.getK6B()));
            // 栄養管理_カロリー制限
            infoInDto.setK6C(ReportUtil.nullToEmpty(entity.getK6C()));
            // 栄養管理_低脂肪
            infoInDto.setK6D(ReportUtil.nullToEmpty(entity.getK6D()));
            // 栄養管理_その他
            infoInDto.setK6E(ReportUtil.nullToEmpty(entity.getK6E()));
            // k1_メモ
            infoInDto.setK1MemoKnj(ReportUtil.nullToEmpty(entity.getK1MemoKnj()));
            // k1_メモフォント
            infoInDto.setK1MemoFont(getMemoFont(entity.getK1MemoFont()));

            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ
            Boolean colorFlg = false;
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg()) ||
                    CommonConstants.STR_1.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }
            // k1_メモ色
            infoInDto.setK1MemoColor(getMemoColor(entity.getK1MemoColor(), colorFlg));
            // k2_メモ
            infoInDto.setK2MemoKnj(ReportUtil.nullToEmpty(entity.getK2MemoKnj()));
            // k2_メモフォント
            infoInDto.setK2MemoFont(getMemoFont(entity.getK2MemoFont()));
            // k2_メモ色
            infoInDto.setK2MemoColor(getMemoColor(entity.getK2MemoColor(), colorFlg));
            // k3_メモ
            infoInDto.setK3MemoKnj(ReportUtil.nullToEmpty(entity.getK3MemoKnj()));
            // k3_メモフォント
            infoInDto.setK3MemoFont(getMemoFont(entity.getK3MemoFont()));
            // k3_メモ色
            infoInDto.setK3MemoColor(getMemoColor(entity.getK3MemoColor(), colorFlg));
            // k4_メモ
            infoInDto.setK4MemoKnj(ReportUtil.nullToEmpty(entity.getK4MemoKnj()));
            // k4_メモフォント
            infoInDto.setK4MemoFont(getMemoFont(entity.getK4MemoFont()));
            // k4_メモ色
            infoInDto.setK4MemoColor(getMemoColor(entity.getK4MemoColor(), colorFlg));
            // k5_メモ
            infoInDto.setK5MemoKnj(ReportUtil.nullToEmpty(entity.getK5MemoKnj()));
            // k5_メモフォント
            infoInDto.setK5MemoFont(getMemoFont(entity.getK5MemoFont()));
            // k5_メモ色
            infoInDto.setK5MemoColor(getMemoColor(entity.getK5MemoColor(), colorFlg));
            // k6_メモ
            infoInDto.setK6MemoKnj(ReportUtil.nullToEmpty(entity.getK6MemoKnj()));
            // k6_メモフォント
            infoInDto.setK6MemoFont(getMemoFont(entity.getK6MemoFont()));
            // k6_メモ色
            infoInDto.setK6MemoColor(getMemoColor(entity.getK6MemoColor(), colorFlg));

        }

        // 指定日（年号）
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
        // 指定日（年）
        infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
        // 指定日（月）
        infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
        // 指定日（日）
        infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

        // 2.文書番号情報の取得
        /*
         * ===============共通関数補足 2.文書番号情報取得処理===============
         * 
         */
        // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01 関数名：GetF3GkProfile
        // を利用し、設定の読込（システム環境以外の設定）情報を取得する。
        // 【リクエストパラメータ】.システムコード
        String syscd = model.getSyscd();
        // 職員ID←リクエストパラメータ.データ.印刷対象履歴リスト[0].出力帳票印刷情報リスト[0].職員ID
        String shokuId = printSubjectHistoryList.get(0).getChoPrtList().get(0).getShokuId();
        // セクション←リクエストパラメータ.データ.出力帳票印刷情報リスト[0].セクション
        String section = printSubjectHistoryList.get(0).getChoPrtList().get(0).getSection();
        // サービス事業者ID←リクエストパラメータ.事業者情報.事業者ID
        String svJigyoId = model.getJigyoInfo().getSvJigyoId();

        String bunsyoKanriNo = this.getBunsyoKanriNo(syscd, shokuId, section, svJigyoId);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(ReportUtil.nullToEmpty(bunsyoKanriNo));

        return infoInDto;

    }

    /**
     * U06080_アセスメント表（Ｌ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetLReportServiceInDto getLReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {

        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        // infoInDtoを設定する。
        AssessmentSheetLReportServiceInDto infoInDto = new AssessmentSheetLReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        // 帳票タイトル ← "インターライ方式ケアアセスメント表"
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 指定日印刷区分 ← リクエストパラメータ.データ.印刷オプション.指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // 事業者名 ← リクエストパラメータ.データ.事業者名
        infoInDto.setJigyoKnj(ReportUtil.nullToEmpty(model.getSvJigyoKnj()));

        /*
         * ===============2. リクエストパラメータ.記入用シートを印刷するフラグ = true の場合、
         * 結果レスポンスを返却する。===============
         * 
         */
        Boolean isEmptyFlg = false;
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            isEmptyFlg = true;
        }
        // 記入用シートを印刷するフラグ リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(isEmptyFlg);
        // リクエストパラメータ.記入用シートを印刷するフラグ = true の場合
        if (isEmptyFlg) {

            infoInDto = this.getLDefReportParams(model, outDto, infoInDto);

            /*
             * ===============リクエストパラメータ.記入用シートを印刷するフラグ = false の場合、
             * アセスメント表（Ｌ）情報の取得。===============
             * 
             */
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getLReportParamsFromAssL(model, outDto, infoInDto);
        }
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｌ）帳票パラメータ取得
     * 
     * @param model     入力データ
     * @param outDto    出力データ
     * @param infoInDto PDF帳票出力詳細データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetLReportServiceInDto getLDefReportParams(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto, AssessmentSheetLReportServiceInDto infoInDto) throws Exception {
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType())));

        // 指定日（年号） 全角フレーム
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年） 全角フレーム
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月） 全角フレーム
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日） 全角フレーム
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // l1_メモ
        infoInDto.setL1MemoKnj(CommonConstants.BLANK_STRING);
        // l1_メモフォント
        infoInDto.setL1MemoFont(CommonConstants.STR_12);
        // l1_メモ色
        infoInDto.setL1MemoColor(CommonConstants.BLANK_STRING);
        // 最重度の褥瘡
        infoInDto.setL1(CommonConstants.BLANK_STRING);
        // l2_メモ
        infoInDto.setL2MemoKnj(CommonConstants.BLANK_STRING);
        // l2_メモフォント
        infoInDto.setL2MemoFont(CommonConstants.STR_12);
        // l2_メモ色
        infoInDto.setL2MemoColor(CommonConstants.BLANK_STRING);
        // 褥瘡の既往
        infoInDto.setL2(CommonConstants.BLANK_STRING);
        // l3_メモ
        infoInDto.setL3MemoKnj(CommonConstants.BLANK_STRING);
        // l3_メモフォント
        infoInDto.setL3MemoFont(CommonConstants.STR_12);
        // l3_メモ色
        infoInDto.setL3MemoColor(CommonConstants.BLANK_STRING);
        // 褥瘡以外の皮膚潰瘍
        infoInDto.setL3(CommonConstants.BLANK_STRING);
        // l4_メモ
        infoInDto.setL4MemoKnj(CommonConstants.BLANK_STRING);
        // l4_メモフォント
        infoInDto.setL4MemoFont(CommonConstants.STR_12);
        // l4_メモ色
        infoInDto.setL4MemoColor(CommonConstants.BLANK_STRING);
        // 重要な皮膚の問題
        infoInDto.setL4(CommonConstants.BLANK_STRING);
        // l5_メモ
        infoInDto.setL5MemoKnj(CommonConstants.BLANK_STRING);
        // l5_メモフォント
        infoInDto.setL5MemoFont(CommonConstants.STR_12);
        // l5_メモ色
        infoInDto.setL5MemoColor(CommonConstants.BLANK_STRING);
        // 皮膚の裂傷や切り傷
        infoInDto.setL5(CommonConstants.BLANK_STRING);
        // l6_メモ
        infoInDto.setL6MemoKnj(CommonConstants.BLANK_STRING);
        // l6_メモフォント
        infoInDto.setL6MemoFont(CommonConstants.STR_12);
        // l6_メモ色
        infoInDto.setL6MemoColor(CommonConstants.BLANK_STRING);
        // その他の皮膚の状態や変化
        infoInDto.setL6(CommonConstants.BLANK_STRING);
        // l7_メモ
        infoInDto.setL7MemoKnj(CommonConstants.BLANK_STRING);
        // l7_メモフォント
        infoInDto.setL7MemoFont(CommonConstants.STR_12);
        // l7_メモ色
        infoInDto.setL7MemoColor(CommonConstants.BLANK_STRING);
        // 足の問題
        infoInDto.setL7(CommonConstants.BLANK_STRING);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;

    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｌ）帳票パラメータ取得
     * 
     * @param model     入力データ
     * @param outDto    出力データ
     * @param infoInDto PDF帳票出力詳細データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetLReportServiceInDto getLReportParamsFromAssL(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto, AssessmentSheetLReportServiceInDto infoInDto) throws Exception {
        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();

        // アセスメント表（Ｌ）情報の取得。
        // アセスメント表（Ｌ）情報取得のINPUT情報初期化
        KghCpnRaiAssLPByCriteriaInEntity kghCpnRaiAssLPByCriteriaInEntity = new KghCpnRaiAssLPByCriteriaInEntity();
        // アセスメントID←リクエストパラメータ.データ.印刷対象履歴リスト[0].アセスメントID
        kghCpnRaiAssLPByCriteriaInEntity
                .setAnRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
        // 2.1. 下記のアセスメント表（Ｌ）情報取得のDAOを利用し、アセスメント表（Ｌ）情報を取得する。
        List<KghCpnRaiAssLPOutEntity> kghCpnRaiAssLPOutEntityList = raiRrkKghCpnRaiAssLSelectMapper
                .findKghCpnRaiAssLPByCriteria(kghCpnRaiAssLPByCriteriaInEntity);

        /*
         * ===============2.2. 「2.1.」で取得したアセスメント表（Ｌ）情報の件数 > 0 件の場合
         * 処理を続ける、===============
         * 
         */

        if (CollectionUtils.isNotEmpty(kghCpnRaiAssLPOutEntityList)) {
            // アセスメント表（Ｌ）情報の一件目を取得する
            KghCpnRaiAssLPOutEntity entity = kghCpnRaiAssLPOutEntityList.get(0);
            /*
             * ===============3.1. 利用者基本情報の取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());
            // 氏名（姓）
            infoInDto.setName1Knj(ReportUtil.nullToEmpty(userPair.getLeft()));
            // 氏名（名）
            infoInDto.setName2Knj(ReportUtil.nullToEmpty(userPair.getRight()));

            /*
             * =============== 4. アセスメント種別情報を取得する。===============
             * 
             */
            Pair<Integer, String> syubetuPair = this
                    .getSyubetuByRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
            // 調査アセスメント種別名
            infoInDto.setSyubetuKnj(syubetuPair.getRight());

            /*
             * ===============5.上記編集した帳票用データを設定する。===============
             * 
             */
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            Boolean colorFlg = false;
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg()) ||
                    CommonConstants.STR_1.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }
            // l1_メモ
            infoInDto.setL1MemoKnj(ReportUtil.nullToEmpty(entity.getL1MemoKnj()));
            // l1_メモフォント
            infoInDto.setL1MemoFont(getMemoFont(entity.getL1MemoFont()));
            // l1_メモ色
            infoInDto.setL1MemoColor(getMemoColor(entity.getL1MemoColor(), colorFlg));
            // 最重度の褥瘡
            infoInDto.setL1(ReportUtil.nullToEmpty(entity.getL1()));
            // l2_メモ
            infoInDto.setL2MemoKnj(ReportUtil.nullToEmpty(entity.getL2MemoKnj()));
            // l2_メモフォント
            infoInDto.setL2MemoFont(getMemoFont(entity.getL2MemoFont()));
            // l2_メモ色
            infoInDto.setL2MemoColor(getMemoColor(entity.getL2MemoColor(), colorFlg));
            // 褥瘡の既往
            infoInDto.setL2(ReportUtil.nullToEmpty(entity.getL2()));
            // l3_メモ
            infoInDto.setL3MemoKnj(ReportUtil.nullToEmpty(entity.getL3MemoKnj()));
            // l3_メモフォント
            infoInDto.setL3MemoFont(getMemoFont(entity.getL3MemoFont()));
            // l3_メモ色
            infoInDto.setL3MemoColor(getMemoColor(entity.getL3MemoColor(), colorFlg));
            // 褥瘡以外の皮膚潰瘍
            infoInDto.setL3(ReportUtil.nullToEmpty(entity.getL3()));
            // l4_メモ
            infoInDto.setL4MemoKnj(ReportUtil.nullToEmpty(entity.getL4MemoKnj()));
            // l4_メモフォント
            infoInDto.setL4MemoFont(getMemoFont(entity.getL4MemoFont()));
            // l4_メモ色
            infoInDto.setL4MemoColor(getMemoColor(entity.getL4MemoColor(), colorFlg));
            // 重要な皮膚の問題
            infoInDto.setL4(ReportUtil.nullToEmpty(entity.getL4()));
            // l5_メモ
            infoInDto.setL5MemoKnj(ReportUtil.nullToEmpty(entity.getL5MemoKnj()));
            // l5_メモフォント
            infoInDto.setL5MemoFont(getMemoFont(entity.getL5MemoFont()));
            // l5_メモ色
            infoInDto.setL5MemoColor(getMemoColor(entity.getL5MemoColor(), colorFlg));
            // 皮膚の裂傷や切り傷
            infoInDto.setL5(ReportUtil.nullToEmpty(entity.getL5()));
            // l6_メモ
            infoInDto.setL6MemoKnj(ReportUtil.nullToEmpty(entity.getL6MemoKnj()));
            // l6_メモフォント
            infoInDto.setL6MemoFont(getMemoFont(entity.getL6MemoFont()));
            // l6_メモ色
            infoInDto.setL6MemoColor(getMemoColor(entity.getL6MemoColor(), colorFlg));
            // その他の皮膚の状態や変化
            infoInDto.setL6(ReportUtil.nullToEmpty(entity.getL6()));
            // l7_メモ
            infoInDto.setL7MemoKnj(ReportUtil.nullToEmpty(entity.getL7MemoKnj()));
            // l7_メモフォント
            infoInDto.setL7MemoFont(getMemoFont(entity.getL7MemoFont()));
            // l7_メモ色
            infoInDto.setL7MemoColor(getMemoColor(entity.getL7MemoColor(), colorFlg));
            // 足の問題
            infoInDto.setL7(ReportUtil.nullToEmpty(entity.getL7()));

        }

        // 指定日（年号）
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
        // 指定日（年）
        infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
        // 指定日（月）
        infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
        // 指定日（日）
        infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

        // 2.文書番号情報の取得
        /*
         * ===============共通関数補足 2.文書番号情報取得処理===============
         * 
         */
        // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01 関数名：GetF3GkProfile
        // を利用し、設定の読込（システム環境以外の設定）情報を取得する。
        // 【リクエストパラメータ】.システムコード
        String syscd = model.getSyscd();
        // 職員ID←リクエストパラメータ.データ.印刷対象履歴リスト[0].出力帳票印刷情報リスト[0].職員ID
        String shokuId = printSubjectHistoryList.get(0).getChoPrtList().get(0).getShokuId();
        // セクション←リクエストパラメータ.データ.出力帳票印刷情報リスト[0].セクション
        String section = printSubjectHistoryList.get(0).getChoPrtList().get(0).getSection();
        // サービス事業者ID←リクエストパラメータ.事業者情報.事業者ID
        String svJigyoId = model.getJigyoInfo().getSvJigyoId();

        String bunsyoKanriNo = this.getBunsyoKanriNo(syscd, shokuId, section, svJigyoId);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(ReportUtil.nullToEmpty(bunsyoKanriNo));

        return infoInDto;

    }

    /**
     * U06080_アセスメント表（Ｍ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetMReportServiceInDto getMReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {

        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        // infoInDtoを設定する。
        AssessmentSheetMReportServiceInDto infoInDto = new AssessmentSheetMReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        // 帳票タイトル ← "インターライ方式ケアアセスメント表"
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 指定日印刷区分 ← リクエストパラメータ.データ.印刷オプション.指定日印刷区分
        infoInDto.setPrDate(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // 事業者名 ← リクエストパラメータ.データ.事業者名
        infoInDto.setJigyosha(ReportUtil.nullToEmpty(model.getSvJigyoKnj()));

        /*
         * ===============2. リクエストパラメータ.記入用シートを印刷するフラグ = true の場合、
         * 結果レスポンスを返却する。===============
         * 
         */
        Boolean isEmptyFlg = false;
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            isEmptyFlg = true;
        }
        // 記入用シートを印刷するフラグ リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(isEmptyFlg);
        // リクエストパラメータ.記入用シートを印刷するフラグ = true の場合
        if (isEmptyFlg) {

            infoInDto = this.getMDefReportParams(model, outDto, infoInDto);

            /*
             * ===============リクエストパラメータ.記入用シートを印刷するフラグ = false の場合、
             * アセスメント表（Ｍ）情報の取得。===============
             * 
             */
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getMReportParamsFromAssM(model, outDto, infoInDto);
        }
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｍ）帳票パラメータ取得
     * 
     * @param model     入力データ
     * @param outDto    出力データ
     * @param infoInDto PDF帳票出力詳細データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetMReportServiceInDto getMDefReportParams(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto, AssessmentSheetMReportServiceInDto infoInDto) throws Exception {
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType())));

        // 調査アセスメント種別 ← リクエストパラメータ.データ.印刷オプション.記入用シートアセスメント種別
        infoInDto.setAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType()));

        // 指定日（年号） 全角フレーム
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年） 全角フレーム
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月） 全角フレーム
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日） 全角フレーム
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）=""
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）=""
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // m1_メモ=""
        infoInDto.setM1MemoKnj(CommonConstants.BLANK_STRING);
        // m1_メモフォント="12"
        infoInDto.setM1MemoFont(CommonConstants.STR_12);
        // m1_メモ色=""
        infoInDto.setM1MemoColor(CommonConstants.BLANK_STRING);
        // 活動への平均参加時間=""
        infoInDto.setM1(CommonConstants.BLANK_STRING);
        // m2_メモ=""
        infoInDto.setM2MemoKnj(CommonConstants.BLANK_STRING);
        // m2_メモフォント="12"
        infoInDto.setM2MemoFont(CommonConstants.STR_12);
        // m2_メモ色=""
        infoInDto.setM2MemoColor(CommonConstants.BLANK_STRING);
        // 好む活動と関与_カード=""
        infoInDto.setM2A(CommonConstants.BLANK_STRING);
        // 好む活動と関与_コンピュータ=""
        infoInDto.setM2B(CommonConstants.BLANK_STRING);
        // 好む活動と関与_会話=""
        infoInDto.setM2C(CommonConstants.BLANK_STRING);
        // 好む活動と関与_創作活動=""
        infoInDto.setM2D(CommonConstants.BLANK_STRING);
        // 好む活動と関与_ダンス=""
        infoInDto.setM2E(CommonConstants.BLANK_STRING);
        // 好む活動と関与_人生=""
        infoInDto.setM2F(CommonConstants.BLANK_STRING);
        // 好む活動と関与_運動=""
        infoInDto.setM2G(CommonConstants.BLANK_STRING);
        // 好む活動と関与_庭、畑仕事=""
        infoInDto.setM2H(CommonConstants.BLANK_STRING);
        // 好む活動と関与_他者の手助け=""
        infoInDto.setM2I(CommonConstants.BLANK_STRING);
        // 好む活動と関与_音楽=""
        infoInDto.setM2J(CommonConstants.BLANK_STRING);
        // 好む活動と関与_ペット=""
        infoInDto.setM2K(CommonConstants.BLANK_STRING);
        // 好む活動と関与_読書=""
        infoInDto.setM2L(CommonConstants.BLANK_STRING);
        // 好む活動と関与_宗教活動=""
        infoInDto.setM2M(CommonConstants.BLANK_STRING);
        // 好む活動と関与_旅行=""
        infoInDto.setM2N(CommonConstants.BLANK_STRING);
        // 好む活動と関与_散歩=""
        infoInDto.setM2O(CommonConstants.BLANK_STRING);
        // 好む活動と関与_鑑賞=""
        infoInDto.setM2P(CommonConstants.BLANK_STRING);
        // 好む活動と関与_料理=""
        infoInDto.setM2Q(CommonConstants.BLANK_STRING);
        // 好む活動と関与_パズル=""
        infoInDto.setM2R(CommonConstants.BLANK_STRING);
        // 好む活動と関与_その他1漢字=""
        infoInDto.setM2SKnj(CommonConstants.BLANK_STRING);
        // 好む活動と関与_その他1=""
        infoInDto.setM2S(CommonConstants.BLANK_STRING);
        // 好む活動と関与_その他2漢字=""
        infoInDto.setM2TKnj(CommonConstants.BLANK_STRING);
        // 好む活動と関与_その他2=""
        infoInDto.setM2T(CommonConstants.BLANK_STRING);
        // m3_メモ=""
        infoInDto.setM3MemoKnj(CommonConstants.BLANK_STRING);
        // m3_メモフォント="12"
        infoInDto.setM3MemoFont(CommonConstants.STR_12);
        // m3_メモ色=""
        infoInDto.setM3MemoColor(CommonConstants.BLANK_STRING);
        // 日中寝ている時間=""
        infoInDto.setM3(CommonConstants.BLANK_STRING);
        // m4_メモ=""
        infoInDto.setM4MemoKnj(CommonConstants.BLANK_STRING);
        // m4_メモフォント="12"
        infoInDto.setM4MemoFont(CommonConstants.STR_12);
        // m4_メモ色=""
        infoInDto.setM4MemoColor(CommonConstants.BLANK_STRING);
        // 興味・関心_レクリエーション=""
        infoInDto.setM4A(CommonConstants.BLANK_STRING);
        // 興味・関心_転倒予防=""
        infoInDto.setM4B(CommonConstants.BLANK_STRING);
        // 興味・関心_記憶力改善=""
        infoInDto.setM4C(CommonConstants.BLANK_STRING);
        // 興味・関心_身体機能向上=""
        infoInDto.setM4D(CommonConstants.BLANK_STRING);
        // 文書管理番号=""
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;

    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｍ）帳票パラメータ取得
     * 
     * @param model     入力データ
     * @param outDto    出力データ
     * @param infoInDto PDF帳票出力詳細データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetMReportServiceInDto getMReportParamsFromAssM(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto, AssessmentSheetMReportServiceInDto infoInDto) throws Exception {
        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();

        // アセスメント表（Ｍ）情報の取得。
        // アセスメント表（Ｍ）情報取得のINPUT情報初期化
        KghCpnRaiAssMPByCriteriaInEntity kghCpnRaiAssMPByCriteriaInEntity = new KghCpnRaiAssMPByCriteriaInEntity();
        // アセスメントID←リクエストパラメータ.データ.印刷対象履歴リスト[0].アセスメントID
        kghCpnRaiAssMPByCriteriaInEntity
                .setAnRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
        // 2.1. 下記のアセスメント表（Ｍ）情報取得のDAOを利用し、アセスメント表（Ｍ）情報を取得する。
        List<KghCpnRaiAssMPOutEntity> kghCpnRaiAssMPOutEntityList = raiRrkKghCpnRaiAssMSelectMapper
                .findKghCpnRaiAssMPByCriteria(kghCpnRaiAssMPByCriteriaInEntity);

        /*
         * ===============2.2. 「2.1.」で取得したアセスメント表（Ｍ）情報の件数 > 0 件の場合
         * 処理を続ける、===============
         * 
         */

        if (CollectionUtils.isNotEmpty(kghCpnRaiAssMPOutEntityList)) {
            // アセスメント表（Ｍ）情報の一件目を取得する
            KghCpnRaiAssMPOutEntity entity = kghCpnRaiAssMPOutEntityList.get(0);
            /*
             * ===============3.1. 利用者基本情報の取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());
            // 氏名（姓）
            infoInDto.setName1Knj(ReportUtil.nullToEmpty(userPair.getLeft()));
            // 氏名（名）
            infoInDto.setName2Knj(ReportUtil.nullToEmpty(userPair.getRight()));

            /*
             * =============== 4. アセスメント種別情報を取得する。===============
             * 
             */
            Pair<Integer, String> syubetuPair = this
                    .getSyubetuByRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
            // 調査アセスメント種別名
            infoInDto.setSyubetuKnj(syubetuPair.getRight());
            // 調査アセスメント種別
            infoInDto.setAssType(syubetuPair.getLeft());

            /*
             * ===============5.上記編集した帳票用データを設定する。===============
             * 
             */

            // m1_メモ=API定義の処理「2.1」のm1_メモ
            infoInDto.setM1MemoKnj(ReportUtil.nullToEmpty(entity.getM1MemoKnj()));
            // m1_メモフォント
            infoInDto.setM1MemoFont(getMemoFont(entity.getM1MemoFont()));
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            Boolean colorFlg = false;
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg()) ||
                    CommonConstants.STR_1.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }
            // m1_メモ色
            infoInDto.setM1MemoColor(getMemoColor(entity.getM1MemoColor(), colorFlg));
            // 活動への平均参加時間=API定義の処理「2.1」の「活動への平均参加時間」
            infoInDto.setM1(ReportUtil.nullToEmpty(entity.getM1()));
            // m2_メモ=API定義の処理「2.1」のm2_メモ
            infoInDto.setM2MemoKnj(ReportUtil.nullToEmpty(entity.getM2MemoKnj()));
            // m2_メモフォント
            infoInDto.setM2MemoFont(getMemoFont(entity.getM2MemoFont()));
            // m2_メモ色
            infoInDto.setM2MemoColor(getMemoColor(entity.getM2MemoColor(), colorFlg));
            // 好む活動と関与_カード=API定義の処理「2.1」の「好む活動と関与_カード」
            infoInDto.setM2A(ReportUtil.nullToEmpty(entity.getM2A()));
            // 好む活動と関与_コンピュータ=API定義の処理「2.1」の「好む活動と関与_コンピュータ」
            infoInDto.setM2B(ReportUtil.nullToEmpty(entity.getM2B()));
            // 好む活動と関与_会話=API定義の処理「2.1」の「好む活動と関与_会話」
            infoInDto.setM2C(ReportUtil.nullToEmpty(entity.getM2C()));
            // 好む活動と関与_創作活動=API定義の処理「2.1」の「好む活動と関与_創作活動」
            infoInDto.setM2D(ReportUtil.nullToEmpty(entity.getM2D()));
            // 好む活動と関与_ダンス=API定義の処理「2.1」の「好む活動と関与_ダンス」
            infoInDto.setM2E(ReportUtil.nullToEmpty(entity.getM2E()));
            // 好む活動と関与_人生=API定義の処理「2.1」の「好む活動と関与_人生」
            infoInDto.setM2F(ReportUtil.nullToEmpty(entity.getM2F()));
            // 好む活動と関与_運動=API定義の処理「2.1」の「好む活動と関与_運動」
            infoInDto.setM2G(ReportUtil.nullToEmpty(entity.getM2G()));
            // 好む活動と関与_庭、畑仕事=API定義の処理「2.1」の「好む活動と関与_庭、畑仕事」
            infoInDto.setM2H(ReportUtil.nullToEmpty(entity.getM2H()));
            // 好む活動と関与_他者の手助け=API定義の処理「2.1」の「好む活動と関与_他者の手助け」
            infoInDto.setM2I(ReportUtil.nullToEmpty(entity.getM2I()));
            // 好む活動と関与_音楽=API定義の処理「2.1」の「好む活動と関与_音楽」
            infoInDto.setM2J(ReportUtil.nullToEmpty(entity.getM2J()));
            // 好む活動と関与_ペット=API定義の処理「2.1」の「好む活動と関与_ペット」
            infoInDto.setM2K(ReportUtil.nullToEmpty(entity.getM2K()));
            // 好む活動と関与_読書=API定義の処理「2.1」の「好む活動と関与_読書」
            infoInDto.setM2L(ReportUtil.nullToEmpty(entity.getM2L()));
            // 好む活動と関与_宗教活動=API定義の処理「2.1」の「好む活動と関与_宗教活動」
            infoInDto.setM2M(ReportUtil.nullToEmpty(entity.getM2M()));
            // 好む活動と関与_旅行=API定義の処理「2.1」の「好む活動と関与_旅行」
            infoInDto.setM2N(ReportUtil.nullToEmpty(entity.getM2N()));
            // 好む活動と関与_散歩=API定義の処理「2.1」の「好む活動と関与_散歩」
            infoInDto.setM2O(ReportUtil.nullToEmpty(entity.getM2O()));
            // 好む活動と関与_鑑賞=API定義の処理「2.1」の「好む活動と関与_鑑賞」
            infoInDto.setM2P(ReportUtil.nullToEmpty(entity.getM2P()));
            // 好む活動と関与_料理=API定義の処理「2.1」の「好む活動と関与_料理」
            infoInDto.setM2Q(ReportUtil.nullToEmpty(entity.getM2Q()));
            // 好む活動と関与_パズル=API定義の処理「2.1」の「好む活動と関与_パズル」
            infoInDto.setM2R(ReportUtil.nullToEmpty(entity.getM2R()));
            // 好む活動と関与_その他1漢字=API定義の処理「2.1」の「好む活動と関与_その他1漢字」
            infoInDto.setM2SKnj(
                    ReportUtil.nullToEmpty(entity.getM2SKnj()).replaceAll("\\r?\\n|\\r", CommonConstants.BLANK_STRING));
            // 好む活動と関与_その他1=API定義の処理「2.1」の「好む活動と関与_その他1」
            infoInDto.setM2S(ReportUtil.nullToEmpty(entity.getM2S()));
            // 好む活動と関与_その他2漢字=API定義の処理「2.1」の「好む活動と関与_その他2漢字」
            infoInDto.setM2TKnj(
                    ReportUtil.nullToEmpty(entity.getM2TKnj()).replaceAll("\\r?\\n|\\r", CommonConstants.BLANK_STRING));
            // 好む活動と関与_その他2=API定義の処理「2.1」の「好む活動と関与_その他2」
            infoInDto.setM2T(ReportUtil.nullToEmpty(entity.getM2T()));
            // m3_メモ=API定義の処理「2.1」のm3_メモ
            infoInDto.setM3MemoKnj(ReportUtil.nullToEmpty(entity.getM3MemoKnj()));
            // m3_メモフォント
            infoInDto.setM3MemoFont(getMemoFont(entity.getM3MemoFont()));
            // m3_メモ色
            infoInDto.setM3MemoColor(getMemoColor(entity.getM3MemoColor(), colorFlg));
            // 日中寝ている時間=API定義の処理「2.1」の「 日中寝ている時間」
            infoInDto.setM3(ReportUtil.nullToEmpty(entity.getM3()));
            // m4_メモ=API定義の処理「2.1」のm4_メモ
            infoInDto.setM4MemoKnj(ReportUtil.nullToEmpty(entity.getM4MemoKnj()));
            // m4_メモフォント
            infoInDto.setM4MemoFont(getMemoFont(entity.getM4MemoFont()));
            // m4_メモ色
            infoInDto.setM4MemoColor(getMemoColor(entity.getM4MemoColor(), colorFlg));
            // 興味・関心_レクリエーション=API定義の処理「2.1」の「興味・関心_レクリエーション」
            infoInDto.setM4A(ReportUtil.nullToEmpty(entity.getM4A()));
            // 興味・関心_転倒予防=API定義の処理「2.1」の「興味・関心_転倒予防」
            infoInDto.setM4B(ReportUtil.nullToEmpty(entity.getM4B()));
            // 興味・関心_記憶力改善=API定義の処理「2.1」の「興味・関心_記憶力改善」
            infoInDto.setM4C(ReportUtil.nullToEmpty(entity.getM4C()));
            // 興味・関心_身体機能向上=API定義の処理「2.1」の興味・関心_身体機能向上
            infoInDto.setM4D(ReportUtil.nullToEmpty(entity.getM4D()));

        }

        // 指定日（年号）
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
        // 指定日（年）
        infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
        // 指定日（月）
        infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
        // 指定日（日）
        infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

        // 2.文書番号情報の取得
        /*
         * ===============共通関数補足 2.文書番号情報取得処理===============
         * 
         */
        // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01 関数名：GetF3GkProfile
        // を利用し、設定の読込（システム環境以外の設定）情報を取得する。
        // 【リクエストパラメータ】.システムコード
        String syscd = model.getSyscd();
        // 職員ID←リクエストパラメータ.データ.印刷対象履歴リスト[0].出力帳票印刷情報リスト[0].職員ID
        String shokuId = printSubjectHistoryList.get(0).getChoPrtList().get(0).getShokuId();
        // セクション←リクエストパラメータ.データ.出力帳票印刷情報リスト[0].セクション
        String section = printSubjectHistoryList.get(0).getChoPrtList().get(0).getSection();
        // サービス事業者ID←リクエストパラメータ.事業者情報.事業者ID
        String svJigyoId = model.getJigyoInfo().getSvJigyoId();

        String bunsyoKanriNo = this.getBunsyoKanriNo(syscd, shokuId, section, svJigyoId);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(ReportUtil.nullToEmpty(bunsyoKanriNo));

        return infoInDto;

    }

    /**
     * U06080_アセスメント表（Ｎ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetNReportServiceInDto getNReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {

        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        // infoInDtoを設定する。
        AssessmentSheetNReportServiceInDto infoInDto = new AssessmentSheetNReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();

        // 帳票タイトル "インターライ方式ケアアセスメント表"
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 指定日印刷区分 ← リクエストパラメータ.データ.印刷オプション.指定日印刷区分
        infoInDto.setShiTeiKubun(Integer.valueOf(printSet.getShiTeiKubun()));
        // 事業者名 リクエストパラメータ.データ.事業者名
        infoInDto.setJigyousha(model.getSvJigyoKnj());

        /*
         * ===============2. リクエストパラメータ.記入用シートを印刷するフラグ = true の場合、
         * 結果レスポンスを返却する。===============
         * 
         */
        Boolean isEmptyFlg = false;
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg()) ||
                CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            isEmptyFlg = true;
        }
        // 記入用シートを印刷するフラグ リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(isEmptyFlg);
        // リクエストパラメータ.記入用シートを印刷するフラグ = true の場合
        if (isEmptyFlg) {

            infoInDto = this.getNDefReportParams(model, outDto, infoInDto);

            /*
             * ===============リクエストパラメータ.記入用シートを印刷するフラグ = false の場合、
             * アセスメント表（Ｉ）情報の取得。===============
             * 
             */
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getNReportParamsFromAssN(model, outDto, infoInDto);
        }
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｎ）帳票パラメータ取得
     * 
     * @param model     入力データ
     * @param outDto    出力データ
     * @param infoInDto PDF帳票出力詳細データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetNReportServiceInDto getNDefReportParams(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto, AssessmentSheetNReportServiceInDto infoInDto) throws Exception {
        // 印刷オプション

        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();

        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType())));
        // 調査アセスメント種別 ← リクエストパラメータ.データ.印刷オプション.記入用シートアセスメント種別
        infoInDto.setAssType(CommonDtoUtil.strValToInt(model.getPrintOption().getKinyuAssType()));
        // 指定日（年号）=" "
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）=" "
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）=" "
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）=" "
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）=""
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）=""
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // n1_メモ=""
        infoInDto.setN1MemoKnj(CommonConstants.BLANK_STRING);
        // n1_メモフォント="12"
        infoInDto.setN1MemoFont(CommonConstants.STR_12);
        // n1_メモ色=""
        infoInDto.setN1MemoColor(CommonConstants.BLANK_STRING);
        // n2_メモ=""
        infoInDto.setN2MemoKnj(CommonConstants.BLANK_STRING);
        // n2_メモフォント="12"
        infoInDto.setN2MemoFont(CommonConstants.STR_12);
        // n2_メモ色=""
        infoInDto.setN2MemoColor(CommonConstants.BLANK_STRING);
        // 薬のアレルギー=""
        infoInDto.setN2(CommonConstants.BLANK_STRING);
        // n3_メモ=""
        infoInDto.setN3MemoKnj(CommonConstants.BLANK_STRING);
        // n3_メモフォント=""
        infoInDto.setN3MemoFont(CommonConstants.STR_12);
        // n3_メモ色=""
        infoInDto.setN3MemoColor(CommonConstants.BLANK_STRING);
        // 処方薬の順守=""
        infoInDto.setN3(CommonConstants.BLANK_STRING);
        // 文書管理番号=""
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        // 薬剤リスト
        // 15行の空データを追加する
        List<YaKuzaiList> yaKuzaiList = new ArrayList<YaKuzaiList>();
        List<YaKuzaiList> emptyYaKuzaiList = Collections.nCopies(CommonConstants.NUMBER_15, new YaKuzaiList());
        yaKuzaiList.addAll(emptyYaKuzaiList);
        JRBeanCollectionDataSource wkYaKuzaiList = new JRBeanCollectionDataSource(yaKuzaiList);
        infoInDto.setYaKuzaiList(wkYaKuzaiList);

        return infoInDto;

    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｎ）帳票パラメータ取得
     * 
     * @param model     入力データ
     * @param outDto    出力データ
     * @param infoInDto PDF帳票出力詳細データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetNReportServiceInDto getNReportParamsFromAssN(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto, AssessmentSheetNReportServiceInDto infoInDto) throws Exception {
        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();

        // アセスメント表（Ｎ）情報の取得。
        // アセスメント表（Ｎ）データ情報取得のINPUT情報初期化
        KghCpnRaiAssNDataByCriteriaInEntity kghCpnRaiAssNDataByCriteriaInEntity = new KghCpnRaiAssNDataByCriteriaInEntity();
        // アセスメントID←リクエストパラメータ.データ.印刷対象履歴リスト[0].アセスメントID
        kghCpnRaiAssNDataByCriteriaInEntity
                .setAnRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
        // 2.1. 下記のアセスメント表（Ｎ）情報取得のDAOを利用し、アセスメント表（Ｎ）データ情報を取得し、薬剤情報リストを作成する。
        List<KghCpnRaiAssNDataOutEntity> kghCpnRaiAssNDataOutEntityList = kghCpnRaiAssNDataSelectMapper
                .findKghCpnRaiAssNDataByCriteria(kghCpnRaiAssNDataByCriteriaInEntity);
        // 取得したアセスメント表(I)データ情報から薬剤情報リストの作成について、
        // レコード数 = (int) Math.ceil((double) 取得した件数 / 15) * 15
        // に作成する、レコード数を空のデータで追加して、補足する。
        if (CollectionUtils.isNotEmpty(kghCpnRaiAssNDataOutEntityList)) {
            int recordCount = (int) Math.ceil((double) kghCpnRaiAssNDataOutEntityList.size() / CommonConstants.NUMBER_15) * CommonConstants.NUMBER_15;

            for (int i = kghCpnRaiAssNDataOutEntityList.size(); i < recordCount; i++) {
                kghCpnRaiAssNDataOutEntityList.add(new KghCpnRaiAssNDataOutEntity());
            }
        } else {
            // 薬剤情報リスト
            // レコード数 = (int) Math.ceil((double) 取得した件数 / 15) * 15
            // に作成する、レコード数を空のデータで追加して、補足する。
            kghCpnRaiAssNDataOutEntityList = Collections.nCopies(CommonConstants.NUMBER_15, new KghCpnRaiAssNDataOutEntity());
        }

        // 薬剤分類マスタ情報取得のINPUT情報初期化
        DrugClassificationMasterInfoByCriteriaInEntity drugClassificationMasterInfoByCriteriaInEntity = new DrugClassificationMasterInfoByCriteriaInEntity();
        // 2.2. 下記のアセスメント表（Ｎ）情報取得のDAOを利用し、アセスメント表（Ｎ）薬剤分類リスト情報を取得する。
        List<DrugClassificationMasterInfoOutEntity> drugClassificationMasterInfoOutEntityList = this.drugClassificationMasterInfoSelectMapper
                .findDrugClassificationMasterInfoByCriteria(drugClassificationMasterInfoByCriteriaInEntity);

        // 2.3. 「2.1.」で取得したアセスメント表（Ｎ）情報の件数をワークアセスメント表（Ｎ）情報.総件数に設定する。
        // 「2.1.」で取得したアセスメント表（N）情報の件数.総件数 > 0 件の場合，
        if (CollectionUtils.isNotEmpty(kghCpnRaiAssNDataOutEntityList)) {
            // ワークアセスメント表（N）情報の一件目を取得する
            KghCpnRaiAssNDataOutEntity entity = kghCpnRaiAssNDataOutEntityList.get(0);
            // 薬剤情報リスト
            List<YaKuzaiList> yaKuzaiList = new ArrayList<YaKuzaiList>();
            // 2.4. 「2.1」で取得したアセスメント表（Ｎ）薬剤情報リストのレコード単位で繰り返し、
            // 『「2.1」で取得したアセスメント表（Ｎ）薬剤情報リスト.薬剤名=「2.2」で取得したアセスメント表（Ｎ）薬剤分類リスト.薬剤id』の条件で「医薬品名」を取得し薬剤リストに設定する。
            // 取得できない場合は薬剤名に""で設定する
            for (KghCpnRaiAssNDataOutEntity kghCpnRaiAssNDataOutEntityInfo : kghCpnRaiAssNDataOutEntityList) {
                YaKuzaiList yaKuzaiListInfo = new YaKuzaiList();
                String foundDrugName = CommonConstants.BLANK_STRING;
                if (!Objects.isNull(kghCpnRaiAssNDataOutEntityInfo.getN1A())) {
                    List<DrugClassificationMasterInfoOutEntity> DrugClassificationMasterInfoOutEntities = drugClassificationMasterInfoOutEntityList
                            .stream().filter(item -> item.getDrugId() == kghCpnRaiAssNDataOutEntityInfo.getN1A())
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(DrugClassificationMasterInfoOutEntities)) {
                        foundDrugName = DrugClassificationMasterInfoOutEntities.getFirst().getDrugKnj();
                    }
                }
                // 薬剤名
                yaKuzaiListInfo
                        .setDrugKnj(foundDrugName);
                // API定義の処理「2.1.」薬剤情報リストの一日量
                yaKuzaiListInfo
                        .setN1B(ReportUtil.nullToEmpty(kghCpnRaiAssNDataOutEntityInfo.getN1B()).replace(
                                ReportConstants.DECIMAL_POINT + CommonConstants.STR_0, CommonConstants.BLANK_STRING));
                // API定義の処理「2.1.」薬剤情報リストの単位
                yaKuzaiListInfo
                        .setN1CKnj(ReportUtil.nullToEmpty(kghCpnRaiAssNDataOutEntityInfo.getN1CKnj()));
                // API定義の処理「2.1.」薬剤情報リストの経路
                yaKuzaiListInfo
                        .setN1D(ReportUtil.nullToEmpty(kghCpnRaiAssNDataOutEntityInfo.getN1D()));
                // API定義の処理「2.1.」薬剤情報リストの頻度
                if (CommonConstants.STR_0.equals(ReportUtil.nullToEmpty(kghCpnRaiAssNDataOutEntityInfo.getN1F()))) {
                    yaKuzaiListInfo
                            .setN1E(ReportUtil.nullToEmpty(kghCpnRaiAssNDataOutEntityInfo.getN1E())
                                    + CommonConstants.SPACE_STRING + ReportConstants.STR_DELIMITER +
                                    ReportConstants.DATE_DAY);
                } else {
                    yaKuzaiListInfo
                            .setN1E(ReportUtil.nullToEmpty(kghCpnRaiAssNDataOutEntityInfo.getN1E()));
                }
                // API定義の処理「2.1.」薬剤情報リストの頓用
                yaKuzaiListInfo
                        .setN1F(ReportUtil.nullToEmpty(kghCpnRaiAssNDataOutEntityInfo.getN1F()));
                yaKuzaiList.add(yaKuzaiListInfo);
            }

            JRBeanCollectionDataSource wkyaKuzaiList = new JRBeanCollectionDataSource(yaKuzaiList);
            infoInDto.setYaKuzaiList(wkyaKuzaiList);

            /*
             * ===============3. 利用者基本情報を取得する。===============
             * 
             */
            // 3.1. 利用者基本情報の取得
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());
            // 氏名（姓）
            infoInDto.setName1Knj(ReportUtil.nullToEmpty(userPair.getLeft()));
            // 氏名（名）
            infoInDto.setName2Knj(ReportUtil.nullToEmpty(userPair.getRight()));

            /*
             * ===============4. アセスメント種別情報を取得する===============
             * 
             */
            Pair<Integer, String> syubetuPair = this
                    .getSyubetuByRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
            // 調査アセスメント種別名
            infoInDto.setSyubetuKnj(syubetuPair.getRight());
            // 調査アセスメント種別
            infoInDto.setAssType(syubetuPair.getLeft());

            /*
             * ===============5.上記編集した帳票用データを設定する。===============
             * 
             */

            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            Boolean colorFlg = false;
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg()) ||
                    CommonConstants.STR_1.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }
            // n1_メモ
            infoInDto.setN1MemoKnj(ReportUtil.nullToEmpty(entity.getN1MemoKnj()));

            // n1_メモフォント
            infoInDto.setN1MemoFont(getMemoFont(entity.getN1MemoFont()));

            // n1_メモ色
            infoInDto.setN1MemoColor(getMemoColor(entity.getN1MemoColor(), colorFlg));

            // n2_メモ
            infoInDto.setN2MemoKnj(ReportUtil.nullToEmpty(entity.getN2MemoKnj()));

            // n2_メモフォント
            infoInDto.setN2MemoFont(getMemoFont(entity.getN2MemoFont()));

            // n2_メモ色
            infoInDto.setN2MemoColor(getMemoColor(entity.getN2MemoColor(), colorFlg));

            // 薬のアレルギー
            infoInDto.setN2(ReportUtil.nullToEmpty(entity.getN2()));

            // n3_メモ
            infoInDto.setN3MemoKnj(ReportUtil.nullToEmpty(entity.getN3MemoKnj()));

            // n3_メモフォント
            infoInDto.setN3MemoFont(getMemoFont(entity.getN3MemoFont()));

            // n3_メモ色
            infoInDto.setN3MemoColor(getMemoColor(entity.getN3MemoColor(), colorFlg));

            // 処方薬の順守
            infoInDto.setN3(ReportUtil.nullToEmpty(entity.getN3()));
        }

        // 指定日（年号）
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
        // 指定日（年）
        infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
        // 指定日（月）
        infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
        // 指定日（日）
        infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

        // 2.文書番号情報の取得
        /*
         * ===============共通関数補足 2.文書番号情報取得処理===============
         * 
         */
        // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01 関数名：GetF3GkProfile
        // を利用し、設定の読込（システム環境以外の設定）情報を取得する。
        // 【リクエストパラメータ】.システムコード
        String syscd = model.getSyscd();
        // 職員ID←リクエストパラメータ.データ.印刷対象履歴リスト[0].出力帳票印刷情報リスト[0].職員ID
        String shokuId = printSubjectHistoryList.get(0).getChoPrtList().get(0).getShokuId();
        // セクション←リクエストパラメータ.データ.出力帳票印刷情報リスト[0].セクション
        String section = printSubjectHistoryList.get(0).getChoPrtList().get(0).getSection();
        // サービス事業者ID←リクエストパラメータ.事業者情報.事業者ID
        String svJigyoId = model.getJigyoInfo().getSvJigyoId();

        String bunsyoKanriNo = this.getBunsyoKanriNo(syscd, shokuId, section, svJigyoId);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(ReportUtil.nullToEmpty(bunsyoKanriNo));

        return infoInDto;

    }

    /**
     * U06080_アセスメント表（Ｏ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetOReportServiceInDto getOReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        AssessmentSheetOReportServiceInDto infoInDto = new AssessmentSheetOReportServiceInDto();

        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、アセスメント表（Ｏ）情報の取得。===============
         * 
         */
        // 【リクエストパラメータ】.事業者名
        String svJigyoKnj = model.getSvJigyoKnj();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            infoInDto = this.getODefReportParams(printOption.getKinyuAssType());
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.TRUE);
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getOReportParamsFromAssO(model, outDto);
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.FALSE);
        }
        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // 事業者名
        infoInDto.setJigyousha(ReportUtil.nullToEmpty(svJigyoKnj));
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｏ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetOReportServiceInDto getODefReportParams(String kinyuAssType) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetOReportServiceInDto infoInDto = new AssessmentSheetOReportServiceInDto();
        // 調査アセスメント種別
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(kinyuAssType)));
        // 指定日（年号）=""
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）=""
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）=""
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）=""
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // o1_メモ
        infoInDto.setO1MemoKnj(CommonConstants.BLANK_STRING);
        // o1_メモフォント
        infoInDto.setO1MemoFont(CommonConstants.STR_12);
        // o1_メモ色
        infoInDto.setO1MemoColor(CommonConstants.BLANK_STRING);
        // 健診・予防接種_血圧
        infoInDto.setO1A(CommonConstants.BLANK_STRING);
        // 健診・予防接種_大腸内視鏡
        infoInDto.setO1B(CommonConstants.BLANK_STRING);
        // 健診・予防接種_歯科
        infoInDto.setO1C(CommonConstants.BLANK_STRING);
        // 健診・予防接種_眼科
        infoInDto.setO1D(CommonConstants.BLANK_STRING);
        // 健診・予防接種_聴力
        infoInDto.setO1E(CommonConstants.BLANK_STRING);
        // 健診・予防接種_インフルエンザ
        infoInDto.setO1F(CommonConstants.BLANK_STRING);
        // 健診・予防接種_マンモグラフィー
        infoInDto.setO1G(CommonConstants.BLANK_STRING);
        // 健診・予防接種_肺炎
        infoInDto.setO1H(CommonConstants.BLANK_STRING);
        // o2_メモ
        infoInDto.setO2MemoKnj(CommonConstants.BLANK_STRING);
        // o2_メモフォント
        infoInDto.setO2MemoFont(CommonConstants.STR_12);
        // o2_メモ色
        infoInDto.setO2MemoColor(CommonConstants.BLANK_STRING);
        // 治療・ケア_治療_抗がん剤
        infoInDto.setO2A(CommonConstants.BLANK_STRING);
        // 治療・ケア_治療_透析
        infoInDto.setO2B(CommonConstants.BLANK_STRING);
        // 治療・ケア_治療_感染
        infoInDto.setO2C(CommonConstants.BLANK_STRING);
        // 治療・ケア_治療_薬物投与
        infoInDto.setO2D(CommonConstants.BLANK_STRING);
        // 治療・ケア_治療_酸素
        infoInDto.setO2E(CommonConstants.BLANK_STRING);
        // 治療・ケア_治療_放射線
        infoInDto.setO2F(CommonConstants.BLANK_STRING);
        // 治療・ケア_治療_吸引
        infoInDto.setO2G(CommonConstants.BLANK_STRING);
        // 治療・ケア_治療_気管切開口
        infoInDto.setO2H(CommonConstants.BLANK_STRING);
        // 治療・ケア_治療_輸血
        infoInDto.setO2I(CommonConstants.BLANK_STRING);
        // 治療・ケア_治療_人工呼吸器
        infoInDto.setO2J(CommonConstants.BLANK_STRING);
        // 治療・ケア_治療_創のケア
        infoInDto.setO2K(CommonConstants.BLANK_STRING);
        // 治療・ケア_プログラム_トイレ
        infoInDto.setO2L(CommonConstants.BLANK_STRING);
        // 治療・ケア_プログラム_緩和
        infoInDto.setO2M(CommonConstants.BLANK_STRING);
        // 治療・ケア_プログラム_体位
        infoInDto.setO2N(CommonConstants.BLANK_STRING);
        // o3_メモ
        infoInDto.setO3MemoKnj(CommonConstants.BLANK_STRING);
        // o3_メモフォント
        infoInDto.setO3MemoFont(CommonConstants.STR_12);
        // o3_メモ色
        infoInDto.setO3MemoColor(CommonConstants.BLANK_STRING);
        // 訪問介護_実施日数(A)
        infoInDto.setO3AA(CommonConstants.BLANK_STRING);
        // 訪問介護_分数(B)
        infoInDto.setO3AB(CommonConstants.BLANK_STRING);
        // 訪問看護_実施日数(A)
        infoInDto.setO3BA(CommonConstants.BLANK_STRING);
        // 訪問看護_分数(B)
        infoInDto.setO3BB(CommonConstants.BLANK_STRING);
        // 通所介護・リハ_実施日数(A)
        infoInDto.setO3CA(CommonConstants.BLANK_STRING);
        // 食事/配食_実施日数(A)
        infoInDto.setO3DA(CommonConstants.BLANK_STRING);
        // o4_メモ
        infoInDto.setO4MemoKnj(CommonConstants.BLANK_STRING);
        // o4_メモフォント
        infoInDto.setO4MemoFont(CommonConstants.STR_12);
        // o4_メモ色
        infoInDto.setO4MemoColor(CommonConstants.BLANK_STRING);
        // リハ_理学_計画日数(A)
        infoInDto.setO4AA(CommonConstants.BLANK_STRING);
        // リハ_理学_実施日数(B)
        infoInDto.setO4AB(CommonConstants.BLANK_STRING);
        // リハ_理学_分数(C)
        infoInDto.setO4AC(CommonConstants.BLANK_STRING);
        // リハ_作業_計画日数(A)
        infoInDto.setO4BA(CommonConstants.BLANK_STRING);
        // リハ_作業_実施日数(B)
        infoInDto.setO4BB(CommonConstants.BLANK_STRING);
        // リハ_作業_分数(C)
        infoInDto.setO4BC(CommonConstants.BLANK_STRING);
        // リハ_言語_計画日数(A)
        infoInDto.setO4CA(CommonConstants.BLANK_STRING);
        // リハ_言語_実施日数(B)
        infoInDto.setO4CB(CommonConstants.BLANK_STRING);
        // リハ_言語_分数(C)
        infoInDto.setO4CC(CommonConstants.BLANK_STRING);
        // リハ_心理_計画日数(A)
        infoInDto.setO4DA(CommonConstants.BLANK_STRING);
        // リハ_心理_実施日数(B)
        infoInDto.setO4DB(CommonConstants.BLANK_STRING);
        // リハ_心理_分数(C)
        infoInDto.setO4DC(CommonConstants.BLANK_STRING);
        // リハ_呼吸_計画日数(A)
        infoInDto.setO4EA(CommonConstants.BLANK_STRING);
        // リハ_呼吸_実施日数(B)
        infoInDto.setO4EB(CommonConstants.BLANK_STRING);
        // リハ_呼吸_分数(C)
        infoInDto.setO4EC(CommonConstants.BLANK_STRING);
        // リハ_訓練_計画日数(A)
        infoInDto.setO4FA(CommonConstants.BLANK_STRING);
        // リハ_訓練_実施日数(B)
        infoInDto.setO4FB(CommonConstants.BLANK_STRING);
        // リハ_訓練_分数(C)
        infoInDto.setO4FC(CommonConstants.BLANK_STRING);
        // o5_メモ
        infoInDto.setO5MemoKnj(CommonConstants.BLANK_STRING);
        // o5_メモフォント
        infoInDto.setO5MemoFont(CommonConstants.STR_12);
        // o5_メモ色
        infoInDto.setO5MemoColor(CommonConstants.BLANK_STRING);
        // 入院
        infoInDto.setO5A(CommonConstants.BLANK_STRING);
        // 救急外来
        infoInDto.setO5B(CommonConstants.BLANK_STRING);
        // 医師の診察
        infoInDto.setO5C(CommonConstants.BLANK_STRING);
        // o6_メモ
        infoInDto.setO6MemoKnj(CommonConstants.BLANK_STRING);
        // o6_メモフォント
        infoInDto.setO6MemoFont(CommonConstants.STR_12);
        // o6_メモ色
        infoInDto.setO6MemoColor(CommonConstants.BLANK_STRING);
        // 受診
        infoInDto.setO6(CommonConstants.BLANK_STRING);
        // o7_メモ
        infoInDto.setO7MemoKnj(CommonConstants.BLANK_STRING);
        // o7_メモフォント
        infoInDto.setO7MemoFont(CommonConstants.STR_12);
        // o7_メモ色
        infoInDto.setO7MemoColor(CommonConstants.BLANK_STRING);
        // 医師の指示変更
        infoInDto.setO7(CommonConstants.BLANK_STRING);
        // o8_メモ
        infoInDto.setO8MemoKnj(CommonConstants.BLANK_STRING);
        // o8_メモフォント
        infoInDto.setO8MemoFont(CommonConstants.STR_12);
        // o8_メモ色
        infoInDto.setO8MemoColor(CommonConstants.BLANK_STRING);
        // 身体抑制
        infoInDto.setO8A(CommonConstants.BLANK_STRING);
        // 身体抑制_ベッド柵
        infoInDto.setO8B(CommonConstants.BLANK_STRING);
        // 身体抑制_体幹部
        infoInDto.setO8C(CommonConstants.BLANK_STRING);
        // 身体抑制_立ち上がり
        infoInDto.setO8D(CommonConstants.BLANK_STRING);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｏ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetOReportServiceInDto getOReportParamsFromAssO(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {

        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();

        // 帳票用データ詳細
        AssessmentSheetOReportServiceInDto infoInDto = new AssessmentSheetOReportServiceInDto();

        // アセスメント表（Ｏ）情報取得のDAOを利用
        KghCpnRaiAssOPByCriteriaInEntity kghCpnRaiAssOPByCriteriaInEntity = new KghCpnRaiAssOPByCriteriaInEntity();
        // アセスメントID
        kghCpnRaiAssOPByCriteriaInEntity.setAnRaiId(
                CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
        // アセスメント表（Ｏ）情報を取得する
        List<KghCpnRaiAssOPOutEntity> kghCpnRaiAssOPOutEntity = this.raiRrkKghCpnRaiAssOSelectMapper
                .findKghCpnRaiAssOPByCriteria(kghCpnRaiAssOPByCriteriaInEntity);
        // 「2.1.」で取得したアセスメント表（Ｏ）情報の件数をワークアセスメント表（Ｏ）情報.総件数に設定する
        // 2.2.1. 総件数 > 0 件の場合
        if (CollectionUtils.isNotEmpty(kghCpnRaiAssOPOutEntity)) {
            KghCpnRaiAssOPOutEntity entity = kghCpnRaiAssOPOutEntity.get(0);
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ
            Boolean colorFlg = false;
            if (ReportConstants.COLOR_FLG_TRUE.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }
            // o1_メモ
            infoInDto.setO1MemoKnj(ReportUtil.nullToEmpty(entity.getO1MemoKnj()));
            // API定義の処理「2.1」のo1_メモフォント=nullの場合、o1_メモフォント="12"
            // 上記以外の場合、o1_メモフォント=API定義の処理「2.1」のo1_メモフォント
            infoInDto.setO1MemoFont(getMemoFont(entity.getO1MemoFont()));
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            // API定義の処理「2.1」のo1_メモ色=nullの場合
            // o1_メモ色＝"#000000"
            // 上記以外の場合
            // o1_メモ色=共通関数補足の「3.1」の十六進数RGB値
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= false の場合
            // o1_メモ色＝"#000000"
            infoInDto.setO1MemoColor(getMemoColor(entity.getO2MemoColor(), colorFlg));
            // 健診・予防接種_血圧
            infoInDto.setO1A(ReportUtil.nullToEmpty(entity.getO1A()));
            // 健診・予防接種_大腸内視鏡
            infoInDto.setO1B(ReportUtil.nullToEmpty(entity.getO1B()));
            // 健診・予防接種_歯科
            infoInDto.setO1C(ReportUtil.nullToEmpty(entity.getO1C()));
            // 健診・予防接種_眼科
            infoInDto.setO1D(ReportUtil.nullToEmpty(entity.getO1D()));
            // 健診・予防接種_聴力
            infoInDto.setO1E(ReportUtil.nullToEmpty(entity.getO1E()));
            // 健診・予防接種_インフルエンザ
            infoInDto.setO1F(ReportUtil.nullToEmpty(entity.getO1F()));
            // 健診・予防接種_マンモグラフィー
            infoInDto.setO1G(ReportUtil.nullToEmpty(entity.getO1G()));
            // 健診・予防接種_肺炎
            infoInDto.setO1H(ReportUtil.nullToEmpty(entity.getO1H()));
            // o2_メモ=API定義の処理「2.1」のo2_メモ
            infoInDto.setO2MemoKnj(ReportUtil.nullToEmpty(entity.getO2MemoKnj()));
            // API定義の処理「2.1」のo2_メモフォント=nullの場合、o2_メモフォント="12"
            // 上記以外の場合、o2_メモフォント=API定義の処理「2.1」のo2_メモフォント
            infoInDto.setO2MemoFont(getMemoFont(entity.getO2MemoFont()));
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            // API定義の処理「2.1」のo2_メモ色=nullの場合
            // o2_メモ色＝"#000000"
            // 上記以外の場合、
            // o2_メモ色=共通関数補足の「3.1」の十六進数RGB値
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= false の場合
            // o2_メモ色＝"#000000"
            infoInDto.setO2MemoColor(getMemoColor(entity.getO2MemoColor(), colorFlg));
            // 治療・ケア_治療_抗がん剤
            infoInDto.setO2A(ReportUtil.nullToEmpty(entity.getO2A()));
            // 治療・ケア_治療_透析
            infoInDto.setO2B(ReportUtil.nullToEmpty(entity.getO2B()));
            // 治療・ケア_治療_感染
            infoInDto.setO2C(ReportUtil.nullToEmpty(entity.getO2C()));
            // 治療・ケア_治療_薬物投与
            infoInDto.setO2D(ReportUtil.nullToEmpty(entity.getO2D()));
            // 治療・ケア_治療_酸素
            infoInDto.setO2E(ReportUtil.nullToEmpty(entity.getO2E()));
            // 治療・ケア_治療_放射線
            infoInDto.setO2F(ReportUtil.nullToEmpty(entity.getO2F()));
            // 治療・ケア_治療_吸引
            infoInDto.setO2G(ReportUtil.nullToEmpty(entity.getO2G()));
            // 治療・ケア_治療_気管切開口
            infoInDto.setO2H(ReportUtil.nullToEmpty(entity.getO2H()));
            // 治療・ケア_治療_輸血
            infoInDto.setO2I(ReportUtil.nullToEmpty(entity.getO2I()));
            // 治療・ケア_治療_人工呼吸器
            infoInDto.setO2J(ReportUtil.nullToEmpty(entity.getO2J()));
            // 治療・ケア_治療_創のケア
            infoInDto.setO2K(ReportUtil.nullToEmpty(entity.getO2K()));
            // 治療・ケア_プログラム_トイレ
            infoInDto.setO2L(ReportUtil.nullToEmpty(entity.getO2L()));
            // 治療・ケア_プログラム_緩和
            infoInDto.setO2M(ReportUtil.nullToEmpty(entity.getO2M()));
            // 治療・ケア_プログラム_体位
            infoInDto.setO2N(ReportUtil.nullToEmpty(entity.getO2N()));
            // o3_メモ=API定義の処理「2.1」のo3_メモ
            infoInDto.setO3MemoKnj(ReportUtil.nullToEmpty(entity.getO3MemoKnj()));
            // API定義の処理「2.1」のo3_メモフォント=nullの場合、o3_メモフォント="12"
            // 上記以外の場合、o3_メモフォント=API定義の処理「2.1」のo3_メモフォント
            infoInDto.setO3MemoFont(getMemoFont(entity.getO3MemoFont()));
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            // API定義の処理「2.1」のo3_メモ色=nullの場合
            // o3_メモ色＝"#000000"
            // 上記以外の場合、
            // o3_メモ色=共通関数補足の「3.1」の十六進数RGB値
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= false の場合
            // o3_メモ色＝"#000000"
            infoInDto.setO3MemoColor(getMemoColor(entity.getO3MemoColor(), colorFlg));
            // 訪問介護_実施日数(A)
            infoInDto.setO3AA(ReportUtil.nullToEmpty(entity.getO3AA()));
            // 訪問介護_分数(B)
            infoInDto.setO3AB(ReportUtil.nullToEmpty(entity.getO3AB()));
            // 訪問看護_実施日数(A)
            infoInDto.setO3BA(ReportUtil.nullToEmpty(entity.getO3BA()));
            // 訪問看護_分数(B)
            infoInDto.setO3BB(ReportUtil.nullToEmpty(entity.getO3BB()));
            // 通所介護・リハ_実施日数(A)
            infoInDto.setO3CA(ReportUtil.nullToEmpty(entity.getO3CA()));
            // 食事/配食_実施日数(A)
            infoInDto.setO3DA(ReportUtil.nullToEmpty(entity.getO3DA()));
            // o4_メモ=API定義の処理「2.1」のo4_メモ
            infoInDto.setO4MemoKnj(ReportUtil.nullToEmpty(entity.getO4MemoKnj()));
            // API定義の処理「2.1」のo4_メモフォント=nullの場合、o4_メモフォント="12"
            // 上記以外の場合、o4_メモフォント=API定義の処理「2.1」のo4_メモフォント
            infoInDto.setO4MemoFont(getMemoFont(entity.getO4MemoFont()));
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            // API定義の処理「2.1」のo4_メモ色=nullの場合
            // o4_メモ色＝"#000000"
            // 上記以外の場合、
            // o4_メモ色=共通関数補足の「3.1」の十六進数RGB値
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= false の場合
            // o4_メモ色＝"#000000"
            infoInDto.setO4MemoColor(getMemoColor(entity.getO4MemoColor(), colorFlg));
            // リハ_理学_計画日数(A)
            infoInDto.setO4AA(ReportUtil.nullToEmpty(entity.getO4AA()));
            // リハ_理学_実施日数(B)
            infoInDto.setO4AB(ReportUtil.nullToEmpty(entity.getO4AB()));
            // リハ_理学_分数(C)
            infoInDto.setO4AC(ReportUtil.nullToEmpty(entity.getO4AC()));
            // リハ_作業_計画日数(A)
            infoInDto.setO4BA(ReportUtil.nullToEmpty(entity.getO4BA()));
            // リハ_作業_実施日数(B)
            infoInDto.setO4BB(ReportUtil.nullToEmpty(entity.getO4BA()));
            // リハ_作業_分数(C)
            infoInDto.setO4BC(ReportUtil.nullToEmpty(entity.getO4BC()));
            // リハ_言語_計画日数(A)
            infoInDto.setO4CA(ReportUtil.nullToEmpty(entity.getO4CA()));
            // リハ_言語_実施日数(B)
            infoInDto.setO4CB(ReportUtil.nullToEmpty(entity.getO4CB()));
            // リハ_言語_分数(C)
            infoInDto.setO4CC(ReportUtil.nullToEmpty(entity.getO4CC()));
            // リハ_心理_計画日数(A)
            infoInDto.setO4DA(ReportUtil.nullToEmpty(entity.getO4DA()));
            // リハ_心理_実施日数(B)
            infoInDto.setO4DB(ReportUtil.nullToEmpty(entity.getO4DB()));
            // リハ_心理_分数(C)
            infoInDto.setO4DC(ReportUtil.nullToEmpty(entity.getO4DC()));
            // リハ_呼吸_計画日数(A)
            infoInDto.setO4EA(ReportUtil.nullToEmpty(entity.getO4EA()));
            // リハ_呼吸_実施日数(B)
            infoInDto.setO4EB(ReportUtil.nullToEmpty(entity.getO4EB()));
            // リハ_呼吸_分数(C)
            infoInDto.setO4EC(ReportUtil.nullToEmpty(entity.getO4EC()));
            // リハ_訓練_計画日数(A)
            infoInDto.setO4FA(ReportUtil.nullToEmpty(entity.getO4FA()));
            // リハ_訓練_実施日数(B)
            infoInDto.setO4FB(ReportUtil.nullToEmpty(entity.getO4FB()));
            // リハ_訓練_分数(C)
            infoInDto.setO4FC(ReportUtil.nullToEmpty(entity.getO4FC()));
            // o5_メモ=API定義の処理「2.1」のo5_メモ
            infoInDto.setO5MemoKnj(ReportUtil.nullToEmpty(entity.getO5MemoKnj()));
            // API定義の処理「2.1」のo5_メモフォント=nullの場合、o5_メモフォント="12"
            // 上記以外の場合、o5_メモフォント=API定義の処理「2.1」のo5_メモフォント
            infoInDto.setO5MemoFont(getMemoFont(entity.getO5MemoFont()));
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            // API定義の処理「2.1」のo5_メモ色=nullの場合
            // o5_メモ色＝"#000000"
            // 上記以外の場合、
            // o5_メモ色=共通関数補足の「3.1」の十六進数RGB値
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= false の場合
            // o5_メモ色＝"#000000"
            infoInDto.setO5MemoColor(getMemoColor(entity.getO5MemoColor(), colorFlg));
            // 入院
            infoInDto.setO5A(ReportUtil.nullToEmpty(entity.getO5A()));
            // 救急外来
            infoInDto.setO5B(ReportUtil.nullToEmpty(entity.getO5B()));
            // 医師の診察
            infoInDto.setO5C(ReportUtil.nullToEmpty(entity.getO5C()));
            // o6_メモ=API定義の処理「2.1」のo6_メモ
            infoInDto.setO6MemoKnj(ReportUtil.nullToEmpty(entity.getO6MemoKnj()));
            // API定義の処理「2.1」のo6_メモフォント=nullの場合、o6_メモフォント="12"
            // 上記以外の場合、o6_メモフォント=API定義の処理「2.1」のo6_メモフォント
            infoInDto.setO6MemoFont(getMemoFont(entity.getO6MemoFont()));
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            // API定義の処理「2.1」のo6_メモ色=nullの場合
            // o6_メモ色＝"#000000"
            // 上記以外の場合、
            // o6_メモ色=共通関数補足の「3.1」の十六進数RGB値
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= false の場合
            // o6_メモ色＝"#000000"
            infoInDto.setO6MemoColor(getMemoColor(entity.getO6MemoColor(), colorFlg));
            // 受診
            infoInDto.setO6(ReportUtil.nullToEmpty(entity.getO6()));
            // o7_メモ=API定義の処理「2.1」のo7_メモ
            infoInDto.setO7MemoKnj(ReportUtil.nullToEmpty(entity.getO7MemoKnj()));
            // API定義の処理「2.1」のo7_メモフォント=nullの場合、o7_メモフォント="12"
            // 上記以外の場合、o7_メモフォント=API定義の処理「2.1」のo7_メモフォント
            infoInDto.setO7MemoFont(getMemoFont(entity.getO7MemoFont()));
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            // API定義の処理「2.1」のo7_メモ色=nullの場合
            // o7_メモ色＝"#000000"
            // 上記以外の場合、
            // o7_メモ色=共通関数補足の「3.1」の十六進数RGB値
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= false の場合
            // o7_メモ色＝"#000000"
            infoInDto.setO7MemoColor(getMemoColor(entity.getO7MemoColor(), colorFlg));
            // 医師の指示変更
            infoInDto.setO7(ReportUtil.nullToEmpty(entity.getO7()));
            // o8_メモ=API定義の処理「2.1」のo7_メモ
            infoInDto.setO8MemoKnj(ReportUtil.nullToEmpty(entity.getO8MemoKnj()));
            // API定義の処理「2.1」のo8_メモフォント=nullの場合、o8_メモフォント="12"
            // 上記以外の場合、o8_メモフォント=API定義の処理「2.1」のo8_メモフォント
            infoInDto.setO8MemoFont(getMemoFont(entity.getO8MemoFont()));
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            // API定義の処理「2.1」のo7_メモ色=nullの場合
            // o8_メモ色＝"#000000"
            // 上記以外の場合、
            // o8_メモ色=共通関数補足の「3.1」の十六進数RGB値
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= false の場合
            // o8_メモ色＝"#000000"
            infoInDto.setO8MemoColor(getMemoColor(entity.getO8MemoColor(), colorFlg));
            // 身体抑制
            infoInDto.setO8A(ReportUtil.nullToEmpty(entity.getO8A()));
            // 身体抑制_ベッド柵
            infoInDto.setO8B(ReportUtil.nullToEmpty(entity.getO8B()));
            // 身体抑制_体幹部
            infoInDto.setO8C(ReportUtil.nullToEmpty(entity.getO8C()));
            // 身体抑制_立ち上がり
            infoInDto.setO8D(ReportUtil.nullToEmpty(entity.getO8D()));

            /**
             * ===============3.利用者基本情報の取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());
            // 氏名（姓）
            infoInDto.setName1Knj(ReportUtil.nullToEmpty(userPair.getLeft()));
            // 氏名（名）
            infoInDto.setName2Knj(ReportUtil.nullToEmpty(userPair.getRight()));
        }

        /*
         * ===============4.アセスメント種別の取得===============
         * 
         */
        Pair<Integer, String> syubetuPair = this
                .getSyubetuByRaiId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId()));
        /*
         * =============== 5.上記編集した帳票用データを設定===============
         * 
         */
        // 調査アセスメント種別
        infoInDto.setAssType(syubetuPair.getLeft());
        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(syubetuPair.getRight());
        // 指定日（年号）=""
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
        // 指定日（年）=""
        infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
        // 指定日（月）=""
        infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
        // 指定日（日）=""
        infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

        /*
         * ===============共通関数補足 2.文書番号情報取得処理===============
         * 
         */
        // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01 関数名：GetF3GkProfile
        // を利用し、設定の読込（システム環境以外の設定）情報を取得する。
        // 【リクエストパラメータ】.システムコード
        String syscd = model.getSyscd();
        // 職員ID
        String shokuId = model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getShokuId();
        // セクション
        String section = model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection();
        // 【リクエストパラメータ】.事業者ID
        String svJigyoId = model.getJigyoInfo().getSvJigyoId();

        String bunsyoKanriNo = this.getBunsyoKanriNo(syscd, shokuId, section, svJigyoId);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(bunsyoKanriNo);

        return infoInDto;
    }

    /**
     * U06080_アセスメント表（Ｐ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetPReportServiceInDto getPReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {

        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));
        // 【リクエストパラメータ】.事業者ID
        String svJigyoId = model.getJigyoInfo().getSvJigyoId();
        // 【リクエストパラメータ】.事業者名
        String svJigyoKnj = model.getSvJigyoKnj();
        // 【リクエストパラメータ】.システムコード
        String syscd = model.getSyscd();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();
        // 【リクエストパラメータ】.記入用シートを印刷するフラグ
        Boolean isEmptyFlg = false;
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            isEmptyFlg = true;
        }
        // 帳票用データ詳細
        AssessmentSheetPReportServiceInDto infoInDto = new AssessmentSheetPReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、アセスメント表（P）情報の取得。===============
         * 
         */
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合

        infoInDto = this.getPDefReportParams(model);

        // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
        if (!isEmptyFlg) {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getPReportParamsFromAssP(printSubjectHistoryList, printSet,
                    model, syscd, svJigyoId, printOption, infoInDto);

        }

        // 帳票タイトル="インターライ方式ケアアセスメント表"
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 指定日印刷区分=リクエストパラメータ.データ.印刷設定.指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // 事業者名=リクエストパラメータ.データ.事業者名
        infoInDto.setJigyousha(svJigyoKnj);
        // 印刷時に色をつけるフラグ
        // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ = trueの場合、1を設定する
        // 上記以外の場合、2を設定する
        infoInDto.setColorFlg((CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg())
                || CommonConstants.COLOR_FLG_ON.equals(printOption.getColorFlg())) ? CommonConstants.NUMBER_1
                        : CommonConstants.NUMBER_2);
        // 記入用シートを印刷するフラグ=リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(isEmptyFlg);

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｐ）帳票パラメータ取得
     * 
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetPReportServiceInDto getPDefReportParams(AssessmentSheetReportParameterModel model)
            throws Exception {
        // 帳票用データ詳細
        AssessmentSheetPReportServiceInDto infoInDto = new AssessmentSheetPReportServiceInDto();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType())));
        // 調査アセスメント種別
        infoInDto.setAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType()));
        // shiTeiDateGG、shiTeiDateYY、shiTeiDateMM、shiTeiDateDDに" "を設定
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // p1_メモ
        infoInDto.setP1MemoKnj(CommonConstants.BLANK_STRING);
        // p1_メモフォント
        infoInDto.setP1MemoFont(CommonConstants.STR_12);
        // p1_メモ色
        infoInDto.setP1MemoColor(CommonConstants.BLANK_STRING);
        // 意思決定権_法定後見人等
        infoInDto.setP1A(CommonConstants.BLANK_STRING);
        // 意思決定権_任意後見
        infoInDto.setP1B(CommonConstants.BLANK_STRING);
        // 意思決定権_家族等の代理
        infoInDto.setP1C(CommonConstants.BLANK_STRING);
        // p2_メモ
        infoInDto.setP2MemoKnj(CommonConstants.BLANK_STRING);
        // p2_メモフォント
        infoInDto.setP2MemoFont(CommonConstants.STR_12);
        // p2_メモ色
        infoInDto.setP2MemoColor(CommonConstants.BLANK_STRING);
        // 事前指示_蘇生術
        infoInDto.setP2A(CommonConstants.BLANK_STRING);
        // 事前指示_挿管
        infoInDto.setP2B(CommonConstants.BLANK_STRING);
        // 事前指示_入院
        infoInDto.setP2C(CommonConstants.BLANK_STRING);
        // 事前指示_経管栄養
        infoInDto.setP2D(CommonConstants.BLANK_STRING);
        // 事前指示_薬剤制限
        infoInDto.setP2E(CommonConstants.BLANK_STRING);
        // q1_a_メモ
        infoInDto.setQ1AMemoKnj(CommonConstants.BLANK_STRING);
        // q1_a_メモフォント
        infoInDto.setQ1AMemoFont(CommonConstants.STR_12);
        // q1_a_メモ色
        infoInDto.setQ1AMemoColor(CommonConstants.BLANK_STRING);
        // インフォーマル_関係(主)
        infoInDto.setQ1A1(CommonConstants.BLANK_STRING);
        // インフォーマル_関係(副)
        infoInDto.setQ1A2(CommonConstants.BLANK_STRING);
        // q1_b_メモ
        infoInDto.setQ1BMemoKnj(CommonConstants.BLANK_STRING);
        // q1_b_メモフォント
        infoInDto.setQ1BMemoFont(CommonConstants.STR_12);
        // q1_b_メモ色
        infoInDto.setQ1BMemoColor(CommonConstants.BLANK_STRING);
        // インフォーマル_同居(主)
        infoInDto.setQ1B1(CommonConstants.BLANK_STRING);
        // インフォーマル_同居(副)
        infoInDto.setQ1B2(CommonConstants.BLANK_STRING);
        // q1_cd_メモ
        infoInDto.setQ1CdMemoKnj(CommonConstants.BLANK_STRING);
        // q1_cd_メモフォント
        infoInDto.setQ1CdMemoFont(CommonConstants.STR_12);
        // q1_cd_メモ色
        infoInDto.setQ1CdMemoColor(CommonConstants.BLANK_STRING);
        // インフォーマル_IADL(主)
        infoInDto.setQ1C1(CommonConstants.BLANK_STRING);
        // インフォーマル_IADL(副)
        infoInDto.setQ1C2(CommonConstants.BLANK_STRING);
        // インフォーマル_ADL(主)
        infoInDto.setQ1D1(CommonConstants.BLANK_STRING);
        // インフォーマル_ADL(副)
        infoInDto.setQ1D2(CommonConstants.BLANK_STRING);
        // q2_メモ
        infoInDto.setQ2MemoKnj(CommonConstants.BLANK_STRING);
        // q2メモフォント
        infoInDto.setQ2MemoFont(CommonConstants.STR_12);
        // q2メモ色
        infoInDto.setQ2MemoColor(CommonConstants.BLANK_STRING);
        // インフォーマル状況_ケア
        infoInDto.setQ2A(CommonConstants.BLANK_STRING);
        // インフォーマル状況_苦悩
        infoInDto.setQ2B(CommonConstants.BLANK_STRING);
        // インフォーマル状況_憔悴
        infoInDto.setQ2C(CommonConstants.BLANK_STRING);
        // q3_メモ
        infoInDto.setQ3MemoKnj(CommonConstants.BLANK_STRING);
        // q3メモフォント
        infoInDto.setQ3MemoFont(CommonConstants.STR_12);
        // q3メモ色
        infoInDto.setQ3MemoColor(CommonConstants.BLANK_STRING);
        // インフォーマルな援助量
        infoInDto.setQ3(CommonConstants.BLANK_STRING);
        // r1_メモ
        infoInDto.setR1MemoKnj(CommonConstants.BLANK_STRING);
        // r1_メモフォント
        infoInDto.setR1MemoFont(CommonConstants.STR_12);
        // r1_メモ色
        infoInDto.setR1MemoColor(CommonConstants.BLANK_STRING);
        // 退所の可能性_利用者
        infoInDto.setR1A(CommonConstants.BLANK_STRING);
        // 退所の可能性_支援者
        infoInDto.setR1B(CommonConstants.BLANK_STRING);
        // 退所の可能性_家
        infoInDto.setR1C(CommonConstants.BLANK_STRING);
        // r2_メモ
        infoInDto.setR2MemoKnj(CommonConstants.BLANK_STRING);
        // r2メモフォント
        infoInDto.setR2MemoFont(CommonConstants.STR_12);
        // r2メモ色
        infoInDto.setR2MemoColor(CommonConstants.BLANK_STRING);
        // 退所の予測期間
        infoInDto.setR2(CommonConstants.BLANK_STRING);
        // 終了日（年号）u1YmdGGに" "設定
        infoInDto.setU1YmdGG(CommonConstants.FULL_WIDTH_SPACE + CommonConstants.FULL_WIDTH_SPACE);
        // 終了日（年）u1YmdYY、u1YmdMM、u1YmdDDに" "を設定
        infoInDto.setU1YmdYY(CommonConstants.FULL_WIDTH_SPACE);
        // 終了日（月）
        infoInDto.setU1YmdMM(CommonConstants.FULL_WIDTH_SPACE);
        // 終了日（日）
        infoInDto.setU1YmdDD(CommonConstants.FULL_WIDTH_SPACE);
        // u2_メモ
        infoInDto.setU2MemoKnj(CommonConstants.BLANK_STRING);
        // u2メモフォント
        infoInDto.setU2MemoFont(CommonConstants.STR_12);
        // u2メモ色
        infoInDto.setU2MemoColor(CommonConstants.BLANK_STRING);
        // 今後の居住場所
        infoInDto.setU2(CommonConstants.BLANK_STRING);
        // u3_メモ
        infoInDto.setU3MemoKnj(CommonConstants.BLANK_STRING);
        // u3メモフォント
        infoInDto.setU3MemoFont(CommonConstants.STR_12);
        // u3メモ色
        infoInDto.setU3MemoColor(CommonConstants.BLANK_STRING);
        // サービスを受ける予定
        infoInDto.setU3(CommonConstants.BLANK_STRING);
        // 職員名（姓）
        infoInDto.setShokuin1Knj(CommonConstants.BLANK_STRING);
        // 職員名（名）
        infoInDto.setShokuin2Knj(CommonConstants.BLANK_STRING);
        // アセスメント完成日（年号）v2YmdGGに" "設定
        infoInDto.setV2YmdGG(CommonConstants.FULL_WIDTH_SPACE + CommonConstants.FULL_WIDTH_SPACE);
        // アセスメント完成日（年）v2YmdYY、v2YmdMM、v2YmdDDに" "を設定
        infoInDto.setV2YmdYY(CommonConstants.FULL_WIDTH_SPACE);
        // アセスメント完成日（月）
        infoInDto.setV2YmdMM(CommonConstants.FULL_WIDTH_SPACE);
        // アセスメント完成日（日）
        infoInDto.setV2YmdDD(CommonConstants.FULL_WIDTH_SPACE);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｐ）帳票パラメータ取得
     * 
     * @param printSubjectHistoryList 印刷対象履歴
     * @param printSet                プリンタ設定
     * @param model                   入力データ
     * @param syscd                   システムコード
     * @param svJigyoId               サービス事業者ID
     * @param printOption             印刷オプション
     * @param infoInDto               PDF帳票出力詳細データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetPReportServiceInDto getPReportParamsFromAssP(
            List<ReportCommonPrintSubjectHistory> printSubjectHistoryList,
            ReportCommonPrintSet printSet,
            AssessmentSheetReportParameterModel model, String syscd, String svJigyoId,
            ReportAssessmentSheetPrintOption printOption, AssessmentSheetPReportServiceInDto infoInDto)
            throws Exception {
        // 帳票用データ詳細

        /*
         * ===============4.アセスメント種別の取得===============
         * 
         */
        // 4.1. 「記入用シートを印刷する」チェックOFFの場合、
        // 下記のアセスメント種別のDAOを利用し、アセスメント種別情報を取得する。
        String syubetuKnj = CommonConstants.BLANK_STRING;
        Integer raiId = CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId());
        Pair<Integer, String> syubetPair = this.getSyubetuByRaiId(raiId);
        Integer assType = syubetPair.getLeft();

        List<KghCpnRaiAssPqPOutEntity> kghCpnRaiAssPqPOutEntities = new ArrayList<>();
        List<KghCpnRaiAssPrPOutEntity> kghCpnRaiAssPrPOutEntities = new ArrayList<>();
        List<KghCpnRaiAssPuvPOutEntity> kghCpnRaiAssPuvPOutEntities = new ArrayList<>();

        switch (assType) {
            case ReportConstants.ASSESSMENT_TYPE_HOMEEDITION:
                // 「4.」の調査アセスメント種別 = 1 の場合
                syubetuKnj = ReportConstants.STR_HOMEEDITION;

                // 2.1. 「4.1」で取得した調査アセスメント種別=1の場合、アセスメント表(P)(Q)情報を取得する。
                KghCpnRaiAssPqPByCriteriaInEntity kghCpnRaiAssPqPByCriteriaInEntity = new KghCpnRaiAssPqPByCriteriaInEntity();
                kghCpnRaiAssPqPByCriteriaInEntity.setAnRaiId(raiId);
                kghCpnRaiAssPqPOutEntities = raiRrkKghCpnRaiAssPQSelectMapper
                        .findKghCpnRaiAssPqPByCriteria(kghCpnRaiAssPqPByCriteriaInEntity);
                break;

            case ReportConstants.ASSESSMENT_TYPE_FACILITYEDITION:
                // 「4.」の調査アセスメント種別 = 2 の場合
                syubetuKnj = ReportConstants.STR_FACILITYEDITION;

                // 2.2. 「4.1」で取得した調査アセスメント種別=2の場合、アセスメント表(R)情報を取得する。
                KghCpnRaiAssPrPByCriteriaInEntity kghCpnRaiAssPrPByCriteriaInEntity = new KghCpnRaiAssPrPByCriteriaInEntity();
                kghCpnRaiAssPrPByCriteriaInEntity.setAnRaiId(raiId);
                kghCpnRaiAssPrPOutEntities = raiRrkKghCpnRaiAssPRSelectMapper
                        .findKghCpnRaiAssPrPByCriteria(kghCpnRaiAssPrPByCriteriaInEntity);
                break;

            case ReportConstants.ASSESSMENT_TYPE_SENIORHOUSINGEDITION:
                // 「4.」の調査アセスメント種別 = 3 の場合
                syubetuKnj = ReportConstants.STR_SENIORHOUSINGEDITION;

                // 2.3. 「4.1」で取得した調査アセスメント種別=3の場合、アセスメント表(P)(U)(V)情報を取得する。
                KghCpnRaiAssPuvPByCriteriaInEntity kghCpnRaiAssPuvPByCriteriaInEntity = new KghCpnRaiAssPuvPByCriteriaInEntity();
                kghCpnRaiAssPuvPByCriteriaInEntity.setAnRaiId(raiId);
                kghCpnRaiAssPuvPOutEntities = raiRrkKghCpnRaiAssPUVSelectMapper
                        .findKghCpnRaiAssPuvPByCriteria(kghCpnRaiAssPuvPByCriteriaInEntity);
                break;
        }

        /*
         * =============== 5.帳票用データを設定===============
         * 
         */
        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(syubetuKnj);
        // 調査アセスメント種別
        infoInDto.setAssType(assType);
        // 指定日（年号）
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
        // 指定日（年）
        infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
        // 指定日（月）
        infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
        // 指定日（日）
        infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

        // 2.4. 「2.1」~「2.3」で取得したアセスメント表（Ｐ）情報の件数をワークアセスメント表（Ｐ）情報.総件数に設定する。
        // 2.4.1. 総件数 > 0 件の場合。
        if (CollectionUtils.isNotEmpty(kghCpnRaiAssPqPOutEntities)
                || CollectionUtils.isNotEmpty(kghCpnRaiAssPrPOutEntities)
                || CollectionUtils.isNotEmpty(kghCpnRaiAssPuvPOutEntities)) {

            boolean pqpEntitiesFlag = false;
            boolean prpEntitiesFlag = false;
            boolean puvpEntitiesFlag = false;
            KghCpnRaiAssPqPOutEntity kghCpnRaiAssPqPOutEntity = new KghCpnRaiAssPqPOutEntity();
            KghCpnRaiAssPrPOutEntity kghCpnRaiAssPrPOutEntity = new KghCpnRaiAssPrPOutEntity();
            KghCpnRaiAssPuvPOutEntity kghCpnRaiAssPuvPOutEntity = new KghCpnRaiAssPuvPOutEntity();

            /*
             * ===============3. 利用者基本情報を取得===============
             * 
             */
            Integer userid = CommonConstants.NUMBER_0;

            // 利用者IDとアセスメント表の情報を取得する
            // 取得した調査アセスメント種別=1の場合
            if (CollectionUtils.isNotEmpty(kghCpnRaiAssPqPOutEntities)) {
                pqpEntitiesFlag = true;
                kghCpnRaiAssPqPOutEntity = kghCpnRaiAssPqPOutEntities.get(0);
                userid = kghCpnRaiAssPqPOutEntity.getUserid();

                // 取得した調査アセスメント種別=2の場合
            } else if (CollectionUtils.isNotEmpty(kghCpnRaiAssPrPOutEntities)) {
                prpEntitiesFlag = true;
                kghCpnRaiAssPrPOutEntity = kghCpnRaiAssPrPOutEntities.get(0);
                userid = kghCpnRaiAssPrPOutEntity.getUserid();

                // 取得した調査アセスメント種別=3の場合
            } else if (CollectionUtils.isNotEmpty(kghCpnRaiAssPuvPOutEntities)) {
                puvpEntitiesFlag = true;
                kghCpnRaiAssPuvPOutEntity = kghCpnRaiAssPuvPOutEntities.get(0);
                userid = kghCpnRaiAssPuvPOutEntity.getUserid();
            }
            Pair<String, String> userPair = this.getUserNamesPair(userid);

            // 氏名（姓）
            infoInDto.setName1Knj(userPair.getLeft());
            // 氏名（名）
            infoInDto.setName2Knj(userPair.getRight());

            boolean colorFlg = false;
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg())) {
                colorFlg = true;
            }

            // 取得した調査アセスメント種別=1の場合
            if (pqpEntitiesFlag && kghCpnRaiAssPqPOutEntity != null) {
                // p1_メモ=API定義の処理「2.1」のp1_メモ
                infoInDto.setP1MemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getP1MemoKnj()));

                // API定義の処理「2.1」のp1_メモフォント=nullの場合、
                // p1_メモフォント="12"
                // 上記以外の場合、
                // p1_メモフォント=API定義の処理「2.1」のp1_メモフォント
                infoInDto.setP1MemoFont(getMemoFont(kghCpnRaiAssPqPOutEntity.getP1MemoFont()));

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のp1_メモ色=nullの場合
                // p1_メモ色＝"#000000"
                // 上記以外の場合、
                // p1_メモ色=共通関数補足の「3.1」の十六進数RGB値
                // 上記以外の場合、p1_メモ色＝"#000000"
                infoInDto.setP1MemoColor(getMemoColor(kghCpnRaiAssPqPOutEntity.getP1MemoColor(), colorFlg));

                // 意思決定権_法定後見人等=API定義の処理「2.1」の意思決定権_法定後見人等
                infoInDto.setP1A(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getP1A()));

                // API定義の処理「2.2」のp2_メモフォント=nullの場合、p2_メモフォント="12"
                infoInDto.setP2MemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.2」のp2_メモ色=nullの場合、p2_メモ色＝"#000000"
                // 上記以外の場合、p2_メモ色＝"#000000"
                infoInDto.setP2MemoColor(ReportConstants.COLOR_BLANK);

                // q1_a_メモ=API定義の処理「2.1」のq1_a_メモ
                infoInDto.setQ1AMemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ1AMemoKnj()));

                // API定義の処理「2.1」のq1_a_メモフォント=nullの場合、
                // q1_a_メモフォント="12"
                // 上記以外の場合、
                // q1_a_メモフォント=API定義の処理「2.1」のq1_a_メモフォント
                infoInDto.setQ1AMemoFont(getMemoFont(kghCpnRaiAssPqPOutEntity.getQ1AMemoFont()));

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq1_a_メモ色=nullの場合、
                // q1_a_メモ色＝"#000000"
                // 上記以外の場合、
                // p1_メモ色=共通関数補足の「3.1」の十六進数RGB値
                // 上記以外の場合、
                // p1_メモ色＝"#000000"
                infoInDto.setQ1AMemoColor(getMemoColor(kghCpnRaiAssPqPOutEntity.getQ1AMemoColor(), colorFlg));

                // インフォーマル_関係(主) =API定義の処理「2.1」のインフォーマル_関係(主)
                infoInDto.setQ1A1(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ1A1()));
                // インフォーマル_関係(副)=API定義の処理「2.1」のインフォーマル_関係(副)
                infoInDto.setQ1A2(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ1A2()));
                // q1_b_メモ=API定義の処理「2.1」のq1_b_メモ
                infoInDto.setQ1BMemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ1BMemoKnj()));

                // API定義の処理「2.1」のq1_b_メモフォント=nullの場合、
                // q1_b_メモフォント="12"
                // 上記以外の場合、
                // q1_b_メモフォント=API定義の処理「2.1」のq1_b_メモフォント
                infoInDto.setQ1BMemoFont(getMemoFont(kghCpnRaiAssPqPOutEntity.getQ1BMemoFont()));

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq1_b_メモ色=nullの場合、
                // q1_b_メモ色＝"#000000"
                // 上記以外の場合、
                // q1_b_メモ色=共通関数補足の「3.1」の十六進数RGB値
                // 上記以外の場合、
                // q1_b_メモ色＝"#000000"
                infoInDto.setQ1BMemoColor(getMemoColor(kghCpnRaiAssPqPOutEntity.getQ1BMemoColor(), colorFlg));

                // インフォーマル_同居(主)=API定義の処理「2.1」のインフォーマル_同居(主)
                infoInDto.setQ1B1(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ1B1()));
                // インフォーマル_同居(副) =API定義の処理「2.1」のインフォーマル_同居(副)
                infoInDto.setQ1B2(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ1B2()));
                // q1_cd_メモ=API定義の処理「2.1」のq1_cdメモ
                infoInDto.setQ1CdMemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ1CdMemoKnj()));

                // API定義の処理「2.1」のq1_cd_メモフォント=nullの場合、
                // q1_cd_メモフォント="12"
                // 上記以外の場合、
                // q1_cd_メモフォント=API定義の処理「2.1」のq1_cd_メモフォント
                infoInDto.setQ1CdMemoFont(getMemoFont(kghCpnRaiAssPqPOutEntity.getQ1CdMemoFont()));

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq1_cd_メモ色=nullの場合、
                // q1_cd_メモ色＝"#000000"
                // 上記以外の場合、
                // q1_cd_メモ色=共通関数補足の「3.1」の十六進数RGB値
                // 上記以外の場合、
                // q1_cd_メモ色＝"#000000"
                infoInDto.setQ1CdMemoColor(getMemoColor(kghCpnRaiAssPqPOutEntity.getQ1CdMemoColor(), colorFlg));

                // インフォーマル_IADL(主) =API定義の処理「2.1」のインフォーマル_IADL(主)
                infoInDto.setQ1C1(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ1C1()));
                // インフォーマル_IADL(副) =API定義の処理「2.1」のインフォーマル_IADL(副)
                infoInDto.setQ1C2(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ1C2()));
                // インフォーマル_ADL(主) =API定義の処理「2.1」のインフォーマル_ADL(主)
                infoInDto.setQ1D1(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ1D1()));
                // インフォーマル_ADL(副) =API定義の処理「2.1」のインフォーマル_ADL(副)
                infoInDto.setQ1D2(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ1D2()));

                // q2_メモ=API定義の処理「2.1」のq2_メモ
                infoInDto.setQ2MemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ2MemoKnj()));

                // API定義の処理「2.1」のq2_メモフォント=nullの場合、
                // q2_メモフォント="12"
                // 上記以外の場合、
                // q2_メモフォント=API定義の処理「2.1」のq2_メモフォント
                infoInDto.setQ2MemoFont(getMemoFont(kghCpnRaiAssPqPOutEntity.getQ2MemoFont()));

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq2_メモ色=nullの場合、
                // q2_メモ色＝"#000000"
                // 上記以外の場合、
                // q2_メモ色=共通関数補足の「3.1」の十六進数RGB値
                // 上記以外の場合、
                // q2_メモ色＝"#000000"
                infoInDto.setQ2MemoColor(getMemoColor(kghCpnRaiAssPqPOutEntity.getQ2MemoColor(), colorFlg));

                // インフォーマル状況_ケア=API定義の処理「2.1」のインフォーマル状況_ケア
                infoInDto.setQ2A(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ2A()));
                // インフォーマル状況_苦悩=API定義の処理「2.1」のインフォーマル状況_苦悩
                infoInDto.setQ2B(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ2B()));
                // インフォーマル状況_憔悴=API定義の処理「2.1」のインフォーマル状況_憔悴
                infoInDto.setQ2C(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ2C()));
                // q3_メモ=API定義の処理「2.1」のq3_メモ
                infoInDto.setQ3MemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ3MemoKnj()));

                // API定義の処理「2.1」のq3_メモフォント=nullの場合、
                // q3_メモフォント="12"
                // 上記以外の場合、
                // q3_メモフォント=API定義の処理「2.1」のq3_メモフォント
                infoInDto.setQ3MemoFont(getMemoFont(kghCpnRaiAssPqPOutEntity.getQ3MemoFont()));

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq3_メモ色=nullの場合、
                // q3_メモ色＝"#000000"
                // 上記以外の場合、
                // q3_メモ色=共通関数補足の「3.1」の十六進数RGB値
                // 上記以外の場合、
                // q3_メモ色＝"#000000"
                infoInDto.setQ3MemoColor(getMemoColor(kghCpnRaiAssPqPOutEntity.getQ3MemoColor(), colorFlg));

                // インフォーマルな援助量=API定義の処理「2.1」のインフォーマルな援助量
                infoInDto.setQ3(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutEntity.getQ3()));

                // API定義の処理「2.2」のr1_メモフォント=nullの場合、r1_メモフォント="12"
                infoInDto.setR1MemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.2」のr1_メモ色=nullの場合、r1_メモ色＝"#000000"
                // 上記以外の場合、r1_メモ色＝"#000000"
                infoInDto.setR1MemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.2」のr2_メモフォント=nullの場合、r2_メモフォント="12"
                infoInDto.setR2MemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.2」のr2_メモ色=nullの場合、r2_メモ色＝"#000000"
                // 上記以外の場合、r2_メモ色＝"#000000"
                infoInDto.setR2MemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.3」のu2_メモフォント=nullの場合、u2_メモフォント="12"
                infoInDto.setU2MemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.3」のu2_メモ色=nullの場合 u2_メモ色＝"#000000"
                // 上記以外の場合、u2_メモ色＝"#000000"
                infoInDto.setU2MemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.3」のu3_メモフォント=nullの場合、u3_メモフォント="12"
                infoInDto.setU3MemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.3」のu3_メモ色=nullの場合 u3_メモ色＝"#000000"
                // 上記以外の場合、u3_メモ色＝"#000000"
                infoInDto.setU3MemoColor(ReportConstants.COLOR_BLANK);

                // 取得した調査アセスメント種別=2の場合
            } else if (prpEntitiesFlag && kghCpnRaiAssPrPOutEntity != null) {
                // p1_メモ=API定義の処理「2.2」のp1_メモ
                infoInDto.setP1MemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getP1MemoKnj()));

                // API定義の処理「2.2」のp1_メモフォント=nullの場合、
                // p1_メモフォント="12"
                // 上記以外の場合、
                // p1_メモフォント=API定義の処理「2.2」のp1_メモフォント
                infoInDto.setP1MemoFont(getMemoFont(kghCpnRaiAssPrPOutEntity.getP1MemoFont()));

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.2」のp1_メモ色=nullの場合
                // p1_メモ色＝"#000000"
                // 上記以外の場合、
                // p1_メモ色=共通関数補足の「3.1」の十六進数RGB値
                // 上記以外の場合、
                // p1_メモ色＝"#000000"
                infoInDto.setP1MemoColor(getMemoColor(kghCpnRaiAssPrPOutEntity.getP1MemoColor(), colorFlg));

                // 意思決定権_法定後見人等=API定義の処理「2.2」の意思決定権_法定後見人等
                infoInDto.setP1A(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getP1A()));
                // 意思決定権_任意後見=API定義の処理「2.2」の意思決定権_任意後見
                infoInDto.setP1B(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getP1B()));
                // 意思決定権_家族等の代理=API定義の処理「2.2」の意思決定権_家族等の代理
                infoInDto.setP1C(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getP1C()));

                // p2_メモ=API定義の処理「2.2」のp2_メモ
                infoInDto.setP2MemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getP2MemoKnj()));

                // API定義の処理「2.2」のp2_メモフォント=nullの場合、
                // p2_メモフォント="12"
                // 上記以外の場合、
                // p2_メモフォント=API定義の処理「2.2」のp2_メモフォント
                infoInDto.setP2MemoFont(getMemoFont(kghCpnRaiAssPrPOutEntity.getP2MemoFont()));

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // p2_メモ色＝"#000000"
                // 上記以外の場合、
                // p2_メモ色=共通関数補足の「3.1」の十六進数RGB値
                // 上記以外の場合、
                // p2_メモ色＝"#000000"
                infoInDto.setP2MemoColor(getMemoColor(kghCpnRaiAssPrPOutEntity.getP2MemoColor(), colorFlg));

                // 事前指示_蘇生術=API定義の処理「2.2」の事前指示_蘇生術
                infoInDto.setP2A(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getP2A()));
                // 事前指示_挿管=API定義の処理「2.2」の事前指示_挿管
                infoInDto.setP2B(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getP2B()));
                // 事前指示_入院=API定義の処理「2.2」の事前指示_入院
                infoInDto.setP2C(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getP2C()));
                // 事前指示_経管栄養=API定義の処理「2.2」の事前指示_経管栄養
                infoInDto.setP2D(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getP2D()));
                // 事前指示_薬剤制限=API定義の処理「2.2」の事前指示_薬剤制限
                infoInDto.setP2E(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getP2E()));

                // API定義の処理「2.1」のq1_a_メモフォント=nullの場合、q1_a_メモフォント="12"
                infoInDto.setQ1AMemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq1_a_メモ色=nullの場合、q1_a_メモ色＝"#000000"
                // 上記以外の場合、p1_メモ色＝"#000000"
                infoInDto.setQ1AMemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.1」のq1_b_メモフォント=nullの場合、q1_b_メモフォント="12"
                infoInDto.setQ1BMemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq1_b_メモ色=nullの場合、q1_b_メモ色＝"#000000"
                // 上記以外の場合、q1_b_メモ色＝"#000000"
                infoInDto.setQ1BMemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.1」のq1_cd_メモフォント=nullの場合、q1_cd_メモフォント="12"
                infoInDto.setQ1CdMemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq1_cd_メモ色=nullの場合、q1_cd_メモ色＝"#000000"
                // 上記以外の場合、q1_cd_メモ色＝"#000000"
                infoInDto.setQ1CdMemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.1」のq2_メモフォント=nullの場合、q2_メモフォント="12"
                infoInDto.setQ2MemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq2_メモ色=nullの場合、q2_メモ色＝"#000000"
                // 上記以外の場合、q2_メモ色＝"#000000"
                infoInDto.setQ2MemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.1」のq3_メモフォント=nullの場合、q3_メモフォント="12"
                infoInDto.setQ3MemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq3_メモ色=nullの場合、q3_メモ色＝"#000000"
                // 上記以外の場合、q3_メモ色＝"#000000"
                infoInDto.setQ3MemoColor(ReportConstants.COLOR_BLANK);

                // r1_メモ=API定義の処理「2.2」のr1_メモ
                infoInDto.setR1MemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getR1MemoKnj()));

                // API定義の処理「2.2」のr1_メモフォント=nullの場合、
                // r1_メモフォント="12"
                // 上記以外の場合、
                // r1_メモフォント=API定義の処理「2.2」のr1_メモフォント
                infoInDto.setR1MemoFont(getMemoFont(kghCpnRaiAssPrPOutEntity.getR1MemoFont()));

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.2」のr1_メモ色=nullの場合、
                // r1_メモ色＝"#000000"
                // 上記以外の場合、
                // r1_メモ色=共通関数補足の「3.1」の十六進数RGB値
                // 上記以外の場合、
                // r1_メモ色＝"#000000"
                infoInDto.setR1MemoColor(getMemoColor(kghCpnRaiAssPrPOutEntity.getR1MemoColor(), colorFlg));

                // 退所の可能性_利用者=API定義の処理「2.2」の退所の可能性_利用者
                infoInDto.setR1A(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getR1A()));
                // 退所の可能性_支援者=API定義の処理「2.2」の退所の可能性_支援者
                infoInDto.setR1B(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getR1B()));
                // 退所の可能性_家=API定義の処理「2.2」の退所の可能性_家
                infoInDto.setR1C(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getR1C()));
                // r2_メモ=API定義の処理「2.2」のr2_メモ
                infoInDto.setR2MemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getR2MemoKnj()));

                // API定義の処理「2.2」のr2_メモフォント=nullの場合、
                // r2_メモフォント="12"
                // 上記以外の場合、
                // r2_メモフォント=API定義の処理「2.2」のr1_メモフォント
                infoInDto.setR2MemoFont(getMemoFont(kghCpnRaiAssPrPOutEntity.getR2MemoFont()));

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.2」のr2_メモ色=nullの場合、
                // r2_メモ色＝"#000000"
                // 上記以外の場合、
                // r2_メモ色=共通関数補足の「3.1」の十六進数RGB値
                // 上記以外の場合、
                // r2_メモ色＝"#000000"
                infoInDto.setR2MemoColor(getMemoColor(kghCpnRaiAssPrPOutEntity.getR2MemoColor(), colorFlg));

                // 退所の予測期間API定義の処理「2.2」の退所の予測期間
                infoInDto.setR2(ReportUtil.nullToEmpty(kghCpnRaiAssPrPOutEntity.getR2()));

                // API定義の処理「2.3」のu2_メモフォント=nullの場合、u2_メモフォント="12"
                infoInDto.setU2MemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.3」のu2_メモ色=nullの場合 u2_メモ色＝"#000000"
                // 上記以外の場合、u2_メモ色＝"#000000"
                infoInDto.setU2MemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.3」のu3_メモフォント=nullの場合、u3_メモフォント="12"
                infoInDto.setU3MemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.3」のu3_メモ色=nullの場合 u3_メモ色＝"#000000"
                // 上記以外の場合、u3_メモ色＝"#000000"
                infoInDto.setU3MemoColor(ReportConstants.COLOR_BLANK);

                // 取得した調査アセスメント種別=3の場合
            } else if (puvpEntitiesFlag && kghCpnRaiAssPuvPOutEntity != null) {
                // p1_メモ=API定義の処理「2.3」のp1_メモ
                infoInDto.setP1MemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPuvPOutEntity.getP1MemoKnj()));

                // API定義の処理「2.3」のp1_メモフォント=nullの場合、
                // p1_メモフォント="12"
                // 上記以外の場合、
                // p1_メモフォント=API定義の処理「2.3」のp1_メモフォント
                infoInDto.setP1MemoFont(getMemoFont(kghCpnRaiAssPuvPOutEntity.getP1MemoFont()));

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.3」のp1_メモ色=nullの場合
                // p1_メモ色＝"#000000"
                // 上記以外の場合、
                // p1_メモ色=共通関数補足の「3.1」の十六進数RGB値
                // 上記以外の場合、
                // p1_メモ色＝"#000000"
                infoInDto.setP1MemoColor(getMemoColor(kghCpnRaiAssPuvPOutEntity.getP1MemoColor(), colorFlg));

                // 意思決定権_法定後見人等=API定義の処理「2.3」の意思決定権_法定後見人等
                infoInDto.setP1A(ReportUtil.nullToEmpty(kghCpnRaiAssPuvPOutEntity.getP1A()));

                // API定義の処理「2.2」のp2_メモフォント=nullの場合、p2_メモフォント="12"
                infoInDto.setP2MemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.2」のp2_メモ色=nullの場合、p2_メモ色＝"#000000"
                // 上記以外の場合、p2_メモ色＝"#000000"
                infoInDto.setP2MemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.1」のq1_a_メモフォント=nullの場合、q1_a_メモフォント="12"
                infoInDto.setQ1AMemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq1_a_メモ色=nullの場合、q1_a_メモ色＝"#000000"
                // 上記以外の場合、p1_メモ色＝"#000000"
                infoInDto.setQ1AMemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.1」のq1_b_メモフォント=nullの場合、q1_b_メモフォント="12"
                infoInDto.setQ1BMemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq1_b_メモ色=nullの場合、q1_b_メモ色＝"#000000"
                // 上記以外の場合、q1_b_メモ色＝"#000000"
                infoInDto.setQ1BMemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.1」のq1_cd_メモフォント=nullの場合、q1_cd_メモフォント="12"
                infoInDto.setQ1CdMemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq1_cd_メモ色=nullの場合、q1_cd_メモ色＝"#000000"
                // 上記以外の場合、q1_cd_メモ色＝"#000000"
                infoInDto.setQ1CdMemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.1」のq2_メモフォント=nullの場合、q2_メモフォント="12"
                infoInDto.setQ2MemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq2_メモ色=nullの場合、q2_メモ色＝"#000000"
                // 上記以外の場合、q2_メモ色＝"#000000"
                infoInDto.setQ2MemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.1」のq3_メモフォント=nullの場合、q3_メモフォント="12"
                infoInDto.setQ3MemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のq3_メモ色=nullの場合、q3_メモ色＝"#000000"
                // 上記以外の場合、q3_メモ色＝"#000000"
                infoInDto.setQ3MemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.2」のr1_メモフォント=nullの場合、r1_メモフォント="12"
                infoInDto.setR1MemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.2」のr1_メモ色=nullの場合、r1_メモ色＝"#000000"
                // 上記以外の場合、r1_メモ色＝"#000000"
                infoInDto.setR1MemoColor(ReportConstants.COLOR_BLANK);

                // API定義の処理「2.2」のr2_メモフォント=nullの場合、r2_メモフォント="12"
                infoInDto.setR2MemoFont(CommonConstants.STR_12);

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.2」のr2_メモ色=nullの場合、r2_メモ色＝"#000000"
                // 上記以外の場合、r2_メモ色＝"#000000"
                infoInDto.setR2MemoColor(ReportConstants.COLOR_BLANK);

                List<String> u1ymdList = this.getDate(ReportUtil.nullToEmpty(kghCpnRaiAssPuvPOutEntity.getU1Ymd()));
                // 終了日（年号）
                infoInDto.setU1YmdGG(u1ymdList.get(0));
                // 終了日（年）
                infoInDto.setU1YmdYY(u1ymdList.get(1));
                // 終了日（月）
                infoInDto.setU1YmdMM(u1ymdList.get(2));
                // 終了日（日）
                infoInDto.setU1YmdDD(u1ymdList.get(3));
                // u2_メモ=API定義の処理「2.3」のu2_メモ
                infoInDto.setU2MemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPuvPOutEntity.getU2MemoKnj()));

                // API定義の処理「2.3」のu2_メモフォント=nullの場合、
                // u2_メモフォント="12"
                // 上記以外の場合、
                // u2_メモフォント=API定義の処理「2.3」のu2_メモフォント
                infoInDto.setU2MemoFont(getMemoFont(kghCpnRaiAssPuvPOutEntity.getU2MemoFont()));

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.3」のu2_メモ色=nullの場合
                // u2_メモ色＝"#000000"
                // 上記以外の場合、
                // u2_メモ色=共通関数補足の「3.1」の十六進数RGB値
                // 上記以外の場合、
                // u2_メモ色＝"#000000"
                infoInDto.setU2MemoColor(getMemoColor(kghCpnRaiAssPuvPOutEntity.getU2MemoColor(), colorFlg));

                // 今後の居住場所=API定義の処理「2.3」の今後の居住場所
                infoInDto.setU2(ReportUtil.nullToEmpty(kghCpnRaiAssPuvPOutEntity.getU2()));
                // u3_メモ=API定義の処理「2.3」のu3_メモ
                infoInDto.setU3MemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPuvPOutEntity.getU3MemoKnj()));

                // API定義の処理「2.3」のu3_メモフォント=nullの場合、
                // u3メモフォント="12"
                // 上記以外の場合、
                // u3_メモフォント=API定義の処理「2.3」のu3_メモフォント
                infoInDto.setU3MemoFont(getMemoFont(kghCpnRaiAssPuvPOutEntity.getU3MemoFont()));

                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.3」のu3_メモ色=nullの場合
                // u3_メモ色＝"#000000"
                // 上記以外の場合、
                // u3_メモ色=共通関数補足の「3.1」の十六進数RGB値
                // 上記以外の場合、
                // u3_メモ色＝"#000000"
                infoInDto.setU3MemoColor(getMemoColor(kghCpnRaiAssPuvPOutEntity.getU3MemoColor(), colorFlg));

                // サービスを受ける予定=API定義の処理「2.3」のサービスを受ける予定
                infoInDto.setU3(ReportUtil.nullToEmpty(kghCpnRaiAssPuvPOutEntity.getU3()));
                // 職員名（姓）=API定義の処理「2.3」の職員名（姓）
                infoInDto.setShokuin1Knj(ReportUtil.nullToEmpty(kghCpnRaiAssPuvPOutEntity.getShokuin1Knj()));
                // 職員名（名）=API定義の処理「2.3」の職員名（名）
                infoInDto.setShokuin2Knj(ReportUtil.nullToEmpty(kghCpnRaiAssPuvPOutEntity.getShokuin2Knj()));
                List<String> v2ymdList = this.getDate(ReportUtil.nullToEmpty(kghCpnRaiAssPuvPOutEntity.getV2Ymd()));
                // アセスメント完成日（年号）
                infoInDto.setV2YmdGG(v2ymdList.get(0));
                // アセスメント完成日（年）
                infoInDto.setV2YmdYY(v2ymdList.get(1));
                // アセスメント完成日（月）
                infoInDto.setV2YmdMM(v2ymdList.get(2));
                // アセスメント完成日（日）
                infoInDto.setV2YmdDD(v2ymdList.get(3));
            }
        }

        /*
         * =============== 共通関数補足 2.文書番号情報取得処理===============
         * 
         */
        // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01 関数名：GetF3GkProfile
        // を利用し、設定の読込（システム環境以外の設定）情報を取得する。
        // 職員ID
        String shokuId = printSubjectHistoryList.get(0).getChoPrtList().get(0).getShokuId();
        // セクション
        String section = printSubjectHistoryList.get(0).getChoPrtList().get(0).getSection();
        String bunsyoKanriNo = this.getBunsyoKanriNo(syscd, shokuId, section, svJigyoId);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(ReportUtil.nullToEmpty(bunsyoKanriNo));

        return infoInDto;
    }

    /**
     * U06080_アセスメント表（Ｑ））帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetQReportServiceInDto getQReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        AssessmentSheetQReportServiceInDto infoInDto = new AssessmentSheetQReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、アセスメント表（Ｑ））情報の取得。===============
         * 
         */
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.記入用シートを印刷するフラグ
        Boolean isEmptyFlg = false;
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            isEmptyFlg = true;
        }
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (isEmptyFlg) {
            infoInDto = this.getQDefReportParams(printOption.getKinyuAssType());
        } else {
            // 【リクエストパラメータ】.印刷設定
            ReportCommonPrintSet printSet = model.getPrintSet();
            // 【リクエストパラメータ】.印刷対象履歴リスト
            ReportCommonPrintSubjectHistory printSubjectHistory = model.getPrintSubjectHistoryList().get(0);
            Boolean colorFlg = CommonConstants.COLOR_FLG_ON.equals(printOption.getColorFlg()) ? Boolean.TRUE
                    : Boolean.FALSE;
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getQReportParamsFromAssQ(printSubjectHistory, printSet.getShiTeiKubun(),
                    model.getSystemDate(), printSet.getShiTeiDate(), colorFlg, model.getSyscd(),
                    model.getJigyoInfo().getSvJigyoId());
        }

        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 事業者名
        infoInDto.setJigyousha(model.getSvJigyoKnj());
        // 指定日印刷区分←リクエストパラメータ.データ.印刷設定.指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(model.getPrintSet().getShiTeiKubun()));
        // 調査アセスメント種別
        infoInDto.setAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType()));
        // 記入用シートを印刷するフラグ=リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(isEmptyFlg);
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｑ））帳票パラメータ取得
     * 
     * @param kinyuAssType アセスメント種別
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetQReportServiceInDto getQDefReportParams(String kinyuAssType) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetQReportServiceInDto infoInDto = new AssessmentSheetQReportServiceInDto();
        // アセスメント種別
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(kinyuAssType)));

        // 指定日（年号）=" "
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）=" "
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）=" "
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）=" "
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // p1_メモ
        infoInDto.setP1MemoKnj(CommonConstants.BLANK_STRING);
        // p1_メモフォント
        infoInDto.setP1MemoFont(CommonConstants.STR_12);
        // p1_メモ色
        infoInDto.setP1MemoColor(CommonConstants.BLANK_STRING);
        // 意思決定権_法定後見人等
        infoInDto.setP1A(CommonConstants.BLANK_STRING);
        // q1_a_メモ
        infoInDto.setQ1AMemoKnj(CommonConstants.BLANK_STRING);
        // q1_a_メモフォント
        infoInDto.setQ1AMemoFont(CommonConstants.STR_12);
        // q1_a_メモ色
        infoInDto.setQ1AMemoColor(CommonConstants.BLANK_STRING);
        // インフォーマル_関係(主)
        infoInDto.setQ1A1(CommonConstants.BLANK_STRING);
        // インフォーマル_関係(副)
        infoInDto.setQ1A2(CommonConstants.BLANK_STRING);
        // q1_b_メモ
        infoInDto.setQ1BMemoKnj(CommonConstants.BLANK_STRING);
        // q1_b_メモフォント
        infoInDto.setQ1BMemoFont(CommonConstants.STR_12);
        // q1_b_メモ色
        infoInDto.setQ1BMemoColor(CommonConstants.BLANK_STRING);
        // インフォーマル_同居(主)
        infoInDto.setQ1B1(CommonConstants.BLANK_STRING);
        // インフォーマル_同居(副)
        infoInDto.setQ1B2(CommonConstants.BLANK_STRING);
        // q1_cdメモ
        infoInDto.setQ1CdMemoKnj(CommonConstants.BLANK_STRING);
        // q1_cd_メモフォント
        infoInDto.setQ1CdMemoFont(CommonConstants.STR_12);
        // q1_cd_メモ色
        infoInDto.setQ1CdMemoColor(CommonConstants.BLANK_STRING);
        // インフォーマル_IADL(主)
        infoInDto.setQ1C1(CommonConstants.BLANK_STRING);
        // インフォーマル_IADL(副)
        infoInDto.setQ1C2(CommonConstants.BLANK_STRING);
        // インフォーマル_ADL(主)
        infoInDto.setQ1D1(CommonConstants.BLANK_STRING);
        // インフォーマル_ADL(副)
        infoInDto.setQ1D2(CommonConstants.BLANK_STRING);
        // q2_メモ
        infoInDto.setQ2MemoKnj(CommonConstants.BLANK_STRING);
        // q2_メモフォント
        infoInDto.setQ2MemoFont(CommonConstants.STR_12);
        // q2_メモ色
        infoInDto.setQ2MemoColor(CommonConstants.BLANK_STRING);
        // インフォーマル状況_ケア
        infoInDto.setQ2A(CommonConstants.BLANK_STRING);
        // インフォーマル状況_苦悩
        infoInDto.setQ2B(CommonConstants.BLANK_STRING);
        // インフォーマル状況_憔悴
        infoInDto.setQ2C(CommonConstants.BLANK_STRING);
        // q3_メモ
        infoInDto.setQ3MemoKnj(CommonConstants.BLANK_STRING);
        // q3_メモフォント
        infoInDto.setQ3MemoFont(CommonConstants.STR_12);
        // q3_メモ色
        infoInDto.setQ3MemoColor(CommonConstants.BLANK_STRING);
        // インフォーマルな援助量
        infoInDto.setQ3(CommonConstants.BLANK_STRING);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｑ））帳票パラメータ取得
     * 
     * @param history     印刷対象履歴
     * @param shiTeiKubun 指定日印刷区分
     * @param systemDate  システム日付
     * @param shiTeiDate  西暦日付
     * @param colorFlg    印刷時に色をつけるフラグ
     * @param syscd       システムコード
     * @param svJigyoId   サービス事業者ID
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetQReportServiceInDto getQReportParamsFromAssQ(ReportCommonPrintSubjectHistory history,
            String shiTeiKubun,
            String systemDate, String shiTeiDate, Boolean colorFlg, String syscd, String svJigyoId) throws Exception {

        // 帳票用データ詳細
        AssessmentSheetQReportServiceInDto infoInDto = new AssessmentSheetQReportServiceInDto();

        // 2.1. アセスメント表（Ｑ））情報を取得する。
        KghCpnRaiAssPqPByCriteriaInEntity kghCpnRaiAssPqPByCriteriaInEntity = new KghCpnRaiAssPqPByCriteriaInEntity();
        kghCpnRaiAssPqPByCriteriaInEntity.setAnRaiId(CommonDtoUtil.strValToInt(history.getRaiId()));
        List<KghCpnRaiAssPqPOutEntity> assQList = raiRrkKghCpnRaiAssPQSelectMapper
                .findKghCpnRaiAssPqPByCriteria(kghCpnRaiAssPqPByCriteriaInEntity);
        // 2.2.「2.1.」で取得したアセスメント表（Ｑ））情報の件数をワークアセスメント表（Ｑ））情報.総件数に設定する。
        if (CollectionUtils.isNotEmpty(assQList)) {
            // 2.2.1 総件数 > 0 件の場合
            KghCpnRaiAssPqPOutEntity kghCpnRaiAssPqPOutentity = assQList.get(0);
            /*
             * ===============3. 利用者基本情報を取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(kghCpnRaiAssPqPOutentity.getUserid());

            /*
             * ===============4.アセスメント種別の取得===============
             * 
             */
            Pair<Integer, String> syubetPair = this.getSyubetuByRaiId(CommonDtoUtil.strValToInt(history.getRaiId()));

            /*
             * =============== 5.帳票用データを設定===============
             * 
             */
            // アセスメント種別
            infoInDto.setSyubetuKnj(syubetPair.getRight());

            // 指定日（年号）
            ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(shiTeiKubun, shiTeiDate, systemDate);
            infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
            // 指定日（年）
            infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
            // 指定日（月）
            infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
            // 指定日（日）
            infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());
            // 氏名（姓）
            infoInDto.setName1Knj(userPair.getLeft());
            // 氏名（名）
            infoInDto.setName2Knj(userPair.getRight());

            // p1_メモ
            infoInDto.setP1MemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getP1MemoKnj()));

            // p1_メモフォント
            infoInDto.setP1MemoFont(getMemoFont(kghCpnRaiAssPqPOutentity.getP1MemoFont()));

            // p1_メモ色
            infoInDto.setP1MemoColor(getMemoColor(kghCpnRaiAssPqPOutentity.getP1MemoColor(), colorFlg));

            // 意思決定権_法定後見人等
            infoInDto.setP1A(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getP1A()));

            // q1_a_メモ
            infoInDto.setQ1AMemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ1AMemoKnj()));

            // q1_a_メモフォント
            infoInDto.setQ1AMemoFont(getMemoFont(kghCpnRaiAssPqPOutentity.getQ1AMemoFont()));

            // q1_a_メモ色
            infoInDto.setQ1AMemoColor(getMemoColor(kghCpnRaiAssPqPOutentity.getQ1AMemoColor(), colorFlg));

            // インフォーマル_関係(主)
            infoInDto.setQ1A1(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ1A1()));

            // インフォーマル_関係(副)
            infoInDto.setQ1A2(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ1A2()));

            // q1_b_メモ
            infoInDto.setQ1BMemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ1BMemoKnj()));

            // q1_b_メモフォント
            infoInDto.setQ1BMemoFont(getMemoFont(kghCpnRaiAssPqPOutentity.getQ1BMemoFont()));

            // q1_b_メモ色
            infoInDto.setQ1BMemoColor(getMemoColor(kghCpnRaiAssPqPOutentity.getQ1BMemoColor(), colorFlg));

            // インフォーマル_同居(主)
            infoInDto.setQ1B1(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ1B1()));

            // インフォーマル_同居(副)
            infoInDto.setQ1B2(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ1B2()));

            // q1_cd_メモ
            infoInDto.setQ1CdMemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ1CdMemoKnj()));

            // q1_cd_メモフォント
            infoInDto.setQ1CdMemoFont(getMemoFont(kghCpnRaiAssPqPOutentity.getQ1CdMemoFont()));

            // q1_cd_メモ色
            infoInDto.setQ1CdMemoColor(getMemoColor(kghCpnRaiAssPqPOutentity.getQ1CdMemoColor(), colorFlg));

            // インフォーマル_IADL(主)
            infoInDto.setQ1C1(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ1C1()));

            // インフォーマル_IADL(副)
            infoInDto.setQ1C2(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ1C2()));

            // インフォーマル_ADL(主)
            infoInDto.setQ1D1(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ1D1()));

            // インフォーマル_ADL(副)
            infoInDto.setQ1D2(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ1D2()));

            // q2_メモ
            infoInDto.setQ2MemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ2MemoKnj()));

            // q2_メモフォント
            infoInDto.setQ2MemoFont(getMemoFont(kghCpnRaiAssPqPOutentity.getQ2MemoFont()));

            // q2_メモ色
            infoInDto.setQ2MemoColor(getMemoColor(kghCpnRaiAssPqPOutentity.getQ2MemoColor(), colorFlg));

            // インフォーマル状況_ケア
            infoInDto.setQ2A(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ2A()));

            // インフォーマル状況_苦悩
            infoInDto.setQ2B(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ2B()));

            // インフォーマル状況_憔悴
            infoInDto.setQ2C(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ2C()));

            // q3_メモ
            infoInDto.setQ3MemoKnj(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ3MemoKnj()));

            // q3_メモフォント
            infoInDto.setQ3MemoFont(getMemoFont(kghCpnRaiAssPqPOutentity.getQ3MemoFont()));

            // q3_メモ色
            infoInDto.setQ3MemoColor(getMemoColor(kghCpnRaiAssPqPOutentity.getQ3MemoColor(), colorFlg));

            // インフォーマルな援助量
            infoInDto.setQ3(ReportUtil.nullToEmpty(kghCpnRaiAssPqPOutentity.getQ3()));

            // 出力帳票印刷情報
            ReportCommonChoPrt choPrtList = history.getChoPrtList().get(0);
            // 文書管理番号
            infoInDto.setBunsyoKanriNo(
                    this.getBunsyoKanriNo(syscd, choPrtList.getShokuId(), choPrtList.getSection(), svJigyoId));
        }

        return infoInDto;
    }

    /**
     * U06080_アセスメント表（Ｒ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetRReportServiceInDto getRReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        AssessmentSheetRReportServiceInDto infoInDto = new AssessmentSheetRReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、アセスメント表（Ｒ）情報の取得。===============
         * Ｒ
         */
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        // 【リクエストパラメータ】.記入用シートを印刷するフラグ
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            infoInDto = this.getRDefReportParams(printOption.getKinyuAssType());
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.TRUE);
        } else {
            // 【リクエストパラメータ】.印刷対象履歴リスト
            ReportCommonPrintSubjectHistory printSubjectHistory = model.getPrintSubjectHistoryList().get(0);
            Boolean colorFlg = CommonConstants.COLOR_FLG_ON.equals(printOption.getColorFlg()) ? Boolean.TRUE
                    : Boolean.FALSE;
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getRReportParamsFromAssR(printSubjectHistory, printSet.getShiTeiKubun(),
                    model.getSystemDate(), printSet.getShiTeiDate(), colorFlg, model.getSyscd(),
                    model.getJigyoInfo().getSvJigyoId());
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.FALSE);
        }

        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // 事業者名
        infoInDto.setJigyousha(model.getSvJigyoKnj());
        // 印刷時に色をつけるフラグ
        infoInDto.setColorFlg(CommonConstants.COLOR_FLG_ON.equals(printOption.getColorFlg()) ? 1 : 0);
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｒ）帳票パラメータ取得
     * 
     * @param kinyuAssType アセスメント種別
     * @param infoInDto    帳票用データ詳細
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetRReportServiceInDto getRDefReportParams(String kinyuAssType) throws Exception {

        AssessmentSheetRReportServiceInDto infoInDto = new AssessmentSheetRReportServiceInDto();

        // （※1:居宅版、3:高齢者住宅版の場合は印刷不可）
        infoInDto.setSyubetuKnj(CommonConstants.BLANK_STRING);
        if (ReportConstants.ASSESSMENT_TYPE_FACILITYEDITION == CommonDtoUtil.strValToInt(kinyuAssType)) {
            // アセスメント種別 = 2 の場合、"施設版"
            infoInDto.setSyubetuKnj(ReportConstants.STR_FACILITYEDITION);
        }
        // 指定日（年号）=""
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）=""
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）=""
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）=""
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // 意思決定権_法定後見人等 p1A
        infoInDto.setP1A(CommonConstants.BLANK_STRING);
        // 意思決定権_任意後見 p1B
        infoInDto.setP1B(CommonConstants.BLANK_STRING);
        // 意思決定権_家族等の代理 p1C
        infoInDto.setP1C(CommonConstants.BLANK_STRING);
        // 事前指示_蘇生術 p2A
        infoInDto.setP2A(CommonConstants.BLANK_STRING);
        // 事前指示_挿管 p2B
        infoInDto.setP2B(CommonConstants.BLANK_STRING);
        // 事前指示_入院 p2C
        infoInDto.setP2C(CommonConstants.BLANK_STRING);
        // 事前指示_経管栄養 p2D
        infoInDto.setP2D(CommonConstants.BLANK_STRING);
        // 事前指示_薬剤制限 p2E
        infoInDto.setP2E(CommonConstants.BLANK_STRING);
        // p1_メモ */
        infoInDto.setP1MemoKnj(CommonConstants.BLANK_STRING);
        // p1_メモフォント */
        infoInDto.setP1MemoFont(CommonConstants.STR_12);
        // p1_メモ色 */
        infoInDto.setP1MemoColor(CommonConstants.BLANK_STRING);
        // p2_メモ */
        infoInDto.setP2MemoKnj(CommonConstants.BLANK_STRING);
        // P2_メモフォント */
        infoInDto.setP2MemoFont(CommonConstants.STR_12);
        // p2_メモ色 */
        infoInDto.setP2MemoColor(CommonConstants.BLANK_STRING);
        // 退所の可能性_利用者 */
        infoInDto.setR1A(CommonConstants.BLANK_STRING);
        // 退所の可能性_支援者 */
        infoInDto.setR1B(CommonConstants.BLANK_STRING);
        // 退所の可能性_家 */
        infoInDto.setR1C(CommonConstants.BLANK_STRING);
        // 退所の予測期間 */
        infoInDto.setR2(CommonConstants.BLANK_STRING);
        // r1_メモ */
        infoInDto.setR1MemoKnj(CommonConstants.BLANK_STRING);
        // r1_メモフォント */
        infoInDto.setR1MemoFont(CommonConstants.STR_12);
        // r1_メモ色 */
        infoInDto.setR1MemoColor(CommonConstants.BLANK_STRING);
        // r2_メモ */
        infoInDto.setR2MemoKnj(CommonConstants.BLANK_STRING);
        // r2_メモフォント */
        infoInDto.setR2MemoFont(CommonConstants.STR_12);
        // r2_メモ色 */
        infoInDto.setR2MemoColor(CommonConstants.BLANK_STRING);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｒ）帳票パラメータ取得
     * 
     * @param history     印刷対象履歴
     * @param shiTeiKubun 指定日印刷区分
     * @param systemDate  システム日付
     * @param shiTeiDate  西暦日付
     * @param colorFlg    印刷時に色をつけるフラグ
     * @param syscd       システムコード
     * @param svJigyoId   サービス事業者ID
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetRReportServiceInDto getRReportParamsFromAssR(ReportCommonPrintSubjectHistory history,
            String shiTeiKubun,
            String systemDate, String shiTeiDate, Boolean colorFlg, String syscd, String svJigyoId) throws Exception {

        AssessmentSheetRReportServiceInDto infoInDto = new AssessmentSheetRReportServiceInDto();

        // API定義の処理「4.」
        Pair<Integer, String> syubetuPair = this.getSyubetuByRaiId(CommonDtoUtil.strValToInt(history.getRaiId()));
        // 2.1. 「4.1」で取得した調査アセスメント種別=2の場合、下記のアセスメント表（Ｒ）情報取得のDAOを利用し、アセスメント表（Ｒ）情報を取得する。
        if (ReportConstants.ASSESSMENT_TYPE_FACILITYEDITION == syubetuPair.getLeft()) {
            // 2.1. アセスメント表（Ｒ）情報を取得する。
            KghCpnRaiAssPrPByCriteriaInEntity criteria = new KghCpnRaiAssPrPByCriteriaInEntity();
            criteria.setAnRaiId(CommonDtoUtil.strValToInt(history.getRaiId()));
            List<KghCpnRaiAssPrPOutEntity> assRList = raiRrkKghCpnRaiAssPRSelectMapper
                    .findKghCpnRaiAssPrPByCriteria(criteria);
            // 2.2.「2.1.」で取得したアセスメント表（Ｒ）情報の件数をワークアセスメント表（Ｒ）情報.総件数に設定する。
            if (CollectionUtils.isNotEmpty(assRList)) {
                // 2.2.1 総件数 > 0 件の場合
                KghCpnRaiAssPrPOutEntity entity = assRList.get(0);

                // API定義の処理「3.1」
                Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());

                /*
                 * =============== 5.帳票用データを設定===============
                 * 
                 */

                // 調査アセスメント種別名
                // （※1:居宅版、3:高齢者住宅版の場合は印刷不可）
                infoInDto.setSyubetuKnj(syubetuPair.getRight());
                // 指定日（年号）=""
                ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(shiTeiKubun, shiTeiDate, systemDate);
                infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
                // 指定日（年）=""
                infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
                // 指定日（月）=""
                infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
                // 指定日（日）=""
                infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());
                // 氏名（姓）
                infoInDto.setName1Knj(userPair.getLeft());
                // 氏名（名）
                infoInDto.setName2Knj(userPair.getRight());
                // 意思決定権_法定後見人等
                infoInDto.setP1A(ReportUtil.nullToEmpty(entity.getP1A()));
                // 意思決定権_任意後見
                infoInDto.setP1B(ReportUtil.nullToEmpty(entity.getP1B()));
                // 意思決定権_家族等の代理
                infoInDto.setP1C(ReportUtil.nullToEmpty(entity.getP1C()));
                // 事前指示_蘇生術
                infoInDto.setP2A(ReportUtil.nullToEmpty(entity.getP2A()));
                // 事前指示_挿管
                infoInDto.setP2B(ReportUtil.nullToEmpty(entity.getP2B()));
                // 事前指示_入院
                infoInDto.setP2C(ReportUtil.nullToEmpty(entity.getP2C()));
                // 事前指示_経管栄養
                infoInDto.setP2D(ReportUtil.nullToEmpty(entity.getP2D()));
                // 事前指示_薬剤制限
                infoInDto.setP2E(ReportUtil.nullToEmpty(entity.getP2E()));
                // p1_メモ
                infoInDto.setP1MemoKnj(ReportUtil.nullToEmpty(entity.getP1MemoKnj()));
                // p2_メモ
                infoInDto.setP2MemoKnj(ReportUtil.nullToEmpty(entity.getP2MemoKnj()));
                // 退所の可能性_利用者
                infoInDto.setR1A(ReportUtil.nullToEmpty(entity.getR1A()));
                // 退所の可能性_支援者
                infoInDto.setR1B(ReportUtil.nullToEmpty(entity.getR1B()));
                // 退所の可能性_家
                infoInDto.setR1C(ReportUtil.nullToEmpty(entity.getR1C()));
                // 退所の予測期間
                infoInDto.setR2(ReportUtil.nullToEmpty(entity.getR2()));
                // r1_メモ
                infoInDto.setR1MemoKnj(ReportUtil.nullToEmpty(entity.getR1MemoKnj()));
                // r2_メモ
                infoInDto.setR2MemoKnj(ReportUtil.nullToEmpty(entity.getR2MemoKnj()));
                // p1_メモフォント
                infoInDto.setP1MemoFont(getMemoFont(entity.getP1MemoFont()));
                // p2_メモフォント
                infoInDto.setP2MemoFont(getMemoFont(entity.getP2MemoFont()));
                // r1_メモフォント */
                infoInDto.setR1MemoFont(getMemoFont(entity.getR1MemoFont()));
                // r2_メモフォント */
                infoInDto.setR2MemoFont(getMemoFont(entity.getR2MemoFont()));
                // p1_メモ色
                infoInDto.setP1MemoColor(getMemoColor(entity.getP1MemoColor(), colorFlg));
                // p2_メモ色
                infoInDto.setP2MemoColor(getMemoColor(entity.getP2MemoColor(), colorFlg));
                // r1_メモ色
                infoInDto.setR1MemoColor(getMemoColor(entity.getR1MemoColor(), colorFlg));
                // r2_メモ色
                infoInDto.setR2MemoColor(getMemoColor(entity.getR2MemoColor(), colorFlg));

                // 出力帳票印刷情報
                ReportCommonChoPrt choPrtList = history.getChoPrtList().get(0);
                // 文書管理番号
                infoInDto.setBunsyoKanriNo(
                        this.getBunsyoKanriNo(syscd, choPrtList.getShokuId(), choPrtList.getSection(), svJigyoId));
            }
        }

        return infoInDto;
    }

    /**
     * U06080_アセスメント表（Ｓ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetSReportServiceInDto getSReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));
        // 帳票用データ詳細
        AssessmentSheetSReportServiceInDto infoInDto = new AssessmentSheetSReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            infoInDto = this.getSDefReportParams(printOption);
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.TRUE);
        } else {
            // 【リクエストパラメータ】.印刷設定
            ReportCommonPrintSet printSet = model.getPrintSet();
            // 【リクエストパラメータ】.印刷対象履歴リスト
            ReportCommonPrintSubjectHistory printSubjectHistory = model.getPrintSubjectHistoryList().get(0);
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getSReportParamsFromAssS(printSubjectHistory, printSet.getShiTeiKubun(),
                    model.getSystemDate(), printSet.getShiTeiDate(), printOption.getColorFlg(), model.getSyscd(),
                    model.getJigyoInfo().getSvJigyoId());
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.FALSE);
        }
        // 印刷時に色をつけるフラグ
        infoInDto.setColorFlg(CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg()) ? 1 : 2);
        // 帳票タイトル←"インターライ方式ケアアセスメント表"
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 事業者名←リクエストパラメータ.データ.事業者名
        infoInDto.setJigyosha(model.getSvJigyoKnj());
        // 指定日印刷区分←リクエストパラメータ.データ.印刷設定.指定日印刷区分
        infoInDto.setPrDate(CommonDtoUtil.strValToInt(model.getPrintSet().getShiTeiKubun()));
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｓ）帳票パラメータ取得
     * 
     * @param kinyuAssType 印刷オプション
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetSReportServiceInDto getSDefReportParams(ReportAssessmentSheetPrintOption printOption) {

        // 帳票用データ詳細
        AssessmentSheetSReportServiceInDto infoInDto = new AssessmentSheetSReportServiceInDto();

        // リクエストパラメータ.データ.印刷オプション.記入用シートアセスメント種別=1:居宅版の場合
        if (ReportConstants.ASSESSMENT_TYPE_HOMEEDITION == CommonDtoUtil.strValToInt(printOption.getKinyuAssType())) {
            // "居宅版"は調査アセスメント種別名に設定する。
            infoInDto.setSyubetuKnj(ReportConstants.STR_HOMEEDITION);
        } else {
            infoInDto.setSyubetuKnj(CommonConstants.BLANK_STRING);
        }
        // 調査アセスメント種別
        infoInDto.setAssType(CommonDtoUtil.strValToInt(printOption.getKinyuAssType()));
        // 指定日（年号）=""
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）=""
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）=""
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）=""
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);

        // 氏名（姓）=""
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）=""
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // 屋内の環境_家の荒廃=""
        infoInDto.setS1A(CommonConstants.BLANK_STRING);
        // 屋内の環境_不潔=""
        infoInDto.setS1B(CommonConstants.BLANK_STRING);
        // 屋内の環境_冷暖房=""
        infoInDto.setS1C(CommonConstants.BLANK_STRING);
        // 屋内の環境_安全=""
        infoInDto.setS1D(CommonConstants.BLANK_STRING);
        // 屋内の環境_手段=""
        infoInDto.setS1E(CommonConstants.BLANK_STRING);
        // バリアフリー仕様=""
        infoInDto.setS2(CommonConstants.BLANK_STRING);
        // 周辺環境_緊急通報=""
        infoInDto.setS3A(CommonConstants.BLANK_STRING);
        // 周辺環境_店=""
        infoInDto.setS3B(CommonConstants.BLANK_STRING);
        // 周辺環境_配達=""
        infoInDto.setS3C(CommonConstants.BLANK_STRING);
        // 経済状況=""
        infoInDto.setS4(CommonConstants.BLANK_STRING);
        // s1_メモ=""
        infoInDto.setS1MemoKnj(CommonConstants.BLANK_STRING);
        // s1_メモフォント="12"
        infoInDto.setS1MemoFont(CommonConstants.STR_12);
        // s1_メモ色=""
        infoInDto.setS1MemoColor(CommonConstants.BLANK_STRING);
        // s2_メモ=""
        infoInDto.setS2MemoKnj(CommonConstants.BLANK_STRING);
        // s2_メモフォント="12"
        infoInDto.setS2MemoFont(CommonConstants.STR_12);
        // s2_メモ色=""
        infoInDto.setS2MemoColor(CommonConstants.BLANK_STRING);
        // s3_メモ=""
        infoInDto.setS3MemoKnj(CommonConstants.BLANK_STRING);
        // s3_メモフォント="12"
        infoInDto.setS3MemoFont(CommonConstants.STR_12);
        // s3_メモ色=""
        infoInDto.setS3MemoColor(CommonConstants.BLANK_STRING);
        // s4_メモ=""
        infoInDto.setS4MemoKnj(CommonConstants.BLANK_STRING);
        // s4_メモフォント="12"
        infoInDto.setS4MemoFont(CommonConstants.STR_12);
        // s4_メモ色=""
        infoInDto.setS4MemoColor(CommonConstants.BLANK_STRING);
        // 文書管理番号=""
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｓ）帳票パラメータ取得
     * 
     * @param history     印刷対象履歴
     * @param shiTeiKubun 指定日印刷区分
     * @param systemDate  システム日付
     * @param shiTeiDate  西暦日付
     * @param colorFlg    印刷時に色をつけるフラグ
     * @param syscd       システムコード
     * @param svJigyoId   サービス事業者ID
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private AssessmentSheetSReportServiceInDto getSReportParamsFromAssS(ReportCommonPrintSubjectHistory history,
            String shiTeiKubun,
            String systemDate, String shiTeiDate, String colorFlg, String syscd, String svJigyoId) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetSReportServiceInDto infoInDto = new AssessmentSheetSReportServiceInDto();

        // 2.1. アセスメント表（S）情報を取得する。
        KghCpnRaiAssSPByCriteriaInEntity kghCpnRaiAssSPByCriteriaInEntity = new KghCpnRaiAssSPByCriteriaInEntity();
        // アセスメントID ← リクエストパラメータ.アセスメントID
        kghCpnRaiAssSPByCriteriaInEntity.setAnRaiId(CommonDtoUtil.strValToInt(history.getRaiId()));
        List<KghCpnRaiAssSPOutEntity> assSList = raiRrkKghCpnRaiAssSSelectMapper
                .findKghCpnRaiAssSPByCriteria(kghCpnRaiAssSPByCriteriaInEntity);
        // 2.2.「2.1.」で取得したアセスメント表（S）情報の件数をワークアセスメント表（S）情報.総件数に設定する。
        if (CollectionUtils.isNotEmpty(assSList)) {
            // 2.2.1 総件数 > 0 件の場合
            KghCpnRaiAssSPOutEntity entity = assSList.get(0);
            /*
             * ===============3. 利用者基本情報を取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());

            /*
             * ===============4.アセスメント種別の取得===============
             * 
             */
            Pair<Integer, String> syubetPair = this.getSyubetuByRaiId(CommonDtoUtil.strValToInt(history.getRaiId()));

            /*
             * =============== 5.帳票用データを設定===============
             * 
             */
            // ・API定義の処理「4.」の調査アセスメント種別=1:居宅版の場合
            // "居宅版"

            // リクエストパラメータ.データ.印刷オプション.記入用シートアセスメント種別=1:居宅版の場合
            if (ReportConstants.ASSESSMENT_TYPE_HOMEEDITION == syubetPair.getLeft()) {
                // "居宅版"は調査アセスメント種別名に設定する。
                infoInDto.setSyubetuKnj(ReportConstants.STR_HOMEEDITION);
            }
            infoInDto.setAssType(syubetPair.getLeft());
            // ・リクエストパラメータ.データ.印刷設定.指定日印刷区分＝2:指定日印刷の場合、
            // 共通関数補足の「4.1」の和暦日付
            // ・リクエストパラメータ.データ.印刷設定.指定日印刷区分＝3:日付空欄印刷の場合、
            // 共通関数補足の「1.1」のシステム日付元号
            // ・リクエストパラメータ.データ.印刷設定.指定日印刷区分＝1:印刷しないの場合
            // 指定日=""
            // 指定日
            // 指定日（年号）
            ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(shiTeiKubun, shiTeiDate, systemDate);
            infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
            // 指定日（年）
            infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
            // 指定日（月）
            infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
            // 指定日（日）
            infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

            // 氏名（姓）=API定義の処理「3.1」の氏名（姓）
            infoInDto.setName1Knj(userPair.getLeft());
            // 氏名（名）=API定義の処理「3.1」の氏名（名）
            infoInDto.setName2Knj(userPair.getRight());
            // 屋内の環境_家の荒廃
            infoInDto.setS1A(CommonDtoUtil.objValToString(entity.getS1A()));
            // 屋内の環境_不潔
            infoInDto.setS1B(CommonDtoUtil.objValToString(entity.getS1B()));
            // 屋内の環境_冷暖房
            infoInDto.setS1C(CommonDtoUtil.objValToString(entity.getS1C()));
            // 屋内の環境_安全
            infoInDto.setS1D(CommonDtoUtil.objValToString(entity.getS1D()));
            // 屋内の環境_手段
            infoInDto.setS1E(CommonDtoUtil.objValToString(entity.getS1E()));
            // バリアフリー仕様
            infoInDto.setS2(CommonDtoUtil.objValToString(entity.getS2()));
            // 周辺環境_緊急通報
            infoInDto.setS3A(CommonDtoUtil.objValToString(entity.getS3A()));
            // 周辺環境_店
            infoInDto.setS3B(CommonDtoUtil.objValToString(entity.getS3B()));
            // 周辺環境_配達
            infoInDto.setS3C(CommonDtoUtil.objValToString(entity.getS3C()));
            // 経済状況
            infoInDto.setS4(CommonDtoUtil.objValToString(entity.getS4()));
            // s1_メモ
            infoInDto.setS1MemoKnj(entity.getS1MemoKnj());
            // ・API定義の処理「2.1」のs1_メモフォント=nullの場合、
            // s1_メモフォント="12"
            // ・上記以外の場合、
            // s1_メモフォント=API定義の処理「2.1」のs1_メモフォント
            infoInDto.setS1MemoFont(getMemoFont(entity.getS1MemoFont()));
            // ・リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            // ・API定義の処理「2.1」のs1_メモ色=nullの場合、
            // s1_メモ色＝"#000000"
            // ・上記以外の場合、
            // s1_メモ色=共通関数補足の「3.1」の十六進数RGB値
            // ・上記以外の場合、
            // s1_メモ色＝"#000000"
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            Boolean colorFlg1 = false;
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(colorFlg)
                    || CommonConstants.STR_1.equals(colorFlg)) {
                colorFlg1 = true;
            }
            infoInDto.setS1MemoColor(getMemoColor(entity.getS1MemoColor(), colorFlg1));
            // s2_メモ=API定義の処理「2.1」のs2_メモ
            infoInDto.setS2MemoFont(CommonDtoUtil.objValToString(entity.getS2MemoFont()));
            // ・API定義の処理「2.1」のs2_メモフォント=nullの場合、
            // s2_メモフォント="12"
            // ・上記以外の場合、
            // s2_メモフォント=API定義の処理「2.1」のs2_メモフォント
            infoInDto.setS2MemoFont(getMemoFont(entity.getS2MemoFont()));
            // ・リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            // ・API定義の処理「2.1」のs2_メモ色=nullの場合、
            // s2_メモ色＝"#000000"
            // ・上記以外の場合、
            // s2_メモ色=共通関数補足の「3.1」の十六進数RGB値
            // ・上記以外の場合、
            // s2_メモ色＝"#000000"
            infoInDto.setS2MemoColor(colorFlg1 ? (entity.getS2MemoColor() == null ? ReportConstants.COLOR_BLANK
                    : colorConvertLogic.bgrToHexRgb(
                            entity.getS2MemoColor()))
                    : ReportConstants.COLOR_BLANK);
            // s3_メモ=API定義の処理「2.1」のs3_メモ
            infoInDto.setS3MemoFont(CommonDtoUtil.objValToString(entity.getS3MemoFont()));
            // ・API定義の処理「2.1」のs3_メモフォント=nullの場合、
            // s3_メモフォント="12"
            // ・上記以外の場合、
            // s3_メモフォント=API定義の処理「2.1」のs3_メモフォント
            infoInDto.setS3MemoFont(getMemoFont(entity.getS3MemoFont()));
            // ・API定義の処理「2.1」のs3_メモ色=nullの場合、
            // s3_メモ色＝"#000000"
            // ・上記以外の場合、
            // s3_メモ色=共通関数補足の「3.1」の十六進数RGB値
            // ・上記以外の場合、
            // s3_メモ色＝"#000000"
            infoInDto.setS3MemoColor(colorFlg1 ? (entity.getS3MemoColor() == null ? ReportConstants.COLOR_BLANK
                    : colorConvertLogic.bgrToHexRgb(
                            entity.getS3MemoColor()))
                    : ReportConstants.COLOR_BLANK);
            // s4_メモ=API定義の処理「2.1」のs4_メモ
            infoInDto.setS3MemoFont(CommonDtoUtil.objValToString(entity.getS3MemoFont()));
            // ・API定義の処理「2.1」のs4_メモフォント=nullの場合、
            // s4_メモフォント="12"
            // ・上記以外の場合、
            // s4_メモフォント=API定義の処理「2.1」のs4_メモフォント
            infoInDto.setS4MemoFont(getMemoFont(entity.getS4MemoFont()));
            // ・リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
            // ・API定義の処理「2.1」のs4_メモ色=nullの場合、
            // s4_メモ色＝"#000000"
            // ・上記以外の場合、
            // s4_メモ色=共通関数補足の「3.1」の十六進数RGB値
            // ・上記以外の場合、
            // s4_メモ色＝"#000000"
            infoInDto.setS4MemoColor(colorFlg1 ? (entity.getS4MemoColor() == null ? ReportConstants.COLOR_BLANK
                    : colorConvertLogic.bgrToHexRgb(
                            entity.getS4MemoColor()))
                    : ReportConstants.COLOR_BLANK);

            // 出力帳票印刷情報
            ReportCommonChoPrt choPrtList = history.getChoPrtList().get(0);
            // 文書管理番号
            infoInDto.setBunsyoKanriNo(
                    this.getBunsyoKanriNo(syscd, choPrtList.getShokuId(), choPrtList.getSection(), svJigyoId));
        }

        return infoInDto;

    }

    /**
     * U06080_アセスメント表（Ｔ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetTReportServiceInDto getTReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        AssessmentSheetTReportServiceInDto infoInDto = new AssessmentSheetTReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、アセスメント表（Ｔ）情報の取得。===============
         * 
         */
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            infoInDto = this.getTDefReportParams(model, outDto);
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.TRUE);
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getTReportParamsFromAssT(model, outDto);
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.FALSE);
        }
        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 事業者名
        infoInDto.setJigyousha(model.getSvJigyoKnj());
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（Ｔ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetTReportServiceInDto getTDefReportParams(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetTReportServiceInDto infoInDto = new AssessmentSheetTReportServiceInDto();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(ReportConstants.ASSESSMENT_TYPE_HOMEEDITION == CommonDtoUtil
                .strValToInt(ReportUtil.nullToEmpty(printOption.getKinyuAssType()))
                        ? ReportConstants.STR_HOMEEDITION
                        : CommonConstants.BLANK_STRING);
        // 指定日（年号）
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // t1_メモ
        infoInDto.setT1MemoKnj(CommonConstants.BLANK_STRING);
        // t1_メモフォント
        infoInDto.setT1MemoFont(CommonConstants.STR_12);
        // t1_メモ色
        infoInDto.setT1MemoColor(CommonConstants.BLANK_STRING);
        // ケア目標の達成
        infoInDto.setT1(CommonConstants.BLANK_STRING);
        // t2_メモ
        infoInDto.setT2MemoKnj(CommonConstants.BLANK_STRING);
        // t2_メモフォント
        infoInDto.setT2MemoFont(CommonConstants.STR_12);
        // t2_メモ色
        infoInDto.setT2MemoColor(CommonConstants.BLANK_STRING);
        // 自立度の変化
        infoInDto.setT2(CommonConstants.BLANK_STRING);
        // t3_メモ
        infoInDto.setT3MemoKnj(CommonConstants.BLANK_STRING);
        // t3_メモフォント
        infoInDto.setT3MemoFont(CommonConstants.STR_12);
        // t3_メモ色
        infoInDto.setT3MemoColor(CommonConstants.BLANK_STRING);
        // 自立していたADLの数
        infoInDto.setT3(CommonConstants.BLANK_STRING);
        // t4_メモ
        infoInDto.setT4MemoKnj(CommonConstants.BLANK_STRING);
        // t4_メモフォント
        infoInDto.setT4MemoFont(CommonConstants.STR_12);
        // t4_メモ色
        infoInDto.setT4MemoColor(CommonConstants.BLANK_STRING);
        // 自立していたIADLの数
        infoInDto.setT4(CommonConstants.BLANK_STRING);
        // t5_メモ
        infoInDto.setT5MemoKnj(CommonConstants.BLANK_STRING);
        // t5_メモフォント
        infoInDto.setT5MemoFont(CommonConstants.STR_12);
        // t5_メモ色
        infoInDto.setT5MemoColor(CommonConstants.BLANK_STRING);
        // 増悪原因の起こった時期
        infoInDto.setT5(CommonConstants.BLANK_STRING);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（Ｔ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetTReportServiceInDto getTReportParamsFromAssT(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetTReportServiceInDto infoInDto = new AssessmentSheetTReportServiceInDto();

        // 【リクエストパラメータ】.印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 指定日（年号）
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
        // 指定日（年）
        infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
        // 指定日（月）
        infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
        // 指定日（日）
        infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

        // 2.1. アセスメント表（Ｔ）情報を取得する。
        KghCpnRaiAssTPByCriteriaInEntity kghCpnRaiAssTPByCriteriaInEntity = new KghCpnRaiAssTPByCriteriaInEntity();
        kghCpnRaiAssTPByCriteriaInEntity
                .setAnRaiId(Integer.valueOf(
                        printSubjectHistoryList.get(0).getRaiId()));
        List<KghCpnRaiAssTPOutEntity> kghCpnRaiAssTPOutEntities = raiRrkKghCpnRaiAssTSelectMapper
                .findKghCpnRaiAssTPByCriteria(kghCpnRaiAssTPByCriteriaInEntity);

        // 2.2.「2.1.」で取得したアセスメント表（Ｔ）情報の件数をワークアセスメント表（Ｔ）情報.総件数に設定する。
        if (CollectionUtils.isNotEmpty(kghCpnRaiAssTPOutEntities)) {
            // 2.2.1 総件数 > 0 件の場合
            KghCpnRaiAssTPOutEntity entity = kghCpnRaiAssTPOutEntities.get(0);
            /*
             * ===============3. 利用者基本情報を取得===============
             * 
             */
            Pair<String, String> userPair = this.getUserNamesPair(entity.getUserid());
            // 氏名（姓）
            infoInDto.setName1Knj(ReportUtil.nullToEmpty(userPair.getLeft()));
            // 氏名（名）
            infoInDto.setName2Knj(ReportUtil.nullToEmpty(userPair.getRight()));
            /*
             * ===============4.アセスメント種別の取得===============
             * 
             */
            Integer syubetu = this.getSyubetuByRaiId(
                    CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId())).getLeft();
            String syubetuKnj = this.getSyubetuByRaiId(
                    CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getRaiId())).getRight();
            /*
             * =============== 5.帳票用データを設定===============
             * 
             */
            // 調査アセスメント種別名
            infoInDto.setSyubetuKnj(
                    ReportConstants.ASSESSMENT_TYPE_HOMEEDITION == syubetu ? syubetuKnj : CommonConstants.BLANK_STRING);
            // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ
            Boolean colorFlg = false;
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getColorFlg())
                    || CommonConstants.STR_1.equals(printOption.getColorFlg())) {
                colorFlg = true;
            }
            // t1_メモ
            infoInDto.setT1MemoKnj(ReportUtil.nullToEmpty(entity.getT1MemoKnj()));
            // t1_メモフォント
            infoInDto.setT1MemoFont(getMemoFont(entity.getT1MemoFont()));
            // t1_メモ色
            infoInDto.setT1MemoColor(getMemoColor(entity.getT1MemoColor(), colorFlg));
            // ケア目標の達成
            infoInDto.setT1(ReportUtil.nullToEmpty(entity.getT1()));
            // t2_メモ
            infoInDto.setT2MemoKnj(ReportUtil.nullToEmpty(entity.getT1MemoKnj()));
            // t2_メモフォント
            infoInDto.setT2MemoFont(getMemoFont(entity.getT1MemoFont()));
            // t2_メモ色
            infoInDto.setT2MemoColor(getMemoColor(entity.getT1MemoColor(), colorFlg));
            // 自立度の変化
            infoInDto.setT2(ReportUtil.nullToEmpty(entity.getT2()));
            // t3_メモ
            infoInDto.setT3MemoKnj(ReportUtil.nullToEmpty(entity.getT3MemoKnj()));
            // t3_メモフォント
            infoInDto.setT3MemoFont(getMemoFont(entity.getT3MemoFont()));
            // t3_メモ色
            infoInDto.setT3MemoColor(getMemoColor(entity.getT3MemoColor(), colorFlg));
            // 自立していたADLの数
            infoInDto.setT3(ReportUtil.nullToEmpty(entity.getT3()));
            // t4_メモ
            infoInDto.setT4MemoKnj(ReportUtil.nullToEmpty(entity.getT4MemoKnj()));
            // t4_メモフォント
            infoInDto.setT4MemoFont(getMemoFont(entity.getT4MemoFont()));
            // t4_メモ色
            infoInDto.setT4MemoColor(getMemoColor(entity.getT4MemoColor(), colorFlg));
            // 自立していたIADLの数
            infoInDto.setT4(ReportUtil.nullToEmpty(entity.getT4()));
            // t5_メモ
            infoInDto.setT5MemoKnj(ReportUtil.nullToEmpty(entity.getT5MemoKnj()));
            // t5_メモフォント
            infoInDto.setT5MemoFont(getMemoFont(entity.getT5MemoFont()));
            // t5_メモ色
            infoInDto.setT5MemoColor(getMemoColor(entity.getT5MemoColor(), colorFlg));
            // 増悪原因の起こった時期
            infoInDto.setT5(ReportUtil.nullToEmpty(entity.getT5()));

            /*
             * =============== 共通関数補足 2.文書番号情報取得処理===============
             * 
             */
            // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01 関数名：GetF3GkProfile
            // を利用し、設定の読込（システム環境以外の設定）情報を取得する。
            // 【リクエストパラメータ】.システムコード
            String syscd = model.getSyscd();
            // 職員ID
            String shokuId = printSubjectHistoryList.get(0).getChoPrtList().get(0).getShokuId();
            // セクション
            String section = printSubjectHistoryList.get(0).getChoPrtList().get(0).getSection();
            // 【リクエストパラメータ】.事業者ID
            String svJigyoId = model.getJigyoInfo().getSvJigyoId();

            String bunsyoKanriNo = this.getBunsyoKanriNo(syscd, shokuId, section, svJigyoId);
            // 文書管理番号
            infoInDto.setBunsyoKanriNo(bunsyoKanriNo);
        }
        return infoInDto;
    }

    /**
     * U06080_アセスメント表（ＵⅤ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentSheetUVReportServiceInDto getUVReportParameters(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        AssessmentSheetUVReportServiceInDto infoInDto = new AssessmentSheetUVReportServiceInDto();

        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、アセスメント表（Ⅴ）情報の取得。===============
         * 
         */
        // 【リクエストパラメータ】.事業者名
        String svJigyoKnj = model.getSvJigyoKnj();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())
                || CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            infoInDto = this.getUVDefReportParams(printOption.getKinyuAssType());
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.TRUE);
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getUVReportParamsFromAssUV(model, outDto);
            // 記入用シートを印刷するフラグ
            infoInDto.setEmptyFlg(Boolean.FALSE);
        }
        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.ASSESSMENT_TITLE);
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // 事業者名
        infoInDto.setJigyousha(ReportUtil.nullToEmpty(svJigyoKnj));
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06080_アセスメント表（ＵⅤ）帳票パラメータ取得
     * 
     * @param kinyuAssType アセスメント種別
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetUVReportServiceInDto getUVDefReportParams(String kinyuAssType) throws Exception {
        // 帳票用データ詳細
        AssessmentSheetUVReportServiceInDto infoInDto = new AssessmentSheetUVReportServiceInDto();
        // 調査アセスメント種別
        infoInDto.setAssType(CommonDtoUtil.strValToInt(kinyuAssType));
        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuByAssType(CommonDtoUtil.strValToInt(kinyuAssType)));
        // 指定日（年号）
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 氏名（姓）
        infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
        // 氏名（名）
        infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
        // p1_メモ
        infoInDto.setP1MemoKnj(CommonConstants.BLANK_STRING);
        // p1_メモフォント
        infoInDto.setP1MemoFont(CommonConstants.STR_12);
        // p1_メモ色
        infoInDto.setP1MemoColor(CommonConstants.BLANK_STRING);
        // 意思決定権_法定後見人等
        infoInDto.setP1A(CommonConstants.BLANK_STRING);
        // 終了日（年号）
        infoInDto.setU1YmdGG(CommonConstants.FULL_WIDTH_SPACE + CommonConstants.FULL_WIDTH_SPACE);
        // 終了日（年）
        infoInDto.setU1YmdYY(CommonConstants.FULL_WIDTH_SPACE);
        // 終了日（月）
        infoInDto.setU1YmdMM(CommonConstants.FULL_WIDTH_SPACE);
        // 終了日（日）
        infoInDto.setU1YmdDD(CommonConstants.FULL_WIDTH_SPACE);
        // u2_メモ
        infoInDto.setU2MemoKnj(CommonConstants.BLANK_STRING);
        // u2_メモフォント
        infoInDto.setU2MemoFont(CommonConstants.STR_12);
        // u2_メモ色
        infoInDto.setU2MemoColor(CommonConstants.BLANK_STRING);
        // 今後の居住場所
        infoInDto.setU2(CommonConstants.BLANK_STRING);
        // u3_メモ
        infoInDto.setU3MemoKnj(CommonConstants.BLANK_STRING);
        // u3_メモフォント
        infoInDto.setU3MemoFont(CommonConstants.STR_12);
        // u3_メモ色
        infoInDto.setU3MemoColor(CommonConstants.BLANK_STRING);
        // サービスを受ける予定
        infoInDto.setU3(CommonConstants.BLANK_STRING);
        // アセスメント完成日（年号）
        infoInDto.setV2YmdGG(CommonConstants.FULL_WIDTH_SPACE + CommonConstants.FULL_WIDTH_SPACE);
        // アセスメント完成日（年）
        infoInDto.setV2YmdYY(CommonConstants.FULL_WIDTH_SPACE);
        // アセスメント完成日（月）
        infoInDto.setV2YmdMM(CommonConstants.FULL_WIDTH_SPACE);
        // アセスメント完成日（日）
        infoInDto.setV2YmdDD(CommonConstants.FULL_WIDTH_SPACE);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(CommonConstants.BLANK_STRING);
        // アセスメント担当者（姓）
        infoInDto.setShokuin1Knj(CommonConstants.BLANK_STRING);
        // アセスメント担当者（名）
        infoInDto.setShokuin2Knj(CommonConstants.BLANK_STRING);

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06080_アセスメント表（ＵⅤ）帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private AssessmentSheetUVReportServiceInDto getUVReportParamsFromAssUV(AssessmentSheetReportParameterModel model,
            AssessmentSheetReportServiceOutDto outDto) throws Exception {

        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷オプション
        ReportAssessmentSheetPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();

        // 帳票用データ詳細
        AssessmentSheetUVReportServiceInDto infoInDto = new AssessmentSheetUVReportServiceInDto();

        // 「2.1」~「2.2」で取得した利用者ID
        String userId = "";
        // 「2.1」~「2.2」で取得した調査者
        String shokuId = "";
        // 「2.1」~「2.2」で取得した終了日
        List<String> u1YmdList = new ArrayList<>();
        // 「2.1」~「2.2」でアセスメント完成日
        List<String> v2YmdList = new ArrayList<>();
        // 「2.1」で取得した件数
        Integer kensuV = 0;
        // 「2.2」で取得した件数
        Integer kensuPUV = 0;
        // 「2.1」~「2.2」で取得した総件数
        Integer kensuAll = 0;

        /*
         * ===============4.アセスメント種別の取得===============
         * 
         */
        Pair<Integer, String> syubetPair = this
                .getSyubetuByRaiId(Integer.valueOf(printSubjectHistoryList.get(0).getRaiId()));
        // 調査アセスメント種別
        infoInDto.setAssType(syubetPair.getLeft());

        // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ
        Boolean colorFlg = false;
        if (ReportConstants.COLOR_FLG_TRUE.equals(printOption.getColorFlg())) {
            colorFlg = true;
        }
        // 2.1. 「4.1」で調査アセスメント種別=1または2の場合
        if ((ReportConstants.ASSESSMENT_TYPE_HOMEEDITION == syubetPair.getLeft()
                || ReportConstants.ASSESSMENT_TYPE_FACILITYEDITION == syubetPair.getLeft())) {

            // 下記のアセスメント表（ＵＶ）情報取得のDAOを利用
            KghCpnRaiAssUvPByCriteriaInEntity kghCpnRaiAssUvPByCriteriaInEntity = new KghCpnRaiAssUvPByCriteriaInEntity();
            // アセスメントID
            kghCpnRaiAssUvPByCriteriaInEntity.setAnRaiId(CommonDtoUtil
                    .strValToInt(printSubjectHistoryList.get(0).getRaiId()));
            // アセスメント表（ＵＶ）情報を取得
            List<KghCpnRaiAssUvPOutEntity> kghCpnRaiAssUvPOutEntity = this.raiRrkKghCpnRaiAssUvSelectMapper
                    .findKghCpnRaiAssUvPByCriteria(
                            kghCpnRaiAssUvPByCriteriaInEntity);
            // 件数を取得
            kensuV = kghCpnRaiAssUvPOutEntity.size();
            KghCpnRaiAssUvPOutEntity entity = kghCpnRaiAssUvPOutEntity.get(0);
            if (CollectionUtils.isNotEmpty(kghCpnRaiAssUvPOutEntity)) {
                // 利用者ID
                userId = ReportUtil.nullToEmpty(entity.getUserid());
                // 調査者
                shokuId = ReportUtil.nullToEmpty(entity.getAssShokuId());
                // 終了日
                u1YmdList = this.getDate(ReportUtil.nullToEmpty(entity.getU1Ymd()));

                // u2_メモ
                infoInDto.setU2MemoKnj(ReportUtil.nullToEmpty(entity.getU2MemoKnj()));
                // API定義の処理「2.1」のu2_メモフォント=空白の場合、u2_メモフォント="12"、上記以外の場合、u2_メモフォント
                infoInDto.setU2MemoFont(getMemoFont(entity.getU2MemoFont()));
                // リクエストパラメータ.印刷時に色をつけるフラグ= true の場合
                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のu2_メモ色=空白の場合
                // u2_メモ色＝#000000
                // 上記以外の場合
                // u2_メモ色
                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= false の場合
                // u2_メモ色＝#000000
                infoInDto.setU2MemoColor(getMemoColor(entity.getU2MemoColor(), colorFlg));
                // 今後の居住場所
                infoInDto.setU2(ReportUtil.nullToEmpty(entity.getU2()));
                // u3_メモ
                infoInDto.setU3MemoKnj(ReportUtil.nullToEmpty(entity.getU3MemoKnj()));
                // API定義の処理「2.1」のu3_メモフォント=空白の場合、u3_メモフォント="12"、上記以外の場合、u3_メモフォント
                infoInDto.setU3MemoFont(getMemoFont(entity.getU3MemoFont()));
                // リクエストパラメータ.印刷時に色をつけるフラグ= true の場合
                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のu3_メモ色=空白の場合
                // u3_メモ色＝#000000
                // 上記以外の場合
                // u3_メモ色
                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= false の場合
                // u3_メモ色＝#000000
                infoInDto.setU3MemoColor(getMemoColor(entity.getU3MemoColor(), colorFlg));
                // サービスを受ける予定
                infoInDto.setU3(ReportUtil.nullToEmpty(entity.getU3()));
                // アセスメント完成日
                v2YmdList = this.getDate(ReportUtil.nullToEmpty(entity.getV2Ymd()));
            }
        }
        // 2.2. 「4.1」で取得した調査アセスメント種別=3の場合
        else if (ReportConstants.ASSESSMENT_TYPE_SENIORHOUSINGEDITION == syubetPair.getLeft()) {
            // アセスメント表(P)(U)(V)情報取得のDAOを利用
            KghCpnRaiAssPuvPByCriteriaInEntity kghCpnRaiAssPuvPByCriteriaInEntity = new KghCpnRaiAssPuvPByCriteriaInEntity();
            // アセスメントID
            kghCpnRaiAssPuvPByCriteriaInEntity.setAnRaiId(CommonDtoUtil
                    .strValToInt(printSubjectHistoryList.get(0).getRaiId()));
            // アセスメント表(P)(U)(V)情報報取得
            List<KghCpnRaiAssPuvPOutEntity> kghCpnRaiAssPuvPOutEntity = this.raiRrkKghCpnRaiAssPUVSelectMapper
                    .findKghCpnRaiAssPuvPByCriteria(
                            kghCpnRaiAssPuvPByCriteriaInEntity);
            // 件数を取得
            kensuPUV = kghCpnRaiAssPuvPOutEntity.size();
            if (CollectionUtils.isNotEmpty(kghCpnRaiAssPuvPOutEntity)) {
                KghCpnRaiAssPuvPOutEntity entity = kghCpnRaiAssPuvPOutEntity.get(0);
                // 利用者ID
                userId = ReportUtil.nullToEmpty(entity.getUserid());
                // 調査者
                shokuId = ReportUtil.nullToEmpty(entity.getAssShokuId());
                // p1_メモ
                infoInDto.setP1MemoKnj(ReportUtil.nullToEmpty(entity.getP1MemoKnj()));
                // API定義の処理「2.2」のp1_メモフォント=空白の場合、p1_メモフォント="12"、上記以外の場合、p1_メモフォント
                infoInDto.setP1MemoFont(getMemoFont(entity.getP1MemoFont()));
                // リクエストパラメータ.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のp1_メモ色=空白の場合
                // p1_メモ色＝#000000
                // 上記以外の場合
                // p1_メモ色
                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= false の場合
                // p1_メモ色＝#000000
                infoInDto.setP1MemoColor(getMemoColor(entity.getP1MemoColor(), colorFlg));
                // 「API定義の処理「2.2」の意思決定権_法定後見人等」を取得し「意思決定権_法定後見人等」に設定
                infoInDto.setP1A(ReportUtil.nullToEmpty(entity.getP1A()));
                // 終了日
                u1YmdList = this.getDate(ReportUtil.nullToEmpty(entity.getU1Ymd()));
                // u2_メモ
                infoInDto.setU2MemoKnj(ReportUtil.nullToEmpty(entity.getU2MemoKnj()));
                // API定義の処理「2.2」のu2_メモフォント=空白の場合、u2_メモフォント="12"、上記以外の場合、u2_メモフォント
                infoInDto.setU2MemoFont(getMemoFont(entity.getU2MemoFont()));
                // リクエストパラメータ.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のu2_メモ色=空白の場合
                // u2_メモ色＝#000000
                // 上記以外の場合
                // u2_メモ色
                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= false の場合
                // u2_メモ色＝#000000
                infoInDto.setU2MemoColor(getMemoColor(entity.getU2MemoColor(), colorFlg));
                // 今後の居住場所
                infoInDto.setU2(ReportUtil.nullToEmpty(entity.getU2()));
                // u3_メモ
                infoInDto.setU3MemoKnj(ReportUtil.nullToEmpty(entity.getU3MemoKnj()));
                // API定義の処理「2.2」のu3_メモフォント=空白の場合、u3_メモフォント="12"、上記以外の場合、u3_メモフォント
                infoInDto.setU3MemoFont(getMemoFont(entity.getU3MemoFont()));
                // リクエストパラメータ.印刷時に色をつけるフラグ= true の場合
                // API定義の処理「2.1」のu3_メモ色=空白の場合
                // u3_メモ色＝#000000
                // 上記以外の場合
                // u3_メモ色
                // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= false の場合
                // u3_メモ色＝#000000
                infoInDto.setU3MemoColor(getMemoColor(entity.getU3MemoColor(), colorFlg));
                // サービスを受ける予定
                infoInDto.setU3(ReportUtil.nullToEmpty(entity.getU3()));
                // アセスメント完成日
                v2YmdList = this.getDate(ReportUtil.nullToEmpty(entity.getV2Ymd()));
            }
        }
        // 「2.1」~「2.2」で取得したアセスメント表（ＵＶ）情報の総件数
        kensuAll = kensuV + kensuPUV;
        if (kensuAll > 0) {
            /**
             * ===============3.利用者基本情報と職員情報の取得===============
             * 
             */
            // 利用者基本情報
            Pair<String, String> userPair = this.getUserNamesPair(CommonDtoUtil.strValToInt(userId));
            // 氏名（姓）
            infoInDto.setName1Knj(ReportUtil.nullToEmpty(userPair.getLeft()));
            // 氏名（名）
            infoInDto.setName2Knj(ReportUtil.nullToEmpty(userPair.getRight()));

            // 職員情報
            Pair<String, String> shokuinPair = this.getShokuinNamesPair(CommonDtoUtil.strValToInt(shokuId));
            // アセスメント担当者（姓）
            infoInDto.setShokuin1Knj(ReportUtil.nullToEmpty(shokuinPair.getLeft()));
            // アセスメント担当者（名）
            infoInDto.setShokuin2Knj(ReportUtil.nullToEmpty(shokuinPair.getRight()));
        }
        // 上記以外の場合
        else {
            // 氏名（姓）
            infoInDto.setName1Knj(CommonConstants.BLANK_STRING);
            // 氏名（名）
            infoInDto.setName2Knj(CommonConstants.BLANK_STRING);
            // アセスメント担当者（姓）
            infoInDto.setShokuin1Knj(CommonConstants.BLANK_STRING);
            // アセスメント担当者（名）
            infoInDto.setShokuin2Knj(CommonConstants.BLANK_STRING);
        }

        /*
         * =============== 5.上記編集した帳票用データを設定===============
         * 
         */
        // 調査アセスメント種別
        infoInDto.setAssType(syubetPair.getLeft());
        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(syubetPair.getRight());
        // 指定日（年号）
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
        // 指定日（年）
        infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
        // 指定日（月）
        infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
        // 指定日（日）
        infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

        // 終了日（年号）
        infoInDto.setU1YmdGG(u1YmdList.get(0));
        // 終了日（年）
        infoInDto.setU1YmdYY(u1YmdList.get(1));
        // 終了日（月）
        infoInDto.setU1YmdMM(u1YmdList.get(2));
        // 終了日（日）
        infoInDto.setU1YmdDD(u1YmdList.get(3));
        // アセスメント完成日（年号）
        infoInDto.setV2YmdGG(v2YmdList.get(0));
        // アセスメント完成日（年）
        infoInDto.setV2YmdYY(v2YmdList.get(1));
        // アセスメント完成日（月）
        infoInDto.setV2YmdMM(v2YmdList.get(2));
        // アセスメント完成日（日）
        infoInDto.setV2YmdDD(v2YmdList.get(3));
        /*
         * ===============共通関数補足 2.文書番号情報取得処理===============
         * 
         */
        // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01 関数名：GetF3GkProfile
        // を利用し、設定の読込（システム環境以外の設定）情報を取得する。
        // 【リクエストパラメータ】.システムコード
        String syscd = model.getSyscd();
        // セクション
        String section = model.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection();
        // 【リクエストパラメータ】.事業者ID
        String svJigyoId = model.getJigyoInfo().getSvJigyoId();

        String bunsyoKanriNo = this.getBunsyoKanriNo(syscd, shokuId, section, svJigyoId);
        // 文書管理番号
        infoInDto.setBunsyoKanriNo(bunsyoKanriNo);

        return infoInDto;
    }

    /**
     * 利用者基本情報を取得する。
     * 
     * @param userId 利用者ID
     * @return 氏名情報
     */
    public Pair<String, String> getUserNamesPair(Integer userId) {
        String name1Knj = CommonConstants.BLANK_STRING;
        String name2Knj = CommonConstants.BLANK_STRING;
        UserinfoByCriteriaInEntity userinfoByCriteriaInEntity = new UserinfoByCriteriaInEntity();
        userinfoByCriteriaInEntity.setUser(userId);
        List<UserinfoOutEntity> userinfoOutList = comTucUserSelectMapper
                .findUserinfoByCriteria(userinfoByCriteriaInEntity);
        if (userinfoOutList != null && userinfoOutList.size() > 0) {
            // 氏名（姓）
            name1Knj = ReportUtil.nullToEmpty(userinfoOutList.get(0).getName1Knj());
            // 氏名（名）
            name2Knj = ReportUtil.nullToEmpty(userinfoOutList.get(0).getName2Knj());
        }
        return Pair.of(name1Knj, name2Knj);
    }

    /**
     * 職員情報情報を取得する。
     * 
     * @param shokuId 利用者ID
     * @return 氏名情報
     */
    public Pair<String, String> getShokuinNamesPair(Integer shokuId) {
        // 職員名（姓）
        String shokuin1Knj = CommonConstants.BLANK_STRING;
        // 職員名（名）
        String shokuin2Knj = CommonConstants.BLANK_STRING;
        // 職員情報一覧情報取得のDAOを利用
        ShokuinInfoListSpaceSortByCriteriaInEntity shokuinInfoListSpaceSortByCriteriaInEntity = new ShokuinInfoListSpaceSortByCriteriaInEntity();
        // 職員情報一覧情報を取得
        List<ShokuinInfoListSpaceSortOutEntity> shokuinInfoListSpaceSortOutEntity = this.comMscShokuinSelectMapper
                .findShokuinInfoListSpaceSortByCriteria(
                        shokuinInfoListSpaceSortByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(shokuinInfoListSpaceSortOutEntity)
                && shokuinInfoListSpaceSortOutEntity.size() > 0) {
            // 取得した調査者 = 取得した職員情報リスト.職員ID
            if (CollectionUtils.isNotEmpty(shokuinInfoListSpaceSortOutEntity)) {
                List<ShokuinInfoListSpaceSortOutEntity> shokuinInfoListSpaceSortFilterList = shokuinInfoListSpaceSortOutEntity
                        .stream().filter(item -> shokuId == item.getChkShokuId()).collect(Collectors.toList());
                // 職員名（姓）
                shokuin1Knj = ReportUtil.nullToEmpty(shokuinInfoListSpaceSortFilterList.get(0).getShokuin1Knj());
                // 職員名（名）
                shokuin2Knj = ReportUtil.nullToEmpty(shokuinInfoListSpaceSortFilterList.get(0).getShokuin1Knj());
            }
        }
        return Pair.of(shokuin1Knj, shokuin2Knj);
    }

    /**
     * アセスメント種別を取得する。
     * 
     * @param raiId アセスメントID
     * @return アセスメント種別
     * @throws Exception
     */
    public Pair<Integer, String> getSyubetuByRaiId(Integer raiId) {
        String syubetuKnj = CommonConstants.BLANK_STRING;
        AssTypeByCriteriaInEntity assTypeByCriteriaInEntity = new AssTypeByCriteriaInEntity();
        assTypeByCriteriaInEntity.setIlRirekiId(raiId);
        List<AssTypeOutEntity> assTypeOutList = cpnTucRaiRrkSelectMapper
                .findAssTypeByCriteria(assTypeByCriteriaInEntity);
        Integer assType = ReportConstants.ASSESSMENT_TYPE_HOMEEDITION;
        if (assTypeOutList != null && assTypeOutList.size() > 0) {
            assType = assTypeOutList.get(0).getAssType();
        }
        syubetuKnj = this.getSyubetuByAssType(assType);

        return Pair.of(assType, syubetuKnj);
    }

    /**
     * アセスメント種別名を取得する
     * 
     * @param kinyuAssType アセスメント種別
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private String getSyubetuByAssType(Integer kinyuAssType) {
        String syubetuKnj = CommonConstants.BLANK_STRING;
        switch (kinyuAssType) {
            case ReportConstants.ASSESSMENT_TYPE_HOMEEDITION:
                // アセスメント種別 = 1 の場合
                syubetuKnj = ReportConstants.STR_HOMEEDITION;
                break;
            case ReportConstants.ASSESSMENT_TYPE_FACILITYEDITION:
                // アセスメント種別 = 2 の場合
                syubetuKnj = ReportConstants.STR_FACILITYEDITION;
                break;
            case ReportConstants.ASSESSMENT_TYPE_SENIORHOUSINGEDITION:
                // アセスメント種別 = 3 の場合
                syubetuKnj = ReportConstants.STR_SENIORHOUSINGEDITION;
                break;
            default:
                // 上記以外の場合、
                syubetuKnj = CommonConstants.BLANK_STRING;
                break;
        }
        return syubetuKnj;
    }

    /**
     * 文書番号情報取得処理。
     * 
     * @param sysCode    システムコード
     * @param shokuId    職員ID
     * @param sectionKnj セクション
     * @param svJigyoId  サービス事業者ID
     * @return アセスメント種別
     */
    public String getBunsyoKanriNo(String sysCode, String shokuId, String sectionKnj, String svJigyoId) {
        String bunsyoKanriNo = CommonConstants.BLANK_STRING;
        // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01Logic 関数名：GetF3GkProfile を利用し、
        // 設定の読込（システム環境以外の設定）情報を取得する。
        F3gkGetProfileInDto f3gkGetProfileInDto = new F3gkGetProfileInDto();
        f3gkGetProfileInDto.setGsyscd(sysCode);
        f3gkGetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(shokuId));
        f3gkGetProfileInDto.setHoujinId(CommonConstants.HOUJIN_ID_0);
        f3gkGetProfileInDto.setShisetuId(CommonConstants.SHISETU_ID);
        f3gkGetProfileInDto.setSvJigyoId(CommonConstants.SV_JIGYO_ID_0);
        f3gkGetProfileInDto.setKinounameKnj(CommonConstants.KINOU_NAME_PRT);
        f3gkGetProfileInDto.setSectionKnj(sectionKnj);
        f3gkGetProfileInDto.setKeyKnj(CommonConstants.ISO9001_FLG);
        f3gkGetProfileInDto.setAsDefault(CommonConstants.STR_DEFAULTDE_0);
        String f3gkProfile = nds3GkFunc01Logic.getF3gkProfile(f3gkGetProfileInDto);

        // 2.2. 設定の読込（システム環境以外の設定）情報取得の戻り値は"1"（正常参照）の場合、下記の24-06
        // 帳票書式番号設定マスタ情報取得のDAOを利用し、文書番号を取得する。
        if (CommonConstants.STR_1.equals(f3gkProfile)) {
            YoushikiNoByCriteriaInEntity youshikiNoByCriteriaInEntity = new YoushikiNoByCriteriaInEntity();
            youshikiNoByCriteriaInEntity.setAsSection(sectionKnj);
            youshikiNoByCriteriaInEntity.setLlSvJigyoId(svJigyoId);
            List<YoushikiNoOutEntity> youshikiNoOutList = comMocPrtYoushikiSelectMapper
                    .findYoushikiNoByCriteria(youshikiNoByCriteriaInEntity);
            if (CollectionUtils.isNotEmpty(youshikiNoOutList)) {
                bunsyoKanriNo = ReportUtil.nullToEmpty(youshikiNoOutList.get(0).getYoushikiNo());
            }
        }
        return bunsyoKanriNo;
    }

    /**
     * メモフォントを取得する。
     * 
     * @param obj
     * @return メモフォント
     */
    public String getMemoFont(Object obj) {
        // メモフォント=nullの場合、メモフォント="12"
        // 上記以外の場合、メモフォント=API定義の処理「2.2.」のメモフォント
        return null == obj ? CommonConstants.STR_12 : CommonDtoUtil.objValToString(Math.abs((int) obj));
    }

    /**
     * メモ色を取得する。
     * 
     * @param memoColor
     * @return メモ色
     */
    public String getMemoColor(Integer memoColor, boolean colorFlg) {
        String ret = ReportConstants.COLOR_BLANK;
        // リクエストパラメータ.データ.印刷オプション.印刷時に色をつけるフラグ= true の場合
        if (colorFlg) {
            // API定義の処理のメモ色!=nullの場合、
            if (memoColor != null) {
                // メモ色=共通関数補足の十六進数RGB値
                // 共通関数「クラス名：ColorConvertLogic 関数名：bgrToHexRgb」を利用し、十六進数のRGB値を取得する。
                ret = colorConvertLogic.bgrToHexRgb(memoColor);
            }
        }
        return ret;
    }

    /**
     * 日付を取得する。
     * 
     * @param date
     * @return List
     */
    public List<String> getDate(String date) {
        List<String> dateList = new ArrayList<>();
        if (CommonConstants.BLANK_STRING.equals(date)) {
            // 日付（年号）
            dateList.add(CommonConstants.FULL_WIDTH_SPACE + CommonConstants.FULL_WIDTH_SPACE);
            // 日付（年）
            dateList.add(CommonConstants.FULL_WIDTH_SPACE);
            // 日付（月）
            dateList.add(CommonConstants.FULL_WIDTH_SPACE);
            // 日付（日）
            dateList.add(CommonConstants.FULL_WIDTH_SPACE);
        } else {
            String wkDate = nds3GkFunc01Logic.get2Gengouj(CommonConstants.INT_1, date);
            // 日付（年号）
            dateList.add(StringUtils.mid(wkDate, 0, 2));
            // 日付（年）
            dateList.add(StringUtils.mid(wkDate, 2, 2));
            // 日付（月）
            dateList.add(StringUtils.mid(wkDate, 5, 2));
            // 日付（日）
            dateList.add(StringUtils.mid(wkDate, 8, 2));
        }
        return dateList;
    }

    /**
     * 指定日を取得する
     * 
     * @param shiTeiKubun 指定日印刷区分
     * @param shiTeiDate  西暦日付
     * @param systemDate  システム日付
     * @throws Exception 例外
     */
    private ReportCommonShiTeiDateParts getShiTeiDate(String shiTeiKubun, String shiTeiDate, String systemDate) {
        // 指定日（年号）=""
        String shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（年）=""
        String shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（月）=""
        String shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（日）=""
        String shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日印刷区分判定
        switch (shiTeiKubun) {
            case ReportConstants.PRINTDATE_KBN_SHINAI:
                // 指定日印刷区分 = 1 の場合
                // 指定日（年号）=""
                shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（年）=""
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）=""
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）=""
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            case ReportConstants.PRINTDATE_KBN_PRINT:
                // 指定日印刷区分 = 2 の場合
                // 西暦日付を和暦日付に変換する
                List<String> dateParts = ReportUtil.getLocalDateToJapanDateTimeFormat(shiTeiDate);
                shiTeiDateGG = dateParts.get(ReportConstants.SHITEIDATE_GG);
                // 指定日（年）=""
                shiTeiDateYY = dateParts.get(ReportConstants.SHITEIDATE_YY);
                // 指定日（月）=""
                shiTeiDateMM = dateParts.get(ReportConstants.SHITEIDATE_MM);
                // 指定日（日）=""
                shiTeiDateDD = dateParts.get(ReportConstants.SHITEIDATE_DD);
                break;
            case ReportConstants.PRINTDATE_KBN_BLANKDATE:
                // 指定日印刷区分 = 3 の場合
                // 指定日の空欄表示処理
                DateTimeFormatter inputFormatter = DateTimeFormatter
                        .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
                DateTimeFormatter outputFormatter = DateTimeFormatter
                        .ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
                LocalDateTime dateTime = LocalDateTime.parse(systemDate, inputFormatter);
                String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
                // 指定日（年号）=""
                shiTeiDateGG = blankDate.substring(0, 2);
                // 指定日（年）=""
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）=""
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）=""
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            default:
                // 指定日（年号）=""
                shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（年）=""
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）=""
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）=""
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;

                break;
        }
        return new ReportCommonShiTeiDateParts(shiTeiDateGG, shiTeiDateYY, shiTeiDateMM, shiTeiDateDD);
    }
}
