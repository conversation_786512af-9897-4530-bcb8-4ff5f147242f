package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00797HouInfo;
import jp.ndsoft.carebase.cmn.api.logic.AssessmentHomeLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeHousingUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeHousingUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeSaveServiceInDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Hou11H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Hou11R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdlRirekiMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Hou11H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Hou11H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Hou11R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Hou11R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRireki;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRirekiCriteria;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * @since 2025.04.18
 * <AUTHOR> 崔貝貝
 * @apiNote GUI00797_［アセスメント］画面（居宅）（4） データ保存
 */
@Service
public class AssessmentHomeHousingUpdateServiceImpl extends
        UpdateServiceImpl<AssessmentHomeHousingUpdateServiceInDto, AssessmentHomeHousingUpdateServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** ＧＬ＿住宅等の状況（Ｈ２１改訂）DAO */
    @Autowired
    private CpnTucGdl4Hou11H21Mapper cpnTucGdl4Hou11H21Mapper;

    /** ＧＬ＿住宅等の状況（R３改訂）DAO */
    @Autowired
    private CpnTucGdl5Hou11R3Mapper cpnTucGdl5Hou11R3Mapper;

    /** ＧＬ＿居宅アセスメント履歴 */
    @Autowired
    private CpnTucGdlRirekiMapper cpnTucGdlRirekiMapper;

    /** ［アセスメント］画面（居宅）画面のロジッククラス */
    @Autowired
    private AssessmentHomeLogic assessmentHomeLogic;

    @Autowired
    private PlatformTransactionManager transactionManager;

    /**
     * ［アセスメント］画面（居宅）（4） データ保存
     * 
     * @param inDto ［アセスメント］画面（居宅）（4） データ保存の入力DTO
     * @return ［アセスメント］画面（居宅）（4） データ保存の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected AssessmentHomeHousingUpdateServiceOutDto mainProcess(AssessmentHomeHousingUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        AssessmentHomeHousingUpdateServiceOutDto outDto = new AssessmentHomeHousingUpdateServiceOutDto();
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        /*
         * ===============2. バリデーションチェックを行う。===============
         * 
         */
        boolean resFlg = assessmentHomeLogic.documentPerformValidationCheck(loginDto);
        // 2.2.4. 【変数】.処理結果が「false：失敗」の場合
        if (!resFlg) {
            // 2.2.4. 【変数】.処理結果が「false：失敗」の場合、下記返却する情報を設定して、後続処理を飛ばして、API処理を正常終了とする。
            // レスポンス.計画期間ID ： リクエストパラメータ.計画期間ID
            outDto.setSc1Id(loginDto.getSc1Id());
            // レスポンス.アセスメントID ： リクエストパラメータ.アセスメントID
            outDto.setGdlId(loginDto.getGdlId());
            // レスポンス.エラー区分: "1
            outDto.setErrKbn(CommonConstants.E_DOC_CHECK_ERR);
            return outDto;
        }
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transactionB = transactionManager.getTransaction(def);
        try {
            // ［アセスメント］画面（居宅）（4） データ保存
            outDto = mainProcessMealUpdate(inDto);
            transactionManager.commit(transactionB);
        } catch (Exception e) {
            transactionManager.rollback(transactionB);
            throw e;
        }

        // e-文書_ケアチェック表１のリクエストパラメータ
        TransactionStatus transactionC = transactionManager.getTransaction(def);
        try {
            mainProcessEdocOut(inDto, outDto);
            transactionManager.commit(transactionC);
        } catch (Exception e) {
            transactionManager.rollback(transactionC);
            throw e;
        }

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * API定義 (e文書出力)
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected void mainProcessEdocOut(
            AssessmentHomeHousingUpdateServiceInDto inDto, AssessmentHomeHousingUpdateServiceOutDto outDto)
            throws Exception {
        // 入力dtoをLoginDtoにコピーする
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);

        /*
         * ===============e文書出力===============
         * 
         */
        // e文書出力
        String errKbn = assessmentHomeLogic.getPrint(loginDto, outDto.getSc1Id());
        outDto.setErrKbn(errKbn);
    }

    /**
     * ［アセスメント］画面（居宅）（4） データ保存
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected AssessmentHomeHousingUpdateServiceOutDto mainProcessMealUpdate(
            AssessmentHomeHousingUpdateServiceInDto inDto)
            throws Exception {
        AssessmentHomeHousingUpdateServiceOutDto outDto = new AssessmentHomeHousingUpdateServiceOutDto();

        String sc1IdTemp = CommonConstants.BLANK_STRING;

        // ＧＬ＿住宅等の状況（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_hou11_h21）登録で採番されたアセスメントID
        String gdlIdTemp = CommonConstants.BLANK_STRING;

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. 計画対象期間の保存処理===============
         * 
         */
        // 2.1.リクエストパラメータ.計画対象期間IDがnullの場合、【27-06記録共通期間】情報を登録する。
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        loginDto = this.setAssessmentHomeSaveServiceInDto(inDto);

        if (inDto.getSc1Id() == null || StringUtil.isEmpty(inDto.getSc1Id())) {
            // 計画対象期間の保存処理
            this.assessmentHomeLogic.insertKghTucKrkKikan(loginDto);
            // 変数.計画対象期間ID=採番した期間ID
            sc1IdTemp = loginDto.getSc1Id();

        } else {
            // 変数.計画対象期間ID=リクエストパラメータ.計画対象期間ID
            sc1IdTemp = inDto.getSc1Id();
        }

        // 計画対象期間ID
        outDto.setSc1Id(sc1IdTemp);

        /*
         * ======3.リクエストパラメータ.削除処理区分が2:画面を履歴ごと削除するの場合、下記テーブルデータを削除する。========
         * 
         */
        if (CommonConstants.DELETE_KBN_2.equals(inDto.getDeleteKbn())) {
            assessmentHomeLogic.homeLogicsyuri(loginDto, CommonDtoUtil.strValToInt(inDto.getSc1Id()));
            // アセスメントID
            outDto.setGdlId(inDto.getGdlId());
        }

        /*
         * ======4. リクエストパラメータ.削除処理区分が1:画面のみ削除するの場合========
         * 
         */
        else if (CommonConstants.DELETE_KBN_1.equals(inDto.getDeleteKbn())) {
            // 4.1. リクエストパラメータ.改定フラグが4「H21/４改訂版」の場合、【ＧＬ＿住宅等の状況（Ｈ２１改訂）】情報を更新する。
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                this.deleteHou11H21(inDto, sc1IdTemp);

                // アセスメントID
                outDto.setGdlId(inDto.getGdlId());
            }
            // 4.2. リクエストパラメータ.改定フラグが5「R3/４改訂版」の場合、【ＧＬ＿住宅等の状況（R３改訂）】情報を更新する。
            else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                this.deleteHou11R3(inDto, sc1IdTemp);

                // アセスメントID
                outDto.setGdlId(inDto.getGdlId());
            }

            // 4.3. 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
            this.clearRireki(inDto, sc1IdTemp);

        } else {
            /*
             * ======5. 以外の場合========
             * 
             */
            // 5.1. 履歴情報の保存処理
            // 5.1.1. リクエストパラメータ.履歴更新区分が"C":新規の場合、【ＧＬ＿居宅アセスメント履歴】情報を登録する。
            if (CommonDtoUtil.isHistoryCreate(inDto)) {
                this.insertRireki(inDto, sc1IdTemp);

                // 「5.1.1.」で採番したアセスメントID
                gdlIdTemp = inDto.getGdlId();
                // アセスメントID
                outDto.setGdlId(gdlIdTemp);
            } else if (CommonDtoUtil.isHistoryUpdate(inDto)) {
                // 5.1.2. リクエストパラメータ.履歴更新区分が"U":更新の場合、【ＧＬ＿居宅アセスメント履歴】情報を更新する。
                this.updateRireki(inDto, sc1IdTemp);

                // アセスメントID
                outDto.setGdlId(inDto.getGdlId());
            }

            // 5.2. 住宅等の状況の保存処理
            // 5.2.1. リクエストパラメータ.更新区分が"C":新規の場合、住宅等の状況情報を登録する。
            if (CommonDtoUtil.isCreate(inDto)) {
                // 5.2.1.1. リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、【ＧＬ＿住宅等の状況（Ｈ２１改訂）】情報を登録する。
                if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                    // アセスメントID設定
                    gdlIdTemp = inDto.getGdlId();
                    this.insertHou11H21(inDto, sc1IdTemp, gdlIdTemp);

                }
                // 5.2.1.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【ＧＬ＿住宅等の状況（R３改訂）】情報を登録する。
                else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                    // アセスメントID設定
                    gdlIdTemp = inDto.getGdlId();
                    this.insertHou11R3(inDto, sc1IdTemp, gdlIdTemp);

                }
            }
            // 5.2.2. リクエストパラメータ.更新区分が"U":更新の場合、住宅等の状況情報を更新する。
            else if (CommonDtoUtil.isUpdate(inDto)) {
                // 5.2.2.1. リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、【ＧＬ＿住宅等の状況（Ｈ２１改訂）】情報を更新する。
                if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                    this.updateHou11H21(inDto, sc1IdTemp);

                } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                    this.updateHou11R3(inDto, sc1IdTemp);
                }

            }

            // 5.3. リクエストパラメータ.【課題と目標リスト】の件数分、課題と目標情報を保存する。
            assessmentHomeLogic.homeLogicCpnTucGdlKadai(loginDto, CommonDtoUtil.strValToInt(sc1IdTemp));
        }

        return outDto;
    }

    /**
     * ＧＬ＿住宅等の状況（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_hou11_h21）の削除更新詳細
     * 関数名：deleteHou11h21
     * 
     * @param inDto     ［アセスメント］画面（居宅）（4） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     */
    private void deleteHou11H21(AssessmentHomeHousingUpdateServiceInDto inDto, String sc1IdTemp) {

        CpnTucGdl4Hou11H21Criteria cpnTucGdl4Hou11H21Criteria = new CpnTucGdl4Hou11H21Criteria();
        cpnTucGdl4Hou11H21Criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp));

        // DAOを実行
        this.cpnTucGdl4Hou11H21Mapper.deleteByCriteria(cpnTucGdl4Hou11H21Criteria);
    }

    /**
     * ＧＬ＿住宅等の状況（R３改訂）テーブル（cpn_tuc_gdl5_hou11_r3）の削除更新詳細
     * 関数名：deleteHou11R3
     * 
     * @param inDto     ［アセスメント］画面（居宅）（4） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     */
    private void deleteHou11R3(AssessmentHomeHousingUpdateServiceInDto inDto, String sc1IdTemp) {

        CpnTucGdl5Hou11R3Criteria cpnTucGdl5Hou11R3Criteria = new CpnTucGdl5Hou11R3Criteria();
        cpnTucGdl5Hou11R3Criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp));

        // DAOを実行
       this.cpnTucGdl5Hou11R3Mapper.deleteByCriteria(cpnTucGdl5Hou11R3Criteria);
    }

    /**
     * ＧＬ＿居宅アセスメント履歴テーブル（cpn_tuc_gdl_rireki）の更新詳細
     * 関数名：clearRireki
     * 
     * @param inDto     ［アセスメント］画面（居宅）（4） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     */
    private void clearRireki(AssessmentHomeHousingUpdateServiceInDto inDto, String sc1IdTemp) {
        // DAOパラメータを作成
        // 3.1.居宅アセスメント履歴を更新する
        // ＧＬ＿居宅アセスメント履歴パラメータを作成
        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                // 利用者ＩＤ
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 法人ID
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();

        // 住居等の状況
        cpnTucGdlRireki.setAss4(CommonConstants.BLANK_STRING);

        // DAOを実行
        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRireki, criteria);
    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を登録する。
     * 関数名：insertRireki
     * 
     * @param inDto     ［アセスメント］画面（居宅）（4） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     */
    private void insertRireki(AssessmentHomeHousingUpdateServiceInDto inDto, String sc1IdTemp) throws Exception {
        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();
        // 計画期間ID
        cpnTucGdlRireki.setSc1Id(CommonDtoUtil.strValToInt(sc1IdTemp));
        // 法人ID
        cpnTucGdlRireki.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        cpnTucGdlRireki.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        cpnTucGdlRireki.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        cpnTucGdlRireki.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // アセスメント実施日
        cpnTucGdlRireki.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRireki.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // 住居等の状況
        cpnTucGdlRireki.setAss4(CommonConstants.MARU);
        // Ｊの状態
        cpnTucGdlRireki.setAss12(CommonConstants.DASH);
        // 本人の基本動作等8
        cpnTucGdlRireki.setAss13(CommonConstants.DASH);
        // 改定フラグ
        cpnTucGdlRireki.setNinteiFormF(CommonDtoUtil.strValToInt(inDto.getNinteiFormF()));

        this.cpnTucGdlRirekiMapper.insertSelectiveAndReturn(cpnTucGdlRireki);

        inDto.setGdlId(CommonDtoUtil.objValToString(cpnTucGdlRireki.getGdlId()));

    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
     * 関数名：updateRireki
     * 
     * @param inDto     ［アセスメント］画面（居宅）（4） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     */
    private void updateRireki(AssessmentHomeHousingUpdateServiceInDto inDto, String sc1IdTemp) {

        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();
        // アセスメント実施日
        cpnTucGdlRireki.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRireki.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // 住居等の状況
        cpnTucGdlRireki.setAss4(CommonConstants.MARU);
        // Ｊの状態
        cpnTucGdlRireki.setAss12(CommonConstants.DASH);
        // 本人の基本動作等8
        cpnTucGdlRireki.setAss13(CommonConstants.DASH);

        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                // 利用者ＩＤ
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 法人ID
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRireki, criteria);
    }

    /**
     * ＧＬ＿住宅等の状況（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_hou11_h21）の登録詳細
     * 関数名：insertHou11H21
     * 
     * @param inDto     ［アセスメント］画面（居宅）（4） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @param gdlIdTemp アセスメントID
     * @throws Exception Exception
     */
    private void insertHou11H21(AssessmentHomeHousingUpdateServiceInDto inDto, String sc1IdTemp, String gdlIdTemp)
            throws Exception {
        CpnTucGdl4Hou11H21 cpnTucGdl4Hou11H21 = this.setCpnTucGdl4Hou11H21(inDto.getHouInfo(), true);
        // アセスメントID
        cpnTucGdl4Hou11H21
                .setGdlId(CommonDtoUtil.strValToInt(gdlIdTemp));
        // 計画期間ID
        cpnTucGdl4Hou11H21
                .setSc1Id(CommonDtoUtil.strValToInt(sc1IdTemp));

        this.cpnTucGdl4Hou11H21Mapper.insertSelective(cpnTucGdl4Hou11H21);

    }

    /**
     * ＧＬ＿住宅等の状況（R３改訂）テーブル（cpn_tuc_gdl5_hou11_r3）の登録詳細
     * 関数名：insertHou11R3
     * 
     * @param inDto     ［アセスメント］画面（居宅）（4） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @param gdlIdTemp アセスメントID
     * @throws Exception Exception
     */
    private void insertHou11R3(AssessmentHomeHousingUpdateServiceInDto inDto, String sc1IdTemp, String gdlIdTemp)
            throws Exception {

        CpnTucGdl5Hou11R3 cpnTucGdl5Hou11R3 = this.setCpnTucGdl5Hou11R3(inDto.getHouInfo(), true);
        // アセスメントID
        cpnTucGdl5Hou11R3.setGdlId(CommonDtoUtil.strValToInt(gdlIdTemp));
        // 計画期間ID
        cpnTucGdl5Hou11R3.setSc1Id(CommonDtoUtil.strValToInt(sc1IdTemp));

        this.cpnTucGdl5Hou11R3Mapper.insertSelective(cpnTucGdl5Hou11R3);

    }

    /**
     * ＧＬ＿住宅等の状況（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_hou11_h21）の更新詳細
     * 関数名：updateHou11H21
     * 
     * @param inDto     ［アセスメント］画面（居宅）（4） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID

     * @throws Exception Exception
     */
    private void updateHou11H21(AssessmentHomeHousingUpdateServiceInDto inDto, String sc1IdTemp)
            throws Exception {

        CpnTucGdl4Hou11H21 cpnTucGdl4Hou11H21 = this.setCpnTucGdl4Hou11H21(inDto.getHouInfo(), false);

        CpnTucGdl4Hou11H21Criteria criteria = new CpnTucGdl4Hou11H21Criteria();
        // アセスメントID
        criteria.createCriteria()
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp));

        this.cpnTucGdl4Hou11H21Mapper
                .updateByCriteriaSelective(cpnTucGdl4Hou11H21, criteria);
    }

    /**
     * ＧＬ＿住宅等の状況（R３改訂）テーブル（cpn_tuc_gdl5_hou11_r3）の更新詳細
     * 関数名：updateHou11R3
     * 
     * @param inDto     ［アセスメント］画面（居宅）（4） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @throws Exception Exception
     */
    private void updateHou11R3(AssessmentHomeHousingUpdateServiceInDto inDto, String sc1IdTemp)
            throws Exception {

        CpnTucGdl5Hou11R3 cpnTucGdl5Hou11R3 = this.setCpnTucGdl5Hou11R3(inDto.getHouInfo(), false);

        CpnTucGdl5Hou11R3Criteria criteria = new CpnTucGdl5Hou11R3Criteria();
        // アセスメントID
        criteria.createCriteria()
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp));

        this.cpnTucGdl5Hou11R3Mapper
                .updateByCriteriaSelective(cpnTucGdl5Hou11R3, criteria);

    }

    /**
     * 【27-06記録共通期間】情報を登録パラメータを設定する
     * 
     * @param inDto 施設マスタ
     * @return 【27-06記録共通期間】データ保存入力DTO.
     */
    private AssessmentHomeSaveServiceInDto setAssessmentHomeSaveServiceInDto(
            AssessmentHomeHousingUpdateServiceInDto inDto) {
        AssessmentHomeSaveServiceInDto assessmentHomeSaveServiceInDto = new AssessmentHomeSaveServiceInDto();
        // 法人ID
        assessmentHomeSaveServiceInDto.setHoujinId(inDto.getHoujinId());
        // 施設ID
        assessmentHomeSaveServiceInDto.setShisetuId(inDto.getShisetuId());
        // 事業者ID
        assessmentHomeSaveServiceInDto.setSvJigyoId(inDto.getSvJigyoId());
        // 利用者ID
        assessmentHomeSaveServiceInDto.setUserId(inDto.getUserId());
        // 種別ID
        assessmentHomeSaveServiceInDto.setSyubetsuId(inDto.getSyubetsuId());
        // 作成日
        assessmentHomeSaveServiceInDto.setKijunbiYmd(inDto.getKijunbiYmd());
        // 履歴ID
        assessmentHomeSaveServiceInDto.setGdlId(inDto.getGdlId());
        // 課題リスト
        assessmentHomeSaveServiceInDto.setKadaiList(inDto.getKadaiList());

        return assessmentHomeSaveServiceInDto;

    }

    /**
     * ＧＬ＿住宅等の状況（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_hou11_h21）の登録パラメータを設定する
     * 
     * @param houInfo  住居等の状況情報
     * @param isInsert 登録/更新
     * @return ＧＬ＿住宅等の状況（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_hou11_h21）の登録パラメータ
     */
    private CpnTucGdl4Hou11H21 setCpnTucGdl4Hou11H21(Gui00797HouInfo houInfo, Boolean isInsert) {
        CpnTucGdl4Hou11H21 cpnTucGdl4Hou11H21 = new CpnTucGdl4Hou11H21();

        // １戸建て・集合住宅
        cpnTucGdl4Hou11H21.setHouseShu(CommonDtoUtil.strValToInt(houInfo.getHouseShu()));
        // 賃貸・所有・給与住宅等
        cpnTucGdl4Hou11H21.setShoyuShu(CommonDtoUtil.strValToInt(houInfo.getShoyuShu()));
        // その他(ﾒﾓ）
        cpnTucGdl4Hou11H21.setShoyuMemoKnj(houInfo.getShoyuMemoKnj());

        if (isInsert) {
            // 平屋建て
            cpnTucGdl4Hou11H21.setHiraya(0);
            // ○階建て
            cpnTucGdl4Hou11H21.setKaidate(0);
            // 集合住宅
            cpnTucGdl4Hou11H21.setShugou(0);
        }

        // 専用居室の有無
        cpnTucGdl4Hou11H21.setKyoUmu(CommonDtoUtil.strValToInt(houInfo.getKyoUmu()));
        // １階
        cpnTucGdl4Hou11H21.setKyoKai1(CommonDtoUtil.strValToInt(houInfo.getKyoKai1()));
        // ２階
        cpnTucGdl4Hou11H21.setKyoKai2(CommonDtoUtil.strValToInt(houInfo.getKyoKai2()));
        // その他（２階以上）
        cpnTucGdl4Hou11H21.setKyoKai3(CommonDtoUtil.strValToInt(houInfo.getKyoKai3()));
        // 階数
        cpnTucGdl4Hou11H21.setKyoKaisu(CommonDtoUtil.strValToInt(houInfo.getKyoKaisu()));
        // エレベーターの有無
        cpnTucGdl4Hou11H21.setElevatorUmu(CommonDtoUtil.strValToInt(houInfo.getElevatorUmu()));
        // 寝具
        cpnTucGdl4Hou11H21.setSleepWhere(CommonDtoUtil.strValToInt(houInfo.getSleepWhere()));
        // ベッドの種類１(固定）
        cpnTucGdl4Hou11H21.setBedShu1(CommonDtoUtil.strValToInt(houInfo.getBedShu1()));
        // ベッドの種類2（ギャッチ）
        cpnTucGdl4Hou11H21.setBedShu2(CommonDtoUtil.strValToInt(houInfo.getBedShu2()));
        // ベッドの種類3（電動）
        cpnTucGdl4Hou11H21.setBedShu3(CommonDtoUtil.strValToInt(houInfo.getBedShu3()));
        // ベッドの種類4(その他）
        cpnTucGdl4Hou11H21.setBedShu4(CommonDtoUtil.strValToInt(houInfo.getBedShu4()));
        // その他(ﾒﾓ）（寝具）
        cpnTucGdl4Hou11H21.setBedMemoKnj(houInfo.getBedMemoKnj());
        // 陽あたり
        cpnTucGdl4Hou11H21.setHiatari(CommonDtoUtil.strValToInt(houInfo.getHiatari()));
        // 暖房
        cpnTucGdl4Hou11H21.setHeater(CommonDtoUtil.strValToInt(houInfo.getHeater()));
        // 冷房
        cpnTucGdl4Hou11H21.setAirCooling(CommonDtoUtil.strValToInt(houInfo.getAirCooling()));
        // 和式
        cpnTucGdl4Hou11H21.setToilShu1(CommonDtoUtil.strValToInt(houInfo.getToilShu1()));
        // 洋式
        cpnTucGdl4Hou11H21.setToilShu2(CommonDtoUtil.strValToInt(houInfo.getToilShu2()));
        // その他（トイレ）
        cpnTucGdl4Hou11H21.setToilShu3(CommonDtoUtil.strValToInt(houInfo.getToilShu3()));
        // その他(ﾒﾓ）（トイレ）
        cpnTucGdl4Hou11H21.setToilMemoKnj(houInfo.getToilMemoKnj());
        // 手すりの有無（トイレ）
        cpnTucGdl4Hou11H21.setToilTesuriUmu(CommonDtoUtil.strValToInt(houInfo.getToilTesuriUmu()));
        // 段差の有無（トイレ）
        cpnTucGdl4Hou11H21.setToilDansaUmu(CommonDtoUtil.strValToInt(houInfo.getToilDansaUmu()));
        // (自宅に）浴槽の有無
        cpnTucGdl4Hou11H21.setBathUmu(CommonDtoUtil.strValToInt(houInfo.getBathUmu()));
        // 手すりの有無（浴槽）
        cpnTucGdl4Hou11H21.setBathTesuriUmu(CommonDtoUtil.strValToInt(houInfo.getBathTesuriUmu()));
        // 段差の有無（浴槽）
        cpnTucGdl4Hou11H21.setBathDansaUmu(CommonDtoUtil.strValToInt(houInfo.getBathDansaUmu()));
        // 福祉機器（室外）使用状態 1:使用 2:使用しない
        cpnTucGdl4Hou11H21.setHukuOutUse(CommonDtoUtil.strValToInt(houInfo.getHukuOutUse()));
        // 車いす（室外）
        cpnTucGdl4Hou11H21.setHukuOut1(CommonDtoUtil.strValToInt(houInfo.getHukuOut1()));
        // 電動車いす（室外）
        cpnTucGdl4Hou11H21.setHukuOut2(CommonDtoUtil.strValToInt(houInfo.getHukuOut2()));
        // 杖（室外）
        cpnTucGdl4Hou11H21.setHukuOut3(CommonDtoUtil.strValToInt(houInfo.getHukuOut3()));
        // 歩行器（室外）
        cpnTucGdl4Hou11H21.setHukuOut4(CommonDtoUtil.strValToInt(houInfo.getHukuOut4()));
        // その他（室外）
        cpnTucGdl4Hou11H21.setHukuOut5(CommonDtoUtil.strValToInt(houInfo.getHukuOut5()));
        // その他(ﾒﾓ）（室外）
        cpnTucGdl4Hou11H21.setOutMemoKnj(houInfo.getOutMemoKnj());
        // 福祉機器（室内）使用状態 1:使用 2:使用しない
        cpnTucGdl4Hou11H21.setHukuInUse(CommonDtoUtil.strValToInt(houInfo.getHukuInUse()));
        // 車いす（室内）
        cpnTucGdl4Hou11H21.setHukuIn1(CommonDtoUtil.strValToInt(houInfo.getHukuIn1()));
        // 電動車いす（室内）
        cpnTucGdl4Hou11H21.setHukuIn2(CommonDtoUtil.strValToInt(houInfo.getHukuIn2()));
        // 杖（室内）
        cpnTucGdl4Hou11H21.setHukuIn3(CommonDtoUtil.strValToInt(houInfo.getHukuIn3()));
        // 歩行器（室内）
        cpnTucGdl4Hou11H21.setHukuIn4(CommonDtoUtil.strValToInt(houInfo.getHukuIn4()));
        // その他（室内）
        cpnTucGdl4Hou11H21.setHukuIn5(CommonDtoUtil.strValToInt(houInfo.getHukuIn5()));
        // その他(ﾒﾓ）（室内）
        cpnTucGdl4Hou11H21.setInMemoKnj(houInfo.getInMemoKnj());
        // 洗濯機
        cpnTucGdl4Hou11H21.setSetsubi1(CommonDtoUtil.strValToInt(houInfo.getSetsubi1()));
        // 湯沸器
        cpnTucGdl4Hou11H21.setSetsubi2(CommonDtoUtil.strValToInt(houInfo.getSetsubi2()));
        // 冷蔵庫
        cpnTucGdl4Hou11H21.setSetsubi3(CommonDtoUtil.strValToInt(houInfo.getSetsubi3()));

        if (isInsert) {
            // 立地環境上の問題の有
            cpnTucGdl4Hou11H21.setMondaiUmu1(0);
            // 立地環境上の問題の無
            cpnTucGdl4Hou11H21.setMondaiUmu2(0);
        }

        // 特記事項
        cpnTucGdl4Hou11H21.setMemoKnj(houInfo.getMemoKnj());
        // 調理器具
        cpnTucGdl4Hou11H21.setSetsubi4(CommonDtoUtil.strValToInt(houInfo.getSetsubi4()));
        // 暖房器具（ガス）
        cpnTucGdl4Hou11H21.setSetsubi5Shu1(CommonDtoUtil.strValToInt(houInfo.getSetsubi5Shu1()));
        // 暖房器具（電気）
        cpnTucGdl4Hou11H21.setSetsubi5Shu2(CommonDtoUtil.strValToInt(houInfo.getSetsubi5Shu2()));
        // 暖房器具（灯油）
        cpnTucGdl4Hou11H21.setSetsubi5Shu3(CommonDtoUtil.strValToInt(houInfo.getSetsubi5Shu3()));
        // 暖房器具（その他）
        cpnTucGdl4Hou11H21.setSetsubi5Shu4(CommonDtoUtil.strValToInt(houInfo.getSetsubi5Shu4()));
        // その他（メモ）（暖房器具）
        cpnTucGdl4Hou11H21.setSetsubi5Shu4MemoKnj(houInfo.getSetsubi5Shu4MemoKnj());

        return cpnTucGdl4Hou11H21;

    }

    /**
     * ＧＬ＿住宅等の状況（R３改訂）テーブル（cpn_tuc_gdl5_hou11_r3）の登録詳細パラメータを設定する
     * 
     * @param inDto    施設マスタ
     * @param isInsert 登録/更新
     * 
     * @return ＧＬ＿住宅等の状況（R３改訂）テーブル（cpn_tuc_gdl5_hou11_r3）の登録詳細パラメータ
     */
    private CpnTucGdl5Hou11R3 setCpnTucGdl5Hou11R3(Gui00797HouInfo houInfo, Boolean isInsert) {
        CpnTucGdl5Hou11R3 cpnTucGdl5Hou11R3 = new CpnTucGdl5Hou11R3();

        // １戸建て・集合住宅
        cpnTucGdl5Hou11R3.setHouseShu(CommonDtoUtil.strValToInt(houInfo.getHouseShu()));
        // 賃貸・所有・給与住宅等
        cpnTucGdl5Hou11R3.setShoyuShu(CommonDtoUtil.strValToInt(houInfo.getShoyuShu()));
        // その他(ﾒﾓ）
        cpnTucGdl5Hou11R3.setShoyuMemoKnj(houInfo.getShoyuMemoKnj());

        if (isInsert) {
            // 平屋建て
            cpnTucGdl5Hou11R3.setHiraya(0);
            // ○階建て
            cpnTucGdl5Hou11R3.setKaidate(0);
            // 集合住宅
            cpnTucGdl5Hou11R3.setShugou(0);
        }

        // 専用居室の有無
        cpnTucGdl5Hou11R3.setKyoUmu(CommonDtoUtil.strValToInt(houInfo.getKyoUmu()));
        // １階
        cpnTucGdl5Hou11R3.setKyoKai1(CommonDtoUtil.strValToInt(houInfo.getKyoKai1()));
        // ２階
        cpnTucGdl5Hou11R3.setKyoKai2(CommonDtoUtil.strValToInt(houInfo.getKyoKai2()));
        // その他（２階以上）
        cpnTucGdl5Hou11R3.setKyoKai3(CommonDtoUtil.strValToInt(houInfo.getKyoKai3()));
        // 階数
        cpnTucGdl5Hou11R3.setKyoKaisu(CommonDtoUtil.strValToInt(houInfo.getKyoKaisu()));
        // エレベーターの有無
        cpnTucGdl5Hou11R3.setElevatorUmu(CommonDtoUtil.strValToInt(houInfo.getElevatorUmu()));
        // 寝具
        cpnTucGdl5Hou11R3.setSleepWhere(CommonDtoUtil.strValToInt(houInfo.getSleepWhere()));
        // ベッドの種類１(固定）
        cpnTucGdl5Hou11R3.setBedShu1(CommonDtoUtil.strValToInt(houInfo.getBedShu1()));
        // ベッドの種類2（ギャッチ）
        cpnTucGdl5Hou11R3.setBedShu2(CommonDtoUtil.strValToInt(houInfo.getBedShu2()));
        // ベッドの種類3（電動）
        cpnTucGdl5Hou11R3.setBedShu3(CommonDtoUtil.strValToInt(houInfo.getBedShu3()));
        // ベッドの種類4(その他）
        cpnTucGdl5Hou11R3.setBedShu4(CommonDtoUtil.strValToInt(houInfo.getBedShu4()));
        // その他(ﾒﾓ）（寝具）
        cpnTucGdl5Hou11R3.setBedMemoKnj(houInfo.getBedMemoKnj());
        // 陽あたり
        cpnTucGdl5Hou11R3.setHiatari(CommonDtoUtil.strValToInt(houInfo.getHiatari()));
        // 暖房
        cpnTucGdl5Hou11R3.setHeater(CommonDtoUtil.strValToInt(houInfo.getHeater()));
        // 冷房
        cpnTucGdl5Hou11R3.setAirCooling(CommonDtoUtil.strValToInt(houInfo.getAirCooling()));
        // 和式
        cpnTucGdl5Hou11R3.setToilShu1(CommonDtoUtil.strValToInt(houInfo.getToilShu1()));
        // 洋式
        cpnTucGdl5Hou11R3.setToilShu2(CommonDtoUtil.strValToInt(houInfo.getToilShu2()));
        // その他（トイレ）
        cpnTucGdl5Hou11R3.setToilShu3(CommonDtoUtil.strValToInt(houInfo.getToilShu3()));
        // その他(ﾒﾓ）（トイレ）
        cpnTucGdl5Hou11R3.setToilMemoKnj(houInfo.getToilMemoKnj());
        // 手すりの有無（トイレ）
        cpnTucGdl5Hou11R3.setToilTesuriUmu(CommonDtoUtil.strValToInt(houInfo.getToilTesuriUmu()));
        // 段差の有無（トイレ）
        cpnTucGdl5Hou11R3.setToilDansaUmu(CommonDtoUtil.strValToInt(houInfo.getToilDansaUmu()));
        // (自宅に）浴槽の有無
        cpnTucGdl5Hou11R3.setBathUmu(CommonDtoUtil.strValToInt(houInfo.getBathUmu()));
        // 手すりの有無（浴槽）
        cpnTucGdl5Hou11R3.setBathTesuriUmu(CommonDtoUtil.strValToInt(houInfo.getBathTesuriUmu()));
        // 段差の有無（浴槽）
        cpnTucGdl5Hou11R3.setBathDansaUmu(CommonDtoUtil.strValToInt(houInfo.getBathDansaUmu()));
        // 福祉機器（室外）使用状態 1:使用 2:使用しない
        cpnTucGdl5Hou11R3.setHukuOutUse(CommonDtoUtil.strValToInt(houInfo.getHukuOutUse()));
        // 車いす（室外）
        cpnTucGdl5Hou11R3.setHukuOut1(CommonDtoUtil.strValToInt(houInfo.getHukuOut1()));
        // 電動車いす（室外）
        cpnTucGdl5Hou11R3.setHukuOut2(CommonDtoUtil.strValToInt(houInfo.getHukuOut2()));
        // 杖（室外）
        cpnTucGdl5Hou11R3.setHukuOut3(CommonDtoUtil.strValToInt(houInfo.getHukuOut3()));
        // 歩行器（室外）
        cpnTucGdl5Hou11R3.setHukuOut4(CommonDtoUtil.strValToInt(houInfo.getHukuOut4()));
        // その他（室外）
        cpnTucGdl5Hou11R3.setHukuOut5(CommonDtoUtil.strValToInt(houInfo.getHukuOut5()));
        // その他(ﾒﾓ）（室外）
        cpnTucGdl5Hou11R3.setOutMemoKnj(houInfo.getOutMemoKnj());
        // 福祉機器（室内）使用状態 1:使用 2:使用しない
        cpnTucGdl5Hou11R3.setHukuInUse(CommonDtoUtil.strValToInt(houInfo.getHukuInUse()));
        // 車いす（室内）
        cpnTucGdl5Hou11R3.setHukuIn1(CommonDtoUtil.strValToInt(houInfo.getHukuIn1()));
        // 電動車いす（室内）
        cpnTucGdl5Hou11R3.setHukuIn2(CommonDtoUtil.strValToInt(houInfo.getHukuIn2()));
        // 杖（室内）
        cpnTucGdl5Hou11R3.setHukuIn3(CommonDtoUtil.strValToInt(houInfo.getHukuIn3()));
        // 歩行器（室内）
        cpnTucGdl5Hou11R3.setHukuIn4(CommonDtoUtil.strValToInt(houInfo.getHukuIn4()));
        // その他（室内）
        cpnTucGdl5Hou11R3.setHukuIn5(CommonDtoUtil.strValToInt(houInfo.getHukuIn5()));
        // その他(ﾒﾓ）（室内）
        cpnTucGdl5Hou11R3.setInMemoKnj(houInfo.getInMemoKnj());

        if (isInsert) {
            // 洗濯機
            cpnTucGdl5Hou11R3.setSetsubi1(0);
            // 湯沸器
            cpnTucGdl5Hou11R3.setSetsubi2(0);
            // 冷蔵庫
            cpnTucGdl5Hou11R3.setSetsubi3(0);
            // 立地環境上の問題の有
            cpnTucGdl5Hou11R3.setMondaiUmu1(0);
            // 立地環境上の問題の無
            cpnTucGdl5Hou11R3.setMondaiUmu2(0);
        }
        // 特記事項
        cpnTucGdl5Hou11R3.setMemoKnj(houInfo.getMemoKnj());
        // 調理器具
        cpnTucGdl5Hou11R3.setSetsubi4(CommonDtoUtil.strValToInt(houInfo.getSetsubi4()));
        // 暖房器具（ガス）
        cpnTucGdl5Hou11R3.setSetsubi5Shu1(CommonDtoUtil.strValToInt(houInfo.getSetsubi5Shu1()));
        // 暖房器具（電気）
        cpnTucGdl5Hou11R3.setSetsubi5Shu2(CommonDtoUtil.strValToInt(houInfo.getSetsubi5Shu2()));
        // 暖房器具（灯油）
        cpnTucGdl5Hou11R3.setSetsubi5Shu3(CommonDtoUtil.strValToInt(houInfo.getSetsubi5Shu3()));
        // 暖房器具（その他）
        cpnTucGdl5Hou11R3.setSetsubi5Shu4(CommonDtoUtil.strValToInt(houInfo.getSetsubi5Shu4()));
        // その他（メモ）（暖房器具）
        cpnTucGdl5Hou11R3.setSetsubi5Shu4MemoKnj(houInfo.getSetsubi5Shu4MemoKnj());

        return cpnTucGdl5Hou11R3;

    }

}