package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComPrintSettingsSaveDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComSystemIniDto;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * 印刷対象一覧情報取得の出力DTO.
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class PlanAllPrintSettingsSubjectSelectServiceOutDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 帳票選択状態文字列 */
    private String selectedReport;

    /** 印刷対象履歴リスト */
    private List<PrintSettingsHistoryDto> prtHistoryList;

    /** 印刷設定情報リスト */
    @NotEmpty
    private List<GyoumuComPrintSettingsSaveDto> prtList;

    /** システムINI情報リスト */
    private List<GyoumuComSystemIniDto> sysIniInfoList;
}
