package jp.ndsoft.carebase.cmn.api.report.dto;

import jp.ndsoft.smh.framework.global.base.dto.PdfOutDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * U00891_計画書一括印刷 帳票出力
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PlanAllPrintReportServiceOutDto extends PdfOutDto {
    /** UID. */
    private static final long serialVersionUID = 1L;

    /** pdfアップロード先パス */
    private String pdfUpLoadPath;

}
