package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.service.dto.DailyScheduleMasterUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.DailyScheduleMasterUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.KghMocKrkSsmInitializationMasterInfoDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;

import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkSsmMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsm;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsmCriteria;

/**
 * GUI01056_日課計画マスタ
 * 日課計画マスタ情報保存
 *
 * <AUTHOR>
 */
@Service
public class DailyScheduleMasterUpdateServiceImpl
        extends UpdateServiceImpl<DailyScheduleMasterUpdateServiceInDto, DailyScheduleMasterUpdateServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    /** 分類1：2 */
    private static final Integer BUNRUI_1_ID = 2;
    /** 分類2：3 */
    private static final Integer BUNRUI_2_ID = 6;
    /** 初期設定マスタ情報取得 */
    @Autowired
    private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;

    /** 初期設定マスタ登録更新 */
    @Autowired
    private KghMocKrkSsmMapper kghMocKrkSsmMapper;

    /**
     * 日課計画マスタ情報保存
     * 
     * @param inDto 日課計画マスタ情報保存の入力DTO
     * @return 日課計画マスタ情報保存の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected DailyScheduleMasterUpdateServiceOutDto mainProcess(
            DailyScheduleMasterUpdateServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        // 単項目チェック以外の入力チェック 特になし

        // DTOOUT情報
        DailyScheduleMasterUpdateServiceOutDto outDto = new DailyScheduleMasterUpdateServiceOutDto();

        // 日課計画マスタ保存処理
        // データ存在チェックを行う
        KrkSsmInfoByCriteriaInEntity inEntity = new KrkSsmInfoByCriteriaInEntity();
        // 施設ID
        inEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        inEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 分類1
        inEntity.setBunrui1Id(BUNRUI_1_ID);
        // 分類2
        inEntity.setBunrui2Id(BUNRUI_2_ID);
        List<KrkSsmInfoOutEntity> outEntityList = kghMocKrkSsmSelectMapper.findKrkSsmInfoByCriteria(inEntity);

        // リクエストパラメータ.初期設定マスタ情報リストを繰り返して、00-00 初期設定マスタ登録処理
        for (KghMocKrkSsmInitializationMasterInfoDto x : inDto.getBunrui3SetList()) {
            if (x.getBunrui3Id().isEmpty() || outEntityList.stream()
                    .filter(y -> y.getBunrui3Id().equals(Integer.valueOf(x.getBunrui3Id()))).count() <= 0) {
                KghMocKrkSsm kghMocKrkSsm = new KghMocKrkSsm();
                kghMocKrkSsm.setBunrui1Id(BUNRUI_1_ID);
                kghMocKrkSsm.setBunrui2Id(BUNRUI_2_ID);
                kghMocKrkSsm.setShisetuId(Integer.valueOf(inDto.getShisetuId()));
                kghMocKrkSsm.setSvJigyoId(Integer.valueOf(inDto.getSvJigyoId()));
                kghMocKrkSsm.setBunrui3Id(Integer.valueOf(x.getBunrui3Id()));
                kghMocKrkSsm.setIntValue(Integer.valueOf(x.getBunrui3Value()));
                // CommonDaoUtil.setInsertCommonColumns(kghMocKrkSsm);
                int count = kghMocKrkSsmMapper.insertSelective(kghMocKrkSsm);
                if (count == 0) {
                    throw new ExclusiveException();
                }
            } else {
                KrkSsmInfoOutEntity krkSsmInfoOutEntity = outEntityList.stream()
                        .filter(y -> y.getBunrui3Id().equals(Integer.valueOf(x.getBunrui3Id()))).findFirst().get();

                KghMocKrkSsmCriteria criteria = new KghMocKrkSsmCriteria();
                criteria.createCriteria().andBunrui1IdEqualTo(krkSsmInfoOutEntity.getBunrui1Id())
                        .andBunrui2IdEqualTo(krkSsmInfoOutEntity.getBunrui2Id()).andBunrui3IdEqualTo(
                                krkSsmInfoOutEntity.getBunrui3Id())
                        .andShisetuIdEqualTo(krkSsmInfoOutEntity.getShisetuId()).andSvJigyoIdEqualTo(
                                krkSsmInfoOutEntity.getSvJigyoId());
                KghMocKrkSsm kghMocKrkSsm = new KghMocKrkSsm();
                kghMocKrkSsm.setBunrui1Id(krkSsmInfoOutEntity.getBunrui1Id());
                kghMocKrkSsm.setBunrui2Id(krkSsmInfoOutEntity.getBunrui2Id());
                kghMocKrkSsm.setBunrui3Id(krkSsmInfoOutEntity.getBunrui3Id());
                kghMocKrkSsm.setShisetuId(krkSsmInfoOutEntity.getShisetuId());
                kghMocKrkSsm.setSvJigyoId(krkSsmInfoOutEntity.getSvJigyoId());
                kghMocKrkSsm.setIntValue(Integer.valueOf(x.getBunrui3Value()));

                int count = kghMocKrkSsmMapper.updateByCriteriaSelective(kghMocKrkSsm, criteria);
                if (count == 0) {
                    throw new ExclusiveException();
                }
            }
        }

        // 6. 上記処理で取得した結果レスポンスを返却する。
        LOG.info(Constants.END);
        return outDto;
    }
}
