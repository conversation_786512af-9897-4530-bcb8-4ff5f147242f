package jp.ndsoft.carebase.cmn.api.logic;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.dto.ChkPrinterMxdwOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KghSmglKinouIdOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocSecSettingByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocSecSettingOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SmglChkKinouIdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SmglChkKinouIdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SystemTimeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SystemTimeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSecSettingSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.DUALSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.SmglMocRestrictSelectMapper;

/**
 * e-文書法区分取得
 * 
 * <AUTHOR>
 */
@Component
public class KghSmglHistoryLogic {

    /** e-文書法区分: 0 */
    private static final Integer E_BUNSHO_KBN_ZERO = 0;

    /** セキュリティ基本設定情報取得 */
    @Autowired
    private ComMocSecSettingSelectMapper comMocSecSettingSelectMapper;
    /** */
    @Autowired
    private SmglMocRestrictSelectMapper smglMocRestrictSelectMapper;
    /** 履歴保存ストアドプロシージャ情報取得 */
    @Autowired
    private DUALSelectMapper dUALSelectMapper;
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** エラーメッセージ */
    private static final String ERROR_MESSAGE = "更新履歴ファイルを保存できませんでした。\nシステム管理者に確認の上、\n更新履歴ファイルを保存するためのプリンタ\n「Microsoft XPS Document Writer」を追加し、\n再度、保存を行ってください。";

    /**
     * 電子保存の３原則 履歴の作成・削除制限の対象機能チェック No.8 f_kgh_smgl_chk_kinou_id
     * 
     * @param kinouId 機能ID
     * @return asYmdCol 日付カラム名 asKeyCol キーカラム名
     */
    public KghSmglKinouIdOutDto chkKghSmglKinouId(Integer kinouId) {
        SmglChkKinouIdByCriteriaInEntity in = new SmglChkKinouIdByCriteriaInEntity();
        in.setKinouId(kinouId);
        List<SmglChkKinouIdOutEntity> items = smglMocRestrictSelectMapper.findSmglChkKinouIdByCriteria(in);
        if (items != null && items.size() > 0) {
            KghSmglKinouIdOutDto ret = new KghSmglKinouIdOutDto();
            ret.setYmdCol(items.get(0).getYmdCol());
            ret.setKeyCol(items.get(0).getKeyCol());
            return ret;
        }
        return null;
    }

    /**
     * e-文書法区分取得 関数名：getKghComEBunshoKbn
     * 
     * @return 電子ファイル保存設定フラグ,True：適用する,False：適用しない
     */
    public boolean getKghComEBunshoKbn() {
        //2025.05.14 misawa MOD STR 障害対応（潜在-3221）
        if(nds3GkFunc01Logic.isF3gkAsp(CommonConstants.BOOLEAN_FALSE)){
            // OLP環境の場合は、e-文書機能は使用不可
	        // オンプレ→OLP環境へ移行したユーザは「適用する」になっているケースもある為、チェックする（非表示なので画面から「適用する」「適用しない」の切り替え不可能）
            return false;
        }
        //2025.05.14 misawa MOD END 障害対応（潜在-3221）

        // データベースからe-文書法設定を取得
        List<ComMocSecSettingOutEntity> results = comMocSecSettingSelectMapper
                .findComMocSecSettingByCriteria(new ComMocSecSettingByCriteriaInEntity());

        // 取得結果が存在するか確認
        if (CollectionUtils.isEmpty(results)) {
            return false;
        }
        // 先頭レコードのe_bunsho_kbn値を取得
        Integer eBunshoKbn = results.getFirst().getEBunshoKbn();

        // 数値からbooleanに変換（0: false, その他: true）
        return eBunshoKbn != E_BUNSHO_KBN_ZERO;
    }

    /**
     * Microsoft XPS Document Writerの存在チェックを行います 関数名：chkPrinterMxdw
     * 
     * @return true：成功、false：失敗
     */
    public ChkPrinterMxdwOutDto chkPrinterMxdw() {
        ChkPrinterMxdwOutDto outDto = new ChkPrinterMxdwOutDto();
        boolean result = false;
        // プリンタ名の取得 TODO
        if (!result) {
            // エラーメッセージの設定
            outDto.setErrMessage(ERROR_MESSAGE);
        }
        outDto.setResult(result);
        return outDto;
    }

    /**
     * 複写先の履歴番号が変更されないように作成日を変更する 関数名：gf_kgh_smgl_change_ymd
     * 
     * @param asDataobject HC2.0、MDS2.1の検討表
     * @param kinouId      機能ID
     * @param adsSaki      作成日の対象に変更します
     * @return 戻り値 ： boolean
     * <AUTHOR>
     */
    public boolean kghSmglChangeYmd(List<?> asDataobject, Integer kinouId, List<?> adsSaki) {
        // 電子保存の３原則 履歴の作成・削除制限の対象機能チェック
        KghSmglKinouIdOutDto kghSmglKinouIdOutDto = chkKghSmglKinouId(kinouId);
        if (Objects.isNull(kghSmglKinouIdOutDto)) {
            return true;
        }
        // DBサーバの日付を取得
        SystemTimeByCriteriaInEntity systemTimeByCriteriaInEntity = new SystemTimeByCriteriaInEntity();

        // DAOを実行
        List<SystemTimeOutEntity> kghKrkFreeFaceOpeOutEntityList = this.dUALSelectMapper
                .findSystemTimeByCriteria(systemTimeByCriteriaInEntity);
        // FROMAT "yyyy/MM/dd"
        String dateString = kghKrkFreeFaceOpeOutEntityList.get(0).getNowTime();
        LocalDate date = LocalDate.parse(dateString, DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD));
        String lsNewYmd = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD).format(date);

        // 最新の日付を取得

        // HC2.0、MDS2.1の検討表
        if (CollectionUtils.isEmpty(asDataobject)) {
            return true;
        }

        // 日付の降順でソートする
        asDataobject = asDataobject.stream().sorted(Comparator.comparing(x -> {
            try {
                return Objects.toString(x.getClass()
                        .getMethod(toMethodName(CommonConstants.STR_GET, kghSmglKinouIdOutDto.getYmdCol())).invoke(x),
                        StringUtils.EMPTY);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, Comparator.reverseOrder())).collect(Collectors.toList());

        // 既存の履歴番号が変更されないように日付を変更する
        try {
            Field field = asDataobject.getFirst().getClass().getDeclaredField(kghSmglKinouIdOutDto.getYmdCol());
            field.setAccessible(true);
            String lsTmpYmd = (String) field.get(asDataobject.getFirst());
            if (lsNewYmd.compareTo(lsTmpYmd) < 0) {
                lsNewYmd = lsTmpYmd;
            }
            for (int i = 0; i < CollectionUtils.size(adsSaki); i++) {
                Field fieldAds = adsSaki.get(i).getClass().getDeclaredField(kghSmglKinouIdOutDto.getYmdCol());
                fieldAds.setAccessible(true);
                String lsSakiYmd = (String) fieldAds.get(adsSaki.get(i));
                if (StringUtils.isNotEmpty(lsSakiYmd) && lsSakiYmd.compareTo(lsNewYmd) <= 0) {
                    fieldAds.set(kghSmglKinouIdOutDto.getYmdCol(), lsNewYmd);
                }
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * ターメソッド名に変換します
     * 
     * @param method    ターメソッド
     * @param fieldName 変換対象のフィールド名
     * @return ターメソッド名
     */
    private String toMethodName(String method, String fieldName) {
        if (StringUtils.isEmpty(fieldName) || StringUtils.isEmpty(method)) {
            return null;
        }
        // 下線を分割して大文字と小文字を変換する
        String camelCase = Arrays.stream(fieldName.split(CommonConstants.UNDERSCORE_SEPARATOR_REGEX))
                .filter(part -> !part.isEmpty())
                .map(part -> part.substring(CommonConstants.INT_0, CommonConstants.INT_1).toUpperCase()
                        + part.substring(CommonConstants.INT_1).toLowerCase())
                .collect(Collectors.joining());
        return method + camelCase;
    }
}
