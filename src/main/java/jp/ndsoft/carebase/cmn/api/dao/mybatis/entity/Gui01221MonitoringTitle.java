package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.carebase.framework.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GUI01221_モニタリングタイトルマスタ
 * 
 * @description　モニタリングタイトル　エンティティ
 * <AUTHOR>　高金康
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Gui01221MonitoringTitle implements Serializable, IEntity{
 /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** マスタヘッダID */
    @NotEmpty
    private String free1Id;
    /** 帳票タイトル */
    @NotEmpty
    private String titleKnj;
    /** 行の分割数（上） */
    @NotEmpty
    private String columnCount;
    /** 行の分割数（下） */
    @NotEmpty
    private String columnCount2;
    /** 表示順 */
    @NotEmpty
    private String sort;
    /** 印刷文字サイズ */
    @NotEmpty
    private String fontSize;
    /** 用紙サイズ */
    @NotEmpty
    private String youshiSize;
    /** 使用中フラグ */
    private String useFlg;
    /** 初期値作成フラグ */
    private String defUpdateFlg;
    /** 適用フラグ */
    private String dmyTekiyouFlg;
    /** 他事業者適用フラグ */
    private String dmyOtherTekiyouFlg;
    /** 複写元マスタヘッダID */
    private String dmyFree1Id;
    /** ロックフラグ */
    private String dmyLockFlg;
    /** 進捗管理対象フラグ */
    private String dmySncFlg;
    /** 固定様式区分 */
    private String koteiKbn;
    /** 分割数（上）更新状態 */
    private String columnCountStatus;
    /** 更新区分 */
    private String updateKbn;
}
