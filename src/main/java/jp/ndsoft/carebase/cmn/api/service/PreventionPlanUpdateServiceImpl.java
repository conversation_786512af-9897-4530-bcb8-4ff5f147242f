package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import io.micrometer.common.util.StringUtils;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.GyoumuComLogic;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComElectronicSaveInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComElectronicSaveInitMasterData;
import jp.ndsoft.carebase.cmn.api.service.dto.PreventionPlanHokenSvData;
import jp.ndsoft.carebase.cmn.api.service.dto.PreventionPlanHokenYmdData;
import jp.ndsoft.carebase.cmn.api.service.dto.PreventionPlanTab2Data;
import jp.ndsoft.carebase.cmn.api.service.dto.PreventionPlanTab3Data;
import jp.ndsoft.carebase.cmn.api.service.dto.PreventionPlanTab4Data;
import jp.ndsoft.carebase.cmn.api.service.dto.PreventionPlanUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PreventionPlanUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.KghTucKrkKikanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.KycTucPlan11Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.KycTucPlan12Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.KycTucPlan13Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.KycTucPlan14Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.KycTucPlan15Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.KycTucPlan16Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTucKrkKikan;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KycTucPlan11;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KycTucPlan11Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KycTucPlan12;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KycTucPlan12Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KycTucPlan13;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KycTucPlan13Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KycTucPlan14;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KycTucPlan14Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KycTucPlan15;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KycTucPlan15Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KycTucPlan16;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KycTucPlan16Criteria;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * GUI01088_目標とする生活（予防計画書）情報更新サービス.
 *
 * <AUTHOR>
 */
@Service
public class PreventionPlanUpdateServiceImpl
        extends UpdateServiceImpl<PreventionPlanUpdateServiceInDto, PreventionPlanUpdateServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 操作区分 3:削除 */
    private static final String OPER_FLAG_3 = "3";
    /** [mnu3][3GK]計画表 */
    private static final String MENU3_KNJ = "[mnu3][3GK]計画表";

    /** 採番 */
    // @Autowired
    // private Numbering numbering;
    /** 27-06 記録共通期間（kgh_tuc_krk_kikan） */
    @Autowired
    private KghTucKrkKikanMapper kghTucKrkKikanMapper;

    /** 28-45 介護予防サービス・支援計画表ヘッダ（kyc_tuc_plan11） */
    @Autowired
    private KycTucPlan11Mapper kycTucPlan11Mapper;

    /** 28-53 介護予防サービス・支援計画書データ（kyc_tuc_plan12） */
    @Autowired
    private KycTucPlan12Mapper kycTucPlan12Mapper;

    /** 28-54 介護予防サービス・支援計画書データ（総合的課題～目標）（kyc_tuc_plan13） */
    @Autowired
    private KycTucPlan13Mapper kycTucPlan13Mapper;

    /** 28-53 介護予防サービス・支援計画書データ（支援計画）（kyc_tuc_plan14） */
    @Autowired
    private KycTucPlan14Mapper kycTucPlan14Mapper;

    /** 28-54 介護予防サービス・支援計画書データ（サービス、時間）（kyc_tuc_plan15） */
    @Autowired
    private KycTucPlan15Mapper kycTucPlan15Mapper;

    /** 28-69 介護予防サービス・支援計画書データ（月日指定）（kyc_tuc_plan16） */
    @Autowired
    private KycTucPlan16Mapper kycTucPlan16Mapper;

    /** 業務共通用 */
    @Autowired
    private GyoumuComLogic gyoumuComLogic;

    /**
     * チェック
     * 
     * @param inDto GUI01088_目標とする生活（予防計画書）情報更新の入力DTO
     */
    @Override
    protected void checkProcess(final PreventionPlanUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // 共通部品権限チェック
        gyoumuComLogic.checkCommon(inDto, MENU3_KNJ);
        LOG.info(Constants.END);
    }

    /**
     * GUI01088_目標とする生活（予防計画書）情報更新処理
     * 
     * @param inDto GUI01088_目標とする生活（予防計画書）情報更新の入力DTO
     * @return GUI01088_目標とする生活（予防計画書）情報更新の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected PreventionPlanUpdateServiceOutDto mainProcess(
            PreventionPlanUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // DTOOUT情報
        PreventionPlanUpdateServiceOutDto outDto = new PreventionPlanUpdateServiceOutDto();

        // 3.削除処理の実施
        deleteInfo(inDto, outDto);

        // 4.登録更新処理の実施
        updateInfo(inDto, outDto);

        // 5. 電子保存
        gyoumuComLogic.print(getPrintInDto(inDto), MENU3_KNJ);

        // 6. 上記処理で取得した結果レスポンスを返却する。
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * パラメータを取得する。
     * 
     * @param inDto inデータ
     * @return パラメータ
     */
    private GyoumuComElectronicSaveInDto getPrintInDto(PreventionPlanUpdateServiceInDto inDto) {
        GyoumuComElectronicSaveInDto param = new GyoumuComElectronicSaveInDto();
        // ログイン職員ＩＤ
        param.setLoginShokuinId(inDto.getLoginShokuinId());
        // 操作区分
        param.setOperaFlg(inDto.getOperaFlg());
        // 施設ID
        param.setShisetuId(inDto.getShisetuId());
        // 計画期間ID
        param.setSc1Id(inDto.getSc1Id());
        // 利用者ID
        param.setUserId(inDto.getUserId());
        // 履歴ID
        param.setRirekiId(inDto.getRirekiId());
        // 事業所ID
        param.setSvJigyoId(inDto.getSvJigyoId());
        // 法人ID
        param.setHoujinId(inDto.getHoujinId());
        // 事業所CD
        param.setSvJigyoCd(inDto.getSvJigyoCd());
        // システム日付
        param.setSysYmd(inDto.getSysYmd());
        // サービス事業者名称
        param.setSvJigyoKnj(inDto.getSvJigyoKnj());
        // 初期設定マスタの情報
        GyoumuComElectronicSaveInitMasterData initMasterData = new GyoumuComElectronicSaveInitMasterData();
        if (inDto.getInitMasterObj() != null) {
            // ケアプラン方式
            initMasterData.setCpnFlg(inDto.getInitMasterObj().getCpnFlg());
            // 計画表様式
            initMasterData.setItakuKkakPrtFlg(inDto.getInitMasterObj().getItakuKkakPrtFlg());
            // メッセージ表示
            initMasterData.setMsgFlg(inDto.getInitMasterObj().getMsgFlg());
            // 期間の管理(計画表)
            initMasterData.setKikanFlg(inDto.getInitMasterObj().getItakuKkakKikanFlg());
            // 期間のカレンダー取込(計画表)
            initMasterData.setKikanYmdFlg(inDto.getInitMasterObj().getItakuKkakYmdFlg());
            // 敬称オプション
            initMasterData.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
            // 敬称
            initMasterData.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
        }
        param.setInitMasterObj(initMasterData);
        return param;
    }

    /**
     * 削除処理
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @throws ExclusiveException Exception
     */
    private void deleteInfo(
            PreventionPlanUpdateServiceInDto inDto,
            PreventionPlanUpdateServiceOutDto outDto) throws ExclusiveException {

        // 操作区分
        String operaFlg = inDto.getOperaFlg();
        // リクエストパラメータ.操作区分が「3:削除」以外の場合、「3.削除処理の実施」処理終了
        if (!OPER_FLAG_3.equals(operaFlg)) {
            return;
        }

        // 保存用「目標とする生活」情報.更新区分がD:削除の場合、下記の削除処理を行う
        if (CommonDtoUtil.isDelete(inDto.getTab1DataObj())) {
            final KycTucPlan11Criteria criteria = new KycTucPlan11Criteria();
            // BigInteger modifiedCnt = new
            // BigInteger(inDto.getTab1DataObj().getModifiedCnt());
            criteria.createCriteria()
                    .andPlan11IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getPlan11Id()));
            // .andModifiedCntEqualTo(modifiedCnt)
            // .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
            // final KycTucPlan11 row = new KycTucPlan11();
            // 削除（論理）時の共通カラム値設定処理
            // CommonDaoUtil.setDeleteCommonColumns(row, modifiedCnt);
            kycTucPlan11Mapper.deleteByCriteria(criteria);
            // int updateCnt = kycTucPlan11Mapper.updateByCriteriaSelective(row, criteria);
            // if (updateCnt <= 0) {
            // throw new ExclusiveException();
            // }
        }
    }

    /**
     * 更新処理
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @throws ExclusiveException Exception
     */
    private void updateInfo(
            PreventionPlanUpdateServiceInDto inDto,
            PreventionPlanUpdateServiceOutDto outDto) throws Exception {

        // 計画対象期間の保存
        saveKikanInfo(inDto, outDto);

        // リクエストパラメータ.操作区分が「3:削除」の場合、「4.登録更新処理の実施」処理終了
        if (OPER_FLAG_3.equals(inDto.getOperaFlg())) {
            return;
        }

        // 履歴の保存
        saveTab1Data(inDto, outDto);

        // 詳細データの保存
        saveDetailData(inDto, outDto);
    }

    /**
     * 計画対象期間の保存
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @throws ExclusiveException Exception
     */
    private void saveKikanInfo(
            PreventionPlanUpdateServiceInDto inDto,
            PreventionPlanUpdateServiceOutDto outDto) throws Exception {
        // リクエストパラメータ.計画期間ＩＤ
        String sc1Id = inDto.getSc1Id();
        // リクエストパラメータ.計画対象期間IDが空白または0の場合、下記の新規登録処理を行う
        if (sc1Id.isBlank() || CommonConstants.STR_0.equals(sc1Id)) {

            final KghTucKrkKikan kghTucKrkKikan = new KghTucKrkKikan();
            // リクエストパラメータ.法人ＩＤ
            kghTucKrkKikan.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
            // リクエストパラメータ.施設ＩＤ
            kghTucKrkKikan.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            // リクエストパラメータ.事業所ＩＤ
            kghTucKrkKikan.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // リクエストパラメータ.利用者ＩＤ
            kghTucKrkKikan.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
            // リクエストパラメータ.種別ＩＤ
            kghTucKrkKikan.setSyubetsuId(CommonDtoUtil.strValToInt(inDto.getSyubetuId()));
            // リクエストパラメータ.保存用「計画対象期間」情報.開始日
            kghTucKrkKikan.setStartYmd(inDto.getKikanObj().getStartYmd());
            // リクエストパラメータ.保存用「計画対象期間」情報.終了日
            kghTucKrkKikan.setEndYmd(inDto.getKikanObj().getEndYmd());
            // 登録時の共通カラム値設定処理
            // CommonDaoUtil.setInsertCommonColumns(kghTucKrkKikan);
            // kghTucKrkKikan.setSc1Id(numbering.getNumber(AppUtil.getKeiyakushaId(),
            // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.KGH_TUC_KRK_KIKAN_SC1_ID.getIntValue()));

            this.kghTucKrkKikanMapper.insertSelectiveAndReturn(kghTucKrkKikan);
            // 最新の期間ID設定
            outDto.setSc1IdNew(String.valueOf(kghTucKrkKikan.getSc1Id()));
        }
    }

    /**
     * 履歴の保存
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @throws ExclusiveException Exception
     */
    private void saveTab1Data(
            PreventionPlanUpdateServiceInDto inDto,
            PreventionPlanUpdateServiceOutDto outDto) throws Exception {

        String sc1Id = inDto.getSc1Id();
        // リクエストパラメータ.計画対象期間IDが空白または0の場合
        if (sc1Id.isBlank() || CommonConstants.STR_0.equals(sc1Id)) {
            sc1Id = outDto.getSc1IdNew();
        }
        final KycTucPlan11 kycTucPlan11 = new KycTucPlan11();
        // 保存用「目標とする生活」情報.機能区分
        kycTucPlan11.setKycFlg(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getKycFlg()));
        // 保存用「目標とする生活」情報.作成日
        kycTucPlan11.setCreateYmd(inDto.getTab1DataObj().getCreateYmd());
        // 保存用「目標とする生活」情報.初回作成日
        kycTucPlan11.setShokaiYmd(inDto.getTab1DataObj().getShokaiYmd());
        // 保存用「目標とする生活」情報.支援センターの作成者（職員ID）
        kycTucPlan11.setCenterShokuId(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getCenterShokuId()));
        // 保存用「目標とする生活」情報.支援センターの作成者
        kycTucPlan11.setCenterShokuKnj(inDto.getTab1DataObj().getCenterShokuKnj());
        // 保存用「目標とする生活」情報.委託先の作成者（職員ID）
        kycTucPlan11.setItakuShokuId(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getItakuShokuId()));
        // 保存用「目標とする生活」情報.委託先の作成者
        kycTucPlan11.setItakuShokuKnj(inDto.getTab1DataObj().getItakuShokuKnj());
        // 保存用「目標とする生活」情報.認定日
        kycTucPlan11.setNinteiYmd(inDto.getTab1DataObj().getNinteiYmd());
        // 保存用「目標とする生活」情報.有効開始日
        kycTucPlan11.setYukoSYmd(inDto.getTab1DataObj().getYukoSYmd());
        // 保存用「目標とする生活」情報.有効終了日
        kycTucPlan11.setYukoEYmd(inDto.getTab1DataObj().getYukoEYmd());
        // 保存用「目標とする生活」情報.計画区分
        kycTucPlan11.setKubun(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getKubun()));
        // 保存用「目標とする生活」情報.認定区分
        kycTucPlan11.setNintei(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getNintei()));
        // 保存用「目標とする生活」情報.要支援区分
        kycTucPlan11.setYoshienKbn(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getYoshienKbn()));
        // 保存用「目標とする生活」情報.目標とする生活（1日）
        kycTucPlan11.setMokuhyoDayKnj(inDto.getTab1DataObj().getMokuhyoDayKnj());
        // 保存用「目標とする生活」情報.目標とする生活（1年）
        kycTucPlan11.setMokuhyoYearKnj(inDto.getTab1DataObj().getMokuhyoYearKnj());
        // 保存用「目標とする生活」情報.健康状態について
        kycTucPlan11.setKenkoRyuitenKnj(inDto.getTab1DataObj().getKenkoRyuitenKnj());
        // 保存用「目標とする生活」情報.妥当な支援の実施に向けた方針
        kycTucPlan11.setDatoHoshinKnj(inDto.getTab1DataObj().getDatoHoshinKnj());
        // 保存用「目標とする生活」情報.総合的な方針
        kycTucPlan11.setSogoHoshinKnj(inDto.getTab1DataObj().getSogoHoshinKnj());
        // 保存用「目標とする生活」情報.必要な事業プログラム（運動不足）
        kycTucPlan11.setProgram1Flg(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getProgram1Flg()));
        // 保存用「目標とする生活」情報.必要な事業プログラム（栄養改善）
        kycTucPlan11.setProgram2Flg(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getProgram2Flg()));
        // 保存用「目標とする生活」情報.必要な事業プログラム（口腔内ケア）
        kycTucPlan11.setProgram3Flg(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getProgram3Flg()));
        // 保存用「目標とする生活」情報.必要な事業プログラム（閉じこもり予防）
        kycTucPlan11.setProgram4Flg(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getProgram4Flg()));
        // 保存用「目標とする生活」情報.必要な事業プログラム（物忘れ予防）
        kycTucPlan11.setProgram5Flg(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getProgram5Flg()));
        // 保存用「目標とする生活」情報.必要な事業プログラム（うつ予防）
        kycTucPlan11.setProgram6Flg(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getProgram6Flg()));
        // 保存用「目標とする生活」情報.地域包括支援センターの意見
        kycTucPlan11.setCenterIkenKnj(inDto.getTab1DataObj().getCenterIkenKnj());
        // 保存用「目標とする生活」情報.地域包括支援センター確認印
        kycTucPlan11.setCenterKakuninFlg(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getCenterKakuninFlg()));
        // リクエストパラメータ.システム年月日
        kycTucPlan11.setUpYmd(inDto.getSysYmd());
        // リクエストパラメータ.ログイン職員ＩＤ
        kycTucPlan11.setUpShokuId(CommonDtoUtil.strValToInt(inDto.getLoginShokuinId()));
        // 保存用「目標とする生活」情報.同意日
        kycTucPlan11.setDoiYmd(inDto.getTab1DataObj().getDoiYmd());
        // 保存用「目標とする生活」情報.同意者
        kycTucPlan11.setDoiKnj(inDto.getTab1DataObj().getDoiKnj());
        // 保存用「目標とする生活」情報.0
        kycTucPlan11.setLockFlg(0);
        // 保存用「目標とする生活」情報.担当地域包括支援センター
        kycTucPlan11.setChiJigyoKnj(inDto.getTab1DataObj().getChiJigyoKnj());
        // 保存用「目標とする生活」情報.委託の場合の作成事業者所在
        kycTucPlan11.setItkJigyoKnj(inDto.getTab1DataObj().getItkJigyoKnj());
        // 保存用「目標とする生活」情報.必要な事業プログラム（運動不足）チェック数
        kycTucPlan11.setProgram1Cnt(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getProgram1Cnt()));
        // 保存用「目標とする生活」情報.必要な事業プログラム（栄養改善）チェック数
        kycTucPlan11.setProgram2Cnt(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getProgram2Cnt()));
        // 保存用「目標とする生活」情報.必要な事業プログラム（口腔内ケア）チェック数
        kycTucPlan11.setProgram3Cnt(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getProgram3Cnt()));
        // 保存用「目標とする生活」情報.必要な事業プログラム（閉じこもり予防）チェック数
        kycTucPlan11.setProgram4Cnt(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getProgram4Cnt()));
        // 保存用「目標とする生活」情報.必要な事業プログラム（物忘れ予防）チェック数
        kycTucPlan11.setProgram5Cnt(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getProgram5Cnt()));
        // 保存用「目標とする生活」情報.必要な事業プログラム（うつ予防）チェック数
        kycTucPlan11.setProgram6Cnt(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getProgram6Cnt()));
        // 空白
        kycTucPlan11.setCpsPlan11Id(null);
        // 保存用「目標とする生活」情報.履歴変更フラグ
        kycTucPlan11.setModifyFlg(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getModifyFlg()));
        // 保存用「目標とする生活」情報.有効期間ＩＤ
        kycTucPlan11.setTermid(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getTermid()));
        // 保存用「目標とする生活」情報.利用サービス（介護保険）
        kycTucPlan11.setService1Flg(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getService1Flg()));
        // 保存用「目標とする生活」情報.利用サービス（地域支援事業）
        kycTucPlan11.setService2Flg(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getService2Flg()));

        // 保存用「実施計画②履歴」情報.更新区分がC:新規の場合、下記の新規登録処理を行う
        if (CommonDtoUtil.isCreate(inDto.getTab1DataObj())) {
            // 保存用の計画期間ＩＤ ※「4.3.1.」を参照
            kycTucPlan11.setSc1Id(CommonDtoUtil.strValToInt(sc1Id));
            // リクエストパラメータ.事業所ＩＤ
            kycTucPlan11.setChiJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // リクエストパラメータ.事業所ＩＤ
            kycTucPlan11.setItkJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 法人ID
            kycTucPlan11.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
            // 施設ID
            kycTucPlan11.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            // 事業者ID
            kycTucPlan11.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 利用者ＩＤ
            kycTucPlan11.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
            // 新規時の共通カラム値設定処理
            // CommonDaoUtil.setInsertCommonColumns(kycTucPlan11);
            // kycTucPlan11.setPlan11Id(numbering.getNumber(AppUtil.getKeiyakushaId(),
            // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.KYC_TUC_PLAN11_PLAN11_ID.getIntValue()));

            kycTucPlan11Mapper.insertSelectiveAndReturn(kycTucPlan11);
            outDto.setRirekiIdNew(String.valueOf(kycTucPlan11.getPlan11Id()));
        } else if (CommonDtoUtil.isUpdate(inDto.getTab1DataObj())) {
            // BigInteger modifiedCnt = new
            // BigInteger(inDto.getTab1DataObj().getModifiedCnt());
            // 保存用「目標とする生活」情報.履歴ＩＤ
            kycTucPlan11.setPlan11Id(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getPlan11Id()));
            // 更新時の共通カラム値設定処理
            // CommonDaoUtil.setUpdateCommonColumns(kycTucPlan11, modifiedCnt);
            // 保存用「実施計画②履歴」情報.更新区分がU:更新の場合
            final KycTucPlan11Criteria criteria = new KycTucPlan11Criteria();
            criteria.createCriteria()
                    .andPlan11IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTab1DataObj().getPlan11Id()));
            // .andModifiedCntEqualTo(modifiedCnt)
            // .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
            int count = kycTucPlan11Mapper.updateByCriteriaSelective(kycTucPlan11, criteria);
            if (count <= 0) {
                throw new ExclusiveException();
            }
        }
    }

    /**
     * 詳細データの保存
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @throws ExclusiveException Exception
     */
    private void saveDetailData(
            PreventionPlanUpdateServiceInDto inDto,
            PreventionPlanUpdateServiceOutDto outDto) throws Exception {
        String rirekiId = inDto.getRirekiId();
        // リクエストパラメータ.履歴ＩＤが空白または0の場合
        if (rirekiId.isBlank() || CommonConstants.STR_0.equals(rirekiId)) {
            rirekiId = outDto.getRirekiIdNew();
        }
        // リクエストパラメータ.履歴ＩＤ.計画表様式が「1:A4-3枚」以外の場合、
        if (!CommonConstants.STR_1.equals(inDto.getItakuKkakPrtFlg())) {
            // 保存用「アセスメント領域と課題」リストの5件目データ（アセスメント領域IDが5）を下記のように補正する。
            // 保存用「アセスメント領域と課題」リストの5件目データ.ＩＤが空白の場合
            if (StringUtils.isEmpty(inDto.getTab2DataList().get(4).getPlan12Id())) {
                // 保存用「アセスメント領域と課題」リストから当該データを削除する
                inDto.getTab2DataList().remove(4);
            } else {
                // 保存用「アセスメント領域と課題」リストの5件目データ.更新区分に「D:削除」を設定する
                inDto.getTab2DataList().get(4).setUpdateKbn(CommonConstants.UPDATE_KBN_D);
            }
        }
        int index = 1;
        for (int i = 0; i < inDto.getTab3DataList().size(); i++) {
            PreventionPlanTab3Data tab3Data = inDto.getTab3DataList().get(i);
            // 元の総合的課題番号
            String sogoKadaiNo = tab3Data.getSogoKadaiNo();
            if (!CommonDtoUtil.isDelete(tab3Data)) {
                tab3Data.setSogoKadaiNo(String.valueOf(index));
                index++;
            }
            int seqNo = 1;
            for (int j = 0; j < inDto.getTab4DataList().size(); j++) {
                PreventionPlanTab4Data tab4Data = inDto.getTab4DataList().get(j);
                if (sogoKadaiNo.equals(tab4Data.getSogoKadaiNo())) {
                    if (!CommonDtoUtil.isDelete(tab4Data)) {
                        // 保存用「支援計画」リスト.表示順
                        tab4Data.setSeqNo(String.valueOf(seqNo));
                        seqNo++;
                    }
                    // 保存用「支援計画」リスト.総合的課題番号
                    tab4Data.setSogoKadaiNo(tab3Data.getSogoKadaiNo());
                }
            }
        }
        // 「アセスメント領域と課題」の保存

        for (PreventionPlanTab2Data data : inDto.getTab2DataList()) {
            final KycTucPlan12 kycTucPlan12 = new KycTucPlan12();
            // 保存用の履歴ＩＤ ※「4.4.1.」を参照
            kycTucPlan12.setPlan11Id(CommonDtoUtil.strValToInt(rirekiId));
            // 保存用「アセスメント領域と課題」リスト.アセスメント領域ID
            kycTucPlan12.setAssryoId(CommonDtoUtil.strValToInt(data.getAssryoId()));
            // 保存用「アセスメント領域と課題」リスト.アセスメント領域と現在の状況
            kycTucPlan12.setJokyoKnj(data.getJokyoKnj());
            // 保存用「アセスメント領域と課題」リスト.本人・家族の意欲・意向
            kycTucPlan12.setIyokuikouKnj(data.getIyokuikouKnj());
            // 保存用「アセスメント領域と課題」リスト.領域における課題（背景・原因）フラグ
            kycTucPlan12.setKadaiFlg(CommonDtoUtil.strValToInt(data.getKadaiFlg()));
            // 保存用「アセスメント領域と課題」リスト.領域における課題（背景・原因）
            kycTucPlan12.setKadaiKnj(data.getKadaiKnj());
            // 保存用「長期」リスト.更新区分がC:新規の場合、下記の新規登録処理を行う
            if (CommonDtoUtil.isCreate(data)) {
                // 登録時の共通カラム値設定処理
                // CommonDaoUtil.setInsertCommonColumns(kycTucPlan12);
                // kycTucPlan12.setPlan12Id(numbering.getNumber(AppUtil.getKeiyakushaId(),
                // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.KYC_TUC_PLAN12_PLAN12_ID.getIntValue()));

                kycTucPlan12Mapper.insertSelective(kycTucPlan12);
            } else if (CommonDtoUtil.isUpdate(data)) {
                // BigInteger modifiedCnt = new BigInteger(data.getModifiedCnt());
                // if (CommonDtoUtil.isUpdate(data)) {
                // // 更新時の共通カラム値設定処理
                // CommonDaoUtil.setUpdateCommonColumns(kycTucPlan12, modifiedCnt);
                // } else {
                // // 削除（論理）時の共通カラム値設定処理
                // CommonDaoUtil.setDeleteCommonColumns(kycTucPlan12, modifiedCnt);
                // }
                final KycTucPlan12Criteria criteria = new KycTucPlan12Criteria();
                criteria.createCriteria()
                        .andPlan12IdEqualTo(CommonDtoUtil.strValToInt(data.getPlan12Id()));
                // .andModifiedCntEqualTo(modifiedCnt)
                // .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
                int count = kycTucPlan12Mapper.updateByCriteriaSelective(kycTucPlan12, criteria);
                if (count <= 0) {
                    throw new ExclusiveException();
                }
            } else if (CommonDtoUtil.isDelete(data)) {
                final KycTucPlan12Criteria criteria = new KycTucPlan12Criteria();
                criteria.createCriteria()
                        .andPlan12IdEqualTo(CommonDtoUtil.strValToInt(data.getPlan12Id()));
                kycTucPlan12Mapper.deleteByCriteria(criteria);
            }
        }

        // 「総合的課題と目標」の保存
        for (PreventionPlanTab3Data data : inDto.getTab3DataList()) {
            final KycTucPlan13 kycTucPlan13 = new KycTucPlan13();
            // 保存用の履歴ＩＤ ※「4.4.1.」を参照
            kycTucPlan13.setPlan11Id(CommonDtoUtil.strValToInt(rirekiId));
            // 保存用「総合的課題と目標」リスト.総合的課題番号
            kycTucPlan13.setSogoKadaiNo(CommonDtoUtil.strValToInt(data.getSogoKadaiNo()));
            // 保存用「総合的課題と目標」リスト.総合的課題
            kycTucPlan13.setSogoKadaiKnj(data.getSogoKadaiKnj());
            // 保存用「総合的課題と目標」リスト.課題に対する目標と具体策の提案
            kycTucPlan13.setSogoTeianKnj(data.getSogoTeianKnj());
            // 保存用「総合的課題と目標」リスト.具体策についての意向 本人・家族
            kycTucPlan13.setSogoIkouKnj(data.getSogoIkouKnj());
            // 保存用「総合的課題と目標」リスト.目標
            kycTucPlan13.setSogoMokuhyoKnj(data.getSogoMokuhyoKnj());
            // 保存用「総合的課題と目標」リスト.課題番号
            kycTucPlan13.setKadaiNo(CommonDtoUtil.strValToInt(data.getKadaiNo()));
            // 保存用「長期」リスト.更新区分がC:新規の場合、下記の新規登録処理を行う
            if (CommonDtoUtil.isCreate(data)) {
                // 登録時の共通カラム値設定処理
                // CommonDaoUtil.setInsertCommonColumns(kycTucPlan13);
                // kycTucPlan13.setPlan13Id(numbering.getNumber(AppUtil.getKeiyakushaId(),
                // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.KYC_TUC_PLAN13_PLAN13_ID.getIntValue()));
                kycTucPlan13Mapper.insertSelective(kycTucPlan13);
            } else if (CommonDtoUtil.isUpdate(data)) {
                // BigInteger modifiedCnt = new BigInteger(data.getModifiedCnt());
                // if (CommonDtoUtil.isUpdate(data)) {
                // // 更新時の共通カラム値設定処理
                // CommonDaoUtil.setUpdateCommonColumns(kycTucPlan13, modifiedCnt);
                // } else {
                // // 削除（論理）時の共通カラム値設定処理
                // CommonDaoUtil.setDeleteCommonColumns(kycTucPlan13, modifiedCnt);
                // }
                final KycTucPlan13Criteria criteria = new KycTucPlan13Criteria();
                criteria.createCriteria()
                        .andPlan13IdEqualTo(CommonDtoUtil.strValToInt(data.getPlan13Id()));
                // .andModifiedCntEqualTo(modifiedCnt)
                // .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
                int count = kycTucPlan13Mapper.updateByCriteriaSelective(kycTucPlan13, criteria);
                if (count <= 0) {
                    throw new ExclusiveException();
                }
            } else if (CommonDtoUtil.isDelete(data)) {
                final KycTucPlan13Criteria criteria = new KycTucPlan13Criteria();
                criteria.createCriteria()
                        .andPlan13IdEqualTo(CommonDtoUtil.strValToInt(data.getPlan13Id()));
                kycTucPlan13Mapper.deleteByCriteria(criteria);
            }
        }

        if (inDto.getTab4DataList() != null) {
            // 「支援計画」の保存
            for (PreventionPlanTab4Data data : inDto.getTab4DataList()) {
                final KycTucPlan14 kycTucPlan14 = new KycTucPlan14();
                // 保存用の履歴ＩＤ ※「4.4.1.」を参照
                kycTucPlan14.setPlan11Id(CommonDtoUtil.strValToInt(rirekiId));
                // 保存用「支援計画」リスト.総合的課題番号
                kycTucPlan14.setSogoKadaiNo(CommonDtoUtil.strValToInt(data.getSogoKadaiNo()));
                // 保存用「支援計画」リスト.目標についての支援のポイント
                kycTucPlan14.setShienPointKnj(data.getShienPointKnj());
                // 保存用「支援計画」リスト.具体的な支援の内容（本人）
                kycTucPlan14.setInfoServiceKnj(data.getInfoServiceKnj());
                // 保存用「支援計画」リスト.具体的な支援の内容（保険・地域）
                kycTucPlan14.setHokenServiceKnj(data.getHokenServiceKnj());
                // 保存用「支援計画」リスト.サービス種別（本人）
                kycTucPlan14.setSvShubetuKnj(data.getSvShubetuKnj());
                // 保存用「支援計画」リスト.事業所（本人）
                kycTucPlan14.setSvJigyoKnj(data.getSvJigyoKnj());
                // 保存用「支援計画」リスト.期間（本人）
                kycTucPlan14.setKikanKnj(data.getKikanKnj());
                // 保存用「支援計画」リスト.表示順
                kycTucPlan14.setSeqNo(CommonDtoUtil.strValToInt(data.getSeqNo()));
                // 保存用「支援計画」リスト.具体的な支援の内容（家族）
                kycTucPlan14.setKazokuServiceKnj(data.getKazokuServiceKnj());
                // 保存用「支援計画」リスト.※1（本人）
                kycTucPlan14.setSvKbn(CommonDtoUtil.strValToInt(data.getSvKbn()));
                // 保存用「支援計画」リスト.※1（家族）
                kycTucPlan14.setSvKazokuKbn(CommonDtoUtil.strValToInt(data.getSvKazokuKbn()));
                // 保存用「支援計画」リスト.※1（保険・地域）
                kycTucPlan14.setSvHokenKbn(CommonDtoUtil.strValToInt(data.getSvHokenKbn()));
                // 保存用「支援計画」リスト.サービス種別（家族）
                kycTucPlan14.setSvShubetuKazokuKnj(data.getSvShubetuKazokuKnj());
                // 保存用「支援計画」リスト.サービス種別（保険・地域）
                kycTucPlan14.setSvShubetuHokenKnj(data.getSvShubetuHokenKnj());
                // 保存用「支援計画」リスト.事業所（家族）
                kycTucPlan14.setSvJigyoKazokuKnj(data.getSvJigyoKazokuKnj());
                // 保存用「支援計画」リスト.事業所（保険・地域）
                kycTucPlan14.setSvJigyoHokenKnj(data.getSvJigyoHokenKnj());
                // 保存用「支援計画」リスト.頻度（本人）
                kycTucPlan14.setHindoKnj(data.getHindoKnj());
                // 保存用「支援計画」リスト.頻度（家族）
                kycTucPlan14.setHindoKazokuKnj(data.getHindoKazokuKnj());
                // 保存用「支援計画」リスト.頻度（保険・地域）
                kycTucPlan14.setHindoHokenKnj(data.getHindoHokenKnj());
                // 保存用「支援計画」リスト.期間（家族）
                kycTucPlan14.setKikanKazokuKnj(data.getKikanKazokuKnj());
                // 保存用「支援計画」リスト.期間（保険・地域）
                kycTucPlan14.setKikanHokenKnj(data.getKikanHokenKnj());
                // 保存用「支援計画」リスト.期間開始日（本人）
                kycTucPlan14.setKikanSYmd(data.getKikanSYmd());
                // 保存用「支援計画」リスト.期間終了日（本人）
                kycTucPlan14.setKikanEYmd(data.getKikanEYmd());
                // 保存用「支援計画」リスト.期間開始日（家族）
                kycTucPlan14.setKikanKazokuSYmd(data.getKikanKazokuSYmd());
                // 保存用「支援計画」リスト.期間終了日（家族）
                kycTucPlan14.setKikanKazokuEYmd(data.getKikanKazokuEYmd());
                // 保存用「支援計画」リスト.期間開始日（保険・地域）
                kycTucPlan14.setKikanHokenSYmd(data.getKikanHokenSYmd());
                // 保存用「支援計画」リスト.期間終了日（保険・地域）
                kycTucPlan14.setKikanHokenEYmd(data.getKikanHokenEYmd());
                // 保存用「長期」リスト.更新区分がC:新規の場合、下記の新規登録処理を行う
                if (CommonDtoUtil.isCreate(data)) {
                    // 登録時の共通カラム値設定処理
                    // CommonDaoUtil.setInsertCommonColumns(kycTucPlan14);
                    // kycTucPlan14.setPlan14Id(numbering.getNumber(AppUtil.getKeiyakushaId(),
                    // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.KYC_TUC_PLAN14_PLAN14_ID.getIntValue()));
                    kycTucPlan14Mapper.insertSelective(kycTucPlan14);
                } else if (CommonDtoUtil.isUpdate(data)) {
                    // BigInteger modifiedCnt = new BigInteger(data.getModifiedCnt());
                    // if (CommonDtoUtil.isUpdate(data)) {
                    // // 更新時の共通カラム値設定処理
                    // CommonDaoUtil.setUpdateCommonColumns(kycTucPlan14, modifiedCnt);
                    // } else {
                    // // 削除（論理）時の共通カラム値設定処理
                    // CommonDaoUtil.setDeleteCommonColumns(kycTucPlan14, modifiedCnt);
                    // }
                    final KycTucPlan14Criteria criteria = new KycTucPlan14Criteria();
                    criteria.createCriteria()
                            .andPlan14IdEqualTo(CommonDtoUtil.strValToInt(data.getPlan14Id()));
                    // .andModifiedCntEqualTo(modifiedCnt)
                    // .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
                    int count = kycTucPlan14Mapper.updateByCriteriaSelective(kycTucPlan14, criteria);
                    if (count <= 0) {
                        throw new ExclusiveException();
                    }
                } else if (CommonDtoUtil.isDelete(data)) {
                    final KycTucPlan14Criteria criteria = new KycTucPlan14Criteria();
                    criteria.createCriteria()
                            .andPlan14IdEqualTo(CommonDtoUtil.strValToInt(data.getPlan14Id()));
                    kycTucPlan14Mapper.deleteByCriteria(criteria);
                }
            }
        }
        if (inDto.getHokenList() != null) {
            // 「保険サービス」の保存
            for (PreventionPlanHokenSvData data : inDto.getHokenList()) {
                final KycTucPlan15 kycTucPlan15 = new KycTucPlan15();
                // 保存用の履歴ＩＤ ※「4.4.1.」を参照
                kycTucPlan15.setPlan11Id(CommonDtoUtil.strValToInt(rirekiId));
                // 保存用「保険サービス」リスト.曜日
                kycTucPlan15.setYoubi(data.getYoubi());
                // 保存用「保険サービス」リスト.週単位以外のｻｰﾋﾞｽ区分
                kycTucPlan15.setIgaiKbn(CommonDtoUtil.strValToInt(data.getIgaiKbn()));
                // 保存用「保険サービス」リスト.週単位以外のｻｰﾋﾞｽ（日付指定）
                kycTucPlan15.setIgaiDate(data.getIgaiDate());
                // 保存用「保険サービス」リスト.週単位以外のｻｰﾋﾞｽ（曜日指定）
                kycTucPlan15.setIgaiWeek(data.getIgaiWeek());
                // 保存用「保険サービス」リスト.居宅開始時間
                kycTucPlan15.setKaishiJikan(data.getKaishiJikan());
                // 保存用「保険サービス」リスト.居宅終了時間
                kycTucPlan15.setShuuryouJikan(data.getShuuryouJikan());
                // 保存用「保険サービス」リスト.居宅サービス種類
                kycTucPlan15.setSvShuruiCd(data.getSvShuruiCd());
                // 保存用「保険サービス」リスト.居宅サービス項目（台帳）
                kycTucPlan15.setSvItemCd(CommonDtoUtil.strValToInt(data.getSvItemCd()));
                // 保存用「保険サービス」リスト.居宅サービス事業者CD
                kycTucPlan15.setSvJigyoId(CommonDtoUtil.strValToInt(data.getSvJigyoId()));
                // 保存用「保険サービス」リスト.居宅福祉用具貸与単位
                kycTucPlan15.setTanka(StringUtils.isEmpty(data.getTanka()) ? null : Double.valueOf(data.getTanka()));
                // 保存用「保険サービス」リスト.福祉用具貸与商品コード
                kycTucPlan15.setFygId(CommonDtoUtil.strValToInt(data.getFygId()));
                // 保存用「保険サービス」リスト.合成識別区分
                kycTucPlan15.setGouseiSikKbn(data.getGouseiSikKbn());
                // 保存用「保険サービス」リスト.加算フラグ
                kycTucPlan15.setKasanFlg(CommonDtoUtil.strValToInt(data.getKasanFlg()));
                // 保存用「保険サービス」リスト.親レコード番号
                kycTucPlan15.setOyaLineNo(CommonDtoUtil.strValToInt(data.getOyaLineNo()));
                // 保存用「長期」リスト.更新区分がC:新規の場合、下記の新規登録処理を行う
                if (CommonDtoUtil.isCreate(data)) {
                    // 登録時の共通カラム値設定処理
                    // CommonDaoUtil.setInsertCommonColumns(kycTucPlan15);
                    // kycTucPlan15.setPlan15Id(numbering.getNumber(AppUtil.getKeiyakushaId(),
                    // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.KYC_TUC_PLAN15_PLAN15_ID.getIntValue()));
                    kycTucPlan15Mapper.insertSelective(kycTucPlan15);
                } else if (CommonDtoUtil.isUpdate(data)) {
                    // BigInteger modifiedCnt = new BigInteger(data.getModifiedCnt());
                    // if (CommonDtoUtil.isUpdate(data)) {
                    // // 更新時の共通カラム値設定処理
                    // CommonDaoUtil.setUpdateCommonColumns(kycTucPlan15, modifiedCnt);
                    // } else {
                    // // 削除（論理）時の共通カラム値設定処理
                    // CommonDaoUtil.setDeleteCommonColumns(kycTucPlan15, modifiedCnt);
                    // }
                    final KycTucPlan15Criteria criteria = new KycTucPlan15Criteria();
                    criteria.createCriteria()
                            .andPlan15IdEqualTo(CommonDtoUtil.strValToInt(data.getPlan15Id()));
                    // .andModifiedCntEqualTo(modifiedCnt)
                    // .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
                    int count = kycTucPlan15Mapper.updateByCriteriaSelective(kycTucPlan15, criteria);
                    if (count <= 0) {
                        throw new ExclusiveException();
                    }
                } else if (CommonDtoUtil.isDelete(data)) {
                    final KycTucPlan15Criteria criteria = new KycTucPlan15Criteria();
                    criteria.createCriteria()
                            .andPlan15IdEqualTo(CommonDtoUtil.strValToInt(data.getPlan15Id()));
                    kycTucPlan15Mapper.deleteByCriteria(criteria);
                }
            }
        }
        if (inDto.getHokenYmdList() != null) {
            // 「保険サービス（年月日指定）」の保存
            for (PreventionPlanHokenYmdData data : inDto.getHokenYmdList()) {
                final KycTucPlan16 kycTucPlan16 = new KycTucPlan16();
                // 保存用の履歴ＩＤ ※「4.4.1.」を参照
                kycTucPlan16.setPlan11Id(CommonDtoUtil.strValToInt(rirekiId));
                // 保存用「保険サービス（年月日指定）」リスト.詳細ID
                kycTucPlan16.setPlan15Id(CommonDtoUtil.strValToInt(data.getPlan15Id()));
                // 保存用「保険サービス（年月日指定）」リスト.開始日
                kycTucPlan16.setStartYmd(data.getStartYmd());
                // 保存用「保険サービス（年月日指定）」リスト.終了日
                kycTucPlan16.setEndYmd(data.getEndYmd());
                // 保存用「長期」リスト.更新区分がC:新規の場合、下記の新規登録処理を行う
                if (CommonDtoUtil.isCreate(data)) {
                    // 登録時の共通カラム値設定処理
                    // CommonDaoUtil.setInsertCommonColumns(kycTucPlan16);
                    // kycTucPlan16.setPlan16Id(numbering.getNumber(AppUtil.getKeiyakushaId(),
                    // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.KYC_TUC_PLAN16_PLAN16_ID.getIntValue()));
                    kycTucPlan16Mapper.insertSelective(kycTucPlan16);
                } else if (CommonDtoUtil.isUpdate(data)) {
                    // BigInteger modifiedCnt = new BigInteger(data.getModifiedCnt());
                    // if (CommonDtoUtil.isUpdate(data)) {
                    // // 更新時の共通カラム値設定処理
                    // CommonDaoUtil.setUpdateCommonColumns(kycTucPlan16, modifiedCnt);
                    // } else {
                    // // 削除（論理）時の共通カラム値設定処理
                    // CommonDaoUtil.setDeleteCommonColumns(kycTucPlan16, modifiedCnt);
                    // }
                    final KycTucPlan16Criteria criteria = new KycTucPlan16Criteria();
                    criteria.createCriteria()
                            .andPlan16IdEqualTo(CommonDtoUtil.strValToInt(data.getPlan16Id()));
                    // .andModifiedCntEqualTo(modifiedCnt)
                    // .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
                    int count = kycTucPlan16Mapper.updateByCriteriaSelective(kycTucPlan16, criteria);
                    if (count <= 0) {
                        throw new ExclusiveException();
                    }
                } else if (CommonDtoUtil.isDelete(data)) {
                    final KycTucPlan16Criteria criteria = new KycTucPlan16Criteria();
                    criteria.createCriteria()
                            .andPlan16IdEqualTo(CommonDtoUtil.strValToInt(data.getPlan16Id()));
                    kycTucPlan16Mapper.deleteByCriteria(criteria);
                }
            }
        }
    }
}
