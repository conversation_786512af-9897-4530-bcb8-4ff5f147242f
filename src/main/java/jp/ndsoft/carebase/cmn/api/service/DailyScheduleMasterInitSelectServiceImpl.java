package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.service.dto.DailyScheduleMasterInitSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.DailyScheduleMasterInitSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;
import jp.ndsoft.carebase.cmn.api.logic.SvJigyoInfoLogic;

import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * GUI01056_日課計画マスタ
 * 日課計画マスタ初期情報取得
 *
 * <AUTHOR>
 */
@Service
public class DailyScheduleMasterInitSelectServiceImpl
        extends
        SelectServiceImpl<DailyScheduleMasterInitSelectServiceInDto, DailyScheduleMasterInitSelectServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    /** 分類1：2 */
    private static final Integer BUNRUI_1_ID = 2;
    /** 分類2：3 */
    private static final Integer BUNRUI_2_ID = 6;
    /** 初期取込：1 */
    private static final String INITIAL_UPTAKE_DEFAULT = "1";
    /** 時間：0 */
    private static final String TIME_DEFAULT = "0";
    /** 文字サイズ：2 */
    private static final String CHAR_SIZE_DEFAULT = "2";
    /** 文字位置：0 */
    private static final String CHAR_POSITION_DEFAULT = "0";
    /** 文字色：0 */
    private static final String CHAR_COLOR_DEFAULT = "0";
    /** 背景色：12632256 */
    private static final String BACK_GROUND_COLOR_DEFAULT = "12632256";

    /** 初期取込 */
    private static final Integer INITIAL_UPTAKE = 2;
    /** 時間 */
    private static final Integer TIME = 4;
    /** 文字サイズ */
    private static final Integer CHAR_SIZE = 3;
    /** 文字位置 */
    private static final Integer CHAR_POSITION = 8;
    /** 文字色 */
    private static final Integer CHAR_COLOR = 6;
    /** 背景色 */
    private static final Integer BACK_GROUND_COLOR = 7;
    /** 初期設定マスタ情報取得 */
    @Autowired
    private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;

    /** リクエストパラメータ.適用事業所IDリストの事業所名取得 */
    @Autowired
    private SvJigyoInfoLogic svJigyoInfoLogic;

    /**
     * 日課計画マスタ初期情報取得
     * 
     * @param inDto 日課計画マスタ初期情報取得の入力DTO.
     * @return 日課計画マスタ初期情報取得の出力DTO
     * @throws Exception Exception
     */
    @Override
    protected DailyScheduleMasterInitSelectServiceOutDto mainProcess(DailyScheduleMasterInitSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // 戻り値のDTOを初期化
        DailyScheduleMasterInitSelectServiceOutDto outDto = new DailyScheduleMasterInitSelectServiceOutDto();

        // 項目にを初期化する。
        // 初期取込
        outDto.setInitialUptake(INITIAL_UPTAKE_DEFAULT);
        // 時間
        outDto.setTime(TIME_DEFAULT);
        // 文字サイズ
        outDto.setCharSize(CHAR_SIZE_DEFAULT);
        // 文字位置
        outDto.setCharPosition(CHAR_POSITION_DEFAULT);
        // 文字色
        outDto.setCharColor(CHAR_COLOR_DEFAULT);
        // 背景色
        outDto.setBackgroundColor(BACK_GROUND_COLOR_DEFAULT);

        // 単項目チェック以外の入力チェック 特になし

        // 検索条件を設定
        KrkSsmInfoByCriteriaInEntity inEntity = new KrkSsmInfoByCriteriaInEntity();
        // 施設ID
        inEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        inEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 分類1
        inEntity.setBunrui1Id(BUNRUI_1_ID);
        // 分類2
        inEntity.setBunrui2Id(BUNRUI_2_ID);

        // 初期設定マスタ情報を取得する
        List<KrkSsmInfoOutEntity> outEntityList = kghMocKrkSsmSelectMapper.findKrkSsmInfoByCriteria(inEntity);
        if (!outEntityList.isEmpty()) {
            // 分類３は1が存在する場合、
            outEntityList.forEach(x -> {
                // 初期取込
                if (x.getBunrui3Id().equals(INITIAL_UPTAKE)) {
                    outDto.setInitialUptake(x.getIntValue().toString());
                    outDto.setInitialUptakeBunrui3(INITIAL_UPTAKE.toString());
                    // 時間
                } else if (x.getBunrui3Id().equals(TIME)) {
                    outDto.setTime(x.getIntValue().toString());
                    outDto.setTimeBunrui3(TIME.toString());
                } else if (x.getBunrui3Id().equals(CHAR_SIZE)) {
                    // 文字サイズ
                    outDto.setCharSize(x.getIntValue().toString());
                    outDto.setCharSizeBunrui3(CHAR_SIZE.toString());
                } else if (x.getBunrui3Id().equals(CHAR_POSITION)) {
                    // 文字位置
                    outDto.setCharPosition(x.getIntValue().toString());
                    outDto.setCharPositionBunrui3(CHAR_POSITION.toString());
                } else if (x.getBunrui3Id().equals(CHAR_COLOR)) {
                    // 文字色
                    outDto.setCharColor(x.getIntValue().toString());
                    outDto.setCharColorBunrui3(CHAR_COLOR.toString());
                } else if (x.getBunrui3Id().equals(BACK_GROUND_COLOR)) {
                    // 背景色
                    outDto.setBackgroundColor(x.getIntValue().toString());
                    outDto.setBackgroundColorBunrui3(BACK_GROUND_COLOR.toString());
                }
            });
        }

        // リクエストパラメータ.適用事業所IDリストの件数により、以下の処理を繰り返す。
        outDto.setSvJigyoListInfo(svJigyoInfoLogic.getSvJigyoInfoList(inDto.getSvJigyoIdList()));

        LOG.info(Constants.END);
        return outDto;
    }
}
