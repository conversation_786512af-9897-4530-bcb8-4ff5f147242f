package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * @since 2025.04.23
 * <AUTHOR> DAO VAN DUONG
 * @implNote GUI00835_記号と意味 画面 DTO.
 */
@Getter
@Setter
public class KigoImiUpdateServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;
    /** ケアチェックID */
    @NotEmpty
    private String cc1Id;
    /** 計画期間ID */
    @NotEmpty
    private String sc1Id;
    /** 法人ID */
    @NotEmpty
    private String houjinId;
    /** 施設ID */
    @NotEmpty
    private String shisetuId;
    /** 事業者ID */
    @NotEmpty
    private String svJigyoId;
    /** 利用者ID */
    @NotEmpty
    private String userId;
    /** 職員ID */
    private String shokuId;
    /** 記号と意味1 */
    private String koumoku1Knj;
    /** 記号と意味2 */
    private String koumoku2Knj;
    /** 記号と意味3 */
    private String koumoku3Knj;
    /** 記号と意味4 */
    private String koumoku4Knj;
    /** 記号と意味5 */
    private String koumoku5Knj;
    /** 更新区分 */
    private String updateKbn;
    /** 更新回数 */
    private String modifiedCnt;

}
