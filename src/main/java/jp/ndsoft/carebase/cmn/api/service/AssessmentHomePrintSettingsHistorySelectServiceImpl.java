package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00815PrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00815User;
import jp.ndsoft.carebase.cmn.api.logic.PrintSettingLogic;
import jp.ndsoft.carebase.cmn.api.logic.dto.PrintSettingInfoDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.SysIniInfoDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomePrintSettingsHistorySelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomePrintSettingsHistorySelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.FreeAssessmentFacePrintSettingsUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KikanCpnTucGdlRirekiInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KikanCpnTucGdlRirekiInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KikanCpnTucGdlRirekiSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * @since 2025.07.18
 * <AUTHOR>
 * @implNote GUI00815_印刷設定 複数利用者履歴情報取得
 */
@Service
public class AssessmentHomePrintSettingsHistorySelectServiceImpl extends
        UpdateServiceImpl<AssessmentHomePrintSettingsHistorySelectServiceInDto, AssessmentHomePrintSettingsHistorySelectServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 印刷設定共通クラス */
    @Autowired
    private PrintSettingLogic printSettingLogic;

    /** 計画期間の居宅アセスメント履歴取得 */
    @Autowired
    private KikanCpnTucGdlRirekiSelectMapper kikanCpnTucGdlRirekiSelectMapper;

    /**
     * 初期情報取得
     * 
     * @param inDto 初期情報取得サービス入力Dto
     * @return 初期情報取得サービス出力Dto
     * @throws Exception Exception
     */
    @Override
    protected AssessmentHomePrintSettingsHistorySelectServiceOutDto mainProcess(
            AssessmentHomePrintSettingsHistorySelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        AssessmentHomePrintSettingsHistorySelectServiceOutDto outDto = new AssessmentHomePrintSettingsHistorySelectServiceOutDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        List<Gui00815PrintSubjectHistory> printSubjectHistoryList = new ArrayList<>();
        /*
         * ===============2. 印刷対象履歴一覧を取得する。===============
         * 
         */
        List<KikanCpnTucGdlRirekiInfoOutEntity> kikanCpnTucGdlRirekiInfoList=new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inDto.getUserList())) {
            // (1).リクエストパラメータ.利用者リスト件数分、繰り返し、計画期間の居宅アセスメント履歴一覧を取得する
            for (Gui00815User user : inDto.getUserList()) {
                // ・下記の履歴＿調査記録情報取得のDAOを利用し、計画期間の居宅アセスメント履歴一覧を取得する。
                KikanCpnTucGdlRirekiInfoByCriteriaInEntity kikanCpnTucGdlRirekiInfoByCriteriaInEntity = new KikanCpnTucGdlRirekiInfoByCriteriaInEntity();
                // 事業者ID
                kikanCpnTucGdlRirekiInfoByCriteriaInEntity
                        .setSvJigyo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                // 利用者ID
                kikanCpnTucGdlRirekiInfoByCriteriaInEntity.setUser(CommonDtoUtil.strValToInt(user.getUserId()));

                kikanCpnTucGdlRirekiInfoList = this.kikanCpnTucGdlRirekiSelectMapper
                        .findKikanCpnTucGdlRirekiInfoByCriteria(kikanCpnTucGdlRirekiInfoByCriteriaInEntity);

                Gui00815PrintSubjectHistory periodHistoryDto = new Gui00815PrintSubjectHistory();

                // ・履歴取得ありの場合、
                if (CollectionUtils.isNotEmpty(kikanCpnTucGdlRirekiInfoList)) {
                    // ①上記のOUTPUT情報を（アセスメント実施日 D 降序, アセスメントID 降序, ）ソート処理
                    // ソートキー："検討日 降序, アセスメントID 降序"
                    kikanCpnTucGdlRirekiInfoList.sort(Comparator
                            .comparing(
                                    KikanCpnTucGdlRirekiInfoOutEntity::getAsJisshiDateYmd,
                                    Comparator.nullsLast(Comparator.reverseOrder()))
                            .thenComparing(
                                    KikanCpnTucGdlRirekiInfoOutEntity::getGdlId,
                                    Comparator.nullsLast(Comparator.reverseOrder())));

                    // ②ソート結果からアセスメント実施日をリクエストパラメータ.基準日の以前のレコードの1件目を取得する
                    List<KikanCpnTucGdlRirekiInfoOutEntity> filteredKikanCpnTucGdlRirekiInfoList = kikanCpnTucGdlRirekiInfoList
                            .stream()
                            .filter(kikanCpnTucGdlRirekiInfo -> StringUtil
                                    .isNotEmpty(kikanCpnTucGdlRirekiInfo.getAsJisshiDateYmd())
                                    && kikanCpnTucGdlRirekiInfo.getAsJisshiDateYmd()
                                            .compareTo(inDto.getKijunbiYmd()) <= 0)
                            .collect(Collectors.toList());

                    // ②ソート結果からアセスメント実施日をリクエストパラメータ.基準日の以前のレコードの1件目を取得する
                    if (CollectionUtils.isNotEmpty(filteredKikanCpnTucGdlRirekiInfoList)) {
                        // 印刷対象履歴を設定する。
                        periodHistoryDto = this.setPrintSubjectHistory(
                                filteredKikanCpnTucGdlRirekiInfoList.get(0), user);
                    } else {
                        // ③上記ソート結果なしの場合、
                        // 利用者ID
                        periodHistoryDto.setUserId(user.getUserId());
                        // 利用者名
                        periodHistoryDto.setUserName(user.getUserName());

                        periodHistoryDto.setResult(CommonConstants.ASS_PRT_RRK_NONE_MSG);

                    }

                } else {
                    // ・履歴取得なしの場合、
                    // 利用者ID
                    periodHistoryDto.setUserId(user.getUserId());
                    // 利用者名
                    periodHistoryDto.setUserName(user.getUserName());

                    periodHistoryDto.setResult(CommonConstants.ASS_PRT_RRK_NONE_MSG);

                }
                printSubjectHistoryList.add(periodHistoryDto);

            }

        }

        /*
         * ===============４.印刷設定初期値情報の取得===============
         * 
         */
        String sectionName = CommonConstants.EMPTY_STRING;
        if (CollectionUtils.isNotEmpty(kikanCpnTucGdlRirekiInfoList)
                && StringUtil.isNotEmpty(kikanCpnTucGdlRirekiInfoList.get(0).getAsJisshiDateYmd())
                && kikanCpnTucGdlRirekiInfoList.get(0).getAsJisshiDateYmd()
                        .compareTo(CommonConstants.CREATION_DATE_20180401) >= 0) {
            sectionName = CommonConstants.ASSESSMENT_H30_4;
        } else {
            sectionName = CommonConstants.ASSESSMENT_H21_4;
        }

        List<PrintSettingInfoDto> saveBeforePrtList = new ArrayList<PrintSettingInfoDto>();

        // 共通「API定義書_APINo(959)_初期情報取得.xlsx」の「２～3」を参照
        // API定義書_APINo(959)_初期情報取得.xlsx」の「2.」
        // 2.印刷設定情報リストを取得する。
        Pair<Boolean, List<PrintSettingInfoDto>> resultPrtList = this.printSettingLogic.getInitPrtList(
                inDto.getSysRyaku(),
                sectionName,
                inDto.getShokuId(),
                inDto.getHoujinId(),
                inDto.getShisetuId(),
                inDto.getSvJigyoId());

        if (resultPrtList.getLeft() == true) {
            saveBeforePrtList = resultPrtList.getRight();

            // 印刷設定情報リスト
            outDto.setPrtList(saveBeforePrtList);

            return outDto;

        } else {
            saveBeforePrtList = resultPrtList.getRight();
        }

        // API定義書_APINo(959)_初期情報取得.xlsx」の「3.」
        // 3.印刷設定保存情報を保存する
        FreeAssessmentFacePrintSettingsUpdateServiceOutDto freeAssessmentFacePrintSettingsUpdateServiceOutDto = this.printSettingLogic
                .insertPrtList(
                        saveBeforePrtList,
                        inDto.getSysRyaku(),
                        sectionName,
                        inDto.getShokuId(),
                        inDto.getHoujinId(),
                        inDto.getShisetuId(),
                        inDto.getSvJigyoId(),
                        inDto.getIndex(),
                        inDto.getGsysCd(),
                        inDto.getKojinhogoUsedFlg(),
                        inDto.getSectionAddNo());

        List<PrintSettingInfoDto> printSettingInfoList = freeAssessmentFacePrintSettingsUpdateServiceOutDto
                .getPrtList();

        SysIniInfoDto sysIniInfo = freeAssessmentFacePrintSettingsUpdateServiceOutDto.getSysIniInfo();

        /*
         * ===============5. レスポンスを返却する。===============
         * 
         */
        // 印刷設定情報リスト
        outDto.setPrtList(printSettingInfoList);
        // システムINI情報
        outDto.setSysIniInfo(sysIniInfo);
        // アセスメント履歴リスト
        outDto.setPrintSubjectHistoryList(printSubjectHistoryList);

        LOG.info(Constants.END);

        return outDto;
    }

    /**
     * 印刷対象履歴を設定する。
     * 
     * @param kikanCpnTucGdlRirekiInfo アセスメント履歴
     * @param user                     利用者
     * 
     * @return 印刷対象履歴
     */
    private Gui00815PrintSubjectHistory setPrintSubjectHistory(
            KikanCpnTucGdlRirekiInfoOutEntity kikanCpnTucGdlRirekiInfo, Gui00815User user) {
        Gui00815PrintSubjectHistory periodHistoryDto = new Gui00815PrintSubjectHistory();
        // 利用者名
        periodHistoryDto.setUserName(user.getUserName());
        // 利用者ID
        periodHistoryDto.setUserId(CommonDtoUtil.objValToString(kikanCpnTucGdlRirekiInfo.getUserId()));
        // 期間ID */
        periodHistoryDto.setSc1Id(CommonDtoUtil.objValToString(kikanCpnTucGdlRirekiInfo.getSc1Id()));
        // 開始日 */
        periodHistoryDto.setStartYmd(kikanCpnTucGdlRirekiInfo.getStartYmd());
        // 終了日 */
        periodHistoryDto.setEndYmd(kikanCpnTucGdlRirekiInfo.getEndYmd());
        // アセスメントID */
        periodHistoryDto.setGdlId(CommonDtoUtil.objValToString(kikanCpnTucGdlRirekiInfo.getGdlId()));
        // アセスメント実施日 */
        periodHistoryDto.setAsJisshiDateYmd(kikanCpnTucGdlRirekiInfo.getAsJisshiDateYmd());
        // 記載者ID */
        periodHistoryDto.setShokuId(CommonDtoUtil.objValToString(kikanCpnTucGdlRirekiInfo.getShokuId()));
        // 改定フラグ */
        periodHistoryDto.setNinteiFormF(CommonDtoUtil.objValToString(kikanCpnTucGdlRirekiInfo.getNinteiFormF()));

        return periodHistoryDto;

    }

    /**
     * インターライ方式履歴を設定する。
     * 
     * @param visioFlg                Visioフラグ
     * @param printSubjectHistoryList 履歴リスト
     * 
     * @return アセスメント履歴
     */
    private String getSectionName(List<Gui00815PrintSubjectHistory> printSubjectHistoryList) {
        String sectionName = null;

        // ・変数.調査票改訂フラグが4(H21/4改訂版)の場合、
        // ・変数.セクション名＝”居宅アセスメント表（H21/4改訂）”
        sectionName = CommonConstants.ASSESSMENT_H21_4;
        // 2.4で取得した アセスメント履歴一覧リストのサイズが0件以上の場合、
        if (CollectionUtils.isNotEmpty(printSubjectHistoryList)) {
            // 2.4で取得した アセスメント履歴一覧リストの1件目.アセスメント実施日>="2018/04/01"
            if (StringUtil.isNotEmpty(printSubjectHistoryList.get(0).getAsJisshiDateYmd())
                    && printSubjectHistoryList.get(0).getAsJisshiDateYmd()
                            .compareTo(CommonConstants.CREATION_DATE_20180401) >= 0) {
                // ・変数.セクション名＝”居宅アセスメント表（H30/4改訂）”
                sectionName = CommonConstants.ASSESSMENT_H30_4;

            }
        }

        return sectionName;

    }
}
