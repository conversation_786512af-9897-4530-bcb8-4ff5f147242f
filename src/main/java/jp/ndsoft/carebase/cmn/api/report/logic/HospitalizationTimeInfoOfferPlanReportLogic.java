package jp.ndsoft.carebase.cmn.api.report.logic;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import io.micrometer.common.util.StringUtils;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.HospitalizationTimeOfferPlanPrintOption;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.HospitalizationTimeOfferPlanSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonDateParts;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonShiTeiDateParts;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.NdsMidaLogic;
import jp.ndsoft.carebase.cmn.api.logic.dto.AgeYmOutDto;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoJigyoKnj2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMucZokugaraSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserAddressSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHospInfoTeikyouHeadRelationSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.NyuuinJouhouTeikyoushoMeisaiSelectMapper;
import jp.ndsoft.carebase.cmn.api.report.dto.HospitalizationInfoOffer2ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.HospitalizationTimeInfoOfferPlanServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.HospitalizationTimeInfoOfferPlanServiceInDto;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMucZokugaraInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMucZokugaraInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucUserAddressByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucUserAddressOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnNyuTeikyouBackKakuteiPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnNyuTeikyouBackKakuteiPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnNyuTeikyouFrontKakuteiPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnNyuTeikyouFrontKakuteiPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghStringComMscSvjigyoJigyoKnj2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghStringComMscSvjigyoJigyoKnj2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinInfoListSpaceSortByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinInfoListSpaceSortOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.UserinfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.UserinfoOutEntity;
import jp.ndsoft.carebase.cmn.api.report.model.HospitalizationTimeOfferAllReportParameterModel;

/**
 * @since 2025.07.14
 * <AUTHOR>
 * @description 入院時情報提供書 帳票出力 ロジッククラス
 */
@Component
public class HospitalizationTimeInfoOfferPlanReportLogic {
    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;
    /** NdsMidaLogicロジッククラス */
     @Autowired
    private NdsMidaLogic ndsMidaLogic;
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    /** 入院時情報提供書明細情報取得 */
    @Autowired
    private NyuuinJouhouTeikyoushoMeisaiSelectMapper nyuuinJouhouTeikyoushoMeisaiSelectMapper;

    /** 続柄マスタ情報取得 */
    @Autowired
    private ComMucZokugaraSelectMapper comMucZokugaraSelectMapper;

    /** 職員基本情報取得 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;
    /** 1To15分割情報取得 */
    @Autowired
    private ComMscSvjigyoJigyoKnj2SelectMapper comMscSvjigyoJigyoKnj2SelectMapper;
    /** 利用者の情報取得 */
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;
    /** 01-23 利用者住所履歴情報取得 */
    @Autowired
    private ComTucUserAddressSelectMapper comTucUserAddressSelectMapper;
    /** 入院時情報提供ヘッダ関連情報取得 */
    @Autowired
    private CpnTucHospInfoTeikyouHeadRelationSelectMapper cpnTucHospInfoTeikyouHeadRelationSelectMapper;

    /**
     * U06091_入院時情報提供書①帳票パラメータ取得
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    public HospitalizationTimeInfoOfferPlanServiceInDto getHospitalizationInfoOffer1ReportParameters(
            HospitalizationTimeOfferAllReportParameterModel model,
            HospitalizationTimeInfoOfferPlanServiceOutDto outDto) throws Exception {

        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷オプション
        HospitalizationTimeOfferPlanPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.記入用シートを印刷するフラグ
        Boolean isEmptyFlg = false;
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())) {
            isEmptyFlg = true;
        }
        // 帳票用データ詳細
        HospitalizationTimeInfoOfferPlanServiceInDto infoInDto = new HospitalizationTimeInfoOfferPlanServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、入院時情報提供書明細情報の取得。===============
         * 
         */
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (isEmptyFlg) {
            infoInDto = this.getHospitalizationOfferPlanReportParams();
        } // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
        else {
            infoInDto = this.getHospitalizationOfferPlanReportParamsFrom1(printSet, model, printOption);
        }
        /** 指定日印刷区分 */
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // リクエストパラメータ.データ.印刷オプション.敬称変更フラグ＝1の場合
        if (CommonConstants.STR_1.equalsIgnoreCase(printOption.getKeishoFlg())) {
            infoInDto.setStKeisho(CommonDtoUtil.strNullToEmpty(printOption.getKeisho())); // 敬称
        }
        // リクエストパラメータ.データ.印刷オプション.敬称変更フラグが1以外 かつ
        // リクエストパラメータ.データ.初期設定マスタの情報の敬称オプション＝1 の場合
        else if (CommonConstants.STR_1.equalsIgnoreCase(model.getStKeishoFlg())) {
            infoInDto.setStKeisho(model.getStKeisho());// 敬称
        }
        // 上記以外の場合
        else {
            infoInDto.setStKeisho(CommonConstants.STR_LIKE); // 敬称
        }
        // 別紙に印刷する=リクエストパラメータ.データ.印刷オプション.別紙に印刷するフラグ
        infoInDto.setOtherPagePrtFlg(CommonDtoUtil.strValToInt(printOption.getBesshiFlg()));
        // 別紙のタイトル="入院時情報提供書①"
        infoInDto.setStTitle(ReportConstants.HOSPITALIZATION_TIME_INFO_OFFER_PLAN_TITLE);
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(isEmptyFlg);

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06091_入院時情報提供書①帳票パラメータ取得
     * 
     * @param inDto 入力データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private HospitalizationTimeInfoOfferPlanServiceInDto getHospitalizationOfferPlanReportParams() throws Exception {

        HospitalizationTimeInfoOfferPlanServiceInDto infoInDto = new HospitalizationTimeInfoOfferPlanServiceInDto();
        // 指定日（年号）=""
        infoInDto.setShiTeiDateGG(CommonConstants.BLANK_STRING);
        // 指定日（年）=""
        infoInDto.setShiTeiDateYY(CommonConstants.BLANK_STRING);
        // 指定日（月）=""
        infoInDto.setShiTeiDateMM(CommonConstants.BLANK_STRING);
        // 指定日（日）=""
        infoInDto.setShiTeiDateDD(CommonConstants.BLANK_STRING);
        // 記入日（年号）=""
        infoInDto.setCreateDateGG(CommonConstants.BLANK_STRING);
        // 記入日（年）=""
        infoInDto.setCreateDateYY(CommonConstants.BLANK_STRING);
        // 記入日（月）=""
        infoInDto.setCreateDateMM(CommonConstants.BLANK_STRING);
        // 記入日（日）=""
        infoInDto.setCreateDateDD(CommonConstants.BLANK_STRING);
        // 入院日（年号）=""
        infoInDto.setSickDateGG(CommonConstants.BLANK_STRING);
        // 入院日（年）=""
        infoInDto.setSickDateYY(CommonConstants.BLANK_STRING);
        // 入院日（月）=""
        infoInDto.setSickDateMM(CommonConstants.BLANK_STRING);
        // 入院日（日）=""
        infoInDto.setSickDateDD(CommonConstants.BLANK_STRING);
        // 情報提供日（年号）=""
        infoInDto.setTeikyouDateGG(CommonConstants.BLANK_STRING);
        // 情報提供日（年）=""
        infoInDto.setTeikyouDateYY(CommonConstants.BLANK_STRING);
        // 情報提供日（月）=""
        infoInDto.setTeikyouDateMM(CommonConstants.BLANK_STRING);
        // 情報提供日（日）=""
        infoInDto.setTeikyouDateDD(CommonConstants.BLANK_STRING);
        // 医療機関名=""
        infoInDto.setHospKnj(CommonConstants.BLANK_STRING);
        // 医療機関名フォント="9"
        infoInDto.setHospFont(ReportConstants.FONT_SIZE_9);
        // 事業所名=""
        infoInDto.setKyotakuknj(CommonConstants.BLANK_STRING);
        // 事業所名フォント=""
        infoInDto.setKyotakuFont(ReportConstants.FONT_SIZE_9);
        // ご担当者名=""
        infoInDto.setTantoknj(CommonConstants.BLANK_STRING);

        // ケアマネジャー氏名=""
        infoInDto.setTantoCmnKnj(CommonConstants.BLANK_STRING);
        // ケアマネジャー氏名フォント=""
        infoInDto.setTantoCmnFont(ReportConstants.FONT_SIZE_9);
        // TEL=""
        infoInDto.setTel(CommonConstants.BLANK_STRING);
        // FAX=""
        infoInDto.setFax(CommonConstants.BLANK_STRING);
        // フリガナ（姓）=""
        infoInDto.setUserKana1(CommonConstants.BLANK_STRING);
        // フリガナ（名）=""
        infoInDto.setUserKana2(CommonConstants.BLANK_STRING);
        // フリガナフォント=""
        infoInDto.setUserKanaFont(ReportConstants.FONT_SIZE_10);
        // 氏名（姓）=""
        infoInDto.setUserName1(CommonConstants.BLANK_STRING);
        // 氏名（名）=""
        infoInDto.setUserName2(CommonConstants.BLANK_STRING);
        // 氏名フォント=""
        infoInDto.setUserNameFont(ReportConstants.FONT_SIZE_12);
        // 患者年齢=""
        infoInDto.setAge(CommonConstants.BLANK_STRING);
        // 患者性別=""
        infoInDto.setUserSex(CommonConstants.BLANK_STRING);
        // 患者元年区分=""
        infoInDto.setBirthdayNenKbn(CommonConstants.BLANK_STRING);
        // 患者生年月日（年）=""
        infoInDto.setBirthdayYY(CommonConstants.BLANK_STRING);
        // 患者生年月日（月）=""
        infoInDto.setBirthdayMM(CommonConstants.BLANK_STRING);
        // 患者生年月日（日）=""
        infoInDto.setBirthdayDD(CommonConstants.BLANK_STRING);
        // 患者郵便番号=""
        infoInDto.setUserZip(CommonConstants.BLANK_STRING);
        // 患者住所=""
        infoInDto.setUserAddress(CommonConstants.BLANK_STRING);
        // 患者住所フォント=""
        infoInDto.setUserAddressFont(ReportConstants.FONT_SIZE_10);
        // 患者電話番号=""
        infoInDto.setUserTel(CommonConstants.BLANK_STRING);
        // 住環境=""
        infoInDto.setHouseKbn(CommonConstants.BLANK_STRING);
        // 住居階層=""
        infoInDto.setHouseFloor(CommonConstants.BLANK_STRING);
        // 居室階=""
        infoInDto.setHouseRoomFloor(CommonConstants.BLANK_STRING);
        // エレベーター有無=""
        infoInDto.setElevatorUmu(CommonConstants.BLANK_STRING);
        // 住まいに関する特記事項=""
        infoInDto.setHouseTokkiKnj(CommonConstants.BLANK_STRING);
        // 入院時の要介護度（確定版）=""
        infoInDto.setNyuuinYokaiKbnKakutei(CommonConstants.NUMBER_0);
        // 要支援=""
        infoInDto.setHospYoshien(CommonConstants.BLANK_STRING);
        // 要介護=""
        infoInDto.setHospYokaigo(CommonConstants.BLANK_STRING);
        // 認定有効開始日（年号）=""
        infoInDto.setNinteiStartDateGG(CommonConstants.BLANK_STRING);
        // 認定有効開始日（年）=""
        infoInDto.setNinteiStartDateYY(CommonConstants.BLANK_STRING);
        // 認定有効開始日（月）=""
        infoInDto.setNinteiStartDateMM(CommonConstants.BLANK_STRING);
        // 認定有効開始日（日）=""
        infoInDto.setNinteiStartDateDD(CommonConstants.BLANK_STRING);
        // 認定終了日（年号）=""
        infoInDto.setNinteiEndDateGG(CommonConstants.BLANK_STRING);
        // 認定終了日（年）=""
        infoInDto.setNinteiEndDateYY(CommonConstants.BLANK_STRING);
        // 認定終了日（月）=""
        infoInDto.setNinteiEndDateMM(CommonConstants.BLANK_STRING);
        // 認定終了日（日）=""
        infoInDto.setNinteiEndDateDD(CommonConstants.BLANK_STRING);
        // 申請中フラグ=""
        infoInDto.setNinteiShinseiFlg(CommonConstants.NUMBER_0);
        // 申請中申請日（年号）=""
        infoInDto.setNinteiShinseiDateGG(CommonConstants.BLANK_STRING);
        // 申請中申請日（年）=""
        infoInDto.setNinteiShinseiDateYY(CommonConstants.BLANK_STRING);
        // 申請中申請日（月）=""
        infoInDto.setNinteiShinseiDateMM(CommonConstants.BLANK_STRING);
        // 申請中申請日（日）=""
        infoInDto.setNinteiShinseiDateDD(CommonConstants.BLANK_STRING);
        // 区分変更=""
        infoInDto.setNinteiKbnHenkou(CommonConstants.NUMBER_0);
        // 区分変更申請日（年号）=""
        infoInDto.setKbnHenkouDateGG(CommonConstants.BLANK_STRING);
        // 区分変更申請日（年）=""
        infoInDto.setKbnHenkouDateYY(CommonConstants.BLANK_STRING);
        // 区分変更申請日（月）=""
        infoInDto.setKbnHenkouDateMM(CommonConstants.BLANK_STRING);
        // 区分変更申請日（日）=""
        infoInDto.setKbnHenkouDateDD(CommonConstants.BLANK_STRING);
        // 未申請フラグ=""
        infoInDto.setNinteiMishinseifFlg(CommonConstants.NUMBER_0);
        // 障害高齢者の日常生活自立度=""
        infoInDto.setShogaiJiritsuCd(CommonConstants.NUMBER_0);
        // 認知症高齢者の日常生活自立度=""
        infoInDto.setNinchiJiritsuCd(CommonConstants.NUMBER_0);
        // 医師の判断=""
        infoInDto.setIshiHandanFlg(CommonConstants.NUMBER_0);
        // ケアマネジャーの判断=""
        infoInDto.setCareHandanFlg(CommonConstants.NUMBER_0);
        // 介護保険の自己負担割合=""
        infoInDto.setFutanWariFlg(CommonConstants.NUMBER_0);
        // 自己負担割合テキスト=""
        infoInDto.setFutanWariai(CommonConstants.BLANK_STRING);
        // 障害など認定の有無=""
        infoInDto.setShogaiNintei(CommonConstants.NUMBER_0);
        // 障害認定（身体）=""
        infoInDto.setShintaiShogaiKbn(CommonConstants.NUMBER_0);
        // 障害認定（精神）=""
        infoInDto.setSeishinShogaiKbn(CommonConstants.NUMBER_0);
        // 障害認定（知的）=""
        infoInDto.setChitekiShogaiKbn(CommonConstants.NUMBER_0);
        // 年金種類（国民年金）=""
        infoInDto.setNenkin1Umu(CommonConstants.NUMBER_0);
        // 年金種類（厚生年金）=""
        infoInDto.setNenkin2Umu(CommonConstants.NUMBER_0);
        // 年金種類（障害年金）=""
        infoInDto.setNenkin3Umu(CommonConstants.NUMBER_0);
        // 年金種類（生活保護）=""
        infoInDto.setNenkin4Umu(CommonConstants.NUMBER_0);
        // 年金種類（その他）=""
        infoInDto.setNenkin5Umu(CommonConstants.NUMBER_0);
        // 年金などの種類その他メモ=""
        infoInDto.setNenkinMemoKnj(CommonConstants.BLANK_STRING);
        // 世帯構成（独居）=""
        infoInDto.setSetaiKousei1(CommonConstants.NUMBER_0);
        // 世帯構成（高齢者世帯）=""
        infoInDto.setSetaiKousei2(CommonConstants.NUMBER_0);
        // 世帯構成（子と同居）=""
        infoInDto.setSetaiKousei3(CommonConstants.NUMBER_0);
        // 世帯構成（その他）=""
        infoInDto.setSetaiKousei4(CommonConstants.NUMBER_0);
        // 世帯構成（日中独居）=""
        infoInDto.setSetaiKousei5(CommonConstants.NUMBER_0);
        // 世帯構成（その他）メモ=""
        infoInDto.setSetaiKouseiMemoKnj(CommonConstants.BLANK_STRING);
        // 主介護者氏名=""
        infoInDto.setKaigoMainKakutei(CommonConstants.BLANK_STRING);
        // 主介護者続柄=""
        infoInDto.setKaigoMainZcode(CommonConstants.BLANK_STRING);
        // 主介護者年齢=""
        infoInDto.setKaigoMainAge(CommonConstants.BLANK_STRING);
        // 同居・別居=""
        infoInDto.setKaigoMainKousei(CommonConstants.NUMBER_0);
        // 主介護者TEL=""
        infoInDto.setKaigoMainTel(CommonConstants.BLANK_STRING);
        // キーパーソン氏名=""
        infoInDto.setKeyPerson(CommonConstants.BLANK_STRING);
        // キーパーソン続柄=""
        infoInDto.setKeyPersonZcode(CommonConstants.BLANK_STRING);
        // キーパーソン年齢=""
        infoInDto.setKeyPersonAge(CommonConstants.BLANK_STRING);
        // キーパーソン連絡先TEL=""
        infoInDto.setKeyPersonRenrakuTel(CommonConstants.BLANK_STRING);
        // キーパーソンTEL=""
        infoInDto.setKeyPersonTel(CommonConstants.BLANK_STRING);
        // 本人の性格/趣味・関心領域など
        infoInDto.setUserInfo(CommonConstants.BLANK_STRING);
        // 別紙印刷1フラグ=""
        infoInDto.setBesshi1Flg(CommonConstants.NUMBER_0);
        // 本人の生活歴=""
        infoInDto.setUserSeikatureki(CommonConstants.BLANK_STRING);
        // 別紙印刷2フラグ=""
        infoInDto.setBesshi2Flg(CommonConstants.NUMBER_0);
        // 入院前の本人の意向=""
        infoInDto.setUserIkouKnj(CommonConstants.BLANK_STRING);
        // 別紙印刷3フラグ=""
        infoInDto.setBesshi3Flg(CommonConstants.NUMBER_0);
        // 入院前の家族の意向=""
        infoInDto.setKazokuIkouKnj(CommonConstants.BLANK_STRING);
        // 別紙印刷4フラグ=""
        infoInDto.setBesshi4Flg(CommonConstants.NUMBER_0);
        // 本人同封の居宅サービス計画（１）参照=""
        infoInDto.setUserIkouCksFlg(CommonConstants.NUMBER_0);
        // 家族同封の居宅サービス計画（１）参照=""
        infoInDto.setKazokuIkouCksFlg(CommonConstants.NUMBER_0);
        // 入院前の介護サービスの利用状況（居宅サービス計画書123表）=""
        infoInDto.setServiceRiyoKbn1(CommonConstants.NUMBER_0);
        // 入院前の介護サービスの利用状況（その他）=""
        infoInDto.setServiceRiyoKbn2(CommonConstants.NUMBER_0);
        // 入院前の介護サービスの利用状況（その他メモ）=""
        infoInDto.setServiceRiyoMemoKnj(CommonConstants.BLANK_STRING);
        // 入院前の介護サービスの利用状況（その他メモ）フォント=""
        infoInDto.setServiceRiyoMemoFont(ReportConstants.FONT_SIZE_10);
        // 在宅生活に必要な要件=""
        infoInDto.setZaitakuYokenKnj(CommonConstants.BLANK_STRING);
        // 別紙印刷5フラグ=""
        infoInDto.setBesshi5Flg(CommonConstants.NUMBER_0);
        // 退院後の世帯状況(独居)=""
        infoInDto.setKazokuKaigo1Umu(CommonConstants.NUMBER_0);
        // 退院後の世帯状況(高齢世帯)=""
        infoInDto.setKazokuKaigo3Umu(CommonConstants.NUMBER_0);
        // 退院後の世帯状況(子と同居)=""
        infoInDto.setKazokuKaigo7Umu(CommonConstants.NUMBER_0);
        // 退院後の世帯状況(日中独居)=""
        infoInDto.setKazokuKaigo2Umu(CommonConstants.NUMBER_0);
        // 退院後の世帯状況(その他)=""
        infoInDto.setKazokuKaigo6Umu(CommonConstants.NUMBER_0);
        // 家族構成員数=""
        infoInDto.setKazokuKaigo7Ninzu(CommonConstants.BLANK_STRING);
        // 退院後の世帯状況（その他）メモ=""
        infoInDto.setKazokuKaigoMemoKnj(CommonConstants.BLANK_STRING);
        // 世帯に対する配慮=""
        infoInDto.setSetaiHairyo(CommonConstants.NUMBER_0);
        // 世帯に対する配慮メモ=""
        infoInDto.setSetaiHairyoMemoKnj(CommonConstants.BLANK_STRING);
        // 退院後の主介護者=""
        infoInDto.setTaiingoKaigoMain(CommonConstants.NUMBER_0);
        // 退院後の主介護者氏名=""
        infoInDto.setTaiingoKaigoMainName(CommonConstants.BLANK_STRING);
        // 退院後の主介護者続柄=""
        infoInDto.setTaiingoKaigoMainZcode(CommonConstants.BLANK_STRING);
        // 退院後の主介護者年齢=""
        infoInDto.setTaiingoKaigoMainAge(CommonConstants.BLANK_STRING);
        // 介護力(介護力が見込める)=""
        infoInDto.setKazokuKaigo8Umu(CommonConstants.NUMBER_0);
        // 介護力(介護力は見込めない)=""
        infoInDto.setKazokuKaigo9Umu(CommonConstants.NUMBER_0);
        // 介護力(家族や支援者はいない)=""
        infoInDto.setKazokuKaigo4Umu(CommonConstants.NUMBER_0);
        // 介護力が見込め選択=""
        infoInDto.setKazokuKaigo8Kbn(CommonConstants.NUMBER_0);
        // 家族や同居者等による虐待の疑い=""
        infoInDto.setGyakutaiUmu(CommonConstants.NUMBER_0);
        // 虐待の疑い（メモ）=""
        infoInDto.setGyakutaiUmuMemoKnj(CommonConstants.BLANK_STRING);
        // 特記事項=""
        infoInDto.setTokkiKnj(CommonConstants.BLANK_STRING);
        // 別紙印刷6フラグ=""
        infoInDto.setBesshi6Flg(CommonConstants.NUMBER_0);
        // 「院内の多職種カンファレンス」への参加
        infoInDto.setHospConfSanka(CommonConstants.NUMBER_0);
        // 「退院前カンファレンス」への参加=""
        infoInDto.setLeavConfSanka(CommonConstants.NUMBER_0);
        // 「退院前カンファレンス」への参加メモ=""
        infoInDto.setLeavConfMemoKnj(CommonConstants.BLANK_STRING);
        // 「退院前カンファレンス」への参加メモフォント="10"
        infoInDto.setLeavConfMemoFont(ReportConstants.FONT_SIZE_10);
        // 退院前訪問指導への同行=""
        infoInDto.setLeavHoumonDoukou(CommonConstants.NUMBER_0);

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06091_入院時情報提供書①帳票パラメータ取得
     * 
     * @param printSet    プリンタ設定
     * @param model       システム日付
     * @param printOption 印刷オプション
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private HospitalizationTimeInfoOfferPlanServiceInDto getHospitalizationOfferPlanReportParamsFrom1(
            ReportCommonPrintSet printSet,
            HospitalizationTimeOfferAllReportParameterModel model,
            HospitalizationTimeOfferPlanPrintOption printOption) throws Exception {
        // 帳票用データ詳細初期化
        HospitalizationTimeInfoOfferPlanServiceInDto infoInDto = this.getHospitalizationOfferPlanReportParams();

        // 【リクエストパラメータ】.印刷対象履歴リスト
        List<HospitalizationTimeOfferPlanSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();

        /*
         * ===============2.ケアアセスメント(2)情報を取得する===============
         * 
         */
        // 2.1. 下記の入院時情報提供書明細情報取得のDAOを利用し、入院時情報提供書①情報を取得する。
        KghCpnNyuTeikyouFrontKakuteiPByCriteriaInEntity kghCpnNyuTeikyouFrontKakuteiPByCriteriaInEntity = new KghCpnNyuTeikyouFrontKakuteiPByCriteriaInEntity();

        // リクエストパラメータ.データ.印刷対象履歴リスト[0].提供ID
        Integer teikyouId = CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getTeikyouId());
        kghCpnNyuTeikyouFrontKakuteiPByCriteriaInEntity.setAnTeikyouId(teikyouId);

        List<KghCpnNyuTeikyouFrontKakuteiPOutEntity> kghCpnNyuTeikyouFrontKakuteiPOutEntities = nyuuinJouhouTeikyoushoMeisaiSelectMapper
                .findKghCpnNyuTeikyouFrontKakuteiPByCriteria(kghCpnNyuTeikyouFrontKakuteiPByCriteriaInEntity);

        // 2.2. 「2.1.」で取得した入院時情報提供書①情報の件数をワーク入院時情報提供書①情報.総件数に設定する。
        // 2.2.1. 総件数 > 0 件の場合。
        if (CollectionUtils.isNotEmpty(kghCpnNyuTeikyouFrontKakuteiPOutEntities)) {
            // 2.3. 続柄マスタ情報の取得
            ComMucZokugaraInfoByCriteriaInEntity comMucZokugaraInfoByCriteriaInEntity = new ComMucZokugaraInfoByCriteriaInEntity();

            // 2.3.1. 下記の続柄マスタ取得のDAOを利用し、続柄マスタ情報を取得する。
            List<ComMucZokugaraInfoOutEntity> comMucZokugaraInfoOutEntities = comMucZokugaraSelectMapper
                    .findComMucZokugaraInfoByCriteria(comMucZokugaraInfoByCriteriaInEntity);
            // ワークキーパーソン続柄に続柄分類名
            String keyzokugaraKnj = CommonConstants.BLANK_STRING;
            // 2.3.2.
            // 上記処理「2.3.1」で取得した続柄コードが上記処理「2.1」で取得したキーパーソン（続柄）と一致の場合、ワークキーパーソン続柄に続柄分類名を設定する
            for (ComMucZokugaraInfoOutEntity comMucZokugaraInfoOutEntity : comMucZokugaraInfoOutEntities) {
                for (KghCpnNyuTeikyouFrontKakuteiPOutEntity kghCpnNyuTeikyouFrontKakuteiPOutEntity : kghCpnNyuTeikyouFrontKakuteiPOutEntities) {
                    if (kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKeyPersonZcode() == comMucZokugaraInfoOutEntity
                            .getZcode()) {
                        keyzokugaraKnj = comMucZokugaraInfoOutEntity.getZokugaraKnj();
                        break;
                    }
                }
            }
            String kaizokugaraKnj = CommonConstants.BLANK_STRING;
            // 2.3.3.
            // 上記処理「2.3.1」で取得した続柄コードが上記処理「2.1」で取得した主介護者（続柄）と一致の場合、ワーク主介護者続柄に続柄分類名を設定する
            for (ComMucZokugaraInfoOutEntity comMucZokugaraInfoOutEntity : comMucZokugaraInfoOutEntities) {
                for (KghCpnNyuTeikyouFrontKakuteiPOutEntity kghCpnNyuTeikyouFrontKakuteiPOutEntity : kghCpnNyuTeikyouFrontKakuteiPOutEntities) {
                    if (kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKaigoMainZcode() == comMucZokugaraInfoOutEntity
                            .getZcode()) {
                        kaizokugaraKnj = comMucZokugaraInfoOutEntity.getZokugaraKnj();
                        break;
                    }
                }
            }
            String taizokugaraKnj = CommonConstants.BLANK_STRING;
            // 2.3.4.
            // 上記処理「2.3.1」で取得した続柄コードが上記処理「2.1」で取得した退院後の主介護者（続柄）と一致の場合、ワーク退院後の主介護者続柄に続柄分類名を設定する
            for (ComMucZokugaraInfoOutEntity comMucZokugaraInfoOutEntity : comMucZokugaraInfoOutEntities) {
                for (KghCpnNyuTeikyouFrontKakuteiPOutEntity kghCpnNyuTeikyouFrontKakuteiPOutEntity : kghCpnNyuTeikyouFrontKakuteiPOutEntities) {
                    if (kghCpnNyuTeikyouFrontKakuteiPOutEntity.getTaiingoKaigoMainZcode() == comMucZokugaraInfoOutEntity
                            .getZcode()) {
                        taizokugaraKnj = comMucZokugaraInfoOutEntity.getZokugaraKnj();
                        break;
                    }
                }
            }
            /*
             * =============== 3. 入院時情報提供書①情報の編集処理を行う。===============
             * 
             */
            // 3.1. 担当ケアマネジャー名の取得
            // 3.1.1. 下記の職員基本情報取得のDAOを利用し、続柄マスタ情報を取得する。
            List<ShokuinInfoListSpaceSortOutEntity> shokuinInfoListSpaceSortOutEntities = comMscShokuinSelectMapper
                    .findShokuinInfoListSpaceSortByCriteria(new ShokuinInfoListSpaceSortByCriteriaInEntity());
            // 3.1.2. 上記処理「3.1.1.」で取得した情報.職員IDが上記「2.1.」OUTPUT情報.担当ケアマネIDと一致の場合、
            // ワーク担当ケアマネ名に職員名（姓）+ " "+職員名（名）を設定する
            String shokuinKnj = CommonConstants.BLANK_STRING;
            for (ShokuinInfoListSpaceSortOutEntity shokuinInfoListSpaceSortOutEntity : shokuinInfoListSpaceSortOutEntities) {
                for (KghCpnNyuTeikyouFrontKakuteiPOutEntity kghCpnNyuTeikyouFrontKakuteiPOutEntity : kghCpnNyuTeikyouFrontKakuteiPOutEntities) {
                    if (kghCpnNyuTeikyouFrontKakuteiPOutEntity.getTantoShokuId() == shokuinInfoListSpaceSortOutEntity
                            .getChkShokuId()) {
                        shokuinKnj = shokuinInfoListSpaceSortOutEntity.getShokuin1Knj() + CommonConstants.SPACE_STRING
                                + shokuinInfoListSpaceSortOutEntity.getShokuin2Knj();
                        break;
                    }
                }
            }
            // 3.2. 居宅介護支援事業所の取得
            KghStringComMscSvjigyoJigyoKnj2ByCriteriaInEntity kghStringComMscSvjigyoJigyoKnj2ByCriteriaInEntity = new KghStringComMscSvjigyoJigyoKnj2ByCriteriaInEntity();
            // 「2.1」で取得した居宅介護支援事業所ID
            List<Integer> list = new ArrayList<Integer>();
            Integer shienJigyoId = CommonConstants.NUMBER_0;
            for (KghCpnNyuTeikyouFrontKakuteiPOutEntity kghCpnNyuTeikyouFrontKakuteiPOutEntity : kghCpnNyuTeikyouFrontKakuteiPOutEntities) {
                shienJigyoId = kghCpnNyuTeikyouFrontKakuteiPOutEntity.getShienJigyoId();
            }
            list.add(shienJigyoId);
            kghStringComMscSvjigyoJigyoKnj2ByCriteriaInEntity.setLSvJigyoIdList(list);
            // 下記の1To15分割情報取得のDAOを利用し、居宅介護支援事業所情報を取得する。
            List<KghStringComMscSvjigyoJigyoKnj2OutEntity> kghStringComMscSvjigyoJigyoKnj2OutEntities = comMscSvjigyoJigyoKnj2SelectMapper
                    .findKghStringComMscSvjigyoJigyoKnj2ByCriteria(kghStringComMscSvjigyoJigyoKnj2ByCriteriaInEntity);
            // 3.3. 利用者基本情報の取得
            RiyoshaKihonByCriteriaInEntity riyoshaKihonByCriteriaInEntity = new RiyoshaKihonByCriteriaInEntity();
            // リクエストパラメータ.データ.印刷対象履歴リスト[0].利用者ID
            riyoshaKihonByCriteriaInEntity
                    .setAlUserid(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getUserId()));

            // 3.3.1. 下記の利用者（年齢以外）情報取得のDAOを利用し、利用者の情報を取得する。
            List<RiyoshaKihonOutEntity> riyoshaKihonOutEntityList = comTucUserSelectMapper
                    .findRiyoshaKihonByCriteria(riyoshaKihonByCriteriaInEntity);
            // 3.3.2. 上記「3.3.1. 」で利用者基本情報取得ある場合、下記処理を行う。
            if (CollectionUtils.isNotEmpty(riyoshaKihonOutEntityList)) {
                List<ComTucUserAddressOutEntity> allComTucUserAddressOutEntities = new ArrayList<ComTucUserAddressOutEntity>();
                // 下記の01-23 利用者住所履歴情報取得のDAOを利用し、利用者住所情報を取得する。
                ComTucUserAddressByCriteriaInEntity comTucUserAddressByCriteriaInEntity = new ComTucUserAddressByCriteriaInEntity();

                for (KghCpnNyuTeikyouFrontKakuteiPOutEntity kghCpnNyuTeikyouFrontKakuteiPOutEntity : kghCpnNyuTeikyouFrontKakuteiPOutEntities) {
                    // リクエストパラメータ.データ.印刷対象履歴リスト[0].利用者ID
                    comTucUserAddressByCriteriaInEntity
                            .setUserId(CommonDtoUtil.strValToInt(printSubjectHistoryList.get(0).getUserId()));
                    // 「2.1」で取得した記入日
                    comTucUserAddressByCriteriaInEntity
                            .setLsCreateYmd(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getCreateYmd());
                    List<ComTucUserAddressOutEntity> comTucUserAddressOutEntities = comTucUserAddressSelectMapper
                            .findComTucUserAddressByCriteria(comTucUserAddressByCriteriaInEntity);
                    if (comTucUserAddressOutEntities != null) {
                        allComTucUserAddressOutEntities.addAll(comTucUserAddressOutEntities);
                    }
                }
                /*
                 * =============== 4.帳票用データを設定===============
                 * 
                 */
                for (KghCpnNyuTeikyouFrontKakuteiPOutEntity kghCpnNyuTeikyouFrontKakuteiPOutEntity : kghCpnNyuTeikyouFrontKakuteiPOutEntities) {
                    String createYmd = nds3GkFunc01Logic.get2Gengouj(CommonConstants.NUMBER_ONE,
                            kghCpnNyuTeikyouFrontKakuteiPOutEntity.getCreateYmd());
                    ReportCommonDateParts createDateParts = this.getDate(createYmd);
                    // 記入日（年号）
                    infoInDto.setCreateDateGG(createDateParts.getDateGG());
                    // 記入日（年）
                    infoInDto.setCreateDateYY(createDateParts.getDateYY());
                    // 記入日（月）
                    infoInDto.setCreateDateMM(createDateParts.getDateMM());
                    // 記入日（日）
                    infoInDto.setCreateDateDD(createDateParts.getDateDD());

                    String sickDateYmd = nds3GkFunc01Logic.get2Gengouj(CommonConstants.NUMBER_ONE,
                            kghCpnNyuTeikyouFrontKakuteiPOutEntity.getSickYmd());
                    ReportCommonDateParts sickDateParts = this.getDate(sickDateYmd);
                    // 入院日（年号）
                    infoInDto.setSickDateGG(sickDateParts.getDateGG());
                    // 入院日（年）
                    infoInDto.setSickDateYY(sickDateParts.getDateYY());
                    // 入院日（月）
                    infoInDto.setSickDateMM(sickDateParts.getDateMM());
                    // 入院日（日）
                    infoInDto.setSickDateDD(sickDateParts.getDateDD());
                    String teikyouDateYmd = nds3GkFunc01Logic.get2Gengouj(CommonConstants.NUMBER_ONE,
                            kghCpnNyuTeikyouFrontKakuteiPOutEntity.getSickYmd());
                    ReportCommonDateParts teikyouDateParts = this.getDate(teikyouDateYmd);
                    // 情報提供日（年号）
                    infoInDto.setTeikyouDateGG(teikyouDateParts.getDateGG());
                    // 情報提供日（年）
                    infoInDto.setTeikyouDateYY(teikyouDateParts.getDateYY());
                    // 情報提供日（月）
                    infoInDto.setTeikyouDateMM(teikyouDateParts.getDateMM());
                    // 情報提供日（日）
                    infoInDto.setTeikyouDateDD(teikyouDateParts.getDateDD());
                    // 医療機関名=API定義の処理「2.1」の医療機関名
                    infoInDto.setHospKnj(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getHospKnj());
                    // 医療機関名の桁数>46の場合、医療機関名フォント="8"
                    if (ReportUtil.getByteLength(infoInDto.getHospKnj()) > ReportConstants.DIGIT_COUNT_46) {
                        infoInDto.setHospFont(ReportConstants.FONT_SIZE_8);
                    }
                    // 医療機関名の桁数<=46の場合、医療機関名フォント="9"
                    if (ReportUtil.getByteLength(infoInDto.getHospKnj()) <= ReportConstants.DIGIT_COUNT_46) {
                        infoInDto.setHospFont(ReportConstants.FONT_SIZE_9);
                    }
                    // 事業所名=API定義の処理「3.2」の事業名
                    for (KghStringComMscSvjigyoJigyoKnj2OutEntity kghStringComMscSvjigyoJigyoKnj2OutEntity : kghStringComMscSvjigyoJigyoKnj2OutEntities) {
                        infoInDto.setKyotakuknj(kghStringComMscSvjigyoJigyoKnj2OutEntity.getJigyoKnj());
                        // 事業所名の桁数>46の場合、事業所名フォント="7"
                        if (ReportUtil.getByteLength(kghStringComMscSvjigyoJigyoKnj2OutEntity
                                .getJigyoKnj()) > ReportConstants.DIGIT_COUNT_46) {
                            infoInDto.setKyotakuFont(ReportConstants.FONT_SIZE_7);
                        }
                        // 事業所名の桁数<=46の場合、事業所名フォント="9"
                        if (ReportUtil.getByteLength(kghStringComMscSvjigyoJigyoKnj2OutEntity
                                .getJigyoKnj()) <= ReportConstants.DIGIT_COUNT_46) {
                            infoInDto.setKyotakuFont(ReportConstants.FONT_SIZE_9);
                        }
                        // ご担当者名=API定義の処理「2.1」の担当者名
                        infoInDto.setTantoknj(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getTantoKnj());
                        // ケアマネジャー氏名=API定義の処理「3.1.2」のワーク担当ケアマネ名
                        infoInDto.setTantoCmnKnj(shokuinKnj);

                        // ケアマネジャー氏名の桁数>37の場合、ケアマネジャー氏名フォント="7"
                        if (ReportUtil.getByteLength(shokuinKnj) > ReportConstants.DIGIT_COUNT_37) {
                            infoInDto.setKyotakuFont(ReportConstants.FONT_SIZE_7);
                        }
                        // ケアマネジャー氏名の桁数<=37の場合、ケアマネジャー氏名フォント="9"
                        if (ReportUtil.getByteLength(shokuinKnj) <= ReportConstants.DIGIT_COUNT_37) {
                            infoInDto.setKyotakuFont(ReportConstants.FONT_SIZE_9);
                        }

                        // TEL=API定義の処理「2.1」の電話番号
                        infoInDto.setTel(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getTel());
                        // FAX=API定義の処理「2.1」のFAX番号
                        infoInDto.setFax(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getFax());
                        for (RiyoshaKihonOutEntity riyoshaKihonOutEntity : riyoshaKihonOutEntityList) {
                            // フリガナ（姓）=API定義の処理「3.3.1」のフリガナ（姓）
                            infoInDto.setUserKana1(riyoshaKihonOutEntity.getName1Kana());
                            // フリガナ（名）=API定義の処理「3.3.1」のフリガナ（名）
                            infoInDto.setUserKana2(riyoshaKihonOutEntity.getName2Kana());

                            // 27<{フリガナ（姓）} + " " + {フリガナ（名）} の桁数<=31の場合、フリガナフォント="9"
                            String nameKana = riyoshaKihonOutEntity.getName1Kana() + CommonConstants.SPACE_STRING
                                    + riyoshaKihonOutEntity.getName2Kana();
                            if (ReportUtil.getByteLength(nameKana) > ReportConstants.DIGIT_COUNT_27
                                    && ReportUtil.getByteLength(nameKana) <= ReportConstants.DIGIT_COUNT_31) {
                                infoInDto.setUserNameFont(ReportConstants.FONT_SIZE_9);
                            }
                            // 31<{フリガナ（姓）} + " " + {フリガナ（名）} の桁数<=35の場合、フリガナフォント="8"
                            if (ReportUtil.getByteLength(nameKana) > ReportConstants.DIGIT_COUNT_31
                                    && ReportUtil.getByteLength(nameKana) <= ReportConstants.DIGIT_COUNT_35) {
                                infoInDto.setUserNameFont(ReportConstants.FONT_SIZE_8);
                            }
                            // {フリガナ（姓）} + " " + {フリガナ（名）} の桁数>35の場合、フリガナフォント="7"
                            if (ReportUtil.getByteLength(nameKana) > ReportConstants.DIGIT_COUNT_35) {
                                infoInDto.setUserNameFont(ReportConstants.FONT_SIZE_7);
                            }
                            // {フリガナ（姓）} + " " + {フリガナ（名）} の桁数<=27の場合、フリガナフォント="10"
                            if (ReportUtil.getByteLength(nameKana) < ReportConstants.DIGIT_COUNT_27) {
                                infoInDto.setUserNameFont(ReportConstants.FONT_SIZE_10);
                            }
                            // 氏名（姓）=API定義の処理「3.3.1」の氏名（姓）
                            infoInDto.setUserName1(riyoshaKihonOutEntity.getName1Knj());
                            // 氏名（名）=API定義の処理「3.3.1」の氏名（名）
                            infoInDto.setUserName2(riyoshaKihonOutEntity.getName2Knj());

                            // 22<{氏名（姓）}+" "+{氏名（名）}の桁数<=26の場合、氏名フォント="10"
                            String username = riyoshaKihonOutEntity.getName1Knj() + CommonConstants.SPACE_STRING
                                    + riyoshaKihonOutEntity.getName2Knj();
                            if (ReportUtil.getByteLength(username) > ReportConstants.DIGIT_COUNT_22
                                    && ReportUtil.getByteLength(username) <= ReportConstants.DIGIT_COUNT_26) {
                                infoInDto.setUserNameFont(ReportConstants.FONT_SIZE_10);
                            }
                            // 26<{氏名（姓）}+" "+{氏名（名）}の桁数<=32の場合、氏名フォント="9"
                            if (ReportUtil.getByteLength(username) > ReportConstants.DIGIT_COUNT_26
                                    && ReportUtil.getByteLength(username) <= ReportConstants.DIGIT_COUNT_32) {
                                infoInDto.setUserNameFont(ReportConstants.FONT_SIZE_9);
                            }
                            // {氏名（姓）}+" "+{氏名（名）}の桁数>32の場合、氏名フォント="7"
                            if (ReportUtil.getByteLength(username) > ReportConstants.DIGIT_COUNT_32) {
                                infoInDto.setUserNameFont(ReportConstants.FONT_SIZE_7);
                            }
                            // {氏名（姓）}+" "+{氏名（名）}の桁数<=22の場合、氏名フォント="12"
                            if (ReportUtil.getByteLength(username) < ReportConstants.DIGIT_COUNT_22) {
                                infoInDto.setUserNameFont(ReportConstants.FONT_SIZE_12);
                            }

                            // 共通関数補足の処理「3.2.1」で変換後の生年月日(日本語元号付)
                            String birthdayYmd = nds3GkFunc01Logic
                                    .getChangeSeireki(nds3GkFunc01Logic.get2Gengouj(CommonConstants.NUMBER_ONE,
                                            riyoshaKihonOutEntity.getBirthdayYmd()));
                            // API定義の処理「2.1」で取得した記入日
                            String createDateYmd = nds3GkFunc01Logic
                                    .getCnvYymmdd(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getCreateYmd());
                            AgeYmOutDto ageYmOutDto = nds3GkFunc01Logic.getAgeYm(birthdayYmd, createDateYmd);
                            // 患者年齢=共通関数補足の処理「3.2.5」の計算結果年齢
                            infoInDto.setAge(CommonDtoUtil.objValToString(ageYmOutDto.getY3()));
                            // 患者性別=API定義の処理「3.3.1」の性別
                            infoInDto.setUserSex(riyoshaKihonOutEntity.getSex());

                            // 共通関数補足の「3.2.2」を行う
                            // パラメータ.変換前の年月日(和暦or西暦)に共通関数補足の処理「3.2.1」で取得した変換後の生年月日(日本語元号付)を設定
                            // パラメータ.切り取りの開始位置に1(開始位置)を設定
                            // パラメータ.切り取り文字数に4(文字数)を設定
                            String date = ndsMidaLogic.fNdsMida(birthdayYmd, CommonConstants.INT_1,
                                    CommonConstants.INT_4);
                            infoInDto.setBirthdayNenKbn(CommonConstants.BLANK_STRING);
                            // 関数戻り値が"明治"の場合、患者元年区分="1"
                            if (ReportConstants.MEIJI.equals(date)) {
                                infoInDto.setBirthdayNenKbn(CommonConstants.NUMBER_1.toString());
                            }
                            // 関数戻り値が"大正"の場合、患者元年区分="2"
                            if (ReportConstants.TAISHO.equals(date)) {
                                infoInDto.setBirthdayNenKbn(CommonConstants.NUMBER_2.toString());
                            }
                            // 関数戻り値が"昭和"の場合、患者元年区分="3"
                            if (ReportConstants.SHOWA.equals(date)) {
                                infoInDto.setBirthdayNenKbn(CommonConstants.NUMBER_3.toString());
                            }
                            // 共通関数補足の「3.2.2」を行う
                            // パラメータ.変換前の年月日(和暦or西暦)に共通関数補足の処理「3.2.1」で取得した変換後の生年月日(日本語元号付)を設定
                            // パラメータ.切り取りの開始位置に5(開始位置)を設定
                            // パラメータ.切り取り文字数に2(文字数)を設定
                            String birthdayYY = ndsMidaLogic.fNdsMida(birthdayYmd, CommonConstants.INT_5,
                                    CommonConstants.INT_2);
                            // 関数戻り値をbirthdayYYに設定
                            infoInDto.setBirthdayYY(birthdayYY);
                            // 共通関数補足の「3.2.2」を行う
                            // パラメータ.変換前の年月日(和暦or西暦)に共通関数補足の処理「3.2.1」で取得した変換後の生年月日(日本語元号付)を設定
                            // パラメータ.切り取りの開始位置に9(開始位置)を設定
                            // パラメータ.切り取り文字数に2(文字数)を設定
                            String birthdayMM = ndsMidaLogic.fNdsMida(birthdayYmd, CommonConstants.INT_9,
                                    CommonConstants.INT_2);
                            // 関数戻り値をbirthdayMMに設定
                            infoInDto.setBirthdayMM(birthdayMM);
                            // 共通関数補足の「3.2.2」を行う
                            // パラメータ.変換前の年月日(和暦or西暦)に共通関数補足の処理「3.2.1」で取得した変換後の生年月日(日本語元号付)を設定
                            // パラメータ.切り取りの開始位置に13(開始位置)を設定
                            // パラメータ.切り取り文字数に2(文字数)を設定
                            String birthdayDD = ndsMidaLogic.fNdsMida(birthdayYmd, CommonConstants.INT_13,
                                    CommonConstants.INT_2);
                            // 関数戻り値をbirthdayDDに設定
                            infoInDto.setBirthdayMM(birthdayDD);
                            // 患者郵便番号=API定義の処理「3.3.1」の郵便番号
                            infoInDto.setUserZip(riyoshaKihonOutEntity.getZip());

                            for (ComTucUserAddressOutEntity comTucUserAddressOutEntity : allComTucUserAddressOutEntities) {
                                // 患者住所=API定義の処理「3.3.2」の住所
                                infoInDto.setUserAddress(comTucUserAddressOutEntity.getAddressKnj());
                                // 患者住所の桁数>64の場合、患者住所フォント="7"
                                if (ReportUtil.getByteLength(
                                        comTucUserAddressOutEntity.getAddressKnj()) > ReportConstants.DIGIT_COUNT_64) {
                                    infoInDto.setUserAddressFont(ReportConstants.FONT_SIZE_7);
                                }
                                // 患者住所の桁数<=64の場合、患者住所フォント="10"
                                if (ReportUtil.getByteLength(
                                        comTucUserAddressOutEntity.getAddressKnj()) <= ReportConstants.DIGIT_COUNT_64) {
                                    infoInDto.setUserAddressFont(ReportConstants.FONT_SIZE_10);
                                }
                                // 患者電話番号=API定義の処理「3.3.2」の電話番号
                                infoInDto.setUserTel(comTucUserAddressOutEntity.getTel());
                                // 住環境=API定義の処理「2.1」の住環境
                                infoInDto.setHouseKbn(CommonDtoUtil
                                        .objValToString(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getHouseKbn()));
                                // 住居階層=API定義の処理「2.1」の住居階層
                                infoInDto.setHouseFloor(CommonDtoUtil
                                        .objValToString(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getHouseFloor()));
                                // 居室階=API定義の処理「2.1」の居室階
                                infoInDto.setHouseRoomFloor(CommonDtoUtil.objValToString(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getHouseRoomFloor()));
                                // エレベーター有無=API定義の処理「2.1」のエレベーター有無
                                infoInDto.setElevatorUmu(
                                        CommonDtoUtil.objValToString(
                                                kghCpnNyuTeikyouFrontKakuteiPOutEntity.getElevatorUmu()));
                                // 住まいに関する特記事項=API定義の処理「2.1」の住まいに関する特記事項
                                infoInDto.setHouseTokkiKnj(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getHouseTokkiKnj());
                                // 入院時の要介護度（確定版）=API定義の処理「2.1」の入院時の要介護度（確定版）
                                infoInDto.setNyuuinYokaiKbnKakutei(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getNyuuinYokaiKbnKakutei());
                                // 入院時の要介護度（確定版）="0"の場合、要支援=API定義の処理「2.1」の要介護度
                                if (CommonConstants.NUMBER_0
                                        .equals(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getNyuuinYokaiKbnKakutei())) {
                                    infoInDto.setHospYoshien(CommonDtoUtil
                                            .objValToString(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getHospYoshien()));
                                }
                                // 入院時の要介護度（確定版）="1"の場合、要介護=API定義の処理「2.1」の要介護度
                                else if (CommonConstants.NUMBER_1
                                        .equals(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getNyuuinYokaiKbnKakutei())) {
                                    infoInDto.setHospYokaigo(CommonDtoUtil
                                            .objValToString(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getHospYokaigo()));
                                }
                                // 上記以外の場合、要支援="0"、要介護="0"
                                else {
                                    infoInDto.setHospYoshien(CommonConstants.STR_0);
                                    infoInDto.setHospYokaigo(CommonConstants.STR_0);
                                }
                                // 共通関数補足の「3.1」を行う
                                // パラメータ.モードに1（平成 9年 9月 9日 に変換する）を設定
                                // パラメータ.変換前の年月日(和暦or西暦)にAPI定義の処理「2.1」で取得した認定有効開始日を設定
                                String ninteiStartYmd = nds3GkFunc01Logic.get2Gengouj(CommonConstants.NUMBER_ONE,
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getNinteiStartYmd());
                                ReportCommonDateParts ninteiStartYmdParts = this.getDate(ninteiStartYmd);
                                // 関数戻り値の年号をninteiStartDateGGに設定
                                infoInDto.setTeikyouDateGG(ninteiStartYmdParts.getDateGG());
                                // 関数戻り値の年をninteiStartDateYYに設定
                                infoInDto.setTeikyouDateYY(ninteiStartYmdParts.getDateYY());
                                // 関数戻り値の月をninteiStartDateMMに設定
                                infoInDto.setTeikyouDateMM(ninteiStartYmdParts.getDateMM());
                                // 関数戻り値の日をninteiStartDateDDに設定
                                infoInDto.setTeikyouDateDD(ninteiStartYmdParts.getDateDD());

                                // 共通関数補足の「3.1」を行う
                                // パラメータ.モードに1（平成 9年 9月 9日 に変換する）を設定
                                // パラメータ.変換前の年月日(和暦or西暦)にAPI定義の処理「2.1」で取得した認定申請日を設定
                                String ninteiShinseiYmd = nds3GkFunc01Logic.get2Gengouj(CommonConstants.NUMBER_ONE,
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getNinteiShinseiYmd());
                                ReportCommonDateParts ninteiShinseiYmdParts = this.getDate(ninteiShinseiYmd);
                                // 関数戻り値の年号をninteiShinseiDateGGに設定
                                infoInDto.setNinteiShinseiDateGG(ninteiShinseiYmdParts.getDateGG());
                                // 関数戻り値の年をninteiShinseiDateYYに設定
                                infoInDto.setNinteiShinseiDateYY(ninteiShinseiYmdParts.getDateYY());
                                // 関数戻り値の月をninteiShinseiDateMMに設定
                                infoInDto.setNinteiShinseiDateMM(ninteiShinseiYmdParts.getDateMM());
                                // 関数戻り値の日をninteiShinseiDateDDに設定
                                infoInDto.setNinteiShinseiDateDD(ninteiShinseiYmdParts.getDateDD());
                                // 区分変更=API定義の処理「2.1」の区分変更
                                infoInDto.setNinteiKbnHenkou(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getNinteiKbnHenkou());
                                // 共通関数補足の「3.1」を行う
                                // パラメータ.モードに1（平成 9年 9月 9日 に変換する）を設定
                                // パラメータ.変換前の年月日(和暦or西暦)にAPI定義の処理「2.1」で取得した区分変更申請日を設定
                                String kbnHenkouDate = nds3GkFunc01Logic.get2Gengouj(CommonConstants.NUMBER_ONE,
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKbnHenkouYmd());
                                ReportCommonDateParts kbnHenkouDateParts = this.getDate(kbnHenkouDate);
                                // 関数戻り値の年号をkbnHenkouDateGGに設定
                                infoInDto.setKbnHenkouDateGG(kbnHenkouDateParts.getDateGG());
                                // 関数戻り値の年をkbnHenkouDateYYに設定
                                infoInDto.setKbnHenkouDateYY(kbnHenkouDateParts.getDateYY());
                                // 関数戻り値の月をkbnHenkouDateMMに設定
                                infoInDto.setKbnHenkouDateMM(kbnHenkouDateParts.getDateMM());
                                // 関数戻り値の日をkbnHenkouDateDDに設定
                                infoInDto.setKbnHenkouDateDD(kbnHenkouDateParts.getDateDD());
                                // 未申請フラグ=API定義の処理「2.1」の未申請フラグ
                                infoInDto.setNinteiMishinseifFlg(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getNinteiMishinseiFlg());
                                // 障害高齢者の日常生活自立度=API定義の処理「2.1」の障害高齢者の日常生活自立度
                                infoInDto.setShogaiJiritsuCd(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getShogaiJiritsuCd());
                                // 認知症高齢者の日常生活自立度=API定義の処理「2.1」の認知症高齢者の日常生活自立度
                                infoInDto.setNinchiJiritsuCd(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getNinchiJiritsuCd());
                                // 医師の判断=API定義の処理「2.1」の医師の判断
                                infoInDto.setIshiHandanFlg(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getIshiHandanFlg());
                                // ケアマネジャーの判断=API定義の処理「2.1」のケアマネジャーの判断
                                infoInDto.setCareHandanFlg(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getCareHandanFlg());
                                // 介護保険の自己負担割合=API定義の処理「2.1」の自己負担割合有無
                                infoInDto.setFutanWariFlg(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getFutanWariFlg());
                                // 自己負担割合テキスト=API定義の処理「2.1」の自己負担割合
                                infoInDto.setFutanWariai(CommonDtoUtil
                                        .objValToString(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getFutanWariai()));
                                // 障害など認定の有無=API定義の処理「2.1」の障害など認定
                                infoInDto.setShogaiNintei(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getShogaiNintei());
                                // 障害認定（身体）=API定義の処理「2.1」の障害認定（身体）
                                infoInDto.setShintaiShogaiKbn(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getShintaiShogaiKbn());
                                // 障害認定（精神）=API定義の処理「2.1」の障害認定（精神）
                                infoInDto.setSeishinShogaiKbn(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getSeishinShogaiKbn());
                                // 障害認定（知的）=API定義の処理「2.1」の障害認定（知的）
                                infoInDto.setChitekiShogaiKbn(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getChitekiShogaiKbn());
                                // 年金種類（国民年金）=API定義の処理「2.1」の年金種類（国民年金）
                                infoInDto.setNenkin1Umu(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getNenkin1Umu());
                                // 年金種類（厚生年金）=API定義の処理「2.1」の年金種類（厚生年金）
                                infoInDto.setNenkin2Umu(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getNenkin2Umu());
                                // 年金種類（障害年金）=API定義の処理「2.1」の年金種類（障害年金）
                                infoInDto.setNenkin3Umu(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getNenkin3Umu());
                                // 年金種類（生活保護）=API定義の処理「2.1」の年金種類（生活保護）
                                infoInDto.setNenkin4Umu(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getNenkin4Umu());
                                // 年金種類（その他）=API定義の処理「2.1」の年金種類（その他）
                                infoInDto.setNenkin5Umu(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getNenkin5Umu());
                                // 年金などの種類その他メモ=API定義の処理「2.1」の年金種類（その他）メモ
                                infoInDto.setNenkinMemoKnj(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getNenkinMemoKnj());
                                // 世帯構成（独居）=API定義の処理「2.1」の世帯構成（独居）
                                infoInDto.setSetaiKousei1(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getSetaiKousei1());
                                // 世帯構成（高齢者世帯）=API定義の処理「2.1」の世帯構成（高齢者世帯）
                                infoInDto.setSetaiKousei2(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getSetaiKousei2());
                                // 世帯構成（子と同居）=API定義の処理「2.1」の世帯構成（子と同居）
                                infoInDto.setSetaiKousei3(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getSetaiKousei3());
                                // 世帯構成（その他）=API定義の処理「2.1」の世帯構成（その他）
                                infoInDto.setSetaiKousei4(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getSetaiKousei4());
                                // 世帯構成（日中独居）=API定義の処理「2.1」の世帯構成（日中独居）
                                infoInDto.setSetaiKousei5(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getSetaiKousei5());
                                // 世帯構成（その他）メモ=API定義の処理「2.1」の世帯構成（その他）メモ
                                infoInDto.setSetaiKouseiMemoKnj(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getSetaiKouseiMemoKnj());
                                // 主介護者氏名=API定義の処理「2.1」の確定版主介護者氏名
                                infoInDto.setKaigoMainKakutei(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKaigoMainKakutei());
                                // 主介護者続柄=API定義の処理「2.3.3」のワーク主介護者続柄
                                infoInDto.setKaigoMainZcode(CommonDtoUtil.objValToString(kaizokugaraKnj));
                                // 主介護者年齢=API定義の処理「2.1」の主介護者（年齢）
                                infoInDto.setKaigoMainAge(CommonDtoUtil
                                        .objValToString(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKaigoMainAge()));
                                // 同居・別居=API定義の処理「2.1」の主介護者（同居別居）
                                infoInDto.setKaigoMainKousei(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKaigoMainKousei());
                                // 主介護者TEL=API定義の処理「2.1」の主介護者（TEL）
                                infoInDto.setKaigoMainTel(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKaigoMainTel());
                                // キーパーソン氏名=API定義の処理「2.1」のキーパーソン
                                infoInDto.setKeyPerson(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKeyPerson());
                                // キーパーソン続柄=API定義の処理「2.3.2」のワークキーパーソン続柄
                                infoInDto.setKeyPersonZcode(CommonDtoUtil.objValToString(keyzokugaraKnj));
                                // キーパーソン年齢=API定義の処理「2.1」のキーパーソン（年齢）
                                infoInDto.setKeyPersonAge(CommonDtoUtil
                                        .objValToString(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKeyPersonAge()));
                                // キーパーソン連絡先TEL=API定義の処理「2.1」のキーパーソン（連絡先TEL）
                                infoInDto.setKeyPersonRenrakuTel(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKeyPersonRenrakuTel());
                                // キーパーソンTEL=API定義の処理「2.1」のキーパーソン（TEL）
                                infoInDto.setKeyPersonTel(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKeyPersonTel());
                                // 本人の性格/趣味・関心領域など=API定義の処理「2.1」の本人の性格/趣味関心領域など
                                infoInDto.setUserInfo(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getUserInfo());

                                // 別紙に印刷する=0(OFF)の場合、別紙印刷1フラグ=0
                                if (CommonConstants.STR_0.equals(printOption.getBesshiFlg())) {
                                    infoInDto.setBesshi1Flg(CommonConstants.NUMBER_0);
                                }
                                // 別紙に印刷する=1(ON)、かつ、本人の性格/趣味//関心領域などの桁数<=180の場合、別紙印刷1フラグ=0
                                if (CommonConstants.STR_1.equals(printOption.getBesshiFlg())
                                        && ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                                .getUserInfo()) <= ReportConstants.DIGIT_COUNT_180) {
                                    infoInDto.setBesshi1Flg(CommonConstants.NUMBER_0);
                                }
                                // 別紙に印刷する=1(ON)、かつ、本人の性格/趣味//関心領域などの桁数>180の場合、 別紙印刷1フラグ=1
                                if (CommonConstants.STR_1.equals(printOption.getBesshiFlg())
                                        && ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                                .getUserInfo()) > ReportConstants.DIGIT_COUNT_180) {
                                    infoInDto.setBesshi1Flg(CommonConstants.NUMBER_1);
                                }
                                // 本人の生活歴=API定義の処理「2.1」の本人の生活歴
                                infoInDto.setUserSeikatureki(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getUserSeikatureki());
                                // 別紙に印刷する=0(OFF)の場合、別紙印刷2フラグ="0"
                                if (CommonConstants.STR_0.equals(printOption.getBesshiFlg())) {
                                    infoInDto.setBesshi2Flg(CommonConstants.NUMBER_0);
                                }
                                // 別紙に印刷する=1(ON)、かつ、本人の生活歴の桁数<=180の場合、別紙印刷2フラグ="0"
                                if (CommonConstants.STR_1.equals(printOption.getBesshiFlg())
                                        && ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                                .getUserSeikatureki()) <= ReportConstants.DIGIT_COUNT_180) {
                                    infoInDto.setBesshi2Flg(CommonConstants.NUMBER_0);
                                }
                                // 別紙に印刷する=1(ON)、かつ、本人の生活歴の桁数>180の場合、別紙印刷2フラグ="1"
                                if (CommonConstants.STR_1.equals(printOption.getBesshiFlg())
                                        && ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                                .getUserSeikatureki()) > ReportConstants.DIGIT_COUNT_180) {
                                    infoInDto.setBesshi2Flg(CommonConstants.NUMBER_1);
                                }
                                // 入院前の本人の意向=API定義の処理「2.1」の入院前の本人の意向
                                infoInDto.setUserIkouKnj(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getUserIkouKnj());
                                // 別紙に印刷する=0(OFF)の場合、別紙印刷3フラグ="0"
                                if (printOption.getBesshiFlg().equals(CommonConstants.STR_0)) {
                                    infoInDto.setBesshi3Flg(CommonConstants.NUMBER_0);
                                }
                                // 別紙に印刷する=1(ON)、かつ、入院前の本人の意向の桁数<=180の場合、別紙印刷3フラグ="0"
                                if (CommonConstants.STR_1.equals(printOption.getBesshiFlg())
                                        && ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                                .getUserIkouKnj()) <= ReportConstants.DIGIT_COUNT_180) {
                                    infoInDto.setBesshi3Flg(CommonConstants.NUMBER_0);
                                }
                                // 別紙に印刷する=1(ON)、かつ、入院前の本人の意向の桁数>180の場合、別紙印刷3フラグ="1"
                                if (CommonConstants.STR_1.equals(printOption.getBesshiFlg())
                                        && ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                                .getUserIkouKnj()) > ReportConstants.DIGIT_COUNT_180) {
                                    infoInDto.setBesshi3Flg(CommonConstants.NUMBER_1);
                                }
                                // 入院前の家族の意向=API定義の処理「2.1」の入院前の家族の意向
                                infoInDto.setKazokuIkouKnj(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKazokuIkouKnj());
                                // 別紙に印刷する=0(OFF)の場合、別紙印刷4フラグ="0"
                                if (CommonConstants.STR_0.equals(printOption.getBesshiFlg())) {
                                    infoInDto.setBesshi4Flg(CommonConstants.NUMBER_0);
                                }
                                // 別紙に印刷する=1(ON)、かつ、入院前の家族の意向の桁数<=180の場合、別紙印刷4フラグ="0"
                                if (CommonConstants.STR_1.equals(printOption.getBesshiFlg())
                                        && ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                                .getKazokuIkouKnj()) <= ReportConstants.DIGIT_COUNT_180) {
                                    infoInDto.setBesshi4Flg(CommonConstants.NUMBER_0);
                                }
                                // 別紙に印刷する=1(ON)、かつ、入院前の家族の意向の桁数>180の場合、別紙印刷4フラグ="1"
                                if (CommonConstants.STR_1.equals(printOption.getBesshiFlg())
                                        && ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                                .getKazokuIkouKnj()) > ReportConstants.DIGIT_COUNT_180) {
                                    infoInDto.setBesshi4Flg(CommonConstants.NUMBER_1);
                                }

                                // 本人同封の居宅サービス計画（１）参照=API定義の処理「2.1」の入院前の本人の意向（計画書（1）参照有無）
                                infoInDto.setUserIkouCksFlg(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getUserIkouCksFlg());
                                // 家族同封の居宅サービス計画（１）参照=API定義の処理「2.1」の入院前の家族の意向（計画書（1）参照有無）
                                infoInDto.setKazokuIkouCksFlg(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKazokuIkouCksFlg());
                                // 入院前の介護サービスの利用状況（居宅サービス計画書123表）=API定義の処理「2.1」の入院前の介護サービスの利用状況（居宅サービス計画書123表）
                                infoInDto.setServiceRiyoKbn1(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getServiceRiyoKbn());
                                // 入院前の介護サービスの利用状況（その他）=API定義の処理「2.1」の入院前の介護サービスの利用状況（その他）
                                infoInDto.setServiceRiyoKbn2(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getServiceRiyoKbn2());
                                // 入院前の介護サービスの利用状況（その他メモ）=API定義の処理「2.1」の入院前の介護サービスの利用状況（その他メモ）
                                infoInDto.setServiceRiyoMemoKnj(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getServiceRiyoMemoKnj());

                                // 入院前の介護サービスの利用状況（その他メモ）の桁数>44の場合、入院前の介護サービスの利用状況（その他メモ）フォント="9"
                                if (ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                        .getServiceRiyoMemoKnj()) > ReportConstants.DIGIT_COUNT_44) {
                                    infoInDto.setServiceRiyoMemoFont(ReportConstants.FONT_SIZE_9);
                                }
                                // 入院前の介護サービスの利用状況（その他メモ）の桁数<=44の場合、入院前の介護サービスの利用状況（その他メモ）フォント="10"
                                if (ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                        .getServiceRiyoMemoKnj()) <= ReportConstants.DIGIT_COUNT_44) {
                                    infoInDto.setServiceRiyoMemoFont(ReportConstants.FONT_SIZE_10);
                                }
                                // 在宅生活に必要な要件=API定義の処理「2.1」の在宅生活に必要な要件
                                infoInDto.setZaitakuYokenKnj(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getZaitakuYokenKnj());

                                // 別紙に印刷する=0(OFF)の場合、別紙印刷5フラグ="0"
                                if (CommonConstants.STR_0.equals(printOption.getBesshiFlg())) {
                                    infoInDto.setBesshi5Flg(CommonConstants.NUMBER_0);
                                }
                                // 別紙に印刷する=1(ON)、かつ、在宅生活に必要な要件の桁数<=270の場合、別紙印刷5フラグ="0"
                                if (CommonConstants.STR_1.equals(printOption.getBesshiFlg())
                                        && ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                                .getZaitakuYokenKnj()) <= ReportConstants.DIGIT_COUNT_270) {
                                    infoInDto.setBesshi5Flg(CommonConstants.NUMBER_0);
                                }
                                // 別紙に印刷する=1(ON)、かつ、在宅生活に必要な要件の桁数>270の場合、別紙印刷5フラグ="1"
                                if (CommonConstants.STR_1.equals(printOption.getBesshiFlg())
                                        && ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                                .getZaitakuYokenKnj()) > ReportConstants.DIGIT_COUNT_270) {
                                    infoInDto.setBesshi5Flg(CommonConstants.NUMBER_1);
                                }
                                // 退院後の世帯状況(独居)=API定義の処理「2.1」の家族の介護力（独居）
                                infoInDto.setKazokuKaigo1Umu(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKazokuKaigo1Umu());
                                // 退院後の世帯状況(高齢世帯)=API定義の処理「2.1」の家族の介護力（高齢世帯）
                                infoInDto.setKazokuKaigo3Umu(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKazokuKaigo3Umu());
                                // 退院後の世帯状況(子と同居)=API定義の処理「2.1」の家族の介護力（子と同居）
                                infoInDto.setKazokuKaigo7Umu(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKazokuKaigo7Umu());
                                // 退院後の世帯状況(日中独居)=API定義の処理「2.1」の家族の介護力（日中独居）
                                infoInDto.setKazokuKaigo2Umu(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKazokuKaigo2Umu());
                                // 退院後の世帯状況(その他)=API定義の処理「2.1」の家族の介護力（その他）
                                infoInDto.setKazokuKaigo6Umu(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKazokuKaigo6Umu());
                                // 家族構成員数=API定義の処理「2.1」の家族の介護力（子と同居）_家族構成員数
                                infoInDto.setKazokuKaigo7Ninzu(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKazokuKaigo7Ninzu());
                                // 退院後の世帯状況（その他）メモ=API定義の処理「2.1」の家族の介護力（その他）メモ
                                infoInDto.setKazokuKaigoMemoKnj(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKazokuKaigoMemoKnj());
                                // 世帯に対する配慮=API定義の処理「2.1」の世帯に対する配慮
                                infoInDto.setSetaiHairyo(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getSetaiHairyo());
                                // 世帯に対する配慮メモ=API定義の処理「2.1」の世帯に対する配慮（必要メモ）
                                infoInDto.setSetaiHairyoMemoKnj(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getSetaiHairyoMemoKnj());
                                // 退院後の主介護者=API定義の処理「2.1」の退院後の主介護者
                                infoInDto.setTaiingoKaigoMain(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getTaiingoKaigoMain());
                                // 退院後の主介護者氏名=API定義の処理「2.1」の退院後の主介護者（氏名）
                                infoInDto.setTaiingoKaigoMainName(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getTaiingoKaigoMainName());
                                // 退院後の主介護者続柄=API定義の処理「2.3.4」のワーク退院後の主介護者続柄
                                infoInDto.setTaiingoKaigoMainZcode(CommonDtoUtil.objValToString(taizokugaraKnj));
                                // 退院後の主介護者年齢=API定義の処理「2.1」の退院後の主介護者年齢
                                infoInDto.setTaiingoKaigoMainAge(
                                        CommonDtoUtil.objValToString(
                                                kghCpnNyuTeikyouFrontKakuteiPOutEntity.getTaiingoKaigoMainAge()));
                                // 介護力(介護力が見込める)=API定義の処理「2.1」の家族の介護力（介護力が見込める）
                                infoInDto.setKazokuKaigo8Umu(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKazokuKaigo8Umu());
                                // 介護力(介護力は見込めない)=API定義の処理「2.1」の家族の介護力（介護力は見込めない）
                                infoInDto.setKazokuKaigo9Umu(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKazokuKaigo9Umu());
                                // 介護力(家族や支援者はいない)=API定義の処理「2.1」の家族の介護力（サポートできる家族や支援者が不在）
                                infoInDto.setKazokuKaigo4Umu(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKazokuKaigo4Umu());
                                // 介護力が見込め選択=API定義の処理「2.1」の介護力見込み
                                infoInDto.setKazokuKaigo8Kbn(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getKazokuKaigo8Kbn());
                                // 家族や同居者等による虐待の疑い=API定義の処理「2.1」の虐待の疑い
                                infoInDto.setGyakutaiUmu(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getGyakutaiUmu());
                                // 虐待の疑い（メモ）=API定義の処理「2.1」の虐待の疑い（メモ）
                                infoInDto.setGyakutaiUmuMemoKnj(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getGyakutaiUmuMemoKnj());
                                // 特記事項=API定義の処理「2.1」の特記事項
                                infoInDto.setTokkiKnj(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getTokkiKnj());

                                // 別紙に印刷する=0(OFF)の場合、別紙印刷6フラグ="0"
                                if (CommonConstants.STR_0.equals(printOption.getBesshiFlg())) {
                                    infoInDto.setBesshi6Flg(CommonConstants.NUMBER_0);
                                }
                                // 別紙に印刷する=1(ON)、かつ、特記事項の桁数<=180の場合、別紙印刷6フラグ="0"
                                if (CommonConstants.STR_1.equals(printOption.getBesshiFlg())
                                        && ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                                .getTokkiKnj()) <= ReportConstants.DIGIT_COUNT_180) {
                                    infoInDto.setBesshi6Flg(CommonConstants.NUMBER_0);
                                }
                                // 別紙に印刷する=1(ON)、かつ、特記事項の桁数>180の場合、別紙印刷6フラグ="1"
                                if (CommonConstants.STR_1.equals(printOption.getBesshiFlg())
                                        && ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                                .getTokkiKnj()) > ReportConstants.DIGIT_COUNT_180) {
                                    infoInDto.setBesshi6Flg(CommonConstants.NUMBER_1);
                                }
                                // 「院内の多職種カンファレンス」への参加=API定義の処理「2.1」の院内の多職種カンファレンスへの参加
                                infoInDto.setHospConfSanka(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getHospConfSanka());
                                // 「退院前カンファレンス」への参加=API定義の処理「2.1」の退院前カンファレンスへの参加
                                infoInDto.setLeavConfSanka(kghCpnNyuTeikyouFrontKakuteiPOutEntity.getLeavConfSanka());
                                // 「退院前カンファレンス」への参加メモ=API定義の処理「2.1」の退院前カンファレンスへの参加メモ
                                infoInDto.setLeavConfMemoKnj(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getLeavConfMemoKnj());
                                // 「退院前カンファレンス」への参加メモの桁数>34の場合、「退院前カンファレンス」への参加メモフォント="9"
                                if (ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                        .getLeavConfMemoKnj()) > ReportConstants.DIGIT_COUNT_34) {
                                    infoInDto.setLeavConfMemoFont(ReportConstants.FONT_SIZE_9);
                                }
                                // 「退院前カンファレンス」への参加メモの桁数<=34の場合、「退院前カンファレンス」への参加メモフォント="10"
                                if (ReportUtil.getByteLength(kghCpnNyuTeikyouFrontKakuteiPOutEntity
                                        .getLeavConfMemoKnj()) <= ReportConstants.DIGIT_COUNT_34) {
                                    infoInDto.setLeavConfMemoFont(ReportConstants.FONT_SIZE_10);
                                }
                                // 退院前訪問指導への同行=API定義の処理「2.1」の退院前訪問指導への同行
                                infoInDto.setLeavHoumonDoukou(
                                        kghCpnNyuTeikyouFrontKakuteiPOutEntity.getLeavHoumonDoukou());

                            }

                        }

                    }
                }
            }
        }
        return infoInDto;
    }

    /**
     * U06091_入院時情報提供書②帳票パラメータ取得
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    public HospitalizationInfoOffer2ReportServiceInDto getHospitalizationInfoOffer2ReportParameters(
            HospitalizationTimeOfferAllReportParameterModel model,
            HospitalizationTimeInfoOfferPlanServiceOutDto outDto) throws Exception {

        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));
        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷オプション
        HospitalizationTimeOfferPlanPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.記入用シートを印刷するフラグ
        Boolean isEmptyFlg = false;
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getEmptyFlg())) {
            isEmptyFlg = true;
        }

        // 帳票用データ詳細
        HospitalizationInfoOffer2ReportServiceInDto infoInDto = new HospitalizationInfoOffer2ReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true
         * の場合、結果レスポンスを返却する。===============
         * ===============リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false
         * の場合、入院時情報提供書②情報の取得。===============
         * 
         */
        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (isEmptyFlg) {
            infoInDto = this.getHospitalizationInfoOffer2ReportParams();
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
        } else {
            infoInDto = this.getReportParamsFromHospitalizationInfoOffer2(printSet, model, printOption);
        }
        // 記入用シートを印刷するフラグ ← リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(isEmptyFlg);
        // 欄外に利用者氏名を印刷するフラグ ← リクエストパラメータ.データ.印刷オプション.欄外に利用者氏名を印刷するフラグ
        infoInDto.setUserNameFlg(CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getUserNameFlg()));
        // 指定日印刷区分 ← リクエストパラメータ.データ.印刷設定.指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(model.getPrintSet().getShiTeiKubun()));

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06091_入院時情報提供書②パラメータ取得
     * 
     * @param inDto 入力データ
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private HospitalizationInfoOffer2ReportServiceInDto getHospitalizationInfoOffer2ReportParams() throws Exception {

        HospitalizationInfoOffer2ReportServiceInDto infoInDto = new HospitalizationInfoOffer2ReportServiceInDto();

        // 指定日（年号）
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 利用者氏名
        infoInDto.setRiyoushaNm(CommonConstants.BLANK_STRING);
        // 麻痺の状況
        infoInDto.setMahiKbn(CommonConstants.INT_9);
        // 褥瘡の有無
        infoInDto.setJyokusouUmu(CommonConstants.INT_9);
        // 褥瘡メモ
        infoInDto.setJyokusouMemoKnj(CommonConstants.BLANK_STRING);
        // ADL移動（自立）
        infoInDto.setAdlIdou1(CommonConstants.INT_0);
        // ADL移動（見守り）
        infoInDto.setAdlIdou2(CommonConstants.INT_0);
        // ADL移動（一部介助）
        infoInDto.setAdlIdou3(CommonConstants.INT_0);
        // ADL移動（全介助）
        infoInDto.setAdlIdou4(CommonConstants.INT_0);
        // ADL移動（室内）（杖）
        infoInDto.setAdlIdouShitsunai1(CommonConstants.INT_0);
        // ADL移動（室内）（歩行器）
        infoInDto.setAdlIdouShitsunai2(CommonConstants.INT_0);
        // ADL移動（室内）（車いす）
        infoInDto.setAdlIdouShitsunai3(CommonConstants.INT_0);
        // ADL移動（室内）（その他）
        infoInDto.setAdlIdouShitsunai4(CommonConstants.INT_0);
        // ADL移乗（自立）
        infoInDto.setAdlIjyou1(CommonConstants.INT_0);
        // ADL移乗（見守り）
        infoInDto.setAdlIjyou2(CommonConstants.INT_0);
        // ADL移乗（一部介助）
        infoInDto.setAdlIjyou3(CommonConstants.INT_0);
        // ADL移乗（全介助）
        infoInDto.setAdlIjyou4(CommonConstants.INT_0);
        // ADL移動(屋外)（杖）
        infoInDto.setAdlIdouShudan1(CommonConstants.INT_0);
        // ADL移動(屋外)（歩行器）
        infoInDto.setAdlIdouShudan2(CommonConstants.INT_0);
        // ADL移動(屋外)（車いす）
        infoInDto.setAdlIdouShudan3(CommonConstants.INT_0);
        // ADL移動(屋外)（その他）
        infoInDto.setAdlIdouShudan4(CommonConstants.INT_0);
        // ADL更衣（自立）
        infoInDto.setAdlKoui1(CommonConstants.INT_0);
        // ADL更衣（見守り）
        infoInDto.setAdlKoui2(CommonConstants.INT_0);
        // ADL更衣（一部介助）
        infoInDto.setAdlKoui3(CommonConstants.INT_0);
        // ADL更衣（全介助）
        infoInDto.setAdlKoui4(CommonConstants.INT_0);
        // ADL起居動作（自立）
        infoInDto.setAdlKikyo1(CommonConstants.INT_0);
        // ADL起居動作（見守り）
        infoInDto.setAdlKikyo2(CommonConstants.INT_0);
        // ADL起居動作（一部介助）
        infoInDto.setAdlKikyo3(CommonConstants.INT_0);
        // ADL起居動作（全介助）
        infoInDto.setAdlKikyo4(CommonConstants.INT_0);
        // ADL整容（自立）
        infoInDto.setAdlSeiyou1(CommonConstants.INT_0);
        // ADL整容（見守り）
        infoInDto.setAdlSeiyou2(CommonConstants.INT_0);
        // ADL整容（一部介助）
        infoInDto.setAdlSeiyou3(CommonConstants.INT_0);
        // ADL整容（全介助）
        infoInDto.setAdlSeiyou4(CommonConstants.INT_0);
        // ADL入浴（自立）
        infoInDto.setAdlNyuuyoku1(CommonConstants.INT_0);
        // ADL入浴（見守り）
        infoInDto.setAdlNyuuyoku2(CommonConstants.INT_0);
        // ADL入浴（一部介助）
        infoInDto.setAdlNyuuyoku3(CommonConstants.INT_0);
        // ADL入浴（全介助）
        infoInDto.setAdlNyuuyoku4(CommonConstants.INT_0);
        // ADL食事（自立）
        infoInDto.setAdlShokuji1(CommonConstants.INT_0);
        // ADL食事（見守り）
        infoInDto.setAdlShokuji2(CommonConstants.INT_0);
        // ADL食事（一部介助）
        infoInDto.setAdlShokuji3(CommonConstants.INT_0);
        // ADL食事（全介助）
        infoInDto.setAdlShokuji4(CommonConstants.INT_0);
        // 食事回数
        infoInDto.setShokujiCnt(CommonConstants.BLANK_STRING);
        // 食事回数（朝）
        infoInDto.setShokujiCntMorn(CommonConstants.BLANK_STRING);
        // 食事回数（昼）
        infoInDto.setShokujiCntNoon(CommonConstants.BLANK_STRING);
        // 食事回数（夜）
        infoInDto.setShokujiCntEven(CommonConstants.BLANK_STRING);
        // 食事制限
        infoInDto.setShokujiSeigen(CommonConstants.INT_9);
        // 食事制限メモ
        infoInDto.setShokujiSeigenMemoKnj(CommonConstants.BLANK_STRING);
        // 食事形態（普通）
        infoInDto.setShokujiKeitai1(CommonConstants.INT_0);
        // 食事形態（きざみ）
        infoInDto.setShokujiKeitai2(CommonConstants.INT_0);
        // 食事形態（嚥下障害食）
        infoInDto.setShokujiKeitai3(CommonConstants.INT_0);
        // 食事形態（ミキサー）
        infoInDto.setShokujiKeitai4(CommonConstants.INT_0);
        // UDF等の食形態区分
        infoInDto.setUdfShokujiKeitaiKbn(CommonConstants.BLANK_STRING);
        // 摂取方法（経口）
        infoInDto.setSesshuHouhou1(CommonConstants.INT_0);
        // 摂取方法（経管栄養）
        infoInDto.setSesshuHouhou2(CommonConstants.INT_0);
        // 水分とろみ
        infoInDto.setSuibunToromi(CommonConstants.INT_9);
        // 水分制限
        infoInDto.setSuibunSeigen(CommonConstants.INT_9);
        // 水分制限メモ
        infoInDto.setSuibunSeigenMemoKnj(CommonConstants.BLANK_STRING);
        // 嚥下機能
        infoInDto.setEngeUmu(CommonConstants.INT_9);
        // 義歯
        infoInDto.setGishiUmu(CommonConstants.INT_9);
        // 義歯区分
        infoInDto.setGishiKbn(CommonConstants.INT_9);
        // 口腔清潔
        infoInDto.setKoukuuCare(CommonConstants.INT_9);
        // 口臭
        infoInDto.setKoushuuUmu(CommonConstants.INT_9);
        // 排尿（自立）
        infoInDto.setHainyou1(CommonConstants.INT_0);
        // 排尿（見守り）
        infoInDto.setHainyou2(CommonConstants.INT_0);
        // 排尿（一部介助）
        infoInDto.setHainyou3(CommonConstants.INT_0);
        // 排尿（全介助）
        infoInDto.setHainyou4(CommonConstants.INT_0);
        // ポータブルトイレ
        infoInDto.setPortableToiletUmu(CommonConstants.INT_9);
        // 排便（自立）
        infoInDto.setHaiben1(CommonConstants.INT_0);
        // 排便（見守り）
        infoInDto.setHaiben2(CommonConstants.INT_0);
        // 排便（一部介助）
        infoInDto.setHaiben3(CommonConstants.INT_0);
        // 排便（全介助）
        infoInDto.setHaiben4(CommonConstants.INT_0);
        // オムツ/パッド
        infoInDto.setOmutsuPadUmu(CommonConstants.INT_9);
        // 睡眠の状態
        infoInDto.setSleepJyoutai(CommonConstants.INT_9);
        // 睡眠の状態メモ
        infoInDto.setSleepMemoKnj(CommonConstants.BLANK_STRING);
        // 眠剤の使用
        infoInDto.setSleepDrugUmu(CommonConstants.INT_9);
        // 喫煙有無
        infoInDto.setSmokingUmu(CommonConstants.INT_9);
        // 喫煙量
        infoInDto.setSmoking(CommonConstants.BLANK_STRING);
        // 飲酒有無
        infoInDto.setAlcoholUmu(CommonConstants.INT_9);
        // 飲酒量
        infoInDto.setAlcohol(CommonConstants.BLANK_STRING);
        // 飲酒量フォント
        infoInDto.setAlcoholFontSize(ReportConstants.FONT_SIZE_10);
        // 視力
        infoInDto.setEyesightKbn(CommonConstants.INT_9);
        // 眼鏡
        infoInDto.setGlassesUmu(CommonConstants.INT_9);
        // 眼鏡メモ
        infoInDto.setGlassesMemoKnj(CommonConstants.BLANK_STRING);
        // 聴力
        infoInDto.setHearingKbn(CommonConstants.INT_9);
        // 補聴器
        infoInDto.setHearingAidUmu(CommonConstants.INT_9);
        // 言語
        infoInDto.setLanguageAbility(CommonConstants.INT_9);
        // 意思疎通
        infoInDto.setComnKbn(CommonConstants.INT_9);
        // コミュニケーションに関する特記事項
        infoInDto.setComnTokkiKnj(CommonConstants.BLANK_STRING);
        // 精神面における療養上の問題（なし）
        infoInDto.setRyouyou1Umu(CommonConstants.INT_0);
        // 精神面における療養上の問題（幻視幻聴）
        infoInDto.setRyouyou2Umu(CommonConstants.INT_0);
        // 精神面における療養上の問題（興奮）
        infoInDto.setRyouyou3Umu(CommonConstants.INT_0);
        // 精神面における療養上の問題（焦燥不穏）
        infoInDto.setRyouyou4Umu(CommonConstants.INT_0);
        // 精神面における療養上の問題（妄想）
        infoInDto.setRyouyou5Umu(CommonConstants.INT_0);
        // 精神面における療養上の問題（暴力/攻撃性）
        infoInDto.setRyouyou6Umu(CommonConstants.INT_0);
        // 精神面における療養上の問題（介護への抵抗）
        infoInDto.setRyouyou7Umu(CommonConstants.INT_0);
        // 精神面における療養上の問題（不眠）
        infoInDto.setRyouyou8Umu(CommonConstants.INT_0);
        // 精神面における療養上の問題（昼夜逆転）
        infoInDto.setRyouyou9Umu(CommonConstants.INT_0);
        // 精神面における療養上の問題（徘徊）
        infoInDto.setRyouyou10Umu(CommonConstants.INT_0);
        // 精神面における療養上の問題（危険行為）
        infoInDto.setRyouyou11Umu(CommonConstants.INT_0);
        // 精神面における療養上の問題（不潔行為）
        infoInDto.setRyouyou12Umu(CommonConstants.INT_0);
        // 精神面における療養上の問題（その他）
        infoInDto.setRyouyou13Umu(CommonConstants.INT_0);
        // 精神面における療養上の問題（その他メモ）
        infoInDto.setRyouyouMemoKnj(CommonConstants.BLANK_STRING);
        // 疾患歴（なし）
        infoInDto.setSick1Umu(CommonConstants.INT_0);
        // 疾患歴（悪性腫瘍）
        infoInDto.setSick2Umu(CommonConstants.INT_0);
        // 疾患歴（認知症）
        infoInDto.setSick3Umu(CommonConstants.INT_0);
        // 疾患歴（急性呼吸器感染症）
        infoInDto.setSick4Umu(CommonConstants.INT_0);
        // 疾患歴（脳血管障害）
        infoInDto.setSick5Umu(CommonConstants.INT_0);
        // 疾患歴（骨折）
        infoInDto.setSick6Umu(CommonConstants.INT_0);
        // 疾患歴（その他）
        infoInDto.setSick7Umu(CommonConstants.INT_0);
        // 疾患歴（その他）メモ
        infoInDto.setSickMemoKnj(CommonConstants.BLANK_STRING);
        // 最近半年間での入院
        infoInDto.setNyuuinUmu(CommonConstants.INT_9);
        // 入院理由
        infoInDto.setNyuuinRiyu(CommonConstants.BLANK_STRING);
        // 最近半年間での入院開始日（年号）
        infoInDto.setNyuuinStartDateGG(CommonConstants.BLANK_STRING);
        // 最近半年間での入院開始日（年）
        infoInDto.setNyuuinStartDateYY(CommonConstants.BLANK_STRING);
        // 最近半年間での入院開始日（月）
        infoInDto.setNyuuinStartDateMM(CommonConstants.BLANK_STRING);
        // 最近半年間での入院開始日（日）
        infoInDto.setNyuuinStartDateDD(CommonConstants.BLANK_STRING);
        // 最近半年間での入院終了日（年号）
        infoInDto.setNyuuinEndDateGG(CommonConstants.BLANK_STRING);
        // 最近半年間での入院終了日（年）
        infoInDto.setNyuuinEndDateYY(CommonConstants.BLANK_STRING);
        // 最近半年間での入院終了日（月）
        infoInDto.setNyuuinEndDateMM(CommonConstants.BLANK_STRING);
        // 最近半年間での入院終了日（日）
        infoInDto.setNyuuinEndDateDD(CommonConstants.BLANK_STRING);
        // 入院頻度
        infoInDto.setNyuuinHindo(CommonConstants.INT_9);
        // 医療処置（なし）
        infoInDto.setShochi1Umu(CommonConstants.INT_0);
        // 医療処置（点滴）
        infoInDto.setShochi2Umu(CommonConstants.INT_0);
        // 医療処置（酸素療法）
        infoInDto.setShochi3Umu(CommonConstants.INT_0);
        // 医療処置（喀痰吸引）
        infoInDto.setShochi4Umu(CommonConstants.INT_0);
        // 医療処置（気管切開）
        infoInDto.setShochi5Umu(CommonConstants.INT_0);
        // 医療処置（胃ろう）
        infoInDto.setShochi6Umu(CommonConstants.INT_0);
        // 医療処置（経鼻栄養）
        infoInDto.setShochi7Umu(CommonConstants.INT_0);
        // 医療処置（経腸栄養）
        infoInDto.setShochi8Umu(CommonConstants.INT_0);
        // 医療処置（褥瘡）
        infoInDto.setShochi9Umu(CommonConstants.INT_0);
        // 医療処置（尿道カテーテル）
        infoInDto.setShochi10Umu(CommonConstants.INT_0);
        // 医療処置（尿路ストーマ）
        infoInDto.setShochi11Umu(CommonConstants.INT_0);
        // 医療処置（消化管ストーマ）
        infoInDto.setShochi12Umu(CommonConstants.INT_0);
        // 医療処置（痛みコントロール）
        infoInDto.setShochi13Umu(CommonConstants.INT_0);
        // 医療処置（排便コントロール）
        infoInDto.setShochi14Umu(CommonConstants.INT_0);
        // 医療処置（自己注射）
        infoInDto.setShochi15Umu(CommonConstants.INT_0);
        // 医療処置（自己注射）メモ
        infoInDto.setShochi15MemoKnj(CommonConstants.BLANK_STRING);
        // 医療処置（その他）
        infoInDto.setShochi16Umu(CommonConstants.INT_0);
        // 医療処置（その他）メモ
        infoInDto.setShochi16MemoKnj(CommonConstants.BLANK_STRING);
        // 内服薬
        infoInDto.setDrugUmu(CommonConstants.INT_9);
        // 内服薬メモ
        infoInDto.setDrugMemoKnj(CommonConstants.BLANK_STRING);
        // 居宅療養管理指導
        infoInDto.setRyouyouKanriUmu(CommonConstants.INT_9);
        // 居宅療養管理指導メモ
        infoInDto.setRyouyouKanriMemoKnj(CommonConstants.BLANK_STRING);
        // 薬剤管理
        infoInDto.setDrugKanriKbn(CommonConstants.INT_9);
        // 薬剤管理（管理者）
        infoInDto.setDrugKanriKanrisya(CommonConstants.BLANK_STRING);
        // 薬剤管理（管理方法）
        infoInDto.setDrugKanriHouhou(CommonConstants.BLANK_STRING);
        // 服薬状況
        infoInDto.setDrugJyoukyou(CommonConstants.INT_9);
        // お薬に関する、特記事項
        infoInDto.setDrugTokkiKnj(CommonConstants.BLANK_STRING);
        // 医療機関名
        infoInDto.setHospKnj(CommonConstants.BLANK_STRING);
        // 医療機関名フォント
        infoInDto.setHospKnjFont(ReportConstants.FONT_SIZE_10);
        // 電話番号
        infoInDto.setHospTel(CommonConstants.BLANK_STRING);
        // 医師名フリガナ
        infoInDto.setDoctorKana(CommonConstants.BLANK_STRING);
        // 医師名フリガナフォント
        infoInDto.setDoctorKanaFont(ReportConstants.FONT_SIZE_8);
        // 医師名
        infoInDto.setDoctorKnj(CommonConstants.BLANK_STRING);
        // 医師名フォント
        infoInDto.setDoctorKnjFont(ReportConstants.FONT_SIZE_10);
        // 診察方法
        infoInDto.setHospHouhou(CommonConstants.INT_9);
        // 診察頻度（回数）
        infoInDto.setHospKaisu(CommonConstants.BLANK_STRING);
        // 別紙出力1フラグ
        infoInDto.setComnTokkiFlg(CommonConstants.INT_0);
        // 別紙出力2フラグ
        infoInDto.setDrugTokkiFlg(CommonConstants.INT_0);

        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false、U06091_入院時情報提供書②取得
     * 
     * @param printSet    プリンタ設定
     * @param model       システム日付
     * @param printOption 印刷オプション
     * @return PDF帳票パラメータ
     * @throws Exception 例外
     */
    private HospitalizationInfoOffer2ReportServiceInDto getReportParamsFromHospitalizationInfoOffer2(
            ReportCommonPrintSet printSet,
            HospitalizationTimeOfferAllReportParameterModel model,
            HospitalizationTimeOfferPlanPrintOption printOption) throws Exception {
        HospitalizationInfoOffer2ReportServiceInDto infoInDto = new HospitalizationInfoOffer2ReportServiceInDto();

        // 【リクエストパラメータ】.印刷対象履歴リスト
        List<HospitalizationTimeOfferPlanSubjectHistory> printSubjectHistoryList = model
                .getPrintSubjectHistoryList();
        HospitalizationTimeOfferPlanSubjectHistory printSubjectHistory = printSubjectHistoryList.get(0);

        // 指定日（年号）
        ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDateGG(printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(), model.getSystemDate());
        infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
        // 指定日（年）
        infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
        // 指定日（月）
        infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
        // 指定日（日）
        infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

        /*
         * ===============利用者基本情報を取得===============
         * 
         */
        Pair<String, String> userPair = this.getUserNamesPair(
                CommonDtoUtil.strValToInt(printSubjectHistory.getUserId()));
        // 利用者氏名
        infoInDto.setRiyoushaNm(userPair.getLeft()+CommonConstants.SPACE_STRING+userPair.getRight());

        /*
         * ===============敬称処理===============
         * 
         */
        if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getKeishoFlg())) {
            infoInDto.setKeisho(printOption.getKeisho());
        } else if (CommonConstants.STR_TRUE.equalsIgnoreCase(model.getStKeishoFlg())) {
            infoInDto.setKeisho(model.getStKeisho());
        } else {
            infoInDto.setKeisho(CommonConstants.STR_LIKE);
        }

        /*
         * ===============2.入院時情報提供書②情報の取得===============
         * 
         */
        // 2.1. 下記の入院時情報提供書明細情報取得のDAOを利用し、入院時情報提供書②情報を取得する。
        KghCpnNyuTeikyouBackKakuteiPByCriteriaInEntity kghCpnNyuTeikyouBackKakuteiPByCriteriaInEntity = new KghCpnNyuTeikyouBackKakuteiPByCriteriaInEntity();
        // リクエストパラメータ.データ.印刷対象履歴リスト[0].提供ID
        kghCpnNyuTeikyouBackKakuteiPByCriteriaInEntity
                .setAlTeikyouId(CommonDtoUtil.strValToInt(printSubjectHistory.getTeikyouId()));
        List<KghCpnNyuTeikyouBackKakuteiPOutEntity> kghCpnNyuTeikyouBackKakuteiPOutEntities = cpnTucHospInfoTeikyouHeadRelationSelectMapper
                .findKghCpnNyuTeikyouBackKakuteiPByCriteria(kghCpnNyuTeikyouBackKakuteiPByCriteriaInEntity);

        // 2.2. 「2.1.」で取得した入院時情報提供書②情報の件数をワーク入院時情報提供書②情報.総件数に設定する。
        // 2.2.1. 総件数 > 0 件の場合。処理を続ける。
        if (CollectionUtils.isNotEmpty(kghCpnNyuTeikyouBackKakuteiPOutEntities)) {

            KghCpnNyuTeikyouBackKakuteiPOutEntity entity = kghCpnNyuTeikyouBackKakuteiPOutEntities.getFirst();
            // 麻痺の状況
            infoInDto.setMahiKbn(entity.getMahiKbn());
            // 褥瘡の有無
            infoInDto.setJyokusouUmu(entity.getJyokusouUmu());
            // 褥瘡メモ
            infoInDto.setJyokusouMemoKnj(entity.getJyokusouMemoKnj());
            // ADL移動（自立）
            infoInDto.setAdlIdou1(entity.getAdlIdou1());
            // ADL移動（見守り）
            infoInDto.setAdlIdou2(entity.getAdlIdou2());
            // ADL移動（一部介助）
            infoInDto.setAdlIdou3(entity.getAdlIdou3());
            // ADL移動（全介助）
            infoInDto.setAdlIdou4(entity.getAdlIdou4());
            // ADL移動（室内）（杖）
            infoInDto.setAdlIdouShitsunai1(entity.getAdlIdouShitsunai1());
            // ADL移動（室内）（歩行器）
            infoInDto.setAdlIdouShitsunai2(entity.getAdlIdouShitsunai2());
            // ADL移動（室内）（車いす）
            infoInDto.setAdlIdouShitsunai3(entity.getAdlIdouShitsunai3());
            // ADL移動（室内）（その他）
            infoInDto.setAdlIdouShitsunai4(entity.getAdlIdouShitsunai4());
            // ADL移乗（自立）
            infoInDto.setAdlIjyou1(entity.getAdlIjyou1());
            // ADL移乗（見守り）
            infoInDto.setAdlIjyou2(entity.getAdlIjyou2());
            // ADL移乗（一部介助）
            infoInDto.setAdlIjyou3(entity.getAdlIjyou3());
            // ADL移乗（全介助）
            infoInDto.setAdlIjyou4(entity.getAdlIjyou4());
            // ADL移動(屋外)（杖）
            infoInDto.setAdlIdouShudan1(entity.getAdlIdouShudan1());
            // ADL移動(屋外)（歩行器）
            infoInDto.setAdlIdouShudan2(entity.getAdlIdouShudan2());
            // ADL移動(屋外)（車いす）
            infoInDto.setAdlIdouShudan3(entity.getAdlIdouShudan3());
            // ADL移動(屋外)（その他）
            infoInDto.setAdlIdouShudan4(entity.getAdlIdouShudan4());
            // ADL更衣（自立）
            infoInDto.setAdlKoui1(entity.getAdlKoui1());
            // ADL更衣（見守り）
            infoInDto.setAdlKoui2(entity.getAdlKoui2());
            // ADL更衣（一部介助）
            infoInDto.setAdlKoui3(entity.getAdlKoui3());
            // ADL更衣（全介助）
            infoInDto.setAdlKoui4(entity.getAdlKoui4());
            // ADL起居動作（自立）
            infoInDto.setAdlKikyo1(entity.getAdlKikyo1());
            // ADL起居動作（見守り）
            infoInDto.setAdlKikyo2(entity.getAdlKikyo2());
            // ADL起居動作（一部介助）
            infoInDto.setAdlKikyo3(entity.getAdlKikyo3());
            // ADL起居動作（全介助）
            infoInDto.setAdlKikyo4(entity.getAdlKikyo4());
            // ADL整容（自立）
            infoInDto.setAdlSeiyou1(entity.getAdlSeiyou1());
            // ADL整容（見守り）
            infoInDto.setAdlSeiyou2(entity.getAdlSeiyou2());
            // ADL整容（一部介助）
            infoInDto.setAdlSeiyou3(entity.getAdlSeiyou3());
            // ADL整容（全介助）
            infoInDto.setAdlSeiyou4(entity.getAdlSeiyou4());
            // ADL入浴（自立）
            infoInDto.setAdlNyuuyoku1(entity.getAdlNyuuyoku1());
            // ADL入浴（見守り）
            infoInDto.setAdlNyuuyoku2(entity.getAdlNyuuyoku2());
            // ADL入浴（一部介助）
            infoInDto.setAdlNyuuyoku3(entity.getAdlNyuuyoku3());
            // ADL入浴（全介助）
            infoInDto.setAdlNyuuyoku4(entity.getAdlNyuuyoku4());
            // ADL食事（自立）
            infoInDto.setAdlShokuji1(entity.getAdlShokuji1());
            // ADL食事（見守り）
            infoInDto.setAdlShokuji2(entity.getAdlShokuji2());
            // ADL食事（一部介助）
            infoInDto.setAdlShokuji3(entity.getAdlShokuji3());
            // ADL食事（全介助）
            infoInDto.setAdlShokuji4(entity.getAdlShokuji4());
            // 食事回数
            infoInDto.setShokujiCnt(String.valueOf(entity.getShokujiCnt()));
            // 食事回数（朝）
            infoInDto.setShokujiCntMorn(String.valueOf(entity.getShokujiCntMorn()));
            // 食事回数（昼）
            infoInDto.setShokujiCntNoon(String.valueOf(entity.getShokujiCntNoon()));
            // 食事回数（夜）
            infoInDto.setShokujiCntEven(String.valueOf(entity.getShokujiCntEven()));
            // 食事制限
            infoInDto.setShokujiSeigen(entity.getShokujiSeigen());
            // 食事制限メモ
            infoInDto.setShokujiSeigenMemoKnj(ReportUtil.nullToEmpty(entity.getShokujiSeigenMemoKnj()));
            // 食事形態（普通）
            infoInDto.setShokujiKeitai1(entity.getShokujiKeitai1());
            // 食事形態（きざみ）
            infoInDto.setShokujiKeitai2(entity.getShokujiKeitai2());
            // 食事形態（嚥下障害食）
            infoInDto.setShokujiKeitai3(entity.getShokujiKeitai3());
            // 食事形態（ミキサー）
            infoInDto.setShokujiKeitai4(entity.getShokujiKeitai4());
            // UDF等の食形態区分
            infoInDto.setUdfShokujiKeitaiKbn(ReportUtil.nullToEmpty(entity.getUdfShokujiKeitaiKbn()));
            // 摂取方法（経口）
            infoInDto.setSesshuHouhou1(entity.getSesshuHouhou1());
            // 摂取方法（経管栄養）
            infoInDto.setSesshuHouhou2(entity.getSesshuHouhou2());
            // 水分とろみ
            infoInDto.setSuibunToromi(entity.getSuibunToromi());
            // 水分制限
            infoInDto.setSuibunSeigen(entity.getSuibunSeigen());
            // 水分制限メモ
            infoInDto.setSuibunSeigenMemoKnj(ReportUtil.nullToEmpty(entity.getSuibunSeigenMemoKnj()));
            // 嚥下機能
            infoInDto.setEngeUmu(entity.getEngeUmu());
            // 義歯
            infoInDto.setGishiUmu(entity.getGishiUmu());
            // 義歯区分
            infoInDto.setGishiKbn(entity.getGishiKbn());
            // 口腔清潔
            infoInDto.setKoukuuCare(entity.getKoukuuCare());
            // 口臭
            infoInDto.setKoushuuUmu(entity.getKoushuuUmu());
            // 排尿（自立）
            infoInDto.setHainyou1(entity.getHainyou1());
            // 排尿（見守り）
            infoInDto.setHainyou2(entity.getHainyou2());
            // 排尿（一部介助）
            infoInDto.setHainyou3(entity.getHainyou3());
            // 排尿（全介助）
            infoInDto.setHainyou4(entity.getHainyou4());
            // ポータブルトイレ
            infoInDto.setPortableToiletUmu(entity.getPortableToiletUmu());
            // 排便（自立）
            infoInDto.setHaiben1(entity.getHaiben1());
            // 排便（見守り）
            infoInDto.setHaiben2(entity.getHaiben2());
            // 排便（一部介助）
            infoInDto.setHaiben3(entity.getHaiben3());
            // 排便（全介助）
            infoInDto.setHaiben4(entity.getHaiben4());
            // オムツ/パッド
            infoInDto.setOmutsuPadUmu(entity.getOmutsuPadUmu());
            // 睡眠の状態
            infoInDto.setSleepJyoutai(entity.getSleepJyoutai());
            // 睡眠の状態メモ
            infoInDto.setSleepMemoKnj(ReportUtil.nullToEmpty(entity.getSleepMemoKnj()));
            // 眠剤の使用
            infoInDto.setSleepDrugUmu(entity.getSleepDrugUmu());
            // 喫煙有無
            infoInDto.setSmokingUmu(entity.getSmokingUmu());
            // 喫煙量
            infoInDto.setSmoking(ReportUtil.nullToEmpty(entity.getSmoking()));
            // 飲酒有無
            infoInDto.setAlcoholUmu(entity.getAlcoholUmu());
            // 飲酒量
            infoInDto.setAlcohol(ReportUtil.nullToEmpty(entity.getAlcohol()));
            // 飲酒量フォント
            infoInDto.setAlcoholFontSize(
                    ReportUtil.getByteLength(entity.getAlcohol()) > 20 ? ReportConstants.FONT_SIZE_7
                            : ReportConstants.FONT_SIZE_10);
            // 視力
            infoInDto.setEyesightKbn(entity.getEyesightKbn());
            // 眼鏡
            infoInDto.setGlassesUmu(entity.getGlassesUmu());
            // 眼鏡メモ
            infoInDto.setGlassesMemoKnj(ReportUtil.nullToEmpty(entity.getGlassesMemoKnj()));
            // 聴力
            infoInDto.setHearingKbn(entity.getHearingKbn());
            // 補聴器
            infoInDto.setHearingAidUmu(entity.getHearingAidUmu());
            // 言語
            infoInDto.setLanguageAbility(entity.getLanguageAbility());
            // 意思疎通
            infoInDto.setComnKbn(entity.getComnKbn());
            // 精神面における療養上の問題（なし）
            infoInDto.setRyouyou1Umu(entity.getRyouyou1Umu());
            // 精神面における療養上の問題（幻視幻聴）
            infoInDto.setRyouyou2Umu(entity.getRyouyou2Umu());
            // 精神面における療養上の問題（興奮）
            infoInDto.setRyouyou3Umu(entity.getRyouyou3Umu());
            // 精神面における療養上の問題（焦燥不穏）
            infoInDto.setRyouyou4Umu(entity.getRyouyou4Umu());
            // 精神面における療養上の問題（妄想）
            infoInDto.setRyouyou5Umu(entity.getRyouyou5Umu());
            // 精神面における療養上の問題（暴力/攻撃性）
            infoInDto.setRyouyou6Umu(entity.getRyouyou6Umu());
            // 精神面における療養上の問題（介護への抵抗）
            infoInDto.setRyouyou7Umu(entity.getRyouyou7Umu());
            // 精神面における療養上の問題（不眠）
            infoInDto.setRyouyou8Umu(entity.getRyouyou8Umu());
            // 精神面における療養上の問題（昼夜逆転）
            infoInDto.setRyouyou9Umu(entity.getRyouyou9Umu());
            // 精神面における療養上の問題（徘徊）
            infoInDto.setRyouyou10Umu(entity.getRyouyou10Umu());
            // 精神面における療養上の問題（危険行為）
            infoInDto.setRyouyou11Umu(entity.getRyouyou11Umu());
            // 精神面における療養上の問題（不潔行為）
            infoInDto.setRyouyou12Umu(entity.getRyouyou12Umu());
            // 精神面における療養上の問題（その他）
            infoInDto.setRyouyou13Umu(entity.getRyouyou13Umu());
            // 精神面における療養上の問題（その他メモ）
            infoInDto.setRyouyouMemoKnj(ReportUtil.nullToEmpty(entity.getRyouyouMemoKnj()));
            // 疾患歴（なし）
            infoInDto.setSick1Umu(entity.getSick1Umu());
            // 疾患歴（悪性腫瘍）
            infoInDto.setSick2Umu(entity.getSick2Umu());
            // 疾患歴（認知症）
            infoInDto.setSick3Umu(entity.getSick3Umu());
            // 疾患歴（急性呼吸器感染症）
            infoInDto.setSick4Umu(entity.getSick4Umu());
            // 疾患歴（脳血管障害）
            infoInDto.setSick5Umu(entity.getSick5Umu());
            // 疾患歴（骨折）
            infoInDto.setSick6Umu(entity.getSick6Umu());
            // 疾患歴（その他）
            infoInDto.setSick7Umu(entity.getSick7Umu());
            // 疾患歴（その他）メモ
            infoInDto.setSickMemoKnj(ReportUtil.nullToEmpty(entity.getSickMemoKnj()));
            // 最近半年間での入院
            infoInDto.setNyuuinUmu(entity.getNyuuinUmu());
            // 入院理由
            infoInDto.setNyuuinRiyu(ReportUtil.nullToEmpty(entity.getNyuuinRiyu()));
            // 最近半年間での入院開始日（年号）
            List<String> startDateParts = ReportUtil
                    .getLocalDateToJapanDateTimeFormat(entity.getNyuuinStartYmd().replace(CommonConstants.STR_DELIMITER, CommonConstants.AS_OP_SUB));
            infoInDto.setNyuuinStartDateGG(startDateParts.get(ReportConstants.SHITEIDATE_GG));
            // 最近半年間での入院開始日（年）
            infoInDto.setNyuuinStartDateYY(startDateParts.get(ReportConstants.SHITEIDATE_YY));
            // 最近半年間での入院開始日（月）
            infoInDto.setNyuuinStartDateMM(startDateParts.get(ReportConstants.SHITEIDATE_MM));
            // 最近半年間での入院開始日（日）
            infoInDto.setNyuuinStartDateDD(startDateParts.get(ReportConstants.SHITEIDATE_DD));
            // 最近半年間での入院終了日（年号）
            List<String> endDateParts = ReportUtil
                    .getLocalDateToJapanDateTimeFormat(entity.getNyuuinEndYmd().replace(CommonConstants.STR_DELIMITER, CommonConstants.AS_OP_SUB));
            infoInDto.setNyuuinEndDateGG(endDateParts.get(ReportConstants.SHITEIDATE_GG));
            // 最近半年間での入院終了日（年）
            infoInDto.setNyuuinEndDateYY(endDateParts.get(ReportConstants.SHITEIDATE_YY));
            // 最近半年間での入院終了日（月）
            infoInDto.setNyuuinEndDateMM(endDateParts.get(ReportConstants.SHITEIDATE_MM));
            // 最近半年間での入院終了日（日）
            infoInDto.setNyuuinEndDateDD(endDateParts.get(ReportConstants.SHITEIDATE_DD));
            // 入院頻度
            infoInDto.setNyuuinHindo(entity.getNyuuinHindo());
            // 医療処置（なし）
            infoInDto.setShochi1Umu(entity.getShochi1Umu());
            // 医療処置（点滴）
            infoInDto.setShochi2Umu(entity.getShochi2Umu());
            // 医療処置（酸素療法）
            infoInDto.setShochi3Umu(entity.getShochi3Umu());
            // 医療処置（喀痰吸引）
            infoInDto.setShochi4Umu(entity.getShochi4Umu());
            // 医療処置（気管切開）
            infoInDto.setShochi5Umu(entity.getShochi5Umu());
            // 医療処置（胃ろう）
            infoInDto.setShochi6Umu(entity.getShochi6Umu());
            // 医療処置（経鼻栄養）
            infoInDto.setShochi7Umu(entity.getShochi7Umu());
            // 医療処置（経腸栄養）
            infoInDto.setShochi8Umu(entity.getShochi8Umu());
            // 医療処置（褥瘡）
            infoInDto.setShochi9Umu(entity.getShochi9Umu());
            // 医療処置（尿道カテーテル）
            infoInDto.setShochi10Umu(entity.getShochi10Umu());
            // 医療処置（尿路ストーマ）
            infoInDto.setShochi11Umu(entity.getShochi11Umu());
            // 医療処置（消化管ストーマ）
            infoInDto.setShochi12Umu(entity.getShochi12Umu());
            // 医療処置（痛みコントロール）
            infoInDto.setShochi13Umu(entity.getShochi13Umu());
            // 医療処置（排便コントロール）
            infoInDto.setShochi14Umu(entity.getShochi14Umu());
            // 医療処置（自己注射）
            infoInDto.setShochi15Umu(entity.getShochi15Umu());
            // 医療処置（自己注射）メモ
            infoInDto.setShochi15MemoKnj(ReportUtil.nullToEmpty(entity.getShochi15MemoKnj()));
            // 医療処置（その他）
            infoInDto.setShochi16Umu(entity.getShochi16Umu());
            // 医療処置（その他）メモ
            infoInDto.setShochi16MemoKnj(ReportUtil.nullToEmpty(entity.getShochi16MemoKnj()));
            // 内服薬
            infoInDto.setDrugUmu(entity.getDrugUmu());
            // 内服薬メモ
            infoInDto.setDrugMemoKnj(ReportUtil.nullToEmpty(entity.getDrugMemoKnj()));
            // 居宅療養管理指導
            infoInDto.setRyouyouKanriUmu(entity.getRyouyouKanriUmu());
            // 居宅療養管理指導メモ
            infoInDto.setRyouyouKanriMemoKnj(ReportUtil.nullToEmpty(entity.getRyouyouKanriMemoKnj()));
            // 薬剤管理
            infoInDto.setDrugKanriKbn(entity.getDrugKanriKbn());
            // 薬剤管理（管理者）
            infoInDto.setDrugKanriKanrisya(ReportUtil.nullToEmpty(entity.getDrugKanriKanrisya()));
            // 薬剤管理（管理方法）
            infoInDto.setDrugKanriHouhou(ReportUtil.nullToEmpty(entity.getDrugKanriHouhou()));
            // 服薬状況
            infoInDto.setDrugJyoukyou(entity.getDrugJyoukyou());
            // 医療機関名
            infoInDto.setHospKnj(ReportUtil.nullToEmpty(entity.getHospKnj()));
            // 医療機関名フォント
            infoInDto.setHospKnjFont(
                    ReportUtil.getByteLength(entity.getHospKnj()) > 36 ? ReportConstants.FONT_SIZE_7
                            : ReportConstants.FONT_SIZE_10);
            // 電話番号
            infoInDto.setHospTel(ReportUtil.nullToEmpty(entity.getHospTel()));
            // 医師名フリガナ
            infoInDto.setDoctorKana(ReportUtil.nullToEmpty(entity.getDoctorKana()));
            // 医師名フリガナフォント
            infoInDto.setDoctorKanaFont(
                    ReportUtil.getByteLength(entity.getDoctorKana()) > 34 ? ReportConstants.FONT_SIZE_7
                            : ReportConstants.FONT_SIZE_8);
            // 医師名
            infoInDto.setDoctorKnj(ReportUtil.nullToEmpty(entity.getDoctorKnj()));
            // 医師名フォント
            infoInDto.setDoctorKnjFont(
                    ReportUtil.getByteLength(entity.getDoctorKnj()) > 30 ? ReportConstants.FONT_SIZE_7
                            : ReportConstants.FONT_SIZE_10);
            // 診察方法
            infoInDto.setHospHouhou(entity.getHospHouhou());
            // 診察頻度（回数）
            infoInDto.setHospKaisu(String.valueOf(entity.getHospKaisu()));
            // コミュニケーションに関する特記事項
            infoInDto.setComnTokkiKnj(ReportUtil.nullToEmpty(entity.getComnTokkiKnj()));
            // お薬に関する、特記事項
            infoInDto.setDrugTokkiKnj(ReportUtil.nullToEmpty(entity.getDrugTokkiKnj()));
            // リクエストパラメータ.データ.印刷オプション.別紙に印刷するフラグ＝trueの場合
            Integer comnTokkiFlg = CommonConstants.INT_0;
            Integer drugTokkiFlg = CommonConstants.INT_0;
            if (CommonConstants.STR_TRUE.equalsIgnoreCase(printOption.getBesshiFlg())) {
                // コミュニケーションに関する特記事項の有効な行数
                Pair<Integer, List<String>> pairComnTokki = kghCmpF01Logic.cmpLtext2Rows(entity.getComnTokkiKnj(), 50);
                if (pairComnTokki.getLeft() > 2) {
                    pairComnTokki = kghCmpF01Logic.cmpLtext2Rows(entity.getComnTokkiKnj(), 102);
                    comnTokkiFlg = CommonConstants.INT_1;
                    List<String> lstComnTokki = pairComnTokki.getRight();
                    String comnTokkiKnj = CommonConstants.EMPTY_STRING;
                    if (!CollectionUtils.isNullOrEmpty(lstComnTokki)) {
                        for (int i = 0; i < lstComnTokki.size(); i++) {
                            String str = lstComnTokki.get(i);
                            if (i == lstComnTokki.size() - 1) {
                                comnTokkiKnj += str;
                            } else {
                                comnTokkiKnj += str + ReportConstants.LINE_BREAKS;
                            }
                        }
                    }
                    infoInDto.setComnTokkiKnj(comnTokkiKnj);
                }
                // お薬に関する、特記事項の有効な行数
                Pair<Integer, List<String>> pairDrugTokki = kghCmpF01Logic.cmpLtext2Rows(entity.getDrugTokkiKnj(), 82);
                if (pairDrugTokki.getLeft() > 3) {
                    pairDrugTokki = kghCmpF01Logic.cmpLtext2Rows(entity.getDrugTokkiKnj(), 102);
                    drugTokkiFlg = CommonConstants.INT_1;
                    List<String> lstDrugTokki = pairDrugTokki.getRight();
                    String drugTokkiKnj = CommonConstants.EMPTY_STRING;
                    if (!CollectionUtils.isNullOrEmpty(lstDrugTokki)) {
                        for (int i = 0; i < lstDrugTokki.size(); i++) {
                            String str = lstDrugTokki.get(i);
                            if (i == lstDrugTokki.size() - 1) {
                                drugTokkiKnj += str;
                            } else {
                                drugTokkiKnj += str + ReportConstants.LINE_BREAKS;
                            }
                        }
                    }
                    infoInDto.setDrugTokkiKnj(drugTokkiKnj);
                }
            }
            infoInDto.setComnTokkiFlg(comnTokkiFlg);
            infoInDto.setDrugTokkiFlg(drugTokkiFlg);
        }
        return infoInDto;
    }

    /**
     * 指定日を取得する
     * 
     * @param shiTeiKubun 指定日印刷区分
     * @param shiTeiDate  西暦日付
     * @param systemDate  システム日付
     * @throws Exception 例外
     */
    private ReportCommonShiTeiDateParts getShiTeiDateGG(String shiTeiKubun, String shiTeiDate, String systemDate) {
        // 指定日（年号）
        String shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（年）
        String shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（月）
        String shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（日）
        String shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日印刷区分判定
        switch (shiTeiKubun) {
            case ReportConstants.PRINTDATE_KBN_SHINAI:
                // 指定日印刷区分 = 1 の場合
                // 指定日（年号）
                shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（年）
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            case ReportConstants.PRINTDATE_KBN_PRINT:
                // 指定日印刷区分 = 2 の場合
                // 西暦日付を和暦日付に変換する
                List<String> dateParts = ReportUtil.getLocalDateToJapanDateTimeFormat(shiTeiDate);
                shiTeiDateGG = dateParts.get(ReportConstants.SHITEIDATE_GG);
                // 指定日（年）
                shiTeiDateYY = dateParts.get(ReportConstants.SHITEIDATE_YY);
                // 指定日（月）
                shiTeiDateMM = dateParts.get(ReportConstants.SHITEIDATE_MM);
                // 指定日（日）
                shiTeiDateDD = dateParts.get(ReportConstants.SHITEIDATE_DD);
                break;
            case ReportConstants.PRINTDATE_KBN_BLANKDATE:
                // 指定日印刷区分 = 3 の場合
                // 指定日の空欄表示処理
                DateTimeFormatter inputFormatter = DateTimeFormatter
                        .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
                DateTimeFormatter outputFormatter = DateTimeFormatter
                        .ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
                LocalDateTime dateTime = LocalDateTime.parse(systemDate, inputFormatter);
                String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
                // 指定日（年号）
                shiTeiDateGG = blankDate.substring(0, 2);
                // 指定日（年）
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            default:
                // 指定日（年号）
                shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（年）
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
        }
        return new ReportCommonShiTeiDateParts(shiTeiDateGG, shiTeiDateYY, shiTeiDateMM, shiTeiDateDD);
    }

    /**
     * 日付を取得する
     * 
     * @param shiTeiDate 西暦日付
     * @throws Exception 例外
     */
    private ReportCommonDateParts getDate(String date) {
        // 日付（年号）=""
        String dateGG = CommonConstants.FULL_WIDTH_SPACE;
        // 日付（年）=""
        String dateYY = CommonConstants.FULL_WIDTH_SPACE;
        // 日付（月）=""
        String dateMM = CommonConstants.FULL_WIDTH_SPACE;
        // 日付（日）=""
        String dateDD = CommonConstants.FULL_WIDTH_SPACE;
        if (StringUtils.isNotEmpty(date)) {
            dateGG = date.substring(CommonConstants.INT_0, CommonConstants.INT_2);
            dateYY = date.substring(CommonConstants.INT_2, CommonConstants.INT_4);
            dateMM = date.substring(CommonConstants.INT_5, CommonConstants.INT_7);
            dateDD = date.substring(CommonConstants.INT_8, CommonConstants.INT_10);
        }
        return new ReportCommonDateParts(dateGG, dateYY, dateMM, dateDD);
    }

    /**
     * 利用者基本情報を取得する。
     * 
     * @param userId 利用者ID
     * @return 氏名情報
     */
    public Pair<String, String> getUserNamesPair(Integer userId) {
        String name1Knj = CommonConstants.BLANK_STRING;
        String name2Knj = CommonConstants.BLANK_STRING;
        UserinfoByCriteriaInEntity userinfoByCriteriaInEntity = new UserinfoByCriteriaInEntity();
        userinfoByCriteriaInEntity.setUser(userId);
        List<UserinfoOutEntity> userinfoOutList = comTucUserSelectMapper
                .findUserinfoByCriteria(userinfoByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(userinfoOutList)) {
            // 氏名（姓）
            name1Knj = ReportUtil.nullToEmpty(userinfoOutList.get(0).getName1Knj());
            // 氏名（名）
            name2Knj = ReportUtil.nullToEmpty(userinfoOutList.get(0).getName2Knj());
        }

        return Pair.of(name1Knj, name2Knj);
    }
}
