package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.service.dto.PreventionEvaluationTableMasterUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PreventionEvaluationTableMasterUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDaoUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkSsmMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsm;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsmCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * @since 2025.05.22
 * <AUTHOR>
 * @implNote GUI01241_予防評価表マスタデータ保存サービス
 * 
 */
@Service
public class PreventionEvaluationTableMasterUpdateServiceImpl extends
        UpdateServiceImpl<PreventionEvaluationTableMasterUpdateServiceInDto, PreventionEvaluationTableMasterUpdateServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 初期設定マスタ情報取得DAO */
    @Autowired
    private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;
    /** 週間表マスタ情報保存 */
    @Autowired
    private KghMocKrkSsmMapper kghMocKrkSsmMapper;
    Integer count = 0;
    /**
     * 予防評価表マスタデータ保存
     * 
     * @param inDto 予防評価表マスタデータ保存サービス入力Dto
     * @return ［予防評価表マスタデータ保存サービス出力DTO
     */
    @Override
    protected PreventionEvaluationTableMasterUpdateServiceOutDto mainProcess(
            final PreventionEvaluationTableMasterUpdateServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        // 更新件数
        // 戻り値初期化
        PreventionEvaluationTableMasterUpdateServiceOutDto outDto = new PreventionEvaluationTableMasterUpdateServiceOutDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. バリデーションチェックを行う。===============
         * 
         */
        // 特になし
        /*
         * ===============3. 予防評価表マスタ保存処理===============
         * 
         */

        // DAOを実行
        List<KrkSsmInfoOutEntity> krkSsmInfoList = selectKrkSsmInfo(inDto);
        // 3.2. 上記「3.1.」で取得した予防評価表マスタ情報がNULLの場合、【00-00 初期設定マスタ】情報を登録する。
        if (CollectionUtils.isEmpty(krkSsmInfoList)) {
            insertKrkSsmInfo(inDto);
        } else {
            // 3.3. 上記以外の場合、【00-00 初期設定マスタ】情報を更新する。
            count=updateKrkSsmInfo(inDto);
            if (0 >= count) {
                LOG.info(Constants.END);
                throw new ExclusiveException();
            }
        }
        outDto.setResult(count > 0 ? "1" : "0");
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 予防評価表マスタ情報を取得する。
     *
     * @param inDto アセスメント(パッケージプラン)のデータ保存入力DTO
     * @return 予防評価表マスタ情報
     */
    private List<KrkSsmInfoOutEntity> selectKrkSsmInfo(PreventionEvaluationTableMasterUpdateServiceInDto inDto) {
        KrkSsmInfoByCriteriaInEntity krkSsmInfoByCriteriaInEntity = new KrkSsmInfoByCriteriaInEntity();
        // 施設ID ← リクエストパラメータ.施設ID
        krkSsmInfoByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID ← リクエストパラメータ.事業所ID
        krkSsmInfoByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 分類1 ← 2
        krkSsmInfoByCriteriaInEntity.setBunrui1Id(CommonConstants.BUNRUI1_ID_2);
        // 分類2 ← 25
        krkSsmInfoByCriteriaInEntity.setBunrui2Id(CommonConstants.NUMBER_25);

        return this.kghMocKrkSsmSelectMapper
                .findKrkSsmInfoByCriteria(krkSsmInfoByCriteriaInEntity);
    }

    /**
     * 【00-00 初期設定マスタ】情報を登録する。
     *
     * @param inDto アセスメント(パッケージプラン)のデータ保存入力DTO
     * @return 予防評価表マスタ情報
     */
    private void insertKrkSsmInfo(PreventionEvaluationTableMasterUpdateServiceInDto inDto) {
        KghMocKrkSsm createItem = new KghMocKrkSsm();
        // 施設ID←リクエストパラメータ.施設ID
        createItem.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID←リクエストパラメータ.事業所ID
        createItem.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 分類1←2
        createItem.setBunrui1Id(CommonConstants.BUNRUI1_ID_2);
        // 分類2←25
        createItem.setBunrui2Id(CommonConstants.NUMBER_25);
        // 分類3←2
        createItem.setBunrui3Id(CommonConstants.NUMBER_2);
        // 整数←リクエストパラメータ.印刷時の文字サイズ
        createItem.setIntValue(CommonDtoUtil.strValToInt(inDto.getIntValue()));
        // 小数点付←0
        createItem.setDoubleValue(Double.valueOf(CommonConstants.NUMBER_0));
        // 文字列1←NULL
        createItem.setText1Knj(null);
        // 登録時の共通カラム値設定処理
        CommonDaoUtil.setInsertCommonColumns(createItem);
        count=kghMocKrkSsmMapper.insertSelective(createItem);
    }

    /**
     * 【00-00 初期設定マスタ】情報を更新更新する。
     *
     * @param inDto アセスメント(パッケージプラン)のデータ保存入力DTO
     * @return 更新結果
     */
    private int updateKrkSsmInfo(PreventionEvaluationTableMasterUpdateServiceInDto inDto) {
        // 更新内容パータン
        KghMocKrkSsm updItem = new KghMocKrkSsm();
        // 整数←リクエストパラメータ.印刷時の文字サイズ
        updItem.setIntValue(CommonDtoUtil.strValToInt(inDto.getIntValue()));
        // 更新条件キー
        KghMocKrkSsmCriteria whereItem = new KghMocKrkSsmCriteria();
        // 施設ID＝リクエストパラメータ.施設ID
        // 事業者ID＝リクエストパラメータ.事業所ID
        // 分類1＝2
        // 分類2＝25
        // 分類3＝2
        // 削除フラグ = 0（未削除データ）
        // 更新回数=リクエストパラメータ.更新回数
        whereItem.createCriteria().andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()))
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                .andBunrui1IdEqualTo(CommonConstants.BUNRUI1_ID_2)
                .andBunrui2IdEqualTo(CommonConstants.NUMBER_25)
                .andBunrui3IdEqualTo(CommonConstants.NUMBER_2);
        // 3.3. 上記以外の場合、【00-00 初期設定マスタ】情報を更新する。
        return kghMocKrkSsmMapper.updateByCriteriaSelective(updItem, whereItem);
    }
}
