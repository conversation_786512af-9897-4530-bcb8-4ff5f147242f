package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01023CreationPeriodIDInfoOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01023IssueContentSelectOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01023IssueFormatInfoOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01023PlanPeriodInfoOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.IssueSummaryImportRetrievalSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.IssueSummaryImportRetrievalSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMucKssYoushiki1YoushikiKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMucKssYoushiki1YoushikiKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucKss1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucKss1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnKssMitosiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnKssMitosiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkComKikanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkComKikanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinFullNameByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinFullNameOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucKssYoushiki1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucKss1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucKss3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.SelectPeriodSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.04.24
 * <AUTHOR> Bui The Khang
 * @implNote GUI01023_課題整理総括取込画面
 */
@Service
public class IssueSummaryImportRetrievalSelectServiceImpl extends
        SelectServiceImpl<IssueSummaryImportRetrievalSelectServiceInDto, IssueSummaryImportRetrievalSelectServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private SelectPeriodSelectMapper selectPeriodSelectMapper;

    @Autowired
    private CpnTucKss1SelectMapper cpnTucKss1SelectMapper;

    @Autowired
    private CpnMucKssYoushiki1SelectMapper cpnMucKssYoushiki1SelectMapper;

    @Autowired
    private CpnTucKss3SelectMapper cpnTucKss3SelectMapper;

    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /**
     * 課題整理総括取込画面
     * 
     * @param inDto IssueSummaryImportRetrievalSelectServiceInDto.
     * @return IssueSummaryImportRetrievalSelectServiceOutDto
     * @throws Exception
     */
    @Override
    protected IssueSummaryImportRetrievalSelectServiceOutDto mainProcess(
            IssueSummaryImportRetrievalSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        IssueSummaryImportRetrievalSelectServiceOutDto dto = new IssueSummaryImportRetrievalSelectServiceOutDto();
        // 1.単項目チェック以外の入力チェック
        // 2. 計画書2課題整理総括取込初期情報の取得
        // ＊１ 共通情報.計画期間管理フラグの値が「管理する/管理しない」いずれであっても、計画書2課題整理総括取込初期情報の取得は当該フラグとは関係ありません。
        // ＊２
        // 現行システムには「KirokuKyotuKikanInfoSelectMapper」DAO定義書のシート「findKirokuKyotuKikanInfoByCriteria」などには「法人ID」の記載がありませんが、
        // 厳密に言えば、検索条件には「法人ID」を追加すべきだと思ってます。
        // ただし、計画書2課題整理総括画面機能で使用している全部テーブルの関連条件において、「法人ID」検索条件は行われていないため、
        // おそらくそのような業務上のケースは発生しないことを前提としているため、制限が設けられていないのかもしれません。本件については仕様変更は不要と判断されます。
        // 「補足資料」をご確認ください。

        // 2.1.1 下記の記録共通期間情報取得のDAOを利用し、作成期間ID情報リストを取得する。
        KrkComKikanByCriteriaInEntity krkComKikanByCriteriaInEntity = new KrkComKikanByCriteriaInEntity();
        // 事業者ID
        krkComKikanByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        krkComKikanByCriteriaInEntity.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 種別ID
        krkComKikanByCriteriaInEntity.setSyubetsuId(CommonDtoUtil.strValToInt(inDto.getSyubetsuId()));
        // 施設ID
        krkComKikanByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        List<KrkComKikanOutEntity> kyotuKikanInfoOutEntities = selectPeriodSelectMapper
                .findKrkComKikanByCriteria(krkComKikanByCriteriaInEntity);

        // 作成期間ID情報リスト
        List<Gui01023CreationPeriodIDInfoOutDto> creationPeriodIDInfoSelectList = new ArrayList<>();
        // 計画期間情報リスト
        List<Gui01023PlanPeriodInfoOutDto> planPeriodInfoSelectList = new ArrayList<>();
        // 課題整理総括様式情報リスト
        List<Gui01023IssueFormatInfoOutDto> issueFormatInfoSelect = new ArrayList<>();
        // 課題内容情報リスト
        List<Gui01023IssueContentSelectOutDto> issueContentSelect = new ArrayList<>();

        // 2.1.2 期間IDごとに、開始日、終了日を結合して、和暦で画面に表示
        // API定義の処理「2.1.1」■Output
        for (KrkComKikanOutEntity kikanInfoOutEntity : kyotuKikanInfoOutEntities) {
            Gui01023CreationPeriodIDInfoOutDto dtoIdInfoOutDto = new Gui01023CreationPeriodIDInfoOutDto();
            // 期間ID
            dtoIdInfoOutDto.setSc1Id(CommonDtoUtil.objValToString(kikanInfoOutEntity.getSc1Id()));
            // 法人ID
            dtoIdInfoOutDto.setHoujinId(CommonDtoUtil.objValToString(kikanInfoOutEntity.getHoujinId()));
            // 施設ID
            dtoIdInfoOutDto.setShisetuId(CommonDtoUtil.objValToString(kikanInfoOutEntity.getShisetuId()));
            // 事業者ID
            dtoIdInfoOutDto.setSvJigyoId(CommonDtoUtil.objValToString(kikanInfoOutEntity.getSvJigyoId()));
            // 利用者ID
            dtoIdInfoOutDto.setUserId(CommonDtoUtil.objValToString(kikanInfoOutEntity.getUserId()));
            // 種別ID
            dtoIdInfoOutDto.setSyubetsuId(CommonDtoUtil.objValToString(kikanInfoOutEntity.getSyubetsuId()));
            // 開始日
            dtoIdInfoOutDto.setStartYmd(kikanInfoOutEntity.getStartYmd());
            // 終了日
            dtoIdInfoOutDto.setEndYmd(kikanInfoOutEntity.getEndYmd());
            // 期間内履歴数
            dtoIdInfoOutDto.setRecordsCnt(CommonConstants.STR_0);
            // 更新回数
            // dtoIdInfoOutDto.setModifiedCnt(CommonDtoUtil.objValToString(kikanInfoOutEntity.getKikanModifiedCnt()));

            creationPeriodIDInfoSelectList.add(dtoIdInfoOutDto);
            // 2.1.2 期間IDごとに、開始日、終了日を結合して、和暦で画面に表示

            // 2.2.1 下記の課題整理総括 ヘッダ情報取得のDAOを利用し、計画期間情報リストを取得する。
            CpnTucKss1ByCriteriaInEntity cpnTucKss1ByCriteriaInEntity = new CpnTucKss1ByCriteriaInEntity();
            // 取得した作成期間ID情報リスト.期間ID
            cpnTucKss1ByCriteriaInEntity.setKss1Id(kikanInfoOutEntity.getSc1Id());
            List<CpnTucKss1OutEntity> cpnTucKss1OutEntities = cpnTucKss1SelectMapper
                    .findCpnTucKss1ByCriteria(cpnTucKss1ByCriteriaInEntity);
            // 2.2.3 ソート順：開始日 降順、期間履歴数 降順
            // API定義の処理「2.2.1」■Output
            for (CpnTucKss1OutEntity cpnTucKss1OutEntity : cpnTucKss1OutEntities) {
                ShokuinFullNameByCriteriaInEntity shokuinFullNameByCriteriaIn = new ShokuinFullNameByCriteriaInEntity();
                // 職員ID
                shokuinFullNameByCriteriaIn.setAlShoku(CommonDtoUtil.objValToString(cpnTucKss1OutEntity.getShokuId()));
                // 2.2.1.2 職員基本情報取得のDAOを利用し、職員情報を取得する。
                List<ShokuinFullNameOutEntity> shokuinFullNameOutList = comMscShokuinSelectMapper
                        .findShokuinFullNameByCriteria(shokuinFullNameByCriteriaIn);

                Gui01023PlanPeriodInfoOutDto gui01023PlanPeriodInfoOutDto = new Gui01023PlanPeriodInfoOutDto();
                // ヘッダID
                gui01023PlanPeriodInfoOutDto.setKss1Id(
                        CommonDtoUtil.objValToString(cpnTucKss1OutEntity.getKss1Id()));
                // 計画期間ID
                gui01023PlanPeriodInfoOutDto
                        .setSc1Id(CommonDtoUtil.objValToString(cpnTucKss1OutEntity.getSc1Id()));
                // 法人ID
                gui01023PlanPeriodInfoOutDto
                        .setHoujinId(CommonDtoUtil
                                .objValToString(cpnTucKss1OutEntity.getHoujinId()));
                // 施設ID
                gui01023PlanPeriodInfoOutDto
                        .setShisetuId(CommonDtoUtil
                                .objValToString(cpnTucKss1OutEntity.getShisetuId()));
                // 事業者ID
                gui01023PlanPeriodInfoOutDto
                        .setSvJigyoId(CommonDtoUtil
                                .objValToString(cpnTucKss1OutEntity.getSvJigyoId()));
                // 利用者ID
                gui01023PlanPeriodInfoOutDto.setUserid(
                        CommonDtoUtil.objValToString(cpnTucKss1OutEntity.getUserid()));
                // 作成日
                gui01023PlanPeriodInfoOutDto.setCreateYmd(cpnTucKss1OutEntity.getCreateYmd());
                // 職員ID
                gui01023PlanPeriodInfoOutDto.setShokuId(
                        CommonDtoUtil.objValToString(cpnTucKss1OutEntity.getShokuId()));
                // 様式
                gui01023PlanPeriodInfoOutDto
                        .setYoushikiId(CommonDtoUtil
                                .objValToString(cpnTucKss1OutEntity.getYoushikiId()));
                // 作成者氏名
                if (shokuinFullNameOutList.size() > CommonConstants.NUMBER_0) {
                    gui01023PlanPeriodInfoOutDto.setShokuName(shokuinFullNameOutList.getFirst().getShokuin1Knj()
                            + CommonConstants.SPACE_STRING + shokuinFullNameOutList.getFirst().getShokuin2Knj());
                }
                // 更新回数
                // gui01023PlanPeriodInfoOutDto
                // .setModifiedCnt(CommonDtoUtil
                // .objValToString(cpnTucKss1OutEntity.getModifiedCnt()));
                planPeriodInfoSelectList.add(gui01023PlanPeriodInfoOutDto);

                // 2.4.1 下記の課題整理総括 データ（見通し）取得のDAOを利用し、課題内容情報を取得する。
                KghCpnKssMitosiByCriteriaInEntity kghCpnKssMitosiByCriteriaInEntity = new KghCpnKssMitosiByCriteriaInEntity();
                kghCpnKssMitosiByCriteriaInEntity.setKss1Id(cpnTucKss1OutEntity.getKss1Id());

                List<KghCpnKssMitosiOutEntity> kssMitosiOutEntities = cpnTucKss3SelectMapper
                        .findKghCpnKssMitosiByCriteria(kghCpnKssMitosiByCriteriaInEntity);
                // 2.4.2 取得した課題内容情報リスト.ヘッダIDが計画期間情報リスト.ヘッダIDと一致する場合、生活全般の解決すべき課題と優先順位を表示
                // 2.4.3 ソート順：課題内容情報リスト.表示順
                for (KghCpnKssMitosiOutEntity entity : kssMitosiOutEntities) {
                    Gui01023IssueContentSelectOutDto contentSelectOutDto = new Gui01023IssueContentSelectOutDto();
                    // 連番
                    contentSelectOutDto.setKss3Id(CommonDtoUtil.objValToString(entity.getKss3Id()));
                    // ヘッダID
                    contentSelectOutDto.setKss1Id(CommonDtoUtil.objValToString(entity.getKss1Id()));
                    // 見通し
                    contentSelectOutDto.setMitosiKnj(entity.getMitosiKnj());
                    // 生活全般の解決すべき課題
                    contentSelectOutDto.setKadaiKnj(entity.getKadaiKnj());
                    // 優先順位
                    contentSelectOutDto.setYusenNo(entity.getYusenNo());
                    // 表示順
                    contentSelectOutDto.setSort(CommonDtoUtil.objValToString(entity.getSort()));
                    // 更新回数
                    // contentSelectOutDto.setModifiedCnt(
                    // CommonDtoUtil.objValToString(entity.getModifiedCnt()));
                    issueContentSelect.add(contentSelectOutDto);
                }
            }
        }
        // 2.1.3 ソート順：開始日 降順
        creationPeriodIDInfoSelectList
                .sort(Comparator.comparing(Gui01023CreationPeriodIDInfoOutDto::getStartYmd).reversed());
        // 2.2.2 取得した計画期間情報リスト.計画期間IDに対して、以下のループ処理を実施する。
        for (Gui01023CreationPeriodIDInfoOutDto gui01023CreationPeriodIDInfo : creationPeriodIDInfoSelectList) {
            int recordsCnt = CommonConstants.NUMBER_0;
            for (Gui01023PlanPeriodInfoOutDto gui01023PlanPeriodInfo : planPeriodInfoSelectList) {
                if (gui01023PlanPeriodInfo.getSc1Id().equals(gui01023CreationPeriodIDInfo.getSc1Id())) {
                    recordsCnt++;
                }
            }
            gui01023CreationPeriodIDInfo.setRecordsCnt(CommonDtoUtil.objValToString(recordsCnt));
        }
        // 2.3.1 下記の課題整理総括様式マスタの様式名取得のDAOを利用し、課題整理総括様式情報を取得する。
        // 2.3.2 取得した課題整理総括様式情報取得リスト.様式IDに対して、以下のループ処理を実施する。
        CpnMucKssYoushiki1YoushikiKnjByCriteriaInEntity cpnMucKssYoushiki1YoushikiKnjByCriteriaInEntity = new CpnMucKssYoushiki1YoushikiKnjByCriteriaInEntity();
        List<CpnMucKssYoushiki1YoushikiKnjOutEntity> knjOutEntitiesYoushikiKnjOutEntities = cpnMucKssYoushiki1SelectMapper
                .findCpnMucKssYoushiki1YoushikiKnjByCriteria(
                        cpnMucKssYoushiki1YoushikiKnjByCriteriaInEntity);
        for (CpnMucKssYoushiki1YoushikiKnjOutEntity entity : knjOutEntitiesYoushikiKnjOutEntities) {
            Gui01023IssueFormatInfoOutDto formatInfoOutDto = new Gui01023IssueFormatInfoOutDto();
            // 様式ID
            formatInfoOutDto.setYoushikiId(CommonDtoUtil.objValToString(entity.getYoushikiId()));
            // 様式名
            formatInfoOutDto.setYoushikiKnj(entity.getYoushikiKnj());
            // 更新回数
            // formatInfoOutDto.setModifiedCnt(CommonDtoUtil.objValToString(entity.getModifiedCnt()));
            issueFormatInfoSelect.add(formatInfoOutDto);
        }
        // 3. レスポンスを返却する。
        // ※返却する情報の編集要領は「レスポンスパラメータ詳細」を参照
        dto.setCreationPeriodIDInfoSelectList(creationPeriodIDInfoSelectList);
        dto.setIssueContentSelect(issueContentSelect);
        dto.setIssueFormatInfoSelect(issueFormatInfoSelect);
        dto.setPlanPeriodInfoSelectList(planPeriodInfoSelectList);
        LOG.info(Constants.END);
        return dto;
    }
}
