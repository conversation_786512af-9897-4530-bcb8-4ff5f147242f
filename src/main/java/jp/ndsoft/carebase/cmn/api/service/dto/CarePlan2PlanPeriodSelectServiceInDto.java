package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI01014_計画書（2）計画期間変更の入力Dto
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class CarePlan2PlanPeriodSelectServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 事業者ID */
    @NotEmpty
    private String svJigyoId;
    /** 施設ID */
    @NotEmpty
    private String shisetuId;
    /** 利用者ID */
    @NotEmpty
    private String userId;
    /** 種別ID */
    @NotEmpty
    private String syubetsuId;
    /** 法人ID */
    @NotEmpty
    private String houjinId;
    /** 計画対象期間ID */
    @NotEmpty
    private String sc1Id;
    /** 計画対象期間インデックス */
    @NotEmpty
    private String kikanIndex;
    /** 計画期間ページ区分 */
    @NotEmpty
    private String kikanPage;
    /** 計画書様式 */
    @NotEmpty
    private String cksflg;
    /** 記録との連携 */
    @NotEmpty
    private String kirokuRenkeiFlg;
}
