package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00905IssuesPlanningStyleTitleMasterUpdateData;
import jp.ndsoft.carebase.cmn.api.service.dto.IssuesPlanningStyleTitleUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.IssuesPlanningStyleTitleUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkFree1Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkFree2Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkFreeTekiyoMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkFree1;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkFree1Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkFree2;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkFree2Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkFreeTekiyo;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkFreeTekiyoCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMocFree2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMocFree2NoFltByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMocFree2NoFltOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMocFree2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFreeTekiyoInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghMocKrkFreeTekiyoInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucFreeassKra1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucFreeassKra1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFree2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFreeTekiyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucFreeassKra1SelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * @since 2025.05.14
 * <AUTHOR> 盧青陽
 * @implNote GUI00905_課題立案様式タイトルマスタ画面の初期情報の登録
 */
@Service
public class IssuesPlanningStyleTitleUpdateServiceImpl
        extends
        UpdateServiceImpl<IssuesPlanningStyleTitleUpdateServiceInDto, IssuesPlanningStyleTitleUpdateServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // /** 採番 */
    // @Autowired
    // private Numbering numbering;
    // 32-07 課題立案ヘッダ情報取得のMapper
    @Autowired
    private KghTucFreeassKra1SelectMapper kghTucFreeassKra1SelectMapper;

    // 27-18 カスタマイズ帳票適用マスタ情報取得のMapper
    @Autowired
    private KghMocKrkFreeTekiyoSelectMapper kghMocKrkFreeTekiyoSelectMapper;

    // 7-18 カスタマイズ帳票適用マスタ情報取得のMapper
    @Autowired
    private KghMocKrkFree2SelectMapper kghMocKrkFree2SelectMapper;

    // 2.1.1. 「27-01 カスタマイズ帳票マスタヘッダ」の登録処理のMapper
    @Autowired
    private KghMocKrkFree1Mapper kghMocKrkFree1Mapper;

    // *******. 処理*******の取得件数 = 0の場合、「27-18 カスタマイズ帳票適用マスタ」の登録処理のMapper
    @Autowired
    private KghMocKrkFreeTekiyoMapper kghMocKrkFreeTekiyoMapper;

    // *******.2. 「27-02 カスタマイズ帳票マスタデータ」の登録処理のMapper
    @Autowired
    private KghMocKrkFree2Mapper kghMocKrkFree2Mapper;

    // 変数.行の分割数（上）
    Integer columnCount = 0;

    // 変数.初期表示の行の分割数（上）
    Integer initColumnCount = 0;

    // ループ回数
    Integer roopKaisuu = 0;

    /**
     * GUI00905_課題立案様式タイトルマスタ画面情報保存処理
     * 
     * @param inDto 課題立案様式設定マスタデータ保存入力DTO
     * @return 課題立案様式設定マスタデータ保存出力DTO
     * @throws Exception
     */
    @Override
    protected IssuesPlanningStyleTitleUpdateServiceOutDto mainProcess(IssuesPlanningStyleTitleUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);

        // 戻り情報を設定
        IssuesPlanningStyleTitleUpdateServiceOutDto outDto = new IssuesPlanningStyleTitleUpdateServiceOutDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============4.画面対象データ件数により、繰り返して下記チェック処理を行う。===============
         * 
         */
        for (Gui00905IssuesPlanningStyleTitleMasterUpdateData gui00905IPSTInDto : inDto
                .getIssuesPlanningStyleTitleMasterList()) {
            // 4.1 リクエストパラメータ.課題立案様式タイトルマスタリスト.更新区分が"D":削除
            // 又は、リクエストパラメータ.課題立案様式タイトルマスタリスト.アップデートフラグ1が"1":アップデート有の場合、
            if (CommonDtoUtil.isDelete(gui00905IPSTInDto)
                    || (CommonDtoUtil.isUpdate(gui00905IPSTInDto)
                            && CommonConstants.DEF_UPDATE_FLG_EXIST.equals(gui00905IPSTInDto.getDefUpdateFlg1()))) {
                // 下記の情報取得のDAOを利用し
                KghTucFreeassKra1ByCriteriaInEntity kghTucFreeassKra1ByCriteriaInEntity = new KghTucFreeassKra1ByCriteriaInEntity();
                // マスタヘッダID(リクエストパラメータ.課題立案様式タイトルマスタリスト.マスタヘッダID)
                kghTucFreeassKra1ByCriteriaInEntity
                        .setIlTitleId(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getFree1Id()));
                // 「32-07 課題立案ヘッダ」情報を取得する。
                KghTucFreeassKra1OutEntity kghTFK1OutEntityCount = this.kghTucFreeassKra1SelectMapper
                        .countKghTucFreeassKra1ByCriteria(kghTucFreeassKra1ByCriteriaInEntity);

                // 4.2 上記処理に取得データの件数 > 0の場合、下記情報を返して、後続処理を終了する
                if (kghTFK1OutEntityCount.getCNT() > 0) {
                    // レスポンス.エラー区分: "1"
                    outDto.setErrKbn(CommonConstants.ERROR_KBN);
                    return outDto;
                }
            }
        }

        // マスタヘッダID
        Integer free1Id = 0;
        // 配列.li_moji_max_cnt
        int[] liMojiMaxCnt = new int[] {};
        // 配列.li_moji_max_cnt_a3
        int[] liMojiMaxCntA3 = new int[] {};
        // 配列.li_moji_max_cnt_a3_wk
        int[] liMojiMaxCntA3Wk = new int[] {0,0,0,0};
        // 配列.li_moji_max_cnt_a4_wk
        int[] liMojiMaxCntA4Wk = new int[] {0,0,0,0};
        // 変数.用紙サイズ
        String youshiSize = null;
        // 変数.切替区分
        String kirikaeKbn = null;
        // 変数.項目名
        String nameKnj = null;
        // 変数.入力方法
        String inputKbn = null;
        // 変数.連動区分
        String rendouKbn = null;
        // 変数.印刷文字サイズ
        Integer fontSize = 0;
        // 減数
        Integer offset = 0;
        // 余り
        Integer amari = 0;
        // 変数.文字数
        Integer widthCnt = 0;
        // 変数.文字数合計
        Integer widthCntTotal = 0;
        // 変数.入力方法2_COUNT
        Integer inputKbnCnt = 0;
        // 変数.個別入力件数
        Integer inputKensuu = 0;
        // 取得件数
        Integer syutokuCnt = 0;

        /*
         * ===============2. 画面対象データ件数により、繰り返して下記保存処理を行う。===============
         * 
         */
        // 特になし

        // 上記の情報取得のDAOを利用し
        // *******.
        // *******. 下記の情報取得のDAOを利用し、既存の「27-18 カスタマイズ帳票適用マスタ」情報を全件取得する。
        KghKrkMocFree2NoFltByCriteriaInEntity kghKrkMocFree2NoFltByCriteriaInEntity = new KghKrkMocFree2NoFltByCriteriaInEntity();
        // 既存の「27-18 カスタマイズ帳票適用マスタ」情報を全件取得する。
        List<KghKrkMocFree2NoFltOutEntity> kghKrkMocFree2NoFltOutList = this.kghMocKrkFree2SelectMapper
                .findKghKrkMocFree2NoFltByCriteria(kghKrkMocFree2NoFltByCriteriaInEntity);

        /*
         * ===============2. 画面対象データ件数により、繰り返して下記保存処理を行う。===============
         * 
         */
        for (Gui00905IssuesPlanningStyleTitleMasterUpdateData gui00905IPSTInDto : inDto
                .getIssuesPlanningStyleTitleMasterList()) {
            // 2.1. リクエストパラメータ.課題立案様式タイトルマスタリスト.更新区分が"C":新規の場合
            if (CommonDtoUtil.isCreate(gui00905IPSTInDto)) {
                // 2.1.1. 「27-01 カスタマイズ帳票マスタヘッダ」の登録処理を行う。
                // *******. 上記登録処理に採番されたマスタヘッダIDを変数.採番マスタヘッダIDに設定する
                free1Id = insertInfoFirst(gui00905IPSTInDto);

                // 2.1.2. リクエストパラメータ.課題立案様式タイトルマスタリスト.適用フラグが"1":チェックONの場合
                saveKghMocKrkFreeTekiyo(gui00905IPSTInDto, free1Id);

                // 2.1.3. リクエストパラメータ.課題立案様式タイトルマスタリスト.フリーIDが"0":(新規行、又は、新規行を基づく複写行)の場合
                if (CommonConstants.DMY_FREE1ID_SINKI.equals(gui00905IPSTInDto.getDmyFree1Id())) {
                    // 「27-02 カスタマイズ帳票マスタデータ」の保存処理を行う。

                    // *******. 登録用の文字数を取得する
                    // *******.1. 下記配列初期設定を行う (変数定義略)
                    // 配列.li_moji_max_cnt = { 79,72,66,66 }
                    liMojiMaxCnt = new int[] { 79, 72, 66, 66 };
                    // 配列.li_moji_max_cnt_a3 = { 118, 105, 95, 95 }
                    liMojiMaxCntA3 = new int[] { 118, 105, 95, 95 };

                    // *******.2. 文字数の算出のために、必要な情報を設定する。
                    // 変数.用紙サイズ = リクエストパラメータ.課題立案様式タイトルマスタリスト.用紙サイズ
                    youshiSize = gui00905IPSTInDto.getYoushiSize();
                    // 変数.印刷文字サイズ ：
                    // リクエストパラメータ.課題立案様式タイトルマスタリスト.印刷文字サイズが「0」:9ptの場合
                    if (CommonConstants.FONT_SIZE_9PT.equals(gui00905IPSTInDto.getFontSize())) {
                        // 「0」に設定する
                        fontSize = CommonConstants.NUMBER_0;
                    }
                    // リクエストパラメータ.課題立案様式タイトルマスタリスト.印刷文字サイズが「1」:10ptの場合
                    else if (CommonConstants.FONT_SIZE_10PT.equals(gui00905IPSTInDto.getFontSize())) {
                        // 「1」に設定する
                        fontSize = CommonConstants.NUMBER_1;
                    }
                    // リクエストパラメータ.課題立案様式タイトルマスタリスト.印刷文字サイズが「2」:11ptの場合
                    else if (CommonConstants.FONT_SIZE_11PT.equals(gui00905IPSTInDto.getFontSize())) {
                        // 「2」に設定する
                        fontSize = CommonConstants.NUMBER_2;
                    }
                    // リクエストパラメータ.課題立案様式タイトルマスタリスト.印刷文字サイズが「3」:12ptの場合
                    else if (CommonConstants.FONT_SIZE_12PT.equals(gui00905IPSTInDto.getFontSize())) {
                        // 「3」に設定する
                        fontSize = CommonConstants.NUMBER_3;
                    }
                    // 上記以外の場合
                    else {
                        // 「2」に設定する
                        fontSize = CommonConstants.NUMBER_2;
                    }

                    // 変数.切替区分 = リクエストパラメータ.課題立案様式タイトルマスタリスト.切替区分
                    kirikaeKbn = gui00905IPSTInDto.getKirikaeKbn();
                    // 変数.行の分割数（上） = リクエストパラメータ.課題立案様式タイトルマスタリスト.行の分割数（上）
                    columnCount = CommonDtoUtil.strValToInt(gui00905IPSTInDto.getColumnCount());

                    // *******.3. 配列.li_moji_max_cnt_a4_wkと配列.li_moji_max_cnt_a3_wkの値を設定する。
                    // 変数.切替区分が「0」：全ての場合、
                    if (CommonConstants.KIRIKAE_KBN_ALL.equals(kirikaeKbn)) {
                        offset = CommonConstants.NUMBER_25;
                        for (int i = 0; i < 4; i++) {
                            // 配列.li_moji_max_cnt_a4_wk[0] = 配列.li_moji_max_cnt[0] - 25
                            // 配列.li_moji_max_cnt_a4_wk[1] = 配列.li_moji_max_cnt[1] - 25
                            // 配列.li_moji_max_cnt_a4_wk[2] = 配列.li_moji_max_cnt[2] - 25
                            // 配列.li_moji_max_cnt_a4_wk[3] = 配列.li_moji_max_cnt[3] - 25
                            liMojiMaxCntA4Wk[i] = liMojiMaxCnt[i] - offset;
                            // 配列.li_moji_max_cnt_a3_wk[0] = 配列.li_moji_max_cnt_a3[0] - 25
                            // 配列.li_moji_max_cnt_a3_wk[1] = 配列.li_moji_max_cnt_a3[1] - 25
                            // 配列.li_moji_max_cnt_a3_wk[2] = 配列.li_moji_max_cnt_a3[2] - 25
                            // 配列.li_moji_max_cnt_a3_wk[3] = 配列.li_moji_max_cnt_a3[3] - 25
                            liMojiMaxCntA3Wk[i] = liMojiMaxCntA3[i] - offset;
                        }
                    }
                    // 上記以外の場合
                    else {
                        offset = CommonConstants.NUMBER_23;
                        for (int i = 0; i < 4; i++) {
                            // 配列.li_moji_max_cnt_a4_wk[0] = 配列.li_moji_max_cnt[0] - 23
                            // 配列.li_moji_max_cnt_a4_wk[1] = 配列.li_moji_max_cnt[1] - 23
                            // 配列.li_moji_max_cnt_a4_wk[2] = 配列.li_moji_max_cnt[2] - 23
                            // 配列.li_moji_max_cnt_a4_wk[3] = 配列.li_moji_max_cnt[3] - 23
                            liMojiMaxCntA4Wk[i] = liMojiMaxCnt[i] - offset;
                            // 配列.li_moji_max_cnt_a3_wk[0] = 配列.li_moji_max_cnt_a3[0] - 23
                            // 配列.li_moji_max_cnt_a3_wk[1] = 配列.li_moji_max_cnt_a3[1] - 23
                            // 配列.li_moji_max_cnt_a3_wk[2] = 配列.li_moji_max_cnt_a3[2] - 23
                            // 配列.li_moji_max_cnt_a3_wk[3] = 配列.li_moji_max_cnt_a3[3] - 23
                            liMojiMaxCntA3Wk[i] = liMojiMaxCntA3[i] - offset;
                        }
                    }
                    // *******. リクエストパラメータ.課題立案様式タイトルマスタリスト.行の分割数（上）の値をループ回数として繰り返して、下記処理を行う。
                    for (roopKaisuu = 1; roopKaisuu <= CommonDtoUtil
                            .strValToInt(gui00905IPSTInDto.getColumnCount()); roopKaisuu++) {
                        // *******.1. 文字数を設定する。
                        // 変数.用紙サイズが"0"：A4の場合
                        if (CommonConstants.YOUSHI_SIZE_A4.equals(youshiSize)) {
                            // 「配列.li_moji_max_cnt_a4_wk[変数.印刷文字サイズ] / 変数.行の分割数（上）」の余りの数
                            amari = liMojiMaxCntA4Wk[fontSize] / columnCount;
                            // 「配列.li_moji_max_cnt_a4_wk[変数.印刷文字サイズ] / 変数.行の分割数（上）」の余りの数 >= ループ回数の場合
                            if (amari >= roopKaisuu) {
                                // 変数.文字数 = 配列.li_moji_max_cnt_a4_wk[変数.印刷文字サイズ] / 変数.行の分割数（上）+ 1
                                widthCnt = liMojiMaxCntA4Wk[fontSize] / columnCount + CommonConstants.NUMBER_1;
                            }
                            // 上記以外の場合
                            else {
                                // 変数.文字数 = 配列.li_moji_max_cnt_a4_wk[変数.印刷文字サイズ] / 変数.行の分割数（上）
                                widthCnt = liMojiMaxCntA4Wk[fontSize] / columnCount;
                            }
                        }
                        // 上記以外の場合
                        else {
                            // 「配列.li_moji_max_cnt_a3_wk[変数.印刷文字サイズ] / 変数.行の分割数（上）」の余りの数
                            amari = liMojiMaxCntA3Wk[fontSize] / columnCount;
                            // 「配列.li_moji_max_cnt_a3_wk[変数.印刷文字サイズ] / 変数.行の分割数（上）」の余りの数 >= ループ回数の場合
                            if (amari >= roopKaisuu) {
                                // 変数.文字数 = 配列.li_moji_max_cnt_a3_wk[変数.印刷文字サイズ] / 変数.行の分割数（上）+ 1
                                widthCnt = liMojiMaxCntA3Wk[fontSize] / columnCount + CommonConstants.NUMBER_1;
                            }
                            // 上記以外の場合
                            else {
                                // 変数.文字数 = 配列.li_moji_max_cnt_a3_wk[変数.印刷文字サイズ] / 変数.行の分割数（上）
                                widthCnt = liMojiMaxCntA3Wk[fontSize] / columnCount;
                            }
                        }
                        // *******.2. 「27-02 カスタマイズ帳票マスタデータ」の登録処理を行う。
                        insertInfoThird(free1Id, roopKaisuu, widthCnt);
                    }
                }
                // 2.1.4. リクエストパラメータ.課題立案様式タイトルマスタリスト.フリーIDが"0"以外:複写行の場合、
                else {
                    // *******. 文字数の算出のために、必要な情報を設定する。
                    // a) 下記変数を値設定する
                    // 変数.印刷文字サイズ ：
                    // リクエストパラメータ.課題立案様式タイトルマスタリスト.印刷文字サイズが「0」:9ptの場合
                    if (CommonConstants.FONT_SIZE_9PT.equals(gui00905IPSTInDto.getFontSize())) {
                        // 「0」に設定する
                        fontSize = CommonConstants.NUMBER_0;
                    }
                    // リクエストパラメータ.課題立案様式タイトルマスタリスト.印刷文字サイズが「1」:10ptの場合
                    else if (CommonConstants.FONT_SIZE_10PT.equals(gui00905IPSTInDto.getFontSize())) {
                        // 「1」に設定する
                        fontSize = CommonConstants.NUMBER_1;
                    }
                    // リクエストパラメータ.課題立案様式タイトルマスタリスト.印刷文字サイズが「2」:11ptの場合
                    else if (CommonConstants.FONT_SIZE_11PT.equals(gui00905IPSTInDto.getFontSize())) {
                        // 「2」に設定する
                        fontSize = CommonConstants.NUMBER_2;
                    }
                    // リクエストパラメータ.課題立案様式タイトルマスタリスト.印刷文字サイズが「3」:12ptの場合
                    else if (CommonConstants.FONT_SIZE_12PT.equals(gui00905IPSTInDto.getFontSize())) {
                        // 「4」に設定する
                        fontSize = CommonConstants.NUMBER_3;
                    }
                    // 上記以外の場合
                    else {
                        // 「2」に設定する
                        fontSize = CommonConstants.NUMBER_2;
                    }
                    // 変数.行の分割数（上） = リクエストパラメータ.課題立案様式タイトルマスタリスト.行の分割数（上）
                    columnCount = CommonDtoUtil.strValToInt(gui00905IPSTInDto.getColumnCount());
                    // 変数.用紙サイズ = リクエストパラメータ.課題立案様式タイトルマスタリスト.用紙サイズ
                    youshiSize = gui00905IPSTInDto.getYoushiSize();
                    // 変数.切替区分 = リクエストパラメータ.課題立案様式タイトルマスタリスト.切替区分
                    kirikaeKbn = gui00905IPSTInDto.getKinouKbn();
                    // 変数.文字数合計 = 0
                    widthCntTotal = CommonConstants.NUMBER_0;

                    // b) 下記配列を値設定する
                    // 変数.用紙サイズが"0"：A4の場合
                    if (CommonConstants.YOUSHI_SIZE_A4.equals(youshiSize)) {
                        // 配列.li_moji_max_cnt = { 79,72,66,66 }
                        liMojiMaxCnt = new int[] { 79, 72, 66, 66 };
                    }
                    // 上記以外の場合
                    else {
                        // 配列.li_moji_max_cnt = { 118,105,95,95 }
                        liMojiMaxCnt = new int[] { 118, 105, 95, 95 };
                    }
                    // 変数.切替区分が「0」：全ての場合
                    if (CommonConstants.KIRIKAE_KBN_ALL.equals(kirikaeKbn)) {
                        offset = CommonConstants.NUMBER_25;
                        for (int i = 0; i < liMojiMaxCnt.length; i++) {
                            // 配列.li_moji_max_cnt[0] = 配列.li_moji_max_cnt[0] - 25
                            // 配列.li_moji_max_cnt[1] = 配列.li_moji_max_cnt[1] - 25
                            // 配列.li_moji_max_cnt[2] = 配列.li_moji_max_cnt[2] - 25
                            // 配列.li_moji_max_cnt[3] = 配列.li_moji_max_cnt[3] - 25
                            liMojiMaxCnt[i] -= offset;
                        }
                    }
                    // 上記以外の場合
                    else {
                        offset = CommonConstants.NUMBER_23;
                        for (int i = 0; i < liMojiMaxCnt.length; i++) {
                            // 配列.li_moji_max_cnt[0] = 配列.li_moji_max_cnt[0] - 23
                            // 配列.li_moji_max_cnt[1] = 配列.li_moji_max_cnt[1] - 23
                            // 配列.li_moji_max_cnt[2] = 配列.li_moji_max_cnt[2] - 23
                            // 配列.li_moji_max_cnt[3] = 配列.li_moji_max_cnt[3] - 23
                            liMojiMaxCnt[i] -= offset;
                        }
                    }
                    // *******. 変数.行の分割数（上）をループ回数として繰り返して、下記処理を行う。
                    for (roopKaisuu = 1; roopKaisuu <= CommonDtoUtil
                            .strValToInt(gui00905IPSTInDto.getColumnCount()); roopKaisuu++) {
                        // *******.1. 下記の絞り込み条件で処理*******の取得結果から、複写元の「27-02 カスタマイズ帳票マスタデータ」情報を取得する。
                        if (CollectionUtils.isNotEmpty(kghKrkMocFree2NoFltOutList)) {

                            // 処理*******のOUTPUT情報.マスタヘッダID = リクエストパラメータ.課題立案様式タイトルマスタリスト.フリーID
                            // 処理*******のOUTPUT情報.区分ID = 1
                            // 処理*******のOUTPUT情報.項目ID = ループ回数
                            // 処理*******のOUTPUT情報.削除フラグ＝0 (未削除データ) --- SQL定義のうちに、削除フラグ = 0 は検索条件です。
                            Optional<KghKrkMocFree2NoFltOutEntity> kghKrkMocFree2NoFltOptional = kghKrkMocFree2NoFltOutList
                                    .stream().filter(item -> gui00905IPSTInDto.getDmyFree1Id()
                                            .equals(CommonDtoUtil.objValToString(item.getFree1Id()))
                                            && CommonConstants.KBN_ID_UP
                                                    .equals(CommonDtoUtil.objValToString(item.getKbnId()))
                                            && roopKaisuu.equals(item.getKoumokuId()))
                                    .findFirst();

                            // *******.2. 処理*******.1の取得データの件数 > 0の場合
                            if (kghKrkMocFree2NoFltOptional.isPresent()) {
                                KghKrkMocFree2NoFltOutEntity kghKrkMocFree2NoFlt = kghKrkMocFree2NoFltOptional.get();
                                // a) 下記変数を値設定する
                                // 変数.項目名 = 処理*******.1の取得データの項目名
                                nameKnj = kghKrkMocFree2NoFlt.getNameKnj();
                                // 変数.入力方法 = 処理*******.1の取得データの入力方法
                                inputKbn = CommonDtoUtil.objValToString(kghKrkMocFree2NoFlt.getInputKbn());
                                // 変数.連動区分 = 処理*******.1の取得データの連動区分
                                rendouKbn = CommonDtoUtil.objValToString(kghKrkMocFree2NoFlt.getRendouKbn());
                                // 変数.文字数 = 処理*******.1の取得データの文字数
                                widthCnt = kghKrkMocFree2NoFlt.getWidthCnt();
                                // b) 変数.文字数を再設定する。
                                // 変数.文字数合計 < 配列.li_moji_max_cnt[変数.印刷文字サイズ]の場合
                                if (widthCntTotal < liMojiMaxCnt[fontSize]) {
                                    // ループ回数が最終回の場合
                                    if (roopKaisuu == CommonDtoUtil
                                            .strValToInt(gui00905IPSTInDto.getColumnCount())) {
                                        // 変数.文字数 = 配列.li_moji_max_cnt[変数.印刷文字サイズ] - 変数.文字数合計
                                        widthCnt = liMojiMaxCnt[fontSize] - widthCntTotal;
                                    }
                                    // ループ回数が最終回以外の場合
                                    else {
                                        // 変数.文字数合計 = 変数.文字数合計 + 変数.文字数
                                        widthCntTotal += widthCnt;

                                        // 上記の加算で「変数.文字数合計 >= 配列.li_moji_max_cnt[変数.印刷文字サイズ]」になる場合
                                        if (widthCntTotal >= liMojiMaxCnt[fontSize]) {
                                            // 変数.文字数 = 変数.文字数 - (変数.文字数合計 - 配列.li_moji_max_cnt[変数.印刷文字サイズ] )
                                            widthCnt = widthCnt - (widthCntTotal - liMojiMaxCnt[fontSize]);
                                        }
                                    }
                                }
                                // 上記以外の場合
                                else {
                                    // 変数.文字数 = 0
                                    widthCnt = CommonConstants.NUMBER_0;
                                }
                            }
                            // *******.3. 処理*******.1の取得データの件数 = 0の場合
                            else {
                                // a) 下記変数を値設定する
                                // 変数.項目名 = "項目" + ループ回数
                                nameKnj = CommonConstants.NAME_KNJ + roopKaisuu;
                                // 変数.入力方法 = 1
                                inputKbn = CommonConstants.INPUT_KBN_TEXT;
                                // 変数.連動区分 = 0
                                rendouKbn = "0";
                                // 変数.文字数 = 0
                                widthCnt = CommonConstants.NUMBER_0;

                                // b) 変数.文字数合計 < 配列.li_moji_max_cnt[変数.印刷文字サイズ]、且つ、ループ回数が最終回の場合、 変数.文字数を再設定する。
                                if (widthCntTotal < liMojiMaxCnt[fontSize]
                                        && roopKaisuu == gui00905IPSTInDto.getColumnCount().length()) {
                                    // 変数.文字数 = 配列.li_moji_max_cnt[変数.印刷文字サイズ] - 変数.文字数合計
                                    widthCnt = liMojiMaxCnt[fontSize] - widthCntTotal;
                                }
                            }
                            // *******.4. 「27-02 カスタマイズ帳票マスタデータ」の登録処理を行う。
                            insertInfoFourth(free1Id, roopKaisuu, widthCnt, nameKnj, inputKbn, rendouKbn);
                        }
                    }
                }
            }
            // 2.2. リクエストパラメータ.課題立案様式タイトルマスタリスト.更新区分が"U":更新の場合
            else if (CommonDtoUtil.isUpdate(gui00905IPSTInDto)) {
                // 2.2.1. 「27-01 カスタマイズ帳票マスタヘッダ」情報を更新する
                updateInfoSecond(gui00905IPSTInDto);

                // 2.2.2. リクエストパラメータ.課題立案様式タイトルマスタリスト.適用フラグが"1":チェックONの場合
                saveKghMocKrkFreeTekiyo(gui00905IPSTInDto, free1Id);

                // 2.2.3. リクエストパラメータ.課題立案様式タイトルマスタリスト.アップデートフラグが"1":アップデート有の場合
                // 「27-02 カスタマイズ帳票マスタデータ」の保存処理を行う。
                if (CommonConstants.DEF_UPDATE_FLG_EXIST.equals(gui00905IPSTInDto.getUpdateKbn())) {

                    // *******. 文字数の算出のために、必要な情報を設定する。
                    // 配列.li_moji_max_cnt = { 79,72,66,66 }
                    liMojiMaxCnt = new int[] { 79, 72, 66, 66 };
                    // 配列.li_moji_max_cnt_a3 = { 118, 105, 95, 95 }
                    liMojiMaxCntA3 = new int[] { 118, 105, 95, 95 };
                    // 変数.印刷文字サイズ ：
                    if (CommonConstants.FONT_SIZE_9PT.equals(gui00905IPSTInDto.getFontSize())) {
                        // 「0」に設定する
                        fontSize = CommonConstants.NUMBER_0;
                    }
                    // リクエストパラメータ.課題立案様式タイトルマスタリスト.印刷文字サイズが「1」:10ptの場合
                    else if (CommonConstants.FONT_SIZE_10PT.equals(gui00905IPSTInDto.getFontSize())) {
                        // 「1」に設定する
                        fontSize = CommonConstants.NUMBER_1;
                    }
                    // リクエストパラメータ.課題立案様式タイトルマスタリスト.印刷文字サイズが「2」:11ptの場合
                    else if (CommonConstants.FONT_SIZE_11PT.equals(gui00905IPSTInDto.getFontSize())) {
                        // 「2」に設定する
                        fontSize = CommonConstants.NUMBER_2;
                    }
                    // リクエストパラメータ.課題立案様式タイトルマスタリスト.印刷文字サイズが「3」:12ptの場合
                    else if (CommonConstants.FONT_SIZE_12PT.equals(gui00905IPSTInDto.getFontSize())) {
                        // 「3」に設定する
                        fontSize = CommonConstants.NUMBER_3;
                    }
                    // 上記以外の場合
                    else {
                        // 「2」に設定する
                        fontSize = CommonConstants.NUMBER_2;
                    }
                    // 変数.行の分割数（上） = リクエストパラメータ.課題立案様式タイトルマスタリスト.行の分割数（上）
                    columnCount = CommonDtoUtil.strValToInt(gui00905IPSTInDto.getColumnCount());
                    // 変数.初期表示の行の分割数（上） = リクエストパラメータ.課題立案様式タイトルマスタリスト.初期表示の行の分割数（上）
                    initColumnCount = CommonDtoUtil.strValToInt(gui00905IPSTInDto.getInitColumnCount());
                    // 変数.用紙サイズ = リクエストパラメータ.課題立案様式タイトルマスタリスト.用紙サイズ
                    youshiSize = gui00905IPSTInDto.getYoushiSize();
                    // 変数.切替区分 = リクエストパラメータ.課題立案様式タイトルマスタリスト.切替区分
                    kirikaeKbn = gui00905IPSTInDto.getKirikaeKbn();
                    // 変数.個別入力件数 = 0
                    inputKensuu = CommonConstants.NUMBER_0;

                    // *******. 配列.li_moji_max_cnt_a4_wkと配列.li_moji_max_cnt_a3_wkの値を設定する。
                    // 変数.切替区分が「0」：全ての場合
                    if (CommonConstants.KIRIKAE_KBN_ALL.equals(kirikaeKbn)) {
                        offset = CommonConstants.NUMBER_25;
                        for (int i = 0; i < 4; i++) {
                            // 配列.li_moji_max_cnt_a4_wk[0] = 配列.li_moji_max_cnt[0] - 25
                            // 配列.li_moji_max_cnt_a4_wk[1] = 配列.li_moji_max_cnt[1] - 25
                            // 配列.li_moji_max_cnt_a4_wk[2] = 配列.li_moji_max_cnt[2] - 25
                            // 配列.li_moji_max_cnt_a4_wk[3] = 配列.li_moji_max_cnt[3] - 25
                            liMojiMaxCntA4Wk[i] = liMojiMaxCnt[i] - offset;
                            // 配列.li_moji_max_cnt_a3_wk[0] = 配列.li_moji_max_cnt_a3[0] - 25
                            // 配列.li_moji_max_cnt_a3_wk[1] = 配列.li_moji_max_cnt_a3[1] - 25
                            // 配列.li_moji_max_cnt_a3_wk[2] = 配列.li_moji_max_cnt_a3[2] - 25
                            // 配列.li_moji_max_cnt_a3_wk[3] = 配列.li_moji_max_cnt_a3[3] - 25
                            liMojiMaxCntA3Wk[i] = liMojiMaxCntA3[i] - offset;
                        }
                    }
                    // 上記以外の場合
                    else {
                        offset = CommonConstants.NUMBER_23;
                        for (int i = 0; i < 4; i++) {
                            // 配列.li_moji_max_cnt_a4_wk[0] = 配列.li_moji_max_cnt[0] - 23
                            // 配列.li_moji_max_cnt_a4_wk[1] = 配列.li_moji_max_cnt[1] - 23
                            // 配列.li_moji_max_cnt_a4_wk[2] = 配列.li_moji_max_cnt[2] - 23
                            // 配列.li_moji_max_cnt_a4_wk[3] = 配列.li_moji_max_cnt[3] - 23
                            liMojiMaxCntA4Wk[i] = liMojiMaxCnt[i] - offset;
                            // 配列.li_moji_max_cnt_a3_wk[0] = 配列.li_moji_max_cnt_a3[0] - 23
                            // 配列.li_moji_max_cnt_a3_wk[1] = 配列.li_moji_max_cnt_a3[1] - 23
                            // 配列.li_moji_max_cnt_a3_wk[2] = 配列.li_moji_max_cnt_a3[2] - 23
                            // 配列.li_moji_max_cnt_a3_wk[3] = 配列.li_moji_max_cnt_a3[3] - 23
                            liMojiMaxCntA3Wk[i] = liMojiMaxCntA3[i] - offset;
                        }
                    }
                    // 2.2.3.4. 下記の絞り込み条件で処理*******の取得結果から
                    if (CollectionUtils.isNotEmpty(kghKrkMocFree2NoFltOutList)) {

                        // 処理*******のOUTPUT情報.マスタヘッダID = リクエストパラメータ.課題立案様式タイトルマスタリスト.マスタヘッダID
                        // 処理*******のOUTPUT情報."1：上部、2：下部"＝1 (1：上部)
                        // 処理*******のOUTPUT情報.項目ID <= ※項目ID (※項目ID:
                        // 変数.行の分割数（上）と変数.初期表示の行の分割数（上）のうち、小さい値を設定する)
                        // 処理*******のOUTPUT情報.削除フラグ＝0 (未削除データ)
                        // 入力方法が”2”：数値のデータ件数を取得して、
                        List<KghKrkMocFree2NoFltOutEntity> kghKrkMocFree2NoFltFilter1List = kghKrkMocFree2NoFltOutList
                                .stream().filter(item -> gui00905IPSTInDto.getDmyFree1Id()
                                        .equals(CommonDtoUtil.objValToString(item.getFree1Id()))
                                        && CommonConstants.KBN_ID_UP
                                                .equals(CommonDtoUtil.objValToString(item.getKbnId()))
                                        && item.getKoumokuId() <= (Math.min(columnCount, initColumnCount))
                                        && CommonConstants.INPUT_KBN_NUMERIC
                                                .equals(CommonDtoUtil.objValToString(item.getInputKbn())))
                                .collect(Collectors.toList());

                        // 変数.入力方法2_COUNTに設定する
                        inputKbnCnt = kghKrkMocFree2NoFltFilter1List.size();
                    }

                    // ******* 変数.行の分割数（上）と変数.初期表示の行の分割数（上）のうち、大きい値をループ回数として、繰り返して下記処理を行う。
                    for (roopKaisuu = 1; roopKaisuu <= Math.max(initColumnCount, columnCount); roopKaisuu++) {
                        if (CollectionUtils.isNotEmpty(kghKrkMocFree2NoFltOutList)) {
                            // *******.1. 下記の絞り込み条件で処理*******の取得結果から、更新先の「27-02 カスタマイズ帳票マスタデータ」情報を取得する。
                            // 処理*******のOUTPUT情報.マスタヘッダID = リクエストパラメータ.課題立案様式タイトルマスタリスト.マスタヘッダID
                            // 処理*******のOUTPUT情報.区分ID = 1
                            // 処理*******のOUTPUT情報.項目ID = ループ回数
                            // 処理*******のOUTPUT情報.削除フラグ＝0 (未削除データ)
                            Optional<KghKrkMocFree2NoFltOutEntity> kghKrkMocFree2NoFltFilter2Optional = kghKrkMocFree2NoFltOutList
                                    .stream().filter(item -> gui00905IPSTInDto.getFree1Id()
                                            .equals(CommonDtoUtil.objValToString(item.getFree1Id()))
                                            && CommonConstants.KBN_ID_UP
                                                    .equals(CommonDtoUtil.objValToString(item.getKbnId()))
                                            && roopKaisuu.equals(item.getKoumokuId()))
                                    .findFirst();

                            KghKrkMocFree2NoFltOutEntity kghKrkMocFree2NoFltFilter2 = new KghKrkMocFree2NoFltOutEntity();
                            if (kghKrkMocFree2NoFltFilter2Optional.isPresent()) {
                                kghKrkMocFree2NoFltFilter2 = kghKrkMocFree2NoFltFilter2Optional.get();
                                syutokuCnt = CommonConstants.NUMBER_1;
                            }

                            // *******.2 変数.行の分割数（上）<= 変数.初期表示の行の分割数（上）の場合
                            if (columnCount <= initColumnCount) {
                                // *******.2.1 ループ回数 <= 変数.行の分割数（上）の場合
                                if (roopKaisuu <= columnCount) {
                                    // A) 変数.文字数を設定する
                                    // A-1) 処理*******.1の取得データの入力区分が"2：数値"の場合
                                    if (CommonConstants.INPUT_KBN_NUMERIC.equals(
                                            CommonDtoUtil.objValToString(kghKrkMocFree2NoFltFilter2.getInputKbn()))) {
                                        // 変数.文字数 = 1
                                        widthCnt = CommonConstants.NUMBER_1;
                                    }
                                    // A-2) 上記以外の場合
                                    else {
                                        // A-2-1) 変数.用紙サイズが"0"：A4の場合
                                        if (CommonConstants.YOUSHI_SIZE_A4.equals(youshiSize)) {
                                            // 変数.文字数 = (配列.li_moji_max_cnt_a4_wk[変数.印刷文字サイズ] - 変数.入力方法2_COUNT)/
                                            // (変数.行の分割数（上）-変数.入力方法2_COUNT)
                                            widthCnt = (liMojiMaxCntA4Wk[fontSize] - inputKbnCnt)
                                                    / (columnCount - inputKbnCnt);
                                            // 「(配列.li_moji_max_cnt_a4_wk[変数.印刷文字サイズ] - 変数.入力方法2_COUNT) /
                                            // (変数.行の分割数（上）-変数.入力方法2_COUNT)」の余りの数 > 変数.個別入力件数の場合
                                            if ((liMojiMaxCntA4Wk[fontSize] - inputKbnCnt)
                                                    % (columnCount - inputKbnCnt) > inputKensuu) {
                                                // 変数.文字数 = 変数.文字数 + 1
                                                widthCnt += CommonConstants.NUMBER_1;
                                            }
                                        }
                                        // A-2-2) 上記以外の場合
                                        else {
                                            // 変数.文字数 = (配列.li_moji_max_cnt_a3_wk[変数.印刷文字サイズ] - 変数.入力方法2_COUNT)/
                                            // (変数.行の分割数（上）-変数.入力方法2_COUNT)
                                            widthCnt = (liMojiMaxCntA3Wk[fontSize] - inputKbnCnt)
                                                    / (columnCount - inputKbnCnt);
                                            // 「 (配列.li_moji_max_cnt_a3_wk[変数.印刷文字サイズ] - 変数.入力方法2_COUNT) /
                                            // (変数.行の分割数（上）-変数.入力方法2_COUNT)」の余りの数 > 変数.個別入力件数の場合
                                            if ((liMojiMaxCntA3Wk[fontSize] - inputKbnCnt)
                                                    % (columnCount - inputKbnCnt) > inputKensuu) {
                                                // 変数.文字数 = 変数.文字数 + 1
                                                widthCnt += CommonConstants.NUMBER_1;
                                            }
                                        }
                                        // A-2-3) 変数.個別入力件数 = 変数.個別入力件数 +「1」のように設定
                                        inputKensuu += CommonConstants.NUMBER_1;
                                    }
                                    // B) 「27-02 カスタマイズ帳票マスタデータ」を更新する
                                    updateInfoThird(gui00905IPSTInDto, kghKrkMocFree2NoFltFilter2, roopKaisuu,
                                            widthCnt, true);
                                }
                                // *******.2.2 上記以外の場合
                                else {
                                    // 「27-02 カスタマイズ帳票マスタデータ」を更新する
                                    updateInfoThird(gui00905IPSTInDto, kghKrkMocFree2NoFltFilter2, roopKaisuu, widthCnt,
                                            false);
                                }
                            }
                            // *******.3 変数.行の分割数（上）> 変数.初期表示の行の分割数（上）の場合
                            else if (columnCount > initColumnCount) {
                                // *******.3.1 変数.文字数を設定する
                                // ループ回数 <= 変数.行の分割数（上）の場合
                                if (roopKaisuu <= columnCount) {
                                    // A) 変数.文字数を設定する
                                    // A-1) 処理*******.1の取得データの入力区分が"2：数値"の場合
                                    if (CommonConstants.INPUT_KBN_NUMERIC.equals(
                                            CommonDtoUtil.objValToString(kghKrkMocFree2NoFltFilter2.getInputKbn()))) {
                                        // 変数.文字数 = 1
                                        widthCnt = CommonConstants.NUMBER_1;
                                    }
                                    // A-2) 上記以外の場合
                                    else {
                                        // A-2-1) 変数.用紙サイズが"0"：A4の場合
                                        if (CommonConstants.YOUSHI_SIZE_A4.equals(youshiSize)) {
                                            // 変数.文字数 = (配列.li_moji_max_cnt_a4_wk[変数.印刷文字サイズ] - 変数.入力方法2_COUNT)/
                                            // (変数.行の分割数（上）-変数.入力方法2_COUNT)
                                            widthCnt = (liMojiMaxCntA4Wk[fontSize] - inputKbnCnt)
                                                    / (columnCount - inputKbnCnt);
                                            // 「(配列.li_moji_max_cnt_a4_wk[変数.印刷文字サイズ] - 変数.入力方法2_COUNT) /
                                            // (変数.行の分割数（上）-変数.入力方法2_COUNT)」の余りの数 > 変数.個別入力件数の場合
                                            if ((liMojiMaxCntA4Wk[fontSize] - inputKbnCnt)
                                                    % (columnCount - inputKbnCnt) > inputKensuu) {
                                                // 変数.文字数 = 変数.文字数 + 1
                                                widthCnt += CommonConstants.NUMBER_1;
                                            }
                                        }
                                        // A-2-2) 上記以外の場合
                                        else {
                                            // 変数.文字数 = (配列.li_moji_max_cnt_a3_wk[変数.印刷文字サイズ] - 変数.入力方法2_COUNT)/
                                            // (変数.行の分割数（上）-変数.入力方法2_COUNT)
                                            widthCnt = (liMojiMaxCntA3Wk[fontSize] - inputKbnCnt)
                                                    / (columnCount - inputKbnCnt);
                                            // 「 (配列.li_moji_max_cnt_a3_wk[変数.印刷文字サイズ] - 変数.入力方法2_COUNT) /
                                            // (変数.行の分割数（上）-変数.入力方法2_COUNT)」の余りの数 > 変数.個別入力件数の場合
                                            if ((liMojiMaxCntA3Wk[fontSize] - inputKbnCnt)
                                                    % (columnCount - inputKbnCnt) > inputKensuu) {
                                                // 変数.文字数 = 変数.文字数 + 1
                                                widthCnt += CommonConstants.NUMBER_1;
                                            }
                                        }
                                        // A-2-3) 変数.個別入力件数 = 変数.個別入力件数 +「1」のように設定
                                        inputKensuu += CommonConstants.NUMBER_1;
                                    }
                                    // B) 「27-02 カスタマイズ帳票マスタデータ」を更新する
                                    updateInfoThird(gui00905IPSTInDto, kghKrkMocFree2NoFltFilter2, roopKaisuu,
                                            widthCnt, true);
                                }

                                // *******.3.2 ループ回数 <= 変数.初期表示の行の分割数（上）の場合
                                if (roopKaisuu <= initColumnCount) {
                                    // 「27-02 カスタマイズ帳票マスタデータ」を更新する
                                    updateInfoThird(gui00905IPSTInDto, kghKrkMocFree2NoFltFilter2, roopKaisuu,
                                            widthCnt, true);
                                }
                                // *******.3.3 上記以外の場合
                                else {
                                    // A) 処理*******.1の取得データの件数 = 0の場合
                                    if (syutokuCnt == CommonConstants.NUMBER_0) {
                                        // 「27-02 カスタマイズ帳票マスタデータ」を登録する
                                        insertInfoFifth(free1Id, roopKaisuu, widthCnt);
                                    }
                                    // B) 処理*******.1の取得データの件数 > 0の場合
                                    if (syutokuCnt > CommonConstants.NUMBER_0) {
                                        // 「27-02 カスタマイズ帳票マスタデータ」を更新する
                                        updateInfoForth(gui00905IPSTInDto, kghKrkMocFree2NoFltFilter2,
                                                roopKaisuu, widthCnt);
                                    }
                                }
                            }

                            for (KghKrkMocFree2NoFltOutEntity kKMFree2NoFltOutEntity : kghKrkMocFree2NoFltOutList) {

                                // 処理*******のOUTPUT情報.マスタヘッダID = リクエストパラメータ.課題立案様式タイトルマスタリスト.マスタヘッダID
                                if (gui00905IPSTInDto.getFree1Id()
                                        .equals(CommonDtoUtil.objValToString(kKMFree2NoFltOutEntity.getFree1Id()))
                                        // 処理*******のOUTPUT情報."1：上部、2：下部"＝1 (1：上部)
                                        && CommonConstants.KBN_ID_UP
                                                .equals(CommonDtoUtil.objValToString(kKMFree2NoFltOutEntity.getKbnId()))
                                        // 処理*******のOUTPUT情報.項目ID = ループ回数
                                        && kKMFree2NoFltOutEntity.getKoumokuId().equals(roopKaisuu)) {
                                    // 処理*******のOUTPUT情報.削除フラグ＝0 (未削除データ)
                                    // SQL定義のうちに、削除フラグ = 0 は検索条件です。

                                    // 取得件数
                                    syutokuCnt++;
                                }

                            }
                        }
                    }
                }
            }
            // 2.3. リクエストパラメータ.課題立案様式タイトルマスタリスト.更新区分が"D":削除の場合
            else if (CommonDtoUtil.isDelete(gui00905IPSTInDto)) {
                // 2.3.1. リクエストパラメータ.課題立案様式タイトルマスタリスト.マスタヘッダIDが"0"以外:既存行の場合、下記処理を行う。
                if (!CommonConstants.FREE1_ID_NEW_LINE.equals(gui00905IPSTInDto.getFree1Id())) {
                    // *******. 「27-01 カスタマイズ帳票マスタヘッダ」をロジック的に削除する
                    deleteInfoFirst(gui00905IPSTInDto);

                    // *******. 下記の情報取得のDAOを利用し
                    KghKrkMocFree2ByCriteriaInEntity kghKrkMocFree2ByCriteriaInEntity = new KghKrkMocFree2ByCriteriaInEntity();
                    // マスタID
                    kghKrkMocFree2ByCriteriaInEntity
                            .setAiFree1Id(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getFree1Id()));
                    // 区分ID
                    kghKrkMocFree2ByCriteriaInEntity.setAiKbnId(CommonDtoUtil.strValToInt(CommonConstants.KBN_ID_UP));

                    // 「27-02 カスタマイズ帳票マスタデータ」情報を取得する。
                    List<KghKrkMocFree2OutEntity> KghKrkMocFree2OutEntityList = this.kghMocKrkFree2SelectMapper
                            .findKghKrkMocFree2ByCriteria(kghKrkMocFree2ByCriteriaInEntity);

                    // *******. 処理*******の取得データを繰り返して
                    for (KghKrkMocFree2OutEntity kghKrkMocFree2OutEntity : KghKrkMocFree2OutEntityList) {
                        deleteInfoSecond(kghKrkMocFree2OutEntity);
                    }

                }
                // 2.3.2. リクエストパラメータ.課題立案様式タイトルマスタリスト.マスタヘッダIDが"0":新規行の場合
            }
            // 2.4. 上記以外の場合
            // 処理なし
        }
        /*
         * ===============3. レスポンスを返却する。===============
         * 
         */
        // ※返却する情報の編集要領は「レスポンスパラメータ詳細」を参照
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 「27-18 カスタマイズ帳票適用マスタ」の保存処理を行う。
     * 
     * @param gui00905IPSTInDto
     * @param free1Id
     */
    private void saveKghMocKrkFreeTekiyo(Gui00905IssuesPlanningStyleTitleMasterUpdateData gui00905IPSTInDto,
            Integer free1Id)
            throws Exception {
        // 2.1.2. リクエストパラメータ.課題立案様式タイトルマスタリスト.適用フラグが"1":チェックONの場合
        if (CommonConstants.TEKIYO_FLG_CHECK_ON.equals(gui00905IPSTInDto.getTekiyouFlg())) {
            // 「27-18 カスタマイズ帳票適用マスタ」の保存処理を行う。
            // *******. 下記の情報取得のDAOを利用し、「27-18 カスタマイズ帳票適用マスタ」情報を取得する。
            KghMocKrkFreeTekiyoInfoByCriteriaInEntity kghMocKrkFreeTekiyoInfoByCriteriaInEntity = new KghMocKrkFreeTekiyoInfoByCriteriaInEntity();
            // 事業所ID
            kghMocKrkFreeTekiyoInfoByCriteriaInEntity
                    .setIlSvJigyoId(gui00905IPSTInDto.getSvJigyoId());
            // 機能区分
            kghMocKrkFreeTekiyoInfoByCriteriaInEntity
                    .setIiKinouKbn(gui00905IPSTInDto.getKinouKbn());
            // 様式区分
            kghMocKrkFreeTekiyoInfoByCriteriaInEntity
                    .setIiYoshikiKbn(gui00905IPSTInDto.getYoushikiKbn());
            // 「27-18 カスタマイズ帳票適用マスタ」情報を取得する。
            List<KghMocKrkFreeTekiyoInfoOutEntity> kghMocKrkFreeTekiyoInfoOutList = this.kghMocKrkFreeTekiyoSelectMapper
                    .findKghMocKrkFreeTekiyoInfoByCriteria(kghMocKrkFreeTekiyoInfoByCriteriaInEntity);

            // *******. 処理*******の取得件数 = 0の場合
            if (kghMocKrkFreeTekiyoInfoOutList.size() == CommonConstants.NUMBER_0) {
                // 「27-18 カスタマイズ帳票適用マスタ」の登録処理を行う。
                insertInfoSecond(gui00905IPSTInDto, free1Id);
            }
            // *******. 処理*******の取得件数 > 0の場合
            else if (kghMocKrkFreeTekiyoInfoOutList.size() > CommonConstants.NUMBER_0) {
                // 「27-18 カスタマイズ帳票適用マスタ」の更新処理を行う。
                updateInfoFirst(gui00905IPSTInDto, kghMocKrkFreeTekiyoInfoOutList, free1Id);
            }
        }
    }

    /**
     * 新規処理_1(2.1.1. 「27-01 カスタマイズ帳票マスタヘッダ」の登録処理)
     * 
     * @param gui00905IPSTInDto inデータ
     * @return マスタヘッダID
     */
    private Integer insertInfoFirst(Gui00905IssuesPlanningStyleTitleMasterUpdateData gui00905IPSTInDto)
            throws Exception {
        // ■対象テーブル: 27-01 カスタマイズ帳票マスタヘッダ
        KghMocKrkFree1 kghMocKrkFree1 = new KghMocKrkFree1();

        // 機能区分
        kghMocKrkFree1.setKinouKbn(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getKinouKbn()));
        // 様式区分
        kghMocKrkFree1.setYoushikiKbn(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getYoushikiKbn()));
        // 帳票タイトル
        kghMocKrkFree1.setTitleKnj(gui00905IPSTInDto.getTitleKnj());
        // 行の分割数（上）
        kghMocKrkFree1.setColumnCount(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getColumnCount()));
        // 行の分割数（下）
        kghMocKrkFree1.setColumnCount2(CommonConstants.NUMBER_0);
        // 表示順
        kghMocKrkFree1.setSort(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getSort()));
        // 印刷文字サイズ
        kghMocKrkFree1.setFontSize(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getFontSize()));
        // 用紙サイズ
        kghMocKrkFree1.setYoushiSize(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getYoushiSize()));
        // 自由に使用
        kghMocKrkFree1.setFreeKbn(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getFreeKbn()));
        // 切替区分
        kghMocKrkFree1.setKirikaeKbn(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getKirikaeKbn()));
        // 切替区分
        kghMocKrkFree1.setDelFlg(CommonConstants.DEL_FLG_OFF);

        // 文字列入力支援の入力
        int insertCnt = kghMocKrkFree1Mapper.insertSelectiveAndReturn(kghMocKrkFree1);
        // 排他チェック
        if (insertCnt <= CommonConstants.NUMBER_0) {
            throw new ExclusiveException();
        }
        // マスタヘッダID
        Integer free1Id = kghMocKrkFree1.getFree1Id().intValue();
        return free1Id;
    }

    /**
     * 新規処理_2(*******. 処理*******の取得件数 = 0の場合、「27-18 カスタマイズ帳票適用マスタ」の登録処理を行う。)
     * 
     * @param gui00905IPSTInDto inデータ
     * @param freeid            マスタヘッダID
     * @throws Exception
     */

    private void insertInfoSecond(Gui00905IssuesPlanningStyleTitleMasterUpdateData gui00905IPSTInDto, int free1Id)
            throws Exception {

        // ■対象テーブル: 27-01 カスタマイズ帳票マスタヘッダ
        KghMocKrkFreeTekiyo kghMocKrkFreeTekiyo = new KghMocKrkFreeTekiyo();
        // 事業所ID
        kghMocKrkFreeTekiyo.setSvJigyoId(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getSvJigyoId()));
        // 機能区分
        kghMocKrkFreeTekiyo.setKinouKbn(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getKinouKbn()));
        // 様式区分
        kghMocKrkFreeTekiyo.setYoushikiKbn(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getYoushikiKbn()));
        // ・リクエストパラメータ.課題立案様式タイトルマスタリスト.更新区分が"C":新規の場合
        if (CommonDtoUtil.isCreate(gui00905IPSTInDto)) {
            // 「処理2.1.1で採番されたマスタヘッダID」を設定する
            kghMocKrkFreeTekiyo.setFree1Id(free1Id);
        }
        // ・リクエストパラメータ.課題立案様式タイトルマスタリスト.更新区分が"U":更新の場合
        else if (CommonDtoUtil.isUpdate(gui00905IPSTInDto)) {
            // 「リクエストパラメータ.課題立案様式タイトルマスタリスト.マスタヘッダID」を設定する
            kghMocKrkFreeTekiyo.setFree1Id(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getFree1Id()));
        }

        // 文字列入力支援の入力
        int insertCnt = kghMocKrkFreeTekiyoMapper.insertSelective(kghMocKrkFreeTekiyo);
        // 排他チェック
        if (insertCnt <= CommonConstants.NUMBER_0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 新規処理_3(*******.2. 「27-02 カスタマイズ帳票マスタデータ」の登録処理を行う。)
     * 
     * @param free1Id    マスタヘッダID
     * @param roopKaisuu ループ回数
     * @param widthCnt   変数.文字数
     * @throws Exception
     */

    private void insertInfoThird(int free1Id, int roopKaisuu, int widthCnt) throws Exception {

        // ■対象テーブル: 27-02 カスタマイズ帳票マスタデータ
        KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();
        // マスタヘッダID
        kghMocKrkFree2.setFree1Id(free1Id);
        // 1：上部、2：下部
        kghMocKrkFree2.setKbnId(CommonDtoUtil.strValToInt(CommonConstants.KBN_ID_UP));
        // 項目ID
        kghMocKrkFree2.setKoumokuId(roopKaisuu);
        // 項目名
        kghMocKrkFree2.setNameKnj(CommonConstants.NAME_KNJ + CommonDtoUtil.objValToString(roopKaisuu));
        // 入力方法
        kghMocKrkFree2.setInputKbn(CommonDtoUtil.strValToInt(CommonConstants.INPUT_KBN_TEXT));
        // 連動区分
        kghMocKrkFree2.setRendouKbn(CommonConstants.NUMBER_0);
        // 文字数
        kghMocKrkFree2.setWidthCnt(widthCnt);

        // 文字列入力支援の入力
        int insertCnt = kghMocKrkFree2Mapper.insertSelective(kghMocKrkFree2);
        // 排他チェック
        if (insertCnt <= CommonConstants.NUMBER_0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 新規処理_4(*******.4. 「27-02 カスタマイズ帳票マスタデータ」の登録処理を行う。)
     * 
     * @param free1Id    マスタヘッダID
     * @param roopKaisuu ループ回数
     * @param widthCnt   変数.文字数
     * @param nameKnj    項目名
     * @param inputKbn   入力方法
     * @param rendouKbn  連動区分
     * @throws Exception
     */

    private void insertInfoFourth(int free1Id, int roopKaisuu, int widthCnt, String nameKnj, String inputKbn,
            String rendouKbn) throws Exception {

        // ■対象テーブル: 27-02 カスタマイズ帳票マスタデータ
        KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();
        // マスタヘッダID
        kghMocKrkFree2.setFree1Id(free1Id);
        // 1：上部、2：下部
        kghMocKrkFree2.setKbnId(CommonDtoUtil.strValToInt(CommonConstants.KBN_ID_UP));
        // 項目ID
        kghMocKrkFree2.setKoumokuId(roopKaisuu);
        // 項目名
        kghMocKrkFree2.setNameKnj(nameKnj);
        // 入力方法
        kghMocKrkFree2.setInputKbn(CommonDtoUtil.strValToInt(inputKbn));
        // 連動区分
        kghMocKrkFree2.setRendouKbn(CommonDtoUtil.strValToInt(rendouKbn));
        // 文字数
        kghMocKrkFree2.setWidthCnt(widthCnt);

        // 文字列入力支援の入力
        int insertCnt = kghMocKrkFree2Mapper.insertSelective(kghMocKrkFree2);
        // 排他チェック
        if (insertCnt <= CommonConstants.NUMBER_0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 新規処理_5(A) 処理*******.1の取得データの件数 = 0の場合、「27-02 カスタマイズ帳票マスタデータ」を登録する)
     * 
     * @param free1Id    マスタヘッダID
     * @param roopKaisuu ループ回数
     * @param widthCnt   変数.文字数
     * @throws Exception
     */

    private void insertInfoFifth(int free1Id, int roopKaisuu, int widthCnt) throws Exception {

        // ■対象テーブル: 27-02 カスタマイズ帳票マスタデータ
        KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();
        // マスタヘッダID
        kghMocKrkFree2.setFree1Id(free1Id);
        // 1：上部、2：下部
        kghMocKrkFree2.setKbnId(CommonDtoUtil.strValToInt(CommonConstants.KBN_ID_UP));
        // 項目ID
        kghMocKrkFree2.setKoumokuId(roopKaisuu);
        // 項目名
        kghMocKrkFree2.setNameKnj(CommonConstants.NAME_KNJ + CommonDtoUtil.objValToString(roopKaisuu));
        // 入力方法
        kghMocKrkFree2.setInputKbn(CommonDtoUtil.strValToInt(CommonConstants.INPUT_KBN_TEXT));
        // 連動区分
        kghMocKrkFree2.setRendouKbn(CommonConstants.NUMBER_0);
        // 文字数
        kghMocKrkFree2.setWidthCnt(widthCnt);

        // 文字列入力支援の入力
        int insertCnt = kghMocKrkFree2Mapper.insertSelective(kghMocKrkFree2);
        // 排他チェック
        if (insertCnt <= CommonConstants.NUMBER_0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 更新処理_1(*******. 処理*******の取得件数 > 0の場合、「27-18 カスタマイズ帳票適用マスタ」の更新処理を行う。)
     * 
     * @param gui00905IPSTInDto              inデータ
     * @param kghMocKrkFreeTekiyoInfoOutList 情報リスト
     * @param free1Id                        ループ回数
     * @throws ExclusiveException
     */
    private void updateInfoFirst(Gui00905IssuesPlanningStyleTitleMasterUpdateData gui00905IPSTInDto,
            List<KghMocKrkFreeTekiyoInfoOutEntity> kghMocKrkFreeTekiyoInfoOutList, int free1Id)
            throws ExclusiveException {
        final KghMocKrkFreeTekiyoCriteria kghMocKrkFreeTekiyoCriteria = new KghMocKrkFreeTekiyoCriteria();

        // 更新条件
        kghMocKrkFreeTekiyoCriteria.createCriteria()
                // 事業所ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getSvJigyoId()))
                // 機能区分
                .andKinouKbnEqualTo(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getKinouKbn()))
                // 様式区分
                .andYoushikiKbnEqualTo(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getYoushikiKbn()));

        // ■対象テーブル: 27-01 カスタマイズ帳票マスタヘッダ
        KghMocKrkFreeTekiyo kghMocKrkFreeTekiyo = new KghMocKrkFreeTekiyo();
        // ・リクエストパラメータ.課題立案様式タイトルマスタリスト.更新区分が"C":新規の場合
        if (CommonDtoUtil.isCreate(gui00905IPSTInDto)) {
            // 「処理2.1.1で採番されたマスタヘッダID」を設定する
            kghMocKrkFreeTekiyo.setFree1Id(free1Id);
        }
        // ・リクエストパラメータ.課題立案様式タイトルマスタリスト.更新区分が"U":更新の場合
        else if (CommonDtoUtil.isUpdate(gui00905IPSTInDto)) {
            // 「リクエストパラメータ.課題立案様式タイトルマスタリスト.マスタヘッダID」を設定する
            kghMocKrkFreeTekiyo.setFree1Id(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getFree1Id()));
        }

        // 文字列入力支援の更新
        int updateCnt = kghMocKrkFreeTekiyoMapper.updateByCriteriaSelective(kghMocKrkFreeTekiyo,
                kghMocKrkFreeTekiyoCriteria);

        // 排他チェック
        if (updateCnt <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 更新処理_2(2.2.1. 「27-01 カスタマイズ帳票マスタヘッダ」情報を更新する)
     * 
     * @param gui00905IPSTInDto inデータ
     * @throws ExclusiveException
     */
    private void updateInfoSecond(Gui00905IssuesPlanningStyleTitleMasterUpdateData gui00905IPSTInDto)
            throws ExclusiveException {
        final KghMocKrkFree1Criteria kghMocKrkFree1Criteria = new KghMocKrkFree1Criteria();

        // 更新条件
        kghMocKrkFree1Criteria.createCriteria()
                // マスタヘッダID
                .andFree1IdEqualTo(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getFree1Id()))
                // 削除フラグ
                .andDelFlgEqualTo(CommonConstants.NUMBER_0);

        // ■対象テーブル: 27-01 カスタマイズ帳票マスタヘッダ
        KghMocKrkFree1 kghMocKrkFree1 = new KghMocKrkFree1();
        // 帳票タイトル
        kghMocKrkFree1.setTitleKnj(gui00905IPSTInDto.getTitleKnj());
        // 行の分割数（上）
        kghMocKrkFree1.setColumnCount(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getColumnCount()));
        // 表示順
        kghMocKrkFree1.setSort(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getSort()));
        // 印刷文字サイズ
        kghMocKrkFree1.setFontSize(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getFontSize()));
        // 用紙サイズ
        kghMocKrkFree1.setYoushiSize(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getYoushiSize()));
        // 自由に使用
        kghMocKrkFree1.setFreeKbn(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getFreeKbn()));
        // 切替区分
        kghMocKrkFree1.setKirikaeKbn(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getFree1Id()));

        // 文字列入力支援の更新
        int updateCnt = kghMocKrkFree1Mapper.updateByCriteriaSelective(kghMocKrkFree1,
                kghMocKrkFree1Criteria);

        // 排他チェック
        if (updateCnt <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 更新処理_3(B) 「27-02 カスタマイズ帳票マスタデータ」を更新する)
     * 
     * @param gui00905IPSTInDto              inデータ
     * @param kghMocKrkFreeTekiyoInfoOutList 情報リスト
     * @param widthCnt                       文字数
     * @throws ExclusiveException
     */
    private void updateInfoThird(Gui00905IssuesPlanningStyleTitleMasterUpdateData gui00905IPSTInDto,
            KghKrkMocFree2NoFltOutEntity kghKrkMocFree2NoFltFilter2, Integer roopKaisuu, Integer widthCnt,
            Boolean widthFlg)
            throws ExclusiveException {
        final KghMocKrkFree2Criteria kghMocKrkFree2Criteria = new KghMocKrkFree2Criteria();

        // 更新条件
        kghMocKrkFree2Criteria.createCriteria()
                // マスタヘッダID
                .andFree1IdEqualTo(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getFree1Id()))
                // 区分ID＝1
                .andKbnIdEqualTo(CommonDtoUtil.strValToInt(CommonConstants.KBN_ID_UP))
                // 項目ID＝ループ回数
                .andKoumokuIdEqualTo(roopKaisuu);

        KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();
        // 文字数
        kghMocKrkFree2.setWidthCnt(widthFlg ? widthCnt : CommonConstants.NUMBER_0);

        // 文字列入力支援の更新
        int updateCnt = kghMocKrkFree2Mapper.updateByCriteriaSelective(kghMocKrkFree2,
                kghMocKrkFree2Criteria);

        // 排他チェック
        if (updateCnt <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 更新処理_4(B) 処理*******.1の取得データの件数 > 0の場合、「27-02 カスタマイズ帳票マスタデータ」を更新する)
     * 
     * @param gui00905IPSTInDto              inデータ
     * @param kghMocKrkFreeTekiyoInfoOutList 情報リスト
     * @param roopKaisuu                     ループ回数
     * @param widthCnt                       文字数
     * @throws ExclusiveException
     */
    private void updateInfoForth(Gui00905IssuesPlanningStyleTitleMasterUpdateData gui00905IPSTInDto,
            KghKrkMocFree2NoFltOutEntity kghKrkMocFree2NoFltFilter2, int roopKaisuu, int widthCnt)
            throws ExclusiveException {
        final KghMocKrkFree2Criteria kghMocKrkFree2Criteria = new KghMocKrkFree2Criteria();

        // 更新条件
        kghMocKrkFree2Criteria.createCriteria()
                // マスタヘッダID
                .andFree1IdEqualTo(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getFree1Id()))
                // 区分ID＝1
                .andKbnIdEqualTo(CommonDtoUtil.strValToInt(CommonConstants.KBN_ID_UP))
                // 項目ID
                .andKoumokuIdEqualTo(roopKaisuu);

        // ■対象テーブル: 7-02 カスタマイズ帳票マスタデータ
        KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();
        // 項目名
        kghMocKrkFree2.setNameKnj(CommonConstants.NAME_KNJ + roopKaisuu);
        // 入力方法
        kghMocKrkFree2.setInputKbn(CommonDtoUtil.strValToInt(CommonConstants.INPUT_KBN_TEXT));
        // 連動区分
        kghMocKrkFree2.setRendouKbn(CommonConstants.NUMBER_0);
        // 文字数
        kghMocKrkFree2.setWidthCnt(widthCnt);

        // 文字列入力支援の更新
        int updateCnt = kghMocKrkFree2Mapper.updateByCriteriaSelective(kghMocKrkFree2,
                kghMocKrkFree2Criteria);

        // 排他チェック
        if (updateCnt <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 削除処理_1(*******. 「27-01 カスタマイズ帳票マスタヘッダ」をロジック的に削除する)
     * 
     * @param gui00905IPSTInDto inデータ
     * @throws ExclusiveException
     */
    private void deleteInfoFirst(Gui00905IssuesPlanningStyleTitleMasterUpdateData gui00905IPSTInDto)
            throws ExclusiveException {
        final KghMocKrkFree1Criteria kghMocKrkFree1Criteria = new KghMocKrkFree1Criteria();

        // 削除条件
        kghMocKrkFree1Criteria.createCriteria()
                // マスタヘッダID
                .andFree1IdEqualTo(CommonDtoUtil.strValToInt(gui00905IPSTInDto.getFree1Id()));

        // 文字列入力支援の更新
        int updateCnt = kghMocKrkFree1Mapper.deleteByCriteria(kghMocKrkFree1Criteria);

        // 排他チェック
        if (updateCnt <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 削除処理_2(*******. 処理*******の取得データを繰り返して、「27-02 カスタマイズ帳票マスタデータ」をロジック的に削除する)
     * 
     * @param kCHDataOutList 情報リスト
     * @throws ExclusiveException
     */

    /**
     * 27-02 カスタマイズ帳票マスタデータ」を削除する)
     * 
     * @param kasutamaizuCyouHyouData カスタマイズ帳票マスタデータ
     * @throws ExclusiveException
     */
    private void deleteInfoSecond(KghKrkMocFree2OutEntity kghKrkMocFree2OutEntity)
            throws ExclusiveException {
        final KghMocKrkFree2Criteria kghMocKrkFree2Criteria = new KghMocKrkFree2Criteria();

        // 削除条件
        kghMocKrkFree2Criteria.createCriteria()
                // マスタヘッダID
                .andFree1IdEqualTo(kghKrkMocFree2OutEntity.getFree1Id())
                // 区分ID
                .andKbnIdEqualTo(kghKrkMocFree2OutEntity.getKbnId())
                // 項目ID
                .andKoumokuIdEqualTo(kghKrkMocFree2OutEntity.getKoumokuId());

        // 文字列入力支援の更新
        int updateCnt = kghMocKrkFree2Mapper.deleteByCriteria(kghMocKrkFree2Criteria);

        // 排他チェック
        if (updateCnt <= 0) {
            throw new ExclusiveException();
        }
    }
}
