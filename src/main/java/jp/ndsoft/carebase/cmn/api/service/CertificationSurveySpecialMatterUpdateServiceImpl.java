package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01272TokkiSave;
import jp.ndsoft.carebase.cmn.api.service.dto.CertificationSurveySpecialMatterUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.CertificationSurveySpecialMatterUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCsc4Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCsc4;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCsc4Criteria;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * @since 2025.05.30
 * <AUTHOR>
 * @apiNote GUI01272_認定調査票特記事項画面保存
 */
@Service
public class CertificationSurveySpecialMatterUpdateServiceImpl extends
        UpdateServiceImpl<CertificationSurveySpecialMatterUpdateServiceInDto, CertificationSurveySpecialMatterUpdateServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 認定調査票（特記事項）Mapper */
    @Autowired
    private CpnTucCsc4Mapper cpnTucCsc4Mapper;

    /**
     * 特記事項情報を保存するメイン処理
     * 
     * @param inDto 入力DTO
     * @return 出力DTO
     * @throws Exception 処理例外
     */
    @Override
    protected CertificationSurveySpecialMatterUpdateServiceOutDto mainProcess(
            CertificationSurveySpecialMatterUpdateServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        // 3.保存処理を行う
        // 3.1 リクエストパラメータ.特記事項リストをループして、保存処理を行う。
        for (Gui01272TokkiSave tokki : inDto.getTokkiList()) {
            // 3.1.1. リクエストパラメータ.特記事項リスト.更新区別が"C":新規の場合、
            if (CommonDtoUtil.isCreate(tokki)) {
                // 認定調査票（特記事項）を登録する
                insertTokki(tokki, inDto.getScId(), inDto.getCschId());
            }
            // 3.1.2. リクエストパラメータ.特記事項リスト.更新区別が"D":削除の場合、
            if (CommonDtoUtil.isDelete(tokki)) {
                // 認定調査票（特記事項）を物理削除する
                deleteTokki(tokki);
            }
            // 3.1.3. リクエストパラメータ.特記事項リスト.更新区別が"U":更新の場合、
            if (CommonDtoUtil.isUpdate(tokki)) {
                // 認定調査票（特記事項）を更新する
                updateTokki(tokki, inDto.getScId(), inDto.getCschId());
            }
        }
        LOG.info(Constants.END);
        return new CertificationSurveySpecialMatterUpdateServiceOutDto();

    }

    /**
     * 認定調査票（特記事項）を更新する
     * 
     * @param tokki リクエストの特記事項
     * @throws Exception
     */
    private void updateTokki(Gui01272TokkiSave tokki, String scId, String cschId) throws Exception {
        CpnTucCsc4 cpnTucCsc4 = new CpnTucCsc4();
        // 認定（特記：大）
        cpnTucCsc4.setN1tCd(CommonDtoUtil.strValToInt(tokki.getN1tCd()));
        // 認定（特記：小）
        cpnTucCsc4.setN2tCd(tokki.getN2tCd());
        // 特記事項
        cpnTucCsc4.setMemoKnj(tokki.getMemoKnj());
        // 表示順
        cpnTucCsc4.setSeqNo(CommonDtoUtil.strValToInt(tokki.getSeqNo()));

        final CpnTucCsc4Criteria cpnTucCsc4Criteria = new CpnTucCsc4Criteria();
        cpnTucCsc4Criteria.createCriteria().andCounterEqualTo(CommonDtoUtil.strValToInt(tokki.getCounter()));

        if (cpnTucCsc4Mapper.updateByCriteriaSelective(cpnTucCsc4, cpnTucCsc4Criteria) <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 認定調査票（特記事項）を物理削除する
     * 
     * @param tokki リクエストの特記事項
     * @throws Exception 処理例外
     */
    private void deleteTokki(Gui01272TokkiSave tokki) throws Exception {
        // 認定調査票（特記事項）を物理削除する
        CpnTucCsc4Criteria criteria = new CpnTucCsc4Criteria();
        // 削除条件
        criteria.createCriteria().andCounterEqualTo(CommonDtoUtil.strValToInt(tokki.getCounter()));
        if (cpnTucCsc4Mapper.deleteByCriteria(criteria) <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }
    }

    /**
     * 認定調査票（特記事項）を登録する
     * 
     * @param tokki  リクエストの特記事項
     * @param scId   計画期間ID
     * @param cschId 調査票ID
     * @throws Exception 処理例外
     */
    private void insertTokki(Gui01272TokkiSave tokki, String scId, String cschId) throws Exception {
        CpnTucCsc4 cpnTucCsc4 = new CpnTucCsc4();
        // 計画期間ID
        cpnTucCsc4.setSc1Id(CommonDtoUtil.strValToInt(scId));
        // 調査票ID
        cpnTucCsc4.setCschId(CommonDtoUtil.strValToInt(cschId));
        // 認定（特記：大）
        cpnTucCsc4.setN1tCd(CommonDtoUtil.strValToInt(tokki.getN1tCd()));
        // 認定（特記：小）
        cpnTucCsc4.setN2tCd(tokki.getN2tCd());
        // 特記事項
        cpnTucCsc4.setMemoKnj(tokki.getMemoKnj());
        // 表示順s
        cpnTucCsc4.setSeqNo(CommonDtoUtil.strValToInt(tokki.getSeqNo()));
        if (cpnTucCsc4Mapper.insertSelective(cpnTucCsc4) <= 0) {
            throw new ExclusiveException();
        }
    }
}
