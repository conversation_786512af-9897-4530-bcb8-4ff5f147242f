package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * @since 2025.05.12
 * <AUTHOR>
 * @description GUI00904_課題立案様式設定マスタ参照DTO
 */
@Getter
@Setter
public class Gui00904kghMocKrkFree11OutDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    // マスタヘッダID
    @NotEmpty
    private String free1Id;
    // 帳票タイトル
    @NotEmpty
    private String titleKnj;
    // 帳票タイトルID組合せ
    private String dmyTitleKnj;
}
