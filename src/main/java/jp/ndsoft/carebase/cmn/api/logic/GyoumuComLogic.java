package jp.ndsoft.carebase.cmn.api.logic;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.apache.commons.lang3.StringUtils;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.DailyRoutinePlanReportServiceDbNoSaveData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.DailyRoutinePlanReportServicePrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.DailyRoutinePlanReportServicePrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ImplementationPlan1ReportServiceInitMasterObj;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ImplementationPlan3ReportServiceInitMasterObj;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.InitSettingMasterData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.MonthlyYearlyReportServiceDbNoSaveData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.MonthlyYearlyReportServicePrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Plan1kReportServiceDbNoSaveData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PreventionPlanA31ReportServiceDbNoSaveData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PreventionPlanA31ReportServiceInitMasterObj;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServiceDbNoSaveData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServicePrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServicePrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportImplementationPlan2NoSaveData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportImplementationPlan2PrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportImplementationPlan2PrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkSetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComCheckInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComCopyInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComCopyOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComCopyRirekiInfoDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComElectronicSaveInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComInitMasterData;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComPrintSettingsDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComPrintSettingsInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComPrintSettingsInputDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComPrintSettingsParamDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComPrintSettingsSaveDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComSecurityDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComSystemIniDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KikanInfoDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.RirekiInfoDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.TermId2DateLogicOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.PreventionPlanA42ReportServiceParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.DailyRoutinePlanReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.DailyTaskTableA3ReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.ImplementationPlan1ReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.ImplementationPlan2ReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.ImplementationPlan3ReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.MonthlyYearlyReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.Plan1kReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.PreventionPlanA31ReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.PreventionPlanA43ReportParameterModel;
import jp.ndsoft.carebase.cmn.api.service.InitialSettingMasterSelectServiceImpl;
import jp.ndsoft.carebase.cmn.api.service.dto.ComPrintSettingsDto;
import jp.ndsoft.carebase.cmn.api.service.dto.HokenSvDto;
import jp.ndsoft.carebase.cmn.api.service.dto.HokenSvOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.HokenYmdDto;
import jp.ndsoft.carebase.cmn.api.service.dto.InitialSettingMasterSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.InitialSettingMasterSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.InsuranceServicesResetSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.KghMocKrkSsmMasterInfoDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Plan1InitMasterData;
import jp.ndsoft.carebase.cmn.api.logic.dto.GyoumuComCopyKikanInfoDto;
import jp.ndsoft.carebase.cmn.api.util.CmnStorageServiceUtil;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.ComMocPrtiniSMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocPrtiniS;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocPrtiniSCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemuseNameByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemuseNameOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocPrtiniByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocPrtiniOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocPrtiniSByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocPrtiniSOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocPrtiniSZoomByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocPrtiniSZoomOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCks11ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCks11OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucKss1InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucKss1InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucMygKky1InfoFilterMstKbnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucMygKky1InfoFilterMstKbnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucMygKky2InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucMygKky2InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucSypAss1InfoFilteruserIdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucSypAss1InfoFilteruserIdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucSypAss4InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucSypAss4InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ItemuseSuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ItemuseSuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKkak2SvItemSougouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKkak2SvItemSougouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucKrkKikanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucKrkKikanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RuakuKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RuakuKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SabisuJigyoshaByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SabisuJigyoshaOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ScodeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ScodeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSougouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocPrtiniSSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocPrtiniSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoNameSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucKss1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucMygKky1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkComKikanSvjArrayByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkComKikanSvjArrayOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Kss1IkouKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Kss1IkouKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucMygKky2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucSypAss1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucSypAss4SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucKrkKikanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KikanSentakuSelectMapper;
import jp.ndsoft.smh.framework.controllers.report.model.Jigyosyo;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.smh.framework.global.report.dto.FixedReportInDto;
import jp.ndsoft.smh.framework.util.AppUtil;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * 業務共通用
 * 
 * <AUTHOR>
 */
@Component
public class GyoumuComLogic {

    /** 機能ＩＤ */
    public static final String HOSHIKI_KINOU_ID = "機能ＩＤ";
    /** 機能名 */
    public static final String HOSHIKI_KINOU_NAME = "機能名";
    /** 操作区分 0:初期化 */
    private static final String OPER_FLAG_0 = "0";
    /** 操作区分 1:新規 */
    private static final String OPER_FLAG_1 = "1";
    /** 操作区分 3:削除 */
    private static final String OPER_FLAG_3 = "3";
    /** 操作区分 9:履歴以降再検索 */
    private static final String OPER_FLAG_9 = "9";
    /** 操作区分 K1:計画対象期間prev */
    private static final String OPER_FLAG_K1 = "K1";
    /** 操作区分 K2:計画対象期間next */
    private static final String OPER_FLAG_K2 = "K2";
    /** 操作区分 K3:計画対象期間Open */
    private static final String OPER_FLAG_K3 = "K3";
    /** 操作区分 R1:履歴prev */
    private static final String OPER_FLAG_R1 = "R1";
    /** 操作区分 R2:履歴next */
    private static final String OPER_FLAG_R2 = "R2";
    /** 操作区分 R3:履履歴Open */
    private static final String OPER_FLAG_R3 = "R3";
    /** 1:「計画期間」先頭件取得エラー */
    private static final String RET_ERR_CODE_1 = "1";
    /** 2:「計画期間」最終件取得エラー */
    private static final String RET_ERR_CODE_2 = "2";
    /** 8:保存確認 */
    private static final String RET_CODE_8 = "8";
    /** 1:あり */
    private static final String PAGE_FLAG_1 = "1";
    /** 0:なし */
    private static final String PAGE_FLAG_0 = "0";
    /** count:0 */
    private static final String STR_COUNT_0 = "0";
    /** count:1 */
    private static final String STR_COUNT_1 = "1";
    /** 「0:権限なし」 */
    private static final String AUTH_FLAG_0 = "0";
    /** 文字列"P" */
    private static final String STR_P = "P";
    /** 文字列"prt" */
    private static final String STR_PRT = "prt";
    /** 文字列"amikake_flg" */
    private static final String STR_AMIKAKE_FLG = "amikake_flg";
    /** 文字列"iso9001_flg" */
    private static final String STR_ISO9001_FLG = "iso9001_flg";
    /** 文字列"kojinhogo_flg" */
    private static final String STR_KOJINHOGO_FLG = "kojinhogo_flg";
    /** 帳票番号: 1(固定) */
    private static final String PRTNO_STR_1 = "1";
    /** インデックス: 1(固定) */
    private static final String INDEX_STR_1 = "1";

    /** 対象サービス事業コードリスト */
    private static final List<String> TARGET_SERVICE_CODES = Arrays.asList("A1010", "A5013", "A9010");

    /** 「計画対象期間」リストを取得 */
    @Autowired
    private KghTucKrkKikanSelectMapper kghTucKrkKikanSelectMapper;

    /** 共通部品「f_kgh_krk_get_syubetu」 */
    @Autowired
    private KghKrkZOther01Logic kghKrkZOther01Logic;

    /** 共通部品「f_kgh_krk_get_kikan」 */
    @Autowired
    private KghKrkZSnc01Logic kghKrkZSnc01Logic;

    /** サービス事業者名称取得 */
    @Autowired
    private SvJigyoInfoLogic svJigyoInfoLogic;

    /** 職員基本情報取得 */
    @Autowired
    private ShokuinInfoLogic shokuinInfoLogic;

    /** 「アセスメント総括履歴」リストを取得する */
    @Autowired
    private CpnTucSypAss1SelectMapper cpnTucSypAss1SelectMapper;

    /** 「総括の課題・目標」リストを取得する */
    @Autowired
    private CpnTucSypAss4SelectMapper cpnTucSypAss4SelectMapper;

    /** 共通部品「f_kgh_com_get_e_bunsho_kbn」 */
    @Autowired
    private KghSmglHistoryLogic kghSmglHistoryLogic;

    /** 共通部品「uf_use_kinou_chk」 */
    @Autowired
    private KghAdmSecurityLogic kghAdmSecurityLogic;

    /** サービス事業者名称取得 */
    @Autowired
    private ComMscSvjigyoSelectMapper comMscSvjigyoSelectMapper;

    /** 略称取得 */
    @Autowired
    private ComMscSvjigyoNameSelectMapper comMscSvjigyoNameSelectMapper;

    /** 共通関数kghCmnF01Logic */
    @Autowired
    private KghCmnF01Logic kghCmnF01Logic;

    /** 共通関数KghKrkZCpnFuncLogic */
    @Autowired
    private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;

    /** 介護サービス費適用マスタ（20-4）（com_mhc_itemuse）から項目カウントを取得 */
    @Autowired
    private ComMhcItemuseSelectMapper comMhcItemuseSelectMapper;

    /** 20-20 介護予防・日常生活支援総合事業サービス費適用マスタ（com_mhc_itemuse_sougou）から正式名称を取得 */
    @Autowired
    private ComMhcItemuseSougouSelectMapper comMhcItemuseSougouSelectMapper;

    /** 項目識別コードの取得 */
    @Autowired
    private ComMhcItemSelectMapper comMhcItemSelectMapper;

    /** 課題整理総括の意向内容 */
    @Autowired
    private CpnTucKss1SelectMapper cpnTucKss1SelectMapper;

    /** 課題検討の総合方針 */
    @Autowired
    private CpnTucMygKky2SelectMapper cpnTucMygKky2SelectMapper;

    /** 課題検討履歴 */
    @Autowired
    private CpnTucMygKky1SelectMapper cpnTucMygKky1SelectMapper;

    /** 表示用「計画期間」リスト取得 */
    @Autowired
    private KikanSentakuSelectMapper kikanSentakuSelectMapper;

    /** 24-04 印刷iniファイルデータ初期値テーブル情報を取得 */
    @Autowired
    private ComMocPrtiniSelectMapper comMocPrtiniSelectMapper;

    /** 24-05 印刷iniファイルデータ保存テーブル情報取得 */
    @Autowired
    private ComMocPrtiniSSelectMapper comMocPrtiniSSelectMapper;

    /** 24-05 印刷iniファイルデータ保存テーブル（com_moc_prtini_s） */
    @Autowired
    private ComMocPrtiniSMapper comMocPrtiniSMapper;

    /** 設定の書込（NEXのパソコン単位の設定） */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** 初期設定マスタの情報を取得する。 */
    @Autowired
    private InitialSettingMasterSelectServiceImpl initialSettingMasterSelectServiceImpl;

    /** ケアマネストレージサービス共通処理 */
    @SuppressWarnings("rawtypes")
    @Autowired
    private CmnStorageServiceUtil cmnStorageServiceUtil;

    /**
     * 業務共通化-共通部品の取得
     * 
     * @param inDto         リクエストパラメータ
     * @param outDto        レスポンスパラメータ
     * @param menu3Knj      機能名
     * @param securityList  セキュリティリス
     * @param initMasterObj 初期設定マスタの情報
     * @throws Exception
     */
    public <T extends GyoumuComInitMasterData> void getCommonInfo(GyoumuComInDto inDto, GyoumuComOutDto outDto,
            String menu3Knj, List<GyoumuComSecurityDto> securityList, T initMasterObj) throws Exception {

        // 次処理実行フラグが0:処理正常に設定する
        outDto.setContinue(true);
        // 共通部品の取得 共通部品「f_kgh_krk_get_syubetu」を利用し、種別ＩＤを取得する。
        Integer syubetuId = kghKrkZOther01Logic.getKghKrkSyubetu(CommonConstants.BLANK_STRING, menu3Knj,
                CommonConstants.BLANK_STRING);
        // 出力Dto: 種別ＩＤ
        outDto.setSyubetuId(CommonDtoUtil.objValToString(syubetuId));
        // 共通部品の取得 共通部品「f_kgh_krk_get_kikan」を利用し、期間管理フラグを取得する。
        boolean kikanFlg = kghKrkZSnc01Logic.getKghKrkKikan(syubetuId, CommonDtoUtil.strValToInt(inDto.getSvJigyoId()),
                CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 出力Dto: 期間管理フラグ
        outDto.setKikanFlg(kikanFlg ? CommonConstants.PERIOD_MANAGE_FLG : CommonConstants.PERIOD_NO_MANAGE_FLG);
        // 事業所名取得
        outDto.setSvJigyoRyakuKnj(svJigyoInfoLogic.getSvJigyoRyakuKnjById(
                CommonDtoUtil.strValToInt(inDto.getSvJigyoId())));
        List<String> authList = new ArrayList<>();
        if (securityList != null) {
            for (GyoumuComSecurityDto item : securityList) {
                // 共通部品「uf_use_kinou_chk」を利用し、セキュリティチェックを取得する。
                int chkResult = CommonConstants.INT_0;
                // セキュリティリスト.方式が"機能ＩＤ"の場合
                if (HOSHIKI_KINOU_ID.equals(item.getHoushiki())) {
                    chkResult = kghAdmSecurityLogic.chkUseKinou(CommonDtoUtil.strValToInt(inDto.getLoginShokuinId()),
                            CommonConstants.SYS_CD_71101,
                            CommonDtoUtil.strValToInt(inDto.getSvJigyoId()),
                            CommonDtoUtil.strValToInt(item.getKinou()), CommonConstants.BLANK_STRING);
                } else if (HOSHIKI_KINOU_NAME.equals(item.getHoushiki())) {
                    // セキュリティリスト.方式が"機能名"の場合
                    chkResult = kghAdmSecurityLogic.chkUseKinou(CommonDtoUtil.strValToInt(inDto.getLoginShokuinId()),
                            CommonConstants.SYS_CD_71101,
                            CommonDtoUtil.strValToInt(inDto.getSvJigyoId()),
                            item.getKinou());
                }
                // 権限が0の場合、「0:権限なし」
                authList.add(
                        chkResult == CommonConstants.INT_0 ? CommonConstants.AUTUFLG_NO : CommonConstants.AUTUFLG_YES);
            }
        }
        outDto.setAuthList(authList);
        // マスタ情報取得
        InitialSettingMasterSelectServiceInDto initInDto = new InitialSettingMasterSelectServiceInDto();
        initInDto.setSvJigyoId(inDto.getSvJigyoId());
        initInDto.setShokuinId(inDto.getLoginShokuinId());
        initInDto.setShisetuId(inDto.getShisetuId());
        InitialSettingMasterSelectServiceOutDto initOutDto = initialSettingMasterSelectServiceImpl
                .selectExecute(initInDto);
        // initMasterObjがTので、copyPropertiesを使用する
        BeanUtils.copyProperties(initOutDto, initMasterObj);
    }

    /**
     * 計画対象期間の取得
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     * @return 計画対象期間
     */
    public void getKrkKikanInfo(GyoumuComInDto inDto, GyoumuComOutDto outDto) {
        int index = -1;
        // 操作区分
        String operaFlg = inDto.getOperaFlg();
        String sc1 = inDto.getSc1Id();
        String syubetuId = outDto.getSyubetuId();

        KghTucKrkKikanByCriteriaInEntity kghTucKrkKikanByCriteriaInEntity = new KghTucKrkKikanByCriteriaInEntity();
        // 施設ＩＤ
        kghTucKrkKikanByCriteriaInEntity.setSId(Integer.valueOf(inDto.getShisetuId()));
        // 事業所ＩＤ
        kghTucKrkKikanByCriteriaInEntity.setJId(Integer.valueOf(inDto.getSvJigyoId()));
        // 種別ＩＤ
        kghTucKrkKikanByCriteriaInEntity.setSyubetsuId(Integer.valueOf((syubetuId)));
        // 利用者ＩＤ
        kghTucKrkKikanByCriteriaInEntity.setUId(Integer.valueOf(inDto.getUserId()));
        List<KghTucKrkKikanOutEntity> krkKikanInfoOutEntityList = kghTucKrkKikanSelectMapper
                .findKghTucKrkKikanByCriteria(kghTucKrkKikanByCriteriaInEntity);
        KghTucKrkKikanOutEntity kikanInfo = null;
        switch (operaFlg) {
            // 0:初期化、1:新規、K3:計画対象期間Openの場合
            case OPER_FLAG_0, OPER_FLAG_1, OPER_FLAG_K3:
                // 「計画対象期間」リストが1件以上の場合、インデックスの取得
                if (krkKikanInfoOutEntityList.size() > 0) {
                    // 「計画対象期間」リストにおいて、リクエストパラメータ.計画期間ＩＤに対応するデータがある場合、該当インデックスを設定
                    for (KghTucKrkKikanOutEntity entity : krkKikanInfoOutEntityList) {
                        if (sc1.equals(CommonDtoUtil.objValToString(entity.getSc1Id()))) {
                            // 該当エンティティ設定
                            kikanInfo = entity;
                            // 該当インデックス設定
                            index = krkKikanInfoOutEntityList.indexOf(entity);

                        }
                    }
                    // 「計画対象期間」リストにおいて、リクエストパラメータ.計画期間ＩＤに対応するデータがない場合、「0」を設定
                    if (index < 0) {
                        // インデックスが 「0」 を設定
                        index = 0;
                    }
                }
                break;
            // K1:計画対象期間prev、K2:計画対象期間nextの場合
            case OPER_FLAG_K1, OPER_FLAG_K2:
                // 「計画対象期間」リストが1件以上の場合、インデックスの取得
                if (krkKikanInfoOutEntityList.size() > 0) {
                    // 「計画対象期間」リストにおいて、リクエストパラメータ.計画期間ＩＤに対応するデータがある場合、該当インデックスを設定
                    for (KghTucKrkKikanOutEntity entity : krkKikanInfoOutEntityList) {
                        if (sc1.equals(CommonDtoUtil.objValToString(entity.getSc1Id()))) {
                            // 該当インデックス設定
                            index = krkKikanInfoOutEntityList.indexOf(entity);
                            // リクエストパラメータ.操作区分がK1:計画対象期間prevの場合
                            if (OPER_FLAG_K1.equals(operaFlg)) {
                                if (index == (krkKikanInfoOutEntityList.size() - 1)) {
                                    // レスポンスパラメータ.業務結果コード 1:「計画期間」先頭件取得エラー
                                    outDto.setResultCd(RET_ERR_CODE_1);
                                    // 「4. 計画対象期間の取得」処理終了
                                    outDto.setContinue(false);
                                    return;
                                } else {
                                    // インデックスが インデックス＋1 を設定
                                    index = index + 1;
                                    break;
                                }
                            } else {
                                // リクエストパラメータ.操作区分がK2:計画対象期間nextの場合
                                if (index == 0) {
                                    // レスポンスパラメータ.業務結果コード 2:「計画期間」最終件取得エラー
                                    outDto.setResultCd(RET_ERR_CODE_2);
                                    // 「4. 計画対象期間の取得」処理終了
                                    outDto.setContinue(false);
                                    return;
                                } else {
                                    // インデックスが インデックス－1 を設定
                                    index = index - 1;
                                }
                            }

                        }
                    }

                    // 「計画対象期間」リストにおいて、リクエストパラメータ.計画期間ＩＤに対応するデータがない場合、
                    // 且、リクエストパラメータ.操作区分がK1:計画対象期間prevの場合
                    if (index < 0 && OPER_FLAG_K1.equals(operaFlg)) {
                        // インデックスが「0」を設定
                        index = 0;
                    } else if (index < 0 && OPER_FLAG_K2.equals(operaFlg)) {
                        // 「計画対象期間」リストにおいて、リクエストパラメータ.計画期間ＩＤに対応するデータがない場合 、
                        // 且、 リクエストパラメータ.操作区分がK2:計画対象期間nextの場合
                        // 「4. 計画対象期間の取得」処理終了
                        outDto.setContinue(false);
                        return;
                    }

                } else {
                    // 「計画対象期間」リストが0件の場合
                    // 「4. 計画対象期間の取得」処理終了
                    outDto.setContinue(false);
                    return;
                }
                break;
            default:
                break;
        }
        // 表示用「計画対象期間」情報の設定
        KikanInfoDto kikanDto = new KikanInfoDto();
        // 表示用「計画対象期間」情報.ページング区分
        kikanDto.setPagingFlg(PAGE_FLAG_1.equals(outDto.getKikanFlg())
                ? krkKikanInfoOutEntityList.size() > 0 ? PAGE_FLAG_1 : PAGE_FLAG_0
                : PAGE_FLAG_1);
        if (index >= 0) {
            // インデックスが0以上の場合
            kikanInfo = krkKikanInfoOutEntityList.get(index);
            // 表示用「計画対象期間」情報.計画期間ＩＤ
            kikanDto.setSc1Id(String.valueOf(kikanInfo.getSc1Id()));
            // 表示用「計画対象期間」情報.開始日
            kikanDto.setStartYmd(kikanInfo.getStartYmd());
            // 表示用「計画対象期間」情報.終了日
            kikanDto.setEndYmd(kikanInfo.getEndYmd());
            // 表示用「計画対象期間」情報.更新回数
            // kikanDto.setModifiedCnt(String.valueOf(kikanInfo.getModifiedCnt()));
            // 表示用「計画対象期間」情報.ページング番号
            kikanDto.setPagingNo(String.valueOf(krkKikanInfoOutEntityList.size() - index));
            // 表示用「計画対象期間」情報.ページング総数
            kikanDto.setPagingCnt(String.valueOf(krkKikanInfoOutEntityList.size()));
        } else {
            // 以外の場合
            // 空白設定
            kikanDto.setSc1Id(CommonConstants.BLANK_STRING);
            kikanDto.setStartYmd(CommonConstants.BLANK_STRING);
            kikanDto.setEndYmd(CommonConstants.BLANK_STRING);
            kikanDto.setModifiedCnt(STR_COUNT_0);

            // 表示用「計画対象期間」情報.ページング番号
            kikanDto.setPagingNo(STR_COUNT_0);
            // 表示用「計画対象期間」情報.ページング総数
            kikanDto.setPagingCnt(STR_COUNT_0);
        }
        // リクエストパラメータ.操作区分がK1:計画対象期間prev、K2:計画対象期間nextの場合、かつレスポンスパラメータ.遷移保存確認区分が1:要の場合
        if ((OPER_FLAG_K1.equals(operaFlg) || OPER_FLAG_K2.equals(operaFlg)) && STR_COUNT_1.equals(
                inDto.getMoveSaveConfirmFlg())) {
            // レスポンスパラメータ.業務結果コード
            outDto.setResultCd(RET_CODE_8);
            // 次処理実行フラグ
            outDto.setContinue(false);
        }
        outDto.setKikanObj(kikanDto);
    }

    /**
     * 履歴情報の取得
     * 
     * @param inDto           inデータ
     * @param outDto          outデータ
     * @param getRirekiBefore 【業務共通化-履歴情報の取得】の実行前処理
     * @param getRirekiAfter  【業務共通化-履歴情報の取得】の実行後処理
     * @param rirekiInfo      履歴情報
     */
    public void getRirekiInfo(GyoumuComInDto inDto, GyoumuComOutDto outDto,
            Function<String, List<? extends RirekiInfoDto>> getRirekiBefore,
            BiFunction<? super RirekiInfoDto, Integer, Boolean> getRirekiAfter,
            RirekiInfoDto rirekiInfo) {

        // リクエストパラメータ.操作区分がR3:履歴Openの場合
        if (OPER_FLAG_R3.equals(inDto.getOperaFlg())) {
            // リクエストパラメータ.履歴ＩＤ
            rirekiInfo.setRirekiId(inDto.getRirekiId());
            getRirekiAfter.apply(rirekiInfo, null);
            return;
        }
        int index = -1;
        // 操作区分
        String operaFlg = inDto.getOperaFlg();
        String searchSc1Id = CommonConstants.BLANK_STRING;
        String rirekiId = inDto.getRirekiId();

        // 取得した期間管理フラグが1:期間管理する場合
        if (CommonConstants.PERIOD_MANAGE_FLG.equals(outDto.getKikanFlg())) {
            switch (operaFlg) {
                // リクエストパラメータ.操作区分が1:新規、3:削除、9:履歴以降再検索、R1:履歴prev、R2:履歴nextの場合
                case OPER_FLAG_1, OPER_FLAG_3, OPER_FLAG_9, OPER_FLAG_R1, OPER_FLAG_R2:
                    searchSc1Id = inDto.getSc1Id();
                    break;
                // リクエストパラメータ.操作区分が0:初期化、K1:計画対象期間prev、K2:計画対象期間next、K3:計画対象期間Openの場合
                case OPER_FLAG_0, OPER_FLAG_K1, OPER_FLAG_K2, OPER_FLAG_K3:
                    // 表示用「計画対象期間」情報.計画期間ＩＤ
                    searchSc1Id = outDto.getKikanObj().getSc1Id();
                    break;
                default:
                    break;
            }
        } else {
            // ※全検索
            searchSc1Id = STR_COUNT_0;
        }

        // 個別画面APIでの処理を行う
        // 【業務共通化-履歴情報の取得】の実行前処理
        List<? extends RirekiInfoDto> rirekiList = getRirekiBefore.apply(searchSc1Id);

        switch (operaFlg) {
            // リクエストパラメータ.操作区分が0:初期化、3:削除、9:履歴以降再検索、K1:計画対象期間prev、K2:計画対象期間next、K3:計画対象期間Openの場合
            case OPER_FLAG_0, OPER_FLAG_3, OPER_FLAG_9, OPER_FLAG_K1, OPER_FLAG_K2, OPER_FLAG_K3:
                // 業務共通用「履歴」リストが1件以上の場合
                if (rirekiList.size() > 0) {
                    // リクエストパラメータ.操作区分が3:削除の場合
                    if (OPER_FLAG_3.equals(operaFlg)) {
                        // リクエストパラメータ.削除再検索用履歴ページング番号が総件数より大きい場合
                        Integer pageNo = Integer.parseInt(inDto.getRirekiPagingNo());
                        if (pageNo > 0) {
                            // ※最新履歴を取得
                            index = 0;
                        } else {
                            index = rirekiList.size() - pageNo;
                        }
                    } else {
                        Optional<? extends RirekiInfoDto> matchingEntity = rirekiList
                                .stream()
                                .filter(x -> x.getRirekiId().equals(rirekiId))
                                .findFirst();
                        // リクエストパラメータ.履歴ＩＤに対応するデータのインデックスを取得する
                        if (matchingEntity.isPresent()) {
                            index = rirekiList.indexOf(matchingEntity.get());
                        } else {
                            // インデックスが 「0」 を設定
                            index = 0;
                        }
                    }
                }
                break;

            // リクエストパラメータ.操作区分がR1:履歴prev、R2:履歴nextの場合
            case OPER_FLAG_R1, OPER_FLAG_R2:
                // インデックスの取得
                if (rirekiList.size() <= 0) {
                    // 「5. 履歴情報の取得」処理終了
                    outDto.setContinue(false);
                    return;
                }
                Optional<? extends RirekiInfoDto> matchingEntity = rirekiList
                        .stream().filter(x -> x.getRirekiId().equals(rirekiId))
                        .findFirst();
                if (matchingEntity.isPresent()) {
                    // 該当インデックス設定
                    index = rirekiList.indexOf(matchingEntity.get());
                    // リクエストパラメータ.操作区分がR1:履歴prevの場合
                    if (OPER_FLAG_R1.equals(operaFlg)) {
                        // インデックスと（総件数 － 1）が一致する場合
                        if (index == rirekiList.size() - 1) {
                            // 「5. 履歴情報の取得」処理終了
                            outDto.setContinue(false);
                            return;
                        } else {
                            index += 1;
                        }
                    }
                    // リクエストパラメータ.操作区分がR2:履歴nextの場合
                    if (OPER_FLAG_R2.equals(operaFlg)) {
                        // インデックスと（総件数 － 1）が一致する場合
                        if (index == 0) {
                            // 「5. 履歴情報の取得」処理終了
                            outDto.setContinue(false);
                            return;
                        } else {
                            index -= 1;
                        }
                    }
                } else {
                    // リクエストパラメータ.操作区分がR1:履歴prevの場合
                    if (OPER_FLAG_R1.equals(operaFlg)) {
                        index = 0;
                    } else {
                        // 「5. 履歴情報の取得」処理終了
                        outDto.setContinue(false);
                        return;
                    }
                }

                break;
            default:
                break;
        }
        // インデックスが0以上の場合
        if (index >= 0) {
            rirekiInfo = rirekiList.get(index);
            // 業務共通用「履歴」情報.作成者
            rirekiInfo.setShokuKnj(shokuinInfoLogic.getShokuNameKnj(rirekiInfo.getShokuId()));
        } else {
            // 業務共通用「履歴」情報.履歴ＩＤ 空白
            rirekiInfo.setRirekiId(CommonConstants.BLANK_STRING);
            // 業務共通用「履歴」情報.作成者ＩＤ ログイン情報.職員ＩＤ
            rirekiInfo.setShokuId(inDto.getLoginShokuinId());
            // 業務共通用「履歴」情報.作成者 ※下記の名前取得を参照（職員名（姓） ＋ " " ＋ 職員名（名））
            rirekiInfo.setShokuKnj(shokuinInfoLogic.getShokuNameKnj(inDto.getLoginShokuinId()));
            // 業務共通用「履歴」情報.作成日 'リクエストパラメータ.システム年月日
            rirekiInfo.setCreateYmd(inDto.getSysYmd());
        }

        // 新規以外の場合
        if (!OPER_FLAG_1.equals(operaFlg)) {
            // インデックスが0以上の場合
            if (index >= 0) {
                // 業務共通用「履歴」リストの総件数 － インデックス
                rirekiInfo.setPagingNo(String.valueOf(rirekiList.size() - index));
                // 業務共通用「履歴」リストの総件数
                rirekiInfo.setPagingCnt(String.valueOf(rirekiList.size()));
            } else {
                // 業務共通用「履歴」情報.ページング番号
                rirekiInfo.setPagingNo(STR_COUNT_1);
                // 業務共通用「履歴」情報.ページング総数
                rirekiInfo.setPagingCnt(STR_COUNT_1);
            }
        }

        // リクエストパラメータ.操作区分がR1:履歴prev、R2:履歴nextの場合、かつレスポンスパラメータ.遷移保存確認区分が1:要の場合
        if ((OPER_FLAG_R1.equals(operaFlg) || OPER_FLAG_R2.equals(operaFlg)) && STR_COUNT_1.equals(
                inDto.getMoveSaveConfirmFlg())) {
            // レスポンスパラメータ.業務結果コード
            outDto.setResultCd(RET_CODE_8);
            // 次処理実行フラグ
            outDto.setContinue(false);
        }

        // 個別画面APIでの処理を行う
        // 【業務共通化-履歴情報の取得】の実行後処理
        getRirekiAfter.apply(rirekiInfo, index);
    }

    /**
     * 「総括の課題・目標」リストの取得
     * 
     * @param inDto            inデータ
     * @param searchRirekiId   検索用履歴ＩＤ
     * @param kadaiTorikomiFlg 課題取込セキュリティチェック
     * @return 「総括の課題・目標」リスト
     */
    public List<CpnTucSypAss4InfoOutEntity> getSummaryIssueGoal(GyoumuComInDto inDto, String searchRirekiId,
            String kadaiTorikomiFlg) {
        // 検索用履歴ＩＤが「空白、0」以外の場合、または上記3.で取得した課題取込セキュリティチェックが「0:権限なし」場合、またはリクエストパラメータ.操作区分がR3:履歴Openの場合
        if (!CommonConstants.BLANK_STRING.equals(searchRirekiId) && !STR_COUNT_0.equals(searchRirekiId)
                || AUTH_FLAG_0.equals(kadaiTorikomiFlg)
                || inDto.getOperaFlg().equals(OPER_FLAG_R3)) {
            return null;
        }

        CpnTucSypAss1InfoFilteruserIdByCriteriaInEntity cpnTucSypAss1InfoFilteruserIdByCriteriaInEntity = new CpnTucSypAss1InfoFilteruserIdByCriteriaInEntity();
        cpnTucSypAss1InfoFilteruserIdByCriteriaInEntity.setSc1(0);// ※全検索
        cpnTucSypAss1InfoFilteruserIdByCriteriaInEntity.setJId(Integer.parseInt(inDto.getSvJigyoId()));
        cpnTucSypAss1InfoFilteruserIdByCriteriaInEntity.setUId(Integer.parseInt(inDto.getUserId()));
        List<CpnTucSypAss1InfoFilteruserIdOutEntity> summaryList = cpnTucSypAss1SelectMapper
                .findCpnTucSypAss1InfoFilteruserIdByCriteria(
                        cpnTucSypAss1InfoFilteruserIdByCriteriaInEntity);
        // 上記取得した「アセスメント総括履歴」リストが0件の場合
        if (summaryList.size() == 0) {
            return null;
        }
        // 「アセスメント総括履歴」リスト.作成日がリクエストパラメータ.システム年月日以前の最新履歴のデータがあるかどうかを判断する
        Optional<CpnTucSypAss1InfoFilteruserIdOutEntity> matchingEntity = summaryList.stream()
                .filter(x -> x.getCreateYmd().compareTo(inDto.getSysYmd()) >= 0).findFirst();
        Integer assId = 0;
        if (matchingEntity.isPresent()) {
            assId = matchingEntity.get().getAssId();
        } else {
            return null;
        }
        CpnTucSypAss4InfoByCriteriaInEntity cpnTucSypAss4InfoByCriteriaInEntity = new CpnTucSypAss4InfoByCriteriaInEntity();
        cpnTucSypAss4InfoByCriteriaInEntity.setAssId(assId);
        return cpnTucSypAss4SelectMapper
                .findCpnTucSypAss4InfoByCriteria(cpnTucSypAss4InfoByCriteriaInEntity);
    }

    /**
     * 業務共通化-保存チェック
     * 
     * @param inDto    リクエストパラメータ
     * @param outDto   レスポンスパラメータ
     * @param menu3Knj 機能名
     * @throws Exception
     */
    public void checkCommon(GyoumuComCheckInDto inDto, String menu3Knj) throws Exception {
        // 2.2. 電子保存の3原則
        // 2.2.1. 共通部品「f_kgh_com_get_e_bunsho_kbn」を利用し、文章法区分を取得する。
        boolean eBunshoKbn = kghSmglHistoryLogic.getKghComEBunshoKbn();
        // 2.2.2 文章法区分がTRUEの場合、
        if (eBunshoKbn) {
            // TODO #3904 共通部品「uf_smgl_chk_ymd」を利用し、「電子保存の3原則」の既存履歴番号変更を取得する。
            // 「電子保存の3原則」の既存履歴番号変更の結果がtrueの場合、
            // 「電子保存の3原則」の既存履歴番号変更が1:変更有を返却する。以降の処理を行わず。
            // 以外の場合、
            // 「電子保存の3原則」の既存履歴番号変更が0:変更無
        }
    }

    /**
     * 業務共通化-サービス行表示用情報の取得
     * 
     * @param inDto            inデータ
     * @param searchRirekiId   検索用履歴ＩＤ
     * @param kadaiTorikomiFlg 課題取込セキュリティチェック
     * @return 「総括の課題・目標」リスト
     */
    public List<HokenSvOutDto> getServiceDisplayInfo(InsuranceServicesResetSelectServiceInDto inDto) {
        List<HokenSvOutDto> outList = new ArrayList<HokenSvOutDto>();
        for (HokenSvDto hokenSv : inDto.getHokenSvList()) {
            HokenSvOutDto newHokenSvOut = new HokenSvOutDto();
            // 曜日
            newHokenSvOut.setYoubi(hokenSv.getYoubi());
            // 週単位以外のサービス区分
            newHokenSvOut.setIgaiKbn(hokenSv.getIgaiKbn());
            // 週単位以外のサービス（日付指定）
            newHokenSvOut.setIgaiDate(hokenSv.getIgaiDate());
            // 週単位以外のサービス（曜日指定）
            newHokenSvOut.setIgaiWeek(hokenSv.getIgaiWeek());
            // 居宅：開始時間
            newHokenSvOut.setKaishiJikan(hokenSv.getKaishiJikan());
            // 居宅：終了時間
            newHokenSvOut.setShuuryouJikan(hokenSv.getShuuryouJikan());
            // 居宅：サービス種類
            newHokenSvOut.setSvShuruiCd(hokenSv.getSvShuruiCd());
            // 居宅：サービス項目（台帳）
            newHokenSvOut.setSvItemCd(hokenSv.getSvItemCd());
            // 居宅：サービス事業者CD
            newHokenSvOut.setSvJigyoId(hokenSv.getSvJigyoId());
            // 居宅：福祉用具貸与単位
            newHokenSvOut.setTanka(hokenSv.getTanka());
            // 福祉用具貸与商品コード
            newHokenSvOut.setFygId(hokenSv.getFygId());
            // 合成識別区分
            newHokenSvOut.setGouseiSikKbn(hokenSv.getGouseiSikKbn());
            // 加算フラグ
            newHokenSvOut.setKasanFlg(hokenSv.getKasanFlg());
            // 親レコード番号
            newHokenSvOut.setOyaLineNo(hokenSv.getOyaLineNo());
            // ダミーID
            newHokenSvOut.setDmySvId(hokenSv.getDmySvId());
            // ID
            newHokenSvOut.setSvId(hokenSv.getSvId());
            // 更新回数
            newHokenSvOut.setModifiedCnt(hokenSv.getModifiedCnt());
            // 更新区分
            newHokenSvOut.setUpdateKbn(
                    CommonConstants.BLANK_STRING.equals(newHokenSvOut.getSvId()) ? CommonConstants.UPDATE_KBN_C
                            : CommonConstants.BLANK_STRING);
            // 下記項目を初期化する
            newHokenSvOut.setDmySvShuruiKnj("");
            newHokenSvOut.setDmySvJigyoKnj("");
            newHokenSvOut.setDmyItemname("");
            newHokenSvOut.setDmyTime("");
            newHokenSvOut.setDmyWeek("");
            newHokenSvOut.setItemCode("");
            // サービス事業者名称取得
            SabisuJigyoshaByCriteriaInEntity sabisuJigyoshaByCriteriaIn = new SabisuJigyoshaByCriteriaInEntity();
            // サービス事業者ID
            sabisuJigyoshaByCriteriaIn.setLlSvJigyoId(hokenSv.getSvJigyoId());
            List<SabisuJigyoshaOutEntity> sabisuJigyoshaList = comMscSvjigyoSelectMapper
                    .findSabisuJigyoshaByCriteria(sabisuJigyoshaByCriteriaIn);
            if (sabisuJigyoshaList.size() > 0) {
                if (CommonConstants.ITAKU_KKAK_HS_JIGYO_FLG_0.equals(inDto.getHsJigyoFlg())) {
                    // OUTPUT情報.事業名を設定
                    newHokenSvOut.setDmySvShuruiKnj(sabisuJigyoshaList.get(0).getJigyoKnj());
                } else if (CommonConstants.ITAKU_KKAK_HS_JIGYO_FLG_1.equals(inDto.getHsJigyoFlg())) {
                    // OUTPUT情報.事業名（略称）を設定
                    newHokenSvOut.setDmySvShuruiKnj(sabisuJigyoshaList.get(0).getJigyoRyakuKnj());
                }
            }
            // サービス種別を取得
            RuakuKnjByCriteriaInEntity ruakuKnjByCriteriaIn = new RuakuKnjByCriteriaInEntity();
            if (sabisuJigyoshaList.size() > 0) {
                ruakuKnjByCriteriaIn.setLsSvJigyoCd(sabisuJigyoshaList.get(0).getSvJigyoCd());
            } else if (CommonDtoUtil.strValToInt(hokenSv.getSvShuruiCd()) > 0) {
                ruakuKnjByCriteriaIn.setLsSvJigyoCd(hokenSv.getSvShuruiCd());
            }
            List<RuakuKnjOutEntity> ruakuKnjList = comMscSvjigyoNameSelectMapper
                    .findRuakuKnjByCriteria(ruakuKnjByCriteriaIn);
            if (ruakuKnjList.size() > 0) {
                // サービス種別（表示用）の設定
                newHokenSvOut.setDmySvJigyoKnj(ruakuKnjList.get(0).getRuakuKnj());
            }
            // サービス内容（表示用）の取得
            newHokenSvOut
                    .setDmyItemname(getSvItemName(inDto.getItemId(), hokenSv.getSvJigyoId(), hokenSv.getSvItemCd()));
            // サービス時間（表示用）の取得
            if (!((CommonConstants.KAISHI_JIKAN_7.equals(hokenSv.getKaishiJikan())
                    && CommonConstants.SHUURYOU_JIKAN_7.equals(hokenSv.getShuuryouJikan())) ||
                    (CommonConstants.KAISHI_JIKAN_8.equals(hokenSv.getKaishiJikan())
                            && CommonConstants.SHUURYOU_JIKAN_8.equals(hokenSv.getShuuryouJikan()))
                    || (CommonConstants.KAISHI_JIKAN_9.equals(hokenSv.getKaishiJikan())
                            && CommonConstants.SHUURYOU_JIKAN_9.equals(hokenSv.getShuuryouJikan())))) {
                newHokenSvOut.setDmyTime(
                        hokenSv.getKaishiJikan() + CommonConstants.RANGE_SEPARATOR + hokenSv.getShuuryouJikan());
            }
            // サービス曜日（表示用）の取得
            // リクエストパラメータ.週単位以外のサービス区分が 3 の場合
            if (CommonConstants.IGAI_KBN_STR_THREE.equals(hokenSv.getIgaiKbn())) {
                // リクエストパラメータ.月日指定リストから下記の条件で対象レコードを取得する。
                List<HokenYmdDto> cyrrentHokenYmdList = inDto.getHokenYmdList().stream()
                        .filter((x) -> hokenSv.getDmySvId().equals(x.getDmySvId())).toList();
                String dmyWeek = "";
                for (HokenYmdDto hokenYmd : cyrrentHokenYmdList) {
                    if (dmyWeek.equals("")) {
                        dmyWeek = hokenYmd.getStartYmd() + CommonConstants.RANGE_SEPARATOR
                                + hokenYmd.getEndYmd();
                    } else {
                        dmyWeek += CommonConstants.STRING_COMMA + hokenYmd.getStartYmd()
                                + CommonConstants.RANGE_SEPARATOR
                                + hokenYmd.getEndYmd();
                    }
                }
                newHokenSvOut.setDmyWeek(dmyWeek);
                // 上記以外の場合
            } else {
                String dmyWeek = "";
                // リクエストパラメータ.頻度取込 = 0:選択項目名称の場合
                if (CommonConstants.ITAKU_KKAK_HINDO_FLG_0.equals(inDto.getHindoFlg())) {
                    switch (hokenSv.getYoubi()) {
                        // リクエストパラメータ.曜日 = "0000000"の場合
                        case CommonConstants.YOUBI_0:
                            break;
                        // リクエストパラメータ.曜日 = "1111111"の場合
                        case CommonConstants.YOUBI_1:
                            dmyWeek = CommonConstants.EVERYDAY_STR;
                            break;
                        case CommonConstants.YOUBI_9:
                            // リクエストパラメータ.週単位以外のサービス区分 = 1:日付指定 の場合
                            if (CommonConstants.IGAI_KBN_STR_ONE.equals(hokenSv.getIgaiKbn())) {
                                dmyWeek = kghKrkZCpnFuncLogic.getWeekDateToMoji(hokenSv.getIgaiDate());
                                // リクエストパラメータ.週単位以外のサービス区分 = 2:曜日指定 の場合
                            } else if (CommonConstants.IGAI_KBN_STR_TWO.equals(hokenSv.getIgaiKbn())) {
                                dmyWeek = kghKrkZCpnFuncLogic.getWeekToMoji(hokenSv.getIgaiWeek());
                            }
                            break;
                        // 上記以外の場合（週単位）
                        default:
                            for (int i = 0; i < hokenSv.getYoubi().length(); i++) {
                                if (CommonConstants.STR_1.equals(String.valueOf(hokenSv.getYoubi().charAt(i)))) {
                                    if (dmyWeek.equals("")) {
                                        dmyWeek = CommonConstants.YOUBI_NAMES[i];
                                    } else {
                                        dmyWeek += CommonConstants.CHARACTOR_CONNECTOR + CommonConstants.YOUBI_NAMES[i];
                                    }
                                }
                            }
                            break;
                    }
                    // リクエストパラメータ.頻度取込 = 1:週〇回／月〇回の場合
                } else if (CommonConstants.ITAKU_KKAK_HINDO_FLG_1.equals(inDto.getHindoFlg())) {
                    // リクエストパラメータ.週単位以外のサービス区分 = 1:日付指定 の場合
                    if (CommonConstants.IGAI_KBN_STR_ONE.equals(hokenSv.getIgaiKbn())) {
                        Integer counts = StringUtils.countMatches(hokenSv.getIgaiDate(), CommonConstants.STR_1);
                        dmyWeek = CommonConstants.MONTH_STR + counts + CommonConstants.TIMES_STR;
                        // リクエストパラメータ.週単位以外のサービス区分 = 2:曜日指定 の場合
                    } else if (CommonConstants.IGAI_KBN_STR_TWO.equals(hokenSv.getIgaiKbn())) {
                        Integer counts = StringUtils.countMatches(hokenSv.getIgaiWeek(), CommonConstants.STR_1);
                        dmyWeek = CommonConstants.MONTH_STR + counts + CommonConstants.TIMES_STR;
                    } else {
                        Integer counts = StringUtils.countMatches(hokenSv.getYoubi(), CommonConstants.STR_1);
                        dmyWeek = CommonConstants.WEEK_STR + counts + CommonConstants.TIMES_STR;
                    }
                    // リクエストパラメータ.頻度取込 = 2:随時の場合
                } else if (CommonConstants.ITAKU_KKAK_HINDO_FLG_2.equals(inDto.getHindoFlg())) {
                    dmyWeek = CommonConstants.ANYTIME_STR;
                }
                newHokenSvOut.setDmyWeek(dmyWeek);
            }
            // 項目識別コードの取得
            // リクエストパラメータ.加算フラグは1:加算サービスの場合、項目識別コードの取得
            if (CommonConstants.KASAN_FLG_1.equals(hokenSv.getKasanFlg())) {
                ScodeByCriteriaInEntity scodeByCriteriaInEntity = new ScodeByCriteriaInEntity();
                // 項目コード
                scodeByCriteriaInEntity.setAlItm(hokenSv.getSvItemCd());
                // 有効期間ID
                scodeByCriteriaInEntity.setLlTerm(inDto.getItemId());
                List<ScodeOutEntity> scodeList = comMhcItemSelectMapper.findScodeByCriteria(scodeByCriteriaInEntity);
                if (scodeList.size() > 0) {
                    newHokenSvOut.setItemCode(scodeList.get(0).getScode());
                }
            }
            outList.add(newHokenSvOut);
        }
        return outList;
    }

    /**
     * 業務共通化-サービス項目名の取得
     * 
     * @param itemId    有効期間ID
     * @param svJigyoId サービス事業者ID
     * @param svItemCd  サービス項目
     * @return サービス項目名
     */
    public String getSvItemName(String itemId, String svJigyoId, String svItemCd) {
        String itemKnj = "";
        // 事業所区分を取得
        String svJigyoCd = kghCmnF01Logic.getSvJigyoCd(CommonDtoUtil.strValToInt(svJigyoId));
        TermId2DateLogicOutDto termId2DateLogicOutDto = kghKrkZCpnFuncLogic
                .getTermid2date(CommonDtoUtil.strValToInt(itemId), "", "");
        // 項目カウントを設定
        Integer itemCount = null;
        if (TARGET_SERVICE_CODES.contains(svJigyoCd)) {
            itemCount = 0;
        } else {
            ItemuseSuByCriteriaInEntity itemuseSuByCriteriaIn = new ItemuseSuByCriteriaInEntity();
            // 事業者ID
            itemuseSuByCriteriaIn.setAlSvJigyoId(svJigyoId);
            // 項目コード
            itemuseSuByCriteriaIn.setAlSvItemCd(svItemCd);
            // 開始日
            itemuseSuByCriteriaIn.setLsSymd(termId2DateLogicOutDto.getSYmd());
            // 終了日
            itemuseSuByCriteriaIn.setLsEymd(termId2DateLogicOutDto.getEYmd());
            ItemuseSuOutEntity itemuseSu = comMhcItemuseSelectMapper.countItemuseSuByCriteria(itemuseSuByCriteriaIn);
            itemCount = itemuseSu.getCnt();
        }
        // サービス項目名称を取得
        // 検索用事業者ID
        String searchSvJigyoId = svJigyoId;
        // 項目カウント = 0 の場合
        if (itemCount != null && itemCount.equals(0)) {
            searchSvJigyoId = "0";
        }
        // 事業所区分が "A1010"、又は、 "A5013"、又は、 "A9010"(介護予防・日常生活支援総合事業事業所)の場合
        if (TARGET_SERVICE_CODES.contains(svJigyoCd)) {
            KghKkak2SvItemSougouByCriteriaInEntity kghKkak2SvItemSougouByCriteriaIn = new KghKkak2SvItemSougouByCriteriaInEntity();
            // 項目コード
            kghKkak2SvItemSougouByCriteriaIn.setAnItemcode(CommonDtoUtil.strValToInt(svItemCd));
            // 開始日
            kghKkak2SvItemSougouByCriteriaIn.setAsSYmd(termId2DateLogicOutDto.getSYmd());
            // 終了日
            kghKkak2SvItemSougouByCriteriaIn.setAsEYmd(termId2DateLogicOutDto.getEYmd());
            List<KghKkak2SvItemSougouOutEntity> kghKkak2SvItemSougouList = comMhcItemuseSougouSelectMapper
                    .findKghKkak2SvItemSougouByCriteria(kghKkak2SvItemSougouByCriteriaIn);
            // 最後件のデータ.正式名を取得して、戻り値.サービス項目名に設定する
            if (kghKkak2SvItemSougouList.size() > 0) {
                itemKnj = kghKkak2SvItemSougouList.get(kghKkak2SvItemSougouList.size() -
                        1).getFormalnameKnj();
            }
        } else {
            ComMhcItemuseNameByCriteriaInEntity comMhcItemuseNameByCriteriaIn = new ComMhcItemuseNameByCriteriaInEntity();
            // 事業者ID
            comMhcItemuseNameByCriteriaIn.setSvJigyoId(CommonDtoUtil.strValToInt(searchSvJigyoId));
            // 項目コード
            comMhcItemuseNameByCriteriaIn.setItemcode(CommonDtoUtil.strValToInt(svItemCd));
            // 開始日
            comMhcItemuseNameByCriteriaIn.setSYmd(termId2DateLogicOutDto.getSYmd());
            // 終了日
            comMhcItemuseNameByCriteriaIn.setEYmd(termId2DateLogicOutDto.getEYmd());
            List<ComMhcItemuseNameOutEntity> comMhcItemuseNameList = comMhcItemuseSelectMapper
                    .findComMhcItemuseNameByCriteria(
                            comMhcItemuseNameByCriteriaIn);
            // 最後件のデータ.正式名を取得して、戻り値.サービス項目名に設定する
            if (comMhcItemuseNameList.size() > 0) {
                itemKnj = comMhcItemuseNameList.get(comMhcItemuseNameList.size() - 1).getFormalnameKnj();
            }
        }
        return itemKnj;
    }

    /**
     * 「課題検討の総合方針」リストの取得
     * 
     * @param inDto            inデータ
     * @param searchRirekiId   検索用履歴ＩＤ
     * @param kadaiTorikomiFlg 課題取込セキュリティチェック
     * @return 「総括の課題・目標」リスト
     */
    public List<CpnTucMygKky2InfoOutEntity> getSummaryHoushinList(GyoumuComInDto inDto, String searchRirekID,
            String mstKnb) {

        // 検索用履歴ＩＤが「空白、0」以外の場合、またはリクエストパラメータ.操作区分がR3:履歴Openの場合
        if (!CommonConstants.BLANK_STRING.equals(searchRirekID) && STR_COUNT_0.equals(searchRirekID)) {
            return null;
        } else if (OPER_FLAG_R3.equals(inDto.getOperaFlg())) {
            return null;
        }

        CpnTucMygKky1InfoFilterMstKbnByCriteriaInEntity inEntity = new CpnTucMygKky1InfoFilterMstKbnByCriteriaInEntity();
        // 検索用計画期間ＩＤ(0:全検索)
        inEntity.setSc1Id(0);
        // リクエストパラメータ.事業所ＩＤ
        inEntity.setJId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // リクエストパラメータ.利用者ＩＤ
        inEntity.setUId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 計画書様式（1:施設、2:居宅）
        inEntity.setMstKbn(CommonDtoUtil.strValToInt(mstKnb));
        List<CpnTucMygKky1InfoFilterMstKbnOutEntity> outEntityList = cpnTucMygKky1SelectMapper
                .findCpnTucMygKky1InfoFilterMstKbnByCriteria(inEntity);

        if (outEntityList.size() > 0) {
            CpnTucMygKky2InfoByCriteriaInEntity inEntity2 = new CpnTucMygKky2InfoByCriteriaInEntity();
            // 課題検討ＩＤ
            inEntity2.setKky1Id(outEntityList.get(0).getKky1Id());
            // 期間区分
            inEntity2.setIKkak(CommonDtoUtil.strValToInt(STR_COUNT_1));
            return cpnTucMygKky2SelectMapper
                    .findCpnTucMygKky2InfoByCriteria(inEntity2);
        } else {
            return null;
        }

    }

    /**
     * 業務共通化-複写の計画対象期間の取得
     * 
     * @param inDto  inデータ
     * @param outDto outデータ
     */
    public <T extends GyoumuComCopyRirekiInfoDto> void getKrkKikanListOfCopy(GyoumuComCopyInDto inDto,
            GyoumuComCopyOutDto<T> outDto) {
        // 「計画対象期間」リストを取得
        KrkComKikanSvjArrayByCriteriaInEntity krkComKikanSvjArrayByCriteriaInEntity = new KrkComKikanSvjArrayByCriteriaInEntity();
        // リクエストパラメータ.適用事業所ＩＤ
        krkComKikanSvjArrayByCriteriaInEntity.setSvJigyoIdList(inDto.getSvJigyoIdList().stream().map(x -> {
            return CommonDtoUtil.strValToInt(x);
        }).collect(Collectors.toList()));
        // リクエストパラメータ.利用者ＩＤ
        krkComKikanSvjArrayByCriteriaInEntity.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // リクエストパラメータ.種別ＩＤ
        krkComKikanSvjArrayByCriteriaInEntity.setSyubetsuId(CommonDtoUtil.strValToInt(inDto.getSyubetuId()));
        // リクエストパラメータ.施設ＩＤ
        krkComKikanSvjArrayByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        List<KrkComKikanSvjArrayOutEntity> kikanSvjArrayOutEntities = kikanSentakuSelectMapper
                .findKrkComKikanSvjArrayByCriteria(krkComKikanSvjArrayByCriteriaInEntity);
        outDto.setKikanList(kikanSvjArrayOutEntities.stream().map(x -> {
            GyoumuComCopyKikanInfoDto data = new GyoumuComCopyKikanInfoDto();
            // 期間ＩＤ
            data.setSc1Id(CommonDtoUtil.objValToString(x.getSc1Id()));
            // 事業所ＩＤ
            data.setSvJigyoId(CommonDtoUtil.objValToString(x.getSvJigyoId()));
            // 開始日
            data.setStartYmd(x.getStartYmd());
            // 終了日
            data.setEndYmd(x.getEndYmd());
            // 事業名（略称）
            data.setJigyoRyakuKnj(x.getJigyoRyakuKnj());

            return data;
        }).collect(Collectors.toList()));
    }

    /**
     * 業務共通化-複写の履歴情報の取得
     * 
     * @param inDto           inデータ
     * @param outDto          outデータ
     * @param getRirekiBefore 【業務共通化-履歴情報の取得】の実行前処理
     */
    public <T extends GyoumuComCopyRirekiInfoDto> void getRirekiListOfCopy(
            GyoumuComCopyInDto inDto,
            GyoumuComCopyOutDto<T> outDto,
            BiFunction<String, String, List<T>> getRirekiBefore) {
        // 操作区分が0:初期化の場合のみ、履歴数を取得する
        if (OPER_FLAG_0.equals(inDto.getOperaFlg())) {
            // リクエストパラメータ.期間管理フラグが1:期間管理する場合
            if (CommonConstants.PERIOD_MANAGE_FLG.equals(inDto.getKikanFlg())) {
                for (GyoumuComCopyKikanInfoDto kikan : outDto.getKikanList()) {
                    // 表示用「計画期間」リスト.期間ＩＤ
                    // 表示用「計画期間」リスト.事業所ＩＤ
                    List<T> resultList = getRirekiBefore.apply(kikan.getSc1Id(), kikan.getSvJigyoId());
                    // 表示用「計画期間」リスト.期間内履歴数
                    kikan.setRirekiCnt(CommonDtoUtil.objValToString(resultList.size()));
                }
            }
        }
        List<T> rirekiList = null;
        // 操作区分が0:初期化の場合
        if (OPER_FLAG_0.equals(inDto.getOperaFlg())) {
            if (outDto.getKikanList() == null || outDto.getKikanList().size() == 0) {
                return;
            }
            // リクエストパラメータ.期間管理フラグが1:期間管理する場合
            if (CommonConstants.PERIOD_MANAGE_FLG.equals(inDto.getKikanFlg())) {
                // 表示用「計画期間」リスト取得の1件目.期間ＩＤ
                // 表示用「計画期間」リスト取得の1件目.事業所ＩＤ
                rirekiList = getRirekiBefore.apply(outDto.getKikanList().get(0).getSc1Id(),
                        outDto.getKikanList().get(0).getSvJigyoId());
            } else {
                // ※全検索
                // 表示用「計画期間」リスト取得の1件目.事業所ＩＤ
                rirekiList = getRirekiBefore.apply(CommonConstants.STR_0,
                        outDto.getKikanList().get(0).getSvJigyoId());
            }
        } else if (OPER_FLAG_1.equals(inDto.getOperaFlg())) {
            // 操作区分が1:期間選択の場合
            rirekiList = getRirekiBefore.apply(inDto.getSc1Id(), inDto.getSvJigyoId());
        }
        if (rirekiList != null) {
            for (T dto : rirekiList) {
                // ※下記の名前取得を参照（職員名（姓） ＋ " " ＋ 職員名（名））
                dto.setShokuKnj(shokuinInfoLogic.getShokuNameKnj(dto.getShokuId()));
            }
        }
        outDto.setRirekiList(rirekiList);
    }

    /**
     * 業務共通化-期間管理フラグの取得
     * 
     * @param inDto    inデータ
     * @param menu3Knj 機能名
     * @return 期間管理フラグ
     */
    public String getKikan(GyoumuComPrintSettingsInDto inDto, String menu3Knj) {
        // 共通部品「f_kgh_krk_get_syubetu」を利用し、種別IDを取得する。
        Integer syubetuId = kghKrkZOther01Logic.getKghKrkSyubetu(CommonConstants.BLANK_STRING, menu3Knj,
                CommonConstants.BLANK_STRING);
        // 共通部品「f_kgh_krk_get_kikan」を利用し、期間管理フラグを取得する。
        boolean kikanFlg = kghKrkZSnc01Logic.getKghKrkKikan(syubetuId, CommonDtoUtil.strValToInt(inDto.getSvJigyoId()),
                CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        return kikanFlg ? CommonConstants.PERIOD_MANAGE_FLG : CommonConstants.PERIOD_NO_MANAGE_FLG;
    }

    /**
     * 業務共通化-印刷設定情報リストの取得
     * 
     * @param inDto inデータ
     * @return 印刷設定情報リスト
     */
    public List<GyoumuComPrintSettingsDto> getPrtList(GyoumuComPrintSettingsInDto inDto) {
        List<GyoumuComPrintSettingsDto> list = null;
        ComMocPrtiniByCriteriaInEntity comMocPrtiniByCriteriaInEntity = new ComMocPrtiniByCriteriaInEntity();
        // "3GK"
        comMocPrtiniByCriteriaInEntity.setSysRyaku(CommonConstants.SYS_CD_KBN_3GK);
        // リクエストパラメータ.セクション名
        comMocPrtiniByCriteriaInEntity.setSectionName(inDto.getSectionName());
        List<ComMocPrtiniOutEntity> comMocPrtiniOutEntities = comMocPrtiniSelectMapper
                .findComMocPrtiniByCriteria(comMocPrtiniByCriteriaInEntity);
        // 処理「3.1.」で取得した件数は1以上の場合
        if (!CollectionUtils.isNullOrEmpty(comMocPrtiniOutEntities)) {
            List<ComMocPrtiniSOutEntity> comMocPrtiniSOutEntities = getPrintList(inDto,
                    comMocPrtiniOutEntities.get(0).getSectionNo());
            AtomicInteger index = new AtomicInteger(0);
            list = comMocPrtiniOutEntities.stream().map(x -> {
                GyoumuComPrintSettingsDto data = new GyoumuComPrintSettingsDto();
                // セクション番号
                data.setSectionNo(x.getSectionNo());
                // 帳票番号
                data.setPrtNo(CommonDtoUtil.objValToString(x.getPrtNo()));
                // セクション名
                data.setSectionName(x.getSectionName());
                // 帳票リスト名
                data.setListTitle(x.getListTitle());
                // 帳票タイトル
                data.setDefPrtTitle(x.getDefPrtTitle());
                // 日付表示有無
                data.setPrnDate(CommonDtoUtil.objValToString(x.getDefPrndate()));
                // 職員表示有無
                data.setPrnshoku(CommonDtoUtil.objValToString(x.getDefPrnshoku()));
                // パラメータ01
                data.setParam01(x.getParam01());
                // パラメータ02
                data.setParam02(x.getParam02());
                // パラメータ03
                data.setParam03(x.getParam03());
                // パラメータ04
                data.setParam04(x.getParam04());
                // パラメータ05
                data.setParam05(x.getParam05());
                // パラメータ06
                data.setParam06(x.getParam06());
                // パラメータ07
                data.setParam07(x.getParam07());
                // パラメータ08
                data.setParam08(x.getParam08());
                // パラメータ09
                data.setParam09(x.getParam09());
                // パラメータ10
                data.setParam10(x.getParam10());
                // パラメータ11
                data.setParam11(x.getParam11());
                // パラメータ12
                data.setParam12(x.getParam12());
                // パラメータ13
                data.setParam13(x.getParam13());
                // パラメータ14
                data.setParam14(x.getParam14());
                // パラメータ15
                data.setParam15(x.getParam15());
                // パラメータ16
                data.setParam16(x.getParam16());
                // パラメータ17
                data.setParam17(x.getParam17());
                // パラメータ18
                data.setParam18(x.getParam18());
                // パラメータ19
                data.setParam19(x.getParam19());
                // パラメータ20
                data.setParam20(x.getParam20());
                // パラメータ21
                data.setParam21(x.getParam21());
                // パラメータ22
                data.setParam22(x.getParam22());
                // パラメータ23
                data.setParam23(x.getParam23());
                // パラメータ24
                data.setParam24(x.getParam24());
                // パラメータ25
                data.setParam25(x.getParam25());
                // パラメータ26
                data.setParam26(x.getParam26());
                // パラメータ27
                data.setParam27(x.getParam27());
                // パラメータ28
                data.setParam28(x.getParam28());
                // パラメータ29
                data.setParam29(x.getParam29());
                // パラメータ30
                data.setParam30(x.getParam30());
                // パラメータ31
                data.setParam31(x.getParam31());
                // パラメータ32
                data.setParam32(x.getParam32());
                // パラメータ33
                data.setParam33(x.getParam33());
                // パラメータ34
                data.setParam34(x.getParam34());
                // パラメータ35
                data.setParam35(x.getParam35());
                // パラメータ36
                data.setParam36(x.getParam36());
                // パラメータ37
                data.setParam37(x.getParam37());
                // パラメータ38
                data.setParam38(x.getParam38());
                // パラメータ39
                data.setParam39(x.getParam39());
                // パラメータ40
                data.setParam40(x.getParam40());
                // パラメータ41
                data.setParam41(x.getParam41());
                // パラメータ42
                data.setParam42(x.getParam42());
                // パラメータ43
                data.setParam43(x.getParam43());
                // パラメータ44
                data.setParam44(x.getParam44());
                // パラメータ45
                data.setParam45(x.getParam45());
                // パラメータ46
                data.setParam46(x.getParam46());
                // パラメータ47
                data.setParam47(x.getParam47());
                // パラメータ48
                data.setParam48(x.getParam48());
                // パラメータ49
                data.setParam49(x.getParam49());
                // パラメータ50
                data.setParam50(x.getParam50());

                // 更新回数
                // data.setModifiedCnt(CommonDtoUtil.objValToString(x.getModifiedCnt()));
                // ・印刷設定情報リスト.インデックス＝1から連番
                data.setIndex(CommonDtoUtil.objValToString(index.incrementAndGet()));

                // ・印刷設定情報リスト.帳票名＝該当レコード.帳票タイトル
                data.setPrtTitle(x.getDefPrtTitle());
                // ・印刷設定情報リスト.プロファイル＝"3GK" + 該当レコード.セクション番号+ "P" ＋
                // 該当レコード.帳票番号の3桁(3桁未満の場合、先頭に0を埋める)
                data.setProfile(CommonConstants.SYS_CD_KBN_3GK.concat(x.getSectionNo()).concat(STR_P)
                        .concat(String.format("%03d", x.getPrtNo())));
                // 該当レコード.帳票番号と一致するレコードがある場合、
                Optional<ComMocPrtiniSOutEntity> matchingEntity = comMocPrtiniSOutEntities.stream()
                        .filter(y -> y.getPrtNo().equals(x.getPrtNo())).findFirst();
                if (matchingEntity.isPresent()) {
                    ComMocPrtiniSOutEntity entity = matchingEntity.get();
                    // ・印刷設定保存用情報.帳票タイトルは空以外の場合、印刷設定情報リスト.帳票タイトル＝印刷設定保存用情報.帳票タイトル
                    if (StringUtil.isNotEmpty(entity.getPrtTitle())) {
                        data.setPrtTitle(entity.getPrtTitle());
                    }
                    // ・印刷設定保存用情報.日付表示有無は空の場合、印刷設定情報リスト.日付表示有無＝2
                    if (entity.getPrndate() == null) {
                        data.setPrnDate(CommonConstants.PRN_DATE_DESIGNATED);
                    } else {
                        // ・印刷設定保存用情報.日付表示有無は空以外の場合、印刷設定情報リスト.日付表示有無＝印刷設定保存用情報.日付表示有無
                        data.setPrnDate(CommonDtoUtil.objValToString(entity.getPrndate()));
                    }
                    // ・印刷設定保存用情報.職員表示有無は空の場合、印刷設定情報リスト.職員表示有無＝1
                    if (entity.getPrnshoku() == null) {
                        data.setPrnshoku(CommonConstants.PRN_SHOKU_EXIST);
                    } else {
                        // ・印刷設定保存用情報.職員表示有無は空以外の場合、印刷設定情報リスト.職員表示有無＝印刷設定保存用情報.職員表示有無
                        data.setPrnshoku(CommonDtoUtil.objValToString(entity.getPrnshoku()));
                    }
                    // ・印刷設定保存用情報.パラメータ01～パラメータ50は空以外の場合、印刷設定情報リスト.パラメータ01～パラメータ50=印刷設定保存用情報.パラメータ01～パラメータ50
                    for (int i = 0; i < 50; i++) {
                        try {
                            Field field = ComMocPrtiniSOutEntity.class
                                    .getDeclaredField("param" + String.format("%02d", i + 1));

                            Field fieldTarget = GyoumuComPrintSettingsSaveDto.class
                                    .getDeclaredField("param" + String.format("%02d", i + 1));
                            field.setAccessible(true);
                            fieldTarget.setAccessible(true);
                            String param = (String) field.get(entity);
                            if (StringUtil.isNotEmpty(param)) {
                                fieldTarget.set(data, param);
                            }
                        } catch (NoSuchFieldException | IllegalAccessException e) {
                            break;
                        }
                    }
                }
                return data;
            }).collect(Collectors.toList());
        }
        return list;
    }

    /**
     * 印刷設定保存用情報リストを取得する。
     * 
     * @param inDto     inデータ
     * @param sectionNo セクション番号
     * @return 印刷設定保存用情報リスト
     */
    private List<ComMocPrtiniSOutEntity> getPrintList(GyoumuComPrintSettingsInDto inDto, String sectionNo) {
        ComMocPrtiniSByCriteriaInEntity comMocPrtiniSByCriteriaInEntity = new ComMocPrtiniSByCriteriaInEntity();
        // リクエストパラメータ.職員ID
        comMocPrtiniSByCriteriaInEntity.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
        // 処理「3.1.」で取得した印刷設定情報リストの一件目.セクション番号
        comMocPrtiniSByCriteriaInEntity.setSectionNo(sectionNo);
        // リクエストパラメータ.法人ID
        comMocPrtiniSByCriteriaInEntity.setHid(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // リクエストパラメータ.施設ID
        comMocPrtiniSByCriteriaInEntity.setSid(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // リクエストパラメータ.事業所ID
        comMocPrtiniSByCriteriaInEntity.setJid(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        return comMocPrtiniSSelectMapper.findComMocPrtiniSByCriteria(comMocPrtiniSByCriteriaInEntity);
    }

    /**
     * 業務共通化-印刷設定情報の保存
     * 
     * @param inDto         inデータ
     * @param printList     印刷設定情報リスト
     * @param systemIniList システムINI情報リスト
     * @return 印刷設定情報リスト
     * @throws ExclusiveException
     */
    public List<GyoumuComPrintSettingsDto> setPrtList(GyoumuComPrintSettingsInDto inDto,
            List<GyoumuComPrintSettingsDto> printList,
            List<GyoumuComSystemIniDto> systemIniList) throws ExclusiveException {
        GyoumuComPrintSettingsParamDto paramDto = new GyoumuComPrintSettingsParamDto();
        // セクション名
        paramDto.setSectionName(inDto.getSectionName());
        // システムコード
        paramDto.setGsyscd(inDto.getGsyscd());
        // 職員ID
        paramDto.setShokuId(inDto.getShokuId());
        // 法人ID
        paramDto.setHoujinId(inDto.getHoujinId());
        // 施設ID
        paramDto.setShisetuId(inDto.getShisetuId());
        // 事業所ID
        paramDto.setSvJigyoId(inDto.getSvJigyoId());
        // 印刷設定情報リスト
        paramDto.setPrtList(printList);
        // システムINI情報リスト
        paramDto.setSysIniInfoList(systemIniList);
        // 印刷設定保存情報を保存する
        savePrtList(paramDto);
        // システムINI情報の保存
        saveIniInfo(paramDto);
        // 印刷設定情報リストを再取得
        List<ComMocPrtiniSOutEntity> comMocPrtiniSOutEntities = getPrintList(inDto,
                printList.get(0).getSectionNo());
        AtomicInteger index = new AtomicInteger(0);
        return comMocPrtiniSOutEntities.stream().map(x -> {
            GyoumuComPrintSettingsDto data = new GyoumuComPrintSettingsDto();
            // セクション番号
            data.setSectionNo(x.getSectionNo());
            // 帳票番号
            data.setPrtNo(CommonDtoUtil.objValToString(x.getPrtNo()));
            // セクション名
            data.setSectionName(x.getSectionName());
            // 帳票リスト名
            data.setListTitle(x.getListTitle());
            // 帳票タイトル
            data.setPrtTitle(x.getPrtTitle());
            // 日付表示有無
            data.setPrnDate(CommonDtoUtil.objValToString(x.getPrndate()));
            // 職員表示有無
            data.setPrnshoku(CommonDtoUtil.objValToString(x.getPrnshoku()));
            // パラメータ01
            data.setParam01(x.getParam01());
            // パラメータ02
            data.setParam02(x.getParam02());
            // パラメータ03
            data.setParam03(x.getParam03());
            // パラメータ04
            data.setParam04(x.getParam04());
            // パラメータ05
            data.setParam05(x.getParam05());
            // パラメータ06
            data.setParam06(x.getParam06());
            // パラメータ07
            data.setParam07(x.getParam07());
            // パラメータ08
            data.setParam08(x.getParam08());
            // パラメータ09
            data.setParam09(x.getParam09());
            // パラメータ10
            data.setParam10(x.getParam10());
            // パラメータ11
            data.setParam11(x.getParam11());
            // パラメータ12
            data.setParam12(x.getParam12());
            // パラメータ13
            data.setParam13(x.getParam13());
            // パラメータ14
            data.setParam14(x.getParam14());
            // パラメータ15
            data.setParam15(x.getParam15());
            // パラメータ16
            data.setParam16(x.getParam16());
            // パラメータ17
            data.setParam17(x.getParam17());
            // パラメータ18
            data.setParam18(x.getParam18());
            // パラメータ19
            data.setParam19(x.getParam19());
            // パラメータ20
            data.setParam20(x.getParam20());
            // パラメータ21
            data.setParam21(x.getParam21());
            // パラメータ22
            data.setParam22(x.getParam22());
            // パラメータ23
            data.setParam23(x.getParam23());
            // パラメータ24
            data.setParam24(x.getParam24());
            // パラメータ25
            data.setParam25(x.getParam25());
            // パラメータ26
            data.setParam26(x.getParam26());
            // パラメータ27
            data.setParam27(x.getParam27());
            // パラメータ28
            data.setParam28(x.getParam28());
            // パラメータ29
            data.setParam29(x.getParam29());
            // パラメータ30
            data.setParam30(x.getParam30());
            // パラメータ31
            data.setParam31(x.getParam31());
            // パラメータ32
            data.setParam32(x.getParam32());
            // パラメータ33
            data.setParam33(x.getParam33());
            // パラメータ34
            data.setParam34(x.getParam34());
            // パラメータ35
            data.setParam35(x.getParam35());
            // パラメータ36
            data.setParam36(x.getParam36());
            // パラメータ37
            data.setParam37(x.getParam37());
            // パラメータ38
            data.setParam38(x.getParam38());
            // パラメータ39
            data.setParam39(x.getParam39());
            // パラメータ40
            data.setParam40(x.getParam40());
            // パラメータ41
            data.setParam41(x.getParam41());
            // パラメータ42
            data.setParam42(x.getParam42());
            // パラメータ43
            data.setParam43(x.getParam43());
            // パラメータ44
            data.setParam44(x.getParam44());
            // パラメータ45
            data.setParam45(x.getParam45());
            // パラメータ46
            data.setParam46(x.getParam46());
            // パラメータ47
            data.setParam47(x.getParam47());
            // パラメータ48
            data.setParam48(x.getParam48());
            // パラメータ49
            data.setParam49(x.getParam49());
            // パラメータ50
            data.setParam50(x.getParam50());

            // 更新回数
            // data.setModifiedCnt(CommonDtoUtil.objValToString(x.getModifiedCnt()));
            // 印刷設定情報リストから帳票番号は印刷設定情報リスト.帳票番号と一致する帳票名
            data.setDefPrtTitle(
                    printList.stream().filter(y -> y.getPrtNo().equals(CommonDtoUtil.objValToString(x.getPrtNo())))
                            .findFirst().get().getDefPrtTitle());
            // ・印刷設定情報リスト.インデックス＝1から連番
            data.setIndex(CommonDtoUtil.objValToString(index.incrementAndGet()));
            // ・印刷設定情報リスト.プロファイル＝"3GK" + 該当レコード.セクション番号+ "P" ＋
            // 該当レコード.帳票番号の3桁(3桁未満の場合、先頭に0を埋める)
            data.setProfile(CommonConstants.SYS_CD_KBN_3GK.concat(x.getSectionNo()).concat(STR_P)
                    .concat(String.format("%03d", x.getPrtNo())));
            return data;
        }).collect(Collectors.toList());
    }

    /**
     * 業務共通化-印刷設定情報の保存
     * 
     * @param inDto inデータ
     * @throws ExclusiveException
     */
    public void savePrtList(GyoumuComPrintSettingsParamDto inDto)
            throws ExclusiveException {
        // リクエストパラメータ.印刷設定情報リストの一件目のセクション番号
        List<ComMocPrtiniSOutEntity> list = getPrintList(inDto, inDto.getPrtList().get(0).getSectionNo());
        // リクエストパラメータ.印刷設定情報リストの件数分、下記の処理を実行する。
        for (GyoumuComPrintSettingsSaveDto data : inDto.getPrtList()) {
            // 帳票番号は該当レコード.帳票番号と一致するデータがある場合
            Optional<ComMocPrtiniSOutEntity> matchingEntity = list.stream()
                    .filter(x -> x.getPrtNo().equals(CommonDtoUtil.strValToInt(data.getPrtNo()))).findFirst();

            ComMocPrtiniS comMocPrtiniS = new ComMocPrtiniS();
            // 帳票タイトル
            comMocPrtiniS.setPrtTitle(data.getPrtTitle());
            // セクション名
            comMocPrtiniS.setSectionName(inDto.getSectionName());
            comMocPrtiniS.setListTitle(null);
            // セクション番号
            comMocPrtiniS.setSectionNo(data.getSectionNo());
            // 帳票番号
            comMocPrtiniS.setPrtNo(CommonDtoUtil.strValToInt(data.getPrtNo()));
            // 日付表示有無
            comMocPrtiniS.setPrndate(CommonDtoUtil.strValToInt(data.getPrnDate()));
            // 職員表示有無
            comMocPrtiniS.setPrnshoku(CommonDtoUtil.strValToInt(data.getPrnshoku()));
            // パラメータ01
            comMocPrtiniS.setParam01(data.getParam01());
            // パラメータ02
            comMocPrtiniS.setParam02(data.getParam02());
            // パラメータ03
            comMocPrtiniS.setParam03(data.getParam03());
            // パラメータ04
            comMocPrtiniS.setParam04(data.getParam04());
            // パラメータ05
            comMocPrtiniS.setParam05(data.getParam05());
            // パラメータ06
            comMocPrtiniS.setParam06(data.getParam06());
            // パラメータ07
            comMocPrtiniS.setParam07(data.getParam07());
            // パラメータ08
            comMocPrtiniS.setParam08(data.getParam08());
            // パラメータ09
            comMocPrtiniS.setParam09(data.getParam09());
            // パラメータ10
            comMocPrtiniS.setParam10(data.getParam10());
            // パラメータ11
            comMocPrtiniS.setParam11(data.getParam11());
            // パラメータ12
            comMocPrtiniS.setParam12(data.getParam12());
            // パラメータ13
            comMocPrtiniS.setParam13(data.getParam13());
            // パラメータ14
            comMocPrtiniS.setParam14(data.getParam14());
            // パラメータ15
            comMocPrtiniS.setParam15(data.getParam15());
            // パラメータ16
            comMocPrtiniS.setParam16(data.getParam16());
            // パラメータ17
            comMocPrtiniS.setParam17(data.getParam17());
            // パラメータ18
            comMocPrtiniS.setParam18(data.getParam18());
            // パラメータ19
            comMocPrtiniS.setParam19(data.getParam19());
            // パラメータ20
            comMocPrtiniS.setParam20(data.getParam20());
            // パラメータ21
            comMocPrtiniS.setParam21(data.getParam21());
            // パラメータ22
            comMocPrtiniS.setParam22(data.getParam22());
            // パラメータ23
            comMocPrtiniS.setParam23(data.getParam23());
            // パラメータ24
            comMocPrtiniS.setParam24(data.getParam24());
            // パラメータ25
            comMocPrtiniS.setParam25(data.getParam25());
            // パラメータ26
            comMocPrtiniS.setParam26(data.getParam26());
            // パラメータ27
            comMocPrtiniS.setParam27(data.getParam27());
            // パラメータ28
            comMocPrtiniS.setParam28(data.getParam28());
            // パラメータ29
            comMocPrtiniS.setParam29(data.getParam29());
            // パラメータ30
            comMocPrtiniS.setParam30(data.getParam30());
            // パラメータ31
            comMocPrtiniS.setParam31(data.getParam31());
            // パラメータ32
            comMocPrtiniS.setParam32(data.getParam32());
            // パラメータ33
            comMocPrtiniS.setParam33(data.getParam33());
            // パラメータ34
            comMocPrtiniS.setParam34(data.getParam34());
            // パラメータ35
            comMocPrtiniS.setParam35(data.getParam35());
            // パラメータ36
            comMocPrtiniS.setParam36(data.getParam36());
            // パラメータ37
            comMocPrtiniS.setParam37(data.getParam37());
            // パラメータ38
            comMocPrtiniS.setParam38(data.getParam38());
            // パラメータ39
            comMocPrtiniS.setParam39(data.getParam39());
            // パラメータ40
            comMocPrtiniS.setParam40(data.getParam40());
            // パラメータ41
            comMocPrtiniS.setParam41(data.getParam41());
            // パラメータ42
            comMocPrtiniS.setParam42(data.getParam42());
            // パラメータ43
            comMocPrtiniS.setParam43(data.getParam43());
            // パラメータ44
            comMocPrtiniS.setParam44(data.getParam44());
            // パラメータ45
            comMocPrtiniS.setParam45(data.getParam45());
            // パラメータ46
            comMocPrtiniS.setParam46(data.getParam46());
            // パラメータ47
            comMocPrtiniS.setParam47(data.getParam47());
            // パラメータ48
            comMocPrtiniS.setParam48(data.getParam48());
            // パラメータ49
            comMocPrtiniS.setParam49(data.getParam49());
            // パラメータ50
            comMocPrtiniS.setParam50(data.getParam50());

            if (matchingEntity.isPresent()) {
                // BigInteger modifiedCnt =
                // CommonDtoUtil.intValToBigInt(matchingEntity.get().getModifiedCnt());
                // 更新時の共通カラム値設定処理
                // CommonDaoUtil.setUpdateCommonColumns(comMocPrtiniS, modifiedCnt);

                // MyBatis拦截器期望的通用字段设置（临时解决方案）
                // 由于ComMocPrtiniS实体类缺少modifiedUser等字段，暂时跳过通用字段设置

                // 「24-05 印刷iniファイルデータ保存テーブル」を更新する
                ComMocPrtiniSCriteria criteria = new ComMocPrtiniSCriteria();
                criteria.createCriteria()
                        .andShokuIdEqualTo(CommonDtoUtil.strValToInt(
                                inDto.getShokuId()))
                        // セクション番号 ＝ 印刷設定情報.セクション番号
                        .andSectionNoEqualTo(data.getSectionNo())
                        // 帳票番号 ＝ 印刷設定情報.帳票番号
                        .andPrtNoEqualTo(CommonDtoUtil.strValToInt(data.getPrtNo()))
                        // 法人ID ＝ リクエストパラメータ.法人ID
                        .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(
                                inDto.getHoujinId()))
                        // 施設ID ＝ リクエストパラメータ.施設ID
                        .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(
                                inDto.getShisetuId()))
                        // サービス事業者ID ＝ リクエストパラメータ.事業者ID
                        .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(
                                inDto.getSvJigyoId()));
                // 削除フラグ ＝ 0
                // .andDelFlgEqualTo(Constants.DELL_FLG_OFF)
                // 更新回数 ＝ 印刷設定情報.更新回数
                // .andModifiedCntEqualTo(modifiedCnt);

                int count = comMocPrtiniSMapper.updateByCriteriaSelective(comMocPrtiniS, criteria);

                // 更新失敗の場合
                if (count <= 0) {
                    throw new ExclusiveException();
                }
            } else {
                // 「24-05 印刷iniファイルデータ保存テーブル」を登録する。
                // リクエストパラメータ.職員ID
                comMocPrtiniS.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
                // システム略称
                comMocPrtiniS.setSysRyaku(CommonConstants.SYS_CD_KBN_3GK);
                // リクエストパラメータ.法人ID
                comMocPrtiniS.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
                // リクエストパラメータ.施設ID
                comMocPrtiniS.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                // リクエストパラメータ.事業者ID
                comMocPrtiniS.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                // 0
                comMocPrtiniS.setHsjId(0);
                // 登録時の共通カラム値設定処理
                // CommonDaoUtil.setInsertCommonColumns(comMocPrtiniS);
                comMocPrtiniSMapper.insertSelective(comMocPrtiniS);
            }
        }
    }

    /**
     * 業務共通化-システムINI情報の保存
     * 
     * @param inDto inデータ
     */
    public void saveIniInfo(GyoumuComPrintSettingsParamDto inDto) {
        if (inDto.getSysIniInfoList() == null) {
            return;
        }
        // リクエストパラメータ.システムINI情報リストの件数分
        for (GyoumuComSystemIniDto dto : inDto.getSysIniInfoList()) {
            // 氏名伏字設定情報をシステムiniファイルに保存する。
            F3gkSetProfileInDto f3gkSetProfileInDto = new F3gkSetProfileInDto();
            // リクエストパラメータ.職員ID
            f3gkSetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
            // 0
            f3gkSetProfileInDto.setHoujinId(0);
            // 0
            f3gkSetProfileInDto.setShisetuId(0);
            // 0
            f3gkSetProfileInDto.setSvJigyoId(0);
            // ”prt"
            f3gkSetProfileInDto.setKinounameKnj(STR_PRT);
            // リクエストパラメータ.システムINI情報リスト.プロファイル
            f3gkSetProfileInDto.setSectionKnj(dto.getProfile());
            // "amikake_flg"
            f3gkSetProfileInDto.setKeyKnj(STR_AMIKAKE_FLG);
            // "リクエストパラメータ.システムINI情報リスト.氏名伏字設定フラグ
            // ※ NULLの場合、0を設定"
            f3gkSetProfileInDto.setParam(dto.getAmikakeFlg() == null ? CommonConstants.STR_0 : dto.getAmikakeFlg());
            // リクエストパラメータ.システムコード
            f3gkSetProfileInDto.setGsyscd(inDto.getGsyscd());
            // "リクエストパラメータ.システムINI情報リスト.氏名伏字更新回数
            // ※ NULLの場合、0を設定"
            // f3gkSetProfileInDto
            // .setModifiedCnt(
            // dto.getAmikakeModifiedCnt() == null ? CommonConstants.STR_0 :
            // dto.getAmikakeModifiedCnt());

            nds3GkFunc01Logic.setF3gkProfile(f3gkSetProfileInDto);

            // 文章管理設定情報をシステムiniファイルに保存する。
            // "iso9001_flg"
            f3gkSetProfileInDto.setKeyKnj(STR_ISO9001_FLG);
            // "リクエストパラメータ.システムINI情報リスト.文章管理設定フラグ
            // ※ NULLの場合、0を設定"
            f3gkSetProfileInDto.setParam(dto.getIso9001Flg() == null ? CommonConstants.STR_0 : dto.getIso9001Flg());
            // "リクエストパラメータ.システムINI情報リスト.文章管理更新回数
            // ※ NULLの場合、0を設定"
            // f3gkSetProfileInDto
            // .setModifiedCnt(
            // dto.getIso9001ModifiedCnt() == null ? CommonConstants.STR_0 :
            // dto.getIso9001ModifiedCnt());

            nds3GkFunc01Logic.setF3gkProfile(f3gkSetProfileInDto);

            // リクエストパラメータ.個人情報設定表示フラグは1:表示の場合
            if (CommonConstants.STR_1.equals(dto.getKojinhogoDisplayFlg())) {
                // 個人情報をシステムiniファイルに保存する。
                // "kojinhogo_flg"
                f3gkSetProfileInDto.setKeyKnj(STR_KOJINHOGO_FLG);
                // "リクエストパラメータ.システムINI情報リスト.個人情報設定フラグ
                // ※ NULLの場合、0を設定"
                f3gkSetProfileInDto
                        .setParam(dto.getKojinhogoFlg() == null ? CommonConstants.STR_0 : dto.getKojinhogoFlg());
                // "リクエストパラメータ.システムINI情報リスト.個人情報更新回数
                // ※ NULLの場合、0を設定"
                f3gkSetProfileInDto
                        .setParam(dto.getKojinhogo1ModifiedCnt() == null ? CommonConstants.STR_0
                                : dto.getKojinhogo1ModifiedCnt());

                nds3GkFunc01Logic.setF3gkProfile(f3gkSetProfileInDto);
            }
        }
    }

    /**
     * 業務共通化-印刷設定情報の取得
     */
    public GyoumuComPrintSettingsDto getPrtInfo(GyoumuComPrintSettingsInputDto inDto) {
        GyoumuComPrintSettingsDto outDto = new GyoumuComPrintSettingsDto();
        // 印刷設定情報の取得
        CpnTucCks11ByCriteriaInEntity cpnTucCks11ByCriteriaIn = new CpnTucCks11ByCriteriaInEntity();
        // システム略称 "3GK"
        cpnTucCks11ByCriteriaIn.setSysRyaku(CommonConstants.SYS_CD_KBN_3GK);
        // セクション番号
        cpnTucCks11ByCriteriaIn.setSectionNo(inDto.getSectionNo());
        // 帳票番号
        cpnTucCks11ByCriteriaIn.setPrtNo(CommonDtoUtil.strValToInt(inDto.getPrtNo()));
        // デフォルト印刷設定情報の取得
        List<CpnTucCks11OutEntity> cpnTucCks11List = comMocPrtiniSelectMapper
                .findCpnTucCks11ByCriteria(cpnTucCks11ByCriteriaIn);
        ComMocPrtiniSZoomByCriteriaInEntity comMocPrtiniSZoomByCriteriaIn = new ComMocPrtiniSZoomByCriteriaInEntity();
        // 職員ID
        comMocPrtiniSZoomByCriteriaIn.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
        // セクション番号
        comMocPrtiniSZoomByCriteriaIn.setSectionNo(inDto.getSectionNo());
        // 法人ID
        comMocPrtiniSZoomByCriteriaIn.setHid(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        comMocPrtiniSZoomByCriteriaIn.setSid(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // サービス事業所ID
        comMocPrtiniSZoomByCriteriaIn.setJid(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 帳票番号
        comMocPrtiniSZoomByCriteriaIn.setPrtno(CommonDtoUtil.strValToInt(inDto.getPrtNo()));
        // カスタマイズ印刷設定情報の取得
        List<ComMocPrtiniSZoomOutEntity> comMocPrtiniSZoomList = comMocPrtiniSSelectMapper
                .findComMocPrtiniSZoomByCriteria(comMocPrtiniSZoomByCriteriaIn);
        // 印刷設定情報の設定
        if (cpnTucCks11List.size() == 1) {
            // セクション番号、帳票番号、パラメータ01～パラメータ50、更新回数の設定
            // セクション番号
            outDto.setSectionNo(cpnTucCks11List.get(0).getSectionNo());
            // 帳票番号
            outDto.setPrtNo(CommonDtoUtil.objValToString(cpnTucCks11List.get(0).getPrtNo()));
            // パラメータ01
            outDto.setParam01(cpnTucCks11List.get(0).getParam01());
            // パラメータ02
            outDto.setParam02(cpnTucCks11List.get(0).getParam02());
            // パラメータ03
            outDto.setParam03(cpnTucCks11List.get(0).getParam03());
            // パラメータ04
            outDto.setParam04(cpnTucCks11List.get(0).getParam04());
            // パラメータ05
            outDto.setParam05(cpnTucCks11List.get(0).getParam05());
            // パラメータ06
            outDto.setParam06(cpnTucCks11List.get(0).getParam06());
            // パラメータ07
            outDto.setParam07(cpnTucCks11List.get(0).getParam07());
            // パラメータ08
            outDto.setParam08(cpnTucCks11List.get(0).getParam08());
            // パラメータ09
            outDto.setParam09(cpnTucCks11List.get(0).getParam09());
            // パラメータ10
            outDto.setParam10(cpnTucCks11List.get(0).getParam10());
            // パラメータ11
            outDto.setParam11(cpnTucCks11List.get(0).getParam11());
            // パラメータ12
            outDto.setParam12(cpnTucCks11List.get(0).getParam12());
            // パラメータ13
            outDto.setParam13(cpnTucCks11List.get(0).getParam13());
            // パラメータ14
            outDto.setParam14(cpnTucCks11List.get(0).getParam14());
            // パラメータ15
            outDto.setParam15(cpnTucCks11List.get(0).getParam15());
            // パラメータ16
            outDto.setParam16(cpnTucCks11List.get(0).getParam16());
            // パラメータ17
            outDto.setParam17(cpnTucCks11List.get(0).getParam17());
            // パラメータ18
            outDto.setParam18(cpnTucCks11List.get(0).getParam18());
            // パラメータ19
            outDto.setParam19(cpnTucCks11List.get(0).getParam19());
            // パラメータ20
            outDto.setParam20(cpnTucCks11List.get(0).getParam20());
            // パラメータ21
            outDto.setParam21(cpnTucCks11List.get(0).getParam21());
            // パラメータ22
            outDto.setParam22(cpnTucCks11List.get(0).getParam22());
            // パラメータ23
            outDto.setParam23(cpnTucCks11List.get(0).getParam23());
            // パラメータ24
            outDto.setParam24(cpnTucCks11List.get(0).getParam24());
            // パラメータ25
            outDto.setParam25(cpnTucCks11List.get(0).getParam25());
            // パラメータ26
            outDto.setParam26(cpnTucCks11List.get(0).getParam26());
            // パラメータ27
            outDto.setParam27(cpnTucCks11List.get(0).getParam27());
            // パラメータ28
            outDto.setParam28(cpnTucCks11List.get(0).getParam28());
            // パラメータ29
            outDto.setParam29(cpnTucCks11List.get(0).getParam29());
            // パラメータ30
            outDto.setParam30(cpnTucCks11List.get(0).getParam30());
            // パラメータ31
            outDto.setParam31(cpnTucCks11List.get(0).getParam31());
            // パラメータ32
            outDto.setParam32(cpnTucCks11List.get(0).getParam32());
            // パラメータ33
            outDto.setParam33(cpnTucCks11List.get(0).getParam33());
            // パラメータ34
            outDto.setParam34(cpnTucCks11List.get(0).getParam34());
            // パラメータ35
            outDto.setParam35(cpnTucCks11List.get(0).getParam35());
            // パラメータ36
            outDto.setParam36(cpnTucCks11List.get(0).getParam36());
            // パラメータ37
            outDto.setParam37(cpnTucCks11List.get(0).getParam37());
            // パラメータ38
            outDto.setParam38(cpnTucCks11List.get(0).getParam38());
            // パラメータ39
            outDto.setParam39(cpnTucCks11List.get(0).getParam39());
            // パラメータ40
            outDto.setParam40(cpnTucCks11List.get(0).getParam40());
            // パラメータ41
            outDto.setParam41(cpnTucCks11List.get(0).getParam41());
            // パラメータ42
            outDto.setParam42(cpnTucCks11List.get(0).getParam42());
            // パラメータ43
            outDto.setParam43(cpnTucCks11List.get(0).getParam43());
            // パラメータ44
            outDto.setParam44(cpnTucCks11List.get(0).getParam44());
            // パラメータ45
            outDto.setParam45(cpnTucCks11List.get(0).getParam45());
            // パラメータ46
            outDto.setParam46(cpnTucCks11List.get(0).getParam46());
            // パラメータ47
            outDto.setParam47(cpnTucCks11List.get(0).getParam47());
            // パラメータ48
            outDto.setParam48(cpnTucCks11List.get(0).getParam48());
            // パラメータ49
            outDto.setParam49(cpnTucCks11List.get(0).getParam49());
            // パラメータ50
            outDto.setParam50(cpnTucCks11List.get(0).getParam50());
            // カスタマイズ印刷設定情報があれば、印刷設定情報にカスタマイズ印刷設定情報との同名項目を再設定する。
            if (comMocPrtiniSZoomList.size() == 1) {
                // 帳票タイトル
                outDto.setPrtTitle(comMocPrtiniSZoomList.get(0).getPrtTitle());
                // 帳票リスト名
                outDto.setListTitle(comMocPrtiniSZoomList.get(0).getListTitle());
                // セクション名
                outDto.setSectionName(comMocPrtiniSZoomList.get(0).getSectionName());
                // セクション番号
                outDto.setSectionNo(comMocPrtiniSZoomList.get(0).getSectionNo());
                // 帳票番号
                outDto.setPrtNo(CommonDtoUtil.objValToString(comMocPrtiniSZoomList.get(0).getPrtNo()));
                // 日付表示有無
                outDto.setPrnDate(CommonDtoUtil.objValToString(comMocPrtiniSZoomList.get(0).getPrndate()));
                // 職員表示有無
                outDto.setPrnshoku(CommonDtoUtil.objValToString(comMocPrtiniSZoomList.get(0).getPrnshoku()));
                // パラメータ01
                outDto.setParam01(comMocPrtiniSZoomList.get(0).getParam01());
                // パラメータ02
                outDto.setParam02(comMocPrtiniSZoomList.get(0).getParam02());
                // パラメータ03
                outDto.setParam03(comMocPrtiniSZoomList.get(0).getParam03());
                // パラメータ04
                outDto.setParam04(comMocPrtiniSZoomList.get(0).getParam04());
                // パラメータ05
                outDto.setParam05(comMocPrtiniSZoomList.get(0).getParam05());
                // パラメータ06
                outDto.setParam06(comMocPrtiniSZoomList.get(0).getParam06());
                // パラメータ07
                outDto.setParam07(comMocPrtiniSZoomList.get(0).getParam07());
                // パラメータ08
                outDto.setParam08(comMocPrtiniSZoomList.get(0).getParam08());
                // パラメータ09
                outDto.setParam09(comMocPrtiniSZoomList.get(0).getParam09());
                // パラメータ10
                outDto.setParam10(comMocPrtiniSZoomList.get(0).getParam10());
                // パラメータ11
                outDto.setParam11(comMocPrtiniSZoomList.get(0).getParam11());
                // パラメータ12
                outDto.setParam12(comMocPrtiniSZoomList.get(0).getParam12());
                // パラメータ13
                outDto.setParam13(comMocPrtiniSZoomList.get(0).getParam13());
                // パラメータ14
                outDto.setParam14(comMocPrtiniSZoomList.get(0).getParam14());
                // パラメータ15
                outDto.setParam15(comMocPrtiniSZoomList.get(0).getParam15());
                // パラメータ16
                outDto.setParam16(comMocPrtiniSZoomList.get(0).getParam16());
                // パラメータ17
                outDto.setParam17(comMocPrtiniSZoomList.get(0).getParam17());
                // パラメータ18
                outDto.setParam18(comMocPrtiniSZoomList.get(0).getParam18());
                // パラメータ19
                outDto.setParam19(comMocPrtiniSZoomList.get(0).getParam19());
                // パラメータ20
                outDto.setParam20(comMocPrtiniSZoomList.get(0).getParam20());
                // パラメータ21
                outDto.setParam21(comMocPrtiniSZoomList.get(0).getParam21());
                // パラメータ22
                outDto.setParam22(comMocPrtiniSZoomList.get(0).getParam22());
                // パラメータ23
                outDto.setParam23(comMocPrtiniSZoomList.get(0).getParam23());
                // パラメータ24
                outDto.setParam24(comMocPrtiniSZoomList.get(0).getParam24());
                // パラメータ25
                outDto.setParam25(comMocPrtiniSZoomList.get(0).getParam25());
                // パラメータ26
                outDto.setParam26(comMocPrtiniSZoomList.get(0).getParam26());
                // パラメータ27
                outDto.setParam27(comMocPrtiniSZoomList.get(0).getParam27());
                // パラメータ28
                outDto.setParam28(comMocPrtiniSZoomList.get(0).getParam28());
                // パラメータ29
                outDto.setParam29(comMocPrtiniSZoomList.get(0).getParam29());
                // パラメータ30
                outDto.setParam30(comMocPrtiniSZoomList.get(0).getParam30());
                // パラメータ31
                outDto.setParam31(comMocPrtiniSZoomList.get(0).getParam31());
                // パラメータ32
                outDto.setParam32(comMocPrtiniSZoomList.get(0).getParam32());
                // パラメータ33
                outDto.setParam33(comMocPrtiniSZoomList.get(0).getParam33());
                // パラメータ34
                outDto.setParam34(comMocPrtiniSZoomList.get(0).getParam34());
                // パラメータ35
                outDto.setParam35(comMocPrtiniSZoomList.get(0).getParam35());
                // パラメータ36
                outDto.setParam36(comMocPrtiniSZoomList.get(0).getParam36());
                // パラメータ37
                outDto.setParam37(comMocPrtiniSZoomList.get(0).getParam37());
                // パラメータ38
                outDto.setParam38(comMocPrtiniSZoomList.get(0).getParam38());
                // パラメータ39
                outDto.setParam39(comMocPrtiniSZoomList.get(0).getParam39());
                // パラメータ40
                outDto.setParam40(comMocPrtiniSZoomList.get(0).getParam40());
                // パラメータ41
                outDto.setParam41(comMocPrtiniSZoomList.get(0).getParam41());
                // パラメータ42
                outDto.setParam42(comMocPrtiniSZoomList.get(0).getParam42());
                // パラメータ43
                outDto.setParam43(comMocPrtiniSZoomList.get(0).getParam43());
                // パラメータ44
                outDto.setParam44(comMocPrtiniSZoomList.get(0).getParam44());
                // パラメータ45
                outDto.setParam45(comMocPrtiniSZoomList.get(0).getParam45());
                // パラメータ46
                outDto.setParam46(comMocPrtiniSZoomList.get(0).getParam46());
                // パラメータ47
                outDto.setParam47(comMocPrtiniSZoomList.get(0).getParam47());
                // パラメータ48
                outDto.setParam48(comMocPrtiniSZoomList.get(0).getParam48());
                // パラメータ49
                outDto.setParam49(comMocPrtiniSZoomList.get(0).getParam49());
                // パラメータ50
                outDto.setParam50(comMocPrtiniSZoomList.get(0).getParam50());
            }
            // その他の設定について
            // インデックス
            outDto.setIndex(inDto.getIndex());
            // プロファイル
            // "3GK" +デフォルト印刷設定情報.セクション番号+ "P" ＋
            // デフォルト印刷設定情報.帳票番号の3桁(3桁未満の場合、先頭に0を埋める)
            outDto.setProfile(CommonConstants.SYS_CD_KBN_3GK.concat(cpnTucCks11List.get(0).getSectionNo()).concat(STR_P)
                    .concat(String.format("%03d",
                            cpnTucCks11List.get(0).getPrtNo())));
            // 帳票リスト名
            outDto.setListTitle(cpnTucCks11List.get(0).getListTitle());
            // 帳票名
            outDto.setDefPrtTitle(cpnTucCks11List.get(0).getDefPrtTitle());
            // 帳票タイトル
            outDto.setPrtTitle(comMocPrtiniSZoomList.size() == 1 ? comMocPrtiniSZoomList.get(0).getPrtTitle()
                    : cpnTucCks11List
                            .get(0).getDefPrtTitle());
            // 日付表示有無
            outDto.setPrnDate(CommonDtoUtil
                    .objValToString(comMocPrtiniSZoomList.size() == 1 ? comMocPrtiniSZoomList.get(0).getPrndate()
                            : cpnTucCks11List
                                    .get(0).getDefPrndate()));
            // 職員表示有無
            outDto.setPrnshoku(CommonDtoUtil
                    .objValToString(comMocPrtiniSZoomList.size() == 1 ? comMocPrtiniSZoomList.get(0).getPrnshoku()
                            : cpnTucCks11List
                                    .get(0).getDefPrnshoku()));
        }
        return outDto;
    }

    /**
     * 「課題整理総括の意向内容」リストの取得
     * 
     * @param inDto          inデータ
     * @param searchRirekiId 検索用履歴ＩＤ
     * @return 「総括の課題・目標」リスト
     */
    public List<Kss1IkouKnjOutEntity> getIkouTorikomiList(GyoumuComInDto inDto, String searchRirekID) {

        // 検索用履歴ＩＤが「空白、0」以外の場合、またはリクエストパラメータ.操作区分がR3:履歴Openの場合
        if (!CommonConstants.BLANK_STRING.equals(searchRirekID) && STR_COUNT_0.equals(searchRirekID)) {
            return null;
        } else if (OPER_FLAG_R3.equals(inDto.getOperaFlg())) {
            return null;
        }

        CpnTucKss1InfoByCriteriaInEntity inEntity = new CpnTucKss1InfoByCriteriaInEntity();
        // 検索用計画期間ＩＤ(0:全検索)
        inEntity.setSc1Id(0);
        // リクエストパラメータ.事業所ＩＤ
        inEntity.setSvj(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // リクエストパラメータ.利用者ＩＤ
        inEntity.setUid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        List<CpnTucKss1InfoOutEntity> outEntityList = cpnTucKss1SelectMapper
                .findCpnTucKss1InfoByCriteria(inEntity);

        if (outEntityList.size() > 0) {
            Kss1IkouKnjByCriteriaInEntity inEntity2 = new Kss1IkouKnjByCriteriaInEntity();
            // 履歴ＩＤ
            inEntity2.setKss1Id(outEntityList.get(0).getKss1Id());
            return cpnTucKss1SelectMapper
                    .findKss1IkouKnjByCriteria(inEntity2);
        } else {
            return null;
        }
    }

    /**
     * 業務共通化-電子保存
     * 
     * @param inDto    リクエストパラメータ
     * @param menu3Knj 機能名
     * @throws Exception
     * 
     */
    @SuppressWarnings("unchecked")
    public void print(GyoumuComElectronicSaveInDto inDto, String menu3Knj) throws Exception {
        // 文章法区分
        if (!kghSmglHistoryLogic.getKghComEBunshoKbn()) {
            return;
        }
        // 電子保存のINPUT情報の準備
        String prtId = "";
        // 帳票サービスInDto
        GyoumuComPrintSettingsDto printSettings = getReportServiceInDto(inDto, menu3Knj);
        // E文書更新区分
        String eDocumentUpdKbn = "";
        // リクエストパラメータ.操作区分が1:新規の場合、C:新規
        if (OPER_FLAG_1.equals(inDto.getOperaFlg())) {
            eDocumentUpdKbn = CommonConstants.UPDATE_KBN_C;
            // リクエストパラメータ.操作区分が3:削除の場合、D:削除
        } else if (OPER_FLAG_3.equals(inDto.getOperaFlg())) {
            eDocumentUpdKbn = CommonConstants.UPDATE_KBN_D;
        } else {
            eDocumentUpdKbn = CommonConstants.UPDATE_KBN_U;
        }
        switch (menu3Knj) {
            // 機能名が"[mnu3][3GK]実施計画～①"の場合
            case CommonConstants.KINOU_NAME_IMPLEMENTATION_PLAN_1_TAB:
                prtId = "implementationPlan1Report";
                ImplementationPlan1ReportParameterModel reportParameterModelPlan1 = new ImplementationPlan1ReportParameterModel();
                // 帳票サービスInDtoの設定
                setReportParameterModel(inDto, printSettings, reportParameterModelPlan1);
                // 事業者名
                reportParameterModelPlan1.setJigyoKnj(inDto.getSvJigyoKnj());
                // システム日付
                reportParameterModelPlan1.setAppYmd(inDto.getSysYmd());
                // 初期設定マスタの情報
                ImplementationPlan1ReportServiceInitMasterObj initMasterObjPlan1 = new ImplementationPlan1ReportServiceInitMasterObj();
                // 敬称オプション
                initMasterObjPlan1.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                // 敬称
                initMasterObjPlan1.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
                // 実施計画～①：内容が空白の場合省略
                initMasterObjPlan1.setPkkak21Keisen1Flg(inDto.getInitMasterObj().getPkkak21Keisen1Flg());
                // 実施計画～①：内容が同じ場合省略
                initMasterObjPlan1.setPkkak21Keisen2Flg(inDto.getInitMasterObj().getPkkak21Keisen2Flg());
                reportParameterModelPlan1.setInitMasterObj(initMasterObjPlan1);
                // 印刷設定
                PrintReportServicePrintSet printSetPlan1 = new PrintReportServicePrintSet();
                // 帳票タイトル
                printSetPlan1.setPrtTitle(printSettings.getPrtTitle());
                // セクション番号
                printSetPlan1.setSectionNo(printSettings.getSectionNo());
                // 帳票No
                printSetPlan1.setPrtNo(printSettings.getPrtNo());
                // プロファイル
                printSetPlan1.setProfile(printSettings.getProfile());
                // 日付表示有無
                printSetPlan1.setPrnDate(printSettings.getPrnDate());
                // 職員表示有無
                printSetPlan1.setPrnshoku(printSettings.getPrnshoku());
                // パラメータ01
                printSetPlan1.setParam01(printSettings.getParam01());
                // パラメータ02
                printSetPlan1.setParam02(printSettings.getParam02());
                // パラメータ03
                printSetPlan1.setParam03(printSettings.getParam03());
                // パラメータ04
                printSetPlan1.setParam04(printSettings.getParam04());
                // パラメータ05
                printSetPlan1.setParam05(printSettings.getParam05());
                // パラメータ06
                printSetPlan1.setParam06(printSettings.getParam06());
                // パラメータ07
                printSetPlan1.setParam07(printSettings.getParam07());
                // パラメータ08
                printSetPlan1.setParam08(printSettings.getParam08());
                // パラメータ09
                printSetPlan1.setParam09(printSettings.getParam09());
                // パラメータ10
                printSetPlan1.setParam10(printSettings.getParam10());
                // パラメータ11
                printSetPlan1.setParam11(printSettings.getParam11());
                // パラメータ12
                printSetPlan1.setParam12(printSettings.getParam12());
                // パラメータ13
                printSetPlan1.setParam13(printSettings.getParam13());
                // パラメータ14
                printSetPlan1.setParam14(printSettings.getParam14());
                // パラメータ15
                printSetPlan1.setParam15(printSettings.getParam15());
                // パラメータ16
                printSetPlan1.setParam16(printSettings.getParam16());
                // パラメータ17
                printSetPlan1.setParam17(printSettings.getParam17());
                // パラメータ18
                printSetPlan1.setParam18(printSettings.getParam18());
                // パラメータ19
                printSetPlan1.setParam19(printSettings.getParam19());
                // パラメータ20
                printSetPlan1.setParam20(printSettings.getParam20());
                // パラメータ21
                printSetPlan1.setParam21(printSettings.getParam21());
                // パラメータ22
                printSetPlan1.setParam22(printSettings.getParam22());
                // パラメータ23
                printSetPlan1.setParam23(printSettings.getParam23());
                // パラメータ24
                printSetPlan1.setParam24(printSettings.getParam24());
                // パラメータ25
                printSetPlan1.setParam25(printSettings.getParam25());
                // パラメータ26
                printSetPlan1.setParam26(printSettings.getParam26());
                // パラメータ27
                printSetPlan1.setParam27(printSettings.getParam27());
                // パラメータ28
                printSetPlan1.setParam28(printSettings.getParam28());
                // パラメータ29
                printSetPlan1.setParam29(printSettings.getParam29());
                // パラメータ30
                printSetPlan1.setParam30(printSettings.getParam30());
                // パラメータ31
                printSetPlan1.setParam31(printSettings.getParam31());
                // パラメータ32
                printSetPlan1.setParam32(printSettings.getParam32());
                // パラメータ33
                printSetPlan1.setParam33(printSettings.getParam33());
                // パラメータ34
                printSetPlan1.setParam34(printSettings.getParam34());
                // パラメータ35
                printSetPlan1.setParam35(printSettings.getParam35());
                // パラメータ36
                printSetPlan1.setParam36(printSettings.getParam36());
                // パラメータ37
                printSetPlan1.setParam37(printSettings.getParam37());
                // パラメータ38
                printSetPlan1.setParam38(printSettings.getParam38());
                // パラメータ39
                printSetPlan1.setParam39(printSettings.getParam39());
                // パラメータ40
                printSetPlan1.setParam40(printSettings.getParam40());
                // パラメータ41
                printSetPlan1.setParam41(printSettings.getParam41());
                // パラメータ42
                printSetPlan1.setParam42(printSettings.getParam42());
                // パラメータ43
                printSetPlan1.setParam43(printSettings.getParam43());
                // パラメータ44
                printSetPlan1.setParam44(printSettings.getParam44());
                // パラメータ45
                printSetPlan1.setParam45(printSettings.getParam45());
                // パラメータ46
                printSetPlan1.setParam46(printSettings.getParam46());
                // パラメータ47
                printSetPlan1.setParam47(printSettings.getParam47());
                // パラメータ48
                printSetPlan1.setParam48(printSettings.getParam48());
                // パラメータ49
                printSetPlan1.setParam49(printSettings.getParam49());
                // パラメータ50
                printSetPlan1.setParam50(printSettings.getParam50());
                reportParameterModelPlan1.setPrintSet(printSetPlan1);
                // DB未保存画面項目
                PrintReportServiceDbNoSaveData dbNoSaveDataPlan1 = new PrintReportServiceDbNoSaveData();
                // 指定日
                dbNoSaveDataPlan1.setSelectDate(inDto.getSelectDate());
                // 記入用シートを印刷するフラグ
                dbNoSaveDataPlan1.setEmptyFlg(inDto.getEmptyFlg());
                reportParameterModelPlan1.setDbNoSaveData(dbNoSaveDataPlan1);
                // 印刷対象履歴
                PrintReportServicePrintSubjectHistory printSubjectHistoryPlan1 = new PrintReportServicePrintSubjectHistory();
                // 利用者ID
                printSubjectHistoryPlan1.setUserId(inDto.getUserId());
                // 履歴ID
                printSubjectHistoryPlan1.setRirekiId(inDto.getRirekiId());
                reportParameterModelPlan1.setPrintSubjectHistory(printSubjectHistoryPlan1);
                // S3e文書保存処理共通関数を取得する
                try {
                    cmnStorageServiceUtil.uploadToS3EDocument(
                            prtId,
                            reportParameterModelPlan1, inDto.getLoginShokuinId(),
                            eDocumentUpdKbn,
                            inDto.getRirekiId(), inDto.getSc1Id());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            // 機能名が"[mnu3][3GK]実施計画～②"の場合
            case CommonConstants.KINOU_NAME_IMPLEMENTATION_PLAN_2_TAB:
                prtId = "implementationPlan2Report";
                ImplementationPlan2ReportParameterModel reportParameterModelPlan2 = new ImplementationPlan2ReportParameterModel();
                // 帳票サービスInDtoの設定
                setReportParameterModel(inDto, printSettings, reportParameterModelPlan2);
                // 事業者名
                reportParameterModelPlan2.setJigyoKnj(inDto.getSvJigyoKnj());
                // システム日付
                reportParameterModelPlan2.setAppYmd(inDto.getSysYmd());
                // 初期設定マスタの情報
                InitSettingMasterData initMasterObjPlan2 = new InitSettingMasterData();
                // 敬称オプション
                initMasterObjPlan2.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                // 敬称
                initMasterObjPlan2.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
                // 実施計画～①：内容が空白の場合省略
                initMasterObjPlan2.setPkkak22Keisen1Flg(inDto.getInitMasterObj().getPkkak21Keisen1Flg());
                // 実施計画～①：内容が同じ場合省略
                initMasterObjPlan2.setPkkak22Keisen2Flg(inDto.getInitMasterObj().getPkkak21Keisen2Flg());
                reportParameterModelPlan2.setInitMasterObj(initMasterObjPlan2);
                // 印刷設定
                ReportImplementationPlan2PrintSet printSetPlan2 = new ReportImplementationPlan2PrintSet();
                // 帳票タイトル
                printSetPlan2.setTitle(printSettings.getPrtTitle());
                // セクション番号
                printSetPlan2.setSectionNo(printSettings.getSectionNo());
                // 帳票No
                printSetPlan2.setPrtNo(printSettings.getPrtNo());
                // プロファイル
                printSetPlan2.setProfile(printSettings.getProfile());
                // 日付表示有無
                printSetPlan2.setPrnDate(printSettings.getPrnDate());
                // 職員表示有無
                printSetPlan2.setPrnshoku(printSettings.getPrnshoku());
                // パラメータ01
                printSetPlan2.setParam01(printSettings.getParam01());
                // パラメータ02
                printSetPlan2.setParam02(printSettings.getParam02());
                // パラメータ03
                printSetPlan2.setParam03(printSettings.getParam03());
                // パラメータ04
                printSetPlan2.setParam04(printSettings.getParam04());
                // パラメータ05
                printSetPlan2.setParam05(printSettings.getParam05());
                // パラメータ06
                printSetPlan2.setParam06(printSettings.getParam06());
                // パラメータ07
                printSetPlan2.setParam07(printSettings.getParam07());
                // パラメータ08
                printSetPlan2.setParam08(printSettings.getParam08());
                // パラメータ09
                printSetPlan2.setParam09(printSettings.getParam09());
                // パラメータ10
                printSetPlan2.setParam10(printSettings.getParam10());
                // パラメータ11
                printSetPlan2.setParam11(printSettings.getParam11());
                // パラメータ12
                printSetPlan2.setParam12(printSettings.getParam12());
                // パラメータ13
                printSetPlan2.setParam13(printSettings.getParam13());
                // パラメータ14
                printSetPlan2.setParam14(printSettings.getParam14());
                // パラメータ15
                printSetPlan2.setParam15(printSettings.getParam15());
                // パラメータ16
                printSetPlan2.setParam16(printSettings.getParam16());
                // パラメータ17
                printSetPlan2.setParam17(printSettings.getParam17());
                // パラメータ18
                printSetPlan2.setParam18(printSettings.getParam18());
                // パラメータ19
                printSetPlan2.setParam19(printSettings.getParam19());
                // パラメータ20
                printSetPlan2.setParam20(printSettings.getParam20());
                // パラメータ21
                printSetPlan2.setParam21(printSettings.getParam21());
                // パラメータ22
                printSetPlan2.setParam22(printSettings.getParam22());
                // パラメータ23
                printSetPlan2.setParam23(printSettings.getParam23());
                // パラメータ24
                printSetPlan2.setParam24(printSettings.getParam24());
                // パラメータ25
                printSetPlan2.setParam25(printSettings.getParam25());
                // パラメータ26
                printSetPlan2.setParam26(printSettings.getParam26());
                // パラメータ27
                printSetPlan2.setParam27(printSettings.getParam27());
                // パラメータ28
                printSetPlan2.setParam28(printSettings.getParam28());
                // パラメータ29
                printSetPlan2.setParam29(printSettings.getParam29());
                // パラメータ30
                printSetPlan2.setParam30(printSettings.getParam30());
                // パラメータ31
                printSetPlan2.setParam31(printSettings.getParam31());
                // パラメータ32
                printSetPlan2.setParam32(printSettings.getParam32());
                // パラメータ33
                printSetPlan2.setParam33(printSettings.getParam33());
                // パラメータ34
                printSetPlan2.setParam34(printSettings.getParam34());
                // パラメータ35
                printSetPlan2.setParam35(printSettings.getParam35());
                // パラメータ36
                printSetPlan2.setParam36(printSettings.getParam36());
                // パラメータ37
                printSetPlan2.setParam37(printSettings.getParam37());
                // パラメータ38
                printSetPlan2.setParam38(printSettings.getParam38());
                // パラメータ39
                printSetPlan2.setParam39(printSettings.getParam39());
                // パラメータ40
                printSetPlan2.setParam40(printSettings.getParam40());
                // パラメータ41
                printSetPlan2.setParam41(printSettings.getParam41());
                // パラメータ42
                printSetPlan2.setParam42(printSettings.getParam42());
                // パラメータ43
                printSetPlan2.setParam43(printSettings.getParam43());
                // パラメータ44
                printSetPlan2.setParam44(printSettings.getParam44());
                // パラメータ45
                printSetPlan2.setParam45(printSettings.getParam45());
                // パラメータ46
                printSetPlan2.setParam46(printSettings.getParam46());
                // パラメータ47
                printSetPlan2.setParam47(printSettings.getParam47());
                // パラメータ48
                printSetPlan2.setParam48(printSettings.getParam48());
                // パラメータ49
                printSetPlan2.setParam49(printSettings.getParam49());
                // パラメータ50
                printSetPlan2.setParam50(printSettings.getParam50());
                reportParameterModelPlan2.setPrintSet(printSetPlan2);
                // DB未保存画面項目
                ReportImplementationPlan2NoSaveData dbNoSaveDataPlan2 = new ReportImplementationPlan2NoSaveData();
                // 指定日
                dbNoSaveDataPlan2.setEmptyFlg(inDto.getEmptyFlg());
                // 記入用シートを印刷するフラグ
                dbNoSaveDataPlan2.setSelectDate(inDto.getSelectDate());
                reportParameterModelPlan2.setDbNoSaveData(dbNoSaveDataPlan2);
                // 印刷対象履歴
                ReportImplementationPlan2PrintSubjectHistory printSubjectHistoryPlan2 = new ReportImplementationPlan2PrintSubjectHistory();
                // 利用者ID
                printSubjectHistoryPlan2.setUserId(inDto.getUserId());
                // 履歴ID
                printSubjectHistoryPlan2.setRirekiId(inDto.getRirekiId());
                reportParameterModelPlan2.setPrintSubjectHistory(printSubjectHistoryPlan2);

                // S3e文書保存処理共通関数を取得する
                try {
                    cmnStorageServiceUtil.uploadToS3EDocument(
                            prtId,
                            reportParameterModelPlan2, inDto.getLoginShokuinId(),
                            eDocumentUpdKbn,
                            inDto.getRirekiId(), inDto.getSc1Id());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            // 機能名が"[mnu3][3GK]実施計画～③"の場合
            case CommonConstants.KINOU_NAME_IMPLEMENTATION_PLAN_3_TAB:
                prtId = "implementationPlan3Report";
                ImplementationPlan3ReportParameterModel reportParameterModelPlan3 = new ImplementationPlan3ReportParameterModel();
                // 帳票サービスInDtoの設定
                setReportParameterModel(inDto, printSettings, reportParameterModelPlan3);
                // 事業者名
                reportParameterModelPlan3.setJigyoKnj(inDto.getSvJigyoKnj());
                // システム日付
                reportParameterModelPlan3.setAppYmd(inDto.getSysYmd());
                // 初期設定マスタの情報
                ImplementationPlan3ReportServiceInitMasterObj initMasterObjPlan3 = new ImplementationPlan3ReportServiceInitMasterObj();
                // 敬称オプション
                initMasterObjPlan3.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                // 敬称
                initMasterObjPlan3.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
                reportParameterModelPlan3.setInitMasterObj(initMasterObjPlan3);
                // 印刷設定
                PrintReportServicePrintSet printSetPlan3 = new PrintReportServicePrintSet();
                // 帳票タイトル
                printSetPlan3.setPrtTitle(printSettings.getPrtTitle());
                // セクション番号
                printSetPlan3.setSectionNo(printSettings.getSectionNo());
                // 帳票No
                printSetPlan3.setPrtNo(printSettings.getPrtNo());
                // プロファイル
                printSetPlan3.setProfile(printSettings.getProfile());
                // 日付表示有無
                printSetPlan3.setPrnDate(printSettings.getPrnDate());
                // 職員表示有無
                printSetPlan3.setPrnshoku(printSettings.getPrnshoku());
                // パラメータ01
                printSetPlan3.setParam01(printSettings.getParam01());
                // パラメータ02
                printSetPlan3.setParam02(printSettings.getParam02());
                // パラメータ03
                printSetPlan3.setParam03(printSettings.getParam03());
                // パラメータ04
                printSetPlan3.setParam04(printSettings.getParam04());
                // パラメータ05
                printSetPlan3.setParam05(printSettings.getParam05());
                // パラメータ06
                printSetPlan3.setParam06(printSettings.getParam06());
                // パラメータ07
                printSetPlan3.setParam07(printSettings.getParam07());
                // パラメータ08
                printSetPlan3.setParam08(printSettings.getParam08());
                // パラメータ09
                printSetPlan3.setParam09(printSettings.getParam09());
                // パラメータ10
                printSetPlan3.setParam10(printSettings.getParam10());
                // パラメータ11
                printSetPlan3.setParam11(printSettings.getParam11());
                // パラメータ12
                printSetPlan3.setParam12(printSettings.getParam12());
                // パラメータ13
                printSetPlan3.setParam13(printSettings.getParam13());
                // パラメータ14
                printSetPlan3.setParam14(printSettings.getParam14());
                // パラメータ15
                printSetPlan3.setParam15(printSettings.getParam15());
                // パラメータ16
                printSetPlan3.setParam16(printSettings.getParam16());
                // パラメータ17
                printSetPlan3.setParam17(printSettings.getParam17());
                // パラメータ18
                printSetPlan3.setParam18(printSettings.getParam18());
                // パラメータ19
                printSetPlan3.setParam19(printSettings.getParam19());
                // パラメータ20
                printSetPlan3.setParam20(printSettings.getParam20());
                // パラメータ21
                printSetPlan3.setParam21(printSettings.getParam21());
                // パラメータ22
                printSetPlan3.setParam22(printSettings.getParam22());
                // パラメータ23
                printSetPlan3.setParam23(printSettings.getParam23());
                // パラメータ24
                printSetPlan3.setParam24(printSettings.getParam24());
                // パラメータ25
                printSetPlan3.setParam25(printSettings.getParam25());
                // パラメータ26
                printSetPlan3.setParam26(printSettings.getParam26());
                // パラメータ27
                printSetPlan3.setParam27(printSettings.getParam27());
                // パラメータ28
                printSetPlan3.setParam28(printSettings.getParam28());
                // パラメータ29
                printSetPlan3.setParam29(printSettings.getParam29());
                // パラメータ30
                printSetPlan3.setParam30(printSettings.getParam30());
                // パラメータ31
                printSetPlan3.setParam31(printSettings.getParam31());
                // パラメータ32
                printSetPlan3.setParam32(printSettings.getParam32());
                // パラメータ33
                printSetPlan3.setParam33(printSettings.getParam33());
                // パラメータ34
                printSetPlan3.setParam34(printSettings.getParam34());
                // パラメータ35
                printSetPlan3.setParam35(printSettings.getParam35());
                // パラメータ36
                printSetPlan3.setParam36(printSettings.getParam36());
                // パラメータ37
                printSetPlan3.setParam37(printSettings.getParam37());
                // パラメータ38
                printSetPlan3.setParam38(printSettings.getParam38());
                // パラメータ39
                printSetPlan3.setParam39(printSettings.getParam39());
                // パラメータ40
                printSetPlan3.setParam40(printSettings.getParam40());
                // パラメータ41
                printSetPlan3.setParam41(printSettings.getParam41());
                // パラメータ42
                printSetPlan3.setParam42(printSettings.getParam42());
                // パラメータ43
                printSetPlan3.setParam43(printSettings.getParam43());
                // パラメータ44
                printSetPlan3.setParam44(printSettings.getParam44());
                // パラメータ45
                printSetPlan3.setParam45(printSettings.getParam45());
                // パラメータ46
                printSetPlan3.setParam46(printSettings.getParam46());
                // パラメータ47
                printSetPlan3.setParam47(printSettings.getParam47());
                // パラメータ48
                printSetPlan3.setParam48(printSettings.getParam48());
                // パラメータ49
                printSetPlan3.setParam49(printSettings.getParam49());
                // パラメータ50
                printSetPlan3.setParam50(printSettings.getParam50());
                reportParameterModelPlan3.setPrintSet(printSetPlan3);
                // DB未保存画面項目
                PrintReportServiceDbNoSaveData dbNoSaveDataPlan3 = new PrintReportServiceDbNoSaveData();
                // 指定日
                dbNoSaveDataPlan3.setEmptyFlg(inDto.getEmptyFlg());
                // 記入用シートを印刷するフラグ
                dbNoSaveDataPlan3.setSelectDate(inDto.getSelectDate());
                reportParameterModelPlan3.setDbNoSaveData(dbNoSaveDataPlan3);
                // 印刷対象履歴
                PrintReportServicePrintSubjectHistory printSubjectHistoryPlan3 = new PrintReportServicePrintSubjectHistory();
                // 利用者ID
                printSubjectHistoryPlan3.setUserId(inDto.getUserId());
                // 履歴ID
                printSubjectHistoryPlan3.setRirekiId(inDto.getRirekiId());
                reportParameterModelPlan3.setPrintSubjectHistory(printSubjectHistoryPlan3);

                // S3e文書保存処理共通関数を取得する
                try {
                    cmnStorageServiceUtil.uploadToS3EDocument(
                            prtId,
                            reportParameterModelPlan3, inDto.getLoginShokuinId(),
                            eDocumentUpdKbn,
                            inDto.getRirekiId(), inDto.getSc1Id());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            // 機能名が"[mnu3][3GK]計画書(1)"の場合
            case CommonConstants.KINOU_NAME_CARE_PLAN_1:
                // リクエストパラメータ.初期設定マスタの情報.計画書様式が"1:施設"の場合、"plan1sReport"
                if (CommonConstants.CKS_FLG_FACILITY.equals(inDto.getInitMasterObj().getCksFlg())) {
                    prtId = "plan1sReport";
                    Plan1kReportParameterModel plan1sReportParameterModel = new Plan1kReportParameterModel();
                    // 帳票サービスInDtoの設定
                    setReportParameterModel(inDto, printSettings, plan1sReportParameterModel);
                    // 事業者名
                    plan1sReportParameterModel.setJigyoKnj(inDto.getSvJigyoKnj());
                    // システム日付
                    plan1sReportParameterModel.setAppYmd(inDto.getSysYmd());
                    // 初期設定マスタの情報
                    Plan1InitMasterData plan1sinitMasterObj = new Plan1InitMasterData();
                    // 計画書書式
                    plan1sinitMasterObj.setShosikiFlg(inDto.getInitMasterObj().getShosikiFlg());
                    // 計画書様式
                    plan1sinitMasterObj.setCksFlg(inDto.getInitMasterObj().getCksFlg());
                    // 担当者（計画書（１））
                    plan1sinitMasterObj.setCks1TantoFlg(inDto.getInitMasterObj().getCks1TantoFlg());
                    // 要介護度（計画書（１））
                    plan1sinitMasterObj.setCks1YokaiFlg(inDto.getInitMasterObj().getCks1YokaiFlg());
                    // 印刷時の文字サイズ（計画書（１））
                    plan1sinitMasterObj.setCks1PrtSizeFlg(inDto.getInitMasterObj().getCks1PrtSizeFlg());
                    // 承認欄情報
                    plan1sinitMasterObj.setShoninFlg(inDto.getInitMasterObj().getShoninFlg());
                    // メッセージ表示
                    plan1sinitMasterObj.setMsgFlg(inDto.getInitMasterObj().getMsgFlg());
                    plan1sReportParameterModel.setInitMasterObj(plan1sinitMasterObj);
                    // 印刷設定
                    PrintReportServicePrintSet plan3PrintSet = new PrintReportServicePrintSet();
                    // 帳票タイトル
                    plan3PrintSet.setPrtTitle(printSettings.getPrtTitle());
                    // セクション番号
                    plan3PrintSet.setSectionNo(printSettings.getSectionNo());
                    // 帳票No
                    plan3PrintSet.setPrtNo(printSettings.getPrtNo());
                    // プロファイル
                    plan3PrintSet.setProfile(printSettings.getProfile());
                    // 日付表示有無
                    plan3PrintSet.setPrnDate(printSettings.getPrnDate());
                    // 職員表示有無
                    plan3PrintSet.setPrnshoku(printSettings.getPrnshoku());
                    // パラメータ01
                    plan3PrintSet.setParam01(printSettings.getParam01());
                    // パラメータ02
                    plan3PrintSet.setParam02(printSettings.getParam02());
                    // パラメータ03
                    plan3PrintSet.setParam03(printSettings.getParam03());
                    // パラメータ04
                    plan3PrintSet.setParam04(printSettings.getParam04());
                    // パラメータ05
                    plan3PrintSet.setParam05(printSettings.getParam05());
                    // パラメータ06
                    plan3PrintSet.setParam06(printSettings.getParam06());
                    // パラメータ07
                    plan3PrintSet.setParam07(printSettings.getParam07());
                    // パラメータ08
                    plan3PrintSet.setParam08(printSettings.getParam08());
                    // パラメータ09
                    plan3PrintSet.setParam09(printSettings.getParam09());
                    // パラメータ10
                    plan3PrintSet.setParam10(printSettings.getParam10());
                    // パラメータ11
                    plan3PrintSet.setParam11(printSettings.getParam11());
                    // パラメータ12
                    plan3PrintSet.setParam12(printSettings.getParam12());
                    // パラメータ13
                    plan3PrintSet.setParam13(printSettings.getParam13());
                    // パラメータ14
                    plan3PrintSet.setParam14(printSettings.getParam14());
                    // パラメータ15
                    plan3PrintSet.setParam15(printSettings.getParam15());
                    // パラメータ16
                    plan3PrintSet.setParam16(printSettings.getParam16());
                    // パラメータ17
                    plan3PrintSet.setParam17(printSettings.getParam17());
                    // パラメータ18
                    plan3PrintSet.setParam18(printSettings.getParam18());
                    // パラメータ19
                    plan3PrintSet.setParam19(printSettings.getParam19());
                    // パラメータ20
                    plan3PrintSet.setParam20(printSettings.getParam20());
                    // パラメータ21
                    plan3PrintSet.setParam21(printSettings.getParam21());
                    // パラメータ22
                    plan3PrintSet.setParam22(printSettings.getParam22());
                    // パラメータ23
                    plan3PrintSet.setParam23(printSettings.getParam23());
                    // パラメータ24
                    plan3PrintSet.setParam24(printSettings.getParam24());
                    // パラメータ25
                    plan3PrintSet.setParam25(printSettings.getParam25());
                    // パラメータ26
                    plan3PrintSet.setParam26(printSettings.getParam26());
                    // パラメータ27
                    plan3PrintSet.setParam27(printSettings.getParam27());
                    // パラメータ28
                    plan3PrintSet.setParam28(printSettings.getParam28());
                    // パラメータ29
                    plan3PrintSet.setParam29(printSettings.getParam29());
                    // パラメータ30
                    plan3PrintSet.setParam30(printSettings.getParam30());
                    // パラメータ31
                    plan3PrintSet.setParam31(printSettings.getParam31());
                    // パラメータ32
                    plan3PrintSet.setParam32(printSettings.getParam32());
                    // パラメータ33
                    plan3PrintSet.setParam33(printSettings.getParam33());
                    // パラメータ34
                    plan3PrintSet.setParam34(printSettings.getParam34());
                    // パラメータ35
                    plan3PrintSet.setParam35(printSettings.getParam35());
                    // パラメータ36
                    plan3PrintSet.setParam36(printSettings.getParam36());
                    // パラメータ37
                    plan3PrintSet.setParam37(printSettings.getParam37());
                    // パラメータ38
                    plan3PrintSet.setParam38(printSettings.getParam38());
                    // パラメータ39
                    plan3PrintSet.setParam39(printSettings.getParam39());
                    // パラメータ40
                    plan3PrintSet.setParam40(printSettings.getParam40());
                    // パラメータ41
                    plan3PrintSet.setParam41(printSettings.getParam41());
                    // パラメータ42
                    plan3PrintSet.setParam42(printSettings.getParam42());
                    // パラメータ43
                    plan3PrintSet.setParam43(printSettings.getParam43());
                    // パラメータ44
                    plan3PrintSet.setParam44(printSettings.getParam44());
                    // パラメータ45
                    plan3PrintSet.setParam45(printSettings.getParam45());
                    // パラメータ46
                    plan3PrintSet.setParam46(printSettings.getParam46());
                    // パラメータ47
                    plan3PrintSet.setParam47(printSettings.getParam47());
                    // パラメータ48
                    plan3PrintSet.setParam48(printSettings.getParam48());
                    // パラメータ49
                    plan3PrintSet.setParam49(printSettings.getParam49());
                    // パラメータ50
                    plan3PrintSet.setParam50(printSettings.getParam50());
                    plan1sReportParameterModel.setPrintSet(plan3PrintSet);
                    // DB未保存画面項目
                    Plan1kReportServiceDbNoSaveData plan1sDbNoSaveData = new Plan1kReportServiceDbNoSaveData();
                    // 指定日
                    plan1sDbNoSaveData.setEmptyFlg(inDto.getEmptyFlg());
                    // 記入用シートを印刷するフラグ
                    plan1sDbNoSaveData.setSelectDate(inDto.getSelectDate());
                    // システムコード
                    plan1sDbNoSaveData.setSysCd(inDto.getSysCd());
                    // 職員Id
                    plan1sDbNoSaveData.setShokuinId(inDto.getLoginShokuinId());
                    plan1sReportParameterModel.setDbNoSaveData(plan1sDbNoSaveData);
                    // 印刷対象履歴
                    PrintReportServicePrintSubjectHistory plan1sPrintSubjectHistory = new PrintReportServicePrintSubjectHistory();
                    // 利用者ID
                    plan1sPrintSubjectHistory.setUserId(inDto.getUserId());
                    // 履歴ID
                    plan1sPrintSubjectHistory.setRirekiId(inDto.getRirekiId());
                    plan1sReportParameterModel.setPrintSubjectHistory(plan1sPrintSubjectHistory);
                    // S3e文書保存処理共通関数を取得する
                    try {
                        cmnStorageServiceUtil.uploadToS3EDocument(
                                prtId,
                                plan1sReportParameterModel, inDto.getLoginShokuinId(),
                                eDocumentUpdKbn,
                                inDto.getRirekiId(), inDto.getSc1Id());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    // リクエストパラメータ.初期設定マスタの情報.計画書様式が"2:居宅"の場合、"plan1kReport"
                } else if (CommonConstants.CKS_FLG_HOME.equals(inDto.getInitMasterObj().getCksFlg())) {
                    prtId = "plan1kReport";
                    Plan1kReportParameterModel plan1kReportParameterModel = new Plan1kReportParameterModel();
                    // 帳票サービスInDtoの設定
                    setReportParameterModel(inDto, printSettings, plan1kReportParameterModel);
                    // 事業者名
                    plan1kReportParameterModel.setJigyoKnj(inDto.getSvJigyoKnj());
                    // システム日付
                    plan1kReportParameterModel.setAppYmd(inDto.getSysYmd());
                    // 初期設定マスタの情報
                    Plan1InitMasterData plan1kinitMasterObj = new Plan1InitMasterData();
                    // 計画書書式
                    plan1kinitMasterObj.setShosikiFlg(inDto.getInitMasterObj().getShosikiFlg());
                    // 計画書様式
                    plan1kinitMasterObj.setCksFlg(inDto.getInitMasterObj().getCksFlg());
                    // 担当者（計画書（１））
                    plan1kinitMasterObj.setCks1TantoFlg(inDto.getInitMasterObj().getCks1TantoFlg());
                    // 要介護度（計画書（１））
                    plan1kinitMasterObj.setCks1YokaiFlg(inDto.getInitMasterObj().getCks1YokaiFlg());
                    // 印刷時の文字サイズ（計画書（１））
                    plan1kinitMasterObj.setCks1PrtSizeFlg(inDto.getInitMasterObj().getCks1PrtSizeFlg());
                    // 承認欄情報
                    plan1kinitMasterObj.setShoninFlg(inDto.getInitMasterObj().getShoninFlg());
                    // メッセージ表示
                    plan1kinitMasterObj.setMsgFlg(inDto.getInitMasterObj().getMsgFlg());
                    plan1kReportParameterModel.setInitMasterObj(plan1kinitMasterObj);
                    // 印刷設定
                    PrintReportServicePrintSet plan1kPrintSet = new PrintReportServicePrintSet();
                    // 帳票タイトル
                    plan1kPrintSet.setPrtTitle(printSettings.getPrtTitle());
                    // セクション番号
                    plan1kPrintSet.setSectionNo(printSettings.getSectionNo());
                    // 帳票No
                    plan1kPrintSet.setPrtNo(printSettings.getPrtNo());
                    // プロファイル
                    plan1kPrintSet.setProfile(printSettings.getProfile());
                    // 日付表示有無
                    plan1kPrintSet.setPrnDate(printSettings.getPrnDate());
                    // 職員表示有無
                    plan1kPrintSet.setPrnshoku(printSettings.getPrnshoku());
                    // パラメータ01
                    plan1kPrintSet.setParam01(printSettings.getParam01());
                    // パラメータ02
                    plan1kPrintSet.setParam02(printSettings.getParam02());
                    // パラメータ03
                    plan1kPrintSet.setParam03(printSettings.getParam03());
                    // パラメータ04
                    plan1kPrintSet.setParam04(printSettings.getParam04());
                    // パラメータ05
                    plan1kPrintSet.setParam05(printSettings.getParam05());
                    // パラメータ06
                    plan1kPrintSet.setParam06(printSettings.getParam06());
                    // パラメータ07
                    plan1kPrintSet.setParam07(printSettings.getParam07());
                    // パラメータ08
                    plan1kPrintSet.setParam08(printSettings.getParam08());
                    // パラメータ09
                    plan1kPrintSet.setParam09(printSettings.getParam09());
                    // パラメータ10
                    plan1kPrintSet.setParam10(printSettings.getParam10());
                    // パラメータ11
                    plan1kPrintSet.setParam11(printSettings.getParam11());
                    // パラメータ12
                    plan1kPrintSet.setParam12(printSettings.getParam12());
                    // パラメータ13
                    plan1kPrintSet.setParam13(printSettings.getParam13());
                    // パラメータ14
                    plan1kPrintSet.setParam14(printSettings.getParam14());
                    // パラメータ15
                    plan1kPrintSet.setParam15(printSettings.getParam15());
                    // パラメータ16
                    plan1kPrintSet.setParam16(printSettings.getParam16());
                    // パラメータ17
                    plan1kPrintSet.setParam17(printSettings.getParam17());
                    // パラメータ18
                    plan1kPrintSet.setParam18(printSettings.getParam18());
                    // パラメータ19
                    plan1kPrintSet.setParam19(printSettings.getParam19());
                    // パラメータ20
                    plan1kPrintSet.setParam20(printSettings.getParam20());
                    // パラメータ21
                    plan1kPrintSet.setParam21(printSettings.getParam21());
                    // パラメータ22
                    plan1kPrintSet.setParam22(printSettings.getParam22());
                    // パラメータ23
                    plan1kPrintSet.setParam23(printSettings.getParam23());
                    // パラメータ24
                    plan1kPrintSet.setParam24(printSettings.getParam24());
                    // パラメータ25
                    plan1kPrintSet.setParam25(printSettings.getParam25());
                    // パラメータ26
                    plan1kPrintSet.setParam26(printSettings.getParam26());
                    // パラメータ27
                    plan1kPrintSet.setParam27(printSettings.getParam27());
                    // パラメータ28
                    plan1kPrintSet.setParam28(printSettings.getParam28());
                    // パラメータ29
                    plan1kPrintSet.setParam29(printSettings.getParam29());
                    // パラメータ30
                    plan1kPrintSet.setParam30(printSettings.getParam30());
                    // パラメータ31
                    plan1kPrintSet.setParam31(printSettings.getParam31());
                    // パラメータ32
                    plan1kPrintSet.setParam32(printSettings.getParam32());
                    // パラメータ33
                    plan1kPrintSet.setParam33(printSettings.getParam33());
                    // パラメータ34
                    plan1kPrintSet.setParam34(printSettings.getParam34());
                    // パラメータ35
                    plan1kPrintSet.setParam35(printSettings.getParam35());
                    // パラメータ36
                    plan1kPrintSet.setParam36(printSettings.getParam36());
                    // パラメータ37
                    plan1kPrintSet.setParam37(printSettings.getParam37());
                    // パラメータ38
                    plan1kPrintSet.setParam38(printSettings.getParam38());
                    // パラメータ39
                    plan1kPrintSet.setParam39(printSettings.getParam39());
                    // パラメータ40
                    plan1kPrintSet.setParam40(printSettings.getParam40());
                    // パラメータ41
                    plan1kPrintSet.setParam41(printSettings.getParam41());
                    // パラメータ42
                    plan1kPrintSet.setParam42(printSettings.getParam42());
                    // パラメータ43
                    plan1kPrintSet.setParam43(printSettings.getParam43());
                    // パラメータ44
                    plan1kPrintSet.setParam44(printSettings.getParam44());
                    // パラメータ45
                    plan1kPrintSet.setParam45(printSettings.getParam45());
                    // パラメータ46
                    plan1kPrintSet.setParam46(printSettings.getParam46());
                    // パラメータ47
                    plan1kPrintSet.setParam47(printSettings.getParam47());
                    // パラメータ48
                    plan1kPrintSet.setParam48(printSettings.getParam48());
                    // パラメータ49
                    plan1kPrintSet.setParam49(printSettings.getParam49());
                    // パラメータ50
                    plan1kPrintSet.setParam50(printSettings.getParam50());
                    plan1kReportParameterModel.setPrintSet(plan1kPrintSet);
                    // DB未保存画面項目
                    Plan1kReportServiceDbNoSaveData plan1kDbNoSaveData = new Plan1kReportServiceDbNoSaveData();
                    // 指定日
                    plan1kDbNoSaveData.setEmptyFlg(inDto.getEmptyFlg());
                    // 記入用シートを印刷するフラグ
                    plan1kDbNoSaveData.setSelectDate(inDto.getSelectDate());
                    // システムコード
                    plan1kDbNoSaveData.setSysCd(inDto.getSysCd());
                    // 職員Id
                    plan1kDbNoSaveData.setShokuinId(inDto.getLoginShokuinId());
                    plan1kReportParameterModel.setDbNoSaveData(plan1kDbNoSaveData);
                    // 印刷対象履歴
                    PrintReportServicePrintSubjectHistory plan1kPrintSubjectHistory = new PrintReportServicePrintSubjectHistory();
                    // 利用者ID
                    plan1kPrintSubjectHistory.setUserId(inDto.getUserId());
                    // 履歴ID
                    plan1kPrintSubjectHistory.setRirekiId(inDto.getRirekiId());
                    plan1kReportParameterModel.setPrintSubjectHistory(plan1kPrintSubjectHistory);
                    // S3e文書保存処理共通関数を取得する
                    try {
                        cmnStorageServiceUtil.uploadToS3EDocument(
                                prtId,
                                plan1kReportParameterModel, inDto.getLoginShokuinId(),
                                eDocumentUpdKbn,
                                inDto.getRirekiId(), inDto.getSc1Id());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                break;
            // 機能名が"[mnu3][3GK]日課計画"の場合
            case CommonConstants.KINOU_NAME_DAY_PLAN:
                prtId = "dailyRoutinePlanReport";
                DailyRoutinePlanReportParameterModel dailyRoutinePlanReportParameterModel = new DailyRoutinePlanReportParameterModel();
                // 帳票サービスInDtoの設定
                setReportParameterModel(inDto, printSettings, dailyRoutinePlanReportParameterModel);
                // 事業者名
                dailyRoutinePlanReportParameterModel.setJigyoKnj(inDto.getSvJigyoKnj());
                // システム日付
                dailyRoutinePlanReportParameterModel.setAppYmd(inDto.getSysYmd());
                // 初期設定マスタの情報
                KghMocKrkSsmMasterInfoDto dailyRoutinePlanInitMasterObj = new KghMocKrkSsmMasterInfoDto();
                // パッケージプラン改訂フラグ
                dailyRoutinePlanInitMasterObj.setPkaiteiFlg(inDto.getInitMasterObj().getPkaiteiFlg());
                // 日課表：メモ表示
                dailyRoutinePlanInitMasterObj.setPdayMemoFlg(inDto.getInitMasterObj().getPdayMemoFlg());
                // 日課表：時間表示
                dailyRoutinePlanInitMasterObj.setPdayTimeFlg(inDto.getInitMasterObj().getPdayTimeFlg());
                // ケアプラン方式
                dailyRoutinePlanInitMasterObj.setCpnFlg(inDto.getInitMasterObj().getCpnFlg());
                // 敬称オプション
                dailyRoutinePlanInitMasterObj.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                // 敬称
                dailyRoutinePlanInitMasterObj.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
                dailyRoutinePlanReportParameterModel.setInitMasterObj(dailyRoutinePlanInitMasterObj);
                // 印刷設定
                DailyRoutinePlanReportServicePrintSet dailyRoutinePlanPrintSet = new DailyRoutinePlanReportServicePrintSet();
                // 帳票タイトル
                dailyRoutinePlanPrintSet.setPrtTitle(printSettings.getPrtTitle());
                // セクション番号
                dailyRoutinePlanPrintSet.setSectionNo(printSettings.getSectionNo());
                // 帳票No
                dailyRoutinePlanPrintSet.setPrtNo(printSettings.getPrtNo());
                // プロファイル
                dailyRoutinePlanPrintSet.setProfile(printSettings.getProfile());
                // 日付表示有無
                dailyRoutinePlanPrintSet.setPrnDate(printSettings.getPrnDate());
                // 職員表示有無
                dailyRoutinePlanPrintSet.setPrnshoku(printSettings.getPrnshoku());
                // パラメータ01
                dailyRoutinePlanPrintSet.setParam01(printSettings.getParam01());
                // パラメータ02
                dailyRoutinePlanPrintSet.setParam02(printSettings.getParam02());
                // パラメータ03
                dailyRoutinePlanPrintSet.setParam03(printSettings.getParam03());
                // パラメータ04
                dailyRoutinePlanPrintSet.setParam04(printSettings.getParam04());
                // パラメータ05
                dailyRoutinePlanPrintSet.setParam05(printSettings.getParam05());
                // パラメータ06
                dailyRoutinePlanPrintSet.setParam06(printSettings.getParam06());
                // パラメータ07
                dailyRoutinePlanPrintSet.setParam07(printSettings.getParam07());
                // パラメータ08
                dailyRoutinePlanPrintSet.setParam08(printSettings.getParam08());
                // パラメータ09
                dailyRoutinePlanPrintSet.setParam09(printSettings.getParam09());
                // パラメータ10
                dailyRoutinePlanPrintSet.setParam10(printSettings.getParam10());
                // パラメータ11
                dailyRoutinePlanPrintSet.setParam11(printSettings.getParam11());
                // パラメータ12
                dailyRoutinePlanPrintSet.setParam12(printSettings.getParam12());
                // パラメータ13
                dailyRoutinePlanPrintSet.setParam13(printSettings.getParam13());
                // パラメータ14
                dailyRoutinePlanPrintSet.setParam14(printSettings.getParam14());
                // パラメータ15
                dailyRoutinePlanPrintSet.setParam15(printSettings.getParam15());
                // パラメータ16
                dailyRoutinePlanPrintSet.setParam16(printSettings.getParam16());
                // パラメータ17
                dailyRoutinePlanPrintSet.setParam17(printSettings.getParam17());
                // パラメータ18
                dailyRoutinePlanPrintSet.setParam18(printSettings.getParam18());
                // パラメータ19
                dailyRoutinePlanPrintSet.setParam19(printSettings.getParam19());
                // パラメータ20
                dailyRoutinePlanPrintSet.setParam20(printSettings.getParam20());
                // パラメータ21
                dailyRoutinePlanPrintSet.setParam21(printSettings.getParam21());
                // パラメータ22
                dailyRoutinePlanPrintSet.setParam22(printSettings.getParam22());
                // パラメータ23
                dailyRoutinePlanPrintSet.setParam23(printSettings.getParam23());
                // パラメータ24
                dailyRoutinePlanPrintSet.setParam24(printSettings.getParam24());
                // パラメータ25
                dailyRoutinePlanPrintSet.setParam25(printSettings.getParam25());
                // パラメータ26
                dailyRoutinePlanPrintSet.setParam26(printSettings.getParam26());
                // パラメータ27
                dailyRoutinePlanPrintSet.setParam27(printSettings.getParam27());
                // パラメータ28
                dailyRoutinePlanPrintSet.setParam28(printSettings.getParam28());
                // パラメータ29
                dailyRoutinePlanPrintSet.setParam29(printSettings.getParam29());
                // パラメータ30
                dailyRoutinePlanPrintSet.setParam30(printSettings.getParam30());
                // パラメータ31
                dailyRoutinePlanPrintSet.setParam31(printSettings.getParam31());
                // パラメータ32
                dailyRoutinePlanPrintSet.setParam32(printSettings.getParam32());
                // パラメータ33
                dailyRoutinePlanPrintSet.setParam33(printSettings.getParam33());
                // パラメータ34
                dailyRoutinePlanPrintSet.setParam34(printSettings.getParam34());
                // パラメータ35
                dailyRoutinePlanPrintSet.setParam35(printSettings.getParam35());
                // パラメータ36
                dailyRoutinePlanPrintSet.setParam36(printSettings.getParam36());
                // パラメータ37
                dailyRoutinePlanPrintSet.setParam37(printSettings.getParam37());
                // パラメータ38
                dailyRoutinePlanPrintSet.setParam38(printSettings.getParam38());
                // パラメータ39
                dailyRoutinePlanPrintSet.setParam39(printSettings.getParam39());
                // パラメータ40
                dailyRoutinePlanPrintSet.setParam40(printSettings.getParam40());
                // パラメータ41
                dailyRoutinePlanPrintSet.setParam41(printSettings.getParam41());
                // パラメータ42
                dailyRoutinePlanPrintSet.setParam42(printSettings.getParam42());
                // パラメータ43
                dailyRoutinePlanPrintSet.setParam43(printSettings.getParam43());
                // パラメータ44
                dailyRoutinePlanPrintSet.setParam44(printSettings.getParam44());
                // パラメータ45
                dailyRoutinePlanPrintSet.setParam45(printSettings.getParam45());
                // パラメータ46
                dailyRoutinePlanPrintSet.setParam46(printSettings.getParam46());
                // パラメータ47
                dailyRoutinePlanPrintSet.setParam47(printSettings.getParam47());
                // パラメータ48
                dailyRoutinePlanPrintSet.setParam48(printSettings.getParam48());
                // パラメータ49
                dailyRoutinePlanPrintSet.setParam49(printSettings.getParam49());
                // パラメータ50
                dailyRoutinePlanPrintSet.setParam50(printSettings.getParam50());
                dailyRoutinePlanReportParameterModel.setPrintSet(dailyRoutinePlanPrintSet);
                // DB未保存画面項目
                DailyRoutinePlanReportServiceDbNoSaveData dailyRoutinePlanDbNoSaveData = new DailyRoutinePlanReportServiceDbNoSaveData();
                // 指定日
                dailyRoutinePlanDbNoSaveData.setEmptyFlg(inDto.getEmptyFlg());
                // 記入用シートを印刷するフラグ
                dailyRoutinePlanDbNoSaveData.setSelectDate(inDto.getSelectDate());
                // 作成日
                dailyRoutinePlanDbNoSaveData.setCreateYmd(inDto.getCreateYmd());
                dailyRoutinePlanReportParameterModel.setDbNoSaveData(dailyRoutinePlanDbNoSaveData);
                // 印刷対象履歴
                DailyRoutinePlanReportServicePrintSubjectHistory dailyRoutinePlanPrintSubjectHistory = new DailyRoutinePlanReportServicePrintSubjectHistory();
                // 利用者ID
                dailyRoutinePlanPrintSubjectHistory.setUserId(inDto.getUserId());
                // 履歴ID
                dailyRoutinePlanPrintSubjectHistory.setRirekiId(inDto.getRirekiId());
                // 計画期間ID
                dailyRoutinePlanPrintSubjectHistory.setSc1Id(inDto.getSc1Id());
                dailyRoutinePlanReportParameterModel.setPrintSubjectHistory(dailyRoutinePlanPrintSubjectHistory);
                // S3e文書保存処理共通関数を取得する
                try {
                    cmnStorageServiceUtil.uploadToS3EDocument(
                            prtId,
                            dailyRoutinePlanReportParameterModel, inDto.getLoginShokuinId(),
                            eDocumentUpdKbn,
                            inDto.getRirekiId(), inDto.getSc1Id());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            // 機能名が"[mnu3][3GK]日課表"の場合
            case CommonConstants.KINOU_NAME_DAILY_SCHEDULE:
                prtId = "DailyTaskTableA3Report";
                DailyTaskTableA3ReportParameterModel dailyTaskTableA3ReportParameterModel = new DailyTaskTableA3ReportParameterModel();
                // 帳票サービスInDtoの設定
                setReportParameterModel(inDto, printSettings, dailyTaskTableA3ReportParameterModel);
                // 事業者名
                dailyTaskTableA3ReportParameterModel.setJigyoKnj(inDto.getSvJigyoKnj());
                // システム日付
                dailyTaskTableA3ReportParameterModel.setAppYmd(inDto.getSysYmd());
                // 初期設定マスタの情報
                KghMocKrkSsmMasterInfoDto dailyTaskTableA3InitMasterObj = new KghMocKrkSsmMasterInfoDto();
                // パッケージプラン改訂フラグ
                dailyTaskTableA3InitMasterObj.setPkaiteiFlg(inDto.getInitMasterObj().getPkaiteiFlg());
                // 日課表：メモ表示
                dailyTaskTableA3InitMasterObj.setPdayMemoFlg(inDto.getInitMasterObj().getPdayMemoFlg());
                // 日課表：時間表示
                dailyTaskTableA3InitMasterObj.setPdayTimeFlg(inDto.getInitMasterObj().getPdayTimeFlg());
                // ケアプラン方式
                dailyTaskTableA3InitMasterObj.setCpnFlg(inDto.getInitMasterObj().getCpnFlg());
                // 敬称オプション
                dailyTaskTableA3InitMasterObj.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                // 敬称
                dailyTaskTableA3InitMasterObj.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
                dailyTaskTableA3ReportParameterModel.setInitMasterObj(dailyTaskTableA3InitMasterObj);
                // 印刷設定
                PrintReportServicePrintSet dailyTaskTableA3PrintSet = new PrintReportServicePrintSet();
                // 帳票タイトル
                dailyTaskTableA3PrintSet.setPrtTitle(printSettings.getPrtTitle());
                // セクション番号
                dailyTaskTableA3PrintSet.setSectionNo(printSettings.getSectionNo());
                // 帳票No
                dailyTaskTableA3PrintSet.setPrtNo(printSettings.getPrtNo());
                // プロファイル
                dailyTaskTableA3PrintSet.setProfile(printSettings.getProfile());
                // 日付表示有無
                dailyTaskTableA3PrintSet.setPrnDate(printSettings.getPrnDate());
                // 職員表示有無
                dailyTaskTableA3PrintSet.setPrnshoku(printSettings.getPrnshoku());
                // パラメータ01
                dailyTaskTableA3PrintSet.setParam01(printSettings.getParam01());
                // パラメータ02
                dailyTaskTableA3PrintSet.setParam02(printSettings.getParam02());
                // パラメータ03
                dailyTaskTableA3PrintSet.setParam03(printSettings.getParam03());
                // パラメータ04
                dailyTaskTableA3PrintSet.setParam04(printSettings.getParam04());
                // パラメータ05
                dailyTaskTableA3PrintSet.setParam05(printSettings.getParam05());
                // パラメータ06
                dailyTaskTableA3PrintSet.setParam06(printSettings.getParam06());
                // パラメータ07
                dailyTaskTableA3PrintSet.setParam07(printSettings.getParam07());
                // パラメータ08
                dailyTaskTableA3PrintSet.setParam08(printSettings.getParam08());
                // パラメータ09
                dailyTaskTableA3PrintSet.setParam09(printSettings.getParam09());
                // パラメータ10
                dailyTaskTableA3PrintSet.setParam10(printSettings.getParam10());
                // パラメータ11
                dailyTaskTableA3PrintSet.setParam11(printSettings.getParam11());
                // パラメータ12
                dailyTaskTableA3PrintSet.setParam12(printSettings.getParam12());
                // パラメータ13
                dailyTaskTableA3PrintSet.setParam13(printSettings.getParam13());
                // パラメータ14
                dailyTaskTableA3PrintSet.setParam14(printSettings.getParam14());
                // パラメータ15
                dailyTaskTableA3PrintSet.setParam15(printSettings.getParam15());
                // パラメータ16
                dailyTaskTableA3PrintSet.setParam16(printSettings.getParam16());
                // パラメータ17
                dailyTaskTableA3PrintSet.setParam17(printSettings.getParam17());
                // パラメータ18
                dailyTaskTableA3PrintSet.setParam18(printSettings.getParam18());
                // パラメータ19
                dailyTaskTableA3PrintSet.setParam19(printSettings.getParam19());
                // パラメータ20
                dailyTaskTableA3PrintSet.setParam20(printSettings.getParam20());
                // パラメータ21
                dailyTaskTableA3PrintSet.setParam21(printSettings.getParam21());
                // パラメータ22
                dailyTaskTableA3PrintSet.setParam22(printSettings.getParam22());
                // パラメータ23
                dailyTaskTableA3PrintSet.setParam23(printSettings.getParam23());
                // パラメータ24
                dailyTaskTableA3PrintSet.setParam24(printSettings.getParam24());
                // パラメータ25
                dailyTaskTableA3PrintSet.setParam25(printSettings.getParam25());
                // パラメータ26
                dailyTaskTableA3PrintSet.setParam26(printSettings.getParam26());
                // パラメータ27
                dailyTaskTableA3PrintSet.setParam27(printSettings.getParam27());
                // パラメータ28
                dailyTaskTableA3PrintSet.setParam28(printSettings.getParam28());
                // パラメータ29
                dailyTaskTableA3PrintSet.setParam29(printSettings.getParam29());
                // パラメータ30
                dailyTaskTableA3PrintSet.setParam30(printSettings.getParam30());
                // パラメータ31
                dailyTaskTableA3PrintSet.setParam31(printSettings.getParam31());
                // パラメータ32
                dailyTaskTableA3PrintSet.setParam32(printSettings.getParam32());
                // パラメータ33
                dailyTaskTableA3PrintSet.setParam33(printSettings.getParam33());
                // パラメータ34
                dailyTaskTableA3PrintSet.setParam34(printSettings.getParam34());
                // パラメータ35
                dailyTaskTableA3PrintSet.setParam35(printSettings.getParam35());
                // パラメータ36
                dailyTaskTableA3PrintSet.setParam36(printSettings.getParam36());
                // パラメータ37
                dailyTaskTableA3PrintSet.setParam37(printSettings.getParam37());
                // パラメータ38
                dailyTaskTableA3PrintSet.setParam38(printSettings.getParam38());
                // パラメータ39
                dailyTaskTableA3PrintSet.setParam39(printSettings.getParam39());
                // パラメータ40
                dailyTaskTableA3PrintSet.setParam40(printSettings.getParam40());
                // パラメータ41
                dailyTaskTableA3PrintSet.setParam41(printSettings.getParam41());
                // パラメータ42
                dailyTaskTableA3PrintSet.setParam42(printSettings.getParam42());
                // パラメータ43
                dailyTaskTableA3PrintSet.setParam43(printSettings.getParam43());
                // パラメータ44
                dailyTaskTableA3PrintSet.setParam44(printSettings.getParam44());
                // パラメータ45
                dailyTaskTableA3PrintSet.setParam45(printSettings.getParam45());
                // パラメータ46
                dailyTaskTableA3PrintSet.setParam46(printSettings.getParam46());
                // パラメータ47
                dailyTaskTableA3PrintSet.setParam47(printSettings.getParam47());
                // パラメータ48
                dailyTaskTableA3PrintSet.setParam48(printSettings.getParam48());
                // パラメータ49
                dailyTaskTableA3PrintSet.setParam49(printSettings.getParam49());
                // パラメータ50
                dailyTaskTableA3PrintSet.setParam50(printSettings.getParam50());
                dailyTaskTableA3ReportParameterModel.setPrintSet(dailyTaskTableA3PrintSet);
                // DB未保存画面項目
                PrintReportServiceDbNoSaveData dailyTaskTableA3DbNoSaveData = new PrintReportServiceDbNoSaveData();
                // 指定日
                dailyTaskTableA3DbNoSaveData.setEmptyFlg(inDto.getEmptyFlg());
                // 記入用シートを印刷するフラグ
                dailyTaskTableA3DbNoSaveData.setSelectDate(inDto.getSelectDate());
                dailyTaskTableA3ReportParameterModel.setDbNoSaveData(dailyTaskTableA3DbNoSaveData);
                // 印刷対象履歴
                PrintReportServicePrintSubjectHistory dailyTaskTableA3PrintSubjectHistory = new PrintReportServicePrintSubjectHistory();
                // 利用者ID
                dailyTaskTableA3PrintSubjectHistory.setUserId(inDto.getUserId());
                // 履歴ID
                dailyTaskTableA3PrintSubjectHistory.setRirekiId(inDto.getRirekiId());
                dailyTaskTableA3ReportParameterModel.setPrintSubjectHistory(dailyTaskTableA3PrintSubjectHistory);
                // S3e文書保存処理共通関数を取得する
                try {
                    cmnStorageServiceUtil.uploadToS3EDocument(
                            prtId,
                            dailyTaskTableA3ReportParameterModel, inDto.getLoginShokuinId(),
                            eDocumentUpdKbn,
                            inDto.getRirekiId(), inDto.getSc1Id());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            // 機能名が"[mnu3][3GK]月間・年間表"の場合
            case CommonConstants.KINOU_NAME_MONTHLY_YEARLY_CALENDAR:
                prtId = "monthlyYearlyReport";
                MonthlyYearlyReportParameterModel monthlyYearlyReportParameterModel = new MonthlyYearlyReportParameterModel();
                // 帳票サービスInDtoの設定
                setReportParameterModel(inDto, printSettings, monthlyYearlyReportParameterModel);
                // 事業者名
                monthlyYearlyReportParameterModel.setJigyoKnj(inDto.getSvJigyoKnj());
                // システム日付
                monthlyYearlyReportParameterModel.setAppYmd(inDto.getSysYmd());

                // 初期設定マスタの情報
                KghMocKrkSsmMasterInfoDto monthlyYearlyReportMasterInfoDto = new KghMocKrkSsmMasterInfoDto();
                // パッケージプラン改訂フラグ
                monthlyYearlyReportMasterInfoDto.setPkaiteiFlg(inDto.getInitMasterObj().getPkaiteiFlg());
                // 日課表：メモ表示
                monthlyYearlyReportMasterInfoDto.setPdayMemoFlg(inDto.getInitMasterObj().getPdayMemoFlg());
                // 日課表：時間表示
                monthlyYearlyReportMasterInfoDto.setPdayTimeFlg(inDto.getInitMasterObj().getPdayTimeFlg());
                // ケアプラン方式
                monthlyYearlyReportMasterInfoDto.setCpnFlg(inDto.getInitMasterObj().getCpnFlg());
                // 敬称オプション
                monthlyYearlyReportMasterInfoDto.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                // 敬称
                monthlyYearlyReportMasterInfoDto.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
                monthlyYearlyReportParameterModel.setInitMasterObj(monthlyYearlyReportMasterInfoDto);
                /** 印刷設定 */
                PrintReportServicePrintSet monthlyYearlyReportPrintSet = new PrintReportServicePrintSet();
                // 帳票タイトル
                monthlyYearlyReportPrintSet.setPrtTitle(printSettings.getPrtTitle());
                // セクション番号
                monthlyYearlyReportPrintSet.setSectionNo(printSettings.getSectionNo());
                // 帳票No
                monthlyYearlyReportPrintSet.setPrtNo(printSettings.getPrtNo());
                // プロファイル
                monthlyYearlyReportPrintSet.setProfile(printSettings.getProfile());
                // 日付表示有無
                monthlyYearlyReportPrintSet.setPrnDate(printSettings.getPrnDate());
                // 職員表示有無
                monthlyYearlyReportPrintSet.setPrnshoku(printSettings.getPrnshoku());
                // パラメータ01
                monthlyYearlyReportPrintSet.setParam01(printSettings.getParam01());
                // パラメータ02
                monthlyYearlyReportPrintSet.setParam02(printSettings.getParam02());
                // パラメータ03
                monthlyYearlyReportPrintSet.setParam03(printSettings.getParam03());
                // パラメータ04
                monthlyYearlyReportPrintSet.setParam04(printSettings.getParam04());
                // パラメータ05
                monthlyYearlyReportPrintSet.setParam05(printSettings.getParam05());
                // パラメータ06
                monthlyYearlyReportPrintSet.setParam06(printSettings.getParam06());
                // パラメータ07
                monthlyYearlyReportPrintSet.setParam07(printSettings.getParam07());
                // パラメータ08
                monthlyYearlyReportPrintSet.setParam08(printSettings.getParam08());
                // パラメータ09
                monthlyYearlyReportPrintSet.setParam09(printSettings.getParam09());
                // パラメータ10
                monthlyYearlyReportPrintSet.setParam10(printSettings.getParam10());
                // パラメータ11
                monthlyYearlyReportPrintSet.setParam11(printSettings.getParam11());
                // パラメータ12
                monthlyYearlyReportPrintSet.setParam12(printSettings.getParam12());
                // パラメータ13
                monthlyYearlyReportPrintSet.setParam13(printSettings.getParam13());
                // パラメータ14
                monthlyYearlyReportPrintSet.setParam14(printSettings.getParam14());
                // パラメータ15
                monthlyYearlyReportPrintSet.setParam15(printSettings.getParam15());
                // パラメータ16
                monthlyYearlyReportPrintSet.setParam16(printSettings.getParam16());
                // パラメータ17
                monthlyYearlyReportPrintSet.setParam17(printSettings.getParam17());
                // パラメータ18
                monthlyYearlyReportPrintSet.setParam18(printSettings.getParam18());
                // パラメータ19
                monthlyYearlyReportPrintSet.setParam19(printSettings.getParam19());
                // パラメータ20
                monthlyYearlyReportPrintSet.setParam20(printSettings.getParam20());
                // パラメータ21
                monthlyYearlyReportPrintSet.setParam21(printSettings.getParam21());
                // パラメータ22
                monthlyYearlyReportPrintSet.setParam22(printSettings.getParam22());
                // パラメータ23
                monthlyYearlyReportPrintSet.setParam23(printSettings.getParam23());
                // パラメータ24
                monthlyYearlyReportPrintSet.setParam24(printSettings.getParam24());
                // パラメータ25
                monthlyYearlyReportPrintSet.setParam25(printSettings.getParam25());
                // パラメータ26
                monthlyYearlyReportPrintSet.setParam26(printSettings.getParam26());
                // パラメータ27
                monthlyYearlyReportPrintSet.setParam27(printSettings.getParam27());
                // パラメータ28
                monthlyYearlyReportPrintSet.setParam28(printSettings.getParam28());
                // パラメータ29
                monthlyYearlyReportPrintSet.setParam29(printSettings.getParam29());
                // パラメータ30
                monthlyYearlyReportPrintSet.setParam30(printSettings.getParam30());
                // パラメータ31
                monthlyYearlyReportPrintSet.setParam31(printSettings.getParam31());
                // パラメータ32
                monthlyYearlyReportPrintSet.setParam32(printSettings.getParam32());
                // パラメータ33
                monthlyYearlyReportPrintSet.setParam33(printSettings.getParam33());
                // パラメータ34
                monthlyYearlyReportPrintSet.setParam34(printSettings.getParam34());
                // パラメータ35
                monthlyYearlyReportPrintSet.setParam35(printSettings.getParam35());
                // パラメータ36
                monthlyYearlyReportPrintSet.setParam36(printSettings.getParam36());
                // パラメータ37
                monthlyYearlyReportPrintSet.setParam37(printSettings.getParam37());
                // パラメータ38
                monthlyYearlyReportPrintSet.setParam38(printSettings.getParam38());
                // パラメータ39
                monthlyYearlyReportPrintSet.setParam39(printSettings.getParam39());
                // パラメータ40
                monthlyYearlyReportPrintSet.setParam40(printSettings.getParam40());
                // パラメータ41
                monthlyYearlyReportPrintSet.setParam41(printSettings.getParam41());
                // パラメータ42
                monthlyYearlyReportPrintSet.setParam42(printSettings.getParam42());
                // パラメータ43
                monthlyYearlyReportPrintSet.setParam43(printSettings.getParam43());
                // パラメータ44
                monthlyYearlyReportPrintSet.setParam44(printSettings.getParam44());
                // パラメータ45
                monthlyYearlyReportPrintSet.setParam45(printSettings.getParam45());
                // パラメータ46
                monthlyYearlyReportPrintSet.setParam46(printSettings.getParam46());
                // パラメータ47
                monthlyYearlyReportPrintSet.setParam47(printSettings.getParam47());
                // パラメータ48
                monthlyYearlyReportPrintSet.setParam48(printSettings.getParam48());
                // パラメータ49
                monthlyYearlyReportPrintSet.setParam49(printSettings.getParam49());
                // パラメータ50
                monthlyYearlyReportPrintSet.setParam50(printSettings.getParam50());
                monthlyYearlyReportParameterModel.setPrintSet(monthlyYearlyReportPrintSet);
                // DB未保存画面項目
                MonthlyYearlyReportServiceDbNoSaveData monthlyYearlyReportDbNoSaveData = new MonthlyYearlyReportServiceDbNoSaveData();
                // 指定日
                monthlyYearlyReportDbNoSaveData.setEmptyFlg(inDto.getEmptyFlg());
                // 記入用シートを印刷するフラグ
                monthlyYearlyReportDbNoSaveData.setSelectDate(inDto.getSelectDate());
                monthlyYearlyReportParameterModel.setDbNoSaveData(monthlyYearlyReportDbNoSaveData);
                // 印刷対象履歴
                MonthlyYearlyReportServicePrintSubjectHistory monthlyYearlyReportPrintSubjectHistory = new MonthlyYearlyReportServicePrintSubjectHistory();
                // 利用者ID
                monthlyYearlyReportPrintSubjectHistory.setUserId(inDto.getUserId());
                // 履歴ID
                monthlyYearlyReportPrintSubjectHistory.setRirekiId(inDto.getRirekiId());
                // 作成日
                monthlyYearlyReportPrintSubjectHistory.setCreateYmd(inDto.getCreateYmd());
                monthlyYearlyReportParameterModel.setPrintSubjectHistory(monthlyYearlyReportPrintSubjectHistory);
                break;
            // 機能名が"[mnu3][3GK]計画表"の場合
            case CommonConstants.KINOU_NAME_PLAN_TABLE:
                // リクエストパラメータ.初期設定マスタの情報.計画表様式が"0:A3"の場合、"preventionPlanA31Report"
                if (CommonConstants.VAL_ITAKU_KKAK_PRT_FLG.equals(inDto.getInitMasterObj().getItakuKkakPrtFlg())) {
                    prtId = "preventionPlanA31Report";
                    PreventionPlanA31ReportParameterModel planA31ReportParameterModel = new PreventionPlanA31ReportParameterModel();
                    // 帳票サービスInDtoの設定
                    setReportParameterModel(inDto, printSettings, planA31ReportParameterModel);
                    // 事業者名
                    planA31ReportParameterModel.setJigyoKnj(inDto.getSvJigyoKnj());
                    // システム日付
                    planA31ReportParameterModel.setAppYmd(inDto.getSysYmd());
                    // 初期設定マスタの情報
                    PreventionPlanA31ReportServiceInitMasterObj planA31InitMasterObj = new PreventionPlanA31ReportServiceInitMasterObj();
                    // 敬称フラグ
                    planA31InitMasterObj.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                    // 敬称
                    planA31InitMasterObj.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
                    // 印刷フォント
                    planA31InitMasterObj.setPrintFont(inDto.getInitMasterObj().getPrintFont());
                    // 期間の管理フラグ
                    planA31InitMasterObj.setKikanFlg(inDto.getInitMasterObj().getKikanFlg());
                    // 期間のカレンダー取込フラグ
                    planA31InitMasterObj.setKikanYmdFlg(inDto.getInitMasterObj().getKikanYmdFlg());
                    planA31ReportParameterModel.setInitMasterObj(planA31InitMasterObj);
                    // 印刷設定
                    PrintReportServicePrintSet planA31PrintSet = new PrintReportServicePrintSet();
                    // 帳票タイトル
                    planA31PrintSet.setPrtTitle(printSettings.getPrtTitle());
                    // セクション番号
                    planA31PrintSet.setSectionNo(printSettings.getSectionNo());
                    // 帳票No
                    planA31PrintSet.setPrtNo(printSettings.getPrtNo());
                    // プロファイル
                    planA31PrintSet.setProfile(printSettings.getProfile());
                    // 日付表示有無
                    planA31PrintSet.setPrnDate(printSettings.getPrnDate());
                    // 職員表示有無
                    planA31PrintSet.setPrnshoku(printSettings.getPrnshoku());
                    // パラメータ01
                    planA31PrintSet.setParam01(printSettings.getParam01());
                    // パラメータ02
                    planA31PrintSet.setParam02(printSettings.getParam02());
                    // パラメータ03
                    planA31PrintSet.setParam03(printSettings.getParam03());
                    // パラメータ04
                    planA31PrintSet.setParam04(printSettings.getParam04());
                    // パラメータ05
                    planA31PrintSet.setParam05(printSettings.getParam05());
                    // パラメータ06
                    planA31PrintSet.setParam06(printSettings.getParam06());
                    // パラメータ07
                    planA31PrintSet.setParam07(printSettings.getParam07());
                    // パラメータ08
                    planA31PrintSet.setParam08(printSettings.getParam08());
                    // パラメータ09
                    planA31PrintSet.setParam09(printSettings.getParam09());
                    // パラメータ10
                    planA31PrintSet.setParam10(printSettings.getParam10());
                    // パラメータ11
                    planA31PrintSet.setParam11(printSettings.getParam11());
                    // パラメータ12
                    planA31PrintSet.setParam12(printSettings.getParam12());
                    // パラメータ13
                    planA31PrintSet.setParam13(printSettings.getParam13());
                    // パラメータ14
                    planA31PrintSet.setParam14(printSettings.getParam14());
                    // パラメータ15
                    planA31PrintSet.setParam15(printSettings.getParam15());
                    // パラメータ16
                    planA31PrintSet.setParam16(printSettings.getParam16());
                    // パラメータ17
                    planA31PrintSet.setParam17(printSettings.getParam17());
                    // パラメータ18
                    planA31PrintSet.setParam18(printSettings.getParam18());
                    // パラメータ19
                    planA31PrintSet.setParam19(printSettings.getParam19());
                    // パラメータ20
                    planA31PrintSet.setParam20(printSettings.getParam20());
                    // パラメータ21
                    planA31PrintSet.setParam21(printSettings.getParam21());
                    // パラメータ22
                    planA31PrintSet.setParam22(printSettings.getParam22());
                    // パラメータ23
                    planA31PrintSet.setParam23(printSettings.getParam23());
                    // パラメータ24
                    planA31PrintSet.setParam24(printSettings.getParam24());
                    // パラメータ25
                    planA31PrintSet.setParam25(printSettings.getParam25());
                    // パラメータ26
                    planA31PrintSet.setParam26(printSettings.getParam26());
                    // パラメータ27
                    planA31PrintSet.setParam27(printSettings.getParam27());
                    // パラメータ28
                    planA31PrintSet.setParam28(printSettings.getParam28());
                    // パラメータ29
                    planA31PrintSet.setParam29(printSettings.getParam29());
                    // パラメータ30
                    planA31PrintSet.setParam30(printSettings.getParam30());
                    // パラメータ31
                    planA31PrintSet.setParam31(printSettings.getParam31());
                    // パラメータ32
                    planA31PrintSet.setParam32(printSettings.getParam32());
                    // パラメータ33
                    planA31PrintSet.setParam33(printSettings.getParam33());
                    // パラメータ34
                    planA31PrintSet.setParam34(printSettings.getParam34());
                    // パラメータ35
                    planA31PrintSet.setParam35(printSettings.getParam35());
                    // パラメータ36
                    planA31PrintSet.setParam36(printSettings.getParam36());
                    // パラメータ37
                    planA31PrintSet.setParam37(printSettings.getParam37());
                    // パラメータ38
                    planA31PrintSet.setParam38(printSettings.getParam38());
                    // パラメータ39
                    planA31PrintSet.setParam39(printSettings.getParam39());
                    // パラメータ40
                    planA31PrintSet.setParam40(printSettings.getParam40());
                    // パラメータ41
                    planA31PrintSet.setParam41(printSettings.getParam41());
                    // パラメータ42
                    planA31PrintSet.setParam42(printSettings.getParam42());
                    // パラメータ43
                    planA31PrintSet.setParam43(printSettings.getParam43());
                    // パラメータ44
                    planA31PrintSet.setParam44(printSettings.getParam44());
                    // パラメータ45
                    planA31PrintSet.setParam45(printSettings.getParam45());
                    // パラメータ46
                    planA31PrintSet.setParam46(printSettings.getParam46());
                    // パラメータ47
                    planA31PrintSet.setParam47(printSettings.getParam47());
                    // パラメータ48
                    planA31PrintSet.setParam48(printSettings.getParam48());
                    // パラメータ49
                    planA31PrintSet.setParam49(printSettings.getParam49());
                    // パラメータ50
                    planA31PrintSet.setParam50(printSettings.getParam50());
                    planA31ReportParameterModel.setPrintSet(planA31PrintSet);
                    // DB未保存画面項目
                    PreventionPlanA31ReportServiceDbNoSaveData planA31DbNoSaveData = new PreventionPlanA31ReportServiceDbNoSaveData();
                    // 指定日
                    planA31DbNoSaveData.setEmptyFlg(inDto.getEmptyFlg());
                    // 記入用シートを印刷するフラグ
                    planA31DbNoSaveData.setSelectDate(inDto.getSelectDate());
                    // システムコード
                    planA31DbNoSaveData.setSysCode(inDto.getSysCd());
                    // 職員ID
                    planA31DbNoSaveData.setShokuId(inDto.getLoginShokuinId());
                    planA31ReportParameterModel.setDbNoSaveData(planA31DbNoSaveData);
                    // 印刷対象履歴
                    PrintReportServicePrintSubjectHistory planA31PrintSubjectHistory = new PrintReportServicePrintSubjectHistory();
                    // 利用者ID
                    planA31PrintSubjectHistory.setUserId(inDto.getUserId());
                    // 履歴ID
                    planA31PrintSubjectHistory.setRirekiId(inDto.getRirekiId());
                    planA31ReportParameterModel.setPrintSubjectHistory(planA31PrintSubjectHistory);
                    // S3e文書保存処理共通関数を取得する
                    try {
                        cmnStorageServiceUtil.uploadToS3EDocument(
                                prtId,
                                planA31ReportParameterModel, inDto.getLoginShokuinId(),
                                eDocumentUpdKbn,
                                inDto.getRirekiId(), inDto.getSc1Id());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    // リクエストパラメータ.初期設定マスタの情報.計画表様式が"1:A4-3枚"の場合、"preventionPlanA43Report"
                } else if (CommonConstants.ITAKU_KKAK_PRT_FLG_1.equals(inDto.getInitMasterObj().getItakuKkakPrtFlg())) {
                    prtId = "preventionPlanA43Report";
                    PreventionPlanA43ReportParameterModel planA43ReportParameterModel = new PreventionPlanA43ReportParameterModel();
                    // 帳票サービスInDtoの設定
                    setReportParameterModel(inDto, printSettings, planA43ReportParameterModel);
                    // 事業者名
                    planA43ReportParameterModel.setJigyoKnj(inDto.getSvJigyoKnj());
                    // システム日付
                    planA43ReportParameterModel.setAppYmd(inDto.getSysYmd());

                    // 初期設定マスタの情報
                    PreventionPlanA31ReportServiceInitMasterObj planA43InitMasterObj = new PreventionPlanA31ReportServiceInitMasterObj();
                    // 敬称フラグ
                    planA43InitMasterObj.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                    // 敬称
                    planA43InitMasterObj.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
                    // 印刷フォント
                    planA43InitMasterObj.setPrintFont(inDto.getInitMasterObj().getPrintFont());
                    // 期間の管理フラグ
                    planA43InitMasterObj.setKikanFlg(inDto.getInitMasterObj().getKikanFlg());
                    // 期間のカレンダー取込フラグ
                    planA43InitMasterObj.setKikanYmdFlg(inDto.getInitMasterObj().getKikanFlg());
                    planA43ReportParameterModel.setInitMasterObj(planA43InitMasterObj);
                    // 印刷設定
                    PrintReportServicePrintSet planA43PrintSet = new PrintReportServicePrintSet();
                    // 帳票タイトル
                    planA43PrintSet.setPrtTitle(printSettings.getPrtTitle());
                    // セクション番号
                    planA43PrintSet.setSectionNo(printSettings.getSectionNo());
                    // 帳票No
                    planA43PrintSet.setPrtNo(printSettings.getPrtNo());
                    // プロファイル
                    planA43PrintSet.setProfile(printSettings.getProfile());
                    // 日付表示有無
                    planA43PrintSet.setPrnDate(printSettings.getPrnDate());
                    // 職員表示有無
                    planA43PrintSet.setPrnshoku(printSettings.getPrnshoku());
                    // パラメータ01
                    planA43PrintSet.setParam01(printSettings.getParam01());
                    // パラメータ02
                    planA43PrintSet.setParam02(printSettings.getParam02());
                    // パラメータ03
                    planA43PrintSet.setParam03(printSettings.getParam03());
                    // パラメータ04
                    planA43PrintSet.setParam04(printSettings.getParam04());
                    // パラメータ05
                    planA43PrintSet.setParam05(printSettings.getParam05());
                    // パラメータ06
                    planA43PrintSet.setParam06(printSettings.getParam06());
                    // パラメータ07
                    planA43PrintSet.setParam07(printSettings.getParam07());
                    // パラメータ08
                    planA43PrintSet.setParam08(printSettings.getParam08());
                    // パラメータ09
                    planA43PrintSet.setParam09(printSettings.getParam09());
                    // パラメータ10
                    planA43PrintSet.setParam10(printSettings.getParam10());
                    // パラメータ11
                    planA43PrintSet.setParam11(printSettings.getParam11());
                    // パラメータ12
                    planA43PrintSet.setParam12(printSettings.getParam12());
                    // パラメータ13
                    planA43PrintSet.setParam13(printSettings.getParam13());
                    // パラメータ14
                    planA43PrintSet.setParam14(printSettings.getParam14());
                    // パラメータ15
                    planA43PrintSet.setParam15(printSettings.getParam15());
                    // パラメータ16
                    planA43PrintSet.setParam16(printSettings.getParam16());
                    // パラメータ17
                    planA43PrintSet.setParam17(printSettings.getParam17());
                    // パラメータ18
                    planA43PrintSet.setParam18(printSettings.getParam18());
                    // パラメータ19
                    planA43PrintSet.setParam19(printSettings.getParam19());
                    // パラメータ20
                    planA43PrintSet.setParam20(printSettings.getParam20());
                    // パラメータ21
                    planA43PrintSet.setParam21(printSettings.getParam21());
                    // パラメータ22
                    planA43PrintSet.setParam22(printSettings.getParam22());
                    // パラメータ23
                    planA43PrintSet.setParam23(printSettings.getParam23());
                    // パラメータ24
                    planA43PrintSet.setParam24(printSettings.getParam24());
                    // パラメータ25
                    planA43PrintSet.setParam25(printSettings.getParam25());
                    // パラメータ26
                    planA43PrintSet.setParam26(printSettings.getParam26());
                    // パラメータ27
                    planA43PrintSet.setParam27(printSettings.getParam27());
                    // パラメータ28
                    planA43PrintSet.setParam28(printSettings.getParam28());
                    // パラメータ29
                    planA43PrintSet.setParam29(printSettings.getParam29());
                    // パラメータ30
                    planA43PrintSet.setParam30(printSettings.getParam30());
                    // パラメータ31
                    planA43PrintSet.setParam31(printSettings.getParam31());
                    // パラメータ32
                    planA43PrintSet.setParam32(printSettings.getParam32());
                    // パラメータ33
                    planA43PrintSet.setParam33(printSettings.getParam33());
                    // パラメータ34
                    planA43PrintSet.setParam34(printSettings.getParam34());
                    // パラメータ35
                    planA43PrintSet.setParam35(printSettings.getParam35());
                    // パラメータ36
                    planA43PrintSet.setParam36(printSettings.getParam36());
                    // パラメータ37
                    planA43PrintSet.setParam37(printSettings.getParam37());
                    // パラメータ38
                    planA43PrintSet.setParam38(printSettings.getParam38());
                    // パラメータ39
                    planA43PrintSet.setParam39(printSettings.getParam39());
                    // パラメータ40
                    planA43PrintSet.setParam40(printSettings.getParam40());
                    // パラメータ41
                    planA43PrintSet.setParam41(printSettings.getParam41());
                    // パラメータ42
                    planA43PrintSet.setParam42(printSettings.getParam42());
                    // パラメータ43
                    planA43PrintSet.setParam43(printSettings.getParam43());
                    // パラメータ44
                    planA43PrintSet.setParam44(printSettings.getParam44());
                    // パラメータ45
                    planA43PrintSet.setParam45(printSettings.getParam45());
                    // パラメータ46
                    planA43PrintSet.setParam46(printSettings.getParam46());
                    // パラメータ47
                    planA43PrintSet.setParam47(printSettings.getParam47());
                    // パラメータ48
                    planA43PrintSet.setParam48(printSettings.getParam48());
                    // パラメータ49
                    planA43PrintSet.setParam49(printSettings.getParam49());
                    // パラメータ50
                    planA43PrintSet.setParam50(printSettings.getParam50());
                    planA43ReportParameterModel.setPrintSet(planA43PrintSet);
                    // DB未保存画面項目
                    PreventionPlanA31ReportServiceDbNoSaveData planA43DbNoSaveData = new PreventionPlanA31ReportServiceDbNoSaveData();
                    // 指定日
                    planA43DbNoSaveData.setEmptyFlg(inDto.getEmptyFlg());
                    // 記入用シートを印刷するフラグ
                    planA43DbNoSaveData.setSelectDate(inDto.getSelectDate());
                    // システムコード
                    planA43DbNoSaveData.setSysCode(inDto.getSysCd());
                    // 職員ID
                    planA43DbNoSaveData.setShokuId(inDto.getLoginShokuinId());
                    planA43ReportParameterModel.setDbNoSaveData(planA43DbNoSaveData);
                    // 印刷対象履歴
                    PrintReportServicePrintSubjectHistory planA43PrintSubjectHistory = new PrintReportServicePrintSubjectHistory();
                    // 利用者ID
                    planA43PrintSubjectHistory.setUserId(inDto.getUserId());
                    // 履歴ID
                    planA43PrintSubjectHistory.setRirekiId(inDto.getRirekiId());
                    planA43ReportParameterModel.setPrintSubjectHistory(planA43PrintSubjectHistory);
                    // S3e文書保存処理共通関数を取得する
                    try {
                        cmnStorageServiceUtil.uploadToS3EDocument(
                                prtId,
                                planA43ReportParameterModel, inDto.getLoginShokuinId(),
                                eDocumentUpdKbn,
                                inDto.getRirekiId(), inDto.getSc1Id());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    // リクエストパラメータ.初期設定マスタの情報.計画表様式が"2:A4-2枚"の場合、"preventionPlanA42Report"
                } else if (CommonConstants.ITAKU_KKAK_PRT_FLG_2.equals(inDto.getInitMasterObj().getItakuKkakPrtFlg())) {
                    prtId = "preventionPlanA42Report";
                    PreventionPlanA42ReportServiceParameterModel planA42ReportParameterModel = new PreventionPlanA42ReportServiceParameterModel();
                    // 帳票サービスInDtoの設定
                    setReportParameterModel(inDto, printSettings, planA42ReportParameterModel);
                    // 事業者名
                    planA42ReportParameterModel.setJigyoKnj(inDto.getSvJigyoKnj());
                    // システム日付
                    planA42ReportParameterModel.setAppYmd(inDto.getSysYmd());

                    // 初期設定マスタの情報
                    PreventionPlanA31ReportServiceInitMasterObj planA42InitMasterObj = new PreventionPlanA31ReportServiceInitMasterObj();
                    // 敬称フラグ
                    planA42InitMasterObj.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                    // 敬称
                    planA42InitMasterObj.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
                    // 印刷フォント
                    planA42InitMasterObj.setPrintFont(inDto.getInitMasterObj().getPrintFont());
                    // 期間の管理フラグ
                    planA42InitMasterObj.setKikanFlg(inDto.getInitMasterObj().getKikanFlg());
                    // 期間のカレンダー取込フラグ
                    planA42InitMasterObj.setKikanYmdFlg(inDto.getInitMasterObj().getKikanYmdFlg());
                    planA42ReportParameterModel.setInitMasterObj(planA42InitMasterObj);
                    // 印刷設定
                    PrintReportServicePrintSet planA42PrintSet = new PrintReportServicePrintSet();
                    // 帳票タイトル
                    planA42PrintSet.setPrtTitle(printSettings.getPrtTitle());
                    // セクション番号
                    planA42PrintSet.setSectionNo(printSettings.getSectionNo());
                    // 帳票No
                    planA42PrintSet.setPrtNo(printSettings.getPrtNo());
                    // プロファイル
                    planA42PrintSet.setProfile(printSettings.getProfile());
                    // 日付表示有無
                    planA42PrintSet.setPrnDate(printSettings.getPrnDate());
                    // 職員表示有無
                    planA42PrintSet.setPrnshoku(printSettings.getPrnshoku());
                    // パラメータ01
                    planA42PrintSet.setParam01(printSettings.getParam01());
                    // パラメータ02
                    planA42PrintSet.setParam02(printSettings.getParam02());
                    // パラメータ03
                    planA42PrintSet.setParam03(printSettings.getParam03());
                    // パラメータ04
                    planA42PrintSet.setParam04(printSettings.getParam04());
                    // パラメータ05
                    planA42PrintSet.setParam05(printSettings.getParam05());
                    // パラメータ06
                    planA42PrintSet.setParam06(printSettings.getParam06());
                    // パラメータ07
                    planA42PrintSet.setParam07(printSettings.getParam07());
                    // パラメータ08
                    planA42PrintSet.setParam08(printSettings.getParam08());
                    // パラメータ09
                    planA42PrintSet.setParam09(printSettings.getParam09());
                    // パラメータ10
                    planA42PrintSet.setParam10(printSettings.getParam10());
                    // パラメータ11
                    planA42PrintSet.setParam11(printSettings.getParam11());
                    // パラメータ12
                    planA42PrintSet.setParam12(printSettings.getParam12());
                    // パラメータ13
                    planA42PrintSet.setParam13(printSettings.getParam13());
                    // パラメータ14
                    planA42PrintSet.setParam14(printSettings.getParam14());
                    // パラメータ15
                    planA42PrintSet.setParam15(printSettings.getParam15());
                    // パラメータ16
                    planA42PrintSet.setParam16(printSettings.getParam16());
                    // パラメータ17
                    planA42PrintSet.setParam17(printSettings.getParam17());
                    // パラメータ18
                    planA42PrintSet.setParam18(printSettings.getParam18());
                    // パラメータ19
                    planA42PrintSet.setParam19(printSettings.getParam19());
                    // パラメータ20
                    planA42PrintSet.setParam20(printSettings.getParam20());
                    // パラメータ21
                    planA42PrintSet.setParam21(printSettings.getParam21());
                    // パラメータ22
                    planA42PrintSet.setParam22(printSettings.getParam22());
                    // パラメータ23
                    planA42PrintSet.setParam23(printSettings.getParam23());
                    // パラメータ24
                    planA42PrintSet.setParam24(printSettings.getParam24());
                    // パラメータ25
                    planA42PrintSet.setParam25(printSettings.getParam25());
                    // パラメータ26
                    planA42PrintSet.setParam26(printSettings.getParam26());
                    // パラメータ27
                    planA42PrintSet.setParam27(printSettings.getParam27());
                    // パラメータ28
                    planA42PrintSet.setParam28(printSettings.getParam28());
                    // パラメータ29
                    planA42PrintSet.setParam29(printSettings.getParam29());
                    // パラメータ30
                    planA42PrintSet.setParam30(printSettings.getParam30());
                    // パラメータ31
                    planA42PrintSet.setParam31(printSettings.getParam31());
                    // パラメータ32
                    planA42PrintSet.setParam32(printSettings.getParam32());
                    // パラメータ33
                    planA42PrintSet.setParam33(printSettings.getParam33());
                    // パラメータ34
                    planA42PrintSet.setParam34(printSettings.getParam34());
                    // パラメータ35
                    planA42PrintSet.setParam35(printSettings.getParam35());
                    // パラメータ36
                    planA42PrintSet.setParam36(printSettings.getParam36());
                    // パラメータ37
                    planA42PrintSet.setParam37(printSettings.getParam37());
                    // パラメータ38
                    planA42PrintSet.setParam38(printSettings.getParam38());
                    // パラメータ39
                    planA42PrintSet.setParam39(printSettings.getParam39());
                    // パラメータ40
                    planA42PrintSet.setParam40(printSettings.getParam40());
                    // パラメータ41
                    planA42PrintSet.setParam41(printSettings.getParam41());
                    // パラメータ42
                    planA42PrintSet.setParam42(printSettings.getParam42());
                    // パラメータ43
                    planA42PrintSet.setParam43(printSettings.getParam43());
                    // パラメータ44
                    planA42PrintSet.setParam44(printSettings.getParam44());
                    // パラメータ45
                    planA42PrintSet.setParam45(printSettings.getParam45());
                    // パラメータ46
                    planA42PrintSet.setParam46(printSettings.getParam46());
                    // パラメータ47
                    planA42PrintSet.setParam47(printSettings.getParam47());
                    // パラメータ48
                    planA42PrintSet.setParam48(printSettings.getParam48());
                    // パラメータ49
                    planA42PrintSet.setParam49(printSettings.getParam49());
                    // パラメータ50
                    planA42PrintSet.setParam50(printSettings.getParam50());
                    planA42ReportParameterModel.setPrintSet(planA42PrintSet);
                    // DB未保存画面項目
                    PreventionPlanA31ReportServiceDbNoSaveData planA42DbNoSaveData = new PreventionPlanA31ReportServiceDbNoSaveData();
                    // 指定日
                    planA42DbNoSaveData.setEmptyFlg(inDto.getEmptyFlg());
                    // 記入用シートを印刷するフラグ
                    planA42DbNoSaveData.setSelectDate(inDto.getSelectDate());
                    // システムコード
                    planA42DbNoSaveData.setSysCode(inDto.getSysCd());
                    // 職員ID
                    planA42DbNoSaveData.setShokuId(inDto.getLoginShokuinId());
                    planA42ReportParameterModel.setDbNoSaveData(planA42DbNoSaveData);
                    // 印刷対象履歴
                    PrintReportServicePrintSubjectHistory planA42PrintSubjectHistory = new PrintReportServicePrintSubjectHistory();
                    // 利用者ID
                    planA42PrintSubjectHistory.setUserId(inDto.getUserId());
                    // 履歴ID
                    planA42PrintSubjectHistory.setRirekiId(inDto.getRirekiId());
                    planA42ReportParameterModel.setPrintSubjectHistory(planA42PrintSubjectHistory);
                    // S3e文書保存処理共通関数を取得する
                    try {
                        cmnStorageServiceUtil.uploadToS3EDocument(
                                prtId,
                                planA42ReportParameterModel, inDto.getLoginShokuinId(),
                                eDocumentUpdKbn,
                                inDto.getRirekiId(), inDto.getSc1Id());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 帳票サービスInDtoの設定
     * 
     * @param inDto         リクエストパラメータ
     * @param printSettings 印刷設定情報Dto
     * @param outDto        帳票サービスInDto
     */
    private <T extends FixedReportInDto> void setReportParameterModel(GyoumuComElectronicSaveInDto inDto,
            GyoumuComPrintSettingsDto printSettings, T outDto) {
        // 帳票ID
        outDto.setReportId(printSettings.getPrtId());
        // 契約者ID
        outDto.setKeiyakushaId(AppUtil.getKeiyakushaId());
        // 事業者情報
        Jigyosyo jigyosyo = new Jigyosyo();
        // 法人ID
        jigyosyo.setHoujinId(inDto.getHoujinId());
        // 施設ID
        jigyosyo.setShisetuId(inDto.getShisetuId());
        // サービス事業者ID
        jigyosyo.setSvJigyoId(inDto.getSvJigyoId());
        // サービス事業者コード
        jigyosyo.setSvJigyoCd(inDto.getSvJigyoCd());
        outDto.setJigyoInfo(jigyosyo);
        // システム日付
        outDto.setSystemDate(inDto.getSysYmd());
    }

    /**
     * 業務共通化-帳票サービスInDtoの設定
     * 
     * @param inDto    リクエストパラメータ
     * @param menu3Knj 機能名
     * @return 帳票サービスInDto
     */
    private GyoumuComPrintSettingsDto getReportServiceInDto(GyoumuComElectronicSaveInDto inDto, String menu3Knj) {
        // 印刷設定情報のINPUT情報の準備
        GyoumuComPrintSettingsInputDto settingsInput = new GyoumuComPrintSettingsInputDto();
        // セクション番号
        String sectionNo = "";
        switch (menu3Knj) {
            // 機能名が"[mnu3][3GK]実施計画～①"の場合
            case CommonConstants.KINOU_NAME_IMPLEMENTATION_PLAN_1_TAB:
                sectionNo = "U0P150";
                break;
            // 機能名が"[mnu3][3GK]実施計画～②"の場合
            case CommonConstants.KINOU_NAME_IMPLEMENTATION_PLAN_2_TAB:
                sectionNo = "U0P151";
                break;
            // 機能名が"[mnu3][3GK]実施計画～③"の場合
            case CommonConstants.KINOU_NAME_IMPLEMENTATION_PLAN_3_TAB:
                sectionNo = "U0P152";
                break;
            // 機能名が"[mnu3][3GK]計画書(1)"の場合
            case CommonConstants.KINOU_NAME_CARE_PLAN_1:
                // リクエストパラメータ.初期設定マスタの情報.計画書様式が"1:施設"の場合、"U0081S"
                if (CommonConstants.CKS_FLG_FACILITY.equals(inDto.getInitMasterObj().getItakuKkakPrtFlg())) {
                    sectionNo = "U0081S";
                    // リクエストパラメータ.初期設定マスタの情報.計画書様式が"2:居宅"の場合、"U0081K"
                } else if (CommonConstants.CKS_FLG_HOME.equals(inDto.getInitMasterObj().getItakuKkakPrtFlg())) {
                    sectionNo = "U0081K";
                }
                break;
            // 機能名が"[mnu3][3GK]日課計画"の場合
            case CommonConstants.KINOU_NAME_DAY_PLAN:
                sectionNo = "U00861";
                break;
            // 機能名が"[mnu3][3GK]日課表"の場合
            case CommonConstants.KINOU_NAME_DAILY_SCHEDULE:
                sectionNo = "U0P145";
                break;
            // 機能名が"[mnu3][3GK]月間・年間表"の場合
            case CommonConstants.KINOU_NAME_MONTHLY_YEARLY_CALENDAR:
                sectionNo = "U0P157";
                break;
            // 機能名が"[mnu3][3GK]計画表"の場合
            case CommonConstants.KINOU_NAME_PLAN_TABLE:
                // リクエストパラメータ.初期設定マスタの情報.計画表様式が"0:A3"の場合、"E00400"
                if (CommonConstants.VAL_ITAKU_KKAK_PRT_FLG.equals(inDto.getInitMasterObj().getItakuKkakPrtFlg())) {
                    sectionNo = "E00400";
                    // リクエストパラメータ.初期設定マスタの情報.計画表様式が"1:A4-3枚"の場合、"E00401"
                } else if (CommonConstants.ITAKU_KKAK_PRT_FLG_1.equals(inDto.getInitMasterObj().getItakuKkakPrtFlg())) {
                    sectionNo = "E00401";
                    // リクエストパラメータ.初期設定マスタの情報.計画表様式が"2:A4-2枚"の場合、"E00402"
                } else if (CommonConstants.ITAKU_KKAK_PRT_FLG_2.equals(inDto.getInitMasterObj().getItakuKkakPrtFlg())) {
                    sectionNo = "E00402";
                }
                break;
            default:
                break;
        }
        settingsInput.setSectionNo(sectionNo);
        // 帳票番号
        settingsInput.setPrtNo(PRTNO_STR_1);
        // 職員ID
        settingsInput.setShokuId(inDto.getLoginShokuinId());
        // 法人ID
        settingsInput.setHoujinId(inDto.getHoujinId());
        // 施設ID
        settingsInput.setShisetuId(inDto.getShisetuId());
        // 事業所ID
        settingsInput.setSvJigyoId(inDto.getSvJigyoId());
        // インデックス
        settingsInput.setIndex(INDEX_STR_1);
        GyoumuComPrintSettingsDto outDto = getPrtInfo(settingsInput);
        return outDto;
    }

    /**
     * GyoumuComPrintSettingsDtoからコピー
     * 
     * @param printData
     * @return
     */
    public ComPrintSettingsDto copyToComPrintSettingsDto(GyoumuComPrintSettingsDto printData) {
        ComPrintSettingsDto newDto = new ComPrintSettingsDto();
        if (printData == null) {
            return newDto;
        }
        // インデックス
        newDto.setIndex(printData.getIndex());
        // 帳票ID
        newDto.setPrtId(printData.getPrtId());
        // プロファイル
        newDto.setProfile(printData.getProfile());
        // 帳票名
        newDto.setDefPrtTitle(printData.getDefPrtTitle());
        // 帳票タイトル
        newDto.setPrtTitle(printData.getPrtTitle());
        // セクション番号
        newDto.setSectionNo(printData.getSectionNo());
        // 帳票番号
        newDto.setPrtNo(printData.getPrtNo());
        // 日付表示有無
        newDto.setPrnDate(printData.getPrnDate());
        // 職員表示有無
        newDto.setPrnshoku(printData.getPrnshoku());
        // パラメータ01
        newDto.setParam01(printData.getParam01());
        // パラメータ02
        newDto.setParam02(printData.getParam02());
        // パラメータ03
        newDto.setParam03(printData.getParam03());
        // パラメータ04
        newDto.setParam04(printData.getParam04());
        // パラメータ05
        newDto.setParam05(printData.getParam05());
        // パラメータ06
        newDto.setParam06(printData.getParam06());
        // パラメータ07
        newDto.setParam07(printData.getParam07());
        // パラメータ08
        newDto.setParam08(printData.getParam08());
        // パラメータ09
        newDto.setParam09(printData.getParam09());
        // パラメータ10
        newDto.setParam10(printData.getParam10());
        // パラメータ11
        newDto.setParam11(printData.getParam11());
        // パラメータ12
        newDto.setParam12(printData.getParam12());
        // パラメータ13
        newDto.setParam13(printData.getParam13());
        // パラメータ14
        newDto.setParam14(printData.getParam14());
        // パラメータ15
        newDto.setParam15(printData.getParam15());
        // パラメータ16
        newDto.setParam16(printData.getParam16());
        // パラメータ17
        newDto.setParam17(printData.getParam17());
        // パラメータ18
        newDto.setParam18(printData.getParam18());
        // パラメータ19
        newDto.setParam19(printData.getParam19());
        // パラメータ20
        newDto.setParam20(printData.getParam20());
        // パラメータ21
        newDto.setParam21(printData.getParam21());
        // パラメータ22
        newDto.setParam22(printData.getParam22());
        // パラメータ23
        newDto.setParam23(printData.getParam23());
        // パラメータ24
        newDto.setParam24(printData.getParam24());
        // パラメータ25
        newDto.setParam25(printData.getParam25());
        // パラメータ26
        newDto.setParam26(printData.getParam26());
        // パラメータ27
        newDto.setParam27(printData.getParam27());
        // パラメータ28
        newDto.setParam28(printData.getParam28());
        // パラメータ29
        newDto.setParam29(printData.getParam29());
        // パラメータ30
        newDto.setParam30(printData.getParam30());
        // パラメータ31
        newDto.setParam31(printData.getParam31());
        // パラメータ32
        newDto.setParam32(printData.getParam32());
        // パラメータ33
        newDto.setParam33(printData.getParam33());
        // パラメータ34
        newDto.setParam34(printData.getParam34());
        // パラメータ35
        newDto.setParam35(printData.getParam35());
        // パラメータ36
        newDto.setParam36(printData.getParam36());
        // パラメータ37
        newDto.setParam37(printData.getParam37());
        // パラメータ38
        newDto.setParam38(printData.getParam38());
        // パラメータ39
        newDto.setParam39(printData.getParam39());
        // パラメータ40
        newDto.setParam40(printData.getParam40());
        // パラメータ41
        newDto.setParam41(printData.getParam41());
        // パラメータ42
        newDto.setParam42(printData.getParam42());
        // パラメータ43
        newDto.setParam43(printData.getParam43());
        // パラメータ44
        newDto.setParam44(printData.getParam44());
        // パラメータ45
        newDto.setParam45(printData.getParam45());
        // パラメータ46
        newDto.setParam46(printData.getParam46());
        // パラメータ47
        newDto.setParam47(printData.getParam47());
        // パラメータ48
        newDto.setParam48(printData.getParam48());
        // パラメータ49
        newDto.setParam49(printData.getParam49());
        // パラメータ50
        newDto.setParam50(printData.getParam50());
        // 更新回数
        newDto.setModifiedCnt(printData.getModifiedCnt());
        return newDto;
    }
}
