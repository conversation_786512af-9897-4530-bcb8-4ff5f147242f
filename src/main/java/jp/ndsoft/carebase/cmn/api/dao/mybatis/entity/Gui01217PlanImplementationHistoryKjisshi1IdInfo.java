package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.carebase.framework.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GUI01217_計画実施‐実績登録の画面
 * 
 * @description
 *              履歴情報取得
 *              「計画実施‐実績登録」画面用履歴情報のエンティティ
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Gui01217PlanImplementationHistoryKjisshi1IdInfo implements Serializable, IEntity {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** ヘッダID */
    @NotEmpty
    private String kjisshi1Id;

}
