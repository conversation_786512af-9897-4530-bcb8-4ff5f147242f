package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01044Cks52;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01044Cks54;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01044Cks55;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01044Cks56;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01044Cks57;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01044Cks58;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnWeekLogic;
import jp.ndsoft.carebase.cmn.api.logic.dto.Cks58InDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.TermId2DateLogicOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekPlanPatternSettingsDetailSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekPlanPatternSettingsDetailSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.Cks57InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Cks57InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekIgaiHindoHanniKakusyuiPtnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekIgaiHindoHanniKakusyuiPtnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekPtn1ranKRByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekPtn1ranKROutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekPtnInputKasanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekPtnInputKasanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekPtnNichijoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekPtnNichijoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekPtnTantoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekPtnTantoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekSvShurui1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekSvShurui1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSvselSelJigyoShubetuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSvselSelJigyoShubetuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoNameSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucKs52SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucKs54SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucKs55SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucKs56SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucKs58SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks57SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KyotsuMasutaSabisuJigyoMeiSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * GUI01044_週間計画パターン（設定）明細を取得サービス.
 *
 * <AUTHOR>
 */
@Service
public class WeekPlanPatternSettingsDetailSelectServiceImpl extends
        SelectServiceImpl<WeekPlanPatternSettingsDetailSelectServiceInDto, WeekPlanPatternSettingsDetailSelectServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /**  */
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    /** 週間計画ﾊﾟﾀｰﾝ（日常生活データ）情報 */
    @Autowired
    private CpnMucKs54SelectMapper cpnMucKs54SelectMapper;

    /** 週間計画ﾊﾟﾀｰﾝ（詳細データ）情報 */
    @Autowired
    private ComMscSvjigyoNameSelectMapper comMscSvjigyoNameSelectMapper;

    /** 詳細データの抽出 */
    @Autowired
    private CpnMucKs52SelectMapper cpnMucKs52SelectMapper;

    /** 週間計画ﾊﾟﾀｰﾝ（加算データ）情報 */
    @Autowired
    private CpnMucKs55SelectMapper cpnMucKs55SelectMapper;

    /** 週間計画ﾊﾟﾀｰﾝ（担当者）情報 */
    @Autowired
    private CpnMucKs56SelectMapper cpnMucKs56SelectMapper;

    /** 週間計画ﾊﾟﾀｰﾝ（隔週）情報 */
    @Autowired
    private CpnMucKs58SelectMapper cpnMucKs58SelectMapper;

    /** 週間計画（月日指定）情報 */
    @Autowired
    private CpnTucCks57SelectMapper cpnTucCks57SelectMapper;

    /** サービス事業名称、略称 */
    @Autowired
    private KyotsuMasutaSabisuJigyoMeiSelectMapper kyotsuMasutaSabisuJigyoMeiSelectMapper;

    /** ｻｰﾋﾞｽ検索の開始、終了をtermidから取得するロジッククラス */
    @Autowired
    private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;

    /** サービス項目の名称をマスタデータから取得する。 */
    @Autowired
    private KghKrkZCpnWeekLogic KghKrkZCpnWeekLogic;

    /**
     * 「週間計画パターン（設定）」画面の明細情報を取得する。
     * 
     * @param inDto 週間計画パターン（設定）明細情報を取得サービス入力Dto
     * @return 週間計画パターン（設定）明細情報を取得の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected WeekPlanPatternSettingsDetailSelectServiceOutDto mainProcess(
            WeekPlanPatternSettingsDetailSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        // 週間計画パターン（設定）明細情報を取得の出力Dto
        WeekPlanPatternSettingsDetailSelectServiceOutDto outDto = new WeekPlanPatternSettingsDetailSelectServiceOutDto();
        // 2.リクエストパラメータ.週間計画ID＝NULLの場合、処理終了する。
        if (inDto.getKs51Id() == null) {
            return outDto;
        }
        // 3.サービス検索日付取得
        TermId2DateLogicOutDto termId2DateLogicOutDto = this.kghKrkZCpnFuncLogic
                .getTermid2date(CommonDtoUtil.strValToInt(inDto.getTermId()), StringUtils.EMPTY, StringUtils.EMPTY);
        // 4.週間計画ﾊﾟﾀｰﾝ詳細情報取得
        weekPlanDetailInfo(inDto, outDto, termId2DateLogicOutDto);
        // 5.週以外、月日、曜日の文字転換
        letterFormat(inDto, outDto);
        if (outDto.getCks52List() == null) {
            outDto.setCks52List(new ArrayList<Gui01044Cks52>());
        }
        if (outDto.getCks54List() == null) {
            outDto.setCks54List(new ArrayList<Gui01044Cks54>());
        }
        for (Gui01044Cks52 cks52 : outDto.getCks52List()) {
            if (cks52.getCks55List() == null) {
                cks52.setCks55List(new ArrayList<Gui01044Cks55>());
            }
            if (cks52.getCks56List() == null) {
                cks52.setCks56List(new ArrayList<Gui01044Cks56>());
            }
            if (cks52.getCks57List() == null) {
                cks52.setCks57List(new ArrayList<Gui01044Cks57>());
            }
            if (cks52.getCks58List() == null) {
                cks52.setCks58List(new ArrayList<Gui01044Cks58>());
            }
        }
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * サービス検索日付取得処理
     * 
     * @param request                週間計画パターン（設定）明細情報を取得サービス入力Dto
     * @param response               週間計画パターン（設定）明細情報を取得サービス出力Dto
     * @param termId2DateLogicOutDto サービス検索日付
     */
    private void weekPlanDetailInfo(WeekPlanPatternSettingsDetailSelectServiceInDto request,
            WeekPlanPatternSettingsDetailSelectServiceOutDto response, TermId2DateLogicOutDto termId2DateLogicOutDto) {
        // 4.週間計画ﾊﾟﾀｰﾝ詳細情報取得
        // 4.1.週間計画ﾊﾟﾀｰﾝ（日常生活データ）情報の検索処理を行う
        CpnWeekPtnNichijoByCriteriaInEntity cks54Criteria = new CpnWeekPtnNichijoByCriteriaInEntity();
        // 週間計画ID リクエストパラメータ.週間計画ID
        cks54Criteria.setKs51Id(CommonDtoUtil.strValToInt(request.getKs51Id()));
        // データ取得処理
        List<CpnWeekPtnNichijoOutEntity> nichijoList = cpnMucKs54SelectMapper
                .findCpnWeekPtnNichijoByCriteria(cks54Criteria);
        // 戻り値のデータがある場合、レスポンスパラメータ.日常リストに戻り値を設定する
        if (!CollectionUtils.isEmpty(nichijoList)) {
            response.setCks54List(nichijoList.stream().map(entity -> {
                // 日常エンティティ
                Gui01044Cks54 data = new Gui01044Cks54();
                // 日常データID
                data.setKs54Id(CommonDtoUtil.objValToString(entity.getKs54Id()));
                // 週間計画ID
                data.setKs51Id(CommonDtoUtil.objValToString(entity.getKs51Id()));
                // 表示順
                data.setSeq(CommonDtoUtil.objValToString(entity.getSeq()));
                // 主な日常生活上の活動
                data.setNichijoKnj(entity.getNichijoKnj());
                return data;
            }).collect(Collectors.toList()));
        }
        // 4.2.週間計画ﾊﾟﾀｰﾝ（詳細データ）情報の検索処理を行う
        // 4.2.1.サービス種類名称
        List<CpnWeekSvShurui1OutEntity> svjigyoNameInfoList = comMscSvjigyoNameSelectMapper
                .findCpnWeekSvShurui1ByCriteria(new CpnWeekSvShurui1ByCriteriaInEntity());
        // ・変数.サービス種類名称リストに戻り値を設定する。
        Map<String, String> svjigyoNameInfoMap = svjigyoNameInfoList.stream()
                .filter(svjigyoNameInfo -> svjigyoNameInfo.getRuakuKnj() != null).collect(Collectors
                        .toMap(CpnWeekSvShurui1OutEntity::getSvKindCd, CpnWeekSvShurui1OutEntity::getRuakuKnj));
        // 4.2.2.サービス事業名称、略称
        List<KghCmnSvselSelJigyoShubetuOutEntity> kghCmnSvselSelJigyoShubetuList = kyotsuMasutaSabisuJigyoMeiSelectMapper
                .findKghCmnSvselSelJigyoShubetuByCriteria(new KghCmnSvselSelJigyoShubetuByCriteriaInEntity());
        // 4.2.2.1.戻り値.★サービス種類コード→4-5が('51' ,'52' ,'53' ,'54' ,'43' ,
        // '46')の場合、戻り値に該当レコードを削除する。
        List<KghCmnSvselSelJigyoShubetuOutEntity> filtered = kghCmnSvselSelJigyoShubetuList.stream()
                .filter(entity -> !containsFilterCd(entity.getSvKindCd())).collect(Collectors.toList());
        // 4.2.2.1.変数.サービス事業名称略称リストに戻り値を設定する。
        // 4.2.3.詳細データの抽出
        CpnWeekPtn1ranKRByCriteriaInEntity cpnWeekPtn1ranKRByCriteria = new CpnWeekPtn1ranKRByCriteriaInEntity();
        // 週間計画ID リクエストパラメータ.週間計画ID
        cpnWeekPtn1ranKRByCriteria.setKs51(CommonDtoUtil.strValToInt(request.getKs51Id()));
        List<CpnWeekPtn1ranKROutEntity> detailList = cpnMucKs52SelectMapper
                .findCpnWeekPtn1ranKRByCriteria(cpnWeekPtn1ranKRByCriteria);
        // 4.2.3.1.戻り値のリストを繰り返し、
        List<Gui01044Cks52> cks52List = new ArrayList<>();
        for (CpnWeekPtn1ranKROutEntity data : detailList) {
            // 詳細データ
            Gui01044Cks52 cks52 = new Gui01044Cks52();
            // dmyKs52Id
            cks52.setDmyKs52Id(CommonDtoUtil.objValToString(data.getDmyKs52Id()));
            // dmySvItem
            cks52.setDmySvItem(data.getDmySvItem());
            // wN1
            cks52.setWN1(CommonDtoUtil.objValToString(data.getWN1()));
            // wN2
            cks52.setWN2(CommonDtoUtil.objValToString(data.getWN2()));
            // wN3
            cks52.setWN3(CommonDtoUtil.objValToString(data.getWN3()));
            // wN4
            cks52.setWN4(CommonDtoUtil.objValToString(data.getWN4()));
            // wN5
            cks52.setWN5(CommonDtoUtil.objValToString(data.getWN5()));
            // wN6
            cks52.setWN6(CommonDtoUtil.objValToString(data.getWN6()));
            // wN7
            cks52.setWN7(CommonDtoUtil.objValToString(data.getWN7()));
            // wN8
            cks52.setWN8(CommonDtoUtil.objValToString(data.getWN8()));
            // wN9
            cks52.setWN9(CommonDtoUtil.objValToString(data.getWN9()));
            // dmyHindo
            cks52.setDmyHindo(data.getDmyHindo());
            // 詳細データID
            cks52.setKs52Id(CommonDtoUtil.objValToString(data.getKs52Id()));
            // 週間計画ID
            cks52.setKs51Id(CommonDtoUtil.objValToString(data.getKs51Id()));
            // 曜日
            cks52.setYoubi(data.getYoubi());
            // 開始時間
            cks52.setKaishiJikan(data.getKaishiJikan());
            // 終了時間
            cks52.setShuuryouJikan(data.getShuuryouJikan());
            // 内容
            cks52.setNaiyouKnj(data.getNaiyouKnj());
            // 文字サイズ
            cks52.setFontSize(CommonDtoUtil.objValToString(data.getFontSize()));
            // 表示モード
            cks52.setDispMode(CommonDtoUtil.objValToString(data.getDispMode()));
            // 文字位置
            cks52.setAlignment(CommonDtoUtil.objValToString(data.getAlignment()));
            // サービス種類CD
            cks52.setSvShuruiCd(data.getSvShuruiCd());
            // サービス項目（台帳）ID
            cks52.setSvItemCd(CommonDtoUtil.objValToString(data.getSvItemCd()));
            // サービス事業者CD
            cks52.setSvJigyoId(CommonDtoUtil.objValToString(data.getSvJigyoId()));
            // 文字カラー
            cks52.setFontColor(CommonDtoUtil.objValToString(data.getFontColor()));
            // 背景カラー
            cks52.setBackColor(CommonDtoUtil.objValToString(data.getBackColor()));
            // 時間表示区分
            cks52.setTimeKbn(CommonDtoUtil.objValToString(data.getTimeKbn()));
            // 週単位以外のｻｰﾋﾞｽ区分
            cks52.setIgaiKbn(CommonDtoUtil.objValToString(data.getIgaiKbn()));
            // 週単位以外のｻｰﾋﾞｽ（日付指定）
            cks52.setIgaiDate(data.getIgaiDate());
            // 週単位以外のｻｰﾋﾞｽ（曜日指定）
            cks52.setIgaiWeek(data.getIgaiWeek());
            // 福祉用具貸与の単価
            cks52.setSvTani(CommonDtoUtil.objValToString(data.getSvTani()));
            // 福祉用具貸与マスタID
            cks52.setFygId(CommonDtoUtil.objValToString(data.getFygId()));
            // 枠外表示するかのフラグ
            cks52.setWakugaiFlg(CommonDtoUtil.objValToString(data.getWakugaiFlg()));
            // 4.2.3.1.1.変数.サービス種類名称リスト.★サービス種類コード→4-5 ＝ 戻り値.サービス種類の場合
            // レスポンスパラメータ詳細.詳細リスト.サービス種類名称 に 変数.サービス種類名称リスト.略称 を設定する
            cks52.setSvShuruiKnj(svjigyoNameInfoMap.get(data.getSvShuruiCd()));
            // 4.2.3.1.2.変数.サービス事業名称略称リスト.サービス事業者コード ＝ 戻り値.サービス事業者CD
            for (KghCmnSvselSelJigyoShubetuOutEntity item : filtered) {
                if (item.getSvJigyoCd().equals(CommonDtoUtil.objValToString(data.getSvJigyoId()))) {
                    // レスポンスパラメータ詳細.詳細リスト.サービス事業者名称 に 変数.サービス事業名称略称リスト.事業名 を設定する
                    cks52.setSvJigyoKnj(item.getJigyoKnj());
                    // レスポンスパラメータ詳細.詳細リスト.サービス事業者略称 に 変数.サービス事業名称略称リスト.事業名（略称） を設定する
                    cks52.setSvJigyoRyaku(item.getJigyoRyakuKnj());
                }
            }
            // 4.2.3.1.3.サービス項目名称を取得
            // data.getSvJigyoId();
            // data.getSvItemCd();
            // termId2DateLogicOutDto.getSYmd();
            // termId2DateLogicOutDto.getEYmd();
            // CMN06FC009S16(共通サービス) 共通関数
            String itemName = KghKrkZCpnWeekLogic.getName(data.getSvJigyoId(), data.getSvItemCd(),
                    termId2DateLogicOutDto.getSYmd(), termId2DateLogicOutDto.getEYmd());
            // ・レスポンスパラメータ詳細.詳細リスト.サービス項目名称に戻り値.サービス項目名称を設定する
            cks52.setSvItemKnj(itemName);
            cks52List.add(cks52);
        }
        response.setCks52List(cks52List);
        // 加算データ処理
        processKasanData(request, response);
        // 担当者処理
        processTantoData(request, response);
        // 隔週
        processKakushuData(request, response);
        // 月日指定
        processTsukihiData(request, response);
    }

    /**
     * 加算データ処理（4.3）
     * 
     * @param request  週間計画パターン（設定）明細情報を取得サービス入力Dto
     * @param response 週間計画パターン（設定）明細情報を取得サービス出力Dto
     */
    private void processKasanData(WeekPlanPatternSettingsDetailSelectServiceInDto request,
            WeekPlanPatternSettingsDetailSelectServiceOutDto response) {
        // 4.3.週間計画ﾊﾟﾀｰﾝ（加算データ）情報の検索処理を行う
        CpnWeekPtnInputKasanByCriteriaInEntity cpnWeekPtnInputKasanByCriteria = new CpnWeekPtnInputKasanByCriteriaInEntity();
        // 週間計画ID リクエストパラメータ.週間計画ID
        cpnWeekPtnInputKasanByCriteria.setKs51Id(CommonDtoUtil.strValToInt(request.getKs51Id()));
        List<CpnWeekPtnInputKasanOutEntity> cpnWeekPtnInputKasanList = cpnMucKs55SelectMapper
                .findCpnWeekPtnInputKasanByCriteria(cpnWeekPtnInputKasanByCriteria);
        // 4.3.1.戻り値のデータがある場合
        if (!CollectionUtils.isEmpty(cpnWeekPtnInputKasanList)) {
            // レスポンスパラメータ詳細.詳細リスト.詳細データID=戻り値.詳細データIDの場合
            for (Gui01044Cks52 cks52 : response.getCks52List()) {
                List<Gui01044Cks55> cks55List = new ArrayList<>();
                for (CpnWeekPtnInputKasanOutEntity item : cpnWeekPtnInputKasanList) {
                    if (cks52.getKs52Id().equals(CommonDtoUtil.objValToString(item.getKs52Id()))) {
                        Gui01044Cks55 cks55 = new Gui01044Cks55();
                        // 加算データID
                        cks55.setKs55Id(CommonDtoUtil.objValToString(item.getKs55Id()));
                        // 詳細データID
                        cks55.setKs52Id(CommonDtoUtil.objValToString(item.getKs52Id()));
                        // 加算サービス事業者ID
                        cks55.setSvJigyoId(CommonDtoUtil.objValToString(item.getSvJigyoId()));
                        // 加算サービス項目ID
                        cks55.setSvItemCd(CommonDtoUtil.objValToString(item.getSvItemCd()));
                        // 回数
                        cks55.setKaisuu(CommonDtoUtil.objValToString(item.getKaisuu()));
                        // 福祉用具貸与の単価
                        cks55.setSvTani(CommonDtoUtil.objValToString(item.getSvTani()));
                        // 福祉用具貸与マスタID
                        cks55.setFygId(CommonDtoUtil.objValToString(item.getFygId()));
                        // 週間計画ID
                        cks55.setKs51Id(CommonDtoUtil.objValToString(item.getKs51Id()));
                        // Dmy詳細データID
                        cks55.setDmyKs52Id(CommonDtoUtil.objValToString(item.getDmyKs52Id()));
                        // 正式名称漢字
                        cks55.setFormalnameKnj(item.getFormalnameKnj());
                        cks55List.add(cks55);
                    }
                }
                // レスポンスパラメータ.詳細リスト.加算リストに戻り値を設定する
                cks52.setCks55List(cks55List);
            }
        }
    }

    /**
     * 担当者データ処理（4.4）
     * 
     * @param request  週間計画パターン（設定）明細情報を取得サービス入力Dto
     * @param response 週間計画パターン（設定）明細情報を取得サービス出力Dto
     */
    private void processTantoData(WeekPlanPatternSettingsDetailSelectServiceInDto request,
            WeekPlanPatternSettingsDetailSelectServiceOutDto response) {
        // 4.4.週間計画ﾊﾟﾀｰﾝ（担当者）情報の検索処理を行う
        CpnWeekPtnTantoByCriteriaInEntity input = new CpnWeekPtnTantoByCriteriaInEntity();
        // 週間計画ID リクエストパラメータ.週間計画ID
        input.setKs51Id(CommonDtoUtil.strValToInt(request.getKs51Id()));
        List<CpnWeekPtnTantoOutEntity> results = cpnMucKs56SelectMapper.findCpnWeekPtnTantoByCriteria(input);
        // 4.4.1.戻り値のデータがある場合
        if (!CollectionUtils.isEmpty(results)) {
            // レスポンスパラメータ詳細.詳細リスト.詳細データID=戻り値.詳細データIDの場合
            for (Gui01044Cks52 cks52 : response.getCks52List()) {
                List<Gui01044Cks56> cks56List = new ArrayList<>();
                for (CpnWeekPtnTantoOutEntity item : results) {
                    if (cks52.getKs52Id().equals(CommonDtoUtil.objValToString(item.getKs52Id()))) {
                        Gui01044Cks56 cks56 = new Gui01044Cks56();
                        // 担当者データID
                        cks56.setKs56Id(CommonDtoUtil.objValToString(item.getKs56Id()));
                        // 詳細データID
                        cks56.setKs52Id(CommonDtoUtil.objValToString(item.getKs52Id()));
                        // 担当者名称
                        cks56.setShokushuKnj(item.getRyakuKnj());
                        // 職種
                        cks56.setShokushuId(CommonDtoUtil.objValToString(item.getShokushuId()));
                        cks56List.add(cks56);
                    }
                }
                // レスポンスパラメータ.詳細リスト.担当者リストに戻り値を設定する
                cks52.setCks56List(cks56List);
            }
        }
    }

    /**
     * 隔週データ処理（4.5）
     * 
     * @param request  週間計画パターン（設定）明細情報を取得サービス入力Dto
     * @param response 週間計画パターン（設定）明細情報を取得サービス出力Dto
     */
    private void processKakushuData(WeekPlanPatternSettingsDetailSelectServiceInDto request,
            WeekPlanPatternSettingsDetailSelectServiceOutDto response) {
        // 週間計画ﾊﾟﾀｰﾝ（隔週）情報の検索処理を行う
        CpnWeekIgaiHindoHanniKakusyuiPtnByCriteriaInEntity input = new CpnWeekIgaiHindoHanniKakusyuiPtnByCriteriaInEntity();
        // 週間計画ID リクエストパラメータ.週間計画ID
        input.setAlKs51Id(CommonDtoUtil.strValToInt(request.getKs51Id()));
        List<CpnWeekIgaiHindoHanniKakusyuiPtnOutEntity> results = cpnMucKs58SelectMapper
                .findCpnWeekIgaiHindoHanniKakusyuiPtnByCriteria(input);
        // 4.5.1.戻り値のデータがある場合
        if (!CollectionUtils.isEmpty(results)) {
            // レスポンスパラメータ詳細.詳細リスト.詳細データID=戻り値.詳細データIDの場合
            for (Gui01044Cks52 cks52 : response.getCks52List()) {
                List<Gui01044Cks58> cks58List = new ArrayList<>();
                for (CpnWeekIgaiHindoHanniKakusyuiPtnOutEntity item : results) {
                    if (cks52.getKs52Id().equals(CommonDtoUtil.objValToString(item.getKs52Id()))) {
                        Gui01044Cks58 cks58 = new Gui01044Cks58();
                        // 隔週データのプライマリID
                        cks58.setKs58Id(CommonDtoUtil.objValToString(item.getKs58Id()));
                        // 詳細データID
                        cks58.setKs52Id(CommonDtoUtil.objValToString(item.getKs52Id()));
                        // 隔週基準年月日
                        cks58.setKakusyuuYmd(item.getKakusyuuYmd());
                        // 隔週週間隔
                        cks58.setKakusyuuKankaku(CommonDtoUtil.objValToString(item.getKakusyuuKankaku()));
                        // 曜日区分
                        cks58.setYoubi(CommonDtoUtil.objValToString(item.getYoubi()));
                        cks58List.add(cks58);
                    }
                }
                // レスポンスパラメータ.詳細リスト.隔週リストに戻り値を設定する
                cks52.setCks58List(cks58List);
            }
        }
    }

    /**
     * 月日指定データ処理（4.6）
     * 
     * @param request  週間計画パターン（設定）明細情報を取得サービス入力Dto
     * @param response 週間計画パターン（設定）明細情報を取得サービス出力Dto
     */
    private void processTsukihiData(WeekPlanPatternSettingsDetailSelectServiceInDto request,
            WeekPlanPatternSettingsDetailSelectServiceOutDto response) {
        // 4.6.週間計画（月日指定）情報の検索処理を行う
        Cks57InfoByCriteriaInEntity input = new Cks57InfoByCriteriaInEntity();
        // 週間計画ID リクエストパラメータ.週間計画ID
        input.setKs51Id(CommonDtoUtil.strValToInt(request.getKs51Id()));
        List<Cks57InfoOutEntity> results = cpnTucCks57SelectMapper.findCks57InfoByCriteria(input);
        // 4.6.1.戻り値のデータがある場合
        if (!CollectionUtils.isEmpty(results)) {
            // レスポンスパラメータ詳細.詳細リスト.詳細データID=戻り値.詳細データIDの場合
            for (Gui01044Cks52 cks52 : response.getCks52List()) {
                List<Gui01044Cks57> cks57List = new ArrayList<>();
                for (Cks57InfoOutEntity item : results) {
                    if (cks52.getKs52Id().equals(CommonDtoUtil.objValToString(item.getKs52Id()))) {
                        Gui01044Cks57 cks57 = new Gui01044Cks57();
                        // 月日データID
                        cks57.setKs57Id(CommonDtoUtil.objValToString(item.getKs57Id()));
                        // 詳細データID
                        cks57.setKs52Id(CommonDtoUtil.objValToString(item.getKs52Id()));
                        // 月日指定_開始日
                        cks57.setStartYmd(item.getStartYmd());
                        // 月日指定_終了日
                        cks57.setEndYmd(item.getEndYmd());
                        cks57List.add(cks57);
                    }
                }
                // レスポンスパラメータ.詳細リスト.月日リストに戻り値を設定する
                cks52.setCks57List(cks57List);
            }
        }
    }

    /**
     * 週以外、月日、曜日の文字転換
     * 
     * @param request  週間計画パターン（設定）明細情報を取得サービス入力Dto
     * @param response 週間計画パターン（設定）明細情報を取得サービス出力Dto
     */
    private void letterFormat(WeekPlanPatternSettingsDetailSelectServiceInDto request,
            WeekPlanPatternSettingsDetailSelectServiceOutDto response) {
        for (Gui01044Cks52 cks52List : response.getCks52List()) {
            // 5.1.設定する
            // 変数.週単位以外文字＝NULL
            String weekUnitOtherThanLetter = null;
            // 5.2.レスポンスパラメータ.詳細リスト.曜日 ＝ ”9999999”の場合
            if (CommonConstants.YOUBI_STR.equals(cks52List.getYoubi())) {
                // 5.2.1.レスポンスパラメータ.詳細リスト.週単位以外のｻｰﾋﾞｽ区分 ＝ 1の場合
                if (CommonConstants.IGAI_KBN_STR_ONE.equals(cks52List.getIgaiKbn())) {
                    // レスポンスパラメータ.詳細リスト.週単位以外のｻｰﾋﾞｽ（日付指定）
                    weekUnitOtherThanLetter = kghKrkZCpnFuncLogic.getWeekDateToMoji(cks52List.getIgaiDate());
                }
                // 5.2.2.レスポンスパラメータ.詳細リスト.週単位以外のｻｰﾋﾞｽ区分 ＝ 2の場合
                if (CommonConstants.IGAI_KBN_STR_TWO.equals(cks52List.getIgaiKbn())) {
                    // レスポンスパラメータ.詳細リスト.週単位以外のｻｰﾋﾞｽ（曜日指定）
                    weekUnitOtherThanLetter = kghKrkZCpnFuncLogic.getWeekToMoji(cks52List.getIgaiWeek());
                }
                // 5.2.3.レスポンスパラメータ.詳細リスト.週単位以外のｻｰﾋﾞｽ区分 ＝ 3の場合
                if (CommonConstants.IGAI_KBN_STR_THREE.equals(cks52List.getIgaiKbn())) {
                    // 5.2.3.1.レスポンスパラメータ.詳細リスト.週間計画月日リストを繰り返す
                    for (Gui01044Cks57 cks57 : cks52List.getCks57List()) {
                        // 変数.週単位以外文字 <>"" or NULL
                        if (StringUtils.isNotEmpty(weekUnitOtherThanLetter)) {
                            // 変数.週単位以外文字 += "、"
                            weekUnitOtherThanLetter += CommonConstants.STRING_COMMA;
                        }
                        // f_cmp_s2w_ez 西暦→和暦関数（簡易版）
                        // レスポンスパラメータ.詳細リスト.月日指定_開始日
                        String startYmd = kghCmpF01Logic.getCmpS2wEz(cks57.getStartYmd(), CommonConstants.INT_1);
                        // 変数.週単位以外文字 ＋＝ 戻り値.和暦日付
                        weekUnitOtherThanLetter += startYmd;
                        // f_cmp_s2w_ez 西暦→和暦関数（簡易版）
                        // レスポンスパラメータ.詳細リスト.月日指定_終了日
                        String endYmd = kghCmpF01Logic.getCmpS2wEz(cks57.getEndYmd(), CommonConstants.INT_1);
                        // 変数.週単位以外文字 ＋＝ 戻り値.和暦日付
                        weekUnitOtherThanLetter += endYmd;
                    }
                }
                // 5.2.4.レスポンスパラメータ.詳細リスト.週単位以外のｻｰﾋﾞｽ区分 ＝ 4の場合
                if (CommonConstants.IGAI_KBN_STR_FOUR.equals(cks52List.getIgaiKbn())) {
                    // レスポンスパラメータ.詳細リスト.隔週リスト.隔週週間隔
                    List<Cks58InDto> cks58InDtoList = new ArrayList<>();
                    for (Gui01044Cks58 cks58 : cks52List.getCks58List()) {
                        Cks58InDto cks58InDto = new Cks58InDto();
                        cks58InDto.setKakusyuuKankaku(cks58.getKakusyuuKankaku());
                        cks58InDtoList.add(cks58InDto);
                    }
                    // f_cpn_week_kakusyuu_to_moji 週間計画：隔週指定データを文字列に変換
                    String res = kghKrkZCpnFuncLogic.getWeekKakusyuuToMoji(cks58InDtoList);
                    // 変数.週単位以外文字 ＋＝ 戻り値.頻度文字列
                    weekUnitOtherThanLetter += res;
                }
            }
            // 5.3.レスポンスパラメータ.詳細リスト.週単位以外文字 に 変数.週単位以外文字を設定する
            cks52List.setIgaiMoji(weekUnitOtherThanLetter);
        }
    }

    /**
     * フィルタ対象コードチェック
     */
    private boolean containsFilterCd(String svShuruiCd) {
        for (String cd : CommonConstants.FILTER_SV_SHURUI_CD) {
            if (cd.equals(svShuruiCd)) {
                return true;
            }
        }
        return false;
    }

}
