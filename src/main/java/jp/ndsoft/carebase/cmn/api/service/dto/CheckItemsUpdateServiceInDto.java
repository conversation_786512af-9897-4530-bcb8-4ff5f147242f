package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckItem;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.06.03
 * <AUTHOR>
 * @implNote GUI00892_チェック項目画面 データ保存サービス入力Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CheckItemsUpdateServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 法人ID */
    @NotEmpty
    private String houjinId;

    /** 施設ID */
    @NotEmpty
    private String shisetuId;

    /** 事業者ID */
    @NotEmpty
    private String jigyoId;

    /** 利用者ID */
    @NotEmpty
    private String userId;

    /** 種別ID */
    @NotEmpty
    private String syubetsuId;

    /** 計画期間ID */
    private String sc1Id;

    /** 履歴ID */
    private String assId;

    /** 様式ID */
    private String cstId;

    /** 作成日 */
    private String createYmd;

    /** 作成者 */
    private String shokuId;

    /** 履歴更新区分 */
    @NotEmpty
    private String historyUpdateKbn;

    /** チェック項目詳細情報 */
    private List<Gui00892CheckItem> checkItemiList;

}
