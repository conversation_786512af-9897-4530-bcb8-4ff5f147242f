package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentSummaryImportSettingUpdateInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentSummaryImportSettingUpdateOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui00827ParameterListUpdateInDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.ComMocSysCIniMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocSysCIni;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocSysCIniCriteria;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * @since 2025/05/13
 * <AUTHOR> THANH PHONG
 * @implNote GUI00827_「アセスメント総括取込み設定」画面
 */
@Service
public class AssessmentSummaryImportSettingUpdateServiceImpl extends
        UpdateServiceImpl<AssessmentSummaryImportSettingUpdateInDto, AssessmentSummaryImportSettingUpdateOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 24-03 iniファイルデータ保存テーブル */
    @Autowired
    private ComMocSysCIniMapper comMocSysCIniMapper;

    int count = 0;

    @Override
    protected AssessmentSummaryImportSettingUpdateOutDto mainProcess(AssessmentSummaryImportSettingUpdateInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        AssessmentSummaryImportSettingUpdateOutDto outDto = new AssessmentSummaryImportSettingUpdateOutDto();
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        /** 2.アセスメント総括取込み設定セクションで設定された内容で、以下のキー毎に画面のすべての項目を更新する */
        for (Gui00827ParameterListUpdateInDto entity : inDto.getParamList()) {
            ComMocSysCIniCriteria comMocSysCIniCriteria = new ComMocSysCIniCriteria();
            // 更新条件
            comMocSysCIniCriteria.createCriteria()
                    // システムコード=リクエストパラメータ.システムコード
                    .andSysCdEqualTo(inDto.getSyscd())
                    // 職員ID＝リクエストパラメータ.職員ID
                    .andShokuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShokuId()))
                    // 法人ID＝リクエストパラメータ.法人ID
                    .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                    // 施設ID＝リクエストパラメータ.施設ID
                    .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()))
                    // 事業所ID＝リクエストパラメータ.事業所ID
                    .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                    // 機能名＝リクエストパラメータ.機能名
                    .andKinounameKnjEqualTo(inDto.getKinounameKnj())
                    // セクション＝リクエストパラメータ.セクション
                    .andSectionKnjEqualTo(inDto.getSectionKnj()).andKeyKnjEqualTo(entity.getKeyKnj());
            // ■Input
            ComMocSysCIni comMocSysCIni = new ComMocSysCIni();
            comMocSysCIni.setParamKnj(entity.getParamKnj());
            // CommonDaoUtil.setUpdateCommonColumns(comMocSysCIni,
            // CommonDtoUtil.strValToBigInteger(entity.getModifiedCnt()));
            count = comMocSysCIniMapper.updateByCriteriaSelective(comMocSysCIni, comMocSysCIniCriteria);
            if (count <= 0) {
                ComMocSysCIni ins = new ComMocSysCIni();
                ins.setSysCd(inDto.getSyscd());
                ins.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
                ins.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
                ins.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                ins.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                ins.setKinounameKnj(inDto.getKinounameKnj());
                ins.setSectionKnj(inDto.getSectionKnj());
                ins.setKeyKnj(entity.getKeyKnj());
                ins.setParamKnj(entity.getParamKnj());
                count = comMocSysCIniMapper.insertSelective(ins);
                if (count <= 0)
                    throw new ExclusiveException();
            }
        }

        // 3. レスポンスを返却する。
        return outDto;
    }

}
