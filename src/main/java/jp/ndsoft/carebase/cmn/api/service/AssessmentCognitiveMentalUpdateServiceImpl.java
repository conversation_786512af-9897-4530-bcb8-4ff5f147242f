package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00801Kan13Info;
import jp.ndsoft.carebase.cmn.api.logic.AssessmentHomeLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentCognitiveMentalUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentCognitiveMentalUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeSaveServiceInDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kan13H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kan13R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdlRirekiMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan13H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan13H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan13R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan13R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRireki;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRirekiCriteria;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * @since 2025.05.19
 * <AUTHOR> 石貴勇
 * @apiNote GUI00801_［アセスメント］画面（居宅）（6③-④） データ保存
 */
@Service
public class AssessmentCognitiveMentalUpdateServiceImpl extends
        UpdateServiceImpl<AssessmentCognitiveMentalUpdateServiceInDto, AssessmentCognitiveMentalUpdateServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** ［アセスメント］画面（居宅）画面のロジッククラス */
    @Autowired
    private AssessmentHomeLogic assessmentHomeLogic;

    /** ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_kan13_h21） */
    @Autowired
    private CpnTucGdl4Kan13H21Mapper cpnTucGdl4Kan13H21Mapper;

    /** ＧＬ＿③認知機能・④精神・行動障害（R３改訂）テーブル（cpn_tuc_gdl5_kan13_r3） */
    @Autowired
    private CpnTucGdl5Kan13R3Mapper cpnTucGdl5Kan13R3Mapper;

    /** ＧＬ＿居宅アセスメント履歴 */
    @Autowired
    private CpnTucGdlRirekiMapper cpnTucGdlRirekiMapper;

    @Autowired
    private PlatformTransactionManager transactionManager;       

    /**
     * ［アセスメント］画面（居宅）（6③-④） データ保存
     * 
     * @param inDto ［アセスメント］画面（居宅）（6③-④） データ保存の入力DTO
     * @return ［アセスメント］画面（居宅）（6③-④） データ保存の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected AssessmentCognitiveMentalUpdateServiceOutDto mainProcess(
            AssessmentCognitiveMentalUpdateServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        AssessmentCognitiveMentalUpdateServiceOutDto outDto = new AssessmentCognitiveMentalUpdateServiceOutDto();
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        /*
         * ===============2. バリデーションチェックを行う。===============
         * 
         */
        boolean resFlg = assessmentHomeLogic.documentPerformValidationCheck(loginDto);
        // 2.2.4. 【変数】.処理結果が「false：失敗」の場合
        if (!resFlg) {
            // 2.2.4. 【変数】.処理結果が「false：失敗」の場合、下記返却する情報を設定して、後続処理を飛ばして、API処理を正常終了とする。
            // レスポンス.計画期間ID ： リクエストパラメータ.計画期間ID
            outDto.setSc1Id(loginDto.getSc1Id());
            // レスポンス.アセスメントID ： リクエストパラメータ.アセスメントID
            outDto.setGdlId(loginDto.getGdlId());
            // レスポンス.エラー区分: "1
            outDto.setErrKbn(CommonConstants.E_DOC_CHECK_ERR);
            return outDto;
        }
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transactionB = transactionManager.getTransaction(def);
        try {
            // ［アセスメント］画面（居宅）（1） データ保存
            outDto = mainProcessMealUpdate(inDto);
            transactionManager.commit(transactionB);
        } catch (Exception e) {
            transactionManager.rollback(transactionB);
            throw e;
        }

        // e-文書_ケアチェック表１のリクエストパラメータ
        TransactionStatus transactionC = transactionManager.getTransaction(def);
        try {
            mainProcessEdocOut(inDto, outDto);
            transactionManager.commit(transactionC);
        } catch (Exception e) {
            transactionManager.rollback(transactionC);
            throw e;
        }

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * API定義 (e文書出力)
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected void mainProcessEdocOut(
            AssessmentCognitiveMentalUpdateServiceInDto inDto, AssessmentCognitiveMentalUpdateServiceOutDto outDto)
            throws Exception {
        // 入力dtoをLoginDtoにコピーする
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);

        /*
         * ===============e文書出力===============
         * 
         */
        // e文書出力
        String errKbn = assessmentHomeLogic.getPrint(loginDto, outDto.getSc1Id());
        outDto.setErrKbn(errKbn);
    }

    /**
     * ［アセスメント］画面（居宅）（1） データ保存
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected AssessmentCognitiveMentalUpdateServiceOutDto mainProcessMealUpdate(
            AssessmentCognitiveMentalUpdateServiceInDto inDto)
            throws Exception {

        AssessmentCognitiveMentalUpdateServiceOutDto outDto = new AssessmentCognitiveMentalUpdateServiceOutDto();

        String sc1IdTemp = CommonConstants.BLANK_STRING;

        // ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_kan13_h21）登録で採番されたアセスメントID
        String gdlIdTemp = CommonConstants.BLANK_STRING;

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. 計画対象期間の保存処理===============
         * 
         */
        // 2.1.リクエストパラメータ.計画対象期間IDがnullの場合、【27-06記録共通期間】情報を登録する。
        AssessmentHomeSaveServiceInDto loginDto = this.setAssessmentHomeSaveServiceInDto(inDto);

        if (inDto.getSc1Id() == null || StringUtil.isEmpty(inDto.getSc1Id())) {
            // 計画対象期間の保存処理
            this.assessmentHomeLogic.insertKghTucKrkKikan(loginDto);
            // 変数.計画対象期間ID=採番した期間ID
            sc1IdTemp = loginDto.getSc1Id();

        } else {
            // 変数.計画対象期間ID=リクエストパラメータ.計画対象期間ID
            sc1IdTemp = inDto.getSc1Id();
        }

        // 計画対象期間ID
        outDto.setSc1Id(sc1IdTemp);

        /*
         * ======3.リクエストパラメータ.削除処理区分が2:画面を履歴ごと削除するの場合、下記テーブルデータを削除する。========
         * 
         */
        if (CommonConstants.DELETE_KBN_2.equals(inDto.getDeleteKbn())) {
            assessmentHomeLogic.homeLogicsyuri(loginDto, CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        }

        /*
         * ======4. リクエストパラメータ.削除処理区分が1:画面のみ削除するの場合========
         * 
         */
        else if (CommonConstants.DELETE_KBN_1.equals(inDto.getDeleteKbn())) {
            // 4.1. リクエストパラメータ.改定フラグが4「H21/４改訂版」の場合、【ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）】情報を更新する。
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                this.deleteKan13H21(inDto, sc1IdTemp);
            }
            // 4.2. リクエストパラメータ.改定フラグが5「R3/４改訂版」の場合、【ＧＬ＿③認知機能・④精神・行動障害（R３改訂）】情報を更新する。
            else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                this.deleteKan13R3(inDto, sc1IdTemp);
            }

            // 4.3. 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
            this.clearRireki(inDto, sc1IdTemp);


        } else {
            /*
             * ======5. 以外の場合========
             * 
             */
            // 5.1. 履歴情報の保存処理
            // 5.1.1. リクエストパラメータ.履歴更新区分が"C":新規の場合、【ＧＬ＿居宅アセスメント履歴】情報を登録する。
            if (CommonDtoUtil.isHistoryCreate(inDto)) {
                this.insertRireki(inDto, sc1IdTemp);

                // 「5.1.1.」で採番したアセスメントID
                gdlIdTemp = inDto.getGdlId();
                // アセスメントID
                outDto.setGdlId(gdlIdTemp);
            } else if (CommonDtoUtil.isHistoryUpdate(inDto)) {
                // 5.1.2. リクエストパラメータ.履歴更新区分が"U":更新の場合、【ＧＬ＿居宅アセスメント履歴】情報を更新する。
                this.updateRireki(inDto, sc1IdTemp);

                // アセスメントID
                outDto.setGdlId(inDto.getGdlId());
            }

            // 5.2. ③認知機能・④精神・行動障害情報の保存処理
            // 5.2.1. リクエストパラメータ.更新区分が"C":新規の場合、③認知機能・④精神・行動障害情報の保存処理を登録する。
            if (CommonDtoUtil.isHistoryCreate(inDto)) {
                // 5.2.1.1. リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、【ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）】情報を登録する。
                if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                    this.insertKan13H21(inDto, sc1IdTemp, gdlIdTemp);

                } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                    // 5.2.1.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【ＧＬ＿③認知機能・④精神・行動障害（R３改訂）】情報を登録する。
                    this.insertKan13R3(inDto, sc1IdTemp, gdlIdTemp);

                }
            }
            // 5.2.2. リクエストパラメータ.更新区分が"U":更新の場合、③認知機能・④精神・行動障害情報を更新する。
            else if (CommonDtoUtil.isHistoryUpdate(inDto)) {
                // 5.2.2.1. リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、【ＧＬ＿住宅等の状況（Ｈ２１改訂）】情報を更新する。
                if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                    this.updateKan13H21(inDto, sc1IdTemp);

                } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                    this.updateKan13R3(inDto, sc1IdTemp);
                }

            }

            // 5.3. リクエストパラメータ.【課題と目標リスト】の件数分、課題と目標情報を保存する。
            assessmentHomeLogic.homeLogicCpnTucGdlKadai(loginDto, CommonDtoUtil.strValToInt(sc1IdTemp));
        }

        return outDto;
    }

    /**
     * ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_kan13_h21）の更新詳細
     * 関数名：deleteKan13H21
     * 
     * @param inDto     ［アセスメント］画面（居宅）（6③-④） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     */
    private void deleteKan13H21(AssessmentCognitiveMentalUpdateServiceInDto inDto, String sc1IdTemp) {

        CpnTucGdl4Kan13H21Criteria cpnTucGdl4Kan13H21Criteria = new CpnTucGdl4Kan13H21Criteria();
        cpnTucGdl4Kan13H21Criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp));

        // DAOを実行
        this.cpnTucGdl4Kan13H21Mapper.deleteByCriteria(cpnTucGdl4Kan13H21Criteria);

    }

    /**
     * ＧＬ＿③認知機能・④精神・行動障害（R３改訂）テーブル（cpn_tuc_gdl5_kan13_r3）の更新詳細
     * 関数名：deleteKan13R3
     * 
     * @param inDto     ［アセスメント］画面（居宅）（6③-④） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     */
    private void deleteKan13R3(AssessmentCognitiveMentalUpdateServiceInDto inDto, String sc1IdTemp) {

        CpnTucGdl5Kan13R3Criteria cpnTucGdl5Kan13R3Criteria = new CpnTucGdl5Kan13R3Criteria();
        cpnTucGdl5Kan13R3Criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp));

        // DAOを実行
        this.cpnTucGdl5Kan13R3Mapper.deleteByCriteria(cpnTucGdl5Kan13R3Criteria);

    }

    /**
     * ＧＬ＿居宅アセスメント履歴テーブル（cpn_tuc_gdl_rireki）の更新詳細
     * 関数名：clearRireki
     * 
     * @param inDto     ［アセスメント］画面（居宅）（6③-④） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     */
    private void clearRireki(AssessmentCognitiveMentalUpdateServiceInDto inDto, String sc1IdTemp) {
        // DAOパラメータを作成
        // 3.1.居宅アセスメント履歴を更新する
        // ＧＬ＿居宅アセスメント履歴パラメータを作成
        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                // 利用者ＩＤ
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 法人ID
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();
        // Ｆの状態
        cpnTucGdlRireki.setAss8(CommonConstants.BLANK_STRING);
        // Ｇの状態
        cpnTucGdlRireki.setAss9(CommonConstants.BLANK_STRING);

        // DAOを実行
        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRireki, criteria);
    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を登録する。
     * 関数名：insertRireki
     * 
     * @param inDto     ［アセスメント］画面（居宅）（6③-④） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     */
    private void insertRireki(AssessmentCognitiveMentalUpdateServiceInDto inDto, String sc1IdTemp) throws Exception {
        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();
        // 計画期間ID
        cpnTucGdlRireki.setSc1Id(CommonDtoUtil.strValToInt(sc1IdTemp));
        // 法人ID
        cpnTucGdlRireki.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        cpnTucGdlRireki.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        cpnTucGdlRireki.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        cpnTucGdlRireki.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // アセスメント実施日
        cpnTucGdlRireki.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRireki.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // Ｆの状態
        cpnTucGdlRireki.setAss8(CommonConstants.MARU);
        // Ｇの状態
        cpnTucGdlRireki.setAss9(CommonConstants.MARU);
        // Ｊの状態
        cpnTucGdlRireki.setAss12(CommonConstants.DASH);
        // 本人の基本動作等8
        cpnTucGdlRireki.setAss13(CommonConstants.DASH);
        // 改定フラグ
        cpnTucGdlRireki.setNinteiFormF(CommonDtoUtil.strValToInt(inDto.getNinteiFormF()));

        this.cpnTucGdlRirekiMapper.insertSelectiveAndReturn(cpnTucGdlRireki);

        inDto.setGdlId(CommonDtoUtil.objValToString(cpnTucGdlRireki.getGdlId()));

    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
     * 関数名：updateRireki
     * 
     * @param inDto     ［アセスメント］画面（居宅）（6③-④） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     */
    private void updateRireki(AssessmentCognitiveMentalUpdateServiceInDto inDto, String sc1IdTemp) {

        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();
        // アセスメント実施日
        cpnTucGdlRireki.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRireki.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // Ｆの状態
        cpnTucGdlRireki.setAss8(CommonConstants.MARU);
        // Ｇの状態
        cpnTucGdlRireki.setAss9(CommonConstants.MARU);
        // Ｊの状態
        cpnTucGdlRireki.setAss12(CommonConstants.DASH);
        // 本人の基本動作等8
        cpnTucGdlRireki.setAss13(CommonConstants.DASH);

        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                // 利用者ＩＤ
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 法人ID
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRireki, criteria);

    }

    /**
     * ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_kan13_h21）の登録詳細
     * 関数名：insertKan13H21
     * 
     * @param inDto     ［アセスメント］画面（居宅）（6③-④） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @param gdlIdTemp アセスメントID
     * @throws Exception Exception
     */
    private void insertKan13H21(AssessmentCognitiveMentalUpdateServiceInDto inDto, String sc1IdTemp, String gdlIdTemp)
            throws Exception {

        CpnTucGdl4Kan13H21 cpnTucGdl4Kan13H21 = this.setCpnTucGdl4Kan13H21(inDto.getKan13Info());
        // アセスメントID
        cpnTucGdl4Kan13H21.setGdlId(CommonDtoUtil.strValToInt(gdlIdTemp));
        // 計画期間ID
        cpnTucGdl4Kan13H21.setSc1Id(CommonDtoUtil.strValToInt(sc1IdTemp));

        this.cpnTucGdl4Kan13H21Mapper.insertSelective(cpnTucGdl4Kan13H21);

    }

    /**
     * ＧＬ＿③認知機能・④精神・行動障害（R３改訂）テーブル（cpn_tuc_gdl5_kan13_r3）の登録詳細
     * 関数名：insertHou11R3
     * 
     * @param inDto     ［アセスメント］画面（居宅）（6③-④） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @param gdlIdTemp アセスメントID
     * @throws Exception Exception
     */
    private void insertKan13R3(AssessmentCognitiveMentalUpdateServiceInDto inDto, String sc1IdTemp, String gdlIdTemp)
            throws Exception {

        CpnTucGdl5Kan13R3 cpnTucGdl5Kan13R3 = this.setCpnTucGdl5Kan13R3(inDto.getKan13Info());
        // アセスメントID
        cpnTucGdl5Kan13R3.setGdlId(CommonDtoUtil.strValToInt(gdlIdTemp));
        // 計画期間ID
        cpnTucGdl5Kan13R3.setSc1Id(CommonDtoUtil.strValToInt(sc1IdTemp));

        this.cpnTucGdl5Kan13R3Mapper.insertSelective(cpnTucGdl5Kan13R3);

    }

    /**
     * ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_kan13_h21）の更新詳細
     * 関数名：updateKan13H21
     * 
     * @param inDto     ［アセスメント］画面（居宅）（6③-④） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @throws Exception Exception
     */
    private void updateKan13H21(AssessmentCognitiveMentalUpdateServiceInDto inDto, String sc1IdTemp)
            throws Exception {

        CpnTucGdl4Kan13H21 cpnTucGdl4Kan13H21 = this.setCpnTucGdl4Kan13H21(inDto.getKan13Info());

        CpnTucGdl4Kan13H21Criteria criteria = new CpnTucGdl4Kan13H21Criteria();
        // アセスメントID
        criteria.createCriteria()
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp));

        this.cpnTucGdl4Kan13H21Mapper
                .updateByCriteriaSelective(cpnTucGdl4Kan13H21, criteria);

    }

    /**
     * ＧＬ＿③認知機能・④精神・行動障害（R３改訂）テーブル（cpn_tuc_gdl5_kan13_r3）の更新詳細
     * 関数名：updateKan13R3
     * 
     * @param inDto     ［アセスメント］画面（居宅）（6③-④） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @throws Exception Exception
     */
    private void updateKan13R3(AssessmentCognitiveMentalUpdateServiceInDto inDto, String sc1IdTemp)
            throws Exception {

        CpnTucGdl5Kan13R3 cpnTucGdl5Kan13R3 = this.setCpnTucGdl5Kan13R3(inDto.getKan13Info());

        CpnTucGdl5Kan13R3Criteria criteria = new CpnTucGdl5Kan13R3Criteria();
        // アセスメントID
        criteria.createCriteria()
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp));

        this.cpnTucGdl5Kan13R3Mapper
                .updateByCriteriaSelective(cpnTucGdl5Kan13R3, criteria);

    }

    /**
     * 【27-06記録共通期間】情報を登録パラメータを設定する
     * 
     * @param inDto 施設マスタ
     * @return 【27-06記録共通期間】データ保存入力DTO.
     */
    private AssessmentHomeSaveServiceInDto setAssessmentHomeSaveServiceInDto(
            AssessmentCognitiveMentalUpdateServiceInDto inDto) {
        AssessmentHomeSaveServiceInDto assessmentHomeSaveServiceInDto = new AssessmentHomeSaveServiceInDto();
        // 法人ID
        assessmentHomeSaveServiceInDto.setHoujinId(inDto.getHoujinId());
        // 施設ID
        assessmentHomeSaveServiceInDto.setShisetuId(inDto.getShisetuId());
        // 事業者ID
        assessmentHomeSaveServiceInDto.setSvJigyoId(inDto.getSvJigyoId());
        // 利用者ID
        assessmentHomeSaveServiceInDto.setUserId(inDto.getUserId());
        // 種別ID
        assessmentHomeSaveServiceInDto.setSyubetsuId(inDto.getSyubetsuId());
        // 作成日
        assessmentHomeSaveServiceInDto.setKijunbiYmd(inDto.getKijunbiYmd());

        return assessmentHomeSaveServiceInDto;

    }

    /**
     * ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_kan13_h21）の登録詳細パラメータを作成
     * 
     * @param kan13Info ③認知機能・④精神・行動障害情報
     * @return ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_kan13_h21）の登録詳細パラメータ
     */
    private CpnTucGdl4Kan13H21 setCpnTucGdl4Kan13H21(Gui00801Kan13Info kan13Info) {
        CpnTucGdl4Kan13H21 cpnTucGdl4Kan13H21 = new CpnTucGdl4Kan13H21();

        // 認定項目3-1（意思の伝達） */
        cpnTucGdl4Kan13H21.setBango31(CommonDtoUtil.strValToInt(kan13Info.getBango31()));
        // 認定項目3-2（毎日の日課を理解する） */
        cpnTucGdl4Kan13H21.setBango32(CommonDtoUtil.strValToInt(kan13Info.getBango32()));
        // 認定項目3-3（生年月日や年齢を答える） */
        cpnTucGdl4Kan13H21.setBango33(CommonDtoUtil.strValToInt(kan13Info.getBango33()));
        // 認定項目3-4（面接調査の直前記憶） */
        cpnTucGdl4Kan13H21.setBango34(CommonDtoUtil.strValToInt(kan13Info.getBango34()));
        // 認定項目3-5（自分の名前を答える） */
        cpnTucGdl4Kan13H21.setBango35(CommonDtoUtil.strValToInt(kan13Info.getBango35()));
        // 認定項目3-6（今の季節を理解する） */
        cpnTucGdl4Kan13H21.setBango36(CommonDtoUtil.strValToInt(kan13Info.getBango36()));
        // 認定項目3-7（自分のいる場所を答える） */
        cpnTucGdl4Kan13H21.setBango37(CommonDtoUtil.strValToInt(kan13Info.getBango37()));
        // 認定項目3-8（徘徊） */
        cpnTucGdl4Kan13H21.setBango38(CommonDtoUtil.strValToInt(kan13Info.getBango38()));
        // 認定項目3-9（外出すると戻れない（迷子）） */
        cpnTucGdl4Kan13H21.setBango39(CommonDtoUtil.strValToInt(kan13Info.getBango39()));
        // 認定項目3-10（介護者との意思伝達） */
        cpnTucGdl4Kan13H21.setBango310(CommonDtoUtil.strValToInt(kan13Info.getBango310()));
        // 認定項目4-1（被害妄想（物を盗られたなど）） */
        cpnTucGdl4Kan13H21.setBango41(CommonDtoUtil.strValToInt(kan13Info.getBango41()));
        // 認定項目4-2（作話をする） */
        cpnTucGdl4Kan13H21.setBango42(CommonDtoUtil.strValToInt(kan13Info.getBango42()));
        // 認定項目4-3（感情が不安定になる） */
        cpnTucGdl4Kan13H21.setBango43(CommonDtoUtil.strValToInt(kan13Info.getBango43()));
        // 認定項目4-4（昼夜の逆転） */
        cpnTucGdl4Kan13H21.setBango44(CommonDtoUtil.strValToInt(kan13Info.getBango44()));
        // 認定項目4-5（しつこく同じ話をする） */
        cpnTucGdl4Kan13H21.setBango45(CommonDtoUtil.strValToInt(kan13Info.getBango45()));
        // 認定項目4-6（大声を出す） */
        cpnTucGdl4Kan13H21.setBango46(CommonDtoUtil.strValToInt(kan13Info.getBango46()));
        // 認定項目4-7（介護に抵抗する） */
        cpnTucGdl4Kan13H21.setBango47(CommonDtoUtil.strValToInt(kan13Info.getBango47()));
        // 認定項目4-8（落ち着きがない（家に帰る等）） */
        cpnTucGdl4Kan13H21.setBango48(CommonDtoUtil.strValToInt(kan13Info.getBango48()));
        // 認定項目4-9（外に出たがり目が離せない） */
        cpnTucGdl4Kan13H21.setBango49(CommonDtoUtil.strValToInt(kan13Info.getBango49()));
        // 認定項目4-10（ものを集める無断でもってくる） */
        cpnTucGdl4Kan13H21.setBango410(CommonDtoUtil.strValToInt(kan13Info.getBango410()));
        // 認定項目4-11（物を壊す衣類を破く） */
        cpnTucGdl4Kan13H21.setBango411(CommonDtoUtil.strValToInt(kan13Info.getBango411()));
        // 認定項目4-12（ひどい物忘れ） */
        cpnTucGdl4Kan13H21.setBango412(CommonDtoUtil.strValToInt(kan13Info.getBango412()));
        // 認定項目4-13（独り言や独り笑い） */
        cpnTucGdl4Kan13H21.setBango413(CommonDtoUtil.strValToInt(kan13Info.getBango413()));
        // 認定項目4-14（自分勝手な行動） */
        cpnTucGdl4Kan13H21.setBango414(CommonDtoUtil.strValToInt(kan13Info.getBango414()));
        // 認定項目4-15（話がまとまらない会話にならない） */
        cpnTucGdl4Kan13H21.setBango415(CommonDtoUtil.strValToInt(kan13Info.getBango415()));
        // 認定項目4-16（幻視幻聴） */
        cpnTucGdl4Kan13H21.setBango416(CommonDtoUtil.strValToInt(kan13Info.getBango416()));
        // 認定項目4-17（暴言暴力） */
        cpnTucGdl4Kan13H21.setBango417(CommonDtoUtil.strValToInt(kan13Info.getBango417()));
        // 認定項目4-18（目的なく動き回る） */
        cpnTucGdl4Kan13H21.setBango418(CommonDtoUtil.strValToInt(kan13Info.getBango418()));
        // 認定項目4-19（火の始末管理） */
        cpnTucGdl4Kan13H21.setBango419(CommonDtoUtil.strValToInt(kan13Info.getBango419()));
        // 認定項目4-20（不潔行為） */
        cpnTucGdl4Kan13H21.setBango420(CommonDtoUtil.strValToInt(kan13Info.getBango420()));
        // 認定項目4-21（異食行動） */
        cpnTucGdl4Kan13H21.setBango421(CommonDtoUtil.strValToInt(kan13Info.getBango421()));
        // 家族からの情報と観察 */
        cpnTucGdl4Kan13H21.setKazokuJouhouKnj(kan13Info.getKazokuJouhouKnj());
        // 援助の現状（家族） */
        cpnTucGdl4Kan13H21.setFamMemoKnj(kan13Info.getFamMemoKnj());
        // 援助の現状（サービス） */
        cpnTucGdl4Kan13H21.setSerMemoKnj(kan13Info.getSerMemoKnj());
        // 援助の希望（本人） */
        cpnTucGdl4Kan13H21.setKiboMemoKnj(kan13Info.getKiboMemoKnj());
        // 援助の希望（家族） */
        cpnTucGdl4Kan13H21.setKiboFamMemoKnj(kan13Info.getKiboFamMemoKnj());
        // 援助の計画 */
        cpnTucGdl4Kan13H21.setKeikakuMemoKnj(kan13Info.getKeikakuMemoKnj());
        // 特記解決すべき課題など */
        cpnTucGdl4Kan13H21.setMemo1Knj(kan13Info.getMemo1Knj());

        return cpnTucGdl4Kan13H21;

    }

    /**
     * ＧＬ＿③認知機能・④精神・行動障害（R３改訂）テーブル（cpn_tuc_gdl5_kan13_r3）の登録詳細パラメータを作成
     * 
     * @param kan13Info ③認知機能・④精神・行動障害情報
     * @return ＧＬ＿③認知機能・④精神・行動障害（R３改訂）テーブル（cpn_tuc_gdl5_kan13_r3）の登録詳細パラメータ
     */
    private CpnTucGdl5Kan13R3 setCpnTucGdl5Kan13R3(Gui00801Kan13Info kan13Info) {
        CpnTucGdl5Kan13R3 cpnTucGdl5Kan13R3 = new CpnTucGdl5Kan13R3();

        // 認定項目3-1（意思の伝達） */
        cpnTucGdl5Kan13R3.setBango31(CommonDtoUtil.strValToInt(kan13Info.getBango31()));
        // 認定項目3-2（毎日の日課を理解する） */
        cpnTucGdl5Kan13R3.setBango32(CommonDtoUtil.strValToInt(kan13Info.getBango32()));
        // 認定項目3-3（生年月日や年齢を答える） */
        cpnTucGdl5Kan13R3.setBango33(CommonDtoUtil.strValToInt(kan13Info.getBango33()));
        // 認定項目3-4（面接調査の直前記憶） */
        cpnTucGdl5Kan13R3.setBango34(CommonDtoUtil.strValToInt(kan13Info.getBango34()));
        // 認定項目3-5（自分の名前を答える） */
        cpnTucGdl5Kan13R3.setBango35(CommonDtoUtil.strValToInt(kan13Info.getBango35()));
        // 認定項目3-6（今の季節を理解する） */
        cpnTucGdl5Kan13R3.setBango36(CommonDtoUtil.strValToInt(kan13Info.getBango36()));
        // 認定項目3-7（自分のいる場所を答える） */
        cpnTucGdl5Kan13R3.setBango37(CommonDtoUtil.strValToInt(kan13Info.getBango37()));
        // 認定項目3-8（徘徊） */
        cpnTucGdl5Kan13R3.setBango38(CommonDtoUtil.strValToInt(kan13Info.getBango38()));
        // 認定項目3-9（外出すると戻れない（迷子）） */
        cpnTucGdl5Kan13R3.setBango39(CommonDtoUtil.strValToInt(kan13Info.getBango39()));
        // 認定項目3-10（介護者との意思伝達） */
        cpnTucGdl5Kan13R3.setBango310(CommonDtoUtil.strValToInt(kan13Info.getBango310()));
        // 認定項目4-1（被害妄想（物を盗られたなど）） */
        cpnTucGdl5Kan13R3.setBango41(CommonDtoUtil.strValToInt(kan13Info.getBango41()));
        // 認定項目4-2（作話をする） */
        cpnTucGdl5Kan13R3.setBango42(CommonDtoUtil.strValToInt(kan13Info.getBango42()));
        // 認定項目4-3（感情が不安定になる） */
        cpnTucGdl5Kan13R3.setBango43(CommonDtoUtil.strValToInt(kan13Info.getBango43()));
        // 認定項目4-4（昼夜の逆転） */
        cpnTucGdl5Kan13R3.setBango44(CommonDtoUtil.strValToInt(kan13Info.getBango44()));
        // 認定項目4-5（しつこく同じ話をする） */
        cpnTucGdl5Kan13R3.setBango45(CommonDtoUtil.strValToInt(kan13Info.getBango45()));
        // 認定項目4-6（大声を出す） */
        cpnTucGdl5Kan13R3.setBango46(CommonDtoUtil.strValToInt(kan13Info.getBango46()));
        // 認定項目4-7（介護に抵抗する） */
        cpnTucGdl5Kan13R3.setBango47(CommonDtoUtil.strValToInt(kan13Info.getBango47()));
        // 認定項目4-8（落ち着きがない（家に帰る等）） */
        cpnTucGdl5Kan13R3.setBango48(CommonDtoUtil.strValToInt(kan13Info.getBango48()));
        // 認定項目4-9（外に出たがり目が離せない） */
        cpnTucGdl5Kan13R3.setBango49(CommonDtoUtil.strValToInt(kan13Info.getBango49()));
        // 認定項目4-10（ものを集める無断でもってくる） */
        cpnTucGdl5Kan13R3.setBango410(CommonDtoUtil.strValToInt(kan13Info.getBango410()));
        // 認定項目4-11（物を壊す衣類を破く） */
        cpnTucGdl5Kan13R3.setBango411(CommonDtoUtil.strValToInt(kan13Info.getBango411()));
        // 認定項目4-12（ひどい物忘れ） */
        cpnTucGdl5Kan13R3.setBango412(CommonDtoUtil.strValToInt(kan13Info.getBango412()));
        // 認定項目4-13（独り言や独り笑い） */
        cpnTucGdl5Kan13R3.setBango413(CommonDtoUtil.strValToInt(kan13Info.getBango413()));
        // 認定項目4-14（自分勝手な行動） */
        cpnTucGdl5Kan13R3.setBango414(CommonDtoUtil.strValToInt(kan13Info.getBango414()));
        // 認定項目4-15（話がまとまらない会話にならない） */
        cpnTucGdl5Kan13R3.setBango415(CommonDtoUtil.strValToInt(kan13Info.getBango415()));
        // 認定項目4-16（幻視幻聴） */
        cpnTucGdl5Kan13R3.setBango416(CommonDtoUtil.strValToInt(kan13Info.getBango416()));
        // 認定項目4-17（暴言暴力） */
        cpnTucGdl5Kan13R3.setBango417(CommonDtoUtil.strValToInt(kan13Info.getBango417()));
        // 認定項目4-18（目的なく動き回る） */
        cpnTucGdl5Kan13R3.setBango418(CommonDtoUtil.strValToInt(kan13Info.getBango418()));
        // 認定項目4-19（火の始末管理） */
        cpnTucGdl5Kan13R3.setBango419(CommonDtoUtil.strValToInt(kan13Info.getBango419()));
        // 認定項目4-20（不潔行為） */
        cpnTucGdl5Kan13R3.setBango420(CommonDtoUtil.strValToInt(kan13Info.getBango420()));
        // 認定項目4-21（異食行動） */
        cpnTucGdl5Kan13R3.setBango421(CommonDtoUtil.strValToInt(kan13Info.getBango421()));
        // 家族からの情報と観察 */
        cpnTucGdl5Kan13R3.setKazokuJouhouKnj(kan13Info.getKazokuJouhouKnj());
        // 援助の現状（家族） */
        cpnTucGdl5Kan13R3.setFamMemoKnj(kan13Info.getFamMemoKnj());
        // 援助の現状（サービス） */
        cpnTucGdl5Kan13R3.setSerMemoKnj(kan13Info.getSerMemoKnj());
        // 援助の希望（本人） */
        cpnTucGdl5Kan13R3.setKiboMemoKnj(kan13Info.getKiboMemoKnj());
        // 援助の希望（家族） */
        cpnTucGdl5Kan13R3.setKiboFamMemoKnj(kan13Info.getKiboFamMemoKnj());
        // 援助の計画 */
        cpnTucGdl5Kan13R3.setKeikakuMemoKnj(kan13Info.getKeikakuMemoKnj());
        // 特記解決すべき課題など */
        cpnTucGdl5Kan13R3.setMemo1Knj(kan13Info.getMemo1Knj());

        return cpnTucGdl5Kan13R3;

    }

}
