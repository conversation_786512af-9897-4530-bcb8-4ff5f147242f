package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.SvJigyoInfoLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.IntegratedPlanMasterInitSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.IntegratedPlanMasterInitSelectServiceOutDto;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * GUI00970_総合計画マスタ 初期情報取得
 *
 * <AUTHOR>
 */
@Service
public class IntegratedPlanMasterInitSelectServiceImpl
        extends
        SelectServiceImpl<IntegratedPlanMasterInitSelectServiceInDto, IntegratedPlanMasterInitSelectServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;
    @Autowired
    private SvJigyoInfoLogic svJigyoInfoLogic;

    /**
     * 総合計画マスタ初期情報を取得する。
     * 
     * @param inDto 総合計画マスタ初期情報取得サービス入力Dto
     * @return 総合計画マスタ初期情報取得の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected IntegratedPlanMasterInitSelectServiceOutDto mainProcess(IntegratedPlanMasterInitSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        IntegratedPlanMasterInitSelectServiceOutDto outDto = new IntegratedPlanMasterInitSelectServiceOutDto();
        // 単項目チェック以外の入力チェック 特になし

        // レスポンス項目に初期値を設定する
        outDto.setYokaigo(CommonConstants.YOKAI_KBN_2);
        // outDto.setYokaigoModifiedCnt(String.valueOf(CommonConstants.NUMBER_ZERO));
        // 総合計画マスタ情報取得
        KrkSsmInfoByCriteriaInEntity inEntity = new KrkSsmInfoByCriteriaInEntity();
        inEntity.setSvJigyoId(Integer.valueOf(inDto.getSvJigyoId()));
        inEntity.setShisetuId(Integer.valueOf(inDto.getShisetuId()));
        inEntity.setBunrui1Id(CommonConstants.BUNRUI1_ID_2);
        inEntity.setBunrui2Id(CommonConstants.BUNRUI2_ID_13);
        List<KrkSsmInfoOutEntity> list = kghMocKrkSsmSelectMapper.findKrkSsmInfoByCriteria(inEntity);
        if (!list.isEmpty() && list.size() > 0) {
            for (KrkSsmInfoOutEntity entity : list) {
                if (CommonConstants.NUMBER_ONE.equals(entity.getBunrui3Id())) {
                    outDto.setYokaigo(String.valueOf(entity.getIntValue()));
                    // outDto.setYokaigoModifiedCnt(String.valueOf(entity.getModifiedCnt()));
                    break;
                }
            }
        }
        // リクエストパラメータ.適用事業所IDリストの事業所名取得
        outDto.setSvJigyoListInfo(svJigyoInfoLogic.getSvJigyoInfoList(inDto.getSvJigyoIdList()));
        LOG.info(Constants.END);
        return outDto;
    }
}
