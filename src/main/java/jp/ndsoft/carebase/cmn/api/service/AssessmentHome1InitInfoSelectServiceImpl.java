package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.List;
import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Gdl4FaceH21Info;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Gdl4FaceH30Info;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Gdl5FaceInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794NewInfo;
import jp.ndsoft.carebase.cmn.api.logic.AssessmentHomeLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHome1InitInfoSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHome1InitInfoSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4FaceH21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4FaceH21InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4FaceH21InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucGdl4FaceH21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass1R3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Gdl5Ass1R3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPrnPreSrw2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl4FaceH21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdl5FaceR3SelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.04.17
 * <AUTHOR>
 * @apiNote GUI00794_［アセスメント］画面（居宅）（1） 初期情報取得
 */
@Service
public class AssessmentHome1InitInfoSelectServiceImpl
        extends
        SelectServiceImpl<AssessmentHome1InitInfoSelectServiceInDto, AssessmentHome1InitInfoSelectServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** ＧＬ＿フェースシート（Ｈ２１改訂）一覧情報取得 */
    @Autowired
    private CpnTucGdl4FaceH21SelectMapper cpnTucGdl4FaceH21SelectMapper;

    /** GL＿フェースシート（R3改訂）情報取得 */
    @Autowired
    private CpnTucGdl5FaceR3SelectMapper cpnTucGdl5FaceR3SelectMapper;

    /** 相談受付者情報取得 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /** 相談受付者情報取得 */
    @Autowired
    private AssessmentHomeLogic assessmentHomeLogic;

    /**
     * 初期情報取得
     * 
     * @param inDto 初期情報取得の入力DTO.
     * @return 初期情報取得OUT DTO
     * @throws Exception Exception
     */
    @Override
    protected AssessmentHome1InitInfoSelectServiceOutDto mainProcess(
            AssessmentHome1InitInfoSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // 戻り情報を設定
        AssessmentHome1InitInfoSelectServiceOutDto out = new AssessmentHome1InitInfoSelectServiceOutDto();

        // フェースシート（Ｈ30)情報
        Gui00794Gdl4FaceH30Info gdl4FaceH30Info = new Gui00794Gdl4FaceH30Info();
        // フェースシート（Ｈ21)情報
        Gui00794Gdl4FaceH21Info gdl4FaceH21Info = new Gui00794Gdl4FaceH21Info();

        // フェースシート（R3）情報
        Gui00794Gdl5FaceInfo gdl5FaceInfo = new Gui00794Gdl5FaceInfo();

        // 相談受付者情報
        KghCpnRaiMonKentPrnPreSrw2OutEntity kghCpnRaiMonKentPrnPreSrw2 = new KghCpnRaiMonKentPrnPreSrw2OutEntity();

        // 新規表示情報
        Gui00794NewInfo gui00794NewInfo = new Gui00794NewInfo();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        /*
         * ===============2. フェースシート情報を取得===============
         * 
         */
        if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {
            // 2.1. リクエストパラメータ.改定フラグが4「H21/４改訂版」の場合、フェースシート情報を取得する。
            if (CommonConstants.CREATION_DATE_20180401.compareTo(inDto.getKijunbiYmd()) <= 0) {
                // 2.1.1.リクエストパラメータ.作成日≧「2018/04/01」の場合、
                // ＧＬ＿フェースシート（Ｈ２１改訂）一覧情報取得のDAOを利用し、フェースシート情報を取得する。
                CpnTucGdl4FaceH21ByCriteriaInEntity cpnTucGdl4FaceH21ByCriteriaEntity = new CpnTucGdl4FaceH21ByCriteriaInEntity();
                // アセスメントID
                cpnTucGdl4FaceH21ByCriteriaEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
                // 計画期間ID
                cpnTucGdl4FaceH21ByCriteriaEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

                List<CpnTucGdl4FaceH21OutEntity> cpnTucGdl4FaceH21tList = this.cpnTucGdl4FaceH21SelectMapper
                        .findCpnTucGdl4FaceH21ByCriteria(cpnTucGdl4FaceH21ByCriteriaEntity);
                if (cpnTucGdl4FaceH21tList != null && cpnTucGdl4FaceH21tList.size() > 0) {
                    // フェースシート（Ｈ30)情報の編集
                    // dmyGet2Ymd */
                    gdl4FaceH30Info.setDmyGet2Ymd(cpnTucGdl4FaceH21tList.get(0).getDmyGet2Ymd());
                    // dmyGet1Ymd */
                    gdl4FaceH30Info.setDmyGet1Ymd(cpnTucGdl4FaceH21tList.get(0).getDmyGet1Ymd());
                    // dmyGet3Ymd */
                    gdl4FaceH30Info.setDmyGet3Ymd(cpnTucGdl4FaceH21tList.get(0).getDmyGet3Ymd());
                    // dmyNinteiYmd */
                    gdl4FaceH30Info.setDmyNinteiYmd(cpnTucGdl4FaceH21tList.get(0).getDmyNinteiYmd());
                    // dmyUserNameKnj */
                    gdl4FaceH30Info.setDmyUserNameKnj(cpnTucGdl4FaceH21tList.get(0).getDmyUserNameKnj());
                    // dmyUserZip */
                    gdl4FaceH30Info.setDmyUserZip(cpnTucGdl4FaceH21tList.get(0).getDmyUserZip());
                    // dmyUserTel */
                    gdl4FaceH30Info.setDmyUserTel(cpnTucGdl4FaceH21tList.get(0).getDmyUserTel());
                    // dmyUserBirthday */
                    gdl4FaceH30Info.setDmyUserBirthday(cpnTucGdl4FaceH21tList.get(0).getDmyUserBirthday());
                    // dmyUserAge */
                    gdl4FaceH30Info.setDmyUserAge(cpnTucGdl4FaceH21tList.get(0).getDmyUserAge());
                    // dmyRequestDateYmd */
                    gdl4FaceH30Info.setDmyRequestDateYmd(cpnTucGdl4FaceH21tList.get(0).getDmyRequestDateYmd());
                    // dmyAdl1DateYmd */
                    gdl4FaceH30Info.setDmyAdl1DateYmd(cpnTucGdl4FaceH21tList.get(0).getDmyAdl1DateYmd());
                    // dmyAdl2DateYmd */
                    gdl4FaceH30Info.setDmyAdl2DateYmd(cpnTucGdl4FaceH21tList.get(0).getDmyAdl2DateYmd());
                    // dmySoudanUketukeYmd */
                    gdl4FaceH30Info.setDmySoudanUketukeYmd(cpnTucGdl4FaceH21tList.get(0).getDmySoudanUketukeYmd());
                    // dmyUserAddKnj */
                    gdl4FaceH30Info.setDmyUserAddKnj(cpnTucGdl4FaceH21tList.get(0).getDmyUserAddKnj());
                    // dmyUserSex */
                    gdl4FaceH30Info
                            .setDmyUserSex(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getDmyUserSex()));
                    // アセスメントID */
                    gdl4FaceH30Info.setGdlId(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getGdlId()));
                    // 計画期間ID */
                    gdl4FaceH30Info.setSc1Id(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getSc1Id()));
                    // 相談履歴コード */
                    gdl4FaceH30Info
                            .setSdRirekiCd(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getSdRirekiCd()));
                    // 関係者連番 */
                    gdl4FaceH30Info.setKid(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getKId()));
                    // 連絡先氏名 */
                    gdl4FaceH30Info.setKnameKnj(cpnTucGdl4FaceH21tList.get(0).getKNameKnj());
                    // 連絡先性別 */
                    gdl4FaceH30Info.setKsex(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getKSex()));
                    // 連絡先年齢 */
                    gdl4FaceH30Info
                            .setKnenrei(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getKNenrei()));
                    // 連絡先続柄 */
                    gdl4FaceH30Info.setKzcode(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getKZcode()));
                    // 連絡先住所 */
                    gdl4FaceH30Info.setKaddressKnj(cpnTucGdl4FaceH21tList.get(0).getKAddressKnj());
                    // 連絡先TEL */
                    gdl4FaceH30Info.setKtel(cpnTucGdl4FaceH21tList.get(0).getKTel());
                    // 連絡先TEL（携帯） */
                    gdl4FaceH30Info.setKkeitaitel(cpnTucGdl4FaceH21tList.get(0).getKKeitaitel());
                    // 相談者続柄 */
                    gdl4FaceH30Info
                            .setSdZcode(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getSdZcode()));
                    // 相談者住所 */
                    gdl4FaceH30Info.setSodanshaAddKnj(cpnTucGdl4FaceH21tList.get(0).getSodanshaAddKnj());
                    // 依頼年月日 */
                    gdl4FaceH30Info.setRequestDateYmd(cpnTucGdl4FaceH21tList.get(0).getRequestDateYmd());
                    // 寝たきり度判定日 */
                    gdl4FaceH30Info.setAdl1DateYmd(cpnTucGdl4FaceH21tList.get(0).getAdl1DateYmd());
                    // 認知症判定日 */
                    gdl4FaceH30Info.setAdl2DateYmd(cpnTucGdl4FaceH21tList.get(0).getAdl2DateYmd());
                    // 手帳1（身障） */
                    gdl4FaceH30Info
                            .setTecho1Cd(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getTecho1Cd()));
                    // 手帳2（療育） */
                    gdl4FaceH30Info
                            .setTecho2Cd(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getTecho2Cd()));
                    // 手帳3（精保） */
                    gdl4FaceH30Info
                            .setTecho3Cd(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getTecho3Cd()));
                    // 備考1 */
                    gdl4FaceH30Info.setMemo1Knj(cpnTucGdl4FaceH21tList.get(0).getMemo1Knj());
                    // 備考2 */
                    gdl4FaceH30Info.setMemo2Knj(cpnTucGdl4FaceH21tList.get(0).getMemo2Knj());
                    // 高額介護サービス費該当 */
                    gdl4FaceH30Info.setKkaigo(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getKKaigo()));
                    // 相談内容（本人） */
                    gdl4FaceH30Info.setSoudanNaiyouKnj(cpnTucGdl4FaceH21tList.get(0).getSoudanNaiyouKnj());
                    // 相談内容2（家族及び介護者） */
                    gdl4FaceH30Info.setSoudanNaiyou2Knj(cpnTucGdl4FaceH21tList.get(0).getSoudanNaiyou2Knj());
                    // 生活の経過 */
                    gdl4FaceH30Info.setSeikatuKeikaKnj(cpnTucGdl4FaceH21tList.get(0).getSeikatuKeikaKnj());
                    // 支援費受給の有無 */
                    gdl4FaceH30Info.setSienJukyuUmu(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getSienJukyuUmu()));
                    // 支援費受給の区分 */
                    gdl4FaceH30Info.setSienJukyuKubunKnj(cpnTucGdl4FaceH21tList.get(0).getSienJukyuKubunKnj());
                    // 相談形態 */
                    gdl4FaceH30Info.setSoudanKeitai(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getSoudanKeitai()));
                    // 相談形態その他 */
                    gdl4FaceH30Info.setKeitaiSonotaKnj(cpnTucGdl4FaceH21tList.get(0).getKeitaiSonotaKnj());
                    // 相談受付者ID */
                    gdl4FaceH30Info.setSoudanTantoId(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getSoudanTantoId()));
                    // 相談受付日 */
                    gdl4FaceH30Info.setSoudanUketukeYmd(cpnTucGdl4FaceH21tList.get(0).getSoudanUketukeYmd());
                    // 相談者氏名 */
                    gdl4FaceH30Info.setAdName2Knj(cpnTucGdl4FaceH21tList.get(0).getAdName2Knj());
                    // 相談者性別 */
                    gdl4FaceH30Info.setSdSex2(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getSdSex2()));
                    // 相談者生年月日 */
                    gdl4FaceH30Info.setSdBirth2(cpnTucGdl4FaceH21tList.get(0).getSdBirth2());
                    // 相談者電話番号 */
                    gdl4FaceH30Info.setSdTel2(cpnTucGdl4FaceH21tList.get(0).getSdTel2());
                    // 相談者電話番号（携帯） */
                    gdl4FaceH30Info.setSdKeitaitel2(cpnTucGdl4FaceH21tList.get(0).getSdKeitaitel2());
                    // 相談経路 */
                    gdl4FaceH30Info.setKeiro2Knj(cpnTucGdl4FaceH21tList.get(0).getKeiro2Knj());
                    // 相談者年齢 */
                    gdl4FaceH30Info
                            .setSdNenrei2(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getSdNenrei2()));
                    // 身障種別 */
                    gdl4FaceH30Info.setSinshouShu(cpnTucGdl4FaceH21tList.get(0).getSinshouShu());
                    // 認定済要介護認定 */
                    gdl4FaceH30Info
                            .setYokaiKbn1(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getYokaiKbn1()));
                    // 見込み要介護認定 */
                    gdl4FaceH30Info
                            .setYokaiKbn2(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getYokaiKbn2()));
                    // 要介護認定日 */
                    gdl4FaceH30Info.setNinteiYmd(cpnTucGdl4FaceH21tList.get(0).getNinteiYmd());
                    // 寝たきり度 */
                    gdl4FaceH30Info.setAdl1(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getAdl1()));
                    // 認知症度 */
                    gdl4FaceH30Info.setAdl2(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getAdl2()));
                    // 寝たきり度判定者 */
                    gdl4FaceH30Info.setAdl1TantoKnj(cpnTucGdl4FaceH21tList.get(0).getAdl1TantoKnj());
                    // 認知症度判定者 */
                    gdl4FaceH30Info.setAdl2TantoKnj(cpnTucGdl4FaceH21tList.get(0).getAdl2TantoKnj());
                    // 寝たきり度判定病院 */
                    gdl4FaceH30Info.setAdl1HospKnj(cpnTucGdl4FaceH21tList.get(0).getAdl1HospKnj());
                    // 認知症度判定病院 */
                    gdl4FaceH30Info.setAdl2HospKnj(cpnTucGdl4FaceH21tList.get(0).getAdl2HospKnj());
                    // 身障手帳有り無し */
                    gdl4FaceH30Info
                            .setTechoUmu1(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getTechoUmu1()));
                    // 療育手帳有り無し */
                    gdl4FaceH30Info
                            .setTechoUmu2(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getTechoUmu2()));
                    // 精神手帳有り無し */
                    gdl4FaceH30Info
                            .setTechoUmu3(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getTechoUmu3()));
                    // 身障種別 */
                    gdl4FaceH30Info
                            .setTkindCd(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getTKindCd()));
                    // 身障等級 */
                    gdl4FaceH30Info
                            .setTtokyu1(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getTTokyu1()));
                    // 療育手帳程度 */
                    gdl4FaceH30Info
                            .setTtokyu2(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getTTokyu2()));
                    // 精神手帳等級 */
                    gdl4FaceH30Info
                            .setTtokyu3(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getTTokyu3()));
                    // 身障手帳備考 */
                    gdl4FaceH30Info.setBiko1Knj(cpnTucGdl4FaceH21tList.get(0).getBiko1Knj());
                    // 療育手帳備考 */
                    gdl4FaceH30Info.setBiko2Knj(cpnTucGdl4FaceH21tList.get(0).getBiko2Knj());
                    // 精神手帳備考 */
                    gdl4FaceH30Info.setBiko3Knj(cpnTucGdl4FaceH21tList.get(0).getBiko3Knj());
                    // 身障手帳交付日 */
                    gdl4FaceH30Info.setGet1Ymd(cpnTucGdl4FaceH21tList.get(0).getGet1Ymd());
                    // 療育手帳交付日 */
                    gdl4FaceH30Info.setGet2Ymd(cpnTucGdl4FaceH21tList.get(0).getGet2Ymd());
                    // 精神手帳交付日 */
                    gdl4FaceH30Info.setGet3Ymd(cpnTucGdl4FaceH21tList.get(0).getGet3Ymd());
                    // 自立支援医療受給者証の有無 */
                    gdl4FaceH30Info.setShogaiJukyuUmu(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getShogaiJukyuUmu()));
                    // 介護保険利用者負担割合 */
                    gdl4FaceH30Info.setHutanWariai(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getHutanWariai()));
                    // 後期高齢者医療保険一部負担金 */
                    gdl4FaceH30Info
                            .setHutanKin(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21tList.get(0).getHutanKin()));
                    // dmyJisshiShokaiYmd */
                    gdl4FaceH30Info.setDmyJisshiShokaiYmd(cpnTucGdl4FaceH21tList.get(0).getDmyJisshiShokaiYmd());
                    // アセスメント初回実施日 */
                    gdl4FaceH30Info.setJisshiShokaiYmd(cpnTucGdl4FaceH21tList.get(0).getJisshiShokaiYmd());

                }
            } else {
                // 2.1.2.
                // リクエストパラメータ.作成日＜「2018/04/01」の場合、下記のＧＬ＿フェースシート（Ｈ２１改訂）一覧情報取得のDAOを利用し、フェースシート情報を取得する。
                CpnTucGdl4FaceH21InfoByCriteriaInEntity cpnTucGdl4FaceH21InfoByCriteriaInEntity = new CpnTucGdl4FaceH21InfoByCriteriaInEntity();
                // アセスメントID
                cpnTucGdl4FaceH21InfoByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
                // 計画期間ID
                cpnTucGdl4FaceH21InfoByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

                List<CpnTucGdl4FaceH21InfoOutEntity> cpnTucGdl4FaceH21InfoList = this.cpnTucGdl4FaceH21SelectMapper
                        .findCpnTucGdl4FaceH21InfoByCriteria(cpnTucGdl4FaceH21InfoByCriteriaInEntity);
                if (cpnTucGdl4FaceH21InfoList != null && cpnTucGdl4FaceH21InfoList.size() > 0) {
                    // フェースシート（Ｈ21)情報の編集
                    // dmyGet2Ymd */
                    gdl4FaceH21Info.setDmyGet2Ymd(cpnTucGdl4FaceH21InfoList.get(0).getDmyGet2Ymd());
                    // dmyGet1Ymd */
                    gdl4FaceH21Info.setDmyGet1Ymd(cpnTucGdl4FaceH21InfoList.get(0).getDmyGet1Ymd());
                    // dmyGet3Ymd */
                    gdl4FaceH21Info.setDmyGet3Ymd(cpnTucGdl4FaceH21InfoList.get(0).getDmyGet3Ymd());
                    // dmyNinteiYmd */
                    gdl4FaceH21Info.setDmyNinteiYmd(cpnTucGdl4FaceH21InfoList.get(0).getDmyNinteiYmd());
                    // dmyUserNameKnj */
                    gdl4FaceH21Info.setDmyUserNameKnj(cpnTucGdl4FaceH21InfoList.get(0).getDmyUserNameKnj());
                    // dmyUserZip */
                    gdl4FaceH21Info.setDmyUserZip(cpnTucGdl4FaceH21InfoList.get(0).getDmyUserZip());
                    // dmyUserTel */
                    gdl4FaceH21Info.setDmyUserTel(cpnTucGdl4FaceH21InfoList.get(0).getDmyUserTel());
                    // dmyUserBirthday */
                    gdl4FaceH21Info.setDmyUserBirthday(cpnTucGdl4FaceH21InfoList.get(0).getDmyUserBirthday());
                    // dmyUserAge */
                    gdl4FaceH21Info.setDmyUserAge(cpnTucGdl4FaceH21InfoList.get(0).getDmyUserAge());
                    // dmyRequestDateYmd */
                    gdl4FaceH21Info.setDmyRequestDateYmd(cpnTucGdl4FaceH21InfoList.get(0).getDmyRequestDateYmd());
                    // dmyAdl1DateYmd */
                    gdl4FaceH21Info.setDmyAdl1DateYmd(cpnTucGdl4FaceH21InfoList.get(0).getDmyAdl1DateYmd());
                    // dmyAdl2DateYmd */
                    gdl4FaceH21Info.setDmyAdl2DateYmd(cpnTucGdl4FaceH21InfoList.get(0).getDmyAdl2DateYmd());
                    // dmySoudanUketukeYmd */
                    gdl4FaceH21Info.setDmySoudanUketukeYmd(cpnTucGdl4FaceH21InfoList.get(0).getDmySoudanUketukeYmd());
                    // dmyUserAddKnj */
                    gdl4FaceH21Info.setDmyUserAddKnj(cpnTucGdl4FaceH21InfoList.get(0).getDmyUserAddKnj());
                    // dmyUserSex */
                    gdl4FaceH21Info.setDmyUserSex(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getDmyUserSex()));
                    // アセスメントID */
                    gdl4FaceH21Info.setGdlId(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getGdlId()));
                    // 計画期間ID */
                    gdl4FaceH21Info.setSc1Id(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getSc1Id()));
                    // 相談履歴コード */
                    gdl4FaceH21Info.setSdRirekiCd(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getSdRirekiCd()));
                    // 関係者連番 */
                    gdl4FaceH21Info.setKid(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getKId()));
                    // 連絡先氏名 */
                    gdl4FaceH21Info.setKnameKnj(cpnTucGdl4FaceH21InfoList.get(0).getKNameKnj());
                    // 連絡先性別 */
                    gdl4FaceH21Info.setKsex(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getKSex()));
                    // 連絡先年齢 */
                    gdl4FaceH21Info
                            .setKnenrei(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getKNenrei()));
                    // 連絡先続柄 */
                    gdl4FaceH21Info
                            .setKzcode(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getKZcode()));
                    // 連絡先住所 */
                    gdl4FaceH21Info.setKaddressKnj(cpnTucGdl4FaceH21InfoList.get(0).getKAddressKnj());
                    // 連絡先TEL */
                    gdl4FaceH21Info.setKtel(cpnTucGdl4FaceH21InfoList.get(0).getKTel());
                    // 連絡先TEL（携帯） */
                    gdl4FaceH21Info.setKkeitaitel(cpnTucGdl4FaceH21InfoList.get(0).getKKeitaitel());
                    // 相談者続柄 */
                    gdl4FaceH21Info
                            .setSdZcode(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getSdZcode()));
                    // 相談者住所 */
                    gdl4FaceH21Info.setSodanshaAddKnj(cpnTucGdl4FaceH21InfoList.get(0).getSodanshaAddKnj());
                    // 依頼年月日 */
                    gdl4FaceH21Info.setRequestDateYmd(cpnTucGdl4FaceH21InfoList.get(0).getRequestDateYmd());
                    // 寝たきり度判定日 */
                    gdl4FaceH21Info.setAdl1DateYmd(cpnTucGdl4FaceH21InfoList.get(0).getAdl1DateYmd());
                    // 認知症判定日 */
                    gdl4FaceH21Info.setAdl2DateYmd(cpnTucGdl4FaceH21InfoList.get(0).getAdl2DateYmd());
                    // 手帳1（身障） */
                    gdl4FaceH21Info
                            .setTecho1Cd(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getTecho1Cd()));
                    // 手帳2（療育） */
                    gdl4FaceH21Info
                            .setTecho2Cd(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getTecho2Cd()));
                    // 手帳3（精保） */
                    gdl4FaceH21Info
                            .setTecho3Cd(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getTecho3Cd()));
                    // 備考1 */
                    gdl4FaceH21Info.setMemo1Knj(cpnTucGdl4FaceH21InfoList.get(0).getMemo1Knj());
                    // 備考2 */
                    gdl4FaceH21Info.setMemo2Knj(cpnTucGdl4FaceH21InfoList.get(0).getMemo2Knj());
                    // 高額介護サービス費該当 */
                    gdl4FaceH21Info
                            .setKkaigo(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getKKaigo()));
                    // 相談内容（本人） */
                    gdl4FaceH21Info.setSoudanNaiyouKnj(cpnTucGdl4FaceH21InfoList.get(0).getSoudanNaiyouKnj());
                    // 相談内容2（家族及び介護者） */
                    gdl4FaceH21Info.setSoudanNaiyou2Knj(cpnTucGdl4FaceH21InfoList.get(0).getSoudanNaiyou2Knj());
                    // 生活の経過 */
                    gdl4FaceH21Info.setSeikatuKeikaKnj(cpnTucGdl4FaceH21InfoList.get(0).getSeikatuKeikaKnj());
                    // 支援費受給の有無 */
                    gdl4FaceH21Info.setSienJukyuUmu(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getSienJukyuUmu()));
                    // 支援費受給の区分 */
                    gdl4FaceH21Info.setSienJukyuKubunKnj(cpnTucGdl4FaceH21InfoList.get(0).getSienJukyuKubunKnj());
                    // 相談形態 */
                    gdl4FaceH21Info.setSoudanKeitai(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getSoudanKeitai()));
                    // 相談形態その他 */
                    gdl4FaceH21Info.setKeitaiSonotaKnj(cpnTucGdl4FaceH21InfoList.get(0).getKeitaiSonotaKnj());
                    // 相談受付者 */
                    gdl4FaceH21Info.setSoudanTantoId(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getSoudanTantoId()));
                    // 相談受付日 */
                    gdl4FaceH21Info.setSoudanUketukeYmd(cpnTucGdl4FaceH21InfoList.get(0).getSoudanUketukeYmd());
                    // 相談者氏名 */
                    gdl4FaceH21Info.setAdName2Knj(cpnTucGdl4FaceH21InfoList.get(0).getAdName2Knj());
                    // 相談者性別 */
                    gdl4FaceH21Info.setSdSex2(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getSdSex2()));
                    // 相談者生年月日 */
                    gdl4FaceH21Info.setSdBirth2(cpnTucGdl4FaceH21InfoList.get(0).getSdBirth2());
                    // 相談者電話番号 */
                    gdl4FaceH21Info.setSdTel2(cpnTucGdl4FaceH21InfoList.get(0).getSdTel2());
                    // 相談者電話番号（携帯） */
                    gdl4FaceH21Info.setSdKeitaitel2(cpnTucGdl4FaceH21InfoList.get(0).getSdKeitaitel2());
                    // 相談経路 */
                    gdl4FaceH21Info.setKeiro2Knj(cpnTucGdl4FaceH21InfoList.get(0).getKeiro2Knj());
                    // 相談者年齢 */
                    gdl4FaceH21Info.setSdNenrei2(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getSdNenrei2()));
                    // 身障種別 */
                    gdl4FaceH21Info.setSinshouShu(cpnTucGdl4FaceH21InfoList.get(0).getSinshouShu());
                    // 認定済要介護認定 */
                    gdl4FaceH21Info.setYokaiKbn1(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getYokaiKbn1()));
                    // 見込み要介護認定 */
                    gdl4FaceH21Info.setYokaiKbn2(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getYokaiKbn2()));
                    // 要介護認定日 */
                    gdl4FaceH21Info.setNinteiYmd(cpnTucGdl4FaceH21InfoList.get(0).getNinteiYmd());
                    // 寝たきり度 */
                    gdl4FaceH21Info.setAdl1(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getAdl1()));
                    // 認知症度 */
                    gdl4FaceH21Info.setAdl2(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getAdl2()));
                    // 寝たきり度判定者 */
                    gdl4FaceH21Info.setAdl1TantoKnj(cpnTucGdl4FaceH21InfoList.get(0).getAdl1TantoKnj());
                    // 認知症度判定者 */
                    gdl4FaceH21Info.setAdl2TantoKnj(cpnTucGdl4FaceH21InfoList.get(0).getAdl2TantoKnj());
                    // 寝たきり度判定病院 */
                    gdl4FaceH21Info.setAdl1HospKnj(cpnTucGdl4FaceH21InfoList.get(0).getAdl1HospKnj());
                    // 認知症度判定病院 */
                    gdl4FaceH21Info.setAdl2HospKnj(cpnTucGdl4FaceH21InfoList.get(0).getAdl2HospKnj());
                    // 身障手帳有り無し */
                    gdl4FaceH21Info
                            .setTechoUmu1(
                                    CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getTechoUmu1()));
                    // 療育手帳有り無し */
                    gdl4FaceH21Info
                            .setTechoUmu2(
                                    CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getTechoUmu2()));
                    // 精神手帳有り無し */
                    gdl4FaceH21Info
                            .setTechoUmu3(
                                    CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getTechoUmu3()));
                    // 身障種別 */
                    gdl4FaceH21Info
                            .setTkindCd(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getTKindCd()));
                    // 身障等級 */
                    gdl4FaceH21Info
                            .setTtokyu1(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getTTokyu1()));
                    // 療育手帳程度 */
                    gdl4FaceH21Info
                            .setTtokyu2(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getTTokyu2()));
                    // 精神手帳等級 */
                    gdl4FaceH21Info
                            .setTtokyu3(CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getTTokyu3()));
                    // 身障手帳備考 */
                    gdl4FaceH21Info.setBiko1Knj(cpnTucGdl4FaceH21InfoList.get(0).getBiko1Knj());
                    // 療育手帳備考 */
                    gdl4FaceH21Info.setBiko2Knj(cpnTucGdl4FaceH21InfoList.get(0).getBiko2Knj());
                    // 精神手帳備考 */
                    gdl4FaceH21Info.setBiko3Knj(cpnTucGdl4FaceH21InfoList.get(0).getBiko3Knj());
                    // 身障手帳交付日 */
                    gdl4FaceH21Info.setGet1Ymd(cpnTucGdl4FaceH21InfoList.get(0).getGet1Ymd());
                    // 療育手帳交付日 */
                    gdl4FaceH21Info.setGet2Ymd(cpnTucGdl4FaceH21InfoList.get(0).getGet2Ymd());
                    // 精神手帳交付日 */
                    gdl4FaceH21Info.setGet3Ymd(cpnTucGdl4FaceH21InfoList.get(0).getGet3Ymd());
                    // 自立支援医療受給者証の有無 */
                    gdl4FaceH21Info.setShogaiJukyuUmu(
                            CommonDtoUtil.objValToString(cpnTucGdl4FaceH21InfoList.get(0).getShogaiJukyuUmu()));

                }
            }
        } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {
            // 2.2.リクエストパラメータ.改定フラグが5「R3/4改訂版」の場合、
            // 下記のGL＿フェースシート（R3改訂）情報取得のDAOを利用し

            Gdl5Ass1R3ByCriteriaInEntity gdl5Ass1R3ByCriteriaInEntity = new Gdl5Ass1R3ByCriteriaInEntity();
            // アセスメントID
            gdl5Ass1R3ByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            gdl5Ass1R3ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
            List<Gdl5Ass1R3OutEntity> gdl5Ass1R3OutList = cpnTucGdl5FaceR3SelectMapper
                    .findGdl5Ass1R3ByCriteria(gdl5Ass1R3ByCriteriaInEntity);
            if (Objects.nonNull(gdl5Ass1R3OutList) && gdl5Ass1R3OutList.size() > 0) {
                // フェースシート（R3）情報
                // dmyGet2Ymd
                gdl5FaceInfo.setDmyGet2Ymd(gdl5Ass1R3OutList.get(0).getDmyGet2Ymd());
                // dmyGet1Ymd
                gdl5FaceInfo.setDmyGet1Ymd(gdl5Ass1R3OutList.get(0).getDmyGet1Ymd());
                // dmyGet3Ymd
                gdl5FaceInfo.setDmyGet3Ymd(gdl5Ass1R3OutList.get(0).getDmyGet3Ymd());
                // dmyNinteiYmd
                gdl5FaceInfo.setDmyNinteiYmd(gdl5Ass1R3OutList.get(0).getDmyNinteiYmd());
                // dmyUserNameKnj
                gdl5FaceInfo.setDmyUserNameKnj(gdl5Ass1R3OutList.get(0).getDmyUserNameKnj());
                // dmyUserZip
                gdl5FaceInfo.setDmyUserZip(gdl5Ass1R3OutList.get(0).getDmyUserZip());
                // dmyUserTel
                gdl5FaceInfo.setDmyUserTel(gdl5Ass1R3OutList.get(0).getDmyUserTel());
                // dmyUserBirthday
                gdl5FaceInfo.setDmyUserBirthday(gdl5Ass1R3OutList.get(0).getDmyUserBirthday());
                // dmyUserAge
                gdl5FaceInfo.setDmyUserAge(gdl5Ass1R3OutList.get(0).getDmyUserAge());
                // dmyRequestDateYmd
                gdl5FaceInfo.setDmyRequestDateYmd(gdl5Ass1R3OutList.get(0).getDmyRequestDateYmd());
                // dmyAdl1DateYmd
                gdl5FaceInfo.setDmyAdl1DateYmd(gdl5Ass1R3OutList.get(0).getDmyAdl1DateYmd());
                // dmyAdl2DateYmd
                gdl5FaceInfo.setDmyAdl2DateYmd(gdl5Ass1R3OutList.get(0).getDmyAdl2DateYmd());
                // dmySoudanUketukeYmd
                gdl5FaceInfo.setDmySoudanUketukeYmd(gdl5Ass1R3OutList.get(0).getDmySoudanUketukeYmd());
                // dmyUserAddKnj
                gdl5FaceInfo.setDmyUserAddKnj(gdl5Ass1R3OutList.get(0).getDmyUserAddKnj());
                // dmyUserSex
                gdl5FaceInfo.setDmyUserSex(gdl5Ass1R3OutList.get(0).getDmyUserSex());
                // アセスメントID
                gdl5FaceInfo.setGdlId(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getGdlId()));
                // 計画期間ID
                gdl5FaceInfo.setSc1Id(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getSc1Id()));
                // 相談履歴コード
                gdl5FaceInfo.setSdRirekiCd(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getSdRirekiCd()));
                // 関係者連番
                gdl5FaceInfo.setKid(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getKId()));
                // 連絡先氏名
                gdl5FaceInfo.setKnameKnj(gdl5Ass1R3OutList.get(0).getKNameKnj());
                // 連絡先性別
                gdl5FaceInfo.setKsex(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getKSex()));
                // 連絡先年齢
                gdl5FaceInfo.setKnenrei(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getKNenrei()));
                // 連絡先続柄
                gdl5FaceInfo.setKzcode(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getKZcode()));
                // 連絡先住所
                gdl5FaceInfo.setKaddressKnj(gdl5Ass1R3OutList.get(0).getKAddressKnj());
                // 連絡先TEL
                gdl5FaceInfo.setKtel(gdl5Ass1R3OutList.get(0).getKTel());
                // 連絡先TEL（携帯）
                gdl5FaceInfo.setKkeitaitel(gdl5Ass1R3OutList.get(0).getKKeitaitel());
                // 相談者続柄
                gdl5FaceInfo.setSdZcode(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getSdZcode()));
                // 相談者住所
                gdl5FaceInfo.setSodanshaAddKnj(gdl5Ass1R3OutList.get(0).getSodanshaAddKnj());
                // 依頼年月日
                gdl5FaceInfo.setRequestDateYmd(gdl5Ass1R3OutList.get(0).getRequestDateYmd());
                // 寝たきり度判定日
                gdl5FaceInfo.setAdl1DateYmd(gdl5Ass1R3OutList.get(0).getAdl1DateYmd());
                // 認知症判定日
                gdl5FaceInfo.setAdl2DateYmd(gdl5Ass1R3OutList.get(0).getAdl2DateYmd());
                // 手帳1（身障）
                gdl5FaceInfo.setTecho1Cd(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getTecho1Cd()));
                // 手帳2（療育）
                gdl5FaceInfo.setTecho2Cd(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getTecho2Cd()));
                // 手帳3（精保）
                gdl5FaceInfo.setTecho3Cd(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getTecho3Cd()));
                // 備考1
                gdl5FaceInfo.setMemo1Knj(gdl5Ass1R3OutList.get(0).getMemo1Knj());
                // 備考2
                gdl5FaceInfo.setMemo2Knj(gdl5Ass1R3OutList.get(0).getMemo2Knj());
                // 高額介護サービス費該当
                gdl5FaceInfo.setKkaigo(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getKKaigo()));
                // 相談内容（本人）
                gdl5FaceInfo.setSoudanNaiyouKnj(gdl5Ass1R3OutList.get(0).getSoudanNaiyouKnj());
                // 相談内容2（家族及び介護者）
                gdl5FaceInfo.setSoudanNaiyou2Knj(gdl5Ass1R3OutList.get(0).getSoudanNaiyou2Knj());
                // 生活の経過
                gdl5FaceInfo.setSeikatuKeikaKnj(gdl5Ass1R3OutList.get(0).getSeikatuKeikaKnj());
                // 支援費受給の有無
                gdl5FaceInfo.setSienJukyuUmu(
                        CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getSienJukyuUmu()));
                // 支援費受給の区分
                gdl5FaceInfo.setSienJukyuKubunKnj(gdl5Ass1R3OutList.get(0).getSienJukyuKubunKnj());
                // 相談形態
                gdl5FaceInfo.setSoudanKeitai(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getSoudanKeitai()));
                // 相談形態その他
                gdl5FaceInfo.setKeitaiSonotaKnj(gdl5Ass1R3OutList.get(0).getKeitaiSonotaKnj());
                // 相談受付者ID
                gdl5FaceInfo
                        .setSoudanTantoId(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getSoudanTantoId()));
                // 相談受付日
                gdl5FaceInfo.setSoudanUketukeYmd(gdl5Ass1R3OutList.get(0).getSoudanUketukeYmd());
                // 相談者氏名
                gdl5FaceInfo.setAdName2Knj(gdl5Ass1R3OutList.get(0).getAdName2Knj());
                // 相談者性別
                gdl5FaceInfo.setSdSex2(
                        CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getSdSex2()));
                // 相談者生年月日
                gdl5FaceInfo.setSdBirth2(gdl5Ass1R3OutList.get(0).getSdBirth2());
                // 相談者電話番号
                gdl5FaceInfo.setSdTel2(gdl5Ass1R3OutList.get(0).getSdTel2());
                // 相談者電話番号（携帯）
                gdl5FaceInfo.setSdKeitaitel2(gdl5Ass1R3OutList.get(0).getSdKeitaitel2());
                // 相談経路
                gdl5FaceInfo.setKeiro2Knj(gdl5Ass1R3OutList.get(0).getKeiro2Knj());
                // 相談者年齢
                gdl5FaceInfo.setSdNenrei2(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getSdNenrei2()));
                // 身障種別
                gdl5FaceInfo.setSinshouShu(gdl5Ass1R3OutList.get(0).getSinshouShu());
                // 認定済要介護認定
                gdl5FaceInfo.setYokaiKbn1(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getYokaiKbn1()));
                // 見込み要介護認定
                gdl5FaceInfo.setYokaiKbn2(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getYokaiKbn2()));
                // 要介護認定日
                gdl5FaceInfo.setNinteiYmd(gdl5Ass1R3OutList.get(0).getNinteiYmd());
                // 寝たきり度
                gdl5FaceInfo.setAdl1(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getAdl1()));
                // 認知症度
                gdl5FaceInfo.setAdl2(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getAdl2()));
                // 寝たきり度判定者
                gdl5FaceInfo.setAdl1TantoKnj(gdl5Ass1R3OutList.get(0).getAdl1TantoKnj());
                // 認知症度判定者
                gdl5FaceInfo.setAdl2TantoKnj(gdl5Ass1R3OutList.get(0).getAdl2TantoKnj());
                // 寝たきり度判定病院
                gdl5FaceInfo.setAdl1HospKnj(gdl5Ass1R3OutList.get(0).getAdl1HospKnj());
                // 認知症度判定病院
                gdl5FaceInfo.setAdl2HospKnj(gdl5Ass1R3OutList.get(0).getAdl2HospKnj());
                // 身障手帳有り無し */
                gdl5FaceInfo
                        .setTechoUmu1(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getTechoUmu1()));
                // 療育手帳有り無し */
                gdl5FaceInfo
                        .setTechoUmu2(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getTechoUmu2()));
                // 精神手帳有り無し */
                gdl5FaceInfo
                        .setTechoUmu3(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getTechoUmu3()));
                // 身障種別 */
                gdl5FaceInfo
                        .setTkindCd(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getTKindCd()));
                // 身障等級 */
                gdl5FaceInfo
                        .setTtokyu1(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getTTokyu1()));
                // 療育手帳程度 */
                gdl5FaceInfo
                        .setTtokyu2(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getTTokyu2()));
                // 精神手帳等級 */
                gdl5FaceInfo
                        .setTtokyu3(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getTTokyu3()));
                // 身障手帳備考 */
                gdl5FaceInfo.setBiko1Knj(gdl5Ass1R3OutList.get(0).getBiko1Knj());
                // 療育手帳備考
                gdl5FaceInfo.setBiko2Knj(gdl5Ass1R3OutList.get(0).getBiko2Knj());
                // 精神手帳備考
                gdl5FaceInfo.setBiko3Knj(gdl5Ass1R3OutList.get(0).getBiko3Knj());
                // 身障手帳交付日
                gdl5FaceInfo.setGet1Ymd(gdl5Ass1R3OutList.get(0).getGet1Ymd());
                // 療育手帳交付日
                gdl5FaceInfo.setGet2Ymd(gdl5Ass1R3OutList.get(0).getGet2Ymd());
                // 精神手帳交付日
                gdl5FaceInfo.setGet3Ymd(gdl5Ass1R3OutList.get(0).getGet3Ymd());
                // 自立支援医療受給者証の有無
                gdl5FaceInfo.setShogaiJukyuUmu(
                        CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getShogaiJukyuUmu()));
                // 介護保険利用者負担割合
                gdl5FaceInfo.setHutanWariai(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getHutanWariai()));
                // 後期高齢者医療保険一部負担金
                gdl5FaceInfo.setHutanKin(CommonDtoUtil.objValToString(gdl5Ass1R3OutList.get(0).getHutanKin()));
                // dmyJisshiShokaiYmd
                gdl5FaceInfo.setDmyJisshiShokaiYmd(gdl5Ass1R3OutList.get(0).getDmyJisshiShokaiYmd());
                // アセスメント初回実施日
                gdl5FaceInfo.setJisshiShokaiYmd(gdl5Ass1R3OutList.get(0).getJisshiShokaiYmd());

            }
        }

        // 初期化設定
        String shokuinName = CommonConstants.BLANK_STRING;

        if (Objects.nonNull(gdl4FaceH30Info) && (Objects.nonNull(gdl4FaceH30Info.getSoudanTantoId()))) {
            // 3.1.【変数】.職員名＝2.で取得した相談受付者ID
            shokuinName = gdl4FaceH30Info.getSoudanTantoId();
            // 相談受付者情報を取得
            kghCpnRaiMonKentPrnPreSrw2 = this
                    .findKghCpnRaiMonKentPrnPreSrw2(CommonDtoUtil.strValToInt(gdl4FaceH30Info.getSoudanTantoId()));
            // 3.2.下記の職員基本取得DAOを利用し、相談受付者情報が取得できる場合、【変数】.職員名＝下記で取得した職員名
            if (Objects.nonNull(kghCpnRaiMonKentPrnPreSrw2)) {
                shokuinName = kghCpnRaiMonKentPrnPreSrw2.getShokuinName();
            }
        } else if (Objects.nonNull(gdl4FaceH21Info) && (Objects.nonNull(gdl4FaceH21Info.getSoudanTantoId()))) {
            // 3.1.【変数】.職員名＝2.で取得した相談受付者ID
            shokuinName = gdl4FaceH21Info.getSoudanTantoId();
            // 相談受付者情報を取得
            kghCpnRaiMonKentPrnPreSrw2 = this
                    .findKghCpnRaiMonKentPrnPreSrw2(CommonDtoUtil.strValToInt(gdl4FaceH21Info.getSoudanTantoId()));
            // 3.2.下記の職員基本取得DAOを利用し、相談受付者情報が取得できる場合、【変数】.職員名＝下記で取得した職員名
            if (Objects.nonNull(kghCpnRaiMonKentPrnPreSrw2)) {
                shokuinName = kghCpnRaiMonKentPrnPreSrw2.getShokuinName();
            }
        } else if (Objects.nonNull(gdl5FaceInfo) && (Objects.nonNull(gdl5FaceInfo.getSoudanTantoId()))) {
            // 3.1.【変数】.職員名＝2.で取得した相談受付者ID
            shokuinName = gdl5FaceInfo.getSoudanTantoId();
            // 相談受付者情報を取得
            kghCpnRaiMonKentPrnPreSrw2 = this
                    .findKghCpnRaiMonKentPrnPreSrw2(CommonDtoUtil.strValToInt(gdl5FaceInfo.getSoudanTantoId()));
            // 3.2.下記の職員基本取得DAOを利用し、相談受付者情報が取得できる場合、【変数】.職員名＝下記で取得した職員名
            if (Objects.nonNull(kghCpnRaiMonKentPrnPreSrw2)) {
                shokuinName = kghCpnRaiMonKentPrnPreSrw2.getShokuinName();
            }
        }
        if ((Objects.isNull(gdl4FaceH30Info.getSoudanTantoId()))
                && (Objects.isNull(gdl4FaceH21Info.getSoudanTantoId()))
                && (Objects.isNull(gdl5FaceInfo.getSoudanTantoId()))) {
            // GUI00794_［アセスメント］画面（居宅）（1）の「API定義書_APINo(104)_新規表示取得.xlsx」を参照
            gui00794NewInfo = assessmentHomeLogic.newInfoSub(inDto.getHoujinId(), inDto.getShisetuId(),
                    inDto.getSvJigyoId(), inDto.getUserid(), inDto.getKijunbiYmd());
        }

        // フェースシート（Ｈ30)情報
        out.setGdl4FaceH30Info(gdl4FaceH30Info);
        // フェースシート（Ｈ21)情報
        out.setGdl4FaceH21Info(gdl4FaceH21Info);
        // フェースシート（R3）情報
        out.setGdl5FaceInfo(gdl5FaceInfo);
        // 相談受付者名
        out.setShokuinName(shokuinName);
        // 新規表示情報
        out.setNewInfo(gui00794NewInfo);
        LOG.info(Constants.END);
        return out;
    }

    /**
     * 相談受付者情報
     * 
     * @param soudanTantoId 相談受付者ID
     * @return 相談受付者情報
     */
    private KghCpnRaiMonKentPrnPreSrw2OutEntity findKghCpnRaiMonKentPrnPreSrw2(Integer soudanTantoId) {
        KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity();
        KghCpnRaiMonKentPrnPreSrw2OutEntity kghCpnRaiMonKentPrnPreSrw2OutEntity = null;
        // 職員ID
        kghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity.setLlTmp(CommonDtoUtil.objValToString(soudanTantoId));
        List<KghCpnRaiMonKentPrnPreSrw2OutEntity> KghCpnRaiMonKentPrnPreSrw2List = comMscShokuinSelectMapper
                .findKghCpnRaiMonKentPrnPreSrw2ByCriteria(
                        kghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity);
        if (KghCpnRaiMonKentPrnPreSrw2List != null && KghCpnRaiMonKentPrnPreSrw2List.size() > 0) {
            // 相談受付者情報
            kghCpnRaiMonKentPrnPreSrw2OutEntity = KghCpnRaiMonKentPrnPreSrw2List.get(0);
        }
        return kghCpnRaiMonKentPrnPreSrw2OutEntity;
    }

}
