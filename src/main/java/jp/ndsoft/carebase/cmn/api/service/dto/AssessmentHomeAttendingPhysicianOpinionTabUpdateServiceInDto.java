package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00804DoctorOpinionInfo3;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00804DoctorOpinionInfo4;
import jp.ndsoft.carebase.cmn.api.logic.dto.KadaiInDto;
import jp.ndsoft.carebase.cmn.api.report.model.EdocumentDeleteReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.HomeAssessmentSheetAllReportParameterModel;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.04.08
 * <AUTHOR>
 * @apiNote GUI00804_［アセスメント］画面（居宅）（6医） データ保存 サービス入力Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;
    /** タブID */
    @NotEmpty
    private String tabId;

    /** 機能ID */
    @NotEmpty
    private String kinoId;

    /** 当履歴ページ番号 */
    @NotEmpty
    private String krirekiNo;

    /** e文書用パラメータ */
    @NotNull
    private HomeAssessmentSheetAllReportParameterModel edocumentUseParam;

    /** e文書削除用パラメータ */
    @NotNull
    private EdocumentDeleteReportParameterModel edocumentDeleteUseParam;

    /** 期間対象フラグ */
    @NotEmpty
    private String kikanFlg;

    /** 計画対象期間番号 */
    private String planningPeriodNo;

    /** 開始日 */
    private String startYmd;

    /** 終了日 */
    private String endYmd;

    /** ガイドラインまとめ */
    @NotEmpty
    private String matomeFlg;

    /** ログインID */
    @NotEmpty
    private String loginId;

    /** システム略称 */
    @NotEmpty
    private String sysRyaku;

    /** 職員ID */
    @NotEmpty
    private String shokuId;

    /** システムコード */
    @NotEmpty
    private String sysCd;

    /** 事業者名 */
    @NotEmpty
    private String svJigyoKnj;

    /** 作成者名 */
    @NotEmpty
    private String createUserName;

    /** 利用者名 */
    @NotEmpty
    private String userName;

    /** 法人ID */
    @NotEmpty
    private String houjinId;

    /** 施設ID */
    @NotEmpty
    private String shisetuId;

    /** 作成日 */
    @NotEmpty
    private String kijunbiYmd;

    /** 作成者ID */
    @NotEmpty
    private String sakuseiId;

    /** 利用者ID */
    @NotEmpty
    private String userId;

    /** 事業者ID */
    @NotEmpty
    private String svJigyoId;

    /** 計画対象期間ID */
    @NotEmpty
    private String sc1Id;

    /** アセスメントID */
    @NotEmpty
    private String raiId;

    /** 改訂フラグ */
    @NotEmpty
    private String ninteiFlg;

    /** 更新区分 */
    @NotEmpty
    private String updateKbn;

    /** 削除処理区分 */
    @NotEmpty
    private String deleteKbn;

    /** 履歴更新区分 */
    @NotEmpty
    private String historyUpdateKbn;

    /** 種別ID */
    @NotEmpty
    private String syubetsuId;

    /** 医師の意見情報（H21/4改訂版） */
    private Gui00804DoctorOpinionInfo3 doctorOpinionInfo3;

    /** 医師の意見情報（R3/4改訂版） */
    private Gui00804DoctorOpinionInfo4 doctorOpinionInfo4;

    /** 課題と目標情報リスト */
    private List<KadaiInDto> kadaiList;
}
