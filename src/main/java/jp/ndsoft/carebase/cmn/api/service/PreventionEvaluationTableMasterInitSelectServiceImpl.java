package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01241PreventionEvaluationTableMasterInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.PreventionEvaluationTableMasterInitSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PreventionEvaluationTableMasterInitSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import java.util.*;

/**
 * @since 2025.05.22
 * <AUTHOR>
 * @implNote GUI01241_予防評価表マスタデータ保存サービス
 * 
 */
@Service
public class PreventionEvaluationTableMasterInitSelectServiceImpl extends
        SelectServiceImpl<PreventionEvaluationTableMasterInitSelectServiceInDto, PreventionEvaluationTableMasterInitSelectServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 初期設定マスタ情報取得DAO */
    @Autowired
    private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;

    /**
     * 予防評価表マスタ初期情報取得
     * 
     * @param inDto 予防評価表マスタ初期情報取得サービス入力Dto
     * @return ［予防評価表マスタ初期情報取得サービス出力DTO
     */
    @Override
    protected PreventionEvaluationTableMasterInitSelectServiceOutDto mainProcess(
            final PreventionEvaluationTableMasterInitSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============予防評価表マスタ情報の取得。===============
         * 
         */
        // DAOを実行
        List<KrkSsmInfoOutEntity> krkSsmInfoList = this.selectKrkSsmInfo(inDto);
        /*
         * =============== 予防評価表マスタ編集。===============
         * 
         */

        LOG.info(Constants.END);
        return this.editKrkSsmInfo(krkSsmInfoList);

    }

    /**
     * 予防評価表マスタ情報を取得する。
     *
     * @param inDto アセスメント(パッケージプラン)の初期情報取得入力DTO
     * @return 予防評価表マスタ情報
     */
    private List<KrkSsmInfoOutEntity> selectKrkSsmInfo(PreventionEvaluationTableMasterInitSelectServiceInDto inDto) {
        KrkSsmInfoByCriteriaInEntity krkSsmInfoByCriteriaInEntity = new KrkSsmInfoByCriteriaInEntity();
        // 施設ID ← リクエストパラメータ.施設ID
        krkSsmInfoByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID ← リクエストパラメータ.事業所ID
        krkSsmInfoByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 分類1 ← 2
        krkSsmInfoByCriteriaInEntity.setBunrui1Id(CommonConstants.BUNRUI1_ID_2);
        // 分類2 ← 25
        krkSsmInfoByCriteriaInEntity.setBunrui2Id(CommonConstants.NUMBER_25);

        return this.kghMocKrkSsmSelectMapper
                .findKrkSsmInfoByCriteria(krkSsmInfoByCriteriaInEntity);
    }

    /**
     * 予防評価表マスタ情報を取得する。
     *
     * @param krkSsmInfoList 予防評価表マスタ情報
     * @return アセスメント(パッケージプラン)の初期情報取得出力DTO
     */
    private PreventionEvaluationTableMasterInitSelectServiceOutDto editKrkSsmInfo(
            List<KrkSsmInfoOutEntity> krkSsmInfoList) {
        /// 戻り値初期化
        PreventionEvaluationTableMasterInitSelectServiceOutDto outDto = new PreventionEvaluationTableMasterInitSelectServiceOutDto();
        // List<Gui01241PreventionEvaluationTableMasterInfo> preventionEvaluationTableMasterInfoList = new ArrayList<>();

        // if (CollectionUtils.isEmpty(krkSsmInfoList)) {
        //     // 3.1. 上記「2.1. 」で取得した予防評価表マスタ情報がNULLの場合、
        //     Gui01241PreventionEvaluationTableMasterInfo preventionEvaluationTableMasterInfo = new Gui01241PreventionEvaluationTableMasterInfo();
        //     // レスポンスパラメータ.分類3 = 2
        //     preventionEvaluationTableMasterInfo.setBunrui3Id(CommonDtoUtil.objValToString(CommonConstants.NUMBER_2));
        //     // レスポンスパラメータ.整数 = 2
        //     preventionEvaluationTableMasterInfo.setIntValue(CommonDtoUtil.objValToString(CommonConstants.NUMBER_2));
        //     preventionEvaluationTableMasterInfoList.add(preventionEvaluationTableMasterInfo);
        //     // ※返却する情報の編集要領は「レスポンスパラメータ詳細」を参照する。
        //     outDto.setPreventionEvaluationTableMasterInfoList(preventionEvaluationTableMasterInfoList);

        //     return outDto;
        // }
        Gui01241PreventionEvaluationTableMasterInfo preventionEvaluationTableMasterInfo = new Gui01241PreventionEvaluationTableMasterInfo();
        if(!krkSsmInfoList.isEmpty()) {
            // 3.2. 上記以外の場合、
             // 分類3
            KrkSsmInfoOutEntity krkSsmInfo = krkSsmInfoList.get(0);
            preventionEvaluationTableMasterInfo.setBunrui3Id(CommonDtoUtil.objValToString(krkSsmInfo.getBunrui3Id()));
            // 整数
            preventionEvaluationTableMasterInfo.setIntValue(CommonDtoUtil.objValToString(krkSsmInfo.getIntValue()));
        }
        // ※返却する情報の編集要領は「レスポンスパラメータ詳細」を参照する。
        outDto.setPreventionEvaluationTableMasterInfo(preventionEvaluationTableMasterInfo);
        return outDto;
    }
}
