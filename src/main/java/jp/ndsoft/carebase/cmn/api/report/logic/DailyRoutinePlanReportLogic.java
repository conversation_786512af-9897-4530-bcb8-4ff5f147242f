package jp.ndsoft.carebase.cmn.api.report.logic;

import java.io.FileInputStream;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.awt.Color;
import io.micrometer.common.util.StringUtils;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpU01Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.ShokuinInfoLogic;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.DailyRoutinePlanReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.DailyRoutinePlanReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.DailyRoutinePlanReportSvknjDto;
import jp.ndsoft.carebase.cmn.api.report.model.DailyRoutinePlanReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.service.ShoninSetReportService;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMucDayKyotusvInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMucDayKyotusvInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnNikka1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnNikka1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnNikka2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnNikka2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucDayKyotusvSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCnikka1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCnikka2SelectMapper;
import jp.ndsoft.smh.framework.properties.FrameworkProperties;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.design.JRDesignBand;
import net.sf.jasperreports.engine.design.JRDesignFrame;
import net.sf.jasperreports.engine.design.JRDesignStaticText;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.type.HorizontalTextAlignEnum;
import net.sf.jasperreports.engine.type.ModeEnum;
import net.sf.jasperreports.engine.type.StretchTypeEnum;
import net.sf.jasperreports.engine.xml.JRXmlLoader;

/**
 * U00861_日課計画表出力 ロジッククラス
 * 
 * <AUTHOR>
 */
@Component
public class DailyRoutinePlanReportLogic {

    // 28-11 日課計画表ヘッダ
    @Autowired
    private CpnTucCnikka1SelectMapper cpnTucCnikka1SelectMapper;

    // 28-12 日課計画表データ
    @Autowired
    private CpnTucCnikka2SelectMapper cpnTucCnikka2SelectMapper;

    // 日課表共通サービスマスタ情報取得
    @Autowired
    private CpnMucDayKyotusvSelectMapper cpnMucDayKyotusvSelectMapper;

    // 03-01 職員基本情報取得
    @Autowired
    private ShokuinInfoLogic shokuinInfoLogic;

    // 記入用シート印刷時、処理日から印刷日を返す
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    // 西暦日付を和暦日付に変換
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    // 利用者名を返す
    @Autowired
    private KghCmpU01Logic kghCmpU01Logic;

    // 承認欄設定
    @Autowired
    private ShoninSetReportService shoninSetReportService;

    /**
     * U00861_日課計画表の帳票パラメータ取得
     *
     * @param model 入力データ
     * @return PDF帳票パラメータ
     */
    public DailyRoutinePlanReportServiceInDto getU00861ReportParameters(DailyRoutinePlanReportParameterModel model,
            DailyRoutinePlanReportServiceOutDto outDto) {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));
        // 表示用データの取得
        DailyRoutinePlanReportServiceInDto inDto = new DailyRoutinePlanReportServiceInDto();

        // 職員ID
        Integer shokuId = CommonConstants.INT_0;
        // 作成日
        String createYmd = CommonConstants.EMPTY_STRING;
        // リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグが「0:印刷しない」 の場合、ヘッダ部情報を取得。
        if (ReportConstants.EMPTY_FLG_FALSE.equals(model.getDbNoSaveData().getEmptyFlg())) {
            // 日課計画表ヘッダ情報を取得する
            CpnNikka1ByCriteriaInEntity inEntity = new CpnNikka1ByCriteriaInEntity();
            // 計画期間ID
            inEntity.setSc1(CommonDtoUtil.strValToInt(model.getPrintSubjectHistory().getSc1Id()));
            // 日課計画ID
            inEntity.setNikka1(CommonDtoUtil.strValToInt(model.getPrintSubjectHistory().getRirekiId()));
            List<CpnNikka1OutEntity> outEntities = cpnTucCnikka1SelectMapper.findCpnNikka1ByCriteria(inEntity);
            List<CpnNikka2OutEntity> cpnNikka2OutEntities = new ArrayList<>();
            // 2.1.1. 上記ヘッダー情報を取得できた場合、下記項目を取得する
            if (outEntities.size() > 0) {
                // 作成日
                createYmd = outEntities.get(0).getCreateYmd();
                // 職員ID
                shokuId = outEntities.get(0).getShokuId();
                // その他サービス
                inDto.setSonotaSvKnj(outEntities.get(0).getSonotaSvKnj());

                // 2.1.2.下記共通ロジックのufGetNameメソッドを経由して、利用者名を取得する
                inDto.setUserName(kghCmpU01Logic
                        .ufGetName(CommonDtoUtil.strValToInt(model.getPrintSubjectHistory().getUserId())));

                // 2.1.3.下記共通ロジックのgetCmpYokaiメソッドを経由して、要介護度を取得する
                inDto.setYokaiKnj(kghCmpF01Logic.getCmpYokai(
                        CommonDtoUtil.strValToInt(model.getJigyoInfo().getSvJigyoId()),
                        CommonDtoUtil.strValToInt(model.getPrintSubjectHistory().getUserId()),
                        CommonDtoUtil.strValToInt(model.getPrintSet().getParam07()),
                        CommonDtoUtil.strValToInt(model.getPrintSubjectHistory().getSc1Id()), inDto.getCreateYmd()));

                // 2.1.5.下記の28-12 日課計画表データのDAOを利用し、日課計画表データの情報を取得する
                CpnNikka2ByCriteriaInEntity inEntity3 = new CpnNikka2ByCriteriaInEntity();
                // 日課計画ID
                inEntity3.setNikka1(CommonDtoUtil.strValToInt(model.getPrintSubjectHistory().getRirekiId()));
                List<CpnNikka2OutEntity> outEntities3 = cpnTucCnikka2SelectMapper.findCpnNikka2ByCriteria(inEntity3);
                // 2.1.5.1. 上記の処理「2.1.5.」で取得した日課計画表データの情報リスト件数分、下記処理を繰り返す。
                if (outEntities3.size() > 0) {
                    for (int i = 0; i < outEntities3.size(); i++) {
                        CpnNikka2OutEntity itemEntity = outEntities3.get(i);
                        // 日課表示テキストの生成
                        String memo = CommonConstants.EMPTY_STRING;
                        String memo2 = itemEntity.getNaiyoKnj();
                        if (StringUtils.isNotEmpty(memo)) {
                            if (StringUtils.isNotEmpty(memo2)) {
                                memo = memo + CommonConstants.STRING_COMMA + memo2;
                            }
                        } else {
                            memo = memo2;
                        }
                        itemEntity.setNaiyoKnj(memo);
                        // 担当表示テキストの生成
                        String tanto = CommonConstants.EMPTY_STRING;
                        String tanto2 = itemEntity.getTantoKnj();
                        if (StringUtils.isNotEmpty(tanto)) {
                            if (StringUtils.isNotEmpty(tanto2)) {
                                tanto = tanto + CommonConstants.STRING_COMMA + tanto2;
                            }
                        } else {
                            tanto = tanto2;
                        }
                        itemEntity.setTantoKnj(tanto);
                        // 時間重複チェック
                        boolean isRepeat = false;
                        // データ区分
                        Integer dataKbn = Integer.valueOf(itemEntity.getDataKbn());

                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.TIME_FORMAT_HHMM);
                        // 開始時間
                        LocalTime startTime1 = LocalTime.parse(itemEntity.getStartTime(), formatter);
                        // 終了時間
                        LocalTime endTime1 = LocalTime.parse(itemEntity.getEndTime(), formatter);
                        for (int j = 0; j < outEntities3.size(); j++) {
                            // 同じデータの場合は次の処理へ
                            if (i == j) {
                                continue;
                            }
                            CpnNikka2OutEntity itemEntity2 = outEntities3.get(j);
                            // データ区分
                            Integer dataKbn2 = Integer.valueOf(itemEntity2.getDataKbn());
                            // 区分が同じ場合
                            if (dataKbn.equals(dataKbn2)) {
                                // 開始時間
                                LocalTime startTime2 = LocalTime.parse(itemEntity2.getStartTime(), formatter);
                                // 終了時間
                                LocalTime endTime2 = LocalTime.parse(itemEntity2.getEndTime(), formatter);
                                // 時間が重複している場合
                                if (startTime1.compareTo(endTime2) < 0 && startTime2.compareTo(endTime1) < 0) {
                                    // 重複フラグをONにして処理を抜ける
                                    isRepeat = true;
                                    break;
                                }
                            }
                        }
                        // 重複の場合
                        if (isRepeat) {
                            String naiyouKnjText = itemEntity.getNaiyoKnj();
                            // Nullの場合は空白に変更
                            if (StringUtils.isEmpty(naiyouKnjText)) {
                                naiyouKnjText = CommonConstants.EMPTY_STRING;
                            }
                            // 先頭に「★」をつける
                            naiyouKnjText = ReportConstants.STR_STAR_RPTIME + naiyouKnjText;
                            itemEntity.setNaiyoKnj(naiyouKnjText);
                        }
                        cpnNikka2OutEntities.add(itemEntity);
                    }
                }
                inDto.setCpnNikka2OutEntities(cpnNikka2OutEntities);
                // 2.1.7.下記日課表共通サービスマスタ情報取得DAOを経由し、共通サービス例リスト情報を取得する
                List<CpnMucDayKyotusvInfoOutEntity> outEntities4 = cpnMucDayKyotusvSelectMapper
                        .findCpnMucDayKyotusvInfoByCriteria(new CpnMucDayKyotusvInfoByCriteriaInEntity());
                if (outEntities4.size() > 0) {
                    List<DailyRoutinePlanReportSvknjDto> svDailyList = new ArrayList<>();
                    // 共通サービス例リストを設定
                    for (CpnMucDayKyotusvInfoOutEntity entity : (outEntities4.size() > 25 ? outEntities4.subList(0, 25)
                            : outEntities4)) {
                        DailyRoutinePlanReportSvknjDto dto = new DailyRoutinePlanReportSvknjDto();
                        dto.setSvKnj(entity.getSvKnj());
                        svDailyList.add(dto);
                    }
                    inDto.setSvDailyList(svDailyList);
                }

                // リクエストパラメータ.データ.印刷設定.帳票タイトル
                inDto.setTitle(model.getPrintSet().getPrtTitle());

                // リクエストパラメータ.データ.印刷設定.日付表示有無＝2:指定日印刷の場合
                if (ReportConstants.PRINTDATE_KBN_PRINT.equals(model.getPrintSet().getPrnDate())) {
                    // 指定日＝共通関数補足の「2.1」の和暦日付
                    inDto.setShiteiDate(nds3GkFunc01Logic.get2Gengouj(1, model.getDbNoSaveData().getSelectDate()));
                } else if (ReportConstants.PRINTDATE_KBN_BLANKDATE.equals(model.getPrintSet().getPrnDate())) {
                    // リクエストパラメータ.データ.印刷設定.日付表示有無＝3:日付空欄印刷の場合
                    // 指定日＝共通関数補足の「1.1」のシステム日付元号
                    DateTimeFormatter inputFormatter = DateTimeFormatter
                            .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
                    DateTimeFormatter outputFormatter = DateTimeFormatter
                            .ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
                    LocalDateTime dateTime = LocalDateTime.parse(model.getSystemDate(), inputFormatter);
                    String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
                    // 指定日の設定
                    inDto.setShiteiDate(blankDate);
                }

                // 事業所名の設定
                inDto.setJigyoKnj(model.getJigyoKnj());

                // 敬称の設定
                // リクエストパラメータ.データ.印刷設定.パラメータ03=1の場合、
                if (CommonConstants.STR_1.equals(model.getPrintSet().getParam03())) {
                    // 敬称＝リクエストパラメータ.データ.印刷設定.パラメータ04
                    inDto.setKeisho(model.getPrintSet().getParam04());
                } else {
                    // リクエストパラメータ.データ.初期設定マスタの情報.印刷帳票の敬称オプション＝1の場合、
                    if (CommonConstants.STR_1.equals(model.getInitMasterObj().getKeishoFlg())) {
                        // 敬称＝リクエストパラメータ.データ.初期設定マスタの情報.印刷帳票の敬称
                        inDto.setKeisho(model.getInitMasterObj().getKeishoKnj());
                    }
                }

                // 作成者の設定
                // リクエストパラメータ.データ.印刷設定.パラメータ05=1の場合、
                if (CommonConstants.STR_1.equals(model.getPrintSet().getParam05())) {
                    // 上記の処理で取得した日課表情報.作成者
                    inDto.setShokuName(shokuinInfoLogic.getShokuNameKnj(CommonDtoUtil.objValToString(shokuId)));
                }

                // リクエストパラメータ.データ.印刷設定.パラメータ13＝3:作成年月日を印刷しないの場合
                if (ReportConstants.CREATEYMD_KBN_SHINAI.equals(model.getPrintSet().getParam13())) {
                    // 作成年月日ラベルの設定
                    inDto.setLabelCreateYmd(CommonConstants.EMPTY_STRING);
                } else {
                    // 作成年月日ラベルの設定
                    inDto.setLabelCreateYmd(ReportConstants.STR_LABEL_CREATEYMD);
                }

                // リクエストパラメータ.データ.印刷設定.パラメータ13＝1:作成年月日を印刷するの場合
                if (ReportConstants.CREATEYMD_KBN_PRINT.equals(model.getPrintSet().getParam13())) {
                    // 作成日＝共通関数補足の「4.1」の和暦日付
                    inDto.setCreateYmd(kghCmpF01Logic.getCmpS2wjEz(createYmd, 1));
                    // リクエストパラメータ.データ.印刷設定.パラメータ13＝2:空白の場合
                } else if (ReportConstants.CREATEYMD_KBN_BLANKDATE.equals(model.getPrintSet().getParam13())) {
                    // 作成日＝共通関数補足の「1.1」のシステム日付元号
                    DateTimeFormatter inputFormatter = DateTimeFormatter
                            .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
                    DateTimeFormatter outputFormatter = DateTimeFormatter
                            .ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
                    LocalDateTime dateTime = LocalDateTime.parse(model.getSystemDate(), inputFormatter);
                    String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
                    // 作成日の設定
                    inDto.setCreateYmd(blankDate);
                }

                // 承認欄印刷フラグ
                inDto.setLbWaku(model.getPrintSet().getParam05());
                // 表示行数
                // inDto.setDispKbn(CommonDtoUtil.strValToInt(model.getDbNoSaveData().getDspKbn()));
                // 指定日印刷区分
                inDto.setParam08(model.getPrintSet().getParam08());
                // 作成日印刷区分
                inDto.setParam13(model.getPrintSet().getParam13());

                // リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ
                inDto.setEmptyFlg(CommonDtoUtil.strValToInt(model.getDbNoSaveData().getEmptyFlg()));
            }
        }
        return inDto;
    }

    /**
     * 帳票レイアウトファイル取得
     * 
     * @return 帳票レイアウトファイル
     * @throws Exception
     */
    public JasperReport getJasperReport(FrameworkProperties fwProps,
            DailyRoutinePlanReportServiceInDto reportParameter, String svJigyoId)
            throws Exception {
        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(fwProps, ReportConstants.U00861_DAILY_ROUTINE_PLAN_REPORT)));
        // コンパイル
        final JasperDesign design = JRXmlLoader.load(is);

        HashMap<String, Object> shonin = shoninSetReportService.getShoninSetReport(design, 1,
                svJigyoId, "");
        reportParameter.setSubReportPath((String) shonin.get(ReportConstants.SUBREPORTPATH));
        reportParameter.setSubReportDataDs((JRBeanCollectionDataSource) shonin.get(ReportConstants.SUBREPORTDATADS));

        final JRDesignBand detailBand = (JRDesignBand) design.getDetailSection().getBands()[0];
        // 日課計画表情報リスト件数分、下記処理を繰り返す。
        if (reportParameter.getCpnNikka2OutEntities() != null) {
            for (CpnNikka2OutEntity entity : reportParameter.getCpnNikka2OutEntities()) {
                String naiyoKnj = entity.getNaiyoKnj();
                // 当該行.サービス主な日常生活上の活動があるの場合
                if (entity.getNaiyoKnj() != null) {
                    // 当該行.文字内容があるの場合、
                    if (!naiyoKnj.isEmpty()) {
                        // 当該行.文字内容が当該行.文字内容 + 改行 +当該行.サービス主な日常生活上の活動を結合して表示する
                        naiyoKnj = naiyoKnj.concat(CommonConstants.NEWLINE_CHARACTOR).concat(entity.getNaiyoKnj());
                    } else {
                        // 上記以外の場合、当該行.文字内容が当該行.サービス主な日常生活上の活動を結合して表示する
                        naiyoKnj = entity.getNaiyoKnj();
                    }
                }

                // 当該行.時間表示区分が「1:先頭」の場合
                if (entity.getTimeKbn() == 1) {
                    // 当該行.文字内容が当該行.開始時間 + "~" + 当該行.終了時間 + 改行 + 当該行.文字内容を結合して表示する。
                    naiyoKnj = entity.getStartTime().concat(CommonConstants.DATE_CONNECTOR).concat(entity.getEndTime())
                            .concat(CommonConstants.NEWLINE_CHARACTOR).concat(naiyoKnj);
                } else if (entity.getTimeKbn() == 2) {
                    // 当該行.時間表示区分が「2:末尾」の場合、
                    // 当該行.文字内容があるの場合、当該行.文字内容が当該行.文字内容 + 改行 + 当該行.開始時間 + "~" + 当該行.終了時間を結合して表示する。
                    if (!naiyoKnj.isEmpty()) {
                        naiyoKnj = naiyoKnj.concat(CommonConstants.NEWLINE_CHARACTOR).concat(entity.getStartTime())
                                .concat(CommonConstants.DATE_CONNECTOR).concat(entity.getEndTime());
                    } else {
                        // 上記以外の場合、当該行.文字内容が当該行.開始時間 + "~" + 当該行.終了時間を結合して表示する。
                        naiyoKnj = entity.getStartTime().concat(CommonConstants.DATE_CONNECTOR)
                                .concat(entity.getEndTime());
                    }
                }
                // 当該行随時実施するかのフラグが「1」の場合
                if (entity.getZuijiFlg() == 1) {
                    if (CommonConstants.DATA_KBN_1.equals(entity.getDataKbn())) {
                        JRDesignFrame frame = new JRDesignFrame();
                        // 当該行.開始位置Xが「64」を設定する。当該行.幅が「384」を設定する。
                        frame.setX(64);
                        frame.setHeight(24);
                        frame.setWidth(384);
                        detailBand.addElement(frame);
                        addTextFieldZuiji(entity, frame, entity.getNaiyoKnj(), 384);
                        JRDesignFrame frame2 = new JRDesignFrame();
                        // 当該行.開始位置Xが「194」を設定する。当該行.幅が「92」を設定する。
                        frame2.setX(194);
                        frame2.setHeight(24);
                        frame2.setWidth(92);
                        detailBand.addElement(frame2);
                        addTextFieldZuiji(entity, frame2, entity.getTantoKnj(), 92);
                    } else if (CommonConstants.DATA_KBN_2.equals(entity.getDataKbn())) {
                        // 該当行.区分が"2":個別サービスの場合、
                        JRDesignFrame frame1 = new JRDesignFrame();
                        // 当該行.開始位置Xが「286」を設定する。当該行.幅が「130」を設定する。
                        frame1.setX(286);
                        frame1.setHeight(24);
                        frame1.setWidth(130);
                        detailBand.addElement(frame1);
                        addTextFieldZuiji(entity, frame1, entity.getNaiyoKnj(), 130);

                        JRDesignFrame frame2 = new JRDesignFrame();
                        // 当該行.開始位置Xが「416」を設定する。当該行.幅が「92」を設定する。
                        frame2.setX(416);
                        frame2.setHeight(24);
                        frame2.setWidth(92);
                        detailBand.addElement(frame2);
                        addTextFieldZuiji(entity, frame2, entity.getTantoKnj(), 92);
                    } else if (CommonConstants.DATA_KBN_3.equals(entity.getDataKbn())) {
                        // 該当行.区分が"3":主な日常生活上の活動の場合、
                        JRDesignFrame frame1 = new JRDesignFrame();
                        // 当該行.開始位置Xが「508」を設定する。当該行.幅が「130」を設定する。
                        frame1.setX(508);
                        frame1.setHeight(24);
                        frame1.setWidth(130);
                        detailBand.addElement(frame1);
                        addTextFieldZuiji(entity, frame1, entity.getNaiyoKnj(), 130);
                    }
                } else {
                    // 該当行.区分が"1":共通サービスの場合
                    if (CommonConstants.DATA_KBN_1.equals(entity.getDataKbn())) {
                        JRDesignFrame frame = new JRDesignFrame();
                        // 当該行.開始位置Xが「64」を設定する。当該行.幅が「130」を設定する。
                        frame.setX(64);
                        frame.setWidth(130);
                        detailBand.addElement(frame);
                        JRDesignFrame designedFrame = addTextField(entity, frame, naiyoKnj);
                        JRDesignFrame frameText = new JRDesignFrame();
                        frameText.setX(64);
                        frameText.setWidth(130);
                        detailBand.addElement(frameText);
                        addTextFieldOnlyText(entity, frameText, naiyoKnj, designedFrame.getHeight(),
                                designedFrame.getY(),
                                judgeTime(entity));

                        JRDesignFrame frame2 = new JRDesignFrame();
                        // 当該行.開始位置Xが「194」を設定する。当該行.幅が「92」を設定する。
                        frame2.setX(194);
                        frame2.setWidth(92);
                        detailBand.addElement(frame2);
                        JRDesignFrame designedFrame2 = addTextField(entity, frame2, entity.getTantoKnj());
                        JRDesignFrame frameText2 = new JRDesignFrame();
                        frameText2.setX(194);
                        frameText2.setWidth(92);
                        detailBand.addElement(frameText2);
                        addTextFieldOnlyText(entity, frameText2, entity.getTantoKnj(), designedFrame2.getHeight(),
                                designedFrame2.getY(),
                                judgeTime(entity));
                    } else if (CommonConstants.DATA_KBN_2.equals(entity.getDataKbn())) {
                        // 該当行.区分が"2":個別サービスの場合、
                        JRDesignFrame frame1 = new JRDesignFrame();
                        // 当該行.開始位置Xが「286」を設定する。当該行.幅が「130」を設定する。
                        frame1.setX(286);
                        frame1.setWidth(130);
                        detailBand.addElement(frame1);
                        JRDesignFrame designedFrame1 = addTextField(entity, frame1, naiyoKnj);
                        JRDesignFrame frameText1 = new JRDesignFrame();
                        frameText1.setX(286);
                        frameText1.setWidth(130);
                        detailBand.addElement(frameText1);
                        addTextFieldOnlyText(entity, frameText1, naiyoKnj, designedFrame1.getHeight(),
                                designedFrame1.getY(),
                                judgeTime(entity));

                        JRDesignFrame frame2 = new JRDesignFrame();
                        // 当該行.開始位置Xが「416」を設定する。当該行.幅が「92」を設定する。
                        frame2.setX(416);
                        frame2.setWidth(92);
                        detailBand.addElement(frame2);
                        JRDesignFrame designedFrame2 = addTextField(entity, frame2, entity.getTantoKnj());
                        JRDesignFrame frameText2 = new JRDesignFrame();
                        frameText2.setX(416);
                        frameText2.setWidth(92);
                        detailBand.addElement(frameText2);
                        addTextFieldOnlyText(entity, frameText2, entity.getTantoKnj(), designedFrame2.getHeight(),
                                designedFrame2.getY(),
                                judgeTime(entity));
                    } else if (CommonConstants.DATA_KBN_3.equals(entity.getDataKbn())) {
                        // 該当行.区分が"3":主な日常生活上の活動の場合、
                        JRDesignFrame frame1 = new JRDesignFrame();
                        // 当該行.開始位置Xが「508」を設定する。当該行.幅が「130」を設定する。
                        frame1.setX(508);
                        frame1.setWidth(130);
                        detailBand.addElement(frame1);
                        JRDesignFrame designedFrame1 = addTextField(entity, frame1, naiyoKnj);
                        JRDesignFrame frameText1 = new JRDesignFrame();
                        frameText1.setX(508);
                        frameText1.setWidth(130);
                        detailBand.addElement(frameText1);
                        addTextFieldOnlyText(entity, frameText1, naiyoKnj, designedFrame1.getHeight(),
                                designedFrame1.getY(),
                                judgeTime(entity));
                    }
                }
            }
        }

        JasperReport jasperFile = JasperCompileManager.compileReport(design);
        return jasperFile;
    }

    /**
     * 時間帯解析
     * 
     * @param entity 日課計画表データ
     * @return
     */
    private boolean judgeTime(CpnNikka2OutEntity entity) {
        if (entity.getWakugaiFlg() == 1) {
            // HH:mm 形式のフォーマッターを作成
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.TIME_FORMAT_HHMM);
            LocalTime startTime = LocalTime.parse(entity.getStartTime(), formatter);
            LocalTime endTime = LocalTime.parse(entity.getEndTime(), formatter);
            Duration duration = Duration.between(startTime, endTime);
            long timeDiffMinutes = duration.toMinutes();
            return timeDiffMinutes < 90;
        } else {
            return false;
        }
    }

    /**
     * 随時実施するかのフラグが「1」の場合 ラベルの設定
     * 
     * @param entity   日課計画表データ
     * @param frame    日課計画表
     * @param naiyoKnj 表示内容
     * @return
     */
    private void addTextFieldZuiji(CpnNikka2OutEntity entity, JRDesignFrame frame,
            String naiyoKnj, int width) {
        JRDesignStaticText textField = new JRDesignStaticText();
        // フォント設定
        textField.setFontName(ReportConstants.PDF_FONTGOTHIC);
        // 位置設定
        frame.setY(400);
        // 初期x座標
        textField.setX(0);
        // 初期y座標
        textField.setY(0);
        // 高さ設定
        textField.setHeight(24);
        // 幅設定
        textField.setWidth(width);
        // 文字サイズ
        textField.setFontSize(Float.valueOf(CommonDtoUtil.objValToString(entity.getFontSize() > 0 ? entity.getFontSize()
                : entity
                        .getFontSize() * -1)));
        // 文字位置
        if (entity.getAlignment() == 1) {
            textField.setHorizontalTextAlign(HorizontalTextAlignEnum.RIGHT);
        } else if (entity.getAlignment() == 2) {
            textField.setHorizontalTextAlign(HorizontalTextAlignEnum.CENTER);
        }
        // 文字カラー
        textField.setForecolor(new Color(entity.getFontColor()));
        // 背景カラー
        textField.setBackcolor(new Color(entity.getBackColor()));
        // ラベル設定
        textField.setText(naiyoKnj);
        textField.setStretchType(StretchTypeEnum.ELEMENT_GROUP_HEIGHT);
        // 透過設定
        textField.setMode(ModeEnum.OPAQUE);
        frame.setMode(ModeEnum.OPAQUE);
        frame.addElement(textField);
        // 外枠設定
        frame.getLineBox().getBoxContainer().getLineBox().getPen().setLineWidth((float) 1);
        // 外枠色設定
        frame.getLineBox().getBoxContainer().getLineBox().getPen().setLineColor(Color.BLACK);
    }

    /**
     * 該当行データを日課計画表表示リストに追加する
     * 
     * @param entity   日課計画表データ
     * @param frame    日課計画表
     * @param naiyoKnj 表示内容
     * @return
     */
    private JRDesignFrame addTextField(CpnNikka2OutEntity entity, JRDesignFrame frame,
            String naiyoKnj) {
        JRDesignStaticText textField = new JRDesignStaticText();
        // フォント設定
        textField.setFontName(ReportConstants.PDF_FONTGOTHIC);
        // 初期x座標
        textField.setX(0);
        // 初期y座標
        textField.setY(0);
        // 透過設定
        textField.setMode(ModeEnum.OPAQUE);
        // 背景カラー
        textField.setBackcolor(new Color(entity.getBackColor()));
        textField.setStretchType(StretchTypeEnum.ELEMENT_GROUP_HEIGHT);

        // 時間帯解析
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.TIME_FORMAT_HHMM);
        LocalTime startTime = LocalTime.parse(entity.getStartTime(), formatter);
        // 開始時間の時
        int startHh = startTime.getHour();
        // 開始時間の分
        int startMm = startTime.getMinute();
        LocalTime endTime = LocalTime.parse(entity.getEndTime(), formatter);
        // 終了時間の時
        int endHh = endTime.getHour();
        // 終了時間の分
        int endMm = endTime.getMinute();

        // 変数.開始時間の時間部分と分部分両方とも0、且つ変数.終了時間の時間部分と分部分両方とも0の場合
        if (startHh == 0 && startMm == 0 && endHh == 0 && endMm == 0) {
            // 開始位置のYに、320を設定する
            frame.setY(320);
            // 行の高さに、16を設定する
            frame.setHeight(16);
        }
        // 変数.開始時間の分の設定
        // 変数.開始時間の分＜30の場合
        if (startMm < 30) {
            startMm = 0;
            // 上記以外の場合
        } else {
            // 変数.開始時間の分が30を設定する
            startMm = 30;
        }
        // 変数.終了時間の分の設定
        if (endMm == 0) {
            endMm = 0;
        } else if (endMm <= 30) {
            endMm = 30;
            // 上記以外の場合
        } else {
            // 変数.終了時間の分が0を設定
            endMm = 0;
            // 変数.終了時間の時間部分に１をAddする
            endHh = endHh + 1;
        }

        // 当該行.開始位置Yの設定
        // 変数.開始時間の時間 > 3の場合
        if (startHh > 3) {
            // 変数.開始時間の時間 - 3 で 計算する
            startHh = startHh - 3;
        } else {
            startHh = startHh + 21;
        }

        // 変数開始時間の分が0の場合
        if (startMm == 0) {
            // 当該行の開始位置Yが (16 * (変数.開始時間の時 - 1))を設定する
            frame.setY(16 * startHh);
            // 変数開始時間の分が30の場合
        } else if (startMm == 30) {
            // 当該行の開始位置Yが (16 * (変数.開始時間の時 - 1) + 8)を設定する
            frame.setY(16 * startHh + 8);
        }

        // 当該行.終了位置Yの設定
        // （変数.終了時間の時間＊100 + 変数.終了時間の分） > 400の場合
        if ((endHh * 100 + endMm) > 400) {
            endHh = endHh - 3;
            // 上記以外の場合
        } else {
            // 変数終了時間の時間 ＋ 21 で 計算する
            endHh = endHh + 21;
        }

        int startTotalMinutes = startHh * 60 + startMm;
        int endTotalMinutes = endHh * 60 + endMm;
        int timeDiffMinutes = endTotalMinutes - startTotalMinutes;
        int heightUnits = timeDiffMinutes / 30;
        int frameHeight = heightUnits * 8;

        // 高さ設定
        frame.setHeight(frameHeight);
        // 幅設定
        textField.setWidth(frame.getWidth());
        // 高さ設定
        textField.setHeight(frame.getHeight());
        // 透過設定
        frame.setMode(ModeEnum.OPAQUE);

        frame.addElement(textField);
        // 外枠設定
        frame.getLineBox().getBoxContainer().getLineBox().getPen().setLineWidth((float) 1);
        // 外枠色設定
        frame.getLineBox().getBoxContainer().getLineBox().getPen().setLineColor(Color.BLACK);
        return frame;
    }

    /**
     * 該当行データを日課計画表表示リストに追加する
     * 
     * @param entity    日課計画表データ
     * @param frame     日課計画表
     * @param naiyoKnj  表示内容
     * @param height    高さ
     * @param y         Y座標
     * @param isWakugai 外枠フラグ
     * @return
     */
    private void addTextFieldOnlyText(CpnNikka2OutEntity entity, JRDesignFrame frame,
            String naiyoKnj, int height, int y, boolean isWakugai) {
        JRDesignStaticText textField = new JRDesignStaticText();
        // フォント設定
        textField.setFontName(ReportConstants.PDF_FONTGOTHIC);
        // 初期x座標
        textField.setX(0);
        // 初期y座標
        textField.setY(y);
        // 透過設定
        textField.setMode(ModeEnum.TRANSPARENT);
        // 文字サイズ
        textField.setFontSize(Float.valueOf(CommonDtoUtil.objValToString(entity.getFontSize() > 0 ? entity.getFontSize()
                : entity
                        .getFontSize() * -1)));
        // 文字位置
        if (entity.getAlignment() == 1) {
            textField.setHorizontalTextAlign(HorizontalTextAlignEnum.RIGHT);
        } else if (entity.getAlignment() == 2) {
            textField.setHorizontalTextAlign(HorizontalTextAlignEnum.CENTER);
        }
        // 文字カラー
        textField.setForecolor(new Color(entity.getFontColor()));
        // 背景カラー
        textField.setBackcolor(new Color(0, 0, 0, 0));
        textField.setText(naiyoKnj);
        textField.setStretchType(StretchTypeEnum.ELEMENT_GROUP_HEIGHT);

        frame.setHeight(height);
        // 外枠のフラグがOn、且つ該当行の終了時間-該当行の開始時間 ＜ 90分の場合
        if (isWakugai) {
            textField.setFontName(ReportConstants.PDF_FONTGOTHIC);
            textField.setY(textField.getY() - 6);
            textField.setMode(ModeEnum.TRANSPARENT);

        }
        // 上記以外の場合
        else {
            textField.setY(textField.getY());
        }
        // 幅設定
        textField.setWidth(frame.getWidth());
        // 高さ設定
        textField.setHeight(frame.getHeight());
        frame.setMode(ModeEnum.TRANSPARENT);
        frame.addElement(textField);
    }
}
