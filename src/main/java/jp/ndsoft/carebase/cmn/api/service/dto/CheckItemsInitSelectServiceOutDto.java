package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jakarta.validation.Valid;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckItemInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckMainRireki;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892KknInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892RirekiInfo;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.06.03
 * <AUTHOR>
 * @implNote GUI00892_チェック項目画面 初期情報取得サービス出力Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CheckItemsInitSelectServiceOutDto extends IDtoImpl {

    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 期間管理フラグ */
    private String kikanFlg;

    /** 計画期間情報 */
    @Valid
    private Gui00892KknInfo kikanObj;

    /** チェック項目ヘッダ履歴情報 */
    private Gui00892RirekiInfo rirekiInfo;

    /** チェック項目詳細情報 */
    private List<Gui00892CheckItemInfo> checkItemInfo;

    /** チェックメイン履歴情報 */
    private List<Gui00892CheckMainRireki> checkMainRireki;

}
