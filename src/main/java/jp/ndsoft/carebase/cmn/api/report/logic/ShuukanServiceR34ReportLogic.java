package jp.ndsoft.carebase.cmn.api.report.logic;

import static net.sf.jasperreports.engine.type.HorizontalTextAlignEnum.CENTER;
import static net.sf.jasperreports.engine.type.HorizontalTextAlignEnum.LEFT;
import static net.sf.jasperreports.engine.type.HorizontalTextAlignEnum.RIGHT;

import java.awt.Color;
import java.io.FileInputStream;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonShiTeiPrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportSvcR34PrintOption;
import jp.ndsoft.carebase.cmn.api.logic.KghCmn03gFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkBase02Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.JigyoRirekiInfoDto;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.ShuukanServiceR34ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.model.ShuukanServiceR34ReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.service.ShoninSetReportService;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.Cks54InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Cks54InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekInputByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekInputOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekRirekiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekRirekiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPrnPreSrw2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinHogoJohoSetteiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinHogoJohoSetteiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSysiniSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMstChouhyouInkanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks51SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks52SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks54SelectMapper;
import jp.ndsoft.smh.framework.properties.FrameworkProperties;
import net.sf.jasperreports.engine.JRBand;
import net.sf.jasperreports.engine.JRLineBox;
import net.sf.jasperreports.engine.JRSection;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.design.JRDesignBand;
import net.sf.jasperreports.engine.design.JRDesignExpression;
import net.sf.jasperreports.engine.design.JRDesignTextField;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.type.ModeEnum;
import net.sf.jasperreports.engine.type.PositionTypeEnum;
import net.sf.jasperreports.engine.xml.JRXmlLoader;

/**
 * @since 2025.07.22
 * <AUTHOR>
 * @description U00852_週間サービス計画表（R34改訂） 帳票出力
 */
@Component
public class ShuukanServiceR34ReportLogic {

    @Autowired
    private CpnTucCks51SelectMapper cpnTucCks51SelectMapper;

    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;

    @Autowired
    private ComMocSysiniSelectMapper comMocSysiniSelectMapper;

    @Autowired
    private CpnMstChouhyouInkanSelectMapper cpnMstChouhyouInkanSelectMapper;

    @Autowired
    private CpnTucCks54SelectMapper cpnTucCks54SelectMapper;

    @Autowired
    private CpnTucCks52SelectMapper cpnTucCks52SelectMapper;

    /** ロジッククラス */
    @Autowired
    private KghCmn03gFunc01Logic kghCmn03gFunc01Logic;

    /** */
    @Autowired
    private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;

    /** KghCmpF01Logicロジッククラス */
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** Nds3GkBase02Logicロジッククラス */
    @Autowired
    private Nds3GkBase02Logic nds3GkBase02Logic;

    // 承認欄設定
    @Autowired
    private ShoninSetReportService shoninSetReportService;

    /** widthDefNumber */
    private final Integer widthDefNumber = 82;
    /** heightDefNumber */
    private final Integer heightDefNumber = 15;
    /** heightMax */
    private Integer heightMax = 432;
    /** startX */
    private Integer startX = 24;
    /** startY */
    private Integer startY = 17;

    /**
     * V00251_サービス利用票の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public ShuukanServiceR34ReportServiceInDto getShuukanServiceR34ReportParameters(
            ShuukanServiceR34ReportParameterModel inDto) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 帳票用データ詳細
        ShuukanServiceR34ReportServiceInDto infoInDto = new ShuukanServiceR34ReportServiceInDto();

        // 【リクエストパラメータ】.印刷オプション
        ReportSvcR34PrintOption printOption = inDto.getPrintOption();
        // 【リクエストパラメータ】.指定日印刷設定
        ReportCommonPrintSet printSet = inDto.getShiTeiPrintSet();
        // 【リクエストパラメータ】.作成日印刷設定
        ReportCommonShiTeiPrintSet sakuseiPrintSet = inDto.getSakuseiPrintSet();
        // 【リクエストパラメータ】.利用者ID
        Integer userId = CommonDtoUtil.strValToInt(inDto.getUserId());
        // 【リクエストパラメータ】.履歴ID
        Integer rirekiId = CommonDtoUtil.strValToInt(inDto.getRirekiId());
        // 【リクエストパラメータ】.事業所ID
        Integer svJigyoId = CommonDtoUtil.strValToInt(inDto.getSvJigyoId());
        // 【リクエストパラメータ】.計画期間ID
        Integer sc1Id = CommonDtoUtil.strValToInt(inDto.getSc1Id());
        // 【リクエストパラメータ】.職員ID
        Integer shokuinId = CommonDtoUtil.strValToInt(inDto.getShokuinId());
        // 【リクエストパラメータ】.システムコード
        String syscd = inDto.getSyscd();
        // 【リクエストパラメータ】.印鑑欄を表示するフラグ
        String inkanFlg = inDto.getInkanFlg();
        // 【リクエストパラメータ】.法人ID
        Integer houjinId = CommonDtoUtil.strValToInt(inDto.getHoujinId());
        // 【リクエストパラメータ】.施設ID
        Integer shisetuId = CommonDtoUtil.strValToInt(inDto.getShisetuId());

        // 2. 週間計画表（ヘッダ）情報取得
        CpnWeekRirekiByCriteriaInEntity cpnWeekRirekiByCriteriaInEntity = new CpnWeekRirekiByCriteriaInEntity();
        cpnWeekRirekiByCriteriaInEntity.setKs51(rirekiId);
        List<CpnWeekRirekiOutEntity> cpnWeekRirekiOutEntityList = cpnTucCks51SelectMapper
                .findCpnWeekRirekiByCriteria(cpnWeekRirekiByCriteriaInEntity);

        // 3. 上記2で取得した週間計画表（ヘッダ）情報がある場合
        if (cpnWeekRirekiOutEntityList != null && cpnWeekRirekiOutEntityList.size() > 0) {
            // 3.1. 職員基本情報の取得
            KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity kghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity = new KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity();
            kghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity
                    .setLlTmp(CommonDtoUtil.objValToString(cpnWeekRirekiOutEntityList.get(0).getUserid()));
            List<KghCpnRaiMonKentPrnPreSrw2OutEntity> kghCpnRaiMonKentPrnPreSrw2ByCriteriaList = comMscShokuinSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw2ByCriteria(kghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity);

            if (kghCpnRaiMonKentPrnPreSrw2ByCriteriaList != null
                    && kghCpnRaiMonKentPrnPreSrw2ByCriteriaList.size() > 0) {
                KghCpnRaiMonKentPrnPreSrw2OutEntity kghCpnRaiMonKentPrnPreSrw2OutEntity = kghCpnRaiMonKentPrnPreSrw2ByCriteriaList
                        .getFirst();
                String shokuinName = CommonDtoUtil.strNullToEmpty(kghCpnRaiMonKentPrnPreSrw2OutEntity.getShokuinName());
                // 作成者
                infoInDto.setShoKuId(shokuinName);
            }

            // 3.2. 利用者基本（１－６）情報の取得
            RiyoshaKihonByCriteriaInEntity riyoshaKihonByCriteriaInEntity = new RiyoshaKihonByCriteriaInEntity();
            riyoshaKihonByCriteriaInEntity.setAlUserid(userId);
            List<RiyoshaKihonOutEntity> riyoshaKihonOutEntityList = comTucUserSelectMapper
                    .findRiyoshaKihonByCriteria(riyoshaKihonByCriteriaInEntity);

            if (riyoshaKihonOutEntityList != null && riyoshaKihonOutEntityList.size() > 0) {
                // 利用者基本（１－６）情報
                RiyoshaKihonOutEntity riyoshaKihonOutEntity = riyoshaKihonOutEntityList.getFirst();
                String name1Knj = CommonDtoUtil.strNullToEmpty(riyoshaKihonOutEntity.getName1Knj());
                String name2Knj = CommonDtoUtil.strNullToEmpty(riyoshaKihonOutEntity.getName2Knj());
                // 利用者名
                infoInDto.setRiyoushaNm(name1Knj + CommonConstants.BLANK_SPACE + name2Knj);
            }
        }

        // 4. 事業履歴より事業所名を取得する。
        // 事業所名
        String jigyoName = CommonConstants.BLANK_STRING;
        // リクエストパラメータ.事業所名に右上を印刷するフラグが”０”の場合、
        if (CommonConstants.STR_0.equals(printOption.getJigyomigiFlg())) {
            // 事業所名が空白を設定
            jigyoName = CommonConstants.BLANK_STRING;
        } else {
            // 4.2.1. 変数.処理月を設定
            String tougaiYm = CommonConstants.BLANK_STRING;
            if (cpnWeekRirekiOutEntityList != null && cpnWeekRirekiOutEntityList.size() > 0) {
                // 変数.処理月 ＝ 上記2で取得した週間計画表（ヘッダ）情報(1行目).処理月
                tougaiYm = cpnWeekRirekiOutEntityList.get(0).getTougaiYm();
            } else {
                // ・上記2で取得した週間計画表（ヘッダ）情報がない場合
                // 変数.処理月 ＝ ””
                tougaiYm = CommonConstants.BLANK_STRING;
            }
            // 4.2.2. 変数.処理月が空白、または「NULL」の場合
            String createYmd = CommonConstants.BLANK_STRING;
            if (tougaiYm == null || tougaiYm.isEmpty()) {
                // ・上記2で取得した週間計画表（ヘッダ）情報がない場合
                if (cpnWeekRirekiOutEntityList == null || cpnWeekRirekiOutEntityList.size() == 0) {
                    // 変数.履歴検索年月日 ＝ ””
                    createYmd = CommonConstants.BLANK_STRING;
                } else {
                    // 変数.履歴検索年月日 ＝ 上記2で取得した週間計画表（ヘッダ）情報(1行目).作成日
                    createYmd = cpnWeekRirekiOutEntityList.get(0).getCreateYmd();
                }
            } else {
                // 下記共通関数を利用して、戻り値を変数.履歴検索年月日に設定 f_kgh_cmn_get_tukimatu
                createYmd = kghCmn03gFunc01Logic.getTukimatu(tougaiYm + CommonConstants.YYMMYM_31);
            }
            // 4.4. 下記関数を利用して、事業所正式名称、事業所略称を取得する f_cpn_get_jigyo_rireki_knj
            JigyoRirekiInfoDto retDto = kghKrkZCpnFuncLogic.getJigyoRirekiKnj(svJigyoId, createYmd);
            // 4.4. リクエストパラメータ.事業所名表示フラグ = "2：正式名称" の場合
            if (CommonConstants.STR_2.equals(printOption.getJigyoDisplayFlg())) {
                // 事業所名が上記取得した事業所正式名称を設定
                jigyoName = retDto.getJigyoRirekiKnj();
            } else {
                // 事業所名が上記取得した事業所略称を設定
                jigyoName = retDto.getJigyoRirekiRyakuKnj();
            }
        }
        // 事業所名
        infoInDto.setJigyoName(jigyoName);

        // 5. 上記2で取得した週間計画表（ヘッダ）情報がある場合、要介護度、利用者名、作成者名の取得
        // 要介護度
        String youkaigoKbn = CommonConstants.BLANK_STRING;
        String youkaigo = CommonConstants.BLANK_STRING;
        // 利用者名
        String riyoushaNm = CommonConstants.BLANK_STRING;
        // 作成者名
        String shokuId = CommonConstants.BLANK_STRING;
        if (cpnWeekRirekiOutEntityList != null && cpnWeekRirekiOutEntityList.size() > 0) {
            // 5.1. 下記共通関数を利用し、要介護度の情報を取得する。 f_cmp_yokai_get
            youkaigo = kghCmpF01Logic.getCmpYokai(svJigyoId, userId,
                    CommonDtoUtil.strValToInt(printOption.getYoukaiKbn()), sc1Id,
                    cpnWeekRirekiOutEntityList.get(0).getCreateYmd());

            // 5.2. 個人情報保護設定の取得
            KojinHogoJohoSetteiByCriteriaInEntity kojinHogoJohoSetteiByCriteriaInEntity = new KojinHogoJohoSetteiByCriteriaInEntity();
            List<KojinHogoJohoSetteiOutEntity> kojinHogoJohoSetteiOutEntityList = comMocSysiniSelectMapper
                    .findKojinHogoJohoSetteiByCriteria(kojinHogoJohoSetteiByCriteriaInEntity);
            if (kojinHogoJohoSetteiOutEntityList != null && kojinHogoJohoSetteiOutEntityList.size() > 0) {
                // 5.3. 上記5.2で取得した値が”１”の場合、下記の関数を実行する。
                if (CommonConstants.STR_1.equals(kojinHogoJohoSetteiOutEntityList.get(0).getNewParam())) {
                    // 5.3.1. 下記関数を利用して、個人情報保護フラグの取得 f3gk_getprofile
                    F3gkGetProfileInDto fileInDto = new F3gkGetProfileInDto();
                    // システムコード
                    fileInDto.setGsyscd(syscd);
                    // 職員ID
                    fileInDto.setShokuId(shokuinId);
                    // 法人ID
                    fileInDto.setHoujinId(CommonConstants.DISPLAY_CONFIGURE_HOU_JIN_ID_0);
                    // 施設ID
                    fileInDto.setShisetuId(CommonConstants.DISPLAY_CONFIGURE_SHISETU_ID_0);
                    // 事業所ID
                    fileInDto.setSvJigyoId(CommonConstants.DISPLAY_CONFIGURE_SV_JIGYO_ID_0);
                    // 画面名 スイッチ
                    fileInDto.setKinounameKnj(CommonConstants.KINOU_NAME_PRT);
                    // セクション
                    fileInDto.setSectionKnj(CommonConstants.ISSECTION_U00852);
                    // キー
                    fileInDto.setKeyKnj(CommonConstants.KOJINHOGO_FLG);
                    // 初期値
                    fileInDto.setAsDefault(CommonConstants.STR_1);

                    String kojin = nds3GkFunc01Logic.getF3gkProfile(fileInDto);
                    // 5.3.2. 上記5.3.1で取得したパラメータが”1：正常”の場合
                    if (CommonConstants.STR_1.equals(kojin)) {
                        youkaigoKbn = CommonConstants.BLANK_STRING;
                        // ②、下記共通を利用して、取得した利用者名代替データを帳票用データ詳細.利用者名に設定
                        infoInDto.setRiyoushaNm(nds3GkBase02Logic.getKojinhogoNokado(userId));
                    } else {
                        // ①、要介護度 ＝ 上記5.1で取得した要介護度名
                        youkaigoKbn = youkaigo;
                    }
                } else {
                    // 5.4.1. 利用者の氏名欄に伏字を適用
                    List<Object> dataWindowDtoList = new ArrayList<>();
                    dataWindowDtoList.add(infoInDto);
                    nds3GkBase02Logic.getF3gkComPrintFuseji(inDto.getShokuinId(), ReportConstants.STRING_NAME_KNJ,
                            new String[] { ReportConstants.STRING_NAME_KNJ }, ReportConstants.INT_0, dataWindowDtoList,
                            new ArrayList<>(), ReportConstants.INT_0, CommonConstants.ISSECTION_U00852,
                            CommonConstants.BLANK_STRING, CommonConstants.BLANK_STRING, inDto.getSyscd());
                    // 5.4.2. 作成者の氏名欄に伏字を適用
                    nds3GkBase02Logic.getF3gkComPrintFuseji(inDto.getShokuinId(), ReportConstants.STRING_NAME_KNJ,
                            new String[] { ReportConstants.STRING_NAME_KNJ }, ReportConstants.INT_0, dataWindowDtoList,
                            new ArrayList<>(), ReportConstants.INT_0, CommonConstants.ISSECTION_U00852,
                            CommonConstants.BLANK_STRING, CommonConstants.BLANK_STRING, inDto.getSyscd());
                }
            }
        }
        // 要介護度
        infoInDto.setYoukaigoKbn(youkaigoKbn);
        // 利用者名
        infoInDto.setRiyoushaNm(riyoushaNm);
        // 作成者名
        infoInDto.setShoKuId(shokuId);

        // 6. 印鑑欄設定情報を取得する。
        // 6.1.リクエストパラメータ.印鑑欄を表示するフラグ = trueとリクエストパラメータ.記入用シートを印刷するフラグ = false
        // の場合、印鑑欄設定情報の取得
        if (CommonConstants.STR_TRUE.equals(inkanFlg) && CommonConstants.STR_FALSE.equals(printOption.getEmptyFlg())) {
            KghCpnMstChouhyouInkanPrnByCriteriaInEntity kghCpnMstChouhyouInkanPrnByCriteriaInEntity = new KghCpnMstChouhyouInkanPrnByCriteriaInEntity();
            kghCpnMstChouhyouInkanPrnByCriteriaInEntity.setAnKey1(houjinId);
            kghCpnMstChouhyouInkanPrnByCriteriaInEntity.setAnKey2(shisetuId);
            kghCpnMstChouhyouInkanPrnByCriteriaInEntity.setAnKey3(svJigyoId);
            kghCpnMstChouhyouInkanPrnByCriteriaInEntity.setAsSec(CommonConstants.ISSECTION_U00852);
            List<KghCpnMstChouhyouInkanPrnOutEntity> kghCpnMstChouhyouInkanPrnOutEntityList = cpnMstChouhyouInkanSelectMapper
                    .findKghCpnMstChouhyouInkanPrnByCriteria(kghCpnMstChouhyouInkanPrnByCriteriaInEntity);
            if (kghCpnMstChouhyouInkanPrnOutEntityList != null && kghCpnMstChouhyouInkanPrnOutEntityList.size() > 0) {
                // 印鑑欄表示区分
                if (kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHyoujiKbn() == 1) {
                    infoInDto.setIsDisplayStamp("true");
                } else {
                    infoInDto.setIsDisplayStamp("false");
                }
                // 印鑑1
                infoInDto.setStampColumnTitle1(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko1Knj());
                // 印鑑2
                infoInDto.setStampColumnTitle2(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko2Knj());
                // 印鑑3
                infoInDto.setStampColumnTitle3(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko3Knj());
                // 印鑑4
                infoInDto.setStampColumnTitle4(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko4Knj());
                // 印鑑5
                infoInDto.setStampColumnTitle5(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko5Knj());
                // 印鑑6
                infoInDto.setStampColumnTitle6(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko6Knj());
                // 印鑑7
                infoInDto.setStampColumnTitle7(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko7Knj());
                // 印鑑8
                infoInDto.setStampColumnTitle8(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko8Knj());
                // 印鑑9
                infoInDto.setStampColumnTitle9(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko9Knj());
                // 印鑑10
                infoInDto.setStampColumnTitle10(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko10Knj());
                // 印鑑11
                infoInDto.setStampColumnTitle11(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko11Knj());
                // 印鑑12
                infoInDto.setStampColumnTitle12(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko12Knj());
                // 印鑑13
                infoInDto.setStampColumnTitle13(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko13Knj());
                // 印鑑14
                infoInDto.setStampColumnTitle14(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko14Knj());
                // 印鑑15
                infoInDto.setStampColumnTitle15(kghCpnMstChouhyouInkanPrnOutEntityList.get(0).getHanko15Knj());
            }
        }

        // 7. リクエストパラメータ.処理年月日を印刷するフラグが”true”の場合、当該年月の設定
        String stTougaiYm = CommonConstants.BLANK_STRING;
        if (CommonConstants.STR_TRUE.equals(printOption.getNenshoriFlg())) {
            // 7.1. 上記2で取得した週間計画表（ヘッダ）情報.処理月がある場合、
            if (cpnWeekRirekiOutEntityList != null && cpnWeekRirekiOutEntityList.size() > 0) {
                if (cpnWeekRirekiOutEntityList.get(0).getCreateYmd() != null
                        && !cpnWeekRirekiOutEntityList.get(0).getCreateYmd().isEmpty()) {
                    // 7.1.1. 下記関数を利用して、和暦かどうか判定する
                    stTougaiYm = ReportUtil.getLocalDateToJapaneseDateTimeFormat(
                            cpnWeekRirekiOutEntityList.get(0).getTougaiYm() + CommonConstants.STRING_FIRST_DAY);
                    // 7.1.2. リクエストパラメータ.記入用シートを印刷するフラグが”false”の場合
                    if (CommonConstants.STR_FALSE.equals(printOption.getEmptyFlg())) {
                        // 7.1.2.1. 下記共通関数を利用して、該当年月を取得する
                        stTougaiYm = nds3GkFunc01Logic.cnv2zenh(stTougaiYm, CommonConstants.INT_1);
                    }
                }
            }
        }
        // 年 月分
        infoInDto.setSttougaiYm(stTougaiYm + CommonConstants.MINUTE + printOption.getGobi());

        // 8. 週間計画表（日常生活データ）情報の取得
        Cks54InfoByCriteriaInEntity cks54InfoByCriteriaInEntity = new Cks54InfoByCriteriaInEntity();
        cks54InfoByCriteriaInEntity.setKs51Id(sc1Id);
        List<Cks54InfoOutEntity> cks54InfoOutEntityList = cpnTucCks54SelectMapper
                .findCks54InfoByCriteria(cks54InfoByCriteriaInEntity);
        if (cks54InfoOutEntityList != null && cks54InfoOutEntityList.size() > 0) {
            for (Cks54InfoOutEntity e : cks54InfoOutEntityList) {
                // 主な日常生活上の活動リスト
                switch (e.getSeq()) {
                    case 1:
                        infoInDto.setNichijo1Knj(e.getNichijoKnj());
                        break;
                    case 2:
                        infoInDto.setNichijo2Knj(e.getNichijoKnj());
                        break;
                    case 3:
                        infoInDto.setNichijo3Knj(e.getNichijoKnj());
                        break;
                    case 4:
                        infoInDto.setNichijo4Knj(e.getNichijoKnj());
                        break;
                    case 5:
                        infoInDto.setNichijo5Knj(e.getNichijoKnj());
                        break;
                    case 6:
                        infoInDto.setNichijo6Knj(e.getNichijoKnj());
                        break;
                    case 7:
                        infoInDto.setNichijo7Knj(e.getNichijoKnj());
                        break;
                    case 8:
                        infoInDto.setNichijo8Knj(e.getNichijoKnj());
                        break;
                    case 9:
                        infoInDto.setNichijo9Knj(e.getNichijoKnj());
                        break;
                    case 10:
                        infoInDto.setNichijo10Knj(e.getNichijoKnj());
                        break;
                    case 11:
                        infoInDto.setNichijo11Knj(e.getNichijoKnj());
                        break;
                    case 12:
                        infoInDto.setNichijo12Knj(e.getNichijoKnj());
                        break;
                    case 13:
                        infoInDto.setNichijo13Knj(e.getNichijoKnj());
                        break;
                    case 14:
                        infoInDto.setNichijo14Knj(e.getNichijoKnj());
                        break;
                    case 15:
                        infoInDto.setNichijo15Knj(e.getNichijoKnj());
                        break;
                    case 16:
                        infoInDto.setNichijo16Knj(e.getNichijoKnj());
                        break;
                    case 17:
                        infoInDto.setNichijo17Knj(e.getNichijoKnj());
                        break;
                    case 18:
                        infoInDto.setNichijo18Knj(e.getNichijoKnj());
                        break;
                    case 19:
                        infoInDto.setNichijo19Knj(e.getNichijoKnj());
                        break;
                    case 20:
                        infoInDto.setNichijo20Knj(e.getNichijoKnj());
                        break;
                    case 21:
                        infoInDto.setNichijo21Knj(e.getNichijoKnj());
                        break;
                    case 22:
                        infoInDto.setNichijo22Knj(e.getNichijoKnj());
                        break;
                    case 23:
                        infoInDto.setNichijo23Knj(e.getNichijoKnj());
                        break;
                    case 24:
                        infoInDto.setNichijo24Knj(e.getNichijoKnj());
                        break;
                }
            }
        }

        // '9. 週間計画表（詳細データ）の取得
        CpnWeekInputByCriteriaInEntity cpnWeekInputByCriteriaInEntity = new CpnWeekInputByCriteriaInEntity();
        cpnWeekInputByCriteriaInEntity.setKs51Id(sc1Id);
        List<CpnWeekInputOutEntity> cpnWeekInputOutEntityList = cpnTucCks52SelectMapper
                .findCpnWeekInputByCriteria(cpnWeekInputByCriteriaInEntity);
        infoInDto.setCpnWeekInputOutEntityList(cpnWeekInputOutEntityList);

        // 指定日印刷区分
        infoInDto.setShiTeiKubun(printSet.getShiTeiKubun());

        // 指定日
        // ・リクエストパラメータ.記入用シートを印刷するフラグ = true の場合
        if (CommonConstants.STR_1.equals(printOption.getEmptyFlg())) {
            infoInDto.setShiTeiDate(CommonConstants.BLANK_STRING);
        } else {
            if (ReportConstants.PRINTDATE_KBN_PRINT.equals(printSet.getShiTeiKubun())) {
                // リクエストパラメータ.指定日印刷区分＝2 の場合（※2：指定日印刷）
                infoInDto.setShiTeiDate(ReportUtil.getLocalDateToJapaneseDateTimeFormat(printSet.getShiTeiDate()));
            } else if (ReportConstants.PRINTDATE_KBN_BLANKDATE.equals(printSet.getShiTeiKubun())) {
                // リクエストパラメータ.指定日印刷区分＝3 の場合（※3：日付空欄印刷）
                DateTimeFormatter formattere = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
                String date = LocalDate.now().format(formattere);
                String ymd = nds3GkFunc01Logic.blankDate(date);
                infoInDto.setShiTeiDate(ymd);
            } else if (ReportConstants.PRINTDATE_KBN_SHINAI.equals(printSet.getShiTeiKubun())) {
                // リクエストパラメータ.指定日印刷区分＝1 の場合（※1：印刷しない）
                infoInDto.setShiTeiDate(CommonConstants.BLANK_STRING);
            }
        }
        // 敬称
        // リクエストパラメータ.敬称変更フラグ＝"1"の場合
        if (CommonConstants.STR_1.equals(printOption.getKeishoFlg())) {
            // 敬称＝リクエストパラメータ.敬称
            infoInDto.setKeisho(printOption.getKeisho());
        } else {
            infoInDto.setKeisho(CommonConstants.KEISHO_STR_TONO);
        }

        // 帳票タイトル
        infoInDto.setTitle(printOption.getTitle());
        // 承認欄を印刷するフラグ
        infoInDto.setShoninFlg(printOption.getShoninFlg());
        // 作成年月日
        // リクエストパラメータ.記入用シートを印刷するフラグ = true の場合、
        if (CommonConstants.STR_TRUE.equals(printOption.getEmptyFlg())) {
            // 空白
            infoInDto.setCreateYmd(CommonConstants.BLANK_STRING);
        } else {
            if (CommonConstants.STR_1.equals(sakuseiPrintSet.getSakuseiKubun())) {
                // リクエストパラメータ.作成年月日印刷区分＝”1：印刷する” の場合、
                infoInDto.setCreateYmd(ReportUtil.getLocalDateToJapaneseDateTimeFormat(sakuseiPrintSet.getCreateYmd()));
            } else if (CommonConstants.STR_2.equals(sakuseiPrintSet.getSakuseiKubun())) {
                // リクエストパラメータ.作成年月日印刷区分＝"2：空白" の場合（※3：日付空欄印刷）、
                infoInDto.setCreateYmd(CommonConstants.CREATEDATE);
            } else if (CommonConstants.STR_3.equals(sakuseiPrintSet.getSakuseiKubun())) {
                // リクエストパラメータ.作成年月日印刷区分＝"3：印刷しない" の場合（※1：印刷しない）
                infoInDto.setCreateYmd(CommonConstants.BLANK_STRING);
            }
        }

        // 週単位以外のサービス
        infoInDto.setIgaiKbn(CommonConstants.BLANK_STRING);
        // より
        infoInDto.setYori2(printOption.getGobi());
        // よりフラグ
        infoInDto.setYoriFlag(printOption.getGobiFlg());

        return infoInDto;
    }

    /**
     * 帳票レイアウトファイル取得
     *
     * @param fwProps         FrameworkProperties
     * @param reportParameter PDF帳票パラメータ
     * @param inDto           入力データ
     * @return 帳票レイアウトファイル
     * @throws Exception
     */
    public JasperReport getJasperReport(FrameworkProperties fwProps,
            ShuukanServiceR34ReportServiceInDto reportParameter, ShuukanServiceR34ReportParameterModel model)
            throws Exception {

        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(fwProps, ReportConstants.JRXML_U00852_SHUUKANR34)));

        // コンパイル
        final JasperDesign jasperDesign = JRXmlLoader.load(is);

        // 承認欄の取得処理
        // リクエストパラメータ.データ.初期設定マスタの情報.承認欄情報が「1:帳票毎保持する」の場合
        HashMap<String, Object> shonin = shoninSetReportService.getShoninSetReport(jasperDesign, ReportConstants.INT_0,
                model.getSvJigyoId(), CommonConstants.ISSECTION_U00852);
        reportParameter.setSubReportPath((String) shonin.get(ReportConstants.SUBREPORTPATH));
        reportParameter.setSubReportDataDs(
                (JRBeanCollectionDataSource) shonin.get(ReportConstants.SUBREPORTDATADS));

        // 帳票レイアウトファイルのリソースを取得する
        // 9.1.4. 矩形ボックスを帳票レイアウトにロードする。
        drawDetailSection(model.getInkanFlg(),
                fwProps,
                reportParameter.getCpnWeekInputOutEntityList(), jasperDesign);

        return JasperCompileManager.compileReport(jasperDesign);
    }

    /**
     * 矩形ボックスを帳票レイアウトにロードする
     * 
     * @param inkanPrintFlag            印鑑欄を表示するフラグ
     * @param jasperDesign              JasperDesign
     * @param cpnWeekInputOutEntityList 週間計画表（詳細データ）
     * @throws Exception
     */
    private JasperDesign drawDetailSection(String inkanPrintFlag, FrameworkProperties fwProps,
            List<CpnWeekInputOutEntity> cpnWeekInputOutEntityList, JasperDesign jasperDesign) throws Exception {

        // detailSectionの存在チェックを行う
        JRSection detailSection = (JRSection) jasperDesign.getDetailSection();
        if (detailSection == null) {
            // 処理終了
            return null;
        }

        JRBand[] detailBands = detailSection.getBands();
        // detailBandsの存在チェックを行う
        if (detailBands == null || detailBands.length < 3) {
            // 処理終了
            return null;
        }

        JRBand detailBand = detailBands[0];
        JRBand detailBand2 = detailBands[1];

        if (detailBand instanceof JRDesignBand && detailBand2 instanceof JRDesignBand) {
            JRDesignBand designBand = (JRDesignBand) detailBand;
            JRDesignBand designBand2 = (JRDesignBand) detailBand2;

            // 4.週間計画表詳細情報リストをループする
            for (CpnWeekInputOutEntity cpnWeekInputOutInfo : cpnWeekInputOutEntityList) {

                if (cpnWeekInputOutInfo == null || cpnWeekInputOutInfo.getYoubi() == null
                        || cpnWeekInputOutInfo.getYoubi().isEmpty()
                        || cpnWeekInputOutInfo.getYoubi().equals(CommonConstants.YOUBI_8SIZE_9)) {
                    continue;
                }

                analyzeBandInformation(designBand, designBand2, jasperDesign, cpnWeekInputOutInfo, inkanPrintFlag);
            }
        }

        return jasperDesign;
    }

    /**
     * 矩形ボックスの設定
     * 
     * @param designBand          JRDesignBand
     * @param designBand2         JRDesignBand
     * @param jasperDesign        JasperDesign
     * @param cpnWeekInputOutInfo 週間計画表詳細情報
     * @param inkanPrintFlag      印鑑欄を表示するフラグ
     */
    private void analyzeBandInformation(JRDesignBand designBand, JRDesignBand designBand2, JasperDesign jasperDesign,
            CpnWeekInputOutEntity cpnWeekInputOutInfo, String inkanPrintFlag) {

        int youbiNumber = 0;
        // 曜日取得
        String youbi = cpnWeekInputOutInfo.getYoubi();
        youbiNumber = switch (youbi) {
            // 月曜日
            case CommonConstants.YOUBI_10000000 -> CommonConstants.YOUBI_NUMBER_1;
            // 火曜日
            case CommonConstants.YOUBI_01000000 -> CommonConstants.YOUBI_NUMBER_2;
            // 水曜日
            case CommonConstants.YOUBI_00100000 -> CommonConstants.YOUBI_NUMBER_3;
            // 木曜日
            case CommonConstants.YOUBI_00010000 -> CommonConstants.YOUBI_NUMBER_4;
            // 金曜日
            case CommonConstants.YOUBI_00001000 -> CommonConstants.YOUBI_NUMBER_5;
            // 土曜日
            case CommonConstants.YOUBI_00000100 -> CommonConstants.YOUBI_NUMBER_6;
            // 日曜日
            case CommonConstants.YOUBI_00000010 -> CommonConstants.YOUBI_NUMBER_7;
            default -> youbiNumber;
        };

        double calculateHours = 0;
        int hour = 0;
        // 開始時間取得
        String startTime = cpnWeekInputOutInfo.getKaishiJikan();
        // 終了時間取得
        String endTime = cpnWeekInputOutInfo.getShuuryouJikan();
        if (startTime == null || startTime.isEmpty() || endTime == null || endTime.isEmpty()) {
            return;
        } else {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.TIME_FORMAT_HHMM);
            // 時間文字列をLocalTimeに解析
            LocalTime localTime = LocalTime.parse(startTime, formatter);
            // 時間部分を取得
            hour = localTime.getHour();

            calculateHours = calculateHours(startTime, endTime);
        }

        // 5.内容情報取得
        String content = cpnWeekInputOutInfo.getNaiyouKnj() == null ? "" : cpnWeekInputOutInfo.getNaiyouKnj();

        // .開始時間 + "~" + 終了時間
        String startEndTime = startTime + CommonConstants.RANGE_SEPARATOR + endTime;

        String contentAndMemo = "";
        contentAndMemo = content;

        // 時間表示区分
        int timeKbn = cpnWeekInputOutInfo.getTimeKbn();
        // 時間表示区分 =1の場合
        if (timeKbn == 1) {
            if (contentAndMemo.isEmpty()) {
                // 4.1.1.1.2.
                contentAndMemo = startEndTime;
            } else {
                // 4.1.1.2.2.、4.1.1.3.2.、4.1.1.4.2.
                contentAndMemo = startEndTime + "\\n" + contentAndMemo;
            }
            // 時間表示区分 =2の場合
        } else if (timeKbn == 2) {
            if (contentAndMemo.isEmpty()) {
                // 4.1.1.1.3.
                contentAndMemo = startEndTime;
            } else {
                // // 4.1.1.2.3.、4.1.1.3.3.、4.1.1.4.3.
                contentAndMemo = contentAndMemo + "\\n" + startEndTime;
            }
        }

        // 文字サイズ
        int fontSize = Math.abs(cpnWeekInputOutInfo.getFontSize());

        // 文字カラー
        int fontColorNumber = cpnWeekInputOutInfo.getFontColor();
        Color fontColor = getColor(fontColorNumber);

        // 背景カラー
        int backColorNumber = cpnWeekInputOutInfo.getBackColor();
        Color backColor = getColor(backColorNumber);

        // X軸座標設定
        int startXNumber = (youbiNumber - 1) * widthDefNumber + startX;

        // Y軸座標設定
        int startYNumber = hour * heightDefNumber + startY;

        // 高さ設定
        int height = (int) (calculateHours * heightDefNumber);

        //
        int alignment = cpnWeekInputOutInfo.getAlignment();

        float bottomLineWidth = 0.5f;
        if (height > heightMax) {
            height = heightMax;
            bottomLineWidth = 1.5f;
        }

        drawBand(designBand2, jasperDesign, contentAndMemo, startXNumber, startYNumber, height, fontSize, fontColor,
                backColor, alignment, bottomLineWidth, judgeTime(cpnWeekInputOutInfo));
    }

    /**
     * 時間帯解析
     * 
     * @param entity 日課計画表データ
     * @return
     */
    private boolean judgeTime(CpnWeekInputOutEntity entity) {
        if (entity.getWakugaiFlg() == 1) {
            // HH:mm 形式のフォーマッターを作成
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.TIME_FORMAT_HHMM);
            LocalTime startTime = LocalTime.parse(entity.getKaishiJikan(), formatter);
            LocalTime endTime = LocalTime.parse(entity.getShuuryouJikan(), formatter);
            Duration duration = Duration.between(startTime, endTime);
            long timeDiffMinutes = duration.toMinutes();
            return timeDiffMinutes <= 90;
        } else {
            return false;
        }
    }

    /**
     * 時間差を計算する
     * 
     * @param startTimeStr 開始時間
     * @param endTimeStr   終了時間
     * @return 時間差
     */
    public static double calculateHours(String startTimeStr, String endTimeStr) {
        // 時間取得
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.TIME_FORMAT_HHMM);
        LocalTime startTime = LocalTime.parse(startTimeStr, formatter);
        LocalTime endTime = LocalTime.parse(endTimeStr, formatter);

        Duration duration;
        // 日を跨いでいるか判定（終了時間が開始時間より早い場合は日跨ぎとみなす）
        if (endTime.isBefore(startTime)) {
            // 日跨ぎの場合：終了時間に24時間を加算してから、開始時間との差を計算
            duration = Duration.between(startTime, endTime.plusHours(24));
        } else {
            // 同日の場合：直接差分を計算
            duration = Duration.between(startTime, endTime);
        }
        // 時間に変換（総分数 ÷ 60）
        long totalMinutes = duration.toMinutes();
        return Math.round((totalMinutes / 60.0) * 10) / 10.0; // 小数点第一位まで丸める
    }

    /**
     * 数值をColorオブジェクトに変換する
     * 
     * @param value 色を表す数値
     * @return 対応するColorオブジェクト
     */
    public static Color getColor(int value) {
        // valueが255の場合：赤成分のみを設定（#RR0000）
        if (value == 255) {
            // 赤成分を設定
            return new Color(value, 0, 0);
        } else {
            // その他の値：RGB値として解析（#RRGGBB）
            // ビットマスクでRGB成分を抽出（アルファ値は不透明）
            int red = (value >> 16) & 0xFF;
            int green = (value >> 8) & 0xFF;
            int blue = value & 0xFF;
            return new Color(red, green, blue);
        }
    }

    /**
     * 矩形ボックスを帳票レイアウトにロード
     * 
     * @param designBand     designBand
     * @param jasperDesign   JasperDesign
     * @param contentAndMemo 表示内容
     * @param startXNumber   X軸
     * @param fontSize       文字サイズ
     * @param fontColor      文字カラー
     * @param backColor      背景カラー
     * @param alignment      文字位置
     * @param isWakugai      外枠フラグ
     */
    private void drawBand(JRDesignBand designBand, JasperDesign jasperDesign, String contentAndMemo, int startXNumber,
            int startYNumber, int height, int fontSize, Color fontColor, Color backColor, int alignment,
            Float bottomLineWidth, boolean isWakugai) {
        // 矩形ボックス初期化
        JRDesignTextField overlayField = new JRDesignTextField(jasperDesign);
        // 表示内容情報設定
        JRDesignExpression expression = new JRDesignExpression();
        expression.setText(getExpressionSetText(contentAndMemo));
        overlayField.setExpression(expression);

        overlayField.setX(startXNumber); // X軸
        overlayField.setY(startYNumber); // Y軸
        overlayField.setWidth(widthDefNumber); // 横幅
        overlayField.setHeight(height); // 高さ

        overlayField.setPositionType(PositionTypeEnum.FLOAT); //
        overlayField.setBlankWhenNull(true); //

        if (alignment == CommonConstants.ALINMENT_1) {
            overlayField.setHorizontalTextAlign(LEFT); // 文字位置「左」
        } else if (alignment == CommonConstants.ALINMENT_2) {
            overlayField.setHorizontalTextAlign(CENTER); // 文字位置「中」
        } else {
            overlayField.setHorizontalTextAlign(RIGHT); // 文字位置「右」
        }
        overlayField.setFontSize((float) fontSize); // 文字サイズ
        overlayField.setFontName(CommonConstants.FONT); // 文字字体
        overlayField.setBackcolor(backColor); // 背景カラー
        overlayField.setForecolor(fontColor); // 文字カラー
        overlayField.setMode(ModeEnum.OPAQUE);

        // 外枠のフラグがOn、且つ該当行の終了時間-該当行の開始時間 ＜ 90分の場合
        if (isWakugai) {
            overlayField.setY(overlayField.getY() - 4);
            overlayField.setMode(ModeEnum.TRANSPARENT);
        }

        // 枠線の色と幅の設定
        JRLineBox lineBox = overlayField.getLineBox();
        setLineWidth(lineBox, bottomLineWidth, true);
        setLineColor(lineBox);
        // 矩形ボックスを帳票レイアウトにロードする
        designBand.addElement(overlayField);
    }

    /**
     * 枠線幅の設定
     * 
     * @param lineBox         枠線
     * @param bottomLineWidth 底部の幅
     * @param topLineFlag     トップの幅
     */
    private static void setLineWidth(JRLineBox lineBox, Float bottomLineWidth, boolean topLineFlag) {
        if (topLineFlag) {
            lineBox.getTopPen().setLineWidth(0.5f);
        }

        lineBox.getLeftPen().setLineWidth(0.5f);
        lineBox.getBottomPen().setLineWidth(bottomLineWidth);
        lineBox.getRightPen().setLineWidth(0.5f);
    }

    /**
     * 枠線色の設定
     * 
     * @param lineBox 枠線
     */
    private void setLineColor(JRLineBox lineBox) {
        // 枠線色の設定
        lineBox.getTopPen().setLineColor(Color.BLACK);
        lineBox.getLeftPen().setLineColor(Color.BLACK);
        lineBox.getBottomPen().setLineColor(Color.BLACK);
        lineBox.getRightPen().setLineColor(Color.BLACK);
    }

    /**
     * 矩形ボックスの表示情報取得
     * 
     * @param contentAndMemo 表示内容情報
     * @return 表示内容情報
     */
    private String getExpressionSetText(String contentAndMemo) {
        String textContent = "";
        if (!contentAndMemo.equals(CommonConstants.BLANK_STRING)) {
            textContent = "\"" + contentAndMemo + "\"";
        }
        return textContent;
    }
}
