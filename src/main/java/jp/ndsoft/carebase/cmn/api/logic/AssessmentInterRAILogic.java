package jp.ndsoft.carebase.cmn.api.logic;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00764SubInfoA;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00765SubInfoB;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00766SubInfoC;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00767SubInfoD;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00768SubInfoE;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00769SubInfoF;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00771SubInfoH;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00772UpdateSubInfoI1;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00773DiagnosisInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00773SubInfoI2;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00774SubInfoJ;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00779SubInfoO;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00781UpdateSubInfoQ;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00782SubInfoR;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00783SubInfoS;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00770SubInfoGList;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00775SubInfoK;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00780UpdateSubInfoP;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentSheetPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonChoPrt;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.logic.dto.InsertEdocumentParamDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.PrintSettingInfoDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.SmglYmdInDto;
import jp.ndsoft.carebase.cmn.api.report.model.AssessmentSheetReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.EdocumentDeleteReportParameterModel;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentInterRAIUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentInterRAIUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.FreeAssessmentFacePrintSettingsUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.GUI00776LjyouhoOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui00777SubMInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui00778SubInfoNInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui00784SubInfoTInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui00785SubUUpdateInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui00786SubInfoVUpdateOutDto;
import jp.ndsoft.carebase.cmn.api.util.CmnStorageServiceUtil;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssAMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssBMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssCMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssDMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssEMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssFMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssGMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssHMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssI2Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssIMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssJMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssKMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssLMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssMMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssN1Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssNMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssOMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssPMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssQMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssRMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssSMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssTMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssUMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiAssVMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiCap1Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiCap2Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiPlan1Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiPlan2Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucRaiRrkMapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghTucKrkKikanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssA;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssACriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssB;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssBCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssC;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssCCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssD;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssDCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssE;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssECriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssF;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssFCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssGCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssH;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssHCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssI;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssI2;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssI2Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssICriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssJ;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssJCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssK;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssKCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssLCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssMCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssN1Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssNCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssO;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssOCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssP;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssPCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssQ;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssQCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssR;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssS;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssRCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssSCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssTCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssUCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiAssVCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiCap1Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiCap2Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiPlan1Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiPlan2Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiRrk;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucRaiRrkCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTucKrkKikan;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssessmentDiseaseDiagnosisInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssessmentDiseaseDiagnosisInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssessmentDiseaseInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssessmentDiseaseInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucRaiRrkInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucRaiRrkInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.InterRaiAsensIShikkanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.InterRaiAsensIShikkanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPln2LeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPln2LeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentSumSPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentSumSPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.AssessmentDiseaseDiagnosisInfoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.AssessmentDiseaseInfoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucRaiCap1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucRaiPlan2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucRaiRrkSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.InterRaiAsensIShikkanSelectMapper;
import jp.ndsoft.carebase.common.util.CareBaseMComSeqMgrCodeConstants;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.smh.framework.services.util.Numbering;
import jp.ndsoft.smh.framework.util.AppUtil;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * アセスメント(インターライ)画面のロジッククラス
 * 
 * <AUTHOR>
 */
@Component
public class AssessmentInterRAILogic {

    /** 採番 */
    @Autowired
    private Numbering numbering;
    /** 28-xx インターライ方式_CAP選定_結果 */
    @Autowired
    private CpnTucRaiCap1SelectMapper cpnTucRaiCap1SelectMapper;

    /** 28-xx インターライ方式_CAP検討_課題と目標 */
    @Autowired
    private CpnTucRaiPlan2SelectMapper cpnTucRaiPlan2SelectMapper;

    /** 28-xx インターライ方式_履歴 */
    @Autowired
    private KghTucKrkKikanMapper kghTucKrkKikanMapper;

    /** 28-xx インターライ方式_履歴 */
    @Autowired
    private CpnTucRaiRrkMapper cpnTucRaiRrkMapper;

    /** 28-xx インターライ方式_CAP選定_結果 */
    @Autowired
    private CpnTucRaiCap1Mapper cpnTucRaiCap1Mapper;

    /** 28-xx インターライ方式_CAP検討_課題と目標 */
    @Autowired
    private CpnTucRaiPlan2Mapper cpnTucRaiPlan2Mapper;

    /** 28-xx インターライ方式_CAP検討_課題と目標 */
    @Autowired
    private CpnTucRaiPlan1Mapper cpnTucRaiPlan1Mapper;

    /** 機能名からシステム種別を取得する */
    @Autowired
    private KghSmglHistoryLogic kghSmglHistoryLogic;

    /** インターライ方式_履歴情報取得 */
    @Autowired
    private CpnTucRaiRrkSelectMapper cpnTucRaiRrkSelectMapper;

    /** 既存の履歴番号が更新確認 */
    @Autowired
    private KghKrkNokikanOpeLogic kghKrkNokikanOpeLogic;

    /** PrintSettingLogicクラス */
    @Autowired
    private PrintSettingLogic printSettingLogic;

    /** S3e文書保存処理共通関数 */
    @Autowired
    private CmnStorageServiceUtil cmnStorageServiceUtil;

    /** 28-xx インターライ方式_アセスメント_A基本情報 */
    @Autowired
    private CpnTucRaiAssAMapper cpnTucRaiAssAMapper;

    /** 28-xx インターライ方式_アセスメント_B相談受付表 */
    @Autowired
    private CpnTucRaiAssBMapper cpnTucRaiAssBMapper;

    /** 28-xx インターライ方式_アセスメント_C認知 */
    @Autowired
    private CpnTucRaiAssCMapper cpnTucRaiAssCMapper;

    /** 28-xx インターライ方式_アセスメント_Dコミュニケーションと視覚 */
    @Autowired
    private CpnTucRaiAssDMapper cpnTucRaiAssDMapper;

    /** 28-xx インターライ方式_アセスメント_E気分と行動 */
    @Autowired
    private CpnTucRaiAssEMapper cpnTucRaiAssEMapper;

    /** 28-xx インターライ方式_アセスメント_F心理社会面 */
    @Autowired
    private CpnTucRaiAssFMapper cpnTucRaiAssFMapper;

    /** 28-xx インターライ方式_アセスメント_G機能状態 */
    @Autowired
    private CpnTucRaiAssGMapper cpnTucRaiAssGMapper;

    /** 28-xx インターライ方式_アセスメント_H失禁 */
    @Autowired
    private CpnTucRaiAssHMapper cpnTucRaiAssHMapper;

    /** 28-xx インターライ方式_アセスメント_I疾患 */
    @Autowired
    private CpnTucRaiAssIMapper cpnTucRaiAssIMapper;

    /** 28-xx インターライ方式_アセスメント_I疾患（診断） */
    @Autowired
    private CpnTucRaiAssI2Mapper cpnTucRaiAssI2Mapper;

    /** 28-xx インターライ方式_アセスメント_J健康状態 */
    @Autowired
    private CpnTucRaiAssJMapper cpnTucRaiAssJMapper;

    /** 28-xx インターライ方式_アセスメント_K口腔および栄養状態 */
    @Autowired
    private CpnTucRaiAssKMapper cpnTucRaiAssKMapper;

    /** 28-xx インターライ方式_アセスメント_L皮膚の状態 */
    @Autowired
    private CpnTucRaiAssLMapper cpnTucRaiAssLMapper;

    /** 28-xx インターライ方式_アセスメント_Mアクティビティ */
    @Autowired
    private CpnTucRaiAssMMapper cpnTucRaiAssMMapper;

    /** 28-xx インターライ方式_アセスメント_N薬剤 */
    @Autowired
    private CpnTucRaiAssNMapper cpnTucRaiAssNMapper;

    /** 28-xx インターライ方式_アセスメント_N薬剤（薬剤リスト） */
    @Autowired
    private CpnTucRaiAssN1Mapper cpnTucRaiAssN1Mapper;

    /** 28-xx インターライ方式_アセスメント_O治療とケアプログラム */
    @Autowired
    private CpnTucRaiAssOMapper cpnTucRaiAssOMapper;

    /** 28-xx インターライ方式_アセスメント_P意思決定権 */
    @Autowired
    private CpnTucRaiAssPMapper cpnTucRaiAssPMapper;

    /** 28-xx インターライ方式_アセスメント_Q支援状況 */
    @Autowired
    private CpnTucRaiAssQMapper cpnTucRaiAssQMapper;

    /** 28-xx インターライ方式_アセスメント_R退所の可能性 */
    @Autowired
    private CpnTucRaiAssRMapper cpnTucRaiAssRMapper;

    /** 28-xx インターライ方式_アセスメント_S環境評価 */
    @Autowired
    private CpnTucRaiAssSMapper cpnTucRaiAssSMapper;

    /** 28-xx インターライ方式_アセスメント_T今後の見通しと全体状況 */
    @Autowired
    private CpnTucRaiAssTMapper cpnTucRaiAssTMapper;

    /** 28-xx インターライ方式_アセスメント_U利用の終了 */
    @Autowired
    private CpnTucRaiAssUMapper cpnTucRaiAssUMapper;

    /** 28-xx インターライ方式_アセスメント_Vアセスメント情報 */
    @Autowired
    private CpnTucRaiAssVMapper cpnTucRaiAssVMapper;

    /**
     * 28-xx インターライ方式_CAP選定_結果
     */
    @Autowired
    private CpnTucRaiCap2Mapper cpnTucRaiCap2Mapper;

    /** アセスメント_I疾患情報取得 */
    @Autowired
    private AssessmentDiseaseInfoSelectMapper assessmentDiseaseInfoSelectMapper;

    @Autowired
    private AssessmentDiseaseDiagnosisInfoSelectMapper assessmentDiseaseDiagnosisInfoSelectMapper;

    /** interRAIｱｾｽﾒﾝﾄI疾患情報取得 */
    @Autowired
    private InterRaiAsensIShikkanSelectMapper interRaiAsensIShikkanSelectMapper;

    // ・変数.値存在フラグ ＝ 0
    String flg;

    /**
     * 【28-xx インターライ方式_CAP選定_結果】情報を検索する
     * 関数名：selectCap1
     *
     * @param inDto 検索パラメータ
     * @return 【28-xx インターライ方式_CAP選定_結果】情報
     * @throws Exception 例外
     */
    public KghCpnRaiMonKentSumSPOutEntity selectCap1(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        KghCpnRaiMonKentSumSPByCriteriaInEntity entity = new KghCpnRaiMonKentSumSPByCriteriaInEntity();
        entity.setAnRaiId(CommonDtoUtil.strValToInt(inDto.getRaiId()));
        // DAOを実行
        List<KghCpnRaiMonKentSumSPOutEntity> entityList = this.cpnTucRaiCap1SelectMapper
                .findKghCpnRaiMonKentSumSPByCriteria(entity);
        KghCpnRaiMonKentSumSPOutEntity kghCpnRaiMonKentSumSPOutEntity = new KghCpnRaiMonKentSumSPOutEntity();
        if (entityList != null && entityList.size() > 0) {
            kghCpnRaiMonKentSumSPOutEntity = entityList.get(0);
        }
        return kghCpnRaiMonKentSumSPOutEntity;
    }

    /**
     * 【28-xx インターライ方式_CAP検討_課題と目標】情報を検索する
     * 関数名：selectPlan2
     *
     * @param inDto 検索パラメータ
     * @return 【28-xx インターライ方式_CAP検討_課題と目標】情報
     */
    public List<KghCpnRaiMonKentPln2LeOutEntity> selectPlan2(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        KghCpnRaiMonKentPln2LeByCriteriaInEntity entity = new KghCpnRaiMonKentPln2LeByCriteriaInEntity();
        entity.setAnRaiId(CommonDtoUtil.strValToInt(inDto.getRaiId()));
        // DAOを実行
        List<KghCpnRaiMonKentPln2LeOutEntity> entityList = this.cpnTucRaiPlan2SelectMapper
                .findKghCpnRaiMonKentPln2LeByCriteria(entity);
        if (entityList == null || entityList.size() == 0) {
            entityList = new ArrayList<KghCpnRaiMonKentPln2LeOutEntity>();
        }
        return entityList;
    }

    /**
     * 計画対象期間の保存処理
     * 関数名：insertKikan
     *
     * @param inDto 登録パラメータ
     * @return 結果ステータス
     * @throws Exception 例外
     */
    public AssessmentInterRAIUpdateServiceOutDto insertKikan(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        // 戻り情報を設定
        AssessmentInterRAIUpdateServiceOutDto outDto = new AssessmentInterRAIUpdateServiceOutDto();
        // 3.1.リクエストパラメータ.期間管理フラグが「0：対象管理しない」の場合
        String sc1Id = CommonConstants.BLANK_STRING;
        if (CommonConstants.STR_0.equals(inDto.getKikanKanriFlg())) {
            // 3.1.1.リクエストパラメータ.計画対象期間IDがnullであり、またはリクエストパラメータ.計画対象期間IDが０である場合
            if (StringUtil.isEmpty(inDto.getSc1Id()) ||
                    CommonConstants.PERIOD_NO_MANAGE_FLG.equals(inDto.getSc1Id())) {
                KghTucKrkKikan kghTucKrkKikan = new KghTucKrkKikan();
                // 法人ID
                kghTucKrkKikan.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
                // 施設ID
                kghTucKrkKikan.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                // 事業者ID
                kghTucKrkKikan.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                // 利用者ID
                kghTucKrkKikan.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
                // 種別ID
                kghTucKrkKikan.setSyubetsuId(CommonDtoUtil.strValToInt(inDto.getSyubetsuId()));
                // 開始日
                kghTucKrkKikan.setStartYmd(inDto.getKijunbiYmd());
                kghTucKrkKikan.setSc1Id(CommonDtoUtil.bigIntValToInt(numbering.getNumber(AppUtil.getKeiyakushaId(),
                        CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.KGH_TUC_KRK_KIKAN_SC1_ID.getIntValue())));

                // DAOを実行
                kghTucKrkKikanMapper.insertSelective(kghTucKrkKikan);
                // 変数.計画対象期間ID=採番した期間ID
                sc1Id = CommonDtoUtil.objValToString(kghTucKrkKikan.getSc1Id());
            }
            // 3.1.2.上記以外の場合、
            else {
                // 変数.計画対象期間ID=リクエストパラメータ.計画対象期間ID
                sc1Id = inDto.getSc1Id();
            }
        }

        // 採番した期間IDをInDtoに設定する
        outDto.setSc1Id(sc1Id);

        return outDto;
    }

    /**
     * アセスメント(インターライ)画面の履歴の保存処理
     * historySave
     *
     * @param inDto 登録パラメータ
     * @param list  インターライ方式_履歴情報リスト
     * @param sc1Id 計画対象期間ID
     * @return 結果ステータス
     * @throws Exception
     */
    public AssessmentInterRAIUpdateServiceOutDto historySave(AssessmentInterRAIUpdateServiceInDto inDto,
            List<CpnTucRaiRrkInfoOutEntity> list, String sc1Id) throws Exception {
        // 戻り情報を設定
        AssessmentInterRAIUpdateServiceOutDto outDto = new AssessmentInterRAIUpdateServiceOutDto();
        // 4.1.リクエストパラメータ.履歴更新区分が"D":削除の場合
        if (CommonDtoUtil.isHistoryDelete(inDto)) {
            /*
             * ===============5. 画面履歴の削除処理===============
             *
             */
            // 5.1.リクエストパラメータ.アセスメントIDが上記処理「2.2. 」で取得した履歴情報リストに存在する場合
            if (CollectionUtils.isNotEmpty(list)) {
                for (CpnTucRaiRrkInfoOutEntity item : list) {
                    if (null != item) {
                        if (item.getRaiId() == CommonDtoUtil.strValToInt(inDto.getRaiId())) {
                            // 5.1.1. 【28-xx インターライ方式_履歴】情報を削除する
                            deleteAll(inDto);
                            outDto.setRaiId(inDto.getRaiId());
                            break;
                        }
                    }
                }
            }
            // 5.1.2. 「API定義」の8の処理へジャンプする
            // 後続処理
        }
        // 4.2.上記以外の場合
        else {
            /*
             * ===============6. 画面履歴の保存処理===============
             *
             */
            AssessmentInterRAIUpdateServiceOutDto rUpdateServiceOutDto = historyUpdate(inDto, list, sc1Id);
            outDto.setRaiId(rUpdateServiceOutDto.getRaiId());
        }

        return outDto;
    }

    /**
     * アセスメント(インターライ)画面の履歴の保存処理
     * historySave
     *
     * @param inDto 登録パラメータ
     * @param list  インターライ方式_履歴情報リスト
     * @param sc1Id 計画対象期間ID
     * @return 結果ステータス
     * @throws Exception
     */
    public AssessmentInterRAIUpdateServiceOutDto historyUpdate(AssessmentInterRAIUpdateServiceInDto inDto,
            List<CpnTucRaiRrkInfoOutEntity> list, String sc1Id) throws Exception {
        AssessmentInterRAIUpdateServiceOutDto rUpdateServiceOutDto = new AssessmentInterRAIUpdateServiceOutDto();
        // 6.1. 変数初期化
        // 変数.CAP選定削除フラグを「0」に設定する
        int capSelectedDeleteFlag = 0;
        // 変数.セクション項目を空に設定する
        String sectionItem = CommonConstants.BLANK_STRING;
        // 変数.セクション設定値を空に設定する
        String sectionSettingVal = CommonConstants.BLANK_STRING;
        // 変数.アセスメントIDをリクエストパラメータ.アセスメントIDに設定する
        String raiId = inDto.getRaiId();
        // 6.2. 前回保存時の選定表の削除
        // 6.2.1. リクエストパラメータ.選定表・検討表作成区分が"0":選定表のみ作成済、または、 "1":選定表と検討表作成済の場合
        if (CommonConstants.STR_0.equals(inDto.getTableCreateKbn()) ||
                CommonConstants.STR_1.equals(inDto.getTableCreateKbn())) {
            // 6.2.1.1. 【28-xx インターライ方式_CAP選定_結果】情報を削除する

            CpnTucRaiCap2Criteria criteria = new CpnTucRaiCap2Criteria();
            criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));
            // DAOを実行
            this.cpnTucRaiCap2Mapper.deleteByCriteria(criteria);

            // 6.2.1.2. 変数.CAP選定削除フラグを「1」に設定する
            capSelectedDeleteFlag = 1;
        }

        // 6.3. インターライ方式の履歴保存
        // 6.3.1. リクエストパラメータ.更新区分が"D":削除の場合、セクション削除処理する
        if (CommonDtoUtil.isDelete(inDto)) {
            // 6.3.1.1. 【インターライ方式_アセスメント_基本情報】情報を削除する
            // ※5.1.1.の連動テーブルを参照する
            switch (inDto.getSubKbn().toLowerCase()) {
                case "a":
                    // 28-xx インターライ方式_アセスメント_A基本情報
                    deleteCpnTucRaiAssA(inDto);
                    break;
                case "b":
                    // 28-xx インターライ方式_アセスメント_B基本情報
                    deleteCpnTucRaiAssB(inDto);
                    break;
                case "c":
                    // 28-xx インターライ方式_アセスメント_C基本情報
                    deleteCpnTucRaiAssC(inDto);
                    break;
                case "d":
                    // 28-xx インターライ方式_アセスメント_D基本情報
                    deleteCpnTucRaiAssD(inDto);
                    break;
                case "e":
                    // 28-xx インターライ方式_アセスメント_E基本情報
                    deleteCpnTucRaiAssE(inDto);
                    break;
                case "f":
                    // 28-xx インターライ方式_アセスメント_F基本情報
                    deleteCpnTucRaiAssF(inDto);
                    break;
                case "g":
                    // 28-xx インターライ方式_アセスメント_G基本情報
                    deleteCpnTucRaiAssG(inDto);
                    break;
                case "h":
                    // 28-xx インターライ方式_アセスメント_H基本情報
                    deleteCpnTucRaiAssH(inDto);
                    break;
                case "i1":
                    // 28-xx インターライ方式_アセスメント_I基本情報
                    // deleteCpnTucRaiAssI(inDto);
                    break;
                case "i2":
                    // 28-xx インターライ方式_アセスメント_I1基本情報
                    deleteCpnTucRaiAssI2(inDto);
                    break;
                case "j":
                    // 28-xx インターライ方式_アセスメント_J基本情報
                    deleteCpnTucRaiAssJ(inDto);
                    break;
                case "k":
                    // 28-xx インターライ方式_アセスメント_K基本情報
                    deleteCpnTucRaiAssK(inDto);
                    break;
                case "l":
                    // 28-xx インターライ方式_アセスメント_L基本情報
                    deleteCpnTucRaiAssL(inDto);
                    break;
                case "m":
                    // 28-xx インターライ方式_アセスメント_M基本情報
                    deleteCpnTucRaiAssM(inDto);
                    break;
                case "n":
                    // 28-xx インターライ方式_アセスメント_N基本情報
                    deleteCpnTucRaiAssN(inDto);
                    break;
                case "n1":
                    // 28-xx インターライ方式_アセスメント_N1基本情報
                    deleteCpnTucRaiAssN1(inDto);
                    break;
                case "o":
                    // 28-xx インターライ方式_アセスメント_O基本情報
                    deleteCpnTucRaiAssO(inDto);
                    break;
                case "p":
                    // 28-xx インターライ方式_アセスメント_P基本情報
                    deleteCpnTucRaiAssP(inDto);
                    break;
                case "q":
                    // 28-xx インターライ方式_アセスメント_Q基本情報
                    deleteCpnTucRaiAssQ(inDto);
                    break;
                case "r":
                    // 28-xx インターライ方式_アセスメント_R基本情報
                    deleteCpnTucRaiAssR(inDto);
                    break;
                case "s":
                    // 28-xx インターライ方式_アセスメント_S基本情報
                    deleteCpnTucRaiAssS(inDto);
                    break;
                case "t":
                    // 28-xx インターライ方式_アセスメント_T基本情報
                    deleteCpnTucRaiAssT(inDto);
                    break;
                case "u":
                    // 28-xx インターライ方式_アセスメント_U基本情報
                    deleteCpnTucRaiAssU(inDto);
                    break;
                case "v":
                    // 28-xx インターライ方式_アセスメント_V基本情報
                    deleteCpnTucRaiAssV(inDto);
                    break;
                default:
                    break;
            }

            // 6.3.1.2. リクエストパラメータ.タブ名が”I” または ”N”の場合、個別処理する。（※Aタブ対象外）
            String iOrNResult = CommonConstants.BLANK_STRING;
            if (CommonConstants.TAB_NAME_I.equals(inDto.getTabName())
                    && CommonConstants.TAB_NAME_I1.equals(inDto.getSubKbn())) {
                // ・変数.値存在フラグ ＝ 0
                flg = CommonConstants.STR_0;
                // (1). 下記の記録共通期間取得のDAOを利用し、アセスメント_I疾患情報を取得する。
                // DAOパラメータを作成
                AssessmentDiseaseInfoByCriteriaInEntity assessmentDiseaseInfoByCriteriaInEntity = new AssessmentDiseaseInfoByCriteriaInEntity();
                // アセスメントID
                assessmentDiseaseInfoByCriteriaInEntity.setAnRaiId(CommonDtoUtil.strValToInt(inDto.getRaiId()));
                // DAOを実行
                List<AssessmentDiseaseInfoOutEntity> assessmentDiseaseInfoList = this.assessmentDiseaseInfoSelectMapper
                        .findAssessmentDiseaseInfoByCriteria(assessmentDiseaseInfoByCriteriaInEntity);
                // 上記取得したのOUTPUT情報.i2_メモの値がある場合
                if (CollectionUtils.isNotEmpty(assessmentDiseaseInfoList)
                        && Objects.nonNull(assessmentDiseaseInfoList.get(0).getI2MemoKnj())
                        && StringUtil.isNotEmpty(assessmentDiseaseInfoList.get(0).getI2MemoKnj())) {
                    // 変数.値存在フラグ ＝ 1
                    flg = CommonConstants.STR_1;
                }

                // (2). 下記の記録共通期間取得のDAOを利用し、アセスメント_I疾患（診断）情報を取得する。
                AssessmentDiseaseDiagnosisInfoByCriteriaInEntity assessmentDiseaseDiagnosisInfoByCriteriaInEntity = new AssessmentDiseaseDiagnosisInfoByCriteriaInEntity();
                assessmentDiseaseDiagnosisInfoByCriteriaInEntity
                        .setAnRaiId(CommonDtoUtil.strValToInt(inDto.getRaiId()));
                List<AssessmentDiseaseDiagnosisInfoOutEntity> assessmentDiseaseDiagnosisInfoOutEntities = assessmentDiseaseDiagnosisInfoSelectMapper
                        .findAssessmentDiseaseDiagnosisInfoByCriteria(
                                assessmentDiseaseDiagnosisInfoByCriteriaInEntity);
                // 上記取得したのOUTPUT情報.i2_メモの値がある場合
                if (CollectionUtils.isNotEmpty(assessmentDiseaseDiagnosisInfoOutEntities)) {
                    // 変数.値存在フラグ ＝ 1
                    flg = CommonConstants.STR_1;
                }

                // (3). 履歴更新用値の設定
                // ・変数.値存在フラグが ０の場合、
                if (CommonConstants.STR_0.equals(flg)) {
                    // 変数.値存在フラグ ＝3
                    flg = CommonConstants.STR_3;
                    // 戻り値.セクション設定値 ＝ ""
                    iOrNResult = CommonConstants.BLANK_STRING;
                    // ・上記以外の場合、
                } else {
                    // 変数.値存在フラグ ＝4
                    flg = CommonConstants.STR_4;
                    // 戻り値.セクション設定値 ＝ "●"
                    iOrNResult = CommonConstants.MARU;

                }

            } else if (CommonConstants.TAB_NAME_I.equals(inDto.getTabName())
                    && CommonConstants.TAB_NAME_I2.equals(inDto.getSubKbn())) {
                // ・変数.値存在フラグ ＝ 0
                flg = CommonConstants.STR_0;
                // (1). 下記の記録共通期間取得のDAOを利用し、アセスメント_I疾患情報を取得する。
                // DAOパラメータを作成
                InterRaiAsensIShikkanByCriteriaInEntity interRaiAsensIShikkanByCriteriaInEntity = new InterRaiAsensIShikkanByCriteriaInEntity();

                // アセスメントID
                interRaiAsensIShikkanByCriteriaInEntity.setAnRaiId(CommonDtoUtil.strValToInt(inDto.getRaiId()));
                List<InterRaiAsensIShikkanOutEntity> interRaiAsensIShikkanList = this.interRaiAsensIShikkanSelectMapper
                        .findInterRaiAsensIShikkanByCriteria(interRaiAsensIShikkanByCriteriaInEntity);

                // アセスメント_I疾患（診断）情報取得を保持
                if (CollectionUtils.isNotEmpty(interRaiAsensIShikkanList)) {
                    // 変数.値存在フラグ ＝ 1
                    flg = CommonConstants.STR_1;
                }
                // (2). 履歴更新用値の設定
                // ・変数.値存在フラグが ０の場合、
                if (CommonConstants.STR_0.equals(flg)) {
                    // 変数.値存在フラグ ＝3
                    flg = CommonConstants.STR_3;
                    // 戻り値.セクション設定値 ＝ ""
                    iOrNResult = CommonConstants.BLANK_STRING;
                    // ・上記以外の場合、
                } else {
                    // 変数.値存在フラグ ＝4
                    flg = CommonConstants.STR_4;
                    // 戻り値.セクション設定値 ＝ "●"
                    iOrNResult = CommonConstants.MARU;

                }

            } else if (CommonConstants.TAB_NAME_N.equals(inDto.getTabName())) {
                // TODO ※詳しい内容はNタブAPI設計書を参照
            }

            // 6.3.1.3. 削除セクション確認
            // 更新項目確認
            // 変数.セクション項目を「“section” + [リクエストパラメータ.インデックス - 1]」に設定する
            sectionItem = CommonConstants.SECTION_PREFIX +
                    CommonDtoUtil.objValToString(CommonDtoUtil.strValToInt(inDto.getIndex()) -
                            1);

            // 更新項目の設定値
            // リクエストパラメータ.タブ名が”I”の場合
            if (CommonConstants.TAB_NAME_I.equals(inDto.getTabName())) {
                // 変数.セクション設定値を上記処理「6.3.1.2. 」返すセクション設定値で設定する
                sectionSettingVal = iOrNResult;
            }
            // 上記以外の場合
            else {
                // 変数.セクション設定値を空文字に設定する
                // 初期定義処理
            }

            // 6.3.1.4. インターライ方式_履歴更新（セクション削除）
            CpnTucRaiRrk row = new CpnTucRaiRrk();
            // セクション状態
            switch (sectionItem) {
                case CommonConstants.STR_SECTION_1:
                    row.setSection1(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_2:
                    row.setSection2(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_3:
                    row.setSection3(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_4:
                    row.setSection4(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_5:
                    row.setSection5(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_6:
                    row.setSection6(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_7:
                    row.setSection7(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_8:
                    row.setSection8(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_9:
                    row.setSection9(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_10:
                    row.setSection10(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_11:
                    row.setSection11(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_12:
                    row.setSection12(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_13:
                    row.setSection13(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_14:
                    row.setSection14(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_15:
                    row.setSection15(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_16:
                    row.setSection16(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_17:
                    row.setSection17(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_18:
                    row.setSection18(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_19:
                    row.setSection19(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_20:
                    row.setSection20(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_21:
                    row.setSection21(sectionSettingVal);
                    break;
                case CommonConstants.STR_SECTION_22:
                    row.setSection22(sectionSettingVal);
                    break;
                default:
                    break;
            }
            // 調査アセスメント種別
            row.setAssType(CommonDtoUtil.strValToInt(inDto.getAssType()));
            // 調査日
            row.setAssDateYmd(inDto.getKijunbiYmd());
            // 調査者
            row.setAssShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
            // 変数.CAP選定削除フラグが「1」の場合、編集する
            if (1 == capSelectedDeleteFlag) {
                // 選定アセスメント種別
                row.setCapType(null);
                // 選定日
                row.setCapDateYmd(null);
                // 選定者
                row.setCapShokuId(null);
            }

            CpnTucRaiRrkCriteria criteria = new CpnTucRaiRrkCriteria();
            criteria.createCriteria()
                    .andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

            int updateCnt = cpnTucRaiRrkMapper.updateByCriteriaSelective(row, criteria);
            if (updateCnt <= 0) {
                throw new ExclusiveException();
            }

            // 6.3.1.5. 「API定義」の8の処理へジャンプする。
            // 後続処理
        }
        // 6.3.2. 上記以外の場合
        else {
            // 6.3.2.1. 更新セクション確認
            // 更新項目確認
            // 変数.セクション項目を「“section” + [リクエストパラメータ.インデックス - 1]」に設定する
            sectionItem = CommonConstants.SECTION_PREFIX +
                    CommonDtoUtil.objValToString(CommonDtoUtil.strValToInt(inDto.getIndex()) -
                            1);

            // 更新項目の設定値
            // 変数.セクション設定値を「●」に設定する
            sectionSettingVal = CommonConstants.MARU;

            // 6.3.2.2. インターライ方式_履歴の更新処理
            // 6.3.2.2.1. リクエストパラメータ.アセスメントIDが上記処理「2.2. 」で取得した履歴情報リストに存在する場合
            if (CollectionUtils.isNotEmpty(list)
                    && !list.stream().noneMatch(e -> e.getRaiId() == CommonDtoUtil.strValToInt(inDto.getRaiId()))) {
                // 【28-xx インターライ方式_履歴】情報を更新する。
                updateRrk(inDto, sectionItem, sectionSettingVal, capSelectedDeleteFlag);
            }
            // 6.3.2.2.2. 上記以外の場合
            else {
                // 【28-xxインターライ方式_履歴】情報を登録する。
                AssessmentInterRAIUpdateServiceOutDto insertRrk = insertRrk(inDto,
                        sectionItem, sectionSettingVal, sc1Id);
                // 変数.アセスメントIDを採番したアセスメントIDで設定する
                raiId = insertRrk.getRaiId();
            }
        }
        rUpdateServiceOutDto.setRaiId(raiId);
        return rUpdateServiceOutDto;
    }

    /**
     * アセスメント(インターライ)画面の履歴登録処理を行う
     * 関数名：insertRrk
     *
     * @param inDto             登録パラメータ
     * @param sectionItem       セクション項目
     * @param sectionSettingVal セクション設定値
     * @param sc1Id             計画対象期間ID
     * @return 結果ステータス
     * @throws Exception 例外
     */
    public AssessmentInterRAIUpdateServiceOutDto insertRrk(AssessmentInterRAIUpdateServiceInDto inDto,
            String sectionItem,
            String sectionSettingVal, String sc1Id)
            throws Exception {
        CpnTucRaiRrk cpnTucRaiRrk = new CpnTucRaiRrk();
        // 法人ID
        cpnTucRaiRrk.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        cpnTucRaiRrk.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        cpnTucRaiRrk.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        cpnTucRaiRrk.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 計画期間ID
        cpnTucRaiRrk.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        switch (sectionItem) {
            case CommonConstants.STR_SECTION_1:
                // セクションＡ状態
                cpnTucRaiRrk.setSection1(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_2:
                // セクションＢ状態
                cpnTucRaiRrk.setSection2(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_3:
                // セクションＣ状態
                cpnTucRaiRrk.setSection3(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_4:
                // セクションＤ状態
                cpnTucRaiRrk.setSection4(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_5:
                // セクションＥ状態
                cpnTucRaiRrk.setSection5(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_6:
                // セクションＦ状態
                cpnTucRaiRrk.setSection6(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_7:
                // セクションＧ状態
                cpnTucRaiRrk.setSection7(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_8:
                // セクションＨ状態
                cpnTucRaiRrk.setSection8(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_9:
                // セクションＩ状態
                cpnTucRaiRrk.setSection9(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_10:
                // セクションＪ状態
                cpnTucRaiRrk.setSection10(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_11:
                // セクションＫ状態
                cpnTucRaiRrk.setSection11(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_12:
                // セクションＬ状態
                cpnTucRaiRrk.setSection12(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_13:
                // セクションＭ状態
                cpnTucRaiRrk.setSection13(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_14:
                // セクションＮ状態
                cpnTucRaiRrk.setSection14(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_15:
                // セクションＯ状態
                cpnTucRaiRrk.setSection15(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_16:
                // セクションＰ状態
                cpnTucRaiRrk.setSection16(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_17:
                // セクションＱ状態
                cpnTucRaiRrk.setSection17(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_18:
                // セクションＲ状態
                cpnTucRaiRrk.setSection18(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_19:
                // セクションＳ状態
                cpnTucRaiRrk.setSection19(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_20:
                // セクションＴ状態
                cpnTucRaiRrk.setSection20(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_21:
                // セクションＵ状態
                cpnTucRaiRrk.setSection21(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_22:
                // セクションＶ状態
                cpnTucRaiRrk.setSection22(sectionSettingVal);
                break;

            default:
                break;
        }
        // 調査アセスメント種別
        cpnTucRaiRrk.setAssType(CommonDtoUtil.strValToInt(inDto.getAssType()));
        // 調査日
        cpnTucRaiRrk.setAssDateYmd(inDto.getKijunbiYmd());
        // 調査者
        cpnTucRaiRrk.setAssShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));

        // DAOを実行
        this.cpnTucRaiRrkMapper.insertSelectiveAndReturn(cpnTucRaiRrk);
        // 戻り情報を設定
        AssessmentInterRAIUpdateServiceOutDto outDto = new AssessmentInterRAIUpdateServiceOutDto();
        // 採番したアセスメントIDをOutDtoに設定する
        outDto.setRaiId(String.valueOf(cpnTucRaiRrk.getRaiId()));

        return outDto;
    }

    /**
     * アセスメント(インターライ)画面の履歴更新処理を行う
     * 関数名：updateRrk
     *
     * @param inDto                 更新パラメータ
     * @param sectionItem           セクション項目
     * @param sectionSettingVal     セクション設定値
     * @param capSelectedDeleteFlag CAP選定削除フラグ
     * @return 結果ステータス
     * @throws Exception 例外
     */
    public AssessmentInterRAIUpdateServiceOutDto updateRrk(AssessmentInterRAIUpdateServiceInDto inDto,
            String sectionItem,
            String sectionSettingVal, int capSelectedDeleteFlag)
            throws Exception {
        CpnTucRaiRrkCriteria criteria = new CpnTucRaiRrkCriteria();
        criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        CpnTucRaiRrk cpnTucRaiRrk = new CpnTucRaiRrk();
        switch (sectionItem) {
            case CommonConstants.STR_SECTION_1:
                // セクションＡ状態
                cpnTucRaiRrk.setSection1(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_2:
                // セクションＢ状態
                cpnTucRaiRrk.setSection2(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_3:
                // セクションＣ状態
                cpnTucRaiRrk.setSection3(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_4:
                // セクションＤ状態
                cpnTucRaiRrk.setSection4(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_5:
                // セクションＥ状態
                cpnTucRaiRrk.setSection5(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_6:
                // セクションＦ状態
                cpnTucRaiRrk.setSection6(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_7:
                // セクションＧ状態
                cpnTucRaiRrk.setSection7(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_8:
                // セクションＨ状態
                cpnTucRaiRrk.setSection8(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_9:
                // セクションＩ状態
                cpnTucRaiRrk.setSection9(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_10:
                // セクションＪ状態
                cpnTucRaiRrk.setSection10(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_11:
                // セクションＫ状態
                cpnTucRaiRrk.setSection11(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_12:
                // セクションＬ状態
                cpnTucRaiRrk.setSection12(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_13:
                // セクションＭ状態
                cpnTucRaiRrk.setSection13(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_14:
                // セクションＮ状態
                cpnTucRaiRrk.setSection14(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_15:
                // セクションＯ状態
                cpnTucRaiRrk.setSection15(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_16:
                // セクションＰ状態
                cpnTucRaiRrk.setSection16(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_17:
                // セクションＱ状態
                cpnTucRaiRrk.setSection17(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_18:
                // セクションＲ状態
                cpnTucRaiRrk.setSection18(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_19:
                // セクションＳ状態
                cpnTucRaiRrk.setSection19(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_20:
                // セクションＴ状態
                cpnTucRaiRrk.setSection20(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_21:
                // セクションＵ状態
                cpnTucRaiRrk.setSection21(sectionSettingVal);
                break;
            case CommonConstants.STR_SECTION_22:
                // セクションＶ状態
                cpnTucRaiRrk.setSection22(sectionSettingVal);
                break;

            default:
                break;
        }
        // 調査アセスメント種別
        cpnTucRaiRrk.setAssType(CommonDtoUtil.strValToInt(inDto.getAssType()));
        // 調査日
        cpnTucRaiRrk.setAssDateYmd(inDto.getKijunbiYmd());
        // 調査者
        cpnTucRaiRrk.setAssShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));

        // 変数.CAP選定削除フラグが「1」の場合
        if (1 == capSelectedDeleteFlag) {
            // 選定アセスメント種別
            cpnTucRaiRrk.setCapType(null);
            // 選定日
            cpnTucRaiRrk.setCapDateYmd(null);
            // 選定者
            cpnTucRaiRrk.setCapShokuId(null);
        }

        // DAOを実行
        int count = this.cpnTucRaiRrkMapper.updateByCriteriaSelective(cpnTucRaiRrk,
                criteria);
        // 戻り情報を設定
        AssessmentInterRAIUpdateServiceOutDto outDto = new AssessmentInterRAIUpdateServiceOutDto();
        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
        return outDto;
    }

    /**
     * 画面履歴の削除処理
     * 関数名：deleteAll
     *
     * @param inDto 削除パラメータ
     * @return 結果ステータス
     * @throws Exception 例外
     */
    public AssessmentInterRAIUpdateServiceOutDto deleteAll(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        // 戻り情報を設定
        AssessmentInterRAIUpdateServiceOutDto outDto = new AssessmentInterRAIUpdateServiceOutDto();
        // 28-xx インターライ方式_履歴
        deleteCpnTucRaiRrk(inDto);
        // 28-xx インターライ方式_アセスメント_A基本情報
        deleteCpnTucRaiAssA(inDto);
        // 28-xx インターライ方式_アセスメント_B基本情報
        deleteCpnTucRaiAssB(inDto);
        // 28-xx インターライ方式_アセスメント_C基本情報
        deleteCpnTucRaiAssC(inDto);
        // 28-xx インターライ方式_アセスメント_D基本情報
        deleteCpnTucRaiAssD(inDto);
        // 28-xx インターライ方式_アセスメント_E基本情報
        deleteCpnTucRaiAssE(inDto);
        // 28-xx インターライ方式_アセスメント_F基本情報
        deleteCpnTucRaiAssF(inDto);
        // 28-xx インターライ方式_アセスメント_G基本情報
        deleteCpnTucRaiAssG(inDto);
        // 28-xx インターライ方式_アセスメント_H基本情報
        deleteCpnTucRaiAssH(inDto);
        // 28-xx インターライ方式_アセスメント_I基本情報
        deleteCpnTucRaiAssI(inDto);
        // 28-xx インターライ方式_アセスメント_I1基本情報
        deleteCpnTucRaiAssI2(inDto);
        // 28-xx インターライ方式_アセスメント_J基本情報
        deleteCpnTucRaiAssJ(inDto);
        // 28-xx インターライ方式_アセスメント_K基本情報
        deleteCpnTucRaiAssK(inDto);
        // 28-xx インターライ方式_アセスメント_L基本情報
        deleteCpnTucRaiAssL(inDto);
        // 28-xx インターライ方式_アセスメント_M基本情報
        deleteCpnTucRaiAssM(inDto);
        // 28-xx インターライ方式_アセスメント_N基本情報
        deleteCpnTucRaiAssN(inDto);
        // 28-xx インターライ方式_アセスメント_N1基本情報
        deleteCpnTucRaiAssN1(inDto);
        // 28-xx インターライ方式_アセスメント_O基本情報
        deleteCpnTucRaiAssO(inDto);
        // 28-xx インターライ方式_アセスメント_P基本情報
        deleteCpnTucRaiAssP(inDto);
        // 28-xx インターライ方式_アセスメント_Q基本情報
        deleteCpnTucRaiAssQ(inDto);
        // 28-xx インターライ方式_アセスメント_R基本情報
        deleteCpnTucRaiAssR(inDto);
        // 28-xx インターライ方式_アセスメント_S基本情報
        deleteCpnTucRaiAssS(inDto);
        // 28-xx インターライ方式_アセスメント_T基本情報
        deleteCpnTucRaiAssT(inDto);
        // 28-xx インターライ方式_アセスメント_U基本情報
        deleteCpnTucRaiAssU(inDto);
        // 28-xx インターライ方式_アセスメント_V基本情報
        deleteCpnTucRaiAssV(inDto);
        // 28-xx インターライ方式_CAP選定_結果
        KghCpnRaiMonKentSumSPOutEntity selectCap1 = selectCap1(inDto);
        deleteCap1(inDto, selectCap1);
        // 28-xx インターライ方式_CAP検討_問題と指針
        deleteCpnTucRaiPlan1(inDto);

        return outDto;
    }

    /**
     * 28-xx インターライ方式_履歴テーブル
     * 関数名：deleteCpnTucRaiRrk
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiRrk(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiRrkCriteria criteria = new CpnTucRaiRrkCriteria();
        criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiRrkMapper.deleteByCriteria(criteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_A基本情報
     * 関数名：deleteCpnTucRaiAssA
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssA(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssACriteria cpnTucRaiAssACriteria = new CpnTucRaiAssACriteria();
        cpnTucRaiAssACriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssAMapper.deleteByCriteria(cpnTucRaiAssACriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_B基本情報
     * 関数名：deleteCpnTucRaiAssB
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssB(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssBCriteria cpnTucRaiAssBCriteria = new CpnTucRaiAssBCriteria();
        cpnTucRaiAssBCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssBMapper.deleteByCriteria(cpnTucRaiAssBCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_C基本情報
     * 関数名：deleteCpnTucRaiAssC
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssC(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssCCriteria cpnTucRaiAssCCriteria = new CpnTucRaiAssCCriteria();
        cpnTucRaiAssCCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssCMapper.deleteByCriteria(cpnTucRaiAssCCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_D基本情報
     * 関数名：deleteCpnTucRaiAssD
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssD(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssDCriteria cpnTucRaiAssDCriteria = new CpnTucRaiAssDCriteria();
        cpnTucRaiAssDCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssDMapper.deleteByCriteria(cpnTucRaiAssDCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_E基本情報
     * 関数名：deleteCpnTucRaiAssE
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssE(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssECriteria cpnTucRaiAssECriteria = new CpnTucRaiAssECriteria();
        cpnTucRaiAssECriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));
        // DAOを実行
        cpnTucRaiAssEMapper.deleteByCriteria(cpnTucRaiAssECriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_F基本情報
     * 関数名：deleteCpnTucRaiAssF
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssF(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssFCriteria cpnTucRaiAssFCriteria = new CpnTucRaiAssFCriteria();
        cpnTucRaiAssFCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssFMapper.deleteByCriteria(cpnTucRaiAssFCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_G基本情報
     * 関数名：deleteCpnTucRaiAssG
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssG(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssGCriteria cpnTucRaiAssGCriteria = new CpnTucRaiAssGCriteria();
        cpnTucRaiAssGCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssGMapper.deleteByCriteria(cpnTucRaiAssGCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_A基本情報
     * 関数名：deleteCpnTucRaiAssH
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssH(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssHCriteria cpnTucRaiAssHCriteria = new CpnTucRaiAssHCriteria();
        cpnTucRaiAssHCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssHMapper.deleteByCriteria(cpnTucRaiAssHCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_I基本情報
     * 関数名：deleteCpnTucRaiAssI
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssI(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssICriteria cpnTucRaiAssICriteria = new CpnTucRaiAssICriteria();
        cpnTucRaiAssICriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssIMapper.deleteByCriteria(cpnTucRaiAssICriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_I2基本情報
     * 関数名：deleteCpnTucRaiAssI2
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssI2(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssI2Criteria cpnTucRaiAssI2Criteria = new CpnTucRaiAssI2Criteria();
        cpnTucRaiAssI2Criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssI2Mapper.deleteByCriteria(cpnTucRaiAssI2Criteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_J基本情報
     * 関数名：deleteCpnTucRaiAssJ
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssJ(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssJCriteria cpnTucRaiAssJCriteria = new CpnTucRaiAssJCriteria();
        cpnTucRaiAssJCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssJMapper.deleteByCriteria(cpnTucRaiAssJCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_K基本情報
     * 関数名：deleteCpnTucRaiAssK
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssK(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssKCriteria cpnTucRaiAssKCriteria = new CpnTucRaiAssKCriteria();
        cpnTucRaiAssKCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssKMapper.deleteByCriteria(cpnTucRaiAssKCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_L基本情報
     * 関数名：deleteCpnTucRaiAssL
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssL(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssLCriteria cpnTucRaiAssLCriteria = new CpnTucRaiAssLCriteria();
        cpnTucRaiAssLCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssLMapper.deleteByCriteria(cpnTucRaiAssLCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_M基本情報
     * 関数名：deleteCpnTucRaiAssM
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssM(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssMCriteria cpnTucRaiAssMCriteria = new CpnTucRaiAssMCriteria();
        cpnTucRaiAssMCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssMMapper.deleteByCriteria(cpnTucRaiAssMCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_N基本情報
     * 関数名：deleteCpnTucRaiAssN
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssN(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssNCriteria cpnTucRaiAssNCriteria = new CpnTucRaiAssNCriteria();
        cpnTucRaiAssNCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssNMapper.deleteByCriteria(cpnTucRaiAssNCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_N1基本情報
     * 関数名：deleteCpnTucRaiAssN1
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssN1(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssN1Criteria cpnTucRaiAssN1Criteria = new CpnTucRaiAssN1Criteria();
        cpnTucRaiAssN1Criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssN1Mapper.deleteByCriteria(cpnTucRaiAssN1Criteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_O基本情報
     * 関数名：deleteCpnTucRaiAssO
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssO(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssOCriteria cpnTucRaiAssOCriteria = new CpnTucRaiAssOCriteria();
        cpnTucRaiAssOCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssOMapper.deleteByCriteria(cpnTucRaiAssOCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_P基本情報
     * 関数名：deleteCpnTucRaiAssP
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssP(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssPCriteria cpnTucRaiAssPCriteria = new CpnTucRaiAssPCriteria();
        cpnTucRaiAssPCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssPMapper.deleteByCriteria(cpnTucRaiAssPCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_Q基本情報
     * 関数名：deleteCpnTucRaiAssQ
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssQ(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssQCriteria cpnTucRaiAssQCriteria = new CpnTucRaiAssQCriteria();
        cpnTucRaiAssQCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssQMapper.deleteByCriteria(cpnTucRaiAssQCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_R基本情報
     * 関数名：deleteCpnTucRaiAssR
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssR(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssRCriteria cpnTucRaiAssRCriteria = new CpnTucRaiAssRCriteria();
        cpnTucRaiAssRCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssRMapper.deleteByCriteria(cpnTucRaiAssRCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_S基本情報
     * 関数名：deleteCpnTucRaiAssS
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssS(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssSCriteria cpnTucRaiAssSCriteria = new CpnTucRaiAssSCriteria();
        cpnTucRaiAssSCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssSMapper.deleteByCriteria(cpnTucRaiAssSCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_T基本情報
     * 関数名：deleteCpnTucRaiAssT
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssT(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssTCriteria cpnTucRaiAssTCriteria = new CpnTucRaiAssTCriteria();
        cpnTucRaiAssTCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssTMapper.deleteByCriteria(cpnTucRaiAssTCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_U基本情報
     * 関数名：deleteCpnTucRaiAssU
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssU(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssUCriteria cpnTucRaiAssUCriteria = new CpnTucRaiAssUCriteria();
        cpnTucRaiAssUCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssUMapper.deleteByCriteria(cpnTucRaiAssUCriteria);
    }

    /**
     * 28-xx インターライ方式_アセスメント_V基本情報
     * 関数名：deleteCpnTucRaiAssV
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiAssV(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiAssVCriteria cpnTucRaiAssVCriteria = new CpnTucRaiAssVCriteria();
        cpnTucRaiAssVCriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiAssVMapper.deleteByCriteria(cpnTucRaiAssVCriteria);
    }

    /**
     * 28-xx インターライ方式_CAP検討_問題と指針
     * 関数名：deleteCpnTucRaipLAN1
     *
     * @param inDto 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteCpnTucRaiPlan1(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        CpnTucRaiPlan1Criteria cpnTucRaiPlan1Criteria = new CpnTucRaiPlan1Criteria();
        cpnTucRaiPlan1Criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        cpnTucRaiPlan1Mapper.deleteByCriteria(cpnTucRaiPlan1Criteria);
    }

    /**
     * アセスメント(インターライ)画面の履歴削除処理を行う
     * 関数名：deleteRrk
     *
     * @param inDto 削除パラメータ
     * @return 結果ステータス
     * @throws Exception 例外
     */
    private AssessmentInterRAIUpdateServiceOutDto deleteRrk(AssessmentInterRAIUpdateServiceInDto inDto) {
        // リクエストパラメータ.履歴更新区分が"D":削除の場合、アセスメント単位の削除処理を行う。
        // 戻り情報を設定
        AssessmentInterRAIUpdateServiceOutDto outDto = new AssessmentInterRAIUpdateServiceOutDto();

        CpnTucRaiRrkCriteria criteria = new CpnTucRaiRrkCriteria();
        criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        this.cpnTucRaiRrkMapper.deleteByCriteria(criteria);
        return outDto;
    }

    /**
     * 【28-xx インターライ方式_CAP選定_結果】情報を削除更新する
     * 関数名：deleteCap1
     *
     * @param inDto      削除パラメータ
     * @param cap1Entity 28-xx インターライ方式_CAP選定_結果情報
     * @return 結果ステータス
     * @throws Exception 例外
     */
    public AssessmentInterRAIUpdateServiceOutDto deleteCap1(AssessmentInterRAIUpdateServiceInDto inDto,
            KghCpnRaiMonKentSumSPOutEntity cap1Entity)
            throws Exception {
        // 戻り情報を設定
        AssessmentInterRAIUpdateServiceOutDto outDto = new AssessmentInterRAIUpdateServiceOutDto();
        CpnTucRaiCap1Criteria criteria = new CpnTucRaiCap1Criteria();
        criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        // DAOを実行
        this.cpnTucRaiCap1Mapper.deleteByCriteria(criteria);
        return outDto;
    }

    /**
     * 【28-xx インターライ方式_CAP検討_課題と目標】情報を削除更新する
     * 関数名：deletePlan2
     *
     * @param inDto     削除パラメータ
     * @param plan2List 28-xx インターライ方式_CAP検討_課題と目標情報リスト
     * @return 結果ステータス
     * @throws Exception 例外
     */
    private AssessmentInterRAIUpdateServiceOutDto deletePlan2(AssessmentInterRAIUpdateServiceInDto inDto,
            List<KghCpnRaiMonKentPln2LeOutEntity> plan2List) {
        // 戻り情報を設定
        AssessmentInterRAIUpdateServiceOutDto outDto = new AssessmentInterRAIUpdateServiceOutDto();
        for (KghCpnRaiMonKentPln2LeOutEntity entity : plan2List) {
            CpnTucRaiPlan2Criteria criteria = new CpnTucRaiPlan2Criteria();
            criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                    .andCapIdEqualTo(entity.getCapId().intValue())
                    .andKadaiIdEqualTo(entity.getKadaiId());

            // DAOを実行
            this.cpnTucRaiPlan2Mapper.deleteByCriteria(criteria);
        }
        return outDto;
    }

    /**
     * 履歴情報のセクション状態をクリアする。
     * 関数名：clearRrkSection
     *
     * @param inDto 更新パラメータ
     * @return 結果ステータス
     * @throws Exception 例外
     */
    public AssessmentInterRAIUpdateServiceOutDto clearRrkSection(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        // 戻り情報を設定
        AssessmentInterRAIUpdateServiceOutDto outDto = new AssessmentInterRAIUpdateServiceOutDto();

        CpnTucRaiRrkCriteria criteria = new CpnTucRaiRrkCriteria();
        criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));
        CpnTucRaiRrk cpnTucRaiRrk = new CpnTucRaiRrk();
        switch (inDto.getSubKbn()) {
            case CommonConstants.SUB_A:
                // セクションＡ状態
                cpnTucRaiRrk.setSection1(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_B:
                // セクションＢ状態
                cpnTucRaiRrk.setSection2(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_C:
                // セクションＣ状態
                cpnTucRaiRrk.setSection3(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_D:
                // セクションＤ状態
                cpnTucRaiRrk.setSection4(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_E:
                // セクションＥ状態
                cpnTucRaiRrk.setSection5(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_F:
                // セクションＦ状態
                cpnTucRaiRrk.setSection6(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_G:
                // セクションＧ状態
                cpnTucRaiRrk.setSection7(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_H:
                // セクションＨ状態
                cpnTucRaiRrk.setSection8(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_I_1:
            case CommonConstants.SUB_I_2:
                // セクションＩ状態
                cpnTucRaiRrk.setSection9(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_J:
                // セクションＪ状態
                cpnTucRaiRrk.setSection10(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_K:
                // セクションＫ状態
                cpnTucRaiRrk.setSection11(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_L:
                // セクションＬ状態
                cpnTucRaiRrk.setSection12(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_M:
                // セクションＭ状態
                cpnTucRaiRrk.setSection13(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_N:
                // セクションＮ状態
                cpnTucRaiRrk.setSection14(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_O:
                // セクションＯ状態
                cpnTucRaiRrk.setSection15(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_P:
                // セクションＰ状態
                cpnTucRaiRrk.setSection16(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_Q:
                // セクションＱ状態
                cpnTucRaiRrk.setSection17(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_R:
                // セクションＲ状態
                cpnTucRaiRrk.setSection18(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_S:
                // セクションＳ状態
                cpnTucRaiRrk.setSection19(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_T:
                // セクションＴ状態
                cpnTucRaiRrk.setSection20(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_U:
                // セクションＵ状態
                cpnTucRaiRrk.setSection21(CommonConstants.BLANK_STRING);
                break;
            case CommonConstants.SUB_V:
                // セクションＶ状態
                cpnTucRaiRrk.setSection22(CommonConstants.BLANK_STRING);
                break;

            default:
                break;
        }

        // DAOを実行
        this.cpnTucRaiRrkMapper.updateByCriteriaSelective(cpnTucRaiRrk,
                criteria);

        return outDto;
    }

    /**
     * 履歴情報の選定アセスメント種別などをクリアする。
     * 関数名：clearRrkCap
     *
     * @param inDto 更新パラメータ
     * @return 結果ステータス
     * @throws Exception 例外
     */
    public AssessmentInterRAIUpdateServiceOutDto clearRrkCap(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        // 戻り情報を設定
        AssessmentInterRAIUpdateServiceOutDto outDto = new AssessmentInterRAIUpdateServiceOutDto();

        CpnTucRaiRrkCriteria criteria = new CpnTucRaiRrkCriteria();
        criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

        CpnTucRaiRrk cpnTucRaiRrk = cpnTucRaiRrkMapper
                .selectByPrimaryKey(CommonDtoUtil.strValToInt(inDto.getRaiId()));
        // 選定アセスメント種別
        cpnTucRaiRrk.setCapType(null);
        // 選定日
        cpnTucRaiRrk.setCapDateYmd(null);
        // 選定者
        cpnTucRaiRrk.setCapShokuId(null);

        // DAOを実行
        int count = this.cpnTucRaiRrkMapper.updateByCriteria(cpnTucRaiRrk, criteria);

        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
        return outDto;
    }

    /**
     * バリデーションチェックを行う。
     * 関数名：checkEdocument
     *
     * @param inDto 更新パラメータ
     * @return 電子ファイル保存設定フラグと結果outDto
     */
    public Pair<Boolean, AssessmentInterRAIUpdateServiceOutDto> checkEdocument(
            AssessmentInterRAIUpdateServiceInDto inDto) {
        AssessmentInterRAIUpdateServiceOutDto outDto = new AssessmentInterRAIUpdateServiceOutDto();

        // 2.1. 電子ファイル保存設定フラグを取得する
        Boolean insertSettingFlg = this.kghSmglHistoryLogic.getKghComEBunshoKbn();

        List<CpnTucRaiRrkInfoOutEntity> cpnTucRaiRrkInfoList = new ArrayList<>();

        // 2.2. 上記処理「2.1. 」で取得した電子ファイル保存設定フラグが「True」の場合、既存の履歴番号を更新確認
        if (insertSettingFlg) {
            // 2.2.1. リクエストパラメータ.計画対象期間IDがある場合、下記のインターライ方式_履歴情報取得のDAOを利用し、履歴情報リストを取得する。
            if (StringUtil.isNotEmpty(inDto.getSc1Id())) {
                CpnTucRaiRrkInfoByCriteriaInEntity cpnTucRaiRrkInfoByCriteriaInEntity = new CpnTucRaiRrkInfoByCriteriaInEntity();
                // 計画期間ID
                cpnTucRaiRrkInfoByCriteriaInEntity.setAnSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
                // 事業者ID
                cpnTucRaiRrkInfoByCriteriaInEntity.setAnJId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                // 利用者ID
                cpnTucRaiRrkInfoByCriteriaInEntity.setAnUId(CommonDtoUtil.strValToInt(inDto.getUserId()));

                cpnTucRaiRrkInfoList = this.cpnTucRaiRrkSelectMapper
                        .findCpnTucRaiRrkInfoByCriteria(cpnTucRaiRrkInfoByCriteriaInEntity);

            }

            // 2.2.2. 既存の履歴番号が変更を判断する
            SmglYmdInDto smglYmdInDto = new SmglYmdInDto();
            // 履歴番号
            smglYmdInDto.setKrirekiNo(CommonDtoUtil.strValToInt(inDto.getHistoryNo()));
            // 作成日
            smglYmdInDto.setCreateYmd(inDto.getKijunbiYmd());

            // 削除フラグ
            Integer processFlg = null;
            if (CommonDtoUtil.isUpdate(inDto)) {
                processFlg = CommonConstants.NUMBER_0;

            } else if (CommonDtoUtil.isCreate(inDto)) {
                processFlg = CommonConstants.NUMBER_1;

            } else if (CommonDtoUtil.isDelete(inDto)) {
                processFlg = CommonConstants.NUMBER_2;

            }

            // 履歴リスト
            List<Object> krireki = new ArrayList<>();
            krireki.addAll(cpnTucRaiRrkInfoList);

            boolean resultFlg = this.kghKrkNokikanOpeLogic.chkSmglYmd(CommonDtoUtil.strValToInt(inDto.getKinouId()),
                    processFlg,
                    smglYmdInDto, krireki);

            // 2.2.2.1.
            // 「2.2.2.」の結果フラグが「false：失敗」の場合、下記返却する情報を設定して、後続処理を飛ばして、API処理を正常終了とする。
            if (!resultFlg) {
                // 計画対象期間ID
                outDto.setSc1Id(inDto.getSc1Id());
                // アセスメントID
                outDto.setRaiId(inDto.getRaiId());
                // エラー区分
                outDto.setErrKbn(CommonConstants.STR_1);

            }

        }

        return Pair.of(insertSettingFlg, outDto);

    }

    /**
     * e-文書の履歴保存処理
     * 関数名：insertEdocument
     * 
     * @param inDto            更新パラメータ
     * @param insertSettingFlg 電子ファイル保存設定フラグ
     * @return 結果outDto
     */
    @SuppressWarnings("unchecked")
    public AssessmentInterRAIUpdateServiceOutDto insertEdocument(AssessmentInterRAIUpdateServiceInDto inDto,
            boolean insertSettingFlg) throws Exception {
        AssessmentInterRAIUpdateServiceOutDto outDto = new AssessmentInterRAIUpdateServiceOutDto();

        // 8.1.変数.電子ファイル保存設定フラグが「true」の場合、e-文書の履歴保存処理を実施する（処理8.1以降については、新しいトランザクションで実行）
        if (insertSettingFlg) {
            // 8.1.1 印刷設定オプション取得
            // ※API定義書_APINo(959)_初期情報取得の「処理2～3」を参照
            // API定義書_APINo(959)_初期情報取得.xlsx」の「2.」
            List<PrintSettingInfoDto> saveBeforePrtList = new ArrayList<PrintSettingInfoDto>();

            // 2.印刷設定情報リストを取得する。
            Pair<Boolean, List<PrintSettingInfoDto>> resultPrtList = this.printSettingLogic.getInitPrtList(
                    inDto.getSysRyaku(),
                    inDto.getSectionName(),
                    inDto.getShokuId(),
                    inDto.getHoujinId(),
                    inDto.getShisetuId(),
                    inDto.getSvJigyoId());

            saveBeforePrtList = resultPrtList.getRight();

            // API定義書_APINo(959)_初期情報取得.xlsx」の「3.」
            // 3.印刷設定保存情報を保存する
            FreeAssessmentFacePrintSettingsUpdateServiceOutDto freeAssessmentFacePrintSettingsUpdateServiceOutDto = this.printSettingLogic
                    .insertPrtList(
                            saveBeforePrtList,
                            inDto.getSysRyaku(),
                            inDto.getSectionName(),
                            inDto.getShokuId(),
                            inDto.getHoujinId(),
                            inDto.getShisetuId(),
                            inDto.getSvJigyoId(),
                            inDto.getIndex(),
                            inDto.getGsyscd(),
                            CommonConstants.STR_0,
                            CommonConstants.STR_0);

            List<PrintSettingInfoDto> printSettingInfoList = freeAssessmentFacePrintSettingsUpdateServiceOutDto
                    .getPrtList();

            // ・上記処理取得した印刷設定情報リスト[0].プロファイルが「”No”」の場合、レスポンス.エラー区分が「2」を設定し、「API定義」の8の処理へジャンプする。
            if (CommonDtoUtil.checkStringEqual(
                    printSettingInfoList.get(0).getProfile(), CommonConstants.PRO_FILE_NAME_INIT)) {
                outDto.setErrKbn(CommonConstants.STR_2);
                return outDto;
            }

            // 8.1.2 S3e文書保存処理共通関数のパラメータを編集
            // 8.1.2.1. 変数.パラメータリストを初期化する
            List<InsertEdocumentParamDto> insertEdocumentParamDtoList = new ArrayList<>();

            // 8.1.2.2. リクエストパラメータ.履歴更新区分が「D:削除」の場合
            if (CommonDtoUtil.isHistoryDelete(inDto)) {
                // 8.1.2.2.1 取得した印刷設定情報リスト（A～Vタブ）を繰り返し、各タブのパラメータリストを作成
                if (CollectionUtils.isNotEmpty(printSettingInfoList)) {
                    printSettingInfoList.forEach(printSettingInfo -> {
                        // ※印刷設定情報リスト[ループINDEX].帳票リスト名が「アセスメント表（全て）」の場合、該当レコードを処理対象外である
                        if (!CommonDtoUtil.checkStringEqual(printSettingInfo.getProfile(),
                                CommonConstants.PRT_TITLE_ASSESSMENT_ALL)) {
                            InsertEdocumentParamDto insertEdocumentParam = this.setInsertEdocumentParamOfDeleteDto(
                                    inDto, printSettingInfo);
                            insertEdocumentParamDtoList.add(insertEdocumentParam);
                        }
                    });
                }
            } else {
                // 8.1.2.3. 上記以外の場合
                // 8.1.2.3.1リクエストパラメータ.更新区分が「D:削除」の場合
                if (CommonDtoUtil.isDelete(inDto)) {
                    // ※本機能対するタブ（A）のパラメータリストの作成が上記「7.1.2.2. 」の編集仕様を参照
                    // ※対象判断条件 ： 印刷設定情報リスト[ループINDEX].インデックス ＝ リクエストパラメータ.インデックス
                    if (CollectionUtils.isNotEmpty(printSettingInfoList)) {
                        printSettingInfoList.forEach(printSettingInfo -> {
                            if (CommonDtoUtil.checkStringEqual(printSettingInfo.getIndex(), inDto.getIndex())) {
                                InsertEdocumentParamDto insertEdocumentParam = this.setInsertEdocumentParamOfDeleteDto(
                                        inDto, printSettingInfo);
                                insertEdocumentParamDtoList.add(insertEdocumentParam);
                            }
                        });
                    }
                } else {
                    // 8.1.2.3.2 上記以外の場合
                    if (CollectionUtils.isNotEmpty(printSettingInfoList)) {
                        printSettingInfoList.forEach(printSettingInfo -> {
                            if (CommonDtoUtil.checkStringEqual(printSettingInfo.getIndex(), inDto.getIndex())) {
                                InsertEdocumentParamDto insertEdocumentParam = this.setInsertEdocumentParamDto(
                                        inDto, printSettingInfo);
                                insertEdocumentParamDtoList.add(insertEdocumentParam);
                            }
                        });
                    }
                }
            }

            // 8.1.3 変数.パラメータリストを繰り返し、S3e文書保存処理共通関数を呼び出し
            for (int i = 1; i < insertEdocumentParamDtoList.size(); i++) {
                InsertEdocumentParamDto insertEdocumentParam = insertEdocumentParamDtoList.get(i);
                this.cmnStorageServiceUtil.uploadToS3EDocument(insertEdocumentParam.getReportId(),
                        insertEdocumentParam.getReportServiceeDeleteInDto(), insertEdocumentParam.getLoginId(),
                        insertEdocumentParam.getEDocumentUpdKbn(), insertEdocumentParam.getRirekiNo(),
                        insertEdocumentParam.getSc1Id());
            }
        }

        return outDto;
    }

    /**
     * 削除の場合e-文書の履歴保存処理パラメータを編集
     * setInsertEdocumentParamOfDeleteDto
     * 
     * @param inDto            更新パラメータ
     * @param insertSettingFlg 電子ファイル保存設定フラグ
     * @return 結果outDto
     */
    private InsertEdocumentParamDto setInsertEdocumentParamOfDeleteDto(AssessmentInterRAIUpdateServiceInDto inDto,
            PrintSettingInfoDto printSettingInfo) {
        InsertEdocumentParamDto insertEdocumentParam = new InsertEdocumentParamDto();
        // 帳票ID
        insertEdocumentParam.setReportId(CommonConstants.EDOCUMENT_DELETE_REPORT);
        // ログインID
        insertEdocumentParam.setLoginId(inDto.getShokuId());
        // E文書更新区分
        insertEdocumentParam.setEDocumentUpdKbn(CommonConstants.UPDATE_KBN_D);
        // 履歴番号
        insertEdocumentParam.setRirekiNo(inDto.getHistoryNo());
        // 計画対象期間ID
        insertEdocumentParam.setSc1Id(inDto.getSc1Id());

        // 帳票サービスInDto
        EdocumentDeleteReportParameterModel reportServiceeDeleteInDto = new EdocumentDeleteReportParameterModel();
        // 帳票区分
        reportServiceeDeleteInDto.setLedgerKbn(CommonConstants.LEDGER_KBN_1);
        // 帳票名
        reportServiceeDeleteInDto.setLedgerName(printSettingInfo.getProfile());

        if (CommonDtoUtil.checkStringEqual(inDto.getKikanKanriFlg(),
                CommonConstants.PERIOD_NO_MANAGE_FLG)) {
            // 計画対象期間番号
            // リクエストパラメータ.期間管理フラグが「0：対象管理しない」の場合、"000"を設定
            reportServiceeDeleteInDto.setPlanningPeriodNumber(CommonConstants.PLAN_NUMBER);

            // 計画対象期間（自）～（至）
            // リクエストパラメータ.期間管理フラグが「0：対象管理しない」の場合、""を設定
            reportServiceeDeleteInDto.setPlanningPeriodStartToEnd(CommonConstants.BLANK_STRING);
        } else {
            // 上記以外の場合、リクエストパラメータ.期間番号を設定
            // ※数字が3桁未満の場合、先頭「0」を埋め
            reportServiceeDeleteInDto
                    .setPlanningPeriodNumber(String.format(CommonConstants.ZERO_THREE_STRING,
                            inDto.getSc1Id()));

            // 計画対象期間（自）～（至）
            // 上記以外の場合、リクエストパラメータ.開始日 + "～" + リクエストパラメータ.終了日
            reportServiceeDeleteInDto.setPlanningPeriodStartToEnd(
                    inDto.getStartYmd() + CommonConstants.RANGE_SEPARATOR + inDto.getEndYmd());
        }

        // 履歴番号
        reportServiceeDeleteInDto.setHistoryNumber(String.format(CommonConstants.ZERO_FIVE_STRING,
                inDto.getHistoryNo()));

        insertEdocumentParam.setReportServiceeDeleteInDto(reportServiceeDeleteInDto);

        return insertEdocumentParam;
    }

    /**
     * 削除以外の場合e-文書の履歴保存処理パラメータを編集
     * 関数名：setInsertEdocumentParamDto
     * 
     * @param inDto            更新パラメータ
     * @param insertSettingFlg 電子ファイル保存設定フラグ
     * @return 結果outDto
     */
    private InsertEdocumentParamDto setInsertEdocumentParamDto(AssessmentInterRAIUpdateServiceInDto inDto,
            PrintSettingInfoDto printSettingInfo) {
        InsertEdocumentParamDto insertEdocumentParam = new InsertEdocumentParamDto();
        // 帳票ID
        insertEdocumentParam
                .setReportId(CommonConstants.ASSESSMENT_SHEET + inDto.getTabName() + CommonConstants.REPORT);
        // ログインID
        insertEdocumentParam.setLoginId(inDto.getShokuId());
        // E文書更新区分
        insertEdocumentParam.setEDocumentUpdKbn(inDto.getUpdateKbn());
        // 履歴番号
        insertEdocumentParam.setRirekiNo(inDto.getHistoryNo());
        // 計画対象期間ID
        insertEdocumentParam.setSc1Id(inDto.getSc1Id());

        // 帳票サービスInDto
        AssessmentSheetReportParameterModel reportServiceeInDto = new AssessmentSheetReportParameterModel();
        // 事業者名
        reportServiceeInDto.setSvJigyoKnj(inDto.getSvJigyoName());
        // システムコード
        reportServiceeInDto.setSyscd(inDto.getGsyscd());

        // 印刷設定
        ReportCommonPrintSet printSet = new ReportCommonPrintSet();
        // 指定日印刷区分
        printSet.setShiTeiKubun(printSettingInfo.getPrnDate());
        // 指定日
        // Format -> yyyy/MM/dd
        DateTimeFormatter format_slash = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
        printSet.setShiTeiDate(LocalDate.now().format(format_slash));
        reportServiceeInDto.setPrintSet(printSet);

        // 印刷オプション
        ReportAssessmentSheetPrintOption printOption = new ReportAssessmentSheetPrintOption();
        // 記入用シートを印刷するフラグ
        printOption.setEmptyFlg(CommonConstants.STR_FALSE);
        // 記入用シートアセスメント種別
        printOption.setKinyuAssType(inDto.getAssType());
        // 印刷時に色をつけるフラグ
        printOption.setColorFlg(printSettingInfo.getParam05());
        reportServiceeInDto.setPrintOption(printOption);

        // 印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = new ArrayList<>();
        ReportCommonPrintSubjectHistory reportCommonPrintSubjectHistory = new ReportCommonPrintSubjectHistory();
        // 利用者ID
        reportCommonPrintSubjectHistory.setUserId(inDto.getUserId());
        // 期間ID
        reportCommonPrintSubjectHistory.setSc1Id(inDto.getSc1Id());
        // 開始日
        reportCommonPrintSubjectHistory.setStartYmd(inDto.getStartYmd());
        // 終了日
        reportCommonPrintSubjectHistory.setEndYmd(inDto.getEndYmd());
        // アセスメントID
        reportCommonPrintSubjectHistory.setRaiId(inDto.getRaiId());
        // 調査アセスメント種別
        reportCommonPrintSubjectHistory.setAssType(inDto.getAssType());
        // 出力帳票印刷情報リスト
        List<ReportCommonChoPrt> choPrtList = new ArrayList<>();
        choPrtList.add(this.setChoPrt(printSettingInfo));

        reportCommonPrintSubjectHistory.setChoPrtList(choPrtList);

        reportServiceeInDto.setPrintSubjectHistoryList(printSubjectHistoryList);

        insertEdocumentParam.setReportServiceeInDto(reportServiceeInDto);

        return insertEdocumentParam;
    }

    /**
     * 出力帳票印刷情報リストを編集
     * 関数名：setChoPrt
     * 
     * @param printSettingInfo 印刷設定情報
     * @return 出力帳票印刷情報リスト
     */
    private ReportCommonChoPrt setChoPrt(PrintSettingInfoDto printSettingInfo) {
        ReportCommonChoPrt reportCommonChoPrt = new ReportCommonChoPrt();
        // 職員ID */
        reportCommonChoPrt.setShokuId(printSettingInfo.getShokuId());
        // システム略称 */
        reportCommonChoPrt.setSysRyaku(printSettingInfo.getSysRyaku());
        // 出力帳票名 */
        reportCommonChoPrt.setDefPrtTitle(printSettingInfo.getDefPrtTitle());
        // 帳票タイトル */
        reportCommonChoPrt.setPrtTitle(printSettingInfo.getPrtTitle());
        // セクション */
        reportCommonChoPrt.setSection(printSettingInfo.getSectionNo());
        // 帳票番号 */
        reportCommonChoPrt.setPrtNo(printSettingInfo.getPrtNo());
        // プロファイル */
        reportCommonChoPrt.setChoPro(printSettingInfo.getProfile());
        // セクション名 */
        reportCommonChoPrt.setSectionName(CommonConstants.BLANK_STRING);
        // オブジェクト名 */
        reportCommonChoPrt.setDwobject(printSettingInfo.getDwobject());
        // 用紙向き */
        reportCommonChoPrt.setPrtOrient(printSettingInfo.getPrtOrient());
        // 用紙サイズ */
        reportCommonChoPrt.setPrtSize(printSettingInfo.getPrtSize());
        // 帳票リスト名 */
        reportCommonChoPrt.setListTitle(printSettingInfo.getListTitle());
        // 上余白 */
        reportCommonChoPrt.setMtop(printSettingInfo.getMtop());
        // 下余白 */
        reportCommonChoPrt.setMbottom(printSettingInfo.getMbottom());
        // 左余白 */
        reportCommonChoPrt.setMleft(printSettingInfo.getMleft());
        // 右余白 */
        reportCommonChoPrt.setMright(printSettingInfo.getMright());
        // ルーラ表示有無 */
        reportCommonChoPrt.setRuler(printSettingInfo.getRuler());
        // 日付表示有無 */
        reportCommonChoPrt.setPrndate(printSettingInfo.getPrnDate());
        // 職員表示有無 */
        reportCommonChoPrt.setPrnshoku(printSettingInfo.getPrnshoku());
        // シリアルフラグ */
        reportCommonChoPrt.setSerialFlg(printSettingInfo.getSerialFlg());
        // モードフラグ */
        reportCommonChoPrt.setModFlg(printSettingInfo.getModFlg());
        // セクションフラグ */
        reportCommonChoPrt.setSecFlg(printSettingInfo.getSecFlg());
        // 高さ */
        reportCommonChoPrt.setSerialHeight(printSettingInfo.getSerialHeight());
        // 印刷行数 */
        reportCommonChoPrt.setSerialPagelen(printSettingInfo.getSerialPagelen());
        // 表示内拡大率 */
        reportCommonChoPrt.setZoomRate(printSettingInfo.getZoomRate());
        // パラメータ01 */
        reportCommonChoPrt.setParam01(printSettingInfo.getParam01());
        // パラメータ02 */
        reportCommonChoPrt.setParam02(printSettingInfo.getParam02());
        // パラメータ03 */
        reportCommonChoPrt.setParam03(printSettingInfo.getParam03());
        // パラメータ04 */
        reportCommonChoPrt.setParam04(printSettingInfo.getParam04());
        // パラメータ05 */
        reportCommonChoPrt.setParam05(printSettingInfo.getParam05());
        // パラメータ06 */
        reportCommonChoPrt.setParam06(printSettingInfo.getParam06());
        // パラメータ07 */
        reportCommonChoPrt.setParam07(printSettingInfo.getParam07());
        // パラメータ08 */
        reportCommonChoPrt.setParam08(printSettingInfo.getParam08());
        // パラメータ09 */
        reportCommonChoPrt.setParam09(printSettingInfo.getParam09());
        // パラメータ10 */
        reportCommonChoPrt.setParam10(printSettingInfo.getParam10());
        // パラメータ11 */
        reportCommonChoPrt.setParam11(printSettingInfo.getParam11());
        // パラメータ12 */
        reportCommonChoPrt.setParam12(printSettingInfo.getParam12());
        // パラメータ13 */
        reportCommonChoPrt.setParam13(printSettingInfo.getParam13());
        // パラメータ14 */
        reportCommonChoPrt.setParam14(printSettingInfo.getParam14());
        // パラメータ15 */
        reportCommonChoPrt.setParam15(printSettingInfo.getParam15());
        // パラメータ16 */
        reportCommonChoPrt.setParam16(printSettingInfo.getParam16());
        // パラメータ17 */
        reportCommonChoPrt.setParam17(printSettingInfo.getParam17());
        // パラメータ18 */
        reportCommonChoPrt.setParam18(printSettingInfo.getParam18());
        // パラメータ19 */
        reportCommonChoPrt.setParam19(printSettingInfo.getParam19());
        // パラメータ20 */
        reportCommonChoPrt.setParam20(printSettingInfo.getParam20());
        // パラメータ21 */
        reportCommonChoPrt.setParam21(printSettingInfo.getParam21());
        // パラメータ22 */
        reportCommonChoPrt.setParam22(printSettingInfo.getParam22());
        // パラメータ23 */
        reportCommonChoPrt.setParam23(printSettingInfo.getParam23());
        // パラメータ24 */
        reportCommonChoPrt.setParam24(printSettingInfo.getParam24());
        // パラメータ25 */
        reportCommonChoPrt.setParam25(printSettingInfo.getParam25());
        // パラメータ26 */
        reportCommonChoPrt.setParam26(printSettingInfo.getParam26());
        // パラメータ27 */
        reportCommonChoPrt.setParam27(printSettingInfo.getParam27());
        // パラメータ28 */
        reportCommonChoPrt.setParam28(printSettingInfo.getParam28());
        // パラメータ29 */
        reportCommonChoPrt.setParam29(printSettingInfo.getParam29());
        // パラメータ30 */
        reportCommonChoPrt.setParam30(printSettingInfo.getParam30());
        // パラメータ31 */
        reportCommonChoPrt.setParam31(printSettingInfo.getParam31());
        // パラメータ32 */
        reportCommonChoPrt.setParam32(printSettingInfo.getParam32());
        // パラメータ33 */
        reportCommonChoPrt.setParam33(printSettingInfo.getParam33());
        // パラメータ34 */
        reportCommonChoPrt.setParam34(printSettingInfo.getParam34());
        // パラメータ35 */
        reportCommonChoPrt.setParam35(printSettingInfo.getParam35());
        // パラメータ36 */
        reportCommonChoPrt.setParam36(printSettingInfo.getParam36());
        // パラメータ37 */
        reportCommonChoPrt.setParam37(printSettingInfo.getParam37());
        // パラメータ38 */
        reportCommonChoPrt.setParam38(printSettingInfo.getParam38());
        // パラメータ39 */
        reportCommonChoPrt.setParam39(printSettingInfo.getParam39());
        // パラメータ40 */
        reportCommonChoPrt.setParam40(printSettingInfo.getParam40());
        // パラメータ41 */
        reportCommonChoPrt.setParam41(printSettingInfo.getParam41());
        // パラメータ42 */
        reportCommonChoPrt.setParam42(printSettingInfo.getParam42());
        // パラメータ43 */
        reportCommonChoPrt.setParam43(printSettingInfo.getParam43());
        // パラメータ44 */
        reportCommonChoPrt.setParam44(printSettingInfo.getParam44());
        // パラメータ45 */
        reportCommonChoPrt.setParam45(printSettingInfo.getParam45());
        // パラメータ46 */
        reportCommonChoPrt.setParam46(printSettingInfo.getParam46());
        // パラメータ47 */
        reportCommonChoPrt.setParam47(printSettingInfo.getParam47());
        // パラメータ48 */
        reportCommonChoPrt.setParam48(printSettingInfo.getParam48());
        // パラメータ49 */
        reportCommonChoPrt.setParam49(printSettingInfo.getParam49());
        // パラメータ50 */
        reportCommonChoPrt.setParam50(printSettingInfo.getParam50());

        return reportCommonChoPrt;

    }

    /**
     * サブ情報更新
     * 関数名：updateRrkSubInfo
     *
     * @param @param raiId アセスメントID
     * @param inDto  更新パラメータ
     * @return 結果outDto
     * @throws Exception
     */
    public void updateRrkSubInfo(String raiId,
            AssessmentInterRAIUpdateServiceInDto inDto) throws Exception {
        // 7.1.1.リクエストパラメータ.更新区分が"C":新規の場合
        if (CommonDtoUtil.isCreate(inDto)) {
            switch (inDto.getSubKbn()) {
                case CommonConstants.TAB_NAME_A:
                    // リクエストパラメータ.タブ名が”A”の場合
                    insertRrkSubInfoA(raiId, inDto.getSubInfoA());
                    break;
                case CommonConstants.TAB_NAME_B:
                    // リクエストパラメータ.タブ名が”B”の場合
                    insertRrkSubInfoB(raiId, inDto.getSubInfoB());
                    break;
                case CommonConstants.TAB_NAME_C:
                    // リクエストパラメータ.タブ名が”C”の場合
                    insertRrkSubInfoC(raiId, inDto.getSubInfoC());
                    break;
                case CommonConstants.TAB_NAME_D:
                    // リクエストパラメータ.タブ名が”D”の場合
                    insertRrkSubInfoD(raiId, inDto.getSubInfoD());
                    break;
                case CommonConstants.TAB_NAME_E:
                    // リクエストパラメータ.タブ名が”E”の場合
                    insertRrkSubInfoE(raiId, inDto.getSubInfoE());
                    break;
                case CommonConstants.TAB_NAME_F:
                    // リクエストパラメータ.タブ名が”F”の場合
                    insertRrkSubInfoF(raiId, inDto.getSubInfoF());
                    break;
                case CommonConstants.TAB_NAME_G:
                    // リクエストパラメータ.タブ名が”G”の場合
                    insertRrkSubInfoG(raiId, inDto.getSubInfoG());
                    break;
                case CommonConstants.TAB_NAME_H:
                    // リクエストパラメータ.タブ名が”H”の場合
                    insertRrkSubInfoH(raiId, inDto.getSubInfoH());
                    break;
                case CommonConstants.TAB_NAME_I1:
                    // リクエストパラメータ.タブ名が”I1”の場合
                    insertRrkSubInfoI1(raiId, inDto.getSubInfoI1());
                    break;
                case CommonConstants.TAB_NAME_I2:
                    // リクエストパラメータ.タブ名が”I2”の場合
                    insertRrkSubInfoI2(raiId, inDto);
                    break;
                case CommonConstants.TAB_NAME_J:
                    // リクエストパラメータ.タブ名が”J”の場合
                    insertRrkSubInfoJ(raiId, inDto.getSubInfoJ());
                    break;
                case CommonConstants.TAB_NAME_K:
                    // リクエストパラメータ.タブ名が”K”の場合
                    insertRrkSubInfoK(raiId, inDto.getSubInfoK());
                    break;
                case CommonConstants.TAB_NAME_L:
                    // リクエストパラメータ.タブ名が”L”の場合
                    insertRrkSubInfoL(raiId, inDto.getSubInfoL());
                    break;
                case CommonConstants.TAB_NAME_M:
                    // リクエストパラメータ.タブ名が”M”の場合
                    insertRrkSubInfoM(raiId, inDto.getSubInfoM());
                    break;
                case CommonConstants.TAB_NAME_N:
                    // リクエストパラメータ.タブ名が”N”の場合
                    insertRrkSubInfoN(raiId, inDto.getSubInfoN());
                    break;
                case CommonConstants.TAB_NAME_O:
                    // リクエストパラメータ.タブ名が”O”の場合
                    insertRrkSubInfoO(raiId, inDto.getSubInfoO());
                    break;
                case CommonConstants.TAB_NAME_P:
                    // リクエストパラメータ.タブ名が”P”の場合
                    insertRrkSubInfoP(raiId, inDto.getSubInfoP());
                    break;
                case CommonConstants.TAB_NAME_Q:
                    // リクエストパラメータ.タブ名が”Q”の場合
                    insertRrkSubInfoQ(raiId, inDto.getSubInfoQ());
                    break;
                case CommonConstants.TAB_NAME_R:
                    // リクエストパラメータ.タブ名が”R”の場合
                    insertRrkSubInfoR(raiId, inDto.getSubInfoR());
                    break;
                case CommonConstants.TAB_NAME_S:
                    // リクエストパラメータ.タブ名が”S”の場合
                    insertRrkSubInfoS(raiId, inDto.getSubInfoS());
                    break;
                case CommonConstants.TAB_NAME_T:
                    // リクエストパラメータ.タブ名が”T”の場合
                    insertRrkSubInfoT(raiId, inDto.getSubInfoT());
                    break;
                case CommonConstants.TAB_NAME_U:
                    // リクエストパラメータ.タブ名が”U”の場合
                    insertRrkSubInfoU(raiId, inDto.getSubInfoU());
                    break;
                case CommonConstants.TAB_NAME_V:
                    // リクエストパラメータ.タブ名が”V”の場合
                    insertRrkSubInfoV(raiId, inDto.getSubInfoV());
                    break;
                default:
                    break;
            }
        }
        // 7.1.2.リクエストパラメータ.更新区分が"U":更新の場合
        else if (CommonDtoUtil.isUpdate(inDto)) {
            switch (inDto.getSubKbn()) {
                case CommonConstants.TAB_NAME_A:
                    // リクエストパラメータ.タブ名が”A”の場合
                    updateRrkSubInfoA(raiId, inDto.getSubInfoA());
                    break;
                case CommonConstants.TAB_NAME_B:
                    // リクエストパラメータ.タブ名が”B”の場合
                    updateRrkSubInfoB(raiId, inDto.getSubInfoB());
                    break;
                case CommonConstants.TAB_NAME_C:
                    // リクエストパラメータ.タブ名が”C”の場合
                    updateRrkSubInfoC(raiId, inDto.getSubInfoC());
                    break;
                case CommonConstants.TAB_NAME_D:
                    // リクエストパラメータ.タブ名が”D”の場合
                    updateRrkSubInfoD(raiId, inDto.getSubInfoD());
                    break;
                case CommonConstants.TAB_NAME_E:
                    // リクエストパラメータ.タブ名が”E”の場合
                    updateRrkSubInfoE(raiId, inDto.getSubInfoE());
                    break;
                case CommonConstants.TAB_NAME_F:
                    // リクエストパラメータ.タブ名が”F”の場合
                    updateRrkSubInfoF(raiId, inDto.getSubInfoF());
                    break;
                case CommonConstants.TAB_NAME_G:
                    // リクエストパラメータ.タブ名が”G”の場合
                    updateRrkSubInfoG(raiId, inDto.getSubInfoG());
                    break;
                case CommonConstants.TAB_NAME_H:
                    // リクエストパラメータ.タブ名が”H”の場合
                    updateRrkSubInfoH(raiId, inDto.getSubInfoH());
                    break;
                case CommonConstants.TAB_NAME_I1:
                    // リクエストパラメータ.タ ブ名が”I”の場合
                    updateRrkSubInfoI1(raiId, inDto.getSubInfoI1());
                    break;
                case CommonConstants.TAB_NAME_I2:
                    // リクエストパラメータ.タブ名が”I”の場合
                    updateRrkSubInfoI2(raiId, inDto);
                    break;
                case CommonConstants.TAB_NAME_J:
                    // リクエストパラメータ.タブ名が”J”の場合
                    updateRrkSubInfoJ(raiId, inDto.getSubInfoJ());
                    break;
                case CommonConstants.TAB_NAME_K:
                    // リクエストパラメータ.タブ名が”K”の場合
                    updateRrkSubInfoK(raiId, inDto.getSubInfoK());
                    break;
                case CommonConstants.TAB_NAME_L:
                    // リクエストパラメータ.タブ名が”L”の場合
                    updateRrkSubInfoL(raiId, inDto.getSubInfoL());
                    break;
                case CommonConstants.TAB_NAME_M:
                    // リクエストパラメータ.タブ名が”M”の場合
                    updateRrkSubInfoM(raiId, inDto.getSubInfoM());
                    break;
                case CommonConstants.TAB_NAME_N:
                    // リクエストパラメータ.タブ名が”N”の場合
                    updateRrkSubInfoN(raiId, inDto.getSubInfoN());
                    break;
                case CommonConstants.TAB_NAME_O:
                    // リクエストパラメータ.タブ名が”O”の場合
                    updateRrkSubInfoO(raiId, inDto.getSubInfoO());
                    break;
                case CommonConstants.TAB_NAME_P:
                    // リクエストパラメータ.タブ名が”P”の場合
                    updateRrkSubInfoP(raiId, inDto.getSubInfoP());
                    break;
                case CommonConstants.TAB_NAME_Q:
                    // リクエストパラメータ.タブ名が”Q”の場合
                    updateRrkSubInfoQ(raiId, inDto.getSubInfoQ());
                    break;
                case CommonConstants.TAB_NAME_R:
                    // リクエストパラメータ.タブ名が”R”の場合
                    updateRrkSubInfoR(raiId, inDto.getSubInfoR());
                    break;
                case CommonConstants.TAB_NAME_S:
                    // リクエストパラメータ.タブ名が”S”の場合
                    updateRrkSubInfoS(raiId, inDto.getSubInfoS());
                    break;
                case CommonConstants.TAB_NAME_T:
                    // リクエストパラメータ.タブ名が”T”の場合
                    updateRrkSubInfoT(raiId, inDto.getSubInfoT());
                    break;
                case CommonConstants.TAB_NAME_U:
                    // リクエストパラメータ.タブ名が”U”の場合
                    updateRrkSubInfoU(raiId, inDto.getSubInfoU());
                    break;
                case CommonConstants.TAB_NAME_V:
                    // リクエストパラメータ.タブ名が”V”の場合
                    updateRrkSubInfoV(raiId, inDto.getSubInfoV());
                    break;
                default:
                    break;
            }
        }
        // 7.1.2.リクエストパラメータ.更新区分が"D"の場合
        else if (CommonDtoUtil.isDelete(inDto)) {
            // 6.3.1.2. リクエストパラメータ.タブ名が”I”の場合、個別処理する。
            if (CommonConstants.TAB_NAME_I.equals(inDto.getTabName())
                    && CommonConstants.TAB_NAME_I1.equals(inDto.getSubKbn())) {
                // ・上記の変数.値存在フラグ が4以外の場合
                if (!CommonConstants.STR_4.equals(flg)) {
                    CpnTucRaiAssICriteria cpnTucRaiAssICriteria = new CpnTucRaiAssICriteria();
                    cpnTucRaiAssICriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

                    // DAOを実行
                    cpnTucRaiAssIMapper.deleteByCriteria(cpnTucRaiAssICriteria);
                    // ・上記以外の場合
                } else {
                    CpnTucRaiAssI cpnTucRaiAssI = new CpnTucRaiAssI();
                    // 疾患_筋骨系_大腿骨骨折
                    cpnTucRaiAssI.setI1A(null);
                    // 疾患_筋骨系_その他の骨折
                    cpnTucRaiAssI.setI1B(null);
                    // 疾患_神経系_アルツハイマー病
                    cpnTucRaiAssI.setI1C(null);
                    // 疾患_神経系_認知症
                    cpnTucRaiAssI.setI1D(null);
                    // 疾患_神経系_片麻痺
                    cpnTucRaiAssI.setI1E(null);
                    // 疾患_神経系_多発性硬化症
                    cpnTucRaiAssI.setI1F(null);
                    // 疾患_神経系_対麻痺
                    cpnTucRaiAssI.setI1G(null);
                    // 疾患_神経系_パーキンソン病
                    cpnTucRaiAssI.setI1H(null);
                    // 疾患_神経系_四肢麻痺
                    cpnTucRaiAssI.setI1I(null);
                    // 疾患_神経系_脳卒中/脳血管障害
                    cpnTucRaiAssI.setI1J(null);
                    // 疾患_心肺系_CHD
                    cpnTucRaiAssI.setI1K(null);
                    // 疾患_心肺系_COPD
                    cpnTucRaiAssI.setI1L(null);
                    // 疾患_心肺系_CHF
                    cpnTucRaiAssI.setI1M(null);
                    // 疾患_心肺系_高血圧症
                    cpnTucRaiAssI.setI1N(null);
                    // 疾患_精神_不安症
                    cpnTucRaiAssI.setI1O(null);
                    // 疾患_精神_双極性障害
                    cpnTucRaiAssI.setI1P(null);
                    // 疾患_精神_うつ
                    cpnTucRaiAssI.setI1Q(null);
                    // 疾患_精神_統合失調症
                    cpnTucRaiAssI.setI1R(null);
                    // 疾患_感染症_肺炎
                    cpnTucRaiAssI.setI1S(null);
                    // 疾患_感染症_UTI
                    cpnTucRaiAssI.setI1T(null);
                    // 疾患_その他_がん
                    cpnTucRaiAssI.setI1U(null);
                    // 疾患_その他_糖尿病
                    cpnTucRaiAssI.setI1V(null);
                    // i1_メモ
                    cpnTucRaiAssI.setI1MemoKnj(null);

                    CpnTucRaiAssICriteria criteria = new CpnTucRaiAssICriteria();
                    criteria.createCriteria().andRaiIdEqualTo(
                            // アセスメントID
                            CommonDtoUtil.strValToInt(raiId));

                    // DAOを実行
                    this.cpnTucRaiAssIMapper.updateByCriteriaSelective(cpnTucRaiAssI, criteria);
                }

            } else if (CommonConstants.TAB_NAME_I.equals(inDto.getTabName())
                    && CommonConstants.TAB_NAME_I2.equals(inDto.getSubKbn())) {
                // ・上記の変数.値存在フラグ が4以外の場合
                if (!CommonConstants.STR_4.equals(flg)) {
                    // 6.2.2.1. 【28-xx インターライ方式_アセスメント_I疾患】情報を削除する。
                    CpnTucRaiAssICriteria cpnTucRaiAssICriteria = new CpnTucRaiAssICriteria();
                    cpnTucRaiAssICriteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()));

                    // DAOを実行
                    cpnTucRaiAssIMapper.deleteByCriteria(cpnTucRaiAssICriteria);
                    // ・上記以外の場合
                } else {
                    CpnTucRaiAssI cpnTucRaiAssI = new CpnTucRaiAssI();
                    cpnTucRaiAssI.setI2MemoKnj(null);
                    cpnTucRaiAssI.setI2MemoColor(null);
                    cpnTucRaiAssI.setI2MemoFont(null);

                    CpnTucRaiAssICriteria criteria = new CpnTucRaiAssICriteria();
                    criteria.createCriteria().andRaiIdEqualTo(
                            // アセスメントID
                            CommonDtoUtil.strValToInt(raiId));

                    // DAOを実行
                    this.cpnTucRaiAssIMapper.updateByCriteriaSelective(cpnTucRaiAssI, criteria);
                }
                // 6.2.2.2. 【28-xx インターライ方式_アセスメント_I疾患（診断）】情報リストを繰り返して、削除処理を行う。
                if (CollectionUtils.isNotEmpty(inDto.getDiagnosisInfoList())) {
                    // 【繰り返し開始】リクエストパラメータ.疾患（診断）情報リストを繰り返して、
                    for (int i = 0; i < inDto.getDiagnosisInfoList().size(); i++) {
                        GUI00773DiagnosisInfo diagnosisInfo = inDto.getDiagnosisInfoList().get(i);
                        CpnTucRaiAssI2Criteria cpnTucRaiAssI2Criteria = new CpnTucRaiAssI2Criteria();
                        cpnTucRaiAssI2Criteria.createCriteria()
                                .andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                                .andIdEqualTo(CommonDtoUtil.strValToInt(diagnosisInfo.getId()));

                        // DAOを実行
                        cpnTucRaiAssI2Mapper.deleteByCriteria(cpnTucRaiAssI2Criteria);
                    }
                }
                // 6.2.2.3. 履歴情報のセクション状態をクリアする。
                clearRrkSection(inDto);
            }
        }
    }

    /**
     * サブA情報更新
     * 関数名：updateRrkSubInfoA
     *
     * @param @param   raiId アセスメントID
     * @param subInfoA サブA情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoA(String raiId, GUI00764SubInfoA subInfoA)
            throws ExclusiveException {
        CpnTucRaiAssA cpnTucRaiAssA = new CpnTucRaiAssA();
        // アセスメントID
        cpnTucRaiAssA.setRaiId(CommonDtoUtil.strValToInt(raiId));
        // 婚姻状況 */
        cpnTucRaiAssA.setA4(CommonDtoUtil.strValToInt(subInfoA.getA4()));
        // 要介護度 */
        cpnTucRaiAssA.setA7(CommonDtoUtil.strValToInt(subInfoA.getA7()));
        // アセスメントの理由 */
        cpnTucRaiAssA.setA8(CommonDtoUtil.strValToInt(subInfoA.getA8()));
        // 本人のケアの目標 */
        cpnTucRaiAssA.setA10Knj(subInfoA.getA10Knj());
        // アセスメント時の居住場所 */
        cpnTucRaiAssA.setA11(CommonDtoUtil.strValToInt(subInfoA.getA11()));
        // 同居形態_同居者 */
        cpnTucRaiAssA.setA12A(CommonDtoUtil.strValToInt(subInfoA.getA12A()));
        // 同居形態_変化 */
        cpnTucRaiAssA.setA12B(CommonDtoUtil.strValToInt(subInfoA.getA12B()));
        // 同居形態_他の居住 */
        cpnTucRaiAssA.setA12C(CommonDtoUtil.strValToInt(subInfoA.getA12C()));
        // 退院後の経過期間 */
        cpnTucRaiAssA.setA13(CommonDtoUtil.strValToInt(subInfoA.getA13()));
        // a4_メモ */
        cpnTucRaiAssA.setA4MemoKnj(subInfoA.getA4MemoKnj());
        // a4_メモフォント */
        cpnTucRaiAssA.setA4MemoFont(CommonDtoUtil.strValToInt(subInfoA.getA4MemoFont()));
        // a4_メモ色 */
        cpnTucRaiAssA.setA4MemoColor(CommonDtoUtil.strValToInt(subInfoA.getA4MemoColor()));
        // a7_メモ */
        cpnTucRaiAssA.setA7MemoKnj(subInfoA.getA7MemoKnj());
        // a7_メモフォント */
        cpnTucRaiAssA.setA7MemoFont(CommonDtoUtil.strValToInt(subInfoA.getA7MemoFont()));
        // a7_メモ色 */
        cpnTucRaiAssA.setA7MemoColor(CommonDtoUtil.strValToInt(subInfoA.getA7MemoColor()));
        // a8_メモ */
        cpnTucRaiAssA.setA8MemoKnj(subInfoA.getA8MemoKnj());
        // a8_メモフォント */
        cpnTucRaiAssA.setA8MemoFont(CommonDtoUtil.strValToInt(subInfoA.getA8MemoFont()));
        // a8_メモ色 */
        cpnTucRaiAssA.setA8MemoColor(CommonDtoUtil.strValToInt(subInfoA.getA8MemoColor()));
        // a11_メモ */
        cpnTucRaiAssA.setA11MemoKnj(subInfoA.getA11MemoKnj());
        // a11_メモフォント */
        cpnTucRaiAssA.setA11MemoFont(CommonDtoUtil.strValToInt(subInfoA.getA11MemoFont()));
        // a11_メモ色 */
        cpnTucRaiAssA.setA11MemoColor(CommonDtoUtil.strValToInt(subInfoA.getA11MemoColor()));
        // a12_a_メモ */
        cpnTucRaiAssA.setA12AMemoKnj(subInfoA.getA12AMemoKnj());
        // a12_a_メモフォント */
        cpnTucRaiAssA.setA12AMemoFont(CommonDtoUtil.strValToInt(subInfoA.getA12AMemoFont()));
        // a12_a_メモ色 */
        cpnTucRaiAssA.setA12AMemoColor(CommonDtoUtil.strValToInt(subInfoA.getA12AMemoColor()));
        // a12_b_メモ */
        cpnTucRaiAssA.setA12BMemoKnj(subInfoA.getA12BMemoKnj());
        // a12_b_メモフォント */
        cpnTucRaiAssA.setA12BMemoFont(CommonDtoUtil.strValToInt(subInfoA.getA12BMemoFont()));
        // a12_b_メモ色 */
        cpnTucRaiAssA.setA12BMemoColor(CommonDtoUtil.strValToInt(subInfoA.getA12BMemoColor()));
        // a12C_メモ */
        cpnTucRaiAssA.setA12CMemoKnj(subInfoA.getA12CMemoKnj());
        // a12C_メモフォント */
        cpnTucRaiAssA.setA12CMemoFont(CommonDtoUtil.strValToInt(subInfoA.getA12CMemoFont()));
        // a12C_メモ色 */
        cpnTucRaiAssA.setA12CMemoColor(CommonDtoUtil.strValToInt(subInfoA.getA12CMemoColor()));
        // a13_メモ */
        cpnTucRaiAssA.setA13MemoKnj(subInfoA.getA13MemoKnj());
        // a13_メモフォント */
        cpnTucRaiAssA.setA13MemoFont(CommonDtoUtil.strValToInt(subInfoA.getA13MemoFont()));
        // a13_メモ色 */
        cpnTucRaiAssA.setA13MemoColor(CommonDtoUtil.strValToInt(subInfoA.getA13MemoColor()));

        CpnTucRaiAssACriteria cpnTucRaiAssACriteria = new CpnTucRaiAssACriteria();
        cpnTucRaiAssACriteria.createCriteria()
                // アセスメントID
                .andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));
        int count = this.cpnTucRaiAssAMapper.updateByCriteriaSelective(cpnTucRaiAssA,
                cpnTucRaiAssACriteria);

        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * サブB情報更新
     * 関数名：updateRrkSubInfoB
     *
     * @param @param   raiId アセスメントID
     * @param subInfoB サブB情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoB(String raiId, GUI00765SubInfoB subBInfo)
            throws ExclusiveException {
        CpnTucRaiAssBCriteria criteria = new CpnTucRaiAssBCriteria();
        criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));

        CpnTucRaiAssB cpnTucRaiAssB = new CpnTucRaiAssB();
        // 入所に対して本人の意思度合
        cpnTucRaiAssB.setB1(CommonDtoUtil.strValToInt(subBInfo.getB1()));
        // 受付日
        cpnTucRaiAssB.setB2Ymd(subBInfo.getB2Ymd());
        // 相談受付時までの経過
        cpnTucRaiAssB.setB3Knj(subBInfo.getB3Knj());
        // 相談受付内容
        cpnTucRaiAssB.setB4Knj(subBInfo.getB4Knj());
        // 利用歴_介護施設、療養病院/病棟
        cpnTucRaiAssB.setB5A(CommonDtoUtil.strValToInt(subBInfo.getB5A()));
        // 利用歴_認知症対応型共同生活介護、小規模多機能型居宅介護
        cpnTucRaiAssB.setB5B(CommonDtoUtil.strValToInt(subBInfo.getB5B()));
        // 利用歴_高齢者住宅－有料老人ホーム
        cpnTucRaiAssB.setB5C(CommonDtoUtil.strValToInt(subBInfo.getB5C()));
        // 利用歴_精神科病院、精神科病棟
        cpnTucRaiAssB.setB5D(CommonDtoUtil.strValToInt(subBInfo.getB5D()));
        // 利用歴_精神障害者施設
        cpnTucRaiAssB.setB5E(CommonDtoUtil.strValToInt(subBInfo.getB5E()));
        // 利用歴_知的障害者施設
        cpnTucRaiAssB.setB5F(CommonDtoUtil.strValToInt(subBInfo.getB5F()));
        // 入所直前の居住場所
        cpnTucRaiAssB.setB6A(CommonDtoUtil.strValToInt(subBInfo.getB6A()));
        // 通常の居住場所
        cpnTucRaiAssB.setB6B(CommonDtoUtil.strValToInt(subBInfo.getB6B()));
        // 入所前の同居形態
        cpnTucRaiAssB.setB7(CommonDtoUtil.strValToInt(subBInfo.getB7()));
        // 精神疾患歴
        cpnTucRaiAssB.setB8(CommonDtoUtil.strValToInt(subBInfo.getB8()));
        // 教育歴
        cpnTucRaiAssB.setB9(CommonDtoUtil.strValToInt(subBInfo.getB9()));
        // 医療機関受診時の送迎
        cpnTucRaiAssB.setB10(CommonDtoUtil.strValToInt(subBInfo.getB10()));
        // 受診中の付き添いが必要
        cpnTucRaiAssB.setB11(CommonDtoUtil.strValToInt(subBInfo.getB11()));
        // b1_メモ
        cpnTucRaiAssB.setB1MemoKnj(subBInfo.getB1MemoKnj());
        // b1_メモフォント
        cpnTucRaiAssB.setB1MemoFont(CommonDtoUtil.strValToInt(subBInfo.getB1MemoFont()));
        // b1_メモ色
        cpnTucRaiAssB.setB1MemoColor(CommonDtoUtil.strValToInt(subBInfo.getB1MemoColor()));
        // b5_メモ
        cpnTucRaiAssB.setB5MemoKnj(subBInfo.getB5MemoKnj());
        // b5_メモフォント
        cpnTucRaiAssB.setB5MemoFont(CommonDtoUtil.strValToInt(subBInfo.getB5MemoFont()));
        // b5_メモ色
        cpnTucRaiAssB.setB5MemoColor(CommonDtoUtil.strValToInt(subBInfo.getB5MemoColor()));
        // b6_メモ
        cpnTucRaiAssB.setB6MemoKnj(subBInfo.getB6MemoKnj());
        // b6_メモフォント
        cpnTucRaiAssB.setB6MemoFont(CommonDtoUtil.strValToInt(subBInfo.getB6MemoFont()));
        // b6_メモ色
        cpnTucRaiAssB.setB6MemoColor(CommonDtoUtil.strValToInt(subBInfo.getB6MemoColor()));
        // b7_メモ
        cpnTucRaiAssB.setB7MemoKnj(subBInfo.getB7MemoKnj());
        // b7_メモフォント
        cpnTucRaiAssB.setB7MemoFont(CommonDtoUtil.strValToInt(subBInfo.getB7MemoFont()));
        // b7_メモ色
        cpnTucRaiAssB.setB7MemoColor(CommonDtoUtil.strValToInt(subBInfo.getB7MemoColor()));
        // b8_メモ
        cpnTucRaiAssB.setB8MemoKnj(subBInfo.getB8MemoKnj());
        // b8_メモフォント
        cpnTucRaiAssB.setB8MemoFont(CommonDtoUtil.strValToInt(subBInfo.getB8MemoFont()));
        // b8_メモ色
        cpnTucRaiAssB.setB8MemoColor(CommonDtoUtil.strValToInt(subBInfo.getB8MemoColor()));
        // b9_メモ
        cpnTucRaiAssB.setB9MemoKnj(subBInfo.getB9MemoKnj());
        // b9_メモフォント
        cpnTucRaiAssB.setB9MemoFont(CommonDtoUtil.strValToInt(subBInfo.getB9MemoFont()));
        // b9_メモ色
        cpnTucRaiAssB.setB9MemoColor(CommonDtoUtil.strValToInt(subBInfo.getB9MemoColor()));
        // b10_メモ
        cpnTucRaiAssB.setB10MemoKnj(subBInfo.getB10MemoKnj());
        // b10_メモフォント
        cpnTucRaiAssB.setB10MemoFont(CommonDtoUtil.strValToInt(subBInfo.getB10MemoFont()));
        // b10_メモ色
        cpnTucRaiAssB.setB10MemoColor(CommonDtoUtil.strValToInt(subBInfo.getB10MemoColor()));
        // b11_メモ
        cpnTucRaiAssB.setB11MemoKnj(subBInfo.getB11MemoKnj());
        // b11_メモフォント
        cpnTucRaiAssB.setB11MemoFont(CommonDtoUtil.strValToInt(subBInfo.getB11MemoFont()));
        // b11_メモ色
        cpnTucRaiAssB.setB11MemoColor(CommonDtoUtil.strValToInt(subBInfo.getB11MemoColor()));

        // DAOを実行
        int count = this.cpnTucRaiAssBMapper.updateByCriteriaSelective(cpnTucRaiAssB, criteria);
        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * サブC情報更新
     * 関数名：updateRrkSubInfoC
     *
     * @param @param   raiId アセスメントID
     * @param subInfoC サブC情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoC(String raiId, GUI00766SubInfoC subInfoC)
            throws ExclusiveException {
        // サブ情報（C）
        CpnTucRaiAssC cpnTucRaiAssC = new CpnTucRaiAssC();

        // 日常の意思決定の認知能力 */
        cpnTucRaiAssC.setC1(CommonDtoUtil.strValToInt(subInfoC.getC1()));
        // 短期記憶 */
        cpnTucRaiAssC.setC2A(CommonDtoUtil.strValToInt(subInfoC.getC2A()));
        // 長期記憶 */
        cpnTucRaiAssC.setC2B(CommonDtoUtil.strValToInt(subInfoC.getC2B()));
        // 手続き記憶 */
        cpnTucRaiAssC.setC2C(CommonDtoUtil.strValToInt(subInfoC.getC2C()));
        // 状況記憶 */
        cpnTucRaiAssC.setC2D(CommonDtoUtil.strValToInt(subInfoC.getC2D()));
        // せん妄の兆候_注意 */
        cpnTucRaiAssC.setC3A(CommonDtoUtil.strValToInt(subInfoC.getC3A()));
        // せん妄の兆候_会話 */
        cpnTucRaiAssC.setC3B(CommonDtoUtil.strValToInt(subInfoC.getC3B()));
        // せん妄の兆候_精神機能 */
        cpnTucRaiAssC.setC3C(CommonDtoUtil.strValToInt(subInfoC.getC3C()));
        // 精神状態の急な変化 */
        cpnTucRaiAssC.setC4(CommonDtoUtil.strValToInt(subInfoC.getC4()));
        // 意思決定能力の変化 */
        cpnTucRaiAssC.setC5(CommonDtoUtil.strValToInt(subInfoC.getC5()));
        // c1_メモ */
        cpnTucRaiAssC.setC1MemoKnj(subInfoC.getC1MemoKnj());
        // c1_メモフォント */
        cpnTucRaiAssC.setC1MemoFont(CommonDtoUtil.strValToInt(subInfoC.getC1MemoFont()));
        // c1_メモ色 */
        cpnTucRaiAssC.setC1MemoColor(CommonDtoUtil.strValToInt(subInfoC.getC1MemoColor()));
        // c2_メモ */
        cpnTucRaiAssC.setC2MemoKnj(subInfoC.getC2MemoKnj());
        // c2_メモフォント */
        cpnTucRaiAssC.setC2MemoFont(CommonDtoUtil.strValToInt(subInfoC.getC2MemoFont()));
        // c2_メモ色 */
        cpnTucRaiAssC.setC2MemoColor(CommonDtoUtil.strValToInt(subInfoC.getC2MemoColor()));
        // c3_メモ */
        cpnTucRaiAssC.setC3MemoKnj(subInfoC.getC3MemoKnj());
        // c3_メモフォント */
        cpnTucRaiAssC.setC3MemoFont(CommonDtoUtil.strValToInt(subInfoC.getC3MemoFont()));
        // c3_メモ色 */
        cpnTucRaiAssC.setC3MemoColor(CommonDtoUtil.strValToInt(subInfoC.getC3MemoColor()));
        // c4_メモ */
        cpnTucRaiAssC.setC4MemoKnj(subInfoC.getC4MemoKnj());
        // c4_メモフォント */
        cpnTucRaiAssC.setC4MemoFont(CommonDtoUtil.strValToInt(subInfoC.getC4MemoFont()));
        // c4_メモ色 */
        cpnTucRaiAssC.setC4MemoColor(CommonDtoUtil.strValToInt(subInfoC.getC4MemoColor()));
        // c5_メモ */
        cpnTucRaiAssC.setC5MemoKnj(subInfoC.getC5MemoKnj());
        // c5_メモフォント */
        cpnTucRaiAssC.setC5MemoFont(CommonDtoUtil.strValToInt(subInfoC.getC5MemoFont()));
        // c5_メモ色 */
        cpnTucRaiAssC.setC5MemoColor(CommonDtoUtil.strValToInt(subInfoC.getC5MemoColor()));

        CpnTucRaiAssCCriteria cpnTucRaiAssCCriteria = new CpnTucRaiAssCCriteria();
        cpnTucRaiAssCCriteria.createCriteria()
                .andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));

        // DAOを実行
        this.cpnTucRaiAssCMapper.updateByCriteriaSelective(cpnTucRaiAssC,
                cpnTucRaiAssCCriteria);
        // 4.2.2. リクエストパラメータ.サブ情報（C）.日常の意思決定の認知能力が"5":認識できる意識がない、昏睡 — セクションGへの場合
        if (CommonConstants.C1_5.equals(subInfoC.getC1())) {
            // 4.2.2.1. 【28-xx インターライ方式_アセスメント_Dコミュニケーションと視覚】情報を更新する。
            // 変更する前に、該当データを検索する
            CpnTucRaiAssD cpnTucRaiAssD = new CpnTucRaiAssD();
            this.cpnTucRaiAssDMapper
                    .selectByPrimaryKey(CommonDtoUtil.strValToInt(raiId));

            // 理解させる能力
            cpnTucRaiAssD.setD1(null);
            // 理解できる能力
            cpnTucRaiAssD.setD2(null);
            // 聴力
            cpnTucRaiAssD.setD3A(null);
            // 補聴器の使用
            cpnTucRaiAssD.setD3B(null);
            // 視力
            cpnTucRaiAssD.setD4A(null);
            // 眼鏡、コンタクトレンズ、拡大鏡の使用
            cpnTucRaiAssD.setD4B(null);

            // d1_メモ
            cpnTucRaiAssD.setD1MemoKnj(null);
            // d1_メモフォント
            cpnTucRaiAssD.setD1MemoFont(null);
            // d1_メモ色
            cpnTucRaiAssD.setD1MemoColor(null);

            // d2_メモ
            cpnTucRaiAssD.setD2MemoKnj(null);
            // d2_メモフォント
            cpnTucRaiAssD.setD2MemoFont(null);
            // d2_メモ色
            cpnTucRaiAssD.setD2MemoColor(null);

            // d3_a_メモ
            cpnTucRaiAssD.setD3AMemoKnj(null);
            // d3_a_メモフォント
            cpnTucRaiAssD.setD3AMemoFont(null);
            // d3_a_メモ色
            cpnTucRaiAssD.setD3AMemoColor(null);

            // d3_b_メモ
            cpnTucRaiAssD.setD3BMemoKnj(null);
            // d3_b_メモフォント
            cpnTucRaiAssD.setD3BMemoFont(null);
            // d3_b_メモ色
            cpnTucRaiAssD.setD3BMemoColor(null);

            // d4_a_メモ
            cpnTucRaiAssD.setD4AMemoKnj(null);
            // d4_a_メモフォント
            cpnTucRaiAssD.setD4AMemoFont(null);
            // d4_a_メモ色
            cpnTucRaiAssD.setD4AMemoColor(null);

            // d4_b_メモ
            cpnTucRaiAssD.setD4BMemoKnj(null);
            // d4_b_メモフォント
            cpnTucRaiAssD.setD4BMemoFont(null);
            // d4_b_メモ色
            cpnTucRaiAssD.setD4BMemoColor(null);

            CpnTucRaiAssDCriteria cpnTucRaiAssDCriteria = new CpnTucRaiAssDCriteria();
            cpnTucRaiAssDCriteria.createCriteria()
                    .andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));

            // DAOを実行
            this.cpnTucRaiAssDMapper.updateByCriteria(cpnTucRaiAssD, cpnTucRaiAssDCriteria);

            // 4.2.2.2. 【28-xx インターライ方式_アセスメント_E気分と行動】情報を更新する。
            // 変更する前に、該当データを検索する
            CpnTucRaiAssE cpnTucRaiAssE = new CpnTucRaiAssE();
            this.cpnTucRaiAssEMapper
                    .selectByPrimaryKey(CommonDtoUtil.strValToInt(raiId));

            // 気分の兆候_否定
            cpnTucRaiAssE.setE1A(null);
            // 気分の兆候_怒り
            cpnTucRaiAssE.setE1B(null);
            // 気分の兆候_恐れ
            cpnTucRaiAssE.setE1C(null);
            // 気分の兆候_不調
            cpnTucRaiAssE.setE1D(null);
            // 気分の兆候_不安
            cpnTucRaiAssE.setE1E(null);
            // 気分の兆候_悲しみ、苦悩、心配
            cpnTucRaiAssE.setE1F(null);
            // 気分の兆候_泣く
            cpnTucRaiAssE.setE1G(null);
            // 気分の兆候_ひどいことが起こりそう
            cpnTucRaiAssE.setE1H(null);
            // 気分の兆候_興味
            cpnTucRaiAssE.setE1I(null);
            // 気分の兆候_社会的交流の減少
            cpnTucRaiAssE.setE1J(null);
            // 気分の兆候_快感喪失
            cpnTucRaiAssE.setE1K(null);

            // 利用者自身_興味や喜び
            cpnTucRaiAssE.setE2A(null);
            // 利用者自身_不安
            cpnTucRaiAssE.setE2B(null);
            // 利用者自身_絶望
            cpnTucRaiAssE.setE2C(null);

            // 行動の問題_徘徊
            cpnTucRaiAssE.setE3A(null);
            // 行動の問題_暴言
            cpnTucRaiAssE.setE3B(null);
            // 行動の問題_暴行
            cpnTucRaiAssE.setE3C(null);
            // 行動の問題_社会的迷惑行為
            cpnTucRaiAssE.setE3D(null);
            // 行動の問題_性的行動や脱衣
            cpnTucRaiAssE.setE3E(null);
            // 行動の問題_抵抗
            cpnTucRaiAssE.setE3F(null);
            // 行動の問題_退居・家出
            cpnTucRaiAssE.setE3G(null);

            // 生活満足度
            cpnTucRaiAssE.setE4(null);

            // e1_メモ
            cpnTucRaiAssE.setE1MemoKnj(null);
            // e1_メモフォント
            cpnTucRaiAssE.setE1MemoFont(null);
            // e1_メモ色
            cpnTucRaiAssE.setE1MemoColor(null);

            // e2_メモ
            cpnTucRaiAssE.setE2MemoKnj(null);
            // e2_メモフォント
            cpnTucRaiAssE.setE2MemoFont(null);
            // e2_メモ色
            cpnTucRaiAssE.setE2MemoColor(null);

            // e3_メモ
            cpnTucRaiAssE.setE3MemoKnj(null);
            // e3_メモフォント
            cpnTucRaiAssE.setE3MemoFont(null);
            // e3_メモ色
            cpnTucRaiAssE.setE3MemoColor(null);

            // e4_メモ
            cpnTucRaiAssE.setE4MemoKnj(null);
            // e4_メモフォント
            cpnTucRaiAssE.setE4MemoFont(null);
            // e4_メモ色
            cpnTucRaiAssE.setE4MemoColor(null);

            CpnTucRaiAssECriteria cpnTucRaiAssECriteria = new CpnTucRaiAssECriteria();
            cpnTucRaiAssECriteria.createCriteria()
                    .andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));

            // DAOを実行
            this.cpnTucRaiAssEMapper.updateByCriteria(cpnTucRaiAssE, cpnTucRaiAssECriteria);

            // 4.2.2.3. 【28-xx インターライ方式_アセスメント_F心理社会面】情報を更新する。
            // 変更する前に、該当データを検索する
            CpnTucRaiAssF cpnTucRaiAssF = new CpnTucRaiAssF();
            this.cpnTucRaiAssFMapper
                    .selectByPrimaryKey(CommonDtoUtil.strValToInt(raiId));

            // 社会関係_活動への参加
            cpnTucRaiAssF.setF1A(null);
            // 社会関係_訪問
            cpnTucRaiAssF.setF1B(null);
            // 社会関係_その他の交流
            cpnTucRaiAssF.setF1C(null);
            // 社会関係_葛藤や怒り
            cpnTucRaiAssF.setF1D(null);
            // 社会関係_恐れ
            cpnTucRaiAssF.setF1E(null);
            // 社会関係_虐待
            cpnTucRaiAssF.setF1F(null);

            // 孤独
            cpnTucRaiAssF.setF2(null);

            // 社会的活動の変化
            cpnTucRaiAssF.setF3(null);

            // 一人きりでいる時間
            cpnTucRaiAssF.setF4(null);

            // 自発性・参加_他者
            cpnTucRaiAssF.setF5A(null);
            // 自発性・参加_活動
            cpnTucRaiAssF.setF5B(null);
            // 自発性・参加_グループ活動
            cpnTucRaiAssF.setF5C(null);
            // 自発性・参加_施設内
            cpnTucRaiAssF.setF5D(null);
            // 自発性・参加_他者との交流
            cpnTucRaiAssF.setF5E(null);
            // 自発性・参加_肯定
            cpnTucRaiAssF.setF5F(null);
            // 自発性・参加_日課の変化
            cpnTucRaiAssF.setF5G(null);

            // 対人関係_ほかの利用者
            cpnTucRaiAssF.setF6A(null);
            // 対人関係_ケアスタッフ
            cpnTucRaiAssF.setF6B(null);
            // 対人関係_対応
            cpnTucRaiAssF.setF6C(null);
            // 対人関係_家族や近い友人
            cpnTucRaiAssF.setF6D(null);

            // 大きなストレス
            cpnTucRaiAssF.setF7(null);

            // 強み_前向き
            cpnTucRaiAssF.setF8A(null);
            // 強み_生活
            cpnTucRaiAssF.setF8B(null);
            // 強み_家族
            cpnTucRaiAssF.setF8C(null);

            // f1_メモ
            cpnTucRaiAssF.setF1MemoKnj(null);
            // f1_メモフォント
            cpnTucRaiAssF.setF1MemoFont(null);
            // f1_メモ色
            cpnTucRaiAssF.setF1MemoColor(null);

            // f2_メモ
            cpnTucRaiAssF.setF2MemoKnj(null);
            // f2_メモフォント
            cpnTucRaiAssF.setF2MemoFont(null);
            // f2_メモ色
            cpnTucRaiAssF.setF2MemoColor(null);

            // f3_メモ
            cpnTucRaiAssF.setF3MemoKnj(null);
            // f3_メモフォント
            cpnTucRaiAssF.setF3MemoFont(null);
            // f3_メモ色
            cpnTucRaiAssF.setF3MemoColor(null);

            // f4_メモ
            cpnTucRaiAssF.setF4MemoKnj(null);
            // f4_メモフォント
            cpnTucRaiAssF.setF4MemoFont(null);
            // f4_メモ色
            cpnTucRaiAssF.setF4MemoColor(null);

            // f5_メモ
            cpnTucRaiAssF.setF5MemoKnj(null);
            // f5_メモフォント
            cpnTucRaiAssF.setF5MemoFont(null);
            // f5_メモ色
            cpnTucRaiAssF.setF5MemoColor(null);

            // f6_メモ
            cpnTucRaiAssF.setF6MemoKnj(null);
            // f6_メモフォント
            cpnTucRaiAssF.setF6MemoFont(null);
            // f6_メモ色
            cpnTucRaiAssF.setF6MemoColor(null);

            // f7_メモ
            cpnTucRaiAssF.setF7MemoKnj(null);
            // f7_メモフォント
            cpnTucRaiAssF.setF7MemoFont(null);
            // f7_メモ色
            cpnTucRaiAssF.setF7MemoColor(null);

            // f8_メモ
            cpnTucRaiAssF.setF8MemoKnj(null);
            // f8_メモフォント
            cpnTucRaiAssF.setF8MemoFont(null);
            // f8_メモ色
            cpnTucRaiAssF.setF8MemoColor(null);

            CpnTucRaiAssFCriteria cpnTucRaiAssFCriteria = new CpnTucRaiAssFCriteria();
            cpnTucRaiAssFCriteria.createCriteria()
                    .andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));

            // DAOを実行
            this.cpnTucRaiAssFMapper.updateByCriteria(cpnTucRaiAssF, cpnTucRaiAssFCriteria);
        }
    }

    /**
     * サブD情報更新
     * 関数名：updateRrkSubInfoD
     *
     * @param @param   raiId アセスメントID
     * @param subInfoD サブD情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoD(String raiId, GUI00767SubInfoD subInfoD)
            throws ExclusiveException {
        final CpnTucRaiAssDCriteria criteria = new CpnTucRaiAssDCriteria();

        final CpnTucRaiAssD cpnTucRaiAssD = new CpnTucRaiAssD();
        // 理解させる能力
        cpnTucRaiAssD.setD1(CommonDtoUtil.strValToInt(subInfoD.getD1()));
        // 理解できる能力
        cpnTucRaiAssD.setD2(CommonDtoUtil.strValToInt(subInfoD.getD2()));
        // 聴力
        cpnTucRaiAssD.setD3A(CommonDtoUtil.strValToInt(subInfoD.getD3A()));
        // 補聴器の使用
        cpnTucRaiAssD.setD3B(CommonDtoUtil.strValToInt(subInfoD.getD3B()));
        // 視力
        cpnTucRaiAssD.setD4A(CommonDtoUtil.strValToInt(subInfoD.getD4A()));
        // 眼鏡、コンタクトレンズ、拡大鏡の使用
        cpnTucRaiAssD.setD4B(CommonDtoUtil.strValToInt(subInfoD.getD4B()));
        // d1_メモ
        cpnTucRaiAssD.setD1MemoKnj(subInfoD.getD1MemoKnj());
        // d1_メモフォント
        cpnTucRaiAssD.setD1MemoFont(CommonDtoUtil.strValToInt(subInfoD.getD1MemoFont()));
        // d1_メモ色
        cpnTucRaiAssD.setD1MemoColor(CommonDtoUtil.strValToInt(subInfoD.getD1MemoColor()));
        // d2_メモ
        cpnTucRaiAssD.setD2MemoKnj(subInfoD.getD2MemoKnj());
        // d2_メモフォント
        cpnTucRaiAssD.setD2MemoFont(CommonDtoUtil.strValToInt(subInfoD.getD2MemoFont()));
        // d2_メモ色
        cpnTucRaiAssD.setD2MemoColor(CommonDtoUtil.strValToInt(subInfoD.getD2MemoColor()));
        // d3_a_メモ
        cpnTucRaiAssD.setD3AMemoKnj(subInfoD.getD3AMemoKnj());
        // d3_a_メモフォント
        cpnTucRaiAssD.setD3AMemoFont(CommonDtoUtil.strValToInt(subInfoD.getD3AMemoFont()));
        // d3_a_メモ色
        cpnTucRaiAssD.setD3AMemoColor(CommonDtoUtil.strValToInt(subInfoD.getD3AMemoColor()));
        // d3_b_メモ
        cpnTucRaiAssD.setD3BMemoKnj(subInfoD.getD3BMemoKnj());
        // d3_b_メモフォント
        cpnTucRaiAssD.setD3BMemoFont(CommonDtoUtil.strValToInt(subInfoD.getD3BMemoFont()));
        // d3_b_メモ色
        cpnTucRaiAssD.setD3BMemoColor(CommonDtoUtil.strValToInt(subInfoD.getD3BMemoColor()));
        // d4_a_メモ
        cpnTucRaiAssD.setD4AMemoKnj(subInfoD.getD4AMemoKnj());
        // d4_a_メモフォント
        cpnTucRaiAssD.setD4AMemoFont(CommonDtoUtil.strValToInt(subInfoD.getD4AMemoFont()));
        // d4_a_メモ色
        cpnTucRaiAssD.setD4AMemoColor(CommonDtoUtil.strValToInt(subInfoD.getD4AMemoColor()));
        // d4_b_メモ
        cpnTucRaiAssD.setD4BMemoKnj(subInfoD.getD4BMemoKnj());
        // d4_b_メモフォント
        cpnTucRaiAssD.setD4BMemoFont(CommonDtoUtil.strValToInt(subInfoD.getD4BMemoFont()));
        // d4_b_メモ色
        cpnTucRaiAssD.setD4BMemoColor(CommonDtoUtil.strValToInt(subInfoD.getD4BMemoColor()));

        criteria.createCriteria()
                .andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));

        this.cpnTucRaiAssDMapper.updateByCriteriaSelective(cpnTucRaiAssD, criteria);
    }

    /**
     * サブE情報更新
     * 関数名：updateRrkSubInfoE
     *
     * @param @param   raiId アセスメントID
     * @param subInfoE サブE情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoE(String raiId, GUI00768SubInfoE subInfoE)
            throws ExclusiveException {
        final CpnTucRaiAssECriteria criteria = new CpnTucRaiAssECriteria();
        criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));

        CpnTucRaiAssE cpnTucRaiAssE = new CpnTucRaiAssE();
        // E気分と行動情報設定
        // 気分の兆候_否定
        cpnTucRaiAssE.setE1A(CommonDtoUtil.strValToInt(subInfoE.getE1A()));
        // 気分の兆候_怒り
        cpnTucRaiAssE.setE1B(CommonDtoUtil.strValToInt(subInfoE.getE1B()));
        // 気分の兆候_恐れ
        cpnTucRaiAssE.setE1C(CommonDtoUtil.strValToInt(subInfoE.getE1C()));
        // 気分の兆候_不調
        cpnTucRaiAssE.setE1D(CommonDtoUtil.strValToInt(subInfoE.getE1D()));
        // 気分の兆候_不安
        cpnTucRaiAssE.setE1E(CommonDtoUtil.strValToInt(subInfoE.getE1E()));
        // 気分の兆候_悲しみ苦悩心配
        cpnTucRaiAssE.setE1F(CommonDtoUtil.strValToInt(subInfoE.getE1F()));
        // 気分の兆候_泣く
        cpnTucRaiAssE.setE1G(CommonDtoUtil.strValToInt(subInfoE.getE1G()));
        // 気分の兆候_ひどいことが起こりそう
        cpnTucRaiAssE.setE1H(CommonDtoUtil.strValToInt(subInfoE.getE1H()));
        // 気分の兆候_興味
        cpnTucRaiAssE.setE1I(CommonDtoUtil.strValToInt(subInfoE.getE1I()));
        // 気分の兆候_社会的交流の減少
        cpnTucRaiAssE.setE1J(CommonDtoUtil.strValToInt(subInfoE.getE1J()));
        // 気分の兆候_快感喪失
        cpnTucRaiAssE.setE1K(CommonDtoUtil.strValToInt(subInfoE.getE1K()));
        // 利用者自身_興味や喜び
        cpnTucRaiAssE.setE2A(CommonDtoUtil.strValToInt(subInfoE.getE2A()));
        // 利用者自身_不安
        cpnTucRaiAssE.setE2B(CommonDtoUtil.strValToInt(subInfoE.getE2B()));
        // 利用者自身_絶望
        cpnTucRaiAssE.setE2C(CommonDtoUtil.strValToInt(subInfoE.getE2C()));
        // 行動の問題_徘徊
        cpnTucRaiAssE.setE3A(CommonDtoUtil.strValToInt(subInfoE.getE3A()));
        // 行動の問題_暴言
        cpnTucRaiAssE.setE3B(CommonDtoUtil.strValToInt(subInfoE.getE3B()));
        // 行動の問題_暴行
        cpnTucRaiAssE.setE3C(CommonDtoUtil.strValToInt(subInfoE.getE3C()));
        // 行動の問題_社会的迷惑行為
        cpnTucRaiAssE.setE3D(CommonDtoUtil.strValToInt(subInfoE.getE3D()));
        // 行動の問題_性的行動や脱衣
        cpnTucRaiAssE.setE3E(CommonDtoUtil.strValToInt(subInfoE.getE3E()));
        // 行動の問題_抵抗
        cpnTucRaiAssE.setE3F(CommonDtoUtil.strValToInt(subInfoE.getE3F()));
        // 行動の問題_退居家出
        cpnTucRaiAssE.setE3G(CommonDtoUtil.strValToInt(subInfoE.getE3G()));
        // 生活満足度
        cpnTucRaiAssE.setE4(CommonDtoUtil.strValToInt(subInfoE.getE4()));
        // e1_メモ
        cpnTucRaiAssE.setE1MemoKnj(subInfoE.getE1MemoKnj());
        // e1_メモフォント
        cpnTucRaiAssE.setE1MemoFont(CommonDtoUtil.strValToInt(subInfoE.getE1MemoFont()));
        // e1_メモ色
        cpnTucRaiAssE.setE1MemoColor(CommonDtoUtil.strValToInt(subInfoE.getE1MemoColor()));
        // e2_メモ
        cpnTucRaiAssE.setE2MemoKnj(subInfoE.getE2MemoKnj());
        // e2_メモフォント
        cpnTucRaiAssE.setE2MemoFont(CommonDtoUtil.strValToInt(subInfoE.getE2MemoFont()));
        // e2_メモ色
        cpnTucRaiAssE.setE2MemoColor(CommonDtoUtil.strValToInt(subInfoE.getE2MemoColor()));
        // e3_メモ
        cpnTucRaiAssE.setE3MemoKnj(subInfoE.getE3MemoKnj());
        // e3_メモフォント
        cpnTucRaiAssE.setE3MemoFont(CommonDtoUtil.strValToInt(subInfoE.getE3MemoFont()));
        // e3_メモ色
        cpnTucRaiAssE.setE3MemoColor(CommonDtoUtil.strValToInt(subInfoE.getE3MemoColor()));
        // e4_メモ
        cpnTucRaiAssE.setE4MemoKnj(subInfoE.getE4MemoKnj());
        // e4_メモフォント
        cpnTucRaiAssE.setE4MemoFont(CommonDtoUtil.strValToInt(subInfoE.getE4MemoFont()));
        // e4_メモ色
        cpnTucRaiAssE.setE4MemoColor(CommonDtoUtil.strValToInt(subInfoE.getE4MemoColor()));

        cpnTucRaiAssEMapper.updateByCriteriaSelective(cpnTucRaiAssE, criteria);
    }

    /**
     * サブF情報更新
     * 関数名：updateRrkSubInfoF
     *
     * @param @param   raiId アセスメントID
     * @param subInfoF サブF情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoF(String raiId, GUI00769SubInfoF subInfoF)
            throws ExclusiveException {
        // TODO
    }

    /**
     * サブG情報更新
     * 関数名：updateRrkSubInfoG
     *
     * @param @param   raiId アセスメントID
     * @param subInfoG サブG情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoG(String raiId, Gui00770SubInfoGList subInfoG)
            throws ExclusiveException {
        // TODO
    }

    /**
     * サブH情報更新
     * 関数名：updateRrkSubInfoH
     *
     * @param @param   raiId アセスメントID
     * @param subInfoH サブH情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoH(String raiId, GUI00771SubInfoH subInfoH)
            throws ExclusiveException {
        CpnTucRaiAssH cpnTucRaiAssH = new CpnTucRaiAssH();
        // 尿失禁
        cpnTucRaiAssH.setH1(CommonDtoUtil.strValToInt(subInfoH.getH1()));
        // 尿失禁器材
        cpnTucRaiAssH.setH2(CommonDtoUtil.strValToInt(subInfoH.getH2()));
        // 便失禁
        cpnTucRaiAssH.setH3(CommonDtoUtil.strValToInt(subInfoH.getH3()));
        // オムツやパッドの使用
        cpnTucRaiAssH.setH4(CommonDtoUtil.strValToInt(subInfoH.getH4()));
        // ストーマ
        cpnTucRaiAssH.setH5(CommonDtoUtil.strValToInt(subInfoH.getH5()));
        // h1メモ
        cpnTucRaiAssH.setH1MemoKnj(subInfoH.getH1MemoKnj());
        // h1メモフォント
        cpnTucRaiAssH.setH1MemoFont(CommonDtoUtil.strValToInt(subInfoH.getH1MemoFont()));
        // h1メモ色
        cpnTucRaiAssH.setH1MemoColor(CommonDtoUtil.strValToInt(subInfoH.getH1MemoColor()));
        // h2メモ
        cpnTucRaiAssH.setH2MemoKnj(subInfoH.getH2MemoKnj());
        // h2メモフォント
        cpnTucRaiAssH.setH2MemoFont(CommonDtoUtil.strValToInt(subInfoH.getH2MemoFont()));
        // h2メモ色
        cpnTucRaiAssH.setH2MemoColor(CommonDtoUtil.strValToInt(subInfoH.getH2MemoColor()));
        // h3メモ
        cpnTucRaiAssH.setH3MemoKnj(subInfoH.getH3MemoKnj());
        // h3メモフォント
        cpnTucRaiAssH.setH3MemoFont(CommonDtoUtil.strValToInt(subInfoH.getH3MemoFont()));
        // h3メモ色
        cpnTucRaiAssH.setH3MemoColor(CommonDtoUtil.strValToInt(subInfoH.getH3MemoColor()));
        // h4メモ
        cpnTucRaiAssH.setH4MemoKnj(subInfoH.getH4MemoKnj());
        // h4メモフォント
        cpnTucRaiAssH.setH4MemoFont(CommonDtoUtil.strValToInt(subInfoH.getH4MemoFont()));
        // h4メモ色
        cpnTucRaiAssH.setH4MemoColor(CommonDtoUtil.strValToInt(subInfoH.getH4MemoColor()));
        // h5メモ
        cpnTucRaiAssH.setH5MemoKnj(subInfoH.getH5MemoKnj());
        // h5メモフォント
        cpnTucRaiAssH.setH5MemoFont(CommonDtoUtil.strValToInt(subInfoH.getH5MemoFont()));
        // h5メモ色
        cpnTucRaiAssH.setH5MemoColor(CommonDtoUtil.strValToInt(subInfoH.getH5MemoColor()));

        CpnTucRaiAssHCriteria cpnTucRaiAssHCriteria = new CpnTucRaiAssHCriteria();
        cpnTucRaiAssHCriteria.createCriteria()
                // アセスメントID
                .andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));

        this.cpnTucRaiAssHMapper.updateByCriteriaSelective(cpnTucRaiAssH, cpnTucRaiAssHCriteria);
    }

    /**
     * サブI1情報更新
     * 関数名：updateRrkSubInfoI1
     *
     * @param @param    raiId アセスメントID
     * @param subInfoI1 サブI1情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoI1(String raiId, GUI00772UpdateSubInfoI1 subInfoI1) throws ExclusiveException {
        CpnTucRaiAssI cpnTucRaiAssI = new CpnTucRaiAssI();

        // 疾患_筋骨系_大腿骨骨折 */
        cpnTucRaiAssI.setI1A(CommonDtoUtil.strValToInt(subInfoI1.getI1A()));
        // 疾患_筋骨系_その他の骨折 */
        cpnTucRaiAssI.setI1B(CommonDtoUtil.strValToInt(subInfoI1.getI1B()));
        // 疾患_神経系_アルツハイマー病 */
        cpnTucRaiAssI.setI1C(CommonDtoUtil.strValToInt(subInfoI1.getI1C()));
        // 疾患_神経系_認知症 */
        cpnTucRaiAssI.setI1D(CommonDtoUtil.strValToInt(subInfoI1.getI1D()));
        // 疾患_神経系_片麻痺 */
        cpnTucRaiAssI.setI1E(CommonDtoUtil.strValToInt(subInfoI1.getI1E()));
        // 疾患_神経系_多発性硬化症 */
        cpnTucRaiAssI.setI1F(CommonDtoUtil.strValToInt(subInfoI1.getI1F()));
        // 疾患_神経系_対麻痺 */
        cpnTucRaiAssI.setI1G(CommonDtoUtil.strValToInt(subInfoI1.getI1G()));
        // 疾患_神経系_パーキンソン病 */
        cpnTucRaiAssI.setI1H(CommonDtoUtil.strValToInt(subInfoI1.getI1H()));
        // 疾患_神経系_四肢麻痺 */
        cpnTucRaiAssI.setI1I(CommonDtoUtil.strValToInt(subInfoI1.getI1I()));
        // 疾患_神経系_脳卒中/脳血管障害 */
        cpnTucRaiAssI.setI1J(CommonDtoUtil.strValToInt(subInfoI1.getI1J()));
        // 疾患_心肺系_CHD */
        cpnTucRaiAssI.setI1K(CommonDtoUtil.strValToInt(subInfoI1.getI1K()));
        // 疾患_心肺系_COPD */
        cpnTucRaiAssI.setI1L(CommonDtoUtil.strValToInt(subInfoI1.getI1L()));
        // 疾患_心肺系_CHF */
        cpnTucRaiAssI.setI1M(CommonDtoUtil.strValToInt(subInfoI1.getI1M()));
        // 疾患_心肺系_高血圧症 */
        cpnTucRaiAssI.setI1N(CommonDtoUtil.strValToInt(subInfoI1.getI1N()));
        // 疾患_精神_不安症 */
        cpnTucRaiAssI.setI1O(CommonDtoUtil.strValToInt(subInfoI1.getI1O()));
        // 疾患_精神_双極性障害 */
        cpnTucRaiAssI.setI1P(CommonDtoUtil.strValToInt(subInfoI1.getI1P()));
        // 疾患_精神_うつ */
        cpnTucRaiAssI.setI1Q(CommonDtoUtil.strValToInt(subInfoI1.getI1Q()));
        // 疾患_精神_統合失調症 */
        cpnTucRaiAssI.setI1R(CommonDtoUtil.strValToInt(subInfoI1.getI1R()));
        // 疾患_感染症_肺炎 */
        cpnTucRaiAssI.setI1S(CommonDtoUtil.strValToInt(subInfoI1.getI1S()));
        // 疾患_感染症_UTI */
        cpnTucRaiAssI.setI1T(CommonDtoUtil.strValToInt(subInfoI1.getI1T()));
        // 疾患_その他_がん */
        cpnTucRaiAssI.setI1U(CommonDtoUtil.strValToInt(subInfoI1.getI1U()));
        // 疾患_その他_糖尿病 */
        cpnTucRaiAssI.setI1V(CommonDtoUtil.strValToInt(subInfoI1.getI1V()));
        // i1_メモ */
        cpnTucRaiAssI.setI1MemoKnj(subInfoI1.getI1MemoKnj());
        // i1_メモフォント */
        cpnTucRaiAssI.setI1MemoFont(CommonDtoUtil.strValToInt(subInfoI1.getI1MemoFont()));
        // i1_メモ色 */
        cpnTucRaiAssI.setI1MemoColor(CommonDtoUtil.strValToInt(subInfoI1.getI1MemoColor()));

        CpnTucRaiAssICriteria criteria = new CpnTucRaiAssICriteria();
        criteria.createCriteria().andRaiIdEqualTo(
                // アセスメントID
                CommonDtoUtil.strValToInt(raiId));

        // DAOを実行
        this.cpnTucRaiAssIMapper.updateByCriteriaSelective(cpnTucRaiAssI, criteria);

    }

    /**
     * サブI2情報更新
     * 関数名：updateRrkSubInfoI2
     *
     * @param @param raiId アセスメントID
     * @param inDto  入力DTO
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoI2(String raiId, AssessmentInterRAIUpdateServiceInDto inDto)
            throws ExclusiveException {
        CpnTucRaiAssI cpnTucRaiAssI = new CpnTucRaiAssI();
        // i2_メモ */
        cpnTucRaiAssI.setI2MemoKnj(inDto.getSubInfoI2().getI2MemoKnj());
        // i2_メモフォント */
        cpnTucRaiAssI.setI2MemoFont(CommonDtoUtil.strValToInt(inDto.getSubInfoI2().getI2MemoFont()));
        // i2_メモ色 */
        cpnTucRaiAssI.setI2MemoColor(CommonDtoUtil.strValToInt(inDto.getSubInfoI2().getI2MemoColor()));

        CpnTucRaiAssICriteria criteria = new CpnTucRaiAssICriteria();
        criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));

        this.cpnTucRaiAssIMapper.updateByCriteriaSelective(cpnTucRaiAssI, criteria);

        // 6.2.1.2.リクエストパラメータ.疾患（診断）情報リストの件数＞0件の場合、
        if (CollectionUtils.isNotEmpty(inDto.getDiagnosisInfoList())) {
            // 【繰り返し開始】リクエストパラメータ.疾患（診断）情報リストを繰り返して、
            for (int i = 0; i < inDto.getDiagnosisInfoList().size(); i++) {
                GUI00773DiagnosisInfo diagnosisInfo = inDto.getDiagnosisInfoList().get(i);
                // 6.2.1.2.1. リクエストパラメータ.疾患（診断）情報リスト.更新区分が"C":新規の場合、
                if (CommonConstants.UPDATE_KBN_C.equals(diagnosisInfo.getDiagnosisUpdateKbn())) {
                    // 【28-xxインターライ方式_アセスメント_I疾患（診断）】情報を登録する。
                    CpnTucRaiAssI2 cpnTucRaiAssI2 = new CpnTucRaiAssI2();
                    // 疾患（診断）情報パラメータを設定する
                    // 表示順 */
                    cpnTucRaiAssI2.setSort(CommonDtoUtil.strValToInt(diagnosisInfo.getSort()));
                    // 診断名 */
                    cpnTucRaiAssI2.setShindanKnj(diagnosisInfo.getShindanKnj());
                    // 疾患コード */
                    cpnTucRaiAssI2.setShikkanCd(CommonDtoUtil.strValToInt(diagnosisInfo.getShikkanCd()));
                    // ICD-CMコード1 */
                    cpnTucRaiAssI2.setIcdCmCode1(diagnosisInfo.getIcdCmCode1());
                    // ICD-CMコード2 */
                    cpnTucRaiAssI2.setIcdCmCode2(diagnosisInfo.getIcdCmCode2());
                    // アセスメントID
                    cpnTucRaiAssI2.setRaiId(CommonDtoUtil.strValToInt(raiId));

                    this.cpnTucRaiAssI2Mapper.insertSelective(cpnTucRaiAssI2);
                } else if (CommonConstants.UPDATE_KBN_U.equals(diagnosisInfo.getDiagnosisUpdateKbn())) {
                    // 6.2.1.2.2.リクエストパラメータ.疾患（診断）情報リスト.更新区分が"U":更新の場合、
                    // 【28-xxインターライ方式_アセスメント_I疾患（診断）】情報を更新する。
                    CpnTucRaiAssI2 cpnTucRaiAssI2 = new CpnTucRaiAssI2();
                    // 疾患（診断）情報パラメータを設定する
                    // 表示順 */
                    cpnTucRaiAssI2.setSort(CommonDtoUtil.strValToInt(diagnosisInfo.getSort()));
                    // 診断名 */
                    cpnTucRaiAssI2.setShindanKnj(diagnosisInfo.getShindanKnj());
                    // 疾患コード */
                    cpnTucRaiAssI2.setShikkanCd(CommonDtoUtil.strValToInt(diagnosisInfo.getShikkanCd()));
                    // ICD-CMコード1 */
                    cpnTucRaiAssI2.setIcdCmCode1(diagnosisInfo.getIcdCmCode1());
                    // ICD-CMコード2 */
                    cpnTucRaiAssI2.setIcdCmCode2(diagnosisInfo.getIcdCmCode2());

                    CpnTucRaiAssI2Criteria CpnTucRaiAssI2Criteria = new CpnTucRaiAssI2Criteria();
                    CpnTucRaiAssI2Criteria.createCriteria().andRaiIdEqualTo(
                            CommonDtoUtil.strValToInt(raiId))
                            .andIdEqualTo(CommonDtoUtil.strValToInt(diagnosisInfo.getId()));

                    this.cpnTucRaiAssI2Mapper.updateByCriteriaSelective(cpnTucRaiAssI2,
                            CpnTucRaiAssI2Criteria);

                    // 6.2.1.3.リクエストパラメータ.疾患（診断）情報リスト.更新区分が"D":削除の場合、
                    // 【28-xx インターライ方式_アセスメント_I疾患（診断）】情報を削除する。
                } else if (CommonConstants.UPDATE_KBN_D.equals(diagnosisInfo.getDiagnosisUpdateKbn())) {
                    CpnTucRaiAssI2Criteria cpnTucRaiAssI2Criteria = new CpnTucRaiAssI2Criteria();
                    cpnTucRaiAssI2Criteria.createCriteria()
                            .andRaiIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                            .andIdEqualTo(CommonDtoUtil.strValToInt(diagnosisInfo.getId()));

                    // DAOを実行
                    cpnTucRaiAssI2Mapper.deleteByCriteria(cpnTucRaiAssI2Criteria);
                }

            }
        }
    }

    /**
     * サブJ情報更新
     * 関数名：updateRrkSubInfoJ
     *
     * @param @param   raiId アセスメントID
     * @param subInfoJ サブJ情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoJ(String raiId, GUI00774SubInfoJ subInfoJ)
            throws ExclusiveException {
        CpnTucRaiAssJCriteria criteria = new CpnTucRaiAssJCriteria();
        criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));

        CpnTucRaiAssJ cpnTucRaiAssJ = new CpnTucRaiAssJ();
        // 転倒 j1
        cpnTucRaiAssJ.setJ1(CommonDtoUtil.strValToInt(subInfoJ.getJ1()));
        // 最近の転倒 j2
        cpnTucRaiAssJ.setJ2(CommonDtoUtil.strValToInt(subInfoJ.getJ2()));
        // 問題頻度_バランス_立位 j3_a
        cpnTucRaiAssJ.setJ3A(CommonDtoUtil.strValToInt(subInfoJ.getJ3A()));
        // 問題頻度_バランス_方向転換 j3_b
        cpnTucRaiAssJ.setJ3B(CommonDtoUtil.strValToInt(subInfoJ.getJ3B()));
        // 問題頻度_バランス_めまい j3_c
        cpnTucRaiAssJ.setJ3C(CommonDtoUtil.strValToInt(subInfoJ.getJ3C()));
        // 問題頻度_バランス_歩行 j3_d
        cpnTucRaiAssJ.setJ3D(CommonDtoUtil.strValToInt(subInfoJ.getJ3D()));
        // 問題頻度_心肺_胸痛 j3_e
        cpnTucRaiAssJ.setJ3E(CommonDtoUtil.strValToInt(subInfoJ.getJ3E()));
        // 問題頻度_心肺_気道 j3_f
        cpnTucRaiAssJ.setJ3F(CommonDtoUtil.strValToInt(subInfoJ.getJ3F()));
        // 問題頻度_精神_思考 j3_g
        cpnTucRaiAssJ.setJ3G(CommonDtoUtil.strValToInt(subInfoJ.getJ3G()));
        // 問題頻度_精神_妄想 j3_h
        cpnTucRaiAssJ.setJ3H(CommonDtoUtil.strValToInt(subInfoJ.getJ3H()));
        // 問題頻度_精神_幻覚 j3_i
        cpnTucRaiAssJ.setJ3I(CommonDtoUtil.strValToInt(subInfoJ.getJ3I()));
        // 問題頻度_神経_失語症 j3_j
        cpnTucRaiAssJ.setJ3J(CommonDtoUtil.strValToInt(subInfoJ.getJ3J()));
        // 問題頻度_消化器系_胃酸 j3_k
        cpnTucRaiAssJ.setJ3K(CommonDtoUtil.strValToInt(subInfoJ.getJ3K()));
        // 問題頻度_消化器系_便秘 j3_l
        cpnTucRaiAssJ.setJ3L(CommonDtoUtil.strValToInt(subInfoJ.getJ3L()));
        // 問題頻度_消化器系_下痢 j3_m
        cpnTucRaiAssJ.setJ3M(CommonDtoUtil.strValToInt(subInfoJ.getJ3M()));
        // 問題頻度_消化器系_嘔吐 j3_n
        cpnTucRaiAssJ.setJ3N(CommonDtoUtil.strValToInt(subInfoJ.getJ3N()));
        // 問題頻度_睡眠障害_入眠 j3_o
        cpnTucRaiAssJ.setJ3O(CommonDtoUtil.strValToInt(subInfoJ.getJ3O()));
        // 問題頻度_睡眠障害_過多 j3_p
        cpnTucRaiAssJ.setJ3P(CommonDtoUtil.strValToInt(subInfoJ.getJ3P()));
        // 問題頻度_その他_誤嚥 j3_q
        cpnTucRaiAssJ.setJ3Q(CommonDtoUtil.strValToInt(subInfoJ.getJ3Q()));
        // 問題頻度_その他_発熱 j3_r
        cpnTucRaiAssJ.setJ3R(CommonDtoUtil.strValToInt(subInfoJ.getJ3R()));
        // 問題頻度_その他_出血 j3_s
        cpnTucRaiAssJ.setJ3S(CommonDtoUtil.strValToInt(subInfoJ.getJ3S()));
        // 問題頻度_その他_不衛生 j3_t
        cpnTucRaiAssJ.setJ3T(CommonDtoUtil.strValToInt(subInfoJ.getJ3T()));
        // 問題頻度_その他_末梢浮腫 j3_u
        cpnTucRaiAssJ.setJ3U(CommonDtoUtil.strValToInt(subInfoJ.getJ3U()));
        // 呼吸困難 j4
        cpnTucRaiAssJ.setJ4(CommonDtoUtil.strValToInt(subInfoJ.getJ4()));
        // 疲労感 j5
        cpnTucRaiAssJ.setJ5(CommonDtoUtil.strValToInt(subInfoJ.getJ5()));
        // 痛み_頻度 j6_a
        cpnTucRaiAssJ.setJ6A(CommonDtoUtil.strValToInt(subInfoJ.getJ6A()));
        // 痛み_程度 j6_b
        cpnTucRaiAssJ.setJ6B(CommonDtoUtil.strValToInt(subInfoJ.getJ6B()));
        // 痛み_持続性 j6_c
        cpnTucRaiAssJ.setJ6C(CommonDtoUtil.strValToInt(subInfoJ.getJ6C()));
        // 痛み_突発痛 j6_d
        cpnTucRaiAssJ.setJ6D(CommonDtoUtil.strValToInt(subInfoJ.getJ6D()));
        // 痛み_コントロール j6_e
        cpnTucRaiAssJ.setJ6E(CommonDtoUtil.strValToInt(subInfoJ.getJ6E()));
        // 状態の不安定性_症状 j7_a
        cpnTucRaiAssJ.setJ7A(CommonDtoUtil.strValToInt(subInfoJ.getJ7A()));
        // 状態の不安定性_発性 j7_b
        cpnTucRaiAssJ.setJ7B(CommonDtoUtil.strValToInt(subInfoJ.getJ7B()));
        // 状態の不安定性_末期の疾患 j7_c
        cpnTucRaiAssJ.setJ7C(CommonDtoUtil.strValToInt(subInfoJ.getJ7C()));
        // 主観的健康感 j8
        cpnTucRaiAssJ.setJ8(CommonDtoUtil.strValToInt(subInfoJ.getJ8()));
        // 喫煙と飲酒_喫煙 j9_a
        cpnTucRaiAssJ.setJ9A(CommonDtoUtil.strValToInt(subInfoJ.getJ9A()));
        // 喫煙と飲酒_飲酒 j9_b
        cpnTucRaiAssJ.setJ9B(CommonDtoUtil.strValToInt(subInfoJ.getJ9B()));
        // j1_メモ j1_memo_knj
        cpnTucRaiAssJ.setJ1MemoKnj(subInfoJ.getJ1MemoKnj());
        // j1_メモフォント j1_memo_font
        cpnTucRaiAssJ.setJ1MemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ1MemoFont()));
        // j1_メモ色 j1_memo_color
        cpnTucRaiAssJ.setJ1MemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ1MemoColor()));
        // j2_メモ j2_memo_knj
        cpnTucRaiAssJ.setJ2MemoKnj(subInfoJ.getJ2MemoKnj());
        // j2_メモフォント j2_memo_font
        cpnTucRaiAssJ.setJ2MemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ2MemoFont()));
        // j2_メモ色 j2_memo_color
        cpnTucRaiAssJ.setJ2MemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ2MemoColor()));
        // j3_メモ j3_memo_knj
        cpnTucRaiAssJ.setJ3MemoKnj(subInfoJ.getJ3MemoKnj());
        // j3_メモフォント j3_memo_font
        cpnTucRaiAssJ.setJ3MemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ3MemoFont()));
        // j3_メモ色 j3_memo_color
        cpnTucRaiAssJ.setJ3MemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ3MemoColor()));
        // j4_メモ j4_memo_knj
        cpnTucRaiAssJ.setJ4MemoKnj(subInfoJ.getJ4MemoKnj());
        // j4_メモフォント j4_memo_font
        cpnTucRaiAssJ.setJ4MemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ4MemoFont()));
        // j4_メモ色 j4_memo_color
        cpnTucRaiAssJ.setJ4MemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ4MemoColor()));
        // j5_メモ j5_memo_knj
        cpnTucRaiAssJ.setJ5MemoKnj(subInfoJ.getJ5MemoKnj());
        // j5_メモフォント j5_memo_font
        cpnTucRaiAssJ.setJ5MemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ5MemoFont()));
        // j5_メモ色 j5_memo_color
        cpnTucRaiAssJ.setJ5MemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ5MemoColor()));
        // j6_a_メモ j6_a_memo_knj
        cpnTucRaiAssJ.setJ6AMemoKnj(subInfoJ.getJ6AMemoKnj());
        // j6_a_メモフォント j6_a_memo_font
        cpnTucRaiAssJ.setJ6AMemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ6AMemoFont()));
        // j6_a_メモ色 j6_a_memo_color
        cpnTucRaiAssJ.setJ6AMemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ6AMemoColor()));
        // j6_b_メモ j6_b_memo_knj
        cpnTucRaiAssJ.setJ6BMemoKnj(subInfoJ.getJ6BMemoKnj());
        // j6_b_メモフォント j6_b_memo_font
        cpnTucRaiAssJ.setJ6BMemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ6BMemoFont()));
        // j6_b_メモ色 j6_b_memo_color
        cpnTucRaiAssJ.setJ6BMemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ6BMemoColor()));
        // j6_c_メモ j6_c_memo_knj
        cpnTucRaiAssJ.setJ6CMemoKnj(subInfoJ.getJ6CMemoKnj());
        // j6_c_メモフォント j6_c_memo_font
        cpnTucRaiAssJ.setJ6CMemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ6CMemoFont()));
        // j6_c_メモ色 j6_c_memo_color
        cpnTucRaiAssJ.setJ6CMemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ6CMemoColor()));
        // j6_d_メモ j6_d_memo_knj
        cpnTucRaiAssJ.setJ6DMemoKnj(subInfoJ.getJ6DMemoKnj());
        // j6_d_メモフォント j6_d_memo_font
        cpnTucRaiAssJ.setJ6DMemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ6DMemoFont()));
        // j6_d_メモ色 j6_d_memo_color
        cpnTucRaiAssJ.setJ6DMemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ6DMemoColor()));
        // j6_e_メモ j6_e_memo_knj
        cpnTucRaiAssJ.setJ6EMemoKnj(subInfoJ.getJ6EMemoKnj());
        // j6_e_メモフォント j6_e_memo_font
        cpnTucRaiAssJ.setJ6EMemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ6EMemoFont()));
        // j6_e_メモ色 j6_e_memo_color
        cpnTucRaiAssJ.setJ6EMemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ6EMemoColor()));
        // j7_メモ j7_memo_knj
        cpnTucRaiAssJ.setJ7MemoKnj(subInfoJ.getJ7MemoKnj());
        // j7_メモフォント j7_memo_font
        cpnTucRaiAssJ.setJ7MemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ7MemoFont()));
        // j7_メモ色 j7_memo_color
        cpnTucRaiAssJ.setJ7MemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ7MemoColor()));
        // j8_メモ j8_memo_knj
        cpnTucRaiAssJ.setJ8MemoKnj(subInfoJ.getJ8MemoKnj());
        // j8_メモフォント j8_memo_font
        cpnTucRaiAssJ.setJ8MemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ8MemoFont()));
        // j8_メモ色 j8_memo_color
        cpnTucRaiAssJ.setJ8MemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ8MemoColor()));
        // j9_a_メモ j9_a_memo_knj
        cpnTucRaiAssJ.setJ9AMemoKnj(subInfoJ.getJ9AMemoKnj());
        // j9_a_メモフォント j9_a_memo_font
        cpnTucRaiAssJ.setJ9AMemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ9AMemoFont()));
        // j9_a_メモ色 j9_a_memo_color
        cpnTucRaiAssJ.setJ9AMemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ9AMemoColor()));
        // j9_b_メモ j9_b_memo_knj
        cpnTucRaiAssJ.setJ9BMemoKnj(subInfoJ.getJ9BMemoKnj());
        // j9_b_メモフォント j9_b_memo_font
        cpnTucRaiAssJ.setJ9BMemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ9BMemoFont()));
        // j9_b_メモ色 j9_b_memo_color
        cpnTucRaiAssJ.setJ9BMemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ9BMemoColor()));

        // DAOを実行
        this.cpnTucRaiAssJMapper.updateByCriteriaSelective(cpnTucRaiAssJ, criteria);
    }

    /**
     * サブK情報更新
     * 関数名：updateRrkSubInfoK
     *
     * @param @param   raiId アセスメントID
     * @param subInfoK サブK情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoK(String raiId, Gui00775SubInfoK subInfoK)
            throws ExclusiveException {
        CpnTucRaiAssKCriteria criteria = new CpnTucRaiAssKCriteria();
        criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));

        CpnTucRaiAssK cpnTucRaiAssK = new CpnTucRaiAssK();
        // 身長（cm）
        BigDecimal K1A = new BigDecimal(subInfoK.getK1A());
        cpnTucRaiAssK.setK1A(K1A);

        // 体重（kg）
        BigDecimal K1B = new BigDecimal(subInfoK.getK1B());
        cpnTucRaiAssK.setK1B(K1B);

        // 栄養上の問題_体重減少
        cpnTucRaiAssK.setK2A(CommonDtoUtil.strValToInt(subInfoK.getK2A()));

        // 栄養上の問題_脱水
        cpnTucRaiAssK.setK2B(CommonDtoUtil.strValToInt(subInfoK.getK2B()));

        // 栄養上の問題_水分摂取
        cpnTucRaiAssK.setK2C(CommonDtoUtil.strValToInt(subInfoK.getK2C()));

        // 栄養上の問題_水分排泄量
        cpnTucRaiAssK.setK2D(CommonDtoUtil.strValToInt(subInfoK.getK2D()));

        // 栄養摂取の方法
        cpnTucRaiAssK.setK3(CommonDtoUtil.strValToInt(subInfoK.getK3()));

        // 経静脈／経管栄養摂取量
        cpnTucRaiAssK.setK4(CommonDtoUtil.strValToInt(subInfoK.getK4()));

        // 歯科口腔_義歯
        cpnTucRaiAssK.setK5A(CommonDtoUtil.strValToInt(subInfoK.getK5A()));

        // 歯科口腔_正常でない
        cpnTucRaiAssK.setK5B(CommonDtoUtil.strValToInt(subInfoK.getK5B()));

        // 歯科口腔_痛み/不快感
        cpnTucRaiAssK.setK5C(CommonDtoUtil.strValToInt(subInfoK.getK5C()));

        // 歯科口腔_口の渇き
        cpnTucRaiAssK.setK5D(CommonDtoUtil.strValToInt(subInfoK.getK5D()));

        // 歯科口腔_咀嚼困難
        cpnTucRaiAssK.setK5E(CommonDtoUtil.strValToInt(subInfoK.getK5E()));

        // 歯科口腔_歯肉
        cpnTucRaiAssK.setK5F(CommonDtoUtil.strValToInt(subInfoK.getK5F()));

        // 栄養管理_食物形態の加工
        cpnTucRaiAssK.setK6A(CommonDtoUtil.strValToInt(subInfoK.getK6A()));

        // 栄養管理_低塩分
        cpnTucRaiAssK.setK6B(CommonDtoUtil.strValToInt(subInfoK.getK6B()));

        // 栄養管理_カロリー制限
        cpnTucRaiAssK.setK6C(CommonDtoUtil.strValToInt(subInfoK.getK6C()));

        // 栄養管理_低脂肪
        cpnTucRaiAssK.setK6D(CommonDtoUtil.strValToInt(subInfoK.getK6D()));

        // 栄養管理_その他
        cpnTucRaiAssK.setK6E(CommonDtoUtil.strValToInt(subInfoK.getK6E()));

        // k1_メモ
        cpnTucRaiAssK.setK1MemoKnj(subInfoK.getK1MemoKnj());

        // k1_メモフォント
        cpnTucRaiAssK.setK1MemoFont(CommonDtoUtil.strValToInt(subInfoK.getK1MemoFont()));

        // k1_メモ色
        cpnTucRaiAssK.setK1MemoColor(CommonDtoUtil.strValToInt(subInfoK.getK1MemoColor()));

        // k2_メモ
        cpnTucRaiAssK.setK2MemoKnj(subInfoK.getK2MemoKnj());

        // k2_メモフォント
        cpnTucRaiAssK.setK2MemoFont(CommonDtoUtil.strValToInt(subInfoK.getK2MemoFont()));

        // k2_メモ色
        cpnTucRaiAssK.setK2MemoColor(CommonDtoUtil.strValToInt(subInfoK.getK2MemoColor()));

        // k3_メモ
        cpnTucRaiAssK.setK3MemoKnj(subInfoK.getK3MemoKnj());

        // k3_メモフォント
        cpnTucRaiAssK.setK3MemoFont(CommonDtoUtil.strValToInt(subInfoK.getK3MemoFont()));

        // k3_メモ色
        cpnTucRaiAssK.setK3MemoColor(CommonDtoUtil.strValToInt(subInfoK.getK3MemoColor()));

        // k4_メモ
        cpnTucRaiAssK.setK4MemoKnj(subInfoK.getK4MemoKnj());

        // k4_メモフォント
        cpnTucRaiAssK.setK4MemoFont(CommonDtoUtil.strValToInt(subInfoK.getK4MemoFont()));

        // k4_メモ色
        cpnTucRaiAssK.setK4MemoColor(CommonDtoUtil.strValToInt(subInfoK.getK4MemoColor()));

        // k5_メモ
        cpnTucRaiAssK.setK5MemoKnj(subInfoK.getK5MemoKnj());

        // k5_メモフォント
        cpnTucRaiAssK.setK5MemoFont(CommonDtoUtil.strValToInt(subInfoK.getK5MemoFont()));

        // k5_メモ色
        cpnTucRaiAssK.setK5MemoColor(CommonDtoUtil.strValToInt(subInfoK.getK5MemoColor()));

        // k6_メモ
        cpnTucRaiAssK.setK6MemoKnj(subInfoK.getK6MemoKnj());

        // k6_メモフォント
        cpnTucRaiAssK.setK6MemoFont(CommonDtoUtil.strValToInt(subInfoK.getK6MemoFont()));

        // k6_メモ色
        cpnTucRaiAssK.setK6MemoColor(CommonDtoUtil.strValToInt(subInfoK.getK6MemoColor()));
        // DAOを実行
        int count = this.cpnTucRaiAssKMapper.updateByCriteriaSelective(cpnTucRaiAssK, criteria);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * サブL情報更新
     * 関数名：updateRrkSubInfoL
     *
     * @param @param   raiId アセスメントID
     * @param subInfoL サブL情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoL(String raiId, GUI00776LjyouhoOutDto subInfoL)
            throws ExclusiveException {
        // TODO
    }

    /**
     * サブM情報更新
     * 関数名：updateRrkSubInfoM
     *
     * @param @param   raiId アセスメントID
     * @param subInfoM サブM情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoM(String raiId, Gui00777SubMInDto subInfoM)
            throws ExclusiveException {
        // TODO
    }

    /**
     * サブN情報更新
     * 関数名：updateRrkSubInfoN
     *
     * @param @param   raiId アセスメントID
     * @param subInfoN サブN情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoN(String raiId, Gui00778SubInfoNInDto subInfoN)
            throws ExclusiveException {
        // TODO
    }

    /**
     * サブO情報更新
     * 関数名：updateRrkSubInfoO
     *
     * @param @param   raiId アセスメントID
     * @param subInfoO サブO情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoO(String raiId, GUI00779SubInfoO subInfoO)
            throws ExclusiveException {
        CpnTucRaiAssO cpnTucRaiAssO = new CpnTucRaiAssO();
        // 健診予防接種_血圧
        cpnTucRaiAssO.setO1A(CommonDtoUtil.strValToInt(subInfoO.getO1A()));
        // 健診予防接種_大腸内視鏡
        cpnTucRaiAssO.setO1B(CommonDtoUtil.strValToInt(subInfoO.getO1B()));
        // 健診予防接種_歯科
        cpnTucRaiAssO.setO1C(CommonDtoUtil.strValToInt(subInfoO.getO1C()));
        // 健診予防接種_眼科
        cpnTucRaiAssO.setO1D(CommonDtoUtil.strValToInt(subInfoO.getO1D()));
        // 健診予防接種_聴力
        cpnTucRaiAssO.setO1E(CommonDtoUtil.strValToInt(subInfoO.getO1E()));
        // 健診予防接種_インフルエンザ
        cpnTucRaiAssO.setO1F(CommonDtoUtil.strValToInt(subInfoO.getO1F()));
        // 健診予防接種_マンモグラフィー
        cpnTucRaiAssO.setO1G(CommonDtoUtil.strValToInt(subInfoO.getO1G()));
        // 健診予防接種_肺炎
        cpnTucRaiAssO.setO1H(CommonDtoUtil.strValToInt(subInfoO.getO1H()));
        // 治療ケア_治療_抗がん剤
        cpnTucRaiAssO.setO2A(CommonDtoUtil.strValToInt(subInfoO.getO2A()));
        // 治療ケア_治療_透析
        cpnTucRaiAssO.setO2B(CommonDtoUtil.strValToInt(subInfoO.getO2B()));
        // 治療ケア_治療_感染
        cpnTucRaiAssO.setO2C(CommonDtoUtil.strValToInt(subInfoO.getO2C()));
        // 治療ケア_治療_薬物投与
        cpnTucRaiAssO.setO2D(CommonDtoUtil.strValToInt(subInfoO.getO2D()));
        // 治療ケア_治療_酸素
        cpnTucRaiAssO.setO2E(CommonDtoUtil.strValToInt(subInfoO.getO2E()));
        // 治療ケア_治療_放射線
        cpnTucRaiAssO.setO2F(CommonDtoUtil.strValToInt(subInfoO.getO2F()));
        // 治療ケア_治療_吸引
        cpnTucRaiAssO.setO2G(CommonDtoUtil.strValToInt(subInfoO.getO2G()));
        // 治療ケア_治療_気管切開口
        cpnTucRaiAssO.setO2H(CommonDtoUtil.strValToInt(subInfoO.getO2H()));
        // 治療ケア_治療_輸血
        cpnTucRaiAssO.setO2I(CommonDtoUtil.strValToInt(subInfoO.getO2I()));
        // 治療ケア_治療_人工呼吸器
        cpnTucRaiAssO.setO2J(CommonDtoUtil.strValToInt(subInfoO.getO2J()));
        // 治療ケア_治療_創のケア
        cpnTucRaiAssO.setO2K(CommonDtoUtil.strValToInt(subInfoO.getO2K()));
        // 治療ケア_プログラム_トイレ
        cpnTucRaiAssO.setO2L(CommonDtoUtil.strValToInt(subInfoO.getO2L()));
        // 治療ケア_プログラム_緩和
        cpnTucRaiAssO.setO2M(CommonDtoUtil.strValToInt(subInfoO.getO2M()));
        // 治療ケア_プログラム_体位
        cpnTucRaiAssO.setO2N(CommonDtoUtil.strValToInt(subInfoO.getO2N()));
        // 訪問介護_実施日数(A)
        cpnTucRaiAssO.setO3AA(CommonDtoUtil.strValToInt(subInfoO.getO3AA()));
        // 訪問介護_分数(B)
        cpnTucRaiAssO.setO3AB(CommonDtoUtil.strValToInt(subInfoO.getO3AB()));
        // 訪問看護_実施日数(A)
        cpnTucRaiAssO.setO3BA(CommonDtoUtil.strValToInt(subInfoO.getO3BA()));
        // 訪問看護_分数(B)
        cpnTucRaiAssO.setO3BB(CommonDtoUtil.strValToInt(subInfoO.getO3BB()));
        // 通所介護リハ_実施日数(A)
        cpnTucRaiAssO.setO3CA(CommonDtoUtil.strValToInt(subInfoO.getO3CA()));
        // 食事/配食_実施日数(A)
        cpnTucRaiAssO.setO3DA(CommonDtoUtil.strValToInt(subInfoO.getO3DA()));
        // リハ_理学_計画日数(A)
        cpnTucRaiAssO.setO4AA(CommonDtoUtil.strValToInt(subInfoO.getO4AA()));
        // リハ_理学_実施日数(B)
        cpnTucRaiAssO.setO4AB(CommonDtoUtil.strValToInt(subInfoO.getO4AB()));
        // リハ_理学_分数(C)
        cpnTucRaiAssO.setO4AC(CommonDtoUtil.strValToInt(subInfoO.getO4AC()));
        // リハ_作業_計画日数(A)
        cpnTucRaiAssO.setO4BA(CommonDtoUtil.strValToInt(subInfoO.getO4BA()));
        // リハ_作業_実施日数(B)
        cpnTucRaiAssO.setO4BB(CommonDtoUtil.strValToInt(subInfoO.getO4BB()));
        // リハ_作業_分数(C)
        cpnTucRaiAssO.setO4BC(CommonDtoUtil.strValToInt(subInfoO.getO4BC()));
        // リハ_言語_計画日数(A)
        cpnTucRaiAssO.setO4CA(CommonDtoUtil.strValToInt(subInfoO.getO4CA()));
        // リハ_言語_実施日数(B)
        cpnTucRaiAssO.setO4CB(CommonDtoUtil.strValToInt(subInfoO.getO4CB()));
        // リハ_言語_分数(C)
        cpnTucRaiAssO.setO4CC(CommonDtoUtil.strValToInt(subInfoO.getO4CC()));
        // リハ_心理_計画日数(A)
        cpnTucRaiAssO.setO4DA(CommonDtoUtil.strValToInt(subInfoO.getO4DA()));
        // リハ_心理_実施日数(B)
        cpnTucRaiAssO.setO4DB(CommonDtoUtil.strValToInt(subInfoO.getO4DB()));
        // リハ_心理_分数(C)
        cpnTucRaiAssO.setO4DC(CommonDtoUtil.strValToInt(subInfoO.getO4DC()));
        // リハ_呼吸_計画日数(A)
        cpnTucRaiAssO.setO4EA(CommonDtoUtil.strValToInt(subInfoO.getO4EA()));
        // リハ_呼吸_実施日数(B)
        cpnTucRaiAssO.setO4EB(CommonDtoUtil.strValToInt(subInfoO.getO4EB()));
        // リハ_呼吸_分数(C)
        cpnTucRaiAssO.setO4EC(CommonDtoUtil.strValToInt(subInfoO.getO4EC()));
        // リハ_訓練_計画日数(A)
        cpnTucRaiAssO.setO4FA(CommonDtoUtil.strValToInt(subInfoO.getO4FA()));
        // リハ_訓練_実施日数(B)
        cpnTucRaiAssO.setO4FB(CommonDtoUtil.strValToInt(subInfoO.getO4FB()));
        // リハ_訓練_分数(C)
        cpnTucRaiAssO.setO4FC(CommonDtoUtil.strValToInt(subInfoO.getO4FC()));
        // 入院
        cpnTucRaiAssO.setO5A(CommonDtoUtil.strValToInt(subInfoO.getO5A()));
        // 救急外来
        cpnTucRaiAssO.setO5B(CommonDtoUtil.strValToInt(subInfoO.getO5B()));
        // 医師の診察
        cpnTucRaiAssO.setO5C(CommonDtoUtil.strValToInt(subInfoO.getO5C()));
        // 受診
        cpnTucRaiAssO.setO6(CommonDtoUtil.strValToInt(subInfoO.getO6()));
        // 医師の指示変更
        cpnTucRaiAssO.setO7(CommonDtoUtil.strValToInt(subInfoO.getO7()));
        // 身体抑制
        cpnTucRaiAssO.setO8A(CommonDtoUtil.strValToInt(subInfoO.getO8A()));
        // 身体抑制_ベッド柵
        cpnTucRaiAssO.setO8B(CommonDtoUtil.strValToInt(subInfoO.getO8B()));
        // 身体抑制_体幹部
        cpnTucRaiAssO.setO8C(CommonDtoUtil.strValToInt(subInfoO.getO8C()));
        // 身体抑制_立ち上がり
        cpnTucRaiAssO.setO8D(CommonDtoUtil.strValToInt(subInfoO.getO8D()));
        // o1_メモ
        cpnTucRaiAssO.setO1MemoKnj(subInfoO.getO1MemoKnj());
        // o1_メモフォント
        cpnTucRaiAssO.setO1MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO1MemoFont()));
        // o1_メモ色
        cpnTucRaiAssO.setO1MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO1MemoColor()));
        // o2_メモ
        cpnTucRaiAssO.setO2MemoKnj(subInfoO.getO2MemoKnj());
        // o2_メモフォント
        cpnTucRaiAssO.setO2MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO2MemoFont()));
        // o2_メモ色
        cpnTucRaiAssO.setO2MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO2MemoColor()));
        // o3_メモ
        cpnTucRaiAssO.setO3MemoKnj(subInfoO.getO3MemoKnj());
        // o3_メモフォント
        cpnTucRaiAssO.setO3MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO3MemoFont()));
        // o3_メモ色
        cpnTucRaiAssO.setO3MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO3MemoColor()));
        // o4_メモ
        cpnTucRaiAssO.setO4MemoKnj(subInfoO.getO4MemoKnj());
        // o4_メモフォント
        cpnTucRaiAssO.setO4MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO4MemoFont()));
        // o4_メモ色
        cpnTucRaiAssO.setO4MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO4MemoColor()));
        // o5_メモ
        cpnTucRaiAssO.setO5MemoKnj(subInfoO.getO5MemoKnj());
        // o5_メモフォント
        cpnTucRaiAssO.setO5MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO5MemoFont()));
        // o5_メモ色
        cpnTucRaiAssO.setO5MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO5MemoColor()));
        // o6_メモ
        cpnTucRaiAssO.setO6MemoKnj(subInfoO.getO6MemoKnj());
        // o6_メモフォント
        cpnTucRaiAssO.setO6MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO6MemoFont()));
        // o6_メモ色
        cpnTucRaiAssO.setO6MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO6MemoColor()));
        // o7_メモ
        cpnTucRaiAssO.setO7MemoKnj(subInfoO.getO7MemoKnj());
        // o7_メモフォント
        cpnTucRaiAssO.setO7MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO7MemoFont()));
        // o7_メモ色
        cpnTucRaiAssO.setO7MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO7MemoColor()));
        // o8_メモ
        cpnTucRaiAssO.setO8MemoKnj(subInfoO.getO8MemoKnj());
        // o8_メモフォント
        cpnTucRaiAssO.setO8MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO8MemoFont()));
        // o8_メモ色
        cpnTucRaiAssO.setO8MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO8MemoColor()));

        CpnTucRaiAssOCriteria criteria = new CpnTucRaiAssOCriteria();
        criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));

        this.cpnTucRaiAssOMapper.updateByCriteriaSelective(cpnTucRaiAssO, criteria);

    }

    /**
     * サブP情報更新
     * 関数名：updateRrkSubInfoP
     *
     * @param @param   raiId アセスメントID
     * @param subInfoP サブP情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoP(String raiId, Gui00780UpdateSubInfoP subInfoP)
            throws ExclusiveException {
        CpnTucRaiAssPCriteria criteria = new CpnTucRaiAssPCriteria();
        criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));

        CpnTucRaiAssP cpnTucRaiAssP = new CpnTucRaiAssP();
        // 意思決定権_法定後見人等
        cpnTucRaiAssP.setP1A(CommonDtoUtil.strValToInt(subInfoP.getP1A()));
        // 意思決定権_任意後見
        cpnTucRaiAssP.setP1B(CommonDtoUtil.strValToInt(subInfoP.getP1B()));
        // 意思決定権_家族等の代理
        cpnTucRaiAssP.setP1C(CommonDtoUtil.strValToInt(subInfoP.getP1C()));
        // 事前指示_蘇生術
        cpnTucRaiAssP.setP2A(CommonDtoUtil.strValToInt(subInfoP.getP2A()));
        // 事前指示_挿管
        cpnTucRaiAssP.setP2B(CommonDtoUtil.strValToInt(subInfoP.getP2B()));
        // 事前指示_入院
        cpnTucRaiAssP.setP2C(CommonDtoUtil.strValToInt(subInfoP.getP2C()));
        // 事前指示_経管栄養
        cpnTucRaiAssP.setP2D(CommonDtoUtil.strValToInt(subInfoP.getP2D()));
        // 事前指示_薬剤制限
        cpnTucRaiAssP.setP2E(CommonDtoUtil.strValToInt(subInfoP.getP2E()));
        // p1_メモ
        cpnTucRaiAssP.setP1MemoKnj(subInfoP.getP1MemoKnj());
        // p1_メモフォント
        cpnTucRaiAssP.setP1MemoFont(CommonDtoUtil.strValToInt(subInfoP.getP1MemoFont()));
        // p1_メモ色
        cpnTucRaiAssP.setP1MemoColor(CommonDtoUtil.strValToInt(subInfoP.getP1MemoColor()));
        // p2_メモ
        cpnTucRaiAssP.setP2MemoKnj(subInfoP.getP2MemoKnj());
        // p2_メモフォント
        cpnTucRaiAssP.setP2MemoFont(CommonDtoUtil.strValToInt(subInfoP.getP2MemoFont()));
        // p2_メモ色
        cpnTucRaiAssP.setP2MemoColor(CommonDtoUtil.strValToInt(subInfoP.getP2MemoColor()));
        // DAOを実行
        int count = this.cpnTucRaiAssPMapper.updateByCriteriaSelective(cpnTucRaiAssP, criteria);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * サブQ情報更新
     * 関数名：updateRrkSubInfoQ
     *
     * @param @param   raiId アセスメントID
     * @param subInfoQ サブQ情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoQ(String raiId, GUI00781UpdateSubInfoQ subInfoQ)
            throws ExclusiveException {
        CpnTucRaiAssQ cpnTucRaiAssQ = new CpnTucRaiAssQ();
        // インフォーマル_関係(主)
        cpnTucRaiAssQ.setQ1A1(CommonDtoUtil.strValToInt(subInfoQ.getQ1A1()));
        // インフォーマル_関係(副)
        cpnTucRaiAssQ.setQ1A2(CommonDtoUtil.strValToInt(subInfoQ.getQ1A2()));
        // インフォーマル_同居(主)
        cpnTucRaiAssQ.setQ1B1(CommonDtoUtil.strValToInt(subInfoQ.getQ1B1()));
        // インフォーマル_同居(副)
        cpnTucRaiAssQ.setQ1B2(CommonDtoUtil.strValToInt(subInfoQ.getQ1B2()));
        // インフォーマル_IADL(主)
        cpnTucRaiAssQ.setQ1C1(CommonDtoUtil.strValToInt(subInfoQ.getQ1C1()));
        // インフォーマル_IADL(副)
        cpnTucRaiAssQ.setQ1C2(CommonDtoUtil.strValToInt(subInfoQ.getQ1C2()));
        // インフォーマル_ADL(主)
        cpnTucRaiAssQ.setQ1D1(CommonDtoUtil.strValToInt(subInfoQ.getQ1D1()));
        // インフォーマル_ADL(副)
        cpnTucRaiAssQ.setQ1D2(CommonDtoUtil.strValToInt(subInfoQ.getQ1D2()));
        // インフォーマル状況_ケア
        cpnTucRaiAssQ.setQ2A(CommonDtoUtil.strValToInt(subInfoQ.getQ2A()));
        // インフォーマル状況_苦悩
        cpnTucRaiAssQ.setQ2B(CommonDtoUtil.strValToInt(subInfoQ.getQ2B()));
        // インフォーマル状況_憔悴
        cpnTucRaiAssQ.setQ2C(CommonDtoUtil.strValToInt(subInfoQ.getQ2C()));
        // インフォーマルな援助量
        cpnTucRaiAssQ.setQ3(CommonDtoUtil.strValToInt(subInfoQ.getQ3()));
        // q1a_メモ
        cpnTucRaiAssQ.setQ1AMemoKnj(subInfoQ.getQ1AMemoKnj());
        // q1a_メモフォント
        cpnTucRaiAssQ.setQ1AMemoFont(CommonDtoUtil.strValToInt(subInfoQ.getQ1AMemoFont()));
        // q1a_メモ色
        cpnTucRaiAssQ.setQ1AMemoColor(CommonDtoUtil.strValToInt(subInfoQ.getQ1AMemoColor()));
        // q1b_メモ
        cpnTucRaiAssQ.setQ1BMemoKnj(subInfoQ.getQ1BMemoKnj());
        // q1b_メモフォント
        cpnTucRaiAssQ.setQ1BMemoFont(CommonDtoUtil.strValToInt(subInfoQ.getQ1BMemoFont()));
        // q1b_メモ色
        cpnTucRaiAssQ.setQ1BMemoColor(CommonDtoUtil.strValToInt(subInfoQ.getQ1BMemoColor()));
        // q1cd_メモ
        cpnTucRaiAssQ.setQ1CdMemoKnj(subInfoQ.getQ1CdMemoKnj());
        // q1cd_メモフォント
        cpnTucRaiAssQ.setQ1CdMemoFont(CommonDtoUtil.strValToInt(subInfoQ.getQ1CdMemoFont()));
        // q1cd_メモ色
        cpnTucRaiAssQ.setQ1CdMemoColor(CommonDtoUtil.strValToInt(subInfoQ.getQ1CdMemoColor()));
        // q2_メモ
        cpnTucRaiAssQ.setQ2MemoKnj(subInfoQ.getQ2MemoKnj());
        // q2_メモフォント
        cpnTucRaiAssQ.setQ2MemoFont(CommonDtoUtil.strValToInt(subInfoQ.getQ2MemoFont()));
        // q2_メモ色
        cpnTucRaiAssQ.setQ2MemoColor(CommonDtoUtil.strValToInt(subInfoQ.getQ2MemoColor()));
        // q3_メモ
        cpnTucRaiAssQ.setQ3MemoKnj(subInfoQ.getQ3MemoKnj());
        // q3_メモフォント
        cpnTucRaiAssQ.setQ3MemoFont(CommonDtoUtil.strValToInt(subInfoQ.getQ3MemoFont()));
        // q3_メモ色
        cpnTucRaiAssQ.setQ3MemoColor(CommonDtoUtil.strValToInt(subInfoQ.getQ3MemoColor()));

        CpnTucRaiAssQCriteria criteria = new CpnTucRaiAssQCriteria();
        criteria.createCriteria().andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));
        int count = this.cpnTucRaiAssQMapper.updateByCriteriaSelective(cpnTucRaiAssQ, criteria);
        if (count <= 0) {
            throw new ExclusiveException();
        }

    }

    /**
     * サブR情報更新
     * 関数名：updateRrkSubInfoR
     *
     * @param @param   raiId アセスメントID
     * @param subInfoR サブR情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoR(String raiId, GUI00782SubInfoR subInfoR)
            throws ExclusiveException {
        CpnTucRaiAssR cpnTucRaiAssR = new CpnTucRaiAssR();
        // アセスメントID
        cpnTucRaiAssR.setRaiId(CommonDtoUtil.strValToInt(raiId));
        // 退所の可能性_利用者
        cpnTucRaiAssR.setR1A(CommonDtoUtil.strValToInt(subInfoR.getR1A()));
        // 退所の可能性_支援者
        cpnTucRaiAssR.setR1B(CommonDtoUtil.strValToInt(subInfoR.getR1B()));
        // 退所の可能性_家
        cpnTucRaiAssR.setR1C(CommonDtoUtil.strValToInt(subInfoR.getR1C()));
        // 退所の予測期間
        cpnTucRaiAssR.setR2(CommonDtoUtil.strValToInt(subInfoR.getR2()));
        // r1_メモ
        cpnTucRaiAssR.setR1MemoKnj(subInfoR.getR1MemoKnj());
        // r1_メモフォント
        cpnTucRaiAssR.setR1MemoFont(CommonDtoUtil.strValToInt(subInfoR.getR1MemoFont()));
        // r1_メモ色
        cpnTucRaiAssR.setR1MemoColor(CommonDtoUtil.strValToInt(subInfoR.getR1MemoColor()));
        // r2_メモ
        cpnTucRaiAssR.setR2MemoKnj(subInfoR.getR2MemoKnj());
        // r2_メモフォント
        cpnTucRaiAssR.setR2MemoFont(CommonDtoUtil.strValToInt(subInfoR.getR2MemoFont()));
        // r2_メモ色
        cpnTucRaiAssR.setR2MemoColor(CommonDtoUtil.strValToInt(subInfoR.getR2MemoColor()));

        CpnTucRaiAssRCriteria cpnTucRaiAssRCriteria = new CpnTucRaiAssRCriteria();
        cpnTucRaiAssRCriteria.createCriteria()
                // アセスメントID
                .andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));
        int count = this.cpnTucRaiAssRMapper.updateByCriteriaSelective(cpnTucRaiAssR,
                cpnTucRaiAssRCriteria);

        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * サブS情報更新
     * 関数名：updateRrkSubInfoS
     *
     * @param @param   raiId アセスメントID
     * @param subInfoS サブS情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoS(String raiId, GUI00783SubInfoS subInfoS)
            throws ExclusiveException {
        CpnTucRaiAssS cpnTucRaiAssS = new CpnTucRaiAssS();
        // アセスメントID
        cpnTucRaiAssS.setRaiId(CommonDtoUtil.strValToInt(raiId));

        cpnTucRaiAssS.setS1A(CommonDtoUtil.strValToInt(subInfoS.getS1A()));

        cpnTucRaiAssS.setS1B(CommonDtoUtil.strValToInt(subInfoS.getS1B()));

        cpnTucRaiAssS.setS1C(CommonDtoUtil.strValToInt(subInfoS.getS1C()));

        cpnTucRaiAssS.setS1D(CommonDtoUtil.strValToInt(subInfoS.getS1D()));

        cpnTucRaiAssS.setS1E(CommonDtoUtil.strValToInt(subInfoS.getS1E()));

        cpnTucRaiAssS.setS2(CommonDtoUtil.strValToInt(subInfoS.getS2()));

        cpnTucRaiAssS.setS3A(CommonDtoUtil.strValToInt(subInfoS.getS3A()));

        cpnTucRaiAssS.setS3B(CommonDtoUtil.strValToInt(subInfoS.getS3B()));

        cpnTucRaiAssS.setS3C(CommonDtoUtil.strValToInt(subInfoS.getS3C()));

        cpnTucRaiAssS.setS4(CommonDtoUtil.strValToInt(subInfoS.getS4()));
        // S1_メモ
        cpnTucRaiAssS.setS1MemoKnj(subInfoS.getS1MemoKnj());
        // S1_メモフォント
        cpnTucRaiAssS.setS1MemoFont(CommonDtoUtil.strValToInt(subInfoS.getS1MemoFont()));
        // S1_メモ色
        cpnTucRaiAssS.setS1MemoColor(CommonDtoUtil.strValToInt(subInfoS.getS1MemoColor()));
        // S2_メモ
        cpnTucRaiAssS.setS2MemoKnj(subInfoS.getS2MemoKnj());
        // S2_メモフォント
        cpnTucRaiAssS.setS2MemoFont(CommonDtoUtil.strValToInt(subInfoS.getS2MemoFont()));
        // S2_メモ色
        cpnTucRaiAssS.setS2MemoColor(CommonDtoUtil.strValToInt(subInfoS.getS2MemoColor()));
        // S3_メモ
        cpnTucRaiAssS.setS3MemoKnj(subInfoS.getS3MemoKnj());
        // S3_メモフォント
        cpnTucRaiAssS.setS3MemoFont(CommonDtoUtil.strValToInt(subInfoS.getS3MemoFont()));
        // S3_メモ色
        cpnTucRaiAssS.setS3MemoColor(CommonDtoUtil.strValToInt(subInfoS.getS3MemoColor()));
        // s4_メモ
        cpnTucRaiAssS.setS4MemoKnj(subInfoS.getS4MemoKnj());
        // s4_メモフォント
        cpnTucRaiAssS.setS4MemoFont(CommonDtoUtil.strValToInt(subInfoS.getS4MemoFont()));
        // s4_メモ色
        cpnTucRaiAssS.setS4MemoColor(CommonDtoUtil.strValToInt(subInfoS.getS4MemoColor()));

        CpnTucRaiAssSCriteria cpnTucRaiAssRCriteria = new CpnTucRaiAssSCriteria();
        cpnTucRaiAssRCriteria.createCriteria()
                // アセスメントID
                .andRaiIdEqualTo(CommonDtoUtil.strValToInt(raiId));
        int count = this.cpnTucRaiAssSMapper.updateByCriteriaSelective(cpnTucRaiAssS,
                cpnTucRaiAssRCriteria);

        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * サブT情報更新
     * 関数名：updateRrkSubInfoT
     *
     * @param @param   raiId アセスメントID
     * @param subInfoT サブT情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoT(String raiId, Gui00784SubInfoTInDto subInfoT)
            throws ExclusiveException {
        // TODO
    }

    /**
     * サブU情報更新
     * 関数名：updateRrkSubInfoU
     *
     * @param @param   raiId アセスメントID
     * @param subInfoU サブU情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoU(String raiId, Gui00785SubUUpdateInDto subInfoU) throws ExclusiveException {
        // TODO
    }

    /**
     * サブV情報更新
     * 関数名：updateRrkSubInfoV
     *
     * @param @param   raiId アセスメントID
     * @param subInfoU サブV情報
     * @return 結果outDto
     * @throws ExclusiveException
     */
    private void updateRrkSubInfoV(String raiId, Gui00786SubInfoVUpdateOutDto subInfoV) throws ExclusiveException {
        // TODO
    }

    /**
     * サブA情報登録
     * 関数名：insertRrkSubInfoA
     *
     * @param @param   raiId アセスメントID
     * @param subInfoA サブA情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoA(String raiId, GUI00764SubInfoA subInfoA) {
        CpnTucRaiAssA cpnTucRaiAssA = new CpnTucRaiAssA();
        // アセスメントID
        cpnTucRaiAssA.setRaiId(CommonDtoUtil.strValToInt(raiId));
        // 婚姻状況 */
        cpnTucRaiAssA.setA4(CommonDtoUtil.strValToInt(subInfoA.getA4()));
        // 要介護度 */
        cpnTucRaiAssA.setA7(CommonDtoUtil.strValToInt(subInfoA.getA7()));
        // アセスメントの理由 */
        cpnTucRaiAssA.setA8(CommonDtoUtil.strValToInt(subInfoA.getA8()));
        // 本人のケアの目標 */
        cpnTucRaiAssA.setA10Knj(subInfoA.getA10Knj());
        // アセスメント時の居住場所 */
        cpnTucRaiAssA.setA11(CommonDtoUtil.strValToInt(subInfoA.getA11()));
        // 同居形態_同居者 */
        cpnTucRaiAssA.setA12A(CommonDtoUtil.strValToInt(subInfoA.getA12A()));
        // 同居形態_変化 */
        cpnTucRaiAssA.setA12B(CommonDtoUtil.strValToInt(subInfoA.getA12B()));
        // 同居形態_他の居住 */
        cpnTucRaiAssA.setA12C(CommonDtoUtil.strValToInt(subInfoA.getA12C()));
        // 退院後の経過期間 */
        cpnTucRaiAssA.setA13(CommonDtoUtil.strValToInt(subInfoA.getA13()));
        // a4_メモ */
        cpnTucRaiAssA.setA4MemoKnj(subInfoA.getA4MemoKnj());
        // a4_メモフォント */
        cpnTucRaiAssA.setA4MemoFont(CommonDtoUtil.strValToInt(subInfoA.getA4MemoFont()));
        // a4_メモ色 */
        cpnTucRaiAssA.setA4MemoColor(CommonDtoUtil.strValToInt(subInfoA.getA4MemoColor()));
        // a7_メモ */
        cpnTucRaiAssA.setA7MemoKnj(subInfoA.getA7MemoKnj());
        // a7_メモフォント */
        cpnTucRaiAssA.setA7MemoFont(CommonDtoUtil.strValToInt(subInfoA.getA7MemoFont()));
        // a7_メモ色 */
        cpnTucRaiAssA.setA7MemoColor(CommonDtoUtil.strValToInt(subInfoA.getA7MemoColor()));
        // a8_メモ */
        cpnTucRaiAssA.setA8MemoKnj(subInfoA.getA8MemoKnj());
        // a8_メモフォント */
        cpnTucRaiAssA.setA8MemoFont(CommonDtoUtil.strValToInt(subInfoA.getA8MemoFont()));
        // a8_メモ色 */
        cpnTucRaiAssA.setA8MemoColor(CommonDtoUtil.strValToInt(subInfoA.getA8MemoColor()));
        // a11_メモ */
        cpnTucRaiAssA.setA11MemoKnj(subInfoA.getA11MemoKnj());
        // a11_メモフォント */
        cpnTucRaiAssA.setA11MemoFont(CommonDtoUtil.strValToInt(subInfoA.getA11MemoFont()));
        // a11_メモ色 */
        cpnTucRaiAssA.setA11MemoColor(CommonDtoUtil.strValToInt(subInfoA.getA11MemoColor()));
        // a12_a_メモ */
        cpnTucRaiAssA.setA12AMemoKnj(subInfoA.getA12AMemoKnj());
        // a12_a_メモフォント */
        cpnTucRaiAssA.setA12AMemoFont(CommonDtoUtil.strValToInt(subInfoA.getA12AMemoFont()));
        // a12_a_メモ色 */
        cpnTucRaiAssA.setA12AMemoColor(CommonDtoUtil.strValToInt(subInfoA.getA12AMemoColor()));
        // a12_b_メモ */
        cpnTucRaiAssA.setA12BMemoKnj(subInfoA.getA12BMemoKnj());
        // a12_b_メモフォント */
        cpnTucRaiAssA.setA12BMemoFont(CommonDtoUtil.strValToInt(subInfoA.getA12BMemoFont()));
        // a12_b_メモ色 */
        cpnTucRaiAssA.setA12BMemoColor(CommonDtoUtil.strValToInt(subInfoA.getA12BMemoColor()));
        // a12C_メモ */
        cpnTucRaiAssA.setA12CMemoKnj(subInfoA.getA12CMemoKnj());
        // a12C_メモフォント */
        cpnTucRaiAssA.setA12CMemoFont(CommonDtoUtil.strValToInt(subInfoA.getA12CMemoFont()));
        // a12C_メモ色 */
        cpnTucRaiAssA.setA12CMemoColor(CommonDtoUtil.strValToInt(subInfoA.getA12CMemoColor()));
        // a13_メモ */
        cpnTucRaiAssA.setA13MemoKnj(subInfoA.getA13MemoKnj());
        // a13_メモフォント */
        cpnTucRaiAssA.setA13MemoFont(CommonDtoUtil.strValToInt(subInfoA.getA13MemoFont()));
        // a13_メモ色 */
        cpnTucRaiAssA.setA13MemoColor(CommonDtoUtil.strValToInt(subInfoA.getA13MemoColor()));
        cpnTucRaiAssAMapper.insertSelective(cpnTucRaiAssA);
    }

    /**
     * サブB情報登録
     * 関数名：insertRrkSubInfoB
     *
     * @param @param   raiId アセスメントID
     * @param subInfoB サブB情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoB(String raiId, GUI00765SubInfoB subInfoB) {

        CpnTucRaiAssB cpnTucRaiAssB = new CpnTucRaiAssB();
        // アセスメントID
        cpnTucRaiAssB.setRaiId(CommonDtoUtil.strValToInt(raiId));
        // 入所に対して本人の意思度合
        cpnTucRaiAssB.setB1(CommonDtoUtil.strValToInt(subInfoB.getB1()));
        // 受付日
        cpnTucRaiAssB.setB2Ymd(subInfoB.getB2Ymd());
        // 相談受付時までの経過
        cpnTucRaiAssB.setB3Knj(subInfoB.getB3Knj());
        // 相談受付内容
        cpnTucRaiAssB.setB4Knj(subInfoB.getB4Knj());
        // 利用歴_介護施設、療養病院/病棟
        cpnTucRaiAssB.setB5A(CommonDtoUtil.strValToInt(subInfoB.getB5A()));
        // 利用歴_認知症対応型共同生活介護、小規模多機能型居宅介護
        cpnTucRaiAssB.setB5B(CommonDtoUtil.strValToInt(subInfoB.getB5B()));
        // 利用歴_高齢者住宅－有料老人ホーム
        cpnTucRaiAssB.setB5C(CommonDtoUtil.strValToInt(subInfoB.getB5C()));
        // 利用歴_精神科病院、精神科病棟
        cpnTucRaiAssB.setB5D(CommonDtoUtil.strValToInt(subInfoB.getB5D()));
        // 利用歴_精神障害者施設
        cpnTucRaiAssB.setB5E(CommonDtoUtil.strValToInt(subInfoB.getB5E()));
        // 利用歴_知的障害者施設
        cpnTucRaiAssB.setB5F(CommonDtoUtil.strValToInt(subInfoB.getB5F()));
        // 入所直前の居住場所
        cpnTucRaiAssB.setB6A(CommonDtoUtil.strValToInt(subInfoB.getB6A()));
        // 通常の居住場所
        cpnTucRaiAssB.setB6B(CommonDtoUtil.strValToInt(subInfoB.getB6B()));
        // 入所前の同居形態
        cpnTucRaiAssB.setB7(CommonDtoUtil.strValToInt(subInfoB.getB7()));
        // 精神疾患歴
        cpnTucRaiAssB.setB8(CommonDtoUtil.strValToInt(subInfoB.getB8()));
        // 教育歴
        cpnTucRaiAssB.setB9(CommonDtoUtil.strValToInt(subInfoB.getB9()));
        // 医療機関受診時の送迎
        cpnTucRaiAssB.setB10(CommonDtoUtil.strValToInt(subInfoB.getB10()));
        // 受診中の付き添いが必要
        cpnTucRaiAssB.setB11(CommonDtoUtil.strValToInt(subInfoB.getB11()));
        // b1_メモ
        cpnTucRaiAssB.setB1MemoKnj(subInfoB.getB1MemoKnj());
        // b1_メモフォント
        cpnTucRaiAssB.setB1MemoFont(CommonDtoUtil.strValToInt(subInfoB.getB1MemoFont()));
        // b1_メモ色
        cpnTucRaiAssB.setB1MemoColor(CommonDtoUtil.strValToInt(subInfoB.getB1MemoColor()));
        // b5_メモ
        cpnTucRaiAssB.setB5MemoKnj(subInfoB.getB5MemoKnj());
        // b5_メモフォント
        cpnTucRaiAssB.setB5MemoFont(CommonDtoUtil.strValToInt(subInfoB.getB5MemoFont()));
        // b5_メモ色
        cpnTucRaiAssB.setB5MemoColor(CommonDtoUtil.strValToInt(subInfoB.getB5MemoColor()));
        // b6_メモ
        cpnTucRaiAssB.setB6MemoKnj(subInfoB.getB6MemoKnj());
        // b6_メモフォント
        cpnTucRaiAssB.setB6MemoFont(CommonDtoUtil.strValToInt(subInfoB.getB6MemoFont()));
        // b6_メモ色
        cpnTucRaiAssB.setB6MemoColor(CommonDtoUtil.strValToInt(subInfoB.getB6MemoColor()));
        // b7_メモ
        cpnTucRaiAssB.setB7MemoKnj(subInfoB.getB7MemoKnj());
        // b7_メモフォント
        cpnTucRaiAssB.setB7MemoFont(CommonDtoUtil.strValToInt(subInfoB.getB7MemoFont()));
        // b7_メモ色
        cpnTucRaiAssB.setB7MemoColor(CommonDtoUtil.strValToInt(subInfoB.getB7MemoColor()));
        // b8_メモ
        cpnTucRaiAssB.setB8MemoKnj(subInfoB.getB8MemoKnj());
        // b8_メモフォント
        cpnTucRaiAssB.setB8MemoFont(CommonDtoUtil.strValToInt(subInfoB.getB8MemoFont()));
        // b8_メモ色
        cpnTucRaiAssB.setB8MemoColor(CommonDtoUtil.strValToInt(subInfoB.getB8MemoColor()));
        // b9_メモ
        cpnTucRaiAssB.setB9MemoKnj(subInfoB.getB9MemoKnj());
        // b9_メモフォント
        cpnTucRaiAssB.setB9MemoFont(CommonDtoUtil.strValToInt(subInfoB.getB9MemoFont()));
        // b9_メモ色
        cpnTucRaiAssB.setB9MemoColor(CommonDtoUtil.strValToInt(subInfoB.getB9MemoColor()));
        // b10_メモ
        cpnTucRaiAssB.setB10MemoKnj(subInfoB.getB10MemoKnj());
        // b10_メモフォント
        cpnTucRaiAssB.setB10MemoFont(CommonDtoUtil.strValToInt(subInfoB.getB10MemoFont()));
        // b10_メモ色
        cpnTucRaiAssB.setB10MemoColor(CommonDtoUtil.strValToInt(subInfoB.getB10MemoColor()));
        // b11_メモ
        cpnTucRaiAssB.setB11MemoKnj(subInfoB.getB11MemoKnj());
        // b11_メモフォント
        cpnTucRaiAssB.setB11MemoFont(CommonDtoUtil.strValToInt(subInfoB.getB11MemoFont()));
        // b11_メモ色
        cpnTucRaiAssB.setB11MemoColor(CommonDtoUtil.strValToInt(subInfoB.getB11MemoColor()));

        // DAOを実行
        this.cpnTucRaiAssBMapper.insertSelective(cpnTucRaiAssB);
    }

    /**
     * サブC情報登録
     * 関数名：insertRrkSubInfoC
     *
     * @param @param   raiId アセスメントID
     * @param subInfoC サブC情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoC(String raiId, GUI00766SubInfoC subInfoC) {
        // サブ情報（C）
        CpnTucRaiAssC cpnTucRaiAssC = new CpnTucRaiAssC();

        // 日常の意思決定の認知能力 */
        cpnTucRaiAssC.setC1(CommonDtoUtil.strValToInt(subInfoC.getC1()));
        // 短期記憶 */
        cpnTucRaiAssC.setC2A(CommonDtoUtil.strValToInt(subInfoC.getC2A()));
        // 長期記憶 */
        cpnTucRaiAssC.setC2B(CommonDtoUtil.strValToInt(subInfoC.getC2B()));
        // 手続き記憶 */
        cpnTucRaiAssC.setC2C(CommonDtoUtil.strValToInt(subInfoC.getC2C()));
        // 状況記憶 */
        cpnTucRaiAssC.setC2D(CommonDtoUtil.strValToInt(subInfoC.getC2D()));
        // せん妄の兆候_注意 */
        cpnTucRaiAssC.setC3A(CommonDtoUtil.strValToInt(subInfoC.getC3A()));
        // せん妄の兆候_会話 */
        cpnTucRaiAssC.setC3B(CommonDtoUtil.strValToInt(subInfoC.getC3B()));
        // せん妄の兆候_精神機能 */
        cpnTucRaiAssC.setC3C(CommonDtoUtil.strValToInt(subInfoC.getC3C()));
        // 精神状態の急な変化 */
        cpnTucRaiAssC.setC4(CommonDtoUtil.strValToInt(subInfoC.getC4()));
        // 意思決定能力の変化 */
        cpnTucRaiAssC.setC5(CommonDtoUtil.strValToInt(subInfoC.getC5()));
        // c1_メモ */
        cpnTucRaiAssC.setC1MemoKnj(subInfoC.getC1MemoKnj());
        // c1_メモフォント */
        cpnTucRaiAssC.setC1MemoFont(CommonDtoUtil.strValToInt(subInfoC.getC1MemoFont()));
        // c1_メモ色 */
        cpnTucRaiAssC.setC1MemoColor(CommonDtoUtil.strValToInt(subInfoC.getC1MemoColor()));
        // c2_メモ */
        cpnTucRaiAssC.setC2MemoKnj(subInfoC.getC2MemoKnj());
        // c2_メモフォント */
        cpnTucRaiAssC.setC2MemoFont(CommonDtoUtil.strValToInt(subInfoC.getC2MemoFont()));
        // c2_メモ色 */
        cpnTucRaiAssC.setC2MemoColor(CommonDtoUtil.strValToInt(subInfoC.getC2MemoColor()));
        // c3_メモ */
        cpnTucRaiAssC.setC3MemoKnj(subInfoC.getC3MemoKnj());
        // c3_メモフォント */
        cpnTucRaiAssC.setC3MemoFont(CommonDtoUtil.strValToInt(subInfoC.getC3MemoFont()));
        // c3_メモ色 */
        cpnTucRaiAssC.setC3MemoColor(CommonDtoUtil.strValToInt(subInfoC.getC3MemoColor()));
        // c4_メモ */
        cpnTucRaiAssC.setC4MemoKnj(subInfoC.getC4MemoKnj());
        // c4_メモフォント */
        cpnTucRaiAssC.setC4MemoFont(CommonDtoUtil.strValToInt(subInfoC.getC4MemoFont()));
        // c4_メモ色 */
        cpnTucRaiAssC.setC4MemoColor(CommonDtoUtil.strValToInt(subInfoC.getC4MemoColor()));
        // c5_メモ */
        cpnTucRaiAssC.setC5MemoKnj(subInfoC.getC5MemoKnj());
        // c5_メモフォント */
        cpnTucRaiAssC.setC5MemoFont(CommonDtoUtil.strValToInt(subInfoC.getC5MemoFont()));
        // c5_メモ色 */
        cpnTucRaiAssC.setC5MemoColor(CommonDtoUtil.strValToInt(subInfoC.getC5MemoColor()));

        // アセスメントID
        cpnTucRaiAssC.setRaiId(CommonDtoUtil.strValToInt(raiId));

        // DAOを実行
        this.cpnTucRaiAssCMapper.insertSelective(cpnTucRaiAssC);
    }

    /**
     * サブD情報登録
     * 関数名：insertRrkSubInfoD
     *
     * @param @param   raiId アセスメントID
     * @param subInfoD サブD情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoD(String raiId, GUI00767SubInfoD subInfoD) {
        final CpnTucRaiAssD cpnTucRaiAssD = new CpnTucRaiAssD();

        // アセスメントID
        cpnTucRaiAssD.setRaiId(CommonDtoUtil.strValToInt(raiId));
        // 理解させる能力
        cpnTucRaiAssD.setD1(CommonDtoUtil.strValToInt(subInfoD.getD1()));
        // 理解できる能力
        cpnTucRaiAssD.setD2(CommonDtoUtil.strValToInt(subInfoD.getD2()));
        // 聴力
        cpnTucRaiAssD.setD3A(CommonDtoUtil.strValToInt(subInfoD.getD3A()));
        // 補聴器の使用
        cpnTucRaiAssD.setD3B(CommonDtoUtil.strValToInt(subInfoD.getD3B()));
        // 視力
        cpnTucRaiAssD.setD4A(CommonDtoUtil.strValToInt(subInfoD.getD4A()));
        // 眼鏡、コンタクトレンズ、拡大鏡の使用
        cpnTucRaiAssD.setD4B(CommonDtoUtil.strValToInt(subInfoD.getD4B()));
        // d1_メモ
        cpnTucRaiAssD.setD1MemoKnj(subInfoD.getD1MemoKnj());
        // d1_メモフォント
        cpnTucRaiAssD.setD1MemoFont(CommonDtoUtil.strValToInt(subInfoD.getD1MemoFont()));
        // d1_メモ色
        cpnTucRaiAssD.setD1MemoColor(CommonDtoUtil.strValToInt(subInfoD.getD1MemoColor()));
        // d2_メモ
        cpnTucRaiAssD.setD2MemoKnj(subInfoD.getD2MemoKnj());
        // d2_メモフォント
        cpnTucRaiAssD.setD2MemoFont(CommonDtoUtil.strValToInt(subInfoD.getD2MemoFont()));
        // d2_メモ色
        cpnTucRaiAssD.setD2MemoColor(CommonDtoUtil.strValToInt(subInfoD.getD2MemoColor()));
        // d3_a_メモ
        cpnTucRaiAssD.setD3AMemoKnj(subInfoD.getD3AMemoKnj());
        // d3_a_メモフォント
        cpnTucRaiAssD.setD3AMemoFont(CommonDtoUtil.strValToInt(subInfoD.getD3AMemoFont()));
        // d3_a_メモ色
        cpnTucRaiAssD.setD3AMemoColor(CommonDtoUtil.strValToInt(subInfoD.getD3AMemoColor()));
        // d3_b_メモ
        cpnTucRaiAssD.setD3BMemoKnj(subInfoD.getD3BMemoKnj());
        // d3_b_メモフォント
        cpnTucRaiAssD.setD3BMemoFont(CommonDtoUtil.strValToInt(subInfoD.getD3BMemoFont()));
        // d3_b_メモ色
        cpnTucRaiAssD.setD3BMemoColor(CommonDtoUtil.strValToInt(subInfoD.getD3BMemoColor()));
        // d4_a_メモ
        cpnTucRaiAssD.setD4AMemoKnj(subInfoD.getD4AMemoKnj());
        // d4_a_メモフォント
        cpnTucRaiAssD.setD4AMemoFont(CommonDtoUtil.strValToInt(subInfoD.getD4AMemoFont()));
        // d4_a_メモ色
        cpnTucRaiAssD.setD4AMemoColor(CommonDtoUtil.strValToInt(subInfoD.getD4AMemoColor()));
        // d4_b_メモ
        cpnTucRaiAssD.setD4BMemoKnj(subInfoD.getD4BMemoKnj());
        // d4_b_メモフォント
        cpnTucRaiAssD.setD4BMemoFont(CommonDtoUtil.strValToInt(subInfoD.getD4BMemoFont()));
        // d4_b_メモ色
        cpnTucRaiAssD.setD4BMemoColor(CommonDtoUtil.strValToInt(subInfoD.getD4BMemoColor()));

        this.cpnTucRaiAssDMapper.insertSelective(cpnTucRaiAssD);
    }

    /**
     * サブE情報登録
     * 関数名：insertRrkSubInfoE
     *
     * @param @param   raiId アセスメントID
     * @param subInfoE サブE情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoE(String raiId, GUI00768SubInfoE subInfoE) {
        CpnTucRaiAssE cpnTucRaiAssE = new CpnTucRaiAssE();
        cpnTucRaiAssE.setRaiId(CommonDtoUtil.strValToInt(raiId));
        // E気分と行動情報設定
        // 気分の兆候_否定
        cpnTucRaiAssE.setE1A(CommonDtoUtil.strValToInt(subInfoE.getE1A()));
        // 気分の兆候_怒り
        cpnTucRaiAssE.setE1B(CommonDtoUtil.strValToInt(subInfoE.getE1B()));
        // 気分の兆候_恐れ
        cpnTucRaiAssE.setE1C(CommonDtoUtil.strValToInt(subInfoE.getE1C()));
        // 気分の兆候_不調
        cpnTucRaiAssE.setE1D(CommonDtoUtil.strValToInt(subInfoE.getE1D()));
        // 気分の兆候_不安
        cpnTucRaiAssE.setE1E(CommonDtoUtil.strValToInt(subInfoE.getE1E()));
        // 気分の兆候_悲しみ苦悩心配
        cpnTucRaiAssE.setE1F(CommonDtoUtil.strValToInt(subInfoE.getE1F()));
        // 気分の兆候_泣く
        cpnTucRaiAssE.setE1G(CommonDtoUtil.strValToInt(subInfoE.getE1G()));
        // 気分の兆候_ひどいことが起こりそう
        cpnTucRaiAssE.setE1H(CommonDtoUtil.strValToInt(subInfoE.getE1H()));
        // 気分の兆候_興味
        cpnTucRaiAssE.setE1I(CommonDtoUtil.strValToInt(subInfoE.getE1I()));
        // 気分の兆候_社会的交流の減少
        cpnTucRaiAssE.setE1J(CommonDtoUtil.strValToInt(subInfoE.getE1J()));
        // 気分の兆候_快感喪失
        cpnTucRaiAssE.setE1K(CommonDtoUtil.strValToInt(subInfoE.getE1K()));
        // 利用者自身_興味や喜び
        cpnTucRaiAssE.setE2A(CommonDtoUtil.strValToInt(subInfoE.getE2A()));
        // 利用者自身_不安
        cpnTucRaiAssE.setE2B(CommonDtoUtil.strValToInt(subInfoE.getE2B()));
        // 利用者自身_絶望
        cpnTucRaiAssE.setE2C(CommonDtoUtil.strValToInt(subInfoE.getE2C()));
        // 行動の問題_徘徊
        cpnTucRaiAssE.setE3A(CommonDtoUtil.strValToInt(subInfoE.getE3A()));
        // 行動の問題_暴言
        cpnTucRaiAssE.setE3B(CommonDtoUtil.strValToInt(subInfoE.getE3B()));
        // 行動の問題_暴行
        cpnTucRaiAssE.setE3C(CommonDtoUtil.strValToInt(subInfoE.getE3C()));
        // 行動の問題_社会的迷惑行為
        cpnTucRaiAssE.setE3D(CommonDtoUtil.strValToInt(subInfoE.getE3D()));
        // 行動の問題_性的行動や脱衣
        cpnTucRaiAssE.setE3E(CommonDtoUtil.strValToInt(subInfoE.getE3E()));
        // 行動の問題_抵抗
        cpnTucRaiAssE.setE3F(CommonDtoUtil.strValToInt(subInfoE.getE3F()));
        // 行動の問題_退居家出
        cpnTucRaiAssE.setE3G(CommonDtoUtil.strValToInt(subInfoE.getE3G()));
        // 生活満足度
        cpnTucRaiAssE.setE4(CommonDtoUtil.strValToInt(subInfoE.getE4()));
        // e1_メモ
        cpnTucRaiAssE.setE1MemoKnj(subInfoE.getE1MemoKnj());
        // e1_メモフォント
        cpnTucRaiAssE.setE1MemoFont(CommonDtoUtil.strValToInt(subInfoE.getE1MemoFont()));
        // e1_メモ色
        cpnTucRaiAssE.setE1MemoColor(CommonDtoUtil.strValToInt(subInfoE.getE1MemoColor()));
        // e2_メモ
        cpnTucRaiAssE.setE2MemoKnj(subInfoE.getE2MemoKnj());
        // e2_メモフォント
        cpnTucRaiAssE.setE2MemoFont(CommonDtoUtil.strValToInt(subInfoE.getE2MemoFont()));
        // e2_メモ色
        cpnTucRaiAssE.setE2MemoColor(CommonDtoUtil.strValToInt(subInfoE.getE2MemoColor()));
        // e3_メモ
        cpnTucRaiAssE.setE3MemoKnj(subInfoE.getE3MemoKnj());
        // e3_メモフォント
        cpnTucRaiAssE.setE3MemoFont(CommonDtoUtil.strValToInt(subInfoE.getE3MemoFont()));
        // e3_メモ色
        cpnTucRaiAssE.setE3MemoColor(CommonDtoUtil.strValToInt(subInfoE.getE3MemoColor()));
        // e4_メモ
        cpnTucRaiAssE.setE4MemoKnj(subInfoE.getE4MemoKnj());
        // e4_メモフォント
        cpnTucRaiAssE.setE4MemoFont(CommonDtoUtil.strValToInt(subInfoE.getE4MemoFont()));
        // e4_メモ色
        cpnTucRaiAssE.setE4MemoColor(CommonDtoUtil.strValToInt(subInfoE.getE4MemoColor()));

        cpnTucRaiAssEMapper.insertSelective(cpnTucRaiAssE);
    }

    /**
     * サブF情報登録
     * 関数名：insertRrkSubInfoF
     *
     * @param @param   raiId アセスメントID
     * @param subInfoF サブF情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoF(String raiId, GUI00769SubInfoF subInfoF) {
        // TODO
    }

    /**
     * サブG情報登録
     * 関数名：insertRrkSubInfoG
     *
     * @param @param   raiId アセスメントID
     * @param subInfoG サブG情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoG(String raiId, Gui00770SubInfoGList subInfoG) {
        // TODO
    }

    /**
     * サブH情報登録
     * 関数名：insertRrkSubInfoH
     *
     * @param @param   raiId アセスメントID
     * @param subInfoH サブH情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoH(String raiId, GUI00771SubInfoH subInfoH) {
        CpnTucRaiAssH cpnTucRaiAssH = new CpnTucRaiAssH();
        // アセスメントID
        cpnTucRaiAssH.setRaiId(CommonDtoUtil.strValToInt(raiId));
        // 尿失禁
        cpnTucRaiAssH.setH1(CommonDtoUtil.strValToInt(subInfoH.getH1()));
        // 尿失禁器材
        cpnTucRaiAssH.setH2(CommonDtoUtil.strValToInt(subInfoH.getH2()));
        // 便失禁
        cpnTucRaiAssH.setH3(CommonDtoUtil.strValToInt(subInfoH.getH3()));
        // オムツやパッドの使用
        cpnTucRaiAssH.setH4(CommonDtoUtil.strValToInt(subInfoH.getH4()));
        // ストーマ
        cpnTucRaiAssH.setH5(CommonDtoUtil.strValToInt(subInfoH.getH5()));
        // h1メモ
        cpnTucRaiAssH.setH1MemoKnj(subInfoH.getH1MemoKnj());
        // h1メモフォント
        cpnTucRaiAssH.setH1MemoFont(CommonDtoUtil.strValToInt(subInfoH.getH1MemoFont()));
        // h1メモ色
        cpnTucRaiAssH.setH1MemoColor(CommonDtoUtil.strValToInt(subInfoH.getH1MemoColor()));
        // h2メモ
        cpnTucRaiAssH.setH2MemoKnj(subInfoH.getH2MemoKnj());
        // h2メモフォント
        cpnTucRaiAssH.setH2MemoFont(CommonDtoUtil.strValToInt(subInfoH.getH2MemoFont()));
        // h2メモ色
        cpnTucRaiAssH.setH2MemoColor(CommonDtoUtil.strValToInt(subInfoH.getH2MemoColor()));
        // h3メモ
        cpnTucRaiAssH.setH3MemoKnj(subInfoH.getH3MemoKnj());
        // h3メモフォント
        cpnTucRaiAssH.setH3MemoFont(CommonDtoUtil.strValToInt(subInfoH.getH3MemoFont()));
        // h3メモ色
        cpnTucRaiAssH.setH3MemoColor(CommonDtoUtil.strValToInt(subInfoH.getH3MemoColor()));
        // h4メモ
        cpnTucRaiAssH.setH4MemoKnj(subInfoH.getH4MemoKnj());
        // h4メモフォント
        cpnTucRaiAssH.setH4MemoFont(CommonDtoUtil.strValToInt(subInfoH.getH4MemoFont()));
        // h4メモ色
        cpnTucRaiAssH.setH4MemoColor(CommonDtoUtil.strValToInt(subInfoH.getH4MemoColor()));
        // h5メモ
        cpnTucRaiAssH.setH5MemoKnj(subInfoH.getH5MemoKnj());
        // h5メモフォント
        cpnTucRaiAssH.setH5MemoFont(CommonDtoUtil.strValToInt(subInfoH.getH5MemoFont()));
        // h5メモ色
        cpnTucRaiAssH.setH5MemoColor(CommonDtoUtil.strValToInt(subInfoH.getH5MemoColor()));

        cpnTucRaiAssHMapper.insertSelective(cpnTucRaiAssH);
    }

    /**
     * サブI1情報登録
     * 関数名：insertRrkSubInfoI1
     *
     * @param @param    raiId アセスメントID
     * @param subInfoI1 サブI1情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoI1(String raiId, GUI00772UpdateSubInfoI1 subInfoI1) {
        CpnTucRaiAssI cpnTucRaiAssI = new CpnTucRaiAssI();

        // 疾患_筋骨系_大腿骨骨折 */
        cpnTucRaiAssI.setI1A(CommonDtoUtil.strValToInt(subInfoI1.getI1A()));
        // 疾患_筋骨系_その他の骨折 */
        cpnTucRaiAssI.setI1B(CommonDtoUtil.strValToInt(subInfoI1.getI1B()));
        // 疾患_神経系_アルツハイマー病 */
        cpnTucRaiAssI.setI1C(CommonDtoUtil.strValToInt(subInfoI1.getI1C()));
        // 疾患_神経系_認知症 */
        cpnTucRaiAssI.setI1D(CommonDtoUtil.strValToInt(subInfoI1.getI1D()));
        // 疾患_神経系_片麻痺 */
        cpnTucRaiAssI.setI1E(CommonDtoUtil.strValToInt(subInfoI1.getI1E()));
        // 疾患_神経系_多発性硬化症 */
        cpnTucRaiAssI.setI1F(CommonDtoUtil.strValToInt(subInfoI1.getI1F()));
        // 疾患_神経系_対麻痺 */
        cpnTucRaiAssI.setI1G(CommonDtoUtil.strValToInt(subInfoI1.getI1G()));
        // 疾患_神経系_パーキンソン病 */
        cpnTucRaiAssI.setI1H(CommonDtoUtil.strValToInt(subInfoI1.getI1H()));
        // 疾患_神経系_四肢麻痺 */
        cpnTucRaiAssI.setI1I(CommonDtoUtil.strValToInt(subInfoI1.getI1I()));
        // 疾患_神経系_脳卒中/脳血管障害 */
        cpnTucRaiAssI.setI1J(CommonDtoUtil.strValToInt(subInfoI1.getI1J()));
        // 疾患_心肺系_CHD */
        cpnTucRaiAssI.setI1K(CommonDtoUtil.strValToInt(subInfoI1.getI1K()));
        // 疾患_心肺系_COPD */
        cpnTucRaiAssI.setI1L(CommonDtoUtil.strValToInt(subInfoI1.getI1L()));
        // 疾患_心肺系_CHF */
        cpnTucRaiAssI.setI1M(CommonDtoUtil.strValToInt(subInfoI1.getI1M()));
        // 疾患_心肺系_高血圧症 */
        cpnTucRaiAssI.setI1N(CommonDtoUtil.strValToInt(subInfoI1.getI1N()));
        // 疾患_精神_不安症 */
        cpnTucRaiAssI.setI1O(CommonDtoUtil.strValToInt(subInfoI1.getI1O()));
        // 疾患_精神_双極性障害 */
        cpnTucRaiAssI.setI1P(CommonDtoUtil.strValToInt(subInfoI1.getI1P()));
        // 疾患_精神_うつ */
        cpnTucRaiAssI.setI1Q(CommonDtoUtil.strValToInt(subInfoI1.getI1Q()));
        // 疾患_精神_統合失調症 */
        cpnTucRaiAssI.setI1R(CommonDtoUtil.strValToInt(subInfoI1.getI1R()));
        // 疾患_感染症_肺炎 */
        cpnTucRaiAssI.setI1S(CommonDtoUtil.strValToInt(subInfoI1.getI1S()));
        // 疾患_感染症_UTI */
        cpnTucRaiAssI.setI1T(CommonDtoUtil.strValToInt(subInfoI1.getI1T()));
        // 疾患_その他_がん */
        cpnTucRaiAssI.setI1U(CommonDtoUtil.strValToInt(subInfoI1.getI1U()));
        // 疾患_その他_糖尿病 */
        cpnTucRaiAssI.setI1V(CommonDtoUtil.strValToInt(subInfoI1.getI1V()));
        // i1_メモ */
        cpnTucRaiAssI.setI1MemoKnj(subInfoI1.getI1MemoKnj());
        // i1_メモフォント */
        cpnTucRaiAssI.setI1MemoFont(CommonDtoUtil.strValToInt(subInfoI1.getI1MemoFont()));
        // i1_メモ色 */
        cpnTucRaiAssI.setI1MemoColor(CommonDtoUtil.strValToInt(subInfoI1.getI1MemoColor()));

        // アセスメントID
        cpnTucRaiAssI.setRaiId(CommonDtoUtil.strValToInt(raiId));

        // DAOを実行
        this.cpnTucRaiAssIMapper.insertSelective(cpnTucRaiAssI);
    }

    /**
     * サブI2情報登録
     * 関数名：insertRrkSubInfoI2
     *
     * @param @param raiId アセスメントID
     * @param inDto  入力DTO
     * @return 結果outDto
     */
    private void insertRrkSubInfoI2(String raiId, AssessmentInterRAIUpdateServiceInDto inDto) {

        CpnTucRaiAssI cpnTucRaiAssI = new CpnTucRaiAssI();
        // パラメータ作成
        // i2_メモ */
        cpnTucRaiAssI.setI2MemoKnj(inDto.getSubInfoI2().getI2MemoKnj());
        // i2_メモフォント */
        cpnTucRaiAssI.setI2MemoFont(CommonDtoUtil.strValToInt(inDto.getSubInfoI2().getI2MemoFont()));
        // i2_メモ色 */
        cpnTucRaiAssI.setI2MemoColor(CommonDtoUtil.strValToInt(inDto.getSubInfoI2().getI2MemoColor()));
        // アセスメントID
        cpnTucRaiAssI.setRaiId(CommonDtoUtil.strValToInt(raiId));

        // DAOを実行
        this.cpnTucRaiAssIMapper.insertSelective(cpnTucRaiAssI);

        // 6.1.2. リクエストパラメータ.疾患（診断）情報リストの件数>0件の場合、
        if (CollectionUtils.isNotEmpty(inDto.getDiagnosisInfoList())) {
            // 【繰り返し開始】リクエストパラメータ.疾患（診断）情報リストを繰り返して、登録処理を行う。
            for (int i = 0; i < inDto.getDiagnosisInfoList().size(); i++) {
                CpnTucRaiAssI2 cpnTucRaiAssI2 = new CpnTucRaiAssI2();
                // 疾患（診断）情報パラメータを設定する
                // 表示順 */
                cpnTucRaiAssI2.setSort(CommonDtoUtil.strValToInt(inDto.getDiagnosisInfoList().get(i).getSort()));
                // 診断名 */
                cpnTucRaiAssI2.setShindanKnj(inDto.getDiagnosisInfoList().get(i).getShindanKnj());
                // 疾患コード */
                cpnTucRaiAssI2
                        .setShikkanCd(CommonDtoUtil.strValToInt(inDto.getDiagnosisInfoList().get(i).getShikkanCd()));
                // ICD-CMコード1 */
                cpnTucRaiAssI2.setIcdCmCode1(inDto.getDiagnosisInfoList().get(i).getIcdCmCode1());
                // ICD-CMコード2 */
                cpnTucRaiAssI2.setIcdCmCode2(inDto.getDiagnosisInfoList().get(i).getIcdCmCode2());
                // アセスメントID
                cpnTucRaiAssI2.setRaiId(CommonDtoUtil.strValToInt(raiId));

                this.cpnTucRaiAssI2Mapper.insertSelectiveAndReturn(cpnTucRaiAssI2);

            }
        }
    }

    /**
     * サブJ情報登録
     * 関数名：insertRrkSubInfoJ
     *
     * @param @param   raiId アセスメントID
     * @param subInfoJ サブJ情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoJ(String raiId, GUI00774SubInfoJ subInfoJ) {
        CpnTucRaiAssJ cpnTucRaiAssJ = new CpnTucRaiAssJ();
        // アセスメントID
        cpnTucRaiAssJ.setRaiId(CommonDtoUtil.strValToInt(raiId));
        // 転倒 j1
        cpnTucRaiAssJ.setJ1(CommonDtoUtil.strValToInt(subInfoJ.getJ1()));
        // 最近の転倒 j2
        cpnTucRaiAssJ.setJ2(CommonDtoUtil.strValToInt(subInfoJ.getJ2()));
        // 問題頻度_バランス_立位 j3_a
        cpnTucRaiAssJ.setJ3A(CommonDtoUtil.strValToInt(subInfoJ.getJ3A()));
        // 問題頻度_バランス_方向転換 j3_b
        cpnTucRaiAssJ.setJ3B(CommonDtoUtil.strValToInt(subInfoJ.getJ3B()));
        // 問題頻度_バランス_めまい j3_c
        cpnTucRaiAssJ.setJ3C(CommonDtoUtil.strValToInt(subInfoJ.getJ3C()));
        // 問題頻度_バランス_歩行 j3_d
        cpnTucRaiAssJ.setJ3D(CommonDtoUtil.strValToInt(subInfoJ.getJ3D()));
        // 問題頻度_心肺_胸痛 j3_e
        cpnTucRaiAssJ.setJ3E(CommonDtoUtil.strValToInt(subInfoJ.getJ3E()));
        // 問題頻度_心肺_気道 j3_f
        cpnTucRaiAssJ.setJ3F(CommonDtoUtil.strValToInt(subInfoJ.getJ3F()));
        // 問題頻度_精神_思考 j3_g
        cpnTucRaiAssJ.setJ3G(CommonDtoUtil.strValToInt(subInfoJ.getJ3G()));
        // 問題頻度_精神_妄想 j3_h
        cpnTucRaiAssJ.setJ3H(CommonDtoUtil.strValToInt(subInfoJ.getJ3H()));
        // 問題頻度_精神_幻覚 j3_i
        cpnTucRaiAssJ.setJ3I(CommonDtoUtil.strValToInt(subInfoJ.getJ3I()));
        // 問題頻度_神経_失語症 j3_j
        cpnTucRaiAssJ.setJ3J(CommonDtoUtil.strValToInt(subInfoJ.getJ3J()));
        // 問題頻度_消化器系_胃酸 j3_k
        cpnTucRaiAssJ.setJ3K(CommonDtoUtil.strValToInt(subInfoJ.getJ3K()));
        // 問題頻度_消化器系_便秘 j3_l
        cpnTucRaiAssJ.setJ3L(CommonDtoUtil.strValToInt(subInfoJ.getJ3L()));
        // 問題頻度_消化器系_下痢 j3_m
        cpnTucRaiAssJ.setJ3M(CommonDtoUtil.strValToInt(subInfoJ.getJ3M()));
        // 問題頻度_消化器系_嘔吐 j3_n
        cpnTucRaiAssJ.setJ3N(CommonDtoUtil.strValToInt(subInfoJ.getJ3N()));
        // 問題頻度_睡眠障害_入眠 j3_o
        cpnTucRaiAssJ.setJ3O(CommonDtoUtil.strValToInt(subInfoJ.getJ3O()));
        // 問題頻度_睡眠障害_過多 j3_p
        cpnTucRaiAssJ.setJ3P(CommonDtoUtil.strValToInt(subInfoJ.getJ3P()));
        // 問題頻度_その他_誤嚥 j3_q
        cpnTucRaiAssJ.setJ3Q(CommonDtoUtil.strValToInt(subInfoJ.getJ3Q()));
        // 問題頻度_その他_発熱 j3_r
        cpnTucRaiAssJ.setJ3R(CommonDtoUtil.strValToInt(subInfoJ.getJ3R()));
        // 問題頻度_その他_出血 j3_s
        cpnTucRaiAssJ.setJ3S(CommonDtoUtil.strValToInt(subInfoJ.getJ3S()));
        // 問題頻度_その他_不衛生 j3_t
        cpnTucRaiAssJ.setJ3T(CommonDtoUtil.strValToInt(subInfoJ.getJ3T()));
        // 問題頻度_その他_末梢浮腫 j3_u
        cpnTucRaiAssJ.setJ3U(CommonDtoUtil.strValToInt(subInfoJ.getJ3U()));
        // 呼吸困難 j4
        cpnTucRaiAssJ.setJ4(CommonDtoUtil.strValToInt(subInfoJ.getJ4()));
        // 疲労感 j5
        cpnTucRaiAssJ.setJ5(CommonDtoUtil.strValToInt(subInfoJ.getJ5()));
        // 痛み_頻度 j6_a
        cpnTucRaiAssJ.setJ6A(CommonDtoUtil.strValToInt(subInfoJ.getJ6A()));
        // 痛み_程度 j6_b
        cpnTucRaiAssJ.setJ6B(CommonDtoUtil.strValToInt(subInfoJ.getJ6B()));
        // 痛み_持続性 j6_c
        cpnTucRaiAssJ.setJ6C(CommonDtoUtil.strValToInt(subInfoJ.getJ6C()));
        // 痛み_突発痛 j6_d
        cpnTucRaiAssJ.setJ6D(CommonDtoUtil.strValToInt(subInfoJ.getJ6D()));
        // 痛み_コントロール j6_e
        cpnTucRaiAssJ.setJ6E(CommonDtoUtil.strValToInt(subInfoJ.getJ6E()));
        // 状態の不安定性_症状 j7_a
        cpnTucRaiAssJ.setJ7A(CommonDtoUtil.strValToInt(subInfoJ.getJ7A()));
        // 状態の不安定性_発性 j7_b
        cpnTucRaiAssJ.setJ7B(CommonDtoUtil.strValToInt(subInfoJ.getJ7B()));
        // 状態の不安定性_末期の疾患 j7_c
        cpnTucRaiAssJ.setJ7C(CommonDtoUtil.strValToInt(subInfoJ.getJ7C()));
        // 主観的健康感 j8
        cpnTucRaiAssJ.setJ8(CommonDtoUtil.strValToInt(subInfoJ.getJ8()));
        // 喫煙と飲酒_喫煙 j9_a
        cpnTucRaiAssJ.setJ9A(CommonDtoUtil.strValToInt(subInfoJ.getJ9A()));
        // 喫煙と飲酒_飲酒 j9_b
        cpnTucRaiAssJ.setJ9B(CommonDtoUtil.strValToInt(subInfoJ.getJ9B()));
        // j1_メモ j1_memo_knj
        cpnTucRaiAssJ.setJ1MemoKnj(subInfoJ.getJ1MemoKnj());
        // j1_メモフォント j1_memo_font
        cpnTucRaiAssJ.setJ1MemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ1MemoFont()));
        // j1_メモ色 j1_memo_color
        cpnTucRaiAssJ.setJ1MemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ1MemoColor()));
        // j2_メモ j2_memo_knj
        cpnTucRaiAssJ.setJ2MemoKnj(subInfoJ.getJ2MemoKnj());
        // j2_メモフォント j2_memo_font
        cpnTucRaiAssJ.setJ2MemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ2MemoFont()));
        // j2_メモ色 j2_memo_color
        cpnTucRaiAssJ.setJ2MemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ2MemoColor()));
        // j3_メモ j3_memo_knj
        cpnTucRaiAssJ.setJ3MemoKnj(subInfoJ.getJ3MemoKnj());
        // j3_メモフォント j3_memo_font
        cpnTucRaiAssJ.setJ3MemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ3MemoFont()));
        // j3_メモ色 j3_memo_color
        cpnTucRaiAssJ.setJ3MemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ3MemoColor()));
        // j4_メモ j4_memo_knj
        cpnTucRaiAssJ.setJ4MemoKnj(subInfoJ.getJ4MemoKnj());
        // j4_メモフォント j4_memo_font
        cpnTucRaiAssJ.setJ4MemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ4MemoFont()));
        // j4_メモ色 j4_memo_color
        cpnTucRaiAssJ.setJ4MemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ4MemoColor()));
        // j5_メモ j5_memo_knj
        cpnTucRaiAssJ.setJ5MemoKnj(subInfoJ.getJ5MemoKnj());
        // j5_メモフォント j5_memo_font
        cpnTucRaiAssJ.setJ5MemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ5MemoFont()));
        // j5_メモ色 j5_memo_color
        cpnTucRaiAssJ.setJ5MemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ5MemoColor()));
        // j6_a_メモ j6_a_memo_knj
        cpnTucRaiAssJ.setJ6AMemoKnj(subInfoJ.getJ6AMemoKnj());
        // j6_a_メモフォント j6_a_memo_font
        cpnTucRaiAssJ.setJ6AMemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ6AMemoFont()));
        // j6_a_メモ色 j6_a_memo_color
        cpnTucRaiAssJ.setJ6AMemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ6AMemoColor()));
        // j6_b_メモ j6_b_memo_knj
        cpnTucRaiAssJ.setJ6BMemoKnj(subInfoJ.getJ6BMemoKnj());
        // j6_b_メモフォント j6_b_memo_font
        cpnTucRaiAssJ.setJ6BMemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ6BMemoFont()));
        // j6_b_メモ色 j6_b_memo_color
        cpnTucRaiAssJ.setJ6BMemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ6BMemoColor()));
        // j6_c_メモ j6_c_memo_knj
        cpnTucRaiAssJ.setJ6CMemoKnj(subInfoJ.getJ6CMemoKnj());
        // j6_c_メモフォント j6_c_memo_font
        cpnTucRaiAssJ.setJ6CMemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ6CMemoFont()));
        // j6_c_メモ色 j6_c_memo_color
        cpnTucRaiAssJ.setJ6CMemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ6CMemoColor()));
        // j6_d_メモ j6_d_memo_knj
        cpnTucRaiAssJ.setJ6DMemoKnj(subInfoJ.getJ6DMemoKnj());
        // j6_d_メモフォント j6_d_memo_font
        cpnTucRaiAssJ.setJ6DMemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ6DMemoFont()));
        // j6_d_メモ色 j6_d_memo_color
        cpnTucRaiAssJ.setJ6DMemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ6DMemoColor()));
        // j6_e_メモ j6_e_memo_knj
        cpnTucRaiAssJ.setJ6EMemoKnj(subInfoJ.getJ6EMemoKnj());
        // j6_e_メモフォント j6_e_memo_font
        cpnTucRaiAssJ.setJ6EMemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ6EMemoFont()));
        // j6_e_メモ色 j6_e_memo_color
        cpnTucRaiAssJ.setJ6EMemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ6EMemoColor()));
        // j7_メモ j7_memo_knj
        cpnTucRaiAssJ.setJ7MemoKnj(subInfoJ.getJ7MemoKnj());
        // j7_メモフォント j7_memo_font
        cpnTucRaiAssJ.setJ7MemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ7MemoFont()));
        // j7_メモ色 j7_memo_color
        cpnTucRaiAssJ.setJ7MemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ7MemoColor()));
        // j8_メモ j8_memo_knj
        cpnTucRaiAssJ.setJ8MemoKnj(subInfoJ.getJ8MemoKnj());
        // j8_メモフォント j8_memo_font
        cpnTucRaiAssJ.setJ8MemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ8MemoFont()));
        // j8_メモ色 j8_memo_color
        cpnTucRaiAssJ.setJ8MemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ8MemoColor()));
        // j9_a_メモ j9_a_memo_knj
        cpnTucRaiAssJ.setJ9AMemoKnj(subInfoJ.getJ9AMemoKnj());
        // j9_a_メモフォント j9_a_memo_font
        cpnTucRaiAssJ.setJ9AMemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ9AMemoFont()));
        // j9_a_メモ色 j9_a_memo_color
        cpnTucRaiAssJ.setJ9AMemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ9AMemoColor()));
        // j9_b_メモ j9_b_memo_knj
        cpnTucRaiAssJ.setJ9BMemoKnj(subInfoJ.getJ9BMemoKnj());
        // j9_b_メモフォント j9_b_memo_font
        cpnTucRaiAssJ.setJ9BMemoFont(CommonDtoUtil.strValToInt(subInfoJ.getJ9BMemoFont()));
        // j9_b_メモ色 j9_b_memo_color
        cpnTucRaiAssJ.setJ9BMemoColor(CommonDtoUtil.strValToInt(subInfoJ.getJ9BMemoColor()));

        // DAOを実行
        this.cpnTucRaiAssJMapper.insertSelective(cpnTucRaiAssJ);
    }

    /**
     * サブK情報登録
     * 関数名：insertRrkSubInfoK
     *
     * @param @param   raiId アセスメントID
     * @param subInfoK サブK情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoK(String raiId, Gui00775SubInfoK subInfoK) {
        CpnTucRaiAssK cpnTucRaiAssK = new CpnTucRaiAssK();
        // アセスメントID
        cpnTucRaiAssK.setRaiId(CommonDtoUtil.strValToInt(raiId));
        // 身長（cm）
        BigDecimal K1A = new BigDecimal(subInfoK.getK1A());
        cpnTucRaiAssK.setK1A(K1A);

        // 体重（kg）
        BigDecimal K1B = new BigDecimal(subInfoK.getK1B());
        cpnTucRaiAssK.setK1B(K1B);

        // 栄養上の問題_体重減少
        cpnTucRaiAssK.setK2A(CommonDtoUtil.strValToInt(subInfoK.getK2A()));

        // 栄養上の問題_脱水
        cpnTucRaiAssK.setK2B(CommonDtoUtil.strValToInt(subInfoK.getK2B()));

        // 栄養上の問題_水分摂取
        cpnTucRaiAssK.setK2C(CommonDtoUtil.strValToInt(subInfoK.getK2C()));

        // 栄養上の問題_水分排泄量
        cpnTucRaiAssK.setK2D(CommonDtoUtil.strValToInt(subInfoK.getK2D()));

        // 栄養摂取の方法
        cpnTucRaiAssK.setK3(CommonDtoUtil.strValToInt(subInfoK.getK3()));

        // 経静脈／経管栄養摂取量
        cpnTucRaiAssK.setK4(CommonDtoUtil.strValToInt(subInfoK.getK4()));

        // 歯科口腔_義歯
        cpnTucRaiAssK.setK5A(CommonDtoUtil.strValToInt(subInfoK.getK5A()));

        // 歯科口腔_正常でない
        cpnTucRaiAssK.setK5B(CommonDtoUtil.strValToInt(subInfoK.getK5B()));

        // 歯科口腔_痛み/不快感
        cpnTucRaiAssK.setK5C(CommonDtoUtil.strValToInt(subInfoK.getK5C()));

        // 歯科口腔_口の渇き
        cpnTucRaiAssK.setK5D(CommonDtoUtil.strValToInt(subInfoK.getK5D()));

        // 歯科口腔_咀嚼困難
        cpnTucRaiAssK.setK5E(CommonDtoUtil.strValToInt(subInfoK.getK5E()));

        // 歯科口腔_歯肉
        cpnTucRaiAssK.setK5F(CommonDtoUtil.strValToInt(subInfoK.getK5F()));

        // 栄養管理_食物形態の加工
        cpnTucRaiAssK.setK6A(CommonDtoUtil.strValToInt(subInfoK.getK6A()));

        // 栄養管理_低塩分
        cpnTucRaiAssK.setK6B(CommonDtoUtil.strValToInt(subInfoK.getK6B()));

        // 栄養管理_カロリー制限
        cpnTucRaiAssK.setK6C(CommonDtoUtil.strValToInt(subInfoK.getK6C()));

        // 栄養管理_低脂肪
        cpnTucRaiAssK.setK6D(CommonDtoUtil.strValToInt(subInfoK.getK6D()));

        // 栄養管理_その他
        cpnTucRaiAssK.setK6E(CommonDtoUtil.strValToInt(subInfoK.getK6E()));

        // k1_メモ
        cpnTucRaiAssK.setK1MemoKnj(subInfoK.getK1MemoKnj());

        // k1_メモフォント
        cpnTucRaiAssK.setK1MemoFont(CommonDtoUtil.strValToInt(subInfoK.getK1MemoFont()));

        // k1_メモ色
        cpnTucRaiAssK.setK1MemoColor(CommonDtoUtil.strValToInt(subInfoK.getK1MemoColor()));

        // k2_メモ
        cpnTucRaiAssK.setK2MemoKnj(subInfoK.getK2MemoKnj());

        // k2_メモフォント
        cpnTucRaiAssK.setK2MemoFont(CommonDtoUtil.strValToInt(subInfoK.getK2MemoFont()));

        // k2_メモ色
        cpnTucRaiAssK.setK2MemoColor(CommonDtoUtil.strValToInt(subInfoK.getK2MemoColor()));

        // k3_メモ
        cpnTucRaiAssK.setK3MemoKnj(subInfoK.getK3MemoKnj());

        // k3_メモフォント
        cpnTucRaiAssK.setK3MemoFont(CommonDtoUtil.strValToInt(subInfoK.getK3MemoFont()));

        // k3_メモ色
        cpnTucRaiAssK.setK3MemoColor(CommonDtoUtil.strValToInt(subInfoK.getK3MemoColor()));

        // k4_メモ
        cpnTucRaiAssK.setK4MemoKnj(subInfoK.getK4MemoKnj());

        // k4_メモフォント
        cpnTucRaiAssK.setK4MemoFont(CommonDtoUtil.strValToInt(subInfoK.getK4MemoFont()));

        // k4_メモ色
        cpnTucRaiAssK.setK4MemoColor(CommonDtoUtil.strValToInt(subInfoK.getK4MemoColor()));

        // k5_メモ
        cpnTucRaiAssK.setK5MemoKnj(subInfoK.getK5MemoKnj());

        // k5_メモフォント
        cpnTucRaiAssK.setK5MemoFont(CommonDtoUtil.strValToInt(subInfoK.getK5MemoFont()));

        // k5_メモ色
        cpnTucRaiAssK.setK5MemoColor(CommonDtoUtil.strValToInt(subInfoK.getK5MemoColor()));

        // k6_メモ
        cpnTucRaiAssK.setK6MemoKnj(subInfoK.getK6MemoKnj());

        // k6_メモフォント
        cpnTucRaiAssK.setK6MemoFont(CommonDtoUtil.strValToInt(subInfoK.getK6MemoFont()));

        // k6_メモ色
        cpnTucRaiAssK.setK6MemoColor(CommonDtoUtil.strValToInt(subInfoK.getK6MemoColor()));

        cpnTucRaiAssKMapper.insertSelective(cpnTucRaiAssK);
    }

    /**
     * サブL情報登録
     * 関数名：insertRrkSubInfoL
     *
     * @param @param   raiId アセスメントID
     * @param subInfoL サブL情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoL(String raiId, GUI00776LjyouhoOutDto subInfoL) {
        // TODO
    }

    /**
     * サブM情報登録
     * 関数名：insertRrkSubInfoM
     *
     * @param @param   raiId アセスメントID
     * @param subInfoM サブM情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoM(String raiId, Gui00777SubMInDto subInfoM) {
        // TODO
    }

    /**
     * サブN情報登録
     * 関数名：insertRrkSubInfoN
     *
     * @param @param   raiId アセスメントID
     * @param subInfoN サブN情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoN(String raiId, Gui00778SubInfoNInDto subInfoN) {
        // TODO
    }

    /**
     * サブO情報登録
     * 関数名：insertRrkSubInfoO
     *
     * @param @param   raiId アセスメントID
     * @param subInfoO サブO情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoO(String raiId, GUI00779SubInfoO subInfoO) {
        CpnTucRaiAssO cpnTucRaiAssO = new CpnTucRaiAssO();
        // 健診予防接種_血圧
        cpnTucRaiAssO.setO1A(CommonDtoUtil.strValToInt(subInfoO.getO1A()));
        // 健診予防接種_大腸内視鏡
        cpnTucRaiAssO.setO1B(CommonDtoUtil.strValToInt(subInfoO.getO1B()));
        // 健診予防接種_歯科
        cpnTucRaiAssO.setO1C(CommonDtoUtil.strValToInt(subInfoO.getO1C()));
        // 健診予防接種_眼科
        cpnTucRaiAssO.setO1D(CommonDtoUtil.strValToInt(subInfoO.getO1D()));
        // 健診予防接種_聴力
        cpnTucRaiAssO.setO1E(CommonDtoUtil.strValToInt(subInfoO.getO1E()));
        // 健診予防接種_インフルエンザ
        cpnTucRaiAssO.setO1F(CommonDtoUtil.strValToInt(subInfoO.getO1F()));
        // 健診予防接種_マンモグラフィー
        cpnTucRaiAssO.setO1G(CommonDtoUtil.strValToInt(subInfoO.getO1G()));
        // 健診予防接種_肺炎
        cpnTucRaiAssO.setO1H(CommonDtoUtil.strValToInt(subInfoO.getO1H()));
        // 治療ケア_治療_抗がん剤
        cpnTucRaiAssO.setO2A(CommonDtoUtil.strValToInt(subInfoO.getO2A()));
        // 治療ケア_治療_透析
        cpnTucRaiAssO.setO2B(CommonDtoUtil.strValToInt(subInfoO.getO2B()));
        // 治療ケア_治療_感染
        cpnTucRaiAssO.setO2C(CommonDtoUtil.strValToInt(subInfoO.getO2C()));
        // 治療ケア_治療_薬物投与
        cpnTucRaiAssO.setO2D(CommonDtoUtil.strValToInt(subInfoO.getO2D()));
        // 治療ケア_治療_酸素
        cpnTucRaiAssO.setO2E(CommonDtoUtil.strValToInt(subInfoO.getO2E()));
        // 治療ケア_治療_放射線
        cpnTucRaiAssO.setO2F(CommonDtoUtil.strValToInt(subInfoO.getO2F()));
        // 治療ケア_治療_吸引
        cpnTucRaiAssO.setO2G(CommonDtoUtil.strValToInt(subInfoO.getO2G()));
        // 治療ケア_治療_気管切開口
        cpnTucRaiAssO.setO2H(CommonDtoUtil.strValToInt(subInfoO.getO2H()));
        // 治療ケア_治療_輸血
        cpnTucRaiAssO.setO2I(CommonDtoUtil.strValToInt(subInfoO.getO2I()));
        // 治療ケア_治療_人工呼吸器
        cpnTucRaiAssO.setO2J(CommonDtoUtil.strValToInt(subInfoO.getO2J()));
        // 治療ケア_治療_創のケア
        cpnTucRaiAssO.setO2K(CommonDtoUtil.strValToInt(subInfoO.getO2K()));
        // 治療ケア_プログラム_トイレ
        cpnTucRaiAssO.setO2L(CommonDtoUtil.strValToInt(subInfoO.getO2L()));
        // 治療ケア_プログラム_緩和
        cpnTucRaiAssO.setO2M(CommonDtoUtil.strValToInt(subInfoO.getO2M()));
        // 治療ケア_プログラム_体位
        cpnTucRaiAssO.setO2N(CommonDtoUtil.strValToInt(subInfoO.getO2N()));
        // 訪問介護_実施日数(A)
        cpnTucRaiAssO.setO3AA(CommonDtoUtil.strValToInt(subInfoO.getO3AA()));
        // 訪問介護_分数(B)
        cpnTucRaiAssO.setO3AB(CommonDtoUtil.strValToInt(subInfoO.getO3AB()));
        // 訪問看護_実施日数(A)
        cpnTucRaiAssO.setO3BA(CommonDtoUtil.strValToInt(subInfoO.getO3BA()));
        // 訪問看護_分数(B)
        cpnTucRaiAssO.setO3BB(CommonDtoUtil.strValToInt(subInfoO.getO3BB()));
        // 通所介護リハ_実施日数(A)
        cpnTucRaiAssO.setO3CA(CommonDtoUtil.strValToInt(subInfoO.getO3CA()));
        // 食事/配食_実施日数(A)
        cpnTucRaiAssO.setO3DA(CommonDtoUtil.strValToInt(subInfoO.getO3DA()));
        // リハ_理学_計画日数(A)
        cpnTucRaiAssO.setO4AA(CommonDtoUtil.strValToInt(subInfoO.getO4AA()));
        // リハ_理学_実施日数(B)
        cpnTucRaiAssO.setO4AB(CommonDtoUtil.strValToInt(subInfoO.getO4AB()));
        // リハ_理学_分数(C)
        cpnTucRaiAssO.setO4AC(CommonDtoUtil.strValToInt(subInfoO.getO4AC()));
        // リハ_作業_計画日数(A)
        cpnTucRaiAssO.setO4BA(CommonDtoUtil.strValToInt(subInfoO.getO4BA()));
        // リハ_作業_実施日数(B)
        cpnTucRaiAssO.setO4BB(CommonDtoUtil.strValToInt(subInfoO.getO4BB()));
        // リハ_作業_分数(C)
        cpnTucRaiAssO.setO4BC(CommonDtoUtil.strValToInt(subInfoO.getO4BC()));
        // リハ_言語_計画日数(A)
        cpnTucRaiAssO.setO4CA(CommonDtoUtil.strValToInt(subInfoO.getO4CA()));
        // リハ_言語_実施日数(B)
        cpnTucRaiAssO.setO4CB(CommonDtoUtil.strValToInt(subInfoO.getO4CB()));
        // リハ_言語_分数(C)
        cpnTucRaiAssO.setO4CC(CommonDtoUtil.strValToInt(subInfoO.getO4CC()));
        // リハ_心理_計画日数(A)
        cpnTucRaiAssO.setO4DA(CommonDtoUtil.strValToInt(subInfoO.getO4DA()));
        // リハ_心理_実施日数(B)
        cpnTucRaiAssO.setO4DB(CommonDtoUtil.strValToInt(subInfoO.getO4DB()));
        // リハ_心理_分数(C)
        cpnTucRaiAssO.setO4DC(CommonDtoUtil.strValToInt(subInfoO.getO4DC()));
        // リハ_呼吸_計画日数(A)
        cpnTucRaiAssO.setO4EA(CommonDtoUtil.strValToInt(subInfoO.getO4EA()));
        // リハ_呼吸_実施日数(B)
        cpnTucRaiAssO.setO4EB(CommonDtoUtil.strValToInt(subInfoO.getO4EB()));
        // リハ_呼吸_分数(C)
        cpnTucRaiAssO.setO4EC(CommonDtoUtil.strValToInt(subInfoO.getO4EC()));
        // リハ_訓練_計画日数(A)
        cpnTucRaiAssO.setO4FA(CommonDtoUtil.strValToInt(subInfoO.getO4FA()));
        // リハ_訓練_実施日数(B)
        cpnTucRaiAssO.setO4FB(CommonDtoUtil.strValToInt(subInfoO.getO4FB()));
        // リハ_訓練_分数(C)
        cpnTucRaiAssO.setO4FC(CommonDtoUtil.strValToInt(subInfoO.getO4FC()));
        // 入院
        cpnTucRaiAssO.setO5A(CommonDtoUtil.strValToInt(subInfoO.getO5A()));
        // 救急外来
        cpnTucRaiAssO.setO5B(CommonDtoUtil.strValToInt(subInfoO.getO5B()));
        // 医師の診察
        cpnTucRaiAssO.setO5C(CommonDtoUtil.strValToInt(subInfoO.getO5C()));
        // 受診
        cpnTucRaiAssO.setO6(CommonDtoUtil.strValToInt(subInfoO.getO6()));
        // 医師の指示変更
        cpnTucRaiAssO.setO7(CommonDtoUtil.strValToInt(subInfoO.getO7()));
        // 身体抑制
        cpnTucRaiAssO.setO8A(CommonDtoUtil.strValToInt(subInfoO.getO8A()));
        // 身体抑制_ベッド柵
        cpnTucRaiAssO.setO8B(CommonDtoUtil.strValToInt(subInfoO.getO8B()));
        // 身体抑制_体幹部
        cpnTucRaiAssO.setO8C(CommonDtoUtil.strValToInt(subInfoO.getO8C()));
        // 身体抑制_立ち上がり
        cpnTucRaiAssO.setO8D(CommonDtoUtil.strValToInt(subInfoO.getO8D()));
        // o1_メモ
        cpnTucRaiAssO.setO1MemoKnj(subInfoO.getO1MemoKnj());
        // o1_メモフォント
        cpnTucRaiAssO.setO1MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO1MemoFont()));
        // o1_メモ色
        cpnTucRaiAssO.setO1MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO1MemoColor()));
        // o2_メモ
        cpnTucRaiAssO.setO2MemoKnj(subInfoO.getO2MemoKnj());
        // o2_メモフォント
        cpnTucRaiAssO.setO2MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO2MemoFont()));
        // o2_メモ色
        cpnTucRaiAssO.setO2MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO2MemoColor()));
        // o3_メモ
        cpnTucRaiAssO.setO3MemoKnj(subInfoO.getO3MemoKnj());
        // o3_メモフォント
        cpnTucRaiAssO.setO3MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO3MemoFont()));
        // o3_メモ色
        cpnTucRaiAssO.setO3MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO3MemoColor()));
        // o4_メモ
        cpnTucRaiAssO.setO4MemoKnj(subInfoO.getO4MemoKnj());
        // o4_メモフォント
        cpnTucRaiAssO.setO4MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO4MemoFont()));
        // o4_メモ色
        cpnTucRaiAssO.setO4MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO4MemoColor()));
        // o5_メモ
        cpnTucRaiAssO.setO5MemoKnj(subInfoO.getO5MemoKnj());
        // o5_メモフォント
        cpnTucRaiAssO.setO5MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO5MemoFont()));
        // o5_メモ色
        cpnTucRaiAssO.setO5MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO5MemoColor()));
        // o6_メモ
        cpnTucRaiAssO.setO6MemoKnj(subInfoO.getO6MemoKnj());
        // o6_メモフォント
        cpnTucRaiAssO.setO6MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO6MemoFont()));
        // o6_メモ色
        cpnTucRaiAssO.setO6MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO6MemoColor()));
        // o7_メモ
        cpnTucRaiAssO.setO7MemoKnj(subInfoO.getO7MemoKnj());
        // o7_メモフォント
        cpnTucRaiAssO.setO7MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO7MemoFont()));
        // o7_メモ色
        cpnTucRaiAssO.setO7MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO7MemoColor()));
        // o8_メモ
        cpnTucRaiAssO.setO8MemoKnj(subInfoO.getO8MemoKnj());
        // o8_メモフォント
        cpnTucRaiAssO.setO8MemoFont(CommonDtoUtil.strValToInt(subInfoO.getO8MemoFont()));
        // o8_メモ色
        cpnTucRaiAssO.setO8MemoColor(CommonDtoUtil.strValToInt(subInfoO.getO8MemoColor()));

        // アセスメントID
        cpnTucRaiAssO.setRaiId(CommonDtoUtil.strValToInt(raiId));

        cpnTucRaiAssOMapper.insertSelective(cpnTucRaiAssO);
    }

    /**
     * サブP情報登録
     * 関数名：insertRrkSubInfoP
     *
     * @param @param   raiId アセスメントID
     * @param subInfoP サブP情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoP(String raiId, Gui00780UpdateSubInfoP subInfoP) {
        CpnTucRaiAssP cpnTucRaiAssP = new CpnTucRaiAssP();
        // アセスメントID
        cpnTucRaiAssP.setRaiId(CommonDtoUtil.strValToInt(raiId));
        // 意思決定権_法定後見人等
        cpnTucRaiAssP.setP1A(CommonDtoUtil.strValToInt(subInfoP.getP1A()));
        // 意思決定権_任意後見
        cpnTucRaiAssP.setP1B(CommonDtoUtil.strValToInt(subInfoP.getP1B()));
        // 意思決定権_家族等の代理
        cpnTucRaiAssP.setP1C(CommonDtoUtil.strValToInt(subInfoP.getP1C()));
        // 事前指示_蘇生術
        cpnTucRaiAssP.setP2A(CommonDtoUtil.strValToInt(subInfoP.getP2A()));
        // 事前指示_挿管
        cpnTucRaiAssP.setP2B(CommonDtoUtil.strValToInt(subInfoP.getP2B()));
        // 事前指示_入院
        cpnTucRaiAssP.setP2C(CommonDtoUtil.strValToInt(subInfoP.getP2C()));
        // 事前指示_経管栄養
        cpnTucRaiAssP.setP2D(CommonDtoUtil.strValToInt(subInfoP.getP2D()));
        // 事前指示_薬剤制限
        cpnTucRaiAssP.setP2E(CommonDtoUtil.strValToInt(subInfoP.getP2E()));
        // p1_メモ
        cpnTucRaiAssP.setP1MemoKnj(subInfoP.getP1MemoKnj());
        // p1_メモフォント
        cpnTucRaiAssP.setP1MemoFont(CommonDtoUtil.strValToInt(subInfoP.getP1MemoFont()));
        // p1_メモ色
        cpnTucRaiAssP.setP1MemoColor(CommonDtoUtil.strValToInt(subInfoP.getP1MemoColor()));
        // p2_メモ
        cpnTucRaiAssP.setP2MemoKnj(subInfoP.getP2MemoKnj());
        // p2_メモフォント
        cpnTucRaiAssP.setP2MemoFont(CommonDtoUtil.strValToInt(subInfoP.getP2MemoFont()));
        // p2_メモ色
        cpnTucRaiAssP.setP2MemoColor(CommonDtoUtil.strValToInt(subInfoP.getP2MemoColor()));

        cpnTucRaiAssPMapper.insertSelective(cpnTucRaiAssP);
    }

    /**
     * サブQ情報登録
     * 関数名：insertRrkSubInfoQ
     *
     * @param @param   raiId アセスメントID
     * @param subInfoQ サブQ情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoQ(String raiId, GUI00781UpdateSubInfoQ subInfoQ) {
        CpnTucRaiAssQ cpnTucRaiAssQ = new CpnTucRaiAssQ();
        // アセスメントID
        cpnTucRaiAssQ.setRaiId(CommonDtoUtil.strValToInt(raiId));
        // インフォーマル_関係(主)
        cpnTucRaiAssQ.setQ1A1(CommonDtoUtil.strValToInt(subInfoQ.getQ1A1()));
        // インフォーマル_関係(副)
        cpnTucRaiAssQ.setQ1A2(CommonDtoUtil.strValToInt(subInfoQ.getQ1A2()));
        // インフォーマル_同居(主)
        cpnTucRaiAssQ.setQ1B1(CommonDtoUtil.strValToInt(subInfoQ.getQ1B1()));
        // インフォーマル_同居(副)
        cpnTucRaiAssQ.setQ1B2(CommonDtoUtil.strValToInt(subInfoQ.getQ1B2()));
        // インフォーマル_IADL(主)
        cpnTucRaiAssQ.setQ1C1(CommonDtoUtil.strValToInt(subInfoQ.getQ1C1()));
        // インフォーマル_IADL(副)
        cpnTucRaiAssQ.setQ1C2(CommonDtoUtil.strValToInt(subInfoQ.getQ1C2()));
        // インフォーマル_ADL(主)
        cpnTucRaiAssQ.setQ1D1(CommonDtoUtil.strValToInt(subInfoQ.getQ1D1()));
        // インフォーマル_ADL(副)
        cpnTucRaiAssQ.setQ1D2(CommonDtoUtil.strValToInt(subInfoQ.getQ1D2()));
        // インフォーマル状況_ケア
        cpnTucRaiAssQ.setQ2A(CommonDtoUtil.strValToInt(subInfoQ.getQ2A()));
        // インフォーマル状況_苦悩
        cpnTucRaiAssQ.setQ2B(CommonDtoUtil.strValToInt(subInfoQ.getQ2B()));
        // インフォーマル状況_憔悴
        cpnTucRaiAssQ.setQ2C(CommonDtoUtil.strValToInt(subInfoQ.getQ2C()));
        // インフォーマルな援助量
        cpnTucRaiAssQ.setQ3(CommonDtoUtil.strValToInt(subInfoQ.getQ3()));
        // q1a_メモ
        cpnTucRaiAssQ.setQ1AMemoKnj(subInfoQ.getQ1AMemoKnj());
        // q1a_メモフォント
        cpnTucRaiAssQ.setQ1AMemoFont(CommonDtoUtil.strValToInt(subInfoQ.getQ1AMemoFont()));
        // q1a_メモ色
        cpnTucRaiAssQ.setQ1AMemoColor(CommonDtoUtil.strValToInt(subInfoQ.getQ1AMemoColor()));
        // q1b_メモ
        cpnTucRaiAssQ.setQ1BMemoKnj(subInfoQ.getQ1BMemoKnj());
        // q1b_メモフォント
        cpnTucRaiAssQ.setQ1BMemoFont(CommonDtoUtil.strValToInt(subInfoQ.getQ1BMemoFont()));
        // q1b_メモ色
        cpnTucRaiAssQ.setQ1BMemoColor(CommonDtoUtil.strValToInt(subInfoQ.getQ1BMemoColor()));
        // q1cd_メモ
        cpnTucRaiAssQ.setQ1CdMemoKnj(subInfoQ.getQ1CdMemoKnj());
        // q1cd_メモフォント
        cpnTucRaiAssQ.setQ1CdMemoFont(CommonDtoUtil.strValToInt(subInfoQ.getQ1CdMemoFont()));
        // q1cd_メモ色
        cpnTucRaiAssQ.setQ1CdMemoColor(CommonDtoUtil.strValToInt(subInfoQ.getQ1CdMemoColor()));
        // q2_メモ
        cpnTucRaiAssQ.setQ2MemoKnj(subInfoQ.getQ2MemoKnj());
        // q2_メモフォント
        cpnTucRaiAssQ.setQ2MemoFont(CommonDtoUtil.strValToInt(subInfoQ.getQ2MemoFont()));
        // q2_メモ色
        cpnTucRaiAssQ.setQ2MemoColor(CommonDtoUtil.strValToInt(subInfoQ.getQ2MemoColor()));
        // q3_メモ
        cpnTucRaiAssQ.setQ3MemoKnj(subInfoQ.getQ3MemoKnj());
        // q3_メモフォント
        cpnTucRaiAssQ.setQ3MemoFont(CommonDtoUtil.strValToInt(subInfoQ.getQ3MemoFont()));
        // q3_メモ色
        cpnTucRaiAssQ.setQ3MemoColor(CommonDtoUtil.strValToInt(subInfoQ.getQ3MemoColor()));

        cpnTucRaiAssQMapper.insertSelective(cpnTucRaiAssQ);
    }

    /**
     * サブR情報登録
     * 関数名：insertRrkSubInfoR
     *
     * @param @param   raiId アセスメントID
     * @param subInfoR サブR情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoR(String raiId, GUI00782SubInfoR subInfoR) {
        CpnTucRaiAssR cpnTucRaiAssR = new CpnTucRaiAssR();
        // アセスメントID
        cpnTucRaiAssR.setRaiId(CommonDtoUtil.strValToInt(raiId));
        // 退所の可能性_利用者
        cpnTucRaiAssR.setR1A(CommonDtoUtil.strValToInt(subInfoR.getR1A()));
        // 退所の可能性_支援者
        cpnTucRaiAssR.setR1B(CommonDtoUtil.strValToInt(subInfoR.getR1B()));
        // 退所の可能性_家
        cpnTucRaiAssR.setR1C(CommonDtoUtil.strValToInt(subInfoR.getR1C()));
        // 退所の予測期間
        cpnTucRaiAssR.setR2(CommonDtoUtil.strValToInt(subInfoR.getR2()));
        // r1_メモ
        cpnTucRaiAssR.setR1MemoKnj(subInfoR.getR1MemoKnj());
        // r1_メモフォント
        cpnTucRaiAssR.setR1MemoFont(CommonDtoUtil.strValToInt(subInfoR.getR1MemoFont()));
        // r1_メモ色
        cpnTucRaiAssR.setR1MemoColor(CommonDtoUtil.strValToInt(subInfoR.getR1MemoColor()));
        // r2_メモ
        cpnTucRaiAssR.setR2MemoKnj(subInfoR.getR2MemoKnj());
        // r2_メモフォント
        cpnTucRaiAssR.setR2MemoFont(CommonDtoUtil.strValToInt(subInfoR.getR2MemoFont()));
        // r2_メモ色
        cpnTucRaiAssR.setR2MemoColor(CommonDtoUtil.strValToInt(subInfoR.getR2MemoColor()));

        cpnTucRaiAssRMapper.insertSelective(cpnTucRaiAssR);
    }

    /**
     * サブS情報登録
     * 関数名：insertRrkSubInfoS
     *
     * @param @param   raiId アセスメントID
     * @param subInfoS サブS情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoS(String raiId, GUI00783SubInfoS subInfoS) {
        CpnTucRaiAssS cpnTucRaiAssS = new CpnTucRaiAssS();
        // アセスメントID
        cpnTucRaiAssS.setRaiId(CommonDtoUtil.strValToInt(raiId));

        cpnTucRaiAssS.setS1A(CommonDtoUtil.strValToInt(subInfoS.getS1A()));

        cpnTucRaiAssS.setS1B(CommonDtoUtil.strValToInt(subInfoS.getS1B()));

        cpnTucRaiAssS.setS1C(CommonDtoUtil.strValToInt(subInfoS.getS1C()));

        cpnTucRaiAssS.setS1D(CommonDtoUtil.strValToInt(subInfoS.getS1D()));

        cpnTucRaiAssS.setS1E(CommonDtoUtil.strValToInt(subInfoS.getS1E()));

        cpnTucRaiAssS.setS2(CommonDtoUtil.strValToInt(subInfoS.getS2()));

        cpnTucRaiAssS.setS3A(CommonDtoUtil.strValToInt(subInfoS.getS3A()));

        cpnTucRaiAssS.setS3B(CommonDtoUtil.strValToInt(subInfoS.getS3B()));

        cpnTucRaiAssS.setS3C(CommonDtoUtil.strValToInt(subInfoS.getS3C()));

        cpnTucRaiAssS.setS4(CommonDtoUtil.strValToInt(subInfoS.getS4()));
        // S1_メモ
        cpnTucRaiAssS.setS1MemoKnj(subInfoS.getS1MemoKnj());
        // S1_メモフォント
        cpnTucRaiAssS.setS1MemoFont(CommonDtoUtil.strValToInt(subInfoS.getS1MemoFont()));
        // S1_メモ色
        cpnTucRaiAssS.setS1MemoColor(CommonDtoUtil.strValToInt(subInfoS.getS1MemoColor()));
        // S2_メモ
        cpnTucRaiAssS.setS2MemoKnj(subInfoS.getS2MemoKnj());
        // S2_メモフォント
        cpnTucRaiAssS.setS2MemoFont(CommonDtoUtil.strValToInt(subInfoS.getS2MemoFont()));
        // S2_メモ色
        cpnTucRaiAssS.setS2MemoColor(CommonDtoUtil.strValToInt(subInfoS.getS2MemoColor()));
        // S3_メモ
        cpnTucRaiAssS.setS3MemoKnj(subInfoS.getS3MemoKnj());
        // S3_メモフォント
        cpnTucRaiAssS.setS3MemoFont(CommonDtoUtil.strValToInt(subInfoS.getS3MemoFont()));
        // S3_メモ色
        cpnTucRaiAssS.setS3MemoColor(CommonDtoUtil.strValToInt(subInfoS.getS3MemoColor()));
        // s4_メモ
        cpnTucRaiAssS.setS4MemoKnj(subInfoS.getS4MemoKnj());
        // s4_メモフォント
        cpnTucRaiAssS.setS4MemoFont(CommonDtoUtil.strValToInt(subInfoS.getS4MemoFont()));
        // s4_メモ色
        cpnTucRaiAssS.setS4MemoColor(CommonDtoUtil.strValToInt(subInfoS.getS4MemoColor()));

        cpnTucRaiAssSMapper.insertSelective(cpnTucRaiAssS);
    }

    /**
     * サブT情報登録
     * 関数名：insertRrkSubInfoT
     *
     * @param @param   raiId アセスメントID
     * @param subInfoT サブT情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoT(String raiId, Gui00784SubInfoTInDto subInfoT) {
        // TODO
    }

    /**
     * サブU情報登録
     * 関数名：insertRrkSubInfoU
     *
     * @param @param   raiId アセスメントID
     * @param subInfoU サブU情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoU(String raiId, Gui00785SubUUpdateInDto subInfoU) {
        // TODO
    }

    /**
     * サブV情報登録
     * 関数名：insertRrkSubInfoV
     *
     * @param @param   raiId アセスメントID
     * @param subInfoV サブV情報
     * @return 結果outDto
     */
    private void insertRrkSubInfoV(String raiId, Gui00786SubInfoVUpdateOutDto subInfoV) {
        // TODO
    }
}
