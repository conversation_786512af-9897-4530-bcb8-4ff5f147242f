// package jp.ndsoft.carebase.cmn.api.service;

// import java.lang.invoke.MethodHandles;

// import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
// import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01221UpdateMonitoringTitle;
// import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01221DivisionNoForCompareBackup;
// import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01221DuplicateRowForBackup;

// import java.util.List;
// import java.util.Optional;
// import java.util.stream.Collectors;

// import jp.ndsoft.carebase.cmn.api.service.dto.MonitoringTitleMasterUpdateServiceInDto;
// import jp.ndsoft.carebase.cmn.api.service.dto.MonitoringTitleMasterUpdateServiceOutDto;
// import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
// import jp.ndsoft.carebase.cmn.api.util.CommonDaoUtil;
// import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;

// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;

// import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkFree1Mapper;
// import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkFree2Mapper;
// import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkFreeSncTaishouMapper;
// import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkFreeTekiyoMapper;
// import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkFree1;
// import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkFree1Criteria;
// import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkFree2;
// import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkFree2Criteria;
// import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkFreeSncTaishouCriteria;
// import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkFreeTekiyo;
// import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkFreeTekiyoCriteria;
// import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkFreeSncTaishou;
// import jp.ndsoft.carebase.common.dao.mysql.entity.DsKghKrkMocFreeTekiyoByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.DsKghKrkMocFreeTekiyoOutEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMocFree2NoFltByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMocFree2NoFltOutEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMocFreeSncTaishouByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkMocFreeSncTaishouOutEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucFreeassKra1ByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucFreeassKra1OutEntity;
// import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFree2SelectMapper;
// import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFreeSncTaishouSelectMapper;
// import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkFreeTekiyoSelectMapper;
// import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucFreeassKra1SelectMapper;
// import jp.ndsoft.carebase.common.util.CareBaseMComSeqMgrCodeConstants;
// import jp.ndsoft.smh.framework.common.Constants;
// import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
// import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
// import jp.ndsoft.smh.framework.services.util.Numbering;
// import jp.ndsoft.smh.framework.util.AppUtil;

// /**
//  * @since 2025.05.22
//  * <AUTHOR>
//  * @implNote GUI01221_モニタリングタイトルマスタ の保存処理
//  */
// @Service
// public class MonitoringTitleMasterUpdateServiceImpl extends
//         UpdateServiceImpl<MonitoringTitleMasterUpdateServiceInDto, MonitoringTitleMasterUpdateServiceOutDto> {
//     /** ロガー. */
//     private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

//     /** 32-07 課題立案ヘッダ取得のDAO */
//     @Autowired
//     private KghTucFreeassKra1SelectMapper kghTucFreeassKra1SelectMapper;

//     /** カスタマイズ帳票マスタヘッダのDAO */
//     @Autowired
//     private KghMocKrkFree1Mapper kghMocKrkFree1Mapper;

//     /** カスタマイズ帳票適用マスタ取得のDAO */
//     @Autowired
//     private KghMocKrkFreeTekiyoSelectMapper kghMocKrkFreeTekiyoSelectMapper;

//     /** カスタマイズ帳票適用マスタのDAO */
//     @Autowired
//     private KghMocKrkFreeTekiyoMapper kghMocKrkFreeTekiyoMapper;

//     /** カスタマイズ帳票進捗管理対象マスタの取得DAO */
//     @Autowired
//     private KghMocKrkFreeSncTaishouSelectMapper kghMocKrkFreeSncTaishouSelectMapper;

//     /** カスタマイズ帳票進捗管理対象マスタのDAO */
//     @Autowired
//     private KghMocKrkFreeSncTaishouMapper kghMocKrkFreeSncTaishouMapper;

//     /** カスタマイズ帳票マスタデータの取得DAO */
//     @Autowired
//     private KghMocKrkFree2SelectMapper kghMocKrkFree2SelectMapper;

//     /** カスタマイズ帳票マスタデータのDAO */
//     @Autowired
//     private KghMocKrkFree2Mapper kghMocKrkFree2Mapper;

//     @Autowired
//     private Numbering numbering;

//     /**
//      * GUI01221_モニタリングタイトルマスタ の保存処理
//      *
//      *
//      * @param inDto GUI01221_モニタリングタイトルマスタ の保存処理入力DTO.
//      * @return GUI01221_モニタリングタイトルマスタ の保存処理出力DTO
//      */
//     @Override
//     protected MonitoringTitleMasterUpdateServiceOutDto mainProcess(
//             final MonitoringTitleMasterUpdateServiceInDto inDto) throws Exception {
//         LOG.info(Constants.START);
//         MonitoringTitleMasterUpdateServiceOutDto outDto = new MonitoringTitleMasterUpdateServiceOutDto();
//         // 更新前チェックフラグ
//         String beforeUpdateFlag = CommonConstants.BEFORE_UPDATE_FLAG_OFF;

//         /*
//          * ===============1.単項目チェック以外の入力チェック===============
//          *
//          */
//         // 特になし
//         /*
//          * ===============2. バリデーションチェックを行う。===============
//          *
//          */
//         // なし

//         /*
//          * ===============3. 更新前チェックを行う===============
//          *
//          */
//         //
//         // 3.1.リクエストパラメータ.モニタリングタイトルリストの件数＞0件の場合、リクエストパラメータ.モニタリングタイトルリストを繰り返して、更新前のチェックを実施する。
//         // モニタリングタイトルリスト
//         List<Gui01221UpdateMonitoringTitle> monitoringTitleList = inDto.getMonitoringTitleList();
//         if (!CollectionUtils.isNullOrEmpty(monitoringTitleList)) {
//             // 【繰り返し開始】
//             for (Gui01221UpdateMonitoringTitle gui01221MonitoringTitle : monitoringTitleList) {
//                 // 3.1.1. モニタリングタイトルリスト.分割数（上）更新状態=1の場合
//                 if (CommonConstants.COLUMN_COUNT_STATUS_ON.equals(gui01221MonitoringTitle.getColumnCountStatus())) {

//                     // 3.1.1.1. 下記の32-07 課題立案ヘッダ取得のDAOを利用し、マスタヘッダIDに対応するデータが存在するかチェックを行う
//                     // 課題立案様式ID モニタリングタイトル.マスタヘッダID
//                     KghTucFreeassKra1ByCriteriaInEntity kghTucFreeassKra1ByCriteriaInEntity = new KghTucFreeassKra1ByCriteriaInEntity();
//                     kghTucFreeassKra1ByCriteriaInEntity
//                             .setIlTitleId(CommonDtoUtil.strValToInt(gui01221MonitoringTitle.getFree1Id()));
//                     KghTucFreeassKra1OutEntity countKghTucFreeassKra1ByCriteria = kghTucFreeassKra1SelectMapper

//                             .countKghTucFreeassKra1ByCriteria(kghTucFreeassKra1ByCriteriaInEntity);
//                     // 3.1.1.2. 「3.1.1.1.」で取得する件数>0の場合
//                     if (countKghTucFreeassKra1ByCriteria.getCNT() > CommonConstants.NUMBER_ZERO) {
//                         // 更新前チェックフラグ=1
//                         beforeUpdateFlag = CommonConstants.BEFORE_UPDATE_FLAG_ON;
//                         outDto.setBeforeUpdateFlag(beforeUpdateFlag);
//                         // 次の処理を中止する。
//                         return outDto;
//                     }

//                 }

//             }
//             // 【繰り返し終了】
//         }

//         /*
//          * ===============4. モニタリングタイトル一覧情報の保存処理=============
//          *
//          */
//         // 4. リクエストパラメータ.モニタリングタイトルリストの件数＞0件の場合、モニタリングタイトル一覧情報の保存処理を実施する
//         // 4.1. リクエストパラメータ.モニタリングタイトルリストを繰り返して、下記処理を行う
//         // 【繰り返し開始】
//         if (!CollectionUtils.isNullOrEmpty(monitoringTitleList)) {
//             for (Gui01221UpdateMonitoringTitle gui01221MonitoringTitle : monitoringTitleList) {
//                 KghMocKrkFree1 kghMocKrkFree1 = new KghMocKrkFree1();
//                 // 4.1.1 モニタリングタイトルリスト[ループ回数].更新区分=Cの場合、27-01 カスタマイズ帳票マスタヘッダの情報を登録する
//                 if (CommonDtoUtil.isCreate(gui01221MonitoringTitle)) {
//                     // 機能区分 1
//                     kghMocKrkFree1.setKinouKbn(CommonConstants.KINOUKBN_KINOU1);

//                     // 様式区分 リクエストパラメータ.様式区分

//                     kghMocKrkFree1.setYoushikiKbn(CommonDtoUtil.strValToInt(inDto.getYoushikiKbn()));

//                     // 帳票タイトル
//                     kghMocKrkFree1.setTitleKnj(gui01221MonitoringTitle.getTitleKnj());

//                     // 行の分割数（上）

//                     kghMocKrkFree1.setColumnCount(CommonDtoUtil.strValToInt(gui01221MonitoringTitle.getColumnCount()));

//                     // 行の分割数（下）
//                     kghMocKrkFree1

//                             .setColumnCount2(CommonDtoUtil.strValToInt(gui01221MonitoringTitle.getColumnCount2()));

//                     // 表示順

//                     kghMocKrkFree1.setSort(CommonDtoUtil.strValToInt(gui01221MonitoringTitle.getSort()));

//                     // 印刷文字サイズ

//                     kghMocKrkFree1.setFontSize(CommonDtoUtil.strValToInt(gui01221MonitoringTitle.getFontSize()));

//                     // 用紙サイズ

//                     kghMocKrkFree1.setYoushiSize(CommonDtoUtil.strValToInt(gui01221MonitoringTitle.getYoushiSize()));

//                     // 固定様式区分

//                     kghMocKrkFree1.setKoteiKbn(CommonDtoUtil.strValToInt(gui01221MonitoringTitle.getKoteiKbn()));

//                     // 採番基盤利用

//                     kghMocKrkFree1.setFree1Id(numbering.getNumber(AppUtil.getKeiyakushaId(),

//                             CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.KGH_MOC_KRK_FREE1_FREE1_ID
//                                     .getIntValue())
//                             .intValue());

//                     // 使用中止フラグ 0「※ setInsertCommonColumns ＝＞setDelFlg ＝0」
//                     // 登録時の共通カラム値設定処理
//                     CommonDaoUtil.setInsertCommonColumns(kghMocKrkFree1);
//                     kghMocKrkFree1Mapper.insertSelective(kghMocKrkFree1);
//                     gui01221MonitoringTitle.setFree1Id(
//                             CommonDtoUtil.objValToString(kghMocKrkFree1.getFree1Id()));

//                 } else if (CommonDtoUtil.isUpdate(gui01221MonitoringTitle)) {
//                     // 4.1.2 モニタリングタイトルリスト[ループ回数].更新区分=Uの場合、27-01 カスタマイズ帳票マスタヘッダの情報を更新する
//                     // title_knj 帳票タイトル
//                     kghMocKrkFree1.setTitleKnj(gui01221MonitoringTitle.getTitleKnj());
//                     // column_count 行の分割数（上）
//                     kghMocKrkFree1

//                             .setColumnCount(CommonDtoUtil.strValToInt(gui01221MonitoringTitle.getColumnCount()));
//                     // column_count2 行の分割数（下）
//                     kghMocKrkFree1.setColumnCount2(

//                             CommonDtoUtil.strValToInt(gui01221MonitoringTitle.getColumnCount2()));
//                     // sort 表示順

//                     kghMocKrkFree1.setSort(CommonDtoUtil.strValToInt(gui01221MonitoringTitle.getSort()));

//                     // font_size 印刷文字サイズ

//                     kghMocKrkFree1.setFontSize(CommonDtoUtil.strValToInt(gui01221MonitoringTitle.getFontSize()));

//                     // 更新条件
//                     KghMocKrkFree1Criteria kghMocKrkFree1Criteria = new KghMocKrkFree1Criteria();
//                     // スタヘッダID＝リクエストパラメータ.モニタリングタイトルリスト[ループ回数].マスタヘッダID
//                     // 削除フラグ = 0（未削除データ）
//                     // 更新回数 = リクエストパラメータ.モニタリングタイトルリスト[ループ回数].更新回数
//                     kghMocKrkFree1Criteria.createCriteria()

//                             .andFree1IdEqualTo(CommonDtoUtil.strValToInt(gui01221MonitoringTitle.getFree1Id()));
//                     int updateCnt = kghMocKrkFree1Mapper.updateByCriteriaSelective(kghMocKrkFree1,
//                             kghMocKrkFree1Criteria);
//                     if (updateCnt <= 0) {
//                         throw new ExclusiveException();
//                     }

//                 } else if (CommonDtoUtil.isDelete(gui01221MonitoringTitle)) {
//                     // 4.1.3 モニタリングタイトルリスト[ループ回数].更新区分=Dの場合、27-01
//                     // カスタマイズ帳票マスタヘッダの情報を削除更新する
//                     // 別紙「DB更新詳細」の「14.」 を参照する
//                     // 更新条件
//                     KghMocKrkFree1Criteria kghMocKrkFree1Criteria = new KghMocKrkFree1Criteria();
//                     // マスタヘッダID＝リクエストパラメータ.モニタリングタイトルリスト[ループ回数].マスタヘッダID
//                     // 削除フラグ = 0（未削除データ）
//                     // 更新回数 = リクエストパラメータ.モニタリングタイトルリスト[ループ回数].更新回数
//                     kghMocKrkFree1Criteria.createCriteria()
//                             .andFree1IdEqualTo(CommonDtoUtil.strValToInt(gui01221MonitoringTitle.getFree1Id()));

//                     int updateCnt = kghMocKrkFree1Mapper.updateByCriteriaSelective(kghMocKrkFree1,
//                             kghMocKrkFree1Criteria);
//                     if (updateCnt <= 0) {
//                         throw new ExclusiveException();
//                     }

//                 }

//             }

//         }

//         /*
//          * ===============5.
//          * // 更新時に適用フラグの更新を行う「kgh_moc_krk_tekiyoテーブルへの登録処理」=============
//          *
//          */
//         // 5.1. 下記の27-18 カスタマイズ帳票適用マスタ取得のDAOを利用し、カスタマイズ帳票適用マスタ情報を取得する。
//         List<DsKghKrkMocFreeTekiyoOutEntity> dsKghKrkMocFreeTekiyoByCriteriaList = kghMocKrkFreeTekiyoSelectMapper
//                 .findDsKghKrkMocFreeTekiyoByCriteria(new DsKghKrkMocFreeTekiyoByCriteriaInEntity());
//         // 5.2. 「リクエストパラメータ.モニタリングタイトルリスト.適用フラグ=1」のデータが存在する場合、
//         Optional<Gui01221UpdateMonitoringTitle> gui01221MonitoringTitleOptional = monitoringTitleList.stream()
//                 .filter(element -> CommonConstants.DMY_TEKIYOU_FLG_ON.equals(element.getDmyTekiyouFlg())).findFirst();

//         if (gui01221MonitoringTitleOptional.isPresent()) {
//             //
//             // 5.2.1.変数.適用マスタヘッダIDを設定する[※変数.適用マスタヘッダID=該当レコードモニタリングタイトル.マスタヘッダID]
//             // ※リクエストパラメータ.モニタリングタイトルリストに、適用フラグ値が"1"の対象レコードのマスタヘッダIDを利用する
//             String free1Id = gui01221MonitoringTitleOptional.get().getFree1Id();
//             // 下記条件で特定できた【「5.1.」で取得した「カスタマイズ帳票適用マスタ情報」】を 「変数.帳票適用マスタ情報」に設定する
//             // カスタマイズ帳票適用マスタ情報.事業所ID=リクエストパラメータ.事業所ID、
//             // カスタマイズ帳票適用マスタ情報.機能区分=1
//             // カスタマイズ帳票適用マスタ情報.様式区分=リクエストパラメータ.様式区分
//             Optional<DsKghKrkMocFreeTekiyoOutEntity> gsKghKrkMocFreeTekiyoOptional = dsKghKrkMocFreeTekiyoByCriteriaList
//                     .stream().filter(element -> element.getSvJigyoId() == CommonDtoUtil
//                             .strValToInt(inDto.getSvJigyoId())
//                             && CommonConstants.KINOUKBN_KINOU1 == element.getKinouKbn()
//                             && CommonDtoUtil.strValToShort(inDto.getYoushikiKbn()) == element
//                                     .getYoushikiKbn())
//                     .findFirst();
//             // 5.2.2. 「5.1.」で取得するカスタマイズ帳票適用マスタ情報が0件数ではないの場合、更新処理を実施する
//             if (gsKghKrkMocFreeTekiyoOptional.isPresent()) {
//                 DsKghKrkMocFreeTekiyoOutEntity gsKghKrkMocFreeTekiyo = gsKghKrkMocFreeTekiyoOptional.get();
//                 // 変数.帳票適用マスタ情報.マスタヘッダID <>変数.適用マスタヘッダIDの場合
//                 if (CommonDtoUtil.strValToInt(free1Id) != gsKghKrkMocFreeTekiyo.getFree1Id()) {
//                     KghMocKrkFreeTekiyo kghMocKrkFreeTekiyo = new KghMocKrkFreeTekiyo();
//                     KghMocKrkFreeTekiyoCriteria kghMocKrkFreeTekiyoCriteria = new KghMocKrkFreeTekiyoCriteria();
//                     // 変数.帳票適用マスタ情報.マスタヘッダID は変数.適用マスタヘッダIDで設定して、変数.帳票適用マスタ情報を更新する。
//                     // 更新条件
//                     kghMocKrkFreeTekiyoCriteria.createCriteria()
//                             // 事業所ID＝変数.帳票適用マスタ情報.事業所ID
//                             .andSvJigyoIdEqualTo(gsKghKrkMocFreeTekiyo.getSvJigyoId())
//                             // 機能区分＝変数.帳票適用マスタ情報.機能区分
//                             .andKinouKbnEqualTo(gsKghKrkMocFreeTekiyo.getKinouKbn().intValue())
//                             // 様式区分＝変数.帳票適用マスタ情報.様式区分
//                             .andYoushikiKbnEqualTo(gsKghKrkMocFreeTekiyo.getYoushikiKbn().intValue());
//                     // マスタヘッダID 変数.適用マスタヘッダID

//                     kghMocKrkFreeTekiyo.setFree1Id(CommonDtoUtil.strValToInt(free1Id));
//                     // DAOを実行
//                     int updateCnt = kghMocKrkFreeTekiyoMapper.updateByCriteriaSelective(
//                             kghMocKrkFreeTekiyo,
//                             kghMocKrkFreeTekiyoCriteria);
//                     // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
//                     if (updateCnt <= 0) {
//                         throw new ExclusiveException();
//                     }
//                 }
//             } else {
//                 KghMocKrkFreeTekiyo kghMocKrkFreeTekiyo = new KghMocKrkFreeTekiyo();
//                 // 5.2.2.2 「5.2.2.1」以外の場合
//                 // 変数.適用マスタヘッダIDを利用して、帳票適用マスタ情報を登録する
//                 // 事業所ID = リクエストパラメータ.事業所ID

//                 kghMocKrkFreeTekiyo.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
//                 // 機能区分 = 1
//                 kghMocKrkFreeTekiyo.setKinouKbn(CommonConstants.KINOUKBN_KINOU1);
//                 // 様式区分 = リクエストパラメータ.様式区分

//                 kghMocKrkFreeTekiyo.setYoushikiKbn(CommonDtoUtil.strValToInt(inDto.getYoushikiKbn()));
//                 // マスタヘッダID = 変数.適用マスタヘッダID

//                 kghMocKrkFreeTekiyo.setFree1Id(CommonDtoUtil.strValToInt(free1Id));
//                 // 削除フラグ = 0
//                 // 登録時の共通カラム値設定処理
//                 CommonDaoUtil.setInsertCommonColumns(kghMocKrkFreeTekiyo);
//                 // DAOを実行
//                 kghMocKrkFreeTekiyoMapper.insertSelective(kghMocKrkFreeTekiyo);
//             }

//         }

//         // 5.3. リクエストパラメータ.総進捗管理対象フラグ=1の場合「※進捗管理対象の処理が必要な場合」
//         if (CommonConstants.ALL_SNS_FLG_ON.equals(inDto.getAllSncFlg())) {
//             // 5.3.1. 【27-30 カスタマイズ帳票進捗管理対象マスタ】の取得DAOを利用して、【27-30
//             // カスタマイズ帳票進捗管理対象マスタ】情報リストを取得する。「※更新回数取得用」
//             KghKrkMocFreeSncTaishouByCriteriaInEntity kghKrkMocFreeSncTaishouByCriteriaInEntity = new KghKrkMocFreeSncTaishouByCriteriaInEntity();
//             // 事業者ID = リクエストパラメータ.事業者ID

//             kghKrkMocFreeSncTaishouByCriteriaInEntity.setAiSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
//             List<KghKrkMocFreeSncTaishouOutEntity> KghKrkMocFreeSncTaishouOutEntityList = kghMocKrkFreeSncTaishouSelectMapper

//                     .findKghKrkMocFreeSncTaishouByCriteria(kghKrkMocFreeSncTaishouByCriteriaInEntity);
//             // 5.3.2. 「5.3.1. 」取得情報リストの件数＞0件の場合、情報リストを繰り返して、【27-30
//             if (!CollectionUtils.isNullOrEmpty(KghKrkMocFreeSncTaishouOutEntityList)) {
//                 // カスタマイズ帳票進捗管理対象マスタ】情報を削除更新する。 「※一度対象事業所のデータを全て削除する」
//                 // 【繰り返し開始】
//                 for (KghKrkMocFreeSncTaishouOutEntity kEntity : KghKrkMocFreeSncTaishouOutEntityList) {
//                     // 27-30 カスタマイズ帳票進捗管理対象マスタ(テーブル)
//                     KghMocKrkFreeSncTaishou kghMocKrkFreeSncTaishou = new KghMocKrkFreeSncTaishou();
//                     // 更新条件
//                     KghMocKrkFreeSncTaishouCriteria KghMocKrkFreeSncTaishouCriteria = new KghMocKrkFreeSncTaishouCriteria();
//                     KghMocKrkFreeSncTaishouCriteria.createCriteria()
//                             // 事業所ID＝「5.3.1. 」の情報リスト.事業所ID
//                             .andSvJigyoIdEqualTo(kEntity.getSvJigyoId());
//                     // DAOを実行
//                     int updateCnt = kghMocKrkFreeSncTaishouMapper.updateByCriteriaSelective(kghMocKrkFreeSncTaishou,
//                             KghMocKrkFreeSncTaishouCriteria);
//                     // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
//                     if (updateCnt <= 0) {
//                         throw new ExclusiveException();
//                     }
//                 }
//                 // 【繰り返し終了】
//             }
//             // 5.3.3. 「5.3.2.」の更新削除処理は成功する場合、進捗管理対象でチェックが付いてる物は全て追加する。
//             // 5.3.3.1. リクエストパラメータ.モニタリングタイトルリストが0件数ではないの場合
//             if (!CollectionUtils.isNullOrEmpty(monitoringTitleList)) {
//                 //
//                 // 5.3.3.1.1.リクエストパラメータ.モニタリングタイトルリストにて、モニタリングタイトル.進捗管理対象フラグが”1”のレコードを抽出し、登録用情報リストを作成する
//                 List<Gui01221UpdateMonitoringTitle> gMonitoringTitleList = monitoringTitleList
//                         .stream()
//                         .filter(element -> CommonConstants.DMY_SNS_FLG_ON.equals(element.getDmySncFlg()))
//                         .collect(Collectors.toList());
//                 // 登録用情報リストが０件以上の場合、リスト件数分を繰り返す、該当行のマスタヘッダIDを利用し、【27-30
//                 // カスタマイズ帳票進捗管理対象マスタ】情報を登録する。
//                 for (Gui01221UpdateMonitoringTitle monitoringEntity : gMonitoringTitleList) {
//                     // 27-30 カスタマイズ帳票進捗管理対象マスタ
//                     KghMocKrkFreeSncTaishou kghMocKrkFreeSncTaishou = new KghMocKrkFreeSncTaishou();
//                     // 事業所ID = リクエストパラメータ.事業所ID

//                     kghMocKrkFreeSncTaishou.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
//                     // 機能区分 = 1

//                     kghMocKrkFreeSncTaishou.setKinouKbn(CommonConstants.KINOUKBN_KINOU1);
//                     // 様式区分 リクエストパラメータ.様式区分

//                     kghMocKrkFreeSncTaishou.setYoushikiKbn(CommonDtoUtil.strValToInt(inDto.getYoushikiKbn()));
//                     // マスタヘッダID = [5.3.3.1.1]該当行のマスタヘッダID

//                     kghMocKrkFreeSncTaishou.setFree1Id(CommonDtoUtil.strValToInt(monitoringEntity.getFree1Id()));
//                     // 削除フラグ 0
//                     // 登録時の共通カラム値設定処理
//                     CommonDaoUtil.setInsertCommonColumns(kghMocKrkFreeSncTaishou);
//                     // DAOを実行

//                     kghMocKrkFreeSncTaishouMapper.insertSelective(kghMocKrkFreeSncTaishou);
//                 }
//             }
//         }

//         /*
//          * ===============6. 「5.」の登録処理が成功する
//          * 且つリクエストパラメータ.モニタリングタイトルリストの件数＞0件の場合、モニタリングタイトルリストを繰り返して、
//          * kgh_moc_krk_free2テーブルへの登録処理を実施する=============
//          *
//          */
//         if (!CollectionUtils.isNullOrEmpty(monitoringTitleList)) {
//             // 【繰り返し開始】
//             for (Gui01221UpdateMonitoringTitle monitoringTitle : monitoringTitleList) {
//                 // 6.1. 変数と初期値の設定
//                 // 6.1.1. 変数.設定可能最大文字数の設定
//                 // モニタリングタイトル.印刷文字サイズより変数.設定可能最大文字数の設定
//                 // モニタリングタイトルリスト[ループ回数].印刷文字サイズ
//                 // 変数.設定可能最大文字数
//                 Integer widthCntMax = CommonConstants.WIDTH_CNT_MAX_ZERO;
//                 if (CommonConstants.FONT_SIZE_9PT.equals(monitoringTitle.getFontSize())) {
//                     // 0：9pt 84
//                     widthCntMax = CommonConstants.NUMBER_EIGHTY_FOUR;
//                 } else if (CommonConstants.FONT_SIZE_10PT.equals(monitoringTitle.getFontSize())) {
//                     // 1：10pt 76
//                     widthCntMax = CommonConstants.NUMBER_SEVENTY_SIX;

//                 } else if (CommonConstants.FONT_SIZE_11PT.equals(monitoringTitle.getFontSize())) {
//                     // 2：11pt 70
//                     widthCntMax = CommonConstants.NUMBER_SEVENTY;

//                 } else if (CommonConstants.FONT_SIZE_12PT.equals(monitoringTitle.getFontSize())) {
//                     // 3：12pt 64
//                     widthCntMax = CommonConstants.NUMBER_SIXTY_FOUR;
//                 } else {
//                     // その以外 70
//                     widthCntMax = CommonConstants.NUMBER_SEVENTY;
//                 }

//                 // 6.1.2. 変数.分割数（上）＝リクエストパラメータ.モニタリングタイトルリスト[ループ回数].分割数（上）
//                 String columnCount = monitoringTitle.getColumnCount();
//                 // 6.1.3. 変数.分割数（下）＝リクエストパラメータ.モニタリングタイトルリスト[ループ回数].分割数（下）
//                 String columnCount2 = monitoringTitle.getColumnCount2();
//                 // 6.1.4. 変数.マスタヘッダID＝リクエストパラメータ.モニタリングタイトルリスト[ループ回数].マスタヘッダID
//                 String free1Id = monitoringTitle.getFree1Id();
//                 // 6.1.6. 変数.複写元マスタヘッダID＝リクエストパラメータ.モニタリングタイトルリスト[ループ回数].複写元マスタヘッダID
//                 String dmyFree1Id = monitoringTitle.getDmyFree1Id();
//                 // 6.1.6. 変数.複写用フラグ=false
//                 boolean copyFlg = Boolean.FALSE;
//                 // 6.1.7. 変数.総文字数=0
//                 int totalWidthCnt = CommonConstants.TOTAL_WIDTH_CNT_ZERO;
//                 // 6.1.8. 変数.初期値作成フラグ=リクエストパラメータ.モニタリングタイトルリスト[ループ回数].初期値作成フラグ
//                 String defUpdateFlg = monitoringTitle.getDefUpdateFlg();
//                 // 6.1.9.
//                 //
//                 // リクエストパラメータ.分割数比較用バックアップリストにて、分割数比較用バックアップ.マスタヘッダID＝変数.マスタヘッダIDのレコード対象として
//                 int compareColumnCount = CommonConstants.COMPARE_COLIMN_COUNT_ZERO;
//                 int compareColumnCount2 = CommonConstants.COMPARE_COLIMN_COUNT2_ZERO;
//                 // 一致するのレコード
//                 List<Gui01221DivisionNoForCompareBackup> divisionNoForCompareBackupData = inDto
//                         .getDivisionNoForCompareBackupList().stream()
//                         .filter(element -> element.getFree1Id().equals(free1Id)).collect(Collectors.toList());
//                 if (!CollectionUtils.isNullOrEmpty(divisionNoForCompareBackupData)) {
//                     // 変数.分割数比較用バックアップ分割数（上）=分割数比較用バックアップリスト[該当レコード].行の分割数（上）
//                     compareColumnCount = CommonDtoUtil

//                             .strValToInt(divisionNoForCompareBackupData.getFirst().getColumnCount());
//                     // 変数.分割数比較用バックアップ分割数（下）=分割数比較用バックアップリスト[該当レコード].行の分割数（下）
//                     compareColumnCount2 = CommonDtoUtil

//                             .strValToInt(divisionNoForCompareBackupData.getFirst().getColumnCount2());
//                 }

//                 // 6.2. 【カスタマイズ帳票マスタデータ】の取得DAOを利用して、【カスタマイズ帳票マスタデータ】情報リストを取得する。
//                 List<KghKrkMocFree2NoFltOutEntity> krkMocFree2NoFltOutEntitieList = kghMocKrkFree2SelectMapper
//                         .findKghKrkMocFree2NoFltByCriteria(new KghKrkMocFree2NoFltByCriteriaInEntity());
//                 // 6.3. 「変数.初期値作成フラグ=1」の場合、新規行の処理を実施する
//                 if (CommonConstants.STR_1.equals(defUpdateFlg)) {
//                     // 6.3.1. 「変数.複写元マスタヘッダID=0」の場合、下記行追加時の処理を行う
//                     if (CommonConstants.STR_0.equals(dmyFree1Id)) {
//                         // 6.3.1.1. 変数.ループ回数が1から15「上段の最大項目数」まで繰り返し、以下の処理を行う。「※文字数は個別に設定を行う」
//                         // 【繰り返し開始】
//                         // 変数.ループ回数
//                         for (int count = CommonConstants.NUMBER_ONE; count <= CommonConstants.NUMBER_FIFTY; count++) {
//                             final int countkaisu = count;
//                             // 下記条件で特定できた「カスタマイズ帳票マスタデータ情報」を「変数.帳票マスタデータ情報」に設定する
//                             // カスタマイズ帳票マスタデータ情報.マスタヘッダID=変数.マスタヘッダID、
//                             // カスタマイズ帳票マスタデータ情報.区分ID=1、
//                             // カスタマイズ帳票マスタデータ情報.項目ID=変数.ループ回数
//                             Optional<KghKrkMocFree2NoFltOutEntity> kghKrkMocFree2NoFltOptional = krkMocFree2NoFltOutEntitieList
//                                     .stream()
//                                     .filter(element -> element.getFree1Id() == CommonDtoUtil.strValToInt(free1Id)
//                                             && CommonDtoUtil.intValToShort(CommonConstants.NUMBER_ONE)  == element.getKbnId()
//                                             && countkaisu == element.getKoumokuId())
//                                     .findFirst();
//                             // 6.3.1.1.2 「変数.帳票マスタデータ情報」を特定できない場合（存在なし） AND 変数.ループ回数<=
//                             // 変数.分割数（上）の場合
//                             if (!kghKrkMocFree2NoFltOptional.isPresent() &&
//                                     count <= CommonDtoUtil.strValToInt(columnCount)) {
//                                 // 変数.文字数を計算する
//                                 // ①-1 「6.1.」変数設定により、変数.設定可能最大文字数を取得する。
//                                 int widthCnt = CommonConstants.NUMBER_ZERO;
//                                 // ①-2 以下の計算式の商と余りを取得し、変数.文字数計算の商と変数.文字数計算の余りに設定する。
//                                 // 計算式：変数.設定可能最大文字数 / 変数.分割数（上）
//                                 // 変数.文字数計算の余り >= 変数.ループ回数の場合
//                                 int widthResult = widthCntMax /
//                                         CommonDtoUtil.strValToInt(columnCount);
//                                 int widthRemainder = widthCntMax %
//                                         CommonDtoUtil.strValToInt(columnCount);
//                                 if (widthRemainder >= count) {
//                                     // 変数.文字数＝変数.文字数計算の商 + 1
//                                     widthCnt = widthResult + CommonConstants.NUMBER_ONE;
//                                 } else {
//                                     // 以外の場合 変数.文字数＝変数.文字数計算の商
//                                     widthCnt = widthResult;
//                                 }
//                                 // ②帳票マスタデータ情報を登録する
//                                 // 27-02 カスタマイズ帳票マスタデータ(対象テーブル)※別紙「DB更新詳細」の「5.」 を参照する
//                                 KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();
//                                 // マスタヘッダID = 変数.マスタヘッダID

//                                 kghMocKrkFree2.setFree1Id(CommonDtoUtil.strValToInt(free1Id));
//                                 // 区分ID = 1
//                                 kghMocKrkFree2.setKbnId(CommonConstants.NUMBER_ONE);
//                                 // 項目ID（1～15）= 変数.ループ回数
//                                 kghMocKrkFree2.setKoumokuId(count);
//                                 // 項目名 "項目" + 変数.ループ回数
//                                 kghMocKrkFree2.setNameKnj(CommonConstants.NAME_KNJ + count);
//                                 // 入力方法 = 1
//                                 kghMocKrkFree2.setInputKbn(CommonConstants.NUMBER_ONE);
//                                 // 連動区分 = 0
//                                 kghMocKrkFree2.setRendouKbn(CommonConstants.NUMBER_ZERO);
//                                 // 文字数 = 変数.文字数
//                                 kghMocKrkFree2.setWidthCnt(widthCnt);
//                                 // 自由に使用 -
//                                 // 削除フラグ 0
//                                 // 登録時の共通カラム値設定処理
//                                 CommonDaoUtil.setInsertCommonColumns(kghMocKrkFree2);
//                                 // DAOの実行
//                                 kghMocKrkFree2Mapper.insertSelective(kghMocKrkFree2);
//                             }
//                         }

//                         // 6.3.1.2. 変数.ループ回数が1から3「下段の最大項目数」まで繰り返し、以下の処理を行う。
//                         // 【繰り返し開始】
//                         for (int count = CommonConstants.NUMBER_ONE; count <= CommonConstants.NUMBER_3; count++) {
//                             final int countkaisu = count;
//                             // 6.3.1.2.1 「6.2. 」で取得したカスタマイズ帳票マスタデータ情報リストより
//                             // カスタマイズ帳票マスタデータ情報.マスタヘッダID=変数.マスタヘッダID、
//                             // カスタマイズ帳票マスタデータ情報.区分ID=2、
//                             // カスタマイズ帳票マスタデータ情報.項目ID=変数.ループ回数
//                             // を変数.帳票マスタデータ情報」に設定する
//                             Optional<KghKrkMocFree2NoFltOutEntity> kghKrkMocFree2NoFltOptional = krkMocFree2NoFltOutEntitieList
//                                     .stream()
//                                     .filter(element -> CommonDtoUtil.strValToInt(free1Id) == element.getFree1Id()
//                                             && CommonDtoUtil.intValToShort(CommonConstants.NUMBER_TWO) == element.getKbnId()
//                                             && countkaisu == element.getKoumokuId())
//                                     .findFirst();
//                             // 6.3.1.2.2 「変数.帳票マスタデータ情報」を特定できない場合（存在なし） AND 変数.ループ回数<=
//                             // 変数.分割数（下）の場合、下記処理を行う。
//                             if (!kghKrkMocFree2NoFltOptional.isPresent()
//                                     && count <= CommonDtoUtil.strValToInt(columnCount2)) {
//                                 // ① 変数.文字数を計算する
//                                 // ①-1 「6.1.」変数設定により、変数.設定可能最大文字数を取得する。
//                                 int widthCnt = CommonConstants.NUMBER_ZERO;
//                                 // ①-2 以下の計算式の商と余りを取得し、変数.文字数計算の商と変数.文字数計算の余りに設定する。
//                                 // 計算式：変数.設定可能最大文字数 / 変数.分割数（下）
//                                 int widthResult = widthCntMax /
//                                         CommonDtoUtil.strValToInt(columnCount2);
//                                 int widthRemainder = widthCntMax %
//                                         CommonDtoUtil.strValToInt(columnCount2);
//                                 // 変数.文字数計算の余り >= 変数.ループ回数の場合
//                                 if (widthRemainder >= count) {
//                                     // 変数.文字数＝変数.文字数計算の商 + 1
//                                     widthCnt = widthResult + CommonConstants.NUMBER_ONE;
//                                 } else {
//                                     // 以外の場合 変数.文字数＝変数.文字数計算の商
//                                     widthCnt = widthResult;
//                                 }
//                                 // ②帳票マスタデータ情報を登録する
//                                 // 27-02 カスタマイズ帳票マスタデータ(対象テーブル)※別紙「DB更新詳細」の「7.」 を参照する
//                                 KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();
//                                 // マスタヘッダID 変数.マスタヘッダID

//                                 kghMocKrkFree2.setFree1Id(CommonDtoUtil.strValToInt(free1Id));
//                                 // 区分ID = 2
//                                 kghMocKrkFree2.setKbnId(CommonConstants.NUMBER_TWO);
//                                 // 項目ID（1～15） = 変数.ループ回数
//                                 kghMocKrkFree2.setKoumokuId(count);
//                                 // 項目名 ="項目" + 変数.ループ回数
//                                 kghMocKrkFree2.setNameKnj(CommonConstants.NAME_KNJ + count);
//                                 // 入力方法 =1
//                                 kghMocKrkFree2.setInputKbn(CommonConstants.NUMBER_ONE);
//                                 // 連動区分= 0
//                                 kghMocKrkFree2.setRendouKbn(CommonConstants.NUMBER_ZERO);
//                                 // 文字数 変数.文字数
//                                 kghMocKrkFree2.setWidthCnt(widthCnt);
//                                 // 自由に使用 -
//                                 // 削除フラグ =0
//                                 // 登録時の共通カラム値設定処理
//                                 CommonDaoUtil.setInsertCommonColumns(kghMocKrkFree2);
//                                 // DAOの実行
//                                 kghMocKrkFree2Mapper.insertSelective(kghMocKrkFree2);
//                             }

//                         }
//                     } else {
//                         // 6.3.2 「6.3.1.」以外の場合「※「変数.複写元マスタヘッダID=0」以外の場合」、行複写時の処理を行う
//                         // 6.3.2.1. 変数.ループ回数が1から15「上段の最大項目数」まで繰り返し、以下の処理を行う。
//                         // 【繰り返し開始】
//                         for (int count = CommonConstants.NUMBER_ONE; count <= CommonConstants.NUMBER_FIFTY; count++) {
//                             // 6.3.2.1.1 「変数.帳票マスタデータ情報」と「変数.帳票マスタデータ情報バックアップ」を設定する
//                             //
//                             // ①リクエストパラメータ.行複写用バックアップリストより下記条件で特定できた「行複写用バックアップ情報」を「変数.帳票マスタデータ情報バックアップ」に設定する
//                             List<Gui01221DuplicateRowForBackup> duplicateRowForBackupList = inDto
//                                     .getDuplicateRowForBackupList();

//                             final int countkaisu = count;
//                             // 行複写用バックアップ情報.マスタヘッダID=変数.複写元マスタヘッダID、
//                             // 行複写用バックアップ情報.区分ID=１、
//                             // 行複写用バックアップ情報.項目ID=変数.ループ回数
//                             // を「変数.帳票マスタデータ情報バックアップ」に設定する
//                             Optional<Gui01221DuplicateRowForBackup> gui01221DuplicateRowForBackupOptional = duplicateRowForBackupList
//                                     .stream()
//                                     .filter(element -> dmyFree1Id == element.getFree1Id()
//                                             && CommonConstants.NUMBER_ONE == CommonDtoUtil
//                                                     .strValToInt(element.getKbnId())
//                                             && countkaisu == CommonDtoUtil.strValToInt(element.getKoumokuId()))
//                                     .findFirst();
//                             // ②「6.2. 」で取得したカスタマイズ帳票マスタデータ情報リストより
//                             // カスタマイズ帳票マスタデータ情報.マスタヘッダID=変数.マスタヘッダID、
//                             // カスタマイズ帳票マスタデータ情報.区分ID=1、
//                             // カスタマイズ帳票マスタデータ情報.項目ID=変数.ループ回数
//                             // を「変数.帳票マスタデータ情報」に設定する
//                             Optional<KghKrkMocFree2NoFltOutEntity> kghKrkMocFree2NoFltOptional = krkMocFree2NoFltOutEntitieList
//                                     .stream()
//                                     .filter(element -> CommonDtoUtil.strValToInt(free1Id) == element.getFree1Id()
//                                             && CommonDtoUtil.intValToShort(CommonConstants.NUMBER_ONE) == element.getKbnId()
//                                             && countkaisu == element.getKoumokuId())
//                                     .findFirst();

//                             // 6.3.2.1.2 「変数.帳票マスタデータ情報」を特定できない場合（存在なし）、かつ 変数.ループ回数<=
//                             // 変数.分割数（上）の場合、下記処理を行う。
//                             if (!kghKrkMocFree2NoFltOptional.isPresent()
//                                     && count <= CommonDtoUtil.strValToInt(columnCount)) {
//                                 //
//                                 // ①「変数.帳票マスタデータ情報バックアップ」情報は取得る場合「複写元にデータがある場合」、帳票マスタデータ情報の登録用データを作成する
//                                 String nameKnj;
//                                 int inputKbn;
//                                 int rendouKbn;
//                                 int widthCnt;
//                                 if (gui01221DuplicateRowForBackupOptional.isPresent()) {
//                                     // ①-1 変数と初期値を設定する。
//                                     // 変数.項目名＝変数.帳票マスタデータ情報バックアップ.項目名
//                                     nameKnj = gui01221DuplicateRowForBackupOptional.get().getNameKnj();
//                                     // 変数.入力方法＝変数.帳票マスタデータ情報バックアップ.入力方法
//                                     inputKbn = CommonDtoUtil

//                                             .strValToInt(gui01221DuplicateRowForBackupOptional.get().getInputKbn());
//                                     // 変数.連動区分＝変数.帳票マスタデータ情報バックアップ.連動区分
//                                     rendouKbn = CommonDtoUtil

//                                             .strValToInt(gui01221DuplicateRowForBackupOptional.get().getRendouKbn());
//                                     // 変数.文字数＝変数.帳票マスタデータ情報バックアップ.文字数
//                                     widthCnt = CommonDtoUtil

//                                             .strValToInt(gui01221DuplicateRowForBackupOptional.get().getWidthCnt());
//                                     // 「6.1.」変数設定により、変数.設定可能最大文字数を取得する。
//                                     // ①-2 以下の計算式で変数.文字数を設定する。
//                                     // ⅰ. 変数.複写用フラグ=falseの場合
//                                     if (!copyFlg) {
//                                         // ⅰ-1. 変数.ループ= 変数.分割数（上）の場合
//                                         if (count == CommonDtoUtil.strValToInt(columnCount)) {
//                                             // 変数.文字数＝変数.設定可能最大文字数 - 変数.総文字数
//                                             widthCnt = widthCntMax - totalWidthCnt;
//                                         } else {
//                                             // ⅰ-1. 「ⅰ-1.」以外の場合
//                                             // 変数.総文字数=変数.文字数 + 変数.総文字数
//                                             totalWidthCnt = widthCnt + totalWidthCnt;
//                                             // ⅰ-1-1. 変数.設定可能最大文字数 <= 変数.総文字数の場合
//                                             if (widthCntMax <= totalWidthCnt) {
//                                                 // 変数.文字数=変数.文字数 - (変数.総文字数 - 変数.設定可能最大文字数)
//                                                 widthCnt = widthCnt - (totalWidthCnt - widthCntMax);
//                                                 // 変数.複写用フラグ=true
//                                                 copyFlg = Boolean.TRUE;
//                                             }
//                                         }
//                                     } else {
//                                         // Ⅱ. 変数.複写用フラグ=trueの場合
//                                         // 変数.文字数＝0
//                                         widthCnt = CommonConstants.NUMBER_ZERO;
//                                     }

//                                 } else {
//                                     // ②
//                                     // 「①」以外の場合「変数.帳票マスタデータ情報バックアップ情報はない場合「複写元にデータがない場合」」、帳票マスタデータ情報の登録用データを作成する

//                                     // ②-1 変数と初期値を設定する。
//                                     // 変数.項目名＝"項目" + 変数.ループ回数
//                                     nameKnj = CommonConstants.NAME_KNJ + count;
//                                     // 変数.入力方法＝1
//                                     inputKbn = CommonConstants.NUMBER_ONE;
//                                     // 変数.連動区分＝0
//                                     rendouKbn = CommonConstants.NUMBER_ZERO;
//                                     // 変数.文字数＝0
//                                     widthCnt = CommonConstants.NUMBER_ZERO;
//                                     // 「6.1.」変数設定により、変数.設定可能最大文字数を取得する。
//                                     // ②-2 以下の計算式で変数.文字数を設定する。
//                                     // ⅰ. 変数.複写用フラグ=falseの場合
//                                     if (!copyFlg && count == CommonDtoUtil.strValToInt(columnCount)) {
//                                         // ⅰ-1. 変数.ループ回数= 変数.分割数（上）の場合
//                                         // 変数.文字数＝変数.設定可能最大文字数 - 変数.総文字数
//                                         widthCnt = widthCntMax - totalWidthCnt;
//                                     }
//                                 }
//                                 // ③帳票マスタデータ情報を登録する

//                                 // 27-02 カスタマイズ帳票マスタデータ(対象テーブル)
//                                 // 別紙「DB更新詳細」の「9.」 を参照する
//                                 KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();
//                                 // マスタヘッダID = 変数.マスタヘッダID

//                                 kghMocKrkFree2.setFree1Id(CommonDtoUtil.strValToInt(free1Id));
//                                 // 区分ID = 1
//                                 kghMocKrkFree2.setKbnId(CommonConstants.NUMBER_ONE);
//                                 // 項目ID（1～15）= 変数.ループ回数
//                                 kghMocKrkFree2.setKoumokuId(count);
//                                 // 項目名 = 変数.項目名
//                                 kghMocKrkFree2.setNameKnj(nameKnj);
//                                 // 入力方法 = 変数.入力方法
//                                 kghMocKrkFree2.setInputKbn(inputKbn);
//                                 // 連動区分 = 変数.連動区分
//                                 kghMocKrkFree2.setRendouKbn(rendouKbn);
//                                 // 文字数 = 変数.文字数
//                                 kghMocKrkFree2.setWidthCnt(widthCnt);
//                                 // 自由に使用 -
//                                 // 削除フラグ = 0
//                                 // 登録時の共通カラム値設定処理
//                                 CommonDaoUtil.setInsertCommonColumns(kghMocKrkFree2);
//                                 // DAOの実行
//                                 kghMocKrkFree2Mapper.insertSelective(kghMocKrkFree2);

//                             }

//                         }
//                         // 6.3.2.2. 変数の初期化処理を行う
//                         // ①. 変数.複写用フラグ=false
//                         copyFlg = Boolean.FALSE;
//                         // ②. 変数.総文字数=0
//                         totalWidthCnt = CommonConstants.NUMBER_ZERO;
//                         // 6.3.2.3. 変数.ループ回数が1から3「下段の最大項目数」まで繰り返し、以下の処理を行う。
//                         // 【繰り返し開始】
//                         for (int count = CommonConstants.NUMBER_ONE; count <= CommonConstants.NUMBER_3; count++) {
//                             // 6.3.2.3.1 「変数.帳票マスタデータ情報」と「変数.帳票マスタデータ情報バックアップ」を設定する
//                             //
//                             // ①リクエストパラメータ.行複写用バックアップリストより下記条件で特定できた「行複写用バックアップ情報」を「変数.帳票マスタデータ情報バックアップ」に設定する
//                             List<Gui01221DuplicateRowForBackup> duplicateRowForBackupList = inDto
//                                     .getDuplicateRowForBackupList();
//                             // 行複写用バックアップ情報.マスタヘッダID=変数.複写元マスタヘッダID、
//                             // 行複写用バックアップ情報.区分ID=2、
//                             // 行複写用バックアップ情報.項目ID=変数.ループ回数
//                             // を「変数.帳票マスタデータ情報バックアップ」に設定する
//                             final int countkaisu = count;

//                             Optional<Gui01221DuplicateRowForBackup> gui01221DuplicateRowForBackupOptional = duplicateRowForBackupList
//                                     .stream()
//                                     .filter(element -> dmyFree1Id == element.getFree1Id()
//                                             && CommonConstants.NUMBER_TWO == CommonDtoUtil
//                                                     .strValToInt(element.getKbnId())
//                                             && countkaisu == CommonDtoUtil.strValToInt(element.getKoumokuId()))
//                                     .findFirst();

//                             // ②「6.2. 」で取得したカスタマイズ帳票マスタデータ情報リストより
//                             // カスタマイズ帳票マスタデータ情報.マスタヘッダID=変数.マスタヘッダID、
//                             // カスタマイズ帳票マスタデータ情報.区分ID=2、
//                             // カスタマイズ帳票マスタデータ情報.項目ID=変数.ループ回数
//                             // 特定できた「カスタマイズ帳票マスタデータ情報」を「変数.帳票マスタデータ情報」に設定する

//                             Optional<KghKrkMocFree2NoFltOutEntity> kghKrkMocFree2NoFltOptional = krkMocFree2NoFltOutEntitieList
//                                     .stream()
//                                     .filter(element -> CommonDtoUtil.strValToInt(free1Id) == element.getFree1Id()
//                                             && CommonDtoUtil.intValToShort(CommonConstants.NUMBER_TWO) == element.getKbnId()
//                                             && countkaisu == element.getKoumokuId())
//                                     .findFirst();

//                             // 「変数.帳票マスタデータ情報」特定できない場合 かつ 変数.ループ回数<= 変数.分割数（下）の場合、下記処理を行う。
//                             if (!kghKrkMocFree2NoFltOptional.isPresent()
//                                     && count <= CommonDtoUtil.strValToInt(columnCount2)) {
//                                 String nameKnj;
//                                 int inputKbn;
//                                 int rendouKbn;
//                                 int widthCnt;
//                                 //
//                                 // ①「変数.帳票マスタデータ情報バックアップ」情報は取得る場合「複写元にデータがある場合」、帳票マスタデータ情報の登録用データを作成する
//                                 if (gui01221DuplicateRowForBackupOptional.isPresent()) {

//                                     // ①-1 変数と初期値を設定する。
//                                     // 変数.項目名＝変数.帳票マスタデータ情報バックアップ.項目名
//                                     nameKnj = gui01221DuplicateRowForBackupOptional.get().getNameKnj();
//                                     // 変数.入力方法＝変数.帳票マスタデータ情報バックアップ.入力方法
//                                     inputKbn = CommonDtoUtil

//                                             .strValToInt(gui01221DuplicateRowForBackupOptional.get().getInputKbn());
//                                     // 変数.連動区分＝変数.帳票マスタデータ情報バックアップ.連動区分
//                                     rendouKbn = CommonDtoUtil

//                                             .strValToInt(gui01221DuplicateRowForBackupOptional.get().getRendouKbn());
//                                     // 変数.文字数＝変数.帳票マスタデータ情報バックアップ.文字数
//                                     widthCnt = CommonDtoUtil

//                                             .strValToInt(gui01221DuplicateRowForBackupOptional.get().getWidthCnt());
//                                     // 「6.1.」変数設定により、変数.設定可能最大文字数を取得する。
//                                     // ①-2 以下の計算式で変数.文字数を設定する。
//                                     // ⅰ. 変数.複写用フラグ=falseの場合
//                                     if (!copyFlg) {
//                                         // ⅰ-1. 変数.ループ回数= 変数.分割数（下）の場合
//                                         if (count == CommonDtoUtil.strValToInt(columnCount2)) {
//                                             // 変数.文字数＝変数.設定可能最大文字数 - 変数.総文字数
//                                             widthCnt = widthCntMax - totalWidthCnt;
//                                         } else {
//                                             // ⅰ-1. 「ⅰ-1.」以外の場合
//                                             // 変数.総文字数=変数.文字数 + 変数.総文字数
//                                             totalWidthCnt = widthCnt + totalWidthCnt;
//                                             // ⅰ-1-1. 変数.設定可能最大文字数 <= 変数.総文字数の場合
//                                             if (widthCntMax <= totalWidthCnt) {
//                                                 // 変数.文字数=変数.文字数 - (変数.総文字数 - 変数.設定可能最大文字数)
//                                                 widthCnt = widthCnt - (totalWidthCnt - widthCntMax);
//                                                 // 変数.複写用フラグ=true
//                                                 copyFlg = Boolean.TRUE;
//                                             }
//                                         }

//                                     } else {
//                                         // Ⅱ. 変数.複写用フラグ=trueの場合
//                                         // 変数.文字数＝0
//                                         widthCnt = CommonConstants.NUMBER_ZERO;

//                                     }

//                                 } else {
//                                     // ②
//                                     // 「①」以外の場合「変数.帳票マスタデータ情報バックアップ情報はない場合「複写元にデータがない場合」」、帳票マスタデータ情報の登録用データを作成する

//                                     // ②-1 変数と初期値を設定する。
//                                     // 変数.項目名＝"項目" + 変数.ループ回数
//                                     nameKnj = CommonConstants.NAME_KNJ + count;
//                                     // 変数.入力方法＝1
//                                     inputKbn = CommonConstants.NUMBER_ONE;
//                                     // 変数.連動区分＝0
//                                     rendouKbn = CommonConstants.NUMBER_ZERO;
//                                     // 変数.文字数＝0
//                                     widthCnt = CommonConstants.NUMBER_ZERO;
//                                     // 「6.1.」変数設定により、変数.設定可能最大文字数を取得する。
//                                     // ②-2 以下の計算式で変数.文字数を設定する。
//                                     // ⅰ. 変数.複写用フラグ=falseの場合
//                                     if (!copyFlg && count == CommonDtoUtil.strValToInt(columnCount2)) {
//                                         // ⅰ-1. 変数.ループ回数= 変数.分割数（下）の場合
//                                         // 変数.文字数＝変数.設定可能最大文字数 - 変数.総文字数
//                                         widthCnt = widthCntMax - totalWidthCnt;
//                                     }
//                                 }

//                                 // ③帳票マスタデータ情報を登録する

//                                 // 27-02 カスタマイズ帳票マスタデータ(対象テーブル)※別紙「DB更新詳細」の「11.」 を参照する
//                                 KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();
//                                 // マスタヘッダID =変数.マスタヘッダID

//                                 kghMocKrkFree2.setFree1Id(CommonDtoUtil.strValToInt(free1Id));
//                                 // 区分ID= 2
//                                 kghMocKrkFree2.setKbnId(CommonConstants.NUMBER_TWO);
//                                 // 項目ID=（1～15） 変数.ループ回数
//                                 kghMocKrkFree2.setKoumokuId(count);
//                                 // 項目名 =変数.項目名
//                                 kghMocKrkFree2.setNameKnj(nameKnj);
//                                 // 入力方法 =変数.入力方法
//                                 kghMocKrkFree2.setInputKbn(inputKbn);
//                                 // 連動区分 =変数.連動区分
//                                 kghMocKrkFree2.setRendouKbn(rendouKbn);
//                                 // 文字数 =変数.文字数
//                                 kghMocKrkFree2.setWidthCnt(widthCnt);
//                                 // 自由に使用 -
//                                 // 削除フラグ = 0
//                                 // 登録時の共通カラム値設定処理
//                                 CommonDaoUtil.setInsertCommonColumns(kghMocKrkFree2);
//                                 // DAOの実行
//                                 kghMocKrkFree2Mapper.insertSelective(kghMocKrkFree2);

//                             }

//                         }

//                     }

//                 } else if (CommonConstants.STR_2.equals(defUpdateFlg)) {
//                     // 6.4. 「変数.初期値作成フラグ=2」の場合、既存変更行の処理を実施する
//                     // 6.4.1. 更新時にkgh_moc_krk_free2テーブルの値を変更する（文字・分割数変更用）処理を行う
//                     // 6.4.1.1 変数の設定
//                     // 変数.数字件数=0
//                     int variableNumber = CommonConstants.NUMBER_ZERO;
//                     // 6.4.1.2. 変数.ループ回数が1から15「上段の最大項目数」まで繰り返し、以下の処理を行う。
//                     // 【繰り返し開始】
//                     for (int count = 1; count <= CommonConstants.NUMBER_FIFTY; count++) {

//                         // 6.4.1.2.1.上記「6.2. 」で取得したカスタマイズ帳票マスタデータ情報リストより
//                         // 下記条件で特定できた「カスタマイズ帳票マスタデータ情報」を「変数.帳票マスタデータ情報」に設定する
//                         // カスタマイズ帳票マスタデータ情報.マスタヘッダID=変数.マスタヘッダID、
//                         // カスタマイズ帳票マスタデータ情報.区分ID=1、
//                         // カスタマイズ帳票マスタデータ情報.項目ID=変数.ループ回数
//                         final int countkaisu = count;
//                         Optional<KghKrkMocFree2NoFltOutEntity> kghKrkMocFree2NoFltOptional = krkMocFree2NoFltOutEntitieList
//                                 .stream()
//                                 .filter(element -> CommonDtoUtil.strValToInt(free1Id) == element.getFree1Id()
//                                         && CommonDtoUtil.intValToShort(CommonConstants.NUMBER_ONE) == element.getKbnId()
//                                         && countkaisu == element.getKoumokuId())
//                                 .findFirst();
//                         // 6.4.1.2.2 「変数.帳票マスタデータ情報」が取得できる かつ 変数.ループ回数<=
//                         // 変数.分割数（上）の場合、下記処理を行う。
//                         if (kghKrkMocFree2NoFltOptional.isPresent()
//                                 && count <= CommonDtoUtil.strValToInt(columnCount)) {
//                             // ① 変数.帳票マスタデータ情報.入力方法=2の場合
//                             if (CommonDtoUtil.intValToShort(CommonConstants.NUMBER_TWO) == kghKrkMocFree2NoFltOptional.get().getInputKbn()) {
//                                 // ①-1 変数.数字件数=変数.数字件数+1
//                                 variableNumber += CommonConstants.NUMBER_ONE;
//                             }
//                         }

//                     }
//                     // 【繰り返し終了】
//                     // 6.4.1.3 変数の設定
//                     // 変数.入力件数=0
//                     int variableInput = CommonConstants.NUMBER_ZERO;
//                     // 6.4.1.4. 変数.ループが1から15「上段の最大項目数」まで繰り返し、以下の処理を行う。
//                     // 【繰り返し開始】
//                     for (int count = 1; count <= CommonConstants.NUMBER_FIFTY; count++) {
//                         // 6.4.1.4.1.上記「6.2. 」で取得したカスタマイズ帳票マスタデータ情報リストより
//                         // 下記条件で特定できた「カスタマイズ帳票マスタデータ情報」を「変数.帳票マスタデータ情報」に設定する
//                         // カスタマイズ帳票マスタデータ情報.マスタヘッダID=変数.マスタヘッダID、
//                         // カスタマイズ帳票マスタデータ情報.区分ID=２、
//                         // カスタマイズ帳票マスタデータ情報.項目ID=変数.ループ回数

//                         final int countkaisu = count;
//                         Optional<KghKrkMocFree2NoFltOutEntity> kghKrkMocFree2NoFltOptional = krkMocFree2NoFltOutEntitieList
//                                 .stream()
//                                 .filter(element -> CommonDtoUtil.strValToInt(free1Id) == element.getFree1Id()
//                                         && CommonDtoUtil.intValToShort(CommonConstants.NUMBER_TWO) == element.getKbnId()
//                                         && countkaisu == element.getKoumokuId())
//                                 .findFirst();
//                         // 6.4.1.4.2 変数.ループ回数<= 変数.分割数（上）の場合、下記処理を行う。
//                         if (count <= CommonDtoUtil.strValToInt(columnCount)) {
//                             int widthCnt = CommonConstants.NUMBER_ZERO;
//                             // ① 変数.帳票マスタデータ情報.入力方法=2の場合
//                             if (kghKrkMocFree2NoFltOptional.isPresent()
//                                     && CommonDtoUtil.intValToShort(CommonConstants.NUMBER_TWO) == kghKrkMocFree2NoFltOptional.get().getInputKbn()) {
//                                 // 変数.文字数=1
//                                 widthCnt = CommonConstants.NUMBER_ONE;
//                             } else {
//                                 // ② 上記「①」以外の場合、 変数.文字数を計算する
//                                 // ②-1 「6.1.」変数設定により、変数.設定可能最大文字数を取得する。
//                                 // ②-2 以下の計算式の商と余りを取得し、変数.文字数計算の商と変数.文字数計算の余りに設定する。
//                                 // 計算式：(変数.設定可能最大文字数 - 変数.数字件数) / (変数.分割数（上） - 変数.数字件数)
//                                 int widthResult = (widthCntMax - variableNumber)
//                                         / (CommonDtoUtil.strValToInt(columnCount) - variableNumber);
//                                 int widthRemainder = (widthCntMax - variableNumber)
//                                         % (CommonDtoUtil.strValToInt(columnCount) - variableNumber);
//                                 // 変数.文字数計算の余り > 変数.入力件数の場合
//                                 if (widthRemainder > variableInput) {
//                                     // 変数.文字数＝変数.文字数計算の商 + 1
//                                     widthCnt = widthResult + CommonConstants.NUMBER_ONE;
//                                 } else {
//                                     // 以外の場合
//                                     // 変数.文字数＝変数.文字数計算の商
//                                     widthCnt = widthResult;
//                                 }
//                                 // ②-3 変数.入力件数の設定
//                                 // 変数.入力件数=変数.入力件数+1
//                                 variableInput += CommonConstants.NUMBER_ONE;
//                             }

//                             // ③ 変数.ループ回数> 変数.分割数比較用バックアップ分割数（上）の場合

//                             if (count > compareColumnCount) {
//                                 // ③-1 「変数.帳票マスタデータ情報」が取得できない場合、帳票マスタデータ情報を登録する
//                                 if (!kghKrkMocFree2NoFltOptional.isPresent()) {
//                                     // 27-02 カスタマイズ帳票マスタデータ（対象テーブル）※別紙「DB更新詳細」の「5.」 を参照する
//                                     // マスタヘッダID 変数.マスタヘッダID
//                                     KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();

//                                     kghMocKrkFree2.setFree1Id(CommonDtoUtil.strValToInt(free1Id));
//                                     // 区分ID= 1
//                                     kghMocKrkFree2.setKbnId(CommonConstants.NUMBER_ONE);
//                                     // 項目ID=（1～15） 変数.ループ
//                                     kghMocKrkFree2.setKoumokuId(count);
//                                     // 項目名= "項目" + 変数.ループ
//                                     kghMocKrkFree2.setNameKnj(CommonConstants.NAME_KNJ + count);
//                                     // 入力方法= 1
//                                     kghMocKrkFree2.setInputKbn(CommonConstants.NUMBER_ONE);
//                                     // 連動区分= 0
//                                     kghMocKrkFree2.setRendouKbn(CommonConstants.NUMBER_ZERO);
//                                     // 文字数 変数.文字数
//                                     kghMocKrkFree2.setWidthCnt(widthCnt);
//                                     // 自由に使用 -
//                                     // 削除フラグ= 0
//                                     // 登録時の共通カラム値設定処理
//                                     CommonDaoUtil.setInsertCommonColumns(kghMocKrkFree2);
//                                     // DAOの実行
//                                     kghMocKrkFree2Mapper.insertSelective(kghMocKrkFree2);
//                                 } else {
//                                     // ③-2 「変数.帳票マスタデータ情報」が取得できる場合、帳票マスタデータ情報を更新する
//                                     // 27-02 カスタマイズ帳票マスタデータ※別紙「DB更新詳細」の「6.」 を参照する
//                                     KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();
//                                     // width_cnt 文字数
//                                     kghMocKrkFree2.setWidthCnt(widthCnt);
//                                     // 更新条件
//                                     KghMocKrkFree2Criteria kghMocKrkFree2Criteria = new KghMocKrkFree2Criteria();
//                                     // マスタヘッダID＝変数.マスタヘッダID
//                                     kghMocKrkFree2Criteria.createCriteria()
//                                             .andFree1IdEqualTo(CommonDtoUtil.strValToInt(free1Id))
//                                             // 区分ID＝1
//                                             .andKbnIdEqualTo(CommonConstants.NUMBER_ONE)
//                                             // 項目ID＝変数.ループ回数
//                                             .andKoumokuIdEqualTo(count);
//                                     // 更新時の共通カラム値設定処理
//                                     // DAOの実行
//                                     int updateCnt = kghMocKrkFree2Mapper.updateByCriteriaSelective(kghMocKrkFree2,
//                                             kghMocKrkFree2Criteria);
//                                     // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
//                                     if (updateCnt <= 0) {
//                                         throw new ExclusiveException();
//                                     }
//                                 }

//                             } else {
//                                 // ④ 上記「③」以外の場合
//                                 // ④-1 「変数.帳票マスタデータ情報」が取得できない場合、帳票マスタデータ情報を登録する
//                                 // ③-1 の処理を参照してください。 ※27-02 カスタマイズ帳票マスタデータ※別紙「DB更新詳細」の「5.」 を参照する
//                                 if (!kghKrkMocFree2NoFltOptional.isPresent()) {
//                                     KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();

//                                     kghMocKrkFree2.setFree1Id(CommonDtoUtil.strValToInt(free1Id));
//                                     // 区分ID= 1
//                                     kghMocKrkFree2.setKbnId(CommonConstants.NUMBER_ONE);
//                                     // 項目ID=（1～15） 変数.ループ
//                                     kghMocKrkFree2.setKoumokuId(count);
//                                     // 項目名= "項目" + 変数.ループ
//                                     kghMocKrkFree2.setNameKnj(CommonConstants.NAME_KNJ + count);
//                                     // 入力方法= 1
//                                     kghMocKrkFree2.setInputKbn(CommonConstants.NUMBER_ONE);
//                                     // 連動区分= 0
//                                     kghMocKrkFree2.setRendouKbn(CommonConstants.NUMBER_ZERO);
//                                     // 文字数 変数.文字数
//                                     kghMocKrkFree2.setWidthCnt(widthCnt);
//                                     // 自由に使用 -
//                                     // 削除フラグ= 0
//                                     // 登録時の共通カラム値設定処理
//                                     CommonDaoUtil.setInsertCommonColumns(kghMocKrkFree2);
//                                     // DAOの実行
//                                     kghMocKrkFree2Mapper.insertSelective(kghMocKrkFree2);
//                                 }
//                             }
//                         }
//                     }
//                     // 【繰り返し終了】

//                     // 6.4.1.5 変数の初期化
//                     // 変数.入力件数=0
//                     variableInput = CommonConstants.VARUABLE_IN_PUT_ZERO;
//                     // 6.4.1.6. 【繰り返し開始】変数.ループが1から3「下段の最大項目数」まで繰り返し、以下の処理を行う。
//                     for (int count = CommonConstants.NUMBER_1; count <= CommonConstants.NUMBER_3; count++) {
//                         // 6.4.1.4.1.上記「6.2. 」で取得したカスタマイズ帳票マスタデータ情報リストより
//                         // 下記条件で特定できた「カスタマイズ帳票マスタデータ情報」を「変数.帳票マスタデータ情報」に設定する
//                         // カスタマイズ帳票マスタデータ情報.マスタヘッダID=変数.マスタヘッダID、
//                         // カスタマイズ帳票マスタデータ情報.区分ID=２、
//                         // カスタマイズ帳票マスタデータ情報.項目ID=変数.ループ回数
//                         final int countkaisu = count;
//                         Optional<KghKrkMocFree2NoFltOutEntity> kghKrkMocFree2NoFltOptional = krkMocFree2NoFltOutEntitieList
//                                 .stream()
//                                 .filter(element -> CommonDtoUtil.strValToInt(free1Id) == element.getFree1Id()
//                                         && CommonDtoUtil.intValToShort(CommonConstants.NUMBER_TWO) == element.getKbnId()
//                                         && countkaisu == element.getKoumokuId())
//                                 .findFirst();
//                         // 6.4.1.6.2 変数.ループ回数<= 変数.分割数（下）の場合、下記処理を行う。
//                         int widthCnt = CommonConstants.NUMBER_ZERO;
//                         if (count <= CommonDtoUtil.strValToInt(columnCount2)) {
//                             // ① 変数.帳票マスタデータ情報.入力方法=2の場合
//                             if (kghKrkMocFree2NoFltOptional.isPresent()
//                                     && CommonDtoUtil.intValToShort(CommonConstants.NUMBER_TWO) == kghKrkMocFree2NoFltOptional.get().getInputKbn()) {
//                                 // 変数.文字数=1
//                                 widthCnt = CommonConstants.NUMBER_ONE;
//                             } else {
//                                 // ② 上記「①」以外の場合、 変数.文字数を計算する
//                                 // ②-1 「6.1.」変数設定により、変数.設定可能最大文字数を取得する。
//                                 // ②-2 以下の計算式の商と余りを取得し、変数.文字数計算の商と変数.文字数計算の余りに設定する。
//                                 // 計算式：(変数.設定可能最大文字数) / (変数.分割数（下）)
//                                 int widthResult = widthCntMax /
//                                         CommonDtoUtil.strValToInt(columnCount2);
//                                 int widthRemainder = widthCntMax %
//                                         CommonDtoUtil.strValToInt(columnCount2);
//                                 // 変数.文字数計算の余り > 変数.入力件数の場合
//                                 if (widthRemainder > variableInput) {
//                                     // 変数.文字数＝変数.文字数計算の商 + 1
//                                     widthCnt = widthResult + CommonConstants.NUMBER_ONE;
//                                 } else {
//                                     // 以外の場合
//                                     // 変数.文字数＝変数.文字数計算の商
//                                     widthCnt = widthResult;
//                                 }
//                                 // ②-3 変数.入力件数の設定
//                                 // 変数.入力件数=変数.入力件数+1
//                                 variableInput += CommonConstants.NUMBER_ONE;
//                             }
//                             // ③ 変数.ループ回数 > 変数.分割数比較用バックアップ分割数（下）の場合
//                             if (count > compareColumnCount2) {
//                                 // ③-1 「変数.帳票マスタデータ情報」が取得できない場合、帳票マスタデータ情報を登録する
//                                 if (!kghKrkMocFree2NoFltOptional.isPresent()) {
//                                     // 27-02 カスタマイズ帳票マスタデータ ※別紙「DB更新詳細」の「7.」 を参照する
//                                     KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();
//                                     // マスタヘッダID = 変数.マスタヘッダID

//                                     kghMocKrkFree2.setFree1Id(CommonDtoUtil.strValToInt(free1Id));
//                                     // 区分ID =2
//                                     kghMocKrkFree2.setKbnId(CommonConstants.NUMBER_TWO);
//                                     // 項目ID（1～15）= 変数.ループ回数
//                                     kghMocKrkFree2.setKoumokuId(count);
//                                     // 項目名 ="項目" + 変数.ループ回数
//                                     kghMocKrkFree2.setNameKnj(CommonConstants.NAME_KNJ + count);
//                                     // 入力方法 =1
//                                     kghMocKrkFree2.setInputKbn(CommonConstants.NUMBER_ONE);
//                                     // 連動区分= 0
//                                     kghMocKrkFree2.setRendouKbn(CommonConstants.NUMBER_ZERO);
//                                     // 文字数 =変数.文字数
//                                     kghMocKrkFree2.setWidthCnt(widthCnt);
//                                     // 自由に使用 -
//                                     // 削除フラグ =0
//                                     // 登録時の共通カラム値設定処理
//                                     CommonDaoUtil.setInsertCommonColumns(kghMocKrkFree2);
//                                     // DAOの実行
//                                     kghMocKrkFree2Mapper.insertSelective(kghMocKrkFree2);

//                                 } else {
//                                     // ③-2 「変数.帳票マスタデータ情報」が取得できる場合、帳票マスタデータ情報を更新する
//                                     // 27-02 カスタマイズ帳票マスタデータ ※別紙「DB更新詳細」の「8.」 を参照する
//                                     KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();
//                                     // 更新条件
//                                     KghMocKrkFree2Criteria kghMocKrkFree2Criteria = new KghMocKrkFree2Criteria();
//                                     // マスタヘッダID＝変数.マスタヘッダID
//                                     kghMocKrkFree2Criteria.createCriteria()
//                                             .andFree1IdEqualTo(CommonDtoUtil.strValToInt(free1Id))
//                                             // 区分ID＝2
//                                             .andKbnIdEqualTo(CommonConstants.NUMBER_TWO)
//                                             // 項目ID＝変数.ループ回数
//                                             .andKoumokuIdEqualTo(count);
//                                     // width_cnt 文字数 変数.文字数
//                                     kghMocKrkFree2.setWidthCnt(widthCnt);
//                                     // DAOの実行
//                                     int updateCnt = kghMocKrkFree2Mapper.updateByCriteriaSelective(kghMocKrkFree2,
//                                             kghMocKrkFree2Criteria);
//                                     // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
//                                     if (updateCnt <= 0) {
//                                         throw new ExclusiveException();
//                                     }

//                                 }

//                             } else {
//                                 // ④ 上記「③」以外の場合
//                                 // ④-1 「変数.帳票マスタデータ情報」が取得できない場合、帳票マスタデータ情報を登録する
//                                 // ③-1 の処理を参照してください。 ※27-02 カスタマイズ帳票マスタデータ ※別紙「DB更新詳細」の「7.」 を参照する
//                                 if (!kghKrkMocFree2NoFltOptional.isPresent()) {
//                                     KghMocKrkFree2 kghMocKrkFree2 = new KghMocKrkFree2();
//                                     // マスタヘッダID = 変数.マスタヘッダID

//                                     kghMocKrkFree2.setFree1Id(CommonDtoUtil.strValToInt(free1Id));
//                                     // 区分ID =2
//                                     kghMocKrkFree2.setKbnId(CommonConstants.NUMBER_TWO);
//                                     // 項目ID（1～15）= 変数.ループ回数
//                                     kghMocKrkFree2.setKoumokuId(count);
//                                     // 項目名 ="項目" + 変数.ループ回数
//                                     kghMocKrkFree2.setNameKnj(CommonConstants.NAME_KNJ + count);
//                                     // 入力方法 =1
//                                     kghMocKrkFree2.setInputKbn(CommonConstants.NUMBER_ONE);
//                                     // 連動区分= 0
//                                     kghMocKrkFree2.setRendouKbn(CommonConstants.NUMBER_ZERO);
//                                     // 文字数 =変数.文字数
//                                     kghMocKrkFree2.setWidthCnt(widthCnt);
//                                     // 自由に使用 -
//                                     // 削除フラグ =0
//                                     // 登録時の共通カラム値設定処理
//                                     CommonDaoUtil.setInsertCommonColumns(kghMocKrkFree2);
//                                     // DAOの実行
//                                     kghMocKrkFree2Mapper.insertSelective(kghMocKrkFree2);
//                                 }
//                             }
//                         }

//                     }
//                     // 【繰り返し終了】

//                 }

//             }
//         }
//         outDto.setBeforeUpdateFlag(beforeUpdateFlag);
//         LOG.info(Constants.END);
//         return outDto;
//     }

// }
