package jp.ndsoft.carebase.cmn.api.report.dto;

import java.util.Map;
import jp.ndsoft.smh.framework.global.report.dto.FixedReportInDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * @since 2025.09.01
 * <AUTHOR>
 *         V00261_限度額超過利用者・算定エラー利用者一覧の入力DTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SanteErrItiranRecordReportServiceInDto extends FixedReportInDto {
    /** UID. */
    private static final long serialVersionUID = 1L;
    /** データソース */
    private JRBeanCollectionDataSource dataSource;
    /** パラメータ */
    private Map<String, Object> parameters;

    /** 帳票タイトル */
    private String title;

    /** 指定日印刷区分 */
    private Integer shiTeiKubun;

    /** 出力日 */
    private String shiTeiDateGG;

    /** 出力日 */
    private String shiTeiDateYY;

    /** 出力日 */
    private String shiTeiDateMM;

    /** 出力日 */
    private String shiTeiDateDD;

    /** 支援事業者名 */
    private String jigyoName;

    /** 変更日(開始) */
    private String henkouStartYmdGG;

    /** 変更日(開始) */
    private String henkouStartYmdYY;

    /** 変更日(開始) */
    private String henkouStartYmdMM;

    /** 変更日(終了) */
    private String henkouEndYmdGG;

    /** 変更日(終了) */
    private String henkouEndYmdYY;

    /** 変更日(終了) */
    private String henkouEndYmdMM;

    /** 変更日期間表示フラグ */
    private Integer henkouYmdDispFlg;

    /** 一覧詳細リスト */
    private JRBeanCollectionDataSource itiranDetailList;

    /** 一覧詳細リスト１ */
    private JRBeanCollectionDataSource itiranDetailList1;

    /** 文書管理番号 */
    private String bunsyoKanriNo;

    /** 種類限度表示フラグ */
    private Integer shuGendoDispFlg;

    /** フル表示フラグ */
    private Integer fullDispFlg;

    /** 表示フラグ */
    private Integer dispFlg;

}