package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.*;
import jp.ndsoft.carebase.cmn.api.service.dto.CarePlan2HistorySelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.CarePlan2HistorySelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.*;
import jp.ndsoft.carebase.common.dao.mysql.mapper.*;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.invoke.MethodHandles;
import java.util.*;
import java.util.stream.Collectors;

/**
 * GUI01014_計画書（２）履歴変更
 *
 * <AUTHOR>
 */
@Service
public class CarePlan2HistorySelectServiceImpl
        extends SelectServiceImpl<CarePlan2HistorySelectServiceInDto, CarePlan2HistorySelectServiceOutDto> {
    /**
     * ロガー
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    /**
     * 変数.履歴インデックスが1
     */
    private static Integer RIREKI_INDEX_ONE = 1;
    /**
     * 計画期間ページ区分が1
     */
    private static String RIREKI_PAGE_ONE = "1";
    /**
     * 計画期間ページ区分が2
     */
    private static String RIREKI_PAGE_TWO = "2";

    /**
     * 職員氏名情報の取得
     */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;
    /**
     * 履歴情報を取得
     */
    @Autowired
    private CpnTucCks21SelectMapper cpnTucCks21SelectMapper;
    /**
     * 計画書（２）詳細情報を取得
     */
    @Autowired
    private CpnTucCks22SelectMapper cpnTucCks22SelectMapper;
    /**
     * 保険サービス情報
     */
    @Autowired
    private CpnTucCks211SelectMapper cpnTucCks211SelectMapper;
    /**
     * 月日指定情報を取得
     */
    @Autowired
    private CpnTucCks212SelectMapper cpnTucCks212SelectMapper;
    /**
     * サービス曜日情報を取得
     */
    @Autowired
    private CpnTucCks221SelectMapper cpnTucCks221SelectMapper;
    /**
     * サービス曜日情報を取得
     */
    @Autowired
    private CpnTucCks222SelectMapper cpnTucCks222SelectMapper;
    /**
     * CpnTucCks224SelectMapper
     */
    @Autowired
    private CpnTucCks224SelectMapper cpnTucCks224SelectMapper;


    /**
     * 計画書（２）履歴変更
     *
     * @param inDto 計画書（２）履歴変更の入力DTO.
     * @return 計画書（２）履歴変更のOUT DTO
     * @throws Exception Exception
     */
    @Override
    protected CarePlan2HistorySelectServiceOutDto mainProcess(CarePlan2HistorySelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // 出力Dto
        CarePlan2HistorySelectServiceOutDto outDto = new CarePlan2HistorySelectServiceOutDto();
        // 2. 職員氏名情報の取得
        List<ShokuinInfoListSpaceSortOutEntity> shokuiOutList = comMscShokuinSelectMapper
                .findShokuinInfoListSpaceSortByCriteria(new ShokuinInfoListSpaceSortByCriteriaInEntity());

        // 3. 履歴情報の取得
        Keikaku2HeadInfoListByCriteriaInEntity keikaku2HeadInfoListByCriteriaInEntity = new Keikaku2HeadInfoListByCriteriaInEntity();

        // 計画対象期間情報.期間ID
        keikaku2HeadInfoListByCriteriaInEntity.setSc1(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // 計画対象期間情報.事業者ID
        keikaku2HeadInfoListByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 計画対象期間情報.利用者ID
        keikaku2HeadInfoListByCriteriaInEntity.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        List<Keikaku2HeadInfoListOutEntity> cks21OutList = cpnTucCks21SelectMapper
                .findKeikaku2HeadInfoListByCriteria(keikaku2HeadInfoListByCriteriaInEntity);

        // 変数.履歴インデックス
        Integer hisIndex = 0;
        // 履歴情報の総件数
        Integer hisListSize = 0;
        // 履歴情報に存在フラグ
        boolean hasHisID = false;
        if (CollectionUtils.isNotEmpty(cks21OutList)) {
            // 履歴情報の総件数
            hisListSize = cks21OutList.size();

            int ks21IdInt = CommonDtoUtil.strValToInt(inDto.getKs21Id());
            for (int i = 0; i < hisListSize; i++) {
                Keikaku2HeadInfoListOutEntity history = cks21OutList.get(i);
                if (history.getKs21Id().equals(ks21IdInt)) {
                    hasHisID = true;
                    hisIndex = hisListSize - i;
                }
            }
        }
        // 3.2. リクエストパラメータ.履歴IDが履歴情報に存在しない場合
        if (!hasHisID) {
            // 3.2.1. 履歴情報のサイズを変数.履歴インデックスに設定する。
            hisIndex = hisListSize;
        } else {
            // 3.3.2.リクエストパラメータ.計画期間ページ区分が1、且つ、変数.履歴インデックスが1ではない場合、変数.履歴インデックスから1を減らす。
            if (RIREKI_PAGE_ONE.equals(inDto.getRirekiPage()) && !RIREKI_INDEX_ONE.equals(hisIndex)) {
                hisIndex -= 1;
            }
            // 3.3.3.リクエストパラメータ.計画期間ページ区分が2、且つ、変数.履歴インデックスが履歴情報のサイズではない場合、変数.履歴インデックスから1を増やす。
            if (RIREKI_PAGE_TWO.equals(inDto.getRirekiPage()) && !hisListSize.equals(hisIndex)) {
                hisIndex += 1;
            }
        }
        // 出力Dtoの編集
        if (CollectionUtils.isNotEmpty(cks21OutList)) {
            Keikaku2HeadInfoListOutEntity headCks21OutInfo = cks21OutList.get(cks21OutList.size() - hisIndex);

            // 履歴情報の編集
            outDto.setRirekiObj(editRirekiInfo(headCks21OutInfo, shokuiOutList, hisIndex, hisListSize));

            // 4. 計画書（２）詳細データを取得する。
            // 4.1. 計画書（２）詳細情報を取得する。
            outDto.setKeikasyo2List(acquCpnOutInfo(headCks21OutInfo.getKs21Id()));

            // 4.2. リクエストパラメータ.計画書様式が居宅の場合
            if (CommonConstants.CKS_FLG_HOME.equals(inDto.getCksflg())) {
                // 保険サービスリスト
                outDto.setHokenList(acquHokenList(headCks21OutInfo.getKs21Id()));
                // 月日指定リスト
                outDto.setTukihiList(acquTukihiList(headCks21OutInfo.getKs21Id()));
            } // 4.3. リクエストパラメータ.計画書様式が施設の場合
            else if (CommonConstants.CKS_FLG_FACILITY.equals(inDto.getCksflg())) {
                // サービス曜日情報の編集
                List<Gui01014Yobi> yobiList = acquYobiList(headCks21OutInfo.getKs21Id());
                // 担当者情報の編集
                List<Gui01014Tanto> tantoList = acquTantoList(headCks21OutInfo.getKs21Id());

                for (Gui01014Keikasyo2 kasyo2Info : outDto.getKeikasyo2List()) {
                    // サービス曜日リスト
                    List<Gui01014Yobi> filterYobiList = yobiList.stream()
                            // 計画書（２）行データID ＝ 計画書（２）リスト.カウンター
                            .filter(item -> item.getKs22Id().equals(kasyo2Info.getKs22Id())
                                    // 計画書（２）ID = 計画書（２）リスト.計画書（２）ID
                                    && item.getKs21Id().equals(kasyo2Info.getKs21Id()))
                            .collect(Collectors.toList());
                    kasyo2Info.setYobiList(filterYobiList);

                    // 担当者リスト
                    List<Gui01014Tanto> filterTantoList = tantoList.stream()
                            // 計画書（２）行データID ＝ 計画書（２）リスト.カウンター
                            .filter(item -> item.getKs22Id().equals(kasyo2Info.getKs22Id())
                                    // 計画書（２）ID = 計画書（２）リスト.計画書（２）ID
                                    && item.getKs21Id().equals(kasyo2Info.getKs21Id()))
                            .collect(Collectors.toList());
                    kasyo2Info.setTantoList(filterTantoList);
                }
            }

            // 4.4. リクエストパラメータ.記録との連携が「1」:記録との連携を行うの場合
            if (CommonConstants.STR_1.equals(inDto.getKirokuRenkeiFlg())) {
                CpnTucCks224InfoByCriteriaInEntity cpnTucCks224InfoIn = new CpnTucCks224InfoByCriteriaInEntity();
                cpnTucCks224InfoIn.setKs21Id(headCks21OutInfo.getKs21Id());
                List<CpnTucCks224InfoOutEntity> cpnTucCks224InfoOuts = cpnTucCks224SelectMapper.findCpnTucCks224InfoByCriteria(
                        cpnTucCks224InfoIn);

                Map<String, CpnTucCks224InfoOutEntity> cpnTucCks224InfoMap = new HashMap<>();
                cpnTucCks224InfoOuts.forEach((cpnTucCks224Info) -> {
                    cpnTucCks224InfoMap.put(CommonDtoUtil.objValToString(cpnTucCks224Info.getKs22Id()),
                            cpnTucCks224Info);
                });

                outDto.getKeikasyo2List().forEach((keikasyo2) -> {
                    CpnTucCks224InfoOutEntity cpnTucCks224Info = cpnTucCks224InfoMap.get(keikasyo2.getKs22Id());
                    if (cpnTucCks224Info != null) {
                        keikasyo2.setNikkaId(CommonDtoUtil.objValToString(cpnTucCks224Info.getNikkaId()));
                    }
                });
            }
        }

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 履歴情報の編集
     *
     * @param headCks21OutInfo 履歴情報
     * @param shokuiOutList    職員情報リスト
     * @param hisIndex         .変数.履歴インデックス
     * @param hisListSize      履歴情報の総件数
     * @return 履歴情報
     */
    private List<Gui01014Rireki> editRirekiInfo(Keikaku2HeadInfoListOutEntity headCks21OutInfo,
            List<ShokuinInfoListSpaceSortOutEntity> shokuiOutList,
            Integer hisIndex, Integer hisListSize) {
        Gui01014Rireki rirekiObj = new Gui01014Rireki();
        // 計画書（２）ID
        rirekiObj.setKs21Id(CommonDtoUtil.objValToString(headCks21OutInfo.getKs21Id()));
        // 作成日
        rirekiObj.setCreateYmd(headCks21OutInfo.getCreateYmd());
        // 作成者ID
        rirekiObj.setShokuId(CommonDtoUtil.objValToString(headCks21OutInfo.getShokuId()));
        // 作成者名
        String outShokuknj = null;
        if (CollectionUtils.isNotEmpty(shokuiOutList)) {
            ShokuinInfoListSpaceSortOutEntity shokuiOutHead = shokuiOutList.stream()
                    .filter(shokuin -> shokuin.getChkShokuId().equals(headCks21OutInfo.getShokuId())).findFirst()
                    .orElse(null);
            if (shokuiOutHead != null) {
                // 職員名（姓） + 半角スペース + 職員名（名）
                outShokuknj = CommonDtoUtil.strNullToEmpty(shokuiOutHead.getShokuin1Knj()).concat(StringUtils.SPACE)
                        .concat(CommonDtoUtil.strNullToEmpty(shokuiOutHead.getShokuin2Knj()));
            }
        }
        rirekiObj.setShokuKnj(outShokuknj);
        // 有効期間ID
        rirekiObj.setTermid(CommonDtoUtil.objValToString(headCks21OutInfo.getTermid()));
        // 履歴インデックス
        rirekiObj.setRirekiIndex(CommonDtoUtil.objValToString(hisIndex));
        // 履歴件数
        rirekiObj.setRirekiCnt(CommonDtoUtil.objValToString(hisListSize));
        List<Gui01014Rireki> rirekiList = new ArrayList<>();
        rirekiList.add(rirekiObj);
        return rirekiList;
    }

    /**
     * 4. 計画書（２）詳細データを取得する。
     *
     * @param hisKs21Id 履歴情報.計画書（２）ID
     * @return 計画書（２）詳細データ
     */
    private List<Gui01014Keikasyo2> acquCpnOutInfo(Integer hisKs21Id) {
        List<Gui01014Keikasyo2> planTwoOutList = new ArrayList<Gui01014Keikasyo2>();
        // 4.1. 計画書（２）詳細情報を取得する。
        CpnTucCks22Ns1SortInfoByCriteriaInEntity cks22InInfo = new CpnTucCks22Ns1SortInfoByCriteriaInEntity();
        // 計画書（２）ID 履歴情報.計画書（２）ID
        cks22InInfo.setKs21(hisKs21Id);
        List<CpnTucCks22Ns1SortInfoOutEntity> cks22OutList = cpnTucCks22SelectMapper
                .findCpnTucCks22Ns1SortInfoByCriteria(cks22InInfo);

        // 計画書（２）リストの編集
        for (CpnTucCks22Ns1SortInfoOutEntity cks22OutInfo : cks22OutList) {
            planTwoOutList.add(editPlanTwoInfo(cks22OutInfo));
        }
        return planTwoOutList;
    }

    /**
     * 4.2.1. 保険サービス情報を取得する。
     *
     * @param hisKs21Id 履歴情報.計画書（２）ID
     * @return 保険サービス情報
     */
    private List<Gui01014Hoken> acquHokenList(Integer hisKs21Id) {
        // 4.2.1. 保険サービス情報を取得する。
        CpnTucCks211InfoByCriteriaInEntity cpnTucCks211InfoByCriteriaInEntity = new CpnTucCks211InfoByCriteriaInEntity();
        // 履歴情報.計画書（２）ID
        cpnTucCks211InfoByCriteriaInEntity.setAiKs21Id(hisKs21Id);
        List<CpnTucCks211InfoOutEntity> cks211OutList = cpnTucCks211SelectMapper
                .findCpnTucCks211InfoByCriteria(cpnTucCks211InfoByCriteriaInEntity);
        // 保険サービスリストの編集
        List<Gui01014Hoken> hokenList = new ArrayList<Gui01014Hoken>();
        for (CpnTucCks211InfoOutEntity cks211OutInfo : cks211OutList) {
            hokenList.add(editInsuranceInfo(cks211OutInfo));
        }
        return hokenList;
    }

    /**
     * 4.2.2. 月日指定情報を取得する。
     *
     * @param hisKs21Id 履歴情報.計画書（２）ID
     * @return 月日指定情報
     */
    private List<Gui01014Tukihi> acquTukihiList(Integer hisKs21Id) {
        // 4.2.2. 月日指定情報を取得する。
        Keikaku2TukihiInfoByCriteriaInEntity keikaku2TukihiInfoByCriteriaInEntity = new Keikaku2TukihiInfoByCriteriaInEntity();
        // 履歴情報.計画書（２）ID
        keikaku2TukihiInfoByCriteriaInEntity.setAiKs21Id(hisKs21Id);
        List<Keikaku2TukihiInfoOutEntity> cks212OutList = cpnTucCks212SelectMapper
                .findKeikaku2TukihiInfoByCriteria(keikaku2TukihiInfoByCriteriaInEntity);
        // 月日指定リストの編集
        List<Gui01014Tukihi> tukihiList = new ArrayList<Gui01014Tukihi>();
        for (Keikaku2TukihiInfoOutEntity cks212OutInfo : cks212OutList) {
            tukihiList.add(editTulohInfo(cks212OutInfo));
        }
        return tukihiList;
    }

    /**
     * 4.3.1. サービス曜日情報を取得する。
     *
     * @param hisKs21Id 履歴情報.計画書（２）ID
     * @return サービス曜日情報
     */
    private List<Gui01014Yobi> acquYobiList(Integer hisKs21Id) {
        // 4.3.1. サービス曜日情報を取得する。
        CpnTucCks221InfoByCriteriaInEntity cpnTucCks221InfoByCriteriaInEntity = new CpnTucCks221InfoByCriteriaInEntity();
        // 履歴情報.計画書（２）ID
        cpnTucCks221InfoByCriteriaInEntity.setAiKs21Id(hisKs21Id);
        List<CpnTucCks221InfoOutEntity> cks221OutList = cpnTucCks221SelectMapper
                .findCpnTucCks221InfoByCriteria(cpnTucCks221InfoByCriteriaInEntity);
        // サービス曜日リストの編集
        List<Gui01014Yobi> yobiList = new ArrayList<Gui01014Yobi>();
        for (CpnTucCks221InfoOutEntity cks221OutInfo : cks221OutList) {
            yobiList.add(editYobiInfo(cks221OutInfo));
        }
        return yobiList;
    }

    /**
     * 4.3.2. サービス曜日情報を取得する。
     *
     * @param hisKs21Id 履歴情報.計画書（２）ID
     * @return 担当者リスト
     */
    private List<Gui01014Tanto> acquTantoList(Integer hisKs21Id) {
        // 4.3.2. サービス曜日情報を取得する。
        CpnTucCks222InfoByCriteriaInEntity cpnTucCks222InfoByCriteriaInEntity = new CpnTucCks222InfoByCriteriaInEntity();
        // 履歴情報.計画書（２）ID
        cpnTucCks222InfoByCriteriaInEntity.setAiKs21Id(hisKs21Id);
        List<CpnTucCks222InfoOutEntity> cks222OutList = cpnTucCks222SelectMapper
                .findCpnTucCks222InfoByCriteria(cpnTucCks222InfoByCriteriaInEntity);
        // 担当者リストの編集
        List<Gui01014Tanto> tantoList = new ArrayList<Gui01014Tanto>();
        for (CpnTucCks222InfoOutEntity cks222OutInfo : cks222OutList) {
            tantoList.add(editGuiTantoInfo(cks222OutInfo));
        }
        return tantoList;
    }

    /**
     * 計画書（２）リストの編集
     *
     * @param cks22OutInfo 計画書（２）詳細情報
     * @return 計画書（２）リスト
     */
    private Gui01014Keikasyo2 editPlanTwoInfo(CpnTucCks22Ns1SortInfoOutEntity cks22OutInfo) {
        Gui01014Keikasyo2 planTwoOutInfo = new Gui01014Keikasyo2();
        // カウンター
        planTwoOutInfo.setKs22Id(CommonDtoUtil.objValToString(cks22OutInfo.getKs22Id()));
        // 計画書（２）ID
        planTwoOutInfo.setKs21Id(CommonDtoUtil.objValToString(cks22OutInfo.getKs21Id()));
        // 具体的
        planTwoOutInfo.setGutaitekiKnj(cks22OutInfo.getGutaitekiKnj());
        // 長期
        planTwoOutInfo.setChoukiKnj(cks22OutInfo.getChoukiKnj());
        // 短期
        planTwoOutInfo.setTankiKnj(cks22OutInfo.getTankiKnj());
        // 介護
        planTwoOutInfo.setKaigoKnj(cks22OutInfo.getKaigoKnj());
        // サービス種
        planTwoOutInfo.setSvShuKnj(cks22OutInfo.getSvShuKnj());
        // 頻度
        planTwoOutInfo.setHindoKnj(cks22OutInfo.getHindoKnj());
        // 期間
        planTwoOutInfo.setKikanKnj(cks22OutInfo.getKikanKnj());
        // 通番
        planTwoOutInfo.setSeq(CommonDtoUtil.objValToString(cks22OutInfo.getSeq()));
        // 課題番号
        planTwoOutInfo.setKadaiNo(CommonDtoUtil.objValToString(cks22OutInfo.getKadaiNo()));
        // 介護番号
        planTwoOutInfo.setKaigoNo(CommonDtoUtil.objValToString(cks22OutInfo.getKaigoNo()));
        // 長期期間
        planTwoOutInfo.setChoKikanKnj(cks22OutInfo.getChoKikanKnj());
        // 短期期間
        planTwoOutInfo.setTanKikanKnj(cks22OutInfo.getTanKikanKnj());
        // 給付対象
        planTwoOutInfo.setHkyuKbn(CommonDtoUtil.objValToString(cks22OutInfo.getHkyuKbn()));
        // サービス事業者ＣＤ
        planTwoOutInfo.setJigyouId(CommonDtoUtil.objValToString(cks22OutInfo.getJigyouId()));
        // サービス事業者名
        planTwoOutInfo.setJigyoNameKnj(cks22OutInfo.getJigyoNameKnj());
        // 給付文字
        planTwoOutInfo.setHkyuKnj(cks22OutInfo.getHkyuKnj());
        // 長期期間開始日
        planTwoOutInfo.setChoSYmd(cks22OutInfo.getChoSYmd());
        // 長期期間終了日
        planTwoOutInfo.setChoEYmd(cks22OutInfo.getChoEYmd());
        // 短期期間開始日
        planTwoOutInfo.setTanSYmd(cks22OutInfo.getTanSYmd());
        // 短期期間終了日
        planTwoOutInfo.setTanEYmd(cks22OutInfo.getTanEYmd());
        // 期間開始日
        planTwoOutInfo.setKikanSYmd(cks22OutInfo.getKikanSYmd());
        // 期間終了日
        planTwoOutInfo.setKikanEYmd(cks22OutInfo.getKikanEYmd());
        return planTwoOutInfo;
    }

    /**
     * 保険サービスリストの編集
     *
     * @param cks211OutInfo 保険サービス情報
     * @return 保険サービスリスト
     */
    private Gui01014Hoken editInsuranceInfo(CpnTucCks211InfoOutEntity cks211OutInfo) {
        Gui01014Hoken insuranceInfo = new Gui01014Hoken();
        // カウンター
        insuranceInfo.setKs211Id(CommonDtoUtil.objValToString(cks211OutInfo.getKs211Id()));
        // 計画書（２）ID
        insuranceInfo.setKs21Id(CommonDtoUtil.objValToString(cks211OutInfo.getKs21Id()));
        // 曜日
        insuranceInfo.setYoubi(cks211OutInfo.getYoubi());
        // 週単位以外のｻｰﾋﾞｽ区分
        insuranceInfo.setIgaiKbn(CommonDtoUtil.objValToString(cks211OutInfo.getIgaiKbn()));
        // 週単位以外のｻｰﾋﾞｽ（日付指定）
        insuranceInfo.setIgaiDate(cks211OutInfo.getIgaiDate());
        // 週単位以外のｻｰﾋﾞｽ（曜日指定）
        insuranceInfo.setIgaiWeek(cks211OutInfo.getIgaiWeek());
        // 居宅：開始時間
        insuranceInfo.setKaishiJikan(cks211OutInfo.getKaishiJikan());
        // 居宅：終了時間
        insuranceInfo.setShuuryouJikan(cks211OutInfo.getShuuryouJikan());
        // 居宅：サービス種類
        insuranceInfo.setSvShuruiCd(cks211OutInfo.getSvShuruiCd());
        // 居宅：サービス項目（台帳）
        insuranceInfo.setSvItemCd(CommonDtoUtil.objValToString(cks211OutInfo.getSvItemCd()));
        // 居宅：サービス事業者CD
        insuranceInfo.setSvJigyoId(CommonDtoUtil.objValToString(cks211OutInfo.getSvJigyoId()));
        // 居宅：福祉用具貸与単位
        insuranceInfo.setTanka(CommonDtoUtil.objValToString(cks211OutInfo.getTanka()));
        // 福祉用具貸与商品コード
        insuranceInfo.setFygId(CommonDtoUtil.objValToString(cks211OutInfo.getFygId()));
        // 合成識別区分
        insuranceInfo.setGouseiSikKbn(cks211OutInfo.getGouseiSikKbn());
        // 加算フラグ
        insuranceInfo.setKasanFlg(CommonDtoUtil.objValToString(cks211OutInfo.getKasanFlg()));
        // 親レコード番号
        insuranceInfo.setOyaLineNo(CommonDtoUtil.objValToString(cks211OutInfo.getOyaLineNo()));
        return insuranceInfo;
    }

    /**
     * 月日指定リストの編集
     *
     * @param cks212OutInfo 月日指定情報
     * @return 月日指定リスト
     */
    private Gui01014Tukihi editTulohInfo(Keikaku2TukihiInfoOutEntity cks212OutInfo) {
        Gui01014Tukihi tukihInfo = new Gui01014Tukihi();
        // カウンター
        tukihInfo.setKs212Id(CommonDtoUtil.objValToString(cks212OutInfo.getKs212Id()));
        // 計画書（２）ID
        tukihInfo.setKs21Id(CommonDtoUtil.objValToString(cks212OutInfo.getKs21Id()));
        // 月日指定開始日
        tukihInfo.setStartYmd(cks212OutInfo.getStartYmd());
        // 月日指定終了日
        tukihInfo.setEndYmd(cks212OutInfo.getEndYmd());
        return tukihInfo;
    }

    /**
     * サービス曜日リストの編集
     *
     * @param cks221OutInfo サービス曜日情報
     * @return サービス曜日リスト
     */
    private Gui01014Yobi editYobiInfo(CpnTucCks221InfoOutEntity cks221OutInfo) {
        Gui01014Yobi yobiInfo = new Gui01014Yobi();
        // カウンター
        yobiInfo.setKs221Id(CommonDtoUtil.objValToString(cks221OutInfo.getKs221Id()));
        // 計画書（２）行データID
        yobiInfo.setKs22Id(CommonDtoUtil.objValToString(cks221OutInfo.getKs22Id()));
        // 計画書（２）ID
        yobiInfo.setKs21Id(CommonDtoUtil.objValToString(cks221OutInfo.getKs21Id()));
        // 曜日
        yobiInfo.setYoubi(cks221OutInfo.getYoubi());
        // 週単位以外のｻｰﾋﾞｽ区分
        yobiInfo.setIgaiKbn(CommonDtoUtil.objValToString(cks221OutInfo.getIgaiKbn()));
        // 週単位以外のｻｰﾋﾞｽ（日付指定）
        yobiInfo.setIgaiDate(cks221OutInfo.getIgaiDate());
        // 週単位以外のｻｰﾋﾞｽ（曜日指定）
        yobiInfo.setIgaiWeek(cks221OutInfo.getIgaiWeek());
        // 居宅：開始時間
        yobiInfo.setKaishiJikan(cks221OutInfo.getKaishiJikan());
        // 居宅：終了時間
        yobiInfo.setShuuryouJikan(cks221OutInfo.getShuuryouJikan());
        // 居宅：サービス種類
        yobiInfo.setSvShuruiCd(cks221OutInfo.getSvShuruiCd());
        // 居宅：サービス項目（台帳）
        yobiInfo.setSvItemCd(CommonDtoUtil.objValToString(cks221OutInfo.getSvItemCd()));
        // 居宅：サービス事業者CD
        yobiInfo.setSvJigyoId(CommonDtoUtil.objValToString(cks221OutInfo.getSvJigyoId()));
        // 居宅：福祉用具貸与単位
        yobiInfo.setTanka(CommonDtoUtil.objValToString(cks221OutInfo.getTanka()));
        // 福祉用具貸与商品コード
        yobiInfo.setFygId(CommonDtoUtil.objValToString(cks221OutInfo.getFygId()));
        // 合成識別区分
        yobiInfo.setGouseiSikKbn(cks221OutInfo.getGouseiSikKbn());
        return yobiInfo;
    }

    /**
     * 担当者リストの編集
     *
     * @param cks222OutInfo 担当者情報
     * @return 担当者リスト
     */
    private Gui01014Tanto editGuiTantoInfo(CpnTucCks222InfoOutEntity cks222OutInfo) {
        Gui01014Tanto guiTantoInfo = new Gui01014Tanto();
        // カウンター
        guiTantoInfo.setKs222Id(CommonDtoUtil.objValToString(cks222OutInfo.getKs222Id()));
        // 計画書（２）行データID
        guiTantoInfo.setKs22Id(CommonDtoUtil.objValToString(cks222OutInfo.getKs22Id()));
        // 計画書（２）ID
        guiTantoInfo.setKs21Id(CommonDtoUtil.objValToString(cks222OutInfo.getKs21Id()));
        // 施設：職種（担当者）
        guiTantoInfo.setShokushuId(CommonDtoUtil.objValToString(cks222OutInfo.getShokushuId()));
        return guiTantoInfo;
    }
}
