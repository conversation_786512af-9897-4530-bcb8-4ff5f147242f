package jp.ndsoft.carebase.cmn.api.report.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.MonitoringEvaluationReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.MonitoringEvaluationReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.logic.MonitoringEvaluationReportLogic;
import jp.ndsoft.carebase.cmn.api.report.model.MonitoringEvaluationReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.xml.JRXmlLoader;

/**
 * @since 2025.08.07
 * <AUTHOR> 張婧
 *         U01020_モニタリング・評価表（H21改訂版） 帳票出力
 */
@Service("MonitoringEvaluationReport")
public class MonitoringEvaluationReportService
        extends
        PdfReportServiceImpl<MonitoringEvaluationReportParameterModel, MonitoringEvaluationReportServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    /** モニタリング・評価表（H21改訂版） 情報取得 */
    @Autowired
    private MonitoringEvaluationReportLogic reportLogic;

    /** 承認欄path */
    private String subReportPath;

    /** 承認欄 */
    private JRBeanCollectionDataSource subReportDataDs;

    /** 承認欄設定クラス */
    @Autowired
    private ShoninSetReportService shoninSetReportService;

    /**
     * 帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(MonitoringEvaluationReportParameterModel model,
            MonitoringEvaluationReportServiceOutDto outDto) throws Exception {
        LOG.info(Constants.START);
        // 帳票用データ詳細
        MonitoringEvaluationReportServiceInDto infoInDto = (MonitoringEvaluationReportServiceInDto) this.reportLogic
                .getMonitoringEvaluationReportParameters(model);
        // ノート情報格納配列
        List<MonitoringEvaluationReportServiceInDto> monitoringEvaluationReportServiceInDto = new ArrayList<MonitoringEvaluationReportServiceInDto>();

        // リクエストパラメータ.データ.印刷オプション.承認欄を印刷するフラグ>0の場合、承認欄設定の共通関数で承認欄を設定する
        if (!CommonConstants.STR_0.equals(model.getPrintOption().getShoninFlg())) {
            infoInDto.setSubReportPath(subReportPath);
            infoInDto.setSubReportDataDs(subReportDataDs);
        }
        monitoringEvaluationReportServiceInDto.add(infoInDto);

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(monitoringEvaluationReportServiceInDto);
        infoInDto.setDataSource(dataSource);

        LOG.info(Constants.END);
        return infoInDto;
    }

    /**
     * 帳票出力
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final MonitoringEvaluationReportParameterModel model,
            final MonitoringEvaluationReportServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);

        // 帳票テンプレートのロード
        String sourceFilePath = ReportUtil.getReportJrxmlFile(getFwProps(),
                ReportConstants.JRXML_U01020_MONITORINGEVALUATION);
        JasperDesign jasperDesign = JRXmlLoader.load(sourceFilePath);

        HashMap<String, Object> shonin = shoninSetReportService.getShoninSetReport(jasperDesign, ReportConstants.INT_1,
                model.getJigyoInfo().getSvJigyoId(), CommonConstants.BLANK_STRING);
        subReportPath = (String) shonin.get(ReportConstants.SUBREPORTPATH);
        subReportDataDs = (JRBeanCollectionDataSource) shonin.get(ReportConstants.SUBREPORTDATADS);

        // 帳票に出力するデータ群の取得
        final MonitoringEvaluationReportServiceInDto reportParameter = (MonitoringEvaluationReportServiceInDto) getReportParameters(
                model, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(getFwProps(),
                        ReportConstants.JRXML_CAPSUMMARY_SHEET)));

        // コンパイル
        final JasperReport jasperFile = JasperCompileManager.compileReport(is);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile,
                reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(model, jasperPrint);

        /*
         * =============== 6.レスポンスを返却===============
         * 
         */
        super.setFilepath(model, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }
}
