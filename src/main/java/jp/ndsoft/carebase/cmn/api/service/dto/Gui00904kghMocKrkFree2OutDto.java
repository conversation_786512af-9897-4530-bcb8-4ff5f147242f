package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.05.12
 * <AUTHOR>
 * @description GUI00904_課題立案様式設定マスタ参照DTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Gui00904kghMocKrkFree2OutDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    // 項目名
    @NotEmpty
    private String nameKnj;
    // 入力値変更フラグ
    @NotEmpty
    private String inputKbnUpdateFlag;
    // 入力方法
    @NotEmpty
    private String inputKbn;
    // 連動区分
    @NotEmpty
    private String rendouKbn;
    // 文字数
    @NotEmpty
    private String widthCnt;
}
