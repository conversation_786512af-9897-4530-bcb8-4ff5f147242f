package jp.ndsoft.carebase.cmn.api.report.service;

import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.invoke.MethodHandles;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.chrono.JapaneseDate;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSet;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.ReportU01010ContentsListServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.SpecialistPassageByDateReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.SpecialistPassageByDateReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.model.SpecialistPassageByDateReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSelUserOnce0ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSelUserOnce0OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnKeikaK202104LeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnKeikaK202104LeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinNameNumberByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinNameNumberOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ShoTucCasebikoSelectMapper;
import jp.ndsoft.carebase.report.util.JasperUtil;

/**
 * @since 2025.07.10
 * <AUTHOR> BUI VIET ANH
 * @implNote U01010_介護支援経過日別一覧
 */
@Service("SpecialistPassageByDateReportService")
public class SpecialistPassageByDateReportService extends
        PdfReportServiceImpl<SpecialistPassageByDateReportParameterModel, SpecialistPassageByDateReportServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 29-17 備考情報取得DAO */
    @Autowired
    private ShoTucCasebikoSelectMapper shoTucCasebikoSelectMapper;

    /** 利用者の情報取得情報DAO */
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;

    /** 職員基本情報取得DAO */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    private static final String SHISETSU = "施設介護支援経過";
    private static final String KYOTAKU = "居宅介護支援経過";

    /**
     * 性別コード（0: 男, 1: 女）に対応する文字列を返す
     *
     * @param genderCode 性別コード（0 = 男, 1 = 女）
     * @return 性別文字列（"男" または "女"）
     */
    public static String getGenderString(Short genderCode) {
        if (CommonDtoUtil.shortValToInt(genderCode).equals(CommonConstants.GENDER_CODE_MALE)) {
            return CommonConstants.STR_MALE;
        } else if (CommonDtoUtil.shortValToInt(genderCode).equals(CommonConstants.GENDER_CODE_FEMALE)) {
            return CommonConstants.STR_FEMALE;
        } else {
            return CommonConstants.EMPTY_STRING;
        }
    }

    @Override
    protected Object getReportParameters(SpecialistPassageByDateReportParameterModel model,
            SpecialistPassageByDateReportServiceOutDto outDto) throws Exception {
        LOG.info(Constants.START);
        // 印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));
        // U01010_介護支援経過の入力Dto
        SpecialistPassageByDateReportServiceInDto infoInDto = new SpecialistPassageByDateReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. 備考情報取得の情報を取得===============
         * 
         */
        // 2.1. DAOから備考情報取得の情報を収集する。
        // 備考情報取得の結果を保存するリスト（casebikoList）を作成する。
        List<KghCpnKeikaK202104LeOutEntity> casebikoList = new ArrayList<>();

        // リクエストパラメータの利用者IDごとにループし、各利用者IDに対して備考情報取得を実行し、情報を取得する。
        for (String userId : model.getUserIdList()) {
            KghCpnKeikaK202104LeByCriteriaInEntity kghCpnKeikaK202104LeByCriteria = new KghCpnKeikaK202104LeByCriteriaInEntity();
            kghCpnKeikaK202104LeByCriteria.setJid(CommonDtoUtil.strValToInt(model.getSvJigyoId()));
            kghCpnKeikaK202104LeByCriteria.setStYymmdd(model.getYymmYmd());
            kghCpnKeikaK202104LeByCriteria.setEdYymmdd(model.getYymmYmd());
            kghCpnKeikaK202104LeByCriteria.setUid(CommonDtoUtil.strValToInt(userId));

            List<KghCpnKeikaK202104LeOutEntity> kghCpnKeikaK202104LeList = shoTucCasebikoSelectMapper
                    .findKghCpnKeikaK202104LeByCriteria(kghCpnKeikaK202104LeByCriteria);

            // 取得した結果を上記のリストに追加する。
            casebikoList.addAll(kghCpnKeikaK202104LeList);
        }

        /*
         * ===============3. 利用者基本（１－６）情報の取得===============
         * 
         */
        // 3.1. 下記の利用者基本情報取得のDAOを利用し、利用者基本（１－６）情報を取得する。
        KghCmnSelUserOnce0ByCriteriaInEntity kghKrkYakurekiUserByCriteria = new
        KghCmnSelUserOnce0ByCriteriaInEntity();
        kghKrkYakurekiUserByCriteria
        .setAlUuuList(model.getUserIdList().stream().map(CommonDtoUtil::strValToInt).toList());

        List<KghCmnSelUserOnce0OutEntity> userinfoList = comTucUserSelectMapper
        .findKghCmnSelUserOnce0ByCriteria(kghKrkYakurekiUserByCriteria);

        /*
         * ===============4. 職員基本情報取得の情報を収集する===============
         * 
         */
        List<ShokuinNameNumberOutEntity> shokuinNameNumberList = comMscShokuinSelectMapper
                .findShokuinNameNumberByCriteria(new ShokuinNameNumberByCriteriaInEntity());

        /*
         * ===============5. 指定日の空欄表示処理===============
         * 
         */
        // 5.1. 下記共通関数で利用し、指定日の空欄表示を取得する。
        // 指定日
        infoInDto.setDesignationDate(getShiTeiDate(
                printSet.getShiTeiKubun(),
                printSet.getShiTeiDate(),
                model.getSystemDate()));

        /*
         * ===============パラメータを設定する手順===============
         * 
         */
        // ・リクエストパラメータ.初期設定マスタ = "1" の場合、title =「施設介護支援経過」
        if (model.getCksFlg().equals(CommonConstants.CKS_FLG_FACILITY)) {
            // 帳票タイトル
            infoInDto.setTitle(SHISETSU);
            // ・リクエストパラメータ.初期設定マスタ == 2 の場合は true、それ以外は false を返す。
            // 項目の表示フラグ
            infoInDto.setItaku_flag(false);
        }
        // ・リクエストパラメータ.初期設定マスタ = "2" の場合、title =「居宅介護支援経過」
        else {
            // 帳票タイトル
            infoInDto.setTitle(KYOTAKU);
            // ・リクエストパラメータ.初期設定マスタ == 2 の場合は true、それ以外は false を返す。
            // 項目の表示フラグ
            infoInDto.setItaku_flag(true);
        }

        // 事業者名
        infoInDto.setJigyo_knj(model.getSvJigyoKnj());

        // 記録日
        infoInDto.setYymm_ymd(getLocalDateToJapaneseDateTimeFormat(model.getYymmYmd()));

        // 支援結果記録一覧
        List<ReportU01010ContentsListServiceInDto> contentList = new ArrayList<>();

        for (KghCpnKeikaK202104LeOutEntity casebikoItem : casebikoList) {
        ReportU01010ContentsListServiceInDto contentsItem = new
        ReportU01010ContentsListServiceInDto();

        // ユーザー情報を userinfoList から検索する（userid で一致）
        KghCmnSelUserOnce0OutEntity userInfo = userinfoList.stream()
        .filter(u -> u.getId() != null && u.getId().equals(casebikoItem.getUserid()))
        .findFirst()
        .orElse(null);

        // 利用者番号
        contentsItem.setUserid(userInfo.getId());
        // 利用者性別
        contentsItem.setSex(getGenderString(userInfo.getSex().shortValue()));
        // 利用者氏名
        contentsItem
        .setName_knj(userInfo.getName1Knj() + CommonConstants.BLANK_SPACE +
        userInfo.getName2Knj());
        // 内容
        contentsItem.setNaiyo_knj(
        StringUtils.isNotBlank(casebikoItem.getCaseKnj()) ? casebikoItem.getCaseKnj()
        : CommonConstants.BLANK_STRING);

        // 職員情報を検索する
        ShokuinNameNumberOutEntity staff = shokuinNameNumberList.stream()
        .filter(s -> s.getChkShokuId().equals(casebikoItem.getStaffid()))
        .findFirst()
        .orElse(null);
        // 職員名を結合する（氏名の間に全角スペースを挿入）
        String staffName = staff.getShokuin1Knj() + CommonConstants.BLANK_SPACE
        + staff.getShokuin2Knj();

        // 記録者
        contentsItem.setRyaku_knj(staffName);
        // 項目
        contentsItem.setKoumoku_knj(
        StringUtils.isNotBlank(casebikoItem.getKoumokuKnj()) ?
        casebikoItem.getKoumokuKnj()
        : CommonConstants.BLANK_STRING);
        // 記録ー訪問
        contentsItem.setHoumon_flg(
        casebikoItem.getHoumonFlg() != null && casebikoItem.getHoumonFlg() !=
        CommonConstants.INT_0
        ? CommonConstants.MARU
        : CommonConstants.BLANK_STRING);
        // 記録ー計画書１
        contentsItem.setKkak1_flg(
        casebikoItem.getKkak1Flg() != null && casebikoItem.getKkak1Flg() !=
        CommonConstants.INT_0
        ? CommonConstants.MARU
        : CommonConstants.BLANK_STRING);
        // 記録ー計画書２
        contentsItem.setKkak2_flg(
        casebikoItem.getKkak2Flg() != null && casebikoItem.getKkak2Flg() !=
        CommonConstants.INT_0
        ? CommonConstants.MARU
        : CommonConstants.BLANK_STRING);
        // 記録ー週刊計画
        contentsItem.setWeek_flg(
        casebikoItem.getWeekFlg() != null && casebikoItem.getWeekFlg() !=
        CommonConstants.INT_0
        ? CommonConstants.MARU
        : CommonConstants.BLANK_STRING);
        // 記録ー利用表
        contentsItem.setRiyo_flg(
        casebikoItem.getRiyoFlg() != null && casebikoItem.getRiyoFlg() !=
        CommonConstants.INT_0
        ? CommonConstants.MARU
        : CommonConstants.BLANK_STRING);
        // 項目の表示フラグ
        contentsItem.setItaku_flag(infoInDto.getItaku_flag());
        // 記録ー計画対象年月
        contentsItem.setTaisho_ym(
        StringUtils.isNotBlank(casebikoItem.getTaishoYm())
        ? convertYearMonthToJapaneseFormat(casebikoItem.getTaishoYm())
        : CommonConstants.BLANK_STRING);

        contentList.add(contentsItem);
        }

        JRBeanCollectionDataSource contentsList = new JRBeanCollectionDataSource(contentList);
        // 支援結果記録一覧
        infoInDto.setContentsList(contentsList);

        /*
         * ===============6. 上記処理で取得した結果レスポンスを返却する。===============
         * 
         */
        List<SpecialistPassageByDateReportServiceInDto> dataList = new ArrayList<>();
        dataList.add(infoInDto);

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(dataList);
        infoInDto.setDataSource(dataSource);

        LOG.info(Constants.END);

        return infoInDto;
    }

    /**
     * 指定日を取得する
     * 
     * @param shiTeiKubun 指定日印刷区分
     * @param shiTeiDate  西暦日付
     * @param systemDate  システム日付
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private String getShiTeiDate(String shiTeiKubun, String shiTeiDate, String systemDate) {
        String blankDate = CommonConstants.BLANK_STRING;
        switch (shiTeiKubun) {
            case ReportConstants.PRINTDATE_KBN_SHINAI:
                // 指定日印刷区分 = 1 の場合、指定日=""
                blankDate = CommonConstants.BLANK_STRING;
                break;
            case ReportConstants.PRINTDATE_KBN_PRINT:
                // 指定日印刷区分 = 2 の場合
                // 西暦日付を和暦日付に変換する
                blankDate = getLocalDateToJapaneseDateTimeFormat(shiTeiDate);
                break;
            case ReportConstants.PRINTDATE_KBN_BLANKDATE:
                // 指定日印刷区分 = 3 の場合
                // 指定日の空欄表示処理
                DateTimeFormatter inputFormatter = DateTimeFormatter
                        .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
                DateTimeFormatter outputFormatter = DateTimeFormatter
                        .ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
                LocalDateTime dateTime = LocalDateTime.parse(systemDate, inputFormatter);
                blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
                break;
            default:
                blankDate = CommonConstants.BLANK_STRING;
                break;
        }

        return blankDate;
    }

    /**
     * 西暦日付を和暦日付に変換する
     * 
     * @param japaneseDateStr 西暦日付文字列
     * @return 和暦日付文字列
     */
    public static String getLocalDateToJapaneseDateTimeFormat(String westernDateStr) {
        if (Strings.isNullOrEmpty(westernDateStr)) {
            return "";
        }

        String formatDateString = ReportUtil.convertDateFormat(westernDateStr);

        // 日付文字列を LocalDate に変換
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD);
        LocalDate date = LocalDate.parse(formatDateString, inputFormatter);
        JapaneseDate japaneseDate = JapaneseDate.from(date);

        // 元号（例: 令和、平成など）の名前を日本語で取得
        String eraName = japaneseDate.getEra().getDisplayName(TextStyle.FULL, Locale.JAPAN);

        // 和暦の年、月、日を個別に取得
        int eraYear = japaneseDate.get(ChronoField.YEAR_OF_ERA);
        int month = date.getMonthValue();
        int day = date.getDayOfMonth();

        return String.format("%s%2d年%2d月%2d日", eraName, eraYear, month, day);

    }

    /**
     * yyyy/MM 形式の文字列を「和暦 年 月分」形式に変換する
     *
     * @param yearMonthStr 西暦年月文字列（例: "2025/07"）
     * @return 和暦形式の文字列（例: "令和 7年 7月分"）
     */
    public static String convertYearMonthToJapaneseFormat(String yearMonthStr) {
        if (yearMonthStr == null || !yearMonthStr.matches("\\d{4}/\\d{1,2}")) {
            return "";
        }

        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy/M");
        YearMonth ym = YearMonth.parse(yearMonthStr, inputFormatter);
        LocalDate date = ym.atDay(1); // 月初日に変換

        JapaneseDate japaneseDate = JapaneseDate.from(date);
        String eraName = japaneseDate.getEra().getDisplayName(TextStyle.FULL, Locale.JAPAN);
        int eraYear = japaneseDate.get(ChronoField.YEAR_OF_ERA);
        int month = date.getMonthValue();

        return String.format("%s%2d年%2d月分", eraName, eraYear, month);
    }

    /**
     * 帳票出力
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final SpecialistPassageByDateReportParameterModel model,
            final SpecialistPassageByDateReportServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final SpecialistPassageByDateReportServiceInDto reportParameter = (SpecialistPassageByDateReportServiceInDto) getReportParameters(
                model, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(getFwProps(),
                        ReportConstants.JRXML_SPECIALIST_PASSAGE_BY_DATE)));

        // コンパイル
        final JasperReport jasperFile = JasperCompileManager.compileReport(is);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile,
                reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(model, jasperPrint);

        super.setFilepath(model, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }
}
