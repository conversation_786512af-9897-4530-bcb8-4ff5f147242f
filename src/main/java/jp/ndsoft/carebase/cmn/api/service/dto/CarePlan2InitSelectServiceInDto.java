package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * GUI01014_計画書（2）初期情報取得サービス.の入力Dto
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class CarePlan2InitSelectServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 事業者ID */
    @NotEmpty
    private String svJigyoId;
    /** 施設ID */
    @NotEmpty
    private String shisetuId;
    /** 利用者ID */
    @NotEmpty
    private String userId;
    /** 種別ID */
    @NotEmpty
    private String syubetsuId;
    /** 計画書様式 */
    @NotEmpty
    private String cksflg;
    /** 法人ID */
    @NotEmpty
    private String houjinId;
    /** サービス事業者IDリスト */
    @NotEmpty
    private List<String> svJigyoIdList;
    /** 事業者CDリスト */
    @NotEmpty
    private List<String> svJigyoCdList;
    /** 記録との連携 */
    @NotEmpty
    private String kirokuRenkeiFlg;
}
