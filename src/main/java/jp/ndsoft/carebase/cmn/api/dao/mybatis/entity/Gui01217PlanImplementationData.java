package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.carebase.framework.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GUI01217_計画実施‐画面に実績情報一覧の情報を保存
 * 
 * @description
 *              画面に実績情報一覧の情報を保存取得
 *              画面に実績情報一覧の情報を保存のエンティティ
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Gui01217PlanImplementationData implements Serializable, IEntity {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** データID */
    @NotEmpty
    private String kjisshi2Id;

    /** ヘッダID */
    @NotEmpty
    private String kjisshi1Id;

    /** dmy実施チェック */
    private String dmyJisshiChk;

    /** 区分コード */
    @NotEmpty
    private String kbnCd;

    /** 更新区分 */
    private String updateKbn;
}
