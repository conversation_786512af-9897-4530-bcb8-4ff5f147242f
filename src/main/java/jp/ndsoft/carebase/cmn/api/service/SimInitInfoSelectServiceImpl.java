package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.KghBase01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmn03gFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnRiyou01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.FygJohoNextOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.TekiyoJigyoInfoInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.TekiyoJigyoInfoOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Jigyo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Kohi;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149PlanOv30;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Riyou;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Riyou701Info;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149RiyouBeppyo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149RiyouSvItemNmInInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149RiyouSvItemNmOutInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Riyoursha;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149RiyouDataBackgroundColorOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Sum;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01213Jigyo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01213RiyourshaData;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01213SvjigyoList;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01213SyuruiGendoData;
import jp.ndsoft.carebase.cmn.api.service.dto.SimInitInfoSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.SimInitInfoSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.UseSlipInitInfoSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3KohiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3KohiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSimuObject1LineByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSimuObject1LineOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSimuObject1List202504ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSimuObject1List202504OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSimuObject1ListByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSimuObject1ListOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSimuObject1OpeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSimuObject1OpeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSimuObject1SyuruiSougouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSimuObject1SyuruiSougouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSvselSelJigyoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSvselSelJigyoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucPlanOv30SimByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucPlanOv30SimOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucPlanSimByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucPlanSimOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnSvselSelJigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanBetu1SimSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanBetu2SimSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanKouhiFutanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanOv30SimSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanRiyouSimSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanSimSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscKaigoHokenjaSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * 
 * @since 2025.07.02
 * <AUTHOR> 王嘉淳
 * @implNote GUI01213_APINo(1240)_シミュレーション初期情報取得
 */
@Service
public class SimInitInfoSelectServiceImpl
        extends SelectServiceImpl<SimInitInfoSelectServiceInDto, SimInitInfoSelectServiceOutDto> {
    // ロガー.
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private UseSlipInitInfoSelectServiceImpl useSlipInitInfoSelectServiceImpl;

    @Autowired
    private KghCmn03gFunc01Logic kghCmn03gFunc01Logic;

    @Autowired
    private KghBase01Logic kghBase01Logic;

    @Autowired
    private KghCmnF01Logic kghCmnF01Logic;

    @Autowired
    private KghCmnRiyou01Logic kghCmnRiyou01Logic;

    /** シミュレーション雛形選択情報の検索処理 */
    @Autowired
    private CmnTucPlanSimSelectMapper cmnTucPlanSimSelectMapper;

    /** 介護保険者マスタ（４－３）を取得する */
    @Autowired
    private ComMscKaigoHokenjaSelectMapper comMscKaigoHokenjaSelectMapper;

    /** 利用票明細（シミュレーション）を検索する */
    @Autowired
    private CmnTucPlanRiyouSimSelectMapper cmnTucPlanRiyouSimSelectMapper;

    /** ケアマネ：利用票：シミュレーション処理 別表 別表明細取得情報 */
    @Autowired
    private CmnTucPlanBetu1SimSelectMapper cmnTucPlanBetu1SimSelectMapper;

    /** 公費集計欄情報の取得 */
    @Autowired
    private CmnTucPlanKouhiFutanSelectMapper cmnTucPlanKouhiFutanSelectMapper;

    /** 事業所リスト情報 */
    @Autowired
    private CmnSvselSelJigyoSelectMapper cmnSvselSelJigyoSelectMapper;

    /** シミュレーション 30日超過取得情報 */
    @Autowired
    private CmnTucPlanOv30SimSelectMapper cmnTucPlanOv30SimSelectMapper;

    /** 種類別限度情報を取得 */
    @Autowired
    private CmnTucPlanBetu2SimSelectMapper cmnTucPlanBetu2SimSelectMapper;

    /**
     * シミュレーション初期情報を取得する
     * 
     * @param inDto SimInitInfoSelectServiceInDto.
     * @return SimInitInfoSelectServiceOutDto
     * @throws Exception Exception
     */
    @Override
    protected SimInitInfoSelectServiceOutDto mainProcess(SimInitInfoSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        SimInitInfoSelectServiceOutDto outDto = new SimInitInfoSelectServiceOutDto();

        // 2.提供年月と提供月（日）の取得
        getTeikyouYyyyMM(inDto, outDto);
        // 3.利用者情報を取得する
        getRiyouInfoFrom1149(inDto, outDto);
        // 4.事業所情報取得を行う
        getSvJigyoInfo(inDto, outDto);
        // 5.利用者情報を取得する。
        // 6.5.1の利用票・別表ヘッダ-sim取得件数>0の場合
        // 7.5.1の利用票・別表ヘッダ-sim取得件数<=0の場合、行追加を行う
        // 8.画面表示の初期化をおこなう
        getRiyouInfoFromMapper(inDto, outDto);
        // 9.利用票：重複するサービスの背景色を赤にする
        setBackGroundColorToRedPrco(inDto, outDto);
        // 10.日付に該当するtermidを取得する
        getTermId(inDto.getAppYm(), outDto);
        // 11.区分支給限度基準額を取得する
        getGendo(inDto.getAppYm(), outDto);
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 2.提供年月と提供月（日）の取得
     * 
     * @param dto SimInitInfoSelectServiceInDto
     * @return SimInitInfoSelectServiceOutDto
     */
    private void getTeikyouYyyyMM(SimInitInfoSelectServiceInDto dto, SimInitInfoSelectServiceOutDto simDto) {
        // 2.1.リクエストパラメータ.提供年月 = nullの場合、提供年月を取得する
        if (StringUtils.isEmpty(dto.getAppYm())) {
            simDto.setTeikyouYm(CommonDtoUtil.objValToString(LocalDate.now()).substring(0, 7));
        }
        // 2.2.リクエストパラメータ.提供年月 <> nullの場合、
        else {
            simDto.setTeikyouYm(dto.getAppYm());
        }
        // 2.3.レスポンスパラメータ.提供月（日） = "01"
        simDto.setTeikyouYmD(CommonConstants.TEIKYOU_YM_D);
    }

    /**
     * 3.利用者情報を取得する
     * 
     * @param dto SimInitInfoSelectServiceInDto
     * @return SimInitInfoSelectServiceOutDto
     */
    private void getRiyouInfoFrom1149(SimInitInfoSelectServiceInDto dto, SimInitInfoSelectServiceOutDto simDto) {
        // リクエストパラメータ
        UseSlipInitInfoSelectServiceInDto selectServiceInDto = new UseSlipInitInfoSelectServiceInDto();
        selectServiceInDto.setUserId(dto.getUserId());
        selectServiceInDto.setSvJigyoId(dto.getDefSvJigyoId());
        Gui01149Riyou701Info riyouInfo = new Gui01149Riyou701Info();
        riyouInfo.setTeikyouYm(simDto.getTeikyouYm());
        riyouInfo.setTeikyouYmD(simDto.getTeikyouYmD());

        // 3.1.利用票共通を呼出前、初期情報をセットする
        useSlipInitInfoSelectServiceImpl.getRiyouProcessObject(selectServiceInDto, riyouInfo);
        simDto.setRiyouProcessList(riyouInfo.getRiyouProcessObject());
    }

    /**
     * 4.事業所情報取得を行う
     * 
     * @param dto SimInitInfoSelectServiceInDto
     * @return SimInitInfoSelectServiceOutDto
     */
    private void getSvJigyoInfo(SimInitInfoSelectServiceInDto dto, SimInitInfoSelectServiceOutDto simDto) {
        // INPUT情報
        TekiyoJigyoInfoInDto tekiyoJigyoInfoInDto = new TekiyoJigyoInfoInDto();
        List<Integer> jigyoIdList = new ArrayList<>();
        for (Gui01213Jigyo jigyo : dto.getJigyoList()) {
            jigyoIdList.add(CommonDtoUtil.strValToInt(jigyo.getJigyoId()));
        }
        // 事業者グループ適用ID
        tekiyoJigyoInfoInDto.setTekiyoGroupId(CommonDtoUtil.strValToInt(dto.getTekiyoFlg()));
        // 適用事業所ＩＤリスト
        tekiyoJigyoInfoInDto.setSvJigyoId(jigyoIdList);
        // 事業所情報取得
        List<TekiyoJigyoInfoOutDto> tekiyoJigyoInfoOutList = kghBase01Logic.getTekiyoJigyoInfo(tekiyoJigyoInfoInDto);
        List<Gui01149Jigyo> jigyoList = new ArrayList<>();
        for (TekiyoJigyoInfoOutDto tekiyoJigyoInfoOut : tekiyoJigyoInfoOutList) {
            Gui01149Jigyo gui01149Jigyo = new Gui01149Jigyo();
            gui01149Jigyo.setHoujinId(CommonDtoUtil.objValToString(tekiyoJigyoInfoOut.getHoujinId()));
            gui01149Jigyo.setJigyoNameKnj(tekiyoJigyoInfoOut.getJigyoNameKnj());
            gui01149Jigyo.setShisetuId(CommonDtoUtil.objValToString(tekiyoJigyoInfoOut.getShisetuId()));
            gui01149Jigyo.setSvJigyoId(CommonDtoUtil.objValToString(tekiyoJigyoInfoOut.getSvJigyoId()));
            gui01149Jigyo.setTekiyoFlg(tekiyoJigyoInfoOut.getTekiyoFlg());
            jigyoList.add(gui01149Jigyo);
        }
        simDto.setJigyoList(jigyoList);
    }

    /**
     * 5.利用者情報を取得する。
     * 
     * @param dto SimInitInfoSelectServiceInDto
     * @return SimInitInfoSelectServiceOutDto
     * @throws Exception
     */
    private void getRiyouInfoFromMapper(SimInitInfoSelectServiceInDto dto, SimInitInfoSelectServiceOutDto simDto)
            throws Exception {
        // 5.1.利用票・別表ヘッダ-simを取得する。
        KghCmnTucPlanSimByCriteriaInEntity kghCmnTucPlanSimByCriteriaInEntity = new KghCmnTucPlanSimByCriteriaInEntity();
        kghCmnTucPlanSimByCriteriaInEntity.setAlShienId(CommonDtoUtil.strValToInt(dto.getDefSvJigyoId()));
        kghCmnTucPlanSimByCriteriaInEntity.setAlUserid(CommonDtoUtil.strValToInt(dto.getUserId()));
        kghCmnTucPlanSimByCriteriaInEntity.setAsYymYm(dto.getAppYm());
        List<KghCmnTucPlanSimOutEntity> simOutList = cmnTucPlanSimSelectMapper
                .findKghCmnTucPlanSimByCriteria(kghCmnTucPlanSimByCriteriaInEntity);
        // 利用者情報
        Gui01213RiyourshaData gui01213RiyourshaData = new Gui01213RiyourshaData();
        List<Gui01213RiyourshaData> riyourShaDataList = new ArrayList<>();
        // 5.2.上記利用票・別表ヘッダ-sim取得件数>0の場合
        if (CollectionUtils.isNotEmpty(simOutList)) {
            // 利用票・別表ヘッダ-simの1件目データ
            KghCmnTucPlanSimOutEntity cmnTucPlanSimOut1Entity = simOutList.get(0);
            // 5.2.1.利用票・別表ヘッダ-simの1件目.作成日がnullの場合
            if (StringUtils.isEmpty(cmnTucPlanSimOut1Entity.getCreateYmd())) {
                gui01213RiyourshaData.setCreateYmd(CommonConstants.BLANK_STRING);
                gui01213RiyourshaData.setTZengetu(CommonConstants.STR_0);
                gui01213RiyourshaData.setTTougetu(CommonConstants.STR_0);
                gui01213RiyourshaData.setTRuiseki(CommonConstants.STR_0);
                gui01213RiyourshaData.setOv30Zengetu(CommonConstants.STR_0);
                gui01213RiyourshaData.setYoteiZumiFlg(CommonConstants.STR_0);
                gui01213RiyourshaData.setJissekiZumiFlg(CommonConstants.STR_0);
                gui01213RiyourshaData.setKakuteiZumiFlg(CommonConstants.STR_0);
            }
            // 5.2.2.上記以外場合
            else {
                gui01213RiyourshaData.setCreateYmd(cmnTucPlanSimOut1Entity.getCreateYmd());
                gui01213RiyourshaData.setTZengetu(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getTZengetu()));
                gui01213RiyourshaData.setTTougetu(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getTTougetu()));
                gui01213RiyourshaData.setTRuiseki(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getTRuiseki()));
                gui01213RiyourshaData
                        .setOv30Zengetu(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getOv30Zengetu()));
                gui01213RiyourshaData
                        .setYoteiZumiFlg(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getYoteiZumiFlg()));
                gui01213RiyourshaData
                        .setJissekiZumiFlg(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getJissekiZumiFlg()));
                gui01213RiyourshaData
                        .setKakuteiZumiFlg(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getKakuteiZumiFlg()));
            }
            // 5.2.3.介護保険者マスタ情報を取得する。
            gui01213RiyourshaData
                    .setCmnTucPlanKHokenCdHidden(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getKHokenCd()));
            // ケアマネ：利用票：シミュレーション処理 画面機能UO取得情報
            KghCmnSimuObject1OpeByCriteriaInEntity kghCmnSimuObject1OpeByCriteriaInEntity = new KghCmnSimuObject1OpeByCriteriaInEntity();
            kghCmnSimuObject1OpeByCriteriaInEntity
                    .setLlKHokenCd(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getKHokenCd()));
            List<KghCmnSimuObject1OpeOutEntity> comSimuObject1OpeOutList = comMscKaigoHokenjaSelectMapper
                    .findKghCmnSimuObject1OpeByCriteria(kghCmnSimuObject1OpeByCriteriaInEntity);
            // 5.2.4.上記介護保険者マスタ情報取得件数=0の場合
            if (CollectionUtils.isEmpty(comSimuObject1OpeOutList)) {
                gui01213RiyourshaData.setDmyhhokenno(CommonConstants.BLANK_STRING);
                gui01213RiyourshaData.setDmykhokenknj(CommonConstants.BLANK_STRING);
            }
            if (CollectionUtils.isNotEmpty(comSimuObject1OpeOutList)) {
                gui01213RiyourshaData.setHHokenNo(cmnTucPlanSimOut1Entity.getHHokenNo());
                gui01213RiyourshaData.setCmnTucPlanHHokenNoHidden(cmnTucPlanSimOut1Entity.getHHokenNo());
                gui01213RiyourshaData.setYokaiKbn(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getYokaiKbn()));
                gui01213RiyourshaData
                        .setDmyHenKaigodo(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getHenYokaiKbn()));
                gui01213RiyourshaData
                        .setDmyhendate(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getHenYokaiYmd()));
                gui01213RiyourshaData
                        .setGendoGaku(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getGendoGaku()));
                gui01213RiyourshaData.setGendoStartYmd(cmnTucPlanSimOut1Entity.getGendoStartYmd());
                gui01213RiyourshaData.setGendoEndYmd(cmnTucPlanSimOut1Entity.getGendoEndYmd());
                gui01213RiyourshaData.setCreateYmd(cmnTucPlanSimOut1Entity.getCreateYmd());
                gui01213RiyourshaData.setSimTitleKnj(cmnTucPlanSimOut1Entity.getSimTitleKnj());
                gui01213RiyourshaData
                        .setSimWaribikiRitu(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getSimWaribikiRitu()));
            }
        } else {
            // 5.3.上記利用票・別表ヘッダ-simが取得できない場合、台帳で登録された要介護度等を表示
            gui01213RiyourshaData.setYoteiZumiFlg(CommonConstants.STR_0);
            gui01213RiyourshaData.setJissekiZumiFlg(CommonConstants.STR_0);
            gui01213RiyourshaData.setKakuteiZumiFlg(CommonConstants.STR_0);

            gui01213RiyourshaData.setTZengetu(CommonConstants.STR_0);
            gui01213RiyourshaData.setCmnTucPlanKHokenCdHidden(simDto.getRiyouProcessList().get(0).getKHokenCd());
            gui01213RiyourshaData.setDmyhhokenno(simDto.getRiyouProcessList().get(0).getKHokenNo());
            gui01213RiyourshaData.setDmykhokenknj(simDto.getRiyouProcessList().get(0).getKHokenKnj());
            gui01213RiyourshaData.setHHokenNo(simDto.getRiyouProcessList().get(0).getHHokenNo());
            gui01213RiyourshaData.setCmnTucPlanHHokenNoHidden(simDto.getRiyouProcessList().get(0).getHHokenNo());

            gui01213RiyourshaData.setYokaiKbn(simDto.getRiyouProcessList().get(0).getYokaiKbn());
            gui01213RiyourshaData.setDmyHenKaigodo(simDto.getRiyouProcessList().get(0).getYokaiKbnN());
            gui01213RiyourshaData.setDmyhendate(simDto.getRiyouProcessList().get(0).getHenkouYmd());

            gui01213RiyourshaData.setGendoGaku(simDto.getRiyouProcessList().get(0).getTusho1GendoN());
            gui01213RiyourshaData.setGendoStartYmd(simDto.getRiyouProcessList().get(0).getKikanFr());
            gui01213RiyourshaData.setGendoEndYmd(simDto.getRiyouProcessList().get(0).getKikanTo());
            gui01213RiyourshaData.setCreateYmd(CommonDtoUtil.objValToString(LocalDate.now()));
            gui01213RiyourshaData.setSimTitleKnj(CommonConstants.BLANK_STRING);
            gui01213RiyourshaData.setSimWaribikiRitu(CommonConstants.FUTAN_RATE_90);
        }

        // 5.4.ヘッダに利用者情報をセット
        gui01213RiyourshaData.setShienId(dto.getDefSvJigyoId());
        gui01213RiyourshaData.setUserid(dto.getUserId());
        gui01213RiyourshaData.setYymmYm(dto.getAppYm());
        gui01213RiyourshaData.setYymmD(CommonConstants.MONTHSTART);
        gui01213RiyourshaData.setDmyUserName(simDto.getRiyouProcessList().get(0).getUsername());
        gui01213RiyourshaData.setDmySeqNo(simDto.getRiyouProcessList().get(0).getSeqNo());
        gui01213RiyourshaData.setDmyTanto(simDto.getRiyouProcessList().get(0).getTanto());

        // 5.4.1. 5.1の利用票・別表ヘッダ-sim取得件数>0の場合
        if (CollectionUtils.isNotEmpty(simOutList)) {
            // 利用票・別表ヘッダ-simの1件目データ
            KghCmnTucPlanSimOutEntity cmnTucPlanSimOut1Entity = simOutList.get(0);
            gui01213RiyourshaData.setSvTensuY(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getSvTensuY()));
            gui01213RiyourshaData.setSvTensuJ(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getSvTensuJ()));
            gui01213RiyourshaData.setHShuOver(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getHShuOver()));
            gui01213RiyourshaData.setHShuKijun(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getHShuKijun()));
            gui01213RiyourshaData.setHKbnOver(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getHKbnOver()));
            gui01213RiyourshaData.setHKbnKijun(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getHKbnKijun()));
            gui01213RiyourshaData.setHHknHutan(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getHHknHutan()));
            gui01213RiyourshaData.setHJihHutan(CommonDtoUtil.objValToString(cmnTucPlanSimOut1Entity.getHJihHutan()));
        }
        riyourShaDataList.add(gui01213RiyourshaData);
        simDto.setRiyourshaData(riyourShaDataList);

        // 6. 5.1の利用票・別表ヘッダ-sim取得件数>0の場合
        if (CollectionUtils.isNotEmpty(simOutList)) {
            // 6.1.ケアマネ：利用票：シミュレーション処理 利用票 利用票明細を取得する。
            KghCmnSimuObject1LineByCriteriaInEntity inEntity = new KghCmnSimuObject1LineByCriteriaInEntity();
            inEntity.setShienId(CommonDtoUtil.strValToInt(dto.getDefSvJigyoId()));
            inEntity.setUserid(CommonDtoUtil.strValToInt(dto.getUserId()));
            inEntity.setTeiYm(dto.getAppYm());
            inEntity.setTeiYmd(CommonConstants.MONTHSTART);
            // ケアマネ：利用票：シミュレーション処理 利用票 利用票明細取得情報
            List<KghCmnSimuObject1LineOutEntity> lineOutEntities = cmnTucPlanRiyouSimSelectMapper
                    .findKghCmnSimuObject1LineByCriteria(inEntity);
            // 6.2.上記取得できる場合
            if (CollectionUtils.isNotEmpty(lineOutEntities)) {
                List<Gui01149Riyou> list = new ArrayList<>();
                // 6.2.1.取得結果をループして、サービス内容を取得する
                for (KghCmnSimuObject1LineOutEntity kghCmnSimuObject1LineOutEntity : lineOutEntities) {
                    Gui01149RiyouSvItemNmInInfo gui01149RiyouSvItemNmInInfo = new Gui01149RiyouSvItemNmInInfo();
                    // 画面用明細情報 = 取得結果.該当行
                    gui01149RiyouSvItemNmInInfo.setRiyouInfo(setRiyouValue(kghCmnSimuObject1LineOutEntity));
                    gui01149RiyouSvItemNmInInfo.setBeppyoInfo(new Gui01149RiyouBeppyo());
                    Gui01149RiyouSvItemNmOutInfo gui01149RiyouSvItemNmOutInfo = useSlipInitInfoSelectServiceImpl
                            .acquDetailServiceItemNm(
                                    // 情報対象ID = "4"
                                    CommonConstants.STR_4,
                                    // サービス事業者ID = 取得結果.該当行.サービス事業者ID
                                    CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getSvJigyoId()),
                                    // サービス項目ID = 取得結果.該当行.サービス項目ID
                                    CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getSvItemCd()),
                                    // 提供年月 = リクエストパラメータ.提供年月
                                    dto.getAppYm(),
                                    // 提供月（日）
                                    CommonConstants.MONTHSTART,
                                    // ３０日超過フラグ（予定）
                                    CommonConstants.STR_0,
                                    // 画面用明細情報 = 取得結果.該当行
                                    gui01149RiyouSvItemNmInInfo);
                    // ・レスポンスパラメータ.利用票明細情報.該当行.保険データID = 0
                    gui01149RiyouSvItemNmInInfo.getRiyouInfo().setDmySeqNo(CommonConstants.STR_0);
                    // 6.2.2.レスポンスパラメータ.利用票明細情報に上記取得した画面用明細情報を格納する
                    list.add(gui01149RiyouSvItemNmOutInfo.getRiyouInfo());
                }
                simDto.setRiyouList(list);
            }

            // 6.3.ソート用ダミー時間のセット
            List<Gui01149Riyou> tmpList = getRiyouOutList(dto.getAppYm(), simDto.getRiyouList());
            simDto.setRiyouList(tmpList);

            // 6.4.合計欄情報を取得し、レスポンスパラメータ.合計表示欄情報を追加する。
            Gui01149Sum gui01149Sum = new Gui01149Sum();
            // レスポンスパラメータ.利用者情報の1件目データ
            Gui01213RiyourshaData tmpEntity = simDto.getRiyourshaData().get(0);
            gui01149Sum.setZenGetuSum(tmpEntity.getTZengetu());
            gui01149Sum.setCmnTucPlanTTougetu(tmpEntity.getTTougetu());
            gui01149Sum.setComputeRuikeiSum(tmpEntity.getTRuiseki());
            gui01149Sum.setTensuYoteiSum(tmpEntity.getSvTensuY());
            gui01149Sum.setTensuJissekiSum(tmpEntity.getSvTensuJ());
            gui01149Sum.setKbnOverSum(tmpEntity.getHKbnOver());
            gui01149Sum.setKbnKijunSum(tmpEntity.getHKbnKijun());
            gui01149Sum.setHknHutanSum(tmpEntity.getHHknHutan());
            gui01149Sum.setJihHutanSum(tmpEntity.getHJihHutan());
            gui01149Sum.setHShuOver(tmpEntity.getHShuOver());
            gui01149Sum.setHShuKijun(tmpEntity.getHShuKijun());
            gui01149Sum.setOv30Zengetu(tmpEntity.getOv30Zengetu());
            simDto.setSumData(List.of(gui01149Sum));

            // 6.5.別表明細情報を取得する。
            KghCmnSimuObject1List202504ByCriteriaInEntity kghCmnSimuObject1List202504ByCriteriaInEntity = new KghCmnSimuObject1List202504ByCriteriaInEntity();
            kghCmnSimuObject1List202504ByCriteriaInEntity.setAlShienId(CommonDtoUtil.strValToInt(dto.getDefSvJigyoId()));
            kghCmnSimuObject1List202504ByCriteriaInEntity.setAlUserid(CommonDtoUtil.strValToInt(dto.getUserId()));
            kghCmnSimuObject1List202504ByCriteriaInEntity.setAsYymmYm(simDto.getTeikyouYm());
            kghCmnSimuObject1List202504ByCriteriaInEntity.setAsYymmD(simDto.getTeikyouYmD());
            // 別表 別表明細取得SQL
            List<KghCmnSimuObject1List202504OutEntity> simuObjectList = cmnTucPlanBetu1SimSelectMapper
                    .findKghCmnSimuObject1List202504ByCriteria(kghCmnSimuObject1List202504ByCriteriaInEntity);
            List<Gui01149RiyouBeppyo> listRiyouBeppyo = new ArrayList<>();
            for (KghCmnSimuObject1List202504OutEntity kghCmnSimuObject1ListOutEntity : simuObjectList) {
                Gui01149RiyouBeppyo beppyo = setRiyouBeppyoValue(kghCmnSimuObject1ListOutEntity);

                // 6.5.1.上記別表明細情報検索結果をループする
                // Gui01149RiyouBeppyo beppyo = new Gui01149RiyouBeppyo();
                // 6.5.1.1.別表明細情報.サービス事業者ID > 0の場合、事業所名(略称)を取得する
                if (StringUtils.compare(beppyo.getSvJigyoId(), CommonConstants.STR_0) > 0) {
                    String yyyyMMdd = simDto.getTeikyouYm() + CommonConstants.STR_DELIMITER + simDto.getTeikyouYmD();
                    String svJigyoNameKnj = kghCmn03gFunc01Logic
                            .getShienNameRyaku(CommonDtoUtil.strValToInt(beppyo.getSvJigyoId()), yyyyMMdd);
                    // 別表明細情報.事業所名 = 上記結果.事業所名(略称)
                    beppyo.setDmyJigyoNameKnj(svJigyoNameKnj);
                }
                // 6.5.1.2.上記以外の場合、
                else {
                    // 別表明細情報.事業所名 = "（未選択）"
                    beppyo.setDmyJigyoNameKnj(CommonConstants.UNSELECTED_DISPLAY);
                }
                // 6.5.1.3.事業所番号を取得する
                String yyyyMMdd = simDto.getTeikyouYm() + CommonConstants.STR_DELIMITER + simDto.getTeikyouYmD();
                String ShienNumber = kghCmn03gFunc01Logic
                        .getShienNumber(CommonDtoUtil.strValToInt(beppyo.getSvJigyoId()), yyyyMMdd);
                // 別表明細情報.事業所番号 = 上記結果.事業所番号
                beppyo.setDmyJigyoNumber(ShienNumber);

                Gui01149RiyouSvItemNmInInfo gui01149RiyouSvItemNmInInfo = new Gui01149RiyouSvItemNmInInfo();
                gui01149RiyouSvItemNmInInfo.setRiyouInfo(new Gui01149Riyou());
                // 取得結果.該当行
                gui01149RiyouSvItemNmInInfo.setBeppyoInfo(beppyo);
                // *******.事業所IDとサービス項目IDよりサービス項目名を取得しサービス項目名用カラムにセットする
                // API定義書_APINo(701)_利用票画面初期情報取得.xlsxの「関数_明細情報サービス項目名取得」シート
                Gui01149RiyouSvItemNmOutInfo gui01149RiyouSvItemNmOutInfo = useSlipInitInfoSelectServiceImpl
                        .acquDetailServiceItemNm(
                                // 情報対象ID = "2"
                                CommonConstants.STR_2,
                                // サービス事業者ID = 取得結果.該当行.サービス事業者ID
                                CommonDtoUtil.objValToString(beppyo.getSvJigyoId()),
                                // サービス項目ID = 取得結果.該当行.サービス項目ID
                                CommonDtoUtil.objValToString(beppyo.getSvItemCd()),
                                // 提供年月 = リクエストパラメータ.提供年月
                                dto.getAppYm(),
                                // 提供月（日） = "01"
                                CommonConstants.MONTHSTART,
                                // ３０日超過フラグ（予定） = 取得結果.該当行.30日超過フラグ
                                CommonDtoUtil.objValToString(beppyo.getOv30Fl()),
                                // 画面用明細情報 = 取得結果.該当行
                                gui01149RiyouSvItemNmInInfo);
                Gui01149RiyouBeppyo newBeppyo = gui01149RiyouSvItemNmOutInfo.getBeppyoInfo();

                // 6.5.1.5.総合サービス区分を取得する
                newBeppyo
                        .setCSougouKbn(CommonDtoUtil.objValToString(kghCmnF01Logic.sougouSvKbn(newBeppyo.getSvType())));
                // 6.5.1.6.値設定
                setValue(newBeppyo);

                listRiyouBeppyo.add(newBeppyo);
            }
            // ・レスポンスパラメータ.別表明細情報に上記別表明細情報検索結果を設定する。
            simDto.setRiyouBeppyoList(listRiyouBeppyo);

            // 6.6.計算ソート用を取得する
            String yyyyMMdd = simDto.getTeikyouYm() + CommonConstants.STR_DELIMITER + simDto.getTeikyouYmD();
            kghCmnRiyou01Logic.setAdderServiceBetuDw(simDto.getRiyouBeppyoList(), yyyyMMdd);

            // 6.7.サービス種類CD取得後、別表明細情報は以下のソートで並べ替えられる。
            String svTypeResult = kghCmnF01Logic.getSvType(CommonDtoUtil.strValToInt(dto.getUserId()));
            // 上記取得したサービス種類CD = '33' OR サービス種類CD = '35' OR サービス種類CD ='36'の場合
            if (CommonConstants.SV_CD_STR_33.equals(svTypeResult) || CommonConstants.SV_CD_STR_35.equals(svTypeResult)
                    || CommonConstants.SV_CD_STR_36.equals(svTypeResult)) {
                // 表示用ソート：ソート用サービス種類CD A, ３０日超過フラグ A,
                // 加算フラグ A, 合計フラグ A, 事業所番号 A, サービス事業者ID A,
                // 計算ソート A, サービスコード A, サービス項目ID A, 枝番 A
                simDto.setRiyouBeppyoList(simDto.getRiyouBeppyoList().stream().sorted(Comparator
                        .comparing(Gui01149RiyouBeppyo::getSvType).thenComparing(Gui01149RiyouBeppyo::getOv30Fl)
                        .thenComparing(Gui01149RiyouBeppyo::getKasanF).thenComparing(Gui01149RiyouBeppyo::getTotalF)
                        .thenComparing(Gui01149RiyouBeppyo::getDmyJigyoNumber)
                        .thenComparing(Gui01149RiyouBeppyo::getSvJigyoId)
                        .thenComparing(Gui01149RiyouBeppyo::getDmyCalcSort)
                        .thenComparing(Gui01149RiyouBeppyo::getScode).thenComparing(Gui01149RiyouBeppyo::getSvItemCd)
                        .thenComparing(Gui01149RiyouBeppyo::getEdaNo)).toList());

            } else if (!CommonConstants.SV_CD_STR_33.equals(svTypeResult)
                    && !CommonConstants.SV_CD_STR_35.equals(svTypeResult)
                    && !CommonConstants.SV_CD_STR_36.equals(svTypeResult)) {
                // 表示用ソート：サービス種類CD A, 事業所番号 A,
                // サービス事業者ID A, ３０日超過フラグ A, 加算フラグ A,
                // 合計フラグ A, 計算ソート A, サービスコード A, サービス項目ID A, 枝番 A
                simDto.setRiyouBeppyoList(simDto.getRiyouBeppyoList().stream().sorted(Comparator
                        .comparing(Gui01149RiyouBeppyo::getSvType).thenComparing(Gui01149RiyouBeppyo::getDmyJigyoNumber)
                        .thenComparing(Gui01149RiyouBeppyo::getSvJigyoId).thenComparing(Gui01149RiyouBeppyo::getOv30Fl)
                        .thenComparing(Gui01149RiyouBeppyo::getKasanF).thenComparing(Gui01149RiyouBeppyo::getTotalF)
                        .thenComparing(Gui01149RiyouBeppyo::getDmyCalcSort)
                        .thenComparing(Gui01149RiyouBeppyo::getScode).thenComparing(Gui01149RiyouBeppyo::getSvItemCd)
                        .thenComparing(Gui01149RiyouBeppyo::getEdaNo)).toList());
            }

            // 6.8.金額のセット処理
            Gui01149Riyou701Info riyouInfo = new Gui01149Riyou701Info();
            // ・変数.シミュレーション画面情報にレスポンスパラメータ.利用者情報を追加する。
            riyouInfo.setRiyourshaData(set1213RiyourshaDataTo1149(simDto.getRiyourshaData()));
            // ・変数.シミュレーション画面情報にレスポンスパラメータ.別表明細情報を追加する。
            riyouInfo.setRiyouBeppyoList(simDto.getRiyouBeppyoList());
            // ・変数.シミュレーション画面情報にレスポンスパラメータ.区分支給限度情報を追加する。
            riyouInfo.setKbnShikyuuGendoData(simDto.getKbnShikyuuGendoData());
            // ・変数.シミュレーション画面情報にレスポンスパラメータ.利用票画面処理用構造体を追加する。
            riyouInfo.setRiyouProcessObject(simDto.getRiyouProcessList());
            // ・変数.シミュレーション画面情報にレスポンスパラメータ.合計表示欄情報を追加する。
            riyouInfo.setSumData(simDto.getSumData());
            // ・変数.シミュレーション画面情報にレスポンスパラメータ.警告メッセージ配列を追加する。
            riyouInfo.setMessageList(simDto.getMessageList());

            // API定義書_APINo(701)_利用票画面初期情報取得.xlsxの「関数_金額のセット」シート
            useSlipInitInfoSelectServiceImpl.getAmountsPrco(CommonDtoUtil.strValToInt(dto.getUserId()), dto.getAppYm(),
                    CommonConstants.STR_0, riyouInfo);

            // ・レスポンスパラメータ.利用票画面情報.合計表示欄情報 = 上記結果.利用票画面情報.合計表示欄情報
            simDto.setSumData(riyouInfo.getSumData());
            // ・レスポンスパラメータ.利用票画面情報.警告メッセージ配列 = 上記結果.利用票画面情報.警告メッセージ配列
            simDto.setMessageList(riyouInfo.getMessageList());

            // 6.9. 福祉用具情報の設定 ※以下の条件に全てあてはまる場合のみ実施する
            if (dto.getAppYm().compareTo(CommonConstants.STR_YM_2025_04) >= 0) {
                // 6.9.1. リクエストパラメータ.提供年月  >= "2025/04" の場合
                for (Gui01149RiyouBeppyo riyouBepyo : simDto.getRiyouBeppyoList()) {
                    // *******. レスポンスパラメータ.別表明細情報.サービスコードの先頭2桁が"17" OR "67" 
                    // OR レスポンスパラメータ.別表明細情報.サービスコードの先頭4桁が"3320" OR "3519"の場合
                    if (CommonConstants.SV_TYPE_CHECK_LIST_7
                        .contains(StringUtils.substring(riyouBepyo.getScode(), 2))
                        || CommonConstants.STR_3320.equals(StringUtils.substring(riyouBepyo.getScode(), 4))
                        || CommonConstants.STR_3519.equals(StringUtils.substring(riyouBepyo.getScode(), 4))) {
                            // *******.1. レスポンスパラメータ.別表明細情報.福祉用具貸与マスタIDが NULL でない、且つ、0 でない場合
                            // 以下の共通関数を呼び出し、用具名称と TAISコードを取得する
                            FygJohoNextOutDto fygJohoNextOutDto = kghCmnRiyou01Logic.kghCmnGetFygJohoNext(
                                    CommonDtoUtil.strValToInt(riyouBepyo.getFygId()),
                                    simDto.getTeikyouYm()+ CommonConstants.STR_DELIMITER + simDto.getTeikyouYmD());
                            // 上記結果.件数 > 0 の場合
                            if (fygJohoNextOutDto.getLlRet() > 0) {
                                // 【返却】.別表明細情報.用具名称 = 【変数】.福祉用具情報構造体.商品名称
                                riyouBepyo.setFygShouhinKnj(fygJohoNextOutDto.getRiyouTaisJohoOutAstr().getShouhinKnj());
                                // 【返却】.別表明細情報.TAISコード = 【変数】.福祉用具情報構造体.TAISコード/JANコード
                                riyouBepyo.setFygTekiyouCode(fygJohoNextOutDto.getRiyouTaisJohoOutAstr().getTekiyouCode());
                            }
                        }
                        listRiyouBeppyo.add(riyouBepyo);
                }
                simDto.setRiyouBeppyoList(listRiyouBeppyo);
            }

            // 6.10.種類別限度情報を取得する。
            KghCmnSimuObject1SyuruiSougouByCriteriaInEntity kghCmnSimuObject1SyuruiSougouByCriteriaInEntity = new KghCmnSimuObject1SyuruiSougouByCriteriaInEntity();
            // 支援事業者ID
            kghCmnSimuObject1SyuruiSougouByCriteriaInEntity.setAlShi(CommonDtoUtil.strValToInt(dto.getDefSvJigyoId()));
            // 利用者ID
            kghCmnSimuObject1SyuruiSougouByCriteriaInEntity.setAlUsr(CommonDtoUtil.strValToInt(dto.getUserId()));
            // サービス提供年月
            kghCmnSimuObject1SyuruiSougouByCriteriaInEntity.setAsYm(simDto.getTeikyouYm());
            // サービス提供年月（変更日）
            kghCmnSimuObject1SyuruiSougouByCriteriaInEntity.setAsDd(CommonConstants.MONTHSTART);
            // 取得SQL
            List<KghCmnSimuObject1SyuruiSougouOutEntity> kghCmnBeppyo2SyuruiSougouOutList = cmnTucPlanBetu2SimSelectMapper
                    .findKghCmnSimuObject1SyuruiSougouByCriteria(kghCmnSimuObject1SyuruiSougouByCriteriaInEntity);
            List<Gui01213SyuruiGendoData> gendoDataList = new ArrayList<>();
            for (KghCmnSimuObject1SyuruiSougouOutEntity entity2 : kghCmnBeppyo2SyuruiSougouOutList) {
                Gui01213SyuruiGendoData gendoData = setGendoData(entity2);
                gendoDataList.add(gendoData);
            }
            simDto.setSyuruiGendoData(gendoDataList);

            // 6.11.公費集計欄情報を取得する。
            KghCmnBeppyo3KohiByCriteriaInEntity kghCmnBeppyo3KohiByCriteriaInEntity = new KghCmnBeppyo3KohiByCriteriaInEntity();
            // 支援事業者ID
            kghCmnBeppyo3KohiByCriteriaInEntity.setAlShienId(CommonDtoUtil.strValToInt(dto.getDefSvJigyoId()));
            // 利用者ID
            kghCmnBeppyo3KohiByCriteriaInEntity.setAlUserid(CommonDtoUtil.strValToInt(dto.getUserId()));
            // サービス提供年月
            kghCmnBeppyo3KohiByCriteriaInEntity.setAsYymmYm(simDto.getTeikyouYm());
            // サービス提供年月（変更日）
            kghCmnBeppyo3KohiByCriteriaInEntity.setAsYymmD(simDto.getTeikyouYmD());
            // 公費集計欄情報の取得
            List<KghCmnBeppyo3KohiOutEntity> kghCmnBeppyo3KohiOutList = cmnTucPlanKouhiFutanSelectMapper
                    .findKghCmnBeppyo3KohiByCriteria(kghCmnBeppyo3KohiByCriteriaInEntity);
            // 公費集計欄情報
            List<Gui01149Kohi> kohiList = new ArrayList<>();
            // 上記結果を【返却】.公費集計欄情報に設定する
            for (KghCmnBeppyo3KohiOutEntity kghCmnBeppyo3KohiOutEntity : kghCmnBeppyo3KohiOutList) {
                // 公費集計欄情報
                Gui01149Kohi kohi = new Gui01149Kohi();
                if (kghCmnBeppyo3KohiOutEntity.getSvJigyoId() > 0) {
                    // 事業者名(略称)を得る（履歴対応版）
                    String svJigyoNameKnj = kghCmn03gFunc01Logic.getShienNameRyaku(
                            kghCmnBeppyo3KohiOutEntity.getSvJigyoId(),
                            simDto.getTeikyouYm() + CommonConstants.STR_DELIMITER + simDto.getTeikyouYmD());
                    // レスポンスパラメー.公費集計欄情報.事業所名 = 上記結果.事業所名(略称)
                    kohi.setDmyJigyoNameKnj(svJigyoNameKnj);
                } else {
                    // 上記以外の場合、
                    // レスポンスパラメー.公費集計欄情報.事業所名 = "（未選択）"
                    kohi.setDmyJigyoNameKnj(CommonConstants.UNSELECTED_DISPLAY);
                }
                // サービス事業者ID
                kohi.setSvJigyoId(CommonDtoUtil.objValToString(kghCmnBeppyo3KohiOutEntity.getSvJigyoId()));
                // 公費分負担額
                kohi.setKohiGaku(CommonDtoUtil.objValToString(kghCmnBeppyo3KohiOutEntity.getFutanGaku()));
                // 公費分本人負担額
                kohi.setHonninHutan(CommonDtoUtil.objValToString(kghCmnBeppyo3KohiOutEntity.getHonninFutanGaku()));
                // 支援事業者ID
                kohi.setShienId(CommonDtoUtil.objValToString(kghCmnBeppyo3KohiOutEntity.getShienId()));
                // 利用者ID
                kohi.setUserid(CommonDtoUtil.objValToString(kghCmnBeppyo3KohiOutEntity.getUserid()));
                // サービス提供年月
                kohi.setYymmYm(kghCmnBeppyo3KohiOutEntity.getYymmYm());
                // サービス提供年月（変更日）
                kohi.setYymmD(kghCmnBeppyo3KohiOutEntity.getYymmD());
                // 公費法制コード
                kohi.setCode(kghCmnBeppyo3KohiOutEntity.getCode());
                // サービス種類
                kohi.setSvtype(kghCmnBeppyo3KohiOutEntity.getSvtype());

                kohiList.add(kohi);
            }
            simDto.setKohiList(kohiList);

            // 6.12 サービス事業所情報リストを取得する
            KghCmnSvselSelJigyoByCriteriaInEntity kghCmnSvselSelJigyoByCriteriaInEntity = new KghCmnSvselSelJigyoByCriteriaInEntity();
            List<KghCmnSvselSelJigyoOutEntity> kghCmnSvselSelJigyoOutEntityList = cmnSvselSelJigyoSelectMapper
                    .findKghCmnSvselSelJigyoByCriteria(kghCmnSvselSelJigyoByCriteriaInEntity);
            List<String> shienIdArray = new ArrayList<>();
            for (Gui01149RiyouBeppyo gui01149RiyouBeppyo : simDto.getRiyouBeppyoList()) {
                shienIdArray.add(gui01149RiyouBeppyo.getShienId());
            }
            // 6.12.1.変数.支援事業者ID配列 = レスポンスパラメータ.別表明細情報.支援事業者ID(重複のIDが不要)
            List<String> shienIdArrayDuplicates = shienIdArray.stream().distinct().toList();

            // 6.12.2.処理6.11で取得したサービス事業所情報リスト.サービス種類コード in 変数.支援事業者ID配列のデータを洗い出して
            List<Gui01213SvjigyoList> gui01213SvjigyoList = new ArrayList<>();
            for (KghCmnSvselSelJigyoOutEntity kghCmnSvselSelJigyoOutEntity : kghCmnSvselSelJigyoOutEntityList) {
                if (shienIdArrayDuplicates.contains(kghCmnSvselSelJigyoOutEntity.getSvKindCd())) {
                    Gui01213SvjigyoList gui01213Svjigyo = new Gui01213SvjigyoList();
                    // サービス事業者ID
                    gui01213Svjigyo
                            .setSvJigyoId(CommonDtoUtil.objValToString(kghCmnSvselSelJigyoOutEntity.getSvJigyoId()));
                    // 事業名（略称）
                    gui01213Svjigyo.setJigyoRyakuKnj(
                            CommonDtoUtil.objValToString(kghCmnSvselSelJigyoOutEntity.getSvJigyoId()));
                    // ★サービス種類コード→4-5
                    gui01213Svjigyo.setSvKindCd(kghCmnSvselSelJigyoOutEntity.getSvKindCd());
                    // 略称
                    gui01213Svjigyo.setRuakuKnj(kghCmnSvselSelJigyoOutEntity.getRuakuKnj());
                    // 事業所番号
                    gui01213Svjigyo.setJigyoNumber(kghCmnSvselSelJigyoOutEntity.getJigyoNumber());
                    gui01213SvjigyoList.add(gui01213Svjigyo);
                }
            }
            // レスポンスパラメータ.サービス事業所情報リストに設定する
            simDto.setSvjigyoList(gui01213SvjigyoList);

            // 6.13.短期入所連続利用30日超過情報を取得する
            // 入力パラメータ
            KghCmnTucPlanOv30SimByCriteriaInEntity kghCmnTucPlanOv30SimByCriteriaInEntity = new KghCmnTucPlanOv30SimByCriteriaInEntity();
            kghCmnTucPlanOv30SimByCriteriaInEntity.setAlShienId(CommonDtoUtil.strValToInt(dto.getDefSvJigyoId()));
            kghCmnTucPlanOv30SimByCriteriaInEntity.setAlUserid(CommonDtoUtil.strValToInt(dto.getUserId()));
            kghCmnTucPlanOv30SimByCriteriaInEntity.setAsYymmYm(dto.getAppYm());
            List<KghCmnTucPlanOv30SimOutEntity> sov30SimoutList = cmnTucPlanOv30SimSelectMapper
                    .findKghCmnTucPlanOv30SimByCriteria(kghCmnTucPlanOv30SimByCriteriaInEntity);
            List<Gui01149PlanOv30> planOv30List = new ArrayList<>();
            for (KghCmnTucPlanOv30SimOutEntity kghCmnTucPlanOv30SimOutEntity : sov30SimoutList) {
                Gui01149PlanOv30 gui01149PlanOv30 = new Gui01149PlanOv30();
                gui01149PlanOv30.setShienId(CommonDtoUtil.objValToString(kghCmnTucPlanOv30SimOutEntity.getShienId()));
                gui01149PlanOv30.setUserid(CommonDtoUtil.objValToString(kghCmnTucPlanOv30SimOutEntity.getUserid()));
                gui01149PlanOv30.setYymmYm(kghCmnTucPlanOv30SimOutEntity.getYymmYm());
                gui01149PlanOv30.setYymmD(kghCmnTucPlanOv30SimOutEntity.getYymmD());
                gui01149PlanOv30
                        .setSvJigyoId(CommonDtoUtil.objValToString(kghCmnTucPlanOv30SimOutEntity.getSvJigyoId()));
                gui01149PlanOv30.setSvtype(kghCmnTucPlanOv30SimOutEntity.getSvtype());
                gui01149PlanOv30.setOv30Day(CommonDtoUtil.objValToString(kghCmnTucPlanOv30SimOutEntity.getOv30Day()));

                planOv30List.add(gui01149PlanOv30);
            }
            simDto.setPlanOv30List(planOv30List);
        }
        // 7. 5.1の利用票・別表ヘッダ-sim取得件数<=0の場合、行追加を行う
        else if (CollectionUtils.isEmpty(simOutList)) {
            // 7.1.利用者情報の初期化処理を行う
            List<Gui01213RiyourshaData> riyourshaDataList = new ArrayList<>();
            // ・レスポンスパラメータ.利用者情報.サービス提供年月 = リクエストパラメータ.提供年月
            gui01213RiyourshaData.setYymmYm(dto.getAppYm());
            // ・レスポンスパラメータ.利用者情報.サービス提供年月（変更日） = "01"
            gui01213RiyourshaData.setYymmD(CommonConstants.MONTHSTART);
            // ・レスポンスパラメータ.利用者情報.要介護度 = 7
            gui01213RiyourshaData.setYokaiKbn(CommonConstants.YOKAI_KBN_7);
            // ・レスポンスパラメータ.利用者情報.タイトル = ""
            gui01213RiyourshaData.setSimTitleKnj(CommonConstants.BLANK_STRING);
            // ・レスポンスパラメータ.利用者情報.作成日 = システム日付(yyyy/mm/dd)
            gui01213RiyourshaData.setCreateYmd(CommonDtoUtil.objValToString(LocalDate.now()));
            // ・レスポンスパラメータ.利用者情報.給付率 = 90
            gui01213RiyourshaData.setSimWaribikiRitu(CommonConstants.FUTAN_RATE_90);
            riyourshaDataList.add(gui01213RiyourshaData);
            simDto.setRiyourshaData(riyourshaDataList);

            // 7.2.事業所番号のセット
            // 7.2.1.事業所番号を取得する
            // 履歴検索年月日
            String yyyyMMdd = simDto.getTeikyouYm() + CommonConstants.STR_DELIMITER + simDto.getTeikyouYmD();
            // 対象の事業所id
            Integer tmpSvId = CommonDtoUtil.strValToInt(simDto.getRiyouBeppyoList().get(0).getSvJigyoId());
            String ShienNumber = kghCmn03gFunc01Logic.getShienNumber(tmpSvId, yyyyMMdd);
            // 別表明細情報.事業所番号 = 上記結果.事業所番号
            Gui01149RiyouBeppyo gui01149RiyouBeppyo = new Gui01149RiyouBeppyo();
            gui01149RiyouBeppyo.setDmyJigyoNumber(ShienNumber);
            simDto.setRiyouBeppyoList(List.of(gui01149RiyouBeppyo));

            // 7.3.金額のセット処理
            Gui01149Riyou701Info riyouInfo = new Gui01149Riyou701Info();
            // ・変数.シミュレーション画面情報にレスポンスパラメータ.利用者情報を追加する。
            riyouInfo.setRiyourshaData(set1213RiyourshaDataTo1149(simDto.getRiyourshaData()));
            // ・変数.シミュレーション画面情報にレスポンスパラメータ.別表明細情報を追加する。
            riyouInfo.setRiyouBeppyoList(simDto.getRiyouBeppyoList());
            // ・変数.シミュレーション画面情報にレスポンスパラメータ.区分支給限度情報を追加する。
            riyouInfo.setKbnShikyuuGendoData(simDto.getKbnShikyuuGendoData());
            // ・変数.シミュレーション画面情報にレスポンスパラメータ.利用票画面処理用構造体を追加する。
            riyouInfo.setRiyouProcessObject(simDto.getRiyouProcessList());
            // ・変数.シミュレーション画面情報にレスポンスパラメータ.合計表示欄情報を追加する。
            riyouInfo.setSumData(simDto.getSumData());
            // ・変数.シミュレーション画面情報にレスポンスパラメータ.警告メッセージ配列を追加する。
            riyouInfo.setMessageList(simDto.getMessageList());

            useSlipInitInfoSelectServiceImpl.getAmountsPrco(CommonDtoUtil.strValToInt(dto.getUserId()), dto.getAppYm(),
                    CommonConstants.STR_0, riyouInfo);
            // ・レスポンスパラメータ.利用票画面情報.合計表示欄情報 = 上記結果.利用票画面情報.合計表示欄情報
            simDto.setSumData(riyouInfo.getSumData());
            // ・レスポンスパラメータ.利用票画面情報.警告メッセージ配列 = 上記結果.利用票画面情報.警告メッセージ配列
            simDto.setMessageList(riyouInfo.getMessageList());

            // 7.4. 画面表示の初期化をおこなう
            Gui01149RiyouDataBackgroundColorOutDto gui01149RiyouDataBackgroundColorOutDto = useSlipInitInfoSelectServiceImpl
                    .riyouDataBackgroundColorSetProc(dto.getAppYm(), CommonConstants.NUMBER_0, simDto.getRiyouList(),
                            simDto.getRiyouProcessList());
            // ・レスポンスパラメータ.利用票曜日文字配列 = 上記結果.利用票曜日文字配列
            simDto.setWeek(gui01149RiyouDataBackgroundColorOutDto.getWeek());
            // ・レスポンスパラメータ.利用票曜日の文字の色配列 = 上記結果.利用票曜日の文字の色配列
            simDto.setWeekColor(gui01149RiyouDataBackgroundColorOutDto.getWeekColor());
            // ・レスポンスパラメータ.利用票曜日の背景色配列 = 上記結果.利用票曜日の背景色配列
            simDto.setWeekBgColor(gui01149RiyouDataBackgroundColorOutDto.getWeekBgColor());
            // ・レスポンスパラメータ.利用票明細情報 = 上記結果.利用票明細情報
            simDto.setRiyouList(gui01149RiyouDataBackgroundColorOutDto.getRiyouList());
            // ・レスポンスパラメータ.利用票画面処理用構造体 = 上記結果.利用票画面処理用構造体
            simDto.setRiyouProcessList(gui01149RiyouDataBackgroundColorOutDto.getRiyouProcessObject());

            // 7.5.ケアマネ：利用票：シミュレーション処理 利用票 利用票明細を取得する。
            KghCmnSimuObject1LineByCriteriaInEntity inEntity = new KghCmnSimuObject1LineByCriteriaInEntity();
            inEntity.setShienId(CommonDtoUtil.strValToInt(dto.getDefSvJigyoId()));
            inEntity.setUserid(CommonConstants.NUMBER_0);
            inEntity.setTeiYm(simDto.getTeikyouYm());
            inEntity.setTeiYmd(simDto.getTeikyouYmD());
            List<KghCmnSimuObject1LineOutEntity> lineOutEntities = cmnTucPlanRiyouSimSelectMapper
                    .findKghCmnSimuObject1LineByCriteria(inEntity);
            List<Gui01149Riyou> riyouList = new ArrayList<>();
            for (KghCmnSimuObject1LineOutEntity kghCmnSimuObject1LineOutEntity : lineOutEntities) {
                Gui01149Riyou gui01149Riyou = new Gui01149Riyou();
                gui01149Riyou = setRiyouListInfo(kghCmnSimuObject1LineOutEntity);
                riyouList.add(gui01149Riyou);
            }
            // ・レスポンスパラメータ.利用票明細情報 = 上記取得結果
            simDto.setRiyouList(riyouList);
        }

        // 8.画面表示の初期化をおこなう
        Gui01149RiyouDataBackgroundColorOutDto gui01149RiyouDataBackgroundColorOutDto = useSlipInitInfoSelectServiceImpl
                .riyouDataBackgroundColorSetProc(dto.getAppYm(), CommonConstants.NUMBER_0, simDto.getRiyouList(),
                        simDto.getRiyouProcessList());
        // ・レスポンスパラメータ.利用票曜日文字配列 = 上記結果.利用票曜日文字配列
        simDto.setWeek(gui01149RiyouDataBackgroundColorOutDto.getWeek());
        // ・レスポンスパラメータ.利用票曜日の文字の色配列 = 上記結果.利用票曜日の文字の色配列
        simDto.setWeekColor(gui01149RiyouDataBackgroundColorOutDto.getWeekColor());
        // ・レスポンスパラメータ.利用票曜日の背景色配列 = 上記結果.利用票曜日の背景色配列
        simDto.setWeekBgColor(gui01149RiyouDataBackgroundColorOutDto.getWeekBgColor());
        // ・レスポンスパラメータ.利用票明細情報 = 上記結果.利用票明細情報
        simDto.setRiyouList(gui01149RiyouDataBackgroundColorOutDto.getRiyouList());
        // ・レスポンスパラメータ.利用票画面処理用構造体 = 上記結果.利用票画面処理用構造体
        simDto.setRiyouProcessList(gui01149RiyouDataBackgroundColorOutDto.getRiyouProcessObject());
    }

    /**
     * 9.利用票：重複するサービスの背景色を赤にする
     * 
     * @param teikyouYm 提供年月
     * @param riyouInfo 利用票画面情報
     * @throws Exception Exception
     */
    private void setBackGroundColorToRedPrco(SimInitInfoSelectServiceInDto dto, SimInitInfoSelectServiceOutDto simDto)
            throws Exception {
        Gui01149Riyou701Info riyouInfo = new Gui01149Riyou701Info();
        riyouInfo.setRiyourshaData(set1213RiyourshaDataTo1149(simDto.getRiyourshaData()));
        // 利用票の重複するサービスの背景色を設定する
        List<Gui01149Riyou> riyouList = useSlipInitInfoSelectServiceImpl.duplicateSvBackgroundColorSet(dto.getAppYm(),
                new ArrayList<Gui01149PlanOv30>(), riyouInfo.getRiyourshaData(), simDto.getRiyouList(),
                simDto.getRiyouProcessList());
        simDto.setRiyouList(riyouList);
    }

    /**
     * 10.日付に該当するtermidを取得する
     * 
     * @param teikyouYm 提供年月
     * @param simDto    レスポンスパラメータ詳細
     */
    private void getTermId(String teikyouYm, SimInitInfoSelectServiceOutDto simDto) {
        // 21.1.変数を宣言する。
        // 【変数】.有効期間ID＝ 0
        Integer termId = 0;
        // 【変数】.年月日＝ ""
        String yymmDd = CommonConstants.BLANK_STRING;
        // 21.2.日付を設定する
        // 【変数】.年月日＝引き渡しパラメータ.提供年月 + "/01" ※yyyy/mm/dd
        yymmDd = teikyouYm + CommonConstants.STRING_FIRST_DAY;
        // 21.3.有効期間を判定する。
        // ① "2003/03/31" >= 【変数】.年月日 の場合、
        if (CommonConstants.YMD_20030331.compareTo(yymmDd) >= 0) {
            // 【変数】.有効期間ID ＝ 1
            termId = 1;
        }
        // ② "2003/04/01" <= 【変数】.年月日 AND 【変数】.年月日 <= "2005/09/30" の場合、
        if (CommonConstants.YMD_20030401.compareTo(yymmDd) <= 0
                && yymmDd.compareTo(CommonConstants.YMD_20050930) <= 0) {
            // 【変数】.有効期間ID ＝ 2
            termId = 2;
        }
        // ③ "2005/10/01" <= 【変数】.年月日 AND 【変数】.年月日 <= "2006/03/31"の場合、
        if (CommonConstants.YMD_20051001.compareTo(yymmDd) <= 0
                && yymmDd.compareTo(CommonConstants.YMD_20060331) <= 0) {
            // 【変数】.有効期間ID ＝ 3
            termId = 3;
        }
        // ④ "2006/04/01" <= 【変数】.年月日 AND 【変数】.年月日 <= "2009/03/31"の場合、
        if (CommonConstants.YMD_20060401.compareTo(yymmDd) <= 0
                && yymmDd.compareTo(CommonConstants.YMD_20090331) <= 0) {
            // 【変数】.有効期間ID ＝ 4
            termId = 4;
        }
        // ⑤ "2009/04/01" <= 【変数】.年月日 AND 【変数】.年月日 <= "2012/03/31"の場合、
        if (CommonConstants.YMD_20090401.compareTo(yymmDd) <= 0
                && yymmDd.compareTo(CommonConstants.YMD_20120331) <= 0) {
            // 【変数】.有効期間ID ＝ 5
            termId = 5;
        }
        // ⑥ "2012/04/01" <= 【変数】.年月日 AND 【変数】.年月日 <= "2014/03/31"の場合、
        if (CommonConstants.YMD_20120401.compareTo(yymmDd) <= 0
                && yymmDd.compareTo(CommonConstants.YMD_20140331) <= 0) {
            // 【変数】.有効期間ID ＝ 6
            termId = 6;
        }
        // ⑦ "2014/04/01" <= 【変数】.年月日 AND 【変数】.年月日 <= "2015/03/31"の場合、
        if (CommonConstants.YMD_20140401.compareTo(yymmDd) <= 0
                && yymmDd.compareTo(CommonConstants.YMD_20150331) <= 0) {
            // 【変数】.有効期間ID ＝ 7
            termId = 7;
        }
        // ⑧ "2015/04/01" <= 【変数】.年月日 の場合、
        if (CommonConstants.YMD_20150401.compareTo(yymmDd) <= 0) {
            // 年月から termid を取得する（簡易版）.
            termId = kghCmn03gFunc01Logic.getTermidEasy(yymmDd.substring(0, 7));
            // 【変数】.有効期間ID ＝ 上記結果.termid
        }
        // 21.4.【返却】.termid = 【変数】.有効期間ID
        simDto.setTermid(CommonDtoUtil.objValToString(termId));
    }

    /**
     * 11.区分支給限度基準額を取得する
     * 
     * @param teikyouYm 提供年月
     * @param simDto    レスポンスパラメータ詳細
     */
    private void getGendo(String teikyouYm, SimInitInfoSelectServiceOutDto simDto) {
        // 要介護度区分（ＮＤ独自）
        Integer aiYokaiKbn = 11;
        // 初期設定マスタから取得するか
        boolean flg1 = false;
        // 区分支給限度額を取得するか
        boolean flg2 = false;
        // f_cmn_get_gendo 区分支給限度額を取得する
        Integer tmp = kghCmnF01Logic.getGendo(aiYokaiKbn, teikyouYm, flg1, flg2);
        // レスポンスパラメータ.区分支給限度基準額(変更用) = 上記取得した限度額
        simDto.setGendogakuHidden(CommonDtoUtil.objValToString(tmp));
    }

    /**
     * 5.2.2.ソート用ダミー時間のセット
     * 
     * @param teikyouYm    提供年月
     * @param riyouVarList 【変数】.利用票明細情報
     * @return 【変数】.利用票明細情報
     */
    private List<Gui01149Riyou> getRiyouOutList(String teikyouYm, List<Gui01149Riyou> riyouVarList) {
        // 5.2.2.1.【変数】初期化
        // ・【変数】.検索行番号 = 1
        Integer selIndex = 1;
        // ・【変数】.ソート順1 = 親レコード番号 ASC、合成識別区分 ASC、サービス項目コード ASC、サービス項目ID ASC
        // ・【変数】.ソート順2 = ソート順 ASC、サービス開始時間_hidden ASC、サービス終了時間_hidden ASC、サービス種別コード ASC、
        // サービス事業者ID ASC、親レコード番号_hidden ASC、合成識別区分 ASC、保険データID ASC、サービス項目CD ASC、サービス項目ID
        // ASC、
        // サービス開始時間 ASC、サービス終了時間 ASC
        // 【変数】.利用票明細情報
        List<Gui01149Riyou> riyouOutList = new ArrayList<>();
        // 5.2.2.2.【繰り返し開始】(Loop1)：【変数】.検索行番号 > 0 の場合、下記処理を実施する。
        while (selIndex > 0) {
            // ・【変数】.親レコード番号 = 0
            Integer oyaLineNo = 0;
            // ラムダ用に変更不可能なコピーを作成します
            final Integer currentOyaLineNo = oyaLineNo;
            // 検索条件: 親レコード番号 > 【変数】.親レコード番号
            List<Gui01149Riyou> riyouOyaLineNoList = riyouVarList.stream()
                    .filter(item -> CommonDtoUtil.strValToInt(item.getOyaLineNo()) > currentOyaLineNo)
                    .collect(Collectors.toList());
            // ソート順:【変数】.ソート順1
            riyouOyaLineNoList = riyouOyaLineNoList.stream()
                    .sorted(Comparator.comparing(Gui01149Riyou::getOyaLineNo)
                            .thenComparing(Comparator.comparing(Gui01149Riyou::getGouseiSikKbn))
                            .thenComparing(Comparator.comparing(Gui01149Riyou::getSvcode))
                            .thenComparing(Comparator.comparing(Gui01149Riyou::getSvItemCd)))
                    .collect(Collectors.toList());
            // 【変数】.検索行番号 = 以下の検索条件を満たす「【変数】.利用票明細情報」の一番目のデータのindex
            selIndex = CollectionUtils.size(riyouOyaLineNoList);
            // ・【変数】.検索行番号 + 1 > 0 の場合、※検索条件を満たすデータが存在する場合
            if (selIndex > 0) {
                // 【変数】.親レコード番号 = 【変数】.利用票明細情報[【変数】.検索行番号].親レコード番号
                oyaLineNo = CommonDtoUtil.strValToInt(riyouOyaLineNoList.getFirst().getOyaLineNo());
                // 【変数】.サービス開始時間 = 【変数】.利用票明細情報[【変数】.検索行番号].サービス開始時間
                String svStartTime = riyouOyaLineNoList.getFirst().getSvStartTime();
                // 【変数】.サービス終了時間 = 【変数】.利用票明細情報[【変数】.検索行番号].サービス終了時間
                String svEndTime = riyouOyaLineNoList.getFirst().getSvEndTime();
                // ラムダ用に変更不可能なコピーを作成します
                final Integer currentOyaLineNo2 = oyaLineNo;
                // 【変数】.利用票明細情報_Filter = 【変数】.利用票明細情報に以下の条件のデータを洗い出す。
                List<Gui01149Riyou> riyouFilterList = riyouVarList.stream()
                        .filter(item -> CommonDtoUtil.strValToInt(item.getOyaLineNo()) == currentOyaLineNo2)
                        .collect(Collectors.toList());
                // ソート順:【変数】.ソート順1
                riyouFilterList = riyouFilterList.stream()
                        .sorted(Comparator.comparing(Gui01149Riyou::getOyaLineNo)
                                .thenComparing(Comparator.comparing(Gui01149Riyou::getGouseiSikKbn))
                                .thenComparing(Comparator.comparing(Gui01149Riyou::getSvcode))
                                .thenComparing(Comparator.comparing(Gui01149Riyou::getSvItemCd)))
                        .collect(Collectors.toList());
                // ・【繰り返し開始】(Loop2):【変数】.利用票明細情報_Filterをループする
                for (Gui01149Riyou riyouFilter : riyouFilterList) {
                    // 【変数】.利用票明細情報_Filter.サービス開始時間 = 【変数】.サービス開始時間
                    riyouFilter.setSvStartTime(svStartTime);
                    // 【変数】.利用票明細情報_Filter.サービス終了時間 = 【変数】.サービス終了時間
                    riyouFilter.setSvEndTime(svEndTime);
                    // 処遇改善加算かどうかを判定する
                    Integer isShoguukaizenmkasanRst = kghCmnF01Logic
                            .isShoguuKaizenKasan(teikyouYm + CommonConstants.STRING_FIRST_DAY, riyouFilter.getScode());
                    // 上記結果 > 0の場合、
                    if (isShoguukaizenmkasanRst > 0) {
                        // 【変数】.利用票明細情報_Filter.保険データID = 9
                        riyouFilter.setDmySeqNo(CommonConstants.NUM_STR_9);
                    }
                }
                // 【変数】.利用票明細情報_Filter に【変数】.利用票明細情報を反映させる
                riyouOutList.addAll(riyouFilterList);
            }
        }
        // 【変数】.利用票明細情報は以下のソートで並べ替えられる。
        // ソート順:【変数】.ソート順2
        riyouOutList = riyouOutList.stream()
                .sorted(Comparator.comparing(Gui01149Riyou::getSortNo)
                        .thenComparing(Comparator.comparing(Gui01149Riyou::getDmyStartTime))
                        .thenComparing(Comparator.comparing(Gui01149Riyou::getDmyEndTime))
                        .thenComparing(Comparator.comparing(Gui01149Riyou::getSvtype))
                        .thenComparing(Comparator.comparing(Gui01149Riyou::getSvJigyoId))
                        .thenComparing(Comparator.comparing(Gui01149Riyou::getCmpOyaLineNo))
                        .thenComparing(Comparator.comparing(Gui01149Riyou::getGouseiSikKbn))
                        .thenComparing(Comparator.comparing(Gui01149Riyou::getDmySeqNo))
                        .thenComparing(Comparator.comparing(Gui01149Riyou::getSvcode))
                        .thenComparing(Comparator.comparing(Gui01149Riyou::getSvItemCd))
                        .thenComparing(Comparator.comparing(Gui01149Riyou::getSvStartTime))
                        .thenComparing(Comparator.comparing(Gui01149Riyou::getSvEndTime)))
                .collect(Collectors.toList());
        return riyouOutList;
    }

    /**
     * 利用票明細情報に上記取得した画面用明細情報を格納する
     * 
     * @param kghCmnSimuObject1LineOutEntity 入力パラメータ。
     * @return Gui01149Riyou
     */
    private Gui01149Riyou setRiyouValue(KghCmnSimuObject1LineOutEntity kghCmnSimuObject1LineOutEntity) {
        Gui01149Riyou riyou1149 = new Gui01149Riyou();
        riyou1149.setShienId(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getShienId()));
        riyou1149.setUserid(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getUserid()));
        riyou1149.setYymmYm(kghCmnSimuObject1LineOutEntity.getYymmYm());
        riyou1149.setYymmD(kghCmnSimuObject1LineOutEntity.getYymmD());
        riyou1149.setSvJigyoId(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getSvJigyoId()));
        riyou1149.setSvItemCd(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getSvItemCd()));
        riyou1149.setTermid(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getTermid()));
        riyou1149.setSvtype(kghCmnSimuObject1LineOutEntity.getSvtype());
        riyou1149.setSvcode(kghCmnSimuObject1LineOutEntity.getSvcode());
        riyou1149.setScode(kghCmnSimuObject1LineOutEntity.getScode());
        riyou1149.setSvStartTime(kghCmnSimuObject1LineOutEntity.getSvStartTime());
        riyou1149.setSvEndTime(kghCmnSimuObject1LineOutEntity.getSvEndTime());
        riyou1149.setSvTani(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getSvTani()));
        riyou1149.setYDay01(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay01()));
        riyou1149.setYDay02(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay02()));
        riyou1149.setYDay03(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay03()));
        riyou1149.setYDay04(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay04()));
        riyou1149.setYDay05(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay05()));
        riyou1149.setYDay06(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay06()));
        riyou1149.setYDay07(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay07()));
        riyou1149.setYDay08(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay08()));
        riyou1149.setYDay09(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay09()));
        riyou1149.setYDay10(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay10()));
        riyou1149.setYDay11(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay11()));
        riyou1149.setYDay12(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay12()));
        riyou1149.setYDay13(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay13()));
        riyou1149.setYDay14(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay14()));
        riyou1149.setYDay15(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay15()));
        riyou1149.setYDay16(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay16()));
        riyou1149.setYDay17(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay17()));
        riyou1149.setYDay18(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay18()));
        riyou1149.setYDay19(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay19()));
        riyou1149.setYDay20(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay20()));
        riyou1149.setYDay21(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay21()));
        riyou1149.setYDay22(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay22()));
        riyou1149.setYDay23(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay23()));
        riyou1149.setYDay24(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay24()));
        riyou1149.setYDay25(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay25()));
        riyou1149.setYDay26(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay26()));
        riyou1149.setYDay27(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay27()));
        riyou1149.setYDay28(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay28()));
        riyou1149.setYDay29(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay29()));
        riyou1149.setYDay30(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay30()));
        riyou1149.setYDay31(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYDay31()));
        riyou1149.setYTotal(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYTotal()));
        riyou1149.setYRentalF(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYRentalF()));
        riyou1149.setShoukiboKbn(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getShoukiboKbn()));
        riyou1149.setYOv30Fl(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getYOv30Fl()));
        riyou1149.setJDay01(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay01()));
        riyou1149.setJDay02(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay02()));
        riyou1149.setJDay03(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay03()));
        riyou1149.setJDay04(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay04()));
        riyou1149.setJDay05(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay05()));
        riyou1149.setJDay06(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay06()));
        riyou1149.setJDay07(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay07()));
        riyou1149.setJDay08(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay08()));
        riyou1149.setJDay09(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay09()));
        riyou1149.setJDay10(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay10()));
        riyou1149.setJDay11(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay11()));
        riyou1149.setJDay12(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay12()));
        riyou1149.setJDay13(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay13()));
        riyou1149.setJDay14(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay14()));
        riyou1149.setJDay15(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay15()));
        riyou1149.setJDay16(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay16()));
        riyou1149.setJDay17(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay17()));
        riyou1149.setJDay18(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay18()));
        riyou1149.setJDay19(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay19()));
        riyou1149.setJDay20(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay20()));
        riyou1149.setJDay21(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay21()));
        riyou1149.setJDay22(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay22()));
        riyou1149.setJDay23(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay23()));
        riyou1149.setJDay24(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay24()));
        riyou1149.setJDay25(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay25()));
        riyou1149.setJDay26(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay26()));
        riyou1149.setJDay27(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay27()));
        riyou1149.setJDay28(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay28()));
        riyou1149.setJDay29(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay29()));
        riyou1149.setJDay30(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay30()));
        riyou1149.setJDay31(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJDay31()));
        riyou1149.setJTotal(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJTotal()));
        riyou1149.setJRentalF(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJRentalF()));
        riyou1149.setJOv30Fl(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJOv30Fl()));
        riyou1149.setTensouTime(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getTensouTime()));
        riyou1149.setEdaBack(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getEdaBack()));
        riyou1149.setSortNo(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getSortNo()));
        riyou1149.setHenkouTime(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getHenkouTime()));
        riyou1149.setTensouFl(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getTensouFl()));
        riyou1149.setFygId(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getFygId()));
        riyou1149.setMax(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getMax()));
        riyou1149.setYoteiZumiFlg(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getMax()));
        riyou1149.setJissekiZumiFlg(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getJissekiZumiFlg()));
        riyou1149.setGouseiSikKbn(kghCmnSimuObject1LineOutEntity.getGouseiSikKbn());
        riyou1149.setGenTaiKbn(kghCmnSimuObject1LineOutEntity.getGenTaiKbn());
        riyou1149.setOyaLineNo(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getOyaLineNo()));
        riyou1149.setSanteiTani(kghCmnSimuObject1LineOutEntity.getSanteiTani());
        riyou1149.setKikanJiki(kghCmnSimuObject1LineOutEntity.getKikanJiki());
        riyou1149.setKaisuNisu(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getKaisuNisu()));
        riyou1149.setTenkintype(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getTenkintype()));
        riyou1149.setKiteiSu(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getDefaultVal()));

        riyou1149.setEdaNo(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getEdaNo()));
        riyou1149.setDmyStartTime(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getDmyStartTime()));
        riyou1149.setDmyEndTime(CommonDtoUtil.objValToString(kghCmnSimuObject1LineOutEntity.getDmyEndTime()));

        return riyou1149;
    }

    /**
     * 別表明細情報に上記別表明細情報検索結果を設定する。
     * 
     * @param entity 入力パラメータ。
     * @return Gui01149RiyouBeppyo
     */
    private Gui01149RiyouBeppyo setRiyouBeppyoValue(KghCmnSimuObject1List202504OutEntity entity) {
        Gui01149RiyouBeppyo beppyo = new Gui01149RiyouBeppyo();
        beppyo.setDmyJigyoNameKnj(entity.getDmyFormalnameKnj());
        beppyo.setDmyJigyoNumber(entity.getDmyJigyoNumber());
        beppyo.setDmyFormalnameKnj(entity.getDmyFormalnameKnj());
        beppyo.setScode(entity.getScode());
        beppyo.setTensu(CommonDtoUtil.objValToString(entity.getTensu()));
        beppyo.setWaribikiRitu(CommonDtoUtil.objValToString(entity.getWaribikiRitu()));
        beppyo.setWaribikiTen(CommonDtoUtil.objValToString(entity.getWaribikiTen()));
        beppyo.setKaisu(CommonDtoUtil.objValToString(entity.getKaisu()));
        beppyo.setSvTensu(CommonDtoUtil.objValToString(entity.getSvTensu()));
        beppyo.setSTensuOver(CommonDtoUtil.objValToString(entity.getSTensuOver()));
        beppyo.setSTensu(CommonDtoUtil.objValToString(entity.getSTensu()));
        beppyo.setKTensuOver(CommonDtoUtil.objValToString(entity.getKTensuOver()));
        beppyo.setKTensu(CommonDtoUtil.objValToString(entity.getKTensu()));
        beppyo.setTanka(CommonDtoUtil.objValToString(entity.getTanka()));
        beppyo.setHiyouSougaku(CommonDtoUtil.objValToString(entity.getHiyouSougaku()));
        beppyo.setKyufuRitu(CommonDtoUtil.objValToString(entity.getKyufuRitu()));
        beppyo.setHKyufugaku(CommonDtoUtil.objValToString(entity.getHKyufugaku()));
        beppyo.setTHutanTanka(CommonDtoUtil.objValToString(entity.getTHutanTanka()));
        beppyo.setHutanH(CommonDtoUtil.objValToString(entity.getHutanH()));
        beppyo.setHutanJ(CommonDtoUtil.objValToString(entity.getHutanJ()));
        beppyo.setShienId(CommonDtoUtil.objValToString(entity.getShienId()));
        beppyo.setUserid(CommonDtoUtil.objValToString(entity.getUserid()));
        beppyo.setYymmYm(entity.getYymmYm());
        beppyo.setYymmD(entity.getYymmD());
        beppyo.setSvJigyoId(CommonDtoUtil.objValToString(entity.getSvJigyoId()));
        beppyo.setSvItemCd(CommonDtoUtil.objValToString(entity.getSvItemCd()));
        beppyo.setEdaNo(CommonDtoUtil.objValToString(entity.getEdaNo()));
        beppyo.setSvType(entity.getSvtype());
        beppyo.setSvcode(entity.getSvcode());
        beppyo.setTotalF(CommonDtoUtil.objValToString(entity.getTotalF()));
        beppyo.setKasanF(CommonDtoUtil.objValToString(entity.getKasanF()));
        beppyo.setOv30Fl(CommonDtoUtil.objValToString(entity.getOv30Fl()));
        beppyo.setKyufuDiffF(CommonDtoUtil.objValToString(entity.getKyufuDiffF()));
        beppyo.setDmyCalcSort(CommonDtoUtil.objValToString(entity.getDmyCalcSort()));
        beppyo.setFygId(CommonDtoUtil.objValToString(entity.getFygId()));
        beppyo.setFygShouhinKnj(entity.getDmyFygShouhinKnj());
        beppyo.setFygTekiyouCode(entity.getDmyFygTekiyouCode());

        return beppyo;
    }

    /**
     * 値設定
     * 
     * @param beppyo 別表明細情報
     */
    private void setValue(Gui01149RiyouBeppyo beppyo) {
        // ・レスポンスパラメータ.別表明細情報.合計フラグ = 1の場合
        if (CommonConstants.STR_1.equals(beppyo.getTotalF())) {
            // 別表明細情報.費用総額_hidden
            beppyo.setCHiyouSougaku(beppyo.getHiyouSougaku());
            // 別表明細情報.介護保険給付額_hidden
            beppyo.setCHKyufugaku(beppyo.getHKyufugaku());
            // 利用者負担額・保険給付対象分_hidden
            beppyo.setCHutanH(beppyo.getHutanH());
        }
        // ・以外の場合
        else {
            // 別表明細情報.費用総額_hidden
            beppyo.setCHiyouSougaku(CommonConstants.STR_0);
            // 別表明細情報.介護保険給付額_hidden
            beppyo.setCHKyufugaku(CommonConstants.STR_0);
            // 利用者負担額・保険給付対象分_hidden
            beppyo.setCHutanH(CommonConstants.STR_0);
        }
        // ・レスポンスパラメータ.別表明細情報.合計フラグ = 1
        // and レスポンスパラメータ.別表明細情報.加算フラグ = 0
        // and レスポンスパラメータ.別表明細情報.３０日超過フラグ = 0の場合
        if (CommonConstants.STR_1.equals(beppyo.getTotalF()) && CommonConstants.STR_0.equals(beppyo.getKasanF())
                && CommonConstants.STR_0.equals(beppyo.getOv30Fl())) {
            // 合計単位数_hidden
            beppyo.setCSvTensu(beppyo.getSvTensu());
            // 種類限度外単位数_hidden
            beppyo.setCSTensuOver(beppyo.getSTensuOver());
            // 別表明細情報.種類限度額内単位数_hidden
            beppyo.setCSTensu(beppyo.getSTensu());
            // 区分支給限度外単位数_hidden
            beppyo.setCKTensuOver(beppyo.getKTensuOver());
            // 区分支給限度額内単位数_hidden
            beppyo.setCKTensu(beppyo.getKTensu());
        } else {
            // 合計単位数_hidden
            beppyo.setCSvTensu(CommonConstants.STR_0);
            // 種類限度外単位数_hidden
            beppyo.setCSTensuOver(CommonConstants.STR_0);
            // 別表明細情報.種類限度額内単位数_hidden
            beppyo.setCSTensu(CommonConstants.STR_0);
            // 区分支給限度外単位数_hidden
            beppyo.setCKTensuOver(CommonConstants.STR_0);
            // 区分支給限度額内単位数_hidden
            beppyo.setCKTensu(CommonConstants.STR_0);
        }

    }

    /**
     * レスポンスパラメータ.種類別限度情報に設定する
     * 
     * @param entity 入力パラメータ。
     * @return Gui01213SyuruiGendoData
     */
    private Gui01213SyuruiGendoData setGendoData(KghCmnSimuObject1SyuruiSougouOutEntity entity) {
        Gui01213SyuruiGendoData gui01213SyuruiGendoData = new Gui01213SyuruiGendoData();
        gui01213SyuruiGendoData.setShienId(CommonDtoUtil.objValToString(entity.getShienId()));
        gui01213SyuruiGendoData.setUserid(CommonDtoUtil.objValToString(entity.getUserid()));
        gui01213SyuruiGendoData.setYymmYm(entity.getYymmYm());
        gui01213SyuruiGendoData.setYymmD(entity.getYymmD());
        gui01213SyuruiGendoData.setSvGendo1(CommonDtoUtil.objValToString(entity.getSvGendo1()));
        gui01213SyuruiGendoData.setSvGendo2(CommonDtoUtil.objValToString(entity.getSvGendo2()));
        gui01213SyuruiGendoData.setSvGendo3(CommonDtoUtil.objValToString(entity.getSvGendo3()));
        gui01213SyuruiGendoData.setSvGendo4(CommonDtoUtil.objValToString(entity.getSvGendo4()));
        gui01213SyuruiGendoData.setSvGendo5(CommonDtoUtil.objValToString(entity.getSvGendo5()));
        gui01213SyuruiGendoData.setSvGendo6(CommonDtoUtil.objValToString(entity.getSvGendo6()));
        gui01213SyuruiGendoData.setSvGendo7(CommonDtoUtil.objValToString(entity.getSvGendo7()));
        gui01213SyuruiGendoData.setSvGendo8(CommonDtoUtil.objValToString(entity.getSvGendo8()));
        gui01213SyuruiGendoData.setSvGendo9(CommonDtoUtil.objValToString(entity.getSvGendo9()));
        gui01213SyuruiGendoData.setSvGendo10(CommonDtoUtil.objValToString(entity.getSvGendo10()));
        gui01213SyuruiGendoData.setSvGendo11(CommonDtoUtil.objValToString(entity.getSvGendo11()));
        gui01213SyuruiGendoData.setSvGendo12(CommonDtoUtil.objValToString(entity.getSvGendo12()));
        gui01213SyuruiGendoData.setSvTotal1(CommonDtoUtil.objValToString(entity.getSvTotal1()));
        gui01213SyuruiGendoData.setSvTotal2(CommonDtoUtil.objValToString(entity.getSvTotal2()));
        gui01213SyuruiGendoData.setSvTotal3(CommonDtoUtil.objValToString(entity.getSvTotal3()));
        gui01213SyuruiGendoData.setSvTotal4(CommonDtoUtil.objValToString(entity.getSvTotal4()));
        gui01213SyuruiGendoData.setSvTotal5(CommonDtoUtil.objValToString(entity.getSvTotal5()));
        gui01213SyuruiGendoData.setSvTotal6(CommonDtoUtil.objValToString(entity.getSvTotal6()));
        gui01213SyuruiGendoData.setSvTotal7(CommonDtoUtil.objValToString(entity.getSvTotal7()));
        gui01213SyuruiGendoData.setSvTotal8(CommonDtoUtil.objValToString(entity.getSvTotal8()));
        gui01213SyuruiGendoData.setSvTotal9(CommonDtoUtil.objValToString(entity.getSvTotal9()));
        gui01213SyuruiGendoData.setSvTotal10(CommonDtoUtil.objValToString(entity.getSvTotal10()));
        gui01213SyuruiGendoData.setSvTotal11(CommonDtoUtil.objValToString(entity.getSvTotal11()));
        gui01213SyuruiGendoData.setSvTotal12(CommonDtoUtil.objValToString(entity.getSvTotal12()));
        gui01213SyuruiGendoData.setSvOver1(CommonDtoUtil.objValToString(entity.getSvOver1()));
        gui01213SyuruiGendoData.setSvOver2(CommonDtoUtil.objValToString(entity.getSvOver2()));
        gui01213SyuruiGendoData.setSvOver3(CommonDtoUtil.objValToString(entity.getSvOver3()));
        gui01213SyuruiGendoData.setSvOver4(CommonDtoUtil.objValToString(entity.getSvOver4()));
        gui01213SyuruiGendoData.setSvOver5(CommonDtoUtil.objValToString(entity.getSvOver5()));
        gui01213SyuruiGendoData.setSvOver6(CommonDtoUtil.objValToString(entity.getSvOver6()));
        gui01213SyuruiGendoData.setSvOver7(CommonDtoUtil.objValToString(entity.getSvOver7()));
        gui01213SyuruiGendoData.setSvOver8(CommonDtoUtil.objValToString(entity.getSvOver8()));
        gui01213SyuruiGendoData.setSvOver9(CommonDtoUtil.objValToString(entity.getSvOver9()));
        gui01213SyuruiGendoData.setSvOver10(CommonDtoUtil.objValToString(entity.getSvOver10()));
        gui01213SyuruiGendoData.setSvOver11(CommonDtoUtil.objValToString(entity.getSvOver11()));
        gui01213SyuruiGendoData.setSvOver12(CommonDtoUtil.objValToString(entity.getSvOver12()));
        gui01213SyuruiGendoData.setSvName1(CommonDtoUtil.objValToString(entity.getSvName1()));
        gui01213SyuruiGendoData.setSvName2(CommonDtoUtil.objValToString(entity.getSvName1()));
        gui01213SyuruiGendoData.setSvName3(CommonDtoUtil.objValToString(entity.getSvName1()));
        gui01213SyuruiGendoData.setSvName4(CommonDtoUtil.objValToString(entity.getSvName1()));
        gui01213SyuruiGendoData.setSvName5(CommonDtoUtil.objValToString(entity.getSvName1()));
        gui01213SyuruiGendoData.setSvName6(CommonDtoUtil.objValToString(entity.getSvName1()));
        gui01213SyuruiGendoData.setSvName7(CommonDtoUtil.objValToString(entity.getSvName1()));
        gui01213SyuruiGendoData.setSvName8(CommonDtoUtil.objValToString(entity.getSvName1()));
        gui01213SyuruiGendoData.setSvName9(CommonDtoUtil.objValToString(entity.getSvName1()));
        gui01213SyuruiGendoData.setSvName10(CommonDtoUtil.objValToString(entity.getSvName1()));
        gui01213SyuruiGendoData.setSvName11(CommonDtoUtil.objValToString(entity.getSvName1()));
        gui01213SyuruiGendoData.setSvName12(CommonDtoUtil.objValToString(entity.getSvName1()));
        return gui01213SyuruiGendoData;
    }

    /**
     * レスポンスパラメータ.利用票明細情報に設定する
     * 
     * @param entity 入力パラメータ。
     * @return Gui01149Riyou
     */
    private Gui01149Riyou setRiyouListInfo(KghCmnSimuObject1LineOutEntity entity) {
        Gui01149Riyou gui01149Riyou = new Gui01149Riyou();
        gui01149Riyou.setDmyJigyoNameKnj(entity.getDmyJigyoNameKnj());
        gui01149Riyou.setDmyFormalnameKnj(entity.getDmyFormalnameKnj());
        gui01149Riyou.setDmy0SvTani(CommonDtoUtil.objValToString(entity.getDmy0SvTani()));
        gui01149Riyou.setDmy0SvTanj(CommonDtoUtil.objValToString(entity.getDmy0SvTanj()));
        gui01149Riyou.setDmyRHasu(CommonDtoUtil.objValToString(entity.getDmyRHasu()));
        gui01149Riyou.setSvTani(CommonDtoUtil.objValToString(entity.getSvTani()));
        gui01149Riyou.setDmySvTaniVisible(CommonDtoUtil.objValToString(entity.getDmySvTaniVisible()));
        gui01149Riyou.setDmyOneOfMonth(CommonDtoUtil.objValToString(entity.getDmyOneOfMonth()));
        gui01149Riyou.setSvtype(entity.getSvtype());
        gui01149Riyou.setSvcode(entity.getSvcode());
        gui01149Riyou.setScode(entity.getScode());
        gui01149Riyou.setSvStartTime(entity.getSvStartTime());
        gui01149Riyou.setSvEndTime(entity.getSvEndTime());
        gui01149Riyou.setDmySeqNo(CommonDtoUtil.objValToString(entity.getDmySeqNo()));
        gui01149Riyou.setYDay01(CommonDtoUtil.objValToString(entity.getYDay01()));
        gui01149Riyou.setYDay02(CommonDtoUtil.objValToString(entity.getYDay02()));
        gui01149Riyou.setYDay03(CommonDtoUtil.objValToString(entity.getYDay03()));
        gui01149Riyou.setYDay04(CommonDtoUtil.objValToString(entity.getYDay04()));
        gui01149Riyou.setYDay05(CommonDtoUtil.objValToString(entity.getYDay05()));
        gui01149Riyou.setYDay06(CommonDtoUtil.objValToString(entity.getYDay06()));
        gui01149Riyou.setYDay07(CommonDtoUtil.objValToString(entity.getYDay07()));
        gui01149Riyou.setYDay08(CommonDtoUtil.objValToString(entity.getYDay08()));
        gui01149Riyou.setYDay09(CommonDtoUtil.objValToString(entity.getYDay09()));
        gui01149Riyou.setYDay10(CommonDtoUtil.objValToString(entity.getYDay10()));
        gui01149Riyou.setYDay11(CommonDtoUtil.objValToString(entity.getYDay11()));
        gui01149Riyou.setYDay12(CommonDtoUtil.objValToString(entity.getYDay12()));
        gui01149Riyou.setYDay13(CommonDtoUtil.objValToString(entity.getYDay13()));
        gui01149Riyou.setYDay14(CommonDtoUtil.objValToString(entity.getYDay14()));
        gui01149Riyou.setYDay15(CommonDtoUtil.objValToString(entity.getYDay15()));
        gui01149Riyou.setYDay16(CommonDtoUtil.objValToString(entity.getYDay16()));
        gui01149Riyou.setYDay17(CommonDtoUtil.objValToString(entity.getYDay17()));
        gui01149Riyou.setYDay18(CommonDtoUtil.objValToString(entity.getYDay18()));
        gui01149Riyou.setYDay19(CommonDtoUtil.objValToString(entity.getYDay19()));
        gui01149Riyou.setYDay20(CommonDtoUtil.objValToString(entity.getYDay20()));
        gui01149Riyou.setYDay21(CommonDtoUtil.objValToString(entity.getYDay21()));
        gui01149Riyou.setYDay22(CommonDtoUtil.objValToString(entity.getYDay22()));
        gui01149Riyou.setYDay23(CommonDtoUtil.objValToString(entity.getYDay23()));
        gui01149Riyou.setYDay24(CommonDtoUtil.objValToString(entity.getYDay24()));
        gui01149Riyou.setYDay25(CommonDtoUtil.objValToString(entity.getYDay25()));
        gui01149Riyou.setYDay26(CommonDtoUtil.objValToString(entity.getYDay26()));
        gui01149Riyou.setYDay27(CommonDtoUtil.objValToString(entity.getYDay27()));
        gui01149Riyou.setYDay28(CommonDtoUtil.objValToString(entity.getYDay28()));
        gui01149Riyou.setYDay29(CommonDtoUtil.objValToString(entity.getYDay29()));
        gui01149Riyou.setYDay30(CommonDtoUtil.objValToString(entity.getYDay30()));
        gui01149Riyou.setYDay31(CommonDtoUtil.objValToString(entity.getYDay31()));
        gui01149Riyou.setYTotal(CommonDtoUtil.objValToString(entity.getYTotal()));
        gui01149Riyou.setYRentalF(CommonDtoUtil.objValToString(entity.getYRentalF()));
        gui01149Riyou.setShoukiboKbn(CommonDtoUtil.objValToString(entity.getShoukiboKbn()));
        gui01149Riyou.setYOv30Fl(CommonDtoUtil.objValToString(entity.getYOv30Fl()));
        gui01149Riyou.setJDay01(CommonDtoUtil.objValToString(entity.getJDay01()));
        gui01149Riyou.setJDay02(CommonDtoUtil.objValToString(entity.getJDay02()));
        gui01149Riyou.setJDay03(CommonDtoUtil.objValToString(entity.getJDay03()));
        gui01149Riyou.setJDay04(CommonDtoUtil.objValToString(entity.getJDay04()));
        gui01149Riyou.setJDay05(CommonDtoUtil.objValToString(entity.getJDay05()));
        gui01149Riyou.setJDay06(CommonDtoUtil.objValToString(entity.getJDay06()));
        gui01149Riyou.setJDay07(CommonDtoUtil.objValToString(entity.getJDay07()));
        gui01149Riyou.setJDay08(CommonDtoUtil.objValToString(entity.getJDay08()));
        gui01149Riyou.setJDay09(CommonDtoUtil.objValToString(entity.getJDay09()));
        gui01149Riyou.setJDay10(CommonDtoUtil.objValToString(entity.getJDay10()));
        gui01149Riyou.setJDay11(CommonDtoUtil.objValToString(entity.getJDay11()));
        gui01149Riyou.setJDay12(CommonDtoUtil.objValToString(entity.getJDay12()));
        gui01149Riyou.setJDay13(CommonDtoUtil.objValToString(entity.getJDay13()));
        gui01149Riyou.setJDay14(CommonDtoUtil.objValToString(entity.getJDay14()));
        gui01149Riyou.setJDay15(CommonDtoUtil.objValToString(entity.getJDay15()));
        gui01149Riyou.setJDay16(CommonDtoUtil.objValToString(entity.getJDay16()));
        gui01149Riyou.setJDay17(CommonDtoUtil.objValToString(entity.getJDay17()));
        gui01149Riyou.setJDay18(CommonDtoUtil.objValToString(entity.getJDay18()));
        gui01149Riyou.setJDay19(CommonDtoUtil.objValToString(entity.getJDay19()));
        gui01149Riyou.setJDay20(CommonDtoUtil.objValToString(entity.getJDay20()));
        gui01149Riyou.setJDay21(CommonDtoUtil.objValToString(entity.getJDay21()));
        gui01149Riyou.setJDay22(CommonDtoUtil.objValToString(entity.getJDay22()));
        gui01149Riyou.setJDay23(CommonDtoUtil.objValToString(entity.getJDay23()));
        gui01149Riyou.setJDay24(CommonDtoUtil.objValToString(entity.getJDay24()));
        gui01149Riyou.setJDay25(CommonDtoUtil.objValToString(entity.getJDay25()));
        gui01149Riyou.setJDay26(CommonDtoUtil.objValToString(entity.getJDay26()));
        gui01149Riyou.setJDay27(CommonDtoUtil.objValToString(entity.getJDay27()));
        gui01149Riyou.setJDay28(CommonDtoUtil.objValToString(entity.getJDay28()));
        gui01149Riyou.setJDay29(CommonDtoUtil.objValToString(entity.getJDay29()));
        gui01149Riyou.setJDay30(CommonDtoUtil.objValToString(entity.getJDay30()));
        gui01149Riyou.setJDay31(CommonDtoUtil.objValToString(entity.getJDay31()));
        gui01149Riyou.setJTotal(CommonDtoUtil.objValToString(entity.getJTotal()));
        gui01149Riyou.setJRentalF(CommonDtoUtil.objValToString(entity.getJRentalF()));
        gui01149Riyou.setJOv30Fl(CommonDtoUtil.objValToString(entity.getJOv30Fl()));
        gui01149Riyou.setTensouTime(CommonDtoUtil.objValToString(entity.getTensouTime()));
        gui01149Riyou.setEdaBack(CommonDtoUtil.objValToString(entity.getEdaBack()));
        gui01149Riyou.setSortNo(CommonDtoUtil.objValToString(entity.getSortNo()));
        gui01149Riyou.setHenkouTime(CommonDtoUtil.objValToString(entity.getHenkouTime()));
        gui01149Riyou.setTensouFl(CommonDtoUtil.objValToString(entity.getTensouFl()));
        gui01149Riyou.setFygId(CommonDtoUtil.objValToString(entity.getFygId()));
        gui01149Riyou.setMax(CommonDtoUtil.objValToString(entity.getMax()));
        gui01149Riyou.setYoteiZumiFlg(CommonDtoUtil.objValToString(entity.getYoteiZumiFlg()));
        gui01149Riyou.setJissekiZumiFlg(CommonDtoUtil.objValToString(entity.getJissekiZumiFlg()));
        gui01149Riyou.setGouseiSikKbn(entity.getGouseiSikKbn());
        gui01149Riyou.setGenTaiKbn(entity.getGenTaiKbn());
        gui01149Riyou.setOyaLineNo(CommonDtoUtil.objValToString(entity.getOyaLineNo()));
        gui01149Riyou.setSanteiTani(entity.getSanteiTani());
        gui01149Riyou.setKikanJiki(entity.getKikanJiki());
        gui01149Riyou.setKaisuNisu(CommonDtoUtil.objValToString(entity.getKaisuNisu()));
        gui01149Riyou.setTenkintype(CommonDtoUtil.objValToString(entity.getTenkintype()));
        gui01149Riyou.setKiteiSu(null);
        gui01149Riyou.setShienId(CommonDtoUtil.objValToString(entity.getShienId()));
        gui01149Riyou.setUserid(CommonDtoUtil.objValToString(entity.getUserid()));
        gui01149Riyou.setSvJigyoId(CommonDtoUtil.objValToString(entity.getSvJigyoId()));
        gui01149Riyou.setSvItemCd(CommonDtoUtil.objValToString(entity.getSvItemCd()));
        gui01149Riyou.setYymmYm(entity.getYymmYm());
        gui01149Riyou.setYymmD(entity.getYymmD());
        gui01149Riyou.setEdaNo(CommonDtoUtil.objValToString(entity.getEdaNo()));
        gui01149Riyou.setTermid(CommonDtoUtil.objValToString(entity.getTermid()));
        gui01149Riyou.setRiyouListUpdKbn(null);
        gui01149Riyou.setDmyStartTime(entity.getDmyStartTime());
        gui01149Riyou.setDmyEndTime(entity.getDmyEndTime());
        gui01149Riyou.setCmpOyaLineNo(CommonDtoUtil.objValToString(entity.getOyaLineNo()));
        List<String> colorYList = new ArrayList<>();
        List<String> colorJList = new ArrayList<>();
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY01()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY02()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY03()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY04()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY05()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY06()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY07()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY08()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY09()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY10()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY11()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY12()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY13()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY14()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY15()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY16()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY17()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY18()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY19()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY20()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY21()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY22()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY23()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY24()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY25()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY26()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY27()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY28()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY29()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY30()));
        colorYList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorY31()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ01()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ02()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ03()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ04()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ05()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ06()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ07()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ08()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ09()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ10()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ11()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ12()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ13()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ14()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ15()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ16()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ17()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ18()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ19()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ20()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ21()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ22()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ23()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ24()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ25()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ26()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ27()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ28()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ29()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ30()));
        colorJList.add(CommonDtoUtil.objValToString(entity.getDmyBgColorJ31()));
        gui01149Riyou.setDmyBgColorY(colorYList);
        gui01149Riyou.setDmyBgColorJ(colorJList);

        return gui01149Riyou;
    }

    /**
     * レスポンスパラメータ.利用者情報に設定する
     * 
     * @param list 入力パラメータ。
     * @return Gui01149Riyou
     */
    private List<Gui01149Riyoursha> set1213RiyourshaDataTo1149(List<Gui01213RiyourshaData> list) {
        List<Gui01149Riyoursha> riyourshaList = new ArrayList<>();
        for (Gui01213RiyourshaData data : list) {
            Gui01149Riyoursha gui01149Riyoursha = new Gui01149Riyoursha();
            gui01149Riyoursha.setRCompBase(data.getRCompBase());
            gui01149Riyoursha.setCreateYmd(data.getCreateYmd());
            gui01149Riyoursha.setYoteiZumiFlg(data.getYoteiZumiFlg());
            gui01149Riyoursha.setJissekiZumiFlg(data.getJissekiZumiFlg());
            gui01149Riyoursha.setKakuteiZumiFlg(data.getKakuteiZumiFlg());
            gui01149Riyoursha.setDmyKHokenNo(data.getKHokenCd());
            gui01149Riyoursha.setDmyKHokenKnj(data.getHHokenNo());
            gui01149Riyoursha.setDmyKaigodo(data.getYokaiKbn());
            gui01149Riyoursha.setDmyHenDate(data.getDmyhendate());
            gui01149Riyoursha.setDmyHHokenNo(data.getDmyhhokenno());
            gui01149Riyoursha.setDmyUserName(data.getDmyUserName());
            gui01149Riyoursha.setDmyHenKaigodo(data.getDmyHenKaigodo());
            gui01149Riyoursha.setDmyShikyuGaku(data.getGendoGaku());
            gui01149Riyoursha.setDmyTanto(data.getDmyTanto());
            gui01149Riyoursha.setDmyKibouGaku(data.getDmyKibouGaku());
            gui01149Riyoursha.setDmyKikanFr(data.getGendoStartYmd());
            gui01149Riyoursha.setDmyKikanTo(data.getGendoEndYmd());
            gui01149Riyoursha.setCmnTucPlanUseridHidden(data.getUserid());
            gui01149Riyoursha.setCmnTucPlanYymmYmHidden(data.getYymmYm());
            gui01149Riyoursha.setCmnTucPlanYymmDHidden(data.getYymmD());
            gui01149Riyoursha.setCmnTucPlanCreateYmdHidden(data.getCreateYmd());
            gui01149Riyoursha.setCmnTucPlanHHokenNoHidden(data.getCmnTucPlanHHokenNoHidden());
            gui01149Riyoursha.setDmyTantoIdHidden(data.getDmyTanto());
            gui01149Riyoursha.setCmnTucPlanKHokenCdHidden(data.getKHokenCd());
            gui01149Riyoursha.setCmnTucPlanSvTensuY(data.getSvTensuY());
            gui01149Riyoursha.setCmnTucPlanSvTensuJ(data.getSvTensuJ());
            gui01149Riyoursha.setShienId(data.getShienId());
            gui01149Riyoursha.setUserid(data.getUserid());
            gui01149Riyoursha.setYymmYm(data.getYymmYm());
            gui01149Riyoursha.setYymmD(data.getYymmD());
            gui01149Riyoursha.setDate1Ymd(data.getDate1Ymd());
            gui01149Riyoursha.setDate2Ymd(data.getDate2Ymd());
            gui01149Riyoursha.setBikouRiyouKnj(data.getBikouRiyouKnj());
            gui01149Riyoursha.setBikouTeikyouKnj(data.getBikouTeikyouKnj());
            gui01149Riyoursha.setBikouCalenderKnj(data.getBikouCalenderKnj());
            gui01149Riyoursha.setOv30St(data.getOv30St());
            gui01149Riyoursha.setOv30Ed(data.getOv30Ed());
            gui01149Riyoursha.setJissekiDays(data.getJissekiDays());
            gui01149Riyoursha.setDmySeqNo(data.getDmySeqNo());
            gui01149Riyoursha.setSimWaribikiRitu(data.getSimWaribikiRitu());
            gui01149Riyoursha.setRiyourshaDataUpdateKbn(data.getUpdateKbn());
            riyourshaList.add(gui01149Riyoursha);
        }
        return riyourshaList;
    }

}
