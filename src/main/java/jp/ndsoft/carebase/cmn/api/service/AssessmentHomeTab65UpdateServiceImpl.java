package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.StringUtils;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00802CpnTucGdlKan15Info;
import jp.ndsoft.carebase.cmn.api.logic.AssessmentHomeLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeSaveServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeTab65UpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeTab65UpdateServiceInDto;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdlRirekiMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRireki;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRirekiCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan15H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan15H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan15R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan15R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kan15H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kan15R3Mapper;

import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;

/**
 * @since 2025.04.07
 * <AUTHOR>
 * @apiNote GUI00802_［アセスメント］画面（居宅）（6⑤） データ保存
 */
@Service
public class AssessmentHomeTab65UpdateServiceImpl
        extends
        UpdateServiceImpl<AssessmentHomeTab65UpdateServiceInDto, AssessmentHomeTab65UpdateServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 居宅アセスメント履歴の更新詳細 DAO */
    @Autowired
    private CpnTucGdlRirekiMapper cpnTucGdlRirekiMapper;

    /** ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂） */
    @Autowired
    private CpnTucGdl4Kan15H21Mapper cpnTucGdl4Kan15H21Mapper;

    /** ＧＬ＿⑤社会生活（への適応）力（R３改訂） */
    @Autowired
    private CpnTucGdl5Kan15R3Mapper cpnTucGdl5Kan15R3Mapper;

    /** ［アセスメント］画面（居宅）画面のロジッククラス */
    @Autowired
    private AssessmentHomeLogic assessmentHomeLogic;

    @Autowired
    private PlatformTransactionManager transactionManager;

    /**
     * ［アセスメント］画面（居宅）（6⑤） データ保存する
     * 
     * @param inDto アセスメント込データ保存入力DTO
     * @return アセスメントデータ保存出力DTO
     */
    @Override
    protected AssessmentHomeTab65UpdateServiceOutDto mainProcess(AssessmentHomeTab65UpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        AssessmentHomeTab65UpdateServiceOutDto outDto = new AssessmentHomeTab65UpdateServiceOutDto();
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        /*
         * ===============2. バリデーションチェックを行う。===============
         * 
         */
        boolean resFlg = assessmentHomeLogic.documentPerformValidationCheck(loginDto);
        // 2.2.4. 【変数】.処理結果が「false：失敗」の場合
        if (!resFlg) {
            // 2.2.4. 【変数】.処理結果が「false：失敗」の場合、下記返却する情報を設定して、後続処理を飛ばして、API処理を正常終了とする。
            // レスポンス.計画期間ID ： リクエストパラメータ.計画期間ID
            outDto.setSc1Id(loginDto.getSc1Id());
            // レスポンス.アセスメントID ： リクエストパラメータ.アセスメントID
            outDto.setGdlId(loginDto.getGdlId());
            // レスポンス.エラー区分: "1
            outDto.setErrKbn(CommonConstants.E_DOC_CHECK_ERR);
            return outDto;
        }
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transactionB = transactionManager.getTransaction(def);
        try {
            // ［アセスメント］画面（居宅）（6⑤） データ保存
            outDto = mainProcessMealUpdate(inDto);
            transactionManager.commit(transactionB);
        } catch (Exception e) {
            transactionManager.rollback(transactionB);
            throw e;
        }

        // e-文書_ケアチェック表１のリクエストパラメータ
        TransactionStatus transactionC = transactionManager.getTransaction(def);
        try {
            mainProcessEdocOut(inDto, outDto);
            transactionManager.commit(transactionC);
        } catch (Exception e) {
            transactionManager.rollback(transactionC);
            throw e;
        }

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * API定義 (e文書出力)
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected void mainProcessEdocOut(
            AssessmentHomeTab65UpdateServiceInDto inDto, AssessmentHomeTab65UpdateServiceOutDto outDto)
            throws Exception {
        // 入力dtoをLoginDtoにコピーする
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);

        /*
         * ===============e文書出力===============
         * 
         */
        // e文書出力
        String errKbn = assessmentHomeLogic.getPrint(loginDto, outDto.getSc1Id());
        outDto.setErrKbn(errKbn);
    }

    /**
     * ［アセスメント］画面（居宅）（6⑤） データ保存
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected AssessmentHomeTab65UpdateServiceOutDto mainProcessMealUpdate(
            AssessmentHomeTab65UpdateServiceInDto inDto)
            throws Exception {

        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);
        loginDto.setNinteiFormF(inDto.getNiteiFlg());
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. 計画対象期間の保存処理===============
         * 
         */

        // 変数.計画対象期間ID
        Integer sc1IdTmp0 = 0;
        // 変数.アセスメントID
        Integer gdlIdTmp0 = CommonDtoUtil.strValToInt(inDto.getGdlId());
        // 2.1.リクエストパラメータ詳細.計画対象期間IDがnullの場合、【27-06 記録共通期間】情報を登録する。
        if (!StringUtils.hasLength(inDto.getSc1Id())) {
            this.assessmentHomeLogic.insertKghTucKrkKikan(loginDto);
            // 変数.計画対象期間ID = 採番した期間ID
            sc1IdTmp0 = CommonDtoUtil.strValToInt(loginDto.getSc1Id());
        }
        // 2.2.上記以外の場合、
        else {
            // 変数.計画対象期間ID = リクエストパラメータ詳細.計画対象期間ID
            sc1IdTmp0 = CommonDtoUtil.strValToInt(inDto.getSc1Id());
        }

        final Integer sc1IdTmp = sc1IdTmp0;
        /*
         * ===============3. リクエストパラメータ詳細.削除処理区分が2:画面を履歴ごと削除するの場合、===============
         * 
         */
        if (CommonConstants.DELETE_TEI_KBN_2.equals(inDto.getDeleteKbn())) {
            // 3.サブ情報を更新する
            this.assessmentHomeLogic.homeLogicsyuri(loginDto, sc1IdTmp);
        }
        /*
         * ===============4. リクエストパラメータ詳細.削除処理区分が1:画面のみ削除するの場合===============
         * 
         */
        else if (CommonConstants.DELETE_KBN_1.equals(inDto.getDeleteKbn())) {
            // 【ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂/R３改訂）】情報を削除する
            deleteCpnTucGdlKan15(inDto, sc1IdTmp);

            // 4.3. 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
            deleteCpnTucGdlRireki(inDto, sc1IdTmp);
        }
        /*
         * ===============5. 以外の場合===============
         * 
         */
        else {
            // 5.1. 履歴情報の保存処理
            // 5.1.1. リクエストパラメータ詳細.履歴更新区分が"C":新規の場合、【ＧＬ＿居宅アセスメント履歴】情報を登録する。
            if (CommonDtoUtil.isHistoryCreate(inDto)) {
                gdlIdTmp0 = insertCpnTucGdlRireki(inDto, sc1IdTmp);
            }
            // 5.1.2. リクエストパラメータ詳細.履歴更新区分が"U":更新の場合、【ＧＬ＿居宅アセスメント履歴】情報を更新する。
            else if (CommonDtoUtil.isHistoryUpdate(inDto)) {
                updateCpnTucGdlRireki(inDto, sc1IdTmp);
            }

            // 5.2. 社会生活（への適応）力情報の保存処理

            final Integer gdlIdTmp = gdlIdTmp0;
            // 5.2.1. リクエストパラメータ詳細.更新区分が"C":新規の場合
            if (CommonDtoUtil.isCreate(inDto)) {
                // 【ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂/R３改訂）】情報を登録する
                insertCpnTucGdlKan15(inDto, sc1IdTmp, gdlIdTmp);
            }
            // 5.2.2. リクエストパラメータ詳細.更新区分が"U":更新の場合
            else if (CommonDtoUtil.isUpdate(inDto)) {
                // 【ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂/R３改訂）】情報を更新する
                updateCpnTucGdlKan15(inDto, sc1IdTmp);
            }

            // 5.3.リクエストパラメータ.【課題と目標リスト】の件数分、【ＧＬ＿課題と目標】情報を保存する。
            this.assessmentHomeLogic.homeLogicCpnTucGdlKadai(loginDto, sc1IdTmp);
        }

        // 戻り情報を設定
        AssessmentHomeTab65UpdateServiceOutDto outDto = new AssessmentHomeTab65UpdateServiceOutDto();

        // 「2.」で処理した変数.計画対象期間ID
        outDto.setSc1Id(CommonDtoUtil.objValToString(sc1IdTmp));

        // 登録の場合：「5.1.1.」で採番したアセスメントID
        // 更新の場合：リクエストパラメータ.アセスメントID
        outDto.setGdlId(CommonDtoUtil.objValToString(gdlIdTmp0));

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 【ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂/R３改訂）】情報を削除する
     * 
     * @param inDto
     * @param sc1IdTmp
     */
    private void deleteCpnTucGdlKan15(AssessmentHomeTab65UpdateServiceInDto inDto, Integer sc1IdTmp) {

        // 4.1. リクエストパラメータ詳細.改定フラグが4（H21/4改訂版）の場合、【ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）】情報を削除する。
        if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNiteiFlg())) {

            final CpnTucGdl4Kan15H21Criteria cpnTucGdl4Kan15H21Criteria = new CpnTucGdl4Kan15H21Criteria();

            // ■更新条件
            // アセスメントID＝リクエストパラメータ.アセスメントID
            // 計画期間ID＝【変数】.計画対象期間ID
            // 削除フラグ = 0（未削除データ）
            // 更新回数 = リクエストパラメータ詳細.社会生活（への適応）力情報（H21/4改訂版）.更新回数
            cpnTucGdl4Kan15H21Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(sc1IdTmp);

            this.cpnTucGdl4Kan15H21Mapper.deleteByCriteria(cpnTucGdl4Kan15H21Criteria);

        }
        // 4.2. リクエストパラメータ詳細.改定フラグが5（R3/4改訂版）の場合、【ＧＬ＿⑤社会生活（への適応）力（R３改訂）】情報を削除する。
        else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNiteiFlg())) {

            final CpnTucGdl5Kan15R3Criteria cpnTucGdl5Kan15R3Criteria = new CpnTucGdl5Kan15R3Criteria();

            // ■更新条件
            // アセスメントID＝リクエストパラメータ.アセスメントID
            // 計画期間ID＝【変数】.計画対象期間ID
            // 削除フラグ = 0（未削除データ）
            // 更新回数 = リクエストパラメータ詳細.社会生活（への適応）力情報（R3/4改訂版）.更新回数
            cpnTucGdl5Kan15R3Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(sc1IdTmp);

            this.cpnTucGdl5Kan15R3Mapper.deleteByCriteria(cpnTucGdl5Kan15R3Criteria);
        }

    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
     * 
     * @param inDto
     * @param sc1IdTmp
     */
    private void deleteCpnTucGdlRireki(AssessmentHomeTab65UpdateServiceInDto inDto, Integer sc1IdTmp) {

        final CpnTucGdlRirekiCriteria cpnTucGdlRirekiCriteria = new CpnTucGdlRirekiCriteria();
        // ■更新条件
        // アセスメントID=リクエストパラメータ詳細.アセスメントID
        // 計画期間ID＝変数.計画対象期間ID
        // 事業者ID＝リクエストパラメータ詳細.事業者ID
        // 利用者ＩＤ＝リクエストパラメータ詳細.利用者ＩＤ
        // 法人ID＝リクエストパラメータ.法人ID
        // 施設ID＝リクエストパラメータ.施設ID
        // 削除フラグ = 0（未削除データ）
        // 更新回数 = リクエストパラメータ詳細.履歴更新回数
        cpnTucGdlRirekiCriteria.createCriteria()
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                .andSc1IdEqualTo(sc1IdTmp)
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        this.cpnTucGdlRirekiMapper.deleteByCriteria(cpnTucGdlRirekiCriteria);

    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を登録する
     * 
     * @param inDto
     * @param sc1IdTmp
     */
    private Integer insertCpnTucGdlRireki(AssessmentHomeTab65UpdateServiceInDto inDto, Integer sc1IdTmp)
            throws Exception {

        CpnTucGdlRireki cpnTucGdlRirekiRecord = new CpnTucGdlRireki();
        // 計画期間ID
        cpnTucGdlRirekiRecord.setSc1Id(sc1IdTmp);
        // 法人ID
        cpnTucGdlRirekiRecord.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        cpnTucGdlRirekiRecord.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        cpnTucGdlRirekiRecord.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ＩＤ
        cpnTucGdlRirekiRecord.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // アセスメント実施日
        cpnTucGdlRirekiRecord.setAsJisshiDateYmd(inDto.getCreatedYmd());
        // 記載者ID
        cpnTucGdlRirekiRecord.setShokuId(CommonDtoUtil.strValToInt(inDto.getCreatedUser()));
        // Ｈの状態
        cpnTucGdlRirekiRecord.setAss10("●");
        // Ｊの状態
        cpnTucGdlRirekiRecord.setAss12("―");
        // 本人の基本動作等8
        cpnTucGdlRirekiRecord.setAss13("―");
        // 改定フラグ
        cpnTucGdlRirekiRecord.setNinteiFormF(CommonDtoUtil.strValToInt(inDto.getNiteiFlg()));

        this.cpnTucGdlRirekiMapper.insertSelectiveAndReturn(cpnTucGdlRirekiRecord);

        return cpnTucGdlRirekiRecord.getGdlId().intValue();
    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する
     * 
     * @param inDto
     * @param sc1IdTmp
     * @return
     */
    private void updateCpnTucGdlRireki(AssessmentHomeTab65UpdateServiceInDto inDto, Integer sc1IdTmp) {
        // ＧＬ＿居宅アセスメント履歴
        CpnTucGdlRireki cpnTucGdlRirekiRecord = new CpnTucGdlRireki();
        // Ｈの状態
        cpnTucGdlRirekiRecord.setAss10("●");
        // Ｊの状態
        cpnTucGdlRirekiRecord.setAss12("―");
        // 本人の基本動作等8
        cpnTucGdlRirekiRecord.setAss13("―");
        // アセスメント実施日
        cpnTucGdlRirekiRecord.setAsJisshiDateYmd(inDto.getCreatedYmd());
        // 記載者ID
        cpnTucGdlRirekiRecord.setShokuId(CommonDtoUtil.strValToInt(inDto.getCreatedUser()));

        final CpnTucGdlRirekiCriteria cpnTucGdlRirekiCriteria = new CpnTucGdlRirekiCriteria();
        // ■更新条件
        // アセスメントID=リクエストパラメータ詳細.アセスメントID
        // 計画期間ID＝変数.計画対象期間ID
        // 事業者ID＝リクエストパラメータ詳細.事業者ID
        // 利用者ＩＤ＝リクエストパラメータ詳細.利用者ＩＤ
        // 法人ID＝リクエストパラメータ.法人ID
        // 施設ID＝リクエストパラメータ.施設ID
        // 削除フラグ = 0（未削除データ）
        // 更新回数 = リクエストパラメータ詳細.履歴更新回数
        cpnTucGdlRirekiCriteria.createCriteria()
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                .andSc1IdEqualTo(sc1IdTmp)
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRirekiRecord,
                cpnTucGdlRirekiCriteria);

    }

    /**
     * 【ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂/R３改訂）】情報を登録する
     * 
     * @param inDto
     * @param sc1IdTmp
     * @param cpnTucGdlKan15Info
     */
    private void insertCpnTucGdlKan15(AssessmentHomeTab65UpdateServiceInDto inDto, Integer sc1IdTmp, Integer gdlIdTmp)
            throws Exception {

        /** 基本動作改訂フラグ4、5情報 */
        Gui00802CpnTucGdlKan15Info cpnTucGdlKan15Info = inDto.getCpnTucGdlKan15Info();
        // 5.2.1.1. リクエストパラメータ詳細.改定フラグが4（H21/4改訂版）の場合、【ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）】情報を登録する。
        if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNiteiFlg())) {

            CpnTucGdl4Kan15H21 cpnTucGdl4Kan15H21Record = new CpnTucGdl4Kan15H21();

            BeanUtils.copyProperties(cpnTucGdl4Kan15H21Record, cpnTucGdlKan15Info);

            // 採番したアセスメントID
            cpnTucGdl4Kan15H21Record.setGdlId(gdlIdTmp);
            // 計画期間ID
            cpnTucGdl4Kan15H21Record.setSc1Id(sc1IdTmp);

            this.cpnTucGdl4Kan15H21Mapper.insertSelective(cpnTucGdl4Kan15H21Record);
        }
        // 5.2.1.2. リクエストパラメータ詳細.改定フラグが5（R3/4改訂版）の場合、【ＧＬ＿⑤社会生活（への適応）力（R３改訂）】情報を登録する。
        else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNiteiFlg())) {

            CpnTucGdl5Kan15R3 cpnTucGdl5Kan15R3Record = new CpnTucGdl5Kan15R3();

            BeanUtils.copyProperties(cpnTucGdl5Kan15R3Record, cpnTucGdlKan15Info);

            // 採番したアセスメントID
            cpnTucGdl5Kan15R3Record.setGdlId(gdlIdTmp);
            // 計画期間ID
            cpnTucGdl5Kan15R3Record.setSc1Id(sc1IdTmp);

            this.cpnTucGdl5Kan15R3Mapper.insertSelective(cpnTucGdl5Kan15R3Record);
        }
    }

    /**
     * 【ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂/R３改訂）】情報を更新する
     * 
     * @param inDto
     * @param sc1IdTmp
     * @param cpnTucGdlKan15Info
     */
    private void updateCpnTucGdlKan15(AssessmentHomeTab65UpdateServiceInDto inDto, Integer sc1IdTmp)
            throws Exception {
        /** 基本動作改訂フラグ4、5情報 */
        Gui00802CpnTucGdlKan15Info cpnTucGdlKan15Info = inDto.getCpnTucGdlKan15Info();

        // 5.2.2.1. リクエストパラメータ詳細.改定フラグが4（H21/4改訂版）の場合、【ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）】情報を更新する。
        if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNiteiFlg())) {

            CpnTucGdl4Kan15H21 cpnTucGdl4Kan15H21Record = new CpnTucGdl4Kan15H21();

            BeanUtils.copyProperties(cpnTucGdl4Kan15H21Record, cpnTucGdlKan15Info);

            final CpnTucGdl4Kan15H21Criteria cpnTucGdl4Kan15H21Criteria = new CpnTucGdl4Kan15H21Criteria();
            // ■更新条件
            // アセスメントID=リクエストパラメータ詳細.アセスメントID
            // 計画期間ID=変数.計画対象期間ID
            // 削除フラグ = 0（未削除データ）
            // 更新回数 = リクエストパラメータ詳細.社会生活（への適応）力情報（H21/4改訂版）.更新回数
            cpnTucGdl4Kan15H21Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(sc1IdTmp);

            this.cpnTucGdl4Kan15H21Mapper.updateByCriteriaSelective(cpnTucGdl4Kan15H21Record,
                    cpnTucGdl4Kan15H21Criteria);
        }
        // 5.2.2.2. リクエストパラメータ詳細.改定フラグが5（R3/4改訂版）の場合、【ＧＬ＿⑤社会生活（への適応）力（R３改訂）】情報を更新する。
        else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNiteiFlg())) {

            CpnTucGdl5Kan15R3 cpnTucGdl5Kan15R3Record = new CpnTucGdl5Kan15R3();

            BeanUtils.copyProperties(cpnTucGdl5Kan15R3Record, cpnTucGdlKan15Info);

            final CpnTucGdl5Kan15R3Criteria cpnTucGdl5Kan15R3Criteria = new CpnTucGdl5Kan15R3Criteria();
            // ■更新条件
            // アセスメントID=リクエストパラメータ詳細.アセスメントID
            // 計画期間ID=変数.計画対象期間ID
            // 削除フラグ = 0（未削除データ）
            // 更新回数 = リクエストパラメータ詳細.社会生活（への適応）力情報（R3/4改訂版）.更新回数
            cpnTucGdl5Kan15R3Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            this.cpnTucGdl5Kan15R3Mapper.updateByCriteriaSelective(cpnTucGdl5Kan15R3Record,
                    cpnTucGdl5Kan15R3Criteria);
        }

    }
}
