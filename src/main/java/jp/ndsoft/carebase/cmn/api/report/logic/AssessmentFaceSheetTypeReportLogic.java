package jp.ndsoft.carebase.cmn.api.report.logic;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeAdlEvaluation;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeAtomicBombNotebook;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeBodyHandycapNotebook;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeBurdenLimitInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeChoPrt;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeElderlyHealthInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeFamilyRelationshipInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeHealthDiagnosis;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeHobbyAndPreference;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeInspection;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeBasicSituation;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeLivingEnvironmentInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeMedicalInsurance;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeMentalNotebook;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeNursingCareInsuranceInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeOtherNotebook;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypePensionNotebook;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypePrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypePrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypePrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypePublicExpenditureInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeRemedialEducationNotebook;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeAccount;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeAdlEvaluationInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeBodySituation;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.U06050FamilyRelationship;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeHealthDiagnosisInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeIndependenceLevel;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeInspectionInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeLifeHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypePastMedicalHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypePersonalBelongings;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportAssessmentFaceSheetTypeUseService;
import jp.ndsoft.carebase.cmn.api.logic.KghAdmSecurityLogic;
import jp.ndsoft.carebase.cmn.api.logic.KghKhnFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkBase02Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentFaceSheetTypeReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.AssessmentFaceSheetTypeReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.model.AssessmentFaceSheetTypeReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.FreeFaceGetKoumokuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.FreeFaceGetKoumokuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.FreeFaceMstPrintByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.FreeFaceMstPrintOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoSys1KmsZuMstByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoSys1KmsZuMstOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrdFaceAss12GetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrdFaceAss12GetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrdFaceAss1GetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrdFaceAss1GetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrdFaceAss22GetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrdFaceAss22GetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrdFaceAss2KhnGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkAssFace1GetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkAssFace1GetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavAnyGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavAnyGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavAss1DmyGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavAss1DmyGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavAss1GetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavAss1GetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavAss2DmyGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavAss2DmyGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavAss2GetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavAss2GetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavKazGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavKazGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavKhnGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavKhnGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavKioGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavKioGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavKozGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavKozGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavNinteiGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavNinteiGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavRanGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavRanGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavSbtGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavSbtGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavSktGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavSktGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavSrvGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavSrvGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavSymGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavSymGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavTecEtGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavTecEtGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavTecGeGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavTecGeGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavTecSeGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavTecSeGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavTecNeGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavTecNeGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavTecRyGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavTecRyGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavTecSiGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavTecSiGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFreeFaceAssBunruiGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFreeFaceAssBunruiGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFreeFaceAssKoumokuGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFreeFaceAssKoumokuGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFreeFaceDataByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFreeFaceDataOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavFtnGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavFtnGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavHokenGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavHokenGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavHanreiGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavHanreiGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavJintaiGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavJintaiGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavJukGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavJukGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavKaigoGetByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkFaceSavKaigoGetOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.AssessmentMasterSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscJintaiBuhinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.HyokaKoumokuMasutaDetaSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghKrkFreeFaceAssBunruiGetSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghKrkFreeFaceAssKoumokuGetSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghKrkFreeFaceGetKoumokuSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghKrkFreeFaceMstPrintSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFace1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFace2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceAnySelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceAss1DmySelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceAss1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceAss2DmySelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceAss2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceKazSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceFtnSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceHokenSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceJintaiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceJukSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceKaigoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceKhnSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceKioSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceKozSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceNinteiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceRanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceSbtSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceSktSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceSrvSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceSymSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceTecEtSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceTecGeSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceTecSeSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceTecNeSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceTecRySelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucAssFaceTecSiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KmsMstAssessment1SelectMapper;
import jp.ndsoft.smh.framework.util.StringUtil;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * @since 2025.07.18
 * <AUTHOR> 関悦
 * @description U06050_アセスメントフェースシート(TYPE1） 帳票出力
 */
@Component
public class AssessmentFaceSheetTypeReportLogic {

    public List<String> koumokuSortList = new ArrayList<>();

    /** アセスメントフェースシート履歴ヘッダ情報を取得 */
    @Autowired
    private KghTucAssFace1SelectMapper kghTucAssFace1SelectMapper;

    /** アセスメントフェースシート表示設定マスタ情報を取得 */
    @Autowired
    private KghKrkFreeFaceGetKoumokuSelectMapper kghKrkFreeFaceGetKoumokuSelectMapper;

    /** 共通関数 */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** 共通関数 */
    @Autowired
    private Nds3GkBase02Logic nds3GkBase02Logic;

    /** 年月の差を取得 */
    @Autowired
    private KghKhnFunc01Logic kghKhnFunc01Logic;

    /** 権限チェック */
    @Autowired
    private KghAdmSecurityLogic kghAdmSecurityLogic;

    /** アセスメントフェースシート履歴基本情報を取得 */
    @Autowired
    private KghTucAssFaceKhnSelectMapper kghTucAssFaceKhnSelectMapper;

    /** 親族関係情報取得 */
    @Autowired
    private KghTucAssFaceKazSelectMapper kghTucAssFaceKazSelectMapper;

    /** アセスメントフェースシート履歴その他情報取得 */
    @Autowired
    private KghTucAssFaceAnySelectMapper kghTucAssFaceAnySelectMapper;

    /** アセスメントフェースシート履歴私物情報取得 */
    @Autowired
    private KghTucAssFaceSbtSelectMapper kghTucAssFaceSbtSelectMapper;

    /** アセスメントフェースシート履歴既往歴情報取得 */
    @Autowired
    private KghTucAssFaceKioSelectMapper kghTucAssFaceKioSelectMapper;

    /** アセスメントフェースシート履歴生活歴情報取得 */
    @Autowired
    private KghTucAssFaceSktSelectMapper kghTucAssFaceSktSelectMapper;

    /** アセスメントフェースシート履歴趣味嗜好情報取得 */
    @Autowired
    private KghTucAssFaceSymSelectMapper kghTucAssFaceSymSelectMapper;

    /** 障害部位図 部品（１０－６）情報取得 */
    @Autowired
    private ComMscJintaiBuhinSelectMapper comMscJintaiBuhinSelectMapper;

    /** アセスメントフェースシート履歴人体図情報取得 */
    @Autowired
    private KghTucAssFaceJintaiSelectMapper kghTucAssFaceJintaiSelectMapper;

    /** アセスメントフェースシート履歴保険情報取得 */
    @Autowired
    private KghTucAssFaceHokenSelectMapper kghTucAssFaceHokenSelectMapper;

    /** アセスメントフェースシート履歴自立度情報を取得 */
    @Autowired
    private KghTucAssFaceRanSelectMapper kghTucAssFaceRanSelectMapper;

    /** アセスメントフェースシート履歴療育者手帳情報取得 */
    @Autowired
    private KghTucAssFaceTecRySelectMapper kghTucAssFaceTecRySelectMapper;

    /** アセスメントフェースシート履歴年金者手帳情報取得 */
    @Autowired
    private KghTucAssFaceTecNeSelectMapper kghTucAssFaceTecNeSelectMapper;

    /** マスタ印刷設定取得 */
    @Autowired
    private KghKrkFreeFaceMstPrintSelectMapper kghKrkFreeFaceMstPrintSelectMapper;

    /** アセスメントフェースシート履歴負担限度情報取得 */
    @Autowired
    private KghTucAssFaceFtnSelectMapper kghTucAssFaceFtnSelectMapper;

    /** アセスメントフェースシート履歴住環境情報 */
    @Autowired
    private KghTucAssFaceJukSelectMapper kghTucAssFaceJukSelectMapper;

    /** アセスメントフェースシート履歴口座情報を取得 */
    @Autowired
    private KghTucAssFaceKozSelectMapper kghTucAssFaceKozSelectMapper;

    /** アセスメントフェースシート表示設定マスタリストを取得 */
    @Autowired
    private KghKrkFreeFaceAssBunruiGetSelectMapper kghKrkFreeFaceAssBunruiGetSelectMapper;

    /** 履歴番号を取得 */
    @Autowired
    private HyokaKoumokuMasutaDetaSelectMapper hyokaKoumokuMasutaDetaSelectMapper;

    /** アセスメントフェースシート履歴アセスメント1情報を取得 */
    @Autowired
    private KghTucAssFaceAss1SelectMapper kghTucAssFaceAss1SelectMapper;

    /** アセスメントフェースシート履歴基本状況情報リストを取得 */
    // @Autowired
    // private KghKrdFaceAss2KhnGetSelectMapper kghKrdFaceAss2KhnGetSelectMapper;

    /** アセスメントフェースシート履歴基本状況情報リストを取得 */
    @Autowired
    private KghTucAssFaceAss2SelectMapper kghTucAssFaceAss2SelectMapper;

    /** アセスメントフェースシート履歴介護保険情報取得 */
    @Autowired
    private KghTucAssFaceKaigoSelectMapper kghTucAssFaceKaigoSelectMapper;

    /** アセスメントフェースシート履歴介護確定情報取得 */
    @Autowired
    private KghTucAssFaceNinteiSelectMapper kghTucAssFaceNinteiSelectMapper;

    /** アセスメントフェースシート履歴履歴データ情報を取得 */
    @Autowired
    private KghTucAssFace2SelectMapper kghTucAssFace2SelectMapper;

    /** アセスメントフェースシート履歴身体障害手帳情報を取得 */
    @Autowired
    private KghTucAssFaceTecSiSelectMapper kghTucAssFaceTecSiSelectMapper;

    /** アセスメントフェースシート履歴精神者手帳情報を取得 */
    @Autowired
    private KghTucAssFaceTecSeSelectMapper kghTucAssFaceTecSeSelectMapper;

    /** アセスメントフェースシート履歴原爆者手帳情報を取得 */
    @Autowired
    private KghTucAssFaceTecGeSelectMapper kghTucAssFaceTecGeSelectMapper;

    /** アセスメントフェースシート履歴その他手帳情報を取得 */
    @Autowired
    private KghTucAssFaceTecEtSelectMapper kghTucAssFaceTecEtSelectMapper;

    /** アセスメントフェースシート履歴利用サービス情報を取得 */
    @Autowired
    private KghTucAssFaceSrvSelectMapper kghTucAssFaceSrvSelectMapper;

    /** アセスメントフェースシート表示設定項目情報を取得 */
    @Autowired
    private KghKrkFreeFaceAssKoumokuGetSelectMapper kghKrkFreeFaceAssKoumokuGetSelectMapper;

    /** アセスメントフェースシート履歴アセスメント1ダミー情報を取得 */
    @Autowired
    private KmsMstAssessment1SelectMapper kmsMstAssessment1SelectMapper;

    /** アセスメントマスタ１の大分類情報を取得 */
    @Autowired
    private KghTucAssFaceAss1DmySelectMapper kghTucAssFaceAss1DmySelectMapper;

    /** アセスメントマスタ情報を取得 */
    @Autowired
    private AssessmentMasterSelectMapper assessmentMasterSelectMapper;

    /** アセスメントフェースシート履歴アセスメント2ダミー情報を取得 */
    @Autowired
    private KghTucAssFaceAss2DmySelectMapper kghTucAssFaceAss2DmySelectMapper;

    public enum OutputTarget {
        // U06050_出力対象_基本情報定数
        KHN("khn"),
        // U06050_出力対象_負担限度定数
        FTN("ftn"),
        // U06050_出力対象_親族関係定数
        KAZ1("kaz1"),
        KAZ2("kaz2"),
        KAZ3("kaz3"),
        KAZ4("kaz4"),
        // U06050_出力対象_住環境定数
        JUK1("juk1"),
        JUK2("juk2"),
        // U06050_出力対象_口座定数
        KOZ("koz"),
        // 06050_出力対象_私物定数
        SBT("sbt"),
        // U06050_出力対象_既往歴定数
        KIO("kio"),
        // U06050_出力対象_生活歴定数
        SKT("skt"),
        // U06050_出力対象_自立度定数
        RAN("ran"),
        // U06050_出力対象_基本状況定数
        KHJ("khj"),
        KHJ2("khj2"),
        // U06050_出力対象_趣味嗜好定数
        SYM("sym"),
        // U06050_出力対象_ADL評価定数
        ADL("adl"),
        // U06050_出力対象_JTZ人体図の設定
        JTZ("jtz"),
        // U06050_出力対象_健康診断定数
        KKS("kks"),
        // U06050_出力対象_介護保険定数
        HOK1("hok1"),
        // U06050_出力対象_主保健定数
        HOK2("hok2"),
        // U06050_出力対象_老人保健定数
        HOK3("hok3"),
        // U06050_出力対象_公費定数
        HOK4("hok4"),
        // U06050_出力対象_身障手帳定数
        TEC_SI("tec_si"),
        // U06050_出力対象_療育手帳定数
        TEC_RY("tec_ry"),
        // U06050_出力対象_精神手帳定数
        TEC_SE("tec_se"),
        // U06050_出力対象_年金手帳定数
        TEC_NE("tec_ne"),
        // U06050_出力対象_原爆手帳定数
        TEC_GE("tec_ge"),
        // U06050_出力対象_その他手帳定数
        TEC_ET("tec_et"),
        // U06050_出力対象_サービス定数
        SRV("srv"),
        // U06050_出力対象_検査表示定数
        KNS("kns");

        private final String value;

        OutputTarget(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }


    /**
     * U06050_出力対象
     */
    public static final Map<String, String> OUTPUT_TARGET_CONSTANTS = new HashMap<>();
    static {
        for (OutputTarget target : OutputTarget.values()) {
            OUTPUT_TARGET_CONSTANTS.put(target.name(), target.getValue());
        }
    }

    /**
     * U06050_アセスメントフェースシート 帳票用データ詳細の取得
     * 
     * @param model       入力データ
     * @param outDto      出力データ
     * @param serviceType 帳票区分
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public AssessmentFaceSheetTypeReportServiceInDto getAssessmentFaceSheetTypeParameters(
            final AssessmentFaceSheetTypeReportParameterModel model,
            final AssessmentFaceSheetTypeReportServiceOutDto outDto,
            final Integer serviceType)
            throws Exception {

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        AssessmentFaceSheetTypeReportServiceInDto infoInDto = new AssessmentFaceSheetTypeReportServiceInDto();

        /*
         * 2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
         * 結果レスポンスを返却する。
         * 2. リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false の場合
         * ケアチェック表２情報の取得
         */

        ReportAssessmentFaceSheetTypePrintSet printSet = model.getPrintSet();
        ReportAssessmentFaceSheetTypePrintOption printOption = model.getPrintOption();
        ReportAssessmentFaceSheetTypePrintSubjectHistory history = model.getPrintSubjectHistoryList()
                .get(CommonConstants.INT_0);

        infoInDto = getAssessmentFaceSheetTypeParameters(model, printOption.getEmptyFlg(), printSet, history,
                serviceType);
        // 帳票タイトル
        infoInDto.setTitle(printSet.getPrtTitle());
        // 指定日
        if (CommonConstants.STR_2.equals(printSet.getShiTeiKubun())) {
            // 印刷設定.指定日印刷区分＝2:指定日印刷の場合
            List<String> shiteiYmd = ReportUtil.getLocalDateToJapanDateTimeFormat(printSet.getShiTeiDate());
            infoInDto.setShiTeiDateGG(ReportUtil.nullToEmpty(shiteiYmd.get(CommonConstants.INT_0)));
            infoInDto.setShiTeiDateYY(ReportUtil.nullToEmpty(shiteiYmd.get(CommonConstants.INT_1)));
            infoInDto.setShiTeiDateMM(ReportUtil.nullToEmpty(shiteiYmd.get(CommonConstants.INT_2)));
            infoInDto.setShiTeiDateDD(ReportUtil.nullToEmpty(shiteiYmd.get(CommonConstants.INT_3)));
        }
        if (CommonConstants.STR_3.equals(printSet.getShiTeiKubun())) {
            // 印刷設定.指定日印刷区分＝3:日付空欄印刷の場合
            String blankDate = nds3GkFunc01Logic.blankDate(LocalDate.now().toString());
            infoInDto.setShiTeiDateGG(
                    ReportUtil.nullToEmpty(blankDate.substring(CommonConstants.INT_0,
                            blankDate.indexOf(CommonConstants.BLANK_SPACE))));
            infoInDto.setShiTeiDateYY(CommonConstants.BLANK_STRING);
            infoInDto.setShiTeiDateMM(CommonConstants.BLANK_STRING);
            infoInDto.setShiTeiDateDD(CommonConstants.BLANK_STRING);
        }
        if (CommonConstants.STR_1.equals(printSet.getShiTeiKubun())) {
            // 印刷設定.指定日印刷区分＝1:印刷しないの場合
            infoInDto.setShiTeiDateGG(CommonConstants.BLANK_STRING);
            infoInDto.setShiTeiDateYY(CommonConstants.BLANK_STRING);
            infoInDto.setShiTeiDateMM(CommonConstants.BLANK_STRING);
            infoInDto.setShiTeiDateDD(CommonConstants.BLANK_STRING);
        }

        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // 対象年月日
        String systemYmd = ReportUtil.getByteLength(model.getSystemDate()) > CommonConstants.INT_10
                ? model.getSystemDate().substring(CommonConstants.INT_0, CommonConstants.INT_10)
                : model.getSystemDate();
        List<String> taisyoYmdList = ReportUtil.getLocalDateToJapanDateTimeFormat(systemYmd);
        if (CollectionUtils.isNotEmpty(taisyoYmdList)) {
            infoInDto.setTaisyoYmdGG(ReportUtil.nullToEmpty(taisyoYmdList.get(CommonConstants.INT_0)));
            infoInDto.setTaisyoYmdYY(ReportUtil.nullToEmpty(taisyoYmdList.get(CommonConstants.INT_1)));
            infoInDto.setTaisyoYmdMM(ReportUtil.nullToEmpty(taisyoYmdList.get(CommonConstants.INT_2)));
            infoInDto.setTaisyoYmdDD(ReportUtil.nullToEmpty(taisyoYmdList.get(CommonConstants.INT_3)));
        } else {
            infoInDto.setTaisyoYmdGG(CommonConstants.BLANK_STRING);
            infoInDto.setTaisyoYmdYY(CommonConstants.BLANK_STRING);
            infoInDto.setTaisyoYmdMM(CommonConstants.BLANK_STRING);
            infoInDto.setTaisyoYmdDD(CommonConstants.BLANK_STRING);
        }
        // 事業者名
        infoInDto.setJigyousha(model.getSvJigyoKnj());
        // セクション番号
        infoInDto.setSectionBango(history.getChoPrtList().get(CommonConstants.INT_0).getSection());
        return infoInDto;
    }

    /**
     * U06050_アセスメントフェースシート(TYPE1） 帳票用データ詳細の取得
     *
     * @param model       入力データ
     * @param emptyFlg    空欄で印刷フラグ
     * @param printSet    印刷設定
     * @param history     印刷対象履歴
     * @param serviceType 帳票区分
     * @return 帳票用データ詳細
     * @throws Exception 例外
     */
    protected AssessmentFaceSheetTypeReportServiceInDto getAssessmentFaceSheetTypeParameters(
            final AssessmentFaceSheetTypeReportParameterModel model,
            String emptyFlg,
            ReportAssessmentFaceSheetTypePrintSet printSet,
            ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            Integer serviceType) throws Exception {

        // 帳票用データ詳細
        AssessmentFaceSheetTypeReportServiceInDto infoInDto = new AssessmentFaceSheetTypeReportServiceInDto();

        // 出力帳票印刷情報リスト
        ReportAssessmentFaceSheetTypeChoPrt choPrt = history.getChoPrtList().get(CommonConstants.INT_0);

        if (serviceType == CommonConstants.INT_1) {
            // TYPE1帳票出力対象
            setOutputTargets(CommonConstants.INT_1);
        } else if (serviceType == CommonConstants.INT_2) {
            // TYPE2帳票出力対象
            setOutputTargets(CommonConstants.INT_2);
        }

        F3gkGetProfileInDto f3gkGetProfileInDto = new F3gkGetProfileInDto();
        f3gkGetProfileInDto.setGsyscd(model.getSyscd());
        f3gkGetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(choPrt.getShokuId()));
        f3gkGetProfileInDto.setHoujinId(CommonConstants.INT_0);
        f3gkGetProfileInDto.setShisetuId(CommonConstants.INT_0);
        f3gkGetProfileInDto.setSvJigyoId(CommonConstants.INT_0);
        f3gkGetProfileInDto.setKinounameKnj(CommonConstants.GAMEN_NAME_FRE);
        f3gkGetProfileInDto.setSectionKnj(CommonConstants.SECTION_FACE);
        f3gkGetProfileInDto.setKeyKnj(CommonConstants.KEY_KAZ_GAI);
        f3gkGetProfileInDto.setAsDefault(CommonConstants.STR_0);

        /*
         * 2.3. データの検索
         */

        // 選択利用者セット
        String paramFor23 = nds3GkFunc01Logic.getF3gkProfile(f3gkGetProfileInDto);

        // (3-2). 電話フラグ取得
        f3gkGetProfileInDto.setKeyKnj(CommonConstants.KEY_KAZ_TEL);
        Integer telFlg = CommonDtoUtil.strValToInt(nds3GkFunc01Logic.getF3gkProfile(f3gkGetProfileInDto));
        if (null == telFlg) {
            telFlg = CommonConstants.INT_0;
        }

        // 2.6.1. 基本状況、生活状況、日常状況の速度向上をチェック
        f3gkGetProfileInDto.setKeyKnj(CommonConstants.KEY_KJN_FLG);
        String paramFor262 = nds3GkFunc01Logic.getF3gkProfile(f3gkGetProfileInDto);
        Integer baseFlg = Strings.isNotBlank(paramFor262)
                ? CommonDtoUtil.strValToInt(paramFor262) == CommonConstants.INT_1 ? CommonConstants.INT_1
                        : CommonConstants.INT_0
                : CommonConstants.INT_0;

        /*
         * 2.1. アセスメントフェースシート履歴ヘッダ情報取得
         */

        KghKrkAssFace1GetByCriteriaInEntity criteriaInEntity = new KghKrkAssFace1GetByCriteriaInEntity();
        criteriaInEntity.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkAssFace1GetOutEntity> list = kghTucAssFace1SelectMapper
                .findKghKrkAssFace1GetByCriteria(criteriaInEntity);

        // 変数.対象年月日
        // String taisyoYmd;
        // 変数.法人ID
        Integer houJinId;
        // 変数.施設ID
        Integer shisetuId;
        // 変数.事業者ID
        Integer svJigyoId;
        if (CollectionUtils.isNotEmpty(list)) {
            // 取得データあり場合
            // taisyoYmd = list.get(CommonConstants.INT_0).getShoriYmd();
            houJinId = list.get(CommonConstants.INT_0).getHoujinId();
            shisetuId = list.get(CommonConstants.INT_0).getShisetuId();
            svJigyoId = list.get(CommonConstants.INT_0).getSvJigyoId();
        } else {
            // 取得データなし場合
            // taisyoYmd = model.getSystemDate();
            shisetuId = CommonDtoUtil.strValToInt(model.getJigyoInfo().getShisetuId());
            houJinId = CommonDtoUtil.strValToInt(model.getJigyoInfo().getHoujinId());
            svJigyoId = CommonDtoUtil.strValToInt(model.getJigyoInfo().getSvJigyoId());
        }

        /*
         * 2.2. マスタ取得
         */

        FreeFaceGetKoumokuByCriteriaInEntity criteriaInEntity2 = new FreeFaceGetKoumokuByCriteriaInEntity();
        criteriaInEntity2.setSvJigyoId(svJigyoId);
        List<FreeFaceGetKoumokuOutEntity> masterList = kghKrkFreeFaceGetKoumokuSelectMapper
                .findFreeFaceGetKoumokuByCriteria(criteriaInEntity2);
        // 上記取得のOUTPUT情報を（"item_kbn = 4"）でフィルターする
        List<FreeFaceGetKoumokuOutEntity> masterFIlterList = masterList.stream()
                .filter(item -> item.getItemKbn() == CommonConstants.INT_4).toList();

        /*
         * 2.4. 基本情報取得
         */

        KghKrkFaceSavKhnGetByCriteriaInEntity kghKrkFaceSavKhnGetParam = new KghKrkFaceSavKhnGetByCriteriaInEntity();
        kghKrkFaceSavKhnGetParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavKhnGetOutEntity> kghKrkFaceSavKhnGetList = kghTucAssFaceKhnSelectMapper
                .findKghKrkFaceSavKhnGetByCriteria(kghKrkFaceSavKhnGetParam);

        /*
         * 2.5. マスタ印刷設定取得
         */

        FreeFaceMstPrintByCriteriaInEntity freeFaceMstPrint = new FreeFaceMstPrintByCriteriaInEntity();
        freeFaceMstPrint.setSvJigyoId(svJigyoId);
        List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutList = kghKrkFreeFaceMstPrintSelectMapper
                .findFreeFaceMstPrintByCriteria(freeFaceMstPrint);

        /*
         * 2.6. 表示順、表示・非表示、行数、表示項目など取得
         */

        // 人体図フラグ
        Integer bodyImgFlg = CommonConstants.INT_0;
        // マスタ印刷設定情報を（"koumoku_id = 16"）
        Optional<FreeFaceMstPrintOutEntity> firstMatch = freeFaceMstPrintOutList.stream()
                .filter(freeFaceMst -> freeFaceMst.getKoumokuId() == CommonConstants.INT_16)
                .findFirst();
        // フィルター結果あり場合
        if (firstMatch.isPresent()) {
            // 人体図フラグ ＝ フィルター結果.作図出力フラグ
            bodyImgFlg = firstMatch.get().getPictureFlg();
        }

        // 2.6.2. 機能タブの並び順の取得
        // 2.6.2.1. マスタソート処理
        masterFIlterList = masterFIlterList.stream()
                .sorted(Comparator.comparing(FreeFaceGetKoumokuOutEntity::getSort)).toList();

        // 2.6.3. 出力対象編集
        List<String> outputList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(masterFIlterList)) {
            int cnt = CommonConstants.INT_1;
            // 2.6.3.1. 基本情報のデータ対象を設定する
            outputList.add(CommonConstants.INT_0, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.KHN.name()));

            // 2.6.3.2. 上記「2.6.3.1.」ソート結果の件数分、変数.出力対象を作成する
            for (FreeFaceGetKoumokuOutEntity master : masterFIlterList) {
                // 種別ID
                Integer syubetsuId = master.getSyubetsuId();
                // 項目ID
                Integer koumokuId = master.getKoumokuId();
                // 表示フラグ
                Integer useFlg = master.getUseFlg();
                // (1). 変数.表示フラグが0の場合、該当処理を終了し、次のデータを実施する
                if (useFlg == CommonConstants.INT_0) {
                    continue;
                }

                // (2). 権限・適用事業所チェック
                // 変数.機能名

                String koumokuKnj = master.getKoumokuKnj();
                if (ReportConstants.KAZOKU_STRING_1.equals(koumokuKnj)) {
                    // 変数.機能名が"親族･関係者"の場合、変数.機能名を"親族関係"で設定する
                    koumokuKnj = ReportConstants.KAZOKU_STRING_2;
                }

                if (ReportConstants.HOSPITAL_HOKEN_STRING.equals(koumokuKnj)) {
                    // 変数.機能名が"医療保険"の場合、変数.機能名を"主保険"で設定する
                    koumokuKnj = ReportConstants.MAIN_HOKEN_STRING;
                }

                if (ReportConstants.KOUZA_STRING.equals(koumokuKnj)) {
                    // (2-1). 変数.機能名が"口座"の場合、下記権限チェックを行う
                    Integer checkResult1 = kghAdmSecurityLogic.chkUseKinou(
                            CommonDtoUtil.strValToInt(choPrt.getShokuId()),
                            CommonConstants.SYSTEM_CODE_71101, CommonDtoUtil.strValToInt(model.getJigyoInfo().getSvJigyoId()),
                            ReportConstants.KINOU_NAME_1);
                    Integer checkResult2 = kghAdmSecurityLogic.chkUseKinou(
                            CommonDtoUtil.strValToInt(choPrt.getShokuId()),
                            CommonConstants.SYSTEM_CODE_71101, CommonDtoUtil.strValToInt(model.getJigyoInfo().getSvJigyoId()),
                            ReportConstants.KINOU_NAME_2);
                    Integer checkResult3 = kghAdmSecurityLogic.chkUseKinou(
                            CommonDtoUtil.strValToInt(choPrt.getShokuId()),
                            CommonConstants.SYSTEM_CODE_71101, CommonDtoUtil.strValToInt(model.getJigyoInfo().getSvJigyoId()),
                            ReportConstants.KINOU_NAME_3);
                    if (checkResult1 == CommonConstants.INT_0 && checkResult2 == CommonConstants.INT_0
                            && checkResult3 == CommonConstants.INT_0) {
                        continue;
                    }
                } else if (ReportConstants.BURDEN_LIMIT.equals(koumokuKnj)) {
                    // (2-2). 変数.機能名が"負担限度額"の場合、下記権限チェックを行う
                    Integer checkResult1 = kghAdmSecurityLogic.chkUseKinou(
                            CommonDtoUtil.strValToInt(choPrt.getShokuId()),
                            CommonConstants.SYSTEM_CODE_71101, CommonDtoUtil.strValToInt(model.getJigyoInfo().getSvJigyoId()),
                            ReportConstants.KINOU_NAME_4);
                    Integer checkResult2 = kghAdmSecurityLogic.chkUseKinou(
                            CommonDtoUtil.strValToInt(choPrt.getShokuId()),
                            CommonConstants.SYSTEM_CODE_71101, CommonDtoUtil.strValToInt(model.getJigyoInfo().getSvJigyoId()),
                            ReportConstants.KINOU_NAME_5);
                    if (checkResult1 == CommonConstants.INT_0 && checkResult2 == CommonConstants.INT_0) {
                        continue;
                    }
                } else {
                    // (2-3). 上記以外、変数.機能名で、下記権限チェックを行う
                    Integer checkResult = kghAdmSecurityLogic.chkUseKinou(
                            CommonDtoUtil.strValToInt(choPrt.getShokuId()),
                            CommonConstants.SYSTEM_CODE_71101, CommonDtoUtil.strValToInt(model.getJigyoInfo().getSvJigyoId()),
                            ReportConstants.KINOU_NAME_LABEL + koumokuKnj);
                    if (checkResult == CommonConstants.INT_0) {
                        continue;
                    }
                }

                // (3). 変数.種別IDが9の場合、下記処理を行う
                if (syubetsuId == CommonConstants.INT_9) {
                    outputList.add(cnt, CommonConstants.BLANK_STRING);
                    switch (koumokuId) {
                        case 2:
                            // (3-1). 変数.項目ＩＤが2：負担限度の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.FTN.name()));
                            break;
                        case 3:
                            // (3-2). 変数.項目ＩＤが3：親族関係の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.KAZ1.name()));
                            break;
                        case 4:
                            // (3-3). 変数.項目ＩＤが4：住環境の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.JUK1.name()));
                            break;
                        case 5:
                            // (3-4). 変数.項目ＩＤが5：口座の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.KOZ.name()));
                            break;
                        case 6:
                            // (3-5). 変数.項目ＩＤが6：私物の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.SBT.name()));
                            break;
                        case 7:
                            // (3-6). 変数.項目ＩＤが7：既往歴の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.KIO.name()));
                            break;
                        case 8:
                            // (3-7). 変数.項目ＩＤが8：生活歴の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.SKT.name()));
                            break;
                        case 10:
                            // (3-8). 変数.項目ＩＤが10：自立度の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.RAN.name()));
                            break;
                        case 12:
                            // (3-9). 変数.項目ＩＤが12：基本状況の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.KHJ.name()));
                            break;
                        case 13:
                            // (3-10). 変数.項目ＩＤが13：趣味嗜好の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.SYM.name()));
                            break;
                        case 16:
                            // (3-11). 変数.項目ＩＤが16：ADL評価の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            // 「3. 特定アセスメントのDS設定」を行う
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.ADL.name()));

                            // 表示設定分類情報を取得する
                            KghKrkFreeFaceAssBunruiGetByCriteriaInEntity kghKrkFreeFaceAssBunruiGetParam = new KghKrkFreeFaceAssBunruiGetByCriteriaInEntity();
                            kghKrkFreeFaceAssBunruiGetParam.setSvJigyoId(svJigyoId);
                            List<KghKrkFreeFaceAssBunruiGetOutEntity> kghKrkFreeFaceAssBunruiGetList = kghKrkFreeFaceAssBunruiGetSelectMapper
                                    .findKghKrkFreeFaceAssBunruiGetByCriteria(kghKrkFreeFaceAssBunruiGetParam);
                            // 表示設定分類件数 ＞0 且つ 人体図フラグが1の場合
                            if (CollectionUtils.isNotEmpty(kghKrkFreeFaceAssBunruiGetList)
                                    && bodyImgFlg == CommonConstants.NUMBER_1) {
                                setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                                outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.JTZ.name()));
                            }
                            break;
                        case 17:
                            // (3-12). 変数.項目ＩＤが17：健康診断の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.KKS.name()));
                            break;
                        case 18:
                            // (3-13). 変数.項目ＩＤが18：介護保険の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.HOK1.name()));
                            break;
                        case 19:
                            // (3-14). 変数.項目ＩＤが19：主保健の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.HOK2.name()));
                            break;
                        case 20:
                            // (3-15). 変数.項目ＩＤが20：老人保健の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.HOK3.name()));
                            break;
                        case 21:
                            // (3-16). 変数.項目ＩＤが21：公費の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.HOK4.name()));
                            break;
                        case 22:
                            // (3-17). 変数.項目ＩＤが22：身障手帳の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.TEC_SI.name()));
                            break;
                        case 23:
                            // (3-18). 変数.項目ＩＤが23：療育手帳の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.TEC_RY.name()));
                            break;
                        case 24:
                            // (3-19). 変数.項目ＩＤが24：精神手帳の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.TEC_SE.name()));
                            break;
                        case 25:
                            // (3-20). 変数.項目ＩＤが25：年金手帳の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.TEC_NE.name()));
                            break;
                        case 26:
                            // (3-21). 変数.項目ＩＤが26：原爆手帳の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.TEC_GE.name()));
                            break;
                        case 27:
                            // (3-22). 変数.項目ＩＤが27：その他手帳の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.TEC_ET.name()));
                            break;
                        case 28:
                            // (3-23). 変数.項目ＩＤが28：サービスの場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.SRV.name()));
                            break;
                        case 29:
                            // (3-24). 変数.項目ＩＤが29：検査の場合
                            setDynamicData(infoInDto, null, ReportConstants.INT_1, cnt, Boolean.TRUE);
                            outputList.set(cnt, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.KNS.name()));
                            break;
                    }
                    cnt++;
                }
            }
        }

        /*
         * 2.6.4. レポートの表示順設定
         */
        // 2.6.4.1. 変数.出力対象の件数分、繰り返し、下記処理を行う
        List<String> preViewList = new ArrayList<>();
        preViewList.add(CommonConstants.INT_0, CommonConstants.BLANK_STRING);
        for (int i = CommonConstants.INT_1; i < outputList.size(); i++) {
            for (OutputTarget target : OutputTarget.values()) {
                if (outputList.get(i).equals(OUTPUT_TARGET_CONSTANTS.get(target.name()))) {
                    switch (target) {
                        case KHJ:
                            // (1). 変数.出力対象[i] が"d_kgh_krd_face1_khj_rep"の場合
                            if (baseFlg == CommonConstants.INT_1) {
                                // 基本フラグ が1の場合
                                preViewList.add(i, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.KHJ2.name()));
                            } else {
                                // 上記以外の場合
                                preViewList.add(i, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.KHJ.name()));
                            }
                            break;
                        case KAZ1:
                            // (3). 変数.出力対象[i] が"d_kgh_krd_face1_kaz1_rep"の場合
                            // (3-1). 作図出力フラグ取得
                            // 上記2.5. マスタ印刷設定取得のOUTPUT情報を（"koumoku_id = 3"）でフィルターする
                            Optional<FreeFaceMstPrintOutEntity> freeFaceMstPrintOut1 = freeFaceMstPrintOutList.stream()
                                    .filter(item -> item.getKoumokuId() == CommonConstants.INT_3).findFirst();
                            Integer pictureOutputFlg1;
                            if (freeFaceMstPrintOut1.isPresent()) {
                                // フィルター結果存在する場合、
                                pictureOutputFlg1 = freeFaceMstPrintOut1.get().getPictureFlg();
                            } else {
                                // フィルター結果存在しない場合、
                                pictureOutputFlg1 = CommonConstants.INT_0;
                            }

                            // (3-2). 電話フラグ取得
                            telFlg = telFlg == CommonConstants.INT_1 ? CommonConstants.INT_1 : CommonConstants.INT_0;

                            // (3-3). 家族図の有無設定
                            if (telFlg == CommonConstants.INT_1) {
                                // 電話フラグが1の場合
                                if (pictureOutputFlg1 == CommonConstants.INT_1) {
                                    // 作図出力フラグが1の場合
                                    preViewList.add(i, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.KAZ4.name()));
                                    infoInDto.setKazJrxmlKbn(CommonConstants.NUMBER_4);
                                } else {
                                    // 上記以外の場合
                                    preViewList.add(i, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.KAZ3.name()));
                                    infoInDto.setKazJrxmlKbn(CommonConstants.NUMBER_3);
                                }
                            } else {
                                // 上記以外の場合
                                if (pictureOutputFlg1 == CommonConstants.INT_1) {
                                    // 作図出力フラグが1の場合
                                    preViewList.add(i, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.KAZ2.name()));
                                    infoInDto.setKazJrxmlKbn(CommonConstants.NUMBER_2);
                                } else {
                                    // 上記以外の場合
                                    preViewList.add(i, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.KAZ1.name()));
                                    infoInDto.setKazJrxmlKbn(CommonConstants.NUMBER_1);
                                }
                            }

                            // (3-4). 変数.出力対象更新
                            outputList.set(i, preViewList.get(i));
                            break;
                        case JUK1:
                            // (4). 変数.出力対象[i] が"d_kgh_krd_face1_juk1_rep" の場合
                            // (4-1). 作図出力フラグ取得
                            // 上記2.5. マスタ印刷設定取得のOUTPUT情報を（"koumoku_id = 4"）でフィルターする
                            Optional<FreeFaceMstPrintOutEntity> freeFaceMstPrintOut2 = freeFaceMstPrintOutList.stream()
                                    .filter(item -> item.getKoumokuId() == CommonConstants.INT_4).findFirst();
                            Integer pictureOutputFlg2;
                            if (freeFaceMstPrintOut2.isPresent()) {
                                // フィルター結果存在する場合、
                                pictureOutputFlg2 = freeFaceMstPrintOut2.get().getPictureFlg();
                            } else {
                                // フィルター結果存在しない場合、
                                pictureOutputFlg2 = CommonConstants.INT_0;
                            }

                            // (4-2). 見取図の有無設定
                            if (pictureOutputFlg2 == CommonConstants.INT_1) {
                                preViewList.add(i, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.JUK2.name()));
                                infoInDto.setJukJrxmlKbn(CommonConstants.NUMBER_2);
                            } else {
                                preViewList.add(i, OUTPUT_TARGET_CONSTANTS.get(OutputTarget.JUK1.name()));
                                infoInDto.setJukJrxmlKbn(CommonConstants.NUMBER_1);
                            }
                            break;
                        default:
                            // (5). 上記以外の場合、
                            preViewList.add(i, outputList.get(i));
                            break;
                    }
                }
            }
            if (preViewList.size() == i) {
                preViewList.add(i, outputList.get(i));
            }
        }

        // メモ欄印刷フラグ ＝ 共通関数補足の2.4. 共通関数「設定の読込」.OUTPUT情報.パラメータ
        f3gkGetProfileInDto.setKeyKnj(CommonConstants.KEY_MEMO);
        Integer memoPrintFlg = CommonDtoUtil.strValToInt(nds3GkFunc01Logic.getF3gkProfile(f3gkGetProfileInDto));
        // メモ区分 ＝ メモ欄印刷フラグ, メモ区分が1以外の場合、メモ区分 ＝0
        Integer memoKubun = null == memoPrintFlg ? CommonConstants.INT_0
                : memoPrintFlg == CommonConstants.INT_1 ? memoPrintFlg : CommonConstants.INT_0;
        setKoumokuSortList(outputList);
        if (CommonConstants.STR_TRUE.equals(emptyFlg)) {
            // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
            // デフォルト値の設定
            return getAssessmentFaceSheetTypeDefaultParameters(memoKubun, freeFaceMstPrintOutList);
        }

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = false の場合
        /*
         * ===============3. 出力情報設定処理===============
         * 
         */
        // 3.1. システム年月作成
        String systemDate = model.getSystemDate();
        // システム年月が""またNULLの場合
        if (!StringUtil.isNotEmpty(systemDate)) {
            // 現在の日時（"yyyy/mm/dd" ）
            DateTimeFormatter dTimeFormatter = DateTimeFormatter
                    .ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            systemDate = LocalDateTime.now().format(dTimeFormatter);
        } else {
            if (systemDate.contains(CommonConstants.TEL_SUF)) {
                systemDate = systemDate.replace(CommonConstants.TEL_SUF, CommonConstants.STR_DELIMITER);
            }
        }

        // 3.2. メモ欄表示設定

        // 3.3. 変数.出力対象の件数分、繰り返し、下記処理を行う
        for (int i = CommonConstants.INT_0; i < outputList.size(); i++) {
            for (OutputTarget target : OutputTarget.values()) {
                if (outputList.get(i).equals(OUTPUT_TARGET_CONSTANTS.get(target.name()))) {
                    switch (target) {
                        case KHN:
                            // 3.3.1. "d_kgh_krd_face1_khn_rep"の場合
                            // 基本情報の設定を行う
                            getBaseInfoParameters(infoInDto, kghKrkFaceSavKhnGetList, model, systemDate, memoPrintFlg,
                                    serviceType);
                            break;
                        case FTN:
                            // 3.3.2. "d_kgh_krd_face1_ftn_rep" の場合
                            // 負担限度の設定を行う
                            JRBeanCollectionDataSource ftnInfo = getBurdenLimitInfoParameters(
                                    history, serviceType);
                            setDynamicData(infoInDto, ftnInfo, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case KAZ1, KAZ2, KAZ3, KAZ4:
                            // 3.3.3. "d_kgh_krd_face1_kaz1_rep" また "d_kgh_krd_face1_kaz2_rep"
                            // また "d_kgh_krd_face1_kaz3_rep" また "d_kgh_krd_face1_kaz4_rep" の場合
                            // 親族関係、保護者の設定を行う
                            JRBeanCollectionDataSource familyRelationshipInfo = getFamilyRelationshipInfoParameters(
                                    history, systemDate, paramFor23, telFlg, printSet, serviceType);
                            setDynamicData(infoInDto, familyRelationshipInfo, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case JUK1, JUK2:
                            // 3.3.4. "d_kgh_krd_face1_juk1_rep" また "d_kgh_krd_face1_juk2_rep" の場合
                            // 住環境の設定を行う
                            JRBeanCollectionDataSource livingEnvironmentInfoList = getLivingEvnironmentInfoListParameters(
                                    history, memoPrintFlg, freeFaceMstPrintOutList, kghKrkFaceSavKhnGetList,
                                    serviceType);
                            setDynamicData(infoInDto, livingEnvironmentInfoList, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case KOZ:
                            // 3.3.5. "d_kgh_krd_face1_koz_rep" の場合
                            // 口座の設定を行う
                            JRBeanCollectionDataSource accountList = getAccountInfoParameters(history,
                                    freeFaceMstPrintOutList, memoPrintFlg, memoKubun, serviceType);
                            setDynamicData(infoInDto, accountList, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case SBT:
                            // 3.3.6. "d_kgh_krd_face1_sbt_rep" の場合
                            // 私物の設定を行う
                            JRBeanCollectionDataSource personalBelongingsList = getPersonalBelongingsParameters(
                                    history, freeFaceMstPrintOutList);
                            setDynamicData(infoInDto, personalBelongingsList, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case KIO:
                            // 3.3.7. "d_kgh_krd_face1_kio_rep" の場合
                            // 既往歴の設定を行う
                            JRBeanCollectionDataSource pastMedicalHistories = getPastMedicalHistoriesParameters(
                                    history, freeFaceMstPrintOutList, memoPrintFlg, memoKubun, serviceType);
                            setDynamicData(infoInDto, pastMedicalHistories, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case SKT:
                            // 3.3.8. "d_kgh_krd_face1_skt_rep" の場合
                            // 生活歴の設定を行う
                            JRBeanCollectionDataSource lifeHistories = getLifeHistoriesParameters(
                                    history, freeFaceMstPrintOutList, memoPrintFlg, memoKubun, serviceType);
                            setDynamicData(infoInDto, lifeHistories, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case RAN:
                            // 3.3.9. "d_kgh_krd_face1_ran_rep" の場合
                            // 自立度の設定を行う
                            JRBeanCollectionDataSource independenceLevelList = getIndependenceLevelParameters(history);
                            setDynamicData(infoInDto, independenceLevelList, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case KHJ, KHJ2:
                            // 3.3.10. "d_kgh_krd_face1_khj_rep" また "d_kgh_krd_face1_khj2_rep" の場合
                            // 基本状況の設定を行う
                            JRBeanCollectionDataSource jokyoList = getJoKyoParameters(houJinId, shisetuId, svJigyoId,
                                    freeFaceMstPrintOutList, history, systemDate, baseFlg);
                            setDynamicData(infoInDto, jokyoList, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case SYM:
                            // 3.3.11. "d_kgh_krd_face1_sym_rep" の場合
                            // 趣味嗜好の設定を行う
                            JRBeanCollectionDataSource hobbyAndPreferences = getHobbyAndPreferencesParameters(
                                    history, freeFaceMstPrintOutList, memoPrintFlg, memoKubun, serviceType);
                            setDynamicData(infoInDto, hobbyAndPreferences, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case ADL:
                            // 3.3.12. "d_kgh_krd_face1_adl_rep" の場合
                            // ADL評価の設定を行う
                            JRBeanCollectionDataSource adlEvaluationList = getReportInspectionsParameters(
                                    history, svJigyoId, CommonConstants.INT_16);
                            setDynamicData(infoInDto, adlEvaluationList, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case JTZ:
                            // 3.3.13. "d_kgh_krd_face1_jtz_rep" の場合
                            // 人体図の設定を行う
                            JRBeanCollectionDataSource bodySituations = getBodySituationsParameters(
                                    history, bodyImgFlg, serviceType);
                            setDynamicData(infoInDto, bodySituations, ReportConstants.INT_0, i, Boolean.FALSE);
                        case KKS:
                            // 3.3.13. "d_kgh_krd_face1_kks_rep" の場合
                            // 健康診断の設定を行う
                            JRBeanCollectionDataSource healthDiagnosisList = getReportInspectionsParameters(
                                    history, svJigyoId, CommonConstants.INT_17);
                            setDynamicData(infoInDto, healthDiagnosisList, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case HOK1:
                            // 3.3.15. "d_kgh_krd_face1_hok1_rep" の場合
                            // 介護保険の設定を行う
                            JRBeanCollectionDataSource nursingCareInsuranceInfo = getNursingCareInsuranceInfoParamters(
                                    history, memoPrintFlg, memoKubun, serviceType);
                            setDynamicData(infoInDto, nursingCareInsuranceInfo, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case HOK2:
                            // 3.3.16. "d_kgh_krd_face1_hok2_rep" の場合
                            // 主保健の設定を行う
                            JRBeanCollectionDataSource medicalInsurances = getMedicalInsurancesParameters(
                                    history, freeFaceMstPrintOutList, serviceType);
                            setDynamicData(infoInDto, medicalInsurances, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case HOK3:
                            // 3.3.17. "d_kgh_krd_face1_hok3_rep" の場合
                            // 老人保健の設定を行う
                            JRBeanCollectionDataSource elderlyHealthInfo = getElderlyHealthInfoParamters(
                                    history, serviceType);
                            setDynamicData(infoInDto, elderlyHealthInfo, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case HOK4:
                            // 3.3.18. "d_kgh_krd_face1_hok4_rep" の場合
                            // 公費の設定を行う
                            JRBeanCollectionDataSource publicExpenditureInfo = getPublicExpenditureInfoParamters(
                                    history);
                            setDynamicData(infoInDto, publicExpenditureInfo, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case TEC_SI:
                            // 3.3.19. "d_kgh_krd_face1_tec_si_rep" の場合
                            // 身障手帳の設定を行う
                            JRBeanCollectionDataSource bodyHandycapNotebook = getBodyHandycapNotebookParameters(
                                    history, memoPrintFlg, memoKubun, serviceType);
                            setDynamicData(infoInDto, bodyHandycapNotebook, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case TEC_RY:
                            // 3.3.20. "d_kgh_krd_face1_tec_ry_rep" の場合
                            // 療育手帳の設定を行う
                            JRBeanCollectionDataSource remedialEducationNotebook = getRemedialEducationNotebookParameters(
                                    history, freeFaceMstPrintOutList, memoPrintFlg, memoKubun, serviceType);
                            setDynamicData(infoInDto, remedialEducationNotebook, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case TEC_SE:
                            // 3.3.21. "d_kgh_krd_face1_tec_se_rep" の場合
                            // 精神手帳の設定を行う
                            JRBeanCollectionDataSource mentalNotebook = getMentalNotebookParamters(
                                    history, memoPrintFlg, memoKubun, serviceType);
                            setDynamicData(infoInDto, mentalNotebook, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case TEC_NE:
                            // 3.3.22. "d_kgh_krd_face1_tec_ne_rep" の場合
                            // 年金手帳の設定を行う
                            JRBeanCollectionDataSource pensionNotebooks = getPensionNotebooksParameters(
                                    history, freeFaceMstPrintOutList, memoPrintFlg, memoKubun, serviceType);
                            setDynamicData(infoInDto, pensionNotebooks, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case TEC_GE:
                            // 3.3.23. "d_kgh_krd_face1_tec_ge_rep" の場合
                            // 原爆手帳の設定を行う
                            JRBeanCollectionDataSource atomicBombNotebook = getAtomicBombNotebookParamters(
                                    history, memoPrintFlg, memoKubun, serviceType);
                            setDynamicData(infoInDto, atomicBombNotebook, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case TEC_ET:
                            // 3.3.24. "d_kgh_krd_face1_tec_et_rep" の場合
                            // 他の手帳の設定を行う
                            JRBeanCollectionDataSource otherNotebookList = getOtherNotebookListParamters(history,
                                    freeFaceMstPrintOutList,
                                    memoPrintFlg, memoKubun, serviceType);
                            setDynamicData(infoInDto, otherNotebookList, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case SRV:
                            // 3.3.25. "d_kgh_krd_face1_srv_rep" の場合
                            // サービスの設定を行う
                            JRBeanCollectionDataSource serviceList = getServiceListParamters(history,
                                    freeFaceMstPrintOutList,
                                    memoPrintFlg, memoKubun, serviceType);
                            setDynamicData(infoInDto, serviceList, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case KNS:
                            // 3.3.26. "d_kgh_krd_face1_kns_rep" の場合
                            // 検査の設定を行う
                            JRBeanCollectionDataSource inspections = getReportInspectionsParameters(
                                    history, svJigyoId, CommonConstants.INT_29);
                            setDynamicData(infoInDto, inspections, ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        return infoInDto;
    }

    /**
     * デフォルト値の設定
     * 
     * @param memoKubun               メモ欄の印刷区分
     * @param freeFaceMstPrintOutList マスタ印刷設定
     * @return 帳票用データ詳細
     * @throws Exception 例外
     */
    protected AssessmentFaceSheetTypeReportServiceInDto getAssessmentFaceSheetTypeDefaultParameters(Integer memoKubun,
            List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutList)
            throws Exception {
        // 帳票用データ詳細
        AssessmentFaceSheetTypeReportServiceInDto inDto = new AssessmentFaceSheetTypeReportServiceInDto();

        // 印刷行数
        Integer lineCnt = CommonConstants.INT_0;
        Optional<FreeFaceMstPrintOutEntity> printConfig = Optional.empty();

        // 利用者番号
        inDto.setSelfId(CommonConstants.BLANK_STRING);
        // 氏名カナ
        inDto.setNameKana(CommonConstants.BLANK_STRING);
        // 氏名
        inDto.setNameKnj(CommonConstants.BLANK_STRING);
        // 性別
        inDto.setSex(CommonConstants.INT_MINUS_1);
        // 血液型名
        inDto.setAboKnj(CommonConstants.BLANK_STRING);
        // RH名
        inDto.setRhKnj(CommonConstants.BLANK_STRING);
        // 生年月日（年号）
        inDto.setBirthdayYmdGG(CommonConstants.BLANK_STRING);
        // 生年月日（年）
        inDto.setBirthdayYmdYY(CommonConstants.BLANK_STRING);
        // 生年月日（月）
        inDto.setBirthdayYmdMM(CommonConstants.BLANK_STRING);
        // 生年月日（日）
        inDto.setBirthdayYmdDD(CommonConstants.BLANK_STRING);
        // 年齢
        inDto.setAge(CommonConstants.FULL_WIDTH_SPACE);
        // 現住所
        inDto.setAddressKnj(CommonConstants.BLANK_STRING);
        // 現住所フォント
        inDto.setAddressKnjFont(CommonConstants.BLANK_STRING);
        // 電話番号
        inDto.setTel(CommonConstants.BLANK_STRING);
        // 本籍地住所
        inDto.setHonseki(CommonConstants.BLANK_STRING);
        // 顔写真パス
        inDto.setBmpPath(CommonConstants.BLANK_STRING);
        // 撮影日（年号）
        inDto.setBmpYmdGG(CommonConstants.BLANK_STRING);
        // 撮影日（年）
        inDto.setBmpYmdYY(CommonConstants.BLANK_STRING);
        // 撮影日（月）
        inDto.setBmpYmdMM(CommonConstants.BLANK_STRING);
        // 撮影日（日）
        inDto.setBmpYmdDD(CommonConstants.BLANK_STRING);
        // メモ
        inDto.setMemoKnj(CommonConstants.BLANK_STRING);
        // メモ区分
        inDto.setMemoKbn(memoKubun);

        for (int i = CommonConstants.INT_0; i < koumokuSortList.size(); i++) {
            for (OutputTarget target : OutputTarget.values()) {
                if (koumokuSortList.get(i).equals(OUTPUT_TARGET_CONSTANTS.get(target.name()))) {
                    switch (target) {
                        case KHN:
                            break;
                        case FTN:
                            // 負担限度表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 負担限度情報
                            ReportAssessmentFaceSheetTypeBurdenLimitInfo burdenLimitInfo = new ReportAssessmentFaceSheetTypeBurdenLimitInfo();
                            List<ReportAssessmentFaceSheetTypeBurdenLimitInfo> burdenLimitInfoList = new ArrayList<ReportAssessmentFaceSheetTypeBurdenLimitInfo>();
                            setPropertyDefaultValue(burdenLimitInfo);
                            burdenLimitInfo.setHokenKnjFont(null);
                            burdenLimitInfoList.add(burdenLimitInfo);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(burdenLimitInfoList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case KAZ1, KAZ2, KAZ3, KAZ4:
                            // 親族関係表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 親族情報
                            ReportAssessmentFaceSheetTypeFamilyRelationshipInfo familyRelationshipInfo = new ReportAssessmentFaceSheetTypeFamilyRelationshipInfo();
                            List<ReportAssessmentFaceSheetTypeFamilyRelationshipInfo> familyRelationshipInfoList = new ArrayList<ReportAssessmentFaceSheetTypeFamilyRelationshipInfo>();
                            List<U06050FamilyRelationship> familyRelationshipList = new ArrayList<U06050FamilyRelationship>();
                            setPropertyDefaultValue(familyRelationshipInfo);
                            for (int j = CommonConstants.INT_0; j < CommonConstants.INT_6; j++) {
                                U06050FamilyRelationship familyRelationship = new U06050FamilyRelationship();
                                setPropertyDefaultValue(familyRelationship);
                                familyRelationship.setSex(null);
                                familyRelationship.setMemoFont(null);
                                familyRelationshipList.add(familyRelationship);
                            }
                            familyRelationshipInfo.setHogoAddressKnjFont(null);
                            familyRelationshipInfo
                                    .setFamilyRelationshipList(new JRBeanCollectionDataSource(familyRelationshipList));
                            familyRelationshipInfoList.add(familyRelationshipInfo);
                            inDto.setKazJrxmlKbn(CommonConstants.INT_1);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(familyRelationshipInfoList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case JUK1, JUK2:
                            // 3.3.4. "d_kgh_krd_face1_juk1_rep" また "d_kgh_krd_face1_juk2_rep" の場合
                            printConfig = freeFaceMstPrintOutList.stream()
                                    .filter(item -> item.getKoumokuId() == CommonConstants.INT_4).findFirst();
                            if (printConfig.isPresent()) {
                                lineCnt = printConfig.get().getLineCnt();
                            }
                            // 住環境の設定を行う
                            // 住環境表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 住環境情報
                            List<ReportAssessmentFaceSheetTypeLivingEnvironmentInfo> livingEvnironmentInfoList = new ArrayList<>();
                            List<Map<String, Object>> livingEvnironmentInfoMapList = new ArrayList<>();
                            Map<String, Object> livingEvnironmentInfoMap = new HashMap<>();
                            ReportAssessmentFaceSheetTypeLivingEnvironmentInfo livingEnvironmentInfo = new ReportAssessmentFaceSheetTypeLivingEnvironmentInfo();
                            setPropertyDefaultValue(livingEnvironmentInfo);
                            livingEnvironmentInfo.setMemoKbn(memoKubun);
                            livingEnvironmentInfo.setMemoFont(null);
                            for (int line = CommonConstants.INT_0; line < lineCnt; line++) {
                                livingEvnironmentInfoList.add(livingEnvironmentInfo);
                            }
                            livingEvnironmentInfoMap.put(ReportConstants.JRDS_JUKLIST_KEY,
                                    new JRBeanCollectionDataSource(livingEvnironmentInfoList));
                            livingEvnironmentInfoMap.put(ReportConstants.JRDS_TITLE_KEY, memoKubun);
                            livingEvnironmentInfoMapList.add(livingEvnironmentInfoMap);
                            inDto.setJukJrxmlKbn(CommonConstants.INT_1);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(livingEvnironmentInfoMapList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case KOZ:
                            // 3.3.5. "d_kgh_krd_face1_koz_rep" の場合
                            printConfig = freeFaceMstPrintOutList.stream()
                                    .filter(item -> item.getKoumokuId() == CommonConstants.INT_5).findFirst();
                            if (printConfig.isPresent()) {
                                lineCnt = printConfig.get().getLineCnt();
                            }
                            // 口座の設定を行う
                            // 口座表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 口座リスト
                            List<ReportAssessmentFaceSheetTypeAccount> accountList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeAccount account = new ReportAssessmentFaceSheetTypeAccount();
                            List<Map<String, Object>> accountMapList = new ArrayList<>();
                            Map<String, Object> accountMap = new HashMap<>();
                            setPropertyDefaultValue(account);
                            account.setMemoKnjFont(null);
                            account.setGinstnKnjFont(null);
                            account.setMemoKbn(memoKubun);
                            for (int line = CommonConstants.INT_0; line < lineCnt; line++) {
                                accountList.add(account);
                            }
                            accountMap.put(ReportConstants.JRDS_KOZLIST_KEY, new JRBeanCollectionDataSource(accountList));
                            accountMap.put(ReportConstants.JRDS_TITLE_KEY, memoKubun);
                            accountMapList.add(accountMap);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(accountMapList), ReportConstants.INT_0,
                                    i, Boolean.FALSE);
                            break;
                        case SBT:
                            printConfig = freeFaceMstPrintOutList.stream()
                                    .filter(item -> item.getKoumokuId() == CommonConstants.INT_6).findFirst();
                            if (printConfig.isPresent()) {
                                lineCnt = printConfig.get().getLineCnt();
                            }
                            // 私物表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 私物リスト
                            List<ReportAssessmentFaceSheetTypePersonalBelongings> personalBelongingsList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypePersonalBelongings personalBelongings = new ReportAssessmentFaceSheetTypePersonalBelongings();
                            List<Map<String, Object>> personalBelongingsMapList = new ArrayList<>();
                            Map<String, Object> personalBelongingsMap = new HashMap<>();
                            setPropertyDefaultValue(personalBelongings);
                            for (int line = CommonConstants.INT_0; line < lineCnt; line++) {
                                personalBelongingsList.add(personalBelongings);
                            }
                            personalBelongingsMap.put(ReportConstants.JRDS_SBTLIST_KEY,
                                    new JRBeanCollectionDataSource(personalBelongingsList));
                            personalBelongingsMapList.add(personalBelongingsMap);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(personalBelongingsMapList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case KIO:
                            printConfig = freeFaceMstPrintOutList.stream()
                                    .filter(item -> item.getKoumokuId() == CommonConstants.INT_7).findFirst();
                            if (printConfig.isPresent()) {
                                lineCnt = printConfig.get().getLineCnt();
                            }
                            // 既往歴表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 既往歴リスト
                            List<ReportAssessmentFaceSheetTypePastMedicalHistory> pastMedicalHistoryList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypePastMedicalHistory pastMedicalHistory = new ReportAssessmentFaceSheetTypePastMedicalHistory();
                            List<Map<String, Object>> pastMedicalHistoryMapList = new ArrayList<>();
                            Map<String, Object> pastMedicalHistoryMap = new HashMap<>();
                            setPropertyDefaultValue(pastMedicalHistory);
                            pastMedicalHistory.setDragKbn(null);
                            pastMedicalHistory.setSickKnjFont(null);
                            pastMedicalHistory.setDetailsFont(null);
                            pastMedicalHistory.setMemoKbn(memoKubun);
                            for (int line = CommonConstants.INT_0; line < lineCnt; line++) {
                                pastMedicalHistoryList.add(pastMedicalHistory);
                            }
                            pastMedicalHistoryMap.put(ReportConstants.JRDS_KIOLIST_KEY,
                                    new JRBeanCollectionDataSource(pastMedicalHistoryList));
                            pastMedicalHistoryMapList.add(pastMedicalHistoryMap);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(pastMedicalHistoryMapList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case SKT:
                            printConfig = freeFaceMstPrintOutList.stream()
                                    .filter(item -> item.getKoumokuId() == CommonConstants.INT_8).findFirst();
                            if (printConfig.isPresent()) {
                                lineCnt = printConfig.get().getLineCnt();
                            }
                            // 生活歴表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 生活歴リスト
                            List<ReportAssessmentFaceSheetTypeLifeHistory> lifeHistoryList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeLifeHistory lifeHistory = new ReportAssessmentFaceSheetTypeLifeHistory();
                            List<Map<String, Object>> lifeHistoriesMapList = new ArrayList<>();
                            Map<String, Object> lifeHistoriesMap = new HashMap<>();
                            setPropertyDefaultValue(lifeHistory);
                            lifeHistory.setLifeMemoKbn(memoKubun);
                            lifeHistory.setLifeLabel(ReportConstants.LIFE_LABEL);
                            lifeHistory.setLifeMemoKnjFont(null);
                            for (int line = CommonConstants.INT_0; line < lineCnt; line++) {
                                lifeHistoryList.add(lifeHistory);
                            }
                            lifeHistoriesMap.put(ReportConstants.JRDS_SKTLIST_KEY, new JRBeanCollectionDataSource(lifeHistoryList));
                            lifeHistoriesMap.put("lifeMemoKbn", lifeHistory.getLifeMemoKbn());
                            lifeHistoriesMap.put("lifeLabel", lifeHistory.getLifeLabel());
                            lifeHistoriesMap.put("lifeMemoKnj", lifeHistory.getLifeMemoKnj());
                            lifeHistoriesMap.put("lifeMemoKnjFont", lifeHistory.getLifeMemoKnjFont());
                            lifeHistoriesMapList.add(lifeHistoriesMap);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(lifeHistoriesMapList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case RAN:
                            // 自立度表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 自立度リスト
                            List<ReportAssessmentFaceSheetTypeIndependenceLevel> independenceLevelList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeIndependenceLevel independenceLevel = new ReportAssessmentFaceSheetTypeIndependenceLevel();
                            setPropertyDefaultValue(independenceLevel);
                            independenceLevelList.add(independenceLevel);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(independenceLevelList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case KHJ, KHJ2:
                            printConfig = freeFaceMstPrintOutList.stream()
                                    .filter(item -> item.getKoumokuId() == CommonConstants.INT_12).findFirst();
                            if (printConfig.isPresent()) {
                                lineCnt = printConfig.get().getLineCnt();
                            }
                            // 基本状況表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 基本状況リスト
                            List<ReportAssessmentFaceSheetTypeBasicSituation> basicSituationList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeBasicSituation basicSituation = new ReportAssessmentFaceSheetTypeBasicSituation();
                            List<Map<String, JRBeanCollectionDataSource>> khjList = new ArrayList<>();
                            Map<String, JRBeanCollectionDataSource> khjMap = new HashMap<>();
                            setPropertyDefaultValue(basicSituation);
                            for (int line = CommonConstants.INT_0; line < lineCnt; line++) {
                                basicSituationList.add(basicSituation);
                            }
                            khjMap.put(ReportConstants.JRDS_KHJLIST_KEY, new JRBeanCollectionDataSource(basicSituationList));
                            khjList.add(khjMap);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(khjList), ReportConstants.INT_0, i,
                                    Boolean.FALSE);
                            break;
                        case SYM:
                            printConfig = freeFaceMstPrintOutList.stream()
                                    .filter(item -> item.getKoumokuId() == CommonConstants.INT_13).findFirst();
                            if (printConfig.isPresent()) {
                                lineCnt = printConfig.get().getLineCnt();
                            }
                            // 趣味嗜好表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 趣味嗜好リスト
                            List<ReportAssessmentFaceSheetTypeHobbyAndPreference> hobbyAndPreferenceList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeHobbyAndPreference hobbyAndPreference = new ReportAssessmentFaceSheetTypeHobbyAndPreference();
                            List<Map<String, Object>> hobbyAndPreferencesMapList = new ArrayList<>();
                            Map<String, Object> hobbyAndPreferencesMap = new HashMap<>();
                            setPropertyDefaultValue(hobbyAndPreference);
                            hobbyAndPreference.setSymMemoKbn(null);
                            hobbyAndPreference.setShu2KnjFont(null);
                            hobbyAndPreference.setSymMemoKbn(memoKubun);
                            for (int line = CommonConstants.INT_0; line < lineCnt; line++) {
                                hobbyAndPreferenceList.add(hobbyAndPreference);
                            }
                            hobbyAndPreferencesMap.put(ReportConstants.JRDS_SYMLIST_KEY,
                                    new JRBeanCollectionDataSource(hobbyAndPreferenceList));
                            hobbyAndPreferencesMap.put("symMemoKbn", hobbyAndPreference.getSymMemoKbn());
                            hobbyAndPreferencesMap.put("symMemoKnj", hobbyAndPreference.getSymMemoKnj());
                            hobbyAndPreferencesMapList.add(hobbyAndPreferencesMap);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(hobbyAndPreferencesMapList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case ADL:
                            // ADL評価表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // ADL評価リスト
                            List<ReportAssessmentFaceSheetTypeAdlEvaluation> adlEvaluationList = new ArrayList<>();
                            List<ReportAssessmentFaceSheetTypeAdlEvaluationInfo> adlEvaluationInfoList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeAdlEvaluation adlEvaluation = new ReportAssessmentFaceSheetTypeAdlEvaluation();
                            ReportAssessmentFaceSheetTypeAdlEvaluationInfo adlEvaluationInfo = new ReportAssessmentFaceSheetTypeAdlEvaluationInfo();
                            List<Map<String, Object>> adlEvaluationMapList = new ArrayList<>();
                            Map<String, Object> adlEvaluationMap = new HashMap<>();
                            adlEvaluationInfo.setAdlKoumokuKnj(CommonConstants.BLANK_STRING);
                            adlEvaluationInfo.setAdlNaiyouKnj(CommonConstants.BLANK_STRING);
                            adlEvaluationInfoList.add(adlEvaluationInfo);

                            adlEvaluation.setAdlKoumokuTitle(CommonConstants.BLANK_STRING);
                            adlEvaluation.setAdlKoumokuTitleFont(null);
                            adlEvaluation.setAdlInfoList(new JRBeanCollectionDataSource(adlEvaluationInfoList));
                            adlEvaluationList.add(adlEvaluation);
                            adlEvaluationMap.put(ReportConstants.JRDS_ADLLIST_KEY, new JRBeanCollectionDataSource(adlEvaluationList));
                            adlEvaluationMapList.add(adlEvaluationMap);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(adlEvaluationMapList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case JTZ:
                            // 人体図表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 身体状況リスト
                            List<ReportAssessmentFaceSheetTypeBodySituation> bodySituationList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeBodySituation bodySituation = new ReportAssessmentFaceSheetTypeBodySituation();
                            setPropertyDefaultValue(bodySituation);
                            bodySituationList.add(bodySituation);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(bodySituationList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                        case KKS:
                            // 健康診断表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 健康診断リスト
                            List<ReportAssessmentFaceSheetTypeHealthDiagnosis> healthDiagnosisList = new ArrayList<>();
                            List<ReportAssessmentFaceSheetTypeHealthDiagnosisInfo> healthDiagnosisInfoList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeHealthDiagnosis healthDiagnosis = new ReportAssessmentFaceSheetTypeHealthDiagnosis();
                            ReportAssessmentFaceSheetTypeHealthDiagnosisInfo healthDiagnosisInfo = new ReportAssessmentFaceSheetTypeHealthDiagnosisInfo();
                            List<Map<String, Object>> healthDiagnosisMapList = new ArrayList<>();
                            Map<String, Object> healthDiagnosisMap = new HashMap<>();
                            healthDiagnosisInfo.setKksKoumokuKnj(CommonConstants.BLANK_STRING);
                            healthDiagnosisInfo.setKksNaiyouKnj(CommonConstants.BLANK_STRING);
                            healthDiagnosisInfoList.add(healthDiagnosisInfo);

                            healthDiagnosis.setKksKoumokuTitle(ReportConstants.HEALTH_DIAGNOSIS_LIST_LABEL);
                            healthDiagnosis.setKksKoumokuTitleFont(ReportConstants.FONT_SIZE_12);
                            healthDiagnosis.setKksInfoList(new JRBeanCollectionDataSource(healthDiagnosisInfoList));
                            healthDiagnosisList.add(healthDiagnosis);
                            healthDiagnosisMap.put(ReportConstants.JRDS_ADLLIST_KEY,
                                    new JRBeanCollectionDataSource(healthDiagnosisList));
                            healthDiagnosisMapList.add(healthDiagnosisMap);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(healthDiagnosisMapList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case HOK1:
                            // 介護保険表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 介護保険情報
                            List<ReportAssessmentFaceSheetTypeNursingCareInsuranceInfo> nursingCareInsuranceInfoList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeNursingCareInsuranceInfo nursingCareInsuranceInfo = new ReportAssessmentFaceSheetTypeNursingCareInsuranceInfo();
                            setPropertyDefaultValue(nursingCareInsuranceInfo);
                            nursingCareInsuranceInfo.setKaigoMemoKnjFont(null);
                            nursingCareInsuranceInfo.setKaigoMemoKbn(memoKubun);
                            nursingCareInsuranceInfoList.add(nursingCareInsuranceInfo);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(nursingCareInsuranceInfoList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case HOK2:
                            printConfig = freeFaceMstPrintOutList.stream()
                                    .filter(item -> item.getKoumokuId() == CommonConstants.INT_19).findFirst();
                            if (printConfig.isPresent()) {
                                lineCnt = printConfig.get().getLineCnt();
                            }
                            // 主保健表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 医療保険リスト
                            List<ReportAssessmentFaceSheetTypeMedicalInsurance> medicalInsuranceList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeMedicalInsurance medicalInsurance = new ReportAssessmentFaceSheetTypeMedicalInsurance();
                            List<Map<String, Object>> medicalInsurancesMapList = new ArrayList<>();
                            Map<String, Object> medicalInsurancesMap = new HashMap<>();
                            setPropertyDefaultValue(medicalInsurance);
                            medicalInsurance.setIryouHokenKnjFont(null);
                            medicalInsurance.setKigonoKnjFont(ReportConstants.FONT_SIZE_12);
                            for (int line = CommonConstants.INT_0; line < lineCnt; line++) {
                                medicalInsuranceList.add(medicalInsurance);
                            }
                            medicalInsurancesMap.put(ReportConstants.JRDS_HOK2LIST_KEY,
                                    new JRBeanCollectionDataSource(medicalInsuranceList));
                            medicalInsurancesMapList.add(medicalInsurancesMap);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(medicalInsurancesMapList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case HOK3:
                            // 老人保健表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 老人保健情報
                            List<ReportAssessmentFaceSheetTypeElderlyHealthInfo> elderlyHealthInfoList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeElderlyHealthInfo elderlyHealthInfo = new ReportAssessmentFaceSheetTypeElderlyHealthInfo();
                            setPropertyDefaultValue(elderlyHealthInfo);
                            elderlyHealthInfo.setHokenKnjFontSize(null);
                            elderlyHealthInfoList.add(elderlyHealthInfo);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(elderlyHealthInfoList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case HOK4:
                            // 公費表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 公費情報
                            List<ReportAssessmentFaceSheetTypePublicExpenditureInfo> publicExpenditureInfoList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypePublicExpenditureInfo publicExpenditureInfo = new ReportAssessmentFaceSheetTypePublicExpenditureInfo();
                            setPropertyDefaultValue(publicExpenditureInfo);
                            publicExpenditureInfoList.add(publicExpenditureInfo);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(publicExpenditureInfoList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case TEC_SI:
                            // 身障手帳表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 身障手帳
                            List<ReportAssessmentFaceSheetTypeBodyHandycapNotebook> bodyHandycapNotebookList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeBodyHandycapNotebook bodyHandycapNotebook = new ReportAssessmentFaceSheetTypeBodyHandycapNotebook();
                            setPropertyDefaultValue(bodyHandycapNotebook);
                            bodyHandycapNotebook.setShougaiKnjFontSize(null);
                            bodyHandycapNotebook.setMemoKbn(memoKubun);
                            bodyHandycapNotebookList.add(bodyHandycapNotebook);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(bodyHandycapNotebookList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case TEC_RY:
                            // 療育手帳表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 療育手帳
                            List<ReportAssessmentFaceSheetTypeRemedialEducationNotebook> remedialEducationNotebookList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeRemedialEducationNotebook remedialEducationNotebook = new ReportAssessmentFaceSheetTypeRemedialEducationNotebook();
                            setPropertyDefaultValue(remedialEducationNotebook);
                            remedialEducationNotebook.setHannteikikanKnjFontSize(null);
                            remedialEducationNotebook.setShougaiKnjFontSize(ReportConstants.FONT_SIZE_12);
                            remedialEducationNotebook.setMemoKbn(memoKubun);
                            remedialEducationNotebookList.add(remedialEducationNotebook);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(remedialEducationNotebookList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case TEC_SE:
                            // 精神手帳表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 精神手帳
                            List<ReportAssessmentFaceSheetTypeMentalNotebook> mentalNotebookList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeMentalNotebook mentalNotebook = new ReportAssessmentFaceSheetTypeMentalNotebook();
                            setPropertyDefaultValue(mentalNotebook);
                            mentalNotebook.setMemoKnjFontSize(ReportConstants.FONT_SIZE_12);
                            mentalNotebook.setMemoKbn(memoKubun);
                            mentalNotebookList.add(mentalNotebook);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(mentalNotebookList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case TEC_NE:
                            printConfig = freeFaceMstPrintOutList.stream()
                                    .filter(item -> item.getKoumokuId() == CommonConstants.INT_25).findFirst();
                            if (printConfig.isPresent()) {
                                lineCnt = printConfig.get().getLineCnt();
                            }
                            // 年金手帳表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 年金手帳リスト
                            List<ReportAssessmentFaceSheetTypePensionNotebook> pensionNotebookList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypePensionNotebook pensionNotebook = new ReportAssessmentFaceSheetTypePensionNotebook();
                            List<Map<String, Object>> pensionNotebookMapList = new ArrayList<>();
                            Map<String, Object> pensionNotebookMap = new HashMap<>();
                            setPropertyDefaultValue(pensionNotebook);
                            pensionNotebook.setNenkinKnjFontSize(null);
                            pensionNotebook.setKingakuFontSize(null);
                            pensionNotebook.setSikyuuKnjFontSize(null);
                            pensionNotebook.setMemoKbn(memoKubun);
                            for (int line = CommonConstants.INT_0; line < lineCnt; line++) {
                                pensionNotebookList.add(pensionNotebook);
                            }
                            pensionNotebookMap.put(ReportConstants.JRDS_TECNELIST_KEY,
                                    new JRBeanCollectionDataSource(pensionNotebookList));
                            pensionNotebookMap.put(ReportConstants.JRDS_TITLE_KEY, memoKubun);
                            pensionNotebookMapList.add(pensionNotebookMap);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(pensionNotebookMapList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case TEC_GE:
                            // 原爆手帳表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 原爆手帳
                            List<ReportAssessmentFaceSheetTypeAtomicBombNotebook> atomicBombNotebookList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeAtomicBombNotebook atomicBombNotebook = new ReportAssessmentFaceSheetTypeAtomicBombNotebook();
                            setPropertyDefaultValue(atomicBombNotebook);
                            atomicBombNotebook.setPlaceKnjFont(null);
                            atomicBombNotebook.setMoveKnjFont(null);
                            atomicBombNotebook.setJyoukyouKnjFont(null);
                            atomicBombNotebook.setShoujyouKnjFont(null);
                            atomicBombNotebook.setByoumeiKnjFont(null);
                            atomicBombNotebook.setMemoKnjFontSize(null);
                            atomicBombNotebook.setMemoKbn(memoKubun);
                            atomicBombNotebookList.add(atomicBombNotebook);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(atomicBombNotebookList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case TEC_ET:
                            printConfig = freeFaceMstPrintOutList.stream()
                                    .filter(item -> item.getKoumokuId() == CommonConstants.INT_27).findFirst();
                            if (printConfig.isPresent()) {
                                lineCnt = printConfig.get().getLineCnt();
                            }
                            // その他手帳表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 他の手帳リスト
                            List<ReportAssessmentFaceSheetTypeOtherNotebook> otherNotebookList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeOtherNotebook otherNotebook = new ReportAssessmentFaceSheetTypeOtherNotebook();
                            List<Map<String, Object>> otherNotebookMapList = new ArrayList<>();
                            Map<String, Object> otherNotebookMap = new HashMap<>();
                            setPropertyDefaultValue(otherNotebook);
                            otherNotebook.setMemoKnjFontSize(null);
                            otherNotebook.setMemoKbn(memoKubun);
                            for (int line = CommonConstants.INT_0; line < lineCnt; line++) {
                                otherNotebookList.add(otherNotebook);
                            }
                            otherNotebookMap.put(ReportConstants.JRDS_TECETLIST_KEY, new JRBeanCollectionDataSource(otherNotebookList));
                            otherNotebookMap.put(ReportConstants.JRDS_TITLE_KEY, memoKubun);
                            otherNotebookMapList.add(otherNotebookMap);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(otherNotebookMapList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        case SRV:
                            printConfig = freeFaceMstPrintOutList.stream()
                                    .filter(item -> item.getKoumokuId() == CommonConstants.INT_28).findFirst();
                            if (printConfig.isPresent()) {
                                lineCnt = printConfig.get().getLineCnt();
                            }
                            // サービス表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // サービスリスト
                            List<ReportAssessmentFaceSheetTypeUseService> serviceList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeUseService service = new ReportAssessmentFaceSheetTypeUseService();
                            List<Map<String, Object>> serviceMapList = new ArrayList<>();
                            Map<String, Object> serviceMap = new HashMap<>();
                            setPropertyDefaultValue(service);
                            service.setSvJigyoKnjFont(null);
                            service.setMemoKnjFont(null);
                            service.setMemoKbn(memoKubun);
                            for (int line = CommonConstants.INT_0; line < lineCnt; line++) {
                                serviceList.add(service);
                            }
                            serviceMap.put(ReportConstants.JRDS_SRVLIST_KEY, new JRBeanCollectionDataSource(serviceList));
                            serviceMap.put(ReportConstants.JRDS_TITLE_KEY, memoKubun);
                            serviceMapList.add(serviceMap);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(serviceMapList), ReportConstants.INT_0,
                                    i, Boolean.FALSE);
                            break;
                        case KNS:
                            // 検査表示フラグ
                            setDynamicData(inDto, null, CommonConstants.INT_1, i, Boolean.TRUE);
                            // 検査リスト
                            List<ReportAssessmentFaceSheetTypeInspection> inspectionList = new ArrayList<>();
                            List<ReportAssessmentFaceSheetTypeInspectionInfo> inspectionInfoList = new ArrayList<>();
                            ReportAssessmentFaceSheetTypeInspection inspection = new ReportAssessmentFaceSheetTypeInspection();
                            ReportAssessmentFaceSheetTypeInspectionInfo inspectionInfo = new ReportAssessmentFaceSheetTypeInspectionInfo();
                            List<Map<String, Object>> inspectionMapList = new ArrayList<>();
                            Map<String, Object> inspectionMap = new HashMap<>();
                            inspectionInfo.setKnsKoumokuKnj(CommonConstants.BLANK_STRING);
                            inspectionInfo.setKnsNaiyouKnj(CommonConstants.BLANK_STRING);
                            inspectionInfoList.add(inspectionInfo);

                            inspection.setKnsKoumokuTitle(ReportConstants.INSPECTION_LIST_LABEL);
                            inspection.setKnsKoumokuTitleFont(ReportConstants.FONT_SIZE_12);
                            inspection.setKnsInfoList(new JRBeanCollectionDataSource(inspectionInfoList));
                            inspectionList.add(inspection);
                            inspectionMap.put(ReportConstants.JRDS_ADLLIST_KEY, new JRBeanCollectionDataSource(inspectionList));
                            inspectionMapList.add(inspectionMap);
                            setDynamicData(inDto, new JRBeanCollectionDataSource(inspectionMapList),
                                    ReportConstants.INT_0, i, Boolean.FALSE);
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        // 空欄で印刷フラグ
        inDto.setEmptyFlg(Boolean.TRUE);

        return inDto;
    }

    /**
     * 別紙基本情報設定
     * 
     * @param infoInDto               帳票用データ詳細
     * @param kghKrkFaceSavKhnGetList 基本情報
     * @param model                   リクエストパラメータ.データ
     * @param systemDate              システム日付
     * @param memoPrintFlg            メモ欄印刷フラグ
     * @param serviceType             帳票区分
     * @return 帳票用データ.基本情報
     * @throws Exception 例外
     */
    public void getBaseInfoParameters(
            AssessmentFaceSheetTypeReportServiceInDto infoInDto,
            List<KghKrkFaceSavKhnGetOutEntity> kghKrkFaceSavKhnGetList,
            AssessmentFaceSheetTypeReportParameterModel model,
            String systemDate,
            Integer memoPrintFlg,
            Integer serviceType) throws Exception {

        /* =============1. 基本情報設定処理============= */
        // メモ = 共通関数補足の2.4. 共通関数「設定の読込」.OUTPUT情報.パラメータ
        Integer defaultMemoFlg = CommonConstants.INT_0;
        if (memoPrintFlg == CommonConstants.NUMBER_1) {
            defaultMemoFlg = CommonConstants.INT_1;
            memoPrintFlg = CommonConstants.INT_1;
        } else if (memoPrintFlg == CommonConstants.NUMBER_0) {
            defaultMemoFlg = CommonConstants.INT_0;
            memoPrintFlg = CommonConstants.INT_0;
        } else if (memoPrintFlg == CommonConstants.NUMBER_2) {
            defaultMemoFlg = CommonConstants.INT_2;
            memoPrintFlg = CommonConstants.INT_0;
        }
        // 基本情報の記録数>0の場合
        if (!CollectionUtils.isNullOrEmpty(kghKrkFaceSavKhnGetList)) {
            KghKrkFaceSavKhnGetOutEntity kghKrkFaceSavKhnGetOutEntity = kghKrkFaceSavKhnGetList
                    .get(CommonConstants.INT_0);
            // 帳票用データ.基本情報.利用者番号 = 基本情報.利用者番号
            infoInDto.setSelfId(ReportUtil.nullToEmpty(kghKrkFaceSavKhnGetOutEntity.getSelfId()));
            // 帳票用データ.基本情報.氏名カナ = 基本情報.ﾌﾘｶﾞﾅ(姓)+" "+基本情報.ﾌﾘｶﾞﾅ(名)
            infoInDto.setNameKana(ReportUtil.nullToEmpty(kghKrkFaceSavKhnGetOutEntity.getName1Kana()) +
                    CommonConstants.SPACE_STRING + ReportUtil.nullToEmpty(kghKrkFaceSavKhnGetOutEntity.getName2Kana()));

            List<Object> adw = new ArrayList<>();
            List<Object> adwc = new ArrayList<>();
            adwc.add(infoInDto);
            // 共通関数5.1.「伏字にする」
            // (リクエストパラメータ.データ.印刷対象履歴リスト[0].出力帳票印刷情報リスト[0].職員ID,"name_kana",["name_kana"],0,NULL.基本情報,1,"name_kana",0,0,0)
            String[] nameKanaAsCol = { ReportConstants.STRING_NAME_KANA };
            nds3GkBase02Logic.getF3gkComPrintFuseji(
                    model.getPrintSubjectHistoryList().get(CommonConstants.INT_0).getChoPrtList()
                            .get(CommonConstants.INT_0).getShokuId(),
                    ReportConstants.STRING_NAME_KANA, nameKanaAsCol, CommonConstants.NUMBER_ZERO, null, adwc,
                    CommonConstants.NUMBER_ONE,
                    ReportConstants.STRING_NAME_KANA, CommonConstants.STR_0, CommonConstants.STR_0,
                    CommonConstants.STR_0);
            // 汎用変数 = 基本情報.同名識別子
            String genericVar = kghKrkFaceSavKhnGetOutEntity.getDouseiKnj();
            // 汎用変数 = null の場合
            if (!StringUtil.isNotEmpty(genericVar)) {
                genericVar = CommonConstants.BLANK_STRING;
            } else {
                // 汎用変数 = "（汎用変数）"
                genericVar = CommonConstants.LEFT_PARENTHESIS + genericVar + CommonConstants.RIGHT_PARENTHESIS;
            }
            // 帳票用データ.基本情報.氏名 = 基本情報.氏名(姓)+" "+基本情報.氏名(名)+変数.汎用変数
            infoInDto.setNameKnj(ReportUtil.nullToEmpty(kghKrkFaceSavKhnGetOutEntity.getName1Knj()) +
                    CommonConstants.SPACE_STRING + ReportUtil.nullToEmpty(kghKrkFaceSavKhnGetOutEntity.getName2Knj())
                    + genericVar);
            // 共通関数5.1.「伏字にする」
            // (リクエストパラメータ.データ.印刷対象履歴リスト[0].出力帳票印刷情報リスト[0].職員ID,"name_knj",["name_knj"],0,null.基本情報,1,"name_knj",0,0,0)
            String[] nameKnjAsCol = { infoInDto.getNameKana() };
            nds3GkBase02Logic.getF3gkComPrintFuseji(
                    model.getPrintSubjectHistoryList().get(CommonConstants.INT_0).getChoPrtList()
                            .get(CommonConstants.INT_0).getShokuId(),
                    ReportConstants.STRING_NAME_KNJ, nameKnjAsCol, CommonConstants.NUMBER_ZERO, adw, adwc,
                    CommonConstants.NUMBER_ONE,
                    ReportConstants.STRING_NAME_KNJ, CommonConstants.STR_0, CommonConstants.STR_0,
                    CommonConstants.STR_0);
            // 帳票用データ.基本情報.性別 = 基本情報.性別
            infoInDto.setSex(kghKrkFaceSavKhnGetOutEntity.getSex());
            // 帳票用データ.基本情報.生年月日 = 基本情報.生年月日
            String birthDay = kghKrkFaceSavKhnGetOutEntity.getBirthdayYmd();
            if (StringUtil.isNotEmpty(birthDay)) {
                if (birthDay.contains(CommonConstants.STR_DELIMITER)) {
                    birthDay = birthDay.replace(CommonConstants.STR_DELIMITER, CommonConstants.TEL_SUF);
                }
            }
            List<String> dateParts = ReportUtil
                    .getLocalDateToJapanDateTimeFormat(birthDay);
            if (CollectionUtils.isNotEmpty(dateParts)) {
                infoInDto.setBirthdayYmdGG(ReportUtil.nullToEmpty(dateParts.get(CommonConstants.INT_0)));
                infoInDto.setBirthdayYmdYY(ReportUtil.nullToEmpty(dateParts.get(CommonConstants.INT_1)));
                infoInDto.setBirthdayYmdMM(ReportUtil.nullToEmpty(dateParts.get(CommonConstants.INT_2)));
                infoInDto.setBirthdayYmdDD(ReportUtil.nullToEmpty(dateParts.get(CommonConstants.INT_3)));
            } else {
                infoInDto.setBirthdayYmdGG(CommonConstants.BLANK_STRING);
                infoInDto.setBirthdayYmdYY(CommonConstants.BLANK_STRING);
                infoInDto.setBirthdayYmdMM(CommonConstants.BLANK_STRING);
                infoInDto.setBirthdayYmdDD(CommonConstants.BLANK_STRING);
            }

            // リクエストパラメータ.データ.印刷設定.氏名等を伏字にするフラグ = 1 の場合
            if (CommonDtoUtil.checkStringEqual(model.getPrintSet().getAmikakeFlg(), CommonConstants.STR_1)) {
                // 共通関数5.1.「伏字にする」
                // (リクエストパラメータ.データ.印刷対象履歴リスト[0].出力帳票印刷情報リスト[0].職員ID,"birthday",["birth_ymd"],0,null.基本情報,1,"birthday",0,0,0)
                String[] BirthdayAsCol = { "birth_ymd" };
                nds3GkBase02Logic.getF3gkComPrintFuseji(
                        model.getPrintSubjectHistoryList().get(CommonConstants.INT_0).getChoPrtList()
                                .get(CommonConstants.INT_0).getShokuId(),
                        ReportConstants.STRING_BIRTHDAY, BirthdayAsCol, CommonConstants.NUMBER_ZERO, adw, adwc,
                        CommonConstants.NUMBER_ONE,
                        ReportConstants.STRING_BIRTHDAY, CommonConstants.STR_0, CommonConstants.STR_0,
                        CommonConstants.STR_0);
            }
            // 帳票用データ.基本情報.年齢 = 共通関数補足の6.1. 共通関数「年月の差を返す（年齢）」(基本情報.生年月日,変数.システム日付)
            infoInDto.setAge(
                    getAgeStr(kghKrkFaceSavKhnGetOutEntity.getBirthdayYmd(), systemDate));
            // 帳票用データ.基本情報.血液型
            switch (kghKrkFaceSavKhnGetOutEntity.getAbo()) {
                case CommonConstants.INT_0:
                    infoInDto.setAboKnj(CommonConstants.STR_A);
                    break;
                case CommonConstants.INT_1:
                    infoInDto.setAboKnj(CommonConstants.STR_B);
                    break;
                case CommonConstants.INT_2:
                    infoInDto.setAboKnj(CommonConstants.SUB_O);
                    break;
                case CommonConstants.INT_3:
                    infoInDto.setAboKnj(CommonConstants.SV_KIND_CD_AB);
                    break;
                case CommonConstants.INT_4:
                    infoInDto.setAboKnj(CommonConstants.BLANK_STRING);
                    break;
                default:
                    break;
            }
            // 帳票用データ.基本情報.RH
            switch (kghKrkFaceSavKhnGetOutEntity.getRh()) {
                case CommonConstants.INT_0:
                    infoInDto.setRhKnj(ReportConstants.RHKNJ_1);
                    break;
                case CommonConstants.INT_1:
                    infoInDto.setRhKnj(ReportConstants.RHKNJ_2);
                    break;
                case CommonConstants.INT_2:
                    infoInDto.setRhKnj(CommonConstants.BLANK_STRING);
                    break;
                default:
                    break;
            }
            // 汎用変数 = 基本情報.郵便番号（'###-####'）形式にフォーマットする
            genericVar = ReportUtil.nullToEmpty(kghKrkFaceSavKhnGetOutEntity.getZip());
            // 汎用変数 <> "" の場合、汎用変数 = " 〒" + 変数.汎用変数 + " "
            if (StringUtil.isNotEmpty(genericVar)) {
                StringBuffer zip = new StringBuffer();
                zip.append(genericVar);
                zip.insert(CommonConstants.INT_3, CommonConstants.TEL_SUF);
                zip.insert(CommonConstants.INT_0, CommonConstants.TEL_BOL);
                zip.append(CommonConstants.SPACE_STRING);
                genericVar = zip.toString();
            }
            // 帳票用データ.基本情報.住所 = 変数.汎用変数+基本情報.住所
            infoInDto.setAddressKnj(genericVar + ReportUtil.nullToEmpty(kghKrkFaceSavKhnGetOutEntity.getAddressKnj()));
            // リクエストパラメータ.データ.印刷設定.氏名等を伏字にするフラグ = 1 の場合
            if (CommonDtoUtil.checkStringEqual(model.getPrintSet().getAmikakeFlg(), CommonConstants.STR_1)) {
                // 共通関数5.1.「伏字にする」
                // (リクエストパラメータ.データ.印刷対象履歴リスト[0].出力帳票印刷情報リスト[0].職員ID,"address",["address_knj"],0,NULL.基本情報,1,"address",0,0,0)
                String[] addressKnjAsCol = { "address_knj" };
                nds3GkBase02Logic.getF3gkComPrintFuseji(
                        model.getPrintSubjectHistoryList().get(CommonConstants.INT_0).getChoPrtList()
                                .get(CommonConstants.INT_0).getShokuId(),
                        ReportConstants.STRING_ADDRESS, addressKnjAsCol, CommonConstants.NUMBER_ZERO, adw, adwc,
                        CommonConstants.NUMBER_ONE,
                        ReportConstants.STRING_ADDRESS, CommonConstants.STR_0, CommonConstants.STR_0,
                        CommonConstants.STR_0);
            }

            // 住所フォント
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                if (StringUtil.isNotEmpty(infoInDto.getAddressKnj())) {
                    int addressKnjLength = ReportUtil.getByteLength(infoInDto.getAddressKnj());
                    if (addressKnjLength > 139) {
                        infoInDto.setAddressKnjFont("10.5");
                    } else {
                        infoInDto.setAddressKnjFont(ReportConstants.FONT_SIZE_12);
                    }
                }
            } else if (serviceType == CommonConstants.INT_2) {
                // アセスメントフェースシート(TYPE2）の場合
                infoInDto.setAddressKnjFont(ReportConstants.FONT_SIZE_9);
            }
            // 基本情報.電話番号 = "℡:"+基本情報.電話番号
            infoInDto.setTel("℡:" + kghKrkFaceSavKhnGetOutEntity.getTel());
            // リクエストパラメータ.データ.印刷設定.氏名等を伏字にするフラグ = 1 の場合
            if (CommonDtoUtil.checkStringEqual(model.getPrintSet().getAmikakeFlg(), CommonConstants.STR_1)) {
                // 共通関数5.1.「伏字にする」
                // (リクエストパラメータ.データ.印刷対象履歴リスト[0].出力帳票印刷情報リスト[0].職員ID,"tel",["tel"],0,NULL.基本情報,1,"tel",0,0,0)
                String[] telAsCol = { ReportConstants.STRING_TEL };
                nds3GkBase02Logic.getF3gkComPrintFuseji(
                        model.getPrintSubjectHistoryList().get(CommonConstants.INT_0).getChoPrtList()
                                .get(CommonConstants.INT_0).getShokuId(),
                        ReportConstants.STRING_TEL, telAsCol, CommonConstants.NUMBER_ZERO, adw, adwc,
                        CommonConstants.NUMBER_ONE,
                        ReportConstants.STRING_TEL, CommonConstants.STR_0, CommonConstants.STR_0,
                        CommonConstants.STR_0);
            }
            infoInDto.setTel("℡:" + kghKrkFaceSavKhnGetOutEntity.getTel());

            // 基本情報.本籍地住所
            genericVar = ReportUtil.nullToEmpty(kghKrkFaceSavKhnGetOutEntity.getHonZip());
            if (StringUtil.isNotEmpty(genericVar)) {
                StringBuffer honzip = new StringBuffer();
                honzip.append(genericVar);
                honzip.insert(CommonConstants.INT_3, CommonConstants.TEL_SUF);
                honzip.insert(CommonConstants.INT_0, CommonConstants.TEL_BOL);
                honzip.append(CommonConstants.SPACE_STRING);
                genericVar = honzip.toString();
            }
            infoInDto.setHonseki(genericVar + ReportUtil.nullToEmpty(kghKrkFaceSavKhnGetOutEntity.getHonAddressKnj()));
            // リクエストパラメータ.データ.印刷設定.氏名等を伏字にするフラグ = 1 の場合
            if (CommonDtoUtil.checkStringEqual(model.getPrintSet().getAmikakeFlg(), CommonConstants.STR_1)) {
                String[] addressKnjAsCol = { "honseki" };
                nds3GkBase02Logic.getF3gkComPrintFuseji(
                        model.getPrintSubjectHistoryList().get(CommonConstants.INT_0).getChoPrtList()
                                .get(CommonConstants.INT_0).getShokuId(),
                        ReportConstants.STRING_ADDRESS, addressKnjAsCol, CommonConstants.NUMBER_ZERO, adw, adwc,
                        CommonConstants.NUMBER_ONE,
                        ReportConstants.STRING_ADDRESS, CommonConstants.STR_0, CommonConstants.STR_0,
                        CommonConstants.STR_0);
            }

            // 基本情報.顔写真パス
            infoInDto.setBmpPath(ReportUtil.nullToEmpty(kghKrkFaceSavKhnGetOutEntity.getBmpPath()));
            // 基本情報.撮影日
            List<String> bmpYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavKhnGetOutEntity.getBmpYmd());
            if (CollectionUtils.isNotEmpty(bmpYmd)) {
                infoInDto.setBmpYmdGG(ReportUtil.nullToEmpty(bmpYmd.get(CommonConstants.INT_0)));
                infoInDto.setBmpYmdYY(ReportUtil.nullToEmpty(bmpYmd.get(CommonConstants.INT_1)));
                infoInDto.setBmpYmdMM(ReportUtil.nullToEmpty(bmpYmd.get(CommonConstants.INT_2)));
                infoInDto.setBmpYmdDD(ReportUtil.nullToEmpty(bmpYmd.get(CommonConstants.INT_3)));
            } else {
                infoInDto.setBmpYmdGG(CommonConstants.BLANK_STRING);
                infoInDto.setBmpYmdYY(CommonConstants.BLANK_STRING);
                infoInDto.setBmpYmdMM(CommonConstants.BLANK_STRING);
                infoInDto.setBmpYmdDD(CommonConstants.BLANK_STRING);
            }
            // 基本情報.メモ
            infoInDto.setMemoKnj(ReportUtil.nullToEmpty(kghKrkFaceSavKhnGetOutEntity.getMemoKnj()));

            // 基本情報.メモ区分
            infoInDto.setMemoKbn(CommonConstants.INT_0);
            if (CommonConstants.NUMBER_0.equals(defaultMemoFlg) || CommonConstants.NUMBER_1.equals(defaultMemoFlg)) {
                infoInDto.setMemoKbn(memoPrintFlg);
            } else if (CommonConstants.NUMBER_2.equals(defaultMemoFlg)) {
                // 基本情報.メモ = "" or 基本情報.メモ = null の場合、 帳票用データ.基本情報.メモ区分 = 0
                if (CommonConstants.EMPTY_STRING.equals(kghKrkFaceSavKhnGetOutEntity.getMemoKnj())
                        || kghKrkFaceSavKhnGetOutEntity.getMemoKnj().isEmpty()) {
                    infoInDto.setMemoKbn(CommonConstants.INT_0);
                } else {
                    // 上記以外の場合、 帳票用データ.基本情報.メモ区分 = 1
                    infoInDto.setMemoKbn(CommonConstants.INT_1);
                }
            }
        } else {
            // 上記以外の場合、帳票用データ.基本情報の属性フィールドをすべて''またはnullに設定する
            // 利用者番号
            infoInDto.setSelfId(CommonConstants.BLANK_STRING);
            // 氏名カナ
            infoInDto.setNameKana(CommonConstants.BLANK_STRING);
            // 氏名
            infoInDto.setNameKnj(CommonConstants.BLANK_STRING);
            // 性別
            infoInDto.setSex(CommonConstants.INT_MINUS_1);
            // 血液型名
            infoInDto.setAboKnj(CommonConstants.BLANK_STRING);
            // RH名
            infoInDto.setRhKnj(CommonConstants.BLANK_STRING);
            // 生年月日（年号）
            infoInDto.setBirthdayYmdGG(CommonConstants.BLANK_STRING);
            // 生年月日（年）
            infoInDto.setBirthdayYmdYY(CommonConstants.BLANK_STRING);
            // 生年月日（月）
            infoInDto.setBirthdayYmdMM(CommonConstants.BLANK_STRING);
            // 生年月日（日）
            infoInDto.setBirthdayYmdDD(CommonConstants.BLANK_STRING);
            // 年齢
            infoInDto.setAge(CommonConstants.STR_0);
            // 現住所
            infoInDto.setAddressKnj(CommonConstants.BLANK_STRING);
            // 電話番号
            infoInDto.setTel(CommonConstants.BLANK_STRING);
            // 本籍地住所
            infoInDto.setHonseki(CommonConstants.BLANK_STRING);
            // 顔写真パス
            infoInDto.setBmpPath(CommonConstants.BLANK_STRING);
            // 撮影日（年号）
            infoInDto.setBmpYmdGG(CommonConstants.BLANK_STRING);
            // 撮影日（年）
            infoInDto.setBmpYmdYY(CommonConstants.BLANK_STRING);
            // 撮影日（月）
            infoInDto.setBmpYmdMM(CommonConstants.BLANK_STRING);
            // 撮影日（日）
            infoInDto.setBmpYmdDD(CommonConstants.BLANK_STRING);
            // メモ
            infoInDto.setMemoKnj(CommonConstants.BLANK_STRING);
            // メモ区分
            infoInDto.setMemoKbn(CommonConstants.INT_0);

        }
    }

    /**
     * 別紙親族関係設定
     * 
     * @param history     リクエストパラメータ.データ.印刷対象履歴[0]
     * @param systemDate  システム日付
     * @param paramFor23  続柄の分類が「その他（親族外）」の人を出力しない
     * @param telFlg      電話フラグ
     * @param printSet    印刷設定
     * @param serviceType 帳票区分
     * @return 帳票用データ.親族関係
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getFamilyRelationshipInfoParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            String systemDate,
            String paramFor23,
            Integer telFlg,
            ReportAssessmentFaceSheetTypePrintSet printSet,
            Integer serviceType) throws Exception {

        List<ReportAssessmentFaceSheetTypeFamilyRelationshipInfo> familyRelationshipInfoList = new ArrayList<>();
        ReportAssessmentFaceSheetTypeFamilyRelationshipInfo familyRelationshipInfo = new ReportAssessmentFaceSheetTypeFamilyRelationshipInfo();

        ReportAssessmentFaceSheetTypeChoPrt choPrt = history.getChoPrtList().get(CommonConstants.INT_0);

        /* =============1. 情報を取得する============= */
        // 1.1.下記DAOを利用し、親族関係情報を取得する
        // アセスメントフェースシート履歴親族関係情報取得
        KghKrkFaceSavKazGetByCriteriaInEntity kghKrkFaceSavKazGetByCriteriaInEntity = new KghKrkFaceSavKazGetByCriteriaInEntity();
        // リクエストパラメータ.データ.印刷対象履歴リスト[0].保存連番
        kghKrkFaceSavKazGetByCriteriaInEntity.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavKazGetOutEntity> kghKrkFaceSavKazGetOutEntities = kghTucAssFaceKazSelectMapper
                .findKghKrkFaceSavKazGetByCriteria(kghKrkFaceSavKazGetByCriteriaInEntity);

        // 1.1.1 関係区分4 = 1 でデータをフィルタリングする
        List<KghKrkFaceSavKazGetOutEntity> kghKrkFaceSavKaz4KbnEntities = new ArrayList<>();
        if (!CollectionUtils.isNullOrEmpty(kghKrkFaceSavKazGetOutEntities)) {
            kghKrkFaceSavKaz4KbnEntities = kghKrkFaceSavKazGetOutEntities.stream()
                    .filter(item -> item.getKankei4Kbn() == CommonConstants.INT_1).toList();
        }

        // 1.2.下記DAOを利用し、家族図情報を取得する 課題#123175 ※中期の開発対象外

        /* =============2. 親族関係設定============= */
        // 2.1.下記DAOを利用し、アセスメントフェースシート履歴その他情報を取得する
        KghKrkFaceSavAnyGetByCriteriaInEntity kghKrkFaceSavAnyGetByCriteriaInEntity = new KghKrkFaceSavAnyGetByCriteriaInEntity();
        // リクエストパラメータ.データ.印刷対象履歴リスト[インデックス].保存連番
        kghKrkFaceSavAnyGetByCriteriaInEntity.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        kghKrkFaceSavAnyGetByCriteriaInEntity.setKey1(CommonConstants.NUMBER_3);
        List<KghKrkFaceSavAnyGetOutEntity> kghKrkFaceSavAnyGetOutEntities = kghTucAssFaceAnySelectMapper
                .findKghKrkFaceSavAnyGetByCriteria(kghKrkFaceSavAnyGetByCriteriaInEntity);

        // 1から10までループする
        List<String> relationshipList = new ArrayList<>();
        for (int i = CommonConstants.INT_0; i < CommonConstants.INT_10; i++) {
            String sDat = CommonConstants.BLANK_STRING;
            if (i < kghKrkFaceSavAnyGetOutEntities.size()
                    && !kghKrkFaceSavAnyGetOutEntities.get(i).getSdat().isEmpty()) {
                sDat = kghKrkFaceSavAnyGetOutEntities.get(i).getSdat();
                sDat = CommonConstants.LEFT_PARENTHESIS.concat(sDat)
                        .concat(CommonConstants.RIGHT_PARENTHESIS);
            }
            relationshipList.add(sDat);
        }

        // システム日付
        if (!StringUtil.isNotEmpty(systemDate)) {
            DateTimeFormatter formattere = DateTimeFormatter.ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_SLASH);
            systemDate = LocalDate.now().format(formattere);
        }

        // 上記「1.1.1」履歴親族関係リストの記録数>0の場合
        if (!CollectionUtils.isNullOrEmpty(kghKrkFaceSavKaz4KbnEntities)) {
            KghKrkFaceSavKazGetOutEntity kghKrkFaceSavKazGetOutEntity = kghKrkFaceSavKaz4KbnEntities
                    .get(CommonConstants.INT_0);
            // 汎用変数
            String genericVar = CommonConstants.LEFT_PARENTHESIS
                    + getAgeStr(kghKrkFaceSavKazGetOutEntity.getBirthdayYmd(), systemDate)
                    + CommonConstants.RIGHT_PARENTHESIS;
            // 帳票用データ.家族情報.保証人氏名 = 履歴親族関係リスト[0].氏名(姓)+" "+ 履歴親族関係リスト[0].氏名(名) + 汎用変数
            familyRelationshipInfo.setHogoName1Knj(ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getName1Knj()) +
                    CommonConstants.BLANK_SPACE + ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getName2Knj())
                    + genericVar);
            // 帳票用データ.家族情報.保証人電話番号 = 履歴親族関係リスト[0].(自宅)電話番号
            familyRelationshipInfo.setHogoTel(ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getTel()));
            // 帳票用データ.家族情報.保証人続柄 ="(続柄) " + 履歴親族関係リスト[0].続柄名
            familyRelationshipInfo.setHogoZokugaraKnj(
                    "(続柄) " + ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getZokugaraKnj()));
            // 履歴親族関係リスト[0].郵便番号（"〒###-#### "）形式
            genericVar = ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getZip());
            if (StringUtil.isNotEmpty(genericVar)) {
                StringBuffer zip = new StringBuffer();
                zip.append(genericVar);
                zip.insert(CommonConstants.INT_3, CommonConstants.TEL_SUF);
                zip.insert(CommonConstants.INT_0, CommonConstants.TEL_BOL);
                zip.append(CommonConstants.SPACE_STRING);
                genericVar = zip.toString();
            }
            // 帳票用データ.家族情報.保証人住所 = 汎用変数 + 履歴親族関係リスト[0].住所
            familyRelationshipInfo.setHogoAddressKnj(
                    genericVar + ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getAddressKnj()));
            // 帳票用データ.家族情報.保証人住所フォント
            Integer hogoAddressKnjLength = ReportUtil.getByteLength(familyRelationshipInfo.getHogoAddressKnj());
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                // 帳票用データ.家族情報.保証人住所の桁数<=76の場合
                if (hogoAddressKnjLength <= 76) {
                    // 帳票用データ.家族情報.保証人住所フォント = 12
                    familyRelationshipInfo.setHogoAddressKnjFont(ReportConstants.FONT_SIZE_12);
                    // 76<帳票用データ.家族情報.保証人住所の桁数<=95の場合
                } else if (76 < hogoAddressKnjLength && hogoAddressKnjLength <= 95) {
                    // 帳票用データ.家族情報.保証人住所フォント = 10
                    familyRelationshipInfo.setHogoAddressKnjFont(ReportConstants.FONT_SIZE_10);
                } else {
                    // 帳票用データ.家族情報.保証人住所フォント = 8
                    familyRelationshipInfo.setHogoAddressKnjFont(ReportConstants.FONT_SIZE_8);
                }
            } else if (serviceType == CommonConstants.INT_2) {
                // アセスメントフェースシート(TYPE2）の場合
                // 帳票用データ.家族情報.保証人住所の桁数>162の場合
                if (hogoAddressKnjLength > 162) {
                    // 帳票用データ.家族情報.保証人住所フォント = 8
                    familyRelationshipInfo.setHogoAddressKnjFont(ReportConstants.FONT_SIZE_8);
                } else {
                    // 帳票用データ.家族情報.保証人住所フォント = 9
                    familyRelationshipInfo.setHogoAddressKnjFont(ReportConstants.FONT_SIZE_9);
                }
            }
            // 帳票用データ.家族情報.保証人携帯電話番号 = 履歴親族関係リスト[0].携帯番号
            familyRelationshipInfo.setHogoTel3(ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getKeitaitel()));
            // 帳票用データ.家族情報.保証人工作電話番号区分 = 履歴親族関係リスト[0].種別名
            familyRelationshipInfo
                    .setHogoWorkTel1Kbn(ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getTelKindKnj()));
            // 帳票用データ.家族情報.保証人工作電話番号 = 履歴親族関係リスト[0].その他TEL1
            familyRelationshipInfo.setHogoWorkTel1(ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getOther1Tel()));
            // 履歴親族関係リスト[0].その他郵便番号1（"〒###-#### "）形式
            genericVar = ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getOther1Zip());
            if (StringUtil.isNotEmpty(genericVar)) {
                StringBuffer zip = new StringBuffer();
                zip.append(genericVar);
                zip.insert(CommonConstants.INT_3, CommonConstants.TEL_SUF);
                zip.insert(CommonConstants.INT_0, CommonConstants.TEL_BOL);
                zip.append(CommonConstants.SPACE_STRING);
                genericVar = zip.toString();
            }
            // 帳票用データ.家族情報.保証人連絡先住所 = 汎用変数 + 履歴親族関係リスト[0].その他住所1
            familyRelationshipInfo.setOtherAddresKnj(
                    genericVar + ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getOther1AddressKnj()));

        } else {
            // 帳票用データ.基本情報の属性フィールドをすべて''またはnullに設定する
            // 保証人氏名
            familyRelationshipInfo.setHogoName1Knj(CommonConstants.BLANK_STRING);
            // 保証人電話番号
            familyRelationshipInfo.setHogoTel(CommonConstants.BLANK_STRING);
            // 保証人続柄
            familyRelationshipInfo.setHogoZokugaraKnj(CommonConstants.BLANK_STRING);
            // 保証人住所
            familyRelationshipInfo.setHogoAddressKnj(CommonConstants.BLANK_STRING);
            // 保証人住所フォント
            familyRelationshipInfo.setHogoAddressKnjFont(CommonConstants.BLANK_STRING);
            // 保証人携帯電話番号
            familyRelationshipInfo.setHogoTel3(CommonConstants.BLANK_STRING);
            // 保証人工作電話番号区分
            familyRelationshipInfo.setHogoWorkTel1Kbn(CommonConstants.BLANK_STRING);
            // 保証人工作電話番号
            familyRelationshipInfo.setHogoWorkTel1(CommonConstants.BLANK_STRING);
            // 保証人連絡先住所
            familyRelationshipInfo.setOtherAddresKnj(CommonConstants.BLANK_STRING);
        }

        // 1から6までのループ
        for (int i = CommonConstants.INT_0; i < CommonConstants.INT_6; i++) {
            // 帳票用データ.家族情報.保証人電話番号+インデックス = ""
            // familyRelationshipInfo.setHogoTel(CommonConstants.BLANK_STRING);
            // 帳票用データ.家族情報.保証人電話番号+インデックス = ""
            // familyRelationshipInfo.setHogoTel3(CommonConstants.BLANK_STRING);
        }

        // 変数.統計数 = 0
        int totalCnt = CommonConstants.INT_0;
        List<U06050FamilyRelationship> familyRelationships = new ArrayList<>();
        U06050FamilyRelationship familyRelationship = new U06050FamilyRelationship();
        if (!CollectionUtils.isNullOrEmpty(kghKrkFaceSavKaz4KbnEntities)) {
            for (int i = CommonConstants.INT_0; i < kghKrkFaceSavKaz4KbnEntities.size(); i++) {
                // 統計数++
                totalCnt++;
                // 続柄の分類が「その他（親族外）」の人を出力しない <> 1 の場合
                if (!CommonDtoUtil.checkStringEqual(paramFor23, CommonConstants.STR_1)) {
                    KghKrkFaceSavKazGetOutEntity kghKrkFaceSavKazGetOutEntity = kghKrkFaceSavKaz4KbnEntities.get(i);
                    familyRelationship = new U06050FamilyRelationship();

                    // 帳票用データ.家族構成リスト[インデックス].氏名 = 履歴親族関係リスト[インデックス].氏名(姓)+"
                    // "+履歴親族関係リスト[インデックス].氏名(名)
                    familyRelationship.setNameKnj(ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getName1Knj()) +
                            CommonConstants.BLANK_SPACE
                            + ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getName2Knj()));

                    // リクエストパラメータ.データ.印刷設定.氏名等を伏字にするフラグ = 1 の場合
                    if (CommonDtoUtil.checkStringEqual(printSet.getAmikakeFlg(), CommonConstants.STR_1)) {
                        String[] nameKnjs = { ReportConstants.STRING_NAME_KNJ };
                        List<Object> adwc = new ArrayList<>();
                        adwc.add(familyRelationship);
                        nds3GkBase02Logic.getF3gkComPrintFuseji(choPrt.getShokuId(),
                                ReportConstants.STRING_NAME_KNJ, nameKnjs, CommonConstants.NUMBER_0, null, adwc,
                                CommonConstants.NUMBER_1,
                                ReportConstants.STRING_NAME_KNJ, CommonConstants.STR_0, CommonConstants.STR_0,
                                CommonConstants.STR_0);
                    }

                    // 帳票用データ.家族構成リスト[インデックス].続柄 = 履歴親族関係リスト[インデックス].続柄名
                    familyRelationship
                            .setZokugara(ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getZokugaraKnj()));
                    // 汎用変数
                    String genericVar = getAgeStr(kghKrkFaceSavKazGetOutEntity.getBirthdayYmd(), systemDate);
                    // 帳票用データ.家族構成リスト[インデックス].年齢 = 履歴親族関係リスト[インデックス].生年月日
                    familyRelationship.setBirthday(
                            ReportUtil.nullToEmpty(genericVar));
                    // 帳票用データ.家族構成リスト[インデックス].性別 = 履歴親族関係リスト[インデックス].性別
                    familyRelationship.setSex(
                            ReportUtil
                                    .nullToEmpty(CommonDtoUtil.objValToString(kghKrkFaceSavKazGetOutEntity.getSex())));
                    if (kghKrkFaceSavKazGetOutEntity.getSex() == CommonConstants.NUMBER_1.shortValue()) {
                        familyRelationship.setSexKnj(CommonConstants.STR_MALE);
                    } else if (kghKrkFaceSavKazGetOutEntity.getSex() == CommonConstants.NUMBER_2.shortValue()) {
                        familyRelationship.setSexKnj(CommonConstants.STR_FEMALE);
                    }

                    // 帳票用データ.家族構成リスト[インデックス].職業名 = 履歴親族関係リスト[インデックス].職業
                    familyRelationship.setJob(ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getJobNameKnj()));
                    // 帳票用データ.家族構成リスト[インデックス].備考 = 履歴親族関係リスト[インデックス].備考
                    familyRelationship.setMemo(ReportUtil.nullToEmpty(kghKrkFaceSavKazGetOutEntity.getNoteKnj()));

                    Integer memoLength = ReportUtil.getByteLength(familyRelationship.getMemo());
                    if (serviceType == CommonConstants.INT_1) {
                        // アセスメントフェースシート(TYPE1）の場合
                        // 電話フラグ = 1 の場合
                        if (telFlg == CommonConstants.NUMBER_1) {
                            // 帳票用データ.家族情報.家族構成リスト[ループ.インデックス].備考の桁数＞24の場合
                            if (memoLength > CommonConstants.INT_24) {
                                familyRelationship.setMemoFont(ReportConstants.FONT_SIZE_8);
                            } else {
                                familyRelationship.setMemoFont(ReportConstants.FONT_SIZE_9);
                            }
                        } else {
                            // 帳票用データ.家族情報.家族構成リスト[ループ.インデックス].備考の桁数＞30の場合
                            if (memoLength > CommonConstants.INT_30) {
                                familyRelationship.setMemoFont(ReportConstants.FONT_SIZE_8);
                                // 30>=帳票用データ.家族情報.家族構成リスト[ループ.インデックス].備考の桁数＞24の場合
                            } else if (CommonConstants.INT_24 < memoLength && memoLength <= CommonConstants.INT_30) {
                                familyRelationship.setMemoFont(ReportConstants.FONT_SIZE_10);
                            } else {
                                familyRelationship.setMemoFont(ReportConstants.FONT_SIZE_12);
                            }
                        }
                    } else if (serviceType == CommonConstants.INT_2) {
                        // アセスメントフェースシート(TYPE2）の場合
                        // 帳票用データ.家族情報.家族構成リスト[ループ.インデックス].備考の桁数<=36の場合
                        if (memoLength <= CommonConstants.INT_36) {
                            familyRelationship.setMemoFont(ReportConstants.FONT_SIZE_9);
                            // 36<帳票用データ.家族情報.家族構成リスト[ループ.インデックス].備考の桁数<=40の場合
                        } else if (CommonConstants.INT_36 < memoLength && memoLength <= CommonConstants.INT_40) {
                            familyRelationship.setMemoFont(ReportConstants.FONT_SIZE_8);
                        } else {
                            familyRelationship.setMemoFont(ReportConstants.FONT_SIZE_6);
                        }
                    }

                    // 変数.汎用変数 = 上記「1.1.1」履歴親族関係リスト[ループ.インデックス].(自宅)電話番号
                    genericVar = kghKrkFaceSavKazGetOutEntity.getTel();
                    if (StringUtil.isNotEmpty(genericVar)) {
                        genericVar = "℡ " + genericVar;
                    } else {
                        genericVar = CommonConstants.BLANK_STRING;
                    }
                    // 帳票用データ.家族情報.家族構成リスト[ループ.インデックス].保証人電話番号+インデックス = 変数.汎用変数

                    // 変数.汎用変数 = 上記「1.1.1」履歴親族関係リスト[ループ.インデックス].携帯番号
                    genericVar = kghKrkFaceSavKazGetOutEntity.getKeitaitel();
                    if (StringUtil.isNotEmpty(genericVar)) {
                        genericVar = "携 " + genericVar;
                    } else {
                        genericVar = CommonConstants.BLANK_STRING;
                    }
                    // 帳票用データ.家族情報.家族構成リスト[ループ.インデックス].保証人携帯電話番号+インデックス = 変数.汎用変数

                    // 1から10までをループする
                    for (int j = CommonConstants.INT_0; j < CommonConstants.INT_10; j++) {
                        Method getKankeiKbn = kghKrkFaceSavKazGetOutEntity.getClass()
                                .getMethod("getKankei" + (i + CommonConstants.INT_1) + "Kbn");
                        Integer kankeiKbn = (Integer) getKankeiKbn.invoke(kghKrkFaceSavKazGetOutEntity);
                        if (kankeiKbn == CommonConstants.INT_1) {
                            genericVar += relationshipList.get(j);
                        }
                    }
                    familyRelationships.add(familyRelationship);
                    // 統計数 >=6の場合、ループを抜ける
                    if (totalCnt >= CommonConstants.INT_6) {
                        break;
                    }
                }
            }
        }

        // 統計数<6の場合
        if (totalCnt < CommonConstants.INT_6) {
            for (int i = totalCnt; i < CommonConstants.INT_6; i++) {
                U06050FamilyRelationship u06050FamilyRelationship = new U06050FamilyRelationship();
                u06050FamilyRelationship.setNameKnj(CommonConstants.BLANK_STRING);
                u06050FamilyRelationship.setZokugara(CommonConstants.BLANK_STRING);
                u06050FamilyRelationship.setSex(null);
                u06050FamilyRelationship.setSexKnj(CommonConstants.BLANK_STRING);
                u06050FamilyRelationship.setBirthday(CommonConstants.BLANK_STRING);
                u06050FamilyRelationship.setJob(CommonConstants.BLANK_STRING);
                u06050FamilyRelationship.setMemo(CommonConstants.BLANK_STRING);
                familyRelationships.add(u06050FamilyRelationship);
            }
        }

        // 帳票用データ.家族構成リスト
        JRBeanCollectionDataSource familyRelationshipsDataSource = new JRBeanCollectionDataSource(
                familyRelationships);
        familyRelationshipInfo.setFamilyRelationshipList(familyRelationshipsDataSource);

        // リクエストパラメータ.データ.印刷設定.氏名等を伏字にするフラグ = 1 の場合
        if (CommonDtoUtil.checkStringEqual(printSet.getAmikakeFlg(), CommonConstants.STR_1)) {
            String[] nameKnjs = { "hogo_name1_knj" };
            List<Object> adw = new ArrayList<>();
            List<Object> adwc = new ArrayList<>();
            adwc.add(familyRelationshipInfo);
            // 保証人氏名
            nds3GkBase02Logic.getF3gkComPrintFuseji(choPrt.getShokuId(),
                    ReportConstants.STRING_NAME_KNJ,
                    nameKnjs,
                    CommonConstants.NUMBER_0,
                    adw, adwc, CommonConstants.NUMBER_1,
                    ReportConstants.STRING_NAME_KNJ, CommonConstants.STR_0,
                    CommonConstants.STR_0, CommonConstants.STR_0);

            // 保証人電話番号
            String[] tel = { "hogo_tel" };
            nds3GkBase02Logic.getF3gkComPrintFuseji(choPrt.getShokuId(),
                    ReportConstants.STRING_TEL,
                    tel,
                    CommonConstants.NUMBER_0,
                    adw, adwc, CommonConstants.NUMBER_1,
                    ReportConstants.STRING_TEL, CommonConstants.STR_0,
                    CommonConstants.STR_0, CommonConstants.STR_0);
            // 保証人住所
            String[] address = { "hogo_address_knj" };
            nds3GkBase02Logic.getF3gkComPrintFuseji(choPrt.getShokuId(),
                    ReportConstants.STRING_ADDRESS,
                    address,
                    CommonConstants.NUMBER_0,
                    adw, adwc, CommonConstants.NUMBER_1,
                    ReportConstants.STRING_ADDRESS, CommonConstants.STR_0,
                    CommonConstants.STR_0, CommonConstants.STR_0);
            // 保証人携帯電話番号
            String[] keitai = { "hogo_tel3" };
            nds3GkBase02Logic.getF3gkComPrintFuseji(choPrt.getShokuId(),
                    "keitai",
                    keitai,
                    CommonConstants.NUMBER_0,
                    adw, adwc, CommonConstants.NUMBER_1,
                    "keitai", CommonConstants.STR_0,
                    CommonConstants.STR_0, CommonConstants.STR_0);
            // 保証人工作電話番号
            String[] tel1 = { "hogo_work_tel1" };
            nds3GkBase02Logic.getF3gkComPrintFuseji(choPrt.getShokuId(),
                    ReportConstants.STRING_TEL,
                    tel1,
                    CommonConstants.NUMBER_0,
                    adw, adwc, CommonConstants.NUMBER_1,
                    ReportConstants.STRING_TEL, CommonConstants.STR_0,
                    CommonConstants.STR_0, CommonConstants.STR_0);
            // 保証人連絡先住所
            String[] otherAddress = { "other_address_knj" };
            nds3GkBase02Logic.getF3gkComPrintFuseji(choPrt.getShokuId(),
                    ReportConstants.STRING_ADDRESS,
                    otherAddress,
                    CommonConstants.NUMBER_0,
                    adw, adwc, CommonConstants.NUMBER_1,
                    ReportConstants.STRING_ADDRESS, CommonConstants.STR_0,
                    CommonConstants.STR_0, CommonConstants.STR_0);
        }
        familyRelationshipInfoList.add(familyRelationshipInfo);
        return new JRBeanCollectionDataSource(familyRelationshipInfoList);
    }

    /**
     * アセスメントフェースシート履歴その負担限度の印刷データを編集
     * 
     * @param history     リクエストパラメータ.データ.印刷対象履歴リスト[0]
     * @param serviceType 帳票区分
     * @return 負担限度情報
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getBurdenLimitInfoParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            Integer serviceType) throws Exception {

        // 負担限度情報
        List<ReportAssessmentFaceSheetTypeBurdenLimitInfo> burdenLimitInfos = new ArrayList<>();
        ReportAssessmentFaceSheetTypeBurdenLimitInfo burdenLimitInfo = new ReportAssessmentFaceSheetTypeBurdenLimitInfo();

        // 1.負担限度情報を取得する
        KghKrkFaceSavFtnGetByCriteriaInEntity param = new KghKrkFaceSavFtnGetByCriteriaInEntity();
        param.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavFtnGetOutEntity> ftnList = kghTucAssFaceFtnSelectMapper
                .findKghKrkFaceSavFtnGetByCriteria(param);

        // 2. 負担限度情報設定
        if (CollectionUtils.isNotEmpty(ftnList)) {
            // 上記負担限度リストの記録数>0の場合

            // 交付年月日
            List<String> koufuYmdList = nds3GkFunc01Logic
                    .getFs2gKantanRtn(ftnList.get(CommonConstants.INT_0).getKoufuYmd());
            if (CollectionUtils.isNotEmpty(koufuYmdList)) {
                burdenLimitInfo.setKoufuYmdGG(ReportUtil.nullToEmpty(koufuYmdList.get(CommonConstants.INT_0)));
                burdenLimitInfo.setKoufuYmdYY(ReportUtil.nullToEmpty(koufuYmdList.get(CommonConstants.INT_1)));
                burdenLimitInfo.setKoufuYmdMM(ReportUtil.nullToEmpty(koufuYmdList.get(CommonConstants.INT_2)));
                burdenLimitInfo.setKoufuYmdDD(ReportUtil.nullToEmpty(koufuYmdList.get(CommonConstants.INT_3)));
            } else {
                burdenLimitInfo.setKoufuYmdGG(CommonConstants.BLANK_STRING);
                burdenLimitInfo.setKoufuYmdYY(CommonConstants.BLANK_STRING);
                burdenLimitInfo.setKoufuYmdMM(CommonConstants.BLANK_STRING);
                burdenLimitInfo.setKoufuYmdDD(CommonConstants.BLANK_STRING);
            }
            // 適用年月日)
            List<String> startYmdList = nds3GkFunc01Logic
                    .getFs2gKantanRtn(ftnList.get(CommonConstants.INT_0).getStartYmd());
            if (CollectionUtils.isNotEmpty(startYmdList)) {
                burdenLimitInfo.setStartYmdGG(ReportUtil.nullToEmpty(startYmdList.get(CommonConstants.INT_0)));
                burdenLimitInfo.setStartYmdYY(ReportUtil.nullToEmpty(startYmdList.get(CommonConstants.INT_1)));
                burdenLimitInfo.setStartYmdMM(ReportUtil.nullToEmpty(startYmdList.get(CommonConstants.INT_2)));
                burdenLimitInfo.setStartYmdDD(ReportUtil.nullToEmpty(startYmdList.get(CommonConstants.INT_3)));
            } else {
                burdenLimitInfo.setStartYmdGG(CommonConstants.BLANK_STRING);
                burdenLimitInfo.setStartYmdYY(CommonConstants.BLANK_STRING);
                burdenLimitInfo.setStartYmdMM(CommonConstants.BLANK_STRING);
                burdenLimitInfo.setStartYmdDD(CommonConstants.BLANK_STRING);
            }
            // 有効期限
            List<String> endYmdList = nds3GkFunc01Logic
                    .getFs2gKantanRtn(ftnList.get(CommonConstants.INT_0).getEndYmd());
            if (CollectionUtils.isNotEmpty(endYmdList)) {
                burdenLimitInfo.setEndYmdGG(ReportUtil.nullToEmpty(endYmdList.get(CommonConstants.INT_0)));
                burdenLimitInfo.setEndYmdYY(ReportUtil.nullToEmpty(endYmdList.get(CommonConstants.INT_1)));
                burdenLimitInfo.setEndYmdMM(ReportUtil.nullToEmpty(endYmdList.get(CommonConstants.INT_2)));
                burdenLimitInfo.setEndYmdDD(ReportUtil.nullToEmpty(endYmdList.get(CommonConstants.INT_3)));
            } else {
                burdenLimitInfo.setEndYmdGG(CommonConstants.BLANK_STRING);
                burdenLimitInfo.setEndYmdYY(CommonConstants.BLANK_STRING);
                burdenLimitInfo.setEndYmdMM(CommonConstants.BLANK_STRING);
                burdenLimitInfo.setEndYmdDD(CommonConstants.BLANK_STRING);
            }
            // 限度額認定証確認日
            List<String> chkYmdList = nds3GkFunc01Logic
                    .getFs2gKantanRtn(ftnList.get(CommonConstants.INT_0).getChkYmd());
            if (CollectionUtils.isNotEmpty(chkYmdList)) {
                burdenLimitInfo.setChkYmdGG(ReportUtil.nullToEmpty(chkYmdList.get(CommonConstants.INT_0)));
                burdenLimitInfo.setChkYmdYY(ReportUtil.nullToEmpty(chkYmdList.get(CommonConstants.INT_1)));
                burdenLimitInfo.setChkYmdMM(ReportUtil.nullToEmpty(chkYmdList.get(CommonConstants.INT_2)));
                burdenLimitInfo.setChkYmdDD(ReportUtil.nullToEmpty(chkYmdList.get(CommonConstants.INT_3)));
            } else {
                burdenLimitInfo.setChkYmdGG(CommonConstants.BLANK_STRING);
                burdenLimitInfo.setChkYmdYY(CommonConstants.BLANK_STRING);
                burdenLimitInfo.setChkYmdMM(CommonConstants.BLANK_STRING);
                burdenLimitInfo.setChkYmdDD(CommonConstants.BLANK_STRING);
            }

            // 食事限度負担額
            burdenLimitInfo
                    .setSyokuji(ReportUtil.nullToEmpty(formatAmount(ftnList.get(CommonConstants.INT_0).getSyokuji())));
            // ユニット型個室
            burdenLimitInfo.setUnit(ReportUtil.nullToEmpty(formatAmount(ftnList.get(CommonConstants.INT_0).getUnit())));
            // ユニット型準個室
            burdenLimitInfo
                    .setUnitJ(ReportUtil.nullToEmpty(formatAmount(ftnList.get(CommonConstants.INT_0).getUnitJ())));
            // 従来型個室(特養等)
            burdenLimitInfo.setKoshituT(
                    ReportUtil.nullToEmpty(formatAmount(ftnList.get(CommonConstants.INT_0).getKoshituT())));
            // 従来型個室(老健・療養等)
            burdenLimitInfo.setKoshituR(
                    ReportUtil.nullToEmpty(formatAmount(ftnList.get(CommonConstants.INT_0).getKoshituR())));
            // 多床室
            burdenLimitInfo
                    .setTashou(ReportUtil.nullToEmpty(formatAmount(ftnList.get(CommonConstants.INT_0).getTashou())));
            // 被保険者番号
            burdenLimitInfo.setHhokennoKnj(ReportUtil.nullToEmpty(ftnList.get(CommonConstants.INT_0).getHHokennoKnj()));
            // 短期入所
            burdenLimitInfo.setSyokujiTanki(
                    ReportUtil.nullToEmpty(formatAmount(ftnList.get(CommonConstants.INT_0).getSyokujiTanki())));
            // 保険者名称
            String kHokenKnj = ftnList.get(CommonConstants.INT_0).getKHokenKnj();
            if (CommonDtoUtil.checkStringEqual(kHokenKnj, null)) {
                kHokenKnj = CommonConstants.BLANK_STRING;
            }
            // 保険者
            burdenLimitInfo.setHokenKnj(
                    ReportUtil.nullToEmpty(ftnList.get(CommonConstants.INT_0).getKHokenNo())
                            + CommonConstants.BLANK_SPACE + kHokenKnj);
            // 保険者フォント
            if (ReportUtil.getByteLength(burdenLimitInfo.getHokenKnj()) > 41) {
                burdenLimitInfo.setHokenKnjFont(ReportConstants.FONT_SIZE_9);
            } else if (ReportUtil.getByteLength(burdenLimitInfo.getHokenKnj()) > 36
                    && ReportUtil.getByteLength(burdenLimitInfo.getHokenKnj()) <= 41) {
                burdenLimitInfo.setHokenKnjFont(ReportConstants.FONT_SIZE_10);
            } else {
                burdenLimitInfo.setHokenKnjFont(ReportConstants.FONT_SIZE_12);
            }
        } else {
            // 上記以外の場合、属性のデフォルト値設定
            setPropertyDefaultValue(burdenLimitInfo);
            burdenLimitInfo.setHokenKnjFont(null);
        }

        burdenLimitInfos.add(burdenLimitInfo);
        return new JRBeanCollectionDataSource(burdenLimitInfos);
    }

    /**
     * 住環境の印刷データを編集する
     * 
     * @param history                 リクエストパラメータ.データ.印刷対象履歴リスト[0]
     * @param memoPrintFlg            メモ欄印刷フラグ
     * @param freeFaceMstPrintOutList マスタ印刷設定
     * @param kghKrkFaceSavKhnGetList 基本情報
     * @param serviceType             帳票区分
     * @throws Exception 例外
     * @return
     */
    public JRBeanCollectionDataSource getLivingEvnironmentInfoListParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history, Integer memoPrintFlg,
            List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutList,
            List<KghKrkFaceSavKhnGetOutEntity> kghKrkFaceSavKhnGetList,
            Integer serviceType) throws Exception {

        // 住環境データ
        List<ReportAssessmentFaceSheetTypeLivingEnvironmentInfo> livingEvnironmentInfoList = new ArrayList<>();

        // 1.住環境情報を取得する
        KghKrkFaceSavJukGetByCriteriaInEntity kghKrkFaceSavJukGetparam = new KghKrkFaceSavJukGetByCriteriaInEntity();
        kghKrkFaceSavJukGetparam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavJukGetOutEntity> kghKrkFaceSavJukGetList = kghTucAssFaceJukSelectMapper
                .findKghKrkFaceSavJukGetByCriteria(kghKrkFaceSavJukGetparam);

        // 2.住環境情報設定
        // 「2.5.」マスタ印刷設定情報に「条件項目ID=4」のデータをフィルター
        List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutFilterList = freeFaceMstPrintOutList.stream()
                .filter(item -> item.getKoumokuId() == CommonConstants.INT_4).toList();
        // 印刷行数
        int lineCount;
        if (CollectionUtils.isNotEmpty(freeFaceMstPrintOutFilterList)) {
            // データが存在する場合
            lineCount = freeFaceMstPrintOutFilterList.get(CommonConstants.INT_0).getLineCnt();
        } else {
            lineCount = CommonConstants.INT_0;
        }

        Integer defaultMemoFlg = null;
        Integer memoFlg = null;
        if (memoPrintFlg == CommonConstants.INT_1) {
            defaultMemoFlg = CommonConstants.INT_1;
            memoFlg = CommonConstants.INT_1;
        } else if (memoPrintFlg == CommonConstants.INT_0) {
            defaultMemoFlg = CommonConstants.INT_0;
            memoFlg = CommonConstants.INT_0;
        } else if (memoPrintFlg == CommonConstants.INT_2) {
            defaultMemoFlg = CommonConstants.INT_2;
            memoFlg = CommonConstants.INT_0;
        }

        for (int i = CommonConstants.INT_0; i < lineCount; i++) {
            ReportAssessmentFaceSheetTypeLivingEnvironmentInfo info = new ReportAssessmentFaceSheetTypeLivingEnvironmentInfo();
            // 調査項目コード
            info.setChousa1Cd(null);
            // 状況コード
            info.setChousa2Cd(null);
            // 調査項目名称
            info.setChousaKnj(CommonConstants.BLANK_STRING);
            // 状況名称
            info.setJoukyoKnj(CommonConstants.BLANK_STRING);
            // 手すり有無区分
            info.setTesuriKbn(CommonConstants.INT_0);
            // 段差有無区分
            info.setDansaKbn(CommonConstants.INT_0);
            // レベルコード
            info.setLevel(CommonConstants.INT_0);
            if (serviceType == CommonConstants.INT_2) {
                // アセスメントフェースシート(TYPE2）の場合
                // 問題レベル
                info.setLevelKnj(CommonConstants.BLANK_STRING);
            }
            // 対応済みフラグ
            info.setTaiouFlg(null);
            // メモ
            info.setMemo(CommonConstants.BLANK_STRING);
            // メモ区分
            info.setMemoKbn(null);

            livingEvnironmentInfoList.add(info);
        }

        int i = CommonConstants.INT_0;
        for (KghKrkFaceSavJukGetOutEntity entity : kghKrkFaceSavJukGetList) {
            if (i == lineCount) {
                break;
            }

            ReportAssessmentFaceSheetTypeLivingEnvironmentInfo info = livingEvnironmentInfoList.get(i);
            // 調査項目コード
            info.setChousa1Cd(entity.getChousa1Cd());
            // 状況コード
            info.setChousa2Cd(entity.getChousa2Cd());
            // 調査項目名称
            info.setChousaKnj(ReportUtil.nullToEmpty(entity.getChousa1Knj()));
            // 状況名称
            info.setJoukyoKnj(ReportUtil.nullToEmpty(entity.getChousa2Knj()));
            // 手すり有無区分
            info.setTesuriKbn(entity.getTesuriKbn());
            // 段差有無区分
            info.setDansaKbn(entity.getDansaKbn());
            // レベルコード
            info.setLevel(CommonDtoUtil.strValToInt(entity.getLevelKnj()));
            if (serviceType == CommonConstants.INT_2) {
                // アセスメントフェースシート(TYPE2）の場合
                // 問題レベル
                switch (info.getLevel()) {
                    case CommonConstants.INT_1:
                        info.setLevelKnj(ReportConstants.LEVELKNJ_1);
                        break;
                    case CommonConstants.INT_2:
                        info.setLevelKnj(ReportConstants.LEVELKNJ_2);
                        break;
                    case CommonConstants.INT_3:
                        info.setLevelKnj(ReportConstants.LEVELKNJ_3);
                        break;
                    case CommonConstants.INT_4:
                        info.setLevelKnj(ReportConstants.LEVELKNJ_4);
                        break;
                    case CommonConstants.INT_5:
                        info.setLevelKnj(ReportConstants.LEVELKNJ_5);
                        break;
                    default:
                        info.setLevelKnj(CommonConstants.BLANK_STRING);
                }
            }

            // 対応済みフラグ
            info.setTaiouFlg(entity.getTaiouFlg());
            // メモ
            info.setMemo(ReportUtil.nullToEmpty(entity.getBikoKnj()));
            // メモフォント
            if (StringUtil.isNotEmpty(info.getMemo())) {
                if (serviceType == CommonConstants.INT_1) {
                    // アセスメントフェースシート(TYPE1）の場合
                    if (ReportUtil.getByteLength(info.getMemo()) > 376) {
                        info.setMemoFont(ReportConstants.FONT_SIZE_6);
                    } else if (ReportUtil.getByteLength(info.getMemo()) > 210
                            && ReportUtil.getByteLength(info.getMemo()) <= 376) {
                        info.setMemoFont(ReportConstants.FONT_SIZE_9);
                    } else {
                        info.setMemoFont(ReportConstants.FONT_SIZE_12);
                    }
                } else {
                    // アセスメントフェースシート(TYPE2）の場合
                    if (ReportUtil.getByteLength(info.getMemo()) <= 306) {
                        info.setMemoFont(ReportConstants.FONT_SIZE_9);
                    } else if (ReportUtil.getByteLength(info.getMemo()) > 306
                            && ReportUtil.getByteLength(info.getMemo()) <= 448) {
                        info.setMemoFont(ReportConstants.FONT_SIZE_8);
                    } else {
                        info.setMemoFont(ReportConstants.FONT_SIZE_6);
                    }
                }
            }
            // メモ区分
            if (defaultMemoFlg != null
                    && (defaultMemoFlg == CommonConstants.INT_0 || defaultMemoFlg == CommonConstants.INT_1)) {
                info.setMemoKbn(memoFlg);
            } else if (defaultMemoFlg != null && defaultMemoFlg == CommonConstants.INT_2) {
                if (!StringUtil.isNotEmpty(entity.getBikoKnj())) {
                    info.setMemoKbn(CommonConstants.INT_0);
                } else {
                    info.setMemoKbn(CommonConstants.INT_1);
                }
            }
        }

        // 結果を返し
        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put(ReportConstants.JRDS_JUKLIST_KEY, new JRBeanCollectionDataSource(livingEvnironmentInfoList));
        map.put(ReportConstants.JRDS_TITLE_KEY,
                CollectionUtils.isNotEmpty(livingEvnironmentInfoList)
                        ? livingEvnironmentInfoList.get(CommonConstants.INT_0).getMemoKbn()
                        : CommonConstants.INT_0);
        mapList.add(map);
        return new JRBeanCollectionDataSource(mapList);
    }

    /**
     * 口座情報の印刷データを編集
     * 
     * @param history                 リクエストパラメータ.データ.印刷対象履歴リスト[0]
     * @param freeFaceMstPrintOutList マスタ印刷設定
     * @param memoPrintFlg            メモ欄印刷フラグ
     * @param memoKubun               メモ区分
     * @param serviceType             帳票区分
     * @return 口座情報
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getAccountInfoParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutList, Integer memoPrintFlg, Integer memoKubun,
            Integer serviceType)
            throws Exception {

        // 1. 印刷行数設定の取得
        int lineCount = CommonConstants.INT_0;
        Optional<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutFilter = freeFaceMstPrintOutList.stream()
                .filter(item -> item.getKoumokuId() == CommonConstants.INT_5).findFirst();
        if (freeFaceMstPrintOutFilter.isPresent()) {
            lineCount = freeFaceMstPrintOutFilter.get().getLineCnt();
        }

        // 2. 口座編集
        KghKrkFaceSavKozGetByCriteriaInEntity param = new KghKrkFaceSavKozGetByCriteriaInEntity();
        param.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavKozGetOutEntity> result = kghTucAssFaceKozSelectMapper
                .findKghKrkFaceSavKozGetByCriteria(param);

        List<ReportAssessmentFaceSheetTypeAccount> accountList = new ArrayList<>();
        // 印刷行数まで、繰り返し、下記デフォルト値設定処理を行う
        for (int i = CommonConstants.INT_0; i < lineCount; i++) {
            ReportAssessmentFaceSheetTypeAccount accountInfo = new ReportAssessmentFaceSheetTypeAccount();
            // 口座番号
            accountInfo.setKouzaNumber(CommonConstants.BLANK_STRING);
            // 名義人カナ
            accountInfo.setKouzaKana(CommonConstants.BLANK_STRING);
            // 名義人氏名
            accountInfo.setKouzaKnj(CommonConstants.BLANK_STRING);
            // 口座種別
            accountInfo.setKouzaShuKnj(CommonConstants.BLANK_STRING);
            // メモ
            accountInfo.setMemoKnj(CommonConstants.BLANK_STRING);
            // 銀行名
            accountInfo.setGinstnKnj(CommonConstants.BLANK_STRING);
            // 口座開設日
            accountInfo.setKouzaYmdGG(CommonConstants.BLANK_STRING);
            accountInfo.setKouzaYmdYY(CommonConstants.BLANK_STRING);
            accountInfo.setKouzaYmdMM(CommonConstants.BLANK_STRING);
            accountInfo.setKouzaYmdDD(CommonConstants.BLANK_STRING);
            // 口座解約日
            accountInfo.setEndYmdGG(CommonConstants.BLANK_STRING);
            accountInfo.setEndYmdYY(CommonConstants.BLANK_STRING);
            accountInfo.setEndYmdMM(CommonConstants.BLANK_STRING);
            accountInfo.setEndYmdDD(CommonConstants.BLANK_STRING);
            // 適用施設
            accountInfo.setShisetuKnj(CommonConstants.BLANK_STRING);
            // 口座名
            accountInfo.setKouzaNameKnj(CommonConstants.BLANK_STRING);
            // 満期日
            accountInfo.setMankibiYmdGG(CommonConstants.BLANK_STRING);
            accountInfo.setMankibiYmdYY(CommonConstants.BLANK_STRING);
            accountInfo.setMankibiYmdMM(CommonConstants.BLANK_STRING);
            accountInfo.setMankibiYmdDD(CommonConstants.BLANK_STRING);
            // 種別名
            accountInfo.setKoushyKnj(CommonConstants.BLANK_STRING);
            // 期首金額
            accountInfo.setKishuKingaku(CommonConstants.BLANK_STRING);
            // 上限額
            accountInfo.setJougenGaku(CommonConstants.BLANK_STRING);
            // メモ区分
            accountInfo.setMemoKbn(memoKubun);
            accountList.add(accountInfo);
        }

        int i = CommonConstants.INT_0;
        for (KghKrkFaceSavKozGetOutEntity entity : result) {
            if (i == lineCount) {
                break;
            }
            ReportAssessmentFaceSheetTypeAccount accountInfo = accountList.get(i);
            // 口座番号
            accountInfo.setKouzaNumber(ReportUtil.nullToEmpty(entity.getKouzaNumber()));
            // 名義人カナ
            accountInfo.setKouzaKana(ReportUtil.nullToEmpty(entity.getKouzaKana()));
            // 名義人氏名
            accountInfo.setKouzaKnj(ReportUtil.nullToEmpty(entity.getKouzaKnj()));
            // 口座種別
            accountInfo.setKouzaShuKnj(ReportUtil.nullToEmpty(entity.getKouzaShuKnj()));
            if (StringUtil.isNotEmpty(accountInfo.getKouzaShuKnj())) {
                accountInfo.setKouzaShuKnj(CommonConstants.LEFT_PARENTHESIS.concat(accountInfo.getKouzaShuKnj())
                        .concat(CommonConstants.RIGHT_PARENTHESIS));
            }
            // メモ
            accountInfo.setMemoKnj(ReportUtil.nullToEmpty(entity.getMemoKnj()));
            // メモの桁数
            if (serviceType == CommonConstants.INT_2) {
                // アセスメントフェースシート(TYPE2）の場合
                if (StringUtil.isNotEmpty(accountInfo.getMemoKnj())) {
                    if (ReportUtil.getByteLength(accountInfo.getMemoKnj()) <= 104) {
                        accountInfo.setMemoKnjFont(ReportConstants.FONT_SIZE_9);
                    } else {
                        accountInfo.setMemoKnjFont(CommonConstants.STR_7);
                    }
                }
            }
            // 銀行名
            accountInfo.setGinstnKnj(
                    ReportUtil.nullToEmpty(entity.getBankKnj() + CommonConstants.BLANK_SPACE + entity.getStnKnj()));
            // 銀行名フォント
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                if (ReportUtil.getByteLength(accountInfo.getGinstnKnj()) < 23) {
                    accountInfo.setGinstnKnjFont(ReportConstants.FONT_SIZE_12);
                } else if (ReportUtil.getByteLength(accountInfo.getGinstnKnj()) >= CommonConstants.INT_23
                        && ReportUtil.getByteLength(accountInfo.getGinstnKnj()) < CommonConstants.INT_29) {
                    accountInfo.setGinstnKnjFont(ReportConstants.FONT_SIZE_10);
                } else if (ReportUtil.getByteLength(accountInfo.getGinstnKnj()) >= CommonConstants.INT_29
                        && ReportUtil.getByteLength(accountInfo.getGinstnKnj()) < 33) {
                    accountInfo.setGinstnKnjFont(ReportConstants.FONT_SIZE_8);
                } else {
                    accountInfo.setGinstnKnjFont(ReportConstants.FONT_SIZE_6);
                }
            } else {
                // アセスメントフェースシート(TYPE2）の場合
                accountInfo.setGinstnKnjFont(ReportConstants.FONT_SIZE_9);
            }
            // 口座開設日
            List<String> kouzaYmd = nds3GkFunc01Logic.getFs2gKantanRtn(entity.getStartYmd());
            if (CollectionUtils.isNotEmpty(kouzaYmd)) {
                accountInfo.setKouzaYmdGG(ReportUtil.nullToEmpty(kouzaYmd.get(CommonConstants.INT_0)));
                accountInfo.setKouzaYmdYY(ReportUtil.nullToEmpty(kouzaYmd.get(CommonConstants.INT_1)));
                accountInfo.setKouzaYmdMM(ReportUtil.nullToEmpty(kouzaYmd.get(CommonConstants.INT_2)));
                accountInfo.setKouzaYmdDD(ReportUtil.nullToEmpty(kouzaYmd.get(CommonConstants.INT_3)));
            } else {
                accountInfo.setKouzaYmdGG(CommonConstants.BLANK_STRING);
                accountInfo.setKouzaYmdYY(CommonConstants.BLANK_STRING);
                accountInfo.setKouzaYmdMM(CommonConstants.BLANK_STRING);
                accountInfo.setKouzaYmdDD(CommonConstants.BLANK_STRING);
            }
            // 口座解約日
            List<String> endYmd = nds3GkFunc01Logic.getFs2gKantanRtn(entity.getEndYmd());
            if (CollectionUtils.isNotEmpty(kouzaYmd)) {
                accountInfo.setEndYmdGG(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_0)));
                accountInfo.setEndYmdYY(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_1)));
                accountInfo.setEndYmdMM(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_2)));
                accountInfo.setEndYmdDD(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_3)));
            } else {
                accountInfo.setEndYmdGG(CommonConstants.BLANK_STRING);
                accountInfo.setEndYmdYY(CommonConstants.BLANK_STRING);
                accountInfo.setEndYmdMM(CommonConstants.BLANK_STRING);
                accountInfo.setEndYmdDD(CommonConstants.BLANK_STRING);
            }
            // 適用施設
            accountInfo.setShisetuKnj(ReportUtil.nullToEmpty(entity.getShisetuKnj()));
            // 口座名
            accountInfo.setKouzaNameKnj(ReportUtil.nullToEmpty(entity.getKouzaNameKnj()));
            // 満期日
            List<String> manKiBiYmd = nds3GkFunc01Logic.getFs2gKantanRtn(entity.getMankibiYmd());
            if (CollectionUtils.isNotEmpty(kouzaYmd)) {
                accountInfo.setMankibiYmdGG(ReportUtil.nullToEmpty(manKiBiYmd.get(CommonConstants.INT_0)));
                accountInfo.setMankibiYmdYY(ReportUtil.nullToEmpty(manKiBiYmd.get(CommonConstants.INT_1)));
                accountInfo.setMankibiYmdMM(ReportUtil.nullToEmpty(manKiBiYmd.get(CommonConstants.INT_2)));
                accountInfo.setMankibiYmdDD(ReportUtil.nullToEmpty(manKiBiYmd.get(CommonConstants.INT_3)));
            } else {
                accountInfo.setMankibiYmdGG(CommonConstants.BLANK_STRING);
                accountInfo.setMankibiYmdYY(CommonConstants.BLANK_STRING);
                accountInfo.setMankibiYmdMM(CommonConstants.BLANK_STRING);
                accountInfo.setMankibiYmdDD(CommonConstants.BLANK_STRING);
            }
            // 種別名
            accountInfo.setKoushyKnj(ReportUtil.nullToEmpty(entity.getKoushyNameKnj()));
            // 期首金額
            accountInfo.setKishuKingaku(ReportUtil.nullToEmpty(formatAmount(entity.getKishuKingaku())));
            // 上限額
            accountInfo.setJougenGaku(ReportUtil.nullToEmpty(formatAmount(entity.getJougenGaku())));

            // メモ区分
            if (memoPrintFlg == CommonConstants.INT_0 || memoPrintFlg == CommonConstants.INT_1) {
                accountInfo.setMemoKbn(memoKubun);
            } else if (memoPrintFlg == CommonConstants.INT_2) {
                if (StringUtil.isNotEmpty(entity.getMemoKnj())) {
                    accountInfo.setMemoKbn(CommonConstants.INT_1);
                } else {
                    accountInfo.setMemoKbn(CommonConstants.INT_0);
                }
            }
            i++;
        }

        // 結果を返し
        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put(ReportConstants.JRDS_KOZLIST_KEY, new JRBeanCollectionDataSource(accountList));
        map.put(ReportConstants.JRDS_TITLE_KEY,
                CollectionUtils.isNotEmpty(accountList) ? accountList.get(CommonConstants.INT_0).getMemoKbn()
                        : CommonConstants.INT_0);
        mapList.add(map);
        return new JRBeanCollectionDataSource(mapList);
    }

    /**
     * 自立度情報の印刷データを編集する
     * 
     * @param history リクエストパラメータ.データ.印刷対象履歴リスト[0]
     * @return 自立度情報の印刷データ
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getIndependenceLevelParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history) throws Exception {
        List<ReportAssessmentFaceSheetTypeIndependenceLevel> independenceLevelList = new ArrayList<>();

        /*
         * 1.行政区分名称リストの取得
         */
        // 行政区分名称リストを取得する
        KghKrkFaceSavAnyGetByCriteriaInEntity param = new KghKrkFaceSavAnyGetByCriteriaInEntity();
        param.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        param.setKey1(CommonConstants.INT_1);
        List<KghKrkFaceSavAnyGetOutEntity> kghKrkFaceSavAnyGetList = kghTucAssFaceAnySelectMapper
                .findKghKrkFaceSavAnyGetByCriteria(param);

        /*
         * 2.行政区分リストの取得
         */
        // 行政区分リストの取得
        KghKrkFaceSavKhnGetByCriteriaInEntity kghKrkFaceSavKhnParam = new KghKrkFaceSavKhnGetByCriteriaInEntity();
        kghKrkFaceSavKhnParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavKhnGetOutEntity> kghKrkFaceSavKhnList = kghTucAssFaceKhnSelectMapper
                .findKghKrkFaceSavKhnGetByCriteria(kghKrkFaceSavKhnParam);

        /*
         * 3. 自立度情報編集
         */
        // 自立度情報を取得する
        KghKrkFaceSavRanGetByCriteriaInEntity kghKrkFaceSavRanGetParam = new KghKrkFaceSavRanGetByCriteriaInEntity();
        kghKrkFaceSavRanGetParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavRanGetOutEntity> kghKrkFaceSavRanGetList = kghTucAssFaceRanSelectMapper
                .findKghKrkFaceSavRanGetByCriteria(kghKrkFaceSavRanGetParam);

        // (1). 自立度情報が存在する場合、下記編集処理を行う
        ReportAssessmentFaceSheetTypeIndependenceLevel info = new ReportAssessmentFaceSheetTypeIndependenceLevel();
        if (CollectionUtils.isNotEmpty(kghKrkFaceSavRanGetList)) {
            // 自立度情報が存在する場合
            KghKrkFaceSavRanGetOutEntity kghKrkFaceSavRanGet = kghKrkFaceSavRanGetList.get(CommonConstants.INT_0);
            // 判定日
            List<String> hanteiYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavRanGet.getChkYmd());
            if (CollectionUtils.isNotEmpty(hanteiYmd)) {
                info.setHanteiDateGG(ReportUtil.nullToEmpty(hanteiYmd.get(CommonConstants.INT_0)));
                info.setHanteiDateYY(ReportUtil.nullToEmpty(hanteiYmd.get(CommonConstants.INT_1)));
                info.setHanteiDateMM(ReportUtil.nullToEmpty(hanteiYmd.get(CommonConstants.INT_2)));
                info.setHanteiDateDD(ReportUtil.nullToEmpty(hanteiYmd.get(CommonConstants.INT_3)));
            } else {
                info.setHanteiDateGG(CommonConstants.BLANK_STRING);
                info.setHanteiDateYY(CommonConstants.BLANK_STRING);
                info.setHanteiDateMM(CommonConstants.BLANK_STRING);
                info.setHanteiDateDD(CommonConstants.BLANK_STRING);
            }
            // 判定者氏名
            info.setHanteiName(ReportUtil.nullToEmpty(kghKrkFaceSavRanGet.getChkKnj()));
            // 寝たきり度
            info.setNetaKnj(ReportUtil.nullToEmpty(kghKrkFaceSavRanGet.getNeta1Cd()));
            // 痴呆度
            info.setTihoKnj(ReportUtil.nullToEmpty(kghKrkFaceSavRanGet.getNeta2Cd()));
        } else {
            // 自立度情報が存在しない場合
            // 判定日
            info.setHanteiDateGG(CommonConstants.BLANK_STRING);
            info.setHanteiDateYY(CommonConstants.BLANK_STRING);
            info.setHanteiDateMM(CommonConstants.BLANK_STRING);
            info.setHanteiDateDD(CommonConstants.BLANK_STRING);
            // 判定者氏名
            info.setHanteiName(CommonConstants.BLANK_STRING);
            // 寝たきり度
            info.setNetaKnj(CommonConstants.BLANK_STRING);
            // 痴呆度
            info.setTihoKnj(CommonConstants.BLANK_STRING);
        }
        independenceLevelList.add(info);

        // (3). 適用区分1～10の設定
        Optional<KghKrkFaceSavAnyGetOutEntity> filter;
        String tekiyouKnj = CommonConstants.BLANK_STRING;
        for (int i = CommonConstants.INT_1; i <= 10; i++) {
            int index = i;
            if (CollectionUtils.isNotEmpty(kghKrkFaceSavAnyGetList)) {
                filter = kghKrkFaceSavAnyGetList.stream().filter(item -> item.getKey2() == index).findFirst();
                if (filter.isPresent()) {
                    tekiyouKnj = ReportUtil.nullToEmpty(filter.get().getSdat());
                }
            }
            switch (i) {
                case 1:
                    independenceLevelList.get(CommonConstants.INT_0).setTekiyou1Knj(tekiyouKnj);
                    break;
                case 2:
                    independenceLevelList.get(CommonConstants.INT_0).setTekiyou2Knj(tekiyouKnj);
                    break;
                case 3:
                    independenceLevelList.get(CommonConstants.INT_0).setTekiyou3Knj(tekiyouKnj);
                    break;
                case 4:
                    independenceLevelList.get(CommonConstants.INT_0).setTekiyou4Knj(tekiyouKnj);
                    break;
                case 5:
                    independenceLevelList.get(CommonConstants.INT_0).setTekiyou5Knj(tekiyouKnj);
                    break;
                case 6:
                    independenceLevelList.get(CommonConstants.INT_0).setTekiyou6Knj(tekiyouKnj);
                    break;
                case 7:
                    independenceLevelList.get(CommonConstants.INT_0).setTekiyou7Knj(tekiyouKnj);
                    break;
                case 8:
                    independenceLevelList.get(CommonConstants.INT_0).setTekiyou8Knj(tekiyouKnj);
                    break;
                case 9:
                    independenceLevelList.get(CommonConstants.INT_0).setTekiyou9Knj(tekiyouKnj);
                    break;
                case 10:
                    independenceLevelList.get(CommonConstants.INT_0).setTekiyou10Knj(tekiyouKnj);
                    break;
            }
        }

        // (4). 適用区分1～10区分の設定
        if (CollectionUtils.isNotEmpty(kghKrkFaceSavKhnList)) {
            independenceLevelList.get(CommonConstants.INT_0)
                    .setTekiyou1Kbn(
                            kghKrkFaceSavKhnList.get(CommonConstants.INT_0).getGyou1Kbn());
            independenceLevelList.get(CommonConstants.INT_0)
                    .setTekiyou2Kbn(
                            kghKrkFaceSavKhnList.get(CommonConstants.INT_0).getGyou2Kbn());
            independenceLevelList.get(CommonConstants.INT_0)
                    .setTekiyou3Kbn(
                            kghKrkFaceSavKhnList.get(CommonConstants.INT_0).getGyou3Kbn());
            independenceLevelList.get(CommonConstants.INT_0)
                    .setTekiyou4Kbn(
                            kghKrkFaceSavKhnList.get(CommonConstants.INT_0).getGyou4Kbn());
            independenceLevelList.get(CommonConstants.INT_0)
                    .setTekiyou5Kbn(
                            kghKrkFaceSavKhnList.get(CommonConstants.INT_0).getGyou5Kbn());
            independenceLevelList.get(CommonConstants.INT_0)
                    .setTekiyou6Kbn(
                            kghKrkFaceSavKhnList.get(CommonConstants.INT_0).getGyou6Kbn());
            independenceLevelList.get(CommonConstants.INT_0)
                    .setTekiyou7Kbn(
                            kghKrkFaceSavKhnList.get(CommonConstants.INT_0).getGyou7Kbn());
            independenceLevelList.get(CommonConstants.INT_0)
                    .setTekiyou8Kbn(
                            kghKrkFaceSavKhnList.get(CommonConstants.INT_0).getGyou8Kbn());
            independenceLevelList.get(CommonConstants.INT_0)
                    .setTekiyou9Kbn(
                            kghKrkFaceSavKhnList.get(CommonConstants.INT_0).getGyou9Kbn());
            independenceLevelList.get(CommonConstants.INT_0)
                    .setTekiyou10Kbn(
                            kghKrkFaceSavKhnList.get(CommonConstants.INT_0).getGyou10Kbn());
        } else {
            independenceLevelList.get(CommonConstants.INT_0).setTekiyou1Kbn(CommonConstants.INT_0);
            independenceLevelList.get(CommonConstants.INT_0).setTekiyou2Kbn(CommonConstants.INT_0);
            independenceLevelList.get(CommonConstants.INT_0).setTekiyou3Kbn(CommonConstants.INT_0);
            independenceLevelList.get(CommonConstants.INT_0).setTekiyou4Kbn(CommonConstants.INT_0);
            independenceLevelList.get(CommonConstants.INT_0).setTekiyou5Kbn(CommonConstants.INT_0);
            independenceLevelList.get(CommonConstants.INT_0).setTekiyou6Kbn(CommonConstants.INT_0);
            independenceLevelList.get(CommonConstants.INT_0).setTekiyou7Kbn(CommonConstants.INT_0);
            independenceLevelList.get(CommonConstants.INT_0).setTekiyou8Kbn(CommonConstants.INT_0);
            independenceLevelList.get(CommonConstants.INT_0).setTekiyou9Kbn(CommonConstants.INT_0);
            independenceLevelList.get(CommonConstants.INT_0).setTekiyou10Kbn(CommonConstants.INT_0);
        }

        // 結果を返し
        return new JRBeanCollectionDataSource(independenceLevelList);
    }

    /**
     * 基本状況情報の印刷データを編集する
     * 
     * @param houJinId                法人ID
     * @param shisetuId               施設ID
     * @param svJigyoId               事業者ID
     * @param freeFaceMstPrintOutList マスタ印刷設定
     * @param history                 リクエストパラメータ.データ.印刷対象履歴リスト[0]
     * @param systemDate              システム年月
     * @param baseFlg                 基本フラグ
     * @return 基本状況情報
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getJoKyoParameters(Integer houJinId, Integer shisetuId, Integer svJigyoId,
            List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutList,
            ReportAssessmentFaceSheetTypePrintSubjectHistory history, String systemDate, Integer baseFlg)
            throws Exception {

        List<ReportAssessmentFaceSheetTypeBasicSituation> infoList = new ArrayList<>();
        List<Map<String, JRBeanCollectionDataSource>> khjList = new ArrayList<>();
        Map<String, JRBeanCollectionDataSource> khjMap = new HashMap<>();

        // 1.1.表示設定マスタ情報リストの取得
        KghKrkFreeFaceAssBunruiGetByCriteriaInEntity kghKrkFreeFaceAssBunruiParam = new KghKrkFreeFaceAssBunruiGetByCriteriaInEntity();
        kghKrkFreeFaceAssBunruiParam.setSvJigyoId(svJigyoId);
        List<KghKrkFreeFaceAssBunruiGetOutEntity> kghKrkFreeFaceAssBunruiList = kghKrkFreeFaceAssBunruiGetSelectMapper
                .findKghKrkFreeFaceAssBunruiGetByCriteria(kghKrkFreeFaceAssBunruiParam);

        // 1.2.表示設定マスタ情報リスト＝上記「1.1.」取得のOUTPUT情報を項目ＩＤ＝9でフィルターする
        List<KghKrkFreeFaceAssBunruiGetOutEntity> filterList = kghKrkFreeFaceAssBunruiList.stream()
                .filter(item -> item.getItemKbn() == CommonConstants.INT_9).toList();

        // 印刷行数設定の取得
        int lineCount = CommonConstants.INT_0;
        Optional<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutFilter = freeFaceMstPrintOutList.stream()
                .filter(item -> item.getKoumokuId() == CommonConstants.INT_12).findFirst();
        if (freeFaceMstPrintOutFilter.isPresent()) {
            lineCount = freeFaceMstPrintOutFilter.get().getLineCnt();
        }

        // 2.基本状況情報リストの取得
        for (int i = CommonConstants.INT_0; i < lineCount; i++) {
            ReportAssessmentFaceSheetTypeBasicSituation info = new ReportAssessmentFaceSheetTypeBasicSituation();
            // 状況名
            info.setJyoukyouKnj(CommonConstants.BLANK_STRING);
            // メモ
            info.setKhjMemoKnj(CommonConstants.BLANK_STRING);
            // 状態表示区分
            info.setJyoutaiKbn(CommonConstants.INT_0);
            // 速度向上フラグ
            info.setLiKjnFlg(CommonConstants.INT_0);
            // メモ欄の出力領域を固定フラグ
            info.setCbxKjnFlg(CommonConstants.INT_0);
            infoList.add(info);
        }

        List<Integer> svJigyoIdList = new ArrayList<>();
        svJigyoIdList.add(svJigyoId);

        // 2.2.上記処理「1.2.」取得した表示設定マスタ情報リスト件数分、変数.印刷行数まで、下記処理を行う
        KghKrdFaceAss1GetByCriteriaInEntity kghKrdFaceAss1GetParam = new KghKrdFaceAss1GetByCriteriaInEntity();
        KghKrdFaceAss2KhnGetByCriteriaInEntity param2 = new KghKrdFaceAss2KhnGetByCriteriaInEntity();
        KghKrkFaceSavAss1GetByCriteriaInEntity kghKrkFaceSavAss1GetParam = new KghKrkFaceSavAss1GetByCriteriaInEntity();
        KghKrkFaceSavAss2GetByCriteriaInEntity kghKrkFaceSavAss2GetParam = new KghKrkFaceSavAss2GetByCriteriaInEntity();
        int i = CommonConstants.INT_0;
        for (KghKrkFreeFaceAssBunruiGetOutEntity entity : filterList) {
            if (i == lineCount) {
                break;
            }

            List<KghKrkFaceSavAss2GetOutEntity> kghKrkFaceSavAss2List = new ArrayList<>();
            ReportAssessmentFaceSheetTypeBasicSituation info = infoList.get(i);

            // 2.2.1.履歴番号を取得する
            if (CommonDtoUtil.checkStringEqual(history.getSeqSav(), CommonConstants.STR_0)) {
                // ①リクエストパラメータ.データ.印刷対象履歴リスト[0].保存連番＝0の場合
                // 履歴番号を取得する
                kghKrdFaceAss1GetParam.setHid(svJigyoId);
                kghKrdFaceAss1GetParam.setSid(shisetuId);
                kghKrdFaceAss1GetParam.setJidList(svJigyoIdList);
                kghKrdFaceAss1GetParam.setUserid(CommonDtoUtil.strValToInt(history.getUserId()));
                kghKrdFaceAss1GetParam.setYmd(systemDate);
                kghKrdFaceAss1GetParam.setBunId(entity.getBunId());
                List<KghKrdFaceAss1GetOutEntity> kghKrdFaceAss1GetList = hyokaKoumokuMasutaDetaSelectMapper
                        .findKghKrdFaceAss1GetByCriteria(kghKrdFaceAss1GetParam);
                // 基本状況情報リストを取得する
                param2.setRirekiNo(kghKrdFaceAss1GetList.get(CommonConstants.INT_0).getRirekiNo());
                // List<KghKrdFaceAss2KhnGetOutEntity> kghKrdFaceAss2KhnGetList =
                // kghKrdFaceAss2KhnGetSelectMapper
                // .findKghKrdFaceAss2KhnGetByCriteria(param2);

            } else {
                // ②上記以外の場合
                // アセスメントフェースシート履歴アセスメント1情報を取得する
                kghKrkFaceSavAss1GetParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
                kghKrkFaceSavAss1GetParam.setDaibunruiId(entity.getBunId());
                List<KghKrkFaceSavAss1GetOutEntity> resultList4 = kghTucAssFaceAss1SelectMapper
                        .findKghKrkFaceSavAss1GetByCriteria(kghKrkFaceSavAss1GetParam);
                // 基本状況情報リストを取得する
                kghKrkFaceSavAss2GetParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
                kghKrkFaceSavAss2GetParam.setRirekiNo(resultList4.get(CommonConstants.INT_0).getRirekiNo());
                kghKrkFaceSavAss2List = kghTucAssFaceAss2SelectMapper
                        .findKghKrkFaceSavAss2GetByCriteria(kghKrkFaceSavAss2GetParam);
            }

            // 状態表示区分
            info.setJyoutaiKbn(baseFlg);
            // 速度向上フラグ
            info.setLiKjnFlg(baseFlg);
            // メモ欄の出力領域を固定フラグ
            info.setCbxKjnFlg(baseFlg);

            // 2.2.3.基本状況情報リストのメモを取得する
            if (CollectionUtils.isNotEmpty(kghKrkFaceSavAss2List)) {
                // 状況名
                info.setJyoukyouKnj(kghKrkFaceSavAss2List.get(CommonConstants.INT_0).getAssessKnj());
                // メモ
                if (kghKrkFaceSavAss2List.get(CommonConstants.INT_0).getRirekiNo() != null
                        && CommonDtoUtil.strValToInt(history.getSeqSav()) > CommonConstants.INT_0) {
                    if (StringUtil.isNotEmpty(kghKrkFaceSavAss2List.get(CommonConstants.INT_0).getAssessKnj())) {
                        info.setKhjMemoKnj(
                                ReportUtil.nullToEmpty(kghKrkFaceSavAss2List.get(CommonConstants.INT_0).getYouguKnj()));
                    } else {
                        info.setKhjMemoKnj(CommonConstants.BLANK_STRING);
                    }
                }
                // メモフォント
                if (info.getLiKjnFlg() == CommonConstants.INT_1) {
                    if (ReportUtil.getByteLength(info.getKhjMemoKnj()) > 272) {
                        info.setKhjMemoKnjFont("9.6");
                    } else {
                        info.setKhjMemoKnjFont(ReportConstants.FONT_SIZE_12);
                    }
                } else {
                    info.setKhjMemoKnjFont(ReportConstants.FONT_SIZE_12);
                }
            }
            i++;
        }

        // 結果を返し
        khjMap.put(ReportConstants.JRDS_KHJLIST_KEY, new JRBeanCollectionDataSource(infoList));
        khjList.add(khjMap);
        return new JRBeanCollectionDataSource(khjList);
    }

    /**
     * 介護保険情報の印刷データを編集する
     * 
     * @param history      印刷対象履歴
     * @param memoPrintFlg メモ欄印刷フラグ
     * @param memoKubun    メモ区分
     * @param serviceType  帳票区分
     * @return 介護保険情報
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getNursingCareInsuranceInfoParamters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            Integer memoPrintFlg,
            Integer memoKubun, Integer serviceType) throws Exception {

        List<ReportAssessmentFaceSheetTypeNursingCareInsuranceInfo> nursingCareInsuranceInfoList = new ArrayList<>();
        ReportAssessmentFaceSheetTypeNursingCareInsuranceInfo nursingCareInsuranceInfo = new ReportAssessmentFaceSheetTypeNursingCareInsuranceInfo();

        /*
         * 介護保険情報と介護確定情報を取得する
         */
        // 介護保険情報取得
        KghKrkFaceSavKaigoGetByCriteriaInEntity kghKrkFaceSavKaigoParam = new KghKrkFaceSavKaigoGetByCriteriaInEntity();
        kghKrkFaceSavKaigoParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavKaigoGetOutEntity> kghKrkFaceSavKaigoList = kghTucAssFaceKaigoSelectMapper
                .findKghKrkFaceSavKaigoGetByCriteria(kghKrkFaceSavKaigoParam);

        // 介護確定情報取得
        KghKrkFaceSavNinteiGetByCriteriaInEntity kghKrkFaceSavNinteiParam = new KghKrkFaceSavNinteiGetByCriteriaInEntity();
        kghKrkFaceSavNinteiParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavNinteiGetOutEntity> kghKrkFaceSavNinteiList = kghTucAssFaceNinteiSelectMapper
                .findKghKrkFaceSavNinteiGetByCriteria(kghKrkFaceSavNinteiParam);

        // 連番
        Integer seqNo;
        // 1. 介護保険情報編集
        if (CollectionUtils.isNotEmpty(kghKrkFaceSavKaigoList)) {
            KghKrkFaceSavKaigoGetOutEntity kghKrkFaceSavKaigo = kghKrkFaceSavKaigoList.get(CommonConstants.INT_0);
            seqNo = kghKrkFaceSavKaigo.getSeqNo();
            // 介護保険情報.有効期間・開始日
            List<String> startYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavKaigo.getStartYmd());
            if (CollectionUtils.isNotEmpty(startYmd)) {
                nursingCareInsuranceInfo
                        .setKaigoStartDateGG(ReportUtil.nullToEmpty(startYmd.get(CommonConstants.INT_0)));
                nursingCareInsuranceInfo
                        .setKaigoStartDateYY(ReportUtil.nullToEmpty(startYmd.get(CommonConstants.INT_1)));
                nursingCareInsuranceInfo
                        .setKaigoStartDateMM(ReportUtil.nullToEmpty(startYmd.get(CommonConstants.INT_2)));
                nursingCareInsuranceInfo
                        .setKaigoStartDateDD(ReportUtil.nullToEmpty(startYmd.get(CommonConstants.INT_3)));
            } else {
                nursingCareInsuranceInfo.setKaigoStartDateGG(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setKaigoStartDateYY(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setKaigoStartDateMM(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setKaigoStartDateDD(CommonConstants.BLANK_STRING);
            }
            // 介護保険情報.有効期間・終了日
            List<String> endYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavKaigo.getEndYmd());
            if (CollectionUtils.isNotEmpty(endYmd)) {
                nursingCareInsuranceInfo.setKaigoEndDateGG(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_0)));
                nursingCareInsuranceInfo.setKaigoEndDateYY(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_1)));
                nursingCareInsuranceInfo.setKaigoEndDateMM(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_2)));
                nursingCareInsuranceInfo.setKaigoEndDateDD(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_3)));
            } else {
                nursingCareInsuranceInfo.setKaigoEndDateGG(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setKaigoEndDateYY(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setKaigoEndDateMM(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setKaigoEndDateDD(CommonConstants.BLANK_STRING);
            }
            // 介護保険情報.確認日)
            List<String> chkYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavKaigo.getChkYmd());
            if (CollectionUtils.isNotEmpty(chkYmd)) {
                nursingCareInsuranceInfo.setChkDateGG(ReportUtil.nullToEmpty(chkYmd.get(CommonConstants.INT_0)));
                nursingCareInsuranceInfo.setChkDateYY(ReportUtil.nullToEmpty(chkYmd.get(CommonConstants.INT_1)));
                nursingCareInsuranceInfo.setChkDateMM(ReportUtil.nullToEmpty(chkYmd.get(CommonConstants.INT_2)));
                nursingCareInsuranceInfo.setChkDateDD(ReportUtil.nullToEmpty(chkYmd.get(CommonConstants.INT_3)));
            } else {
                nursingCareInsuranceInfo.setChkDateGG(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setChkDateYY(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setChkDateMM(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setChkDateDD(CommonConstants.BLANK_STRING);
            }
            // 介護保険情報.資格取得日
            List<String> shikakuYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavKaigo.getShikakuYmd());
            if (CollectionUtils.isNotEmpty(shikakuYmd)) {
                nursingCareInsuranceInfo.setSikakuDateGG(ReportUtil.nullToEmpty(shikakuYmd.get(CommonConstants.INT_0)));
                nursingCareInsuranceInfo.setSikakuDateYY(ReportUtil.nullToEmpty(shikakuYmd.get(CommonConstants.INT_1)));
                nursingCareInsuranceInfo.setSikakuDateMM(ReportUtil.nullToEmpty(shikakuYmd.get(CommonConstants.INT_2)));
                nursingCareInsuranceInfo.setSikakuDateDD(ReportUtil.nullToEmpty(shikakuYmd.get(CommonConstants.INT_3)));
            } else {
                nursingCareInsuranceInfo.setSikakuDateGG(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setSikakuDateYY(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setSikakuDateMM(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setSikakuDateDD(CommonConstants.BLANK_STRING);
            }
            // 介護保険情報.給付率
            nursingCareInsuranceInfo.setRateKnj(ReportUtil.nullToEmpty(kghKrkFaceSavKaigo.getKyufuwari().toString()));
            // 介護保険情報.給付種類
            nursingCareInsuranceInfo.setRateTypeKnj(CommonConstants.BLANK_STRING);
            // 介護保険情報.保険者
            nursingCareInsuranceInfo
                    .setKhokenKnj(
                            ReportUtil.nullToEmpty(kghKrkFaceSavKaigo.getKHokenNo()) + CommonConstants.BLANK_STRING
                                    + ReportUtil.nullToEmpty(kghKrkFaceSavKaigo.getKHokenKnj()));
            // 介護保険情報.被保険者番号
            nursingCareInsuranceInfo.setHhokenNo(ReportUtil.nullToEmpty(kghKrkFaceSavKaigo.getHHokenNo()));

            // 介護保険情報.給付率 ＞０の場合
            if (CommonDtoUtil.strValToDouble(nursingCareInsuranceInfo.getRateKnj()) > CommonConstants.INT_0) {
                // 履歴データ情報を取得する
                KghKrkFreeFaceDataByCriteriaInEntity kghKrkFreeFaceDataParam = new KghKrkFreeFaceDataByCriteriaInEntity();
                kghKrkFreeFaceDataParam.setSavSeq(kghKrkFaceSavKaigo.getSavSeq());
                List<KghKrkFreeFaceDataOutEntity> kghKrkFreeFaceDataList = kghTucAssFace2SelectMapper
                        .findKghKrkFreeFaceDataByCriteria(kghKrkFreeFaceDataParam);

                // 上記取得の履歴データ情報.OUTPUT情報を（"koumoku_id = 18"）でフィルターする
                List<KghKrkFreeFaceDataOutEntity> filterList = kghKrkFreeFaceDataList.stream()
                        .filter(item -> item.getKoumokuId() == CommonConstants.INT_18).toList();

                // フィルター結果を（"item_name_knj = '給付率'"）で検索結果データが存在する場合
                Optional<KghKrkFreeFaceDataOutEntity> filterForRate = filterList.stream()
                        .filter(item -> CommonDtoUtil.checkStringEqual(item.getItemNameKnj(), ReportConstants.PAYOUT_RATE)).findFirst();
                if (filterForRate.isPresent()) {
                    // 履歴データ情報のOUTPUT情報.項目内容の値 ＞０の場合
                    if (CommonDtoUtil.strValToDouble(filterForRate.get().getItemNaiyouKnj()) > CommonConstants.INT_0) {
                        nursingCareInsuranceInfo
                                .setRateKnj(ReportUtil.nullToEmpty(filterForRate.get().getItemNaiyouKnj()));

                        // フィルター結果を（"item_name_knj = '給付種類'"）で検索結果データが存在する場合
                        Optional<KghKrkFreeFaceDataOutEntity> filterForRateType = filterList.stream()
                                .filter(item -> CommonDtoUtil.checkStringEqual(item.getItemNameKnj(), ReportConstants.PAYOUT_TYPE))
                                .findFirst();
                        if (filterForRateType.isPresent()) {
                            nursingCareInsuranceInfo
                                    .setRateTypeKnj(ReportUtil.nullToEmpty(filterForRateType.get().getItemNaiyouKnj()));
                        }
                    } else {
                        nursingCareInsuranceInfo.setRateKnj(CommonConstants.BLANK_STRING);
                    }
                }
            }
        } else {
            seqNo = CommonConstants.INT_0;
            // 介護保険情報.有効期間・開始日
            nursingCareInsuranceInfo.setKaigoStartDateGG(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setKaigoStartDateYY(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setKaigoStartDateMM(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setKaigoStartDateDD(CommonConstants.BLANK_STRING);
            // 介護保険情報.有効期間・終了日
            nursingCareInsuranceInfo.setKaigoEndDateGG(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setKaigoEndDateYY(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setKaigoEndDateMM(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setKaigoEndDateDD(CommonConstants.BLANK_STRING);
            // 介護保険情報.確認日
            nursingCareInsuranceInfo.setChkDateGG(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setChkDateYY(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setChkDateMM(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setChkDateDD(CommonConstants.BLANK_STRING);
            // 介護保険情報.資格取得日
            nursingCareInsuranceInfo.setSikakuDateGG(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setSikakuDateYY(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setSikakuDateMM(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setSikakuDateDD(CommonConstants.BLANK_STRING);
            // 介護保険情報.給付率
            nursingCareInsuranceInfo.setRateKnj(CommonConstants.BLANK_STRING);
            // 介護保険情報.給付種類
            nursingCareInsuranceInfo.setRateTypeKnj(CommonConstants.BLANK_STRING);
            // 介護保険情報.保険者
            nursingCareInsuranceInfo.setKhokenKnj(CommonConstants.BLANK_STRING);
            // 介護保険情報.被保険者番号
            nursingCareInsuranceInfo.setHhokenNo(CommonConstants.BLANK_STRING);

        }

        // 2. 介護認定情報編集
        if (seqNo > CommonConstants.INT_0 && CollectionUtils.isNotEmpty(kghKrkFaceSavNinteiList)) {
            // 連番 ＞０ 且つ 介護認定情報のデータが存在する場合
            KghKrkFaceSavNinteiGetOutEntity kghKrkFaceSavNintei = kghKrkFaceSavNinteiList.get(CommonConstants.INT_0);
            // 介護保険情報.認定区分名
            // 介護保険・認定区分＝0の場合
            if (kghKrkFaceSavNintei.getShinseiflg() == CommonConstants.INT_0) {
                nursingCareInsuranceInfo.setNinteiKbnLabel(ReportConstants.APPROVED_STRING);
            } else if (kghKrkFaceSavNintei.getShinseiflg() == CommonConstants.INT_1) {
                // 介護保険・認定区分＝1の場合
                nursingCareInsuranceInfo.setNinteiKbnLabel(ReportConstants.UNDER_REVIEW_STRING);
            } else {
                // 上記以外の場合
                nursingCareInsuranceInfo.setNinteiKbnLabel(CommonConstants.BLANK_STRING);
            }
            // 介護保険情報.認定日
            List<String> ninteYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavNintei.getNinteiYmd());
            if (CollectionUtils.isNotEmpty(ninteYmd)) {
                nursingCareInsuranceInfo.setNinteiDateGG(ReportUtil.nullToEmpty(ninteYmd.get(CommonConstants.INT_0)));
                nursingCareInsuranceInfo.setNinteiDateYY(ReportUtil.nullToEmpty(ninteYmd.get(CommonConstants.INT_1)));
                nursingCareInsuranceInfo.setNinteiDateMM(ReportUtil.nullToEmpty(ninteYmd.get(CommonConstants.INT_2)));
                nursingCareInsuranceInfo.setNinteiDateDD(ReportUtil.nullToEmpty(ninteYmd.get(CommonConstants.INT_3)));
            } else {
                nursingCareInsuranceInfo.setNinteiDateGG(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setNinteiDateYY(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setNinteiDateMM(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setNinteiDateDD(CommonConstants.BLANK_STRING);
            }
            // 介護保険情報.要介護度名
            // 介護保険情報.要介護度=1の場合
            if (kghKrkFaceSavNintei.getYokaiKbn() == CommonConstants.INT_1) {
                nursingCareInsuranceInfo.setYokaidoKnj(CommonConstants.YOUKI_KBN_KNJ_1);
            } else if (kghKrkFaceSavNintei.getYokaiKbn() == CommonConstants.INT_2) {
                // 介護保険情報.要介護度=2の場合
                nursingCareInsuranceInfo.setYokaidoKnj(CommonConstants.YOUKI_KBN_KNJ_9);
            } else if (kghKrkFaceSavNintei.getYokaiKbn() == CommonConstants.INT_3) {
                // 介護保険情報.要介護度=3の場合
                nursingCareInsuranceInfo.setYokaidoKnj(CommonConstants.YOUKI_KBN_KNJ_2);
            } else if (kghKrkFaceSavNintei.getYokaiKbn() == CommonConstants.INT_4) {
                // 介護保険情報.要介護度=4の場合
                nursingCareInsuranceInfo.setYokaidoKnj(CommonConstants.YOUKI_KBN_KNJ_5);
            } else if (kghKrkFaceSavNintei.getYokaiKbn() == CommonConstants.INT_5) {
                // 介護保険情報.要介護度=5の場合
                nursingCareInsuranceInfo.setYokaidoKnj(CommonConstants.YOUKI_KBN_KNJ_6);
            } else if (kghKrkFaceSavNintei.getYokaiKbn() == CommonConstants.INT_6) {
                // 介護保険情報.要介護度=6の場合
                nursingCareInsuranceInfo.setYokaidoKnj(CommonConstants.YOUKI_KBN_KNJ_7);
            } else if (kghKrkFaceSavNintei.getYokaiKbn() == CommonConstants.INT_7) {
                // 介護保険情報.要介護度=7の場合
                nursingCareInsuranceInfo.setYokaidoKnj(CommonConstants.YOUKI_KBN_KNJ_8);
            } else if (kghKrkFaceSavNintei.getYokaiKbn() == CommonConstants.INT_11) {
                // 介護保険情報.要介護度=7の場合
                nursingCareInsuranceInfo.setYokaidoKnj(CommonConstants.YOUKI_KBN_KNJ_2);
            } else if (kghKrkFaceSavNintei.getYokaiKbn() == CommonConstants.INT_12) {
                // 介護保険情報.要介護度=7の場合
                nursingCareInsuranceInfo.setYokaidoKnj(CommonConstants.YOUKI_KBN_KNJ_3);
            } else if (kghKrkFaceSavNintei.getYokaiKbn() == CommonConstants.INT_21) {
                // 介護保険情報.要介護度=7の場合
                nursingCareInsuranceInfo.setYokaidoKnj(CommonConstants.YOKAIKNJ_1);
            } else {
                // 上記以外の場合
                nursingCareInsuranceInfo.setYokaidoKnj(CommonConstants.BLANK_STRING);
            }
            // 介護保険情報.有効期間・開始日
            List<String> startYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavNintei.getStartYmd());
            if (CollectionUtils.isNotEmpty(startYmd)) {
                nursingCareInsuranceInfo
                        .setDispStartDateGG(ReportUtil.nullToEmpty(startYmd.get(CommonConstants.INT_0)));
                nursingCareInsuranceInfo
                        .setDispStartDateYY(ReportUtil.nullToEmpty(startYmd.get(CommonConstants.INT_1)));
                nursingCareInsuranceInfo
                        .setDispStartDateMM(ReportUtil.nullToEmpty(startYmd.get(CommonConstants.INT_2)));
                nursingCareInsuranceInfo
                        .setDispStartDateDD(ReportUtil.nullToEmpty(startYmd.get(CommonConstants.INT_3)));
            } else {
                nursingCareInsuranceInfo.setDispStartDateGG(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setDispStartDateYY(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setDispStartDateMM(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setDispStartDateDD(CommonConstants.BLANK_STRING);
            }
            // 介護保険情報.有効期間・終了日
            List<String> endYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavNintei.getEndYmd());
            if (CollectionUtils.isNotEmpty(endYmd)) {
                nursingCareInsuranceInfo.setDispEndDateGG(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_0)));
                nursingCareInsuranceInfo.setDispEndDateYY(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_1)));
                nursingCareInsuranceInfo.setDispEndDateMM(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_2)));
                nursingCareInsuranceInfo.setDispEndDateDD(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_3)));
            } else {
                nursingCareInsuranceInfo.setDispEndDateGG(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setDispEndDateYY(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setDispEndDateMM(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setDispEndDateDD(CommonConstants.BLANK_STRING);
            }
            // 介護保険情報.居宅サービス・開始日
            List<String> tuusyoStartYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavNintei.getTuusyoStartYmd());
            if (CollectionUtils.isNotEmpty(tuusyoStartYmd)) {
                nursingCareInsuranceInfo
                        .setTushoStartDateGG(ReportUtil.nullToEmpty(tuusyoStartYmd.get(CommonConstants.INT_0)));
                nursingCareInsuranceInfo
                        .setTushoStartDateYY(ReportUtil.nullToEmpty(tuusyoStartYmd.get(CommonConstants.INT_1)));
                nursingCareInsuranceInfo
                        .setTushoStartDateMM(ReportUtil.nullToEmpty(tuusyoStartYmd.get(CommonConstants.INT_2)));
                nursingCareInsuranceInfo
                        .setTushoStartDateDD(ReportUtil.nullToEmpty(tuusyoStartYmd.get(CommonConstants.INT_3)));
            } else {
                nursingCareInsuranceInfo.setTushoStartDateGG(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setTushoStartDateYY(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setTushoStartDateMM(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setTushoStartDateDD(CommonConstants.BLANK_STRING);
            }
            // 介護保険情報.居宅サービス・終了日
            List<String> tuusyoEndYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavNintei.getTuusyoEndYmd());
            if (CollectionUtils.isNotEmpty(tuusyoEndYmd)) {
                nursingCareInsuranceInfo
                        .setTushoEndDateGG(ReportUtil.nullToEmpty(tuusyoEndYmd.get(CommonConstants.INT_0)));
                nursingCareInsuranceInfo
                        .setTushoEndDateYY(ReportUtil.nullToEmpty(tuusyoEndYmd.get(CommonConstants.INT_1)));
                nursingCareInsuranceInfo
                        .setTushoEndDateMM(ReportUtil.nullToEmpty(tuusyoEndYmd.get(CommonConstants.INT_2)));
                nursingCareInsuranceInfo
                        .setTushoEndDateDD(ReportUtil.nullToEmpty(tuusyoEndYmd.get(CommonConstants.INT_3)));
            } else {
                nursingCareInsuranceInfo.setTushoEndDateGG(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setTushoEndDateYY(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setTushoEndDateMM(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setTushoEndDateDD(CommonConstants.BLANK_STRING);
            }
            // 介護保険情報.支給限度基準額
            nursingCareInsuranceInfo.setSikyuGendoGaku(ReportUtil.nullToEmpty(
                    formatAmount(kghKrkFaceSavNintei.getTusho1Gendo())));
            // 介護保険情報.メモ
            nursingCareInsuranceInfo.setKaigoMemoKnj(ReportUtil.nullToEmpty(kghKrkFaceSavNintei.getRyuuiKnj()));
            // 介護保険情報.メモフォント
            if (StringUtil.isNotEmpty(nursingCareInsuranceInfo.getKaigoMemoKnj())) {
                if (serviceType == CommonConstants.INT_1) {
                    // アセスメントフェースシート(TYPE1）の場合
                    if (ReportUtil.getByteLength(nursingCareInsuranceInfo.getKaigoMemoKnj()) > 240) {
                        nursingCareInsuranceInfo.setKaigoMemoKnjFont(ReportConstants.FONT_SIZE_10);
                    } else {
                        nursingCareInsuranceInfo.setKaigoMemoKnjFont(ReportConstants.FONT_SIZE_12);
                    }
                } else {
                    // アセスメントフェースシート(TYPE2）の場合
                    nursingCareInsuranceInfo.setKaigoMemoKnjFont(ReportConstants.FONT_SIZE_9);
                }
            }

            // 介護保険情報.交付年月日
            List<String> kouhuYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavNintei.getKouhuYmd());
            if (CollectionUtils.isNotEmpty(kouhuYmd)) {
                nursingCareInsuranceInfo.setKouhuDateGG(ReportUtil.nullToEmpty(kouhuYmd.get(CommonConstants.INT_0)));
                nursingCareInsuranceInfo.setKouhuDateYY(ReportUtil.nullToEmpty(kouhuYmd.get(CommonConstants.INT_1)));
                nursingCareInsuranceInfo.setKouhuDateMM(ReportUtil.nullToEmpty(kouhuYmd.get(CommonConstants.INT_2)));
                nursingCareInsuranceInfo.setKouhuDateDD(ReportUtil.nullToEmpty(kouhuYmd.get(CommonConstants.INT_3)));
            } else {
                nursingCareInsuranceInfo.setKouhuDateGG(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setKouhuDateYY(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setKouhuDateMM(CommonConstants.BLANK_STRING);
                nursingCareInsuranceInfo.setKouhuDateDD(CommonConstants.BLANK_STRING);
            }
            // 介護保険情報.支援事業者
            nursingCareInsuranceInfo.setSienjigyoName(ReportUtil.nullToEmpty(kghKrkFaceSavNintei.getSienjigyoName()));
            // 介護保険情報.サービス限定
            nursingCareInsuranceInfo.setServiceLimit(ReportUtil.nullToEmpty(kghKrkFaceSavNintei.getServiceLimit()));
        } else {
            // 上記以外の場合
            // 介護保険情報.認定区分名
            nursingCareInsuranceInfo.setNinteiKbnLabel(CommonConstants.BLANK_STRING);
            // 介護保険情報.認定日
            nursingCareInsuranceInfo.setNinteiDateGG(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setNinteiDateYY(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setNinteiDateMM(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setNinteiDateDD(CommonConstants.BLANK_STRING);
            // 介護保険情報.要介護度名
            nursingCareInsuranceInfo.setYokaidoKnj(CommonConstants.BLANK_STRING);
            // 介護保険情報.有効期間・開始日
            nursingCareInsuranceInfo.setDispStartDateGG(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setDispStartDateYY(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setDispStartDateMM(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setDispStartDateDD(CommonConstants.BLANK_STRING);
            // 介護保険情報.有効期間・終了日
            nursingCareInsuranceInfo.setDispEndDateGG(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setDispEndDateYY(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setDispEndDateMM(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setDispEndDateDD(CommonConstants.BLANK_STRING);
            // 介護保険情報.居宅サービス・開始日
            nursingCareInsuranceInfo.setTushoStartDateGG(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setTushoStartDateYY(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setTushoStartDateMM(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setTushoStartDateDD(CommonConstants.BLANK_STRING);
            // 介護保険情報.居宅サービス・終了日
            nursingCareInsuranceInfo.setTushoEndDateGG(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setTushoEndDateYY(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setTushoEndDateMM(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setTushoEndDateDD(CommonConstants.BLANK_STRING);
            // 介護保険情報.支給限度基準額
            nursingCareInsuranceInfo.setSikyuGendoGaku(CommonConstants.BLANK_STRING);
            // 介護保険情報.メモ
            nursingCareInsuranceInfo.setKaigoMemoKnj(CommonConstants.BLANK_STRING);
            // 介護保険情報.交付年月日
            nursingCareInsuranceInfo.setKouhuDateGG(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setKouhuDateYY(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setKouhuDateMM(CommonConstants.BLANK_STRING);
            nursingCareInsuranceInfo.setKouhuDateDD(CommonConstants.BLANK_STRING);
            // 介護保険情報.支援事業者
            nursingCareInsuranceInfo.setSienjigyoName(CommonConstants.BLANK_STRING);
            // 介護保険情報.サービス限定
            nursingCareInsuranceInfo.setServiceLimit(CommonConstants.BLANK_STRING);
        }

        // 3. 介護保険情報.メモ区分
        if (memoPrintFlg == CommonConstants.INT_0 || memoPrintFlg == CommonConstants.INT_1) {
            // メモ欄印刷フラグが0 また 1の場合
            nursingCareInsuranceInfo.setKaigoMemoKbn(memoKubun);
        } else if (memoPrintFlg == CommonConstants.INT_2) {
            // メモ欄印刷フラグが2の場合
            nursingCareInsuranceInfo.setKaigoMemoKbn(CommonConstants.INT_1);
        } else {
            // 以外の場合
            nursingCareInsuranceInfo.setKaigoMemoKbn(CommonConstants.INT_0);
        }

        // 結果を返し
        nursingCareInsuranceInfoList.add(nursingCareInsuranceInfo);
        return new JRBeanCollectionDataSource(nursingCareInsuranceInfoList);
    }

    /**
     * 老人保健情報の印刷データを編集する
     * 
     * @param history     リクエストパラメータ.データ.印刷対象履歴[0]
     * @param serviceType 帳票区分
     * @return 老人保健情報
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getElderlyHealthInfoParamters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history, Integer serviceType) throws Exception {

        List<ReportAssessmentFaceSheetTypeElderlyHealthInfo> elderlyHealthInfoList = new ArrayList<>();
        ReportAssessmentFaceSheetTypeElderlyHealthInfo elderlyHealthInfo = new ReportAssessmentFaceSheetTypeElderlyHealthInfo();

        // 保険情報（主保険情報、老人保健情報、公費情報）を取得する
        KghKrkFaceSavHokenGetByCriteriaInEntity kghKrkFaceSavHokenParam = new KghKrkFaceSavHokenGetByCriteriaInEntity();
        kghKrkFaceSavHokenParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavHokenGetOutEntity> kghKrkFaceSavHokenList = kghTucAssFaceHokenSelectMapper
                .findKghKrkFaceSavHokenGetByCriteria(kghKrkFaceSavHokenParam);

        /*
         * 1. 老人保健情報編集
         */
        List<KghKrkFaceSavHokenGetOutEntity> kghKrkFaceSavHokenFilterList = kghKrkFaceSavHokenList.stream()
                .filter(item -> item.getType() == CommonConstants.INT_4).toList();
        if (CollectionUtils.isNotEmpty(kghKrkFaceSavHokenFilterList)) {
            // (1). 保険情報を（"type = 4"）でフィルターしたのデータが存在する場合、下記編集処理を行う
            KghKrkFaceSavHokenGetOutEntity kghKrkFaceSavHoken = kghKrkFaceSavHokenFilterList.get(CommonConstants.INT_0);
            // 老人保健情報.保険区分
            // 老人保健情報.法制コード
            // 老人保健情報.保険名称
            elderlyHealthInfo.setHokenKnj(ReportUtil.nullToEmpty(kghKrkFaceSavHoken.getNameKnj()));
            // 老人保健情報.保険名称フォント
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                if (ReportUtil.getByteLength(elderlyHealthInfo.getHokenKnj()) < 33) {
                    // 老人保健情報.保険名称の桁数 < 33 の場合
                    elderlyHealthInfo.setHokenKnjFontSize(ReportConstants.FONT_SIZE_12);
                } else if (ReportUtil.getByteLength(elderlyHealthInfo.getHokenKnj()) >= 33
                        && ReportUtil.getByteLength(elderlyHealthInfo.getHokenKnj()) < 39) {
                    // 33 <＝ 老人保健情報.保険名称の桁数 <39 の場合の場合
                    elderlyHealthInfo.setHokenKnjFontSize(ReportConstants.FONT_SIZE_10);
                } else if (ReportUtil.getByteLength(elderlyHealthInfo.getHokenKnj()) >= 39
                        && ReportUtil.getByteLength(elderlyHealthInfo.getHokenKnj()) < 47) {
                    // 39 <＝ 老人保健情報.保険名称の桁数 <47 の場合
                    elderlyHealthInfo.setHokenKnjFontSize(ReportConstants.FONT_SIZE_8);
                } else {
                    // 上記以外の場合
                    elderlyHealthInfo.setHokenKnjFontSize(ReportConstants.FONT_SIZE_6);
                }
            } else {
                // アセスメントフェースシート(TYPE2）の場合
                elderlyHealthInfo.setHokenKnjFontSize(ReportConstants.FONT_SIZE_9);
            }

            // 老人保健情報.負担者番号
            elderlyHealthInfo.setKhokenNo(ReportUtil.nullToEmpty(kghKrkFaceSavHoken.getPublicno()));
            // 老人保健情報.受給者番号
            elderlyHealthInfo.setHhokenNo(ReportUtil.nullToEmpty(kghKrkFaceSavHoken.getJukyuNo()));
            // 老人保健情報.有効期間・開始日
            List<String> startDateYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavHoken.getStartdateYmd());
            if (CollectionUtils.isNotEmpty(startDateYmd)) {
                elderlyHealthInfo.setStartYmdGG(ReportUtil.nullToEmpty(startDateYmd.get(CommonConstants.INT_0)));
                elderlyHealthInfo.setStartYmdYY(ReportUtil.nullToEmpty(startDateYmd.get(CommonConstants.INT_1)));
                elderlyHealthInfo.setStartYmdMM(ReportUtil.nullToEmpty(startDateYmd.get(CommonConstants.INT_2)));
                elderlyHealthInfo.setStartYmdDD(ReportUtil.nullToEmpty(startDateYmd.get(CommonConstants.INT_3)));
            } else {
                elderlyHealthInfo.setStartYmdGG(CommonConstants.BLANK_STRING);
                elderlyHealthInfo.setStartYmdYY(CommonConstants.BLANK_STRING);
                elderlyHealthInfo.setStartYmdMM(CommonConstants.BLANK_STRING);
                elderlyHealthInfo.setStartYmdDD(CommonConstants.BLANK_STRING);
            }
            // 老人保健情報.有効期間・終了日
            List<String> endDateYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavHoken.getEnddateYmd());
            if (CollectionUtils.isNotEmpty(endDateYmd)) {
                elderlyHealthInfo.setEndYmdGG(ReportUtil.nullToEmpty(endDateYmd.get(CommonConstants.INT_0)));
                elderlyHealthInfo.setEndYmdYY(ReportUtil.nullToEmpty(endDateYmd.get(CommonConstants.INT_1)));
                elderlyHealthInfo.setEndYmdMM(ReportUtil.nullToEmpty(endDateYmd.get(CommonConstants.INT_2)));
                elderlyHealthInfo.setEndYmdDD(ReportUtil.nullToEmpty(endDateYmd.get(CommonConstants.INT_3)));
            } else {
                elderlyHealthInfo.setEndYmdGG(CommonConstants.BLANK_STRING);
                elderlyHealthInfo.setEndYmdYY(CommonConstants.BLANK_STRING);
                elderlyHealthInfo.setEndYmdMM(CommonConstants.BLANK_STRING);
                elderlyHealthInfo.setEndYmdDD(CommonConstants.BLANK_STRING);
            }
            // 老人保健情報.確認日
            List<String> chkYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavHoken.getCheckdateYmd());
            if (CollectionUtils.isNotEmpty(chkYmd)) {
                elderlyHealthInfo.setChkYmdGG(ReportUtil.nullToEmpty(chkYmd.get(CommonConstants.INT_0)));
                elderlyHealthInfo.setChkYmdYY(ReportUtil.nullToEmpty(chkYmd.get(CommonConstants.INT_1)));
                elderlyHealthInfo.setChkYmdMM(ReportUtil.nullToEmpty(chkYmd.get(CommonConstants.INT_2)));
                elderlyHealthInfo.setChkYmdDD(ReportUtil.nullToEmpty(chkYmd.get(CommonConstants.INT_3)));
            } else {
                elderlyHealthInfo.setChkYmdGG(CommonConstants.BLANK_STRING);
                elderlyHealthInfo.setChkYmdYY(CommonConstants.BLANK_STRING);
                elderlyHealthInfo.setChkYmdMM(CommonConstants.BLANK_STRING);
                elderlyHealthInfo.setChkYmdDD(CommonConstants.BLANK_STRING);
            }
            // 老人保健情報.外来負担金
            elderlyHealthInfo.setGfutanGaku(ReportUtil.nullToEmpty(formatAmount(kghKrkFaceSavHoken.getGaifutan())));
            // 老人保健情報.入院負担金
            elderlyHealthInfo.setNfutanGaku(ReportUtil.nullToEmpty(formatAmount(kghKrkFaceSavHoken.getNyufutan())));
            // 老人保健情報.申請区分名
            // 老人保健情報.申請区分=1の場合
            if (kghKrkFaceSavHoken.getSinsei() == CommonConstants.INT_1) {
                elderlyHealthInfo.setSinseiKbnKnj(ReportConstants.CONTINUATION_STRING);
            } else if (kghKrkFaceSavHoken.getSinsei() == CommonConstants.INT_2) {
                // 老人保健情報.申請区分=2の場合
                elderlyHealthInfo.setSinseiKbnKnj(ReportConstants.UNDER_REVIEW_STRING);
            } else {
                // 上記以外の場合
                elderlyHealthInfo.setSinseiKbnKnj(CommonConstants.BLANK_STRING);
            }
            // 老人保健情報.特別区分名
            // 老人保健情報.特別区分=1の場合
            if (kghKrkFaceSavHoken.getSpecial() == CommonConstants.INT_1) {
                elderlyHealthInfo.setTokubetuKbnKnj(ReportConstants.LINE_OF_DUTY_STRING);
            } else if (kghKrkFaceSavHoken.getSpecial() == CommonConstants.INT_2) {
                // 老人保健情報.特別区分=2の場合
                elderlyHealthInfo.setTokubetuKbnKnj(ReportConstants.WITHIN_THREE_MONTH_AFTER_DISEMBARKATION_STRING);
            } else if (kghKrkFaceSavHoken.getSpecial() == CommonConstants.INT_3) {
                // 老人保健情報.特別区分=3の場合
                elderlyHealthInfo.setTokubetuKbnKnj(ReportConstants.COMMUTING_INJURY_STRING);
            } else {
                // 上記以外の場合
                elderlyHealthInfo.setTokubetuKbnKnj(CommonConstants.BLANK_STRING);
            }
        } else {
            // (2). 上記以外の場合
            // 老人保健情報.保険名称
            elderlyHealthInfo.setHokenKnj(CommonConstants.BLANK_STRING);
            // 老人保健情報.負担者番号
            elderlyHealthInfo.setKhokenNo(CommonConstants.BLANK_STRING);
            // 老人保健情報.受給者番号
            elderlyHealthInfo.setHhokenNo(CommonConstants.BLANK_STRING);
            // 老人保健情報.有効期間・開始日
            elderlyHealthInfo.setStartYmdGG(CommonConstants.BLANK_STRING);
            elderlyHealthInfo.setStartYmdYY(CommonConstants.BLANK_STRING);
            elderlyHealthInfo.setStartYmdMM(CommonConstants.BLANK_STRING);
            elderlyHealthInfo.setStartYmdDD(CommonConstants.BLANK_STRING);
            // 老人保健情報.有効期間・終了日
            elderlyHealthInfo.setEndYmdGG(CommonConstants.BLANK_STRING);
            elderlyHealthInfo.setEndYmdYY(CommonConstants.BLANK_STRING);
            elderlyHealthInfo.setEndYmdMM(CommonConstants.BLANK_STRING);
            elderlyHealthInfo.setEndYmdDD(CommonConstants.BLANK_STRING);
            // 老人保健情報.確認日
            elderlyHealthInfo.setChkYmdGG(CommonConstants.BLANK_STRING);
            elderlyHealthInfo.setChkYmdYY(CommonConstants.BLANK_STRING);
            elderlyHealthInfo.setChkYmdMM(CommonConstants.BLANK_STRING);
            elderlyHealthInfo.setChkYmdDD(CommonConstants.BLANK_STRING);
            // 老人保健情報.外来負担金
            elderlyHealthInfo.setGfutanGaku(CommonConstants.BLANK_STRING);
            // 老人保健情報.入院負担金
            elderlyHealthInfo.setNfutanGaku(CommonConstants.BLANK_STRING);
            // 老人保健情報.申請区分名
            elderlyHealthInfo.setSinseiKbnKnj(CommonConstants.BLANK_STRING);
            // 老人保健情報.特別区分名
            elderlyHealthInfo.setTokubetuKbnKnj(CommonConstants.BLANK_STRING);
        }

        // 結果を返し
        elderlyHealthInfoList.add(elderlyHealthInfo);
        return new JRBeanCollectionDataSource(elderlyHealthInfoList);
    }

    /**
     * 公費保険情報の印刷データを編集する
     * 
     * @param history リクエストパラメータ.データ.印刷対象履歴[0]
     * @return 公費保険情報
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getPublicExpenditureInfoParamters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history) throws Exception {

        List<ReportAssessmentFaceSheetTypePublicExpenditureInfo> publicExpenditureInfoList = new ArrayList<>();
        ReportAssessmentFaceSheetTypePublicExpenditureInfo publicExpenditureInfo = new ReportAssessmentFaceSheetTypePublicExpenditureInfo();

        // 保険情報（主保険情報、老人保健情報、公費情報）を取得する
        KghKrkFaceSavHokenGetByCriteriaInEntity kghKrkFaceSavHokenParam = new KghKrkFaceSavHokenGetByCriteriaInEntity();
        kghKrkFaceSavHokenParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavHokenGetOutEntity> kghKrkFaceSavHokenList = kghTucAssFaceHokenSelectMapper
                .findKghKrkFaceSavHokenGetByCriteria(kghKrkFaceSavHokenParam);

        /*
         * 1. 公費保険情報編集
         */
        // (1). 保険情報を（"type = 3"）でフィルタ
        Optional<KghKrkFaceSavHokenGetOutEntity> kghKrkFaceSavHokenFilterList = kghKrkFaceSavHokenList.stream()
                .filter(item -> item.getType() == CommonConstants.INT_3).findFirst();
        if (kghKrkFaceSavHokenFilterList.isPresent()) {
            // データが存在する場合、下記編集処理を行う
            KghKrkFaceSavHokenGetOutEntity kghKrkFaceSavHoken = kghKrkFaceSavHokenFilterList.get();
            // 公費情報.保険名称
            if (CommonDtoUtil.checkStringEqual(kghKrkFaceSavHoken.getCode(), CommonConstants.STR_12)) {
                // 法制コード が12の場合
                if (kghKrkFaceSavHoken.getSeihoKbn() == CommonConstants.INT_1) {
                    // 生保単独併用区分が1の場合
                    publicExpenditureInfo.setHokenKnj(ReportConstants.HOKENKNJ_1);
                } else {
                    // 上記以外の場合
                    publicExpenditureInfo.setHokenKnj(ReportConstants.HOKENKNJ_2);
                }
            } else {
                // 上記以外の場合
                publicExpenditureInfo.setHokenKnj(ReportUtil.nullToEmpty(kghKrkFaceSavHoken.getNameKnj()));
            }
            // 公費情報.負担者番号
            publicExpenditureInfo.setKhokenNo(ReportUtil.nullToEmpty(kghKrkFaceSavHoken.getPublicno()));
            // 公費情報.受給者番号
            publicExpenditureInfo.setHhokenNo(ReportUtil.nullToEmpty(kghKrkFaceSavHoken.getJukyuNo()));
            // 公費情報.有効期間・開始日
            List<String> startDateYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavHoken.getStartdateYmd());
            if (CollectionUtils.isNotEmpty(startDateYmd)) {
                publicExpenditureInfo.setStartYmdGG(ReportUtil.nullToEmpty(startDateYmd.get(CommonConstants.INT_0)));
                publicExpenditureInfo.setStartYmdYY(ReportUtil.nullToEmpty(startDateYmd.get(CommonConstants.INT_1)));
                publicExpenditureInfo.setStartYmdMM(ReportUtil.nullToEmpty(startDateYmd.get(CommonConstants.INT_2)));
                publicExpenditureInfo.setStartYmdDD(ReportUtil.nullToEmpty(startDateYmd.get(CommonConstants.INT_3)));
            } else {
                publicExpenditureInfo.setStartYmdGG(CommonConstants.BLANK_STRING);
                publicExpenditureInfo.setStartYmdYY(CommonConstants.BLANK_STRING);
                publicExpenditureInfo.setStartYmdMM(CommonConstants.BLANK_STRING);
                publicExpenditureInfo.setStartYmdDD(CommonConstants.BLANK_STRING);
            }
            // 公費情報.有効期間・終了日
            List<String> endDateYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavHoken.getEnddateYmd());
            if (CollectionUtils.isNotEmpty(endDateYmd)) {
                publicExpenditureInfo.setEndYmdGG(ReportUtil.nullToEmpty(endDateYmd.get(CommonConstants.INT_0)));
                publicExpenditureInfo.setEndYmdYY(ReportUtil.nullToEmpty(endDateYmd.get(CommonConstants.INT_1)));
                publicExpenditureInfo.setEndYmdMM(ReportUtil.nullToEmpty(endDateYmd.get(CommonConstants.INT_2)));
                publicExpenditureInfo.setEndYmdDD(ReportUtil.nullToEmpty(endDateYmd.get(CommonConstants.INT_3)));
            } else {
                publicExpenditureInfo.setEndYmdGG(CommonConstants.BLANK_STRING);
                publicExpenditureInfo.setEndYmdYY(CommonConstants.BLANK_STRING);
                publicExpenditureInfo.setEndYmdMM(CommonConstants.BLANK_STRING);
                publicExpenditureInfo.setEndYmdDD(CommonConstants.BLANK_STRING);
            }
            // 公費情報.確認日
            List<String> chkDateYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavHoken.getCheckdateYmd());
            if (CollectionUtils.isNotEmpty(chkDateYmd)) {
                publicExpenditureInfo.setChkYmdGG(ReportUtil.nullToEmpty(chkDateYmd.get(CommonConstants.INT_0)));
                publicExpenditureInfo.setChkYmdYY(ReportUtil.nullToEmpty(chkDateYmd.get(CommonConstants.INT_1)));
                publicExpenditureInfo.setChkYmdMM(ReportUtil.nullToEmpty(chkDateYmd.get(CommonConstants.INT_2)));
                publicExpenditureInfo.setChkYmdDD(ReportUtil.nullToEmpty(chkDateYmd.get(CommonConstants.INT_3)));
            } else {
                publicExpenditureInfo.setChkYmdGG(CommonConstants.BLANK_STRING);
                publicExpenditureInfo.setChkYmdYY(CommonConstants.BLANK_STRING);
                publicExpenditureInfo.setChkYmdMM(CommonConstants.BLANK_STRING);
                publicExpenditureInfo.setChkYmdDD(CommonConstants.BLANK_STRING);
            }
            // 公費情報.給付割合
            publicExpenditureInfo.setRateKnj(ReportUtil.nullToEmpty(kghKrkFaceSavHoken.getTrate()));
            // 公費情報.外来負担金
            publicExpenditureInfo.setGfutanGaku(ReportUtil.nullToEmpty(formatAmount(kghKrkFaceSavHoken.getGaifutan())));
            // 公費情報.入院負担金
            publicExpenditureInfo.setNfutanGaku(ReportUtil.nullToEmpty(formatAmount(kghKrkFaceSavHoken.getNyufutan())));
            // 公費情報.申請区分名
            // 公費情報.申請区分=1の場合
            if (kghKrkFaceSavHoken.getSinsei() == CommonConstants.INT_1) {
                publicExpenditureInfo.setSinseiKbnKnj(ReportConstants.CONTINUATION_STRING);
            } else if (kghKrkFaceSavHoken.getSinsei() == CommonConstants.INT_2) {
                // 公費情報.申請区分=2の場合
                publicExpenditureInfo.setSinseiKbnKnj(ReportConstants.UNDER_REVIEW_STRING);
            } else {
                // 上記以外の場合
                publicExpenditureInfo.setSinseiKbnKnj(CommonConstants.BLANK_STRING);
            }
            // 公費情報.特別区分名
            // 公費情報.特別区分=1の場合
            if (kghKrkFaceSavHoken.getSpecial() == CommonConstants.INT_1) {
                publicExpenditureInfo.setTokubetuKbnKnj(ReportConstants.LINE_OF_DUTY_STRING);
            } else if (kghKrkFaceSavHoken.getSpecial() == CommonConstants.INT_2) {
                // 公費情報.特別区分=2の場合
                publicExpenditureInfo.setTokubetuKbnKnj(ReportConstants.WITHIN_THREE_MONTH_AFTER_DISEMBARKATION_STRING);
            } else if (kghKrkFaceSavHoken.getSpecial() == CommonConstants.INT_3) {
                // 公費情報.特別区分=3の場合
                publicExpenditureInfo.setTokubetuKbnKnj(ReportConstants.COMMUTING_INJURY_STRING);
            } else {
                // 上記以外の場合
                publicExpenditureInfo.setTokubetuKbnKnj(CommonConstants.BLANK_STRING);
            }
            // 公費情報.労働保険番号
            publicExpenditureInfo.setRsPublicNo(ReportUtil.nullToEmpty(kghKrkFaceSavHoken.getRsPublicNo()));
            // 公費情報.年金証書番号
            publicExpenditureInfo.setRsNenkinNo(ReportUtil.nullToEmpty(kghKrkFaceSavHoken.getRsNenkinNo()));
            // 公費情報.事業名称
            publicExpenditureInfo.setRsJigyoNameKnj(ReportUtil.nullToEmpty(kghKrkFaceSavHoken.getRsJigyoNameKnj()));
            // 公費情報.事業場所在地
            publicExpenditureInfo
                    .setRsJigyoAddressKnj(ReportUtil.nullToEmpty(kghKrkFaceSavHoken.getRsJigyoAddressKnj()));

        } else {
            // (2). 上記以外の場合
            // 公費情報.保険名称
            publicExpenditureInfo.setHokenKnj(CommonConstants.BLANK_STRING);
            // 公費情報.負担者番号
            publicExpenditureInfo.setKhokenNo(CommonConstants.BLANK_STRING);
            // 公費情報.受給者番号
            publicExpenditureInfo.setHhokenNo(CommonConstants.BLANK_STRING);
            // 公費情報.有効期間・開始日
            publicExpenditureInfo.setStartYmdGG(CommonConstants.BLANK_STRING);
            publicExpenditureInfo.setStartYmdYY(CommonConstants.BLANK_STRING);
            publicExpenditureInfo.setStartYmdMM(CommonConstants.BLANK_STRING);
            publicExpenditureInfo.setStartYmdDD(CommonConstants.BLANK_STRING);
            // 公費情報.有効期間・終了日
            publicExpenditureInfo.setEndYmdGG(CommonConstants.BLANK_STRING);
            publicExpenditureInfo.setEndYmdYY(CommonConstants.BLANK_STRING);
            publicExpenditureInfo.setEndYmdMM(CommonConstants.BLANK_STRING);
            publicExpenditureInfo.setEndYmdDD(CommonConstants.BLANK_STRING);
            // 公費情報.確認日
            publicExpenditureInfo.setChkYmdGG(CommonConstants.BLANK_STRING);
            publicExpenditureInfo.setChkYmdYY(CommonConstants.BLANK_STRING);
            publicExpenditureInfo.setChkYmdMM(CommonConstants.BLANK_STRING);
            publicExpenditureInfo.setChkYmdDD(CommonConstants.BLANK_STRING);
            // 公費情報.給付割合
            publicExpenditureInfo.setRateKnj(CommonConstants.BLANK_STRING);
            // 公費情報.外来負担金
            publicExpenditureInfo.setGfutanGaku(CommonConstants.BLANK_STRING);
            // 公費情報.入院負担金
            publicExpenditureInfo.setNfutanGaku(CommonConstants.BLANK_STRING);
            // 公費情報.申請区分名
            publicExpenditureInfo.setSinseiKbnKnj(CommonConstants.BLANK_STRING);
            // 公費情報.特別区分名
            publicExpenditureInfo.setTokubetuKbnKnj(CommonConstants.BLANK_STRING);
            // 公費情報.労働保険番号
            publicExpenditureInfo.setRsPublicNo(CommonConstants.BLANK_STRING);
            // 公費情報.年金証書番号
            publicExpenditureInfo.setRsNenkinNo(CommonConstants.BLANK_STRING);
            // 公費情報.事業名称
            publicExpenditureInfo.setRsJigyoNameKnj(CommonConstants.BLANK_STRING);
            // 公費情報.事業場所在地
            publicExpenditureInfo.setRsJigyoAddressKnj(CommonConstants.BLANK_STRING);
        }

        // 結果を返し
        publicExpenditureInfoList.add(publicExpenditureInfo);
        return new JRBeanCollectionDataSource(publicExpenditureInfoList);
    }

    /**
     * 障害者手帳の印刷データを編集する
     * 
     * @param history      リクエストパラメータ.データ.印刷対象履歴[0]
     * @param memoPrintFlg メモ欄印刷フラグ
     * @param memoKubun    メモ区分
     * @param serviceType  帳票区分
     * @return 障害者手帳の印刷データ
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getBodyHandycapNotebookParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history, Integer memoPrintFlg, Integer memoKubun,
            Integer serviceType)
            throws Exception {

        List<ReportAssessmentFaceSheetTypeBodyHandycapNotebook> bodyHandycapNotebookList = new ArrayList<>();
        ReportAssessmentFaceSheetTypeBodyHandycapNotebook bodyHandycapNotebook = new ReportAssessmentFaceSheetTypeBodyHandycapNotebook();

        // 身体障害手帳情報を取得する
        KghKrkFaceSavTecSiGetByCriteriaInEntity kghKrkFaceSavTecSiParam = new KghKrkFaceSavTecSiGetByCriteriaInEntity();
        kghKrkFaceSavTecSiParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavTecSiGetOutEntity> kghKrkFaceSavTecSiList = kghTucAssFaceTecSiSelectMapper
                .findKghKrkFaceSavTecSiGetByCriteria(kghKrkFaceSavTecSiParam);

        /*
         * 1. 身体障害者手帳編集
         */
        // (1). 障害者手帳情報のデータが存在する場合、下記編集処理を行う
        if (CollectionUtils.isNotEmpty(kghKrkFaceSavTecSiList)) {
            KghKrkFaceSavTecSiGetOutEntity kghKrkFaceSavTecSi = kghKrkFaceSavTecSiList.get(CommonConstants.INT_0);
            // 身障手帳.交付年月日
            List<String> koufuYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavTecSi.getGetYmd());
            if (CollectionUtils.isNotEmpty(koufuYmd)) {
                bodyHandycapNotebook.setKoufuYmdGG(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_0)));
                bodyHandycapNotebook.setKoufuYmdYY(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_1)));
                bodyHandycapNotebook.setKoufuYmdMM(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_2)));
                bodyHandycapNotebook.setKoufuYmdDD(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_3)));
            } else {
                bodyHandycapNotebook.setKoufuYmdGG(CommonConstants.BLANK_STRING);
                bodyHandycapNotebook.setKoufuYmdYY(CommonConstants.BLANK_STRING);
                bodyHandycapNotebook.setKoufuYmdMM(CommonConstants.BLANK_STRING);
                bodyHandycapNotebook.setKoufuYmdDD(CommonConstants.BLANK_STRING);
            }
            // 身障手帳.手帳番号
            bodyHandycapNotebook.setTechouBango(ReportUtil.nullToEmpty(kghKrkFaceSavTecSi.getTBango()));
            // 身障手帳.種別
            bodyHandycapNotebook.setShubetuKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecSi.getTKindKnj()));
            // 身障手帳.等級
            bodyHandycapNotebook.setToukyuuKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecSi.getTTokyu()));
            // 身障手帳.補助具等
            bodyHandycapNotebook.setHojoKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecSi.getHojoKnj()));
            // 身障手帳.メモ
            bodyHandycapNotebook.setMemoKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecSi.getBikoKnj()));

            // 障害状況編集
            String shougaiKnjResult = CommonConstants.BLANK_STRING;
            shougaiKnjResult = processFullShougai(shougaiKnjResult, kghKrkFaceSavTecSi.getToukyuu1Knj(),
                    kghKrkFaceSavTecSi.getKansa1Knj(), kghKrkFaceSavTecSi.getT1KbnKnj());
            shougaiKnjResult = processFullShougai(shougaiKnjResult, kghKrkFaceSavTecSi.getToukyuu2Knj(),
                    kghKrkFaceSavTecSi.getKansa2Knj(), kghKrkFaceSavTecSi.getT2KbnKnj());
            shougaiKnjResult = processFullShougai(shougaiKnjResult, kghKrkFaceSavTecSi.getToukyuu3Knj(),
                    kghKrkFaceSavTecSi.getKansa3Knj(), kghKrkFaceSavTecSi.getT3KbnKnj());
            shougaiKnjResult = processFullShougai(shougaiKnjResult, kghKrkFaceSavTecSi.getToukyuu4Knj(),
                    kghKrkFaceSavTecSi.getKansa4Knj(), kghKrkFaceSavTecSi.getT4KbnKnj());
            shougaiKnjResult = processFullShougai(shougaiKnjResult, kghKrkFaceSavTecSi.getToukyuu5Knj(),
                    kghKrkFaceSavTecSi.getKansa5Knj(), kghKrkFaceSavTecSi.getT5KbnKnj());
            shougaiKnjResult = processFullShougai(shougaiKnjResult, kghKrkFaceSavTecSi.getToukyuu6Knj(),
                    kghKrkFaceSavTecSi.getKansa6Knj(), kghKrkFaceSavTecSi.getT6KbnKnj());
            shougaiKnjResult = processFullShougai(shougaiKnjResult, kghKrkFaceSavTecSi.getToukyuu7Knj(),
                    kghKrkFaceSavTecSi.getKansa7Knj(), kghKrkFaceSavTecSi.getT7KbnKnj());
            shougaiKnjResult = processFullShougai(shougaiKnjResult, kghKrkFaceSavTecSi.getToukyuu8Knj(),
                    kghKrkFaceSavTecSi.getKansa8Knj(), kghKrkFaceSavTecSi.getT8KbnKnj());
            shougaiKnjResult = processFullShougai(shougaiKnjResult, kghKrkFaceSavTecSi.getToukyuu9Knj(),
                    kghKrkFaceSavTecSi.getKansa9Knj(), kghKrkFaceSavTecSi.getT9KbnKnj());
            shougaiKnjResult = processFullShougai(shougaiKnjResult, kghKrkFaceSavTecSi.getToukyuu10Knj(),
                    kghKrkFaceSavTecSi.getKansa10Knj(), kghKrkFaceSavTecSi.getT10KbnKnj());

            shougaiKnjResult = shougaiKnjResult.substring(CommonConstants.INT_0,
                    ReportUtil.getByteLength(shougaiKnjResult) - CommonConstants.INT_2);
            bodyHandycapNotebook.setShougaiKnj(shougaiKnjResult);

            // 障害状況フォント
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                if (ReportUtil.getByteLength(shougaiKnjResult) <= 140) {
                    // 身障手帳.障害状況の桁数<=140の場合
                    bodyHandycapNotebook.setShougaiKnjFontSize("11.5");
                } else if (ReportUtil.getByteLength(shougaiKnjResult) > 140
                        && ReportUtil.getByteLength(shougaiKnjResult) <= 246) {
                    // 140< 身障手帳.障害状況の桁数<=246の場合の場合
                    bodyHandycapNotebook.setShougaiKnjFontSize("9.3");
                } else if (ReportUtil.getByteLength(shougaiKnjResult) > 246
                        && ReportUtil.getByteLength(shougaiKnjResult) <= 376) {
                    // 246< 身障手帳.障害状況の桁数<=376の場合
                    bodyHandycapNotebook.setShougaiKnjFontSize("7.7");
                } else {
                    // 上記以外の場合
                    bodyHandycapNotebook.setShougaiKnjFontSize(ReportConstants.FONT_SIZE_6);
                }
            } else {
                // アセスメントフェースシート(TYPE2）の場合
                if (ReportUtil.getByteLength(shougaiKnjResult) > 94) {
                    bodyHandycapNotebook.setShougaiKnjFontSize(ReportConstants.FONT_SIZE_6);
                } else {
                    bodyHandycapNotebook.setShougaiKnjFontSize(ReportConstants.FONT_SIZE_9);
                }
            }

            // // 身障手帳.メモ区分
            if (memoPrintFlg == CommonConstants.INT_0 || memoPrintFlg == CommonConstants.INT_1) {
                // メモ欄印刷フラグが0また1の場合
                bodyHandycapNotebook.setMemoKbn(memoKubun);
            } else if (memoPrintFlg == CommonConstants.INT_2) {
                // メモ欄印刷フラグが2の場合
                if ((StringUtil.isNotEmpty(kghKrkFaceSavTecSi.getHojoKnj()))
                        || (StringUtil.isNotEmpty(kghKrkFaceSavTecSi.getBikoKnj()))) {
                    bodyHandycapNotebook.setMemoKbn(CommonConstants.INT_1);
                } else {
                    // 以外の場合
                    bodyHandycapNotebook.setMemoKbn(CommonConstants.INT_0);
                }
            }

        } else {
            // (2). 上記以外の場合
            // 身障手帳.交付年月日
            bodyHandycapNotebook.setKoufuYmdGG(CommonConstants.BLANK_STRING);
            bodyHandycapNotebook.setKoufuYmdYY(CommonConstants.BLANK_STRING);
            bodyHandycapNotebook.setKoufuYmdMM(CommonConstants.BLANK_STRING);
            bodyHandycapNotebook.setKoufuYmdDD(CommonConstants.BLANK_STRING);
            // 身障手帳.手帳番号
            bodyHandycapNotebook.setTechouBango(CommonConstants.BLANK_STRING);
            // 身障手帳.種別
            bodyHandycapNotebook.setShubetuKnj(CommonConstants.BLANK_STRING);
            // 身障手帳.等級
            bodyHandycapNotebook.setToukyuuKnj(CommonConstants.BLANK_STRING);
            // 身障手帳.補助具等
            bodyHandycapNotebook.setHojoKnj(CommonConstants.BLANK_STRING);
            // 身障手帳.障害状況
            bodyHandycapNotebook.setShougaiKnj(CommonConstants.BLANK_STRING);
            // 身障手帳.メモ
            bodyHandycapNotebook.setMemoKnj(CommonConstants.BLANK_STRING);
            // 身障手帳.メモ区分
            bodyHandycapNotebook.setMemoKbn(memoKubun);
        }

        bodyHandycapNotebookList.add(bodyHandycapNotebook);
        return new JRBeanCollectionDataSource(bodyHandycapNotebookList);
    }

    /**
     * 障害状況処理
     * 
     * @param shougaiKnjResult 処理後の障害状況
     * @param toukyuuKnj       等級コード
     * @param kansaKnj         監査障害区分ID
     * @param tKbnKnj          障害者手帳区分
     * @return 処理後の障害状況
     */
    private String processFullShougai(String shougaiKnjResult, String toukyuuKnj, String kansaKnj, String tKbnKnj) {
        if (StringUtil.isNotEmpty(toukyuuKnj)) {
            shougaiKnjResult += kansaKnj + CommonConstants.BLANK_STRING + tKbnKnj + CommonConstants.LEFT_PARENTHESIS
                    + toukyuuKnj + CommonConstants.RIGHT_PARENTHESIS + ",";
        }
        return shougaiKnjResult;
    }

    /**
     * アセスメントフェースシート履歴精神者手帳の印刷データを編集する
     * 
     * @param history      リクエストパラメータ.データ.印刷対象履歴[0]
     * @param memoPrintFlg メモ欄印刷フラグ
     * @param memoKubun    メモ区分
     * @param serviceType  帳票区分
     * @return 精神者手帳の印刷データ
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getMentalNotebookParamters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history, Integer memoPrintFlg, Integer memoKubun,
            Integer serviceType)
            throws Exception {

        List<ReportAssessmentFaceSheetTypeMentalNotebook> mentalNotebookList = new ArrayList<>();
        ReportAssessmentFaceSheetTypeMentalNotebook mentalNotebook = new ReportAssessmentFaceSheetTypeMentalNotebook();

        // 精神者手帳情報を取得する
        KghKrkFaceSavTecSeGetByCriteriaInEntity kghKrkFaceSavTecSeGetParam = new KghKrkFaceSavTecSeGetByCriteriaInEntity();
        kghKrkFaceSavTecSeGetParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavTecSeGetOutEntity> kghKrkFaceSavTecSeGetList = kghTucAssFaceTecSeSelectMapper
                .findKghKrkFaceSavTecSeGetByCriteria(kghKrkFaceSavTecSeGetParam);

        /*
         * 精神手帳編集
         */
        if (CollectionUtils.isNotEmpty(kghKrkFaceSavTecSeGetList)) {
            // (1). 精神者手帳情報のデータが存在する場合、下記編集処理を行う
            KghKrkFaceSavTecSeGetOutEntity kghKrkFaceSavTecSeGet = kghKrkFaceSavTecSeGetList.get(CommonConstants.INT_0);
            // 交付年月日
            List<String> koufuYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavTecSeGet.getGetYmd());
            if (CollectionUtils.isNotEmpty(koufuYmd)) {
                mentalNotebook.setKoufuYmdGG(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_0)));
                mentalNotebook.setKoufuYmdYY(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_1)));
                mentalNotebook.setKoufuYmdMM(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_2)));
                mentalNotebook.setKoufuYmdDD(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_3)));
            } else {
                mentalNotebook.setKoufuYmdGG(CommonConstants.BLANK_STRING);
                mentalNotebook.setKoufuYmdYY(CommonConstants.BLANK_STRING);
                mentalNotebook.setKoufuYmdMM(CommonConstants.BLANK_STRING);
                mentalNotebook.setKoufuYmdDD(CommonConstants.BLANK_STRING);
            }
            // 有効期間開始日
            List<String> satrtYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavTecSeGet.getStartYmd());
            if (CollectionUtils.isNotEmpty(satrtYmd)) {
                mentalNotebook.setStartYmdGG(ReportUtil.nullToEmpty(satrtYmd.get(CommonConstants.INT_0)));
                mentalNotebook.setStartYmdYY(ReportUtil.nullToEmpty(satrtYmd.get(CommonConstants.INT_1)));
                mentalNotebook.setStartYmdMM(ReportUtil.nullToEmpty(satrtYmd.get(CommonConstants.INT_2)));
                mentalNotebook.setStartYmdDD(ReportUtil.nullToEmpty(satrtYmd.get(CommonConstants.INT_3)));
            } else {
                mentalNotebook.setStartYmdGG(CommonConstants.BLANK_STRING);
                mentalNotebook.setStartYmdYY(CommonConstants.BLANK_STRING);
                mentalNotebook.setStartYmdMM(CommonConstants.BLANK_STRING);
                mentalNotebook.setStartYmdDD(CommonConstants.BLANK_STRING);
            }
            // 有効期間終了日
            List<String> endYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavTecSeGet.getEndYmd());
            if (CollectionUtils.isNotEmpty(endYmd)) {
                mentalNotebook.setEndYmdGG(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_0)));
                mentalNotebook.setEndYmdYY(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_1)));
                mentalNotebook.setEndYmdMM(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_2)));
                mentalNotebook.setEndYmdDD(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_3)));
            } else {
                mentalNotebook.setEndYmdGG(CommonConstants.BLANK_STRING);
                mentalNotebook.setEndYmdYY(CommonConstants.BLANK_STRING);
                mentalNotebook.setEndYmdMM(CommonConstants.BLANK_STRING);
                mentalNotebook.setEndYmdDD(CommonConstants.BLANK_STRING);
            }
            // 手帳番号
            mentalNotebook.setTechouBango(ReportUtil.nullToEmpty(kghKrkFaceSavTecSeGet.getTBango()));
            // 通院医療費受給者番号
            mentalNotebook.setTuuinTecyouBango(ReportUtil.nullToEmpty(kghKrkFaceSavTecSeGet.getTuuinTecyouBango()));
            // 精神等級名
            mentalNotebook.setToukyuuKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecSeGet.getToukyuuKnj()));
            // メモ
            mentalNotebook.setMemoKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecSeGet.getBikoKnj()));
            // メモフォント
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                if (ReportUtil.getByteLength(mentalNotebook.getMemoKnj()) > 240) {
                    mentalNotebook.setMemoKnjFontSize(ReportConstants.FONT_SIZE_10);
                } else {
                    mentalNotebook.setMemoKnjFontSize(ReportConstants.FONT_SIZE_12);
                }
            } else {
                // アセスメントフェースシート(TYPE2）の場合
                if (ReportUtil.getByteLength(mentalNotebook.getMemoKnj()) <= 90) {
                    mentalNotebook.setMemoKnjFontSize(ReportConstants.FONT_SIZE_10);
                } else if (ReportUtil.getByteLength(mentalNotebook.getMemoKnj()) > 90
                        && ReportUtil.getByteLength(mentalNotebook.getMemoKnj()) <= 106) {
                    mentalNotebook.setMemoKnjFontSize(ReportConstants.FONT_SIZE_8);
                } else {
                    mentalNotebook.setMemoKnjFontSize(ReportConstants.FONT_SIZE_6);
                }

            }
            // メモ区分
            if (memoPrintFlg == CommonConstants.INT_0 || memoPrintFlg == CommonConstants.INT_1) {
                mentalNotebook.setMemoKbn(memoKubun);
            } else if (memoPrintFlg == CommonConstants.INT_2) {
                if (StringUtil.isNotEmpty(kghKrkFaceSavTecSeGet.getBikoKnj())) {
                    mentalNotebook.setMemoKbn(CommonConstants.INT_1);
                } else {
                    mentalNotebook.setMemoKbn(CommonConstants.INT_0);
                }
            }
        } else {
            // (2). 上記以外の場合
            // 交付年月日
            mentalNotebook.setKoufuYmdGG(CommonConstants.BLANK_STRING);
            mentalNotebook.setKoufuYmdYY(CommonConstants.BLANK_STRING);
            mentalNotebook.setKoufuYmdMM(CommonConstants.BLANK_STRING);
            mentalNotebook.setKoufuYmdDD(CommonConstants.BLANK_STRING);
            // 有効期間開始日
            mentalNotebook.setStartYmdGG(CommonConstants.BLANK_STRING);
            mentalNotebook.setStartYmdYY(CommonConstants.BLANK_STRING);
            mentalNotebook.setStartYmdMM(CommonConstants.BLANK_STRING);
            mentalNotebook.setStartYmdDD(CommonConstants.BLANK_STRING);
            // 有効期間終了日
            mentalNotebook.setEndYmdGG(CommonConstants.BLANK_STRING);
            mentalNotebook.setEndYmdYY(CommonConstants.BLANK_STRING);
            mentalNotebook.setEndYmdMM(CommonConstants.BLANK_STRING);
            mentalNotebook.setEndYmdDD(CommonConstants.BLANK_STRING);
            // 手帳番号
            mentalNotebook.setTechouBango(CommonConstants.BLANK_STRING);
            // 通院医療費受給者番号
            mentalNotebook.setTuuinTecyouBango(CommonConstants.BLANK_STRING);
            // 精神等級名
            mentalNotebook.setToukyuuKnj(CommonConstants.BLANK_STRING);
            // メモ
            mentalNotebook.setMemoKnj(CommonConstants.BLANK_STRING);
            // メモ区分
            mentalNotebook.setMemoKbn(memoKubun);
        }

        // 結果を返し
        mentalNotebookList.add(mentalNotebook);
        return new JRBeanCollectionDataSource(mentalNotebookList);
    }

    /**
     * アセスメントフェースシート履歴原爆者手帳の印刷データを編集する
     * 
     * @param history      リクエストパラメータ.データ.印刷対象履歴[0]
     * @param memoPrintFlg メモ欄印刷フラグ
     * @param memoKubun    メモ区分
     * @param serviceType  帳票区分
     * @return 原爆者手帳の印刷データ
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getAtomicBombNotebookParamters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history, Integer memoPrintFlg, Integer memoKubun,
            Integer serviceType)
            throws Exception {

        List<ReportAssessmentFaceSheetTypeAtomicBombNotebook> atomicBombNotebookList = new ArrayList<>();
        ReportAssessmentFaceSheetTypeAtomicBombNotebook atomicBombNotebook = new ReportAssessmentFaceSheetTypeAtomicBombNotebook();

        // 原爆者手帳情報を取得する
        KghKrkFaceSavTecGeGetByCriteriaInEntity kghKrkFaceSavTecGeGetParam = new KghKrkFaceSavTecGeGetByCriteriaInEntity();
        kghKrkFaceSavTecGeGetParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavTecGeGetOutEntity> kghKrkFaceSavTecGeGetList = kghTucAssFaceTecGeSelectMapper
                .findKghKrkFaceSavTecGeGetByCriteria(kghKrkFaceSavTecGeGetParam);

        /*
         * 1. 原爆手帳編集
         */
        if (CollectionUtils.isNotEmpty(kghKrkFaceSavTecGeGetList)) {
            // (1). 原爆者手帳情報のデータが存在する場合、下記編集処理を行う
            KghKrkFaceSavTecGeGetOutEntity kghKrkFaceSavTecGeGet = kghKrkFaceSavTecGeGetList.get(CommonConstants.INT_0);
            // 原爆手帳.受給者番号
            atomicBombNotebook.setTechouBango(ReportUtil.nullToEmpty(kghKrkFaceSavTecGeGet.getTBango()));
            // 原爆手帳.公費負担者番号
            atomicBombNotebook
                    .setKouhiFutanshaBango(ReportUtil.nullToEmpty(kghKrkFaceSavTecGeGet.getKouhiFutanshaBango()));
            // 原爆手帳.交付年月日
            List<String> koufuYmd = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavTecGeGet.getGetYmd());
            if (CollectionUtils.isNotEmpty(koufuYmd)) {
                atomicBombNotebook.setKoufuYmdGG(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_0)));
                atomicBombNotebook.setKoufuYmdYY(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_1)));
                atomicBombNotebook.setKoufuYmdMM(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_2)));
                atomicBombNotebook.setKoufuYmdDD(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_3)));
            } else {
                atomicBombNotebook.setKoufuYmdGG(CommonConstants.BLANK_STRING);
                atomicBombNotebook.setKoufuYmdYY(CommonConstants.BLANK_STRING);
                atomicBombNotebook.setKoufuYmdMM(CommonConstants.BLANK_STRING);
                atomicBombNotebook.setKoufuYmdDD(CommonConstants.BLANK_STRING);
            }
            // 原爆手帳.法第１条による区分名
            atomicBombNotebook.setHibakushaCdKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecGeGet.getHibakushakbnKnj()));
            // 原爆手帳.被爆時の年齢
            atomicBombNotebook.setHibakuAge(ReportUtil.nullToEmpty(kghKrkFaceSavTecGeGet.getHibakuAge()));
            // 原爆手帳.被爆の場所
            atomicBombNotebook.setPlaceKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecGeGet.getPlaceKnj()));
            // 被爆の場所フォント
            if (serviceType == CommonConstants.INT_2) {
                // アセスメントフェースシート(TYPE2）の場合
                if (ReportUtil.getByteLength(atomicBombNotebook.getPlaceKnj()) > 76) {
                    atomicBombNotebook.setPlaceKnjFont(ReportConstants.FONT_SIZE_6);
                } else {
                    atomicBombNotebook.setPlaceKnjFont(ReportConstants.FONT_SIZE_9);
                }
            }
            // 原爆手帳.被爆直後の行動
            atomicBombNotebook.setMoveKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecGeGet.getMoveKnj()));
            // 被爆直後の行動フォント
            if (serviceType == CommonConstants.INT_2) {
                // アセスメントフェースシート(TYPE2）の場合
                if (ReportUtil.getByteLength(atomicBombNotebook.getMoveKnj()) > 76) {
                    atomicBombNotebook.setMoveKnjFont(ReportConstants.FONT_SIZE_6);
                } else {
                    atomicBombNotebook.setMoveKnjFont(ReportConstants.FONT_SIZE_9);
                }
            }
            // 原爆手帳.被爆当時の外傷・熱傷の状況
            atomicBombNotebook.setJyoukyouKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecGeGet.getJyoukyouKnj()));
            // 被爆当時の外傷・熱傷の状況フォント
            if (serviceType == CommonConstants.INT_2) {
                // アセスメントフェースシート(TYPE2）の場合
                if (ReportUtil.getByteLength(atomicBombNotebook.getJyoukyouKnj()) > 76) {
                    atomicBombNotebook.setJyoukyouKnjFont(ReportConstants.FONT_SIZE_6);
                } else {
                    atomicBombNotebook.setJyoukyouKnjFont(ReportConstants.FONT_SIZE_9);
                }
            }
            // 原爆手帳.被爆当時の急性症状
            atomicBombNotebook.setShoujyouKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecGeGet.getShoujyouKnj()));
            // 被爆当時の急性症状フォント
            if (serviceType == CommonConstants.INT_2) {
                // アセスメントフェースシート(TYPE2）の場合
                if (ReportUtil.getByteLength(atomicBombNotebook.getShoujyouKnj()) > 76) {
                    atomicBombNotebook.setShoujyouKnjFont(ReportConstants.FONT_SIZE_6);
                } else {
                    atomicBombNotebook.setShoujyouKnjFont(ReportConstants.FONT_SIZE_9);
                }
            }
            // 原爆手帳.傷病名
            atomicBombNotebook.setByoumeiKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecGeGet.getByoumeiKnj()));
            // 傷病名フォント
            if (serviceType == CommonConstants.INT_2) {
                // アセスメントフェースシート(TYPE2）の場合
                if (ReportUtil.getByteLength(atomicBombNotebook.getByoumeiKnj()) > 76) {
                    atomicBombNotebook.setByoumeiKnjFont(ReportConstants.FONT_SIZE_6);
                } else {
                    atomicBombNotebook.setByoumeiKnjFont(ReportConstants.FONT_SIZE_9);
                }
            }
            // 原爆手帳.メモ
            atomicBombNotebook.setMemoKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecGeGet.getBikoKnj()));
            // 原爆手帳.メモフォント
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                if (ReportUtil.getByteLength(atomicBombNotebook.getMemoKnj()) > 240) {
                    // 原爆手帳.メモの桁数>240の場合
                    atomicBombNotebook.setMemoKnjFontSize("9.5");
                } else {
                    // 上記以外の場合
                    atomicBombNotebook.setMemoKnjFontSize(ReportConstants.FONT_SIZE_12);
                }
            } else {
                // アセスメントフェースシート(TYPE2）の場合
                if (ReportUtil.getByteLength(atomicBombNotebook.getMemoKnj()) > 104) {
                    // 原爆手帳.メモの桁数>104の場合
                    atomicBombNotebook.setMemoKnjFontSize(ReportConstants.FONT_SIZE_6);
                } else {
                    // 上記以外の場合
                    atomicBombNotebook.setMemoKnjFontSize(ReportConstants.FONT_SIZE_9);
                }
            }
            // 原爆手帳.メモ区分
            if (memoPrintFlg == CommonConstants.INT_0 || memoPrintFlg == CommonConstants.INT_1) {
                atomicBombNotebook.setMemoKbn(memoKubun);
            } else if (memoPrintFlg == CommonConstants.INT_2) {
                if (StringUtil.isNotEmpty(atomicBombNotebook.getPlaceKnj())
                        || StringUtil.isNotEmpty(atomicBombNotebook.getMoveKnj())
                        || StringUtil.isNotEmpty(atomicBombNotebook.getJyoukyouKnj())
                        || StringUtil.isNotEmpty(atomicBombNotebook.getShoujyouKnj())
                        || StringUtil.isNotEmpty(atomicBombNotebook.getByoumeiKnj())
                        || StringUtil.isNotEmpty(atomicBombNotebook.getMemoKnj())) {
                    // いずれかに値が存在する場合
                    atomicBombNotebook.setMemoKbn(CommonConstants.INT_1);
                } else {
                    // 以外の場合
                    atomicBombNotebook.setMemoKbn(CommonConstants.INT_0);
                }
            }
        } else {
            // (2). 上記以外の場合
            // 原爆手帳.受給者番号
            atomicBombNotebook.setTechouBango(CommonConstants.BLANK_STRING);
            // 原爆手帳.公費負担者番号
            atomicBombNotebook.setKouhiFutanshaBango(CommonConstants.BLANK_STRING);
            // 原爆手帳.交付年月日
            atomicBombNotebook.setKoufuYmdGG(CommonConstants.BLANK_STRING);
            atomicBombNotebook.setKoufuYmdYY(CommonConstants.BLANK_STRING);
            atomicBombNotebook.setKoufuYmdMM(CommonConstants.BLANK_STRING);
            atomicBombNotebook.setKoufuYmdDD(CommonConstants.BLANK_STRING);
            // 原爆手帳.法第１条による区分名
            atomicBombNotebook.setHibakushaCdKnj(CommonConstants.BLANK_STRING);
            // 原爆手帳.被爆時の年齢
            atomicBombNotebook.setHibakuAge(null);
            // 原爆手帳.被爆の場所
            atomicBombNotebook.setPlaceKnj(CommonConstants.BLANK_STRING);
            // 原爆手帳.被爆直後の行動
            atomicBombNotebook.setMoveKnj(CommonConstants.BLANK_STRING);
            // 原爆手帳.被爆当時の外傷・熱傷の状況
            atomicBombNotebook.setJyoukyouKnj(CommonConstants.BLANK_STRING);
            // 原爆手帳.被爆当時の急性症状
            atomicBombNotebook.setShoujyouKnj(CommonConstants.BLANK_STRING);
            // 原爆手帳.傷病名
            atomicBombNotebook.setByoumeiKnj(CommonConstants.BLANK_STRING);
            // 原爆手帳.メモ
            atomicBombNotebook.setMemoKnj(CommonConstants.BLANK_STRING);
            // 原爆手帳.メモ区分
            atomicBombNotebook.setMemoKbn(memoKubun);
        }

        // 結果を返し
        atomicBombNotebookList.add(atomicBombNotebook);
        return new JRBeanCollectionDataSource(atomicBombNotebookList);
    }

    /**
     * アセスメントフェースシート履歴その他手帳情報の印刷データを編集する
     * 
     * @param history                 リクエストパラメータ.データ.印刷対象履歴[0]
     * @param freeFaceMstPrintOutList マスタ印刷設定
     * @param memoPrintFlg            メモ欄印刷フラグが
     * @param memoKubun               メモ区分
     * @param serviceType             帳票区分
     * @return その他手帳情報の印刷データ
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getOtherNotebookListParamters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutList, Integer memoPrintFlg, Integer memoKubun,
            Integer serviceType)
            throws Exception {

        List<ReportAssessmentFaceSheetTypeOtherNotebook> otherNotebookList = new ArrayList<>();

        /*
         * 1. 行数設定の取得
         */
        int lineCnt;
        // シート「API定義の2.5. マスタ印刷設定取得」のOUTPUT情報を（"koumoku_id = 27"）でフィルターする
        Optional<FreeFaceMstPrintOutEntity> filter = freeFaceMstPrintOutList.stream()
                .filter(item -> item.getKoumokuId() == CommonConstants.INT_27).findFirst();
        if (filter.isPresent()) {
            lineCnt = filter.get().getLineCnt();
        } else {
            lineCnt = CommonConstants.INT_0;
        }

        /*
         * 2. 他の手帳編集
         */
        // その他手帳情報を取得する
        KghKrkFaceSavTecEtGetByCriteriaInEntity kghKrkFaceSavTecEtGetParam = new KghKrkFaceSavTecEtGetByCriteriaInEntity();
        kghKrkFaceSavTecEtGetParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavTecEtGetOutEntity> kghKrkFaceSavTecEtGetList = kghTucAssFaceTecEtSelectMapper
                .findKghKrkFaceSavTecEtGetByCriteria(kghKrkFaceSavTecEtGetParam);

        // (1). 印刷行数まで、繰り返し、下記デフォルト値設定処理を行う
        for (int i = CommonConstants.INT_0; i < lineCnt; i++) {
            ReportAssessmentFaceSheetTypeOtherNotebook info = new ReportAssessmentFaceSheetTypeOtherNotebook();
            // 手帳番号
            info.setTechouBango(CommonConstants.BLANK_STRING);
            // 交付年月日
            info.setKoufuYmdGG(CommonConstants.BLANK_STRING);
            info.setKoufuYmdYY(CommonConstants.BLANK_STRING);
            info.setKoufuYmdMM(CommonConstants.BLANK_STRING);
            info.setKoufuYmdDD(CommonConstants.BLANK_STRING);
            // 手帳区分
            info.setTkbn(CommonConstants.BLANK_STRING);
            // 有効期限・開始日
            info.setStartYmdGG(CommonConstants.BLANK_STRING);
            info.setStartYmdYY(CommonConstants.BLANK_STRING);
            info.setStartYmdMM(CommonConstants.BLANK_STRING);
            info.setStartYmdDD(CommonConstants.BLANK_STRING);
            // 有効期限・終了日
            info.setEndYmdGG(CommonConstants.BLANK_STRING);
            info.setEndYmdYY(CommonConstants.BLANK_STRING);
            info.setEndYmdMM(CommonConstants.BLANK_STRING);
            info.setEndYmdDD(CommonConstants.BLANK_STRING);
            // メモ
            info.setMemoKnj(CommonConstants.BLANK_STRING);
            // メモ区分
            info.setMemoKbn(memoKubun);
            otherNotebookList.add(info);
        }

        // (2). その他手帳情報件数分、印刷行数まで、下記処理を行う
        int i = CommonConstants.INT_0;
        for (KghKrkFaceSavTecEtGetOutEntity entity : kghKrkFaceSavTecEtGetList) {
            if (i == lineCnt) {
                break;
            }
            ReportAssessmentFaceSheetTypeOtherNotebook info = otherNotebookList.get(i);

            // 手帳番号
            info.setTechouBango(ReportUtil.nullToEmpty(entity.getTBango()));
            // 交付年月日
            List<String> koufuYmd = nds3GkFunc01Logic.getFs2gKantanRtn(entity.getGetYmd());
            if (CollectionUtils.isNotEmpty(koufuYmd)) {
                info.setKoufuYmdGG(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_0)));
                info.setKoufuYmdYY(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_1)));
                info.setKoufuYmdMM(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_2)));
                info.setKoufuYmdDD(ReportUtil.nullToEmpty(koufuYmd.get(CommonConstants.INT_3)));
            } else {
                info.setKoufuYmdGG(CommonConstants.BLANK_STRING);
                info.setKoufuYmdYY(CommonConstants.BLANK_STRING);
                info.setKoufuYmdMM(CommonConstants.BLANK_STRING);
                info.setKoufuYmdDD(CommonConstants.BLANK_STRING);
            }
            // 手帳区分
            info.setTkbn(ReportUtil.nullToEmpty(entity.getTkbn()));
            // 有効期限・開始日
            List<String> satrtYmd = nds3GkFunc01Logic.getFs2gKantanRtn(entity.getStartYmd());
            if (CollectionUtils.isNotEmpty(satrtYmd)) {
                info.setStartYmdGG(ReportUtil.nullToEmpty(satrtYmd.get(CommonConstants.INT_0)));
                info.setStartYmdYY(ReportUtil.nullToEmpty(satrtYmd.get(CommonConstants.INT_1)));
                info.setStartYmdMM(ReportUtil.nullToEmpty(satrtYmd.get(CommonConstants.INT_2)));
                info.setStartYmdDD(ReportUtil.nullToEmpty(satrtYmd.get(CommonConstants.INT_3)));
            } else {
                info.setStartYmdGG(CommonConstants.BLANK_STRING);
                info.setStartYmdYY(CommonConstants.BLANK_STRING);
                info.setStartYmdMM(CommonConstants.BLANK_STRING);
                info.setStartYmdDD(CommonConstants.BLANK_STRING);
            }
            // 有効期限・終了日
            List<String> endYmd = nds3GkFunc01Logic.getFs2gKantanRtn(entity.getEndYmd());
            if (CollectionUtils.isNotEmpty(endYmd)) {
                info.setEndYmdGG(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_0)));
                info.setEndYmdYY(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_1)));
                info.setEndYmdMM(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_2)));
                info.setEndYmdDD(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_3)));
            } else {
                info.setEndYmdGG(CommonConstants.BLANK_STRING);
                info.setEndYmdYY(CommonConstants.BLANK_STRING);
                info.setEndYmdMM(CommonConstants.BLANK_STRING);
                info.setEndYmdDD(CommonConstants.BLANK_STRING);
            }
            // メモ
            info.setMemoKnj(ReportUtil.nullToEmpty(entity.getBikoKnj()));
            // メモフォント
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                if (ReportUtil.getByteLength(info.getMemoKnj()) > 240) {
                    info.setMemoKnjFontSize(ReportConstants.FONT_SIZE_10);
                } else {
                    info.setMemoKnjFontSize(ReportConstants.FONT_SIZE_12);
                }
            } else {
                // アセスメントフェースシート(TYPE2）の場合
                if (ReportUtil.getByteLength(info.getMemoKnj()) <= 90) {
                    info.setMemoKnjFontSize(ReportConstants.FONT_SIZE_10);
                } else if (ReportUtil.getByteLength(info.getMemoKnj()) > 90
                        && ReportUtil.getByteLength(info.getMemoKnj()) <= 106) {
                    info.setMemoKnjFontSize(ReportConstants.FONT_SIZE_8);
                } else {
                    info.setMemoKnjFontSize(ReportConstants.FONT_SIZE_6);
                }
            }
            // メモ区分
            if (memoPrintFlg == CommonConstants.INT_0 || memoPrintFlg == CommonConstants.INT_1) {
                info.setMemoKbn(memoKubun);
            } else if (memoPrintFlg == CommonConstants.INT_2) {
                if (StringUtil.isNotEmpty(info.getMemoKnj())) {
                    info.setMemoKbn(CommonConstants.INT_1);
                } else {
                    info.setMemoKbn(CommonConstants.INT_0);
                }
            }
            i++;
        }

        // 結果を返し
        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put(ReportConstants.JRDS_TECETLIST_KEY, new JRBeanCollectionDataSource(otherNotebookList));
        map.put(ReportConstants.JRDS_TITLE_KEY,
                CollectionUtils.isNotEmpty(otherNotebookList)
                        ? otherNotebookList.get(CommonConstants.INT_0).getMemoKbn()
                        : CommonConstants.INT_0);
        mapList.add(map);
        return new JRBeanCollectionDataSource(mapList);
    }

    /**
     * 利用サービス情報の印刷データを編集する
     * 
     * @param history                 リクエストパラメータ.データ.印刷対象履歴[0]
     * @param freeFaceMstPrintOutList マスタ印刷設定
     * @param memoPrintFlg            メモ欄印刷フラグ
     * @param memoKubun               メモ区分
     * @param serviceType             帳票区分
     * @return 利用サービス情報の印刷データ
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getServiceListParamters(ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutList, Integer memoPrintFlg, Integer memoKubun,
            Integer serviceType)
            throws Exception {
        List<ReportAssessmentFaceSheetTypeUseService> serviceList = new ArrayList<>();

        /*
         * 1. 行数設定の取得
         */
        int lineCnt;
        // シート「API定義の2.5. マスタ印刷設定取得」のOUTPUT情報を（"koumoku_id = 28"）でフィルターする
        Optional<FreeFaceMstPrintOutEntity> filter = freeFaceMstPrintOutList.stream()
                .filter(item -> item.getKoumokuId() == CommonConstants.INT_28).findFirst();
        if (filter.isPresent()) {
            lineCnt = filter.get().getLineCnt();
        } else {
            lineCnt = CommonConstants.INT_0;
        }

        /*
         * 2.利用サービスデータ編集
         */
        // 利用サービス情報を取得する
        KghKrkFaceSavSrvGetByCriteriaInEntity kghKrkFaceSavSrvGetParam = new KghKrkFaceSavSrvGetByCriteriaInEntity();
        kghKrkFaceSavSrvGetParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavSrvGetOutEntity> kghKrkFaceSavSrvGetList = kghTucAssFaceSrvSelectMapper
                .findKghKrkFaceSavSrvGetByCriteria(kghKrkFaceSavSrvGetParam);

        // (1). 変数.印刷行数まで、繰り返し、下記デフォルト値設定処理を行う
        for (int i = CommonConstants.INT_0; i < lineCnt; i++) {
            ReportAssessmentFaceSheetTypeUseService info = new ReportAssessmentFaceSheetTypeUseService();
            // 提供事業所
            info.setSvJigyoKnj(CommonConstants.BLANK_STRING);
            // 利用期間・開始日
            info.setStartYmdGG(CommonConstants.BLANK_STRING);
            info.setStartYmdYY(CommonConstants.BLANK_STRING);
            info.setStartYmdMM(CommonConstants.BLANK_STRING);
            info.setStartYmdDD(CommonConstants.BLANK_STRING);
            // 利用期間・終了日
            info.setEndYmdGG(CommonConstants.BLANK_STRING);
            info.setEndYmdYY(CommonConstants.BLANK_STRING);
            info.setEndYmdMM(CommonConstants.BLANK_STRING);
            info.setEndYmdDD(CommonConstants.BLANK_STRING);
            // 担当者
            info.setTantoName(CommonConstants.BLANK_STRING);
            // メモ
            info.setMemo(CommonConstants.BLANK_STRING);
            // メモ区分
            info.setMemoKbn(memoKubun);
            serviceList.add(info);
        }

        // (2). 利用サービス情報件数分、印刷行数まで、下記処理を行う
        List<KghKrkFaceSavSrvGetOutEntity> filterrList = kghKrkFaceSavSrvGetList.stream()
                .sorted(Comparator.comparing(KghKrkFaceSavSrvGetOutEntity::getHoujinId)
                        .thenComparing(KghKrkFaceSavSrvGetOutEntity::getShisetuId)
                        .thenComparing(KghKrkFaceSavSrvGetOutEntity::getJigyoId)
                        .thenComparing(KghKrkFaceSavSrvGetOutEntity::getJigyoNumber))
                .toList();
        int i = CommonConstants.INT_0;

        Integer houjinId = CommonConstants.INT_0;
        Integer shisetuId = CommonConstants.INT_0;
        Integer jigyoId = CommonConstants.INT_0;
        String jigyoNumber = null;
        for (KghKrkFaceSavSrvGetOutEntity entity : filterrList) {
            if (i == lineCnt) {
                break;
            }

            if (!CommonDtoUtil.checkStringEqual(entity.getJigyoNumber(), jigyoNumber) ||
                    entity.getHoujinId() != houjinId || entity.getShisetuId() != shisetuId
                    || entity.getJigyoId() != jigyoId) {

                ReportAssessmentFaceSheetTypeUseService info = serviceList.get(i);

                // 提供事業所
                info.setSvJigyoKnj(ReportUtil.nullToEmpty(entity.getJigyoKnj()));
                // 提供事業所フォント
                if (serviceType == CommonConstants.INT_1) {
                    // アセスメントフェースシート(TYPE1）の場合
                    if (ReportUtil.getByteLength(info.getSvJigyoKnj()) > 58) {
                        info.setSvJigyoKnjFont(ReportConstants.FONT_SIZE_8);
                    } else if (ReportUtil.getByteLength(info.getSvJigyoKnj()) > 46
                            && ReportUtil.getByteLength(info.getSvJigyoKnj()) <= 58) {
                        info.setSvJigyoKnjFont(ReportConstants.FONT_SIZE_10);
                    } else {
                        info.setSvJigyoKnjFont(ReportConstants.FONT_SIZE_12);
                    }
                } else {
                    // アセスメントフェースシート(TYPE02）の場合
                    info.setSvJigyoKnjFont(ReportConstants.FONT_SIZE_9);
                }

                // 利用期間・開始日
                List<String> startYmd = nds3GkFunc01Logic.getFs2gKantanRtn(entity.getStartYmd());
                if (CollectionUtils.isNotEmpty(startYmd)) {
                    info.setStartYmdGG(ReportUtil.nullToEmpty(startYmd.get(CommonConstants.INT_0)));
                    info.setStartYmdYY(ReportUtil.nullToEmpty(startYmd.get(CommonConstants.INT_1)));
                    info.setStartYmdMM(ReportUtil.nullToEmpty(startYmd.get(CommonConstants.INT_2)));
                    info.setStartYmdDD(ReportUtil.nullToEmpty(startYmd.get(CommonConstants.INT_3)));
                } else {
                    info.setStartYmdGG(CommonConstants.BLANK_STRING);
                    info.setStartYmdYY(CommonConstants.BLANK_STRING);
                    info.setStartYmdMM(CommonConstants.BLANK_STRING);
                    info.setStartYmdDD(CommonConstants.BLANK_STRING);
                }
                // 利用期間・終了日
                List<String> endYmd = nds3GkFunc01Logic.getFs2gKantanRtn(entity.getEndYmd());
                if (CollectionUtils.isNotEmpty(endYmd)) {
                    info.setEndYmdGG(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_0)));
                    info.setEndYmdYY(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_1)));
                    info.setEndYmdMM(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_2)));
                    info.setEndYmdDD(ReportUtil.nullToEmpty(endYmd.get(CommonConstants.INT_3)));
                } else {
                    info.setEndYmdGG(CommonConstants.BLANK_STRING);
                    info.setEndYmdYY(CommonConstants.BLANK_STRING);
                    info.setEndYmdMM(CommonConstants.BLANK_STRING);
                    info.setEndYmdDD(CommonConstants.BLANK_STRING);
                }

                // 担当者
                info.setTantoName(ReportUtil.nullToEmpty(entity.getShokuin1Knj()) + CommonConstants.BLANK_SPACE
                        + ReportUtil.nullToEmpty(entity.getShokuin2Knj()));

                // メモ
                info.setMemo(ReportUtil.nullToEmpty(entity.getBikoKnj()));
                // メモフォント
                info.setMemoKnjFont("11.5");
                // メモ区分
                if (memoPrintFlg == CommonConstants.INT_0 || memoPrintFlg == CommonConstants.INT_1) {
                    info.setMemoKbn(memoKubun);
                } else if (memoPrintFlg == CommonConstants.INT_2) {
                    if (StringUtil.isNotEmpty(info.getMemo())) {
                        info.setMemoKbn(CommonConstants.INT_1);
                    } else {
                        info.setMemoKbn(CommonConstants.INT_0);
                    }
                }
                i++;
            }

            houjinId = entity.getHoujinId();
            shisetuId = entity.getShisetuId();
            jigyoId = entity.getJigyoId();
            jigyoNumber = entity.getJigyoNumber();
        }

        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put(ReportConstants.JRDS_SRVLIST_KEY, new JRBeanCollectionDataSource(serviceList));
        map.put(ReportConstants.JRDS_TITLE_KEY,
                CollectionUtils.isNotEmpty(serviceList) ? serviceList.get(CommonConstants.INT_0).getMemoKbn()
                        : CommonConstants.INT_0);
        mapList.add(map);
        return new JRBeanCollectionDataSource(mapList);
    }

    /**
     * 別紙私物設定
     * 
     * @param history                 リクエストパラメータ.データ.印刷対象履歴[0]
     * @param freeFaceMstPrintOutList マスタ印刷設定
     * @return 帳票用データ.私物リスト
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getPersonalBelongingsParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutList) throws Exception {

        List<ReportAssessmentFaceSheetTypePersonalBelongings> personalBelongingsList = new ArrayList<>();
        /* =============1. 行数設定の取得============= */
        Integer lineCnt = CommonConstants.NUMBER_0;
        // マスタ印刷設定情報を（"koumoku_id = 6"）
        Optional<FreeFaceMstPrintOutEntity> firstMatch = freeFaceMstPrintOutList.stream()
                .filter(freeFaceMst -> freeFaceMst.getKoumokuId() == CommonConstants.NUMBER_6)
                .findFirst();
        if (firstMatch.isPresent()) {
            // 印刷行数をフィルター結果の１行目の印刷行数で設定する
            lineCnt = firstMatch.get().getLineCnt();
        }

        /* =============2. 私物編集============= */
        // 下記DAOを利用し、私物情報を取得する
        KghKrkFaceSavSbtGetByCriteriaInEntity kghKrkFaceSavSbtGetByCriteriaInEntity = new KghKrkFaceSavSbtGetByCriteriaInEntity();
        // リクエストパラメータ.データ.印刷対象履歴リスト[0].保存連番
        kghKrkFaceSavSbtGetByCriteriaInEntity.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavSbtGetOutEntity> kghKrkFaceSavSbtGetOutEntities = kghTucAssFaceSbtSelectMapper
                .findKghKrkFaceSavSbtGetByCriteria(kghKrkFaceSavSbtGetByCriteriaInEntity);

        // 印刷行数まで、繰り返し、下記デフォルト値設定処理を行う
        for (int i = CommonConstants.INT_0; i < lineCnt; i++) {
            ReportAssessmentFaceSheetTypePersonalBelongings personalBelongings = new ReportAssessmentFaceSheetTypePersonalBelongings();
            // 私物.受払年月日
            personalBelongings.setDateYmdGG(CommonConstants.BLANK_STRING);
            personalBelongings.setDateYmdYY(CommonConstants.BLANK_STRING);
            personalBelongings.setDateYmdMM(CommonConstants.BLANK_STRING);
            personalBelongings.setDateYmdDD(CommonConstants.BLANK_STRING);
            // 私物.私物名
            personalBelongings.setSibutuKnj(CommonConstants.BLANK_STRING);
            // 私物.色柄等
            personalBelongings.setBikoKnj(CommonConstants.BLANK_STRING);
            // 私物.受入
            personalBelongings.setIncount(CommonConstants.BLANK_STRING);
            // 私物.払出
            personalBelongings.setOutcount(CommonConstants.BLANK_STRING);
            personalBelongingsList.add(personalBelongings);
        }

        // 私物情報件数分、印刷行数まで、下記処理を行う
        for (int i = CommonConstants.INT_0; i < kghKrkFaceSavSbtGetOutEntities.size() && i < lineCnt; i++) {
            KghKrkFaceSavSbtGetOutEntity kghKrkFaceSavSbtGetOutEntity = kghKrkFaceSavSbtGetOutEntities.get(i);
            List<String> dateParts = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavSbtGetOutEntity.getYymmddYmd());
            ReportAssessmentFaceSheetTypePersonalBelongings personalBelongings = personalBelongingsList.get(i);
            // 私物.受払年月日
            if (CollectionUtils.isNotEmpty(dateParts)) {
                personalBelongings.setDateYmdGG(ReportUtil.nullToEmpty(dateParts.get(CommonConstants.INT_0)));
                personalBelongings.setDateYmdYY(ReportUtil.nullToEmpty(dateParts.get(CommonConstants.INT_1)));
                personalBelongings.setDateYmdMM(ReportUtil.nullToEmpty(dateParts.get(CommonConstants.INT_2)));
                personalBelongings.setDateYmdDD(ReportUtil.nullToEmpty(dateParts.get(CommonConstants.INT_3)));
            } else {
                personalBelongings.setDateYmdGG(CommonConstants.BLANK_STRING);
                personalBelongings.setDateYmdYY(CommonConstants.BLANK_STRING);
                personalBelongings.setDateYmdMM(CommonConstants.BLANK_STRING);
                personalBelongings.setDateYmdDD(CommonConstants.BLANK_STRING);
            }
            // 私物.私物名
            personalBelongings.setSibutuKnj(ReportUtil.nullToEmpty(kghKrkFaceSavSbtGetOutEntity.getShibutuKnj()));
            // 私物.色柄等
            personalBelongings.setBikoKnj(ReportUtil.nullToEmpty(kghKrkFaceSavSbtGetOutEntity.getBikoKnj()));
            // 私物.受入
            personalBelongings.setIncount(ReportUtil.nullToEmpty(kghKrkFaceSavSbtGetOutEntity.getIncount()));
            // 私物.払出
            personalBelongings.setOutcount(ReportUtil.nullToEmpty(kghKrkFaceSavSbtGetOutEntity.getOutcount()));
            personalBelongingsList.set(i, personalBelongings);
        }

        // 私物情報
        List<Map<String, Object>> personalBelongingsMapList = new ArrayList<>();
        Map<String, Object> personalBelongingsMap = new HashMap<>();
        personalBelongingsMap.put(ReportConstants.JRDS_SBTLIST_KEY, new JRBeanCollectionDataSource(personalBelongingsList));
        personalBelongingsMapList.add(personalBelongingsMap);
        return new JRBeanCollectionDataSource(personalBelongingsMapList);
    }

    /**
     * 別紙既往歴設定
     * 
     * @param history                 リクエストパラメータ.データ.印刷対象履歴[0]
     * @param freeFaceMstPrintOutList マスタ印刷設定
     * @param memoPrintFlg            メモ欄印刷フラグ
     * @param memoKubun               メモ区分
     * @param serviceType             帳票区分
     * @return 帳票用データ.既往歴
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getPastMedicalHistoriesParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutList,
            Integer memoPrintFlg, Integer memoKubun, Integer serviceType) throws Exception {

        List<ReportAssessmentFaceSheetTypePastMedicalHistory> pastMedicalHistories = new ArrayList<>();
        /* =============1. 行数設定の取得============= */
        Integer lineCnt = CommonConstants.NUMBER_0;
        // マスタ印刷設定情報を（"koumoku_id = 7"）
        Optional<FreeFaceMstPrintOutEntity> firstMatch = freeFaceMstPrintOutList.stream()
                .filter(freeFaceMst -> freeFaceMst.getKoumokuId() == CommonConstants.NUMBER_7)
                .findFirst();
        if (firstMatch.isPresent()) {
            // 印刷行数をフィルター結果の１行目の印刷行数で設定する
            lineCnt = firstMatch.get().getLineCnt();
        }

        /* =============2. 私物編集============= */
        // 下記DAOを利用し、既往歴情報を取得する
        KghKrkFaceSavKioGetByCriteriaInEntity kghKrkFaceSavKioGetByCriteriaInEntity = new KghKrkFaceSavKioGetByCriteriaInEntity();
        // リクエストパラメータ.データ.印刷対象履歴リスト[0].保存連番
        kghKrkFaceSavKioGetByCriteriaInEntity.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavKioGetOutEntity> kghKrkFaceSavSbtGetOutEntities = kghTucAssFaceKioSelectMapper
                .findKghKrkFaceSavKioGetByCriteria(kghKrkFaceSavKioGetByCriteriaInEntity);

        // 印刷行数まで、繰り返し、下記デフォルト値設定処理を行う
        for (int i = CommonConstants.INT_0; i < lineCnt; i++) {
            ReportAssessmentFaceSheetTypePastMedicalHistory pastMedicalHistory = new ReportAssessmentFaceSheetTypePastMedicalHistory();
            // 既往歴.入院通院区分名
            pastMedicalHistory.setNyutuKbnKnj(CommonConstants.BLANK_STRING);
            // 既往歴.主副区分名
            pastMedicalHistory.setMainKbnKnj(CommonConstants.BLANK_STRING);
            // 既往歴.有効期間・開始日
            pastMedicalHistory.setStartYmdGG(CommonConstants.BLANK_STRING);
            pastMedicalHistory.setStartYmdYY(CommonConstants.BLANK_STRING);
            pastMedicalHistory.setStartYmdMM(CommonConstants.BLANK_STRING);
            pastMedicalHistory.setStartYmdDD(CommonConstants.BLANK_STRING);
            // 既往歴.有効期間・終了日
            pastMedicalHistory.setEndYmdGG(CommonConstants.BLANK_STRING);
            pastMedicalHistory.setEndYmdYY(CommonConstants.BLANK_STRING);
            pastMedicalHistory.setEndYmdMM(CommonConstants.BLANK_STRING);
            pastMedicalHistory.setEndYmdDD(CommonConstants.BLANK_STRING);
            // 既往歴.継続区分名
            pastMedicalHistory.setEndKbnKnj(CommonConstants.BLANK_STRING);
            // 既往歴.メモ区分
            pastMedicalHistory.setMemoKbn(CommonConstants.NUMBER_0);
            // 既往歴.メモ欄
            pastMedicalHistory.setMemoKnj(CommonConstants.BLANK_STRING);
            // 既往歴.病名
            pastMedicalHistory.setSickKnj(CommonConstants.BLANK_STRING);
            // 既往歴.服薬区分
            pastMedicalHistory.setDragKbn(CommonConstants.INT_MINUS_1);
            // 既往歴.発病年月日
            pastMedicalHistory.setHatubyoYmdGG(CommonConstants.BLANK_STRING);
            pastMedicalHistory.setHatubyoYmdYY(CommonConstants.BLANK_STRING);
            pastMedicalHistory.setHatubyoYmdMM(CommonConstants.BLANK_STRING);
            pastMedicalHistory.setHatubyoYmdDD(CommonConstants.BLANK_STRING);
            // 既往歴.調査担当者
            pastMedicalHistory.setChousaName(CommonConstants.BLANK_STRING);
            // 既往歴.医療機関詳細
            pastMedicalHistory.setDetails(CommonConstants.BLANK_STRING);
            pastMedicalHistories.add(pastMedicalHistory);
        }

        // 私物情報件数分、印刷行数まで、下記処理を行う
        for (int i = CommonConstants.INT_0; i < kghKrkFaceSavSbtGetOutEntities.size() && i < lineCnt; i++) {
            KghKrkFaceSavKioGetOutEntity kghKrkFaceSavKioGetOutEntity = kghKrkFaceSavSbtGetOutEntities.get(i);
            ReportAssessmentFaceSheetTypePastMedicalHistory pastMedicalHistory = new ReportAssessmentFaceSheetTypePastMedicalHistory();
            // 既往歴.入院通院区分名
            if (kghKrkFaceSavKioGetOutEntity.getNyutuKbn() == CommonConstants.INT_0) {
                pastMedicalHistory.setNyutuKbnKnj(ReportConstants.NYUTUKBNKNJ_1);
            } else if (kghKrkFaceSavKioGetOutEntity.getNyutuKbn() == CommonConstants.INT_1) {
                pastMedicalHistory.setNyutuKbnKnj(ReportConstants.NYUTUKBNKNJ_2);
            } else if (kghKrkFaceSavKioGetOutEntity.getNyutuKbn() == CommonConstants.INT_2) {
                pastMedicalHistory.setNyutuKbnKnj(ReportConstants.NYUTUKBNKNJ_3);
            } else if (kghKrkFaceSavKioGetOutEntity.getNyutuKbn() == CommonConstants.INT_3) {
                pastMedicalHistory.setNyutuKbnKnj(ReportConstants.NYUTUKBNKNJ_4);
            }
            // 既往歴.主副区分名
            if (kghKrkFaceSavKioGetOutEntity.getMainKbn() == CommonConstants.INT_1) {
                pastMedicalHistory.setMainKbnKnj(ReportConstants.MAINKBNKNJ_1);
            } else if (kghKrkFaceSavKioGetOutEntity.getMainKbn() == CommonConstants.INT_0) {
                pastMedicalHistory.setMainKbnKnj(ReportConstants.MAINKBNKNJ_2);
            } else {
                pastMedicalHistory.setMainKbnKnj(CommonConstants.BLANK_STRING);
            }
            // 既往歴.有効期間・開始日
            List<String> startYmdParts = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavKioGetOutEntity.getStartYmd());
            if (CollectionUtils.isNotEmpty(startYmdParts)) {
                pastMedicalHistory.setStartYmdGG(ReportUtil.nullToEmpty(startYmdParts.get(CommonConstants.INT_0)));
                pastMedicalHistory.setStartYmdYY(ReportUtil.nullToEmpty(startYmdParts.get(CommonConstants.INT_1)));
                pastMedicalHistory.setStartYmdMM(ReportUtil.nullToEmpty(startYmdParts.get(CommonConstants.INT_2)));
                pastMedicalHistory.setStartYmdDD(ReportUtil.nullToEmpty(startYmdParts.get(CommonConstants.INT_3)));
            } else {
                pastMedicalHistory.setStartYmdGG(CommonConstants.BLANK_STRING);
                pastMedicalHistory.setStartYmdYY(CommonConstants.BLANK_STRING);
                pastMedicalHistory.setStartYmdMM(CommonConstants.BLANK_STRING);
                pastMedicalHistory.setStartYmdDD(CommonConstants.BLANK_STRING);
            }
            // 既往歴.有効期間・終了日
            List<String> endYmdParts = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavKioGetOutEntity.getEndYmd());
            if (CollectionUtils.isNotEmpty(endYmdParts)) {
                pastMedicalHistory.setEndYmdGG(ReportUtil.nullToEmpty(endYmdParts.get(CommonConstants.INT_0)));
                pastMedicalHistory.setEndYmdYY(ReportUtil.nullToEmpty(endYmdParts.get(CommonConstants.INT_1)));
                pastMedicalHistory.setEndYmdMM(ReportUtil.nullToEmpty(endYmdParts.get(CommonConstants.INT_2)));
                pastMedicalHistory.setEndYmdDD(ReportUtil.nullToEmpty(endYmdParts.get(CommonConstants.INT_3)));
            } else {
                pastMedicalHistory.setEndYmdGG(CommonConstants.BLANK_STRING);
                pastMedicalHistory.setEndYmdYY(CommonConstants.BLANK_STRING);
                pastMedicalHistory.setEndYmdMM(CommonConstants.BLANK_STRING);
                pastMedicalHistory.setEndYmdDD(CommonConstants.BLANK_STRING);
            }
            // 既往歴.継続区分名
            if (kghKrkFaceSavKioGetOutEntity.getKeizokuKbn() == CommonConstants.INT_0) {
                pastMedicalHistory.setEndKbnKnj(ReportConstants.ENDKBNKNJ_1);
            } else if (kghKrkFaceSavKioGetOutEntity.getKeizokuKbn() == CommonConstants.INT_1) {
                pastMedicalHistory.setEndKbnKnj(ReportConstants.ENDKBNKNJ_2);
            } else if (kghKrkFaceSavKioGetOutEntity.getKeizokuKbn() == CommonConstants.INT_2) {
                pastMedicalHistory.setEndKbnKnj(ReportConstants.ENDKBNKNJ_3);
            } else if (kghKrkFaceSavKioGetOutEntity.getKeizokuKbn() == CommonConstants.INT_3) {
                pastMedicalHistory.setEndKbnKnj(ReportConstants.ENDKBNKNJ_4);
            } else if (kghKrkFaceSavKioGetOutEntity.getKeizokuKbn() == CommonConstants.INT_4) {
                pastMedicalHistory.setEndKbnKnj(ReportConstants.ENDKBNKNJ_5);
            } else {
                pastMedicalHistory.setEndKbnKnj(CommonConstants.BLANK_STRING);
            }
            // 既往歴.メモ欄
            pastMedicalHistory.setMemoKnj(ReportUtil.nullToEmpty(kghKrkFaceSavKioGetOutEntity.getBikoKnj()));
            // 既往歴.病名
            pastMedicalHistory.setSickKnj(ReportUtil.nullToEmpty(kghKrkFaceSavKioGetOutEntity.getBunruiKnj())
                    + CommonConstants.BLANK_SPACE + ReportUtil.nullToEmpty(kghKrkFaceSavKioGetOutEntity.getSickKnj()));
            // 既往歴.病名フォント
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                // 既往歴.病名の桁数<＝44の場合
                if (ReportUtil.getByteLength(pastMedicalHistory.getSickKnj()) <= 44) {
                    pastMedicalHistory.setSickKnjFont(ReportConstants.FONT_SIZE_12);
                    // 44< 既往歴.病名の桁数<＝54の場合
                } else if (ReportUtil.getByteLength(pastMedicalHistory.getSickKnj()) > 44
                        && ReportUtil.getByteLength(pastMedicalHistory.getSickKnj()) <= 54) {
                    pastMedicalHistory.setSickKnjFont(ReportConstants.FONT_SIZE_10);
                } else {
                    pastMedicalHistory.setSickKnjFont(ReportConstants.FONT_SIZE_8);
                }
            } else if (serviceType == CommonConstants.INT_2) {
                // アセスメントフェースシート(TYPE2）の場合
                // 既往歴.病名の桁数<＝62の場合
                if (ReportUtil.getByteLength(pastMedicalHistory.getSickKnj()) <= 62) {
                    pastMedicalHistory.setSickKnjFont(ReportConstants.FONT_SIZE_9);
                    // 62< 既往歴.病名の桁数<＝68の場合
                } else if (ReportUtil.getByteLength(pastMedicalHistory.getSickKnj()) > 62
                        && ReportUtil.getByteLength(pastMedicalHistory.getSickKnj()) <= 68) {
                    pastMedicalHistory.setSickKnjFont(ReportConstants.FONT_SIZE_8);
                } else {
                    pastMedicalHistory.setSickKnjFont(ReportConstants.FONT_SIZE_6);
                }
            }
            // 医療機関名
            String hospKnj = ReportUtil.nullToEmpty(kghKrkFaceSavKioGetOutEntity.getHospKnj());
            // 診療科名
            String kaKnj = ReportUtil.nullToEmpty(kghKrkFaceSavKioGetOutEntity.getKaKnj());
            // 医師名
            String hospDrKnj = ReportUtil.nullToEmpty(kghKrkFaceSavKioGetOutEntity.getDoctor1Knj()
                    + CommonConstants.BLANK_SPACE + kghKrkFaceSavKioGetOutEntity.getDoctor2Knj());
            // 電話番号
            String tel = ReportUtil.nullToEmpty(kghKrkFaceSavKioGetOutEntity.getTel());
            // 医療機関詳細編集
            String details = CommonConstants.BLANK_STRING;
            if (StringUtil.isNotEmpty(hospDrKnj.trim()) && StringUtil.isNotEmpty(hospKnj)
                    && StringUtil.isNotEmpty(kaKnj)) {
                if (StringUtil.isNotEmpty(tel)) {
                    details = hospDrKnj + CommonConstants.LEFT_PARENTHESIS + hospKnj + "・" + kaKnj
                            + CommonConstants.RIGHT_PARENTHESIS + " ℡ " + tel;
                } else {
                    details = hospDrKnj + CommonConstants.LEFT_PARENTHESIS + hospKnj + "・" + kaKnj
                            + CommonConstants.RIGHT_PARENTHESIS;
                }
            }
            // 医師名=""の場合
            if (!StringUtil.isNotEmpty(hospDrKnj.trim())) {
                details = hospKnj + "・" + kaKnj + " ℡ " + tel;
            }
            // 診療科名=""の場合
            if (!StringUtil.isNotEmpty(kaKnj)) {
                details = hospKnj + " ℡ " + tel;
            }
            // 医療機関名=""の場合
            if (!StringUtil.isNotEmpty(hospKnj)) {
                details = CommonConstants.BLANK_STRING;
            }
            pastMedicalHistory.setDetails(ReportUtil.nullToEmpty(details));
            // 医療機関詳細のフォントサイズ編集
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                // 既往歴.医療機関詳細の桁数<81の場合
                if (ReportUtil.getByteLength(details) < 81) {
                    pastMedicalHistory.setDetailsFont(ReportConstants.FONT_SIZE_12);
                    // 81<＝ 既往歴.医療機関詳細の桁数<109の場合
                } else if (ReportUtil.getByteLength(details) < 109) {
                    pastMedicalHistory.setDetailsFont(ReportConstants.FONT_SIZE_9);
                } else {
                    pastMedicalHistory.setDetailsFont(ReportConstants.FONT_SIZE_8);
                }
            } else if (serviceType == CommonConstants.INT_2) {
                // アセスメントフェースシート(TYPE2）の場合
                // 既往歴.医療機関詳細の桁数<=102の場合
                if (ReportUtil.getByteLength(details) <= 102) {
                    pastMedicalHistory.setDetailsFont(ReportConstants.FONT_SIZE_9);
                    // 102<既往歴.医療機関詳細の桁数<=112の場合
                } else if (ReportUtil.getByteLength(details) <= 112) {
                    pastMedicalHistory.setDetailsFont(ReportConstants.FONT_SIZE_8);
                } else {
                    pastMedicalHistory.setDetailsFont(ReportConstants.FONT_SIZE_6);
                }
            }
            // 既往歴.服薬区分
            pastMedicalHistory.setDragKbn(kghKrkFaceSavKioGetOutEntity.getFukuyakuKbn());
            // 既往歴.発病年月日
            List<String> hatubyoYmdParts = nds3GkFunc01Logic
                    .getFs2gKantanRtn(kghKrkFaceSavKioGetOutEntity.getSickYmd());
            if (CollectionUtils.isNotEmpty(hatubyoYmdParts)) {
                pastMedicalHistory.setHatubyoYmdGG(ReportUtil.nullToEmpty(hatubyoYmdParts.get(CommonConstants.INT_0)));
                pastMedicalHistory.setHatubyoYmdYY(ReportUtil.nullToEmpty(hatubyoYmdParts.get(CommonConstants.INT_1)));
                pastMedicalHistory.setHatubyoYmdMM(ReportUtil.nullToEmpty(hatubyoYmdParts.get(CommonConstants.INT_2)));
                pastMedicalHistory.setHatubyoYmdDD(ReportUtil.nullToEmpty(hatubyoYmdParts.get(CommonConstants.INT_3)));
            } else {
                pastMedicalHistory.setHatubyoYmdGG(CommonConstants.BLANK_STRING);
                pastMedicalHistory.setHatubyoYmdYY(CommonConstants.BLANK_STRING);
                pastMedicalHistory.setHatubyoYmdMM(CommonConstants.BLANK_STRING);
                pastMedicalHistory.setHatubyoYmdDD(CommonConstants.BLANK_STRING);
            }
            // 既往歴.調査担当者
            pastMedicalHistory.setChousaName(ReportUtil.nullToEmpty(kghKrkFaceSavKioGetOutEntity.getShokuin1Knj())
                    + CommonConstants.BLANK_SPACE
                    + ReportUtil.nullToEmpty(kghKrkFaceSavKioGetOutEntity.getShokuin2Knj()));
            // メモ欄印刷フラグが0または1の場合
            if (memoPrintFlg == CommonConstants.NUMBER_0
                    || memoPrintFlg == CommonConstants.NUMBER_1) {
                pastMedicalHistory.setMemoKbn(memoKubun);
            } else if (memoPrintFlg == CommonConstants.NUMBER_2) {
                // メモ欄印刷フラグが2の場合
                if (StringUtil.isNotEmpty(pastMedicalHistory.getMemoKnj())) {
                    pastMedicalHistory.setMemoKbn(CommonConstants.NUMBER_1);
                } else {
                    pastMedicalHistory.setMemoKbn(CommonConstants.NUMBER_0);
                }
            }
            pastMedicalHistories.set(i, pastMedicalHistory);
        }

        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put(ReportConstants.JRDS_KIOLIST_KEY, new JRBeanCollectionDataSource(pastMedicalHistories));
        mapList.add(map);
        return new JRBeanCollectionDataSource(mapList);
    }

    /**
     * 別紙生活歴設定
     * 
     * @param history                 リクエストパラメータ.データ.印刷対象履歴[0]
     * @param freeFaceMstPrintOutList マスタ印刷設定
     * @param memoPrintFlg            メモ欄印刷フラグ
     * @param memoKubun               メモ区分
     * @param serviceType             帳票区分
     * @return 帳票用データ.生活歴
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getLifeHistoriesParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutList,
            Integer memoPrintFlg, Integer memoKubun, Integer serviceType) throws Exception {

        List<ReportAssessmentFaceSheetTypeLifeHistory> lifeHistories = new ArrayList<>();
        // 下記DAOを利用し、生活歴情報を取得する
        KghKrkFaceSavSktGetByCriteriaInEntity kghKrkFaceSavSktGetByCriteriaInEntity = new KghKrkFaceSavSktGetByCriteriaInEntity();
        // リクエストパラメータ.データ.印刷対象履歴リスト[0].保存連番
        kghKrkFaceSavSktGetByCriteriaInEntity.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavSktGetOutEntity> kghKrkFaceSavSktGetOutEntities = kghTucAssFaceSktSelectMapper
                .findKghKrkFaceSavSktGetByCriteria(kghKrkFaceSavSktGetByCriteriaInEntity);

        /* =============1.メモの設定============= */
        // 生活歴情報を（"rireki_kbn = 2"）でフィルターしたのデータが存在する場合
        String memo = CommonConstants.BLANK_STRING;
        Optional<KghKrkFaceSavSktGetOutEntity> kghKrkFaceSavSktFirstMatch = kghKrkFaceSavSktGetOutEntities.stream()
                .filter(kghKrkFaceSavSkt -> kghKrkFaceSavSkt.getRirekiKbn() == CommonConstants.NUMBER_2.shortValue())
                .findFirst();
        if (kghKrkFaceSavSktFirstMatch.isPresent()) {
            memo = kghKrkFaceSavSktFirstMatch.get().getMemoKnj();
        }

        /* =============2.行数設定の取得============= */
        Integer lineCnt = CommonConstants.NUMBER_0;
        // マスタ印刷設定情報を（"koumoku_id = 8"）
        Optional<FreeFaceMstPrintOutEntity> firstMatch = freeFaceMstPrintOutList.stream()
                .filter(freeFaceMst -> freeFaceMst.getKoumokuId() == CommonConstants.NUMBER_8)
                .findFirst();
        if (firstMatch.isPresent()) {
            // 印刷行数をフィルター結果の１行目の印刷行数で設定する
            lineCnt = firstMatch.get().getLineCnt();
        }

        /* =============3. 生活歴情報編集============= */
        // 印刷行数まで、繰り返し、下記デフォルト値設定処理を行う
        for (int i = CommonConstants.INT_0; i < lineCnt; i++) {
            ReportAssessmentFaceSheetTypeLifeHistory lifeHistory = new ReportAssessmentFaceSheetTypeLifeHistory();
            // 生活歴.日付
            lifeHistory.setLifeDateGG(CommonConstants.BLANK_STRING);
            lifeHistory.setLifeDateYY(CommonConstants.BLANK_STRING);
            lifeHistory.setLifeDateMM(CommonConstants.BLANK_STRING);
            lifeHistory.setLifeDateDD(CommonConstants.BLANK_STRING);
            // 生活歴.生活区分
            lifeHistory.setSeikatuKbn(CommonConstants.BLANK_STRING);
            // 生活歴.内容
            lifeHistory.setNaiyo(CommonConstants.BLANK_STRING);
            // 最終行の場合
            // 生活歴.メモ欄
            if (i == lineCnt - CommonConstants.INT_1) {
                lifeHistory.setLifeMemoKnj(memo);
                if (memoPrintFlg == CommonConstants.NUMBER_2) {
                    lifeHistory.setLifeMemoKbn(
                            !StringUtil.isNotEmpty(memo) ? CommonConstants.NUMBER_0 : CommonConstants.NUMBER_1);
                } else {
                    lifeHistory.setLifeMemoKbn(memoKubun);
                }
            } else {
                lifeHistory.setLifeMemoKnj(CommonConstants.BLANK_STRING);
                lifeHistory.setLifeMemoKbn(CommonConstants.NUMBER_0);
            }
            // 生活歴.メモ欄フォント
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                // メモの桁数>320の場合
                if (StringUtil.isNotEmpty(memo) && ReportUtil.getByteLength(memo) > 320) {
                    lifeHistory.setLifeMemoKnjFont(ReportConstants.FONT_SIZE_9);
                } else {
                    lifeHistory.setLifeMemoKnjFont(ReportConstants.FONT_SIZE_11);
                }
            } else if (serviceType == CommonConstants.INT_2) {
                // アセスメントフェースシート(TYPE2）の場合
                // メモの桁数>220の場合
                if (StringUtil.isNotEmpty(memo) && ReportUtil.getByteLength(memo) > 220) {
                    lifeHistory.setLifeMemoKnjFont(ReportConstants.FONT_SIZE_6);
                    // 200<メモの桁数<=220の場合
                } else if (StringUtil.isNotEmpty(memo) && 200 < ReportUtil.getByteLength(memo)
                        && ReportUtil.getByteLength(memo) <= 220) {
                    lifeHistory.setLifeMemoKnjFont(ReportConstants.FONT_SIZE_8);
                } else {
                    lifeHistory.setLifeMemoKnjFont(ReportConstants.FONT_SIZE_9);
                }
            }
            // 生活歴メモ区分＝0 且つ 印刷設定.行数<3の場合
            if (lifeHistory.getLifeMemoKbn() == CommonConstants.NUMBER_0 && lineCnt < CommonConstants.INT_3) {
                lifeHistory.setLifeLabel(ReportConstants.LIFE_LABEL);
            } else {
                lifeHistory.setLifeLabel(ReportConstants.LIFE_HISTORY_LABEL);
            }
            lifeHistories.add(lifeHistory);
        }

        // 生活歴情報は（rireki_kbn = 1）でフィルター件数分、変数.印刷行数まで、下記処理を行う
        List<KghKrkFaceSavSktGetOutEntity> kghKrkFaceSavSkts = kghKrkFaceSavSktGetOutEntities.stream()
                .filter(kghKrkFaceSavSkt -> kghKrkFaceSavSkt.getRirekiKbn() == CommonConstants.NUMBER_1.shortValue())
                .toList();
        for (int i = CommonConstants.INT_0; i < kghKrkFaceSavSkts.size() && i < lineCnt; i++) {
            KghKrkFaceSavSktGetOutEntity kghKrkFaceSavSktGetOutEntity = kghKrkFaceSavSkts.get(i);
            ReportAssessmentFaceSheetTypeLifeHistory lifeHistory = lifeHistories.get(i);
            // 生活歴.日付
            List<String> dateParts = nds3GkFunc01Logic.getFs2gKantanRtn(kghKrkFaceSavSktGetOutEntity.getDateYmd());
            if (CollectionUtils.isNotEmpty(dateParts)) {
                lifeHistory.setLifeDateGG(ReportUtil.nullToEmpty(dateParts.get(CommonConstants.INT_0)));
                lifeHistory.setLifeDateYY(ReportUtil.nullToEmpty(dateParts.get(CommonConstants.INT_1)));
                lifeHistory.setLifeDateMM(ReportUtil.nullToEmpty(dateParts.get(CommonConstants.INT_2)));
                lifeHistory.setLifeDateDD(ReportUtil.nullToEmpty(dateParts.get(CommonConstants.INT_3)));
            } else {
                lifeHistory.setLifeDateGG(CommonConstants.BLANK_STRING);
                lifeHistory.setLifeDateYY(CommonConstants.BLANK_STRING);
                lifeHistory.setLifeDateMM(CommonConstants.BLANK_STRING);
                lifeHistory.setLifeDateDD(CommonConstants.BLANK_STRING);
            }
            // 生活歴.生活区分
            lifeHistory.setSeikatuKbn(ReportUtil.nullToEmpty(kghKrkFaceSavSktGetOutEntity.getSeikatuKnj()));
            // 生活歴.内容
            lifeHistory.setNaiyo(ReportUtil.nullToEmpty(kghKrkFaceSavSktGetOutEntity.getMemoKnj()));
            lifeHistories.set(i, lifeHistory);
        }

        // 生活歴
        List<Map<String, Object>> lifeHistoriesMapList = new ArrayList<>();
        Map<String, Object> lifeHistoriesMap = new HashMap<>();
        lifeHistoriesMap.put(ReportConstants.JRDS_SKTLIST_KEY, new JRBeanCollectionDataSource(lifeHistories));
        ReportAssessmentFaceSheetTypeLifeHistory lastLifeHistory = lifeHistories.getLast();
        lifeHistoriesMap.put("lifeMemoKbn", lastLifeHistory.getLifeMemoKbn());
        lifeHistoriesMap.put("lifeLabel", lastLifeHistory.getLifeLabel());
        lifeHistoriesMap.put("lifeMemoKnj", lastLifeHistory.getLifeMemoKnj());
        lifeHistoriesMap.put("lifeMemoKnjFont", lastLifeHistory.getLifeMemoKnjFont());
        lifeHistoriesMapList.add(lifeHistoriesMap);
        return new JRBeanCollectionDataSource(lifeHistoriesMapList);
    }

    /**
     * 別紙趣味嗜好設定
     * 
     * @param history                 リクエストパラメータ.データ.印刷対象履歴[0]
     * @param freeFaceMstPrintOutList マスタ印刷設定
     * @param memoPrintFlg            メモ欄印刷フラグ
     * @param memoKubun               メモ区分
     * @param serviceType             帳票区分
     * @return 帳票用データ.趣味嗜好
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getHobbyAndPreferencesParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutList,
            Integer memoPrintFlg, Integer memoKubun, Integer serviceType) throws Exception {

        List<ReportAssessmentFaceSheetTypeHobbyAndPreference> hobbyAndPreferences = new ArrayList<>();

        /* =============1.行数設定の取得============= */
        Integer lineCnt = CommonConstants.NUMBER_0;
        // マスタ印刷設定情報を（"koumoku_id = 13"）
        Optional<FreeFaceMstPrintOutEntity> firstMatch = freeFaceMstPrintOutList.stream()
                .filter(freeFaceMst -> freeFaceMst.getKoumokuId() == CommonConstants.NUMBER_13)
                .findFirst();
        if (firstMatch.isPresent()) {
            // 印刷行数をフィルター結果の１行目の印刷行数で設定する
            lineCnt = firstMatch.get().getLineCnt();
        }

        /* =============2. 趣味嗜好情報編集============= */
        // 下記DAOを利用し、趣味嗜好情報を取得する
        KghKrkFaceSavSymGetByCriteriaInEntity kghKrkFaceSavSymGetByCriteriaInEntity = new KghKrkFaceSavSymGetByCriteriaInEntity();
        // リクエストパラメータ.データ.印刷対象履歴リスト[0].保存連番
        kghKrkFaceSavSymGetByCriteriaInEntity.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavSymGetOutEntity> kghKrkFaceSavSymGetOutEntities = kghTucAssFaceSymSelectMapper
                .findKghKrkFaceSavSymGetByCriteria(kghKrkFaceSavSymGetByCriteriaInEntity);

        // 変数.印刷行数まで、繰り返し、下記デフォルト値設定処理を行う
        for (int i = CommonConstants.INT_0; i < lineCnt; i++) {
            ReportAssessmentFaceSheetTypeHobbyAndPreference hobbyAndPreference = new ReportAssessmentFaceSheetTypeHobbyAndPreference();
            // 趣味嗜好.項目名称
            hobbyAndPreference.setShu1Knj(CommonConstants.BLANK_STRING);
            // 趣味嗜好.内容
            hobbyAndPreference.setShu2Knj(CommonConstants.BLANK_STRING);
            // 趣味嗜好.メモ
            hobbyAndPreference.setSymMemoKnj(CommonConstants.BLANK_STRING);
            // 印刷行数 が i 一致の場合
            if (i == lineCnt - CommonConstants.INT_1) {
                // 趣味嗜好.メモ
                String tokiKnj = CommonConstants.BLANK_STRING;
                if (!CollectionUtils.isNullOrEmpty(kghKrkFaceSavSymGetOutEntities)) {
                    // 趣味嗜好情報のOUTPUT情報の1件目の特記事項
                    tokiKnj = kghKrkFaceSavSymGetOutEntities.getFirst().getTokiKnj();
                }
                hobbyAndPreference.setSymMemoKnj(tokiKnj);
                // 変数.メモ欄印刷フラグが2の場合
                if (memoPrintFlg == CommonConstants.NUMBER_2) {
                    hobbyAndPreference.setSymMemoKbn(
                            !StringUtil.isNotEmpty(tokiKnj) ? CommonConstants.NUMBER_0 : CommonConstants.NUMBER_1);
                    // 変数.メモ欄印刷フラグが0また1の場合
                } else if (memoPrintFlg == CommonConstants.NUMBER_0
                        || memoPrintFlg == CommonConstants.NUMBER_1) {
                    hobbyAndPreference.setSymMemoKbn(memoKubun);
                }
            } else {
                hobbyAndPreference.setSymMemoKbn(CommonConstants.NUMBER_0);
            }
            hobbyAndPreferences.add(hobbyAndPreference);
        }

        // 趣味嗜好情報のOUTPUT情報.趣味嗜好項目名称でグループした結果を下記編集処理行う
        Map<String, List<KghKrkFaceSavSymGetOutEntity>> groupedByShumi1Knj = kghKrkFaceSavSymGetOutEntities.stream()
                .collect(Collectors.groupingBy(KghKrkFaceSavSymGetOutEntity::getShumi1Knj));

        // 趣味嗜好情報件数分、変数.印刷行数まで、下記処理を行う
        int i = CommonConstants.INT_0;
        for (String key : groupedByShumi1Knj.keySet()) {
            if (i == lineCnt) {
                break;
            }

            ReportAssessmentFaceSheetTypeHobbyAndPreference hobbyAndPreference = hobbyAndPreferences.get(i);
            // 趣味嗜好.項目名称
            hobbyAndPreference.setShu1Knj(ReportUtil.nullToEmpty(key));

            String shumi2Knj = CommonConstants.BLANK_STRING;
            // 趣味嗜好リスト[i].項目名で、趣味嗜好情報のOUTPUT情報.趣味嗜好内容リストを取得する
            for (KghKrkFaceSavSymGetOutEntity kghKrkFaceSavSymGetOutEntity2 : groupedByShumi1Knj.get(key)) {
                String thisShumi2Knj = kghKrkFaceSavSymGetOutEntity2.getShumi2Knj();
                // 趣味嗜好.内容リスト[j].趣味嗜好内容の値が存在する場合
                if (StringUtil.isNotEmpty(thisShumi2Knj)) {
                    // 変数.内容が空の場合
                    if (shumi2Knj.isEmpty()) {
                        shumi2Knj = shumi2Knj.concat("･").concat(kghKrkFaceSavSymGetOutEntity2.getShumi2Knj());
                    } else {
                        if (ReportUtil.getByteLength(shumi2Knj.concat(" ･").concat(thisShumi2Knj)) <= 360) {
                            shumi2Knj = shumi2Knj.concat(" ･").concat(thisShumi2Knj);
                        } else {
                            break;
                        }
                    }
                }
            }

            // 趣味嗜好.内容
            hobbyAndPreference.setShu2Knj(ReportUtil.nullToEmpty(shumi2Knj));
            // 趣味嗜好.内容フォント
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                // 趣味嗜好リスト[i].内容の桁数 ＞ 184の場合
                if (StringUtil.isNotEmpty(shumi2Knj) && ReportUtil.getByteLength(shumi2Knj) > 184) {
                    hobbyAndPreference.setShu2KnjFont("5.3");
                    // 140 ＜ 趣味嗜好リスト[i].内容の桁数 ＜＝ 184の場合、
                } else if (StringUtil.isNotEmpty(shumi2Knj) && ReportUtil.getByteLength(shumi2Knj) > 140
                        && ReportUtil.getByteLength(shumi2Knj) <= 184) {
                    hobbyAndPreference.setShu2KnjFont(ReportConstants.FONT_SIZE_8);
                } else {
                    hobbyAndPreference.setShu2KnjFont("10.5");
                }
            } else {
                // アセスメントフェースシート(TYPE2）の場合
                // 趣味嗜好リスト[i].内容の桁数 > 200の場合
                if (StringUtil.isNotEmpty(shumi2Knj) && ReportUtil.getByteLength(shumi2Knj) > 200) {
                    hobbyAndPreference.setShu2KnjFont(ReportConstants.FONT_SIZE_6);
                } else {
                    hobbyAndPreference.setShu2KnjFont(ReportConstants.FONT_SIZE_9);
                }
            }
            hobbyAndPreferences.set(i, hobbyAndPreference);
            i++;
        }

        // 趣味嗜好
        List<Map<String, Object>> hobbyAndPreferencesMapList = new ArrayList<>();
        Map<String, Object> hobbyAndPreferencesMap = new HashMap<>();
        hobbyAndPreferencesMap.put(ReportConstants.JRDS_SYMLIST_KEY, new JRBeanCollectionDataSource(hobbyAndPreferences));
        ReportAssessmentFaceSheetTypeHobbyAndPreference lastHobbyAndPreference = hobbyAndPreferences.getLast();
        hobbyAndPreferencesMap.put("symMemoKbn", lastHobbyAndPreference.getSymMemoKbn());
        hobbyAndPreferencesMap.put("symMemoKnj", lastHobbyAndPreference.getSymMemoKnj());
        hobbyAndPreferencesMapList.add(hobbyAndPreferencesMap);
        return new JRBeanCollectionDataSource(hobbyAndPreferencesMapList);
    }

    /**
     * 別紙人体図設定
     * 
     * @param history     リクエストパラメータ.データ.印刷対象履歴[0]
     * @param bodyImgFlg  人体図フラグ
     * @param serviceType 帳票区分
     * @return 帳票用データ.人体図
     */
    public JRBeanCollectionDataSource getBodySituationsParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            Integer bodyImgFlg, Integer serviceType) {

        List<ReportAssessmentFaceSheetTypeBodySituation> bodySituations = new ArrayList<>();

        // 変数.凡例
        String hanrei = CommonConstants.BLANK_STRING;

        /* =============1. 凡例データ取得============= */
        // 1.1. リクエストパラメータ.データ.印刷対象履歴リスト[0].保存連番が0の場合
        if (CommonDtoUtil.checkStringEqual(history.getSeqSav(), CommonConstants.STR_0)) {
            // 下記DAOを利用し、障害部位図 部品（１０－６）情報を取得する
            KaigoSys1KmsZuMstByCriteriaInEntity kaigoSys1KmsZuMstByCriteriaInEntity = new KaigoSys1KmsZuMstByCriteriaInEntity();
            List<KaigoSys1KmsZuMstOutEntity> kaigoSys1KmsZuMstOutEntities = comMscJintaiBuhinSelectMapper
                    .findKaigoSys1KmsZuMstByCriteria(kaigoSys1KmsZuMstByCriteriaInEntity);
            if (CollectionUtils.isNotEmpty(kaigoSys1KmsZuMstOutEntities)) {
                // 取得の情報を( "use_flg = 1 and ((item_cd > 7 and hatch_flg <> 2) or item_cd < 8)"
                // )でフィルターする
                List<KaigoSys1KmsZuMstOutEntity> kaigoSys1KmsZuMstOutFilteredList = kaigoSys1KmsZuMstOutEntities
                        .stream()
                        .filter(kaigoSys1KmsZuMst -> kaigoSys1KmsZuMst.getUseFlg() == CommonConstants.INT_1
                                && ((kaigoSys1KmsZuMst.getItemCd() > CommonConstants.INT_7
                                        && kaigoSys1KmsZuMst.getHatchFlg() != CommonConstants.INT_2)
                                        || kaigoSys1KmsZuMst.getItemCd() < CommonConstants.INT_8))
                        .toList();
                // ソートキー：表示順 昇順, アイテムコード 昇順
                kaigoSys1KmsZuMstOutFilteredList.sort(Comparator.comparing(KaigoSys1KmsZuMstOutEntity::getSort)
                        .thenComparing(KaigoSys1KmsZuMstOutEntity::getItemCd));
                // ソート結果件数分、繰り返し、下記処理を行う
                for (KaigoSys1KmsZuMstOutEntity kaigoSys1KmsZuMstOutEntity : kaigoSys1KmsZuMstOutFilteredList) {
                    hanrei = hanrei.concat("・")
                            .concat(kaigoSys1KmsZuMstOutEntity.getMark())
                            .concat(CommonConstants.LEFT_PARENTHESIS)
                            .concat(kaigoSys1KmsZuMstOutEntity.getItemnameKnj())
                            .concat(CommonConstants.RIGHT_PARENTHESIS);
                }
            }
            // 1.2. リクエストパラメータ.データ.印刷対象履歴リスト[0].保存連番が0以外の場合
        } else {
            // 下記DAOを利用し、アセスメントフェースシート履歴その他情報を取得する
            KghKrkFaceSavHanreiGetByCriteriaInEntity kghKrkFaceSavHanreiGetByCriteriaInEntity = new KghKrkFaceSavHanreiGetByCriteriaInEntity();
            // リクエストパラメータ.データ.印刷対象履歴リスト[0].保存連番
            kghKrkFaceSavHanreiGetByCriteriaInEntity.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
            List<KghKrkFaceSavHanreiGetOutEntity> kghKrkFaceSavHanreiGetOutEntities = kghTucAssFaceAnySelectMapper
                    .findKghKrkFaceSavHanreiGetByCriteria(kghKrkFaceSavHanreiGetByCriteriaInEntity);
            // 取得データ存在する場合、1件目のデータを取得する
            if (!CollectionUtils.isNullOrEmpty(kghKrkFaceSavHanreiGetOutEntities)) {
                hanrei = kghKrkFaceSavHanreiGetOutEntities.get(CommonConstants.INT_0).getSdat();
            } else {
                hanrei = CommonConstants.BLANK_STRING;
            }
        }

        // 1.3. 凡例編集
        hanrei = ReportConstants.HANREI_STRING + CommonConstants.NEWLINE_CHARACTOR + hanrei;

        /* =============2. 人体図情報取得============= */
        // 1.1. 下記DAOを利用し、ADL評価人体図情報を取得する
        KghKrkFaceSavJintaiGetByCriteriaInEntity kghKrkFaceSavJintaiGetByCriteriaInEntity = new KghKrkFaceSavJintaiGetByCriteriaInEntity();
        kghKrkFaceSavJintaiGetByCriteriaInEntity.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavJintaiGetOutEntity> kghKrkFaceSavJintaiGetOutEntities = kghTucAssFaceJintaiSelectMapper
                .findKghKrkFaceSavJintaiGetByCriteria(kghKrkFaceSavJintaiGetByCriteriaInEntity);

        /* =============3. 人体図情報設定============= */
        // xデフォルト座標
        Integer xDefPos;
        if (serviceType == CommonConstants.INT_1) {
            // アセスメントフェースシート(TYPE1）の場合
            xDefPos = 1852;
        } else {
            // アセスメントフェースシート(TYPE2）の場合
            xDefPos = 1349;
        }

        // yデフォルト座標
        Integer yDefPos = 53;

        // 人体図フラグ = 1の場合
        if (bodyImgFlg == CommonConstants.NUMBER_1) {
            for (KghKrkFaceSavJintaiGetOutEntity kghKrkFaceSavJintaiGetOutEntity : kghKrkFaceSavJintaiGetOutEntities) {
                // ×や＼の種類
                Integer itemCd = kghKrkFaceSavJintaiGetOutEntity.getItemCd();
                // x座標
                Integer xpos = kghKrkFaceSavJintaiGetOutEntity.getXpos() + xDefPos;
                // y座標
                Integer ypos = kghKrkFaceSavJintaiGetOutEntity.getYpos() + yDefPos;
                // x2座標
                Integer x2pos = kghKrkFaceSavJintaiGetOutEntity.getX2();
                // y2座標
                Integer y2pos = kghKrkFaceSavJintaiGetOutEntity.getY2();
                // オブジェクト番号
                // Integer objNo = kghKrkFaceSavJintaiGetOutEntity.getObjNo();
                // 表示色
                Integer color = kghKrkFaceSavJintaiGetOutEntity.getColor();
                // 塗りの種類
                Integer hatchType = kghKrkFaceSavJintaiGetOutEntity.getHatchType();
                // 枠線の種類
                Integer lineType = kghKrkFaceSavJintaiGetOutEntity.getLineType();
                // 部品種別
                Integer hatchFlg = kghKrkFaceSavJintaiGetOutEntity.getHatchFlg();
                // タイプ
                // Integer type;
                if (color == null) {
                    color = CommonConstants.NUMBER_0;
                }
                if (hatchType == null) {
                    hatchType = CommonConstants.NUMBER_0;
                }
                if (lineType == null) {
                    lineType = CommonConstants.NUMBER_5;
                }
                if (hatchFlg == null) {
                    hatchFlg = CommonConstants.NUMBER_0;
                }
                if (x2pos == null || x2pos == CommonConstants.NUMBER_0) {
                    if (itemCd == CommonConstants.NUMBER_0) {
                        x2pos = 1905;
                    } else if (CommonConstants.INT_2 <= itemCd && itemCd <= CommonConstants.INT_6) {
                        x2pos = 360;
                    } else if (itemCd == CommonConstants.INT_1 || itemCd == CommonConstants.INT_7) {
                        x2pos = 608;
                    } else {
                        if (hatchFlg == CommonConstants.NUMBER_0) {
                            x2pos = 608;
                        } else if (hatchFlg == CommonConstants.INT_1 || hatchFlg == CommonConstants.INT_3
                                || hatchFlg == CommonConstants.INT_4) {
                            x2pos = 360;
                        } else if (hatchFlg == CommonConstants.INT_2) {
                            x2pos = 1905;
                        }
                    }
                }
                if ((y2pos == null || y2pos == CommonConstants.NUMBER_0) && itemCd > CommonConstants.NUMBER_0) {
                    if (itemCd == CommonConstants.NUMBER_0) {
                        y2pos = 476;
                    } else if (CommonConstants.INT_2 <= itemCd && itemCd <= CommonConstants.INT_6) {
                        y2pos = 300;
                    } else if (itemCd == CommonConstants.INT_1 || itemCd == CommonConstants.INT_7) {
                        y2pos = 502;
                    } else {
                        if (hatchFlg == CommonConstants.NUMBER_0) {
                            y2pos = 502;
                        } else if (hatchFlg == CommonConstants.INT_1 || hatchFlg == CommonConstants.INT_3
                                || hatchFlg == CommonConstants.INT_4) {
                            y2pos = 300;
                        } else if (hatchFlg == CommonConstants.INT_2) {
                            y2pos = 476;
                        }
                    }
                }
                if (xpos + (x2pos != null ? x2pos : 0) > 10927) {
                    x2pos = 10927 - xpos;
                }
                if (ypos + (y2pos != null ? y2pos : 0) > 7673) {
                    y2pos = 7673 - ypos;
                }
                if (itemCd == CommonConstants.INT_2) {
                    // type = 4;
                } else if (itemCd == CommonConstants.INT_3) {
                    // type = 5;
                } else if (itemCd == CommonConstants.INT_4) {
                    // type = 0;
                } else if (itemCd == CommonConstants.INT_5) {
                    // type = 1;
                } else if (itemCd == CommonConstants.INT_6) {
                    // type = 6;
                }
            }
        }
        return new JRBeanCollectionDataSource(bodySituations);
    }

    /**
     * 別紙主保健設定
     * 
     * @param history                 リクエストパラメータ.データ.印刷対象履歴[0]
     * @param freeFaceMstPrintOutList マスタ印刷設定
     * @param serviceType             帳票区分
     * @return 帳票用データ.主保健
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getMedicalInsurancesParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutList,
            Integer serviceType) throws Exception {

        List<ReportAssessmentFaceSheetTypeMedicalInsurance> medicalInsurances = new ArrayList<>();

        /* =============1.行数設定の取得============= */
        Integer lineCnt = CommonConstants.NUMBER_0;
        // マスタ印刷設定情報を（"koumoku_id = 19"）
        Optional<FreeFaceMstPrintOutEntity> firstMatch = freeFaceMstPrintOutList.stream()
                .filter(freeFaceMst -> freeFaceMst.getKoumokuId() == CommonConstants.INT_19)
                .findFirst();
        if (firstMatch.isPresent()) {
            // 印刷行数をフィルター結果の１行目の印刷行数で設定する
            lineCnt = firstMatch.get().getLineCnt();
        }

        /* =============2. 人体図情報取得============= */
        // 保険情報（主保険情報、老人保健情報、公費情報）を取得する
        KghKrkFaceSavHokenGetByCriteriaInEntity kghKrkFaceSavHokenGetByCriteriaInEntity = new KghKrkFaceSavHokenGetByCriteriaInEntity();
        // リクエストパラメータ.データ.印刷対象履歴リスト[0].保存連番
        kghKrkFaceSavHokenGetByCriteriaInEntity.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        // 下記DAOを利用し、保険情報を取得する
        List<KghKrkFaceSavHokenGetOutEntity> kghKrkFaceSavHokenGetOutEntities = kghTucAssFaceHokenSelectMapper
                .findKghKrkFaceSavHokenGetByCriteria(kghKrkFaceSavHokenGetByCriteriaInEntity);

        // 印刷行数まで、繰り返し、下記デフォルト値設定処理を行う
        for (int i = CommonConstants.INT_0; i < lineCnt; i++) {
            ReportAssessmentFaceSheetTypeMedicalInsurance medicalInsurance = new ReportAssessmentFaceSheetTypeMedicalInsurance();
            // 医療保険.保険名称
            medicalInsurance.setIryouHokenKnj(CommonConstants.BLANK_STRING);
            // 医療保険.保険者番号
            medicalInsurance.setIryoukHokenNo(CommonConstants.BLANK_STRING);
            // 医療保険.記号・番号
            medicalInsurance.setKigonoKnj(CommonConstants.BLANK_STRING);
            // 医療保険.開始日
            medicalInsurance.setIryouStartDateGG(CommonConstants.BLANK_STRING);
            medicalInsurance.setIryouStartDateYY(CommonConstants.BLANK_STRING);
            medicalInsurance.setIryouStartDateMM(CommonConstants.BLANK_STRING);
            medicalInsurance.setIryouStartDateDD(CommonConstants.BLANK_STRING);
            // 医療保険.終了日
            medicalInsurance.setIryouEndDateGG(CommonConstants.BLANK_STRING);
            medicalInsurance.setIryouEndDateYY(CommonConstants.BLANK_STRING);
            medicalInsurance.setIryouEndDateMM(CommonConstants.BLANK_STRING);
            medicalInsurance.setIryouEndDateDD(CommonConstants.BLANK_STRING);
            // 医療保険.チェック日
            medicalInsurance.setIryouChkDateGG(CommonConstants.BLANK_STRING);
            medicalInsurance.setIryouChkDateYY(CommonConstants.BLANK_STRING);
            medicalInsurance.setIryouChkDateMM(CommonConstants.BLANK_STRING);
            medicalInsurance.setIryouChkDateDD(CommonConstants.BLANK_STRING);
            // 医療保険.外来負担金
            medicalInsurance.setIryougFutanGaku(CommonConstants.BLANK_STRING);
            // 医療保険.入院負担金
            medicalInsurance.setIryounFutanGaku(CommonConstants.BLANK_STRING);
            // 医療保険.申請区分名
            medicalInsurance.setSinseiKbnKnj(CommonConstants.BLANK_STRING);
            // 医療保険.特別区分名
            medicalInsurance.setTokubetuKbnKnj(CommonConstants.BLANK_STRING);
            // 医療保険.本人家族区分名
            medicalInsurance.setOwnerKnj(CommonConstants.BLANK_STRING);
            // 医療保険.枝番
            medicalInsurance.setKigonoEda(CommonConstants.BLANK_STRING);
            medicalInsurances.add(medicalInsurance);
        }

        Map<String, List<KghKrkFaceSavHokenGetOutEntity>> groupedByCode = new HashMap<>();
        if (CollectionUtils.isNotEmpty(kghKrkFaceSavHokenGetOutEntities)) {
            // 主保健情報のOUTPUT情報を（"type IN ( 1, 2, 6 )"）でフィルターする
            List<KghKrkFaceSavHokenGetOutEntity> kghKrkFaceSavHokens = kghKrkFaceSavHokenGetOutEntities.stream()
                    .filter(kghKrkFaceSavHoken -> kghKrkFaceSavHoken.getType() == CommonConstants.INT_1
                            || kghKrkFaceSavHoken.getType() == CommonConstants.INT_2
                            || kghKrkFaceSavHoken.getType() == CommonConstants.INT_6)
                    .toList();

            // ・主保健情報のOUTPUT情報の中で「★法制コード→4-7」値一致のデータを最初の1件目だけ出力する
            groupedByCode = kghKrkFaceSavHokens.stream()
                    .collect(Collectors.groupingBy(KghKrkFaceSavHokenGetOutEntity::getCode));
        }
        Set<String> keySet = groupedByCode.keySet();
        // 主保健情報フィルター結果の件数分、変数.印刷行数まで、下記処理を行う
        for (int i = CommonConstants.INT_0; i < keySet.size() && i < lineCnt; i++) {
            for (String key : keySet) {
                List<KghKrkFaceSavHokenGetOutEntity> kghKrkFaceSavHokenList = groupedByCode.get(key);
                if (CollectionUtils.isNotEmpty(kghKrkFaceSavHokenList)) {
                    KghKrkFaceSavHokenGetOutEntity kghKrkFaceSavHokenGetOutEntity = kghKrkFaceSavHokenList
                            .get(CommonConstants.INT_0);
                    ReportAssessmentFaceSheetTypeMedicalInsurance medicalInsurance = medicalInsurances.get(i);
                    String iryouHokenKnj = kghKrkFaceSavHokenGetOutEntity.getNameKnj();
                    // 医療保険.保険名称
                    medicalInsurance.setIryouHokenKnj(ReportUtil.nullToEmpty(iryouHokenKnj));
                    // 医療保険.保険名称フォント
                    // 医療保険リスト[i].保険名称の桁数 < 33 の場合
                    if (StringUtil.isNotEmpty(iryouHokenKnj)) {
                        if (serviceType == CommonConstants.INT_1) {
                            // アセスメントフェースシート(TYPE1）の場合
                            if (ReportUtil.getByteLength(iryouHokenKnj) < 33) {
                                medicalInsurance.setIryouHokenKnjFont(ReportConstants.FONT_SIZE_11);
                                // 33 <＝ 医療保険リスト[i].保険名称の桁数 <39 の場合
                            } else if (ReportUtil.getByteLength(iryouHokenKnj) < 39) {
                                medicalInsurance.setIryouHokenKnjFont(ReportConstants.FONT_SIZE_9);
                                // 39 <＝ 医療保険リスト[i].保険名称の桁数 <47 の場合
                            } else if (ReportUtil.getByteLength(iryouHokenKnj) < 47) {
                                medicalInsurance.setIryouHokenKnjFont(ReportConstants.FONT_SIZE_7);
                            } else {
                                medicalInsurance.setIryouHokenKnjFont(ReportConstants.FONT_SIZE_6);
                            }
                        } else {
                            // アセスメントフェースシート(TYPE2）の場合
                            medicalInsurance.setIryouHokenKnjFont(ReportConstants.FONT_SIZE_9);
                        }
                    }
                    // 医療保険.保険番号
                    medicalInsurance
                            .setIryoukHokenNo(ReportUtil.nullToEmpty(kghKrkFaceSavHokenGetOutEntity.getPublicno()));
                    // 医療保険.記号・番号
                    medicalInsurance
                            .setKigonoKnj(ReportUtil.nullToEmpty(kghKrkFaceSavHokenGetOutEntity.getKigonoKnj()));
                    // 医療保険.記号・番号フォント
                    // 医療保険リスト[i].記号・番号の桁数 ＞ 32 の場合
                    if (serviceType == CommonConstants.INT_1) {
                        // アセスメントフェースシート(TYPE1）の場合
                        if (ReportUtil.getByteLength(medicalInsurance.getKigonoKnj()) > CommonConstants.INT_32) {
                            medicalInsurance.setKigonoKnjFont(ReportConstants.FONT_SIZE_10);
                        } else {
                            medicalInsurance.setKigonoKnjFont(ReportConstants.FONT_SIZE_11);
                        }
                    } else {
                        // アセスメントフェースシート(TYPE2）の場合
                        medicalInsurance.setKigonoKnjFont(ReportConstants.FONT_SIZE_9);
                    }
                    // 医療保険.開始日
                    List<String> startYmdParts = nds3GkFunc01Logic
                            .getFs2gKantanRtn(kghKrkFaceSavHokenGetOutEntity.getStartdateYmd());
                    if (CollectionUtils.isNotEmpty(startYmdParts)) {
                        medicalInsurance
                                .setIryouStartDateGG(ReportUtil.nullToEmpty(startYmdParts.get(CommonConstants.INT_0)));
                        medicalInsurance
                                .setIryouStartDateYY(ReportUtil.nullToEmpty(startYmdParts.get(CommonConstants.INT_1)));
                        medicalInsurance
                                .setIryouStartDateMM(ReportUtil.nullToEmpty(startYmdParts.get(CommonConstants.INT_2)));
                        medicalInsurance
                                .setIryouStartDateDD(ReportUtil.nullToEmpty(startYmdParts.get(CommonConstants.INT_3)));
                    } else {
                        medicalInsurance.setIryouStartDateGG(CommonConstants.BLANK_STRING);
                        medicalInsurance.setIryouStartDateYY(CommonConstants.BLANK_STRING);
                        medicalInsurance.setIryouStartDateMM(CommonConstants.BLANK_STRING);
                        medicalInsurance.setIryouStartDateDD(CommonConstants.BLANK_STRING);
                    }
                    // 医療保険.終了日
                    List<String> endYmdParts = nds3GkFunc01Logic
                            .getFs2gKantanRtn(kghKrkFaceSavHokenGetOutEntity.getEnddateYmd());
                    if (CollectionUtils.isNotEmpty(endYmdParts)) {
                        medicalInsurance
                                .setIryouEndDateGG(ReportUtil.nullToEmpty(endYmdParts.get(CommonConstants.INT_0)));
                        medicalInsurance
                                .setIryouEndDateYY(ReportUtil.nullToEmpty(endYmdParts.get(CommonConstants.INT_1)));
                        medicalInsurance
                                .setIryouEndDateMM(ReportUtil.nullToEmpty(endYmdParts.get(CommonConstants.INT_2)));
                        medicalInsurance
                                .setIryouEndDateDD(ReportUtil.nullToEmpty(endYmdParts.get(CommonConstants.INT_3)));
                    } else {
                        medicalInsurance.setIryouEndDateGG(CommonConstants.BLANK_STRING);
                        medicalInsurance.setIryouEndDateYY(CommonConstants.BLANK_STRING);
                        medicalInsurance.setIryouEndDateMM(CommonConstants.BLANK_STRING);
                        medicalInsurance.setIryouEndDateDD(CommonConstants.BLANK_STRING);
                    }
                    // 医療保険.チェック日
                    List<String> chkYmdParts = nds3GkFunc01Logic
                            .getFs2gKantanRtn(kghKrkFaceSavHokenGetOutEntity.getCheckdateYmd());
                    if (CollectionUtils.isNotEmpty(endYmdParts)) {
                        medicalInsurance
                                .setIryouChkDateGG(ReportUtil.nullToEmpty(chkYmdParts.get(CommonConstants.INT_0)));
                        medicalInsurance
                                .setIryouChkDateYY(ReportUtil.nullToEmpty(chkYmdParts.get(CommonConstants.INT_1)));
                        medicalInsurance
                                .setIryouChkDateMM(ReportUtil.nullToEmpty(chkYmdParts.get(CommonConstants.INT_2)));
                        medicalInsurance
                                .setIryouChkDateDD(ReportUtil.nullToEmpty(chkYmdParts.get(CommonConstants.INT_3)));
                    } else {
                        medicalInsurance.setIryouChkDateGG(CommonConstants.BLANK_STRING);
                        medicalInsurance.setIryouChkDateYY(CommonConstants.BLANK_STRING);
                        medicalInsurance.setIryouChkDateMM(CommonConstants.BLANK_STRING);
                        medicalInsurance.setIryouChkDateDD(CommonConstants.BLANK_STRING);
                    }
                    // 医療保険.外来負担金
                    medicalInsurance.setIryougFutanGaku(
                            ReportUtil.nullToEmpty(formatAmount(kghKrkFaceSavHokenGetOutEntity.getGaifutan())));
                    // 医療保険.入院負担金
                    medicalInsurance.setIryounFutanGaku(
                            ReportUtil.nullToEmpty(formatAmount(kghKrkFaceSavHokenGetOutEntity.getNyufutan())));
                    // 医療保険.申請区分名
                    // 申請区分=1の場合
                    if (kghKrkFaceSavHokenGetOutEntity.getSinsei() == CommonConstants.INT_1) {
                        medicalInsurance.setSinseiKbnKnj(ReportConstants.CONTINUATION_STRING);
                        // 申請区分=2の場合
                    } else if (kghKrkFaceSavHokenGetOutEntity.getSinsei() == CommonConstants.INT_2) {
                        medicalInsurance.setSinseiKbnKnj(ReportConstants.UNDER_REVIEW_STRING);
                    } else {
                        medicalInsurance.setSinseiKbnKnj(CommonConstants.BLANK_STRING);
                    }
                    // 医療保険.特別区分名
                    // 特別区分=1の場合
                    if (kghKrkFaceSavHokenGetOutEntity.getSpecial() == CommonConstants.INT_1) {
                        medicalInsurance.setTokubetuKbnKnj(ReportConstants.LINE_OF_DUTY_STRING);
                        // 特別区分=2の場合
                    } else if (kghKrkFaceSavHokenGetOutEntity.getSpecial() == CommonConstants.INT_2) {
                        medicalInsurance.setTokubetuKbnKnj(ReportConstants.WITHIN_THREE_MONTH_AFTER_DISEMBARKATION_STRING);
                    } else if (kghKrkFaceSavHokenGetOutEntity.getSpecial() == CommonConstants.INT_3) {
                        medicalInsurance.setTokubetuKbnKnj(ReportConstants.COMMUTING_INJURY_STRING);
                    } else {
                        medicalInsurance.setTokubetuKbnKnj(CommonConstants.BLANK_STRING);
                    }
                    // 医療保険.所有者
                    // 本人家族区分=1の場合
                    if (kghKrkFaceSavHokenGetOutEntity.getOwner() == CommonConstants.INT_1) {
                        medicalInsurance.setOwnerKnj(ReportConstants.HONNIN_STRING);
                        // 本人家族区分=2の場合
                    } else if (kghKrkFaceSavHokenGetOutEntity.getOwner() == CommonConstants.INT_0) {
                        medicalInsurance.setOwnerKnj(ReportConstants.KAZOKU_STRING_3);
                    } else {
                        medicalInsurance.setOwnerKnj(CommonConstants.BLANK_STRING);
                    }
                    // 医療保険.枝番
                    medicalInsurance
                            .setKigonoEda(ReportUtil.nullToEmpty(kghKrkFaceSavHokenGetOutEntity.getKigonoEda()));
                }
            }
        }

        // 主保健 医療保険
        List<Map<String, Object>> medicalInsurancesMapList = new ArrayList<>();
        Map<String, Object> medicalInsurancesMap = new HashMap<>();
        medicalInsurancesMap.put(ReportConstants.JRDS_HOK2LIST_KEY, new JRBeanCollectionDataSource(medicalInsurances));
        medicalInsurancesMapList.add(medicalInsurancesMap);
        return new JRBeanCollectionDataSource(medicalInsurancesMapList);
    }

    /**
     * 別紙療育手帳設定
     * 
     * @param history                 リクエストパラメータ.データ.印刷対象履歴[0]
     * @param freeFaceMstPrintOutList マスタ印刷設定
     * @param memoPrintFlg            メモ欄印刷フラグ
     * @param memoKubun               メモ欄印刷フラグ
     * @param serviceType             帳票区分
     * @return 別紙療育手帳情報
     * @throws Exception 例外
     * 
     */
    public JRBeanCollectionDataSource getRemedialEducationNotebookParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutList,
            Integer memoPrintFlg, Integer memoKubun, Integer serviceType) throws Exception {

        List<ReportAssessmentFaceSheetTypeRemedialEducationNotebook> remedialEducationNotebookList = new ArrayList<>();
        ReportAssessmentFaceSheetTypeRemedialEducationNotebook remedialEducationNotebook = new ReportAssessmentFaceSheetTypeRemedialEducationNotebook();

        // 下記DAOを利用し、療育者手帳情報を取得する
        KghKrkFaceSavTecRyGetByCriteriaInEntity kghKrkFaceSavTecRyGetByCriteriaInEntity = new KghKrkFaceSavTecRyGetByCriteriaInEntity();
        // リクエストパラメータ.データ.印刷対象履歴リスト[0].保存連番
        kghKrkFaceSavTecRyGetByCriteriaInEntity.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavTecRyGetOutEntity> kghKrkFaceSavTecRyGetOutEntities = kghTucAssFaceTecRySelectMapper
                .findKghKrkFaceSavTecRyGetByCriteria(kghKrkFaceSavTecRyGetByCriteriaInEntity);

        /* =============1. 療育手帳編集============= */
        // 療育者手帳情報のデータが存在する場合、下記編集処理を行う
        if (!CollectionUtils.isNullOrEmpty(kghKrkFaceSavTecRyGetOutEntities)) {
            KghKrkFaceSavTecRyGetOutEntity kghKrkFaceSavTecRyGetOutEntity = kghKrkFaceSavTecRyGetOutEntities.getFirst();

            // 療育手帳.交付年月日
            List<String> getYmdParts = nds3GkFunc01Logic
                    .getFs2gKantanRtn(kghKrkFaceSavTecRyGetOutEntity.getGetYmd());
            if (CollectionUtils.isNotEmpty(getYmdParts)) {
                remedialEducationNotebook.setKoufuYmdGG(ReportUtil.nullToEmpty(getYmdParts.get(CommonConstants.INT_0)));
                remedialEducationNotebook.setKoufuYmdYY(ReportUtil.nullToEmpty(getYmdParts.get(CommonConstants.INT_1)));
                remedialEducationNotebook.setKoufuYmdMM(ReportUtil.nullToEmpty(getYmdParts.get(CommonConstants.INT_2)));
                remedialEducationNotebook.setKoufuYmdDD(ReportUtil.nullToEmpty(getYmdParts.get(CommonConstants.INT_3)));
            } else {
                remedialEducationNotebook.setKoufuYmdGG(CommonConstants.BLANK_STRING);
                remedialEducationNotebook.setKoufuYmdYY(CommonConstants.BLANK_STRING);
                remedialEducationNotebook.setKoufuYmdMM(CommonConstants.BLANK_STRING);
                remedialEducationNotebook.setKoufuYmdDD(CommonConstants.BLANK_STRING);
            }
            // 療育手帳.判定日
            List<String> jugeYmdParts = nds3GkFunc01Logic
                    .getFs2gKantanRtn(kghKrkFaceSavTecRyGetOutEntity.getJugeYmd());
            if (CollectionUtils.isNotEmpty(jugeYmdParts)) {
                remedialEducationNotebook
                        .setHanteiYmdGG(ReportUtil.nullToEmpty(jugeYmdParts.get(CommonConstants.INT_0)));
                remedialEducationNotebook
                        .setHanteiYmdYY(ReportUtil.nullToEmpty(jugeYmdParts.get(CommonConstants.INT_1)));
                remedialEducationNotebook
                        .setHanteiYmdMM(ReportUtil.nullToEmpty(jugeYmdParts.get(CommonConstants.INT_2)));
                remedialEducationNotebook
                        .setHanteiYmdDD(ReportUtil.nullToEmpty(jugeYmdParts.get(CommonConstants.INT_3)));
            } else {
                remedialEducationNotebook.setHanteiYmdGG(CommonConstants.BLANK_STRING);
                remedialEducationNotebook.setHanteiYmdYY(CommonConstants.BLANK_STRING);
                remedialEducationNotebook.setHanteiYmdMM(CommonConstants.BLANK_STRING);
                remedialEducationNotebook.setHanteiYmdDD(CommonConstants.BLANK_STRING);
            }
            // 療育手帳.次回交付年月日
            List<String> nextYmdParts = nds3GkFunc01Logic
                    .getFs2gKantanRtn(kghKrkFaceSavTecRyGetOutEntity.getNextYmd());
            if (CollectionUtils.isNotEmpty(nextYmdParts)) {
                remedialEducationNotebook.setNextYmdGG(ReportUtil.nullToEmpty(nextYmdParts.get(CommonConstants.INT_0)));
                remedialEducationNotebook.setNextYmdYY(ReportUtil.nullToEmpty(nextYmdParts.get(CommonConstants.INT_1)));
                remedialEducationNotebook.setNextYmdMM(ReportUtil.nullToEmpty(nextYmdParts.get(CommonConstants.INT_2)));
                remedialEducationNotebook.setNextYmdDD(ReportUtil.nullToEmpty(nextYmdParts.get(CommonConstants.INT_3)));
            } else {
                remedialEducationNotebook.setNextYmdGG(CommonConstants.BLANK_STRING);
                remedialEducationNotebook.setNextYmdYY(CommonConstants.BLANK_STRING);
                remedialEducationNotebook.setNextYmdMM(CommonConstants.BLANK_STRING);
                remedialEducationNotebook.setNextYmdDD(CommonConstants.BLANK_STRING);
            }
            // 療育手帳.手帳番号
            remedialEducationNotebook
                    .setTechouBango(ReportUtil.nullToEmpty(kghKrkFaceSavTecRyGetOutEntity.getTBango()));
            // 療育手帳.障害の程度名
            remedialEducationNotebook.setTeidoKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecRyGetOutEntity.getTeido()));
            // 療育手帳.判定機関
            remedialEducationNotebook
                    .setHannteikikanKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecRyGetOutEntity.getHannteikikanKnj()));
            // 療育手帳.判定機関フォント
            if (StringUtil.isNotEmpty(remedialEducationNotebook.getHannteikikanKnj())) {
                if (serviceType == CommonConstants.INT_1) {
                    // アセスメントフェースシート(TYPE1）の場合
                    // 療育手帳.判定機関の桁数 ＜43の場合
                    if (ReportUtil
                            .getByteLength(remedialEducationNotebook.getHannteikikanKnj()) < CommonConstants.INT_43) {
                        remedialEducationNotebook.setHannteikikanKnjFontSize(ReportConstants.FONT_SIZE_11);
                        // 43 <= 療育手帳.判定機関の桁数 < 57の場合
                    } else if (ReportUtil.getByteLength(remedialEducationNotebook.getHannteikikanKnj()) < 57) {
                        remedialEducationNotebook.setHannteikikanKnjFontSize(ReportConstants.FONT_SIZE_8);
                    } else {
                        remedialEducationNotebook.setHannteikikanKnjFontSize(ReportConstants.FONT_SIZE_6);
                    }
                } else {
                    // アセスメントフェースシート(TYPE2）の場合
                    if (ReportUtil.getByteLength(remedialEducationNotebook.getHannteikikanKnj()) < 59) {
                        remedialEducationNotebook.setHannteikikanKnjFontSize(ReportConstants.FONT_SIZE_9);
                    } else {
                        remedialEducationNotebook.setHannteikikanKnjFontSize(ReportConstants.FONT_SIZE_6);
                    }
                }
            }
            // 療育手帳.合併障害
            remedialEducationNotebook.setShougaiKnj(
                    ReportUtil.nullToEmpty(kghKrkFaceSavTecRyGetOutEntity.getGappeiShougaiKnj()));
            // 療育手帳.合併障害フォント
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                // 療育手帳.合併障害の桁数>240の場合
                if (ReportUtil.getByteLength(remedialEducationNotebook.getShougaiKnj()) > 240) {
                    remedialEducationNotebook.setShougaiKnjFontSize(ReportConstants.FONT_SIZE_10);
                } else {
                    remedialEducationNotebook.setShougaiKnjFontSize(ReportConstants.FONT_SIZE_12);
                }
            } else {
                // アセスメントフェースシート(TYPE2）の場合
                if (ReportUtil.getByteLength(remedialEducationNotebook.getShougaiKnj()) > 94) {
                    remedialEducationNotebook.setShougaiKnjFontSize(ReportConstants.FONT_SIZE_6);
                } else {
                    remedialEducationNotebook.setShougaiKnjFontSize(ReportConstants.FONT_SIZE_9);
                }
            }
            // メモ欄印刷フラグが0また1の場合
            if (memoPrintFlg == CommonConstants.NUMBER_0
                    || memoPrintFlg == CommonConstants.NUMBER_1) {
                // 療育手帳.メモ区分
                remedialEducationNotebook.setMemoKbn(memoKubun);
                // メモ欄印刷フラグが2の場合
            } else if (memoPrintFlg == CommonConstants.NUMBER_2) {
                // 療育手帳.メモ区分
                if (StringUtil.isNotEmpty(kghKrkFaceSavTecRyGetOutEntity.getGappeiShougaiKnj())
                        && ReportUtil.getByteLength(
                                kghKrkFaceSavTecRyGetOutEntity.getGappeiShougaiKnj()) > CommonConstants.INT_0) {
                    remedialEducationNotebook.setMemoKbn(CommonConstants.NUMBER_1);
                } else {
                    remedialEducationNotebook.setMemoKbn(CommonConstants.NUMBER_0);
                }
            }
        } else {
            // 療育手帳.交付年月日
            remedialEducationNotebook.setKoufuYmdGG(CommonConstants.BLANK_STRING);
            remedialEducationNotebook.setKoufuYmdYY(CommonConstants.BLANK_STRING);
            remedialEducationNotebook.setKoufuYmdMM(CommonConstants.BLANK_STRING);
            remedialEducationNotebook.setKoufuYmdDD(CommonConstants.BLANK_STRING);
            // 療育手帳.判定日
            remedialEducationNotebook.setHanteiYmdGG(CommonConstants.BLANK_STRING);
            remedialEducationNotebook.setHanteiYmdYY(CommonConstants.BLANK_STRING);
            remedialEducationNotebook.setHanteiYmdMM(CommonConstants.BLANK_STRING);
            remedialEducationNotebook.setHanteiYmdDD(CommonConstants.BLANK_STRING);
            // 療育手帳.次回交付年月日
            remedialEducationNotebook.setNextYmdGG(CommonConstants.BLANK_STRING);
            remedialEducationNotebook.setNextYmdYY(CommonConstants.BLANK_STRING);
            remedialEducationNotebook.setNextYmdMM(CommonConstants.BLANK_STRING);
            remedialEducationNotebook.setNextYmdDD(CommonConstants.BLANK_STRING);
            // 療育手帳.手帳番号
            remedialEducationNotebook.setTechouBango(CommonConstants.BLANK_STRING);
            // 療育手帳.障害の程度名
            remedialEducationNotebook.setTeidoKnj(CommonConstants.BLANK_STRING);
            // 療育手帳.判定機関名
            remedialEducationNotebook.setHannteikikanKnj(CommonConstants.BLANK_STRING);
            // 療育手帳.合併障害
            remedialEducationNotebook.setShougaiKnj(CommonConstants.BLANK_STRING);
            // 療育手帳.メモ区分
            remedialEducationNotebook.setMemoKbn(memoKubun);
        }

        remedialEducationNotebookList.add(remedialEducationNotebook);
        return new JRBeanCollectionDataSource(remedialEducationNotebookList);
    }

    /**
     * 別紙年金手帳設定
     * 
     * @param history                 リクエストパラメータ.データ.印刷対象履歴[0]
     * @param freeFaceMstPrintOutList マスタ印刷設定
     * @param memoPrintFlg            メモ欄印刷フラグ
     * @param memoKubun               メモ区分
     * @param serviceType             帳票区分
     * @return 帳票用データ.年金手帳
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getPensionNotebooksParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history,
            List<FreeFaceMstPrintOutEntity> freeFaceMstPrintOutList,
            Integer memoPrintFlg, Integer memoKubun, Integer serviceType) throws Exception {

        List<ReportAssessmentFaceSheetTypePensionNotebook> pensionNotebooks = new ArrayList<>();

        /* =============1. 行数設定の取得============= */
        Integer lineCnt = CommonConstants.NUMBER_0;
        // マスタ印刷設定情報を（"koumoku_id = 25"）
        Optional<FreeFaceMstPrintOutEntity> firstMatch = freeFaceMstPrintOutList.stream()
                .filter(freeFaceMst -> freeFaceMst.getKoumokuId() == CommonConstants.NUMBER_25)
                .findFirst();
        if (firstMatch.isPresent()) {
            // 印刷行数をフィルター結果の１行目の印刷行数で設定する
            lineCnt = firstMatch.get().getLineCnt();
        }

        /* =============2. 年金手帳編集============= */
        // 下記DAOを利用し、年金者手帳情報を取得する
        KghKrkFaceSavTecNeGetByCriteriaInEntity kghKrkFaceSavTecNeGetByCriteriaInEntity = new KghKrkFaceSavTecNeGetByCriteriaInEntity();
        // リクエストパラメータ.データ.印刷対象履歴リスト[0].保存連番
        kghKrkFaceSavTecNeGetByCriteriaInEntity.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        List<KghKrkFaceSavTecNeGetOutEntity> kghKrkFaceSavTecNeGetOutEntities = kghTucAssFaceTecNeSelectMapper
                .findKghKrkFaceSavTecNeGetByCriteria(kghKrkFaceSavTecNeGetByCriteriaInEntity);

        // 印刷行数まで、繰り返し、下記デフォルト値設定処理を行う
        for (int i = CommonConstants.INT_0; i < lineCnt; i++) {
            ReportAssessmentFaceSheetTypePensionNotebook pensionNotebook = new ReportAssessmentFaceSheetTypePensionNotebook();
            // 年金手帳.手帳番号
            pensionNotebook.setTechouBango(CommonConstants.BLANK_STRING);
            // 年金手帳.種別
            pensionNotebook.setNenkinKnj(CommonConstants.BLANK_STRING);
            // 年金手帳.年金額
            pensionNotebook.setKingaku(CommonConstants.BLANK_STRING);
            // 年金手帳.交付年月日
            pensionNotebook.setKoufuYmdGG(CommonConstants.BLANK_STRING);
            pensionNotebook.setKoufuYmdYY(CommonConstants.BLANK_STRING);
            pensionNotebook.setKoufuYmdMM(CommonConstants.BLANK_STRING);
            pensionNotebook.setKoufuYmdDD(CommonConstants.BLANK_STRING);
            // 年金手帳.支給開始年月日
            pensionNotebook.setStartYmdGG(CommonConstants.BLANK_STRING);
            pensionNotebook.setStartYmdYY(CommonConstants.BLANK_STRING);
            pensionNotebook.setStartYmdMM(CommonConstants.BLANK_STRING);
            pensionNotebook.setStartYmdDD(CommonConstants.BLANK_STRING);
            // 年金手帳.支給
            pensionNotebook.setSikyuuKnj(CommonConstants.BLANK_STRING);
            // 年金手帳.メモ区分
            pensionNotebook.setMemoKbn(memoKubun);
            // 年金手帳.メモ
            pensionNotebook.setMemoKnj(CommonConstants.BLANK_STRING);
            pensionNotebooks.add(pensionNotebook);
        }

        // 年金者手帳情報件数分、変数.印刷行数まで、下記処理を行う
        for (int i = CommonConstants.INT_0; i < kghKrkFaceSavTecNeGetOutEntities.size() && i < lineCnt; i++) {
            KghKrkFaceSavTecNeGetOutEntity kghKrkFaceSavTecNeGetOutEntity = kghKrkFaceSavTecNeGetOutEntities.get(i);
            ReportAssessmentFaceSheetTypePensionNotebook pensionNotebook = pensionNotebooks.get(i);
            // 年金手帳.手帳番号
            pensionNotebook.setTechouBango(ReportUtil.nullToEmpty(kghKrkFaceSavTecNeGetOutEntity.getTBango()));
            // 年金手帳.種別
            pensionNotebook.setNenkinKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecNeGetOutEntity.getTKindKnj()));
            // 年金手帳.種別フォント
            // 年金手帳リスト[i].種別の桁数 ＞ 21の場合
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                if (ReportUtil.getByteLength(pensionNotebook.getNenkinKnj()) > CommonConstants.INT_21) {
                    pensionNotebook.setNenkinKnjFontSize(ReportConstants.FONT_SIZE_10);
                } else {
                    pensionNotebook.setNenkinKnjFontSize(ReportConstants.FONT_SIZE_12);
                }
            } else {
                // アセスメントフェースシート(TYPE2）の場合
                pensionNotebook.setNenkinKnjFontSize(ReportConstants.FONT_SIZE_9);
            }
            // 年金手帳.年金額
            pensionNotebook
                    .setKingaku(ReportUtil.nullToEmpty(formatAmount(kghKrkFaceSavTecNeGetOutEntity.getKingaku())));
            // 年金手帳.年金額 ＞ 999999の場合
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                if (ReportUtil.getByteLength(pensionNotebook.getKingaku()) > CommonConstants.INT_6) {
                    pensionNotebook.setKingakuFontSize("8.5");
                } else {
                    pensionNotebook.setKingakuFontSize("11.5");
                }
            } else {
                // アセスメントフェースシート(TYPE2）の場合
                pensionNotebook.setKingakuFontSize(ReportConstants.FONT_SIZE_9);
            }
            // 年金手帳.交付年月日
            List<String> getYmdParts = nds3GkFunc01Logic
                    .getFs2gKantanRtn(kghKrkFaceSavTecNeGetOutEntity.getGetYmd());
            if (CollectionUtils.isNotEmpty(getYmdParts)) {
                pensionNotebook.setKoufuYmdGG(ReportUtil.nullToEmpty(getYmdParts.get(CommonConstants.INT_0)));
                pensionNotebook.setKoufuYmdYY(ReportUtil.nullToEmpty(getYmdParts.get(CommonConstants.INT_1)));
                pensionNotebook.setKoufuYmdMM(ReportUtil.nullToEmpty(getYmdParts.get(CommonConstants.INT_2)));
                pensionNotebook.setKoufuYmdDD(ReportUtil.nullToEmpty(getYmdParts.get(CommonConstants.INT_3)));
            } else {
                pensionNotebook.setKoufuYmdGG(CommonConstants.BLANK_STRING);
                pensionNotebook.setKoufuYmdYY(CommonConstants.BLANK_STRING);
                pensionNotebook.setKoufuYmdMM(CommonConstants.BLANK_STRING);
                pensionNotebook.setKoufuYmdDD(CommonConstants.BLANK_STRING);
            }
            // 年金手帳.支給開始年月日
            List<String> startYmdParts = nds3GkFunc01Logic
                    .getFs2gKantanRtn(kghKrkFaceSavTecNeGetOutEntity.getStartYmd());
            if (CollectionUtils.isNotEmpty(startYmdParts)) {
                pensionNotebook.setStartYmdGG(ReportUtil.nullToEmpty(startYmdParts.get(CommonConstants.INT_0)));
                pensionNotebook.setStartYmdYY(ReportUtil.nullToEmpty(startYmdParts.get(CommonConstants.INT_1)));
                pensionNotebook.setStartYmdMM(ReportUtil.nullToEmpty(startYmdParts.get(CommonConstants.INT_2)));
                pensionNotebook.setStartYmdDD(ReportUtil.nullToEmpty(startYmdParts.get(CommonConstants.INT_3)));
            } else {
                pensionNotebook.setStartYmdGG(CommonConstants.BLANK_STRING);
                pensionNotebook.setStartYmdYY(CommonConstants.BLANK_STRING);
                pensionNotebook.setStartYmdMM(CommonConstants.BLANK_STRING);
                pensionNotebook.setStartYmdDD(CommonConstants.BLANK_STRING);
            }
            // 年金手帳.支給
            // 受給月1月から受給月12月の中で1に等しい1から12をカンマスペース（", "）で連結する
            String sikyuuKnj = CommonConstants.BLANK_STRING;
            String commaSpaceStr = CommonConstants.STR_COMMA + CommonConstants.SPACE_STRING;
            for (int month = CommonConstants.INT_1; month <= CommonConstants.INT_12; month++) {
                Method getTukiFlg = kghKrkFaceSavTecNeGetOutEntity.getClass().getMethod("getTuki" + month + "Flg");
                Integer tukiFlg = (Integer) getTukiFlg.invoke(kghKrkFaceSavTecNeGetOutEntity);
                if (tukiFlg == CommonConstants.INT_1) {
                    sikyuuKnj += (month == CommonConstants.INT_1 ? CommonConstants.BLANK_STRING : commaSpaceStr)
                            + month;
                }
            }

            // 年金手帳.支給の値が存在する場合
            pensionNotebook.setSikyuuKnj(
                    StringUtil.isNotEmpty(sikyuuKnj) ? sikyuuKnj + ReportConstants.MONTHLY_SALARY_STRING : CommonConstants.BLANK_STRING);
            if (serviceType == CommonConstants.INT_1) {
                // アセスメントフェースシート(TYPE1）の場合
                // 年金手帳リスト[i].支給の桁数 ＞ 36の場合
                if (ReportUtil.getByteLength(pensionNotebook.getSikyuuKnj()) > CommonConstants.INT_36) {
                    pensionNotebook.setSikyuuKnjFontSize(ReportConstants.FONT_SIZE_10);
                } else {
                    pensionNotebook.setSikyuuKnjFontSize("11.5");
                }
            } else {
                // アセスメントフェースシート(TYPE2）の場合
                if (ReportUtil.getByteLength(sikyuuKnj) > 52) {
                    pensionNotebook.setSikyuuKnjFontSize(ReportConstants.FONT_SIZE_6);
                } else {
                    pensionNotebook.setSikyuuKnjFontSize(ReportConstants.FONT_SIZE_9);
                }
            }
            // 年金手帳.メモ
            pensionNotebook.setMemoKnj(ReportUtil.nullToEmpty(kghKrkFaceSavTecNeGetOutEntity.getBikoKnj()));
            // メモ欄印刷フラグが0また1の場合
            if (memoPrintFlg == CommonConstants.NUMBER_0
                    || memoPrintFlg == CommonConstants.NUMBER_1) {
                // 年金手帳.メモ区分
                pensionNotebook.setMemoKbn(memoKubun);
                // メモ欄印刷フラグが2の場合
            } else if (memoPrintFlg == CommonConstants.NUMBER_2) {
                // 年金手帳.メモ区分
                if (StringUtil.isNotEmpty(kghKrkFaceSavTecNeGetOutEntity.getBikoKnj())
                        && ReportUtil
                                .getByteLength(kghKrkFaceSavTecNeGetOutEntity.getBikoKnj()) > CommonConstants.INT_0) {
                    pensionNotebook.setMemoKbn(CommonConstants.NUMBER_1);
                } else {
                    pensionNotebook.setMemoKbn(CommonConstants.NUMBER_0);
                }
            }

        }
        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put(ReportConstants.JRDS_TECNELIST_KEY, new JRBeanCollectionDataSource(pensionNotebooks));
        map.put(ReportConstants.JRDS_TITLE_KEY,
                CollectionUtils.isNotEmpty(pensionNotebooks) ? pensionNotebooks.get(CommonConstants.INT_0).getMemoKbn()
                        : CommonConstants.INT_0);
        mapList.add(map);
        return new JRBeanCollectionDataSource(mapList);

    }

    /**
     * 別紙ADL評価健康診断検査設定
     * 
     * @param history   リクエストパラメータ.データ.印刷対象履歴[0]
     * @param svJigyoId 事業者ID
     * @param koumoku   項目
     * @return 帳票用データ.ADL評価健康診断検査
     * @throws Exception 例外
     */
    public JRBeanCollectionDataSource getReportInspectionsParameters(
            ReportAssessmentFaceSheetTypePrintSubjectHistory history, Integer svJigyoId, Integer koumoku)
            throws Exception {

        List<ReportAssessmentFaceSheetTypeAdlEvaluation> adlEvaluationList = new ArrayList<>();
        List<ReportAssessmentFaceSheetTypeHealthDiagnosis> healthDiagnosisList = new ArrayList<>();
        List<ReportAssessmentFaceSheetTypeInspection> inspectionList = new ArrayList<>();

        /* =============1. 表示設定分類情報の取得============= */
        // 1.1. 下記DAOを利用し、アセスメントフェースシート表示設定分類情報を取得する
        KghKrkFreeFaceAssBunruiGetByCriteriaInEntity kghKrkFreeFaceAssBunruiGetParam = new KghKrkFreeFaceAssBunruiGetByCriteriaInEntity();
        kghKrkFreeFaceAssBunruiGetParam.setSvJigyoId(svJigyoId);
        List<KghKrkFreeFaceAssBunruiGetOutEntity> kghKrkFreeFaceAssBunruiGetList = kghKrkFreeFaceAssBunruiGetSelectMapper
                .findKghKrkFreeFaceAssBunruiGetByCriteria(kghKrkFreeFaceAssBunruiGetParam);

        /* =============2. 表示設定項目情報の取得============= */
        // 2.1. 下記DAOを利用し、アセスメントフェースシート表示設定項目情報を取得する
        KghKrkFreeFaceAssKoumokuGetByCriteriaInEntity kghKrkFreeFaceAssKoumokuGetParam = new KghKrkFreeFaceAssKoumokuGetByCriteriaInEntity();
        kghKrkFreeFaceAssKoumokuGetParam.setSvJigyoId(svJigyoId);
        List<KghKrkFreeFaceAssKoumokuGetOutEntity> kghKrkFreeFaceAssKoumokuGetList = kghKrkFreeFaceAssKoumokuGetSelectMapper
                .findKghKrkFreeFaceAssKoumokuGetByCriteria(kghKrkFreeFaceAssKoumokuGetParam);

        // 上記取得したのOUTPUT情報を（"use_flg = 1 and koumoku_id = " + 変数.項目）でフィルターする
        List<KghKrkFreeFaceAssKoumokuGetOutEntity> kghKrkFreeFaceAssKoumokuGetFilterList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(kghKrkFreeFaceAssKoumokuGetList)) {
            kghKrkFreeFaceAssKoumokuGetFilterList = kghKrkFreeFaceAssKoumokuGetList
                    .stream()
                    .filter(item -> item.getUseFlg() == CommonConstants.INT_1 && item.getKoumokuId() == koumoku)
                    .toList();
        }

        /* =============3. データ編集============= */
        // 表示設定項目情報のフィルター結果件数分、繰り返し、下記処理を行う
        for (KghKrkFreeFaceAssKoumokuGetOutEntity kghKrkFreeFaceAssKoumokuGet : kghKrkFreeFaceAssKoumokuGetFilterList) {
            // 3.1. 画面区分設定
            Integer gamenKbn = CommonConstants.INT_0;
            if (kghKrkFreeFaceAssKoumokuGet.getKoumokuId() == CommonConstants.INT_12) {
                gamenKbn = CommonConstants.INT_9;
            } else if (kghKrkFreeFaceAssKoumokuGet.getKoumokuId() == CommonConstants.INT_16) {
                gamenKbn = CommonConstants.INT_7;
            } else if (kghKrkFreeFaceAssKoumokuGet.getKoumokuId() == CommonConstants.INT_17) {
                gamenKbn = CommonConstants.INT_8;
            } else if (kghKrkFreeFaceAssKoumokuGet.getKoumokuId() == CommonConstants.INT_29) {
                gamenKbn = CommonConstants.INT_5;
            }

            // 3.2. 表示設定分類情報フィルター処理
            Integer filterGamenKbn = gamenKbn;
            List<KghKrkFreeFaceAssBunruiGetOutEntity> kghKrkFreeFaceAssBunruiGetFilterList = kghKrkFreeFaceAssBunruiGetList
                    .stream().filter(item -> item.getGamenKbn() == filterGamenKbn).toList();

            // 3.3. 項目内容編集
            for (KghKrkFreeFaceAssBunruiGetOutEntity kghKrkFreeFaceAssBunruiGet : kghKrkFreeFaceAssBunruiGetFilterList) {
                // 変数.分類ＩＤ
                Integer bunId = kghKrkFreeFaceAssBunruiGet.getBunId();
                // 変数.項目タイトル
                String koumokuTitle = CommonConstants.BLANK_STRING;
                // 変数.項目名リスト
                List<String> koumokuNameList = new ArrayList<>();
                // 変数.内容名リスト
                List<String> naiyouNameList = new ArrayList<>();

                /* =============4. 特定アセスメントの最新データの取得============= */
                koumokuTitle = processAssessment(gamenKbn, bunId, history, koumokuNameList, naiyouNameList);

                // リスト情報
                int minSize = Math.min(koumokuNameList.size(), naiyouNameList.size());

                if (CommonConstants.INT_16 == koumoku) {
                    // ADL評価の場合
                    ReportAssessmentFaceSheetTypeAdlEvaluation adlEvaluation = new ReportAssessmentFaceSheetTypeAdlEvaluation();
                    // 項目タイトル
                    adlEvaluation.setAdlKoumokuTitle(ReportUtil.nullToEmpty(koumokuTitle));
                    // 項目タイトルフォント
                    if (StringUtil.isNotEmpty(adlEvaluation.getAdlKoumokuTitle())) {
                        if (ReportUtil.getByteLength(adlEvaluation.getAdlKoumokuTitle()) > CommonConstants.INT_44) {
                            adlEvaluation.setAdlKoumokuTitleFont(ReportConstants.FONT_SIZE_8);
                        } else {
                            adlEvaluation.setAdlKoumokuTitleFont(ReportConstants.FONT_SIZE_12);
                        }
                    }
                    List<ReportAssessmentFaceSheetTypeAdlEvaluationInfo> adlEvaluationInfoList = IntStream
                            .range(0, minSize)
                            .mapToObj(i -> {
                                ReportAssessmentFaceSheetTypeAdlEvaluationInfo info = new ReportAssessmentFaceSheetTypeAdlEvaluationInfo();
                                info.setAdlKoumokuKnj(ReportUtil.nullToEmpty(koumokuNameList.get(i)));
                                info.setAdlNaiyouKnj(ReportUtil.nullToEmpty(naiyouNameList.get(i)));
                                return info;
                            }).toList();
                    adlEvaluation.setAdlInfoList(new JRBeanCollectionDataSource(adlEvaluationInfoList));
                    adlEvaluationList.add(adlEvaluation);
                } else if (CommonConstants.INT_17 == koumoku) {
                    // 健康診断の場合
                    ReportAssessmentFaceSheetTypeHealthDiagnosis healthDiagnosis = new ReportAssessmentFaceSheetTypeHealthDiagnosis();
                    // 項目タイトル
                    healthDiagnosis.setKksKoumokuTitle(ReportUtil.nullToEmpty(koumokuTitle));
                    // 項目タイトルフォント
                    if (StringUtil.isNotEmpty(healthDiagnosis.getKksKoumokuTitle())) {
                        if (ReportUtil.getByteLength(healthDiagnosis.getKksKoumokuTitle()) > CommonConstants.INT_44) {
                            healthDiagnosis.setKksKoumokuTitleFont(ReportConstants.FONT_SIZE_8);
                        } else {
                            healthDiagnosis.setKksKoumokuTitleFont(ReportConstants.FONT_SIZE_12);
                        }
                    }
                    List<ReportAssessmentFaceSheetTypeHealthDiagnosisInfo> healthDiagnosisInfoList = IntStream
                            .range(CommonConstants.INT_0, minSize)
                            .mapToObj(i -> {
                                ReportAssessmentFaceSheetTypeHealthDiagnosisInfo info = new ReportAssessmentFaceSheetTypeHealthDiagnosisInfo();
                                info.setKksKoumokuKnj(ReportUtil.nullToEmpty(koumokuNameList.get(i)));
                                info.setKksNaiyouKnj(ReportUtil.nullToEmpty(naiyouNameList.get(i)));
                                return info;
                            }).toList();
                    healthDiagnosis.setKksInfoList(new JRBeanCollectionDataSource(healthDiagnosisInfoList));
                    healthDiagnosisList.add(healthDiagnosis);
                } else if (CommonConstants.INT_29 == koumoku) {
                    // 検査の場合
                    ReportAssessmentFaceSheetTypeInspection inspection = new ReportAssessmentFaceSheetTypeInspection();
                    // 項目タイトル
                    inspection.setKnsKoumokuTitle(ReportUtil.nullToEmpty(koumokuTitle));
                    // 項目タイトルフォント
                    if (StringUtil.isNotEmpty(inspection.getKnsKoumokuTitle())) {
                        if (ReportUtil.getByteLength(inspection.getKnsKoumokuTitle()) > CommonConstants.INT_44) {
                            inspection.setKnsKoumokuTitleFont(ReportConstants.FONT_SIZE_8);
                        } else {
                            inspection.setKnsKoumokuTitleFont(ReportConstants.FONT_SIZE_12);
                        }
                    }
                    List<ReportAssessmentFaceSheetTypeInspectionInfo> inspectionInfoList = IntStream
                            .range(CommonConstants.INT_0, minSize)
                            .mapToObj(i -> {
                                ReportAssessmentFaceSheetTypeInspectionInfo info = new ReportAssessmentFaceSheetTypeInspectionInfo();
                                info.setKnsKoumokuKnj(ReportUtil.nullToEmpty(koumokuNameList.get(i)));
                                info.setKnsNaiyouKnj(ReportUtil.nullToEmpty(naiyouNameList.get(i)));
                                return info;
                            }).toList();
                    inspection.setKnsInfoList(new JRBeanCollectionDataSource(inspectionInfoList));
                    inspectionList.add(inspection);
                }
            }
        }

        if (CommonConstants.INT_16 == koumoku) {
            // ADL評価の場合
            List<Map<String, Object>> adlEvaluationMapList = new ArrayList<>();
            Map<String, Object> adlEvaluationMap = new HashMap<>();
            adlEvaluationMap.put(ReportConstants.JRDS_ADLLIST_KEY, new JRBeanCollectionDataSource(adlEvaluationList));
            adlEvaluationMapList.add(adlEvaluationMap);
            return new JRBeanCollectionDataSource(adlEvaluationMapList);
        } else if (CommonConstants.INT_17 == koumoku) {
            // 健康診断の場合
            List<Map<String, Object>> healthDiagnosisMapList = new ArrayList<>();
            Map<String, Object> healthDiagnosisMap = new HashMap<>();
            healthDiagnosisMap.put(ReportConstants.JRDS_KKSLIST_KEY, new JRBeanCollectionDataSource(healthDiagnosisList));
            healthDiagnosisMapList.add(healthDiagnosisMap);
            return new JRBeanCollectionDataSource(healthDiagnosisMapList);
        } else if (CommonConstants.INT_29 == koumoku) {
            // 検査の場合
            List<Map<String, Object>> inspectionMapList = new ArrayList<>();
            Map<String, Object> inspectionMap = new HashMap<>();
            inspectionMap.put(ReportConstants.JRDS_KNSLIST_KEY, new JRBeanCollectionDataSource(inspectionList));
            inspectionMapList.add(inspectionMap);
            return new JRBeanCollectionDataSource(inspectionMapList);
        } else {
            return new JRBeanCollectionDataSource(new ArrayList<>());
        }
    }

    /**
     * 特定アセスメントの最新データの取得
     * 
     * @param gamenKbn        画面区分設定
     * @param bunId           分類ＩＤ
     * @param history         リクエストパラメータ.データ.印刷対象履歴[0]
     * @param koumokuNameList 項目名リスト
     * @param naiyouNameList  内容名リスト
     * @return 項目タイトル
     * @throws Exception 例外
     */
    private String processAssessment(Integer gamenKbn, Integer bunId,
            ReportAssessmentFaceSheetTypePrintSubjectHistory history, List<String> koumokuNameList,
            List<String> naiyouNameList) throws Exception {
        // 項目タイトル
        String koumokuTitle = CommonConstants.BLANK_STRING;

        // 4.1. 下記DAOを利用し、アセスメントフェースシート履歴アセスメント1情報を取得する
        KghKrkFaceSavAss1GetByCriteriaInEntity kghKrkFaceSavAss1GetByCriteriaInEntity = new KghKrkFaceSavAss1GetByCriteriaInEntity();
        // リクエストパラメータ.データ.印刷対象履歴リスト[0].保存連番
        kghKrkFaceSavAss1GetByCriteriaInEntity.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
        // 分類ＩＤ
        kghKrkFaceSavAss1GetByCriteriaInEntity.setDaibunruiId(bunId);
        List<KghKrkFaceSavAss1GetOutEntity> kghKrkFaceSavAss1GetOutEntities = kghTucAssFaceAss1SelectMapper
                .findKghKrkFaceSavAss1GetByCriteria(kghKrkFaceSavAss1GetByCriteriaInEntity);
        // 4.2. 上記取得のOUTPUT情報データがある場合
        if (!CollectionUtils.isNullOrEmpty(kghKrkFaceSavAss1GetOutEntities)) {
            KghKrkFaceSavAss1GetOutEntity kghKrkFaceSavAss1GetOutEntity = kghKrkFaceSavAss1GetOutEntities
                    .get(CommonConstants.INT_0);
            // 履歴番号
            Integer rirekiNo = kghKrkFaceSavAss1GetOutEntity.getRirekiNo();
            // 記録日
            String yymmYmd = kghKrkFaceSavAss1GetOutEntity.getYymmYmd();
            // 画面区分
            gamenKbn = kghKrkFaceSavAss1GetOutEntity.getGamenKbn();
            // 評価形式
            Integer keishikiKbn = kghKrkFaceSavAss1GetOutEntity.getKeishikiKbn();
            // ICF区分
            Integer icfFlg = kghKrkFaceSavAss1GetOutEntity.getIcfFlg();
            // ICF使用用具区分
            // Integer icfYouguFlg =
            // ReportUtil.shortToInteger(kghKrkFaceSavAss1GetOutEntity.getIcfYouguFlg());
            // 項目タイトル
            koumokuTitle = kghKrkFaceSavAss1GetOutEntity.getDaibunruiKnj();

            // 4.2.1. 下記DAOを利用し、アセスメントフェースシート履歴アセスメント2情報を取得する
            KghKrkFaceSavAss2GetByCriteriaInEntity kghKrkFaceSavAss2GetByCriteriaInEntity = new KghKrkFaceSavAss2GetByCriteriaInEntity();
            // リクエストパラメータ.データ.印刷対象履歴リスト[0].保存連番
            kghKrkFaceSavAss2GetByCriteriaInEntity.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
            // 履歴番号
            kghKrkFaceSavAss2GetByCriteriaInEntity.setRirekiNo(rirekiNo);
            List<KghKrkFaceSavAss2GetOutEntity> kghKrkFaceSavAss2GetOutEntities = kghTucAssFaceAss2SelectMapper
                    .findKghKrkFaceSavAss2GetByCriteria(kghKrkFaceSavAss2GetByCriteriaInEntity);
            // 取得の情報の件数＜１の場合、該当処理を終了する
            if (CollectionUtils.isNullOrEmpty(kghKrkFaceSavAss2GetOutEntities)) {
                return koumokuTitle;
            }

            // 4.2.2. 作成日設定
            // 項目名リスト[1] ＝ "年月日"
            koumokuNameList.add(CommonConstants.STR_DATA);
            // 内容名リスト[1] ＝ シート「共通関数補足」の4.2.共通関数「クラス名：Nds3GkFunc01のOUTPUT情報
            List<String> yymmYmdParts = nds3GkFunc01Logic.getFs2gKantanRtn(yymmYmd);
            String yymmNaiyou = CommonConstants.BLANK_STRING;
            if (CollectionUtils.isNotEmpty(yymmYmdParts)) {
                yymmNaiyou = yymmYmdParts.stream().collect(Collectors.joining(CommonConstants.STR_DELIMITER));
            }
            naiyouNameList.add(yymmNaiyou);

            // 4.2.3. アセスメントデータ編集
            for (KghKrkFaceSavAss2GetOutEntity kghKrkFaceSavAss2Get : kghKrkFaceSavAss2GetOutEntities) {
                naiyouNameList.add(CommonConstants.BLANK_STRING);
                int lastIndex = naiyouNameList.size() - CommonConstants.INT_1;
                // 検査区分
                Integer kensaId = kghKrkFaceSavAss2Get.getKensaId();
                // 使用用具
                String youguKnj = kghKrkFaceSavAss2Get.getYouguKnj();

                if (keishikiKbn == CommonConstants.INT_2) {
                    // (1). 変数.評価形式が2：複数の場合
                    // 上記「4.2.1」の取得データのOUTPUT情報.分類名称 + ":" + 上記「4.2.1」の取得データのOUTPUT情報.適用名1
                    koumokuNameList.add(
                            kghKrkFaceSavAss2Get.getBunruiKnj() + ":" + kghKrkFaceSavAss2Get.getAssessKnj());

                    // 上記「4.2.1」の取得データのOUTPUT情報.適用1から適用10の中で1に等しい適用名1から適用名10のデータをスラッシュ（"/"）で連結する
                    String fullTekiyouKnj = CommonConstants.BLANK_STRING;
                    if (kghKrkFaceSavAss2Get.getTekiyou1Id() == CommonConstants.NUMBER_1) {
                        fullTekiyouKnj += ReportUtil.nullToEmpty(kghKrkFaceSavAss2Get.getTekiyou1Knj())
                                + CommonConstants.STR_DELIMITER;
                    }
                    if (kghKrkFaceSavAss2Get.getTekiyou2Id() == CommonConstants.NUMBER_1) {
                        fullTekiyouKnj += ReportUtil.nullToEmpty(kghKrkFaceSavAss2Get.getTekiyou2Knj())
                                + CommonConstants.STR_DELIMITER;
                    }
                    if (kghKrkFaceSavAss2Get.getTekiyou3Id() == CommonConstants.NUMBER_1) {
                        fullTekiyouKnj += ReportUtil.nullToEmpty(kghKrkFaceSavAss2Get.getTekiyou3Knj())
                                + CommonConstants.STR_DELIMITER;
                    }
                    if (kghKrkFaceSavAss2Get.getTekiyou4Id() == CommonConstants.NUMBER_1) {
                        fullTekiyouKnj += ReportUtil.nullToEmpty(kghKrkFaceSavAss2Get.getTekiyou4Knj())
                                + CommonConstants.STR_DELIMITER;
                    }
                    if (kghKrkFaceSavAss2Get.getTekiyou5Id() == CommonConstants.NUMBER_1) {
                        fullTekiyouKnj += ReportUtil.nullToEmpty(kghKrkFaceSavAss2Get.getTekiyou5Knj())
                                + CommonConstants.STR_DELIMITER;
                    }
                    if (kghKrkFaceSavAss2Get.getTekiyou6Id() == CommonConstants.NUMBER_1) {
                        fullTekiyouKnj += ReportUtil.nullToEmpty(kghKrkFaceSavAss2Get.getTekiyou6Knj())
                                + CommonConstants.STR_DELIMITER;
                    }
                    if (kghKrkFaceSavAss2Get.getTekiyou7Id() == CommonConstants.NUMBER_1) {
                        fullTekiyouKnj += ReportUtil.nullToEmpty(kghKrkFaceSavAss2Get.getTekiyou7Knj())
                                + CommonConstants.STR_DELIMITER;
                    }
                    if (kghKrkFaceSavAss2Get.getTekiyou8Id() == CommonConstants.NUMBER_1) {
                        fullTekiyouKnj += ReportUtil.nullToEmpty(kghKrkFaceSavAss2Get.getTekiyou8Knj())
                                + CommonConstants.STR_DELIMITER;
                    }
                    if (kghKrkFaceSavAss2Get.getTekiyou9Id() == CommonConstants.NUMBER_1) {
                        fullTekiyouKnj += ReportUtil.nullToEmpty(kghKrkFaceSavAss2Get.getTekiyou9Knj())
                                + CommonConstants.STR_DELIMITER;
                    }
                    if (kghKrkFaceSavAss2Get.getTekiyou10Id() == CommonConstants.NUMBER_1) {
                        fullTekiyouKnj += ReportUtil.nullToEmpty(kghKrkFaceSavAss2Get.getTekiyou10Knj());
                    }
                    naiyouNameList.set(lastIndex, fullTekiyouKnj);

                    // 変数.使用用具のデータが存在する場合
                    if (StringUtil.isNotEmpty(youguKnj)) {
                        if (StringUtil.isNotEmpty(naiyouNameList.get(lastIndex))) {
                            naiyouNameList.set(lastIndex, naiyouNameList.get(lastIndex)
                                    + CommonConstants.NEWLINE_CHARACTOR + youguKnj);
                        } else {
                            naiyouNameList.set(lastIndex, ReportUtil.nullToEmpty(youguKnj));
                        }
                    }

                } else if (icfFlg == CommonConstants.INT_1) {
                    // (2). 変数.ICF区分が1：icf準拠するの場合
                    koumokuNameList.add(kghKrkFaceSavAss2Get.getBunruiKnj());
                    /*
                     * 目標内容編集
                     */
                    // 変数.評価
                    String keiseki = kghKrkFaceSavAss2Get.getAssessKnj();
                    // 変数.介護内容
                    String kaigoNaiyo = kghKrkFaceSavAss2Get.getYougu2Knj();

                    if (StringUtil.isNotEmpty(keiseki)) {
                        // 変数.評価のデータが存在する（"" 且つ NULL 以外）場合
                        keiseki = CommonConstants.NEWLINE_CHARACTOR + ReportConstants.KEISEKI_LABEL_STRING + keiseki;
                    }

                    if (StringUtil.isNotEmpty(youguKnj)) {
                        // 変数.使用用具のデータが存在する（"" 且つ NULL 以外）場合
                        youguKnj = CommonConstants.NEWLINE_CHARACTOR + ReportConstants.YONGUKNJ_LABEL_STRING + youguKnj;
                    }

                    if (StringUtil.isNotEmpty(kaigoNaiyo)) {
                        // 変数.介護内容のデータが存在する（"" 且つ NULL 以外）場合
                        kaigoNaiyo = CommonConstants.NEWLINE_CHARACTOR + ReportConstants.KAIGONAIYO_LABEL_STRING + kaigoNaiyo;
                    }

                    // 「変数.評価 + 変数.使用用具 + 変数.介護内容」の値が存在する（"" 且つ NULL 以外）場合
                    String mokuhonaiyo = keiseki + youguKnj + kaigoNaiyo;
                    if (mokuhonaiyo.startsWith(CommonConstants.NEWLINE_CHARACTOR)) {
                        mokuhonaiyo.replaceFirst(CommonConstants.NEWLINE_CHARACTOR, CommonConstants.BLANK_STRING);
                    }
                    if (StringUtil.isNotEmpty(mokuhonaiyo)) {
                        naiyouNameList.set(lastIndex, ReportConstants.TARGET_LABEL_STRING + mokuhonaiyo);
                    }

                    /*
                     * 実行内容編集
                     */
                    // 変数.評価
                    keiseki = kghKrkFaceSavAss2Get.getAssess2Knj();
                    // 使用用具
                    youguKnj = kghKrkFaceSavAss2Get.getYougu3Knj();
                    // 変数.介護内容
                    kaigoNaiyo = kghKrkFaceSavAss2Get.getYougu4Knj();

                    if (StringUtil.isNotEmpty(keiseki)) {
                        // 変数.評価のデータが存在する（"" 且つ NULL 以外）場合
                        keiseki = CommonConstants.NEWLINE_CHARACTOR + ReportConstants.KEISEKI_LABEL_STRING + keiseki;
                    }

                    if (StringUtil.isNotEmpty(youguKnj)) {
                        // 変数.使用用具のデータが存在する（"" 且つ NULL 以外）場合
                        youguKnj = CommonConstants.NEWLINE_CHARACTOR + ReportConstants.YONGUKNJ_LABEL_STRING + youguKnj;
                    }

                    if (StringUtil.isNotEmpty(kaigoNaiyo)) {
                        // 変数.介護内容のデータが存在する（"" 且つ NULL 以外）場合
                        kaigoNaiyo = CommonConstants.NEWLINE_CHARACTOR + ReportConstants.KAIGONAIYO_LABEL_STRING + kaigoNaiyo;
                    }

                    // 「変数.評価 + 変数.使用用具 + 変数.介護内容」の値が存在する（"" 且つ NULL 以外）場合
                    String jikkonaiyo = keiseki + youguKnj + kaigoNaiyo;
                    if (StringUtil.isNotEmpty(jikkonaiyo)) {
                        if (StringUtil.isNotEmpty(naiyouNameList.get(lastIndex))) {
                            naiyouNameList.set(lastIndex,
                                    naiyouNameList.get(lastIndex) + CommonConstants.NEWLINE_CHARACTOR);
                        }
                        if (jikkonaiyo.startsWith(CommonConstants.NEWLINE_CHARACTOR)) {
                            jikkonaiyo.replaceFirst(CommonConstants.NEWLINE_CHARACTOR, CommonConstants.BLANK_STRING);
                        }
                        naiyouNameList.set(lastIndex, naiyouNameList.get(lastIndex) + ReportConstants.JIKKOU_LABEL_STRING + jikkonaiyo);
                    }

                    /*
                     * 能力内容編集
                     */
                    // 変数.評価
                    keiseki = kghKrkFaceSavAss2Get.getAssess3Knj();
                    // 使用用具
                    youguKnj = kghKrkFaceSavAss2Get.getYougu5Knj();
                    // 変数.介護内容
                    kaigoNaiyo = kghKrkFaceSavAss2Get.getYougu6Knj();

                    if (StringUtil.isNotEmpty(keiseki)) {
                        // 変数.評価のデータが存在する（"" 且つ NULL 以外）場合
                        keiseki = CommonConstants.NEWLINE_CHARACTOR + ReportConstants.KEISEKI_LABEL_STRING + keiseki;
                    }

                    if (StringUtil.isNotEmpty(youguKnj)) {
                        // 変数.使用用具のデータが存在する（"" 且つ NULL 以外）場合
                        youguKnj = CommonConstants.NEWLINE_CHARACTOR + ReportConstants.YONGUKNJ_LABEL_STRING + youguKnj;
                    }

                    if (StringUtil.isNotEmpty(kaigoNaiyo)) {
                        // 変数.介護内容のデータが存在する（"" 且つ NULL 以外）場合
                        kaigoNaiyo = CommonConstants.NEWLINE_CHARACTOR + ReportConstants.KAIGONAIYO_LABEL_STRING + kaigoNaiyo;
                    }

                    // 「変数.評価 + 変数.使用用具 + 変数.介護内容」の値が存在する（"" 且つ NULL 以外）場合
                    String noryukunaiyo = keiseki + youguKnj + kaigoNaiyo;
                    if (StringUtil.isNotEmpty(noryukunaiyo)) {
                        if (StringUtil.isNotEmpty(naiyouNameList.get(lastIndex))) {
                            naiyouNameList.set(lastIndex,
                                    naiyouNameList.get(lastIndex) + CommonConstants.NEWLINE_CHARACTOR);
                        }
                        if (noryukunaiyo.startsWith(CommonConstants.NEWLINE_CHARACTOR)) {
                            noryukunaiyo.replaceFirst(CommonConstants.NEWLINE_CHARACTOR,
                                    CommonConstants.BLANK_STRING);
                        }
                        naiyouNameList.set(lastIndex, naiyouNameList.get(lastIndex) + ReportConstants.NORYO_LABEL_STRING + noryukunaiyo);
                    }
                } else {
                    // (3). 上記以外の場合
                    koumokuNameList.add(kghKrkFaceSavAss2Get.getBunruiKnj());
                    if (kensaId == CommonConstants.INT_1) {
                        naiyouNameList.set(lastIndex, ReportUtil.nullToEmpty(kghKrkFaceSavAss2Get.getAssessKnj()));
                    } else if (kensaId == CommonConstants.INT_2) {
                        naiyouNameList.set(lastIndex, ReportUtil.nullToEmpty(kghKrkFaceSavAss2Get.getBunshouKnj()));
                    } else {
                        naiyouNameList.set(lastIndex,
                                ReportUtil.nullToEmpty(kghKrkFaceSavAss2Get.getKeisokuchi())
                                        + ReportUtil.nullToEmpty(kghKrkFaceSavAss2Get.getTani()));
                    }

                    if (StringUtil.isNotEmpty(youguKnj)) {
                        if (lastIndex != CommonConstants.INT_0) {
                            if (StringUtil.isNotEmpty(naiyouNameList.get(lastIndex))) {
                                naiyouNameList.set(lastIndex, naiyouNameList.get(lastIndex) + youguKnj);
                            } else {
                                naiyouNameList.set(lastIndex, youguKnj);
                            }
                        } else {
                            naiyouNameList.set(lastIndex, youguKnj);
                        }
                    }
                }
            }

        } else {
            // 4.3. 上記取得のOUTPUT情報データがなし場合
            // 4.3.1. 履歴アセスメント１取得
            List<KghKrdFaceAss12GetOutEntity> kghKrdFaceAss12GetList = new ArrayList<>();
            List<KghKrkFaceSavAss1DmyGetOutEntity> kghKrkFaceSavAss1DmyGetList = new ArrayList<>();
            if (CommonDtoUtil.strValToInt(history.getSeqSav()) == CommonConstants.INT_0) {
                // アセスメントマスタ１の大分類情報を取得する
                KghKrdFaceAss12GetByCriteriaInEntity kghKrdFaceAss12GetParam = new KghKrdFaceAss12GetByCriteriaInEntity();
                kghKrdFaceAss12GetParam.setDaibunruiId(bunId);
                kghKrdFaceAss12GetList = kmsMstAssessment1SelectMapper
                        .findKghKrdFaceAss12GetByCriteria(kghKrdFaceAss12GetParam);
            } else {
                // アセスメントフェースシート履歴アセスメント1ダミー情報を取得する
                KghKrkFaceSavAss1DmyGetByCriteriaInEntity kghKrkFaceSavAss1DmyGetParam = new KghKrkFaceSavAss1DmyGetByCriteriaInEntity();
                kghKrkFaceSavAss1DmyGetParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
                kghKrkFaceSavAss1DmyGetParam.setDaibunruiId(bunId);
                kghKrkFaceSavAss1DmyGetList = kghTucAssFaceAss1DmySelectMapper
                        .findKghKrkFaceSavAss1DmyGetByCriteria(kghKrkFaceSavAss1DmyGetParam);
            }

            // 上記「4.3.1」の取得データがなし場合、該当処理を終了する
            if (CollectionUtils.isNullOrEmpty(kghKrdFaceAss12GetList)
                    && CollectionUtils.isNullOrEmpty(kghKrkFaceSavAss1DmyGetList)) {
                return koumokuTitle;
            }

            // 4.3.2. 履歴アセスメント１データ取得
            // 変数.評価形式
            Integer keishikiKbn = CommonConstants.INT_0;
            // 変数.ICF区分
            // Integer icfFlg = 0;
            // 変数.ICF使用用具区分
            // Integer icfYouguFlg = 0;
            // 変数.大分類ID
            Integer daibunruiId = CommonConstants.INT_0;
            if (CollectionUtils.isNotEmpty(kghKrdFaceAss12GetList)) {
                // 変数.画面区分
                gamenKbn = kghKrdFaceAss12GetList.get(CommonConstants.INT_0).getGamenKbn();
                keishikiKbn = kghKrdFaceAss12GetList.get(CommonConstants.INT_0).getKeishikiKbn();
                // icfFlg =
                // ReportUtil.shortToInteger(kghKrdFaceAss12GetList.get(CommonConstants.INT_0).getIcfFlg());
                // icfYouguFlg =
                // ReportUtil.shortToInteger(kghKrdFaceAss12GetList.get(CommonConstants.INT_0).getIcfYouguFlg());
                daibunruiId = kghKrdFaceAss12GetList.get(CommonConstants.INT_0).getDaibunruiId();
                koumokuTitle = kghKrdFaceAss12GetList.get(CommonConstants.INT_0).getDaibunruiKnj();
            }

            if (CollectionUtils.isNotEmpty(kghKrkFaceSavAss1DmyGetList)) {
                gamenKbn = kghKrkFaceSavAss1DmyGetList.get(CommonConstants.INT_0).getGamenKbn();
                keishikiKbn = kghKrkFaceSavAss1DmyGetList.get(CommonConstants.INT_0).getKeishikiKbn();
                // icfFlg =
                // ReportUtil.shortToInteger(kghKrkFaceSavAss1DmyGetList.get(CommonConstants.INT_0).getIcfFlg());
                // icfYouguFlg =
                // ReportUtil.shortToInteger(kghKrkFaceSavAss1DmyGetList.get(CommonConstants.INT_0).getIcfYouguFlg());
                daibunruiId = kghKrkFaceSavAss1DmyGetList.get(CommonConstants.INT_0).getDaibunruiId();
                koumokuTitle = kghKrkFaceSavAss1DmyGetList.get(CommonConstants.INT_0).getDaibunruiKnj();
            }

            // 4.3.3. 履歴アセスメント２取得
            List<KghKrdFaceAss22GetOutEntity> kghKrdFaceAss22GetFilterList = new ArrayList<>();
            List<KghKrkFaceSavAss2DmyGetOutEntity> kghKrkFaceSavAss2DmyGetList = new ArrayList<>();
            if (CommonDtoUtil.strValToInt(history.getSeqSav()) == CommonConstants.INT_0) {
                // アセスメントマスタ情報を取得する
                KghKrdFaceAss22GetByCriteriaInEntity kghKrdFaceAss22GetParam = new KghKrdFaceAss22GetByCriteriaInEntity();
                kghKrdFaceAss22GetParam.setGamenKbn(gamenKbn);
                List<KghKrdFaceAss22GetOutEntity> kghKrdFaceAss22GetList = assessmentMasterSelectMapper
                        .findKghKrdFaceAss22GetByCriteria(kghKrdFaceAss22GetParam);

                // 上記取得データを（"daibunrui_id = " + 変数.大分類ID）でフィルターする
                int daibunruiIdForFilter = daibunruiId;
                kghKrdFaceAss22GetFilterList = kghKrdFaceAss22GetList.stream()
                        .filter(item -> item.getDaibunruiId() == daibunruiIdForFilter).toList();
            } else {

                // アセスメントフェースシート履歴アセスメント2ダミー情報を取得する
                KghKrkFaceSavAss2DmyGetByCriteriaInEntity kghKrkFaceSavAss2DmyGetParam = new KghKrkFaceSavAss2DmyGetByCriteriaInEntity();
                kghKrkFaceSavAss2DmyGetParam.setSavSeq(CommonDtoUtil.strValToInt(history.getSeqSav()));
                kghKrkFaceSavAss2DmyGetParam.setGamenKbn(gamenKbn);
                kghKrkFaceSavAss2DmyGetParam.setDaibunruiId(daibunruiId);
                kghKrkFaceSavAss2DmyGetList = kghTucAssFaceAss2DmySelectMapper
                        .findKghKrkFaceSavAss2DmyGetByCriteria(kghKrkFaceSavAss2DmyGetParam);
            }

            // 上記「4.3.3」の取得データがなし場合、該当処理を終了する
            if (CollectionUtils.isNullOrEmpty(kghKrdFaceAss22GetFilterList)
                    && CollectionUtils.isNullOrEmpty(kghKrkFaceSavAss2DmyGetList)) {
                return koumokuTitle;
            }

            // 4.3.4. 作成日設定
            koumokuNameList.add(CommonConstants.STR_DATA);
            naiyouNameList.add(CommonConstants.NEWLINE_CHARACTOR);

            // 4.3.5. アセスメントデータ編集
            // 上記「4.3.3」の取得データ件数分、繰り返し、下記処理を行う
            String bunruiKnj = CommonConstants.BLANK_STRING;
            if (CollectionUtils.isNotEmpty(kghKrdFaceAss22GetFilterList)) {
                int index = CommonConstants.INT_0;
                for (KghKrdFaceAss22GetOutEntity kghKrdFaceAss22GetFilter : kghKrdFaceAss22GetFilterList) {
                    if (keishikiKbn == CommonConstants.INT_2) {
                        // (1). 変数.評価形式が2：複数の場合
                        koumokuNameList.add(kghKrdFaceAss22GetFilter.getBunruiKnj() + ":"
                                + kghKrdFaceAss22GetFilter.getAssessKnj());
                        naiyouNameList
                                .add(CommonConstants.NEWLINE_CHARACTOR + CommonConstants.NEWLINE_CHARACTOR);
                    } else {
                        // (2). 上記以外の場合
                        if (index == CommonConstants.INT_0) {
                            // ①.１行目の場合
                            koumokuNameList.add(kghKrdFaceAss22GetFilter.getBunruiKnj());
                            naiyouNameList
                                    .add(CommonConstants.NEWLINE_CHARACTOR + CommonConstants.NEWLINE_CHARACTOR);
                        } else {
                            // ②.上記以外の場合
                            if (!CommonDtoUtil.checkStringEqual(bunruiKnj,
                                    kghKrdFaceAss22GetFilter.getBunruiKnj())) {
                                // 取得した分類名称と前の行で取得した分類名称が異なる場合
                                koumokuNameList.add(kghKrdFaceAss22GetFilter.getBunruiKnj());
                                naiyouNameList
                                        .add(CommonConstants.NEWLINE_CHARACTOR
                                                + CommonConstants.NEWLINE_CHARACTOR);
                            }
                        }
                    }
                    bunruiKnj = kghKrdFaceAss22GetFilter.getBunruiKnj();
                    index++;
                }
            }

            if (CollectionUtils.isNotEmpty(kghKrkFaceSavAss2DmyGetList)) {
                int index = CommonConstants.INT_0;
                for (KghKrkFaceSavAss2DmyGetOutEntity kghKrkFaceSavAss2DmyGet : kghKrkFaceSavAss2DmyGetList) {
                    if (keishikiKbn == CommonConstants.INT_2) {
                        // (1). 変数.評価形式が2：複数の場合
                        koumokuNameList.add(kghKrkFaceSavAss2DmyGet.getBunruiKnj() + ":"
                                + kghKrkFaceSavAss2DmyGet.getAssessKnj());
                        naiyouNameList
                                .add(CommonConstants.NEWLINE_CHARACTOR + CommonConstants.NEWLINE_CHARACTOR);
                    } else {
                        // (2). 上記以外の場合
                        if (index == CommonConstants.INT_0) {
                            // ①.１行目の場合
                            koumokuNameList.add(kghKrkFaceSavAss2DmyGet.getBunruiKnj());
                            naiyouNameList
                                    .add(CommonConstants.NEWLINE_CHARACTOR + CommonConstants.NEWLINE_CHARACTOR);
                        } else {
                            // ②.上記以外の場合
                            if (!CommonDtoUtil.checkStringEqual(bunruiKnj,
                                    kghKrkFaceSavAss2DmyGet.getBunruiKnj())) {
                                // 取得した分類名称と前の行で取得した分類名称が異なる場合
                                koumokuNameList.add(kghKrkFaceSavAss2DmyGet.getBunruiKnj());
                                naiyouNameList
                                        .add(CommonConstants.NEWLINE_CHARACTOR
                                                + CommonConstants.NEWLINE_CHARACTOR);
                            }
                        }
                    }
                    bunruiKnj = kghKrkFaceSavAss2DmyGet.getBunruiKnj();
                    index++;
                }
            }
        }

        return koumokuTitle;

    }

    /**
     * 属性のデフォルト値設定
     * 
     * @param obj 値設定対象
     */
    public void setPropertyDefaultValue(Object obj) {
        if (Objects.isNull(obj)) {
            return;
        }

        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            try {
                Class<?> fieldType = field.getType();
                field.setAccessible(true);
                if (fieldType == String.class) {
                    setDefaultValue(obj, field, CommonConstants.BLANK_STRING);
                } else if (fieldType == Integer.class) {
                    setDefaultValue(obj, field, CommonConstants.INT_0);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * デフォルト値設定
     * 
     * @param obj          値設定対象
     * @param field        対象の属性
     * @param defaultValue デフォルト値
     * @throws Exception 例外
     */
    private static void setDefaultValue(Object obj, Field field, Object defaultValue) throws Exception {
        String fieldName = field.getName();
        String setterName = "set" + fieldName.substring(CommonConstants.INT_0, CommonConstants.INT_1).toUpperCase()
                + fieldName.substring(CommonConstants.INT_1);
        Method setterMethod = obj.getClass().getMethod(setterName, field.getType());
        if (setterMethod != null) {
            setterMethod.invoke(obj, defaultValue);
        } else {
            field.set(obj, defaultValue);
        }
    }

    /**
     * 出力対象区分を取得する。
     * 
     * @param type 帳票区分
     */
    public static void setOutputTargets(Integer type) {
        for (Map.Entry<String, String> entry : OUTPUT_TARGET_CONSTANTS.entrySet()) {
            StringBuilder koumokuKbnBuilder = new StringBuilder();
            koumokuKbnBuilder.append(ReportConstants.OUTPUT_PREFIX)
                    .append(type)
                    .append(CommonConstants.KASEN_STRING)
                    .append(entry.getValue())
                    .append(CommonConstants.KASEN_STRING)
                    .append(ReportConstants.OUTPUT_SUFFIX);
            OUTPUT_TARGET_CONSTANTS.put(entry.getKey(), koumokuKbnBuilder.toString());
        }
    }

    /**
     * 年齢を計算する
     * 
     * @param birthdayYmd 誕生日 yyyy/MM/dd型
     * @param systemDate  判断日 yyyy/MM/dd型
     * @return 年齢(X歳)
     * @throws Exception 例外
     */
    public String getAgeStr(String birthdayYmd, String systemDate) throws Exception {
        return CommonDtoUtil.objValToString(
                kghKhnFunc01Logic.getAge4(birthdayYmd,
                        ReportUtil.getByteLength(systemDate) > CommonConstants.INT_10
                                ? systemDate.substring(CommonConstants.INT_0, CommonConstants.INT_10)
                                : systemDate));
    }

    /**
     * 金額のフォーマット(#,###,###)
     * 
     * @param amount 金額
     * @return フォーマットの金額
     */
    public String formatAmount(Number amount) {
        String formatAmount = CommonConstants.BLANK_STRING;
        if (null != amount) {
            DecimalFormat formatter = new DecimalFormat("#,###");
            formatAmount = formatter.format(amount);
        }
        return formatAmount;
    }

    /**
     * 値設定
     * 
     * @param inDto  値設定オブジェクト
     * @param data   フラグフィールド以外の値
     * @param flg    フラグフィールドの値
     * @param index  インデックス
     * @param isFlag フラグフィールドかどうか
     * @throws Exception 例外
     */
    private void setDynamicData(AssessmentFaceSheetTypeReportServiceInDto inDto, JRBeanCollectionDataSource data,
            Integer flg, Integer index, boolean isFlag) throws Exception {
        Field field = AssessmentFaceSheetTypeReportServiceInDto.class
                .getDeclaredField((isFlag ? "flg" : "database") + index);
        field.setAccessible(true);
        field.set(inDto, isFlag ? flg : data);
    }

    /**
     * 出力対象初期化
     * 
     * @param outputList 出力対象リスト
     */
    public void setKoumokuSortList(List<String> outputList) {
        this.koumokuSortList = outputList;
    }

    /**
     * 出力対象を取得
     * 
     * @return 出力対象リスト
     */
    public List<String> getKoumokuSortList() {
        return this.koumokuSortList;
    }
}