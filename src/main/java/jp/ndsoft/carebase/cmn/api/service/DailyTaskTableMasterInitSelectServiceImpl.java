package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.logic.SvJigyoInfoLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.DailyTaskTableMasterInitSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.DailyTaskTableMasterInitSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * GUI00987_日課表マスタ情報取得サービス.
 * 
 * @since 2025.05.12
 * <AUTHOR>
 */
@Service
public class DailyTaskTableMasterInitSelectServiceImpl extends
        SelectServiceImpl<DailyTaskTableMasterInitSelectServiceInDto, DailyTaskTableMasterInitSelectServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    /** 初期設定マスタ情報取得DAO */
    @Autowired
    private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;

    /** サービス事業者名称取得 */
    @Autowired
    private SvJigyoInfoLogic svJigyoInfoLogic;

    /**
     * メモ分類3
     */
    private static final Integer MEMOBUNRUI3 = 1;
    /**
     * メモ分類3
     */
    private static final String MEMOBUNRUI3_STRING = "1";
    /**
     * 時間（初期値）分類3
     */
    private static final Integer TIMEINITIALBUNRUI3 = 2;
    /**
     * 時間（初期値）分類3
     */
    private static final String TIMEINITIALBUNRUI3_STRING = "2";
    /**
     * 分類１
     */
    private static final Integer BUNRUI1ID = 2;
    /**
     * 分類２
     */
    private static final Integer BUNRUI2ID = 18;

    /** メモ デフォルト値（1:表示する） */
    private static final String MEMO_DEFAUL = "1";

    /** 時間（初期値） デフォルト値（0:表示しない） */
    private static final String TIMEINITIAL_DEFAUL = "2";

    /**
     * 日課表マスタ情報取得
     * 
     * @param inDto 日課表マスタ画面の入力DTO.
     * @return 日課表マスタ出力DTO
     * @throws Exception Exception
     */
    @Override
    public DailyTaskTableMasterInitSelectServiceOutDto mainProcess(
            DailyTaskTableMasterInitSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        // DAOパラメータを作成
        KrkSsmInfoByCriteriaInEntity krkSsmInfoByCriteriaInEntity = new KrkSsmInfoByCriteriaInEntity();
        // リクエストパラメータ.施設ID
        krkSsmInfoByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // リクエストパラメータ.事業所ID
        krkSsmInfoByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 固定:2
        krkSsmInfoByCriteriaInEntity.setBunrui1Id(BUNRUI1ID);
        // 固定:18
        krkSsmInfoByCriteriaInEntity.setBunrui2Id(BUNRUI2ID);
        // DAOを実行
        List<KrkSsmInfoOutEntity> krkSsmInfoList = this.kghMocKrkSsmSelectMapper
                .findKrkSsmInfoByCriteria(krkSsmInfoByCriteriaInEntity);
        DailyTaskTableMasterInitSelectServiceOutDto outDto = new DailyTaskTableMasterInitSelectServiceOutDto();
        // メモ初期化
        outDto.setMemo(MEMO_DEFAUL);
        // 時間（初期値）初期化
        outDto.setTimeInitial(TIMEINITIAL_DEFAUL);
        for (KrkSsmInfoOutEntity krkSsmInfoOutEntity : krkSsmInfoList) {
            // 分類３は1が存在する場合
            if (krkSsmInfoOutEntity.getBunrui3Id().equals(MEMOBUNRUI3)) {
                // メモ分類3
                outDto.setMemoBunrui3(MEMOBUNRUI3_STRING);
                // メモ
                outDto.setMemo(CommonDtoUtil.objValToString(krkSsmInfoOutEntity.getIntValue()));
                // メモ更新回数
                // outDto.setMemoModifiedCnt(CommonDtoUtil.objValToString(krkSsmInfoOutEntity.getModifiedCnt()));
            }
            // 分類３は2が存在する場合
            if (krkSsmInfoOutEntity.getBunrui3Id().equals(TIMEINITIALBUNRUI3)) {
                // 時間（初期値）分類3
                outDto.setTimeInitialBunrui3(TIMEINITIALBUNRUI3_STRING);
                // 時間（初期値）
                outDto.setTimeInitial(CommonDtoUtil.objValToString(krkSsmInfoOutEntity.getIntValue()));
                // 時間（初期値）更新回数
                // outDto.setTimeInitialModifiedCnt(CommonDtoUtil.objValToString(krkSsmInfoOutEntity.getModifiedCnt()));
            }
        }
        // 事業所名取得
        outDto.setSvJigyoInfoList(svJigyoInfoLogic.getSvJigyoInfoList(inDto.getSvJigyoIdList()));
        LOG.info(Constants.END);
        return outDto;
    }
}
