package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * @since 2025.05.23
 * <AUTHOR> BUI VIET ANH
 * @implNote GUI00926_阻害要因入力支援 DTO.
 */
@Getter
@Setter
public class InhibitoryFactorInfoSelectServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 事業者ID */
    @NotEmpty
    private String svJigyoId;
}
