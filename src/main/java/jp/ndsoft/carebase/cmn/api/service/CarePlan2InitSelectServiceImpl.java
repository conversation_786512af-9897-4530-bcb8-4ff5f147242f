package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.*;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZSnc01Logic;
import jp.ndsoft.carebase.cmn.api.service.dto.CarePlan2InitSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.CarePlan2InitSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.*;
import jp.ndsoft.carebase.common.dao.mysql.mapper.*;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.invoke.MethodHandles;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * GUI01014_計画書（２）初期情報取得サービス.
 *
 * <AUTHOR>
 */
@Service
public class CarePlan2InitSelectServiceImpl
        extends SelectServiceImpl<CarePlan2InitSelectServiceInDto, CarePlan2InitSelectServiceOutDto> {
    /**
     * ロガー
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /**
     * 計画期間ID 0（固定）
     */
    private static final Integer SC_ID_ZERO = 0;

    /**
     * 計画対象期間情報取得
     */
    @Autowired
    private KghTucKrkKikanSelectMapper kghTucKrkKikanSelectMapper;
    /**
     * 職員基本情報取得
     */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;
    /**
     * 履歴情報の取得
     */
    @Autowired
    private CpnTucCks21SelectMapper cpnTucCks21SelectMapper;
    /**
     * 計画書（２）詳細情報の取得
     */
    @Autowired
    private CpnTucCks22SelectMapper cpnTucCks22SelectMapper;
    /**
     * 保険サービス情報を取得
     */
    @Autowired
    private CpnTucCks211SelectMapper cpnTucCks211SelectMapper;
    /**
     * 月日指定情報を取得
     */
    @Autowired
    private CpnTucCks212SelectMapper cpnTucCks212SelectMapper;
    /**
     * サービス曜日情報を取得
     */
    @Autowired
    private CpnTucCks221SelectMapper cpnTucCks221SelectMapper;
    /**
     * サービス曜日情報を取得
     */
    @Autowired
    private CpnTucCks222SelectMapper cpnTucCks222SelectMapper;
    /**
     * CpnTucCks224SelectMapper
     */
    @Autowired
    private CpnTucCks224SelectMapper cpnTucCks224SelectMapper;
    /**
     * 老健カルテプロブレム件数を取得
     */
    @Autowired
    private KghTucRoukenKarteProblemSelectMapper kghTucRoukenKarteProblemSelectMapper;
    /**
     * サービス事業者より事業者情報を取得
     */
    @Autowired
    private ComMscSvjigyoJigyoKnj2SelectMapper comMscSvjigyoJigyoKnj2SelectMapper;
    /**
     * 期間管理フラグ取得
     */
    @Autowired
    private KghKrkZSnc01Logic kghKrkZSnc01Logic;
    /**
     * サービス利用状況（９－１）、サービス事業者マスタ（１５－５）
     */
    @Autowired
    private ComGetUserSvriyoEndAllSelectMapper comGetUserSvriyoEndAllSelectMapper;

    /**
     * 計画書（２）初期情報取得
     *
     * @param inDto 計画書（２）初期情報取得の入力DTO.
     * @return 計画書（２）初期情報取得の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected CarePlan2InitSelectServiceOutDto mainProcess(CarePlan2InitSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        // 出力Dto
        CarePlan2InitSelectServiceOutDto outDto = new CarePlan2InitSelectServiceOutDto();
        // 2. 共通部品「f_kgh_krk_get_kikan」を利用し、期間管理フラグを取得する。
        boolean kikanFlg = kghKrkZSnc01Logic.getKghKrkKikan(CommonDtoUtil.strValToInt(inDto.getSyubetsuId()),
                CommonDtoUtil.strValToInt(inDto.getSvJigyoId()), CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        String strKikanFlg = StringUtils.EMPTY;
        // 期間管理フラグ
        if (kikanFlg) {
            // T:管理
            strKikanFlg = CommonConstants.KIKANFLG_T;
        } else {
            // F:管理しない
            strKikanFlg = CommonConstants.KIKANFLG_F;
        }
        // 出力Dto: 期間管理フラグ
        outDto.setKikanFlg(strKikanFlg);
        // 計画対象期間情報
        List<KghTucKrkKikanOutEntity> kikanOutList = new ArrayList<KghTucKrkKikanOutEntity>();
        // 計画対象期間情報の１件目
        KghTucKrkKikanOutEntity kikanHeadOutInfo = null;
        // 4. 職員氏名情報の取得
        // 下記の職員基本情報取得のDAOを利用し、職員氏名情報を取得する。
        List<ShokuinInfoListSpaceSortOutEntity> comMscInfo = comMscShokuinSelectMapper
                .findShokuinInfoListSpaceSortByCriteria(new ShokuinInfoListSpaceSortByCriteriaInEntity());
        // 履歴情報.計画書（２）ID
        Gui01014Rireki gui01014Rireki = null;
        // 上記2.で取得した期間管理フラグが管理の場合、計画対象期間情報を取得する。(計画対象期間情報１件目だけレスポンス情報に設定する。)
        if (CommonConstants.KIKANFLG_T.equals(strKikanFlg)) {
            // 3. 上記2.で取得した期間管理フラグが管理の場合、計画対象期間情報を取得する
            kikanOutList = acquKikanOutList(inDto);
            // 計画期間情報設定する
            if (CollectionUtils.isNotEmpty(kikanOutList)) {
                kikanHeadOutInfo = kikanOutList.getFirst();
                outDto.setKikanObj(editPlanInfo(kikanOutList, kikanHeadOutInfo));

                // 5. 履歴情報の取得
                // 5.1. 上記2.で取得した期間管理フラグが管理の場合、計画対象期間情報の１件目より、履歴情報を取得する。
                gui01014Rireki = acquCks21OutList(kikanHeadOutInfo.getSc1Id(), kikanHeadOutInfo.getSvJigyoId(),
                        kikanHeadOutInfo.getUserId(), comMscInfo);
            }
        } else {
            // 5. 履歴情報の取得
            // 5.2. 上記2.で取得した期間管理フラグが管理しない場合、履歴情報を取得する。
            gui01014Rireki = acquCks21OutList(SC_ID_ZERO, CommonDtoUtil.strValToInt(inDto.getSvJigyoId()),
                    CommonDtoUtil.strValToInt(inDto.getUserId()), comMscInfo);
        }
        // 履歴情報
        List<Gui01014Rireki> gui01014RirekiList = new ArrayList<>();
        gui01014RirekiList.add(gui01014Rireki);
        outDto.setRirekiObj(gui01014RirekiList);
        if (gui01014Rireki != null) {
            Integer hisKs21Id = CommonDtoUtil.strValToInt(gui01014Rireki.getKs21Id());
            // 6. 計画書（２）詳細データを取得する。
            outDto.setKeikasyo2List(acquCpnOutInfo(hisKs21Id));

            // 6.2. 計画書様式が居宅の場合
            if (CommonConstants.CKS_FLG_HOME.equals(inDto.getCksflg())) {
                // 保険サービスリスト
                outDto.setHokenList(acquHokenList(hisKs21Id));
                // 月日指定リスト
                outDto.setTukihiList(acquTukihiList(hisKs21Id));
            } // 6.3. 計画書様式が施設の場合
            else if (CommonConstants.CKS_FLG_FACILITY.equals(inDto.getCksflg())) {
                // 6.3.1. サービス曜日情報を取得する。
                List<Gui01014Yobi> yobiList = acquYobiList(hisKs21Id);
                // 6.3.2. サービス曜日情報を取得する。
                List<Gui01014Tanto> tantoList = acquTantoList(hisKs21Id);
                for (Gui01014Keikasyo2 kasyo2Info : outDto.getKeikasyo2List()) {
                    // サービス曜日リスト
                    List<Gui01014Yobi> filterYobiList = yobiList.stream()
                            // 計画書（２）行データID ＝ 計画書（２）リスト.カウンター
                            .filter(item -> item.getKs22Id().equals(kasyo2Info.getKs22Id())
                                    // 計画書（２）ID = 計画書（２）リスト.計画書（２）ID
                                    && item.getKs21Id().equals(kasyo2Info.getKs21Id()))
                            .collect(Collectors.toList());
                    kasyo2Info.setYobiList(filterYobiList);

                    // 担当者リスト
                    List<Gui01014Tanto> filterTantoList = tantoList.stream()
                            // 計画書（２）行データID ＝ 計画書（２）リスト.カウンター
                            .filter(item -> item.getKs22Id().equals(kasyo2Info.getKs22Id())
                                    // 計画書（２）ID = 計画書（２）リスト.計画書（２）ID
                                    && item.getKs21Id().equals(kasyo2Info.getKs21Id()))
                            .collect(Collectors.toList());
                    kasyo2Info.setTantoList(filterTantoList);
                }
            }

            // 6.4. リクエストパラメータ.記録との連携が「1」:記録との連携を行うの場合
            if (CommonConstants.STR_1.equals(inDto.getKirokuRenkeiFlg())) {
                CpnTucCks224InfoByCriteriaInEntity cpnTucCks224InfoIn = new CpnTucCks224InfoByCriteriaInEntity();
                cpnTucCks224InfoIn.setKs21Id(hisKs21Id);
                List<CpnTucCks224InfoOutEntity> cpnTucCks224InfoOuts = cpnTucCks224SelectMapper.findCpnTucCks224InfoByCriteria(
                        cpnTucCks224InfoIn);

                Map<String, CpnTucCks224InfoOutEntity> cpnTucCks224InfoMap = new HashMap<>();
                cpnTucCks224InfoOuts.forEach((cpnTucCks224Info) -> {
                    cpnTucCks224InfoMap.put(CommonDtoUtil.objValToString(cpnTucCks224Info.getKs22Id()),
                            cpnTucCks224Info);
                });

                outDto.getKeikasyo2List().forEach((keikasyo2) -> {
                    CpnTucCks224InfoOutEntity cpnTucCks224Info = cpnTucCks224InfoMap.get(keikasyo2.getKs22Id());
                    if (cpnTucCks224Info != null) {
                        keikasyo2.setNikkaId(CommonDtoUtil.objValToString(cpnTucCks224Info.getNikkaId()));
                    }
                });
            }
        }
        // 7. 取込フラグの編集
        outDto.setPlFlg(acquReadFlg(inDto));
        // 8. 適用事業所一覧情報の取得
        // 8.1. サービス事業者より事業者情報を取得する。
        KghStringComMscSvjigyoJigyoKnj2ByCriteriaInEntity svjigyoInfoByCriteriaInEntity = new KghStringComMscSvjigyoJigyoKnj2ByCriteriaInEntity();

        // リクエストパラメータ.サービス事業者IDリスト
        svjigyoInfoByCriteriaInEntity
                .setLSvJigyoIdList(inDto.getSvJigyoIdList().stream().map(id -> CommonDtoUtil.strValToInt(id)).toList());
        List<KghStringComMscSvjigyoJigyoKnj2OutEntity> svjiOutList = comMscSvjigyoJigyoKnj2SelectMapper
                .findKghStringComMscSvjigyoJigyoKnj2ByCriteria(svjigyoInfoByCriteriaInEntity);
        // 8.2. サービス利用状況（９－１）、サービス事業者マスタ（１５－５）より、利用可能サービス事業者を洗出し
        KghComGetUserSvriyoEndAllByCriteriaInEntity kghComInEntity = new KghComGetUserSvriyoEndAllByCriteriaInEntity();
        // List<String>->List<Integer>
        List<Integer> paramSvJigyoIdList = inDto.getSvJigyoIdList().stream().map(str -> {
            try {
                return CommonDtoUtil.strValToInt(str);
            } catch (NumberFormatException e) {
                return null;
            }
        }).filter(num -> num != null).collect(Collectors.toList());
        // サービス事業者ID リクエストパラメータ.サービス事業者IDリスト
        kghComInEntity.setJidsList(paramSvJigyoIdList);
        // 利用者ID リクエストパラメータ.利用者ID
        kghComInEntity.setUid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 利用開始日 システム日付
        DateTimeFormatter formattere = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        kghComInEntity.setYmd(LocalDate.now().format(formattere));
        List<KghComGetUserSvriyoEndAllOutEntity> kghComOutList = comGetUserSvriyoEndAllSelectMapper
                .findKghComGetUserSvriyoEndAllByCriteria(kghComInEntity);
        // サービス事業者情報
        List<String> khgComCdList = kghComOutList.stream().map(KghComGetUserSvriyoEndAllOutEntity::getSvJigyoCd)
                .collect(Collectors.toList());
        List<Gui01014JigyoInfo> jigyoList = new ArrayList<Gui01014JigyoInfo>();
        for (KghStringComMscSvjigyoJigyoKnj2OutEntity svjiOutInfo : svjiOutList) {
            // 8.3. 事業者情報より繰り返し、利用可能サービス事業者情報に存在しない場合、事業者情報から削除する。
            if (!khgComCdList.contains(svjiOutInfo.getSvJigyoCd())) {
                continue;
            }
            // 適用事業所一覧情報の編集
            jigyoList.add(editJigyoInfo(svjiOutInfo));
        }
        outDto.setJigyoList(jigyoList);

        LOG.info(Constants.END);

        return outDto;
    }

    /**
     * 計画対象期間情報取得検索パラメータ作成
     *
     * @param inDto 計画書（２）初期情報取得の入力DTO.
     * @return 検索パラメータ
     */
    private List<KghTucKrkKikanOutEntity> acquKikanOutList(CarePlan2InitSelectServiceInDto inDto) {
        // 計画対象期間情報取得検索パラメータ作成
        KghTucKrkKikanByCriteriaInEntity criteriaInEntity = new KghTucKrkKikanByCriteriaInEntity();
        // 種別ID
        criteriaInEntity.setSyubetsuId(CommonDtoUtil.strValToInt(inDto.getSyubetsuId()));
        // 事業者ID
        criteriaInEntity.setJId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 施設ID
        criteriaInEntity.setSId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 利用者ID
        criteriaInEntity.setUId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 計画対象期間情報取得
        return kghTucKrkKikanSelectMapper.findKghTucKrkKikanByCriteria(criteriaInEntity);
    }

    /**
     * 計画期間情報の編集
     *
     * @param kikanOutList     計画対象期間情報
     * @param kikanHeadOutInfo 計画対象期間情報の１件目
     * @return 計画期間情報
     */
    private List<Gui01014Kikan> editPlanInfo(List<KghTucKrkKikanOutEntity> kikanOutList,
            KghTucKrkKikanOutEntity kikanHeadOutInfo) {
        Gui01014Kikan kikan = new Gui01014Kikan();
        // 期間ID
        kikan.setSc1Id(CommonDtoUtil.objValToString(kikanHeadOutInfo.getSc1Id()));
        // 開始日
        kikan.setStartYmd(kikanHeadOutInfo.getStartYmd());
        // 終了日
        kikan.setEndYmd(kikanHeadOutInfo.getEndYmd());
        // 計画対象期間インデックス
        kikan.setKikanIndex(CommonDtoUtil.objValToString(kikanOutList.size()));

        kikan.setKikanTotal(CommonDtoUtil.objValToString(kikanOutList.size()));
        List<Gui01014Kikan> kikanList = new ArrayList<>();
        kikanList.add(kikan);
        return kikanList;
    }

    /**
     * 履歴情報の取得
     *
     * @param sc1Id      計画期間ID
     * @param svJigyoId  事業者ID
     * @param userId     利用者ID
     * @param comMscInfo 職員氏名情報
     * @return 履歴情報
     */
    private Gui01014Rireki acquCks21OutList(Integer sc1Id, Integer svJigyoId, Integer userId,
            List<ShokuinInfoListSpaceSortOutEntity> comMscInfo) {
        Keikaku2HeadInfoListByCriteriaInEntity keikaku2HeadInfoListByCriteriaInEntity = new Keikaku2HeadInfoListByCriteriaInEntity();
        // 計画期間ID.計画対象期間情報.期間ID
        keikaku2HeadInfoListByCriteriaInEntity.setSc1(sc1Id);
        // 事業者ID.計画対象期間情報.事業者ID
        keikaku2HeadInfoListByCriteriaInEntity.setSvJigyoId(svJigyoId);
        // 利用者ID.計画対象期間情報.利用者ID
        keikaku2HeadInfoListByCriteriaInEntity.setUserid(userId);
        List<Keikaku2HeadInfoListOutEntity> cks21OutList = cpnTucCks21SelectMapper
                .findKeikaku2HeadInfoListByCriteria(keikaku2HeadInfoListByCriteriaInEntity);

        // 履歴情報設定する
        Gui01014Rireki rirekiInfo = null;
        if (CollectionUtils.isNotEmpty(cks21OutList)) {
            Keikaku2HeadInfoListOutEntity cks21Info = cks21OutList.getFirst();
            rirekiInfo = new Gui01014Rireki();
            // 計画書（２）ID
            rirekiInfo.setKs21Id(CommonDtoUtil.objValToString(cks21Info.getKs21Id()));
            // 作成日
            rirekiInfo.setCreateYmd(cks21Info.getCreateYmd());
            // 作成者ID
            rirekiInfo.setShokuId(CommonDtoUtil.objValToString(cks21Info.getShokuId()));
            // 作成者名
            String outShokuKnj = null;
            if (CollectionUtils.isNotEmpty(comMscInfo)) {
                ShokuinInfoListSpaceSortOutEntity shokuiOutHead = comMscInfo.stream()
                        .filter(shokuin -> shokuin.getChkShokuId().equals(cks21Info.getShokuId())).findFirst()
                        .orElse(null);
                if (shokuiOutHead != null) {
                    // 職員名（姓） + 半角スペース + 職員名（名）
                    outShokuKnj = CommonDtoUtil.strNullToEmpty(shokuiOutHead.getShokuin1Knj()).concat(StringUtils.SPACE)
                            .concat(CommonDtoUtil.strNullToEmpty(shokuiOutHead.getShokuin2Knj()));
                }
            }
            rirekiInfo.setShokuKnj(outShokuKnj);
            // 有効期間ID
            rirekiInfo.setTermid(CommonDtoUtil.objValToString(cks21Info.getTermid()));
            // 履歴インデックス
            rirekiInfo.setRirekiIndex(CommonDtoUtil.objValToString(cks21OutList.size()));
            // 履歴件数
            rirekiInfo.setRirekiCnt(CommonDtoUtil.objValToString(cks21OutList.size()));

        }
        return rirekiInfo;
    }

    /**
     * 6. 計画書（２）詳細データを取得する。
     *
     * @param hisKs21Id 履歴情報.計画書（２）ID
     * @return 計画書（２）詳細データ
     */
    private List<Gui01014Keikasyo2> acquCpnOutInfo(Integer hisKs21Id) {
        List<Gui01014Keikasyo2> planTwoOutList = new ArrayList<Gui01014Keikasyo2>();
        // 6.1. 計画書（２）詳細情報を取得する。
        CpnTucCks22Ns1SortInfoByCriteriaInEntity cks22InInfo = new CpnTucCks22Ns1SortInfoByCriteriaInEntity();
        // 計画書（２）ID 履歴情報.計画書（２）ID
        cks22InInfo.setKs21(hisKs21Id);
        List<CpnTucCks22Ns1SortInfoOutEntity> cks22OutList = cpnTucCks22SelectMapper
                .findCpnTucCks22Ns1SortInfoByCriteria(cks22InInfo);

        // 計画書（２）リストの編集
        for (CpnTucCks22Ns1SortInfoOutEntity cks22OutInfo : cks22OutList) {
            planTwoOutList.add(editPlanTwoInfo(cks22OutInfo));
        }
        return planTwoOutList;
    }

    /**
     * 6.2.1. 保険サービス情報を取得する。
     *
     * @param hisKs21Id 履歴情報.計画書（２）ID
     * @return 保険サービス情報
     */
    private List<Gui01014Hoken> acquHokenList(Integer hisKs21Id) {
        // 6.2.1. 保険サービス情報を取得する。
        CpnTucCks211InfoByCriteriaInEntity cpnTucCks211InfoByCriteriaInEntity = new CpnTucCks211InfoByCriteriaInEntity();
        // 履歴情報.計画書（２）ID
        cpnTucCks211InfoByCriteriaInEntity.setAiKs21Id(hisKs21Id);
        List<CpnTucCks211InfoOutEntity> cks211OutList = cpnTucCks211SelectMapper
                .findCpnTucCks211InfoByCriteria(cpnTucCks211InfoByCriteriaInEntity);
        // 保険サービスリストの編集
        List<Gui01014Hoken> hokenList = new ArrayList<Gui01014Hoken>();
        for (CpnTucCks211InfoOutEntity cks211OutInfo : cks211OutList) {
            hokenList.add(editInsuranceInfo(cks211OutInfo));
        }
        return hokenList;
    }

    /**
     * 6.2.2. 月日指定情報を取得する。
     *
     * @param hisKs21Id 履歴情報.計画書（２）ID
     * @return 月日指定情報
     */
    private List<Gui01014Tukihi> acquTukihiList(Integer hisKs21Id) {
        // 6.2.2. 月日指定情報を取得する。
        Keikaku2TukihiInfoByCriteriaInEntity keikaku2TukihiInfoByCriteriaInEntity = new Keikaku2TukihiInfoByCriteriaInEntity();
        // 履歴情報.計画書（２）ID
        keikaku2TukihiInfoByCriteriaInEntity.setAiKs21Id(hisKs21Id);
        List<Keikaku2TukihiInfoOutEntity> cks212OutList = cpnTucCks212SelectMapper
                .findKeikaku2TukihiInfoByCriteria(keikaku2TukihiInfoByCriteriaInEntity);
        // 月日指定リストの編集
        List<Gui01014Tukihi> tukihiList = new ArrayList<Gui01014Tukihi>();
        for (Keikaku2TukihiInfoOutEntity cks212OutInfo : cks212OutList) {
            tukihiList.add(editTulohInfo(cks212OutInfo));
        }
        return tukihiList;
    }

    /**
     * 6.3.1. サービス曜日情報を取得する。
     *
     * @param hisKs21Id 履歴情報.計画書（２）ID
     * @return サービス曜日情報
     */
    private List<Gui01014Yobi> acquYobiList(Integer hisKs21Id) {
        // 6.3.1. サービス曜日情報を取得する。
        CpnTucCks221InfoByCriteriaInEntity cpnTucCks221InfoByCriteriaInEntity = new CpnTucCks221InfoByCriteriaInEntity();
        // 履歴情報.計画書（２）ID
        cpnTucCks221InfoByCriteriaInEntity.setAiKs21Id(hisKs21Id);
        List<CpnTucCks221InfoOutEntity> cks221OutList = cpnTucCks221SelectMapper
                .findCpnTucCks221InfoByCriteria(cpnTucCks221InfoByCriteriaInEntity);
        // サービス曜日リストの編集
        List<Gui01014Yobi> yobiList = new ArrayList<Gui01014Yobi>();
        for (CpnTucCks221InfoOutEntity cks221OutInfo : cks221OutList) {
            yobiList.add(editYobiInfo(cks221OutInfo));
        }
        return yobiList;
    }

    /**
     * 6.3.2. サービス曜日情報を取得する。
     *
     * @param hisKs21Id 履歴情報.計画書（２）ID
     * @return 担当者リスト
     */
    private List<Gui01014Tanto> acquTantoList(Integer hisKs21Id) {
        // 6.3.2. サービス曜日情報を取得する。
        CpnTucCks222InfoByCriteriaInEntity cpnTucCks222InfoByCriteriaInEntity = new CpnTucCks222InfoByCriteriaInEntity();
        // 履歴情報.計画書（２）ID
        cpnTucCks222InfoByCriteriaInEntity.setAiKs21Id(hisKs21Id);
        List<CpnTucCks222InfoOutEntity> cks222OutList = cpnTucCks222SelectMapper
                .findCpnTucCks222InfoByCriteria(cpnTucCks222InfoByCriteriaInEntity);
        // 担当者リストの編集
        List<Gui01014Tanto> tantoList = new ArrayList<Gui01014Tanto>();
        for (CpnTucCks222InfoOutEntity cks222OutInfo : cks222OutList) {
            tantoList.add(editGuiTantoInfo(cks222OutInfo));
        }
        return tantoList;
    }

    /**
     * 計画書（２）リストの編集
     *
     * @param cks22OutInfo 計画書（２）詳細情報
     * @return 計画書（２）リスト
     */
    private Gui01014Keikasyo2 editPlanTwoInfo(CpnTucCks22Ns1SortInfoOutEntity cks22OutInfo) {
        Gui01014Keikasyo2 planTwoOutInfo = new Gui01014Keikasyo2();
        // カウンター
        planTwoOutInfo.setKs22Id(CommonDtoUtil.objValToString(cks22OutInfo.getKs22Id()));
        // 計画書（２）ID
        planTwoOutInfo.setKs21Id(CommonDtoUtil.objValToString(cks22OutInfo.getKs21Id()));
        // 具体的
        planTwoOutInfo.setGutaitekiKnj(cks22OutInfo.getGutaitekiKnj());
        // 長期
        planTwoOutInfo.setChoukiKnj(cks22OutInfo.getChoukiKnj());
        // 短期
        planTwoOutInfo.setTankiKnj(cks22OutInfo.getTankiKnj());
        // 介護
        planTwoOutInfo.setKaigoKnj(cks22OutInfo.getKaigoKnj());
        // サービス種
        planTwoOutInfo.setSvShuKnj(cks22OutInfo.getSvShuKnj());
        // 頻度
        planTwoOutInfo.setHindoKnj(cks22OutInfo.getHindoKnj());
        // 期間
        planTwoOutInfo.setKikanKnj(cks22OutInfo.getKikanKnj());
        // 通番
        planTwoOutInfo.setSeq(CommonDtoUtil.objValToString(cks22OutInfo.getSeq()));
        // 課題番号
        planTwoOutInfo.setKadaiNo(CommonDtoUtil.objValToString(cks22OutInfo.getKadaiNo()));
        // 介護番号
        planTwoOutInfo.setKaigoNo(CommonDtoUtil.objValToString(cks22OutInfo.getKaigoNo()));
        // 長期期間
        planTwoOutInfo.setChoKikanKnj(cks22OutInfo.getChoKikanKnj());
        // 短期期間
        planTwoOutInfo.setTanKikanKnj(cks22OutInfo.getTanKikanKnj());
        // 給付対象
        planTwoOutInfo.setHkyuKbn(CommonDtoUtil.objValToString(cks22OutInfo.getHkyuKbn()));
        // サービス事業者ＣＤ
        planTwoOutInfo.setJigyouId(CommonDtoUtil.objValToString(cks22OutInfo.getJigyouId()));
        // サービス事業者名
        planTwoOutInfo.setJigyoNameKnj(cks22OutInfo.getJigyoNameKnj());
        // 給付文字
        planTwoOutInfo.setHkyuKnj(cks22OutInfo.getHkyuKnj());
        // 長期期間開始日
        planTwoOutInfo.setChoSYmd(cks22OutInfo.getChoSYmd());
        // 長期期間終了日
        planTwoOutInfo.setChoEYmd(cks22OutInfo.getChoEYmd());
        // 短期期間開始日
        planTwoOutInfo.setTanSYmd(cks22OutInfo.getTanSYmd());
        // 短期期間終了日
        planTwoOutInfo.setTanEYmd(cks22OutInfo.getTanEYmd());
        // 期間開始日
        planTwoOutInfo.setKikanSYmd(cks22OutInfo.getKikanSYmd());
        // 期間終了日
        planTwoOutInfo.setKikanEYmd(cks22OutInfo.getKikanEYmd());
        return planTwoOutInfo;
    }

    /**
     * 保険サービスリストの編集
     *
     * @param cks211OutInfo 保険サービス情報
     * @return 保険サービスリスト
     */
    private Gui01014Hoken editInsuranceInfo(CpnTucCks211InfoOutEntity cks211OutInfo) {
        Gui01014Hoken insuranceInfo = new Gui01014Hoken();
        // カウンター
        insuranceInfo.setKs211Id(CommonDtoUtil.objValToString(cks211OutInfo.getKs211Id()));
        // 計画書（２）ID
        insuranceInfo.setKs21Id(CommonDtoUtil.objValToString(cks211OutInfo.getKs21Id()));
        // 曜日
        insuranceInfo.setYoubi(cks211OutInfo.getYoubi());
        // 週単位以外のｻｰﾋﾞｽ区分
        insuranceInfo.setIgaiKbn(CommonDtoUtil.objValToString(cks211OutInfo.getIgaiKbn()));
        // 週単位以外のｻｰﾋﾞｽ（日付指定）
        insuranceInfo.setIgaiDate(cks211OutInfo.getIgaiDate());
        // 週単位以外のｻｰﾋﾞｽ（曜日指定）
        insuranceInfo.setIgaiWeek(cks211OutInfo.getIgaiWeek());
        // 居宅：開始時間
        insuranceInfo.setKaishiJikan(cks211OutInfo.getKaishiJikan());
        // 居宅：終了時間
        insuranceInfo.setShuuryouJikan(cks211OutInfo.getShuuryouJikan());
        // 居宅：サービス種類
        insuranceInfo.setSvShuruiCd(cks211OutInfo.getSvShuruiCd());
        // 居宅：サービス項目（台帳）
        insuranceInfo.setSvItemCd(CommonDtoUtil.objValToString(cks211OutInfo.getSvItemCd()));
        // 居宅：サービス事業者CD
        insuranceInfo.setSvJigyoId(CommonDtoUtil.objValToString(cks211OutInfo.getSvJigyoId()));
        // 居宅：福祉用具貸与単位
        insuranceInfo.setTanka(CommonDtoUtil.objValToString(cks211OutInfo.getTanka()));
        // 福祉用具貸与商品コード
        insuranceInfo.setFygId(CommonDtoUtil.objValToString(cks211OutInfo.getFygId()));
        // 合成識別区分
        insuranceInfo.setGouseiSikKbn(cks211OutInfo.getGouseiSikKbn());
        // 加算フラグ
        insuranceInfo.setKasanFlg(CommonDtoUtil.objValToString(cks211OutInfo.getKasanFlg()));
        // 親レコード番号
        insuranceInfo.setOyaLineNo(CommonDtoUtil.objValToString(cks211OutInfo.getOyaLineNo()));
        return insuranceInfo;
    }

    /**
     * 月日指定リストの編集
     *
     * @param cks212OutInfo 月日指定情報
     * @return 月日指定リスト
     */
    private Gui01014Tukihi editTulohInfo(Keikaku2TukihiInfoOutEntity cks212OutInfo) {
        Gui01014Tukihi tukihInfo = new Gui01014Tukihi();
        // カウンター
        tukihInfo.setKs212Id(CommonDtoUtil.objValToString(cks212OutInfo.getKs212Id()));
        // 計画書（２）ID
        tukihInfo.setKs21Id(CommonDtoUtil.objValToString(cks212OutInfo.getKs21Id()));
        // 月日指定開始日
        tukihInfo.setStartYmd(cks212OutInfo.getStartYmd());
        // 月日指定終了日
        tukihInfo.setEndYmd(cks212OutInfo.getEndYmd());
        return tukihInfo;
    }

    /**
     * サービス曜日リストの編集
     *
     * @param cks221OutInfo サービス曜日情報
     * @return サービス曜日リスト
     */
    private Gui01014Yobi editYobiInfo(CpnTucCks221InfoOutEntity cks221OutInfo) {
        Gui01014Yobi yobiInfo = new Gui01014Yobi();
        // カウンター
        yobiInfo.setKs221Id(CommonDtoUtil.objValToString(cks221OutInfo.getKs221Id()));
        // 計画書（２）行データID
        yobiInfo.setKs22Id(CommonDtoUtil.objValToString(cks221OutInfo.getKs22Id()));
        // 計画書（２）ID
        yobiInfo.setKs21Id(CommonDtoUtil.objValToString(cks221OutInfo.getKs21Id()));
        // 曜日
        yobiInfo.setYoubi(cks221OutInfo.getYoubi());
        // 週単位以外のｻｰﾋﾞｽ区分
        yobiInfo.setIgaiKbn(CommonDtoUtil.objValToString(cks221OutInfo.getIgaiKbn()));
        // 週単位以外のｻｰﾋﾞｽ（日付指定）
        yobiInfo.setIgaiDate(cks221OutInfo.getIgaiDate());
        // 週単位以外のｻｰﾋﾞｽ（曜日指定）
        yobiInfo.setIgaiWeek(cks221OutInfo.getIgaiWeek());
        // 居宅：開始時間
        yobiInfo.setKaishiJikan(cks221OutInfo.getKaishiJikan());
        // 居宅：終了時間
        yobiInfo.setShuuryouJikan(cks221OutInfo.getShuuryouJikan());
        // 居宅：サービス種類
        yobiInfo.setSvShuruiCd(cks221OutInfo.getSvShuruiCd());
        // 居宅：サービス項目（台帳）
        yobiInfo.setSvItemCd(CommonDtoUtil.objValToString(cks221OutInfo.getSvItemCd()));
        // 居宅：サービス事業者CD
        yobiInfo.setSvJigyoId(CommonDtoUtil.objValToString(cks221OutInfo.getSvJigyoId()));
        // 居宅：福祉用具貸与単位
        yobiInfo.setTanka(CommonDtoUtil.objValToString(cks221OutInfo.getTanka()));
        // 福祉用具貸与商品コード
        yobiInfo.setFygId(CommonDtoUtil.objValToString(cks221OutInfo.getFygId()));
        // 合成識別区分
        yobiInfo.setGouseiSikKbn(cks221OutInfo.getGouseiSikKbn());
        return yobiInfo;
    }

    /**
     * 担当者リストの編集
     *
     * @param cks222OutInfo 担当者情報
     * @return 担当者リスト
     */
    private Gui01014Tanto editGuiTantoInfo(CpnTucCks222InfoOutEntity cks222OutInfo) {
        Gui01014Tanto guiTantoInfo = new Gui01014Tanto();
        // カウンター
        guiTantoInfo.setKs222Id(CommonDtoUtil.objValToString(cks222OutInfo.getKs222Id()));
        // 計画書（２）行データID
        guiTantoInfo.setKs22Id(CommonDtoUtil.objValToString(cks222OutInfo.getKs22Id()));
        // 計画書（２）ID
        guiTantoInfo.setKs21Id(CommonDtoUtil.objValToString(cks222OutInfo.getKs21Id()));
        // 施設：職種（担当者）
        guiTantoInfo.setShokushuId(CommonDtoUtil.objValToString(cks222OutInfo.getShokushuId()));
        return guiTantoInfo;
    }

    /**
     * 7. 取込フラグの編集
     *
     * @param inDto リクエストパラメータ
     * @return 取込フラグ
     */
    private String acquReadFlg(CarePlan2InitSelectServiceInDto inDto) {
        // 7.1. 取込フラグが「TRUE」を設定する。
        String readFlg = CommonConstants.STR_IMPORT_FLG_TRUE;
        // 7.3. XX-XX 老健カルテプロブレムの件数より判定する。
        RoukenKarteProblemByCriteriaInEntity roukenKarteProblemByCriteriaInEntity = new RoukenKarteProblemByCriteriaInEntity();
        // リクエストパラメータ.法人ID
        roukenKarteProblemByCriteriaInEntity.setHoujinId(inDto.getHoujinId());
        // リクエストパラメータ.施設ID
        roukenKarteProblemByCriteriaInEntity.setShisetuId(inDto.getShisetuId());
        // リクエストパラメータ.利用者ID
        roukenKarteProblemByCriteriaInEntity.setUserId(inDto.getUserId());
        RoukenKarteProblemOutEntity roukenOutInfo = kghTucRoukenKarteProblemSelectMapper
                .countRoukenKarteProblemByCriteria(roukenKarteProblemByCriteriaInEntity);
        if (CommonConstants.STR_ZERO.equals(roukenOutInfo.getRecCount())) {
            // 7.3.1. 件数が0の場合、取込フラグが「FALSE」を設定する。
            readFlg = CommonConstants.STR_IMPORT_FLG_FALSE;
        }

        return readFlg;
    }

    /**
     * 適用事業所一覧情報の編集
     *
     * @param svjiOutInfo サービス事業者より事業者情報
     * @return 適用事業所一覧情報
     */
    private Gui01014JigyoInfo editJigyoInfo(KghStringComMscSvjigyoJigyoKnj2OutEntity svjiOutInfo) {
        Gui01014JigyoInfo jigyoInfo = new Gui01014JigyoInfo();
        // サービス事業者ID
        jigyoInfo.setSvJigyoId(CommonDtoUtil.objValToString(svjiOutInfo.getSvJigyoId()));
        // 事業名
        jigyoInfo.setJigyoKnj(svjiOutInfo.getJigyoKnj());
        // 事業名（略称）
        jigyoInfo.setJigyoRyakuKnj(svjiOutInfo.getJigyoRyakuKnj());
        // サービス事業者コード
        jigyoInfo.setSvJigyoCd(svjiOutInfo.getSvJigyoCd());
        // 法人ID
        jigyoInfo.setHoujinId(CommonDtoUtil.objValToString(svjiOutInfo.getHoujinId()));
        // 施設ID
        jigyoInfo.setShisetuId(CommonDtoUtil.objValToString(svjiOutInfo.getShisetuId()));
        // 外部サービス型
        jigyoInfo.setChsGaibuFlg(CommonDtoUtil.objValToString(svjiOutInfo.getChsGaibuFlg()));
        // 表示順(サービス事業者)
        jigyoInfo.setSortSvjigyo(CommonDtoUtil.objValToString(svjiOutInfo.getSort3()));
        // 表示順(施設)
        jigyoInfo.setSortShisetu(CommonDtoUtil.objValToString(svjiOutInfo.getSort1()));
        // 表示順(法人)
        jigyoInfo.setSortHoujin(CommonDtoUtil.objValToString(svjiOutInfo.getSort2()));
        return jigyoInfo;
    }
}
