package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.service.dto.Gui01015IssuesTermGoalCaptureItemOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01015IssuesTermGoalCaptureOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01015PlanPeriodOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.IssuesShortTermGoalImportSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.IssuesShortTermGoalImportSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.GLKadaiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GLKadaiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GLKyoTakuAsensByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GLKyoTakuAsensOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.HC20RyoikiHeadByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.HC20RyoikiHeadOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.HC20RyoikiKadaiTankiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.HC20RyoikiKadaiTankiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.HKKeaCheckByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.HKKeaCheckOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.InteraiHoushikiInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.InteraiHoushikiInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KeikakuSho2ToriByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KeikakuSho2ToriOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucKrkKikanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucKrkKikanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RapsMondaiKentoKadaiTankiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RapsMondaiKentoKadaiTankiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinNameByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinNameOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdlKadaiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucGdlRirekiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHc20Kent1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHcc1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucMds21Kent1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KadaiToTankiMohyouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KeikakuSho2ToriSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucKrkKikanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RapsMondaiKentoKadaiTankiSelectMapper;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.04.25
 * <AUTHOR> Bui The Khang
 * @implNote GUI01015_課題と短期目標取込
 */
@Service
public class IssuesShortTermGoalImportSelectServiceImpl extends
        SelectServiceImpl<IssuesShortTermGoalImportSelectServiceInDto, IssuesShortTermGoalImportSelectServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private KghTucKrkKikanSelectMapper kghTucKrkKikanSelectMapper;

    @Autowired
    private CpnTucHcc1SelectMapper cpnTucHcc1SelectMapper;

    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    @Autowired
    private KeikakuSho2ToriSelectMapper keikakuSho2ToriSelectMapper;

    @Autowired
    private CpnTucGdlRirekiSelectMapper cpnTucGdlRirekiSelectMapper;

    @Autowired
    private CpnTucGdlKadaiSelectMapper cpnTucGdlKadaiSelectMapper;

    @Autowired
    private CpnTucHc20Kent1SelectMapper cpnTucHc20Kent1SelectMapper;

    @Autowired
    private KadaiToTankiMohyouSelectMapper kadaiToTankiMohyouSelectMapper;

    @Autowired
    private RapsMondaiKentoKadaiTankiSelectMapper rapsMondaiKentoKadaiTankiSelectMapper;

    @Autowired
    private CpnTucMds21Kent1SelectMapper cpnTucMds21Kent1SelectMapper;

    /**
     * 課題と短期目標取込
     * 
     * @param IssuesShortTermGoalImportSelectServiceInDto
     * @return IssuesShortTermGoalImportSelectServiceOutDto
     * @throws Exception
     * 
     */
    // 「1:包括」
    private static final String ASSESSMENT_METHOD_HOUKATSU = "1";
    // 「2:居宅」
    private static final String ASSESSMENT_METHOD_KYOTAKU = "2";
    // 「3:MDS-HC2.0」
    private static final String ASSESSMENT_METHOD_MDS_HC2 = "3";
    // 「4:MDS2.1
    private static final String ASSESSMENT_METHOD_MDS_21 = "4";
    // 0
    private static final String VALUE_DEFAULT = "0";

    @Override
    protected IssuesShortTermGoalImportSelectServiceOutDto mainProcess(
            IssuesShortTermGoalImportSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        IssuesShortTermGoalImportSelectServiceOutDto dto = new IssuesShortTermGoalImportSelectServiceOutDto();
        // 1. 単項目チェック以外の入力チェック
        // 2. リクエストパラメータ.期間管理フラグ = TRUEの場合、計画対象期間情報の検索処理を行い、日課表共通サービスマスタ情報取得
        // 【変数】.期間ID = 0
        List<Integer> listKikanId = new ArrayList<>();
        listKikanId.add(0);
        List<Gui01015PlanPeriodOutDto> planPeriodList = new ArrayList<>();

        List<Gui01015IssuesTermGoalCaptureOutDto> issuesTermGoalCaptureList = new ArrayList<>();

        List<Gui01015IssuesTermGoalCaptureItemOutDto> issuesTermGoalCaptureItemList = new ArrayList<>();

        if (BooleanUtils.TRUE.equals(inDto.getPeriodManagementFlag())) {
            KghTucKrkKikanByCriteriaInEntity kghTucKrkKikanByCriteriaInEntity = new KghTucKrkKikanByCriteriaInEntity();
            // 施設ID
            kghTucKrkKikanByCriteriaInEntity.setSId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            // 事業者ID
            kghTucKrkKikanByCriteriaInEntity.setJId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 利用者ID
            kghTucKrkKikanByCriteriaInEntity.setUId(CommonDtoUtil.strValToInt(inDto.getUserId()));
            // 種別ID
            kghTucKrkKikanByCriteriaInEntity.setSyubetsuId(CommonDtoUtil.strValToInt(inDto.getSyubetsuId()));
            List<KghTucKrkKikanOutEntity> kikanOutEntities = kghTucKrkKikanSelectMapper
                    .findKghTucKrkKikanByCriteria(kghTucKrkKikanByCriteriaInEntity);
            // 3. 課題と短期目標取込ヘッダ情報を取得する。
            // ・リクエストパラメータ.期間管理フラグ ＝ ”TRUE”、 上記処理(2).計画対象期間リスト.件数 > 0 件の場合、
            if (!kikanOutEntities.isEmpty()) {
                listKikanId.remove(0);
                for (KghTucKrkKikanOutEntity entity : kikanOutEntities) {
                    Gui01015PlanPeriodOutDto gui01015PlanPeriodOutDto = new Gui01015PlanPeriodOutDto();
                    // 開始日
                    gui01015PlanPeriodOutDto.setStartYmd(entity.getStartYmd());
                    // 終了日
                    gui01015PlanPeriodOutDto.setEndYmd(entity.getEndYmd());
                    // 期間ID
                    gui01015PlanPeriodOutDto.setKikanId(CommonDtoUtil.objValToString(entity.getSc1Id()));
                    // 【変数】.期間ID = 取得した計画対象期間リストの選択した行
                    listKikanId.add(entity.getSc1Id());
                    planPeriodList.add(gui01015PlanPeriodOutDto);
                }
            }

        }

        for (Integer sc1Id : listKikanId) {
            switch (inDto.getCpnFlg()) {
                case ASSESSMENT_METHOD_HOUKATSU:
                    // 3.1 リクエストパラメータ.アセスメント方式 = 「1:包括」の場合
                    handleCpnFlgBy1(sc1Id, issuesTermGoalCaptureList, issuesTermGoalCaptureItemList);
                    break;
                case ASSESSMENT_METHOD_KYOTAKU:
                    // 3.2 リクエストパラメータ.アセスメント方式 = 「2:居宅」の場合
                    handleCpnFlgBy2(sc1Id, issuesTermGoalCaptureList, issuesTermGoalCaptureItemList);
                    break;
                case ASSESSMENT_METHOD_MDS_HC2:
                    // 3.3 リクエストパラメータ.アセスメント方式 = 「3:MDS-HC2.0」の場合
                    handleCpnFlgBy3(sc1Id, issuesTermGoalCaptureList, issuesTermGoalCaptureItemList);
                    break;
                case ASSESSMENT_METHOD_MDS_21:
                    // 3.4 リクエストパラメータ.アセスメント方式 = 「4:MDS2.1」
                    handleCpnFlgBy4(sc1Id, issuesTermGoalCaptureList, issuesTermGoalCaptureItemList);
                    break;
                default:
                    break;
            }
        }
        // 5. 上記処理で取得した結果レスポンスを返却する。
        dto.setIssuesTermGoalCaptureItemList(issuesTermGoalCaptureItemList);
        dto.setIssuesTermGoalCaptureList(issuesTermGoalCaptureList);
        dto.setPlanPeriodList(planPeriodList);
        LOG.info(Constants.END);
        return dto;
    }

    /**
     * アセスメント方式「1:包括」に対応する課題と短期目標のデータを取得・設定する。
     *
     * ケアチェック情報およびサービスメモ情報を基に、課題・短期目標DTOリストを作成する。
     * 
     * @param sc1Id                         計画期間ID
     * @param issuesTermGoalCaptureList     課題短期目標キャプチャDTOリスト（出力用）
     * @param issuesTermGoalCaptureItemList 課題短期目標キャプチャ項目DTOリスト（出力用）
     */
    private void handleCpnFlgBy1(Integer sc1Id,
            List<Gui01015IssuesTermGoalCaptureOutDto> issuesTermGoalCaptureList,
            List<Gui01015IssuesTermGoalCaptureItemOutDto> issuesTermGoalCaptureItemList) {
        // 3.1.1 課題と短期目標取込ヘッダ情報リスト_包括を取得する。
        HKKeaCheckByCriteriaInEntity hKKeaCheckByCriteriaInEntity = new HKKeaCheckByCriteriaInEntity();
        hKKeaCheckByCriteriaInEntity.setSc1(sc1Id);
        List<HKKeaCheckOutEntity> hkKeaCheckOutEntities = cpnTucHcc1SelectMapper
                .findHKKeaCheckByCriteria(hKKeaCheckByCriteriaInEntity);
        for (HKKeaCheckOutEntity entity : hkKeaCheckOutEntities) {
            Gui01015IssuesTermGoalCaptureOutDto captureOutDto = new Gui01015IssuesTermGoalCaptureOutDto();
            // ケアチェックID
            captureOutDto.setCcId1(CommonDtoUtil.objValToString(entity.getCc1Id()));
            // アセスメントID
            captureOutDto.setCcId2(VALUE_DEFAULT);
            // 作成日
            captureOutDto.setCreateYmd(entity.getCreateYmd());
            // 期間ID
            captureOutDto.setKikanId(CommonDtoUtil.objValToString(sc1Id));
            // 3.1.2 作成者氏名を設定する。
            ShokuinNameByCriteriaInEntity shokuinNameByCriteriaInEntity = new ShokuinNameByCriteriaInEntity();
            shokuinNameByCriteriaInEntity.setLlShokuId(CommonDtoUtil.objValToString(entity.getShokuId()));
            List<ShokuinNameOutEntity> shokuinNameOutEntities = comMscShokuinSelectMapper
                    .findShokuinNameByCriteria(shokuinNameByCriteriaInEntity);
            if (!shokuinNameOutEntities.isEmpty()) {
                // 作成者 = 職員名（姓）+ " " +職員名（名）
                ShokuinNameOutEntity nameOutEntity = shokuinNameOutEntities.get(0);
                captureOutDto
                        .setShoku(nameOutEntity.getShokuin1Knj() + " " + nameOutEntity.getShokuin2Knj());
            }
            issuesTermGoalCaptureList.add(captureOutDto);
            // 4.1 リクエストパラメータ.アセスメント方式 = 「1:包括」
            KeikakuSho2ToriByCriteriaInEntity serviceMemoByCriteriaInEntity = new KeikakuSho2ToriByCriteriaInEntity();
            // ケアチェックID
            serviceMemoByCriteriaInEntity.setCc1Id(entity.getCc1Id());
            // 計画期間ID
            serviceMemoByCriteriaInEntity.setSc1Id(sc1Id);
            List<KeikakuSho2ToriOutEntity> memoOutEntities = keikakuSho2ToriSelectMapper
                    .findKeikakuSho2ToriByCriteria(serviceMemoByCriteriaInEntity);

            for (KeikakuSho2ToriOutEntity memoOutEntity : memoOutEntities) {
                Gui01015IssuesTermGoalCaptureItemOutDto captureItemOutDto = new Gui01015IssuesTermGoalCaptureItemOutDto();
                // ケアチェックID
                captureItemOutDto.setCcId1(CommonDtoUtil.objValToString(entity.getCc1Id()));
                // アセスメントID
                captureItemOutDto.setCcId2(VALUE_DEFAULT);
                // 期間ID
                captureItemOutDto.setKikanId(CommonDtoUtil.objValToString(sc1Id));
                // 課題
                captureItemOutDto.setKadaiKnj(memoOutEntity.getMemoKnj32());
                // // 短期目標
                captureItemOutDto.setTankiKnj(memoOutEntity.getMemoKnj33());
                issuesTermGoalCaptureItemList.add(captureItemOutDto);
            }
        }
    }

    /**
     * アセスメント方式「2:居宅」に対応する課題と短期目標のデータを取得・設定する。
     *
     * ケアチェック情報およびサービスメモ情報を基に、課題・短期目標DTOリストを作成する。
     * 
     * @param sc1Id                         計画期間ID
     * @param issuesTermGoalCaptureList     課題短期目標キャプチャDTOリスト（出力用）
     * @param issuesTermGoalCaptureItemList 課題短期目標キャプチャ項目DTOリスト（出力用）
     */
    private void handleCpnFlgBy2(Integer sc1Id,
            List<Gui01015IssuesTermGoalCaptureOutDto> issuesTermGoalCaptureList,
            List<Gui01015IssuesTermGoalCaptureItemOutDto> issuesTermGoalCaptureItemList) {
        // 3.2.1 課題と短期目標取込ヘッダ情報リスト_居宅を取得する。
        GLKyoTakuAsensByCriteriaInEntity gLKyoTakuAsensByCriteriaInEntity = new GLKyoTakuAsensByCriteriaInEntity();
        gLKyoTakuAsensByCriteriaInEntity.setAlSc1Id(sc1Id);
        List<GLKyoTakuAsensOutEntity> kyoTakuAsensOutEntities = cpnTucGdlRirekiSelectMapper
                .findGLKyoTakuAsensByCriteria(gLKyoTakuAsensByCriteriaInEntity);
        for (GLKyoTakuAsensOutEntity entity : kyoTakuAsensOutEntities) {
            Gui01015IssuesTermGoalCaptureOutDto captureOutDto = new Gui01015IssuesTermGoalCaptureOutDto();
            // ケアチェックID

            captureOutDto.setCcId1(VALUE_DEFAULT);
            // アセスメントID
            captureOutDto.setCcId2(CommonDtoUtil.objValToString(entity.getGdlId()));
            // 作成日
            captureOutDto.setCreateYmd(entity.getAsJisshiDateYmd());
            // 期間ID
            captureOutDto.setKikanId(CommonDtoUtil.objValToString(sc1Id));

            // 3.2.2 作成者氏名を設定する。
            ShokuinNameByCriteriaInEntity shokuinNameByCriteriaInEntity = new ShokuinNameByCriteriaInEntity();
            // 職員ID
            shokuinNameByCriteriaInEntity.setLlShokuId(CommonDtoUtil.objValToString(entity.getShokuId()));
            List<ShokuinNameOutEntity> shokuinNameOutEntities = comMscShokuinSelectMapper
                    .findShokuinNameByCriteria(shokuinNameByCriteriaInEntity);
            if (!shokuinNameOutEntities.isEmpty()) {
                // 作成者 = 職員名（姓）+ " " +職員名（名）
                ShokuinNameOutEntity nameOutEntity = shokuinNameOutEntities.get(0);
                captureOutDto
                        .setShoku(nameOutEntity.getShokuin1Knj() + " " + nameOutEntity.getShokuin2Knj());
            }
            issuesTermGoalCaptureList.add(captureOutDto);

            // 4.2 リクエストパラメータ.アセスメント方式 = 「2:居宅」
            GLKadaiByCriteriaInEntity gLKadaiByCriteriaInEntity = new GLKadaiByCriteriaInEntity();
            // アセスメントID
            gLKadaiByCriteriaInEntity.setGdl(entity.getGdlId());
            // 計画期間ID
            gLKadaiByCriteriaInEntity.setSc1(sc1Id);
            List<GLKadaiOutEntity> glKadaiOutEntities = cpnTucGdlKadaiSelectMapper
                    .findGLKadaiByCriteria(gLKadaiByCriteriaInEntity);
            for (GLKadaiOutEntity glKadaiOutEntity : glKadaiOutEntities) {
                Gui01015IssuesTermGoalCaptureItemOutDto captureItemOutDto = new Gui01015IssuesTermGoalCaptureItemOutDto();
                // ケアチェックID
                captureItemOutDto.setCcId1(VALUE_DEFAULT);
                // アセスメントID
                captureItemOutDto.setCcId2(CommonDtoUtil.objValToString(entity.getGdlId()));
                // 期間ID
                captureItemOutDto.setKikanId(CommonDtoUtil.objValToString(sc1Id));
                // 課題
                captureItemOutDto.setKadaiKnj(glKadaiOutEntity.getKadaiKnj());
                // 短期目標
                captureItemOutDto.setTankiKnj(glKadaiOutEntity.getTankiKnj());
                // 長期目標
                captureItemOutDto.setChoukiKnj(glKadaiOutEntity.getChoukiKnj());
                issuesTermGoalCaptureItemList.add(captureItemOutDto);
            }
        }
    }

    /**
     * アセスメント方式「3:MDS-HC2.0」に対応する課題と短期目標のデータを取得・設定する。
     *
     * ケアチェック情報およびサービスメモ情報を基に、課題・短期目標DTOリストを作成する。
     * 
     * @param sc1Id                         計画期間ID
     * @param issuesTermGoalCaptureList     課題短期目標キャプチャDTOリスト（出力用）
     * @param issuesTermGoalCaptureItemList 課題短期目標キャプチャ項目DTOリスト（出力用）
     */
    private void handleCpnFlgBy3(Integer sc1Id,
            List<Gui01015IssuesTermGoalCaptureOutDto> issuesTermGoalCaptureList,
            List<Gui01015IssuesTermGoalCaptureItemOutDto> issuesTermGoalCaptureItemList) {
        // 3.3.1 課題と短期目標取込ヘッダ情報リスト_HCを取得する。
        HC20RyoikiHeadByCriteriaInEntity hC20RyoikiHeadByCriteriaInEntity = new HC20RyoikiHeadByCriteriaInEntity();
        // 計画期間ID
        hC20RyoikiHeadByCriteriaInEntity.setSc1(sc1Id);
        List<HC20RyoikiHeadOutEntity> hc20RyoikiHeadOutEntities = cpnTucHc20Kent1SelectMapper
                .findHC20RyoikiHeadByCriteria(hC20RyoikiHeadByCriteriaInEntity);
        for (HC20RyoikiHeadOutEntity entity : hc20RyoikiHeadOutEntities) {
            Gui01015IssuesTermGoalCaptureOutDto captureOutDto = new Gui01015IssuesTermGoalCaptureOutDto();
            // ケアチェックID
            captureOutDto.setCcId1(VALUE_DEFAULT);
            // アセスメントID
            captureOutDto.setCcId2(CommonDtoUtil.objValToString(entity.getHc20Id()));
            // 作成日
            captureOutDto.setCreateYmd(entity.getChousaYmd());
            // 期間ID
            captureOutDto.setKikanId(CommonDtoUtil.objValToString(sc1Id));

            // 3.3.2 作成者氏名を設定する。
            ShokuinNameByCriteriaInEntity shokuinNameByCriteriaInEntity = new ShokuinNameByCriteriaInEntity();
            // 職員ID
            shokuinNameByCriteriaInEntity.setLlShokuId(CommonDtoUtil.objValToString(entity.getKisaisha()));
            List<ShokuinNameOutEntity> shokuinNameOutEntities = comMscShokuinSelectMapper
                    .findShokuinNameByCriteria(shokuinNameByCriteriaInEntity);
            if (!shokuinNameOutEntities.isEmpty()) {
                // 作成者 = 職員名（姓）+ " " +職員名（名）
                ShokuinNameOutEntity nameOutEntity = shokuinNameOutEntities.get(0);
                captureOutDto
                        .setShoku(nameOutEntity.getShokuin1Knj() + " " + nameOutEntity.getShokuin2Knj());
            }
            issuesTermGoalCaptureList.add(captureOutDto);

            // 4.3 リクエストパラメータ.アセスメント方式 = 「3:MDS-HC2.0」
            HC20RyoikiKadaiTankiByCriteriaInEntity hC20RyoikiKadaiTankiByCriteriaInEntity = new HC20RyoikiKadaiTankiByCriteriaInEntity();
            // 計画期間ID
            hC20RyoikiKadaiTankiByCriteriaInEntity.setSc1(sc1Id);
            // アセスメントID
            hC20RyoikiKadaiTankiByCriteriaInEntity.setHc20(entity.getHc20Id());
            List<HC20RyoikiKadaiTankiOutEntity> ryoikiKadaiTankiOutEntities = kadaiToTankiMohyouSelectMapper
                    .findHC20RyoikiKadaiTankiByCriteria(hC20RyoikiKadaiTankiByCriteriaInEntity);

            for (HC20RyoikiKadaiTankiOutEntity kadaiTankiOutEntity : ryoikiKadaiTankiOutEntities) {
                Gui01015IssuesTermGoalCaptureItemOutDto captureItemOutDto = new Gui01015IssuesTermGoalCaptureItemOutDto();
                // ケアチェックID
                captureItemOutDto.setCcId1(VALUE_DEFAULT);
                // アセスメントID
                captureItemOutDto.setCcId2(CommonDtoUtil.objValToString(entity.getHc20Id()));
                // 期間ID
                captureItemOutDto.setKikanId(CommonDtoUtil.objValToString(sc1Id));
                // 短期目標
                captureItemOutDto.setTankiKnj(kadaiTankiOutEntity.getTankiKnj());
                // 課題
                captureItemOutDto.setKadaiKnj(kadaiTankiOutEntity.getKadaiKnj());
                issuesTermGoalCaptureItemList.add(captureItemOutDto);
            }
        }
    }

    /**
     * アセスメント方式「4:MDS2.1」に対応する課題と短期目標のデータを取得・設定する。
     *
     * ケアチェック情報およびサービスメモ情報を基に、課題・短期目標DTOリストを作成する。
     * 
     * @param sc1Id                         計画期間ID
     * @param issuesTermGoalCaptureList     課題短期目標キャプチャDTOリスト（出力用）
     * @param issuesTermGoalCaptureItemList 課題短期目標キャプチャ項目DTOリスト（出力用）
     */
    private void handleCpnFlgBy4(Integer sc1Id,
            List<Gui01015IssuesTermGoalCaptureOutDto> issuesTermGoalCaptureList,
            List<Gui01015IssuesTermGoalCaptureItemOutDto> issuesTermGoalCaptureItemList) {
        // 3.4.1 課題と短期目標取込ヘッダ情報リスト_MDSを取得する。
        InteraiHoushikiInfoByCriteriaInEntity interaiHoushikiInfoByCriteriaInEntity = new InteraiHoushikiInfoByCriteriaInEntity();
        interaiHoushikiInfoByCriteriaInEntity.setSc1(sc1Id);
        List<InteraiHoushikiInfoOutEntity> infoOutEntities = cpnTucMds21Kent1SelectMapper
                .findInteraiHoushikiInfoByCriteria(interaiHoushikiInfoByCriteriaInEntity);
        for (InteraiHoushikiInfoOutEntity entity : infoOutEntities) {
            Gui01015IssuesTermGoalCaptureOutDto captureOutDto = new Gui01015IssuesTermGoalCaptureOutDto();
            // ケアチェックID
            captureOutDto.setCcId1(VALUE_DEFAULT);
            // アセスメントID
            captureOutDto.setCcId2(CommonDtoUtil.objValToString(entity.getMds21Id()));
            // 期間ID
            captureOutDto.setKikanId(CommonDtoUtil.objValToString(sc1Id));
            // 作成日
            captureOutDto.setCreateYmd(entity.getChousaYmd());
            // 3.4.2 作成者氏名を設定する。
            ShokuinNameByCriteriaInEntity shokuinNameByCriteriaInEntity = new ShokuinNameByCriteriaInEntity();
            // 職員ID
            shokuinNameByCriteriaInEntity.setLlShokuId(CommonDtoUtil.objValToString(entity.getKisaisha()));
            List<ShokuinNameOutEntity> shokuinNameOutEntities = comMscShokuinSelectMapper
                    .findShokuinNameByCriteria(shokuinNameByCriteriaInEntity);
            if (!shokuinNameOutEntities.isEmpty()) {
                // 作成者 = 職員名（姓）+ " " +職員名（名）
                ShokuinNameOutEntity nameOutEntity = shokuinNameOutEntities.get(0);
                captureOutDto
                        .setShoku(nameOutEntity.getShokuin1Knj() + " " + nameOutEntity.getShokuin2Knj());
            }
            issuesTermGoalCaptureList.add(captureOutDto);

            // 4.4 リクエストパラメータ.アセスメント方式 = 「4:MDS2.1」
            RapsMondaiKentoKadaiTankiByCriteriaInEntity rapsMondaiKentoKadaiTankiByCriteriaInEntity = new RapsMondaiKentoKadaiTankiByCriteriaInEntity();
            // アセスメントID
            rapsMondaiKentoKadaiTankiByCriteriaInEntity.setMds21Id(entity.getMds21Id());
            // 計画期間ID
            rapsMondaiKentoKadaiTankiByCriteriaInEntity.setSc1Id(sc1Id);
            List<RapsMondaiKentoKadaiTankiOutEntity> kentoKadaiTankiOutEntities = rapsMondaiKentoKadaiTankiSelectMapper
                    .findRapsMondaiKentoKadaiTankiByCriteria(rapsMondaiKentoKadaiTankiByCriteriaInEntity);

            for (RapsMondaiKentoKadaiTankiOutEntity kadaiTankiOutEntity : kentoKadaiTankiOutEntities) {
                Gui01015IssuesTermGoalCaptureItemOutDto captureItemOutDto = new Gui01015IssuesTermGoalCaptureItemOutDto();
                // ケアチェックID
                captureItemOutDto.setCcId1(VALUE_DEFAULT);
                // アセスメントID
                captureItemOutDto.setCcId2(CommonDtoUtil.objValToString(kadaiTankiOutEntity.getMds21Id()));
                // 期間ID
                captureItemOutDto.setKikanId(CommonDtoUtil.objValToString(sc1Id));
                // 短期目標
                captureItemOutDto.setTankiKnj(kadaiTankiOutEntity.getTankiKnj());
                // 課題
                captureItemOutDto.setKadaiKnj(kadaiTankiOutEntity.getKadaiKnj());
                issuesTermGoalCaptureItemList.add(captureItemOutDto);
            }
        }
    }

}
