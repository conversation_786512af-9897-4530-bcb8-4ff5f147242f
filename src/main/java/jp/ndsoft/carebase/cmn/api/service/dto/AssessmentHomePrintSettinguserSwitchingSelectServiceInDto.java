package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.07.18
 * <AUTHOR>
 * @implNote GUI00815_印刷設定 利用者切替サービス入力Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AssessmentHomePrintSettinguserSwitchingSelectServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 利用者ID */
    @NotEmpty
    private String userId;

    /** システム略称 */
    @NotEmpty
    private String sysRyaku;

    /** セクション名 */
    @NotEmpty
    private String sectionName;

    /** システムコード */
    @NotEmpty
    private String gsysCd;

    /** 法人ID */
    @NotEmpty
    private String houjinId;

    /** 施設ID */
    @NotEmpty
    private String shisetuId;

    /** 期間管理フラグ */
    @NotEmpty
    private String kikanFlg;

    /** 職員ID */
    @NotEmpty
    private String shokuId;

    /** 事業者ID */
    @NotEmpty
    private String svJigyoId;

    /** 改訂範囲 */
    @NotEmpty
    private String revisionRange;

    /** 履歴選択フラグ */
    @NotEmpty
    private String historySelectFlag;

    /** インデックス */
    @NotEmpty
    private String index;

    /** 個人情報使用フラグ */
    @NotEmpty
    private String kojinhogoUsedFlg;

    /** 個人情報番号 */
    @NotEmpty
    private String sectionAddNo;

}
