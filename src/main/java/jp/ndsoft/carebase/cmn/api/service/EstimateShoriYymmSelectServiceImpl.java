package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.dto.TermId2DateLogicOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.EstimateShoriYymmSelectInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.EstimateShoriYymmSelectOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import jp.ndsoft.smh.framework.util.AppUtil;
import jp.ndsoft.smh.framework.util.DateUtil;

/**
 * @since 2025.09.08
 * <AUTHOR> 李晨昊
 * @implNote GUI00979_週間表（一覧）
 */
@Service
public class EstimateShoriYymmSelectServiceImpl extends SelectServiceImpl<EstimateShoriYymmSelectInDto, EstimateShoriYymmSelectOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired //getTermid2date
    private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;


    /**
     * 概算の処理年月を取得する
     * 
     * @param inDto 概算の処理年月取得入力DTO.
     * @return 概算の処理年月取得出力DTO
     */
    @Override
    protected EstimateShoriYymmSelectOutDto mainProcess(EstimateShoriYymmSelectInDto inDto) throws Exception {
        LOG.info(Constants.START);
        EstimateShoriYymmSelectOutDto outDto = new EstimateShoriYymmSelectOutDto();

        // 2.　リクエストパラメータ.当該年月が空以外の場合
        if (StringUtils.isNotEmpty(inDto.getTougaiYmd())) {
            // レスポンスパラメータ.処理年月　＝　リクエストパラメータ.当該年月の前7桁
            outDto.setShoriYymm(StringUtils.substring(inDto.getTougaiYmd(), 0, 7));
        } 
        // 3.　上記以外の場合
        else {
            // 3.1.　共通関数「f_cpn_week_termid2date」を呼び出し、サービス日を取得して返す
            TermId2DateLogicOutDto termId2DateLogicOutDto = new TermId2DateLogicOutDto();
            Integer termId = CommonDtoUtil.strValToInt(inDto.getTermid());
            termId2DateLogicOutDto = kghKrkZCpnFuncLogic.getTermid2date(termId, StringUtils.EMPTY, StringUtils.EMPTY);

            // 3.2. 変数.作成日を編集
            String createYMD = inDto.getCreateYmd();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            LocalDate localYmd = LocalDate.parse(createYMD, formatter);
            LocalDate SYmd = LocalDate.parse(termId2DateLogicOutDto.getSYmd(), formatter);
            LocalDate EYmd = LocalDate.parse(termId2DateLogicOutDto.getEYmd(), formatter);
            // 3.2.1. 変数.作成日が空、またはNULLの場合
            if (StringUtils.isEmpty(createYMD)) {
                String date = DateUtil.formatDateToString(AppUtil.getSystemTimeStamp(), null);
                createYMD = date;
            }
            // 3.2.2. 変数.作成日 < 3.1で取得したサービス検索の開始日の場合
            if (localYmd.isBefore(SYmd)) {
                createYMD = CommonDtoUtil.objValToString(SYmd);
            }
            // 3.2.3. 変数.作成日 ＞ 3.1で取得したサービス検索の終了日の場合
            if (localYmd.isAfter(EYmd)) {
                createYMD = CommonDtoUtil.objValToString(EYmd);
            }
            outDto.setShoriYymm(StringUtils.substring(createYMD, 0, 7));
        }

        LOG.info(Constants.END);
        // 4. 上記処理で取得した結果レスポンスを返却する
        return outDto;
    }
}
