package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * @since 2025.04.24
 * <AUTHOR> Bui The Khang
 * @implNote GUI01076_［同意欄の編集］画面情報更新
 */
@Setter
@Getter
public class Gui01076DoiObject extends IDtoImpl {
    // serialVersionUID
    private static final long serialVersionUID = 1L;

    /** 事業者ID */
    @NotEmpty
    private String chiJigyoId;

    /** 機能区分 */
    @NotEmpty
    private String kycFlg;

    /** 垂直スクロールバー */
    private String scrollFlg;

    /** 敬称の変更 */
    private String keishoFlg;

    /** 敬称 */
    private String keishoKnj;

    /** 利用者基本情報同意欄 */
    private String khnDoiKnj;

    /** 計画書様式 */
    private String kkakPrtFlg;

    /** 更新区分 */
    private String updateKbn;

    /** 更新回数 */
    // private String modifiedCnt;
}
