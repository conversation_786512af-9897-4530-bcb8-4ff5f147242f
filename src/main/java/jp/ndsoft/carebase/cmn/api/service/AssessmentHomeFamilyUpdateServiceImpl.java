package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00795FamInfo;
import jp.ndsoft.carebase.cmn.api.logic.AssessmentHomeLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeFamilyUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeFamilyUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeSaveServiceInDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Fam11H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Fam11R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdlRirekiMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Fam11H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Fam11H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Fam11R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Fam11R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRireki;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRirekiCriteria;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * @since 2025.04.18
 * <AUTHOR> 劉逍
 * @apiNote GUI00795_［アセスメント］画面（居宅）（2） データ保存
 */
@Service
public class AssessmentHomeFamilyUpdateServiceImpl extends
        UpdateServiceImpl<AssessmentHomeFamilyUpdateServiceInDto, AssessmentHomeFamilyUpdateServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** ＧＬ＿居宅アセスメント履歴 */
    @Autowired
    private CpnTucGdlRirekiMapper cpnTucGdlRirekiMapper;

    /** ＧＬ＿家族状況とインフォーマルな支援の状況（Ｈ２１改訂） */
    @Autowired
    private CpnTucGdl4Fam11H21Mapper cpnTucGdl4Fam11H21Mapper;

    /** ＧＬ＿家族状況とインフォーマルな支援の状況（R３改訂） */
    @Autowired
    private CpnTucGdl5Fam11R3Mapper cpnTucGdl5Fam11R3Mapper;

    @Autowired
    private AssessmentHomeLogic assessmentHomeLogic;

    @Autowired
    private PlatformTransactionManager transactionManager;

    /**
     * ［アセスメント］画面（居宅）（2） データ保存
     * 
     * @param inDto ［アセスメント］画面（居宅）（2） データ保存の入力DTO
     * @return ［アセスメント］画面（居宅）（2） データ保存の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected AssessmentHomeFamilyUpdateServiceOutDto mainProcess(AssessmentHomeFamilyUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        AssessmentHomeFamilyUpdateServiceOutDto outDto = new AssessmentHomeFamilyUpdateServiceOutDto();
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        /*
         * ===============2. バリデーションチェックを行う。===============
         * 
         */
        boolean resFlg = assessmentHomeLogic.documentPerformValidationCheck(loginDto);
        // 2.2.4. 【変数】.処理結果が「false：失敗」の場合
        if (!resFlg) {
            // 2.2.4. 【変数】.処理結果が「false：失敗」の場合、下記返却する情報を設定して、後続処理を飛ばして、API処理を正常終了とする。
            // レスポンス.計画期間ID ： リクエストパラメータ.計画期間ID
            outDto.setSc1Id(loginDto.getSc1Id());
            // レスポンス.アセスメントID ： リクエストパラメータ.アセスメントID
            outDto.setGdlId(loginDto.getGdlId());
            // レスポンス.エラー区分: "1
            outDto.setErrKbn(CommonConstants.E_DOC_CHECK_ERR);
            return outDto;
        }
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transactionB = transactionManager.getTransaction(def);
        try {
            // ［アセスメント］画面（居宅）（2） データ保存
            outDto = mainProcessMealUpdate(inDto);
            transactionManager.commit(transactionB);
        } catch (Exception e) {
            transactionManager.rollback(transactionB);
            throw e;
        }

        // e-文書_ケアチェック表１のリクエストパラメータ
        TransactionStatus transactionC = transactionManager.getTransaction(def);
        try {
            mainProcessEdocOut(inDto, outDto);
            transactionManager.commit(transactionC);
        } catch (Exception e) {
            transactionManager.rollback(transactionC);
            throw e;
        }

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * API定義 (e文書出力)
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected void mainProcessEdocOut(
            AssessmentHomeFamilyUpdateServiceInDto inDto, AssessmentHomeFamilyUpdateServiceOutDto outDto)
            throws Exception {
        // 入力dtoをLoginDtoにコピーする
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);

        /*
         * ===============e文書出力===============
         * 
         */
        // e文書出力
        String errKbn = assessmentHomeLogic.getPrint(loginDto, outDto.getSc1Id());
        outDto.setErrKbn(errKbn);
    }

    /**
     * ［アセスメント］画面（居宅）（2） データ保存
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected AssessmentHomeFamilyUpdateServiceOutDto mainProcessMealUpdate(
            AssessmentHomeFamilyUpdateServiceInDto inDto)
            throws Exception {

        AssessmentHomeFamilyUpdateServiceOutDto outDto = new AssessmentHomeFamilyUpdateServiceOutDto();

        String sc1IdTemp = CommonConstants.BLANK_STRING;

        // ＧＬ＿住宅等の状況（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_hou11_h21）登録で採番されたアセスメントID
        String gdlIdTemp = CommonConstants.BLANK_STRING;

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. 計画対象期間の保存処理===============
         * 
         */
        // ［アセスメント］画面（居宅）の計画対象期間共通 データ保存サービス入力Dto
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        // 計画対象期間set
        setLoginDto(loginDto, inDto);

        // 2.1.リクエストパラメータ.期間対象フラグが「0：期間管理しない」、かつ、リクエストパラメータ.計画対象期間IDがnullの場合、【27-06記録共通期間】情報を登録する。
        if (CommonDtoUtil.checkStringEqual(inDto.getSc1Id(), CommonConstants.PERIOD_NO_MANAGE_FLG)
                || StringUtil.isNull(inDto.getSc1Id())) {
            // 計画対象期間の保存処理
            this.assessmentHomeLogic.insertKghTucKrkKikan(loginDto);
            // 変数.計画対象期間ID=採番した期間ID
            sc1IdTemp = loginDto.getSc1Id();
        }
        // 2.2.上記以外の場合、
        else {
            // 変数.計画対象期間ID=リクエストパラメータ.計画対象期間ID
            sc1IdTemp = inDto.getSc1Id();
        }
        /*
         * ======3.リクエストパラメータ.削除処理区分が2:画面を履歴ごと削除するの場合、下記テーブルデータを削除する。========
         * 
         */
        // リクエストパラメータ.削除処理区分が2:画面を履歴ごと削除するの場合
        if (CommonDtoUtil.checkStringEqual(inDto.getDeleteKbn(), CommonConstants.DELETE_KBN_2)) {
            // 下記テーブルデータを削除する
            this.assessmentHomeLogic.homeLogicsyuri(loginDto, CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        }
        /*
         * ======4. リクエストパラメータ.削除処理区分が1:画面のみ削除するの場合========
         * 
         */
        // リクエストパラメータ.削除処理区分が1:画面のみ削除するの場合
        else if (CommonDtoUtil.checkStringEqual(inDto.getDeleteKbn(), CommonConstants.DELETE_KBN_1)) {
            // 4.1. リクエストパラメータ.改定フラグが4「H21/４改訂版」の場合、【ＧＬ＿家族状況とインフォーマルな支援の状況（Ｈ２１改訂）】情報を更新する。
            if (CommonDtoUtil.checkStringEqual(inDto.getNinteiFormF(), CommonConstants.KAI_TEI_FLAG_H21_4)) {
                this.deleteFam11H21(inDto, sc1IdTemp);
            }
            // 4.2. リクエストパラメータ.改定フラグが5「R3/４改訂版」の場合、【ＧＬ＿家族状況とインフォーマルな支援の状況（R３改訂）】情報を更新する。
            else if (CommonDtoUtil.checkStringEqual(inDto.getNinteiFormF(), CommonConstants.KAI_TEI_FLAG_R3_4_5)) {
                this.deleteFam11R3(inDto, sc1IdTemp);
            }

            // 4.3. 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
            this.updateRirekiColumn(inDto, sc1IdTemp);
        }
        /*
         * ======5. 以外の場合========
         * 
         */
        else {
            // 5.1. 履歴情報の保存処理
            // 5.1.1. リクエストパラメータ.履歴更新区分が"C":新規の場合、【ＧＬ＿居宅アセスメント履歴】情報を登録する。
            if (CommonDtoUtil.isHistoryCreate(inDto)) {
                this.insertRireki(inDto, sc1IdTemp);

                // 「5.1.1.」で採番したアセスメントID
                gdlIdTemp = inDto.getGdlId();
                // アセスメントID
                outDto.setGdlId(gdlIdTemp);
            }
            // 5.1.2. リクエストパラメータ.履歴更新区分が"U":更新の場合、【ＧＬ＿居宅アセスメント履歴】情報を更新する。
            else if (CommonDtoUtil.isHistoryUpdate(inDto)) {
                this.updateRireki(inDto, sc1IdTemp);

                // アセスメントID
                outDto.setGdlId(inDto.getGdlId());
            }

            // 5.2. 家族状況／支援情報の保存処理
            // 5.2.1. リクエストパラメータ.更新区分が"C":新規の場合、家族状況とインフォーマルな支援の状況情報を登録する。
            if (CommonDtoUtil.isHistoryCreate(inDto)) {
                // 5.2.1.1.
                // リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、
                if (CommonDtoUtil.checkStringEqual(inDto.getNinteiFormF(), CommonConstants.KAI_TEI_FLAG_H21_4)) {
                    // 【ＧＬ＿家族状況とインフォーマルな支援の状況（Ｈ２１改訂）】情報を登録する。
                    this.insertFam11H21(inDto, sc1IdTemp, gdlIdTemp);
                }
                // 5.2.1.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【
                else if (CommonDtoUtil.checkStringEqual(inDto.getNinteiFormF(),
                        CommonConstants.KAI_TEI_FLAG_R3_4_5)) {
                    // ＧＬ＿家族状況とインフォーマルな支援の状況（R３改訂）】情報を登録する。
                    this.insertFam11R3(inDto, sc1IdTemp, gdlIdTemp);
                }
            }
            // 5.2.2. リクエストパラメータ.更新区分が"U":更新の場合、家族状況とインフォーマルな支援の状況情報を更新する。
            else if (CommonDtoUtil.isHistoryUpdate(inDto)) {
                // 5.2.2.1.
                // リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、【ＧＬ＿家族状況とインフォーマルな支援の状況（Ｈ２１改訂）】情報を更新する。
                if (CommonDtoUtil.checkStringEqual(inDto.getNinteiFormF(), CommonConstants.KAI_TEI_FLAG_H21_4)) {
                    this.updateFam11H21(inDto, sc1IdTemp);

                    // 5.2.2.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【ＧＬ＿家族状況とインフォーマルな支援の状況（R３改訂）】情報を更新する。
                } else if (CommonDtoUtil.checkStringEqual(inDto.getNinteiFormF(),
                        CommonConstants.KAI_TEI_FLAG_R3_4_5)) {
                    this.updateFam11R3(inDto, sc1IdTemp);
                }
            }

            // 5.3. リクエストパラメータ.【課題と目標リスト】の件数分、課題と目標情報を保存する。
            this.assessmentHomeLogic.homeLogicCpnTucGdlKadai(loginDto, CommonDtoUtil.strValToInt(sc1IdTemp));
        }

        outDto.setGdlId(gdlIdTemp);
        outDto.setSc1Id(sc1IdTemp);
        return outDto;
    }

    /**
     * 計画対象期間set
     * 
     * @param loginDto --[アセスメント］画面（居宅）の計画対象期間共通 データ保存サービス入力Dto
     * @param inDto    --Gui00795_［アセスメント］画面（居宅）（2） データ保存サービス入力Dto
     */
    private void setLoginDto(AssessmentHomeSaveServiceInDto loginDto, AssessmentHomeFamilyUpdateServiceInDto inDto) {
        // 法人ID
        loginDto.setHoujinId(inDto.getHoujinId());
        // 施設ID
        loginDto.setShisetuId(inDto.getShisetuId());
        // 事業者ID
        loginDto.setSvJigyoId(inDto.getSvJigyoId());
        // 利用者ID
        loginDto.setUserId(inDto.getUserId());
        // 種別ID
        loginDto.setSyubetsuId(inDto.getSyubetsuId());
        // 作成日
        loginDto.setKijunbiYmd(inDto.getKijunbiYmd());
    }

    /**
     * 【ＧＬ＿家族状況とインフォーマルな支援の状況（Ｈ２１改訂）】の削除更新詳細
     * 関数名：deleteHou11h21
     * 
     * @param inDto     ［アセスメント］画面（居宅）（2） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @return 更新件数
     */
    private void deleteFam11H21(AssessmentHomeFamilyUpdateServiceInDto inDto, String sc1IdTemp) {

        CpnTucGdl4Fam11H21Criteria cpnTucGdl4Fam11H21Criteria = new CpnTucGdl4Fam11H21Criteria();
        cpnTucGdl4Fam11H21Criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp));

        // DAOを実行
        this.cpnTucGdl4Fam11H21Mapper.deleteByCriteria(cpnTucGdl4Fam11H21Criteria);

    }

    /**
     * 【ＧＬ＿家族状況とインフォーマルな支援の状況（R３改訂）】の削除更新詳細
     * 関数名：deleteHou11R3
     * 
     * @param inDto     ［アセスメント］画面（居宅）（2） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @return 更新件数
     */
    private void deleteFam11R3(AssessmentHomeFamilyUpdateServiceInDto inDto, String sc1IdTemp) {

        CpnTucGdl5Fam11R3Criteria cpnTucGdl5Fam11R3Criteria = new CpnTucGdl5Fam11R3Criteria();
        cpnTucGdl5Fam11R3Criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp));

        this.cpnTucGdl5Fam11R3Mapper.deleteByCriteria(cpnTucGdl5Fam11R3Criteria);

    }

    /**
     * ＧＬ＿居宅アセスメント履歴テーブル（cpn_tuc_gdl_rireki）の更新詳細
     * 関数名：clearRireki
     * 
     * @param inDto     ［アセスメント］画面（居宅）（2） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @return 更新件数
     */
    private void updateRirekiColumn(AssessmentHomeFamilyUpdateServiceInDto inDto, String sc1IdTemp) {

        // DAOパラメータを作成
        // 3.1.居宅アセスメント履歴を更新する
        // ＧＬ＿居宅アセスメント履歴パラメータを作成
        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                // 利用者ＩＤ
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 法人ID
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();

        // 住居等の状況
        cpnTucGdlRireki.setAss2(StringUtils.EMPTY);

        // DAOを実行
        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRireki, criteria);
    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を登録する。
     * 関数名：insertRireki
     * 
     * @param inDto     ［アセスメント］画面（居宅）（2） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     */
    private void insertRireki(AssessmentHomeFamilyUpdateServiceInDto inDto, String sc1IdTemp) throws Exception {
        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();
        // 計画期間ID
        cpnTucGdlRireki.setSc1Id(CommonDtoUtil.strValToInt(sc1IdTemp));
        // 法人ID
        cpnTucGdlRireki.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        cpnTucGdlRireki.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        cpnTucGdlRireki.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        cpnTucGdlRireki.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // アセスメント実施日
        cpnTucGdlRireki.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRireki.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // 家族状況／支援
        cpnTucGdlRireki.setAss2(CommonConstants.MARU);
        // Ｊの状態
        cpnTucGdlRireki.setAss12(CommonConstants.DASH);
        // 本人の基本動作等8
        cpnTucGdlRireki.setAss13(CommonConstants.DASH);
        // 改定フラグ
        cpnTucGdlRireki.setNinteiFormF(CommonDtoUtil.strValToInt(inDto.getNinteiFormF()));

        this.cpnTucGdlRirekiMapper.insertSelectiveAndReturn(cpnTucGdlRireki);

        inDto.setGdlId(CommonDtoUtil.objValToString(cpnTucGdlRireki.getGdlId()));

    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
     * 関数名：updateRireki
     * 
     * @param inDto     ［アセスメント］画面（居宅）（2） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @return 更新件数
     */
    private void updateRireki(AssessmentHomeFamilyUpdateServiceInDto inDto, String sc1IdTemp) {

        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();
        // アセスメント実施日
        cpnTucGdlRireki.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRireki.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // 住居等の状況
        cpnTucGdlRireki.setAss2(CommonConstants.MARU);
        // Ｊの状態
        cpnTucGdlRireki.setAss12(CommonConstants.DASH);
        // 本人の基本動作等8
        cpnTucGdlRireki.setAss13(CommonConstants.DASH);

        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();
        criteria.createCriteria()
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                // 利用者ＩＤ
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 法人ID
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRireki, criteria);
    }

    /**
     * ＧＬ＿住宅等の状況（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_hou11_h21）の登録詳細
     * 関数名：insertHou11H21
     * 
     * @param inDto     ［アセスメント］画面（居宅）（2） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @param gdlIdTemp アセスメントID
     * @throws Exception Exception
     */
    private void insertFam11H21(
            AssessmentHomeFamilyUpdateServiceInDto inDto, String sc1IdTemp, String gdlIdTemp)
            throws Exception {
        CpnTucGdl4Fam11H21 cpnTucGdl4Fam11H21 = new CpnTucGdl4Fam11H21();
        // 変数に値を代入する
        setCpnTucGdl4Fam11H21(cpnTucGdl4Fam11H21, inDto.getFamInfo());

        // アセスメントID
        cpnTucGdl4Fam11H21
                .setGdlId(CommonDtoUtil.strValToInt(gdlIdTemp));
        // 計画期間ID
        cpnTucGdl4Fam11H21
                .setSc1Id(CommonDtoUtil.strValToInt(sc1IdTemp));
        // 家族図縮小フラグ
        cpnTucGdl4Fam11H21.setShukushoFlg(CommonConstants.NUMBER_ZERO);

        this.cpnTucGdl4Fam11H21Mapper.insertSelective(cpnTucGdl4Fam11H21);
    }

    /**
     * cpn_tuc_gdl4_hou11_h21 変数に値を代入する
     * 
     * @param cpnTucGdl4Fam11H21 --値を代入する必要があるオブジェクト
     * @param famInfo            --代入データ
     */
    private void setCpnTucGdl4Fam11H21(CpnTucGdl4Fam11H21 cpnTucGdl4Fam11H21, Gui00795FamInfo famInfo) {
        // 家族氏名1
        cpnTucGdl4Fam11H21.setFamily1NameKnj(famInfo.getFamily1NameKnj());
        // 家族氏名2
        cpnTucGdl4Fam11H21.setFamily2NameKnj(famInfo.getFamily2NameKnj());
        // 家族氏名3
        cpnTucGdl4Fam11H21.setFamily3NameKnj(famInfo.getFamily3NameKnj());
        // 家族氏名4
        cpnTucGdl4Fam11H21.setFamily4NameKnj(famInfo.getFamily4NameKnj());
        // 家族氏名5
        cpnTucGdl4Fam11H21.setFamily5NameKnj(famInfo.getFamily5NameKnj());
        // 家族性別1
        cpnTucGdl4Fam11H21.setSex1(CommonDtoUtil.strValToInt(famInfo.getSex1()));
        // 家族性別2
        cpnTucGdl4Fam11H21.setSex2(CommonDtoUtil.strValToInt(famInfo.getSex2()));
        // 家族性別3
        cpnTucGdl4Fam11H21.setSex3(CommonDtoUtil.strValToInt(famInfo.getSex3()));
        // 家族性別4
        cpnTucGdl4Fam11H21.setSex4(CommonDtoUtil.strValToInt(famInfo.getSex4()));
        // 家族性別5
        cpnTucGdl4Fam11H21.setSex5(CommonDtoUtil.strValToInt(famInfo.getSex5()));
        // 続柄1
        cpnTucGdl4Fam11H21.setZcode1(CommonDtoUtil.strValToInt(famInfo.getZcode1()));
        // 続柄2
        cpnTucGdl4Fam11H21.setZcode2(CommonDtoUtil.strValToInt(famInfo.getZcode2()));
        // 続柄3
        cpnTucGdl4Fam11H21.setZcode3(CommonDtoUtil.strValToInt(famInfo.getZcode3()));
        // 続柄4
        cpnTucGdl4Fam11H21.setZcode4(CommonDtoUtil.strValToInt(famInfo.getZcode4()));
        // 続柄5
        cpnTucGdl4Fam11H21.setZcode5(CommonDtoUtil.strValToInt(famInfo.getZcode5()));
        // 同別居1
        cpnTucGdl4Fam11H21.setDoukyo1(CommonDtoUtil.strValToInt(famInfo.getDoukyo1()));
        // 同別居2
        cpnTucGdl4Fam11H21.setDoukyo2(CommonDtoUtil.strValToInt(famInfo.getDoukyo2()));
        // 同別居3
        cpnTucGdl4Fam11H21.setDoukyo3(CommonDtoUtil.strValToInt(famInfo.getDoukyo3()));
        // 同別居4
        cpnTucGdl4Fam11H21.setDoukyo4(CommonDtoUtil.strValToInt(famInfo.getDoukyo4()));
        // 同別居5
        cpnTucGdl4Fam11H21.setDoukyo5(CommonDtoUtil.strValToInt(famInfo.getDoukyo5()));
        // 介護者1
        cpnTucGdl4Fam11H21.setKaigo1(CommonDtoUtil.strValToInt(famInfo.getKaigo1()));
        // 介護者2
        cpnTucGdl4Fam11H21.setKaigo2(CommonDtoUtil.strValToInt(famInfo.getKaigo2()));
        // 介護者3
        cpnTucGdl4Fam11H21.setKaigo3(CommonDtoUtil.strValToInt(famInfo.getKaigo3()));
        // 介護者4
        cpnTucGdl4Fam11H21.setKaigo4(CommonDtoUtil.strValToInt(famInfo.getKaigo4()));
        // 介護者5
        cpnTucGdl4Fam11H21.setKaigo5(CommonDtoUtil.strValToInt(famInfo.getKaigo5()));
        // 職の有無1
        cpnTucGdl4Fam11H21.setShokugyo1(CommonDtoUtil.strValToInt(famInfo.getShokugyo1()));
        // 職の有無2
        cpnTucGdl4Fam11H21.setShokugyo2(CommonDtoUtil.strValToInt(famInfo.getShokugyo2()));
        // 職の有無3
        cpnTucGdl4Fam11H21.setShokugyo3(CommonDtoUtil.strValToInt(famInfo.getShokugyo3()));
        // 職の有無4
        cpnTucGdl4Fam11H21.setShokugyo4(CommonDtoUtil.strValToInt(famInfo.getShokugyo4()));
        // 職の有無5
        cpnTucGdl4Fam11H21.setShokugyo5(CommonDtoUtil.strValToInt(famInfo.getShokugyo5()));
        // 健康状態等特記事項1
        cpnTucGdl4Fam11H21.setKenkou1Knj(famInfo.getKenkou1Knj());
        // 健康状態等特記事項2
        cpnTucGdl4Fam11H21.setKenkou2Knj(famInfo.getKenkou2Knj());
        // 健康状態等特記事項3
        cpnTucGdl4Fam11H21.setKenkou3Knj(famInfo.getKenkou3Knj());
        // 健康状態等特記事項4
        cpnTucGdl4Fam11H21.setKenkou4Knj(famInfo.getKenkou4Knj());
        // 健康状態等特記事項5
        cpnTucGdl4Fam11H21.setKenkou5Knj(famInfo.getKenkou5Knj());
        // 支援提供者
        cpnTucGdl4Fam11H21.setTeikyoNameKnj(famInfo.getTeikyoNameKnj());
        // 支援内容
        cpnTucGdl4Fam11H21.setNaiyo1Knj(famInfo.getNaiyo1Knj());
        // 受けたい支援
        cpnTucGdl4Fam11H21.setNaiyo2Knj(famInfo.getNaiyo2Knj());
        // 特記事項
        cpnTucGdl4Fam11H21.setMemoKnj(famInfo.getMemoKnj());
        // 特記事項1
        cpnTucGdl4Fam11H21.setTokki1Knj(famInfo.getTokki1Knj());
        // 特記事項2
        cpnTucGdl4Fam11H21.setTokki2Knj(famInfo.getTokki2Knj());
        // 特記事項3
        cpnTucGdl4Fam11H21.setTokki3Knj(famInfo.getTokki3Knj());
        // 特記事項4
        cpnTucGdl4Fam11H21.setTokki4Knj(famInfo.getTokki4Knj());
        // 特記事項5
        cpnTucGdl4Fam11H21.setTokki5Knj(famInfo.getTokki5Knj());
        // 支援生活状況特記
        cpnTucGdl4Fam11H21.setSienTokkiKnj(famInfo.getSienTokkiKnj());
        // 支援提供者(本人が受けたい支援)
        cpnTucGdl4Fam11H21.setTeikyoName2Knj(famInfo.getTeikyoName2Knj());
        // 特記事項(本人が受けたい支援)
        cpnTucGdl4Fam11H21.setSienTokki2Knj(famInfo.getSienTokki2Knj());
    }

    /**
     * ＧＬ＿住宅等の状況（R３改訂）テーブル（cpn_tuc_gdl5_hou11_r3）の登録詳細
     * 関数名：insertHou11R3
     * 
     * @param inDto     ［アセスメント］画面（居宅）（2） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @param gdlIdTemp アセスメントID
     * @throws Exception Exception
     */
    private void insertFam11R3(
            AssessmentHomeFamilyUpdateServiceInDto inDto, String sc1IdTemp, String gdlIdTemp)
            throws Exception {

        CpnTucGdl5Fam11R3 cpnTucGdl5Fam11R3 = new CpnTucGdl5Fam11R3();
        // 変数に値を代入する
        setCpnTucGdl5Fam11R3(cpnTucGdl5Fam11R3, inDto.getFamInfo());
        // アセスメントID
        cpnTucGdl5Fam11R3.setGdlId(CommonDtoUtil.strValToInt(gdlIdTemp));
        // 計画期間ID
        cpnTucGdl5Fam11R3.setSc1Id(CommonDtoUtil.strValToInt(sc1IdTemp));
        // 家族図縮小フラグ
        cpnTucGdl5Fam11R3.setShukushoFlg(CommonConstants.NUMBER_ZERO);

        this.cpnTucGdl5Fam11R3Mapper.insertSelective(cpnTucGdl5Fam11R3);
    }

    /**
     * cpn_tuc_gdl5_hou11_r3 変数に値を代入する
     * 
     * @param cpnTucGdl5Fam11R3 --値を代入する必要があるオブジェクト
     * @param famInfo           --代入データ
     */
    private void setCpnTucGdl5Fam11R3(CpnTucGdl5Fam11R3 cpnTucGdl5Fam11R3, Gui00795FamInfo famInfo) {
        // 家族氏名1
        cpnTucGdl5Fam11R3.setFamily1NameKnj(famInfo.getFamily1NameKnj());
        // 家族氏名2
        cpnTucGdl5Fam11R3.setFamily2NameKnj(famInfo.getFamily2NameKnj());
        // 家族氏名3
        cpnTucGdl5Fam11R3.setFamily3NameKnj(famInfo.getFamily3NameKnj());
        // 家族氏名4
        cpnTucGdl5Fam11R3.setFamily4NameKnj(famInfo.getFamily4NameKnj());
        // 家族氏名5
        cpnTucGdl5Fam11R3.setFamily5NameKnj(famInfo.getFamily5NameKnj());
        // 続柄1
        cpnTucGdl5Fam11R3.setZcode1(CommonDtoUtil.strValToInt(famInfo.getZcode1()));
        // 続柄2
        cpnTucGdl5Fam11R3.setZcode2(CommonDtoUtil.strValToInt(famInfo.getZcode2()));
        // 続柄3
        cpnTucGdl5Fam11R3.setZcode3(CommonDtoUtil.strValToInt(famInfo.getZcode3()));
        // 続柄4
        cpnTucGdl5Fam11R3.setZcode4(CommonDtoUtil.strValToInt(famInfo.getZcode4()));
        // 続柄5
        cpnTucGdl5Fam11R3.setZcode5(CommonDtoUtil.strValToInt(famInfo.getZcode5()));
        // 同別居1
        cpnTucGdl5Fam11R3.setDoukyo1(CommonDtoUtil.strValToInt(famInfo.getDoukyo1()));
        // 同別居2
        cpnTucGdl5Fam11R3.setDoukyo2(CommonDtoUtil.strValToInt(famInfo.getDoukyo2()));
        // 同別居3
        cpnTucGdl5Fam11R3.setDoukyo3(CommonDtoUtil.strValToInt(famInfo.getDoukyo3()));
        // 同別居4
        cpnTucGdl5Fam11R3.setDoukyo4(CommonDtoUtil.strValToInt(famInfo.getDoukyo4()));
        // 同別居5
        cpnTucGdl5Fam11R3.setDoukyo5(CommonDtoUtil.strValToInt(famInfo.getDoukyo5()));
        // 介護者1
        cpnTucGdl5Fam11R3.setKaigo1(CommonDtoUtil.strValToInt(famInfo.getKaigo1()));
        // 介護者2
        cpnTucGdl5Fam11R3.setKaigo2(CommonDtoUtil.strValToInt(famInfo.getKaigo2()));
        // 介護者3
        cpnTucGdl5Fam11R3.setKaigo3(CommonDtoUtil.strValToInt(famInfo.getKaigo3()));
        // 介護者4
        cpnTucGdl5Fam11R3.setKaigo4(CommonDtoUtil.strValToInt(famInfo.getKaigo4()));
        // 介護者5
        cpnTucGdl5Fam11R3.setKaigo5(CommonDtoUtil.strValToInt(famInfo.getKaigo5()));
        // 職の有無1
        cpnTucGdl5Fam11R3.setShokugyo1(CommonDtoUtil.strValToInt(famInfo.getShokugyo1()));
        // 職の有無2
        cpnTucGdl5Fam11R3.setShokugyo2(CommonDtoUtil.strValToInt(famInfo.getShokugyo2()));
        // 職の有無3
        cpnTucGdl5Fam11R3.setShokugyo3(CommonDtoUtil.strValToInt(famInfo.getShokugyo3()));
        // 職の有無4
        cpnTucGdl5Fam11R3.setShokugyo4(CommonDtoUtil.strValToInt(famInfo.getShokugyo4()));
        // 職の有無5
        cpnTucGdl5Fam11R3.setShokugyo5(CommonDtoUtil.strValToInt(famInfo.getShokugyo5()));
        // 健康状態等特記事項1
        cpnTucGdl5Fam11R3.setKenkou1Knj(famInfo.getKenkou1Knj());
        // 健康状態等特記事項2
        cpnTucGdl5Fam11R3.setKenkou2Knj(famInfo.getKenkou2Knj());
        // 健康状態等特記事項3
        cpnTucGdl5Fam11R3.setKenkou3Knj(famInfo.getKenkou3Knj());
        // 健康状態等特記事項4
        cpnTucGdl5Fam11R3.setKenkou4Knj(famInfo.getKenkou4Knj());
        // 健康状態等特記事項5
        cpnTucGdl5Fam11R3.setKenkou5Knj(famInfo.getKenkou5Knj());
        // 支援提供者
        cpnTucGdl5Fam11R3.setTeikyoNameKnj(famInfo.getTeikyoNameKnj());
        // 支援内容
        cpnTucGdl5Fam11R3.setNaiyo1Knj(famInfo.getNaiyo1Knj());
        // 受けたい支援
        cpnTucGdl5Fam11R3.setNaiyo2Knj(famInfo.getNaiyo2Knj());
        // 特記事項
        cpnTucGdl5Fam11R3.setMemoKnj(famInfo.getMemoKnj());
        // 特記事項1
        cpnTucGdl5Fam11R3.setTokki1Knj(famInfo.getTokki1Knj());
        // 特記事項2
        cpnTucGdl5Fam11R3.setTokki2Knj(famInfo.getTokki2Knj());
        // 特記事項3
        cpnTucGdl5Fam11R3.setTokki3Knj(famInfo.getTokki3Knj());
        // 特記事項4
        cpnTucGdl5Fam11R3.setTokki4Knj(famInfo.getTokki4Knj());
        // 特記事項5
        cpnTucGdl5Fam11R3.setTokki5Knj(famInfo.getTokki5Knj());
        // 支援生活状況特記
        cpnTucGdl5Fam11R3.setSienTokkiKnj(famInfo.getSienTokkiKnj());
        // 支援提供者(本人が受けたい支援)
        cpnTucGdl5Fam11R3.setTeikyoName2Knj(famInfo.getTeikyoName2Knj());
        // 特記事項(本人が受けたい支援)
        cpnTucGdl5Fam11R3.setSienTokki2Knj(famInfo.getSienTokki2Knj());
    }

    /**
     * ＧＬ＿住宅等の状況（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_hou11_h21）の更新詳細
     * 関数名：updateHou11H21
     * 
     * @param inDto     ［アセスメント］画面（居宅）（2） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @return 更新件数
     * @throws Exception Exception
     */
    private void updateFam11H21(
            AssessmentHomeFamilyUpdateServiceInDto inDto, String sc1IdTemp)
            throws Exception {

        CpnTucGdl4Fam11H21 cpnTucGdl4Fam11H21 = new CpnTucGdl4Fam11H21();
        // 変数に値を代入する
        setCpnTucGdl4Fam11H21(cpnTucGdl4Fam11H21, inDto.getFamInfo());

        // 家族図縮小フラグ 0
        cpnTucGdl4Fam11H21.setShukushoFlg(CommonConstants.NUMBER_ZERO);

        final CpnTucGdl4Fam11H21Criteria criteria = new CpnTucGdl4Fam11H21Criteria();
        criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp));

        this.cpnTucGdl4Fam11H21Mapper
                .updateByCriteriaSelective(cpnTucGdl4Fam11H21, criteria);

    }

    /**
     * ＧＬ＿住宅等の状況（R３改訂）テーブル（cpn_tuc_gdl5_hou11_r3）の更新詳細
     * 関数名：updateHou11R3
     * 
     * @param inDto     ［アセスメント］画面（居宅）（2） データ保存の入力DTO.
     * @param sc1IdTemp 計画対象期間ID
     * @return 更新件数
     * @throws Exception Exception
     */
    private void updateFam11R3(
            AssessmentHomeFamilyUpdateServiceInDto inDto, String sc1IdTemp)
            throws Exception {

        CpnTucGdl5Fam11R3 cpnTucGdl5Fam11R3 = new CpnTucGdl5Fam11R3();
        // 変数に値を代入する
        setCpnTucGdl5Fam11R3(cpnTucGdl5Fam11R3, inDto.getFamInfo());
        // 家族図縮小フラグ 0
        cpnTucGdl5Fam11R3.setShukushoFlg(CommonConstants.NUMBER_ZERO);

        CpnTucGdl5Fam11R3Criteria criteria = new CpnTucGdl5Fam11R3Criteria();
        criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(sc1IdTemp));

        this.cpnTucGdl5Fam11R3Mapper
                .updateByCriteriaSelective(cpnTucGdl5Fam11R3, criteria);

    }
}
