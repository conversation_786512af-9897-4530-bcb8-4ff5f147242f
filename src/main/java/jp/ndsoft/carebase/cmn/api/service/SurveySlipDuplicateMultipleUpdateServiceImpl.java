package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01268Gss;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01270No;
import jp.ndsoft.carebase.cmn.api.service.dto.SurveySlipDuplicateMultipleUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.SurveySlipDuplicateMultipleUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCsc12Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCsc22Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCsc3H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCsc4Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCschMapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghTucKrkKikanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCsc12;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCsc22;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCsc22Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCsc3H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCsc3H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCsc4;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCsc4Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCsch;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTucKrkKikan;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChotokkiResH21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChotokkiResH21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnChoKihon1H21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnChoKihon1H21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCsc3H21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCsc4SelectMapper;
import jp.ndsoft.carebase.common.dao.unique.entity.CpnTucCsc12ForCscCopyInEntity;
import jp.ndsoft.carebase.common.dao.unique.mapper.CmnUniqueUpdateMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * GUI01270_調査票複数複写確定
 *
 * <AUTHOR>
 */
@Service
@Transactional
public class SurveySlipDuplicateMultipleUpdateServiceImpl extends
        UpdateServiceImpl<SurveySlipDuplicateMultipleUpdateServiceInDto, SurveySlipDuplicateMultipleUpdateServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // 27-06 記録共通期間Mapper
    @Autowired
    private KghTucKrkKikanMapper kghTucKrkKikanMapper;

    // 認定調査票（ヘッダ）Mapper
    @Autowired
    private CpnTucCschMapper cpnTucCschMapper;

    // 改訂版認定調査票（概況調査）Mapper
    @Autowired
    private CpnTucCsc12Mapper cpnTucCsc12Mapper;

    // 改訂版認定調査票（サービス利用）Mapper
    @Autowired
    private CpnTucCsc22Mapper cpnTucCsc22Mapper;

    // 複写元の[基本調査］情報を取得Mapper
    @Autowired
    private CpnTucCsc3H21SelectMapper cpnTucCsc3H21SelectMapper;

    // Ｈ２１改訂版認定調査票（基本）Mapper
    @Autowired
    private CpnTucCsc3H21Mapper cpnTucCsc3H21Mapper;

    // 複写元の[特記事項］情報を取得Mapper
    @Autowired
    private CpnTucCsc4SelectMapper cpnTucCsc4SelectMapper;

    // 認定調査票（特記事項）Mapper
    @Autowired
    private CpnTucCsc4Mapper cpnTucCsc4Mapper;

    // 概況調査の初期情報取得
    @Autowired
    private GeneralSituationSurveyInitialInfoSelectServiceImpl generalSituationSurveyInitialInfoSelectServiceImpl;

    // 認定調査票（特記事項）Mapper
    @Autowired
    private CmnUniqueUpdateMapper cmnUniqueUpdateMapper;

    /**
     * 調査票複数複写確定
     * 
     * @param inDto 調査票複数複写確定の入力DTO.
     * @return 調査票複数複写確定のOUT DTO
     * @throws Exception Exception
     */
    @Override
    protected SurveySlipDuplicateMultipleUpdateServiceOutDto mainProcess(
            SurveySlipDuplicateMultipleUpdateServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        SurveySlipDuplicateMultipleUpdateServiceOutDto outDto = new SurveySlipDuplicateMultipleUpdateServiceOutDto();
        // 2.期間の存在判断
        kikanJudgment(inDto, outDto);
        // 3.履歴の存在判断
        Integer newCschId = rirekiJudgment(inDto, outDto);
        // 4.リクエストパラメータ.複写モデルが「2:複数複写」の場合、リクエストパラメータ.タブNoリストをループする
        multipleCopy(inDto, newCschId);
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 複数複写
     * 
     * @param inDto     調査票複数複写確定の入力DTO.
     * @param newCschId 【変数】.新調査票ID
     * @throws Exception Exception
     */
    private void multipleCopy(SurveySlipDuplicateMultipleUpdateServiceInDto inDto, Integer newCschId) throws Exception {
    	// 4.リクエストパラメータ.複写モデルが「2:複数複写」の場合、
        if (CommonConstants.STR_2.equals(inDto.getCopymodel())) {
        	
        	// 【変数】.調査票ID
        	Integer cschId;
        	// リクエストパラメータ.タブNoリストをループする
        	for (Gui01270No item : inDto.getNoList()) {
        		// 4.1.調査票詳細情報を複写する
        		// 4.1.1.【変数】.調査票IDの値を設定する
        		// ・リクエストパラメータ.複写先調査票ID ＜1の場合、
        		Integer sakiCschId = CommonDtoUtil.strValToInt(inDto.getSakiCschId());
        		if (sakiCschId < CommonConstants.NUMBER_1) {
        			// 【変数】.調査票ID = 【変数】.新調査票ID
        			cschId = newCschId;
        		} else {
        			// ・リクエストパラメータ.複写先調査票ID >＝１の場合、
        			// 【変数】.調査票ID = リクエストパラメータ.複写先調査票ID
        			cschId = sakiCschId;
        		}
        		// 4.1.2.リクエストパラメータ.タブNoリスト．タブNo＝１の場合、[概況調査］情報を複写する
        		if (CommonConstants.TAB_ID_1.equals(item.getNo())) {
        			// 4.1.2.1.関数_複写元の「概況調査」情報取得
        			// ・※「API定義書_APINo(607)_概況調査の初期情報取得.xlsx」のシート「関数_「概況調査」取得」を参照する。
        			// 上記取得結果を【変数】.概況調査リストに設定する
        			List<Gui01268Gss> gssList = generalSituationSurveyInitialInfoSelectServiceImpl
        					.getGeneralSituationInfo(inDto.getMotoNinteiFlg(), inDto.getMotoSc1Id(),
        							inDto.getMotoCschId(), inDto.getIkenshoId());
        			// 4.1.2.2.「概況調査」情報を更新する。
        			// 4.1.2.2.1.リクエストパラメータ.複写先調査票ID = 0の場合、概況調査情報を登録する。
        			if (sakiCschId == CommonConstants.NUMBER_0) {
        				for (Gui01268Gss gss : gssList) {
        					// ①【改訂版認定調査票（概況調査）】情報を登録する。
        					cpnTucCsc12Insert(cschId, inDto, gss);
        					// ②【改訂版認定調査票（サービス利用）】情報を登録する。
        					cpnTucCsc22Insert(cschId, inDto, gss);
        				}
        			} else {
        				// 4.1.2.2.2.リクエストパラメータ.複写先調査票ID <> 0の場合、概況調査情報を更新する。
        				for (Gui01268Gss gss : gssList) {
        					// ①【改訂版認定調査票（概況調査）】情報を更新する。
        					cpnTucCsc12Update(gss, cschId);
        					// ②【改訂版認定調査票（サービス利用）】情報を更新する。
        					cpnTucCsc22Update(gss, cschId);
        				}
        			}
        		}
        		// 4.1.3.リクエストパラメータ.タブNoリスト．タブNo＝2の場合、[基本調査］情報を複写する
        		if (CommonConstants.TAB_ID_2.equals(item.getNo())) {
        			// 4.1.3.1.複写元の[基本調査］情報取得
        			CpnChoKihon1H21ByCriteriaInEntity cpnChoKihon1H21ByCriteriaInEntity = new CpnChoKihon1H21ByCriteriaInEntity();
        			// 計画期間ID リクエストパラメータ.複写元計画期間ID
        			cpnChoKihon1H21ByCriteriaInEntity.setSc1Id(CommonDtoUtil.strValToInt(inDto.getMotoSc1Id()));
        			// 調査票ID リクエストパラメータ.複写元調査票ID
        			cpnChoKihon1H21ByCriteriaInEntity.setCschId(CommonDtoUtil.strValToInt(inDto.getMotoCschId()));
        			List<CpnChoKihon1H21OutEntity> cpnChoKihon1H21List = cpnTucCsc3H21SelectMapper
        					.findCpnChoKihon1H21ByCriteria(cpnChoKihon1H21ByCriteriaInEntity);
        			// 上記取得結果を【変数】.基本調査リストに設定する
        			// 4.1.3.2.「基本調査」情報を更新する。
        			// 4.1.3.2.1.リクエストパラメータ.複写先調査票ID = 0の場合、基本調査情報を登録する。
        			if (sakiCschId == CommonConstants.NUMBER_0) {
        				for (CpnChoKihon1H21OutEntity cpnChoKihon1H21 : cpnChoKihon1H21List) {
        					// ①【Ｈ２１改訂版認定調査票（基本）】情報を登録する。
        					cpnTucCsc3H21Insert(cschId, inDto, cpnChoKihon1H21);
        				}
        			} else {
        				// 4.1.3.2.2.リクエストパラメータ.複写先調査票ID <> 0の場合、基本調査情報を更新する。
        				for (CpnChoKihon1H21OutEntity cpnChoKihon1H21 : cpnChoKihon1H21List) {
        					// ①【Ｈ２１改訂版認定調査票（基本）】情報を更新する。
        					cpnTucCsc3H21Update(cpnChoKihon1H21, cschId);
        				}
        			}
        		}
        		// 4.1.4.リクエストパラメータ.タブNoリスト．タブNo＝3の場合、[特記事項］情報を複写する
        		if (CommonConstants.TAB_ID_3.equals(item.getNo())) {
        			// 4.1.4.1.複写元の[特記事項］情報取得
        			ChotokkiResH21ByCriteriaInEntity chotokkiResH21ByCriteriaInEntity = new ChotokkiResH21ByCriteriaInEntity();
        			// 計画期間ID リクエストパラメータ.複写元計画期間ID
        			chotokkiResH21ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getMotoSc1Id()));
        			// 調査票ID リクエストパラメータ.複写元調査票ID
        			chotokkiResH21ByCriteriaInEntity.setAlCschId(CommonDtoUtil.strValToInt(inDto.getMotoCschId()));
        			List<ChotokkiResH21OutEntity> chotokkiResH21List = cpnTucCsc4SelectMapper
        					.findChotokkiResH21ByCriteria(chotokkiResH21ByCriteriaInEntity);
        			// 上記取得結果を【変数】.特記事項リストに設定する
        			// 4.1.4.2.【変数】.特記事項リストのデータをフィルター処理する
        			// ・【変数】.特記事項リスト.認定（特記大）<=7の場合、該当【変数】.特記事項リストの情報を保存する。
        			List<ChotokkiResH21OutEntity> filterChotokkiResH21List = chotokkiResH21List.stream()
        					.filter(x -> x.getN1tCd() <= CommonConstants.INT_7).collect(Collectors.toList());
        			// 4.1.4.3.「特記事項」情報を更新する。
        			// 4.1.4.3.1.リクエストパラメータ.複写先調査票ID = 0の場合、特記事項情報を登録する。
        			if (sakiCschId == CommonConstants.NUMBER_0) {
        				for (ChotokkiResH21OutEntity chotokkiResH21 : filterChotokkiResH21List) {
        					// ①【認定調査票（特記事項）】情報を登録する。
        					cpnTucCsc4Insert(cschId, inDto, chotokkiResH21);
        				}
        			} else {
        				// 4.1.4.3.2.リクエストパラメータ.複写先調査票ID <> 0の場合、特記事項情報を更新する。
        				for (ChotokkiResH21OutEntity chotokkiResH21 : filterChotokkiResH21List) {
        					// ①【認定調査票（特記事項）】情報を更新する。
        					cpnTucCsc4Update(chotokkiResH21, cschId);
        				}
        			}
        		}
        	}
        }
    }

    /**
     * 【認定調査票（特記事項）】情報を更新する。
     * 
     * @param chotokkiResH21 【変数】.特記事項リスト
     * @throws Exception Exception
     */
    private void cpnTucCsc4Update(ChotokkiResH21OutEntity chotokkiResH21, Integer cschId) throws Exception {
        final CpnTucCsc4Criteria criteria = new CpnTucCsc4Criteria();
        // 調査票ID＝【変数】.特記事項リスト.調査票ID
        criteria.createCriteria().andCschIdEqualTo(cschId);
        final CpnTucCsc4 cpnTucCsc4 = new CpnTucCsc4();
        // 認定（特記大）【変数】.特記事項リスト.認定（特記大）
        cpnTucCsc4.setN1tCd(chotokkiResH21.getN1tCd());
        // 認定（特記小）【変数】.特記事項リスト.認定（特記小）
        cpnTucCsc4.setN2tCd(chotokkiResH21.getN2tCd());
        // 特記事項【変数】.特記事項リスト.特記事項
        cpnTucCsc4.setMemoKnj(chotokkiResH21.getMemoKnj());
        // 表示順【変数】.特記事項リスト.表示順
        cpnTucCsc4.setSeqNo(chotokkiResH21.getSeqNo());
        // DAOを実行
        int updateCnt = this.cpnTucCsc4Mapper.updateByCriteriaSelective(cpnTucCsc4, criteria);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (updateCnt <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 【認定調査票（特記事項）】情報を登録する。
     * 
     * @param cschId         【変数】.調査票ID
     * @param inDto          調査票複数複写確定の入力DTO.
     * @param chotokkiResH21 【変数】.特記事項リスト
     */
    private void cpnTucCsc4Insert(Integer cschId, SurveySlipDuplicateMultipleUpdateServiceInDto inDto,
            ChotokkiResH21OutEntity chotokkiResH21) {
        final CpnTucCsc4 cpnTucCsc4 = new CpnTucCsc4();
        // // 調査票ID【変数】.調査票ID
        cpnTucCsc4.setCschId(cschId);
        // // 計画期間ID リクエストパラメータ.複写先計画期間ID
        cpnTucCsc4.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSakiSc1Id()));
        // 認定（特記大）【変数】.特記事項リスト.認定（特記大）
        cpnTucCsc4.setN1tCd(chotokkiResH21.getN1tCd());
        // 認定（特記小）【変数】.特記事項リスト.認定（特記小）
        cpnTucCsc4.setN2tCd(chotokkiResH21.getN2tCd());
        // 特記事項【変数】.特記事項リスト.特記事項
        cpnTucCsc4.setMemoKnj(chotokkiResH21.getMemoKnj());
        // 表示順【変数】.特記事項リスト.表示順
        cpnTucCsc4.setSeqNo(chotokkiResH21.getSeqNo());
        // DAOを実行
        this.cpnTucCsc4Mapper.insertSelective(cpnTucCsc4);
    }

    /**
     * 【Ｈ２１改訂版認定調査票（基本）】情報を更新する。
     * 
     * @param cpnChoKihon1H21 【変数】.基本調査リスト
     * @throws Exception Exception
     */
    private void cpnTucCsc3H21Update(CpnChoKihon1H21OutEntity cpnChoKihon1H21, Integer cschId) throws Exception {
        final CpnTucCsc3H21Criteria criteria = new CpnTucCsc3H21Criteria();
        // 調査票ID＝【変数】.基本調査リスト.調査票ID
        criteria.createCriteria().andCschIdEqualTo(cschId);
        final CpnTucCsc3H21 cpnTucCsc3H21 = new CpnTucCsc3H21();
        // 第1群麻痺（なし）【変数】.基本調査リスト.第1群麻痺（なし）
        cpnTucCsc3H21.setBango1C1I1(cpnChoKihon1H21.getBango1C1I1());
        // 第1群（左上肢）【変数】.基本調査リスト.第1群（左上肢）
        cpnTucCsc3H21.setBango1C1I2(cpnChoKihon1H21.getBango1C1I2());
        // 第1群（右上肢）【変数】.基本調査リスト.第1群（右上肢）
        cpnTucCsc3H21.setBango1C1I3(cpnChoKihon1H21.getBango1C1I3());
        // 第1群（左下肢）【変数】.基本調査リスト.第1群（左下肢）
        cpnTucCsc3H21.setBango1C1I4(cpnChoKihon1H21.getBango1C1I4());
        // 第1群（右下肢）【変数】.基本調査リスト.第1群（右下肢）
        cpnTucCsc3H21.setBango1C1I5(cpnChoKihon1H21.getBango1C1I5());
        // 第1群（その他）【変数】.基本調査リスト.第1群（その他）
        cpnTucCsc3H21.setBango1C1I6(cpnChoKihon1H21.getBango1C1I6());
        // 第1群拘縮（なし）【変数】.基本調査リスト.第1群拘縮（なし）
        cpnTucCsc3H21.setBango1C2I1(cpnChoKihon1H21.getBango1C2I1());
        // 第1群拘縮（肩）【変数】.基本調査リスト.第1群拘縮（肩）
        cpnTucCsc3H21.setBango1C2I2(cpnChoKihon1H21.getBango1C2I2());
        // 第1群拘縮（股）【変数】.基本調査リスト.第1群拘縮（股）
        cpnTucCsc3H21.setBango1C2I3(cpnChoKihon1H21.getBango1C2I3());
        // 第1群拘縮（膝）【変数】.基本調査リスト.第1群拘縮（膝）
        cpnTucCsc3H21.setBango1C2I4(cpnChoKihon1H21.getBango1C2I4());
        // 第1群拘縮（その他）【変数】.基本調査リスト.第1群拘縮（その他）
        cpnTucCsc3H21.setBango1C2I5(cpnChoKihon1H21.getBango1C2I5());
        // 第1群寝返り【変数】.基本調査リスト.第1群寝返り
        cpnTucCsc3H21.setBango1C3(cpnChoKihon1H21.getBango1C3());
        // 第1群起き上がり【変数】.基本調査リスト.第1群起き上がり
        cpnTucCsc3H21.setBango1C4(cpnChoKihon1H21.getBango1C4());
        // 第1群座位保持【変数】.基本調査リスト.第1群座位保持
        cpnTucCsc3H21.setBango1C5(cpnChoKihon1H21.getBango1C5());
        // 第1群両足での立位保持【変数】.基本調査リスト.第1群両足での立位保持
        cpnTucCsc3H21.setBango1C6(cpnChoKihon1H21.getBango1C6());
        // 第1群歩行【変数】.基本調査リスト.第1群歩行
        cpnTucCsc3H21.setBango1C7(cpnChoKihon1H21.getBango1C7());
        // 第1群立ち上がり【変数】.基本調査リスト.第1群立ち上がり
        cpnTucCsc3H21.setBango1C8(cpnChoKihon1H21.getBango1C8());
        // 第1群片足での立位保持【変数】.基本調査リスト.第1群片足での立位保持
        cpnTucCsc3H21.setBango1C9(cpnChoKihon1H21.getBango1C9());
        // 第1群洗身【変数】.基本調査リスト.第1群洗身
        cpnTucCsc3H21.setBango1C10(cpnChoKihon1H21.getBango1C10());
        // 第1群つめ切り【変数】.基本調査リスト.第1群つめ切り
        cpnTucCsc3H21.setBango1C11(cpnChoKihon1H21.getBango1C11());
        // 第1群視力【変数】.基本調査リスト.第1群視力
        cpnTucCsc3H21.setBango1C12(cpnChoKihon1H21.getBango1C12());
        // 第1群聴力【変数】.基本調査リスト.第1群聴力
        cpnTucCsc3H21.setBango1C13(cpnChoKihon1H21.getBango1C13());
        // 第2群移乗【変数】.基本調査リスト.第2群移乗
        cpnTucCsc3H21.setBango2C1(cpnChoKihon1H21.getBango2C1());
        // 第2群移動【変数】.基本調査リスト.第2群移動
        cpnTucCsc3H21.setBango2C2(cpnChoKihon1H21.getBango2C2());
        // 第2群えん下【変数】.基本調査リスト.第2群えん下
        cpnTucCsc3H21.setBango2C3(cpnChoKihon1H21.getBango2C3());
        // 第2群食事摂取【変数】.基本調査リスト.第2群食事摂取
        cpnTucCsc3H21.setBango2C4(cpnChoKihon1H21.getBango2C4());
        // 第2群排尿【変数】.基本調査リスト.第2群排尿
        cpnTucCsc3H21.setBango2C5(cpnChoKihon1H21.getBango2C5());
        // 第2群排便【変数】.基本調査リスト.第2群排便
        cpnTucCsc3H21.setBango2C6(cpnChoKihon1H21.getBango2C6());
        // 第2群口腔清潔【変数】.基本調査リスト.第2群口腔清潔
        cpnTucCsc3H21.setBango2C7(cpnChoKihon1H21.getBango2C7());
        // 第2群洗顔【変数】.基本調査リスト.第2群洗顔
        cpnTucCsc3H21.setBango2C8(cpnChoKihon1H21.getBango2C8());
        // 第2群整髪【変数】.基本調査リスト.第2群整髪
        cpnTucCsc3H21.setBango2C9(cpnChoKihon1H21.getBango2C9());
        // 第2群上衣の着脱【変数】.基本調査リスト.第2群上衣の着脱
        cpnTucCsc3H21.setBango2C10(cpnChoKihon1H21.getBango2C10());
        // 第2群ズボン等の着脱【変数】.基本調査リスト.第2群ズボン等の着脱
        cpnTucCsc3H21.setBango2C11(cpnChoKihon1H21.getBango2C11());
        // 第2群外出頻度【変数】.基本調査リスト.第2群外出頻度
        cpnTucCsc3H21.setBango2C12(cpnChoKihon1H21.getBango2C12());
        // 第3群意思の伝達【変数】.基本調査リスト.第3群意思の伝達
        cpnTucCsc3H21.setBango3C1(cpnChoKihon1H21.getBango3C1());
        // 第3群毎日の日課を理解【変数】.基本調査リスト.第3群毎日の日課を理解
        cpnTucCsc3H21.setBango3C2(cpnChoKihon1H21.getBango3C2());
        // 第3群生年月日や年齢を言う【変数】.基本調査リスト.第3群生年月日や年齢を言う
        cpnTucCsc3H21.setBango3C3(cpnChoKihon1H21.getBango3C3());
        // 第3群短期記憶【変数】.基本調査リスト.第3群短期記憶
        cpnTucCsc3H21.setBango3C4(cpnChoKihon1H21.getBango3C4());
        // 第3群自分の名前【変数】.基本調査リスト.第3群自分の名前
        cpnTucCsc3H21.setBango3C5(cpnChoKihon1H21.getBango3C5());
        // 第3群今の季節を理解【変数】.基本調査リスト.第3群今の季節を理解
        cpnTucCsc3H21.setBango3C6(cpnChoKihon1H21.getBango3C6());
        // 第3群場所の理解【変数】.基本調査リスト.第3群場所の理解
        cpnTucCsc3H21.setBango3C7(cpnChoKihon1H21.getBango3C7());
        // 第3群徘徊【変数】.基本調査リスト.第3群徘徊
        cpnTucCsc3H21.setBango3C8(cpnChoKihon1H21.getBango3C8());
        // 第3群外出【変数】.基本調査リスト.第3群外出
        cpnTucCsc3H21.setBango3C9(cpnChoKihon1H21.getBango3C9());
        // 第4群被害的【変数】.基本調査リスト.第4群被害的
        cpnTucCsc3H21.setBango4C1(cpnChoKihon1H21.getBango4C1());
        // 第4群作話【変数】.基本調査リスト.第4群作話
        cpnTucCsc3H21.setBango4C2(cpnChoKihon1H21.getBango4C2());
        // 第4群感情が不安定【変数】.基本調査リスト.第4群感情が不安定
        cpnTucCsc3H21.setBango4C3(cpnChoKihon1H21.getBango4C3());
        // 第4群昼夜逆転【変数】.基本調査リスト.第4群昼夜逆転
        cpnTucCsc3H21.setBango4C4(cpnChoKihon1H21.getBango4C4());
        // 第4群同じ話をする【変数】.基本調査リスト.第4群同じ話をする
        cpnTucCsc3H21.setBango4C5(cpnChoKihon1H21.getBango4C5());
        // 第4群大声をだす【変数】.基本調査リスト.第4群大声をだす
        cpnTucCsc3H21.setBango4C6(cpnChoKihon1H21.getBango4C6());
        // 第4群介護に抵抗【変数】.基本調査リスト.第4群介護に抵抗
        cpnTucCsc3H21.setBango4C7(cpnChoKihon1H21.getBango4C7());
        // 第4群落ち着きなし【変数】.基本調査リスト.第4群落ち着きなし
        cpnTucCsc3H21.setBango4C8(cpnChoKihon1H21.getBango4C8());
        // 第4群一人で出たがる【変数】.基本調査リスト.第4群一人で出たがる
        cpnTucCsc3H21.setBango4C9(cpnChoKihon1H21.getBango4C9());
        // 第4群収集癖【変数】.基本調査リスト.第4群収集癖
        cpnTucCsc3H21.setBango4C10(cpnChoKihon1H21.getBango4C10());
        // 第4群物や衣類を壊す【変数】.基本調査リスト.第4群物や衣類を壊す
        cpnTucCsc3H21.setBango4C11(cpnChoKihon1H21.getBango4C11());
        // 第4群物忘れ【変数】.基本調査リスト.第4群物忘れ
        cpnTucCsc3H21.setBango4C12(cpnChoKihon1H21.getBango4C12());
        // 第4群独り言【変数】.基本調査リスト.第4群独り言
        cpnTucCsc3H21.setBango4C13(cpnChoKihon1H21.getBango4C13());
        // 第4群自分勝手な行動【変数】.基本調査リスト.第4群自分勝手な行動
        cpnTucCsc3H21.setBango4C14(cpnChoKihon1H21.getBango4C14());
        // 第4群話がまとまらない【変数】.基本調査リスト.第4群話がまとまらない
        cpnTucCsc3H21.setBango4C15(cpnChoKihon1H21.getBango4C15());
        // 第5群薬の内服【変数】.基本調査リスト.第5群薬の内服
        cpnTucCsc3H21.setBango5C1(cpnChoKihon1H21.getBango5C1());
        // 第5群金銭の管理【変数】.基本調査リスト.第5群金銭の管理
        cpnTucCsc3H21.setBango5C2(cpnChoKihon1H21.getBango5C2());
        // 第5群日常の意思決定【変数】.基本調査リスト.第5群日常の意思決定
        cpnTucCsc3H21.setBango5C3(cpnChoKihon1H21.getBango5C3());
        // 第5群集団への不適応【変数】.基本調査リスト.第5群集団への不適応
        cpnTucCsc3H21.setBango5C4(cpnChoKihon1H21.getBango5C4());
        // 第5群買い物【変数】.基本調査リスト.第5群買い物
        cpnTucCsc3H21.setBango5C5(cpnChoKihon1H21.getBango5C5());
        // 第5群簡単な調理【変数】.基本調査リスト.第5群簡単な調理
        cpnTucCsc3H21.setBango5C6(cpnChoKihon1H21.getBango5C6());
        // 医療関連行為1【変数】.基本調査リスト.医療関連行為1
        cpnTucCsc3H21.setBango6C1(cpnChoKihon1H21.getBango6C1());
        // 医療関連行為2【変数】.基本調査リスト.医療関連行為2
        cpnTucCsc3H21.setBango6C2(cpnChoKihon1H21.getBango6C2());
        // 医療関連行為3【変数】.基本調査リスト.医療関連行為3
        cpnTucCsc3H21.setBango6C3(cpnChoKihon1H21.getBango6C3());
        // 医療関連行為4【変数】.基本調査リスト.医療関連行為4
        cpnTucCsc3H21.setBango6C4(cpnChoKihon1H21.getBango6C4());
        // 医療関連行為5【変数】.基本調査リスト.医療関連行為5
        cpnTucCsc3H21.setBango6C5(cpnChoKihon1H21.getBango6C5());
        // 医療関連行為6【変数】.基本調査リスト.医療関連行為6
        cpnTucCsc3H21.setBango6C6(cpnChoKihon1H21.getBango6C6());
        // 医療関連行為7【変数】.基本調査リスト.医療関連行為7
        cpnTucCsc3H21.setBango6C7(cpnChoKihon1H21.getBango6C7());
        // 医療関連行為8【変数】.基本調査リスト.医療関連行為8
        cpnTucCsc3H21.setBango6C8(cpnChoKihon1H21.getBango6C8());
        // 医療関連行為9【変数】.基本調査リスト.医療関連行為9
        cpnTucCsc3H21.setBango6C9(cpnChoKihon1H21.getBango6C9());
        // 医療関連行為10【変数】.基本調査リスト.医療関連行為10
        cpnTucCsc3H21.setBango6C10(cpnChoKihon1H21.getBango6C10());
        // 医療関連行為11【変数】.基本調査リスト.医療関連行為11
        cpnTucCsc3H21.setBango6C11(cpnChoKihon1H21.getBango6C11());
        // 医療関連行為12【変数】.基本調査リスト.医療関連行為12
        cpnTucCsc3H21.setBango6C12(cpnChoKihon1H21.getBango6C12());
        // 寝たきり度【変数】.基本調査リスト.寝たきり度
        cpnTucCsc3H21.setBango7C1(cpnChoKihon1H21.getBango7C1());
        // 痴呆度【変数】.基本調査リスト.痴呆度
        cpnTucCsc3H21.setBango7C2(cpnChoKihon1H21.getBango7C2());
        // DAOを実行
        int updateCnt = this.cpnTucCsc3H21Mapper.updateByCriteriaSelective(cpnTucCsc3H21, criteria);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (updateCnt <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 【Ｈ２１改訂版認定調査票（基本）】情報を登録する。
     * 
     * @param cschId          【変数】.調査票ID
     * @param inDto           調査票複数複写確定の入力DTO.
     * @param cpnChoKihon1H21 【変数】.基本調査リスト
     */
    private void cpnTucCsc3H21Insert(Integer cschId, SurveySlipDuplicateMultipleUpdateServiceInDto inDto,
            CpnChoKihon1H21OutEntity cpnChoKihon1H21) {
        final CpnTucCsc3H21 cpnTucCsc3H21 = new CpnTucCsc3H21();
        // 調査票ID【変数】.調査票ID
        cpnTucCsc3H21.setCschId(cschId);
        // 計画期間ID リクエストパラメータ.複写先計画期間ID
        cpnTucCsc3H21.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSakiSc1Id()));
        // 第1群麻痺（なし）【変数】.基本調査リスト.第1群麻痺（なし）
        cpnTucCsc3H21.setBango1C1I1(cpnChoKihon1H21.getBango1C1I1());
        // 第1群（左上肢）【変数】.基本調査リスト.第1群（左上肢）
        cpnTucCsc3H21.setBango1C1I2(cpnChoKihon1H21.getBango1C1I2());
        // 第1群（右上肢）【変数】.基本調査リスト.第1群（右上肢）
        cpnTucCsc3H21.setBango1C1I3(cpnChoKihon1H21.getBango1C1I3());
        // 第1群（左下肢）【変数】.基本調査リスト.第1群（左下肢）
        cpnTucCsc3H21.setBango1C1I4(cpnChoKihon1H21.getBango1C1I4());
        // 第1群（右下肢）【変数】.基本調査リスト.第1群（右下肢）
        cpnTucCsc3H21.setBango1C1I5(cpnChoKihon1H21.getBango1C1I5());
        // 第1群（その他）【変数】.基本調査リスト.第1群（その他）
        cpnTucCsc3H21.setBango1C1I6(cpnChoKihon1H21.getBango1C1I6());
        // 第1群拘縮（なし）【変数】.基本調査リスト.第1群拘縮（なし）
        cpnTucCsc3H21.setBango1C2I1(cpnChoKihon1H21.getBango1C2I1());
        // 第1群拘縮（肩）【変数】.基本調査リスト.第1群拘縮（肩）
        cpnTucCsc3H21.setBango1C2I2(cpnChoKihon1H21.getBango1C2I2());
        // 第1群拘縮（股）【変数】.基本調査リスト.第1群拘縮（股）
        cpnTucCsc3H21.setBango1C2I3(cpnChoKihon1H21.getBango1C2I3());
        // 第1群拘縮（膝）【変数】.基本調査リスト.第1群拘縮（膝）
        cpnTucCsc3H21.setBango1C2I4(cpnChoKihon1H21.getBango1C2I4());
        // 第1群拘縮（その他）【変数】.基本調査リスト.第1群拘縮（その他）
        cpnTucCsc3H21.setBango1C2I5(cpnChoKihon1H21.getBango1C2I5());
        // 第1群寝返り【変数】.基本調査リスト.第1群寝返り
        cpnTucCsc3H21.setBango1C3(cpnChoKihon1H21.getBango1C3());
        // 第1群起き上がり【変数】.基本調査リスト.第1群起き上がり
        cpnTucCsc3H21.setBango1C4(cpnChoKihon1H21.getBango1C4());
        // 第1群座位保持【変数】.基本調査リスト.第1群座位保持
        cpnTucCsc3H21.setBango1C5(cpnChoKihon1H21.getBango1C5());
        // 第1群両足での立位保持【変数】.基本調査リスト.第1群両足での立位保持
        cpnTucCsc3H21.setBango1C6(cpnChoKihon1H21.getBango1C6());
        // 第1群歩行【変数】.基本調査リスト.第1群歩行
        cpnTucCsc3H21.setBango1C7(cpnChoKihon1H21.getBango1C7());
        // 第1群立ち上がり【変数】.基本調査リスト.第1群立ち上がり
        cpnTucCsc3H21.setBango1C8(cpnChoKihon1H21.getBango1C8());
        // 第1群片足での立位保持【変数】.基本調査リスト.第1群片足での立位保持
        cpnTucCsc3H21.setBango1C9(cpnChoKihon1H21.getBango1C9());
        // 第1群洗身【変数】.基本調査リスト.第1群洗身
        cpnTucCsc3H21.setBango1C10(cpnChoKihon1H21.getBango1C10());
        // 第1群つめ切り【変数】.基本調査リスト.第1群つめ切り
        cpnTucCsc3H21.setBango1C11(cpnChoKihon1H21.getBango1C11());
        // 第1群視力【変数】.基本調査リスト.第1群視力
        cpnTucCsc3H21.setBango1C12(cpnChoKihon1H21.getBango1C12());
        // 第1群聴力【変数】.基本調査リスト.第1群聴力
        cpnTucCsc3H21.setBango1C13(cpnChoKihon1H21.getBango1C13());
        // 第2群移乗【変数】.基本調査リスト.第2群移乗
        cpnTucCsc3H21.setBango2C1(cpnChoKihon1H21.getBango2C1());
        // 第2群移動【変数】.基本調査リスト.第2群移動
        cpnTucCsc3H21.setBango2C2(cpnChoKihon1H21.getBango2C2());
        // 第2群えん下【変数】.基本調査リスト.第2群えん下
        cpnTucCsc3H21.setBango2C3(cpnChoKihon1H21.getBango2C3());
        // 第2群食事摂取【変数】.基本調査リスト.第2群食事摂取
        cpnTucCsc3H21.setBango2C4(cpnChoKihon1H21.getBango2C4());
        // 第2群排尿【変数】.基本調査リスト.第2群排尿
        cpnTucCsc3H21.setBango2C5(cpnChoKihon1H21.getBango2C5());
        // 第2群排便【変数】.基本調査リスト.第2群排便
        cpnTucCsc3H21.setBango2C6(cpnChoKihon1H21.getBango2C6());
        // 第2群口腔清潔【変数】.基本調査リスト.第2群口腔清潔
        cpnTucCsc3H21.setBango2C7(cpnChoKihon1H21.getBango2C7());
        // 第2群洗顔【変数】.基本調査リスト.第2群洗顔
        cpnTucCsc3H21.setBango2C8(cpnChoKihon1H21.getBango2C8());
        // 第2群整髪【変数】.基本調査リスト.第2群整髪
        cpnTucCsc3H21.setBango2C9(cpnChoKihon1H21.getBango2C9());
        // 第2群上衣の着脱【変数】.基本調査リスト.第2群上衣の着脱
        cpnTucCsc3H21.setBango2C10(cpnChoKihon1H21.getBango2C10());
        // 第2群ズボン等の着脱【変数】.基本調査リスト.第2群ズボン等の着脱
        cpnTucCsc3H21.setBango2C11(cpnChoKihon1H21.getBango2C11());
        // 第2群外出頻度【変数】.基本調査リスト.第2群外出頻度
        cpnTucCsc3H21.setBango2C12(cpnChoKihon1H21.getBango2C12());
        // 第3群意思の伝達【変数】.基本調査リスト.第3群意思の伝達
        cpnTucCsc3H21.setBango3C1(cpnChoKihon1H21.getBango3C1());
        // 第3群毎日の日課を理解【変数】.基本調査リスト.第3群毎日の日課を理解
        cpnTucCsc3H21.setBango3C2(cpnChoKihon1H21.getBango3C2());
        // 第3群生年月日や年齢を言う【変数】.基本調査リスト.第3群生年月日や年齢を言う
        cpnTucCsc3H21.setBango3C3(cpnChoKihon1H21.getBango3C3());
        // 第3群短期記憶【変数】.基本調査リスト.第3群短期記憶
        cpnTucCsc3H21.setBango3C4(cpnChoKihon1H21.getBango3C4());
        // 第3群自分の名前【変数】.基本調査リスト.第3群自分の名前
        cpnTucCsc3H21.setBango3C5(cpnChoKihon1H21.getBango3C5());
        // 第3群今の季節を理解【変数】.基本調査リスト.第3群今の季節を理解
        cpnTucCsc3H21.setBango3C6(cpnChoKihon1H21.getBango3C6());
        // 第3群場所の理解【変数】.基本調査リスト.第3群場所の理解
        cpnTucCsc3H21.setBango3C7(cpnChoKihon1H21.getBango3C7());
        // 第3群徘徊【変数】.基本調査リスト.第3群徘徊
        cpnTucCsc3H21.setBango3C8(cpnChoKihon1H21.getBango3C8());
        // 第3群外出【変数】.基本調査リスト.第3群外出
        cpnTucCsc3H21.setBango3C9(cpnChoKihon1H21.getBango3C9());
        // 第4群被害的【変数】.基本調査リスト.第4群被害的
        cpnTucCsc3H21.setBango4C1(cpnChoKihon1H21.getBango4C1());
        // 第4群作話【変数】.基本調査リスト.第4群作話
        cpnTucCsc3H21.setBango4C2(cpnChoKihon1H21.getBango4C2());
        // 第4群感情が不安定【変数】.基本調査リスト.第4群感情が不安定
        cpnTucCsc3H21.setBango4C3(cpnChoKihon1H21.getBango4C3());
        // 第4群昼夜逆転【変数】.基本調査リスト.第4群昼夜逆転
        cpnTucCsc3H21.setBango4C4(cpnChoKihon1H21.getBango4C4());
        // 第4群同じ話をする【変数】.基本調査リスト.第4群同じ話をする
        cpnTucCsc3H21.setBango4C5(cpnChoKihon1H21.getBango4C5());
        // 第4群大声をだす【変数】.基本調査リスト.第4群大声をだす
        cpnTucCsc3H21.setBango4C6(cpnChoKihon1H21.getBango4C6());
        // 第4群介護に抵抗【変数】.基本調査リスト.第4群介護に抵抗
        cpnTucCsc3H21.setBango4C7(cpnChoKihon1H21.getBango4C7());
        // 第4群落ち着きなし【変数】.基本調査リスト.第4群落ち着きなし
        cpnTucCsc3H21.setBango4C8(cpnChoKihon1H21.getBango4C8());
        // 第4群一人で出たがる【変数】.基本調査リスト.第4群一人で出たがる
        cpnTucCsc3H21.setBango4C9(cpnChoKihon1H21.getBango4C9());
        // 第4群収集癖【変数】.基本調査リスト.第4群収集癖
        cpnTucCsc3H21.setBango4C10(cpnChoKihon1H21.getBango4C10());
        // 第4群物や衣類を壊す【変数】.基本調査リスト.第4群物や衣類を壊す
        cpnTucCsc3H21.setBango4C11(cpnChoKihon1H21.getBango4C11());
        // 第4群物忘れ【変数】.基本調査リスト.第4群物忘れ
        cpnTucCsc3H21.setBango4C12(cpnChoKihon1H21.getBango4C12());
        // 第4群独り言【変数】.基本調査リスト.第4群独り言
        cpnTucCsc3H21.setBango4C13(cpnChoKihon1H21.getBango4C13());
        // 第4群自分勝手な行動【変数】.基本調査リスト.第4群自分勝手な行動
        cpnTucCsc3H21.setBango4C14(cpnChoKihon1H21.getBango4C14());
        // 第4群話がまとまらない【変数】.基本調査リスト.第4群話がまとまらない
        cpnTucCsc3H21.setBango4C15(cpnChoKihon1H21.getBango4C15());
        // 第5群薬の内服【変数】.基本調査リスト.第5群薬の内服
        cpnTucCsc3H21.setBango5C1(cpnChoKihon1H21.getBango5C1());
        // 第5群金銭の管理【変数】.基本調査リスト.第5群金銭の管理
        cpnTucCsc3H21.setBango5C2(cpnChoKihon1H21.getBango5C2());
        // 第5群日常の意思決定【変数】.基本調査リスト.第5群日常の意思決定
        cpnTucCsc3H21.setBango5C3(cpnChoKihon1H21.getBango5C3());
        // 第5群集団への不適応【変数】.基本調査リスト.第5群集団への不適応
        cpnTucCsc3H21.setBango5C4(cpnChoKihon1H21.getBango5C4());
        // 第5群買い物【変数】.基本調査リスト.第5群買い物
        cpnTucCsc3H21.setBango5C5(cpnChoKihon1H21.getBango5C5());
        // 第5群簡単な調理【変数】.基本調査リスト.第5群簡単な調理
        cpnTucCsc3H21.setBango5C6(cpnChoKihon1H21.getBango5C6());
        // 医療関連行為1【変数】.基本調査リスト.医療関連行為1
        cpnTucCsc3H21.setBango6C1(cpnChoKihon1H21.getBango6C1());
        // 医療関連行為2【変数】.基本調査リスト.医療関連行為2
        cpnTucCsc3H21.setBango6C2(cpnChoKihon1H21.getBango6C2());
        // 医療関連行為3【変数】.基本調査リスト.医療関連行為3
        cpnTucCsc3H21.setBango6C3(cpnChoKihon1H21.getBango6C3());
        // 医療関連行為4【変数】.基本調査リスト.医療関連行為4
        cpnTucCsc3H21.setBango6C4(cpnChoKihon1H21.getBango6C4());
        // 医療関連行為5【変数】.基本調査リスト.医療関連行為5
        cpnTucCsc3H21.setBango6C5(cpnChoKihon1H21.getBango6C5());
        // 医療関連行為6【変数】.基本調査リスト.医療関連行為6
        cpnTucCsc3H21.setBango6C6(cpnChoKihon1H21.getBango6C6());
        // 医療関連行為7【変数】.基本調査リスト.医療関連行為7
        cpnTucCsc3H21.setBango6C7(cpnChoKihon1H21.getBango6C7());
        // 医療関連行為8【変数】.基本調査リスト.医療関連行為8
        cpnTucCsc3H21.setBango6C8(cpnChoKihon1H21.getBango6C8());
        // 医療関連行為9【変数】.基本調査リスト.医療関連行為9
        cpnTucCsc3H21.setBango6C9(cpnChoKihon1H21.getBango6C9());
        // 医療関連行為10【変数】.基本調査リスト.医療関連行為10
        cpnTucCsc3H21.setBango6C10(cpnChoKihon1H21.getBango6C10());
        // 医療関連行為11【変数】.基本調査リスト.医療関連行為11
        cpnTucCsc3H21.setBango6C11(cpnChoKihon1H21.getBango6C11());
        // 医療関連行為12【変数】.基本調査リスト.医療関連行為12
        cpnTucCsc3H21.setBango6C12(cpnChoKihon1H21.getBango6C12());
        // 寝たきり度【変数】.基本調査リスト.寝たきり度
        cpnTucCsc3H21.setBango7C1(cpnChoKihon1H21.getBango7C1());
        // 痴呆度【変数】.基本調査リスト.痴呆度
        cpnTucCsc3H21.setBango7C2(cpnChoKihon1H21.getBango7C2());
        // DAOを実行
        this.cpnTucCsc3H21Mapper.insertSelective(cpnTucCsc3H21);
    }

    /**
     * 【改訂版認定調査票（サービス利用）】情報を更新する。
     * 
     * @param gss 【変数】.概況調査リスト
     * @throws Exception Exception
     */
    private void cpnTucCsc22Update(Gui01268Gss gss, Integer cschId) throws Exception {
        final CpnTucCsc22Criteria criteria = new CpnTucCsc22Criteria();
        // 調査票ID＝【変数】.概況調査リスト.調査票ID
        criteria.createCriteria().andCschIdEqualTo(cschId);
        final CpnTucCsc22 cpnTucCsc22 = new CpnTucCsc22();
        // サービス利用CD1【変数】.概況調査リスト.サービス利用CD1
        cpnTucCsc22.setService1Cd(CommonDtoUtil.strValToInt(gss.getService1Cd()));
        // サービス利用CD2【変数】.概況調査リスト.サービス利用CD2
        cpnTucCsc22.setService2Cd(CommonDtoUtil.strValToInt(gss.getService2Cd()));
        // サービス利用CD3【変数】.概況調査リスト.サービス利用CD3
        cpnTucCsc22.setService3Cd(CommonDtoUtil.strValToInt(gss.getService3Cd()));
        // サービス利用CD4【変数】.概況調査リスト.サービス利用CD4
        cpnTucCsc22.setService4Cd(CommonDtoUtil.strValToInt(gss.getService4Cd()));
        // サービス利用CD5【変数】.概況調査リスト.サービス利用CD5
        cpnTucCsc22.setService5Cd(CommonDtoUtil.strValToInt(gss.getService5Cd()));
        // サービス利用CD6【変数】.概況調査リスト.サービス利用CD6
        cpnTucCsc22.setService6Cd(CommonDtoUtil.strValToInt(gss.getService6Cd()));
        // サービス利用CD7【変数】.概況調査リスト.サービス利用CD7
        cpnTucCsc22.setService7Cd(CommonDtoUtil.strValToInt(gss.getService7Cd()));
        // サービス利用CD8【変数】.概況調査リスト.サービス利用CD8
        cpnTucCsc22.setService8Cd(CommonDtoUtil.strValToInt(gss.getService8Cd()));
        // サービス利用CD9【変数】.概況調査リスト.サービス利用CD9
        cpnTucCsc22.setService9Cd(CommonDtoUtil.strValToInt(gss.getService9Cd()));
        // サービス利用CD10【変数】.概況調査リスト.サービス利用CD10
        cpnTucCsc22.setService10Cd(CommonDtoUtil.strValToInt(gss.getService10Cd()));
        // サービス利用CD11【変数】.概況調査リスト.サービス利用CD11
        cpnTucCsc22.setService11Cd(CommonDtoUtil.strValToInt(gss.getService11Cd()));
        // サービス利用CD12【変数】.概況調査リスト.サービス利用CD12
        cpnTucCsc22.setService12Cd(CommonDtoUtil.strValToInt(gss.getService12Cd()));
        // サービス利用CD13【変数】.概況調査リスト.サービス利用CD13
        cpnTucCsc22.setService13Cd(CommonDtoUtil.strValToInt(gss.getService13Cd()));
        // サービス利用CD14【変数】.概況調査リスト.サービス利用CD14
        cpnTucCsc22.setService14Cd(CommonDtoUtil.strValToInt(gss.getService14Cd()));
        // サービス利用CD15【変数】.概況調査リスト.サービス利用CD15
        cpnTucCsc22.setService15Cd(CommonDtoUtil.strValToInt(gss.getService15Cd()));
        // サービス利用CD16【変数】.概況調査リスト.サービス利用CD16
        cpnTucCsc22.setService16Cd(CommonDtoUtil.strValToInt(gss.getService16Cd()));
        // 利用回数1【変数】.概況調査リスト.利用回数1
        cpnTucCsc22.setKaisuu1(CommonDtoUtil.strValToInt(gss.getKaisuu1()));
        // 利用回数2【変数】.概況調査リスト.利用回数2
        cpnTucCsc22.setKaisuu2(CommonDtoUtil.strValToInt(gss.getKaisuu2()));
        // 利用回数3【変数】.概況調査リスト.利用回数3
        cpnTucCsc22.setKaisuu3(CommonDtoUtil.strValToInt(gss.getKaisuu3()));
        // 利用回数4【変数】.概況調査リスト.利用回数4
        cpnTucCsc22.setKaisuu4(CommonDtoUtil.strValToInt(gss.getKaisuu4()));
        // 利用回数5【変数】.概況調査リスト.利用回数5
        cpnTucCsc22.setKaisuu5(CommonDtoUtil.strValToInt(gss.getKaisuu5()));
        // 利用回数6【変数】.概況調査リスト.利用回数6
        cpnTucCsc22.setKaisuu6(CommonDtoUtil.strValToInt(gss.getKaisuu6()));
        // 利用回数7【変数】.概況調査リスト.利用回数7
        cpnTucCsc22.setKaisuu7(CommonDtoUtil.strValToInt(gss.getKaisuu7()));
        // 利用回数8【変数】.概況調査リスト.利用回数8
        cpnTucCsc22.setKaisuu8(CommonDtoUtil.strValToInt(gss.getKaisuu8()));
        // 利用回数9【変数】.概況調査リスト.利用回数9
        cpnTucCsc22.setKaisuu9(CommonDtoUtil.strValToInt(gss.getKaisuu9()));
        // 利用回数10【変数】.概況調査リスト.利用回数10
        cpnTucCsc22.setKaisuu10(CommonDtoUtil.strValToInt(gss.getKaisuu10()));
        // 利用回数11【変数】.概況調査リスト.利用回数11
        cpnTucCsc22.setKaisuu11(CommonDtoUtil.strValToInt(gss.getKaisuu11()));
        // 利用回数12【変数】.概況調査リスト.利用回数12
        cpnTucCsc22.setKaisuu12(CommonDtoUtil.strValToInt(gss.getKaisuu12()));
        // 利用回数13【変数】.概況調査リスト.利用回数13
        cpnTucCsc22.setKaisuu13(CommonDtoUtil.strValToInt(gss.getKaisuu13()));
        // 改修あり・なし【変数】.概況調査リスト.改修あり・なし
        cpnTucCsc22.setKaishuUmu(CommonDtoUtil.strValToInt(gss.getKaishuUmu()));
        // 特別給付【変数】.概況調査リスト.特別給付
        cpnTucCsc22.setMemo1Knj(gss.getMemo1Knj());
        // 給付外サービス【変数】.概況調査リスト.給付外サービス
        cpnTucCsc22.setMemo2Knj(gss.getMemo2Knj());
        // サービス利用CD17【変数】.概況調査リスト.サービス利用CD17
        cpnTucCsc22.setService17Cd(CommonDtoUtil.strValToInt(gss.getService17Cd()));
        // サービス利用CD18【変数】.概況調査リスト.サービス利用CD18
        cpnTucCsc22.setService18Cd(CommonDtoUtil.strValToInt(gss.getService18Cd()));
        // サービス利用CD19【変数】.概況調査リスト.サービス利用CD19
        cpnTucCsc22.setService19Cd(CommonDtoUtil.strValToInt(gss.getService19Cd()));
        // サービス利用CD20【変数】.概況調査リスト.サービス利用CD20
        cpnTucCsc22.setService20Cd(CommonDtoUtil.strValToInt(gss.getService20Cd()));
        // サービス利用CD21【変数】.概況調査リスト.サービス利用CD21
        cpnTucCsc22.setService21Cd(CommonDtoUtil.strValToInt(gss.getService21Cd()));
        // 利用回数17【変数】.概況調査リスト.利用回数17
        cpnTucCsc22.setKaisuu17(CommonDtoUtil.strValToInt(gss.getKaisuu17()));
        // 利用回数18【変数】.概況調査リスト.利用回数18
        cpnTucCsc22.setKaisuu18(CommonDtoUtil.strValToInt(gss.getKaisuu18()));
        // 利用回数19【変数】.概況調査リスト.利用回数19
        cpnTucCsc22.setKaisuu19(CommonDtoUtil.strValToInt(gss.getKaisuu19()));
        // 利用回数20【変数】.概況調査リスト.利用回数20
        cpnTucCsc22.setKaisuu20(CommonDtoUtil.strValToInt(gss.getKaisuu20()));
        // 利用回数21【変数】.概況調査リスト.利用回数21
        cpnTucCsc22.setKaisuu21(CommonDtoUtil.strValToInt(gss.getKaisuu21()));
        // サービス利用CD22【変数】.概況調査リスト.サービス利用CD22
        cpnTucCsc22.setService22Cd(CommonDtoUtil.strValToInt(gss.getService22Cd()));
        // サービス利用CD23【変数】.概況調査リスト.サービス利用CD23
        cpnTucCsc22.setService23Cd(CommonDtoUtil.strValToInt(gss.getService23Cd()));
        // 利用回数22【変数】.概況調査リスト.利用回数22
        cpnTucCsc22.setKaisuu22(CommonDtoUtil.strValToInt(gss.getKaisuu22()));
        // 利用回数23【変数】.概況調査リスト.利用回数23
        cpnTucCsc22.setKaisuu23(CommonDtoUtil.strValToInt(gss.getKaisuu23()));
        // DAOを実行
        int updateCnt = this.cpnTucCsc22Mapper.updateByCriteriaSelective(cpnTucCsc22, criteria);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (updateCnt <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 【改訂版認定調査票（概況調査）】情報を更新する。
     * 
     * @param gss 【変数】.概況調査リスト
     * @throws Exception Exception
     */
    private void cpnTucCsc12Update(Gui01268Gss gss, Integer cschId) throws Exception {
        CpnTucCsc12ForCscCopyInEntity cpnTucCsc12 = new CpnTucCsc12ForCscCopyInEntity();
        // 調査票ID＝【変数】.概況調査リスト.調査票ID
        cpnTucCsc12.setCschId(cschId);
        // 地域コード【変数】.概況調査リスト.地域コード
        cpnTucCsc12.setChiikiCd(CommonDtoUtil.strValToInt(gss.getChiikiCd()));
        // 調査対象者コード【変数】.概況調査リスト.調査対象者コード
        cpnTucCsc12.setTaishoushaCd(gss.getTaishoushaCd());
        // 実施場所【変数】.概況調査リスト.実施場所
        cpnTucCsc12.setWhereCd(CommonDtoUtil.strValToInt(gss.getWhereCd()));
        // 実施場所その他【変数】.概況調査リスト.実施場所その他
        cpnTucCsc12.setWhereMemoKnj(gss.getWhereMemoKnj());
        // 過去の認定【変数】.概況調査リスト.過去の認定
        cpnTucCsc12.setOldNintei(CommonDtoUtil.strValToInt(gss.getOldNintei()));
        // 前回認定日【変数】.概況調査リスト.前回認定日
        cpnTucCsc12.setOldNinteiYmd(gss.getOldNinteiYmd());
        // 前回認定結果【変数】.概況調査リスト.前回認定結果
        cpnTucCsc12.setOldNinteiDo1(CommonDtoUtil.strValToInt(gss.getOldNinteiDo1()));
        // 前回認定結果（要介護度）【変数】.概況調査リスト.前回認定結果（要介護度）
        cpnTucCsc12.setOldNinteiDo2(CommonDtoUtil.strValToInt(gss.getOldNinteiDo2()));
        // 関係者連番【変数】.概況調査リスト.関係者連番
        cpnTucCsc12.setKankeiId(CommonDtoUtil.strValToInt(gss.getKankeiId()));
        // 連絡先氏名【変数】.概況調査リスト.連絡先氏名
        cpnTucCsc12.setRenrakuNameKnj(gss.getRenrakuNameKnj());
        // 連絡先続柄【変数】.概況調査リスト.連絡先続柄
        cpnTucCsc12.setRenrakuZcode(CommonDtoUtil.strValToInt(gss.getRenrakuZcode()));
        // 連絡先〒【変数】.概況調査リスト.連絡先〒
        cpnTucCsc12.setRenrakuZip(gss.getRenrakuZip());
        // 連絡先ＴＥＬ【変数】.概況調査リスト.連絡先ＴＥＬ
        cpnTucCsc12.setRenrakuTel(gss.getRenrakuTel());
        // 県コード【変数】.概況調査リスト.県コード
        cpnTucCsc12.setKencode(CommonDtoUtil.strValToInt(gss.getKencode()));
        // 市町村コード【変数】.概況調査リスト.市町村コード
        cpnTucCsc12.setCitycode(CommonDtoUtil.strValToInt(gss.getCitycode()));
        // 地区コード【変数】.概況調査リスト.地区コード
        cpnTucCsc12.setAreacode(CommonDtoUtil.strValToInt(gss.getAreacode()));
        // 連絡先住所【変数】.概況調査リスト.連絡先住所
        cpnTucCsc12.setAddressKnj(gss.getAddressKnj());
        // 障害老人ADL【変数】.概況調査リスト.障害老人ADL
        cpnTucCsc12.setAdl1Id(CommonDtoUtil.strValToInt(gss.getAdl1Id()));
        // 痴呆老人ADL【変数】.概況調査リスト.痴呆老人ADL
        cpnTucCsc12.setAdl2Id(CommonDtoUtil.strValToInt(gss.getAdl2Id()));
        // 利用施設種別【変数】.概況調査リスト.利用施設種別
        cpnTucCsc12.setShisetsuShu(CommonDtoUtil.strValToInt(gss.getShisetsuShu()));
        // 利用施設ＩＤ【変数】.概況調査リスト.利用施設ＩＤ
        cpnTucCsc12.setShisetsuId(CommonDtoUtil.strValToInt(gss.getShisetsuId()));
        // 施設名【変数】.概況調査リスト.施設名
        cpnTucCsc12.setShisetsuNameKnj(gss.getShisetsuNameKnj());
        // 内容・特記事項【変数】.概況調査リスト.内容・特記事項
        cpnTucCsc12.setMemoKnj(gss.getMemoKnj());
        // 要介護状態見込み【変数】.概況調査リスト.要介護状態見込み
        cpnTucCsc12.setYokaiKbn1(CommonDtoUtil.strValToInt(gss.getYokaiKbn1()));
        // 要介護状態確定【変数】.概況調査リスト.要介護状態確定
        cpnTucCsc12.setYokaiKbn2(CommonDtoUtil.strValToInt(gss.getYokaiKbn2()));
        // 認定年月日【変数】.概況調査リスト.認定年月日
        cpnTucCsc12.setNinteiYmd(gss.getNinteiYmd());
        // サービス事業者ID【変数】.概況調査リスト.サービス事業者ID
        cpnTucCsc12.setSvJigyoId(CommonDtoUtil.strValToInt(gss.getSvJigyoId()));
        // 認定申請日【変数】.概況調査リスト.認定申請日
        cpnTucCsc12.setNinteiShinseiYmd(gss.getNinteiShinseiYmd());
        // 施設名【変数】.概況調査リスト.施設名
        cpnTucCsc12.setShisetuKnj(gss.getShisetuKnj());
        // 郵便番号【変数】.概況調査リスト.郵便番号
        cpnTucCsc12.setShisetsuZip(gss.getShisetsuZip());
        // 施設住所【変数】.概況調査リスト.施設住所
        cpnTucCsc12.setShisetsuAddressKnj(gss.getShisetsuAddressKnj());
        // 電話番号【変数】.概況調査リスト.電話番号
        cpnTucCsc12.setShisetsuTel(gss.getShisetsuTel());
        // 前回認定結果（要支援度）【変数】.概況調査リスト.前回認定結果（要支援度）
        cpnTucCsc12.setOldNinteiDo3(CommonDtoUtil.strValToInt(gss.getOldNinteiDo3()));
        // 家族の状況 null
        cpnTucCsc12.setKazokuJokyo(null);
        // DAOを実行
        int updateCnt = cmnUniqueUpdateMapper.updateCpnTucCsc12ForCscCopy(cpnTucCsc12);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (updateCnt <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 【改訂版認定調査票（サービス利用）】情報を登録する。
     * 
     * @param cschId 【変数】.調査票ID
     * @param inDto  調査票複数複写確定の入力DTO.
     * @param gss    【変数】.概況調査リスト
     */
    private void cpnTucCsc22Insert(Integer cschId, SurveySlipDuplicateMultipleUpdateServiceInDto inDto,
            Gui01268Gss gss) {
        final CpnTucCsc22 cpnTucCsc22 = new CpnTucCsc22();
        // 調査票ID【変数】.調査票ID
        cpnTucCsc22.setCschId(cschId);
        // 計画期間ID リクエストパラメータ.複写先計画期間ID
        cpnTucCsc22.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSakiSc1Id()));
        // サービス利用CD1【変数】.概況調査リスト.サービス利用CD1
        cpnTucCsc22.setService1Cd(CommonDtoUtil.strValToInt(gss.getService1Cd()));
        // サービス利用CD2【変数】.概況調査リスト.サービス利用CD2
        cpnTucCsc22.setService2Cd(CommonDtoUtil.strValToInt(gss.getService2Cd()));
        // サービス利用CD3【変数】.概況調査リスト.サービス利用CD3
        cpnTucCsc22.setService3Cd(CommonDtoUtil.strValToInt(gss.getService3Cd()));
        // サービス利用CD4【変数】.概況調査リスト.サービス利用CD4
        cpnTucCsc22.setService4Cd(CommonDtoUtil.strValToInt(gss.getService4Cd()));
        // サービス利用CD5【変数】.概況調査リスト.サービス利用CD5
        cpnTucCsc22.setService5Cd(CommonDtoUtil.strValToInt(gss.getService5Cd()));
        // サービス利用CD6【変数】.概況調査リスト.サービス利用CD6
        cpnTucCsc22.setService6Cd(CommonDtoUtil.strValToInt(gss.getService6Cd()));
        // サービス利用CD7【変数】.概況調査リスト.サービス利用CD7
        cpnTucCsc22.setService7Cd(CommonDtoUtil.strValToInt(gss.getService7Cd()));
        // サービス利用CD8【変数】.概況調査リスト.サービス利用CD8
        cpnTucCsc22.setService8Cd(CommonDtoUtil.strValToInt(gss.getService8Cd()));
        // サービス利用CD9【変数】.概況調査リスト.サービス利用CD9
        cpnTucCsc22.setService9Cd(CommonDtoUtil.strValToInt(gss.getService9Cd()));
        // サービス利用CD10【変数】.概況調査リスト.サービス利用CD10
        cpnTucCsc22.setService10Cd(CommonDtoUtil.strValToInt(gss.getService10Cd()));
        // サービス利用CD11【変数】.概況調査リスト.サービス利用CD11
        cpnTucCsc22.setService11Cd(CommonDtoUtil.strValToInt(gss.getService11Cd()));
        // サービス利用CD12【変数】.概況調査リスト.サービス利用CD12
        cpnTucCsc22.setService12Cd(CommonDtoUtil.strValToInt(gss.getService12Cd()));
        // サービス利用CD13【変数】.概況調査リスト.サービス利用CD13
        cpnTucCsc22.setService13Cd(CommonDtoUtil.strValToInt(gss.getService13Cd()));
        // サービス利用CD14【変数】.概況調査リスト.サービス利用CD14
        cpnTucCsc22.setService14Cd(CommonDtoUtil.strValToInt(gss.getService14Cd()));
        // サービス利用CD15【変数】.概況調査リスト.サービス利用CD15
        cpnTucCsc22.setService15Cd(CommonDtoUtil.strValToInt(gss.getService15Cd()));
        // サービス利用CD16【変数】.概況調査リスト.サービス利用CD16
        cpnTucCsc22.setService16Cd(CommonDtoUtil.strValToInt(gss.getService16Cd()));
        // 利用回数1【変数】.概況調査リスト.利用回数1
        cpnTucCsc22.setKaisuu1(CommonDtoUtil.strValToInt(gss.getKaisuu1()));
        // 利用回数2【変数】.概況調査リスト.利用回数2
        cpnTucCsc22.setKaisuu2(CommonDtoUtil.strValToInt(gss.getKaisuu2()));
        // 利用回数3【変数】.概況調査リスト.利用回数3
        cpnTucCsc22.setKaisuu3(CommonDtoUtil.strValToInt(gss.getKaisuu3()));
        // 利用回数4【変数】.概況調査リスト.利用回数4
        cpnTucCsc22.setKaisuu4(CommonDtoUtil.strValToInt(gss.getKaisuu4()));
        // 利用回数5【変数】.概況調査リスト.利用回数5
        cpnTucCsc22.setKaisuu5(CommonDtoUtil.strValToInt(gss.getKaisuu5()));
        // 利用回数6【変数】.概況調査リスト.利用回数6
        cpnTucCsc22.setKaisuu6(CommonDtoUtil.strValToInt(gss.getKaisuu6()));
        // 利用回数7【変数】.概況調査リスト.利用回数7
        cpnTucCsc22.setKaisuu7(CommonDtoUtil.strValToInt(gss.getKaisuu7()));
        // 利用回数8【変数】.概況調査リスト.利用回数8
        cpnTucCsc22.setKaisuu8(CommonDtoUtil.strValToInt(gss.getKaisuu8()));
        // 利用回数9【変数】.概況調査リスト.利用回数9
        cpnTucCsc22.setKaisuu9(CommonDtoUtil.strValToInt(gss.getKaisuu9()));
        // 利用回数10【変数】.概況調査リスト.利用回数10
        cpnTucCsc22.setKaisuu10(CommonDtoUtil.strValToInt(gss.getKaisuu10()));
        // 利用回数11【変数】.概況調査リスト.利用回数11
        cpnTucCsc22.setKaisuu11(CommonDtoUtil.strValToInt(gss.getKaisuu11()));
        // 利用回数12【変数】.概況調査リスト.利用回数12
        cpnTucCsc22.setKaisuu12(CommonDtoUtil.strValToInt(gss.getKaisuu12()));
        // 利用回数13【変数】.概況調査リスト.利用回数13
        cpnTucCsc22.setKaisuu13(CommonDtoUtil.strValToInt(gss.getKaisuu13()));
        // 改修あり・なし【変数】.概況調査リスト.改修あり・なし
        cpnTucCsc22.setKaishuUmu(CommonDtoUtil.strValToInt(gss.getKaishuUmu()));
        // 特別給付【変数】.概況調査リスト.特別給付
        cpnTucCsc22.setMemo1Knj(gss.getMemo1Knj());
        // 給付外サービス【変数】.概況調査リスト.給付外サービス
        cpnTucCsc22.setMemo2Knj(gss.getMemo2Knj());
        // サービス利用CD17【変数】.概況調査リスト.サービス利用CD17
        cpnTucCsc22.setService17Cd(CommonDtoUtil.strValToInt(gss.getService17Cd()));
        // サービス利用CD18【変数】.概況調査リスト.サービス利用CD18
        cpnTucCsc22.setService18Cd(CommonDtoUtil.strValToInt(gss.getService18Cd()));
        // サービス利用CD19【変数】.概況調査リスト.サービス利用CD19
        cpnTucCsc22.setService19Cd(CommonDtoUtil.strValToInt(gss.getService19Cd()));
        // サービス利用CD20【変数】.概況調査リスト.サービス利用CD20
        cpnTucCsc22.setService20Cd(CommonDtoUtil.strValToInt(gss.getService20Cd()));
        // サービス利用CD21【変数】.概況調査リスト.サービス利用CD21
        cpnTucCsc22.setService21Cd(CommonDtoUtil.strValToInt(gss.getService21Cd()));
        // 利用回数17【変数】.概況調査リスト.利用回数17
        cpnTucCsc22.setKaisuu17(CommonDtoUtil.strValToInt(gss.getKaisuu17()));
        // 利用回数18【変数】.概況調査リスト.利用回数18
        cpnTucCsc22.setKaisuu18(CommonDtoUtil.strValToInt(gss.getKaisuu18()));
        // 利用回数19【変数】.概況調査リスト.利用回数19
        cpnTucCsc22.setKaisuu19(CommonDtoUtil.strValToInt(gss.getKaisuu19()));
        // 利用回数20【変数】.概況調査リスト.利用回数20
        cpnTucCsc22.setKaisuu20(CommonDtoUtil.strValToInt(gss.getKaisuu20()));
        // 利用回数21【変数】.概況調査リスト.利用回数21
        cpnTucCsc22.setKaisuu21(CommonDtoUtil.strValToInt(gss.getKaisuu21()));
        // サービス利用CD22【変数】.概況調査リスト.サービス利用CD22
        cpnTucCsc22.setService22Cd(CommonDtoUtil.strValToInt(gss.getService22Cd()));
        // サービス利用CD23【変数】.概況調査リスト.サービス利用CD23
        cpnTucCsc22.setService23Cd(CommonDtoUtil.strValToInt(gss.getService23Cd()));
        // 利用回数22【変数】.概況調査リスト.利用回数22
        cpnTucCsc22.setKaisuu22(CommonDtoUtil.strValToInt(gss.getKaisuu22()));
        // 利用回数23【変数】.概況調査リスト.利用回数23
        cpnTucCsc22.setKaisuu23(CommonDtoUtil.strValToInt(gss.getKaisuu23()));
        // DAOを実行
        this.cpnTucCsc22Mapper.insertSelective(cpnTucCsc22);

    }

    /**
     * 【改訂版認定調査票（概況調査）】情報を登録する。
     * 
     * @param cschId 【変数】.調査票ID
     * @param inDto  調査票複数複写確定の入力DTO.
     * @param gss    【変数】.概況調査リスト
     */
    private void cpnTucCsc12Insert(Integer cschId, SurveySlipDuplicateMultipleUpdateServiceInDto inDto,
            Gui01268Gss gss) {

        final CpnTucCsc12 cpnTucCsc12 = new CpnTucCsc12();
        // 調査票ID【変数】.調査票ID
        cpnTucCsc12.setCschId(cschId);
        // 計画期間ID リクエストパラメータ.複写先計画期間ID
        cpnTucCsc12.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSakiSc1Id()));
        // 地域コード【変数】.概況調査リスト.地域コード
        cpnTucCsc12.setChiikiCd(CommonDtoUtil.strValToInt(gss.getChiikiCd()));
        // 調査対象者コード【変数】.概況調査リスト.調査対象者コード
        cpnTucCsc12.setTaishoushaCd(gss.getTaishoushaCd());
        // 実施場所【変数】.概況調査リスト.実施場所
        cpnTucCsc12.setWhereCd(CommonDtoUtil.strValToInt(gss.getWhereCd()));
        // 実施場所その他【変数】.概況調査リスト.実施場所その他
        cpnTucCsc12.setWhereMemoKnj(gss.getWhereMemoKnj());
        // 過去の認定【変数】.概況調査リスト.過去の認定
        cpnTucCsc12.setOldNintei(CommonDtoUtil.strValToInt(gss.getOldNintei()));
        // 前回認定日【変数】.概況調査リスト.前回認定日
        cpnTucCsc12.setOldNinteiYmd(gss.getOldNinteiYmd());
        // 前回認定結果【変数】.概況調査リスト.前回認定結果
        cpnTucCsc12.setOldNinteiDo1(CommonDtoUtil.strValToInt(gss.getOldNinteiDo1()));
        // 前回認定結果（要介護度）【変数】.概況調査リスト.前回認定結果（要介護度）
        cpnTucCsc12.setOldNinteiDo2(CommonDtoUtil.strValToInt(gss.getOldNinteiDo2()));
        // 関係者連番【変数】.概況調査リスト.関係者連番
        cpnTucCsc12.setKankeiId(CommonDtoUtil.strValToInt(gss.getKankeiId()));
        // 連絡先氏名【変数】.概況調査リスト.連絡先氏名
        cpnTucCsc12.setRenrakuNameKnj(gss.getRenrakuNameKnj());
        // 連絡先続柄【変数】.概況調査リスト.連絡先続柄
        cpnTucCsc12.setRenrakuZcode(CommonDtoUtil.strValToInt(gss.getRenrakuZcode()));
        // 連絡先〒【変数】.概況調査リスト.連絡先〒
        cpnTucCsc12.setRenrakuZip(gss.getRenrakuZip());
        // 連絡先ＴＥＬ【変数】.概況調査リスト.連絡先ＴＥＬ
        cpnTucCsc12.setRenrakuTel(gss.getRenrakuTel());
        // 県コード【変数】.概況調査リスト.県コード
        cpnTucCsc12.setKencode(CommonDtoUtil.strValToInt(gss.getKencode()));
        // 市町村コード【変数】.概況調査リスト.市町村コード
        cpnTucCsc12.setCitycode(CommonDtoUtil.strValToInt(gss.getCitycode()));
        // 地区コード【変数】.概況調査リスト.地区コード
        cpnTucCsc12.setAreacode(CommonDtoUtil.strValToInt(gss.getAreacode()));
        // 連絡先住所【変数】.概況調査リスト.連絡先住所
        cpnTucCsc12.setAddressKnj(gss.getAddressKnj());
        // 障害老人ADL【変数】.概況調査リスト.障害老人ADL
        cpnTucCsc12.setAdl1Id(CommonDtoUtil.strValToInt(gss.getAdl1Id()));
        // 痴呆老人ADL【変数】.概況調査リスト.痴呆老人ADL
        cpnTucCsc12.setAdl2Id(CommonDtoUtil.strValToInt(gss.getAdl2Id()));
        // 利用施設種別【変数】.概況調査リスト.利用施設種別
        cpnTucCsc12.setShisetsuShu(CommonDtoUtil.strValToInt(gss.getShisetsuShu()));
        // 利用施設ＩＤ【変数】.概況調査リスト.利用施設ＩＤ
        cpnTucCsc12.setShisetsuId(CommonDtoUtil.strValToInt(gss.getShisetsuId()));
        // 施設名【変数】.概況調査リスト.施設名
        cpnTucCsc12.setShisetsuNameKnj(gss.getShisetsuNameKnj());
        // 内容・特記事項【変数】.概況調査リスト.内容・特記事項
        cpnTucCsc12.setMemoKnj(gss.getMemoKnj());
        // 要介護状態見込み【変数】.概況調査リスト.要介護状態見込み
        cpnTucCsc12.setYokaiKbn1(CommonDtoUtil.strValToInt(gss.getYokaiKbn1()));
        // 要介護状態確定【変数】.概況調査リスト.要介護状態確定
        cpnTucCsc12.setYokaiKbn2(CommonDtoUtil.strValToInt(gss.getYokaiKbn2()));
        // 認定年月日【変数】.概況調査リスト.認定年月日
        cpnTucCsc12.setNinteiYmd(gss.getNinteiYmd());
        // サービス事業者ID【変数】.概況調査リスト.サービス事業者ID
        cpnTucCsc12.setSvJigyoId(CommonDtoUtil.strValToInt(gss.getSvJigyoId()));
        // 認定申請日【変数】.概況調査リスト.認定申請日
        cpnTucCsc12.setNinteiShinseiYmd(gss.getNinteiShinseiYmd());
        // 施設名【変数】.概況調査リスト.施設名
        cpnTucCsc12.setShisetuKnj(gss.getShisetuKnj());
        // 郵便番号【変数】.概況調査リスト.郵便番号
        cpnTucCsc12.setShisetsuZip(gss.getShisetsuZip());
        // 施設住所【変数】.概況調査リスト.施設住所
        cpnTucCsc12.setShisetsuAddressKnj(gss.getShisetsuAddressKnj());
        // 電話番号【変数】.概況調査リスト.電話番号
        cpnTucCsc12.setShisetsuTel(gss.getShisetsuTel());
        // 前回認定結果（要支援度）【変数】.概況調査リスト.前回認定結果（要支援度）
        cpnTucCsc12.setOldNinteiDo3(CommonDtoUtil.strValToInt(gss.getOldNinteiDo3()));
        // 家族の状況 null
        // DAOを実行
        this.cpnTucCsc12Mapper.insertSelective(cpnTucCsc12);

    }

    /**
     * 履歴の存在判断
     * 
     * @param inDto  調査票複数複写確定の入力DTO.
     * @param outDto 調査票複数複写確定のOUT DTO
     */
    private Integer rirekiJudgment(SurveySlipDuplicateMultipleUpdateServiceInDto inDto,
            SurveySlipDuplicateMultipleUpdateServiceOutDto outDto) throws Exception {
        // 3.1.リクエストパラメータ.複写先調査票ID = null or リクエストパラメータ.複写先調査票ID < 1 の場合、下記の処理を行う
        Integer sakiCschId = CommonDtoUtil.strValToInt(inDto.getSakiCschId());
        if (sakiCschId == null || sakiCschId < CommonConstants.NUMBER_1) {
            // 【変数】.新調査票改訂フラグ
            Integer newNinteiFlg;
            // 【変数】.主治医履歴id
            Integer ikenshoId;
            // 3.1.1.リクエストパラメータ.複写先改訂フラグ = 「4：H21/4改訂」の場合
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getSakiNinteiFlg())) {
                // 【変数】.新調査票改訂フラグ＝2
                newNinteiFlg = CommonConstants.NUMBER_2;
                // 【変数】.主治医履歴id＝リクエストパラメータ.医師意見書ID
                ikenshoId = CommonDtoUtil.strValToInt(inDto.getIkenshoId());
            } else {
                // 3.1.2.上記以外の場合
                // 【変数】.新調査票改訂フラグ＝0
                newNinteiFlg = CommonConstants.NUMBER_0;
                // 【変数】.主治医履歴id＝null
                ikenshoId = null;
            }
            // 3.1.3.【認定調査票（ヘッダ）】情報を登録する。
            final CpnTucCsch cpnTucCsch = new CpnTucCsch();
            // 調査票ID 自動採番
            // 計画期間ID リクエストパラメータ.複写先計画期間ID
            cpnTucCsch.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSakiSc1Id()));
            // 法人ID リクエストパラメータ.法人ID
            cpnTucCsch.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHojinId()));
            // 施設ID リクエストパラメータ.施設ID
            cpnTucCsch.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            // 事業者ID リクエストパラメータ.事業者ID
            cpnTucCsch.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 利用者ID リクエストパラメータ.利用者ID
            cpnTucCsch.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
            // 実施日 リクエストパラメータ.実施日
            cpnTucCsch.setJisshiDateYmd(inDto.getJisshiDateYmd());
            // 記入者コード リクエストパラメータ.記入者コード
            cpnTucCsch.setChkShokuId(CommonDtoUtil.strValToInt(inDto.getChkShokuId()));
            // 調査票改訂フラグ 【変数】.新調査票改訂フラグ
            cpnTucCsch.setNinteiFlg(newNinteiFlg);
            // 一次判定で使用する主治医履歴id（H21/4改訂でのみ使用） 【変数】.主治医履歴id
            cpnTucCsch.setIkenshoId(ikenshoId);
            // DAOを実行
            this.cpnTucCschMapper.insertSelective(cpnTucCsch);
            String cschId = CommonDtoUtil.objValToString(cpnTucCsch.getCschId());
            // レスポンスパラメータ.調査票ID=採番結果.調査票ID
            outDto.setGblId(cschId);
            // 【変数】.新調査票ID=採番結果.調査票ID
            return cpnTucCsch.getCschId().intValue();
        }
        return null;
    }

    /**
     * 期間の存在判断
     * 
     * @param inDto  調査票複数複写確定の入力DTO.
     * @param outDto 調査票複数複写確定のOUT DTO
     */
    private void kikanJudgment(SurveySlipDuplicateMultipleUpdateServiceInDto inDto,
            SurveySlipDuplicateMultipleUpdateServiceOutDto outDto) throws Exception {
        // 2.1.リクエストパラメータ.期間管理フラグ = 「"false":期間管理しない」 かつ、 レスポンスパラメータ.複写先計画期間ID = 0
        // (期間なし)の場合、下記の処理を行う
        if (!Boolean.parseBoolean(inDto.getKikanFlg()) && CommonConstants.STR_0.equals(inDto.getSakiSc1Id())) {
            // 2.1.1.【27-06 記録共通期間】情報を登録する。
            final KghTucKrkKikan kghTucKrkKikan = new KghTucKrkKikan();
            // 計画対象期間ID自動採番
            // 法人ID リクエストパラメータ.法人ID
            kghTucKrkKikan.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHojinId()));
            // 施設ID リクエストパラメータ.施設ID
            kghTucKrkKikan.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            // 事業者ID リクエストパラメータ.事業者ID
            kghTucKrkKikan.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 利用者ID リクエストパラメータ.利用者ID
            kghTucKrkKikan.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
            // 種別ID リクエストパラメータ.種別ID
            kghTucKrkKikan.setSyubetsuId(CommonDtoUtil.strValToInt(inDto.getSyubetsuId()));
            // 開始日 リクエストパラメータ.実施日
            kghTucKrkKikan.setStartYmd(inDto.getJisshiDateYmd());
            // 終了日null
            // DAOを実行
            this.kghTucKrkKikanMapper.insertSelective(kghTucKrkKikan);
            String sc1Id = CommonDtoUtil.objValToString(kghTucKrkKikan.getSc1Id());
            // リクエストパラメータ.複写先計画期間ID=採番結果.計画対象期間ID
            inDto.setSakiSc1Id(sc1Id);
            // レスポンスパラメータ.複写先計画期間ID=採番結果.計画対象期間ID
            outDto.setSc1Id(sc1Id);
        }
    }
}
