package jp.ndsoft.carebase.cmn.api.report.service;

import java.lang.invoke.MethodHandles;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.IncentivesItemCapDetails;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.IncentivesItemReportPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonChoPrt;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonShiTeiDateParts;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.IncentivesItemReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.IncentivesItemReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.model.IncentivesItemReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssTypeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssTypeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentSumSPBByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentSumSPBOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonUuinHeadInfByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonUuinHeadInfOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonYuinRPBByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonYuinRPBOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonYuinRPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonYuinRPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonYuinSPByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonYuinSPOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnRaiMonYuinRPBInfoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnRaiMonYuinRPInfoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucRaiCap1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucRaiRrkSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.YuinCapHeadInfoSelectMapper;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * @since 2025.06.23
 * <AUTHOR>
 * @description U06084_誘因項目帳票出力
 */
@Service("IncentivesItemReport")
public class IncentivesItemReportService extends
        PdfReportServiceImpl<IncentivesItemReportParameterModel, IncentivesItemReportServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 28-xx インターライ方式_CAP選定_結果取得情報用DAO */
    @Autowired
    private CpnTucRaiCap1SelectMapper cpnTucRaiCap1SelectMapper;

    /** インターライ方式誘因項目CAP2取得情報用DAO */
    @Autowired
    private CpnRaiMonYuinRPBInfoSelectMapper cpnRaiMonYuinRPBInfoSelectMapper;

    /** インターライ方式誘因項目CAP1情報取得用DAO */
    @Autowired
    private CpnRaiMonYuinRPInfoSelectMapper cpnRaiMonYuinRPInfoSelectMapper;

    /** アセスメント種別取得 */
    @Autowired
    private CpnTucRaiRrkSelectMapper cpnTucRaiRrkSelectMapper;

    /** インターライ方式誘因項目CAPヘッダー取得情報 */
    @Autowired
    private YuinCapHeadInfoSelectMapper yuinCapHeadInfoSelectMapper;

    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** KghCmpF01Logicロジッククラス */
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    /**
     * 帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(IncentivesItemReportParameterModel model,
            IncentivesItemReportServiceOutDto outDto) throws Exception {
        LOG.info(Constants.START);

        // 帳票用データ詳細
        IncentivesItemReportServiceInDto infoInDto = new IncentivesItemReportServiceInDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        KghCpnRaiMonKentSumSPBOutEntity monKentSumSPB = new KghCpnRaiMonKentSumSPBOutEntity();
        List<KghCpnRaiMonYuinRPBOutEntity> monYuinRPBList = new ArrayList<>();
        KghCpnRaiMonYuinSPOutEntity monYuinSP = new KghCpnRaiMonYuinSPOutEntity();
        List<KghCpnRaiMonYuinRPOutEntity> monYuinRPList = new ArrayList<>();

        // 【リクエストパラメータ】.印刷設定
        ReportCommonPrintSet printSet = model.getPrintSet();
        // 【リクエストパラメータ】.印刷オプション
        IncentivesItemReportPrintOption printOption = model.getPrintOption();
        // 【リクエストパラメータ】.印刷対象履歴リスト
        ReportCommonPrintSubjectHistory printSubjectHistory = model.getPrintSubjectHistoryList().get(0);
        // 出力帳票印刷情報
        ReportCommonChoPrt choPrt = printSubjectHistory.getChoPrtList().get(0);

        /*
         * ===============2. 誘因項目情報を取得する。======================
         * 
         */
        // 2.1. リクエストパラメータ.データ.印刷オプション.ｶﾗｰで印刷フラグ(icolor)= 1の場合、誘因項目情報を取得する。
        if (CommonConstants.I_COLOR_FLG_1.equals(printOption.getIcolor())) {
            Pair<KghCpnRaiMonKentSumSPBOutEntity, List<KghCpnRaiMonYuinRPBOutEntity>> monKentSumSPBPair = this
                    .getMonKentSumSPBPair(printSubjectHistory.getRaiId(), choPrt.getParam01(),
                            printOption.getEmptyFlg());
            monKentSumSPB = monKentSumSPBPair.getLeft();
            monYuinRPBList = monKentSumSPBPair.getRight();
        } else if (CommonConstants.I_COLOR_FLG_2.equals(printOption.getIcolor())) {
            // 2.2. リクエストパラメータ.データ.印刷オプション.ｶﾗｰで印刷フラグ(icolor)= 2の場合、誘因項目情報を取得する。
            Pair<KghCpnRaiMonYuinSPOutEntity, List<KghCpnRaiMonYuinRPOutEntity>> monYuinSPPair = this
                    .getMonYuinSPPair(printSubjectHistory.getRaiId(), choPrt.getParam01(),
                            printOption.getEmptyFlg());
            monYuinSP = monYuinSPPair.getLeft();
            monYuinRPList = monYuinSPPair.getRight();
        }

        String syubetuKnj = CommonConstants.BLANK_STRING;
        String userName = CommonConstants.BLANK_STRING;
        String selDateGG = CommonConstants.FULL_WIDTH_SPACE;
        String selDateYY = CommonConstants.FULL_WIDTH_SPACE;
        String selDateMM = CommonConstants.FULL_WIDTH_SPACE;
        String selDateDD = CommonConstants.FULL_WIDTH_SPACE;
        if (CommonConstants.EMPTY_FLG_OFF.equals(printOption.getEmptyFlg()) ||
                CommonConstants.STR_FALSE.equals(printOption.getEmptyFlg())) {
            /*
             * ===============3. アセスメント種別の取得====
             * 
             */
            // 3. アセスメント種別の取得
            syubetuKnj = this.getSyubetuKnjFromRaiRrk(printSubjectHistory.getRaiId());

            /*
             * ===============4. インターライ方式誘因項目CAPヘッダー情報取得====
             * 
             */
            // 4.1. 下記インターライ方式誘因項目CAPヘッダー情報のDAOを利用し、利用者名と選定日を取得する。
            KghCpnRaiMonUuinHeadInfByCriteriaInEntity headEntity = new KghCpnRaiMonUuinHeadInfByCriteriaInEntity();
            headEntity.setAlRaiId(CommonDtoUtil.strValToInt(printSubjectHistory.getRaiId()));
            List<KghCpnRaiMonUuinHeadInfOutEntity> headList = this.yuinCapHeadInfoSelectMapper
                    .findKghCpnRaiMonUuinHeadInfByCriteria(headEntity);
            if (CollectionUtils.isNotEmpty(headList)) {
                KghCpnRaiMonUuinHeadInfOutEntity headInfo = headList.get(0);
                userName = headInfo.getUserName();
                String selDate = kghCmpF01Logic.getCmpS2wjEz(headInfo.getCapDateYmd(), CommonConstants.NUMBER_1);
                // 選定日（年号）
                selDateGG = StringUtils.mid(selDate, 0, 2);
                int selDateYYIndex = selDate.indexOf(CommonConstants.STRING_YEAR);
                if (selDateYYIndex > 0) {
                    // 選定日（年）
                    selDateYY = StringUtils.substring(selDate, 2, selDateYYIndex);

                    int selDateMMIndex = selDate.indexOf(CommonConstants.STRING_MONTH);
                    // 選定日（月）
                    selDateMM = StringUtils.substring(selDate, selDateYYIndex + 1, selDateMMIndex);

                    int selDateDDIndex = selDate.indexOf(CommonConstants.STRING_DAY);
                    // 選定日（日）
                    selDateDD = StringUtils.substring(selDate, selDateMMIndex + 1, selDateDDIndex);
                }

            }
        }

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合
        if (CommonConstants.EMPTY_FLG_ON.equals(printOption.getEmptyFlg()) ||
                CommonConstants.STR_TRUE.equals(printOption.getEmptyFlg())) {
            infoInDto = this.getDefParams(printOption.getKinyuAssType());
        } else {
            // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
            infoInDto = this.getCapParams(printOption.getIcolor(), monKentSumSPB, monYuinSP);
            // 調査アセスメント種別名
            infoInDto.setSyubetuKnj(syubetuKnj);
            // 指定日 shiTeiDate
            ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDateGYMD(printSet.getShiTeiKubun(),
                    printSet.getShiTeiDate(), model.getSystemDate());
            // 指定日（年号）=""
            infoInDto.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
            // 指定日（年）=""
            infoInDto.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
            // 指定日（月）=""
            infoInDto.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
            // 指定日（日）=""
            infoInDto.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());
            // 利用者名
            infoInDto.setUserName(ReportUtil.nullToEmpty(userName));
            // 利用者名の桁数
            infoInDto.setUserNameKeta(ReportUtil.getByteLength(ReportUtil.nullToEmpty(userName)));
            // 選定日（年号）
            infoInDto.setSelDateGG(selDateGG);
            // 選定日（年）
            infoInDto.setSelDateYY(selDateYY);
            // 選定日（月）
            infoInDto.setSelDateMM(selDateMM);
            // 選定日（日）
            infoInDto.setSelDateDD(selDateDD);
        }

        // 帳票タイトル
        infoInDto.setTitle(ReportConstants.INCENTIVESITEM_TITLE);
        // 指定日印刷区分
        infoInDto.setShiTeiKubun(CommonDtoUtil.strValToInt(printSet.getShiTeiKubun()));
        // 事業者名
        infoInDto.setRyaku(model.getSvJigyoKnj());
        // ｶﾗｰで印刷
        infoInDto.setIcolor(CommonDtoUtil.strValToInt(printOption.getIcolor()));
        // 記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(
                CommonConstants.EMPTY_FLG_ON.equals(printOption.getEmptyFlg()) ||
                        CommonConstants.STR_TRUE.equals(printOption.getEmptyFlg()) ? Boolean.TRUE : Boolean.FALSE);

        Integer kinyuFlg = null;
        // ・リクエストパラメータ.データ.記入用シートを印刷するフラグ = true の場合、
        if (CommonDtoUtil.checkStringEqual(printOption.getEmptyFlg(), CommonConstants.EMPTY_FLG_ON) ||
                CommonConstants.STR_TRUE.equals(printOption.getEmptyFlg())) {
            // ・リクエストパラメータ.記入用シートアセスメント種別=1:居宅版の場合
            if (CommonDtoUtil.checkStringEqual(printOption.getKinyuAssType(),
                    Integer.toString(ReportConstants.ASSESSMENT_TYPE_HOMEEDITION))) {
                kinyuFlg = CommonConstants.NUMBER_1;

            }
            // ・リクエストパラメータ.記入用シートアセスメント種別=2:施設版の場合
            else if (CommonDtoUtil.checkStringEqual(printOption.getKinyuAssType(),
                    Integer.toString(ReportConstants.ASSESSMENT_TYPE_FACILITYEDITION))) {
                kinyuFlg = CommonConstants.NUMBER_2;

            }
            // ・リクエストパラメータ.記入用シートアセスメント種別=3:高齢者住宅版の場合
            else if (CommonDtoUtil.checkStringEqual(printOption.getKinyuAssType(),
                    Integer.toString(ReportConstants.ASSESSMENT_TYPE_SENIORHOUSINGEDITION))) {
                kinyuFlg = CommonConstants.NUMBER_3;

            }

        } else {
            kinyuFlg = CommonConstants.NUMBER_0;
        }

        // 記入フラグ
        infoInDto.setKinyuFlg(kinyuFlg);

        // CAP詳細リスト
        List<IncentivesItemCapDetails> capDetailsList = this.getCapDetailsParams(printOption.getIcolor(),
                printOption.getEmptyFlg(), monYuinRPBList, monYuinRPList);
        JRBeanCollectionDataSource capDetailsSource = new JRBeanCollectionDataSource(capDetailsList);
        infoInDto.setList(capDetailsSource);

        // ノート情報格納配列
        List<IncentivesItemReportServiceInDto> incentivesItemList = new ArrayList<>();

        incentivesItemList.add(infoInDto);

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(incentivesItemList);
        infoInDto.setDataSource(dataSource);

        LOG.info(Constants.END);
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U06084_誘因項目帳票パラメータ取得
     * 
     * @param kinyuAssType アセスメント種別
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private IncentivesItemReportServiceInDto getDefParams(String kinyuAssType) throws Exception {
        // 帳票用データ詳細
        IncentivesItemReportServiceInDto infoInDto = new IncentivesItemReportServiceInDto();
        // 調査アセスメント種別名
        infoInDto.setSyubetuKnj(this.getSyubetuKnj(CommonDtoUtil.strValToInt(kinyuAssType)));
        // 指定日（年号）
        infoInDto.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（年）
        infoInDto.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（月）
        infoInDto.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 指定日（日）
        infoInDto.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // 利用者名
        infoInDto.setUserName(CommonConstants.BLANK_STRING);
        // 利用者名の桁数
        infoInDto.setUserNameKeta(CommonConstants.NUMBER_0);
        // 選定日（年号）
        infoInDto.setSelDateGG(CommonConstants.FULL_WIDTH_SPACE);
        // 選定日（年）
        infoInDto.setSelDateYY(CommonConstants.FULL_WIDTH_SPACE);
        // 選定日（月）
        infoInDto.setSelDateMM(CommonConstants.FULL_WIDTH_SPACE);
        // 選定日（日）
        infoInDto.setSelDateDD(CommonConstants.FULL_WIDTH_SPACE);
        // BMI
        infoInDto.setScaleBmi(CommonConstants.BLANK_STRING);
        // うつ評価尺度
        infoInDto.setScaleDrs(CommonConstants.BLANK_STRING);
        // 痛み尺度
        infoInDto.setScalePs(CommonConstants.BLANK_STRING);
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false の場合、U06084_誘因項目帳票パラメータ取得
     * 
     * @param icolor        ｶﾗｰで印刷フラグ
     * @param monKentSumSPB インターライ方式_CAP選定_結果情報
     * @param monYuinSP     インターライ方式_CAP選定_結果情報
     * @return U06084_誘因項目帳票出力
     * @throws Exception 例外
     */
    private IncentivesItemReportServiceInDto getCapParams(String icolor, KghCpnRaiMonKentSumSPBOutEntity monKentSumSPB,
            KghCpnRaiMonYuinSPOutEntity monYuinSP) throws Exception {
        // 帳票用データ詳細
        IncentivesItemReportServiceInDto infoInDto = new IncentivesItemReportServiceInDto();
        // リクエストパラメータ.データ.印刷オプション.ｶﾗｰで印刷フラグ= 1の場合、
        if (CommonConstants.I_COLOR_FLG_1.equals(icolor)) {
            // BMI
            infoInDto.setScaleBmi(ReportUtil.nullToEmpty(monKentSumSPB.getScaleBmi()));
            // うつ評価尺度
            infoInDto.setScaleDrs(ReportUtil.nullToEmpty(monKentSumSPB.getScaleDrs()));
            // 痛み尺度
            infoInDto.setScalePs(ReportUtil.nullToEmpty(monKentSumSPB.getScalePs()));
            // 認知機能尺度
            infoInDto.setScaleCps(monKentSumSPB.getScaleCps());
            // 日常生活自立度尺度
            infoInDto.setScaleAdlh(monKentSumSPB.getScaleAdlh());
        } else if (CommonConstants.I_COLOR_FLG_2.equals(icolor)) {
            // リクエストパラメータ.データ.印刷オプション.ｶﾗｰで印刷フラグ= 2の場合、
            // BMI
            infoInDto.setScaleBmi(ReportUtil.nullToEmpty(monYuinSP.getScaleBmi()));
            // うつ評価尺度
            infoInDto.setScaleDrs(ReportUtil.nullToEmpty(monYuinSP.getScaleDrs()));
            // 痛み尺度
            infoInDto.setScalePs(ReportUtil.nullToEmpty(monYuinSP.getScalePs()));
            // 認知機能尺度
            infoInDto.setScaleCps(monYuinSP.getScaleCps());
            // 日常生活自立度尺度
            infoInDto.setScaleAdlh(monYuinSP.getScaleAdlh());
        }
        return infoInDto;
    }

    /**
     * 記入用シートを印刷するフラグ = false の場合、U06084_誘因項目帳票パラメータ取得
     * 
     * @param icolor         ｶﾗｰで印刷フラグ
     * @param emptyFlg       記入用シートを印刷するフラグ
     * @param monYuinRPBList インターライ方式_CAP選定_結果情報
     * @param monYuinRPList  インターライ方式_CAP選定_結果情報
     * @return U06084_誘因項目帳票出力
     * @throws Exception 例外
     */
    private List<IncentivesItemCapDetails> getCapDetailsParams(String icolor, String emptyFlg,
            List<KghCpnRaiMonYuinRPBOutEntity> monYuinRPBList, List<KghCpnRaiMonYuinRPOutEntity> monYuinRPList)
            throws Exception {
        List<IncentivesItemCapDetails> capDetailsList = new ArrayList<>();
        if (CommonConstants.I_COLOR_FLG_1.equals(icolor)) {
            if (CollectionUtils.isNotEmpty(monYuinRPBList)) {
                Map<Integer, Boolean> capCheckMap = new HashMap<>();
                monYuinRPBList.stream().forEach(entity -> {
                    if (entity.getTrigId() == 0 && entity.getTrigKbn() == 9) {
                        capCheckMap.put(entity.getCapId(), true);
                    }
                });
                for (KghCpnRaiMonYuinRPBOutEntity entity : monYuinRPBList) {
                    IncentivesItemCapDetails capDetails = new IncentivesItemCapDetails();
                    // CAPID
                    capDetails.setCapId(entity.getCapId());
                    // CAP名
                    capDetails.setCapKnj(ReportUtil.nullToEmpty(entity.getCapKnj()));
                    // CAP区分
                    capDetails.setCapKbn(entity.getCapKbn());
                    // CAP選定条件ID
                    capDetails.setTrigId(entity.getTrigId());
                    // CAP選定条件名
                    capDetails.setTrigKnj(ReportUtil.nullToEmpty(entity.getTrigKnj()));
                    // CAP選定条件式
                    capDetails.setTrigValueKnj(ReportUtil.nullToEmpty(entity.getTrigValueKnj()));
                    if (CommonConstants.EMPTY_FLG_ON.equals(emptyFlg) ||
                            CommonConstants.STR_TRUE.equals(emptyFlg)) {
                        // CAP選定条件区分
                        capDetails.setTrigKbn(CommonConstants.NUMBER_0);
                        // 記入区分
                        capDetails.setKinyuKbn(entity.getKinyuKbn());
                        // CAP選定条件区分
                        capDetails.setHdKbn(CommonConstants.NUMBER_0);
                    } else {
                        // CAP選定条件区分
                        if (capCheckMap.get(entity.getCapId()) != null &&
                                capCheckMap.get(entity.getCapId())) {
                            capDetails.setTrigKbn(CommonConstants.NUMBER_0);
                        } else {
                            capDetails.setTrigKbn(entity.getTrigKbn());
                        }
                        // 記入区分
                        capDetails.setKinyuKbn(CommonConstants.NUMBER_0);
                        // CAP選定条件区分
                        capDetails.setHdKbn(entity.getHdKbn());
                    }
                    capDetailsList.add(capDetails);
                }
            }
        } else if (CommonConstants.I_COLOR_FLG_2.equals(icolor)) {
            if (CollectionUtils.isNotEmpty(monYuinRPList)) {
                Map<Integer, Boolean> capCheckMap = new HashMap<>();
                monYuinRPList.stream().forEach(entity -> {
                    if (entity.getTrigId() == 0 && entity.getTrigKbn() == 9) {
                        capCheckMap.put(entity.getCapId(), true);
                    }
                });
                for (KghCpnRaiMonYuinRPOutEntity entity : monYuinRPList) {
                    IncentivesItemCapDetails capDetails = new IncentivesItemCapDetails();
                    // CAPID
                    capDetails.setCapId(entity.getCapId());
                    // CAP名
                    capDetails.setCapKnj(ReportUtil.nullToEmpty(entity.getCapKnj()));
                    // CAP区分
                    capDetails.setCapKbn(entity.getCapKbn());
                    // CAP選定条件ID
                    capDetails.setTrigId(entity.getTrigId());
                    // CAP選定条件名
                    capDetails.setTrigKnj(ReportUtil.nullToEmpty(entity.getTrigKnj()));
                    // CAP選定条件式
                    capDetails.setTrigValueKnj(ReportUtil.nullToEmpty(entity.getTrigValueKnj()));
                    if (CommonConstants.EMPTY_FLG_ON.equals(emptyFlg) ||
                            CommonConstants.STR_TRUE.equals(emptyFlg)) {
                        // CAP選定条件区分
                        capDetails.setTrigKbn(CommonConstants.NUMBER_0);
                        // 記入区分
                        capDetails.setKinyuKbn(entity.getKinyuKbn());
                        // CAP選定条件区分
                        capDetails.setHdKbn(CommonConstants.NUMBER_0);
                    } else {
                        // CAP選定条件区分
                        if (capCheckMap.get(entity.getCapId()) != null &&
                                capCheckMap.get(entity.getCapId())) {
                            capDetails.setTrigKbn(CommonConstants.NUMBER_0);
                        } else {
                            capDetails.setTrigKbn(entity.getTrigKbn());
                        }
                        // 記入区分
                        capDetails.setKinyuKbn(CommonConstants.NUMBER_0);
                        // CAP選定条件区分
                        capDetails.setHdKbn(entity.getHdKbn());
                    }
                    capDetailsList.add(capDetails);
                }
            }
        }
        return capDetailsList;
    }

    /**
     * ｶﾗｰで印刷フラグ(iColor)= 1の場合、誘因項目情報を取得する。
     * 
     * @param raiId    アセスメントID
     * @param param01  記入フラグ
     * @param emptyFlg 記入用シートを印刷するフラグ
     * @return 誘因項目情報
     */
    private Pair<KghCpnRaiMonKentSumSPBOutEntity, List<KghCpnRaiMonYuinRPBOutEntity>> getMonKentSumSPBPair(
            String raiId, String param01, String emptyFlg) {
        List<KghCpnRaiMonKentSumSPBOutEntity> cap1List = new ArrayList<>();
        // 2.1.1 下記のインターライ方式_CAP選定_結果情報取得のDAOを利用し、
        // インターライ方式_誘因項目のBMI、うつ評価尺度、痛み尺度、認知機能尺度と日常生活自立度尺度を取得する。
        if (CommonConstants.EMPTY_FLG_OFF.equals(emptyFlg) ||
                CommonConstants.STR_FALSE.equals(emptyFlg)) {
            KghCpnRaiMonKentSumSPBByCriteriaInEntity cap1Criteria = new KghCpnRaiMonKentSumSPBByCriteriaInEntity();
            cap1Criteria.setAnRaiId(CommonDtoUtil.strValToInt(raiId));
            cap1List = cpnTucRaiCap1SelectMapper
                    .findKghCpnRaiMonKentSumSPBByCriteria(cap1Criteria);
        }

        KghCpnRaiMonKentSumSPBOutEntity outEntity = new KghCpnRaiMonKentSumSPBOutEntity();
        if (CollectionUtils.isNotEmpty(cap1List)) {
            outEntity = cap1List.get(0);
        }

        // 2.1.2 下記のインターライ方式_CAP選定_詳細情報取得のDAOを利用し、インターライ方式_CAP選定_詳細を取得する。
        KghCpnRaiMonYuinRPBByCriteriaInEntity cap2Criteria = new KghCpnRaiMonYuinRPBByCriteriaInEntity();
        cap2Criteria.setAnKinyuFlg(CommonDtoUtil.strValToInt(param01));
        cap2Criteria.setAnRaiId(CommonConstants.EMPTY_FLG_ON.equals(emptyFlg) ||
                CommonConstants.STR_TRUE.equals(emptyFlg) ? CommonConstants.INT_0
                        : CommonDtoUtil.strValToInt(raiId));
        List<KghCpnRaiMonYuinRPBOutEntity> cap2List = cpnRaiMonYuinRPBInfoSelectMapper
                .findKghCpnRaiMonYuinRPBByCriteria(cap2Criteria);

        return Pair.of(outEntity, cap2List);
    }

    /**
     * ｶﾗｰで印刷フラグ(iColor)= 2の場合、誘因項目情報を取得する。
     * 
     * @param raiId    アセスメントID
     * @param param01  記入フラグ
     * @param emptyFlg 記入用シートを印刷するフラグ
     * @return 誘因項目情報
     */
    private Pair<KghCpnRaiMonYuinSPOutEntity, List<KghCpnRaiMonYuinRPOutEntity>> getMonYuinSPPair(
            String raiId, String param01, String emptyFlg) {
        List<KghCpnRaiMonYuinSPOutEntity> cap1List = new ArrayList<>();
        // 2.2.1 下記のインターライ方式_CAP選定_結果情報取得のDAOを利用し、
        // インターライ方式_誘因項目のBMI、うつ評価尺度、痛み尺度、認知機能尺度と日常生活自立度尺度を取得する。
        if (CommonConstants.EMPTY_FLG_OFF.equals(emptyFlg) ||
                CommonConstants.STR_FALSE.equals(emptyFlg)) {
            KghCpnRaiMonYuinSPByCriteriaInEntity cap1Criteria = new KghCpnRaiMonYuinSPByCriteriaInEntity();
            cap1Criteria.setAnRaiId(CommonDtoUtil.strValToInt(raiId));
            cap1List = cpnTucRaiCap1SelectMapper
                    .findKghCpnRaiMonYuinSPByCriteria(cap1Criteria);
        }
        KghCpnRaiMonYuinSPOutEntity outEntity = new KghCpnRaiMonYuinSPOutEntity();
        if (CollectionUtils.isNotEmpty(cap1List)) {
            outEntity = cap1List.get(0);
        } else {
            outEntity = null;
        }

        // 2.2.2 下記のインターライ方式_CAP選定_詳細情報取得のDAOを利用し、インターライ方式_CAP選定_詳細を取得する。
        KghCpnRaiMonYuinRPByCriteriaInEntity cap2Criteria = new KghCpnRaiMonYuinRPByCriteriaInEntity();
        cap2Criteria.setAnKinyuFlg(CommonDtoUtil.strValToInt(param01));
        cap2Criteria.setAnRaiId(CommonConstants.EMPTY_FLG_ON.equals(emptyFlg) ||
                CommonConstants.STR_TRUE.equals(emptyFlg) ? CommonConstants.INT_0
                        : CommonDtoUtil.strValToInt(raiId));
        List<KghCpnRaiMonYuinRPOutEntity> cap2List = cpnRaiMonYuinRPInfoSelectMapper
                .findKghCpnRaiMonYuinRPByCriteria(cap2Criteria);

        return Pair.of(outEntity, cap2List);
    }

    /**
     * アセスメント種別を取得する。
     * 
     * @param raiId アセスメントID
     * @return アセスメント種別
     * @throws Exception
     */
    private String getSyubetuKnjFromRaiRrk(String raiId) throws Exception {
        String syubetuKnj = CommonConstants.BLANK_STRING;
        AssTypeByCriteriaInEntity assTypeByCriteriaInEntity = new AssTypeByCriteriaInEntity();
        assTypeByCriteriaInEntity.setIlRirekiId(CommonDtoUtil.strValToInt(raiId));
        List<AssTypeOutEntity> assTypeOutList = cpnTucRaiRrkSelectMapper
                .findAssTypeByCriteria(assTypeByCriteriaInEntity);
        Integer assType = CommonConstants.NUMBER_0;
        if (CollectionUtils.isNotEmpty(assTypeOutList)) {
            assType = assTypeOutList.get(0).getAssType();
        }
        syubetuKnj = this.getSyubetuKnj(assType);

        return syubetuKnj;
    }

    /**
     * アセスメント種別名を取得する
     * 
     * @param kinyuAssType アセスメント種別
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private String getSyubetuKnj(Integer kinyuAssType) throws Exception {
        String syubetuKnj = CommonConstants.BLANK_STRING;
        switch (kinyuAssType) {
            case ReportConstants.ASSESSMENT_TYPE_HOMEEDITION:
                // アセスメント種別 = 1 の場合
                syubetuKnj = ReportConstants.STR_HOMEEDITION;
                break;
            case ReportConstants.ASSESSMENT_TYPE_FACILITYEDITION:
                // アセスメント種別 = 2 の場合
                syubetuKnj = ReportConstants.STR_FACILITYEDITION;
                break;
            case ReportConstants.ASSESSMENT_TYPE_SENIORHOUSINGEDITION:
                // アセスメント種別 = 3 の場合
                syubetuKnj = ReportConstants.STR_SENIORHOUSINGEDITION;
                break;
            default:
                // 上記以外の場合、
                syubetuKnj = CommonConstants.BLANK_STRING;
                break;
        }

        return syubetuKnj;
    }

    /**
     * 指定日を取得する
     * 
     * @param shiTeiKubun 指定日印刷区分
     * @param shiTeiDate  西暦日付
     * @param systemDate  システム日付
     * @throws Exception 例外
     */
    private ReportCommonShiTeiDateParts getShiTeiDateGYMD(String shiTeiKubun, String shiTeiDate, String systemDate) {
        // 指定日（年号）=""
        String shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（年）=""
        String shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（月）=""
        String shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（日）=""
        String shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日印刷区分判定
        switch (shiTeiKubun) {
            case ReportConstants.PRINTDATE_KBN_SHINAI:
                // 指定日印刷区分 = 1 の場合
                // 指定日（年号）=""
                shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（年）=""
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）=""
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）=""
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            case ReportConstants.PRINTDATE_KBN_PRINT:
                // 指定日印刷区分 = 2 の場合
                // 西暦日付を和暦日付に変換する
                List<String> dateParts = ReportUtil.getLocalDateToJapanDateTimeFormat(shiTeiDate);
                shiTeiDateGG = dateParts.get(ReportConstants.SHITEIDATE_GG);
                // 指定日（年）=""
                shiTeiDateYY = dateParts.get(ReportConstants.SHITEIDATE_YY);
                // 指定日（月）=""
                shiTeiDateMM = dateParts.get(ReportConstants.SHITEIDATE_MM);
                // 指定日（日）=""
                shiTeiDateDD = dateParts.get(ReportConstants.SHITEIDATE_DD);
                break;
            case ReportConstants.PRINTDATE_KBN_BLANKDATE:
                // 指定日印刷区分 = 3 の場合
                // 指定日の空欄表示処理
                DateTimeFormatter inputFormatter = DateTimeFormatter
                        .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
                DateTimeFormatter outputFormatter = DateTimeFormatter
                        .ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
                LocalDateTime dateTime = LocalDateTime.parse(systemDate, inputFormatter);
                String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
                // 指定日（年号）=""
                shiTeiDateGG = blankDate.substring(0, 2);
                // 指定日（年）=""
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）=""
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）=""
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            default:
                // 指定日（年号）=""
                shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（年）=""
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）=""
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）=""
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
        }
        return new ReportCommonShiTeiDateParts(shiTeiDateGG, shiTeiDateYY, shiTeiDateMM, shiTeiDateDD);
    }

    /**
     * 帳票出力
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @throws Exce
     *              ption 例外
     */
    @Override
    protected void outputReport(final IncentivesItemReportParameterModel model,
            final IncentivesItemReportServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final IncentivesItemReportServiceInDto reportParameter = (IncentivesItemReportServiceInDto) getReportParameters(
                model, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_INCENTIVES_ITEM)));

        // コンパイル
        final JasperReport jasperFile = JasperCompileManager.compileReport(is);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile, reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(model, jasperPrint);

        /*
         * =============== 6.レスポンスを返却===============
         * 
         */
        super.setFilepath(model, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }
}
