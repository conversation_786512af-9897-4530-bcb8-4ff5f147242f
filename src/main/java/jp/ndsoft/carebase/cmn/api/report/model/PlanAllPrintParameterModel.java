package jp.ndsoft.carebase.cmn.api.report.model;

import java.util.List;

import jp.ndsoft.carebase.cmn.api.report.dto.DailyRoutinePlanReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.KyotakuServiceReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.Plan1kReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.Plan1sReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ServiceUseAnnexedTableReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ShisetsuServiceReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ShuukanServiceR34ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.TeikyohyoReportServiceInDto;
import jp.ndsoft.smh.framework.global.report.dto.FixedReportInDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * U00891_計画書一括印刷
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PlanAllPrintParameterModel extends FixedReportInDto {
    /** UID. */
    private static final long serialVersionUID = 1L;

    /** U0081K_居宅サービス計画書（１） */
    private Plan1kReportServiceInDto data1;

    /** U0081K_居宅サービス計画書（１） */
    private Plan1kReportParameterModel parameterModel1;

    /** U0081S_施設サービス計画書（１） */
    private Plan1sReportServiceInDto data2;

    /** U0082S_施設サービス計画書（２） */
    private ShisetsuServiceReportServiceInDto data3;

    /** U0082K_居宅サービス計画書（２） */
    private KyotakuServiceReportServiceInDto data4;

    /** U00852_週間サービス計画表（R34改訂） */
    private ShuukanServiceR34ReportServiceInDto data5;

    /** U00861_日課計画表出力 */
    private DailyRoutinePlanReportServiceInDto data6;

    /** V00241_サービス提供票 */
    private TeikyohyoReportServiceInDto data7;

    /** V00231_サービス利用票別表 */
    private List<ServiceUseAnnexedTableReportServiceInDto> data8;

}
