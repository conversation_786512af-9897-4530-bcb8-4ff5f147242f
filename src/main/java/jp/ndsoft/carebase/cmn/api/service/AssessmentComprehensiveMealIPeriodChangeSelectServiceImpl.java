package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.*;
import java.util.stream.IntStream;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00834HistoryInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00834PlanPeriodInfo;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZOther01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZSnc01Logic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentComprehensiveMealIPeriodChangeSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentComprehensiveMealIPeriodChangeSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.*;
import jp.ndsoft.carebase.common.dao.mysql.mapper.*;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import jp.ndsoft.smh.framework.global.security.authentication.AuthenticationInfo;
import jp.ndsoft.smh.framework.util.AppUtil;
import jp.ndsoft.smh.framework.util.DateUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @since 2025.05.13
 * <AUTHOR>
 * @implNote GUI00834_［アセスメント（包括）］画面 計画期間変更
 */
@Service
public class AssessmentComprehensiveMealIPeriodChangeSelectServiceImpl extends
        SelectServiceImpl<AssessmentComprehensiveMealIPeriodChangeSelectServiceInDto, AssessmentComprehensiveMealIPeriodChangeSelectServiceOutDto> {

    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 計画期間情報 */
    @Autowired
    private KghTucKrkKikanSelectMapper kghTucKrkKikanSelectMapper;

    /** 履歴情報 */
    @Autowired
    private CpnTucHcc1SelectMapper cpnTucHcc1SelectMapper;

    /** 計画期間情報List */
    List<KghTucKrkKikanOutEntity> kghTucKrkKikanOutEntityList;

    /** 期間管理フラグ */
    boolean kikanFlg;

    private AssessmentComprehensiveMealIPeriodChangeSelectServiceOutDto outDto;

    /** 共通部品「f_kgh_krk_get_kikan」 */
    @Autowired
    private KghKrkZSnc01Logic kghKrkZSnc01Logic;

    @Autowired
    private KghKrkZOther01Logic kghKrkZOther01Logic;

    /*
     * ===============1.単項目チェック以外の入力チェック===============
     * 
     */
    @Override
    protected void checkProcess(final AssessmentComprehensiveMealIPeriodChangeSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // 1.1.下記の共通関数「期間管理するかしないかを判断する」を利用し、種別IDを取得する。
        outDto = new AssessmentComprehensiveMealIPeriodChangeSelectServiceOutDto();
        Integer syubetuId = kghKrkZOther01Logic.getKghKrkSyubetu(inDto.getMenu2Name(), inDto.getMenu3Name(), "");
        outDto.setSyubetsuId(CommonDtoUtil.objValToString(syubetuId));
        // 1.2.下記の共通関数「期間管理するかしないかを判断する」を利用し、期間管理フラグを取得する。
        kikanFlg = kghKrkZSnc01Logic.getKghKrkKikan(syubetuId,
                CommonDtoUtil.strValToInt(inDto.getSvJigyoId()),
                CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        // 1.3.下記の記録共通期間取得のDAOを利用し、計画期間情報リストを取得する。

        kghTucKrkKikanOutEntityList = kghTucKrkKikanSelectMapper
                .findKghTucKrkKikanByCriteria(new KghTucKrkKikanByCriteriaInEntity() {
                    {
                        setJId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                        setUId(CommonDtoUtil.strValToInt(inDto.getUserId()));
                        setSId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                        setSyubetsuId(syubetuId);
                    }
                });

        // 1.3.1. 上記で取得した計画期間情報リストの件数を計画期間情報.期間総件数に設定する。

        // 1.3.2. 上記で取得した計画期間情報リストにレコードごとの逆の順番を計画期間情報.期間番号に設定する。

        // 1.4 上記処理に計画対象期間情報が取得できる場合
        if (kghTucKrkKikanOutEntityList != null && kghTucKrkKikanOutEntityList.size() > 0) {
            // リクエストの期間IDがlist内のindexを取る
            int index1 = IntStream.range(0, kghTucKrkKikanOutEntityList.size())
                    .filter(i -> inDto.getSc1Id()
                            .equals(CommonDtoUtil.objValToString(
                                    kghTucKrkKikanOutEntityList.get(i).getSc1Id())))
                    .findFirst()
                    .orElse(CommonConstants.INT_MINUS_1);

            // 1.1.1. リクエストパラメータ.計画期間ページ区分 ＝ "1"の場合（前ページをクリックする）
            if (CommonConstants.PLAN_PERIOD_PAGE_TYPE_1.equals(inDto.getPageFlag())) {
                // 1.1.1.1.計画対象期間IDが上記計画対象期間情報検索結果に存在して、且つ、計画対象期間ID前の期間IDが存在しない場合
                if (index1 == CommonConstants.INT_MINUS_1
                        || index1 == kghTucKrkKikanOutEntityList.size() - CommonConstants.INT_1) {
                    outDto.setErrKbn(CommonConstants.PLAN_PERIOD_PAGE_TYPE_1);
                }
            }
            // 1.1.2. リクエストパラメータ.計画期間ページ区分 ＝ "2"の場合（後ページをクリックする）
            if (CommonConstants.PLAN_PERIOD_PAGE_TYPE_2.equals(inDto.getPageFlag())) {

                // 1.1.1.1.計画対象期間IDが上記計画対象期間情報検索結果に存在して、且つ、計画対象期間ID前の期間IDが存在しない場合
                if (index1 == CommonConstants.INT_MINUS_1 || index1 == CommonConstants.INT_0) {
                    outDto.setErrKbn(CommonConstants.PLAN_PERIOD_PAGE_TYPE_2);
                }
            }
        }

    }

    /**
     * ［アセスメント（包括）］画面 計画期間変更を取得する
     * 
     * @param inDto ［アセスメント（包括）］画面 計画期間変更 入力DTO.
     * @return ［アセスメント（包括）］画面 計画期間変更 出力DTO
     */
    @Override
    protected AssessmentComprehensiveMealIPeriodChangeSelectServiceOutDto mainProcess(
            final AssessmentComprehensiveMealIPeriodChangeSelectServiceInDto inDto) throws Exception {

        LOG.info(Constants.START);

        // ===============［アセスメント（包括）］画面 計画期間変更 START===============//
        /*
         * ===============2.期間管理フラグの取得を行う。===============
         * 
         */
        /*
         * ===============2. 計画期間情報リスト取得===============
         * 
         */
        // 計画期間情報を作成する
        Gui00834PlanPeriodInfo planPeriodInfo = new Gui00834PlanPeriodInfo();
        // 履歴情報を作成する
        Gui00834HistoryInfo historyInfo = new Gui00834HistoryInfo();
        // 2.2.「2.1.」で取得した計画期間情報リストの件数 ＝ 0 件の場合、
        if (CollectionUtils.isNullOrEmpty(kghTucKrkKikanOutEntityList)) {
            // ①【変数】.計画期間情報：
            // 期間ID = 0
            planPeriodInfo.setSc1Id(CommonConstants.STR_0);
            // 期間番号＝0
            planPeriodInfo.setPeriodNo(CommonConstants.STR_0);
            // 開始日＝NULL
            planPeriodInfo.setStartYmd(null);
            // 終了日＝NULL
            planPeriodInfo.setEndYmd(null);
            // 期間総件数＝0
            planPeriodInfo.setPeriodCnt(CommonConstants.STR_0);
            // ②【変数】.履歴情報：
            // ケアチェックID＝0
            historyInfo.setCc1Id(CommonConstants.STR_0);
            // 作成日＝共通情報.作成日
            historyInfo.setCreateYmd(DateUtil.formatDateToString(AppUtil.getSystemTimeStamp(), null));
            // 職員ID ＝ログイン情報.職員名
            historyInfo.setShokuId(AuthenticationInfo.getAccount().getLoginUserId());
            // 履歴番号＝1
            historyInfo.setKrirekiNo(CommonConstants.STR_1);
            // 履歴総件数＝1
            historyInfo.setKrirekiCnt(CommonConstants.STR_1);

            // 2.3.「2.1.」で取得した計画期間情報リストの件数 > 0 件の場合、
        } else {
            // 【変数】.抽出条件.期間ID
            Integer riSc1Id = CommonConstants.NUMBER_0;
            // 2.3.1.リクエストパラメータ.期間管理フラグ=「管理しない」の場合、
            if (!kikanFlg) {
                // 【変数】.抽出条件.期間ID＝0
                riSc1Id = CommonConstants.NUMBER_0;
                // 【変数】.計画期間情報：
                // 期間ID = 計画期間情報リストの先頭１件目計画期間情報の期間ID
                planPeriodInfo.setSc1Id(String.valueOf(
                        kghTucKrkKikanOutEntityList.get(CommonConstants.NUMBER_ZERO)
                                .getSc1Id()));
                // 期間番号＝計画期間情報リストの先頭１件目レコードの期間番号
                planPeriodInfo.setPeriodNo(String.valueOf(kghTucKrkKikanOutEntityList.size()));
                // 開始日＝計画期間情報リストの先頭１件目計画期間情報の開始日
                planPeriodInfo.setStartYmd(
                        kghTucKrkKikanOutEntityList.get(CommonConstants.NUMBER_ZERO)
                                .getStartYmd());
                // 終了日＝計画期間情報リストの先頭１件目計画期間情報の終了日
                planPeriodInfo.setEndYmd(
                        kghTucKrkKikanOutEntityList.get(CommonConstants.NUMBER_ZERO)
                                .getEndYmd());
                // 期間総件数＝上記「2.1」取得した計画期間情報リストの件数
                planPeriodInfo.setPeriodCnt(String.valueOf(kghTucKrkKikanOutEntityList.size()));

                // 2.3.2.リクエストパラメータ.期間管理フラグ=「管理する」の場合、
            } else {
                // 2.3.2.1.リクエストパラメータ. 期間ID = 0の場合、
                if (CommonConstants.STR_0.equals(inDto.getSc1Id())) {
                    // 【変数】.計画期間情報：
                    // 期間ID = 計画期間情報リストの先頭１件目計画期間情報の期間ID
                    planPeriodInfo.setSc1Id(String.valueOf(
                            kghTucKrkKikanOutEntityList.get(CommonConstants.NUMBER_ZERO)
                                    .getSc1Id()));
                    // 期間番号＝計画期間情報リストの先頭１件目レコードの期間番号
                    planPeriodInfo.setPeriodNo(String.valueOf(kghTucKrkKikanOutEntityList.size()));
                    // 開始日＝計画期間情報リストの先頭１件目計画期間情報の開始日
                    planPeriodInfo.setStartYmd(
                            kghTucKrkKikanOutEntityList.get(CommonConstants.NUMBER_ZERO)
                                    .getStartYmd());
                    // 終了日＝計画期間情報リストの先頭１件目計画期間情報の終了日
                    planPeriodInfo.setEndYmd(
                            kghTucKrkKikanOutEntityList.get(CommonConstants.NUMBER_ZERO)
                                    .getEndYmd());
                    // 期間総件数＝上記「2.1」取得した計画期間情報リストの件数
                    planPeriodInfo.setPeriodCnt(String.valueOf(kghTucKrkKikanOutEntityList.size()));

                    // 2.3.2.2.リクエストパラメータ. 期間ID <> 0の場合、
                } else {
                    OptionalInt optionalIntIIndex = IntStream
                            .range(0, kghTucKrkKikanOutEntityList.size())
                            .filter(i -> kghTucKrkKikanOutEntityList.get(i).getSc1Id()
                                    .equals(Integer.parseInt(inDto.getSc1Id())))
                            .findFirst();

                    // 2.3.2.2.1.リクエストパラメータ. 期間IDが「2.1.」で取得した計画期間情報リストにない場合、
                    if (!optionalIntIIndex.isPresent()) {
                        // 【変数】.計画期間情報：
                        // 期間ID = 計画期間情報リストの先頭１件目計画期間情報の期間ID
                        planPeriodInfo.setSc1Id(String.valueOf(
                                kghTucKrkKikanOutEntityList
                                        .get(CommonConstants.NUMBER_ZERO)
                                        .getSc1Id()));
                        // 期間番号＝計画期間情報リストの先頭１件目レコードの期間番号
                        planPeriodInfo.setPeriodNo(
                                String.valueOf(kghTucKrkKikanOutEntityList.size()));
                        // 開始日＝計画期間情報リストの先頭１件目計画期間情報の開始日
                        planPeriodInfo.setStartYmd(kghTucKrkKikanOutEntityList
                                .get(CommonConstants.NUMBER_ZERO).getStartYmd());
                        // 終了日＝計画期間情報リストの先頭１件目計画期間情報の終了日
                        planPeriodInfo.setEndYmd(kghTucKrkKikanOutEntityList
                                .get(CommonConstants.NUMBER_ZERO).getEndYmd());
                        // 期間総件数＝上記「2.1」取得した計画期間情報リストの件数
                        planPeriodInfo.setPeriodCnt(
                                String.valueOf(kghTucKrkKikanOutEntityList.size()));

                        // 2.3.2.2.2.リクエストパラメータ. 期間IDが「2.1.」で取得した計画期間情報リストにある場合、
                    } else {

                        int index = optionalIntIIndex.getAsInt();
                        // 後レコードのインデックス
                        int previousIndex = 0;
                        // 前レコードのインデックス
                        int nextIndex = kghTucKrkKikanOutEntityList.size() - 1;
                        if (index > 0) {
                            previousIndex = index - 1;
                        }

                        if (index < kghTucKrkKikanOutEntityList.size() - 1) {
                            nextIndex = index + 1;
                        }

                        // ①【変数】.期間ID = リクエストパラメータ. 期間ID
                        String sc1Id = inDto.getSc1Id();

                        // ②リクエストパラメータ.計画期間ページ区分 ＝ 1 の場合（前ページをクリックする）
                        if (CommonConstants.PLANNING_PERIOD_PAGE_CATEGORY_1
                                .equals(inDto.getPageFlag())) {
                            // 【変数】.期間ID =「2.1.」の計画期間情報リストからリクエストパラメータ.期間ID前の期間ID
                            sc1Id = String.valueOf(
                                    kghTucKrkKikanOutEntityList.get(nextIndex)
                                            .getSc1Id());
                            index = nextIndex;
                            // ③リクエストパラメータ.計画期間ページ区分 ＝ 2 の場合（後ページをクリックする）
                        } else if (CommonConstants.PLANNING_PERIOD_PAGE_CATEGORY_2
                                .equals(inDto.getPageFlag())) {
                            // 【変数】.期間ID =「2.1.」の計画期間情報リストからリクエストパラメータ.期間ID後の期間ID
                            sc1Id = String.valueOf(kghTucKrkKikanOutEntityList
                                    .get(previousIndex).getSc1Id());

                            index = previousIndex;
                        }
                        // ④【変数】.計画期間情報の設定
                        // 【変数】.計画期間情報：
                        // 期間ID =【変数】.期間ID
                        planPeriodInfo.setSc1Id(sc1Id);

                        // 期間番号＝「2.1.」の計画期間情報リストに【変数】.期間IDのレコードの期間番号
                        planPeriodInfo.setPeriodNo(
                                String.valueOf(kghTucKrkKikanOutEntityList.size()
                                        - index));
                        // 開始日＝「2.1.」の計画期間情報リストに【変数】.期間IDのレコードの開始日
                        planPeriodInfo.setStartYmd(kghTucKrkKikanOutEntityList
                                .get(index).getStartYmd());
                        // 終了日＝「2.1.」の計画期間情報リストに【変数】.期間IDのレコードの終了日
                        planPeriodInfo.setEndYmd(kghTucKrkKikanOutEntityList
                                .get(index).getEndYmd());
                        // 期間総件数＝上記「2.1」取得した計画期間情報リストの件数
                        planPeriodInfo.setPeriodCnt(
                                String.valueOf(kghTucKrkKikanOutEntityList.size()));
                    }
                }
                // 2.2.2.3.【変数】.抽出条件.期間ID＝【変数】.計画期間情報.期間ID
                riSc1Id = Integer.parseInt(planPeriodInfo.getSc1Id());
            }

            // 2.3.2.1.下記のＨＫ＿ケアチェック表（ヘッダ）情報取得のDAOを利用し、包括ケアチェック履歴情報リストを取得する。
            // DAOパラメータを作成
            CpnTucHcc1InfoByCriteriaInEntity cpnTucHcc1InfoByCriteriaInEntity = new CpnTucHcc1InfoByCriteriaInEntity();
            // 計画期間ID
            cpnTucHcc1InfoByCriteriaInEntity.setSc1Id(riSc1Id);
            // 事業者ID
            cpnTucHcc1InfoByCriteriaInEntity.setJId(Integer.parseInt(inDto.getSvJigyoId()));
            // 利用者ID
            cpnTucHcc1InfoByCriteriaInEntity.setUId(Integer.parseInt(inDto.getUserId()));
            // DAOを実行
            List<CpnTucHcc1InfoOutEntity> cpnTucHcc1InfoOutEntityList = this.cpnTucHcc1SelectMapper
                    .findCpnTucHcc1InfoByCriteria(cpnTucHcc1InfoByCriteriaInEntity);

            // ①上記で取得した包括ケアチェック履歴情報リストの件数 ＝ 0 件の場合、
            if (CollectionUtils.isNullOrEmpty(cpnTucHcc1InfoOutEntityList)) {
                // 【変数】.履歴情報：
                // ケアチェックID＝0
                historyInfo.setCc1Id(CommonConstants.STR_0);
                // 作成日＝共通情報.作成日
                historyInfo.setCreateYmd(
                        DateUtil.formatDateToString(AppUtil.getSystemTimeStamp(), null));
                // 職員ID ＝ログイン情報.職員名
                historyInfo.setShokuId(AuthenticationInfo.getAccount().getLoginUserId());
                // 履歴番号＝1
                historyInfo.setKrirekiNo(CommonConstants.STR_1);
                // 履歴総件数＝1
                historyInfo.setKrirekiCnt(CommonConstants.STR_1);

                // ②上記で取得した包括ケアチェック履歴情報リストの件数 > 0 件の場合、
            } else {
                // 【変数】.履歴情報=包括ケアチェック履歴情報リストの先頭１件目を設定
                // ②【変数】.履歴情報：
                // ケアチェックID＝包括ケアチェック履歴情報リストの先頭１件目のケアチェックID
                historyInfo.setCc1Id(String
                        .valueOf(cpnTucHcc1InfoOutEntityList.get(CommonConstants.NUMBER_ZERO)
                                .getCc1Id()));
                // 作成日＝包括ケアチェック履歴情報リストの先頭１件目の作成日
                historyInfo.setCreateYmd(
                        cpnTucHcc1InfoOutEntityList.get(CommonConstants.NUMBER_ZERO)
                                .getCreateYmd());
                // 職員ID ＝包括ケアチェック履歴情報リストの先頭１件目の作成者
                historyInfo.setShokuId(String
                        .valueOf(cpnTucHcc1InfoOutEntityList.get(CommonConstants.NUMBER_ZERO)
                                .getShokuId()));
                // 履歴番号＝包括ケアチェック履歴情報リストの先頭１件目の履歴番号
                historyInfo.setKrirekiNo(String.valueOf(cpnTucHcc1InfoOutEntityList.size()));
                // 履歴総件数＝包括ケアチェック履歴情報リストの件数
                historyInfo.setKrirekiCnt(String
                        .valueOf(cpnTucHcc1InfoOutEntityList.size()));
            }

            // 計画期間ID
            historyInfo.setSc1Id(planPeriodInfo.getSc1Id());
        }

        // ===============［アセスメント（包括）］画面の計画期間と履歴期間の最新情報取得 END===============//

        // 計画期間情報出力
        outDto.setPlanPeriodInfo(planPeriodInfo);
        // 履歴情報出力
        outDto.setHistoryInfo(historyInfo);
        // 期間管理フラグ
        outDto.setKikanKanriFlg(kikanFlg ? CommonConstants.PERIOD_MANAGE_FLG : CommonConstants.PERIOD_NO_MANAGE_FLG);

        LOG.info(Constants.END);

        return outDto;
    }

}
