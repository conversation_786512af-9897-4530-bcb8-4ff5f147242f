package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.05.12
 * <AUTHOR>
 * @description GUI00904_課題立案様式設定マスタ参照DTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Gui00904kghMocKrkFree12OutDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    // 行の分割数（上）
    @NotEmpty
    private String columnCount;
    // 印刷文字サイズ
    @NotEmpty
    private String fontSize;
}
