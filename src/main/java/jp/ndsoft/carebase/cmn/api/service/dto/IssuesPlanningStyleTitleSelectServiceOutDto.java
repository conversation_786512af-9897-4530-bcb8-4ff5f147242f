package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jakarta.validation.Valid;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00905IssuesPlanningStyleTitleMasterSelectData;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.05.13
 * <AUTHOR> 盧青陽
 * @implNote GUI00905_課題立案様式タイトルマスタ画面の出力DTO
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class IssuesPlanningStyleTitleSelectServiceOutDto extends IDtoImpl {

    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    // 課題立案様式タイトルマスタリスト
    @Valid
    private List<Gui00905IssuesPlanningStyleTitleMasterSelectData> issuesPlanningStyleTitleMasterList;
}