package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * @since 2025.08.11
 * <AUTHOR>
 * @implNote U01020_モニタリング記録表一覧 モニタリング記録表一覧詳細情報
 */
@Data
@EqualsAndHashCode(callSuper = false)
// 帳票データ
public class ReportMoniitiranData extends IDtoImpl {
    private static final long serialVersionUID = 1L;

    /**
     * 課題番号
     */
    private String kaidaiNo;

    /**
     * 番号
     */
    private String servNo;

    /**
     * 実行確認・CD
     */
    private String kakuninCd;

    /**
     * 実行確認・内容
     */
    private String kakuninKnj;

    /**
     * 確認方法・CD
     */
    private String houhouCd;

    /**
     * 確認方法・内容
     */
    private String houhouKnj;

    /**
     * 確認期日
     */
    private String kakuninYmdGG;
    private String kakuninYmdYY;
    private String kakuninYmdMM;
    private String kakuninYmdDD;

    /**
     * 本人の意見ＣＤ
     */
    private String ikenHonCd;

    /**
     * 本人の意見
     */
    private String ikenHonKnj;

    /**
     * 家族の意見ＣＤ
     */
    private String ikenKazCd;

    /**
     * 家族の意見
     */
    private String ikenKazKnj;

    /**
     * ニーズ充足度ＣＤ
     */
    private String jusokuCd;

    /**
     * ニーズ充足度
     */
    private String jusokuKnj;

    /**
     * 対応ＣＤ
     */
    private String taiouCd;

    /**
     * 対応
     */
    private String taiouKnj;
}
