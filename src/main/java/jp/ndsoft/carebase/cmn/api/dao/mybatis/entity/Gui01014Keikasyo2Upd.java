package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * GUI01014_計画書（２）
 * 
 * @description
 *              計画書（２）情報保存
 *              計画書（２）リスト
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class Gui01014Keikasyo2Upd extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 計画書（２）更新区分 */
    @NotEmpty
    private String updateKbn;
    /** カウンター */
    @NotEmpty
    private String ks22Id;
    /** 計画書（２）ID */
    @NotEmpty
    private String ks21Id;
    /** 具体的 */
    @NotEmpty
    private String gutaitekiKnj;
    /** 長期 */
    @NotEmpty
    private String choukiKnj;
    /** 短期 */
    @NotEmpty
    private String tankiKnj;
    /** 介護 */
    @NotEmpty
    private String kaigoKnj;
    /** サービス種 */
    @NotEmpty
    private String svShuKnj;
    /** 頻度 */
    @NotEmpty
    private String hindoKnj;
    /** 期間 */
    @NotEmpty
    private String kikanKnj;
    /** 通番 */
    @NotEmpty
    private String seq;
    /** 課題番号 */
    @NotEmpty
    private String kadaiNo;
    /** 介護番号 */
    @NotEmpty
    private String kaigoNo;
    /** 長期期間 */
    @NotEmpty
    private String choKikanKnj;
    /** 短期期間 */
    @NotEmpty
    private String tanKikanKnj;
    /** 給付対象 */
    @NotEmpty
    private String hkyuKbn;
    /** サービス事業者ＣＤ */
    @NotEmpty
    private String jigyouId;
    /** サービス事業者名 */
    @NotEmpty
    private String jigyoNameKnj;
    /** 給付文字 */
    @NotEmpty
    private String hkyuKnj;
    /** 長期期間開始日 */
    @NotEmpty
    private String choSYmd;
    /** 長期期間終了日 */
    @NotEmpty
    private String choEYmd;
    /** 短期期間開始日 */
    @NotEmpty
    private String tanSYmd;
    /** 短期期間終了日 */
    @NotEmpty
    private String tanEYmd;
    /** 期間開始日 */
    @NotEmpty
    private String kikanSYmd;
    /** 期間終了日 */
    @NotEmpty
    private String kikanEYmd;
    /** 日課連動項目ID */
    private String nikkaId;
    /** サービス曜日リスト */
    @NotEmpty
    private List<Gui01014YobiUpd> yobiList;
    /** 担当者リスト */
    @NotEmpty
    private List<Gui01014TantoUpd> tantoList;
}
