package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

/**
 * GUI01014_計画書（２）
 * 
 * @description
 *              計画書（２）初期情報取得
 *              計画書（２）エンティティ
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class Gui01014Keikasyo2 extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** カウンター */
    private String ks22Id;

    /** 計画書（２）ID */
    private String ks21Id;

    /** 具体的 */
    private String gutaitekiKnj;

    /** 長期 */
    private String choukiKnj;

    /** 短期 */
    private String tankiKnj;

    /** 介護 */
    private String kaigoKnj;

    /** サービス種 */
    private String svShuKnj;

    /** 頻度 */
    private String hindoKnj;

    /** 期間 */
    private String kikanKnj;

    /** 通番 */
    private String seq;

    /** 課題番号 */
    private String kadaiNo;

    /** 介護番号 */
    private String kaigoNo;

    /** 長期期間 */
    private String choKikanKnj;

    /** 短期期間 */
    private String tanKikanKnj;

    /** 給付対象 */
    private String hkyuKbn;

    /** サービス事業者ＣＤ */
    private String jigyouId;

    /** サービス事業者名 */
    private String jigyoNameKnj;

    /** 給付文字 */
    private String hkyuKnj;

    /** 長期期間開始日 */
    private String choSYmd;

    /** 長期期間終了日 */
    private String choEYmd;

    /** 短期期間開始日 */
    private String tanSYmd;

    /** 短期期間終了日 */
    private String tanEYmd;

    /** 期間開始日 */
    private String kikanSYmd;

    /** 期間終了日 */
    private String kikanEYmd;

    /**
     * 日課連動項目ID
     */
    private String nikkaId;

    /** サービス曜日リスト */
    private List<Gui01014Yobi> yobiList = Collections.emptyList();

    /** 担当者リスト */
    private List<Gui01014Tanto> tantoList = Collections.emptyList();
}
