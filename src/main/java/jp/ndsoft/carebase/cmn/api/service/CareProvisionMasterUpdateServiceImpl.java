package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.service.dto.CareProvisionMasterUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.CareProvisionMasterUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui00832StringInputAssistInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui00832StringInputAssistOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDaoUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnMucHcf1Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnMucHcf1;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnMucHcf1Criteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnComMastByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnComMastOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucHcf1SelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * @since 2025.04.09
 * <AUTHOR> DAO VAN DUONG
 * @implNote GUI00832_提供マスタ画面 DTO.
 */
@Service
public class CareProvisionMasterUpdateServiceImpl
        extends UpdateServiceImpl<CareProvisionMasterUpdateServiceInDto, CareProvisionMasterUpdateServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 文字列入力支援DAO */
    @Autowired
    private CpnMucHcf1Mapper cpnMucHcf1Mapper;

    /** 提供マスタ情報取得DAO */
    @Autowired
    private CpnMucHcf1SelectMapper cpnMucHcf1SelectMapper;

    // 固定値「2」:提供マスタ
    private static final Integer CF1_KBN = 2;

    /**
     * ［フェースシート（パッケージプラン）］③
     * 
     * @param inDto ［フェースシート（パッケージプラン）］③の入力DTO.
     * @return［フェースシート（パッケージプラン）］③OUT DTO
     * @throws Exception Exception
     */
    @Override
    protected CareProvisionMasterUpdateServiceOutDto mainProcess(CareProvisionMasterUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // DTOOUT情報
        CareProvisionMasterUpdateServiceOutDto outDto = new CareProvisionMasterUpdateServiceOutDto();
        // DTOOUT情報 文字列入力支援リスト
        List<Gui00832StringInputAssistOutDto> cpnStringInputAssistOutList = new ArrayList<>();
        // 更新件数
        int updateCnt = 0;
        // 新しいリスト文字列入力支援リスト
        List<CpnMucHcf1> cpnMucHcf1List = new ArrayList<>();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. バリデーションチェックを行う===============
         * 
         */
        // ※詳細は別紙「バリデーション詳細」を参照
        // なし

        /*
         * ===============3.提供マスタリストの保存処理===============
         * 
         */
        for (Gui00832StringInputAssistInDto item : inDto.getStringInputAssistList()) {
            // 3.1. 文字列入力支援リスト.更新区分が"C":新規の場合、【文字列入力支援】情報を登録する
            if (CommonDtoUtil.isCreate(item)) {
                final CpnMucHcf1 cpnMucHcf1 = new CpnMucHcf1();
                cpnMucHcf1.setCf1Kbn(CommonDtoUtil.strValToInt(item.getCf1Kbn()));
                cpnMucHcf1.setKbnCd(CommonDtoUtil.strValToInt(item.getKbnCd()));
                cpnMucHcf1.setTextKnj((item.getTextKnj()));
                cpnMucHcf1.setChangeF(CommonDtoUtil.strValToInt(item.getChangeF()));
                cpnMucHcf1.setKbnFlg(CommonDtoUtil.strValToInt(item.getKbnFlg()));

                // UUIDの生成
                // UUID uuid = UUID.randomUUID();
                // ByteBuffer buffer = ByteBuffer.allocate(16);
                // buffer.putLong(uuid.getMostSignificantBits());
                // buffer.putLong(uuid.getLeastSignificantBits());
                // cpnMucHcf1.setRowIdentifier(buffer.array());
                // DAOを実行
                // CommonDaoUtil.setInsertCommonColumns(cpnMucHcf1);
                cpnMucHcf1List.add(cpnMucHcf1);

            } else if (CommonDtoUtil.isUpdate(item)) {
                // 3.2.文字列入力支援リスト.更新区分が"U":更新の場合、【文字列入力支援】情報を更新する
                final CpnMucHcf1Criteria criteria = new CpnMucHcf1Criteria();
                criteria.createCriteria().andCf1IdEqualTo(CommonDtoUtil.strValToInt(item.getCf1Id()));
                // .andDelFlgEqualTo(Constants.DELL_FLG_OFF)
                // .andModifiedCntEqualTo(BigInteger.valueOf((CommonDtoUtil.strValToInt(item.getModifiedCnt()))));
                final CpnMucHcf1 cpnMucHcf1 = new CpnMucHcf1();
                cpnMucHcf1.setCf1Id(CommonDtoUtil.strValToInt(item.getCf1Id()));
                cpnMucHcf1.setCf1Kbn(CommonDtoUtil.strValToInt(item.getCf1Kbn()));
                cpnMucHcf1.setKbnCd(CommonDtoUtil.strValToInt(item.getKbnCd()));
                cpnMucHcf1.setTextKnj((item.getTextKnj()));
                cpnMucHcf1.setChangeF(CommonDtoUtil.strValToInt(item.getChangeF()));
                cpnMucHcf1.setKbnFlg(CommonDtoUtil.strValToInt(item.getKbnFlg()));

                // DAOを実行
                // CommonDaoUtil.setUpdateCommonColumns(cpnMucHcf1, new
                // BigInteger(item.getModifiedCnt()));
                updateCnt = cpnMucHcf1Mapper.updateByCriteriaSelective(cpnMucHcf1, criteria);
                // if (updateCnt <= 0) {
                // throw new ExclusiveException();
                // }

            } else if (CommonDtoUtil.isDelete(item)) {
                // 3.3. 文字列入力支援リスト.更新区分が"D":削除の場合、【文字列入力支援】情報を論理削除する
                final CpnMucHcf1Criteria criteria = new CpnMucHcf1Criteria();
                criteria.createCriteria().andCf1IdEqualTo(CommonDtoUtil.strValToInt(item.getCf1Id()));
                // .andDelFlgEqualTo(Constants.DELL_FLG_OFF)
                // .andModifiedCntEqualTo(BigInteger.valueOf((CommonDtoUtil.strValToInt(item.getModifiedCnt()))));
                // final CpnMucHcf1 cpnMucHcf1 = new CpnMucHcf1();
                // cpnMucHcf1.setCf1Id(CommonDtoUtil.strValToInt(item.getCf1Id()));
                // cpnMucHcf1.setDelFlg(Constants.DELL_FLG_ON);

                // DAOを実行
                // CommonDaoUtil.setDeleteCommonColumns(cpnMucHcf1, new
                // BigInteger(item.getModifiedCnt()));
                updateCnt = cpnMucHcf1Mapper.deleteByCriteria(criteria);
                // if (updateCnt <= 0) {
                // throw new ExclusiveException();
                // }
            }

        }

        // バッチを挿入文字列入力支援リスト
        if (cpnMucHcf1List.size() > 0) {
            cpnMucHcf1Mapper.insertBatch(cpnMucHcf1List);
        }

        // 4. 提供マスタ情報の再取得処理
        // 提供マスタ情報取得のDAOを呼出し、提供マスタ情報を取得する。
        CpnComMastByCriteriaInEntity cpnMucHcf1ByCriteriaInEntity = new CpnComMastByCriteriaInEntity();
        cpnMucHcf1ByCriteriaInEntity.setCf1Kbn(CF1_KBN);
        cpnMucHcf1ByCriteriaInEntity
                .setKbnFlg(CommonDtoUtil.strValToInt(inDto.getStringInputAssistList().getFirst().getKbnFlg()));
        List<CpnComMastOutEntity> cpnMucHcf1OutList = cpnMucHcf1SelectMapper
                .findCpnComMastByCriteria(cpnMucHcf1ByCriteriaInEntity);

        cpnMucHcf1OutList.forEach(item -> {
            Gui00832StringInputAssistOutDto cpnStringInputAssistOut = new Gui00832StringInputAssistOutDto();
            // 入力ID
            cpnStringInputAssistOut.setCf1Id(CommonDtoUtil.objValToString(item.getCf1Id()));
            // 入力区分
            cpnStringInputAssistOut.setCf1Kbn(CommonDtoUtil.objValToString(item.getCf1Kbn()));
            // 区分番号
            cpnStringInputAssistOut.setKbnCd(CommonDtoUtil.objValToString(item.getKbnCd()));
            // 内容
            cpnStringInputAssistOut.setTextKnj(item.getTextKnj());
            // 変更フラグ
            cpnStringInputAssistOut.setChangeF(CommonDtoUtil.objValToString(item.getChangeF()));
            // 区分フラグ
            cpnStringInputAssistOut.setKbnFlg(CommonDtoUtil.objValToString(item.getKbnFlg()));
            // 更新回数
            // cpnStringInputAssistOut.setModifiedCnt(CommonDtoUtil.objValToString(item.getModifiedCnt()));

            cpnStringInputAssistOutList.add(cpnStringInputAssistOut);

        });
        outDto.setStringInputAssistList(cpnStringInputAssistOutList);
        return outDto;
    }
}
