package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00070SavePeriod;
import jp.ndsoft.carebase.cmn.api.service.dto.PeriodUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PeriodUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.KghTucKrkKikanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTucKrkKikan;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTucKrkKikanCriteria;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * GUI00070_対象期間
 * 
 * <AUTHOR>
 */
@Service
public class PeriodUpdateServiceImpl extends UpdateServiceImpl<PeriodUpdateServiceInDto, PeriodUpdateServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    
    /** 計画期間情報を取得 */
    @Autowired
    private KghTucKrkKikanMapper kghTucKrkKikanMapper;

    /**
     * 対象期間情報保存
     * 
     * @param inDto 対象期間情報保存入力DTO.
     * @return 対象期間情報保存DTO
     * @throws Exception
     */
    @Override
    protected PeriodUpdateServiceOutDto mainProcess(
            PeriodUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        PeriodUpdateServiceOutDto outDto = new PeriodUpdateServiceOutDto();
        for (Gui00070SavePeriod inDtoItem : inDto.getPeriodList()) {
            String scId = CommonConstants.EMPTY_STRING;
            // "":変更なし
            if (inDtoItem.getUpdateKbn() == null || CommonDtoUtil.isOperationNone(inDtoItem)) {
                continue;
            }
            // 2-1．リクエストパラメータ詳細.グループリスト(n).更新区分="C"：新規の場合、【対象期間】情報を登録する。
            if (CommonDtoUtil.isCreate(inDtoItem)) {
                scId = getInsCnt(inDtoItem);
            }
            // 2-2．リクエストパラメータ詳細.グループリスト(n).更新区分="U"：更新の場合、【対象期間】情報を更新する。
            if (CommonDtoUtil.isUpdate(inDtoItem)) {
                scId = inDtoItem.getSc1Id();
                if (getUpdCnt(inDtoItem) <= 0) {
                    throw new ExclusiveException();
                }
            }
            // 2-3．リクエストパラメータ詳細.グループリスト(n).更新区分="D"：削除の場合、【対象期間】情報を削除する。
            if (CommonDtoUtil.isDelete(inDtoItem)) {
                if (getDelCnt(inDtoItem) <= 0) {
                    throw new ExclusiveException();
                }
                // 関連テーブルの削除(uf_delete_sc1)
            }
            if (StringUtils.isNoneEmpty(inDto.getKikanIndex())
                    && inDto.getKikanIndex().equals(inDtoItem.getKikanIndex())) {
                outDto.setSc1Id(scId);
            }
        }
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 【対象期間】情報を登録
     * 
     * @param inDtoItem 入力Dto
     * @throws Exception
     */
    private String getInsCnt(Gui00070SavePeriod inDtoItem) throws Exception {
        KghTucKrkKikan kghTucKrkKikan = new KghTucKrkKikan();
        // リクエストパラメータ.法人ＩＤ
        kghTucKrkKikan.setHoujinId(CommonDtoUtil.strValToInt(inDtoItem.getHoujinId()));
        // リクエストパラメータ.施設ＩＤ
        kghTucKrkKikan.setShisetuId(CommonDtoUtil.strValToInt(inDtoItem.getShisetuId()));
        // リクエストパラメータ.事業所ＩＤ
        kghTucKrkKikan.setSvJigyoId(CommonDtoUtil.strValToInt(inDtoItem.getSvJigyoId()));
        // リクエストパラメータ.利用者ＩＤ
        kghTucKrkKikan.setUserId(CommonDtoUtil.strValToInt(inDtoItem.getUserId()));
        // リクエストパラメータ.種別ＩＤ
        kghTucKrkKikan.setSyubetsuId(CommonDtoUtil.strValToInt(inDtoItem.getSyubetsuId()));
        // リクエストパラメータ.開始日
        kghTucKrkKikan.setStartYmd(inDtoItem.getStartYmd());
        // リクエストパラメータ.終了日
        kghTucKrkKikan.setEndYmd(inDtoItem.getEndYmd());
        kghTucKrkKikanMapper.insertSelectiveAndReturn(kghTucKrkKikan);
        return CommonDtoUtil.objValToString(kghTucKrkKikan.getSc1Id());
    }

    /**
     * 【対象期間】情報を更新
     * 
     * @param inDtoItem 入力Dto
     */
    private Integer getUpdCnt(Gui00070SavePeriod inDtoItem) {
        // 更新対象
        KghTucKrkKikan updItem = new KghTucKrkKikan();
        // リクエストパラメータ.開始日
        updItem.setStartYmd(inDtoItem.getStartYmd());
        // リクエストパラメータ.終了日
        updItem.setEndYmd(inDtoItem.getEndYmd());

        // 更新条件キー
        KghTucKrkKikanCriteria whereItem = new KghTucKrkKikanCriteria();
        whereItem.createCriteria().andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDtoItem.getSc1Id()))
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDtoItem.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDtoItem.getShisetuId()))
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDtoItem.getSvJigyoId()))
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDtoItem.getUserId()))
                .andSyubetsuIdEqualTo(CommonDtoUtil.strValToInt(inDtoItem.getSyubetsuId()));
        return kghTucKrkKikanMapper.updateByCriteriaSelective(updItem, whereItem);
    }

    /**
     * 【対象期間】情報を削除
     * 
     * @param inDtoItem 入力Dto
     */
    private Integer getDelCnt(Gui00070SavePeriod inDtoItem) {
        // 更新条件キー
        KghTucKrkKikanCriteria whereItem = new KghTucKrkKikanCriteria();

        whereItem.createCriteria().andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDtoItem.getSc1Id()))
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDtoItem.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDtoItem.getShisetuId()))
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDtoItem.getSvJigyoId()))
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDtoItem.getUserId()))
                .andSyubetsuIdEqualTo(CommonDtoUtil.strValToInt(inDtoItem.getSyubetsuId()));
        return kghTucKrkKikanMapper.deleteByCriteria(whereItem);
    }
}
