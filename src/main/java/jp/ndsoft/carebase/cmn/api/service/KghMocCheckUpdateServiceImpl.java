package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00891KoumokuIn;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00891NaiyouIn;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00891SaimokuIn;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00891YoushikiIn;
import jp.ndsoft.carebase.cmn.api.service.dto.KghMocCheckUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.KghMocCheckUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocCheckKoumokuMapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocCheckNaiyouMapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocCheckSaimokuMapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocCheckYoushikiMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocCheckKoumoku;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocCheckKoumokuCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocCheckNaiyou;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocCheckNaiyouCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocCheckSaimoku;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocCheckSaimokuCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocCheckYoushiki;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocCheckYoushikiCriteria;
import jp.ndsoft.carebase.common.util.CareBaseMComSeqMgrCodeConstants;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.smh.framework.services.util.Numbering;
import jp.ndsoft.smh.framework.util.AppUtil;

/**
 * @since 2025.05.24
 * <AUTHOR>
 * @description GUI00891_フリーアセスメントチェック項目マスタ 情報取得
 */
@Service
public class KghMocCheckUpdateServiceImpl extends
        UpdateServiceImpl<KghMocCheckUpdateServiceInDto, KghMocCheckUpdateServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private Numbering numbering;

    /** 32-03 チェック項目様式マスタ */
    @Autowired
    private KghMocCheckYoushikiMapper mocCheckYoushikiMapper;

    /** 32-04 チェック項目評価項目マスタ */
    @Autowired
    private KghMocCheckKoumokuMapper mocCheckKoumokuMapper;

    /** 32-05 チェック項目評価細目マスタ */
    @Autowired
    private KghMocCheckSaimokuMapper mocCheckSaimokuMapper;

    /** 32-06 チェック項目評価内容マスタ */
    @Autowired
    private KghMocCheckNaiyouMapper mocCheckNaiyoMapper;

    /**
     * GUI00891_フリーアセスメントチェック項目マスタ 情報を取得する。
     * 
     * @param inDto GUI00891_フリーアセスメントチェック項目マスタ 情報取得サービス入力Dto
     * @return GUI00891_フリーアセスメントチェック項目マスタ 情報取得サービス出力Dto
     * @throws Exception 例外
     */
    @Override
    protected KghMocCheckUpdateServiceOutDto mainProcess(KghMocCheckUpdateServiceInDto inDto)
            throws Exception {

        LOG.info(Constants.START);

        // 戻り情報を設定
        KghMocCheckUpdateServiceOutDto outDto = new KghMocCheckUpdateServiceOutDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============3. 保存処理===============
         * 
         */
        if (CommonConstants.UPDATE_SUBJECT_KBN_YOUSIKI.equals(inDto.getUpdateSubjectKbn())) {
            // リクエストパラメータ.更新対象区分="1"（32-03
            // チェック項目様式マスタ）の場合は、リクエストパラメータ.チェック項目様式マスタリストの件数分の処理４を繰り返す
            this.saveYoushiki(inDto);
        } else if (CommonConstants.UPDATE_SUBJECT_KBN_KOUMOKU.equals(inDto.getUpdateSubjectKbn())) {
            // リクエストパラメータ.更新対象区分="2"（32-04
            // チェック項目評価項目マスタ）の場合は、リクエストパラメータ.チェック項目評価項目マスタリストの件数分の処理５を繰り返す
            this.saveKoumoku(inDto);
        } else if (CommonConstants.UPDATE_SUBJECT_KBN_SAIMOKU.equals(inDto.getUpdateSubjectKbn())) {
            // リクエストパラメータ.更新対象区分="3"（32-05
            // チェック項目評価細目マスタ）の場合は、リクエストパラメータ.チェック項目評価細目マスタリストの件数分の処理６を繰り返す
            this.saveSaimoku(inDto);
        } else if (CommonConstants.UPDATE_SUBJECT_KBN_NAIYO.equals(inDto.getUpdateSubjectKbn())) {
            // リクエストパラメータ.更新対象区分="4"（32-06
            // チェック項目評価内容マスタ）の場合は、リクエストパラメータ.チェック項目評価内容マスタリストの件数分の処理７を繰り返す
            this.saveNaiyou(inDto);
        }

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * ４. チェック項目様式マスタデータの保存処理
     * 関数名：saveYoushiki
     * 
     * @param inDto 登録パラメータ
     * @throws Exception 例外
     */
    private void saveYoushiki(KghMocCheckUpdateServiceInDto inDto) throws Exception {
        List<Gui00891YoushikiIn> kghMocCheckYoushikiList = inDto.getKghMocCheckYoushikiList();
        for (Gui00891YoushikiIn youshiki : kghMocCheckYoushikiList) {
            if (CommonDtoUtil.isCreate(youshiki)) {
                // 4.1. リクエストパラメータ.更新区分が"C":新規の場合、【32-03 チェック項目様式マスタ】情報を登録する。
                this.insertYoushiki(youshiki);
            } else if (CommonDtoUtil.isUpdate(youshiki)) {
                // 4.2. リクエストパラメータ.更新区分が"U":更新の場合、【32-03 チェック項目様式マスタ】情報を更新する。
                this.updateYoushiki(youshiki);
            } else if (CommonDtoUtil.isDelete(youshiki)) {
                // 4.3. リクエストパラメータ.更新区分が"D":削除の場合、【32-03 チェック項目様式マスタ】情報を削除更新する。
                this.deleteYoushiki(youshiki);
            }
        }
    }

    /**
     * 32-03 チェック項目様式マスタの登録処理
     * 関数名：insertYoushiki
     * 
     * @param youshikiIn 登録パラメータ
     * @throws Exception 例外
     */
    private void insertYoushiki(Gui00891YoushikiIn youshikiIn) throws Exception {
        // 4.1. リクエストパラメータ.更新区分が"C":新規の場合、【32-03 チェック項目様式マスタ】情報を登録する。
        KghMocCheckYoushiki youshiki = new KghMocCheckYoushiki();

        // 法人ID
        youshiki.setHoujinId(CommonDtoUtil.strValToInt(youshikiIn.getHoujinId()));
        // 施設ID
        youshiki.setShisetuId(CommonDtoUtil.strValToInt(youshikiIn.getShisetuId()));
        // 事業者ID
        youshiki.setSvJigyoId(CommonDtoUtil.strValToInt(youshikiIn.getSvJigyoId()));
        // 適用フラグ
        youshiki.setUseFlg(CommonDtoUtil.strValToInt(youshikiIn.getUseFlg()));
        // 様式名称
        youshiki.setYoushikiKnj(youshikiIn.getYoushikiKnj());
        // 表示順
        youshiki.setSort(CommonDtoUtil.strValToInt(youshikiIn.getSort()));

        // DAOを実行
        this.mocCheckYoushikiMapper.insertSelective(youshiki);
    }

    /**
     * 32-03 チェック項目様式マスタの更新処理
     * 関数名：updateYoushiki
     * 
     * @param youshikiIn 登録パラメータ
     * @throws Exception 例外
     */
    private void updateYoushiki(Gui00891YoushikiIn youshikiIn) throws Exception {
        // 4.2. リクエストパラメータ.更新区分が"U":更新の場合、【32-03 チェック項目様式マスタ】情報を更新する。
        KghMocCheckYoushiki youshiki = new KghMocCheckYoushiki();
        // 適用フラグ
        youshiki.setUseFlg(CommonDtoUtil.strValToInt(youshikiIn.getUseFlg()));
        // 様式名称
        youshiki.setYoushikiKnj(youshikiIn.getYoushikiKnj());
        // 表示順
        youshiki.setSort(CommonDtoUtil.strValToInt(youshikiIn.getSort()));

        KghMocCheckYoushikiCriteria criteria = new KghMocCheckYoushikiCriteria();
        criteria.createCriteria().andHoujinIdEqualTo(CommonDtoUtil.strValToInt(youshikiIn.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(youshikiIn.getShisetuId()))
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(youshikiIn.getSvJigyoId()))
                .andYoushikiIdEqualTo(CommonDtoUtil.strValToInt(youshikiIn.getYoushikiId()));

        // DAOを実行
        int count = this.mocCheckYoushikiMapper.updateByCriteriaSelective(youshiki, criteria);

        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 32-03 チェック項目様式マスタの削除処理
     * 関数名：deleteYoushiki
     * 
     * @param youshikiIn 登録パラメータ
     * @throws Exception 例外
     */
    private void deleteYoushiki(Gui00891YoushikiIn youshikiIn) throws Exception {

        KghMocCheckYoushikiCriteria criteria = new KghMocCheckYoushikiCriteria();
        criteria.createCriteria().andHoujinIdEqualTo(CommonDtoUtil.strValToInt(youshikiIn.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(youshikiIn.getShisetuId()))
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(youshikiIn.getSvJigyoId()))
                .andYoushikiIdEqualTo(CommonDtoUtil.strValToInt(youshikiIn.getYoushikiId()));

        // DAOを実行
        int count = this.mocCheckYoushikiMapper.deleteByCriteria(criteria);
        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 5. チェック項目評価項目マスタデータの保存処理
     * 関数名：saveKoumoku
     * 
     * @param inDto 登録パラメータ
     * @throws Exception 例外
     */
    private void saveKoumoku(KghMocCheckUpdateServiceInDto inDto) throws Exception {
        List<Gui00891KoumokuIn> kghMocCheckKoumokuList = inDto.getKghMocCheckKoumokuList();
        for (Gui00891KoumokuIn koumoku : kghMocCheckKoumokuList) {
            if (CommonDtoUtil.isCreate(koumoku)) {
                // 5.1. リクエストパラメータ.更新区分が"C":新規の場合、【32-04 チェック項目評価項目マスタ】情報を登録する。
                this.insertKoumoku(koumoku);
            } else if (CommonDtoUtil.isUpdate(koumoku)) {
                // 5.2. リクエストパラメータ.更新区分が"U":更新の場合、【32-04 チェック項目評価項目マスタ】情報を更新する。
                this.updateKoumoku(koumoku);
            } else if (CommonDtoUtil.isDelete(koumoku)) {
                // 5.3. リクエストパラメータ.更新区分が"D":削除の場合、【32-04 チェック項目評価項目マスタ】情報を削除更新する。
                this.deleteKoumoku(koumoku);
            }
        }
    }

    /**
     * 32-04 チェック項目評価項目マスタの登録処理
     * 関数名：insertKoumoku
     * 
     * @param koumokuIn 登録パラメータ
     * @throws Exception 例外
     */
    private void insertKoumoku(Gui00891KoumokuIn koumokuIn) throws Exception {
        KghMocCheckKoumoku koumoku = new KghMocCheckKoumoku();

        // 法人ID
        koumoku.setHoujinId(CommonDtoUtil.strValToInt(koumokuIn.getHoujinId()));
        // 施設ID
        koumoku.setShisetuId(CommonDtoUtil.strValToInt(koumokuIn.getShisetuId()));
        // 事業者ID
        koumoku.setSvJigyoId(CommonDtoUtil.strValToInt(koumokuIn.getSvJigyoId()));
        // 様式ＩＤ
        koumoku.setYoushikiId(CommonDtoUtil.strValToInt(koumokuIn.getYoushikiId()));
        // 項目名称
        koumoku.setKoumokuKnj(koumokuIn.getKoumokuKnj());
        // 表示順
        koumoku.setSort(CommonDtoUtil.strValToInt(koumokuIn.getSort()));

        // DAOを実行
        this.mocCheckKoumokuMapper.insertSelective(koumoku);
    }

    /**
     * 32-04 チェック項目評価項目マスタの更新処理
     * 関数名：updateKoumoku
     * 
     * @param koumokuIn 更新パラメータ
     * @throws Exception 例外
     */
    private void updateKoumoku(Gui00891KoumokuIn koumokuIn) throws Exception {

        KghMocCheckKoumoku koumoku = new KghMocCheckKoumoku();
        // 項目名称
        koumoku.setKoumokuKnj(koumokuIn.getKoumokuKnj());
        // 表示順
        koumoku.setSort(CommonDtoUtil.strValToInt(koumokuIn.getSort()));

        // 更新条件
        KghMocCheckKoumokuCriteria criteria = new KghMocCheckKoumokuCriteria();
        // 法人ID
        // 施設ID
        // 事業者ID
        // 様式ID
        // 項目ＩＤ
        // 削除フラグ
        // 更新回数
        criteria.createCriteria().andHoujinIdEqualTo(CommonDtoUtil.strValToInt(koumokuIn.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(koumokuIn.getShisetuId()))
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(koumokuIn.getSvJigyoId()))
                .andYoushikiIdEqualTo(CommonDtoUtil.strValToInt(koumokuIn.getYoushikiId()))
                .andKoumokuIdEqualTo(CommonDtoUtil.strValToInt(koumokuIn.getKoumokuId()));

        // DAOを実行
        int count = this.mocCheckKoumokuMapper.updateByCriteriaSelective(koumoku, criteria);

        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 32-04 チェック項目評価項目マスタの削除処理
     * 関数名：deleteKoumoku
     * 
     * @param koumokuIn 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteKoumoku(Gui00891KoumokuIn koumokuIn) throws Exception {

        // 削除条件
        KghMocCheckKoumokuCriteria criteria = new KghMocCheckKoumokuCriteria();
        // 法人ID
        // 施設ID
        // 事業者ID
        // 様式ID
        // 項目ＩＤ
        // 削除フラグ
        // 更新回数
        criteria.createCriteria().andHoujinIdEqualTo(CommonDtoUtil.strValToInt(koumokuIn.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(koumokuIn.getShisetuId()))
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(koumokuIn.getSvJigyoId()))
                .andYoushikiIdEqualTo(CommonDtoUtil.strValToInt(koumokuIn.getYoushikiId()))
                .andKoumokuIdEqualTo(CommonDtoUtil.strValToInt(koumokuIn.getKoumokuId()));

        // DAOを実行
        int count = this.mocCheckKoumokuMapper.deleteByCriteria(criteria);
        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * ６. チェック項目評価細目マスタデータの保存処理
     * 関数名：saveSaimoku
     * 
     * @param inDto 登録パラメータ
     * @throws Exception 例外
     */
    private void saveSaimoku(KghMocCheckUpdateServiceInDto inDto) throws Exception {
        List<Gui00891SaimokuIn> kghMocCheckSaimokuList = inDto.getKghMocCheckSaimokuList();
        for (Gui00891SaimokuIn saimoku : kghMocCheckSaimokuList) {
            if (CommonDtoUtil.isCreate(saimoku)) {
                // 6.1. リクエストパラメータ.更新区分が"C":新規の場合、【32-05 チェック項目評価細目マスタ】情報を登録する。
                this.insertSaimoku(saimoku);
            } else if (CommonDtoUtil.isUpdate(saimoku)) {
                // 6.2. リクエストパラメータ.更新区分が"U":更新の場合、【32-05 チェック項目評価細目マスタ】情報を更新する。
                this.updateSaimoku(saimoku);
            } else if (CommonDtoUtil.isDelete(saimoku)) {
                // 6.3. リクエストパラメータ.更新区分が"D":削除の場合、【32-05 チェック項目評価細目マスタ】情報を削除更新する。
                this.deleteSaimoku(saimoku);
            }
        }
    }

    /**
     * 32-04 チェック項目評価項目マスタの登録処理
     * 関数名：insertSaimoku
     * 
     * @param saimokuIn 登録パラメータ
     * @throws Exception 例外
     */
    private void insertSaimoku(Gui00891SaimokuIn saimokuIn) throws Exception {
        KghMocCheckSaimoku saimoku = new KghMocCheckSaimoku();

        // 法人ID
        saimoku.setHoujinId(CommonDtoUtil.strValToInt(saimokuIn.getHoujinId()));
        // 施設ID
        saimoku.setShisetuId(CommonDtoUtil.strValToInt(saimokuIn.getShisetuId()));
        // 事業者ID
        saimoku.setSvJigyoId(CommonDtoUtil.strValToInt(saimokuIn.getSvJigyoId()));
        // 様式ＩＤ
        saimoku.setYoushikiId(CommonDtoUtil.strValToInt(saimokuIn.getYoushikiId()));
        // 項目ＩＤ
        saimoku.setKoumokuId(CommonDtoUtil.strValToInt(saimokuIn.getKoumokuId()));
        // 細目名称
        saimoku.setSaimokuKnj(saimokuIn.getSaimokuKnj());
        // 表示順
        saimoku.setSort(CommonDtoUtil.strValToInt(saimokuIn.getSort()));

        // DAOを実行
        this.mocCheckSaimokuMapper.insertSelective(saimoku);
    }

    /**
     * 32-05 チェック項目評価細目マスタの更新処理
     * 関数名：updateSaimoku
     * 
     * @param saimokuIn 更新パラメータ
     * @throws Exception 例外
     */
    private void updateSaimoku(Gui00891SaimokuIn saimokuIn) throws Exception {

        KghMocCheckSaimoku saimoku = new KghMocCheckSaimoku();
        // 細目名称
        saimoku.setSaimokuKnj(saimokuIn.getSaimokuKnj());
        // 表示順
        saimoku.setSort(CommonDtoUtil.strValToInt(saimokuIn.getSort()));

        // 削除条件
        KghMocCheckSaimokuCriteria criteria = new KghMocCheckSaimokuCriteria();
        // 法人ID
        // 施設ID
        // 事業者ID
        // 様式ID
        // 項目ＩＤ
        // 細目ID
        // 削除フラグ
        // 更新回数
        criteria.createCriteria().andHoujinIdEqualTo(CommonDtoUtil.strValToInt(saimokuIn.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(saimokuIn.getShisetuId()))
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(saimokuIn.getSvJigyoId()))
                .andYoushikiIdEqualTo(CommonDtoUtil.strValToInt(saimokuIn.getYoushikiId()))
                .andKoumokuIdEqualTo(CommonDtoUtil.strValToInt(saimokuIn.getKoumokuId()))
                .andSaimokuIdEqualTo(CommonDtoUtil.strValToInt(saimokuIn.getSaimokuId()));

        // DAOを実行
        int count = this.mocCheckSaimokuMapper.updateByCriteriaSelective(saimoku, criteria);

        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 32-05 チェック項目評価細目マスタの削除処理
     * 関数名：deleteSaimoku
     * 
     * @param saimokuIn 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteSaimoku(Gui00891SaimokuIn saimokuIn) throws Exception {

        // 削除条件
        KghMocCheckSaimokuCriteria criteria = new KghMocCheckSaimokuCriteria();
        // 法人ID
        // 施設ID
        // 事業者ID
        // 様式ID
        // 項目ＩＤ
        // 細目ID
        // 削除フラグ
        // 更新回数
        criteria.createCriteria().andHoujinIdEqualTo(CommonDtoUtil.strValToInt(saimokuIn.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(saimokuIn.getShisetuId()))
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(saimokuIn.getSvJigyoId()))
                .andYoushikiIdEqualTo(CommonDtoUtil.strValToInt(saimokuIn.getYoushikiId()))
                .andKoumokuIdEqualTo(CommonDtoUtil.strValToInt(saimokuIn.getKoumokuId()))
                .andSaimokuIdEqualTo(CommonDtoUtil.strValToInt(saimokuIn.getSaimokuId()));

        // DAOを実行
        int count = this.mocCheckSaimokuMapper.deleteByCriteria(criteria);
        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 7. チェック項目評価内容マスタデータの保存処理
     * 関数名：saveNaiyou
     * 
     * @param inDto 登録パラメータ
     * @throws Exception 例外
     */
    private void saveNaiyou(KghMocCheckUpdateServiceInDto inDto) throws Exception {
        List<Gui00891NaiyouIn> kghMocCheckNaiyouList = inDto.getKghMocCheckNaiyouList();
        for (Gui00891NaiyouIn naiyou : kghMocCheckNaiyouList) {
            if (CommonDtoUtil.isCreate(naiyou)) {
                // 7.1. リクエストパラメータ.更新区分が"C":新規の場合、【32-06 チェック項目評価内容マスタ】情報を登録する。
                this.insertNaiyou(naiyou);
            } else if (CommonDtoUtil.isUpdate(naiyou)) {
                // 7.2. リクエストパラメータ.更新区分が"U":更新の場合、【32-06 チェック項目評価内容マスタ】情報を更新する。
                this.updateNaiyou(naiyou);
            } else if (CommonDtoUtil.isDelete(naiyou)) {
                // 7.3. リクエストパラメータ.更新区分が"D":削除の場合、【32-06 チェック項目評価内容マスタ】情報を削除更新する。
                this.deleteNaiyou(naiyou);
            }
        }
    }

    /**
     * 32-06 チェック項目評価内容マスタの登録処理
     * 関数名：insertNaiyou
     * 
     * @param naiyouIn 登録パラメータ
     * @throws Exception 例外
     */
    private void insertNaiyou(Gui00891NaiyouIn naiyouIn) throws Exception {
        KghMocCheckNaiyou naiyou = new KghMocCheckNaiyou();

        // 法人ID
        naiyou.setHoujinId(CommonDtoUtil.strValToInt(naiyouIn.getHoujinId()));
        // 施設ID
        naiyou.setShisetuId(CommonDtoUtil.strValToInt(naiyouIn.getShisetuId()));
        // 事業者ID
        naiyou.setSvJigyoId(CommonDtoUtil.strValToInt(naiyouIn.getSvJigyoId()));
        // 様式ＩＤ
        naiyou.setYoushikiId(CommonDtoUtil.strValToInt(naiyouIn.getYoushikiId()));
        // 項目ＩＤ
        naiyou.setKoumokuId(CommonDtoUtil.strValToInt(naiyouIn.getKoumokuId()));
        // 細目ＩＤ
        naiyou.setSaimokuId(CommonDtoUtil.strValToInt(naiyouIn.getSaimokuId()));
        // 点数
        naiyou.setPoint(CommonDtoUtil.strValToInt(naiyouIn.getPoint()));
        // 内容名称
        naiyou.setNaiyouKnj(naiyouIn.getNaiyouKnj());
        // 選択肢説明文
        naiyou.setSetumeiKnj(naiyouIn.getSetumeiKnj());
        // 表示順
        naiyou.setSort(CommonDtoUtil.strValToInt(naiyouIn.getSort()));

        // DAOを実行
        this.mocCheckNaiyoMapper.insertSelective(naiyou);
    }

    /**
     * 32-06 チェック項目評価内容マスタの更新処理
     * 関数名：updateNaiyou
     * 
     * @param naiyouIn 更新パラメータ
     * @throws Exception 例外
     */
    private void updateNaiyou(Gui00891NaiyouIn naiyouIn) throws Exception {

        KghMocCheckNaiyou naiyou = new KghMocCheckNaiyou();
        // 点数
        naiyou.setPoint(CommonDtoUtil.strValToInt(naiyouIn.getPoint()));
        // 内容名称
        naiyou.setNaiyouKnj(naiyouIn.getNaiyouKnj());
        // 選択肢説明文
        naiyou.setSetumeiKnj(naiyouIn.getSetumeiKnj());
        // 表示順
        naiyou.setSort(CommonDtoUtil.strValToInt(naiyouIn.getSort()));

        // 更新条件
        KghMocCheckNaiyouCriteria criteria = new KghMocCheckNaiyouCriteria();
        // 法人ID
        // 施設ID
        // 事業者ID
        // 様式ID
        // 項目ＩＤ
        // 細目ID
        // 内容ID
        // 削除フラグ
        // 更新回数
        criteria.createCriteria().andHoujinIdEqualTo(CommonDtoUtil.strValToInt(naiyouIn.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(naiyouIn.getShisetuId()))
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(naiyouIn.getSvJigyoId()))
                .andYoushikiIdEqualTo(CommonDtoUtil.strValToInt(naiyouIn.getYoushikiId()))
                .andKoumokuIdEqualTo(CommonDtoUtil.strValToInt(naiyouIn.getKoumokuId()))
                .andSaimokuIdEqualTo(CommonDtoUtil.strValToInt(naiyouIn.getSaimokuId()))
                .andNaiyouIdEqualTo(CommonDtoUtil.strValToInt(naiyouIn.getNaiyouId()));

        // DAOを実行
        int count = this.mocCheckNaiyoMapper.updateByCriteriaSelective(naiyou, criteria);

        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }

    /**
     * 32-06 チェック項目評価内容マスタの削除処理
     * 関数名：deleteNaiyou
     * 
     * @param naiyouIn 削除パラメータ
     * @throws Exception 例外
     */
    private void deleteNaiyou(Gui00891NaiyouIn naiyouIn) throws Exception {

        // 削除条件
        KghMocCheckNaiyouCriteria criteria = new KghMocCheckNaiyouCriteria();
        // 法人ID
        // 施設ID
        // 事業者ID
        // 様式ID
        // 項目ＩＤ
        // 細目ID
        // 内容ID
        // 削除フラグ
        // 更新回数
        criteria.createCriteria().andHoujinIdEqualTo(CommonDtoUtil.strValToInt(naiyouIn.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(naiyouIn.getShisetuId()))
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(naiyouIn.getSvJigyoId()))
                .andYoushikiIdEqualTo(CommonDtoUtil.strValToInt(naiyouIn.getYoushikiId()))
                .andKoumokuIdEqualTo(CommonDtoUtil.strValToInt(naiyouIn.getKoumokuId()))
                .andSaimokuIdEqualTo(CommonDtoUtil.strValToInt(naiyouIn.getSaimokuId()))
                .andNaiyouIdEqualTo(CommonDtoUtil.strValToInt(naiyouIn.getNaiyouId()));

        // DAOを実行
        int count = this.mocCheckNaiyoMapper.deleteByCriteria(criteria);
        // 更新失敗の場合
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }
}
