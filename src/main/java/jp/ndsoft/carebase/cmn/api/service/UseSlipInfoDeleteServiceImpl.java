package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.KghKhnPnr1Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghSmglHistoryLogic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.KkakCp1OutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Kohi;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149PlanOv30;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Riyou;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149RiyouBeppyo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149RiyouKakunin;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149RiyouOtherData;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149RiyouOtherDataDelInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Riyoursha;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Riyouryou;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149ServicePoint;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149SvplanShort;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Syafuku;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149SyuruiGendo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149TaihiData;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149TlcSvplanShort;
import jp.ndsoft.carebase.cmn.api.service.dto.UseSlipInfoDeleteServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.UseSlipInfoDeleteServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanBetu1Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanBetu2Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanGaiFutanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanKouhiFutanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanOv30Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanRiyouKakuninMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanRiyouMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanSyafukuKeigenMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucServicePointMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucSvplanShortMapper;
import jp.ndsoft.carebase.common.dao.mybatis.ComTlcSvplanPointMapper;
import jp.ndsoft.carebase.common.dao.mybatis.ComTlcSvplanResultMapper;
import jp.ndsoft.carebase.common.dao.mybatis.ComTlcSvplanShortMapper;
import jp.ndsoft.carebase.common.dao.mybatis.TrsTucServicePointYMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanBetu1Key;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanBetu2Key;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanGaiFutanCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanKey;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanKouhiFutanKey;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanOv30Key;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanRiyouKakuninKey;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanRiyouKey;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanSyafukuKeigenKey;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucServicePointKey;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucSvplanShortKey;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComTlcSvplanPointCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComTlcSvplanResultCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComTlcSvplanShortKey;
import jp.ndsoft.carebase.common.dao.mybatis.entity.TrsTucServicePointYCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnCpmvTlcSvplanPointDelByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnCpmvTlcSvplanPointDelOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnCpmvTlcSvplanResultDelByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnCpmvTlcSvplanResultDelOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSimuObject1OpeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSimuObject1OpeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTrsFServicePointYDelByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTrsFServicePointYDelOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscKaigoHokenjaSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTlcSvplanPointSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTlcSvplanResultSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.TrsTucServicePointYSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * GUI01149_利用票 APINo(709)_利用票画面情報削除処理
 *
 * <AUTHOR>
 */
@Service
public class UseSlipInfoDeleteServiceImpl
        extends UpdateServiceImpl<UseSlipInfoDeleteServiceInDto, UseSlipInfoDeleteServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** e-文書法区分取得 */
    @Autowired
    private KghSmglHistoryLogic kghSmglHistoryLogic;
    /** ComMscKaigoHokenjaSelectMapper サービス */
    @Autowired
    private ComMscKaigoHokenjaSelectMapper comMscKaigoHokenjaSelectMapper;
    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;
    /** ComTlcSvplanResultSelectMapper サービス */
    @Autowired
    private ComTlcSvplanResultSelectMapper comTlcSvplanResultSelectMapper;
    /** ComTlcSvplanPointSelectMapper サービス */
    @Autowired
    private ComTlcSvplanPointSelectMapper comTlcSvplanPointSelectMapper;
    /** TrsTucServicePointYSelectMapper サービス */
    @Autowired
    private TrsTucServicePointYSelectMapper trsTucServicePointYSelectMapper;
    /** CmnTucPlanMapper サービス利用票・別表ヘッダ */
    @Autowired
    private CmnTucPlanMapper cmnTucPlanMapper;
    /** CmnTucPlanRiyouMapper サービス利用票明細 */
    @Autowired
    private CmnTucPlanRiyouMapper cmnTucPlanRiyouMapper;
    /** CmnTucPlanBetu1Mapper 利用票別表 区分支給限度管理 */
    @Autowired
    private CmnTucPlanBetu1Mapper cmnTucPlanBetu1Mapper;
    @Autowired
    private KghKhnPnr1Logic kghKhnPnr1Logic;
    /** CmnTucPlanBetu2Mapper 利用票別表 種類別限度管理 */
    @Autowired
    private CmnTucPlanBetu2Mapper cmnTucPlanBetu2Mapper;
    /** CmnTucPlanKouhiFutanMapper 利用票別表 公費負担 */
    @Autowired
    private CmnTucPlanKouhiFutanMapper cmnTucPlanKouhiFutanMapper;
    /** CmnTucPlanGaiFutanMapper 利用票別表 保険外負担 */
    @Autowired
    private CmnTucPlanGaiFutanMapper cmnTucPlanGaiFutanMapper;
    /** CmnTucPlanOv30Mapper 利用票短期利用30日超過 */
    @Autowired
    private CmnTucPlanOv30Mapper cmnTucPlanOv30Mapper;
    /** CmnTucPlanSyafukuKeigenMapper 利用票別表 社福軽減 */
    @Autowired
    private CmnTucPlanSyafukuKeigenMapper cmnTucPlanSyafukuKeigenMapper;
    /** CmnTucPlanRiyouKakuninMapper サービス利用票：算定確認（20110828） */
    @Autowired
    private CmnTucPlanRiyouKakuninMapper cmnTucPlanRiyouKakuninMapper;
    /** CmnTucServicePointMapper 利用票別表:提供事業所毎小計情報 */
    @Autowired
    private CmnTucServicePointMapper cmnTucServicePointMapper;
    /** CmnTucSvplanShortMapper 短期利用日数保持 */
    @Autowired
    private CmnTucSvplanShortMapper cmnTucSvplanShortMapper;
    /** ComTlcSvplanResultMapper サービス計画予定実績（19-1） */
    @Autowired
    private ComTlcSvplanResultMapper comTlcSvplanResultMapper;
    /** ComTlcSvplanPointMapper サービス別計画点数（19-2） */
    @Autowired
    private ComTlcSvplanPointMapper comTlcSvplanPointMapper;
    /** ComTlcSvplanShortMapper 介護予防短期入所利用状況 */
    @Autowired
    private ComTlcSvplanShortMapper comTlcSvplanShortMapper;
    /** TrsTucServicePointYMapper 様式Ｆ用予定単位数保存テーブル */
    @Autowired
    private TrsTucServicePointYMapper trsTucServicePointYMapper;

    /**
     * データ保存
     *
     * @param inDto 利用票画面情報削除処理の入力DTO
     * @return outDto 利用票画面情報削除処理の出力DTO
     * @throws Exception
     */
    protected UseSlipInfoDeleteServiceOutDto mainProcess(UseSlipInfoDeleteServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        // 利用票画面情報削除処理の出力DTO
        UseSlipInfoDeleteServiceOutDto outDto = new UseSlipInfoDeleteServiceOutDto();

        // 2.削除前のチェック情報を取得する
        getCheckInfo(inDto, outDto);

        // 3.リクエストパラメータ.利用者情報存在フラグ == false場合、処理終了、上記処理で取得した結果レスポンスを返却する。
        if (CommonConstants.STR_FALSE.equals(inDto.getRiyourshaDataExitFlg())) {
            LOG.info(Constants.END);
            return outDto;
        }

        // 4.リクエストパラメータ.利用者情報存在フラグ == true場合、削除処理を行う
        if (CommonConstants.STR_TRUE.equals(inDto.getRiyourshaDataExitFlg())) {
            deleteProc(inDto, outDto);
        }

        // 5. 上記処理で取得した結果レスポンスを返却する。
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 2.削除前のチェック情報を取得する
     * 
     * @param inDto  利用票画面情報削除処理の入力DTO
     * @param outDto 利用票画面情報削除処理の出力DTO
     */
    private void getCheckInfo(UseSlipInfoDeleteServiceInDto inDto, UseSlipInfoDeleteServiceOutDto outDto) {

        // 2.1.設定区分を取得する取得
        boolean bunshoKbn = kghSmglHistoryLogic.getKghComEBunshoKbn();
        // レスポンスパラメータ.電子ファイル保存設定フラグ = 上記結果.電子ファイル保存設定フラグ
        outDto.setBunshoKbn(bunshoKbn ? CommonConstants.STR_TRUE : CommonConstants.STR_FALSE);

        // 2.2. レスポンスパラメータ.電子ファイル保存設定フラグ = trueの場合
        if (CommonConstants.STR_TRUE.equals(outDto.getBunshoKbn())) {
            // 介護保険者マスタ（４－３）を取得する。
            // INPUT情報設定
            KghCmnSimuObject1OpeByCriteriaInEntity criteria = new KghCmnSimuObject1OpeByCriteriaInEntity();
            // 保険者ID
            criteria.setLlKHokenCd(inDto.getCmnTucPlanKHokenCd());
            // 介護保険者マスタ（４－３）を取得する。
            List<KghCmnSimuObject1OpeOutEntity> kghCmnSimuObject1OpeList = comMscKaigoHokenjaSelectMapper
                    .findKghCmnSimuObject1OpeByCriteria(criteria);
            if (CollectionUtils.isNotEmpty(kghCmnSimuObject1OpeList)) {
                // レスポンスパラメータ.保険者番号 = 上記結果.保険者番号
                outDto.setKHokenNo(kghCmnSimuObject1OpeList.getFirst().getKHokenNo());
                // レスポンスパラメータ.保険者名称 = 上記結果.保険者名称
                outDto.setKHokenKnj(kghCmnSimuObject1OpeList.getFirst().getKHokenKnj());
            }
        }
    }

    /**
     * 削除処理
     * 
     * @param inDto  利用票画面情報削除処理の入力DTO
     * @param outDto 利用票画面情報削除処理の出力DTO
     * @throws Exception
     */
    private void deleteProc(UseSlipInfoDeleteServiceInDto inDto, UseSlipInfoDeleteServiceOutDto outDto)
            throws Exception {

        // 4.1.日付を変換する
        String yymmdd = nds3GkFunc01Logic.getCnvYymmdd(inDto.getTeikyouYm());
        // 【変数】.履歴ID = 上記結果.変換結果 + リクエストパラメータ.提供月（日）
        String rirekiId = StringUtils.join(yymmdd, inDto.getTeikyouYmD());

        // 4.2.メッセージ削除処理
        // サービス項目を取得
        // 共通TODO ln_out_core.uf_get_kkak_cp1
        KkakCp1OutDto kkakCp1OutDto = kghKhnPnr1Logic.getKkakCp1(Integer.parseInt(inDto.getUserId()),
                inDto.getShoriname(), rirekiId, CommonConstants.NUMBER_ZERO, CommonConstants.NUMBER_ZERO);
        // 【変数】.生成の判断結果コード= 上記結果.判断結果コード
        // 【変数】.履歴ID = 上記結果.履歴ID
        rirekiId = kkakCp1OutDto.getAsKkakId();
        // 【変数】.書類種別コード = 上記結果.書類種別コード
        // String asKkakCd = kkakCp1OutDto.getAsKkakCd();

        // 4.3.利用票・別表の削除前、利用票・別表その他データ検索
        Gui01149RiyouOtherData riyouOtherData = cmnTucPlanSelect(inDto.getSvJigyoId(), inDto.getUserId(),
                inDto.getTeikyouYm(), inDto.getTeikyouYmD());

        // 4.4.利用票・別表の削除処理を行う
        Gui01149TaihiData taihiData = inDto.getTaihiData().getFirst();
        // 利用票・別表データ削除用情報
        Gui01149RiyouOtherDataDelInfo delInfo = new Gui01149RiyouOtherDataDelInfo();
        // 利用者情報
        delInfo.setRiyoursha(CollectionUtils.isEmpty(taihiData.getRiyourshaList()) ? new Gui01149Riyoursha()
                : taihiData.getRiyourshaList().getFirst());
        // 利用票明細情報
        delInfo.setRiyou(CollectionUtils.isEmpty(taihiData.getRiyouList()) ? new Gui01149Riyou()
                : taihiData.getRiyouList().getFirst());
        // 別表明細情報
        delInfo.setRiyouBeppyo(CollectionUtils.isEmpty(taihiData.getRiyouBeppyoList()) ? new Gui01149RiyouBeppyo()
                : taihiData.getRiyouBeppyoList().getFirst());
        // 種類別限度情報
        delInfo.setSyuruiGendo(CollectionUtils.isEmpty(taihiData.getSyuruiGendoData()) ? new Gui01149SyuruiGendo()
                : taihiData.getSyuruiGendoData().getFirst());
        // 公費集計欄情報
        delInfo.setKohi(CollectionUtils.isEmpty(taihiData.getKohiList()) ? new Gui01149Kohi()
                : taihiData.getKohiList().getFirst());
        // 利用料集計欄情報
        delInfo.setRiyouryou(CollectionUtils.isEmpty(taihiData.getRiyouryouList()) ? new Gui01149Riyouryou()
                : taihiData.getRiyouryouList().getFirst());
        // 社福軽減集計欄情報
        delInfo.setSyafuku(CollectionUtils.isEmpty(taihiData.getSyafukuList()) ? new Gui01149Syafuku()
                : taihiData.getSyafukuList().getFirst());
        // 短期入所連続利用30日超過情報
        delInfo.setPlanOv30(CollectionUtils.isEmpty(taihiData.getPlanOv30List()) ? new Gui01149PlanOv30()
                : taihiData.getPlanOv30List().getFirst());
        // 短期利用日数保持情報
        delInfo.setSvplanShort(CollectionUtils.isEmpty(taihiData.getSvplanShortList()) ? new Gui01149SvplanShort()
                : taihiData.getSvplanShortList().getFirst());
        // 提供事業所毎小計情報
        delInfo.setServicePoint(CollectionUtils.isEmpty(taihiData.getServicePointList()) ? new Gui01149ServicePoint()
                : taihiData.getServicePointList().getFirst());
        // 介護予防短期入所利用状況情報
        delInfo.setTlcSvplanShort(
                CollectionUtils.isEmpty(taihiData.getTlcSvplanShortList()) ? new Gui01149TlcSvplanShort()
                        : taihiData.getTlcSvplanShortList().getFirst());
        // 算定確認情報
        delInfo.setRiyouKakunin(CollectionUtils.isEmpty(taihiData.getRiyouKakuninList()) ? new Gui01149RiyouKakunin()
                : taihiData.getRiyouKakuninList().getFirst());
        // サービス計画予定実績（19-1）
        delInfo.setKghCmnCpmvTlcSvplanResultDel(
                CollectionUtils.isEmpty(riyouOtherData.getKghCmnCpmvTlcSvplanResultDelList())
                        ? new KghCmnCpmvTlcSvplanResultDelOutEntity()
                        : riyouOtherData.getKghCmnCpmvTlcSvplanResultDelList().getFirst());
        // サービス別計画点数（19-2）
        delInfo.setKghCmnCpmvTlcSvplanPointDel(
                CollectionUtils.isEmpty(riyouOtherData.getKghCmnCpmvTlcSvplanPointDelList())
                        ? new KghCmnCpmvTlcSvplanPointDelOutEntity()
                        : riyouOtherData.getKghCmnCpmvTlcSvplanPointDelList().getFirst());
        // 様式Ｆ用予定単位数保存テーブル
        delInfo.setKghTrsFServicePointYDel(CollectionUtils.isEmpty(riyouOtherData.getKghTrsFServicePointYDelList())
                ? new KghTrsFServicePointYDelOutEntity()
                : riyouOtherData.getKghTrsFServicePointYDelList().getFirst());
        // 関数_利用票データの削除を行う
        deleteRiyouData(inDto.getSvJigyoId(), inDto.getUserId(), inDto.getTeikyouYm(), inDto.getTeikyouYmD(), delInfo);

        // 4.5.レスポンスパラメータ.削除処理結果 = 1
        outDto.setResult(CommonConstants.STR_1);
    }

    /**
     * 関数_利用票・別表その他データ検索
     * 
     * @param svJigyoId  事業所ＩＤ
     * @param userId     利用者ＩＤ
     * @param teikyouYm  提供年月
     * @param teikyouYmD 提供月（日）
     */
    private Gui01149RiyouOtherData cmnTucPlanSelect(String svJigyoId, String userId, String teikyouYm,
            String teikyouYmD) {

        // 2.返却情報初期化
        // 【返却】.利用票・別表データ情報
        Gui01149RiyouOtherData riyouOtherData = new Gui01149RiyouOtherData();

        // 3. サービス計画予定実績（19-1）(com_tlc_svplan_result)用、サービス計画予定実績（19-1）の取得について
        // INPUT情報設定
        KghCmnCpmvTlcSvplanResultDelByCriteriaInEntity resultCriteria = new KghCmnCpmvTlcSvplanResultDelByCriteriaInEntity();
        // 利用者ID
        resultCriteria.setAlUser(CommonDtoUtil.strValToInt(userId));
        // 処理年月
        resultCriteria.setAsYymm(StringUtils.join(teikyouYm, CommonConstants.STR_DELIMITER, teikyouYmD));
        // 支援事業者ID
        resultCriteria.setAlShien(CommonDtoUtil.strValToInt(svJigyoId));
        // 事業所ＩＤ、利用者ＩＤ、提供年月、提供年月日より、利用者情報を取得する。
        List<KghCmnCpmvTlcSvplanResultDelOutEntity> kghCmnCpmvTlcSvplanResultDelList = comTlcSvplanResultSelectMapper
                .findKghCmnCpmvTlcSvplanResultDelByCriteria(resultCriteria);
        riyouOtherData.setKghCmnCpmvTlcSvplanResultDelList(kghCmnCpmvTlcSvplanResultDelList);
        // 4. サービス別計画点数（19-2）(com_tlc_svplan_point)用、サービス別計画点数（19-2）の取得について
        // INPUT情報設定
        KghCmnCpmvTlcSvplanPointDelByCriteriaInEntity pointCriteria = new KghCmnCpmvTlcSvplanPointDelByCriteriaInEntity();
        // 利用者ID
        pointCriteria.setAlUser(CommonDtoUtil.strValToInt(userId));
        // 処理年月
        pointCriteria.setAsYymm(StringUtils.join(teikyouYm, CommonConstants.STR_DELIMITER, teikyouYmD));
        // 支援事業者ID
        pointCriteria.setAlShien(CommonDtoUtil.strValToInt(svJigyoId));
        // 事業所ＩＤ、利用者ＩＤ、提供年月、提供年月日より、利用者情報を取得する。
        List<KghCmnCpmvTlcSvplanPointDelOutEntity> kghCmnCpmvTlcSvplanPointDelList = comTlcSvplanPointSelectMapper
                .findKghCmnCpmvTlcSvplanPointDelByCriteria(pointCriteria);
        riyouOtherData.setKghCmnCpmvTlcSvplanPointDelList(kghCmnCpmvTlcSvplanPointDelList);
        // 5. 様式Ｆ用予定単位数保存テーブル(trs_tuc_service_point_y)用、様式Ｆ用予定単位数保存テーブルの取得について
        // INPUT情報設定
        KghTrsFServicePointYDelByCriteriaInEntity pointYCriteria = new KghTrsFServicePointYDelByCriteriaInEntity();
        // 利用者ID
        pointYCriteria.setAlUserid(CommonDtoUtil.strValToInt(userId));
        // 処理年月
        pointYCriteria.setAsYymmYm(StringUtils.join(teikyouYm, CommonConstants.STR_DELIMITER, teikyouYmD));
        // 支援事業者ID
        pointYCriteria.setAlShienId(CommonDtoUtil.strValToInt(svJigyoId));
        // 事業所ＩＤ、利用者ＩＤ、提供年月、提供年月日より、利用者情報を取得する。
        List<KghTrsFServicePointYDelOutEntity> kghTrsFServicePointYDelList = trsTucServicePointYSelectMapper
                .findKghTrsFServicePointYDelByCriteria(pointYCriteria);
        riyouOtherData.setKghTrsFServicePointYDelList(kghTrsFServicePointYDelList);

        // 6.返却情報を設定
        return riyouOtherData;
    }

    /**
     * 関数_利用票データの削除を行う
     * 
     * @param svJigyoId  事業所ＩＤ
     * @param userId     利用者ＩＤ
     * @param teikyouYm  提供年月
     * @param teikyouYmD 提供月（日）
     * @param delInfo    利用票・別表データ削除用情報
     * @throws Exception
     */
    private void deleteRiyouData(String svJigyoId, String userId, String teikyouYm, String teikyouYmD,
            Gui01149RiyouOtherDataDelInfo delInfo) throws Exception {

        // 2.利用票・別表の削除処理を行う
        // サービス利用票・別表ヘッダ(cmn_tuc_plan)の削除
        // 削除条件
        CmnTucPlanKey cmnTucPlanEntity = new CmnTucPlanKey();
        // 支援事業者ID=引き渡しパラメータ.事業所ＩＤ
        cmnTucPlanEntity.setShienId(CommonDtoUtil.strValToInt(svJigyoId));
        // 利用者ID=引き渡しパラメータ.利用者ID
        cmnTucPlanEntity.setUserid(CommonDtoUtil.strValToInt(userId));
        // サービス提供年月=引き渡しパラメータ.提供年月
        cmnTucPlanEntity.setYymmYm(teikyouYm);
        // サービス提供年月（変更日）=引き渡しパラメータ.提供月（日）
        cmnTucPlanEntity.setYymmD(teikyouYmD);
        int cmnTucPlanDelCnt = cmnTucPlanMapper.deleteByPrimaryKey(cmnTucPlanEntity);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (cmnTucPlanDelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }

        // サービス利用票明細(cmn_tuc_plan_riyou)の削除
        // 削除条件
        CmnTucPlanRiyouKey cmnTucPlanRiyouEntity = new CmnTucPlanRiyouKey();
        // 支援事業者ID=引き渡しパラメータ.事業所ＩＤ
        cmnTucPlanRiyouEntity.setShienId(CommonDtoUtil.strValToInt(svJigyoId));
        // 利用者ID=引き渡しパラメータ.利用者ID
        cmnTucPlanRiyouEntity.setUserid(CommonDtoUtil.strValToInt(userId));
        // サービス提供年月=引き渡しパラメータ.提供年月
        cmnTucPlanRiyouEntity.setYymmYm(teikyouYm);
        // サービス提供年月（変更日）=引き渡しパラメータ.提供月（日）
        cmnTucPlanRiyouEntity.setYymmD(teikyouYmD);
        // サービス事業者ID = 引き渡しパラメータ.利用票・別表データ削除用情報 利用票明細情報.サービス事業者ID
        cmnTucPlanRiyouEntity.setSvJigyoId(CommonDtoUtil.strValToInt(delInfo.getRiyou().getSvJigyoId()));
        // サービス項目ID = 引き渡しパラメータ.利用票・別表データ削除用情報 利用票明細情報.サービス項目ID
        cmnTucPlanRiyouEntity.setSvItemCd(CommonDtoUtil.strValToInt(delInfo.getRiyou().getSvItemCd()));
        // 枝番 = サービス項目ID = 引き渡しパラメータ.利用票・別表データ削除用情報 利用票明細情報.枝番
        cmnTucPlanRiyouEntity.setEdaNo(CommonDtoUtil.strValToInt(delInfo.getRiyou().getEdaNo()));
        int cmnTucPlanRiyouDelCnt = cmnTucPlanRiyouMapper.deleteByPrimaryKey(cmnTucPlanRiyouEntity);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (cmnTucPlanRiyouDelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }

        // 利用票別表 区分支給限度管理(cmn_tuc_plan_betu1)の削除
        // 削除条件
        CmnTucPlanBetu1Key cmnTucPlanBetu1Entity = new CmnTucPlanBetu1Key();
        // 支援事業者ID=引き渡しパラメータ.事業所ＩＤ
        cmnTucPlanBetu1Entity.setShienId(CommonDtoUtil.strValToInt(svJigyoId));
        // 利用者ID=引き渡しパラメータ.利用者ID
        cmnTucPlanBetu1Entity.setUserid(CommonDtoUtil.strValToInt(userId));
        // サービス提供年月=引き渡しパラメータ.提供年月
        cmnTucPlanBetu1Entity.setYymmYm(teikyouYm);
        // サービス提供年月（変更日）=引き渡しパラメータ.提供月（日）
        cmnTucPlanBetu1Entity.setYymmD(teikyouYmD);
        // サービス事業者ID = 引き渡しパラメータ.利用票・別表データ削除用情報 別表明細情報.サービス事業者ID
        cmnTucPlanBetu1Entity.setSvJigyoId(CommonDtoUtil.strValToInt(delInfo.getRiyouBeppyo().getSvJigyoId()));
        // サービス項目ID = 引き渡しパラメータ.利用票・別表データ削除用情報 別表明細情報.サービス項目ID
        cmnTucPlanBetu1Entity.setSvItemCd(CommonDtoUtil.strValToInt(delInfo.getRiyouBeppyo().getSvItemCd()));
        // 枝番 = サービス項目ID = 引き渡しパラメータ.利用票・別表データ削除用情報 別表明細情報.枝番
        cmnTucPlanBetu1Entity.setEdaNo(CommonDtoUtil.strValToInt(delInfo.getRiyouBeppyo().getEdaNo()));
        int cmnTucPlanBetu1DelCnt = cmnTucPlanBetu1Mapper.deleteByPrimaryKey(cmnTucPlanBetu1Entity);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (cmnTucPlanBetu1DelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }

        // 利用票別表 種類別限度管理(cmn_tuc_plan_betu2)の削除
        // 削除条件
        CmnTucPlanBetu2Key cmnTucPlanBetu2Entity = new CmnTucPlanBetu2Key();
        // 支援事業者ID=引き渡しパラメータ.事業所ＩＤ
        cmnTucPlanBetu2Entity.setShienId(CommonDtoUtil.strValToInt(svJigyoId));
        // 利用者ID=引き渡しパラメータ.利用者ID
        cmnTucPlanBetu2Entity.setUserid(CommonDtoUtil.strValToInt(userId));
        // サービス提供年月=引き渡しパラメータ.提供年月
        cmnTucPlanBetu2Entity.setYymmYm(teikyouYm);
        // サービス提供年月（変更日）=引き渡しパラメータ.提供月（日）
        cmnTucPlanBetu2Entity.setYymmD(teikyouYmD);
        int cmnTucPlanBetu2DelCnt = cmnTucPlanBetu2Mapper.deleteByPrimaryKey(cmnTucPlanBetu2Entity);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (cmnTucPlanBetu2DelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }

        // 利用票別表 公費負担(cmn_tuc_plan_kouhi_futan)の削除
        // 削除条件
        CmnTucPlanKouhiFutanKey cmnTucPlanKouhiFutanEntity = new CmnTucPlanKouhiFutanKey();
        // 支援事業者ID=引き渡しパラメータ.事業所ＩＤ
        cmnTucPlanKouhiFutanEntity.setShienId(CommonDtoUtil.strValToInt(svJigyoId));
        // 利用者ID=引き渡しパラメータ.利用者ID
        cmnTucPlanKouhiFutanEntity.setUserid(CommonDtoUtil.strValToInt(userId));
        // サービス提供年月=引き渡しパラメータ.提供年月
        cmnTucPlanKouhiFutanEntity.setYymmYm(teikyouYm);
        // サービス提供年月（変更日）=引き渡しパラメータ.提供月（日）
        cmnTucPlanKouhiFutanEntity.setYymmD(teikyouYmD);
        // 公費法制コード = 引き渡しパラメータ.利用票・別表データ削除用情報 別表明細情報.公費法制コード
        cmnTucPlanKouhiFutanEntity.setCode(delInfo.getKohi().getCode());
        // サービス事業者ID = 引き渡しパラメータ.利用票・別表データ削除用情報 別表明細情報.サービス事業者ID
        cmnTucPlanKouhiFutanEntity.setSvJigyoId(CommonDtoUtil.strValToInt(delInfo.getKohi().getSvJigyoId()));
        // サービス種類 = 引き渡しパラメータ.利用票・別表データ削除用情報 別表明細情報.サービス種類
        cmnTucPlanKouhiFutanEntity.setSvtype(delInfo.getKohi().getSvtype());
        int cmnTucPlanKouhiFutanDelCnt = cmnTucPlanKouhiFutanMapper.deleteByPrimaryKey(cmnTucPlanKouhiFutanEntity);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (cmnTucPlanKouhiFutanDelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }

        // 利用票別表 保険外負担(cmn_tuc_plan_gai_futan)の削除
        // 削除条件
        CmnTucPlanGaiFutanCriteria cmnTucPlanGaiFutanCriteria = new CmnTucPlanGaiFutanCriteria();
        cmnTucPlanGaiFutanCriteria.createCriteria()
                // 支援事業者ID=引き渡しパラメータ.事業所ＩＤ
                .andShienIdEqualTo(CommonDtoUtil.strValToInt(svJigyoId))
                // 利用者ID=引き渡しパラメータ.利用者ID
                .andUseridEqualTo(CommonDtoUtil.strValToInt(userId))
                // サービス提供年月=引き渡しパラメータ.提供年月
                .andYymmYmEqualTo(teikyouYm)
                // サービス提供年月（変更日）=引き渡しパラメータ.提供月（日）
                .andYymmDEqualTo(teikyouYmD);
        int cmnTucPlanGaiFutanDelCnt = cmnTucPlanGaiFutanMapper.deleteByCriteria(cmnTucPlanGaiFutanCriteria);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (cmnTucPlanGaiFutanDelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }

        // 利用票短期利用30日超過(cmn_tuc_plan_ov30)の削除
        // 削除条件
        CmnTucPlanOv30Key cmnTucPlanOv30Entity = new CmnTucPlanOv30Key();
        // 支援事業者ID=引き渡しパラメータ.事業所ＩＤ
        cmnTucPlanOv30Entity.setShienId(CommonDtoUtil.strValToInt(svJigyoId));
        // 利用者ID=引き渡しパラメータ.利用者ID
        cmnTucPlanOv30Entity.setUserid(CommonDtoUtil.strValToInt(userId));
        // サービス提供年月=引き渡しパラメータ.提供年月
        cmnTucPlanOv30Entity.setYymmYm(teikyouYm);
        // サービス提供年月（変更日）=引き渡しパラメータ.提供月（日）
        cmnTucPlanOv30Entity.setYymmD(teikyouYmD);
        // サービス事業者ID = 引き渡しパラメータ.利用票・別表データ削除用情報 短期入所連続利用30日超過情報.サービス事業者ID
        cmnTucPlanOv30Entity.setSvJigyoId(CommonDtoUtil.strValToInt(delInfo.getPlanOv30().getSvJigyoId()));
        // 30日超過該当日 = 引き渡しパラメータ.利用票・別表データ削除用情報 短期入所連続利用30日超過情報.30日超過該当日
        cmnTucPlanOv30Entity.setOv30Day(CommonDtoUtil.strValToInt(delInfo.getPlanOv30().getOv30Day()));
        int cmnTucPlanOv30DelCnt = cmnTucPlanOv30Mapper.deleteByPrimaryKey(cmnTucPlanOv30Entity);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (cmnTucPlanOv30DelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }

        // 利用票別表 社福軽減(cmn_tuc_plan_syafuku_keigen)の削除
        // 削除条件
        CmnTucPlanSyafukuKeigenKey cmnTucPlanSyafukuKeigenEntity = new CmnTucPlanSyafukuKeigenKey();
        // 支援事業者ID=引き渡しパラメータ.事業所ＩＤ
        cmnTucPlanSyafukuKeigenEntity.setShienId(CommonDtoUtil.strValToInt(svJigyoId));
        // 利用者ID=引き渡しパラメータ.利用者ID
        cmnTucPlanSyafukuKeigenEntity.setUserid(CommonDtoUtil.strValToInt(userId));
        // サービス提供年月=引き渡しパラメータ.提供年月
        cmnTucPlanSyafukuKeigenEntity.setYymmYm(teikyouYm);
        // サービス提供年月（変更日）=引き渡しパラメータ.提供月（日）
        cmnTucPlanSyafukuKeigenEntity.setYymmD(teikyouYmD);
        // サービス事業者ID = 引き渡しパラメータ.利用票・別表データ削除用情報 社福軽減集計欄情報.サービス事業者ID
        cmnTucPlanSyafukuKeigenEntity.setSvJigyoId(CommonDtoUtil.strValToInt(delInfo.getSyafuku().getSvJigyoId()));
        // サービス種類 =引き渡しパラメータ.利用票・別表データ削除用情報 社福軽減集計欄情報.サービス種類
        cmnTucPlanSyafukuKeigenEntity.setSvShuCd(delInfo.getSyafuku().getSyafukuSv());
        // 保険内外区分 =引き渡しパラメータ.利用票・別表データ削除用情報 社福軽減集計欄情報.保険内外区分
        cmnTucPlanSyafukuKeigenEntity.setKubun(CommonDtoUtil.strValToInt(delInfo.getSyafuku().getKubun()));
        // 作成区分 =引き渡しパラメータ.利用票・別表データ削除用情報 社福軽減集計欄情報.作成区分
        cmnTucPlanSyafukuKeigenEntity.setSakuKbn(CommonDtoUtil.strValToInt(delInfo.getSyafuku().getSakuKbn()));
        int cmnTucPlanSyafukuKeigenDelCnt = cmnTucPlanSyafukuKeigenMapper
                .deleteByPrimaryKey(cmnTucPlanSyafukuKeigenEntity);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (cmnTucPlanSyafukuKeigenDelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }

        // サービス利用票：算定確認（20110828）(cmn_tuc_plan_riyou_kakunin)の削除
        // 削除条件
        CmnTucPlanRiyouKakuninKey cmnTucPlanRiyouKakuninEntity = new CmnTucPlanRiyouKakuninKey();
        // 支援事業者ID=引き渡しパラメータ.事業所ＩＤ
        cmnTucPlanRiyouKakuninEntity.setShienId(CommonDtoUtil.strValToInt(svJigyoId));
        // 利用者ID=引き渡しパラメータ.利用者ID
        cmnTucPlanRiyouKakuninEntity.setUserid(CommonDtoUtil.strValToInt(userId));
        // サービス提供年月=引き渡しパラメータ.提供年月
        cmnTucPlanRiyouKakuninEntity.setYymmYm(teikyouYm);
        // サービス提供年月（変更日）=引き渡しパラメータ.提供月（日）
        cmnTucPlanRiyouKakuninEntity.setYymmD(teikyouYmD);
        // サービス事業者ID = 引き渡しパラメータ.利用票・別表データ削除用情報 算定確認情報.サービス事業者ID
        cmnTucPlanRiyouKakuninEntity.setSvJigyoId(CommonDtoUtil.strValToInt(delInfo.getRiyouKakunin().getSvJigyoId()));
        // サービス項目ID = 引き渡しパラメータ.利用票・別表データ削除用情報 算定確認情報.サービス項目ID
        cmnTucPlanRiyouKakuninEntity.setSvItemCd(CommonDtoUtil.strValToInt(delInfo.getRiyouKakunin().getSvItemCd()));
        // 枝番 = 引き渡しパラメータ.利用票・別表データ削除用情報 算定確認情報.枝番
        cmnTucPlanRiyouKakuninEntity.setEdaNo(CommonDtoUtil.strValToInt(delInfo.getRiyouKakunin().getEdaNo()));
        int cmnTucPlanRiyouKakuninDelCnt = cmnTucPlanRiyouKakuninMapper
                .deleteByPrimaryKey(cmnTucPlanRiyouKakuninEntity);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (cmnTucPlanRiyouKakuninDelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }

        // 利用票別表:提供事業所毎小計情報(cmn_tuc_service_point)の削除
        // 削除条件
        CmnTucServicePointKey cmnTucServicePointEntity = new CmnTucServicePointKey();
        // 利用者ID=引き渡しパラメータ.利用者ID
        cmnTucServicePointEntity.setUserid(CommonDtoUtil.strValToInt(userId));
        // 提供年月=引き渡しパラメータ.提供年月
        cmnTucServicePointEntity.setYymmYm(teikyouYm);
        // 法人ID = 引き渡しパラメータ.利用票・別表データ削除用情報 提供事業所毎小計情報.法人ID
        cmnTucServicePointEntity.setHoujinId(CommonDtoUtil.strValToInt(delInfo.getServicePoint().getHoujinId()));
        // 施設ID = 引き渡しパラメータ.利用票・別表データ削除用情報 提供事業所毎小計情報.施設ID
        cmnTucServicePointEntity.setShisetuId(CommonDtoUtil.strValToInt(delInfo.getServicePoint().getShisetuId()));
        // サービス事業者ID = 引き渡しパラメータ.利用票・別表データ削除用情報 提供事業所毎小計情報.サービス事業者ID
        cmnTucServicePointEntity.setSvJigyoId(CommonDtoUtil.strValToInt(delInfo.getServicePoint().getSvJigyoId()));
        // サービス種類コード = 引き渡しパラメータ.利用票・別表データ削除用情報 提供事業所毎小計情報.サービス種類コード
        cmnTucServicePointEntity.setSvtype(delInfo.getServicePoint().getSvtype());
        int cmnTucServicePointDelCnt = cmnTucServicePointMapper.deleteByPrimaryKey(cmnTucServicePointEntity);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (cmnTucServicePointDelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }

        // 短期利用日数保持(cmn_tuc_svplan_short)の削除
        // 削除条件
        CmnTucSvplanShortKey cmnTucSvplanShortEntity = new CmnTucSvplanShortKey();
        // 支援事業者ID=引き渡しパラメータ.事業所ＩＤ
        cmnTucSvplanShortEntity.setShienId(CommonDtoUtil.strValToInt(svJigyoId));
        // 利用者ID=引き渡しパラメータ.利用者ID
        cmnTucSvplanShortEntity.setUserid(CommonDtoUtil.strValToInt(userId));
        // 提供年月=引き渡しパラメータ.提供年月
        cmnTucSvplanShortEntity.setYymmYm(teikyouYm);
        // 3月途中保険者変更年月日=引き渡しパラメータ.提供月（日）
        cmnTucSvplanShortEntity.setHenkouYmd(teikyouYmD);
        int cmnTucSvplanShortDelCnt = cmnTucSvplanShortMapper.deleteByPrimaryKey(cmnTucSvplanShortEntity);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (cmnTucSvplanShortDelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }

        // サービス計画予定実績（19-1）(com_tlc_svplan_result)の削除
        // 削除条件
        ComTlcSvplanResultCriteria comTlcSvplanResultCriteria = new ComTlcSvplanResultCriteria();
        comTlcSvplanResultCriteria.createCriteria()
                // 支援事業者ID=引き渡しパラメータ.事業所ＩＤ
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(svJigyoId))
                // 利用者ID=引き渡しパラメータ.利用者ID
                .andUseridEqualTo(CommonDtoUtil.strValToInt(userId))
                // 処理年月=引き渡しパラメータ.提供年月
                .andYymmYmdEqualTo(teikyouYm);
        int comTlcSvplanResultDelCnt = comTlcSvplanResultMapper.deleteByCriteria(comTlcSvplanResultCriteria);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (comTlcSvplanResultDelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }

        // サービス別計画点数（19-2）(com_tlc_svplan_point)の削除
        // 削除条件
        ComTlcSvplanPointCriteria comTlcSvplanPointCriteria = new ComTlcSvplanPointCriteria();
        comTlcSvplanPointCriteria.createCriteria()
                // 支援事業者ID=引き渡しパラメータ.事業所ＩＤ
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(svJigyoId))
                // 利用者ID=引き渡しパラメータ.利用者ID
                .andUseridEqualTo(CommonDtoUtil.strValToInt(userId))
                // 処理年月=引き渡しパラメータ.提供年月
                .andYymmYmdEqualTo(teikyouYm);
        int comTlcSvplanPointDelCnt = comTlcSvplanPointMapper.deleteByCriteria(comTlcSvplanPointCriteria);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (comTlcSvplanPointDelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }

        // 介護予防短期入所利用状況(com_tlc_svplan_short)の削除
        // 削除条件
        ComTlcSvplanShortKey comTlcSvplanShortEntity = new ComTlcSvplanShortKey();
        // 利用者ID=引き渡しパラメータ.利用者ID
        comTlcSvplanShortEntity.setUserid(CommonDtoUtil.strValToInt(userId));
        // 処理年月 = 引き渡しパラメータ.提供年月 + "/" + 引き渡しパラメータ.提供月（日） ※yyyy/MM/dd
        comTlcSvplanShortEntity.setYymmYmd(StringUtils.join(teikyouYm, CommonConstants.STR_DELIMITER, teikyouYmD));
        int comTlcSvplanShortDelCnt = comTlcSvplanShortMapper.deleteByPrimaryKey(comTlcSvplanShortEntity);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (comTlcSvplanShortDelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }

        // 様式Ｆ用予定単位数保存テーブル(trs_tuc_service_point_y)の削除
        // 削除条件
        TrsTucServicePointYCriteria trsTucServicePointYCriteria = new TrsTucServicePointYCriteria();
        trsTucServicePointYCriteria.createCriteria()
                // 支援事業者ID=引き渡しパラメータ.事業所ＩＤ
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(svJigyoId))
                // 利用者ID=引き渡しパラメータ.利用者ID
                .andUseridEqualTo(CommonDtoUtil.strValToInt(userId))
                // 処理年月=引き渡しパラメータ.提供年月
                .andYymmYmEqualTo(teikyouYm);
        int trsTucServicePointYDelCnt = trsTucServicePointYMapper.deleteByCriteria(trsTucServicePointYCriteria);
        // modifiedCntを用いた更新処理で更新結果が0件であれば、排他エラーとしてExclusiveExceptionを利用する。
        if (trsTucServicePointYDelCnt <= 0) {
            // 更新失敗
            throw new ExclusiveException();
        }
    }
}
