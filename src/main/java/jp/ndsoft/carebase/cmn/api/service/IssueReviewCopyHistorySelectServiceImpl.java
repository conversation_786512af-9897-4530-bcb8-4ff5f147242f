package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jp.ndsoft.carebase.cmn.api.service.dto.Gui00654KknInfoOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.IssueReviewCopyHistorySelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.IssueReviewCopyHistorySelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucMygKky1InfoFilterMstKbnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucMygKky1InfoFilterMstKbnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkComKikanSvjArrayByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkComKikanSvjArrayOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucMygKky1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KikanSentakuSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025/06/23
 * <AUTHOR> TRAN LANG KHOA
 * @implNote GUI00654_課題検討複写 画面
 */
@Service
public class IssueReviewCopyHistorySelectServiceImpl
        extends
        SelectServiceImpl<IssueReviewCopyHistorySelectServiceInDto, IssueReviewCopyHistorySelectServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private KikanSentakuSelectMapper kikanSentakuSelectMapper;

    @Autowired
    private CpnTucMygKky1SelectMapper cpnTucMygKky1SelectMapper;

    /**
     * ［要因取込］画面
     * 
     * @param inDto ［要因取込］画面の入力DTO.
     * @return ［要因取込］画面の出力DTO
     * @throws Exception Exception
     */
    @Override
    protected IssueReviewCopyHistorySelectServiceOutDto mainProcess(IssueReviewCopyHistorySelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        // DAOパラメータを作成
        IssueReviewCopyHistorySelectServiceOutDto outDto = new IssueReviewCopyHistorySelectServiceOutDto();
        List<Gui00654KknInfoOutDto> kknInfoList = new ArrayList<>();
        /*
         * ===============2. リクエストパラメータ.イベント = 1の場合、下記処理を行う===============
         * 
         */
        // 2.1. 下記のDAOを利用し、計画期間情報を取得する。
        // 2.2. 「2.1.」で取得した計画期間情報の件数を計画期間情報.期間総件数に設定する。
        KrkComKikanSvjArrayByCriteriaInEntity krkComKikanSvjArrayByCriteriaInEntity = new KrkComKikanSvjArrayByCriteriaInEntity();
        krkComKikanSvjArrayByCriteriaInEntity.setSvJigyoIdList(
                inDto.getSvJigyoIdList().stream()
                        .map(CommonDtoUtil::strValToInt)
                        .toList());
        krkComKikanSvjArrayByCriteriaInEntity.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        krkComKikanSvjArrayByCriteriaInEntity.setSyubetsuId(CommonDtoUtil.strValToInt(inDto.getSyubetsuId()));
        krkComKikanSvjArrayByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        List<KrkComKikanSvjArrayOutEntity> krkComKikanSvjList = kikanSentakuSelectMapper
                .findKrkComKikanSvjArrayByCriteria(krkComKikanSvjArrayByCriteriaInEntity);

        if (!CollectionUtils.isEmpty(krkComKikanSvjList)) {
            int totalCount = krkComKikanSvjList.size();
            for (KrkComKikanSvjArrayOutEntity krkComKikanSvj : krkComKikanSvjList) {
                Gui00654KknInfoOutDto kknInfo = new Gui00654KknInfoOutDto();
                // 期間ID
                kknInfo.setSc1Id(CommonDtoUtil.objValToString(krkComKikanSvj.getSc1Id()));
                // 期間総件数
                kknInfo.setPeriodCnt(CommonDtoUtil.objValToString(totalCount));
                // 開始日
                kknInfo.setStartYmd(krkComKikanSvj.getStartYmd());
                // 終了日
                kknInfo.setEndYmd(krkComKikanSvj.getEndYmd());

                kknInfo.setJigyoRyakuKnj(krkComKikanSvj.getJigyoRyakuKnj());

                kknInfoList.add(kknInfo);
            }
        }

        /*
         * ===============3. 取得した記録共通期間毎に、期間内履歴数を取得する。===============
         * 
         */
        if (!CollectionUtils.isEmpty(kknInfoList)) {
            for (Gui00654KknInfoOutDto kknInfo : kknInfoList) {
                for (String svJigyoIdStr : inDto.getSvJigyoIdList()) {
                    CpnTucMygKky1InfoFilterMstKbnByCriteriaInEntity cpnTucMygKky1InfoFilterMstKbnByCriteriaInEntity = new CpnTucMygKky1InfoFilterMstKbnByCriteriaInEntity();
                    cpnTucMygKky1InfoFilterMstKbnByCriteriaInEntity
                            .setUId(CommonDtoUtil.strValToInt(inDto.getUserId()));
                    cpnTucMygKky1InfoFilterMstKbnByCriteriaInEntity
                            .setJId(CommonDtoUtil.strValToInt(svJigyoIdStr));
                    cpnTucMygKky1InfoFilterMstKbnByCriteriaInEntity
                            .setMstKbn(CommonDtoUtil.strValToInt(inDto.getMstKbn()));

                    List<CpnTucMygKky1InfoFilterMstKbnOutEntity> cpnTucMygKky1InfoList = cpnTucMygKky1SelectMapper
                            .findCpnTucMygKky1InfoFilterMstKbnByCriteria(
                                    cpnTucMygKky1InfoFilterMstKbnByCriteriaInEntity);

                    kknInfo.setHistoryCnt(CommonDtoUtil.objValToString(cpnTucMygKky1InfoList.size()));
                }
            }
        }

        /*
         * ===============4. レスポンスを返却する。===============
         * 
         */
        LOG.info(Constants.END);
        outDto.setKknInfo(kknInfoList);
        return outDto;
    }
}
