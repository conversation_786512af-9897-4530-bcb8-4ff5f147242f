package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01272TokkiSave;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @apiNote GUI01272_特記事項情報を保存サービス入力Dto
 */
@Getter
@Setter
public class CertificationSurveySpecialMatterUpdateServiceInDto extends IDtoImpl {
	/** serialVersionUID. */
	private static final long serialVersionUID = 1L;

	/** 計画期間ID */
	private String scId;

	/** 調査票ID */
	private String cschId;

	/** 特記事項リスト */
	private List<Gui01272TokkiSave> tokkiList;

}
