package jp.ndsoft.carebase.cmn.api.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.DailyRoutinePlanReportServiceDbNoSaveData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.DailyRoutinePlanReportServicePrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.DailyRoutinePlanReportServicePrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Plan1kReportServiceDbNoSaveData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServicePrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonShiTeiPrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportSvcR34PrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ShuukanServiceReportCommonPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.V00241UserId;
import jp.ndsoft.carebase.cmn.api.report.dto.DailyRoutinePlanReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.DailyRoutinePlanReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.KyotakuServiceReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.KyotakuServiceReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.Plan1kReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.Plan1sReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ReportCommonGetOptionDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ServiceRiteiReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.dto.ServiceUseAnnexedTableReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ShisetsuServiceReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ShisetsuServiceReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ShuukanServiceR34ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ShuukanServiceReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.TeikyohyoReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.logic.DailyRoutinePlanReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.KyotakuServiceReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.Plan1kReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.Plan1sReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.ServiceRiteiReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.ServiceUseAnnexedTableReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.ShisetsuServiceReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.ShuukanServiceR34ReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.ShuukanServiceReportLogic;
import jp.ndsoft.carebase.cmn.api.report.model.DailyRoutinePlanReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.KyotakuServiceReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.Plan1kReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.ServiceUseAnnexedTableReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.ShisetsuServiceReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.ShuukanServiceR34ReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.ShuukanServiceReportParameterModel;
import jp.ndsoft.carebase.cmn.api.service.dto.KghMocKrkSsmMasterInfoDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Plan1InitMasterData;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanAllPrintReportDataSelectInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanAllPrintReportDataSelectOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import jp.ndsoft.smh.framework.util.AppUtil;

/**
 * 「GUI04514_印刷が終了しました（ケアマネ）」
 * 「U00891_計画書一括印刷」用の帳票データ取得
 *
 * <AUTHOR>
 */
@Service
public class PlanAllPrintReportDataSelectServiceImpl
        extends SelectServiceImpl<PlanAllPrintReportDataSelectInDto, PlanAllPrintReportDataSelectOutDto> {

    /** 居宅サービス計画書（１） */
    private static final String PLAN_1K_REPORT = "Plan1kReport";

    /** 施設サービス計画書（１） */
    private static final String PLAN_1S_REPORT = "Plan1sReport";

    /** 施設サービス計画書（２） */
    private static final String SHISETSU_REPORT = "shisetsuServiceReport";

    /** 居宅サービス計画書（２） */
    private static final String KYOTAKU_REPORT = "kyotakuServiceReport";

    /** 週間サービス計画表 */
    private static final String SHUUKAN_REPORT = "shuukanServiceReport";

    /** 週間サービス計画表 */
    private static final String SHUUKAN_REPORT_R34 = "ShuukanServiceR34Report";

    /** 日課計画表出力 */
    private static final String DAILY_ROUTINE_PLAN_REPORT = "DailyRoutinePlanReport";

    /** サービス利用票 */
    private static final String SERVICE_RITEI_REPORT = "ServiceRiteiReport";

    /** サービス利用票別表 */
    private static final String SERVICE_USE_ANNEXED_TABLE_REPORT = "ServiceUseAnnexedTableReport";

    /** 印鑑欄を表示するフラグ */
    private static final String INKANFLG_TRUE = "true";

    /** U0081K_居宅サービス計画書（１） 帳票出力 ロジッククラス */
    @Autowired
    private Plan1kReportLogic plan1kReportLogic;

    /** U0081S_施設サービス計画書（１） 帳票出力 ロジッククラス */
    @Autowired
    private Plan1sReportLogic plan1sReportLogic;

    /** 施設サービス計画書（２）帳票出力ロジック */
    @Autowired
    private ShisetsuServiceReportLogic shisetsuServiceReportLogic;

    /** アセスメント(居宅) 帳票出力 ロジッククラス */
    @Autowired
    private KyotakuServiceReportLogic kyotakuServiceReportLogic;

    /** 情報収集シート帳票出力ロジック */
    @Autowired
    private ShuukanServiceR34ReportLogic shuukanSvcRptLogic;

    /** U00861_日課計画表出力 ロジッククラス */
    @Autowired
    private DailyRoutinePlanReportLogic dailyRoutinePlanReportLogic;

    /** サービス利用票 帳票出力ロジック */
    @Autowired
    private ServiceRiteiReportLogic serviceRiteiReportLogic;

    /** サービス利用票別表 帳票出力ロジック */
    @Autowired
    ServiceUseAnnexedTableReportLogic serviceUseAnnexedTableReportLogic;

    /** 週間サービス計画表帳票出力ロジック */
    @Autowired
    private ShuukanServiceReportLogic shuukanServiceReportLogic;

    /** 印刷オプション */
    private ReportCommonGetOptionDto usePrintOption;

    /**
     * 帳票データ取得
     * 
     * @param inDto 「U00891_計画書一括印刷」用の帳票データ取得の入力DTO.
     * @return 「U00891_計画書一括印刷」用の帳票データ取得の出力DTO
     * @throws Exception Exception
     */
    @Override
    protected PlanAllPrintReportDataSelectOutDto mainProcess(
            PlanAllPrintReportDataSelectInDto inDto)
            throws Exception {
        inDto.setKeiyakushaId(AppUtil.getKeiyakushaId());
        // 戻り値のDTOを初期化
        PlanAllPrintReportDataSelectOutDto outDto = new PlanAllPrintReportDataSelectOutDto();
        switch (inDto.getReportId()) {
            // 居宅サービス計画書（１）帳票
            case PLAN_1K_REPORT:
                Plan1kReportParameterModel plan1kParameterModel = new Plan1kReportParameterModel();
                // 帳票ID
                plan1kParameterModel.setReportId(inDto.getReportId());
                // 契約者ID
                plan1kParameterModel.setKeiyakushaId(inDto.getKeiyakushaId());
                // システム日付
                plan1kParameterModel.setSystemDate(inDto.getSystemDate());
                // 事業者情報
                plan1kParameterModel.setJigyoInfo(inDto.getJigyoInfo());
                // 事業所名
                plan1kParameterModel.setJigyoKnj(inDto.getJigyoKnj());
                // システム日付（アプリ用）
                plan1kParameterModel.setAppYmd(inDto.getAppYmd());
                // 初期設定マスタの情報
                Plan1InitMasterData plan1KInitMasterObj = new Plan1InitMasterData();
                // ケアプラン方式
                plan1KInitMasterObj.setCpnFlg(inDto.getInitMasterObj().getCpnFlg());
                // 敬称オプション
                plan1KInitMasterObj.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                // 敬称
                plan1KInitMasterObj.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
                // 計画書書式
                plan1KInitMasterObj.setShosikiFlg(inDto.getInitMasterObj().getShosikiFlg());
                // 計画書様式
                plan1KInitMasterObj.setCksFlg(inDto.getInitMasterObj().getCksFlg());
                // 担当者（計画書（１））
                plan1KInitMasterObj.setCks1TantoFlg(inDto.getInitMasterObj().getCks1TantoFlg());
                // 要介護度（計画書（１））
                plan1KInitMasterObj.setCks1YokaiFlg(inDto.getInitMasterObj().getCks1YokaiFlg());
                // 印刷時の文字サイズ（計画書（１））
                plan1KInitMasterObj.setCks1PrtSizeFlg(inDto.getInitMasterObj().getCks1PrtSizeFlg());
                // 承認欄情報
                plan1KInitMasterObj.setShoninFlg(inDto.getInitMasterObj().getShoninFlg());
                // メッセージ表示
                plan1KInitMasterObj.setMsgFlg(inDto.getInitMasterObj().getMsgFlg());
                plan1kParameterModel.setInitMasterObj(plan1KInitMasterObj);
                // 印刷設定
                plan1kParameterModel.setPrintSet(inDto.getPrintSet());
                // DB未保存画面項目
                Plan1kReportServiceDbNoSaveData plan1kDbNoSaveData = new Plan1kReportServiceDbNoSaveData();
                // 指定日
                plan1kDbNoSaveData.setSelectDate(inDto.getDbNoSaveData().getSelectDate());
                // 記入用シートを印刷するフラグ
                plan1kDbNoSaveData.setEmptyFlg(inDto.getDbNoSaveData().getEmptyFlg());
                // システムコード
                plan1kDbNoSaveData.setSysCd(inDto.getDbNoSaveData().getSysCd());
                // 職員Id
                plan1kDbNoSaveData.setShokuinId(inDto.getDbNoSaveData().getShokuinId());
                plan1kParameterModel.setDbNoSaveData(plan1kDbNoSaveData);
                // 印刷対象履歴
                PrintReportServicePrintSubjectHistory plan1kSubjectHistory = new PrintReportServicePrintSubjectHistory();
                // 利用者ID
                plan1kSubjectHistory.setUserId(inDto.getPrintSubjectHistory().getUserId());
                // 履歴ID
                plan1kSubjectHistory.setRirekiId(inDto.getPrintSubjectHistory().getRirekiId());
                plan1kParameterModel.setPrintSubjectHistory(plan1kSubjectHistory);
                // 帳票用データ詳細
                Plan1kReportServiceInDto plan1kModel = plan1kReportLogic
                        .getU0081KReportParameters(plan1kParameterModel);
                outDto.setData1(plan1kModel);
                break;
            // 施設サービス計画書（１）
            case PLAN_1S_REPORT:
                Plan1kReportParameterModel plan1sParameterModel = new Plan1kReportParameterModel();
                // 帳票ID
                plan1sParameterModel.setReportId(inDto.getReportId());
                // 契約者ID
                plan1sParameterModel.setKeiyakushaId(inDto.getKeiyakushaId());
                // システム日付
                plan1sParameterModel.setSystemDate(inDto.getSystemDate());
                // 事業者情報
                plan1sParameterModel.setJigyoInfo(inDto.getJigyoInfo());
                // 事業所名
                plan1sParameterModel.setJigyoKnj(inDto.getJigyoKnj());
                // システム日付（アプリ用）
                plan1sParameterModel.setAppYmd(inDto.getAppYmd());
                // 初期設定マスタの情報
                Plan1InitMasterData plan1sInitMasterObj = new Plan1InitMasterData();
                // ケアプラン方式
                plan1sInitMasterObj.setCpnFlg(inDto.getInitMasterObj().getCpnFlg());
                // 敬称オプション
                plan1sInitMasterObj.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                // 敬称
                plan1sInitMasterObj.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
                // 計画書書式
                plan1sInitMasterObj.setShosikiFlg(inDto.getInitMasterObj().getShosikiFlg());
                // 計画書様式
                plan1sInitMasterObj.setCksFlg(inDto.getInitMasterObj().getCksFlg());
                // 担当者（計画書（１））
                plan1sInitMasterObj.setCks1TantoFlg(inDto.getInitMasterObj().getCks1TantoFlg());
                // 要介護度（計画書（１））
                plan1sInitMasterObj.setCks1YokaiFlg(inDto.getInitMasterObj().getCks1YokaiFlg());
                // 印刷時の文字サイズ（計画書（１））
                plan1sInitMasterObj.setCks1PrtSizeFlg(inDto.getInitMasterObj().getCks1PrtSizeFlg());
                // 承認欄情報
                plan1sInitMasterObj.setShoninFlg(inDto.getInitMasterObj().getShoninFlg());
                // メッセージ表示
                plan1sInitMasterObj.setMsgFlg(inDto.getInitMasterObj().getMsgFlg());
                plan1sParameterModel.setInitMasterObj(plan1sInitMasterObj);
                // 印刷設定
                plan1sParameterModel.setPrintSet(inDto.getPrintSet());
                // DB未保存画面項目
                Plan1kReportServiceDbNoSaveData plan1sDbNoSaveData = new Plan1kReportServiceDbNoSaveData();
                // 指定日
                plan1sDbNoSaveData.setSelectDate(inDto.getDbNoSaveData().getSelectDate());
                // 記入用シートを印刷するフラグ
                plan1sDbNoSaveData.setEmptyFlg(inDto.getDbNoSaveData().getEmptyFlg());
                // システムコード
                plan1sDbNoSaveData.setSysCd(inDto.getDbNoSaveData().getSysCd());
                // 職員Id
                plan1sDbNoSaveData.setShokuinId(inDto.getDbNoSaveData().getShokuinId());
                plan1sParameterModel.setDbNoSaveData(plan1sDbNoSaveData);
                // 印刷対象履歴
                PrintReportServicePrintSubjectHistory plan1sSubjectHistory = new PrintReportServicePrintSubjectHistory();
                // 利用者ID
                plan1sSubjectHistory.setUserId(inDto.getPrintSubjectHistory().getUserId());
                // 履歴ID
                plan1sSubjectHistory.setRirekiId(inDto.getPrintSubjectHistory().getRirekiId());
                plan1sParameterModel.setPrintSubjectHistory(plan1sSubjectHistory);
                Plan1sReportServiceInDto plan1sModel = plan1sReportLogic.getU0081SReportParameters(
                        plan1sParameterModel);
                outDto.setData2(plan1sModel);
                break;
            // 施設サービス計画書（２）
            case SHISETSU_REPORT:
                ShisetsuServiceReportParameterModel plan2sParametermodel = new ShisetsuServiceReportParameterModel();
                ShisetsuServiceReportServiceOutDto plan2sOutDto = new ShisetsuServiceReportServiceOutDto();
                // 帳票ID
                plan2sParametermodel.setReportId(inDto.getReportId());
                // 契約者ID
                plan2sParametermodel.setKeiyakushaId(inDto.getKeiyakushaId());
                // システム日付
                plan2sParametermodel.setSystemDate(inDto.getAppYmd());
                // 事業者情報
                plan2sParametermodel.setJigyoInfo(inDto.getJigyoInfo());
                // 計画期間ID
                plan2sParametermodel.setSc1Id(inDto.getPrintSubjectHistory().getSc1Id());
                // 計画書ID
                plan2sParametermodel.setKs11Id(inDto.getPrintSubjectHistory().getRirekiId());
                // 施設ID
                plan2sParametermodel.setShisetuId(inDto.getJigyoInfo().getShisetuId());
                // 法人ID
                plan2sParametermodel.setHoujinId(inDto.getJigyoInfo().getHoujinId());
                // 利用者ID
                plan2sParametermodel.setUserId(inDto.getPrintSubjectHistory().getUserId());
                // タイトル
                plan2sParametermodel.setTitle(inDto.getPrintSet().getPrtTitle());
                // 印鑑欄を表示するフラグ
                plan2sParametermodel.setInkanFlg(INKANFLG_TRUE);
                // 伏字印刷区分
                plan2sParametermodel.setFuseji(inDto.getFuseji());
                // 指定日印刷区分
                plan2sParametermodel.setShiTeiKubun(inDto.getPrintSet().getPrnDate());
                // 指定日
                plan2sParametermodel.setShiTeiDate(inDto.getDbNoSaveData().getSelectDate());
                // システム日付
                plan2sParametermodel.setSystemDate(inDto.getAppYmd());
                // 作成日印刷区分
                plan2sParametermodel.setSaKuSeiKubun(inDto.getPrintSet().getParam13());
                // 作成年月日
                plan2sParametermodel.setSaKuSeiYmd("");
                // 敬称変更フラグ
                plan2sParametermodel.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                // 敬称
                plan2sParametermodel.setKeisho(inDto.getInitMasterObj().getKeishoKnj());
                // 先頭フラグ
                plan2sParametermodel.setSentouFlg(inDto.getPrintSet().getParam10());
                // 記入用シートを印刷するフラグ
                plan2sParametermodel.setEmptyFlg(inDto.getDbNoSaveData().getEmptyFlg());
                // 要介護度フラグ
                plan2sParametermodel.setYokaiFlg(inDto.getPrintSet().getParam09());
                // 印刷枠の高さを自動調整フラグ
                plan2sParametermodel.setHeightAutoFlg(inDto.getPrintSet().getParam06());
                // 事業名印刷フラグ
                plan2sParametermodel.setJigyoFlg(inDto.getPrintSet().getParam30());
                // 事業名略称印刷フラグ
                plan2sParametermodel.setJigyoRyakuKnjFlg(inDto.getPrintSet().getParam31());
                // 承認欄を印刷するフラグ
                plan2sParametermodel.setShoninFlg(inDto.getPrintSet().getParam05());
                // セクション
                plan2sParametermodel.setSection(inDto.getPrintSet().getSectionNo());
                // システムコード
                plan2sParametermodel.setSyscd(inDto.getDbNoSaveData().getSysCd());
                // 印刷オプション
                plan2sParametermodel.setLiJoken(inDto.getPrintSet().getParam09());
                // 職員ID
                plan2sParametermodel.setShokuId(inDto.getDbNoSaveData().getShokuinId());
                // 事業所ID
                plan2sParametermodel.setSvJigyoId(inDto.getJigyoInfo().getSvJigyoId());
                // 帳票用データ詳細
                ShisetsuServiceReportServiceInDto plan2sModel = shisetsuServiceReportLogic
                        .getShisetsuServiceReportParameters(plan2sParametermodel, plan2sOutDto, getFwProps());
                outDto.setData3(plan2sModel);
                break;
            // 居宅サービス計画書（２）
            case KYOTAKU_REPORT:
                KyotakuServiceReportParameterModel plan2kParametermodel = new KyotakuServiceReportParameterModel();
                // 帳票ID
                plan2kParametermodel.setReportId(inDto.getReportId());
                // 契約者ID
                plan2kParametermodel.setKeiyakushaId(inDto.getKeiyakushaId());
                // 事業者情報
                plan2kParametermodel.setJigyoInfo(inDto.getJigyoInfo());
                // 計画期間ID
                plan2kParametermodel.setSc1Id(inDto.getPrintSubjectHistory().getSc1Id());
                // 計画書ID
                plan2kParametermodel.setKs11Id(inDto.getPrintSubjectHistory().getRirekiId());
                // 施設ID
                plan2kParametermodel.setShisetuId(inDto.getJigyoInfo().getShisetuId());
                // 法人ID
                plan2kParametermodel.setHoujinId(inDto.getJigyoInfo().getHoujinId());
                // 利用者ID
                plan2kParametermodel.setUserId(inDto.getPrintSubjectHistory().getUserId());
                // タイトル
                plan2kParametermodel.setTitle(inDto.getPrintSet().getPrtTitle());
                // 印鑑欄を表示するフラグ
                plan2kParametermodel.setInkanFlg(INKANFLG_TRUE);
                // 伏字印刷区分
                plan2kParametermodel.setFuseji(inDto.getFuseji());
                // 指定日印刷区分
                plan2kParametermodel.setShiTeiKubun(inDto.getPrintSet().getPrnDate());
                // 指定日
                plan2kParametermodel.setShiTeiDate(inDto.getDbNoSaveData().getSelectDate());
                // システム日付
                plan2kParametermodel.setSystemDate(inDto.getAppYmd());
                // 作成日印刷区分
                plan2kParametermodel.setSaKuSeiKubun(inDto.getPrintSet().getParam13());
                // 作成年月日
                plan2kParametermodel.setSaKuSeiYmd("");
                // 敬称変更フラグ
                plan2kParametermodel.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                // 敬称
                plan2kParametermodel.setKeisho(inDto.getInitMasterObj().getKeishoKnj());
                // 先頭フラグ
                plan2kParametermodel.setSentouFlg(inDto.getPrintSet().getParam10());
                // 記入用シートを印刷するフラグ
                plan2kParametermodel.setEmptyFlg(inDto.getDbNoSaveData().getEmptyFlg());
                // 要介護度フラグ
                plan2kParametermodel.setYokaiFlg(inDto.getPrintSet().getParam09());
                // 印刷枠の高さを自動調整フラグ
                plan2kParametermodel.setHeightAutoFlg(inDto.getPrintSet().getParam06());
                // 事業名印刷フラグ
                plan2kParametermodel.setJigyoFlg(inDto.getPrintSet().getParam30());
                // 事業名略称印刷フラグ
                plan2kParametermodel.setJigyoRyakuKnjFlg(inDto.getPrintSet().getParam31());
                // 承認欄を印刷するフラグ
                plan2kParametermodel.setShoninFlg(inDto.getPrintSet().getParam05());
                // セクション
                plan2kParametermodel.setSection(inDto.getPrintSet().getSectionNo());
                // システムコード
                plan2kParametermodel.setSyscd(inDto.getDbNoSaveData().getSysCd());
                // 印刷オプション
                plan2kParametermodel.setLiJoken(inDto.getPrintSet().getParam09());
                // 職員ID
                plan2kParametermodel.setShokuId(inDto.getDbNoSaveData().getShokuinId());
                // 事業所ID
                plan2kParametermodel.setSvJigyoId(inDto.getJigyoInfo().getSvJigyoId());
                KyotakuServiceReportServiceOutDto plan2kOutDto = new KyotakuServiceReportServiceOutDto();
                // 帳票用データ詳細
                KyotakuServiceReportServiceInDto plan2kModel = this.kyotakuServiceReportLogic
                        .getKyotakuServiceReportParameters(plan2kParametermodel, plan2kOutDto, getFwProps());
                outDto.setData4(plan2kModel);
                break;
            // 週間サービス計画表
            case SHUUKAN_REPORT:
                ShuukanServiceReportParameterModel shuukanModel = new ShuukanServiceReportParameterModel();
                // 帳票ID
                shuukanModel.setReportId(inDto.getReportId());
                // 契約者ID
                shuukanModel.setKeiyakushaId(inDto.getKeiyakushaId());
                // システム日付
                shuukanModel.setSystemDate(inDto.getAppYmd());
                // 事業者情報
                shuukanModel.setJigyoInfo(inDto.getJigyoInfo());
                // システムコード
                shuukanModel.setSyscd(inDto.getDbNoSaveData().getSysCd());
                // 計画期間ID
                shuukanModel.setSc1Id(inDto.getPrintSubjectHistory().getSc1Id());
                // 施設ID
                shuukanModel.setShisetuId(inDto.getJigyoInfo().getShisetuId());
                // 法人ID
                shuukanModel.setHoujinId(inDto.getJigyoInfo().getHoujinId());
                // 事業所ID
                shuukanModel.setSvJigyoId(inDto.getJigyoInfo().getSvJigyoId());
                // 履歴ID
                shuukanModel.setRirekiId(inDto.getPrintSubjectHistory().getRirekiId());
                // 利用者ID
                shuukanModel.setUserId(inDto.getPrintSubjectHistory().getUserId());
                // 職員ID
                shuukanModel.setShokuinId(inDto.getDbNoSaveData().getShokuinId());
                // 印鑑欄を表示するフラグ
                shuukanModel.setInkanFlg(INKANFLG_TRUE);
                // 指定日印刷設定
                ReportCommonPrintSet shiTeiPrintSet = new ReportCommonPrintSet();
                // 指定日印刷区分
                shiTeiPrintSet.setShiTeiKubun(inDto.getPrintSet().getPrnDate());
                // 指定日
                shiTeiPrintSet.setShiTeiDate(inDto.getDbNoSaveData().getSelectDate());
                shuukanModel.setShiTeiPrintSet(shiTeiPrintSet);
                // 作成日印刷設定
                ReportCommonShiTeiPrintSet sakuseiPrintSet = new ReportCommonShiTeiPrintSet();
                // 作成年月日印刷区分
                sakuseiPrintSet.setSakuseiKubun(inDto.getPrintSet().getParam13());
                // 作成年月日
                sakuseiPrintSet.setCreateYmd("");
                shuukanModel.setSakuseiPrintSet(sakuseiPrintSet);
                // 印刷オプション
                ShuukanServiceReportCommonPrintOption printOption = new ShuukanServiceReportCommonPrintOption();
                // タイトル
                printOption.setTitle(inDto.getPrintSet().getPrtTitle());
                // 敬称変更フラグ
                printOption.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                // 敬称
                printOption.setKeisho(inDto.getInitMasterObj().getKeishoKnj());
                // 承認欄を印刷するフラグ
                printOption.setShoninFlg(inDto.getPrintSet().getParam05());
                // 記入用シートを印刷するフラグ
                printOption.setEmptyFlg(inDto.getDbNoSaveData().getEmptyFlg());
                // 処理年月日を印刷するフラグ
                printOption.setNenshoriFlg(inDto.getPrintSet().getParam05());
                // 処理年月日
                printOption.setStShori("");
                // 語尾
                printOption.setGobi(inDto.getPrintSet().getParam09());
                // 語尾
                printOption.setGobiFlg(inDto.getPrintSet().getParam07());
                // 処理年月日に右上を印刷するフラグ
                printOption.setNenshorimigiFlg(inDto.getPrintSet().getParam08());
                // 要介護取得方法
                printOption.setYoukaiKbn(inDto.getPrintSet().getParam10());
                // 事業所名に右上を印刷するフラグ
                printOption.setJigyomigiFlg(inDto.getPrintSet().getParam30());
                // 事業所名表示フラグ
                printOption.setJigyoDisplayFlg(inDto.getPrintSet().getParam31());
                shuukanModel.setPrintOption(printOption);
                // 帳票用データ詳細
                ShuukanServiceReportServiceInDto shuukanInDto = shuukanServiceReportLogic
                        .getU00852ReportParameters(shuukanModel);
                outDto.setData9(shuukanInDto);
                break;
            // 週間サービス計画表（R34改訂）
            case SHUUKAN_REPORT_R34:
                ShuukanServiceR34ReportParameterModel shuukanR34Model = new ShuukanServiceR34ReportParameterModel();
                // 帳票ID
                shuukanR34Model.setReportId(inDto.getReportId());
                // 契約者ID
                shuukanR34Model.setKeiyakushaId(inDto.getKeiyakushaId());
                // システム日付
                shuukanR34Model.setSystemDate(inDto.getAppYmd());
                // 事業者情報
                shuukanR34Model.setJigyoInfo(inDto.getJigyoInfo());
                // 計画期間ID
                shuukanR34Model.setSc1Id(inDto.getPrintSubjectHistory().getSc1Id());
                // 施設ID
                shuukanR34Model.setShisetuId(inDto.getJigyoInfo().getShisetuId());
                // 法人ID
                shuukanR34Model.setHoujinId(inDto.getJigyoInfo().getHoujinId());
                // 事業所ID
                shuukanR34Model.setSvJigyoId(inDto.getJigyoInfo().getSvJigyoId());
                // 履歴ID
                shuukanR34Model.setRirekiId(inDto.getPrintSubjectHistory().getRirekiId());
                // 利用者ID
                shuukanR34Model.setUserId(inDto.getPrintSubjectHistory().getUserId());
                // 職員ID
                shuukanR34Model.setShokuinId(inDto.getDbNoSaveData().getShokuinId());
                // システムコード
                shuukanR34Model.setSyscd(inDto.getDbNoSaveData().getSysCd());
                // 印鑑欄を表示するフラグ
                shuukanR34Model.setInkanFlg(INKANFLG_TRUE);
                // 指定日印刷設定
                ReportCommonPrintSet shiTeiR34PrintSet = new ReportCommonPrintSet();
                // 指定日印刷区分
                shiTeiR34PrintSet.setShiTeiKubun(inDto.getPrintSet().getPrnDate());
                // 指定日
                shiTeiR34PrintSet.setShiTeiDate(inDto.getDbNoSaveData().getSelectDate());
                shuukanR34Model.setShiTeiPrintSet(shiTeiR34PrintSet);
                // 作成日印刷設定
                ReportCommonShiTeiPrintSet sakuseiR34PrintSet = new ReportCommonShiTeiPrintSet();
                // 作成年月日印刷区分
                sakuseiR34PrintSet.setSakuseiKubun(inDto.getPrintSet().getParam13());
                // 作成年月日
                sakuseiR34PrintSet.setCreateYmd("");
                shuukanR34Model.setSakuseiPrintSet(sakuseiR34PrintSet);
                // 印刷オプション
                ReportSvcR34PrintOption printOptionR34 = new ReportSvcR34PrintOption();
                // 帳票タイトル
                printOptionR34.setTitle(inDto.getPrintSet().getPrtTitle());
                // 敬称変更フラグ
                printOptionR34.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                // 敬称
                printOptionR34.setKeisho(inDto.getInitMasterObj().getKeishoKnj());
                // 承認欄を印刷するフラグ
                printOptionR34.setShoninFlg(inDto.getPrintSet().getParam05());
                // 記入用シートを印刷するフラグ
                printOptionR34.setEmptyFlg(inDto.getDbNoSaveData().getEmptyFlg());
                // 処理年月日を印刷するフラグ
                printOptionR34.setNenshoriFlg(inDto.getPrintSet().getParam07());
                // 処理年月日
                printOptionR34.setStShori("");
                // 語尾
                printOptionR34.setGobi(inDto.getPrintSet().getParam09());
                // 語尾フラグ
                printOptionR34.setGobiFlg(inDto.getPrintSet().getParam07());
                // 処理年月日に右上を印刷するフラグ
                printOptionR34.setNenshorimigiFlg(inDto.getPrintSet().getParam08());
                // 要介護取得方法
                printOptionR34.setYoukaiKbn(inDto.getPrintSet().getParam10());
                // 事業所名に右上を印刷するフラグ
                printOptionR34.setJigyomigiFlg(inDto.getPrintSet().getParam30());
                // 事業所名表示フラグ
                printOptionR34.setJigyoDisplayFlg(inDto.getPrintSet().getParam31());
                shuukanR34Model.setPrintOption(printOptionR34);
                // 帳票用データ詳細
                ShuukanServiceR34ReportServiceInDto shuukanR34InDto = shuukanSvcRptLogic
                        .getShuukanServiceR34ReportParameters(shuukanR34Model);
                outDto.setData5(shuukanR34InDto);
                break;
            // 日課計画表出力
            case DAILY_ROUTINE_PLAN_REPORT:
                DailyRoutinePlanReportParameterModel dailyRoutineModel = new DailyRoutinePlanReportParameterModel();
                // 帳票ID
                dailyRoutineModel.setReportId(inDto.getReportId());
                // 契約者ID
                dailyRoutineModel.setKeiyakushaId(inDto.getKeiyakushaId());
                // システム日付
                dailyRoutineModel.setSystemDate(inDto.getAppYmd());
                // 事業者情報
                dailyRoutineModel.setJigyoInfo(inDto.getJigyoInfo());
                // 事業所名
                dailyRoutineModel.setJigyoKnj(inDto.getJigyoKnj());
                // システム日付（アプリ用）
                dailyRoutineModel.setAppYmd(inDto.getAppYmd());
                // 初期設定マスタの情報
                KghMocKrkSsmMasterInfoDto dailyRoutineInitMasterObj = new KghMocKrkSsmMasterInfoDto();
                // 敬称オプション
                dailyRoutineInitMasterObj.setKeishoFlg(inDto.getInitMasterObj().getKeishoFlg());
                // 敬称
                dailyRoutineInitMasterObj.setKeishoKnj(inDto.getInitMasterObj().getKeishoKnj());
                dailyRoutineModel.setInitMasterObj(dailyRoutineInitMasterObj);
                // 印刷設定
                DailyRoutinePlanReportServicePrintSet dailyRoutinePrintSet = new DailyRoutinePlanReportServicePrintSet();
                // 帳票タイトル
                dailyRoutinePrintSet.setPrtTitle(inDto.getPrintSet().getPrtTitle());
                // セクション番号
                dailyRoutinePrintSet.setSectionNo(inDto.getPrintSet().getSectionNo());
                // 帳票No
                dailyRoutinePrintSet.setPrtNo(inDto.getPrintSet().getPrtNo());
                // プロファイル
                dailyRoutinePrintSet.setProfile(inDto.getPrintSet().getProfile());
                // 日付表示有無
                dailyRoutinePrintSet.setPrnDate(inDto.getPrintSet().getPrnDate());
                // 職員表示有無
                dailyRoutinePrintSet.setPrnshoku(inDto.getPrintSet().getPrnshoku());
                // パラメータ01
                dailyRoutinePrintSet.setParam01(inDto.getPrintSet().getParam01());
                // パラメータ02
                dailyRoutinePrintSet.setParam02(inDto.getPrintSet().getParam02());
                // パラメータ03
                dailyRoutinePrintSet.setParam03(inDto.getPrintSet().getParam03());
                // パラメータ04
                dailyRoutinePrintSet.setParam04(inDto.getPrintSet().getParam04());
                // パラメータ05
                dailyRoutinePrintSet.setParam05(inDto.getPrintSet().getParam05());
                // パラメータ06
                dailyRoutinePrintSet.setParam06(inDto.getPrintSet().getParam06());
                // パラメータ07
                dailyRoutinePrintSet.setParam07(inDto.getPrintSet().getParam07());
                // パラメータ08
                dailyRoutinePrintSet.setParam08(inDto.getPrintSet().getParam08());
                // パラメータ09
                dailyRoutinePrintSet.setParam09(inDto.getPrintSet().getParam09());
                // パラメータ10
                dailyRoutinePrintSet.setParam10(inDto.getPrintSet().getParam10());
                // パラメータ11
                dailyRoutinePrintSet.setParam11(inDto.getPrintSet().getParam11());
                // パラメータ12
                dailyRoutinePrintSet.setParam12(inDto.getPrintSet().getParam12());
                // パラメータ13
                dailyRoutinePrintSet.setParam13(inDto.getPrintSet().getParam13());
                // パラメータ14
                dailyRoutinePrintSet.setParam14(inDto.getPrintSet().getParam14());
                // パラメータ15
                dailyRoutinePrintSet.setParam15(inDto.getPrintSet().getParam15());
                // パラメータ16
                dailyRoutinePrintSet.setParam16(inDto.getPrintSet().getParam16());
                // パラメータ17
                dailyRoutinePrintSet.setParam17(inDto.getPrintSet().getParam17());
                // パラメータ18
                dailyRoutinePrintSet.setParam18(inDto.getPrintSet().getParam18());
                // パラメータ19
                dailyRoutinePrintSet.setParam19(inDto.getPrintSet().getParam19());
                // パラメータ20
                dailyRoutinePrintSet.setParam20(inDto.getPrintSet().getParam20());
                // パラメータ21
                dailyRoutinePrintSet.setParam21(inDto.getPrintSet().getParam21());
                // パラメータ22
                dailyRoutinePrintSet.setParam22(inDto.getPrintSet().getParam22());
                // パラメータ23
                dailyRoutinePrintSet.setParam23(inDto.getPrintSet().getParam23());
                // パラメータ24
                dailyRoutinePrintSet.setParam24(inDto.getPrintSet().getParam24());
                // パラメータ25
                dailyRoutinePrintSet.setParam25(inDto.getPrintSet().getParam25());
                // パラメータ26
                dailyRoutinePrintSet.setParam26(inDto.getPrintSet().getParam26());
                // パラメータ27
                dailyRoutinePrintSet.setParam27(inDto.getPrintSet().getParam27());
                // パラメータ28
                dailyRoutinePrintSet.setParam28(inDto.getPrintSet().getParam28());
                // パラメータ29
                dailyRoutinePrintSet.setParam29(inDto.getPrintSet().getParam29());
                // パラメータ30
                dailyRoutinePrintSet.setParam30(inDto.getPrintSet().getParam30());
                // パラメータ31
                dailyRoutinePrintSet.setParam31(inDto.getPrintSet().getParam31());
                // パラメータ32
                dailyRoutinePrintSet.setParam32(inDto.getPrintSet().getParam32());
                // パラメータ33
                dailyRoutinePrintSet.setParam33(inDto.getPrintSet().getParam33());
                // パラメータ34
                dailyRoutinePrintSet.setParam34(inDto.getPrintSet().getParam34());
                // パラメータ35
                dailyRoutinePrintSet.setParam35(inDto.getPrintSet().getParam35());
                // パラメータ36
                dailyRoutinePrintSet.setParam36(inDto.getPrintSet().getParam36());
                // パラメータ37
                dailyRoutinePrintSet.setParam37(inDto.getPrintSet().getParam37());
                // パラメータ38
                dailyRoutinePrintSet.setParam38(inDto.getPrintSet().getParam38());
                // パラメータ39
                dailyRoutinePrintSet.setParam39(inDto.getPrintSet().getParam39());
                // パラメータ40
                dailyRoutinePrintSet.setParam40(inDto.getPrintSet().getParam40());
                // パラメータ41
                dailyRoutinePrintSet.setParam41(inDto.getPrintSet().getParam41());
                // パラメータ42
                dailyRoutinePrintSet.setParam42(inDto.getPrintSet().getParam42());
                // パラメータ43
                dailyRoutinePrintSet.setParam43(inDto.getPrintSet().getParam43());
                // パラメータ44
                dailyRoutinePrintSet.setParam44(inDto.getPrintSet().getParam44());
                // パラメータ45
                dailyRoutinePrintSet.setParam45(inDto.getPrintSet().getParam45());
                // パラメータ46
                dailyRoutinePrintSet.setParam46(inDto.getPrintSet().getParam46());
                // パラメータ47
                dailyRoutinePrintSet.setParam47(inDto.getPrintSet().getParam47());
                // パラメータ48
                dailyRoutinePrintSet.setParam48(inDto.getPrintSet().getParam48());
                // パラメータ49
                dailyRoutinePrintSet.setParam49(inDto.getPrintSet().getParam49());
                // パラメータ50
                dailyRoutinePrintSet.setParam50(inDto.getPrintSet().getParam50());
                dailyRoutineModel.setPrintSet(dailyRoutinePrintSet);
                // DB未保存画面項目
                DailyRoutinePlanReportServiceDbNoSaveData dailyRoutineDbNoSaveData = new DailyRoutinePlanReportServiceDbNoSaveData();
                // 記入用シートを印刷するフラグ
                dailyRoutineDbNoSaveData.setEmptyFlg(inDto.getDbNoSaveData().getEmptyFlg());
                // 指定日
                dailyRoutineDbNoSaveData.setSelectDate(inDto.getDbNoSaveData().getSelectDate());
                dailyRoutineModel.setDbNoSaveData(dailyRoutineDbNoSaveData);
                // 印刷対象履歴
                DailyRoutinePlanReportServicePrintSubjectHistory dailyRoutinePrintSubjectHistory = new DailyRoutinePlanReportServicePrintSubjectHistory();
                // 利用者ID
                dailyRoutinePrintSubjectHistory.setUserId(inDto.getPrintSubjectHistory().getUserId());
                // 履歴ID
                dailyRoutinePrintSubjectHistory.setRirekiId(inDto.getPrintSubjectHistory().getRirekiId());
                // 計画期間ID
                dailyRoutinePrintSubjectHistory.setSc1Id(inDto.getPrintSubjectHistory().getSc1Id());
                dailyRoutineModel.setPrintSubjectHistory(dailyRoutinePrintSubjectHistory);
                DailyRoutinePlanReportServiceOutDto dailyRoutineOutDto = new DailyRoutinePlanReportServiceOutDto();
                DailyRoutinePlanReportServiceInDto dailyRoutineInDto = dailyRoutinePlanReportLogic
                        .getU00861ReportParameters(
                                dailyRoutineModel, dailyRoutineOutDto);
                outDto.setData6(dailyRoutineInDto);
                break;
            // サービス利用票
            case SERVICE_RITEI_REPORT:
                ServiceRiteiReportParameterModel teikyohyoModel = new ServiceRiteiReportParameterModel();
                // 帳票ID
                teikyohyoModel.setReportId(inDto.getReportId());
                // 契約者ID
                teikyohyoModel.setKeiyakushaId(inDto.getKeiyakushaId());
                // システム日付
                teikyohyoModel.setSystemDate(inDto.getAppYmd());
                // 事業者情報
                teikyohyoModel.setJigyoInfo(inDto.getJigyoInfo());
                // 印刷設定
                ReportCommonPrintSet printSet = new ReportCommonPrintSet();
                // 指定日印刷区分
                printSet.setShiTeiKubun(inDto.getPrintSet().getPrnDate());
                // 指定日
                printSet.setShiTeiDate(inDto.getDbNoSaveData().getSelectDate());
                teikyohyoModel.setPrintSet(printSet);
                // 提供年月
                teikyohyoModel.setYymmYm(inDto.getPrintSubjectHistory().getYymmYm());
                // システムコード
                teikyohyoModel.setSyscd(inDto.getDbNoSaveData().getSysCd());
                // 職員ID
                teikyohyoModel.setShokuinId(inDto.getDbNoSaveData().getShokuinId());
                // 利用者最大数
                teikyohyoModel.setUserMax(
                        CommonDtoUtil.objValToString(inDto.getPrintSubjectHistory().getUserIdList().size()));
                // 電子保存の3原則フラグ
                teikyohyoModel.setGbeBunshoFlg(CommonConstants.STR_FALSE);
                // 支援事業所ID
                teikyohyoModel.setShienId(inDto.getJigyoInfo().getSvJigyoId());
                // 利用者IDリスト
                teikyohyoModel.setUserIdList(inDto.getPrintSubjectHistory().getUserIdList().stream().map(x -> {
                    V00241UserId user = new V00241UserId();
                    // 利用者ID
                    user.setUserId(x.getUserId());
                    // サービス提供年月(変更日)
                    user.setYymmd(x.getYymmd());
                    // 支援事業者id
                    user.setShienid(x.getShienid());
                    return user;
                }).toList());
                TeikyohyoReportServiceInDto teikyohyoInDto = serviceRiteiReportLogic
                        .getV00231ReportParameters(teikyohyoModel);
                outDto.setData7(teikyohyoInDto);
                break;
            // サービス利用票別表
            case SERVICE_USE_ANNEXED_TABLE_REPORT:
                ServiceUseAnnexedTableReportParameterModel serviceUseAnnexedmodel = new ServiceUseAnnexedTableReportParameterModel();
                // 帳票ID
                serviceUseAnnexedmodel.setReportId(inDto.getReportId());
                // 契約者ID
                serviceUseAnnexedmodel.setKeiyakushaId(inDto.getKeiyakushaId());
                // システム日付
                serviceUseAnnexedmodel.setSystemDate(inDto.getAppYmd());
                // 事業者情報
                serviceUseAnnexedmodel.setJigyoInfo(inDto.getJigyoInfo());
                // 印刷設定
                ReportCommonPrintSet usePrintSet = new ReportCommonPrintSet();
                // 指定日印刷区分
                usePrintSet.setShiTeiKubun(inDto.getPrintSet().getPrnDate());
                // 指定日
                usePrintSet.setShiTeiDate(inDto.getDbNoSaveData().getSelectDate());
                serviceUseAnnexedmodel.setPrintSet(usePrintSet);
                // 利用者IDリスト
                serviceUseAnnexedmodel.setUserIdList(inDto.getPrintSubjectHistory().getUserIdList());
                // 提供年月
                serviceUseAnnexedmodel.setYymmYm(inDto.getPrintSubjectHistory().getYymmYm());
                // システムコード
                serviceUseAnnexedmodel.setSyscd(inDto.getDbNoSaveData().getSysCd());
                // 職員ID
                serviceUseAnnexedmodel.setShokuinId(inDto.getDbNoSaveData().getShokuinId());
                // 事業所ID
                serviceUseAnnexedmodel.setSvJigyoId(inDto.getJigyoInfo().getSvJigyoId());
                // 基準日
                serviceUseAnnexedmodel.setAppYmd(inDto.getAppYmd());
                usePrintOption = new ReportCommonGetOptionDto();
                List<ServiceUseAnnexedTableReportServiceInDto> surveyContentsListInfoList = serviceUseAnnexedTableReportLogic
                        .getV00231ServiceUseAnnexedTableReportParameters(serviceUseAnnexedmodel, usePrintOption);
                outDto.setData8(surveyContentsListInfoList);
                break;
            default:
                break;
        }
        return outDto;
    }
}