package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.07.24
 * <AUTHOR> 苗月
 * @description U01700_課題分析(アセスメントの全てと一括印刷)
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReportAccessment implements Serializable {

    /* 期間ID */
    private String sc1Id;
    /* 履歴ID */
    private String rirekiId;
    /* s_parm */
    private String sparm;
    /* l_parm */
    private String lparm;
    /* arg_i */
    private String argI;
    /* ai_radio */
    private String aiRadio; 
    /** 適用項目フラグ */
    private String tekiyoItemFlg;
    /** Visioフラグ */
    private String visioFlg;
    /** ガイドラインまとめ */
    private String gdlMatomeFlg;
    /** 記号意味（印刷）フラグ */
    private String cckKigoPrtFlg;

}
