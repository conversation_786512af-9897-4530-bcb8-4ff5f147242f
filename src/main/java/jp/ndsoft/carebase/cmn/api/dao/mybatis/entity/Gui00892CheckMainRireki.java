package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import jp.ndsoft.smh.framework.global.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GUI00892_チェック項目画面
 * 
 * @description
 *              チェックメイン履歴情報
 *              チェックメイン履歴情報エンティティ
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Gui00892CheckMainRireki implements Serializable, IEntity {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 法人ID */
    private String houjinId;

    /** 施設ID */
    private String shisetuId;

    /** 事業者ID */
    private String svJigyoId;

    /** 利用者ID */
    private String userid;

    /** 計画期間ID */
    private String sc1Id;

    /** 履歴ID */
    private String assId;

    /** 項目ID */
    private String komokuId;

    /** 細目ID */
    private String saimokuId;

    /** 内容ID */
    private String naiyoId;

    /** 作成日 */
    private String createYmd;

    /** 点数 */
    private String point;

}
