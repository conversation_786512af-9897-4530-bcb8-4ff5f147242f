package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jakarta.validation.Valid;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00905IssuesPlanningStyleTitleMasterUpdateData;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.05.14
 * <AUTHOR> 盧青陽
 * @description GUI00905_課題立案様式タイトルマスタ画面の情報保存入力Dto
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class IssuesPlanningStyleTitleUpdateServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 課題立案様式タイトルマスタリスト */
    @Valid
    private List<Gui00905IssuesPlanningStyleTitleMasterUpdateData> issuesPlanningStyleTitleMasterList;
}
