package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01233EvaluationTableMasterSettingsInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.EvaluationTableMasterInfoSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.EvaluationTableMasterInfoSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.05.21
 * <AUTHOR>
 * @description Gui01233_評価表マスタ 初期情報取得
 */
@Service
public class EvaluationTableMasterInfoSelectServiceImpl
        extends
        SelectServiceImpl<EvaluationTableMasterInfoSelectServiceInDto, EvaluationTableMasterInfoSelectServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 評価表マスタ情報取得DAO */
    @Autowired
    private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;

    public EvaluationTableMasterInfoSelectServiceImpl() {
    }

    /**
     * 「具体的内容・対応するケア項目マスタ」画面の初期情報を取得する
     * 
     * @param inDto 初期設定マスタ情報取込入力DTO
     * @return 初期設定マスタ取込初期情報出力DTO
     */
    @Override
    protected EvaluationTableMasterInfoSelectServiceOutDto mainProcess(
            final EvaluationTableMasterInfoSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        // 戻り情報を設定
        EvaluationTableMasterInfoSelectServiceOutDto outDto = new EvaluationTableMasterInfoSelectServiceOutDto();
        // 具体的内容情報取得
        List<Gui01233EvaluationTableMasterSettingsInfo> evaluationTableMasterSettingsInfoList = new ArrayList<>();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. 評価表マスタ情報取得===============
         * 
         */
        // DAOパラメータを作成
        KrkSsmInfoByCriteriaInEntity krkSsmInfoByCriteriaInEntity = new KrkSsmInfoByCriteriaInEntity();
        // 施設ID
        krkSsmInfoByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        krkSsmInfoByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 分類1
        krkSsmInfoByCriteriaInEntity.setBunrui1Id(CommonDtoUtil.strValToInt(inDto.getBunrui1Id()));
        // 分類2
        krkSsmInfoByCriteriaInEntity.setBunrui2Id(CommonDtoUtil.strValToInt(inDto.getBunrui2Id()));
        // DAOを実行 (DAO定義書)
        List<KrkSsmInfoOutEntity> krkSsmInfoOutEntities = this.kghMocKrkSsmSelectMapper
                .findKrkSsmInfoByCriteria(krkSsmInfoByCriteriaInEntity);

        /*
         * ===============3. レスポンス項目を設定する===============
         * 
         */
        // 3.1. 上記「2.」で取得した評価表マスタ情報がNULLの場合、
        // レスポンスパラメータ.評価表マスタ設定情報に２行を新規して、下記のように設定する。
        if (CollectionUtils.isNullOrEmpty(krkSsmInfoOutEntities)) {
            // １行目
            evaluationTableMasterSettingsInfoList
                    .add(createEvaluationTableMasterSettingsInfo(CommonConstants.EVALUATION_BUNRUI3_ID_1));
            // 2行目
            evaluationTableMasterSettingsInfoList
                    .add(createEvaluationTableMasterSettingsInfo(CommonConstants.EVALUATION_BUNRUI3_ID_2));
        } else {
            // 3.2. 上記「2.」で取得した評価表マスタ情報がNULL以外の場合、内容が空白のマスタ情報を編集
            // １行目
            // 3.2.1. 分類3が”1”（内容が空白の場合）のデータが取得されてない場合
            if (CollectionUtils.isNullOrEmpty(krkSsmInfoOutEntities.stream().filter(
                    entity -> entity.getBunrui3Id() == CommonDtoUtil
                            .strValToInt(CommonConstants.EVALUATION_BUNRUI3_ID_1))
                    .toList())) {
                evaluationTableMasterSettingsInfoList
                        .add(createEvaluationTableMasterSettingsInfo(CommonConstants.EVALUATION_BUNRUI3_ID_1));
            } else {
                // 3.2.2. 以外の場合
                for (KrkSsmInfoOutEntity entity : krkSsmInfoOutEntities) {
                    if (entity.getBunrui3Id() == CommonDtoUtil.strValToInt(CommonConstants.EVALUATION_BUNRUI3_ID_1)) {
                        Gui01233EvaluationTableMasterSettingsInfo outInfo1 = new Gui01233EvaluationTableMasterSettingsInfo();
                        // 分類3
                        outInfo1.setBunrui3Id(String.valueOf(entity.getBunrui3Id()));
                        // 整数
                        outInfo1.setIntValue(String.valueOf(entity.getIntValue()));
                        // 更新区分
                        outInfo1.setUpdateKbn(CommonConstants.UPDATE_KBN_U);
                        evaluationTableMasterSettingsInfoList.add(outInfo1);
                    }
                }
            }

            // 3.3. 上記「2.」で取得した評価表マスタ情報がNULL以外の場合、内容が同じのマスタ情報を編集
            // 2行目
            // 3.3.1. 分類3が”2”（内容が同じ場合）のデータが取得されてない場合
            if (CollectionUtils.isNullOrEmpty(krkSsmInfoOutEntities.stream().filter(
                    entity -> entity.getBunrui3Id() == CommonDtoUtil
                            .strValToInt(CommonConstants.EVALUATION_BUNRUI3_ID_2))
                    .toList())) {
                evaluationTableMasterSettingsInfoList
                        .add(createEvaluationTableMasterSettingsInfo(CommonConstants.EVALUATION_BUNRUI3_ID_2));
            } else {
                // 3.3.2. 以外の場合
                for (KrkSsmInfoOutEntity entity : krkSsmInfoOutEntities) {
                    if (entity.getBunrui3Id() == CommonDtoUtil.strValToInt(CommonConstants.EVALUATION_BUNRUI3_ID_2)) {
                        Gui01233EvaluationTableMasterSettingsInfo outInfo2 = new Gui01233EvaluationTableMasterSettingsInfo();
                        // 分類3
                        outInfo2.setBunrui3Id(String.valueOf(entity.getBunrui3Id()));
                        // 整数
                        outInfo2.setIntValue(String.valueOf(entity.getIntValue()));
                        // 更新区分
                        outInfo2.setUpdateKbn(CommonConstants.UPDATE_KBN_U);
                        evaluationTableMasterSettingsInfoList.add(outInfo2);
                    }
                }
            }

        }
        outDto.setEvaluationTableMasterSettingsInfoList(evaluationTableMasterSettingsInfoList);

        LOG.info(Constants.END);

        return outDto;
    }

    // レスポンスパラメータ.評価表マスタ設定情報にを新規して共通
    private Gui01233EvaluationTableMasterSettingsInfo createEvaluationTableMasterSettingsInfo(String bunrui3Id) {
        Gui01233EvaluationTableMasterSettingsInfo evaluationTableMasterSettingsInfo = new Gui01233EvaluationTableMasterSettingsInfo();

        // 分類3
        evaluationTableMasterSettingsInfo.setBunrui3Id(bunrui3Id);
        // 整数
        evaluationTableMasterSettingsInfo.setIntValue(CommonConstants.EVALUATION_INTERER_1);
        // 更新回数
        evaluationTableMasterSettingsInfo.setModifiedCnt(CommonConstants.MODIFIEDCENT_STRING_0);
        // 更新区分
        evaluationTableMasterSettingsInfo.setUpdateKbn(CommonConstants.UPDATE_KBN_C);
        return evaluationTableMasterSettingsInfo;

    }
}
