package jp.ndsoft.carebase.cmn.api.logic.dto;

import java.io.Serializable;

import jp.ndsoft.smh.framework.global.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.04.17
 * <AUTHOR>
 * @description 画面情報を保存する
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class KadaiInDto implements Serializable, IEntity {
    /* id */
    private String id;

    /* アセスメント番号 */
    private String assNo;

    /* 課題 */
    private String kadaiKnj;

    /* 長期 */
    private String choukiKnj;

    /* 短期 */
    private String tankiKnj;

    /* 連番 */
    private String seq;

    /* 更新区分 */
    private String updateKbn;

}
