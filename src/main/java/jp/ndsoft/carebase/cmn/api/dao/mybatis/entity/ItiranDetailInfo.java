package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.09.01
 * <AUTHOR>
 *         V00261_限度額超過利用者・算定エラー利用者一覧の一覧詳細
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItiranDetailInfo implements Serializable {
    /** UID. */
    private static final long serialVersionUID = 1L;

    /** 支援ID */
    private Integer shienId;

    /** 表示フラグ */
    private Integer dispFlg;

    /** ユーザーID */
    private Integer userId;

    /** 利用者番号 */
    private Integer selfId;

    /** 利用者氏名 */
    private String userName;

    /** 性別 */
    private String sex;

    /** 保険者名 */
    private String hokenKnj;

    /** 被保険者番号 */
    private String hiHoken;

    /** 種類限度超過 */
    private String shuOver;

    /** 種類限度内 */
    private String shuNai;

    /** 区分限度超過 */
    private String kbnOver;

    /** 区分限度内 */
    private String kbnNai;

    /** エラー内容 */
    private String errKnj;

    /** エラーCD */
    private Integer errCode;

    /** 変更日 */
    private String henkoYmd;

    /** 氏名（姓） */
    private String name1Knj;

    /** 氏名（姓） */
    private String name2Knj;

    /** フリガナ（姓） */
    private String name1Kana;

    /** フリガナ（名） */
    private String name2Kana;

    /** フルフリガナ */
    private String fullKana;

    /** 生年月日 */
    private String birthdayYmd;
}
