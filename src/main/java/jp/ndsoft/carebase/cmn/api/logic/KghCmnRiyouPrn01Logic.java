package jp.ndsoft.carebase.cmn.api.logic;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.TokkiKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TokkiKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanTokkiJikouSelectMapper;

/**
 * KghCmnRiyouPrn01Logicロジッククラス
 * 
 * <AUTHOR>
 */
@Component
public class KghCmnRiyouPrn01Logic {

    /** 提供事業所向け特記事項 */
    @Autowired
    private CmnTucPlanTokkiJikouSelectMapper cmnTucPlanTokkiJikouSelectMapper;
    /** サービス種類から総合事業かどうかをチェックする */
    @Autowired
    private KghCmnF01Logic kghCmnF01Logic;
    /** NdsMidaLogicクラス */
    @Autowired
    private NdsMidaLogic ndsMidaLogic;

    /**
     * wf_j5_sub_get_tokki：特記事項（連絡事項）を取得
     * 
     * @param alUserId    支援事業者id
     * @param asShoriname 対象の提供事業所id
     * @param asKkakId    提供年月(yyyy/mm)
     * @param alCpnFlg    利用者id（０の場合には連絡事項）
     * @param alSheetno   変更日(dd)
     * @return 特記事項（連絡事項）
     * <AUTHOR>
     */
    public String getPlanPreview(Integer alShi, Integer alSvj, String asYm, Integer alUsr, String asDd) {
        String lsRet = StringUtils.EMPTY;
        // 引き数チェック
        if (alShi != null && alShi > CommonConstants.INT_0 && alSvj != null && alSvj > CommonConstants.INT_0
                && asYm != null && asYm.compareTo(CommonConstants.SHORI_YM_200003) > CommonConstants.INT_0
                && alUsr != null && alUsr >= CommonConstants.INT_0 && asDd != null
                && asDd.compareTo(CommonConstants.STR_DAY_ALL_ZERO) > CommonConstants.INT_0) {
            TokkiKnjByCriteriaInEntity tokkiKnjByCriteriaInEntity = new TokkiKnjByCriteriaInEntity();
            // 支援事業者id
            tokkiKnjByCriteriaInEntity.setAlShi(CommonDtoUtil.objValToString(alShi));
            // 対象の提供事業所id
            tokkiKnjByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(alSvj));
            // 利用者id（０の場合には連絡事項）
            tokkiKnjByCriteriaInEntity.setAlUsr(CommonDtoUtil.objValToString(alUsr));
            // 提供年月(yyyy/mm)
            tokkiKnjByCriteriaInEntity.setAsYm(asYm);
            // 変更日(dd)
            tokkiKnjByCriteriaInEntity.setAsDd(asDd);
            // DAOを実行
            List<TokkiKnjOutEntity> tokkiKnjList = cmnTucPlanTokkiJikouSelectMapper
                    .findTokkiKnjByCriteria(tokkiKnjByCriteriaInEntity);
            // チェック
            if (!CollectionUtils.isNullOrEmpty(tokkiKnjList)) {
                lsRet = tokkiKnjList.getFirst().getTokkiKnj();
                if (lsRet == null) {
                    lsRet = StringUtils.EMPTY;
                }
            }
        }
        return lsRet;
    }

    /**
     * 関数名 ： f_kgh_cmn_set_adder_service_betu
     * 処理概要 ： dmy_calc_sort に代入：帳票別表限定
     * 
     * @param ads01 : 対象u3gk_ds_base
     * @param asds  : 年月日 : yyyy/mm/dd
     * @return 返り値 : な し
     * <AUTHOR> 李晨昊
     */
    public void setAdderServiceBetu(List<?> ads01, String asds) {
        try {
            String lsc = StringUtils.EMPTY;
            String llSvj = StringUtils.EMPTY;
            String lsSvcode = StringUtils.EMPTY;
            String lsSvtype = StringUtils.EMPTY;
            Integer liShogu = CommonConstants.NUMBER_0;

            if (CollectionUtils.size(ads01) > 0) {
                Object dmyCalcSortObj = ads01.getFirst().getClass().getMethod(CommonConstants.GET_DMY_CALC_SORT)
                        .invoke(ads01.getFirst());
                boolean isInt = false;
                if (Integer.class.equals(dmyCalcSortObj.getClass())) {
                    isInt = true;
                }
                for (Object item : ads01) {
                    lsc = Objects.toString(item.getClass().getMethod(CommonConstants.GET_SCODE).invoke(item),
                            StringUtils.EMPTY);
                    llSvj = Objects.toString(
                            item.getClass().getMethod(CommonConstants.GET_SV_JIGYO_ID).invoke(item), StringUtils.EMPTY);
                    Integer dmyCalcSort = kghCmnF01Logic.checkAdderService(lsc, asds, CommonDtoUtil.strValToInt(llSvj));
                    invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT, isInt ? dmyCalcSort
                            : CommonDtoUtil.objValToString(dmyCalcSort));
                    lsSvcode = ndsMidaLogic.fNdsMida(lsc, CommonConstants.INT_3, CommonConstants.INT_4);
                    liShogu = kghCmnF01Logic.isShoguuKaizenKasan(asds, lsc);
                    if (liShogu != null && liShogu > 0) {
                        Integer val = 2000 + liShogu;
                        invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT, isInt ? val
                                : CommonDtoUtil.objValToString(val));
                    } else {
                        if (kghCmnF01Logic.getKyoseiGenzan(asds, lsc) > 0) {
                            // 共生型サービス：基本サービスを所定単位数に含めるため基本サービスより下に表示されるよう修正
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 900 : CommonConstants.STR_900);
                        } else {
                            lsSvtype = StringUtils.left(lsc, 2);
                            if (asds.compareTo(CommonConstants.CREATION_DATE_20180401) >= 0
                                    && CommonConstants.SV_TYPE_76.equals(lsSvtype)) {
                                if (CommonConstants.SCODE_76411100.equals(lsc)
                                        || CommonConstants.SCODE_76411200.equals(lsc)
                                        || CommonConstants.SCODE_76411300.equals(lsc)
                                        || CommonConstants.SCODE_76411400.equals(lsc)) {
                                    // 定期巡回の同一建物減算は単位数かつ特別地域加算等の所定所定単位数に含めるため
                                    // 小計行と率加算の間に表示されるよう修正
                                    invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                            isInt ? CommonConstants.INT_0 : CommonConstants.STR_0);
                                }
                            }
                        }

                        // 感染症特例加算
                        if (kghCmnF01Logic.isKansenTokureiKasan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 800 : CommonConstants.STR_800);
                        }

                        // 訪問介護特定事業所加算Ⅴ
                        if (kghCmnF01Logic.isTokuteiJigyoKasan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 810 : CommonConstants.STR_810);
                        }

                        // 通所リハ継続減算
                        if (kghCmnF01Logic.isRihaKeizokuGenzan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 810 : CommonConstants.STR_810);
                        }

                        // 延長サービス加算が共生型に含まれるようにする必要がある
                        if (CommonConstants.SV_CD_STR_15.equals(lsSvtype)
                                || CommonConstants.SV_CD_STR_78.equals(lsSvtype)) {
                            if (CommonConstants.SV_CODE_LIST_4.contains(lsSvcode)) {
                                invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                        isInt ? 810 : CommonConstants.STR_810);
                            }
                        }

                        // 特別地域、小規模事業所加算が中山間より下に表示されるように指定
                        if (CommonConstants.SV_CD_STR_73.equals(lsSvtype)
                                || CommonConstants.SV_CD_STR_75.equals(lsSvtype)
                                || CommonConstants.SV_CD_STR_77.equals(lsSvtype)) {
                            if (kghCmnF01Logic.getChusankanRiyo(lsc, asds) == 1
                                    || kghCmnF01Logic.getSpecialPlaceNew(asds, lsc)) {
                                invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                        isInt ? 900 : CommonConstants.STR_900);
                            } else if (kghCmnF01Logic.getChusankanRiyo(lsc, asds) == 2) {
                                invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                        isInt ? 910 : CommonConstants.STR_910);
                            }
                        }

                        // 高齢者虐待防止未実施減算
                        if (kghCmnF01Logic.isGyakutaiBousiGenzan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 790 : CommonConstants.STR_790);
                        }

                        // 業務継続計画未策定減算
                        if (kghCmnF01Logic.isGyomuKeizokuGenzan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 800 : CommonConstants.STR_800);
                        }

                        // 過少サービス減算
                        if (kghCmnF01Logic.isKashouServiceGenzan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 810 : CommonConstants.STR_810);
                        }

                        // サテライト体制未整備減算
                        if (kghCmnF01Logic.isSatelliteMiseibiGenzan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 820 : CommonConstants.STR_820);
                        }
                        // 身体拘束廃止未実施減算
                        if (kghCmnF01Logic.isSatelliteMiseibiGenzan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 780 : CommonConstants.STR_780);
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * エンティティクラスのSetterメソッドを実行する
     * 
     * @param target     DAOエンティティクラス
     * @param methodName メソッド名
     * @param args       引数
     */
    private void invokeMethod(Object target, String methodName, Object... args) {
        try {
            // 引数の型を取得
            Class<?>[] paramTypes = new Class<?>[args.length];
            for (int i = 0; i < args.length; i++) {
                paramTypes[i] = args[i].getClass();
            }

            // メソッドを取得
            Method method = target.getClass().getMethod(methodName, paramTypes);

            // メソッドを実行
            method.invoke(target, args);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke method: " + methodName, e);
        }
    }
}
