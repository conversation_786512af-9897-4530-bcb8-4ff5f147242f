package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01217PlanImplementationHistoryKjisshi1IdInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanImplementationAchievementsRegistHistorySelectInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanImplementationAchievementsRegistHistorySelectOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnKkjRirekiSelByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnKkjRirekiSelOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KeikakuJisshihyouKihonRirekiSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * 
 * @since 2025.05.21
 * <AUTHOR> 朱红昌
 * @implNote GUI01217_「計画実施‐実績登録」画面用履歴情報を取得
 */
@Service
public class PlanImplementationAchievementsRegistHistorySelectServiceImpl extends
        SelectServiceImpl<PlanImplementationAchievementsRegistHistorySelectInDto, PlanImplementationAchievementsRegistHistorySelectOutDto> {

    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private KeikakuJisshihyouKihonRirekiSelectMapper keikakuJisshihyouKihonRirekiSelectMapper;

    /**
     * 計画実施‐実績登録画面用履歴情報取得
     * 
     * @param inDto GUI01217_計画実施‐実績登録の画面用履歴情報の入力DTO.
     * @return GUI01217_計画実施‐実績登録の画面用履歴情報の出力DTO.
     * @throws Exception Exception
     */
    @Override
    protected PlanImplementationAchievementsRegistHistorySelectOutDto mainProcess(
            PlanImplementationAchievementsRegistHistorySelectInDto inDto) throws Exception {
        LOG.info(Constants.START);

        // 戻り情報を設定
        PlanImplementationAchievementsRegistHistorySelectOutDto out = new PlanImplementationAchievementsRegistHistorySelectOutDto();
        // 計画実施履歴ヘッダID情報リスト
        List<Gui01217PlanImplementationHistoryKjisshi1IdInfo> planImplementationHistoryKjisshi1IdInfoList = new ArrayList<>();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2.計画実施履歴選択の情報を取得===============
         * 
         */
        CpnKkjRirekiSelByCriteriaInEntity cpnKkjRirekiSelByCriteriaInEntity = new CpnKkjRirekiSelByCriteriaInEntity();
        // 事業者ID
        cpnKkjRirekiSelByCriteriaInEntity.setAlSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        cpnKkjRirekiSelByCriteriaInEntity.setAlUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // DAOを実行
        List<CpnKkjRirekiSelOutEntity> keikakuJisshihyouKihonRirekiSelectList = this.keikakuJisshihyouKihonRirekiSelectMapper
                .findCpnKkjRirekiSelByCriteria(cpnKkjRirekiSelByCriteriaInEntity);

        // 3.計画実施履歴選択情報リストに
        for (CpnKkjRirekiSelOutEntity keikakuJisshihyouKihonRirekiSelect : keikakuJisshihyouKihonRirekiSelectList) {
            // 「計画実施履歴選択情報リスト.処理年月」=「リクエストパラメータ.実施日の左7桁」のデータを取得して、
            if (CommonDtoUtil.checkStringEqual(keikakuJisshihyouKihonRirekiSelect.getSyoriYm(),
                    inDto.getJisshiDayLeft7())) {
                Gui01217PlanImplementationHistoryKjisshi1IdInfo kjisshi1IdInfo = new Gui01217PlanImplementationHistoryKjisshi1IdInfo();
                kjisshi1IdInfo.setKjisshi1Id(
                        CommonDtoUtil.objValToString(keikakuJisshihyouKihonRirekiSelect.getKjisshi1Id()));
                planImplementationHistoryKjisshi1IdInfoList.add(kjisshi1IdInfo);
            }

        }
        // ヘッダIDのリストはレスポンスへ返却する
        out.setPlanImplementationHistoryKjisshi1IdInfoList(planImplementationHistoryKjisshi1IdInfoList);

        LOG.info(Constants.END);
        return out;
    }
}
