package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.service.dto.Cp2MasterUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Cp2MasterUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01012Keikakusho2InDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkSsmMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsm;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsmCriteria;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.smh.framework.util.AppUtil;

/**
 * 
 * @since 2025.05.05
 * <AUTHOR> TRAN VAN THAO
 * @implNote GUI01012_計画書（2）マスタ設定情報保存
 */
@Service
public class Cp2MasterUpdateServiceImpl
        extends UpdateServiceImpl<Cp2MasterUpdateServiceInDto, Cp2MasterUpdateServiceOutDto> {
    // ロガー.
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // 分類1=2
    private static final Integer BUNRUI1_ID = 2;

    // 分類2=4
    private static final Integer BUNRUI2_ID = 4;

    @Autowired
    private KghMocKrkSsmMapper kghMocKrkSsmMapper;

    /**
     * 計画書（2）マスタ設定情報保存
     * 
     * @param inDto Cp2MasterUpdateServiceInDto.
     * @return Cp2MasterUpdateServiceOutDto
     * @throws Exception Exception
     */
    @Override
    protected Cp2MasterUpdateServiceOutDto mainProcess(Cp2MasterUpdateServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        Cp2MasterUpdateServiceOutDto outDto = new Cp2MasterUpdateServiceOutDto();

        // 施設ID
        String shisetuId = inDto.getShisetuId();
        // 事業所ID
        String svJigyold = inDto.getSvJigyoId();
        // 計画書（2）マスタ情報
        List<Gui01012Keikakusho2InDto> keikakusho2s = inDto.getKeikakusho2();

        // 1.単項目チェック以外の入力チェック
        // 2.リクエストパラメータより、初期設定マスタ情報を更新する
        if (!keikakusho2s.isEmpty()) {
            for (Gui01012Keikakusho2InDto gui01012Keikakusho2InDto : keikakusho2s) {
                if (CommonDtoUtil.isUpdate(gui01012Keikakusho2InDto)) {
                    KghMocKrkSsmCriteria kghMocKrkSsmCriteria = new KghMocKrkSsmCriteria();

                    // リクエストパラメータ詳細.施設ID
                    // リクエストパラメータ詳細.事業所ID
                    // 2(固定値）
                    // 4(固定値）
                    // リクエストパラメータ詳細.計画書（2）マスタ情報.分類３
                    kghMocKrkSsmCriteria.createCriteria().andShisetuIdEqualTo(CommonDtoUtil.strValToInt(shisetuId))
                            .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(svJigyold)).andBunrui1IdEqualTo(BUNRUI1_ID)
                            .andBunrui2IdEqualTo(BUNRUI2_ID)
                            .andBunrui3IdEqualTo(CommonDtoUtil.strValToInt(gui01012Keikakusho2InDto.getBunrui3Id()));

                    // 2.1 マスタ情報をループして、マスタ情報.更新区分が"U":更新の場合、データ存在チェックを行う
                    long countQuery = kghMocKrkSsmMapper.countByCriteria(kghMocKrkSsmCriteria);

                    // OUTPUT情報.Count = 0の場合
                    if (countQuery == 0) {
                        // 2.2 新規処理を行う
                        KghMocKrkSsm kghMocKrkSsm = new KghMocKrkSsm();
                        // リクエストパラメータ詳細.施設ID
                        kghMocKrkSsm.setShisetuId(CommonDtoUtil.strValToInt(shisetuId));
                        // リクエストパラメータ詳細.事業所ID
                        kghMocKrkSsm.setSvJigyoId(CommonDtoUtil.strValToInt(svJigyold));
                        // 分類1
                        kghMocKrkSsm.setBunrui1Id(BUNRUI1_ID);
                        // 分類2
                        kghMocKrkSsm.setBunrui2Id(BUNRUI2_ID);
                        // リクエストパラメータ詳細.計画書（2）マスタ情報.分類３
                        kghMocKrkSsm.setBunrui3Id(CommonDtoUtil.strValToInt(gui01012Keikakusho2InDto.getBunrui3Id()));
                        // リクエストパラメータ詳細.計画書（2）マスタ情報.整数
                        kghMocKrkSsm.setIntValue(CommonDtoUtil.strValToInt(gui01012Keikakusho2InDto.getIntValue()));
                        // CURRENT_TIMESTAMP
                        kghMocKrkSsm.setTimeStmp(AppUtil.getSystemTimeStamp());
                        // 登録時の共通カラム値設定処理
                        // CommonDaoUtil.setInsertCommonColumns(kghMocKrkSsm);

                        // 初期設定マスタテーブル（kgh_moc_krk_ssm）の更新詳細
                        kghMocKrkSsmMapper.insertSelective(kghMocKrkSsm);
                    } else {
                        // 2.3 更新処理を行う
                        KghMocKrkSsm kghMocKrkSsm = new KghMocKrkSsm();

                        // リクエストパラメータ詳細.計画書（2）マスタ情報.整数
                        kghMocKrkSsm.setIntValue(CommonDtoUtil.strValToInt(gui01012Keikakusho2InDto.getIntValue()));
                        // CURRENT_TIMESTAMP
                        kghMocKrkSsm.setTimeStmp(AppUtil.getSystemTimeStamp());
                        // 更新回数
                        // BigInteger modifiedCnt = new
                        // BigInteger(gui01012Keikakusho2InDto.getModifiedCnt());
                        // 共通カラム値設定
                        // CommonDaoUtil.setUpdateCommonColumns(kghMocKrkSsm, modifiedCnt);

                        KghMocKrkSsmCriteria kghMocKrkSsmCriteriaUpdate = new KghMocKrkSsmCriteria();

                        // 更新条件
                        // 施設ID＝リクエストパラメータ詳細.施設ID
                        // 事業者ID＝リクエストパラメータ詳細.事業者ID
                        // 分類1＝2
                        // 分類2＝4
                        // 分類3=リクエストパラメータ詳細.計画書（2）マスタ情報.分類３
                        // 更新回数=リクエストパラメータ詳細.計画書（2）マスタ情報.更新回数
                        kghMocKrkSsmCriteriaUpdate.createCriteria()
                                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(shisetuId))
                                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(svJigyold))
                                .andBunrui1IdEqualTo(BUNRUI1_ID)
                                .andBunrui2IdEqualTo(BUNRUI2_ID)
                                .andBunrui3IdEqualTo(
                                        CommonDtoUtil.strValToInt(gui01012Keikakusho2InDto.getBunrui3Id()));
                        // .andModifiedCntEqualTo(modifiedCnt);

                        // 初期設定マスタテーブル（kgh_moc_krk_ssm）の更新詳細
                        int updateCnt = kghMocKrkSsmMapper.updateByCriteriaSelective(kghMocKrkSsm,
                                kghMocKrkSsmCriteriaUpdate);
                        if (updateCnt <= 0) {
                            throw new ExclusiveException();
                        }
                    }
                }
            }
        }

        LOG.info(Constants.END);
        return outDto;
    }
}
