package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * @since 2025.06.27
 * <AUTHOR>
 * @implNote GUI01149_利用票画面回数入力変更前のチェック情報を取得する
 */
@Getter
@Setter
public class UseSlipInfoNumberOfTimesModifiedBefCheckSelectServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 提供年月 */
    @NotEmpty
    private String teikyouYm;

    /** 選択の行番号 */
    @NotEmpty
    private String lineNo;

    /** 選択の項目名 */
    @NotEmpty
    private String proItem;

    /** 選択の項目の値 */
    @NotEmpty
    private String proValue;

    /** 利用票明細情報 */
    @NotEmpty
    private List<Gui01149Riyou> riyouList;

    /** 利用票画面処理用構造体 */
    @NotEmpty
    private List<Gui01149RiyouProcess> riyouProcessObject;
}
