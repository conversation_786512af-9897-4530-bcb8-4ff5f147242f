package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.04.23
 * <AUTHOR>
 * @apiNote GUI00626_「アセスメントマスタ」画面の入力情報保存サービス入力Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AssessmentMasterUpdateServiceInDto extends IDtoImpl {

    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;
    /** 施設ID */
    @NotEmpty
    private String shisetuId;
    /** 事業者ID */
    @NotEmpty
    private String svJigyoId;
    /** 分類1 */
    @NotEmpty
    private String bunrui1Id;
    /** 分類2 */
    @NotEmpty
    private String bunrui2Id;
    /** Visio */
    private String visioValue;
    /** Visio分類３ */
    @NotEmpty
    private String visioBunrui3;
    /** Visio更新区分 */
    private String visioUpdateKbn;
    /** 障害等の部位の摘要表示 */
    private String syougaiValue;
    /** 障害分類３ */
    @NotEmpty
    private String syougaiBunrui3;
    /** 障害更新区分 */
    private String syougaiUpdateKbn;
    /** 全体のまとめ */
    private String zenntaiValue;
    /** 全体分類３ */
    @NotEmpty
    private String zenntaiBunrui3;
    /** 全体更新区分 */
    private String zenntaiUpdateKbn;
}