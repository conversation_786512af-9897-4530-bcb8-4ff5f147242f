package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI00945_実施計画①マスタ情報保存の入力DTO.
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class ImplementPlanOneMasterUpdateServiceInDto extends IDtoImpl {

    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 施設ID */
    @NotEmpty
    private String shisetuId;

    /** 事業所ID */
    @NotEmpty
    private String jigyoId;

    /** 分類3設定リスト */
    private List<ImplementPlanOneMasterUpdateServiceBunrui3SetDto> bunrui3SetList;
}
