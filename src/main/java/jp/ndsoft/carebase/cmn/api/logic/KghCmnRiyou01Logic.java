package jp.ndsoft.carebase.cmn.api.logic;

import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.dto.FygJohoNextOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.RiyouMeisaiWeekinDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.RiyouTaisJohoOutAstr;
import jp.ndsoft.carebase.cmn.api.logic.dto.SetShortDayInDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanBetu1Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanBetu2Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanGaiFutanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanKouhiFutanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanOv30Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanRiyouKakuninMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanRiyouMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanSyafukuKeigenMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucServicePointMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucSvplanShortMapper;
import jp.ndsoft.carebase.common.dao.mybatis.ComTlcSvplanPointMapper;
import jp.ndsoft.carebase.common.dao.mybatis.ComTlcSvplanResultMapper;
import jp.ndsoft.carebase.common.dao.mybatis.ComTlcSvplanShortMapper;
import jp.ndsoft.carebase.common.dao.mybatis.TrsTucServicePointYMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlan;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanBetu1Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanBetu2Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanGaiFutanCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanKouhiFutanCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanOv30Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanRiyouCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanRiyouKakuninCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanSyafukuKeigenCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucServicePointCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucSvplanShortCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComTlcSvplanPointCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComTlcSvplanResultCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComTlcSvplanShortCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.TrsTucServicePointYCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo1ListByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo1ListOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo2SyuruiSougouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo2SyuruiSougouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3KohiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3KohiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3SyafukuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3SyafukuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBikoEntryByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBikoEntryOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnCpmvTlcSvplanPointDelByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnCpmvTlcSvplanPointDelOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnCpmvTlcSvplanResultDelByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnCpmvTlcSvplanResultDelOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnCpmvTlcSvplanShortDelByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnCpmvTlcSvplanShortDelOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnCpmvTucServicePointByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnCpmvTucServicePointOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnFygMstShouhinByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnFygMstShouhinOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnPlanOv30ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnPlanOv30OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnPrnBetuS3RiyouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnPrnBetuS3RiyouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSvRiteiGendo1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSvRiteiGendo1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucPlanRiyouKakuninByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucPlanRiyouKakuninOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucSvplanShortByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucSvplanShortOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnYojituListByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnYojituListOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTrsFServicePointYDelByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTrsFServicePointYDelOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanBetu1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanBetu2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanGaiFutanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanKouhiFutanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanOv30SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanRiyouKakuninSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanRiyouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanSyafukuKeigenSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucServicePointSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucSvPlanShortSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTlcSvPlanShortSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTlcSvplanPointSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTlcSvplanResultSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.FygMstShouhinFmsjSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.TrsTucServicePointYSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.smh.framework.util.AppUtil;

/**
 * KghCmnRiyou01Logicロジッククラス
 * 
 * <AUTHOR>
 */
@Component
public class KghCmnRiyou01Logic {
    /** クエリーのソート */
    private static final String CMN_TUC_PLAN_SORT = "shien_id DESC, userid DESC, yymm_ym DESC, yymm_d DESC";

    /** サービス利用票・別表ヘッダ */
    @Autowired
    private CmnTucPlanMapper cmnTucPlanMapper;

    /** サービス利用票明細 削除mapper */
    @Autowired
    private CmnTucPlanRiyouMapper cmnTucPlanRiyouMapper;

    /** 利用票別表 区分支給限度管理 削除mapper */
    @Autowired
    private CmnTucPlanBetu1Mapper cmnTucPlanBetu1Mapper;

    /** 利用票別表 種類別限度管理 削除mapper */
    @Autowired
    private CmnTucPlanBetu2Mapper cmnTucPlanBetu2Mapper;

    /** 公費負担額データ 削除mapper */
    @Autowired
    private CmnTucPlanKouhiFutanMapper cmnTucPlanKouhiFutanMapper;

    /** 保険外負担データ 削除mapper */
    @Autowired
    private CmnTucPlanGaiFutanMapper cmnTucPlanGaiFutanMapper;

    /** 30日超過データ 削除mapper */
    @Autowired
    private CmnTucPlanOv30Mapper cmnTucPlanOv30Mapper;

    /** 社福軽減データ 削除mapper */
    @Autowired
    private CmnTucPlanSyafukuKeigenMapper cmnTucPlanSyafukuKeigenMapper;

    /** 日割算定データ 削除mapper */
    @Autowired
    private CmnTucPlanRiyouKakuninMapper cmnTucPlanRiyouKakuninMapper;

    /** サービス種類別計画点数 削除mapper */
    @Autowired
    private CmnTucServicePointMapper cmnTucServicePointMapper;

    /** 短期入所利用日 削除mapper */
    @Autowired
    private CmnTucSvplanShortMapper cmnTucSvPlanShortMapper;

    /** 共通DB 計画削除 mapper */
    @Autowired
    private ComTlcSvplanResultMapper comTlcSvplanResultMapper;

    /** 共通DB サービス種類別計画点数 削除mapper */
    @Autowired
    private ComTlcSvplanPointMapper comTlcSvplanPointMapper;

    /** 共通DB 短期入所利用日 削除mapper */
    @Autowired
    private ComTlcSvplanShortMapper comTlcSvPlanShortMapper;

    /** 提供事業所連携様式F用 予定単位数保存テーブル 削除mapper */
    @Autowired
    private TrsTucServicePointYMapper trsTucServicePointYMapper;

    /** サービス利用票・別表ヘッダ 取得mapper */
    @Autowired
    private CmnTucPlanSelectMapper cmnTucPlanSelectMapper;

    /** サービス利用票明細 取得mapper */
    @Autowired
    private CmnTucPlanRiyouSelectMapper cmnTucPlanRiyouSelectMapper;

    /** 利用票別表 区分支給限度管理 取得mapper */
    @Autowired
    private CmnTucPlanBetu1SelectMapper cmnTucPlanBetu1SelectMapper;

    /** 利用票別表 種類別限度管理 取得mapper */
    @Autowired
    private CmnTucPlanBetu2SelectMapper cmnTucPlanBetu2SelectMapper;

    /** 公費負担額データ 取得mapper */
    @Autowired
    private CmnTucPlanKouhiFutanSelectMapper cmnTucPlanKouhiFutanSelectMapper;

    /** 保険外負担データ 取得mapper */
    @Autowired
    private CmnTucPlanGaiFutanSelectMapper cmnTucPlanGaiFutanSelectMapper;

    /** 30日超過データ 取得mapper */
    @Autowired
    private CmnTucPlanOv30SelectMapper cmnTucPlanOv30SelectMapper;

    /** 社福軽減データ 取得mapper */
    @Autowired
    private CmnTucPlanSyafukuKeigenSelectMapper cmnTucPlanSyafukuKeigenSelectMapper;

    /** 日割算定データ 取得mapper */
    @Autowired
    private CmnTucPlanRiyouKakuninSelectMapper cmnTucPlanRiyouKakuninSelectMapper;

    /** サービス種類別計画点数 取得mapper */
    @Autowired
    private CmnTucServicePointSelectMapper cmnTucServicePointSelectMapper;

    /** 短期入所利用日 取得mapper */
    @Autowired
    private CmnTucSvPlanShortSelectMapper cmnTucSvPlanShortSelectMapper;

    /** 共通DB 計画削除 取得mapper */
    @Autowired
    private ComTlcSvplanResultSelectMapper comTlcSvplanResultSelectMapper;

    /** 共通DB サービス種類別計画点数 取得mapper */
    @Autowired
    private ComTlcSvplanPointSelectMapper comTlcSvplanPointSelectMapper;

    /** 共通DB 短期入所利用日 取得mapper */
    @Autowired
    private ComTlcSvPlanShortSelectMapper comTlcSvPlanShortSelectMapper;

    /** 提供事業所連携様式F用 予定単位数保存テーブル 取得mapper */
    @Autowired
    private TrsTucServicePointYSelectMapper trsTucServicePointYSelectMapper;

    /** 商品マスタ(fyg_mst_shounin)から単価を取得する 取得mapper */
    @Autowired
    private FygMstShouhinFmsjSelectMapper fygMstShouhinFmsjSelectMapper;

    @Autowired
    private KghCmnF01Logic kghCmnF01Logic;

    @Autowired
    private NdsMidaLogic ndsMidaLogic;

    /**
     * 前月までの短期入所利用日数を取得し、値を返す.
     * 
     * @param shien  支援事業所id
     * @param userid 利用者id
     * @param teiYm  基準月（この月より前までの利用日数を取得したい）
     * @param stYm   認定開始日（この月以降の利用日数を取得したい）
     * @return 前月までの短期入所利用日数
     */
    public Integer getZengetuRuikei(Integer shien, Integer userid, String teiYm, String stYm) {
        Integer result = 0;
        Integer ruiseki = null;
        String startYm = stYm.substring(0, 7);

        if (startYm.compareTo(teiYm) >= 0) {
            // 同じか、認定開始日の方が提供年月より大きい場合には今月開始となるので「前月まで」は０
            return 0;
        }

        CmnTucPlanCriteria criteria = new CmnTucPlanCriteria();
        criteria.createCriteria().andShienIdEqualTo(shien).andUseridEqualTo(userid).andYymmYmLessThan(teiYm)
                .andYymmYmGreaterThanOrEqualTo(startYm);
        criteria.setOrderByClause(CMN_TUC_PLAN_SORT);
        List<CmnTucPlan> cmnTucPlanList = cmnTucPlanMapper.selectByCriteria(criteria);
        if (CollectionUtils.isNotEmpty(cmnTucPlanList)) {
            ruiseki = cmnTucPlanList.getFirst().getTRuiseki();
        }

        if (ruiseki != null && ruiseki >= 0) {
            result = ruiseki;
        } else {
            result = 0;
        }

        return result;
    }

    /**
     * f_kgh_cmn_set_adder_service_betu_dw : u3gk_dw_base 限定 : dmy_calc_sort に値を入れる
     * 
     * @param ads01 対象u3gk_dw_base
     * @param asds  年月日
     * @return な し
     * <AUTHOR>
     */
    public void setAdderServiceBetuDw(List<?> ads01, String asds) {
        try {
            String lsc, llSvj, lsSvcode;
            String lsSvtype = StringUtils.EMPTY;
            Integer liShogu;
            int llMax = CollectionUtils.size(ads01);
            if (llMax > 0) {
                Object dmyCalcSortObj = ads01.getFirst().getClass().getMethod(CommonConstants.GET_DMY_CALC_SORT)
                        .invoke(ads01.getFirst());
                boolean isInt = false;
                if (Integer.class.equals(dmyCalcSortObj.getClass())) {
                    isInt = true;
                }
                for (Object item : ads01) {
                    lsc = Objects.toString(item.getClass().getMethod(CommonConstants.GET_SCODE).invoke(item),
                            StringUtils.EMPTY);
                    llSvj = Objects.toString(item.getClass().getMethod(CommonConstants.GET_SV_JIGYO_ID).invoke(item),
                            StringUtils.EMPTY);
                    Integer dmyCalcSort = kghCmnF01Logic.checkAdderService(lsc, asds, CommonDtoUtil.strValToInt(llSvj));
                    invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                            isInt ? dmyCalcSort : CommonDtoUtil.objValToString(dmyCalcSort));
                    lsSvcode = ndsMidaLogic.fNdsMida(lsc, CommonConstants.INT_3, CommonConstants.INT_4);
                    liShogu = kghCmnF01Logic.isShoguuKaizenKasan(asds, lsc);
                    if (liShogu != null && liShogu > 0) {
                        Integer val = 2000 + liShogu;
                        invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                isInt ? val : CommonDtoUtil.objValToString(val));
                    } else {
                        if (kghCmnF01Logic.getKyoseiGenzan(asds, lsc) > 0) {
                            // 共生型サービス：基本サービスを所定単位数に含めるため基本サービスより下に表示されるよう修正
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 900 : CommonConstants.STR_900);
                        } else {
                            lsSvtype = StringUtils.left(lsc, 2);
                            if (asds.compareTo(CommonConstants.CREATION_DATE_20180401) >= 0
                                    && CommonConstants.SV_TYPE_76.equals(lsSvtype)) {
                                if (CommonConstants.SCODE_76411100.equals(lsc)
                                        || CommonConstants.SCODE_76411200.equals(lsc)
                                        || CommonConstants.SCODE_76411300.equals(lsc)
                                        || CommonConstants.SCODE_76411400.equals(lsc)) {
                                    // 定期巡回の同一建物減算は単位数かつ特別地域加算等の所定所定単位数に含めるため
                                    // 小計行と率加算の間に表示されるよう修正
                                    invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                            isInt ? CommonConstants.INT_0 : CommonConstants.STR_0);
                                }
                            }
                        }
                        // 感染症特例加算
                        if (kghCmnF01Logic.isKansenTokureiKasan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 800 : CommonConstants.STR_800);
                        }
                        // 訪問介護特定事業所加算Ⅴ
                        if (kghCmnF01Logic.isTokuteiJigyoKasan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 810 : CommonConstants.STR_810);
                        }
                        // 通所リハ継続減算
                        if (kghCmnF01Logic.isRihaKeizokuGenzan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 810 : CommonConstants.STR_810);
                        }
                        // 延長サービス加算が共生型に含まれるようにする必要がある
                        if (CommonConstants.SV_CD_STR_15.equals(lsSvtype)
                                || CommonConstants.SV_CD_STR_78.equals(lsSvtype)) {
                            if (CommonConstants.SV_CODE_LIST_4.contains(lsSvcode)) {
                                invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                        isInt ? 810 : CommonConstants.STR_810);
                            }
                        }
                        // 特別地域、小規模事業所加算が中山間より下に表示されるように指定
                        if (CommonConstants.SV_CD_STR_73.equals(lsSvtype)
                                || CommonConstants.SV_CD_STR_75.equals(lsSvtype)
                                || CommonConstants.SV_CD_STR_77.equals(lsSvtype)) {
                            if (kghCmnF01Logic.getChusankanRiyo(lsc, asds) == 1
                                    || kghCmnF01Logic.getSpecialPlaceNew(asds, lsc)) {
                                invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                        isInt ? 900 : CommonConstants.STR_900);
                            } else if (kghCmnF01Logic.getChusankanRiyo(lsc, asds) == 2) {
                                invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                        isInt ? 910 : CommonConstants.STR_910);
                            }
                        }
                        // 高齢者虐待防止未実施減算
                        if (kghCmnF01Logic.isGyakutaiBousiGenzan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 790 : CommonConstants.STR_790);
                        }
                        // 業務継続計画未策定減算
                        if (kghCmnF01Logic.isGyomuKeizokuGenzan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 800 : CommonConstants.STR_800);
                        }
                        // 過少サービス減算
                        if (kghCmnF01Logic.isKashouServiceGenzan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 810 : CommonConstants.STR_810);
                        }
                        // サテライト体制未整備減算
                        if (kghCmnF01Logic.isSatelliteMiseibiGenzan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? 820 : CommonConstants.STR_820);
                        }
                        // 身体拘束廃止未実施減算
                        if (kghCmnF01Logic.isShintaiKousokuGenzan(asds, lsc) > 0) {
                            invokeMethod(item, CommonConstants.SET_DMY_CALC_SORT,
                                    isInt ? CommonConstants.STR_780 : CommonConstants.STR_780);
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * uf_set_dmy_timeソートで使用する、ダミーの開始時間をセットする，親の時間帯を、子の加算にセットしていく
     * 
     * @param adw    対象u3gk_dw_base
     * @param String 提供年月
     * @return 固定値1
     * <AUTHOR>
     */
    public Integer ufSetDmyTime(List<Object> adw, String teiYm) {
        try {
            // 初期ソート
            // 初始排序 - リフレクションを使用して直接的なメソッド呼び出しの代わりにする
            adw.sort((o1, o2) -> {
                try {
                    // 順番に各フィールドを比較する
                    String oya1 = Objects.toString(o1.getClass().getMethod("getOyaLineNo").invoke(o1),
                            CommonConstants.TENTEN);
                    String oya2 = Objects.toString(o2.getClass().getMethod("getOyaLineNo").invoke(o2),
                            CommonConstants.TENTEN);
                    int cmp = oya1.compareTo(oya2);
                    if (cmp != 0)
                        return cmp;

                    String gsk1 = Objects.toString(o1.getClass().getMethod("getGouseiSikKbn").invoke(o1),
                            CommonConstants.TENTEN);
                    String gsk2 = Objects.toString(o2.getClass().getMethod("getGouseiSikKbn").invoke(o2),
                            CommonConstants.TENTEN);
                    cmp = gsk1.compareTo(gsk2);
                    if (cmp != 0)
                        return cmp;

                    String svc1 = Objects.toString(o1.getClass().getMethod("getSvcode").invoke(o1),
                            CommonConstants.TENTEN);
                    String svc2 = Objects.toString(o2.getClass().getMethod("getSvcode").invoke(o2),
                            CommonConstants.TENTEN);
                    cmp = svc1.compareTo(svc2);
                    if (cmp != 0)
                        return cmp;

                    String itm1 = Objects.toString(o1.getClass().getMethod("getSvItemCd").invoke(o1),
                            CommonConstants.TENTEN);
                    String itm2 = Objects.toString(o2.getClass().getMethod("getSvItemCd").invoke(o2),
                            CommonConstants.TENTEN);
                    return itm1.compareTo(itm2);
                } catch (Exception e) {
                    throw new RuntimeException("ソートエラー", e);
                }
            });
            // 0より大きいoyaLineNo値を収集しソート
            List<Integer> oyaLineNos = new ArrayList<>();
            for (Object item : adw) {
                String oyaVal = Objects.toString(item.getClass().getMethod("getOyaLineNo").invoke(item),
                        CommonConstants.TENTEN);
                if (!oyaVal.isEmpty()) {
                    int intVal = Integer.parseInt(oyaVal);
                    if (intVal > 0 && !oyaLineNos.contains(intVal)) {
                        oyaLineNos.add(intVal);
                    }
                }

            }
            Collections.sort(oyaLineNos);
            // 各oyaLineNo値の処理
            for (Integer oyaLineNo : oyaLineNos) {
                // adwから最初の条件一致レコードを参照用に抽出

                Object referenceDto = null;
                for (Object item : adw) {
                    String oyaVal = Objects.toString(item.getClass().getMethod("getOyaLineNo").invoke(item),
                            CommonConstants.TENTEN);
                    if (!oyaVal.isEmpty() && Integer.parseInt(oyaVal) == oyaLineNo) {
                        referenceDto = item;
                        break;
                    }
                }

                if (referenceDto == null)
                    continue;
                // 参照レコードの時間値を取得
                String startTime = CommonConstants.TENTEN;
                String endTime = CommonConstants.TENTEN;
                startTime = Objects.toString(referenceDto.getClass().getMethod("getSvStartTime").invoke(referenceDto),
                        CommonConstants.TENTEN);
                endTime = Objects.toString(referenceDto.getClass().getMethod("getSvEndTime").invoke(referenceDto),
                        CommonConstants.TENTEN);
                // 同じ行番号の全レコードを処理

                for (Object item : adw) {
                    String oyaVal = Objects.toString(item.getClass().getMethod("getOyaLineNo").invoke(item),
                            CommonConstants.TENTEN);
                    if (oyaVal.isEmpty() || Integer.parseInt(oyaVal) != oyaLineNo)
                        continue;

                    String scode = Objects.toString(item.getClass().getMethod("getScode").invoke(item),
                            CommonConstants.TENTEN);
                    if (kghCmnF01Logic.isShoguuKaizenKasan(teiYm + "/01", scode) > 0) {
                        item.getClass().getMethod("setDmySeqNo", String.class).invoke(item, CommonConstants.STR_NUM_9);
                    }
                    item.getClass().getMethod("setDmyStartTime", String.class).invoke(item, startTime);
                    item.getClass().getMethod("setDmyEndTime", String.class).invoke(item, endTime);
                }
            }
            // 最終ソート
            adw.sort((o1, o2) -> {
                try {
                    // 順番に各フィールドを比較する
                    String oya1 = Objects.toString(o1.getClass().getMethod("getOyaLineNo").invoke(o1),
                            CommonConstants.TENTEN);
                    String oya2 = Objects.toString(o2.getClass().getMethod("getOyaLineNo").invoke(o2),
                            CommonConstants.TENTEN);
                    int cmp = oya1.compareTo(oya2);
                    if (cmp != 0)
                        return cmp;

                    String gsk1 = Objects.toString(o1.getClass().getMethod("getGouseiSikKbn").invoke(o1),
                            CommonConstants.TENTEN);
                    String gsk2 = Objects.toString(o2.getClass().getMethod("getGouseiSikKbn").invoke(o2),
                            CommonConstants.TENTEN);
                    cmp = gsk1.compareTo(gsk2);
                    if (cmp != 0)
                        return cmp;

                    String svc1 = Objects.toString(o1.getClass().getMethod("getSvcode").invoke(o1),
                            CommonConstants.TENTEN);
                    String svc2 = Objects.toString(o2.getClass().getMethod("getSvcode").invoke(o2),
                            CommonConstants.TENTEN);
                    cmp = svc1.compareTo(svc2);
                    if (cmp != 0)
                        return cmp;

                    String itm1 = Objects.toString(o1.getClass().getMethod("getSvItemCd").invoke(o1),
                            CommonConstants.TENTEN);
                    String itm2 = Objects.toString(o2.getClass().getMethod("getSvItemCd").invoke(o2),
                            CommonConstants.TENTEN);
                    return itm1.compareTo(itm2);
                } catch (Exception e) {
                    throw new RuntimeException("ソートエラー", e);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 1;
    }

    /**
     * 利用票データの削除を行う
     * 
     * @param alShienId 支援事業所id
     * @param alUserid  利用者id
     * @param asYymm    削除月
     * @param asDd      削除日
     * @param shokuId   職員id
     * @return 戻り値
     * @throws ExclusiveException
     */
    public Integer ufPlanDel(Integer alShienId, Integer alUserid, String asYymm, String asDd, Integer shokuId)
            throws ExclusiveException {
        Integer result = 0;
        String lsHenkou = asYymm + CommonConstants.STR_DELIMITER + asDd;

        // 利用票ヘッダ
        KghCmnSvRiteiGendo1ByCriteriaInEntity kghCmnSvRiteiGendo1ByCriteriaInEntity = new KghCmnSvRiteiGendo1ByCriteriaInEntity();
        kghCmnSvRiteiGendo1ByCriteriaInEntity.setAlShi(alShienId);
        kghCmnSvRiteiGendo1ByCriteriaInEntity.setAlUsr(alUserid);
        kghCmnSvRiteiGendo1ByCriteriaInEntity.setAsYm(asYymm);
        kghCmnSvRiteiGendo1ByCriteriaInEntity.setAsD(asDd);
        List<KghCmnSvRiteiGendo1OutEntity> kghCmnSvRiteiGendo1OutEntityList = cmnTucPlanSelectMapper
                .findKghCmnSvRiteiGendo1ByCriteria(kghCmnSvRiteiGendo1ByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghCmnSvRiteiGendo1OutEntityList)) {
            for (KghCmnSvRiteiGendo1OutEntity outEntity : kghCmnSvRiteiGendo1OutEntityList) {
                // ■更新条件
                CmnTucPlanCriteria criteria = new CmnTucPlanCriteria();
                criteria.createCriteria().andShienIdEqualTo(outEntity.getShienId())
                        .andUseridEqualTo(outEntity.getUserid()).andYymmYmEqualTo(outEntity.getYymmYm())
                        .andYymmDEqualTo(outEntity.getYymmD());
                result = cmnTucPlanMapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }

        // 利用票別表明細（区分支給限度管理）データ
        KghCmnYojituListByCriteriaInEntity kghCmnYojituListByCriteriaInEntity = new KghCmnYojituListByCriteriaInEntity();
        kghCmnYojituListByCriteriaInEntity.setShienId(alShienId);
        kghCmnYojituListByCriteriaInEntity.setUserid(alUserid);
        kghCmnYojituListByCriteriaInEntity.setTeiYm(asYymm);
        kghCmnYojituListByCriteriaInEntity.setTeiYmd(asDd);
        List<KghCmnYojituListOutEntity> kghCmnYojituListOutEntityList = cmnTucPlanRiyouSelectMapper
                .findKghCmnYojituListByCriteria(kghCmnYojituListByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghCmnYojituListOutEntityList)) {
            for (KghCmnYojituListOutEntity outEntity : kghCmnYojituListOutEntityList) {
                // ■更新条件
                CmnTucPlanRiyouCriteria criteria = new CmnTucPlanRiyouCriteria();
                criteria.createCriteria().andShienIdEqualTo(alShienId).andUseridEqualTo(alUserid)
                        .andYymmYmEqualTo(asYymm).andYymmDEqualTo(asDd).andSvJigyoIdEqualTo(outEntity.getSvJigyoId())
                        .andSvItemCdEqualTo(outEntity.getSvItemCd()).andEdaNoEqualTo(outEntity.getEdaNo());
                result = cmnTucPlanRiyouMapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }

        // 利用票別表明細（区分支給限度管理）データ
        KghCmnBeppyo1ListByCriteriaInEntity kghCmnBeppyo1ListByCriteriaInEntity = new KghCmnBeppyo1ListByCriteriaInEntity();
        kghCmnBeppyo1ListByCriteriaInEntity.setAlShienId(alShienId);
        kghCmnBeppyo1ListByCriteriaInEntity.setAlUserid(alUserid);
        kghCmnBeppyo1ListByCriteriaInEntity.setAsYymmYm(asYymm);
        kghCmnBeppyo1ListByCriteriaInEntity.setAsYymmD(asDd);
        List<KghCmnBeppyo1ListOutEntity> kghCmnBeppyo1ListOutEntityList = cmnTucPlanBetu1SelectMapper
                .findKghCmnBeppyo1ListByCriteria(kghCmnBeppyo1ListByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghCmnBeppyo1ListOutEntityList)) {
            for (KghCmnBeppyo1ListOutEntity outEntity : kghCmnBeppyo1ListOutEntityList) {
                // ■更新条件
                CmnTucPlanBetu1Criteria criteria = new CmnTucPlanBetu1Criteria();
                criteria.createCriteria().andShienIdEqualTo(alShienId).andUseridEqualTo(alUserid)
                        .andYymmYmEqualTo(asYymm).andYymmDEqualTo(asDd).andSvJigyoIdEqualTo(outEntity.getSvJigyoId())
                        .andSvItemCdEqualTo(outEntity.getSvItemCd()).andEdaNoEqualTo(outEntity.getEdaNo());
                result = cmnTucPlanBetu1Mapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }

        // 利用票別表明細（種類別限度管理）データ
        KghCmnBeppyo2SyuruiSougouByCriteriaInEntity kghCmnBeppyo2SyuruiSougouByCriteriaInEntity = new KghCmnBeppyo2SyuruiSougouByCriteriaInEntity();
        kghCmnBeppyo2SyuruiSougouByCriteriaInEntity.setAlShi(alShienId);
        kghCmnBeppyo2SyuruiSougouByCriteriaInEntity.setAlUsr(alUserid);
        kghCmnBeppyo2SyuruiSougouByCriteriaInEntity.setAsYm(asYymm);
        kghCmnBeppyo2SyuruiSougouByCriteriaInEntity.setAsDd(asDd);
        List<KghCmnBeppyo2SyuruiSougouOutEntity> kghCmnBeppyo2SyuruiSougouOutEntityList = cmnTucPlanBetu2SelectMapper
                .findKghCmnBeppyo2SyuruiSougouByCriteria(kghCmnBeppyo2SyuruiSougouByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghCmnBeppyo2SyuruiSougouOutEntityList)) {
            for (KghCmnBeppyo2SyuruiSougouOutEntity outEntity : kghCmnBeppyo2SyuruiSougouOutEntityList) {
                // ■更新条件
                CmnTucPlanBetu2Criteria criteria = new CmnTucPlanBetu2Criteria();
                criteria.createCriteria().andShienIdEqualTo(outEntity.getShienId())
                        .andUseridEqualTo(outEntity.getUserid()).andYymmYmEqualTo(outEntity.getYymmYm())
                        .andYymmDEqualTo(outEntity.getYymmD());
                result = cmnTucPlanBetu2Mapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }

        // 公費負担額データ
        KghCmnBeppyo3KohiByCriteriaInEntity kghCmnBeppyo3KohiByCriteriaInEntity = new KghCmnBeppyo3KohiByCriteriaInEntity();
        kghCmnBeppyo3KohiByCriteriaInEntity.setAlShienId(alShienId);
        kghCmnBeppyo3KohiByCriteriaInEntity.setAlUserid(alUserid);
        kghCmnBeppyo3KohiByCriteriaInEntity.setAsYymmYm(asYymm);
        kghCmnBeppyo3KohiByCriteriaInEntity.setAsYymmD(asDd);
        List<KghCmnBeppyo3KohiOutEntity> kghCmnBeppyo3KohiOutEntityList = cmnTucPlanKouhiFutanSelectMapper
                .findKghCmnBeppyo3KohiByCriteria(kghCmnBeppyo3KohiByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghCmnBeppyo2SyuruiSougouOutEntityList)) {
            for (KghCmnBeppyo3KohiOutEntity outEntity : kghCmnBeppyo3KohiOutEntityList) {
                // ■更新条件
                CmnTucPlanKouhiFutanCriteria criteria = new CmnTucPlanKouhiFutanCriteria();
                criteria.createCriteria().andShienIdEqualTo(alShienId).andUseridEqualTo(alUserid)
                        .andYymmYmEqualTo(asYymm).andYymmDEqualTo(asDd).andCodeEqualTo(outEntity.getCode())
                        .andSvJigyoIdEqualTo(outEntity.getSvJigyoId()).andSvtypeEqualTo(outEntity.getSvtype());
                result = cmnTucPlanKouhiFutanMapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }

        // 保険外負担データ
        KghCmnPrnBetuS3RiyouByCriteriaInEntity kghCmnPrnBetuS3RiyouByCriteriaInEntity = new KghCmnPrnBetuS3RiyouByCriteriaInEntity();
        kghCmnPrnBetuS3RiyouByCriteriaInEntity.setAlShienId(alShienId);
        kghCmnPrnBetuS3RiyouByCriteriaInEntity.setAlUserid(alUserid);
        kghCmnPrnBetuS3RiyouByCriteriaInEntity.setAsYymmYm(asYymm);
        kghCmnPrnBetuS3RiyouByCriteriaInEntity.setAsYymmD(asDd);
        List<KghCmnPrnBetuS3RiyouOutEntity> kghCmnPrnBetuS3RiyouOutEntityList = cmnTucPlanGaiFutanSelectMapper
                .findKghCmnPrnBetuS3RiyouByCriteria(kghCmnPrnBetuS3RiyouByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghCmnPrnBetuS3RiyouOutEntityList)) {
            for (KghCmnPrnBetuS3RiyouOutEntity outEntity : kghCmnPrnBetuS3RiyouOutEntityList) {
                // ■更新条件
                CmnTucPlanGaiFutanCriteria criteria = new CmnTucPlanGaiFutanCriteria();
                criteria.createCriteria().andShienIdEqualTo(alShienId).andUseridEqualTo(alUserid)
                        .andYymmYmEqualTo(asYymm).andYymmDEqualTo(asDd).andSeqNoEqualTo(outEntity.getSeqNo());
                result = cmnTucPlanGaiFutanMapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }

        // 30日超過データ
        KghCmnPlanOv30ByCriteriaInEntity kghCmnPlanOv30ByCriteriaInEntity = new KghCmnPlanOv30ByCriteriaInEntity();
        kghCmnPlanOv30ByCriteriaInEntity.setAlShi(alShienId);
        kghCmnPlanOv30ByCriteriaInEntity.setAlUsr(alUserid);
        kghCmnPlanOv30ByCriteriaInEntity.setAsYm(asYymm);
        kghCmnPlanOv30ByCriteriaInEntity.setAsYmd(asDd);
        List<KghCmnPlanOv30OutEntity> kghCmnPlanOv30OutEntityList = cmnTucPlanOv30SelectMapper
                .findKghCmnPlanOv30ByCriteria(kghCmnPlanOv30ByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghCmnPlanOv30OutEntityList)) {
            for (KghCmnPlanOv30OutEntity outEntity : kghCmnPlanOv30OutEntityList) {
                // ■更新条件
                CmnTucPlanOv30Criteria criteria = new CmnTucPlanOv30Criteria();
                criteria.createCriteria().andShienIdEqualTo(outEntity.getShienId())
                        .andUseridEqualTo(outEntity.getUserid()).andYymmYmEqualTo(outEntity.getYymmYm())
                        .andYymmDEqualTo(outEntity.getYymmD());
                result = cmnTucPlanOv30Mapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }

        // 社福軽減データ
        KghCmnBeppyo3SyafukuByCriteriaInEntity kghCmnBeppyo3SyafukuByCriteriaInEntity = new KghCmnBeppyo3SyafukuByCriteriaInEntity();
        kghCmnBeppyo3SyafukuByCriteriaInEntity.setAlShienId(alShienId);
        kghCmnBeppyo3SyafukuByCriteriaInEntity.setAlUserid(alUserid);
        kghCmnBeppyo3SyafukuByCriteriaInEntity.setAsYymmYm(asYymm);
        kghCmnBeppyo3SyafukuByCriteriaInEntity.setAsYymmD(asDd);
        List<KghCmnBeppyo3SyafukuOutEntity> kghCmnBeppyo3SyafukuOutEntityList = cmnTucPlanSyafukuKeigenSelectMapper
                .findKghCmnBeppyo3SyafukuByCriteria(kghCmnBeppyo3SyafukuByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghCmnBeppyo3SyafukuOutEntityList)) {
            for (KghCmnBeppyo3SyafukuOutEntity outEntity : kghCmnBeppyo3SyafukuOutEntityList) {
                // ■更新条件
                CmnTucPlanSyafukuKeigenCriteria criteria = new CmnTucPlanSyafukuKeigenCriteria();
                criteria.createCriteria().andShienIdEqualTo(alShienId).andUseridEqualTo(alUserid)
                        .andYymmYmEqualTo(asYymm).andYymmDEqualTo(asDd).andSvJigyoIdEqualTo(outEntity.getSvJigyoId())
                        .andSvShuCdEqualTo(outEntity.getSvShuCd()).andKubunEqualTo(outEntity.getKubun())
                        .andSakuKbnEqualTo(outEntity.getSakuKbn());
                result = cmnTucPlanSyafukuKeigenMapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }

        // 日割算定データ
        KghCmnTucPlanRiyouKakuninByCriteriaInEntity kghCmnTucPlanRiyouKakuninByCriteriaInEntity = new KghCmnTucPlanRiyouKakuninByCriteriaInEntity();
        kghCmnTucPlanRiyouKakuninByCriteriaInEntity.setShienId(alShienId);
        kghCmnTucPlanRiyouKakuninByCriteriaInEntity.setUserid(alUserid);
        kghCmnTucPlanRiyouKakuninByCriteriaInEntity.setTeiYm(asYymm);
        kghCmnTucPlanRiyouKakuninByCriteriaInEntity.setTeiYmd(asDd);
        List<KghCmnTucPlanRiyouKakuninOutEntity> kghCmnTucPlanRiyouKakuninOutEntityList = cmnTucPlanRiyouKakuninSelectMapper
                .findKghCmnTucPlanRiyouKakuninByCriteria(kghCmnTucPlanRiyouKakuninByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghCmnTucPlanRiyouKakuninOutEntityList)) {
            for (KghCmnTucPlanRiyouKakuninOutEntity outEntity : kghCmnTucPlanRiyouKakuninOutEntityList) {
                // ■更新条件
                CmnTucPlanRiyouKakuninCriteria criteria = new CmnTucPlanRiyouKakuninCriteria();
                criteria.createCriteria().andShienIdEqualTo(alShienId).andUseridEqualTo(alUserid)
                        .andYymmYmEqualTo(asYymm).andYymmDEqualTo(asDd).andSvJigyoIdEqualTo(outEntity.getSvJigyoId())
                        .andSvItemCdEqualTo(outEntity.getSvItemCd()).andEdaNoEqualTo(outEntity.getEdaNo());
                result = cmnTucPlanRiyouKakuninMapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }

        // サービス種類別計画点数
        KghCmnCpmvTucServicePointByCriteriaInEntity kghCmnCpmvTucServicePointByCriteriaInEntity = new KghCmnCpmvTucServicePointByCriteriaInEntity();
        kghCmnCpmvTucServicePointByCriteriaInEntity.setAlUsr(alUserid);
        kghCmnCpmvTucServicePointByCriteriaInEntity.setAsYmd(lsHenkou);
        List<KghCmnCpmvTucServicePointOutEntity> kghCmnCpmvTucServicePointOutEntityList = cmnTucServicePointSelectMapper
                .findKghCmnCpmvTucServicePointByCriteria(kghCmnCpmvTucServicePointByCriteriaInEntity);
        List<KghCmnCpmvTucServicePointOutEntity> outEntitiesList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(kghCmnCpmvTucServicePointOutEntityList)) {
            for (KghCmnCpmvTucServicePointOutEntity outEntity : kghCmnCpmvTucServicePointOutEntityList) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
                LocalDate inDate = LocalDate.parse(asYymm + CommonConstants.STR_DELIMITER + asDd, formatter);
                LocalDate sysDate = LocalDate.parse(CommonConstants.TEI_YM_200201 + CommonConstants.STRING_FIRST_DAY, formatter);
                // 支援事業所IDでの絞込み処理を復帰
                if (inDate.isAfter(sysDate)) {
                    if (StringUtils.isNotEmpty(CommonDtoUtil.objValToString(outEntity.getGaiNumber()))
                            || outEntity.getGaiNumber().equals(alShienId)) {
                        outEntitiesList.add(outEntity);
                    }
                }
                if (CollectionUtils.isNotEmpty(outEntitiesList)) {
                    // ■更新条件
                    CmnTucServicePointCriteria criteria = new CmnTucServicePointCriteria();
                    criteria.createCriteria().andUseridEqualTo(alUserid).andYymmYmEqualTo(lsHenkou)
                            .andHoujinIdEqualTo(outEntity.getHoujinId()).andShisetuIdEqualTo(outEntity.getShisetuId())
                            .andSvJigyoIdEqualTo(outEntity.getSvJigyoId()).andSvtypeEqualTo(outEntity.getSvtype());
                    result = cmnTucServicePointMapper.deleteByCriteria(criteria);
                    if (result != 1) {
                        throw new ExclusiveException();
                    }
                }
            }
        }

        // 備考欄
        KghCmnBikoEntryByCriteriaInEntity kghCmnBikoEntryByCriteriaInEntity = new KghCmnBikoEntryByCriteriaInEntity();
        kghCmnBikoEntryByCriteriaInEntity.setAlShienId(alShienId);
        kghCmnBikoEntryByCriteriaInEntity.setAlUserid(alUserid);
        kghCmnBikoEntryByCriteriaInEntity.setAsYymmYm(asYymm);
        kghCmnBikoEntryByCriteriaInEntity.setAsYymmD(asDd);
        List<KghCmnBikoEntryOutEntity> kghCmnBikoEntryOutEntityList = cmnTucPlanSelectMapper
                .findKghCmnBikoEntryByCriteria(kghCmnBikoEntryByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghCmnBikoEntryOutEntityList)) {
            for (KghCmnBikoEntryOutEntity outEntity : kghCmnBikoEntryOutEntityList) {
                // ■更新条件
                CmnTucPlanCriteria criteria = new CmnTucPlanCriteria();
                criteria.createCriteria().andShienIdEqualTo(outEntity.getShienId())
                        .andUseridEqualTo(outEntity.getUserid()).andYymmYmEqualTo(outEntity.getYymmYm())
                        .andYymmDEqualTo(outEntity.getYymmD());
                result = cmnTucPlanMapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }

        // 短期入所利用日
        KghCmnTucSvplanShortByCriteriaInEntity kghCmnTucSvplanShortByCriteriaInEntity = new KghCmnTucSvplanShortByCriteriaInEntity();
        kghCmnTucSvplanShortByCriteriaInEntity.setAlShien(alShienId);
        kghCmnTucSvplanShortByCriteriaInEntity.setAlUser(alUserid);
        kghCmnTucSvplanShortByCriteriaInEntity.setAsYymm(asYymm);
        kghCmnTucSvplanShortByCriteriaInEntity.setAsYmd(asDd);
        List<KghCmnTucSvplanShortOutEntity> kghCmnTucSvplanShortOutEntityList = cmnTucSvPlanShortSelectMapper
                .findKghCmnTucSvplanShortByCriteria(kghCmnTucSvplanShortByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghCmnTucSvplanShortOutEntityList)) {
            for (KghCmnTucSvplanShortOutEntity outEntity : kghCmnTucSvplanShortOutEntityList) {
                // ■更新条件
                CmnTucSvplanShortCriteria criteria = new CmnTucSvplanShortCriteria();
                criteria.createCriteria().andShienIdEqualTo(outEntity.getShienId())
                        .andUseridEqualTo(outEntity.getUserid()).andYymmYmEqualTo(outEntity.getYymmYm())
                        .andHenkouYmdEqualTo(outEntity.getHenkouYmd());
                result = cmnTucSvPlanShortMapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }

        // 共通DB 計画削除
        KghCmnCpmvTlcSvplanResultDelByCriteriaInEntity kghCmnCpmvTlcSvplanResultDelByCriteriaInEntity = new KghCmnCpmvTlcSvplanResultDelByCriteriaInEntity();
        kghCmnCpmvTlcSvplanResultDelByCriteriaInEntity.setAlShien(alShienId);
        kghCmnCpmvTlcSvplanResultDelByCriteriaInEntity.setAlUser(alUserid);
        kghCmnCpmvTlcSvplanResultDelByCriteriaInEntity.setAsYymm(lsHenkou);
        List<KghCmnCpmvTlcSvplanResultDelOutEntity> kghCmnCpmvTlcSvplanResultDelOutEntityList = comTlcSvplanResultSelectMapper
                .findKghCmnCpmvTlcSvplanResultDelByCriteria(kghCmnCpmvTlcSvplanResultDelByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghCmnCpmvTlcSvplanResultDelOutEntityList)) {
            for (KghCmnCpmvTlcSvplanResultDelOutEntity outEntity : kghCmnCpmvTlcSvplanResultDelOutEntityList) {
                outEntity.setTimeStmp(AppUtil.getSystemTimeStamp());
                outEntity.setDelFlg(CommonConstants.NUMBER_1);
                outEntity.setShokuId(shokuId);
                // ■更新条件
                ComTlcSvplanResultCriteria criteria = new ComTlcSvplanResultCriteria();
                criteria.createCriteria().andUseridEqualTo(alUserid).andYymmYmdEqualTo(lsHenkou)
                        .andHoujinIdEqualTo(outEntity.getHoujinId()).andShisetuIdEqualTo(outEntity.getShisetuId())
                        .andSvJigyoIdEqualTo(outEntity.getSvJigyoId()).andItemcodeEqualTo(outEntity.getItemcode())
                        .andEdanoEqualTo(outEntity.getEdano())
                        .andSwchFlgEqualTo(outEntity.getSwchFlg())
                        .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
                result = comTlcSvplanResultMapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }

        // 共通DB サービス種類別計画点数
        KghCmnCpmvTlcSvplanPointDelByCriteriaInEntity kghCmnCpmvTlcSvplanPointDelByCriteriaInEntity = new KghCmnCpmvTlcSvplanPointDelByCriteriaInEntity();
        kghCmnCpmvTlcSvplanPointDelByCriteriaInEntity.setAlUser(alUserid);
        kghCmnCpmvTlcSvplanPointDelByCriteriaInEntity.setAlShien(alShienId);
        kghCmnCpmvTlcSvplanPointDelByCriteriaInEntity.setAsYymm(lsHenkou);
        List<KghCmnCpmvTlcSvplanPointDelOutEntity> kghCmnCpmvTlcSvplanPointDelOutEntityList = comTlcSvplanPointSelectMapper
                .findKghCmnCpmvTlcSvplanPointDelByCriteria(kghCmnCpmvTlcSvplanPointDelByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghCmnCpmvTlcSvplanPointDelOutEntityList)) {
            for (KghCmnCpmvTlcSvplanPointDelOutEntity outEntity : kghCmnCpmvTlcSvplanPointDelOutEntityList) {
                // ■更新条件
                ComTlcSvplanPointCriteria criteria = new ComTlcSvplanPointCriteria();
                criteria.createCriteria().andUseridEqualTo(alUserid).andYymmYmdEqualTo(lsHenkou)
                        .andHoujinIdEqualTo(outEntity.getHoujinId()).andShisetuIdEqualTo(outEntity.getShisetuId())
                        .andSvJigyoIdEqualTo(outEntity.getSvJigyoId()).andSvtypeEqualTo(outEntity.getSvtype())
                        .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
                result = comTlcSvplanPointMapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }

        // 共通DB 短期入所利用日
        KghCmnCpmvTlcSvplanShortDelByCriteriaInEntity kghCmnCpmvTlcSvplanShortDelByCriteriaInEntity = new KghCmnCpmvTlcSvplanShortDelByCriteriaInEntity();
        kghCmnCpmvTlcSvplanShortDelByCriteriaInEntity.setAlUser(alUserid);
        kghCmnCpmvTlcSvplanShortDelByCriteriaInEntity.setAlShien(alShienId);
        kghCmnCpmvTlcSvplanShortDelByCriteriaInEntity.setAsYmd(lsHenkou);
        List<KghCmnCpmvTlcSvplanShortDelOutEntity> kghCmnCpmvTlcSvplanShortDelOutEntityList = comTlcSvPlanShortSelectMapper
                .findKghCmnCpmvTlcSvplanShortDelByCriteria(kghCmnCpmvTlcSvplanShortDelByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghCmnCpmvTlcSvplanShortDelOutEntityList)) {
            for (KghCmnCpmvTlcSvplanShortDelOutEntity outEntity : kghCmnCpmvTlcSvplanShortDelOutEntityList) {
                // ■更新条件
                ComTlcSvplanShortCriteria criteria = new ComTlcSvplanShortCriteria();
                criteria.createCriteria().andUseridEqualTo(outEntity.getUserid()).andYymmYmdEqualTo(lsHenkou)
                        .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
                result = comTlcSvPlanShortMapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }

        // 提供事業所連携様式F用 予定単位数保存テーブル
        KghTrsFServicePointYDelByCriteriaInEntity kghTrsFServicePointYDelByCriteriaInEntity = new KghTrsFServicePointYDelByCriteriaInEntity();
        kghTrsFServicePointYDelByCriteriaInEntity.setAlUserid(alUserid);
        kghTrsFServicePointYDelByCriteriaInEntity.setAlShienId(alShienId);
        kghTrsFServicePointYDelByCriteriaInEntity.setAsYymmYm(lsHenkou);
        List<KghTrsFServicePointYDelOutEntity> kghTrsFServicePointYDelOutEntityList = trsTucServicePointYSelectMapper
                .findKghTrsFServicePointYDelByCriteria(kghTrsFServicePointYDelByCriteriaInEntity);
        if (CollectionUtils.isNotEmpty(kghTrsFServicePointYDelOutEntityList)) {
            for (KghTrsFServicePointYDelOutEntity outEntity : kghTrsFServicePointYDelOutEntityList) {
                // ■更新条件
                TrsTucServicePointYCriteria criteria = new TrsTucServicePointYCriteria();
                criteria.createCriteria().andUseridEqualTo(alUserid).andYymmYmEqualTo(lsHenkou)
                        .andSvJigyoIdEqualTo(outEntity.getSvJigyoId()).andSvtypeEqualTo(outEntity.getSvtype());
                result = trsTucServicePointYMapper.deleteByCriteria(criteria);
                if (result != 1) {
                    throw new ExclusiveException();
                }
            }
        }
        return result;
    }

    /**
     * uf_set_sort_no : ソート順を割り振ります
     * 
     * @param adw        ソート対象リスト
     * @param aiSortType ソートタイプ 0：0埋め 1：行順
     * @return ソート結果
     * <AUTHOR>
     * @throws Exception Exception
     */
    public Integer setSortNo(List<Object> adw, Integer aiSortType) {
        Integer liRet = 0;
        try {
            if (CollectionUtils.isNotEmpty(adw)) {
                // ソートタイプの判定
                if (aiSortType != null && CommonConstants.NUMBER_0 == aiSortType) {
                    // 0埋め
                    for (Object target : adw) {
                        invokeMethod(target, CommonConstants.SET_SORT_NO, CommonConstants.STR_0);
                    }
                } else {
                    // 行順
                    for (int i = 1; i <= adw.size(); i++) {
                        invokeMethod(adw.get(i), CommonConstants.SET_SORT_NO, CommonDtoUtil.objValToString(i));
                    }
                }
                liRet = CommonConstants.NUMBER_1;
            } else {
                liRet = CommonConstants.INT_MINUS_1;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return liRet;
    }

    /**
     * エンティティクラスのSetterメソッドを実行する
     * 
     * @param target     DAOエンティティクラス
     * @param methodName メソッド名
     * @param args       引数
     */
    private void invokeMethod(Object target, String methodName, Object... args) {
        try {
            // 引数の型を取得
            Class<?>[] paramTypes = new Class<?>[args.length];
            for (int i = 0; i < args.length; i++) {
                paramTypes[i] = args[i].getClass();
            }

            // メソッドを取得
            Method method = target.getClass().getMethod(methodName, paramTypes);

            // メソッドを実行
            method.invoke(target, args);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke method: " + methodName, e);
        }
    }

    /**
     * ue_chk_riyou_meisai_week : 利用票明細（マージ前）にてデータチェック及び補正を行う。
     * 
     * @param adw          調査対象の明細DW
     * @param adwHead      ヘッダDW
     * @param plRow        対象となった行
     * @param asMsg        メッセージ
     * @param aiChkDefault 短期補正区分
     * @return 1:時間が重複しているサービスあり 2:短期入所利用中の訪問･通所サービスあり 3:短期入所利用中の短期入所サービスあり
     *         4:訪問介護提供時間間隔が2時間以内 5:回数制限を超過 6:利用者の要介護度とサービスが不整合 7:同時取得不可加算あり
     * <AUTHOR>
     */
    public Integer ueChkRiyouMeisaiWeek(List<RiyouMeisaiWeekinDto> adw, List<Object> adwHead, Integer plRow,
            String asMsg, Integer aiChkDefault) {
        Integer liRet = 0;

        Integer liTankiHosei = 0;
        Integer liFlag = 0;
        Integer liChofuku = 0;
        Integer liTankiHosei2 = 0;
        Integer liShortstayIoDay = 1;
        Integer[] iiShortRiyou = new Integer[32];
        if (aiChkDefault == null)
            aiChkDefault = 0;
        if (aiChkDefault != 0) {
            if (aiChkDefault > 0) {
                liTankiHosei = aiChkDefault;
            } else {
                liTankiHosei = 1;
            }
            if (liTankiHosei != 0 && liTankiHosei != 5) {
                // uf_set_short_day共通関数は未実装です
                List<Integer> shortRiyouList = setShortDay(adw,
                        AppUtil.getSystemTimeStamp().toLocalDateTime().format(DateTimeFormatter.ofPattern("yyyy/MM")));
                // 取得iiShortRiyou
                shortRiyouList.toArray(iiShortRiyou);
            }
        }
        int llRowcnt = adw.size();
        for (int i = 0; i < llRowcnt; i++) {
            RiyouMeisaiWeekinDto item = adw.get(i);
            String ls_svt = CommonConstants.TENTEN;
            // リフレクションでサービスタイプを取得する
            ls_svt = item.getServiceType();
            // 対象サービス種別をチェック
            boolean isTargetService = "11".equals(ls_svt) || "12".equals(ls_svt) || "13".equals(ls_svt)
                    || "14".equals(ls_svt) || "15".equals(ls_svt) || "16".equals(ls_svt) || "61".equals(ls_svt)
                    || "62".equals(ls_svt) || "63".equals(ls_svt) || "64".equals(ls_svt) || "65".equals(ls_svt)
                    || "66".equals(ls_svt) || "71".equals(ls_svt) || "72".equals(ls_svt) || "74".equals(ls_svt)
                    || "76".equals(ls_svt) || "78".equals(ls_svt) || "A1".equals(ls_svt) || "A2".equals(ls_svt)
                    || "A3".equals(ls_svt) || "A4".equals(ls_svt) || "A5".equals(ls_svt) || "A6".equals(ls_svt)
                    || "A7".equals(ls_svt) || "A8".equals(ls_svt);

            if (isTargetService) {
                for (int day = 1; day <= 31; day++) {
                    // 当日の値を取得 (y_day01, y_day02... 相当の操作)
                    // リフレクションで日付値を取得する
                    int dayValue = item.getDay();
                    if (iiShortRiyou[day] == 1 && dayValue >= 1)
                        liFlag = 1;
                    if (liFlag == 1) {
                        if (liChofuku == 0) {
                            // 補正処理
                            // 1 :全て優先する
                            // 2 :入所日は優先しない。
                            // 3 :退所日は優先しない。
                            // 4 :入所日と退所日を優先しない。
                            // 5,0:キャンセル
                            if (liTankiHosei >= 1 && liTankiHosei <= 4) {
                                // 補正画面による指定あり(従来ﾊﾟﾀｰﾝ)
                                if (aiChkDefault == 0) {
                                    liTankiHosei2 = liTankiHosei;// 補正画面での選択
                                    // 補正画面による指定なし(訪問/通所系別)
                                } else {
                                    // サービス種別に基づいてポリシーを自動的に選択する
                                    switch (ls_svt) {
                                        // 訪問系サービス（居宅サービス）​
                                        case "11":
                                        case "12":
                                        case "13":
                                        case "14":
                                        case "61":
                                        case "62":
                                        case "63":
                                        case "64":
                                        case "71":
                                        case "76":
                                        case "A1":
                                        case "A2":
                                        case "A3":
                                        case "A4":
                                            liTankiHosei2 = 4; // 初日・最終日を無視
                                            break;

                                        // 通所系サービス（施設型サービス）
                                        case "15":
                                        case "16":
                                        case "65":
                                        case "66":
                                        case "72":
                                        case "74":
                                        case "78":
                                        case "A5":
                                        case "A6":
                                        case "A7":
                                        case "A8":
                                            liTankiHosei2 = 1; // すべて優先
                                            break;

                                        // デフォルト方針（業務要件に基づき設定）
                                        default:
                                            liTankiHosei2 = 1; // 保守的方針：すべて優先
                                            break;
                                    }
                                }
                            }

                            liShortstayIoDay = this.getShortstayIoDay(day, iiShortRiyou);
                            // 短期入所ｻｰﾋﾞｽ優先処理時の入退所日処理区分
                            switch (liShortstayIoDay) {
                                case 1:
                                    if (liTankiHosei2 == 1 || liTankiHosei2 == 3) {
                                        item.setDay(CommonConstants.NUMBER_ZERO);
                                    }
                                    break;
                                case 2:
                                    if (liTankiHosei2 == 1 || liTankiHosei2 == 2) {
                                        item.setDay(CommonConstants.NUMBER_ZERO);
                                    }
                                    break;
                                case 3:
                                    if (liTankiHosei2 == 1) {
                                        item.setDay(CommonConstants.NUMBER_ZERO);
                                    }
                                    break;
                                case 4:
                                    item.setDay(CommonConstants.NUMBER_ZERO);
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }
            }
        }
        // リフレクションを使用したソート（例外を直接スロー）
        adw.sort(Comparator.comparing(RiyouMeisaiWeekinDto::getShienId));

        return liRet;
    }

    /**
     * uf_get_shortstay_io_day : 該当日が短期入所サービスの入退所日かどうかを判別する
     * 
     * @param ai_day         日付
     * @param ii_short_riyou 短期入所利用日の配列
     * @return 0:非該当, 1:入所日, 2:退所日, 3:入退所日, 4:入所中
     * <AUTHOR>
     */
    public Integer getShortstayIoDay(Integer aiDay, Integer[] iiShortRiyou) {
        Integer liIDay = CommonConstants.INT_0;
        Integer liODay = CommonConstants.INT_0;
        Integer liRet = CommonConstants.INT_0;
        if (iiShortRiyou != null && iiShortRiyou.length >= aiDay
                && iiShortRiyou[aiDay - CommonConstants.INT_1] == CommonConstants.INT_1) {
            // 入所日の判別
            if (aiDay == CommonConstants.INT_1) {
                liIDay = CommonConstants.INT_1;
            } else if (aiDay > CommonConstants.INT_1) {
                if (iiShortRiyou[aiDay - CommonConstants.INT_2] == CommonConstants.INT_0) {
                    liIDay = CommonConstants.INT_1;// 入所日
                } else {
                    liIDay = CommonConstants.INT_0;// 入所中
                }
            }
            // 退所日の判別
            if (aiDay == CommonConstants.INT_31) {
                liODay = CommonConstants.INT_1;
            } else if (aiDay < CommonConstants.INT_31) {
                if (iiShortRiyou[aiDay] == CommonConstants.INT_0) {
                    liODay = CommonConstants.INT_1;// 退所日
                } else {
                    liODay = CommonConstants.INT_0;// 入所中
                }
            }
        } else {
            // 非該当
            liIDay = CommonConstants.INT_2;
            liODay = CommonConstants.INT_2;
        }
        // 非該当
        if (liIDay == CommonConstants.INT_2 && liODay == CommonConstants.INT_2) {
            liRet = CommonConstants.INT_0;
        }
        // 入所日
        else if (liIDay == CommonConstants.INT_1 && liODay == CommonConstants.INT_0) {
            liRet = CommonConstants.INT_1;
        }
        // 退所日
        else if (liIDay == CommonConstants.INT_0 && liODay == CommonConstants.INT_1) {
            liRet = CommonConstants.INT_2;
        }
        // 入退所日
        else if (liIDay == CommonConstants.INT_1 && liODay == CommonConstants.INT_1) {
            liRet = CommonConstants.INT_3;
        }
        // 入所中
        else {
            liRet = CommonConstants.INT_4;
        }
        return liRet;
    }

    /**
     * f_kgh_cmn_get_fyg_joho_next : 商品マスタ(fyg_mst_shounin)から単価を取得する（削除フラグを見ない）
     * 
     * @param alFygId : 商品マスタid
     * <AUTHOR> 李晨昊
     */
    public FygJohoNextOutDto kghCmnGetFygJohoNext(Integer alFygId, String asYmd) {
        FygJohoNextOutDto outDto = new FygJohoNextOutDto();

        Integer llRet = CommonConstants.NUMBER_0;

        // ll_ret = lds_mst_shouhin.retrieve( {al_fyg_id}, as_ymd )
        KghCmnFygMstShouhinByCriteriaInEntity fygInDto = new KghCmnFygMstShouhinByCriteriaInEntity();
        fygInDto.setAlShouhinIdList(Arrays.asList(alFygId));
        fygInDto.setAsDateYmd(asYmd);
        List<KghCmnFygMstShouhinOutEntity> fygOutList = fygMstShouhinFmsjSelectMapper
                .findKghCmnFygMstShouhinByCriteria(fygInDto);
        llRet = CollectionUtils.size(fygOutList);

        RiyouTaisJohoOutAstr riyouTaisJohoOutAstr = new RiyouTaisJohoOutAstr();
        if (CollectionUtils.size(fygOutList) > 0) {
            KghCmnFygMstShouhinOutEntity headInfo = fygOutList.getFirst();
            // astr_out.l_shouhin_id = lds_mst_shouhin.getItemNumber( 1, "shouhin_id" )
            riyouTaisJohoOutAstr.setShouhinId(headInfo.getShouhinId());
            // astr_out.l_itemcode = lds_mst_shouhin.getItemNumber( 1, "itemcode" )
            riyouTaisJohoOutAstr.setItemcode(headInfo.getItemcode());
            // astr_out.s_shouhin_knj = lds_mst_shouhin.getItemString( 1, "shouhin_knj" )
            riyouTaisJohoOutAstr.setShouhinKnj(headInfo.getShouhinKnj());
            // astr_out.s_ryaku_knj = lds_mst_shouhin.getItemString( 1, "ryaku_knj" )
            riyouTaisJohoOutAstr.setRyakuKnj(headInfo.getRyakuKnj());
            // astr_out.s_kataban = lds_mst_shouhin.getItemString( 1, "kataban" )
            riyouTaisJohoOutAstr.setKataban(headInfo.getKataban());
            // astr_out.s_tekiyou_code = lds_mst_shouhin.getItemString( 1, "tekiyou_code" )
            riyouTaisJohoOutAstr.setTekiyouCode(headInfo.getTekiyouCode());
            // astr_out.l_sort = lds_mst_shouhin.getItemNumber( 1, "sort" )
            riyouTaisJohoOutAstr.setSort(headInfo.getSort());
            // astr_out.l_stop_flg = lds_mst_shouhin.getItemNumber( 1, "stop_flg" )
            riyouTaisJohoOutAstr.setStopFlg(headInfo.getStopFlg());
            // astr_out.l_shumoku_id = lds_mst_shouhin.getItemNumber( 1, "shumoku_id" )
            riyouTaisJohoOutAstr.setShumokuId(headInfo.getShumokuId());
        } else {
            // 初期値設定
            // astr_out.l_shouhin_id = 0
            riyouTaisJohoOutAstr.setShouhinId(CommonConstants.NUMBER_0);
            // astr_out.l_itemcode = 0
            riyouTaisJohoOutAstr.setItemcode(CommonConstants.NUMBER_0);
            // astr_out.s_shouhin_knj = ""
            riyouTaisJohoOutAstr.setShouhinKnj(StringUtils.EMPTY);
            // astr_out.s_ryaku_knj = ""
            riyouTaisJohoOutAstr.setRyakuKnj(StringUtils.EMPTY);
            // astr_out.s_kataban = ""
            riyouTaisJohoOutAstr.setKataban(StringUtils.EMPTY);
            // astr_out.s_tekiyou_code = ""
            riyouTaisJohoOutAstr.setTekiyouCode(StringUtils.EMPTY);
            // astr_out.l_sort = 0
            riyouTaisJohoOutAstr.setSort(CommonConstants.NUMBER_0);
            // astr_out.l_stop_flg = 0
            riyouTaisJohoOutAstr.setStopFlg(CommonConstants.NUMBER_0);
            // astr_out.l_shumoku_id = 0
            riyouTaisJohoOutAstr.setShumokuId(CommonConstants.NUMBER_0);
        }

        if (riyouTaisJohoOutAstr.getShouhinId() == null) {
            // astr_out.l_shouhin_id = 0
            riyouTaisJohoOutAstr.setShouhinId(CommonConstants.NUMBER_0);
        }

        if (riyouTaisJohoOutAstr.getItemcode() == null) {
            // astr_out.l_itemcode = 0
            riyouTaisJohoOutAstr.setItemcode(CommonConstants.NUMBER_0);
        }

        if (riyouTaisJohoOutAstr.getShouhinKnj() == null) {
            // astr_out.s_shouhin_knj = ""
            riyouTaisJohoOutAstr.setShouhinKnj(StringUtils.EMPTY);
        }

        if (riyouTaisJohoOutAstr.getRyakuKnj() == null) {
            // astr_out.s_ryaku_knj = ""
            riyouTaisJohoOutAstr.setRyakuKnj(StringUtils.EMPTY);
        }

        if (riyouTaisJohoOutAstr.getKataban() == null) {
            // astr_out.s_kataban = ""
            riyouTaisJohoOutAstr.setKataban(StringUtils.EMPTY);
        }

        if (riyouTaisJohoOutAstr.getTekiyouCode() == null) {
            // astr_out.s_tekiyou_code = ""
            riyouTaisJohoOutAstr.setTekiyouCode(StringUtils.EMPTY);
        }

        if (riyouTaisJohoOutAstr.getSort() == null) {
            // astr_out.l_sort = 0
            riyouTaisJohoOutAstr.setSort(CommonConstants.NUMBER_0);
        }

        if (riyouTaisJohoOutAstr.getStopFlg() == null) {
            // astr_out.l_stop_flg = 0
            riyouTaisJohoOutAstr.setStopFlg(CommonConstants.NUMBER_0);
        }

        if (riyouTaisJohoOutAstr.getShumokuId() == null) {
            // astr_out.l_shumoku_id = 0
            riyouTaisJohoOutAstr.setShumokuId(CommonConstants.NUMBER_0);
        }

        outDto.setLlRet(llRet);
        outDto.setRiyouTaisJohoOutAstr(riyouTaisJohoOutAstr);
        return outDto;
    }

    /**
     * uf_set_short_day : 短期入所利用日の配列にセットさせる
     * 
     * @param adw    調査対象の u3gk_dw_base（利用票ｄｗ）
     * @param yymmYm サービス提供年月
     * @return List<Integer> shortday
     * <AUTHOR>
     */
    public List<Integer> setShortDay(List<? extends SetShortDayInDto> shortDayInDtoList, String yymmYm) {
        List<Integer> shortRiyouList = new ArrayList<>();
        // 短期入所利用時のヘッダ色変更
        // 初期化
        for (int i = 0; i < 31; i++) {
            shortRiyouList.add(CommonConstants.NUMBER_ZERO);
        }
        Integer llRowcnt = shortDayInDtoList.size();
        if (llRowcnt > 0) {
            for (SetShortDayInDto shortDayInDto : shortDayInDtoList) {
                String svtype = shortDayInDto.getSvtype();
                if (svtype != null && CommonConstants.SV_TYPE_CHECK_LIST_2.contains(svtype)) {
                    String lsScode = shortDayInDto.getScode();
                    String lsSvtype = svtype.substring(CommonConstants.NUMBER_ZERO, CommonConstants.NUMBER_TWO);
                    if (("22".equals(lsSvtype) || "25".equals(lsSvtype) || "2A".equals(lsSvtype)
                            || "2B".equals(lsSvtype))
                            && kghCmnF01Logic.chkKasanException(lsScode, yymmYm + "/01") == 1) {
                        // 何もしない
                    } else {
                        for (int day = 0; day < 31; day++) {

                            String fieldName = String.format("Yday%02d", day + CommonConstants.NUMBER_1);
                            String methodName = "get" + fieldName;
                            try {
                                Method method = shortDayInDto.getClass().getMethod(methodName);
                                int value = (int) method.invoke(shortDayInDto);

                                if (value > 0) {
                                    shortRiyouList.set(day, CommonConstants.NUMBER_1);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                        }
                    }
                }
            }
        }
        return shortRiyouList;
    }
}
