package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;
import java.util.List;

import jp.ndsoft.smh.framework.global.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GUI00892_チェック項目画面
 * 
 * @description
 *              チェック項目詳細情報
 *              チェック項目詳細情報エンティティ
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Gui00892CheckItemInfo implements Serializable, IEntity {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 履歴ID */
    private String assId;

    /** 作成日 */
    private String createYmd;

    /** 項目名称 */
    private String koumokuKnj;

    /** 細目名称 */
    private String saimokuKnj;

    /** 課題対象フラグ */
    private String kadaiFlg;

    /** 備考 */
    private String biko;

    /** 項目ID */
    private String koumokuId;

    /** 細目ID */
    private String saimokuId;

    /** 内容ID */
    private String naiyoId;

    /** 内容名称 */
    private String naiyouKnj;

    /** 点数 */
    private String point;

    /** 評価内容リスト */
    private List<Gui00892CheckNaiyou> checkNaiyouList;

    /** 評価内容リスト */
    private List<Gui00892CheckPoint> checkPointList;

}
