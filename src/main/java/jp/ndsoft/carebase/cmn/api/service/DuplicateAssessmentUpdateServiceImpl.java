package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import io.micrometer.common.util.StringUtils;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00838NoInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.DuplicateAssessmentUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.DuplicateAssessmentUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucHcc1Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucHcc21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucHcc22Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucHcc23Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucHcc24Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucHcc25Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucHcc26Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucHcc27Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucHcc31Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucHcc32Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucHcc33Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucHcc34Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghTucKrkKikanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc1;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc1Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc22;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc22Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc23;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc23Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc24;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc24Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc25;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc25Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc26;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc26Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc27;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc27Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc31;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc31Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc32;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc32Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc33;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc33Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc34;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucHcc34Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTucKrkKikan;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnHokCckGutaiInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnHokCckGutaiInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnHokCckMondaiNoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnHokCckMondaiNoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc1InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc1InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc21InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc21InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc22InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc22InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc23InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc23InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc24InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc24InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc25InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc25InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc26InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc26InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc27InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc27InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc31InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc31InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc33InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucHcc33InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHcc1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHcc21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHcc22SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHcc23SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHcc24SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHcc25SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHcc26SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHcc27SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHcc31SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHcc32SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHcc33SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucHcc34Hcc32SelectMapper;
import jp.ndsoft.carebase.common.util.CareBaseMComSeqMgrCodeConstants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.smh.framework.services.util.Numbering;
import jp.ndsoft.smh.framework.util.AppUtil;
import jp.ndsoft.smh.framework.common.Constants;

/**
 * @since 2025.06.10
 * <AUTHOR>
 * @description Gui00838_［アセスメント複写］データ保存
 */
@Service
public class DuplicateAssessmentUpdateServiceImpl
        extends
        UpdateServiceImpl<DuplicateAssessmentUpdateServiceInDto, DuplicateAssessmentUpdateServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 採番 */
    @Autowired
    private Numbering numbering;
    /** アセスメント複写情報更新DAO */
    @Autowired
    private KghTucKrkKikanMapper kghTucKrkKikanMapper;
    /** アセスメント複写情報更新DAO */
    @Autowired
    private CpnTucHcc1Mapper cpnTucHcc1Mapper;

    /** ＨＫ＿ケアチェック表（１．食事）情報取得 */
    @Autowired
    private CpnTucHcc21SelectMapper cpnTucHcc21SelectMapper;
    /** ＨＫ＿ケアチェック表（１．食事）情報更新 */
    @Autowired
    private CpnTucHcc21Mapper cpnTucHcc21Mapper;
    /** ＨＫ＿ケアチェック表（２．排泄）情報取得 */
    @Autowired
    private CpnTucHcc22SelectMapper cpnTucHcc22SelectMapper;
    /** ＨＫ＿ケアチェック表（２．排泄）情報更新 */
    @Autowired
    private CpnTucHcc22Mapper cpnTucHcc22Mapper;
    /** ＨＫ＿ケアチェック表（３．入浴）情報取得 */
    @Autowired
    private CpnTucHcc23SelectMapper cpnTucHcc23SelectMapper;
    /** ＨＫ＿ケアチェック表（３．入浴）情報更新 */
    @Autowired
    private CpnTucHcc23Mapper cpnTucHcc23Mapper;
    /** ＨＫ＿ケアチェック表（４．洗面）情報取得 */
    @Autowired
    private CpnTucHcc24SelectMapper cpnTucHcc24SelectMapper;
    /** ＨＫ＿ケアチェック表（４．洗面）情報更新 */
    @Autowired
    private CpnTucHcc24Mapper cpnTucHcc24Mapper;
    /** ＨＫ＿ケアチェック表（５．基本）情報取得 */
    @Autowired
    private CpnTucHcc25SelectMapper cpnTucHcc25SelectMapper;
    /** ＨＫ＿ケアチェック表（５．基本）情報更新 */
    @Autowired
    private CpnTucHcc25Mapper cpnTucHcc25Mapper;
    /** ＨＫ＿ケアチェック表（６．医療）情報取得 */
    @Autowired
    private CpnTucHcc26SelectMapper cpnTucHcc26SelectMapper;
    /** ＨＫ＿ケアチェック表（６．医療）情報更新 */
    @Autowired
    private CpnTucHcc26Mapper cpnTucHcc26Mapper;
    /** ＨＫ＿ケアチェック表（７．心理）情報取得 */
    @Autowired
    private CpnTucHcc27SelectMapper cpnTucHcc27SelectMapper;
    /** ＨＫ＿ケアチェック表（７．心理）情報取得 */
    @Autowired
    private CpnTucHcc27Mapper cpnTucHcc27Mapper;
    /** ＨＫ＿ケアチェック表（具体的内容）情報取得DAO */
    @Autowired
    private CpnTucHcc32SelectMapper cpnTucHcc32SelectMapper;
    /** ＨＫ＿ケアチェック表（具体的内容）情報更新DAO */
    @Autowired
    private CpnTucHcc32Mapper cpnTucHcc32Mapper;
    /** 具体的内容の問題点番号情報取得DAO */
    @Autowired
    private CpnTucHcc34Hcc32SelectMapper cpnTucHcc34Hcc32SelectMapper;
    /** 具体的内容の問題点番号情報更新DAO */
    @Autowired
    private CpnTucHcc34Mapper cpnTucHcc34Mapper;
    /** 問題点や解決すべき課題情報取得DAO */
    @Autowired
    private CpnTucHcc31SelectMapper cpnTucHcc31SelectMapper;
    /** 問題点や解決すべき課題情報更新DAO */
    @Autowired
    private CpnTucHcc31Mapper cpnTucHcc31Mapper;
    /** ケア項目情報取得DAO */
    @Autowired
    private CpnTucHcc33SelectMapper cpnTucHcc33SelectMapper;
    /** ケア項目情報更新DAO */
    @Autowired
    private CpnTucHcc33Mapper cpnTucHcc33Mapper;

    /** ＨＫ＿ケアチェック表（ヘッダ）情報取得DAO */
    @Autowired
    private CpnTucHcc1SelectMapper cpnTucHcc1SelectMapper;

    /**
     * 「評価表マスタ」画面の情報を保存する。
     * 
     * @param inDto 初期設定マスタ情報保存入力DTO
     * @return 初期設定マスタ取込初期情報出力DTO
     */
    @SuppressWarnings("unchecked")
    @Override
    protected DuplicateAssessmentUpdateServiceOutDto mainProcess(
            final DuplicateAssessmentUpdateServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        List<Gui00838NoInfo> noInfoList = inDto.getObjList();

        DuplicateAssessmentUpdateServiceOutDto outDto = new DuplicateAssessmentUpdateServiceOutDto();
        // 【変数】.複写先期間ID
        Integer scIdTmp0 = 0;

        /*
         * ===============1. 複写先情報論理処理===============
         * 
         */
        // 1.1. リクエストパラメータ.複写先期間ID＝リクエストパラメータ.計画期間ID
        // AND リクエストパラメータ.複写先ケアチェックID＝リクエストパラメータ.ケアチェックIDの場合。
        if (CommonDtoUtil.checkStringEqual(inDto.getTgtSc1Id(), inDto.getDefSc1Id())
                && CommonDtoUtil.checkStringEqual(inDto.getTgtGdlId(), inDto.getDefGdlId())) {
            // 複写元と複写先が同じ場合処理しないので、下記の処理を中止する。
            return outDto;
        }
        // 1.2.期間の存在判断
        // 1.2.1.リクエストパラメータ.期間管理フラグ = false かつ、 リクエストパラメータ.期間ID = 0 (期間なし)の場合、下記の処理を行う
        // 1.2.1.リクエストパラメータ.複写先期間ID = 0 OR リクエストパラメータ.複写先期間ID = 空白の場合
        boolean flag = CommonConstants.STR_ZERO.equals(inDto.getTgtSc1Id()) || StringUtils.isEmpty(inDto.getTgtSc1Id());
        if (flag) {
            // 【変数】.複写先期間ID=採番結果.計画対象期間ID
            scIdTmp0 = insertKghTucKrkKikan(inDto);
        }

        // 1.4.2.ＨＫ＿ケアチェック表の該当データの削除、画面番号リストのループ
        for (Gui00838NoInfo noInfo : noInfoList) {
            // 1.4.1.ＨＫ＿ケアチェック表の該当データの削除
            if (CommonConstants.IMAGE_NO_1.equals(noInfo.getTypeId())) {
                // ＨＫ＿ケアチェック表（１．食事）
                deleteCpnTucHcc21(inDto);
            }
            if (CommonConstants.IMAGE_NO_2.equals(noInfo.getTypeId())) {
                // ＨＫ＿ケアチェック表（２．排泄）
                deleteCpnTucHcc22(inDto);
            }
            if (CommonConstants.IMAGE_NO_3.equals(noInfo.getTypeId())) {
                // ＨＫ＿ケアチェック表（３．入浴）
                deleteCpnTucHcc23(inDto);
            }
            if (CommonConstants.IMAGE_NO_4.equals(noInfo.getTypeId())) {
                // ＨＫ＿ケアチェック表（４．洗面）
                deleteCpnTucHcc24(inDto);
            }
            if (CommonConstants.IMAGE_NO_5.equals(noInfo.getTypeId())) {
                // ＨＫ＿ケアチェック表（５．基本）
                deleteCpnTucHcc25(inDto);
            }
            if (CommonConstants.IMAGE_NO_6.equals(noInfo.getTypeId())) {
                // ＨＫ＿ケアチェック表（６．医療）
                deleteCpnTucHcc26(inDto);
            }
            if (CommonConstants.IMAGE_NO_7.equals(noInfo.getTypeId())) {
                // ＨＫ＿ケアチェック表（７．心理）
                deleteCpnTucHcc27(inDto);
            }
        }
        // ＨＫ＿ケアチェック表（問題点や解決すべき課題）
        deleteCpnTuchcc31(inDto);
        // ＨＫ＿ケアチェック表（具体的内容）
        deleteCpnTuchcc32(inDto);
        // ケアチェック表（具体的内容の問題点番号）
        deleteCpnTuchcc33(inDto);
        // ＨＫ＿ケアチェック表（ケア項目）
        deleteCpnTuchcc34(inDto);

        /*
         * ===============2. 複写元データ情報処理===============
         * 
         */
        // 2.1.画面IDに基づいて、対応するDAOを呼び出して該当のデータを取得する
        // 【変数】.map
        Map<String, List<?>> resultMap = new HashMap<>();
        for (Gui00838NoInfo noInfo : noInfoList) {
            // HK_ケアチェック表（1.食事）情報取得
            if (CommonConstants.IMAGE_NO_1.equals(noInfo.getTypeId())) {
                CpnTucHcc21InfoByCriteriaInEntity cpnTucHcc21InfoByCriteriaInEntity = new CpnTucHcc21InfoByCriteriaInEntity();
                // リクエストパラメータ.期間ID
                cpnTucHcc21InfoByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getDefSc1Id()));
                // リクエストパラメータ.ケアチェックID
                cpnTucHcc21InfoByCriteriaInEntity.setAlCc1Id(CommonDtoUtil.strValToInt(inDto.getDefGdlId()));

                List<CpnTucHcc21InfoOutEntity> cpnTucHcc21InfoOutEntities = this.cpnTucHcc21SelectMapper
                        .findCpnTucHcc21InfoByCriteria(cpnTucHcc21InfoByCriteriaInEntity);

                resultMap.put(CommonConstants.IMAGE_NO_1, cpnTucHcc21InfoOutEntities);
            }

            // ＨＫ＿ケアチェック表（２．排泄）情報取得
            if (CommonConstants.IMAGE_NO_2.equals(noInfo.getTypeId())) {
                CpnTucHcc22InfoByCriteriaInEntity cpnTucHcc22InfoByCriteriaInEntity = new CpnTucHcc22InfoByCriteriaInEntity();
                // リクエストパラメータ.期間ID
                cpnTucHcc22InfoByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getDefSc1Id()));
                // リクエストパラメータ.ケアチェックID
                cpnTucHcc22InfoByCriteriaInEntity.setAlCc1Id(CommonDtoUtil.strValToInt(inDto.getDefGdlId()));

                List<CpnTucHcc22InfoOutEntity> cpnTucHcc22InfoOutEntities = this.cpnTucHcc22SelectMapper
                        .findCpnTucHcc22InfoByCriteria(cpnTucHcc22InfoByCriteriaInEntity);

                resultMap.put(CommonConstants.IMAGE_NO_2, cpnTucHcc22InfoOutEntities);
            }

            // ＨＫ＿ケアチェック表（３．入浴）情報取得
            if (CommonConstants.IMAGE_NO_3.equals(noInfo.getTypeId())) {
                CpnTucHcc23InfoByCriteriaInEntity cpnTucHcc23InfoByCriteriaInEntity = new CpnTucHcc23InfoByCriteriaInEntity();
                // リクエストパラメータ.期間ID
                cpnTucHcc23InfoByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getDefSc1Id()));
                // リクエストパラメータ.ケアチェックID
                cpnTucHcc23InfoByCriteriaInEntity.setAlCc1Id(CommonDtoUtil.strValToInt(inDto.getDefGdlId()));

                List<CpnTucHcc23InfoOutEntity> cpnTucHcc23InfoOutEntities = this.cpnTucHcc23SelectMapper
                        .findCpnTucHcc23InfoByCriteria(cpnTucHcc23InfoByCriteriaInEntity);

                resultMap.put(CommonConstants.IMAGE_NO_3, cpnTucHcc23InfoOutEntities);
            }

            // ＨＫ＿ケアチェック表（４．洗面）情報取得
            if (CommonConstants.IMAGE_NO_4.equals(noInfo.getTypeId())) {
                CpnTucHcc24InfoByCriteriaInEntity cpnTucHcc24InfoByCriteriaInEntity = new CpnTucHcc24InfoByCriteriaInEntity();
                // リクエストパラメータ.期間ID
                cpnTucHcc24InfoByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getDefSc1Id()));
                // リクエストパラメータ.ケアチェックID
                cpnTucHcc24InfoByCriteriaInEntity.setAlCc1Id(CommonDtoUtil.strValToInt(inDto.getDefGdlId()));

                List<CpnTucHcc24InfoOutEntity> cpnTucHcc24InfoOutEntities = this.cpnTucHcc24SelectMapper
                        .findCpnTucHcc24InfoByCriteria(cpnTucHcc24InfoByCriteriaInEntity);

                resultMap.put(CommonConstants.IMAGE_NO_4, cpnTucHcc24InfoOutEntities);
            }

            // ＨＫ＿ケアチェック表（５．基本）情報取得
            if (CommonConstants.IMAGE_NO_5.equals(noInfo.getTypeId())) {
                CpnTucHcc25InfoByCriteriaInEntity cpnTucHcc25InfoByCriteriaInEntity = new CpnTucHcc25InfoByCriteriaInEntity();
                // リクエストパラメータ.期間ID
                cpnTucHcc25InfoByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getDefSc1Id()));
                // リクエストパラメータ.ケアチェックID
                cpnTucHcc25InfoByCriteriaInEntity.setAlCc1Id(CommonDtoUtil.strValToInt(inDto.getDefGdlId()));

                List<CpnTucHcc25InfoOutEntity> cpnTucHcc25InfoOutEntities = this.cpnTucHcc25SelectMapper
                        .findCpnTucHcc25InfoByCriteria(cpnTucHcc25InfoByCriteriaInEntity);

                resultMap.put(CommonConstants.IMAGE_NO_5, cpnTucHcc25InfoOutEntities);
            }

            // ＨＫ＿ケアチェック表（６．医療）情報取得
            if (CommonConstants.IMAGE_NO_6.equals(noInfo.getTypeId())) {
                CpnTucHcc26InfoByCriteriaInEntity cpnTucHcc26InfoByCriteriaInEntity = new CpnTucHcc26InfoByCriteriaInEntity();
                // リクエストパラメータ.期間ID
                cpnTucHcc26InfoByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getDefSc1Id()));
                // リクエストパラメータ.ケアチェックID
                cpnTucHcc26InfoByCriteriaInEntity.setAlCc1Id(CommonDtoUtil.strValToInt(inDto.getDefGdlId()));

                List<CpnTucHcc26InfoOutEntity> cpnTucHcc26InfoOutEntities = this.cpnTucHcc26SelectMapper
                        .findCpnTucHcc26InfoByCriteria(cpnTucHcc26InfoByCriteriaInEntity);

                resultMap.put(CommonConstants.IMAGE_NO_6, cpnTucHcc26InfoOutEntities);
            }

            // ＨＫ＿ケアチェック表（７．心理）情報取得
            if (CommonConstants.IMAGE_NO_7.equals(noInfo.getTypeId())) {
                CpnTucHcc27InfoByCriteriaInEntity cpnTucHcc27InfoByCriteriaInEntity = new CpnTucHcc27InfoByCriteriaInEntity();
                // リクエストパラメータ.期間ID
                cpnTucHcc27InfoByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getDefSc1Id()));
                // リクエストパラメータ.ケアチェックID
                cpnTucHcc27InfoByCriteriaInEntity.setAlCc1Id(CommonDtoUtil.strValToInt(inDto.getDefGdlId()));

                List<CpnTucHcc27InfoOutEntity> cpnTucHcc27InfoOutEntities = this.cpnTucHcc27SelectMapper
                        .findCpnTucHcc27InfoByCriteria(cpnTucHcc27InfoByCriteriaInEntity);

                resultMap.put(CommonConstants.IMAGE_NO_7, cpnTucHcc27InfoOutEntities);
            }
        }
        // 2.2.履歴データ処理
        // 2.2.1.複写先履歴データがあるかどうか確認する
        // 【変数】.複写先ケアチェックID
        Integer tgtGdlId = CommonDtoUtil.strValToInt(inDto.getTgtGdlId());
        if (inDto.getTgtGdlId() != null) {
            // 2.2.1.1. 下記のＨＫ＿ケアチェック表（ヘッダ）情報取得のDAOを利用し、包括ケアチェック履歴情報リストを取得する。
            CpnTucHcc1InfoByCriteriaInEntity cpnTucHcc1InfoByCriteriaInEntity = new CpnTucHcc1InfoByCriteriaInEntity();
            // 計画期間ID
            cpnTucHcc1InfoByCriteriaInEntity.setSc1Id(CommonDtoUtil.strValToInt(inDto.getTgtSc1Id()));
            // 事業者ID
            cpnTucHcc1InfoByCriteriaInEntity.setJId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 利用者ID
            cpnTucHcc1InfoByCriteriaInEntity.setUId(CommonDtoUtil.strValToInt(inDto.getUserId()));

            List<CpnTucHcc1InfoOutEntity> cpnTucHcc1InfoOutEntities = this.cpnTucHcc1SelectMapper
                    .findCpnTucHcc1InfoByCriteria(cpnTucHcc1InfoByCriteriaInEntity);

            // 2.2.1.2.上記「2.2.1.1.」のデータから、法人ID=リクエストパラメータ.法人ID かつ 施設ID=リクエストパラメータ.施設ID
            // を満たすリストデータを取得する
            for (CpnTucHcc1InfoOutEntity cpnTucHcc1InfoOutEntity : cpnTucHcc1InfoOutEntities) {
                if (cpnTucHcc1InfoOutEntity.getHoujinId() == CommonDtoUtil.strValToInt(inDto.getHoujinId())
                        && cpnTucHcc1InfoOutEntity.getShisetuId() == CommonDtoUtil.strValToInt(inDto.getShisetuId())) {
                    // ＨＫ＿ケアチェック表（ヘッダ）情報を更新する。
                    updateCpnTucHcc1(inDto, cpnTucHcc1InfoOutEntities);
                } else {
                    // ＨＫ＿ケアチェック表（ヘッダ）情報を登録する
                    tgtGdlId = insertCpnTucHcc12(inDto, flag, scIdTmp0);
                }
            }
        }
        // 2.3.ＨＫ＿ケアチェック表データを登録する
        // 【変数】.ケアチェックID＝【変数】.複写先ケアチェックID
        Integer gdlId = tgtGdlId;
        // 【変数】.計画期間ID＝リクエストパラメータ.複写先期間ID
        Integer sc1Id = flag ? scIdTmp0 : CommonDtoUtil.strValToInt(inDto.getTgtSc1Id());
        for (Gui00838NoInfo noInfo : noInfoList) {
            if (noInfo.getTypeId().equals(CommonConstants.IMAGE_NO_1)) {
                List<?> list = resultMap.get(CommonConstants.IMAGE_NO_1);
                List<CpnTucHcc21InfoOutEntity> specificList = (List<CpnTucHcc21InfoOutEntity>) list;
                insertCpnTucHcc21(inDto, gdlId, sc1Id, specificList);
            }
            if (noInfo.getTypeId().equals(CommonConstants.IMAGE_NO_2)) {
                List<?> list = resultMap.get(CommonConstants.IMAGE_NO_2);
                List<CpnTucHcc22InfoOutEntity> specificList = (List<CpnTucHcc22InfoOutEntity>) list;
                insertCpnTucHcc22(inDto, gdlId, sc1Id, specificList);
            }
            if (noInfo.getTypeId().equals(CommonConstants.IMAGE_NO_3)) {
                List<?> list = resultMap.get(CommonConstants.IMAGE_NO_3);
                List<CpnTucHcc23InfoOutEntity> specificList = (List<CpnTucHcc23InfoOutEntity>) list;
                insertCpnTucHcc23(inDto, gdlId, sc1Id, specificList);
            }
            if (noInfo.getTypeId().equals(CommonConstants.IMAGE_NO_4)) {
                List<?> list = resultMap.get(CommonConstants.IMAGE_NO_4);
                List<CpnTucHcc24InfoOutEntity> specificList = (List<CpnTucHcc24InfoOutEntity>) list;
                insertCpnTucHcc24(inDto, gdlId, sc1Id, specificList);
            }
            if (noInfo.getTypeId().equals(CommonConstants.IMAGE_NO_5)) {
                List<?> list = resultMap.get(CommonConstants.IMAGE_NO_5);
                List<CpnTucHcc25InfoOutEntity> specificList = (List<CpnTucHcc25InfoOutEntity>) list;
                insertCpnTucHcc25(inDto, gdlId, sc1Id, specificList);
            }
            if (noInfo.getTypeId().equals(CommonConstants.IMAGE_NO_6)) {
                List<?> list = resultMap.get(CommonConstants.IMAGE_NO_6);
                List<CpnTucHcc26InfoOutEntity> specificList = (List<CpnTucHcc26InfoOutEntity>) list;
                insertCpnTucHcc26(inDto, gdlId, sc1Id, specificList);
            }
            if (noInfo.getTypeId().equals(CommonConstants.IMAGE_NO_7)) {
                List<?> list = resultMap.get(CommonConstants.IMAGE_NO_7);
                List<CpnTucHcc27InfoOutEntity> specificList = (List<CpnTucHcc27InfoOutEntity>) list;
                insertCpnTucHcc27(inDto, gdlId, sc1Id, specificList);
            }
        }
        // 2.4.具体的内容データを登録する
        // 2.4.1. 下記のＨＫ＿ケアチェック表（具体的内容）情報取得のDAOを利用し、具体内容情報リストを取得する。
        // 【変数】.具体的内容ID
        Integer cc32Id = 0;
        CpnHokCckGutaiInfoByCriteriaInEntity cpnHokCckGutaiInfoByCriteriaInEntity = new CpnHokCckGutaiInfoByCriteriaInEntity();
        // ケアチェックID
        cpnHokCckGutaiInfoByCriteriaInEntity.setAlCc1Id(CommonDtoUtil.strValToInt(inDto.getDefGdlId()));
        // 計画期間ID
        cpnHokCckGutaiInfoByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getDefSc1Id()));

        List<CpnHokCckGutaiInfoOutEntity> cpnHokCckGutaiInfoOutEntities = this.cpnTucHcc32SelectMapper
                .findCpnHokCckGutaiInfoByCriteria(cpnHokCckGutaiInfoByCriteriaInEntity);
        // 2.4.2.1 上記「2.4.1」で取得したデータをループさせます
        for (CpnHokCckGutaiInfoOutEntity cpnHokCckGutaiInfoOutEntity : cpnHokCckGutaiInfoOutEntities) {
            // ＨＫ＿ケアチェック表（具体的内容）情報を登録する
            cc32Id = insertCpnTucHcc32(inDto, gdlId, sc1Id, cpnHokCckGutaiInfoOutEntity);

            // 2.4.2.2.ＨＫ＿ケアチェック表（具体的内容の問題点番号）登録する
            // 2.4.2.2.1. 下記の具体的内容の問題点番号情報取得のDAOを利用し、問題点番号情報リストを取得する。
            CpnHokCckMondaiNoByCriteriaInEntity cpnHokCckMondaiNoByCriteriaInEntity = new CpnHokCckMondaiNoByCriteriaInEntity();
            // ケアチェックID
            cpnHokCckMondaiNoByCriteriaInEntity.setCc1Id(CommonDtoUtil.strValToInt(inDto.getDefGdlId()));
            // 計画期間ID
            cpnHokCckMondaiNoByCriteriaInEntity.setSc1Id(CommonDtoUtil.strValToInt(inDto.getDefSc1Id()));

            List<CpnHokCckMondaiNoOutEntity> cpnHokCckMondaiNoOutEntities = this.cpnTucHcc34Hcc32SelectMapper
                    .findCpnHokCckMondaiNoByCriteria(cpnHokCckMondaiNoByCriteriaInEntity);

            // 2.4.2.2.2.ＨＫ＿ケアチェック表（具体的内容の問題点番号）情報の保存処理を行うため、以下のサービスを行う
            // 2.4.2.2.2.1
            // 上記「2.4.2.2.1.」具体的内容の問題番号情報リストのデータ件数＞0の場合、具体的内容の問題番号情報リストで取得したデータをループさせます
            if (cpnHokCckMondaiNoOutEntities.size() > 0) {
                for (CpnHokCckMondaiNoOutEntity cpnHokCckMondaiNoOutEntity : cpnHokCckMondaiNoOutEntities) {
                    insertCpnTucHcc34(inDto, gdlId, sc1Id, cc32Id, cpnHokCckMondaiNoOutEntity);
                }
            }
            // 2.4.2.3.ＨＫ＿ケアチェック表（ケア項目）登録する
            // 2.4.2.3.1. 下記のＨＫ＿ケアチェック表（ケア項目）情報取得のDAOを利用し、ケア項目情報リストを取得する。
            CpnTucHcc33InfoByCriteriaInEntity cpnTucHcc33InfoByCriteriaInEntity = new CpnTucHcc33InfoByCriteriaInEntity();
            // ケアチェックID
            cpnTucHcc33InfoByCriteriaInEntity.setAlCc1Id(CommonDtoUtil.strValToInt(inDto.getDefGdlId()));
            // 計画期間ID
            cpnTucHcc33InfoByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getDefSc1Id()));

            List<CpnTucHcc33InfoOutEntity> cpnTucHcc33InfoOutEntities = this.cpnTucHcc33SelectMapper
                    .findCpnTucHcc33InfoByCriteria(cpnTucHcc33InfoByCriteriaInEntity);

            // 2.4.2.3.2.ＨＫ＿ケアチェック表（ケア項目）情報の保存処理を行うため、以下のサービスを行う
            // 2.4.2.3.2.1 上記「2.4.2.3.1.」で取得したデータをループさせます
            for (CpnTucHcc33InfoOutEntity cpnTucHcc33InfoOutEntity : cpnTucHcc33InfoOutEntities) {
                insertCpnTucHcc33(inDto, gdlId, sc1Id, cc32Id, cpnTucHcc33InfoOutEntity);
            }
        }

        // 2.5.ＨＫ＿ケアチェック表（問題点や解決すべき課題）登録する
        // 2.5.1. 下記のＨＫ＿ケアチェック表（問題点や解決すべき課題）情報取得のDAOを利用し、問題点解決情報リストを取得する。
        CpnTucHcc31InfoByCriteriaInEntity cpnTucHcc31InfoByCriteriaInEntity = new CpnTucHcc31InfoByCriteriaInEntity();
        // ケアチェックID
        cpnTucHcc31InfoByCriteriaInEntity.setAlCc1Id(CommonDtoUtil.strValToInt(inDto.getDefGdlId()));
        // 計画期間ID
        cpnTucHcc31InfoByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getDefSc1Id()));

        List<CpnTucHcc31InfoOutEntity> cpnTucHcc31InfoOutEntities = this.cpnTucHcc31SelectMapper
                .findCpnTucHcc31InfoByCriteria(cpnTucHcc31InfoByCriteriaInEntity);

        // 2.5.2.ＨＫ＿ケアチェック表（問題点や解決すべき課題）情報の保存処理を行うため、以下のサービスを行う
        // 2.5.2.1 上記「2.5.1」で取得したデータをループさせます
        for (CpnTucHcc31InfoOutEntity cpnTucHcc31InfoOutEntity : cpnTucHcc31InfoOutEntities) {
            // ＨＫ＿ケアチェック表（問題点や解決すべき課題）を登録する
            insertCpnTucHcc31(inDto, gdlId, sc1Id, cpnTucHcc31InfoOutEntity);
        }

        // ステータス
        outDto.setScId(String.valueOf(sc1Id));
        outDto.setGdlId(String.valueOf(gdlId));
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 記録共通期間情報を登録する
     *
     * @param inDto GUI00838_［アセスメント複写］保存サービス入力Dto
     * @return 採番した期間ID
     */
    private Integer insertKghTucKrkKikan(DuplicateAssessmentUpdateServiceInDto inDto) throws Exception {
        // Input
        KghTucKrkKikan kghTucKrkKikan = new KghTucKrkKikan();
        // 作成日
        kghTucKrkKikan.setStartYmd(inDto.getCreateYmd());
        // 法人ＩＤ
        kghTucKrkKikan.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ＩＤ
        kghTucKrkKikan.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業所ＩＤ
        kghTucKrkKikan.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        kghTucKrkKikan.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 種別ID
        kghTucKrkKikan.setSyubetsuId(CommonDtoUtil.strValToInt(inDto.getSyubetsuId()));

        kghTucKrkKikan.setSc1Id(numbering.getNumber(AppUtil.getKeiyakushaId(),
                CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.KGH_TUC_KRK_KIKAN_SC1_ID.getIntValue()).intValue());

        kghTucKrkKikanMapper.insertSelective(kghTucKrkKikan);
        return kghTucKrkKikan.getSc1Id().intValue();
    }

    /**
     * 【ＨＫ＿ケアチェック表（１．食事）】テーブルを論理削除する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @throws ExclusiveException 排他制御エラー
     */
    private void deleteCpnTucHcc21(DuplicateAssessmentUpdateServiceInDto inDto)
            throws ExclusiveException {

        // input
        Gui00838NoInfo info = inDto.getObjList().stream()
                .filter(o -> o.getTypeId().equals(CommonConstants.IMAGE_NO_1)).findFirst().orElse(null);
        // 件数初期化
        if (Objects.nonNull(info)) {
            CpnTucHcc21Criteria criteria = new CpnTucHcc21Criteria();
            criteria.createCriteria()
                    .andCc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtSc1Id()));

            // 更新用サービス取得処理を実施する
            cpnTucHcc21Mapper.deleteByCriteria(criteria);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（２．排泄）】テーブルを論理削除する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @throws ExclusiveException 排他制御エラー
     */
    private void deleteCpnTucHcc22(DuplicateAssessmentUpdateServiceInDto inDto)
            throws ExclusiveException {

        // input
        Gui00838NoInfo info = inDto.getObjList().stream()
                .filter(o -> o.getTypeId().equals(CommonConstants.IMAGE_NO_2)).findFirst().orElse(null);
        // 件数初期化
        if (Objects.nonNull(info)) {

            CpnTucHcc22Criteria criteria = new CpnTucHcc22Criteria();
            criteria.createCriteria()
                    .andCc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtSc1Id()));

            // 更新用サービス取得処理を実施する
            cpnTucHcc22Mapper.deleteByCriteria(criteria);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（３．入浴）】テーブルを論理削除する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @throws ExclusiveException 排他制御エラー
     */
    private void deleteCpnTucHcc23(DuplicateAssessmentUpdateServiceInDto inDto)
            throws ExclusiveException {

        // input
        Gui00838NoInfo info = inDto.getObjList().stream()
                .filter(o -> o.getTypeId().equals(CommonConstants.IMAGE_NO_3)).findFirst().orElse(null);
        // 件数初期化
        if (Objects.nonNull(info)) {

            CpnTucHcc23Criteria criteria = new CpnTucHcc23Criteria();
            criteria.createCriteria()
                    .andCc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtSc1Id()));

            // 更新用サービス取得処理を実施する
            cpnTucHcc23Mapper.deleteByCriteria(criteria);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（４．洗面）】テーブルを論理削除する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @throws ExclusiveException 排他制御エラー
     */
    private void deleteCpnTucHcc24(DuplicateAssessmentUpdateServiceInDto inDto)
            throws ExclusiveException {

        // input
        Gui00838NoInfo info = inDto.getObjList().stream()
                .filter(o -> o.getTypeId().equals(CommonConstants.IMAGE_NO_4)).findFirst().orElse(null);
        // 件数初期化
        if (Objects.nonNull(info)) {

            CpnTucHcc24Criteria criteria = new CpnTucHcc24Criteria();
            criteria.createCriteria()
                    .andCc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtSc1Id()));

            // 更新用サービス取得処理を実施する
            cpnTucHcc24Mapper.deleteByCriteria(criteria);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（５．基本）】テーブルを論理削除する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @throws ExclusiveException 排他制御エラー
     */
    private void deleteCpnTucHcc25(DuplicateAssessmentUpdateServiceInDto inDto)
            throws ExclusiveException {

        // input
        Gui00838NoInfo info = inDto.getObjList().stream()
                .filter(o -> o.getTypeId().equals(CommonConstants.IMAGE_NO_5)).findFirst().orElse(null);
        // 件数初期化
        if (Objects.nonNull(info)) {

            CpnTucHcc25Criteria criteria = new CpnTucHcc25Criteria();
            criteria.createCriteria()
                    .andCc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtSc1Id()));

            // 更新用サービス取得処理を実施する
            cpnTucHcc25Mapper.deleteByCriteria(criteria);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（６．医療）】テーブルを論理削除する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @throws ExclusiveException 排他制御エラー
     */
    private void deleteCpnTucHcc26(DuplicateAssessmentUpdateServiceInDto inDto)
            throws ExclusiveException {

        // input

        Gui00838NoInfo info = inDto.getObjList().stream()
                .filter(o -> o.getTypeId().equals(CommonConstants.IMAGE_NO_6)).findFirst().orElse(null);
        // 件数初期化
        if (Objects.nonNull(info)) {
            CpnTucHcc26Criteria criteria = new CpnTucHcc26Criteria();
            criteria.createCriteria()
                    .andCc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtSc1Id()));

            // 更新用サービス取得処理を実施する
            cpnTucHcc26Mapper.deleteByCriteria(criteria);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（７．心理）】テーブルを論理削除する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @throws ExclusiveException 排他制御エラー
     */
    private void deleteCpnTucHcc27(DuplicateAssessmentUpdateServiceInDto inDto)
            throws ExclusiveException {

        // input
        Gui00838NoInfo info = inDto.getObjList().stream()
                .filter(o -> o.getTypeId().equals(CommonConstants.IMAGE_NO_7)).findFirst().orElse(null);
        // 件数初期化
        if (Objects.nonNull(info)) {

            CpnTucHcc27Criteria criteria = new CpnTucHcc27Criteria();
            criteria.createCriteria()
                    .andCc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtSc1Id()));

            // 更新用サービス取得処理を実施する
            cpnTucHcc27Mapper.deleteByCriteria(criteria);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（問題点や解決すべき課題）】テーブルを論理削除する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @throws ExclusiveException 排他制御エラー
     */
    private void deleteCpnTuchcc31(DuplicateAssessmentUpdateServiceInDto inDto)
            throws ExclusiveException {
        for (Gui00838NoInfo objList : inDto.getObjList()) {
            CpnTucHcc31Criteria criteria = new CpnTucHcc31Criteria();
            criteria.createCriteria()
                    .andCc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtSc1Id()))
                    .andB1CdEqualTo(CommonDtoUtil.strValToInt(objList.getTypeId()));
            // 更新用サービス取得処理を実施する
            cpnTucHcc31Mapper.deleteByCriteria(criteria);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（具体的内容）】テーブルを論理削除する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @throws ExclusiveException 排他制御エラー
     */
    private void deleteCpnTuchcc32(DuplicateAssessmentUpdateServiceInDto inDto)
            throws ExclusiveException {
        for (Gui00838NoInfo objList : inDto.getObjList()) {
            CpnTucHcc32Criteria criteria = new CpnTucHcc32Criteria();
            criteria.createCriteria()
                    .andCc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtSc1Id()))
                    .andB1CdEqualTo(CommonDtoUtil.strValToInt(objList.getTypeId()));
            // 更新用サービス取得処理を実施する
            cpnTucHcc32Mapper.deleteByCriteria(criteria);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（ケア項目）】テーブルを論理削除する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @throws ExclusiveException 排他制御エラー
     */
    private void deleteCpnTuchcc33(DuplicateAssessmentUpdateServiceInDto inDto)
            throws ExclusiveException {

        for (Gui00838NoInfo objList : inDto.getObjList()) {
            CpnTucHcc33Criteria criteria = new CpnTucHcc33Criteria();
            criteria.createCriteria()
                    .andCc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtSc1Id()))
                    .andB1CdEqualTo(CommonDtoUtil.strValToInt(objList.getTypeId()));
            // 更新用サービス取得処理を実施する
            cpnTucHcc33Mapper.deleteByCriteria(criteria);

        }
    }

    /**
     * 【ケアチェック表（具体的内容の問題点番号）】テーブルを論理削除する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @throws ExclusiveException 排他制御エラー
     */
    private void deleteCpnTuchcc34(DuplicateAssessmentUpdateServiceInDto inDto)
            throws ExclusiveException {

        for (Gui00838NoInfo objList : inDto.getObjList()) {
            CpnTucHcc34Criteria criteria = new CpnTucHcc34Criteria();
            criteria.createCriteria()
                    .andCc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtSc1Id()))
                    .andB4CdEqualTo(CommonDtoUtil.strValToInt(objList.getTypeId()));
            // 更新用サービス取得処理を実施する
            cpnTucHcc34Mapper.deleteByCriteria(criteria);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（ヘッダ）】テーブルを更新する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @throws ExclusiveException 排他制御エラー
     */
    private void updateCpnTucHcc1(DuplicateAssessmentUpdateServiceInDto inDto,
            List<CpnTucHcc1InfoOutEntity> cpnTucHcc1InfoOutEntities)
            throws ExclusiveException {
        // Input
        CpnTucHcc1 cpnTucHcc1 = new CpnTucHcc1();
        // 更新項目に対応.●

        // 作成日
        cpnTucHcc1.setCreateYmd(inDto.getCreateYmd());
        // 職員ID
        cpnTucHcc1.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));

        if (cpnTucHcc1InfoOutEntities.size() > 0) {
            // 件数初期化
            CpnTucHcc1Criteria criteria = new CpnTucHcc1Criteria();
            criteria.createCriteria()
                    .andCc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getTgtSc1Id()))
                    .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                    .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()))
                    .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                    .andUseridEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()));

            cpnTucHcc1Mapper.updateByCriteriaSelective(cpnTucHcc1, criteria);

        }
    }

    /**
     * 【32-07 課題立案ヘッダ】テーブルを登録する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @return 採番した課題立案履歴ＩＤ
     */
    private Integer insertCpnTucHcc12(DuplicateAssessmentUpdateServiceInDto inDto, boolean flag, Integer scIdTmp0)
            throws Exception {
        CpnTucHcc1 cpnTucHcc1 = new CpnTucHcc1();
        // リクエストパラメータ.複写先期間ID 複写先期間ID ＝【変数】.複写先期間ID
        cpnTucHcc1.setSc1Id(flag ? scIdTmp0 : CommonDtoUtil.strValToInt(inDto.getTgtSc1Id()));
        // リクエストパラメータ.法人ＩＤ
        cpnTucHcc1.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // リクエストパラメータ.施設ＩＤ
        cpnTucHcc1.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // リクエストパラメータ.事業所ＩＤ
        cpnTucHcc1.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // リクエストパラメータ.利用者ID
        cpnTucHcc1.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // リクエストパラメータ.作成日
        cpnTucHcc1.setCreateYmd(inDto.getCreateYmd());
        // リクエストパラメータ.職員ID
        cpnTucHcc1.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));

        return cpnTucHcc1Mapper.insertSelectiveAndReturn(cpnTucHcc1);

    }

    /**
     * 【ＨＫ＿ケアチェック表（1.食事）】テーブルを登録する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @return 採番した課題立案履歴ＩＤ
     */
    private void insertCpnTucHcc21(DuplicateAssessmentUpdateServiceInDto inDto, Integer tgtGdlId, Integer scIdTmp0,
            List<CpnTucHcc21InfoOutEntity> specificList) {
        CpnTucHcc21 cpnTucHcc21 = new CpnTucHcc21();
        for (CpnTucHcc21InfoOutEntity entity : specificList) {
            cpnTucHcc21.setCc1Id(tgtGdlId);
            cpnTucHcc21.setSc1Id(scIdTmp0);
            cpnTucHcc21.setCn011(entity.getCn011());
            cpnTucHcc21.setCn012(entity.getCn012());
            cpnTucHcc21.setCn013(entity.getCn013());
            cpnTucHcc21.setCn021(entity.getCn021());
            cpnTucHcc21.setCn022(entity.getCn022());
            cpnTucHcc21.setCn023(entity.getCn023());
            cpnTucHcc21.setCn031(entity.getCn031());
            cpnTucHcc21.setCn032(entity.getCn032());
            cpnTucHcc21.setCn033(entity.getCn033());
            cpnTucHcc21.setCn041(entity.getCn041());
            cpnTucHcc21.setCn042(entity.getCn042());
            cpnTucHcc21.setCn043(entity.getCn043());
            cpnTucHcc21.setCn051(entity.getCn051());
            cpnTucHcc21.setCn052(entity.getCn052());
            cpnTucHcc21.setCn053(entity.getCn053());
            cpnTucHcc21.setCn061(entity.getCn061());
            cpnTucHcc21.setCn062(entity.getCn062());
            cpnTucHcc21.setCn063(entity.getCn063());
            cpnTucHcc21.setCn071(entity.getCn071());
            cpnTucHcc21.setCn072(entity.getCn072());
            cpnTucHcc21.setCn073(entity.getCn073());
            cpnTucHcc21.setCn081(entity.getCn081());
            cpnTucHcc21.setCn082(entity.getCn082());
            cpnTucHcc21.setCn083(entity.getCn083());
            cpnTucHcc21.setCn091(entity.getCn091());
            cpnTucHcc21.setCn092(entity.getCn092());
            cpnTucHcc21.setCn093(entity.getCn093());
            cpnTucHcc21.setCn101(entity.getCn101());
            cpnTucHcc21.setCn102(entity.getCn102());
            cpnTucHcc21.setCn103(entity.getCn103());
            cpnTucHcc21.setCn111(entity.getCn111());
            cpnTucHcc21.setCn112(entity.getCn112());
            cpnTucHcc21.setCn113(entity.getCn113());
            cpnTucHcc21.setCn121(entity.getCn121());
            cpnTucHcc21.setCn122(entity.getCn122());
            cpnTucHcc21.setCn123(entity.getCn123());
            cpnTucHcc21.setCn131(entity.getCn131());
            cpnTucHcc21.setCn132(entity.getCn132());
            cpnTucHcc21.setCn133(entity.getCn133());
            cpnTucHcc21.setCn141(entity.getCn141());
            cpnTucHcc21.setCn142(entity.getCn142());
            cpnTucHcc21.setCn143(entity.getCn143());
            cpnTucHcc21.setCn151(entity.getCn151());
            cpnTucHcc21.setCn152(entity.getCn152());
            cpnTucHcc21.setCn153(entity.getCn153());
            cpnTucHcc21.setCn161(entity.getCn161());
            cpnTucHcc21.setCn162(entity.getCn162());
            cpnTucHcc21.setCn163(entity.getCn163());
            cpnTucHcc21.setCn171(entity.getCn171());
            cpnTucHcc21.setCn172(entity.getCn172());
            cpnTucHcc21.setCn173(entity.getCn173());
            cpnTucHcc21.setCn181(entity.getCn181());
            cpnTucHcc21.setCn182(entity.getCn182());
            cpnTucHcc21.setCn183(entity.getCn183());
            cpnTucHcc21.setCn191(entity.getCn191());
            cpnTucHcc21.setCn192(entity.getCn192());
            cpnTucHcc21.setCn193(entity.getCn193());
            cpnTucHcc21.setCn201(entity.getCn201());
            cpnTucHcc21.setCn202(entity.getCn202());
            cpnTucHcc21.setCn203(entity.getCn203());
            cpnTucHcc21.setCn211(entity.getCn211());
            cpnTucHcc21.setCn212(entity.getCn212());
            cpnTucHcc21.setCn213(entity.getCn213());
            cpnTucHcc21.setCn221(entity.getCn221());
            cpnTucHcc21.setCn222(entity.getCn222());
            cpnTucHcc21.setCn223(entity.getCn223());
            cpnTucHcc21.setCn231(entity.getCn231());
            cpnTucHcc21.setCn232(entity.getCn232());
            cpnTucHcc21.setCn233(entity.getCn233());
            cpnTucHcc21.setCn241(entity.getCn241());
            cpnTucHcc21.setCn242(entity.getCn242());
            cpnTucHcc21.setCn243(entity.getCn243());
            cpnTucHcc21.setCn251(entity.getCn251());
            cpnTucHcc21.setCn252(entity.getCn252());
            cpnTucHcc21.setCn253(entity.getCn253());
            cpnTucHcc21.setCn261(entity.getCn261());
            cpnTucHcc21.setCn262(entity.getCn262());
            cpnTucHcc21.setCn263(entity.getCn263());
            cpnTucHcc21.setCn271(entity.getCn271());
            cpnTucHcc21.setCn272(entity.getCn272());
            cpnTucHcc21.setCn273(entity.getCn273());
            cpnTucHcc21.setCb011(entity.getCb011());
            cpnTucHcc21.setCb021(entity.getCb021());
            cpnTucHcc21.setCb031(entity.getCb031());
            cpnTucHcc21.setCb041(entity.getCb041());
            cpnTucHcc21.setCb051(entity.getCb051());
            cpnTucHcc21.setCb052Knj(entity.getCb052Knj());
            cpnTucHcc21.setCb061(entity.getCb061());
            cpnTucHcc21.setCb071(entity.getCb071());
            cpnTucHcc21.setCb072Knj(entity.getCb072Knj());
            cpnTucHcc21.setCb081(entity.getCb081());
            cpnTucHcc21.setCb082Knj(entity.getCb082Knj());
            cpnTucHcc21.setCb091(entity.getCb091());
            cpnTucHcc21.setCb101(entity.getCb101());
            cpnTucHcc21.setCb102Knj(entity.getCb102Knj());
            cpnTucHcc21.setCb111(entity.getCb111());
            cpnTucHcc21.setCb121(entity.getCb121());
            cpnTucHcc21.setCb131(entity.getCb131());
            cpnTucHcc21.setCb132Knj(entity.getCb132Knj());
            cpnTucHcc21.setCb141(entity.getCb141());
            cpnTucHcc21.setCb151(entity.getCb151());
            cpnTucHcc21.setCb161(entity.getCb161());
            cpnTucHcc21.setCb171(entity.getCb171());
            cpnTucHcc21.setCb172Knj(entity.getCb172Knj());
            cpnTucHcc21.setCb181(entity.getCb181());
            cpnTucHcc21.setCb191(entity.getCb191());
            cpnTucHcc21.setCb201(entity.getCb201());
            cpnTucHcc21.setCb211(entity.getCb211());
            cpnTucHcc21.setCb221(entity.getCb221());
            cpnTucHcc21.setCb222Knj(entity.getCb222Knj());
            cpnTucHcc21.setCb231(entity.getCb231());
            cpnTucHcc21.setCb232Knj(entity.getCb232Knj());
            cpnTucHcc21.setCb241(entity.getCb241());
            cpnTucHcc21.setCb242Knj(entity.getCb242Knj());
            cpnTucHcc21.setCb251(entity.getCb251());
            cpnTucHcc21.setCb252Knj(entity.getCb252Knj());
            cpnTucHcc21.setCb261(entity.getCb261());
            cpnTucHcc21.setCb262Knj(entity.getCb262Knj());
            cpnTucHcc21.setCb271(entity.getCb271());
            cpnTucHcc21.setCb272Knj(entity.getCb272Knj());
            // 共通カラム値設定処理
            cpnTucHcc21Mapper.insertSelective(cpnTucHcc21);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（２．排泄）】テーブルを登録する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @return 採番した課題立案履歴ＩＤ
     */
    private void insertCpnTucHcc22(DuplicateAssessmentUpdateServiceInDto inDto, Integer tgtGdlId, Integer scIdTmp0,
            List<CpnTucHcc22InfoOutEntity> specificList) {
        CpnTucHcc22 cpnTucHcc22 = new CpnTucHcc22();

        for (CpnTucHcc22InfoOutEntity entity : specificList) {
            cpnTucHcc22.setCc1Id(tgtGdlId);
            cpnTucHcc22.setSc1Id(scIdTmp0);
            cpnTucHcc22.setCn011(entity.getCn011());
            cpnTucHcc22.setCn012(entity.getCn012());
            cpnTucHcc22.setCn013(entity.getCn013());
            cpnTucHcc22.setCn021(entity.getCn021());
            cpnTucHcc22.setCn022(entity.getCn022());
            cpnTucHcc22.setCn023(entity.getCn023());
            cpnTucHcc22.setCn031(entity.getCn031());
            cpnTucHcc22.setCn032(entity.getCn032());
            cpnTucHcc22.setCn033(entity.getCn033());
            cpnTucHcc22.setCn041(entity.getCn041());
            cpnTucHcc22.setCn042(entity.getCn042());
            cpnTucHcc22.setCn043(entity.getCn043());
            cpnTucHcc22.setCn051(entity.getCn051());
            cpnTucHcc22.setCn052(entity.getCn052());
            cpnTucHcc22.setCn053(entity.getCn053());
            cpnTucHcc22.setCn061(entity.getCn061());
            cpnTucHcc22.setCn062(entity.getCn062());
            cpnTucHcc22.setCn063(entity.getCn063());
            cpnTucHcc22.setCn071(entity.getCn071());
            cpnTucHcc22.setCn072(entity.getCn072());
            cpnTucHcc22.setCn073(entity.getCn073());
            cpnTucHcc22.setCn081(entity.getCn081());
            cpnTucHcc22.setCn082(entity.getCn082());
            cpnTucHcc22.setCn083(entity.getCn083());
            cpnTucHcc22.setCn091(entity.getCn091());
            cpnTucHcc22.setCn092(entity.getCn092());
            cpnTucHcc22.setCn093(entity.getCn093());
            cpnTucHcc22.setCn101(entity.getCn101());
            cpnTucHcc22.setCn102(entity.getCn102());
            cpnTucHcc22.setCn103(entity.getCn103());
            cpnTucHcc22.setCn111(entity.getCn111());
            cpnTucHcc22.setCn112(entity.getCn112());
            cpnTucHcc22.setCn113(entity.getCn113());
            cpnTucHcc22.setCn121(entity.getCn121());
            cpnTucHcc22.setCn122(entity.getCn122());
            cpnTucHcc22.setCn123(entity.getCn123());
            cpnTucHcc22.setCn131(entity.getCn131());
            cpnTucHcc22.setCn132(entity.getCn132());
            cpnTucHcc22.setCn133(entity.getCn133());
            cpnTucHcc22.setCn141(entity.getCn141());
            cpnTucHcc22.setCn142(entity.getCn142());
            cpnTucHcc22.setCn143(entity.getCn143());
            cpnTucHcc22.setCn151(entity.getCn151());
            cpnTucHcc22.setCn152(entity.getCn152());
            cpnTucHcc22.setCn153(entity.getCn153());
            cpnTucHcc22.setCn161(entity.getCn161());
            cpnTucHcc22.setCn162(entity.getCn162());
            cpnTucHcc22.setCn163(entity.getCn163());
            cpnTucHcc22.setCn171(entity.getCn171());
            cpnTucHcc22.setCn172(entity.getCn172());
            cpnTucHcc22.setCn173(entity.getCn173());
            cpnTucHcc22.setCn181(entity.getCn181());
            cpnTucHcc22.setCn182(entity.getCn182());
            cpnTucHcc22.setCn183(entity.getCn183());
            cpnTucHcc22.setCn191(entity.getCn191());
            cpnTucHcc22.setCn192(entity.getCn192());
            cpnTucHcc22.setCn193(entity.getCn193());
            cpnTucHcc22.setCn201(entity.getCn201());
            cpnTucHcc22.setCn202(entity.getCn202());
            cpnTucHcc22.setCn203(entity.getCn203());
            cpnTucHcc22.setCn211(entity.getCn211());
            cpnTucHcc22.setCn212(entity.getCn212());
            cpnTucHcc22.setCn213(entity.getCn213());
            cpnTucHcc22.setCn221(entity.getCn221());
            cpnTucHcc22.setCn222(entity.getCn222());
            cpnTucHcc22.setCn223(entity.getCn223());
            cpnTucHcc22.setCn231(entity.getCn231());
            cpnTucHcc22.setCn232(entity.getCn232());
            cpnTucHcc22.setCn233(entity.getCn233());
            cpnTucHcc22.setCn241(entity.getCn241());
            cpnTucHcc22.setCn242(entity.getCn242());
            cpnTucHcc22.setCn243(entity.getCn243());
            cpnTucHcc22.setCn251(entity.getCn251());
            cpnTucHcc22.setCn252(entity.getCn252());
            cpnTucHcc22.setCn253(entity.getCn253());
            cpnTucHcc22.setCn261(entity.getCn261());
            cpnTucHcc22.setCn262(entity.getCn262());
            cpnTucHcc22.setCn263(entity.getCn263());
            cpnTucHcc22.setCn271(entity.getCn271());
            cpnTucHcc22.setCn272(entity.getCn272());
            cpnTucHcc22.setCn273(entity.getCn273());
            cpnTucHcc22.setCn281(entity.getCn281());
            cpnTucHcc22.setCn282(entity.getCn282());
            cpnTucHcc22.setCn283(entity.getCn283());
            cpnTucHcc22.setCn291(entity.getCn291());
            cpnTucHcc22.setCn292(entity.getCn292());
            cpnTucHcc22.setCn293(entity.getCn293());
            cpnTucHcc22.setCn301(entity.getCn301());
            cpnTucHcc22.setCn302(entity.getCn302());
            cpnTucHcc22.setCn303(entity.getCn303());
            cpnTucHcc22.setCb011(entity.getCb011());
            cpnTucHcc22.setCb021(entity.getCb021());
            cpnTucHcc22.setCb031(entity.getCb031());
            cpnTucHcc22.setCb041(entity.getCb041());
            cpnTucHcc22.setCb042Knj(entity.getCb042Knj());
            cpnTucHcc22.setCb051(entity.getCb051());
            cpnTucHcc22.setCb061(entity.getCb061());
            cpnTucHcc22.setCb071(entity.getCb071());
            cpnTucHcc22.setCb081(entity.getCb081());
            cpnTucHcc22.setCb091(entity.getCb091());
            cpnTucHcc22.setCb101(entity.getCb101());
            cpnTucHcc22.setCb111(entity.getCb111());
            cpnTucHcc22.setCb121(entity.getCb121());
            cpnTucHcc22.setCb131(entity.getCb131());
            cpnTucHcc22.setCb141(entity.getCb141());
            cpnTucHcc22.setCb151(entity.getCb151());
            cpnTucHcc22.setCb161(entity.getCb161());
            cpnTucHcc22.setCb162Knj(entity.getCb162Knj());
            cpnTucHcc22.setCb171(entity.getCb171());
            cpnTucHcc22.setCb172Knj(entity.getCb172Knj());
            cpnTucHcc22.setCb181(entity.getCb181());
            cpnTucHcc22.setCb182Knj(entity.getCb182Knj());
            cpnTucHcc22.setCb191(entity.getCb191());
            cpnTucHcc22.setCb192Knj(entity.getCb192Knj());
            cpnTucHcc22.setCb201(entity.getCb201());
            cpnTucHcc22.setCb202Knj(entity.getCb202Knj());
            cpnTucHcc22.setCb211(entity.getCb211());
            cpnTucHcc22.setCb212Knj(entity.getCb212Knj());
            cpnTucHcc22.setCb221(entity.getCb221());
            cpnTucHcc22.setCb222Knj(entity.getCb222Knj());
            cpnTucHcc22.setCb231(entity.getCb231());
            cpnTucHcc22.setCb232Knj(entity.getCb232Knj());
            cpnTucHcc22.setCb241(entity.getCb241());
            cpnTucHcc22.setCb242Knj(entity.getCb242Knj());
            cpnTucHcc22.setCb251(entity.getCb251());
            cpnTucHcc22.setCb252Knj(entity.getCb252Knj());
            cpnTucHcc22.setCb261(entity.getCb261());
            cpnTucHcc22.setCb262Knj(entity.getCb262Knj());
            cpnTucHcc22.setCb271(entity.getCb271());
            cpnTucHcc22.setCb272Knj(entity.getCb272Knj());
            cpnTucHcc22.setCb281(entity.getCb281());
            cpnTucHcc22.setCb282Knj(entity.getCb282Knj());
            cpnTucHcc22.setCb291(entity.getCb291());
            cpnTucHcc22.setCb292Knj(entity.getCb292Knj());
            cpnTucHcc22.setCb301(entity.getCb301());
            cpnTucHcc22.setCb302Knj(entity.getCb302Knj());

            // 共通カラム値設定処理
            cpnTucHcc22Mapper.insertSelective(cpnTucHcc22);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（３．入浴）】テーブルを登録する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @return 採番した課題立案履歴ＩＤ
     */
    private void insertCpnTucHcc23(DuplicateAssessmentUpdateServiceInDto inDto, Integer tgtGdlId, Integer scIdTmp0,
            List<CpnTucHcc23InfoOutEntity> specificList) {
        CpnTucHcc23 cpnTucHcc23 = new CpnTucHcc23();
        for (CpnTucHcc23InfoOutEntity entity : specificList) {
            cpnTucHcc23.setCc1Id(tgtGdlId);
            cpnTucHcc23.setSc1Id(scIdTmp0);
            cpnTucHcc23.setCn011(entity.getCn011());
            cpnTucHcc23.setCn012(entity.getCn012());
            cpnTucHcc23.setCn013(entity.getCn013());
            cpnTucHcc23.setCn021(entity.getCn021());
            cpnTucHcc23.setCn022(entity.getCn022());
            cpnTucHcc23.setCn023(entity.getCn023());
            cpnTucHcc23.setCn031(entity.getCn031());
            cpnTucHcc23.setCn032(entity.getCn032());
            cpnTucHcc23.setCn033(entity.getCn033());
            cpnTucHcc23.setCn041(entity.getCn041());
            cpnTucHcc23.setCn042(entity.getCn042());
            cpnTucHcc23.setCn043(entity.getCn043());
            cpnTucHcc23.setCn051(entity.getCn051());
            cpnTucHcc23.setCn052(entity.getCn052());
            cpnTucHcc23.setCn053(entity.getCn053());
            cpnTucHcc23.setCn061(entity.getCn061());
            cpnTucHcc23.setCn062(entity.getCn062());
            cpnTucHcc23.setCn063(entity.getCn063());
            cpnTucHcc23.setCn071(entity.getCn071());
            cpnTucHcc23.setCn072(entity.getCn072());
            cpnTucHcc23.setCn073(entity.getCn073());
            cpnTucHcc23.setCn081(entity.getCn081());
            cpnTucHcc23.setCn082(entity.getCn082());
            cpnTucHcc23.setCn083(entity.getCn083());
            cpnTucHcc23.setCn091(entity.getCn091());
            cpnTucHcc23.setCn092(entity.getCn092());
            cpnTucHcc23.setCn093(entity.getCn093());
            cpnTucHcc23.setCn101(entity.getCn101());
            cpnTucHcc23.setCn102(entity.getCn102());
            cpnTucHcc23.setCn103(entity.getCn103());
            cpnTucHcc23.setCn111(entity.getCn111());
            cpnTucHcc23.setCn112(entity.getCn112());
            cpnTucHcc23.setCn113(entity.getCn113());
            cpnTucHcc23.setCn121(entity.getCn121());
            cpnTucHcc23.setCn122(entity.getCn122());
            cpnTucHcc23.setCn123(entity.getCn123());
            cpnTucHcc23.setCn131(entity.getCn131());
            cpnTucHcc23.setCn132(entity.getCn132());
            cpnTucHcc23.setCn133(entity.getCn133());
            cpnTucHcc23.setCn141(entity.getCn141());
            cpnTucHcc23.setCn142(entity.getCn142());
            cpnTucHcc23.setCn143(entity.getCn143());
            cpnTucHcc23.setCn151(entity.getCn151());
            cpnTucHcc23.setCn152(entity.getCn152());
            cpnTucHcc23.setCn153(entity.getCn153());
            cpnTucHcc23.setCn161(entity.getCn161());
            cpnTucHcc23.setCn162(entity.getCn162());
            cpnTucHcc23.setCn163(entity.getCn163());
            cpnTucHcc23.setCn171(entity.getCn171());
            cpnTucHcc23.setCn172(entity.getCn172());
            cpnTucHcc23.setCn173(entity.getCn173());
            cpnTucHcc23.setCn181(entity.getCn181());
            cpnTucHcc23.setCn182(entity.getCn182());
            cpnTucHcc23.setCn183(entity.getCn183());
            cpnTucHcc23.setCn191(entity.getCn191());
            cpnTucHcc23.setCn192(entity.getCn192());
            cpnTucHcc23.setCn193(entity.getCn193());
            cpnTucHcc23.setCn201(entity.getCn201());
            cpnTucHcc23.setCn202(entity.getCn202());
            cpnTucHcc23.setCn203(entity.getCn203());
            cpnTucHcc23.setCn211(entity.getCn211());
            cpnTucHcc23.setCn212(entity.getCn212());
            cpnTucHcc23.setCn213(entity.getCn213());
            cpnTucHcc23.setCn221(entity.getCn221());
            cpnTucHcc23.setCn222(entity.getCn222());
            cpnTucHcc23.setCn223(entity.getCn223());
            cpnTucHcc23.setCn231(entity.getCn231());
            cpnTucHcc23.setCn232(entity.getCn232());
            cpnTucHcc23.setCn233(entity.getCn233());
            cpnTucHcc23.setCn241(entity.getCn241());
            cpnTucHcc23.setCn242(entity.getCn242());
            cpnTucHcc23.setCn243(entity.getCn243());
            cpnTucHcc23.setCn251(entity.getCn251());
            cpnTucHcc23.setCn252(entity.getCn252());
            cpnTucHcc23.setCn253(entity.getCn253());
            cpnTucHcc23.setCn261(entity.getCn261());
            cpnTucHcc23.setCn262(entity.getCn262());
            cpnTucHcc23.setCn263(entity.getCn263());
            cpnTucHcc23.setCn271(entity.getCn271());
            cpnTucHcc23.setCn272(entity.getCn272());
            cpnTucHcc23.setCn273(entity.getCn273());
            cpnTucHcc23.setCn281(entity.getCn281());
            cpnTucHcc23.setCn282(entity.getCn282());
            cpnTucHcc23.setCn283(entity.getCn283());
            cpnTucHcc23.setCn291(entity.getCn291());
            cpnTucHcc23.setCn292(entity.getCn292());
            cpnTucHcc23.setCn293(entity.getCn293());
            cpnTucHcc23.setCn301(entity.getCn301());
            cpnTucHcc23.setCn302(entity.getCn302());
            cpnTucHcc23.setCn303(entity.getCn303());
            cpnTucHcc23.setCb011(entity.getCb011());
            cpnTucHcc23.setCb021(entity.getCb021());
            cpnTucHcc23.setCb031(entity.getCb031());
            cpnTucHcc23.setCb041(entity.getCb041());
            cpnTucHcc23.setCb051(entity.getCb051());
            cpnTucHcc23.setCb061(entity.getCb061());
            cpnTucHcc23.setCb062Knj(entity.getCb062Knj());
            cpnTucHcc23.setCb071(entity.getCb071());
            cpnTucHcc23.setCb081(entity.getCb081());
            cpnTucHcc23.setCb091(entity.getCb091());
            cpnTucHcc23.setCb101(entity.getCb101());
            cpnTucHcc23.setCb111(entity.getCb111());
            cpnTucHcc23.setCb121(entity.getCb121());
            cpnTucHcc23.setCb131(entity.getCb131());
            cpnTucHcc23.setCb141(entity.getCb141());
            cpnTucHcc23.setCb151(entity.getCb151());
            cpnTucHcc23.setCb161(entity.getCb161());
            cpnTucHcc23.setCb171(entity.getCb171());
            cpnTucHcc23.setCb172Knj(entity.getCb172Knj());
            cpnTucHcc23.setCb181(entity.getCb181());
            cpnTucHcc23.setCb182Knj(entity.getCb182Knj());
            cpnTucHcc23.setCb191(entity.getCb191());
            cpnTucHcc23.setCb192Knj(entity.getCb192Knj());
            cpnTucHcc23.setCb201(entity.getCb201());
            cpnTucHcc23.setCb202Knj(entity.getCb202Knj());
            cpnTucHcc23.setCb211(entity.getCb211());
            cpnTucHcc23.setCb212Knj(entity.getCb212Knj());
            cpnTucHcc23.setCb221(entity.getCb221());
            cpnTucHcc23.setCb222Knj(entity.getCb222Knj());
            cpnTucHcc23.setCb231(entity.getCb231());
            cpnTucHcc23.setCb232Knj(entity.getCb232Knj());
            cpnTucHcc23.setCb241(entity.getCb241());
            cpnTucHcc23.setCb251(entity.getCb251());
            cpnTucHcc23.setCb261(entity.getCb261());
            cpnTucHcc23.setCb271(entity.getCb271());
            cpnTucHcc23.setCb281(entity.getCb281());
            cpnTucHcc23.setCb282Knj(entity.getCb282Knj());
            cpnTucHcc23.setCb291(entity.getCb291());
            cpnTucHcc23.setCb292Knj(entity.getCb292Knj());
            cpnTucHcc23.setCb301(entity.getCb301());
            cpnTucHcc23.setCb302Knj(entity.getCb302Knj());

            // 共通カラム値設定処理
            cpnTucHcc23Mapper.insertSelective(cpnTucHcc23);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（4．洗面）】テーブルを登録する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @return 採番した課題立案履歴ＩＤ
     */
    private void insertCpnTucHcc24(DuplicateAssessmentUpdateServiceInDto inDto, Integer tgtGdlId, Integer scIdTmp0,
            List<CpnTucHcc24InfoOutEntity> specificList) {
        CpnTucHcc24 cpnTucHcc24 = new CpnTucHcc24();

        for (CpnTucHcc24InfoOutEntity entity : specificList) {
            cpnTucHcc24.setCc1Id(tgtGdlId);
            cpnTucHcc24.setSc1Id(scIdTmp0);
            cpnTucHcc24.setCn011(entity.getCn011());
            cpnTucHcc24.setCn012(entity.getCn012());
            cpnTucHcc24.setCn013(entity.getCn013());
            cpnTucHcc24.setCn021(entity.getCn021());
            cpnTucHcc24.setCn022(entity.getCn022());
            cpnTucHcc24.setCn023(entity.getCn023());
            cpnTucHcc24.setCn031(entity.getCn031());
            cpnTucHcc24.setCn032(entity.getCn032());
            cpnTucHcc24.setCn033(entity.getCn033());
            cpnTucHcc24.setCn041(entity.getCn041());
            cpnTucHcc24.setCn042(entity.getCn042());
            cpnTucHcc24.setCn043(entity.getCn043());
            cpnTucHcc24.setCn051(entity.getCn051());
            cpnTucHcc24.setCn052(entity.getCn052());
            cpnTucHcc24.setCn053(entity.getCn053());
            cpnTucHcc24.setCn061(entity.getCn061());
            cpnTucHcc24.setCn062(entity.getCn062());
            cpnTucHcc24.setCn063(entity.getCn063());
            cpnTucHcc24.setCn071(entity.getCn071());
            cpnTucHcc24.setCn072(entity.getCn072());
            cpnTucHcc24.setCn073(entity.getCn073());
            cpnTucHcc24.setCn081(entity.getCn081());
            cpnTucHcc24.setCn082(entity.getCn082());
            cpnTucHcc24.setCn083(entity.getCn083());
            cpnTucHcc24.setCn091(entity.getCn091());
            cpnTucHcc24.setCn092(entity.getCn092());
            cpnTucHcc24.setCn093(entity.getCn093());
            cpnTucHcc24.setCn101(entity.getCn101());
            cpnTucHcc24.setCn102(entity.getCn102());
            cpnTucHcc24.setCn103(entity.getCn103());
            cpnTucHcc24.setCn111(entity.getCn111());
            cpnTucHcc24.setCn112(entity.getCn112());
            cpnTucHcc24.setCn113(entity.getCn113());
            cpnTucHcc24.setCn121(entity.getCn121());
            cpnTucHcc24.setCn122(entity.getCn122());
            cpnTucHcc24.setCn123(entity.getCn123());
            cpnTucHcc24.setCn131(entity.getCn131());
            cpnTucHcc24.setCn132(entity.getCn132());
            cpnTucHcc24.setCn133(entity.getCn133());
            cpnTucHcc24.setCn141(entity.getCn141());
            cpnTucHcc24.setCn142(entity.getCn142());
            cpnTucHcc24.setCn143(entity.getCn143());
            cpnTucHcc24.setCn151(entity.getCn151());
            cpnTucHcc24.setCn152(entity.getCn152());
            cpnTucHcc24.setCn153(entity.getCn153());
            cpnTucHcc24.setCn161(entity.getCn161());
            cpnTucHcc24.setCn162(entity.getCn162());
            cpnTucHcc24.setCn163(entity.getCn163());
            cpnTucHcc24.setCn171(entity.getCn171());
            cpnTucHcc24.setCn172(entity.getCn172());
            cpnTucHcc24.setCn173(entity.getCn173());
            cpnTucHcc24.setCn181(entity.getCn181());
            cpnTucHcc24.setCn182(entity.getCn182());
            cpnTucHcc24.setCn183(entity.getCn183());
            cpnTucHcc24.setCn191(entity.getCn191());
            cpnTucHcc24.setCn192(entity.getCn192());
            cpnTucHcc24.setCn193(entity.getCn193());
            cpnTucHcc24.setCn201(entity.getCn201());
            cpnTucHcc24.setCn202(entity.getCn202());
            cpnTucHcc24.setCn203(entity.getCn203());
            cpnTucHcc24.setCb011(entity.getCb011());
            cpnTucHcc24.setCb021(entity.getCb021());
            cpnTucHcc24.setCb031(entity.getCb031());
            cpnTucHcc24.setCb041(entity.getCb041());
            cpnTucHcc24.setCb051(entity.getCb051());
            cpnTucHcc24.setCb052Knj(entity.getCb052Knj());
            cpnTucHcc24.setCb061(entity.getCb061());
            cpnTucHcc24.setCb071(entity.getCb071());
            cpnTucHcc24.setCb081(entity.getCb081());
            cpnTucHcc24.setCb082Knj(entity.getCb082Knj());
            cpnTucHcc24.setCb091(entity.getCb091());
            cpnTucHcc24.setCb092Knj(entity.getCb092Knj());
            cpnTucHcc24.setCb101(entity.getCb101());
            cpnTucHcc24.setCb102Knj(entity.getCb102Knj());
            cpnTucHcc24.setCb111(entity.getCb111());
            cpnTucHcc24.setCb112Knj(entity.getCb112Knj());
            cpnTucHcc24.setCb121(entity.getCb121());
            cpnTucHcc24.setCb131(entity.getCb131());
            cpnTucHcc24.setCb141(entity.getCb141());
            cpnTucHcc24.setCb151(entity.getCb151());
            cpnTucHcc24.setCb152Knj(entity.getCb152Knj());
            cpnTucHcc24.setCb161(entity.getCb161());
            cpnTucHcc24.setCb162Knj(entity.getCb162Knj());
            cpnTucHcc24.setCb171(entity.getCb171());
            cpnTucHcc24.setCb172Knj(entity.getCb172Knj());
            cpnTucHcc24.setCb181(entity.getCb181());
            cpnTucHcc24.setCb182Knj(entity.getCb182Knj());
            cpnTucHcc24.setCb191(entity.getCb191());
            cpnTucHcc24.setCb192Knj(entity.getCb192Knj());
            cpnTucHcc24.setCb201(entity.getCb201());
            cpnTucHcc24.setCb202Knj(entity.getCb202Knj());

            // 共通カラム値設定処理
            cpnTucHcc24Mapper.insertSelective(cpnTucHcc24);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（５．基本）】テーブルを登録する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @return 採番した課題立案履歴ＩＤ
     */
    private void insertCpnTucHcc25(DuplicateAssessmentUpdateServiceInDto inDto, Integer tgtGdlId, Integer scIdTmp0,
            List<CpnTucHcc25InfoOutEntity> specificList) {
        CpnTucHcc25 cpnTucHcc25 = new CpnTucHcc25();

        for (CpnTucHcc25InfoOutEntity entity : specificList) {
            cpnTucHcc25.setCc1Id(tgtGdlId);
            cpnTucHcc25.setSc1Id(scIdTmp0);
            cpnTucHcc25.setCn011(entity.getCn011());
            cpnTucHcc25.setCn012(entity.getCn012());
            cpnTucHcc25.setCn013(entity.getCn013());
            cpnTucHcc25.setCn021(entity.getCn021());
            cpnTucHcc25.setCn022(entity.getCn022());
            cpnTucHcc25.setCn023(entity.getCn023());
            cpnTucHcc25.setCn031(entity.getCn031());
            cpnTucHcc25.setCn032(entity.getCn032());
            cpnTucHcc25.setCn033(entity.getCn033());
            cpnTucHcc25.setCn041(entity.getCn041());
            cpnTucHcc25.setCn042(entity.getCn042());
            cpnTucHcc25.setCn043(entity.getCn043());
            cpnTucHcc25.setCn051(entity.getCn051());
            cpnTucHcc25.setCn052(entity.getCn052());
            cpnTucHcc25.setCn053(entity.getCn053());
            cpnTucHcc25.setCn061(entity.getCn061());
            cpnTucHcc25.setCn062(entity.getCn062());
            cpnTucHcc25.setCn063(entity.getCn063());
            cpnTucHcc25.setCn071(entity.getCn071());
            cpnTucHcc25.setCn072(entity.getCn072());
            cpnTucHcc25.setCn073(entity.getCn073());
            cpnTucHcc25.setCn081(entity.getCn081());
            cpnTucHcc25.setCn082(entity.getCn082());
            cpnTucHcc25.setCn083(entity.getCn083());
            cpnTucHcc25.setCn091(entity.getCn091());
            cpnTucHcc25.setCn092(entity.getCn092());
            cpnTucHcc25.setCn093(entity.getCn093());
            cpnTucHcc25.setCn101(entity.getCn101());
            cpnTucHcc25.setCn102(entity.getCn102());
            cpnTucHcc25.setCn103(entity.getCn103());
            cpnTucHcc25.setCn111(entity.getCn111());
            cpnTucHcc25.setCn112(entity.getCn112());
            cpnTucHcc25.setCn113(entity.getCn113());
            cpnTucHcc25.setCn121(entity.getCn121());
            cpnTucHcc25.setCn122(entity.getCn122());
            cpnTucHcc25.setCn123(entity.getCn123());
            cpnTucHcc25.setCn131(entity.getCn131());
            cpnTucHcc25.setCn132(entity.getCn132());
            cpnTucHcc25.setCn133(entity.getCn133());
            cpnTucHcc25.setCn141(entity.getCn141());
            cpnTucHcc25.setCn142(entity.getCn142());
            cpnTucHcc25.setCn143(entity.getCn143());
            cpnTucHcc25.setCn151(entity.getCn151());
            cpnTucHcc25.setCn152(entity.getCn152());
            cpnTucHcc25.setCn153(entity.getCn153());
            cpnTucHcc25.setCn161(entity.getCn161());
            cpnTucHcc25.setCn162(entity.getCn162());
            cpnTucHcc25.setCn163(entity.getCn163());
            cpnTucHcc25.setCn171(entity.getCn171());
            cpnTucHcc25.setCn172(entity.getCn172());
            cpnTucHcc25.setCn173(entity.getCn173());
            cpnTucHcc25.setCn181(entity.getCn181());
            cpnTucHcc25.setCn182(entity.getCn182());
            cpnTucHcc25.setCn183(entity.getCn183());
            cpnTucHcc25.setCn191(entity.getCn191());
            cpnTucHcc25.setCn192(entity.getCn192());
            cpnTucHcc25.setCn193(entity.getCn193());
            cpnTucHcc25.setCn201(entity.getCn201());
            cpnTucHcc25.setCn202(entity.getCn202());
            cpnTucHcc25.setCn203(entity.getCn203());
            cpnTucHcc25.setCn211(entity.getCn211());
            cpnTucHcc25.setCn212(entity.getCn212());
            cpnTucHcc25.setCn213(entity.getCn213());
            cpnTucHcc25.setCn221(entity.getCn221());
            cpnTucHcc25.setCn222(entity.getCn222());
            cpnTucHcc25.setCn223(entity.getCn223());
            cpnTucHcc25.setCn231(entity.getCn231());
            cpnTucHcc25.setCn232(entity.getCn232());
            cpnTucHcc25.setCn233(entity.getCn233());
            cpnTucHcc25.setCn241(entity.getCn241());
            cpnTucHcc25.setCn242(entity.getCn242());
            cpnTucHcc25.setCn243(entity.getCn243());
            cpnTucHcc25.setCn250Knj(entity.getCn250Knj());
            cpnTucHcc25.setCn251(entity.getCn251());
            cpnTucHcc25.setCn252(entity.getCn252());
            cpnTucHcc25.setCn253(entity.getCn253());
            cpnTucHcc25.setCn260Knj(entity.getCn260Knj());
            cpnTucHcc25.setCn261(entity.getCn261());
            cpnTucHcc25.setCn262(entity.getCn262());
            cpnTucHcc25.setCn263(entity.getCn263());
            cpnTucHcc25.setCn270Knj(entity.getCn270Knj());
            cpnTucHcc25.setCn271(entity.getCn271());
            cpnTucHcc25.setCn272(entity.getCn272());
            cpnTucHcc25.setCn273(entity.getCn273());
            cpnTucHcc25.setCn280Knj(entity.getCn280Knj());
            cpnTucHcc25.setCn281(entity.getCn281());
            cpnTucHcc25.setCn282(entity.getCn282());
            cpnTucHcc25.setCn283(entity.getCn283());
            cpnTucHcc25.setCn290Knj(entity.getCn290Knj());
            cpnTucHcc25.setCn291(entity.getCn291());
            cpnTucHcc25.setCn292(entity.getCn292());
            cpnTucHcc25.setCn293(entity.getCn293());
            cpnTucHcc25.setCn300Knj(entity.getCn300Knj());
            cpnTucHcc25.setCn301(entity.getCn301());
            cpnTucHcc25.setCn302(entity.getCn302());
            cpnTucHcc25.setCn303(entity.getCn303());
            cpnTucHcc25.setCn310Knj(entity.getCn310Knj());
            cpnTucHcc25.setCn311(entity.getCn311());
            cpnTucHcc25.setCn312(entity.getCn312());
            cpnTucHcc25.setCn313(entity.getCn313());
            cpnTucHcc25.setCn320Knj(entity.getCn320Knj());
            cpnTucHcc25.setCn321(entity.getCn321());
            cpnTucHcc25.setCn322(entity.getCn322());
            cpnTucHcc25.setCn323(entity.getCn323());
            cpnTucHcc25.setCn330Knj(entity.getCn330Knj());
            cpnTucHcc25.setCn331(entity.getCn331());
            cpnTucHcc25.setCn332(entity.getCn332());
            cpnTucHcc25.setCn333(entity.getCn333());
            cpnTucHcc25.setCn340Knj(entity.getCn340Knj());
            cpnTucHcc25.setCn341(entity.getCn341());
            cpnTucHcc25.setCn342(entity.getCn342());
            cpnTucHcc25.setCn343(entity.getCn343());
            cpnTucHcc25.setCn350Knj(entity.getCn350Knj());
            cpnTucHcc25.setCn351(entity.getCn351());
            cpnTucHcc25.setCn352(entity.getCn352());
            cpnTucHcc25.setCn353(entity.getCn353());
            cpnTucHcc25.setCb011(entity.getCb011());
            cpnTucHcc25.setCb021(entity.getCb021());
            cpnTucHcc25.setCb031(entity.getCb031());
            cpnTucHcc25.setCb041(entity.getCb041());
            cpnTucHcc25.setCb051(entity.getCb051());
            cpnTucHcc25.setCb061(entity.getCb061());
            cpnTucHcc25.setCb071(entity.getCb071());
            cpnTucHcc25.setCb072Knj(entity.getCb072Knj());
            cpnTucHcc25.setCb081(entity.getCb081());
            cpnTucHcc25.setCb091(entity.getCb091());
            cpnTucHcc25.setCb101(entity.getCb101());
            cpnTucHcc25.setCb111(entity.getCb111());
            cpnTucHcc25.setCb121(entity.getCb121());
            cpnTucHcc25.setCb122Knj(entity.getCb122Knj());
            cpnTucHcc25.setCb131(entity.getCb131());
            cpnTucHcc25.setCb141(entity.getCb141());
            cpnTucHcc25.setCb151(entity.getCb151());
            cpnTucHcc25.setCb161(entity.getCb161());
            cpnTucHcc25.setCb171(entity.getCb171());
            cpnTucHcc25.setCb181(entity.getCb181());
            cpnTucHcc25.setCb191(entity.getCb191());
            cpnTucHcc25.setCb192Knj(entity.getCb192Knj());
            cpnTucHcc25.setCb201(entity.getCb201());
            cpnTucHcc25.setCb202Knj(entity.getCb202Knj());
            cpnTucHcc25.setCb211(entity.getCb211());
            cpnTucHcc25.setCb212Knj(entity.getCb212Knj());
            cpnTucHcc25.setCb221(entity.getCb221());
            cpnTucHcc25.setCb231(entity.getCb231());
            cpnTucHcc25.setCb241(entity.getCb241());
            cpnTucHcc25.setCb242Knj(entity.getCb242Knj());
            cpnTucHcc25.setCb251(entity.getCb251());
            cpnTucHcc25.setCb252Knj(entity.getCb252Knj());
            cpnTucHcc25.setCb261(entity.getCb261());
            cpnTucHcc25.setCb262Knj(entity.getCb262Knj());
            cpnTucHcc25.setCb271(entity.getCb271());
            cpnTucHcc25.setCb281(entity.getCb281());
            cpnTucHcc25.setCb282Knj(entity.getCb282Knj());
            cpnTucHcc25.setCb291(entity.getCb291());
            cpnTucHcc25.setCb301(entity.getCb301());
            cpnTucHcc25.setCb311(entity.getCb311());
            cpnTucHcc25.setCb321(entity.getCb321());
            cpnTucHcc25.setCb331(entity.getCb331());
            cpnTucHcc25.setCb341(entity.getCb341());
            cpnTucHcc25.setCb351(entity.getCb351());
            cpnTucHcc25.setCb352Knj(entity.getCb352Knj());

            // 共通カラム値設定処理
            cpnTucHcc25Mapper.insertSelective(cpnTucHcc25);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（６．医療）】テーブルを登録する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @return 採番した課題立案履歴ＩＤ
     */
    private void insertCpnTucHcc26(DuplicateAssessmentUpdateServiceInDto inDto, Integer tgtGdlId, Integer scIdTmp0,
            List<CpnTucHcc26InfoOutEntity> specificList) {
        CpnTucHcc26 cpnTucHcc26 = new CpnTucHcc26();

        for (CpnTucHcc26InfoOutEntity entity : specificList) {
            cpnTucHcc26.setCc1Id(tgtGdlId);
            cpnTucHcc26.setSc1Id(scIdTmp0);
            cpnTucHcc26.setCn011(entity.getCn011());
            cpnTucHcc26.setCn012(entity.getCn012());
            cpnTucHcc26.setCn013(entity.getCn013());
            cpnTucHcc26.setCn021(entity.getCn021());
            cpnTucHcc26.setCn022(entity.getCn022());
            cpnTucHcc26.setCn023(entity.getCn023());
            cpnTucHcc26.setCn031(entity.getCn031());
            cpnTucHcc26.setCn032(entity.getCn032());
            cpnTucHcc26.setCn033(entity.getCn033());
            cpnTucHcc26.setCn041(entity.getCn041());
            cpnTucHcc26.setCn042(entity.getCn042());
            cpnTucHcc26.setCn043(entity.getCn043());
            cpnTucHcc26.setCn051(entity.getCn051());
            cpnTucHcc26.setCn052(entity.getCn052());
            cpnTucHcc26.setCn053(entity.getCn053());
            cpnTucHcc26.setCn061(entity.getCn061());
            cpnTucHcc26.setCn062(entity.getCn062());
            cpnTucHcc26.setCn063(entity.getCn063());
            cpnTucHcc26.setCn071(entity.getCn071());
            cpnTucHcc26.setCn072(entity.getCn072());
            cpnTucHcc26.setCn073(entity.getCn073());
            cpnTucHcc26.setCn081(entity.getCn081());
            cpnTucHcc26.setCn082(entity.getCn082());
            cpnTucHcc26.setCn083(entity.getCn083());
            cpnTucHcc26.setCn091(entity.getCn091());
            cpnTucHcc26.setCn092(entity.getCn092());
            cpnTucHcc26.setCn093(entity.getCn093());
            cpnTucHcc26.setCn101(entity.getCn101());
            cpnTucHcc26.setCn102(entity.getCn102());
            cpnTucHcc26.setCn103(entity.getCn103());
            cpnTucHcc26.setCn111(entity.getCn111());
            cpnTucHcc26.setCn112(entity.getCn112());
            cpnTucHcc26.setCn113(entity.getCn113());
            cpnTucHcc26.setCn121(entity.getCn121());
            cpnTucHcc26.setCn122(entity.getCn122());
            cpnTucHcc26.setCn123(entity.getCn123());
            cpnTucHcc26.setCn131(entity.getCn131());
            cpnTucHcc26.setCn132(entity.getCn132());
            cpnTucHcc26.setCn133(entity.getCn133());
            cpnTucHcc26.setCn141(entity.getCn141());
            cpnTucHcc26.setCn142(entity.getCn142());
            cpnTucHcc26.setCn143(entity.getCn143());
            cpnTucHcc26.setCn151(entity.getCn151());
            cpnTucHcc26.setCn152(entity.getCn152());
            cpnTucHcc26.setCn153(entity.getCn153());
            cpnTucHcc26.setCn161(entity.getCn161());
            cpnTucHcc26.setCn162(entity.getCn162());
            cpnTucHcc26.setCn163(entity.getCn163());
            cpnTucHcc26.setCn171(entity.getCn171());
            cpnTucHcc26.setCn172(entity.getCn172());
            cpnTucHcc26.setCn173(entity.getCn173());
            cpnTucHcc26.setCn181(entity.getCn181());
            cpnTucHcc26.setCn182(entity.getCn182());
            cpnTucHcc26.setCn183(entity.getCn183());
            cpnTucHcc26.setCn191(entity.getCn191());
            cpnTucHcc26.setCn192(entity.getCn192());
            cpnTucHcc26.setCn193(entity.getCn193());
            cpnTucHcc26.setCn201(entity.getCn201());
            cpnTucHcc26.setCn202(entity.getCn202());
            cpnTucHcc26.setCn203(entity.getCn203());
            cpnTucHcc26.setCn211(entity.getCn211());
            cpnTucHcc26.setCn212(entity.getCn212());
            cpnTucHcc26.setCn213(entity.getCn213());
            cpnTucHcc26.setCn221(entity.getCn221());
            cpnTucHcc26.setCn222(entity.getCn222());
            cpnTucHcc26.setCn223(entity.getCn223());
            cpnTucHcc26.setCn231(entity.getCn231());
            cpnTucHcc26.setCn232(entity.getCn232());
            cpnTucHcc26.setCn233(entity.getCn233());
            cpnTucHcc26.setCn241(entity.getCn241());
            cpnTucHcc26.setCn242(entity.getCn242());
            cpnTucHcc26.setCn243(entity.getCn243());
            cpnTucHcc26.setCn251(entity.getCn251());
            cpnTucHcc26.setCn252(entity.getCn252());
            cpnTucHcc26.setCn253(entity.getCn253());
            cpnTucHcc26.setCn261(entity.getCn261());
            cpnTucHcc26.setCn262(entity.getCn262());
            cpnTucHcc26.setCn263(entity.getCn263());
            cpnTucHcc26.setCn271(entity.getCn271());
            cpnTucHcc26.setCn272(entity.getCn272());
            cpnTucHcc26.setCn273(entity.getCn273());
            cpnTucHcc26.setCb011(entity.getCb011());
            cpnTucHcc26.setCb021(entity.getCb021());
            cpnTucHcc26.setCb031(entity.getCb031());
            cpnTucHcc26.setCb041(entity.getCb041());
            cpnTucHcc26.setCb051(entity.getCb051());
            cpnTucHcc26.setCb052Knj(entity.getCb052Knj());
            cpnTucHcc26.setCb061(entity.getCb061());
            cpnTucHcc26.setCb062Knj(entity.getCb062Knj());
            cpnTucHcc26.setCb071(entity.getCb071());
            cpnTucHcc26.setCb072Knj(entity.getCb072Knj());
            cpnTucHcc26.setCb081(entity.getCb081());
            cpnTucHcc26.setCb082Knj(entity.getCb082Knj());
            cpnTucHcc26.setCb091(entity.getCb091());
            cpnTucHcc26.setCb092Knj(entity.getCb092Knj());
            cpnTucHcc26.setCb101(entity.getCb101());
            cpnTucHcc26.setCb102Knj(entity.getCb102Knj());
            cpnTucHcc26.setCb111(entity.getCb111());
            cpnTucHcc26.setCb112Knj(entity.getCb112Knj());
            cpnTucHcc26.setCb121(entity.getCb121());
            cpnTucHcc26.setCb122Knj(entity.getCb122Knj());
            cpnTucHcc26.setCb131(entity.getCb131());
            cpnTucHcc26.setCb132Knj(entity.getCb132Knj());
            cpnTucHcc26.setCb141(entity.getCb141());
            cpnTucHcc26.setCb142Knj(entity.getCb142Knj());
            cpnTucHcc26.setCb151(entity.getCb151());
            cpnTucHcc26.setCb152Knj(entity.getCb152Knj());
            cpnTucHcc26.setCb161(entity.getCb161());
            cpnTucHcc26.setCb162Knj(entity.getCb162Knj());
            cpnTucHcc26.setCb171(entity.getCb171());
            cpnTucHcc26.setCb172Knj(entity.getCb172Knj());
            cpnTucHcc26.setCb181(entity.getCb181());
            cpnTucHcc26.setCb182Knj(entity.getCb182Knj());
            cpnTucHcc26.setCb191(entity.getCb191());
            cpnTucHcc26.setCb192Knj(entity.getCb192Knj());
            cpnTucHcc26.setCb201(entity.getCb201());
            cpnTucHcc26.setCb202Knj(entity.getCb202Knj());
            cpnTucHcc26.setCb211(entity.getCb211());
            cpnTucHcc26.setCb212Knj(entity.getCb212Knj());
            cpnTucHcc26.setCb221(entity.getCb221());
            cpnTucHcc26.setCb222Knj(entity.getCb222Knj());
            cpnTucHcc26.setCb231(entity.getCb231());
            cpnTucHcc26.setCb232Knj(entity.getCb232Knj());
            cpnTucHcc26.setCb241(entity.getCb241());
            cpnTucHcc26.setCb242Knj(entity.getCb242Knj());
            cpnTucHcc26.setCb251(entity.getCb251());
            cpnTucHcc26.setCb252Knj(entity.getCb252Knj());
            cpnTucHcc26.setCb261(entity.getCb261());
            cpnTucHcc26.setCb262Knj(entity.getCb262Knj());
            cpnTucHcc26.setCb271(entity.getCb271());
            cpnTucHcc26.setCb272Knj(entity.getCb272Knj());

            // 共通カラム値設定処理
            cpnTucHcc26Mapper.insertSelective(cpnTucHcc26);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（６．医療）】テーブルを登録する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @return 採番した課題立案履歴ＩＤ
     */
    private void insertCpnTucHcc27(DuplicateAssessmentUpdateServiceInDto inDto, Integer tgtGdlId, Integer scIdTmp0,
            List<CpnTucHcc27InfoOutEntity> specificList) {
        CpnTucHcc27 cpnTucHcc27 = new CpnTucHcc27();

        for (CpnTucHcc27InfoOutEntity entity : specificList) {
            cpnTucHcc27.setCc1Id(tgtGdlId);
            cpnTucHcc27.setSc1Id(scIdTmp0);
            cpnTucHcc27.setCn011(entity.getCn011());
            cpnTucHcc27.setCn012(entity.getCn012());
            cpnTucHcc27.setCn013(entity.getCn013());
            cpnTucHcc27.setCn021(entity.getCn021());
            cpnTucHcc27.setCn022(entity.getCn022());
            cpnTucHcc27.setCn023(entity.getCn023());
            cpnTucHcc27.setCn031(entity.getCn031());
            cpnTucHcc27.setCn032(entity.getCn032());
            cpnTucHcc27.setCn033(entity.getCn033());
            cpnTucHcc27.setCn041(entity.getCn041());
            cpnTucHcc27.setCn042(entity.getCn042());
            cpnTucHcc27.setCn043(entity.getCn043());
            cpnTucHcc27.setCn051(entity.getCn051());
            cpnTucHcc27.setCn052(entity.getCn052());
            cpnTucHcc27.setCn053(entity.getCn053());
            cpnTucHcc27.setCn061(entity.getCn061());
            cpnTucHcc27.setCn062(entity.getCn062());
            cpnTucHcc27.setCn063(entity.getCn063());
            cpnTucHcc27.setCn071(entity.getCn071());
            cpnTucHcc27.setCn072(entity.getCn072());
            cpnTucHcc27.setCn073(entity.getCn073());
            cpnTucHcc27.setCn081(entity.getCn081());
            cpnTucHcc27.setCn082(entity.getCn082());
            cpnTucHcc27.setCn083(entity.getCn083());
            cpnTucHcc27.setCn091(entity.getCn091());
            cpnTucHcc27.setCn092(entity.getCn092());
            cpnTucHcc27.setCn093(entity.getCn093());
            cpnTucHcc27.setCn101(entity.getCn101());
            cpnTucHcc27.setCn102(entity.getCn102());
            cpnTucHcc27.setCn103(entity.getCn103());
            cpnTucHcc27.setCn111(entity.getCn111());
            cpnTucHcc27.setCn112(entity.getCn112());
            cpnTucHcc27.setCn113(entity.getCn113());
            cpnTucHcc27.setCn121(entity.getCn121());
            cpnTucHcc27.setCn122(entity.getCn122());
            cpnTucHcc27.setCn123(entity.getCn123());
            cpnTucHcc27.setCn131(entity.getCn131());
            cpnTucHcc27.setCn132(entity.getCn132());
            cpnTucHcc27.setCn133(entity.getCn133());
            cpnTucHcc27.setCn141(entity.getCn141());
            cpnTucHcc27.setCn142(entity.getCn142());
            cpnTucHcc27.setCn143(entity.getCn143());
            cpnTucHcc27.setCn151(entity.getCn151());
            cpnTucHcc27.setCn152(entity.getCn152());
            cpnTucHcc27.setCn153(entity.getCn153());
            cpnTucHcc27.setCn161(entity.getCn161());
            cpnTucHcc27.setCn162(entity.getCn162());
            cpnTucHcc27.setCn163(entity.getCn163());
            cpnTucHcc27.setCn171(entity.getCn171());
            cpnTucHcc27.setCn172(entity.getCn172());
            cpnTucHcc27.setCn173(entity.getCn173());
            cpnTucHcc27.setCn181(entity.getCn181());
            cpnTucHcc27.setCn182(entity.getCn182());
            cpnTucHcc27.setCn183(entity.getCn183());
            cpnTucHcc27.setCn191(entity.getCn191());
            cpnTucHcc27.setCn192(entity.getCn192());
            cpnTucHcc27.setCn193(entity.getCn193());
            cpnTucHcc27.setCn201(entity.getCn201());
            cpnTucHcc27.setCn202(entity.getCn202());
            cpnTucHcc27.setCn203(entity.getCn203());
            cpnTucHcc27.setCn211(entity.getCn211());
            cpnTucHcc27.setCn212(entity.getCn212());
            cpnTucHcc27.setCn213(entity.getCn213());
            cpnTucHcc27.setCn221(entity.getCn221());
            cpnTucHcc27.setCn222(entity.getCn222());
            cpnTucHcc27.setCn223(entity.getCn223());
            cpnTucHcc27.setCn231(entity.getCn231());
            cpnTucHcc27.setCn232(entity.getCn232());
            cpnTucHcc27.setCn233(entity.getCn233());
            cpnTucHcc27.setCn241(entity.getCn241());
            cpnTucHcc27.setCn242(entity.getCn242());
            cpnTucHcc27.setCn243(entity.getCn243());
            cpnTucHcc27.setCn251(entity.getCn251());
            cpnTucHcc27.setCn252(entity.getCn252());
            cpnTucHcc27.setCn253(entity.getCn253());
            cpnTucHcc27.setCn261(entity.getCn261());
            cpnTucHcc27.setCn262(entity.getCn262());
            cpnTucHcc27.setCn263(entity.getCn263());
            cpnTucHcc27.setCb011(entity.getCb011());
            cpnTucHcc27.setCb021(entity.getCb021());
            cpnTucHcc27.setCb031(entity.getCb031());
            cpnTucHcc27.setCb041(entity.getCb041());
            cpnTucHcc27.setCb051(entity.getCb051());
            cpnTucHcc27.setCb061(entity.getCb061());
            cpnTucHcc27.setCb071(entity.getCb071());
            cpnTucHcc27.setCb072Knj(entity.getCb072Knj());
            cpnTucHcc27.setCb081(entity.getCb081());
            cpnTucHcc27.setCb082Knj(entity.getCb082Knj());
            cpnTucHcc27.setCb091(entity.getCb091());
            cpnTucHcc27.setCb092Knj(entity.getCb092Knj());
            cpnTucHcc27.setCb101(entity.getCb101());
            cpnTucHcc27.setCb102Knj(entity.getCb102Knj());
            cpnTucHcc27.setCb111(entity.getCb111());
            cpnTucHcc27.setCb112Knj(entity.getCb112Knj());
            cpnTucHcc27.setCb121(entity.getCb121());
            cpnTucHcc27.setCb122Knj(entity.getCb122Knj());
            cpnTucHcc27.setCb131(entity.getCb131());
            cpnTucHcc27.setCb132Knj(entity.getCb132Knj());
            cpnTucHcc27.setCb141(entity.getCb141());
            cpnTucHcc27.setCb142Knj(entity.getCb142Knj());
            cpnTucHcc27.setCb151(entity.getCb151());
            cpnTucHcc27.setCb152Knj(entity.getCb152Knj());
            cpnTucHcc27.setCb161(entity.getCb161());
            cpnTucHcc27.setCb162Knj(entity.getCb162Knj());
            cpnTucHcc27.setCb171(entity.getCb171());
            cpnTucHcc27.setCb172Knj(entity.getCb172Knj());
            cpnTucHcc27.setCb181(entity.getCb181());
            cpnTucHcc27.setCb182Knj(entity.getCb182Knj());
            cpnTucHcc27.setCb191(entity.getCb191());
            cpnTucHcc27.setCb192Knj(entity.getCb192Knj());
            cpnTucHcc27.setCb201(entity.getCb201());
            cpnTucHcc27.setCb202Knj(entity.getCb202Knj());
            cpnTucHcc27.setCb211(entity.getCb211());
            cpnTucHcc27.setCb212Knj(entity.getCb212Knj());
            cpnTucHcc27.setCb221(entity.getCb221());
            cpnTucHcc27.setCb222Knj(entity.getCb222Knj());
            cpnTucHcc27.setCb231(entity.getCb231());
            cpnTucHcc27.setCb232Knj(entity.getCb232Knj());
            cpnTucHcc27.setCb241(entity.getCb241());
            cpnTucHcc27.setCb242Knj(entity.getCb242Knj());
            cpnTucHcc27.setCb251(entity.getCb251());
            cpnTucHcc27.setCb252Knj(entity.getCb252Knj());
            cpnTucHcc27.setCb261(entity.getCb261());
            cpnTucHcc27.setCb262Knj(entity.getCb262Knj());
            // 共通カラム値設定処理
            cpnTucHcc27Mapper.insertSelective(cpnTucHcc27);

        }
    }

    /**
     * 【ＨＫ＿ケアチェック表（具体的内容）】テーブルを登録する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @return 採番した課題立案履歴ＩＤ
     */
    private Integer insertCpnTucHcc32(DuplicateAssessmentUpdateServiceInDto inDto, Integer gdlId, Integer sc1Id,
            CpnHokCckGutaiInfoOutEntity cpnHokCckGutaiInfoOutEntity) throws Exception {
        CpnTucHcc32 cpnTucHcc32 = new CpnTucHcc32();
        // ケアチェックID
        cpnTucHcc32.setCc1Id(gdlId);
        // 計画期間ID
        cpnTucHcc32.setSc1Id(sc1Id);
        // 大分類CD
        cpnTucHcc32.setB1Cd(cpnHokCckGutaiInfoOutEntity.getB1Cd());
        // 内容
        cpnTucHcc32.setMemoKnj(cpnHokCckGutaiInfoOutEntity.getMemoKnj());
        // 表示順
        cpnTucHcc32.setSeq(cpnHokCckGutaiInfoOutEntity.getSeq().intValue());
        // 優先順位
        if (Objects.nonNull(cpnHokCckGutaiInfoOutEntity.getJuni())) {
            cpnTucHcc32.setJuni(cpnHokCckGutaiInfoOutEntity.getJuni().intValue());
        }

        // 種類
        cpnTucHcc32.setCc32Type(cpnHokCckGutaiInfoOutEntity.getCc32Type());
        // 入力支援（具体的内容）id
        cpnTucHcc32.setCi1Id(cpnHokCckGutaiInfoOutEntity.getCi1Id());

        return cpnTucHcc32Mapper.insertSelectiveAndReturn(cpnTucHcc32);

    }

    /**
     * 【ケアチェック表（具体的内容の問題点番号）】テーブルを登録する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @return 採番した課題立案履歴ＩＤ
     */
    private void insertCpnTucHcc34(DuplicateAssessmentUpdateServiceInDto inDto, Integer tgtGdlId, Integer sc1Id,
            Integer cc32Id,
            CpnHokCckMondaiNoOutEntity cpnHokCckMondaiNoOutEntity) throws Exception {
        CpnTucHcc34 cpnTucHcc34 = new CpnTucHcc34();
        // ケアチェックID
        cpnTucHcc34.setCc1Id(tgtGdlId);
        // 計画期間ID
        cpnTucHcc34.setSc1Id(sc1Id);
        // 具体的内容ID
        cpnTucHcc34.setCc32Id(cc32Id);
        // 問題点CD
        cpnTucHcc34.setB4Cd(cpnHokCckMondaiNoOutEntity.getB4Cd());
        // 表示順
        cpnTucHcc34.setSeq(cpnHokCckMondaiNoOutEntity.getSeq().intValue());

        cpnTucHcc34Mapper.insertSelective(cpnTucHcc34);
    }

    /**
     * 【ＨＫ＿ケアチェック表（ケア項目）】テーブルを登録する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @return 採番した課題立案履歴ＩＤ
     */
    private void insertCpnTucHcc33(DuplicateAssessmentUpdateServiceInDto inDto, Integer gdlId, Integer sc1Id,
            Integer cc32Id,
            CpnTucHcc33InfoOutEntity cpnTucHcc33InfoOutEntity) throws Exception {
        CpnTucHcc33 cpnTucHcc33 = new CpnTucHcc33();
        // ケアチェックID
        cpnTucHcc33.setCc1Id(gdlId);
        // 計画期間ID
        cpnTucHcc33.setSc1Id(sc1Id);
        // 大分類CD
        cpnTucHcc33.setB1Cd(cpnTucHcc33InfoOutEntity.getB1Cd());
        // 内容
        cpnTucHcc33.setMemoKnj(cpnTucHcc33InfoOutEntity.getMemoKnj());
        // 具体的内容ID
        cpnTucHcc33.setCc32Id(cc32Id);
        // 表示順
        cpnTucHcc33.setSeq(cpnTucHcc33InfoOutEntity.getSeq().intValue());
        // 入力支援（対応するｹｱ）id
        cpnTucHcc33.setCi2Id(cpnTucHcc33InfoOutEntity.getCi2Id());

        cpnTucHcc33Mapper.insertSelective(cpnTucHcc33);
    }

    /**
     * 【ＨＫ＿ケアチェック表（問題点や解決すべき課題）】テーブルを登録する
     *
     * @param inDto GUI00838_［アセスメント複写］ データ保存サービス入力Dto
     * @return 採番した課題立案履歴ＩＤ
     */
    private void insertCpnTucHcc31(DuplicateAssessmentUpdateServiceInDto inDto, Integer gdlId, Integer sc1Id,
            CpnTucHcc31InfoOutEntity cpnTucHcc31InfoOutEntity) throws Exception {
        CpnTucHcc31 cpnTucHcc31 = new CpnTucHcc31();
        // ケアチェックID
        cpnTucHcc31.setCc1Id(gdlId);
        // 計画期間ID
        cpnTucHcc31.setSc1Id(sc1Id);
        // 大分類CD
        cpnTucHcc31.setB1Cd(cpnTucHcc31InfoOutEntity.getB1Cd());
        // 問題点CD
        cpnTucHcc31.setB4Cd(cpnTucHcc31InfoOutEntity.getB4Cd());
        // 有無
        cpnTucHcc31.setF1(cpnTucHcc31InfoOutEntity.getF1().intValue());
        // 立案
        cpnTucHcc31.setF2(cpnTucHcc31InfoOutEntity.getF2().intValue());
        cpnTucHcc31Mapper.insertSelective(cpnTucHcc31);
    }

}
