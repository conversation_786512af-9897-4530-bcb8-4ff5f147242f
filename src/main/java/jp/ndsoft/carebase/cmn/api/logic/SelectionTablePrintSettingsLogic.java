package jp.ndsoft.carebase.cmn.api.logic;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.dto.SelectionHistoryDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMucShokuinKanaKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnMucShokuinKanaKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonSentPrnReqRrkByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonSentPrnReqRrkOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghCpnRaiMonSentPrnReqRrkSelSelectMapper;

/**
 * SelectionTablePrintSettingsLogic 印刷設定
 * 
 * <AUTHOR>
 */
@Component
public class SelectionTablePrintSettingsLogic {

    /** 職員一覧取得 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /** インターライ方式印刷リクエスト履歴取得 */
    @Autowired
    private KghCpnRaiMonSentPrnReqRrkSelSelectMapper kghCpnRaiMonSentPrnReqRrkSelSelectMapper;

    /**
     * API定義書_APINo(885)_印刷設定履歴リスト取得の2. 選定表履歴情報「インターライ方式印刷リクエスト履歴情報取得」を取得
     * 
     * @param svJigyoId 事業者ID
     * @param userId    利用者ID
     * @param kikanFlg  期間管理フラグ
     * 
     * @return 選定表履歴リスト
     */
    public List<SelectionHistoryDto> getSelectionHistoryList(String svJigyoId, String userId, String kikanFlg) {
        List<SelectionHistoryDto> selectionHistoryList = new ArrayList<SelectionHistoryDto>();

        // 2.1.下記のインターライ方式印刷リクエスト履歴取得DAOを利用し、選定表履歴一覧を取得する。
        List<KghCpnRaiMonSentPrnReqRrkOutEntity> kghCpnRaiMonSentPrnReqRrkList = this
                .findKghCpnRaiMonSentPrnReqRrkByCriteria(svJigyoId, userId);

        // 2.2.「2.1.」で取得履歴情報件数>0の場合、下記の職員一覧取得DAOを利用し、職員一覧を取得する。
        if (CollectionUtils.isNotEmpty(kghCpnRaiMonSentPrnReqRrkList)) {
            List<CpnMucShokuinKanaKnjOutEntity> cpnMucShokuinKanaKnjList = this
                    .findCpnMucShokuinKanaKnjByCriteria();

            // 2.3「2.1.」で取得履歴情報件数>0の場合、OUTPUT情報作成

            // 2.3.1.データソート処理
            // ①.期間管理フラグが「1:管理する」の場合
            // ソートキー：
            // "start_ymd desc, end_ymd desc, sc1_id desc, cap_date_ymd desc,rai_id desc"
            if (CommonDtoUtil.checkStringEqual(kikanFlg, CommonConstants.PERIOD_MANAGE_FLG)) {
                kghCpnRaiMonSentPrnReqRrkList.sort(
                        Comparator
                                .comparing(KghCpnRaiMonSentPrnReqRrkOutEntity::getStartYmd,
                                        Comparator.nullsLast(Comparator.reverseOrder()))
                                .thenComparing(KghCpnRaiMonSentPrnReqRrkOutEntity::getEndYmd,
                                        Comparator.nullsLast(Comparator.reverseOrder()))
                                .thenComparing(KghCpnRaiMonSentPrnReqRrkOutEntity::getSc1Id,
                                        Comparator.nullsLast(Comparator.reverseOrder()))
                                .thenComparing(KghCpnRaiMonSentPrnReqRrkOutEntity::getCapDateYmd,
                                        Comparator.nullsLast(Comparator.reverseOrder()))
                                .thenComparing(KghCpnRaiMonSentPrnReqRrkOutEntity::getRaiId,
                                        Comparator.nullsLast(Comparator.reverseOrder())));
            } else {
                // ②.期間管理フラグが「0:管理しない」の場合
                // ソートキー："cap_date_ymd desc, rai_id desc"
                kghCpnRaiMonSentPrnReqRrkList.sort(
                        Comparator
                                .comparing(KghCpnRaiMonSentPrnReqRrkOutEntity::getCapDateYmd,
                                        Comparator.nullsLast(Comparator.reverseOrder()))
                                .thenComparing(KghCpnRaiMonSentPrnReqRrkOutEntity::getRaiId,
                                        Comparator.nullsLast(Comparator.reverseOrder())));

            }

            // 2.3.2.上記「2.3.1」で取得情報件数分、OUTPUT情報を作成する
            for (KghCpnRaiMonSentPrnReqRrkOutEntity kghCpnRaiMonSentPrnReqRrk : kghCpnRaiMonSentPrnReqRrkList) {

                // ①.選定表履歴リスト.履歴リスト.作成者の設定
                // 選定者が上記2.2.OUTPUT情報.職員ID一致の場合、作成者に職員名（姓）+ " "+職員名（名）を取得する
                String shokuinKnj = CommonConstants.BLANK_STRING;

                for (CpnMucShokuinKanaKnjOutEntity cpnMucShokuinKanaKnj : cpnMucShokuinKanaKnjList) {
                    if (kghCpnRaiMonSentPrnReqRrk.getCapShokuId() == cpnMucShokuinKanaKnj
                            .getChkShokuId()) {
                        shokuinKnj = cpnMucShokuinKanaKnj.getShokuin1Knj()
                                + CommonConstants.BLANK_SPACE
                                + cpnMucShokuinKanaKnj.getShokuin2Knj();
                        break;
                    }
                }
                // 「2.3.1.」でソートした情報.選定者が上記2.2.OUTPUT情報.職員ID一致しない場合、作成者に選定者を取得する
                if (StringUtils.isEmpty(shokuinKnj)) {
                    shokuinKnj = CommonDtoUtil.objValToString(kghCpnRaiMonSentPrnReqRrk.getCapShokuId());
                }

                // 選定表履歴を設定する。
                SelectionHistoryDto selectionHistory = this.setSelectionHistory(kghCpnRaiMonSentPrnReqRrk,
                        shokuinKnj);

                selectionHistoryList.add(selectionHistory);

            }
        }

        return selectionHistoryList;

    }

    /**
     * 選定表履歴一覧を取得する。
     * 
     * @param svJigyoId 事業者ID
     * @param userId    利用者ID
     * 
     * @return 選定表履歴一覧
     */
    private List<KghCpnRaiMonSentPrnReqRrkOutEntity> findKghCpnRaiMonSentPrnReqRrkByCriteria(
            String svJigyoId,
            String userId) {
        KghCpnRaiMonSentPrnReqRrkByCriteriaInEntity kghCpnRaiMonSentPrnReqRrkByCriteriaInEntity = new KghCpnRaiMonSentPrnReqRrkByCriteriaInEntity();
        // 事業者ID
        kghCpnRaiMonSentPrnReqRrkByCriteriaInEntity.setAnSvJigyoId(CommonDtoUtil.strValToInt(svJigyoId));
        // 利用者ID
        kghCpnRaiMonSentPrnReqRrkByCriteriaInEntity.setAnUserid(CommonDtoUtil.strValToInt(userId));

        return this.kghCpnRaiMonSentPrnReqRrkSelSelectMapper
                .findKghCpnRaiMonSentPrnReqRrkByCriteria(kghCpnRaiMonSentPrnReqRrkByCriteriaInEntity);

    }

    /**
     * 職員一覧を取得する。
     * 
     * @return 職員一覧
     */
    private List<CpnMucShokuinKanaKnjOutEntity> findCpnMucShokuinKanaKnjByCriteria() {
        CpnMucShokuinKanaKnjByCriteriaInEntity cpnMucShokuinKanaKnjByCriteriaInEntity = new CpnMucShokuinKanaKnjByCriteriaInEntity();
        return this.comMscShokuinSelectMapper
                .findCpnMucShokuinKanaKnjByCriteria(cpnMucShokuinKanaKnjByCriteriaInEntity);

    }

    /**
     * 選定表履歴を設定する。
     * 
     * @param kghCpnRaiMonSentPrnReqRrk 選定表履歴
     * @param shokuinKnj                作成者
     * 
     * @return 選定表履歴
     */
    private SelectionHistoryDto setSelectionHistory(KghCpnRaiMonSentPrnReqRrkOutEntity kghCpnRaiMonSentPrnReqRrk,
            String shokuinKnj) {
        SelectionHistoryDto selectionHistory = new SelectionHistoryDto();
        // 期間ID
        selectionHistory.setSc1Id(CommonDtoUtil.objValToString(kghCpnRaiMonSentPrnReqRrk.getSc1Id()));
        // 開始日
        selectionHistory.setStartYmd(kghCpnRaiMonSentPrnReqRrk.getStartYmd());
        // 終了日
        selectionHistory.setEndYmd(kghCpnRaiMonSentPrnReqRrk.getEndYmd());

        // 選択区分
        selectionHistory.setSel(CommonDtoUtil.objValToString(kghCpnRaiMonSentPrnReqRrk.getSel()));
        // アセスメントID
        selectionHistory.setRaiId(CommonDtoUtil.objValToString(kghCpnRaiMonSentPrnReqRrk.getRaiId()));
        // 利用者ID
        selectionHistory.setUserId(CommonDtoUtil.objValToString(kghCpnRaiMonSentPrnReqRrk.getUserid()));
        // 選定アセスメント種別
        selectionHistory.setCapType(CommonDtoUtil.objValToString(kghCpnRaiMonSentPrnReqRrk.getCapType()));
        // 選定日
        selectionHistory.setCapDateYmd(CommonDtoUtil.objValToString(kghCpnRaiMonSentPrnReqRrk.getCapDateYmd()));
        // 選定者
        selectionHistory.setCapShokuId(CommonDtoUtil.objValToString(kghCpnRaiMonSentPrnReqRrk.getCapShokuId()));
        // 作成者
        selectionHistory.setShokuinKnj(shokuinKnj);

        return selectionHistory;

    }

}
