package jp.ndsoft.carebase.cmn.api.report.service;

import java.io.File;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.report.dto.ShisetsuServiceReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ShisetsuServiceReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.logic.ShisetsuServiceReportLogic;
import jp.ndsoft.carebase.cmn.api.report.model.ShisetsuServiceReportParameterModel;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * 
 * U0082S_施設サービス計画書（２）
 * 
 * @since 2025.07.22
 * <AUTHOR> 李晨昊
 */
@Service("ShisetsuServiceReport")
public class ShisetsuServiceReportService
        extends PdfReportServiceImpl<ShisetsuServiceReportParameterModel, ShisetsuServiceReportServiceOutDto> {

    /**
     * ロガー.
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 施設サービス計画書（２）帳票出力ロジック */
    @Autowired
    private ShisetsuServiceReportLogic shisetsuServiceReportLogic;

    /**
     * 帳票パラメータ取得
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(ShisetsuServiceReportParameterModel model,
            ShisetsuServiceReportServiceOutDto outDto) throws Exception {
        LOG.info(Constants.START);

        // 帳票用データ詳細
        ShisetsuServiceReportServiceInDto infoInDto = shisetsuServiceReportLogic
                .getShisetsuServiceReportParameters(model, outDto, getFwProps());

        // ノート情報格納配列
        List<ShisetsuServiceReportServiceInDto> shisetsuInfoList = new ArrayList<ShisetsuServiceReportServiceInDto>();
        shisetsuInfoList.add(infoInDto);

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(shisetsuInfoList);
        infoInDto.setDataSource(dataSource);

        LOG.info(Constants.END);
        return infoInDto;
    }

    /**
     * 帳票出力
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final ShisetsuServiceReportParameterModel model,
            final ShisetsuServiceReportServiceOutDto outDto) throws Exception {
        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final ShisetsuServiceReportServiceInDto reportParameter = (ShisetsuServiceReportServiceInDto) getReportParameters(
                model, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // コンパイル
        final JasperReport jasperFile = shisetsuServiceReportLogic.getJasperReport(getFwProps(), reportParameter,
                model);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile, reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(model, jasperPrint);

        super.setFilepath(model, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }
}
