package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jp.ndsoft.carebase.cmn.api.service.dto.Bunrui3SetDto;
import jp.ndsoft.carebase.cmn.api.service.dto.DailyTaskTableMasterUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.DailyTaskTableMasterUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDaoUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkSsmMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsm;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsmCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * GUI00987_日課表マスタ情報保存サービス.
 * 
 * @since 2025.05.12
 * <AUTHOR>
 */
@Service
public class DailyTaskTableMasterUpdateServiceImpl extends
        UpdateServiceImpl<DailyTaskTableMasterUpdateServiceInDto, DailyTaskTableMasterUpdateServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 初期設定マスタ情報取得DAO */
    @Autowired
    private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;

    @Autowired
    private KghMocKrkSsmMapper kghMocKrkSsmMapper;

    /** 小数点付 固定:0 */
    private static final Double DOUBLE_VALUE_ZERO = (double) 0;

    /**
     * 日課表マスタ情報保存
     * 
     * @param inDto 日課表マスタの保存入力DTO.
     * @return 日課表マスタ保存出力 DTO
     * @throws Exception Exception
     */
    @Override
    protected DailyTaskTableMasterUpdateServiceOutDto mainProcess(
            DailyTaskTableMasterUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // 更新件数
        int count = 0;
        DailyTaskTableMasterUpdateServiceOutDto outDto = new DailyTaskTableMasterUpdateServiceOutDto();

        // DAOパラメータを作成
        KrkSsmInfoByCriteriaInEntity krkSsmInfoByCriteriaInEntity = new KrkSsmInfoByCriteriaInEntity();
        // リクエストパラメータ.施設ID
        krkSsmInfoByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // リクエストパラメータ.事業所ID
        krkSsmInfoByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // リクエストパラメータ.分類１
        krkSsmInfoByCriteriaInEntity.setBunrui1Id(CommonDtoUtil.strValToInt(inDto.getBunrui1Id()));
        // リクエストパラメータ.分類2
        krkSsmInfoByCriteriaInEntity.setBunrui2Id(CommonDtoUtil.strValToInt(inDto.getBunrui2Id()));
        // DAOを実行
        List<KrkSsmInfoOutEntity> krkSsmInfoList = this.kghMocKrkSsmSelectMapper
                .findKrkSsmInfoByCriteria(krkSsmInfoByCriteriaInEntity);
        for (Bunrui3SetDto bunrui3 : inDto.getBunrui3SetList()) {
            Optional<KrkSsmInfoOutEntity> result = krkSsmInfoList.stream()
                    .filter(p -> bunrui3.getBunrui3Id().equals(p.getBunrui3Id()
                            .toString()))
                    .findFirst();
            if (result.isPresent()) {
                if (!StringUtils.hasText(bunrui3.getModifiedCnt())) {
                    throw new ExclusiveException();
                }
                // 更新条件
                KghMocKrkSsmCriteria kghMocKrkSsmCriteria = new KghMocKrkSsmCriteria();
                // 分類１＝リクエストパラメータ.分類１
                kghMocKrkSsmCriteria.createCriteria().andBunrui1IdEqualTo(result.get().getBunrui1Id())
                        // 分類2＝リクエストパラメータ.分類2
                        .andBunrui2IdEqualTo(result.get().getBunrui2Id())
                        // 分類３ = リクエストパラメータ.初期設定マスタ情報リスト.分類3
                        .andBunrui3IdEqualTo(
                                result.get().getBunrui3Id())
                        // 施設ID＝リクエストパラメータ.施設ID
                        .andShisetuIdEqualTo(result.get().getShisetuId())
                        // 事業者ID＝リクエストパラメータ.事業者ID
                        .andSvJigyoIdEqualTo(
                                result.get().getSvJigyoId());
                // 更新回数 ＝ リクエストパラメータ.初期設定マスタ情報リスト.更新回数
                // .andModifiedCntEqualTo(CommonDtoUtil
                // .strValToBigInteger(bunrui3.getModifiedCnt()));
                KghMocKrkSsm kghMocKrkSsm = new KghMocKrkSsm();
                // リクエストパラメータ.初期設定マスタ情報リスト.整数
                kghMocKrkSsm.setIntValue(CommonDtoUtil.strValToInt(bunrui3.getBunrui3Value()));
                CommonDaoUtil.setUpdateCommonColumns(kghMocKrkSsm,
                        CommonDtoUtil.strValToBigInteger(bunrui3.getModifiedCnt()));
                // 00-00 初期設定マスタ(kgh_moc_krk_ssm)
                count = kghMocKrkSsmMapper.updateByCriteriaSelective(kghMocKrkSsm,
                        kghMocKrkSsmCriteria);
                if (count <= 0) {
                    throw new ExclusiveException();
                }
            } else {
                KghMocKrkSsm kghMocKrkSsm = new KghMocKrkSsm();
                // リクエストパラメータ.分類１
                kghMocKrkSsm.setBunrui1Id(CommonDtoUtil.strValToInt(inDto.getBunrui1Id()));
                // リクエストパラメータ.分類2
                kghMocKrkSsm.setBunrui2Id(CommonDtoUtil.strValToInt(inDto.getBunrui2Id()));
                // リクエストパラメータ.施設ID
                kghMocKrkSsm.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                // リクエストパラメータ.事業所ID
                kghMocKrkSsm.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                // 該当行.分類3
                kghMocKrkSsm.setBunrui3Id(CommonDtoUtil.strValToInt(bunrui3.getBunrui3Id()));
                // 該当行.分類3の設定値
                kghMocKrkSsm.setIntValue(CommonDtoUtil.strValToInt(bunrui3.getBunrui3Value()));
                // 固定:0
                kghMocKrkSsm.setDoubleValue(DOUBLE_VALUE_ZERO);
                CommonDaoUtil.setInsertCommonColumns(kghMocKrkSsm);
                count = kghMocKrkSsmMapper.insertSelective(kghMocKrkSsm);
            }
        }
        LOG.info(Constants.END);
        return outDto;
    }

}
