package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckItemInfo;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.06.18
 * <AUTHOR>
 * @implNote GUI00892_チェック項目画面 情報取得サービス入力Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CheckItemsChangedSelectServiceOutDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** チェック項目詳細情報 */
    private List<Gui00892CheckItemInfo> checkItemInfo;

}
