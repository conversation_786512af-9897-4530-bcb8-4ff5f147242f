package jp.ndsoft.carebase.cmn.api.report.service;

import java.io.File;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.report.dto.ServiceRiteiReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.dto.ServiceRiteiReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.TeikyohyoReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.logic.ServiceRiteiReportLogic;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * 
 * V00231_サービス利用票
 * 
 * <AUTHOR>
 */
@Service("ServiceRiteiReport")
public class ServiceRiteiReportService
        extends PdfReportServiceImpl<ServiceRiteiReportParameterModel, ServiceRiteiReportServiceOutDto> {

    /**
     * ロガー.
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private ServiceRiteiReportLogic serviceRiteiReportLogic;

    /**
     * 帳票パラメータ取得
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(ServiceRiteiReportParameterModel model, ServiceRiteiReportServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);

        // 帳票用データ詳細
        TeikyohyoReportServiceInDto inDto = serviceRiteiReportLogic.getV00231ReportParameters(model);

        // ノート情報格納配列
        List<TeikyohyoReportServiceInDto> teikyohyoReportServiceInDtoList = new ArrayList<>();
        teikyohyoReportServiceInDtoList.add(inDto);

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(teikyohyoReportServiceInDtoList);
        inDto.setDataSource(dataSource);

        LOG.info(Constants.END);
        return inDto;
    }

    /**
     * 帳票出力
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final ServiceRiteiReportParameterModel model,
            final ServiceRiteiReportServiceOutDto outDto) throws Exception {

        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final TeikyohyoReportServiceInDto reportParameter = (TeikyohyoReportServiceInDto) getReportParameters(model,
                outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // コンパイル
        final JasperReport jasperFile = serviceRiteiReportLogic.getJasperReport(getFwProps(), reportParameter, model);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile, reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(model, jasperPrint);

        super.setFilepath(model, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }
}
