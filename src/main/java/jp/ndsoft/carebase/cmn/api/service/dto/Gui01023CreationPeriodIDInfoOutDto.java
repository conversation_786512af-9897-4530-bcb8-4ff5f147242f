package jp.ndsoft.carebase.cmn.api.service.dto;

import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * @since 2025.04.24
 * <AUTHOR> Bui The Khang
 * @implNote GUI01023_課題整理総括取込画面
 */
@Getter
@Setter
public class Gui01023CreationPeriodIDInfoOutDto extends IDtoImpl {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;
    /** 期間ID */
    private String sc1Id;

    /** 法人ID */
    private String houjinId;

    /** 施設ID */
    private String shisetuId;

    /** 事業者ID */
    private String svJigyoId;

    /** 利用者ID */
    private String userId;

    /** 種別ID */
    private String syubetsuId;

    /** 開始日 */
    private String startYmd;

    /** 終了日 */
    private String endYmd;

    // 期間内履歴数
    private String recordsCnt;

    /** 更新回数 */
    // private String modifiedCnt;
}
