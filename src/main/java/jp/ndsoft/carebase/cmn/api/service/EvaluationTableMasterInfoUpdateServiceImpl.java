package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01233EvaluationTableMasterSettingsInfoUpdate;
import jp.ndsoft.carebase.cmn.api.service.dto.EvaluationTableMasterInfoUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.EvaluationTableMasterInfoUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkSsmMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsm;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsmCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.smh.framework.util.AppUtil;

/**
 * @since 2025.05.21
 * <AUTHOR>
 * @description Gui01233_評価表マスタの情報を保存サービス.
 */
@Service
public class EvaluationTableMasterInfoUpdateServiceImpl
      extends
      UpdateServiceImpl<EvaluationTableMasterInfoUpdateServiceInDto, EvaluationTableMasterInfoUpdateServiceOutDto> {
   private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

   /** 評価表マスタ情報取得DAO */
   @Autowired
   private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;
   /** 評価表マスタ情報更新DAO */
   @Autowired
   private KghMocKrkSsmMapper kghMocKrkSsmMapper;

   /**
    * 「評価表マスタ」画面の情報を保存する。
    * 
    * @param inDto 初期設定マスタ情報保存入力DTO
    * @return 初期設定マスタ取込初期情報出力DTO
    */
   @Override
   protected EvaluationTableMasterInfoUpdateServiceOutDto mainProcess(
         final EvaluationTableMasterInfoUpdateServiceInDto inDto) throws Exception {
      LOG.info(Constants.START);

      // 更新件数
      Integer count = 0;
      Boolean isUpdate = false;
      /*
       * ===============1.単項目チェック以外の入力チェック===============
       * 
       */
      // 特になし
      /*
       * ===============2. 評価表マスタ情報取得。===============
       * 
       */
      for (Gui01233EvaluationTableMasterSettingsInfoUpdate item : inDto.getEvaluationTableMasterSettingsInfoList()) {
         // DAOパラメータを作成
         KrkSsmInfoByCriteriaInEntity krkSsmInfoByCriteriaInEntity = new KrkSsmInfoByCriteriaInEntity();
         // 施設ID
         krkSsmInfoByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(item.getShisetuId()));
         // 事業者ID
         krkSsmInfoByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(item.getSvJigyoId()));
         // 分類1
         krkSsmInfoByCriteriaInEntity.setBunrui1Id(CommonDtoUtil.strValToInt(item.getBunrui1Id()));
         // 分類2
         krkSsmInfoByCriteriaInEntity.setBunrui2Id(CommonDtoUtil.strValToInt(item.getBunrui2Id()));
         // DAOを実行 (DAO定義書)
         List<KrkSsmInfoOutEntity> krkSsmInfoOutEntities = this.kghMocKrkSsmSelectMapper
               .findKrkSsmInfoByCriteria(krkSsmInfoByCriteriaInEntity);
         /*
          * ===============3.評価表マスタの保存処理。===============
          * 
          */
         // 3.1.リクエストパラメータ.評価表マスタ設定情報リストを繰り返し、下記の保存処理を行う
         for (KrkSsmInfoOutEntity kr : krkSsmInfoOutEntities) {
            // 3.1.1.リクエストパラメータ.評価表マスタ設定情報リスト.分類3が上記「2.
            // 評価表マスタ情報取得」処理で取得した検索データに存在する場合、下記更新処理を行う
            if (kr.getBunrui3Id() == CommonDtoUtil.strValToInt(item.getBunrui3Id())) {
               KghMocKrkSsm kghMocKrkSsm = new KghMocKrkSsm();
               // 整数
               kghMocKrkSsm.setIntValue(CommonDtoUtil.strValToInt(item.getIntValue()));
               // タイムスタンプ
               kghMocKrkSsm.setTimeStmp(AppUtil.getSystemTimeStamp());

               KghMocKrkSsmCriteria criteria = new KghMocKrkSsmCriteria();
               criteria.createCriteria()
                    // 施設ID
                    .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(item.getShisetuId()))
                    // 事業者ID
                    .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(item.getSvJigyoId()))
                    // 分類１
                    .andBunrui1IdEqualTo(CommonDtoUtil.strValToInt(item.getBunrui1Id()))
                    // 分類2
                    .andBunrui2IdEqualTo(CommonDtoUtil.strValToInt(item.getBunrui2Id()))
                    // 分類3
                    .andBunrui3IdEqualTo(CommonDtoUtil.strValToInt(item.getBunrui3Id()));
               // DAOを実行
               count = this.kghMocKrkSsmMapper.updateByCriteriaSelective(kghMocKrkSsm, criteria);
               // 更新失敗の場合
               if (count <= 0) {
                  LOG.info(Constants.END);
                  throw new ExclusiveException();
               }
               isUpdate = true;
            }
         }
         // 3.1.2.上記以外の場合、下記登録処理を行う
         if (!isUpdate) {
            KghMocKrkSsm kghMocKrkSsm = new KghMocKrkSsm();
            // 施設ID
            kghMocKrkSsm.setShisetuId(CommonDtoUtil.strValToInt(item.getShisetuId()));
            // 事業者ID
            kghMocKrkSsm.setSvJigyoId(CommonDtoUtil.strValToInt(item.getSvJigyoId()));
            // 分類１
            kghMocKrkSsm.setBunrui1Id(CommonDtoUtil.strValToInt(item.getBunrui1Id()));
            // 分類２
            kghMocKrkSsm.setBunrui2Id(CommonDtoUtil.strValToInt(item.getBunrui2Id()));
            // 分類３
            kghMocKrkSsm.setBunrui3Id(CommonDtoUtil.strValToInt(item.getBunrui3Id()));
            // 整数
            kghMocKrkSsm.setIntValue(CommonDtoUtil.strValToInt(item.getIntValue()));
            // 小数点付
            kghMocKrkSsm.setDoubleValue(0.0);
            // 文字列1
            kghMocKrkSsm.setText1Knj(null);
            // タイムスタンプ
            kghMocKrkSsm.setTimeStmp(AppUtil.getSystemTimeStamp());
            // DAOを実行
            count = this.kghMocKrkSsmMapper.insertSelective(kghMocKrkSsm);
         }
      }
      EvaluationTableMasterInfoUpdateServiceOutDto outDto = new EvaluationTableMasterInfoUpdateServiceOutDto();
      // ステータス
      // 更新成功の場合、「1」を設定する
      // 更新失敗の場合、「0」を設定する
      outDto.setResult(count > 0 ? "1" : "0");
      LOG.info(Constants.END);
      return outDto;
   }
}
