package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GUI00892_チェック項目画面
 * 
 * @description
 *              計画期間情報
 *              計画期間情報エンティティ
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Gui00892KknInfo implements Serializable, IEntity {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 期間ID */
    @NotEmpty
    private String sc1Id;

    /** 開始日 */
    @NotEmpty
    private String startYmd;

    /** 終了日 */
    private String endYmd;

    /** 期間番号 */
    private String kikanNo;

    /** 期間総件数 */
    private String kikanTotalCnt;

}
