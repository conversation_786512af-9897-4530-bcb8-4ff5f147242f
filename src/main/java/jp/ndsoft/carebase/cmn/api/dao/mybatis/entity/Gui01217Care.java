package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.carebase.framework.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GUI01217_］画面にケース一覧の情報を保存
 * 
 * @description
 *              ケース情報を保存
 *              ケース情報のエンティティ
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Gui01217Care implements Serializable, IEntity {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 備考区分 */
    private String bikoKbn;

    /** 記録日 */
    @NotEmpty
    private String yymmYmd;

    /** レコード番号 */
    private String recNo;

    /** 開始時間 */
    @NotEmpty
    private String timeHh;

    /** 開始分 */
    @NotEmpty
    private String timeMm;

    /** ケース種別 */
    private String caseCd;

    /** ケース色 */
    private String color;

    /** ケース事項 */
    private String caseKnj;

    /** 記入者 */
    @NotEmpty
    private String staffid;

    /** 記入者名 */
    private String staffName;

    /** ｹｰｽ転記フラグ */
    private String caseFlg;

    /** 申し送りフラグ */
    private String moushiokuriFlg;

    /** 結果元履歴番号 */
    private String baseRecNo;

    /** システム別フラグ */
    private String systemFlg;

    /** 指示フラグ */
    private String shijiFlg;

    /** 共有区分 */
    private String kyouyuBikoKbn;

    /** 褥瘡フラグ */
    private String jyoFlg;

    /** ユニークID */
    private String uniqueId;

    /** DmyW01YymmYmd */
    private String dmyW01YymmYmd;

    /** ComputeColor */
    private String computeColor;

    /** 計画書(2)ID */
    private String ks21Id;

    /** 計画書(2)詳細ID */
    private String ks22Id;

    /** 実施モニタリング詳細ID */
    @NotEmpty
    private String kjisshi2Id;

    /** 事業者ID */
    private String svJigyoId;

    /** DmyMFlg */
    private String dmyMFlg;

    /** 実施状況確認詳細ID */
    private String r4sKjisshi2Id;

    /** 結果元ユニークID */
    private String baseUniqueId;

    /** 完了フラグ */
    private String kanryouFlg;

    /** 画像ID */
    private String computeGazouId;

    /** プロブレムID */
    private String problemId;

    /** ComputeHensyuSetteiKbn */
    private String computeHensyuSetteiKbn;

    /** 画像ステータス */
    private String picStatus;

    /** 更新区分 */
    private String updateKbn;

}
