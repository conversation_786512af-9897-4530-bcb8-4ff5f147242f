package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks51;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks52;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks54;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks55;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks56;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks57;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks58;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038Cks59;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038DeleteKasanId;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038DeleteLinkedItemId;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038DeleteTanId;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01038DeleteTukihiId;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekPlanDetailsInfoUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekPlanDetailsInfoUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCks51Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCks52Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCks54Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCks55Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCks56Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCks57Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCks58Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucCks59Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks51;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks51Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks52;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks52Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks54;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks54Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks55;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks55Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks56;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks56Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks57;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks57Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks58;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks58Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks59;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucCks59Criteria;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

/**
 * GUI01038_週間計画詳細情報保存 APINo(382)_週間計画詳細情報保存
 * 
 * <AUTHOR>
 */
@Service
public class WeekPlanDetailsInfoUpdateServiceImpl
        extends UpdateServiceImpl<WeekPlanDetailsInfoUpdateServiceInDto, WeekPlanDetailsInfoUpdateServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 28-25 週間計画表（ヘッダ） */
    @Autowired
    private CpnTucCks51Mapper cpnTucCks51Mapper;

    /** 28-26 週間計画表（詳細データ） */
    @Autowired
    private CpnTucCks52Mapper cpnTucCks52Mapper;

    /** 28-28 週間計画表（加算データ） */
    @Autowired
    private CpnTucCks55Mapper cpnTucCks55Mapper;

    /** 28-29 週間計画表（担当者） */
    @Autowired
    private CpnTucCks56Mapper cpnTucCks56Mapper;

    /** 28-67 週間計画（月日指定） */
    @Autowired
    private CpnTucCks57Mapper cpnTucCks57Mapper;

    /** 28-73 週間計画表（隔週） */
    @Autowired
    private CpnTucCks58Mapper cpnTucCks58Mapper;

    /** 28-67 週間計画（月日指定） */
    @Autowired
    private CpnTucCks54Mapper cpnTucCks54Mapper;

    /** 28-74 週間計画表（連動項目） */
    @Autowired
    private CpnTucCks59Mapper cpnTucCks59Mapper;

    /**
     * 週間計画詳細情報保存
     * 
     * @param inDto 週間計画詳細情報保存の入力Dto
     * @return outDto 週間計画詳細情報保存の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected WeekPlanDetailsInfoUpdateServiceOutDto mainProcess(WeekPlanDetailsInfoUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // 週間計画詳細情報保存の出力Dto
        WeekPlanDetailsInfoUpdateServiceOutDto outDto = new WeekPlanDetailsInfoUpdateServiceOutDto();
        // 1.単項目チェック以外の入力チェック
        // 変数.週間計画ID
        String ks51Id;
        // 2.週間計画履歴の保存処理を行う。
        // 2.1 リクエストパラメータ.履歴フラグが"2"（新規）の場合、
        if (CommonConstants.RIREKI_FLG_2.equals(inDto.getRirekiFlg())) {
            // 【週間計画履歴】情報を登録する
            ks51Id = cks51DataInsProc(inDto);
        } else if (CommonConstants.RIREKI_FLG_1.equals(inDto.getRirekiFlg())) {
            // 2.2 リクエストパラメータ.履歴フラグが"1"（削除）の場合、
            // 【週間計画履歴】情報を削除(論理削除)する
            ks51Id = cks51DataDelProc(inDto);
        } else {
            // 2.3 上記以外の場合、【週間計画履歴】情報を更新する
            ks51Id = cks51DataUpdProc(inDto);
        }
        // 3.週間計画詳細リストを繰り返し、週間計画詳細の保存処理を行う
        for (Gui01038Cks52 gui01038Cks52 : inDto.getCks52List()) {
            // 3.1 リクエストパラメータ.更新区分が"C"（新規）の場合、
            if (CommonDtoUtil.isCreate(gui01038Cks52)) {
                cks52DataInsProc(inDto, gui01038Cks52, ks51Id);
            }
            // 3.2 リクエストパラメータ.更新区分が"U":更新の場合、
            if (CommonDtoUtil.isUpdate(gui01038Cks52)) {
                cks52DataUpdProc(inDto, gui01038Cks52, ks51Id);
            }
            // 3.3 リクエストパラメータ.更新区分が"D"（削除）の場合、
            if (CommonDtoUtil.isDelete(gui01038Cks52)) {
                cks52DataDelProc(gui01038Cks52, ks51Id);
            }
        }
        // 4. 週間計画日常情報保存を行う
        cks54DataDelInsProc(inDto, ks51Id);
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 4. 週間計画日常情報保存を行う
     * 
     * @param inDto  週間計画詳細情報保存の入力Dto
     * @param ks51Id 週間計画ID
     * @throws Exception
     */
    private void cks54DataDelInsProc(WeekPlanDetailsInfoUpdateServiceInDto inDto, String ks51Id) throws Exception {
        // 4.1 28-27 週間計画表（日常生活データ）を削除する
        for (Gui01038Cks54 gui01038Cks54 : inDto.getCks54List()) {
            if (ks51Id != null && gui01038Cks54.getKs54Id() != null) {
                // 削除条件キー
                CpnTucCks54Criteria whereItem = new CpnTucCks54Criteria();
                whereItem.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                        .andKs54IdEqualTo(CommonDtoUtil.strValToInt(gui01038Cks54.getKs54Id()));
                // 削除（論理）時の共通カラム値設定処理
                int delCnt = cpnTucCks54Mapper.deleteByCriteria(whereItem);
                if (delCnt <= 0) {
                    throw new ExclusiveException();
                }
            }
        }
        // 4.2 リクエストパラメータ.履歴フラグ<> "1"（削除以外） 且つ 週間計画日常リスト.件数 ＞ 0 の場合、
        if (!CommonConstants.RIREKI_FLG_1.equals(inDto.getRirekiFlg())
                && CollectionUtils.size(inDto.getCks54List()) > 0) {
            // 28-27 週間計画表（日常生活データ）を登録する
            for (Gui01038Cks54 gui01038Cks54 : inDto.getCks54List()) {
                CpnTucCks54 insItem = new CpnTucCks54();
                // 週間計画ID
                insItem.setKs51Id(CommonDtoUtil.strValToInt(ks51Id));
                // 連番
                insItem.setSeq(CommonDtoUtil.strValToInt(gui01038Cks54.getSeq()));
                // 主な日常生活上の活動
                insItem.setNichijoKnj(gui01038Cks54.getNichijoKnj());
                cpnTucCks54Mapper.insertSelective(insItem);
            }
        }
    }

    /**
     * 3.3 リクエストパラメータ.更新区分が"D"（削除）の場合、
     * 
     * @param gui01038Cks52 週間計画詳細
     * @param ks51Id        週間計画ID
     * @throws Exception
     */
    private void cks52DataDelProc(Gui01038Cks52 gui01038Cks52, String ks51Id) throws Exception {
        // ・【変数】.詳細データID＝リクエストパラメータ.週間計画詳細リスト.詳細データID
        String ks52Id = CommonDtoUtil.objValToString(gui01038Cks52.getKs52Id());
        // 3.3.1 週間計画詳細情報を削除(論理削除)する
        // 削除条件キー
        CpnTucCks52Criteria whereItem = new CpnTucCks52Criteria();
        whereItem.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id));
        int delCnt = cpnTucCks52Mapper.deleteByCriteria(whereItem);
        if (delCnt <= 0) {
            throw new ExclusiveException();
        }
        // 3.3.2 詳細情報内のサブリストの削除を行う
        // 3.3.2.1 28-28 週間計画表（加算データ）を削除(論理削除)する
        for (Gui01038Cks55 gui01038Cks55 : gui01038Cks52.getCks55List()) {
            // 削除条件キー
            CpnTucCks55Criteria where55Item = new CpnTucCks55Criteria();
            where55Item.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                    .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id))
                    .andKs55IdEqualTo(CommonDtoUtil.strValToInt(gui01038Cks55.getKs55Id()));
            int del55Cnt = cpnTucCks55Mapper.deleteByCriteria(where55Item);
            if (del55Cnt <= 0) {
                throw new ExclusiveException();
            }
        }
        // 3.3.2.2 28-29 週間計画表（担当者）を削除(論理削除)する
        for (Gui01038Cks56 gui01038Cks56 : gui01038Cks52.getCks56List()) {
            // 削除条件キー
            CpnTucCks56Criteria where56Item = new CpnTucCks56Criteria();
            where56Item.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                    .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id))
                    .andKs56IdEqualTo(CommonDtoUtil.strValToInt(gui01038Cks56.getKs56Id()));
            int del56Cnt = cpnTucCks56Mapper.deleteByCriteria(where56Item);
            if (del56Cnt <= 0) {
                throw new ExclusiveException();
            }
        }
        // 3.3.2.3 28-73 週間計画表（隔週）を削除(論理削除)する
        for (Gui01038Cks58 gui01038Cks58 : gui01038Cks52.getCks58List()) {
            // 削除条件キー
            CpnTucCks58Criteria where58Item = new CpnTucCks58Criteria();
            where58Item.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                    .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id))
                    .andKs58IdEqualTo(CommonDtoUtil.strValToInt(gui01038Cks58.getKs58Id()));
            int del58Cnt = cpnTucCks58Mapper.deleteByCriteria(where58Item);
            if (del58Cnt <= 0) {
                throw new ExclusiveException();
            }
        }
        // 3.3.2.4 28-67 週間計画（月日指定）を削除(論理削除)する
        for (Gui01038Cks57 gui01038Cks57 : gui01038Cks52.getCks57List()) {
            // 削除条件キー
            CpnTucCks57Criteria where57Item = new CpnTucCks57Criteria();
            where57Item.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                    .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id))
                    .andKs57IdEqualTo(CommonDtoUtil.strValToInt(gui01038Cks57.getKs57Id()));
            int del57Cnt = cpnTucCks57Mapper.deleteByCriteria(where57Item);
            if (del57Cnt <= 0) {
                throw new ExclusiveException();
            }
        }
    }

    /**
     * 3.2 リクエストパラメータ.更新区分が"U":更新の場合、
     * 
     * @param inDto         週間計画詳細情報保存の入力Dto
     * @param gui01038Cks52 週間計画詳細
     * @param ks51Id        週間計画ID
     * @throws Exception
     */
    private void cks52DataUpdProc(WeekPlanDetailsInfoUpdateServiceInDto inDto, Gui01038Cks52 gui01038Cks52,
            String ks51Id) throws Exception {
        // ・【変数】.詳細データID＝リクエストパラメータ.週間計画詳細リスト.詳細データID
        String ks52Id = CommonDtoUtil.objValToString(gui01038Cks52.getKs52Id());

        CpnTucCks52 updItem = new CpnTucCks52();
        // 曜日
        updItem.setYoubi(gui01038Cks52.getYoubi());
        // 開始時間
        updItem.setKaishiJikan(gui01038Cks52.getKaishiJikan());
        // 終了時間
        updItem.setShuuryouJikan(gui01038Cks52.getShuuryouJikan());
        // 内容
        updItem.setNaiyouKnj(gui01038Cks52.getNaiyouKnj());
        // 文字サイズ
        updItem.setFontSize(CommonDtoUtil.strValToInt(gui01038Cks52.getFontSize()));
        // 表示モード
        updItem.setDispMode(CommonDtoUtil.strValToInt(gui01038Cks52.getDispMode()));
        // 文字位置
        updItem.setAlignment(CommonDtoUtil.strValToInt(gui01038Cks52.getAlignment()));
        // サービス種類
        updItem.setSvShuruiCd(gui01038Cks52.getSvShuruiCd());
        // サービス項目（台帳）
        updItem.setSvItemCd(CommonDtoUtil.strValToInt(gui01038Cks52.getSvItemCd()));
        // サービス事業者ID
        updItem.setSvJigyoId(CommonDtoUtil.strValToInt(gui01038Cks52.getSvJigyoId()));
        // 文字カラー
        updItem.setFontColor(CommonDtoUtil.strValToInt(gui01038Cks52.getFontColor()));
        // 背景カラー
        updItem.setBackColor(CommonDtoUtil.strValToInt(gui01038Cks52.getBackColor()));
        // 時間表示区分
        updItem.setTimeKbn(CommonDtoUtil.strValToInt(gui01038Cks52.getTimeKbn()));
        // 週単位以外のｻｰﾋﾞｽ区分
        updItem.setIgaiKbn(CommonDtoUtil.strValToInt(gui01038Cks52.getIgaiKbn()));
        // 週単位以外のｻｰﾋﾞｽ（日付指定）
        updItem.setIgaiDate(gui01038Cks52.getIgaiDate());
        // 週単位以外のｻｰﾋﾞｽ（曜日指定）
        updItem.setIgaiWeek(gui01038Cks52.getIgaiWeek());
        // 福祉用具貸与の単価
        updItem.setSvTani(CommonDtoUtil.strValToDouble(gui01038Cks52.getSvTani()));
        // 福祉用具貸与マスタID
        updItem.setFygId(CommonDtoUtil.strValToInt(gui01038Cks52.getFygId()));
        // 枠外表示するかのフラグ
        updItem.setWakugaiFlg(CommonDtoUtil.strValToInt(gui01038Cks52.getWakugaiFlg()));
        // 更新条件キー
        CpnTucCks52Criteria whereItem = new CpnTucCks52Criteria();
        whereItem.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id));
        int updCnt = cpnTucCks52Mapper.updateByCriteriaSelective(updItem, whereItem);
        if (updCnt <= 0) {
            throw new ExclusiveException();
        }
        // 3.2.2 詳細情報内のサブリストの更新を行う
        apiSubProc(inDto, gui01038Cks52, ks51Id, ks52Id);
    }

    /**
     * 3.1 リクエストパラメータ.更新区分が"C"（新規）の場合、
     * 
     * @param inDto         週間計画詳細情報保存の入力Dto
     * @param gui01038Cks52 週間計画詳細
     * @param ks51Id        週間計画ID
     * @throws Exception
     */
    private void cks52DataInsProc(WeekPlanDetailsInfoUpdateServiceInDto inDto, Gui01038Cks52 gui01038Cks52,
            String ks51Id) throws Exception {
        // 3.1.1 週間計画詳細情報を登録する
        CpnTucCks52 insItem = new CpnTucCks52();
        // 週間計画ID
        insItem.setKs51Id(CommonDtoUtil.strValToInt(ks51Id));
        // 曜日
        insItem.setYoubi(gui01038Cks52.getYoubi());
        // 開始時間
        insItem.setKaishiJikan(gui01038Cks52.getKaishiJikan());
        // 終了時間
        insItem.setShuuryouJikan(gui01038Cks52.getShuuryouJikan());
        // 内容
        insItem.setNaiyouKnj(gui01038Cks52.getNaiyouKnj());
        // 文字サイズ
        insItem.setFontSize(CommonDtoUtil.strValToInt(gui01038Cks52.getFontSize()));
        // 表示モード
        insItem.setDispMode(CommonDtoUtil.strValToInt(gui01038Cks52.getDispMode()));
        // 文字位置
        insItem.setAlignment(CommonDtoUtil.strValToInt(gui01038Cks52.getAlignment()));
        // サービス種類
        insItem.setSvShuruiCd(gui01038Cks52.getSvShuruiCd());
        // サービス項目（台帳）
        insItem.setSvItemCd(CommonDtoUtil.strValToInt(gui01038Cks52.getSvItemCd()));
        // サービス事業者ID
        insItem.setSvJigyoId(CommonDtoUtil.strValToInt(gui01038Cks52.getSvJigyoId()));
        // 文字カラー
        insItem.setFontColor(CommonDtoUtil.strValToInt(gui01038Cks52.getFontColor()));
        // 背景カラー
        insItem.setBackColor(CommonDtoUtil.strValToInt(gui01038Cks52.getBackColor()));
        // 時間表示区分
        insItem.setTimeKbn(CommonDtoUtil.strValToInt(gui01038Cks52.getTimeKbn()));
        // 週単位以外のｻｰﾋﾞｽ区分
        insItem.setIgaiKbn(CommonDtoUtil.strValToInt(gui01038Cks52.getIgaiKbn()));
        // 週単位以外のｻｰﾋﾞｽ（日付指定）
        insItem.setIgaiDate(gui01038Cks52.getIgaiDate());
        // 週単位以外のｻｰﾋﾞｽ（曜日指定）
        insItem.setIgaiWeek(gui01038Cks52.getIgaiWeek());
        // 福祉用具貸与の単価
        insItem.setSvTani(CommonDtoUtil.strValToDouble(gui01038Cks52.getSvTani()));
        // 福祉用具貸与マスタID
        insItem.setFygId(CommonDtoUtil.strValToInt(gui01038Cks52.getFygId()));
        // 枠外表示するかのフラグ
        insItem.setWakugaiFlg(CommonDtoUtil.strValToInt(gui01038Cks52.getWakugaiFlg()));
        cpnTucCks52Mapper.insertSelective(insItem);
        // 3.1.2 詳細情報内のサブリストの登録を行う
        // ・【変数】.詳細データID = 3.1.1で新規した詳細データID
        apiSubProc(inDto, gui01038Cks52, ks51Id, CommonDtoUtil.objValToString(insItem.getKs52Id()));
    }

    /**
     * API定義_Sub
     * 
     * @param inDto         週間計画詳細情報保存の入力Dto
     * @param gui01038Cks52 週間計画詳細
     * @param ks51Id        週間計画ID
     * @param ks52Id        詳細データID
     * @throws Exception
     */
    private void apiSubProc(WeekPlanDetailsInfoUpdateServiceInDto inDto, Gui01038Cks52 gui01038Cks52, String ks51Id,
            String ks52Id) throws Exception {
        // 1.単項目チェック以外の入力チェック
        // 2.サブリストの登録と更新の処理
        // 2.1 週間計画加算リストを繰り返す、下記処理を行う
        cks55DataInsDelUpdProc(inDto, gui01038Cks52, ks51Id, ks52Id);
        // 2.2 週間計画担当者リストを繰り返す、下記処理を行う
        cks56DataInsDelUpdProc(inDto, gui01038Cks52, ks51Id, ks52Id);
        // 2.3 週間計画月日リストを繰り返す、下記処理を行う
        cks57DelInsProc(gui01038Cks52, ks51Id, ks52Id);
        // 2.4 週間計画隔週リストを繰り返す、下記処理を行う
        cks58InsDelUpdProc(inDto, gui01038Cks52, ks51Id, ks52Id);
        // 2.5 週間計画連動項目リストの件数分、下記処理を繰り返す。
        cks59InsDelUpdProc(inDto, gui01038Cks52, ks51Id, ks52Id);
    }

    /**
     * 2.5 週間計画連動項目リストの件数分、下記処理を繰り返す。
     * 
     * @param inDto         週間計画詳細情報保存の入力Dto
     * @param gui01038Cks52 週間計画詳細
     * @param ks51Id        週間計画ID
     * @param ks52Id        詳細データID
     * @throws Exception
     */
    private void cks59InsDelUpdProc(WeekPlanDetailsInfoUpdateServiceInDto inDto, Gui01038Cks52 gui01038Cks52,
            String ks51Id, String ks52Id) throws Exception {
        // 連動項目退避IDリスト
        List<String> deleteLinkedItemIdList = inDto.getDeleteLinkedItemIdList().stream()
                .map(Gui01038DeleteLinkedItemId::getDeleteLinkedItemId).collect(Collectors.toList());
        for (Gui01038Cks59 gui01038Cks59 : gui01038Cks52.getCks59List()) {
            // 2.5.1 週間計画連動項目リスト.連動項目データID = null or "" の場合、登録を行う
            if (StringUtils.isEmpty(gui01038Cks59.getKs59Id())) {
                CpnTucCks59 insItem = new CpnTucCks59();
                // 週間計画ID
                insItem.setKs51Id(CommonDtoUtil.strValToInt(ks51Id));
                // 詳細データID
                insItem.setKs52Id(CommonDtoUtil.strValToInt(ks52Id));
                // 連動項目ID
                insItem.setNikkaId(CommonDtoUtil.strValToInt(gui01038Cks59.getNikkaId()));
                cpnTucCks59Mapper.insertSelective(insItem);
            } else if (!StringUtils.isEmpty(gui01038Cks59.getKs59Id())
                    && deleteLinkedItemIdList.contains(gui01038Cks59.getKs59Id())) {
                // 2.5.2 週間計画連動項目リスト.連動項目データID ＜＞ null AND 週間計画連動項目リスト.連動項目データID ＜＞ ""の場合、
                // 且つ 該当連動項目データIDは連動項目退避IDリストに存在している場合、削除(物理論理削除)を行う
                // 削除条件キー
                CpnTucCks59Criteria whereItem = new CpnTucCks59Criteria();
                whereItem.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                        .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id))
                        .andKs59IdEqualTo(CommonDtoUtil.strValToInt(gui01038Cks59.getKs59Id()));
                // 削除（論理）時の共通カラム値設定処理
                int delCnt = cpnTucCks59Mapper.deleteByCriteria(whereItem);
                if (delCnt <= 0) {
                    throw new ExclusiveException();
                }
            } else {
                // 2.5.3 上記以外の場合、更新を行う
                CpnTucCks59 updItem = new CpnTucCks59();
                // 連動項目ID
                updItem.setNikkaId(CommonDtoUtil.strValToInt(gui01038Cks59.getNikkaId()));
                // 更新条件キー
                CpnTucCks59Criteria whereItem = new CpnTucCks59Criteria();
                whereItem.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                        .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id))
                        .andKs59IdEqualTo(CommonDtoUtil.strValToInt(gui01038Cks59.getKs59Id()));
                int updCnt = cpnTucCks59Mapper.updateByCriteriaSelective(updItem, whereItem);
                if (updCnt <= 0) {
                    throw new ExclusiveException();
                }
            }
        }
    }

    /**
     * 2.4 週間計画隔週リストを繰り返す、下記処理を行う
     * 
     * @param inDto         週間計画詳細情報保存の入力Dto
     * @param gui01038Cks52 週間計画詳細
     * @param ks51Id        週間計画ID
     * @param ks52Id        詳細データID
     * @throws Exception
     */
    private void cks58InsDelUpdProc(WeekPlanDetailsInfoUpdateServiceInDto inDto, Gui01038Cks52 gui01038Cks52,
            String ks51Id, String ks52Id) throws Exception {
        // 隔週退避IDリスト
        List<String> deleteTukihiIdList = inDto.getDeleteTukihiIdList().stream()
                .map(Gui01038DeleteTukihiId::getDeleteTukihiId).collect(Collectors.toList());
        for (Gui01038Cks58 gui01038Cks58 : gui01038Cks52.getCks58List()) {
            // 2.4.1 週間計画隔週リスト.隔週データID = null or "" の場合、28-73 週間計画表（隔週）の登録を行う
            if (gui01038Cks58.getKs58Id() == null || StringUtils.EMPTY.equals(gui01038Cks58.getKs58Id())) {
                CpnTucCks58 insItem = new CpnTucCks58();
                // 週間計画ID
                insItem.setKs51Id(CommonDtoUtil.strValToInt(ks51Id));
                // 詳細データID
                insItem.setKs52Id(CommonDtoUtil.strValToInt(ks52Id));
                // 隔週基準年月日
                insItem.setKakusyuuYmd(gui01038Cks58.getKakusyuuYmd());
                // 隔週週間隔
                insItem.setKakusyuuKankaku(CommonDtoUtil.strValToInt(gui01038Cks58.getKakusyuuKankaku()));
                // 曜日区分
                insItem.setYoubi(CommonDtoUtil.strValToInt(gui01038Cks58.getYoubi()));
                cpnTucCks58Mapper.insertSelective(insItem);
            } else if (gui01038Cks58.getKs58Id() != null && !StringUtils.EMPTY.equals(gui01038Cks58.getKs58Id())
                    && deleteTukihiIdList.contains(gui01038Cks58.getKs58Id())) {
                // 2.4.2 週間計画隔週リスト.隔週データID ＜＞ null AND 週間計画隔週リスト.隔週データID ＜＞ ""の場合、
                // 且つ 該当隔週データIDは隔週退避IDリストに存在している場合、28-73 週間計画表（隔週）の削除(論理削除)を行う
                // 削除条件キー
                CpnTucCks58Criteria whereItem = new CpnTucCks58Criteria();
                whereItem.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                        .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id))
                        .andKs58IdEqualTo(CommonDtoUtil.strValToInt(gui01038Cks58.getKs58Id()));
                // 削除（論理）時の共通カラム値設定処理
                int delCnt = cpnTucCks58Mapper.deleteByCriteria(whereItem);
                if (delCnt <= 0) {
                    throw new ExclusiveException();
                }
            } else {
                // 2.4.3 上記以外の場合、28-73 週間計画表（隔週）の更新を行う
                CpnTucCks58 updItem = new CpnTucCks58();
                // 隔週基準年月日
                updItem.setKakusyuuYmd(gui01038Cks58.getKakusyuuYmd());
                // 隔週週間隔
                updItem.setKakusyuuKankaku(CommonDtoUtil.strValToInt(gui01038Cks58.getKakusyuuKankaku()));
                // 曜日区分
                updItem.setYoubi(CommonDtoUtil.strValToInt(gui01038Cks58.getYoubi()));
                // 更新条件キー
                CpnTucCks58Criteria whereItem = new CpnTucCks58Criteria();
                whereItem.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                        .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id))
                        .andKs58IdEqualTo(CommonDtoUtil.strValToInt(gui01038Cks58.getKs58Id()));
                int updCnt = cpnTucCks58Mapper.updateByCriteriaSelective(updItem, whereItem);
                if (updCnt <= 0) {
                    throw new ExclusiveException();
                }
            }
        }
    }

    /**
     * 2.3 週間計画月日リストを繰り返す、下記処理を行う
     * 
     * @param gui01038Cks52 週間計画詳細
     * @param ks51Id        週間計画ID
     * @param ks52Id        詳細データID
     * @throws Exception
     */
    private void cks57DelInsProc(Gui01038Cks52 gui01038Cks52, String ks51Id, String ks52Id) throws Exception {
        for (Gui01038Cks57 gui01038Cks57 : gui01038Cks52.getCks57List()) {
            // 2.3.1 週間計画月日リスト.カウンター ＜＞ null 且つ 週間計画月日リスト.カウンター ＜＞ ""の場合、28-67
            // 週間計画（月日指定）を削除(論理削除)する
            if (gui01038Cks57.getKs57Id() != null && !StringUtils.EMPTY.equals(gui01038Cks57.getKs57Id())) {
                // 削除条件キー
                CpnTucCks57Criteria whereItem = new CpnTucCks57Criteria();
                whereItem.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                        .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id))
                        .andKs57IdEqualTo(CommonDtoUtil.strValToInt(gui01038Cks57.getKs57Id()));
                int delCnt = cpnTucCks57Mapper.deleteByCriteria(whereItem);
                if (delCnt <= 0) {
                    throw new ExclusiveException();
                }
            }
            // 2.3.2 28-67 週間計画（月日指定）を登録する
            CpnTucCks57 insItem = new CpnTucCks57();
            // 週間計画ID
            insItem.setKs51Id(CommonDtoUtil.strValToInt(ks51Id));
            // // 詳細データID
            insItem.setKs52Id(CommonDtoUtil.strValToInt(ks52Id));
            // 開始日
            insItem.setStartYmd(gui01038Cks57.getStartYmd());
            // 終了日
            insItem.setEndYmd(gui01038Cks57.getEndYmd());
            cpnTucCks57Mapper.insertSelective(insItem);
        }
    }

    /**
     * 2.2 週間計画担当者リストを繰り返す、下記処理を行う
     * 
     * @param inDto         週間計画詳細情報保存の入力Dto
     * @param gui01038Cks52 週間計画詳細
     * @param ks51Id        週間計画ID
     * @param ks52Id        詳細データID
     * @throws Exception
     */
    private void cks56DataInsDelUpdProc(WeekPlanDetailsInfoUpdateServiceInDto inDto, Gui01038Cks52 gui01038Cks52,
            String ks51Id, String ks52Id) throws Exception {
        // 担当者退避IDリスト
        List<String> deleteKasanIdList = inDto.getDeleteTanIdList().stream().map(Gui01038DeleteTanId::getDeleteTanId)
                .collect(Collectors.toList());
        for (Gui01038Cks56 gui01038Cks56 : gui01038Cks52.getCks56List()) {
            // 2.2.1 週間計画担当者リスト.担当者データID = null or "" の場合、 28-29 週間計画表（担当者）の登録を行う
            if (gui01038Cks56.getKs56Id() == null || StringUtils.EMPTY.equals(gui01038Cks56.getKs56Id())) {
                CpnTucCks56 insItem = new CpnTucCks56();
                // 週間計画ID
                insItem.setKs51Id(CommonDtoUtil.strValToInt(ks51Id));
                // 詳細データID
                insItem.setKs52Id(CommonDtoUtil.strValToInt(ks52Id));
                // 施設
                insItem.setShokushuId(CommonDtoUtil.strValToInt(gui01038Cks56.getShokushuId()));
                cpnTucCks56Mapper.insertSelective(insItem);
            } else if (gui01038Cks56.getKs56Id() != null && !StringUtils.EMPTY.equals(gui01038Cks56.getKs56Id())
                    && deleteKasanIdList.contains(gui01038Cks56.getKs56Id())) {
                // 2.2.2 週間計画担当者リスト.担当者データID ＜＞ null AND 週間計画担当者リスト.担当者データID ＜＞ ""
                // 且つ 該当担当者データIDは担当者退避IDリストに存在している場合、28-29 週間計画表（担当者）の削除(論理削除)を行う
                // 削除条件キー
                CpnTucCks56Criteria whereItem = new CpnTucCks56Criteria();
                whereItem.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                        .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id))
                        .andKs56IdEqualTo(CommonDtoUtil.strValToInt(gui01038Cks56.getKs56Id()));
                int delCnt = cpnTucCks56Mapper.deleteByCriteria(whereItem);
                if (delCnt <= 0) {
                    throw new ExclusiveException();
                }
            } else {
                // 2.2.3 上記以外の場合、28-29 週間計画表（担当者）の更新を行う
                CpnTucCks56 updItem = new CpnTucCks56();
                // 施設
                updItem.setShokushuId(CommonDtoUtil.strValToInt(gui01038Cks56.getShokushuId()));
                // 更新条件キー
                CpnTucCks56Criteria whereItem = new CpnTucCks56Criteria();
                whereItem.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                        .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id))
                        .andKs56IdEqualTo(CommonDtoUtil.strValToInt(gui01038Cks56.getKs56Id()));
                int updCnt = cpnTucCks56Mapper.updateByCriteriaSelective(updItem, whereItem);
                if (updCnt <= 0) {
                    throw new ExclusiveException();
                }
            }
        }
    }

    /**
     * 2.1 週間計画加算リストを繰り返す、下記処理を行う
     * 
     * @param inDto         週間計画詳細情報保存の入力Dto
     * @param gui01038Cks52 週間計画詳細
     * @param ks51Id        週間計画ID
     * @param ks52Id        詳細データID
     * @throws Exception
     */
    private void cks55DataInsDelUpdProc(WeekPlanDetailsInfoUpdateServiceInDto inDto, Gui01038Cks52 gui01038Cks52,
            String ks51Id, String ks52Id) throws ExclusiveException {
        // 加算退避IDリスト
        List<String> deleteKasanIdList = inDto.getDeleteKasanIdList().stream()
                .map(Gui01038DeleteKasanId::getDeleteKasanId).collect(Collectors.toList());
        for (Gui01038Cks55 gui01038Cks55 : gui01038Cks52.getCks55List()) {
            // 2.1.1 週間計画加算リスト.加算データID = null or "" の場合、28-28 週間計画表（加算データ）の登録を行う
            if (gui01038Cks55.getKs55Id() == null || StringUtils.EMPTY.equals(gui01038Cks55.getKs55Id())) {
                CpnTucCks55 insItem = new CpnTucCks55();
                // 週間計画ID
                insItem.setKs51Id(CommonDtoUtil.strValToInt(ks51Id));
                // 詳細データID
                insItem.setKs52Id(CommonDtoUtil.strValToInt(ks52Id));
                // サービス事業者ID
                insItem.setSvJigyoId(CommonDtoUtil.strValToInt(gui01038Cks55.getSvJigyoId()));
                // サービス項目ID
                insItem.setSvItemCd(CommonDtoUtil.strValToInt(gui01038Cks55.getSvItemCd()));
                // 回数
                insItem.setKaisuu(CommonDtoUtil.strValToInt(gui01038Cks55.getKaisuu()));
                // 福祉用具貸与の単価
                insItem.setSvTani(CommonDtoUtil.strValToDouble(gui01038Cks55.getSvTani()));
                // 福祉用具貸与マスタID
                insItem.setFygId(CommonDtoUtil.strValToInt(gui01038Cks55.getFygId()));
                cpnTucCks55Mapper.insertSelective(insItem);
            } else if (gui01038Cks55.getKs55Id() != null && !StringUtils.EMPTY.equals(gui01038Cks55.getKs55Id())
                    && deleteKasanIdList.contains(gui01038Cks55.getKs55Id())) {
                // 2.1.2 週間計画加算リスト.加算データID ＜＞ null AND 週間計画加算リスト.加算データID ＜＞ ""
                // 且つ 該当加算データIDは加算退避IDリストに存在している場合、28-28 週間計画表（加算データ）の削除(論理削除)を行う
                // 削除条件キー
                CpnTucCks55Criteria whereItem = new CpnTucCks55Criteria();
                whereItem.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                        .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id))
                        .andKs55IdEqualTo(CommonDtoUtil.strValToInt(gui01038Cks55.getKs55Id()));
                int delCnt = cpnTucCks55Mapper.deleteByCriteria(whereItem);
                if (delCnt <= 0) {
                    throw new ExclusiveException();
                }
            } else {
                // 2.1.3 上記以外の場合、28-28 週間計画表（加算データ）の更新を行う
                CpnTucCks55 updItem = new CpnTucCks55();
                // サービス事業者ID
                updItem.setSvJigyoId(CommonDtoUtil.strValToInt(gui01038Cks55.getSvJigyoId()));
                // サービス項目ID
                updItem.setSvItemCd(CommonDtoUtil.strValToInt(gui01038Cks55.getSvItemCd()));
                // 回数
                updItem.setKaisuu(CommonDtoUtil.strValToInt(gui01038Cks55.getKaisuu()));
                // 福祉用具貸与の単価
                updItem.setSvTani(CommonDtoUtil.strValToDouble(gui01038Cks55.getSvTani()));
                // 福祉用具貸与マスタID
                updItem.setFygId(CommonDtoUtil.strValToInt(gui01038Cks55.getFygId()));
                // 更新条件キー
                CpnTucCks55Criteria whereItem = new CpnTucCks55Criteria();
                whereItem.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id))
                        .andKs52IdEqualTo(CommonDtoUtil.strValToInt(ks52Id))
                        .andKs55IdEqualTo(CommonDtoUtil.strValToInt(gui01038Cks55.getKs55Id()));
                int updCnt = cpnTucCks55Mapper.updateByCriteriaSelective(updItem, whereItem);
                if (updCnt <= 0) {
                    throw new ExclusiveException();
                }
            }
        }
    }

    /**
     * 【週間計画履歴】情報を更新する
     * 
     * @param inDto 週間計画詳細情報保存の入力Dto
     * @return ks51Id 週間計画ID
     * @throws Exception Exception
     */
    private String cks51DataUpdProc(WeekPlanDetailsInfoUpdateServiceInDto inDto) throws Exception {
        // 変数.週間計画ID
        String ks51Id = StringUtils.EMPTY;
        for (Gui01038Cks51 gui01038Cks51 : inDto.getCks51List()) {
            // ・変数.週間計画ID＝リクエストパラメータ.週間計画履歴リスト.週間計画ID
            ks51Id = gui01038Cks51.getKs51Id();
            CpnTucCks51 updItem = new CpnTucCks51();
            // 作成日
            updItem.setCreateYmd(gui01038Cks51.getCreateYmd());
            // 職員ID
            updItem.setShokuId(CommonDtoUtil.strValToInt(gui01038Cks51.getShokuId()));
            // 週単位以外ｻｰﾋﾞｽ
            updItem.setWIgaiKnj(gui01038Cks51.getWIgaiKnj());
            // 当該年月
            updItem.setTougaiYm(gui01038Cks51.getTougaiYm());
            // 有効期間ID
            updItem.setTermid(CommonDtoUtil.strValToInt(gui01038Cks51.getTermId()));
            // 更新条件キー
            CpnTucCks51Criteria whereItem = new CpnTucCks51Criteria();
            whereItem.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id));
            int updCnt = cpnTucCks51Mapper.updateByCriteriaSelective(updItem, whereItem);
            if (updCnt <= 0) {
                throw new ExclusiveException();
            }
        }
        return ks51Id;
    }

    /**
     * 【週間計画履歴】情報を削除(論理削除)する
     * 
     * @param inDto 週間計画詳細情報保存の入力Dto
     * @return ks51Id 週間計画ID
     * @throws Exception Exception
     */
    private String cks51DataDelProc(WeekPlanDetailsInfoUpdateServiceInDto inDto) throws Exception {
        // 変数.週間計画ID
        String ks51Id = StringUtils.EMPTY;
        for (Gui01038Cks51 gui01038Cks51 : inDto.getCks51List()) {
            // ・変数.週間計画ID＝リクエストパラメータ.週間計画履歴リスト.週間計画ID
            ks51Id = gui01038Cks51.getKs51Id();
            // 削除条件キー
            CpnTucCks51Criteria whereItem = new CpnTucCks51Criteria();
            whereItem.createCriteria().andKs51IdEqualTo(CommonDtoUtil.strValToInt(ks51Id));
            int delCnt = cpnTucCks51Mapper.deleteByCriteria(whereItem);
            if (delCnt <= 0) {
                throw new ExclusiveException();
            }
        }
        return ks51Id;
    }

    /**
     * 【週間計画履歴】情報を登録する
     * 
     * @param inDto 週間計画詳細情報保存の入力Dto
     * @return ks51Id 週間計画ID
     */
    private String cks51DataInsProc(WeekPlanDetailsInfoUpdateServiceInDto inDto) throws Exception {
        // 変数.週間計画ID
        String ks51Id = StringUtils.EMPTY;
        Integer ks51IdInt = 0;
        for (Gui01038Cks51 gui01038Cks51 : inDto.getCks51List()) {
            CpnTucCks51 insItem = new CpnTucCks51();
            // 計画期間ID
            insItem.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
            // 法人ID
            insItem.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
            // 施設ID
            insItem.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            // 事業者ID
            insItem.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            // 利用者ID
            insItem.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
            // 作成日
            insItem.setCreateYmd(gui01038Cks51.getCreateYmd());
            // 職員ID
            insItem.setShokuId(CommonDtoUtil.strValToInt(gui01038Cks51.getShokuId()));
            // 週単位以外ｻｰﾋﾞｽ
            insItem.setWIgaiKnj(gui01038Cks51.getWIgaiKnj());
            // 当該年月
            insItem.setTougaiYm(gui01038Cks51.getTougaiYm());
            // 有効期間ID
            insItem.setTermid(CommonDtoUtil.strValToInt(gui01038Cks51.getTermId()));
            cpnTucCks51Mapper.insertSelective(insItem);
            ks51IdInt = insItem.getKs51Id().intValue();
        }
        // 2.1.1 上記2.1登録した後処理
        // ① 週間計画詳細リスト.件数 > 0 の場合、
        if (CollectionUtils.size(inDto.getCks52List()) > 0) {
            // 【変数】.週間計画ID = 2.1で新規した週間計画ID
            ks51Id = CommonDtoUtil.objValToString(ks51IdInt);
        }
        return ks51Id;
    }
}
