package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00978Info;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00978KoumokuOut;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnCpmv01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpSvCnvLogic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnWeekLogic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.ConvertInfoDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.SvItemcodeOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.TermId2DateLogicOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekTableImageDuplicateSelectInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekTableImageDuplicateSelectOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.BaseByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.BaseOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnGetScodeFromItemuseSougouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnGetScodeFromItemuseSougouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ScodeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ScodeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvKHokenCdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvKHokenCdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSougouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * GUI00978_［週間表イメージ］週間表複写データ取得サービス.
 *
 * <AUTHOR>
 */
@Service
public class WeekTableImageDuplicateSelectServiceImpl
		extends SelectServiceImpl<WeekTableImageDuplicateSelectInDto, WeekTableImageDuplicateSelectOutDto> {
	/** ロガー */
	private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

	// GUI00978_［週間表イメージ］初期情報取得サービス.
	@Autowired
	private WeekTableImageInitSelectServiceImpl weekTableImageInitSelectServiceImpl;

	/** 共通関数KghKrkZCpnFuncLogic */
	@Autowired
	private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;

	/** 支援事業所名を取得する */
	@Autowired
	private KghCmpF01Logic kghCmpF01Logic;

	@Autowired
	private KghCmpSvCnvLogic kghCmpSvCnvLogic;

	@Autowired
	private Nds3GkFunc01Logic nds3GkFunc01Logic;

	@Autowired
	private ComMscSvjigyoSelectMapper comMscSvjigyoSelectMapper;

	@Autowired
	private ComMhcItemuseSougouSelectMapper comMhcItemuseSougouSelectMapper;

	@Autowired
	private KghCmnCpmv01Logic kghCmnCpmv01Logic;

	/** サービス種類から総合事業かどうかをチェックする */
	@Autowired
	private KghCmnF01Logic kghCmnF01Logic;

	/** 項目識別コード */
	@Autowired
	private ComMhcItemSelectMapper comMhcItemSelectMapper;

	@Autowired
	private KghKrkZCpnWeekLogic kghKrkZCpnWeekLogic;

	/**
	 * ［週間表イメージ］週間表複写データ取得
	 * 
	 * @param inDto ［週間表イメージ］週間表複写データ取得の入力DTO.
	 * @return ［週間表イメージ］週間表複写データ取得の出力Dto
	 * @throws Exception Exception
	 */
	@Override
	protected WeekTableImageDuplicateSelectOutDto mainProcess(WeekTableImageDuplicateSelectInDto inDto)
			throws Exception {
		LOG.info(Constants.START);

		WeekTableImageDuplicateSelectOutDto outDto = new WeekTableImageDuplicateSelectOutDto();

		// 2. 週間表（ヘッダ）履歴情報の検索処理を行う
		// 2.1. レスポンスパラメータ.複写可否フラグ = TRUEで設定する
		outDto.setCopyFlg(CommonConstants.COPY_FLG_TRUE);

		// ｻｰﾋﾞｽ検索の開始、終了をtermidから取得
		TermId2DateLogicOutDto termId2DateLogicOutDto = kghKrkZCpnFuncLogic
				.getTermid2date(CommonDtoUtil.strValToInt(inDto.getSc1Id()), null, null);

		// 2.3. 週間表詳細情報を取得する
		List<Gui00978Info> gui00978Info = weekTableImageInitSelectServiceImpl.getInfo(inDto.getWeek1Id(),
				termId2DateLogicOutDto.getSYmd(), termId2DateLogicOutDto.getEYmd());
		outDto.setWeek2List(gui00978Info);

		outDto.getWeek2List().forEach(l -> {
			if (CommonConstants.KAITEI_FLG_2.equals(inDto.getKaiteiFlg())) {
				if (l.getKaishiJikan().length() >= CommonConstants.INT_2) {
					// ・開始時＝上記抽出詳細リスト. 開始時間の左から二桁を区切り
					String sHour = CommonDtoUtil
							.objValToString(l.getKaishiJikan().charAt(0) + l.getKaishiJikan().charAt(1));
					// 開始時＝ 24 の場合
					if (CommonConstants.HOUR_24.equals(sHour)) {
						l.setKaishiJikan(CommonConstants.HOUR_00 + l.getKaishiJikan().substring(CommonConstants.INT_2));
					}
					// レスポンスパラメータ.詳細リスト. 曜日＜＞”0000000” 且つ レスポンスパラメータ.詳細リスト. 曜日＜＞”9999999” の場合
					if (!CommonConstants.YOUBI_0.equals(l.getYoubi())
							&& !CommonConstants.YOUBI_9.equals(l.getYoubi())) {
						l.setYoubi(l.getYoubi().charAt(6) + l.getYoubi().substring(0, 6));
					}
				}

				if (l.getKaishiJikan().length() >= CommonConstants.INT_2) {
					// ・終了時＝上記抽出詳細リスト. 終了時間の左から二桁を区切り
					String eHour = CommonDtoUtil
							.objValToString(l.getShuuryouJikan().charAt(0) + l.getShuuryouJikan().charAt(1));
					// 開始時＝ 24 の場合
					if (CommonConstants.HOUR_24.equals(eHour)) {
						l.setShuuryouJikan(CommonConstants.HOUR_00 + l.getShuuryouJikan().substring(2));
					}
				}

				// 週間表サービス項目情報取得
				Gui00978KoumokuOut out = this.getKoumokuCd(l.getSvJigyoId(), l.getSvItemCd(), l.getKaishiJikan(),
						l.getShuuryouJikan(), inDto.getTermidFr(), inDto.getTermidTo(),
						CommonDtoUtil.strNullToEmpty(inDto.getCopyMotoYm()),
						CommonDtoUtil.strNullToEmpty(inDto.getCopySakiYm()), l.getSvShuruiCd(), CommonConstants.STR_0,
						StringUtils.EMPTY);
				l.setSvItemCd(out.getKoumokuCd());

				// 2.4.3. 上記2.3で抽出した詳細リスト.加算リストを繰り返す
				if (!CollectionUtils.isEmpty(l.getWeek3List())) {
					l.getWeek3List().forEach(week3 -> {
						Gui00978KoumokuOut o = this.getKoumokuCd(week3.getSvJigyoId(), week3.getSvItemCd(),
								CommonConstants.BLANK_STRING, CommonConstants.BLANK_STRING, inDto.getTermidFr(),
								inDto.getTermidTo(), CommonDtoUtil.strNullToEmpty(inDto.getCopyMotoYm()),
								CommonDtoUtil.strNullToEmpty(inDto.getCopySakiYm()), l.getSvShuruiCd(), CommonConstants.STR_1,
								week3.getSvJigyoId());
						week3.setSvItemCd(o.getKoumokuCd());

						// ② 追加の加算情報の項目を編集する
						if (!CommonConstants.STR_ZERO.equals(out.getDivisionId())
								&& !StringUtils.isEmpty(out.getDivisionCd())) {
							week3.setSvJigyoId(l.getSvJigyoId());
							week3.setSvItemCd(out.getDivisionId());
							week3.setKaisuu(CommonConstants.CNT_ONE);
						}
					});
				}

				// ③ サービス項目名称を取得する
				l.setSvItemKnj(kghKrkZCpnWeekLogic.getName(CommonDtoUtil.strValToInt(l.getSvShuruiCd()),
						CommonDtoUtil.strValToInt(l.getSvItemCd()), termId2DateLogicOutDto.getSYmd(),
						termId2DateLogicOutDto.getEYmd()));
			}
		});

		LOG.info(Constants.END);
		return outDto;
	}

	/**
	 * 週間表サービス項目情報取得
	 * 
	 * @param svJigyoId     サービス事業者ID
	 * @param svItemCd      サービス項目ID
	 * @param kaishiJikan   サービス開始時間
	 * @param shuuryouJikan サービス終了時間
	 * @param termidFr      取込有効期間ID
	 * @param termidTo      該当有効期間ID
	 * @param copyMotoYm    複写元年月
	 * @param copySakiYm    複写先年月
	 * @param svShuruiCd    サービス種類CD
	 * @param kasanFlg      加算フラグ
	 * @param addSvJigyoId  加算サービス事業者ID
	 */
	public Gui00978KoumokuOut getKoumokuCd(String svJigyoId, String svItemCd, String kaishiJikan, String shuuryouJikan,
			String termidFr, String termidTo, String copyMotoYm, String copySakiYm, String svShuruiCd, String kasanFlg,
			String addSvJigyoId) {

		Gui00978KoumokuOut out = new Gui00978KoumokuOut();

		// ・変数.サービス項目ID = 引き渡しパラメータ.サービス項目ID
		String itemCd = svItemCd;

		// 2.1 引き渡しパラメータ.取込有効期間IDは引き渡しパラメータ.該当有効期間IDと同じではない 且つ
		// (引き渡しパラメータ.該当有効期間ID - 引き渡しパラメータ.取込有効期間ID = 1 ) の 場合
		if (CommonDtoUtil.strValToInt(termidTo) - CommonDtoUtil.strValToInt(termidFr) == CommonConstants.INT_1) {
			// 2.1.1 引き渡しパラメータ.★サービス種類コード→4-5が81から99の範囲で 且つ 加算フラグが1ではない の場合（サービス種類がが独自サービス）
			if (CommonDtoUtil.strValToInt(svShuruiCd) >= CommonConstants.SHURUI_81
					&& CommonDtoUtil.strValToInt(svShuruiCd) <= CommonConstants.SHURUI_99
					&& !CommonConstants.KASAN_FLG_1.equals(kasanFlg)) {
				itemCd = svItemCd;
			} else {
				ConvertInfoDto convertInfoDto = new ConvertInfoDto();
				Integer outFlg = kghCmnCpmv01Logic.ufSvCnv(CommonDtoUtil.strValToInt(svJigyoId),
						CommonDtoUtil.strValToInt(termidFr), CommonDtoUtil.strValToInt(svItemCd),
						CommonDtoUtil.strValToInt(termidTo), kaishiJikan, shuuryouJikan, convertInfoDto);
				// 返却情報.分割内部ID ＝ 上記共通関数のOUTPUT情報.分割内部ID
				out.setDivisionId(CommonDtoUtil.objValToString(convertInfoDto.getItm()));
				// 返却情報.分割内部コード ＝ 上記共通関数のOUTPUT情報.分割内部コード
				out.setDivisionCd(convertInfoDto.getSCode());
				if (outFlg == CommonConstants.INT_1) {
					itemCd = CommonDtoUtil.objValToString(convertInfoDto.getItm());
				} else {
					itemCd = CommonConstants.STR_0;
				}
			}
		}

		// 2.2 引き渡しパラメータ.該当有効期間ID=4 且つ 引き渡しパラメータ.複写先年月 ＜＞”” の場合
		if (StringUtils.equals(termidTo, CommonConstants.TERM_ID_4) && !StringUtils.isEmpty(copySakiYm)) {
			// ・（ 引き渡しパラメータ.サービス種類CD = ”15” OR 引き渡しパラメータ.サービス種類CD = ”78”）まだは 加算フラグ＝1 の場合
			if ((CommonConstants.SHURUI_15.equals(svShuruiCd) || CommonConstants.SHURUI_78.equals(svShuruiCd))
					|| CommonConstants.KASAN_FLG_1.equals(kasanFlg)) {
				// 共通関数「f_cmp_termid4_tanki_cnv」を呼び出しサービス項目を取得
				// 短期療養itemcodeコンバーター
				long res = kghCmpF01Logic.getTermid4TankiCnv(copyMotoYm, copySakiYm,
						CommonDtoUtil.strValToInt(svJigyoId), CommonDtoUtil.strValToInt(svItemCd));
				if (res > CommonConstants.INT_0) {
					itemCd = CommonDtoUtil.objValToString(res);
				}
			}
		}

		// 2.3 引き渡しパラメータ.取込有効期間ID =8 且つ 引き渡しパラメータ.該当有効期間ID=8 且つ 引き渡しパラメータ.複写先年月 ＜＞”” の場合
		if (StringUtils.equals(termidTo, CommonConstants.TERM_ID_4) && !StringUtils.isEmpty(copySakiYm)) {
			// ・引き渡しパラメータ.サービス種類CD = ”15” OR 引き渡しパラメータ.サービス種類CD = ”78” の場合
			if (CommonConstants.SHURUI_15.equals(svShuruiCd) || CommonConstants.SHURUI_78.equals(svShuruiCd)) {
				// 共通関数「uf_sv_cnv_itemcode」を呼び出し、取込サービス（同期間）が登録否を判断する
				// サービスのコンバート（同一term期間内）
				SvItemcodeOutDto svOut = kghCmpSvCnvLogic.cnvSvItemcode(CommonDtoUtil.strValToInt(svJigyoId),
						CommonDtoUtil.strValToInt(termidFr), CommonDtoUtil.strValToInt(svItemCd), null);

				if (svOut.getResult() > CommonConstants.INT_0) {
					itemCd = CommonDtoUtil.objValToString(svOut.getSvCode());
				} else {
					itemCd = CommonConstants.STR_ZERO;
				}
			}

			// 2.3-1. サービス種類から総合事業かどうかをチェックする
			boolean sgTypeFlg = kghCmnF01Logic.chkSougouSvtype(svShuruiCd);

			// 変数.対象
			boolean chgObjFlg = false;
			// 変数.加算サービス項目ID ＝ 返却情報.分割内部コード
			String chgSvjigyo = out.getDivisionCd();

			// 2.3-2. 引き渡しパラメータ.加算フラグ ＝ 1引き渡しパラメータ.取込先有効期間ID ＝ 引き渡しパラメータ.取込元有効期間ID 且つ
			// 引き渡しパラメータ.複写先年月 ＜＞ ”” 且つ
			// 引き渡しパラメータ.取込元有効期間IDが12以上 且つ 上記処理2.4-1で取得したチェック結果がfalse:総合事業ではない の場合
			if (CommonConstants.STR_1.equals(kasanFlg) && StringUtils.equals(termidTo, termidFr)
					&& StringUtils.isNotEmpty(copySakiYm)
					&& CommonDtoUtil.strValToInt(termidFr) >= CommonConstants.NUMBER_TWELVE && !sgTypeFlg) {
				// 2.3-2.1 引き渡しパラメータ.取込元有効期間IDが 12 の場合
				// 2.3-2.2 引き渡しパラメータ.取込元有効期間IDが 13 の場合
				chgObjFlg = targetTermChk(termidFr, termidTo, copySakiYm, svShuruiCd, itemCd);
			}

			// 2.3-3. 上記処理2.3-2で編集した変数.対象がtrueの場合
			if (chgObjFlg) {
				// 共通関数「uf_sv_cnv」を呼び出し、取込サービスが登録否を判断する
				SvItemcodeOutDto svCodeOutDto = kghCmpSvCnvLogic.cnvSvItemcode(CommonDtoUtil.strValToInt(addSvJigyoId),
						CommonDtoUtil.strValToInt(termidTo), CommonDtoUtil.strValToInt(chgSvjigyo), copySakiYm);

				// ・返却情報.分割内部ID ＝ 上記取得したサービス内部ID
				out.setDivisionId(CommonDtoUtil.objValToString(svCodeOutDto.getSvCode()));
				// ・返却情報.分割内部コード ＝ 上記取得したサービスコード
				out.setDivisionCd(svCodeOutDto.getScode());
				// ・戻り値 ＝ １の場合
				if (CommonConstants.NUMBER_1.equals(svCodeOutDto.getResult())) {
					// 返却情報.サービス項目（台帳）ID ＝ 上記取得したサービス内部ID
					itemCd = CommonDtoUtil.objValToString(svCodeOutDto.getSvCode());
				}
				// ・上記以外の場合
				else {
					// 返却情報.サービス項目（台帳）ID ＝ 0
					itemCd = CommonConstants.STR_0;
				}
			}

		}

		// 2.4 引き渡しパラメータ.取込有効期間ID = 引き渡しパラメータ.該当有効期間ID 且つ 引き渡しパラメータ.複写先年月 ＜＞”” の場合
		if (StringUtils.equals(termidFr, termidTo) && !StringUtils.isEmpty(copySakiYm)) {
			// 2.4.1 引き渡しパラメータ.サービス種類CD =("A1"or "A5" or "A9" )の場合
			if (CommonConstants.SV_KIND_CD_A1.equals(svShuruiCd) || CommonConstants.SV_KIND_CD_A5.equals(svShuruiCd)
					|| CommonConstants.SV_KIND_CD_A9.equals(svShuruiCd)) {
				ConvertInfoDto convertInfoDto = new ConvertInfoDto();
				kghCmnCpmv01Logic.ufSvCnv(CommonDtoUtil.strValToInt(svJigyoId), CommonDtoUtil.strValToInt(termidFr),
						CommonDtoUtil.strValToInt(svItemCd), CommonDtoUtil.strValToInt(termidTo), kaishiJikan,
						shuuryouJikan, convertInfoDto);
				// 2.4.1.1 変数.サービスコード = 上記OUTPUT情報.サービスコードの3桁目から4桁を取得する
				String svCode = StringUtils.EMPTY;
				String sCode = convertInfoDto.getSCode();
				if (!StringUtils.isEmpty(sCode) && sCode.length() >= 2) {
					svCode = sCode.substring(2, Math.min(sCode.length(), 6));
				}

				// 2.4.1.2 変数.処理年月日 = 引き渡しパラメータ.複写先年月 + "/01"
				String processYM = copySakiYm + CommonConstants.STRING_FIRST_DAY;

				// 2.4.1.3　保険者Idの取得を行う
				// 引き渡しパラメータ.サービス事業者ID
				SvKHokenCdByCriteriaInEntity svKrdInEntity = new SvKHokenCdByCriteriaInEntity();
				svKrdInEntity.setAlJid(svJigyoId);

				List<SvKHokenCdOutEntity> svKrdOut = comMscSvjigyoSelectMapper
						.findSvKHokenCdByCriteria(svKrdInEntity);

				// 2.4.1.4. 総合事業の種類コードを取得
				BaseByCriteriaInEntity baseInEntity = new BaseByCriteriaInEntity();
				// 該当有効期間ID
				baseInEntity.setLiTermid(termidTo);
				// 変数.サービス内部ID
				baseInEntity.setLlItem(itemCd);

				List<BaseOutEntity> BaseOutEntityList = comMhcItemuseSougouSelectMapper
						.findBaseByCriteria(baseInEntity);
				
				// 2.4.1.5. 引き渡しパラメータ.取込先有効期間ID ＞ 0の場合、かつ上記2.4.1.4で取得したサービス種類コードが空以外の場合、かつ変数.サービスコードが空以外の場合
				if (CommonDtoUtil.strValToInt(termidFr) > CommonConstants.INT_0
						&& BaseOutEntityList.size() > 0 && !StringUtils.isEmpty(svCode)) {
					// ①、月末算出を行う
					String lastDate = nds3GkFunc01Logic.getTukimatu(processYM);
					// ②、共通関数「f_cnvyymmdd 」を呼び出す
					String formatDate = nds3GkFunc01Logic.getCnvYymmdd(lastDate);
					processYM = formatDate;
					
					// ②、上記2.4.1.4で取得したサービス種類コードが'A1'、"A5"、"35"の場合
					int insuredPerson = CommonConstants.INT_0;
					if (CommonConstants.SV_KIND_CD_A1.equals(BaseOutEntityList.getFirst().getSvtype()) || CommonConstants.SV_KIND_CD_A5.equals(BaseOutEntityList.getFirst().getSvtype())
							|| CommonConstants.SV_KIND_CD_35.equals(BaseOutEntityList.getFirst().getSvtype())) {
						// 変数.保険者 = 0
						insuredPerson = CommonConstants.INT_0;	
					} else {
						// 変数.保険者　=　上記2.4.1.3で取得した保険者ID
						insuredPerson = svKrdOut.getFirst().getKHokenCd();
					}
					
					// ④、項目コードの取得を行う
					CmnGetScodeFromItemuseSougouByCriteriaInEntity CmnGetScodeInEntity = new CmnGetScodeFromItemuseSougouByCriteriaInEntity();
					// サービス種類コード
					CmnGetScodeInEntity.setAsSvtype(BaseOutEntityList.getFirst().getSvtype());
					// 変数.サービスコード
					CmnGetScodeInEntity.setAsSvcode(svCode);
					// 适用日
					CmnGetScodeInEntity.setLsGetumatuYmd(processYM);
					// 保険者ID
					CmnGetScodeInEntity.setLlKHokenCd(CommonDtoUtil.objValToString(insuredPerson));
					// 有効期間ID
					CmnGetScodeInEntity.setAlTermid(termidFr);

					List<CmnGetScodeFromItemuseSougouOutEntity> CmnGetOut = comMhcItemuseSougouSelectMapper
							.findCmnGetScodeFromItemuseSougouByCriteria(CmnGetScodeInEntity);

					// ⑤、上記取得した項目コードが空、または0の場合
					if (CollectionUtils.isNotEmpty(CmnGetOut)) {
						CmnGetScodeFromItemuseSougouOutEntity CmnGetInfo = CmnGetOut.getFirst();
						if (CommonConstants.STR_0.equals(CmnGetInfo.getScode()) || CommonConstants.BLANK_STRING.equals(CmnGetInfo.getScode()) ) {
							itemCd = CommonConstants.STR_0;
						}else {
							// ⑥、上記以外の場合
							itemCd = CmnGetOut.getFirst().getScode();
						}
					} else {
						itemCd = CommonConstants.STR_0;
					}
				}	
			}

			// 2.4.2 返却情報.サービス種類CD = "11" 且つ 加算フラグ＜＞1 の場合
			if (CommonConstants.SV_KIND_CD_11.equals(svShuruiCd) && CommonConstants.KASAN_FLG_1.equals(kasanFlg)) {
				// 引き渡しパラメータ.該当有効期間ID=10の場合
				if (CommonConstants.TERM_ID_10.equals(termidTo)) {
					// 共通関数「uf_sv_cnv_itemcode」を呼び出し、取込サービス（同期間）が登録否を判断する
					SvItemcodeOutDto svOut = kghCmpSvCnvLogic.cnvSvItemcode(CommonDtoUtil.strValToInt(svJigyoId),
							CommonDtoUtil.strValToInt(termidFr), CommonDtoUtil.strValToInt(svItemCd), null);
					if (svOut.getResult() == CommonConstants.INT_1) {
						itemCd = CommonDtoUtil.objValToString(svOut.getResult());
					} else {
						itemCd = CommonConstants.STR_ZERO;
					}
				}
			}
		}

		svItemCd = itemCd;
		out.setKoumokuCd(svItemCd);
		return out;
	}

	/**
	 * 2.3-2.1 引き渡しパラメータ.取込元有効期間IDが 12 の場合 2.3-2.2 引き渡しパラメータ.取込元有効期間IDが 13 の場合
	 * 
	 * 
	 * @param termidFr   引き渡しパラメータ.取込元有効期間ID
	 * @param termidTo   引き渡しパラメータ.取込先有効期間ID
	 * @param copySakiYm 引き渡しパラメータ.複写先年月
	 * @param svShuruiCd 引き渡しパラメータ.サービス種類CD
	 * @param itemCd     変数.サービス項目ID
	 * 
	 * @return 変数.対象
	 */
	private boolean targetTermChk(String termidFr, String termidTo, String copySakiYm, String svShuruiCd,
			String itemCd) {
		// 2.3-2.1 引き渡しパラメータ.取込元有効期間IDが 12 の場合
		if (CommonConstants.NUMBER_TWELVE.equals(CommonDtoUtil.strValToInt(termidFr))) {
			// (1)
			// 引き渡しパラメータ.★サービス種類コード→4-5が"11","12","13","14","15","16","21","22","23","24","25","26","27","28","2A","2B","31","32","33","34","35","36","37","38","39",
			// 62,"63","64","66","68","69","71","72","73","74","75","76","77","78","79" の場合
			if (CommonConstants.SVTYPE_SUB_LIST_1.contains(svShuruiCd)) {
				ScodeByCriteriaInEntity scodeInEntity = new ScodeByCriteriaInEntity();
				// 項目コード 変数.サービス項目ID
				scodeInEntity.setAlItm(itemCd);
				// 有効期間ID 引き渡しパラメータ.取込先有効期間ID
				scodeInEntity.setLlTerm(termidTo);
				List<ScodeOutEntity> scodeOutList = comMhcItemSelectMapper.findScodeByCriteria(scodeInEntity);
				// ① 上記処理で取得したOUTPUT情報.項目識別コードが空白の場合、変数.対象がfalseで設定し次の処理2.3-3へ
				if (CollectionUtils.isEmpty(scodeOutList) || StringUtils.isEmpty(scodeOutList.getFirst().getScode())) {
					return false;
				}

				// ② 上乗せ加算の場合Trueを返す
				Integer tokureOut = kghCmnF01Logic.isKansenTokureiKasan(copySakiYm.concat(CommonConstants.MONTHSTART),
						scodeOutList.getFirst().getScode());
				// ・ 上記処理で取得したOUTPUT情報.判定結果が1の場合、変数.対象がtrueで設定し次の処理2.3-3へ
				if (CommonConstants.KANSEN_FLG_1.equals(tokureOut)) {
					return true;
				}

				// ③ 生活行為向上リハビリテーション実施加算の場合Trueを返す
				// ・ 変数.項目識別コード ＝ 上記処理で取得したOUTPUT情報.項目識別コードの3桁から4桁を取る
				String chgScode = StringUtils.substring(scodeOutList.getFirst().getScode(), 2, 6);
				// ・ 引き渡しパラメータ.★サービス種類コード→4-5が 16,66 且つ 変数.項目識別コードが 6255,6256 の場合
				if (CommonConstants.TERMID_LIST_1.contains(svShuruiCd)
						&& CommonConstants.SUB_SCODE_LIST_1.contains(chgScode)) {
					// 変数.対象がtrueで設定し次の処理2.3-3へ
					return true;
				}

				// ④ 処遇改善加算Ⅳ、Ⅴの場合Trueを返す
				Integer kaizenFlg = kghCmnF01Logic.isShoguuKaizenKasan(copySakiYm.concat(CommonConstants.MONTHSTART),
						scodeOutList.getFirst().getScode());
				// ・ 上記処理で取得したOUTPUT情報.判定結果が4,5の場合、変数.対象がtrueで設定し次の処理2.3-3へ
				if (CommonConstants.NUMBER_4.equals(kaizenFlg) || CommonConstants.NUMBER_5.equals(kaizenFlg)) {
					return true;
				}

				// ⑤ １２月超減算の場合Trueを返す
				// ・ 引き渡しパラメータ.★サービス種類コード→4-5が 63,64 且つ 変数.項目識別コードが 6123 の場合
				if (CommonConstants.TERMID_LIST_2.contains(svShuruiCd)
						&& CommonConstants.SUB_SCODE_STR_1.equals(chgScode)) {
					// 変数.対象がtrueで設定し次の処理2.3-3へ
					return true;
				}
				// ・ 引き渡しパラメータ.取込元有効期間IDが 66 且つ 変数.項目識別コードが 6123,6124,6125,6126,6127,6128 の場合
				else if (CommonConstants.STR_66.equals(termidFr)
						&& CommonConstants.SUB_SCODE_LIST_2.contains(chgScode)) {
					// 変数.対象がtrueで設定し次の処理2.3-3へ
					return true;
				}
			}
			// (2) 変数.対象がfalseで設定し次の処理2.3-3へ
			return false;
		}
		// 2.3-2.2 引き渡しパラメータ.取込元有効期間IDが 13 の場合
		else if (CommonConstants.GROUP_NUM_13.equals(CommonDtoUtil.strValToInt(termidFr))) {
			// (1) 引き渡しパラメータ.★サービス種類コード→4-5が 15,78 の場合
			if (CommonConstants.SVTYPE_SUB_LIST_2.contains(svShuruiCd)) {
				ScodeByCriteriaInEntity scodeInEntity = new ScodeByCriteriaInEntity();
				// 項目コード 変数.サービス項目ID
				scodeInEntity.setAlItm(itemCd);
				// 有効期間ID 引き渡しパラメータ.取込先有効期間ID
				scodeInEntity.setLlTerm(termidTo);
				// 項目識別コード
				List<ScodeOutEntity> scodeList = comMhcItemSelectMapper.findScodeByCriteria(scodeInEntity);

				// ① 上記処理で取得したOUTPUT情報.項目識別コードが空白の場合、変数.対象がfalseで設定し次の処理2.3-3へ
				if (CollectionUtils.isEmpty(scodeList) || StringUtils.isEmpty(scodeList.getFirst().getScode())) {
					return false;
				}

				// (2) ＡＤＬ維持等加算Ⅲの場合Trueを返す
				// ・ 変数.項目識別コード ＝ 上記処理で取得したOUTPUT情報.項目識別コードの3桁から4桁を取る
				String chgScode = StringUtils.substring(scodeList.getFirst().getScode(), 2, 6);
				// 変数.項目識別コードが 6340 の場合
				if (CommonConstants.SUB_SCODE_STR_2.equals(chgScode)) {
					// 変数.対象がtrueで設定し次の処理2.3-3へ
					return true;
				}
			}
			// (3) 変数.対象がfalseで設定し次の処理2.3-3へ
			return false;
		}
		// 2.3-2.3 引き渡しパラメータ.取込元有効期間IDが 16 の場合
		else if (CommonConstants.GROUP_NUM_16.equals(CommonDtoUtil.strValToInt(termidFr))) {
			// (1) 引き渡しパラメータ.★サービス種類コード→4-5が 22,25,2A,2B の場合
			if (CommonConstants.SCODE_LEFT_TWO.contains(svShuruiCd)) {
				ScodeByCriteriaInEntity scodeInEntity = new ScodeByCriteriaInEntity();
				// 項目コード 変数.サービス項目ID
				scodeInEntity.setAlItm(itemCd);
				// 有効期間ID 引き渡しパラメータ.取込先有効期間ID
				scodeInEntity.setLlTerm(termidTo);
				// 項目識別コード
				List<ScodeOutEntity> scodeList = comMhcItemSelectMapper.findScodeByCriteria(scodeInEntity);
				// ① 上記処理で取得したOUTPUT情報.項目識別コードが空白の場合、変数.対象がfalseで設定し次の処理2.3-3へ
				if (CollectionUtils.isEmpty(scodeList) || StringUtils.isEmpty(scodeList.getFirst().getScode())) {
					return false;
				}
				// (2) 6160:室料相当額控除の場合Trueを返す
				// ・ 上記処理で取得したOUTPUT情報.項目識別コードの3桁目から4桁の値が"6160"の場合、
				String chgScode = StringUtils.substring(scodeList.getFirst().getScode(), 2, 6);
				// 変数.項目識別コードが 6160 の場合
				if (CommonConstants.SUB_SCODE_STR_3.equals(chgScode)) {
					// 変数.対象がtrueで設定し次の処理2.3-3へ
					return true;
				}
			}
			// 変数.対象がfalseで設定し次の処理2.3-3へ
			return false;
		}
		return false;
	}
}
