package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00773DiagnosisInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.GUI00773SubInfoI2;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentInterRAII2InitSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentInterRAII2InitSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssessmentDiseaseDiagnosisInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssessmentDiseaseDiagnosisInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssessmentDiseaseInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AssessmentDiseaseInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.AssessmentDiseaseDiagnosisInfoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.AssessmentDiseaseInfoSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.04.14
 * <AUTHOR>
 * @apiNote GUI00773_アセスメント(インターライ)画面I-2
 */
@Service
public class AssessmentInterRAII2InitSelectServiceImpl extends
        SelectServiceImpl<AssessmentInterRAII2InitSelectServiceInDto, AssessmentInterRAII2InitSelectServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** アセスメント_I疾患情報取得 */
    @Autowired
    private AssessmentDiseaseInfoSelectMapper assessmentDiseaseInfoSelectMapper;

    /** アセスメント_I疾患（診断）情報取得 */
    @Autowired
    private AssessmentDiseaseDiagnosisInfoSelectMapper assessmentDiseaseDiagnosisInfoSelectMapper;

    /**
     * アセスメント(インターライ)画面I-2の初期処理する。
     * 
     * @param inDto 初期処理サービス入力Dto
     * @return 初期処理の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected AssessmentInterRAII2InitSelectServiceOutDto mainProcess(AssessmentInterRAII2InitSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);

        // 戻り情報
        AssessmentInterRAII2InitSelectServiceOutDto out = new AssessmentInterRAII2InitSelectServiceOutDto();

        // サブ情報（I2）
        GUI00773SubInfoI2 subInfoI2 = new GUI00773SubInfoI2();

        // 疾患（診断）情報リスト
        List<GUI00773DiagnosisInfo> diagnosisInfoList = new ArrayList<GUI00773DiagnosisInfo>();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. アセスメント_I疾患情報取得。===============
         * 
         */
        // 2.1. 下記のアセスメント_I疾患情報取得のDAOを利用し、サブ情報（I2）を取得する。
        // DAOパラメータを作成
        AssessmentDiseaseInfoByCriteriaInEntity assessmentDiseaseInfoByCriteriaInEntity = new AssessmentDiseaseInfoByCriteriaInEntity();
        // アセスメントID
        assessmentDiseaseInfoByCriteriaInEntity.setAnRaiId(CommonDtoUtil.strValToInt(inDto.getRaiId()));
        // DAOを実行
        List<AssessmentDiseaseInfoOutEntity> assessmentDiseaseInfoList = this.assessmentDiseaseInfoSelectMapper
                .findAssessmentDiseaseInfoByCriteria(assessmentDiseaseInfoByCriteriaInEntity);

        // 取得したサブ情報（I2）を保持
        if (CollectionUtils.isNotEmpty(assessmentDiseaseInfoList)) {
            subInfoI2 = this.setSubInfoI2(assessmentDiseaseInfoList.get(0));
        }

        /*
         * ===============3. アセスメント_I疾患（診断）情報取得===============
         * 
         */
        // 3.1. 下記のアセスメント_I疾患（診断）情報取得のDAOを利用し、疾患（診断）情報リストを取得する。
        // DAOパラメータを作成
        AssessmentDiseaseDiagnosisInfoByCriteriaInEntity assessmentDiseaseDiagnosisInfoByCriteriaInEntity = new AssessmentDiseaseDiagnosisInfoByCriteriaInEntity();

        // アセスメントID
        assessmentDiseaseDiagnosisInfoByCriteriaInEntity.setAnRaiId(CommonDtoUtil.strValToInt(inDto.getRaiId()));
        // DAOを実行
        List<AssessmentDiseaseDiagnosisInfoOutEntity> assessmentDiseaseDiagnosisInfoList = this.assessmentDiseaseDiagnosisInfoSelectMapper
                .findAssessmentDiseaseDiagnosisInfoByCriteria(
                        assessmentDiseaseDiagnosisInfoByCriteriaInEntity);

        // アセスメント_I疾患（診断）情報取得を保持
        if (CollectionUtils.isNotEmpty(assessmentDiseaseDiagnosisInfoList)) {
            for (int i = 0; i < assessmentDiseaseDiagnosisInfoList.size(); i++) {
                GUI00773DiagnosisInfo diagnosisInfo = new GUI00773DiagnosisInfo();
                diagnosisInfo = this.setDiagnosisInfo(assessmentDiseaseDiagnosisInfoList.get(i));
                diagnosisInfoList.add(diagnosisInfo);
            }
        }

        // サブ情報（I2）
        out.setSubInfoI2(subInfoI2);
        // 疾患（診断）情報リスト
        out.setDiagnosisInfoList(diagnosisInfoList);

        LOG.info(Constants.END);
        return out;
    }

    /**
     * サブ情報（I2）を設定する
     * 
     * @param assessmentDiseaseInfo アセスメント_I疾患情報
     * @return サブ情報（I2）
     */
    private GUI00773SubInfoI2 setSubInfoI2(AssessmentDiseaseInfoOutEntity assessmentDiseaseInfo) {
        // サブ情報（I2）
        GUI00773SubInfoI2 subInfoI2 = new GUI00773SubInfoI2();

        // アセスメントID */
        subInfoI2.setRaiId(CommonDtoUtil.objValToString(assessmentDiseaseInfo.getRaiId()));
        // i2_メモ */
        subInfoI2.setI2MemoKnj(assessmentDiseaseInfo.getI2MemoKnj());
        // i2_メモフォント */
        subInfoI2.setI2MemoFont(CommonDtoUtil.objValToString(assessmentDiseaseInfo.getI2MemoFont()));
        // i2_メモ色 */
        subInfoI2.setI2MemoColor(CommonDtoUtil.objValToString(assessmentDiseaseInfo.getI2MemoColor()));

        return subInfoI2;

    }

    /**
     * 疾患（診断）情報を設定する
     * 
     * @param assessmentDiseaseDiagnosisInfo アセスメント_I疾患（診断）情報
     * @return 疾患（診断）情報
     */
    private GUI00773DiagnosisInfo setDiagnosisInfo(
            AssessmentDiseaseDiagnosisInfoOutEntity assessmentDiseaseDiagnosisInfo) {
        GUI00773DiagnosisInfo diagnosisInfo = new GUI00773DiagnosisInfo();

        // ID */
        diagnosisInfo.setId(CommonDtoUtil.objValToString(assessmentDiseaseDiagnosisInfo.getId()));
        // 表示順 */
        diagnosisInfo.setSort(CommonDtoUtil.objValToString(assessmentDiseaseDiagnosisInfo.getSort()));
        // 診断名 */
        diagnosisInfo.setShindanKnj(assessmentDiseaseDiagnosisInfo.getShindanKnj());
        // 疾患コード */
        diagnosisInfo.setShikkanCd(CommonDtoUtil.objValToString(assessmentDiseaseDiagnosisInfo.getShikkanCd()));
        // ICD-CMコード1 */
        diagnosisInfo.setIcdCmCode1(assessmentDiseaseDiagnosisInfo.getIcdCmCode1());
        // ICD-CMコード2 */
        diagnosisInfo.setIcdCmCode2(assessmentDiseaseDiagnosisInfo.getIcdCmCode2());

        return diagnosisInfo;

    }

}
