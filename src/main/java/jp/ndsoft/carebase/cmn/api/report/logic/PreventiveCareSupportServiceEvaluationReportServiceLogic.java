package jp.ndsoft.carebase.cmn.api.report.logic;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.E00600Hs;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.E00600PreventiveCareSupportServiceEvaluationReport;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.E00600Se;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonDateParts;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonShiTeiDateParts;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZIcmComLogic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkBase02Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.JigyoRirekiInfoDto;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.PreventiveCareSupportServiceEvaluationReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.model.PreventiveCareSupportServiceEvaluationReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPrnPreSrw2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KycHyk1DsByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KycHyk1DsOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KycHyk2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KycHyk2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YoushikiNoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YoushikiNoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocPrtYoushikiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMstChouhyouInkanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KycTucHyouka1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KycTucHyouka2SelectMapper;

/**
 * @since 2025.08.11
 * <AUTHOR> 張奇
 * @description E00600_介護予防支援-サービス評価表 帳票出力
 */
@Component
public class PreventiveCareSupportServiceEvaluationReportServiceLogic {

    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** Nds3GkBase02Logicロジッククラス */
    @Autowired
    Nds3GkBase02Logic nds3GkBase02Logic;

    /** KghKrkZIcmComLogicロジッククラス */
    @Autowired
    KghKrkZIcmComLogic kghKrkZIcmComLogic;

    /** KghCmnF01Logicロジッククラス */
    @Autowired
    KghCmnF01Logic kghCmnF01Logic;

    /** KghCmpF01Logicクラス */
    @Autowired
    KghCmpF01Logic kghCmpF01Logic;

    /** モニタリング・評価表（H21改訂版）のヘッダ情報取得 */
    @Autowired
    KycTucHyouka1SelectMapper kycTucHyouka1SelectMapper;

    /** 03-01 職員基本情報取得 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /** 利用者の情報取得 */
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;

    /** ケアプラン帳票印鑑欄情報取得 */
    @Autowired
    private CpnMstChouhyouInkanSelectMapper cpnMstChouhyouInkanSelectMapper;

    /** 事業所名取得関数 */
    @Autowired
    private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;

    /** 文書番号情報取得 */
    @Autowired
    private ComMocPrtYoushikiSelectMapper comMocPrtYoushikiSelectMapper;

    /** 文書番号情報取得 */
    @Autowired
    private KycTucHyouka2SelectMapper kycTucHyouka2SelectMapper;

    /**
     * 介護予防支援-サービス評価表の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public PreventiveCareSupportServiceEvaluationReportServiceInDto getPreventiveCareSupportServiceEvaluationParameters(
            PreventiveCareSupportServiceEvaluationReportParameterModel inDto)
            throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 帳票用データ詳細
        PreventiveCareSupportServiceEvaluationReportServiceInDto infoInDto = new PreventiveCareSupportServiceEvaluationReportServiceInDto();

        List<KycHyk2OutEntity> kycHyk2OutEntityList = new ArrayList<>();
        // 2. リクエストパラメータ.印刷オプション.記入用シートを印刷するフラグ = true の場合、結果レスポンスを返却する。
        // リクエストパラメータ.印刷オプション.記入用シートを印刷するフラグ = false の場合、介護予防支援-サービス評価表情報のを取得する
        if (CommonConstants.STR_FALSE.equals(inDto.getPrintOption().getEmptyFlg())) {
            // 2.1. 下記の28-04 サービス評価表情報取得のDAOを利用し、介護予防支援-サービス評価表情報を取得する。
            KycHyk2ByCriteriaInEntity kycHyk2ByCriteriaInEntity = new KycHyk2ByCriteriaInEntity();
            // ヘッダID
            kycHyk2ByCriteriaInEntity
                    .setHyouka1(CommonDtoUtil.strValToInt(inDto.getPrintSubjectHistoryList().get(0).getRirekiId()));

            kycHyk2OutEntityList = kycTucHyouka2SelectMapper.findKycHyk2ByCriteria(kycHyk2ByCriteriaInEntity);

            // 2.2. 「2.1.」で取得した介護予防支援-サービス評価表情報の件数をワーク介護予防支援-サービス評価表総件数に設定する。
            // 2.2.1. 総件数 > 0 件の場合。
            // 処理を続ける。
            // 2.2.2. 上記以外の場合。
            // 処理を中断する。
            if (CollectionUtils.isNullOrEmpty(kycHyk2OutEntityList)) {
                return infoInDto;
            }
        }

        // 3. ヘッダ情報を取得する。
        // 3.1. サービス評価表ヘッダ情報の取得
        // 下記の28-04 サービス評価表ヘッダのDAOを利用し、介護予防支援-サービス評価表のヘッダ情報を取得する。
        KycHyk1DsByCriteriaInEntity kycHyk1DsByCriteriaInEntity = new KycHyk1DsByCriteriaInEntity();
        // ヘッダID
        kycHyk1DsByCriteriaInEntity
                .setHyouka1Id(CommonDtoUtil.strValToInt(inDto.getPrintSubjectHistoryList().get(0).getRirekiId()));

        List<KycHyk1DsOutEntity> kycHyk1DsOutEntityList = kycTucHyouka1SelectMapper
                .findKycHyk1DsByCriteria(kycHyk1DsByCriteriaInEntity);

        // 3.2. 利用者基本情報の取得
        // 下記の利用者基本（１－６）情報取得のDAOを利用し、利用者の情報を取得する。
        // 利用者（年齢以外）情報取得のINPUT情報初期化
        RiyoshaKihonByCriteriaInEntity riyoshaKihonByCriteriaInEntity = new RiyoshaKihonByCriteriaInEntity();
        // 利用者ID←リクエストパラメータ.データ.印刷対象履歴リスト[0].利用者ID
        riyoshaKihonByCriteriaInEntity
                .setAlUserid(CommonDtoUtil.strValToInt(inDto.getPrintSubjectHistoryList().get(0).getUserId()));
        // 下記の利用者（年齢以外）情報取得のDAOを利用し、利用者（年齢以外）情報を取得する。
        List<RiyoshaKihonOutEntity> riyoshaKihonOutEntityList = comTucUserSelectMapper
                .findRiyoshaKihonByCriteria(riyoshaKihonByCriteriaInEntity);

        // 変数.利用者名
        String shokuKnj = CommonConstants.BLANK_STRING;
        if (!CollectionUtils.isNullOrEmpty(riyoshaKihonOutEntityList)) {
            // 氏名（姓）
            String name1Knj = ReportUtil.nullToEmpty(riyoshaKihonOutEntityList.get(0).getName1Knj());
            // 氏名（名）
            String name2Knj = ReportUtil.nullToEmpty(riyoshaKihonOutEntityList.get(0).getName2Knj());
            shokuKnj = (name1Knj + CommonConstants.BLANK_SPACE + name2Knj).trim();
        }

        // 3.3.作成者名の取得
        List<KghCpnRaiMonKentPrnPreSrw2OutEntity> srwOutList = new ArrayList<>();
        // 「3.1」で取得した作成者＞0の場合、下記の03-01 職員基本情報取得のDAOを利用し、作成者名の情報を取得する。
        if (!CollectionUtils.isNullOrEmpty(kycHyk1DsOutEntityList)
                && Objects.nonNull(kycHyk1DsOutEntityList.get(0).getShokuId())
                && kycHyk1DsOutEntityList.get(0).getShokuId() > CommonConstants.INT_0) {
            KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity srwInEntity = new KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity();
            // 職員ID 「3.1」で取得した作成者
            srwInEntity.setLlTmp(CommonDtoUtil.objValToString(kycHyk1DsOutEntityList.get(0).getShokuId()));
            // 03-01 職員基本情報取得
            srwOutList = comMscShokuinSelectMapper
                    .findKghCpnRaiMonKentPrnPreSrw2ByCriteria(srwInEntity);
        }

        // 4. 印鑑欄設定情報を取得する。
        // リクエストパラメータ.データ.印刷オプション.印鑑欄を表示するフラグ = true
        // とリクエストパラメータ.記入用シートを印刷するフラグ = falseの場合、印鑑欄設定情報の取得
        List<KghCpnMstChouhyouInkanPrnOutEntity> chouOutList = new ArrayList<>();
        // 下記の28-xx ケアプラン帳票印鑑欄情報取得のDAOを利用し、印鑑欄設定情報を取得する。
        if (CommonConstants.STR_FALSE.equals(inDto.getPrintOption().getEmptyFlg())) {
            KghCpnMstChouhyouInkanPrnByCriteriaInEntity prnInRntity = new KghCpnMstChouhyouInkanPrnByCriteriaInEntity();
            // 法人ID リクエストパラメータ.法人ID
            prnInRntity.setAnKey1(CommonDtoUtil.strValToInt(inDto.getJigyoInfo().getHoujinId()));
            // 施設ID リクエストパラメータ.施設ID
            prnInRntity.setAnKey2(CommonDtoUtil.strValToInt(inDto.getJigyoInfo().getShisetuId()));
            // 事業所ID リクエストパラメータ.事業所ID
            prnInRntity.setAnKey3(CommonDtoUtil.strValToInt(inDto.getJigyoInfo().getSvJigyoId()));
            // 帳票セクション番号 リクエストパラメータ.データ.出力帳票印刷情報リスト[0].セクション
            prnInRntity.setAsSec(inDto.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection());
            // 印鑑欄設定情報取得
            chouOutList = cpnMstChouhyouInkanSelectMapper
                    .findKghCpnMstChouhyouInkanPrnByCriteria(prnInRntity);
        }

        // 6. 上記編集した帳票用データを設定する。
        E00600PreventiveCareSupportServiceEvaluationReport preventiveCare = new E00600PreventiveCareSupportServiceEvaluationReport();

        // 帳票タイトル
        // ・リクエストパラメータ.データ.印刷オプション.タイトルを変更するフラグ = true の場合、
        if (CommonConstants.STR_TRUE.equals(inDto.getPrintOption().getTitleFlg())) {
            // タイトル=リクエストパラメータ.データ.印刷オプション.変更タイトル
            preventiveCare.setTitle(inDto.getPrintOption().getHenkouTitle());
            // ・上記以外の場合、
        } else {
            // タイトル="介護予防支援-サービス評価表"
            preventiveCare.setTitle(ReportConstants.STR_PREVENTIVECARE);
        }

        // 帳票タイトルフォント
        // ・帳票用データ詳細.データ.帳票タイトルの長さ＞60の場合、
        if (StringUtils.isNotBlank(preventiveCare.getTitle())
                && preventiveCare.getTitle().length() > CommonConstants.INT_60) {
            // 帳票タイトルフォント="12"
            preventiveCare.setTitleFont(CommonConstants.STR_12);
            // ・上記以外の場合、
        } else {
            // 帳票タイトルフォント="14"
            preventiveCare.setTitleFont(CommonConstants.STR_14);
        }

        // 指定日印刷区分
        preventiveCare.setShiTeiKubun(CommonDtoUtil.strValToInt(inDto.getPrintSet().getShiTeiKubun()));

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ
        if (CommonConstants.STR_TRUE.equals(inDto.getPrintOption().getEmptyFlg())) {
            preventiveCare.setEmptyFlg(true);
        } else {
            preventiveCare.setEmptyFlg(false);
        }

        // 承認欄印刷区分
        preventiveCare.setShoninKubun(CommonDtoUtil.strValToInt(inDto.getPrintOption().getShoninFlg()));

        // 敬称
        String keisho = CommonConstants.BLANK_STRING;
        // ・リクエストパラメータ.データ.印刷オプション.敬称を変更するフラグ = false の場合
        if (CommonConstants.STR_FALSE.equals(inDto.getPrintOption().getKeishoFlg())) {
            // ・リクエストパラメータ.データ.システム設定敬称を変更するフラグ = false の場合
            if (CommonConstants.STR_FALSE.equals(inDto.getStKeishoFlg())) {
                // 敬称 = "殿"
                keisho = ReportConstants.KEISHO_TONO;
                // ・リクエストパラメータ.データ.システム設定敬称を変更するフラグ = true の場合
            } else {
                // 敬称 = リクエストパラメータ.データ.システム設定敬称の内容
                keisho = ReportUtil.nullToEmpty(inDto.getStKeisho());
            }
            // ・リクエストパラメータ.データ.印刷オプション.敬称を変更するフラグ = true の場合
        } else {
            // 敬称 = リクエストパラメータ.データ.印刷オプション.敬称の内容
            keisho = ReportUtil.nullToEmpty(inDto.getPrintOption().getKeishoKnj());
        }
        preventiveCare.setKeisho(keisho);

        // リクエストパラメータ.データ.印刷オプション.記入用シートを印刷するフラグ = true の場合、
        if (CommonConstants.STR_TRUE.equals(inDto.getPrintOption().getEmptyFlg())) {
            // 指定日（年号）
            preventiveCare.setShiTeiDateGG(CommonConstants.FULL_WIDTH_SPACE);
            // 指定日（年）
            preventiveCare.setShiTeiDateYY(CommonConstants.FULL_WIDTH_SPACE);
            // 指定日（月）
            preventiveCare.setShiTeiDateMM(CommonConstants.FULL_WIDTH_SPACE);
            // 指定日（日）
            preventiveCare.setShiTeiDateDD(CommonConstants.FULL_WIDTH_SPACE);

            // 事業所名
            preventiveCare.setJigyosha(CommonConstants.BLANK_STRING);

            // 利用者氏名
            preventiveCare.setUserName(CommonConstants.BLANK_STRING);

            // 評価日（年号）
            preventiveCare.setHyoukaGG(CommonConstants.FULL_WIDTH_SPACE + CommonConstants.FULL_WIDTH_SPACE);
            // 評価日（年）
            preventiveCare.setHyoukaYY(CommonConstants.FULL_WIDTH_SPACE);
            // 評価日（月）
            preventiveCare.setHyoukaMM(CommonConstants.FULL_WIDTH_SPACE);
            // 評価日（日）
            preventiveCare.setHyoukaDD(CommonConstants.FULL_WIDTH_SPACE);

            // 計画作成者名
            preventiveCare.setShokuName(CommonConstants.BLANK_STRING);

            // 印鑑欄印刷区分
            preventiveCare.setInkanKubun(CommonConstants.INT_0);

            // 印鑑1
            preventiveCare.setChouhyouInkan1(CommonConstants.BLANK_STRING);
            // 印鑑2
            preventiveCare.setChouhyouInkan2(CommonConstants.BLANK_STRING);
            // 印鑑3
            preventiveCare.setChouhyouInkan3(CommonConstants.BLANK_STRING);
            // 印鑑4
            preventiveCare.setChouhyouInkan4(CommonConstants.BLANK_STRING);
            // 印鑑5
            preventiveCare.setChouhyouInkan5(CommonConstants.BLANK_STRING);
            // 印鑑6
            preventiveCare.setChouhyouInkan6(CommonConstants.BLANK_STRING);
            // 印鑑7
            preventiveCare.setChouhyouInkan7(CommonConstants.BLANK_STRING);
            // 印鑑8
            preventiveCare.setChouhyouInkan8(CommonConstants.BLANK_STRING);
            // 印鑑9
            preventiveCare.setChouhyouInkan9(CommonConstants.BLANK_STRING);
            // 印鑑10
            preventiveCare.setChouhyouInkan10(CommonConstants.BLANK_STRING);
            // 印鑑11
            preventiveCare.setChouhyouInkan11(CommonConstants.BLANK_STRING);
            // 印鑑12
            preventiveCare.setChouhyouInkan12(CommonConstants.BLANK_STRING);
            // 印鑑13
            preventiveCare.setChouhyouInkan13(CommonConstants.BLANK_STRING);
            // 印鑑14
            preventiveCare.setChouhyouInkan14(CommonConstants.BLANK_STRING);
            // 印鑑15
            preventiveCare.setChouhyouInkan15(CommonConstants.BLANK_STRING);

            List<E00600Hs> hsList = new ArrayList<>();
            E00600Hs hs = new E00600Hs();
            String text = CommonConstants.BLANK_STRING;
            for (int i = 0; i < CommonConstants.INT_5; i++) {
                text += ReportConstants.LINE_BREAKS;
            }
            // 総合的な方針
            hs.setSouHousinKnj(text);

            // 介護予防支援事業者意見
            hs.setIkenKnj(text);

            // 予定内容1
            hs.setYotei1Knj(text);

            // 予定内容2
            hs.setYotei2Knj(text);

            // 予定内容3
            hs.setYotei3Knj(text);

            // 対象事業内容1
            hs.setJigyo1Knj(text);

            // 対象事業内容2
            hs.setJigyo2Knj(text);
            // ・リクエストパラメータ.印刷オプション.H27年様式で印刷するフラグ＝false(印刷しない)の場合、
            if (CommonConstants.STR_FALSE.equals(inDto.getPrintOption().getH27Flg())) {
                // 対象事業内容3＝"□ 介護予防特定高齢者施策"
                hs.setJigyo3Knj(ReportConstants.STR_OFF_ASSESSMENT_YOBO);
                // ・上記以外の場合、
            } else {
                // 対象事業内容3＝"□ 介護予防・生活支援サービス事業"
                hs.setJigyo3Knj(ReportConstants.STR_OFF_ASSESSMENT_YOBO_SUPPORT);
            }

            // ・リクエストパラメータ.印刷オプション.H27年様式で印刷するフラグ＝false(印刷しない)の場合、
            if (CommonConstants.STR_FALSE.equals(inDto.getPrintOption().getH27Flg())) {
                // 対象事業内容4＝"□ 介護予防一般高齢者施策"
                hs.setJigyo4Knj(ReportConstants.STR_OFF_ASSESSMENT_YOBO_GENERAL);
                // ・上記以外の場合、
            } else {
                // 対象事業内容4＝"□ 一般介護予防事業"
                hs.setJigyo4Knj(ReportConstants.STR_OFF_GENERAL_ASSESSMENT_YOBO);
            }

            // 対象事業内容5
            hs.setJigyo5Knj(ReportConstants.STR_OFF_END);
            hsList.add(hs);
            preventiveCare.setHsList(hsList);

            List<E00600Se> seList = new ArrayList<>();
            E00600Se se = new E00600Se();
            for (int i = 0; i < CommonConstants.INT_30; i++) {
                text += ReportConstants.LINE_BREAKS;
            }
            // 目標番号
            se.setMokuhyoKnj(text);
            // 目標
            se.setKikanKnj(text);
            // 評価期間
            se.setJyoukyouKnj(text);
            // 目標達成状況
            se.setJyoukyouKnj(text);
            // 達成区分内容
            se.setTasseiKnj(text);
            // 原因(本人・家族）
            se.setGeninKnj(text);
            // 原因（計画作成者）
            se.setGenin2Knj(text);
            // 今後の方針
            se.setHousinKnj(text);
            seList.add(se);
            preventiveCare.setSeList(seList);

            // 文書管理番号
            preventiveCare.setBunsyoKanriNo(CommonConstants.BLANK_STRING);

            // 記入用シートを印刷するフラグ = true の以外の場合
        } else {
            if (!CollectionUtils.isNullOrEmpty(kycHyk1DsOutEntityList)) {
                // API定義の処理「3.1.」の作成日=null以外の場合、
                if (StringUtils.isNotBlank(kycHyk1DsOutEntityList.get(0).getHyoukaYmd())) {
                    // 評価日の和暦変換処理
                    String shokaiDate = kghKrkZIcmComLogic.geKycS2wjEz(kycHyk1DsOutEntityList.get(0).getHyoukaYmd(),
                            CommonConstants.NUMBER_1);
                    ReportCommonDateParts shokaiDateParts = this.getDate(shokaiDate);
                    // 評価日（年号）
                    preventiveCare.setHyoukaGG(shokaiDateParts.getDateGG());
                    // 評価日（年）
                    preventiveCare.setHyoukaYY(shokaiDateParts.getDateYY());
                    // 評価日（月）
                    preventiveCare.setHyoukaMM(shokaiDateParts.getDateMM());
                    // 評価日（日）
                    preventiveCare.setHyoukaDD(shokaiDateParts.getDateDD());
                    // 上記以外の場合、
                } else {
                    // 評価日（年号）
                    preventiveCare.setHyoukaGG(CommonConstants.FULL_WIDTH_SPACE + CommonConstants.FULL_WIDTH_SPACE);
                    // 評価日（年）
                    preventiveCare.setHyoukaYY(CommonConstants.FULL_WIDTH_SPACE);
                    // 評価日（月）
                    preventiveCare.setHyoukaMM(CommonConstants.FULL_WIDTH_SPACE);
                    // 評価日（日）
                    preventiveCare.setHyoukaDD(CommonConstants.FULL_WIDTH_SPACE);
                }

                List<E00600Hs> hsList = new ArrayList<>();
                E00600Hs hs = new E00600Hs();
                // 総合的な方針
                hs.setSouHousinKnj(kycHyk1DsOutEntityList.get(0).getSouHousinKnj());

                // 介護予防支援事業者意見
                hs.setIkenKnj(kycHyk1DsOutEntityList.get(0).getIkenKnj());

                // ・API定義の処理[3.1」の予定CD＝1の場合、
                if (CommonConstants.INT_1 == kycHyk1DsOutEntityList.get(0).getYoteiCd()) {
                    // 予定内容1＝"■ プラン継続"
                    hs.setYotei1Knj(ReportConstants.STR_ON_PLAN_CONTINUATION);
                } else {
                    // 予定内容1="□ プラン継続"
                    hs.setYotei1Knj(ReportConstants.STR_OFF_PLAN_CONTINUATION);
                }

                // ・API定義の処理[3.1」の予定CD＝2の場合、
                if (CommonConstants.INT_2 == kycHyk1DsOutEntityList.get(0).getYoteiCd()) {
                    // 予定内容2＝"■ プラン変更"
                    hs.setYotei2Knj(ReportConstants.STR_ON_PLAN_CHANGE);
                } else {
                    // 予定内容2="□ プラン変更"
                    hs.setYotei2Knj(ReportConstants.STR_OFF_PLAN_CHANGE);
                }

                // ・API定義の処理[3.1」の予定CD＝3の場合、
                if (CommonConstants.INT_3 == kycHyk1DsOutEntityList.get(0).getYoteiCd()) {
                    // 予定内容3＝"■ 終了"
                    hs.setYotei3Knj(ReportConstants.STR_ON_END);
                } else {
                    // 予定内容3="□ 終了"
                    hs.setYotei3Knj(ReportConstants.STR_OFF_END);
                }

                // ・API定義の処理[3.1」の対象事業01＝1:選択の場合、
                if (CommonConstants.INT_1 == kycHyk1DsOutEntityList.get(0).getJigyo01()) {
                    // 対象事業内容1＝"■ 介護給付"
                    hs.setJigyo1Knj(ReportConstants.STR_ON_ASSESSMENT);
                } else {
                    // 対象事業内容1="□ 介護給付"
                    hs.setJigyo1Knj(ReportConstants.STR_OFF_ASSESSMENT);
                }

                // ・API定義の処理[3.1」の対象事業02＝1:選択の場合、
                if (CommonConstants.INT_1 == kycHyk1DsOutEntityList.get(0).getJigyo02()) {
                    // 対象事業内容1＝"■ 介護給付"
                    hs.setJigyo2Knj(ReportConstants.STR_ON_ASSESSMENT);
                } else {
                    // 対象事業内容1="□ 介護給付"
                    hs.setJigyo2Knj(ReportConstants.STR_OFF_ASSESSMENT);
                }

                // ・リクエストパラメータ.印刷オプション.H27年様式で印刷するフラグ＝false(印刷しない)の場合、
                if (CommonConstants.STR_FALSE.equals(inDto.getPrintOption().getH27Flg())) {
                    // API定義の処理[3.1」の対象事業03＝1:選択 の場合、
                    if (CommonConstants.INT_1 == kycHyk1DsOutEntityList.get(0).getJigyo03()) {
                        // 対象事業内容3＝"■ 介護予防特定高齢者施策"
                        hs.setJigyo3Knj(ReportConstants.STR_ON_ASSESSMENT_YOBO);
                    } else {
                        // 対象事業内容3＝"□ 介護予防特定高齢者施策"
                        hs.setJigyo3Knj(ReportConstants.STR_OFF_ASSESSMENT_YOBO);
                    }
                    // ・上記以外の場合、
                } else {
                    // API定義の処理[3.1」の対象事業03＝1:選択 の場合、
                    if (CommonConstants.INT_1 == kycHyk1DsOutEntityList.get(0).getJigyo03()) {
                        // 対象事業内容3＝"■ 介護予防・生活支援サービス事業"
                        hs.setJigyo3Knj(ReportConstants.STR_ON_ASSESSMENT_YOBO_SUPPORT);
                    } else {
                        // 対象事業内容3＝"□ 介護予防・生活支援サービス事業"
                        hs.setJigyo3Knj(ReportConstants.STR_OFF_ASSESSMENT_YOBO_SUPPORT);
                    }
                }

                // ・リクエストパラメータ.印刷オプション.H27年様式で印刷するフラグ＝false(印刷しない)の場合、
                if (CommonConstants.STR_FALSE.equals(inDto.getPrintOption().getH27Flg())) {
                    // AAPI定義の処理[3.1」の対象事業04＝1:選択 の場合、
                    if (CommonConstants.INT_1 == kycHyk1DsOutEntityList.get(0).getJigyo04()) {
                        // 対象事業内容4＝"■ 介護予防一般高齢者施策"
                        hs.setJigyo4Knj(ReportConstants.STR_ON_ASSESSMENT_YOBO_GENERAL);
                    } else {
                        // 対象事業内容4＝"□ 介護予防一般高齢者施策"
                        hs.setJigyo4Knj(ReportConstants.STR_OFF_ASSESSMENT_YOBO_GENERAL);
                    }
                    // ・上記以外の場合、
                } else {
                    // API定義の処理[3.1」の対象事業04＝1:選択 の場合、
                    if (CommonConstants.INT_1 == kycHyk1DsOutEntityList.get(0).getJigyo04()) {
                        // 対象事業内容4＝"■ 一般介護予防事業"
                        hs.setJigyo4Knj(ReportConstants.STR_ON_GENERAL_ASSESSMENT_YOBO);
                    } else {
                        // 対象事業内容4＝"□ 一般介護予防事業"
                        hs.setJigyo4Knj(ReportConstants.STR_OFF_GENERAL_ASSESSMENT_YOBO);
                    }
                }

                // ・API定義の処理[3.1」の対象事業05＝1:選択の場合、
                if (CommonConstants.INT_1 == kycHyk1DsOutEntityList.get(0).getJigyo05()) {
                    // 対象事業内容5＝"■ 終了"
                    hs.setJigyo2Knj(ReportConstants.STR_ON_END);
                } else {
                    // 対象事業内容5="□ 終了"
                    hs.setJigyo2Knj(ReportConstants.STR_OFF_END);
                }
                hsList.add(hs);
                preventiveCare.setHsList(hsList);
                // 上記以外の場合、
            } else {
                // 評価日（年号）
                preventiveCare.setHyoukaGG(CommonConstants.FULL_WIDTH_SPACE + CommonConstants.FULL_WIDTH_SPACE);
                // 評価日（年）
                preventiveCare.setHyoukaYY(CommonConstants.FULL_WIDTH_SPACE);
                // 評価日（月）
                preventiveCare.setHyoukaMM(CommonConstants.FULL_WIDTH_SPACE);
                // 評価日（日）
                preventiveCare.setHyoukaDD(CommonConstants.FULL_WIDTH_SPACE);
                List<E00600Hs> hsList = new ArrayList<>();
                E00600Hs hs = new E00600Hs();
                // 総合的な方針
                hs.setSouHousinKnj(CommonConstants.BLANK_STRING);
                // 介護予防支援事業者意見
                hs.setIkenKnj(CommonConstants.BLANK_STRING);
                // 予定内容1
                hs.setYotei1Knj(ReportConstants.STR_OFF_PLAN_CONTINUATION);
                // 予定内容2
                hs.setYotei2Knj(ReportConstants.STR_OFF_PLAN_CHANGE);
                // 予定内容3
                hs.setYotei3Knj(ReportConstants.STR_OFF_END);

                // 対象事業内容1
                hs.setJigyo1Knj(ReportConstants.STR_OFF_ASSESSMENT);

                // 対象事業内容2
                hs.setJigyo2Knj(ReportConstants.STR_OFF_ASSESSMENT);

                // ・リクエストパラメータ.印刷オプション.H27年様式で印刷するフラグ＝false(印刷しない)の場合、
                if (CommonConstants.STR_FALSE.equals(inDto.getPrintOption().getH27Flg())) {
                    // 対象事業内容3＝"□ 介護予防特定高齢者施策"
                    hs.setJigyo3Knj(ReportConstants.STR_OFF_ASSESSMENT_YOBO);
                    // ・上記以外の場合、
                } else {
                    // 対象事業内容3＝"□ 介護予防・生活支援サービス事業"
                    hs.setJigyo3Knj(ReportConstants.STR_OFF_ASSESSMENT_YOBO_SUPPORT);
                }

                // ・リクエストパラメータ.印刷オプション.H27年様式で印刷するフラグ＝false(印刷しない)の場合、
                if (CommonConstants.STR_FALSE.equals(inDto.getPrintOption().getH27Flg())) {
                    // 対象事業内容4＝"□ 介護予防一般高齢者施策"
                    hs.setJigyo4Knj(ReportConstants.STR_OFF_ASSESSMENT_YOBO_GENERAL);
                    // ・上記以外の場合、
                } else {
                    // 対象事業内容4＝"□ 一般介護予防事業"
                    hs.setJigyo4Knj(ReportConstants.STR_OFF_GENERAL_ASSESSMENT_YOBO);
                }

                // 対象事業内容5
                hs.setJigyo5Knj(ReportConstants.STR_OFF_END);
                hsList.add(hs);
                preventiveCare.setHsList(hsList);
            }

            // 1. 指定日の空欄表示処理
            ReportCommonShiTeiDateParts shiTeiDateParts = this.getShiTeiDate(inDto.getPrintSet().getShiTeiKubun(),
                    inDto.getPrintSet().getShiTeiDate(), inDto.getSystemDate());
            // 指定日（年号）
            preventiveCare.setShiTeiDateGG(shiTeiDateParts.getShiTeiDateGG());
            // 指定日（年）
            preventiveCare.setShiTeiDateYY(shiTeiDateParts.getShiTeiDateYY());
            // 指定日（月）
            preventiveCare.setShiTeiDateMM(shiTeiDateParts.getShiTeiDateMM());
            // 指定日（日）
            preventiveCare.setShiTeiDateDD(shiTeiDateParts.getShiTeiDateDD());

            // 事業所名
            // 事業所名情報を取得する。
            JigyoRirekiInfoDto cpnFuncOutInfo = kghKrkZCpnFuncLogic.getJigyoRirekiKnj(
                    CommonDtoUtil.strValToInt(inDto.getJigyoInfo().getSvJigyoId()), inDto.getAppYmd());
            // 事業者名=共通関数補足の処理「4.」の事業所略称
            preventiveCare.setJigyosha(cpnFuncOutInfo.getJigyoRirekiRyakuKnj());

            // 利用者氏名＝API定義の処理「3.1」の氏名（姓）+ " " + API定義の処理「3.1」の氏名（名）
            preventiveCare.setUserName(shokuKnj);

            // 計画作成者名
            // API定義の処理「3.1」で取得したリストが空白の場合、
            if (CollectionUtils.isNullOrEmpty(kycHyk1DsOutEntityList)) {
                preventiveCare.setShokuName(CommonConstants.BLANK_STRING);
                // ・上記以外の場合、
            } else {
                // ・API定義の処理「3.1」で取得した作成者＞0の場合、
                if (Objects.nonNull(kycHyk1DsOutEntityList.get(0).getShokuId())
                        && kycHyk1DsOutEntityList.get(0).getShokuId() > CommonConstants.INT_0) {
                    // 計画作成者名=API定義の処理「3.3」の職員名
                    preventiveCare.setShokuName(srwOutList.get(0).getShokuinName());
                    // ・上記以外の場合、
                } else {
                    // 計画作成者名=API定義の処理「3.1」の作成者（文字列）
                    preventiveCare.setShokuName(kycHyk1DsOutEntityList.get(0).getShokuKnj());
                }
            }

            // 印鑑欄設定情報取得できる
            if (!CollectionUtils.isNullOrEmpty(chouOutList)) {
                // 印鑑1
                preventiveCare.setChouhyouInkan1(chouOutList.get(0).getHanko1Knj());
                // 印鑑2
                preventiveCare.setChouhyouInkan2(chouOutList.get(0).getHanko2Knj());
                // 印鑑3
                preventiveCare.setChouhyouInkan3(chouOutList.get(0).getHanko3Knj());
                // 印鑑4
                preventiveCare.setChouhyouInkan4(chouOutList.get(0).getHanko4Knj());
                // 印鑑5
                preventiveCare.setChouhyouInkan5(chouOutList.get(0).getHanko5Knj());
                // 印鑑6
                preventiveCare.setChouhyouInkan6(chouOutList.get(0).getHanko6Knj());
                // 印鑑7
                preventiveCare.setChouhyouInkan7(chouOutList.get(0).getHanko7Knj());
                // 印鑑8
                preventiveCare.setChouhyouInkan8(chouOutList.get(0).getHanko8Knj());
                // 印鑑9
                preventiveCare.setChouhyouInkan9(chouOutList.get(0).getHanko9Knj());
                // 印鑑10
                preventiveCare.setChouhyouInkan10(chouOutList.get(0).getHanko10Knj());
                // 印鑑11
                preventiveCare.setChouhyouInkan11(chouOutList.get(0).getHanko11Knj());
                // 印鑑12
                preventiveCare.setChouhyouInkan12(chouOutList.get(0).getHanko12Knj());
                // 印鑑13
                preventiveCare.setChouhyouInkan13(chouOutList.get(0).getHanko13Knj());
                // 印鑑14
                preventiveCare.setChouhyouInkan14(chouOutList.get(0).getHanko14Knj());
                // 印鑑15
                preventiveCare.setChouhyouInkan15(chouOutList.get(0).getHanko15Knj());
            } else {
                // 印鑑1
                preventiveCare.setChouhyouInkan1(CommonConstants.BLANK_STRING);
                // 印鑑2
                preventiveCare.setChouhyouInkan2(CommonConstants.BLANK_STRING);
                // 印鑑3
                preventiveCare.setChouhyouInkan3(CommonConstants.BLANK_STRING);
                // 印鑑4
                preventiveCare.setChouhyouInkan4(CommonConstants.BLANK_STRING);
                // 印鑑5
                preventiveCare.setChouhyouInkan5(CommonConstants.BLANK_STRING);
                // 印鑑6
                preventiveCare.setChouhyouInkan6(CommonConstants.BLANK_STRING);
                // 印鑑7
                preventiveCare.setChouhyouInkan7(CommonConstants.BLANK_STRING);
                // 印鑑8
                preventiveCare.setChouhyouInkan8(CommonConstants.BLANK_STRING);
                // 印鑑9
                preventiveCare.setChouhyouInkan9(CommonConstants.BLANK_STRING);
                // 印鑑10
                preventiveCare.setChouhyouInkan10(CommonConstants.BLANK_STRING);
                // 印鑑11
                preventiveCare.setChouhyouInkan11(CommonConstants.BLANK_STRING);
                // 印鑑12
                preventiveCare.setChouhyouInkan12(CommonConstants.BLANK_STRING);
                // 印鑑13
                preventiveCare.setChouhyouInkan13(CommonConstants.BLANK_STRING);
                // 印鑑14
                preventiveCare.setChouhyouInkan14(CommonConstants.BLANK_STRING);
                // 印鑑15
                preventiveCare.setChouhyouInkan15(CommonConstants.BLANK_STRING);
            }

            List<E00600Se> seList = new ArrayList<>();
            E00600Se se = new E00600Se();
            if (CollectionUtils.isNotEmpty(kycHyk2OutEntityList)) {
                for (KycHyk2OutEntity KycHyk2Out : kycHyk2OutEntityList) {
                    // 目標番号
                    se.setMokuhyoNo(ReportUtil.nullToEmpty(KycHyk2Out.getMokuhyoNo()));
                    // 目標
                    se.setMokuhyoKnj(ReportUtil.nullToEmpty(KycHyk2Out.getMokuhyoKnj()));
                    // 評価期間
                    se.setKikanKnj(ReportUtil.nullToEmpty(KycHyk2Out.getKikanKnj()));
                    // 目標達成状況
                    se.setJyoukyouKnj(ReportUtil.nullToEmpty(KycHyk2Out.getJyoukyouKnj()));
                    // 達成区分内容
                    se.setTasseiKnj(ReportUtil.nullToEmpty(KycHyk2Out.getTasseiKbn()));
                    // 原因(本人・家族）
                    se.setGeninKnj(ReportUtil.nullToEmpty(KycHyk2Out.getGeninKnj()));
                    // 原因（計画作成者）
                    se.setGenin2Knj(ReportUtil.nullToEmpty(KycHyk2Out.getGenin2Knj()));
                    // 今後の方針
                    se.setHousinKnj(ReportUtil.nullToEmpty(KycHyk2Out.getHousinKnj()));
                    seList.add(se);
                    preventiveCare.setSeList(seList);
                }

            }

            // 文書管理番号=共通関数補足の処理「2.2」の文書番号

            preventiveCare.setBunsyoKanriNo(
                    this.getBunsyoKanriNo(inDto.getSyscd(),
                            inDto.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getShokuId(),
                            inDto.getPrintSubjectHistoryList().get(0).getChoPrtList().get(0).getSection(),
                            inDto.getJigyoInfo().getSvJigyoId()));
        }

        infoInDto.setPreventiveCareSupportServiceEvaluationReport(preventiveCare);

        return infoInDto;
    }

    /**
     * 指定日を取得する
     * 
     * @param shiTeiKubun 指定日印刷区分
     * @param shiTeiDate  西暦日付
     * @param systemDate  システム日付
     * @throws Exception 例外
     */
    private ReportCommonShiTeiDateParts getShiTeiDate(String shiTeiKubun, String shiTeiDate, String systemDate) {
        // 指定日（年号）全角フレーム
        String shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（年）全角フレーム
        String shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（月）全角フレーム
        String shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日（日）全角フレーム
        String shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
        // 指定日印刷区分判定
        switch (shiTeiKubun) {
            case ReportConstants.PRINTDATE_KBN_SHINAI:
                // 指定日印刷区分 = 1 の場合
                // 指定日（年号）全角フレーム
                shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（年）全角フレーム
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）全角フレーム
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）全角フレーム
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            case ReportConstants.PRINTDATE_KBN_PRINT:
                // 指定日印刷区分 = 2 の場合
                // 西暦日付を和暦日付に変換する
                List<String> dateParts = ReportUtil.getLocalDateToJapanDateTimeFormat(shiTeiDate);
                // 指定日（年号）
                shiTeiDateGG = dateParts.get(ReportConstants.SHITEIDATE_GG);
                // 指定日（年）
                shiTeiDateYY = dateParts.get(ReportConstants.SHITEIDATE_YY);
                // 指定日（月）
                shiTeiDateMM = dateParts.get(ReportConstants.SHITEIDATE_MM);
                // 指定日（日）
                shiTeiDateDD = dateParts.get(ReportConstants.SHITEIDATE_DD);
                break;
            case ReportConstants.PRINTDATE_KBN_BLANKDATE:
                // 指定日印刷区分 = 3 の場合
                // 指定日の空欄表示処理
                DateTimeFormatter inputFormatter = DateTimeFormatter
                        .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
                DateTimeFormatter outputFormatter = DateTimeFormatter
                        .ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
                LocalDateTime dateTime = LocalDateTime.parse(systemDate, inputFormatter);
                String blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
                // 指定日（年号）
                shiTeiDateGG = blankDate.substring(0, 2);
                // 指定日（年）全角フレーム
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）全角フレーム
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）全角フレーム
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
            default:
                // 指定日（年号）全角フレーム
                shiTeiDateGG = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（年）全角フレーム
                shiTeiDateYY = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（月）全角フレーム
                shiTeiDateMM = CommonConstants.FULL_WIDTH_SPACE;
                // 指定日（日）全角フレーム
                shiTeiDateDD = CommonConstants.FULL_WIDTH_SPACE;
                break;
        }
        return new ReportCommonShiTeiDateParts(shiTeiDateGG, shiTeiDateYY, shiTeiDateMM, shiTeiDateDD);
    }

    /**
     * 日付を取得する
     * 
     * @param shiTeiDate 西暦日付
     * @throws Exception 例外
     */
    private ReportCommonDateParts getDate(String date) {
        // 日付（年号）=""
        String dateGG = CommonConstants.BLANK_STRING;
        // 日付（年）=""
        String dateYY = CommonConstants.BLANK_STRING;
        // 日付（月）=""
        String dateMM = CommonConstants.BLANK_STRING;
        // 日付（日）=""
        String dateDD = CommonConstants.BLANK_STRING;
        if (StringUtils.isNotEmpty(date)) {
            dateGG = date.substring(CommonConstants.INT_0, CommonConstants.INT_2);
            dateYY = date.substring(CommonConstants.INT_2, CommonConstants.INT_4);
            dateMM = date.substring(CommonConstants.INT_5, CommonConstants.INT_7);
            dateDD = date.substring(CommonConstants.INT_8, CommonConstants.INT_10);
        }
        return new ReportCommonDateParts(dateGG, dateYY, dateMM, dateDD);
    }

    /**
     * 文書番号情報取得処理。
     * 
     * @param sysCode    システムコード
     * @param shokuId    職員ID
     * @param sectionKnj セクション
     * @param svJigyoId  サービス事業者ID
     * @return アセスメント種別
     */
    public String getBunsyoKanriNo(String sysCode, String shokuId, String sectionKnj, String svJigyoId) {
        String bunsyoKanriNo = CommonConstants.BLANK_STRING;
        // 2.1. 共通関数「設定の読込」：クラス名：Nds3Gkfunc01Logic 関数名：GetF3GkProfile を利用し、
        // 設定の読込（システム環境以外の設定）情報を取得する。
        F3gkGetProfileInDto f3gkGetProfileInDto = new F3gkGetProfileInDto();
        f3gkGetProfileInDto.setGsyscd(sysCode);
        f3gkGetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(shokuId));
        f3gkGetProfileInDto.setHoujinId(CommonConstants.HOUJIN_ID_0);
        f3gkGetProfileInDto.setShisetuId(CommonConstants.SHISETU_ID);
        f3gkGetProfileInDto.setSvJigyoId(CommonConstants.SV_JIGYO_ID_0);
        f3gkGetProfileInDto.setKinounameKnj(CommonConstants.KINOU_NAME_PRT);
        f3gkGetProfileInDto.setSectionKnj(sectionKnj);
        f3gkGetProfileInDto.setKeyKnj(CommonConstants.ISO9001_FLG);
        f3gkGetProfileInDto.setAsDefault(CommonConstants.STR_DEFAULTDE_0);
        String f3gkProfile = nds3GkFunc01Logic.getF3gkProfile(f3gkGetProfileInDto);

        // 2.2. 設定の読込（システム環境以外の設定）情報取得の戻り値は"1"（正常参照）の場合、下記の24-06
        // 帳票書式番号設定マスタ情報取得のDAOを利用し、文書番号を取得する。
        if (CommonConstants.STR_1.equals(f3gkProfile)) {
            YoushikiNoByCriteriaInEntity youshikiNoByCriteriaInEntity = new YoushikiNoByCriteriaInEntity();
            youshikiNoByCriteriaInEntity.setAsSection(sectionKnj);
            youshikiNoByCriteriaInEntity.setLlSvJigyoId(svJigyoId);
            List<YoushikiNoOutEntity> youshikiNoOutList = comMocPrtYoushikiSelectMapper
                    .findYoushikiNoByCriteria(youshikiNoByCriteriaInEntity);
            if (youshikiNoOutList != null && youshikiNoOutList.size() > CommonConstants.INT_0) {
                bunsyoKanriNo = youshikiNoOutList.get(0).getYoushikiNo();
            }
        }
        return bunsyoKanriNo;
    }
}
