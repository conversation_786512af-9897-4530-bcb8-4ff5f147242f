package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.*;
import java.util.stream.Collectors;

import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeChosaInsertServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeChosaInsertServiceOutDto;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kan11H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kan12H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kan13H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kan15H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kan16H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4SerH21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kan11R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kan12R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kan13R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kan15R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kan16R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5SerR3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdlRirekiMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan11H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan11H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan12H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan12H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan13H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan13H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan15H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan15H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan16H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan16H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4SerH21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4SerH21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan11R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan11R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan12R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan12R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan13R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan13R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan15R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan15R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan16R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan16R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5SerR3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5SerR3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRireki;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRirekiCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.*;
import jp.ndsoft.carebase.common.dao.mysql.mapper.*;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.InsertServiceImpl;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.util.CommonDaoUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;

/**
 * @since 2025.04.02
 * <AUTHOR>
 * @implNote GUI00802_［アセスメント］画面（居宅）（6⑤）調査票一括取込
 */
@Service
public class AssessmentHomeChosaUpdateServiceImpl
        extends
        UpdateServiceImpl<AssessmentHomeChosaInsertServiceInDto, AssessmentHomeChosaInsertServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** ＧＬ＿居宅アセスメント履歴情報取得 */
    @Autowired
    private CpnTucGdlRirekiSelectMapper cpnTucGdlRirekiSelectMapper;
    /** ＧＬ＿サービス利用状況（Ｈ２１改訂）情報取得 */
    @Autowired
    private CpnTucGdl4SerH21SelectMapper cpnTucGdl4SerH21SelectMapper;
    /** ＧＬ＿サービス利用状況（R３改訂）情報取得 */
    @Autowired
    private CpnTucGdl5SerR3SelectMapper cpnTucGdl5SerR3SelectMapper;
    /** 改訂版認定調査票（サービス利用）情報取得 */
    @Autowired
    private KaiteihanNinteiCscInfoSelectMapper kaiteihanNinteiCscInfoSelectMapper;
    /** 改訂版認定調査票（概況調査）取得 */
    @Autowired
    private CpnTucCsc12SelectMapper cpnTucCsc12SelectMapper;
    /** 施設マスタ（１５－４）情報取得 */
    @Autowired
    private ComMscShisetuSelectMapper comMscShisetuSelectMapper;
    /** 認定調査票（ヘッダ）情報 */
    @Autowired
    private CpnTucCschSelectMapper cpnTucCschSelectMapper;
    /** Ｈ２１改訂版認定調査票（基本）情報取得 */
    @Autowired
    private CpnTucCsc3H21SelectMapper cpnTucCsc3H21SelectMapper;

    /** ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）情報取得 */
    @Autowired
    private CpnTucGdl4Kan11H21SelectMapper cpnTucGdl4Kan11H21SelectMapper;

    /** ＧＬ＿①基本（身体機能・起居）動作（Ｒ３改訂）情報取得 */
    @Autowired
    private CpnTucGdl5Kan11R3SelectMapper cpnTucGdl5Kan11R3SelectMapper;
    /** ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）情報取得 */
    @Autowired
    private CpnTucGdl4Kan12H21SelectMapper cpnTucGdl4Kan12H21SelectMapper;
    /** ＧＬ＿②生活機能（食事・排泄）（Ｒ３改訂）情報取得 */
    @Autowired
    private CpnTucGdl5Kan12R3SelectMapper cpnTucGdl5Kan12R3SelectMapper;

    /** ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）情報取得 */
    @Autowired
    private CpnTucGdl4Kan13H21SelectMapper cpnTucGdl4Kan13H21SelectMapper;

    /** ＧＬ＿③認知機能・④精神・行動障害（Ｒ３改訂）情報取得 */
    @Autowired
    private CpnTucGdl5Kan13R3SelectMapper cpnTucGdl5Kan13R3SelectMapper;

    /** ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）情報取得 */
    @Autowired
    private CpnTucGdl4Kan15H21SelectMapper cpnTucGdl4Kan15H21SelectMapper;

    /** ＧＬ＿⑤社会生活（への適応）力（Ｒ３改訂）情報取得 */
    @Autowired
    private CpnTucGdl5Kan15R3SelectMapper cpnTucGdl5Kan15R3SelectMapper;

    /** ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）情報取得 */
    @Autowired
    private CpnTucGdl4Kan16H21SelectMapper cpnTucGdl4Kan16H21SelectMapper;

    /** ＧＬ＿⑥医療・健康関係（Ｒ３改訂）情報取得 */
    @Autowired
    private CpnTucGdl5Kan16R3SelectMapper cpnTucGdl5Kan16R3SelectMapper;

    /** ＧＬ＿サービス利用状況（Ｈ２１改訂） */
    @Autowired
    private CpnTucGdl4SerH21Mapper cpnTucGdl4SerH21Mapper;

    /** ＧＬ＿サービス利用状況（R３改訂） */
    @Autowired
    private CpnTucGdl5SerR3Mapper cpnTucGdl5SerR3Mapper;

    /** ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂） */
    @Autowired
    private CpnTucGdl4Kan11H21Mapper cpnTucGdl4Kan11H21Mapper;

    /** ＧＬ＿①基本（身体機能・起居）動作（R３改訂） */
    @Autowired
    private CpnTucGdl5Kan11R3Mapper cpnTucGdl5Kan11R3Mapper;

    /** ＧＬ＿①基本（身体機能・起居）動作（R３改訂） */
    @Autowired
    private CpnTucGdl4Kan12H21Mapper cpnTucGdl4Kan12H21Mapper;

    /** ＧＬ＿②生活機能（食事・排泄）（R３改訂） */
    @Autowired
    private CpnTucGdl5Kan12R3Mapper cpnTucGdl5Kan12R3Mapper;

    /** ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂） */
    @Autowired
    private CpnTucGdl4Kan13H21Mapper cpnTucGdl4Kan13H21Mapper;

    /** ＧＬ＿③認知機能・④精神・行動障害（R３改訂） */
    @Autowired
    private CpnTucGdl5Kan13R3Mapper cpnTucGdl5Kan13R3Mapper;

    /** ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂） */
    @Autowired
    private CpnTucGdl4Kan15H21Mapper cpnTucGdl4Kan15H21Mapper;

    /** ＧＬ＿⑤社会生活（への適応）力（R３改訂） */
    @Autowired
    private CpnTucGdl5Kan15R3Mapper cpnTucGdl5Kan15R3Mapper;

    /** ＧＬ＿⑥医療・健康関係（Ｈ２１改訂） */
    @Autowired
    private CpnTucGdl4Kan16H21Mapper cpnTucGdl4Kan16H21Mapper;

    /** ＧＬ＿⑥医療・健康関係（R３改訂） */
    @Autowired
    private CpnTucGdl5Kan16R3Mapper cpnTucGdl5Kan16R3Mapper;

    /** ＧＬ＿居宅アセスメント履歴 */
    @Autowired
    private CpnTucGdlRirekiMapper cpnTucGdlRirekiMapper;

    /**
     * ［アセスメント］画面（居宅）（6⑤）調査票一括取込
     * 
     * @param inDto ［アセスメント］画面（居宅）（6⑤）入力DTO.
     * @return ［アセスメント］画面（居宅）（6⑤）出力DTO
     */
    @Override
    protected AssessmentHomeChosaInsertServiceOutDto mainProcess(
            final AssessmentHomeChosaInsertServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        // 排他的結果
        String retStatus;

        // ===============API-82.調査票一括取込 START===============//
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. 下記のＧＬ＿居宅アセスメント履歴のDAOを利用し、履歴情報を取得する===============
         * 
         */
        List<KyotakuRirekiOutEntity> kyotakuRirekiList = findKyotakuRirekiFunc(inDto);

        /*
         * ===============3.
         * 上記の処理で取得した履歴情報.アセスメントIDとリクエストパラメータ詳細．アセスメントIDをマッピングし、データが存在している場合、
         * 下記処理を続けて実行する。===============
         * 
         */
        List<KyotakuRirekiOutEntity> kyotakuRirekiData = kyotakuRirekiList
                .stream()
                .filter(item -> item.getGdlId().equals(CommonDtoUtil.strValToInt(inDto.getGdlId())))
                .collect(Collectors.toList());

        if (kyotakuRirekiData.size() <= 0) {
            return null;
        }
        Optional<KyotakuRirekiOutEntity> kyotakuRirekiOptional = kyotakuRirekiData
                .stream().findFirst();

        KyotakuRirekiOutEntity kyotakuRireki = new KyotakuRirekiOutEntity();
        if (kyotakuRirekiOptional.isPresent()) {
            kyotakuRireki = kyotakuRirekiOptional.get();
        }

        // DAOパラメータを作成
        CpnTucGdlRireki cpnTucGdlRirekiRecord = new CpnTucGdlRireki();

        // 3.1．リクエストパラメータ詳細．タブID<>3の場合
        if (!CommonConstants.TAB_ID_3.equals(inDto.getTabId())) {
            CpnTucGdl4SerH21ListOutEntity cpnTucGdl4SerH21 = new CpnTucGdl4SerH21ListOutEntity();
            Gdl5Ass3R3OutEntity gdl5Ass3R3 = new Gdl5Ass3R3OutEntity();
            // 3.1.1.
            // リクエストパラメータ詳細．改訂フラグ＝4の場合、下記のＧＬ＿サービス利用状況（Ｈ２１改訂）のDAOを利用し、ＧＬ＿サービス利用状況（Ｈ２１改訂）情報を取得する。
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {
                cpnTucGdl4SerH21 = findCpnTucGdl4SerH21ListFunc(inDto);
            }
            // 3.1.2．リクエストパラメータ詳細．改訂フラグ＝5の場合、下記のＧＬ＿サービス利用状況（R３改訂）DAOを利用し、ＧＬ＿サービス利用状況（R３改訂）情報を取得する。
            else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {
                gdl5Ass3R3 = findGdl5Ass3R3Func(inDto);
            }

            // 3.1.3．下記の改訂版認定調査票情報取得DAOを利用し、改訂版認定調査票情報を取得する。
            KaiteihanNinteiCscInfoOutEntity kaiteihanNinteiCscInfo = findKaiteihanNinteiCscInfoFunc(inDto);

            // 3.1.4．下記の改訂版認定調査票（概況調査）DAOを利用し、改訂版認定調査票（概況調査）情報を取得する。
            CpnTucCsc12OutEntity cpnTucCsc12 = findCpnTucCsc12Func(inDto);

            // 3.1.5．上記改訂版認定調査票（概況調査）情報を取得して、
            // 施設名がNULLの場合、下記の施設マスタ（１５－４）のDAOを利用し、施設マスタ（１５－４）情報を取得する。
            CpnChoShisetuInfoOutEntity cpnChoShisetuInfo = new CpnChoShisetuInfoOutEntity();
            if (Objects.nonNull(cpnTucCsc12) && StringUtils.isEmpty(cpnTucCsc12.getShisetuKnj())) {
                cpnChoShisetuInfo = findCpnChoShisetuInfoFunc(cpnTucCsc12);
            }

            // 3.1.11．下記の認定調査票（ヘッダ）のDAOを利用し、実施日を取得する。
            CpnTucCschOutEntity cpnTucCsch = findCpnTucCschByCriteriaFunc(inDto);

            // リクエストパラメータ詳細．改訂フラグ＝4の場合
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {
                CpnTucGdl4SerH21 cpnTucGdl4SerH21Record = new CpnTucGdl4SerH21();
                if (Objects.nonNull(kaiteihanNinteiCscInfo)) {
                    cpnTucGdl4SerH21Record = getCpnTucGdl4SerH21RecordFunc(kaiteihanNinteiCscInfo,
                            cpnTucCsch,
                            cpnTucCsc12, cpnChoShisetuInfo);
                }

                // 【ＧＬ＿サービス利用状況（Ｈ２１改訂）】を更新/登録する
                retStatus = updateCpnTucGdl4SerH21(inDto, cpnTucGdl4SerH21, cpnTucGdl4SerH21Record);
                if (CommonDtoUtil.checkStringEqual(retStatus, CommonConstants.FAILURE)) {
                    throw new ExclusiveException();
                }
            }
            // リクエストパラメータ詳細．改訂フラグ＝5の場合
            else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {
                CpnTucGdl5SerR3 cpnTucGdl5SerR3Record = new CpnTucGdl5SerR3();
                if (Objects.nonNull(kaiteihanNinteiCscInfo)) {
                    cpnTucGdl5SerR3Record = getCpnTucGdl5SerR3RecordFunc(kaiteihanNinteiCscInfo,
                            cpnTucCsch, cpnTucCsc12, cpnChoShisetuInfo);
                }

                // 【ＧＬ＿サービス利用状況（R３改訂）】を更新/登録する
                retStatus = updateCpnTucGdl5SerR3(inDto, gdl5Ass3R3, cpnTucGdl5SerR3Record);
                if (CommonDtoUtil.checkStringEqual(retStatus, CommonConstants.FAILURE)) {
                    throw new ExclusiveException();
                }
            }
            // 3.1.10．リクエストパラメータ詳細．アセスメントID＝上記処理２で履歴情報．アセスメントIDの履歴情報．ass_3を「●」に設定する
            cpnTucGdlRirekiRecord.setAss3("●");
        }

        // 3.2．下記のＨ２１改訂版認定調査票（基本）情報取得のDAOを利用し、Ｈ２１改訂版認定調査票（基本）情報を取得する。
        CpnTucCsc3H21OutEntity cpnTucCsc3H21 = findCpnTucCsc3H21Func(inDto);

        // 改訂版認定調査票（基本）情報を使用して値を設定する
        CpnTucGdl4Kan11H21 cpnTucGdl4Kan11H21Record = getCpnTucGdl4Kan11H21RecordFunc(cpnTucCsc3H21);

        // 3.3．リクエストパラメータ詳細．タブID<>6の場合、
        if (!CommonConstants.TAB_ID_6.equals(inDto.getTabId())) {
            // 3.3.1．リクエストパラメータ詳細．改訂フラグ＝4の場合、
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {
                // １．下記のＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）取得のDAOを利用し、ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）情報を取得する。
                CpnTucGdl4Kan11H21OutEntity cpnTucGdl4Kan11H21 = findCpnTucGdl4Kan11H21Func(inDto);

                // 【ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）】を更新/登録する
                retStatus = updateCpnTucGdl4Kan11H21(inDto, cpnTucGdl4Kan11H21, cpnTucGdl4Kan11H21Record);
                if (CommonDtoUtil.checkStringEqual(retStatus, CommonConstants.FAILURE)) {
                    throw new ExclusiveException();
                }
            }
            // 3.3.2．リクエストパラメータ詳細．改訂フラグ＝5の場合、
            else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {
                // １．下記のＧＬ＿①基本（身体機能・起居）動作（Ｒ３改訂）情報取得のDAOを利用し、ＧＬ＿①基本（身体機能・起居）動作（Ｒ３改訂）情報を取得する。
                Gdl5Ass6R3OutEntity gdl5Ass6R3 = findGdl5Ass6R3Func(inDto);
                // DAOパラメータを作成
                CpnTucGdl5Kan11R3 cpnTucGdl5Kan11R3Record = new CpnTucGdl5Kan11R3();
                BeanUtils.copyProperties(cpnTucGdl5Kan11R3Record, cpnTucGdl4Kan11H21Record);

                // 【ＧＬ＿①基本（身体機能・起居）動作（Ｒ３改訂）】を更新/登録する
                retStatus = updateGdl5Ass6R3(inDto, gdl5Ass6R3, cpnTucGdl5Kan11R3Record);
                if (CommonDtoUtil.checkStringEqual(retStatus, CommonConstants.FAILURE)) {
                    throw new ExclusiveException();
                }
            }
            // 4． リクエストパラメータ詳細．アセスメントID＝上記処理２で履歴情報．アセスメントIDの履歴情報．ass_6を「●」に設定する
            cpnTucGdlRirekiRecord.setAss6("●");
        }

        // 3.4．リクエストパラメータ詳細．タブID<>7の場合、
        if (!CommonConstants.TAB_ID_7.equals(inDto.getTabId())) {
            // 3.4.1．リクエストパラメータ詳細．改訂フラグ＝4の場合、
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {

                // １．下記のＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）取得のDAOを利用し、ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）情報を取得する。
                CpnTucGdl4Kan12H21OutEntity cpnTucGdl4Kan12H21 = findCpnTucGdl4Kan12H21Func(inDto);

                // DAOパラメータを作成
                CpnTucGdl4Kan12H21 cpnTucGdl4Kan12H21Record = getCpnTucGdl4Kan12H21RecordFunc(
                        cpnTucCsc3H21);

                // 【ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）】を更新/登録する
                retStatus = updateCpnTucGdl4Kan12H21(inDto, cpnTucGdl4Kan12H21, cpnTucGdl4Kan12H21Record);
                if (CommonDtoUtil.checkStringEqual(retStatus, CommonConstants.FAILURE)) {
                    throw new ExclusiveException();
                }
            }
            // 3.4.2．リクエストパラメータ詳細．改訂フラグ＝5の場合、
            else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {

                // １．下記のＧＬ＿②生活機能（食事・排泄）（Ｒ３改訂）情報取得のDAOを利用し、ＧＬ＿②生活機能（食事・排泄）（Ｒ３改訂）情報を取得する。

                Gdl5Ass7R3OutEntity gdl5Ass7R3 = findGdl5Ass7R3Func(inDto);

                CpnTucGdl5Kan12R3 cpnTucGdl5Kan12R3Record = getCpnTucGdl5Kan12R3RecordFunc(
                        cpnTucCsc3H21);
                // 【ＧＬ＿②生活機能（食事・排泄）（Ｒ３改訂）】を更新/登録する
                retStatus = updateCpnTucGdl5Kan12R3(inDto, gdl5Ass7R3, cpnTucGdl5Kan12R3Record);
                if (CommonDtoUtil.checkStringEqual(retStatus, CommonConstants.FAILURE)) {
                    throw new ExclusiveException();
                }
            }
            // 4． リクエストパラメータ詳細．アセスメントID＝上記処理２で履歴情報．アセスメントIDの履歴情報．ass_7を「●」に設定する
            cpnTucGdlRirekiRecord.setAss7("●");
        }

        // 3.5．リクエストパラメータ詳細．タブID<>8の場合、
        if (!CommonConstants.TAB_ID_8.equals(inDto.getTabId())) {
            // 3.5.1．リクエストパラメータ詳細．改訂フラグ＝4の場合、
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {
                // １．下記のＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）取得のDAOを利用し、ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）情報を取得する。
                CpnTucGdl4Kan13H21ListOutEntity cpnTucGdl4Kan13H21 = findCpnTucGdl4Kan13H21ListFunc(
                        inDto);

                CpnTucGdl4Kan13H21 cpnTucGdl4Kan13H21Record = getCpnTucGdl4Kan13H21RecordFunc(
                        cpnTucCsc3H21);

                // 【ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）】情報を更新/登録する
                retStatus = updateCpnTucGdl4Kan13H21(inDto, cpnTucGdl4Kan13H21, cpnTucGdl4Kan13H21Record);
                if (CommonDtoUtil.checkStringEqual(retStatus, CommonConstants.FAILURE)) {
                    throw new ExclusiveException();
                }
            } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {
                // 3.5.2．リクエストパラメータ詳細．改訂フラグ＝5の場合、
                // １．下記のＧＬ＿③認知機能・④精神・行動障害（Ｒ３改訂）情報取得のDAOを利用し、ＧＬ＿③認知機能・④精神・行動障害（Ｒ３改訂）情報を取得する。
                Gdl5Ass8R3OutEntity gdl5Ass8R3 = findGdl5Ass8R3Func(inDto);

                CpnTucGdl5Kan13R3 cpnTucGdl5Kan13R3Record = getCpnTucGdl5Kan13R3RecordFunc(
                        cpnTucCsc3H21);

                // 【ＧＬ＿③認知機能・④精神・行動障害（Ｒ３改訂）】を更新/登録する
                retStatus = updateCpnTucGdl5Kan13R3(inDto, gdl5Ass8R3, cpnTucGdl5Kan13R3Record);
                if (CommonDtoUtil.checkStringEqual(retStatus, CommonConstants.FAILURE)) {
                    throw new ExclusiveException();
                }
            }
            // 4． リクエストパラメータ詳細．アセスメントID＝上記処理２で履歴情報．アセスメントIDの履歴情報．ass_8、ass_9を「●」に設定する
            cpnTucGdlRirekiRecord.setAss8("●");
            cpnTucGdlRirekiRecord.setAss9("●");
        }

        // 3.6．リクエストパラメータ詳細．タブID<>9の場合、
        if (!CommonConstants.TAB_ID_9.equals(inDto.getTabId())) {
            // 3.6.1．リクエストパラメータ詳細．改訂フラグ＝4の場合
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {
                // １．下記のＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）取得のDAOを利用し、ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）情報を取得する。
                CpnTucGdl4Kan15H21OutEntity cpnTucGdl4Kan15H21 = findCpnTucGdl4Kan15H21Func(inDto);

                CpnTucGdl4Kan15H21 cpnTucGdl4Kan15H21Record = getCpnTucGdl4Kan15H21RecordFunc(
                        cpnTucCsc3H21);

                // 【ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）】を更新/登録する
                retStatus = updateCpnTucGdl4Kan15H21(inDto, cpnTucGdl4Kan15H21, cpnTucGdl4Kan15H21Record);
                if (CommonDtoUtil.checkStringEqual(retStatus, CommonConstants.FAILURE)) {
                    throw new ExclusiveException();
                }

            }
            // 3.6.2．リクエストパラメータ詳細．改訂フラグ＝5の場合、
            else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {
                // １．下記のＧＬ＿⑤社会生活（への適応）力（Ｒ３改訂）情報取得のDAOを利用し、ＧＬ＿⑤社会生活（への適応）力（Ｒ３改訂）情報を取得する。
                Gdl5Ass9R3OutEntity gdl5Ass9R3 = findGdl5Ass9R3Func(inDto);
                // DAOパラメータを作成
                CpnTucGdl5Kan15R3 cpnTucGdl5Kan15R3Record = getCpnTucGdl5Kan15R3RecordFunc(
                        cpnTucCsc3H21);

                // 【ＧＬ＿⑤社会生活（への適応）力（Ｒ３改訂）】を更新/登録する
                retStatus = updateCpnTucGdl5Kan15R3(inDto, gdl5Ass9R3, cpnTucGdl5Kan15R3Record);
                if (CommonDtoUtil.checkStringEqual(retStatus, CommonConstants.FAILURE)) {
                    throw new ExclusiveException();
                }

            }
            // 4． リクエストパラメータ詳細．アセスメントID＝上記処理２で履歴情報．アセスメントIDの履歴情報．ass_10を「●」に設定する
            cpnTucGdlRirekiRecord.setAss10("●");
        }

        // 3.7．リクエストパラメータ詳細．タブID<>10の場合、
        if (!CommonConstants.TAB_ID_10.equals(inDto.getTabId())) {
            // 3.7.1．リクエストパラメータ詳細．改訂フラグ＝4の場合、
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {
                // １．下記のＧＬ＿⑥医療・健康関係（Ｈ２１改訂）取得のDAOを利用し、ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）情報を取得する。
                CpnTucGdl4Kan16H21OutEntity cpnTucGdl4Kan16H21 = findCpnTucGdl4Kan16H21Func(inDto);

                // DAOパラメータを作成
                CpnTucGdl4Kan16H21 cpnTucGdl4Kan16H21Record = getCpnTucGdl4Kan16H21RecordFunc(
                        cpnTucCsc3H21);

                // 【ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）】を更新/登録する
                retStatus = updateCpnTucGdl4Kan16H21(inDto, cpnTucGdl4Kan16H21, cpnTucGdl4Kan16H21Record);
                if (CommonDtoUtil.checkStringEqual(retStatus, CommonConstants.FAILURE)) {
                    throw new ExclusiveException();
                }
            }
            // 3.7.2．リクエストパラメータ詳細．改訂フラグ＝5の場合、
            else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {
                // １．下記のＧＬ＿⑥医療・健康関係（Ｒ３改訂）情報取得のDAOを利用し、ＧＬ＿⑥医療・健康関係（Ｒ３改訂）情報を取得する。
                Gdl5Ass10R3OutEntity gdl5Ass10R3 = findGdl5Ass10R3Func(inDto);

                // DAOパラメータを作成
                CpnTucGdl5Kan16R3 cpnTucGdl5Kan16R3Record = getCpnTucGdl5Kan16R3RecordFunc(
                        cpnTucCsc3H21);
                // 【ＧＬ＿⑥医療・健康関係（Ｒ３改訂）】を更新/登録する
                retStatus = updateCpnTucGdl5Kan16R3(inDto, gdl5Ass10R3, cpnTucGdl5Kan16R3Record);
                if (CommonDtoUtil.checkStringEqual(retStatus, CommonConstants.FAILURE)) {
                    throw new ExclusiveException();
                }
            }
            // 4． リクエストパラメータ詳細．アセスメントID＝上記処理２で履歴情報．アセスメントIDの履歴情報．ass_11を「●」に設定する
            cpnTucGdlRirekiRecord.setAss11("●");
        }

        // 【ＧＬ＿居宅アセスメント履歴】情報を更新する
        retStatus = updateCpnTucGdlRireki(inDto, kyotakuRireki, cpnTucGdlRirekiRecord);
        if (CommonDtoUtil.checkStringEqual(retStatus, CommonConstants.FAILURE)) {
            throw new ExclusiveException();
        }

        // ===============API-82.調査票一括取込 END===============//
        // 戻り情報を設定
        AssessmentHomeChosaInsertServiceOutDto outDto = new AssessmentHomeChosaInsertServiceOutDto();
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 履歴情報を取得する
     * 
     * @param inDto
     * @return
     */
    private List<KyotakuRirekiOutEntity> findKyotakuRirekiFunc(AssessmentHomeChosaInsertServiceInDto inDto) {
        // DAOパラメータを作成
        KyotakuRirekiByCriteriaInEntity kyotakuRirekiByCriteriaInEntity = new KyotakuRirekiByCriteriaInEntity();
        // 計画期間ID
        kyotakuRirekiByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // 事業所ID
        kyotakuRirekiByCriteriaInEntity.setJId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        kyotakuRirekiByCriteriaInEntity.setUId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // DAOを実行
        List<KyotakuRirekiOutEntity> kyotakuRirekiList = this.cpnTucGdlRirekiSelectMapper
                .findKyotakuRirekiByCriteria(
                        kyotakuRirekiByCriteriaInEntity);
        return kyotakuRirekiList;
    }

    /**
     * ＧＬ＿サービス利用状況（Ｈ２１改訂）情報を取得する
     * 
     * @param inDto
     * @return
     */
    private CpnTucGdl4SerH21ListOutEntity findCpnTucGdl4SerH21ListFunc(
            AssessmentHomeChosaInsertServiceInDto inDto) {

        // DAOパラメータを作成
        CpnTucGdl4SerH21ListByCriteriaInEntity cpnTucGdl4SerH21ListByCriteriaInEntity = new CpnTucGdl4SerH21ListByCriteriaInEntity();
        // 計画期間ID
        cpnTucGdl4SerH21ListByCriteriaInEntity.setAlSclId(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // アセスメントID
        cpnTucGdl4SerH21ListByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // DAOを実行
        List<CpnTucGdl4SerH21ListOutEntity> cpnTucGdl4SerH21List = this.cpnTucGdl4SerH21SelectMapper
                .findCpnTucGdl4SerH21ListByCriteria(cpnTucGdl4SerH21ListByCriteriaInEntity);

        Optional<CpnTucGdl4SerH21ListOutEntity> cpnTucGdl4SerH21Optional = cpnTucGdl4SerH21List
                .stream()
                .findFirst();

        if (cpnTucGdl4SerH21Optional.isPresent()) {
            return cpnTucGdl4SerH21Optional.get();
        }

        return null;
    }

    /**
     * ＧＬ＿サービス利用状況（R３改訂）情報を取得する
     * 
     * @param inDto
     * @return
     */
    private Gdl5Ass3R3OutEntity findGdl5Ass3R3Func(
            AssessmentHomeChosaInsertServiceInDto inDto) {

        // DAOパラメータを作成
        Gdl5Ass3R3ByCriteriaInEntity gdl5Ass3R3ByCriteriaInEntity = new Gdl5Ass3R3ByCriteriaInEntity();
        // 計画期間ID
        gdl5Ass3R3ByCriteriaInEntity.setAlSclId(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // アセスメントID
        gdl5Ass3R3ByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // DAOを実行
        List<Gdl5Ass3R3OutEntity> gdl5Ass3R3OutList = this.cpnTucGdl5SerR3SelectMapper
                .findGdl5Ass3R3ByCriteria(gdl5Ass3R3ByCriteriaInEntity);

        Optional<Gdl5Ass3R3OutEntity> gdl5Ass3R3Optional = gdl5Ass3R3OutList.stream().findFirst();

        if (gdl5Ass3R3Optional.isPresent()) {
            return gdl5Ass3R3Optional.get();
        }

        return null;
    }

    /**
     * 改訂版認定調査票情報を取得する
     * 
     * @param inDto
     * @return
     */
    private KaiteihanNinteiCscInfoOutEntity findKaiteihanNinteiCscInfoFunc(
            AssessmentHomeChosaInsertServiceInDto inDto) {
        // DAOパラメータを作成
        KaiteihanNinteiCscInfoByCriteriaInEntity kaiteihanNinteiCscInfoByCriteriaInEntity = new KaiteihanNinteiCscInfoByCriteriaInEntity();
        // 調査票ID
        kaiteihanNinteiCscInfoByCriteriaInEntity.setCschId(CommonDtoUtil.strValToInt(inDto.getCschId()));
        kaiteihanNinteiCscInfoByCriteriaInEntity.setSc1Id(inDto.getSc1Id());
        // DAOを実行
        List<KaiteihanNinteiCscInfoOutEntity> kaiteihanNinteiCscInfoList = this.kaiteihanNinteiCscInfoSelectMapper
                .findKaiteihanNinteiCscInfoByCriteria(kaiteihanNinteiCscInfoByCriteriaInEntity);

        Optional<KaiteihanNinteiCscInfoOutEntity> kaiteihanNinteiCscInfoOptional = kaiteihanNinteiCscInfoList
                .stream()
                .findFirst();
        if (kaiteihanNinteiCscInfoOptional.isPresent()) {
            return kaiteihanNinteiCscInfoOptional.get();
        }

        return null;
    }

    /**
     * 改訂版認定調査票（概況調査）情報を取得する
     * 
     * @param inDto
     * @return
     */
    private CpnTucCsc12OutEntity findCpnTucCsc12Func(
            AssessmentHomeChosaInsertServiceInDto inDto) {
        // DAOパラメータを作成
        CpnTucCsc12ByCriteriaInEntity cpnTucCsc12ByCriteriaInEntity = new CpnTucCsc12ByCriteriaInEntity();
        // 調査票ID
        cpnTucCsc12ByCriteriaInEntity.setCschId(CommonDtoUtil.strValToInt(inDto.getCschId()));
        cpnTucCsc12ByCriteriaInEntity.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // DAOを実行
        List<CpnTucCsc12OutEntity> cpnTucCsc12List = this.cpnTucCsc12SelectMapper
                .findCpnTucCsc12ByCriteria(cpnTucCsc12ByCriteriaInEntity);

        Optional<CpnTucCsc12OutEntity> cpnTucCsc12Optional = cpnTucCsc12List.stream().findFirst();

        if (cpnTucCsc12Optional.isPresent()) {
            return cpnTucCsc12Optional.get();
        }
        return null;
    }

    /**
     * 施設マスタ（１５－４）情報を取得する
     * 
     * @param cpnTucCsc12
     * @return
     */
    private CpnChoShisetuInfoOutEntity findCpnChoShisetuInfoFunc(CpnTucCsc12OutEntity cpnTucCsc12) {

        // DAOパラメータを作成
        CpnChoShisetuInfoByCriteriaInEntity cpnChoShisetuInfoByCriteriaInEntity = new CpnChoShisetuInfoByCriteriaInEntity();

        // 施設ID
        cpnChoShisetuInfoByCriteriaInEntity
                .setShisetsuId(cpnTucCsc12.getShisetsuId());

        // DAOを実行
        List<CpnChoShisetuInfoOutEntity> cpnChoShisetuInfoList = this.comMscShisetuSelectMapper
                .findCpnChoShisetuInfoByCriteria(cpnChoShisetuInfoByCriteriaInEntity);

        Optional<CpnChoShisetuInfoOutEntity> cpnChoShisetuInfoOptional = cpnChoShisetuInfoList
                .stream().findFirst();

        if (cpnChoShisetuInfoOptional.isPresent()) {
            return cpnChoShisetuInfoOptional.get();
        }

        return null;
    }

    /**
     * 実施日を取得する
     * 
     * @param inDto
     * @return
     */
    private CpnTucCschOutEntity findCpnTucCschByCriteriaFunc(AssessmentHomeChosaInsertServiceInDto inDto) {

        // DAOパラメータを作成
        CpnTucCschByCriteriaInEntity cpnTucCschByCriteriaInEntity = new CpnTucCschByCriteriaInEntity();
        // 計画期間ID
        cpnTucCschByCriteriaInEntity.setSc1Id(inDto.getSc1Id());
        // 調査票ID
        cpnTucCschByCriteriaInEntity.setCschId(inDto.getCschId());
        // DAOを実行
        List<CpnTucCschOutEntity> cpnTucCschList = this.cpnTucCschSelectMapper
                .findCpnTucCschByCriteria(cpnTucCschByCriteriaInEntity);

        Optional<CpnTucCschOutEntity> cpnTucCschOptional = cpnTucCschList.stream().findFirst();

        if (cpnTucCschOptional.isPresent()) {
            return cpnTucCschOptional.get();
        }

        return null;
    }

    /**
     * CpnTucGdl4SerH21 DAOパラメータを作成
     * 
     * @param kaiteihanNinteiCscInfo
     * @param cpnTucCsch
     * @param cpnTucCsc12
     * @param cpnChoShisetuInfo
     * @return
     */
    private CpnTucGdl4SerH21 getCpnTucGdl4SerH21RecordFunc(KaiteihanNinteiCscInfoOutEntity kaiteihanNinteiCscInfo,
            CpnTucCschOutEntity cpnTucCsch, CpnTucCsc12OutEntity cpnTucCsc12,
            CpnChoShisetuInfoOutEntity cpnChoShisetuInfo) {

        // DAOパラメータを作成
        CpnTucGdl4SerH21 cpnTucGdl4SerH21Record = new CpnTucGdl4SerH21();

        // 在宅サービス利用1
        cpnTucGdl4SerH21Record.setSer1Cd(kaiteihanNinteiCscInfo.getService1Cd());
        // 在宅サービス利用2
        cpnTucGdl4SerH21Record.setSer2Cd(kaiteihanNinteiCscInfo.getService2Cd());
        // 在宅サービス利用3
        cpnTucGdl4SerH21Record.setSer3Cd(kaiteihanNinteiCscInfo.getService3Cd());
        // 在宅サービス利用4
        cpnTucGdl4SerH21Record.setSer4Cd(kaiteihanNinteiCscInfo.getService4Cd());
        // 在宅サービス利用5
        cpnTucGdl4SerH21Record.setSer5Cd(kaiteihanNinteiCscInfo.getService5Cd());
        // 在宅サービス利用6
        cpnTucGdl4SerH21Record.setSer6Cd(kaiteihanNinteiCscInfo.getService6Cd());
        // 在宅サービス利用7
        cpnTucGdl4SerH21Record.setSer7Cd(kaiteihanNinteiCscInfo.getService7Cd());
        // 在宅サービス利用8
        cpnTucGdl4SerH21Record.setSer8Cd(kaiteihanNinteiCscInfo.getService8Cd());
        // 在宅サービス利用9
        cpnTucGdl4SerH21Record.setSer9Cd(kaiteihanNinteiCscInfo.getService9Cd());
        // 在宅サービス利用10
        cpnTucGdl4SerH21Record.setSer10Cd(kaiteihanNinteiCscInfo.getService10Cd());
        // 在宅サービス利用11
        cpnTucGdl4SerH21Record.setSer11Cd(kaiteihanNinteiCscInfo.getService11Cd());
        // 在宅サービス利用12
        cpnTucGdl4SerH21Record.setSer12Cd(kaiteihanNinteiCscInfo.getService12Cd());
        // 在宅サービス利用13
        cpnTucGdl4SerH21Record.setSer13Cd(kaiteihanNinteiCscInfo.getService13Cd());
        // 在宅サービス利用14
        cpnTucGdl4SerH21Record.setSer14Cd(kaiteihanNinteiCscInfo.getService14Cd());
        // 在宅サービス利用15
        cpnTucGdl4SerH21Record.setSer15Cd(kaiteihanNinteiCscInfo.getService15Cd());
        // 利用回数1
        cpnTucGdl4SerH21Record.setKaisu1(kaiteihanNinteiCscInfo.getKaisuu1());
        // 利用回数2
        cpnTucGdl4SerH21Record.setKaisu2(kaiteihanNinteiCscInfo.getKaisuu2());
        // 利用回数3
        cpnTucGdl4SerH21Record.setKaisu3(kaiteihanNinteiCscInfo.getKaisuu3());
        // 利用回数4
        cpnTucGdl4SerH21Record.setKaisu4(kaiteihanNinteiCscInfo.getKaisuu4());
        // 利用回数5
        cpnTucGdl4SerH21Record.setKaisu5(kaiteihanNinteiCscInfo.getKaisuu5());
        // 利用回数6
        cpnTucGdl4SerH21Record.setKaisu6(kaiteihanNinteiCscInfo.getKaisuu6());
        // 利用回数7
        cpnTucGdl4SerH21Record.setKaisu7(kaiteihanNinteiCscInfo.getKaisuu7());
        // 利用回数8
        cpnTucGdl4SerH21Record.setKaisu8(kaiteihanNinteiCscInfo.getKaisuu8());
        // 利用回数9
        cpnTucGdl4SerH21Record.setKaisu9(kaiteihanNinteiCscInfo.getKaisuu9());
        // 利用回数10
        cpnTucGdl4SerH21Record.setKaisu10(kaiteihanNinteiCscInfo.getKaisuu10());
        // 利用回数11
        cpnTucGdl4SerH21Record.setKaisu11(kaiteihanNinteiCscInfo.getKaisuu11());
        // 利用回数12
        cpnTucGdl4SerH21Record.setKaisu12(kaiteihanNinteiCscInfo.getKaisuu12());
        // 利用回数13
        cpnTucGdl4SerH21Record.setKaisu13(kaiteihanNinteiCscInfo.getKaisuu13());
        // 夜間対応訪問介護
        cpnTucGdl4SerH21Record.setSer31Cd(kaiteihanNinteiCscInfo.getService17Cd());
        // 夜間対応訪問介護（回数）
        cpnTucGdl4SerH21Record.setKaisu31(kaiteihanNinteiCscInfo.getKaisuu17());
        // 認知症対応型通所介護
        cpnTucGdl4SerH21Record.setSer32Cd(kaiteihanNinteiCscInfo.getService18Cd());
        // 認知症対応型通所介護（回数）
        cpnTucGdl4SerH21Record.setKaisu32(kaiteihanNinteiCscInfo.getKaisuu18());
        // 小規模多機能居宅介護
        cpnTucGdl4SerH21Record.setSer33Cd(kaiteihanNinteiCscInfo.getService19Cd());
        // 小規模多機能居宅介護（回数）
        cpnTucGdl4SerH21Record.setKaisu33(kaiteihanNinteiCscInfo.getKaisuu19());
        // 地域密着型特定施設入居者生活介護
        cpnTucGdl4SerH21Record.setSer34Cd(kaiteihanNinteiCscInfo.getService20Cd());
        // 地域密着型特定施設入居者生活介護（回数）
        cpnTucGdl4SerH21Record.setKaisu34(kaiteihanNinteiCscInfo.getKaisuu20());
        // 地域密着型介護老人福祉施設入居者生活介護
        cpnTucGdl4SerH21Record.setSer35Cd(kaiteihanNinteiCscInfo.getService21Cd());
        // 地域密着型介護老人福祉施設入居者生活介護（回数）
        cpnTucGdl4SerH21Record.setKaisu35(kaiteihanNinteiCscInfo.getKaisuu21());
        // サービス利用状況記載日
        if (Objects.nonNull(cpnTucCsch)) {
            cpnTucGdl4SerH21Record.setSerYmd(cpnTucCsch.getJisshiDateYmd());
        }
        // 複合型サービス
        cpnTucGdl4SerH21Record.setSer36Cd(kaiteihanNinteiCscInfo.getService23Cd());
        // 定期巡回・随時対応型訪問介護看護
        cpnTucGdl4SerH21Record.setSer37Cd(kaiteihanNinteiCscInfo.getService22Cd());
        // 複合型サービス（回数）
        cpnTucGdl4SerH21Record.setKaisu36(kaiteihanNinteiCscInfo.getKaisuu23());
        // 定期巡回・随時対応型訪問介護看護（回数）
        cpnTucGdl4SerH21Record.setKaisu37(kaiteihanNinteiCscInfo.getKaisuu22());
        // 利用回数15
        // ※値がNULLの場合は0をセットする
        cpnTucGdl4SerH21Record
                .setKaisu15(kaiteihanNinteiCscInfo.getKaishuUmu() == null ? 0
                        : kaiteihanNinteiCscInfo.getKaishuUmu());
        // 市町村特別給付
        cpnTucGdl4SerH21Record
                .setTokKyuKnj(kaiteihanNinteiCscInfo.getMemo1Knj());

        // ①改訂版認定調査票（概況調査）情報が存在する場合、
        if (Objects.nonNull(cpnTucCsc12)) {
            // 利用施設種別
            // ①改訂版認定調査票（概況調査）情報が存在する場合、
            // 改訂版認定調査票（概況調査）情報.利用施設種別を設定する。
            // ②以外の場合、登録しない

            cpnTucGdl4SerH21Record
                    .setShisetsuShu(cpnTucCsc12.getShisetsuShu());

            // 施設名
            // ①改訂版認定調査票（概況調査）情報が存在する場合、
            // ①-1.改訂版認定調査票（概況調査）情報.施設名が空白の場合、施設マスタ（１５－４）情報．施設名を設定する
            // ①-2.以外の場合、改訂版認定調査票（概況調査）情報.施設名を設定する
            // ②以外の場合、更新しない
            if (StringUtils.isEmpty(cpnTucCsc12.getShisetuKnj())) {
                // ※施設マスタ（15-4）情報取得できない場合
                if (Objects.isNull(cpnChoShisetuInfo)) {
                    // 「空白」に設定する
                    cpnTucGdl4SerH21Record
                            .setShisetsuNameKnj(CommonConstants.BLANK_STRING);
                } else {
                    cpnTucGdl4SerH21Record
                            .setShisetsuNameKnj(
                                    cpnChoShisetuInfo.getShisetuKnj());
                }

            } else {
                cpnTucGdl4SerH21Record
                        .setShisetsuNameKnj(
                                cpnTucCsc12.getShisetsuNameKnj());
            }
            // 備考
            // ①改訂版認定調査票（概況調査）情報が存在する場合、
            // ①-1.改訂版認定調査票（概況調査）情報.施設名が空白の場合、施設マスタ（１５－４）情報．住所を設定する
            // ①-2.以外の場合、改訂版認定調査票（概況調査）情報.施設住所を設定する
            // ②以外の場合、更新しない
            if (StringUtils.isEmpty(cpnTucCsc12.getShisetuKnj())) {
                // ※施設マスタ（15-4）情報取得できない場合
                if (Objects.isNull(cpnChoShisetuInfo)) {
                    // 「空白」に設定する
                    cpnTucGdl4SerH21Record
                            .setShisetsuMemoKnj(CommonConstants.BLANK_STRING);
                } else {
                    cpnTucGdl4SerH21Record
                            .setShisetsuMemoKnj(
                                    cpnChoShisetuInfo.getAddressKnj());
                }
            } else {
                cpnTucGdl4SerH21Record
                        .setShisetsuMemoKnj(
                                cpnTucCsc12.getAddressKnj());
            }
            // 電話番号
            // ①改訂版認定調査票（概況調査）情報が存在する場合、
            // ①-1.改訂版認定調査票（概況調査）情報.施設名が空白の場合、施設マスタ情報．TELを設定する
            // ①-2.以外の場合、改訂版認定調査票（概況調査）情報.電話番号を設定する
            // ②以外の場合、更新しない
            if (StringUtils.isEmpty(cpnTucCsc12.getShisetuKnj())) {
                // ※施設マスタ（15-4）情報取得できない場合
                if (Objects.isNull(cpnChoShisetuInfo)) {
                    // 「空白」に設定する
                    cpnTucGdl4SerH21Record
                            .setShisetsuTel(CommonConstants.BLANK_STRING);
                } else {
                    cpnTucGdl4SerH21Record
                            .setShisetsuTel(
                                    cpnChoShisetuInfo.getTel());
                }
            } else {
                cpnTucGdl4SerH21Record
                        .setShisetsuTel(
                                cpnTucCsc12.getShisetsuTel());
            }
            // 成年後見人制度
            cpnTucGdl4SerH21Record
                    .setSeinenKoukenKbn(0);
            // 郵便番号
            // ①改訂版認定調査票（概況調査）情報が存在する場合、
            // ①-1.改訂版認定調査票（概況調査）情報.施設名が空白の場合、施設マスタ（１５－４）情報．郵便番号を設定する
            // ①-2.以外の場合、改訂版認定調査票（概況調査）情報.郵便番号を設定する
            // ②以外の場合、更新しない
            if (StringUtils.isEmpty(cpnTucCsc12.getShisetuKnj())) {
                // ※施設マスタ（15-4）情報取得できない場合
                if (Objects.isNull(cpnChoShisetuInfo)) {
                    // 「空白」に設定する
                    cpnTucGdl4SerH21Record
                            .setShisetsuZip(CommonConstants.BLANK_STRING);
                } else {
                    cpnTucGdl4SerH21Record
                            .setShisetsuZip(
                                    cpnChoShisetuInfo.getZip());
                }
            } else {
                cpnTucGdl4SerH21Record
                        .setShisetsuZip(
                                cpnTucCsc12.getShisetsuZip());
            }
        }
        return cpnTucGdl4SerH21Record;
    }

    /**
     * 【ＧＬ＿サービス利用状況（Ｈ２１改訂）】を更新/登録する
     * 
     * @param inDto
     * @param cpnTucGdl4SerH21
     * @param cpnTucGdl4SerH21Record
     */
    private String updateCpnTucGdl4SerH21(AssessmentHomeChosaInsertServiceInDto inDto,
            CpnTucGdl4SerH21ListOutEntity cpnTucGdl4SerH21, CpnTucGdl4SerH21 cpnTucGdl4SerH21Record) {
        // 3.1.6． 処理3.1.1で検索件数＞0の場合、【ＧＬ＿サービス利用状況（Ｈ２１改訂）】を更新する。
        if (Objects.nonNull(cpnTucGdl4SerH21)) {

            // ■更新条件
            // 計画期間ID＝リクエストパラメータ詳細．計画期間ID
            // アセスメントID＝リクエストパラメータ詳細．アセスメントID
            // 更新回数＝上記処理3.1.1で取得したＧＬ＿サービス利用状況（Ｈ２１改訂）情報.更新回数
            // 削除フラグ = 0（未削除データ）
            final CpnTucGdl4SerH21Criteria cpnTucGdl4SerH21Criteria = new CpnTucGdl4SerH21Criteria();
            cpnTucGdl4SerH21Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            Integer cnt = this.cpnTucGdl4SerH21Mapper
                    .updateByCriteriaSelective(cpnTucGdl4SerH21Record,
                            cpnTucGdl4SerH21Criteria);
            if (cnt == 0) {
                return CommonConstants.FAILURE;
            }

        }
        // 3.1.7．処理3.1.1で検索件数=0の場合、【ＧＬ＿サービス利用状況（Ｈ２１改訂）】を登録する。
        else {
            // アセスメントID
            cpnTucGdl4SerH21Record.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            cpnTucGdl4SerH21Record.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            this.cpnTucGdl4SerH21Mapper.insertSelective(cpnTucGdl4SerH21Record);
        }

        return CommonConstants.SUCCESS;
    }

    /**
     * CpnTucGdl5SerR3 DAOパラメータを作成
     * 
     * @param kaiteihanNinteiCscInfo
     * @param cpnTucCsch
     * @param cpnTucCsc12
     * @param cpnChoShisetuInfo
     * @return
     */
    private CpnTucGdl5SerR3 getCpnTucGdl5SerR3RecordFunc(KaiteihanNinteiCscInfoOutEntity kaiteihanNinteiCscInfo,
            CpnTucCschOutEntity cpnTucCsch, CpnTucCsc12OutEntity cpnTucCsc12,
            CpnChoShisetuInfoOutEntity cpnChoShisetuInfo) {
        // DAOパラメータを作成
        CpnTucGdl5SerR3 cpnTucGdl5SerR3Record = new CpnTucGdl5SerR3();

        // 在宅サービス利用1
        cpnTucGdl5SerR3Record.setSer1Cd(kaiteihanNinteiCscInfo.getService1Cd());
        // 在宅サービス利用2
        cpnTucGdl5SerR3Record.setSer2Cd(kaiteihanNinteiCscInfo.getService2Cd());
        // 在宅サービス利用3
        cpnTucGdl5SerR3Record.setSer3Cd(kaiteihanNinteiCscInfo.getService3Cd());
        // 在宅サービス利用4
        cpnTucGdl5SerR3Record.setSer4Cd(kaiteihanNinteiCscInfo.getService4Cd());
        // 在宅サービス利用5
        cpnTucGdl5SerR3Record.setSer5Cd(kaiteihanNinteiCscInfo.getService5Cd());
        // 在宅サービス利用6
        cpnTucGdl5SerR3Record.setSer6Cd(kaiteihanNinteiCscInfo.getService6Cd());
        // 在宅サービス利用7
        cpnTucGdl5SerR3Record.setSer7Cd(kaiteihanNinteiCscInfo.getService7Cd());
        // 在宅サービス利用8
        cpnTucGdl5SerR3Record.setSer8Cd(kaiteihanNinteiCscInfo.getService8Cd());
        // 在宅サービス利用9
        cpnTucGdl5SerR3Record.setSer9Cd(kaiteihanNinteiCscInfo.getService9Cd());
        // 在宅サービス利用10
        cpnTucGdl5SerR3Record.setSer10Cd(kaiteihanNinteiCscInfo.getService10Cd());
        // 在宅サービス利用11
        cpnTucGdl5SerR3Record.setSer11Cd(kaiteihanNinteiCscInfo.getService11Cd());
        // 在宅サービス利用12
        cpnTucGdl5SerR3Record.setSer12Cd(kaiteihanNinteiCscInfo.getService12Cd());
        // 在宅サービス利用13
        cpnTucGdl5SerR3Record.setSer13Cd(kaiteihanNinteiCscInfo.getService13Cd());
        // 在宅サービス利用14
        cpnTucGdl5SerR3Record.setSer14Cd(kaiteihanNinteiCscInfo.getService14Cd());
        // 在宅サービス利用15
        cpnTucGdl5SerR3Record.setSer15Cd(kaiteihanNinteiCscInfo.getService15Cd());
        // 利用回数1
        cpnTucGdl5SerR3Record.setKaisu1(kaiteihanNinteiCscInfo.getKaisuu1());
        // 利用回数2
        cpnTucGdl5SerR3Record.setKaisu2(kaiteihanNinteiCscInfo.getKaisuu2());
        // 利用回数3
        cpnTucGdl5SerR3Record.setKaisu3(kaiteihanNinteiCscInfo.getKaisuu3());
        // 利用回数4
        cpnTucGdl5SerR3Record.setKaisu4(kaiteihanNinteiCscInfo.getKaisuu4());
        // 利用回数5
        cpnTucGdl5SerR3Record.setKaisu5(kaiteihanNinteiCscInfo.getKaisuu5());
        // 利用回数6
        cpnTucGdl5SerR3Record.setKaisu6(kaiteihanNinteiCscInfo.getKaisuu6());
        // 利用回数7
        cpnTucGdl5SerR3Record.setKaisu7(kaiteihanNinteiCscInfo.getKaisuu7());
        // 利用回数8
        cpnTucGdl5SerR3Record.setKaisu8(kaiteihanNinteiCscInfo.getKaisuu8());
        // 利用回数9
        cpnTucGdl5SerR3Record.setKaisu9(kaiteihanNinteiCscInfo.getKaisuu9());
        // 利用回数10
        cpnTucGdl5SerR3Record.setKaisu10(kaiteihanNinteiCscInfo.getKaisuu10());
        // 利用回数11
        cpnTucGdl5SerR3Record.setKaisu11(kaiteihanNinteiCscInfo.getKaisuu11());
        // 利用回数12
        cpnTucGdl5SerR3Record.setKaisu12(kaiteihanNinteiCscInfo.getKaisuu12());
        // 利用回数13
        cpnTucGdl5SerR3Record.setKaisu13(kaiteihanNinteiCscInfo.getKaisuu13());
        // 夜間対応訪問介護
        cpnTucGdl5SerR3Record.setSer31Cd(kaiteihanNinteiCscInfo.getService17Cd());
        // 夜間対応訪問介護（回数）
        cpnTucGdl5SerR3Record.setKaisu31(kaiteihanNinteiCscInfo.getKaisuu17());
        // 認知症対応型通所介護
        cpnTucGdl5SerR3Record.setSer32Cd(kaiteihanNinteiCscInfo.getService18Cd());
        // 認知症対応型通所介護（回数）
        cpnTucGdl5SerR3Record.setKaisu32(kaiteihanNinteiCscInfo.getKaisuu18());
        // 小規模多機能居宅介護
        cpnTucGdl5SerR3Record.setSer33Cd(kaiteihanNinteiCscInfo.getService19Cd());
        // 小規模多機能居宅介護（回数）
        cpnTucGdl5SerR3Record.setKaisu33(kaiteihanNinteiCscInfo.getKaisuu19());
        // 地域密着型特定施設入居者生活介護
        cpnTucGdl5SerR3Record.setSer34Cd(kaiteihanNinteiCscInfo.getService20Cd());
        // 地域密着型特定施設入居者生活介護（回数）
        cpnTucGdl5SerR3Record.setKaisu34(kaiteihanNinteiCscInfo.getKaisuu20());
        // 地域密着型介護老人福祉施設入居者生活介護
        cpnTucGdl5SerR3Record.setSer35Cd(kaiteihanNinteiCscInfo.getService21Cd());
        // 地域密着型介護老人福祉施設入居者生活介護（回数）
        cpnTucGdl5SerR3Record.setKaisu35(kaiteihanNinteiCscInfo.getKaisuu21());
        // サービス利用状況記載日
        if (Objects.nonNull(cpnTucCsch)) {
            cpnTucGdl5SerR3Record.setSerYmd(cpnTucCsch.getJisshiDateYmd());
        }
        // 複合型サービス
        cpnTucGdl5SerR3Record.setSer36Cd(kaiteihanNinteiCscInfo.getService23Cd());
        // 定期巡回・随時対応型訪問介護看護
        cpnTucGdl5SerR3Record.setSer37Cd(kaiteihanNinteiCscInfo.getService22Cd());
        // 複合型サービス（回数）
        cpnTucGdl5SerR3Record.setKaisu36(kaiteihanNinteiCscInfo.getKaisuu23());
        // 定期巡回・随時対応型訪問介護看護（回数）
        cpnTucGdl5SerR3Record.setKaisu37(kaiteihanNinteiCscInfo.getKaisuu22());
        // 利用回数15
        // ※値がNULLの場合は0をセットする
        cpnTucGdl5SerR3Record
                .setKaisu15(kaiteihanNinteiCscInfo.getKaishuUmu() == null ? 0
                        : kaiteihanNinteiCscInfo.getKaishuUmu());
        // 市町村特別給付
        cpnTucGdl5SerR3Record
                .setTokKyuKnj(kaiteihanNinteiCscInfo.getMemo1Knj());
        // 訪問型サービス
        cpnTucGdl5SerR3Record.setSer38Cd(kaiteihanNinteiCscInfo.getService23Cd());
        // 訪問型サービス（回数）
        cpnTucGdl5SerR3Record.setKaisu38(kaiteihanNinteiCscInfo.getKaisuu23());
        // 通所型サービス
        cpnTucGdl5SerR3Record.setSer39Cd(kaiteihanNinteiCscInfo.getService22Cd());
        // 通所型サービス（回数）
        cpnTucGdl5SerR3Record.setKaisu39(kaiteihanNinteiCscInfo.getKaisuu22());

        // シート「補足」を参照
        // 改訂版認定調査票（サービス利用）情報.要介護状態確定
        // ※0またはNULLの場合、改訂版認定調査票（サービス利用）情報.要介護状態見込みを設定する
        if (CommonConstants.YOKAI_KBN_2_0
                .equals(CommonDtoUtil.objValToString(kaiteihanNinteiCscInfo.getYokaiKbn2()))
                || Objects.nonNull(kaiteihanNinteiCscInfo.getYokaiKbn2())) {
            kaiteihanNinteiCscInfo
                    .setYokaiKbn2(kaiteihanNinteiCscInfo.getYokaiKbn1());
        }

        if ((CommonConstants.YOKAI_KBN_2_11
                .equals(CommonDtoUtil.objValToString(kaiteihanNinteiCscInfo.getYokaiKbn2()))
                || CommonConstants.YOKAI_KBN_2_12
                        .equals(CommonDtoUtil
                                .objValToString(kaiteihanNinteiCscInfo.getYokaiKbn2()))
                || CommonConstants.YOKAI_KBN_2_21
                        .equals(CommonDtoUtil.objValToString(
                                kaiteihanNinteiCscInfo.getYokaiKbn2())))) {

            if (kaiteihanNinteiCscInfo.getService1Cd() == 1) {
                // 在宅サービス利用1
                cpnTucGdl5SerR3Record.setSer1Cd(0);
                // 訪問型サービス
                cpnTucGdl5SerR3Record.setSer38Cd(1);
                // 利用回数1
                cpnTucGdl5SerR3Record.setKaisu1(null);
                // 訪問型サービス（回数）
                cpnTucGdl5SerR3Record.setKaisu38(kaiteihanNinteiCscInfo.getKaisuu1());
            }

            if (kaiteihanNinteiCscInfo.getService6Cd() == 1) {
                // 在宅サービス利用6
                cpnTucGdl5SerR3Record.setSer6Cd(0);
                // 通所型サービス
                cpnTucGdl5SerR3Record.setSer39Cd(1);
                // 利用回数6
                cpnTucGdl5SerR3Record.setKaisu6(null);
                // 通所型サービス（回数）
                cpnTucGdl5SerR3Record.setKaisu39(kaiteihanNinteiCscInfo.getKaisuu6());
            }
        } else {
            if (kaiteihanNinteiCscInfo.getService1Cd() == 1) {
                // 在宅サービス利用1
                cpnTucGdl5SerR3Record.setSer1Cd(1);
                // 訪問型サービス
                cpnTucGdl5SerR3Record.setSer38Cd(0);
                // 訪問型サービス（回数）
                cpnTucGdl5SerR3Record.setKaisu38(null);
            }

            if (kaiteihanNinteiCscInfo.getService6Cd() == 1) {
                // 在宅サービス利用6
                cpnTucGdl5SerR3Record.setSer6Cd(1);
                // 通所型サービス
                cpnTucGdl5SerR3Record.setSer39Cd(0);
                // 通所型サービス（回数）
                cpnTucGdl5SerR3Record.setKaisu39(null);
            }
        }

        // ①改訂版認定調査票（概況調査）情報が存在する場合、
        if (Objects.nonNull(cpnTucCsc12)) {
            // 利用施設種別
            // 改訂版認定調査票（概況調査）情報.利用施設種別 = 9 の場合、
            Integer shisetsuShuTmp = null;
            if (CommonConstants.INT_9 == cpnTucCsc12.getShisetsuShu()) {
                // 3を設定する
                shisetsuShuTmp = CommonConstants.INT_3;
                // 以外の場合、
            } else {
                // 改訂版認定調査票（概況調査）情報.利用施設種別
                shisetsuShuTmp = cpnTucCsc12.getShisetsuShu();
            }
            cpnTucGdl5SerR3Record
                    .setShisetsuShu(shisetsuShuTmp);
            // ②以外の場合、更新しない

            // 施設名
            // ①改訂版認定調査票（概況調査）情報が存在する場合、
            // ①-1.改訂版認定調査票（概況調査）情報.施設名が空白の場合、施設マスタ（１５－４）情報．施設名を設定する
            // ①-2.以外の場合、改訂版認定調査票（概況調査）情報.施設名を設定する
            // ②以外の場合、更新しない
            if (StringUtils.isEmpty(cpnTucCsc12.getShisetuKnj())) {
                // ※施設マスタ（15-4）情報取得できない場合
                if (Objects.isNull(cpnChoShisetuInfo)) {
                    // 「空白」に設定する
                    cpnTucGdl5SerR3Record
                            .setShisetsuNameKnj(CommonConstants.BLANK_STRING);
                } else {
                    cpnTucGdl5SerR3Record
                            .setShisetsuNameKnj(
                                    cpnChoShisetuInfo.getShisetuKnj());
                }
            } else {
                cpnTucGdl5SerR3Record
                        .setShisetsuNameKnj(
                                cpnTucCsc12.getShisetsuNameKnj());
            }
            // 備考
            // ①改訂版認定調査票（概況調査）情報が存在する場合、
            // ①-1.改訂版認定調査票（概況調査）情報.施設名が空白の場合、施設マスタ（１５－４）情報．住所を設定する
            // ①-2.以外の場合、改訂版認定調査票（概況調査）情報.施設住所を設定する
            // ②以外の場合、更新しない
            if (StringUtils.isEmpty(cpnTucCsc12.getShisetuKnj())) {
                // ※施設マスタ（15-4）情報取得できない場合
                if (Objects.isNull(cpnChoShisetuInfo)) {
                    // 「空白」に設定する
                    cpnTucGdl5SerR3Record
                            .setShisetsuMemoKnj(CommonConstants.BLANK_STRING);
                } else {
                    cpnTucGdl5SerR3Record
                            .setShisetsuMemoKnj(
                                    cpnChoShisetuInfo.getAddressKnj());
                }
            } else {
                cpnTucGdl5SerR3Record
                        .setShisetsuMemoKnj(
                                cpnTucCsc12.getAddressKnj());
            }
            // 電話番号
            // ①改訂版認定調査票（概況調査）情報が存在する場合、
            // ①-1.改訂版認定調査票（概況調査）情報.施設名が空白の場合、施設マスタ情報．TELを設定する
            // ①-2.以外の場合、改訂版認定調査票（概況調査）情報.電話番号を設定する
            // ②以外の場合、更新しない
            if (StringUtils.isEmpty(cpnTucCsc12.getShisetuKnj())) {
                // ※施設マスタ（15-4）情報取得できない場合
                if (Objects.isNull(cpnChoShisetuInfo)) {
                    // 「空白」に設定する
                    cpnTucGdl5SerR3Record
                            .setShisetsuTel(CommonConstants.BLANK_STRING);
                } else {
                    cpnTucGdl5SerR3Record
                            .setShisetsuTel(
                                    cpnChoShisetuInfo.getTel());
                }
            } else {
                cpnTucGdl5SerR3Record
                        .setShisetsuTel(
                                cpnTucCsc12.getShisetsuTel());
            }
            // 成年後見人制度
            // ①改訂版認定調査票（概況調査）情報が存在する場合、0を設定する
            // ②以外の場合、更新しない
            cpnTucGdl5SerR3Record
                    .setSeinenKoukenKbn(0);

            // 郵便番号
            // ①改訂版認定調査票（概況調査）情報が存在する場合、
            // ①-1.改訂版認定調査票（概況調査）情報.施設名が空白の場合、施設マスタ（１５－４）情報．郵便番号を設定する
            // ①-2.以外の場合、改訂版認定調査票（概況調査）情報.郵便番号を設定する
            // ②以外の場合、更新しない
            if (StringUtils.isEmpty(cpnTucCsc12.getShisetuKnj())) {
                // ※施設マスタ（15-4）情報取得できない場合
                if (Objects.isNull(cpnChoShisetuInfo)) {
                    // 「空白」に設定する
                    cpnTucGdl5SerR3Record
                            .setShisetsuZip(CommonConstants.BLANK_STRING);
                } else {
                    cpnTucGdl5SerR3Record
                            .setShisetsuZip(
                                    cpnChoShisetuInfo.getZip());
                }
            } else {
                cpnTucGdl5SerR3Record
                        .setShisetsuZip(
                                cpnTucCsc12.getShisetsuZip());
            }
        }
        return cpnTucGdl5SerR3Record;
    }

    /**
     * 【ＧＬ＿サービス利用状況（R３改訂）】を更新/登録する
     * 
     * @param inDto
     * @param gdl5Ass3R3
     * @param cpnTucGdl5SerR3Record
     */
    private String updateCpnTucGdl5SerR3(AssessmentHomeChosaInsertServiceInDto inDto, Gdl5Ass3R3OutEntity gdl5Ass3R3,
            CpnTucGdl5SerR3 cpnTucGdl5SerR3Record) {
        // 3.1.8． 処理3.1.2で検索件数＞0の場合、【ＧＬ＿サービス利用状況（R３改訂）】を更新する。
        if (Objects.nonNull(gdl5Ass3R3)) {
            final CpnTucGdl5SerR3Criteria cpnTucGdl5SerR3Criteria = new CpnTucGdl5SerR3Criteria();
            // ■更新条件
            // 計画期間ID＝リクエストパラメータ詳細．計画期間ID
            // アセスメントID＝リクエストパラメータ詳細．アセスメントID
            // 更新回数＝上記処理3.1.2で取得したＧＬ＿サービス利用状況（R３改訂）情報.更新回数
            // 削除フラグ = 0（未削除データ）
            cpnTucGdl5SerR3Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            Integer cnt = this.cpnTucGdl5SerR3Mapper
                    .updateByCriteriaSelective(cpnTucGdl5SerR3Record,
                            cpnTucGdl5SerR3Criteria);

            if (cnt == 0) {
                return CommonConstants.FAILURE;
            }
        }
        // 3.1.9． 処理3.1.2で検索件数=0の場合、【ＧＬ＿サービス利用状況（R３改訂）】を登録する。
        else {

            // アセスメントID
            cpnTucGdl5SerR3Record.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            cpnTucGdl5SerR3Record.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            this.cpnTucGdl5SerR3Mapper.insertSelective(cpnTucGdl5SerR3Record);
        }

        return CommonConstants.SUCCESS;
    }

    /**
     * Ｈ２１改訂版認定調査票（基本）情報を取得する
     * 
     * @param inDto
     * @return
     */
    private CpnTucCsc3H21OutEntity findCpnTucCsc3H21Func(AssessmentHomeChosaInsertServiceInDto inDto) {

        // DAOパラメータを作成
        CpnTucCsc3H21ByCriteriaInEntity cpnTucCsc3H21ByCriteriaInEntity = new CpnTucCsc3H21ByCriteriaInEntity();
        // 調査票ID
        cpnTucCsc3H21ByCriteriaInEntity.setCschId(CommonDtoUtil.strValToInt(inDto.getCschId()));
        cpnTucCsc3H21ByCriteriaInEntity.setSc1Id(inDto.getSc1Id());
        // DAOを実行
        List<CpnTucCsc3H21OutEntity> cpnTucCsc3H21List = this.cpnTucCsc3H21SelectMapper
                .findCpnTucCsc3H21ByCriteria(cpnTucCsc3H21ByCriteriaInEntity);

        Optional<CpnTucCsc3H21OutEntity> cpnTucCsc3H21Optional = cpnTucCsc3H21List.stream().findFirst();

        if (cpnTucCsc3H21Optional.isPresent()) {
            return cpnTucCsc3H21Optional.get();
        }

        return null;
    }

    /**
     * ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）情報を取得する
     * 
     * @param inDto
     * @return
     */
    private CpnTucGdl4Kan11H21OutEntity findCpnTucGdl4Kan11H21Func(AssessmentHomeChosaInsertServiceInDto inDto) {

        // DAOパラメータを作成
        CpnTucGdl4Kan11H21ByCriteriaInEntity cpnTucGdl4Kan11H21ByCriteriaInEntity = new CpnTucGdl4Kan11H21ByCriteriaInEntity();
        // 計画期間ID
        cpnTucGdl4Kan11H21ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // アセスメントID
        cpnTucGdl4Kan11H21ByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // DAOを実行
        List<CpnTucGdl4Kan11H21OutEntity> cpnTucGdl4Kan11H21List = this.cpnTucGdl4Kan11H21SelectMapper
                .findCpnTucGdl4Kan11H21ByCriteria(cpnTucGdl4Kan11H21ByCriteriaInEntity);

        Optional<CpnTucGdl4Kan11H21OutEntity> cpnTucGdl4Kan11H21Optional = cpnTucGdl4Kan11H21List
                .stream().findFirst();

        if (cpnTucGdl4Kan11H21Optional.isPresent()) {
            return cpnTucGdl4Kan11H21Optional.get();
        }
        return null;
    }

    /**
     * 【ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）】を更新/登録する
     * 
     * @param inDto
     * @param cpnTucGdl4Kan11H21
     * @param cpnTucGdl4Kan11H21Record
     */
    private String updateCpnTucGdl4Kan11H21(AssessmentHomeChosaInsertServiceInDto inDto,
            CpnTucGdl4Kan11H21OutEntity cpnTucGdl4Kan11H21, CpnTucGdl4Kan11H21 cpnTucGdl4Kan11H21Record) {

        // 2．検索件数>0の場合、【ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）】を更新する。
        if (Objects.nonNull(cpnTucGdl4Kan11H21)) {
            final CpnTucGdl4Kan11H21Criteria cpnTucGdl4Kan11H21Criteria = new CpnTucGdl4Kan11H21Criteria();

            // ■更新条件
            // 計画期間ID＝リクエストパラメータ詳細．計画期間ID
            // アセスメントID＝リクエストパラメータ詳細．アセスメントID
            // 更新回数＝上記処理1で取得したＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）情報.更新回数
            // 削除フラグ = 0（未削除データ）
            cpnTucGdl4Kan11H21Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            Integer cnt = this.cpnTucGdl4Kan11H21Mapper
                    .updateByCriteriaSelective(cpnTucGdl4Kan11H21Record,
                            cpnTucGdl4Kan11H21Criteria);
            if (cnt == 0) {
                return CommonConstants.FAILURE;
            }

        }
        // 3．以外の場合、【ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）】を登録する。
        else {
            // アセスメントID
            cpnTucGdl4Kan11H21Record.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            cpnTucGdl4Kan11H21Record.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            this.cpnTucGdl4Kan11H21Mapper.insertSelective(cpnTucGdl4Kan11H21Record);
        }

        return CommonConstants.SUCCESS;
    }

    /**
     * CpnTucGdl4Kan11H21 DAOパラメータを作成
     * 
     * @param cpnTucCsc3H21
     * @return
     */
    private CpnTucGdl4Kan11H21 getCpnTucGdl4Kan11H21RecordFunc(CpnTucCsc3H21OutEntity cpnTucCsc3H21) {
        // DAOパラメータを作成
        CpnTucGdl4Kan11H21 cpnTucGdl4Kan11H21Record = new CpnTucGdl4Kan11H21();
        // 認定項目1-1_1(麻痺等（複数可）
        cpnTucGdl4Kan11H21Record
                .setBango1C1I1(cpnTucCsc3H21.getBango1C1I1() == null ? 0
                        : cpnTucCsc3H21.getBango1C1I1());
        // 認定項目1-1_2(麻痺等（複数可）
        cpnTucGdl4Kan11H21Record
                .setBango1C1I2(cpnTucCsc3H21.getBango1C1I2() == null ? 0
                        : cpnTucCsc3H21.getBango1C1I2());
        // 認定項目1-1_3(麻痺等（複数可）
        cpnTucGdl4Kan11H21Record
                .setBango1C1I3(cpnTucCsc3H21.getBango1C1I3() == null ? 0
                        : cpnTucCsc3H21.getBango1C1I3());
        // 認定項目1-1_4(麻痺等（複数可）
        cpnTucGdl4Kan11H21Record
                .setBango1C1I4(cpnTucCsc3H21.getBango1C1I4() == null ? 0
                        : cpnTucCsc3H21.getBango1C1I4());
        // 認定項目1-1_5(麻痺等（複数可）
        cpnTucGdl4Kan11H21Record
                .setBango1C1I5(cpnTucCsc3H21.getBango1C1I5() == null ? 0
                        : cpnTucCsc3H21.getBango1C1I5());
        // 認定項目1-1_6(麻痺等（複数可）
        cpnTucGdl4Kan11H21Record
                .setBango1C1I6(cpnTucCsc3H21.getBango1C1I6() == null ? 0
                        : cpnTucCsc3H21.getBango1C1I6());
        // 認定項目1-2_1(拘縮（複数可）
        cpnTucGdl4Kan11H21Record
                .setBango1C2I1(cpnTucCsc3H21.getBango1C2I1() == null ? 0
                        : cpnTucCsc3H21.getBango1C2I1());
        // 認定項目1-2_2(拘縮（複数可）
        cpnTucGdl4Kan11H21Record
                .setBango1C2I2(cpnTucCsc3H21.getBango1C2I2() == null ? 0
                        : cpnTucCsc3H21.getBango1C2I2());
        // 認定項目1-2_3(拘縮（複数可）
        cpnTucGdl4Kan11H21Record
                .setBango1C2I3(cpnTucCsc3H21.getBango1C2I3() == null ? 0
                        : cpnTucCsc3H21.getBango1C2I3());
        // 認定項目1-2_4(拘縮（複数可）
        cpnTucGdl4Kan11H21Record
                .setBango1C2I4(cpnTucCsc3H21.getBango1C2I4() == null ? 0
                        : cpnTucCsc3H21.getBango1C2I4());
        // 認定項目1-2_5(拘縮（複数可）
        cpnTucGdl4Kan11H21Record
                .setBango1C2I5(cpnTucCsc3H21.getBango1C2I5() == null ? 0
                        : cpnTucCsc3H21.getBango1C2I5());
        // 認定項目1-3（寝返り）
        cpnTucGdl4Kan11H21Record.setBango1C3(cpnTucCsc3H21.getBango1C3() == null ? 0
                : cpnTucCsc3H21.getBango1C3());
        // 認定項目1-4（起き上がり）
        cpnTucGdl4Kan11H21Record.setBango1C4(cpnTucCsc3H21.getBango1C4() == null ? 0
                : cpnTucCsc3H21.getBango1C4());
        // 認定項目1-5（座位保持）
        cpnTucGdl4Kan11H21Record.setBango1C5(cpnTucCsc3H21.getBango1C5() == null ? 0
                : cpnTucCsc3H21.getBango1C5());
        // 認定項目1-6（両足での立位保持）
        cpnTucGdl4Kan11H21Record.setBango1C6(cpnTucCsc3H21.getBango1C6() == null ? 0
                : cpnTucCsc3H21.getBango1C6());
        // 認定項目1-7（歩行）
        cpnTucGdl4Kan11H21Record.setBango1C7(cpnTucCsc3H21.getBango1C7() == null ? 0
                : cpnTucCsc3H21.getBango1C7());
        // 認定項目1-8（立ち上がり）
        cpnTucGdl4Kan11H21Record.setBango1C8(cpnTucCsc3H21.getBango1C8() == null ? 0
                : cpnTucCsc3H21.getBango1C8());
        // 認定項目1-9（片足での立位保持）
        cpnTucGdl4Kan11H21Record.setBango1C9(cpnTucCsc3H21.getBango1C9() == null ? 0
                : cpnTucCsc3H21.getBango1C9());
        // 認定項目1-10（洗身）
        cpnTucGdl4Kan11H21Record
                .setBango1C10(cpnTucCsc3H21.getBango1C10() == null ? 0
                        : cpnTucCsc3H21.getBango1C10());
        // 認定項目1-11（つめ切り）
        cpnTucGdl4Kan11H21Record
                .setBango1C11(cpnTucCsc3H21.getBango1C11() == null ? 0
                        : cpnTucCsc3H21.getBango1C11());
        // 認定項目1-12（視力）
        cpnTucGdl4Kan11H21Record
                .setBango1C12(cpnTucCsc3H21.getBango1C12() == null ? 0
                        : cpnTucCsc3H21.getBango1C12());
        // 認定項目1-13（聴力）
        cpnTucGdl4Kan11H21Record
                .setBango1C13(cpnTucCsc3H21.getBango1C13() == null ? 0
                        : cpnTucCsc3H21.getBango1C13());

        return cpnTucGdl4Kan11H21Record;
    }

    /**
     * ＧＬ＿①基本（身体機能・起居）動作（Ｒ３改訂）情報を取得する
     * 
     * @param inDto
     * @return
     */
    private Gdl5Ass6R3OutEntity findGdl5Ass6R3Func(AssessmentHomeChosaInsertServiceInDto inDto) {

        // DAOパラメータを作成
        Gdl5Ass6R3ByCriteriaInEntity gdl5Ass6R3ByCriteriaInEntity = new Gdl5Ass6R3ByCriteriaInEntity();
        // 計画期間ID
        gdl5Ass6R3ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // アセスメントID
        gdl5Ass6R3ByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // DAOを実行
        List<Gdl5Ass6R3OutEntity> gdl5Ass6R3List = this.cpnTucGdl5Kan11R3SelectMapper
                .findGdl5Ass6R3ByCriteria(gdl5Ass6R3ByCriteriaInEntity);

        Optional<Gdl5Ass6R3OutEntity> gdl5Ass6R3Optional = gdl5Ass6R3List
                .stream().findFirst();
        if (gdl5Ass6R3Optional.isPresent()) {
            return gdl5Ass6R3Optional.get();
        }

        return null;
    }

    /**
     * 【ＧＬ＿①基本（身体機能・起居）動作（Ｒ３改訂）】を更新/登録する
     * 
     * @param inDto
     * @param gdl5Ass6R3
     * @param cpnTucGdl5Kan11R3Record
     */
    private String updateGdl5Ass6R3(AssessmentHomeChosaInsertServiceInDto inDto, Gdl5Ass6R3OutEntity gdl5Ass6R3,
            CpnTucGdl5Kan11R3 cpnTucGdl5Kan11R3Record) {
        // 2．検索件数>0の場合、【ＧＬ＿①基本（身体機能・起居）動作（Ｒ３改訂）】を更新する。
        if (Objects.nonNull(gdl5Ass6R3)) {

            final CpnTucGdl5Kan11R3Criteria cpnTucGdl5Kan11R3Criteria = new CpnTucGdl5Kan11R3Criteria();
            // ■更新条件
            // 計画期間ID＝リクエストパラメータ詳細．計画期間ID
            // アセスメントID＝リクエストパラメータ詳細．アセスメントID
            // 更新回数＝上記処理1で取得したＧＬ＿①基本（身体機能・起居）動作（Ｒ３改訂）情報.更新回数
            // 削除フラグ = 0（未削除データ）
            cpnTucGdl5Kan11R3Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            Integer cnt = this.cpnTucGdl5Kan11R3Mapper
                    .updateByCriteriaSelective(cpnTucGdl5Kan11R3Record,
                            cpnTucGdl5Kan11R3Criteria);

            if (cnt == 0) {
                return CommonConstants.FAILURE;
            }
        }
        // 3．以外の場合、【ＧＬ＿①基本（身体機能・起居）動作（Ｒ３改訂）】を登録する。
        else {
            // アセスメントID
            cpnTucGdl5Kan11R3Record.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            cpnTucGdl5Kan11R3Record.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            this.cpnTucGdl5Kan11R3Mapper.insertSelective(cpnTucGdl5Kan11R3Record);
        }

        return CommonConstants.SUCCESS;
    }

    /**
     * ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）情報を取得する
     * 
     * @param inDto
     * @return
     */
    private CpnTucGdl4Kan12H21OutEntity findCpnTucGdl4Kan12H21Func(AssessmentHomeChosaInsertServiceInDto inDto) {

        // DAOパラメータを作成
        CpnTucGdl4Kan12H21ByCriteriaInEntity cpnTucGdl4Kan12H21ByCriteriaInEntity = new CpnTucGdl4Kan12H21ByCriteriaInEntity();
        // 計画期間ID
        cpnTucGdl4Kan12H21ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // アセスメントID
        cpnTucGdl4Kan12H21ByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // DAOを実行
        List<CpnTucGdl4Kan12H21OutEntity> cpnTucGdl4Kan12H21List = this.cpnTucGdl4Kan12H21SelectMapper
                .findCpnTucGdl4Kan12H21ByCriteria(cpnTucGdl4Kan12H21ByCriteriaInEntity);

        Optional<CpnTucGdl4Kan12H21OutEntity> cpnTucGdl4Kan12H21Optional = cpnTucGdl4Kan12H21List
                .stream().findFirst();

        if (cpnTucGdl4Kan12H21Optional.isPresent()) {
            return cpnTucGdl4Kan12H21Optional.get();
        }

        return null;
    }

    /**
     * CpnTucGdl4Kan12H21 DAOパラメータを作成
     * 
     * @param cpnTucCsc3H21
     * @return
     */
    private CpnTucGdl4Kan12H21 getCpnTucGdl4Kan12H21RecordFunc(CpnTucCsc3H21OutEntity cpnTucCsc3H21) {
        // DAOパラメータを作成
        CpnTucGdl4Kan12H21 CpnTucGdl4Kan12H21Record = new CpnTucGdl4Kan12H21();
        // 認定項目2-1_1（移乗）
        CpnTucGdl4Kan12H21Record
                .setBango21(cpnTucCsc3H21.getBango2C1() == null ? 0 : cpnTucCsc3H21.getBango2C1());
        // 認定項目2-1_2（移動）
        CpnTucGdl4Kan12H21Record
                .setBango22(cpnTucCsc3H21.getBango2C2() == null ? 0 : cpnTucCsc3H21.getBango2C2());
        // 認定項目2-1_3（えん下）
        CpnTucGdl4Kan12H21Record
                .setBango23(cpnTucCsc3H21.getBango2C3() == null ? 0 : cpnTucCsc3H21.getBango2C3());
        // 認定項目2-1_4（食事摂取）
        CpnTucGdl4Kan12H21Record
                .setBango24(cpnTucCsc3H21.getBango2C4() == null ? 0 : cpnTucCsc3H21.getBango2C4());
        // 認定項目2-1_5（排尿）
        CpnTucGdl4Kan12H21Record
                .setBango25(cpnTucCsc3H21.getBango2C5() == null ? 0 : cpnTucCsc3H21.getBango2C5());
        // 認定項目2-1_6（排便）
        CpnTucGdl4Kan12H21Record
                .setBango26(cpnTucCsc3H21.getBango2C6() == null ? 0 : cpnTucCsc3H21.getBango2C6());
        // 認定項目2-1_7（口腔清潔）
        CpnTucGdl4Kan12H21Record
                .setBango27(cpnTucCsc3H21.getBango2C7() == null ? 0 : cpnTucCsc3H21.getBango2C7());
        // 認定項目2-1_8（洗顔）
        CpnTucGdl4Kan12H21Record
                .setBango28(cpnTucCsc3H21.getBango2C8() == null ? 0 : cpnTucCsc3H21.getBango2C8());
        // 認定項目2-1_9（整髪）
        CpnTucGdl4Kan12H21Record
                .setBango29(cpnTucCsc3H21.getBango2C9() == null ? 0 : cpnTucCsc3H21.getBango2C9());
        // 認定項目2-1_10（上衣の着脱）
        CpnTucGdl4Kan12H21Record
                .setBango210(cpnTucCsc3H21.getBango2C10() == null ? 0 : cpnTucCsc3H21.getBango2C10());
        // 認定項目2-1_11（ズボン等の着脱）
        CpnTucGdl4Kan12H21Record
                .setBango211(cpnTucCsc3H21.getBango2C11() == null ? 0 : cpnTucCsc3H21.getBango2C11());
        // 認定項目2-1_12（外出の頻度）
        CpnTucGdl4Kan12H21Record
                .setBango212(cpnTucCsc3H21.getBango2C12() == null ? 0 : cpnTucCsc3H21.getBango2C12());

        return CpnTucGdl4Kan12H21Record;
    }

    /**
     * 【ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）】を更新/登録する
     * 
     * @param inDto
     * @param cpnTucGdl4Kan12H21
     * @param cpnTucGdl4Kan12H21Record
     */
    private String updateCpnTucGdl4Kan12H21(AssessmentHomeChosaInsertServiceInDto inDto,
            CpnTucGdl4Kan12H21OutEntity cpnTucGdl4Kan12H21, CpnTucGdl4Kan12H21 cpnTucGdl4Kan12H21Record) {
        // 2．検索件数>0の場合、【ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）】を更新する。
        if (Objects.nonNull(cpnTucGdl4Kan12H21)) {

            final CpnTucGdl4Kan12H21Criteria cpnTucGdl4Kan12H21Criteria = new CpnTucGdl4Kan12H21Criteria();
            // ■更新条件
            // 計画期間ID＝リクエストパラメータ詳細．計画期間ID
            // アセスメントID＝リクエストパラメータ詳細．アセスメントID
            // 更新回数＝上記処理1で取得したＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）情報.更新回数
            // 削除フラグ = 0（未削除データ）
            cpnTucGdl4Kan12H21Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            Integer cnt = this.cpnTucGdl4Kan12H21Mapper
                    .updateByCriteriaSelective(cpnTucGdl4Kan12H21Record,
                            cpnTucGdl4Kan12H21Criteria);

            if (cnt == 0) {
                return CommonConstants.FAILURE;
            }
        }
        // 3．以外の場合、【ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）】を登録する。
        else {
            // アセスメントID
            cpnTucGdl4Kan12H21Record.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            cpnTucGdl4Kan12H21Record.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            this.cpnTucGdl4Kan12H21Mapper.insertSelective(cpnTucGdl4Kan12H21Record);
        }
        return CommonConstants.SUCCESS;
    }

    /**
     * ＧＬ＿②生活機能（食事・排泄）（Ｒ３改訂）情報を取得する
     * 
     * @param inDto
     * @return
     */
    private Gdl5Ass7R3OutEntity findGdl5Ass7R3Func(AssessmentHomeChosaInsertServiceInDto inDto) {

        // DAOパラメータを作成
        Gdl5Ass7R3ByCriteriaInEntity gdl5Ass7R3ByCriteriaInEntity = new Gdl5Ass7R3ByCriteriaInEntity();
        // 計画期間ID
        gdl5Ass7R3ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // アセスメントID
        gdl5Ass7R3ByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // DAOを実行
        List<Gdl5Ass7R3OutEntity> gdl5Ass7R3List = this.cpnTucGdl5Kan12R3SelectMapper
                .findGdl5Ass7R3ByCriteria(gdl5Ass7R3ByCriteriaInEntity);

        Optional<Gdl5Ass7R3OutEntity> gdl5Ass7R3Optional = gdl5Ass7R3List
                .stream().findFirst();

        if (gdl5Ass7R3Optional.isPresent()) {
            return gdl5Ass7R3Optional.get();
        }
        return null;
    }

    /**
     * CpnTucGdl5Kan12R3 DAOパラメータを作成
     * 
     * @param cpnTucCsc3H21
     * @return
     */
    private CpnTucGdl5Kan12R3 getCpnTucGdl5Kan12R3RecordFunc(CpnTucCsc3H21OutEntity cpnTucCsc3H21) {
        //
        CpnTucGdl5Kan12R3 CpnTucGdl5Kan12R3Record = new CpnTucGdl5Kan12R3();
        // 認定項目2-1_1（移乗）
        CpnTucGdl5Kan12R3Record
                .setBango21(cpnTucCsc3H21.getBango2C1() == null ? 0 : cpnTucCsc3H21.getBango2C1());
        // 認定項目2-1_2（移動）
        CpnTucGdl5Kan12R3Record
                .setBango22(cpnTucCsc3H21.getBango2C2() == null ? 0 : cpnTucCsc3H21.getBango2C2());
        // 認定項目2-1_3（えん下）
        CpnTucGdl5Kan12R3Record
                .setBango23(cpnTucCsc3H21.getBango2C3() == null ? 0 : cpnTucCsc3H21.getBango2C3());
        // 認定項目2-1_4（食事摂取）
        CpnTucGdl5Kan12R3Record
                .setBango24(cpnTucCsc3H21.getBango2C4() == null ? 0 : cpnTucCsc3H21.getBango2C4());
        // 認定項目2-1_5（排尿）
        CpnTucGdl5Kan12R3Record
                .setBango25(cpnTucCsc3H21.getBango2C5() == null ? 0 : cpnTucCsc3H21.getBango2C5());
        // 認定項目2-1_6（排便）
        CpnTucGdl5Kan12R3Record
                .setBango26(cpnTucCsc3H21.getBango2C6() == null ? 0 : cpnTucCsc3H21.getBango2C6());
        // 認定項目2-1_7（口腔清潔）
        CpnTucGdl5Kan12R3Record
                .setBango27(cpnTucCsc3H21.getBango2C7() == null ? 0 : cpnTucCsc3H21.getBango2C7());
        // 認定項目2-1_8（洗顔）
        CpnTucGdl5Kan12R3Record
                .setBango28(cpnTucCsc3H21.getBango2C8() == null ? 0 : cpnTucCsc3H21.getBango2C8());
        // 認定項目2-1_9（整髪）
        CpnTucGdl5Kan12R3Record
                .setBango29(cpnTucCsc3H21.getBango2C9() == null ? 0 : cpnTucCsc3H21.getBango2C9());
        // 認定項目2-1_10（上衣の着脱）
        CpnTucGdl5Kan12R3Record
                .setBango210(cpnTucCsc3H21.getBango2C10() == null ? 0 : cpnTucCsc3H21.getBango2C10());
        // 認定項目2-1_11（ズボン等の着脱）
        CpnTucGdl5Kan12R3Record
                .setBango211(cpnTucCsc3H21.getBango2C11() == null ? 0 : cpnTucCsc3H21.getBango2C11());
        // 認定項目2-1_12（外出の頻度）
        CpnTucGdl5Kan12R3Record
                .setBango212(cpnTucCsc3H21.getBango2C12() == null ? 0 : cpnTucCsc3H21.getBango2C12());

        return CpnTucGdl5Kan12R3Record;
    }

    /**
     * 【ＧＬ＿②生活機能（食事・排泄）（Ｒ３改訂）】を更新/登録する
     * 
     * @param inDto
     * @param gdl5Ass7R3
     * @param cpnTucGdl5Kan12R3Record
     */
    private String updateCpnTucGdl5Kan12R3(AssessmentHomeChosaInsertServiceInDto inDto, Gdl5Ass7R3OutEntity gdl5Ass7R3,
            CpnTucGdl5Kan12R3 cpnTucGdl5Kan12R3Record) {
        // 2．検索件数>0の場合、【ＧＬ＿②生活機能（食事・排泄）（Ｒ３改訂）】を更新する。
        if (Objects.nonNull(gdl5Ass7R3)) {

            final CpnTucGdl5Kan12R3Criteria cpnTucGdl5Kan12R3Criteria = new CpnTucGdl5Kan12R3Criteria();
            // ■更新条件
            // 計画期間ID＝リクエストパラメータ詳細．計画期間ID
            // アセスメントID＝リクエストパラメータ詳細．アセスメントID
            // 更新回数＝上記処理1で取得したＧＬ＿②生活機能（食事・排泄）（Ｒ３改訂）情報.更新回数
            // 削除フラグ = 0（未削除データ）
            cpnTucGdl5Kan12R3Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            Integer cnt = this.cpnTucGdl5Kan12R3Mapper
                    .updateByCriteriaSelective(cpnTucGdl5Kan12R3Record,
                            cpnTucGdl5Kan12R3Criteria);

            if (cnt == 0) {
                return CommonConstants.FAILURE;
            }
        }
        // 3．以外の場合、【ＧＬ＿②生活機能（食事・排泄）（Ｒ３改訂）】を登録する。
        else {

            // アセスメントID
            cpnTucGdl5Kan12R3Record.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            cpnTucGdl5Kan12R3Record.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            this.cpnTucGdl5Kan12R3Mapper.insertSelective(cpnTucGdl5Kan12R3Record);
        }
        return CommonConstants.SUCCESS;
    }

    /**
     * ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）情報を取得する
     * 
     * @param inDto
     * @return
     */
    private CpnTucGdl4Kan13H21ListOutEntity findCpnTucGdl4Kan13H21ListFunc(
            AssessmentHomeChosaInsertServiceInDto inDto) {

        // DAOパラメータを作成
        CpnTucGdl4Kan13H21ListByCriteriaInEntity cpnTucGdl4Kan13H21ListByCriteriaInEntity = new CpnTucGdl4Kan13H21ListByCriteriaInEntity();
        // 計画期間ID
        cpnTucGdl4Kan13H21ListByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // アセスメントID
        cpnTucGdl4Kan13H21ListByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // DAOを実行
        List<CpnTucGdl4Kan13H21ListOutEntity> cpnTucGdl4Kan13H21List = this.cpnTucGdl4Kan13H21SelectMapper
                .findCpnTucGdl4Kan13H21ListByCriteria(cpnTucGdl4Kan13H21ListByCriteriaInEntity);

        Optional<CpnTucGdl4Kan13H21ListOutEntity> cpnTucGdl4Kan13H21Optional = cpnTucGdl4Kan13H21List
                .stream().findFirst();

        if (cpnTucGdl4Kan13H21Optional.isPresent()) {
            return cpnTucGdl4Kan13H21Optional.get();
        }
        return null;
    }

    /**
     * CpnTucGdl4Kan13H21 DAOパラメータを作成
     * 
     * @param cpnTucCsc3H21
     * @return
     */
    private CpnTucGdl4Kan13H21 getCpnTucGdl4Kan13H21RecordFunc(CpnTucCsc3H21OutEntity cpnTucCsc3H21) {

        // DAOパラメータを作成
        CpnTucGdl4Kan13H21 cpnTucGdl4Kan13H21Record = new CpnTucGdl4Kan13H21();
        // 認定項目3-1（意思の伝達）
        cpnTucGdl4Kan13H21Record
                .setBango31(cpnTucCsc3H21.getBango3C1() == null ? 0 : cpnTucCsc3H21.getBango3C1());
        // 認定項目3-2（毎日の日課を理解する）
        cpnTucGdl4Kan13H21Record
                .setBango32(cpnTucCsc3H21.getBango3C2() == null ? 0 : cpnTucCsc3H21.getBango3C2());
        // 認定項目3-3（生年月日や年齢を答える）
        cpnTucGdl4Kan13H21Record
                .setBango33(cpnTucCsc3H21.getBango3C3() == null ? 0 : cpnTucCsc3H21.getBango3C3());
        // 認定項目3-4（面接調査の直前記憶）
        cpnTucGdl4Kan13H21Record
                .setBango34(cpnTucCsc3H21.getBango3C4() == null ? 0 : cpnTucCsc3H21.getBango3C4());
        // 認定項目3-5（自分の名前を答える）
        cpnTucGdl4Kan13H21Record
                .setBango35(cpnTucCsc3H21.getBango3C5() == null ? 0 : cpnTucCsc3H21.getBango3C5());
        // 認定項目3-6（今の季節を理解する）
        cpnTucGdl4Kan13H21Record
                .setBango36(cpnTucCsc3H21.getBango3C6() == null ? 0 : cpnTucCsc3H21.getBango3C6());
        // 認定項目3-7（自分のいる場所を答える）
        cpnTucGdl4Kan13H21Record
                .setBango37(cpnTucCsc3H21.getBango3C7() == null ? 0 : cpnTucCsc3H21.getBango3C7());
        // 認定項目3-8（徘徊）
        cpnTucGdl4Kan13H21Record
                .setBango38(cpnTucCsc3H21.getBango3C8() == null ? 0 : cpnTucCsc3H21.getBango3C8());
        // 認定項目3-9（外出すると戻れない（迷子））
        cpnTucGdl4Kan13H21Record
                .setBango39(cpnTucCsc3H21.getBango3C9() == null ? 0 : cpnTucCsc3H21.getBango3C9());
        // 認定項目4-1（被害妄想（物を盗られたなど））
        cpnTucGdl4Kan13H21Record
                .setBango41(cpnTucCsc3H21.getBango4C1() == null ? 0 : cpnTucCsc3H21.getBango4C1());
        // 認定項目4-2（作話をする）
        cpnTucGdl4Kan13H21Record
                .setBango42(cpnTucCsc3H21.getBango4C2() == null ? 0 : cpnTucCsc3H21.getBango4C2());
        // 認定項目4-3（感情が不安定になる）
        cpnTucGdl4Kan13H21Record
                .setBango43(cpnTucCsc3H21.getBango4C3() == null ? 0 : cpnTucCsc3H21.getBango4C3());
        // 認定項目4-4（昼夜の逆転）
        cpnTucGdl4Kan13H21Record
                .setBango44(cpnTucCsc3H21.getBango4C4() == null ? 0 : cpnTucCsc3H21.getBango4C4());
        // 認定項目4-5（しつこく同じ話をする）
        cpnTucGdl4Kan13H21Record
                .setBango45(cpnTucCsc3H21.getBango4C5() == null ? 0 : cpnTucCsc3H21.getBango4C5());
        // 認定項目4-6（大声を出す）
        cpnTucGdl4Kan13H21Record
                .setBango46(cpnTucCsc3H21.getBango4C6() == null ? 0 : cpnTucCsc3H21.getBango4C6());
        // 認定項目4-7（介護に抵抗する）
        cpnTucGdl4Kan13H21Record
                .setBango47(cpnTucCsc3H21.getBango4C7() == null ? 0 : cpnTucCsc3H21.getBango4C7());
        // 認定項目4-8（落ち着きがない（「家に帰る」等））
        cpnTucGdl4Kan13H21Record
                .setBango48(cpnTucCsc3H21.getBango4C8() == null ? 0 : cpnTucCsc3H21.getBango4C8());
        // 認定項目4-9（外に出たがり目が離せない）
        cpnTucGdl4Kan13H21Record
                .setBango49(cpnTucCsc3H21.getBango4C9() == null ? 0 : cpnTucCsc3H21.getBango4C9());
        // 認定項目4-10（ものを集める、無断でもってくる）
        cpnTucGdl4Kan13H21Record
                .setBango410(cpnTucCsc3H21.getBango4C10() == null ? 0 : cpnTucCsc3H21.getBango4C10());
        // 認定項目4-11（物を壊す、衣類を破く）
        cpnTucGdl4Kan13H21Record
                .setBango411(cpnTucCsc3H21.getBango4C11() == null ? 0 : cpnTucCsc3H21.getBango4C11());
        // 認定項目4-12（ひどい物忘れ）
        cpnTucGdl4Kan13H21Record
                .setBango412(cpnTucCsc3H21.getBango4C12() == null ? 0 : cpnTucCsc3H21.getBango4C12());
        // 認定項目4-13（独り言や独り笑い）
        cpnTucGdl4Kan13H21Record
                .setBango413(cpnTucCsc3H21.getBango4C13() == null ? 0 : cpnTucCsc3H21.getBango4C13());
        // 認定項目4-14（自分勝手な行動）
        cpnTucGdl4Kan13H21Record
                .setBango414(cpnTucCsc3H21.getBango4C14() == null ? 0 : cpnTucCsc3H21.getBango4C14());
        // 認定項目4-15（話がまとまらない、会話にならない）
        cpnTucGdl4Kan13H21Record
                .setBango415(cpnTucCsc3H21.getBango4C15() == null ? 0 : cpnTucCsc3H21.getBango4C15());

        return cpnTucGdl4Kan13H21Record;
    }

    /**
     * 【ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）】情報を更新/登録する
     * 
     * @param inDto
     * @param cpnTucGdl4Kan13H21
     * @param cpnTucGdl4Kan13H21Record
     */
    private String updateCpnTucGdl4Kan13H21(AssessmentHomeChosaInsertServiceInDto inDto,
            CpnTucGdl4Kan13H21ListOutEntity cpnTucGdl4Kan13H21, CpnTucGdl4Kan13H21 cpnTucGdl4Kan13H21Record) {
        // 2．検索件数>0の場合、【ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）】情報を更新する。
        if (Objects.nonNull(cpnTucGdl4Kan13H21)) {

            final CpnTucGdl4Kan13H21Criteria cpnTucGdl4Kan13H21Criteria = new CpnTucGdl4Kan13H21Criteria();
            // ■更新条件
            // 計画期間ID＝リクエストパラメータ詳細．計画期間ID
            // アセスメントID＝リクエストパラメータ詳細．アセスメントID
            // 更新回数＝上記処理1で取得したＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）情報.更新回数
            // 削除フラグ = 0（未削除データ）
            cpnTucGdl4Kan13H21Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            Integer cnt = this.cpnTucGdl4Kan13H21Mapper
                    .updateByCriteriaSelective(cpnTucGdl4Kan13H21Record,
                            cpnTucGdl4Kan13H21Criteria);
            if (cnt == 0) {
                return CommonConstants.FAILURE;
            }
        }
        // 3．以外の場合、【ＧＬ＿③認知機能・④精神・行動障害（Ｈ２１改訂）】情報を登録する。
        else {
            // アセスメントID
            cpnTucGdl4Kan13H21Record.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            cpnTucGdl4Kan13H21Record.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            this.cpnTucGdl4Kan13H21Mapper.insertSelective(cpnTucGdl4Kan13H21Record);
        }

        return CommonConstants.SUCCESS;
    }

    /**
     * ＧＬ＿③認知機能・④精神・行動障害（Ｒ３改訂）情報を取得する
     * 
     * @param inDto
     * @return
     */
    private Gdl5Ass8R3OutEntity findGdl5Ass8R3Func(AssessmentHomeChosaInsertServiceInDto inDto) {

        // DAOパラメータを作成
        Gdl5Ass8R3ByCriteriaInEntity gdl5Ass8R3ByCriteriaInEntity = new Gdl5Ass8R3ByCriteriaInEntity();
        // 計画期間ID
        gdl5Ass8R3ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // アセスメントID
        gdl5Ass8R3ByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // DAOを実行
        List<Gdl5Ass8R3OutEntity> gdl5Ass8R3List = this.cpnTucGdl5Kan13R3SelectMapper
                .findGdl5Ass8R3ByCriteria(gdl5Ass8R3ByCriteriaInEntity);

        Optional<Gdl5Ass8R3OutEntity> gdl5Ass8R3Optional = gdl5Ass8R3List
                .stream().findFirst();

        if (gdl5Ass8R3Optional.isPresent()) {
            return gdl5Ass8R3Optional.get();
        }
        return null;
    }

    /**
     * CpnTucGdl5Kan13R3 DAOパラメータを作成
     * 
     * @param cpnTucCsc3H21
     * @return
     */
    private CpnTucGdl5Kan13R3 getCpnTucGdl5Kan13R3RecordFunc(CpnTucCsc3H21OutEntity cpnTucCsc3H21) {

        // DAOパラメータを作成
        CpnTucGdl5Kan13R3 cpnTucGdl5Kan13R3Record = new CpnTucGdl5Kan13R3();
        // 認定項目3-1（意思の伝達）
        cpnTucGdl5Kan13R3Record.setBango31(cpnTucCsc3H21.getBango3C1() == null ? 0
                : cpnTucCsc3H21.getBango3C1());
        // 認定項目3-2（毎日の日課を理解する）
        cpnTucGdl5Kan13R3Record.setBango32(cpnTucCsc3H21.getBango3C2() == null ? 0
                : cpnTucCsc3H21.getBango3C2());
        // 認定項目3-3（生年月日や年齢を答える）
        cpnTucGdl5Kan13R3Record.setBango33(cpnTucCsc3H21.getBango3C3() == null ? 0
                : cpnTucCsc3H21.getBango3C3());
        // 認定項目3-4（面接調査の直前記憶）
        cpnTucGdl5Kan13R3Record.setBango34(cpnTucCsc3H21.getBango3C4() == null ? 0
                : cpnTucCsc3H21.getBango3C4());
        // 認定項目3-5（自分の名前を答える）
        cpnTucGdl5Kan13R3Record.setBango35(cpnTucCsc3H21.getBango3C5() == null ? 0
                : cpnTucCsc3H21.getBango3C5());
        // 認定項目3-6（今の季節を理解する）
        cpnTucGdl5Kan13R3Record.setBango36(cpnTucCsc3H21.getBango3C6() == null ? 0
                : cpnTucCsc3H21.getBango3C6());
        // 認定項目3-7（自分のいる場所を答える）
        cpnTucGdl5Kan13R3Record.setBango37(cpnTucCsc3H21.getBango3C7() == null ? 0
                : cpnTucCsc3H21.getBango3C7());
        // 認定項目3-8（徘徊）
        cpnTucGdl5Kan13R3Record.setBango38(cpnTucCsc3H21.getBango3C8() == null ? 0
                : cpnTucCsc3H21.getBango3C8());
        // 認定項目3-9（外出すると戻れない（迷子））
        cpnTucGdl5Kan13R3Record.setBango39(cpnTucCsc3H21.getBango3C9() == null ? 0
                : cpnTucCsc3H21.getBango3C9());
        // 認定項目4-1（被害妄想（物を盗られたなど））
        cpnTucGdl5Kan13R3Record.setBango41(cpnTucCsc3H21.getBango4C1() == null ? 0
                : cpnTucCsc3H21.getBango4C1());
        // 認定項目4-2（作話をする）
        cpnTucGdl5Kan13R3Record.setBango42(cpnTucCsc3H21.getBango4C2() == null ? 0
                : cpnTucCsc3H21.getBango4C2());
        // 認定項目4-3（感情が不安定になる）
        cpnTucGdl5Kan13R3Record.setBango43(cpnTucCsc3H21.getBango4C3() == null ? 0
                : cpnTucCsc3H21.getBango4C3());
        // 認定項目4-4（昼夜の逆転）
        cpnTucGdl5Kan13R3Record.setBango44(cpnTucCsc3H21.getBango4C4() == null ? 0
                : cpnTucCsc3H21.getBango4C4());
        // 認定項目4-5（しつこく同じ話をする）
        cpnTucGdl5Kan13R3Record.setBango45(cpnTucCsc3H21.getBango4C5() == null ? 0
                : cpnTucCsc3H21.getBango4C5());
        // 認定項目4-6（大声を出す）
        cpnTucGdl5Kan13R3Record.setBango46(cpnTucCsc3H21.getBango4C6() == null ? 0
                : cpnTucCsc3H21.getBango4C6());
        // 認定項目4-7（介護に抵抗する）
        cpnTucGdl5Kan13R3Record.setBango47(cpnTucCsc3H21.getBango4C7() == null ? 0
                : cpnTucCsc3H21.getBango4C7());
        // 認定項目4-8（落ち着きがない（「家に帰る」等））
        cpnTucGdl5Kan13R3Record.setBango48(cpnTucCsc3H21.getBango4C8() == null ? 0
                : cpnTucCsc3H21.getBango4C8());
        // 認定項目4-9（外に出たがり目が離せない）
        cpnTucGdl5Kan13R3Record.setBango49(cpnTucCsc3H21.getBango4C9() == null ? 0
                : cpnTucCsc3H21.getBango4C9());
        // 認定項目4-10（ものを集める、無断でもってくる）
        cpnTucGdl5Kan13R3Record.setBango410(cpnTucCsc3H21.getBango4C10() == null ? 0
                : cpnTucCsc3H21.getBango4C10());
        // 認定項目4-11（物を壊す、衣類を破く）
        cpnTucGdl5Kan13R3Record.setBango411(cpnTucCsc3H21.getBango4C11() == null ? 0
                : cpnTucCsc3H21.getBango4C11());
        // 認定項目4-12（ひどい物忘れ）
        cpnTucGdl5Kan13R3Record.setBango412(cpnTucCsc3H21.getBango4C12() == null ? 0
                : cpnTucCsc3H21.getBango4C12());
        // 認定項目4-13（独り言や独り笑い）
        cpnTucGdl5Kan13R3Record.setBango413(cpnTucCsc3H21.getBango4C13() == null ? 0
                : cpnTucCsc3H21.getBango4C13());
        // 認定項目4-14（自分勝手な行動）
        cpnTucGdl5Kan13R3Record.setBango414(cpnTucCsc3H21.getBango4C14() == null ? 0
                : cpnTucCsc3H21.getBango4C14());
        // 認定項目4-15（話がまとまらない、会話にならない）
        cpnTucGdl5Kan13R3Record.setBango415(cpnTucCsc3H21.getBango4C15() == null ? 0
                : cpnTucCsc3H21.getBango4C15());

        return cpnTucGdl5Kan13R3Record;
    }

    /**
     * 【ＧＬ＿③認知機能・④精神・行動障害（Ｒ３改訂）】を更新/登録する
     * 
     * @param inDto
     * @param gdl5Ass8R3
     * @param cpnTucGdl5Kan13R3Record
     */
    private String updateCpnTucGdl5Kan13R3(AssessmentHomeChosaInsertServiceInDto inDto, Gdl5Ass8R3OutEntity gdl5Ass8R3,
            CpnTucGdl5Kan13R3 cpnTucGdl5Kan13R3Record) {
        // 2．検索件数>0の場合、【ＧＬ＿③認知機能・④精神・行動障害（Ｒ３改訂）】を更新する。
        if (Objects.nonNull(gdl5Ass8R3)) {

            final CpnTucGdl5Kan13R3Criteria cpnTucGdl5Kan13R3Criteria = new CpnTucGdl5Kan13R3Criteria();
            // ■更新条件
            // 計画期間ID＝リクエストパラメータ詳細．計画期間ID
            // アセスメントID＝リクエストパラメータ詳細．アセスメントID
            // 更新回数＝上記処理1で取得したＧＬ＿③認知機能・④精神・行動障害（Ｒ３改訂）情報.更新回数
            // 削除フラグ = 0（未削除データ）
            cpnTucGdl5Kan13R3Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            Integer cnt = this.cpnTucGdl5Kan13R3Mapper
                    .updateByCriteriaSelective(cpnTucGdl5Kan13R3Record,
                            cpnTucGdl5Kan13R3Criteria);
            if (cnt == 0) {
                return CommonConstants.FAILURE;
            }
        }
        // 3．以外の場合、【ＧＬ＿③認知機能・④精神・行動障害（Ｒ３改訂）】を登録する。
        else {

            // アセスメントID
            cpnTucGdl5Kan13R3Record.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            cpnTucGdl5Kan13R3Record.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            this.cpnTucGdl5Kan13R3Mapper.insertSelective(cpnTucGdl5Kan13R3Record);
        }

        return CommonConstants.SUCCESS;
    }

    /**
     * ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）情報を取得する
     * 
     * @param inDto
     * @return
     */
    private CpnTucGdl4Kan15H21OutEntity findCpnTucGdl4Kan15H21Func(AssessmentHomeChosaInsertServiceInDto inDto) {

        // DAOパラメータを作成
        CpnTucGdl4Kan15H21ByCriteriaInEntity cpnTucGdl4Kan15H21ByCriteriaInEntity = new CpnTucGdl4Kan15H21ByCriteriaInEntity();
        // 計画期間ID
        cpnTucGdl4Kan15H21ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // アセスメントID
        cpnTucGdl4Kan15H21ByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // DAOを実行
        List<CpnTucGdl4Kan15H21OutEntity> cpnTucGdl4Kan15H21List = this.cpnTucGdl4Kan15H21SelectMapper
                .findCpnTucGdl4Kan15H21ByCriteria(cpnTucGdl4Kan15H21ByCriteriaInEntity);

        Optional<CpnTucGdl4Kan15H21OutEntity> cpnTucGdl4Kan15H21Optional = cpnTucGdl4Kan15H21List
                .stream().findFirst();

        if (cpnTucGdl4Kan15H21Optional.isPresent()) {
            return cpnTucGdl4Kan15H21Optional.get();
        }
        return null;
    }

    /**
     * CpnTucGdl4Kan15H21 DAOパラメータを作成
     * 
     * @param cpnTucCsc3H21
     * @return
     */
    private CpnTucGdl4Kan15H21 getCpnTucGdl4Kan15H21RecordFunc(CpnTucCsc3H21OutEntity cpnTucCsc3H21) {
        // DAOパラメータを作成
        CpnTucGdl4Kan15H21 cpnTucGdl4Kan15H21Record = new CpnTucGdl4Kan15H21();
        // 認定項目5-1（薬の内服）
        cpnTucGdl4Kan15H21Record.setBango51(cpnTucCsc3H21.getBango5C1() == null ? 0
                : cpnTucCsc3H21.getBango5C1());
        // 認定項目5-2（金銭の管理）
        cpnTucGdl4Kan15H21Record.setBango52(cpnTucCsc3H21.getBango5C2() == null ? 0
                : cpnTucCsc3H21.getBango5C2());
        // 認定項目5-3（日常の意思決定）
        cpnTucGdl4Kan15H21Record.setBango53(cpnTucCsc3H21.getBango5C3() == null ? 0
                : cpnTucCsc3H21.getBango5C3());
        // 認定項目5-4（集団への不適応）
        cpnTucGdl4Kan15H21Record.setBango54(cpnTucCsc3H21.getBango5C4() == null ? 0
                : cpnTucCsc3H21.getBango5C4());
        // 認定項目5-5（買い物）
        cpnTucGdl4Kan15H21Record.setBango55(cpnTucCsc3H21.getBango5C5() == null ? 0
                : cpnTucCsc3H21.getBango5C5());
        // 認定項目5-6（簡単な調理）
        cpnTucGdl4Kan15H21Record.setBango56(cpnTucCsc3H21.getBango5C6() == null ? 0
                : cpnTucCsc3H21.getBango5C6());

        return cpnTucGdl4Kan15H21Record;
    }

    /**
     * 【ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）】を更新/登録する
     * 
     * @param inDto
     * @param cpnTucGdl4Kan15H21
     * @param cpnTucGdl4Kan15H21Record
     */
    private String updateCpnTucGdl4Kan15H21(AssessmentHomeChosaInsertServiceInDto inDto,
            CpnTucGdl4Kan15H21OutEntity cpnTucGdl4Kan15H21, CpnTucGdl4Kan15H21 cpnTucGdl4Kan15H21Record) {
        // 2．検索件数>0の場合、【ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）】を更新する。
        if (Objects.nonNull(cpnTucGdl4Kan15H21)) {

            final CpnTucGdl4Kan15H21Criteria cpnTucGdl4Kan15H21Criteria = new CpnTucGdl4Kan15H21Criteria();
            // ■更新条件
            // 計画期間ID＝リクエストパラメータ詳細．計画期間ID
            // アセスメントID＝リクエストパラメータ詳細．アセスメントID
            // 更新回数＝上記処理1で取得したＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）情報.更新回数
            // 削除フラグ = 0（未削除データ）
            cpnTucGdl4Kan15H21Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            Integer cnt = this.cpnTucGdl4Kan15H21Mapper
                    .updateByCriteriaSelective(cpnTucGdl4Kan15H21Record,
                            cpnTucGdl4Kan15H21Criteria);
            if (cnt == 0) {
                return CommonConstants.FAILURE;
            }
        }
        // 3．以外の場合、【ＧＬ＿⑤社会生活（への適応）力（Ｈ２１改訂）】を登録する。
        else {

            // アセスメントID
            cpnTucGdl4Kan15H21Record.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            cpnTucGdl4Kan15H21Record.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            this.cpnTucGdl4Kan15H21Mapper.insertSelective(cpnTucGdl4Kan15H21Record);
        }

        return CommonConstants.SUCCESS;
    }

    /**
     * ＧＬ＿⑤社会生活（への適応）力（Ｒ３改訂）情報を取得する
     * 
     * @param inDto
     * @return
     */
    private Gdl5Ass9R3OutEntity findGdl5Ass9R3Func(AssessmentHomeChosaInsertServiceInDto inDto) {

        // DAOパラメータを作成
        Gdl5Ass9R3ByCriteriaInEntity gdl5Ass9R3ByCriteriaInEntity = new Gdl5Ass9R3ByCriteriaInEntity();
        // 計画期間ID
        gdl5Ass9R3ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // アセスメントID
        gdl5Ass9R3ByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // DAOを実行
        List<Gdl5Ass9R3OutEntity> gdl5Ass9R3List = this.cpnTucGdl5Kan15R3SelectMapper
                .findGdl5Ass9R3ByCriteria(gdl5Ass9R3ByCriteriaInEntity);

        Optional<Gdl5Ass9R3OutEntity> gdl5Ass9R3Optional = gdl5Ass9R3List
                .stream().findFirst();

        if (gdl5Ass9R3Optional.isPresent()) {
            return gdl5Ass9R3Optional.get();
        }
        return null;
    }

    /**
     * CpnTucGdl5Kan15R3 DAOパラメータを作成
     * 
     * @param cpnTucCsc3H21
     * @return
     */
    private CpnTucGdl5Kan15R3 getCpnTucGdl5Kan15R3RecordFunc(CpnTucCsc3H21OutEntity cpnTucCsc3H21) {

        // DAOパラメータを作成
        CpnTucGdl5Kan15R3 cpnTucGdl5Kan15R3Record = new CpnTucGdl5Kan15R3();
        // 認定項目5-1（薬の内服）
        cpnTucGdl5Kan15R3Record.setBango51(cpnTucCsc3H21.getBango5C1() == null ? 0
                : cpnTucCsc3H21.getBango5C1());
        // 認定項目5-2（金銭の管理）
        cpnTucGdl5Kan15R3Record.setBango52(cpnTucCsc3H21.getBango5C2() == null ? 0
                : cpnTucCsc3H21.getBango5C2());
        // 認定項目5-3（日常の意思決定）
        cpnTucGdl5Kan15R3Record.setBango53(cpnTucCsc3H21.getBango5C3() == null ? 0
                : cpnTucCsc3H21.getBango5C3());
        // 認定項目5-4（集団への不適応）
        cpnTucGdl5Kan15R3Record.setBango54(cpnTucCsc3H21.getBango5C4() == null ? 0
                : cpnTucCsc3H21.getBango5C4());
        // 認定項目5-5（買い物）
        cpnTucGdl5Kan15R3Record.setBango55(cpnTucCsc3H21.getBango5C5() == null ? 0
                : cpnTucCsc3H21.getBango5C5());
        // 認定項目5-6（簡単な調理）
        cpnTucGdl5Kan15R3Record.setBango56(cpnTucCsc3H21.getBango5C6() == null ? 0
                : cpnTucCsc3H21.getBango5C6());

        return cpnTucGdl5Kan15R3Record;
    }

    /**
     * 【ＧＬ＿⑤社会生活（への適応）力（Ｒ３改訂）】を更新/登録する
     * 
     * @param inDto
     * @param gdl5Ass9R3
     * @param cpnTucGdl5Kan15R3Record
     */
    private String updateCpnTucGdl5Kan15R3(AssessmentHomeChosaInsertServiceInDto inDto, Gdl5Ass9R3OutEntity gdl5Ass9R3,
            CpnTucGdl5Kan15R3 cpnTucGdl5Kan15R3Record) {
        // 2．検索件数>0の場合、【ＧＬ＿⑤社会生活（への適応）力（Ｒ３改訂）】を更新する。
        if (Objects.nonNull(gdl5Ass9R3)) {
            final CpnTucGdl5Kan15R3Criteria cpnTucGdl5Kan15R3Criteria = new CpnTucGdl5Kan15R3Criteria();
            // ■更新条件
            // 計画期間ID＝リクエストパラメータ詳細．計画期間ID
            // アセスメントID＝リクエストパラメータ詳細．アセスメントID
            // 更新回数＝上記処理1で取得したＧＬ＿⑤社会生活（への適応）力（Ｒ３改訂）情報.更新回数
            // 削除フラグ = 0（未削除データ）
            cpnTucGdl5Kan15R3Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            Integer cnt = this.cpnTucGdl5Kan15R3Mapper
                    .updateByCriteriaSelective(cpnTucGdl5Kan15R3Record,
                            cpnTucGdl5Kan15R3Criteria);
            if (cnt == 0) {
                return CommonConstants.FAILURE;
            }
        }
        // 3．以外の場合、【ＧＬ＿⑤社会生活（への適応）力（Ｒ３改訂）】を登録する。
        else {
            // アセスメントID
            cpnTucGdl5Kan15R3Record.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            cpnTucGdl5Kan15R3Record.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            this.cpnTucGdl5Kan15R3Mapper.insertSelective(cpnTucGdl5Kan15R3Record);
        }

        return CommonConstants.SUCCESS;
    }

    /**
     * ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）情報を取得する
     * 
     * @param inDto
     * @return
     */
    private CpnTucGdl4Kan16H21OutEntity findCpnTucGdl4Kan16H21Func(AssessmentHomeChosaInsertServiceInDto inDto) {

        // DAOパラメータを作成
        CpnTucGdl4Kan16H21ByCriteriaInEntity cpnTucGdl4Kan16H21ByCriteriaInEntity = new CpnTucGdl4Kan16H21ByCriteriaInEntity();
        // 計画期間ID
        cpnTucGdl4Kan16H21ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // アセスメントID
        cpnTucGdl4Kan16H21ByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // DAOを実行
        List<CpnTucGdl4Kan16H21OutEntity> cpnTucGdl4Kan16H21List = this.cpnTucGdl4Kan16H21SelectMapper
                .findCpnTucGdl4Kan16H21ByCriteria(cpnTucGdl4Kan16H21ByCriteriaInEntity);

        Optional<CpnTucGdl4Kan16H21OutEntity> cpnTucGdl4Kan16H21Optional = cpnTucGdl4Kan16H21List
                .stream().findFirst();

        if (cpnTucGdl4Kan16H21Optional.isPresent()) {
            return cpnTucGdl4Kan16H21Optional.get();
        }
        return null;
    }

    /**
     * CpnTucGdl4Kan16H21 DAOパラメータを作成
     * 
     * @param cpnTucCsc3H21
     * @return
     */
    private CpnTucGdl4Kan16H21 getCpnTucGdl4Kan16H21RecordFunc(CpnTucCsc3H21OutEntity cpnTucCsc3H21) {
        // DAOパラメータを作成
        CpnTucGdl4Kan16H21 cpnTucGdl4Kan16H21Record = new CpnTucGdl4Kan16H21();

        // 認定項目6-1
        cpnTucGdl4Kan16H21Record.setBango61(cpnTucCsc3H21.getBango6C1() == null ? 0
                : cpnTucCsc3H21.getBango6C1());
        // 認定項目6-2
        cpnTucGdl4Kan16H21Record.setBango62(cpnTucCsc3H21.getBango6C2() == null ? 0
                : cpnTucCsc3H21.getBango6C2());
        // 認定項目6-3
        cpnTucGdl4Kan16H21Record.setBango63(cpnTucCsc3H21.getBango6C3() == null ? 0
                : cpnTucCsc3H21.getBango6C3());
        // 認定項目6-4
        cpnTucGdl4Kan16H21Record.setBango64(cpnTucCsc3H21.getBango6C4() == null ? 0
                : cpnTucCsc3H21.getBango6C4());
        // 認定項目6-5
        cpnTucGdl4Kan16H21Record.setBango65(cpnTucCsc3H21.getBango6C5() == null ? 0
                : cpnTucCsc3H21.getBango6C5());
        // 認定項目6-6
        cpnTucGdl4Kan16H21Record.setBango66(cpnTucCsc3H21.getBango6C6() == null ? 0
                : cpnTucCsc3H21.getBango6C6());
        // 認定項目6-7
        cpnTucGdl4Kan16H21Record.setBango67(cpnTucCsc3H21.getBango6C7() == null ? 0
                : cpnTucCsc3H21.getBango6C7());
        // 認定項目6-8
        cpnTucGdl4Kan16H21Record.setBango68(cpnTucCsc3H21.getBango6C8() == null ? 0
                : cpnTucCsc3H21.getBango6C8());
        // 認定項目6-9
        cpnTucGdl4Kan16H21Record.setBango69(cpnTucCsc3H21.getBango6C9() == null ? 0
                : cpnTucCsc3H21.getBango6C9());
        // 認定項目6-10
        cpnTucGdl4Kan16H21Record.setBango610(cpnTucCsc3H21.getBango6C10() == null ? 0
                : cpnTucCsc3H21.getBango6C10());
        // 認定項目6-11
        cpnTucGdl4Kan16H21Record.setBango611(cpnTucCsc3H21.getBango6C11() == null ? 0
                : cpnTucCsc3H21.getBango6C11());
        // 認定項目6-12
        cpnTucGdl4Kan16H21Record.setBango612(cpnTucCsc3H21.getBango6C12() == null ? 0
                : cpnTucCsc3H21.getBango6C12());

        return cpnTucGdl4Kan16H21Record;
    }

    /**
     * 【ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）】を更新/登録する
     * 
     * @param inDto
     * @param cpnTucGdl4Kan16H21
     * @param cpnTucGdl4Kan16H21Record
     */
    private String updateCpnTucGdl4Kan16H21(AssessmentHomeChosaInsertServiceInDto inDto,
            CpnTucGdl4Kan16H21OutEntity cpnTucGdl4Kan16H21, CpnTucGdl4Kan16H21 cpnTucGdl4Kan16H21Record) {
        // 2．検索件数>0の場合、【ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）】を更新する。
        if (Objects.nonNull(cpnTucGdl4Kan16H21)) {

            final CpnTucGdl4Kan16H21Criteria cpnTucGdl4Kan16H21Criteria = new CpnTucGdl4Kan16H21Criteria();
            // ■更新条件
            // 計画期間ID＝リクエストパラメータ詳細．計画期間ID
            // アセスメントID＝リクエストパラメータ詳細．アセスメントID
            // 更新回数＝上記処理1で取得したＧＬ＿⑥医療・健康関係（Ｈ２１改訂）情報.更新回数
            // 削除フラグ = 0（未削除データ）
            cpnTucGdl4Kan16H21Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            Integer cnt = this.cpnTucGdl4Kan16H21Mapper
                    .updateByCriteriaSelective(cpnTucGdl4Kan16H21Record,
                            cpnTucGdl4Kan16H21Criteria);
            if (cnt == 0) {
                return CommonConstants.FAILURE;
            }
        } else {
            // 3．以外の場合、【ＧＬ＿⑥医療・健康関係（Ｈ２１改訂）】を登録する。
            // アセスメントID
            cpnTucGdl4Kan16H21Record.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            cpnTucGdl4Kan16H21Record.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            this.cpnTucGdl4Kan16H21Mapper.insertSelective(cpnTucGdl4Kan16H21Record);
        }
        return CommonConstants.SUCCESS;
    }

    /**
     * ＧＬ＿⑥医療・健康関係（Ｒ３改訂）情報を取得する
     * 
     * @param inDto
     * @return
     */
    private Gdl5Ass10R3OutEntity findGdl5Ass10R3Func(AssessmentHomeChosaInsertServiceInDto inDto) {

        // DAOパラメータを作成
        Gdl5Ass10R3ByCriteriaInEntity gdl5Ass10R3ByCriteriaInEntity = new Gdl5Ass10R3ByCriteriaInEntity();
        // 計画期間ID
        gdl5Ass10R3ByCriteriaInEntity.setAlSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // アセスメントID
        gdl5Ass10R3ByCriteriaInEntity.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // DAOを実行
        List<Gdl5Ass10R3OutEntity> gdl5Ass10R3List = this.cpnTucGdl5Kan16R3SelectMapper
                .findGdl5Ass10R3ByCriteria(gdl5Ass10R3ByCriteriaInEntity);

        Optional<Gdl5Ass10R3OutEntity> gdl5Ass10R3Optional = gdl5Ass10R3List
                .stream().findFirst();

        if (gdl5Ass10R3Optional.isPresent()) {
            return gdl5Ass10R3Optional.get();
        }
        return null;
    }

    /**
     * CpnTucGdl5Kan16R3 DAOパラメータを作成
     * 
     * @param cpnTucCsc3H21
     * @return
     */
    private CpnTucGdl5Kan16R3 getCpnTucGdl5Kan16R3RecordFunc(CpnTucCsc3H21OutEntity cpnTucCsc3H21) {

        // DAOパラメータを作成
        CpnTucGdl5Kan16R3 cpnTucGdl5Kan16R3Record = new CpnTucGdl5Kan16R3();
        // 認定項目6-1
        cpnTucGdl5Kan16R3Record.setBango61(cpnTucCsc3H21.getBango6C1() == null ? 0
                : cpnTucCsc3H21.getBango6C1());
        // 認定項目6-2
        cpnTucGdl5Kan16R3Record.setBango62(cpnTucCsc3H21.getBango6C2() == null ? 0
                : cpnTucCsc3H21.getBango6C2());
        // 認定項目6-3
        cpnTucGdl5Kan16R3Record.setBango63(cpnTucCsc3H21.getBango6C3() == null ? 0
                : cpnTucCsc3H21.getBango6C3());
        // 認定項目6-4
        cpnTucGdl5Kan16R3Record.setBango64(cpnTucCsc3H21.getBango6C4() == null ? 0
                : cpnTucCsc3H21.getBango6C4());
        // 認定項目6-5
        cpnTucGdl5Kan16R3Record.setBango65(cpnTucCsc3H21.getBango6C5() == null ? 0
                : cpnTucCsc3H21.getBango6C5());
        // 認定項目6-6
        cpnTucGdl5Kan16R3Record.setBango66(cpnTucCsc3H21.getBango6C6() == null ? 0
                : cpnTucCsc3H21.getBango6C6());
        // 認定項目6-7
        cpnTucGdl5Kan16R3Record.setBango67(cpnTucCsc3H21.getBango6C7() == null ? 0
                : cpnTucCsc3H21.getBango6C7());
        // 認定項目6-8
        cpnTucGdl5Kan16R3Record.setBango68(cpnTucCsc3H21.getBango6C8() == null ? 0
                : cpnTucCsc3H21.getBango6C8());
        // 認定項目6-9
        cpnTucGdl5Kan16R3Record.setBango69(cpnTucCsc3H21.getBango6C9() == null ? 0
                : cpnTucCsc3H21.getBango6C9());
        // 認定項目6-10
        cpnTucGdl5Kan16R3Record.setBango610(cpnTucCsc3H21.getBango6C10() == null ? 0
                : cpnTucCsc3H21.getBango6C10());
        // 認定項目6-11
        cpnTucGdl5Kan16R3Record.setBango611(cpnTucCsc3H21.getBango6C11() == null ? 0
                : cpnTucCsc3H21.getBango6C11());
        // 認定項目6-12
        cpnTucGdl5Kan16R3Record.setBango612(cpnTucCsc3H21.getBango6C12() == null ? 0
                : cpnTucCsc3H21.getBango6C12());

        return cpnTucGdl5Kan16R3Record;
    }

    /**
     * 【ＧＬ＿⑥医療・健康関係（Ｒ３改訂）】を更新/登録する
     * 
     * @param inDto
     * @param gdl5Ass10R3
     * @param cpnTucGdl5Kan16R3Record
     */
    private String updateCpnTucGdl5Kan16R3(AssessmentHomeChosaInsertServiceInDto inDto,
            Gdl5Ass10R3OutEntity gdl5Ass10R3,
            CpnTucGdl5Kan16R3 cpnTucGdl5Kan16R3Record) {
        // 2．検索件数>0の場合、【ＧＬ＿⑥医療・健康関係（Ｒ３改訂）】を更新する。
        if (Objects.nonNull(gdl5Ass10R3)) {
            final CpnTucGdl5Kan16R3Criteria cpnTucGdl5Kan16R3Criteria = new CpnTucGdl5Kan16R3Criteria();
            // ■更新条件
            // 計画期間ID＝リクエストパラメータ詳細．計画期間ID
            // アセスメントID＝リクエストパラメータ詳細．アセスメントID
            // 更新回数＝上記処理1で取得したＧＬ＿⑥医療・健康関係（Ｒ３改訂）情報.更新回数
            // 削除フラグ = 0（未削除データ）
            cpnTucGdl5Kan16R3Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                    .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            Integer cnt = this.cpnTucGdl5Kan16R3Mapper
                    .updateByCriteriaSelective(cpnTucGdl5Kan16R3Record,
                            cpnTucGdl5Kan16R3Criteria);
            if (cnt == 0) {
                return CommonConstants.FAILURE;
            }
        }
        // 3．以外の場合、【ＧＬ＿⑥医療・健康関係（Ｒ３改訂）】を登録する。
        else {
            // アセスメントID
            cpnTucGdl5Kan16R3Record.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
            // 計画期間ID
            cpnTucGdl5Kan16R3Record.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

            this.cpnTucGdl5Kan16R3Mapper.insertSelective(cpnTucGdl5Kan16R3Record);
        }
        return CommonConstants.SUCCESS;
    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する
     * 
     * @param inDto
     * @param kyotakuRireki
     * @param cpnTucGdlRirekiRecord
     */
    private String updateCpnTucGdlRireki(AssessmentHomeChosaInsertServiceInDto inDto,
            KyotakuRirekiOutEntity kyotakuRireki, CpnTucGdlRireki cpnTucGdlRirekiRecord) {

        // 4. 上記処理2で取得した履歴情報が変更の場合、【ＧＬ＿居宅アセスメント履歴】情報を更新する。
        final CpnTucGdlRirekiCriteria cpnTucGdlRirekiCriteria = new CpnTucGdlRirekiCriteria();
        // ■更新条件
        // アセスメントID＝リクエストパラメータ詳細．アセスメントID
        // 計画期間ID＝リクエストパラメータ詳細.計画期間ID
        // 法人ID＝リクエストパラメータ詳細.法人ID
        // 施設ID＝リクエストパラメータ詳細.施設ID
        // 事業所ID＝リクエストパラメータ詳細.事業所ID
        // 利用者ID＝リクエストパラメータ詳細.利用者ID
        // 更新回数＝上記処理2で取得した履歴情報.更新回数
        // 削除フラグ = 0（未削除データ）
        cpnTucGdlRirekiCriteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()))
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()))
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()));

        Integer cnt = this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRirekiRecord,
                cpnTucGdlRirekiCriteria);

        if (cnt == 0) {
            return CommonConstants.FAILURE;
        }

        return CommonConstants.SUCCESS;
    }
}