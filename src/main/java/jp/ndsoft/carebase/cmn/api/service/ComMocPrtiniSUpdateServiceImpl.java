package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jp.ndsoft.carebase.cmn.api.service.dto.ComMocPrtiniSUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.ComMocPrtiniSUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.service.dto.CarePlanPrintDataDto;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocPrtiniInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocPrtiniSInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocPrtiniInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMocPrtiniSInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocPrtiniSSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocPrtiniSelectMapper;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.smh.framework.util.StringUtil;
import jp.ndsoft.carebase.common.dao.mybatis.ComMocPrtiniSMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocPrtiniS;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMocPrtiniSCriteria;

/**
 * 「GUI00936_計画書一括印刷」画面の設定情報を保存する
 *
 * <AUTHOR>
 */
@Service
public class ComMocPrtiniSUpdateServiceImpl
        extends UpdateServiceImpl<ComMocPrtiniSUpdateServiceInDto, ComMocPrtiniSUpdateServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    /** 機能名の計画書（１） */
    private static final String KINOU_NAME_CARE_PLAN_1 = "計画書（１）";
    /** 機能名の計画書（２） */
    private static final String KINOU_NAME_CARE_PLAN_2 = "計画書（２）";
    /** 機能名の週間計画 */
    private static final String KINOU_NAME_WEEK_PLAN = "週間計画";
    /** 機能名の日課計画 */
    private static final String KINOU_NAME_DAILY_PLAN = "日課計画";
    /** 機能名の利用票 */
    private static final String KINOU_NAME_USE_SLIP = "利用票";
    /** 機能名の利用票別表 */
    private static final String KINOU_NAME_USE_SLIP_OTHER = "利用票別表";
    /** セクションNOのU00891 */
    private static final String SECTION_NO_U00891 = "U00891";
    /** システム略名の3gk */
    private static final String SYS_RYAKU_3GK = "3gk";
    /** 帳票番号１ */
    private static final int PRT_NO_1 = 1;
    /** 帳票番号３ */
    private static final int PRT_NO_3 = 3;
    /** 帳票番号４ */
    private static final int PRT_NO_4 = 4;
    /** 帳票番号の変数定義 */
    private int liPrtno = 0;
    /** Param14のデフォルト値 */
    private static final String PARAM14_DEFAULT_VALUE = "111100";
    /** Param15のデフォルト値 */
    private static final String PARAM15_DEFAULT_VALUE = "111100";
    /** Param016のデフォルト値 */
    private static final String PARAM16_DEFAULT_VALUE = "111100";
    /** Param17のデフォルト値 */
    private static final String PARAM17_DEFAULT_VALUE = "111100";
    /** Param18のデフォルト値 */
    private static final String PARAM18_DEFAULT_VALUE = "111100";
    /** Param24のデフォルト値 */
    private static final String PARAM24_DEFAULT_VALUE = "125600";
    /** Param25のデフォルト値 */
    private static final String PARAM25_DEFAULT_VALUE = "125600";
    /** Param26のデフォルト値 */
    private static final String PARAM26_DEFAULT_VALUE = "125600";
    /** Param27のデフォルト値 */
    private static final String PARAM27_DEFAULT_VALUE = "125600";
    /** Param28のデフォルト値 */
    private static final String PARAM28_DEFAULT_VALUE = "125600";
    /** Param11のデフォルト値 */
    private static final String PARAM11_DEFAULT_VALUE = "111100";
    /** Param12のデフォルト値 */
    private static final String PARAM12_DEFAULT_VALUE = "111100";
    /** Param21のデフォルト値 */
    private static final String PARAM21_DEFAULT_VALUE = "125600";
    /** Param22のデフォルト値 */
    private static final String PARAM22_DEFAULT_VALUE = "125600";
    /** Param23のデフォルト値 */
    private static final String PARAM23_DEFAULT_VALUE = "125678";
     /** 規定値："00" */
    private static final String DEFAULT_VALUE_STR_00 = "00";
    /** 印刷パラメータ用定数"11" */
    private static final String PARAMP_STR_11 = "11";
    /** 印刷パラメータ用定数"12" */
    private static final String PARAMP_STR_12 = "12";
    /** 印刷パラメータ用定数"13" */
    private static final String PARAMP_STR_13 = "13";

    /** 印刷iniファイルデータ保存テーブル情報取得 */
    @Autowired
    private ComMocPrtiniSSelectMapper comMocPrtiniSSelectMapper;
    /** 印刷iniファイルデータ初期値テーブル情報取得 */
    @Autowired
    private ComMocPrtiniSelectMapper comMocPrtiniSelectMapper;
    /** 印刷iniファイルデータ保存テーブル情報保存マップクラス */
    @Autowired
    private ComMocPrtiniSMapper comMocPrtiniSMapper;
        
    /**
     * 計画書一括印刷の初期情報取得
     * 
     * @param inDto 計画書一括印刷の入力DTO.
     * @return 計画書一括印刷の出力DTO
     * @throws Exception Exception
     */
    @Override
    protected ComMocPrtiniSUpdateServiceOutDto mainProcess(ComMocPrtiniSUpdateServiceInDto inDto)
            throws Exception {       
        LOG.info(Constants.START);
        // DTOOUT情報
        ComMocPrtiniSUpdateServiceOutDto outDto = new ComMocPrtiniSUpdateServiceOutDto();
        
        // 単項目チェック以外の入力チェック 特になし
        // 計画書（１）欄、計画書（２）欄、週間計画欄及び日課計画欄情報を保存する
        outDto = wfSaveOptionPrt(inDto,outDto);

        // 両面、印刷選択、印刷順の設定情報を　保存する。
        outDto = wfSaveOption(inDto,outDto);

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 計画書（１）欄、計画書（２）欄、週間計画欄及び日課計画欄情報を保存する
     * 
     * @param inDto       inデータ
     */
    private ComMocPrtiniSUpdateServiceOutDto wfSaveOptionPrt(ComMocPrtiniSUpdateServiceInDto inDto,ComMocPrtiniSUpdateServiceOutDto outDto) 
    throws Exception {
        // 印刷帳票選択情報リストを作成する
        List<CarePlanPrintDataDto> carePlanPrintDataList = inDto.getCarePlanPrintDataList();

        // リクエストパラメータ.計画書印刷リストの件数分により、下記処理をLoopする
        for (int i = 0;i < carePlanPrintDataList.size();i++) {
            CarePlanPrintDataDto itemData = carePlanPrintDataList.get(i);
            
            if (KINOU_NAME_USE_SLIP.equals(itemData.getCarePlanDisplayName())) {
                liPrtno = PRT_NO_3;
            } else if (KINOU_NAME_USE_SLIP_OTHER.equals(itemData.getCarePlanDisplayName())) {
                liPrtno = PRT_NO_4;
            } else {
                liPrtno = PRT_NO_1;
            }

            // 印刷iniファイルデータ保存テーブル情報取得を行う。
            // 検索条件を設定
            ComMocPrtiniSInfoByCriteriaInEntity inEntity = new ComMocPrtiniSInfoByCriteriaInEntity();
            inEntity.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
            inEntity.setSectionNo(itemData.getCarePlanSectionNo());
            inEntity.setHid(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
            inEntity.setSid(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
            inEntity.setJid(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
            inEntity.setPrtno(CommonConstants.INT_1);
            
            // 検索処理を実施する
            List<ComMocPrtiniSInfoOutEntity> outEntityList = 
            comMocPrtiniSSelectMapper.findComMocPrtiniSInfoByCriteria(inEntity);

            ComMocPrtiniS comMocPrtiniSInEntity = new ComMocPrtiniS();
            if (outEntityList.size() == 0) {
                // 印刷iniファイルデータテーブル情報を取得する。
                // 検索条件を設定
                ComMocPrtiniInfoByCriteriaInEntity inEntity2 = new ComMocPrtiniInfoByCriteriaInEntity();
                
                inEntity2.setSectionNo(itemData.getCarePlanSectionNo());
                inEntity2.setSysRyaku(SYS_RYAKU_3GK);
                inEntity2.setPrtNo(liPrtno);
                List<ComMocPrtiniInfoOutEntity> outEntity2List = comMocPrtiniSelectMapper.findComMocPrtiniInfoByCriteria(inEntity2);
                ComMocPrtiniInfoOutEntity outEntity2 = new ComMocPrtiniInfoOutEntity();
                if (outEntity2List.size() > 0) {
                    outEntity2 = outEntity2List.get(0);
                    // 取得した印刷iniファイルデータテーブル情報を 印刷iniファイルデータ保存テーブルに格納する
                    comMocPrtiniSInEntity = setComMocPrtiniSDefault(comMocPrtiniSInEntity,outEntity2,inDto);
                }
            } else {
                if (KINOU_NAME_USE_SLIP.equals(itemData.getCarePlanKinouName()) || KINOU_NAME_USE_SLIP_OTHER.equals(itemData.getCarePlanKinouName())) {
                    continue;
                }

                copyEntityData(outEntityList.get(0),comMocPrtiniSInEntity);
            }

            // 保存Entityに　Param01～Param50値は　画面上の設定値を入替える。
            comMocPrtiniSInEntity = setComMocPrtiniS(itemData.getCarePlanKinouName(),inDto,comMocPrtiniSInEntity);
            
            // 新規登録
            if (outEntityList.size() == 0) {
                comMocPrtiniSMapper.insertSelective(comMocPrtiniSInEntity);
            } else {
                // 既存データを更新する
                // 更新条件
                ComMocPrtiniSCriteria criteria = new ComMocPrtiniSCriteria();
                criteria.createCriteria()
                .andShokuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShokuId()))
                .andSectionNoEqualTo(comMocPrtiniSInEntity.getSectionNo())
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()))
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                .andPrtNoEqualTo(CommonConstants.INT_1);

                int cnt = comMocPrtiniSMapper.updateByCriteriaSelective(comMocPrtiniSInEntity, criteria);
                // 更新失敗の場合
                if (cnt <= 0) {
                    throw new ExclusiveException();
                }
            }
        }
        return outDto;
    }

    /**
     * 両面、印刷選択、印刷順の設定情報を　保存する。
     * 
     * @param inDto       inデータ
     */
    private ComMocPrtiniSUpdateServiceOutDto wfSaveOption(ComMocPrtiniSUpdateServiceInDto inDto,ComMocPrtiniSUpdateServiceOutDto outDto) 
    throws Exception {
        //印刷iniファイルデータ保存テーブル情報取得を行う。
        ComMocPrtiniSInfoByCriteriaInEntity sInEntity = new ComMocPrtiniSInfoByCriteriaInEntity();
        sInEntity.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
        sInEntity.setSectionNo(SECTION_NO_U00891);
        sInEntity.setHid(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        sInEntity.setSid(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        sInEntity.setJid(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        sInEntity.setPrtno(CommonConstants.INT_1);
        
        // 検索処理を実施する
        List<ComMocPrtiniSInfoOutEntity> sOutEntityList = 
        comMocPrtiniSSelectMapper.findComMocPrtiniSInfoByCriteria(sInEntity);

        ComMocPrtiniS comMocPrtiniSInEntity2 = new ComMocPrtiniS();
        if (sOutEntityList.size() == 0) {
            // 印刷iniファイルデータテーブル情報を取得する。
            // 検索条件を設定
            ComMocPrtiniInfoByCriteriaInEntity sInEntity2 = new ComMocPrtiniInfoByCriteriaInEntity();
            
            sInEntity2.setSectionNo(SECTION_NO_U00891);
            sInEntity2.setSysRyaku(SYS_RYAKU_3GK);
            sInEntity2.setPrtNo(1);
            List<ComMocPrtiniInfoOutEntity> soutEntity2List = comMocPrtiniSelectMapper.findComMocPrtiniInfoByCriteria(sInEntity2);
            ComMocPrtiniInfoOutEntity sOutEntity2 = new ComMocPrtiniInfoOutEntity();
            if (soutEntity2List.size() > 0) {
                sOutEntity2 = soutEntity2List.get(0);
                comMocPrtiniSInEntity2 = setComMocPrtiniSDefault2(comMocPrtiniSInEntity2,sOutEntity2,inDto); 
            }
        } else {
            copyEntityData(sOutEntityList.get(0),comMocPrtiniSInEntity2);
        }

        // 保存Entityに　Param01～Param50値は　画面上の設定値を入替える。
        comMocPrtiniSInEntity2 = setComMocPrtiniS2(inDto,comMocPrtiniSInEntity2);
       
        // 新規登録
        if (sOutEntityList.size() == 0) {
            comMocPrtiniSMapper.insertSelective(comMocPrtiniSInEntity2);
        } else {
            // 既存データを更新する
            // 更新条件
            ComMocPrtiniSCriteria criteria = new ComMocPrtiniSCriteria();
            criteria.createCriteria()
            .andShokuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShokuId()))
            .andSectionNoEqualTo(SECTION_NO_U00891)
            .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
            .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()))
            .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
            .andPrtNoEqualTo(CommonConstants.INT_1);

            int cnt = comMocPrtiniSMapper.updateByCriteriaSelective(comMocPrtiniSInEntity2, criteria);
            
            // 更新失敗の場合
            if (cnt <= 0) {
                throw new ExclusiveException();
            }
        }
        return outDto;
    }

    /**
     * 取得した印刷iniファイルデータテーブル情報を 印刷iniファイルデータ保存テーブルに格納する
     *
     * @param inEntity    inエンティティ 
     * @param outEntity   outエンティティ
     * @param inDto       inデータ
     */
    private ComMocPrtiniS setComMocPrtiniSDefault(ComMocPrtiniS inEntity,ComMocPrtiniInfoOutEntity outEntity,ComMocPrtiniSUpdateServiceInDto inDto) {
        // 職員ID
        inEntity.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
        // 法人ID
        inEntity.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        inEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業所ID
        inEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // セクション名
        inEntity.setSectionName(outEntity.getSectionName());
        // システム略称
        inEntity.setSysRyaku(SYS_RYAKU_3GK);
        // セクションNo
        inEntity.setSectionNo(outEntity.getSectionNo());
        // 帳票番号
        inEntity.setPrtNo(outEntity.getPrtNo());
        // dwobject
        inEntity.setDwobject(CommonConstants.SPACE_STRING);
        // prt_size
        inEntity.setPrtSize(CommonConstants.INT_0);
        // list_title
        inEntity.setListTitle(CommonConstants.SPACE_STRING);
        // prt_title
        inEntity.setPrtTitle(CommonConstants.SPACE_STRING);
        // prndate
        inEntity.setPrndate(CommonConstants.INT_0);
        // prnshoku
        inEntity.setPrnshoku(CommonConstants.INT_0);
        // serial_flg
        inEntity.setSerialFlg(CommonConstants.INT_0);
        // mod_flg
        inEntity.setModFlg(CommonConstants.INT_0);
        // sec_flg
        inEntity.setSecFlg(CommonConstants.INT_0);
        // serial_height
        inEntity.setSerialHeight(CommonConstants.INT_0);
        // serial_pagelen
        inEntity.setSerialPagelen(CommonConstants.INT_0);
        // hsj_id
        inEntity.setHsjId(CommonConstants.INT_0);
        // m_top
        inEntity.setMTop(CommonConstants.INT_MINUS_1);
        // m_bottom
        inEntity.setMBottom(CommonConstants.INT_MINUS_1);
        // m_left
        inEntity.setMLeft(CommonConstants.INT_MINUS_1);
        // m_right
        inEntity.setMRight(CommonConstants.INT_MINUS_1);
        // ruler
        inEntity.setRuler(CommonConstants.INT_MINUS_1);
        // prt_title
        inEntity.setPrtTitle(outEntity.getDefPrtTitle());
        // prndate
        inEntity.setPrndate(Integer.valueOf(outEntity.getDefPrndate()));
        // prnshoku
        inEntity.setPrnshoku(Integer.valueOf(outEntity.getDefPrnshoku()));
        
        // 上記で取得したParam01～Param50の値は　出力Entityに格納する。
        inEntity.setParam01(outEntity.getParam01());
        inEntity.setParam02(outEntity.getParam02());
        inEntity.setParam03(outEntity.getParam03());
        inEntity.setParam04(outEntity.getParam04());
        inEntity.setParam05(outEntity.getParam05());
        inEntity.setParam06(outEntity.getParam06());
        inEntity.setParam07(outEntity.getParam07());
        inEntity.setParam08(outEntity.getParam08());
        inEntity.setParam09(outEntity.getParam09());
        inEntity.setParam10(outEntity.getParam10());
        inEntity.setParam11(outEntity.getParam11());
        inEntity.setParam12(outEntity.getParam12());
        inEntity.setParam13(outEntity.getParam13());
        inEntity.setParam14(outEntity.getParam14());
        inEntity.setParam15(outEntity.getParam15());
        inEntity.setParam16(outEntity.getParam16());
        inEntity.setParam17(outEntity.getParam17());
        inEntity.setParam18(outEntity.getParam18());
        inEntity.setParam19(outEntity.getParam19());
        inEntity.setParam20(outEntity.getParam20());
        inEntity.setParam21(outEntity.getParam21());
        inEntity.setParam22(outEntity.getParam22());
        inEntity.setParam23(outEntity.getParam23());
        inEntity.setParam24(outEntity.getParam24());
        inEntity.setParam25(outEntity.getParam25());
        inEntity.setParam26(outEntity.getParam26());
        inEntity.setParam27(outEntity.getParam27());
        inEntity.setParam28(outEntity.getParam28());
        inEntity.setParam29(outEntity.getParam29());
        inEntity.setParam30(outEntity.getParam30());
        inEntity.setParam31(outEntity.getParam31());
        inEntity.setParam32(outEntity.getParam32());
        inEntity.setParam33(outEntity.getParam33());
        inEntity.setParam34(outEntity.getParam34());
        inEntity.setParam35(outEntity.getParam35());
        inEntity.setParam36(outEntity.getParam36());
        inEntity.setParam37(outEntity.getParam37());
        inEntity.setParam38(outEntity.getParam38());
        inEntity.setParam39(outEntity.getParam39());
        inEntity.setParam40(outEntity.getParam40());
        inEntity.setParam41(outEntity.getParam41());
        inEntity.setParam42(outEntity.getParam42());
        inEntity.setParam43(outEntity.getParam43());
        inEntity.setParam44(outEntity.getParam44());
        inEntity.setParam45(outEntity.getParam45());
        inEntity.setParam46(outEntity.getParam46());
        inEntity.setParam47(outEntity.getParam47());
        inEntity.setParam48(outEntity.getParam48());
        inEntity.setParam49(outEntity.getParam49());
        inEntity.setParam50(outEntity.getParam50()); 

        return inEntity;
    }

    /**
     * 保存Entityに　Param01～Param50値は　画面上の設定値を入替える。
     *
     * @param kinouName   機能名
     * @param inDto       inデータ
     * @param inEntity    inエンティティ 
     */
    private ComMocPrtiniS setComMocPrtiniS(String kinouName,ComMocPrtiniSUpdateServiceInDto inDto,ComMocPrtiniS inEntity) {
        switch (kinouName) {
            case KINOU_NAME_CARE_PLAN_1:
                // 敬称変更
                inEntity.setParam03(inDto.getCarePlan1Data().getParam03());
                // 敬称
                inEntity.setParam04(inDto.getCarePlan1Data().getParam04());
                // 敬承認欄
                inEntity.setParam05(inDto.getCarePlan1Data().getParam05());
                // 枠の高さ
                inEntity.setParam06(inDto.getCarePlan1Data().getParam06());
                // 事業名略称
                inEntity.setParam07(inDto.getCarePlan1Data().getParam07());
                // 介護度
                inEntity.setParam08(inDto.getCarePlan1Data().getParam08());
                // 作成年月日
                inEntity.setParam09(inDto.getCarePlan1Data().getParam09());
                break;
            case KINOU_NAME_CARE_PLAN_2:
                // 敬称変更
                inEntity.setParam03(inDto.getCarePlan2Data().getParam03());
                // 敬称
                inEntity.setParam04(inDto.getCarePlan2Data().getParam04());
                // 敬承認欄
                inEntity.setParam05(inDto.getCarePlan2Data().getParam05());
                // 枠の高さ
                inEntity.setParam06(inDto.getCarePlan2Data().getParam06());
                // 高さの最小値
                inEntity.setParam12(inDto.getCarePlan2Data().getParam12());
                // 先頭改ページ
                inEntity.setParam10(inDto.getCarePlan2Data().getParam10());
                // 印刷要介護度の取得方法オプションセット
                inEntity.setParam09(inDto.getCarePlan2Data().getParam09());
                // 作成年月日
                inEntity.setParam13(inDto.getCarePlan2Data().getParam13());
                // 事業所名
                inEntity.setParam30(inDto.getCarePlan2Data().getParam30());
                // 事業所略名
                inEntity.setParam31(inDto.getCarePlan2Data().getParam31());
                break;
            case KINOU_NAME_WEEK_PLAN:
                // 敬称変更
                inEntity.setParam03(inDto.getWeekPlanData().getParam03());
                // 敬称
                inEntity.setParam04(inDto.getWeekPlanData().getParam04());
                // 敬承認欄
                inEntity.setParam05(inDto.getWeekPlanData().getParam05());
                // モノクロモード
                inEntity.setParam06(inDto.getWeekPlanData().getParam06());
                // 処理年月
                inEntity.setParam07(inDto.getWeekPlanData().getParam07());
                // 右上
                inEntity.setParam08(inDto.getWeekPlanData().getParam08());
                // 処理年月文字
                inEntity.setParam09(inDto.getWeekPlanData().getParam09());
                // 印刷要介護度の取得方法オプションセット
                inEntity.setParam10(inDto.getWeekPlanData().getParam10());
                // 週単位以外
                inEntity.setParam11(inDto.getWeekPlanData().getParam11());
                // 作成年月日
                inEntity.setParam13(inDto.getWeekPlanData().getParam13());
                // 事業所名
                inEntity.setParam30(inDto.getWeekPlanData().getParam30());
                // 事業所略名
                inEntity.setParam31(inDto.getWeekPlanData().getParam31());
                break;
            case KINOU_NAME_DAILY_PLAN:
                // 敬称変更
                inEntity.setParam03(inDto.getDailyCarePlanData().getParam03());
                // 敬称
                inEntity.setParam04(inDto.getDailyCarePlanData().getParam04());
                // 敬承認欄
                inEntity.setParam05(inDto.getDailyCarePlanData().getParam05());
                // 印刷要介護度の取得方法オプションセット
                inEntity.setParam07(inDto.getDailyCarePlanData().getParam07());
                // 作成年月日
                inEntity.setParam13(inDto.getDailyCarePlanData().getParam13());
                break;
            default:
                break;
        }
        return inEntity;
    }

    /**
     * 取得した印刷iniファイルデータテーブル情報を 印刷iniファイルデータ保存テーブルに格納する
     *
     * @param inEntity    inエンティティ 
     * @param outEntity   outエンティティ
     * @param inDto       inデータ
     */
    private ComMocPrtiniS setComMocPrtiniSDefault2(ComMocPrtiniS inEntity,ComMocPrtiniInfoOutEntity outEntity,ComMocPrtiniSUpdateServiceInDto inDto) {
        // 職員ID
        inEntity.setShokuId(CommonDtoUtil.strValToInt(inDto.getShokuId()));
        // 法人ID
        inEntity.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        inEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業所ID
        inEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // セクション名
        inEntity.setSectionName(outEntity.getSectionName());
        // システム略称
        inEntity.setSysRyaku(SYS_RYAKU_3GK);
        // セクションNo
        inEntity.setSectionNo(outEntity.getSectionNo());
        // 帳票番号
        inEntity.setPrtNo(outEntity.getPrtNo());
        // dwobject
        inEntity.setDwobject(CommonConstants.SPACE_STRING);
        // prt_size
        inEntity.setPrtSize(CommonConstants.INT_0);
        // prt_orient
        inEntity.setPrtOrient(CommonConstants.INT_0);
        // list_title
        inEntity.setListTitle(CommonConstants.SPACE_STRING);
        // prt_title
        inEntity.setPrtTitle(CommonConstants.SPACE_STRING);
        // prndate
        inEntity.setPrndate(CommonConstants.INT_0);
        // prnshoku
        inEntity.setPrnshoku(CommonConstants.INT_0);
        // serial_flg
        inEntity.setSerialFlg(CommonConstants.INT_0);
        // mod_flg
        inEntity.setModFlg(CommonConstants.INT_0);
        // sec_flg
        inEntity.setSecFlg(CommonConstants.INT_0);
        // serial_height
        inEntity.setSerialHeight(CommonConstants.INT_0);
        // serial_pagelen
        inEntity.setSerialPagelen(CommonConstants.INT_0);
        // hsj_id
        inEntity.setHsjId(CommonConstants.INT_0);
        // m_top
        inEntity.setMTop(CommonConstants.INT_MINUS_1);
        // m_bottom
        inEntity.setMBottom(CommonConstants.INT_MINUS_1);
        // m_left
        inEntity.setMLeft(CommonConstants.INT_MINUS_1);
        // m_right
        inEntity.setMRight(CommonConstants.INT_MINUS_1);
        // ruler
        inEntity.setRuler(CommonConstants.INT_MINUS_1);
        // prt_title
        inEntity.setPrtTitle(outEntity.getDefPrtTitle());
        // prndate
        inEntity.setPrndate(Integer.valueOf(outEntity.getDefPrndate()));
        // prnshoku
        inEntity.setPrnshoku(Integer.valueOf(outEntity.getDefPrnshoku()));
        
        // 上記で取得したParam01～Param50の値は　出力Entityに格納する。
        inEntity.setParam01(CommonConstants.SPACE_STRING);
        inEntity.setParam02(CommonConstants.SPACE_STRING);
        inEntity.setParam03(CommonConstants.SPACE_STRING);
        inEntity.setParam04(CommonConstants.SPACE_STRING);
        inEntity.setParam05(CommonConstants.SPACE_STRING);
        inEntity.setParam06(CommonConstants.SPACE_STRING);
        inEntity.setParam07(CommonConstants.SPACE_STRING);
        inEntity.setParam08(CommonConstants.SPACE_STRING);
        inEntity.setParam09(CommonConstants.SPACE_STRING);
        inEntity.setParam10(CommonConstants.SPACE_STRING);
        inEntity.setParam11(CommonConstants.SPACE_STRING);
        inEntity.setParam12(CommonConstants.SPACE_STRING);
        inEntity.setParam13(CommonConstants.SPACE_STRING);
        inEntity.setParam14(CommonConstants.SPACE_STRING);
        inEntity.setParam15(CommonConstants.SPACE_STRING);
        inEntity.setParam16(CommonConstants.SPACE_STRING);
        inEntity.setParam17(CommonConstants.SPACE_STRING);
        inEntity.setParam18(CommonConstants.SPACE_STRING);
        inEntity.setParam19(CommonConstants.SPACE_STRING);
        inEntity.setParam20(CommonConstants.SPACE_STRING);
        inEntity.setParam21(CommonConstants.SPACE_STRING);
        inEntity.setParam22(CommonConstants.SPACE_STRING);
        inEntity.setParam23(CommonConstants.SPACE_STRING);
        inEntity.setParam24(CommonConstants.SPACE_STRING);
        inEntity.setParam25(CommonConstants.SPACE_STRING);
        inEntity.setParam26(CommonConstants.SPACE_STRING);
        inEntity.setParam27(CommonConstants.SPACE_STRING);
        inEntity.setParam28(CommonConstants.SPACE_STRING);
        inEntity.setParam29(CommonConstants.SPACE_STRING);
        inEntity.setParam30(CommonConstants.SPACE_STRING);
        inEntity.setParam31(CommonConstants.SPACE_STRING);
        inEntity.setParam32(CommonConstants.SPACE_STRING);
        inEntity.setParam33(CommonConstants.SPACE_STRING);
        inEntity.setParam34(CommonConstants.SPACE_STRING);
        inEntity.setParam35(CommonConstants.SPACE_STRING);
        inEntity.setParam36(CommonConstants.SPACE_STRING);
        inEntity.setParam37(CommonConstants.SPACE_STRING);
        inEntity.setParam38(CommonConstants.SPACE_STRING);
        inEntity.setParam39(CommonConstants.SPACE_STRING);
        inEntity.setParam40(CommonConstants.SPACE_STRING);
        inEntity.setParam41(CommonConstants.SPACE_STRING);
        inEntity.setParam42(CommonConstants.SPACE_STRING);
        inEntity.setParam43(CommonConstants.SPACE_STRING);
        inEntity.setParam44(CommonConstants.SPACE_STRING);
        inEntity.setParam45(CommonConstants.SPACE_STRING);
        inEntity.setParam46(CommonConstants.SPACE_STRING);
        inEntity.setParam47(CommonConstants.SPACE_STRING);
        inEntity.setParam48(CommonConstants.SPACE_STRING);
        inEntity.setParam49(CommonConstants.SPACE_STRING);
        inEntity.setParam50(CommonConstants.SPACE_STRING); 
        inEntity.setParam04(CommonConstants.STR_1);
        inEntity.setParam14(PARAM14_DEFAULT_VALUE);
        inEntity.setParam15(PARAM15_DEFAULT_VALUE);
        inEntity.setParam16(PARAM16_DEFAULT_VALUE);
        inEntity.setParam17(PARAM17_DEFAULT_VALUE);
        inEntity.setParam18(PARAM18_DEFAULT_VALUE);
        inEntity.setParam24(PARAM24_DEFAULT_VALUE);
        inEntity.setParam25(PARAM25_DEFAULT_VALUE);
        inEntity.setParam26(PARAM26_DEFAULT_VALUE);
        inEntity.setParam27(PARAM27_DEFAULT_VALUE);
        inEntity.setParam28(PARAM28_DEFAULT_VALUE);
        inEntity.setParam11(PARAM11_DEFAULT_VALUE);
        inEntity.setParam12(PARAM12_DEFAULT_VALUE);
        inEntity.setParam21(PARAM21_DEFAULT_VALUE);
        inEntity.setParam22(PARAM22_DEFAULT_VALUE);
        inEntity.setParam23(PARAM23_DEFAULT_VALUE);

        return inEntity;
    }

    /**
     * 保存Entityに　Param01～Param50値は　画面上の設定値を入替える。
     *
     * @param inDto       inデータ
     * @param inEntity    inエンティティ 
     */
    private ComMocPrtiniS setComMocPrtiniS2(ComMocPrtiniSUpdateServiceInDto inDto,ComMocPrtiniS inEntity) {
         // 両面
        inEntity.setParam04(inDto.getDoubleSidedPrintingData().getParam04());
        // 計画書インデックス
        String carePlanIndexs = CommonConstants.SPACE_STRING;
        // 印刷選択
        String carePlanChecks = CommonConstants.SPACE_STRING;
        for (int i = 0; i < inDto.getCarePlanPrintDataList().size(); i++ ) {
            CarePlanPrintDataDto item = inDto.getCarePlanPrintDataList().get(i);
            carePlanIndexs += item.getCarePlanIndex();
            carePlanChecks += item.getCarePlanCheck();
        }

        // 6桁未満の場合、“0”を補足する
        if (carePlanIndexs.length() < 6) {
            carePlanIndexs += DEFAULT_VALUE_STR_00;
            carePlanChecks += DEFAULT_VALUE_STR_00;
        }

        if (inDto.getParamp().equals(PARAMP_STR_13)) {
            inEntity.setParam13(carePlanChecks);
            inEntity.setParam23(carePlanIndexs);
        } else if (inDto.getParamp().equals(PARAMP_STR_11)) {
            inEntity.setParam11(carePlanChecks);
            inEntity.setParam21(carePlanIndexs);
        } else if (inDto.getParamp().equals(PARAMP_STR_12)) {
            inEntity.setParam12(carePlanChecks);
            inEntity.setParam22(carePlanIndexs);
        }

        // param19、param29の値が　存在する場合、””をセットする
        if (!StringUtil.isNull(inEntity.getParam19())) {
            inEntity.setParam19(CommonConstants.SPACE_STRING);
        }
        if (!StringUtil.isNull(inEntity.getParam29())) {
            inEntity.setParam29(CommonConstants.SPACE_STRING);
        }
        return inEntity;
    }

    /**
     * 対象Entityの値をコピーする
     *
     * @param formEntity  コピー元エンティティ
     * @param toEntity    コピー先エンティティ
     */
    private void copyEntityData(ComMocPrtiniSInfoOutEntity formEntity,ComMocPrtiniS toEntity) {
        // 職員ID
        toEntity.setShokuId(formEntity.getShokuId());
        // システム略称
        toEntity.setSysRyaku(formEntity.getSysRyaku());
        // セクション番号
        toEntity.setSectionNo(formEntity.getSectionNo());
        // 帳票番号
        toEntity.setPrtNo(formEntity.getPrtNo());
        // セクション名
        toEntity.setSectionName(formEntity.getSectionName());
        // オブジェクト名
        toEntity.setDwobject(formEntity.getDwobject());
        // 用紙向き
        toEntity.setPrtOrient(Integer.valueOf(formEntity.getPrtOrient()));
        // 用紙サイズ
        toEntity.setPrtSize(Integer.valueOf(formEntity.getPrtSize()));
        // 帳票リスト名
        toEntity.setListTitle(formEntity.getListTitle());
        // 帳票タイトル
        toEntity.setPrtTitle(formEntity.getPrtTitle());
        // 上余白
        toEntity.setMTop(formEntity.getMTop());
        // 下余白
        toEntity.setMBottom(formEntity.getMBottom());
        // 左余白
        toEntity.setMLeft(formEntity.getMLeft());
        // 右余白
        toEntity.setMRight(formEntity.getMRight());
        // ルーラ表示有無
        toEntity.setRuler(Integer.valueOf(formEntity.getRuler()));
        // 日付表示有無
        toEntity.setPrndate(Integer.valueOf(formEntity.getPrndate()));
        // 職員表示有無
        toEntity.setPrnshoku(Integer.valueOf(formEntity.getPrnshoku()));
        // シリアルフラグ
        toEntity.setSerialFlg(Integer.valueOf(formEntity.getSerialFlg()));
        // モードフラグ
        toEntity.setModFlg(Integer.valueOf(formEntity.getModFlg()));
        // セクションフラグ
        toEntity.setSecFlg(Integer.valueOf(formEntity.getSecFlg()));
        // パラメータ01
        toEntity.setParam01(formEntity.getParam01());
        // パラメータ02
        toEntity.setParam02(formEntity.getParam02());
        // パラメータ03
        toEntity.setParam03(formEntity.getParam03());
        // パラメータ04
        toEntity.setParam04(formEntity.getParam04());
        // パラメータ05
        toEntity.setParam05(formEntity.getParam05());
        // パラメータ06
        toEntity.setParam06(formEntity.getParam06());
        // パラメータ07
        toEntity.setParam07(formEntity.getParam07());
        // パラメータ08
        toEntity.setParam08(formEntity.getParam08());
        // パラメータ09
        toEntity.setParam09(formEntity.getParam09());
        // パラメータ10
        toEntity.setParam10(formEntity.getParam10());
        // パラメータ11
        toEntity.setParam11(formEntity.getParam11());
        // パラメータ12
        toEntity.setParam12(formEntity.getParam12());
        // パラメータ13
        toEntity.setParam13(formEntity.getParam13());
        // パラメータ14
        toEntity.setParam14(formEntity.getParam14());
        // パラメータ15
        toEntity.setParam15(formEntity.getParam15());
        // パラメータ16
        toEntity.setParam16(formEntity.getParam16());
        // パラメータ17
        toEntity.setParam17(formEntity.getParam17());
        // パラメータ18
        toEntity.setParam18(formEntity.getParam18());
        // パラメータ19
        toEntity.setParam19(formEntity.getParam19());
        // パラメータ20
        toEntity.setParam20(formEntity.getParam20());
        // パラメータ21
        toEntity.setParam21(formEntity.getParam21());
        // パラメータ22
        toEntity.setParam22(formEntity.getParam22());
        // パラメータ23
        toEntity.setParam23(formEntity.getParam23());
        // パラメータ24
        toEntity.setParam24(formEntity.getParam24());
        // パラメータ25
        toEntity.setParam25(formEntity.getParam25());
        // パラメータ26
        toEntity.setParam26(formEntity.getParam26());
        // パラメータ27
        toEntity.setParam27(formEntity.getParam27());
        // パラメータ28
        toEntity.setParam28(formEntity.getParam28());
        // パラメータ29
        toEntity.setParam29(formEntity.getParam29());
        // パラメータ30
        toEntity.setParam30(formEntity.getParam30());
        // パラメータ31
        toEntity.setParam31(formEntity.getParam31());
        // パラメータ32
        toEntity.setParam32(formEntity.getParam32());
        // パラメータ33
        toEntity.setParam33(formEntity.getParam33());
        // パラメータ34
        toEntity.setParam34(formEntity.getParam34());
        // パラメータ35
        toEntity.setParam35(formEntity.getParam35());
        // パラメータ36
        toEntity.setParam36(formEntity.getParam36());
        // パラメータ37
        toEntity.setParam37(formEntity.getParam37());
        // パラメータ38
        toEntity.setParam38(formEntity.getParam38());
        // パラメータ39
        toEntity.setParam39(formEntity.getParam39());
        // パラメータ40
        toEntity.setParam40(formEntity.getParam40());
        // パラメータ41
        toEntity.setParam41(formEntity.getParam41());
        // パラメータ42
        toEntity.setParam42(formEntity.getParam42());
        // パラメータ43
        toEntity.setParam43(formEntity.getParam43());
        // パラメータ44
        toEntity.setParam44(formEntity.getParam44());
        // パラメータ45
        toEntity.setParam45(formEntity.getParam45());
        // パラメータ46
        toEntity.setParam46(formEntity.getParam46());
        // パラメータ47
        toEntity.setParam47(formEntity.getParam47());
        // パラメータ48
        toEntity.setParam48(formEntity.getParam48());
        // パラメータ49
        toEntity.setParam49(formEntity.getParam49());
        // パラメータ50
        toEntity.setParam50(formEntity.getParam50());
        // 高さ
        toEntity.setSerialHeight(formEntity.getSerialHeight());
        // 印刷行数
        toEntity.setSerialPagelen(Integer.valueOf(formEntity.getSerialPagelen()));
        // 法人or施設or事業所ID
        toEntity.setHsjId(formEntity.getHsjId());
        // 法人ID
        toEntity.setHoujinId(formEntity.getHoujinId());
        // 施設ID
        toEntity.setShisetuId(formEntity.getShisetuId());
        // サービス事業者ID
        toEntity.setSvJigyoId(formEntity.getSvJigyoId());
        // 表示内拡大率
        toEntity.setZoomRate(Integer.valueOf(formEntity.getZoomRate()));
    }
}
