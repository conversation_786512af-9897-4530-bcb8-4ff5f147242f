package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.KghCmn03gFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnRiyou01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.FygJohoNextOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.Gui01169Meisai;
import jp.ndsoft.carebase.cmn.api.logic.dto.Gui01169SumData;
import jp.ndsoft.carebase.cmn.api.logic.dto.Gui01169kouhi;
import jp.ndsoft.carebase.cmn.api.logic.dto.Gui01169maintenanceInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.UseSlipOtherDtlMaintenanceInitInfoSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Riyou;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149RiyouBeppyo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149RiyouSvItemNmInInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149RiyouSvItemNmOutInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.UseSlipOtherDtlMaintenanceInitInfoSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnComMscSvjigyoCByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnComMscSvjigyoCOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTucPlanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTucPlanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo1List202504ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo1List202504OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3KohiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3KohiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyoMaintSumByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyoMaintSumOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanBetu1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanKouhiFutanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ServiceJigyosyoInfoSelectMapper;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * <AUTHOR>
 * 
 *         GUI01169_利用票別表メンテナンスのAPI定義書_APINo(1151)_利用票別表メンテナンス画面初期情報取得
 */
@Service
public class UseSlipOtherDtlMaintenanceInitInfoSelectServiceImpl
        extends
        SelectServiceImpl<UseSlipOtherDtlMaintenanceInitInfoSelectServiceInDto, UseSlipOtherDtlMaintenanceInitInfoSelectServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 利用票ヘッダの情報を検索のDAO */
    @Autowired
    private CmnTucPlanSelectMapper cmnTucPlanSelectMapper;
    /** サービス種類コードを返すのDAO */
    @Autowired
    private KghCmnF01Logic kghCmnF01;
    /** '別表明細情報の取得のDAO */
    @Autowired
    private CmnTucPlanBetu1SelectMapper cmnTucPlanBetu1SelectMapper;
    /** ''公費データ検索 */
    @Autowired
    private CmnTucPlanKouhiFutanSelectMapper cmnTucPlanKouhiFutanSelectMapper;
    /** ''利用票・別表履歴を検索する */
    @Autowired
    private UseSlipInitInfoSelectServiceImpl useSlipInitInfoSelectServiceImpl;

    /** KghCmnRiyou01Logic */
    @Autowired
    KghCmnRiyou01Logic kghCmnRiyou01Logic;

    /** KghCmn03gFunc01Logic */
    @Autowired
    KghCmn03gFunc01Logic kghCmn03gFunc01Logic;

    /** '''サービス事業者情報取得のDAO */
    @Autowired
    private ServiceJigyosyoInfoSelectMapper serviceJigyosyoInfoSelectMapper;

    /**
     * API定義書_APINo(1151)_利用票別表メンテナンス画面初期情報取得
     * 
     * @param inDto API定義書_APINo(1151)_利用票別表メンテナンス画面初期情報取得の入力DTO.
     * @return API定義書_APINo(1151)_利用票別表メンテナンス画面初期情報取得のOUT DTO
     * @throws Exception Exception
     */
    @Override
    protected UseSlipOtherDtlMaintenanceInitInfoSelectServiceOutDto mainProcess(
            UseSlipOtherDtlMaintenanceInitInfoSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        UseSlipOtherDtlMaintenanceInitInfoSelectServiceOutDto outDto = new UseSlipOtherDtlMaintenanceInitInfoSelectServiceOutDto();
        // 2. 変数宣言
        // 【変数】.外部利用フラグ
        int outerFlg;
        // 【変数】.明細リスト ※レスポンスパラメータ.明細リストのフォーマットを参照
        List<Gui01169Meisai> meisais = new ArrayList<>();
        // 【変数】.公費リスト ※レスポンスパラメータ.公費リストのフォーマットを参照
        List<Gui01169kouhi> kouhis = new ArrayList<>();
        // ・【変数】.変更日 = リクエストパラメータ.提供年月 + "/" + リクエストパラメータ.提供日
        String yymmd = inDto.getTeiYm() + CommonConstants.STR_DELIMITER + inDto.getTeiYmD();
        List<Gui01169SumData> sumDatas = new ArrayList<>();

        // 3.利用票ヘッダの情報を検索
        CmnTucPlanByCriteriaInEntity cmnTucPlanByCriteriaInEntity = new CmnTucPlanByCriteriaInEntity();
        // リクエストパラメータ.事業所ID
        cmnTucPlanByCriteriaInEntity.setAlShi(CommonDtoUtil.strValToInt(inDto.getDefSvJigyoId()));
        // リクエストパラメータ.利用者ID
        cmnTucPlanByCriteriaInEntity.setAlUsr(CommonDtoUtil.strValToInt(inDto.getUserid()));
        // リクエストパラメータ.提供年月
        cmnTucPlanByCriteriaInEntity.setAsYm(inDto.getTeiYm());
        // リクエストパラメータ.提供日
        cmnTucPlanByCriteriaInEntity.setAsD(inDto.getTeiYmD());

        List<CmnTucPlanOutEntity> outEntities = cmnTucPlanSelectMapper
                .findCmnTucPlanByCriteria(cmnTucPlanByCriteriaInEntity);
        Gui01169maintenanceInfo maintenanceInfo = new Gui01169maintenanceInfo();
        outDto.setMaintenanceInfo(maintenanceInfo);
        // ・レスポンスパラメータ.作成年月日 = 上記結果.作成日
        outDto.getMaintenanceInfo().setCreateYmd(
                outEntities.stream().findFirst().map(x -> x.getCreateYmd()).orElse(CommonConstants.BLANK_STRING));
        // 4.外部利用フラグを設定
        String kghSvType = kghCmnF01.getSvType(CommonDtoUtil.strValToInt(inDto.getDefSvJigyoId()));
        // 4.1 上記結果.サービス種類コード = "33" または "35" または "36"の場合、
        // ・【変数】.外部利用フラグ = 1：外部利用型
        if (kghSvType.equals(CommonConstants.NUM_STR_33) || kghSvType.equals(CommonConstants.NUM_STR_35)
                || kghSvType.equals(CommonConstants.NUM_STR_36)) {
            outerFlg = CommonConstants.INT_1;

        } else {
            // 4.2 上記以外の場合、
            // ・【変数】.外部利用フラグ = 0：通常
            outerFlg = CommonConstants.INT_0;

        }
        // 5.別表データの検索
        // 5.1. 明細データ検索
        KghCmnBeppyo1List202504ByCriteriaInEntity kghCmnBeppyo1List202504ByCriteriaInEntity = new KghCmnBeppyo1List202504ByCriteriaInEntity();
        // リクエストパラメータ.事業所ID
        kghCmnBeppyo1List202504ByCriteriaInEntity.setAlShienId(CommonDtoUtil.strValToInt(inDto.getDefSvJigyoId()));
        // リクエストパラメータ.利用者ID
        kghCmnBeppyo1List202504ByCriteriaInEntity.setAlUserid(CommonDtoUtil.strValToInt(inDto.getUserid()));
        // リクエストパラメータ.提供年月
        kghCmnBeppyo1List202504ByCriteriaInEntity.setAsYymmYm(inDto.getTeiYm());
        // リクエストパラメータ.提供日
        kghCmnBeppyo1List202504ByCriteriaInEntity.setAsYymmD(inDto.getTeiYmD());

        List<KghCmnBeppyo1List202504OutEntity> planBetu1OutEntities = cmnTucPlanBetu1SelectMapper
                .findKghCmnBeppyo1List202504ByCriteria(kghCmnBeppyo1List202504ByCriteriaInEntity);
        for (KghCmnBeppyo1List202504OutEntity kghCmnBeppyo1List202504OutEntity : planBetu1OutEntities) {
            Gui01169Meisai meisai = new Gui01169Meisai();

            // 支援事業者ID
            meisai.setShienId(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getShienId()));
            // 利用者ID
            meisai.setUserid(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getUserid()));
            // サービス提供年月
            meisai.setYymmYm(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getYymmYm()));
            // サービス提供年月（変更日）
            meisai.setYymmD(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getYymmD()));
            // サービス事業者ID
            meisai.setSvJigyoId(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getSvJigyoId()));
            // サービス項目ID
            meisai.setSvItemCd(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getSvItemCd()));
            // 枝番
            meisai.setEdaNo(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getEdaNo()));
            // サービス単位数
            meisai.setTensu(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getTensu()));
            // 回数
            meisai.setKaisu(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getKaisu()));
            // 合計単位数
            meisai.setSTensu(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getSvTensu()));
            // 種類限度外単位数
            meisai.setSTensuOver(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getSTensuOver()));
            // 種類限度額内単位数
            meisai.setSTensu(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getSTensu()));
            // 区分支給限度外単位数
            meisai.setKTensuOver(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getKTensuOver()));
            // 区分支給限度内単位数
            meisai.setKTensu(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getKTensu()));
            // 単位数単価
            meisai.setTanka(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getTanka()));
            // 利用者負担額保険給付対象分
            meisai.setHutanH(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getHutanH()));
            // 利用者負担額全額自己負担分
            meisai.setHutanJ(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getHutanJ()));
            // サービス種類CD
            meisai.setSvtype(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getSvtype()));
            // サービス項目CD
            meisai.setSvcode(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getSvcode()));
            // 合計フラグ
            meisai.setTotalF(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getTotalF()));
            // 費用総額
            meisai.setHiyouSougaku(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getHiyouSougaku()));
            // 給付率
            meisai.setKyufuRitu(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getKyufuRitu()));
            // 介護保険給付額
            meisai.setHKyufugaku(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getHKyufugaku()));
            // 加算フラグ
            meisai.setKasanF(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getKasanF()));
            // サービスコード
            meisai.setScode(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getScode()));
            // 割引率
            meisai.setWaribikiRitu(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getWaribikiRitu()));
            // 割引後単位数
            meisai.setWaribikiTen(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getWaribikiTen()));
            // 30日超過フラグ
            meisai.setOv30Fl(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getOv30Fl()));
            // 定額利用者負担単価金額
            meisai.setTHutanTanka(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getTHutanTanka()));
            // 給付率差異フラグ
            meisai.setKyufuDiffF(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getKyufuDiffF()));
            // DMYFormalnameKnj
            meisai.setDmyFormalnameKnj(
                    CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getDmyFormalnameKnj()));
            // DMYJigyoNumber
            meisai.setDmyJigyoNumber(
                    CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getDmyJigyoNumber()));
            // DMYCalcSort
            meisai.setDmyCalcSort(CommonDtoUtil.objValToString(kghCmnBeppyo1List202504OutEntity.getDmyCalcSort()));
            meisais.add(meisai);

        }
        // 5.2.【変数】.明細リストでループし、以下の処理を実行する。
        for (Gui01169Meisai meisai : meisais) {
            Gui01149RiyouSvItemNmInInfo nmIninfo = new Gui01149RiyouSvItemNmInInfo();
            Gui01149RiyouBeppyo riyouBeppyo = new Gui01149RiyouBeppyo();
            // 支援事業者ID
            riyouBeppyo.setShienId(meisai.getShienId());
            // 利用者ID
            riyouBeppyo.setUserid(meisai.getUserid());
            // サービス提供年月
            riyouBeppyo.setYymmYm(meisai.getYymmYm());
            // サービス提供年月（変更日）
            riyouBeppyo.setYymmD(meisai.getYymmD());
            // サービス事業者ID
            riyouBeppyo.setSvJigyoId(meisai.getSvJigyoId());
            // サービス項目ID
            riyouBeppyo.setSvItemCd(meisai.getSvItemCd());
            // 枝番
            riyouBeppyo.setEdaNo(meisai.getEdaNo());
            // サービス単位数
            riyouBeppyo.setTensu(meisai.getTensu());
            // 回数
            riyouBeppyo.setKaisu(meisai.getKaisu());
            // 合計単位数
            riyouBeppyo.setSvTensu(meisai.getSvTensu());
            // 種類限度外単位数
            riyouBeppyo.setSTensuOver(meisai.getSTensuOver());
            // 種類限度額内単位数
            riyouBeppyo.setSTensu(meisai.getSTensu());
            // 区分支給限度外単位数
            riyouBeppyo.setSTensuOver(meisai.getSTensuOver());
            // 区分支給限度内単位数
            riyouBeppyo.setKTensu(meisai.getKTensu());
            // 単位数単価
            riyouBeppyo.setTanka(meisai.getTanka());
            // 利用者負担額・保険給付対象分
            riyouBeppyo.setHutanH(meisai.getHutanH());
            // 利用者負担額・全額自己負担分
            riyouBeppyo.setHutanJ(meisai.getHutanJ());
            // サービス種類CD
            riyouBeppyo.setSvType(meisai.getSvtype());
            // サービス項目CD
            riyouBeppyo.setSvcode(meisai.getSvcode());
            // 合計フラグ
            riyouBeppyo.setTotalF(meisai.getTotalF());
            // 費用総額
            riyouBeppyo.setHiyouSougaku(meisai.getHiyouSougaku());
            // 給付率
            riyouBeppyo.setKyufuRitu(meisai.getKyufuRitu());
            // 介護保険給付額
            riyouBeppyo.setHKyufugaku(meisai.getHKyufugaku());
            // 加算フラグ
            riyouBeppyo.setKasanF(meisai.getKasanF());
            // サービスコード
            riyouBeppyo.setScode(meisai.getScode());
            // 割引率
            riyouBeppyo.setWaribikiRitu(meisai.getWaribikiRitu());
            // 割引後単位数
            riyouBeppyo.setWaribikiTen(meisai.getWaribikiTen());
            // ３０日超過フラグ
            riyouBeppyo.setOv30Fl(meisai.getOv30Fl());
            // 定額利用者負担単価金額
            riyouBeppyo.setTHutanTanka(meisai.getTHutanTanka());
            // 給付率差異フラグ
            riyouBeppyo.setKyufuDiffF(meisai.getKyufuDiffF());
            // サービス内容/種類
            riyouBeppyo.setDmyFormalnameKnj(meisai.getDmyFormalnameKnj());
            // 事業所番号
            riyouBeppyo.setDmyJigyoNumber(meisai.getDmyJigyoNumber());
            // 計算表示順
            riyouBeppyo.setDmyCalcSort(meisai.getDmyCalcSort());
            // 事業所名
            riyouBeppyo.setDmyJigyoNameKnj(meisai.getJigyonameRyaku());
            // サービス種類CD・ソート用
            riyouBeppyo.setDmySvtype(meisai.getDmySvtype());
            // 総合区分
            riyouBeppyo.setCSougouKbn(meisai.getCSougouKbn());
            // 給付率表示フラグ
            riyouBeppyo.setCSvTensu(meisai.getCSvTensu());
            // 計算・合計単位数
            riyouBeppyo.setCSTensuOver(meisai.getCSTensuOver());
            // 計算・種類限度外単位数
            riyouBeppyo.setCSTensu(meisai.getCSTensu());
            // 計算・種類限度額内単位数
            riyouBeppyo.setCKTensuOver(meisai.getCKTensuOver());
            // 計算・区分支給限度外単位数
            riyouBeppyo.setCKTensu(meisai.getCKTensu());
            // 計算・費用総額
            riyouBeppyo.setCHiyouSougaku(meisai.getCHiyouSougaku());
            // 計算・介護保険給付額
            riyouBeppyo.setCHKyufugaku(meisai.getCHKyufugaku());
            // 計算・利用者負担額・保険給付対象分
            riyouBeppyo.setCHutanH(meisai.getCHutanH());
            // レンタルフラグ
            riyouBeppyo.setIsRentalFlag(meisai.getIsRentalFlag());
            // 単位数単価表示フラグ
            riyouBeppyo.setTankaDisp(meisai.getTankaDisp());

            nmIninfo.setBeppyoInfo(riyouBeppyo);
            nmIninfo.setRiyouInfo(new Gui01149Riyou());
            // 5.2.1.サービス項目のセット
            Gui01149RiyouSvItemNmOutInfo outInfo = useSlipInitInfoSelectServiceImpl.acquDetailServiceItemNm(
                    CommonConstants.STR_3, meisai.getSvJigyoId(),
                    meisai.getSvItemCd(), inDto.getTeiYm(), inDto.getTeiYmD(), meisai.getOv30Fl(), nmIninfo);
            // 支援事業者ID
            meisai.setShienId(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getShienId()));
            // 利用者ID
            meisai.setUserid(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getUserid()));
            // サービス提供年月
            meisai.setYymmYm(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getYymmYm()));
            // サービス提供年月（変更日）
            meisai.setYymmD(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getYymmD()));
            // サービス事業者ID
            meisai.setSvJigyoId(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getSvJigyoId()));
            // サービス項目ID
            meisai.setSvItemCd(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getSvItemCd()));
            // 枝番
            meisai.setEdaNo(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getEdaNo()));
            // サービス単位数
            meisai.setTensu(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getTensu()));
            // 回数
            meisai.setKaisu(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getKaisu()));
            // 合計単位数
            meisai.setSTensu(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getSvTensu()));
            // 種類限度外単位数
            meisai.setSTensuOver(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getSTensuOver()));
            // 種類限度額内単位数
            meisai.setSTensu(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getSTensu()));
            // 区分支給限度外単位数
            meisai.setKTensuOver(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getKTensuOver()));
            // 区分支給限度内単位数
            meisai.setKTensu(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getKTensu()));
            // 単位数単価
            meisai.setTanka(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getTanka()));
            // 利用者負担額保険給付対象分
            meisai.setHutanH(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getHutanH()));
            // 利用者負担額全額自己負担分
            meisai.setHutanJ(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getHutanJ()));
            // サービス種類CD
            meisai.setSvtype(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getSvType()));
            // サービス項目CD
            meisai.setSvcode(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getSvcode()));
            // 合計フラグ
            meisai.setTotalF(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getTotalF()));
            // 費用総額
            meisai.setHiyouSougaku(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getHiyouSougaku()));
            // 給付率
            meisai.setKyufuRitu(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getKyufuRitu()));
            // 介護保険給付額
            meisai.setHKyufugaku(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getHKyufugaku()));
            // 加算フラグ
            meisai.setKasanF(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getKasanF()));
            // サービスコード
            meisai.setScode(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getScode()));
            // 割引率
            meisai.setWaribikiRitu(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getWaribikiRitu()));
            // 割引後単位数
            meisai.setWaribikiTen(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getWaribikiTen()));
            // 30日超過フラグ
            meisai.setOv30Fl(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getOv30Fl()));
            // 定額利用者負担単価金額
            meisai.setTHutanTanka(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getTHutanTanka()));
            // 給付率差異フラグ
            meisai.setKyufuDiffF(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getKyufuDiffF()));
            // DMYFormalnameKnj
            meisai.setDmyFormalnameKnj(
                    CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getDmyFormalnameKnj()));
            // DMYJigyoNumber
            meisai.setDmyJigyoNumber(
                    CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getDmyJigyoNumber()));
            // DMYCalcSort
            meisai.setDmyCalcSort(CommonDtoUtil.objValToString(outInfo.getBeppyoInfo().getDmyCalcSort()));
        }
        // 上記結果を【変数】.明細リスト.該当行に設定する
        // 5.3. 公費データ検索
        KghCmnBeppyo3KohiByCriteriaInEntity kghCmnBeppyo3KohiByCriteriaInEntity = new KghCmnBeppyo3KohiByCriteriaInEntity();
        // リクエストパラメータ.事業所ID
        kghCmnBeppyo3KohiByCriteriaInEntity.setAlShienId(CommonDtoUtil.strValToInt(inDto.getDefSvJigyoId()));
        // リクエストパラメータ.利用者ID
        kghCmnBeppyo3KohiByCriteriaInEntity.setAlUserid(CommonDtoUtil.strValToInt(inDto.getUserid()));
        // リクエストパラメータ.提供年月
        kghCmnBeppyo3KohiByCriteriaInEntity.setAsYymmYm(inDto.getTeiYm());
        // リクエストパラメータ.提供日
        kghCmnBeppyo3KohiByCriteriaInEntity.setAsYymmD(inDto.getTeiYmD());
        List<KghCmnBeppyo3KohiOutEntity> kohiOutEntitys = cmnTucPlanKouhiFutanSelectMapper
                .findKghCmnBeppyo3KohiByCriteria(kghCmnBeppyo3KohiByCriteriaInEntity);
        for (KghCmnBeppyo3KohiOutEntity entity : kohiOutEntitys) {
            Gui01169kouhi kouhi = new Gui01169kouhi();
            // サービス事業者ID
            kouhi.setSvJigyoId(CommonDtoUtil.objValToString(entity.getSvJigyoId()));
            // 公費分負担額
            kouhi.setFutanGaku(CommonDtoUtil.objValToString(entity.getFutanGaku()));
            // 公費分本人負担額
            kouhi.setHonninFutanGaku(CommonDtoUtil.objValToString(entity.getHonninFutanGaku()));
            // 支援事業者ID
            kouhi.setShienId(CommonDtoUtil.objValToString(entity.getShienId()));
            // 利用者ID
            kouhi.setUserid(CommonDtoUtil.objValToString(entity.getUserid()));
            // サービス提供年月
            kouhi.setYymmYm(CommonDtoUtil.objValToString(entity.getYymmYm()));
            // サービス提供年月（変更日）
            kouhi.setYymmD(CommonDtoUtil.objValToString(entity.getYymmD()));
            // 公費法制コード
            kouhi.setCode(CommonDtoUtil.objValToString(entity.getCode()));
            // サービス種類
            kouhi.setSvtype(CommonDtoUtil.objValToString(entity.getSvtype()));
            kouhis.add(kouhi);

        }
        outDto.getMaintenanceInfo().setKouhiList(kouhis);
        // 5.4. 短期日数データ検索
        KghCmnBeppyoMaintSumByCriteriaInEntity kghCmnBeppyoMaintSumByCriteriaInEntity = new KghCmnBeppyoMaintSumByCriteriaInEntity();
        // リクエストパラメータ.支援事業者ID
        kghCmnBeppyoMaintSumByCriteriaInEntity.setShienId(CommonDtoUtil.strValToInt(inDto.getDefSvJigyoId()));
        // リクエストパラメータ.利用者ID
        kghCmnBeppyoMaintSumByCriteriaInEntity.setUserid(CommonDtoUtil.strValToInt(inDto.getUserid()));
        // リクエストパラメータ.提供年月
        kghCmnBeppyoMaintSumByCriteriaInEntity.setTeiYm(inDto.getTeiYm());
        // リクエストパラメータ.提供日
        kghCmnBeppyoMaintSumByCriteriaInEntity.setTeiYmD(inDto.getTeiYmD());

        List<KghCmnBeppyoMaintSumOutEntity> sumOutEntities = cmnTucPlanSelectMapper
                .findKghCmnBeppyoMaintSumByCriteria(kghCmnBeppyoMaintSumByCriteriaInEntity);
        for (KghCmnBeppyoMaintSumOutEntity entity : sumOutEntities) {
            Gui01169SumData sumData = new Gui01169SumData();
            // 支援事業者ID
            sumData.setShienId(CommonDtoUtil.objValToString(entity.getShienId()));
            // 利用者ID
            sumData.setUserid(CommonDtoUtil.objValToString(entity.getUserid()));
            // サービス提供年月
            sumData.setYymmYm(CommonDtoUtil.objValToString(entity.getYymmYm()));
            // サービス提供年月（変更日）
            sumData.setYymmD(CommonDtoUtil.objValToString(entity.getYymmD()));
            // サービス単位数（予定）
            sumData.setSvTensuY(CommonDtoUtil.objValToString(entity.getSvTensuY()));
            // 利用者負担額・保険給付対象分
            sumData.setHHknHutan(CommonDtoUtil.objValToString(entity.getHHknHutan()));
            // 利用者負担額・全額自己負担分
            sumData.setHJihHutan(CommonDtoUtil.objValToString(entity.getHJihHutan()));
            // 短期入所 前月までの利用日数
            sumData.setTZengetu(CommonDtoUtil.objValToString(entity.getTZengetu()));
            // 短期入所 当月の計画利用日数
            sumData.setTTougetu(CommonDtoUtil.objValToString(entity.getTTougetu()));
            // 短期入所 累積利用日数
            sumData.setTRuiseki(CommonDtoUtil.objValToString(entity.getTRuiseki()));
            // 前月の３０日超の連続日数
            sumData.setOv30Zengetu(CommonDtoUtil.objValToString(entity.getOv30Zengetu()));
            // サービス単位数（実績）
            sumData.setSvTensuJ(CommonDtoUtil.objValToString(entity.getSvTensuJ()));
            // 種類限度超過単位数
            sumData.setHShuOver(CommonDtoUtil.objValToString(entity.getHShuOver()));
            // 種類限度内単位数
            sumData.setHShuKijun(CommonDtoUtil.objValToString(entity.getHShuKijun()));
            // 区分支給限度超過単位数
            sumData.setHKbnOver(CommonDtoUtil.objValToString(entity.getHKbnOver()));
            // 区分支給限度内単位数
            sumData.setHKbnKijun(CommonDtoUtil.objValToString(entity.getHKbnKijun()));
            sumDatas.add(sumData);

        }
        outDto.getMaintenanceInfo().setSumData(sumDatas);
        // 6..計算表示順のセット
        // f_kgh_cmn_set_adder_service_betu_dw #11036
        kghCmnRiyou01Logic.setAdderServiceBetuDw(meisais, yymmd);

        // 7.福祉用具情報のセット
        // リクエストパラメータ.提供年月 >= "2025/04" の場合、以下の処理を実行する
        if (inDto.getTeiYm().compareTo(CommonConstants.STR_YM_2025_04) >= 0) {
            // 【繰り返し開始】：【変数】.明細リストで件数分ループし、以下の処理を実行する。
            for (Gui01169Meisai meisai : meisais) {
                // 7.1. 【変数】.福祉用具情報構造体を生成する（空値）
                // 7.2. 【変数】.明細リスト.サービスコードの先頭2桁が"17" OR "67" OR 【変数】.明細リスト.サービスコードの先頭4桁が"3320"
                // OR "3519"の場合
                if (CommonConstants.SV_TYPE_CHECK_LIST_7
                        .contains(StringUtils.substring(meisai.getScode(), 2))
                        || CommonConstants.STR_3320.equals(StringUtils.substring(meisai.getScode(), 4))
                        || CommonConstants.STR_3519.equals(StringUtils.substring(meisai.getScode(), 4))) {
                    // 7.2.1. 【変数】.明細リスト.福祉用具貸与マスタIDが NULL でない、且つ、0 でない場合
                    if (StringUtils.isNotEmpty(meisai.getFygId())
                            && !CommonConstants.STR_0.equals(meisai.getFygId())) {
                        // 以下の共通関数を呼び出し、用具名称と TAISコードを取得する
                        FygJohoNextOutDto fygJohoNextOutDto = kghCmnRiyou01Logic.kghCmnGetFygJohoNext(
                                CommonDtoUtil.strValToInt(meisai.getFygId()),
                                inDto.getTeiYm() + CommonConstants.STR_DELIMITER + inDto.getTeiYmD());
                        // 上記結果.件数 > 0 の場合
                        if (fygJohoNextOutDto.getLlRet() > 0) {
                            // 【変数】.明細リスト.用具名称 = 【変数】.福祉用具情報構造体.商品名称
                            meisai.setFygShouhinKnj(fygJohoNextOutDto.getRiyouTaisJohoOutAstr().getShouhinKnj());
                            // 【変数】.明細リスト.TAISコード = 【変数】.福祉用具情報構造体.TAISコード/JANコード
                            meisai.setFygTekiyouCode(fygJohoNextOutDto.getRiyouTaisJohoOutAstr().getTekiyouCode());
                        }
                    }
                }
            }
        }

        // 8.事業所番号のセット
        // 【繰り返し開始】：【変数】.明細リストの件数 > 0の場合、【変数】.明細リストでループし、以下の処理を実行する。
        if (meisais != null && !meisais.isEmpty()) {
            for (Gui01169Meisai meisai : meisais) {
                // 8.1. 変数設定
                // ・【変数】.サービス事業者ID = 【変数】.明細リスト[ループ行].サービス事業者ID
                String socde = meisai.getSvJigyoId();
                // 8.2. 事業所番号取得
                // f_kgh_cmp_shien_number #7857
                String dmyJigyoNumber = kghCmn03gFunc01Logic.getShienNumber(Integer.parseInt(socde),
                        meisai.getYymmYm() + CommonConstants.STRING_FIRST_DAY);
                // ・【変数】.事業所番号 = 上記結果.事業所番号

                // 8.3. データセット
                meisai.setDmyJigyoNumber(dmyJigyoNumber);

            }
        }
        // 9.金額のセット
        // 9.1. 変数設定
        // ・【変数】.合計 = 0
        int sum = CommonConstants.INT_0;
        for (Gui01169Meisai meisai : meisais) {
            // 【繰り返し開始】：【変数】.明細リストでループし、以下の処理を実行する。
            // 9.2.1. 変数設定
            // ・【変数】.合計フラグ = 【変数】.明細リスト[ループ行].合計フラグ
            String totalF = meisai.getTotalF();
            if (totalF == null) {
                // 【変数】.合計フラグがNULLの場合、0をセット
                totalF = CommonConstants.STR_0;
            }
            // ・【変数】.加算フラグ = 【変数】.明細リスト[ループ行].加算フラグ
            String kasanF = meisai.getKasanF();
            if (kasanF == null) {
                // 【変数】.加算フラグがNULLの場合、0をセット
                kasanF = CommonConstants.STR_0;
            }
            // ・【変数】.３０日超過フラグ = 【変数】.明細リスト[ループ行].３０日超過フラグ
            String ov30F1 = meisai.getOv30Fl();
            if (ov30F1 == null) {
                // 【変数】.３０日超過フラグがNULLの場合、0をセット
                ov30F1 = CommonConstants.STR_0;
            }
            // 9.2.2. 【変数】.合計フラグ = 1：合計行なら総合計に加える
            if (totalF.equals(CommonConstants.STR_1)) {
                // ① 合計単位数取得
                // ・【変数】.合計単位数 = 【変数】.明細リスト[ループ行].合計単位数
                String svTensu = meisai.getSvTensu();

                if (svTensu == null) {
                    // 【変数】.合計単位数がNULLの場合、0をセット
                    svTensu = CommonConstants.STR_0;

                }
                // ② 【変数】.加算フラグ = 0 かつ 【変数】.３０日超過フラグ = 0の場合、
                if (kasanF.equals(CommonConstants.STR_0) && ov30F1.equals(CommonConstants.STR_0)) {
                    // ・【変数】.合計 += 【変数】.合計単位数
                    sum += CommonDtoUtil.strValToInt(svTensu);

                }

            }

        }
        // 9.3. 限度額超過フラグ設定
        // 9.3.1 【変数】.合計 > リクエストパラメータ.限度額の場合、

        if (sum > CommonDtoUtil.strValToInt(inDto.getGendo())) {
            // ・レスポンスパラメータ.限度額超過フラグ = 1
            outDto.getMaintenanceInfo().setOvGendoFlg(CommonConstants.STR_1);
        } else {
            // 9.3.2 上記以外の場合、
            // ・レスポンスパラメータ.限度額超過フラグ = 0
            outDto.getMaintenanceInfo().setOvGendoFlg(CommonConstants.STR_0);
        }

        CmnComMscSvjigyoCByCriteriaInEntity cmnComMscSvjigyoCByCriteriaInEntity = new CmnComMscSvjigyoCByCriteriaInEntity();

        List<CmnComMscSvjigyoCOutEntity> svjigyoCOutEntities = serviceJigyosyoInfoSelectMapper
                .findCmnComMscSvjigyoCByCriteria(cmnComMscSvjigyoCByCriteriaInEntity);
        for (Gui01169Meisai meisai : meisais) {
            // ・【変数】.サービス種類CD = 【変数】.明細リスト[ループ行].サービス種類CD
            String svType = meisai.getSvtype();
            String svTypeCopy = svType;
            // ・【変数】.サービス項目ID = 【変数】.明細リスト[ループ行].サービス項目ID
            String svItemCd = meisai.getSvItemCd();
            // ・【変数】.３０日超過フラグ = 【変数】.明細リスト[ループ行].３０日超過フラグ
            String ov30F1 = meisai.getOv30Fl();
            if (CommonDtoUtil.strValToInt(svItemCd) == CommonConstants.INT_0) {
                if (svjigyoCOutEntities != null && !svjigyoCOutEntities.isEmpty()) {
                    // 検索条件:サービス種類コード = 【変数】.サービス種類CD
                    CmnComMscSvjigyoCOutEntity entity = svjigyoCOutEntities.stream()
                            .filter(x -> svTypeCopy.equals(x.getSvKindCd()))
                            .findFirst()
                            .orElse(null);
                    // ②-1 【変数】.サービス種類CD = 【変数】.事業所名リスト[検索結果の該当行].サービス事業所名 + "合計"
                    if (entity != null) {
                        svType = entity.getSvJigyoKnj() + CommonConstants.STR_TOTAL;
                    }
                    // ②-2 【変数】.３０日超過フラグ = 1の場合
                    // ・【変数】.サービス種類CD = "30超" + 【変数】.サービス種類CD
                    if (CommonDtoUtil.strValToInt(ov30F1) == CommonConstants.INT_1) {
                        svType = CommonConstants.OV_30_STRING + svType;
                    }
                    // ②-3 データセット
                    // 【変数】.明細リスト[ループ行].サービス内容/種類「dmyFormalnameKnj」 = 【変数】.サービス種類CD
                    meisai.setDmyFormalnameKnj(svType);

                }

            }

        }
        // 10.ソートの設定

        if (outerFlg == CommonConstants.INT_1) {
            // 10.1 【変数】.外部利用フラグ = 1の場合

            // ・サービス種類CD・ソート用 昇順
            // ・３０日超過フラグ 昇順
            // ・加算フラグ 昇順
            // ・合計フラグ 昇順
            // ・事業所番号 昇順
            // ・サービス事業者ID 昇順
            // ・計算表示順 昇順
            // ・サービスコード 昇順
            // ・サービス項目ID 昇順
            // ・枝番 昇順

            meisais = meisais.stream()
                    .sorted(Comparator
                            .comparing(Gui01169Meisai::getDmySvtype)
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getOv30Fl))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getKasanF))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getTotalF))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getDmyJigyoNumber))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getSvJigyoId))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getDmyCalcSort))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getScode))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getSvItemCd))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getEdaNo)))
                    .collect(Collectors.toList());
        }
        if (outerFlg == CommonConstants.INT_0) {
            // 10.2 【変数】.外部利用フラグ = 0の場合、

            // ・サービス種類CD 昇順
            // ・事業所番号 昇順
            // ・サービス事業者ID 昇順
            // ・３０日超過フラグ 昇順
            // ・加算フラグ 昇順

            // ・合計フラグ 昇順
            // ・計算表示順 昇順
            // ・サービスコード 昇順
            // ・サービス項目ID 昇順
            // ・枝番 昇順
            meisais = meisais.stream()
                    .sorted(Comparator
                            .comparing(Gui01169Meisai::getSvtype)
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getDmyJigyoNumber))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getSvJigyoId))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getOv30Fl))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getKasanF))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getTotalF))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getDmyCalcSort))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getScode))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getSvItemCd))
                            .thenComparing(Comparator.comparing(Gui01169Meisai::getEdaNo)))
                    .collect(Collectors.toList());
        }
        // 11. レスポンスパラメータ.明細リスト = 【変数】.明細リスト
        outDto.getMaintenanceInfo().setMeisaiList(meisais);
        return outDto;

    }

}
