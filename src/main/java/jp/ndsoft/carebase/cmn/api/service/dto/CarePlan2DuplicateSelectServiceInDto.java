package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI01014_計画書（2）複写情報取得の入力Dto
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class CarePlan2DuplicateSelectServiceInDto
        extends IDtoImpl {
    /**
     * serialVersionUID.
     */
    private static final long serialVersionUID = 1L;

    /**
     * 計画書（２）ID (履歴ID)
     */
    @NotEmpty
    private String ks21Id;
    /**
     * 計画書様式
     */
    @NotEmpty
    private String cksflg;
    /**
     * 記録との連携
     */
    @NotEmpty
    private String kirokuRenkeiFlg;
}
