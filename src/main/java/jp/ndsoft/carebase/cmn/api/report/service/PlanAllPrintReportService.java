package jp.ndsoft.carebase.cmn.api.report.service;

import java.io.File;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.report.dto.DailyRoutinePlanReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.KyotakuServiceReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.Plan1kReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.Plan1sReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.PlanAllPrintReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.PlanAllPrintReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ShisetsuServiceReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ShuukanServiceR34ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.TeikyohyoReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.logic.DailyRoutinePlanReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.KyotakuServiceReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.Plan1kReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.Plan1sReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.ServiceRiteiReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.ServiceUseAnnexedTableReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.ShisetsuServiceReportLogic;
import jp.ndsoft.carebase.cmn.api.report.logic.ShuukanServiceR34ReportLogic;
import jp.ndsoft.carebase.cmn.api.report.model.PlanAllPrintParameterModel;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * U00891_計画書一括印刷 帳票出力
 * 
 * <AUTHOR>
 */
@Service("PlanAllPrint")
public class PlanAllPrintReportService
        extends
        PdfReportServiceImpl<PlanAllPrintParameterModel, PlanAllPrintReportServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** U0081K_居宅サービス計画書（１） 帳票出力 ロジッククラス */
    @Autowired
    private Plan1kReportLogic plan1kReportLogic;

    /** U0081S_施設サービス計画書（１） 帳票出力 ロジッククラス */
    @Autowired
    private Plan1sReportLogic plan1sReportLogic;

    /** 施設サービス計画書（２）帳票出力ロジック */
    @Autowired
    private ShisetsuServiceReportLogic shisetsuServiceReportLogic;

    /** アセスメント(居宅) 帳票出力 ロジッククラス */
    @Autowired
    private KyotakuServiceReportLogic kyotakuServiceReportLogic;

    /** 情報収集シート帳票出力ロジック */
    @Autowired
    private ShuukanServiceR34ReportLogic shuukanSvcRptLogic;

    /** U00861_日課計画表出力 ロジッククラス */
    @Autowired
    private DailyRoutinePlanReportLogic dailyRoutinePlanReportLogic;

    /** サービス利用票 帳票出力ロジック */
    @Autowired
    private ServiceRiteiReportLogic serviceRiteiReportLogic;

    /** サービス利用票別表 帳票出力ロジック */
    @Autowired
    ServiceUseAnnexedTableReportLogic serviceUseAnnexedTableReportLogic;

    @Override
    protected Object getReportParameters(PlanAllPrintParameterModel inDto, PlanAllPrintReportServiceOutDto outDto)
            throws Exception {
        LOG.info(Constants.START);

        // 帳票用データ詳細
        PlanAllPrintReportServiceInDto infoInDto = new PlanAllPrintReportServiceInDto();
        if (inDto.getData1() != null) {
            // U0081K_居宅サービス計画書（１）
            List<Plan1kReportServiceInDto> plan1kReportList = new ArrayList<Plan1kReportServiceInDto>();
            plan1kReportList.add(inDto.getData1());
            JRBeanCollectionDataSource dataSource1 = new JRBeanCollectionDataSource(plan1kReportList);
            infoInDto.setDataSource1(dataSource1);
        }
        if (inDto.getData2() != null) {
            // U0081S_施設サービス計画書(1)
            List<Plan1sReportServiceInDto> plan1sReportList = new ArrayList<Plan1sReportServiceInDto>();
            plan1sReportList.add(inDto.getData2());
            JRBeanCollectionDataSource dataSource2 = new JRBeanCollectionDataSource(plan1sReportList);
            infoInDto.setDataSource2(dataSource2);
        }
        if (inDto.getData3() != null) {
            // U0082S_施設サービス計画書（２）
            List<ShisetsuServiceReportServiceInDto> shisetsuInfoList = new ArrayList<ShisetsuServiceReportServiceInDto>();
            shisetsuInfoList.add(inDto.getData3());
            // if (!shisetsuInfoList.isEmpty()) {
            // shisetsuInfoList.get(0).setSubReportPath(subReportPath);
            // shisetsuInfoList.get(0).setSubReportDataDs(subReportDataDs);
            // }
            // U0082S_施設サービス計画書（２）帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSource3 = new JRBeanCollectionDataSource(shisetsuInfoList);
            infoInDto.setDataSource3(dataSource3);
        }
        if (inDto.getData4() != null) {
            // U0082K_居宅サービス計画書（２）
            List<KyotakuServiceReportServiceInDto> kyotakuServiceReportInfoList = new ArrayList<KyotakuServiceReportServiceInDto>();
            kyotakuServiceReportInfoList.add(inDto.getData4());
            // if (!kyotakuServiceReportInfoList.isEmpty()) {
            // kyotakuServiceReportInfoList.get(0).setSubReportPath(subReportPath);
            // kyotakuServiceReportInfoList.get(0).setSubReportDataDs(subReportDataDs);
            // }
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSource4 = new JRBeanCollectionDataSource(kyotakuServiceReportInfoList);
            infoInDto.setDataSource4(dataSource4);
        }
        if (inDto.getData5() != null) {
            // U00852_週間サービス計画表（R34改訂）
            List<ShuukanServiceR34ReportServiceInDto> shuukanServiceR34ReportInfoList = new ArrayList<ShuukanServiceR34ReportServiceInDto>();
            shuukanServiceR34ReportInfoList.add(inDto.getData5());
            // if (!shuukanServiceR34ReportInfoList.isEmpty()) {
            // shuukanServiceR34ReportInfoList.get(0).setSubReportPath(subReportPath);
            // shuukanServiceR34ReportInfoList.get(0).setSubReportDataDs(subReportDataDs);
            // }
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSource5 = new JRBeanCollectionDataSource(shuukanServiceR34ReportInfoList);
            infoInDto.setDataSource5(dataSource5);
        }
        if (inDto.getData6() != null) {
            // U00861_日課計画表出力
            List<DailyRoutinePlanReportServiceInDto> dailyRoutinePlanReportInfoList = new ArrayList<DailyRoutinePlanReportServiceInDto>();
            dailyRoutinePlanReportInfoList.add(inDto.getData6());
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSource6 = new JRBeanCollectionDataSource(dailyRoutinePlanReportInfoList);
            infoInDto.setDataSource6(dataSource6);
        }

        if (inDto.getData7() != null) {
            // V00231_サービス利用票
            List<TeikyohyoReportServiceInDto> teikyohyoReportServiceInDtoList = new ArrayList<>();
            teikyohyoReportServiceInDtoList.add(inDto.getData7());
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSource7 = new JRBeanCollectionDataSource(teikyohyoReportServiceInDtoList);
            infoInDto.setDataSource6(dataSource7);
        }

        if (!inDto.getData8().isEmpty()) {
            // V00231_サービス利用票別表
            // 帳票用のDataSourceを設定する
            JRBeanCollectionDataSource dataSource8 = new JRBeanCollectionDataSource(inDto.getData8());
            infoInDto.setDataSource6(dataSource8);
        }

        // ノート情報格納配列
        List<PlanAllPrintReportServiceInDto> reportInfoList = new ArrayList<PlanAllPrintReportServiceInDto>();
        reportInfoList.add(infoInDto);
        JRBeanCollectionDataSource dataSourceAll = new JRBeanCollectionDataSource(reportInfoList);
        infoInDto.setDataSourceAll(dataSourceAll);
        LOG.info(Constants.END);
        return infoInDto;
    }

    /**
     * 帳票出力
     * 
     * @param outDto 出力データ
     * @param inDto  入力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final PlanAllPrintParameterModel inDto,
            final PlanAllPrintReportServiceOutDto outDto) throws Exception {
        LOG.info(Constants.START);
        // 帳票レイアウトファイルのリソースを取得する
        final PlanAllPrintReportServiceInDto reportParameter = (PlanAllPrintReportServiceInDto) getReportParameters(
                inDto, outDto);

        List<JasperPrint> pdfJasperPrintList = new ArrayList<>();

        // U0081K_居宅サービス計画書（１）帳票
        if (reportParameter.getDataSource1() != null) {
            final JasperReport jasperFile = plan1kReportLogic.getJasperReport(getFwProps(), inDto.getData1(),
                    inDto.getParameterModel1());
            // 帳票出力
            final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile, reportParameter.getParameters(),
                    reportParameter.getDataSource1());
            pdfJasperPrintList.add(jasperPrint);
        }
        // U0081S_施設サービス計画書
        if (reportParameter.getDataSource2() != null) {
            final JasperReport jasperFile = plan1sReportLogic.getJasperReport(getFwProps(), inDto.getData2());
            // 帳票出力
            final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile, reportParameter.getParameters(),
                    reportParameter.getDataSource2());
            pdfJasperPrintList.add(jasperPrint);
        }
        // 施設サービス計画書（２）帳票出力ロジック
        if (reportParameter.getDataSource3() != null) {
            // TODO
            // final JasperReport jasperFile = shisetsuServiceReportLogic.getJasperReport();
            // // 帳票出力
            // final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile,
            // reportParameter.getParameters(),
            // reportParameter.getDataSource3());
            // pdfJasperPrintList.add(jasperPrint);
        }
        // U0082K_居宅サービス計画書（２）
        if (reportParameter.getDataSource4() != null) {
            // TODO
            // final JasperReport jasperFile = shisetsuServiceReportLogic.getJasperReport();
            // // 帳票出力
            // final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile,
            // reportParameter.getParameters(),
            // reportParameter.getDataSource3());
            // pdfJasperPrintList.add(jasperPrint);
        }
        // U00852_週間サービス計画表（R34改訂）
        if (reportParameter.getDataSource5() != null) {
            // TODO
            // final JasperReport jasperFile = shisetsuServiceReportLogic.getJasperReport();
            // // 帳票出力
            // final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile,
            // reportParameter.getParameters(),
            // reportParameter.getDataSource3());
            // pdfJasperPrintList.add(jasperPrint);
        }
        // U00861_日課計画表出力
        if (reportParameter.getDataSource6() != null) {
            final JasperReport jasperFile = dailyRoutinePlanReportLogic.getJasperReport(getFwProps(), inDto.getData6(),
                    inDto.getJigyoInfo().getSvJigyoId());
            // 帳票出力
            final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile, reportParameter.getParameters(),
                    reportParameter.getDataSource6());
            pdfJasperPrintList.add(jasperPrint);
        }

        // V00231_サービス利用票
        if (reportParameter.getDataSource7() != null) {
            // TODO
            // final JasperReport jasperFile = shisetsuServiceReportLogic.getJasperReport();
            // // 帳票出力
            // final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile,
            // reportParameter.getParameters(),
            // reportParameter.getDataSource3());
            // pdfJasperPrintList.add(jasperPrint);
        }

        // V00231_サービス利用票別表
        if (reportParameter.getDataSource8() != null) {
            // TODO
            // final JasperReport jasperFile = shisetsuServiceReportLogic.getJasperReport();
            // // 帳票出力
            // final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile,
            // reportParameter.getParameters(),
            // reportParameter.getDataSource3());
            // pdfJasperPrintList.add(jasperPrint);
        }

        JasperPrint reslut = JasperUtil.concatReports(pdfJasperPrintList);
        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(inDto, reslut);

        super.setFilepath(inDto, outDto, pdf, pdf.getName());
        LOG.info(Constants.END);
    }
}
