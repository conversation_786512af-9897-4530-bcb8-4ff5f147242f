package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckItemInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckMainRireki;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckNaiyou;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckPoint;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892RirekiInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.CheckItemsInsertSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.CheckItemsInsertSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChkGetYoushikiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChkGetYoushikiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChkMainRirekiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChkMainRirekiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkCheckNaiyouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkCheckNaiyouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ChkGetYoushikiInfoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ChkMainRirekiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocCheckNaiyouSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.06.03
 * <AUTHOR>
 * @implNote GUI00892_チェック項目画面 新規登録情報取得
 */
@Service
public class CheckItemsInsertSelectServiceImpl extends
        SelectServiceImpl<CheckItemsInsertSelectServiceInDto, CheckItemsInsertSelectServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** チェック項目新規処理情報を取得する */
    @Autowired
    private ChkGetYoushikiInfoSelectMapper chkGetYoushikiInfoSelectMapper;

    /** チェック項目評価内容マスタ情報取得 */
    @Autowired
    private KghMocCheckNaiyouSelectMapper kghMocCheckNaiyouSelectMapper;

    /** チェックメイン履歴情報を取得する */
    @Autowired
    private ChkMainRirekiSelectMapper chkMainRirekiSelectMapper;

    /**
     * 新規登録情報取得
     * 
     * @param inDto 新規登録情報取得サービス入力Dto
     * @return 新規登録情報取得サービス出力Dto
     * @throws Exception Exception
     */
    @Override
    protected CheckItemsInsertSelectServiceOutDto mainProcess(CheckItemsInsertSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);

        CheckItemsInsertSelectServiceOutDto outDto = new CheckItemsInsertSelectServiceOutDto();

        // チェック項目ヘッダ履歴情報
        Gui00892RirekiInfo rirekiInfo = new Gui00892RirekiInfo();

        // チェック項目評価内容マスタ情報

        // チェック項目詳細情報
        List<Gui00892CheckItemInfo> checkItemInfoList = new ArrayList<Gui00892CheckItemInfo>();

        // チェックメイン履歴情報
        List<Gui00892CheckMainRireki> checkMainRireki = new ArrayList<Gui00892CheckMainRireki>();

        // チェック項目評価内容マスタ情報
        HashMap<String, List<Gui00892CheckNaiyou>> checkNaiyouInfoMap = new HashMap<>();

        // 評価点数リスト
        HashMap<String, List<Gui00892CheckPoint>> checkPointsMap = new HashMap<>();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. 変数初期化===============
         * 
         */
        // 変数.履歴総件数にリクエストパラメータ.履歴総件数+1を設定する
        Integer rowCount = CommonDtoUtil.strValToInt(inDto.getRowCount()) + 1;
        /*
         * ===============3.下記のDAOを利用してチェック項目新規処理情報を取得する===============
         * 
         */
        // チェック項目新規処理情報
        // 3.1.1.変数.様式ID = 上記取得したチェック項目新規処理情報.様式ID
        Integer cstId = this.findChkGetYoushikiByCriteria(inDto, rirekiInfo, checkItemInfoList, rowCount);
        // 3.2 3.1以外の場合、
        if (Objects.isNull(cstId)) {
            // 6. レスポンスを返却する。
            return outDto;
        }
        outDto.setRirekiInfo(rirekiInfo);
        /*
         * ===============4. 下記のDAOを利用してチェック項目評価内容マスタ情報を取得する。===============
         * 
         */

        this.getCheckNaiyouInfo(inDto, cstId, checkNaiyouInfoMap,
                checkPointsMap, checkItemInfoList);
        /*
         * ===============5. 下記のDAOを利用してチェックメイン履歴情報を取得する。===============
         * 
         */
        // 3.3. 下記のDAOを利用してチェックメイン履歴情報を取得する。
        checkMainRireki = this.getCheckMainRireki(inDto, checkItemInfoList);
        /*
         * ===============4. レスポンスを返却する。===============
         * 
         */
        // チェック項目詳細情報
        outDto.setCheckItemInfo(checkItemInfoList);
        // チェックメイン履歴情報
        outDto.setCheckMainRireki(checkMainRireki);

        LOG.info(Constants.END);

        return outDto;
    }

    /**
     * チェック項目ヘッダ履歴情報を取得する
     * 
     * @param inDto             新規登録情報取得サービス入力Dto
     * @param rirekiInfo        チェック項目ヘッダ履歴情報
     * @param checkItemInfoList チェック項目詳細情報
     * @param rowCount          履歴総件数
     * 
     * @return チェック項目ヘッダ履歴情報
     */
    private Integer findChkGetYoushikiByCriteria(CheckItemsInsertSelectServiceInDto inDto,
            Gui00892RirekiInfo rirekiInfo, List<Gui00892CheckItemInfo> checkItemInfoList, Integer rowCount) {

        ChkGetYoushikiByCriteriaInEntity chkGetYoushikiByCriteriaInEntity = new ChkGetYoushikiByCriteriaInEntity();
        // 法人ID
        chkGetYoushikiByCriteriaInEntity.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        chkGetYoushikiByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        chkGetYoushikiByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));

        List<ChkGetYoushikiOutEntity> chkGetYoushikiList = this.chkGetYoushikiInfoSelectMapper
                .findChkGetYoushikiByCriteria(chkGetYoushikiByCriteriaInEntity);

        if (CollectionUtils.isNotEmpty(chkGetYoushikiList)) {
            // 3.1.2.チェック項目ヘッダ履歴情報を新規作成して下記のように値を設定する
            // チェック項目ヘッダ履歴情報.法人ID=リクエストパラメータ.法人ID
            rirekiInfo.setHoujinId(inDto.getHoujinId());
            // チェック項目ヘッダ履歴情報.施設ID=リクエストパラメータ.施設ID
            rirekiInfo.setShisetuId(inDto.getShisetuId());
            // チェック項目ヘッダ履歴情報.事業者ID=リクエストパラメータ.事業者ID
            rirekiInfo.setSvJigyoId(inDto.getSvJigyoId());
            // チェック項目ヘッダ履歴情報.利用者ID=リクエストパラメータ.利用者ID
            rirekiInfo.setUserId(inDto.getUserId());
            // チェック項目ヘッダ履歴情報.計画期間ID=リクエストパラメータ.計画期間ID
            rirekiInfo.setSc1Id(inDto.getSc1Id());
            // チェック項目ヘッダ履歴情報.様式ID=変数.様式ID
            rirekiInfo.setCstId(CommonDtoUtil.objValToString(chkGetYoushikiList.get(0).getYoushikiId()));
            // チェック項目ヘッダ履歴情報.作成日=リクエストパラメータ.作成日
            rirekiInfo.setCreateYmd(inDto.getCreateYmd());
            // チェック項目ヘッダ履歴情報.作成者=リクエストパラメータ.作成者
            rirekiInfo.setShokuId(inDto.getShokuId());
            // チェック項目ヘッダ履歴情報.履歴総件数=変数.履歴総件数
            rirekiInfo.setRowCount(CommonDtoUtil.objValToString(rowCount));
            // チェック項目ヘッダ履歴情報.履歴番号=変数.履歴総件数
            rirekiInfo.setKrirekiNo(CommonDtoUtil.objValToString(rowCount));

            // 3.1.3. チェック項目新規情報の件数で下記処理を繰り返す
            chkGetYoushikiList.forEach(chkGetYoushiki -> {
                // 3.1.3.1. チェック項目詳細情報を新規作成して下記値を設定する
                Gui00892CheckItemInfo checkItemInfo = new Gui00892CheckItemInfo();
                // チェック項目詳細情報.作成日=リクエストパラメータ.作成日
                checkItemInfo.setCreateYmd(inDto.getCreateYmd());
                // チェック項目詳細情報.項目ID=OUTPUT情報.項目ID
                checkItemInfo.setKoumokuId(CommonDtoUtil.objValToString(chkGetYoushiki.getKoumokuId()));
                // チェック項目詳細情報.項目名称=OUTPUT情報.項目名称
                checkItemInfo.setKoumokuKnj(CommonDtoUtil.objValToString(chkGetYoushiki.getKoumokuKnj()));
                // チェック項目詳細情報.細目ID=OUTPUT情報..細目ID
                checkItemInfo.setSaimokuId(CommonDtoUtil.objValToString(chkGetYoushiki.getSaimokuId()));
                // チェック項目詳細情報.細目名称=OUTPUT情報.細目名称
                checkItemInfo.setSaimokuKnj(CommonDtoUtil.objValToString(chkGetYoushiki.getSaimokuKnj()));
                // チェック項目詳細情報.課題対象フラグ=""
                checkItemInfo.setKadaiFlg("");
                // チェック項目詳細情報.備考=""
                checkItemInfo.setBiko("");
                // チェック項目詳細情報.点数=""
                checkItemInfo.setPoint("");
                // チェック項目詳細情報.内容ID=""
                checkItemInfo.setNaiyoId("");
                // チェック項目詳細情報.内容名称=""
                checkItemInfo.setNaiyouKnj("");

                checkItemInfoList.add(checkItemInfo);
            });

            return chkGetYoushikiList.get(0).getYoushikiId();
        }

        return null;
    }

    /**
     * チェック項目評価内容マスタ情報を取得する。
     * 
     * @param inDto              初期情報取得サービス入力Dto
     * @param cstId              様式ID
     * @param checkNaiyouInfoMap チェック項目評価内容マスタ情報
     * @param checkPointsMap     評価点数リスト
     * @param checkItemInfoList  チェック項目詳細情報
     * @return
     */
    private void getCheckNaiyouInfo(CheckItemsInsertSelectServiceInDto inDto,
            Integer youshikiId, HashMap<String, List<Gui00892CheckNaiyou>> checkNaiyouInfoMap,
            HashMap<String, List<Gui00892CheckPoint>> checkPointsMap, List<Gui00892CheckItemInfo> checkItemInfoList) {

        KghKrkCheckNaiyouByCriteriaInEntity kghKrkCheckNaiyouByCriteriaInEntity = new KghKrkCheckNaiyouByCriteriaInEntity();
        // 法人ID
        kghKrkCheckNaiyouByCriteriaInEntity.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        kghKrkCheckNaiyouByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        kghKrkCheckNaiyouByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 様式ID
        kghKrkCheckNaiyouByCriteriaInEntity.setYoushikiId(youshikiId);

        List<KghKrkCheckNaiyouOutEntity> kghKrkCheckNaiyouList = this.kghMocCheckNaiyouSelectMapper
                .findKghKrkCheckNaiyouByCriteria(kghKrkCheckNaiyouByCriteriaInEntity);

        if (CollectionUtils.isNotEmpty(kghKrkCheckNaiyouList)) {
            kghKrkCheckNaiyouList.forEach(kghKrkCheckNaiyou -> {
                // 5.1.1.下記処理中の変数初期化を行う
                Gui00892CheckNaiyou checkNaiyouInfoItem = new Gui00892CheckNaiyou();
                // 5.1.3.取得したチェック項目評価内容マスタ情報件数分で項目ID,細目ID毎に下記を行う
                // 内容ID
                checkNaiyouInfoItem.setNaiyouId(CommonDtoUtil.objValToString(kghKrkCheckNaiyou.getNaiyouId()));
                // 内容名称
                checkNaiyouInfoItem.setNaiyouKnj(CommonDtoUtil.objValToString(kghKrkCheckNaiyou.getNaiyouKnj()));
                // 評価点数を新規作成して下記項目値を設定する
                Gui00892CheckPoint checkPoint = new Gui00892CheckPoint();
                // 内容ID
                checkPoint.setNaiyouId(CommonDtoUtil.objValToString(kghKrkCheckNaiyou.getNaiyouId()));
                // 評価点数
                checkPoint.setPoint(CommonDtoUtil.objValToString(kghKrkCheckNaiyou.getPoint()));
                String key = String.format("%d%d", kghKrkCheckNaiyou.getKoumokuId(), kghKrkCheckNaiyou.getSaimokuId());
                // マップに追加する
                checkNaiyouInfoMap.putIfAbsent(key, new ArrayList<>());
                checkNaiyouInfoMap.get(key).add(checkNaiyouInfoItem);
                checkPointsMap.putIfAbsent(key, new ArrayList<>());
                checkPointsMap.get(key).add(checkPoint);
            });
            checkItemInfoList.forEach(chkMainInfo -> {
                // 5.2.1.3.チェック項目詳細情報の項目ID、細目IDにより変数.評価内容マップから評価内容リストを取得する
                // チェック項目詳細情報.評価内容リストに取得した評価内容リストを設定する
                String key = String.format("%d%d", chkMainInfo.getKoumokuId(), chkMainInfo.getSaimokuId());
                chkMainInfo.setCheckNaiyouList(checkNaiyouInfoMap.getOrDefault(key, new ArrayList<>()));
                // 5.2.1.4チェック項目詳細情報の項目ID、細目IDにより変数.評価点数マップから評価点数リストを取得する
                // チェック項目詳細情報.評価点数リストに取得した評価点数リストを設定する
                chkMainInfo.setCheckPointList(checkPointsMap.getOrDefault(key, new ArrayList<>()));
            });
        }
    }

    /**
     * チェックメイン履歴情報を取得する。
     * 
     * @param inDto             初期情報取得サービス入力Dto
     * @param checkItemInfoList チェック項目詳細情報
     * @return チェックメイン履歴情報
     */
    private List<Gui00892CheckMainRireki> getCheckMainRireki(CheckItemsInsertSelectServiceInDto inDto,
            List<Gui00892CheckItemInfo> checkItemInfoList) {
        // チェックメイン履歴情報
        List<Gui00892CheckMainRireki> checkMainRireki = new ArrayList<Gui00892CheckMainRireki>();

        ChkMainRirekiByCriteriaInEntity chkMainRirekiByCriteriaInEntity = new ChkMainRirekiByCriteriaInEntity();
        // 法人ID
        chkMainRirekiByCriteriaInEntity.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        chkMainRirekiByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        chkMainRirekiByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        chkMainRirekiByCriteriaInEntity.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 計画期間ID
        chkMainRirekiByCriteriaInEntity.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // 履歴の作成日付
        chkMainRirekiByCriteriaInEntity.setCreateYmd(inDto.getCreateYmd());
        // 0 ※新規の場合、0とする
        chkMainRirekiByCriteriaInEntity.setAssId(CommonConstants.NUMBER_0);

        List<ChkMainRirekiOutEntity> chkMainRirekiList = this.chkMainRirekiSelectMapper
                .findChkMainRirekiByCriteria(chkMainRirekiByCriteriaInEntity);

        if (CollectionUtils.isNotEmpty(chkMainRirekiList)) {
            chkMainRirekiList.forEach(chkMainRireki -> {
                if (checkItemInfoList.stream().anyMatch(item -> item.getKoumokuId()
                        .equals(CommonDtoUtil.objValToString(chkMainRireki.getKomokuId()))
                        && item.getSaimokuId().equals(CommonDtoUtil.objValToString(chkMainRireki.getSaimokuId())))) {

                    Gui00892CheckMainRireki checkMainRirekiItem = new Gui00892CheckMainRireki();
                    // 法人ID
                    checkMainRirekiItem.setHoujinId(CommonDtoUtil.objValToString(chkMainRireki.getHoujinId()));
                    // 施設ID
                    checkMainRirekiItem.setShisetuId(CommonDtoUtil.objValToString(chkMainRireki.getShisetuId()));
                    // 事業者ID
                    checkMainRirekiItem.setSvJigyoId(CommonDtoUtil.objValToString(chkMainRireki.getSvJigyoId()));
                    // 利用者ID
                    checkMainRirekiItem.setUserid(CommonDtoUtil.objValToString(chkMainRireki.getUserid()));
                    // 計画期間ID
                    checkMainRirekiItem.setSc1Id(CommonDtoUtil.objValToString(chkMainRireki.getSc1Id()));
                    // 履歴ID
                    checkMainRirekiItem.setAssId(CommonDtoUtil.objValToString(chkMainRireki.getAssId()));
                    // 項目ID
                    checkMainRirekiItem.setKomokuId(CommonDtoUtil.objValToString(chkMainRireki.getKomokuId()));
                    // 細目ID
                    checkMainRirekiItem.setSaimokuId(CommonDtoUtil.objValToString(chkMainRireki.getSaimokuId()));
                    // 内容ID
                    checkMainRirekiItem.setNaiyoId(CommonDtoUtil.objValToString(chkMainRireki.getNaiyoId()));
                    // 作成日
                    checkMainRirekiItem.setCreateYmd(chkMainRireki.getCreateYmd());
                    // 点数
                    checkMainRirekiItem.setPoint(CommonDtoUtil.objValToString(chkMainRireki.getPoint()));

                    checkMainRireki.add(checkMainRirekiItem);
                }

            });
        }
        return checkMainRireki;
    }

}
