package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.StringUtils;

import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00804DoctorOpinionInfo3;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00804DoctorOpinionInfo4;
import jp.ndsoft.carebase.cmn.api.logic.AssessmentHomeLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeSaveServiceInDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4MedH21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5MedR3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdlRirekiMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4MedH21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4MedH21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5MedR3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5MedR3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRireki;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRirekiCriteria;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;

/**
 * @since 2025.04.08
 * <AUTHOR>
 * @apiNote GUI00804_［アセスメント］画面（居宅）（6医） データ保存
 */
@Service
public class AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceImpl
        extends
        UpdateServiceImpl<AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceInDto, AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 居宅アセスメント履歴の更新詳細 DAO */
    @Autowired
    private CpnTucGdlRirekiMapper cpnTucGdlRirekiMapper;

    /** ＧＬ＿介護に関する医師の意見（Ｈ２１改訂） */
    @Autowired
    private CpnTucGdl4MedH21Mapper cpnTucGdl4MedH21Mapper;

    /** ＧＬ＿介護に関する医師の意見（R３改訂） */
    @Autowired
    private CpnTucGdl5MedR3Mapper cpnTucGdl5MedR3Mapper;

    /** ［アセスメント］画面（居宅）画面のロジッククラス */
    @Autowired
    private AssessmentHomeLogic assessmentHomeLogic;

    @Autowired
    private PlatformTransactionManager transactionManager;

    /**
     * ［アセスメント］画面（居宅）（6医） データ保存する
     * 
     * @param inDto アセスメント込データ保存入力DTO
     * @return アセスメントデータ保存出力DTO
     */
    @Override
    protected AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceOutDto mainProcess(
            AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceOutDto outDto = new AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceOutDto();
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        /*
         * ===============2. バリデーションチェックを行う。===============
         * 
         */
        boolean resFlg = assessmentHomeLogic.documentPerformValidationCheck(loginDto);
        // 2.2.4. 【変数】.処理結果が「false：失敗」の場合
        if (!resFlg) {
            // 2.2.4. 【変数】.処理結果が「false：失敗」の場合、下記返却する情報を設定して、後続処理を飛ばして、API処理を正常終了とする。
            // レスポンス.計画期間ID ： リクエストパラメータ.計画期間ID
            outDto.setSc1Id(loginDto.getSc1Id());
            // レスポンス.アセスメントID ： リクエストパラメータ.アセスメントID
            outDto.setGdlId(loginDto.getGdlId());
            // レスポンス.エラー区分: "1
            outDto.setErrKbn(CommonConstants.E_DOC_CHECK_ERR);
            return outDto;
        }
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transactionB = transactionManager.getTransaction(def);
        try {
            // ［アセスメント］画面（居宅）（1） データ保存
            outDto = mainProcessMealUpdate(inDto);
            transactionManager.commit(transactionB);
        } catch (Exception e) {
            transactionManager.rollback(transactionB);
            throw e;
        }

        // e-文書_ケアチェック表１のリクエストパラメータ
        TransactionStatus transactionC = transactionManager.getTransaction(def);
        try {
            mainProcessEdocOut(inDto, outDto);
            transactionManager.commit(transactionC);
        } catch (Exception e) {
            transactionManager.rollback(transactionC);
            throw e;
        }

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * API定義 (e文書出力)
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected void mainProcessEdocOut(
            AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceInDto inDto,
            AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceOutDto outDto)
            throws Exception {
        // 入力dtoをLoginDtoにコピーする
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);

        /*
         * ===============e文書出力===============
         * 
         */
        // e文書出力
        String errKbn = assessmentHomeLogic.getPrint(loginDto, outDto.getSc1Id());
        outDto.setErrKbn(errKbn);
    }

    /**
     * ［アセスメント］画面（居宅）（6医） データ保存する
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceOutDto mainProcessMealUpdate(
            AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceInDto inDto)
            throws Exception {

        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);
        loginDto.setNinteiFormF(inDto.getNinteiFlg());
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. 計画対象期間の保存処理===============
         * 
         */
        // 変数.計画対象期間ID
        Integer sc1IdTmp0 = 0;
        // 変数.アセスメントID
        Integer gdlIdTmp0 = CommonDtoUtil.strValToInt(inDto.getRaiId());
        // 2.1.リクエストパラメータ詳細.計画対象期間IDがnullの場合、【27-06 記録共通期間】情報を登録する。
        if (!StringUtils.hasLength(inDto.getSc1Id())) {
            this.assessmentHomeLogic.insertKghTucKrkKikan(loginDto);
            // 変数.計画対象期間ID = 採番した期間ID
            sc1IdTmp0 = CommonDtoUtil.strValToInt(loginDto.getSc1Id());
        }
        // 2.2.上記以外の場合、
        else {
            // 変数.計画対象期間ID = リクエストパラメータ詳細.計画対象期間ID
            sc1IdTmp0 = CommonDtoUtil.strValToInt(inDto.getSc1Id());
        }

        final Integer sc1IdTmp = sc1IdTmp0;
        /*
         * ===============3. リクエストパラメータ詳細.削除処理区分が2:画面を履歴ごと削除するの場合、===============
         * 
         */
        if (CommonConstants.DELETE_TEI_KBN_2.equals(inDto.getDeleteKbn())) {
            // 3.サブ情報を更新する
            this.assessmentHomeLogic.homeLogicsyuri(loginDto, sc1IdTmp);
        }
        /*
         * ===============4. リクエストパラメータ詳細.削除処理区分が1:画面のみ削除するの場合===============
         * 
         */
        else if (CommonConstants.DELETE_TEI_KBN_1.equals(inDto.getDeleteKbn())) {
            // 【ＧＬ＿介護に関する医師の意見（Ｈ２１改訂/R３改訂）】情報を削除更新する
            deleteCpnTucGdlMed(inDto, sc1IdTmp);

            // 4.3. 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
            updateCpnTucGdlRireki43(inDto, sc1IdTmp);
        }
        /*
         * ===============5. 以外の場合===============
         * 
         */
        else {
            // 5.1. 履歴情報の保存処理
            // 5.1.1. リクエストパラメータ.履歴更新区分が"C":新規の場合、【ＧＬ＿居宅アセスメント履歴】情報を登録する。
            if (CommonDtoUtil.isHistoryCreate(inDto)) {
                gdlIdTmp0 = insertCpnTucGdlRireki(inDto, sc1IdTmp);
            }
            // 5.1.2. リクエストパラメータ.履歴更新区分が"U":更新の場合、【ＧＬ＿居宅アセスメント履歴】情報を更新する。
            else if (CommonDtoUtil.isHistoryUpdate(inDto)) {
                updateCpnTucGdlRireki512(inDto, sc1IdTmp);
            }

            // 5.2. 6医の画面情報の保存処理
            final Integer gdlIdTmp = gdlIdTmp0;
            // 5.2.1. リクエストパラメータ.更新区分が"C":新規の場合、介護に関する医師の意見情報を登録する。
            if (CommonDtoUtil.isCreate(inDto)) {
                insertCpnTucGdlMed(inDto, sc1IdTmp, gdlIdTmp);
            }
            // 5.2.2. リクエストパラメータ.更新区分が"U":更新の場合、介護に関する医師の意見情報を更新する。
            else if (CommonDtoUtil.isUpdate(inDto)) {

                updateCpnTucGdlMed(inDto, sc1IdTmp);

            }

            // 5.3.リクエストパラメータ.【課題と目標リスト】の件数分、【ＧＬ＿課題と目標】情報を保存する。
            this.assessmentHomeLogic.homeLogicCpnTucGdlKadai(loginDto, sc1IdTmp);
        }

        // 戻り情報を設定
        AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceOutDto outDto = new AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceOutDto();

        // 「2.」で処理した変数.計画対象期間ID
        outDto.setSc1Id(CommonDtoUtil.objValToString(sc1IdTmp));

        // 登録の場合：「5.1.1.」で採番したアセスメントID
        // 更新の場合：リクエストパラメータ.アセスメントID
        outDto.setGdlId(CommonDtoUtil.objValToString(gdlIdTmp0));
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 【ＧＬ＿介護に関する医師の意見（Ｈ２１改訂/R３改訂）】情報を削除更新する
     * 
     * @param inDto    アセスメント込データ保存入力DTO
     * @param sc1IdTmp 変数.計画対象期間ID
     */
    private void deleteCpnTucGdlMed(AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceInDto inDto,
            Integer sc1IdTmp) {

        // 4.1. リクエストパラメータ.改定フラグが4（H21/4改訂版）の場合、【ＧＬ＿介護に関する医師の意見（Ｈ２１改訂）】情報を削除更新する。
        if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {

            final CpnTucGdl4MedH21Criteria cpnTucGdl4MedH21Criteria = new CpnTucGdl4MedH21Criteria();

            // ■更新条件
            // アセスメントID＝リクエストパラメータ.アセスメントID
            // 計画期間ID＝【変数】.計画対象期間ID
            // 削除フラグ = 0（未削除データ）
            // 更新回数＝リクエストパラメータ.医師の意見情報（H21/4改訂版）.更新回数
            cpnTucGdl4MedH21Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                    .andSc1IdEqualTo(sc1IdTmp);

            this.cpnTucGdl4MedH21Mapper.deleteByCriteria(cpnTucGdl4MedH21Criteria);
        }
        // 4.2. リクエストパラメータ.改定フラグが5「R3/４改訂版」の場合、【ＧＬ＿介護に関する医師の意見（R３改訂）】情報を削除更新する。
        else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {

            final CpnTucGdl5MedR3Criteria cpnTucGdl5MedR3Criteria = new CpnTucGdl5MedR3Criteria();

            // ■更新条件
            // アセスメントID＝リクエストパラメータ.アセスメントID
            // 計画期間ID＝【変数】.計画対象期間ID
            // 削除フラグ = 0（未削除データ）
            // 更新回数＝リクエストパラメータ.医師の意見情報（R3/4改訂版）.更新回数
            cpnTucGdl5MedR3Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                    .andSc1IdEqualTo(sc1IdTmp);

            this.cpnTucGdl5MedR3Mapper.deleteByCriteria(cpnTucGdl5MedR3Criteria);
        }
    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
     * 
     * @param inDto    アセスメント込データ保存入力DTO
     * @param sc1IdTmp 変数.計画対象期間ID
     */
    private void updateCpnTucGdlRireki43(AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceInDto inDto,
            Integer sc1IdTmp) {

        // ＧＬ＿居宅アセスメント履歴
        CpnTucGdlRireki cpnTucGdlRirekiRecord = new CpnTucGdlRireki();
        // 医師の意見
        cpnTucGdlRirekiRecord.setAss14("");

        final CpnTucGdlRirekiCriteria cpnTucGdlRirekiCriteria = new CpnTucGdlRirekiCriteria();
        // ■更新条件
        // アセスメントID＝リクエストパラメータ.アセスメントID
        // 計画期間ID＝【変数】.計画対象期間ID
        // 事業者ID＝リクエストパラメータ.事業者ID
        // 利用者ＩＤ＝リクエストパラメータ.利用者ＩＤ
        // 法人ID＝リクエストパラメータ.法人ID
        // 施設ID＝リクエストパラメータ.施設ID
        // 削除フラグ = 0（未削除データ）
        // 更新回数＝リクエストパラメータ.履歴更新回数
        cpnTucGdlRirekiCriteria.createCriteria()
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                .andSc1IdEqualTo(sc1IdTmp)
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRirekiRecord,
                cpnTucGdlRirekiCriteria);

    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を登録する
     * 
     * @param inDto    アセスメント込データ保存入力DTO
     * @param sc1IdTmp 変数.計画対象期間ID
     * @return 【ＧＬ＿居宅アセスメント履歴】情報を登録 処理結果
     */
    private Integer insertCpnTucGdlRireki(AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceInDto inDto,
            Integer sc1IdTmp) throws Exception {

        CpnTucGdlRireki cpnTucGdlRirekiRecord = new CpnTucGdlRireki();
        // 計画期間ID
        cpnTucGdlRirekiRecord.setSc1Id(sc1IdTmp);
        // 法人ID
        cpnTucGdlRirekiRecord.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        cpnTucGdlRirekiRecord.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        cpnTucGdlRirekiRecord.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ＩＤ
        cpnTucGdlRirekiRecord.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // アセスメント実施日
        cpnTucGdlRirekiRecord.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRirekiRecord.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // Ｊの状態
        cpnTucGdlRirekiRecord.setAss12("―");
        // 本人の基本動作等8
        cpnTucGdlRirekiRecord.setAss13("―");
        // 医師の意見
        cpnTucGdlRirekiRecord.setAss14("●");
        // 改定フラグ
        cpnTucGdlRirekiRecord.setNinteiFormF(CommonDtoUtil.strValToInt(inDto.getNinteiFlg()));

        // DAOを実行
        this.cpnTucGdlRirekiMapper.insertSelectiveAndReturn(cpnTucGdlRirekiRecord);
        return cpnTucGdlRirekiRecord.getGdlId().intValue();

    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する
     * 
     * @param inDto    アセスメント込データ保存入力DTO
     * @param sc1IdTmp 変数.計画対象期間ID
     */
    private void updateCpnTucGdlRireki512(AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceInDto inDto,
            Integer sc1IdTmp) {

        // ＧＬ＿居宅アセスメント履歴
        CpnTucGdlRireki cpnTucGdlRirekiRecord = new CpnTucGdlRireki();

        // アセスメント実施日
        cpnTucGdlRirekiRecord.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRirekiRecord.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // Ｊの状態
        cpnTucGdlRirekiRecord.setAss12("―");
        // 本人の基本動作等8
        cpnTucGdlRirekiRecord.setAss13("―");
        // 医師の意見
        cpnTucGdlRirekiRecord.setAss14("●");

        final CpnTucGdlRirekiCriteria cpnTucGdlRirekiCriteria = new CpnTucGdlRirekiCriteria();
        // ■更新条件
        // アセスメントID=リクエストパラメータ.アセスメントID
        // 計画期間ID＝変数.計画対象期間ID
        // 事業者ID＝リクエストパラメータ.事業者ID
        // 利用者ＩＤ＝リクエストパラメータ.利用者ＩＤ
        // 法人ID＝リクエストパラメータ.法人ID
        // 施設ID＝リクエストパラメータ.施設ID
        // 削除フラグ = 0（未削除データ）
        // 更新回数＝リクエストパラメータ.履歴更新回数
        cpnTucGdlRirekiCriteria.createCriteria()
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                .andSc1IdEqualTo(sc1IdTmp)
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRirekiRecord,
                cpnTucGdlRirekiCriteria);

    }

    /**
     * 【ＧＬ＿介護に関する医師の意見（Ｈ２１改訂/R３改訂）】情報を登録する
     * 
     * @param inDto    アセスメント込データ保存入力DTO
     * @param sc1IdTmp 変数.計画対象期間ID
     * @param gdlIdTmp 変数.アセスメントID
     * @throws Exception
     */
    private void insertCpnTucGdlMed(AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceInDto inDto,
            Integer sc1IdTmp, Integer gdlIdTmp) throws Exception {

        // 5.2.1.1. リクエストパラメータ.改定フラグが4（H21/4改訂版）の場合、【ＧＬ＿介護に関する医師の意見（Ｈ２１改訂）】情報を登録する。
        if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {

            /** 医師の意見情報（H21/4改訂版） */
            Gui00804DoctorOpinionInfo3 doctorOpinionInfo3 = inDto.getDoctorOpinionInfo3();

            CpnTucGdl4MedH21 cpnTucGdl4MedH21Record = new CpnTucGdl4MedH21();

            BeanUtils.copyProperties(cpnTucGdl4MedH21Record, doctorOpinionInfo3);

            // アセスメントID
            cpnTucGdl4MedH21Record.setGdlId(gdlIdTmp);
            // 計画期間ID
            cpnTucGdl4MedH21Record.setSc1Id(sc1IdTmp);

            // 運動
            cpnTucGdl4MedH21Record.setUndoVal(CommonDtoUtil.strValToInt(doctorOpinionInfo3.getUndo()));

            this.cpnTucGdl4MedH21Mapper.insertSelective(cpnTucGdl4MedH21Record);

        }
        // 5.2.1.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【ＧＬ＿介護に関する医師の意見（R３改訂）】情報を登録する。
        else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {

            /** 医師の意見情報（R3/4改訂版） */
            Gui00804DoctorOpinionInfo4 doctorOpinionInfo4 = inDto.getDoctorOpinionInfo4();

            CpnTucGdl5MedR3 cpnTucGdl5MedR3Record = new CpnTucGdl5MedR3();

            BeanUtils.copyProperties(cpnTucGdl5MedR3Record, doctorOpinionInfo4);

            // アセスメントID
            cpnTucGdl5MedR3Record.setGdlId(gdlIdTmp);
            // 計画期間ID
            cpnTucGdl5MedR3Record.setSc1Id(sc1IdTmp);
            // 運動
            cpnTucGdl5MedR3Record.setUndoVal(CommonDtoUtil.strValToInt(doctorOpinionInfo4.getUndo()));

            this.cpnTucGdl5MedR3Mapper.insertSelective(cpnTucGdl5MedR3Record);
        }
    }

    /**
     * 【ＧＬ＿介護に関する医師の意見（Ｈ２１改訂/R３改訂）】情報を更新する
     * 
     * @param inDto    アセスメント込データ保存入力DTO
     * @param sc1IdTmp 変数.計画対象期間ID
     */
    private void updateCpnTucGdlMed(AssessmentHomeAttendingPhysicianOpinionTabUpdateServiceInDto inDto,
            Integer sc1IdTmp) throws Exception {

        // 5.2.2.1. リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、【ＧＬ＿介護に関する医師の意見（Ｈ２１改訂）】情報を更新する。
        if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {

            /** 医師の意見情報（H21/4改訂版） */
            Gui00804DoctorOpinionInfo3 doctorOpinionInfo3 = inDto.getDoctorOpinionInfo3();

            CpnTucGdl4MedH21 cpnTucGdl4MedH21Record = new CpnTucGdl4MedH21();

            BeanUtils.copyProperties(cpnTucGdl4MedH21Record, doctorOpinionInfo3);

            // 運動
            cpnTucGdl4MedH21Record.setUndoVal(CommonDtoUtil.strValToInt(doctorOpinionInfo3.getUndo()));

            final CpnTucGdl4MedH21Criteria cpnTucGdl4MedH21Criteria = new CpnTucGdl4MedH21Criteria();
            // ■更新条件
            // アセスメントID＝リクエストパラメータ.アセスメントID
            // 計画期間ID＝変数.計画対象期間ID
            // 削除フラグ = 0（未削除データ）
            // 更新回数＝リクエストパラメータ.医師の意見情報（H21/4改訂版）.更新回数
            cpnTucGdl4MedH21Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                    .andSc1IdEqualTo(sc1IdTmp);

            this.cpnTucGdl4MedH21Mapper.updateByCriteriaSelective(cpnTucGdl4MedH21Record,
                    cpnTucGdl4MedH21Criteria);

        }
        // 5.2.2.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【ＧＬ＿介護に関する医師の意見（R３改訂）】情報を更新する。
        else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {

            /** 医師の意見情報（R3/4改訂版） */
            Gui00804DoctorOpinionInfo4 doctorOpinionInfo4 = inDto.getDoctorOpinionInfo4();

            CpnTucGdl5MedR3 cpnTucGdl5MedR3Record = new CpnTucGdl5MedR3();

            BeanUtils.copyProperties(cpnTucGdl5MedR3Record, doctorOpinionInfo4);

            // リクエストパラメータ.医師の意見情報（R3/4改訂版）.血圧が未入力の場合、2
            if (CommonConstants.BLANK_STRING.equals(CommonDtoUtil.strNullToEmpty(doctorOpinionInfo4.getKetsuatsu()))) {
                cpnTucGdl5MedR3Record.setKetsuatsu(CommonConstants.KETSUATSU_INTEGER_2);
            }

            // リクエストパラメータ.医師の意見情報（R3/4改訂版）.嚥下が未入力の場合、2
            if (CommonConstants.BLANK_STRING.equals(CommonDtoUtil.strNullToEmpty(doctorOpinionInfo4.getEnge()))) {
                cpnTucGdl5MedR3Record.setEnge(CommonConstants.ENGE_INTEGER_2);
            }

            // リクエストパラメータ.医師の意見情報（R3/4改訂版）.摂食が未入力の場合、2
            if (CommonConstants.BLANK_STRING.equals(CommonDtoUtil.strNullToEmpty(doctorOpinionInfo4.getSesshoku()))) {
                cpnTucGdl5MedR3Record.setSesshoku(CommonConstants.SESSHOKU_INTEGER_2);
            }

            // リクエストパラメータ.医師の意見情報（R3/4改訂版）.移動が未入力の場合、2
            if (CommonConstants.BLANK_STRING.equals(CommonDtoUtil.strNullToEmpty(doctorOpinionInfo4.getIdo()))) {
                cpnTucGdl5MedR3Record.setIdo(CommonConstants.IDO_INTEGER_2);
            }

            // リクエストパラメータ.医師の意見情報（R3/4改訂版）.運動が未入力の場合、2
            if (CommonConstants.BLANK_STRING.equals(CommonDtoUtil.strNullToEmpty(doctorOpinionInfo4.getUndo()))) {
                cpnTucGdl5MedR3Record.setUndoVal(CommonConstants.UNDO_INTEGER_2);
            }
            // 上記以外、リクエストパラメータ.医師の意見情報（R3/4改訂版）.運動
            else {
                cpnTucGdl5MedR3Record.setUndoVal(CommonDtoUtil.strValToInt(doctorOpinionInfo4.getUndo()));
            }

            // リクエストパラメータ.医師の意見情報（R3/4改訂版）.留意が未入力の場合、2
            if (CommonConstants.BLANK_STRING.equals(CommonDtoUtil.strNullToEmpty(doctorOpinionInfo4.getRyui()))) {
                cpnTucGdl5MedR3Record.setRyui(CommonConstants.RYUI_INTEGER_2);
            }

            // リクエストパラメータ.医師の意見情報（R3/4改訂版）.特記すべき項目が未入力の場合、2
            if (CommonConstants.BLANK_STRING.equals(CommonDtoUtil.strNullToEmpty(doctorOpinionInfo4.getTokkiNashi()))) {
                cpnTucGdl5MedR3Record.setTokkiNashi(CommonConstants.TOKKI_NASHI_INTEGER_2);
            }

            final CpnTucGdl5MedR3Criteria cpnTucGdl5MedR3Criteria = new CpnTucGdl5MedR3Criteria();
            // ■更新条件
            // アセスメントID＝リクエストパラメータ.アセスメントID
            // 計画期間ID＝変数.計画対象期間ID
            // 削除フラグ = 0（未削除データ）
            // 更新回数＝リクエストパラメータ.医師の意見情報（R3/4改訂版）.更新回数
            cpnTucGdl5MedR3Criteria.createCriteria()
                    .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getRaiId()))
                    .andSc1IdEqualTo(sc1IdTmp);

            this.cpnTucGdl5MedR3Mapper.updateByCriteriaSelective(cpnTucGdl5MedR3Record, cpnTucGdl5MedR3Criteria);
        }
    }
}
