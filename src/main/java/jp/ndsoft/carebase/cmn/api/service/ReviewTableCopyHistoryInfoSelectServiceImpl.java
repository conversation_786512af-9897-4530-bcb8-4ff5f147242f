package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jp.ndsoft.carebase.cmn.api.service.dto.Gui00654RirekiInfoOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.ReviewTableCopyHistoryInfoSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.ReviewTableCopyHistoryInfoSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.ProvidersforCarePlanFormulationServiceMasterByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ProvidersforCarePlanFormulationServiceMasterOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinInfoListSpaceSortByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinInfoListSpaceSortOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ProvidersforCarePlanFormulationServiceMasterSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025/06/23
 * <AUTHOR> TRAN LANG KHOA
 * @implNote GUI00654_課題検討複写 画面
 */
@Service
public class ReviewTableCopyHistoryInfoSelectServiceImpl
        extends
        SelectServiceImpl<ReviewTableCopyHistoryInfoSelectServiceInDto, ReviewTableCopyHistoryInfoSelectServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private ProvidersforCarePlanFormulationServiceMasterSelectMapper providersforCarePlanFormulationServiceMasterSelectMapper;

    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /**
     * ［要因取込］画面
     * 
     * @param inDto ［要因取込］画面の入力DTO.
     * @return ［要因取込］画面の出力DTO
     * @throws Exception Exception
     */
    @Override
    protected ReviewTableCopyHistoryInfoSelectServiceOutDto mainProcess(
            ReviewTableCopyHistoryInfoSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        // DAOパラメータを作成
        ReviewTableCopyHistoryInfoSelectServiceOutDto outDto = new ReviewTableCopyHistoryInfoSelectServiceOutDto();
        List<Gui00654RirekiInfoOutDto> rirekiInfoList = new ArrayList<>();
        /*
         * ===============2. 履歴情報取得===============
         * 
         */
        // 2.1. 下記のモニタリング記録表ヘッダ情報取得のDAOを利用し、履歴情報リストを取得する。

        ProvidersforCarePlanFormulationServiceMasterByCriteriaInEntity providersforCarePlanCriteriaInEntity = new ProvidersforCarePlanFormulationServiceMasterByCriteriaInEntity();
        providersforCarePlanCriteriaInEntity.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        providersforCarePlanCriteriaInEntity.setUId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        providersforCarePlanCriteriaInEntity.setJIdList(
                inDto.getSvJigyoIdList().stream()
                        .map(CommonDtoUtil::strValToInt)
                        .toList());
        // providersforCarePlanCriteriaInEntity.setKaiteiKbn(CommonDtoUtil.strValToInt(inDto.getKaiteiKbn()));
        providersforCarePlanCriteriaInEntity.setMstKbn(CommonDtoUtil.strValToInt(inDto.getMstKbn()));

        List<ProvidersforCarePlanFormulationServiceMasterOutEntity> providersforCarePlanList = providersforCarePlanFormulationServiceMasterSelectMapper
                .findProvidersforCarePlanFormulationServiceMasterByCriteria(
                        providersforCarePlanCriteriaInEntity);

        if (!CollectionUtils.isEmpty(providersforCarePlanList)) {
            for (ProvidersforCarePlanFormulationServiceMasterOutEntity providers : providersforCarePlanList) {
                Gui00654RirekiInfoOutDto rirekiInfo = new Gui00654RirekiInfoOutDto();
                // 課題検討ID
                rirekiInfo.setKky1Id(CommonDtoUtil.objValToString(providers.getKky1Id()));
                // 計画期間ID
                rirekiInfo.setSc1Id(CommonDtoUtil.objValToString(providers.getSc1Id()));
                // 法人ID
                rirekiInfo.setHoujinId(CommonDtoUtil.objValToString(providers.getHoujinId()));
                // 施設ID
                rirekiInfo.setShisetuId(CommonDtoUtil.objValToString(providers.getShisetuId()));
                // 事業者ID
                rirekiInfo.setSvJigyoId(CommonDtoUtil.objValToString(providers.getSvJigyoId()));
                // 利用者ID
                rirekiInfo.setUserid(CommonDtoUtil.objValToString(providers.getUserid()));
                // マスタ区分
                rirekiInfo.setMstKbn(CommonDtoUtil.objValToString(providers.getMstKbn()));
                // アセスメント基準日
                rirekiInfo.setCreateYmd(CommonDtoUtil.objValToString(providers.getCreateYmd()));
                // 担当者ID(職員ID)
                rirekiInfo.setShokuId(CommonDtoUtil.objValToString(providers.getShokuId()));
                // 枠幅1
                rirekiInfo.setWidth1(CommonDtoUtil.objValToString((providers.getWidth1())));
                // 枠幅2
                rirekiInfo.setWidth2(CommonDtoUtil.objValToString((providers.getWidth2())));
                // 枠幅3
                rirekiInfo.setWidth3(CommonDtoUtil.objValToString((providers.getWidth3())));
                // 枠幅4
                rirekiInfo.setWidth4(CommonDtoUtil.objValToString((providers.getWidth4())));
                // 枠幅5
                rirekiInfo.setWidth5(CommonDtoUtil.objValToString((providers.getWidth5())));
                // 枠幅6
                rirekiInfo.setWidth6(CommonDtoUtil.objValToString((providers.getWidth6())));
                // 事業名
                rirekiInfo.setJigyoKnj(CommonDtoUtil.objValToString((providers.getJigyoKnj())));
                // 事業名（略称）
                rirekiInfo.setJigyoRyakuKnj(CommonDtoUtil.objValToString((providers.getJigyoRyakuKnj())));
                // 改訂区分
                rirekiInfo.setKaiteiKbn(CommonDtoUtil.objValToString(providers.getKaiteiKbn()));

                rirekiInfoList.add(rirekiInfo);
            }
        }
        /*
         * ===============3. 職員基本情報を取得する。===============
         * 
         */
        ShokuinInfoListSpaceSortByCriteriaInEntity shokuinInfoListSpaceSortByCriteriaInEntity = new ShokuinInfoListSpaceSortByCriteriaInEntity();

        List<ShokuinInfoListSpaceSortOutEntity> kghCpnRaiMonKentList = comMscShokuinSelectMapper
                .findShokuinInfoListSpaceSortByCriteria(shokuinInfoListSpaceSortByCriteriaInEntity);
        /*
         * ===4.処理No.2で取得した履歴情報毎に、検討者を元に、処理No.3で取得した職員基本情報の職員IDと紐づけて、職員の氏名を編集する。===
         * 
         */
        for (Gui00654RirekiInfoOutDto rirekiInfo : rirekiInfoList) {
            Integer shokuId = CommonDtoUtil.strValToInt(rirekiInfo.getShokuId());

            for (ShokuinInfoListSpaceSortOutEntity shokuin : kghCpnRaiMonKentList) {
                if (shokuin.getChkShokuId() != null && shokuin.getChkShokuId().equals(shokuId)) {
                    String fullName = shokuin.getShokuin1Knj() + " "
                            + shokuin.getShokuin2Knj();
                    rirekiInfo.setPlnShokuKnj(fullName);
                    break;
                }
            }
        }
        /*
         * ===============5. レスポンスを返却する。===============
         * 
         */
        LOG.info(Constants.END);
        outDto.setRirekiInfo(rirekiInfoList);
        return outDto;
    }
}
