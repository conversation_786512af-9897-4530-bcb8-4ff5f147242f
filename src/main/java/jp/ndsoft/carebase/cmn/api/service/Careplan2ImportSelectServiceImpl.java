package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01041Hindo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01041HokenSv;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01041Kikan;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01041Kkak1;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01041Kkak2;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01041Rireki;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01041Tanto;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01041Tukihi;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01041Youbi;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnWeekLogic;
import jp.ndsoft.carebase.cmn.api.logic.dto.TermId2DateLogicOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Careplan2ImportSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Careplan2ImportSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.Cks11InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Cks11InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemuseNameByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemuseNameOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCks221InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCks221InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCks222InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCks222InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCks224InfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCks224InfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ItemuseSuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ItemuseSuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoServiceDataMdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoServiceDataMdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku1DetailInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku1DetailInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku1InfoList1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku1InfoList1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2DataBaseByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2DataBaseOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2DataDetailByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2DataDetailOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2DataDetailTantoHindoCntAriByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2DataDetailTantoHindoCntAriOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2HeadInfoListByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2HeadInfoListOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2HindoInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2HindoInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2ServiceInfoDmyByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2ServiceInfoDmyOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2TukihiInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku2TukihiInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KeikakushoDataSelByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KeikakushoDataSelOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKkak2SvItemSougouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKkak2SvItemSougouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkComKikanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkComKikanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkComYukoKikanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkComYukoKikanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RyakusyoKanjiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RyakusyoKanjiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinInfoListSpaceSortByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinInfoListSpaceSortOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvJigyoCdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvJigyoCdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvJigyoNameByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvJigyoNameOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvjigyoNameDetailInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvjigyoNameDetailInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSougouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcTermSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoNameSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks11SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks211SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks212SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks221SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks222SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks223SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks224SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks22AndCks21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks22SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KycTucPlan16SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.SelectPeriodSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * 計画書（２）取込画面初期処理サービス. GUI01041_計画書（2）取込
 *
 * <AUTHOR>
 */
@Service
public class Careplan2ImportSelectServiceImpl
        extends SelectServiceImpl<Careplan2ImportSelectServiceInDto, Careplan2ImportSelectServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 遷移元が"計画ﾓﾆﾀﾆﾘﾝｸﾞ"、"評価表"、"実施ﾓﾆﾀﾆﾘﾝｸﾞ" */
    private static final List<String> SENSI_MOTO_LIST = Arrays.asList(CommonConstants.STR_PLAN_MONITORING,
            CommonConstants.STR_EVALUATION_TABLE, CommonConstants.STR_IMPLEMENTATION_MONITORING);
    /** 変数.計画期間ID */
    private Integer kikanId;
    /** 変数.履歴ID */
    private Integer rirekiId;
    /** 記録共通期間情報取得Mapper */
    @Autowired
    private SelectPeriodSelectMapper krkKikanMapper;

    /** 計画書（２）ヘッダ情報取得Mapper */
    @Autowired
    private CpnTucCks21SelectMapper cks21Mapper;

    /** 計画書（１）ヘッダ情報取得Mapper */
    @Autowired
    private CpnTucCks11SelectMapper cks11Mapper;
    /** 職員氏名情報取得Mapper */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;
    /** 介護サービス費有効期間マスタ取得Mapper */
    @Autowired
    private ComMhcTermSelectMapper comMhcTermSelectMapper;
    /** 計画書（２）サービス情報取得Mapper */
    @Autowired
    private CpnTucCks211SelectMapper cpnTucCks211SelectMapper;
    /** サービス事業者マスタ情報取得Mapper */
    @Autowired
    private ComMscSvjigyoSelectMapper comMscSvjigyoSelectMapper;
    /** サービス事業者コードマスタ取得Mapper */
    @Autowired
    private ComMscSvjigyoNameSelectMapper comMscSvjigyoNameSelectMapper;
    /** 20-20 介護予防・日常生活支援総合事業サービス費適用マスタ情報取得Mapper */
    @Autowired
    private ComMhcItemuseSougouSelectMapper comMhcItemuseSougouSelectMapper;
    /** 介護サービス費適用正式名称取得Mapper */
    @Autowired
    private ComMhcItemuseSelectMapper comMhcItemuseSelectMapper;
    /** 介護予防サービス・支援計画書データ（月日指定）情報取得Mapper */
    @Autowired
    private KycTucPlan16SelectMapper kycTucPlan16SelectMapper;
    /** 計画書（２）（月日指定）情報取得Mapper */
    @Autowired
    private CpnTucCks212SelectMapper cpnTucCks212SelectMapper;
    /** 計画書（２）データ情報取得Mapper */
    @Autowired
    private CpnTucCks22SelectMapper cpnTucCks22SelectMapper;
    /** 計画書（２）データ詳細情報取得Mapper */
    @Autowired
    private CpnTucCks22AndCks21SelectMapper cpnTucCks22AndCks21SelectMapper;
    /** 計画書（２）サービス曜日情報取得Mapper */
    @Autowired
    private CpnTucCks221SelectMapper cpnTucCks221SelectMapper;
    /** 計画書（２）担当者情報取得Mapper */
    @Autowired
    private CpnTucCks222SelectMapper cpnTucCks222SelectMapper;
    /** 計画書（２）頻度（日課）情報取得Mapper */
    @Autowired
    private CpnTucCks223SelectMapper cpnTucCks223SelectMapper;

    /** 共通関数KghKrkZCpnFuncLogic */
    @Autowired
    private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;
    /** 共通関数KghKrkZCpnWeekLogic */
    @Autowired
    private KghKrkZCpnWeekLogic kghKrkZCpnWeekLogic;
    /** 記録との連携情報取得 */
    @Autowired
    private CpnTucCks224SelectMapper cpnTucCks224SelectMapper;

    /**
     * 計画書（２）取込画面初期処理情報取得
     * 
     * @param inDto 計画書（２）取込画面の入力DTO.
     * @return 計画書（２）取込画面情報OUT DTO
     * @throws Exception Exception
     */
    @Override
    protected Careplan2ImportSelectServiceOutDto mainProcess(Careplan2ImportSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        // 計画書（２）取込画面情報OUT DTO
        Careplan2ImportSelectServiceOutDto outDto = new Careplan2ImportSelectServiceOutDto();
        // 2. 期間リスト取得処理
        if (CommonConstants.PERIOD_NO_MANAGE_FLG.equals(inDto.getKikanFlg())) {
            processUnmanagedKikan(outDto);
        } else if (CommonConstants.PERIOD_MANAGE_FLG.equals(inDto.getKikanFlg())) {
            processManagedKikan(inDto, outDto);
        }

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 2.1 期間管理フラグが「管理しない」場合の処理
     * 
     * @param outDto 計画書（２）取込画面情報OUT DTO
     */
    private void processUnmanagedKikan(Careplan2ImportSelectServiceOutDto outDto) {
        outDto.setKikanList(null);
        kikanId = CommonDtoUtil.strValToInt(CommonConstants.SCILD_ZERO);

    }

    /**
     * 2.2 期間管理フラグが「管理する」場合の処理
     * 
     * @param inDto  計画書（２）取込画面の入力DTO.
     * @param outDto 計画書（２）取込画面情報OUT DTO
     */
    private void processManagedKikan(Careplan2ImportSelectServiceInDto inDto,
            Careplan2ImportSelectServiceOutDto outDto) {
        // 2.2.1 記録共通期間情報取得
        List<KrkComKikanOutEntity> kikanList = getKrkKikanInfo(inDto);
        kikanList.forEach(kikan -> {
            Gui01041Kikan kikanInfo = mapToKikanInfo(kikan);
            if (CommonConstants.STR_CARE_PLAN2.equals(inDto.getTorikomiMoto())) {
                processCks21History(kikan, kikanInfo);
            } else if (CommonConstants.STR_CARE_PLAN1.equals(inDto.getTorikomiMoto())) {
                processCks11History(kikan, kikanInfo);
            }
            outDto.getKikanList().add(kikanInfo);
            // 3. 履歴リスト取得
            // ※変数.計画期間IDを取得できる場合
            if (!Objects.isNull(kikanId)) {
                if (CommonConstants.PERIOD_NO_MANAGE_FLG.equals(inDto.getKikanFlg())
                        || (CommonConstants.PERIOD_MANAGE_FLG.equals(inDto.getKikanFlg()))
                                && CollectionUtils.isNotEmpty(outDto.getKikanList())) {
                    processRireki(inDto, outDto);
                }
            }
            // 4. 詳細情報取得
            // ※変数.履歴IDを取得できる場合
            if (!Objects.isNull(rirekiId)) {
                processDetail(inDto, outDto);
            }
        });
    }

    /**
     * 記録共通期間情報取得処理
     * 
     * @param inDto 計画書（２）取込画面の入力DTO.
     * @return 記録共通期間情報取得
     */
    private List<KrkComKikanOutEntity> getKrkKikanInfo(Careplan2ImportSelectServiceInDto inDto) {
        KrkComKikanByCriteriaInEntity criteria = new KrkComKikanByCriteriaInEntity();
        criteria.setSvJigyoId(stringToInteger(inDto.getSvJigyoId()));
        criteria.setUserId(stringToInteger(inDto.getUserId()));
        criteria.setSyubetsuId(stringToInteger(inDto.getSyubetsuId()));
        criteria.setShisetuId(stringToInteger(inDto.getShisetuId()));
        return krkKikanMapper.findKrkComKikanByCriteria(criteria);
    }

    /**
     * 期間情報マッピング
     * 
     * @param entity 記録共通期間情報
     * @return 期間情報
     */
    private Gui01041Kikan mapToKikanInfo(KrkComKikanOutEntity entity) {
        Gui01041Kikan info = new Gui01041Kikan();
        info.setSc1Id(toStringSafe(entity.getSc1Id()));
        info.setStartYmd(entity.getStartYmd());
        info.setEndYmd(entity.getEndYmd());
        info.setJigyoKnj(entity.getJigyoKnj());
        return info;
    }

    /**
     * 2.2.2 計画書（２）履歴件数処理
     * 
     * @param kikan     記録共通期間情報
     * @param kikanInfo 期間情報
     */
    private void processCks21History(KrkComKikanOutEntity kikan, Gui01041Kikan kikanInfo) {

        Keikaku2HeadInfoListByCriteriaInEntity criteria = new Keikaku2HeadInfoListByCriteriaInEntity();
        criteria.setSvJigyoId(kikan.getSvJigyoId());
        criteria.setUserid(kikan.getUserId());
        criteria.setSc1(kikan.getSc1Id());
        List<Keikaku2HeadInfoListOutEntity> headerList = cks21Mapper.findKeikaku2HeadInfoListByCriteria(criteria);

        // 2.2.2.② 履歴件数設定
        kikanInfo.setRirekiCnt(String.valueOf(headerList.size()));

        if (CollectionUtils.isNotEmpty(headerList)) {
            // 上記処理2.2.2.①が取得できる場合、変数.計画期間IDに一計目.計画期間IDを設定する。
            kikanId = headerList.getFirst().getSc1Id();
        }
    }

    /**
     * 2.2.3 計画書（１）履歴件数処理
     * 
     * @param kikan     記録共通期間情報
     * @param kikanInfo 期間情報
     */
    private void processCks11History(KrkComKikanOutEntity kikan, Gui01041Kikan kikanInfo) {
        Keikaku1InfoList1ByCriteriaInEntity criteria = new Keikaku1InfoList1ByCriteriaInEntity();
        criteria.setJId(kikan.getSvJigyoId());
        criteria.setUId(kikan.getUserId());
        criteria.setAlSc1(kikan.getSc1Id());
        List<Keikaku1InfoList1OutEntity> headerList = cks11Mapper.findKeikaku1InfoList1ByCriteria(criteria);
        // 2.2.3.② 履歴件数設定
        kikanInfo.setRirekiCnt(String.valueOf(headerList.size()));

        if (!headerList.isEmpty()) {
            // 2.2.3.③ 計画期間ID設定
            kikanId = headerList.getFirst().getSc1Id();
        }
    }

    /**
     * 3 履歴リスト取得
     * 
     * @param inDto  計画書（２）取込画面の入力DTO.
     * @param outDto 計画書（２）取込画面情報OUT DTO
     */
    private void processRireki(Careplan2ImportSelectServiceInDto inDto, Careplan2ImportSelectServiceOutDto outDto) {
        // 3.1 職員氏名情報取得
        List<ShokuinInfoListSpaceSortOutEntity> shokuinList = comMscShokuinSelectMapper
                .findShokuinInfoListSpaceSortByCriteria(new ShokuinInfoListSpaceSortByCriteriaInEntity());
        // 3.2 介護サービス費有効期間情報取得
        List<KrkComYukoKikanOutEntity> termList = comMhcTermSelectMapper
                .findKrkComYukoKikanByCriteria(new KrkComYukoKikanByCriteriaInEntity());
        // 3.3 ヘッダ情報取得
        if (CommonConstants.STR_CARE_PLAN2.equals(inDto.getTorikomiMoto())) {
            processCks21RiRekiHeader(inDto, outDto, shokuinList, termList);
        } else if (CommonConstants.STR_CARE_PLAN1.equals(inDto.getTorikomiMoto())) {
            processCks11RiRekiHeader(inDto, outDto, shokuinList);
        }
    }

    /**
     * 3.3.1 計画書（２）ヘッダ情報処理
     * 
     * @param inDto       計画書（２）取込画面の入力DTO.
     * @param outDto      計画書（２）取込画面情報OUT DTO
     * @param shokuinList 職員氏名情報
     * @param termList    介護サービス費有効期間情報
     */
    private void processCks21RiRekiHeader(Careplan2ImportSelectServiceInDto inDto,
            Careplan2ImportSelectServiceOutDto outDto, List<ShokuinInfoListSpaceSortOutEntity> shokuinList,
            List<KrkComYukoKikanOutEntity> termList) {
        Keikaku2HeadInfoListByCriteriaInEntity keikaku2HeadInfoListByCriteriaInEntity = new Keikaku2HeadInfoListByCriteriaInEntity();
        // 計画期間ID: 変数.計画期間ID
        keikaku2HeadInfoListByCriteriaInEntity.setSc1(kikanId);
        // 事業所ID: リクエストパラメータ.事業所ID
        keikaku2HeadInfoListByCriteriaInEntity.setSvJigyoId(stringToInteger(inDto.getSvJigyoId()));
        // 利用者ID: リクエストパラメータ.利用者ID
        keikaku2HeadInfoListByCriteriaInEntity.setUserid(stringToInteger(inDto.getUserId()));
        // 計画書（２）ヘッダ情報取得する
        List<Keikaku2HeadInfoListOutEntity> headerList = cks21Mapper
                .findKeikaku2HeadInfoListByCriteria(keikaku2HeadInfoListByCriteriaInEntity);

        headerList.forEach(header -> {
            Gui01041Rireki rirekiInfo = mapToRirekiInfoCks21(header);
            // 3.4.1.① 作成者氏名マッピング
            mapShokuinName(rirekiInfo, header.getShokuId(), shokuinList);
            // 3.4.1.② 有効期間マッピング
            mapTermInfo(rirekiInfo, header.getTermid(), termList);
            outDto.getRirekiList().add(rirekiInfo);
        });
        // 3.4.1.③ 変数.履歴IDに一計目.計画書（２）IDを設定する。
        if (CollectionUtils.isNotEmpty(headerList)) {
            rirekiId = headerList.getFirst().getKs21Id();
        }
    }

    /**
     * 3.3.2 計画書（１）ヘッダ情報処理
     * 
     * @param inDto       計画書（２）取込画面の入力DTO.
     * @param outDto      計画書（２）取込画面情報OUT DTO
     * @param shokuinList 職員氏名情報
     */
    private void processCks11RiRekiHeader(Careplan2ImportSelectServiceInDto inDto,
            Careplan2ImportSelectServiceOutDto outDto, List<ShokuinInfoListSpaceSortOutEntity> shokuinList) {
        // 計画書（１）ヘッダ情報取得用の検索条件
        Cks11InfoByCriteriaInEntity criteria = new Cks11InfoByCriteriaInEntity();
        // 計画期間IDの設定（変数から取得）
        criteria.setAlSc1(kikanId);
        // 事業者IDの設定（リクエストパラメータから取得）
        criteria.setJId(stringToInteger(inDto.getSvJigyoId()));
        // 利用者IDの設定（リクエストパラメータから取得）
        criteria.setUId(stringToInteger(inDto.getUserId()));
        // 計画書（１）情報取得する
        List<Cks11InfoOutEntity> headerList = cks11Mapper.findCks11InfoByCriteria(criteria);
        headerList.forEach(header -> {
            Gui01041Rireki rirekiInfo = mapToRirekiInfoCks11(header);
            // 3.4.2.① 作成者氏名マッピング
            mapShokuinName(rirekiInfo, header.getShokuId(), shokuinList);
            outDto.getRirekiList().add(rirekiInfo);
        });
        // 3.4.2.②変数.履歴IDに一計目.計画書IDを設定する。
        if (CollectionUtils.isNotEmpty(headerList)) {
            rirekiId = headerList.getFirst().getKs11Id();
        }
    }

    /**
     * ヘッダ情報から履歴情報へのマッピング（計画書（２）用）
     * 
     * @param header 計画書（２）ヘッダ情報
     */
    private Gui01041Rireki mapToRirekiInfoCks21(Keikaku2HeadInfoListOutEntity header) {
        Gui01041Rireki info = new Gui01041Rireki();
        info.setRirekiId(toStringSafe(header.getKs21Id()));
        info.setSc1Id(toStringSafe(header.getSc1Id()));
        info.setHoujinId(toStringSafe(header.getHoujinId()));
        info.setShisetuId(toStringSafe(header.getShisetuId()));
        info.setSvJigyoId(toStringSafe(header.getSvJigyoId()));
        info.setUserid(toStringSafe(header.getUserid()));
        info.setCreateYmd(header.getCreateYmd());
        info.setShokuId(toStringSafe(header.getShokuId()));
        info.setTermid(toStringSafe(header.getTermid()));
        return info;
    }

    /**
     * ヘッダ情報から履歴情報へのマッピング（計画書（1）用）
     * 
     * @param header 計画書（1）ヘッダ情報
     */
    private Gui01041Rireki mapToRirekiInfoCks11(Cks11InfoOutEntity header) {
        Gui01041Rireki info = new Gui01041Rireki();
        info.setRirekiId(toStringSafe(header.getKs11Id()));
        info.setSc1Id(toStringSafe(header.getSc1Id()));
        info.setHoujinId(toStringSafe(header.getHoujinId()));
        info.setShisetuId(toStringSafe(header.getShisetuId()));
        info.setSvJigyoId(toStringSafe(header.getSvJigyoId()));
        info.setUserid(toStringSafe(header.getUserid()));
        info.setCreateYmd(toStringSafe(header.getCreateYmd()));
        info.setShokuId(toStringSafe(header.getShokuId()));
        return info;
    }

    /**
     * 職員氏名情報マッピング
     * 
     * @param rirekiInfo  履歴
     * @param shokuId     職員ID
     * @param shokuinList 職員氏名情報
     */
    private void mapShokuinName(Gui01041Rireki rirekiInfo, Integer shokuId,
            List<ShokuinInfoListSpaceSortOutEntity> shokuinList) {
        shokuinList.stream()
                // マッピング条件：ヘッダ情報.作成者 = 職員氏名情報.職員ID
                .filter(shoku -> shoku.getChkShokuId().equals(shokuId)).findFirst().ifPresent(shoku -> {
                    String fullName = shoku.getShokuin1Knj() + StringUtils.SPACE + shoku.getShokuin2Knj();
                    rirekiInfo.setShokuKnj(fullName);
                });
    }

    /**
     * 有効期間情報マッピング
     * 
     * @param rirekiInfo 履歴
     * @param termId     職員氏名情報.有効期間ID
     * @param termList   職員氏名情報
     */
    private void mapTermInfo(Gui01041Rireki rirekiInfo, Integer termId, List<KrkComYukoKikanOutEntity> termList) {
        termList.stream()
                // マッピング条件：計画書（２）ヘッダ情報.有効期間ID = 職員氏名情報.有効期間ID
                .filter(term -> ((term != null && term.getTermid() != null && termId != null
                        && term.getTermid().equals(termId))
                        || (term != null && term.getTermid() == null && termId == null)))
                .findFirst().ifPresent(term -> {
                    rirekiInfo.setTermKnj(term.getKikan());
                });
    }

    /**
     * 詳細情報取得
     * 
     * @param inDto  計画書（２）取込画面の入力DTO.
     * @param outDto 計画書（２）取込画面情報OUT DTO
     */
    private void processDetail(Careplan2ImportSelectServiceInDto inDto, Careplan2ImportSelectServiceOutDto outDto) {
        // リクエストパラメータ詳細.取込元が「計画書(2)」の場合
        if (CommonConstants.STR_CARE_PLAN2.equals(inDto.getTorikomiMoto())) {
            // 4.1. 且つ、遷移元が"週間計画"、且つ、リクエストパラメータ.sys略称＝"CMN"、且つ、DTOIN.計画書様式≠"施設"
            if (CommonConstants.STR_WEEK_PLAN.equals(inDto.getSeniMoto())
                    && CommonConstants.SYS_KNJ_CMN.equals(inDto.getSys3ryaku())
                    && !CommonConstants.CKS_FLG_FACILITY.equals(inDto.getCksFlg())) {
                processDetail41(inDto, outDto);
            }
            // 4.2.
            // 且つ、（遷移元が"週間計画"、且つ、リクエストパラメータ.計画書様式="施設"、又は、遷移元が"日課計画"、且つ、リクエストパラメータ.計画書様式="施設"）
            else if ((CommonConstants.STR_WEEK_PLAN.equals(inDto.getSeniMoto())
                    && CommonConstants.CKS_FLG_FACILITY.equals(inDto.getCksFlg()))
                    || (CommonConstants.STR_DAILY_ROUTINE_PLAN.equals(inDto.getSeniMoto())
                            && CommonConstants.CKS_FLG_FACILITY.equals(inDto.getCksFlg()))) {
                processDetail42(inDto, outDto);
            }
            // 4.3. リクエストパラメータ詳細.取込元が「計画書(2)」の場合、且つ、（遷移元が"週間計画"、
            // 且つ、リクエストパラメータ.sys略称<>"CMN"、且つ、リクエストパラメータ.計画書様式="居宅"
            // 又は、遷移元が"日課計画"、且つ、リクエストパラメータ.計画書様式="居宅"）
            else if ((CommonConstants.STR_WEEK_PLAN.equals(inDto.getSeniMoto())
                    && !CommonConstants.SYS_KNJ_CMN.equals(inDto.getSys3ryaku())
                    && CommonConstants.CKS_FLG_HOME.equals(inDto.getCksFlg()))
                    || (CommonConstants.STR_DAILY_ROUTINE_PLAN.equals(inDto.getSeniMoto())
                            && CommonConstants.CKS_FLG_HOME.equals(inDto.getCksFlg()))) {
                processDetail43(inDto, outDto);
            }
            // 4.4. リクエストパラメータ詳細.取込元が「計画書(2)」の場合、且つ、遷移元が"計画ﾓﾆﾀﾆﾘﾝｸﾞ"、"評価表"、"実施ﾓﾆﾀﾆﾘﾝｸ"
            else if (SENSI_MOTO_LIST.contains(inDto.getSeniMoto())) {
                processDetail44(inDto, outDto);
            }
        }
        // 4.5. リクエストパラメータ詳細.取込元が「計画書(1)」の場合
        // リクエストパラメータ詳細.取込元が「計画書(2)」の場合、且つ、遷移元が"計画ﾓﾆﾀﾆﾘﾝｸﾞ"、"評価表"、"実施ﾓﾆﾀﾆﾘﾝｸﾞ"
        if (CommonConstants.STR_CARE_PLAN1.equals(inDto.getTorikomiMoto())
                && SENSI_MOTO_LIST.contains(inDto.getSeniMoto())) {
            processDetail45(inDto, outDto);
        }
    }

    /**
     * 4.1. リクエストパラメータ詳細.取込元が「計画書(2)」の場合
     * 且つ、遷移元が"週間計画"、且つ、リクエストパラメータ.sys略称＝"CMN"、且つ、DTOIN.計画書様式≠"施設"
     * 
     * @param inDto  計画書（２）取込画面の入力DTO.
     * @param outDto 計画書（２）取込画面情報OUT DTO
     */
    private void processDetail41(Careplan2ImportSelectServiceInDto inDto, Careplan2ImportSelectServiceOutDto outDto) {
        Keikaku2ServiceInfoDmyByCriteriaInEntity criteria = new Keikaku2ServiceInfoDmyByCriteriaInEntity();
        criteria.setAlKs21Id(rirekiId);
        // 4.1.1. 計画書（２）サービス情報取得処理を行う。
        List<Keikaku2ServiceInfoDmyOutEntity> serviceInfoList = cpnTucCks211SelectMapper
                .findKeikaku2ServiceInfoDmyByCriteria(criteria);
        for (Keikaku2ServiceInfoDmyOutEntity serviceInfo : serviceInfoList) {
            // ① レスポンスパラメータ.保険サービスリストに処理行の同名項目をそのまま設定する
            Gui01041HokenSv hokenSvInfo = getHokenSv(serviceInfo);
            // ②-1 ｻｰﾋﾞｽ事業所（表示用）の編集
            editServiceJigyosho(serviceInfo, hokenSvInfo);
            // ②-2 サービス事業者コードを取得
            String svJigyoCd = getServiceJigyoCd(serviceInfo);
            // ②-3 ｻｰﾋﾞｽ種別（表示用）の編集
            editServiceShubetsu(serviceInfo, hokenSvInfo, svJigyoCd);
            // ②-4 ｻｰﾋﾞｽ内容（表示用）の編集
            editServiceNaiyo(serviceInfo, hokenSvInfo);
            // ②-5 ｻｰﾋﾞｽ時間（表示用）の編集
            editServiceJikan(serviceInfo, hokenSvInfo);
            // ②-6 ｻｰﾋﾞｽ曜日（表示用）の編集
            editServiceYoubi(serviceInfo, hokenSvInfo, inDto);
            outDto.getHokenSvList().add(hokenSvInfo);

        }

        // 4.1.2. 月日リスト取得
        Keikaku2TukihiInfoByCriteriaInEntity tukihiCriteria = new Keikaku2TukihiInfoByCriteriaInEntity();
        tukihiCriteria.setAiKs21Id(rirekiId);
        List<Keikaku2TukihiInfoOutEntity> tukihiList = cpnTucCks212SelectMapper
                .findKeikaku2TukihiInfoByCriteria(tukihiCriteria);
        if (CollectionUtils.isNotEmpty(tukihiList)) {
            outDto.getTukihiList().addAll((tukihiList.stream().map(item -> {
                Gui01041Tukihi tukihi = new Gui01041Tukihi();
                tukihi.setKs212Id(toStringSafe(item.getKs212Id()));
                tukihi.setKs21Id(toStringSafe(item.getKs21Id()));
                tukihi.setKs211Id(toStringSafe(item.getKs211Id()));
                tukihi.setStartYmd(toStringSafe(item.getStartYmd()));
                tukihi.setEndYmd(toStringSafe(item.getEndYmd()));
                return tukihi;
            }).collect(Collectors.toList())));
        }

        // 4.1.3. 下記のレスポンスパラメータNULLを設定する
        outDto.setKkak2List(null);
        outDto.setYoubiList(null);
        outDto.setTantoList(null);
        outDto.setHindoList(null);
        outDto.setKkak1List(null);
    }

    /**
     * 4.2. リクエストパラメータ詳細.取込元が「計画書(2)」の場合
     * 且つ、（遷移元が"週間計画"、且つ、リクエストパラメータ.計画書様式="施設"、又は、遷移元が"日課計画"、且つ、リクエストパラメータ.計画書様式="施設"）
     * 
     * @param inDto  計画書（２）取込画面の入力DTO.
     * @param outDto 計画書（２）取込画面情報OUT DTO
     */
    private void processDetail42(Careplan2ImportSelectServiceInDto inDto, Careplan2ImportSelectServiceOutDto outDto) {
        // 4.2.1. 計画書(2)リスト取得
        Keikaku2DataBaseByCriteriaInEntity keikaku2DBByCriteria = new Keikaku2DataBaseByCriteriaInEntity();
        keikaku2DBByCriteria.setKs21(rirekiId);
        List<Keikaku2DataBaseOutEntity> kkak2List = cpnTucCks22SelectMapper
                .findKeikaku2DataBaseByCriteria(keikaku2DBByCriteria);

        // 計画書（２）データ詳細情報取得処理を行う。
        Keikaku2DataDetailTantoHindoCntAriByCriteriaInEntity keikaku2DataDetailTantoCriteria = new Keikaku2DataDetailTantoHindoCntAriByCriteriaInEntity();
        keikaku2DataDetailTantoCriteria.setAlKs21Id(rirekiId);
        List<Keikaku2DataDetailTantoHindoCntAriOutEntity> detailList = cpnTucCks22AndCks21SelectMapper
                .findKeikaku2DataDetailTantoHindoCntAriByCriteria(keikaku2DataDetailTantoCriteria);
        outDto.getKkak2List().addAll(convertKkak2ListToDto(kkak2List, detailList));
        // 4.2.2. 曜日リスト取得
        CpnTucCks221InfoByCriteriaInEntity cpnTicCks221Criteria = new CpnTucCks221InfoByCriteriaInEntity();
        cpnTicCks221Criteria.setAiKs21Id(rirekiId);
        List<CpnTucCks221InfoOutEntity> youbiList = cpnTucCks221SelectMapper
                .findCpnTucCks221InfoByCriteria(cpnTicCks221Criteria);
        outDto.getYoubiList().addAll(convertYoubiToDto(youbiList));

        // 4.2.3. 担当者リスト取得
        CpnTucCks222InfoByCriteriaInEntity tantoCriteria = new CpnTucCks222InfoByCriteriaInEntity();
        tantoCriteria.setAiKs21Id(rirekiId);
        List<CpnTucCks222InfoOutEntity> tantoList = cpnTucCks222SelectMapper
                .findCpnTucCks222InfoByCriteria(tantoCriteria);
        outDto.getTantoList().addAll(convertTantoToDto(tantoList));

        // 4.2.4. 頻度リスト取得
        Keikaku2HindoInfoByCriteriaInEntity keikaku2HindoInfoByCriteriaInEntity = new Keikaku2HindoInfoByCriteriaInEntity();
        keikaku2HindoInfoByCriteriaInEntity.setAlKs21Id(rirekiId);
        keikaku2HindoInfoByCriteriaInEntity.setAlKs22Id(rirekiId);
        List<Keikaku2HindoInfoOutEntity> hindoList = cpnTucCks223SelectMapper
                .findKeikaku2HindoInfoByCriteria(keikaku2HindoInfoByCriteriaInEntity);
        outDto.getHindoList().addAll(convertHindoToDto(hindoList));

        // 4.2.5. 記録との連携情報リスト取得
        // リクエストパラメータ.記録との連携が「1」:記録との連携を行うの場合、
        if (CommonConstants.STR_1.equals(inDto.getKirokuRenkeiFlg())) {
            // 4.2.5.1. 記録との連携情報を取得する。
            CpnTucCks224InfoByCriteriaInEntity cksInDto = new CpnTucCks224InfoByCriteriaInEntity();
            // 計画書（２）ID 変数.履歴ID
            cksInDto.setKs21Id(rirekiId);
            List<CpnTucCks224InfoOutEntity> cksOutList = cpnTucCks224SelectMapper
                    .findCpnTucCks224InfoByCriteria(cksInDto);
            // 4.2.5.2.
            // 「4.2.5.1.」で取得した記録との連携情報リストと「4.2.1.」で取得し計画書(2)リストとマッピングし、該当する記録との連携情報リスト.日課連動項目IDをレスポンスパラメータ.日課連動項目IDに設定する。
            if (CollectionUtils.isNotEmpty(outDto.getKkak2List()) && CollectionUtils.isNotEmpty(cksOutList)) {
                // ※マッピングキー：計画書（２）行データID／カウンター、計画書（２）ID／計画書（２）ID
                for (CpnTucCks224InfoOutEntity cksOutInfo : cksOutList) {
                    String cksKey = StringUtils.join(CommonConstants.STR_POUND_KEY, cksOutInfo.getKs22Id(),
                            cksOutInfo.getKs21Id());
                    for (Gui01041Kkak2 kkak2Info : outDto.getKkak2List()) {
                        String kkak2Key = StringUtils.join(CommonConstants.STR_POUND_KEY, kkak2Info.getKs22Id(),
                                kkak2Info.getKs21Id());
                        if (cksKey.equals(kkak2Key)) {
                            kkak2Info.setNikkaId(CommonDtoUtil.objValToString(cksOutInfo.getNikkaId()));
                        }
                    }
                }
            }
        }

        // 4.2.5. 下記のレスポンスパラメータNULLを設定する
        outDto.setHokenSvList(null);
        outDto.setTukihiList(null);
        outDto.setKkak1List(null);
    }

    /**
     * 4.3. リクエストパラメータ詳細.取込元が「計画書(2)」の場合
     * 且つ、（遷移元が"週間計画"、且つ、リクエストパラメータ.計画書様式="施設"、又は、遷移元が"日課計画"、且つ、リクエストパラメータ.計画書様式="施設"）
     * 又は、遷移元が"日課計画"、且つ、リクエストパラメータ.計画書様式="居宅"）
     * 
     * @param inDto  計画書（２）取込画面の入力DTO.
     * @param outDto 計画書（２）取込画面情報OUT DTO
     */
    private void processDetail43(Careplan2ImportSelectServiceInDto inDto, Careplan2ImportSelectServiceOutDto outDto) {
        // 4.2.1. 計画書(2)リスト取得
        Keikaku2DataBaseByCriteriaInEntity keikaku2DBByCriteria = new Keikaku2DataBaseByCriteriaInEntity();
        keikaku2DBByCriteria.setKs21(rirekiId);
        List<Keikaku2DataBaseOutEntity> kkak2List = cpnTucCks22SelectMapper
                .findKeikaku2DataBaseByCriteria(keikaku2DBByCriteria);

        // 計画書（２）データ詳細情報取得処理を行う。
        Keikaku2DataDetailByCriteriaInEntity keikaku2DataDetailCriteria = new Keikaku2DataDetailByCriteriaInEntity();
        keikaku2DataDetailCriteria.setAlKs21Id(rirekiId);
        List<Keikaku2DataDetailOutEntity> detailList = cpnTucCks22AndCks21SelectMapper
                .findKeikaku2DataDetailByCriteria(keikaku2DataDetailCriteria);
        outDto.getKkak2List().addAll(convertKkak2ListToDtoWithOutTanto(kkak2List, detailList));
        // 4.2.2. 曜日リスト取得
        CpnTucCks221InfoByCriteriaInEntity cpnTicCks221Criteria = new CpnTucCks221InfoByCriteriaInEntity();
        cpnTicCks221Criteria.setAiKs21Id(rirekiId);
        List<CpnTucCks221InfoOutEntity> youbiList = cpnTucCks221SelectMapper
                .findCpnTucCks221InfoByCriteria(cpnTicCks221Criteria);
        outDto.getYoubiList().addAll(convertYoubiToDto(youbiList));

        // 4.2.3. 担当者リスト取得
        CpnTucCks222InfoByCriteriaInEntity tantoCriteria = new CpnTucCks222InfoByCriteriaInEntity();
        tantoCriteria.setAiKs21Id(rirekiId);
        List<CpnTucCks222InfoOutEntity> tantoList = cpnTucCks222SelectMapper
                .findCpnTucCks222InfoByCriteria(tantoCriteria);
        outDto.getTantoList().addAll(convertTantoToDto(tantoList));

        // 4.2.4. 頻度リスト取得
        Keikaku2HindoInfoByCriteriaInEntity keikaku2HindoInfoByCriteriaInEntity = new Keikaku2HindoInfoByCriteriaInEntity();
        keikaku2HindoInfoByCriteriaInEntity.setAlKs21Id(rirekiId);
        List<Keikaku2HindoInfoOutEntity> hindoList = cpnTucCks223SelectMapper
                .findKeikaku2HindoInfoByCriteria(keikaku2HindoInfoByCriteriaInEntity);
        outDto.getHindoList().addAll(convertHindoToDto(hindoList));

        // 4.2.5. 下記のレスポンスパラメータNULLを設定する
        outDto.setHokenSvList(null);
        outDto.setTukihiList(null);
        outDto.setKkak1List(null);
    }

    /**
     * 4.4. リクエストパラメータ詳細.取込元が「計画書(2)」の場合
     * リクエストパラメータ詳細.取込元が「計画書(2)」の場合、且つ、遷移元が"計画ﾓﾆﾀﾆﾘﾝｸﾞ"、"評価表"、"実施ﾓﾆﾀﾆﾘﾝｸ"
     * 
     * @param inDto  計画書（２）取込画面の入力DTO.
     * @param outDto 計画書（２）取込画面情報OUT DTO
     */
    private void processDetail44(Careplan2ImportSelectServiceInDto inDto, Careplan2ImportSelectServiceOutDto outDto) {
        // 4.4.1. 計画書(2)リスト取得
        KeikakushoDataSelByCriteriaInEntity criteria = new KeikakushoDataSelByCriteriaInEntity();
        criteria.setKs21(rirekiId);
        List<KeikakushoDataSelOutEntity> kkak2List = cpnTucCks22SelectMapper.findKeikakushoDataSelByCriteria(criteria);
        outDto.getKkak2List().addAll(convertKkak2ListToDto(kkak2List));
        // 4.4.2. 下記のレスポンスパラメータNULLを設定する
        outDto.setHokenSvList(null);
        outDto.setTukihiList(null);
        outDto.setYoubiList(null);
        outDto.setTantoList(null);
        outDto.setHindoList(null);
        outDto.setKkak1List(null);
    }

    /**
     * 4.5. リクエストパラメータ詳細.取込元が「計画書(1)」の場合
     * リクエストパラメータ詳細.取込元が「計画書(2)」の場合、且つ、遷移元が"計画ﾓﾆﾀﾆﾘﾝｸﾞ"、"評価表"、"実施ﾓﾆﾀﾆﾘﾝｸﾞ"
     * 
     * @param inDto  計画書（２）取込画面の入力DTO.
     * @param outDto 計画書（２）取込画面情報OUT DTO
     */
    private void processDetail45(Careplan2ImportSelectServiceInDto inDto, Careplan2ImportSelectServiceOutDto outDto) {
        // 4.5.1. 計画書(1)リスト取得
        Keikaku1DetailInfoByCriteriaInEntity criteria = new Keikaku1DetailInfoByCriteriaInEntity();
        criteria.setKs11(rirekiId);
        List<Keikaku1DetailInfoOutEntity> kkak1List = cks11Mapper.findKeikaku1DetailInfoByCriteria(criteria);
        outDto.getKkak1List().addAll(convertKkak1ListToDto(kkak1List));
        // 4.5.2. 下記のレスポンスパラメータNULLを設定する
        outDto.setHokenSvList(null);
        outDto.setTukihiList(null);
        outDto.setKkak2List(null);
        outDto.setYoubiList(null);
        outDto.setTantoList(null);
        outDto.setHindoList(null);
    }

    /**
     * 計画書(2)リストからDTOへ値を設定
     * 
     * @param list       計画書(2)リスト
     * @param detailList 計画書（２）データ詳細情報
     * @return 計画書(2)リストDTO
     */
    private List<Gui01041Kkak2> convertKkak2ListToDto(List<Keikaku2DataBaseOutEntity> list,
            List<Keikaku2DataDetailTantoHindoCntAriOutEntity> detailList) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(model -> {
            Gui01041Kkak2 dto = new Gui01041Kkak2();
            // カウンター
            dto.setKs22Id(toStringSafe(model.getKs22Id()));
            // 計画書（２）ID
            dto.setKs21Id(toStringSafe(model.getKs21Id()));
            // 具体的
            dto.setGutaitekiKnj(model.getGutaitekiKnj());
            // 長期
            dto.setChoukiKnj(model.getChoukiKnj());
            // 短期
            dto.setTankiKnj(model.getTankiKnj());
            // 介護
            dto.setKaigoKnj(model.getKaigoKnj());
            // サービス種
            dto.setSvShuKnj(model.getSvShuKnj());
            // 頻度
            dto.setHindoKnj(model.getHindoKnj());
            // 期間
            dto.setKikanKnj(model.getKikanKnj());
            // 通番
            dto.setSeq(toStringSafe(model.getSeq()));
            // 課題番号
            dto.setKadaiNo(toStringSafe(model.getKadaiNo()));
            // 介護番号
            dto.setKaigoNo(toStringSafe(model.getKaigoNo()));
            // 長期期間
            dto.setChoKikanKnj(model.getChoKikanKnj());
            // 短期期間
            dto.setTanKikanKnj(model.getTanKikanKnj());
            // 給付対象
            dto.setHkyuKbn(toStringSafe(model.getHkyuKbn()));
            // サービス事業者ＣＤ
            dto.setJigyouId(toStringSafe(model.getJigyouId()));
            // サービス事業者名
            dto.setJigyoNameKnj(model.getJigyoNameKnj());
            // 給付文字
            dto.setHkyuKnj(model.getHkyuKnj());
            detailList.stream().filter(
                    detail -> detail.getKs22Id() == model.getKs22Id() && detail.getKs21Id() == model.getKs21Id())
                    .findFirst().ifPresent(item -> {
                        // 有効期間ID
                        dto.setTermId(toStringSafe(item.getTermid()));

                    });
            // 週間
            dto.setSyukan(StringUtils.EMPTY);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 計画書(2)リストからDTOへ値を設定
     * 
     * @param list       計画書(2)リスト
     * @param detailList 計画書（２）データ詳細情報
     * @return 計画書(2)リストDTO
     */
    private List<Gui01041Kkak2> convertKkak2ListToDtoWithOutTanto(List<Keikaku2DataBaseOutEntity> list,
            List<Keikaku2DataDetailOutEntity> detailList) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(model -> {
            Gui01041Kkak2 dto = new Gui01041Kkak2();
            // カウンター
            dto.setKs22Id(toStringSafe(model.getKs22Id()));
            // 計画書（２）ID
            dto.setKs21Id(toStringSafe(model.getKs21Id()));
            // 具体的
            dto.setGutaitekiKnj(model.getGutaitekiKnj());
            // 長期
            dto.setChoukiKnj(model.getChoukiKnj());
            // 短期
            dto.setTankiKnj(model.getTankiKnj());
            // 介護
            dto.setKaigoKnj(model.getKaigoKnj());
            // サービス種
            dto.setSvShuKnj(model.getSvShuKnj());
            // 頻度
            dto.setHindoKnj(model.getHindoKnj());
            // 期間
            dto.setKikanKnj(model.getKikanKnj());
            // 通番
            dto.setSeq(toStringSafe(model.getSeq()));
            // 課題番号
            dto.setKadaiNo(toStringSafe(model.getKadaiNo()));
            // 介護番号
            dto.setKaigoNo(toStringSafe(model.getKaigoNo()));
            // 長期期間
            dto.setChoKikanKnj(model.getChoKikanKnj());
            // 短期期間
            dto.setTanKikanKnj(model.getTanKikanKnj());
            // 給付対象
            dto.setHkyuKbn(toStringSafe(model.getHkyuKbn()));
            // サービス事業者ＣＤ
            dto.setJigyouId(toStringSafe(model.getJigyouId()));
            // サービス事業者名
            dto.setJigyoNameKnj(model.getJigyoNameKnj());
            // 給付文字
            dto.setHkyuKnj(model.getHkyuKnj());
            detailList.stream().filter(
                    detail -> detail.getKs22Id() == model.getKs22Id() && detail.getKs21Id() == model.getKs21Id())
                    .findFirst().ifPresent(item -> {
                        // 有効期間ID
                        dto.setTermId(toStringSafe(item.getTermid()));

                    });
            // 週間
            dto.setSyukan(StringUtils.EMPTY);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 計画書(2)リストからDTOへ値を設定
     * 
     * @param list 計画書(2)リスト
     * @return 計画書(2)リストDTO
     */
    private List<Gui01041Kkak2> convertKkak2ListToDto(List<KeikakushoDataSelOutEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(model -> {
            Gui01041Kkak2 dto = new Gui01041Kkak2();
            // カウンター
            dto.setKs22Id(toStringSafe(model.getKs22Id()));
            // 計画書（２）ID
            dto.setKs21Id(toStringSafe(model.getKs21Id()));
            // 具体的
            dto.setGutaitekiKnj(model.getGutaitekiKnj());
            // 長期
            dto.setChoukiKnj(model.getChoukiKnj());
            // 短期
            dto.setTankiKnj(model.getTankiKnj());
            // 介護
            dto.setKaigoKnj(model.getKaigoKnj());
            // サービス種
            dto.setSvShuKnj(model.getSvShuKnj());
            // 頻度
            dto.setHindoKnj(model.getHindoKnj());
            // 期間
            dto.setKikanKnj(model.getKikanKnj());
            // 通番
            dto.setSeq(toStringSafe(model.getSeq()));
            // 課題番号
            dto.setKadaiNo(toStringSafe(model.getKadaiNo()));
            // 介護番号
            dto.setKaigoNo(toStringSafe(model.getKaigoNo()));
            // 長期期間
            dto.setChoKikanKnj(model.getChoKikanKnj());
            // 短期期間
            dto.setTanKikanKnj(model.getTanKikanKnj());
            // 給付対象
            dto.setHkyuKbn(toStringSafe(model.getHkyuKbn()));
            // サービス事業者ＣＤ
            dto.setJigyouId(toStringSafe(model.getJigyouId()));
            // サービス事業者名
            dto.setJigyoNameKnj(model.getJigyoNameKnj());
            // 給付文字
            dto.setHkyuKnj(model.getHkyuKnj());
            // 週間
            dto.setSyukan(StringUtils.EMPTY);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 計画書(1)リストからDTOへ値を設定
     * 
     * @param list 計画書(1)リスト
     * @return 計画書(1)リストDTO
     */
    private List<Gui01041Kkak1> convertKkak1ListToDto(List<Keikaku1DetailInfoOutEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(model -> {
            Gui01041Kkak1 dto = new Gui01041Kkak1();
            // 計画書ID
            dto.setKs11Id(toStringSafe(model.getKs11Id()));
            // 計画期間ID
            dto.setSc1Id(toStringSafe(model.getSc1Id()));
            // 法人ID
            dto.setHoujinId(toStringSafe(model.getHoujinId()));
            // 施設ID
            dto.setShisetuId(toStringSafe(model.getShisetuId()));
            // 事業者ID
            dto.setSvJigyoId(toStringSafe(model.getSvJigyoId()));
            // 利用者ID
            dto.setUserid(toStringSafe(model.getUserid()));
            // 作成日
            dto.setCreateYmd(model.getCreateYmd());
            // 作成者
            dto.setShokuId(toStringSafe(model.getShokuId()));
            // 意向
            dto.setIkouKnj(model.getIkouKnj());
            // 意見
            dto.setIkenKnj(model.getIkenKnj());
            // 方針
            dto.setHoushinKnj(model.getHoushinKnj());
            // 承諾日
            dto.setShudakuYmd(model.getShudakuYmd());
            // 計画区分
            dto.setKubun(toStringSafe(model.getKubun()));
            // 認定区分
            dto.setNintei(toStringSafe(model.getNintei()));
            // 初回作成日
            dto.setShokaiYmd(model.getShokaiYmd());
            // 認定日
            dto.setNinteiYmd(model.getNinteiYmd());
            // 有効開始日
            dto.setYukoSYmd(model.getYukoSYmd());
            // 有効終了日
            dto.setYukoEYmd(model.getYukoEYmd());
            // 要介護度
            dto.setYokaiKbn(toStringSafe(model.getYokaiKbn()));
            // 要介護見込みフラグ
            dto.setMikomiFlg(toStringSafe(model.getMikomiFlg()));
            // 要介護度(その他)
            dto.setSonotaKnj(model.getSonotaKnj());
            // 家事援助区分
            dto.setKajiCode(toStringSafe(model.getKajiCode()));
            // 家事援助(その他)
            dto.setKajiSonotaKnj(model.getKajiSonotaKnj());
            // 作成者所属
            dto.setShozokuId(toStringSafe(model.getShozokuId()));
            // 地域コード
            dto.setChiikiCd(model.getChiikiCd());
            // 調査対象者コード
            dto.setTaishoushaCd(model.getTaishoushaCd());
            // 担当者
            dto.setTantoId(toStringSafe(model.getTantoId()));
            // 暫定フラグ
            dto.setZanteiFlg(toStringSafe(model.getZanteiFlg()));
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 曜日リストからDTOへ値を設定
     * 
     * @param list 曜日リスト
     * @return 曜日リスト
     */
    private List<Gui01041Youbi> convertYoubiToDto(List<CpnTucCks221InfoOutEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(model -> {
            Gui01041Youbi dto = new Gui01041Youbi();
            // カウンター
            dto.setKs221Id(toStringSafe(model.getKs221Id()));
            // 計画書（２）行データID
            dto.setKs22Id(toStringSafe(model.getKs22Id()));
            // 計画書（２）ID
            dto.setKs21Id(toStringSafe(model.getKs21Id()));
            // 曜日
            dto.setYoubi(model.getYoubi());
            // 週単位以外のｻｰﾋﾞｽ区分
            dto.setIgaiKbn(toStringSafe(model.getIgaiKbn()));
            // 週単位以外のｻｰﾋﾞｽ（日付指定）
            dto.setIgaiDate(model.getIgaiDate());
            // 週単位以外のｻｰﾋﾞｽ（曜日指定）
            dto.setIgaiWeek(model.getIgaiWeek());
            // 居宅：開始時間
            dto.setKaishiJikan(model.getKaishiJikan());
            // 居宅：終了時間
            dto.setShuuryouJikan(model.getShuuryouJikan());
            // 居宅：サービス種類
            dto.setSvShuruiCd(model.getSvShuruiCd());
            // 居宅：サービス項目（台帳）
            dto.setSvItemCd(toStringSafe(model.getSvItemCd()));
            // 居宅：サービス事業者CD
            dto.setSvJigyoId(toStringSafe(model.getSvJigyoId()));
            // 居宅：福祉用具貸与単位
            dto.setTanka(toStringSafe(model.getTanka()));
            // 福祉用具貸与商品コード
            dto.setFygId(toStringSafe(model.getFygId()));
            // 合成識別区分
            dto.setGouseiSikKbn(model.getGouseiSikKbn());
            // 加算フラグ
            dto.setKasanFlg(toStringSafe(model.getKasanFlg()));
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 頻度リストからDTOへ値を設定
     * 
     * @param list 頻度リスト
     * @return 頻度リストDTO
     */
    private List<Gui01041Hindo> convertHindoToDto(List<Keikaku2HindoInfoOutEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(model -> {
            Gui01041Hindo dto = new Gui01041Hindo();
            // カウンター
            dto.setKs223Id(toStringSafe(model.getKs223Id()));
            // 計画書（２）行データID
            dto.setKs22Id(toStringSafe(model.getKs22Id()));
            // 計画書（２）ID
            dto.setKs21Id(toStringSafe(model.getKs21Id()));
            // 施設：頻度（日課）
            dto.setNikka(toStringSafe(model.getNikka()));
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 担当者リストからDTOへ値を設定
     * 
     * @param list 担当者リスト
     * @return 担当者リストDTO
     */
    private List<Gui01041Tanto> convertTantoToDto(List<CpnTucCks222InfoOutEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(model -> {
            Gui01041Tanto dto = new Gui01041Tanto();
            // カウンター
            dto.setKs222Id(toStringSafe(model.getKs222Id()));
            // 計画書（２）行データID
            dto.setKs22Id(toStringSafe(model.getKs22Id()));
            // 計画書（２）ID
            dto.setKs21Id(toStringSafe(model.getKs21Id()));
            // 施設：職種（担当者）
            dto.setShokushuId(toStringSafe(model.getShokushuId()));
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * ②-1 ｻｰﾋﾞｽ事業所（表示用）の編集
     * 
     * @param serviceInfo 計画書（２）サービス情報
     * @param hokenSvInfo 保険サービス情報
     */
    private void editServiceJigyosho(Keikaku2ServiceInfoDmyOutEntity serviceInfo, Gui01041HokenSv hokenSvInfo) {
        SvJigyoNameByCriteriaInEntity criteria = new SvJigyoNameByCriteriaInEntity();
        criteria.setAlSvj(CommonDtoUtil.objValToString(serviceInfo.getSvJigyoId()));
        // サービス事業者マスタ情報取得処理を行う。
        List<SvJigyoNameOutEntity> jigyoName = comMscSvjigyoSelectMapper.findSvJigyoNameByCriteria(criteria);

        // 取得した「事業名」を設定する
        if (CollectionUtils.isNotEmpty(jigyoName)) {
            hokenSvInfo.setDmySvShuruiKnj(jigyoName.getFirst().getJigyoRyakuKnj());
        }
    }

    /**
     * ②-2 サービス事業者コードを取得
     * 
     * @param serviceInfo 計画書（２）サービス情報
     */
    private String getServiceJigyoCd(Keikaku2ServiceInfoDmyOutEntity serviceInfo) {
        SvJigyoCdByCriteriaInEntity criteria = new SvJigyoCdByCriteriaInEntity();
        criteria.setAlSvj(CommonDtoUtil.objValToString(serviceInfo.getSvJigyoId()));
        // サービス事業者マスタ情報取得処理を行う。
        List<SvJigyoCdOutEntity> svJigyoCd = comMscSvjigyoSelectMapper.findSvJigyoCdByCriteria(criteria);

        return CollectionUtils.isNotEmpty(svJigyoCd) ? svJigyoCd.getFirst().getSvJigyoCd() : null;
    }

    /**
     * ②-3 ｻｰﾋﾞｽ種別（表示用）の編集
     * 
     * @param serviceInfo 計画書（２）サービス情報
     * @param hokenSvInfo 保険サービス情報
     * @param svJigyoCd   サービス事業者コード
     */
    private void editServiceShubetsu(Keikaku2ServiceInfoDmyOutEntity serviceInfo, Gui01041HokenSv hokenSvInfo,
            String svJigyoCd) {
        // ②-3-1 サービス事業者コードを取得できる場合
        if (svJigyoCd != null) {
            RyakusyoKanjiByCriteriaInEntity criteria = new RyakusyoKanjiByCriteriaInEntity();
            criteria.setLsConvSvtype(svJigyoCd);
            // サービス事業者コードマスタ(15-8)取得処理を行う。
            List<RyakusyoKanjiOutEntity> svjigyoName = comMscSvjigyoNameSelectMapper
                    .findRyakusyoKanjiByCriteria(criteria);
            if (CollectionUtils.isNotEmpty(svjigyoName)) {
                hokenSvInfo.setDmySvJigyoKnj(svjigyoName.getFirst().getRuakuKnj());
            }
        }
        // ②-3-2 サービス事業者コードを取得できない場合、且つ、
        // 処理行.居宅：サービス種類が数字化できて、且つ、処理行.居宅：サービス種類を数字化後＞ 0
        else if (isNumeric(serviceInfo.getSvShuruiCd()) && Integer.parseInt(serviceInfo.getSvShuruiCd()) > 0) {
            SvjigyoNameDetailInfoByCriteriaInEntity criteria = new SvjigyoNameDetailInfoByCriteriaInEntity();
            criteria.setScKindCdList(Arrays.asList(serviceInfo.getSvShuruiCd()));
            // サービス事業者コードマスタ取得処理を行う。
            List<SvjigyoNameDetailInfoOutEntity> svjigyoName = comMscSvjigyoNameSelectMapper
                    .findSvjigyoNameDetailInfoByCriteria(criteria);

            if (CollectionUtils.isNotEmpty(svjigyoName)) {
                hokenSvInfo.setDmySvJigyoKnj(svjigyoName.getFirst().getRuakuKnj());
            }
        }
    }

    /**
     * ②-4 ｻｰﾋﾞｽ内容（表示用）の編集
     * 
     * @param serviceInfo 計画書（２）サービス情報
     * @param hokenSvInfo 保険サービス情報
     */
    private void editServiceNaiyo(Keikaku2ServiceInfoDmyOutEntity serviceInfo, Gui01041HokenSv hokenSvInfo) {
        // ②-4-1.サービス検索の開始日、終了日取得
        TermId2DateLogicOutDto termDates = kghKrkZCpnFuncLogic.getTermid2date(serviceInfo.getSvJigyoId(),
                serviceInfo.getKaishiJikan(), serviceInfo.getShuuryouJikan());

        // ②-4-2.日常生活支援総合事業サービス判断
        boolean isSougouJigyo = kghKrkZCpnWeekLogic.isSougouJigyo(serviceInfo.getSvJigyoId());
        if (isSougouJigyo) {
            KghKkak2SvItemSougouByCriteriaInEntity criteria = new KghKkak2SvItemSougouByCriteriaInEntity();
            criteria.setAnItemcode(serviceInfo.getSvItemCd());
            criteria.setAsSYmd(termDates.getSYmd());
            criteria.setAsEYmd(termDates.getEYmd());
            // 20-20 介護予防・日常生活支援総合事業サービス費適用マスタ情報取得処理を行う。
            List<KghKkak2SvItemSougouOutEntity> itemuseSougou = comMhcItemuseSougouSelectMapper
                    .findKghKkak2SvItemSougouByCriteria(criteria);
            if (CollectionUtils.isNotEmpty(itemuseSougou)) {
                hokenSvInfo.setDmyItemname(itemuseSougou.getFirst().getFormalnameKnj());
            }
        } else {
            ItemuseSuByCriteriaInEntity criteria = new ItemuseSuByCriteriaInEntity();
            criteria.setAlSvJigyoId(CommonDtoUtil.objValToString(serviceInfo.getSvJigyoId()));
            criteria.setAlSvItemCd(CommonDtoUtil.objValToString(serviceInfo.getSvItemCd()));
            criteria.setLsSymd(termDates.getSYmd());
            criteria.setLsEymd(termDates.getEYmd());
            // ⅱ-1.事業所毎に定めたサービス項目のローカル名称が存在するかどうかマスタデータをカウント
            ItemuseSuOutEntity itemuseSuOutEntity = comMhcItemuseSelectMapper.countItemuseSuByCriteria(criteria);
            if (!Objects.isNull(itemuseSuOutEntity)) {
                if (itemuseSuOutEntity.getCnt() == 0) {
                    ComMhcItemuseNameByCriteriaInEntity itemUseNameByCriteria = new ComMhcItemuseNameByCriteriaInEntity();
                    itemUseNameByCriteria.setSvJigyoId(CommonConstants.AL_SV_JIGYO_ID_ZERO);
                    itemUseNameByCriteria.setItemcode(serviceInfo.getSvItemCd());
                    itemUseNameByCriteria.setSYmd(termDates.getSYmd());
                    itemUseNameByCriteria.setEYmd(termDates.getEYmd());
                    // 介護サービス費適用正式名称取得処理を行う。
                    List<ComMhcItemuseNameOutEntity> itemuseName = comMhcItemuseSelectMapper
                            .findComMhcItemuseNameByCriteria(itemUseNameByCriteria);

                    if (CollectionUtils.isNotEmpty(itemuseName)) {
                        hokenSvInfo.setDmyItemname(itemuseName.getFirst().getFormalnameKnj());
                    }
                } else {
                    ComMhcItemuseNameByCriteriaInEntity itemUseNameByCriteria = new ComMhcItemuseNameByCriteriaInEntity();
                    itemUseNameByCriteria.setSvJigyoId(serviceInfo.getSvJigyoId());
                    itemUseNameByCriteria.setItemcode(serviceInfo.getSvItemCd());
                    itemUseNameByCriteria.setSYmd(termDates.getSYmd());
                    itemUseNameByCriteria.setEYmd(termDates.getEYmd());
                    // 介護サービス費適用正式名称取得処理を行う。
                    List<ComMhcItemuseNameOutEntity> itemuseName = comMhcItemuseSelectMapper
                            .findComMhcItemuseNameByCriteria(itemUseNameByCriteria);
                    if (CollectionUtils.isNotEmpty(itemuseName)) {
                        hokenSvInfo.setDmyItemname(itemuseName.getFirst().getFormalnameKnj());
                    }
                }
            }
        }
    }

    /**
     * ②-5 ｻｰﾋﾞｽ時間（表示用）の編集
     * 
     * @param serviceInfo 計画書（２）サービス情報
     * @param hokenSvInfo 保険サービス情報
     */
    private void editServiceJikan(Keikaku2ServiceInfoDmyOutEntity serviceInfo, Gui01041HokenSv hokenSvInfo) {
        // 特殊時間フォーマットチェック
        if ((CommonConstants.KAISHI_JIKAN_7.equals(serviceInfo.getKaishiJikan())
                && (CommonConstants.KAISHI_JIKAN_7.equals(serviceInfo.getShuuryouJikan()))
                || ((CommonConstants.KAISHI_JIKAN_8.equals(serviceInfo.getKaishiJikan())
                        && (CommonConstants.KAISHI_JIKAN_8.equals(serviceInfo.getShuuryouJikan())))
                        || ((CommonConstants.KAISHI_JIKAN_9.equals(serviceInfo.getKaishiJikan())
                                && (CommonConstants.KAISHI_JIKAN_9.equals(serviceInfo.getShuuryouJikan()))))))) {
            // 特殊時間フォーマットの場合は何も設定しない
            return;
        }
        // 通常の時間表示
        hokenSvInfo.setDmyTime(
                serviceInfo.getKaishiJikan() + CommonConstants.RANGE_SEPARATOR + serviceInfo.getShuuryouJikan());
    }

    /**
     * ②-6 ｻｰﾋﾞｽ曜日（表示用）の編集
     * 
     * @param serviceInfo 計画書（２）サービス情報
     * @param hokenSvInfo 保険サービス情報
     * @param inDto       計画書（２）取込画面の入力DTO
     */
    private void editServiceYoubi(Keikaku2ServiceInfoDmyOutEntity serviceInfo, Gui01041HokenSv hokenSvInfo,
            Careplan2ImportSelectServiceInDto inDto) {
        // ②-6-1.処理行.週単位以外のｻｰﾋﾞｽ区分が「3」の場合
        if (CommonConstants.IGAI_KBN_STR_THREE.equals(CommonDtoUtil.objValToString(serviceInfo.getIgaiKbn()))) {
            // ⅰ. 共通情報.自事業所が介護予防支援事業所「50010」の場合
            if (CommonConstants.JCD_50010.equals(inDto.getDefSvJigyoId())) {
                KaigoServiceDataMdByCriteriaInEntity criteria = new KaigoServiceDataMdByCriteriaInEntity();
                criteria.setAiPlan11Id(rirekiId);
                // 介護予防サービス・支援計画書データ（月日指定）情報取得処理を行う。
                List<KaigoServiceDataMdOutEntity> kaigoServiceList = kycTucPlan16SelectMapper
                        .findKaigoServiceDataMdByCriteria(criteria);

                // ⅲ. 月日指定情報を編集
                hokenSvInfo.setDmyWeek(formatTukihiList(kaigoServiceList));
            }
            // ⅱ. 共通情報.自事業所が介護予防支援事業所「50010」以外の場合
            else {
                Keikaku2TukihiInfoByCriteriaInEntity criteria = new Keikaku2TukihiInfoByCriteriaInEntity();
                criteria.setAiKs21Id(rirekiId);
                // 計画書（２）（月日指定）情報取得処理を行う。
                List<Keikaku2TukihiInfoOutEntity> tukihiList = cpnTucCks212SelectMapper
                        .findKeikaku2TukihiInfoByCriteria(criteria);

                // ⅲ. 月日指定情報を編集
                hokenSvInfo.setDmyWeek(formatTukihiListTikihiInfoOut(tukihiList));
            }
        }
        // ②-6-2.処理行.週単位以外のｻｰﾋﾞｽ区分が「3」以外の場合
        else {
            // ⅰ.リクエストパラメータ.計画表：頻度取込が「0:選択項目名称」の場合
            if (CommonConstants.ITAKU_KKAK_HINDO_FLG_0.equals(inDto.getItakuKkakHindoFlg())) {
                editYoubiForSelectItem(serviceInfo, hokenSvInfo);
            }
            // ⅱ.リクエストパラメータ.計画表：頻度取込が「1:週○回/月○回」の場合
            else if (CommonConstants.ITAKU_KKAK_HINDO_FLG_1.equals(inDto.getItakuKkakHindoFlg())) {
                editYoubiForWeeklyMonthly(serviceInfo, hokenSvInfo);
            }
            // ⅲ.リクエストパラメータ.計画表：頻度取込が「2:随時」の場合
            else if (CommonConstants.ITAKU_KKAK_HINDO_FLG_2.equals(inDto.getItakuKkakHindoFlg())) {
                hokenSvInfo.setDmyWeek(CommonConstants.ANYTIME_STR);
            }
        }
    }

    /**
     * レスポンスパラメータ.保険サービスリスト設定
     * 
     * @param serviceInfo 計画書（２）サービス情報
     */
    private Gui01041HokenSv getHokenSv(Keikaku2ServiceInfoDmyOutEntity serviceInfo) {
        Gui01041HokenSv hokenSv = new Gui01041HokenSv();
        // カウンター
        hokenSv.setKs211Id(toStringSafe(serviceInfo.getKs211Id()));
        // 計画書（２）ID
        hokenSv.setKs21Id(toStringSafe(serviceInfo.getKs21Id()));
        // 曜日
        hokenSv.setYoubi(toStringSafe(serviceInfo.getYoubi()));
        // 週単位以外のｻｰﾋﾞｽ区分
        hokenSv.setIgaiKbn(toStringSafe(serviceInfo.getIgaiKbn()));
        // 週単位以外のｻｰﾋﾞｽ（日付指定）
        hokenSv.setIgaiDate(toStringSafe(serviceInfo.getIgaiDate()));
        // 週単位以外のｻｰﾋﾞｽ（曜日指定）
        hokenSv.setIgaiWeek(toStringSafe(serviceInfo.getIgaiWeek()));
        // 居宅：開始時間
        hokenSv.setKaishiJikan(serviceInfo.getKaishiJikan());
        // 居宅：終了時間
        hokenSv.setShuuryouJikan(serviceInfo.getShuuryouJikan());
        // 居宅：サービス種類
        hokenSv.setSvShuruiCd(serviceInfo.getSvShuruiCd());
        // 居宅：サービス項目（台帳）
        hokenSv.setSvItemCd(toStringSafe(serviceInfo.getSvItemCd()));
        // 居宅：サービス事業者CD
        hokenSv.setSvJigyoId(toStringSafe(serviceInfo.getSvJigyoId()));
        // 居宅：福祉用具貸与単位
        hokenSv.setTanka(toStringSafe(serviceInfo.getTanka()));
        // 福祉用具貸与商品コード
        hokenSv.setFygId(toStringSafe(serviceInfo.getFygId()));
        // 合成識別区分
        hokenSv.setGouseiSikKbn(toStringSafe(serviceInfo.getGouseiSikKbn()));
        // 加算フラグ
        hokenSv.setKasanFlg(toStringSafe(serviceInfo.getKasanFlg()));
        // 親レコード番号
        hokenSv.setOyaLineNo(toStringSafe(serviceInfo.getOyaLineNo()));
        return hokenSv;
    }

    /**
     * 月日リストをフォーマットする
     * 
     * @param tukihiList 月日リスト
     */
    private String formatTukihiListTikihiInfoOut(List<Keikaku2TukihiInfoOutEntity> tukihiList) {
        if (CollectionUtils.isEmpty(tukihiList)) {
            return StringUtils.EMPTY;
        }
        // 開始日、終了日でソート
        tukihiList.sort((a, b) -> {
            int startCompare = a.getStartYmd().compareTo(b.getStartYmd());
            return startCompare != 0 ? startCompare : a.getEndYmd().compareTo(b.getEndYmd());
        });

        // ⅲ-1. 月日指定リストが1件の場合
        if (tukihiList.size() == 1) {
            return tukihiList.get(0).getStartYmd() + CommonConstants.RANGE_SEPARATOR
                    + tukihiList.getFirst().getEndYmd();
        }
        // ⅲ-2. 月日指定リストが2件以上の場合
        else {
            return tukihiList.stream().map(t -> t.getStartYmd() + CommonConstants.RANGE_SEPARATOR + t.getEndYmd())
                    .collect(Collectors.joining(CommonConstants.STRING_COMMA));
        }
    }

    /**
     * 月日リストをフォーマットする
     * 
     * @param tukihiList 月日リスト
     */
    private String formatTukihiList(List<KaigoServiceDataMdOutEntity> tukihiList) {
        if (CollectionUtils.isEmpty(tukihiList)) {
            return StringUtils.EMPTY;
        }
        // 開始日、終了日でソート
        tukihiList.sort((a, b) -> {
            int startCompare = a.getStartYmd().compareTo(b.getStartYmd());
            return startCompare != 0 ? startCompare : a.getEndYmd().compareTo(b.getEndYmd());
        });

        // ⅲ-1. 月日指定リストが1件の場合
        if (tukihiList.size() == 1) {
            return tukihiList.get(0).getStartYmd() + CommonConstants.RANGE_SEPARATOR
                    + tukihiList.getFirst().getEndYmd();
        }
        // ⅲ-2. 月日指定リストが2件以上の場合
        else {
            return tukihiList.stream().map(t -> t.getStartYmd() + CommonConstants.RANGE_SEPARATOR + t.getEndYmd())
                    .collect(Collectors.joining(CommonConstants.STRING_COMMA));
        }
    }

    /**
     * 選択項目名称用の曜日編集
     * 
     * @param serviceInfo 計画書（２）サービス情報
     * @param hokenSvInfo 保険サービス情報
     */
    private void editYoubiForSelectItem(Keikaku2ServiceInfoDmyOutEntity serviceInfo, Gui01041HokenSv hokenSvInfo) {
        // ⅰ-1. 処理行.曜日が「"0000000"」(未登録)の場合
        if (CommonConstants.YOUBI_0.equals(serviceInfo.getYoubi())) {
            hokenSvInfo.setDmyWeek(StringUtils.EMPTY);
        }
        // ⅰ-2. 処理行.曜日が「"1111111"」(毎日)の場合
        else if (CommonConstants.YOUBI_1.equals(serviceInfo.getYoubi())) {
            hokenSvInfo.setDmyWeek(CommonConstants.EVERYDAY_STR);
        }
        // ⅰ-3. 処理行.曜日が「"9999999"」(週単位以外)の場合
        else if (CommonConstants.YOUBI_9 == serviceInfo.getYoubi()) {
            String igaKbn = CommonDtoUtil.objValToString(serviceInfo.getIgaiKbn());
            // ⅰ-3-1. 処理行.週単位以外のｻｰﾋﾞｽ区分が「"1"」(日付指定)の場合
            if (CommonConstants.IGAI_KBN_STR_ONE == igaKbn) {
                String dateMoji = kghKrkZCpnFuncLogic.getWeekDateToMoji(serviceInfo.getIgaiDate());
                hokenSvInfo.setDmyWeek(dateMoji);
            }
            // ⅰ-3-2. 処理行.週単位以外のｻｰﾋﾞｽ区分が「"2"」(曜日指定)の場合
            else if (CommonConstants.IGAI_KBN_STR_TWO == igaKbn) {
                String weekMoji = kghKrkZCpnFuncLogic.getWeekToMoji(serviceInfo.getIgaiWeek());
                hokenSvInfo.setDmyWeek(weekMoji);
            }
        }
        // ⅰ-4. 処理行.曜日が上記以外（週単位）場合
        else {
            hokenSvInfo.setDmyWeek(formatYoubiString(serviceInfo.getYoubi()));
        }
    }

    /**
     * 週○回/月○回用の曜日編集
     * 
     * @param serviceInfo 計画書（２）サービス情報
     * @param hokenSvInfo 保険サービス情報
     */
    private void editYoubiForWeeklyMonthly(Keikaku2ServiceInfoDmyOutEntity serviceInfo, Gui01041HokenSv hokenSvInfo) {
        String igaiKbn = CommonDtoUtil.objValToString(serviceInfo.getIgaiKbn());
        // ⅱ-1. 処理行.週単位以外のｻｰﾋﾞｽ区分が「"1"」の場合
        if (CommonConstants.IGAI_KBN_STR_ONE.equals(igaiKbn)) {
            int count = countNonEmptyChars(serviceInfo.getIgaiDate());
            hokenSvInfo.setDmyWeek(CommonConstants.MONTH_STR + count + CommonConstants.TIMES_STR);
        }
        // ⅱ-2. 処理行.週単位以外のｻｰﾋﾞｽ区分が「"2"」の場合
        else if (CommonConstants.IGAI_KBN_STR_TWO.equals(igaiKbn)) {
            int count = countNonEmptyChars(serviceInfo.getIgaiWeek());
            hokenSvInfo.setDmyWeek(CommonConstants.MONTH_STR + count + CommonConstants.TIMES_STR);
        }
        // ⅱ-3. 処理行.週単位以外のｻｰﾋﾞｽ区分が上記以外の場合
        else {
            int count = countNonEmptyChars(serviceInfo.getYoubi());
            hokenSvInfo.setDmyWeek(CommonConstants.WEEK_STR + count + CommonConstants.TIMES_STR);
        }
    }

    /**
     * 曜日文字列をフォーマットする（例：1010100 → 月・水・金）
     */
    private String formatYoubiString(String youbi) {
        if (youbi == null || youbi.length() != 7) {
            return "";
        }

        String[] youbiNames = { "月", "火", "水", "木", "金", "土", "日" };
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < 7; i++) {
            if (youbi.charAt(i) == '1') {
                if (result.length() > 0) {
                    result.append("・");
                }
                result.append(youbiNames[i]);
            }
        }

        return result.toString();
    }

    /**
     * 文字列中の非空文字をカウント
     */
    private int countNonEmptyChars(String str) {
        if (str == null) {
            return 0;
        }
        return (int) str.chars().filter(c -> c != ' ' && c != '0').count();
    }

    /**
     * string項目TYPEに変換
     * 
     * @param item string項目
     * @return integer項目
     */
    private Integer stringToInteger(String item) {
        if (item != null) {
            return Integer.valueOf(item);
        } else {
            return null;
        }
    }

    /**
     * null安全な文字列変換
     * 
     * @param value object項目
     * @return string項目
     */
    private String toStringSafe(Object value) {
        return Optional.ofNullable(value).map(Object::toString).orElse(StringUtils.EMPTY);
    }

    /**
     * 数値かどうか判定
     */
    private boolean isNumeric(String str) {
        if (str == null) {
            return false;
        }
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
