package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.AssessmentHomeLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeSaveServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeTab62InsertServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeTab62InsertServiceInDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kan12H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kan12R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdlRirekiMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan12H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan12R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kan12R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRireki;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRirekiCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kan12H21Criteria;
import jp.ndsoft.smh.framework.global.base.service.InsertServiceImpl;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * @since 2025.03.25
 * <AUTHOR>
 * @description GUI00800_［アセスメント］画面（居宅）（6②） データ保存
 */
@Service
public class AssessmentHomeTab62InsertServiceImpl
        extends InsertServiceImpl<AssessmentHomeTab62InsertServiceInDto, AssessmentHomeTab62InsertServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** ＧＬ＿居宅アセスメント履歴テーブルの登録詳細DAO */
    @Autowired
    private CpnTucGdlRirekiMapper cpnTucGdlRirekiMapper;
    /** ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）の登録詳細DAO */
    @Autowired
    private CpnTucGdl4Kan12H21Mapper cpnTucGdl4Kan12H21Mapper;
    /** ＧＬ＿②生活機能（食事・排泄）（R３改訂）の登録詳細DAO */
    @Autowired
    private CpnTucGdl5Kan12R3Mapper cpnTucGdl5Kan12R3Mapper;
    /** ［アセスメント］画面（居宅）画面のロジッククラス */
    @Autowired
    private AssessmentHomeLogic assessmentHomeLogic;

    @Autowired
    private PlatformTransactionManager transactionManager;

    /**
     * ［アセスメント］画面（居宅）（6②） データ保存する
     * 
     * @param inDto アセスメント込データ保存入力DTO
     * @return アセスメントデータ保存出力DTO
     */
    @Override
    protected AssessmentHomeTab62InsertServiceOutDto mainProcess(AssessmentHomeTab62InsertServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        AssessmentHomeTab62InsertServiceOutDto outDto = new AssessmentHomeTab62InsertServiceOutDto();
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        /*
         * ===============2. バリデーションチェックを行う。===============
         * 
         */
        boolean resFlg = assessmentHomeLogic.documentPerformValidationCheck(loginDto);
        // 2.2.4. 【変数】.処理結果が「false：失敗」の場合
        if (!resFlg) {
            // 2.2.4. 【変数】.処理結果が「false：失敗」の場合、下記返却する情報を設定して、後続処理を飛ばして、API処理を正常終了とする。
            // レスポンス.計画期間ID ： リクエストパラメータ.計画期間ID
            outDto.setSc1Id(loginDto.getSc1Id());
            // レスポンス.アセスメントID ： リクエストパラメータ.アセスメントID
            outDto.setGdlId(loginDto.getGdlId());
            // レスポンス.エラー区分: "1
            outDto.setErrKbn(CommonConstants.E_DOC_CHECK_ERR);
            return outDto;
        }
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transactionB = transactionManager.getTransaction(def);
        try {
            // ［アセスメント］画面（居宅）（6②） データ保存
            outDto = mainProcessMealUpdate(inDto);
            transactionManager.commit(transactionB);
        } catch (Exception e) {
            transactionManager.rollback(transactionB);
            throw e;
        }

        // e-文書_ケアチェック表１のリクエストパラメータ
        TransactionStatus transactionC = transactionManager.getTransaction(def);
        try {
            mainProcessEdocOut(inDto, outDto);
            transactionManager.commit(transactionC);
        } catch (Exception e) {
            transactionManager.rollback(transactionC);
            throw e;
        }

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * API定義 (e文書出力)
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected void mainProcessEdocOut(
            AssessmentHomeTab62InsertServiceInDto inDto, AssessmentHomeTab62InsertServiceOutDto outDto)
            throws Exception {
        // 入力dtoをLoginDtoにコピーする
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);

        /*
         * ===============e文書出力===============
         * 
         */
        // e文書出力
        String errKbn = assessmentHomeLogic.getPrint(loginDto, outDto.getSc1Id());
        outDto.setErrKbn(errKbn);
    }

    /**
     * ［アセスメント］画面（居宅）（6②） データ保存
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected AssessmentHomeTab62InsertServiceOutDto mainProcessMealUpdate(
            AssessmentHomeTab62InsertServiceInDto inDto)
            throws Exception {
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2.計画対象期間の保存処理================
         * 
         */
        // 戻り情報を設定
        AssessmentHomeTab62InsertServiceOutDto outDto = new AssessmentHomeTab62InsertServiceOutDto();
        // 2.1 リクエストパラメータ.計画対象期間IDがnullの場合、【27-06 記録共通期間】情報を登録する。
        // DAOパラメータを作成
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);

        if (inDto.getSc1Id() == null || StringUtil.isEmpty(inDto.getSc1Id())) {
            // 計画対象期間の保存処理
            this.assessmentHomeLogic.insertKghTucKrkKikan(loginDto);
            // 変数.計画対象期間ID=採番した期間ID
            inDto.setSc1Id(loginDto.getSc1Id());
        }

        /*
         * =====3.リクエストパラメータ.削除処理区分が2:画面を履歴ごと削除するの場合、下記テーブルデータを更新する。==========
         * 
         */
        if (CommonConstants.DELETE_KBN_2.equals(inDto.getDeleteProcessKbn())) {
            assessmentHomeLogic.homeLogicsyuri(loginDto, CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        }
        /*
         * =====4. リクエストパラメータ.削除処理区分が1:画面のみ削除するの場合==========
         * 
         */
        else if (CommonConstants.DELETE_KBN_1.equals(inDto.getDeleteProcessKbn())) {
            // 4.1. リクエストパラメータ.改定フラグが4「H21/４改訂版」の場合、下記対象テーブルのサブ情報をロジック的に削除する。
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                // ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）に削除する。
                this.deleteKan12H21(inDto);

            } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                // 4.2. リクエストパラメータ.改定フラグが5「R3/４改訂版」の場合、下記対象テーブルのサブ情報をロジック的に削除する。
                // ＧＬ＿①基本（身体機能・起居）動作（R３改訂）に削除する。
                this.deleteKan12R3(inDto);
            }

            // 4.3. 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
            this.clearRrk(inDto);

        } else {
            /*
             * ===============5.以外の場合===============
             * 
             */
            // 5.1. 履歴情報の保存処理
            // 5.1.1. リクエストパラメータ.履歴更新区分が"C":新規の場合、【ＧＬ＿居宅アセスメント履歴】情報を登録する。
            if (CommonDtoUtil.isHistoryCreate(inDto)) {
                this.insertRrk(inDto);

            } else if (CommonDtoUtil.isHistoryUpdate(inDto)) {
                // 5.1.2. リクエストパラメータ.履歴更新区分が"U":更新の場合、【ＧＬ＿居宅アセスメント履歴】情報を更新する。
                this.updateRrk(inDto);

            }

            // 5.2. サブ情報の保存処理
            // 5.1. リクエストパラメータ.更新区分が"C":新規の場合、基本動作情報を登録する。
            if (CommonDtoUtil.isCreate(inDto)) {
                // 5.1.1. リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、【ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）】情報を登録する。
                if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                    this.insertKan12H21(inDto);

                } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                    // 5.1.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【ＧＬ＿①基本（身体機能・起居）動作（R３改訂）】情報を登録する。
                    this.insertKan12R3(inDto);
                }
            }
            // 5.2. リクエストパラメータ.更新区分が"U":更新の場合、基本動作情報を更新する。
            else if (CommonDtoUtil.isUpdate(inDto)) {
                // 3.2.1. リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、【ＧＬ＿①基本（身体機能・起居）動作（Ｈ２１改訂）】情報を更新する。
                if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())) {
                    this.updateKan12H21(inDto);

                } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
                    // 3.2.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【ＧＬ＿①基本（身体機能・起居）動作（R３改訂）】情報を更新する。
                    this.updateKan12R3(inDto);
                }
            }

            // 5.3. リクエストパラメータ.【課題と目標リスト】の件数分、【ＧＬ＿課題と目標】情報を保存する。
            assessmentHomeLogic.homeLogicCpnTucGdlKadai(loginDto, CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        }

        /*
         * ===============6. レスポンスを返却===============
         * 
         */
        outDto.setSc1Id(inDto.getSc1Id());
        outDto.setGdlId(inDto.getGdlId());

        return outDto;
    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を登録する。
     * 関数名：insertRrk
     * 
     * @param inDto 更新パラメータ
     */
    private void insertRrk(AssessmentHomeTab62InsertServiceInDto inDto) throws Exception {
        // ＧＬ＿居宅アセスメント履歴パラメータを作成
        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();

        // 計画期間ID
        cpnTucGdlRireki.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // 法人ID
        cpnTucGdlRireki.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        cpnTucGdlRireki.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        cpnTucGdlRireki.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ＩＤ
        cpnTucGdlRireki.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // アセスメント実施日
        cpnTucGdlRireki.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRireki.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // 本人の基本動作等1
        cpnTucGdlRireki.setAss7(CommonConstants.MARU);

        // リクエストパラメータ.改定フラグが4「H21/４改訂版」または5「R3/４改訂版」の場合、CommonConstants.DASHを設定する、以外の場合設定しない
        if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFormF())
                || CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFormF())) {
            // Ｊの状態
            cpnTucGdlRireki.setAss12(CommonConstants.DASH);
            // 本人の基本動作等8
            cpnTucGdlRireki.setAss13(CommonConstants.DASH);
        }

        // 改定フラグ
        cpnTucGdlRireki.setNinteiFormF(CommonDtoUtil.strValToInt(inDto.getNinteiFormF()));

        this.cpnTucGdlRirekiMapper.insertSelectiveAndReturn(cpnTucGdlRireki);

        inDto.setGdlId(CommonDtoUtil.objValToString(cpnTucGdlRireki.getGdlId()));

    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
     * 関数名：updateRrk
     * 
     * @param inDto 更新パラメータ
     */
    private void updateRrk(AssessmentHomeTab62InsertServiceInDto inDto) {
        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();
        // アセスメント実施日
        cpnTucGdlRireki.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRireki.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // 本人の基本動作等1
        cpnTucGdlRireki.setAss7(CommonConstants.MARU);
        // Ｊの状態
        cpnTucGdlRireki.setAss12(CommonConstants.DASH);
        // 本人の基本動作等8
        cpnTucGdlRireki.setAss13(CommonConstants.DASH);

        // ＧＬ＿居宅アセスメント履歴パラメータを作成
        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                // 利用者ＩＤ
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 法人ID
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        // DAOを実行
        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRireki, criteria);

    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
     * 関数名：clearRrk
     * 
     * @param inDto 更新パラメータ
     */
    private void clearRrk(AssessmentHomeTab62InsertServiceInDto inDto) {
        // 削除条件
        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()))
                // 法人ID
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()))
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                // 利用者ＩＤ
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()));

        // DAOを実行
        this.cpnTucGdlRirekiMapper.deleteByCriteria(criteria);

    }

    /**
     * 【ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）】情報を登録する。
     * 関数名：insertKan12H21
     * 
     * @param inDto 更新パラメータ
     */
    private void insertKan12H21(AssessmentHomeTab62InsertServiceInDto inDto) throws Exception {

        // ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）パラメータを作成
        CpnTucGdl4Kan12H21 cpnTucGdl4Kan12H21 = new CpnTucGdl4Kan12H21();

        BeanUtils.copyProperties(cpnTucGdl4Kan12H21, inDto.getLifeFunction4Info());
        // 計画対象期間ID
        cpnTucGdl4Kan12H21.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // 採番されたアセスメントID
        cpnTucGdl4Kan12H21.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));

        // DAOを実行
        this.cpnTucGdl4Kan12H21Mapper.insertSelective(cpnTucGdl4Kan12H21);

    }

    /**
     * 【ＧＬ＿②生活機能（食事・排泄）（R３改訂）】情報を登録する。
     * insertKan12R3
     * 
     * @param inDto 更新パラメータ
     * @return 結果結果
     */
    private void insertKan12R3(AssessmentHomeTab62InsertServiceInDto inDto) throws Exception {
        // 【ＧＬ＿②生活機能（食事・排泄）（R３改訂）】パラメータを作成
        CpnTucGdl5Kan12R3 cpnTucGdl5Kan12R3 = new CpnTucGdl5Kan12R3();

        BeanUtils.copyProperties(cpnTucGdl5Kan12R3, inDto.getLifeFunction5Info());
        // アセスメントID
        cpnTucGdl5Kan12R3.setGdlId(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // 計画期間ID
        cpnTucGdl5Kan12R3.setSc1Id(CommonDtoUtil.strValToInt(inDto.getGdlId()));

        // DAOを実行
        this.cpnTucGdl5Kan12R3Mapper.insertSelective(cpnTucGdl5Kan12R3);

    }

    /**
     * 【ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂】情報を更新する
     * updateKan12H21
     * 
     * @param inDto 更新パラメータ
     * @return 結果結果
     */
    private void updateKan12H21(AssessmentHomeTab62InsertServiceInDto inDto) throws Exception {
        // パラメータを作成
        CpnTucGdl4Kan12H21Criteria criteria = new CpnTucGdl4Kan12H21Criteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        // ＧＬ＿②生活機能（食事・排泄）（Ｈ２１改訂）
        CpnTucGdl4Kan12H21 cpnTucGdl4Kan12H21 = new CpnTucGdl4Kan12H21();
        BeanUtils.copyProperties(cpnTucGdl4Kan12H21, inDto.getLifeFunction4Info());

        // DAOを実行
        this.cpnTucGdl4Kan12H21Mapper.updateByCriteriaSelective(cpnTucGdl4Kan12H21, criteria);

    }

    /**
     * 【ＧＬ＿②生活機能（食事・排泄）（R３改訂）】情報を更新する
     * updateKan12R3
     * 
     * @param inDto 更新パラメータ
     * @return 結果結果
     */
    private void updateKan12R3(AssessmentHomeTab62InsertServiceInDto inDto) throws Exception {
        // パラメータを作成
        CpnTucGdl5Kan12R3Criteria criteria = new CpnTucGdl5Kan12R3Criteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(
                        inDto.getSc1Id()));

        // ＧＬ＿②生活機能（食事・排泄）（R３改訂）
        CpnTucGdl5Kan12R3 cpnTucGdl5Kan12R3 = new CpnTucGdl5Kan12R3();
        BeanUtils.copyProperties(cpnTucGdl5Kan12R3, inDto.getLifeFunction5Info());

        // DAOを実行
        this.cpnTucGdl5Kan12R3Mapper.updateByCriteriaSelective(cpnTucGdl5Kan12R3, criteria);

    }

    /**
     * ＧＬ＿②生活機能（食事・排泄）動作を削除する
     * deleteKan12H21
     * 
     * @param inDto 更新パラメータ
     * @return 結果結果
     */
    private void deleteKan12H21(AssessmentHomeTab62InsertServiceInDto inDto) throws Exception {
        CpnTucGdl4Kan12H21Criteria cpnTucGdl4Kan12H21Criteria = new CpnTucGdl4Kan12H21Criteria();
        cpnTucGdl4Kan12H21Criteria.createCriteria()
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        this.cpnTucGdl4Kan12H21Mapper.deleteByCriteria(cpnTucGdl4Kan12H21Criteria);
    }

    /**
     * ＧＬ＿②生活機能（食事・排泄）（R３改訂）を削除する
     * deleteKan12R3
     * 
     * @param inDto 更新パラメータ
     * @return 結果結果
     */
    private void deleteKan12R3(AssessmentHomeTab62InsertServiceInDto inDto) throws Exception {
        CpnTucGdl5Kan12R3Criteria cpnTucGdl5Kan12R3Criteria = new CpnTucGdl5Kan12R3Criteria();
        cpnTucGdl5Kan12R3Criteria.createCriteria()
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                .andSc1IdEqualTo(CommonDtoUtil.strValToInt(inDto.getSc1Id()));

        this.cpnTucGdl5Kan12R3Mapper.deleteByCriteria(cpnTucGdl5Kan12R3Criteria);
    }
}