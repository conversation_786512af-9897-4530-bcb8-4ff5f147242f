package jp.ndsoft.carebase.cmn.api.report.logic;

import java.io.FileInputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkBase02Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkGlb01Logic;
import jp.ndsoft.carebase.cmn.api.logic.ShokuinInfoLogic;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.JigyoRirekiInfoDto;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.Plan1kReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.model.Plan1kReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.service.ShoninSetReportService;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.AddressKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AddressKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucUserInfo2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucUserInfo2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku1DetailInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Keikaku1DetailInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinHogoJohoSetteiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinHogoJohoSetteiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShoninInfoListByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShoninInfoListOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSysiniSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMstChouhyouInkanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucShoninSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks11SelectMapper;
import jp.ndsoft.smh.framework.properties.FrameworkProperties;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.xml.JRXmlLoader;

/**
 * U0081K_居宅サービス計画書（１） 帳票出力 ロジッククラス
 * 
 * <AUTHOR>
 */
@Component
public class Plan1kReportLogic {

    /** 年 月 日 */
    private static final String STR_EMPTY_YMD = "　　  年  月  日";

    /** "利用者及び家族の" */
    private static final String STR_USER_AND_FAMILY = "利用者及び家族の";

    /** "生活に対する意向" */
    private static final String STR_LIFE_GOAL = "生活に対する意向";

    /** "生活に対する" */
    private static final String STR_LIFE = "生活に対する";

    /** "意向を踏まえた" */
    private static final String STR_GOAL = "意向を踏まえた";

    /** "課題分析の結果" */
    private static final String STR_RESULT = "課題分析の結果";

    /** "介護認定審査会の" */
    private static final String STR_IKEN_TITLE1 = "介護認定審査会の";

    /** "意見及びサービス" */
    private static final String STR_IKEN_TITLE2 = "意見及びサービス";

    /** "の種類の指定" */
    private static final String STR_IKEN_TITLE3 = "の種類の指定";

    /** "総合的な援助の" */
    private static final String STR_HOUSHIN_TITLE1 = "総合的な援助の";

    /** "方針" */
    private static final String STR_HOUSHIN_TITLE2 = "方針";

    /** "birthday" */
    private static final String STR_BIRTHDAY = "birthday";

    /** "address" */
    private static final String STR_ADDRESS = "address";

    // 利用者基本（１－６）情報取得
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;

    // 28-xx ケアプラン帳票印鑑欄情報取得
    @Autowired
    private CpnMstChouhyouInkanSelectMapper cpnMstChouhyouInkanSelectMapper;

    // 52-01 （全体）設定情報テーブルを取得
    @Autowired
    private ComMocSysiniSelectMapper comMocSysiniSelectMapper;

    // 計画書（１）情報取得
    @Autowired
    private CpnTucCks11SelectMapper cpnTucCks11SelectMapper;

    // 支援事業者所在地情報取得
    @Autowired
    private ComMscSvjigyoSelectMapper comMscSvjigyoSelectMapper;

    // 承認欄マスタ情報一覧 取得
    @Autowired
    private CpnMucShoninSelectMapper cpnMucShoninSelectMapper;

    // 桜十字グループ様のユーザ判別用
    @Autowired
    private Nds3GkGlb01Logic nds3GkGlb01Logic;

    // 設定の書込（NEXのパソコン単位の設定）
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    // 西暦日付を和暦日付に変換
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    // 伏字にする
    @Autowired
    private Nds3GkBase02Logic nds3GkBase02Logic;

    // 03-01 職員基本情報取得
    @Autowired
    private ShokuinInfoLogic shokuinInfoLogic;

    // 事業履歴より事業所名を取得する
    @Autowired
    private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;

    // 承認欄設定
    @Autowired
    private ShoninSetReportService shoninSetReportService;

    /**
     * U0081K_居宅サービス計画書（１）の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータ
     */
    public Plan1kReportServiceInDto getU0081KReportParameters(Plan1kReportParameterModel inDto) {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));
        // 表示用データの取得
        Plan1kReportServiceInDto model = new Plan1kReportServiceInDto();

        // リクエストパラメータ.データ.印刷設定.パラメータ03が「1:変更する」の場合
        if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam03())) {
            // リクエストパラメータ.データ.印刷設定.パラメータ04
            model.setKeisho(inDto.getPrintSet().getParam04());
        } else if (CommonConstants.STR_1.equals(inDto.getInitMasterObj().getKeishoFlg())) {
            // リクエストパラメータ.データ.初期設定マスタの情報.敬称オプションが「1:変更する」の場合
            // リクエストパラメータ.データ.初期設定マスタの情報.敬称
            model.setKeisho(inDto.getInitMasterObj().getKeishoKnj());
        } else {
            // "殿"
            model.setKeisho(CommonConstants.KEISHO_STR_TONO);
        }
        // 計画書（１）情報の取得処理
        Keikaku1DetailInfoByCriteriaInEntity keikaku1DetailInfoByCriteriaInEntity = new Keikaku1DetailInfoByCriteriaInEntity();
        // リクエストパラメータ.データ.印刷対象履歴.履歴ID
        keikaku1DetailInfoByCriteriaInEntity
                .setKs11(CommonDtoUtil.strValToInt(inDto.getPrintSubjectHistory().getRirekiId()));
        List<Keikaku1DetailInfoOutEntity> keikaku1DetailInfoOutEntities = cpnTucCks11SelectMapper
                .findKeikaku1DetailInfoByCriteria(keikaku1DetailInfoByCriteriaInEntity);

        // リクエストパラメータ.DB未保存画面項目.記入用シートを印刷するフラグが「1:印刷する」の場合
        if (ReportConstants.EMPTY_FLG_TRUE.equals(inDto.getDbNoSaveData().getEmptyFlg())) {
            // 記入用シート帳票を表示の場合：""
            model.setKojinFlg(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：「0」
            model.setAutoFlg(0);
            // 記入用シート帳票を表示の場合：""
            model.setSakuraUserInfo(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：「0:非表示」
            model.setZanteiFlg(0);
            // 記入用シート帳票を表示の場合：""
            model.setShiTeiDate(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：" 年 月 日"
            model.setCreateYmd(STR_EMPTY_YMD);
            // 記入用シート帳票を表示の場合：「0:非表示」
            model.setHyoujiKbn(0);
            // 記入用シート帳票を表示の場合：""
            model.setHanko1Knj(CommonConstants.BLANK_STRING);
            model.setHanko2Knj(CommonConstants.BLANK_STRING);
            model.setHanko3Knj(CommonConstants.BLANK_STRING);
            model.setHanko4Knj(CommonConstants.BLANK_STRING);
            model.setHanko5Knj(CommonConstants.BLANK_STRING);
            model.setHanko6Knj(CommonConstants.BLANK_STRING);
            model.setHanko7Knj(CommonConstants.BLANK_STRING);
            model.setHanko8Knj(CommonConstants.BLANK_STRING);
            model.setHanko9Knj(CommonConstants.BLANK_STRING);
            model.setHanko10Knj(CommonConstants.BLANK_STRING);
            model.setHanko11Knj(CommonConstants.BLANK_STRING);
            model.setHanko12Knj(CommonConstants.BLANK_STRING);
            model.setHanko13Knj(CommonConstants.BLANK_STRING);
            model.setHanko14Knj(CommonConstants.BLANK_STRING);
            model.setHanko15Knj(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：0
            model.setKubun(0);
            // 記入用シート帳票を表示の場合：0
            model.setNintei(0);
            // 記入用シート帳票を表示の場合：""
            model.setRiyousyaNm(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：""
            model.setBirthday(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：""
            model.setAddress(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：""
            model.setShokuinNm(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：0
            model.setTantouNm(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：""
            model.setJigyoushaNm(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：""
            model.setJigyouAddr(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：""
            model.setServicePlanCreateDate(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：""
            model.setShokaiServicePlanCreateDate(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：""
            model.setNinteiDate(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：""
            model.setYukoSYmd(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：""
            model.setYukoEYmd(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：0
            model.setShienLabelShowFlg(0);
            // 記入用シート帳票を表示の場合：0
            model.setYokaiKbn(0);
            // 記入用シート帳票を表示の場合：0
            model.setMikomiFlg(0);
            // API定義の処理「2.2」で取得した「作成日」 < "2021/04/01"の場合
            if (keikaku1DetailInfoOutEntities != null && keikaku1DetailInfoOutEntities.size() > 0 && LocalDate
                    .parse(keikaku1DetailInfoOutEntities.get(0).getCreateYmd(),
                            DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD))
                    .compareTo(LocalDate.parse(CommonConstants.STR_NUM_2021_04_01,
                            DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD))) < 0) {
                model.setIkouTitle(CommonConstants.STR_LINE_BREAK.concat(CommonConstants.STR_LINE_BREAK).concat(
                        STR_USER_AND_FAMILY).concat(CommonConstants.STR_LINE_BREAK)
                        .concat(CommonConstants.STR_LINE_BREAK)
                        .concat(
                                STR_LIFE_GOAL));
            } else {
                // 上記以外の場合
                model.setIkouTitle(CommonConstants.STR_LINE_BREAK.concat(
                        STR_USER_AND_FAMILY).concat(CommonConstants.STR_LINE_BREAK)
                        .concat(CommonConstants.STR_LINE_BREAK)
                        .concat(
                                STR_LIFE)
                        .concat(CommonConstants.STR_LINE_BREAK).concat(CommonConstants.STR_LINE_BREAK).concat(
                                STR_GOAL)
                        .concat(CommonConstants.STR_LINE_BREAK).concat(CommonConstants.STR_LINE_BREAK).concat(
                                STR_RESULT));
            }
            // 記入用シート帳票を表示の場合：""
            model.setIkou(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：""
            model.setIken(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：""
            model.setHoushin(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：""
            model.setKajiCode(CommonConstants.BLANK_STRING);
            // 記入用シート帳票を表示の場合：""
            model.setKajiSonotaKnj(CommonConstants.BLANK_STRING);
        } else {
            // 居宅サービス計画書（１）情報を取得
            // 桜十字グループ様の利用者基本情報の設定処理
            // 共通関数補足「1. 桜十字グループ様フラグを取得」を参照する
            boolean kghskrjFlg = nds3GkGlb01Logic.getKghSkrjFlg();
            // 上記処理4.1.1の戻り値が「true」の場合、桜十字グループ様の利用者基本情報を取得する
            if (kghskrjFlg) {
                F3gkGetProfileInDto f3gkGetProfileInDto = new F3gkGetProfileInDto();
                // 職員ＩＤ
                f3gkGetProfileInDto.setShokuId(0);
                // 法人ＩＤ
                f3gkGetProfileInDto.setHoujinId(0);
                // 施設ＩＤ
                f3gkGetProfileInDto.setShisetuId(0);
                // 事業所ＩＤ
                f3gkGetProfileInDto.setSvJigyoId(0);
                // 機能名
                f3gkGetProfileInDto.setKinounameKnj("初期設定マスタ");
                // セクション
                f3gkGetProfileInDto.setSectionKnj("全体");
                // キー
                f3gkGetProfileInDto.setKeyKnj("HEADER_SAIKEI");
                // パラメータ
                f3gkGetProfileInDto.setAsDefault(CommonConstants.STR_1);
                // システムコード
                f3gkGetProfileInDto.setGsyscd(inDto.getDbNoSaveData().getSysCd());
                String f3gkProfile = nds3GkFunc01Logic.getF3gkProfile(f3gkGetProfileInDto);
                // 上記取得した「パラメータ」が「1」の場合
                if (CommonConstants.STR_1.equals(f3gkProfile)) {
                    ComTucUserInfo2ByCriteriaInEntity comTucUserInfo2ByCriteriaInEntity = new ComTucUserInfo2ByCriteriaInEntity();
                    comTucUserInfo2ByCriteriaInEntity
                            .setLlUid(inDto.getPrintSubjectHistory().getUserId());
                    List<ComTucUserInfo2OutEntity> comTucUserInfo2OutEntities = comTucUserSelectMapper
                            .findComTucUserInfo2ByCriteria(comTucUserInfo2ByCriteriaInEntity);
                    StringBuilder sb = new StringBuilder();
                    // ①で取得した「氏名（姓）」+「氏名（名）」
                    if (comTucUserInfo2OutEntities.size() > 0) {
                        sb.append(comTucUserInfo2OutEntities.get(0).getName1Knj());
                        sb.append(comTucUserInfo2OutEntities.get(0).getName2Knj());
                        sb.append(CommonConstants.SPACE_STRING);
                        sb.append(CommonConstants.SPACE_STRING);
                    }
                    // 処理「2」で取得した「敬称」
                    sb.append(model.getKeisho());
                    sb.append(CommonConstants.SPACE_STRING);
                    sb.append(CommonConstants.SPACE_STRING);
                    if (comTucUserInfo2OutEntities.size() > 0) {
                        // ※「1」の場合："男"、「0」の場合："女"
                        sb.append(comTucUserInfo2OutEntities.get(0).getSex() == 1 ? CommonConstants.STR_MALE
                                : CommonConstants.STR_FEMALE);
                        sb.append(CommonConstants.SPACE_STRING);
                        sb.append(CommonConstants.SPACE_STRING);
                        // ①で取得した「生年月日」
                        sb.append(nds3GkFunc01Logic.get2Gengouj(1,
                                CommonDtoUtil.strNullToEmpty(comTucUserInfo2OutEntities.get(0).getBirthdayYmd())));
                    }
                    // 桜十字グループ様の利用者基本情報
                    model.setSakuraUserInfo(sb.toString());
                }
            }
            // 印鑑欄情報の取得処理
            KghCpnMstChouhyouInkanPrnByCriteriaInEntity kghCpnMstChouhyouInkanPrnByCriteriaInEntity = new KghCpnMstChouhyouInkanPrnByCriteriaInEntity();
            // リクエストパラメータ.事業者情報.法人ID
            kghCpnMstChouhyouInkanPrnByCriteriaInEntity
                    .setAnKey1(CommonDtoUtil.strValToInt(inDto.getJigyoInfo().getHoujinId()));
            // リクエストパラメータ.事業者情報.施設ID
            kghCpnMstChouhyouInkanPrnByCriteriaInEntity
                    .setAnKey2(CommonDtoUtil.strValToInt(inDto.getJigyoInfo().getShisetuId()));
            // リクエストパラメータ.事業者情報.サービス事業者ID
            kghCpnMstChouhyouInkanPrnByCriteriaInEntity.setAnKey3(
                    CommonDtoUtil.strValToInt(inDto.getJigyoInfo().getSvJigyoId()));
            // "3GKU0081"
            kghCpnMstChouhyouInkanPrnByCriteriaInEntity.setAsSec("3GKU0081");

            List<KghCpnMstChouhyouInkanPrnOutEntity> chouhyouInkanPrnOutEntities = cpnMstChouhyouInkanSelectMapper
                    .findKghCpnMstChouhyouInkanPrnByCriteria(
                            kghCpnMstChouhyouInkanPrnByCriteriaInEntity);
            // 検索結果がある、且つ、検索結果.印鑑欄表示区分が"1"の場合：
            if (chouhyouInkanPrnOutEntities != null && chouhyouInkanPrnOutEntities.size() > 0
                    && chouhyouInkanPrnOutEntities.get(0).getHyoujiKbn() == 1) {
                // 帳票用データ詳細.印鑑欄表示フラグ
                model.setHyoujiKbn(chouhyouInkanPrnOutEntities.get(0).getHyoujiKbn() != null
                        ? (int) chouhyouInkanPrnOutEntities.get(0).getHyoujiKbn()
                        : null);
                // 帳票用データ詳細.印鑑欄情報に検索結果を設定する
                model.setHanko1Knj(chouhyouInkanPrnOutEntities.get(0).getHanko1Knj());
                model.setHanko2Knj(chouhyouInkanPrnOutEntities.get(0).getHanko2Knj());
                model.setHanko3Knj(chouhyouInkanPrnOutEntities.get(0).getHanko3Knj());
                model.setHanko4Knj(chouhyouInkanPrnOutEntities.get(0).getHanko4Knj());
                model.setHanko5Knj(chouhyouInkanPrnOutEntities.get(0).getHanko5Knj());
                model.setHanko6Knj(chouhyouInkanPrnOutEntities.get(0).getHanko6Knj());
                model.setHanko7Knj(chouhyouInkanPrnOutEntities.get(0).getHanko7Knj());
                model.setHanko8Knj(chouhyouInkanPrnOutEntities.get(0).getHanko8Knj());
                model.setHanko9Knj(chouhyouInkanPrnOutEntities.get(0).getHanko9Knj());
                model.setHanko10Knj(chouhyouInkanPrnOutEntities.get(0).getHanko10Knj());
                model.setHanko11Knj(chouhyouInkanPrnOutEntities.get(0).getHanko11Knj());
                model.setHanko12Knj(chouhyouInkanPrnOutEntities.get(0).getHanko12Knj());
                model.setHanko13Knj(chouhyouInkanPrnOutEntities.get(0).getHanko13Knj());
                model.setHanko14Knj(chouhyouInkanPrnOutEntities.get(0).getHanko14Knj());
                model.setHanko15Knj(chouhyouInkanPrnOutEntities.get(0).getHanko15Knj());
            } else {
                model.setHyoujiKbn(0);
                // 帳票用データ詳細.印鑑1～15
                model.setHanko1Knj(CommonConstants.BLANK_STRING);
                model.setHanko2Knj(CommonConstants.BLANK_STRING);
                model.setHanko3Knj(CommonConstants.BLANK_STRING);
                model.setHanko4Knj(CommonConstants.BLANK_STRING);
                model.setHanko5Knj(CommonConstants.BLANK_STRING);
                model.setHanko6Knj(CommonConstants.BLANK_STRING);
                model.setHanko7Knj(CommonConstants.BLANK_STRING);
                model.setHanko8Knj(CommonConstants.BLANK_STRING);
                model.setHanko9Knj(CommonConstants.BLANK_STRING);
                model.setHanko10Knj(CommonConstants.BLANK_STRING);
                model.setHanko11Knj(CommonConstants.BLANK_STRING);
                model.setHanko12Knj(CommonConstants.BLANK_STRING);
                model.setHanko13Knj(CommonConstants.BLANK_STRING);
                model.setHanko14Knj(CommonConstants.BLANK_STRING);
                model.setHanko15Knj(CommonConstants.BLANK_STRING);
            }
            // 個人情報保護について情報を取得
            List<KojinHogoJohoSetteiOutEntity> kojinHogoJohoSetteiOutEntities = comMocSysiniSelectMapper
                    .findKojinHogoJohoSetteiByCriteria(new KojinHogoJohoSetteiByCriteriaInEntity());
            // 上記検索結果がある、且つ、検索結果.値が「１」の場合
            if (kojinHogoJohoSetteiOutEntities != null && kojinHogoJohoSetteiOutEntities.size() > 0
                    && CommonConstants.STR_1.equals(kojinHogoJohoSetteiOutEntities.get(0).getNewParam())) {
                // 個人情報表示フラグ
                model.setKojinShowFlg(CommonConstants.STR_1);

                // 個人情報保護フラグを取得
                F3gkGetProfileInDto f3gkGetProfileInDto = new F3gkGetProfileInDto();
                // リクエストパラメータ.職員ID
                f3gkGetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(inDto.getDbNoSaveData().getShokuinId()));
                // 0
                f3gkGetProfileInDto.setHoujinId(0);
                // 0
                f3gkGetProfileInDto.setShisetuId(0);
                // 0
                f3gkGetProfileInDto.setSvJigyoId(0);
                // ”prt"
                f3gkGetProfileInDto.setKinounameKnj(CommonConstants.PRT_STRING);
                // プロファイル
                f3gkGetProfileInDto.setSectionKnj(inDto.getPrintSet().getProfile());
                // "kojinhogo_flg"
                f3gkGetProfileInDto.setKeyKnj(CommonConstants.S_KOJINHOGO_FLG);
                // 1
                f3gkGetProfileInDto.setAsDefault(CommonConstants.STR_1);
                // リクエストパラメータ.データ.DB未保存画面項目.システムコード
                f3gkGetProfileInDto.setGsyscd(inDto.getDbNoSaveData().getSysCd());
                model.setKojinFlg(nds3GkFunc01Logic.getF3gkProfile(f3gkGetProfileInDto));
            } else {
                model.setKojinShowFlg(CommonConstants.STR_0);
                model.setKojinFlg(CommonConstants.STR_0);
            }

            // 上記4.4の結果があるの場合
            if (keikaku1DetailInfoOutEntities != null && keikaku1DetailInfoOutEntities.size() > 0) {
                // 利用者情報の取得処理
                RiyoshaKihonByCriteriaInEntity riyoshaKihonByCriteriaInEntity = new RiyoshaKihonByCriteriaInEntity();
                riyoshaKihonByCriteriaInEntity
                        .setAlUserid(CommonDtoUtil.strValToInt(inDto.getPrintSubjectHistory().getUserId()));
                List<RiyoshaKihonOutEntity> riyoshaKihonOutEntities = comTucUserSelectMapper
                        .findRiyoshaKihonByCriteria(riyoshaKihonByCriteriaInEntity);
                // 検索結果があるの場合
                if (riyoshaKihonOutEntities != null && riyoshaKihonOutEntities.size() > 0) {
                    // 上記取得した「氏名（姓）」 + ' ' + 「氏名（名）」
                    String name1Knj = riyoshaKihonOutEntities.get(0).getName1Knj();
                    String name2Knj = riyoshaKihonOutEntities.get(0).getName2Knj();
                    StringBuilder sb = new StringBuilder();
                    if (!StringUtils.isEmpty(name1Knj)) {
                        sb.append(name1Knj);
                    }
                    // ※「氏名（姓）」が空、又は、「氏名（名）」が空の場合、' 'が不要
                    if (!StringUtils.isEmpty(name1Knj) && !StringUtils.isEmpty(name2Knj)) {
                        sb.append(CommonConstants.BLANK_SPACE);
                    }
                    if (!StringUtils.isEmpty(name2Knj)) {
                        sb.append(name2Knj);
                    }
                    // 利用者名
                    model.setRiyousyaNm(sb.toString());
                    // 生年月日
                    model.setBirthday(
                            nds3GkFunc01Logic.get2Gengouj(1,
                                    CommonDtoUtil.strNullToEmpty(riyoshaKihonOutEntities.get(0).getBirthdayYmd())));
                    // 住所
                    model.setAddress(kghCmpF01Logic.cnvkaigyo(riyoshaKihonOutEntities.get(0).getAddressKnj()));
                    // API定義の処理「4.3.2」で取得した「個人情報保護フラグ」が「1：個人情報保護」の場合：
                    if (CommonConstants.STR_1.equals(model.getKojinFlg())) {
                        // 共通関数補足の「7. 利用者名置換処理」を調用して、戻り値「利用者名代替データ」を設定する
                        model.setRiyousyaNm(
                                nds3GkBase02Logic.getKojinhogoNokado(
                                        CommonDtoUtil.strValToInt(inDto.getPrintSubjectHistory().getUserId())));
                        // API定義の処理「4.3.2」で取得した「個人情報保護フラグ」が「1：個人情報保護」の場合：""
                        model.setBirthday(CommonConstants.BLANK_STRING);
                        // 住所
                        model.setAddress(CommonConstants.BLANK_STRING);
                        // API定義の処理「4.3.2」で取得した「個人情報保護フラグ」が「1：個人情報保護」の場合：0
                        model.setYokaiKbn(0);
                        // API定義の処理「4.3.2」で取得した「個人情報保護フラグ」が「1：個人情報保護」の場合：0
                        model.setMikomiFlg(0);
                    } else if (CommonConstants.STR_0.equals(model.getKojinShowFlg())) {
                        // API定義の処理「4.3.1」で取得した「個人情報表示フラグ」が「0」の場合、
                        // 共通関数補足の「6. 伏字処理」を調用して、設定する
                        // 利用者名
                        getFuseJi(inDto, model, ReportConstants.STRING_NAME_KNJ, "riyousya_nm", 0);
                        // 生年月日
                        getFuseJi(inDto, model, STR_BIRTHDAY, STR_BIRTHDAY, 0);
                        // 住所
                        getFuseJi(inDto, model, STR_ADDRESS, STR_ADDRESS, 0);
                        // API定義の処理「4.4」に取得した「要介護度」
                        model.setYokaiKbn(keikaku1DetailInfoOutEntities.get(0).getYokaiKbn());
                        // API定義の処理「4.4」に取得した「要介護見込みフラグ」
                        model.setMikomiFlg(keikaku1DetailInfoOutEntities.get(0).getMikomiFlg() != null
                                ? (int) keikaku1DetailInfoOutEntities.get(0).getMikomiFlg()
                                : null);
                    }
                } else {
                    // 利用者名
                    model.setRiyousyaNm(CommonConstants.BLANK_STRING);
                    // 生年月日
                    model.setBirthday(CommonConstants.BLANK_STRING);
                    // 住所
                    model.setAddress(CommonConstants.BLANK_STRING);
                }
                // 職員名情報の取得処理
                model.setShokuinNm(shokuinInfoLogic.getShokuNameKnj(
                        CommonDtoUtil.objValToString(keikaku1DetailInfoOutEntities.get(0).getShokuId())));
                // 要支援などラベル表示フラグの設定処理
                // 要支援などラベル表示フラグに「0」を初期化する
                model.setShienLabelShowFlg(0);
                String createYmd = keikaku1DetailInfoOutEntities.get(0).getCreateYmd();
                // 処理「4.4」で取得した「作成日」 < "2006/04/01"
                if (createYmd.compareTo(CommonConstants.YMD_20060401) < 0) {
                    // 1：ラベル「要支援」表示
                    model.setShienLabelShowFlg(1);
                } else {
                    // リクエストパラメータ.データ.印刷設定.パラメータ08が「1」の場合
                    if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam08())) {
                        int yokaiKbn = keikaku1DetailInfoOutEntities.get(0).getYokaiKbn();
                        // 処理「4.4」で取得した「要介護度」の判断
                        if (yokaiKbn == 2) {
                            model.setShienLabelShowFlg(2);
                        } else if (yokaiKbn == 11) {
                            // 「11」の場合：
                            model.setShienLabelShowFlg(3);
                        } else if (yokaiKbn == 12) {
                            // 「12」の場合：
                            model.setShienLabelShowFlg(4);
                        } else if (yokaiKbn == 21) {
                            // 「21」の場合：
                            model.setShienLabelShowFlg(5);
                        }
                    }
                }
                // 事業所名及び所在地の取得処理
                JigyoRirekiInfoDto jigyoRirekiInfoDto = kghKrkZCpnFuncLogic.getJigyoRirekiKnj(
                        CommonDtoUtil.strValToInt(inDto.getJigyoInfo().getSvJigyoId()),
                        createYmd);
                // リクエストパラメータ.データ.印刷設定.パラメータ07が「1：事業名略称印刷」の場合
                if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam07())) {
                    // ①で取得した「事業名称略称」
                    model.setJigyoushaNm(jigyoRirekiInfoDto.getJigyoRirekiRyakuKnj());
                } else {
                    // ①で取得した「事業名称正式」
                    model.setJigyoushaNm(jigyoRirekiInfoDto.getJigyoRirekiKnj());
                }
                // 事業者所在地の取得処理
                AddressKnjByCriteriaInEntity addressKnjByCriteriaInEntity = new AddressKnjByCriteriaInEntity();
                // リクエストパラメータ.事業者情報.サービス事業者ID
                addressKnjByCriteriaInEntity.setAlJigyo(inDto.getJigyoInfo().getSvJigyoId());
                List<AddressKnjOutEntity> addressKnjOutEntities = comMscSvjigyoSelectMapper
                        .findAddressKnjByCriteria(addressKnjByCriteriaInEntity);
                if (addressKnjOutEntities != null && addressKnjOutEntities.size() > 0) {
                    model.setJigyouAddr(addressKnjOutEntities.get(0).getAddressKnj());
                } else {
                    model.setJigyouAddr(CommonConstants.BLANK_STRING);
                }
                // 担当者名の取得処理
                model.setTantouNm(shokuinInfoLogic.getShokuNameKnj(
                        CommonDtoUtil.objValToString(keikaku1DetailInfoOutEntities.get(0).getTantoId())));

                // 処理「4.3.1」で取得した「個人情報表示フラグ」が「0：「氏名等を伏字にする」表示」の場合
                if (CommonConstants.STR_0.equals(model.getKojinShowFlg())) {
                    // 事業名称
                    getFuseJi(inDto, model, ReportConstants.STRING_NAME_KNJ, "jigyousha_nm", 0);
                    // 事業者所在地
                    getFuseJi(inDto, model, STR_ADDRESS, "jigyo_addr", 0);
                    // 職員氏名
                    getFuseJi(inDto, model, ReportConstants.STRING_NAME_KNJ, "shokuin_nm", 1);
                    // 担当氏名
                    getFuseJi(inDto, model, ReportConstants.STRING_NAME_KNJ, "tantou_nm", 1);
                }

                // API定義の処理「4.4」で取得した「暫定フラグ」が「1」の場合：「1:表示」
                model.setZanteiFlg(keikaku1DetailInfoOutEntities.get(0).getZanteiFlg() != null
                        && keikaku1DetailInfoOutEntities.get(0).getZanteiFlg() == 1 ? 1 : 0);
                // API定義の処理「4.4」に取得した「計画区分」を参照
                model.setKubun(keikaku1DetailInfoOutEntities.get(0).getKubun() != null
                        ? (int) keikaku1DetailInfoOutEntities.get(0).getKubun()
                        : null);
                // API定義の処理「4.4」に取得した「認定区分」を参照
                model.setNintei(keikaku1DetailInfoOutEntities.get(0).getNintei() != null
                        ? (int) keikaku1DetailInfoOutEntities.get(0).getNintei()
                        : null);

                // API定義の処理「4.4」に取得した「作成日」
                model.setServicePlanCreateDate(
                        nds3GkFunc01Logic.get2Gengouj(1,
                                CommonDtoUtil.strNullToEmpty(keikaku1DetailInfoOutEntities.get(0).getCreateYmd())));
                // API定義の処理「4.4」に取得した「初回作成日」
                model.setShokaiServicePlanCreateDate(
                        nds3GkFunc01Logic.get2Gengouj(1,
                                CommonDtoUtil.strNullToEmpty(keikaku1DetailInfoOutEntities.get(0).getShokaiYmd())));
                // API定義の処理「4.4」に取得した「認定日」
                model.setNinteiDate(
                        nds3GkFunc01Logic.get2Gengouj(1,
                                CommonDtoUtil.strNullToEmpty(keikaku1DetailInfoOutEntities.get(0).getNinteiYmd())));
                // API定義の処理「4.4」に取得した「有効開始日」
                model.setYukoSYmd(
                        nds3GkFunc01Logic.get2Gengouj(1,
                                CommonDtoUtil.strNullToEmpty(keikaku1DetailInfoOutEntities.get(0).getYukoSYmd())));
                // API定義の処理「4.4」に取得した「有効終了日」
                model.setYukoEYmd(
                        nds3GkFunc01Logic.get2Gengouj(1,
                                CommonDtoUtil.strNullToEmpty(keikaku1DetailInfoOutEntities.get(0).getYukoEYmd())));
                // API定義の処理「4.4」に取得した「意向」
                model.setIkou(keikaku1DetailInfoOutEntities.get(0).getIkouKnj());
                // API定義の処理「4.4」に取得した「意見」
                model.setIken(keikaku1DetailInfoOutEntities.get(0).getIkenKnj());
                // API定義の処理「4.4」に取得した「方針」
                model.setHoushin(keikaku1DetailInfoOutEntities.get(0).getHoushinKnj());
                // API定義の処理「4.4」に取得した「家事援助区分」
                model.setKajiCode(CommonDtoUtil.objValToString(keikaku1DetailInfoOutEntities.get(0).getKajiCode()));
                // API定義の処理「4.4」に取得した「家事援助(その他)」
                model.setKajiSonotaKnj(keikaku1DetailInfoOutEntities.get(0).getKajiSonotaKnj());

            } else {
                // 以外の場合：「0:非表示」
                model.setZanteiFlg(0);
                // ※空の場合：0
                model.setKubun(0);
                // ※空の場合：0
                model.setNintei(0);
            }
            // リクエストパラメータ.データ.印刷設定.パラメータ06
            model.setAutoFlg(CommonDtoUtil.strValToInt(inDto.getPrintSet().getParam06()));

            // リクエストパラメータ.データ.印刷設定.日付表示有無が「2:指定日印刷」の場合
            if (CommonConstants.STR_2.equals(inDto.getPrintSet().getPrnDate())) {
                // 共通関数補足の「2.1」の和暦日付
                model.setShiTeiDate(
                        nds3GkFunc01Logic.get2Gengouj(1,
                                CommonDtoUtil.strNullToEmpty(inDto.getDbNoSaveData().getSelectDate())));
            } else if (CommonConstants.STR_3.equals(inDto.getPrintSet().getPrnDate())) {
                // リクエストパラメータ.データ.印刷設定.日付表示有無が「3:日付空欄印刷」の場合
                // 共通関数補足の「3.1」の印刷用日付
                model.setShiTeiDate(nds3GkFunc01Logic.blankDate(inDto.getAppYmd()));
            } else {
                // 上記以外の場合
                model.setShiTeiDate(CommonConstants.BLANK_STRING);
            }
            // リクエストパラメータ.データ.印刷設定.パラメータ09を判断
            if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam09())) {
                // 「1:作成年月日を印刷する」の場合
                if (keikaku1DetailInfoOutEntities != null && keikaku1DetailInfoOutEntities.size() > 0) {
                    // API定義の処理「4.4」に取得した「作成日」
                    model.setCreateYmd(
                            nds3GkFunc01Logic.get2Gengouj(1,
                                    CommonDtoUtil.strNullToEmpty(keikaku1DetailInfoOutEntities.get(0).getCreateYmd())));
                }
            } else if (CommonConstants.STR_2.equals(inDto.getPrintSet().getParam09())) {
                // 「2:空白」の場合：" 年 月 日"
                model.setCreateYmd(STR_EMPTY_YMD);
            } else if (CommonConstants.STR_3.equals(inDto.getPrintSet().getParam09())) {
                // 「3:作成年月日を印刷しない」の場合：""
                model.setCreateYmd(CommonConstants.BLANK_STRING);
            }
        }
        // リクエストパラメータ.データ.初期設定マスタの情報.アセスメント方式
        model.setCpnFlg(inDto.getInitMasterObj().getCpnFlg());
        // リクエストパラメータ.データ.DB未保存画面項目.記入用シートを印刷するフラグ
        model.setEmptyFlg(CommonDtoUtil.strValToInt(inDto.getDbNoSaveData().getEmptyFlg()));
        // リクエストパラメータ.データ.印刷設定.パラメータ09
        model.setCreateYmdSelFlg(inDto.getPrintSet().getParam09());
        // リクエストパラメータ.データ.印刷設定.帳票タイトル
        model.setPrtTitle(inDto.getPrintSet().getPrtTitle());
        // API定義の処理「2.2」で取得した「作成日」 < "2021/04/01"の場合
        if (keikaku1DetailInfoOutEntities != null && keikaku1DetailInfoOutEntities.size() > 0
                && LocalDate.parse(keikaku1DetailInfoOutEntities.get(0).getCreateYmd(), DateTimeFormatter.ofPattern(
                        CommonConstants.DATE_FORMAT_YYYY_MM_DD))
                        .compareTo(LocalDate.parse(CommonConstants.STR_NUM_2021_04_01,
                                DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD))) < 0) {
            // 帳票用データ詳細.居宅サービス計画書（１）情報.高さを自動調整フラグが「1:自動調整する」の場合
            if (model.getAutoFlg() == 1) {
                model.setIkouTitle(STR_USER_AND_FAMILY.concat(CommonConstants.STR_LINE_BREAK).concat(STR_LIFE_GOAL));
            } else {
                model.setIkouTitle(CommonConstants.STR_LINE_BREAK.concat(CommonConstants.STR_LINE_BREAK).concat(
                        STR_USER_AND_FAMILY).concat(CommonConstants.STR_LINE_BREAK)
                        .concat(CommonConstants.STR_LINE_BREAK)
                        .concat(STR_LIFE_GOAL));
            }
        } else {
            // 上記以外の場合
            // 帳票用データ詳細.居宅サービス計画書（１）情報.高さを自動調整フラグが「1:自動調整する」の場合
            if (model.getAutoFlg() == 1) {
                model.setIkouTitle(STR_USER_AND_FAMILY.concat(CommonConstants.STR_LINE_BREAK).concat(STR_LIFE)
                        .concat(CommonConstants.STR_LINE_BREAK).concat(STR_GOAL)
                        .concat(CommonConstants.STR_LINE_BREAK).concat(STR_RESULT));
            } else {
                model.setIkouTitle(CommonConstants.STR_LINE_BREAK.concat(STR_USER_AND_FAMILY)
                        .concat(CommonConstants.STR_LINE_BREAK)
                        .concat(CommonConstants.STR_LINE_BREAK)
                        .concat(STR_LIFE)
                        .concat(CommonConstants.STR_LINE_BREAK).concat(CommonConstants.STR_LINE_BREAK).concat(STR_GOAL)
                        .concat(CommonConstants.STR_LINE_BREAK).concat(CommonConstants.STR_LINE_BREAK)
                        .concat(STR_RESULT));
            }
        }
        // 帳票用データ詳細.居宅サービス計画書（１）情報.高さを自動調整フラグが「1:自動調整する」の場合
        if (model.getAutoFlg() == 1) {
            // 意見タイトル
            model.setIkenTitle(STR_IKEN_TITLE1.concat(CommonConstants.STR_LINE_BREAK).concat(STR_IKEN_TITLE2)
                    .concat(CommonConstants.STR_LINE_BREAK).concat(STR_IKEN_TITLE3));
            // 方針タイトル
            model.setHoushinTitle(STR_HOUSHIN_TITLE1.concat(CommonConstants.STR_LINE_BREAK).concat(STR_HOUSHIN_TITLE2));
        } else {
            // 上記以外の場合
            // 意見タイトル
            model.setIkenTitle(CommonConstants.STR_LINE_BREAK.concat(STR_IKEN_TITLE1)
                    .concat(CommonConstants.STR_LINE_BREAK)
                    .concat(CommonConstants.STR_LINE_BREAK)
                    .concat(STR_IKEN_TITLE2)
                    .concat(CommonConstants.STR_LINE_BREAK).concat(CommonConstants.STR_LINE_BREAK)
                    .concat(STR_IKEN_TITLE3));
            // 意見_固定行ラ ベル表示フラグ
            model.setIkenFixedRowFlg(2);
            // 方針タイトル
            model.setHoushinTitle(
                    CommonConstants.STR_LINE_BREAK.concat(CommonConstants.STR_LINE_BREAK).concat(STR_HOUSHIN_TITLE1)
                            .concat(CommonConstants.STR_LINE_BREAK).concat(CommonConstants.STR_LINE_BREAK)
                            .concat(STR_HOUSHIN_TITLE2));
        }
        // リクエストパラメータ.データ.印刷設定.パラメータ05が「1:承認欄印刷」の場合
        if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam05())) {
            ShoninInfoListByCriteriaInEntity shoninInfoListByCriteriaInEntity = new ShoninInfoListByCriteriaInEntity();
            // リクエストパラメータ.事業者情報.サービス事業者ID
            shoninInfoListByCriteriaInEntity
                    .setJigyoId(CommonDtoUtil.strValToInt(inDto.getJigyoInfo().getSvJigyoId()));
            // リクエストパラメータ.データ.初期設定マスタの情報.承認欄情報が「1:帳票毎保持する」の場合
            if (CommonConstants.STR_1.equals(inDto.getInitMasterObj().getShoninFlg())) {
                // フラグ
                shoninInfoListByCriteriaInEntity.setAlFlg(1);
                // リクエストパラメータ.データ.印刷設定.プロファイル
                shoninInfoListByCriteriaInEntity.setAsCode(inDto.getPrintSet().getProfile());
            } else {
                // フラグ
                shoninInfoListByCriteriaInEntity.setAlFlg(0);
                // 帳票コード
                shoninInfoListByCriteriaInEntity.setAsCode(CommonConstants.BLANK_STRING);
            }
            // 承認欄方向
            shoninInfoListByCriteriaInEntity.setAiType(1);
            List<ShoninInfoListOutEntity> shoninInfoListOutEntities = cpnMucShoninSelectMapper
                    .findShoninInfoListByCriteria(shoninInfoListByCriteriaInEntity);

            // 処理「5.1.2」で取得した「表示行数」 > 2の場合
            if (shoninInfoListOutEntities != null && shoninInfoListOutEntities.size() > 0
                    && shoninInfoListOutEntities.get(0).getDispKbn() != null && shoninInfoListOutEntities.get(0)
                            .getDispKbn() > 2) {
                // 帳票用データ詳細.居宅サービス計画書（１）情報.高さを自動調整フラグが「1:自動調整する」の場合
                if (model.getAutoFlg() == 1) {
                    // 意見タイトル
                    model.setIkenTitle(STR_IKEN_TITLE1.concat(CommonConstants.STR_LINE_BREAK).concat(STR_IKEN_TITLE2)
                            .concat(CommonConstants.STR_LINE_BREAK).concat(STR_IKEN_TITLE3));
                } else {
                    // 上記以外の場合
                    // 意見タイトル
                    model.setIkenTitle(
                            CommonConstants.STR_LINE_BREAK.concat(STR_IKEN_TITLE1)
                                    .concat(CommonConstants.STR_LINE_BREAK)
                                    .concat(STR_IKEN_TITLE2)
                                    .concat(CommonConstants.STR_LINE_BREAK)
                                    .concat(STR_IKEN_TITLE3));
                    // 1：固定5行ラベル表示
                    model.setIkenFixedRowFlg(1);
                }
            }
        }
        // リクエストパラメータ.データ.初期設定マスタの情報.印刷時の文字サイズを判断
        switch (inDto.getInitMasterObj().getCks1PrtSizeFlg()) {
            case CommonConstants.STR_0:
                model.setCks1PrtSizeFlg(CommonConstants.STR_9);
                break;
            case CommonConstants.STR_1:
                model.setCks1PrtSizeFlg(CommonConstants.STR_10);
                break;
            case CommonConstants.STR_2:
                model.setCks1PrtSizeFlg(CommonConstants.STR_11);
                break;
            case CommonConstants.STR_3:
                model.setCks1PrtSizeFlg(CommonConstants.STR_12);
                break;
            default:
                model.setCks1PrtSizeFlg(CommonConstants.STR_11);
                break;
        }
        // リクエストパラメータ.データ.印刷設定.パラメータ05
        model.setShoninFlg(inDto.getPrintSet().getParam05());
        return model;
    }

    /**
     * 帳票レイアウトファイル取得
     * 
     * @param fwProps         FrameworkProperties
     * @param reportParameter PDF帳票パラメータ
     * @param inDto           入力データ
     * @return 帳票レイアウトファイル
     * @throws Exception
     */
    public JasperReport getJasperReport(FrameworkProperties fwProps,
            Plan1kReportServiceInDto reportParameter, Plan1kReportParameterModel inDto)
            throws Exception {

        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(fwProps, ReportConstants.U0081K_PLAN1_REPORT)));

        // コンパイル
        final JasperDesign jasperDesign = JRXmlLoader.load(is);

        // 承認欄の取得処理
        // リクエストパラメータ.データ.印刷設定.パラメータ05が「1:承認欄印刷」の場合
        if (CommonConstants.STR_1.equals(inDto.getPrintSet().getParam05())) {
            // リクエストパラメータ.データ.初期設定マスタの情報.承認欄情報が「1:帳票毎保持する」の場合
            HashMap<String, Object> shonin = null;
            if (CommonConstants.STR_1.equals(inDto.getInitMasterObj().getShoninFlg())) {
                shonin = shoninSetReportService.getShoninSetReport(jasperDesign,
                        ReportConstants.INT_1, inDto.getJigyoInfo().getSvJigyoId(),
                        inDto.getPrintSet().getProfile());
            } else {
                shonin = shoninSetReportService.getShoninSetReport(jasperDesign,
                        ReportConstants.INT_0, inDto.getJigyoInfo().getSvJigyoId(),
                        CommonConstants.BLANK_STRING);
            }
            reportParameter.setSubReportPath((String) shonin.get(ReportConstants.SUBREPORTPATH));
            reportParameter.setSubReportDataDs(
                    (JRBeanCollectionDataSource) shonin.get(ReportConstants.SUBREPORTDATADS));
        }

        return JasperCompileManager.compileReport(jasperDesign);
    }

    /**
     * 共通関数補足の「6. 伏字処理」を調用
     * 
     * @param inDto     リクエストパラメータ
     * @param model     対象の値
     * @param asModeKbn カラム名orテキスト名
     * @param mode      処理モード
     * @param kbn       処理区分
     */
    private void getFuseJi(Plan1kReportParameterModel inDto, Plan1kReportServiceInDto model, String asModeKbn,
            String mode, int kbn) {
        List<Object> dataWindowDtoList = new ArrayList<>();
        dataWindowDtoList.add(model);
        nds3GkBase02Logic.getF3gkComPrintFuseji(inDto.getDbNoSaveData().getShokuinId(), asModeKbn,
                new String[] { mode }, kbn, dataWindowDtoList, new ArrayList<>(), ReportConstants.INT_0,
                inDto.getPrintSet().getProfile(), StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY);
    }

}
