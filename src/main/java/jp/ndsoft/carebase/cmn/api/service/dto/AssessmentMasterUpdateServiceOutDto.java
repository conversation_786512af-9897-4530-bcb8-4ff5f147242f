package jp.ndsoft.carebase.cmn.api.service.dto;

import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.04.23
 * <AUTHOR>
 * @apiNote GUI00626_「アセスメントマスタ」画面の入力情報保存サービス出力Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AssessmentMasterUpdateServiceOutDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;
    /** 更新成功フラグ. */
    private boolean updateSuccessFlag;

}