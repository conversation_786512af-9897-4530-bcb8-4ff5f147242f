package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import jp.ndsoft.smh.framework.global.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GUI00772_アセスメント(インターライ)画面I-1
 * 
 * @description
 *              サブ情報（I-1）
 *              サブ情報（I-1）エンティティ
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GUI00772SelectSubInfoI1 implements Serializable, IEntity {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** アセスメントID */
    private String raiId;

    /** 疾患_筋骨系_大腿骨骨折 */
    private String i1A;

    /** 疾患_筋骨系_その他の骨折 */
    private String i1B;

    /** 疾患_神経系_アルツハイマー病 */
    private String i1C;

    /** 疾患_神経系_認知症 */
    private String i1D;

    /** 疾患_神経系_片麻痺 */
    private String i1E;

    /** 疾患_神経系_多発性硬化症 */
    private String i1F;

    /** 疾患_神経系_対麻痺 */
    private String i1G;

    /** 疾患_神経系_パーキンソン病 */
    private String i1H;

    /** 疾患_神経系_四肢麻痺 */
    private String i1I;

    /** 疾患_神経系_脳卒中/脳血管障害 */
    private String i1J;

    /** 疾患_心肺系_CHD */
    private String i1K;

    /** 疾患_心肺系_COPD */
    private String i1L;

    /** 疾患_心肺系_CHF */
    private String i1M;

    /** 疾患_心肺系_高血圧症 */
    private String i1N;

    /** 疾患_精神_不安症 */
    private String i1O;

    /** 疾患_精神_双極性障害 */
    private String i1P;

    /** 疾患_精神_うつ */
    private String i1Q;

    /** 疾患_精神_統合失調症 */
    private String i1R;

    /** 疾患_感染症_肺炎 */
    private String i1S;

    /** 疾患_感染症_UTI */
    private String i1T;

    /** 疾患_その他_がん */
    private String i1U;

    /** 疾患_その他_糖尿病 */
    private String i1V;

    /** i1_メモ */
    private String i1MemoKnj;

    /** i1_メモフォント */
    private String i1MemoFont;

    /** i1_メモ色 */
    private String i1MemoColor;

}
