package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00969IniData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01211ChoPrt;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01211JigyouInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01211KHoken;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01211OutPrint;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01211RiyoushaInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01211Tanto;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI01211_API定義書_APINo(1208)_印刷設定画面初期の出力Dto
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class PrintSettingsScreenInitialInfoSelectGUI01211ServiceOutDto extends IDtoImpl {
	/** serialVersionUID. */
	private static final long serialVersionUID = 1L;

	// 事業所選択情報リスト
	private List<Gui01211JigyouInfo> jigyouInfoList;

	// 利用者情報リスト
	private List<Gui01211RiyoushaInfo> riyoushaInfoList;

	// 担当リスト
	private List<Gui01211Tanto> tantoList;

	// 保険者番号リスト
	private List<Gui01211KHoken> kHokenList;

	// 個人保護フラグ
	private String kojinHogoFlg;

	//　伏字フラグ
	private String amikakeFlg;

	// 文書番号フラグ
	private String monjiNoFlg;

	// 出力帳票印刷情報リスト
	private List<Gui01211ChoPrt> choPrtList;

	// 印刷日付
	private String selectdate;

	// 出力帳票リスト
	private List<Gui01211OutPrint> outPrintList;

	// 処理年月
	private String shoriYm;

	// 画面表示フラグ
	private String gameHyojiFlg;

	// 帳票INIデータリスト
	private List<Gui00969IniData> iniDataList;


}
