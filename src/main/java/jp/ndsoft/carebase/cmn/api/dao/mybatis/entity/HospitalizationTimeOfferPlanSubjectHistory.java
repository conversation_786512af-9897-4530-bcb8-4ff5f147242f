package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
// 印刷対象履歴リスト
public class HospitalizationTimeOfferPlanSubjectHistory implements Serializable {
    /** 利用者ID */
    private String userId;
    /** 利用者名 */
    private String userName;
    /** 期間ID */
    private String sc1Id;
    /** 開始日 */
    private String startYmd;
    /** 終了日 */
    private String endYmd;
    /** 結果 */
    private String result;
    /** 提供ID */
    private String teikyouId;
    /** 出力帳票印刷情報リスト */
    private List<ReportCommonChoPrt> choPrtList;
}