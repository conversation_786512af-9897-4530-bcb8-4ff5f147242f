package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01310PrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01310User;
import jp.ndsoft.carebase.cmn.api.service.dto.LeavingInfoRecordPrintSettingsHistorySelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.LeavingInfoRecordPrintSettingsHistorySelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnTaiKirokuReqRirekiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnTaiKirokuReqRirekiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.TaiJohoKirokuRirekiSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * @since 2025.07.08
 * <AUTHOR>
 * @implNote GUI01310_印刷設定 複数利用者履歴情報取得
 */
@Service
public class LeavingInfoRecordPrintSettingsHistorySelectServiceImpl extends
        SelectServiceImpl<LeavingInfoRecordPrintSettingsHistorySelectServiceInDto, LeavingInfoRecordPrintSettingsHistorySelectServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 印刷設定共通クラス */
    @Autowired
    private TaiJohoKirokuRirekiSelectMapper taiJohoKirokuRirekiSelectMapper;

    /**
     * 複数利用者履歴情報取得
     * 
     * @param inDto 複数利用者履歴情報取得サービス入力Dto
     * @return 複数利用者履歴情報取得サービス出力Dto
     * @throws Exception Exception
     */
    @Override
    protected LeavingInfoRecordPrintSettingsHistorySelectServiceOutDto mainProcess(
            LeavingInfoRecordPrintSettingsHistorySelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        LeavingInfoRecordPrintSettingsHistorySelectServiceOutDto outDto = new LeavingInfoRecordPrintSettingsHistorySelectServiceOutDto();

        List<Gui01310PrintSubjectHistory> printSubjectHistoryList = new ArrayList<Gui01310PrintSubjectHistory>();
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2.印刷対象履歴一覧を取得する。===============
         * 
         */
        // (1).リクエストパラメータ.利用者リスト件数分、繰り返し、退院退所情報記録履歴を取得する
        if (CollectionUtils.isNotEmpty(inDto.getUserList())) {
            for (Gui01310User user : inDto.getUserList()) {
                // ・下記の履歴＿調査記録情報取得のDAOを利用し、退院退所情報記録履歴を取得する。
                KghCpnTaiKirokuReqRirekiByCriteriaInEntity kghCpnTaiKirokuReqRirekiByCriteriaInEntity = new KghCpnTaiKirokuReqRirekiByCriteriaInEntity();
                // 事業者ID
                kghCpnTaiKirokuReqRirekiByCriteriaInEntity
                        .setAnJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                // 利用者ID
                kghCpnTaiKirokuReqRirekiByCriteriaInEntity.setAnUserid(CommonDtoUtil.strValToInt(user.getUserId()));

                List<KghCpnTaiKirokuReqRirekiOutEntity> kghCpnTaiKirokuReqRirekiList = this.taiJohoKirokuRirekiSelectMapper
                        .findKghCpnTaiKirokuReqRirekiByCriteria(kghCpnTaiKirokuReqRirekiByCriteriaInEntity);

                Gui01310PrintSubjectHistory printSubjectHistory = new Gui01310PrintSubjectHistory();

                // ・履歴取得ありの場合、
                if (CollectionUtils.isNotEmpty(kghCpnTaiKirokuReqRirekiList)) {
                    // ①上記のOUTPUT情報を（作成日 降序, ケアチェックID 降序, ）ソート処理
                    kghCpnTaiKirokuReqRirekiList.sort(Comparator
                            .comparing(
                                    KghCpnTaiKirokuReqRirekiOutEntity::getCreateYmd,
                                    Comparator.nullsLast(Comparator.reverseOrder()))
                            .thenComparing(
                                    KghCpnTaiKirokuReqRirekiOutEntity::getRecId,
                                    Comparator.nullsLast(Comparator.reverseOrder())));

                    // ②ソート結果から作成日をリクエストパラメータ.基準日の以前のレコードの1件目を取得する
                    List<KghCpnTaiKirokuReqRirekiOutEntity> filteredList = kghCpnTaiKirokuReqRirekiList.stream()
                            .filter(kghCpnTaiKirokuReqRirekil -> !StringUtil
                                    .isEmpty(kghCpnTaiKirokuReqRirekil.getCreateYmd())
                                    && kghCpnTaiKirokuReqRirekil.getCreateYmd()
                                            .compareTo(inDto.getKijunbiYmd()) <= 0)
                            .collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(filteredList)) {
                        // ①検索結果がある場合、下記の値を設定する
                        printSubjectHistory = this.setPrintSubjectHistory(filteredList.get(0), user);

                    } else {
                        // ②検索結果がない場合、
                        // 利用者ID
                        printSubjectHistory.setUserId(user.getUserId());
                        // 利用者名
                        printSubjectHistory.setUserName(user.getUserName());

                        printSubjectHistory.setResult(CommonConstants.ASS_PRT_RRK_NONE_MSG);
                    }
                } else {
                    // 2.1.2. 「2.1.1.」で取得する履歴情報件数=0件の場合、印刷対象履歴リストを設定して、continue で次のループ処理に移る
                    // 利用者ID
                    printSubjectHistory.setUserId(user.getUserId());
                    // 利用者名
                    printSubjectHistory.setUserName(user.getUserName());

                    printSubjectHistory.setResult(CommonConstants.ASS_PRT_RRK_NONE_MSG);
                }
                printSubjectHistoryList.add(printSubjectHistory);
            }

        }

        outDto.setPrintSubjectHistoryList(printSubjectHistoryList);
        LOG.info(Constants.END);

        return outDto;
    }

    /**
     * 印刷対象履歴を設定する。
     * 
     * @param kghCpnTaiKirokuReqRireki 退院退所情報記録履歴
     * @param user                     利用者
     * 
     * @return 印刷対象履歴
     */
    private Gui01310PrintSubjectHistory setPrintSubjectHistory(
            KghCpnTaiKirokuReqRirekiOutEntity kghCpnTaiKirokuReqRireki, Gui01310User user) {
        Gui01310PrintSubjectHistory printSubjectHistory = new Gui01310PrintSubjectHistory();

        // 利用者ID
        printSubjectHistory.setUserId(user.getUserId());
        // 利用者名
        printSubjectHistory.setUserName(user.getUserName());
        // 期間ID
        printSubjectHistory.setSc1Id(CommonDtoUtil.objValToString(kghCpnTaiKirokuReqRireki.getSc1Id()));
        // 開始日
        printSubjectHistory.setStartYmd(kghCpnTaiKirokuReqRireki.getStartYmd());
        // 終了日
        printSubjectHistory.setEndYmd(kghCpnTaiKirokuReqRireki.getEndYmd());
        // 履歴ID
        printSubjectHistory.setRecId(CommonDtoUtil.objValToString(kghCpnTaiKirokuReqRireki.getRecId()));
        // 記入日
        printSubjectHistory.setCreateYmd(CommonDtoUtil.objValToString(kghCpnTaiKirokuReqRireki.getCreateYmd()));
        // 作成者
        printSubjectHistory.setChkShokuId(CommonDtoUtil.objValToString(kghCpnTaiKirokuReqRireki.getChkShokuId()));
        // 結果
        printSubjectHistory.setResult(CommonConstants.BLANK_STRING);

        return printSubjectHistory;

    }

}
