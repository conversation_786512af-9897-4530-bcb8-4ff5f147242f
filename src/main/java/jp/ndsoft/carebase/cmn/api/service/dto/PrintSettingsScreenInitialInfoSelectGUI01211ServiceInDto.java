package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI01211_API定義書_APINo(1208)_印刷設定画面初期の入力Dto
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class PrintSettingsScreenInitialInfoSelectGUI01211ServiceInDto extends IDtoImpl {
	/** serialVersionUID. */
	private static final long serialVersionUID = 1L;

	// 職員ID
	@NotEmpty
	private String shokuId;
	// 法人ID
	@NotEmpty
	private String houjinId;
	// 施設ID
	@NotEmpty
	private String shisetuId;
	// 事業所ID
	@NotEmpty
	private String svJigyoId;
	// システムコード
	@NotEmpty
	private String sysCd;
	// セクション名
	@NotEmpty
	private String sectionName;
	// システム略称
	@NotEmpty
	private String sysRyaku;
	// 提供年月
	@NotEmpty
	private String yymmYm;
	// 担当ケアマネ
	@NotEmpty
	private String tantoId;
	// 支援事業所
	@NotEmpty
	private String shienId;
	// 選択帳票番号
	@NotEmpty
	private String choIndex;
	// 機能名
	@NotEmpty
	private String kinounameKnj;
	// 個人情報表示フラグ
	@NotEmpty
	private String kojinhogoFlg;
	// 個人情報表示値
	@NotEmpty
	private String sectionAddNo;
}