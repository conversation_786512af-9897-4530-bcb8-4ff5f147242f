package jp.ndsoft.carebase.cmn.api.logic;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.dto.Cmn2svjigyoDelInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.CmnPlanAllCalcsOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.CmnTensouPlanAll0OutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.CmnTorikomiAll3OutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GetUserInfoMonthOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KghCmnUsrInfo3gDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MakeGaiyouInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcItemuseInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcItemuseOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcItemuseSougouInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcItemuseSougouOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcKmInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcKmOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcSmInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcSmOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.RetrieveOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDaoUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanRiyouMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucServicePointMapper;
import jp.ndsoft.carebase.common.dao.mybatis.ComTlcSvplanPointMapper;
import jp.ndsoft.carebase.common.dao.mybatis.ComTlcSvplanResultMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanRiyou;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanRiyouCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucServicePoint;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucServicePointCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComTlcSvplanPoint;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComTlcSvplanPointCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComTlcSvplanResult;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComTlcSvplanResultCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnDelSvplanResultCmnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnDelSvplanResultCmnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnDelSvplanResultSdlUseridByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnDelSvplanResultSdlUseridOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnPlnComMaxEdanoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnPlnComMaxEdanoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnPlnModPlanChkByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnPlnModPlanChkOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnPointTensou3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnPointTensou3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTensouAll3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTensouAll3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTensouAllShienidByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTensouAllShienidOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTensouPlanAll0ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTensouPlanAll0OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTensouPlanDelByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTensouPlanDelOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTensouPointAll0ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTensouPointAll0OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTorikomiAll3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTorikomiAll3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTorikomiPlanAll01ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnTorikomiPlanAll01OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnChkMaxOyaLineByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnChkMaxOyaLineOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnGetMaxOyaLineByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnGetMaxOyaLineOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnGetNdtypeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnGetNdtypeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnListHok15ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnListHok15OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnListItemuseHok15ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnListItemuseHok15OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.OyaLineNoMaxByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.OyaLineNoMaxOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvrRiyoJokenByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvrRiyoJokenOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvtypeOyaLineNoMaxByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvtypeOyaLineNoMaxOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Torikomi0FindByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.Torikomi0FindOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnPlnComMaxEdanoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanRiyouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucServicePointSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcKmSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTlcSvplanPointSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTlcSvplanResultSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ServicePlan2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ServicePlanResult1SelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.smh.framework.util.AppUtil;

/**
 * KghCmnPlanTensouAllLogicロジッククラス
 * 
 * <AUTHOR>
 */
@Component
public class KghCmnPlanTensouAllLogic {

    /** */
    @Autowired
    private KghCmn03gFunc01Logic kghCmn03gFunc01Logic;
    /** */
    @Autowired
    private KghCmnF01Logic kghCmnF01Logic;
    /** */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;
    /** */
    @Autowired
    private KghCmnCalc01Logic kghCmnCalc01Logic;
    /** */
    @Autowired
    private KghCmnWeek1Logic kghCmnWeek1Logic;
    /** */
    @Autowired
    private KghPlanTensouKanriLogic kghPlanTensouKanriLogic;
    /** */
    @Autowired
    private CmnTucPlanRiyouSelectMapper cmnTucPlanRiyouSelectMapper;
    /** */
    @Autowired
    private ComTlcSvplanResultSelectMapper comTlcSvplanResultSelectMapper;
    /** */
    @Autowired
    private ComMhcKmSelectMapper comMhcKmSelectMapper;
    /** */
    @Autowired
    private ComMhcItemuseSelectMapper comMhcItemuseSelectMapper;
    /** */
    @Autowired
    private CmnPlnComMaxEdanoSelectMapper cmnPlnComMaxEdanoSelectMapper;
    /** */
    @Autowired
    private ComTlcSvplanResultMapper comTlcSvplanResultMapper;
    /** */
    @Autowired
    private CmnTucPlanRiyouMapper cmnTucPlanRiyouMapper;
    /** */
    @Autowired
    private ServicePlan2SelectMapper servicePlan2SelectMapper;
    /** */
    @Autowired
    private ServicePlanResult1SelectMapper servicePlanResult1SelectMapper;
    /** */
    @Autowired
    private ComTlcSvplanPointSelectMapper comTlcSvplanPointSelectMapper;
    /** */
    @Autowired
    private CmnTucServicePointSelectMapper cmnTucServicePointSelectMapper;
    /** */
    @Autowired
    private ComTlcSvplanPointMapper comTlcSvplanPointMapper;
    /** */
    @Autowired
    private KghKsgEntry15Logic kghKsgEntry15Logic;
    /** */
    @Autowired
    private CmnTucServicePointMapper cmnTucServicePointMapper;
    /** */
    @Autowired
    private KghShrKsgFunc01Logic kghShrKsgFunc01Logic;

    /**
     * uf_tensou_plan : 計画を転送するGroup3→Group0
     * 
     * @param ilMaxEda             版の最大値をもとめ
     * @param isYymm               処理年月(yyyy/mm)
     * @param shokuinId            職員ID
     * @param cmnTorikomiAll3OList ケアマネ側計画（Grouo3）
     * @param adsListHok15         厚労省 介護サービスマスタ(com_mhc_km)リスト
     * @param adsListItemuseHok15  介護サービス費適用マスタ(com_mhc_itemuse)リスト
     * @return Integer 0：正常終了 -1：異常終了
     * <AUTHOR>
     */
    public Integer tensouPlan(Integer ilMaxEda, Integer shokuinId, String isYymm,
            List<CmnTensouAll3OutEntity> cmnTorikomiAll3OList, List<KghCmnListHok15OutEntity> adsListHok15,
            List<KghCmnListItemuseHok15OutEntity> adsListItemuseHok15) {
        String lsScode = StringUtils.EMPTY;
        String lsTs = StringUtils.EMPTY;
        String lsTe = StringUtils.EMPTY;
        String lsTmp = StringUtils.EMPTY;
        Integer llCmpSvjigyo = CommonConstants.NUMBER_0;
        Integer llShien = CommonConstants.NUMBER_0;
        Integer llUser = CommonConstants.NUMBER_0;
        Integer llHoujin = CommonConstants.NUMBER_0;
        Integer llTermid = CommonConstants.NUMBER_0;
        Integer liYojitiKbn = CommonConstants.NUMBER_0;
        Integer llSisetu = CommonConstants.NUMBER_0;
        Integer llWork = CommonConstants.NUMBER_0;
        String lsYymmdd = StringUtils.EMPTY; // 変更日も加えた日付
        List<ComTlcSvplanResult> results = new ArrayList<>();
        lsTmp = nds3GkFunc01Logic.getTukimatu(isYymm + "/01");
        Integer liMMax = Integer.parseInt(lsTmp.substring(lsTmp.length() - 2));
        // 初期設定 //CmnTucPlanRiyouSelectMapper. findCmnTorikomiAll3ByCriteria
        List<Integer> liDayZero = IntStream.range(CommonConstants.INT_0, CommonConstants.INT_31)
                .mapToObj(i -> CommonConstants.INT_0).collect(Collectors.toList());

        List<Integer> liDay = new ArrayList<>(liDayZero);
        // 計画転送
        Integer llRow = cmnTorikomiAll3OList.size();
        // Group3の計画データをGroup0の計画SetItemする。
        // この段階でids_tensou_all_3(Group3の計画)は支援事業所+利用者+処理年月でFilterされています。
        for (int llCnt = 0; llCnt < cmnTorikomiAll3OList.size(); llCnt++) {
            Integer liTtl = CommonConstants.NUMBER_0;
            Integer liMx = liMMax;
            CmnTensouAll3OutEntity outEntity = cmnTorikomiAll3OList.get(llCnt);
            // サービス事業所ID
            Integer llSvjigyo = outEntity.getSvJigyoId();
            if (llCnt > CommonConstants.NUMBER_0) {
                if (llCmpSvjigyo != llSvjigyo) {
                    // サービス事業所が変わったら変わる前のサービス事業所の概要レコードを作成する
                    MakeGaiyouInDto inDto = new MakeGaiyouInDto();
                    // 支援事業所ID
                    inDto.setAlShien(llShien);
                    // 利用者ID
                    inDto.setAlUser(llUser);
                    // 処理年月
                    inDto.setAsYymm(lsYymmdd);
                    // 法人ID
                    inDto.setAlHoujin(llHoujin);
                    // 施設ID
                    inDto.setAlShisetu(llSisetu);
                    // サービス事業所
                    inDto.setAlSvjigyo(llCmpSvjigyo);
                    inDto.setAiYojituKbn(liYojitiKbn);
                    inDto.setAlTerm(llTermid);
                    inDto.setAiDay(liDay);
                    int res = this.ufMakeGaiyou(inDto);
                    if (res != CommonConstants.NUMBER_0) {
                        return CommonConstants.INT_MINUS_1;
                    }
                    llCmpSvjigyo = llSvjigyo;
                    // li_dayを初期化する
                    liDay = liDayZero;
                }
            } else {
                llCmpSvjigyo = llSvjigyo;
            }
            // ケアマネ側計画（Group3）を転送（Group0にSetItem）する
            ComTlcSvplanResult comTlcSvplanResult = new ComTlcSvplanResult();
            // サービス事業所
            comTlcSvplanResult.setSvJigyoId(llCmpSvjigyo);
            // 利用者ID
            llUser = outEntity.getUserid();
            comTlcSvplanResult.setUserid(llUser);
            // 法人ID
            llHoujin = kghCmnF01Logic.getHoujinCd(llSvjigyo);
            comTlcSvplanResult.setHoujinId(llHoujin);
            // 施設ID
            llSisetu = kghCmnF01Logic.getShisetuCd(llSvjigyo);
            comTlcSvplanResult.setShisetuId(llSisetu);
            // 支援事業所ID
            llShien = outEntity.getShienId();
            comTlcSvplanResult.setShienJigyoId(llShien);
            // 処理年月
            lsYymmdd = outEntity.getYymmYm() + "/" + outEntity.getYymmD();
            comTlcSvplanResult.setYymmYmd(lsYymmdd);
            // サービス項目-
            llWork = outEntity.getSvItemCd();
            comTlcSvplanResult.setItemcode(llWork);
            // 下方にあったのを移動
            lsScode = outEntity.getScode();
            // 枝番
            llWork = outEntity.getEdaNo();
            llWork = llWork + ilMaxEda;
            comTlcSvplanResult.setEdano(llWork);
            // 転送した枝番をケアマネ側の計画に保存しておく（実績取り込みの際使用するため）
            comTlcSvplanResult.setNthEdano(llWork);
            // ids_tensou_all_3.SetItem( ll_cnt1 , "tensou_fl" , 1 )
            // 予定・実績モード
            liYojitiKbn = CommonConstants.NUMBER_1; // 予定
            comTlcSvplanResult.setSwchFlg(liYojitiKbn);
            // termid（サービス費適用マスタ期間ID
            llTermid = outEntity.getTermid();
            comTlcSvplanResult.setTermid(llTermid);
            // 開始時間-終了時間
            lsTs = outEntity.getSvStartTime();
            lsTe = outEntity.getSvEndTime();
            if (kghCmnF01Logic.chkKasanService(lsScode.substring(CommonConstants.NUMBER_0, CommonConstants.NUMBER_2),
                    lsScode, isYymm + "/01", llSvjigyo)) {
                lsTs = CommonConstants.TIME_77_77;
                lsTe = CommonConstants.TIME_77_77;
            }
            if (CommonConstants.KAISHI_JIKAN_9.equals(lsTs) || CommonConstants.KAISHI_JIKAN_8.equals(lsTs)
                    || CommonConstants.KAISHI_JIKAN_7.equals(lsTs)) {
            } else {
                // それ以外であれば代入する
                comTlcSvplanResult.setSvStartTime(lsTs);
                comTlcSvplanResult.setSvEndTime(lsTe);
            }
            // 各日付の計画内容
            for (int i = 1; i < 32; i++) {
                try {
                    // ids_tensou_all_3
                    Field field = ComTlcSvplanResult.class.getDeclaredField("day" + i);
                    field.setAccessible(true);
                    // ids_plan_tensou_all_0
                    String fieldName = String.format("yDay%02d", i);
                    Field field2 = CmnTorikomiAll3OutEntity.class.getDeclaredField(fieldName);
                    field2.setAccessible(true);
                    Object obj = field2.get(outEntity);
                    if (obj != null) {
                        llWork = Integer.parseInt((String) field2.get(outEntity));
                    } else {
                        llWork = CommonConstants.NUMBER_0;
                    }
                    field.set(comTlcSvplanResult, llWork);
                    if (llWork > 0) {
                        liDay.set(i - 1, CommonConstants.NUMBER_1);// 計画のあった日に１をセットする
                        liTtl = liTtl + llWork;// 合算を追加
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            // 合計
            comTlcSvplanResult.setTotal(liTtl);
            // 更新日付
            LocalDateTime ldtNow = LocalDateTime.of(LocalDate.now(), LocalTime.now());
            Timestamp timestamp = Timestamp.valueOf(ldtNow);
            comTlcSvplanResult.setTimeStmp(timestamp);
            // 削除フラグ
            comTlcSvplanResult.setDelFlg(CommonConstants.NUMBER_0);
            // 職員ID
            comTlcSvplanResult.setShokuId(shokuinId);
            // 福祉用具貸与の詳細転送追加
            // 商品id
            Integer llFygId = outEntity.getFygId();
            comTlcSvplanResult.setShouhinId(llFygId);
            // 福祉用具貸与算定区分
            Integer liHiwariKbn = outEntity.getYRentalF();
            comTlcSvplanResult.setHiwariKbn(liHiwariKbn);
            // 貸与事業所点数
            Double ldFygTani = outEntity.getSvTani();
            // 日割り区分が有効で、なおかつ福祉用具貸与だった場合
            if (liHiwariKbn > CommonConstants.NUMBER_0
                    || CommonConstants.STR_17
                            .equals(lsScode.substring(CommonConstants.NUMBER_0, CommonConstants.NUMBER_2))
                    || CommonConstants.STR_67
                            .equals(lsScode.substring(CommonConstants.NUMBER_0, CommonConstants.NUMBER_2))
                    || CommonConstants.STR_3320
                            .equals(lsScode.substring(CommonConstants.NUMBER_0, CommonConstants.NUMBER_4))
                    || CommonConstants.STR_3519
                            .equals(lsScode.substring(CommonConstants.NUMBER_0, CommonConstants.NUMBER_4))) {
                // 端数処理取得
                Integer liHasuu = kghCmnCalc01Logic.getFukushiyouguHasuuFlg(llSvjigyo);
                // 単位数を算定して送信させる
                switch (liHiwariKbn) {
                case CommonConstants.INT_2:
                    // 半額
                    double ldTmp = ldFygTani / 2.0;
                    switch (liHasuu) {
                    case CommonConstants.INT_1:// 切捨て
                        ldFygTani = Math.floor(ldTmp);
                        break;
                    case CommonConstants.INT_2: // 切り上げ
                        ldFygTani = Math.ceil(ldTmp);
                        break;
                    default:// 四捨五入
                        ldFygTani = (double) Math.round(ldTmp);
                        break;
                    }
                    break;
                case CommonConstants.INT_1, CommonConstants.INT_3, CommonConstants.INT_4:
                    // 日割s
                    if (liHiwariKbn == CommonConstants.NUMBER_3) {
                        // 日割30日
                        liMx = 30;
                    } else if (liHiwariKbn == CommonConstants.NUMBER_4) {
                        // 日割31日
                        liMx = 31;
                    }
                    ldTmp = ldFygTani * liTtl / liMx;
                    switch (liHasuu) {
                    case CommonConstants.INT_1:// 切捨て
                        ldFygTani = Math.floor(ldTmp);
                        break;
                    case CommonConstants.INT_2: // 切り上げ
                        ldFygTani = Math.ceil(ldTmp);
                        break;
                    default:// 四捨五入
                        ldFygTani = (double) Math.round(ldTmp);
                        break;
                    }
                    break;
                default:
                    break;
                }
            }
            comTlcSvplanResult.setFygTani(ldFygTani);
            // 小規模多機能型のサービス提供区分を追加
            // サービスコード英数字化
            Integer liShoukiboK = outEntity.getShoukiboKbn();
            if (liShoukiboK > CommonConstants.NUMBER_0 && kghCmnF01Logic.chkKasanService(
                    lsScode.substring(CommonConstants.NUMBER_0, CommonConstants.NUMBER_2), lsScode, isYymm + "/01",
                    llSvjigyo)) {
                liShoukiboK = CommonConstants.NUMBER_0;
            }
            comTlcSvplanResult.setShoukiboKbn(liShoukiboK);
            results.add(comTlcSvplanResult);

        }
        if (llRow > CommonConstants.NUMBER_0) {
            // サービス事業所が変わったら変わる前のサービス事業所の概要レコードを作成する
            MakeGaiyouInDto inDto = new MakeGaiyouInDto();
            // 支援事業所ID
            inDto.setAlShien(llShien);
            // 利用者ID
            inDto.setAlUser(llUser);
            // 処理年月
            inDto.setAsYymm(lsYymmdd);
            // 法人ID
            inDto.setAlHoujin(llHoujin);
            // 施設ID
            inDto.setAlShisetu(llSisetu);
            // サービス事業所
            inDto.setAlSvjigyo(llCmpSvjigyo);
            inDto.setAiYojituKbn(liYojitiKbn);
            inDto.setAlTerm(llTermid);
            inDto.setAiDay(liDay);
            int res = this.ufMakeGaiyou(inDto);
            if (res != CommonConstants.NUMBER_0) {
                return CommonConstants.INT_MINUS_1;
            }
        }
        // G3C45R001_NEXT提供票連携_機能強化_訪看Ⅰ５
        // 自動分割処理を呼び出す
        if (llTermid > CommonConstants.NUMBER_12) {
            try {
                ufHok15Div(results, adsListHok15, adsListItemuseHok15);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        for (ComTlcSvplanResult res : results) {
            int count = comTlcSvplanResultMapper.insertSelective(res);
            if (count < CommonConstants.NUMBER_0) {
                return CommonConstants.NUMBER_0;
            }
        }
        if (llRow > CommonConstants.NUMBER_0) {
            return CommonConstants.NUMBER_1;
        }
        return CommonConstants.NUMBER_0;
    }

    /**
     * uf_del_plan：共通ＤＢ(Group0)側の計画を削除する
     * 
     * @param alShien     支援事業所ID
     * @param asYymm      処理年月(yyyy/mm)
     * @param useridList  利用者ID（配列）
     * @param llSvjigyoId 事業所ID
     * @return Integer 0：正常終了 -1：異常終了
     * <AUTHOR>
     */
    public Integer delPlan(Integer alShien, String asYymm, List<Integer> useridList, Integer llSvjigyoId) {
        CmnTensouAll3ByCriteriaInEntity inEntity = new CmnTensouAll3ByCriteriaInEntity();
        // 支援事業所ID
        inEntity.setAlShien(alShien);
        // 処理年月(yyyy/mm)
        inEntity.setAsYymm(asYymm);
        // 利用者ID（配列）
        inEntity.setAlUserList(useridList);
        // ids_cmn_tensou_plan_all_3
        List<CmnTensouAll3OutEntity> cmnTensouAll3List = cmnTucPlanRiyouSelectMapper
                .findCmnTensouAll3ByCriteria(inEntity);
        // 計画削除（論理削除）削除フラグに１をセットする
        if (CollectionUtils.size(cmnTensouAll3List) > CommonConstants.INT_0) {
            for (CmnTensouAll3OutEntity obj : cmnTensouAll3List) {
                Integer llUser = obj.getUserid();

                // 利用者IDか事業所IDが変わったらその新しいIDでFilter式を作成し
                // Filterする
                // ids_plan_tensou_all_0
                CmnTensouPlanAll0ByCriteriaInEntity all0InEntity = new CmnTensouPlanAll0ByCriteriaInEntity();
                // 支援事業所ID
                all0InEntity.setAlShien(alShien);
                // 利用者ID（配列）
                all0InEntity.setAlUserList(useridList);
                // 処理年月(yyyy/mm)
                all0InEntity.setAsYymmdd(asYymm);
                List<CmnTensouPlanAll0OutEntity> cmnTensouPlanAll0OutList = comTlcSvplanResultSelectMapper
                        .findCmnTensouPlanAll0ByCriteria(all0InEntity);
                // ls_filtstring = "userid = " + string(ll_user) + " AND sv_jigyo_id = " +
                // string(ll_svjigyo)
                List<CmnTensouPlanAll0OutEntity> filteredList = cmnTensouPlanAll0OutList.stream()
                        .filter(entity -> entity.getUserid() == llUser && entity.getSvJigyoId() == llSvjigyoId)
                        .collect(Collectors.toList());
                // Filterされたデータに削除フラグを立てる
                for (CmnTensouPlanAll0OutEntity outEntity : filteredList) {
                    ComTlcSvplanResultCriteria criteria = new ComTlcSvplanResultCriteria();
                    criteria.createCriteria()
                            // 枝番
                            .andEdanoEqualTo(outEntity.getEdano())
                            // 法人ID
                            .andHoujinIdEqualTo(outEntity.getHoujinId())
                            // 項目コード
                            .andItemcodeEqualTo(outEntity.getItemcode())
                            // 施設ID
                            .andShisetuIdEqualTo(outEntity.getShisetuId())
                            // 事業者ID
                            .andSvJigyoIdEqualTo(outEntity.getSvJigyoId())
                            // 日割り区分
                            .andSwchFlgEqualTo(outEntity.getSwchFlg())
                            // 利用者ID
                            .andUseridEqualTo(outEntity.getUserid())
                            // 記録日.
                            .andYymmYmdEqualTo(outEntity.getYymmYmd());
                    ComTlcSvplanResult comTlcSvplanResult = new ComTlcSvplanResult();
                    comTlcSvplanResult.setDelFlg(CommonConstants.DEL_FLG_ON);
                    int count = comTlcSvplanResultMapper
                            .updateByCriteriaSelective(comTlcSvplanResult, criteria);
                    if (count < CommonConstants.INT_0) {
                        return CommonConstants.INT_MINUS_1;
                    }
                }
            }
        }
        return CommonConstants.INT_0;
    }

    /**
     * uf_del_other_shien_jigyo：別の支援事業所で作成された共通ＤＢ(Group0)側の計画、点数を削除する
     * 
     * @param alShien     支援事業所ID
     * @param asYymm      処理年月(yyyy/mm)
     * @param useridList  利用者ID（配列）
     * @param llSvjigyoId 事業所ID
     * @return Integer 0：正常終了 -1：異常終了
     * <AUTHOR>
     */
    public Integer delOtherShienJigyo(Integer alShien, String asYymm, List<Integer> useridList, Integer llSvjigyoId) {
        // 計画削除（論理削除）削除フラグに１をセットする
        CmnTensouAll3ByCriteriaInEntity inEntity = new CmnTensouAll3ByCriteriaInEntity();
        // 支援事業所ID
        inEntity.setAlShien(alShien);
        // 処理年月(yyyy/mm)
        inEntity.setAsYymm(asYymm);
        // 利用者ID（配列）
        inEntity.setAlUserList(useridList);
        // ids_cmn_tensou_plan_all_3
        List<CmnTensouAll3OutEntity> cmnTensouAll3List = cmnTucPlanRiyouSelectMapper
                .findCmnTensouAll3ByCriteria(inEntity);
        // 転送元（ケアマネ側Group3）にある事業所の計画をせべて削除（論理削除）する
        if (CollectionUtils.size(cmnTensouAll3List) > CommonConstants.INT_0) {
            for (CmnTensouAll3OutEntity item : cmnTensouAll3List) {
                Integer llUser = item.getUserid();
                CmnTensouPointAll0ByCriteriaInEntity cmnTensouPointAll0ByCriteriaInEntity = new CmnTensouPointAll0ByCriteriaInEntity();
                // 処理年月(yyyy/mm)
                cmnTensouPointAll0ByCriteriaInEntity.setAsYymmdd(asYymm);
                // 支援事業所ID
                cmnTensouPointAll0ByCriteriaInEntity.setAlShien(alShien);
                // 利用者ID（配列）
                cmnTensouPointAll0ByCriteriaInEntity.setAlUserList(useridList);
                List<CmnTensouPointAll0OutEntity> cmnTensouPointAll0List = comTlcSvplanPointSelectMapper
                        .findCmnTensouPointAll0ByCriteria(cmnTensouPointAll0ByCriteriaInEntity);
                List<CmnTensouPointAll0OutEntity> filteredList = cmnTensouPointAll0List.stream()
                        .filter(entity -> entity.getUserid() == llUser && entity.getSvJigyoId() == llSvjigyoId)
                        .collect(Collectors.toList());
                if (filteredList.size() > CommonConstants.NUMBER_0) {
                    // Filterされたデータに削除フラグを立てる
                    for (CmnTensouPointAll0OutEntity outEntity : filteredList) {
                        ComTlcSvplanPointCriteria criteria = new ComTlcSvplanPointCriteria();
                        criteria.createCriteria()
                                // 法人ID
                                .andHoujinIdEqualTo(outEntity.getHoujinId())
                                // 施設ID
                                .andShisetuIdEqualTo(outEntity.getShisetuId())
                                // 事業者ID
                                .andSvJigyoIdEqualTo(outEntity.getSvJigyoId())
                                // サービス種類コード
                                .andSvtypeEqualTo(outEntity.getSvtype())
                                // 利用者ID
                                .andUseridEqualTo(outEntity.getUserid())
                                // 記録日.
                                .andYymmYmdEqualTo(outEntity.getYymmYmd());
                        ComTlcSvplanPoint comTlcSvplanPoint = new ComTlcSvplanPoint();
                        comTlcSvplanPoint.setDelFlg(CommonConstants.DEL_FLG_ON);
                        int count = comTlcSvplanPointMapper.updateByCriteriaSelective(comTlcSvplanPoint, criteria);
                        if (count < CommonConstants.INT_0) {
                            return CommonConstants.INT_MINUS_1;
                        }
                    }
                }
            }
        }
        return CommonConstants.INT_0;
    }

    /**
     * uf_make_max_eda：版の最大値をもとめ、その最大枝版の１００の位に１００を足した値を作成する
     * 
     * @param alShien     自事業者ID
     * @param alUser      利用者ID（配列）
     * @param asYymm      提供年月(yyyy/mm)
     * @param userid      利用者ID
     * @param llSvjigyoId 事業者ID
     * @return Integer 最大はilMaxEda
     * <AUTHOR>
     */
    public Integer makeMaxEda(Integer alShien, List<Integer> alUser, String asYymm, Integer userid,
            Integer llSvjigyoId) {
        // ・最大はll_max_edaに入る
        Integer llMaxEda = CommonConstants.NUMBER_0;
        if (asYymm.length() >= CommonConstants.INT_7) {
            // ds_cmn_tensou_plan_all_02
            CmnTensouPlanAll0ByCriteriaInEntity inEntity = new CmnTensouPlanAll0ByCriteriaInEntity();
            inEntity.setAlUserList(alUser);
            inEntity.setAlShien(alShien);
            inEntity.setAsYymmdd(asYymm);
            List<CmnTensouPlanAll0OutEntity> cmnTensouPlanAll0List = comTlcSvplanResultSelectMapper
                    .findCmnTensouPlanAll0ByCriteria(inEntity);
            List<CmnTensouPlanAll0OutEntity> filteredList = cmnTensouPlanAll0List.stream()
                    .filter(entity -> entity.getUserid() == userid && entity.getSvJigyoId() == llSvjigyoId).toList();
            // 利用者+サービス事業+処理年月で計画の中で1番大きい枝版を取得する
            for (CmnTensouPlanAll0OutEntity outEntity : filteredList) {
                Integer llCmpEda = outEntity.getEdano();
                if (llCmpEda > llMaxEda) {
                    llMaxEda = llCmpEda;
                }
            }
            // 最大の枝版の１００の位に１００を足した値を作成する
            Integer llMaxCnt = CommonConstants.NUMBER_0;
            if (llMaxEda == CommonConstants.NUMBER_0) {
                llMaxCnt = CommonConstants.NUMBER_1;
            } else {
                // edanoを振るときに使用します
                llMaxCnt = llMaxEda + CommonConstants.GROUP_NUM_100;
                llMaxCnt = llMaxCnt / CommonConstants.GROUP_NUM_100;
            }
            Integer ilMaxEda = llMaxCnt * CommonConstants.GROUP_NUM_100;
            return ilMaxEda;
        }
        return llMaxEda;
    }

    /**
     * uf_del_plan_nex：Group3に無くてGroup0にあるデータを削除する
     * 
     * @param alShien     支援事業所ID
     * @param asYymm      処理年月(yyyy/mm)
     * @param useridList  利用者ID（配列）
     * @param userid      利用者ID
     * @param llSvjigyoId 事業者ID
     * @return Integer 0：正常終了 -1：異常終了
     * <AUTHOR>
     */
    public Integer delPlanNex(Integer alShien, String asYymm, List<Integer> useridList, Integer userid,
            Integer llSvjigyoId) {
        // 削除したいデータを抽出する
        // ids_plan_tensou_all_0
        CmnTensouPlanAll0ByCriteriaInEntity all0InEntity = new CmnTensouPlanAll0ByCriteriaInEntity();
        // 支援事業所ID
        all0InEntity.setAlShien(alShien);
        // 利用者ID（配列）
        all0InEntity.setAlUserList(useridList);
        // 処理年月(yyyy/mm)
        all0InEntity.setAsYymmdd(asYymm);
        List<CmnTensouPlanAll0OutEntity> cmnTensouPlanAll0OutList = comTlcSvplanResultSelectMapper
                .findCmnTensouPlanAll0ByCriteria(all0InEntity);
        List<CmnTensouPlanAll0OutEntity> filteredList = cmnTensouPlanAll0OutList.stream()
                .filter(entity -> entity.getUserid() == userid && entity.getSvJigyoId() == llSvjigyoId).toList();
        // Filterされたデータに削除フラグを立てる
        if (CollectionUtils.size(filteredList) > CommonConstants.INT_0) {
            for (CmnTensouPlanAll0OutEntity outEntity : filteredList) {
                ComTlcSvplanResultCriteria criteria = new ComTlcSvplanResultCriteria();
                criteria.createCriteria()
                        // 枝番
                        .andEdanoEqualTo(outEntity.getEdano())
                        // 法人ID
                        .andHoujinIdEqualTo(outEntity.getHoujinId())
                        // 項目コード
                        .andItemcodeEqualTo(outEntity.getItemcode())
                        // 施設ID
                        .andShisetuIdEqualTo(outEntity.getShisetuId())
                        // 事業者ID
                        .andSvJigyoIdEqualTo(outEntity.getSvJigyoId())
                        // 日割り区分
                        .andSwchFlgEqualTo(outEntity.getSwchFlg())
                        // 利用者ID
                        .andUseridEqualTo(outEntity.getUserid())
                        // 記録日.
                        .andYymmYmdEqualTo(outEntity.getYymmYmd());
                ComTlcSvplanResult comTlcSvplanResult = new ComTlcSvplanResult();
                comTlcSvplanResult.setDelFlg(CommonConstants.DEL_FLG_ON);
                comTlcSvplanResultMapper
                        .updateByCriteriaSelective(comTlcSvplanResult, criteria);
            }
        }
        return CommonConstants.INT_0;
    }

    /**
     * 実績を取り込む uf_torikomi
     * 
     * @param istrMain      メインパラメータ
     * @param alShien       支援事業所ID
     * @param asYymm        処理年月(yyyy/MM)
     * @param alUser        利用者ID（配列）
     * @param asFilterShien サービス事業所を絞り込むFilter式
     * @param aiCalcFlg     再計算フラグ
     * @param gSysCd        システムコード
     * @param gShokuinId    gNdsIni.login.shokuin_id
     * @return 0:成功 -1:失敗
     */
    public long torikomi(String istrMain, Integer alShien, String asYymm, List<Integer> alUser,
            List<Integer> asFilterShien, Integer aiCalcFlg, String gSysCd, Integer gShokuinId)
            throws ExclusiveException {

        // データウィンドウオブジェクト設定
        // idsTorikomi3.setDataObject("ds_cmn_torikomi_all_3");
        // cmnTucPlanRiyouSelectMapper.findCmnTorikomiAll3ByCriteria
        // idsTorikomi0.setDataObject("ds_cmn_torikomi_plan_all_0");
        // ComTlcSvplanResultSelectMapper.findKghCmnUpdSvplanResultJByCriteria
        // ldsMaxOyaLine.setDataObject("ds_kgh_cmn_get_max_oya_line");
        // CmnTucPlanRiyouSelectMapper.findKghCmnGetMaxOyaLineByCriteria
        // idsCalcSet.setDataObject("ds_kgh_cmn_plan_all_calc_u_list");
        List<CmnPlanAllCalcsOutDto> idsCalcSet = null;
        // idsCalcGet.setDataObject("ds_kgh_cmn_plan_all_calc_u_list");

        // 訪看Ⅰ５関連の処理
        Integer llTermid = kghCmn03gFunc01Logic.getTermidEasy(asYymm);
        String lsTekiyoDate = nds3GkFunc01Logic.getCnvYymmdd(asYymm + "/01");

        // ldsListHok15.setDataObject("ds_kgh_cmn_list_hok15");ComMhcKmSelectMapper.findKghCmnListHok15ByCriteria
        // ldsListHok15.retrieve(lsTekiyoDate);
        KghCmnListHok15ByCriteriaInEntity listHok15in = new KghCmnListHok15ByCriteriaInEntity();
        listHok15in.setAsTargetDate(lsTekiyoDate);
        List<KghCmnListHok15OutEntity> ldsListHok15s = comMhcKmSelectMapper.findKghCmnListHok15ByCriteria(listHok15in);

        // ldsListItemuseHok15.setDataObject("ds_kgh_cmn_list_itemuse_hok15");ComMhcItemuseSelectMapper.findKghCmnListItemuseHok15ByCriteria
        // ldsListItemuseHok15.retrieve(llTermid);
        KghCmnListItemuseHok15ByCriteriaInEntity listItemuseHok15in = new KghCmnListItemuseHok15ByCriteriaInEntity();
        listItemuseHok15in.setAlTermid(llTermid);
        List<KghCmnListItemuseHok15OutEntity> ldsListItemuseHok15s = comMhcItemuseSelectMapper
                .findKghCmnListItemuseHok15ByCriteria(listItemuseHok15in);

        // 実績取込用データストアRetrieve
        // uf_retrieve_torikomi (alShien, asYymm, alUser, asFilter);

        CmnTensouPlanAll0ByCriteriaInEntity torikomi0In = new CmnTensouPlanAll0ByCriteriaInEntity();
        torikomi0In.setAlShien(alShien);
        torikomi0In.setAsYymmdd(asYymm);
        torikomi0In.setAlUserList(alUser);
        List<CmnTensouPlanAll0OutEntity> idsTorikomi0sdb = comTlcSvplanResultSelectMapper
                .findCmnTensouPlanAll0ByCriteria(torikomi0In);
        List<CmnTensouPlanAll0OutDto> idsTorikomi0s = idsTorikomi0sdb.stream().map(i -> {
            CmnTensouPlanAll0OutDto dto = new CmnTensouPlanAll0OutDto();
            BeanUtils.copyProperties(i, dto);
            dto.setUpdateFlg(0);
            return dto;
        }).toList();

        CmnTorikomiAll3ByCriteriaInEntity idsTorikomi3In = new CmnTorikomiAll3ByCriteriaInEntity();
        idsTorikomi3In.setAlShien(alShien);
        idsTorikomi3In.setAsYymm(asYymm);
        idsTorikomi3In.setAlUserList(alUser);
        List<CmnTorikomiAll3OutEntity> idsTorikomi3sdb = cmnTucPlanRiyouSelectMapper
                .findCmnTorikomiAll3ByCriteria(idsTorikomi3In);
        List<CmnTorikomiAll3OutDto> idsTorikomi3s = idsTorikomi3sdb.stream().map(i -> {
            CmnTorikomiAll3OutDto dto = new CmnTorikomiAll3OutDto();
            BeanUtils.copyProperties(i, dto);
            dto.setUpdateFlg(0);
            return dto;
        }).toList();

        // ケアマネテーブルcmn_tuc_service_plan_yの最大枝番取得用
        // idsEdanoGet.setDataObject("ds_cmn_pln_torikomi_eda_no");
        // 共通部テーブルcom_tlc_svplan_resultの最大枝番取得用：削除有り無しの関係なし
        // idsComEdanoGet.setDataObject("ds_cmn_pln_com_max_edano");
        // CmnPlnComMaxEdanoSelectMapper.findCmnPlnComMaxEdanoByCriteria
        // 共通部テーブルcom_tlc_svplan_resultの最大枝番取得用：予定行のみ
        // idsYoteifind.setDataObject("ds_cmn_torikomi_plan_all_01");
        // ComTlcSvplanResultSelectMapper.findCmnTorikomiPlanAll01ByCriteria
        // ケアマネ側に無い計画を取り込んだ場合にcom_tlc_svplan_resultに計画を作成するが、既にその計画がある場合があるので検索する
        // idsTorikomi0Find.setDataObject("ds_torikomi_0_find");
        // ComTlcSvplanResultSelectMapper.findTorikomi0FindByCriteria
        // oya_lineチェック用
        // idsChkMaxOyaLine.DataObject = "ds_kgh_cmn_chk_max_oya_line"
        // CmnTucPlanRiyouSelectMapper.findKghCmnChkMaxOyaLineByCriteria

        // 初期値の反映処理
        F3gkGetProfileInDto inDto = new F3gkGetProfileInDto();
        inDto.setShokuId(Integer.valueOf(CommonConstants.STR_ZERO)); // 職員ID
        inDto.setHoujinId(Integer.valueOf(CommonConstants.STR_ZERO));// 法人ID
        inDto.setShisetuId(Integer.valueOf(CommonConstants.STR_ZERO));// 施設ＩＤ
        inDto.setSvJigyoId(Integer.valueOf(CommonConstants.STR_ZERO));// 事業所ID
        inDto.setKinounameKnj(CommonConstants.SCREEN_NAME_CMN);// 画面名 スイッチ
        inDto.setSectionKnj(CommonConstants.SECTION_CMN_OP);// セクション
        inDto.setKeyKnj("sel_jisseki_torikomi_ho");// キー
        inDto.setAsDefault(CommonConstants.STR_ZERO);// 初期値
        inDto.setGsyscd(gSysCd); // システムコード
        int lsVl = Integer.parseInt(nds3GkFunc01Logic.getF3gkProfile(inDto));

        // 初期値設定
        List<Integer> maxOyaLineNo = new ArrayList<>();
        List<Integer> maxSortNo = new ArrayList<>();
        maxOyaLineNo.add(0);// dummy
        maxSortNo.add(0);// dummy
        for (int i = 1; i <= 31; i++) {
            maxOyaLineNo.add(0);
            maxSortNo.add(0);
        }

        // 実績取り込み処理
        for (Integer ilUser : alUser) {
            // 最大親ライン番号の取得
            // ldsMaxOyaLine.retrieve(alShien, asYymm, ilUser);
            KghCmnGetMaxOyaLineByCriteriaInEntity in = new KghCmnGetMaxOyaLineByCriteriaInEntity();
            in.setAlShien(alShien);
            in.setAsYymm(asYymm);
            in.setAlUserid(ilUser);
            List<KghCmnGetMaxOyaLineOutEntity> ldsMaxOyaLines = cmnTucPlanRiyouSelectMapper
                    .findKghCmnGetMaxOyaLineByCriteria(in);
            for (KghCmnGetMaxOyaLineOutEntity ldsMaxOyaLine : ldsMaxOyaLines) {
                String lsYymmD = ldsMaxOyaLine.getYymmD();
                Integer liOyaLineNo = ldsMaxOyaLine.getOyaLineNo();

                if (!StringUtils.isNumeric(lsYymmD))
                    continue;

                Integer liYymmD = Integer.parseInt(lsYymmD);
                maxOyaLineNo.set(liYymmD, liOyaLineNo);

                Integer liSortNo = ldsMaxOyaLine.getSortNo();
                maxSortNo.set(liYymmD, liSortNo != null ? liSortNo : 0); // ii_max_sort_no
            }

            Integer ilShien = alShien;
            String isYymm = asYymm;

            // 訪看Ⅰ５の処理
            for (CmnTensouPlanAll0OutDto idsTorikomi0 : idsTorikomi0s) {
                if (idsTorikomi0.getTermid() > 12 && idsTorikomi0.getUserid().equals(ilUser)
                        && (asFilterShien == null || asFilterShien.contains(idsTorikomi0.getSvJigyoId()))) {

                    // hok15Mrg // uf_hok15_mrg
                    break;
                }
            }

            // 実績取り込み処理 uf_torikomi_plan
            idsCalcSet = torikomiPlan(aiCalcFlg, ilUser, isYymm, ilShien, idsTorikomi0s, idsTorikomi3s, asFilterShien,
                    ldsListHok15s, ldsListItemuseHok15s, maxOyaLineNo, maxSortNo, gSysCd, gShokuinId);
        }
        // ケアマネ側（Group3）更新処理
        // li_ret = ids_torikomi_3.event ue_update(True,False) // 更新
        for (CmnTorikomiAll3OutDto idsTorikomi3s1 : idsTorikomi3s) {
            if (idsTorikomi3s1.getUpdateFlg() != 0) {
                CmnTucPlanRiyou row = new CmnTucPlanRiyou();
                // pk
                row.setShienId(idsTorikomi3s1.getShienId());
                row.setUserid(idsTorikomi3s1.getUserid());
                row.setYymmYm(idsTorikomi3s1.getYymmYm());
                row.setYymmD(idsTorikomi3s1.getYymmD());
                row.setSvJigyoId(idsTorikomi3s1.getSvJigyoId());
                row.setSvItemCd(idsTorikomi3s1.getSvItemCd());
                row.setEdaNo(idsTorikomi3s1.getEdaNo());
                // value
                BeanUtils.copyProperties(idsTorikomi3s1, row);
                if (idsTorikomi3s1.getUpdateFlg() == 1) {
                    cmnTucPlanRiyouMapper.insert(row);
                } else if (idsTorikomi3s1.getUpdateFlg() == 2) {
                    int updateCnt = cmnTucPlanRiyouMapper.updateByPrimaryKey(row);
                    if (updateCnt <= 0) {
                        throw new ExclusiveException();
                    }
                }
            }
        }

        // 取り込み元（Group0）の更新処理（ケアマネが付番したeda_noを更新）
        // li_ret = ids_torikomi_0.event ue_update(True,False) // 更新
        for (CmnTensouPlanAll0OutDto idsTorikomi0 : idsTorikomi0s) {
            if (idsTorikomi0.getUpdateFlg() != 0) {
                ComTlcSvplanResult row = new ComTlcSvplanResult();
                // pk
                row.setUserid(idsTorikomi0.getUserid());
                row.setYymmYmd(idsTorikomi0.getYymmYmd());
                row.setHoujinId(idsTorikomi0.getHoujinId());
                row.setShisetuId(idsTorikomi0.getShisetuId());
                row.setSvJigyoId(idsTorikomi0.getSvJigyoId());
                row.setItemcode(idsTorikomi0.getItemcode());
                row.setEdano(idsTorikomi0.getEdano());
                row.setSwchFlg(idsTorikomi0.getSwchFlg());
                // value
                BeanUtils.copyProperties(idsTorikomi0, row);
                if (idsTorikomi0.getUpdateFlg() == 1) {
                    comTlcSvplanResultMapper.insert(row);
                } else if (idsTorikomi0.getUpdateFlg() == 2) {
                    int updateCnt = comTlcSvplanResultMapper.updateByPrimaryKey(row);
                    if (updateCnt <= 0) {
                        throw new ExclusiveException();
                    }
                }
            }
        }

        // 実績再計算処理
        if (aiCalcFlg == 1 && idsCalcSet != null && idsCalcSet.size() > 0) {
            // performRecalculation(astrMain); //
            // ==============================================================
            // 実績再計算対象のキーがあれば実績再計算を行う
            // ==============================================================
            // lstr_calc.ds_us = ids_calc_set //
            // lstr_calc.ds_ret = ids_calc_get //
            // lstr_calc.b_full_calc = TRUE // True：完全新規計算の場合
            // lstr_calc.i_non_err = 0 // 計画が無い利用者をエラーにする(1)／しない(0)のフラグ
            // lstr_calc.str_main = astr_main // メインパラメータ
            // lstr_calc.i_caller = 2 // 呼び出し元：一括計算(0)・計画複写(1)・週間一括取込(2)
            // lstr_calc.b_set_zengetu = TRUE // 「前月までの短期入所利用日数」をセットする
            // lstr_calc.s_wintitle = "計画転送・実績取込：一括" // タイトル
            // lstr_calc.i_yoji = 0 // 実績で計算

            // --------------------------------------------
            // 一括計算状況表示画面起動
            // --------------------------------------------
            // OpenWithparm( w_kgh_cmn_1katu_calc_ing , lstr_calc )
        }

        return 0;
    }

    /**
     * 関数名 ： n_kgh_cmn_plan_tensou_all.sru_uf_cmn2svjigyo 処理概要
     * ：ケアマネから、指定したサービス提供事業所の計画を転送する
     * 
     * @param alShienId   転送元支援事業所
     * @param asYymm      転送対象の処理年月 yyyy/mm
     * @param alUser      転送利用者 配列
     * @param alSvjigyoId 転送先サービス提供事業所
     * @param shokuinId   職員ID
     * 
     * @return 戻り値 1:転送完了,0:処理なし、ﾏｲﾅｽ値:ｴﾗｰ
     * <AUTHOR> 李晨昊
     */
    public Integer getCmn2svjigyo(Integer alShienId, String asYymm, List<Integer> alUser, Integer alSvjigyoId,
            Integer shokuinId) throws Exception {

        // 変数
        boolean lbTensou = false;

        // 予定実績からは、支援事業所の指定と利用者の指定は不可能なため、指定がない場合は、ケアマネ側で全て転送する
        if (CommonConstants.NUMBER_0.equals(alShienId) && (CollectionUtils.size(alUser) == CommonConstants.NUMBER_1
                && CommonConstants.NUMBER_0.equals(alUser.getFirst()))) {
            // 処理用データストアの生成
            CmnTensouAllShienidByCriteriaInEntity allShienInEntity = new CmnTensouAllShienidByCriteriaInEntity();
            allShienInEntity.setAlSvJigyoId(alSvjigyoId);
            allShienInEntity.setAsYymm(asYymm);
            List<CmnTensouAllShienidOutEntity> allShienOutList = servicePlan2SelectMapper
                    .findCmnTensouAllShienidByCriteria(allShienInEntity);

            // 初期値設定
            List<Integer> cnt3List = Arrays.asList(CommonConstants.NUMBER_0);

            // 転送しなければならないユーザのuseridを配列に保持する
            for (CmnTensouAllShienidOutEntity allShienOutInfo : allShienOutList) {
                Integer llShienId = allShienOutInfo.getShienId();

                CmnPlnModPlanChkByCriteriaInEntity planChkInEntity = new CmnPlnModPlanChkByCriteriaInEntity();
                planChkInEntity.setAsYymm(asYymm);
                planChkInEntity.setAlJigyo(alSvjigyoId);
                planChkInEntity.setAlShienId(llShienId);
                List<CmnPlnModPlanChkOutEntity> planChkOutList = cmnTucPlanRiyouSelectMapper
                        .findCmnPlnModPlanChkByCriteria(planChkInEntity);

                // 修正のあった利用者のuseridを続けて保持する
                for (CmnPlnModPlanChkOutEntity planChkOutInfo : planChkOutList) {
                    cnt3List.add(planChkOutInfo.getUserid());
                }

                // svplan_resultに行があり、ケアマネに無い人を取得する。
                CmnDelSvplanResultSdlUseridByCriteriaInEntity svplanInEntity = new CmnDelSvplanResultSdlUseridByCriteriaInEntity();
                svplanInEntity.setLShienId(llShienId);
                svplanInEntity.setLUseridList(cnt3List);
                svplanInEntity.setSYm(CommonDtoUtil.objValToString(asYymm));
                svplanInEntity.setLSvJigyoId(alSvjigyoId);
                List<CmnDelSvplanResultSdlUseridOutEntity> svPlanOutList = servicePlanResult1SelectMapper
                        .findCmnDelSvplanResultSdlUseridByCriteria(svplanInEntity);
                for (CmnDelSvplanResultSdlUseridOutEntity svPlanOutInfo : svPlanOutList) {
                    cnt3List.add(svPlanOutInfo.getUserid());
                }

                if (CollectionUtils.size(alUser) == CommonConstants.NUMBER_1
                        && CommonConstants.NUMBER_0.equals(alUser.getFirst())) {
                    Integer liRtn = tensousyori(llShienId, asYymm, alUser, alSvjigyoId, shokuinId);
                    if (liRtn > 0) {
                        lbTensou = true;
                    }
                }
            }
        } else {
            Integer liRtn = tensousyori(alShienId, asYymm, alUser, alSvjigyoId, shokuinId);
            if (liRtn > 0) {
                lbTensou = true;
            }
        }
        if (lbTensou) {
            return CommonConstants.NUMBER_1;
        }

        return CommonConstants.NUMBER_0;
    }

    /**
     * 関数名 ： n_kgh_cmn_plan_tensou_all.sru_uf_tensou 処理概要 ：計画転送処理をする
     * 
     * @param alShienId   支援事業所ID
     * @param asYymm      処理年月(yyyy/mm) 例・・(2002/01)
     * @param alUser      利用者ID（配列）
     * @param alSvjigyoId サービス事業所
     * @param shokuinId   職員ID
     * 
     * @return 戻り値 1:転送完了,0:処理なし、ﾏｲﾅｽ値:ｴﾗｰ
     * <AUTHOR> 李晨昊
     * @throws Exception
     */
    public Integer tensousyori(Integer alShien, String asYymm, List<Integer> alUser, Integer alSvjigyoId,
            Integer shokuinId) throws Exception {

        boolean lbTensou = false;
        String lsSvcd = kghCmnF01Logic.getSvJigyoCd(alShien);
        // termid取得
        Integer llTermid = kghCmn03gFunc01Logic.getTermidEasy(asYymm);
        // 日付のスラッシュを削除する
        String lsTekiyoDate = nds3GkFunc01Logic.getCnvYymmdd(asYymm.concat(CommonConstants.STRING_FIRST_DAY));
        // ds_kgh_cmn_list_hok15
        KghCmnListHok15ByCriteriaInEntity listHokInEntity = new KghCmnListHok15ByCriteriaInEntity();
        listHokInEntity.setAsTargetDate(lsTekiyoDate);
        List<KghCmnListHok15OutEntity> listHokOutList = comMhcKmSelectMapper
                .findKghCmnListHok15ByCriteria(listHokInEntity);
        // ds_kgh_cmn_list_itemuse_hok15
        KghCmnListItemuseHok15ByCriteriaInEntity itemusHokInEntity = new KghCmnListItemuseHok15ByCriteriaInEntity();
        itemusHokInEntity.setAlTermid(llTermid);
        List<KghCmnListItemuseHok15OutEntity> itemusHokOutList = comMhcItemuseSelectMapper
                .findKghCmnListItemuseHok15ByCriteria(itemusHokInEntity);
        // 計画転送に使用するデータストアをRetrieveする
        RetrieveOutDto retrieveOutInfo = retrieve(alShien, asYymm, alUser.stream().toArray(Integer[]::new),
                CommonDtoUtil.objValToString(alSvjigyoId));
        // サービス利用票明細
        List<CmnTensouAll3OutEntity> all3List = retrieveOutInfo.getCmnDelSvplanResultCmnOutEntityList();
        // 点数
        List<CmnPointTensou3OutEntity> point3List = retrieveOutInfo.getCmnSvPointOutEntityList();
        // サービス計画予定実績（19-1）
        List<CmnTensouPlanAll0OutEntity> planAll0List = retrieveOutInfo.getCmnTensouPlanAll0OutEntityList();
        // サービス別計画点数（19-2）
        List<CmnTensouPointAll0OutEntity> pointAll0List = retrieveOutInfo.getCmnTensouPointAll0OutEntityList();

        // uf_retrieveは他の処理からも呼ばれているので、削除用データストアは別途retrieve

        // 計画
        CmnTensouPlanDelByCriteriaInEntity planDelInEntity = new CmnTensouPlanDelByCriteriaInEntity();
        planDelInEntity.setAlShien(alShien);
        planDelInEntity.setAsYymmdd(asYymm);
        planDelInEntity.setAlUserList(alUser);
        List<CmnTensouPlanDelOutEntity> planDelOutList = comTlcSvplanResultSelectMapper
                .findCmnTensouPlanDelByCriteria(planDelInEntity);
        planDelOutList = planDelOutList.stream().filter(item -> item.getSvJigyoId().equals(alSvjigyoId))
                .collect(Collectors.toList());

        // 点数
        ComTlcSvplanPointCriteria pointDelOutInEntity = new ComTlcSvplanPointCriteria();
        pointDelOutInEntity.createCriteria().andShienJigyoIdNotEqualTo(alShien).andUseridIn(alUser)
                .andDelFlgEqualTo(CommonConstants.NUMBER_0);
        List<ComTlcSvplanPoint> pointDelOutList = comTlcSvplanPointMapper.selectByCriteria(pointDelOutInEntity);
        if (CollectionUtils.isNotEmpty(pointDelOutList)) {
            pointDelOutList = pointDelOutList.stream()
                    .filter(item -> StringUtils.left(item.getYymmYmd(), CommonConstants.NUMBER_7).equals(asYymm))
                    .collect(Collectors.toList());
        }

        // どれにも行が無い → 転送不要
        if (!(CollectionUtils.size(all3List) > 0 || CollectionUtils.size(planAll0List) > 0
                || CollectionUtils.size(pointAll0List) > 0)) {
            return CommonConstants.NUMBER_0;
        }

        // 計画転送処理
        // uf_del_point()
        // Group0の点数を削除する（物理削除）
        for (CmnTensouPointAll0OutEntity pointAllInfo : pointAll0List) {
            ComTlcSvplanPointCriteria delCriteria = new ComTlcSvplanPointCriteria();
            delCriteria.createCriteria().andUseridEqualTo(pointAllInfo.getUserid())// 利用者ＩＤ
                    .andYymmYmdEqualTo(pointAllInfo.getYymmYmd())// 処理年月
                    .andHoujinIdEqualTo(pointAllInfo.getHoujinId())// 法人ID
                    .andShisetuIdEqualTo(pointAllInfo.getShisetuId())// 施設ID
                    .andSvJigyoIdEqualTo(pointAllInfo.getSvJigyoId())// サービス事業者ＩＤ
                    .andSvtypeEqualTo(pointAllInfo.getSvtype());// サービス種類コード
            Integer delCnt = comTlcSvplanPointMapper.deleteByCriteria(pointDelOutInEntity);
            if (delCnt <= 0) {
                throw new ExclusiveException();
            }
        }

        // すでに転送されているGroup0の計画を削除する（概要レコードも）（論理削除）
        // 別支援事業所で作成された計画、点数を削除する
        // 初期設定
        Integer llCmpUser = CommonConstants.NUMBER_0;
        Integer llCmpSvjigyo = CommonConstants.NUMBER_0;
        if (CollectionUtils.size(all3List) > 0) {
            for (CmnTensouAll3OutEntity tensouAllInfo : all3List) {
                // ll_user = ids_tensou_all_3.GetItemNumber(ll_cnt1, "userid")
                Integer llUser = tensouAllInfo.getUserid();
                // ll_svjigyo = ids_tensou_all_3.GetItemNumber(ll_cnt1, "sv_jigyo_id")
                Integer llSvjigyo = tensouAllInfo.getSvJigyoId();
                if (!llCmpUser.equals(llUser)) {
                    llCmpUser = llUser;
                    llCmpSvjigyo = llSvjigyo;

                    // すでに転送されているGroup0の計画を削除する（概要レコードも）（論理削除）
                    for (CmnTensouPlanAll0OutEntity planAllInfo : planAll0List) {
                        if (planAllInfo.getUserid().equals(llUser) && planAllInfo.getSvJigyoId().equals(llCmpSvjigyo)) {
                            planAllInfo.setDelFlg(CommonConstants.NUMBER_1);
                        }
                    }
                    // 別支援事業所で作成された計画、点数を削除する
                    for (CmnTensouPlanDelOutEntity planDelOutInfo : planDelOutList) {
                        if (planDelOutInfo.getUserid().equals(llUser)
                                && planDelOutInfo.getSvJigyoId().equals(alSvjigyoId)) {
                            planDelOutInfo.setDelFlg(CommonConstants.NUMBER_1);
                        }
                    }
                }
            }
        }
        // 別支援事業所で作成された計画、点数を削除する
        for (CmnTensouPlanDelOutEntity planDelOutInfo : planDelOutList) {
            if (CommonConstants.NUMBER_1.equals(planDelOutInfo.getDelFlg())) {
                ComTlcSvplanResult updInfo = new ComTlcSvplanResult();
                // 更新時の共通カラム値設定処理
                CommonDaoUtil.setUpdateCommonColumns(updInfo, null);
                updInfo.setShienJigyoId(planDelOutInfo.getShienJigyoId());
                updInfo.setShokuId(planDelOutInfo.getShokuId());
                updInfo.setTimeStmp(planDelOutInfo.getTimeStmp());
                updInfo.setDelFlg(planDelOutInfo.getDelFlg());
                updInfo.setSeqno(planDelOutInfo.getSeqno());
                updInfo.setFygTani(planDelOutInfo.getFygTani());
                updInfo.setHiwariKbn(planDelOutInfo.getHiwariKbn());
                updInfo.setShoukiboKbn(planDelOutInfo.getShoukiboKbn());
                updInfo.setShouhinId(planDelOutInfo.getShouhinId());
                updInfo.setSvStartTime(planDelOutInfo.getSvStartTime());
                updInfo.setSvEndTime(planDelOutInfo.getSvEndTime());
                updInfo.setDay1(planDelOutInfo.getDay1());
                updInfo.setDay2(planDelOutInfo.getDay2());
                updInfo.setDay3(planDelOutInfo.getDay3());
                updInfo.setDay4(planDelOutInfo.getDay4());
                updInfo.setDay5(planDelOutInfo.getDay5());
                updInfo.setDay6(planDelOutInfo.getDay6());
                updInfo.setDay7(planDelOutInfo.getDay7());
                updInfo.setDay8(planDelOutInfo.getDay8());
                updInfo.setUserid(planDelOutInfo.getUserid());
                updInfo.setYymmYmd(planDelOutInfo.getYymmYmd());
                updInfo.setHoujinId(planDelOutInfo.getHoujinId());
                updInfo.setShisetuId(planDelOutInfo.getShisetuId());
                updInfo.setSvJigyoId(planDelOutInfo.getSvJigyoId());
                updInfo.setItemcode(planDelOutInfo.getItemcode());
                updInfo.setEdano(planDelOutInfo.getEdano());
                updInfo.setSwchFlg(planDelOutInfo.getSwchFlg());
                updInfo.setTermid(planDelOutInfo.getTermid());
                updInfo.setDay9(planDelOutInfo.getDay9());
                updInfo.setDay10(planDelOutInfo.getDay10());
                updInfo.setDay11(planDelOutInfo.getDay11());
                updInfo.setDay12(planDelOutInfo.getDay12());
                updInfo.setDay13(planDelOutInfo.getDay13());
                updInfo.setDay14(planDelOutInfo.getDay14());
                updInfo.setDay15(planDelOutInfo.getDay15());
                updInfo.setDay16(planDelOutInfo.getDay16());
                updInfo.setDay17(planDelOutInfo.getDay17());
                updInfo.setDay18(planDelOutInfo.getDay18());
                updInfo.setDay19(planDelOutInfo.getDay19());
                updInfo.setDay20(planDelOutInfo.getDay20());
                updInfo.setDay21(planDelOutInfo.getDay21());
                updInfo.setDay22(planDelOutInfo.getDay22());
                updInfo.setDay23(planDelOutInfo.getDay23());
                updInfo.setDay24(planDelOutInfo.getDay24());
                updInfo.setDay25(planDelOutInfo.getDay25());
                updInfo.setDay26(planDelOutInfo.getDay26());
                updInfo.setDay27(planDelOutInfo.getDay27());
                updInfo.setDay28(planDelOutInfo.getDay28());
                updInfo.setDay29(planDelOutInfo.getDay29());
                updInfo.setDay30(planDelOutInfo.getDay30());
                updInfo.setDay31(planDelOutInfo.getDay31());
                updInfo.setTotal(planDelOutInfo.getTotal());

                ComTlcSvplanResultCriteria whereInfo = new ComTlcSvplanResultCriteria();
                whereInfo.createCriteria()
                        // 利用者ＩＤ userid
                        .andUseridEqualTo(planDelOutInfo.getUserid())
                        // 処理年月 yymm_ymd
                        .andYymmYmdEqualTo(planDelOutInfo.getYymmYmd())
                        // 法人ID houjin_id
                        .andHoujinIdEqualTo(planDelOutInfo.getHoujinId())
                        // 施設ID shisetu_id
                        .andShisetuIdEqualTo(planDelOutInfo.getShisetuId())
                        // サービス事業者ＩＤ sv_jigyo_id
                        .andSvJigyoIdEqualTo(planDelOutInfo.getSvJigyoId())
                        // 項目コード itemcode
                        .andItemcodeEqualTo(planDelOutInfo.getItemcode())
                        // 枝番 edano
                        .andEdanoEqualTo(planDelOutInfo.getEdano())
                        // 予定／実績フラグ swch_flg
                        .andSwchFlgEqualTo(planDelOutInfo.getSwchFlg());
                Integer updCnt = comTlcSvplanResultMapper.updateByCriteriaSelective(updInfo, whereInfo);
                if (updCnt <= 0) {
                    throw new ExclusiveException();
                }
            }
        }

        // uf_tensou()で抽出されている別支援事業所の点数用データストアのデータを削除する
        for (

        ComTlcSvplanPoint pointDelOutInfo : pointDelOutList) {
            ComTlcSvplanPointCriteria delInfo = new ComTlcSvplanPointCriteria();
            delInfo.createCriteria()
                    // 利用者ＩＤ userid
                    .andUseridEqualTo(pointDelOutInfo.getUserid())
                    // 処理年月 yymm_ymd
                    .andYymmYmdEqualTo(pointDelOutInfo.getYymmYmd())
                    // 法人ID houjin_id
                    .andHoujinIdEqualTo(pointDelOutInfo.getHoujinId())
                    // 施設ID shisetu_id
                    .andShisetuIdEqualTo(pointDelOutInfo.getShisetuId())
                    // サービス事業者ＩＤ sv_jigyo_id
                    .andSvJigyoIdEqualTo(pointDelOutInfo.getSvJigyoId())
                    // サービス種類コード svtype
                    .andSvtypeEqualTo(pointDelOutInfo.getSvtype());
            Integer delCnt = comTlcSvplanPointMapper.deleteByCriteria(delInfo);
            if (delCnt <= 0) {
                throw new ExclusiveException();
            }
        }

        // svplan_resultテーブル(共通部分)の計画のゴミデータを削除する
        Cmn2svjigyoDelInDto svjigyoDelInEntity = new Cmn2svjigyoDelInDto();
        // 支援事業所ID
        svjigyoDelInEntity.setAlShien(alShien);
        // 利用者ID
        svjigyoDelInEntity.setAlUser(alUser.stream().toArray(Integer[]::new));
        // 処理年月
        svjigyoDelInEntity.setAsYymm(asYymm);
        svjigyoDelInEntity.setCmnTensouPlanAll0OutEntityList(planAll0List);
        svjigyoDelInEntity.setCmnTensouPointAll0OutEntityList(pointAll0List);

        cmn2svjigyoDel(svjigyoDelInEntity);

        // 利用者で抽出し転送する
        // 転送する利用者（al_user[]に入っている利用者IDでループする
        for (Integer user : alUser) {
            // 他の支援事業者の計画転送をチェック
            String lsShy = kghCmnF01Logic.getSvriyoTiming(alShien, user, asYymm);
            if (kghCmnF01Logic.cmnChkKeikakuChoufuku()) {
                continue;
            }
            List<CmnTensouAll3OutEntity> filterTensouAllList = new ArrayList<>();

            if (lsSvcd.equals(CommonConstants.JCD_23031)) {
                // ls_flt_3plan = "(userid = " + String( al_user[ll_cnt1] ) + ") AND (" +
                // as_filter + ") and (left(scode,2)='33' or left(scode,2)='35')"
                filterTensouAllList = all3List.stream()
                        .filter(item -> item.getUserid().equals(user) && item.getSvJigyoId().equals(alSvjigyoId)
                                && (StringUtils.left(item.getScode(), 2).equals(CommonConstants.STR_33)
                                        || StringUtils.left(item.getScode(), 2).equals(CommonConstants.STR_NUM_35)))
                        .collect(Collectors.toList());

            } else if (lsSvcd.equals(CommonConstants.JCD_43031)) {
                // ls_flt_3plan = "(userid = " + String( al_user[ll_cnt1] ) + ") AND (" +
                // as_filter + ") and (left(scode,2)='33' or (left(scode,2)='35' and
                // mid(scode,3,2) <> '22' and mid(scode,3,2) <> '23'))"
                filterTensouAllList = all3List.stream().filter(item -> item.getUserid().equals(user)
                        && item.getSvJigyoId().equals(alSvjigyoId)
                        && (StringUtils.left(item.getScode(), 2).equals(CommonConstants.STR_33) || (StringUtils
                                .left(item.getScode(), 2).equals(CommonConstants.STR_NUM_35)
                                && !StringUtils.substring(item.getScode(), 2, 4).equals(CommonConstants.NUM_STR_22)
                                && !StringUtils.substring(item.getScode(), 2, 4).equals(CommonConstants.NUM_STR_23))))
                        .collect(Collectors.toList());
            } else if (lsSvcd.equals(CommonConstants.JCD_63031)) {
                // ls_flt_3plan = "(userid = " + String( al_user[ll_cnt1] ) + ") AND (" +
                // as_filter + ") and (left(scode,2)='36')"
                filterTensouAllList = all3List.stream()
                        .filter(item -> item.getUserid().equals(user) && item.getSvJigyoId().equals(alSvjigyoId)
                                && StringUtils.left(item.getScode(), 2).equals(CommonConstants.NUM_STR_36))
                        .collect(Collectors.toList());
            } else {
                // "userid = " + String( al_user[ll_cnt1] ) + " AND (" + as_filter + ")"
                filterTensouAllList = all3List.stream()
                        .filter(item -> item.getUserid().equals(user) && item.getSvJigyoId().equals(alSvjigyoId))
                        .collect(Collectors.toList());
            }

            List<CmnTensouPlanAll0OutEntity> filterPlanAllList = planAll0List.stream()
                    .filter(item -> item.getUserid().equals(user) && item.getSvJigyoId().equals(alSvjigyoId))
                    .collect(Collectors.toList());
            // どれにも行が無い → 転送不要
            if (!(CollectionUtils.size(filterTensouAllList) > 0 || CollectionUtils.size(filterPlanAllList) > 0)) {
                continue;
            }

            // 枝版の最大を求め１００の位に１００を足した値を作成し、最大枝番を作成する
            Integer maxEda = makeMaxEda(alShien, alUser, lsShy, user, alSvjigyoId);

            // Group3に無くてGroup0にあるデータを削除する 2004/10/27 s.kato ADD ＮＥＸより使用する
            // uf_del_plan_nex
            for (CmnTensouPlanAll0OutEntity planAllInfo : planAll0List) {
                if (planAllInfo.getSvJigyoId().equals(llCmpSvjigyo)) {
                    planAllInfo.setDelFlg(CommonConstants.NUMBER_1);
                }
            }

            Integer liRtn = tensouPlan(maxEda, shokuinId, asYymm, all3List, listHokOutList, itemusHokOutList);
            if (liRtn > 0) {
                lbTensou = true;
            }

            // 利用者IDでFilterをかけそれぞれで転送処理する（点数）
            if (lsSvcd.equals(CommonConstants.JCD_23031)) {
                // ls_flt_3point = "(userid = " + String( al_user[ll_cnt1] ) + ") AND (" +
                // as_filter + ") and (svtype = '33' or svtype = '35')"
                point3List = point3List.stream()
                        .filter(item -> item.getUserid().equals(user) && item.getSvJigyoId().equals(alSvjigyoId)
                                && (item.getSvtype().equals(CommonConstants.STR_33)
                                        || item.getSvtype().equals(CommonConstants.STR_NUM_35)))
                        .collect(Collectors.toList());

            } else if (lsSvcd.equals(CommonConstants.JCD_43031)) {
                // ls_flt_3point = "(userid = " + String( al_user[ll_cnt1] ) + ") AND (" +
                // as_filter + ") and (svtype = '33' or svtype = '35')"
                point3List = point3List.stream()
                        .filter(item -> item.getUserid().equals(user) && item.getSvJigyoId().equals(alSvjigyoId)
                                && (item.getSvtype().equals(CommonConstants.STR_33)
                                        || item.getSvtype().equals(CommonConstants.STR_NUM_35)))
                        .collect(Collectors.toList());
            } else if (lsSvcd.equals(CommonConstants.JCD_63031)) {
                // ls_flt_3point = "(userid = " + String( al_user[ll_cnt1] ) + ") AND (" +
                // as_filter + ") and (svtype = '36')"
                point3List = point3List.stream()
                        .filter(item -> item.getUserid().equals(user) && item.getSvJigyoId().equals(alSvjigyoId)
                                && item.getSvtype().equals(CommonConstants.NUM_STR_36))
                        .collect(Collectors.toList());
            } else {
                // "userid = " + String( al_user[ll_cnt1] ) + " AND (" + as_filter + ")"
                point3List = point3List.stream()
                        .filter(item -> item.getUserid().equals(user) && item.getSvJigyoId().equals(alSvjigyoId))
                        .collect(Collectors.toList());
            }
        }

        if (lbTensou) {
            return CommonConstants.NUMBER_1;
        }

        // サービス事業者側（Group0）更新処理
        // // サービス利用票明細
        // List<CmnTensouAll3OutEntity> tensouAllList =
        // retrieveOutInfo.getCmnDelSvplanResultCmnOutEntityList();
        for (CmnTensouAll3OutEntity tensouAllInfo : all3List) {
            CmnTucPlanRiyou updInfo = new CmnTucPlanRiyou();
            updInfo.setScode(tensouAllInfo.getScode());
            updInfo.setYOv30Fl(tensouAllInfo.getYOv30Fl());
            updInfo.setHenkouTime(tensouAllInfo.getHenkouTime());
            updInfo.setSortNo(tensouAllInfo.getSortNo());
            updInfo.setYRentalF(tensouAllInfo.getYRentalF());
            updInfo.setTensouFl(tensouAllInfo.getTensouFl());
            updInfo.setShoukiboKbn(tensouAllInfo.getShoukiboKbn());
            updInfo.setYymmD(tensouAllInfo.getYymmD());
            updInfo.setSvTani(tensouAllInfo.getSvTani());
            updInfo.setFygId(tensouAllInfo.getFygId());
            updInfo.setYDay01(tensouAllInfo.getYDay01());
            updInfo.setYDay02(tensouAllInfo.getYDay02());
            updInfo.setYDay03(tensouAllInfo.getYDay03());
            updInfo.setYDay04(tensouAllInfo.getYDay04());
            updInfo.setYDay05(tensouAllInfo.getYDay05());
            updInfo.setYDay06(tensouAllInfo.getYDay06());
            updInfo.setYDay07(tensouAllInfo.getYDay07());
            updInfo.setYDay08(tensouAllInfo.getYDay08());
            updInfo.setYDay09(tensouAllInfo.getYDay09());
            updInfo.setYDay10(tensouAllInfo.getYDay10());
            updInfo.setShienId(tensouAllInfo.getShienId());
            updInfo.setUserid(tensouAllInfo.getUserid());
            updInfo.setYymmYm(tensouAllInfo.getYymmYm());
            updInfo.setSvJigyoId(tensouAllInfo.getSvJigyoId());
            updInfo.setSvItemCd(tensouAllInfo.getSvItemCd());
            updInfo.setEdaNo(tensouAllInfo.getEdaNo());
            updInfo.setTermid(tensouAllInfo.getTermid());
            updInfo.setSvStartTime(tensouAllInfo.getSvStartTime());
            updInfo.setSvEndTime(tensouAllInfo.getSvEndTime());
            updInfo.setYDay11(tensouAllInfo.getYDay11());
            updInfo.setYDay12(tensouAllInfo.getYDay12());
            updInfo.setYDay13(tensouAllInfo.getYDay13());
            updInfo.setYDay14(tensouAllInfo.getYDay14());
            updInfo.setYDay15(tensouAllInfo.getYDay15());
            updInfo.setYDay16(tensouAllInfo.getYDay16());
            updInfo.setYDay17(tensouAllInfo.getYDay17());
            updInfo.setYDay18(tensouAllInfo.getYDay18());
            updInfo.setYDay19(tensouAllInfo.getYDay19());
            updInfo.setYDay20(tensouAllInfo.getYDay20());
            updInfo.setYDay21(tensouAllInfo.getYDay21());
            updInfo.setYDay22(tensouAllInfo.getYDay22());
            updInfo.setYDay23(tensouAllInfo.getYDay23());
            updInfo.setYDay24(tensouAllInfo.getYDay24());
            updInfo.setYDay25(tensouAllInfo.getYDay25());
            updInfo.setYDay26(tensouAllInfo.getYDay26());
            updInfo.setYDay27(tensouAllInfo.getYDay27());
            updInfo.setYDay28(tensouAllInfo.getYDay28());
            updInfo.setYDay29(tensouAllInfo.getYDay29());
            updInfo.setYDay30(tensouAllInfo.getYDay30());
            updInfo.setYDay31(tensouAllInfo.getYDay31());
            updInfo.setYTotal(tensouAllInfo.getYTotal());
            updInfo.setTensouTime(tensouAllInfo.getTensouTime());
            updInfo.setEdaBack(tensouAllInfo.getEdaBack());

            CmnTucPlanRiyouCriteria whereInfo = new CmnTucPlanRiyouCriteria();
            whereInfo.createCriteria()
                    // 支援事業者ID shien_id
                    .andShienIdEqualTo(tensouAllInfo.getShienId())
                    // 利用者ID userid
                    .andUseridEqualTo(tensouAllInfo.getUserid())
                    // サービス提供年月 yymm_ym
                    .andYymmYmEqualTo(tensouAllInfo.getYymmYm())
                    // サービス提供年月（変更日） yymm_d
                    .andYymmDEqualTo(tensouAllInfo.getYymmD())
                    // サービス事業者ID sv_jigyo_id
                    .andSvJigyoIdEqualTo(tensouAllInfo.getSvJigyoId())
                    // サービス項目ID sv_item_cd
                    .andSvItemCdEqualTo(tensouAllInfo.getSvItemCd())
                    // 枝番 eda_no
                    .andEdaNoEqualTo(tensouAllInfo.getEdaNo());
            Integer updateCnt = cmnTucPlanRiyouMapper.updateByCriteriaSelective(updInfo, whereInfo);
            // 検索結果が0以上の場合、処理5.の週間表マスタ更新処理を行う。
            if (updateCnt <= 0) {
                throw new ExclusiveException();
            }
        }
        // // 点数
        List<CmnPointTensou3OutEntity> pointOytList = retrieveOutInfo.getCmnSvPointOutEntityList();
        for (CmnPointTensou3OutEntity pointOytInfo : point3List) {
            CmnTucServicePoint updInfo = new CmnTucServicePoint();
            updInfo.setIryohiKojoFl(pointOytInfo.getIryohiKojoFl());
            updInfo.setUserid(pointOytInfo.getUserid());
            updInfo.setYymmYm(pointOytInfo.getYymmYm());
            updInfo.setHoujinId(pointOytInfo.getHoujinId());
            updInfo.setShisetuId(pointOytInfo.getShisetuId());
            updInfo.setSvJigyoId(pointOytInfo.getSvJigyoId());
            updInfo.setSvtype(pointOytInfo.getSvtype());
            updInfo.setPoint(pointOytInfo.getPoint());
            updInfo.setGaiPoint(pointOytInfo.getGaiPoint());
            updInfo.setNumber(pointOytInfo.getNumber());
            updInfo.setGaiNumber(pointOytInfo.getGaiNumber());

            CmnTucServicePointCriteria whereInfo = new CmnTucServicePointCriteria();
            whereInfo.createCriteria()
                    // userid
                    .andUseridEqualTo(pointOytInfo.getUserid())
                    // yymm_ym
                    .andYymmYmEqualTo(pointOytInfo.getYymmYm())
                    // houjin_id
                    .andHoujinIdEqualTo(pointOytInfo.getHoujinId())
                    // shisetu_id
                    .andShisetuIdEqualTo(pointOytInfo.getShisetuId())
                    // sv_jigyo_id
                    .andSvJigyoIdEqualTo(pointOytInfo.getSvJigyoId())
                    // svtype
                    .andSvtypeEqualTo(pointOytInfo.getSvtype());
            Integer updateCnt = cmnTucServicePointMapper.updateByCriteriaSelective(updInfo, whereInfo);
            // 検索結果が0以上の場合、処理5.の週間表マスタ更新処理を行う。
            if (updateCnt <= 0) {
                throw new ExclusiveException();
            }
        }

        // // サービス計画予定実績（19-1）
        // List<CmnTensouPlanAll0OutEntity> planAllList =
        // retrieveOutInfo.getCmnTensouPlanAll0OutEntityList();
        for (CmnTensouPlanAll0OutEntity planAllInfo : planAll0List) {
            ComTlcSvplanResult updInfo = new ComTlcSvplanResult();
            updInfo.setShienJigyoId(planAllInfo.getShienJigyoId());
            updInfo.setShokuId(planAllInfo.getShokuId());
            updInfo.setTimeStmp(planAllInfo.getTimeStmp());
            updInfo.setDelFlg(planAllInfo.getDelFlg());
            updInfo.setSeqno(planAllInfo.getSeqno());
            updInfo.setFygTani(planAllInfo.getFygTani());
            updInfo.setHiwariKbn(planAllInfo.getHiwariKbn());
            updInfo.setShoukiboKbn(planAllInfo.getShoukiboKbn());
            updInfo.setShouhinId(planAllInfo.getShouhinId());
            updInfo.setSvStartTime(planAllInfo.getSvStartTime());
            updInfo.setSvEndTime(planAllInfo.getSvEndTime());
            updInfo.setDay1(planAllInfo.getDay1());
            updInfo.setDay2(planAllInfo.getDay2());
            updInfo.setDay3(planAllInfo.getDay3());
            updInfo.setDay4(planAllInfo.getDay4());
            updInfo.setDay5(planAllInfo.getDay5());
            updInfo.setDay6(planAllInfo.getDay6());
            updInfo.setDay7(planAllInfo.getDay7());
            updInfo.setDay8(planAllInfo.getDay8());
            updInfo.setUserid(planAllInfo.getUserid());
            updInfo.setYymmYmd(planAllInfo.getYymmYmd());
            updInfo.setHoujinId(planAllInfo.getHoujinId());
            updInfo.setShisetuId(planAllInfo.getShisetuId());
            updInfo.setSvJigyoId(planAllInfo.getSvJigyoId());
            updInfo.setItemcode(planAllInfo.getItemcode());
            updInfo.setEdano(planAllInfo.getEdano());
            updInfo.setSwchFlg(planAllInfo.getSwchFlg());
            updInfo.setTermid(planAllInfo.getTermid());
            updInfo.setDay9(planAllInfo.getDay9());
            updInfo.setDay10(planAllInfo.getDay10());
            updInfo.setDay11(planAllInfo.getDay11());
            updInfo.setDay12(planAllInfo.getDay12());
            updInfo.setDay13(planAllInfo.getDay13());
            updInfo.setDay14(planAllInfo.getDay14());
            updInfo.setDay15(planAllInfo.getDay15());
            updInfo.setDay16(planAllInfo.getDay16());
            updInfo.setDay17(planAllInfo.getDay17());
            updInfo.setDay18(planAllInfo.getDay18());
            updInfo.setDay19(planAllInfo.getDay19());
            updInfo.setDay20(planAllInfo.getDay20());
            updInfo.setDay21(planAllInfo.getDay21());
            updInfo.setDay22(planAllInfo.getDay22());
            updInfo.setDay23(planAllInfo.getDay23());
            updInfo.setDay24(planAllInfo.getDay24());
            updInfo.setDay25(planAllInfo.getDay25());
            updInfo.setDay26(planAllInfo.getDay26());
            updInfo.setDay27(planAllInfo.getDay27());
            updInfo.setDay28(planAllInfo.getDay28());
            updInfo.setDay29(planAllInfo.getDay29());
            updInfo.setDay30(planAllInfo.getDay30());
            updInfo.setDay31(planAllInfo.getDay31());
            updInfo.setTotal(planAllInfo.getTotal());

            ComTlcSvplanResultCriteria whereInfo = new ComTlcSvplanResultCriteria();
            whereInfo.createCriteria()
                    // 利用者ＩＤ userid
                    .andUseridEqualTo(planAllInfo.getUserid())
                    // 処理年月 yymm_ymd
                    .andYymmYmdEqualTo(planAllInfo.getYymmYmd())
                    // 法人ID houjin_id
                    .andHoujinIdEqualTo(planAllInfo.getHoujinId())
                    // 施設ID shisetu_id
                    .andShisetuIdEqualTo(planAllInfo.getShisetuId())
                    // サービス事業者ＩＤ sv_jigyo_id
                    .andSvJigyoIdEqualTo(planAllInfo.getSvJigyoId())
                    // 項目コード itemcode
                    .andItemcodeEqualTo(planAllInfo.getItemcode())
                    // 枝番 edano
                    .andEdanoEqualTo(planAllInfo.getEdano())
                    // 予定／実績フラグ swch_flg
                    .andSwchFlgEqualTo(planAllInfo.getSwchFlg());
            Integer updateCnt = comTlcSvplanResultMapper.updateByCriteriaSelective(updInfo, whereInfo);
            // 検索結果が0以上の場合、処理5.の週間表マスタ更新処理を行う。
            if (updateCnt <= 0) {
                throw new ExclusiveException();
            }
        }

        return CommonConstants.NUMBER_0;
    }

    private class localInt {
        Integer iHSt;
        Integer iDSt;
        Integer iDEd;
    }

    /**
     * 実績の取り込み uf_torikomi_plan
     * 
     * @param aiCalcFlg     再計算フラグ
     * @param ilUser        利用者ID
     * @param isYymm        処理年月(yyyy/MM)
     * @param ilShien       支援事業所ID
     * @param idsTorikomi0s
     * @param idsTorikomi3s
     * @param asFilterShien サービス事業所を絞り込むFilter式
     * @param gSysCd        システムコード
     * @param gShokuinId    gNdsIni.login.shokuin_id
     * @return 0:成功 -1:失敗
     */
    public List<CmnPlanAllCalcsOutDto> torikomiPlan(Integer aiCalcFlg, Integer ilUser, String isYymm, Integer ilShien,
            List<CmnTensouPlanAll0OutDto> idsTorikomi0s, List<CmnTorikomiAll3OutDto> idsTorikomi3s,
            List<Integer> asFilterShien, List<KghCmnListHok15OutEntity> ldsListHok15s,
            List<KghCmnListItemuseHok15OutEntity> ldsListItemuseHok15s, List<Integer> iiMaxOyaLineNo,
            List<Integer> maxSortNo, String gSysCd, Integer gShokuinId) throws ExclusiveException {
        List<CmnPlanAllCalcsOutDto> idsCalcSet = new ArrayList<>();
        ;

        // 外部利用型種別 lds_kgh_cmn_get_ndtype
        // ldsNdtype.DataObject = "ds_kgh_cmn_get_ndtype"
        // ComMhcItemuseSelectMapper.findKghSelSvitemCalc1PonNextByCriteria

        // 利用者IDでFilterをかけそれぞれで転送処理する（計画）
        // 取り込み元（サービス事業者側Group0)にFilter
        // isFiltstring = "userid = " + ilUser + " AND (" + asFilter + ")";
        // idsTorikomi0.setFilter(isFiltstring); //実績
        List<CmnTensouPlanAll0OutDto> idsTorikomi0s1 = idsTorikomi0s.stream().filter(i -> {
            return i.getUserid().equals(ilUser) && (asFilterShien == null || asFilterShien.contains(i.getSvJigyoId()));
        }).toList();

        // idsTorikomi3.setFilter(isFiltstring);
        // 取り込み先（ケアマネ側Group3)にFilter
        List<CmnTorikomiAll3OutDto> idsTorikomi3s1 = idsTorikomi3s.stream().filter(i -> {
            return i.getUserid().equals(ilUser) && (asFilterShien == null || asFilterShien.contains(i.getSvJigyoId()));
        }).toList();

        // 実績取り込み処理
        Integer llRow = idsTorikomi0s1.size();
        Integer llMax3 = idsTorikomi3s1.size();

        if (llRow > 0 && llMax3 > 0) {
            List<localInt> lstrIs = new ArrayList<>();

            // 介護保険情報を取得 now str_kgh_cmn_usr_info_3g f_kgh_cmn_get_userinfo_month()
            GetUserInfoMonthOutDto userInfoMonth = kghCmn03gFunc01Logic.getUserinfoMonth(ilUser, isYymm, ilShien);
            KghCmnUsrInfo3gDto istrUsrInfo = userInfoMonth.getUsrInfo();
            Integer liHMax = istrUsrInfo.getHokenMax();

            if (liHMax > 1) {
                // 月途中保険者変更の場合には、取込開始・終了期間を設定する
                for (int i = 0; i < liHMax; i++) {
                    localInt lstrI = new localInt();
                    LocalDate kiStYmd = LocalDate.parse(istrUsrInfo.getHoken().get(i).getKiStYmd());
                    lstrI.iHSt = kiStYmd.getDayOfMonth();
                    lstrI.iDSt = lstrI.iHSt;
                    if (i < liHMax - 1) {
                        LocalDate nextKiStYmd = LocalDate.parse(istrUsrInfo.getHoken().get(i + 1).getKiStYmd());
                        lstrI.iDEd = nextKiStYmd.getDayOfMonth() - 1;
                    } else {
                        lstrI.iDEd = 31;
                    }
                    lstrIs.add(lstrI);
                }
            }

            // ソート処理
            // ids_torikomi_3.setSort("yymm_d A, sv_jigyo_id A, sv_item_cd A, sv_start_time
            // A, eda_no A");
            idsTorikomi3s1 = idsTorikomi3s1.stream()
                    .sorted(Comparator.comparing(CmnTorikomiAll3OutDto::getYymmD)
                            .thenComparingInt(CmnTorikomiAll3OutDto::getSvJigyoId)
                            .thenComparingInt(CmnTorikomiAll3OutDto::getSvItemCd)
                            .thenComparing(CmnTorikomiAll3OutDto::getSvStartTime, Comparator.naturalOrder())
                            .thenComparingInt(CmnTorikomiAll3OutDto::getEdaNo))
                    .toList();

            // yymm_d の配列を作成する
            List<String> lsDdd = new ArrayList<>();
            for (CmnTorikomiAll3OutDto idsTorikomi3 : idsTorikomi3s1) {
                String dateValue = idsTorikomi3.getYymmD();
                if (!lsDdd.contains(dateValue)) {
                    lsDdd.add(dateValue);
                }
            }

            // yymm_d の配列でループ
            for (String lsDddNow : lsDdd) {
                // フィルター設定
                // ls_3filter = is_filtstring + " and yymm_d = '" + ls_ddd_now + "'"
                List<CmnTorikomiAll3OutDto> idsTorikomi3sf = idsTorikomi3s1.stream()
                        .filter(i -> lsDddNow.equals(i.getYymmD())).toList();
                llMax3 = idsTorikomi3sf.size();

                // 取込開始・終了期間の設定
                Integer liDSt = 0;
                Integer liDEd = 0;
                Integer li_ddd_now = Integer.valueOf(lsDddNow);
                if (liHMax > 1) {
                    // 月途中保険者変更の場合には、取込開始・終了期間を取得する
                    for (localInt lstr_is : lstrIs) {
                        if (li_ddd_now.equals(lstr_is.iHSt)) {
                            liDSt = lstr_is.iDSt;
                            liDEd = lstr_is.iDEd;
                            break;
                        }
                    }

                    // 無いと思うが、とりそこなった場合には規定値をセット
                    if (liDSt == 0) {
                        liDSt = 1;
                        liDEd = 31;
                    }

                } else {
                    // 通常時には規定値をセット
                    liDSt = 1;
                    liDEd = 31;
                }

                List<Integer> ll_jjj = new ArrayList<>();
                for (CmnTensouPlanAll0OutDto idsTorikomi0 : idsTorikomi0s1) {
                    Integer ll_svjigyo = idsTorikomi0.getSvJigyoId(); // サービス事業所ID
                    if (!ll_jjj.contains(ll_svjigyo)) {
                        ll_jjj.add(ll_svjigyo);
                    }
                }
                // 実績を空白化 save data to ids_torikomi_3
                if (ll_jjj.size() > 0) {
                    for (CmnTorikomiAll3OutDto ids_torikomi_3 : idsTorikomi3sf) {
                        Integer ll_svjigyo = ids_torikomi_3.getSvJigyoId(); // サービス事業所ID

                        // 実績データが訪問看護Ⅰ５の場合は後続の処理を行わない
                        Integer ll_svitem = ids_torikomi_3.getSvItemCd(); // sv_item_cd
                        // String ls_work = ids_torikomi_3.getYymmYm() + "/" +
                        // ids_torikomi_3.getYymmD(); // 処理年月日
                        Integer ll_termid = ids_torikomi_3.getTermid(); // termid
                        // f_kgh_cmn_chk_hok15
                        if (kghPlanTensouKanriLogic.checkHok15(ll_svjigyo, ll_svitem, ldsListHok15s,
                                ldsListItemuseHok15s) && ll_termid > 12) {
                            continue;
                        }
                        // 2023/06/18 ADD END Yoshida G3C45R001_NEXT提供票連携_機能強化_訪看Ⅰ５
                        ids_torikomi_3.setUpdateFlg(2);
                        if (ll_jjj.contains(ll_svjigyo)) {
                            for (int i = 1; i <= 31; i++) { // 2011/03/24 : T.ishida
                                setField(ids_torikomi_3, String.format("jDay%02d", i), i);
                            }
                            ids_torikomi_3.setJTotal(0);
                        }
                    }
                }

                for (CmnTensouPlanAll0OutDto idsTorikomi0 : idsTorikomi0s1) {
                    // 検索に必要な値を取得する
                    Integer llShien = idsTorikomi0.getShienJigyoId();// 支援事業所ID
                    Integer llUser = idsTorikomi0.getUserid(); // 利用者ID
                    Integer llHoujin = idsTorikomi0.getHoujinId(); // 法人ID
                    Integer llShisetu = idsTorikomi0.getShisetuId(); // 施設ID
                    Integer llSvjigyo = idsTorikomi0.getSvJigyoId(); // サービス事業所ID
                    Integer llEda = idsTorikomi0.getEdano(); // 枝番

                    Integer llEdaBak = llEda;
                    Integer llSvitem = idsTorikomi0.getItemcode(); // sv_item_cd
                    Integer llTermid = idsTorikomi0.getTermid(); // sv_item_cd

                    String lsWork = idsTorikomi0.getYymmYmd(); // 処理年月
                    String lsYymmdd = lsWork;
                    String lsYymm = StringUtils.left(lsWork, 7);

                    String lsStartTime = idsTorikomi0.getSvStartTime();
                    String lsEndTime = idsTorikomi0.getSvEndTime();

                    // 小規模多機能型の提供区分 : 2008/08/05
                    Integer liShokibo = idsTorikomi0.getShoukiboKbn();
                    if (liShokibo == null)
                        liShokibo = 0;

                    Integer liHiwariKbn = idsTorikomi0.getHiwariKbn();
                    Integer llShohinId = idsTorikomi0.getShouhinId();
                    Double ldcFygTani = idsTorikomi0.getFygTani();

                    // 2011/01/25 <EMAIL>: Modify : 修正履歴№141対応
                    // サービスコードを取得 f_kgh_cmn_get_scode_from_itemuse
                    String lsScd = kghCmn03gFunc01Logic.getScodeFromItemuse(llSvjigyo, llSvitem, lsWork);

                    // サービス種別、サービス項目コードを取得
                    // 総合事業対応
                    String lsSvtype = StringUtils.left(lsScd, 2);
                    String lsSvcode = StringUtils.mid(lsScd, 2, 4);

                    // 実績済フラグを取得 f_cmn_is_user_jisseki_zumi
                    Integer liJissekiZumiFlg = kghCmnF01Logic.getUserJissekiZumi(llShien, llUser, lsYymm, lsDddNow);

                    // 2023/06/18 ADD START Yoshida G3C45R001_NEXT提供票連携_機能強化_訪看Ⅰ５
                    // 実績データが訪問看護Ⅰ５の場合は後続の処理を行わない
                    F3gkGetProfileInDto inDto = new F3gkGetProfileInDto();
                    inDto.setShokuId(Integer.valueOf(CommonConstants.STR_ZERO)); // 職員ID
                    inDto.setHoujinId(Integer.valueOf(CommonConstants.STR_ZERO));// 法人ID
                    inDto.setShisetuId(Integer.valueOf(CommonConstants.STR_ZERO));// 施設ＩＤ
                    inDto.setSvJigyoId(llShien);// 事業所ID
                    inDto.setKinounameKnj(CommonConstants.SCREEN_NAME_CMN);// 画面名 スイッチ
                    inDto.setSectionKnj(CommonConstants.SECTION_CMN_OP);// セクション
                    inDto.setKeyKnj("sel_jisseki_torikomi_ho");// キー
                    inDto.setAsDefault(CommonConstants.STR_ZERO);// 初期値
                    inDto.setGsyscd(gSysCd); // システムコード
                    Integer selJissekiTorikomi = Integer.parseInt(nds3GkFunc01Logic.getF3gkProfile(inDto));
                    Boolean ibSelJissekiTorikomi = false;
                    if (selJissekiTorikomi.equals(0)) {
                        // 時間等参照する：時間毎に（細かく・正確に）取り込む
                        ibSelJissekiTorikomi = true;
                    } else {
                        // 時間等参照しない：サービス毎にまとめて取り込む
                        ibSelJissekiTorikomi = false;
                    }
                    // f_kgh_cmn_chk_hok15
                    if (kghPlanTensouKanriLogic.checkHok15(llSvjigyo, llSvitem, ldsListHok15s, ldsListItemuseHok15s)
                            && llTermid > 12) {
                        if (!ibSelJissekiTorikomi && ("33".equals(lsSvtype) || "35".equals(lsSvtype))) {
                            // まとめる設定かつ外部サービスの場合は何もしない後続の処理実行
                        } else {
                            continue;
                        }
                    }
                    // END Yoshida G3C45R001_NEXT提供票連携_機能強化_訪看Ⅰ５

                    // 初期設定マスタの取得
                    Boolean lbHoumon = false;
                    // 本来はループ外に出すべき項目だが複数自事業所対応に備えて、敢えてループ内に残す
                    switch (lsSvtype) {
                    case "11", "12", "13", "14", "31", "34", "61", "62", "63", "64", "71", "76", "A1", "A2", "A3", "A4":
                        lbHoumon = true; // 訪問系サービスである
                        break;

                    case "15", "16", "65", "66", "72", "73", "74", "75", "77", "78", "A5", "A6", "A7", "A8":
                        // 時間等参照しない：サービス毎にまとめて取り込む
                        ibSelJissekiTorikomi = false;
                        lbHoumon = false; // 訪問系サービスでない

                        // プロファイル設定から取り込み方法を取得
                        inDto.setKeyKnj("sel_jisseki_torikomi");// キー
                        selJissekiTorikomi = Integer.parseInt(nds3GkFunc01Logic.getF3gkProfile(inDto));

                        if (selJissekiTorikomi.equals(0)) {
                            // 時間等参照する：時間毎に（細かく・正確に）取り込む
                            ibSelJissekiTorikomi = true;
                        }
                        break;

                    default:
                        ibSelJissekiTorikomi = true;
                        lbHoumon = false; // 訪問系サービスでない
                        break;
                    }

                    // 指定年月での利用者の介護保険情報を取得
                    String lsYymmD = StringUtils.right(idsTorikomi0.getYymmYmd(), 2);
                    int liYymmD = Integer.parseInt(lsYymmD);

                    // 期間の回数をカウント
                    int liReTotal = 0;
                    for (int liCnt1 = liDSt; liCnt1 <= liDEd; liCnt1++) {
                        Integer liDay = getField(idsTorikomi0, String.format("day%02d", liCnt1));
                        if (liDay == null)
                            liDay = 0;
                        liReTotal += liDay;
                    }

                    // 期間の回数が0の場合は取り込まない
                    if (liReTotal <= 0) {
                        continue;
                    }

                    // 既に取り込んだ事があれば通過する（主に加算） nothing...

                    // サービス側（Group0)の実績をケアマネ側(Group3)に取り込むため、同じ（計画転送した）計画を探す
                    // 為の検索条件を作成し、Findを行う
                    // "shien_id = llShien AND userid =llUser AND sv_jigyo_id = llSvjigyo " +
                    // "AND eda_back = llEda AND sv_item_cd = llSvitem",
                    CmnTorikomiAll3OutDto llFindrow = idsTorikomi3sf.stream().filter(i -> {
                        return i.getShienId().equals(llShien) && i.getUserid().equals(llUser)
                                && i.getSvJigyoId().equals(llSvjigyo) && i.getEdaBack().equals(llEda)
                                && i.getSvItemCd().equals(llSvitem);
                    }).findFirst().orElse(null);

                    // eda_bakで見つからない場合は、さらに開始時間、終了時間で検索する
                    if (llFindrow == null) {
                        List<Predicate<CmnTorikomiAll3OutDto>> lsFindstring = new ArrayList<>();
                        lsFindstring.add(i -> i.getShienId().equals(llShien));
                        lsFindstring.add(i -> i.getUserid().equals(llUser));
                        lsFindstring.add(i -> i.getSvJigyoId().equals(llSvjigyo));
                        lsFindstring.add(i -> i.getSvItemCd().equals(llSvitem));
                        final String flsStartTime = lsStartTime;
                        final String flsEndTime = lsEndTime;
                        lsFindstring.add(i -> i.getSvStartTime().equals(flsStartTime));
                        lsFindstring.add(i -> i.getSvEndTime().equals(flsEndTime));
                        if (((lsStartTime.compareTo("00:00") >= 0 && lsStartTime.compareTo("24:00") <= 0)
                                || (lsEndTime.compareTo("00:00") >= 0 && lsEndTime.compareTo("24:00") <= 0))
                                && ibSelJissekiTorikomi) {

                            // 時間のあるサービスは時間も見て検索する
                            if (liShokibo > 0) {
                                // shien_id = %d AND userid = %d
                                // AND sv_jigyo_id = %d AND sv_item_cd = %d
                                // AND sv_start_time = '%s' AND sv_end_time = '%s'
                                // AND shoukibo_kbn = %d
                                // llShien, llUser,
                                // llSvjigyo, llSvitem,
                                // lsStartTime, lsEndTime,
                                // liShokibo
                                final Integer fliShokibo = liShokibo;
                                lsFindstring.add(i -> i.getShoukiboKbn().equals(fliShokibo));
                            } else {
                                // shien_id = %d AND userid = %d
                                // AND sv_jigyo_id = %d AND sv_item_cd = %d
                                // AND sv_start_time = '%s' AND sv_end_time = '%s'
                                // AND (shoukibo_kbn = 0 OR shoukibo_kbn IS NULL)",
                                // llShien, llUser,
                                // llSvjigyo, llSvitem,
                                // lsStartTime, lsEndTime);
                                lsFindstring.add(i -> i.getShoukiboKbn() == null || i.getShoukiboKbn().equals(0));
                            }

                        } else {
                            // 時間が無いサービスはサービス種類までで検索する
                            if (liShokibo > 0) {
                                // "shien_id = %d AND userid = %d AND sv_jigyo_id = %d " +
                                // "AND sv_item_cd = %d AND shoukibo_kbn = %d",
                                // llShien, llUser, llSvjigyo, llSvitem, liShokibo);
                                final Integer fliShokibo = liShokibo;
                                lsFindstring.add(i -> i.getShoukiboKbn().equals(fliShokibo));
                            } else {
                                // "shien_id = %d AND userid = %d AND sv_jigyo_id = %d " +
                                // "AND sv_item_cd = %d AND (shoukibo_kbn = 0 OR shoukibo_kbn IS NULL)",
                                // llShien, llUser, llSvjigyo, llSvitem);
                                lsFindstring.add(i -> i.getShoukiboKbn() == null || i.getShoukiboKbn().equals(0));

                                if (lsSvtype.equals("17") || lsSvtype.equals("67") || lsSvcode.equals("3320")
                                        || lsSvcode.equals("3519")) {
                                    // 福祉用具貸与の場合
                                    // エスカレーション対応：同じ行に重複して取り込まれるのを回避する
                                    lsFindstring.add(i -> i.getDmyFygTorikomiFlg().equals(0));// += " AND
                                    // (dmy_fyg_torikomi_flg =
                                    // 0)";

                                    // 日割り区分が有効なら追加
                                    if (liHiwariKbn >= 0) {
                                        final Integer liHiwariKbnFinal = liHiwariKbn;
                                        lsFindstring.add(i -> i.getYRentalF().equals(liHiwariKbnFinal));// +=
                                        // String.format("
                                        // AND
                                        // (y_rental_f =
                                        // %d)",
                                        // liHiwariKbn);
                                    }

                                    // 単価が有効なら追加
                                    if (!StringUtils.substring(lsScd, 2, 4).equals(CommonConstants.STR_D_2) &&ldcFygTani >= 0) {
                                        lsFindstring.add(i -> i.getSvTani().equals(ldcFygTani));// += String.format("
                                        // AND (sv_tani = %f)",
                                        // ldcFygTani);
                                    }

                                    // 商品idが有効なら追加
                                    if (llShohinId > 0) {
                                        lsFindstring.add(i -> i.getFygId().equals(llShohinId));
                                        // += String.format("AND(fyg_id = %d)",llShohinId);
                                    }
                                } else {
                                    // 福祉用具貸与以外 : 2011/07/02時点では追加要素は無し
                                }
                            }
                        }

                        llFindrow = idsTorikomi3sf.stream()
                                .filter(lsFindstring.stream().reduce(x -> true, Predicate::and)).findFirst()
                                .orElse(null);
                    }

                    // ケアマネ側（Group3）に同じサービス（枝番も同じ）が見つかれば実績を取り込む

                    if (llFindrow == null) {
                        // ケアマネの計画に無い実績を取り込む
                        // 時間制御
                        switch (lsSvtype) {
                        case "17":
                        case "41":
                        case "42":
                        case "67":
                        case "44":
                        case "45":
                            // 福祉用具貸与、福祉用具購入、住宅改修
                            if (kghCmnF01Logic.chkKasanService(lsSvtype, lsScd, isYymm + "/01", llSvjigyo)) { // f_cmn_chk_kasan_service
                                // 加算サービスであれば時間を変更
                                lsStartTime = "77:77";
                                lsEndTime = "77:77";
                            } else {
                                lsStartTime = "99:99";
                                lsEndTime = "99:99";
                            }
                            break;

                        case "21":
                        case "22":
                        case "23":
                        case "24":
                        case "25":
                        case "26":
                        case "32":
                        case "36":
                        case "38":
                        case "37":
                        case "39":
                        case "27":
                        case "28":
                        case "68":
                        case "69":
                        case "79":
                        case "2A":
                        case "2B":
                            // 短期医療院追加
                            // 短期入所系、グループホーム、ケアハウス
                            if ("22".equals(lsSvtype) || "23".equals(lsSvtype) || "2A".equals(lsSvtype)) {
                                // 短期入所老健、短期入所医療、短期入所医院の日帰りサービスかチェック
                                String lsYmd = nds3GkFunc01Logic.getCnvYymmdd(isYymm + "/01"); // f_cnvyymmdd

                                // 日帰りサービス判定
                                SvrRiyoJokenByCriteriaInEntity in = new SvrRiyoJokenByCriteriaInEntity();
                                in.setLsSvtype(lsSvtype);
                                in.setLsSvcode(lsSvcode);
                                in.setLsYmd(lsYmd);
                                List<SvrRiyoJokenOutEntity> items = comMhcKmSelectMapper.findSvrRiyoJokenByCriteria(in);
                                String lsSvRiyoJkn = items.stream().findFirst().map(SvrRiyoJokenOutEntity::getSvRiyoJkn)
                                        .orElse("");

                                if (!"22".equals(lsSvRiyoJkn)) {
                                    // 日帰りサービス以外は提供時間に"88:88"をセット

                                    lsStartTime = "88:88";
                                    lsEndTime = "88:88";
                                }
                            } else {
                                lsStartTime = "88:88";
                                lsEndTime = "88:88";
                            }
                            break;

                        case "73":
                        case "75":
                        case "77":
                            // 小規模多機能型
                            if (liShokibo == 1 || liShokibo == 3 || liShokibo == 4) {
                                // 時間はそのまま
                            } else {
                                lsStartTime = "88:88";
                                lsEndTime = "88:88";
                            }
                            break;

                        case "33":
                        case "35":
                            // 外部利用関連対応
                            if (lsScd.startsWith("3311") || lsScd.startsWith("3312") || lsScd.startsWith("339")
                                    || lsScd.startsWith("3511") || lsScd.startsWith("3512")
                                    || lsScd.startsWith("359")) {

                                // 特定施設自身のサービス
                                lsStartTime = "88:88";
                                lsEndTime = "88:88";

                            } else if (lsScd.startsWith("3320") || lsScd.startsWith("3519")) {
                                // 外部利用な福祉用具貸与サービスの場合
                                lsStartTime = "99:99";
                                lsEndTime = "99:99";
                            }
                            break;

                        default:
                            // それ以外 f_cmn_chk_kasan_service
                            if (kghCmnF01Logic.chkKasanService(lsSvtype, lsScd, isYymm + "/01", llSvjigyo)) {
                                // 加算サービスであれば時間を変更
                                lsStartTime = "77:77";
                                lsEndTime = "77:77";
                            }

                            // 夜間訪問介護：基本サービス
                            if ("71111100".equals(lsScd)) {
                                lsStartTime = "88:88";
                                lsEndTime = "88:88";
                            }
                            break;
                        }

                        // 枝版を取得するため、同じサービスが無いか検索する
                        // "shien_id = %d AND userid = %d AND sv_jigyo_id = %d AND sv_item_cd = %d",
                        // llShien, llUser, llSvjigyo, llSvitem
                        Integer llEdaMaxNo = idsTorikomi3sf.stream().filter(i -> {
                            return llShien.equals(i.getShienId()) && llUser.equals(i.getUserid())
                                    && llSvjigyo.equals(i.getSvJigyoId()) && llSvitem.equals(i.getSvItemCd());
                        }).map(CmnTorikomiAll3OutDto::getEdaNo).max(Integer::compareTo).orElse(0);
                        llEdaMaxNo++; // 取得した最大値に1を足す

                        // ケアマネに無い計画の枝番が負の値でない場合、実績の枝番をそのまま eda_bak に付番する
                        if (llEdaBak < 0) {
                            llEdaBak = llEdaMaxNo;
                        }

                        // com_tlc_svplan_resultの予定と実績両方をみて、edabanの最大値を取得する
                        CmnPlnComMaxEdanoByCriteriaInEntity in = new CmnPlnComMaxEdanoByCriteriaInEntity();
                        in.setAlUser(ilUser);
                        in.setAsYymm(isYymm);
                        in.setAlHoujin(llHoujin);
                        in.setAlShisetu(llShisetu);
                        in.setAlJigyo(llSvjigyo);
                        in.setAlItemcode(llSvitem);
                        List<CmnPlnComMaxEdanoOutEntity> items = cmnPlnComMaxEdanoSelectMapper
                                .findCmnPlnComMaxEdanoByCriteria(in);
                        // long llRow4 = idsComEdanoGet.retrieve(llUser, lsYymm, llHoujin, llShisetu,
                        // llSvjigyo, llSvitem);
                        Integer llComMaxEda = items.stream().map(CmnPlnComMaxEdanoOutEntity::getEdano)
                                .max(Integer::compareTo).orElse(0);
                        if (llComMaxEda > 0) {
                            llEdaBak = llComMaxEda + 1;
                        }

                        // plus-ALPHA：追加した行のedanoもチェックする
                        // "shien_jigyo_id = %d AND userid = %d AND houjin_id = %d AND shisetu_id =%d
                        // AND sv_jigyo_id = %d AND itemcode = %d AND swch_flg = 1",
                        // llShien, llUser, llHoujin, llShisetu, llSvjigyo, llSvitem
                        Integer llEee = idsTorikomi0s1.stream().filter(i -> {
                            return llShien.equals(i.getShienJigyoId()) && llUser.equals(i.getUserid())
                                    && llHoujin.equals(i.getHoujinId()) && llShisetu.equals(i.getShisetuId())
                                    && llSvjigyo.equals(i.getSvJigyoId()) && llSvitem.equals(i.getItemcode())
                                    && i.getSwchFlg().equals(1);
                        }).map(CmnTensouPlanAll0OutDto::getEdano).max(Integer::compareTo).orElse(0);
                        if (llEdaBak <= llEee) {
                            llEdaBak = llEee + 1;
                        }

                        // 総合事業対応
                        lsSvtype = StringUtils.left(lsScd, 2); // LeftA
                        lsSvcode = StringUtils.substring(lsScd, 2, 6); // f_nds_mida

                        String lsGouseiSikKbn = "";
                        String lsSanteiTani = "";
                        String lsKikanJiki = "";
                        Integer liKaisuNisu = 0;
                        String lsGenTaiKbn = "";
                        BigDecimal ldcSvTani = BigDecimal.ZERO;
                        Integer llMax = 0;
                        Integer llTenkintype = 0;
                        Integer llDefault = 0;
                        switch (lsSvtype) {
                        case "A1":
                        case "A2":
                        case "A3":
                        case "A4":
                        case "A5":
                        case "A6":
                        case "A7":
                        case "A8":
                        case "A9":
                        case "AA":
                        case "AB":
                        case "AC":
                        case "AD":
                        case "AE":
                            // 総合事業の処理
                            // f_kgh_cmn_get_mhc_sm
                            MhcSmInDto inMhcSm = new MhcSmInDto();
                            inMhcSm.setSvJigyoId(llSvjigyo);
                            inMhcSm.setSvType(lsSvtype);
                            inMhcSm.setSvCode(lsSvcode);
                            inMhcSm.setShoriYm(lsYymm);
                            MhcSmOutDto outMhcSm = kghCmnWeek1Logic.getMhcSm(inMhcSm);
                            lsGouseiSikKbn = outMhcSm.getGouseiSikKbn();
                            lsSanteiTani = outMhcSm.getSanteiTani();
                            lsKikanJiki = outMhcSm.getKikanJiki();
                            liKaisuNisu = outMhcSm.getKaisuNisu();
                            lsGenTaiKbn = outMhcSm.getGentaiKbn();
                            // f_kgh_cmn_get_mhc_itemuse_sougou
                            MhcItemuseSougouInDto inSougou = new MhcItemuseSougouInDto();
                            inSougou.setSCode(lsScd);
                            inSougou.setSvjId(llSvjigyo);
                            inSougou.setItemId(llSvitem);
                            inSougou.setShoriYm(lsYymm);
                            MhcItemuseSougouOutDto outSougou = kghCmnWeek1Logic.getMhcItemuseSougou(inSougou);
                            ldcSvTani = outSougou.getSvTani();
                            llMax = outSougou.getMax();
                            llTenkintype = outSougou.getTenkinType();
                            llDefault = outSougou.getDefaultVal();
                            break;

                        default:
                            // 通常サービスの処理
                            // f_kgh_cmn_get_mhc_km
                            MhcKmInDto inKm = new MhcKmInDto();
                            inKm.setSvType(lsSvtype);
                            inKm.setSvCode(lsSvcode);
                            inKm.setShoriYm(lsYymm);
                            MhcKmOutDto outKm = kghCmnWeek1Logic.getMhcKm(inKm);
                            lsGouseiSikKbn = outKm.getGouseiSikKbn();
                            lsSanteiTani = outKm.getSanteiTani();
                            lsKikanJiki = outKm.getKikanJiki();
                            liKaisuNisu = outKm.getKaisuNisu();
                            lsGenTaiKbn = outKm.getGenTaiKbn();
                            // f_kgh_cmn_get_mhc_itemuse
                            MhcItemuseInDto inUse = new MhcItemuseInDto();
                            inUse.setAsScode(lsScd);
                            inUse.setAlSvj(llSvjigyo);
                            inUse.setAlItm(llSvitem);
                            inUse.setAsShoriYm(lsYymm);
                            MhcItemuseOutDto outUse = kghCmnWeek1Logic.getMhcItemuse(inUse);
                            ldcSvTani = outUse.getAdcSvTani();
                            llMax = outUse.getAlMax();
                            llTenkintype = outUse.getAlTenkintype();
                            llDefault = outUse.getAlDefault();
                            break;
                        }

                        // 加算サービス判定
                        boolean lbKasanSv = false;
                        String lsYymmYmd = isYymm + "/" + lsYymmD;

                        // 福祉用具貸与の場合
                        if ("17".equals(lsSvtype) || "67".equals(lsSvtype) || lsScd.startsWith("3320")
                                || lsScd.startsWith("3519")) {
                            if (kghCmnF01Logic.chkKasanService(lsSvtype, lsScd, isYymm + "/01", llSvjigyo)) { // f_cmn_chk_kasan_service
                                lbKasanSv = true;
                            }
                        } else {
                            // 加算サービス判定
                            if (kghCmnF01Logic.checkAdderService(lsScd, lsYymmYmd, llSvjigyo).equals(999)) { // f_cmn_chk_adder_service
                                if (kghCmnF01Logic.chkKasanException(lsScd, lsYymmYmd) == 0) {
                                    // 例外加算かどうかをチェックし、該当しなければ加算扱い
                                    // f_cmn_chk_kasan_exception
                                    lbKasanSv = true;
                                }
                            } else {
                                // 使用している関数が不正の為、正しく動作する関数に変更 f_cmn_is_special_place0_easy
                                if (kghCmnF01Logic.getSpecialPlace0Easy(lsScd, lsYymmYmd, llSvjigyo) == 1) {
                                    lbKasanSv = true;
                                }
                                // 共生型サービスのチェックを追加 f_cmn_is_kyosei_genzan
                                if (kghCmnF01Logic.getKyoseiGenzan(lsYymmYmd, lsScd) > 0) {
                                    lbKasanSv = true;
                                }
                                // G3C42L150_2021年4月改正対応_利用票_実績取込 add OBA 2021/03/24 start
                                // 通所リハ生活行為向上リハ継続減算のチェックを追加 f_cmn_is_riha_keizoku_genzan
                                if (kghCmnF01Logic.isRihaKeizokuGenzan(lsYymmYmd, lsScd) == 1) {
                                    lbKasanSv = true;
                                }
                                // 特定事業所加算Ⅴのチェックを追加 f_cmn_is_tokutei_jigyo_kasan
                                if (kghCmnF01Logic.isTokuteiJigyoKasan(lsYymmYmd, lsScd) == 1) {
                                    lbKasanSv = true;
                                }
                                // G3C42L150_2021年4月改正対応_利用票_実績取込 add OBA 2021/03/24 end

                                // 2024/05/29 ADD START Ken.Matsuda G3C45F188_ケアマネ_実績取込_過少サービス減算等取込不正
                                // 過少サービス減算のチェックを追加 f_cmn_is_kashou_service_genzan
                                if (kghCmnF01Logic.isKashouServiceGenzan(lsYymmYmd, lsScd) == 1) {
                                    lbKasanSv = true;
                                }
                                // サテライト体制未整備減算のチェックを追加 f_cmn_is_satellite_miseibi_genzan
                                if (kghCmnF01Logic.isSatelliteMiseibiGenzan(lsYymmYmd, lsScd) == 1) {
                                    lbKasanSv = true;
                                }
                                // 2024/05/29 ADD END Ken.Matsuda G3C45F188_ケアマネ_実績取込_過少サービス減算等取込不正
                            }
                        }

                        // 親番号・ソート順の変数初期化
                        Integer liOyaLineNo = 0;
                        Integer liSortNo = 0;

                        if (lbKasanSv) {
                            Integer liOyaLineNoKasan = 0;
                            Integer liOyaLineNoKasanTmp = 0;
                            Integer liSortKasan = 0;
                            Integer liSortKasanTmp = 0;

                            // 同じ提供事業所で親番号の最大値を探す
                            final String lsSvtypeFinal = lsSvtype;
                            List<CmnTorikomiAll3OutDto> idsTorikomi3sf1 = idsTorikomi3sf.stream().filter(i -> {
                                boolean r = llSvjigyo.equals(i.getSvJigyoId()) && lsYymmD.equals(i.getYymmD());
                                if (Arrays.asList("A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8")
                                        .contains(lsSvtypeFinal)) {
                                    r = r && lsSvtypeFinal.equals(i.getSvtype());
                                }
                                return r;
                            }).toList();

                            for (CmnTorikomiAll3OutDto idsTorikomi3 : idsTorikomi3sf1) {
                                String lsScodeTmp = idsTorikomi3.getScode();

                                // 例外加算かどうかをチェックする f_cmn_chk_kasan_exception
                                if (kghCmnF01Logic.chkKasanException(lsScodeTmp, lsYymmYmd) == 0) {
                                    if ("17".equals(lsSvtype) || "67".equals(lsSvtype)) {
                                        // 福祉用具貸与
                                        if (StringUtils.mid(lsScd, 4, 2).equals(StringUtils.mid(lsScodeTmp, 4, 2))) {
                                            liOyaLineNoKasanTmp = idsTorikomi3.getOyaLineNo();
                                            liSortKasanTmp = idsTorikomi3.getSortNo();
                                        }
                                    } else {
                                        // 福祉用具貸与以外
                                        liOyaLineNoKasanTmp = idsTorikomi3.getOyaLineNo();
                                        liSortKasanTmp = idsTorikomi3.getSortNo();
                                    }

                                    // 値を比較して入れ替え
                                    if (liOyaLineNoKasanTmp > liOyaLineNoKasan) {
                                        liOyaLineNoKasan = liOyaLineNoKasanTmp;
                                    }
                                    if (liSortKasanTmp > liSortKasan) {
                                        liSortKasan = liSortKasanTmp;
                                    }
                                }

                            }

                            // 親レコードが存在しない場合は取り込まない
                            if (liOyaLineNoKasanTmp == 0 && liSortKasanTmp == 0) {
                                continue;
                            }

                            if ("17".equals(lsSvtype) || "67".equals(lsSvtype)) {
                                // 福祉用具貸与の場合は、種類が一致している基本サービスの値を設定する
                                liOyaLineNo = liOyaLineNoKasan;
                                liSortNo = liSortKasan;
                            } else {
                                // 総合事業サービスの場合は条件を変える
                                // "SELECT max(oya_line_no), max(sort_no) FROM cmn_tuc_plan_riyou WHEREshien_id
                                // = %d AND userid = %d AND yymm_ym = '%s' AND yymm_d = '%s' AND sv_jigyo_id=
                                // %d",
                                // llSvjigyo, llSvjigyo, lsYymm, lsYymmD, llSvjigyo);
                                if (Arrays.asList("A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8").contains(lsSvtype)) {
                                    SvtypeOyaLineNoMaxByCriteriaInEntity inSvtype = new SvtypeOyaLineNoMaxByCriteriaInEntity();
                                    inSvtype.setIlShien(CommonDtoUtil.objValToString(ilShien));
                                    inSvtype.setIlUserid(CommonDtoUtil.objValToString(ilUser));
                                    inSvtype.setIsYm(lsYymm);
                                    inSvtype.setIsDd(lsDddNow);
                                    inSvtype.setLlSvJigyo(CommonDtoUtil.objValToString(llSvjigyo));
                                    inSvtype.setLsShu(lsSvtype);
                                    List<SvtypeOyaLineNoMaxOutEntity> outSvtype = cmnTucPlanRiyouSelectMapper
                                            .findSvtypeOyaLineNoMaxByCriteria(inSvtype);
                                    if (outSvtype != null && outSvtype.size() > 0) {
                                        liOyaLineNo = outSvtype.get(0).getOyaLineNo();
                                        liSortNo = outSvtype.get(0).getSortNo();
                                    }
                                } else {
                                    OyaLineNoMaxByCriteriaInEntity inOya = new OyaLineNoMaxByCriteriaInEntity();
                                    inOya.setIlShien(CommonDtoUtil.objValToString(ilShien));
                                    inOya.setIlUserid(CommonDtoUtil.objValToString(ilUser));
                                    inOya.setIsYm(lsYymm);
                                    inOya.setIsDd(lsDddNow);
                                    inOya.setLlSvJigyo(CommonDtoUtil.objValToString(llSvjigyo));
                                    List<OyaLineNoMaxOutEntity> outOya = cmnTucPlanRiyouSelectMapper
                                            .findOyaLineNoMaxByCriteria(inOya);
                                    if (outOya != null && outOya.size() > 0) {
                                        liOyaLineNo = outOya.get(0).getOyaLineNo();
                                        liSortNo = outOya.get(0).getSortNo();
                                    }
                                }

                                // 例外加算とは親子関係を持てないためチェックする
                                if (liOyaLineNo > 0) {
                                    KghCmnChkMaxOyaLineByCriteriaInEntity inMaxOya = new KghCmnChkMaxOyaLineByCriteriaInEntity();
                                    inMaxOya.setAlShienId(ilShien);
                                    inMaxOya.setAlUserid(ilUser);
                                    inMaxOya.setAsYymmYm(lsYymm);
                                    inMaxOya.setAsYymmD(lsDddNow);
                                    inMaxOya.setAlSvJigyoId(llSvjigyo);
                                    inMaxOya.setAlOyaLineNo(liOyaLineNo);
                                    // ll_rowcnt = ids_chk_max_oya_line.Retrieve( ll_shien, ll_user, ls_yymm,
                                    // ls_ddd_now, ll_svjigyo, li_oya_line_no )
                                    List<KghCmnChkMaxOyaLineOutEntity> outMaxOya = cmnTucPlanRiyouSelectMapper
                                            .findKghCmnChkMaxOyaLineByCriteria(inMaxOya);
                                    for (KghCmnChkMaxOyaLineOutEntity idsChkMaxOyaLine : outMaxOya) {
                                        String lsScodeTmp = StringUtils.left(idsChkMaxOyaLine.getScode(), 6);
                                        if (kghCmnF01Logic.chkKasanException(lsScodeTmp, lsYymmYmd) == 1) {
                                            // f_cmn_chk_kasan_exception liOyaLineNo = 0;
                                            break;
                                        }
                                    }
                                }

                                // 値を比較して入れ替え
                                if (liOyaLineNo < liOyaLineNoKasan) {
                                    liOyaLineNo = liOyaLineNoKasan;
                                }
                                if (liSortNo < liSortKasan) {
                                    liSortNo = liSortKasan;
                                }
                            }
                        } else {
                            iiMaxOyaLineNo.set(liYymmD, iiMaxOyaLineNo.get(liYymmD) + 1);
                            if (iiMaxOyaLineNo.get(liYymmD) != 0) {
                                maxSortNo.set(liYymmD, maxSortNo.get(liYymmD) + 1);
                            }
                        }

                        // 最終的な値の決定 2014/05/01 add itagaki
                        if (liOyaLineNo == 0) {
                            liOyaLineNo = iiMaxOyaLineNo.get(liYymmD);
                        }
                        if (liSortNo == 0) {
                            liSortNo = maxSortNo.get(liYymmD);
                        }

                        llFindrow = new CmnTorikomiAll3OutDto();
                        // save data ...ids_torikomi_3.InsertRow(0)
                        llFindrow.setUpdateFlg(1);
                        idsTorikomi3s.add(llFindrow);
                        llFindrow.setShienId(llShien);
                        llFindrow.setUserid(llUser);
                        llFindrow.setYymmYm(lsYymm);
                        llFindrow.setYymmD(lsDddNow);
                        llFindrow.setSvJigyoId(llSvjigyo);
                        llFindrow.setSvItemCd(llSvitem);
                        llFindrow.setEdaNo(llEdaMaxNo);

                        llFindrow.setTermid(llTermid);
                        // 実績取込_外部利用型種別
                        if ("33".equals(lsSvtype) || "35".equals(lsSvtype)) {
                            // 外部利用型特定施設入居、 外部利用型予防特定施設
                            // 外部サービスの場合は、ndtypeをsvtypeにセットする
                            KghCmnGetNdtypeByCriteriaInEntity inNdtype = new KghCmnGetNdtypeByCriteriaInEntity();
                            inNdtype.setAiIcd(llSvitem);
                            inNdtype.setAiSvjcdList(Arrays.asList(0, llSvjigyo));
                            inNdtype.setAsYmd(lsYymm + "/01");
                            // ll_sv_jigyo_id_array[1] = ll_svjigyo
                            // ll_sv_jigyo_id_array[2] = 0 //独自サービス以外を抽出する場合は「０」をセット
                            // lds_kgh_cmn_get_ndtype.retrieve(ll_svitem, ll_sv_jigyo_id_array, ls_yymm +
                            // "/01") //月途中保険者変更を考慮し月初日を指定
                            List<KghCmnGetNdtypeOutEntity> outNdtype = comMhcItemuseSelectMapper
                                    .findKghCmnGetNdtypeByCriteria(inNdtype);
                            String lsNdtype = outNdtype.stream().findFirst().map(KghCmnGetNdtypeOutEntity::getNdtype)
                                    .orElse(null);
                            if (StringUtils.isNotEmpty(lsNdtype)) {
                                lsSvtype = lsNdtype;
                            }
                        }

                        // 2014.05.26 M.Yamamura(JNET) DEL/ADD STR サービスコード英数字化
                        // llFindrow.setsvtype ( li_svtype );
                        // llFindrow.setsvcode ( li_svcode );
                        llFindrow.setSvtype(lsSvtype);
                        llFindrow.setSvcode(lsSvcode);
                        // 2014.05.26 M.Yamamura(JNET); DEL/ADD END サービスコード英数字化
                        llFindrow.setScode(lsScd);
                        llFindrow.setGouseiSikKbn(lsGouseiSikKbn);
                        llFindrow.setSvStartTime(lsStartTime);
                        llFindrow.setSvEndTime(lsEndTime);

                        if (ldcFygTani > 0) {
                            ldcSvTani = BigDecimal.valueOf(ldcFygTani);
                        }
                        llFindrow.setSvTani(ldcSvTani.doubleValue());
                        llFindrow.setFygId(llShohinId);
                        llFindrow.setYRentalF(liHiwariKbn);
                        llFindrow.setJRentalF(liHiwariKbn);

                        llFindrow.setSanteiTani(lsSanteiTani);

                        llFindrow.setKikanJiki(lsKikanJiki);
                        llFindrow.setKaisuNisu(liKaisuNisu);
                        llFindrow.setGenTaiKbn(lsGenTaiKbn);
                        llFindrow.setMax(llMax);
                        llFindrow.setTenkintype(llTenkintype);
                        llFindrow.setDefaultVal(llDefault);
                        llFindrow.setEdaBack(llEdaBak);
                        llFindrow.setOyaLineNo(liOyaLineNo); // 2014/05/01 add itagaki
                        // llFindrow.setsort_no ( 0 ); //2014/05/01 modify itagaki
                        llFindrow.setSortNo(liSortNo);
                        llFindrow.setShoukiboKbn(liShokibo);
                        llFindrow.setHenkouTime(Timestamp.valueOf(LocalDateTime.now()));
                        llFindrow.setTensouFl(1);
                        // 2012/10/25 modify itagaki
                        // llFindrow.setyotei_zumi_flg ( 1 );
                        // llFindrow.setjisseki_zumi_flg( 0 );
                        switch (liJissekiZumiFlg) {
                        case 1:
                            // 予定済み
                            llFindrow.setYoteiZumiFlg(1);
                            llFindrow.setJissekiZumiFlg(0);
                            break;
                        case 2:
                            // 実績済
                            llFindrow.setYoteiZumiFlg(1);
                            llFindrow.setJissekiZumiFlg(1);
                            break;
                        default:
                            llFindrow.setYoteiZumiFlg(0);
                            llFindrow.setJissekiZumiFlg(0);
                        }

                        for (int i = 1; i <= 31; i++) { // 2011/03/24 : T.ishida
                            setField(llFindrow, String.format("yDay%02d", i), i);
                            setField(llFindrow, String.format("jDay%02d", i), i);
                        }

                        // save to ids_torikomi_0
                        // ケアマネの計画に無い実績を取り込む-------------------------------------------------
                        // ↓↓↓↓ケアマネの付番したeda_noを取り込み元にも付番する--------------------------------
                        idsTorikomi0.setEdano(llEdaBak); // 2004/11/25 s.kato
                        idsTorikomi0.setUpdateFlg(2);
                        // ↑↑↑↑ケアマネの付番したeda_noを取り込み元にも付番する--------------------------------

                        // ---------------------------------------------------------------------------------------------------------------------------
                        // 実績と同じ枝版の計画が無い場合は作成する 2004/11/25 s.kato ADD
                        // ------------------------------------------------------------------------------------------------------------------------------------------------------------------
                        // 作成前に同じキーの計画があれば削除フラグをセットする 2005/07/19 s.kato ADD
                        // ADD同じキーだった計画に削除フラグをセットしたから更新する
                        // ll_torikomi_y_row = ids_torikomi_0_find.Retrieve( ll_shien , ll_user ,
                        // ll_houjin , ll_shisetu , ll_svjigyo , ll_svitem , ls_yymm , ls_start_time
                        // ,ls_end_time )
                        Torikomi0FindByCriteriaInEntity inFind = new Torikomi0FindByCriteriaInEntity();
                        inFind.setAlShien(ilShien);
                        inFind.setAlUser(ilUser);
                        inFind.setAlHoujin(llHoujin);
                        inFind.setAlShisetu(llShisetu);
                        inFind.setAlSvjigyo(llSvjigyo);
                        inFind.setAlSvitem(llSvitem);
                        inFind.setAsYymm(lsYymm);
                        inFind.setAsStartTime(lsStartTime);
                        inFind.setAsEndTime(lsEndTime);
                        List<Torikomi0FindOutEntity> outFind = comTlcSvplanResultSelectMapper
                                .findTorikomi0FindByCriteria(inFind);
                        for (Torikomi0FindOutEntity item : outFind) {
                            ComTlcSvplanResult r = new ComTlcSvplanResult();
                            // pk
                            r.setUserid(item.getUserid());
                            r.setYymmYmd(item.getYymmYmd());
                            r.setHoujinId(item.getHoujinId());
                            r.setShisetuId(item.getShisetuId());
                            r.setSvJigyoId(item.getSvJigyoId());
                            r.setItemcode(item.getItemcode());
                            r.setEdano(item.getEdano());
                            r.setSwchFlg(item.getSwchFlg());
                            // value
                            r.setDelFlg(Constants.DELL_FLG_ON);
                            int updateCnt = comTlcSvplanResultMapper.updateByPrimaryKeySelective(r);
                            if (updateCnt <= 0) {
                                throw new ExclusiveException();
                            }
                        }

                        // ll_findrow2 = ids_yoteifind.Retrieve( ll_shien , ll_user , ll_houjin ,
                        // ll_shisetu , ll_svjigyo , ll_svitem , ls_yymmdd , ll_eda_bak )
                        CmnTorikomiPlanAll01ByCriteriaInEntity in01 = new CmnTorikomiPlanAll01ByCriteriaInEntity();
                        in01.setAlShien(ilShien);
                        in01.setAlUser(ilUser);
                        in01.setAlHoujin(llHoujin);
                        in01.setAlSisetu(llShisetu);
                        in01.setAlJigyo(llSvjigyo);
                        in01.setAlItem(llSvitem);
                        in01.setAlEda(llEdaBak);
                        List<CmnTorikomiPlanAll01OutEntity> llFindrow2 = comTlcSvplanResultSelectMapper
                                .findCmnTorikomiPlanAll01ByCriteria(in01);
                        if (CollectionUtils.isEmpty(llFindrow2)) {
                            // com_tlc_svplan_resultの予定データも作成する 2004/11/16 ADD s.kato
                            // ------------------------------------------------
                            String lsDmyStt = lsStartTime;
                            if (lsDmyStt.compareTo("24:00") >= 0) {
                                lsDmyStt = "";
                            }

                            String lsDmyEdt = lsEndTime;
                            if (lsDmyEdt.compareTo("24:00") >= 0) {
                                lsDmyEdt = "";
                            }

                            CmnTensouPlanAll0OutDto ll_insrow_0 = new CmnTensouPlanAll0OutDto();
                            // ids_torikomi_0.InsertRow(0)
                            // // new ids_torikomi_0
                            ll_insrow_0.setUpdateFlg(1);
                            idsTorikomi0s.add(ll_insrow_0);
                            ll_insrow_0.setShienJigyoId(llShien); // 支援事業所ID
                            ll_insrow_0.setUserid(llUser); // 利用者ID
                            ll_insrow_0.setHoujinId(llHoujin); // 法人ID
                            ll_insrow_0.setShisetuId(llShisetu); // 施設ID
                            ll_insrow_0.setSvJigyoId(llSvjigyo); // サービス事業所ID
                            ll_insrow_0.setEdano(llEdaBak); // 枝番
                            // 2004/11/25 s.kato comment ids_torikomi_0.SetItem( ll_insrow_0( edano(
                            // ll_eda_max_no ); // 枝番
                            ll_insrow_0.setItemcode(llSvitem); // sv_item_cd
                            ll_insrow_0.setYymmYmd(lsYymmdd); // 処理年月
                            // ll_insrow_0.setsv_start_time ( ls_start_time );
                            // ll_insrow_0.setsv_end_time ( ls_end_time );
                            ll_insrow_0.setSvStartTime(lsDmyStt);
                            ll_insrow_0.setSvEndTime(lsDmyEdt);
                            ll_insrow_0.setSwchFlg(1); // 予定・実績区分フラグに予定（１）を入れる
                            ll_insrow_0.setTermid(llTermid);
                            ll_insrow_0.setTimeStmp(Timestamp.valueOf(LocalDateTime.now()));
                            ll_insrow_0.setDelFlg(Constants.DELL_FLG_OFF);
                            ll_insrow_0.setShokuId(gShokuinId); // gNdsIni.login.shokuin_id
                            ll_insrow_0.setShoukiboKbn(liShokibo); // 2008/08/27 : Add

                            for (int i = 1; i <= 31; i++) {
                                // 毎日のサービス側（Group0)の実績をケアマネ側（Group3）にSetItemする : 2004/11/25 : Add
                                setField(ll_insrow_0, String.format("day%d", i), 0);
                            }
                            ll_insrow_0.setTotal(0); // 合計も０
                        }
                        // com_tlc_svplan_resultの予定データも作成する 2004/11/16 ADD s.kato
                        // ------------------------------------------------

                        // 福祉用具貸与の詳細情報取り込み
                        // 日割区分
                        liHiwariKbn = idsTorikomi0.getHiwariKbn();
                        llFindrow.setJRentalF(liHiwariKbn);

                        // 小規模多機能型のサービス提供区分取り込み
                        int liShoukiboKbn = idsTorikomi0.getShoukiboKbn();
                        llFindrow.setShoukiboKbn(liShoukiboKbn);

                        // 合計は貸与の日割り区分の後にセット
                        liReTotal = 0;
                        for (int liCnt1 = liDSt; liCnt1 <= liDEd; liCnt1++) {
                            int liDay = getField(idsTorikomi0, "day" + liCnt1);
                            if (liDay == -1)
                                liDay = 0; // isNull equivalent

                            if (liDay > 0) {
                                Integer liTmp = 0;
                                if (lbHoumon && !ibSelJissekiTorikomi) {
                                    liTmp = getField(llFindrow, String.format("jDay%02d", liCnt1));
                                    if (liTmp == null)
                                        liTmp = 0;
                                    liTmp += liDay;
                                } else {
                                    liTmp = liDay;
                                }
                                setField(llFindrow, String.format("jDay%02d", liCnt1), liTmp);
                            }
                            liReTotal += liDay;
                        }

                        // total の SetItem が抜けていたので追加
                        int liDay = idsTorikomi0.getTotal();
                        if ("17".equals(lsSvtype) || "67".equals(lsSvtype) || lsScd.startsWith("3320")
                                || lsScd.startsWith("3519")) {
                            // 福祉用具貸与ならば
                            switch (liShoukiboKbn) {
                            case 1:
                            case 3:
                            case 4:
                                // 日割り群：回数そのまま
                                liDay = liReTotal;
                                break;
                            default:
                                // 全額・半額の場合は合計を１固定とする
                                liDay = 1;
                                break;
                            }
                        } else {
                            // 通常
                            liDay = liReTotal;
                        }
                        llFindrow.setJTotal(liDay);

                        // エスカレーション対応：福祉用具取込済みフラグに1をセットする 2013/5/24 masaki-g
                        if ("17".equals(lsSvtype) || "67".equals(lsSvtype) || lsScd.startsWith("3320")
                                || lsScd.startsWith("3519")) {
                            llFindrow.setDmyFygTorikomiFlg(1);
                        }

                        // 実績再計算を行う場合、キーを格納する
                        if (aiCalcFlg == 1) {
                            // "userid = %d AND shien_id = %d AND yymm_ym = '%s' AND yymm_d = '%s'",
                            // llUser, llShien, lsYymm, lsDddNow);
                            long llCalcFind = idsCalcSet.stream().filter(i -> {
                                return llUser.equals(i.getUserid()) && llShien.equals(i.getShienId())
                                        && lsYymm.equals(i.getYymmYm()) && lsDddNow.equals(i.getYymmD());
                            }).count();
                            if (llCalcFind == 0) {
                                CmnPlanAllCalcsOutDto llCalcCnt = new CmnPlanAllCalcsOutDto();
                                idsCalcSet.add(llCalcCnt);
                                llCalcCnt.setUserid(llUser);
                                llCalcCnt.setShienId(llShien);
                                llCalcCnt.setYymmYm(lsYymm);
                                llCalcCnt.setYymmD(lsDddNow);
                            }
                        }

                    }
                }
            }
        }

        return idsCalcSet;
    }

    private boolean setField(Object target, String propertyName, Object value) {
        try {
            Class<?> clazz = target.getClass();
            Field field = org.springframework.util.ReflectionUtils.findField(clazz, propertyName);
            ReflectionUtils.makeAccessible(field);
            field.set(target, value);
            return true;
        } catch (Exception e) {
        }
        return false;
    }

    private Integer getField(Object p, String fieldName) {
        try {
            Field field = ReflectionUtils.findField(p.getClass(), fieldName);
            ReflectionUtils.makeAccessible(field);
            return Integer.valueOf(ReflectionUtils.getField(field, p).toString());
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 概要レコードを作成する。 関数名：uf_make_gaiyou
     * 
     * @param makeGaiyouInDto 入力Dto
     * @return 0:成功 -1:失敗
     * <AUTHOR>
     */
    public Integer ufMakeGaiyou(MakeGaiyouInDto makeGaiyouInDto) {
        // 初期値設定
        Integer liTotal = CommonConstants.INT_0;

        // 概要レコード作成
        ComTlcSvplanResult comTlcSvplanResult = new ComTlcSvplanResult();
        // 利用者ID
        comTlcSvplanResult.setUserid(makeGaiyouInDto.getAlUser());
        // 処理年月
        comTlcSvplanResult.setYymmYmd(makeGaiyouInDto.getAsYymm());
        // 法人ID
        comTlcSvplanResult.setHoujinId(makeGaiyouInDto.getAlHoujin());
        // 施設ID
        comTlcSvplanResult.setShisetuId(makeGaiyouInDto.getAlShisetu());
        // サービス事業所ID
        comTlcSvplanResult.setSvJigyoId(makeGaiyouInDto.getAlSvjigyo());
        // 支援事業所ID
        comTlcSvplanResult.setShienJigyoId(makeGaiyouInDto.getAlShien());
        // サービス項目
        comTlcSvplanResult.setItemcode(CommonConstants.INT_0);
        // 枝番
        comTlcSvplanResult.setEdano(makeGaiyouInDto.getIlMaxEda());
        // 予定・実績モード
        comTlcSvplanResult.setSwchFlg(makeGaiyouInDto.getAiYojituKbn());
        // termid（サービス費適用マスタ期間ID
        comTlcSvplanResult.setTermid(makeGaiyouInDto.getAlTerm());
        // 各日付の計画内容
        try {
            for (int i = 1; i <= 31; i++) {
                String fieldNm = CommonConstants.DAY_STR + CommonDtoUtil.objValToString(i);
                Field field = ComTlcSvplanResult.class.getDeclaredField(fieldNm);
                field.setAccessible(true);
                Integer aiDay = makeGaiyouInDto.getAiDay().get(i);
                field.set(comTlcSvplanResult, aiDay);
                liTotal = liTotal + aiDay;
            }
        } catch (NoSuchFieldException | SecurityException | IllegalArgumentException | IllegalAccessException e) {
            return CommonConstants.INT_MINUS_1;
        }
        // 合計
        comTlcSvplanResult.setTotal(liTotal);
        // 削除フラグ
        comTlcSvplanResult.setDelFlg(CommonConstants.INT_0);
        // 更新日付
        comTlcSvplanResult.setTimeStmp(Timestamp.valueOf(LocalDateTime.now()));
        // 職員ID
        comTlcSvplanResult.setShokuId(makeGaiyouInDto.getShokuinId());

        // 登録時の共通カラム値設定処理
        CommonDaoUtil.setInsertCommonColumns(comTlcSvplanResult);
        Integer insCnt = comTlcSvplanResultMapper.insertSelective(comTlcSvplanResult);
        if (insCnt <= 0) {
            return CommonConstants.INT_MINUS_1;
        }
        return CommonConstants.INT_0;
    }

    /***
     * svplan_resultテーブルの計画のゴミデータを削除する。
     * 
     * @param inDto 入力Dto
     * @throws ExclusiveException
     */
    public void cmn2svjigyoDel(Cmn2svjigyoDelInDto inDto) throws ExclusiveException {
        List<Integer> userIdList = Arrays.asList(inDto.getAlUser());
        Integer svJigyoId;
        Integer lsFind = 0;
        // ケアマネ側のDS
        CmnDelSvplanResultCmnByCriteriaInEntity inEntity = new CmnDelSvplanResultCmnByCriteriaInEntity();
        inEntity.setLShienId(inDto.getAlShien());
        inEntity.setLUseridList(userIdList);
        inEntity.setSYm(inDto.getAsYymm());
        List<CmnDelSvplanResultCmnOutEntity> outList = cmnTucPlanRiyouSelectMapper
                .findCmnDelSvplanResultCmnByCriteria(inEntity);
        for (Integer id : userIdList) {
            // 利用者毎に絞り込んで、削除フラグを立てる。
            inDto.setCmnTensouPlanAll0OutEntityList(inDto.getCmnTensouPlanAll0OutEntityList().stream()
                    .filter(i -> i.getUserid().equals(id)).collect(Collectors.toList()));
            for (CmnTensouPlanAll0OutEntity outEntity : inDto.getCmnTensouPlanAll0OutEntityList()) {
                for (CmnDelSvplanResultCmnOutEntity cmnDelSvplanResultCmnOutEntity : outList) {
                    svJigyoId = outEntity.getSvJigyoId();
                    // ①同じサービス提供事業所の計画があるかを確認
                    if (cmnDelSvplanResultCmnOutEntity.getUserid().equals(id)
                            && cmnDelSvplanResultCmnOutEntity.getSvJigyoId().equals(svJigyoId)) {
                        lsFind++;
                    }
                    if (lsFind == 0) {
                        ComTlcSvplanResult comTlcSvplanResult = new ComTlcSvplanResult();
                        comTlcSvplanResult.setSvJigyoId(svJigyoId);
                        comTlcSvplanResult.setDelFlg(1);
                        // ケアマネ側に同じサービス提供事業所の計画行が無いので、削除フラグをたてる。
                        int returnValue = comTlcSvplanResultMapper.updateByPrimaryKey(comTlcSvplanResult);
                        if (returnValue <= 0) {
                            throw new ExclusiveException();
                        }
                    }
                }
            }
            // 利用者毎に絞り込んで、削除フラグを立てる。
            inDto.setCmnTensouPointAll0OutEntityList(inDto.getCmnTensouPointAll0OutEntityList().stream()
                    .filter(i -> i.getUserid().equals(id)).collect(Collectors.toList()));
            for (CmnTensouPointAll0OutEntity cmnTensouPointAll0OutEntity : inDto.getCmnTensouPointAll0OutEntityList()) {
                for (CmnDelSvplanResultCmnOutEntity cmnDelSvplanResultCmnOutEntity : outList) {
                    svJigyoId = cmnTensouPointAll0OutEntity.getSvJigyoId();
                    if (cmnDelSvplanResultCmnOutEntity.getUserid().equals(id)
                            && cmnTensouPointAll0OutEntity.getSvJigyoId().equals(svJigyoId)) {
                        lsFind++;
                    }
                    if (lsFind == 0) {
                        ComTlcSvplanPoint comTlcSvplanPoint = new ComTlcSvplanPoint();
                        comTlcSvplanPoint.setSvJigyoId(svJigyoId);
                        // ケアマネ側に同じサービス提供事業所の計画行が無いので、削除フラグをたてる。
                        int returnValue = comTlcSvplanPointMapper.deleteByPrimaryKey(comTlcSvplanPoint);
                        if (returnValue <= 0) {
                            throw new ExclusiveException();
                        }
                    }
                }
            }
        }
    }

    /**
     * 点数を転送する uf_tensou_point
     * 
     * @param alShien 支援事業所ID
     * @param asYymm  処理年月
     * @param alUser  利用者ID（配列）
     * @param shokuId 職員ID gNdsIni.login.shokuin_id
     * @return 0:成功 -1:失敗
     * @throws Exception
     */
    public Integer tensouPoint(Integer alShien, String asYymm, Integer alUser[], Integer shokuId) throws Exception {
        List<Integer> userIdList = Arrays.asList(alUser);
        Integer liDmyShien;
        Integer llUser;
        String lsWork;
        String lsShienKaisiYmd;
        if (!CollectionUtils.isNullOrEmpty(userIdList)) {
            // ケアマネ点数が自分の点数か判断する
            CmnPointTensou3ByCriteriaInEntity cmnPointTensou3ByCriteriaInEntity = new CmnPointTensou3ByCriteriaInEntity();
            cmnPointTensou3ByCriteriaInEntity.setAlUserList(userIdList);
            cmnPointTensou3ByCriteriaInEntity.setAsYymm(asYymm);
            List<CmnPointTensou3OutEntity> cmnPointTensou3OutEntityList = cmnTucServicePointSelectMapper
                    .findCmnPointTensou3ByCriteria(cmnPointTensou3ByCriteriaInEntity);
            for (CmnPointTensou3OutEntity cmnPointTensou3OutEntity : cmnPointTensou3OutEntityList) {
                // gai_numberを支援事業所ＩＤ代わりにしている
                liDmyShien = cmnPointTensou3OutEntity.getGaiNumber();
                if (liDmyShien != null && liDmyShien != alShien) {
                    continue;
                }
                llUser = cmnPointTensou3OutEntity.getUserid();
                lsWork = cmnPointTensou3OutEntity.getYymmYm();
                if (lsWork.length() == CommonConstants.NUMBER_7) {
                    lsShienKaisiYmd = kghCmnF01Logic.getSvriyoTiming(alShien, llUser, lsWork);
                    lsWork = lsShienKaisiYmd;
                }
                ComTlcSvplanPoint comTlcSvplanPoint = new ComTlcSvplanPoint();
                comTlcSvplanPoint.setHoujinId(cmnPointTensou3OutEntity.getHoujinId());
                comTlcSvplanPoint.setShisetuId(cmnPointTensou3OutEntity.getShisetuId());
                comTlcSvplanPoint.setSvJigyoId(cmnPointTensou3OutEntity.getSvJigyoId());
                // サービス種類CD
                comTlcSvplanPoint.setSvtype(cmnPointTensou3OutEntity.getSvtype());
                // 保険対象点数
                comTlcSvplanPoint.setPoint(cmnPointTensou3OutEntity.getPoint());
                // 保険対象外点数
                comTlcSvplanPoint.setGaiPoint(cmnPointTensou3OutEntity.getGaiPoint());
                // 医療費控除フラグ
                comTlcSvplanPoint.setIryohiKojoFl(cmnPointTensou3OutEntity.getIryohiKojoFl());
                // 支援事業
                comTlcSvplanPoint.setShienJigyoId(alShien);
                comTlcSvplanPoint.setTimeStmp(AppUtil.getSystemTimeStamp());
                comTlcSvplanPoint.setDelFlg(CommonConstants.DEL_FLG_0);
                comTlcSvplanPoint.setShokuId(shokuId);
                Integer insCnt = comTlcSvplanPointMapper.insertSelective(comTlcSvplanPoint);
                if (insCnt <= 0) {
                    return CommonConstants.INT_MINUS_1;
                }
                return CommonConstants.INT_0;
            }
        }
        return CommonConstants.INT_MINUS_1;
    }

    /**
     * ケアマネ側、サービス事業所側それぞれのDataStoreをRetrieveし、指定した サービス事業所のFilter式で絞り込む
     * 
     * @param alShien  支援事業所ID
     * @param asYymm   処理年月
     * @param alUser   利用者ID（配列）
     * @param asFilter サービス事業所を絞り込むFilter式
     * @return RetrieveOutDto
     */
    public RetrieveOutDto retrieve(Integer alShien, String asYymm, Integer alUser[], String asFilter) {
        RetrieveOutDto outDto = new RetrieveOutDto();
        List<Integer> userIdList = Arrays.asList(alUser);
        // ケアマネ側の指定月の指定利用者の計画をretireveし、
        // 指定したサービス提供事業者でfilterする。
        CmnTensouAll3ByCriteriaInEntity cmnDelSvplanResultCmnByCriteriaInEntity = new CmnTensouAll3ByCriteriaInEntity();
        cmnDelSvplanResultCmnByCriteriaInEntity.setAlShien(alShien);
        cmnDelSvplanResultCmnByCriteriaInEntity.setAlUserList(userIdList);
        cmnDelSvplanResultCmnByCriteriaInEntity.setAsYymm(asYymm);
        // 計画
        List<CmnTensouAll3OutEntity> cmnDelSvplanResultCmnOutEntityList = cmnTucPlanRiyouSelectMapper
                .findCmnTensouAll3ByCriteria(cmnDelSvplanResultCmnByCriteriaInEntity);

        // 点数
        List<CmnPointTensou3OutEntity> cmnPointTensou3OutEntityList = new ArrayList<>();
        CmnPointTensou3ByCriteriaInEntity cmnPointTensou3ByCriteriaInEntity = new CmnPointTensou3ByCriteriaInEntity();
        cmnPointTensou3ByCriteriaInEntity.setAlUserList(userIdList);
        cmnPointTensou3ByCriteriaInEntity.setAsYymm(asYymm);
        cmnPointTensou3OutEntityList = cmnTucServicePointSelectMapper
                .findCmnPointTensou3ByCriteria(cmnPointTensou3ByCriteriaInEntity);

        String lsSvcd = kghCmnF01Logic.getSvJigyoCd(alShien);
        // asFilter = 指定された複数のサービス事業所のID
        // 計画 filter
        for (CmnTensouAll3OutEntity cmnDelSvplanResultCmnOutEntity : cmnDelSvplanResultCmnOutEntityList) {
            String condition33Or35 = cmnDelSvplanResultCmnOutEntity.getScode().substring(0, 2);
            switch (lsSvcd) {
            case CommonConstants.JCD_23031:
            case CommonConstants.JCD_43031:
                if (!StringUtils.isEmpty(asFilter)) {
                    cmnDelSvplanResultCmnOutEntityList = cmnDelSvplanResultCmnOutEntityList.stream()
                            .filter(i -> i.getSvJigyoId().equals(CommonDtoUtil.strValToInt(asFilter))
                                    && (condition33Or35.equals("33") || condition33Or35.equals("35")))
                            .collect(Collectors.toList());
                } else {
                    cmnDelSvplanResultCmnOutEntityList = cmnDelSvplanResultCmnOutEntityList.stream()
                            .filter(i -> (condition33Or35.equals("33") || condition33Or35.equals("35")))
                            .collect(Collectors.toList());
                }
                break;
            case CommonConstants.JCD_63031:
                if (!StringUtils.isEmpty(asFilter)) {
                    cmnDelSvplanResultCmnOutEntityList = cmnDelSvplanResultCmnOutEntityList.stream()
                            .filter(i -> i.getSvJigyoId().equals(CommonDtoUtil.strValToInt(asFilter))
                                    && condition33Or35.equals("36"))
                            .collect(Collectors.toList());
                } else {
                    cmnDelSvplanResultCmnOutEntityList = cmnDelSvplanResultCmnOutEntityList.stream()
                            .filter(i -> condition33Or35.equals("36")).collect(Collectors.toList());
                }
            }
        }
        // 点数filter
        cmnPointTensou3OutEntityList = cmnPointTensou3OutEntityList.stream()
                .filter(i -> i.getSvJigyoId().equals(CommonDtoUtil.strValToInt(asFilter))).collect(Collectors.toList());

        // サービス事業所側の指定月の指定利用者の計画をretireveし、
        // 指定したサービス提供事業者でfilterする。

        // 計画
        CmnTensouPlanAll0ByCriteriaInEntity cmnTensouPlanAll0ByCriteriaInEntity = new CmnTensouPlanAll0ByCriteriaInEntity();
        cmnTensouPlanAll0ByCriteriaInEntity.setAlShien(alShien);
        cmnTensouPlanAll0ByCriteriaInEntity.setAsYymmdd(asYymm);
        cmnTensouPlanAll0ByCriteriaInEntity.setAlUserList(userIdList);
        List<CmnTensouPlanAll0OutEntity> cmnTensouPlanAll0OutEntityList = comTlcSvplanResultSelectMapper
                .findCmnTensouPlanAll0ByCriteria(cmnTensouPlanAll0ByCriteriaInEntity);
        cmnTensouPlanAll0OutEntityList = cmnTensouPlanAll0OutEntityList.stream()
                .filter(i -> i.getSvJigyoId().equals(CommonDtoUtil.strValToInt(asFilter))).collect(Collectors.toList());

        // 点数
        CmnTensouPointAll0ByCriteriaInEntity cmnTensouPointAll0ByCriteriaInEntity = new CmnTensouPointAll0ByCriteriaInEntity();
        cmnTensouPointAll0ByCriteriaInEntity.setAlShien(alShien);
        cmnTensouPointAll0ByCriteriaInEntity.setAsYymmdd(asYymm);
        cmnTensouPointAll0ByCriteriaInEntity.setAlUserList(userIdList);
        List<CmnTensouPointAll0OutEntity> cmnTensouPointAll0OutEntityList = comTlcSvplanPointSelectMapper
                .findCmnTensouPointAll0ByCriteria(cmnTensouPointAll0ByCriteriaInEntity);
        cmnTensouPointAll0OutEntityList = cmnTensouPointAll0OutEntityList.stream()
                .filter(i -> i.getSvJigyoId().equals(CommonDtoUtil.strValToInt(asFilter))).collect(Collectors.toList());

        outDto.setCmnDelSvplanResultCmnOutEntityList(cmnDelSvplanResultCmnOutEntityList);
        outDto.setCmnSvPointOutEntityList(cmnPointTensou3OutEntityList);
        outDto.setCmnTensouPlanAll0OutEntityList(cmnTensouPlanAll0OutEntityList);
        outDto.setCmnTensouPointAll0OutEntityList(cmnTensouPointAll0OutEntityList);
        return outDto;
    }

    /**
     * ケアマネで転送された計画データで連続する時間帯のある行を分割する関数 関数名：uf_hok15_div
     * 
     * @param comTlcSvplanResultList (中間テーブル)の計画データ
     * @param adsListHok15           厚労省 介護サービスマスタ(com_mhc_km)リスト
     * @param adsListItemuseHok15    介護サービス費適用マスタ(com_mhc_itemuse)リスト
     * @return 0: 正常終了
     * <AUTHOR>
     */
    public Integer ufHok15Div(List<ComTlcSvplanResult> comTlcSvplanResultList,
            List<KghCmnListHok15OutEntity> adsListHok15, List<KghCmnListItemuseHok15OutEntity> adsListItemuseHok15)
            throws Exception {
        List<ComTlcSvplanResult> comTlcSvplanResultMoveList = new ArrayList<>();
        List<Integer> delRowIndexList = new ArrayList<>();
        Integer delRowIndex = CommonConstants.INT_0;
        // (中間テーブル)の計画データから分割対象となるサービス(訪問看護Ⅰ５)を判定し、作業データストアに計画データを移動する
        for (ComTlcSvplanResult comTlcSvplanResult : comTlcSvplanResultList) {
            // 0:予定がある日がない、1:予定がある日がある
            Integer llZeroFlg = CommonConstants.INT_0;
            if (CommonConstants.INT_0 == comTlcSvplanResult.getDelFlg()) {
                // 削除された計画データは対象外とする
                continue;
            }
            // 計画データが訪問看護Ⅰ５以外は対象外とする
            boolean hasLlFoundRow = kghPlanTensouKanriLogic.checkHok15(comTlcSvplanResult.getSvJigyoId(),
                    comTlcSvplanResult.getItemcode(), adsListHok15, adsListItemuseHok15);
            if (hasLlFoundRow) {
                continue;
            }
            // 予定がない行は計画データの移動処理をしない
            for (int i = 1; i <= 31; i++) {
                Field field = comTlcSvplanResult.getClass().getField(CommonConstants.DAY_STR + i);
                Integer dayVal = (Integer) field.get(comTlcSvplanResult);
                if (dayVal > CommonConstants.INT_0) {
                    llZeroFlg = CommonConstants.INT_1;
                }
            }
            // 作業データストアに分割対象となる計画データを移動する
            if (llZeroFlg > CommonConstants.INT_0) {
                comTlcSvplanResultMoveList.add(comTlcSvplanResult);
                delRowIndexList.add(delRowIndex);
            }
            delRowIndex++;
        }

        for (int rowIndex : delRowIndexList) {
            comTlcSvplanResultList.remove(rowIndex);
        }

        // 対象データが無い場合
        if (CollectionUtils.isEmpty(comTlcSvplanResultMoveList)) {
            return CommonConstants.INT_0;
        }

        comTlcSvplanResultMoveList = comTlcSvplanResultMoveList.stream()
                .sorted(Comparator.comparing(ComTlcSvplanResult::getEdano)).collect(Collectors.toList());
        // 対象データ数の行カウントだけ処理を行う
        for (ComTlcSvplanResult comTlcSvplanResultMove : comTlcSvplanResultMoveList) {
            // 計画データ検索用のデータを取得する
            Integer llOrgUserid = comTlcSvplanResultMove.getUserid();
            String lsOrgYymmYmd = comTlcSvplanResultMove.getYymmYmd();
            Integer llOrgHoujinId = comTlcSvplanResultMove.getHoujinId();
            Integer llOrgShisetuId = comTlcSvplanResultMove.getShisetuId();
            Integer llOrgSvJigyoId = comTlcSvplanResultMove.getSvJigyoId();
            Integer llOrgItemcode = comTlcSvplanResultMove.getItemcode();
            Integer llOrgEdano = comTlcSvplanResultMove.getEdano();
            Integer llOrgSwchFlg = comTlcSvplanResultMove.getSwchFlg();
            for (int llDateNumber = 1; llDateNumber <= 31; llDateNumber++) {
                Field field = comTlcSvplanResultMove.getClass().getField(CommonConstants.DAY_STR + llDateNumber);
                Integer llOrgCnt = (Integer) field.get(comTlcSvplanResultMove);
                // 回数が1未満の場合はskipする
                if (llOrgCnt < CommonConstants.INT_1) {
                    continue;
                }
                String lsOrgServiceStartAddColon = comTlcSvplanResultMove.getSvStartTime();
                String lsOrgServiceStartAdd20minAddColon = kghShrKsgFunc01Logic.ksgAddtime(lsOrgServiceStartAddColon,
                        CommonConstants.TIME_00_20);
                String lsOrgServiceEndAddColon = comTlcSvplanResultMove.getSvEndTime();
                String lsOrgServiceEndStartAdd20minAddColon = lsOrgServiceStartAdd20minAddColon;

                for (int llSplitCnt = 1; llSplitCnt <= llOrgCnt; llSplitCnt++) {
                    Integer llSplitFlg = CommonConstants.INT_1;
                    if (llSplitCnt == llOrgCnt) {
                        // 最終回数の場合、コピー元の終了時間をセットする
                        lsOrgServiceEndStartAdd20minAddColon = lsOrgServiceEndAddColon;
                    } else {
                        Integer totalMinutes = kghKsgEntry15Logic.fSys5CalcTime(lsOrgServiceEndAddColon,
                                lsOrgServiceStartAdd20minAddColon);
                        if (totalMinutes < CommonConstants.INT_20 || totalMinutes == CommonConstants.NUMBER_1440) {
                            // 分割しないパターン
                            llSplitFlg = CommonConstants.INT_0;
                            lsOrgServiceEndStartAdd20minAddColon = lsOrgServiceEndAddColon;
                        }
                        if (lsOrgServiceStartAddColon.compareTo(lsOrgServiceEndAddColon) > 0) {
                            llSplitFlg = CommonConstants.INT_0;
                            lsOrgServiceEndStartAdd20minAddColon = lsOrgServiceEndAddColon;
                        }
                        if (lsOrgServiceStartAddColon.compareTo(lsOrgServiceEndAddColon) == 0) {
                            // 分割しないパターン(開始時刻=終了時刻)
                            llSplitFlg = CommonConstants.INT_0;
                            lsOrgServiceEndStartAdd20minAddColon = lsOrgServiceEndAddColon;
                        }
                        totalMinutes = kghKsgEntry15Logic.fSys5CalcTime(lsOrgServiceEndAddColon,
                                lsOrgServiceStartAddColon);
                        if (totalMinutes < CommonConstants.INT_20 || totalMinutes == CommonConstants.NUMBER_1440) {
                            // 分割しないパターン(20分間に満たない)
                            llSplitFlg = CommonConstants.INT_0;
                            lsOrgServiceEndStartAdd20minAddColon = lsOrgServiceEndAddColon;
                        }
                    }
                    // ids_plan_tensou_all_0(ds_cmn_tensou_plan_all_0[中間テーブル])に計画データがあるか検索する
                    Integer llFoundRow = CommonConstants.INT_0;
                    for (int index = 1; index <= CollectionUtils.size(comTlcSvplanResultList); index++) {
                        ComTlcSvplanResult comTlcSvplanResult = comTlcSvplanResultList.get(index);
                        if (comTlcSvplanResult.getUserid() == llOrgUserid
                                && comTlcSvplanResult.getYymmYmd().equals(lsOrgYymmYmd)
                                && comTlcSvplanResult.getHoujinId() == llOrgHoujinId
                                && comTlcSvplanResult.getShisetuId() == llOrgShisetuId
                                && comTlcSvplanResult.getSvJigyoId() == llOrgSvJigyoId
                                && comTlcSvplanResult.getItemcode() == llOrgItemcode
                                && comTlcSvplanResult.getSvStartTime().equals(lsOrgServiceStartAddColon)
                                && comTlcSvplanResult.getSvEndTime().equals(lsOrgServiceEndStartAdd20minAddColon)
                                && comTlcSvplanResult.getSwchFlg() == llOrgSwchFlg
                                && comTlcSvplanResult.getDelFlg() == CommonConstants.INT_0) {
                            llFoundRow = index;
                            break;
                        }
                    }
                    if (llFoundRow == CommonConstants.INT_0) {
                        // 計画データが存在しない場合は新規に行追加する
                        // バッファ(lds_buff)からids_plan_tensou_all_0(ds_cmn_tensou_plan_all_0)へ該当行をコピーする
                        ComTlcSvplanResult comTlcSvplanResultAdd = comTlcSvplanResultMove;
                        // ids_plan_tensou_all_0(ds_cmn_tensou_plan_all_0)の開始時間を変更する
                        comTlcSvplanResultAdd.setSvStartTime(lsOrgServiceStartAddColon);
                        // ids_plan_tensou_all_0(ds_cmn_tensou_plan_all_0)の終了時間(開始時間に20分を足した時間)を変更す
                        comTlcSvplanResultAdd.setSvEndTime(lsOrgServiceStartAdd20minAddColon);
                        // 計画データのオリジナル(複写元)をlds_buffから検索する
                        Integer llTmpFoundRow = CommonConstants.INT_0;
                        for (int index = 1; index <= CollectionUtils.size(comTlcSvplanResultMoveList); index++) {
                            ComTlcSvplanResult comTlcSvplanResultTmp = comTlcSvplanResultMoveList.get(index);
                            if (comTlcSvplanResultTmp.getUserid() == llOrgUserid
                                    && comTlcSvplanResultTmp.getYymmYmd().equals(lsOrgYymmYmd)
                                    && comTlcSvplanResultTmp.getHoujinId() == llOrgHoujinId
                                    && comTlcSvplanResultTmp.getShisetuId() == llOrgShisetuId
                                    && comTlcSvplanResultTmp.getSvJigyoId() == llOrgSvJigyoId
                                    && comTlcSvplanResultTmp.getItemcode() == llOrgItemcode
                                    && comTlcSvplanResultTmp.getSvStartTime().equals(lsOrgServiceStartAddColon)
                                    && comTlcSvplanResultTmp.getSvEndTime().equals(lsOrgServiceEndAddColon)
                                    && comTlcSvplanResultTmp.getEdano() == llOrgEdano
                                    && comTlcSvplanResultTmp.getSwchFlg() == llOrgSwchFlg
                                    && comTlcSvplanResultTmp.getDelFlg() == CommonConstants.INT_0) {
                                llTmpFoundRow = index;
                            }
                        }

                        if (llTmpFoundRow == CommonConstants.INT_0) {
                            // 計画データが存在しない場合は枝番を採番(最大枝番+1の値)し変更する
                            // 例：分割した後の2回目以降
                            Integer llNewEdano = CommonConstants.INT_0;
                            for (ComTlcSvplanResult comTlcSvplanResultTmp : comTlcSvplanResultMoveList) {
                                Integer llTmpNewEdano = comTlcSvplanResultTmp.getEdano();
                                if (llNewEdano < llTmpNewEdano) {
                                    llNewEdano = llTmpNewEdano;
                                }
                            }

                            // 最大値に1を足す
                            llNewEdano = llNewEdano + CommonConstants.INT_1;
                            comTlcSvplanResultAdd.setEdano(llNewEdano);
                        } else {
                            // 計画データが存在する場合はオリジナル(複写元)の枝番をセットする
                            // 例：分割しない、または分割した後の1回目
                            comTlcSvplanResultAdd.setEdano(llOrgEdano);
                        }

                        for (int llTmpDateNumber = 1; llTmpDateNumber <= 31; llTmpDateNumber++) {
                            String fieldNm = CommonConstants.DAY_STR + CommonDtoUtil.objValToString(llTmpDateNumber);
                            Field fieldAdd = ComTlcSvplanResult.class.getDeclaredField(fieldNm);
                            fieldAdd.setAccessible(true);
                            // ids_plan_tensou_all_0(ds_cmn_tensou_plan_all_0)の日付(1日から31日)の回数に0をセット
                            fieldAdd.set(comTlcSvplanResultAdd, CommonConstants.INT_0);
                        }

                        if (llSplitFlg == CommonConstants.INT_0) {
                            String fieldNm = CommonConstants.DAY_STR + CommonDtoUtil.objValToString(llDateNumber);
                            Field fieldAdd = ComTlcSvplanResult.class.getDeclaredField(fieldNm);
                            fieldAdd.setAccessible(true);
                            // ids_plan_tensou_all_0(ds_cmn_tensou_plan_all_0)の日付(1日から31日)の回数に0をセット
                            fieldAdd.set(comTlcSvplanResultAdd, llOrgCnt - (llSplitCnt - 1));
                        } else {
                            String fieldNm = CommonConstants.DAY_STR + CommonDtoUtil.objValToString(llDateNumber);
                            Field fieldAdd = ComTlcSvplanResult.class.getDeclaredField(fieldNm);
                            fieldAdd.setAccessible(true);
                            // ids_plan_tensou_all_0(ds_cmn_tensou_plan_all_0)の回数を変更
                            fieldAdd.set(comTlcSvplanResultAdd, CommonConstants.INT_1);
                        }
                        comTlcSvplanResultList.add(comTlcSvplanResultAdd);
                    } else {
                        // 計画データが存在する場合は回数をセットする
                        if (llSplitFlg == CommonConstants.INT_0 && llSplitCnt != llOrgCnt) {
                            String fieldNm = CommonConstants.DAY_STR + CommonDtoUtil.objValToString(llDateNumber);
                            Field fieldAdd = ComTlcSvplanResult.class.getDeclaredField(fieldNm);
                            fieldAdd.setAccessible(true);
                            fieldAdd.set(comTlcSvplanResultList.get(llFoundRow - 1), llOrgCnt - (llSplitCnt - 1));
                        } else {
                            String fieldNm = CommonConstants.DAY_STR + CommonDtoUtil.objValToString(llDateNumber);
                            Field fieldAdd = ComTlcSvplanResult.class.getDeclaredField(fieldNm);
                            fieldAdd.setAccessible(true);
                            fieldAdd.set(comTlcSvplanResultList.get(llFoundRow - 1), CommonConstants.INT_1);
                        }
                    }
                    // 計画データ検索用の開始時間を再セットする
                    lsOrgServiceStartAddColon = lsOrgServiceEndStartAdd20minAddColon;
                    lsOrgServiceStartAdd20minAddColon = kghShrKsgFunc01Logic.ksgAddtime(lsOrgServiceStartAddColon,
                            CommonConstants.TIME_00_20);
                    // 計画データ検索用の終了時間を再セットする
                    lsOrgServiceEndStartAdd20minAddColon = lsOrgServiceStartAdd20minAddColon;
                    if (llSplitFlg == CommonConstants.INT_0) {
                        break;
                    }
                }
            }
        }

        // 合計回数を再セットする
        for (ComTlcSvplanResult comTlcSvplanResult : comTlcSvplanResultList) {
            Integer llTotal = CommonConstants.INT_0;
            for (int llTmpDateNumber = 1; llTmpDateNumber <= 31; llTmpDateNumber++) {
                String fieldNm = CommonConstants.DAY_STR + CommonDtoUtil.objValToString(llTmpDateNumber);
                Field field = comTlcSvplanResult.getClass().getField(fieldNm);
                Integer dayValue = (Integer) field.get(comTlcSvplanResult);
                if (dayValue > CommonConstants.INT_0) {
                    llTotal = llTotal + dayValue;
                }
            }
            comTlcSvplanResult.setTotal(llTotal);
        }

        // 枝番重複がある場合採番し直す
        comTlcSvplanResultList = comTlcSvplanResultList.stream()
                .sorted(Comparator.comparing(ComTlcSvplanResult::getEdano).reversed()).collect(Collectors.toList());
        Integer llNewEdano = CommonConstants.INT_0;
        Integer llOrgEdano = CommonConstants.INT_0;
        for (int llCurrentRow = 1; llCurrentRow <= CollectionUtils.size(comTlcSvplanResultList); llCurrentRow++) {
            ComTlcSvplanResult comTlcSvplanResult = comTlcSvplanResultList.get(llCurrentRow - 1);
            if (llCurrentRow == CommonConstants.INT_1) {
                llNewEdano = comTlcSvplanResultList.getFirst().getEdano();
            }
            if (llCurrentRow > CommonConstants.INT_1 && llOrgEdano == comTlcSvplanResult.getEdano()) {
                llNewEdano++;
                comTlcSvplanResultList.get(llCurrentRow - 1).setEdano(llNewEdano);
            } else {
                llOrgEdano = comTlcSvplanResult.getEdano();
            }
        }
        return CommonConstants.INT_0;
    }

}
