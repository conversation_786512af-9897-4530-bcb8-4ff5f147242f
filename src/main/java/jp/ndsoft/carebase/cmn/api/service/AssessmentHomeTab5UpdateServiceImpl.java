package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00798HealthStateVisitSituationInfo;
import jp.ndsoft.carebase.cmn.api.logic.AssessmentHomeLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeSaveServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeTab5UpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeTab5UpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl4Kio11H21Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdl5Kio11R3Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucGdlRirekiMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kio11H21;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl4Kio11H21Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kio11R3;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdl5Kio11R3Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRireki;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucGdlRirekiCriteria;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * @since 2025.05.07
 * <AUTHOR> 謝法文
 * @apiNote GUI00798_［アセスメント］画面（居宅）（5） データ保存
 */
@Service
public class AssessmentHomeTab5UpdateServiceImpl extends
        UpdateServiceImpl<AssessmentHomeTab5UpdateServiceInDto, AssessmentHomeTab5UpdateServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** ［アセスメント］画面（居宅）画面のロジッククラス */
    @Autowired
    private AssessmentHomeLogic assessmentHomeLogic;

    /** ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂） */
    @Autowired
    private CpnTucGdl4Kio11H21Mapper cpnTucGdl4Kio11H21Mapper;

    /** ＧＬ＿本人の健康状態・受診等の状況（R３改訂） */
    @Autowired
    private CpnTucGdl5Kio11R3Mapper cpnTucGdl5Kio11R3Mapper;

    /** ＧＬ＿居宅アセスメント履歴 */
    @Autowired
    private CpnTucGdlRirekiMapper cpnTucGdlRirekiMapper;

    @Autowired
    private PlatformTransactionManager transactionManager;

    /**
     * ［アセスメント］画面（居宅）（5）のデータ保存
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    @Override
    protected AssessmentHomeTab5UpdateServiceOutDto mainProcess(AssessmentHomeTab5UpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        AssessmentHomeTab5UpdateServiceOutDto outDto = new AssessmentHomeTab5UpdateServiceOutDto();
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);
        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし
        /*
         * ===============2. バリデーションチェックを行う。===============
         * 
         */
        boolean resFlg = assessmentHomeLogic.documentPerformValidationCheck(loginDto);
        // 2.2.4. 【変数】.処理結果が「false：失敗」の場合
        if (!resFlg) {
            // 2.2.4. 【変数】.処理結果が「false：失敗」の場合、下記返却する情報を設定して、後続処理を飛ばして、API処理を正常終了とする。
            // レスポンス.計画期間ID ： リクエストパラメータ.計画期間ID
            outDto.setSc1Id(loginDto.getSc1Id());
            // レスポンス.アセスメントID ： リクエストパラメータ.アセスメントID
            outDto.setGdlId(loginDto.getGdlId());
            // レスポンス.エラー区分: "1
            outDto.setErrKbn(CommonConstants.E_DOC_CHECK_ERR);
            return outDto;
        }
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transactionB = transactionManager.getTransaction(def);
        try {
            // ［アセスメント］画面（居宅）（5） データ保存
            outDto = mainProcessMealUpdate(inDto);
            transactionManager.commit(transactionB);
        } catch (Exception e) {
            transactionManager.rollback(transactionB);
            throw e;
        }

        // e-文書_ケアチェック表１のリクエストパラメータ
        TransactionStatus transactionC = transactionManager.getTransaction(def);
        try {
            mainProcessEdocOut(inDto, outDto);
            transactionManager.commit(transactionC);
        } catch (Exception e) {
            transactionManager.rollback(transactionC);
            throw e;
        }

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * API定義 (e文書出力)
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected void mainProcessEdocOut(
            AssessmentHomeTab5UpdateServiceInDto inDto, AssessmentHomeTab5UpdateServiceOutDto outDto)
            throws Exception {
        // 入力dtoをLoginDtoにコピーする
        AssessmentHomeSaveServiceInDto loginDto = new AssessmentHomeSaveServiceInDto();
        BeanUtils.copyProperties(loginDto, inDto);

        /*
         * ===============e文書出力===============
         * 
         */
        // e文書出力
        String errKbn = assessmentHomeLogic.getPrint(loginDto, outDto.getSc1Id());
        outDto.setErrKbn(errKbn);
    }

    /**
     * ［アセスメント］画面（居宅）（5） データ保存
     * 
     * @param inDto データ保存入力DTO.
     * @return データ保存出力DTO
     */
    protected AssessmentHomeTab5UpdateServiceOutDto mainProcessMealUpdate(
            AssessmentHomeTab5UpdateServiceInDto inDto)
            throws Exception {

        // 戻り情報
        AssessmentHomeTab5UpdateServiceOutDto outDto = new AssessmentHomeTab5UpdateServiceOutDto();
        // 変数.計画対象期間ID
        Integer sc1Id = 0;

        // DAOパラメータを作成
        AssessmentHomeSaveServiceInDto loginDto = this.setAssessmentHomeSaveServiceInDto(inDto);

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. 計画対象期間の保存処理===============
         * 
         */
        // 2.1.リクエストパラメータ.計画対象期間IDがnullの場合、【27-06記録共通期間】情報を登録する。
        if (inDto.getSc1Id() == null || StringUtil.isEmpty(inDto.getSc1Id())) {
            // 計画対象期間の保存処理
            this.assessmentHomeLogic.insertKghTucKrkKikan(loginDto);
            // 変数.計画対象期間ID=採番した期間ID
            sc1Id = CommonDtoUtil.strValToInt(loginDto.getSc1Id());

        } else {
            // 変数.計画対象期間ID=リクエストパラメータ.計画対象期間ID
            sc1Id = CommonDtoUtil.strValToInt(inDto.getSc1Id());
        }

        /*
         * =====3.リクエストパラメータ.削除処理区分が2:画面を履歴ごと削除するの場合、下記テーブルデータを更新する。==========
         * 
         */
        if (CommonConstants.DELETE_KBN_2.equals(inDto.getDeleteKbn())) {
            assessmentHomeLogic.homeLogicsyuri(loginDto, sc1Id);
        }

        /*
         * =====4. リクエストパラメータ.削除処理区分が1:画面のみ削除するの場合==========
         * 
         */
        else if (CommonConstants.DELETE_KBN_1.equals(inDto.getDeleteKbn())) {
            // 4.1. リクエストパラメータ.改定フラグが4「H21/４改訂版」の場合、【ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）】情報を更新する。
            if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {
                this.deletekio11H21(inDto, sc1Id);

            } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {
                // 4.2. リクエストパラメータ.改定フラグが5「R3/４改訂版」の場合、【ＧＬ＿本人の健康状態・受診等の状況（R３改訂）】情報を削除更新する。
                this.deletekio11R3(inDto, sc1Id);

            }

            // 4.3. 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
            this.clearRrk(inDto, sc1Id);

        } else {
            /*
             * ===============5.以外の場合===============
             * 
             */
            // 5.1. 履歴情報の保存処理
            // 5.1.1. リクエストパラメータ.履歴更新区分が"C":新規の場合、【ＧＬ＿居宅アセスメント履歴】情報を登録する。
            if (CommonDtoUtil.isHistoryCreate(inDto)) {
                this.insertRrk(inDto, sc1Id);

            } else if (CommonDtoUtil.isHistoryUpdate(inDto)) {
                // 5.1.2. リクエストパラメータ.履歴更新区分が"U":更新の場合、【ＧＬ＿居宅アセスメント履歴】情報を更新する。
                this.updateRrk(inDto, sc1Id);

            }

            // 5.2. 5タブの画面情報の保存処理
            // 5.2.1. リクエストパラメータ.更新区分が"C":新規の場合、本人の健康状態・受診等の状況情報を登録する。
            if (CommonDtoUtil.isCreate(inDto)) {
                // 5.2.1.1. リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、【ＧＬ＿サービス利用状況（Ｈ２１改訂）】情報を登録する。
                if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {
                    this.insertKio11H21(inDto, sc1Id);

                } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {
                    // 5.2.1.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、【ＧＬ＿サービス利用状況（R３改訂）】情報を登録する。
                    this.insertKio11R3(inDto, sc1Id);

                }
            }
            // 5.2.2. リクエストパラメータ.更新区分が"U":更新の場合、本人の健康状態・受診等の状況情報を更新する。
            else if (CommonDtoUtil.isUpdate(inDto)) {
                // 5.2.2.1. リクエストパラメータ.改定フラグが4（H21/４改訂版）の場合、【ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）】情報を更新する。
                if (CommonConstants.KAI_TEI_FLAG_H21_4.equals(inDto.getNinteiFlg())) {
                    this.updateKio11H21(inDto, sc1Id);

                } else if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(inDto.getNinteiFlg())) {
                    // 5.2.2.2. リクエストパラメータ.改定フラグが5（R3/４改訂版）の場合、
                    // 【ＧＬ＿本人の健康状態・受診等の状況（R３改訂）】情報を更新する。
                    this.updateKio11R3(inDto, sc1Id);
                }

            }

            // 5.3. リクエストパラメータ.【課題と目標リスト】の件数分、【ＧＬ＿課題と目標】情報を保存する。
            assessmentHomeLogic.homeLogicCpnTucGdlKadai(loginDto, sc1Id);
        }
        outDto.setGdlId(inDto.getGdlId());
        outDto.setSc1Id(CommonDtoUtil.objValToString(sc1Id));
        return outDto;
    }

    /**
     * ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_kio11_h21）の削除更新詳細
     * 関数名：deletekio11H21
     * 
     * @param inDto 更新パラメータ
     * @param sc1Id 計画対象期間ID
     * @return 結果件数
     */
    private void deletekio11H21(AssessmentHomeTab5UpdateServiceInDto inDto, Integer sc1Id) {
        // ＧＬ＿サービス利用状況（Ｈ２１改訂）パラメータを作成
        CpnTucGdl4Kio11H21Criteria cpnTucGdl4Kio11H21Criteria = new CpnTucGdl4Kio11H21Criteria();
        cpnTucGdl4Kio11H21Criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(sc1Id);

        this.cpnTucGdl4Kio11H21Mapper.deleteByCriteria(cpnTucGdl4Kio11H21Criteria);

    }

    /**
     * ＧＬ＿本人の健康状態・受診等の状況（R３改訂）テーブル（cpn_tuc_gdl5_kio11_r3）の削除更新詳細
     * 関数名：deletekio11R3
     * 
     * @param inDto 更新パラメータ
     * @param sc1Id 計画対象期間ID
     * @return 結果件数
     */
    private void deletekio11R3(AssessmentHomeTab5UpdateServiceInDto inDto, Integer sc1Id) {
        // ＧＬ＿サービス利用状況（Ｈ２１改訂）パラメータを作成
        CpnTucGdl5Kio11R3Criteria cpnTucGdl5Kio11R3Criteria = new CpnTucGdl5Kio11R3Criteria();
        cpnTucGdl5Kio11R3Criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(sc1Id);

        this.cpnTucGdl5Kio11R3Mapper.deleteByCriteria(cpnTucGdl5Kio11R3Criteria);

    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
     * 関数名：clearRrk
     * 
     * @param inDto 更新パラメータ
     * @param sc1Id 計画対象期間ID
     * @return 結果件数
     */
    private void clearRrk(AssessmentHomeTab5UpdateServiceInDto inDto, Integer sc1Id) {
        // DAOパラメータを作成
        // 3.1.居宅アセスメント履歴を更新する
        // ＧＬ＿居宅アセスメント履歴パラメータを作成
        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(sc1Id)
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                // 利用者ＩＤ
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 法人ID
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        // ｻｰﾋﾞｽ利用状況
        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();
        cpnTucGdlRireki.setAss5(StringUtils.EMPTY);

        // DAOを実行
        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRireki, criteria);

    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を登録する。
     * 関数名：insertRrk
     * 
     * @param inDto 更新パラメータ
     * @param sc1Id 計画対象期間ID
     */
    private void insertRrk(AssessmentHomeTab5UpdateServiceInDto inDto, Integer sc1Id) throws Exception {
        // ＧＬ＿居宅アセスメント履歴パラメータを作成
        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();

        // 計画期間ID
        cpnTucGdlRireki.setSc1Id(sc1Id);
        // 法人ID
        cpnTucGdlRireki.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHoujinId()));
        // 施設ID
        cpnTucGdlRireki.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        cpnTucGdlRireki.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ＩＤ
        cpnTucGdlRireki.setUserId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // アセスメント実施日
        cpnTucGdlRireki.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRireki.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // 本人の基本動作等1
        cpnTucGdlRireki.setAss5(CommonConstants.MARU);
        // Ｊの状態
        cpnTucGdlRireki.setAss12(CommonConstants.DASH);
        // 本人の基本動作等8
        cpnTucGdlRireki.setAss13(CommonConstants.DASH);
        // 改定フラグ
        cpnTucGdlRireki.setNinteiFormF(CommonDtoUtil.strValToInt(inDto.getNinteiFlg()));

        this.cpnTucGdlRirekiMapper.insertSelectiveAndReturn(cpnTucGdlRireki);

        // 変数.アセスメントID=採番したアセスメントID
        inDto.setGdlId(CommonDtoUtil.objValToString(cpnTucGdlRireki.getGdlId()));

    }

    /**
     * 【ＧＬ＿居宅アセスメント履歴】情報を更新する。
     * 関数名：updateRrk
     * 
     * @param inDto 更新パラメータ
     * @param sc1Id 計画対象期間ID
     * @return 結果件数
     */
    private void updateRrk(AssessmentHomeTab5UpdateServiceInDto inDto, Integer sc1Id) {
        CpnTucGdlRirekiCriteria criteria = new CpnTucGdlRirekiCriteria();
        // アセスメントID
        criteria.createCriteria().andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(sc1Id)
                // 事業者ID
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()))
                // 利用者ＩＤ
                .andUserIdEqualTo(CommonDtoUtil.strValToInt(inDto.getUserId()))
                // 法人ID
                .andHoujinIdEqualTo(CommonDtoUtil.strValToInt(inDto.getHoujinId()))
                // 施設ID
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()));

        // 削除フラグ設定
        CpnTucGdlRireki cpnTucGdlRireki = new CpnTucGdlRireki();
        // アセスメント実施日
        cpnTucGdlRireki.setAsJisshiDateYmd(inDto.getKijunbiYmd());
        // 記載者ID
        cpnTucGdlRireki.setShokuId(CommonDtoUtil.strValToInt(inDto.getSakuseiId()));
        // 記載者ID
        cpnTucGdlRireki.setAss5(CommonConstants.MARU);
        // Ｊの状態
        cpnTucGdlRireki.setAss12(CommonConstants.DASH);
        // 本人の基本動作等8
        cpnTucGdlRireki.setAss13(CommonConstants.DASH);

        // DAOを実行
        this.cpnTucGdlRirekiMapper.updateByCriteriaSelective(cpnTucGdlRireki, criteria);
    }

    /**
     * ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_kio11_h21）の登録詳細
     * 関数名：insertKio11H21
     * 
     * @param inDto 更新パラメータ
     * @param sc1Id 計画対象期間ID
     */
    private void insertKio11H21(AssessmentHomeTab5UpdateServiceInDto inDto, Integer sc1Id) throws Exception {
        CpnTucGdl4Kio11H21 cpnTucGdl4Kio11H21 = new CpnTucGdl4Kio11H21();
        // ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）パラメータを作成
        cpnTucGdl4Kio11H21 = this.setCpnTucGdl4Kio11H21(inDto.getHealthStateVisitSituationInfo());

        // アセスメントID
        cpnTucGdl4Kio11H21.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // 計画期間ID
        cpnTucGdl4Kio11H21.setSc1Id(sc1Id);

        this.cpnTucGdl4Kio11H21Mapper.insertSelective(cpnTucGdl4Kio11H21);

    }

    /**
     * ＧＬ＿本人の健康状態・受診等の状況（R３改訂）テーブル（cpn_tuc_gdl5_kio11_r3）の登録詳細
     * 関数名：insertKio11R3
     * 
     * @param inDto 更新パラメータ
     * @param sc1Id 計画対象期間ID
     */
    private void insertKio11R3(AssessmentHomeTab5UpdateServiceInDto inDto, Integer sc1Id) throws Exception {
        CpnTucGdl5Kio11R3 cpnTucGdl5Kio11R3 = new CpnTucGdl5Kio11R3();
        // ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）パラメータを作成
        cpnTucGdl5Kio11R3 = this.setcpnTucGdl5Kio11R3(inDto.getHealthStateVisitSituationInfo());

        // アセスメントID
        cpnTucGdl5Kio11R3.setGdlId(CommonDtoUtil.strValToInt(inDto.getGdlId()));
        // 計画期間ID
        cpnTucGdl5Kio11R3.setSc1Id(sc1Id);

        this.cpnTucGdl5Kio11R3Mapper.insertSelective(cpnTucGdl5Kio11R3);

    }

    /**
     * ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_kio11_h21）の更新詳細
     * 関数名：updateKio11H21
     * 
     * @param inDto 更新パラメータ
     * @param sc1Id 計画対象期間ID
     * @return 結果件数
     */
    private void updateKio11H21(AssessmentHomeTab5UpdateServiceInDto inDto, Integer sc1Id) throws Exception {
        CpnTucGdl4Kio11H21 cpnTucGdl4Kio11H21 = new CpnTucGdl4Kio11H21();
        // ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）パラメータを作成
        cpnTucGdl4Kio11H21 = this.setCpnTucGdl4Kio11H21(inDto.getHealthStateVisitSituationInfo());

        CpnTucGdl4Kio11H21Criteria cpnTucGdl4Kio11H21Criteria = new CpnTucGdl4Kio11H21Criteria();

        cpnTucGdl4Kio11H21Criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(sc1Id);

        this.cpnTucGdl4Kio11H21Mapper.updateByCriteriaSelective(cpnTucGdl4Kio11H21,
                cpnTucGdl4Kio11H21Criteria);
    }

    /**
     * ＧＬ＿本人の健康状態・受診等の状況（R３改訂）テーブル（cpn_tuc_gdl5_kio11_r3）の更新詳細
     * 関数名：updateKio11R3
     * 
     * @param inDto 更新パラメータ
     * @param sc1Id 計画対象期間ID
     * @return 結果件数
     */
    private void updateKio11R3(AssessmentHomeTab5UpdateServiceInDto inDto, Integer sc1Id) throws Exception {
        CpnTucGdl5Kio11R3 cpnTucGdl5Kio11R3 = new CpnTucGdl5Kio11R3();
        // ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）パラメータを作成
        cpnTucGdl5Kio11R3 = this.setcpnTucGdl5Kio11R3(inDto.getHealthStateVisitSituationInfo());

        CpnTucGdl5Kio11R3Criteria cpnTucGdl5Kio11R3Criteria = new CpnTucGdl5Kio11R3Criteria();

        cpnTucGdl5Kio11R3Criteria.createCriteria()
                // アセスメントID
                .andGdlIdEqualTo(CommonDtoUtil.strValToInt(inDto.getGdlId()))
                // 計画期間ID
                .andSc1IdEqualTo(sc1Id);

        this.cpnTucGdl5Kio11R3Mapper.updateByCriteriaSelective(
                cpnTucGdl5Kio11R3,
                cpnTucGdl5Kio11R3Criteria);

    }

    /**
     * 【27-06記録共通期間】情報を登録パラメータを設定する
     * 
     * @param inDto 施設マスタ
     * @return 【27-06記録共通期間】データ保存入力DTO.
     */
    private AssessmentHomeSaveServiceInDto setAssessmentHomeSaveServiceInDto(
            AssessmentHomeTab5UpdateServiceInDto inDto) {
        AssessmentHomeSaveServiceInDto assessmentHomeSaveServiceInDto = new AssessmentHomeSaveServiceInDto();
        // 法人ID
        assessmentHomeSaveServiceInDto.setHoujinId(inDto.getHoujinId());
        // 施設ID
        assessmentHomeSaveServiceInDto.setShisetuId(inDto.getShisetuId());
        // 事業者ID
        assessmentHomeSaveServiceInDto.setSvJigyoId(inDto.getSvJigyoId());
        // 利用者ID
        assessmentHomeSaveServiceInDto.setUserId(inDto.getUserId());
        // 種別ID
        assessmentHomeSaveServiceInDto.setSyubetsuId(inDto.getSyubetsuId());
        // 作成日
        assessmentHomeSaveServiceInDto.setKijunbiYmd(inDto.getKijunbiYmd());

        return assessmentHomeSaveServiceInDto;

    }

    /**
     * ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_kio11_h21）の登録詳細パラメータを設定する
     * 
     * @param healthStateVisitSituationInfo 住居等の状況情報
     * @return ＧＬ＿本人の健康状態・受診等の状況（Ｈ２１改訂）テーブル（cpn_tuc_gdl4_kio11_h21）の登録詳細パラメータ
     */
    private CpnTucGdl4Kio11H21 setCpnTucGdl4Kio11H21(
            Gui00798HealthStateVisitSituationInfo healthStateVisitSituationInfo) {
        CpnTucGdl4Kio11H21 cpnTucGdl4Kio11H21 = new CpnTucGdl4Kio11H21();

        // 既往歴現症 */
        cpnTucGdl4Kio11H21.setKiouMemoKnj(healthStateVisitSituationInfo.getKiouMemoKnj());
        // 特記事項 */
        cpnTucGdl4Kio11H21.setMemo1Knj(healthStateVisitSituationInfo.getMemo1Knj());
        // 病名1 */
        cpnTucGdl4Kio11H21.setByomei1Knj(healthStateVisitSituationInfo.getByomei1Knj());
        // 病名2 */
        cpnTucGdl4Kio11H21.setByomei2Knj(healthStateVisitSituationInfo.getByomei2Knj());
        // 病名3 */
        cpnTucGdl4Kio11H21.setByomei3Knj(healthStateVisitSituationInfo.getByomei3Knj());
        // 病名4 */
        cpnTucGdl4Kio11H21.setByomei4Knj(healthStateVisitSituationInfo.getByomei4Knj());
        // 薬の有無1 */
        cpnTucGdl4Kio11H21.setDrugUmu1(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getDrugUmu1()));
        // 薬の有無2 */
        cpnTucGdl4Kio11H21.setDrugUmu2(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getDrugUmu2()));
        // 薬の有無3 */
        cpnTucGdl4Kio11H21.setDrugUmu3(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getDrugUmu3()));
        // 薬の有無4 */
        cpnTucGdl4Kio11H21.setDrugUmu4(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getDrugUmu4()));
        // 受診定期1 */
        cpnTucGdl4Kio11H21.setJuTeiki1(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuTeiki1()));
        // 受診定期2 */
        cpnTucGdl4Kio11H21.setJuTeiki2(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuTeiki2()));
        // 受診定期3 */
        cpnTucGdl4Kio11H21.setJuTeiki3(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuTeiki3()));
        // 受診定期4 */
        cpnTucGdl4Kio11H21.setJuTeiki4(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuTeiki4()));
        // 週（月）1 */
        cpnTucGdl4Kio11H21.setShubetsu1(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getShubetsu1()));
        // 週（月）2 */
        cpnTucGdl4Kio11H21.setShubetsu2(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getShubetsu2()));
        // 週（月）3 */
        cpnTucGdl4Kio11H21.setShubetsu3(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getShubetsu3()));
        // 週（月）4 */
        cpnTucGdl4Kio11H21.setShubetsu4(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getShubetsu4()));
        // 受診回数1 */
        cpnTucGdl4Kio11H21.setJuKaisu1(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuKaisu1()));
        // 受診回数2 */
        cpnTucGdl4Kio11H21.setJuKaisu2(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuKaisu2()));
        // 受診回数3 */
        cpnTucGdl4Kio11H21.setJuKaisu3(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuKaisu3()));
        // 受診回数4 */
        cpnTucGdl4Kio11H21.setJuKaisu4(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuKaisu4()));
        // 受診不定期1 */
        cpnTucGdl4Kio11H21.setJuHuTeiki1(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuHuTeiki1()));
        // 受診不定期2 */
        cpnTucGdl4Kio11H21.setJuHuTeiki2(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuHuTeiki2()));
        // 受診不定期3 */
        cpnTucGdl4Kio11H21.setJuHuTeiki3(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuHuTeiki3()));
        // 受診不定期4 */
        cpnTucGdl4Kio11H21.setJuHuTeiki4(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuHuTeiki4()));
        // 受診状況1 */
        cpnTucGdl4Kio11H21.setJuJokyo1(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuJokyo1()));
        // 受診状況2 */
        cpnTucGdl4Kio11H21.setJuJokyo2(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuJokyo2()));
        // 受診状況3 */
        cpnTucGdl4Kio11H21.setJuJokyo3(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuJokyo3()));
        // 受診状況4 */
        cpnTucGdl4Kio11H21.setJuJokyo4(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuJokyo4()));
        // 医療機関名1 */
        cpnTucGdl4Kio11H21.setHosp1Knj(healthStateVisitSituationInfo.getHosp1Knj());
        // 医療機関名2 */
        cpnTucGdl4Kio11H21.setHosp2Knj(healthStateVisitSituationInfo.getHosp2Knj());
        // 医療機関名3 */
        cpnTucGdl4Kio11H21.setHosp3Knj(healthStateVisitSituationInfo.getHosp3Knj());
        // 医療機関名4 */
        cpnTucGdl4Kio11H21.setHosp4Knj(healthStateVisitSituationInfo.getHosp4Knj());
        // 診療科名1 */
        cpnTucGdl4Kio11H21.setHospKa1Knj(healthStateVisitSituationInfo.getHospKa1Knj());
        // 診療科名2 */
        cpnTucGdl4Kio11H21.setHospKa2Knj(healthStateVisitSituationInfo.getHospKa2Knj());
        // 診療科名3 */
        cpnTucGdl4Kio11H21.setHospKa3Knj(healthStateVisitSituationInfo.getHospKa3Knj());
        // 診療科名4 */
        cpnTucGdl4Kio11H21.setHospKa4Knj(healthStateVisitSituationInfo.getHospKa4Knj());
        // 医師名1 */
        cpnTucGdl4Kio11H21.setHospDr1Knj(healthStateVisitSituationInfo.getHospDr1Knj());
        // 医師名2 */
        cpnTucGdl4Kio11H21.setHospDr2Knj(healthStateVisitSituationInfo.getHospDr2Knj());
        // 医師名3 */
        cpnTucGdl4Kio11H21.setHospDr3Knj(healthStateVisitSituationInfo.getHospDr3Knj());
        // 医師名4 */
        cpnTucGdl4Kio11H21.setHospDr4Knj(healthStateVisitSituationInfo.getHospDr4Knj());
        // 連絡先TEL1 */
        cpnTucGdl4Kio11H21.setTel1(healthStateVisitSituationInfo.getTel1());
        // 連絡先TEL2 */
        cpnTucGdl4Kio11H21.setTel2(healthStateVisitSituationInfo.getTel2());
        // 連絡先TEL3 */
        cpnTucGdl4Kio11H21.setTel3(healthStateVisitSituationInfo.getTel3());
        // 連絡先TEL4 */
        cpnTucGdl4Kio11H21.setTel4(healthStateVisitSituationInfo.getTel4());
        // 往診可能な医療機関の有無 */
        cpnTucGdl4Kio11H21.setOshinHospUmu(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getOshinHospUmu()));
        // 往診可能な医療機関名 */
        cpnTucGdl4Kio11H21.setOshinHospKnj(healthStateVisitSituationInfo.getOshinHospKnj());
        // 緊急入院できる医療機関の有無 */
        cpnTucGdl4Kio11H21
                .setKinkyuHospUmu(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getKinkyuHospUmu()));
        // 緊急入院できる医療機関名 */
        cpnTucGdl4Kio11H21.setKinkyuHospKnj(healthStateVisitSituationInfo.getKinkyuHospKnj());
        // 相談処方を受けている薬局 */
        cpnTucGdl4Kio11H21.setSouYakuUmu(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getSouYakuUmu()));
        // 薬局名 */
        cpnTucGdl4Kio11H21.setSouYakuKnj(healthStateVisitSituationInfo.getSouYakuKnj());
        // 特記生活上配慮すべき課題など */
        cpnTucGdl4Kio11H21.setMemo2Knj(healthStateVisitSituationInfo.getMemo2Knj());
        // 往診可能な医療機関の電話番号 */
        cpnTucGdl4Kio11H21.setOshinTel(healthStateVisitSituationInfo.getOshinTel());
        // 緊急入院できる医療機関の電話番号 */
        cpnTucGdl4Kio11H21.setKinkyuTel(healthStateVisitSituationInfo.getKinkyuTel());
        // 薬局tel */
        cpnTucGdl4Kio11H21.setYakuTel(healthStateVisitSituationInfo.getYakuTel());
        // 身長 */
        cpnTucGdl4Kio11H21.setSincho(healthStateVisitSituationInfo.getSincho());
        // 体重 */
        cpnTucGdl4Kio11H21.setTaiju(healthStateVisitSituationInfo.getTaiju());
        // 歯あり */
        cpnTucGdl4Kio11H21.setHa1(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getHa1()));
        // 歯なし */
        cpnTucGdl4Kio11H21.setHa2(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getHa2()));
        // 総入れ歯 */
        cpnTucGdl4Kio11H21.setHa3(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getHa3()));
        // 局部義歯 */
        cpnTucGdl4Kio11H21.setHa4(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getHa4()));
        // 受診方法留意点等1 */
        cpnTucGdl4Kio11H21.setJusinHouhou1Knj(healthStateVisitSituationInfo.getJusinHouhou1Knj());
        // 受診方法留意点等2 */
        cpnTucGdl4Kio11H21.setJusinHouhou2Knj(healthStateVisitSituationInfo.getJusinHouhou2Knj());
        // 受診方法留意点等3 */
        cpnTucGdl4Kio11H21.setJusinHouhou3Knj(healthStateVisitSituationInfo.getJusinHouhou3Knj());
        // 受診方法留意点等4 */
        cpnTucGdl4Kio11H21.setJusinHouhou4Knj(healthStateVisitSituationInfo.getJusinHouhou4Knj());
        // 発症時期1 */
        cpnTucGdl4Kio11H21.setSickYmd1Knj(healthStateVisitSituationInfo.getSickYmd1Knj());
        // 発症時期2 */
        cpnTucGdl4Kio11H21.setSickYmd2Knj(healthStateVisitSituationInfo.getSickYmd2Knj());
        // 発症時期3 */
        cpnTucGdl4Kio11H21.setSickYmd3Knj(healthStateVisitSituationInfo.getSickYmd3Knj());
        // 発症時期4 */
        cpnTucGdl4Kio11H21.setSickYmd4Knj(healthStateVisitSituationInfo.getSickYmd4Knj());

        return cpnTucGdl4Kio11H21;

    }

    /**
     * ＧＬ＿本人の健康状態・受診等の状況（R３改訂）テーブル（cpn_tuc_gdl5_kio11_r3）の登録詳細パラメータを設定する
     * 
     * @param healthStateVisitSituationInfo 住居等の状況情報
     * @return ＧＬ＿本人の健康状態・受診等の状況（R３改訂）テーブル（cpn_tuc_gdl5_kio11_r3）の登録詳細パラメータ
     */
    private CpnTucGdl5Kio11R3 setcpnTucGdl5Kio11R3(
            Gui00798HealthStateVisitSituationInfo healthStateVisitSituationInfo) {
        CpnTucGdl5Kio11R3 cpnTucGdl5Kio11R3 = new CpnTucGdl5Kio11R3();

        // 既往歴現症 */
        cpnTucGdl5Kio11R3.setKiouMemoKnj(healthStateVisitSituationInfo.getKiouMemoKnj());
        // 特記事項 */
        cpnTucGdl5Kio11R3.setMemo1Knj(healthStateVisitSituationInfo.getMemo1Knj());
        // 病名1 */
        cpnTucGdl5Kio11R3.setByomei1Knj(healthStateVisitSituationInfo.getByomei1Knj());
        // 病名2 */
        cpnTucGdl5Kio11R3.setByomei2Knj(healthStateVisitSituationInfo.getByomei2Knj());
        // 病名3 */
        cpnTucGdl5Kio11R3.setByomei3Knj(healthStateVisitSituationInfo.getByomei3Knj());
        // 病名4 */
        cpnTucGdl5Kio11R3.setByomei4Knj(healthStateVisitSituationInfo.getByomei4Knj());
        // 薬の有無1 */
        cpnTucGdl5Kio11R3.setDrugUmu1(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getDrugUmu1()));
        // 薬の有無2 */
        cpnTucGdl5Kio11R3.setDrugUmu2(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getDrugUmu2()));
        // 薬の有無3 */
        cpnTucGdl5Kio11R3.setDrugUmu3(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getDrugUmu3()));
        // 薬の有無4 */
        cpnTucGdl5Kio11R3.setDrugUmu4(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getDrugUmu4()));
        // 受診定期1 */
        cpnTucGdl5Kio11R3.setJuTeiki1(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuTeiki1()));
        // 受診定期2 */
        cpnTucGdl5Kio11R3.setJuTeiki2(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuTeiki2()));
        // 受診定期3 */
        cpnTucGdl5Kio11R3.setJuTeiki3(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuTeiki3()));
        // 受診定期4 */
        cpnTucGdl5Kio11R3.setJuTeiki4(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuTeiki4()));
        // 週（月）1 */
        cpnTucGdl5Kio11R3.setShubetsu1(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getShubetsu1()));
        // 週（月）2 */
        cpnTucGdl5Kio11R3.setShubetsu2(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getShubetsu2()));
        // 週（月）3 */
        cpnTucGdl5Kio11R3.setShubetsu3(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getShubetsu3()));
        // 週（月）4 */
        cpnTucGdl5Kio11R3.setShubetsu4(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getShubetsu4()));
        // 受診回数1 */
        cpnTucGdl5Kio11R3.setJuKaisu1(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuKaisu1()));
        // 受診回数2 */
        cpnTucGdl5Kio11R3.setJuKaisu2(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuKaisu2()));
        // 受診回数3 */
        cpnTucGdl5Kio11R3.setJuKaisu3(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuKaisu3()));
        // 受診回数4 */
        cpnTucGdl5Kio11R3.setJuKaisu4(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuKaisu4()));
        // 受診不定期1 */
        cpnTucGdl5Kio11R3.setJuHuTeiki1(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuHuTeiki1()));
        // 受診不定期2 */
        cpnTucGdl5Kio11R3.setJuHuTeiki2(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuHuTeiki2()));
        // 受診不定期3 */
        cpnTucGdl5Kio11R3.setJuHuTeiki3(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuHuTeiki3()));
        // 受診不定期4 */
        cpnTucGdl5Kio11R3.setJuHuTeiki4(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuHuTeiki4()));
        // 受診状況1 */
        cpnTucGdl5Kio11R3.setJuJokyo1(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuJokyo1()));
        // 受診状況2 */
        cpnTucGdl5Kio11R3.setJuJokyo2(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuJokyo2()));
        // 受診状況3 */
        cpnTucGdl5Kio11R3.setJuJokyo3(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuJokyo3()));
        // 受診状況4 */
        cpnTucGdl5Kio11R3.setJuJokyo4(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getJuJokyo4()));
        // 医療機関名1 */
        cpnTucGdl5Kio11R3.setHosp1Knj(healthStateVisitSituationInfo.getHosp1Knj());
        // 医療機関名2 */
        cpnTucGdl5Kio11R3.setHosp2Knj(healthStateVisitSituationInfo.getHosp2Knj());
        // 医療機関名3 */
        cpnTucGdl5Kio11R3.setHosp3Knj(healthStateVisitSituationInfo.getHosp3Knj());
        // 医療機関名4 */
        cpnTucGdl5Kio11R3.setHosp4Knj(healthStateVisitSituationInfo.getHosp4Knj());
        // 診療科名1 */
        cpnTucGdl5Kio11R3.setHospKa1Knj(healthStateVisitSituationInfo.getHospKa1Knj());
        // 診療科名2 */
        cpnTucGdl5Kio11R3.setHospKa2Knj(healthStateVisitSituationInfo.getHospKa2Knj());
        // 診療科名3 */
        cpnTucGdl5Kio11R3.setHospKa3Knj(healthStateVisitSituationInfo.getHospKa3Knj());
        // 診療科名4 */
        cpnTucGdl5Kio11R3.setHospKa4Knj(healthStateVisitSituationInfo.getHospKa4Knj());
        // 医師名1 */
        cpnTucGdl5Kio11R3.setHospDr1Knj(healthStateVisitSituationInfo.getHospDr1Knj());
        // 医師名2 */
        cpnTucGdl5Kio11R3.setHospDr2Knj(healthStateVisitSituationInfo.getHospDr2Knj());
        // 医師名3 */
        cpnTucGdl5Kio11R3.setHospDr3Knj(healthStateVisitSituationInfo.getHospDr3Knj());
        // 医師名4 */
        cpnTucGdl5Kio11R3.setHospDr4Knj(healthStateVisitSituationInfo.getHospDr4Knj());
        // 連絡先TEL1 */
        cpnTucGdl5Kio11R3.setTel1(healthStateVisitSituationInfo.getTel1());
        // 連絡先TEL2 */
        cpnTucGdl5Kio11R3.setTel2(healthStateVisitSituationInfo.getTel2());
        // 連絡先TEL3 */
        cpnTucGdl5Kio11R3.setTel3(healthStateVisitSituationInfo.getTel3());
        // 連絡先TEL4 */
        cpnTucGdl5Kio11R3.setTel4(healthStateVisitSituationInfo.getTel4());
        // 往診可能な医療機関の有無 */
        cpnTucGdl5Kio11R3.setOshinHospUmu(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getOshinHospUmu()));
        // 往診可能な医療機関名 */
        cpnTucGdl5Kio11R3.setOshinHospKnj(healthStateVisitSituationInfo.getOshinHospKnj());
        // 緊急入院できる医療機関の有無 */
        cpnTucGdl5Kio11R3.setKinkyuHospUmu(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getKinkyuHospUmu()));
        // 緊急入院できる医療機関名 */
        cpnTucGdl5Kio11R3.setKinkyuHospKnj(healthStateVisitSituationInfo.getKinkyuHospKnj());
        // 相談処方を受けている薬局 */
        cpnTucGdl5Kio11R3.setSouYakuUmu(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getSouYakuUmu()));
        // 薬局名 */
        cpnTucGdl5Kio11R3.setSouYakuKnj(healthStateVisitSituationInfo.getSouYakuKnj());
        // 特記生活上配慮すべき課題など */
        cpnTucGdl5Kio11R3.setMemo2Knj(healthStateVisitSituationInfo.getMemo2Knj());
        // 往診可能な医療機関の電話番号 */
        cpnTucGdl5Kio11R3.setOshinTel(healthStateVisitSituationInfo.getOshinTel());
        // 緊急入院できる医療機関の電話番号 */
        cpnTucGdl5Kio11R3.setKinkyuTel(healthStateVisitSituationInfo.getKinkyuTel());
        // 薬局tel */
        cpnTucGdl5Kio11R3.setYakuTel(healthStateVisitSituationInfo.getYakuTel());
        // 身長 */
        cpnTucGdl5Kio11R3.setSincho(healthStateVisitSituationInfo.getSincho());
        // 体重 */
        cpnTucGdl5Kio11R3.setTaiju(healthStateVisitSituationInfo.getTaiju());
        // 歯あり */
        cpnTucGdl5Kio11R3.setHa1(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getHa1()));
        // 歯なし */
        cpnTucGdl5Kio11R3.setHa2(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getHa2()));
        // 総入れ歯 */
        cpnTucGdl5Kio11R3.setHa3(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getHa3()));
        // 局部義歯 */
        cpnTucGdl5Kio11R3.setHa4(CommonDtoUtil.strValToInt(healthStateVisitSituationInfo.getHa4()));
        // 受診方法留意点等1 */
        cpnTucGdl5Kio11R3.setJusinHouhou1Knj(healthStateVisitSituationInfo.getJusinHouhou1Knj());
        // 受診方法留意点等2 */
        cpnTucGdl5Kio11R3.setJusinHouhou2Knj(healthStateVisitSituationInfo.getJusinHouhou2Knj());
        // 受診方法留意点等3 */
        cpnTucGdl5Kio11R3.setJusinHouhou3Knj(healthStateVisitSituationInfo.getJusinHouhou3Knj());
        // 受診方法留意点等4 */
        cpnTucGdl5Kio11R3.setJusinHouhou4Knj(healthStateVisitSituationInfo.getJusinHouhou4Knj());
        // 発症時期1 */
        cpnTucGdl5Kio11R3.setSickYmd1Knj(healthStateVisitSituationInfo.getSickYmd1Knj());
        // 発症時期2 */
        cpnTucGdl5Kio11R3.setSickYmd2Knj(healthStateVisitSituationInfo.getSickYmd2Knj());
        // 発症時期3 */
        cpnTucGdl5Kio11R3.setSickYmd3Knj(healthStateVisitSituationInfo.getSickYmd3Knj());
        // 発症時期4 */
        cpnTucGdl5Kio11R3.setSickYmd4Knj(healthStateVisitSituationInfo.getSickYmd4Knj());

        return cpnTucGdl5Kio11R3;

    }
}
