package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794CommonInformationGdlKadai;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794CommonInformationHistoryInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794CommonInformationPlanPeriodInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794<PERSON>eishin;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Teido;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Toukyuu;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Yokaigo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00794Zokugara;
import jp.ndsoft.carebase.cmn.api.logic.AssessmentHomeLogic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZOther01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZSnc01Logic;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeCommonInfoSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentHomeCommonInfoSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMstSeishinToukyuuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMstSeishinToukyuuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMstTeidoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMstTeidoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMucZokugaraInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMucZokugaraInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KadaiTourokuSc1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucKrkKikanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KyotakuRirekiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TechouToukyuuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TechouToukyuuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YokaigoKbnListByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YokaigoKbnListOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscYokaigoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMstSeishinToukyuuSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMstTeidoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMstToukyuuSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMucZokugaraSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.04.17
 * <AUTHOR>
 * @apiNote GUI00794_［アセスメント］画面（居宅）（1） 共通情報
 */
@Service
public class AssessmentHomeCommonInfoSelectServiceImpl
        extends
        SelectServiceImpl<AssessmentHomeCommonInfoSelectServiceInDto, AssessmentHomeCommonInfoSelectServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 共通情報取得 */
    @Autowired
    private AssessmentHomeLogic assessmentHomeLogic;

    /** 共通部品「f_kgh_krk_get_kikan」 */
    @Autowired
    private KghKrkZSnc01Logic kghKrkZSnc01Logic;

    /** 共通部品「f_kgh_krk_get_syubetu」 */
    @Autowired
    private KghKrkZOther01Logic kghKrkZOther01Logic;

    /** 続柄マスタ情報取得 */
    @Autowired
    private ComMucZokugaraSelectMapper comMucZokugaraSelectMapper;

    /** 要介護状態マスタ（４－４）情報取得 */
    @Autowired
    private ComMscYokaigoSelectMapper comMscYokaigoSelectMapper;

    /** 手帳等級情報取得 */
    @Autowired
    private ComMstToukyuuSelectMapper comMstToukyuuSelectMapper;

    /** 障害の程度マスタ情報取得 */
    @Autowired
    private ComMstTeidoSelectMapper comMstTeidoSelectMapper;

    /** 精神の等級マスタ情報取得 */
    @Autowired
    private ComMstSeishinToukyuuSelectMapper comMstSeishinToukyuuSelectMapper;

    /**
     * 共通情報
     * 
     * @param inDto 共通情報の入力DTO.
     * @return共通情報OUT DTO
     * @throws Exception Exception
     */
    @Override
    protected AssessmentHomeCommonInfoSelectServiceOutDto mainProcess(
            AssessmentHomeCommonInfoSelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // 戻り情報を設定
        AssessmentHomeCommonInfoSelectServiceOutDto out = new AssessmentHomeCommonInfoSelectServiceOutDto();
        // 計画期間情報
        Gui00794CommonInformationPlanPeriodInfo planPeriodInfo = new Gui00794CommonInformationPlanPeriodInfo();
        // 履歴情報
        Gui00794CommonInformationHistoryInfo historyInfo = new Gui00794CommonInformationHistoryInfo();
        // 課題と目標情報
        List<Gui00794CommonInformationGdlKadai> gdlKadaiList = new ArrayList<Gui00794CommonInformationGdlKadai>();

        // 続柄マスタ情報リスト
        List<Gui00794Zokugara> gui00794ZokugaraList = new ArrayList<Gui00794Zokugara>();

        // 要介護状態情報リスト
        List<Gui00794Yokaigo> gui00794YokaigoList = new ArrayList<Gui00794Yokaigo>();

        // 身障手帳等級情報リスト
        List<Gui00794Toukyuu> gui00794ToukyuuList = new ArrayList<Gui00794Toukyuu>();

        // 療育手帳程度情報リスト
        List<Gui00794Teido> gui00794TeidoList = new ArrayList<Gui00794Teido>();

        // 精神等級情報リスト
        List<Gui00794Seishin> gui00794SeishinList = new ArrayList<Gui00794Seishin>();
        // 2.1.1.下記の共通関数「期間管理するかしないかを判断する」を利用し、種別IDを取得する。
        Integer syubetuId = kghKrkZOther01Logic.getKghKrkSyubetu("[mnu2][3GK][GDL]ｱｾｽﾒﾝﾄ", "[mnu3][3GK][GDL]ｱｾｽﾒﾝﾄ",
                CommonConstants.BLANK_STRING);
        out.setSyubetsuId(CommonDtoUtil.objValToString(syubetuId));

        // 2.画面共通情報を取得する
        // 2.1. 下記の記録共通期間取得のDAOを利用し、計画期間情報を取得する。
        List<KghTucKrkKikanOutEntity> kghTucKrkKikanList = assessmentHomeLogic.findKghTucKrkKikan(
                CommonDtoUtil
                        .strValToInt(inDto.getSvJigyoId()),
                CommonDtoUtil
                        .strValToInt(inDto.getUserid()),
                syubetuId,
                CommonDtoUtil
                        .strValToInt(inDto.getShisetuId()));

        // 2.2. 「2.1.」で取得した計画期間情報の一行目を計画期間情報とする。
        if (kghTucKrkKikanList.size() > 0) {
            // 期間ID */
            planPeriodInfo.setSc1Id(CommonDtoUtil.objValToString(kghTucKrkKikanList.get(0).getSc1Id()));
            // 開始日 */
            planPeriodInfo.setStartYmd(kghTucKrkKikanList.get(0).getStartYmd());
            // 終了日 */
            planPeriodInfo.setEndYmd(kghTucKrkKikanList.get(0).getEndYmd());
        }

        // 2.3. 「2.1.」で取得した計画期間情報の件数を計画期間情報.期間総件数に設定する。
        // 期間番号 periodNo
        planPeriodInfo.setPeriodNo(CommonDtoUtil.objValToString(kghTucKrkKikanList.size()));
        // 期間総件数
        planPeriodInfo.setPeriodCnt(CommonDtoUtil.objValToString(kghTucKrkKikanList.size()));

        // 3.1.1.下記の共通関数「期間管理するかしないかを判断する」を利用し、期間管理フラグの取得を行う。

        boolean kikanFlg = kghKrkZSnc01Logic.getKghKrkKikan(syubetuId,
                CommonDtoUtil.strValToInt(inDto.getSvJigyoId()),
                CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 出力Dto: 期間管理フラグ
        out.setKikanFlg(kikanFlg ? CommonConstants.PERIOD_MANAGE_FLG : CommonConstants.PERIOD_NO_MANAGE_FLG);

        // 3. 「2.」で取得した計画期間情報.件数 > 0 件の場合、居宅アセスメント履歴リストを取得する。
        // 3.1. 【変数】.期間ID を設定する。
        Integer sc1Id = 0;
        if (kikanFlg) {
            // 3.1.2.「3.1.1.」で取得した期間管理フラグが「管理する」の場合、
            // 【変数】.期間ID = 「2.2.」で取得した計画期間情報.期間ID
            sc1Id = CommonDtoUtil.strValToInt(planPeriodInfo.getSc1Id());
        }
        // 3. 「2.」で取得した計画期間情報.件数 > 0 件の場合、居宅アセスメント履歴リストを取得する。
        if (kghTucKrkKikanList != null && kghTucKrkKikanList.size() > 0) {
            // 居宅アセスメント履歴リストを取得
            List<KyotakuRirekiOutEntity> kyotakuRirekiList = assessmentHomeLogic.findKyotakuRireki(sc1Id,
                    CommonDtoUtil.strValToInt(inDto.getSvJigyoId()), CommonDtoUtil.strValToInt(inDto.getUserid()));
            if (kyotakuRirekiList != null && kyotakuRirekiList.size() > 0) {
                for (KyotakuRirekiOutEntity itemOutEntity : kyotakuRirekiList) {
                    if (CommonConstants.KAI_TEI_FLAG_R3_4_5.equals(
                            CommonDtoUtil.objValToString(itemOutEntity.getNinteiFormF()))
                            || CommonConstants.KAI_TEI_FLAG_H21_4
                                    .equals(CommonDtoUtil.objValToString(itemOutEntity.getNinteiFormF()))) {
                        // 3.3. 「3.2.」で取得した居宅アセスメント履歴情報から認定Formfが4「H21/４改訂版」または5「R3/4改訂版」の履歴情報を取得する。
                        // 3.4. 「3.3.」で取得した居宅アセスメント履歴情報の一行目を履歴情報とする。
                        // アセスメントID */
                        historyInfo.setGdlId(CommonDtoUtil.objValToString(itemOutEntity.getGdlId()));
                        // 計画期間ID */
                        historyInfo.setSc1Id(CommonDtoUtil.objValToString(itemOutEntity.getSc1Id()));
                        // アセスメント実施日 */
                        historyInfo.setAsJisshiDateYmd(itemOutEntity.getAsJisshiDateYmd());
                        // 改定フラグ */
                        historyInfo.setNinteiFormF(CommonDtoUtil.objValToString(itemOutEntity.getNinteiFormF()));
                        // 記載者ID */
                        historyInfo.setShokuId(CommonDtoUtil.objValToString(itemOutEntity.getShokuId()));
                        break;
                    }
                }
                // 3.5. 「3.3.」で取得した居宅アセスメント履歴情報の件数を履歴情報.履歴総件数に設定する。
                // 履歴番号
                historyInfo.setKrirekiNo(CommonDtoUtil.objValToString(kyotakuRirekiList.size()));
                // 履歴総件数
                historyInfo.setKrirekiCnt(CommonDtoUtil.objValToString(kyotakuRirekiList.size()));

                // 作成者情報を取得する。
                String shokuinName = assessmentHomeLogic.findKghCpnRaiMonKentPrnPreSrw2(
                        CommonDtoUtil.strValToInt(historyInfo.getShokuId()));
                // 記載者名
                historyInfo.setShokuinName(shokuinName);

                // 4. 「3.4.」で履歴情報を取得できる場合、課題と目標一覧情報を取得する。
                // 4.1. 下記のＧＬ＿課題と目標情報取得のDAOを利用し、課題と目標取得情報を取得する。

                List<KadaiTourokuSc1OutEntity> kadaiTourokuSc1List = assessmentHomeLogic.findKadaiTourokuSc1(sc1Id,
                        CommonDtoUtil.strValToInt(historyInfo.getGdlId()));
                if (kadaiTourokuSc1List != null && kadaiTourokuSc1List.size() > 0) {
                    for (KadaiTourokuSc1OutEntity itemOutEntity : kadaiTourokuSc1List) {
                        Gui00794CommonInformationGdlKadai gdlKadai = new Gui00794CommonInformationGdlKadai();
                        BeanUtils.copyProperties(gdlKadai, itemOutEntity);
                        // アセスメント名称
                        String assName = assessmentHomeLogic.assNoToAssName(
                                (itemOutEntity.getAssNo() != null) ? itemOutEntity.getAssNo().intValue() : null);
                        gdlKadai.setAssName(assName);

                        gdlKadaiList.add(gdlKadai);
                    }
                }
            }

        }

        // 下記の続柄マスタ取得DAOを利用し、続柄マスタ情報リストを取得する。

        ComMucZokugaraInfoByCriteriaInEntity comMucZokugaraInfoInEntity = new ComMucZokugaraInfoByCriteriaInEntity();
        List<ComMucZokugaraInfoOutEntity> comMucZokugaraInfoOutEntityList = comMucZokugaraSelectMapper
                .findComMucZokugaraInfoByCriteria(comMucZokugaraInfoInEntity);

        if (!CollectionUtils.isNullOrEmpty(comMucZokugaraInfoOutEntityList)) {
            for (ComMucZokugaraInfoOutEntity outEntity : comMucZokugaraInfoOutEntityList) {
                Gui00794Zokugara gui00794Zokugara = new Gui00794Zokugara();
                // 続柄コード
                gui00794Zokugara.setZcode(CommonDtoUtil.objValToString(outEntity.getZcode()));
                // 続柄分類名
                gui00794Zokugara.setZokugaraKnj(outEntity.getZokugaraKnj());
                gui00794ZokugaraList.add(gui00794Zokugara);
            }
        }

        // 6. 下記の要介護状態マスタ（４－４）取得DAOを利用し、要介護状態情報リストを取得する。
        YokaigoKbnListByCriteriaInEntity yokaigoInfoInEntity = new YokaigoKbnListByCriteriaInEntity();
        List<YokaigoKbnListOutEntity> yokaigoInfoOutEntityList = comMscYokaigoSelectMapper
                .findYokaigoKbnListByCriteria(yokaigoInfoInEntity);

        if (!CollectionUtils.isNullOrEmpty(yokaigoInfoOutEntityList)) {
            for (YokaigoKbnListOutEntity outEntity : yokaigoInfoOutEntityList) {
                Gui00794Yokaigo gui00794Yokaigo = new Gui00794Yokaigo();
                // 要介護状態区分
                gui00794Yokaigo.setYokaiKbn(CommonDtoUtil.objValToString(outEntity.getYokaiKbn()));
                // 要介護度区分
                gui00794Yokaigo.setYokaiKnj(outEntity.getYokaiKnj());
                gui00794YokaigoList.add(gui00794Yokaigo);
            }
        }
        // 7. 下記の手帳等級マスタ（３－９）情報取得DAOを利用し、手帳等級情報リストを取得する。
        TechouToukyuuByCriteriaInEntity techouToukyuuInEntity = new TechouToukyuuByCriteriaInEntity();
        List<TechouToukyuuOutEntity> techouToukyuuOutEntityList = comMstToukyuuSelectMapper
                .findTechouToukyuuByCriteria(techouToukyuuInEntity);

        if (!CollectionUtils.isNullOrEmpty(techouToukyuuOutEntityList)) {
            for (TechouToukyuuOutEntity outEntity : techouToukyuuOutEntityList) {
                Gui00794Toukyuu gui00794Toukyuu = new Gui00794Toukyuu();
                // 等級コード
                gui00794Toukyuu.setToukyuuCd(CommonDtoUtil.objValToString(outEntity.getToukyuuCd()));
                // 等級名
                gui00794Toukyuu.setToukyuuKnj(outEntity.getToukyuuKnj());
                gui00794ToukyuuList.add(gui00794Toukyuu);

            }
        }

        // 8. 下記の障害の程度マスタ（３－１０）情報取得DAOを利用し、障害程度情報リストを取得する。
        ComMstTeidoByCriteriaInEntity comMstTeidoInEntity = new ComMstTeidoByCriteriaInEntity();
        List<ComMstTeidoOutEntity> comMstTeidoOutEntityList = comMstTeidoSelectMapper
                .findComMstTeidoByCriteria(comMstTeidoInEntity);

        if (!CollectionUtils.isNullOrEmpty(comMstTeidoOutEntityList)) {
            for (ComMstTeidoOutEntity outEntity : comMstTeidoOutEntityList) {
                Gui00794Teido gui00794Teido = new Gui00794Teido();
                // 障害の程度コード
                gui00794Teido.setTeidoCd(CommonDtoUtil.objValToString(outEntity.getTeidoCd()));
                // 障害の程度名
                gui00794Teido.setTeidoKnj(outEntity.getTeidoKnj());
                gui00794TeidoList.add(gui00794Teido);
            }
        }

        // 9. 下記の精神の等級マスタ（３－１１）情報取得DAOを利用し、精神等級情報リストを取得する。
        ComMstSeishinToukyuuByCriteriaInEntity comMstSeishinToukyuuInEntity = new ComMstSeishinToukyuuByCriteriaInEntity();
        List<ComMstSeishinToukyuuOutEntity> comMstSeishinToukyuuOutEntityList = comMstSeishinToukyuuSelectMapper
                .findComMstSeishinToukyuuByCriteria(comMstSeishinToukyuuInEntity);

        if (!CollectionUtils.isNullOrEmpty(comMstSeishinToukyuuOutEntityList)) {
            for (ComMstSeishinToukyuuOutEntity outEntity : comMstSeishinToukyuuOutEntityList) {
                Gui00794Seishin gui00794Seishin = new Gui00794Seishin();
                // 精神等級コード
                gui00794Seishin.setToukyuuCd(CommonDtoUtil.objValToString(outEntity.getToukyuuCd()));
                // 精神等級名
                gui00794Seishin.setToukyuuKnj(outEntity.getToukyuuKnj());
                gui00794SeishinList.add(gui00794Seishin);
            }

        }

        // 続柄マスタ情報リスト
        out.setZokugaraList(gui00794ZokugaraList);
        // 要介護状態情報リスト
        out.setYokaigoList(gui00794YokaigoList);
        // 身障手帳等級情報リスト
        out.setToukyuuList(gui00794ToukyuuList);
        // 療育手帳程度情報リスト
        out.setTeidoList(gui00794TeidoList);
        // 精神等級情報リスト
        out.setSeishinList(gui00794SeishinList);

        // 計画期間情報
        out.setPlanPeriodInfo(planPeriodInfo);
        // 履歴情報
        out.setHistoryInfo(historyInfo);
        // 課題と目標情報リスト
        out.setGdlKadaiList(gdlKadaiList);
        LOG.info(Constants.END);
        return out;
    }
}
