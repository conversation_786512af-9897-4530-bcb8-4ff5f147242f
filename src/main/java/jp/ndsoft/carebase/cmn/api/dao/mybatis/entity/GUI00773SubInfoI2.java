package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import jp.ndsoft.smh.framework.global.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GUI00773_アセスメント(インターライ)画面I-2
 * 
 * @description
 *              サブ情報（I-2）
 *              サブ情報（I-2）エンティティ
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GUI00773SubInfoI2 implements Serializable, IEntity {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** アセスメントID */
    private String raiId;

    /** i2_メモ */
    private String i2MemoKnj;

    /** i2_メモフォント */
    private String i2MemoFont;

    /** i2_メモ色 */
    private String i2MemoColor;

}
