package jp.ndsoft.carebase.cmn.api.logic;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import io.grpc.netty.shaded.io.netty.util.internal.StringUtil;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.dto.CmnUsrHok3gOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.CmnUsrNin1OutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.CmnUsrNin2OutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.CmnUsrNin3gOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GetUserInfoMonthOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KghCmnUsrInfo3gDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KyuufurituCmnOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.YokaiDaysOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.ComMscSvjigyoMapper;
import jp.ndsoft.carebase.common.dao.mybatis.ComMscSvjigyoNameMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMscSvjigyo;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMscSvjigyoCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMscSvjigyoName;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMscSvjigyoNameCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnGetItemcodeFromScode21SonzaiTankaByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnGetItemcodeFromScode21SonzaiTankaOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnGetItemcodeFromScode21TankaGaNaiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnGetItemcodeFromScode21TankaGaNaiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemSonzaiTankaByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemSonzaiTankaOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemTankaGaNaiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemTankaGaNaiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemuseMaxByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemuseMaxOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemuseScodeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemuseScodeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemuseSougouFormalnameKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemuseSougouFormalnameKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemuseSougouMaxByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemuseSougouMaxOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemuseSougouScodeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcItemuseSougouScodeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcTermSvtype35TermidByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcTermSvtype35TermidOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcTermTermidByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcTermTermidOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMscSvjigyoNameFirstSvKindCdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.FygMstShouhinShouhinKnjByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.FygMstShouhinShouhinKnjOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ItemNameFirstByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ItemNameFirstOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ItemcodeAndScodeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ItemcodeAndScodeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyoCdNumberByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyoCdNumberOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyoNumberRirekiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyoNumberRirekiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyosyaRyakuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.JigyosyaRyakuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnGetUserinfoMonthKaigo3gByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnGetUserinfoMonthKaigo3gOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnGetUserinfoMonthKaigoS2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnGetUserinfoMonthKaigoS2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnGetUserinfoMonthKaigoS3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnGetUserinfoMonthKaigoS3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnGetUserinfoMonthKaigoTantoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnGetUserinfoMonthKaigoTantoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKsgGetSvtypeFromSvJigyoIdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKsgGetSvtypeFromSvJigyoIdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoushaShimeiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoushaShimeiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvJigyoNameByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvJigyoNameOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvKindCdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvKindCdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvTelByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvTelOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvcodeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvcodeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TelNoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TelNoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TermIdYukoKikanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TermIdYukoKikanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSougouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcTermSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcTermTermidSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoNameFirstSvKindCdSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoRirekiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.FygMstShouhinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.GaibuItakuItemSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.JigyosyoIDSvSyuruiCodeSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KaigoHokenJyouhouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KihonKaigoHokenJyouhouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RiyoushaGendoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ShuruiBetsuShikyuuGendoGakuSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.TermIdSelectMapper;

/**
 * KghCmn03gFunc01Logicロジッククラス
 * 
 * <AUTHOR>
 */
@Component
public class KghCmn03gFunc01Logic {

    /** サービス種類CD: デフォルト値 */
    private static final String DEFAULT_SERVICE_KIND_CD = "00";
    /** サービス種類CD: 総合事業A1 */
    private static final String SERVICE_KIND_CD_A1 = "A1";
    /** サービス種類CD: 総合事業A5 */
    private static final String SERVICE_KIND_CD_A5 = "A5";
    /** サービス種類CD: 総合事業A9 */
    private static final String SERVICE_KIND_CD_A9 = "A9";

    /** 期間ID: 2003/03以前 */
    private static final Integer TERM_ID_1 = 1;
    /** 期間ID: 2005/09以前 */
    private static final Integer TERM_ID_2 = 2;
    /** 期間ID: 2006/03以前 */
    private static final Integer TERM_ID_3 = 3;
    /** 期間ID: 2009/03以前 */
    private static final Integer TERM_ID_4 = 4;
    /** 期間ID: 2012/03以前 */
    private static final Integer TERM_ID_5 = 5;
    /** 期間ID: 2014/03以前 */
    private static final Integer TERM_ID_6 = 6;
    /** 期間ID: 2015/03以前 */
    private static final Integer TERM_ID_7 = 7;
    /** 期間ID: 2017/03以前 */
    private static final Integer TERM_ID_8 = 8;
    /** 期間ID: 2018/03以前 */
    private static final Integer TERM_ID_9 = 9;
    /** 期間ID: 2019/09以前 */
    private static final Integer TERM_ID_10 = 10;
    /** 期間ID: 2021/03以前 */
    private static final Integer TERM_ID_11 = 11;
    /** 期間ID: 2022/09以前 */
    private static final Integer TERM_ID_12 = 12;
    /** 期間ID: 2024/03以前 */
    private static final Integer TERM_ID_13 = 13;
    /** 期間ID: 2024/05以前 */
    private static final Integer TERM_ID_14 = 14;
    /** 期間ID: 2025/03以前 */
    private static final Integer TERM_ID_15 = 15;
    /** 期間ID: それ以降 */
    private static final Integer TERM_ID_16 = 16;
    /** 境界値: 2003/03 */
    private static final String BOUNDARY_2003_03 = "2003/03";
    /** 境界値: 2005/09 */
    private static final String BOUNDARY_2005_09 = "2005/09";
    /** 境界値: 2006/03 */
    private static final String BOUNDARY_2006_03 = "2006/03";
    /** 境界値: 2009/03 */
    private static final String BOUNDARY_2009_03 = "2009/03";
    /** 境界値: 2012/03 */
    private static final String BOUNDARY_2012_03 = "2012/03";
    /** 境界値: 2014/03 */
    private static final String BOUNDARY_2014_03 = "2014/03";
    /** 境界値: 2015/03 */
    private static final String BOUNDARY_2015_03 = "2015/03";
    /** 境界値: 2017/03 */
    private static final String BOUNDARY_2017_03 = "2017/03";
    /** 境界値: 2018/03 */
    private static final String BOUNDARY_2018_03 = "2018/03";
    /** 境界値: 2019/09 */
    private static final String BOUNDARY_2019_09 = "2019/09";
    /** 境界値: 2021/03 */
    private static final String BOUNDARY_2021_03 = "2021/03";
    /** 境界値: 2022/09 */
    private static final String BOUNDARY_2022_09 = "2022/09";
    /** 境界値: 2024/03 */
    private static final String BOUNDARY_2024_03 = "2024/03";
    /** 境界値: 2024/05 */
    private static final String BOUNDARY_2024_05 = "2024/05";
    /** 境界値: 2025/03 */
    private static final String BOUNDARY_2025_03 = "2025/03";
    /** 境界値: 2018/04/01 */
    private static final String BOUNDARY_2018_04_01 = "2018/04/01";
    /** 境界値: 2021/04/01 */
    private static final String BOUNDARY_2021_04_01 = "2021/04/01";
    /** 日付フォーマット (YYYY/MM/DD) */
    private static final String DATE_FORMAT = "%s/%02d";
    /** 失敗コード */
    private static final int FAILURE_CODE = -1;
    /** 月の最大日数 */
    private static final int MAX_MONTH_DAYS = 31;
    /** 月初日のフォーマット */
    private static final String FIRST_DAY_OF_MONTH_FORMAT = "%s/01";
    /** 介護サービス費適用正式名称取得 */
    @Autowired
    private ComMhcItemuseSelectMapper comMhcItemuseSelectMapper;
    /** 20-20 介護予防・日常生活支援総合事業サービス費適用マスタ情報取得 */
    @Autowired
    private ComMhcItemuseSougouSelectMapper comMhcItemuseSougouSelectMapper;
    /** サービス事業者マスタ情報取得 */
    @Autowired
    private ComMscSvjigyoMapper comMscSvjigyoMapper;
    /** サービス事業者コードマスタ取得 */
    @Autowired
    private ComMscSvjigyoNameMapper comMscSvjigyoNameMapper;
    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;
    /** KghShrKsgFunc02Logicロジッククラス */
    @Autowired
    private KghShrKsgFunc02Logic kghShrKsgFunc02Logic;
    /** */
    @Autowired
    private KghCmnF01Logic kghCmnF01Logic;

    /** 介護保険情報取得 */
    @Autowired
    private KaigoHokenJyouhouSelectMapper kaigoHokenJyouhouSelectMapper;
    /** 事業所のサービス種類CDを取得 */
    @Autowired
    private GaibuItakuItemSelectMapper gaibuItakuItemSelectMapper;
    /**  */
    @Autowired
    private FygMstShouhinSelectMapper fygMstShouhinSelectMapper;
    /**  */
    @Autowired
    private ComMscSvjigyoNameFirstSvKindCdSelectMapper comMscSvjigyoNameFirstSvKindCdSelectMapper;
    /**  */
    @Autowired
    private ComMhcTermSelectMapper comMhcTermSelectMapper;
    /** */
    @Autowired
    private TermIdSelectMapper termIdSelectMapper;
    /** */
    @Autowired
    private JigyosyoIDSvSyuruiCodeSelectMapper jigyosyoIDSvSyuruiCodeSelectMapper;
    /** */
    @Autowired
    private ComMhcTermTermidSelectMapper comMhcTermTermidSelectMapper;
    /** */
    @Autowired
    private KihonKaigoHokenJyouhouSelectMapper kihonKaigoHokenJyouhouSelectMapper;
    /** */
    @Autowired
    private ShuruiBetsuShikyuuGendoGakuSelectMapper shuruiBetsuShikyuuGendoGakuSelectMapper;
    /** */
    @Autowired
    private RiyoushaGendoSelectMapper riyoushaGendoSelectMapper;
    /** */
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;
    /** */
    @Autowired
    private ComMscSvjigyoRirekiSelectMapper comMscSvjigyoRirekiSelectMapper;
    /** */
    @Autowired
    private ComMscSvjigyoSelectMapper comMscSvjigyoSelectMapper;
    /** 介護サービス費マスタ（20-3）項目コードを取得 */
    @Autowired
    private ComMhcItemSelectMapper comMhcItemSelectMapper;

    /**
     * 介護ｻｰﾋﾞｽ費適用ﾏｽﾀからｻｰﾋﾞｽｺｰﾄﾞを取得 f_kgh_cmn_get_scode_from_itemuse
     * 
     * @param alSvj  サービス事業者ID
     * @param alItm  itemcode
     * @param asDate date
     * @return サービスコード
     */
    public String getScodeFromItemuse(Integer alSvj, Integer alItm, String asDate) {
        String lss = "";
        Integer liTermid = 0;

        // サービス種別IDを取得
        ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity in = new ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity();
        in.setAlSvj(alSvj);
        List<ComMscSvjigyoNameFirstSvKindCdOutEntity> items = comMscSvjigyoNameFirstSvKindCdSelectMapper
                .findComMscSvjigyoNameFirstSvKindCdByCriteria(in);
        String lsSvtype = items.stream().filter(i -> i != null).findFirst()
                .map(ComMscSvjigyoNameFirstSvKindCdOutEntity::getSvKindCd).orElse("00");

        // サービス有効期間IDを取得
        if (!(lsSvtype.compareTo("81") >= 0 && lsSvtype.compareTo("99") <= 0)) {
            if ((asDate.compareTo("2018/04/01") >= 0 && (lsSvtype.equals("61") || lsSvtype.equals("65")))
                    || (asDate.compareTo("2021/04/01") >= 0 && (lsSvtype.equals("A1") || lsSvtype.equals("A5")))) {

                ComMhcTermSvtype35TermidByCriteriaInEntity in35 = new ComMhcTermSvtype35TermidByCriteriaInEntity();
                in35.setDateYmd(asDate);
                List<ComMhcTermSvtype35TermidOutEntity> items35 = comMhcTermSelectMapper
                        .findComMhcTermSvtype35TermidByCriteria(in35);
                liTermid = Integer.valueOf(
                        items35.stream().findFirst().map(ComMhcTermSvtype35TermidOutEntity::getTermid).orElse(0));
            } else {
                ComMhcTermTermidByCriteriaInEntity inTerm = new ComMhcTermTermidByCriteriaInEntity();
                inTerm.setAlSvj(alSvj);
                inTerm.setAsDate(asDate);
                List<ComMhcTermTermidOutEntity> itemsTerm = comMhcTermTermidSelectMapper
                        .findComMhcTermTermidByCriteria(inTerm);
                liTermid = Integer
                        .valueOf(itemsTerm.stream().findFirst().map(ComMhcTermTermidOutEntity::getTermid).orElse(0));
            }
        }

        // H27改正対応：総合事業とそれ以外のサービスで処理を分岐
        switch (lsSvtype) {
            case "A1", "A5", "A9":
                // 総合事業
                ComMhcItemuseSougouScodeByCriteriaInEntity inScode = new ComMhcItemuseSougouScodeByCriteriaInEntity();
                inScode.setAlItm(CommonDtoUtil.objValToString(alItm));
                inScode.setAiTerm(CommonDtoUtil.objValToString(liTermid));
                List<ComMhcItemuseSougouScodeOutEntity> itemsScode = comMhcItemuseSougouSelectMapper
                        .findComMhcItemuseSougouScodeByCriteria(inScode);
                lss = itemsScode.stream().findFirst().map(ComMhcItemuseSougouScodeOutEntity::getScode).orElse("");
                break;

            default:
                // 介護・予防サービス・横だしサービス
                SvcodeByCriteriaInEntity inSvcode = new SvcodeByCriteriaInEntity();
                inSvcode.setLlDmyJid(CommonDtoUtil.objValToString(alSvj));
                inSvcode.setAlItemcode(CommonDtoUtil.objValToString(alItm));
                inSvcode.setAlTermid(CommonDtoUtil.objValToString(liTermid));
                List<SvcodeOutEntity> itemsSvcode = comMhcItemuseSelectMapper.findSvcodeByCriteria(inSvcode);
                if (itemsSvcode == null || itemsSvcode.size() == 0) {
                    inSvcode.setLlDmyJid("0");
                    itemsSvcode = comMhcItemuseSelectMapper.findSvcodeByCriteria(inSvcode);
                }

                lss = itemsSvcode.stream().findFirst().map(SvcodeOutEntity::getSvcode).orElse("");
                break;
        }

        return StringUtils.defaultIfEmpty(lss, "");
    }

    /**
     * 指定年月での利用者の介護保険情報を取得する(3G) f_kgh_cmn_get_userinfo_month
     * 
     * @param alUser  利用者ID
     * @param asYymm  提供年月(yyyy/mm)
     * @param alShien 自事業者ID
     * @return 情報格納構造体
     */
    public GetUserInfoMonthOutDto getUserinfoMonth(Integer alUser, String asYymm, Integer alShien) {
        GetUserInfoMonthOutDto result = new GetUserInfoMonthOutDto();
        result.setRet(-1);
        // 入力パラメータチェック
        if (StringUtils.length(asYymm) == 7 && StringUtils.indexOf(asYymm, "/") == 4 && alUser > 0 && alShien > 0) {
            // パラメータOK
        } else {
            return result;
        }

        KghCmnUsrInfo3gDto pstrU = new KghCmnUsrInfo3gDto();
        // 変数定義
        // ldsHk = new DataStore("ds_kgh_cmn_get_userinfo_month_kaigo_3g");
        // kaigoHokenJyouhouSelectMapper.findKghCmnGetUserinfoMonthKaigo3gByCriteria
        // ldsS1 = new DataStore("ds_kgh_com_get_kyuufuritu");
        // ldsS2 = new DataStore("ds_kgh_cmn_get_userinfo_month_kaigo_s2");
        // ShuruiBetsuShikyuuGendoGakuSelectMapper.findKghCmnGetUserinfoMonthKaigoS2ByCriteria
        // ldsS3 = new
        // DataStore("ds_kgh_cmn_get_userinfo_month_kaigo_s3");RiyoushaGendoSelectMapper.findKghCmnGetUserinfoMonthKaigoS3ByCriteria
        // ldsS4 = new DataStore("ds_kgh_cmn_get_userinfo_month_kaigo_tanto");
        // KihonKaigoHokenJyouhouSelectMapper.findKghCmnGetUserinfoMonthKaigoTantoByCriteria

        // 規定の月初日・月末日を得る
        String lsYm1 = asYymm + "/01";
        String lsYm2 = nds3GkFunc01Logic.getTukimatu(lsYm1);

        // 検索実行
        KghCmnGetUserinfoMonthKaigo3gByCriteriaInEntity in3g = new KghCmnGetUserinfoMonthKaigo3gByCriteriaInEntity();
        in3g.setAlUser(alUser);
        in3g.setAsDate(lsYm1);
        in3g.setAsEndd(lsYm2);
        List<KghCmnGetUserinfoMonthKaigo3gOutEntity> ldsHks = kaigoHokenJyouhouSelectMapper
                .findKghCmnGetUserinfoMonthKaigo3gByCriteria(in3g);
        Integer llMhk = ldsHks.size();

        if (llMhk > 0) {
            String lsHih0 = "";
            String lsHkn0 = "";
            Integer llSeq0 = 0;
            Integer llEda0 = 0;

            KghCmnGetUserinfoMonthKaigo3gOutEntity ldsHk1 = ldsHks.get(0);
            // 基本情報設定
            pstrU.setUserid(alUser);
            pstrU.setSelfId(ldsHk1.getSelfId());
            pstrU.setSelfNo("");
            pstrU.setSex(ldsHk1.getSex());
            pstrU.setTel(ldsHk1.getTel());
            pstrU.setBirthdayYmd(ldsHk1.getBirthdayYmd());

            // 利用者名（カナ）設定
            String nameKana = StringUtils.trimToEmpty(ldsHk1.getName1Kana()) + " "
                    + StringUtils.trimToEmpty(ldsHk1.getName2Kana());
            pstrU.setUsernameKana(nameKana.trim());
            // 利用者名設定
            String nameKnj = StringUtils.trimToEmpty(ldsHk1.getName1Knj()) + " "
                    + StringUtils.trimToEmpty(ldsHk1.getName2Knj());
            pstrU.setUsernameKnj(nameKnj.trim());

            // 担当者情報取得
            KghCmnGetUserinfoMonthKaigoTantoByCriteriaInEntity inTanto = new KghCmnGetUserinfoMonthKaigoTantoByCriteriaInEntity();
            inTanto.setAlUser(alUser);
            inTanto.setAlShien(alShien);
            inTanto.setAsYms(lsYm1);
            inTanto.setAsYme(lsYm2);
            List<KghCmnGetUserinfoMonthKaigoTantoOutEntity> ldsS4s = kihonKaigoHokenJyouhouSelectMapper
                    .findKghCmnGetUserinfoMonthKaigoTantoByCriteria(inTanto);
            if (ldsS4s.size() > 0) {
                KghCmnGetUserinfoMonthKaigoTantoOutEntity ldsS4 = ldsS4s.get(0);
                pstrU.setTantoId(ldsS4.getTantoId());
                pstrU.setTantoNameKnj(ldsS4.getShokuin1Knj() + " " + ldsS4.getShokuin2Knj());
                pstrU.setSenmonNo(ldsS4.getSenmonNo());
            }

            // 介護保険・要介護度認定情報
            CmnUsrHok3gOutDto hoken = null;
            for (KghCmnGetUserinfoMonthKaigo3gOutEntity ldsHk : ldsHks) {
                // チェック用の値を取得
                String lsHih1 = ldsHk.getHHokenNo();
                String lsHkn1 = ldsHk.getKHokenNo();
                Integer llSeq1 = ldsHk.getSeqNo();
                Integer llEda1 = ldsHk.getEdaNo();
                // 月途中保険者変更時に前半の値が残るのでリセット
                // ldsS1.reset();

                if (!StringUtils.equals(lsHih0, lsHih1) || !StringUtils.equals(lsHkn0, lsHkn1)) {
                    // 介護保険情報が異なる：被保険者番号違い→格納／保険者番号違いもチェック
                    lsHih0 = lsHih1;
                    lsHkn0 = lsHkn1;
                    llSeq0 = llSeq1;
                    llEda0 = llEda1;

                    // 介護保険情報
                    hoken = new CmnUsrHok3gOutDto();
                    if (CollectionUtils.isEmpty(pstrU.getHoken())) {
                        List<CmnUsrHok3gOutDto> hokenList = new ArrayList<>();
                        pstrU.setHoken(hokenList);
                    }
                    pstrU.getHoken().add(hoken);
                    pstrU.setHokenMax(pstrU.getHoken().size());
                    hoken.setHiHokenNo(lsHih1); // 被保険者番号
                    hoken.setKHokenNo(lsHkn1); // 保険者番号
                    hoken.setKHokenCd(ldsHk.getKHokenCd()); // 保険者cd
                    hoken.setKHokenKnj(ldsHk.getKHokenKnj()); // 保険者名
                    hoken.setSeqNo(llSeq1); // 保険連番
                    hoken.setStartYmd(ldsHk.getStartYmd()); // 有効期間開始日
                    hoken.setEndYmd(ldsHk.getEndYmd()); // 有効期間終了日
                    hoken.setKouhuYmd(ldsHk.getKouhuYmd()); // 交付年月日
                    hoken.setKyufuwari(ldsHk.getKyufuwari()); // 給付率
                    // 保険の指定月での有効期間を得る
                    String lsHst = hoken.getStartYmd();
                    if (lsHst.compareTo(lsYm1) < 0) {
                        lsHst = lsYm1;
                    } else if (pstrU.getHoken().size() == 1) {
                        // 月初日＜保険開始日だが、１行目の場合、月初日に置換する
                        lsHst = lsYm1;
                    }

                    String lsHed = hoken.getEndYmd();
                    if (lsHed == null || StringUtils.isEmpty(lsHed)) {
                        lsHed = lsYm2; // 不具合対策
                    }
                    if (lsHed.compareTo(lsYm2) > 0) {
                        lsHed = lsYm2;
                    }

                    hoken.setKiStYmd(lsHst); // 指定月の有効開始日
                    hoken.setKiEdYmd(lsHed); // 指定月の有効終了日

                    // 2割負担対応⇒共通関数を使用するように変更
                    if (StringUtils.left(hoken.getHiHokenNo(), 1).equals("H")) {
                        // 生保単独の場合は保険情報の給付率を優先
                    } else {
                        // 認定期間を参照する関数に置き換え
                        // ln_kyufu.uf_com_get_kyuufuritu_cmn( ll_user, ls_hst, ls_hed, lds_s1 )
                        List<KyuufurituCmnOutDto> ldsS1s = kghShrKsgFunc02Logic.getKyuufurituCmn(Arrays.asList(alUser),
                                lsHst, lsHed);

                        int liKyw = -99;
                        // 給付制限情報の確認
                        if (ldsS1s != null) {
                            for (KyuufurituCmnOutDto ldsS1 : ldsS1s) {
                                int liTmp = ldsS1.getKyufuwari();

                                if (liKyw < liTmp) {
                                    liKyw = liTmp;
                                }
                            }
                        }

                        // 有効な値があった場合のみ変更
                        if (liKyw != -99) {
                            hoken.setKyufuwari(liKyw); // 給付率（New）
                        }
                    }

                    // 担当ケアマネ
                    String lsT04 = "9999/99/99";
                    if (lsHed != null) {
                        lsT04 = lsHed;
                    }
                    {
                        inTanto.setAlUser(alUser);
                        inTanto.setAlShien(alShien);
                        inTanto.setAsYms(lsHst);
                        inTanto.setAsYme(lsT04);
                        List<KghCmnGetUserinfoMonthKaigoTantoOutEntity> ldsS4s4 = kihonKaigoHokenJyouhouSelectMapper
                                .findKghCmnGetUserinfoMonthKaigoTantoByCriteria(inTanto);

                        if (ldsS4s4 != null && ldsS4s4.size() > 0) {
                            KghCmnGetUserinfoMonthKaigoTantoOutEntity ldsS4 = ldsS4s4.get(0);
                            hoken.setTantoId(ldsS4.getTantoId());
                            hoken.setTantoNameKnj(ldsS4.getShokuin1Knj() + " " + ldsS4.getShokuin2Knj()); // shokuin1_knj
                                                                                                          // + ' ' +
                                                                                                          // shokuin2_knj
                            hoken.setSenmonNo(ldsS4.getSenmonNo());
                        }
                    }

                    // 要介護度認定
                    hoken.setNinteiMax(1);
                    hoken.setNintei(new ArrayList<>());
                    CmnUsrNin3gOutDto nintei = new CmnUsrNin3gOutDto();
                    hoken.getNintei().add(nintei);

                    nintei.setEdaNo(llEda1);
                    nintei.setShinseiflg(ldsHk.getShinseiflg());
                    nintei.setYokaiKbn(ldsHk.getYokaiKbn());

                    nintei.setNinteiYmd(ldsHk.getNinteiYmd());
                    nintei.setStartYmd(ldsHk.getNinteiStartYmd());
                    nintei.setEndYmd(ldsHk.getNinteiEndYmd());

                    nintei.setKbnStartYmd(ldsHk.getTuusyoStartYmd());
                    nintei.setKbnEndYmd(ldsHk.getTuusyoEndYmd());
                    nintei.setKbn2StartYmd(ldsHk.getShortStartYmd());
                    nintei.setKbn2EndYmd(ldsHk.getShortEndYmd());

                    nintei.setGendo(ldsHk.getTusho1Gendo());
                    nintei.setGendo12(ldsHk.getTusho2Gendo());
                    nintei.setGendo21(ldsHk.getNyusho1Gendo());
                    nintei.setGendo22(ldsHk.getNyusho2Gendo());

                    // 届出日
                    Integer llTmp = ldsHk.getSvJigyoId();
                    if (llTmp.equals(alShien)) {
                        nintei.setTodokedeMax(1);
                        nintei.setTodokede(new ArrayList<>());
                        CmnUsrNin1OutDto todokede = new CmnUsrNin1OutDto();
                        nintei.getTodokede().add(todokede);

                        todokede.setShienId(llTmp);
                        todokede.setTodokedeYmd(ldsHk.getTodokedeYmd());
                    }

                    // 種類別支給限度額
                    nintei.setShuruiMax(0);
                    nintei.setShurui(new ArrayList<>());
                    KghCmnGetUserinfoMonthKaigoS2ByCriteriaInEntity inldsS2 = new KghCmnGetUserinfoMonthKaigoS2ByCriteriaInEntity();
                    inldsS2.setAlUsr(alUser);
                    inldsS2.setAlSeq(llSeq0);
                    inldsS2.setAlEda(llEda0);
                    List<KghCmnGetUserinfoMonthKaigoS2OutEntity> ldsS2s = shuruiBetsuShikyuuGendoGakuSelectMapper
                            .findKghCmnGetUserinfoMonthKaigoS2ByCriteria(inldsS2);
                    // llMs2 = ldsS2.retrieve(alUser, llSeq1, llEda1);
                    for (KghCmnGetUserinfoMonthKaigoS2OutEntity ldsS2 : ldsS2s) {
                        CmnUsrNin2OutDto shurui = new CmnUsrNin2OutDto();
                        nintei.getShurui().add(shurui);
                        shurui.setSvtype(ldsS2.getSvKindCd());
                        shurui.setGendo(ldsS2.getGendo());
                    }
                    nintei.setShuruiMax(nintei.getShurui().size());

                    // サービス限定
                    nintei.setSvGentei(ldsHk.getSvGentei());
                    if (nintei.getSvGentei() == null) {
                        // 限定無し
                        nintei.setSvGentei(0);
                    }

                    if (nintei.getSvGentei() == 0) {
                        // 限定無し
                        nintei.setSvGenteiMax(0);
                        nintei.setGentei(new ArrayList<>());
                    } else {
                        // 限定あり
                        nintei.setSvGenteiMax(0);
                        nintei.setGentei(new ArrayList<>());

                        KghCmnGetUserinfoMonthKaigoS3ByCriteriaInEntity inldsS3 = new KghCmnGetUserinfoMonthKaigoS3ByCriteriaInEntity();
                        inldsS3.setAlUsr(alUser);
                        inldsS3.setAlSeq(llSeq1);
                        inldsS3.setAlEda(llEda1);
                        List<KghCmnGetUserinfoMonthKaigoS3OutEntity> ldsS3s = riyoushaGendoSelectMapper
                                .findKghCmnGetUserinfoMonthKaigoS3ByCriteria(inldsS3);
                        // llMs3 = ldsS3.retrieve(alUser, llSeq1, llEda1);
                        for (KghCmnGetUserinfoMonthKaigoS3OutEntity ldsS3 : ldsS3s) {
                            CmnUsrNin2OutDto gentei = new CmnUsrNin2OutDto();
                            nintei.getGentei().add(gentei);
                            gentei.setSvtype(ldsS3.getSvKindCd());
                        }
                        nintei.setSvGenteiMax(nintei.getGentei().size());
                    }

                } else if (lsHih0.equals(lsHih1) && llEda0 != llEda1 && hoken != null) {
                    CmnUsrNin3gOutDto nintei = new CmnUsrNin3gOutDto();
                    if (hoken.getNintei() == null) {
                        hoken.setNintei(new ArrayList<>());
                    }
                    hoken.getNintei().add(nintei);
                    hoken.setNinteiMax(hoken.getNintei().size());

                    // 要介護度認定情報が異なる→取得
                    nintei.setEdaNo(llEda1);
                    nintei.setShinseiflg(ldsHk.getShinseiflg());
                    nintei.setYokaiKbn(ldsHk.getYokaiKbn());

                    nintei.setNinteiYmd(ldsHk.getNinteiYmd());
                    nintei.setStartYmd(ldsHk.getNinteiStartYmd());
                    nintei.setEndYmd(ldsHk.getNinteiEndYmd());

                    nintei.setKbnStartYmd(ldsHk.getTuusyoStartYmd());
                    nintei.setKbnEndYmd(ldsHk.getTuusyoEndYmd());
                    nintei.setKbn2StartYmd(ldsHk.getShortStartYmd());
                    nintei.setKbn2EndYmd(ldsHk.getShortEndYmd());

                    nintei.setGendo(ldsHk.getTusho1Gendo());
                    nintei.setGendo12(ldsHk.getTusho2Gendo());
                    nintei.setGendo21(ldsHk.getNyusho1Gendo());
                    nintei.setGendo22(ldsHk.getNyusho2Gendo());

                    // 届出日
                    Integer llTmp = ldsHk.getSvJigyoId();
                    if (llTmp.equals(alShien)) {
                        nintei.setTodokedeMax(1);
                        nintei.setTodokede(new ArrayList<>());
                        CmnUsrNin1OutDto todokede = new CmnUsrNin1OutDto();
                        nintei.getTodokede().add(todokede);
                        todokede.setShienId(llTmp);
                        todokede.setTodokedeYmd(ldsHk.getTodokedeYmd());
                    }

                    // サービス種類別支給限度額の処理
                    nintei.setShuruiMax(0);
                    nintei.setShurui(new ArrayList<>());
                    KghCmnGetUserinfoMonthKaigoS2ByCriteriaInEntity inldsS2 = new KghCmnGetUserinfoMonthKaigoS2ByCriteriaInEntity();
                    inldsS2.setAlUsr(alUser);
                    inldsS2.setAlSeq(llSeq1);
                    inldsS2.setAlEda(llEda1);
                    List<KghCmnGetUserinfoMonthKaigoS2OutEntity> ldsS2s = shuruiBetsuShikyuuGendoGakuSelectMapper
                            .findKghCmnGetUserinfoMonthKaigoS2ByCriteria(inldsS2);
                    // long llMs2 = ldsS2.retrieve(alUser, llSeq1, llEda1);
                    for (KghCmnGetUserinfoMonthKaigoS2OutEntity ldsS2 : ldsS2s) {
                        CmnUsrNin2OutDto shurui = new CmnUsrNin2OutDto();
                        nintei.getShurui().add(shurui);
                        shurui.setSvtype(ldsS2.getSvKindCd());
                        shurui.setGendo(ldsS2.getGendo());
                    }
                    nintei.setShuruiMax(nintei.getShurui().size());

                    // サービス限定の処理
                    nintei.setSvGentei(ldsHk.getSvGentei());
                    if (nintei.getSvGentei() == null) {
                        // 限定無し
                        nintei.setSvGentei(0);
                    }

                    if (nintei.getSvGentei() == 0) {
                        // 限定無し
                        nintei.setSvGenteiMax(0);
                        nintei.setGentei(new ArrayList<>());
                    } else {
                        // 限定あり
                        nintei.setSvGenteiMax(0);
                        nintei.setGentei(new ArrayList<>());

                        KghCmnGetUserinfoMonthKaigoS3ByCriteriaInEntity inldsS3 = new KghCmnGetUserinfoMonthKaigoS3ByCriteriaInEntity();
                        inldsS3.setAlUsr(alUser);
                        inldsS3.setAlSeq(llSeq1);
                        inldsS3.setAlEda(llEda1);
                        List<KghCmnGetUserinfoMonthKaigoS3OutEntity> ldsS3s = riyoushaGendoSelectMapper
                                .findKghCmnGetUserinfoMonthKaigoS3ByCriteria(inldsS3);
                        // llMs3 = ldsS3.retrieve(alUser, llSeq1, llEda1);
                        for (KghCmnGetUserinfoMonthKaigoS3OutEntity ldsS3 : ldsS3s) {
                            CmnUsrNin2OutDto gentei = new CmnUsrNin2OutDto();
                            nintei.getGentei().add(gentei);
                            gentei.setSvtype(ldsS3.getSvKindCd());
                        }
                        nintei.setSvGenteiMax(nintei.getGentei().size());
                    }

                } else if (lsHih0.equals(lsHih1) && llEda0.equals(llEda1) && hoken != null) {
                    // 届出日が異なる→格納
                    Integer llTmp = ldsHk.getSvJigyoId();
                    if (llTmp.equals(alShien)) {
                        if (hoken.getNintei() != null && hoken.getNinteiMax() > 0) {
                            CmnUsrNin3gOutDto nintei = hoken.getNintei().get(hoken.getNinteiMax() - 1);
                            if (nintei.getTodokede() == null) {
                                nintei.setTodokede(new ArrayList<>());
                            }
                            CmnUsrNin1OutDto todokede = new CmnUsrNin1OutDto();
                            nintei.getTodokede().add(todokede);
                            todokede.setShienId(llTmp);
                            todokede.setTodokedeYmd(ldsHk.getTodokedeYmd());
                        }
                    }
                }
            }

            // 保険の有効期間のチェック
            if (pstrU.getHokenMax() > 0) {
                hoken = pstrU.getHoken().getLast();
                if (hoken.getKiEdYmd().compareTo(lsYm2) < 0) {
                    hoken.setKiEdYmd(lsYm2);
                }
            }
            result.setRet(1);

        } else {
            // 保険情報が取得できない場合の基本情報のみ取得
            pstrU.setUserid(alUser);
            pstrU.setSelfId(kghCmnF01Logic.getUserSelfId(alUser)); // f_cmn_get_user_self_id
            pstrU.setUsernameKana(kghCmnF01Logic.getUserNameKana(alUser)); // f_cmn_get_user_name_kana
            pstrU.setUsernameKnj(kghCmnF01Logic.getUserName(alUser)); // f_cmp_get_user_name

            // 基本情報取得
            RiyoushaShimeiByCriteriaInEntity inUsr = new RiyoushaShimeiByCriteriaInEntity();
            inUsr.setId(alUser);
            List<RiyoushaShimeiOutEntity> itemUsr = comTucUserSelectMapper.findRiyoushaShimeiByCriteria(inUsr);
            if (itemUsr != null && itemUsr.size() > 0) {
                RiyoushaShimeiOutEntity ldsHk = itemUsr.get(0);
                pstrU.setSelfNo("");
                pstrU.setSex(ldsHk.getSex());
                pstrU.setTel(ldsHk.getTel());
                pstrU.setBirthdayYmd(ldsHk.getBirthdayYmd());
            }
            result.setRet(0);

        }
        result.setUsrInfo(pstrU);

        return result;
    }

    /**
     * 短期入所の送迎加算かどうかを判定
     *
     * @param sCode サービスコード
     * @param ymd   判定基準日
     * @return true：短期入所の送迎加算である／false：違う
     */
    public boolean isTankiSougeiKasan(String sCode, String ymd) {
        // パラメータチェック
        if (StringUtils.isEmpty(sCode)) {
            return false;
        }

        // サービス種類とコード取得
        String serviceType = StringUtils.left(sCode, 2);
        String fullCode = StringUtils.left(sCode, 6);

        // 短期入所サービスの判定（予防含む）
        boolean isTargetService = (serviceType.compareTo("21") >= 0 && serviceType.compareTo("26") <= 0)
                || serviceType.equals("2A") || serviceType.equals("2B");

        if (!isTargetService) {
            return false;
        }

        // サービス種類別の判定
        switch (serviceType) {
            case "21": // 短期生活
            case "24": // 予防短期生活
                return Arrays.asList("219200", "249200").contains(fullCode);

            case "22": // 短期老健
            case "25": // 予防短期老健
                return Arrays.asList("221920", "251920").contains(fullCode);

            case "23": // 短期医療
            case "26": // 予防短期医療
                return Arrays.asList("232920", "233920", "234920", "235920", "262920", "263920", "264920", "265920")
                        .contains(fullCode);

            case "2A": // 短期医療院
            case "2B": // 予防短期医療院
                return Arrays.asList("2A6920", "2B6920").contains(fullCode);

            default:
                return false;
        }
    }

    /**
     * サービス項目を取得
     *
     * @param svjId  サービス事業者ID
     * @param itemId サービス内部id
     * @param date   日付
     * @param fygId  貸与の商品id
     * @return 項目の正式名称
     */
    public String getItemFormalname2(Integer svjId, Integer itemId, String date, Integer fygId) {
        String formalName = "";

        // 1. 貸与商品名の取得
        if (fygId > 0) {
            FygMstShouhinShouhinKnjByCriteriaInEntity criteria = new FygMstShouhinShouhinKnjByCriteriaInEntity();
            criteria.setShouhinId(fygId);
            List<FygMstShouhinShouhinKnjOutEntity> items = fygMstShouhinSelectMapper
                    .findFygMstShouhinShouhinKnjByCriteria(criteria);
            if (items != null) {
                formalName = items.stream().findFirst().map(i -> i.getShouhinKnj()).orElse("");
            }
        }

        // 商品名が取得できなかった場合
        if (StringUtils.isEmpty(formalName)) {
            // サービス種別IDを取得
            String svType = "00";
            ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity criteria = new ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity();
            criteria.setAlSvj(svjId);
            List<ComMscSvjigyoNameFirstSvKindCdOutEntity> items = comMscSvjigyoNameFirstSvKindCdSelectMapper
                    .findComMscSvjigyoNameFirstSvKindCdByCriteria(criteria);
            if (items != null && items.size() > 0 && items.get(0) != null) {
                svType = items.stream().findFirst().map(i -> i.getSvKindCd()).orElse("00");
            }

            // サービス有効期間IDを取得
            Integer termId = 0;
            if (!(svType.compareTo("81") >= 0 && svType.compareTo("99") <= 0)) {
                // 2018年4月以降の61,65または2021年4月以降のA1,A5の場合
                if ((date.compareTo("2018/04/01") >= 0 && (svType.equals("61") || svType.equals("65")))
                        || (date.compareTo("2021/04/01") >= 0 && (svType.equals("A1") || svType.equals("A5")))) {
                    ComMhcTermSvtype35TermidByCriteriaInEntity criteria35 = new ComMhcTermSvtype35TermidByCriteriaInEntity();
                    criteria35.setDateYmd(date);
                    List<ComMhcTermSvtype35TermidOutEntity> item35s = comMhcTermSelectMapper
                            .findComMhcTermSvtype35TermidByCriteria(criteria35);
                    if (item35s != null) {
                        termId = Integer.valueOf(item35s.stream().findFirst().map(i -> i.getTermid()).orElse(0));
                    }
                } else {
                    TermIdYukoKikanByCriteriaInEntity criteriaTerm = new TermIdYukoKikanByCriteriaInEntity();
                    criteriaTerm.setAlSvj(svjId);
                    criteriaTerm.setAsDate(date);
                    List<TermIdYukoKikanOutEntity> itemTerms = termIdSelectMapper
                            .findTermIdYukoKikanByCriteria(criteriaTerm);
                    if (itemTerms != null) {
                        termId = Integer.valueOf(itemTerms.stream().findFirst().map(i -> i.getTermid()).orElse(0));
                    }
                }

            }

            // 正式名称の取得
            if (Arrays.asList("A1", "A5", "A9").contains(svType)) {
                // 総合事業サービス
                ComMhcItemuseSougouFormalnameKnjByCriteriaInEntity criteriaSougou = new ComMhcItemuseSougouFormalnameKnjByCriteriaInEntity();
                criteriaSougou.setAlItm(String.valueOf(itemId));
                criteriaSougou.setLiTermid(String.valueOf(termId));
                List<ComMhcItemuseSougouFormalnameKnjOutEntity> itemSougous = comMhcItemuseSougouSelectMapper
                        .findComMhcItemuseSougouFormalnameKnjByCriteria(criteriaSougou);
                if (itemSougous != null) {
                    formalName = itemSougous.stream().findFirst().map(i -> i.getFormalnameKnj()).orElse("");
                }
            } else {
                // 介護・予防サービス・横だしサービス
                ItemNameFirstByCriteriaInEntity criteriaItem = new ItemNameFirstByCriteriaInEntity();
                criteriaItem.setAlSvjList(Arrays.asList(svjId));
                criteriaItem.setAlItm(itemId);
                criteriaItem.setLiTermid(termId);
                List<ItemNameFirstOutEntity> itemItems = comMhcItemuseSelectMapper
                        .findItemNameFirstByCriteria(criteriaItem);
                if (itemItems != null && itemItems.size() > 0 && itemItems.get(0) != null) {
                    formalName = itemItems.stream().findFirst().map(i -> i.getItemnameKnj()).orElse("");
                }
            }

        }

        return formalName != null ? formalName : "";
    }

    /**
     * 提供時間（開始～終了）から、訪問リハビリの回数を取得する
     *
     * @param gousikKbn 合成識別区分
     * @param sCode     サービスコード (厚生労働省６桁)
     * @param startTime サービス提供開始時間 (HH:mm)
     * @param endTime   サービス提供終了時間 (HH:mm)
     * @return 提供時間より得た回数
     */
    public Integer getKaisuFor1464(String gousikKbn, String sCode, String startTime, String endTime) {

        // 初期化
        Integer visitCount = 1;

        // 訪問リハビリであり、回数を持つサービスかどうかをチェック
        if (chkFreqServiceFor1464(gousikKbn, sCode) != 1) {
            return visitCount;
        }

        // 開始時間を分に変換
        Integer startMinutes = convertTimeToMinutes(startTime);

        // 終了時間を分に変換
        Integer endMinutes = convertTimeToMinutes(endTime);

        // 両方の時間が有効な場合
        if (startMinutes >= 0 && endMinutes >= 0) {
            // 差分による回数確定
            int diffMinutes;
            if (endMinutes > startMinutes) {
                diffMinutes = endMinutes - startMinutes;
            } else if (startMinutes > endMinutes) {
                diffMinutes = startMinutes - endMinutes;
            } else {
                diffMinutes = 0;
            }

            // 20分単位で回数を計算
            if (diffMinutes > 0) {
                Integer sessions = diffMinutes / 20;
                visitCount = sessions > 0 ? sessions : 1;
            }
        }

        return visitCount;
    }

    /**
     * f_kgh_cmn_get_scode_from_itemuse_sougou 総合事業ﾏｽﾀからｻｰﾋﾞｽｺｰﾄﾞを取得
     *
     * @param asSvtype   サービス種類
     * @param asSvcode   サービスコード
     * @param asDate     年月日（yyyy/MM/dd）
     * @param alTermid   期間ID
     * @param alKhokenCd 保険者コード
     * @return String 項目識別コード
     * <AUTHOR>
     */
    public String getScodeFromItemuseSougou(String asSvtype, String asSvcode, String asDate, Integer alTermid,
            Integer alKhokenCd) {
        Integer llKHokenCd;
        // 処理年月の月末日を求める
        String lsGetumatuYmd = asDate;
        // 共通関数「f_tukimatu」を呼び出し、月末を算出する
        String tukimatu = nds3GkFunc01Logic.getTukimatu(lsGetumatuYmd);
        // 共通関数「f_cnvymd 」日付の書式変換
        String ymd = nds3GkFunc01Logic.cnvYmd(tukimatu);
        if (CommonConstants.STR_A_ONE.equals(ymd) || CommonConstants.STR_A_FIVE.equals(ymd)
                || CommonConstants.NUM_STR_35.equals(ymd)) {
            llKHokenCd = CommonConstants.NUMBER_0;
        } else {
            llKHokenCd = alKhokenCd;
        }
        // 複写先のサービスコードを取得する
        ItemcodeAndScodeByCriteriaInEntity inEntity = new ItemcodeAndScodeByCriteriaInEntity();
        inEntity.setLlKHokenCd(CommonDtoUtil.objValToString(llKHokenCd));
        inEntity.setAsSvcode(asSvcode);
        inEntity.setAsSvtype(asSvtype);
        inEntity.setAlTrb(CommonDtoUtil.objValToString(alTermid));
        inEntity.setLsGetumatuYmd(ymd);
        List<ItemcodeAndScodeOutEntity> itemcodeAndScodeList = comMhcItemuseSougouSelectMapper
                .findItemcodeAndScodeByCriteria(inEntity);
        if (itemcodeAndScodeList.size() <= CommonConstants.NUMBER_0) {
            return StringUtils.EMPTY;
        }
        String scode = itemcodeAndScodeList.get(CommonConstants.NUMBER_0).getScode();
        return scode;
    }

    /**
     * 介護ｻｰﾋﾞｽ費適用ﾏｽﾀからｻｰﾋﾞｽｺｰﾄﾞを取得
     *
     * @param alSvj  サービス事業者ID
     * @param alItm  項目コード
     * @param asDate 適用日
     * @return String 項目識別コード
     * <AUTHOR>
     */
    public String getScodeFromItemuseSougou(Integer alSvj, Integer alItm, String asDate) {
        // サービス種別ID一時変数を定義
        String lsSvtype = StringUtil.EMPTY_STRING;
        // 有効期間ID一時変数を定義
        Integer lsTermId = CommonConstants.INT_0;
        // 戻り値の項目識別コード変数を定義
        String retCode = StringUtil.EMPTY_STRING;

        // サービス種別IDを取得
        KghKsgGetSvtypeFromSvJigyoIdByCriteriaInEntity inEntity = new KghKsgGetSvtypeFromSvJigyoIdByCriteriaInEntity();
        // サービス事業者ID
        inEntity.setJid(alSvj);
        List<KghKsgGetSvtypeFromSvJigyoIdOutEntity> outEntityList = jigyosyoIDSvSyuruiCodeSelectMapper
                .findKghKsgGetSvtypeFromSvJigyoIdByCriteria(inEntity);
        if (CollectionUtils.isNotEmpty(outEntityList)) {
            KghKsgGetSvtypeFromSvJigyoIdOutEntity outEntity = outEntityList.get(0);
            if (StringUtil.isNullOrEmpty(outEntity.getSvKindCd())) {
                lsSvtype = CommonConstants.SHURUI_00;
            } else {
                lsSvtype = outEntity.getSvKindCd();
            }
        } else {
            lsSvtype = CommonConstants.SHURUI_00;
        }

        if (!(CommonConstants.SHURUI_81_STRING.compareTo(lsSvtype) <= 0
                && CommonConstants.SHURUI_99_STRING.compareTo(lsSvtype) >= 0)) {
            if (BOUNDARY_2018_04_01.compareTo(asDate) <= 0
                    && (CommonConstants.SHURUI_61_STRING.equals(lsSvtype)
                            || CommonConstants.SHURUI_65_STRING.equals(lsSvtype))
                    || (BOUNDARY_2021_04_01.compareTo(asDate) <= 0 && (CommonConstants.SHURUI_A1_STRING.equals(lsSvtype)
                            || CommonConstants.SHURUI_A5_STRING.equals(lsSvtype)))) {
                // 検索条件をセット
                ComMhcTermSvtype35TermidByCriteriaInEntity inEntity2 = new ComMhcTermSvtype35TermidByCriteriaInEntity();
                // 適用日
                inEntity2.setDateYmd(asDate);
                // サービス有効期間IDを取得
                List<ComMhcTermSvtype35TermidOutEntity> outEntityList2 = comMhcTermSelectMapper
                        .findComMhcTermSvtype35TermidByCriteria(inEntity2);
                if (CollectionUtils.isNotEmpty(outEntityList2)) {
                    if (outEntityList2.get(0).getTermid() != null) {
                        lsTermId = Integer.valueOf(outEntityList2.get(0).getTermid());
                    } else {
                        lsTermId = CommonConstants.INT_0;
                    }
                }
            } else {
                // 検索条件をセット
                ComMhcTermTermidByCriteriaInEntity inEntity2 = new ComMhcTermTermidByCriteriaInEntity();
                // サービス事業者ID
                inEntity2.setAlSvj(alSvj);
                // 適用日
                inEntity2.setAsDate(asDate);
                // サービス有効期間IDを取得
                List<ComMhcTermTermidOutEntity> outEntityList2 = comMhcTermTermidSelectMapper
                        .findComMhcTermTermidByCriteria(inEntity2);
                if (CollectionUtils.isNotEmpty(outEntityList2)) {
                    if (outEntityList2.get(0).getTermid() != null) {
                        lsTermId = Integer.valueOf(outEntityList2.get(0).getTermid());
                    } else {
                        lsTermId = CommonConstants.INT_0;
                    }
                }
            }
        } else {
            lsTermId = CommonConstants.INT_0;
        }
        // 総合事業とそれ以外のサービスで処理を分岐
        if (CommonConstants.SHURUI_A1_STRING.equals(lsSvtype) || CommonConstants.SHURUI_A5_STRING.equals(lsSvtype)
                || CommonConstants.SHURUI_A9_STRING.equals(lsSvtype)) {
            // 総合事業
            ComMhcItemuseSougouScodeByCriteriaInEntity inEntity3 = new ComMhcItemuseSougouScodeByCriteriaInEntity();
            // 項目コード
            inEntity3.setAlItm(CommonDtoUtil.objValToString(alItm));
            // 有効期間ID
            inEntity3.setAiTerm(CommonDtoUtil.objValToString(lsTermId));
            List<ComMhcItemuseSougouScodeOutEntity> outEntityList3 = comMhcItemuseSougouSelectMapper
                    .findComMhcItemuseSougouScodeByCriteria(inEntity3);
            if (CollectionUtils.isNotEmpty(outEntityList3)) {
                // 項目識別コードを取得
                if (StringUtils.isNotEmpty(outEntityList3.get(0).getScode())) {
                    retCode = outEntityList3.get(0).getScode();
                }
            }
        } else {
            // 介護・予防サービス
            // 横だしサービス
            ComMhcItemuseScodeByCriteriaInEntity inEntity3 = new ComMhcItemuseScodeByCriteriaInEntity();
            // 事業所ID
            List<Integer> svJigyoIdList = new ArrayList<>();
            svJigyoIdList.add(alSvj);
            inEntity3.setAlSvjList(svJigyoIdList);
            // 項目コード
            inEntity3.setAiTerm(alItm);
            // 有効期間ID
            inEntity3.setAlItm(lsTermId);
            // 項目識別コードを取得
            List<ComMhcItemuseScodeOutEntity> outEntityList3 = comMhcItemuseSelectMapper
                    .findComMhcItemuseScodeByCriteria(inEntity3);
            if (CollectionUtils.isNotEmpty(outEntityList3)) {
                // 項目識別コードを取得
                if (StringUtils.isNotEmpty(outEntityList3.get(0).getScode())) {
                    retCode = outEntityList3.get(0).getScode();
                }
            }
        }
        return retCode;
    }

    /**
     * 時間文字列を分に変換
     * 
     * @param timeStr 時間文字列 (HH:mm)
     * @return 分数 (-1: 無効な時間)
     */
    private Integer convertTimeToMinutes(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return -1;
        }

        timeStr = timeStr.trim();
        if (timeStr.compareTo("00:00") < 0 || timeStr.compareTo("24:00") >= 0) {
            return -1;
        }

        try {
            String[] parts = timeStr.split(":");
            int hours = Integer.parseInt(parts[0]);
            int minutes = Integer.parseInt(parts[1]);
            return hours * 60 + minutes;
        } catch (Exception e) {
            return -1;
        }
    }

    /**
     * 訪問リハで、時間帯で回数が変わるサービスかどうかをチェックする
     * 
     * @param gousikKbn 合成式区分
     * @param sCode     サービスコード
     * @return 1: 該当する, 0: 該当しない
     */
    public Integer chkFreqServiceFor1464(String gousikKbn, String sCode) {
        Integer result = 0;

        // サービスタイプの取得 (左2文字)
        String svType = StringUtils.left(sCode, 2);

        switch (svType) {
            case "14":
            case "64":
                // 訪問リハ
                // 予防訪問リハ
                String middleCode = StringUtils.substring(sCode, 2, 6);
                String checkCode = StringUtils.substring(sCode, 2, 3);
                // 業務継続計画未策定減算のscodeは訪問リハ、予防訪問リハ共に3文字目に'D'で設定されているため、条件式に追加
                if (!gousikKbn.equals("3") || (gousikKbn.equals("3") && ("6101".equals(middleCode)
                        || "5010".equals(middleCode) || "6102".equals(middleCode) || "D".equals(checkCode)
                        || "C".equals(checkCode)))) {
                    result = 1;
                }

                // 予防訪問リハ12月超減算
                if ("64".equals(svType) && gousikKbn.equals("3") && "6123".equals(middleCode)) {
                    result = 1;
                }
                break;

            case "33":
            case "35":
                // 特定施設入居
                // 予防特定施設
                String facilityCode = StringUtils.substring(sCode, 2, 4);
                if ("16".equals(facilityCode)) {
                    result = 1;
                }
                break;
        }

        return result;
    }

    /**
     * サービスの上限数を取得
     *
     * @param svjId    サービス事業者ID
     * @param itemCode サービス内部id
     * @param ym       提供年月 (YYYY/MM形式)
     * @return 該当サービスの上限数
     */
    public Integer getSvMax(Integer svjId, Integer itemCode, String ym) {
        Integer maxLimit = 0;
        String date = ym + "/01";

        // 事業所のサービス種類CDを取得
        String svType = "00";
        SvKindCdByCriteriaInEntity criteria = new SvKindCdByCriteriaInEntity();
        criteria.setSvJigyoId(svjId);
        List<SvKindCdOutEntity> svKindCds = gaibuItakuItemSelectMapper.findSvKindCdByCriteria(criteria);
        if (svKindCds != null) {
            svType = svKindCds.stream().findFirst().map(i -> i.getSvKindCd()).orElse("00");
        }

        // サービス種類CDで処理を分岐
        switch (svType) {
            case "A1":
            case "A5":
            case "A9": {
                // 総合事業
                ComMhcItemuseSougouMaxByCriteriaInEntity in = new ComMhcItemuseSougouMaxByCriteriaInEntity();
                in.setAlItm(CommonDtoUtil.objValToString(itemCode));
                in.setLsDate(date);
                List<ComMhcItemuseSougouMaxOutEntity> maxs = comMhcItemuseSougouSelectMapper
                        .findComMhcItemuseSougouMaxByCriteria(in);
                if (maxs != null) {
                    maxLimit = maxs.stream().findFirst().map(i -> i.getMaxVal()).orElse(0);
                }
                break;
            }

            default: {
                // 介護・予防サービス・横だしサービス
                ComMhcItemuseMaxByCriteriaInEntity in = new ComMhcItemuseMaxByCriteriaInEntity();
                in.setLlSvjList(Arrays.asList(svjId));
                in.setAlItm(itemCode);
                in.setLsDate(date);
                List<ComMhcItemuseMaxOutEntity> maxs = comMhcItemuseSelectMapper.findComMhcItemuseMaxByCriteria(in);
                if (maxs != null) {
                    maxLimit = maxs.stream().findFirst().map(i -> i.getMaxVal()).orElse(0);
                }
                break;
            }
        }

        return maxLimit;
    }

    /**
     * 介護ｻｰﾋﾞｽ費適用ﾏｽﾀからｻｰﾋﾞｽｺｰﾄﾞを取得
     * 
     * @param svJigyoId サービス事業者ID
     * @param itemCode  項目コード
     * @param termId    有効期間ID
     * @return サービスコード (取得できない場合は空文字列)
     */
    public String getScodeFromItemuse2(Integer svJigyoId, Integer itemCode, Integer termId) {
        // サービスコード
        String scode = null;
        // 事業所のサービス種類CDを取得
        String serviceKindCd = getServiceKindCd(svJigyoId);
        if (StringUtils.isEmpty(serviceKindCd)) {
            serviceKindCd = DEFAULT_SERVICE_KIND_CD;
        }
        // サービス種類CDで処理を分岐
        if (SERVICE_KIND_CD_A1.equals(serviceKindCd) || SERVICE_KIND_CD_A5.equals(serviceKindCd)
                || SERVICE_KIND_CD_A9.equals(serviceKindCd)) {
            // 総合事業
            ComMhcItemuseSougouScodeByCriteriaInEntity criteria = new ComMhcItemuseSougouScodeByCriteriaInEntity();
            criteria.setAlItm(CommonDtoUtil.objValToString(itemCode));
            criteria.setAiTerm(CommonDtoUtil.objValToString(termId));
            List<ComMhcItemuseSougouScodeOutEntity> scodeOutEntities = comMhcItemuseSougouSelectMapper
                    .findComMhcItemuseSougouScodeByCriteria(criteria);
            if (CollectionUtils.isNotEmpty(scodeOutEntities)) {
                scode = scodeOutEntities.getFirst().getScode();
            }
        } else {
            // 介護・予防サービス
            // 横だしサービス
            ComMhcItemuseScodeByCriteriaInEntity criteria = new ComMhcItemuseScodeByCriteriaInEntity();
            criteria.setAlSvjList(Arrays.asList(svJigyoId));
            criteria.setAlItm(itemCode);
            criteria.setAiTerm(termId);
            List<ComMhcItemuseScodeOutEntity> entities = comMhcItemuseSelectMapper
                    .findComMhcItemuseScodeByCriteria(criteria);
            if (CollectionUtils.isNotEmpty(entities)) {
                scode = entities.getFirst().getScode();
            }
        }

        return StringUtils.defaultIfEmpty(scode, StringUtils.EMPTY);
    }

    /**
     * サービス事業者IDからサービス種類CDを取得する
     * 
     * @param svJigyoId サービス事業者ID
     * @return サービス種類CD (取得できない場合はnull)
     */
    private String getServiceKindCd(Integer svJigyoId) {
        // サービス事業者情報を取得
        ComMscSvjigyoCriteria criteria = new ComMscSvjigyoCriteria();
        criteria.createCriteria().andSvJigyoCdEqualTo(String.valueOf(svJigyoId));
        List<ComMscSvjigyo> svJigyoList = comMscSvjigyoMapper.selectByCriteria(criteria);
        if (CollectionUtils.isEmpty(svJigyoList)) {
            return null;
        }
        // サービス事業者名情報からサービス種類CDを取得
        ComMscSvjigyoNameCriteria svjigyoNameCriteria = new ComMscSvjigyoNameCriteria();
        criteria.createCriteria().andSvJigyoCdEqualTo(String.valueOf(svJigyoId));
        List<ComMscSvjigyoName> svJigyoNameList = comMscSvjigyoNameMapper.selectByCriteria(svjigyoNameCriteria);
        if (CollectionUtils.isEmpty(svJigyoNameList)) {
            return null;
        }
        return svJigyoNameList.getFirst().getSvKindCd();
    }

    /**
     * 年月から termid を取得する（簡易版）.
     * 
     * @param ym 対象の年月 (形式: "YYYY/MM")
     * @return termid
     */
    public Integer getTermidEasy(String ym) {
        // 戻り値初期化
        Integer termId = TERM_ID_16;

        // 入力チェック
        if (StringUtils.isEmpty(ym)) {
            return termId;
        }

        // 期間判定
        if (ym.compareTo(BOUNDARY_2003_03) <= 0) {
            termId = TERM_ID_1;
        } else if (ym.compareTo(BOUNDARY_2005_09) <= 0) {
            termId = TERM_ID_2;
        } else if (ym.compareTo(BOUNDARY_2006_03) <= 0) {
            termId = TERM_ID_3;
        } else if (ym.compareTo(BOUNDARY_2009_03) <= 0) {
            termId = TERM_ID_4;
        } else if (ym.compareTo(BOUNDARY_2012_03) <= 0) {
            termId = TERM_ID_5;
        } else if (ym.compareTo(BOUNDARY_2014_03) <= 0) {
            termId = TERM_ID_6;
        } else if (ym.compareTo(BOUNDARY_2015_03) <= 0) {
            termId = TERM_ID_7;
        } else if (ym.compareTo(BOUNDARY_2017_03) <= 0) {
            termId = TERM_ID_8;
        } else if (ym.compareTo(BOUNDARY_2018_03) <= 0) {
            termId = TERM_ID_9;
        } else if (ym.compareTo(BOUNDARY_2019_09) <= 0) {
            termId = TERM_ID_10;
        } else if (ym.compareTo(BOUNDARY_2021_03) <= 0) {
            termId = TERM_ID_11;
        } else if (ym.compareTo(BOUNDARY_2022_09) <= 0) {
            termId = TERM_ID_12;
        } else if (ym.compareTo(BOUNDARY_2024_03) <= 0) {
            termId = TERM_ID_13;
        } else if (ym.compareTo(BOUNDARY_2024_05) <= 0) {
            termId = TERM_ID_14;
        } else if (ym.compareTo(BOUNDARY_2025_03) <= 0) {
            termId = TERM_ID_15;
        }
        return termId;
    }

    /**
     * がかったるい（yyyy/mm/dd 型で返してくれない）から被せる.
     * 
     * @param date 対象年月日(yyyy/MM/dd)
     * @return 月末日（yyyy/mm/dd型）
     */
    public String getTukimatu(String date) {
        date = nds3GkFunc01Logic.getTukimatu(date);
        date = nds3GkFunc01Logic.cnvYmd(date);
        return date;
    }

    /**
     * 要介護度の（指定月の）配列を作成
     * 
     * @param alUserid ユーザーID
     * @param asYymm   年月 (YYYY/MM形式)
     * @return 出力 Dto
     */
    public YokaiDaysOutDto getYokaiDays(Integer alUserid, String asYymm) {
        // ローカル変数宣言
        int liDay;
        // 開始日付
        String lsStartYmd;
        // 終了日付
        String lsEndYmd;
        String lsDay;
        int liDEnd;
        long llRowcnt;
        int liFg = 0;
        List<Integer> piYokaiDays = new ArrayList<>();
        YokaiDaysOutDto outDto = new YokaiDaysOutDto();
        // 初期化
        for (liDay = 1; liDay <= MAX_MONTH_DAYS; liDay++) {
            // Javaの配列は0ベースなので調整
            piYokaiDays.add(0);
        }

        // 規定の月初日・月末日を得る
        lsStartYmd = String.format(FIRST_DAY_OF_MONTH_FORMAT, asYymm);
        String lsTmp = nds3GkFunc01Logic.getTukimatu(lsStartYmd);
        lsEndYmd = nds3GkFunc01Logic.cnvYmd(lsTmp);
        liDEnd = Integer.parseInt(lsEndYmd.substring(lsEndYmd.length() - 2));

        // 利用者の介護保険情報を検索
        KghCmnGetUserinfoMonthKaigo3gByCriteriaInEntity criteria = new KghCmnGetUserinfoMonthKaigo3gByCriteriaInEntity();
        criteria.setAlUser(alUserid);
        criteria.setAsDate(lsStartYmd);
        criteria.setAsEndd(lsEndYmd);

        List<KghCmnGetUserinfoMonthKaigo3gOutEntity> resultList = kaigoHokenJyouhouSelectMapper
                .findKghCmnGetUserinfoMonthKaigo3gByCriteria(criteria);
        llRowcnt = resultList.size();

        if (llRowcnt <= 0) {
            outDto.setResult(FAILURE_CODE);
            return outDto;
        }

        // 各日付に対して要介護度を設定
        for (liDay = 1; liDay <= MAX_MONTH_DAYS; liDay++) {
            if (liDay > liDEnd) {
                if (liDay > 1) {
                    piYokaiDays.set(liDay - 1, piYokaiDays.get(liDay - 2));
                }
                continue;
            }

            lsDay = String.format(DATE_FORMAT, asYymm, liDay);
            // フィルタリング処理
            List<KghCmnGetUserinfoMonthKaigo3gOutEntity> filteredList = filterResults(resultList, lsDay);

            if (!filteredList.isEmpty()) {
                piYokaiDays.set(liDay - 1, filteredList.get(0).getYokaiKbn());
                liFg = 1;
            }
        }

        if (liFg == 0) {
            outDto.setResult(FAILURE_CODE);
            return outDto;
        }
        outDto.setYokaiDaysList(piYokaiDays);
        return outDto;
    }

    /**
     * 結果リストをフィルタリング
     * 
     * @param resultList 元の結果リスト
     * @param targetDate 対象日付
     * @return フィルタリングされたリスト
     */
    private List<KghCmnGetUserinfoMonthKaigo3gOutEntity> filterResults(
            List<KghCmnGetUserinfoMonthKaigo3gOutEntity> resultList, String targetDate) {
        return resultList.stream().filter(
                entity -> (entity.getNinteiStartYmd() == null || entity.getNinteiStartYmd().compareTo(targetDate) <= 0)
                        && (StringUtils.isEmpty(entity.getNinteiEndYmd())
                                || entity.getNinteiEndYmd().compareTo(targetDate) >= 0)
                        && (entity.getStartYmd() == null || entity.getStartYmd().compareTo(targetDate) <= 0)
                        && (StringUtils.isEmpty(entity.getEndYmd()) || entity.getEndYmd().compareTo(targetDate) >= 0))
                .toList();
    }

    /**
     * 事業者名(略称)を得る（履歴対応版）f_kgh_cmp_shien_name_ryaku
     * 
     * @param alShien 対象の事業所id
     * @param asYmd   対象年月日
     * @return 事業所名(略称)
     * <AUTHOR>
     */
    public String getShienNameRyaku(Integer alShien, String asYmd) {
        // 月末日をストレートに返す
        String lsYmd = getTukimatu(asYmd);
        String lsDdd = StringUtils.EMPTY;
        // 検索
        JigyosyaRyakuByCriteriaInEntity jigyosyaRyakuByCriteriaInEntity = new JigyosyaRyakuByCriteriaInEntity();
        // 事業所id
        jigyosyaRyakuByCriteriaInEntity.setAlShien(String.valueOf(alShien));
        // 月末日
        jigyosyaRyakuByCriteriaInEntity.setLsYmd(lsYmd);
        List<JigyosyaRyakuOutEntity> jigyosyaRyakuList = comMscSvjigyoRirekiSelectMapper
                .findJigyosyaRyakuByCriteria(jigyosyaRyakuByCriteriaInEntity);
        // チェック
        if (CollectionUtils.isNotEmpty(jigyosyaRyakuList)
                && StringUtils.isNotEmpty(jigyosyaRyakuList.getFirst().getJigyoRyakuKnj())) {
            lsDdd = jigyosyaRyakuList.getFirst().getJigyoRyakuKnj();
        } else {
            // 検索
            SvJigyoNameByCriteriaInEntity svJigyoNameByCriteriaInEntity = new SvJigyoNameByCriteriaInEntity();
            // 事業所id
            svJigyoNameByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(alShien));
            List<SvJigyoNameOutEntity> svJigyoNameList = comMscSvjigyoSelectMapper
                    .findSvJigyoNameByCriteria(svJigyoNameByCriteriaInEntity);
            // チェック
            if (CollectionUtils.isNotEmpty(svJigyoNameList)
                    && StringUtils.isNotEmpty(svJigyoNameList.getFirst().getJigyoRyakuKnj())) {
                lsDdd = svJigyoNameList.getFirst().getJigyoRyakuKnj();
            }
        }
        return lsDdd;
    }

    /**
     * f_kgh_cmp_shien_number : 事業者番号を得る（履歴対応版）
     * 
     * @param alShien 対象の事業所id
     * @param asYmd   履歴検索年月日（yyyy/mm/dd）
     * @return 事業所番号
     * <AUTHOR>
     */
    public String getShienNumber(Integer alShien, String asYmd) {
        String lsDdd = StringUtils.EMPTY;
        String lsYmd;
        lsYmd = getTukimatu(asYmd);
        JigyoNumberRirekiByCriteriaInEntity jigyoNumberRirekiByCriteriaInEntity = new JigyoNumberRirekiByCriteriaInEntity();
        jigyoNumberRirekiByCriteriaInEntity.setAlShien(CommonDtoUtil.objValToString(alShien));
        jigyoNumberRirekiByCriteriaInEntity.setLsYmd(lsYmd);
        // DAOを実行
        List<JigyoNumberRirekiOutEntity> jigyoNumberRirekiList = comMscSvjigyoRirekiSelectMapper
                .findJigyoNumberRirekiByCriteria(jigyoNumberRirekiByCriteriaInEntity);
        // チェック
        if (CollectionUtils.isNotEmpty(jigyoNumberRirekiList)) {
            lsDdd = jigyoNumberRirekiList.getFirst().getJigyoNumber();
            if (lsDdd == null) {
                lsDdd = StringUtils.EMPTY;
            }
        }
        if (StringUtils.length(lsDdd) > 0) {
            // OK
        } else {
            JigyoCdNumberByCriteriaInEntity jigyoCdNumberByCriteriaInEntity = new JigyoCdNumberByCriteriaInEntity();
            jigyoCdNumberByCriteriaInEntity.setIlSvJigyoId(alShien);
            // DAOを実行
            List<JigyoCdNumberOutEntity> jigyoCdNumberOutEntity = comMscSvjigyoSelectMapper
                    .findJigyoCdNumberByCriteria(jigyoCdNumberByCriteriaInEntity);
            // チェック
            if (CollectionUtils.isNotEmpty(jigyoCdNumberOutEntity)) {
                lsDdd = jigyoCdNumberOutEntity.getFirst().getJigyoNumber();
                if (lsDdd == null) {
                    lsDdd = StringUtils.EMPTY;
                }
            }
        }
        return lsDdd;
    }

    /**
     * 事業者名を得る（履歴対応版）.
     * 
     * @param alShien 対象の事業所id
     * @param asYmd   履歴検索年月日（yyyy/mm/dd）
     * @return 事業所名
     */
    public String getShienName(Integer alShien, String asYmd) {
        // がかったるい（yyyy/mm/dd 型で返してくれない）から被せる.
        String lsYmd = getTukimatu(asYmd);
        // 事業者名を得る（履歴対応版）
        String lsDdd = nds3GkFunc01Logic.getJigyoName(alShien, lsYmd);
        return lsDdd;
    }

    /**
     * f_kgh_cmp_shien_tel : 事業者の電話番号を得る（履歴対応版）
     * 
     * @param alShien 対象の事業所id
     * @param asYmd   履歴検索年月日（yyyy/mm/dd）
     * @return 電話番号
     * <AUTHOR>
     */
    public String getShienTel(Integer alShien, String asYmd) {
        String lsDdd = StringUtils.EMPTY;
        String lsYmd = this.getTukimatu(asYmd);
        TelNoByCriteriaInEntity telNoByCriteriaInEntity = new TelNoByCriteriaInEntity();
        // 対象の事業所id
        telNoByCriteriaInEntity.setAlShien(CommonDtoUtil.objValToString(alShien));
        // 履歴検索年月日（yyyy/mm/dd）
        telNoByCriteriaInEntity.setLsYmd(lsYmd);
        List<TelNoOutEntity> telNoList = comMscSvjigyoRirekiSelectMapper.findTelNoByCriteria(telNoByCriteriaInEntity);
        // チェック
        if (CollectionUtils.isNotEmpty(telNoList)) {
            lsDdd = telNoList.getFirst().getTel();
            if (lsDdd == null) {
                lsDdd = StringUtils.EMPTY;
            }
        }
        if (StringUtils.isNotEmpty(lsDdd)) {
            // OK
        } else {
            // findSvTelByCriteria
            comMscSvjigyoSelectMapper.findSvTelByCriteria(null);
            SvTelByCriteriaInEntity svTelByCriteriaInEntity = new SvTelByCriteriaInEntity();
            // 対象の事業所id
            svTelByCriteriaInEntity.setAlShien(CommonDtoUtil.objValToString(alShien));
            List<SvTelOutEntity> svTelList = comMscSvjigyoSelectMapper.findSvTelByCriteria(svTelByCriteriaInEntity);
            // チェック
            if (CollectionUtils.isNotEmpty(svTelList)) {
                lsDdd = svTelList.getFirst().getTel();
                if (lsDdd == null) {
                    lsDdd = StringUtils.EMPTY;
                }
            }
        }
        return lsDdd;
    }

    /**
     * f3gk_com_fuseji_single_gu : 偶数番目を伏字にする
     * 
     * @param asTarget 対象
     * @return 結果
     * <AUTHOR>
     */
    public String getF3gkComFusejiSingleGu(String asTarget) {
        // 引数チェック
        if (asTarget == null || asTarget.isEmpty()) {
            return StringUtils.EMPTY;
        }

        StringBuilder lsRet = new StringBuilder(asTarget);
        int llLen = lsRet.length();
        int lig = 0;

        // 2文字以上である事
        if (llLen > 1) {
            for (int lic = 0; lic < llLen; lic++) {
                char lsOne = lsRet.charAt(lic);

                if (lsOne != ' ' && lsOne != '　') {
                    // スペース（半角・全角）は対象外(skip)
                    lig++;

                    if (lig % 2 == 0) {
                        // 偶数は処理
                        lsRet.setCharAt(lic, '*');
                    }
                }
            }
        }

        String result = lsRet.toString();
        return result;
    }

    /**
     * scode より itemcode（sv_item_cd）を取得する
     * 
     * @param asScode  対象のscode
     * @param aiTermid 対象のtermid
     * @param adTanka  対象の１回当たりの単価
     * @return 結果の itemcode
     */
    public Integer getItemcodeFromScode(String asScode, Integer aiTermid, Integer adTanka) {
        Integer itemCode = CommonConstants.NUMBER_0;
        if (adTanka == null) {
            adTanka = CommonConstants.NUMBER_0;
        }
        if (adTanka > 0) {
            // 単価がある
            CmnGetItemcodeFromScode21SonzaiTankaByCriteriaInEntity entity1 = new CmnGetItemcodeFromScode21SonzaiTankaByCriteriaInEntity();
            entity1.setAsScode(asScode);
            entity1.setAdTanka(CommonDtoUtil.objValToString(adTanka));
            entity1.setAiTerm(CommonDtoUtil.objValToString(aiTermid));
            List<CmnGetItemcodeFromScode21SonzaiTankaOutEntity> outEntityList1 = comMhcItemSelectMapper
                    .findCmnGetItemcodeFromScode21SonzaiTankaByCriteria(entity1);
            if (CollectionUtils.isNotEmpty(outEntityList1)) {
                itemCode = outEntityList1.get(0).getItemcode();
            } else {
                itemCode = 0;
            }

        } else {
            // 単価が無い
            CmnGetItemcodeFromScode21TankaGaNaiByCriteriaInEntity entity2 = new CmnGetItemcodeFromScode21TankaGaNaiByCriteriaInEntity();
            entity2.setAsScode(asScode);
            entity2.setAiTerm(CommonDtoUtil.objValToString(aiTermid));
            List<CmnGetItemcodeFromScode21TankaGaNaiOutEntity> outEntityList2 = comMhcItemSelectMapper
                    .findCmnGetItemcodeFromScode21TankaGaNaiByCriteria(entity2);
            if (CollectionUtils.isNotEmpty(outEntityList2)) {
                itemCode = outEntityList2.get(0).getItemcode();
            } else {
                itemCode = 0;
            }
        }
        return itemCode;
    }

    /**
     * scode より itemcode（sv_item_cd）を取得する
     * 
     * @param asScode  対象のscode
     * @param aiTermid 対象のtermid
     * @param adTanka  対象の１回当たりの単価
     * @return 結果の itemcode
     */
    public Integer getItemcodeFromScode21(String asScode, Integer aiTermid, Integer adTanka, String asYymm) {
        Integer itemCode = CommonConstants.NUMBER_0;
        String lsYmd = asYymm + CommonConstants.STRING_FIRST_DAY;

        if (adTanka == null) {
            adTanka = CommonConstants.NUMBER_0;
        }

        if (adTanka > 0) {
            // 単価がある
            ComMhcItemSonzaiTankaByCriteriaInEntity comMhcItemSonzaiTankaByCriteriaInEntity = new ComMhcItemSonzaiTankaByCriteriaInEntity();
            comMhcItemSonzaiTankaByCriteriaInEntity.setAsScode(asScode);
            comMhcItemSonzaiTankaByCriteriaInEntity.setAdTanka(CommonDtoUtil.objValToString(adTanka));
            comMhcItemSonzaiTankaByCriteriaInEntity.setAiTerm(CommonDtoUtil.objValToString(aiTermid));
            comMhcItemSonzaiTankaByCriteriaInEntity.setLsYmd(lsYmd);
            List<ComMhcItemSonzaiTankaOutEntity> comMhcItemSonzaiTankaOutEntity = comMhcItemSelectMapper
                    .findComMhcItemSonzaiTankaByCriteria(comMhcItemSonzaiTankaByCriteriaInEntity);
            if (CollectionUtils.isNotEmpty(comMhcItemSonzaiTankaOutEntity)) {
                itemCode = comMhcItemSonzaiTankaOutEntity.get(0).getItemcode();
                if (CommonConstants.STR_0.equals(CommonDtoUtil.objValToString(itemCode))) {
                    // 単価がある
                    CmnGetItemcodeFromScode21SonzaiTankaByCriteriaInEntity entity1 = new CmnGetItemcodeFromScode21SonzaiTankaByCriteriaInEntity();
                    entity1.setAsScode(asScode);
                    entity1.setAdTanka(CommonDtoUtil.objValToString(adTanka));
                    entity1.setAiTerm(CommonDtoUtil.objValToString(aiTermid));
                    List<CmnGetItemcodeFromScode21SonzaiTankaOutEntity> cmnGetItemcodeFromScode21SonzaiTankaOutEntity = comMhcItemSelectMapper
                            .findCmnGetItemcodeFromScode21SonzaiTankaByCriteria(entity1);
                    itemCode = cmnGetItemcodeFromScode21SonzaiTankaOutEntity.get(0).getItemcode();
                }
            }
        } else {
            // 単価が無い
            ComMhcItemTankaGaNaiByCriteriaInEntity comMhcItemTankaGaNaiByCriteriaInEntity = new ComMhcItemTankaGaNaiByCriteriaInEntity();
            comMhcItemTankaGaNaiByCriteriaInEntity.setAsScode(asScode);
            comMhcItemTankaGaNaiByCriteriaInEntity.setAiTerm(CommonDtoUtil.objValToString(aiTermid));
            comMhcItemTankaGaNaiByCriteriaInEntity.setLsYmd(lsYmd);
            List<ComMhcItemTankaGaNaiOutEntity> comMhcItemTankaGaNaiOutEntity = comMhcItemSelectMapper
                    .findComMhcItemTankaGaNaiByCriteria(comMhcItemTankaGaNaiByCriteriaInEntity);
            if (CollectionUtils.isNotEmpty(comMhcItemTankaGaNaiOutEntity)) {
                itemCode = comMhcItemTankaGaNaiOutEntity.get(0).getItemcode();
                if (CommonConstants.STR_0.equals(CommonDtoUtil.objValToString(itemCode))) {
                    // 単価が無い
                    CmnGetItemcodeFromScode21TankaGaNaiByCriteriaInEntity entity2 = new CmnGetItemcodeFromScode21TankaGaNaiByCriteriaInEntity();
                    entity2.setAsScode(asScode);
                    entity2.setAiTerm(CommonDtoUtil.objValToString(aiTermid));
                    List<CmnGetItemcodeFromScode21TankaGaNaiOutEntity> outEntityList2 = comMhcItemSelectMapper
                            .findCmnGetItemcodeFromScode21TankaGaNaiByCriteria(entity2);
                    if (CollectionUtils.isNotEmpty(outEntityList2)) {
                        itemCode = outEntityList2.get(0).getItemcode();
                    }
                }
            }
        }
        return itemCode;
    }

    /**
     * f3gk_com_fuseji_single：伏字にする必要があるかどうかを判断し、その結果の文字列を返します
     * 
     * @param asSection   プリンタセクション
     * @param asType      伏字のタイプ ・利用者氏名（カナ含む） …"name" ・電話/FAX/携帯・住所(要は後ろ４文字を伏字にする)
     *                    …"nums" ・被保険者番号 ………………"hiho" ・生年月日などの年月日 ……"bday"
     * @param asTarget    対象の文字列
     * @param shokuinId   職員ＩＤ
     * @param gsyscd      システムコード
     * @param gbRshuukei  利用請求集計中フラグ true(1)：集計中 false(0)：集計中以外
     * @param modifiedCnt 更新回数
     * @return 変換後の文字列
     * <AUTHOR>
     */
    public String getFusejiSingle(String asSection, String asType, String asTarget, Integer shokuinId, String gsyscd,
            String gbRshuukei, String modifiedCnt) {
        // 引き数チェック
        String lsRet = asTarget;
        if (lsRet == null) {
            lsRet = StringUtils.EMPTY;
        }
        if (StringUtils.length(lsRet) > CommonConstants.INT_0
                && StringUtils.length(asSection) > CommonConstants.INT_0) {
            // OK
        } else {
            return lsRet;
        }
        // 現在（20110216）、フラグを一括で見る設定。
        F3gkGetProfileInDto inDto = new F3gkGetProfileInDto();
        // 職員ＩＤ
        inDto.setShokuId(shokuinId);
        // 法人ＩＤ
        inDto.setHoujinId(CommonConstants.INT_0);
        // 施設ＩＤ
        inDto.setShisetuId(CommonConstants.INT_0);
        // 事業所ID
        inDto.setSvJigyoId(CommonConstants.INT_0);
        // 画面名
        inDto.setKinounameKnj(CommonConstants.KINOU_NAME_PRT);
        // セクション
        inDto.setSectionKnj(asSection);
        // キー
        inDto.setKeyKnj(CommonConstants.S_AMIKAKE_FLG);
        // 初期値
        inDto.setAsDefault(CommonConstants.STR_0);
        // システムコード
        inDto.setGsyscd(gsyscd);
        // 利用請求集計中フラグ true(1)：集計中 false(0)：集計中以外
        inDto.setGbRshuukei(gbRshuukei);
        // 更新回数
        inDto.setModifiedCnt(modifiedCnt);
        if (CommonConstants.STR_0.equals(nds3GkFunc01Logic.getF3gkProfile(inDto))) {
            return asTarget;
        }
        // 置換処理
        if (CommonConstants.NAME_STR.equals(asType) || CommonConstants.HIHO_STR.equals(asType)) {
            // 偶数を伏字にする : 被保険者番号は間に空白が入らない → 同じルーチンでＯＫ
            lsRet = getF3gkComFusejiSingleGu(lsRet);
        } else if (CommonConstants.NAME_NUMS.equals(asType)) {
            // 後ろ４文字を伏字にする
            lsRet = getFusejiSingleR4(lsRet);
        } else if (CommonConstants.HIHO_BDAY.equals(asType)) {
            // 年月の月だけ伏字にする
            lsRet = getFusejiSingleYmd(lsRet);
        } else {
            // それ以外は素通りとする
        }
        return lsRet;
    }

    /**
     * f3gk_com_fuseji_single_r4：後ろ４文字を伏字にする
     * 
     * @param asTarget 対象
     * @return 結果
     * <AUTHOR>
     */
    public String getFusejiSingleR4(String asTarget) {
        // 引き数チェック
        String lsRet = asTarget;
        if (lsRet == null) {
            lsRet = StringUtils.EMPTY;
        }
        int llLen = StringUtils.length(lsRet);
        // ４文字以上である事
        if (llLen > CommonConstants.INT_4) {
            lsRet = StringUtils.substring(lsRet, CommonConstants.INT_0, llLen - CommonConstants.INT_4)
                    + CommonConstants.FOUR_STARS;
        } else {
            // ４文字も無い : 偶数番目のみ：４文字以下であればMAX２回←ループ回すよりもif２回の方がstep数が少ない
            StringBuilder sb = new StringBuilder(lsRet);
            if (llLen == CommonConstants.INT_4) {
                sb.setCharAt(CommonConstants.INT_3, CommonConstants.SINGLE_STAR.charAt(CommonConstants.INT_0));
            }
            if (llLen >= CommonConstants.INT_2) {
                sb.setCharAt(CommonConstants.INT_1, CommonConstants.SINGLE_STAR.charAt(CommonConstants.INT_0));
            }
            lsRet = sb.toString();
        }
        return lsRet;
    }

    /**
     * f3gk_com_fuseji_single_ymd：年月日の「月」を伏字にする
     * 
     * @param asTarget 対象
     * @return 結果
     * <AUTHOR>
     */
    public String getFusejiSingleYmd(String asTarget) {
        // 引き数チェック
        String lsRet = asTarget;
        if (lsRet == null) {
            lsRet = StringUtils.EMPTY;
        }
        int llLen = lsRet.length();
        // ５文字以上である事
        if (llLen > CommonConstants.INT_4) {
            int liP = lsRet.indexOf(CommonConstants.DATE_SEPARATOR);
            if (liP >= CommonConstants.INT_0 && liP < llLen - CommonConstants.INT_1) {
                // 「/」付きの形式である
                lsRet = replaceSubstring(lsRet, liP + CommonConstants.INT_1, CommonConstants.INT_2,
                        CommonConstants.SINGLE_STAR + CommonConstants.SINGLE_STAR);
            } else {
                // 「月」の形式か！？
                liP = lsRet.indexOf(CommonConstants.MONTH_CHAR);
                if (liP >= CommonConstants.INT_0) {
                    lsRet = replaceSubstring(lsRet, liP - CommonConstants.INT_2, CommonConstants.INT_2,
                            CommonConstants.SINGLE_STAR + CommonConstants.SINGLE_STAR);
                } else {
                    // スラッシュも「月」も無い
                    if (llLen >= CommonConstants.INT_5) {
                        char firstChar = lsRet.charAt(CommonConstants.INT_0);
                        if (Character.isDigit(firstChar)) {
                            // 先頭が数字 → 西暦で「/」抜き
                            lsRet = replaceSubstring(lsRet, CommonConstants.INT_4, CommonConstants.INT_2,
                                    CommonConstants.SINGLE_STAR + CommonConstants.SINGLE_STAR);
                        } else {
                            if (lsRet.indexOf(CommonConstants.YEAR_CHAR) < CommonConstants.INT_0) {
                                // 和暦で「/」抜き
                                lsRet = replaceSubstring(lsRet, CommonConstants.INT_3, CommonConstants.INT_2,
                                        CommonConstants.SINGLE_STAR + CommonConstants.SINGLE_STAR);
                            }
                        }
                    }
                }
            }
        }
        return lsRet;
    }

    /**
     * 指定位置の部分文字列を置換するヘルパーメソッド
     * 
     * @param source      元の文字列
     * @param start       開始位置
     * @param length      置換する長さ
     * @param replacement 置換文字列
     * @return 置換後の文字列
     * <AUTHOR>
     */
    private String replaceSubstring(String source, int start, int length, String replacement) {
        // 索引範囲を安全に処理
        if (start < CommonConstants.INT_0)
            start = CommonConstants.INT_0;
        int end = start + length;
        if (end > source.length())
            end = source.length();
        // 部分文字列を置換
        return source.substring(CommonConstants.INT_0, start) + replacement + source.substring(end);
    }
}
