package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01217Care;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01217PlanImplementationData;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanImplementationAchievementsRegistUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanImplementationAchievementsRegistUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucKjisshi2Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghTocGazouKanriRirekiMapper;
import jp.ndsoft.carebase.common.dao.mybatis.ShoTucCasebikoMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucKjisshi2;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucKjisshi2Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghTocGazouKanriRirekiCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ShoTucCasebiko;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ShoTucCasebikoCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.GazouIdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GazouIdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTocGazouKanriRirekiSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.smh.framework.util.AppUtil;

/**
 * 
 * @since 2025.05.26
 * <AUTHOR> 朱红昌
 * @implNote GUI01217_［計画実施-実績登録］画面にケース一覧の情報を保存
 */
@Service
public class PlanImplementationAchievementsRegistUpdateServiceImpl extends
        UpdateServiceImpl<PlanImplementationAchievementsRegistUpdateServiceInDto, PlanImplementationAchievementsRegistUpdateServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private ShoTucCasebikoMapper ShoTucCasebikoMapper;

    @Autowired
    private KghTocGazouKanriRirekiSelectMapper kghTocGazouKanriRirekiSelectMapper;

    @Autowired
    private KghTocGazouKanriRirekiMapper kghTocGazouKanriRirekiMapper;

    @Autowired
    private CpnTucKjisshi2Mapper cpnTucKjisshi2Mapper;

    /**
     * GUI01217_「計画実施‐実績登録」画面実績情報一覧の情報を保存
     * 
     * @param inDto 画面実績情報一覧の情報データ保存入力DTO.
     * @return 画面実績情報一覧の情報データ保存出力DTO
     * @throws Exception ExclusiveException
     */
    @Override
    protected PlanImplementationAchievementsRegistUpdateServiceOutDto mainProcess(
            PlanImplementationAchievementsRegistUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // 戻り値初期化
        PlanImplementationAchievementsRegistUpdateServiceOutDto out = new PlanImplementationAchievementsRegistUpdateServiceOutDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. バリデーションチェックを行う。===============
         * 
         */
        // なし

        /*
         * ===============3.ケースリスト情報の保存処理を実施===============
         * 
         */
        // 3. 「リクエストパラメータ.ケースリストの件数＞0件 且つ ケース更新フラグ=1」の場合、ケースリスト情報の保存処理を実施する
        if (!CollectionUtils.isNullOrEmpty(inDto.getCareList())
                && CommonDtoUtil.checkStringEqual(inDto.getCaseUpdateFlag(), CommonConstants.UPDATE_FLG_1)) {
            // 3.1. リクエストパラメータ.ケースリストを繰り返して、下記処理を行う。
            for (Gui01217Care caseInfo : inDto.getCareList()) {
                ShoTucCasebiko shoTucCasebiko = new ShoTucCasebiko();
                ShoTucCasebikoCriteria shoTucCasebikoCriteria = new ShoTucCasebikoCriteria();
                // 3.1.1 ケースリスト.更新区分=Cの場合、29-17 備考の情報を登録
                if (CommonDtoUtil.isCreate(caseInfo)) {
                    // 備考区分
                    shoTucCasebiko.setBikoKbn(CommonConstants.NUMBER_48);
                    // 法人ID
                    shoTucCasebiko.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHojinId()));
                    // 施設ID
                    shoTucCasebiko.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                    // 記録日
                    shoTucCasebiko.setYymmYmd(caseInfo.getYymmYmd());
                    // 利用者番号
                    shoTucCasebiko.setUserid(CommonDtoUtil.strValToInt(inDto.getUserid()));
                    // 開始時間
                    shoTucCasebiko.setTimeHh(CommonDtoUtil.strValToInt(caseInfo.getTimeHh()));
                    // 開始分
                    shoTucCasebiko.setTimeMm(CommonDtoUtil.strValToInt(caseInfo.getTimeMm()));
                    // ケース種別
                    shoTucCasebiko.setCaseCd(CommonDtoUtil.strValToInt(caseInfo.getCaseCd()));
                    // ケース事項
                    shoTucCasebiko.setCaseKnj(caseInfo.getCaseKnj());
                    // 記入者
                    shoTucCasebiko.setStaffid(CommonDtoUtil.strValToInt(caseInfo.getStaffid()));
                    // ｹｰｽ転記フラグ
                    shoTucCasebiko.setCaseFlg(CommonDtoUtil.strValToInt(caseInfo.getCaseFlg()));
                    // 申し送りフラグ
                    shoTucCasebiko
                            .setMoushiokuriFlg(CommonDtoUtil.strValToInt(caseInfo.getMoushiokuriFlg()));
                    // システム別フラグ
                    shoTucCasebiko.setSystemFlg(CommonConstants.INT_9);
                    // 結果元ユニークID
                    shoTucCasebiko.setBaseUniqueId(caseInfo.getBaseUniqueId());
                    // 更新日時
                    shoTucCasebiko.setTimeStmp(AppUtil.getSystemTimeStamp());
                    // 事業者ID
                    shoTucCasebiko.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
                    // 計画書(2)ID
                    shoTucCasebiko.setKs21Id(CommonDtoUtil.strValToInt(caseInfo.getKs21Id()));
                    // 計画書(2)詳細ID
                    shoTucCasebiko.setKs22Id(CommonDtoUtil.strValToInt(caseInfo.getKs22Id()));
                    // 実施モニタリング詳細ID
                    shoTucCasebiko.setKjisshi2Id(CommonDtoUtil.strValToInt(caseInfo.getKjisshi2Id()));
                    // プロブレムID
                    shoTucCasebiko.setProblemId(CommonDtoUtil.strValToInt(caseInfo.getProblemId()));
                    ShoTucCasebikoMapper.insertSelective(shoTucCasebiko);
                }

                // 3.1.2 ケースリスト.更新区分=Uの場合、29-17 備考の情報を更新
                if (CommonDtoUtil.isUpdate(caseInfo)) {
                    // 記録日
                    shoTucCasebiko.setYymmYmd(caseInfo.getYymmYmd());
                    // 開始時間
                    shoTucCasebiko.setTimeHh(CommonDtoUtil.strValToInt(caseInfo.getTimeHh()));
                    // 開始分
                    shoTucCasebiko.setTimeMm(CommonDtoUtil.strValToInt(caseInfo.getTimeMm()));
                    // ケース種別
                    shoTucCasebiko.setCaseCd(CommonDtoUtil.strValToInt(caseInfo.getCaseCd()));
                    // ケース事項
                    shoTucCasebiko.setCaseKnj(caseInfo.getCaseKnj());
                    // 記入者
                    shoTucCasebiko.setStaffid(CommonDtoUtil.strValToInt(caseInfo.getStaffid()));
                    // ｹｰｽ転記フラグ
                    shoTucCasebiko.setCaseFlg(CommonDtoUtil.strValToInt(caseInfo.getCaseFlg()));
                    // 申し送りフラグ
                    shoTucCasebiko
                            .setMoushiokuriFlg(CommonDtoUtil.strValToInt(caseInfo.getMoushiokuriFlg()));
                    // 条件設定
                    shoTucCasebikoCriteria.createCriteria().andUniqueIdEqualTo(caseInfo.getUniqueId());

                    int count = ShoTucCasebikoMapper.updateByCriteriaSelective(shoTucCasebiko, shoTucCasebikoCriteria);

                    if (count <= 0) {
                        LOG.info(Constants.END);
                        throw new ExclusiveException();

                    }
                }

                // 3.1.3 ケースリスト.更新区分=Dの場合、
                if (CommonDtoUtil.isDelete(caseInfo)) {
                    // 3.1.3.1. 29-17 備考の情報を削除処理実施
                    // 条件設定
                    shoTucCasebikoCriteria.createCriteria().andUniqueIdEqualTo(caseInfo.getUniqueId());

                    int count = ShoTucCasebikoMapper.deleteByCriteria(shoTucCasebikoCriteria);

                    if (count <= 0) {
                        LOG.info(Constants.END);
                        throw new ExclusiveException();
                    }

                    // 3.1.3.2. 画像関連記録履歴を削除処理実施
                    // 3.1.3.2.1. 下記の画像関連記録履歴情報取得のDAOを利用し、画像IDを取得
                    GazouIdByCriteriaInEntity gazouIdByCriteriaInEntity = new GazouIdByCriteriaInEntity();
                    // 機能ID
                    gazouIdByCriteriaInEntity.setKinouId(CommonConstants.FUNCTION_ID_INTEGER);
                    // 法人ID
                    gazouIdByCriteriaInEntity.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHojinId()));
                    // 施設ID
                    gazouIdByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                    // 事業所ID
                    gazouIdByCriteriaInEntity.setSvJigyoId(CommonConstants.NUMBER_0);
                    // 利用者IDリスト
                    List<Integer> useridList = new ArrayList<>();
                    useridList.add(CommonDtoUtil.strValToInt(inDto.getUserid()));
                    gazouIdByCriteriaInEntity.setUseridList(useridList);
                    // 履歴番号リスト
                    List<Integer> recNoList = new ArrayList<>();
                    recNoList.add(CommonDtoUtil.strValToInt(inDto.getCareList().get(CommonConstants.INT_0).getRecNo()));
                    gazouIdByCriteriaInEntity.setRecNoList(recNoList);
                    // 記録日リスト
                    List<String> ymdList = new ArrayList<>();
                    ymdList.add(CommonConstants.EMPTY_STRING);
                    gazouIdByCriteriaInEntity.setYmdList(ymdList);
                    // 行番号リスト
                    List<Integer> lineNoList = new ArrayList<>();
                    lineNoList.add(CommonConstants.NUMBER_0);
                    gazouIdByCriteriaInEntity.setLineNoList(lineNoList);
                    // DAOを実行
                    List<GazouIdOutEntity> kghTocGazouKanriRirekiSelectList = this.kghTocGazouKanriRirekiSelectMapper
                            .findGazouIdByCriteria(gazouIdByCriteriaInEntity);

                    // 3.1.3.2.2. 「3.1.3.2.1.」で取得画像履歴件数>0 且つ リクエストパラメータ.ケースリスト.画像IDは上記履歴情報に存在する場合、
                    if (!CollectionUtils.isNullOrEmpty(kghTocGazouKanriRirekiSelectList)
                            && kghTocGazouKanriRirekiSelectList.stream()
                                    .anyMatch(item -> CommonDtoUtil.checkStringEqual(caseInfo.getComputeGazouId(),
                                            CommonDtoUtil.objValToString(item.getGazouId())))) {
                        // 「3.1.3.2.1. の画像関連記録履歴情報」をループして、画像関連履歴情報をを削除処理実施（論理削除）

                        for (GazouIdOutEntity kanriRirekiOutEntity : kghTocGazouKanriRirekiSelectList) {
                            KghTocGazouKanriRirekiCriteria kghTocGazouKanriRirekiCriteria = new KghTocGazouKanriRirekiCriteria();
                            // 条件設定
                            kghTocGazouKanriRirekiCriteria.createCriteria()
                                    // 画像ID=「3.1.3.2.1. の画像関連記録履歴情報」.画像ID
                                    .andGazouIdEqualTo(kanriRirekiOutEntity.getGazouId())
                                    // 機能ID=「3.1.3.2.1. の画像関連記録履歴情報」.機能ID
                                    .andKinouIdEqualTo(kanriRirekiOutEntity.getKinouId())
                                    // 法人ID=「3.1.3.2.1. の画像関連記録履歴情報」.法人ID
                                    .andHoujinIdEqualTo(kanriRirekiOutEntity.getHoujinId())
                                    // 施設ID=「3.1.3.2.1. の画像関連記録履歴情報」.施設ID
                                    .andShisetuIdEqualTo(kanriRirekiOutEntity.getShisetuId())
                                    // 事業所ID=「3.1.3.2.1. の画像関連記録履歴情報」.事業所ID
                                    .andSvJigyoIdEqualTo(kanriRirekiOutEntity.getSvJigyoId())
                                    // 履歴番号=「3.1.3.2.1. の画像関連記録履歴情報」.レコード番号
                                    .andRecNoEqualTo(kanriRirekiOutEntity.getRecNo())
                                    // 利用者ID=「3.1.3.2.1. の画像関連記録履歴情報」.利用者ID
                                    .andUseridEqualTo(kanriRirekiOutEntity.getUserid())
                                    // 記録日=「3.1.3.2.1. の画像関連記録履歴情報」.記録日
                                    .andYymmYmdEqualTo(kanriRirekiOutEntity.getYymmYmd())
                                    // 行番号=「3.1.3.2.1. の画像関連記録履歴情報」.行番号
                                    .andLineNoEqualTo(kanriRirekiOutEntity.getLineNo())
                                    // 薬剤コード=「3.1.3.2.1. の画像関連記録履歴情報」.薬剤コード
                                    .andDrugIdEqualTo(kanriRirekiOutEntity.getDrugId());

                            int kghTocGazouKanriRirekiCount = kghTocGazouKanriRirekiMapper
                                    .deleteByCriteria(kghTocGazouKanriRirekiCriteria);
                            if (kghTocGazouKanriRirekiCount <= 0) {
                                LOG.info(Constants.END);
                                throw new ExclusiveException();
                            }
                        }
                    }
                }
            }
        }

        /*
         * ===============4.計画実施データ欄情報リスト情報の保存処理を実施===============
         * 
         */
        // 「リクエストパラメータ.計画実施データ欄情報リストの件数＞0件 且つ 実績更新フラグ=1」の場合
        if (!CollectionUtils.isNullOrEmpty(inDto.getPlanImplementationDataList())
                && CommonDtoUtil.checkStringEqual(inDto.getAchievementsUpdateFlag(), CommonConstants.UPDATE_FLG_1)) {
            // 4.1. リクエストパラメータ.計画実施データ欄情報リストを繰り返して、下記処理を行う。
            for (Gui01217PlanImplementationData planImplementationData : inDto
                    .getPlanImplementationDataList()) {
                // 4.1.1 計画実施データ欄情報リスト.更新区分=Uの場合、29-20 計画実施表データの情報を更新
                if (CommonDtoUtil.isUpdate(planImplementationData)) {
                    CpnTucKjisshi2 cpnTucKjisshi2 = new CpnTucKjisshi2();
                    Integer kbnCd = CommonDtoUtil.strValToInt(planImplementationData.getKbnCd());
                    switch (planImplementationData.getDmyJisshiChk()) {
                        case "day1":
                            cpnTucKjisshi2.setDay1(kbnCd);
                            break;

                        case "day2":
                            cpnTucKjisshi2.setDay2(kbnCd);
                            break;

                        case "day3":
                            cpnTucKjisshi2.setDay3(kbnCd);
                            break;

                        case "day4":
                            cpnTucKjisshi2.setDay4(kbnCd);
                            break;

                        case "day5":
                            cpnTucKjisshi2.setDay5(kbnCd);
                            break;

                        case "day6":
                            cpnTucKjisshi2.setDay6(kbnCd);
                            break;

                        case "day7":
                            cpnTucKjisshi2.setDay7(kbnCd);
                            break;

                        case "day8":
                            cpnTucKjisshi2.setDay8(kbnCd);
                            break;

                        case "day9":
                            cpnTucKjisshi2.setDay9(kbnCd);
                            break;

                        case "day10":
                            cpnTucKjisshi2.setDay10(kbnCd);
                            break;

                        case "day11":
                            cpnTucKjisshi2.setDay11(kbnCd);
                            break;

                        case "day12":
                            cpnTucKjisshi2.setDay12(kbnCd);
                            break;

                        case "day13":
                            cpnTucKjisshi2.setDay13(kbnCd);
                            break;

                        case "day14":
                            cpnTucKjisshi2.setDay14(kbnCd);
                            break;

                        case "day15":
                            cpnTucKjisshi2.setDay15(kbnCd);
                            break;

                        case "day16":
                            cpnTucKjisshi2.setDay16(kbnCd);
                            break;

                        case "day17":
                            cpnTucKjisshi2.setDay17(kbnCd);
                            break;

                        case "day18":
                            cpnTucKjisshi2.setDay18(kbnCd);
                            break;

                        case "day19":
                            cpnTucKjisshi2.setDay19(kbnCd);
                            break;

                        case "day20":
                            cpnTucKjisshi2.setDay20(kbnCd);
                            break;

                        case "day21":
                            cpnTucKjisshi2.setDay21(kbnCd);
                            break;

                        case "day22":
                            cpnTucKjisshi2.setDay22(kbnCd);
                            break;

                        case "day23":
                            cpnTucKjisshi2.setDay23(kbnCd);
                            break;

                        case "day24":
                            cpnTucKjisshi2.setDay24(kbnCd);
                            break;

                        case "day25":
                            cpnTucKjisshi2.setDay25(kbnCd);
                            break;

                        case "day26":
                            cpnTucKjisshi2.setDay26(kbnCd);
                            break;

                        case "day27":
                            cpnTucKjisshi2.setDay27(kbnCd);
                            break;

                        case "day28":
                            cpnTucKjisshi2.setDay28(kbnCd);
                            break;

                        case "day29":
                            cpnTucKjisshi2.setDay29(kbnCd);
                            break;

                        case "day30":
                            cpnTucKjisshi2.setDay30(kbnCd);
                            break;

                        case "day31":
                            cpnTucKjisshi2.setDay31(kbnCd);
                            break;
                    }

                    // 条件設定
                    CpnTucKjisshi2Criteria cpnTucKjisshi2Criteria = new CpnTucKjisshi2Criteria();
                    cpnTucKjisshi2Criteria.createCriteria()
                            .andKjisshi2IdEqualTo(
                                    CommonDtoUtil.strValToInt(planImplementationData.getKjisshi2Id()))
                            .andKjisshi1IdEqualTo(
                                    CommonDtoUtil.strValToInt(planImplementationData.getKjisshi1Id()));
                    int count = cpnTucKjisshi2Mapper.updateByCriteriaSelective(cpnTucKjisshi2, cpnTucKjisshi2Criteria);

                    if (count <= 0) {
                        LOG.info(Constants.END);
                        throw new ExclusiveException();
                    }
                }
            }
        }

        LOG.info(Constants.END);
        return out;
    }

}
