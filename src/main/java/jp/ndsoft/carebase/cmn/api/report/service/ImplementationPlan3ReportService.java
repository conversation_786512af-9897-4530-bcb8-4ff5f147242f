package jp.ndsoft.carebase.cmn.api.report.service;

import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.controllers.report.model.Jigyosyo;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnSypKkak232H21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnSypKkak232H21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnSypKkak23KekByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnSypKkak23KekOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaKihonOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMstChouhyouInkanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucSypKkak231SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucSypKkak232SelectMapper;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServiceDbNoSaveData;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServicePrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.PrintReportServicePrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ImplementationPlan3ReportServiceDataInfo;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.ShokuinInfoLogic;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.model.ImplementationPlan3ReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.report.dto.ImplementationPlan3ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ImplementationPlan3ReportServiceOutDto;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 * U0P152_（Ⅱ）実施計画～③(H21改訂版)
 * 
 * <AUTHOR>
 */
@Service("ImplementationPlan3Report")
public class ImplementationPlan3ReportService
        extends PdfReportServiceImpl<ImplementationPlan3ReportParameterModel, ImplementationPlan3ReportServiceOutDto> {

    /**
     * ロガー.
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 記入用シート区分(emptyFlg)が1:チェックONの場合、行番号（表示用）の固定値 */
    private static final String BANG_GOU = "　\r\n　\r\n　\r\n　\r\n　\r\n";
    /** 記入用シート区分(emptyFlg)が1:チェックONの場合、留意点の固定値 */
    private static final String RYUIKNJ = "　\r\n　\r\n";

    // 印鑑欄情報を取得
    @Autowired
    private CpnMstChouhyouInkanSelectMapper cpnMstChouhyouInkanSelectMapper;
    // 「総合的課題・目標・支援計画」情報を取得
    @Autowired
    private CpnTucSypKkak231SelectMapper cpnTucSypKkak231SelectMapper;
    // 「支援内容」リストを取得
    @Autowired
    private CpnTucSypKkak232SelectMapper cpnTucSypKkak232SelectMapper;
    /** 設定の書込（NEXのパソコン単位の設定） */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;
    // 利用者（年齢以外）情報取得
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;
    // 職員基本情報取得
    @Autowired
    private ShokuinInfoLogic shokuinInfoLogic;

    /**
     * 帳票出力
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final ImplementationPlan3ReportParameterModel inDto,
            final ImplementationPlan3ReportServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final ImplementationPlan3ReportServiceInDto reportParameter = (ImplementationPlan3ReportServiceInDto) getReportParameters(
                inDto, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                ReportUtil.getReportJrxmlFile(getFwProps(), ReportConstants.JRXML_U0P152_IMPLEMENTATIONPLAN3_H21));

        // コンパイル
        final JasperReport jasperFile = JasperCompileManager.compileReport(is);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile, reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(inDto, jasperPrint);

        super.setFilepath(inDto, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }

    /**
     * 帳票パラメータ取得
     * 
     * @param model  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(
            ImplementationPlan3ReportParameterModel inDto,
            ImplementationPlan3ReportServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);
        // 帳票用データ詳細
        ImplementationPlan3ReportServiceInDto reportInfo = getU0P152ReportParameters(inDto);
        // ノート情報格納配列
        List<ImplementationPlan3ReportServiceInDto> reportInfoList = new ArrayList<ImplementationPlan3ReportServiceInDto>();
        reportInfoList.add(reportInfo);
        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(reportInfoList);
        reportInfo.setDataSource(dataSource);

        LOG.info(Constants.END);
        return reportInfo;
    }

    /**
     * U0P152_（Ⅱ）実施計画～③(H21改訂版)の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータ
     */
    private ImplementationPlan3ReportServiceInDto getU0P152ReportParameters(
            ImplementationPlan3ReportParameterModel inDto) {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 実施計画①帳票情報
        ImplementationPlan3ReportServiceInDto reportInfo = new ImplementationPlan3ReportServiceInDto();

        // 2.1. 印鑑欄情報の取得
        getInkanInfo(inDto, reportInfo);

        // 2.2. 「総合的課題・目標・支援計画」情報の取得
        getRirekiInfo(inDto, reportInfo);

        // 2.3. 支援内容明細リストの取得
        getDataList(inDto, reportInfo);

        // データ.DB未保存画面項目.記入用シートを印刷するフラグ
        String inEmptyFlg = CommonConstants.BLANK_STRING;
        // パラメータ.データのDB未保存画面項目がありの場合
        PrintReportServiceDbNoSaveData inDbNoSaveData = inDto.getDbNoSaveData();
        if (inDbNoSaveData != null) {
            // 記入用シートを印刷するフラグ
            inEmptyFlg = inDbNoSaveData.getEmptyFlg();
        }

        // パラメータ.印刷設定
        PrintReportServicePrintSet inPrintSet = inDto.getPrintSet();
        // データ.印刷設定.日付表示有無
        String inPrnDate = CommonConstants.BLANK_STRING;
        // パラメータ.印刷設定がありの場合
        if (inPrintSet != null) {
            // 日付表示有無
            inPrnDate = inPrintSet.getPrnDate();
            // 帳票タイトル
            reportInfo.setTitle(inPrintSet.getPrtTitle());
            // 作成者表示区分
            reportInfo.setShokuNameFlg(inPrintSet.getParam05());
        }

        // 記入用シート区分
        reportInfo.setEmptyFlg(inEmptyFlg);

        // 印刷するフラグ(emptyFlg)が1:チェックONの場合 or 日付表示有無が「1:印刷しない」の場合、true
        boolean shouldHideDate = CommonConstants.STR_1.equals(inEmptyFlg) || CommonConstants.STR_1.equals(inPrnDate);

        // 日付表示区分
        if (shouldHideDate) {
            reportInfo.setBlankdateFlg(CommonConstants.STR_0);
        } else {
            reportInfo.setBlankdateFlg(CommonConstants.STR_1);
        }

        // 日付
        if (shouldHideDate) {
            reportInfo.setBlankdate(CommonConstants.BLANK_STRING);
        } else if (CommonConstants.STR_2.equals(inPrnDate)) {
            // 共通関数補足の「2.1.」の和暦日付:DB未保存画面項目.指定日
            reportInfo.setBlankdate(get2Gengouj(
                    inDbNoSaveData == null ? CommonConstants.BLANK_STRING : inDbNoSaveData.getSelectDate()));
        } else if (CommonConstants.STR_3.equals(inPrnDate)) {
            // 共通関数補足の「1.1.」の印刷用日付:システム日付
            reportInfo.setBlankdate(nds3GkFunc01Logic.blankDate(inDto.getAppYmd()));
        }

        // 事業所名
        reportInfo.setJigyoKnj(inDto.getJigyoKnj());

        // 氏名
        // データ.記入用シートを印刷するフラグ(emptyFlg)が1:チェックONの場合、空白
        if (CommonConstants.STR_1.equals(inEmptyFlg)) {
            reportInfo.setFullName(CommonConstants.BLANK_STRING);
        } else if (inDto.getPrintSubjectHistory() != null) {
            // 印刷対象履歴.利用者IDの氏名
            reportInfo.setFullName(getFullName(CommonDtoUtil.objValToString(
                    inDto.getPrintSubjectHistory().getUserId())));
        }

        // 敬称
        // ・データ.印刷設定.パラメータ03が1:チェックONの場合
        if (inPrintSet != null && CommonConstants.STR_1.equals(inPrintSet.getParam03())) {
            // 敬称 ← データ.印刷設定.パラメータ04
            reportInfo.setKeisho(inPrintSet.getParam04());
        } else if (inDto.getInitMasterObj() != null
                && CommonConstants.STR_1.equals(inDto.getInitMasterObj().getKeishoFlg())) {
            // ・データ.初期設定マスタの情報.敬称オプションが1:チェックONの場合、敬称 ← データ.初期設定マスタの情報.敬称 //
            reportInfo.setKeisho(inDto.getInitMasterObj().getKeishoKnj());
        } else {
            // 以外の場合、"殿"
            reportInfo.setKeisho(CommonConstants.KEISHO_STR_TONO);
        }

        // ページング表示区分:印刷オプション.記入用シートを印刷するフラグが1:チェックONの場合、0:非表示
        reportInfo.setPagingFlg(CommonConstants.STR_1.equals(inEmptyFlg)
                ? CommonConstants.STR_0
                : CommonConstants.STR_1);

        return reportInfo;
    }

    /**
     * 印鑑欄情報の取得
     * 
     * @param inDto      入力データ
     * @param reportInfo 帳票用データ詳細
     */
    private void getInkanInfo(
            ImplementationPlan3ReportParameterModel inDto,
            ImplementationPlan3ReportServiceInDto reportInfo) {

        // 記入用シートを印刷するフラグ(emptyFlg)が1:チェックONの場合、印鑑欄表示区分に0:非表示を設定、印鑑1～印鑑15に""を設定する
        PrintReportServiceDbNoSaveData dbNoSaveData = inDto.getDbNoSaveData();
        if (CommonConstants.STR_1.equals(dbNoSaveData != null ? dbNoSaveData.getEmptyFlg() : null)) {
            // 印鑑欄表示区分に0:非表示を設定、印鑑1～印鑑15に""を設定する
            setDefaultInkanInfo(reportInfo);
            return;
        }
        // 検索条件がなし、印鑑欄情報の取得終了
        Jigyosyo jigyoDataInfo = inDto.getJigyoInfo();
        if (jigyoDataInfo == null
                || jigyoDataInfo.getHoujinId().isEmpty()
                || jigyoDataInfo.getShisetuId().isEmpty()
                || jigyoDataInfo.getSvJigyoId().isEmpty()) {
            return;
        }

        // リクエストパラメータ.記入用シートを印刷するフラグが0:チェックOFFの場合、下記の帳票データ情報を取得する
        // 印鑑欄情報の取得
        KghCpnMstChouhyouInkanPrnByCriteriaInEntity entity = new KghCpnMstChouhyouInkanPrnByCriteriaInEntity();
        // 法人ID
        entity.setAnKey1(CommonDtoUtil.strValToInt(jigyoDataInfo.getHoujinId()));
        // 施設ID
        entity.setAnKey2(CommonDtoUtil.strValToInt(jigyoDataInfo.getShisetuId()));
        // 事業所ID
        entity.setAnKey3(CommonDtoUtil.strValToInt(jigyoDataInfo.getSvJigyoId()));
        // 帳票セクション番号
        entity.setAsSec(ReportConstants.REPORT_CODE_IMPLEMENTATION_PLAN3);
        // 印鑑欄情報
        List<KghCpnMstChouhyouInkanPrnOutEntity> list = cpnMstChouhyouInkanSelectMapper
                .findKghCpnMstChouhyouInkanPrnByCriteria(entity);
        if (list == null || list.size() == 0) {
            return;
        }

        // 印鑑欄情報の取得
        KghCpnMstChouhyouInkanPrnOutEntity inkanData = list.get(0);
        // 印鑑欄表示区分
        reportInfo.setHyoujiKbn(CommonDtoUtil.objValToString(inkanData.getHyoujiKbn()));
        // 印鑑1
        reportInfo.setHanko1Knj(inkanData.getHanko1Knj());
        // 印鑑2
        reportInfo.setHanko2Knj(inkanData.getHanko2Knj());
        // 印鑑3
        reportInfo.setHanko3Knj(inkanData.getHanko3Knj());
        // 印鑑4
        reportInfo.setHanko4Knj(inkanData.getHanko4Knj());
        // 印鑑5
        reportInfo.setHanko5Knj(inkanData.getHanko5Knj());
        // 印鑑6
        reportInfo.setHanko6Knj(inkanData.getHanko6Knj());
        // 印鑑7
        reportInfo.setHanko7Knj(inkanData.getHanko7Knj());
        // 印鑑8
        reportInfo.setHanko8Knj(inkanData.getHanko8Knj());
        // 印鑑9
        reportInfo.setHanko9Knj(inkanData.getHanko9Knj());
        // 印鑑10
        reportInfo.setHanko10Knj(inkanData.getHanko10Knj());
        // 印鑑11
        reportInfo.setHanko11Knj(inkanData.getHanko11Knj());
        // 印鑑12
        reportInfo.setHanko12Knj(inkanData.getHanko12Knj());
        // 印鑑13
        reportInfo.setHanko13Knj(inkanData.getHanko13Knj());
        // 印鑑14
        reportInfo.setHanko14Knj(inkanData.getHanko14Knj());
        // 印鑑15
        reportInfo.setHanko15Knj(inkanData.getHanko15Knj());
    }

    /**
     * 印鑑欄表示区分に0:非表示を設定、印鑑1～印鑑15に""を設定する
     * 
     * @param inDto      入力データ
     * @param reportInfo 帳票用データ詳細
     */
    private void setDefaultInkanInfo(ImplementationPlan3ReportServiceInDto reportInfo) {
        // 印鑑欄表示区分
        reportInfo.setHyoujiKbn(CommonConstants.STR_0);
        // 印鑑1
        reportInfo.setHanko1Knj(CommonConstants.BLANK_STRING);
        // 印鑑2
        reportInfo.setHanko2Knj(CommonConstants.BLANK_STRING);
        // 印鑑3
        reportInfo.setHanko3Knj(CommonConstants.BLANK_STRING);
        // 印鑑4
        reportInfo.setHanko4Knj(CommonConstants.BLANK_STRING);
        // 印鑑5
        reportInfo.setHanko5Knj(CommonConstants.BLANK_STRING);
        // 印鑑6
        reportInfo.setHanko6Knj(CommonConstants.BLANK_STRING);
        // 印鑑7
        reportInfo.setHanko7Knj(CommonConstants.BLANK_STRING);
        // 印鑑8
        reportInfo.setHanko8Knj(CommonConstants.BLANK_STRING);
        // 印鑑9
        reportInfo.setHanko9Knj(CommonConstants.BLANK_STRING);
        // 印鑑10
        reportInfo.setHanko10Knj(CommonConstants.BLANK_STRING);
        // 印鑑11
        reportInfo.setHanko11Knj(CommonConstants.BLANK_STRING);
        // 印鑑12
        reportInfo.setHanko12Knj(CommonConstants.BLANK_STRING);
        // 印鑑13
        reportInfo.setHanko13Knj(CommonConstants.BLANK_STRING);
        // 印鑑14
        reportInfo.setHanko14Knj(CommonConstants.BLANK_STRING);
        // 印鑑15
        reportInfo.setHanko15Knj(CommonConstants.BLANK_STRING);
    }

    /**
     * 履歴情報の関連情報の取得
     * 
     * @param inDto      入力データ
     * @param reportInfo 帳票用データ詳細
     */
    private void getRirekiInfo(
            ImplementationPlan3ReportParameterModel inDto,
            ImplementationPlan3ReportServiceInDto reportInfo) {
        // 履歴情報の関連情報に空白を設定
        setDefaultRirekiInfo(reportInfo);

        PrintReportServiceDbNoSaveData dbNoSaveData = inDto.getDbNoSaveData();
        // 記入用シートを印刷するフラグ(emptyFlg)が1:チェックON以外の場合
        if (!CommonConstants.STR_1.equals(dbNoSaveData != null ? dbNoSaveData.getEmptyFlg() : null)) {

            // 留意点
            reportInfo.setRyuiKnj(CommonConstants.BLANK_STRING);

            // 検索条件がなし、総合的課題情報の取得終了
            PrintReportServicePrintSubjectHistory printSubjectHistory = inDto.getPrintSubjectHistory();
            if (printSubjectHistory != null
                    && !printSubjectHistory.getRirekiId().isEmpty()) {
                // リクエストパラメータ.記入用シートを印刷するフラグが0:チェックOFFの場合、下記の帳票データ情報を取得する
                // 総合的課題情報の取得
                CpnSypKkak23KekByCriteriaInEntity entity = new CpnSypKkak23KekByCriteriaInEntity();
                // 実施計画書～③ヘッダＩＤ
                entity.setRireki(CommonDtoUtil.strValToInt(printSubjectHistory.getRirekiId()));
                // 「総合的課題・目標・支援計画」情報
                List<CpnSypKkak23KekOutEntity> list = cpnTucSypKkak231SelectMapper
                        .findCpnSypKkak23KekByCriteria(entity);
                if (list != null && list.size() > 0) {
                    // 「総合的課題・目標・支援計画」情報の取得
                    CpnSypKkak23KekOutEntity data = list.get(0);
                    // 総合的課題（運動・移動）
                    reportInfo.setKadaiUndoKnj(CommonDtoUtil.strNullToEmpty(data.getKadaiUndoKnj()));
                    // 総合的課題（日常生活）
                    reportInfo.setKadaiNichijoKnj(CommonDtoUtil.strNullToEmpty(data.getKadaiNichijoKnj()));
                    // 総合的課題（社会参加対人関係）
                    reportInfo.setKadaiShakaiKnj(CommonDtoUtil.strNullToEmpty(data.getKadaiShakaiKnj()));
                    // 総合的課題（健康管理）
                    reportInfo.setKadaiKenkoKnj(CommonDtoUtil.strNullToEmpty(data.getKadaiKenkoKnj()));
                    // 目標
                    reportInfo.setMokuhyoKnj(CommonDtoUtil.strNullToEmpty(data.getMokuhyoKnj()));
                    // 支援計画
                    reportInfo.setShienKeikakuKnj(CommonDtoUtil.strNullToEmpty(data.getShienKeikakuKnj()));
                    // 留意点
                    reportInfo.setRyuiKnj(CommonDtoUtil.strNullToEmpty(data.getRyuiKnj()));
                    // 障害加算フラグ
                    reportInfo.setShogaiKasanFlg(CommonDtoUtil.objValToString(data.getShogaiKasanFlg()));
                    // 改訂フラグ
                    reportInfo.setKaiteiFlg(CommonDtoUtil.objValToString(data.getKaiteiFlg()));

                    // 作成者
                    PrintReportServicePrintSet inPrintSet = inDto.getPrintSet();
                    // データ.印刷設定.パラメータ05が1:チェックONの場合
                    if (inPrintSet != null && CommonConstants.STR_1.equals(inPrintSet.getParam05())) {
                        // API定義の処理「2.4.1. 」の職員名（姓） ＋ " " ＋ 職員名（名）
                        reportInfo.setShokuName(
                                shokuinInfoLogic.getShokuNameKnj(CommonDtoUtil.objValToString(data.getShokuId())));
                    }

                    // 作成日:共通関数補足の「2.1.」の和暦日付
                    reportInfo.setCreateYmd(get2Gengouj(data.getCreateYmd()));
                    // ケースNo.
                    reportInfo.setCaseNo(CommonDtoUtil.strNullToEmpty(data.getCaseNo()));

                    // 初回作成日:共通関数補足の「2.1.」の和暦日付 ※共通関数の戻り値が""の場合、" 年  月  日"を設定する
                    String shokaiYmd = get2Gengouj(data.getShokaiYmd());
                    if (CommonConstants.BLANK_STRING.equals(shokaiYmd)) {
                        shokaiYmd = CommonConstants.REPORT_YMD_DEFAUL_STRING;
                    }
                    reportInfo.setShokaiYmd(shokaiYmd);
                }
            }
        }
    }

    /**
     * 履歴情報の関連情報に空白を設定
     * 
     * @param reportInfo 帳票用データ詳細
     */
    private void setDefaultRirekiInfo(ImplementationPlan3ReportServiceInDto reportInfo) {
        // 留意点
        reportInfo.setRyuiKnj(RYUIKNJ);
        // 作成日
        reportInfo.setCreateYmd(CommonConstants.REPORT_YMD_DEFAUL_STRING);
        // 初回作成日
        reportInfo.setShokaiYmd(CommonConstants.REPORT_YMD_DEFAUL_STRING);
    }

    /**
     * 明細リストを取得する
     * 
     * @param inDto      入力データ
     * @param reportInfo 実施計画①帳票情報
     */
    private void getDataList(
            ImplementationPlan3ReportParameterModel inDto,
            ImplementationPlan3ReportServiceInDto reportInfo) {
        // データ.記入用シートを印刷するフラグ(emptyFlg)が1:チェックONの場合
        if (inDto.getDbNoSaveData() != null
                && CommonConstants.STR_1.equals(inDto.getDbNoSaveData().getEmptyFlg())) {
            // 支援内容明細リストに3件仮データを追加する。データの各項目が空を設定する
            setDefaultDataList(reportInfo);
            return;
        }

        // 明細リスト
        List<ImplementationPlan3ReportServiceDataInfo> dataList = new ArrayList<>();

        // 印刷対象履歴がなし又は検索条件がなし、履歴情報の取得終了
        PrintReportServicePrintSubjectHistory printSubjectHistory = inDto.getPrintSubjectHistory();
        if (printSubjectHistory != null && printSubjectHistory.getRirekiId() != null
                && !printSubjectHistory.getRirekiId().isEmpty()) {
            // 明細リストの取得
            CpnSypKkak232H21ByCriteriaInEntity entity = new CpnSypKkak232H21ByCriteriaInEntity();
            // 実施計画書～①ヘッダＩＤ
            entity.setRireki(CommonDtoUtil.strValToInt(printSubjectHistory.getRirekiId()));
            // 「支援内容明細」リストを取得する
            List<CpnSypKkak232H21OutEntity> dataInfoList = cpnTucSypKkak232SelectMapper
                    .findCpnSypKkak232H21ByCriteria(entity);
            if (dataInfoList != null && dataInfoList.size() > 0) {
                // 帳票表示用明細リスト ＝ API定義の処理「2.3.」のアウトプットを参照
                for (CpnSypKkak232H21OutEntity data : dataInfoList) {
                    ImplementationPlan3ReportServiceDataInfo outDataInfo = new ImplementationPlan3ReportServiceDataInfo();
                    // 行番号（表示用）
                    outDataInfo.setBangou(CommonDtoUtil.strNullToEmpty(CommonDtoUtil.objValToString(data.getBangou())));
                    // サービス内容
                    outDataInfo.setSvNaiyoKnj(data.getSvNaiyoKnj());
                    // サービス種別
                    outDataInfo.setSvShuKnj(data.getSvShuKnj());
                    // 提供事業所名
                    outDataInfo.setJigyoNameKnj(data.getJigyoNameKnj());
                    // 頻度及び期間
                    outDataInfo.setKikanKnj(data.getKikanKnj());
                    dataList.add(outDataInfo);
                }
            }
        }

        JRBeanCollectionDataSource modelDataList = new JRBeanCollectionDataSource(dataList);
        reportInfo.setDataList(modelDataList);
    }

    /**
     * 帳票表示用明細リストに3件仮データを追加する。データの各項目が空を設定する
     * 
     * @param reportInfo 帳票用データ詳細
     * 
     */
    private void setDefaultDataList(ImplementationPlan3ReportServiceInDto reportInfo) {
        // 明細リスト
        List<ImplementationPlan3ReportServiceDataInfo> dataList = new ArrayList<>(3);
        // 帳票表示用明細リストに3件仮データを追加する。データの各項目が空を設定する
        for (int i = 0; i < 3; i++) {
            ImplementationPlan3ReportServiceDataInfo outDataInfo = new ImplementationPlan3ReportServiceDataInfo();
            // 行番号（表示用）
            outDataInfo.setBangou(BANG_GOU);
            dataList.add(outDataInfo);
        }
        JRBeanCollectionDataSource modelDataList = new JRBeanCollectionDataSource(dataList);
        reportInfo.setDataList(modelDataList);
    }

    /**
     * 利用者IDの氏名の取得
     * 
     * @param userId 利用者ID
     */
    private String getFullName(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return CommonConstants.BLANK_STRING;
        }
        String fullName = CommonConstants.BLANK_STRING;
        // 利用者（年齢以外）情報取得
        RiyoshaKihonByCriteriaInEntity entity = new RiyoshaKihonByCriteriaInEntity();
        // 利用者ID
        entity.setAlUserid(CommonDtoUtil.strValToInt(userId));
        List<RiyoshaKihonOutEntity> userList = comTucUserSelectMapper.findRiyoshaKihonByCriteria(entity);
        if (userList != null && userList.size() > 0) {
            RiyoshaKihonOutEntity data = userList.get(0);
            fullName = data.getName1Knj() + CommonConstants.BLANK_SPACE + data.getName2Knj();
        }
        return fullName;
    }

    /**
     * 和暦変換処理
     * 共通関数補足の和暦日付
     * 
     * @param ymd 年月日
     */
    private String get2Gengouj(String ymd) {
        String dateValue = (ymd != null && !ymd.trim().isEmpty()) ? ymd : CommonConstants.BLANK_STRING;
        return nds3GkFunc01Logic.get2Gengouj(CommonConstants.NUMBER_ONE, dateValue);
    }
}
