package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.carebase.framework.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.05.13
 * <AUTHOR> 盧青陽
 * @description GUI00905_課題立案様式タイトルマスタ画面のEntity
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class Gui00905IssuesPlanningStyleTitleMasterUpdateData implements Serializable, IEntity {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 事業所ID */
    @NotEmpty
    private String svJigyoId;

    /** マスタヘッダID */
    @NotEmpty
    private String free1Id;

    /** フリーID */
    @NotEmpty
    private String dmyFree1Id;

    /** 機能区分 */
    @NotEmpty
    private String kinouKbn;

    /** 様式区分 */
    @NotEmpty
    private String youshikiKbn;

    /** 帳票タイトル */
    @NotEmpty
    private String titleKnj;

    /** 行の分割数（上） */
    @NotEmpty
    private String columnCount;

    /** 初期表示の行の分割数（上） */
    @NotEmpty
    private String initColumnCount;

    /** 表示順 */
    @NotEmpty
    private String sort;

    /** 印刷文字サイズ */
    @NotEmpty
    private String fontSize;

    /** 用紙サイズ */
    @NotEmpty
    private String youshiSize;

    /** 自由に使用 */
    private String freeKbn;

    /** 切替区分 */
    private String kirikaeKbn;

    /** 適用フラグ */
    private String tekiyouFlg;

    /** アップデートフラグ */
    private String defUpdateFlg;

    /** アップデートフラグ1 */
    private String defUpdateFlg1;

    /** 更新区分 */
    private String updateKbn;
}
