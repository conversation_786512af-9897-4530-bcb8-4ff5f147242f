package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import jp.ndsoft.smh.framework.global.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GUI00779_アセスメント(インターライ)画面O
 * 
 * @description
 *              サブ情報（O）
 *              サブ情報（O）エンティティ
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GUI00779SubInfoO implements Serializable, IEntity {

    /** アセスメントID */
    private String raiId;
    /**
     * 健診予防接種_血圧
     */
    private String o1A;
    /**
     * 健診予防接種_大腸内視鏡
     */
    private String o1B;
    /**
     * 健診予防接種_歯科
     */
    private String o1C;
    /**
     * 健診予防接種_眼科
     */
    private String o1D;
    /**
     * 健診予防接種_聴力
     */
    private String o1E;
    /**
     * 健診予防接種_インフルエンザ
     */
    private String o1F;
    /**
     * 健診予防接種_マンモグラフィー
     */
    private String o1G;
    /**
     * 健診予防接種_肺炎
     */
    private String o1H;
    /**
     * 治療ケア_治療_抗がん剤
     */
    private String o2A;
    /**
     * 治療ケア_治療_透析
     */
    private String o2B;
    /**
     * 治療ケア_治療_感染
     */
    private String o2C;
    /**
     * 治療ケア_治療_薬物投与
     */
    private String o2D;
    /**
     * 治療ケア_治療_酸素
     */
    private String o2E;
    /**
     * 治療ケア_治療_放射線
     */
    private String o2F;
    /**
     * 治療ケア_治療_吸引
     */
    private String o2G;
    /**
     * 治療ケア_治療_気管切開口
     */
    private String o2H;
    /**
     * 治療ケア_治療_輸血
     */
    private String o2I;
    /**
     * 治療ケア_治療_人工呼吸器
     */
    private String o2J;
    /**
     * 治療ケア_治療_創のケア
     */
    private String o2K;
    /**
     * 治療ケア_プログラム_トイレ
     */
    private String o2L;
    /**
     * 治療ケア_プログラム_緩和
     */
    private String o2M;
    /**
     * 治療ケア_プログラム_体位
     */
    private String o2N;
    /**
     * 訪問介護_実施日数(A)
     */
    private String o3AA;
    /**
     * 訪問介護_分数(B)
     */
    private String o3AB;
    /**
     * 訪問看護_実施日数(A)
     */
    private String o3BA;
    /**
     * 訪問看護_分数(B)
     */
    private String o3BB;
    /**
     * 通所介護リハ_実施日数(A)
     */
    private String o3CA;
    /**
     * 食事/配食_実施日数(A)
     */
    private String o3DA;
    /**
     * リハ_理学_計画日数(A)
     */
    private String o4AA;
    /**
     * リハ_理学_実施日数(B)
     */
    private String o4AB;
    /**
     * リハ_理学_分数(C)
     */
    private String o4AC;
    /**
     * リハ_作業_計画日数(A)
     */
    private String o4BA;
    /**
     * リハ_作業_実施日数(B)
     */
    private String o4BB;
    /**
     * リハ_作業_分数(C)
     */
    private String o4BC;
    /**
     * リハ_言語_計画日数(A)
     */
    private String o4CA;
    /**
     * リハ_言語_実施日数(B)
     */
    private String o4CB;
    /**
     * リハ_言語_分数(C)
     */
    private String o4CC;
    /**
     * リハ_心理_計画日数(A)
     */
    private String o4DA;
    /**
     * リハ_心理_実施日数(B)
     */
    private String o4DB;
    /**
     * リハ_心理_分数(C)
     */
    private String o4DC;
    /**
     * リハ_呼吸_計画日数(A)
     */
    private String o4EA;
    /**
     * リハ_呼吸_実施日数(B)
     */
    private String o4EB;
    /**
     * リハ_呼吸_分数(C)
     */
    private String o4EC;
    /**
     * リハ_訓練_計画日数(A)
     */
    private String o4FA;
    /**
     * リハ_訓練_実施日数(B)
     */
    private String o4FB;
    /**
     * リハ_訓練_分数(C)
     */
    private String o4FC;
    /**
     * a.入院
     */
    private String o5A;
    /**
     * b.救急外来
     */
    private String o5B;
    /**
     * c.医師の診察
     */
    private String o5C;
    /**
     * 受診
     */
    private String o6;
    /**
     * 医師の指示変更
     */
    private String o7;
    /**
     * 身体抑制
     */
    private String o8A;
    /**
     * 身体抑制_ベッド柵
     */
    private String o8B;
    /**
     * 身体抑制_体幹部
     */
    private String o8C;
    /**
     * 身体抑制_立ち上がり
     */
    private String o8D;
    /**
     * o1_メモ
     */
    private String o1MemoKnj;
    /**
     * o1_メモフォント
     */
    private String o1MemoFont;
    /**
     * o1_メモ色
     */
    private String o1MemoColor;
    /**
     * o2_メモ
     */
    private String o2MemoKnj;
    /**
     * o2_メモフォント
     */
    private String o2MemoFont;
    /**
     * o2_メモ色
     */
    private String o2MemoColor;
    /**
     * o3_メモ
     */
    private String o3MemoKnj;
    /**
     * o3_メモフォント
     */
    private String o3MemoFont;
    /**
     * o3_メモ色
     */
    private String o3MemoColor;
    /**
     * o4_メモ
     */
    private String o4MemoKnj;
    /**
     * o4_メモフォント
     */
    private String o4MemoFont;
    /**
     * o4_メモ色
     */
    private String o4MemoColor;
    /**
     * o5_メモ
     */
    private String o5MemoKnj;
    /**
     * o5_メモフォント
     */
    private String o5MemoFont;
    /**
     * o5_メモ色
     */
    private String o5MemoColor;
    /**
     * o6_メモ
     */
    private String o6MemoKnj;
    /**
     * o6_メモフォント
     */
    private String o6MemoFont;
    /**
     * o6_メモ色
     */
    private String o6MemoColor;
    /**
     * o7_メモ
     */
    private String o7MemoKnj;
    /**
     * o7_メモフォント
     */
    private String o7MemoFont;
    /**
     * o7_メモ色
     */
    private String o7MemoColor;
    /**
     * o8_メモ
     */
    private String o8MemoKnj;
    /**
     * o8_メモフォント
     */
    private String o8MemoFont;
    /**
     * o8_メモ色
     */
    private String o8MemoColor;
}
