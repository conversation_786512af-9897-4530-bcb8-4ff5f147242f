package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01048Jgyou;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI01048_有効期間外サービス検索_集計の入力Dto
 * 
 * <AUTHOR> 李晨昊
 */
@Getter
@Setter
public class ValidWithOutPeriodServiceSelectServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 職員ID */
    private String syokuinId;
    /** システムコード */
    private String sysCd;
    /** 事業所ID */
    private String jgyouId;
    /** 機能ID */
    private String kinouId;
    /** 権限チェック高速化フラグ */
    private String kenFlg;
    /** 支援事業者リスト */
    private List<Gui01048Jgyou> jgyouLst;
    /** 処理期間（From） */
    private String syoriYmdFrom;
    /** 処理期間（To） */
    private String syoriYmdTo;
}