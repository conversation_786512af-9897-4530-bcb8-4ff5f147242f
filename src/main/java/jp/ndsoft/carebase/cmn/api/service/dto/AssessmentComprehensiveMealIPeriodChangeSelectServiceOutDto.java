package jp.ndsoft.carebase.cmn.api.service.dto;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00834PlanPeriodInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00834HistoryInfo;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.05.13
 * <AUTHOR>
 * @apiNote GUI00834_［アセスメント（包括）］画面 計画期間変更サービス出力Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AssessmentComprehensiveMealIPeriodChangeSelectServiceOutDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;
    /** 計画期間情報 */
    private Gui00834PlanPeriodInfo planPeriodInfo;
    /** 履歴情報 */
    private Gui00834HistoryInfo historyInfo;
    /** 期間管理フラグ */
    private String kikanKanriFlg;
    /** フラグ */
    private String errKbn;
    /** 種別ID */
    private String syubetsuId;

}
