package jp.ndsoft.carebase.cmn.api.service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI01149_利用票
 * 
 * @description
 *              別表明細情報
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class Gui01149RiyouBeppyo extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 事業所名 */
    private String dmyJigyoNameKnj;

    /** 事業所番号 */
    private String dmyJigyoNumber;

    /** サービス内容 */
    private String dmyFormalnameKnj;

    /** サービスコード */
    @JsonProperty("cScode")
    private String scode;

    /** サービス単位数 */
    private String tensu;

    /** 割引率 */
    private String waribikiRitu;

    /** 割引後単位数 */
    private String waribikiTen;

    /** 回数 */
    private String kaisu;

    /** 合計単位数 */
    private String svTensu;

    /** 合計単位数_hidden */
    @JsonProperty("cSvTensu")
    private String cSvTensu;

    /** 種類限度外単位数 */
    @JsonProperty("sTensuOver")
    private String sTensuOver;

    /** 種類限度外単位数_hidden */
    @JsonProperty("cSTensuOver")
    private String cSTensuOver;

    /** 種類限度額内単位数 */
    @JsonProperty("sTensu")
    private String sTensu;

    /** 種類限度額内単位数_hidden */
    @JsonProperty("cSTensu")
    private String cSTensu;

    /** 区分支給限度外単位数 */
    @JsonProperty("kTensuOver")
    private String kTensuOver;

    /** 区分支給限度外単位数_hidden */
    @JsonProperty("cKTensuOver")
    private String cKTensuOver;

    /** 区分支給限度額内単位数 */
    @JsonProperty("kTensu")
    private String kTensu;

    /** 区分支給限度額内単位数_hidden */
    @JsonProperty("cKTensu")
    private String cKTensu;

    /** 単位数単価 */
    private String tanka;

    /** 費用総額 */
    private String hiyouSougaku;

    /** 費用総額_hidden */
    @JsonProperty("cHiyouSougaku")
    private String cHiyouSougaku;

    /** 給付率 */
    private String kyufuRitu;

    /** 介護保険給付額 */
    @JsonProperty("hKyufugaku")
    private String hKyufugaku;

    /** 介護保険給付額_hidden */
    @JsonProperty("cHKyufugaku")
    private String cHKyufugaku;

    /** 定額利用者負担単価金額 */
    @JsonProperty("tHutanTanka")
    private String tHutanTanka;

    /** 利用者負担額・保険給付対象分 */
    private String hutanH;

    /** 利用者負担額・保険給付対象分_hidden */
    @JsonProperty("cHutanH")
    private String cHutanH;

    /** 利用者負担額・全額自己負担分 */
    private String hutanJ;

    /** 支援事業者ID */
    private String shienId;

    /** 利用者ID */
    private String userid;

    /** サービス提供年月 */
    private String yymmYm;

    /** サービス提供年月（変更日） */
    private String yymmD;

    /** サービス事業者ID */
    private String svJigyoId;

    /** サービス項目ID */
    private String svItemCd;

    /** 枝番 */
    private String edaNo;

    /** サービス種類CD */
    private String svType;

    /** サービス項目CD */
    private String svcode;

    /** 合計フラグ */
    private String totalF;

    /** 加算フラグ */
    private String kasanF;

    /** ３０日超過フラグ */
    private String ov30Fl;

    /** 給付率差異フラグ */
    private String kyufuDiffF;

    /** 計算ソート用 */
    private String dmyCalcSort;

    /** ソート用サービス種類CD */
    private String dmySvtype;

    /** 給付管理単位数 */
    private String kyufukanriTen;

    /** 総合サービス区分 */
    @JsonProperty("cSougouKbn")
    private String cSougouKbn;

    /** 利用票別表明細更新区分 */
    private String riyouBeppyoListUpdKbn;

    /** レンタルフラグ */
    private String isRentalFlag;

    /** 単位数単価表示フラグ */
    private String tankaDisp;

    /** 福祉用具貸与マスタID */
    private String fygId;

    /** 用具名称 */
    private String fygShouhinKnj;

    /** TAISコード */
    private String fygTekiyouCode;
}
