package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;

import jp.ndsoft.smh.framework.global.base.entity.IEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GUI00774_アセスメント(インターライ)画面J
 * 
 * @description
 *              サブ情報（J）
 *              サブ情報（J）エンティティ
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GUI00774SubInfoJ implements Serializable, IEntity {

    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;
    /** アセスメントID */
    private String raiId;
    /** 転倒 */
    private String j1;
    /** 最近の転倒 */
    private String j2;
    /** 問題頻度_バランス_立位 */
    private String j3A;
    /** 問題頻度_バランス_方向転換 */
    private String j3B;
    /** 問題頻度_バランス_めまい */
    private String j3C;
    /** 問題頻度_バランス_歩行 */
    private String j3D;
    /** 問題頻度_心肺_胸痛 */
    private String j3E;
    /** 問題頻度_心肺_気道 */
    private String j3F;
    /** 問題頻度_精神_思考 */
    private String j3G;
    /** 問題頻度_精神_妄想 */
    private String j3H;
    /** 問題頻度_精神_幻覚 */
    private String j3I;
    /** 問題頻度_神経_失語症 */
    private String j3J;
    /** 問題頻度_消化器系_胃酸 */
    private String j3K;
    /** 問題頻度_消化器系_便秘 */
    private String j3L;
    /** 問題頻度_消化器系_下痢 */
    private String j3M;
    /** 問題頻度_消化器系_嘔吐 */
    private String j3N;
    /** 問題頻度_睡眠障害_入眠 */
    private String j3O;
    /** 問題頻度_睡眠障害_過多 */
    private String j3P;
    /** 問題頻度_その他_誤嚥 */
    private String j3Q;
    /** 問題頻度_その他_発熱 */
    private String j3R;
    /** 問題頻度_その他_出血 */
    private String j3S;
    /** 問題頻度_その他_不衛生 */
    private String j3T;
    /** 問題頻度_その他_末梢浮腫 */
    private String j3U;
    /** 呼吸困難 */
    private String j4;
    /** 疲労感 */
    private String j5;
    /** 痛み_頻度 */
    private String j6A;
    /** 痛み_程度 */
    private String j6B;
    /** 痛み_持続性 */
    private String j6C;
    /** 痛み_突発痛 */
    private String j6D;
    /** 痛み_コントロール */
    private String j6E;
    /** 状態の不安定性_症状 */
    private String j7A;
    /** 状態の不安定性_発性 */
    private String j7B;
    /** 状態の不安定性_末期の疾患 */
    private String j7C;
    /** 主観的健康感 */
    private String j8;
    /** 喫煙と飲酒_喫煙 */
    private String j9A;
    /** 喫煙と飲酒_飲酒 */
    private String j9B;
    /** j1_メモ */
    private String j1MemoKnj;
    /** j1_メモフォント */
    private String j1MemoFont;
    /** j1_メモ色 */
    private String j1MemoColor;
    /** j2_メモ */
    private String j2MemoKnj;
    /** j2_メモフォント */
    private String j2MemoFont;
    /** j2_メモ色 */
    private String j2MemoColor;
    /** j3_メモ */
    private String j3MemoKnj;
    /** j3_メモフォント */
    private String j3MemoFont;
    /** j3_メモ色 */
    private String j3MemoColor;
    /** j4_メモ */
    private String j4MemoKnj;
    /** j4_メモフォント */
    private String j4MemoFont;
    /** j4_メモ色 */
    private String j4MemoColor;
    /** j5_メモ */
    private String j5MemoKnj;
    /** j5_メモフォント */
    private String j5MemoFont;
    /** j5_メモ色 */
    private String j5MemoColor;
    /** j6_a_メモ */
    private String j6AMemoKnj;
    /** j6_a_メモフォント */
    private String j6AMemoFont;
    /** j6_a_メモ色 */
    private String j6AMemoColor;
    /** j6_b_メモ */
    private String j6BMemoKnj;
    /** j6_b_メモフォント */
    private String j6BMemoFont;
    /** j6_b_メモ色 */
    private String j6BMemoColor;
    /** j6_c_メモ */
    private String j6CMemoKnj;
    /** j6_c_メモフォント */
    private String j6CMemoFont;
    /** j6_c_メモ色 */
    private String j6CMemoColor;
    /** j6_d_メモ */
    private String j6DMemoKnj;
    /** j6_d_メモフォント */
    private String j6DMemoFont;
    /** j6_d_メモ色 */
    private String j6DMemoColor;
    /** j6_e_メモ */
    private String j6EMemoKnj;
    /** j6_e_メモフォント */
    private String j6EMemoFont;
    /** j6_e_メモ色 */
    private String j6EMemoColor;
    /** j7_メモ */
    private String j7MemoKnj;
    /** j7_メモフォント */
    private String j7MemoFont;
    /** j7_メモ色 */
    private String j7MemoColor;
    /** j8_メモ */
    private String j8MemoKnj;
    /** j8_メモフォント */
    private String j8MemoFont;
    /** j8_メモ色 */
    private String j8MemoColor;
    /** j9_a_メモ */
    private String j9AMemoKnj;
    /** j9_a_メモフォント */
    private String j9AMemoFont;
    /** j9_a_メモ色 */
    private String j9AMemoColor;
    /** j9_b_メモ */
    private String j9BMemoKnj;
    /** j9_b_メモフォント */
    private String j9BMemoFont;
    /** j9_b_メモ色 */
    private String j9BMemoColor;
}
