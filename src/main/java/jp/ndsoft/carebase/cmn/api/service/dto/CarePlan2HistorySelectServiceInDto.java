package jp.ndsoft.carebase.cmn.api.service.dto;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI01014_計画書（2）履歴変更の入力Dto
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class CarePlan2HistorySelectServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 利用者ID */
    @NotEmpty
    private String userId;
    /** 事業者ID */
    @NotEmpty
    private String svJigyoId;
    /** 計画対象期間ID */
    @NotEmpty
    private String sc1Id;
    /** 計画書（２）ID (履歴ID) */
    @NotEmpty
    private String ks21Id;
    /** 履歴インデックス */
    @NotEmpty
    private String rirekiIndex;
    /** 履歴ページ区分 */
    @NotEmpty
    private String rirekiPage;
    /** 計画書様式 */
    @NotEmpty
    private String cksflg;
    /** 記録との連携 */
    @NotEmpty
    private String kirokuRenkeiFlg;
}
