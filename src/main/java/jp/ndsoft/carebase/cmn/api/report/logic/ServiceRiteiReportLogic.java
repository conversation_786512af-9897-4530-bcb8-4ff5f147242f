package jp.ndsoft.carebase.cmn.api.report.logic;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.HashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.drew.lang.StringUtil;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.ReportCommonGetOptionDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ServiceRiteiReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.dto.TeikyohyoReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.service.ShoninSetReportService;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.smh.framework.properties.FrameworkProperties;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.xml.JRXmlLoader;

/**
 * @since 2025.07.17
 * <AUTHOR>
 * @description V00231_サービス利用票 帳票出力
 */
@Component
public class ServiceRiteiReportLogic {

    @Autowired
    private SimulationRiteiReportLogic simulationRiteiReportLogic;

    @Autowired
    private TeikyohyoReportLogic teikyohyoReportLogic;

    @Autowired
    private Nds3GkFunc01Logic nds3gkfunc01Logic;

    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    // 承認欄設定
    @Autowired
    private ShoninSetReportService shoninSetReportService;

    /**
     * V00231_サービス利用票の帳票パラメータ取得
     *
     * @param inDto 入力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public TeikyohyoReportServiceInDto getV00231ReportParameters(ServiceRiteiReportParameterModel inDto) {

        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        TeikyohyoReportServiceInDto infoInDto = new TeikyohyoReportServiceInDto();

        // 2. 印刷オプション情報の取得
        ReportCommonGetOptionDto reportCommonGetOptionDto = simulationRiteiReportLogic.wf_00_get_option(
                inDto.getSyscd(), CommonDtoUtil.strValToInt(inDto.getShokuinId()), inDto.getYymmYm(), null);

        // 3. サービス利用票情報の取得
        infoInDto = teikyohyoReportLogic.wf_1st_ritei_loop(ReportConstants.STR_1, inDto.getUserMax(),
                inDto.getUserIdList(), inDto.getYymmYm(), reportCommonGetOptionDto, inDto.getGbeBunshoFlg(),
                ReportConstants.STR_0, null, null);

        // 4. 提供年月日の設定
        // 提供年月 yymm
        infoInDto.setYymm(nds3gkfunc01Logic.get2Gengouj(CommonConstants.INT_1,
                inDto.getYymmYm() + CommonConstants.STRING_FIRST_DAY) + ReportConstants.MINUTE);
        // 提供年月区分
        infoInDto.setYymmKbn(
                StringUtil.compare(inDto.getYymmYm(), CommonConstants.TEIKYOU_YM_202103) <= 0 ? ReportConstants.STR_1
                        : ReportConstants.STR_0);

        // 5. 和暦変換処理
        infoInDto.setShiTeiDate(
                nds3gkfunc01Logic.get2Gengouj(CommonConstants.INT_1, inDto.getPrintSet().getShiTeiDate()));

        // 6. システムの空欄表示処理
        String blankDate = nds3GkFunc01Logic.blankDate(inDto.getAppYmd());
        infoInDto.setHoumonStYmdGen(blankDate.substring(0, 2));
        infoInDto.setHoumonEdYmdGen(blankDate.substring(0, 2));

        // 7. 事業所関連情報の取得
        // 7.1. 事業所番号の取得
        infoInDto.setShienNumber(kghCmpF01Logic.getCmpShienNumber(CommonDtoUtil.strValToInt(inDto.getShienId())));
        // 7.2. 事業所電話番号の取得
        infoInDto.setTel(kghCmpF01Logic.cmpShienTel(CommonDtoUtil.strValToInt(inDto.getShienId())));
        // 7.3. 支援事業者名の取得
        infoInDto.setShienName(kghCmpF01Logic.getShienNameRyaku(CommonDtoUtil.strValToInt(inDto.getShienId())));

        return infoInDto;

    }

    /**
     * 帳票レイアウトファイル取得
     *
     * @param fwProps         FrameworkProperties
     * @param reportParameter PDF帳票パラメータ
     * @param inDto           入力データ
     * @return 帳票レイアウトファイル
     * @throws Exception
     */
    public JasperReport getJasperReport(FrameworkProperties fwProps,
            TeikyohyoReportServiceInDto reportParameter, ServiceRiteiReportParameterModel inDto)
            throws Exception {

        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(fwProps, ReportConstants.JRXML_RIYOUHYOREPORT)));

        // コンパイル
        final JasperDesign jasperDesign = JRXmlLoader.load(is);

        // 承認欄の取得処理
        // リクエストパラメータ.データ.初期設定マスタの情報.承認欄情報が「1:帳票毎保持する」の場合
        HashMap<String, Object> shonin = shoninSetReportService.getShoninSetReport(jasperDesign, ReportConstants.INT_1,
                inDto.getShienId(), ReportConstants.SECTION_3GKV00231P003);
        reportParameter.setSubReportPath((String) shonin.get(ReportConstants.SUBREPORTPATH));
        reportParameter.setSubReportDataDs(
                (JRBeanCollectionDataSource) shonin.get(ReportConstants.SUBREPORTDATADS));

        return JasperCompileManager.compileReport(jasperDesign);
    }

}