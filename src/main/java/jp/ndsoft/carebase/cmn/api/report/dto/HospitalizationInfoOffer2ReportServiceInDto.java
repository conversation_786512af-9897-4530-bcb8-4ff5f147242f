package jp.ndsoft.carebase.cmn.api.report.dto;

import java.util.Map;

import jp.ndsoft.smh.framework.global.report.dto.FixedReportInDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * @since 2025.07.15
 * <AUTHOR>
 *         U06091_入院時情報提供書② 帳票出力InDto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class HospitalizationInfoOffer2ReportServiceInDto extends FixedReportInDto {
    /** UID. */
    private static final long serialVersionUID = 1L;
    /** データソース */
    private JRBeanCollectionDataSource dataSource;
    /** パラメータ */
    private Map<String, Object> parameters;

    /* 記入用フラグ */
    private Boolean emptyFlg;
    /* 指定日印刷区分 */
    private Integer shiTeiKubun;
    /* 利用者名 */
    private String riyoushaNm;
    /* 敬称 */
    private String keisho;
    /* 出力日 */
    private String shiTeiDateGG;
    private String shiTeiDateYY;
    private String shiTeiDateMM;
    private String shiTeiDateDD;
    /* 麻痺の状況 */
    private Integer mahiKbn;
    /* ADL移動（自立） */
    private Integer adlIdou1;
    /* ADL移動（見守り） */
    private Integer adlIdou2;
    /* ADL移動（一部介助） */
    private Integer adlIdou3;
    /* ADL移動（全介助） */
    private Integer adlIdou4;
    /* ADL移乗（自立） */
    private Integer adlIjyou1;
    /* ADL移乗（見守り） */
    private Integer adlIjyou2;
    /* ADL移乗（一部介助） */
    private Integer adlIjyou3;
    /* ADL移乗（全介助） */
    private Integer adlIjyou4;
    /* ADL更衣（自立） */
    private Integer adlKoui1;
    /* ADL更衣（見守り） */
    private Integer adlKoui2;
    /* ADL更衣（一部介助） */
    private Integer adlKoui3;
    /* ADL更衣（全介助） */
    private Integer adlKoui4;
    /* ADL整容（自立） */
    private Integer adlSeiyou1;
    /* ADL整容（見守り） */
    private Integer adlSeiyou2;
    /* ADL整容（一部介助） */
    private Integer adlSeiyou3;
    /* ADL整容（全介助） */
    private Integer adlSeiyou4;
    /* ADL入浴（自立） */
    private Integer adlNyuuyoku1;
    /* ADL入浴（見守り） */
    private Integer adlNyuuyoku2;
    /* ADL入浴（一部介助） */
    private Integer adlNyuuyoku3;
    /* ADL入浴（全介助） */
    private Integer adlNyuuyoku4;
    /* ADL食事（自立） */
    private Integer adlShokuji1;
    /* ADL食事（見守り） */
    private Integer adlShokuji2;
    /* ADL食事（一部介助） */
    private Integer adlShokuji3;
    /* ADL食事（全介助） */
    private Integer adlShokuji4;
    /* 褥瘡の有無 */
    private Integer jyokusouUmu;
    /* 褥瘡メモ */
    private String jyokusouMemoKnj;
    /* ADL移動（室内）（杖） */
    private Integer adlIdouShitsunai1;
    /* ADL移動（室内）（歩行器） */
    private Integer adlIdouShitsunai2;
    /* ADL移動（室内）（車いす） */
    private Integer adlIdouShitsunai3;
    /* ADL移動（室内）（その他） */
    private Integer adlIdouShitsunai4;
    /* ADL移動（屋外）（杖） */
    private Integer adlIdouShudan1;
    /* ADL移動（屋外）（歩行器） */
    private Integer adlIdouShudan2;
    /* ADL移動（屋外）（車いす） */
    private Integer adlIdouShudan3;
    /* ADL移動（屋外）（その他） */
    private Integer adlIdouShudan4;
    /* ADL起居動作（自立） */
    private Integer adlKikyo1;
    /* ADL起居動作（見守り） */
    private Integer adlKikyo2;
    /* ADL起居動作（一部介助） */
    private Integer adlKikyo3;
    /* ADL起居動作（全介助） */
    private Integer adlKikyo4;
    /* 食事回数 */
    private String shokujiCnt;
    /* 食事回数（朝） */
    private String shokujiCntMorn;
    /* 食事回数（昼） */
    private String shokujiCntNoon;
    /* 食事回数（夜） */
    private String shokujiCntEven;
    /* 食事制限 */
    private Integer shokujiSeigen;
    /* 食事制限メモ */
    private String shokujiSeigenMemoKnj;
    /* 食事形態（普通） */
    private Integer shokujiKeitai1;
    /* 食事形態（きざみ） */
    private Integer shokujiKeitai2;
    /* 食事形態（嚥下障害食） */
    private Integer shokujiKeitai3;
    /* 食事形態（ミキサー） */
    private Integer shokujiKeitai4;
    /* UDF等の食形態区分 */
    private String udfShokujiKeitaiKbn;
    /* 摂取方法（経口） */
    private Integer sesshuHouhou1;
    /* 摂取方法（経管栄養） */
    private Integer sesshuHouhou2;
    /* 水分とろみ */
    private Integer suibunToromi;
    /* 水分制限 */
    private Integer suibunSeigen;
    /* 水分制限メモ */
    private String suibunSeigenMemoKnj;
    /* 嚥下機能 */
    private Integer engeUmu;
    /* 口腔清潔 */
    private Integer koukuuCare;
    /* 義歯 */
    private Integer gishiUmu;
    /* 義歯区分 */
    private Integer gishiKbn;
    /* 口臭 */
    private Integer koushuuUmu;
    /* 排尿（自立） */
    private Integer hainyou1;
    /* 排尿（見守り） */
    private Integer hainyou2;
    /* 排尿（一部介助） */
    private Integer hainyou3;
    /* 排尿（全介助） */
    private Integer hainyou4;
    /* 排便（自立） */
    private Integer haiben1;
    /* 排便（見守り） */
    private Integer haiben2;
    /* 排便（一部介助） */
    private Integer haiben3;
    /* 排便（全介助） */
    private Integer haiben4;
    /* ポータブルトイレ */
    private Integer portableToiletUmu;
    /* オムツ/パッド */
    private Integer omutsuPadUmu;
    /* 睡眠の状態 */
    private Integer sleepJyoutai;
    /* 睡眠の状態メモ */
    private String sleepMemoKnj;
    /* 眠剤の使用 */
    private Integer sleepDrugUmu;
    /* 喫煙量 */
    private String smoking;
    /* 喫煙有無 */
    private Integer smokingUmu;
    /* 飲酒有無 */
    private Integer alcoholUmu;
    /* 飲酒量 */
    private String alcohol;
    /* 飲酒量フォントサイズ */
    private String alcoholFontSize;
    /* 視力 */
    private Integer eyesightKbn;
    /* 聴力 */
    private Integer hearingKbn;
    /* 言語 */
    private Integer languageAbility;
    /* 意思疎通 */
    private Integer comnKbn;
    /* 眼鏡 */
    private Integer glassesUmu;
    /* 補聴器 */
    private Integer hearingAidUmu;
    /* 別紙出力1フラグ */
    private Integer comnTokkiFlg;
    /* コミュニケーションに関する特記事項 */
    private String comnTokkiKnj;
    /* 眼鏡メモ */
    private String glassesMemoKnj;
    /* 精神面における療養上の問題（なし） */
    private Integer ryouyou1Umu;
    /* 精神面における療養上の問題（（幻視幻聴） */
    private Integer ryouyou2Umu;
    /* 精神面における療養上の問題（（興奮） */
    private Integer ryouyou3Umu;
    /* 精神面における療養上の問題（焦燥不穏） */
    private Integer ryouyou4Umu;
    /* 精神面における療養上の問題（妄想） */
    private Integer ryouyou5Umu;
    /* 精神面における療養上の問題（暴力/攻撃性） */
    private Integer ryouyou6Umu;
    /* 精神面における療養上の問題（介護への抵抗） */
    private Integer ryouyou7Umu;
    /* 精神面における療養上の問題（不眠） */
    private Integer ryouyou8Umu;
    /* 精神面における療養上の問題（昼夜逆転） */
    private Integer ryouyou9Umu;
    /* 精神面における療養上の問題（徘徊） */
    private Integer ryouyou10Umu;
    /* 精神面における療養上の問題（危険行為） */
    private Integer ryouyou11Umu;
    /* 精神面における療養上の問題（不潔行為） */
    private Integer ryouyou12Umu;
    /* 精神面における療養上の問題（その他） */
    private Integer ryouyou13Umu;
    /* 精神面における療養上の問題（その他メモ） */
    private String ryouyouMemoKnj;
    /* 疾患歴（なし） */
    private Integer sick1Umu;
    /* 疾患歴（悪性腫瘍） */
    private Integer sick2Umu;
    /* 疾患歴（認知症） */
    private Integer sick3Umu;
    /* 疾患歴（急性呼吸器感染症） */
    private Integer sick4Umu;
    /* 疾患歴（脳血管障害） */
    private Integer sick5Umu;
    /* 疾患歴（骨折） */
    private Integer sick6Umu;
    /* 疾患歴（その他） */
    private Integer sick7Umu;
    /* 疾患歴（その他）メモ */
    private String sickMemoKnj;
    /* 最近半年間での入院 */
    private Integer nyuuinUmu;
    /* 入院理由 */
    private String nyuuinRiyu;
    /* 最近半年間での入院開始日 */
    private String nyuuinStartDateGG;
    private String nyuuinStartDateYY;
    private String nyuuinStartDateMM;
    private String nyuuinStartDateDD;
    /* 最近半年間での入院終了日 */
    private String nyuuinEndDateGG;
    private String nyuuinEndDateYY;
    private String nyuuinEndDateMM;
    private String nyuuinEndDateDD;
    /* 入院頻度 */
    private Integer nyuuinHindo;
    /* 医療処置（なし） */
    private Integer shochi1Umu;
    /* 医療処置（点滴） */
    private Integer shochi2Umu;
    /* 医療処置（酸素療法） */
    private Integer shochi3Umu;
    /* 医療処置（喀痰吸引） */
    private Integer shochi4Umu;
    /* 医療処置（気管切開） */
    private Integer shochi5Umu;
    /* 医療処置（胃ろう） */
    private Integer shochi6Umu;
    /* 医療処置（経鼻栄養） */
    private Integer shochi7Umu;
    /* 医療処置（経腸栄養） */
    private Integer shochi8Umu;
    /* 医療処置（褥瘡） */
    private Integer shochi9Umu;
    /* 医療処置（尿道カテーテル） */
    private Integer shochi10Umu;
    /* 医療処置（尿路ストーマ） */
    private Integer shochi11Umu;
    /* 医療処置（消化管ストーマ） */
    private Integer shochi12Umu;
    /* 医療処置（痛みコントロール） */
    private Integer shochi13Umu;
    /* 医療処置（排便コントロール） */
    private Integer shochi14Umu;
    /* 医療処置（自己注射） */
    private Integer shochi15Umu;
    /* 医療処置（その他） */
    private Integer shochi16Umu;
    /* 医療処置（自己注射）メモ */
    private String shochi15MemoKnj;
    /* 医療処置（その他）メモ */
    private String shochi16MemoKnj;
    /* 内服薬 */
    private Integer drugUmu;
    /* 内服薬メモ */
    private String drugMemoKnj;
    /* 居宅療養管理指導 */
    private Integer ryouyouKanriUmu;
    /* 居宅療養管理指導メモ */
    private String ryouyouKanriMemoKnj;
    /* 薬剤管理 */
    private Integer drugKanriKbn;
    /* 薬剤管理（管理者） */
    private String drugKanriKanrisya;
    /* 薬剤管理（管理方法） */
    private String drugKanriHouhou;
    /* 服薬状況 */
    private Integer drugJyoukyou;
    /* お薬に関する特記事項 */
    private String drugTokkiKnj;
    /* 別紙出力2フラグ */
    private Integer drugTokkiFlg;
    /* 医療機関名 */
    private String hospKnj;
    /* 医療機関名 */
    private String hospKnjFont;
    /* 医師名 */
    private String doctorKnj;
    /* 医師名フォント */
    private String doctorKnjFont;
    /* 医師名 */
    private String doctorKana;
    /* 医師名フォント */
    private String doctorKanaFont;
    /* 電話番号 */
    private String hospTel;
    /* 診察方法 */
    private Integer hospHouhou;
    /* 診察頻度（回数） */
    private String hospKaisu;
    /* 欄外に利用者氏名を印刷するフラグ */
    private Boolean userNameFlg;

}
