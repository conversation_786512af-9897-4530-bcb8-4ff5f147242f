package jp.ndsoft.carebase.cmn.api.logic;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01217CareInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01217PlanImplementationDataInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanImplementationAchievementsRegistAchievementsInfoSelectInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanImplementationAchievementsRegistAchievementsInfoSelectOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanImplementationAchievementsRegistCaseInfoSelectInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanImplementationAchievementsRegistCaseInfoSelectOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.BikouRoukenByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.BikouRoukenOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GazouIdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GazouIdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkKkjJissekiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkKkjJissekiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinInfoListByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinInfoListOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.BikouRoukenSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghKrkKkjJissekiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTocGazouKanriRirekiSelectMapper;
import jp.ndsoft.smh.framework.util.StringUtil;

/**
 * 計画実施‐実績登録
 * 
 * <AUTHOR>
 */
@Component
public class PlanImplementationAchievementsLogic {

    @Autowired
    private KghKrkKkjJissekiSelectMapper kghKrkKkjJissekiSelectMapper;

    @Autowired
    private BikouRoukenSelectMapper bikouRoukenSelectMapper;

    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    @Autowired
    private KghTocGazouKanriRirekiSelectMapper kghTocGazouKanriRirekiSelectMapper;

    /**
     * 計画実施‐実績登録画面の実績情報取得
     * 
     * @param inDto GUI01217_計画実施‐実績登録の画面の実績情報取得の出力DTO.
     * @return GUI01217_計画実施‐実績登録の画面の実績情報取得の出力DTO
     * @throws Exception Exception
     */
    public PlanImplementationAchievementsRegistAchievementsInfoSelectOutDto getAchievementsInfo(
            PlanImplementationAchievementsRegistAchievementsInfoSelectInDto inDto) throws Exception {

        // 戻り情報を設定
        PlanImplementationAchievementsRegistAchievementsInfoSelectOutDto out = new PlanImplementationAchievementsRegistAchievementsInfoSelectOutDto();

        // 計画実施データ欄情報リスト
        List<Gui01217PlanImplementationDataInfo> planImplementationDataInfoList = new ArrayList<>();

        // 計画実施データ欄の情報絞込リスト
        List<KghKrkKkjJissekiOutEntity> kghKrkKkjJissekiOutList = new ArrayList<>();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2.計画実施データ欄の情報を取得===============
         * 
         */
        KghKrkKkjJissekiByCriteriaInEntity kghKrkKkjJissekiByCriteriaInEntity = new KghKrkKkjJissekiByCriteriaInEntity();
        // ヘッダID
        kghKrkKkjJissekiByCriteriaInEntity.setAlKjisshi1Id(CommonDtoUtil.strValToInt(inDto.getKjisshi1Id()));
        // DAOを実行
        List<KghKrkKkjJissekiOutEntity> kghKrkKkjJissekiSelectList = this.kghKrkKkjJissekiSelectMapper
                .findKghKrkKkjJissekiByCriteria(kghKrkKkjJissekiByCriteriaInEntity);

        /*
         * ===============3. 「担当者絞込」と「頻度絞込」を利用して、「計画実施データ欄の情報」を再取得===============
         * 
         */
        // 3.1.「2.」取得する「計画実施データ欄の情報」の件数は>0の場合、条件で情報を編集する
        if (!CollectionUtils.isNullOrEmpty(kghKrkKkjJissekiSelectList)) {
            for (KghKrkKkjJissekiOutEntity kghKrkKkjJissekiSelect : kghKrkKkjJissekiSelectList) {
                // 担当者絞込結果
                Boolean tantoKnjFiltered = filterByTantoKnj(inDto.getTantoKnj(), kghKrkKkjJissekiSelect);
                if (tantoKnjFiltered) {
                    // 頻度絞込結果
                    Boolean hindoKnjFiltered = filterByHindoKnj(inDto.getHindoKnj(), kghKrkKkjJissekiSelect);
                    if (hindoKnjFiltered) {
                        kghKrkKkjJissekiOutList.add(kghKrkKkjJissekiSelect);
                    }
                }
            }
        }
        // 3.2.「3.1.」取得する「計画実施データ欄の情報」の件数は>0の場合、他の情報を設定する
        if (!CollectionUtils.isNullOrEmpty(kghKrkKkjJissekiOutList)) {
            // 3.2.1.1 リクエストパラメータ.実施日の日を取得する
            if (StringUtil.isNotEmpty(inDto.getImplementationDate())
                    && inDto.getImplementationDate().length() > CommonConstants.INT_8) {
                // dmy実施チェックの最後の2文字を取得
                String jisshiDayItem = inDto.getImplementationDate()
                        .substring(inDto.getImplementationDate().length() - 2);
                for (KghKrkKkjJissekiOutEntity kkjJissekiOutEntity : kghKrkKkjJissekiOutList) {
                    planImplementationDataInfoList
                            .add(setPlanImplementationDataInfo(jisshiDayItem, kkjJissekiOutEntity));
                }
            }

        }

        out.setPlanImplementationDataInfoList(planImplementationDataInfoList);
        return out;

    }

    /**
     * 担当者絞込結果を取得
     * 
     * @param tantoKnj               担当者
     * @param kghKrkKkjJissekiSelect 計画実施データ欄の情報
     * @return Boolean
     */
    public Boolean filterByTantoKnj(String tantoKnj, KghKrkKkjJissekiOutEntity kghKrkKkjJissekiSelect) {
        // 3.1.1.1 リクエストパラメータ.担当者絞込がある場合、
        if (StringUtil.isNotEmpty(tantoKnj)) {
            // 計画実施データ欄の情報.担当者 LIKE リクエストパラメータ.担当者絞込
            // 或は 計画実施データ欄の情報.サービス種別 LIKE リクエストパラメータ.担当者絞込
            if ((StringUtil.isNotEmpty(kghKrkKkjJissekiSelect.getSvShuKnj()) &&
                    kghKrkKkjJissekiSelect.getSvShuKnj().contains(tantoKnj)) ||
                    (StringUtil.isNotEmpty(kghKrkKkjJissekiSelect.getTantoKnj())
                            && kghKrkKkjJissekiSelect.getTantoKnj().contains(tantoKnj))) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    /**
     * 頻度絞込結果を取得
     * 
     * @param hindoKnj               頻度
     * @param kghKrkKkjJissekiSelect 計画実施データ欄の情報
     * @return Boolean
     */
    public Boolean filterByHindoKnj(String hindoKnj, KghKrkKkjJissekiOutEntity kghKrkKkjJissekiSelect) {
        // 3.1.1.2 リクエストパラメータ.頻度絞込がある場合、
        if (StringUtil.isNotEmpty(hindoKnj)) {
            // 計画実施データ欄の情報.頻度 LIKE リクエストパラメータ.頻度絞込
            // 或は 計画実施データ欄の情報.計画書（2）頻度 LIKE リクエストパラメータ.頻度絞込
            if ((StringUtil.isNotEmpty(kghKrkKkjJissekiSelect.getHindoKnj()) &&
                    kghKrkKkjJissekiSelect.getHindoKnj().contains(hindoKnj)) ||
                    (StringUtil.isNotEmpty(kghKrkKkjJissekiSelect.getCks22HindoKnj())
                            && kghKrkKkjJissekiSelect.getCks22HindoKnj().contains(hindoKnj))) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    /**
     * 計画実施データ欄情報を設定
     * 
     * @param jisshiDayItem          実施日の日
     * @param kghKrkKkjJissekiSelect 計画実施データ欄の情報
     * @return Gui01217PlanImplementationDataInfo
     */
    public Gui01217PlanImplementationDataInfo setPlanImplementationDataInfo(String jisshiDayItem,
            KghKrkKkjJissekiOutEntity kghKrkKkjJissekiSelect) {
        // 計画実施データ欄情報
        Gui01217PlanImplementationDataInfo dataInfo = new Gui01217PlanImplementationDataInfo();
        // データID
        dataInfo.setKjisshi2Id(CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getKjisshi2Id()));
        // ヘッダID
        dataInfo.setKjisshi1Id(CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getKjisshi1Id()));
        // 課題番号
        dataInfo.setKadaiNo(CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getKadaiNo()));
        // 課題
        dataInfo.setKadaiKnj(kghKrkKkjJissekiSelect.getKadaiKnj());
        // 表示順
        dataInfo.setSeq(CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getSeq()));
        // 担当者
        dataInfo.setTantoKnj(kghKrkKkjJissekiSelect.getTantoKnj());
        // 計画書(2)データID
        dataInfo.setKs22Id(CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getKs22Id()));
        // 計画書(2)担当者ID
        dataInfo.setKs222Id(CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getKs222Id()));
        // 頻度
        dataInfo.setHindoKnj(kghKrkKkjJissekiSelect.getHindoKnj());
        // 計画書(2)詳細ID
        dataInfo.setCks22Ks22Id(CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getCks22Ks22Id()));
        // 計画書（2）ID
        dataInfo.setKs21Id(CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getKs21Id()));
        // 具体的
        dataInfo.setGutaitekiKnj(kghKrkKkjJissekiSelect.getGutaitekiKnj());
        // 長期
        dataInfo.setChoukiKnj(kghKrkKkjJissekiSelect.getChoukiKnj());
        // 短期
        dataInfo.setTankiKnj(kghKrkKkjJissekiSelect.getTankiKnj());
        // 介護
        dataInfo.setKaigoKnj(kghKrkKkjJissekiSelect.getKaigoKnj());
        // サービス種別
        dataInfo.setSvShuKnj(kghKrkKkjJissekiSelect.getSvShuKnj());
        // 計画書（2）頻度
        dataInfo.setCks22HindoKnj(kghKrkKkjJissekiSelect.getCks22HindoKnj());
        // dmy実施チェックのItem
        String dmyJisshiChkItem = null;
        // 区分コード
        String kbnCd = null;
        // 3.2.1 dmy実施チェックの設定
        // 3.2.2 区分コードの設定
        if (jisshiDayItem.charAt(CommonConstants.INT_0) == 0) {
            // ※１～９日の場合、dmy実施チェック=day+"１～９"
            dmyJisshiChkItem = CommonConstants.DAY_STR
                    + CommonDtoUtil.objValToString(jisshiDayItem.charAt(CommonConstants.INT_1));
            switch (jisshiDayItem.charAt(CommonConstants.INT_1)) {
                case CommonConstants.INT_1:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay1());
                    break;
                case CommonConstants.INT_2:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay2());
                    break;
                case CommonConstants.INT_3:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay3());
                    break;
                case CommonConstants.INT_4:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay4());
                    break;
                case CommonConstants.INT_5:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay5());
                    break;
                case CommonConstants.INT_6:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay6());
                    break;
                case CommonConstants.INT_7:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay7());
                    break;
                case CommonConstants.INT_8:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay8());
                    break;
                case CommonConstants.INT_9:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay9());
                    break;
            }
        } else {
            // ※１0～３１日の場合、dmy実施チェック=day+"１0～３１"
            dmyJisshiChkItem = CommonConstants.DAY_STR + jisshiDayItem;
            switch (CommonDtoUtil.strValToInt(jisshiDayItem)) {
                case CommonConstants.INT_10:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay10());
                    break;
                case CommonConstants.INT_11:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay11());
                    break;
                case CommonConstants.INT_12:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay12());
                    break;
                case CommonConstants.INT_13:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay13());
                    break;
                case CommonConstants.INT_14:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay14());
                    break;
                case CommonConstants.INT_15:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay15());
                    break;
                case CommonConstants.INT_16:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay16());
                    break;
                case CommonConstants.INT_17:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay17());
                    break;
                case CommonConstants.INT_18:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay18());
                    break;
                case CommonConstants.INT_19:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay19());
                    break;
                case CommonConstants.INT_20:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay20());
                    break;
                case CommonConstants.INT_21:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay21());
                    break;
                case CommonConstants.INT_22:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay22());
                    break;
                case CommonConstants.INT_23:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay23());
                    break;
                case CommonConstants.INT_24:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay24());
                    break;
                case CommonConstants.INT_25:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay25());
                    break;
                case CommonConstants.INT_26:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay26());
                    break;
                case CommonConstants.INT_27:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay27());
                    break;
                case CommonConstants.INT_28:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay28());
                    break;
                case CommonConstants.INT_29:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay29());
                    break;
                case CommonConstants.INT_30:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay30());
                    break;
                case CommonConstants.INT_31:
                    kbnCd = CommonDtoUtil.objValToString(kghKrkKkjJissekiSelect.getDay31());
                    break;
            }
        }
        // dmy実施チェック="day" +参数实施日对应的日
        dataInfo.setDmyJisshiChk(dmyJisshiChkItem);
        // 3.2.2.1 計画実施データ欄の情報にdmy実施チェック対する日の値は区分コードを設定する
        dataInfo.setKbnCd(kbnCd);
        return dataInfo;
    }

    /**
     * 計画実施‐実績登録画面のケース情報を取得
     * 
     * @param inDto GUI01217_計画実施‐実績登録のケース情報の入力DTO.
     * @return GUI01217_計画実施‐実績登録のケース情報の出力DTO
     * @throws Exception Exception
     */
    public PlanImplementationAchievementsRegistCaseInfoSelectOutDto getCaseInfo(
            PlanImplementationAchievementsRegistCaseInfoSelectInDto inDto) throws Exception {
        // 戻り情報を設定
        PlanImplementationAchievementsRegistCaseInfoSelectOutDto out = new PlanImplementationAchievementsRegistCaseInfoSelectOutDto();
        // ケースリスト
        List<Gui01217CareInfo> careInfoList = new ArrayList<>();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2.ケース情報を取得===============
         * 
         */
        // 2.1. 変数の設定
        // 変数はフォーマット「YYYY/MM/DD」で設定
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
        // 実施日のLocalDate
        LocalDate givenDate = LocalDate.parse(inDto.getImplementationDate(), formatter);
        // 日期を取得
        YearMonth yearMonth = YearMonth.from(givenDate);
        // 月の初日を取得
        LocalDate firstDayOfMonth = yearMonth.atDay(CommonConstants.INT_1);

        // 月の最終日を取得
        LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();

        // 変数.開始記録日
        String stYymmdd = firstDayOfMonth.format(formatter);
        // 変数.終了記録日
        String edYymmdd = lastDayOfMonth.format(formatter);

        // 2.2. 29-17 備考と老健カルテ情報取得のDAOを利用し、ケースリストを取得
        BikouRoukenByCriteriaInEntity bikouRoukenByCriteriaInEntity = new BikouRoukenByCriteriaInEntity();
        // 法人ID
        bikouRoukenByCriteriaInEntity.setHid(CommonDtoUtil.strValToInt(inDto.getHojinId()));
        // 施設ID
        bikouRoukenByCriteriaInEntity.setSid(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 利用者ID
        bikouRoukenByCriteriaInEntity.setUserid(CommonDtoUtil.strValToInt(inDto.getUserid()));
        // 開始記録日
        bikouRoukenByCriteriaInEntity.setStYymmdd(stYymmdd);
        // 終了記録日
        bikouRoukenByCriteriaInEntity.setEdYymmdd(edYymmdd);
        // DAOを実行
        List<BikouRoukenOutEntity> bikouRoukenSelectList = this.bikouRoukenSelectMapper
                .findBikouRoukenByCriteria(bikouRoukenByCriteriaInEntity);

        /*
         * ===============3. 職員情報を取得===============
         * 
         */
        ShokuinInfoListByCriteriaInEntity shokuinInfoListByCriteriaInEntity = new ShokuinInfoListByCriteriaInEntity();
        // DAOを実行
        List<ShokuinInfoListOutEntity> comMscShokuinSelectList = this.comMscShokuinSelectMapper
                .findShokuinInfoListByCriteria(shokuinInfoListByCriteriaInEntity);

        /*
         * ===============4. ケース用情報を取得する===============
         * 
         */
        // 4.1.「2.」取得する「ケース情報」の件数は>0の場合
        if (!CollectionUtils.isNullOrEmpty(bikouRoukenSelectList)) {
            for (BikouRoukenOutEntity bikouRouken : bikouRoukenSelectList) {
                // 4.1.1.1 ケース情報リスト.実施モニタリング詳細ID=リクエストパラメータ.データID
                // 4.1.1.2 ケース情報リスト.備考区分=48
                if (bikouRouken.getKjisshi2Id() == CommonDtoUtil.strValToInt(inDto.getKjisshi2Id())
                        && bikouRouken.getBikoKbn() == CommonConstants.NUMBER_48) {
                    // 文字絞込結果
                    Boolean mojiKnjFiltered = filterByMojiKnj(inDto.getMojiKnj(), bikouRouken);
                    if (mojiKnjFiltered) {
                        // ケース
                        careInfoList.add(setCareList(bikouRouken));
                    }
                }
            }
            if (!CollectionUtils.isNullOrEmpty(careInfoList)) {
                // 4.1.1.4 リクエストパラメータ.画面表示順=1「日付、時間の新しい登録から表示」の場合
                if (inDto.getTimeSortKbn() == CommonConstants.STR_1) {
                    // ケース情報リスト.記録日、開始時間、開始分、レコード番号で降順設定
                    careInfoList.sort(Comparator.comparing(Gui01217CareInfo::getYymmYmd).reversed()
                            .thenComparing(Gui01217CareInfo::getTimeHh).reversed()
                            .thenComparing(Gui01217CareInfo::getTimeMm).reversed()
                            .thenComparing(Gui01217CareInfo::getRecNo).reversed());
                } else {
                    // 以外の場合、ケース情報リスト.記録日、開始時間、開始分、レコード番号で昇順設定
                    careInfoList.sort(
                            Comparator.comparing(Gui01217CareInfo::getYymmYmd)
                                    .thenComparing(Gui01217CareInfo::getTimeHh)
                                    .thenComparing(Gui01217CareInfo::getTimeMm)
                                    .thenComparing(Gui01217CareInfo::getRecNo));
                }
                // 4.2.「4.1.」取得する「ケース情報」の件数は>0の場合、他の情報を設定する
                for (Gui01217CareInfo care : careInfoList) {
                    // 4.2.1.1 ケース情報リスト.記入者名の設定
                    // 「3.」職員情報が取得できる
                    if (!CollectionUtils.isNullOrEmpty(comMscShokuinSelectList)) {
                        Optional<ShokuinInfoListOutEntity> comMscShokuinOptional = comMscShokuinSelectList.stream()
                                .filter(item -> CommonDtoUtil.checkStringEqual(
                                        // かつ ケース情報リスト.記入者=「3.」.職員IDの場合、
                                        CommonDtoUtil.objValToString(item.getChkShokuId()), care.getStaffid()))
                                .findFirst();
                        if (comMscShokuinOptional.isPresent()) {
                            // ケース情報リスト.記入者名＝「3.」.職員名（姓）+' '+「3.」.職員名（名）
                            care.setStaffName(
                                    comMscShokuinOptional.get().getShokuin1Kana() + CommonConstants.BLANK_SPACE
                                            + comMscShokuinOptional.get().getShokuin2Kana());
                        }
                    }

                    // 4.2.1.5. 下記の画像関連記録履歴情報取得のDAOを利用し、画像IDを取得
                    GazouIdByCriteriaInEntity gazouIdByCriteriaInEntity = new GazouIdByCriteriaInEntity();
                    // 機能ID
                    gazouIdByCriteriaInEntity.setKinouId(CommonConstants.FUNCTION_ID_INTEGER);
                    // 法人ID
                    gazouIdByCriteriaInEntity.setHoujinId(CommonDtoUtil.strValToInt(inDto.getHojinId()));
                    // 施設ID
                    gazouIdByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
                    // 事業所ID
                    gazouIdByCriteriaInEntity.setSvJigyoId(CommonConstants.NUMBER_ZERO);
                    // 利用者IDリスト
                    List<Integer> useridList = new ArrayList<>();
                    useridList.add(CommonDtoUtil.strValToInt(inDto.getUserid()));
                    gazouIdByCriteriaInEntity.setUseridList(useridList);
                    // 履歴番号リスト
                    List<Integer> recNoList = new ArrayList<>();
                    recNoList.add(CommonDtoUtil.strValToInt(care.getRecNo()));
                    gazouIdByCriteriaInEntity.setRecNoList(recNoList);
                    // 記録日リスト
                    List<String> ymdList = new ArrayList<>();
                    ymdList.add(StringUtils.EMPTY);
                    gazouIdByCriteriaInEntity.setYmdList(ymdList);
                    // 行番号リスト
                    List<Integer> lineNoList = new ArrayList<>();
                    lineNoList.add(CommonConstants.NUMBER_ZERO);
                    gazouIdByCriteriaInEntity.setLineNoList(lineNoList);
                    // DAOを実行
                    List<GazouIdOutEntity> kghTocGazouKanriRirekiSelectList = this.kghTocGazouKanriRirekiSelectMapper
                            .findGazouIdByCriteria(gazouIdByCriteriaInEntity);

                    // 4.2.1.5.1 画像関連記録履歴情報取得できる場合、ケース情報リスト.画像ID=上記画像関連記録履歴情報取得する画像ID
                    if (!CollectionUtils.isNullOrEmpty(kghTocGazouKanriRirekiSelectList)) {
                        care.setComputeGazouId(CommonDtoUtil.objValToString(
                                kghTocGazouKanriRirekiSelectList.get(CommonConstants.INT_0).getGazouId()));
                    }
                    // 4.2.1.6 ケース情報リスト.ComputeHensyuSetteiKbn=リクエストパラメータ.変数設定区分「親画面.老健ｶﾙﾃ画面
                    // 日時の編集設定」
                    care.setComputeHensyuSetteiKbn(inDto.getComputeHensyuSetteiKbn());
                }
            }
        }

        out.setCareList(careInfoList);
        return out;
    }

    /**
     * 文字絞込結果を取得
     * 
     * @param mojiKnj     文字
     * @param bikouRouken ケース情報
     * @return Boolean
     */
    public Boolean filterByMojiKnj(String mojiKnj, BikouRoukenOutEntity bikouRouken) {
        // 4.1.1.3 リクエストパラメータ.文字絞込がある場合、
        if (StringUtil.isNotEmpty(mojiKnj)) {
            // ケース情報リスト.ケース事項 LIKE リクエストパラメータ.文字絞込
            if (StringUtil.isNotEmpty(bikouRouken.getCaseKnj()) &&
                    bikouRouken.getCaseKnj().contains(mojiKnj)) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    /**
     * ケース情報を設定
     * 
     * @param bikouRouken ケース情報
     * @return Gui01217CareInfo
     */
    public Gui01217CareInfo setCareList(BikouRoukenOutEntity bikouRouken) {
        // ケース情報
        Gui01217CareInfo careInfo = new Gui01217CareInfo();

        // 備考区分
        careInfo.setBikoKbn(CommonDtoUtil.objValToString(bikouRouken.getBikoKbn()));
        // 記録日
        careInfo.setYymmYmd(bikouRouken.getYymmYmd());
        // レコード番号
        careInfo.setRecNo(CommonDtoUtil.objValToString(bikouRouken.getRecNo()));
        // 開始時間
        careInfo.setTimeHh(String.format(CommonConstants.FORMAT_02d, bikouRouken.getTimeHh()));
        // 開始分
        careInfo.setTimeMm(String.format(CommonConstants.FORMAT_02d, bikouRouken.getTimeMm()));
        // ケース種別
        careInfo.setCaseCd(CommonDtoUtil.objValToString(bikouRouken.getCaseCd()));
        // ケース事項
        careInfo.setCaseKnj(bikouRouken.getCaseKnj());
        // 記入者
        careInfo.setStaffid(CommonDtoUtil.objValToString(bikouRouken.getStaffid()));
        // ｹｰｽ転記フラグ
        careInfo.setCaseFlg(CommonDtoUtil.objValToString(bikouRouken.getCaseFlg()));
        // 申し送りフラグ
        careInfo.setMoushiokuriFlg(CommonDtoUtil.objValToString(bikouRouken.getMoushiokuriFlg()));
        // 結果元履歴番号
        careInfo.setBaseRecNo(CommonDtoUtil.objValToString(bikouRouken.getBaseRecNo()));
        // システム別フラグ
        careInfo.setSystemFlg(CommonDtoUtil.objValToString(bikouRouken.getSystemFlg()));
        // 指示フラグ
        careInfo.setShijiFlg(CommonDtoUtil.objValToString(bikouRouken.getShijiFlg()));
        // 共有区分
        careInfo.setKyouyuBikoKbn(CommonDtoUtil.objValToString(bikouRouken.getKyouyuBikoKbn()));
        // 褥瘡フラグ
        careInfo.setJyoFlg(CommonDtoUtil.objValToString(bikouRouken.getJyoFlg()));
        // ユニークID
        careInfo.setUniqueId(bikouRouken.getUniqueId());
        // DmyW01YymmYmd
        careInfo.setDmyW01YymmYmd(bikouRouken.getDmyW01YymmYmd());
        // ComputeColor
        careInfo.setComputeColor(CommonDtoUtil.objValToString(bikouRouken.getComputeColor()));
        // 計画書(2)ID
        careInfo.setKs21Id(CommonDtoUtil.objValToString(bikouRouken.getKs21Id()));
        // 計画書(2)詳細ID
        careInfo.setKs22Id(CommonDtoUtil.objValToString(bikouRouken.getKs22Id()));
        // 実施モニタリング詳細ID
        careInfo.setKjisshi2Id(CommonDtoUtil.objValToString(bikouRouken.getKjisshi2Id()));
        // DmyMFlg
        careInfo.setDmyMFlg(CommonDtoUtil.objValToString(bikouRouken.getDmyMFlg()));
        // 実施状況確認詳細ID
        careInfo.setR4sKjisshi2Id(CommonDtoUtil.objValToString(bikouRouken.getR4sKjisshi2Id()));
        // 結果元ユニークID
        careInfo.setBaseUniqueId(bikouRouken.getBaseUniqueId());
        // 完了フラグ
        careInfo.setKanryouFlg(CommonDtoUtil.objValToString(bikouRouken.getKanryouFlg()));
        // プロブレムID
        careInfo.setProblemId(CommonDtoUtil.objValToString(bikouRouken.getProblemId()));

        return careInfo;
    }

}
