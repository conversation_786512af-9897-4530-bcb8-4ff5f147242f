package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Riyou704Info;
import jp.ndsoft.carebase.cmn.api.service.dto.UseSlipInfoDeleteRowAftProcSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.UseSlipInfoDeleteRowAftProcSelectServiceOutDto;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * GUI01149_利用票 APINo(895)_利用票画面行削除後処理
 *
 * <AUTHOR>
 */
@Service
public class UseSlipInfoDeleteRowAftProcSelectServiceImpl extends
        SelectServiceImpl<UseSlipInfoDeleteRowAftProcSelectServiceInDto, UseSlipInfoDeleteRowAftProcSelectServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** UseSlipInitInfoSelectServiceImpl サービス */
    @Autowired
    private UseSlipInitInfoSelectServiceImpl useSlipInitInfoSelectServiceImpl;

    /**
     * 利用票画面行削除後処理
     * 
     * @param inDto 利用票画面行削除後処理の入力DTO
     * @return outDto 利用票画面行削除後処理の出力DTO
     * @throws Exception Exception
     */
    @Override
    protected UseSlipInfoDeleteRowAftProcSelectServiceOutDto mainProcess(
            UseSlipInfoDeleteRowAftProcSelectServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);

        // DTOOUT情報
        UseSlipInfoDeleteRowAftProcSelectServiceOutDto outDto = new UseSlipInfoDeleteRowAftProcSelectServiceOutDto();
        List<Gui01149Riyou704Info> riyou704InfoList = new ArrayList<>();

        // 2.リクエストパラメータ.利用票明細情報をループして、サービス内容を取得する
        Gui01149Riyou704Info riyou704Info = useSlipInitInfoSelectServiceImpl
                .headerColorChangeProcess(inDto.getRiyouProcessObject(), inDto.getRiyouList());
        // レスポンスパラメータ.利用票画面処理用構造体 = 上記結果.利用票画面処理用構造体
        // レスポンスパラメータ.利用票明細情報 = 上記結果.利用票明細情報
        riyou704InfoList.add(riyou704Info);
        outDto.setRiyou895Info(riyou704InfoList);

        // 3. 上記処理で取得した結果レスポンスを返却する。
        LOG.info(Constants.END);
        return outDto;
    }
}
