package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.KghCmn03gFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnCpmv01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnRiyou01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnTok1Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnWeek1Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpSvCnvLogic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.CmnUsrHok3gOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.CmnUsrNin3gOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.Convert21OutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.ConvertInfoDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GetUserInfoMonthOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KghCmnRiyouhYouMergeDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcItemuseInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcItemuseOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcItemuseSougouInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcItemuseSougouOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcKmInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcKmOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcSmInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.MhcSmOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.SvItemcodeOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.YokaiDaysOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01150CalcDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01150UserDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanDuplicateUseSlipDuplicateUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanDuplicateUseSlipDuplicateUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanDuplicateUseSlipUserInfoSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanDuplicateUseSlipUserInfoSelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanGaiFutanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanRiyouMapper;
import jp.ndsoft.carebase.common.dao.mybatis.CmnTucPlanSyafukuKeigenMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlan;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanGaiFutan;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanGaiFutanCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanRiyou;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanRiyouCriteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanSyafukuKeigen;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CmnTucPlanSyafukuKeigenCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.*;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanGaiFutanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanRiyouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanSyafukuKeigenSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSougouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcKmSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcSmSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucSvriyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.FygMstShouhinSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import lombok.Getter;
import lombok.Setter;

/**
 * 計画複写の利用票複写処理サービス GUI01150_計画複写
 *
 * <AUTHOR>
 */
@Service
public class PlanDuplicateUseSlipDuplicateUpdateServiceImpl
        extends
        UpdateServiceImpl<PlanDuplicateUseSlipDuplicateUpdateServiceInDto, PlanDuplicateUseSlipDuplicateUpdateServiceOutDto> {
    /**
     * ロガー
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /**
     * 計画複写の利用票利用者情報取得サービス
     */
    @Autowired
    private PlanDuplicateUseSlipUserInfoSelectServiceImpl planDuplicateUseSlipUserInfoSelectService;

    /**
     * KghCmn03gFunc01Logicロジッククラス
     */
    @Autowired
    private KghCmn03gFunc01Logic kghCmn03gFunc01Logic;

    /**
     * KghCmpF01Logicクラス
     */
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    /**
     * KghCmnCpmv01Logic
     */
    @Autowired
    private KghCmnCpmv01Logic kghCmnCpmv01Logic;

    /**
     * KghCmpSvCnvLogic
     */
    @Autowired
    private KghCmpSvCnvLogic kghCmpSvCnvLogic;

    /**
     * KghCmnF01Logic
     */
    @Autowired
    private KghCmnF01Logic kghCmnF01Logic;

    /**
     * KghCmnWeek1Logic
     */
    @Autowired
    private KghCmnWeek1Logic kghCmnWeek1Logic;

    /**
     * KghCmnRiyou01Logic
     */
    @Autowired
    private KghCmnRiyou01Logic kghCmnRiyou01Logic;

    /**
     * Nds3GkFunc01Logicロジッククラス
     */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /**
     * KghCmnTok1クラス
     */
    @Autowired
    private KghCmnTok1Logic kghCmnTok1Logic;

    /**
     * CmnTucPlanSelectMapper
     */
    @Autowired
    private CmnTucPlanSelectMapper cmnTucPlanSelectMapper;

    /**
     * CmnTucPlanRiyouSelectMapper
     */
    @Autowired
    private CmnTucPlanRiyouSelectMapper cmnTucPlanRiyouSelectMapper;

    /**
     * ComTucSvriyoSelectMapper
     */
    @Autowired
    private ComTucSvriyoSelectMapper comTucSvriyoSelectMapper;

    /**
     * CmnTucPlanGaiFutanSelectMapper
     */
    @Autowired
    private CmnTucPlanGaiFutanSelectMapper cmnTucPlanGaiFutanSelectMapper;

    /**
     * CmnTucPlanSyafukuKeigenSelectMapper
     */
    @Autowired
    private CmnTucPlanSyafukuKeigenSelectMapper cmnTucPlanSyafukuKeigenSelectMapper;

    /**
     * CmnTucPlanGaiFutanMapper
     */
    @Autowired
    private CmnTucPlanGaiFutanMapper cmnTucPlanGaiFutanMapper;

    /**
     * CmnTucPlanSyafukuKeigenMapper
     */
    @Autowired
    private CmnTucPlanSyafukuKeigenMapper cmnTucPlanSyafukuKeigenMapper;

    /**
     * CmnTucPlanRiyouMapper
     */
    @Autowired
    private CmnTucPlanRiyouMapper cmnTucPlanRiyouMapper;

    /**
     * ComMscSvjigyoSelectMapper
     */
    @Autowired
    private ComMscSvjigyoSelectMapper comMscSvjigyoSelectMapper;

    /**
     * ComMhcItemuseSougouSelectMapper
     */
    @Autowired
    private ComMhcItemuseSougouSelectMapper comMhcItemuseSougouSelectMapper;

    /**
     * FygMstShouhinSelectMapper
     */
    @Autowired
    private FygMstShouhinSelectMapper fygMstShouhinSelectMapper;

    /**
     * CmnTucPlanMapper
     */
    @Autowired
    private CmnTucPlanMapper cmnTucPlanMapper;

    /**
     * ComMhcSmSelectMapper
     */
    @Autowired
    private ComMhcSmSelectMapper comMhcSmSelectMapper;

    /**
     * ComMhcKmSelectMapper
     */
    @Autowired
    private ComMhcKmSelectMapper comMhcKmSelectMapper;

    /**
     * 計画複写の利用票複写処理
     *
     * @param inDto 計画複写の利用票複写処理の入力DTO
     * @return 計画複写の利用票複写処理の出力DTO
     * @throws Exception Exception
     */
    @Override
    protected PlanDuplicateUseSlipDuplicateUpdateServiceOutDto mainProcess(
            PlanDuplicateUseSlipDuplicateUpdateServiceInDto inDto) throws Exception {
        LOG.info(Constants.START);
        PlanDuplicateUseSlipDuplicateUpdateServiceOutDto ret = new PlanDuplicateUseSlipDuplicateUpdateServiceOutDto();

        // 3.複写先の開始～終了年月を配列化
        List<String> copyToYms = getCopyToYms(inDto);

        // 4.利用者ループ
        doDuplicate(inDto, copyToYms, ret);

        // 5.利用者情報再検索
        setRiyouUserList(inDto, ret);

        LOG.info(Constants.END);
        return ret;
    }

    /**
     * 3.複写先の開始～終了年月を配列化
     *
     * @param in 計画複写の利用票複写処理の入力DTO
     * @return 複写先年月リスト
     */
    private List<String> getCopyToYms(PlanDuplicateUseSlipDuplicateUpdateServiceInDto in) {
        List<String> copyToYms = new ArrayList<>();
        kghCmnTok1Logic.kikanGetUpper(in.getStartYm(), in.getEndYm(), copyToYms);
        return copyToYms;
    }

    /**
     * 4.利用者ループ
     *
     * @param in        計画複写の利用票複写処理の入力DTO
     * @param copyToYms 複写先年月リスト
     * @param out       計画複写の利用票複写処理の出力DTO
     * @throws ExclusiveException 排他例外
     */
    private void doDuplicate(PlanDuplicateUseSlipDuplicateUpdateServiceInDto in, List<String> copyToYms,
            PlanDuplicateUseSlipDuplicateUpdateServiceOutDto out) throws ExclusiveException {
        int trueCntInt = 0; // 成功行数
        int difCntInt = 0; // 変更行数
        List<String> difUserIds = new ArrayList<>(); // 変更した利用者IDの配列
        out.setDifUserid(difUserIds);
        List<String> copiedYms = new ArrayList<>();// 複写した年月の配列
        out.setCpUsermonth(copiedYms);
        List<Gui01150CalcDto> calcSetList = new ArrayList<>(); // 計算リスト
        out.setCalcSetList(calcSetList);

        List<String> userIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(in.getUserId())) {
            userIds.addAll(in.getUserId());
        }
        // outer:
        for (String userId : userIds) {
            for (String copyToYm : copyToYms) {
                int[] result = doDuplicateAction(in, userId, copyToYm);
                if (result[CommonConstants.INT_0] == CommonConstants.INT_1) {
                    // ヘッダ情報取得
                    KghCmnKyuCopy4calcGetHeadByCriteriaInEntity inEntity = new KghCmnKyuCopy4calcGetHeadByCriteriaInEntity();
                    inEntity.setAlShi(CommonDtoUtil.strValToInt(in.getShienId()));
                    inEntity.setAsYm(copyToYm);
                    inEntity.setAlUsr(CommonDtoUtil.strValToInt(userId));
                    List<KghCmnKyuCopy4calcGetHeadOutEntity> heads = cmnTucPlanSelectMapper
                            .findKghCmnKyuCopy4calcGetHeadByCriteria(inEntity);

                    // 計算リストにデータを追加
                    for (KghCmnKyuCopy4calcGetHeadOutEntity head : heads) {
                        Gui01150CalcDto calcInfo = new Gui01150CalcDto();
                        calcInfo.setShienId(in.getShienId());
                        calcInfo.setYymmYm(copyToYm);
                        calcInfo.setUserid(userId);
                        calcInfo.setYymmD(head.getYymmD());
                        calcSetList.add(calcInfo);
                    }

                    trueCntInt++;
                    copiedYms.add(copyToYm);
                    if (result[CommonConstants.INT_1] != CommonConstants.INT_0) {
                        // 要介護度差異区分 <> 0の場合
                        if (!difUserIds.contains(userId)) {
                            difUserIds.add(userId);
                        }
                    }
                }
                // 該当ケースなし
                /*
                 * else if (result == CommonConstants.INT_MINUS_4) { break outer; }
                 */
            }
        }

        out.setTrueCnt(CommonDtoUtil.objValToString(trueCntInt));
        out.setDifCnt(CommonDtoUtil.objValToString(difCntInt));
    }

    /**
     * 複写処理を行う
     *
     * @param in       計画複写の利用票複写処理の入力DTO
     * @param userId   利用者ID
     * @param copyToYm 複写先年月
     * @return 処理結果 [0]:戻り値,[1]:要介護度差異区分
     * @throws ExclusiveException 排他例外
     */
    private int[] doDuplicateAction(PlanDuplicateUseSlipDuplicateUpdateServiceInDto in, String userId, String copyToYm)
            throws ExclusiveException {
        int[] ret = new int[CommonConstants.INT_2];

        // 1.引数チェック
        // 1.1.利用者ID
        Integer userIdInt = CommonDtoUtil.strValToInt(userId);
        if (userIdInt == null || userIdInt <= CommonConstants.INT_0) {
            ret[CommonConstants.INT_0] = CommonConstants.INT_MINUS_1;
            return ret;
        }
        // 1.2.複写元年月
        String copyFromYm = in.getFromYm();
        if (copyFromYm == null || CommonDtoUtil.checkStringEqual(copyFromYm, CommonConstants.BLANK_STRING)) {
            ret[CommonConstants.INT_0] = CommonConstants.INT_MINUS_1;
            return ret;
        }
        // 1.3.複写先年月
        if (copyToYm == null || CommonDtoUtil.checkStringEqual(copyToYm, CommonConstants.BLANK_STRING)) {
            ret[CommonConstants.INT_0] = CommonConstants.INT_MINUS_1;
            return ret;
        }
        // 1.4.複写方法
        String copyMode = in.getCopyMode();
        if (copyMode == null || (!CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_1)
                && !CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_2)
                && !CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_3))) {
            ret[CommonConstants.INT_0] = CommonConstants.INT_MINUS_1;
            return ret;
        }
        // 1.5.短期入所複写方法
        String copyTnki = in.getCopyTnki();
        if (copyTnki == null || (!CommonDtoUtil.checkStringEqual(copyTnki, CommonConstants.STR_1)
                && !CommonDtoUtil.checkStringEqual(copyTnki, CommonConstants.STR_2)
                && !CommonDtoUtil.checkStringEqual(copyTnki, CommonConstants.STR_3))) {
            ret[CommonConstants.INT_0] = CommonConstants.INT_MINUS_1;
            return ret;
        }
        // 1.6.複写先利用票状況
        String copyJokyo = in.getCopyJokyo();
        if (copyJokyo == null || (!CommonDtoUtil.checkStringEqual(copyJokyo, CommonConstants.STR_1)
                && !CommonDtoUtil.checkStringEqual(copyJokyo, CommonConstants.STR_2)
                && !CommonDtoUtil.checkStringEqual(copyJokyo, CommonConstants.STR_3))) {
            ret[CommonConstants.INT_0] = CommonConstants.INT_MINUS_1;
            return ret;
        }

        // 2.初期化
        // 有効期間ID
        Integer copyToTermIdDef = getDefaultTermId(copyToYm);
        // 複写先の月末日を取得
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM);
        YearMonth yearMonth = YearMonth.parse(copyToYm, formatter);
        int lastDay = yearMonth.lengthOfMonth();
        // 複写対象日(default)
        List<Integer> allowedCopyDays = new ArrayList<>();
        for (String targetDay : in.getDays()) {
            Integer targetDayInt = CommonDtoUtil.strValToInt(targetDay);
            if (targetDayInt == null) {
                continue;
            }
            if (targetDayInt > lastDay) {
                continue;
            }

            allowedCopyDays.add(targetDayInt);
        }

        // 3.情報取得
        // 3.1.複写元の利用票ヘッダ情報取得
        KghCmnRiyou2HeaderByCriteriaInEntity copyFromHeadEntity = new KghCmnRiyou2HeaderByCriteriaInEntity();
        copyFromHeadEntity.setShienId(CommonDtoUtil.strValToInt(in.getShienId()));
        copyFromHeadEntity.setTeiYm(in.getFromYm());
        copyFromHeadEntity.setUserId(CommonDtoUtil.strValToInt(userId));
        List<KghCmnRiyou2HeaderOutEntity> copyFromHeads = cmnTucPlanSelectMapper
                .findKghCmnRiyou2HeaderByCriteria(copyFromHeadEntity);
        if (CollectionUtils.isEmpty(copyFromHeads)) {
            ret[CommonConstants.INT_0] = CommonConstants.INT_1;
            return ret;
        }

        // 3.2.複写元の利用票明細情報取得
        List<KghCmnYojituListOutEntity> copyFromPlans = new ArrayList<>();
        for (KghCmnRiyou2HeaderOutEntity head : copyFromHeads) {
            KghCmnYojituListByCriteriaInEntity copyFromEntity = new KghCmnYojituListByCriteriaInEntity();
            copyFromEntity.setShienId(CommonDtoUtil.strValToInt(in.getShienId()));
            copyFromEntity.setUserid(userIdInt);
            copyFromEntity.setTeiYm(in.getFromYm());
            copyFromEntity.setTeiYmd(head.getYymmD());
            copyFromPlans.addAll(cmnTucPlanRiyouSelectMapper.findKghCmnYojituListByCriteria(copyFromEntity));
        }
        if (CollectionUtils.isEmpty(copyFromPlans)) {
            ret[CommonConstants.INT_0] = CommonConstants.INT_1;
            return ret;
        }

        // 3.3.複写元年月の利用者の介護保険情報を取得
        int copyFromHokenCount = CommonConstants.INT_MINUS_1;
        GetUserInfoMonthOutDto userInfo = kghCmn03gFunc01Logic.getUserinfoMonth(userIdInt, in.getFromYm(),
                CommonDtoUtil.strValToInt(in.getShienId()));
        if (userInfo != null && userInfo.getUsrInfo() != null && userInfo.getUsrInfo().getHoken() != null) {
            copyFromHokenCount = userInfo.getUsrInfo().getHoken().size();
        }
        if (copyFromHokenCount <= CommonConstants.INT_0
                && CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_0)) {
            ret[CommonConstants.INT_0] = CommonConstants.INT_MINUS_1;
            return ret;
        }
        /*
         * else if (copyFromHokenCount <= CommonConstants.INT_0 &&
         * CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_1)) {
         * copyFromHokenCount = CommonConstants.INT_1; }
         */

        // 3.4.複写先年月の利用者の介護保険情報を取得
        int copyToHokenCount = CommonConstants.INT_MINUS_1;
        List<CmnUsrHok3gOutDto> copyToHokens = new ArrayList<>();
        userInfo = kghCmn03gFunc01Logic.getUserinfoMonth(userIdInt, copyToYm,
                CommonDtoUtil.strValToInt(in.getShienId()));
        if (userInfo != null && userInfo.getUsrInfo() != null && userInfo.getUsrInfo().getHoken() != null) {
            copyToHokenCount = userInfo.getUsrInfo().getHoken().size();
            copyToHokens.addAll(userInfo.getUsrInfo().getHoken());
        }
        if (copyToHokenCount <= CommonConstants.INT_0
                && CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_0)) {
            ret[CommonConstants.INT_0] = CommonConstants.INT_MINUS_1;
            return ret;
        } else if (copyToHokenCount <= CommonConstants.INT_0
                && CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_1)) {
            copyToHokenCount = CommonConstants.INT_1;

            CmnUsrHok3gOutDto hoken = getCmnUsrHok3gOutDto(copyToYm);
            copyToHokens.add(hoken);
        }

        // 3.5.複写元年月の利用者の日付毎の要介護度を取得
        List<Integer> copyFromYokaiDays = new ArrayList<>();
        YokaiDaysOutDto copyFromYokaiDaysInfo = kghCmn03gFunc01Logic.getYokaiDays(userIdInt, in.getFromYm());
        if (copyFromYokaiDaysInfo.getResult() < CommonConstants.INT_0) {
            for (int i = CommonConstants.INT_0; i < CommonConstants.INT_31; i++) {
                copyFromYokaiDays.add(CommonConstants.INT_0);
            }
        } else {
            copyFromYokaiDays.addAll(copyFromYokaiDaysInfo.getYokaiDaysList());
        }

        // 3.6.複写先年月の利用者の日付毎の要介護度を取得
        List<Integer> copyToYokaiDays = new ArrayList<>();
        YokaiDaysOutDto copyToYokaiDaysInfo = kghCmn03gFunc01Logic.getYokaiDays(userIdInt, copyToYm);
        if (copyToYokaiDaysInfo.getResult() < CommonConstants.INT_0
                && CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_0)) {
            ret[CommonConstants.INT_0] = CommonConstants.INT_MINUS_1;
            return ret;
        } else if (copyToYokaiDaysInfo.getResult() < CommonConstants.INT_0
                && CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_1)) {
            copyToYokaiDays.addAll(copyFromYokaiDays);
        } else {
            copyToYokaiDays.addAll(copyToYokaiDaysInfo.getYokaiDaysList());
        }

        // 3.7.複写先年月の利用登録の有無をチェック
        ComTucSvriyoUseridByCriteriaInEntity riyouCheckEntity = new ComTucSvriyoUseridByCriteriaInEntity();
        riyouCheckEntity.setAlUserid(userIdInt);
        riyouCheckEntity.setAlShienId(in.getShienId());
        riyouCheckEntity.setLsYmdEd(copyToYm + CommonConstants.STRING_SLASH + CommonDtoUtil.objValToString(lastDay));
        riyouCheckEntity.setLsYmdSt(copyToYm + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART);
        List<ComTucSvriyoUseridOutEntity> riyouCheckInfos = comTucSvriyoSelectMapper
                .findComTucSvriyoUseridByCriteria(riyouCheckEntity);
        if (CollectionUtils.isEmpty(riyouCheckInfos) || riyouCheckInfos.getFirst().getUserid() == null
                || riyouCheckInfos.getFirst().getUserid() <= CommonConstants.INT_0) {
            ret[CommonConstants.INT_0] = CommonConstants.INT_MINUS_1;
            return ret;
        }

        // 3.8.複写元の利用票明細の抽出
        List<KghCmnYojituListOutEntity> copyFromMergedPlans = new ArrayList<>();
        if (copyToHokenCount == CommonConstants.INT_1) {
            copyFromMergedPlans.addAll(copyFromPlans);
        } else {
            // 複写元利用票ﾍｯﾀﾞDS
            List<KghCmnRiyouhYouMergeDto> adsPlan = new ArrayList<>();
            copyFromHeads.forEach((copyFromHead) -> {
                KghCmnRiyouhYouMergeDto inEntity = new KghCmnRiyouhYouMergeDto();
                inEntity.setCmnTucPlanYymmD(copyFromHead.getYymmD());

                adsPlan.add(inEntity);
            });
            // ﾏｰｼﾞ内容格納DS
            List<KghCmnRiyouhYouMergeDto> adsZ = new ArrayList<>();
            kghCmnCpmv01Logic.riyouhYouMerge(CommonDtoUtil.strValToInt(in.getShienId()), userIdInt, in.getFromYm(),
                    adsPlan, adsZ);
            adsZ.forEach((out) -> {
                KghCmnYojituListOutEntity outEntity = new KghCmnYojituListOutEntity();
                outEntity.setYymmD(out.getCmnTucPlanYymmD());
                outEntity.setSvJigyoId(out.getSvJigyoId());
                outEntity.setSvItemCd(out.getSvItemCd());
                outEntity.setSvEndTime(out.getSvEndTime());
                outEntity.setSvStartTime(out.getSvStartTime());
                outEntity.setOyaLineNo(out.getOyaLineNo());
                outEntity.setYDay01(out.getYday01());
                outEntity.setYDay02(out.getYday02());
                outEntity.setYDay03(out.getYday03());
                outEntity.setYDay04(out.getYday04());
                outEntity.setYDay05(out.getYday05());
                outEntity.setYDay06(out.getYday06());
                outEntity.setYDay07(out.getYday07());
                outEntity.setYDay08(out.getYday08());
                outEntity.setYDay09(out.getYday09());
                outEntity.setYDay10(out.getYday10());
                outEntity.setYDay11(out.getYday11());
                outEntity.setYDay12(out.getYday12());
                outEntity.setYDay13(out.getYday13());
                outEntity.setYDay14(out.getYday14());
                outEntity.setYDay15(out.getYday15());
                outEntity.setYDay16(out.getYday16());
                outEntity.setYDay17(out.getYday17());
                outEntity.setYDay18(out.getYday18());
                outEntity.setYDay19(out.getYday19());
                outEntity.setYDay20(out.getYday20());
                outEntity.setYDay21(out.getYday21());
                outEntity.setYDay22(out.getYday22());
                outEntity.setYDay23(out.getYday23());
                outEntity.setYDay24(out.getYday24());
                outEntity.setYDay25(out.getYday25());
                outEntity.setYDay26(out.getYday26());
                outEntity.setYDay27(out.getYday27());
                outEntity.setYDay28(out.getYday28());
                outEntity.setYDay29(out.getYday29());
                outEntity.setYDay30(out.getYday30());
                outEntity.setYDay31(out.getYday31());
                outEntity.setYTotal(out.getYtotal());
                copyFromMergedPlans.add(outEntity);
            });
        }
        copyFromMergedPlans.sort(Comparator.comparingInt(KghCmnYojituListOutEntity::getOyaLineNo)
                .thenComparing(KghCmnYojituListOutEntity::getGouseiSikKbn)
                .thenComparing(KghCmnYojituListOutEntity::getSvcode));
        Integer maxOyaLineNo = copyFromMergedPlans.getLast().getOyaLineNo();

        // 3.9.複写先の利用票ヘッダ情報取得
        KghCmnRiyou2HeaderByCriteriaInEntity copyToHeadEntity = new KghCmnRiyou2HeaderByCriteriaInEntity();
        copyToHeadEntity.setShienId(CommonDtoUtil.strValToInt(in.getShienId()));
        copyToHeadEntity.setTeiYm(copyToYm);
        copyToHeadEntity.setUserId(CommonDtoUtil.strValToInt(userId));
        List<KghCmnRiyou2HeaderOutEntity> copyToHeads = cmnTucPlanSelectMapper
                .findKghCmnRiyou2HeaderByCriteria(copyToHeadEntity);

        // 3.10.利用料の複写元データを検索
        KghCmnCpmvTucPlanGaiFutanByCriteriaInEntity copyFromFutanEntity = new KghCmnCpmvTucPlanGaiFutanByCriteriaInEntity();
        copyFromFutanEntity.setAlShienId(CommonDtoUtil.strValToInt(in.getShienId()));
        copyFromFutanEntity.setAlUserid(userIdInt);
        copyFromFutanEntity.setAsYymm(in.getFromYm());
        List<KghCmnCpmvTucPlanGaiFutanOutEntity> copyFromFutans = cmnTucPlanGaiFutanSelectMapper
                .findKghCmnCpmvTucPlanGaiFutanByCriteria(copyFromFutanEntity);
        Set<String> copyToFutanKeys = new HashSet<>(); // 複写先利用料のキーリスト

        // 3.11.社福減免の複写元データを検索
        KghCmnCpmvTucPlanSyafukuKeigenByCriteriaInEntity copyFromFukuEntity = new KghCmnCpmvTucPlanSyafukuKeigenByCriteriaInEntity();
        copyFromFukuEntity.setAlShienId(CommonDtoUtil.strValToInt(in.getShienId()));
        copyFromFukuEntity.setAlUserid(userIdInt);
        copyFromFukuEntity.setAsYymm(in.getFromYm());
        List<KghCmnCpmvTucPlanSyafukuKeigenOutEntity> copyFromFukus = cmnTucPlanSyafukuKeigenSelectMapper
                .findKghCmnCpmvTucPlanSyafukuKeigenByCriteria(copyFromFukuEntity);
        Set<String> copyToFukuKeys = new HashSet<>(); // 複写先社福軽減のキーリスト

        // 3.12.要介護度の差異のチェック
        boolean copyFromYokaiDaysDiff = copyFromYokaiDays.stream().distinct().count() != CommonConstants.INT_1;
        boolean copyToYokaiDaysDiff = copyToYokaiDays.stream().distinct().count() != CommonConstants.INT_1;
        if (copyFromYokaiDaysDiff && copyToYokaiDaysDiff) {
            // 要介護度差異区分 = 2
            ret[CommonConstants.INT_1] = CommonConstants.INT_2;
        } else if (!copyFromYokaiDays.getFirst().equals(copyToYokaiDays.getFirst())) {
            // 要介護度差異区分 = 1
            ret[CommonConstants.INT_1] = CommonConstants.INT_1;
        }

        // 4.複写先年月で有効な被保険者番号でのループ
        List<CmnTucPlanRiyou> ids_detail_to_bk = new ArrayList<>();
        for (int ll_cnt = CommonConstants.INT_1; ll_cnt <= copyToHokens.size(); ll_cnt++) {
            CmnUsrHok3gOutDto copyToHoken = copyToHokens.get(ll_cnt - CommonConstants.INT_1);
            // 4.1.変数設定
            // 変更日
            String modifiedDay;
            DateTimeFormatter ymdFormatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
            YearMonth hokenYearMonth = YearMonth.parse(copyToHoken.getStartYmd(), ymdFormatter);
            if (!hokenYearMonth.isBefore(yearMonth)) {
                // LEFT(変数.介護保険有効情報開始日, 7) < 変数.複写先年月の場合、
                // ・変数.変更日 = "01"
                // LEFT(変数.介護保険有効情報開始日, 7) >= 変数.複写先年月の場合、
                // ・変数.変更日 = RIGHT(変数.介護保険有効情報開始日, 2)
                modifiedDay = copyToHoken.getStartYmd()
                        .substring(copyToHoken.getStartYmd().length() - CommonConstants.INT_2);
            } else {
                modifiedDay = CommonConstants.MONTHSTART;
            }
            // 介護保険有効情報開始日
            LocalDate hokenStartYmd = LocalDate.parse(copyToHoken.getStartYmd(), ymdFormatter);
            // 介護保険有効情報終了日
            LocalDate hokenEndYmd = null;
            if (copyToHoken.getEndYmd() != null && !copyToHoken.getEndYmd().isEmpty()) {
                hokenEndYmd = LocalDate.parse(copyToHoken.getEndYmd(), ymdFormatter);
            }
            // 要介護度
            Integer careLevel = copyToHoken.getNintei().getFirst().getYokaiKbn();

            // 4.2.複写先の利用票の登録状況の確認
            if (CollectionUtils.isNotEmpty(copyToHeads)) {
                // 複写先の利用票ヘッダが存在する場合のみ、処理実行。
                if (copyJokyo.equals(CommonConstants.STR_1)) {
                    // 1：未作成のみ
                    continue;
                }

                // 予定済フラグが1のレコードを検索
                boolean hasYotei = copyToHeads.stream()
                        .anyMatch(obj -> CommonConstants.NUMBER_1.equals(obj.getYoteiZumiFlg()));
                // 実績済フラグが1のレコードを検索
                boolean hasJisseki = copyToHeads.stream()
                        .anyMatch(obj -> CommonConstants.NUMBER_1.equals(obj.getJissekiZumiFlg()));

                if (copyJokyo.equals(CommonConstants.STR_2) && (hasYotei || hasJisseki)) {
                    // 2：作成中まで
                    continue;
                }

                if (copyJokyo.equals(CommonConstants.STR_3) && hasJisseki) {
                    // 3：予定済まで
                    continue;
                }
            }

            // 4.3.サービス提供年月（変更日）の検索
            List<KghCmnRiyou2HeaderOutEntity> tempCopyToHeads = copyToHeads.stream()
                    .filter(obj -> modifiedDay.equals(obj.getYymmD())).toList();

            // 4.4.複写先の利用料データがあれば削除
            KghCmnPrnBetuS3RiyouByCriteriaInEntity copyToRiyouEntity = new KghCmnPrnBetuS3RiyouByCriteriaInEntity();
            copyToRiyouEntity.setAlShienId(CommonDtoUtil.strValToInt(in.getShienId()));
            copyToRiyouEntity.setAlUserid(userIdInt);
            copyToRiyouEntity.setAsYymmYm(copyToYm);
            copyToRiyouEntity.setAsYymmD(modifiedDay);
            List<KghCmnPrnBetuS3RiyouOutEntity> copyToRiyous = cmnTucPlanGaiFutanSelectMapper
                    .findKghCmnPrnBetuS3RiyouByCriteria(copyToRiyouEntity);

            for (int i = 0; i < copyToRiyous.size(); i++) {
                // 利用票別表 保険外負担の該当データの論理削除
                CmnTucPlanGaiFutanCriteria criteria = new CmnTucPlanGaiFutanCriteria();
                criteria.createCriteria().andShienIdEqualTo(CommonDtoUtil.strValToInt(in.getShienId()))
                        .andUseridEqualTo(userIdInt).andYymmYmEqualTo(copyToYm).andYymmDEqualTo(modifiedDay);
                int count = cmnTucPlanGaiFutanMapper.deleteByCriteria(criteria);
                if (count <= 0) {
                    throw new ExclusiveException();
                }
            }

            // 4.5.複写先の社福軽減データがあれば削除
            KghCmnBeppyo3SyafukuByCriteriaInEntity copyToSyaFukuEntity = new KghCmnBeppyo3SyafukuByCriteriaInEntity();
            copyToSyaFukuEntity.setAlShienId(CommonDtoUtil.strValToInt(in.getShienId()));
            copyToSyaFukuEntity.setAlUserid(userIdInt);
            copyToSyaFukuEntity.setAsYymmYm(copyToYm);
            copyToSyaFukuEntity.setAsYymmD(modifiedDay);
            List<KghCmnBeppyo3SyafukuOutEntity> copyToSyaFukus = cmnTucPlanSyafukuKeigenSelectMapper
                    .findKghCmnBeppyo3SyafukuByCriteria(copyToSyaFukuEntity);
            for (int i = 0; i < copyToSyaFukus.size(); i++) {
                // 利用票別表 社福軽減の該当データの論理削除
                CmnTucPlanSyafukuKeigenCriteria criteria = new CmnTucPlanSyafukuKeigenCriteria();
                criteria.createCriteria().andShienIdEqualTo(CommonDtoUtil.strValToInt(in.getShienId()))
                        .andUseridEqualTo(userIdInt).andYymmYmEqualTo(copyToYm).andYymmDEqualTo(modifiedDay);
                int count = cmnTucPlanSyafukuKeigenMapper.deleteByCriteria(criteria);
                if (count <= 0) {
                    throw new ExclusiveException();
                }
            }
            // 4.6.複写先の利用票明細データがあれば削除
            KghCmnYojituListByCriteriaInEntity copyToDetailEntity = new KghCmnYojituListByCriteriaInEntity();
            copyToDetailEntity.setShienId(CommonDtoUtil.strValToInt(in.getShienId()));
            copyToDetailEntity.setUserid(userIdInt);
            copyToDetailEntity.setTeiYm(copyToYm);
            copyToDetailEntity.setTeiYmd(modifiedDay);
            List<KghCmnYojituListOutEntity> copyToDetails = cmnTucPlanRiyouSelectMapper
                    .findKghCmnYojituListByCriteria(copyToDetailEntity);
            for (int i = 0; i < copyToDetails.size(); i++) {
                CmnTucPlanRiyouCriteria criteria = new CmnTucPlanRiyouCriteria();
                criteria.createCriteria().andShienIdEqualTo(CommonDtoUtil.strValToInt(in.getShienId()))
                        .andUseridEqualTo(userIdInt).andYymmYmEqualTo(copyToYm).andYymmDEqualTo(modifiedDay);
                int count = cmnTucPlanRiyouMapper.deleteByCriteria(criteria);
                if (count <= 0) {
                    throw new ExclusiveException();
                }
            }
            // 4.6.複写先の利用票明細データがあれば削除
            // 4.7.複写先年月での複写許可されている提供日の設定
            List<Integer> targetCopyDays = new ArrayList<>();
            for (Integer targetDay : allowedCopyDays) {
                LocalDate copyToDate = yearMonth.atDay(targetDay);

                if (hokenStartYmd.isAfter(copyToDate)) {
                    // 介護保険有効情報開始日 > 複写先年月日
                    continue;
                }
                if (hokenEndYmd != null && hokenEndYmd.isBefore(copyToDate)) {
                    // 介護保険有効情報終了日 < 複写先年月日
                    continue;
                }
                if (CommonConstants.NUMBER_0.equals(copyToYokaiDays.getFirst()) && !copyToYokaiDaysDiff
                        && CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_0)) {
                    // 写先年月の1～31日それぞれの日の要介護度[ループ２行] = 0 かつ 複写モード = 0
                    continue;
                }

                targetCopyDays.add(targetDay);
            }

            // 4.8.複写先の利用票ヘッダ情報の取得
            Integer ll_hen_yokai_kbn = CommonConstants.INT_0; // 要介護度(変更後)
            String ls_hen_yokai_ymd = CommonConstants.EMPTY_STRING; // 要介護度変更日
            CmnUsrNin3gOutDto copyToNintei = copyToHoken.getNintei().getFirst();
            if (CommonConstants.NUMBER_1.compareTo(copyToHoken.getNinteiMax()) > 0) {
                CmnUsrNin3gOutDto tempCopyToNintei = copyToHoken.getNintei().get(copyToHoken.getNinteiMax() - 1);
                ll_hen_yokai_kbn = tempCopyToNintei.getYokaiKbn();
                ls_hen_yokai_ymd = tempCopyToNintei.getStartYmd();

                Integer careLevelCompareRet = kghCmpF01Logic.diffYokaiKbn(careLevel, ll_hen_yokai_kbn);
                if (careLevelCompareRet > 0) {
                    copyToNintei = tempCopyToNintei;
                }
            }

            // 限度額適用期間(開始)
            LocalDate nintenStartYmd = LocalDate.parse(copyToNintei.getStartYmd(), ymdFormatter);

            // 4.9.複写元のサービスの件数でループを回す
            int copyFromMergedPlanDefSize = copyFromMergedPlans.size();
            List<CmnTucPlanRiyou> newCopyToList = new ArrayList<>();
            for (int index = 0; index < copyFromMergedPlans.size(); index++) {
                KghCmnYojituListOutEntity copyFromMergedPlan = copyFromMergedPlans.get(index);
                // 複写元サービス項目ID
                Integer copyFromSvItemCd = copyFromMergedPlan.getSvItemCd();
                // 複写元サービスコード
                String copyFromScode = copyFromMergedPlan.getScode();
                // 複写元サービス種別コード
                String copyFromSvtype = copyFromMergedPlan.getSvtype();
                if (copyFromSvtype == null) {
                    copyFromSvtype = copyFromScode.substring(0, CommonConstants.INT_2);
                }
                // 複写元サービス項目コード
                String copyFromSvcode = copyFromMergedPlan.getSvcode();
                if (copyFromSvcode == null) {
                    copyFromSvcode = copyFromScode.substring(CommonConstants.INT_2, CommonConstants.INT_6);
                }
                // 短期入所
                boolean isTanki = CommonConstants.SV_CODE_LIST_1.contains(copyFromSvtype);
                if (isTanki && CommonDtoUtil.checkStringEqual(copyTnki, CommonConstants.STR_3)) {
                    continue;
                }

                // 複写先のｻｰﾋﾞｽ･有効期間IDの変換
                // 複写先有効期間ID
                Integer copyToTermId = copyToTermIdDef;
                // サービス項目ID
                Integer svItemCd = copyFromSvItemCd;

                // 複写先サービス項目ID
                Integer copyToSvItemCd = null;
                // 複写先サービスコード
                String copyToScode = null;
                // 複写先サービス項目コード
                String copyToSvcode = null;
                // 事業所の保険者コード
                Integer hokenCd = null;
                // サービス変更累計リスト
                List<ConvertScodeInfo> serviceModMergedInfos = new ArrayList<>();

                Integer convertResult = CommonConstants.INT_1;
                if (svItemCd > 0 && copyFromSvtype.compareTo(CommonConstants.STR_80) < 0
                        && copyFromSvtype.compareTo(CommonConstants.NUM_STR_10) > 0) {
                    // コンバート処理実行
                    if (!copyToTermId.equals(copyFromMergedPlan.getTermid())) {
                        // 複写元有効期間ID <> 複写先有効期間IDの場合
                        ConvertInfoDto convertInfo = new ConvertInfoDto();
                        convertResult = kghCmnCpmv01Logic.ufSvCnv(copyFromMergedPlan.getSvJigyoId(),
                                copyFromMergedPlan.getTermid(), svItemCd, copyToTermId,
                                copyFromMergedPlan.getSvStartTime(), copyFromMergedPlan.getSvEndTime(), convertInfo);
                        copyToSvItemCd = convertInfo.getItm();
                        copyToScode = convertInfo.getSCode();
                        // OUTパラメータなし 複写先分割されたscode 複写先分割されたサービス項目ID
                        if (CommonConstants.NUMBER_1.equals(convertResult)
                                && CommonConstants.NUMBER_0.compareTo(copyToSvItemCd) < 0) {
                            svItemCd = copyToSvItemCd;
                            copyFromSvItemCd = copyToSvItemCd;
                        } else {
                            svItemCd = CommonConstants.INT_0;
                            copyFromSvItemCd = CommonConstants.INT_0;
                        }
                    }

                    if (copyToTermId.equals(CommonConstants.INT_8)) {
                        // 複写先有効期間ID = 8の場合
                        if (CommonConstants.NUM_STR_21.equals(copyFromSvtype)
                                || CommonConstants.NUM_STR_24.equals(copyFromSvtype)) {
                            Convert21OutDto convertRet = kghCmnCpmv01Logic.convert21(copyFromMergedPlan.getSvJigyoId(),
                                    copyToTermId, copyToYm);
                            convertResult = Long.valueOf(convertRet.getReturnCode()).intValue();
                            copyToSvItemCd = Long.valueOf(convertRet.getItemCode()).intValue();
                            if (CommonConstants.NUMBER_1.equals(convertResult)
                                    && CommonConstants.NUMBER_0.compareTo(copyToSvItemCd) < 0) {
                                svItemCd = copyToSvItemCd;
                                copyFromSvItemCd = copyToSvItemCd;
                            }
                        } else if (CommonConstants.NUM_STR_15.equals(copyFromSvtype)
                                || CommonConstants.SV_KIND_CD_78.equals(copyFromSvtype)) {
                            SvItemcodeOutDto convertRet = kghCmpSvCnvLogic
                                    .cnvSvItemcode(copyFromMergedPlan.getSvJigyoId(), copyToTermId, svItemCd, copyToYm);
                            convertResult = convertRet.getResult();
                            copyToSvItemCd = convertRet.getSvCode();
                            copyToScode = convertRet.getScode();
                            if (CommonConstants.NUMBER_1.equals(convertResult)) {
                                svItemCd = copyToSvItemCd;
                                copyFromSvItemCd = copyToSvItemCd;
                            } else {
                                svItemCd = CommonConstants.INT_0;
                                copyFromSvItemCd = CommonConstants.INT_0;
                            }
                        }
                    } else if (copyToTermId.equals(CommonConstants.INT_10)) {
                        // 複写先有効期間ID = 10の場合
                        if (CommonConstants.NUM_STR_11.equals(copyFromSvtype)
                                || CommonConstants.NUM_STR_31.equals(copyFromSvtype)
                                || CommonConstants.NUM_STR_34.equals(copyFromSvtype)) {
                            SvItemcodeOutDto convertRet = kghCmpSvCnvLogic
                                    .cnvSvItemcode(copyFromMergedPlan.getSvJigyoId(), copyToTermId, svItemCd, copyToYm);
                            convertResult = convertRet.getResult();
                            copyToSvItemCd = convertRet.getSvCode();
                            copyToScode = convertRet.getScode();
                            if (CommonConstants.NUMBER_1.equals(convertResult)) {
                                svItemCd = copyToSvItemCd;
                                copyFromSvItemCd = copyToSvItemCd;
                            } else {
                                svItemCd = CommonConstants.INT_0;
                                copyFromSvItemCd = CommonConstants.INT_0;
                            }
                        }
                    } else if (copyToTermId.compareTo(CommonConstants.INT_12) >= 0) {
                        // 複写先有効期間ID >= 12の場合
                        convertResult = CommonConstants.INT_0;
                        if (copyToTermId.equals(CommonConstants.INT_12)) {
                            // 複写先有効期間ID = 12の場合
                            // termid = 12内でコンバートが必要なサービスかどうかを判断する
                            convertResult = ufTargetCnvTerm12(copyFromSvtype, copyFromScode, copyToYm);
                        } else if (copyToTermId.equals(CommonConstants.INT_13)) {
                            // 複写先有効期間ID = 13の場合
                            // termid = 13内でコンバートが必要なサービスかどうかを判断する
                            convertResult = ufTargetCnvTerm13(copyFromSvtype, copyFromScode, copyToYm);
                        } else if (copyToTermId.equals(CommonConstants.INT_16)) {
                            // 複写先有効期間ID = 16の場合
                            // termid = 16内でコンバートが必要なサービスかどうかを判断する
                            convertResult = ufTargetCnvTerm16(copyFromSvtype, copyFromScode, copyToYm);
                        }

                        if (convertResult.equals(CommonConstants.INT_1)) {
                            // チェック用戻り値 = 1の場合
                            // サービスのコンバート：(同term期間内)
                            SvItemcodeOutDto convertRet = kghCmpSvCnvLogic
                                    .cnvSvItemcode(copyFromMergedPlan.getSvJigyoId(), copyToTermId, svItemCd, copyToYm);
                            convertResult = convertRet.getResult();
                            copyToSvItemCd = convertRet.getSvCode();
                            if (CommonConstants.NUMBER_1.equals(convertResult)) {
                                svItemCd = copyToSvItemCd;
                                copyFromSvItemCd = copyToSvItemCd;
                            } else {
                                svItemCd = CommonConstants.INT_0;
                                copyFromSvItemCd = CommonConstants.INT_0;
                            }
                        }
                    }
                } else {
                    // 総合事業サービスの場合
                    // コンバート処理は行わない
                    if (svItemCd > 0 && kghCmnF01Logic.chkSougouSvtype(copyFromSvtype)) {
                        SvKHokenCdByCriteriaInEntity svEntity = new SvKHokenCdByCriteriaInEntity();
                        svEntity.setAlJid(CommonDtoUtil.objValToString(copyFromMergedPlan.getSvJigyoId()));
                        List<SvKHokenCdOutEntity> svs = comMscSvjigyoSelectMapper.findSvKHokenCdByCriteria(svEntity);
                        if (!svs.isEmpty()) {
                            hokenCd = svs.getFirst().getKHokenCd();
                        }
                        if (CommonConstants.NUM_STR_35
                                .equals(copyFromScode.substring(CommonConstants.INT_0, CommonConstants.INT_2))) {
                            // 「関数_利用票サービス項目ID取得（総合）」シート
                            int[] rets = ufGetItemcdSougou(hokenCd, CommonConstants.NUM_STR_35, copyFromSvcode,
                                    copyToYm + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART, copyToTermId);
                            convertResult = rets[CommonConstants.INT_0];
                            copyToSvItemCd = rets[CommonConstants.INT_1];
                            copyToSvcode = copyFromSvcode;
                        } else {
                            ConvertInfoDto convertInfo = new ConvertInfoDto();
                            convertResult = kghCmnCpmv01Logic.ufSvCnv(copyFromMergedPlan.getSvJigyoId(),
                                    copyFromMergedPlan.getTermid(), svItemCd, copyToTermId,
                                    CommonConstants.EMPTY_STRING, CommonConstants.EMPTY_STRING, convertInfo);
                            copyToSvItemCd = convertInfo.getItm();
                            copyToScode = convertInfo.getSCode();
                            copyToSvcode = copyToScode.substring(CommonConstants.INT_2, CommonConstants.INT_6);
                            // OUTパラメータなし 複写先分割されたscode 複写先分割されたサービス項目ID

                            // 2017/3/1 itagaki G3S38L178_H29介護_ケアマネ_利用票_複写 変換後のsvcodeで期間に該当するitemcodeの取得
                            // 「関数_利用票サービス項目ID取得（総合）」シート
                            int[] rets = ufGetItemcdSougou(hokenCd, copyFromSvtype, copyToSvcode,
                                    copyToYm + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART, copyToTermId);
                            convertResult = rets[CommonConstants.INT_0];
                            copyToSvItemCd = rets[CommonConstants.INT_1];
                        }

                        if (CommonConstants.NUMBER_1.equals(convertResult)) {
                            svItemCd = copyToSvItemCd;
                            copyFromSvcode = copyToSvcode;
                        } else {
                            // 複写先にitemcodeがない場合は0をセット
                            svItemCd = CommonConstants.INT_0;
                            copyFromSvItemCd = CommonConstants.INT_0;
                        }
                    }
                }

                if (!CommonConstants.NUMBER_1.equals(convertResult)) {
                    if (kghCmnF01Logic.chkTermSvtype(copyFromSvtype,
                            copyToYm + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART)) {
                        // 種別が終了していない場合は未選択に変換するため、成功に変える
                        convertResult = CommonConstants.NUMBER_1;
                    }
                }

                // サービスのコンバート
                String ls_scode = null; // 複写先ｻｰﾋﾞｽｺｰﾄﾞ
                String ls_svtype = null; // 複写先ｻｰﾋﾞｽ種別ｺｰﾄﾞ
                String ls_svcode = null; // 複写先ｻｰﾋﾞｽ項目ｺｰﾄﾞ

                if (svItemCd > 0) {
                    if (kghCmnF01Logic.chkSougouSvtype(copyFromSvtype)) {
                        if (CommonConstants.NUM_STR_35
                                .equals(copyFromScode.substring(CommonConstants.INT_0, CommonConstants.INT_2))) {
                            ls_scode = kghCmn03gFunc01Logic.getScodeFromItemuseSougou(CommonConstants.NUM_STR_35,
                                    copyFromSvcode,
                                    copyToYm + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART, copyToTermId,
                                    hokenCd);
                        } else {
                            ls_scode = kghCmn03gFunc01Logic.getScodeFromItemuseSougou(copyFromSvtype, copyFromSvcode,
                                    copyToYm + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART, copyToTermId,
                                    hokenCd);
                        }
                    } else {
                        ls_scode = kghCmn03gFunc01Logic.getScodeFromItemuse(copyFromMergedPlan.getSvJigyoId(), svItemCd,
                                copyToYm + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART);
                    }
                } else {
                    ls_svtype = copyFromSvtype;
                }

                // サービス変更リスト
                List<ConvertScodeInfo> serviceModInfos = new ArrayList<>();
                if (svItemCd > 0 && copyFromSvtype.compareTo(CommonConstants.NUM_STR_10) > 0
                        && copyFromSvtype.compareTo(CommonConstants.STR_80) < 0) {
                    // ｻｰﾋﾞｽが選択されている場合
                    if (!copyToTermId.equals(copyFromMergedPlan.getTermid())
                            || copyToTermId.equals(CommonConstants.INT_8)) {
                        // 「関数_利用票サービスコード変換（呼出用）」シート
                        ConvertScodeResult result = ufCnvServiceYok(copyFromSvItemCd,
                                copyToYm + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART, copyToTermId,
                                copyFromMergedPlan.getGouseiSikKbn(), copyToYm, copyToTermId, copyToYokaiDays,
                                copyFromMergedPlan.getOyaLineNo(), copyFromMergedPlan.getSvJigyoId(), copyFromSvtype,
                                copyMode, maxOyaLineNo);
                        convertResult = result.getResult();
                        maxOyaLineNo = result.getMaxOyaLineNo();
                        serviceModInfos.addAll(result.getInfos());
                    } else {
                        // 「関数_利用票サービスコード変換（呼出用）」シート
                        ConvertScodeResult result = ufCnvServiceYok(copyFromSvItemCd,
                                in.getFromYm() + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART,
                                copyToTermId, copyFromMergedPlan.getGouseiSikKbn(), copyToYm, copyToTermId,
                                copyToYokaiDays, copyFromMergedPlan.getOyaLineNo(), copyFromMergedPlan.getSvJigyoId(),
                                copyFromSvtype, copyMode, maxOyaLineNo);
                        convertResult = result.getResult();
                        maxOyaLineNo = result.getMaxOyaLineNo();
                        serviceModInfos.addAll(result.getInfos());
                    }

                    if (CommonConstants.NUMBER_1.equals(convertResult)) {
                        // 変換された → 累積に追加
                        if (!kghCmnF01Logic.chkKasanService2(copyFromSvtype,
                                ls_scode.substring(CommonConstants.INT_0, CommonConstants.INT_6),
                                copyFromMergedPlan.getGouseiSikKbn())) {
                            serviceModMergedInfos.addAll(serviceModInfos);
                        }
                    } else {
                        // 変換されない → 加算sv である
                        serviceModInfos.addAll(serviceModMergedInfos.stream()
                                .filter((obj) -> copyFromMergedPlan.getOyaLineNo().equals(obj.getBaseOyaLineNo()))
                                .toList());
                    }
                } else if (svItemCd > 0 && kghCmnF01Logic.chkSougouSvtype(copyFromSvtype)) {
                    if (!copyToTermId.equals(copyFromMergedPlan.getTermid())
                            || copyToTermId.equals(CommonConstants.INT_8)) {
                        // 「関数_利用票サービスコード変換（呼出用）」シート
                        ConvertScodeResult result = ufCnvServiceYok(copyFromSvItemCd,
                                copyToYm + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART, copyToTermId,
                                copyFromMergedPlan.getGouseiSikKbn(), copyToYm, copyToTermId, copyToYokaiDays,
                                copyFromMergedPlan.getOyaLineNo(), copyFromMergedPlan.getSvJigyoId(), copyFromSvtype,
                                copyMode, maxOyaLineNo);
                        convertResult = result.getResult();
                        maxOyaLineNo = result.getMaxOyaLineNo();
                        serviceModInfos.addAll(result.getInfos());
                    } else {
                        // 「関数_利用票サービスコード変換（呼出用）」シート
                        ConvertScodeResult result = ufCnvServiceYok(copyFromSvItemCd,
                                in.getFromYm() + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART,
                                copyToTermId, copyFromMergedPlan.getGouseiSikKbn(), copyToYm, copyToTermId,
                                copyToYokaiDays, copyFromMergedPlan.getOyaLineNo(), copyFromMergedPlan.getSvJigyoId(),
                                copyFromSvtype, copyMode, maxOyaLineNo);
                        convertResult = result.getResult();
                        maxOyaLineNo = result.getMaxOyaLineNo();
                        serviceModInfos.addAll(result.getInfos());
                    }

                    if (CommonConstants.NUMBER_1.equals(convertResult)) {
                        boolean checkResult = kghCmnF01Logic.chkKasanService2(copyFromSvtype,
                                ls_scode.substring(CommonConstants.INT_0, CommonConstants.INT_6),
                                copyFromMergedPlan.getGouseiSikKbn());
                        if (!checkResult) {
                            serviceModMergedInfos.addAll(serviceModInfos);
                        }
                    } else {
                        serviceModInfos.addAll(serviceModMergedInfos.stream()
                                .filter((obj) -> copyFromMergedPlan.getOyaLineNo().equals(obj.getBaseOyaLineNo()))
                                .toList());
                    }
                } else if (CommonConstants.NUMBER_1.equals(convertResult)) {
                    // ｻｰﾋﾞｽが選択されていない場合
                    if ((copyFromSvtype.compareTo(CommonConstants.NUM_STR_10) > 0
                            && copyFromSvtype.compareTo(CommonConstants.STR_80) < 0)
                            || kghCmnF01Logic.chkSougouSvtype(copyFromSvtype)) {
                        // 加算サービスのチェックを追加
                        if (!kghCmnF01Logic.chkKasanService2(copyFromSvtype,
                                copyFromScode.substring(CommonConstants.INT_0, CommonConstants.INT_6),
                                copyFromMergedPlan.getGouseiSikKbn())) {
                            convertResult = CommonConstants.INT_1;

                            ConvertScodeInfo info = new ConvertScodeInfo();
                            info.setBaseOyaLineNo(copyFromMergedPlan.getOyaLineNo());
                            info.setNewOyaLineNo(copyFromMergedPlan.getOyaLineNo());
                            info.setSvJigyoId(copyFromMergedPlan.getSvJigyoId());
                            info.setSvItemCd(svItemCd);
                            serviceModInfos.add(info);
                            serviceModMergedInfos.addAll(serviceModInfos);

                            int total = 0;
                            for (int i = CommonConstants.INT_1; i <= CommonConstants.INT_31; i++) {
                                Integer tempCareLevel = 0;
                                if (copyToYokaiDays.size() >= i) {
                                    tempCareLevel = copyToYokaiDays.get(i - CommonConstants.INT_1);
                                }
                                if (CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_0)
                                        && CommonConstants.NUMBER_0.equals(tempCareLevel)) {
                                    continue;
                                }

                                info.getDays()[i - 1] = CommonConstants.INT_1;
                                total++;
                            }
                            info.setTotal(total);
                        }
                    } else {
                        // 横だし、総合事業サービスはsv_item_cd = 0 の加算サービスは存在しないので、そのままとする
                        // → 総合事業は存在するので、加算のチェックを行う（上の分岐に移動） //2019/01/22 itagaki G3C40L191_ケアマネ_H3104改正
                        convertResult = CommonConstants.INT_1;

                        ConvertScodeInfo info = new ConvertScodeInfo();
                        info.setBaseOyaLineNo(copyFromMergedPlan.getOyaLineNo());
                        info.setNewOyaLineNo(copyFromMergedPlan.getOyaLineNo());
                        info.setSvJigyoId(copyFromMergedPlan.getSvJigyoId());
                        info.setSvItemCd(svItemCd);
                        serviceModInfos.add(info);
                        serviceModMergedInfos.addAll(serviceModInfos);

                        int total = 0;
                        for (int i = CommonConstants.INT_1; i <= CommonConstants.INT_31; i++) {
                            Integer tempCareLevel = 0;
                            if (copyToYokaiDays.size() >= i) {
                                tempCareLevel = copyToYokaiDays.get(i - CommonConstants.INT_1);
                            }
                            if (CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_0)
                                    && CommonConstants.NUMBER_0.equals(tempCareLevel)) {
                                continue;
                            }

                            info.getDays()[i - 1] = CommonConstants.INT_1;
                            total++;
                        }
                        info.setTotal(total);
                    }
                }

                if (ls_svtype != null && CommonConstants.SV_KIND_CD_81.compareTo(ls_svtype) < 0
                        && CommonConstants.SV_KIND_CD_99.compareTo(ls_svtype) > 0) {
                    // 複写先サービス種別コード >= "81" かつ 複写先サービス種別コード <= "99"の場合
                    copyToTermId = CommonConstants.INT_0;
                }
                if (serviceModInfos.isEmpty() && index < copyFromMergedPlanDefSize) {
                    copyFromMergedPlans.add(copyFromMergedPlan);
                }

                for (ConvertScodeInfo serviceModInfo : serviceModInfos) {
                    List<Integer> tempTargetCopyDays = new ArrayList<>(targetCopyDays);
                    // Integer tempNewOyaLineNo = serviceModInfo.getNewOyaLineNo();
                    Integer ll_sv_jigyo_id = serviceModInfo.getSvJigyoId();

                    if (CommonConstants.NUMBER_1.equals(convertResult)) {
                        svItemCd = serviceModInfo.getSvItemCd();
                        if (kghCmnF01Logic.chkSougouSvtype(copyFromSvtype)) {
                            String tempScode = kghCmnF01Logic.getScodeFromItemuse2(ll_sv_jigyo_id, svItemCd,
                                    copyToTermId);
                            copyFromSvcode = tempScode.substring(CommonConstants.INT_2, CommonConstants.INT_6);
                        }
                    }

                    if (svItemCd > 0) {
                        // サービスが確定している
                        if (kghCmnF01Logic.chkSougouSvtype(copyFromSvtype)) {
                            if (CommonConstants.NUM_STR_35
                                    .equals(copyFromScode.substring(CommonConstants.INT_0, CommonConstants.INT_2))) {
                                ls_scode = kghCmn03gFunc01Logic.getScodeFromItemuseSougou(CommonConstants.NUM_STR_35,
                                        copyFromSvcode,
                                        copyToYm + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART,
                                        copyToTermId, hokenCd);
                            } else {
                                ls_scode = kghCmn03gFunc01Logic.getScodeFromItemuseSougou(copyFromSvtype,
                                        copyFromSvcode,
                                        copyToYm + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART,
                                        copyToTermId, hokenCd);
                            }
                        } else {
                            ls_scode = kghCmn03gFunc01Logic.getScodeFromItemuse(ll_sv_jigyo_id, svItemCd,
                                    copyToYm + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART);

                        }
                    } else {
                        ls_scode = CommonConstants.EMPTY_STRING;
                    }

                    // サービス種別コード
                    String ls_sss;
                    if (!CommonDtoUtil.checkStringEqual(ls_scode, CommonConstants.EMPTY_STRING)) {
                        ls_sss = copyFromScode.substring(CommonConstants.INT_0, CommonConstants.INT_2);
                        ls_svcode = copyFromScode.substring(CommonConstants.INT_2, CommonConstants.INT_6);
                    } else {
                        ls_sss = copyFromSvtype;
                        ls_svcode = CommonConstants.STR_FOUR_ZERO;
                    }

                    if (ll_sv_jigyo_id > 0) {
                        ls_svtype = kghCmnF01Logic.getSvType(ll_sv_jigyo_id);
                    }

                    for (int i = CommonConstants.INT_1; i <= CommonConstants.INT_31; i++) {
                        if (serviceModInfo.getDays()[i - 1] <= 0) {
                            tempTargetCopyDays.remove(Integer.valueOf(i));
                        }
                    }

                    // 短期入所
                    boolean isTempTanki = CommonConstants.SV_CODE_LIST_1.contains(ls_svtype);

                    // 提供日のパターン作成
                    Integer[] copyDataYoTeiArray = new Integer[CommonConstants.INT_31];
                    Integer[] copyDataJiSekiArray = new Integer[CommonConstants.INT_31];
                    if (isTempTanki || (CommonDtoUtil.checkStringEqual(copyTnki, CommonConstants.STR_1))) {
                        if (CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_1)) {
                            // 複写方法保険外 = 1の場合
                            setCopyDataWeekly(in, copyToYm, tempTargetCopyDays, copyFromMergedPlan, copyDataYoTeiArray,
                                    copyDataJiSekiArray);
                        } else if (CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_2)) {
                            // 複写方法保険外 = 2の場合
                            setCopyDataMonthlyByDay(in, copyFromMergedPlan, tempTargetCopyDays, copyDataYoTeiArray,
                                    copyDataJiSekiArray);
                        } else if (CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_3)) {
                            // 複写方法保険外 = 3の場合
                            setCopyDataMonthlyByWeek(in, copyToYm, tempTargetCopyDays, copyFromMergedPlan,
                                    newCopyToList, ids_detail_to_bk, copyDataYoTeiArray, copyDataJiSekiArray);
                        }
                    }

                    // 療養食加算の回数変更処理
                    List<Integer> copyDataYoTeis = List.of(copyDataYoTeiArray);

                    copyDataYoTeis = kghCmnCpmv01Logic.convertKaisu(copyDataYoTeis, copyFromMergedPlan.getTermid(),
                            copyToTermId, ls_scode);
                    copyDataYoTeiArray = copyDataYoTeis.toArray(new Integer[0]);

                    // 複写先のサービス種別のチェック
                    if (isTempTanki && (CommonDtoUtil.checkStringEqual(copyTnki, CommonConstants.STR_3))) {
                        continue;
                    }
                    if (isTempTanki && (CommonDtoUtil.checkStringEqual(copyTnki, CommonConstants.STR_2))) {
                        copyDataYoTeiArray = new Integer[CommonConstants.INT_31];
                    }

                    // 複写先の利用票明細の追加
                    // 設定値の取得
                    String ls_gousei_sik_kbn = null; // 合成識別区分
                    String ls_santei_tani = null; // 算定単位
                    String ls_kikan_jiki = null; // 期間･時期
                    Integer li_kaisu_nisu = null; // 回数･日数
                    // String ls_gentai_kbn = null; // 支給限度額対象区分
                    Integer ll_max; // 上限数
                    Integer ll_tenkintype; // 点金区分
                    BigDecimal ldc_tanka = null; // 点数/金額/加算率
                    Integer ll_default; // 規定数
                    if (copyFromSvItemCd > 0) {
                        if (kghCmnF01Logic.chkSougouSvtype(ls_svtype)) {
                            if (CommonConstants.NUM_STR_35
                                    .equals(ls_sss.substring(CommonConstants.INT_0, CommonConstants.INT_2))) {
                                MhcKmInDto kmIn = new MhcKmInDto();
                                kmIn.setSvType(ls_sss);
                                kmIn.setSvCode(ls_svcode);
                                kmIn.setShoriYm(copyToHoken.getStartYmd());
                                MhcKmOutDto kmOut = kghCmnWeek1Logic.getMhcKm(kmIn);
                                ls_gousei_sik_kbn = kmOut.getGouseiSikKbn();
                                ls_santei_tani = kmOut.getSanteiTani();
                                ls_kikan_jiki = kmOut.getKikanJiki();
                                li_kaisu_nisu = kmOut.getKaisuNisu();
                                // ls_gentai_kbn = kmOut.getGenTaiKbn();
                            } else {
                                MhcSmInDto smIn = new MhcSmInDto();
                                smIn.setSvJigyoId(ll_sv_jigyo_id);
                                smIn.setSvType(ls_sss);
                                smIn.setSvCode(ls_svcode);
                                smIn.setShoriYm(copyToHoken.getStartYmd());
                                MhcSmOutDto smOut = kghCmnWeek1Logic.getMhcSm(smIn);
                                ls_gousei_sik_kbn = smOut.getGouseiSikKbn();
                                ls_santei_tani = smOut.getSanteiTani();
                                ls_kikan_jiki = smOut.getKikanJiki();
                                li_kaisu_nisu = smOut.getKaisuNisu();
                                // ls_gentai_kbn = smOut.getGentaiKbn();
                            }

                            MhcItemuseSougouInDto useIn = new MhcItemuseSougouInDto();
                            useIn.setSCode(ls_scode);
                            useIn.setSvjId(ll_sv_jigyo_id);
                            useIn.setItemId(svItemCd);
                            useIn.setShoriYm(copyToHoken.getStartYmd());
                            MhcItemuseSougouOutDto useOut = kghCmnWeek1Logic.getMhcItemuseSougou(useIn);
                            ldc_tanka = useOut.getSvTani();
                            ll_max = useOut.getMax();
                            ll_tenkintype = useOut.getTenkinType();
                            ll_default = useOut.getDefaultVal();
                        } else {
                            MhcKmInDto kmIn = new MhcKmInDto();
                            kmIn.setSvType(ls_sss);
                            kmIn.setSvCode(ls_svcode);
                            kmIn.setShoriYm(copyToHoken.getStartYmd());
                            MhcKmOutDto kmOut = kghCmnWeek1Logic.getMhcKm(kmIn);
                            ls_gousei_sik_kbn = kmOut.getGouseiSikKbn();
                            ls_santei_tani = kmOut.getSanteiTani();
                            ls_kikan_jiki = kmOut.getKikanJiki();
                            li_kaisu_nisu = kmOut.getKaisuNisu();
                            // ls_gentai_kbn = kmOut.getGenTaiKbn();

                            MhcItemuseInDto useIn = new MhcItemuseInDto();
                            useIn.setAsScode(ls_scode);
                            useIn.setAlSvj(ll_sv_jigyo_id);
                            useIn.setAlItm(svItemCd);
                            useIn.setAsShoriYm(copyToHoken.getStartYmd());
                            MhcItemuseOutDto useOut = kghCmnWeek1Logic.getMhcItemuse(useIn);
                            ldc_tanka = useOut.getAdcSvTani();
                            ll_max = useOut.getAlMax();
                            ll_tenkintype = useOut.getAlTenkintype();
                            ll_default = useOut.getAlDefault();
                        }

                        if (ll_max == null || CommonConstants.NUMBER_0.equals(ll_max)) {
                            ll_max = CommonConstants.MAX_NUM_32767;
                        }
                    } else {
                        ll_max = CommonConstants.MAX_NUM_32767;
                        ll_tenkintype = CommonConstants.INT_0;
                        ll_default = CommonConstants.INT_1;
                    }

                    // 福祉用具貸与
                    Double ldc_sv_tani = null; // ｻｰﾋﾞｽ単位数
                    if (copyFromSvItemCd > 0) {
                        if (CommonDtoUtil.checkStringEqual(copyFromSvtype, CommonConstants.NUM_STR_17)
                                || CommonDtoUtil.checkStringEqual(copyFromSvtype, CommonConstants.SV_KIND_CD_67)
                                || CommonDtoUtil.checkStringEqual(
                                        copyFromScode.substring(CommonConstants.INT_0, CommonConstants.INT_4),
                                        CommonConstants.SV_KIND_CD_3320)
                                || CommonDtoUtil.checkStringEqual(
                                        copyFromScode.substring(CommonConstants.INT_0, CommonConstants.INT_4),
                                        CommonConstants.SV_KIND_CD_3519)) {
                            BigDecimal ldc_tani = null; // ﾚﾝﾀﾙ単位数
                            KghCmnGetFygMstShouhinByCriteriaInEntity fygInEntity = new KghCmnGetFygMstShouhinByCriteriaInEntity();
                            fygInEntity.setAlSvJigyoId(ll_sv_jigyo_id);
                            fygInEntity.setAlShouhinId(copyFromMergedPlan.getFygId());
                            List<KghCmnGetFygMstShouhinOutEntity> fygs = fygMstShouhinSelectMapper
                                    .findKghCmnGetFygMstShouhinByCriteria(fygInEntity);
                            if (!fygs.isEmpty()) {
                                ldc_tani = fygs.getFirst().getTanni();
                                ldc_sv_tani = copyFromMergedPlan.getSvTani();
                            }

                            if (CommonDtoUtil.checkStringEqual(in.getCopyFygTani(), CommonConstants.STR_1)) {
                                if (ldc_sv_tani == null) {
                                    if (ldc_tani == null) {
                                        ldc_tani = BigDecimal.ZERO;
                                    }
                                    ldc_sv_tani = ldc_tani.doubleValue();
                                }
                            } else {
                                if (ldc_tani == null) {
                                    ldc_tani = BigDecimal.ZERO;
                                    if (ldc_sv_tani == null) {
                                        ldc_sv_tani = ldc_tani.doubleValue();
                                    }
                                } else {
                                    ldc_sv_tani = ldc_tani.doubleValue();
                                }
                            }
                        } else {
                            ldc_sv_tani = ldc_tanka.doubleValue();
                        }
                    }

                    // サービス未選択対策
                    if (CommonConstants.NUMBER_0.equals(svItemCd)) {
                        ls_scode = CommonConstants.STR_EIGHT_ZERO;
                        ls_gousei_sik_kbn = copyFromMergedPlan.getGouseiSikKbn();
                        ldc_sv_tani = CommonConstants.DOUBLE_ZERO;
                    }

                    // 複写先利用票明細リスト作成
                    CmnTucPlanRiyou copyToDetail = new CmnTucPlanRiyou();
                    newCopyToList.add(copyToDetail);

                    copyToDetail.setShienId(CommonDtoUtil.strValToInt(in.getShienId()));
                    copyToDetail.setUserid(userIdInt);
                    copyToDetail.setYymmYm(copyToYm);
                    copyToDetail.setYymmD(modifiedDay);
                    copyToDetail.setSvJigyoId(ll_sv_jigyo_id);
                    copyToDetail.setSvItemCd(svItemCd);
                    copyToDetail.setEdaNo(copyFromMergedPlan.getEdaNo());
                    copyToDetail.setOyaLineNo(serviceModInfo.getNewOyaLineNo());
                    copyToDetail.setTermid(copyToTermId);
                    if (kghCmnF01Logic.chkSougouSvtype(copyFromSvtype)) {
                        copyToDetail.setSvtype(copyFromSvtype);
                    } else {
                        copyToDetail.setSvtype(ls_svtype);
                    }
                    copyToDetail.setSvcode(ls_svcode);
                    copyToDetail.setScode(ls_scode);
                    copyToDetail.setGouseiSikKbn(ls_gousei_sik_kbn);
                    copyToDetail.setSvStartTime(copyFromMergedPlan.getSvStartTime());
                    copyToDetail.setSvEndTime(copyFromMergedPlan.getSvEndTime());
                    copyToDetail.setSvTani(ldc_sv_tani);
                    copyToDetail.setSanteiTani(ls_santei_tani);
                    copyToDetail.setKikanJiki(ls_kikan_jiki);
                    copyToDetail.setKaisuNisu(li_kaisu_nisu);
                    copyToDetail.setMax(ll_max);
                    copyToDetail.setTenkintype(ll_tenkintype);
                    copyToDetail.setDefaultVal(ll_default);
                    copyToDetail.setYRentalF(CommonConstants.INT_0);
                    copyToDetail.setYOv30Fl(CommonConstants.INT_0);
                    copyToDetail.setJRentalF(CommonConstants.INT_0);
                    copyToDetail.setJOv30Fl(CommonConstants.INT_0);
                    copyToDetail.setTensouTime(null);
                    copyToDetail.setEdaBack(CommonConstants.INT_0);
                    copyToDetail.setSortNo(copyFromMergedPlan.getSortNo());
                    copyToDetail.setShoukiboKbn(copyFromMergedPlan.getShoukiboKbn());
                    copyToDetail.setFygId(copyFromMergedPlan.getFygId());
                    copyToDetail.setYoteiZumiFlg(CommonConstants.INT_0);
                    copyToDetail.setJissekiZumiFlg(CommonConstants.INT_0);

                    // 提供日をﾃﾞｰﾀにｾｯﾄする
                    // 同一建物減算上限補正
                    if (CommonDtoUtil.checkStringEqual(ls_svtype, CommonConstants.STR_14)
                            || CommonDtoUtil.checkStringEqual(ls_svtype, CommonConstants.STR_SIXTYFOUR)) {
                        if (kghCmnF01Logic.chkIsAdderPercent(ls_scode, copyToYm) > 0) {
                            ll_max = kghCmn03gFunc01Logic.getSvMax(ll_sv_jigyo_id, svItemCd, copyToYm);
                            if (CommonConstants.NUMBER_0.equals(ll_max)) {
                                ll_max = CommonConstants.MAX_NUM_32767;
                            }
                        }
                    }
                    Integer kyoseiResult = kghCmnF01Logic.getKyoseiGenzan(copyToHoken.getStartYmd(), ls_scode);
                    Integer tempMax = ll_max;
                    if (kyoseiResult != null && kyoseiResult > 0) {
                        tempMax = CommonConstants.MAX_NUM_32767;
                    }
                    int tempTotal = 0;
                    for (int i = CommonConstants.INT_1; i <= CommonConstants.INT_31; i++) {
                        int tempData = copyDataYoTeiArray[i - 1];
                        if (!tempTargetCopyDays.contains(i) || tempMax <= 0) {
                            tempData = 0;
                        }

                        switch (i) {
                            case CommonConstants.INT_1:
                                copyToDetail.setJDay01(tempData);
                                break;
                            case CommonConstants.INT_2:
                                copyToDetail.setJDay02(tempData);
                                break;
                            case CommonConstants.INT_3:
                                copyToDetail.setJDay03(tempData);
                                break;
                            case CommonConstants.INT_4:
                                copyToDetail.setJDay04(tempData);
                                break;
                            case CommonConstants.INT_5:
                                copyToDetail.setJDay05(tempData);
                                break;
                            case CommonConstants.INT_6:
                                copyToDetail.setJDay06(tempData);
                                break;
                            case CommonConstants.INT_7:
                                copyToDetail.setJDay07(tempData);
                                break;
                            case CommonConstants.INT_8:
                                copyToDetail.setJDay08(tempData);
                                break;
                            case CommonConstants.INT_9:
                                copyToDetail.setJDay09(tempData);
                                break;
                            case CommonConstants.INT_10:
                                copyToDetail.setJDay10(tempData);
                                break;
                            case CommonConstants.INT_11:
                                copyToDetail.setJDay11(tempData);
                                break;
                            case CommonConstants.INT_12:
                                copyToDetail.setJDay12(tempData);
                                break;
                            case CommonConstants.INT_13:
                                copyToDetail.setJDay13(tempData);
                                break;
                            case CommonConstants.INT_14:
                                copyToDetail.setJDay14(tempData);
                                break;
                            case CommonConstants.INT_15:
                                copyToDetail.setJDay15(tempData);
                                break;
                            case CommonConstants.INT_16:
                                copyToDetail.setJDay16(tempData);
                                break;
                            case CommonConstants.INT_17:
                                copyToDetail.setJDay17(tempData);
                                break;
                            case CommonConstants.INT_18:
                                copyToDetail.setJDay18(tempData);
                                break;
                            case CommonConstants.INT_19:
                                copyToDetail.setJDay19(tempData);
                                break;
                            case CommonConstants.INT_20:
                                copyToDetail.setJDay20(tempData);
                                break;
                            case CommonConstants.INT_21:
                                copyToDetail.setJDay21(tempData);
                                break;
                            case CommonConstants.INT_22:
                                copyToDetail.setJDay22(tempData);
                                break;
                            case CommonConstants.INT_23:
                                copyToDetail.setJDay23(tempData);
                                break;
                            case CommonConstants.INT_24:
                                copyToDetail.setJDay24(tempData);
                                break;
                            case CommonConstants.INT_25:
                                copyToDetail.setJDay25(tempData);
                                break;
                            case CommonConstants.INT_26:
                                copyToDetail.setJDay26(tempData);
                                break;
                            case CommonConstants.INT_27:
                                copyToDetail.setJDay27(tempData);
                                break;
                            case CommonConstants.INT_28:
                                copyToDetail.setJDay28(tempData);
                                break;
                            case CommonConstants.INT_29:
                                copyToDetail.setJDay29(tempData);
                                break;
                            case CommonConstants.INT_30:
                                copyToDetail.setJDay30(tempData);
                                break;
                            case CommonConstants.INT_31:
                                copyToDetail.setJDay31(tempData);
                                break;
                            default:
                                break;
                        }

                        tempTotal += tempData;
                        tempMax--;
                    }
                    copyToDetail.setYTotal(tempTotal);
                    copyToDetail.setJDay01(CommonConstants.INT_0);
                    copyToDetail.setJDay02(CommonConstants.INT_0);
                    copyToDetail.setJDay03(CommonConstants.INT_0);
                    copyToDetail.setJDay04(CommonConstants.INT_0);
                    copyToDetail.setJDay05(CommonConstants.INT_0);
                    copyToDetail.setJDay06(CommonConstants.INT_0);
                    copyToDetail.setJDay07(CommonConstants.INT_0);
                    copyToDetail.setJDay08(CommonConstants.INT_0);
                    copyToDetail.setJDay09(CommonConstants.INT_0);
                    copyToDetail.setJDay10(CommonConstants.INT_0);
                    copyToDetail.setJDay11(CommonConstants.INT_0);
                    copyToDetail.setJDay12(CommonConstants.INT_0);
                    copyToDetail.setJDay13(CommonConstants.INT_0);
                    copyToDetail.setJDay14(CommonConstants.INT_0);
                    copyToDetail.setJDay15(CommonConstants.INT_0);
                    copyToDetail.setJDay16(CommonConstants.INT_0);
                    copyToDetail.setJDay17(CommonConstants.INT_0);
                    copyToDetail.setJDay18(CommonConstants.INT_0);
                    copyToDetail.setJDay19(CommonConstants.INT_0);
                    copyToDetail.setJDay20(CommonConstants.INT_0);
                    copyToDetail.setJDay21(CommonConstants.INT_0);
                    copyToDetail.setJDay22(CommonConstants.INT_0);
                    copyToDetail.setJDay23(CommonConstants.INT_0);
                    copyToDetail.setJDay24(CommonConstants.INT_0);
                    copyToDetail.setJDay25(CommonConstants.INT_0);
                    copyToDetail.setJDay26(CommonConstants.INT_0);
                    copyToDetail.setJDay27(CommonConstants.INT_0);
                    copyToDetail.setJDay28(CommonConstants.INT_0);
                    copyToDetail.setJDay29(CommonConstants.INT_0);
                    copyToDetail.setJDay30(CommonConstants.INT_0);
                    copyToDetail.setJDay31(CommonConstants.INT_0);
                    copyToDetail.setJTotal(CommonConstants.INT_0);

                    // 一つ目の保険であれば、データをバックアップする
                    if (ll_cnt == CommonConstants.INT_1) {
                        CmnTucPlanRiyou copyToDetailBak = new CmnTucPlanRiyou();
                        BeanUtils.copyProperties(copyToDetail, copyToDetailBak);
                        copyToDetailBak.setOyaLineNo(copyFromMergedPlan.getOyaLineNo());
                        ids_detail_to_bk.add(copyToDetailBak);
                    }

                    // 福祉用具貸与の差異の特別処理
                    if (CommonDtoUtil.checkStringEqual(ls_svtype, CommonConstants.NUM_STR_17)
                            || CommonDtoUtil.checkStringEqual(ls_svtype, CommonConstants.SV_KIND_CD_67)
                            || CommonDtoUtil.checkStringEqual(
                                    ls_scode.substring(CommonConstants.INT_0, CommonConstants.INT_4),
                                    CommonConstants.SV_KIND_CD_3320)
                            || CommonDtoUtil.checkStringEqual(
                                    ls_scode.substring(CommonConstants.INT_0, CommonConstants.INT_4),
                                    CommonConstants.SV_KIND_CD_3519)) {
                        copyToDetail.setYTotal(CommonConstants.INT_1);
                    }

                    // コンバート処理によりサービスコードが分割された場合
                    // パラメータなし 複写先分割されたサービス項目ID
                }
            }

            // 行削除処理
            while (copyFromMergedPlans.size() > copyFromMergedPlanDefSize) {
                copyFromMergedPlans.removeLast();
            }

            // 4.10.登録
            if (newCopyToList.isEmpty()) {
                // 複写先の利用票明細が存在しない場合
                continue;
            }
            // 利用料の登録
            for (KghCmnCpmvTucPlanGaiFutanOutEntity copyFromFutan : copyFromFutans) {
                String futanKey = String.join(CommonConstants.SPACE_STRING,
                        CommonDtoUtil.objValToString(copyFromFutan.getSvJigyoId()), copyFromFutan.getSvNameKnj(),
                        CommonDtoUtil.objValToString(copyFromFutan.getTanka()));
                if (copyToFutanKeys.contains(futanKey)) {
                    continue;
                }
                copyToFutanKeys.add(futanKey);

                CmnTucPlanGaiFutan row = new CmnTucPlanGaiFutan();
                row.setShienId(copyFromFutan.getShienId());
                row.setUserid(userIdInt);
                row.setYymmYm(copyToYm);
                row.setYymmD(modifiedDay);
                row.setSeqNo(copyToFutanKeys.size());
                row.setHoujinId(copyFromFutan.getHoujinId());
                row.setShisetuId(copyFromFutan.getShisetuId());
                row.setSvJigyoId(copyFromFutan.getSvJigyoId());
                row.setItemCd(copyFromFutan.getItemCd());
                row.setSvNameKnj(copyFromFutan.getSvNameKnj());
                row.setTanka(copyFromFutan.getTanka());
                int result = cmnTucPlanGaiFutanMapper.insertSelective(row);
                if (result <= 0) {
                    throw new ExclusiveException();
                }
            }
            // 社福軽減の登録
            for (KghCmnCpmvTucPlanSyafukuKeigenOutEntity copyFromFuku : copyFromFukus) {
                String fukuKey = String.join(CommonConstants.SPACE_STRING,
                        CommonDtoUtil.objValToString(copyFromFuku.getSvJigyoId()),
                        CommonDtoUtil.objValToString(copyFromFuku.getSakuKbn()));
                if (copyToFukuKeys.contains(fukuKey)) {
                    continue;
                }
                copyToFukuKeys.add(fukuKey);

                CmnTucPlanSyafukuKeigen row = new CmnTucPlanSyafukuKeigen();
                row.setShienId(copyFromFuku.getShienId());
                row.setUserid(userIdInt);
                row.setYymmYm(copyToYm);
                row.setYymmD(modifiedDay);
                row.setSvJigyoId(copyFromFuku.getSvJigyoId());
                row.setSvShuCd(copyFromFuku.getSvShuCd());
                row.setKubun(copyFromFuku.getKubun());
                row.setKeigenRitu(copyFromFuku.getKeigenRitu());
                row.setSakuKbn(copyFromFuku.getSakuKbn());
                int result = cmnTucPlanSyafukuKeigenMapper.insertSelective(row);
                if (result <= 0) {
                    throw new ExclusiveException();
                }
            }
            // 利用票明細の登録
            // ソート順設定
            newCopyToList.sort(
                    Comparator.comparing(CmnTucPlanRiyou::getSvJigyoId).thenComparing(CmnTucPlanRiyou::getSvItemCd));

            Integer tempSvJigyoId = CommonConstants.INT_0;
            Integer tempSvItemCd = CommonConstants.INT_0;
            int tempSeqNo = CommonConstants.INT_0;
            for (CmnTucPlanRiyou copyTo : newCopyToList) {
                if (!tempSvJigyoId.equals(copyTo.getSvJigyoId()) || !tempSvItemCd.equals(copyTo.getSvItemCd())) {
                    tempSeqNo = CommonConstants.INT_0;
                }
                tempSvJigyoId = copyTo.getSvJigyoId();
                tempSvItemCd = copyTo.getSvItemCd();
                tempSeqNo++;
                copyTo.setEdaNo(tempSeqNo);
                int result = cmnTucPlanRiyouMapper.insertSelective(copyTo);
                if (result <= 0) {
                    throw new ExclusiveException();
                }
            }
            // 複写先の利用票ヘッダの登録
            Integer li_zengetu = CommonConstants.INT_0;
            if (CommonConstants.MONTHSTART.equals(modifiedDay)) {
                LocalDate copyToDate = yearMonth.atDay(CommonConstants.INT_1);
                if (nintenStartYmd.isBefore(copyToDate)) {
                    li_zengetu = kghCmnRiyou01Logic.getZengetuRuikei(CommonDtoUtil.strValToInt(in.getShienId()),
                            userIdInt, copyToYm, copyToNintei.getStartYmd());
                }
            }
            CmnTucPlan row = new CmnTucPlan();
            row.setKHokenCd(copyToHoken.getKHokenCd());
            row.setHHokenNo(copyToHoken.getHiHokenNo());
            row.setCreateYmd(in.getAppYmd());
            row.setSvTensuY(CommonConstants.DOUBLE_ZERO);
            row.setSvTensuJ(CommonConstants.DOUBLE_ZERO);
            row.setHShuOver(CommonConstants.DOUBLE_ZERO);
            row.setHShuKijun(CommonConstants.DOUBLE_ZERO);
            row.setHKbnOver(CommonConstants.DOUBLE_ZERO);
            row.setHKbnKijun(CommonConstants.DOUBLE_ZERO);
            row.setHHknHutan(CommonConstants.DOUBLE_ZERO);
            row.setHJihHutan(CommonConstants.DOUBLE_ZERO);
            row.setTZengetu(li_zengetu);
            row.setCalcYoji(CommonConstants.INT_1);
            row.setYoteiZumiFlg(CommonConstants.INT_0);
            row.setJissekiZumiFlg(CommonConstants.INT_0);
            row.setKakuteiZumiFlg(CommonConstants.INT_0);
            row.setYokaiKbn(copyToNintei.getYokaiKbn());
            row.setHenYokaiKbn(ll_hen_yokai_kbn);
            row.setHenYokaiYmd(ls_hen_yokai_ymd);
            row.setGendoGaku((double) copyToNintei.getGendo());
            row.setGendoStartYmd(copyToNintei.getStartYmd());
            row.setGendoEndYmd(copyToNintei.getEndYmd());

            if (!tempCopyToHeads.isEmpty()) {
                CmnTucPlanCriteria criteria = new CmnTucPlanCriteria();
                criteria.createCriteria().andShienIdEqualTo(CommonDtoUtil.strValToInt(in.getShienId()))
                        .andUseridEqualTo(userIdInt).andYymmYmEqualTo(copyToYm).andYymmDEqualTo(modifiedDay);

                int result = cmnTucPlanMapper.updateByCriteriaSelective(row, criteria);
                if (result <= 0) {
                    throw new ExclusiveException();
                }
            } else {
                row.setShienId(CommonDtoUtil.strValToInt(in.getShienId()));
                row.setUserid(userIdInt);
                row.setYymmYm(copyToYm);
                row.setYymmD(modifiedDay);

                int result = cmnTucPlanMapper.insertSelective(row);
                if (result <= 0) {
                    throw new ExclusiveException();
                }
            }

        }

        ret[CommonConstants.INT_0] = CommonConstants.INT_1;
        return ret;
    }

    /**
     * termid = 16内でコンバートが必要なサービスかどうかを判断する
     *
     * @param as_svtype サービス種類
     * @param as_scode  サービスコード
     * @param as_ym     複写先処理年月
     * @return 1：対象 0：対象外
     */
    private Integer ufTargetCnvTerm16(String as_svtype, String as_scode, String as_ym) {
        // 室料相当額控除（2025年8月1日開始）
        // 対象サービス：
        // （介護予防）短期入所療養介護（老健） ：22、25
        // （介護予防）短期入所療養介護（介護医療院） ：2A、2B
        String scodeSub = as_scode.substring(Math.min(as_scode.length(), CommonConstants.INT_2),
                Math.min(as_scode.length(), CommonConstants.INT_6));
        if ((as_svtype.equals(CommonConstants.NUM_STR_22) || as_svtype.equals(CommonConstants.NUM_STR_25)
                || as_svtype.equals(CommonConstants.STR_NUM_2A) || as_svtype.equals(CommonConstants.STR_NUM_2B))
                && scodeSub.equals(CommonConstants.STR_6160)) {
            return CommonConstants.INT_1;
        }

        return CommonConstants.INT_0;
    }

    /**
     * termid = 13内でコンバートが必要なサービスかどうかを判断する
     *
     * @param as_svtype サービス種類
     * @param as_scode  サービスコード
     * @param as_ym     複写先処理年月
     * @return 1：対象 0：対象外
     */
    private Integer ufTargetCnvTerm13(String as_svtype, String as_scode, String as_ym) {
        // ADL維持等加算Ⅲ（2023年3月31日廃止）
        String scodeSub = as_scode.substring(Math.min(as_scode.length(), CommonConstants.INT_2),
                Math.min(as_scode.length(), CommonConstants.INT_6));
        if ((as_svtype.equals(CommonConstants.NUM_STR_15) || as_svtype.equals(CommonConstants.NUM_STR_78))
                && scodeSub.equals(CommonConstants.STR_6340)) {
            return CommonConstants.INT_1;
        }

        return CommonConstants.INT_0;
    }

    /**
     * termid = 12内でコンバートが必要なサービスかどうかを判断する
     *
     * @param as_svtype サービス種類
     * @param as_scode  サービスコード
     * @param as_ym     複写先処理年月
     * @return 1：対象 0：対象外
     */
    private Integer ufTargetCnvTerm12(String as_svtype, String as_scode, String as_ym) {
        // 上乗せ加算（2021年9月30日廃止）
        // 判定関数の第１引数である提供年月日は関数側で使用していませんが、暫定的に複写先の年月+'/01'を使用しています。
        if (kghCmnF01Logic.isKansenTokureiKasan(as_ym + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART,
                as_scode) > CommonConstants.INT_0) {
            return CommonConstants.INT_1;
        }

        // （旧）生活行為向上リハビリテーション実施加算
        // 旧加算１（2021年5月31日廃止）、旧加算２（2021年8月31日廃止）
        String scodeSub = as_scode.substring(Math.min(as_scode.length(), CommonConstants.INT_2),
                Math.min(as_scode.length(), CommonConstants.INT_6));
        if (as_svtype != null
                && (as_svtype.equals(CommonConstants.NUM_STR_16) || as_svtype.equals(CommonConstants.STR_66))
                && (scodeSub.equals(CommonConstants.STR_6255) || scodeSub.equals(CommonConstants.STR_6256))) {
            return CommonConstants.INT_1;
        }

        // 処遇改善加算Ⅳ、Ⅴ（2022年3月31日廃止）
        switch (kghCmnF01Logic.isShoguuKaizenKasan(as_ym + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART,
                as_scode)) {
            case CommonConstants.INT_4:
            case CommonConstants.INT_5: {
                return CommonConstants.INT_1;
            }
            default: {
                break;
            }
        }

        // １２月超減算（2022年4月1日開始）
        if (as_svtype != null && as_svtype.equals(CommonConstants.NUM_STR_63)
                && scodeSub.equals(CommonConstants.STR_6123)
                || as_svtype != null && as_svtype.equals(CommonConstants.NUM_STR_64)
                        && scodeSub.equals(CommonConstants.STR_6123)
                || as_svtype != null && as_svtype.equals(CommonConstants.STR_66)
                        && (scodeSub.equals(CommonConstants.STR_6123) || scodeSub.equals(CommonConstants.STR_6124)
                                || scodeSub.equals(CommonConstants.STR_6125)
                                || scodeSub.equals(CommonConstants.STR_6126)
                                || scodeSub.equals(CommonConstants.STR_6127)
                                || scodeSub.equals(CommonConstants.STR_6128))) {
            return CommonConstants.INT_1;
        }

        return CommonConstants.INT_0;
    }

    /**
     * 利用票を月単位で複写
     *
     * @param in                  計画複写の保険外複写処理の入力DTO
     * @param copyToYm            複写先年月
     * @param targetCopyDays      複写許可されている提供日
     * @param copyFromInfo        複写元明細
     * @param copyDataYoTeiArray  複写対象日配列
     * @param copyDataJiSekiArray 複写対象日配列
     */
    private void setCopyDataWeekly(PlanDuplicateUseSlipDuplicateUpdateServiceInDto in, String copyToYm,
            List<Integer> targetCopyDays, KghCmnYojituListOutEntity copyFromInfo, Integer[] copyDataYoTeiArray,
            Integer[] copyDataJiSekiArray) {
        // 変数初期化
        Integer[] weekDataArray = new Integer[CommonConstants.INT_7];

        // 複写元年月日の曜日を取得
        Integer weekDay = nds3GkFunc01Logic.getWeekDay(in.getFromYm() + CommonConstants.STR_DELIMITER + in.getStDay());
        if (weekDay == null) {
            weekDay = CommonConstants.INT_7;
        }

        // 各曜日のパターン採取
        Integer stDayInt = CommonDtoUtil.strValToInt(in.getStDay());
        for (int i = 0; i < CommonConstants.INT_7; i++) {
            if (weekDay == CommonConstants.INT_7) {
                weekDay = CommonConstants.INT_0;
            }

            Integer data = 0;
            switch (stDayInt) {
                case CommonConstants.INT_1:
                    data = copyFromInfo.getYDay01();
                    break;
                case CommonConstants.INT_2:
                    data = copyFromInfo.getYDay02();
                    break;
                case CommonConstants.INT_3:
                    data = copyFromInfo.getYDay03();
                    break;
                case CommonConstants.INT_4:
                    data = copyFromInfo.getYDay04();
                    break;
                case CommonConstants.INT_5:
                    data = copyFromInfo.getYDay05();
                    break;
                case CommonConstants.INT_6:
                    data = copyFromInfo.getYDay06();
                    break;
                case CommonConstants.INT_7:
                    data = copyFromInfo.getYDay07();
                    break;
                case CommonConstants.INT_8:
                    data = copyFromInfo.getYDay08();
                    break;
                case CommonConstants.INT_9:
                    data = copyFromInfo.getYDay09();
                    break;
                case CommonConstants.INT_10:
                    data = copyFromInfo.getYDay10();
                    break;
                case CommonConstants.INT_11:
                    data = copyFromInfo.getYDay11();
                    break;
                case CommonConstants.INT_12:
                    data = copyFromInfo.getYDay12();
                    break;
                case CommonConstants.INT_13:
                    data = copyFromInfo.getYDay13();
                    break;
                case CommonConstants.INT_14:
                    data = copyFromInfo.getYDay14();
                    break;
                case CommonConstants.INT_15:
                    data = copyFromInfo.getYDay15();
                    break;
                case CommonConstants.INT_16:
                    data = copyFromInfo.getYDay16();
                    break;
                case CommonConstants.INT_17:
                    data = copyFromInfo.getYDay17();
                    break;
                case CommonConstants.INT_18:
                    data = copyFromInfo.getYDay18();
                    break;
                case CommonConstants.INT_19:
                    data = copyFromInfo.getYDay19();
                    break;
                case CommonConstants.INT_20:
                    data = copyFromInfo.getYDay20();
                    break;
                case CommonConstants.INT_21:
                    data = copyFromInfo.getYDay21();
                    break;
                case CommonConstants.INT_22:
                    data = copyFromInfo.getYDay22();
                    break;
                case CommonConstants.INT_23:
                    data = copyFromInfo.getYDay23();
                    break;
                case CommonConstants.INT_24:
                    data = copyFromInfo.getYDay24();
                    break;
                case CommonConstants.INT_25:
                    data = copyFromInfo.getYDay25();
                    break;
                case CommonConstants.INT_26:
                    data = copyFromInfo.getYDay26();
                    break;
                case CommonConstants.INT_27:
                    data = copyFromInfo.getYDay27();
                    break;
                case CommonConstants.INT_28:
                    data = copyFromInfo.getYDay28();
                    break;
                case CommonConstants.INT_29:
                    data = copyFromInfo.getYDay29();
                    break;
                case CommonConstants.INT_30:
                    data = copyFromInfo.getYDay30();
                    break;
                case CommonConstants.INT_31:
                    data = copyFromInfo.getYDay31();
                    break;
                default:
                    break;
            }
            weekDataArray[weekDay] = data;

            weekDay++;
            stDayInt++;
        }

        // 複写先の月末日を取得
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM);
        YearMonth yearMonth = YearMonth.parse(copyToYm, formatter);
        int lastDay = yearMonth.lengthOfMonth();

        // 複写先の月初日(1日)の曜日を取得
        weekDay = nds3GkFunc01Logic.getWeekDay(copyToYm + CommonConstants.STR_DELIMITER + CommonConstants.MONTHSTART);
        if (weekDay == null) {
            weekDay = CommonConstants.INT_7;
        }

        // パターン作成
        for (int i = CommonConstants.INT_1; i <= lastDay; i++) {
            if (weekDay == CommonConstants.INT_7) {
                weekDay = CommonConstants.INT_0;
            }

            if (targetCopyDays.contains(i)) {
                copyDataYoTeiArray[i - 1] = weekDataArray[weekDay];
            } else {
                copyDataYoTeiArray[i - 1] = CommonConstants.INT_0;
            }
            copyDataJiSekiArray[i - 1] = CommonConstants.INT_0;

            weekDay++;
        }
    }

    /**
     * 利用票を1ヶ月分を日に合わせて複写
     *
     * @param in                  計画複写の保険外複写処理の入力DTO
     * @param copyFromInfo        複写元明細
     * @param targetCopyDays      複写許可されている提供日
     * @param copyDataYoTeiArray  複写対象日配列
     * @param copyDataJiSekiArray 複写対象日配列
     */
    private void setCopyDataMonthlyByDay(PlanDuplicateUseSlipDuplicateUpdateServiceInDto in,
            KghCmnYojituListOutEntity copyFromInfo, List<Integer> targetCopyDays, Integer[] copyDataYoTeiArray,
            Integer[] copyDataJiSekiArray) {
        // 複写先の対象日を作成
        Set<Integer> copyTargetDays = new HashSet<>(targetCopyDays);

        // パターン作成
        for (int i = CommonConstants.INT_1; i <= CommonConstants.INT_31; i++) {
            if (copyTargetDays.contains(i)) {
                Integer data = 0;
                switch (i) {
                    case CommonConstants.INT_1:
                        data = copyFromInfo.getYDay01();
                        break;
                    case CommonConstants.INT_2:
                        data = copyFromInfo.getYDay02();
                        break;
                    case CommonConstants.INT_3:
                        data = copyFromInfo.getYDay03();
                        break;
                    case CommonConstants.INT_4:
                        data = copyFromInfo.getYDay04();
                        break;
                    case CommonConstants.INT_5:
                        data = copyFromInfo.getYDay05();
                        break;
                    case CommonConstants.INT_6:
                        data = copyFromInfo.getYDay06();
                        break;
                    case CommonConstants.INT_7:
                        data = copyFromInfo.getYDay07();
                        break;
                    case CommonConstants.INT_8:
                        data = copyFromInfo.getYDay08();
                        break;
                    case CommonConstants.INT_9:
                        data = copyFromInfo.getYDay09();
                        break;
                    case CommonConstants.INT_10:
                        data = copyFromInfo.getYDay10();
                        break;
                    case CommonConstants.INT_11:
                        data = copyFromInfo.getYDay11();
                        break;
                    case CommonConstants.INT_12:
                        data = copyFromInfo.getYDay12();
                        break;
                    case CommonConstants.INT_13:
                        data = copyFromInfo.getYDay13();
                        break;
                    case CommonConstants.INT_14:
                        data = copyFromInfo.getYDay14();
                        break;
                    case CommonConstants.INT_15:
                        data = copyFromInfo.getYDay15();
                        break;
                    case CommonConstants.INT_16:
                        data = copyFromInfo.getYDay16();
                        break;
                    case CommonConstants.INT_17:
                        data = copyFromInfo.getYDay17();
                        break;
                    case CommonConstants.INT_18:
                        data = copyFromInfo.getYDay18();
                        break;
                    case CommonConstants.INT_19:
                        data = copyFromInfo.getYDay19();
                        break;
                    case CommonConstants.INT_20:
                        data = copyFromInfo.getYDay20();
                        break;
                    case CommonConstants.INT_21:
                        data = copyFromInfo.getYDay21();
                        break;
                    case CommonConstants.INT_22:
                        data = copyFromInfo.getYDay22();
                        break;
                    case CommonConstants.INT_23:
                        data = copyFromInfo.getYDay23();
                        break;
                    case CommonConstants.INT_24:
                        data = copyFromInfo.getYDay24();
                        break;
                    case CommonConstants.INT_25:
                        data = copyFromInfo.getYDay25();
                        break;
                    case CommonConstants.INT_26:
                        data = copyFromInfo.getYDay26();
                        break;
                    case CommonConstants.INT_27:
                        data = copyFromInfo.getYDay27();
                        break;
                    case CommonConstants.INT_28:
                        data = copyFromInfo.getYDay28();
                        break;
                    case CommonConstants.INT_29:
                        data = copyFromInfo.getYDay29();
                        break;
                    case CommonConstants.INT_30:
                        data = copyFromInfo.getYDay30();
                        break;
                    case CommonConstants.INT_31:
                        data = copyFromInfo.getYDay31();
                        break;
                    default:
                        break;
                }
                copyDataYoTeiArray[i - 1] = data;
                copyDataJiSekiArray[i - 1] = CommonConstants.INT_0;
            } else {
                copyDataYoTeiArray[i - 1] = CommonConstants.INT_0;
                copyDataJiSekiArray[i - 1] = CommonConstants.INT_0;
            }
        }
    }

    /**
     * 利用票を1ヶ月分を週に合わせて複写
     *
     * @param in                  計画複写の保険外複写処理の入力DTO
     * @param copyToYm            複写先年月
     * @param targetCopyDays      複写許可されている提供日
     * @param copyFromInfo        複写元明細
     * @param ids_detail_to       ids_detail_to
     * @param ids_detail_to_bk    ids_detail_to_bk
     * @param copyDataYoTeiArray  複写対象日配列
     * @param copyDataJiSekiArray 複写対象日配列
     */
    private void setCopyDataMonthlyByWeek(PlanDuplicateUseSlipDuplicateUpdateServiceInDto in, String copyToYm,
            List<Integer> targetCopyDays, KghCmnYojituListOutEntity copyFromInfo, List<CmnTucPlanRiyou> ids_detail_to,
            List<CmnTucPlanRiyou> ids_detail_to_bk, Integer[] copyDataYoTeiArray, Integer[] copyDataJiSekiArray) {
        // 複写先の月初日(1日)の曜日を取得
        Integer copyToStartWeekDay = nds3GkFunc01Logic
                .getWeekDay(copyToYm + CommonConstants.STR_DELIMITER + CommonConstants.MONTHSTART);
        if (copyToStartWeekDay == null) {
            copyToStartWeekDay = CommonConstants.INT_7;
        }

        // 複写元の月初日(1日)の曜日を取得
        Integer copyFromStartWeekDay = nds3GkFunc01Logic
                .getWeekDay(in.getFromYm() + CommonConstants.STR_DELIMITER + CommonConstants.MONTHSTART);
        if (copyFromStartWeekDay == null) {
            copyFromStartWeekDay = CommonConstants.INT_7;
        }

        // 複写元の開始日の設定
        int copyFromStartDay;
        if (copyToStartWeekDay >= copyFromStartWeekDay) {
            copyFromStartDay = copyToStartWeekDay - copyFromStartWeekDay;
        } else {
            copyFromStartDay = copyToStartWeekDay + CommonConstants.INT_7 - copyFromStartWeekDay;
        }

        // 複写元の月末日を取得
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM);
        YearMonth yearMonth = YearMonth.parse(in.getFromYm(), formatter);
        int copyFromLastDay = yearMonth.lengthOfMonth();

        // パターン作成
        int copyFromDay = copyFromStartDay + 1;
        int li_total = 0;
        for (int i = CommonConstants.INT_1; i <= CommonConstants.INT_31; i++) {
            if (copyFromDay > copyFromLastDay) {
                // 複写元が月末日の場合
                copyFromDay = copyFromStartDay + 1;
            }

            if (targetCopyDays.contains(i)) {
                Integer data = 0;
                switch (copyFromDay) {
                    case CommonConstants.INT_1:
                        data = copyFromInfo.getYDay01();
                        break;
                    case CommonConstants.INT_2:
                        data = copyFromInfo.getYDay02();
                        break;
                    case CommonConstants.INT_3:
                        data = copyFromInfo.getYDay03();
                        break;
                    case CommonConstants.INT_4:
                        data = copyFromInfo.getYDay04();
                        break;
                    case CommonConstants.INT_5:
                        data = copyFromInfo.getYDay05();
                        break;
                    case CommonConstants.INT_6:
                        data = copyFromInfo.getYDay06();
                        break;
                    case CommonConstants.INT_7:
                        data = copyFromInfo.getYDay07();
                        break;
                    case CommonConstants.INT_8:
                        data = copyFromInfo.getYDay08();
                        break;
                    case CommonConstants.INT_9:
                        data = copyFromInfo.getYDay09();
                        break;
                    case CommonConstants.INT_10:
                        data = copyFromInfo.getYDay10();
                        break;
                    case CommonConstants.INT_11:
                        data = copyFromInfo.getYDay11();
                        break;
                    case CommonConstants.INT_12:
                        data = copyFromInfo.getYDay12();
                        break;
                    case CommonConstants.INT_13:
                        data = copyFromInfo.getYDay13();
                        break;
                    case CommonConstants.INT_14:
                        data = copyFromInfo.getYDay14();
                        break;
                    case CommonConstants.INT_15:
                        data = copyFromInfo.getYDay15();
                        break;
                    case CommonConstants.INT_16:
                        data = copyFromInfo.getYDay16();
                        break;
                    case CommonConstants.INT_17:
                        data = copyFromInfo.getYDay17();
                        break;
                    case CommonConstants.INT_18:
                        data = copyFromInfo.getYDay18();
                        break;
                    case CommonConstants.INT_19:
                        data = copyFromInfo.getYDay19();
                        break;
                    case CommonConstants.INT_20:
                        data = copyFromInfo.getYDay20();
                        break;
                    case CommonConstants.INT_21:
                        data = copyFromInfo.getYDay21();
                        break;
                    case CommonConstants.INT_22:
                        data = copyFromInfo.getYDay22();
                        break;
                    case CommonConstants.INT_23:
                        data = copyFromInfo.getYDay23();
                        break;
                    case CommonConstants.INT_24:
                        data = copyFromInfo.getYDay24();
                        break;
                    case CommonConstants.INT_25:
                        data = copyFromInfo.getYDay25();
                        break;
                    case CommonConstants.INT_26:
                        data = copyFromInfo.getYDay26();
                        break;
                    case CommonConstants.INT_27:
                        data = copyFromInfo.getYDay27();
                        break;
                    case CommonConstants.INT_28:
                        data = copyFromInfo.getYDay28();
                        break;
                    case CommonConstants.INT_29:
                        data = copyFromInfo.getYDay29();
                        break;
                    case CommonConstants.INT_30:
                        data = copyFromInfo.getYDay30();
                        break;
                    case CommonConstants.INT_31:
                        data = copyFromInfo.getYDay31();
                        break;
                    default:
                        break;
                }
                copyDataYoTeiArray[i - 1] = data;
                copyDataJiSekiArray[i - 1] = CommonConstants.INT_0;
            } else {
                copyDataYoTeiArray[i - 1] = CommonConstants.INT_0;
                copyDataJiSekiArray[i - 1] = CommonConstants.INT_0;
            }

            li_total += copyDataYoTeiArray[i - 1];
            copyFromDay++;
        }
        Integer res = copyFromInfo.getMax();
        if (CommonConstants.NUMBER_1.equals(res) && kghCmnF01Logic.chkKasanService2(copyFromInfo.getSvtype(),
                copyFromInfo.getScode(), copyFromInfo.getGouseiSikKbn())) {
            // 利用票複写 & 上限回数1回 & 加算サービス

            // 複写元の予定チェック
            boolean ll_from_yotei = false; // 複写元の予定
            for (int i = CommonConstants.INT_1; i <= CommonConstants.INT_31; i++) {
                Integer data = 0;
                switch (i) {
                    case CommonConstants.INT_1:
                        data = copyFromInfo.getYDay01();
                        break;
                    case CommonConstants.INT_2:
                        data = copyFromInfo.getYDay02();
                        break;
                    case CommonConstants.INT_3:
                        data = copyFromInfo.getYDay03();
                        break;
                    case CommonConstants.INT_4:
                        data = copyFromInfo.getYDay04();
                        break;
                    case CommonConstants.INT_5:
                        data = copyFromInfo.getYDay05();
                        break;
                    case CommonConstants.INT_6:
                        data = copyFromInfo.getYDay06();
                        break;
                    case CommonConstants.INT_7:
                        data = copyFromInfo.getYDay07();
                        break;
                    case CommonConstants.INT_8:
                        data = copyFromInfo.getYDay08();
                        break;
                    case CommonConstants.INT_9:
                        data = copyFromInfo.getYDay09();
                        break;
                    case CommonConstants.INT_10:
                        data = copyFromInfo.getYDay10();
                        break;
                    case CommonConstants.INT_11:
                        data = copyFromInfo.getYDay11();
                        break;
                    case CommonConstants.INT_12:
                        data = copyFromInfo.getYDay12();
                        break;
                    case CommonConstants.INT_13:
                        data = copyFromInfo.getYDay13();
                        break;
                    case CommonConstants.INT_14:
                        data = copyFromInfo.getYDay14();
                        break;
                    case CommonConstants.INT_15:
                        data = copyFromInfo.getYDay15();
                        break;
                    case CommonConstants.INT_16:
                        data = copyFromInfo.getYDay16();
                        break;
                    case CommonConstants.INT_17:
                        data = copyFromInfo.getYDay17();
                        break;
                    case CommonConstants.INT_18:
                        data = copyFromInfo.getYDay18();
                        break;
                    case CommonConstants.INT_19:
                        data = copyFromInfo.getYDay19();
                        break;
                    case CommonConstants.INT_20:
                        data = copyFromInfo.getYDay20();
                        break;
                    case CommonConstants.INT_21:
                        data = copyFromInfo.getYDay21();
                        break;
                    case CommonConstants.INT_22:
                        data = copyFromInfo.getYDay22();
                        break;
                    case CommonConstants.INT_23:
                        data = copyFromInfo.getYDay23();
                        break;
                    case CommonConstants.INT_24:
                        data = copyFromInfo.getYDay24();
                        break;
                    case CommonConstants.INT_25:
                        data = copyFromInfo.getYDay25();
                        break;
                    case CommonConstants.INT_26:
                        data = copyFromInfo.getYDay26();
                        break;
                    case CommonConstants.INT_27:
                        data = copyFromInfo.getYDay27();
                        break;
                    case CommonConstants.INT_28:
                        data = copyFromInfo.getYDay28();
                        break;
                    case CommonConstants.INT_29:
                        data = copyFromInfo.getYDay29();
                        break;
                    case CommonConstants.INT_30:
                        data = copyFromInfo.getYDay30();
                        break;
                    case CommonConstants.INT_31:
                        data = copyFromInfo.getYDay31();
                        break;
                    default:
                        break;
                }
                if (data > 0) {
                    ll_from_yotei = true;
                    break;
                }
            }

            if (ll_from_yotei && li_total > CommonConstants.INT_0) {
                // 複写元に予定があるが、複写先に予定がない = 抜け落ちた加算

                // 複写先から親サービスを検索する
                List<CmnTucPlanRiyou> tempList = ids_detail_to.stream()
                        .filter((item) -> item.getOyaLineNo().equals(copyFromInfo.getOyaLineNo())
                                && (item.getGouseiSikKbn().equals(CommonConstants.STR_1)
                                        || item.getGouseiSikKbn().equals(CommonConstants.STR_2)))
                        .toList();
                if (!tempList.isEmpty()) {
                    CmnTucPlanRiyou plan = tempList.getFirst();

                    for (int i = CommonConstants.INT_1; i <= CommonConstants.INT_31; i++) {
                        Integer data = CommonConstants.INT_0;
                        switch (i) {
                            case CommonConstants.INT_1:
                                data = plan.getYDay01();
                                break;
                            case CommonConstants.INT_2:
                                data = plan.getYDay02();
                                break;
                            case CommonConstants.INT_3:
                                data = plan.getYDay03();
                                break;
                            case CommonConstants.INT_4:
                                data = plan.getYDay04();
                                break;
                            case CommonConstants.INT_5:
                                data = plan.getYDay05();
                                break;
                            case CommonConstants.INT_6:
                                data = plan.getYDay06();
                                break;
                            case CommonConstants.INT_7:
                                data = plan.getYDay07();
                                break;
                            case CommonConstants.INT_8:
                                data = plan.getYDay08();
                                break;
                            case CommonConstants.INT_9:
                                data = plan.getYDay09();
                                break;
                            case CommonConstants.INT_10:
                                data = plan.getYDay10();
                                break;
                            case CommonConstants.INT_11:
                                data = plan.getYDay11();
                                break;
                            case CommonConstants.INT_12:
                                data = plan.getYDay12();
                                break;
                            case CommonConstants.INT_13:
                                data = plan.getYDay13();
                                break;
                            case CommonConstants.INT_14:
                                data = plan.getYDay14();
                                break;
                            case CommonConstants.INT_15:
                                data = plan.getYDay15();
                                break;
                            case CommonConstants.INT_16:
                                data = plan.getYDay16();
                                break;
                            case CommonConstants.INT_17:
                                data = plan.getYDay17();
                                break;
                            case CommonConstants.INT_18:
                                data = plan.getYDay18();
                                break;
                            case CommonConstants.INT_19:
                                data = plan.getYDay19();
                                break;
                            case CommonConstants.INT_20:
                                data = plan.getYDay20();
                                break;
                            case CommonConstants.INT_21:
                                data = plan.getYDay21();
                                break;
                            case CommonConstants.INT_22:
                                data = plan.getYDay22();
                                break;
                            case CommonConstants.INT_23:
                                data = plan.getYDay23();
                                break;
                            case CommonConstants.INT_24:
                                data = plan.getYDay24();
                                break;
                            case CommonConstants.INT_25:
                                data = plan.getYDay25();
                                break;
                            case CommonConstants.INT_26:
                                data = plan.getYDay26();
                                break;
                            case CommonConstants.INT_27:
                                data = plan.getYDay27();
                                break;
                            case CommonConstants.INT_28:
                                data = plan.getYDay28();
                                break;
                            case CommonConstants.INT_29:
                                data = plan.getYDay29();
                                break;
                            case CommonConstants.INT_30:
                                data = plan.getYDay30();
                                break;
                            case CommonConstants.INT_31:
                                data = plan.getYDay31();
                                break;
                            default:
                                break;
                        }
                        if (data > CommonConstants.INT_0) {
                            // 親サービスの予定がある場合、1回目の予定日に「1」を立てる
                            // ただし前の保険に使用しているサービスがあれば「1」は立てない

                            // 変数初期化
                            int ll_y_total_bk = CommonConstants.INT_0;
                            CmnTucPlanRiyou planBk = null;
                            if (!ids_detail_to_bk.isEmpty()) {
                                List<CmnTucPlanRiyou> tempList2 = ids_detail_to.stream()
                                        .filter((item) -> item.getOyaLineNo().equals(copyFromInfo.getOyaLineNo())
                                                && item.getScode().equals(copyFromInfo.getScode()))
                                        .toList();
                                if (!tempList2.isEmpty()) {
                                    planBk = tempList2.getFirst();
                                    ll_y_total_bk = planBk.getYTotal();
                                }
                            }

                            if (ll_y_total_bk < CommonConstants.INT_1) {
                                copyDataYoTeiArray[i - 1] = CommonConstants.INT_1;

                                // 判断用にbkのy_totalに1を立てる
                                if (planBk != null) {
                                    planBk.setYTotal(CommonConstants.INT_1);
                                }
                            }

                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * 利用票サービス項目ID取得（総合）
     *
     * @param hokenCd 保険者コード
     * @param svtype  サービス種別コード
     * @param svcode  サービス項目コード
     * @param ymd     日付
     * @param termId  有効期間ID
     * @return [0]:運行結果 [1]:サービス項目ID
     */
    private int[] ufGetItemcdSougou(Integer hokenCd, String svtype, String svcode, String ymd, Integer termId) {
        int[] result = new int[CommonConstants.INT_2];

        // 引き数チェック
        if (termId <= 0 || svtype == null || svtype.isEmpty() || svcode == null || svcode.isEmpty()) {
            result[CommonConstants.INT_0] = CommonConstants.INT_1;
            return result;
        }

        // 処理年月の月末日を求める
        String lastYmd = kghCmn03gFunc01Logic.getTukimatu(ymd);

        if (CommonDtoUtil.checkStringEqual(svtype, CommonConstants.STR_A_ONE)
                && CommonDtoUtil.checkStringEqual(svtype, CommonConstants.STR_A_FIVE)
                && CommonDtoUtil.checkStringEqual(svtype, CommonConstants.NUM_STR_35)) {
            hokenCd = CommonConstants.INT_0;
        }

        // 複写先のサービス項目IDを取得する
        Integer svItemCd = null;
        SakiItemCodeByCriteriaInEntity entity = new SakiItemCodeByCriteriaInEntity();
        entity.setAsSvtype(svtype);
        entity.setAsSvcode(svcode);
        entity.setLlKHokenCd(CommonDtoUtil.objValToString(hokenCd));
        entity.setAiTermid(CommonDtoUtil.objValToString(termId));
        entity.setLsGetumatuYmd(lastYmd);
        List<SakiItemCodeOutEntity> rets = comMhcItemuseSougouSelectMapper.findSakiItemCodeByCriteria(entity);

        if (!rets.isEmpty()) {
            svItemCd = rets.getFirst().getItemcode();
        }
        if (svItemCd == null || CommonConstants.NUMBER_0.equals(svItemCd)) {
            result[CommonConstants.INT_0] = CommonConstants.INT_MINUS_1;
        } else {
            result[CommonConstants.INT_0] = CommonConstants.INT_1;
            result[CommonConstants.INT_1] = svItemCd;
        }
        return result;
    }

    /**
     * 関数_利用票サービスコード変換（呼出用）
     * uf_cnv_service_yok
     *
     * @param copyFromSvItemCd 変換元サービス
     * @param ymd              変換元基準日
     * @param copyFromTermId   変換元有効期間ID
     * @param gouseiSikKbn     変換元合成識別区分
     * @param copyToYm         変換先基準日
     * @param copyToTermId     変換先有効期間ID
     * @param copyToYokaiDays  変換先年月での要介護度
     * @param oyaLineNo        変換元親レコード番号
     * @param svJigyoId        変換先事業者ID
     * @param svtype           サービス種別
     * @param copyMode         複写モード
     * @param maxOyaLineNo     最大親レコード番号
     */
    private ConvertScodeResult ufCnvServiceYok(Integer copyFromSvItemCd, String ymd, Integer copyFromTermId,
            String gouseiSikKbn, String copyToYm, Integer copyToTermId, List<Integer> copyToYokaiDays,
            Integer oyaLineNo, Integer svJigyoId, String svtype, String copyMode, Integer maxOyaLineNo) {
        ConvertScodeResult ret = new ConvertScodeResult();

        // サービスコード
        String scode;
        // サービス種別
        String svtypeNew;

        // サービスコード取得
        if (kghCmnF01Logic.chkSougouSvtype(svtype)) {
            String tempScode = kghCmnF01Logic.getScodeFromItemuseSougou(CommonConstants.INT_0, copyFromSvItemCd,
                    copyFromTermId);
            scode = tempScode.substring(CommonConstants.INT_0, CommonConstants.INT_6);
            svtypeNew = scode.substring(CommonConstants.INT_0, CommonConstants.INT_2);
        } else {
            String tempScode = kghCmnF01Logic.getScodeFromItemuse2(CommonConstants.INT_0, copyFromSvItemCd,
                    copyFromTermId);
            scode = tempScode.substring(CommonConstants.INT_0, CommonConstants.INT_6);
            svtypeNew = scode.substring(CommonConstants.INT_0, CommonConstants.INT_2);
        }

        // 加算は処理不要である
        if (CommonConstants.STR_3.equals(gouseiSikKbn)) {
            Integer isGyakutaiBousiGenzan = kghCmnF01Logic.isGyakutaiBousiGenzan(ymd, scode);
            Integer isGyomuKeizokuGenzan = kghCmnF01Logic.isGyomuKeizokuGenzan(ymd, scode);
            // 身体拘束廃止未実施減算
            Integer isShintaiKousokuGenzan = kghCmnF01Logic.isShintaiKousokuGenzan(ymd,
                    scode);

            if (!CommonConstants.NUMBER_1.equals(isGyakutaiBousiGenzan)
                    && !CommonConstants.NUMBER_1.equals(isGyomuKeizokuGenzan)
                    && !CommonConstants.NUMBER_1.equals(isShintaiKousokuGenzan)) {
                if (!CommonConstants.SCODE_STR_137000.equals(scode) && !CommonConstants.SCODE_STR_229000.equals(scode)
                        && !CommonConstants.SCODE_STR_326142.equals(scode)
                        && !CommonConstants.SCODE_STR_326502.equals(scode)
                        && !CommonConstants.SCODE_STR_376502.equals(scode)) {
                    if (!CommonConstants.STR_BOOLEN_T
                            .equals(scode.substring(CommonConstants.INT_2, CommonConstants.INT_3))) {
                        if (CommonConstants.NUM_STR_33.equals(svtypeNew)
                                || CommonConstants.NUM_STR_35.equals(svtypeNew)) {
                            boolean checkResult = kghCmnF01Logic.chkKasanService2(svtypeNew, scode, gouseiSikKbn);
                            if (checkResult) {
                                // ・変数.運行結果 = -1
                                ret.setResult(CommonConstants.INT_MINUS_1);
                                return ret;
                            }
                        } else if (CommonConstants.STR_65.equals(svtypeNew) || CommonConstants.STR_66.equals(svtypeNew)
                                || CommonConstants.SV_TYPE_73.equals(svtypeNew)
                                || CommonConstants.SV_TYPE_76.equals(svtypeNew)
                                || CommonConstants.SV_TYPE_77.equals(svtypeNew)
                                || CommonConstants.NUM_STR_32.equals(svtypeNew)
                                || CommonConstants.STR_A_FIVE.equals(svtypeNew)
                                || CommonConstants.SV_KIND_CD_A6.equals(svtypeNew)) {
                            Integer tempResult = kghCmnF01Logic.chkCnvCode(scode, ymd);
                            if (CommonConstants.NUMBER_0.equals(tempResult)) {
                                // ・変数.運行結果 = -1
                                ret.setResult(CommonConstants.INT_MINUS_1);
                                return ret;
                            }
                        } else {
                            // ・変数.運行結果 = -1
                            ret.setResult(CommonConstants.INT_MINUS_1);
                            return ret;
                        }
                    }
                }
            }
        }

        // 基準日取得
        String baseYmd = null;
        if (CommonConstants.NUM_STR_21.equals(svtypeNew) || CommonConstants.NUM_STR_24.equals(svtypeNew)
                || CommonConstants.NUM_STR_15.equals(svtypeNew) || CommonConstants.NUM_STR_33.equals(svtypeNew)
                || CommonConstants.NUM_STR_35.equals(svtypeNew) || CommonConstants.SV_KIND_CD_78.equals(svtypeNew)) {
            baseYmd = ymd;
        }
        if (kghCmnF01Logic.chkSougouSvtype(svtypeNew)) {
            baseYmd = ymd;
        }

        // サービス項目ID取得
        Integer convertedSvItemCd = getSvItemCdFunc(copyFromSvItemCd, scode, ymd, copyToYokaiDays.getFirst(),
                copyToYokaiDays.getFirst(), gouseiSikKbn, copyToTermId, baseYmd, svJigyoId, svtype);

        // サービス変更リスト設定
        List<ConvertScodeInfo> infos = new ArrayList<>();
        ret.setInfos(infos);
        ConvertScodeInfo info = new ConvertScodeInfo();
        info.setBaseOyaLineNo(oyaLineNo);
        info.setNewOyaLineNo(oyaLineNo);
        info.setSvJigyoId(svJigyoId);
        info.setSvItemCd(convertedSvItemCd);
        infos.add(info);

        int total = 0;
        Integer careLevel = 0;
        if (!copyToYokaiDays.isEmpty()) {
            careLevel = copyToYokaiDays.getFirst();
        }
        for (int i = CommonConstants.INT_1; i <= CommonConstants.INT_31; i++) {
            if (CommonDtoUtil.checkStringEqual(copyMode, CommonConstants.STR_0)
                    && CommonConstants.NUMBER_0.equals(careLevel)) {
                continue;
            }

            Integer tempCareLevel = copyToYokaiDays.get(i - 1);
            if (tempCareLevel != null && !tempCareLevel.equals(careLevel) && tempCareLevel > 0) {
                careLevel = tempCareLevel;

                // 要介護度変更があった場合には サービス項目ID を再取得
                Integer tempSvItemCd = getSvItemCdFunc(copyFromSvItemCd, scode, ymd, copyToYokaiDays.getFirst(),
                        tempCareLevel, gouseiSikKbn, copyToTermId, baseYmd, svJigyoId, svtype);
                if (tempSvItemCd != null && !tempSvItemCd.equals(convertedSvItemCd)) {
                    boolean checkResult = kghCmnF01Logic.chkKasanService2(svtypeNew, scode, gouseiSikKbn);
                    if (!checkResult) {
                        maxOyaLineNo++;
                    }
                    total = 0;
                    convertedSvItemCd = tempSvItemCd;
                    info = new ConvertScodeInfo();
                    info.setBaseOyaLineNo(oyaLineNo);
                    info.setNewOyaLineNo(maxOyaLineNo);
                    info.setSvJigyoId(svJigyoId);
                    info.setSvItemCd(convertedSvItemCd);
                    infos.add(info);
                }
            }

            total++;
            info.setTotal(total);
            info.getDays()[i - 1] = CommonConstants.INT_1;
        }

        ret.setResult(CommonConstants.INT_1);
        ret.setMaxOyaLineNo(maxOyaLineNo);
        return ret;
    }

    /**
     * サービス項目ID取得の関数
     *
     * @param svItemCd        サービス項目ID
     * @param scode           サービスコード
     * @param copyFromBaseYmd 変換元基準日
     * @param careLevel1      要介護度(1)
     * @param careLevel2      要介護度(2)
     * @param gouseiSikKbn    合成識別区分
     * @param copyToTermId    変換後有効期間ID
     * @param copyToBaseYmd   変換先基準日
     * @param svJigyoId       変換元時点での事業所id
     * @param svType          サービス種別
     * @return サービス項目ID
     */
    private Integer getSvItemCdFunc(Integer svItemCd, String scode, String copyFromBaseYmd, Integer careLevel1,
            Integer careLevel2, String gouseiSikKbn, Integer copyToTermId, String copyToBaseYmd, Integer svJigyoId,
            String svType) {
        // 変換後サービス項目ID
        Integer ret = null;

        SvKHokenCdByCriteriaInEntity hokenEntity = new SvKHokenCdByCriteriaInEntity();
        hokenEntity.setAlJid(CommonDtoUtil.objValToString(svJigyoId));

        List<SvKHokenCdOutEntity> hokens = comMscSvjigyoSelectMapper.findSvKHokenCdByCriteria(hokenEntity);
        // 保険者コード
        Integer hokenCd = null;
        if (!hokens.isEmpty()) {
            hokenCd = hokens.getFirst().getKHokenCd();
        }
        // 変換先基準日
        String toBaseYmd = copyToBaseYmd;
        if (toBaseYmd != null && toBaseYmd.length() < CommonConstants.INT_10) {
            toBaseYmd += CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART;
        }
        // サービス種類コード
        String svtype = scode.substring(CommonConstants.INT_0, CommonConstants.INT_2);
        // 変換フラグ
        boolean convertFlag = false;
        if (CommonConstants.SCODE_LEFT_TWO.contains(svtype)) {
            if (!CommonConstants.SCODE_STR_226000.equals(scode) && !CommonConstants.SCODE_STR_229000.equals(scode)
                    && !CommonConstants.SCODE_STR_256000.equals(scode)
                    && !CommonConstants.SCODE_STR_259000.equals(scode)
                    && !CommonConstants.SCODE_STR_2A6000.equals(scode)
                    && !CommonConstants.SCODE_STR_2B6000.equals(scode)
                    && !CommonConstants.STR_BOOLEN_T
                            .equals(scode.substring(CommonConstants.INT_2, CommonConstants.INT_3))
                    && CommonConstants.STR_80
                            .compareTo(scode.substring(CommonConstants.INT_0, CommonConstants.INT_2)) > 0) {
                convertFlag = true;
            }
        } else {
            if (!CommonConstants.SCODE_STR_137000.equals(scode) && !CommonConstants.SCODE_STR_326142.equals(scode)
                    && !CommonConstants.SCODE_STR_326143.equals(scode)
                    && !CommonConstants.SCODE_STR_326144.equals(scode)
                    && !CommonConstants.SCODE_STR_326502.equals(scode)
                    && !CommonConstants.SCODE_STR_336125.equals(scode)
                    && !CommonConstants.SCODE_STR_336126.equals(scode)
                    && !CommonConstants.SCODE_STR_336127.equals(scode)
                    && !CommonConstants.SCODE_STR_366125.equals(scode)
                    && !CommonConstants.SCODE_STR_366126.equals(scode)
                    && !CommonConstants.SCODE_STR_366127.equals(scode)
                    && !CommonConstants.SCODE_STR_376502.equals(scode)
                    && !CommonConstants.SCODE_STR_766100.equals(scode)
                    && !CommonConstants.SCODE_STR_776100.equals(scode)) {
                convertFlag = true;
            }
        }
        if (convertFlag) {
            // 関数_利用票サービスコード変換（メイン）
            ConvertScodeRankResult rankResult = convertScodeRank(scode, copyFromBaseYmd, careLevel1, careLevel2,
                    gouseiSikKbn, svType, copyToTermId, CommonDtoUtil.objValToString(hokenCd), toBaseYmd);
            String startScode = rankResult.getAsScodeBom();
            String endScode = rankResult.getAsScodeEom();
            toBaseYmd = rankResult.getAsNewDate();
            Integer result = rankResult.getResult();
            Integer svItemCd1 = null;
            Integer svItemCd2 = null;

            if (CommonConstants.NUMBER_1.equals(result)) {
                // 変更があったの場合
                if (CommonConstants.NUM_STR_21.equals(svtype) || CommonConstants.NUM_STR_24.equals(svtype)
                        || CommonConstants.NUM_STR_15.equals(svtype) || CommonConstants.NUM_STR_33.equals(svtype)
                        || CommonConstants.SV_KIND_CD_78.equals(svtype)) {
                    // サービス種類コード = "21" または "24" または "15" または "33" または "78"の場合
                    svItemCd1 = kghCmn03gFunc01Logic.getItemcodeFromScode21(startScode + CommonConstants.SHURUI_00,
                            copyToTermId, CommonConstants.INT_0, toBaseYmd);
                    svItemCd2 = kghCmn03gFunc01Logic.getItemcodeFromScode21(endScode + CommonConstants.SHURUI_00,
                            copyToTermId, CommonConstants.INT_0, toBaseYmd);
                } else if (kghCmnF01Logic.chkSougouSvtype(svtype)) {
                    int[] rets = ufGetItemcdSougou(hokenCd,
                            startScode.substring(CommonConstants.INT_0, CommonConstants.INT_2),
                            startScode.substring(CommonConstants.INT_2, CommonConstants.INT_6), toBaseYmd,
                            copyToTermId);
                    svItemCd1 = rets[CommonConstants.INT_1];

                    rets = ufGetItemcdSougou(hokenCd, endScode.substring(CommonConstants.INT_0, CommonConstants.INT_2),
                            endScode.substring(CommonConstants.INT_2, CommonConstants.INT_6), toBaseYmd, copyToTermId);
                    svItemCd2 = rets[CommonConstants.INT_1];
                } else {
                    svItemCd1 = kghCmn03gFunc01Logic.getItemcodeFromScode(startScode + CommonConstants.SHURUI_00,
                            copyToTermId, CommonConstants.INT_0);
                    svItemCd2 = kghCmn03gFunc01Logic.getItemcodeFromScode(endScode + CommonConstants.SHURUI_00,
                            copyToTermId, CommonConstants.INT_0);
                }
            } else {
                svItemCd1 = svItemCd;
                svItemCd2 = svItemCd;
            }

            // 返却情報取得
            if (svItemCd1 == null) {
                svItemCd1 = 0;
            }
            if (svItemCd2 == null) {
                svItemCd2 = 0;
            }
            if (CommonConstants.NUMBER_0.equals(svItemCd1) && CommonConstants.NUMBER_0.equals(svItemCd2)) {
                ret = svItemCd2;
            } else {
                ret = svItemCd2;
            }
        } else {
            ret = svItemCd;
        }
        return ret;
    }

    /**
     * 関数_利用票サービスコード変換（メイン） ue_cnv_scode_rank
     *
     * @param as_scode_base     変換元サービスコード
     * @param as_base_date      基準日
     * @param ai_yokai_kbn_bom  月初時要介護度
     * @param ai_yokai_kbn_eom  月末時要介護度
     * @param as_gousei_sik_kbn 合成識別区分
     * @param is_yok_svtype     サービス種別
     * @param ii_yok_term_to    変換後有効期間ID
     * @param il_yok_k_hoken_cd 保険者コード
     * @param is_yok_ymd_to     変換先基準日
     */
    private ConvertScodeRankResult convertScodeRank(String as_scode_base, String as_base_date, Integer ai_yokai_kbn_bom,
            Integer ai_yokai_kbn_eom, String as_gousei_sik_kbn, String is_yok_svtype, Integer ii_yok_term_to,
            String il_yok_k_hoken_cd, String is_yok_ymd_to) {
        ConvertScodeRankResult result = new ConvertScodeRankResult();

        Integer li_ret = CommonConstants.INT_0;
        String[] ls_rank_bom = new String[CommonConstants.INT_8];
        String[] ls_rank_eom = new String[CommonConstants.INT_8];
        String ls_base_date = null;
        String ls_new_date = null;

        // 基準日のスラッシュを除去
        ls_base_date = nds3GkFunc01Logic.getCnvYymmdd(as_base_date);

        // 2012/02/01 H24法改正対応 <EMAIL> MOD START (№25)
        // 処理対象サービスの判別（対象外サービスの場合は終了）

        // 2024/4/4 koki-o G3C45L144_202404改正_利用票_計画複写_4月版_要介護変換 mod start

        Integer isGyakutaiBousiGenzan = kghCmnF01Logic.isGyakutaiBousiGenzan(as_base_date, as_scode_base);
        Integer isGyomuKeizokuGenzan = kghCmnF01Logic.isGyomuKeizokuGenzan(as_base_date, as_scode_base);
        // 身体拘束廃止未実施減算
        Integer isShintaiKousokuGenzan = kghCmnF01Logic.isShintaiKousokuGenzan(as_base_date, as_scode_base);

        if (CommonConstants.NUMBER_1.equals(isGyakutaiBousiGenzan)
                || CommonConstants.NUMBER_1.equals(isGyomuKeizokuGenzan)
                || CommonConstants.NUMBER_1.equals(isShintaiKousokuGenzan)) {
            // 虐防、業未の単独減算の場合

            // 2024/4/8 koki-o G3C45L144_202404改正_利用票_計画複写_4月版_要介護変換 add start
            // 37:予防認知対応、39:予防認知短期は変換対象外
            String scodePrefix = as_scode_base.substring(CommonConstants.INT_0,
                    Math.min(as_scode_base.length(), CommonConstants.INT_2));
            switch (scodePrefix) {
                case CommonConstants.NUM_STR_37:
                case CommonConstants.NUM_STR_39:
                    result.setResult(CommonConstants.INT_0);
                    result.setAsScodeBom(as_scode_base);
                    result.setAsScodeEom(as_scode_base);
                    return result;
            }
            // 2024/4/8 koki-o G3C45L144_202404改正_利用票_計画複写_4月版_要介護変換 add end
        } else {
            String scodePrefix = as_scode_base.substring(CommonConstants.INT_0,
                    Math.min(as_scode_base.length(), CommonConstants.INT_2));
            switch (scodePrefix) {
                // 2018/9/13 koki-o G3C40R067_ケアマネ_サービスコンバート_総合事業要介護度(32を下に移動）
                case CommonConstants.NUM_STR_13:
                case CommonConstants.NUM_STR_15:
                case CommonConstants.NUM_STR_16:
                case CommonConstants.NUM_STR_21:
                case CommonConstants.NUM_STR_22:
                case CommonConstants.NUM_STR_23:
                case CommonConstants.NUM_STR_24:
                case CommonConstants.NUM_STR_25:
                case CommonConstants.NUM_STR_26:
                case CommonConstants.NUM_STR_38:
                case CommonConstants.NUM_STR_36:
                case CommonConstants.SV_CD_STR_72:
                case CommonConstants.SV_CD_STR_74:
                case CommonConstants.SV_CD_STR_75:
                case CommonConstants.NUM_STR_27:
                case CommonConstants.NUM_STR_28:
                case CommonConstants.NUM_STR_68:
                case CommonConstants.NUM_STR_69:
                case CommonConstants.NUM_STR_79:
                case CommonConstants.SV_KIND_CD_78:
                case CommonConstants.SV_TYPE_2A:
                case CommonConstants.SV_TYPE_2B:
                    if (CommonConstants.NUM_STR_3.equals(as_gousei_sik_kbn)) {
                        result.setResult(CommonConstants.INT_0);
                        result.setAsScodeBom(as_scode_base);
                        result.setAsScodeEom(as_scode_base);
                        return result;
                    }
                    break;
                case CommonConstants.NUM_STR_33:
                case CommonConstants.NUM_STR_35:
                case CommonConstants.STR_65:
                case CommonConstants.STR_66:
                case CommonConstants.SV_TYPE_73:
                case CommonConstants.SV_TYPE_76:
                case CommonConstants.SV_TYPE_77:
                case CommonConstants.NUM_STR_32:
                    // G3C41L111_NEXT201910改正対応_調査_利用票複写（NEXT）2019/07/18 ADD STR QSD H.Yamaguchi
                    // 外部利用型の訪問型サービスは変換しない
                    if (CommonConstants.STR_A_ONE.equals(is_yok_svtype)) {
                        result.setResult(CommonConstants.INT_0);
                        result.setAsScodeBom(as_scode_base);
                        result.setAsScodeEom(as_scode_base);
                        return result;
                    }
                    break;
                // 2018/9/13 koki-o G3C40R067_ケアマネ_サービスコンバート_総合事業要介護度
                case CommonConstants.STR_A_FIVE:
                case CommonConstants.STR_A_SIX:
                    break;
                default:
                    result.setResult(CommonConstants.INT_0);
                    result.setAsScodeBom(as_scode_base);
                    result.setAsScodeEom(as_scode_base);
                    return result;
            }
        }
        // 2024/4/4 koki-o G3C45L144_202404改正_利用票_計画複写_4月版_要介護変換 mod end

        // 基準もしくは変更先の要介護度が０の場合、無変換とする : 2011/04/17
        if (ai_yokai_kbn_bom == CommonConstants.INT_0 || ai_yokai_kbn_eom == CommonConstants.INT_0) {
            result.setResult(CommonConstants.INT_0);
            result.setAsScodeBom(as_scode_base);
            result.setAsScodeEom(as_scode_base);
            return result;
        }

        // 月初時要介護度変換（ND標準→厚労省標準）
        ufCnvYokaiKbn(ai_yokai_kbn_bom, ls_rank_bom);

        // 月末時要介護度変換（ND標準→厚労省標準）
        ufCnvYokaiKbn(ai_yokai_kbn_eom, ls_rank_eom);

        // 2024/4/4 koki-o G3C45L144_202404改正_利用票_計画複写_4月版_要介護変換 add start
        if (CommonConstants.NUMBER_1.equals(isGyakutaiBousiGenzan)
                || CommonConstants.NUMBER_1.equals(isGyomuKeizokuGenzan)) {
            // 虐防、業未の単独減算の場合
            return ufCnv2024Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom, ii_yok_term_to,
                    il_yok_k_hoken_cd, is_yok_ymd_to);
        }
        // 2024/4/4 koki-o G3C45L144_202404改正_利用票_計画複写_4月版_要介護変換 add end

        // サービスコード変換
        // 2014.05.20 K.Oomiya(JNET) DEL/ADD STR サービスコード英数字化
        String scodePrefix = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        switch (scodePrefix) {
            case CommonConstants.NUM_STR_13: // 訪問看護
                return ufCnv13Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
            case CommonConstants.NUM_STR_15:
            case CommonConstants.STR_65:
            case CommonConstants.SV_CD_STR_72:
            case CommonConstants.SV_CD_STR_74:
            case CommonConstants.SV_CD_STR_78: // 2016/01/20 <EMAIL> H2803改正対応 MOD
                return ufCnv15Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
            case CommonConstants.NUM_STR_16:
            case CommonConstants.STR_66: // 通所リハ
                return ufCnv16Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
            case CommonConstants.NUM_STR_21:
            case CommonConstants.NUM_STR_24: // 短期生活
                return ufCnv21Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
            case CommonConstants.NUM_STR_22:
            case CommonConstants.NUM_STR_25: // 短期老健
                return ufCnv22Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
            case CommonConstants.NUM_STR_23:
            case CommonConstants.NUM_STR_26: // 短期療養
                return ufCnv23Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
            case CommonConstants.SV_TYPE_2A:
            case CommonConstants.SV_TYPE_2B: // 短期医療院
                return ufCnv2ACode(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
            case CommonConstants.NUM_STR_32:
            case CommonConstants.NUM_STR_38: // 認知症対応型
                return ufCnv32Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
            case CommonConstants.NUM_STR_33:
            case CommonConstants.NUM_STR_35: // 特定施設(外部)
                // 2018/9/13 koki-o G3C40R067_ケアマネ_サービスコンバート_総合事業要介護度
                if (kghCmnF01Logic.chkSougouSvtype(is_yok_svtype)) {
                    return ufCnv35CodeSougou(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
                } else {
                    return ufCnv33Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
                }
            case CommonConstants.SV_CD_STR_73:
            case CommonConstants.SV_CD_STR_75:
            case CommonConstants.NUM_STR_68:
            case CommonConstants.NUM_STR_69: // 小規模多機能、小規模多機能短期 2015/2/25 koki-o H27改正対応
                return ufCnv73Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
            case CommonConstants.SV_TYPE_76: // 定期巡回・随時対応 訪問介護看護
                return ufCnv76Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
            case CommonConstants.SV_TYPE_77:
            case CommonConstants.NUM_STR_79:// 複合型サービス、複合型短期 2015/2/25 koki-o H27改正対応
                return ufCnv77Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
            case CommonConstants.NUM_STR_27: // 特定施設（短期）
                return ufCnv27Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
            case CommonConstants.NUM_STR_28: // 地域密着型特定施設（短期）
                // 2014.05.20 K.Oomiya(JNET) DEL/ADD END サービスコード英数字化
                return ufCnv27Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom);
            // 2018/9/13 koki-o G3C40R067_ケアマネ_サービスコンバート_総合事業要介護度
            case CommonConstants.STR_A_FIVE:
            case CommonConstants.STR_A_SIX:
                return ufCnvA5Code(as_scode_base, ls_base_date, ls_rank_bom, ls_rank_eom, ii_yok_term_to,
                        il_yok_k_hoken_cd,
                        is_yok_ymd_to);
            default:
                break;
        }

        result.setResult(li_ret);
        // 新基準日にスラッシュを付加
        result.setAsNewDate(nds3GkFunc01Logic.cnvYmd(ls_new_date));
        result.setAsScodeBom(as_scode_base);
        result.setAsScodeEom(as_scode_base);

        return result;
    }

    /**
     * サービスコード変換・A5A6
     *
     * @param as_scode_base     変換元サービスコード
     * @param as_base_date      基準日
     * @param as_yokai_kbn_bom  月初時要介護度
     * @param as_yokai_kbn_eom  月末時要介護度
     * @param ii_yok_term_to    変換後有効期間ID
     * @param il_yok_k_hoken_cd 保険者コード
     * @param is_yok_ymd_to     変換先基準日
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnvA5Code(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom, Integer ii_yok_term_to, String il_yok_k_hoken_cd, String is_yok_ymd_to) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        String ls_m_cyuka_kbn = null; // MST.定員超過・人員欠如識別
        String ls_m_jigsis_kbn = null; // MST.事業所・施設区分
        String ls_m_encyo_tais = null; // MST.時間延長サービス体制
        Integer li_m_syoyo_time_fr = null; // MST.開始時間
        Integer li_m_syoyo_time_to = null; // MST.終了時間
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_hwr_sv_sbt = null; // MST.日割計算用サービスCD識別区分
        String ls_m_sv_riyo_jkn = null; // MST.サービス利用条件 // 2012/11/01 add itagaki
        String ls_m_sv_teikyo = null; // MST.サービス提供体制強化加算 // 2012/11/01 add itagaki
        String ls_m_santei_tani = null; // MST.算定単位
        String ls_m_sv_kaisu = null; // MST.サービス提供内容 //2018/9/13 koki-o

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;
        int ll_count;

        String ls_yok_ymd_to;

        ls_yok_ymd_to = nds3GkFunc01Logic.getCnvYymmdd(is_yok_ymd_to);

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        HenkanMotoSvCdByCriteriaInEntity cond = new HenkanMotoSvCdByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);
        List<HenkanMotoSvCdOutEntity> outs = comMhcSmSelectMapper.findHenkanMotoSvCdByCriteria(cond);
        if (!outs.isEmpty()) {
            HenkanMotoSvCdOutEntity out = outs.getFirst();
            ls_m_cyuka_kbn = out.getCyukaKbn();
            ls_m_jigsis_kbn = out.getJigsisKbn();
            ls_m_encyo_tais = out.getEncyoTais();
            li_m_syoyo_time_fr = out.getSyoyoTimeFr();
            li_m_syoyo_time_to = out.getSyoyoTimeTo();
            ls_m_santei_tani = out.getSanteiTani();
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            ls_m_hwr_sv_sbt = out.getHwrSvSbt();
            ls_m_sv_riyo_jkn = out.getSvRiyoJkn();
            ls_m_sv_teikyo = out.getSvTeikyo();
            ls_m_sv_kaisu = out.getSvKaisu();
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        // 変換先が事業対象者の場合は処理しない
        if (li_chk_all_null != CommonConstants.INT_1) {
            result = CommonConstants.INT_MINUS_1;
            ret.setResult(result);
            return ret;
        }

        // NULLチェック
        if (ls_m_cyuka_kbn == null) {
            ls_m_cyuka_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_jigsis_kbn == null) {
            ls_m_jigsis_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_encyo_tais == null) {
            ls_m_encyo_tais = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_hwr_sv_sbt == null) {
            ls_m_hwr_sv_sbt = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_sv_riyo_jkn == null) {
            ls_m_sv_riyo_jkn = CommonConstants.EMPTY_STRING; // 2012/11/01 add itagaki
        }
        if (ls_m_sv_teikyo == null) {
            ls_m_sv_teikyo = CommonConstants.EMPTY_STRING; // 2012/11/01 add itagaki
        }
        if (li_m_syoyo_time_fr == null) {
            li_m_syoyo_time_fr = CommonConstants.INT_0;
        }
        if (li_m_syoyo_time_to == null) {
            li_m_syoyo_time_to = CommonConstants.INT_0;
        }
        if (ls_m_sv_kaisu == null) {
            ls_m_sv_kaisu = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_santei_tani == null) {
            ls_m_santei_tani = CommonConstants.EMPTY_STRING;
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 取得先を初期化
            ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
            ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0 || li_y == CommonConstants.INT_1) {
                SvCdSanteiByCriteriaInEntity cond2 = new SvCdSanteiByCriteriaInEntity();
                cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                cond2.setLsMCyukaKbn(ls_m_cyuka_kbn);
                cond2.setLsMJigsisKbn(ls_m_jigsis_kbn);
                cond2.setLsYokYmdTo(ls_yok_ymd_to);
                cond2.setLsMEncyoTais(ls_m_encyo_tais);
                cond2.setLiMSyoyoTimeFr(li_m_syoyo_time_fr);
                cond2.setLiMSyoyoTimeTo(li_m_syoyo_time_to);
                cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                cond2.setLsMSvRiyoJkn(ls_m_sv_riyo_jkn);
                cond2.setLsMSvTeikyo(ls_m_sv_teikyo);
                cond2.setLsMSvKaisu(ls_m_sv_kaisu);
                cond2.setLsMSanteiTani(ls_m_santei_tani);
                List<SvCdSanteiOutEntity> out2s = comMhcSmSelectMapper.findSvCdSanteiByCriteria(cond2);
                if (!out2s.isEmpty()) {
                    SvCdSanteiOutEntity out2 = out2s.getFirst();
                    ls_s_sv_shu_cd = out2.getSvShuCd();
                    ls_s_sv_kou_cd = out2.getSvKouCd();
                }

                // A6の場合、com_mhc_itemuse_sougouにレコードがあるか確認する
                if (ls_s_sv_shu_cd != null && ls_s_sv_shu_cd.equals(CommonConstants.STR_A_SIX)) {
                    ComMhcItemuseSougouByCriteriaInEntity cond3 = new ComMhcItemuseSougouByCriteriaInEntity();
                    cond3.setTermid(ii_yok_term_to);
                    cond3.setKHokenCd(il_yok_k_hoken_cd);
                    cond3.setSvtype(ls_s_sv_shu_cd);
                    cond3.setSvcode(ls_s_sv_kou_cd);
                    cond3.setYmd(is_yok_ymd_to);
                    ComMhcItemuseSougouOutEntity out3 = comMhcItemuseSougouSelectMapper
                            .countComMhcItemuseSougouByCriteria(cond3);
                    ll_count = CommonConstants.INT_0;
                    if (out3 != null && out3.getCnt() != null) {
                        ll_count = out3.getCnt();
                    }
                    if (ll_count <= CommonConstants.INT_0) {
                        ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
                        ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;
                    }
                }
                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（特定施設・短期）
     *
     * @param as_scode_base    変換元サービスコード
     * @param as_base_date     基準日
     * @param as_yokai_kbn_bom 月初時要介護度
     * @param as_yokai_kbn_eom 月末時要介護度
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv27Code(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        String ls_m_cyuka_kbn = null; // MST.定員超過・人員欠如識別
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_hwr_sv_sbt = null;// MST.日割計算用サービスCD識別区分
        // String ls_m_scode_term = null;// MST.サービスコード末尾1桁

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;
        String ls_gousei_sik_kbn = null;// 合成識別区分
        String ls_jigsis_kbn = null;// 事業所・施設区分
        String ls_sv_kaisu = null;// サービス提供内容（サービス提供回数！？）
        String ls_yusika_kbn = null;// 有資格者区分

        // 2012/03/03
        String[] ls_cyu_cd = new String[CommonConstants.INT_13];

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        ComMhcKmDetails4ByCriteriaInEntity cond = new ComMhcKmDetails4ByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);
        List<ComMhcKmDetails4OutEntity> outs = comMhcKmSelectMapper.findComMhcKmDetails4ByCriteria(cond);
        if (!outs.isEmpty()) {
            ComMhcKmDetails4OutEntity out = outs.getFirst();
            ls_m_cyuka_kbn = out.getCyukaKbn();
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            ls_m_hwr_sv_sbt = out.getHwrSvSbt();
            // ls_m_scode_term = out.getScodeTerm();
            ls_gousei_sik_kbn = out.getGouseiSikKbn();
            ls_jigsis_kbn = out.getJigsisKbn();
            ls_yusika_kbn = out.getYusikaKbn();
            ls_sv_kaisu = out.getSvKaisu();
            ls_cyu_cd[CommonConstants.INT_0] = out.getCyuCd1();
            ls_cyu_cd[CommonConstants.INT_1] = out.getCyuCd2();
            ls_cyu_cd[CommonConstants.INT_2] = out.getCyuCd3();
            ls_cyu_cd[CommonConstants.INT_3] = out.getCyuCd4();
            ls_cyu_cd[CommonConstants.INT_4] = out.getCyuCd5();
            ls_cyu_cd[CommonConstants.INT_5] = out.getCyuCd6();
            ls_cyu_cd[CommonConstants.INT_6] = out.getCyuCd7();
            ls_cyu_cd[CommonConstants.INT_7] = out.getCyuCd8();
            ls_cyu_cd[CommonConstants.INT_8] = out.getCyuCd9();
            ls_cyu_cd[CommonConstants.INT_9] = out.getCyuCd10();
            ls_cyu_cd[CommonConstants.INT_10] = out.getCyuCd11();
            ls_cyu_cd[CommonConstants.INT_11] = out.getCyuCd12();
            ls_cyu_cd[CommonConstants.INT_12] = out.getCyuCd13();
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        // NULLチェック
        if (ls_m_cyuka_kbn == null) {
            ls_m_cyuka_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_hwr_sv_sbt == null) {
            ls_m_hwr_sv_sbt = CommonConstants.EMPTY_STRING;
        }
        if (ls_gousei_sik_kbn == null) {
            ls_gousei_sik_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_jigsis_kbn == null) {
            ls_jigsis_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_sv_kaisu == null) {
            ls_sv_kaisu = CommonConstants.EMPTY_STRING;
        }
        if (ls_yusika_kbn == null) {
            ls_yusika_kbn = CommonConstants.EMPTY_STRING;
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0
                    || (li_y == CommonConstants.INT_1 && li_chk_all_null == CommonConstants.INT_1)) {

                // 取得先を初期化
                ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
                ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

                // サービスコード算定
                ComMhcKmGouseiSikKbn6ByCriteriaInEntity cond2 = new ComMhcKmGouseiSikKbn6ByCriteriaInEntity();
                cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                cond2.setLsMCyukaKbn(ls_m_cyuka_kbn);
                cond2.setAsBaseDate(as_base_date);
                cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                cond2.setLsGouseiSikKbn(ls_gousei_sik_kbn);
                List<ComMhcKmGouseiSikKbn6OutEntity> out2s = comMhcKmSelectMapper
                        .findComMhcKmGouseiSikKbn6ByCriteria(cond2);
                if (!out2s.isEmpty()) {
                    ComMhcKmGouseiSikKbn6OutEntity out2 = out2s.getFirst();
                    ls_s_sv_shu_cd = out2.getSvShuCd();
                    ls_s_sv_kou_cd = out2.getSvKouCd();
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（複合型）
     *
     * @param as_scode_base    変換元サービスコード
     * @param as_base_date     基準日
     * @param as_yokai_kbn_bom 月初時要介護度
     * @param as_yokai_kbn_eom 月末時要介護度
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv77Code(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        String ls_m_cyuka_kbn = null; // MST.定員超過・人員欠如識別
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_hwr_sv_sbt = null;// MST.日割計算用サービスCD識別区分
        String ls_m_scode_term = null;// MST.サービスコード末尾1桁

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;
        String ls_gousei_sik_kbn = null; // 合成識別区分
        String ls_jigsis_kbn = null;// 事業所・施設区分
        String ls_sv_kaisu = null;// サービス提供内容
        String ls_yusika_kbn = null;// 有資格者区分

        // 2012/03/03
        String[] ls_cyu_cd = new String[CommonConstants.INT_13];

        String ls_yobi_328 = null; // サテライト体制
        // G3S39L123_H30介護_ケアマネ_計画複写 2018/03/12 itagaki

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        // G3S39L123_H30介護_ケアマネ_計画複写 yobi_328追加 2018/03/12 itagaki
        ComMhcKmDetails3ByCriteriaInEntity cond = new ComMhcKmDetails3ByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);

        List<ComMhcKmDetails3OutEntity> outs = comMhcKmSelectMapper.findComMhcKmDetails3ByCriteria(cond);
        if (!outs.isEmpty()) {
            ComMhcKmDetails3OutEntity out = outs.getFirst();
            ls_m_cyuka_kbn = out.getCyukaKbn();
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            ls_m_hwr_sv_sbt = out.getHwrSvSbt();
            ls_m_scode_term = out.getScodeTerm();
            ls_gousei_sik_kbn = out.getGouseiSikKbn();
            ls_jigsis_kbn = out.getJigsisKbn();
            ls_yusika_kbn = out.getYusikaKbn();
            ls_sv_kaisu = out.getSvKaisu();
            ls_cyu_cd[CommonConstants.INT_0] = out.getCyuCd1();
            ls_cyu_cd[CommonConstants.INT_1] = out.getCyuCd2();
            ls_cyu_cd[CommonConstants.INT_2] = out.getCyuCd3();
            ls_cyu_cd[CommonConstants.INT_3] = out.getCyuCd4();
            ls_cyu_cd[CommonConstants.INT_4] = out.getCyuCd5();
            ls_cyu_cd[CommonConstants.INT_5] = out.getCyuCd6();
            ls_cyu_cd[CommonConstants.INT_6] = out.getCyuCd7();
            ls_cyu_cd[CommonConstants.INT_7] = out.getCyuCd8();
            ls_cyu_cd[CommonConstants.INT_8] = out.getCyuCd9();
            ls_cyu_cd[CommonConstants.INT_9] = out.getCyuCd10();
            ls_cyu_cd[CommonConstants.INT_10] = out.getCyuCd11();
            ls_cyu_cd[CommonConstants.INT_11] = out.getCyuCd12();
            ls_cyu_cd[CommonConstants.INT_12] = out.getCyuCd13();
            ls_yobi_328 = out.getYobi328();
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        /* 2012/11/02 add itagaki -------------------------------------------------- */
        if (ls_q_sv_shu_cd.equals(CommonConstants.SV_CD_STR_73) && ls_q_sv_kou_cd.equals(CommonConstants.STR_6129)) {
            // 要介護２以外の場合は認知症区分Ⅱを認知症区分Ⅰに変換

            if (ls_rank[CommonConstants.INT_2][CommonConstants.INT_0] == null
                    || !ls_rank[CommonConstants.INT_2][CommonConstants.INT_0].equals(CommonConstants.STR_1)) {
                ls_rank[CommonConstants.INT_0][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_1][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_2][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_3][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_4][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_5][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_6][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_7][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
            }

            if (ls_rank[CommonConstants.INT_2][CommonConstants.INT_1] == null
                    || !ls_rank[CommonConstants.INT_2][CommonConstants.INT_1].equals(CommonConstants.STR_1)) {
                ls_rank[CommonConstants.INT_0][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_1][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_2][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_3][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_4][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_5][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_6][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_7][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
            }
        }
        /*
         * 2012/11/02 add end itagaki --------------------------------------------------
         */

        // NULLチェック
        if (ls_m_cyuka_kbn == null) {
            ls_m_cyuka_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_hwr_sv_sbt == null) {
            ls_m_hwr_sv_sbt = CommonConstants.EMPTY_STRING;
        }
        if (ls_gousei_sik_kbn == null) {
            ls_gousei_sik_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_jigsis_kbn == null) {
            ls_jigsis_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_sv_kaisu == null) {
            ls_sv_kaisu = CommonConstants.EMPTY_STRING;
        }
        if (ls_yusika_kbn == null) {
            ls_yusika_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_yobi_328 == null) {
            ls_yobi_328 = CommonConstants.EMPTY_STRING;
            // G3S39L123_H30介護_ケアマネ_計画複写 2018/03/12 itagaki
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0
                    || (li_y == CommonConstants.INT_1 && li_chk_all_null == CommonConstants.INT_1)) {

                // 取得先を初期化
                ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
                ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

                // サービスコード算定
                // 条件が不正な為修正 2012/11/02 modify itagaki
                if (ls_gousei_sik_kbn.equals(CommonConstants.NUM_STR_3)) {
                    // 加算サービス
                    switch (ls_q_sv_kou_cd) {
                        case CommonConstants.STR_6001:
                        case CommonConstants.STR_6002:
                        case CommonConstants.STR_6003:
                        case CommonConstants.STR_6004:
                        case CommonConstants.STR_6005:
                        case CommonConstants.STR_6006:
                        case CommonConstants.STR_6007:
                        case CommonConstants.STR_6008:
                        case CommonConstants.STR_6009:
                        case CommonConstants.STR_6010: {
                            // 医療訪問看護減算
                            ComMhcKmGouseiSikKbn1ByCriteriaInEntity cond2 = new ComMhcKmGouseiSikKbn1ByCriteriaInEntity();
                            cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                            cond2.setAsBaseDate(as_base_date);
                            cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                            cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                            cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                            cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                            cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                            cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                            cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                            cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                            cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                            cond2.setLsGouseiSikKbn(ls_gousei_sik_kbn);
                            List<ComMhcKmGouseiSikKbn1OutEntity> out2s = comMhcKmSelectMapper
                                    .findComMhcKmGouseiSikKbn1ByCriteria(cond2);
                            if (!out2s.isEmpty()) {
                                ComMhcKmGouseiSikKbn1OutEntity out2 = out2s.getFirst();
                                ls_s_sv_shu_cd = out2.getSvShuCd();
                                ls_s_sv_kou_cd = out2.getSvKouCd();
                            }
                            break;
                        }
                        case CommonConstants.STR_6011:
                        case CommonConstants.STR_6012:
                        case CommonConstants.STR_6013:
                        case CommonConstants.STR_6014:
                        case CommonConstants.STR_6015: {
                            // 訪問看護特別指示減算
                            ComMhcKmGouseiSikKbn2ByCriteriaInEntity cond2 = new ComMhcKmGouseiSikKbn2ByCriteriaInEntity();
                            cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                            cond2.setAsBaseDate(as_base_date);
                            cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                            cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                            cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                            cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                            cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                            cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                            cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                            cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                            cond2.setLsGouseiSikKbn(ls_gousei_sik_kbn);
                            List<ComMhcKmGouseiSikKbn2OutEntity> out2s = comMhcKmSelectMapper
                                    .findComMhcKmGouseiSikKbn2ByCriteria(cond2);
                            if (!out2s.isEmpty()) {
                                ComMhcKmGouseiSikKbn2OutEntity out2 = out2s.getFirst();
                                ls_s_sv_shu_cd = out2.getSvShuCd();
                                ls_s_sv_kou_cd = out2.getSvKouCd();
                            }
                            break;
                        }
                        case CommonConstants.STR_6129: {
                            // 認知症加算Ⅱ
                            ComMhcKmSabisuByCriteriaInEntity cond2 = new ComMhcKmSabisuByCriteriaInEntity();
                            cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                            cond2.setAsBaseDate(as_base_date);
                            cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                            cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                            cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                            cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                            cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                            cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                            cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                            cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                            cond2.setLsMGouseiSikKbn(ls_gousei_sik_kbn);
                            cond2.setLsMSvKaisu(ls_sv_kaisu);
                            List<ComMhcKmSabisuOutEntity> out2s = comMhcKmSelectMapper.findComMhcKmSabisuByCriteria(
                                    cond2);
                            if (!out2s.isEmpty()) {
                                ComMhcKmSabisuOutEntity out2 = out2s.getFirst();
                                ls_s_sv_shu_cd = out2.getSvShuCd();
                                ls_s_sv_kou_cd = out2.getSvKouCd();
                            }
                            break;
                        }
                        /* 2015/05/02 add koki-o -------------------------------------------------- */
                        case CommonConstants.STR_6021:
                        case CommonConstants.STR_6022:
                        case CommonConstants.STR_6023:
                        case CommonConstants.STR_6024:
                        case CommonConstants.STR_6025:
                        case CommonConstants.STR_6026:
                        case CommonConstants.STR_6027:
                        case CommonConstants.STR_6028:
                        case CommonConstants.STR_6029:
                        case CommonConstants.STR_6030: {
                            // 訪問看護体制減算
                            ComMhcKmGouseiSikKbn4ByCriteriaInEntity cond2 = new ComMhcKmGouseiSikKbn4ByCriteriaInEntity();
                            cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                            cond2.setAsBaseDate(as_base_date);
                            cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                            cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                            cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                            cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                            cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                            cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                            cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                            cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                            cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                            cond2.setLsGouseiSikKbn(ls_gousei_sik_kbn);
                            List<ComMhcKmGouseiSikKbn4OutEntity> out2s = comMhcKmSelectMapper
                                    .findComMhcKmGouseiSikKbn4ByCriteria(cond2);
                            if (!out2s.isEmpty()) {
                                ComMhcKmGouseiSikKbn4OutEntity out2 = out2s.getFirst();
                                ls_s_sv_shu_cd = out2.getSvShuCd();
                                ls_s_sv_kou_cd = out2.getSvKouCd();
                            }
                            break;
                        }
                        /* 2015/05/02 add koki-o -------------------------------------------------- */
                        default: {
                            break;
                        }
                    }
                } else {
                    // 基本サービス
                    // G3S39L123_H30介護_ケアマネ_計画複写 yobi_328追加 2018/03/12 itagaki
                    ComMhcKmGouseiSikKbn5ByCriteriaInEntity cond2 = new ComMhcKmGouseiSikKbn5ByCriteriaInEntity();
                    cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                    cond2.setLsMCyukaKbn(ls_m_cyuka_kbn);
                    cond2.setAsBaseDate(as_base_date);
                    cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                    cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                    cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                    cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                    cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                    cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                    cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                    cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                    cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                    cond2.setLsGouseiSikKbn(ls_gousei_sik_kbn);
                    cond2.setLsSvKaisu(ls_sv_kaisu);
                    cond2.setLsMScodeTerm(ls_m_scode_term);
                    cond2.setLsYobi328(ls_yobi_328);
                    List<ComMhcKmGouseiSikKbn5OutEntity> out2s = comMhcKmSelectMapper
                            .findComMhcKmGouseiSikKbn5ByCriteria(cond2);
                    if (!out2s.isEmpty()) {
                        ComMhcKmGouseiSikKbn5OutEntity out2 = out2s.getFirst();
                        ls_s_sv_shu_cd = out2.getSvShuCd();
                        ls_s_sv_kou_cd = out2.getSvKouCd();
                    }
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（訪問介護看護）
     *
     * @param as_scode_base    変換元サービスコード
     * @param as_base_date     基準日
     * @param as_yokai_kbn_bom 月初時要介護度
     * @param as_yokai_kbn_eom 月末時要介護度
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv76Code(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        String ls_m_cyuka_kbn = null; // MST.定員超過・人員欠如識別
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_hwr_sv_sbt = null; // MST.日割計算用サービスCD識別区分
        // String ls_m_scode_term = null; // MST.サービスコード末尾1桁
        String ls_m_yusika_kbn = null; // MST.有資格者区分 // 2012/11/02 add itagaki
        String ls_m_sv_riyo_jkn = null; // MST.サービス利用条件 // 2012/11/02 add itagaki

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;
        String ls_gousei_sik_kbn = null; // 合成識別区分
        String ls_jigsis_kbn = null; // 事業所・施設区分
        String ls_sv_kaisu = null; // サービス提供内容

        // 2012/03/03
        String[] ls_cyu_cd = new String[CommonConstants.INT_13];

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        // 有資格者区分、サービス利用条件も取得するように修正 2012/11/02 modify itagaki
        ComMhcKmDetails2ByCriteriaInEntity cond = new ComMhcKmDetails2ByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);
        List<ComMhcKmDetails2OutEntity> outs = comMhcKmSelectMapper.findComMhcKmDetails2ByCriteria(cond);
        if (!outs.isEmpty()) {
            ComMhcKmDetails2OutEntity out = outs.getFirst();
            ls_m_cyuka_kbn = out.getCyukaKbn();
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            ls_m_hwr_sv_sbt = out.getHwrSvSbt();
            // ls_m_scode_term = out.getScodeTerm();
            ls_gousei_sik_kbn = out.getGouseiSikKbn();
            ls_jigsis_kbn = out.getJigsisKbn();
            ls_sv_kaisu = out.getSvKaisu();
            ls_cyu_cd[CommonConstants.INT_0] = out.getCyuCd1();
            ls_cyu_cd[CommonConstants.INT_1] = out.getCyuCd2();
            ls_cyu_cd[CommonConstants.INT_2] = out.getCyuCd3();
            ls_cyu_cd[CommonConstants.INT_3] = out.getCyuCd4();
            ls_cyu_cd[CommonConstants.INT_4] = out.getCyuCd5();
            ls_cyu_cd[CommonConstants.INT_5] = out.getCyuCd6();
            ls_cyu_cd[CommonConstants.INT_6] = out.getCyuCd7();
            ls_cyu_cd[CommonConstants.INT_7] = out.getCyuCd8();
            ls_cyu_cd[CommonConstants.INT_8] = out.getCyuCd9();
            ls_cyu_cd[CommonConstants.INT_9] = out.getCyuCd10();
            ls_cyu_cd[CommonConstants.INT_10] = out.getCyuCd11();
            ls_cyu_cd[CommonConstants.INT_11] = out.getCyuCd12();
            ls_cyu_cd[CommonConstants.INT_12] = out.getCyuCd13();
            ls_m_yusika_kbn = out.getYusikaKbn();
            ls_m_sv_riyo_jkn = out.getSvRiyoJkn();
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        // NULLチェック
        if (ls_m_cyuka_kbn == null) {
            ls_m_cyuka_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_hwr_sv_sbt == null) {
            ls_m_hwr_sv_sbt = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_yusika_kbn == null) {
            ls_m_yusika_kbn = CommonConstants.EMPTY_STRING; // 2012/11/02 add itagaki
        }
        if (ls_m_sv_riyo_jkn == null) {
            ls_m_sv_riyo_jkn = CommonConstants.EMPTY_STRING; // 2012/11/02 add itagaki
        }
        if (ls_gousei_sik_kbn == null) {
            ls_gousei_sik_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_jigsis_kbn == null) {
            ls_jigsis_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_sv_kaisu == null) {
            ls_sv_kaisu = CommonConstants.EMPTY_STRING;
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0
                    || (li_y == CommonConstants.INT_1 && li_chk_all_null == CommonConstants.INT_1)) {

                // 取得先を初期化
                ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
                ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

                // サービスコード算定
                // 条件に有資格者区分、サービス利用条件を追加 2012/11/02 modify itagaki
                ComMhcKmSvName2ByCriteriaInEntity cond2 = new ComMhcKmSvName2ByCriteriaInEntity();
                cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                cond2.setLsMCyukaKbn(ls_m_cyuka_kbn);
                cond2.setAsBaseDate(as_base_date);
                cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                cond2.setLsGouseiSikKbn(ls_gousei_sik_kbn);
                cond2.setLsJigsisKbn(ls_jigsis_kbn);
                cond2.setLsSvKaisu(ls_sv_kaisu);
                cond2.setLsMYusikaKbn(ls_m_yusika_kbn);
                cond2.setLsMSvRiyoJkn(ls_m_sv_riyo_jkn);
                List<ComMhcKmSvName2OutEntity> out2s = comMhcKmSelectMapper.findComMhcKmSvName2ByCriteria(cond2);
                if (!out2s.isEmpty()) {
                    ComMhcKmSvName2OutEntity out2 = out2s.getFirst();
                    ls_s_sv_shu_cd = out2.getSvShuCd();
                    ls_s_sv_kou_cd = out2.getSvKouCd();
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（小規模多機能型居宅介護）
     *
     * @param as_scode_base    変換元サービスコード
     * @param as_base_date     基準日
     * @param as_yokai_kbn_bom 月初時要介護度
     * @param as_yokai_kbn_eom 月末時要介護度
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv73Code(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        String ls_m_cyuka_kbn = null; // MST.定員超過・人員欠如識別
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_hwr_sv_sbt = null; // MST.日割計算用サービスCD識別区分
        String ls_m_scode_term = null; // MST.サービスコード末尾1桁
        String ls_m_sv_kaisu = null; // MST.サービス提供内容 // 2012/11/02 add itagaki
        String ls_m_gousei_sik_kbn = null; // MST.合成識別区分 // 2012/11/02 add itagaki

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;

        // 2012/03/03
        String[] ls_cyu_cd = new String[CommonConstants.INT_13];
        int li_cyu_max;
        int li_cyu_cnt;

        // 2015/4/3 koki-o 小規居宅サービスの複写不具合の改修
        String ls_sv_kaisu = null;

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        // サービス提供内容、合成識別区分も取得するように修正 2012/11/02 modify itagaki
        ComMhcKmDetailsByCriteriaInEntity cond = new ComMhcKmDetailsByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);
        List<ComMhcKmDetailsOutEntity> outs = comMhcKmSelectMapper.findComMhcKmDetailsByCriteria(cond);
        if (!outs.isEmpty()) {
            ComMhcKmDetailsOutEntity out = outs.getFirst();
            ls_m_cyuka_kbn = out.getCyukaKbn();
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            ls_m_hwr_sv_sbt = out.getHwrSvSbt();
            ls_m_scode_term = out.getScodeTerm();
            ls_cyu_cd[CommonConstants.INT_0] = out.getCyuCd1();
            ls_cyu_cd[CommonConstants.INT_1] = out.getCyuCd2();
            ls_cyu_cd[CommonConstants.INT_2] = out.getCyuCd3();
            ls_cyu_cd[CommonConstants.INT_3] = out.getCyuCd4();
            ls_cyu_cd[CommonConstants.INT_4] = out.getCyuCd5();
            ls_cyu_cd[CommonConstants.INT_5] = out.getCyuCd6();
            ls_cyu_cd[CommonConstants.INT_6] = out.getCyuCd7();
            ls_cyu_cd[CommonConstants.INT_7] = out.getCyuCd8();
            ls_cyu_cd[CommonConstants.INT_8] = out.getCyuCd9();
            ls_cyu_cd[CommonConstants.INT_9] = out.getCyuCd10();
            ls_cyu_cd[CommonConstants.INT_10] = out.getCyuCd11();
            ls_cyu_cd[CommonConstants.INT_11] = out.getCyuCd12();
            ls_cyu_cd[CommonConstants.INT_12] = out.getCyuCd13();
            ls_m_sv_kaisu = out.getSvKaisu();
            ls_m_gousei_sik_kbn = out.getGouseiSikKbn();
            ls_sv_kaisu = out.getSvKaisu(); // 2015/4/3 koki-o
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        /* 2012/11/02 add itagaki -------------------------------------------------- */
        if (ls_q_sv_shu_cd.equals(CommonConstants.SV_CD_STR_73) && ls_q_sv_kou_cd.equals(CommonConstants.STR_6129)) {
            // 要介護２以外の場合は認知症区分Ⅱを認知症区分Ⅰに変換

            if (ls_rank[CommonConstants.INT_2][CommonConstants.INT_0] == null
                    || !ls_rank[CommonConstants.INT_2][CommonConstants.INT_0].equals(CommonConstants.STR_1)) {
                ls_rank[CommonConstants.INT_0][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_1][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_2][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_3][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_4][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_5][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_6][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_7][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
            }

            if (ls_rank[CommonConstants.INT_2][CommonConstants.INT_1] == null
                    || !ls_rank[CommonConstants.INT_2][CommonConstants.INT_1].equals(CommonConstants.STR_1)) {
                ls_rank[CommonConstants.INT_0][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_1][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_2][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_3][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_4][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_5][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_6][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_7][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
            }
        }
        /*
         * 2012/11/02 add end itagaki --------------------------------------------------
         */

        // NULLチェック
        if (ls_m_cyuka_kbn == null) {
            ls_m_cyuka_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_hwr_sv_sbt == null) {
            ls_m_hwr_sv_sbt = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_sv_kaisu == null) {
            ls_m_sv_kaisu = CommonConstants.EMPTY_STRING; // 2012/11/02 add itagaki
        }
        if (ls_m_gousei_sik_kbn == null) {
            ls_m_gousei_sik_kbn = CommonConstants.EMPTY_STRING; // 2012/11/02 add itagaki
        }
        if (ls_sv_kaisu == null) {
            ls_sv_kaisu = CommonConstants.EMPTY_STRING; // 2015/4/3 koki-o
        }

        li_cyu_max = ls_cyu_cd.length;
        for (li_cyu_cnt = CommonConstants.INT_0; li_cyu_cnt < li_cyu_max; li_cyu_cnt++) {
            if (ls_cyu_cd[li_cyu_cnt] == null) {
                ls_cyu_cd[li_cyu_cnt] = CommonConstants.EMPTY_STRING;
            }
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0
                    || (li_y == CommonConstants.INT_1 && li_chk_all_null == CommonConstants.INT_1)) {

                // 取得先を初期化
                ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
                ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

                // サービスコード算定
                // 加算サービスに対応する為に条件を変更 2012/11/02 modify itagaki
                if (ls_m_gousei_sik_kbn.equals(CommonConstants.NUM_STR_3)) {
                    // 加算サービス
                    ComMhcKmSabisuByCriteriaInEntity cond2 = new ComMhcKmSabisuByCriteriaInEntity();
                    cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                    cond2.setAsBaseDate(as_base_date);
                    cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                    cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                    cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                    cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                    cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                    cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                    cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                    cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                    cond2.setLsMSvKaisu(ls_m_sv_kaisu);
                    cond2.setLsMGouseiSikKbn(ls_m_gousei_sik_kbn);
                    List<ComMhcKmSabisuOutEntity> out2s = comMhcKmSelectMapper.findComMhcKmSabisuByCriteria(cond2);
                    if (!out2s.isEmpty()) {
                        ComMhcKmSabisuOutEntity out2 = out2s.getFirst();
                        ls_s_sv_shu_cd = out2.getSvShuCd();
                        ls_s_sv_kou_cd = out2.getSvKouCd();
                    }
                } else {
                    // 基本サービス
                    ComMhcKmKihonSabisuByCriteriaInEntity cond2 = new ComMhcKmKihonSabisuByCriteriaInEntity();
                    cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                    cond2.setLsMCyukaKbn(ls_m_cyuka_kbn);
                    cond2.setAsBaseDate(as_base_date);
                    cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                    cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                    cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                    cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                    cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                    cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                    cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                    cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                    cond2.setLsMScodeTerm(ls_m_scode_term);
                    cond2.setLsCyuCd1(ls_cyu_cd[CommonConstants.INT_0]);
                    cond2.setLsCyuCd2(ls_cyu_cd[CommonConstants.INT_1]);
                    cond2.setLsCyuCd3(ls_cyu_cd[CommonConstants.INT_2]);
                    cond2.setLsCyuCd4(ls_cyu_cd[CommonConstants.INT_3]);
                    cond2.setLsCyuCd5(ls_cyu_cd[CommonConstants.INT_4]);
                    cond2.setLsCyuCd6(ls_cyu_cd[CommonConstants.INT_5]);
                    cond2.setLsCyuCd7(ls_cyu_cd[CommonConstants.INT_6]);
                    cond2.setLsCyuCd8(ls_cyu_cd[CommonConstants.INT_7]);
                    cond2.setLsCyuCd9(ls_cyu_cd[CommonConstants.INT_8]);
                    cond2.setLsCyuCd10(ls_cyu_cd[CommonConstants.INT_9]);
                    cond2.setLsCyuCd11(ls_cyu_cd[CommonConstants.INT_10]);
                    cond2.setLsCyuCd12(ls_cyu_cd[CommonConstants.INT_11]);
                    cond2.setLsCyuCd13(ls_cyu_cd[CommonConstants.INT_12]);
                    cond2.setLsMGouseiSikKbn(ls_m_gousei_sik_kbn);
                    cond2.setLsSvKaisu(ls_sv_kaisu);
                    List<ComMhcKmKihonSabisuOutEntity> out2s = comMhcKmSelectMapper
                            .findComMhcKmKihonSabisuByCriteria(cond2);
                    if (!out2s.isEmpty()) {
                        ComMhcKmKihonSabisuOutEntity out2 = out2s.getFirst();
                        ls_s_sv_shu_cd = out2.getSvShuCd();
                        ls_s_sv_kou_cd = out2.getSvKouCd();
                    }
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（特定施設入居者生活介護）
     *
     * @param as_scode_base    変換元サービスコード
     * @param as_base_date     基準日
     * @param as_yokai_kbn_bom 月初時要介護度
     * @param as_yokai_kbn_eom 月末時要介護度
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv33Code(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        // String ls_m_jinin_kbn = null; // MST.人員配置区分
        Integer li_m_syoyo_time_fr = CommonConstants.INT_0; // MST.開始時間
        Integer li_m_syoyo_time_to = CommonConstants.INT_0; // MST.終了時間
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_gentai_kbn = null; // MST.支給限度額管理対象区分
        String ls_m_gai_sv_syu_cd = null; // MST.外部サービス種類コード
        String ls_m_hwr_sv_sbt = null; // MST.日割計算用サービスCD識別区分
        String ls_m_sv_name = null; // MST.サービス名
        String ls_m_shisetu_kbn = null; // MST.施設区分(サービス名中)
        String ls_m_gousei_sik_kbn = null; // MST.合成識別区分 // 2012/11/01 add itagaki
        String ls_m_cyuka_kbn = null; // MST.定員超過・人員欠如識別 // 2012/11/01 add itagaki

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;
        String ls_shisetu_kbn_l, ls_shisetu_kbn_s;
        int li_jigen_flg = CommonConstants.INT_0; // 2012/03/03

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        // 合成識別区分、定員超過・人員欠如識別区分も取得するように修正 2012/11/01 modify itagaki
        ComMhcKmKaigoInfoByCriteriaInEntity cond = new ComMhcKmKaigoInfoByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);
        List<ComMhcKmKaigoInfoOutEntity> outs = comMhcKmSelectMapper.findComMhcKmKaigoInfoByCriteria(cond);
        if (!outs.isEmpty()) {
            ComMhcKmKaigoInfoOutEntity out = outs.getFirst();
            // ls_m_jinin_kbn = out.getJininKbn();
            li_m_syoyo_time_fr = out.getSyoyoTimeFr();
            li_m_syoyo_time_to = out.getSyoyoTimeTo();
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            ls_m_gentai_kbn = out.getGentaiKbn();
            ls_m_gai_sv_syu_cd = out.getGaiSvSyuCd();
            ls_m_hwr_sv_sbt = out.getHwrSvSbt();
            ls_m_sv_name = out.getSvName();
            ls_m_gousei_sik_kbn = out.getGouseiSikKbn();
            ls_m_cyuka_kbn = out.getCyukaKbn();
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 処理対象サービス以外の場合は終了する
        if (as_base_date.compareTo(CommonConstants.STR_20160331) <= 0) {
            switch (ls_q_sv_shu_cd) {
                case CommonConstants.NUM_STR_33: {
                    String kouCdPrefix = ls_q_sv_kou_cd.substring(CommonConstants.INT_0,
                            Math.min(ls_q_sv_kou_cd.length(), CommonConstants.INT_2));
                    switch (kouCdPrefix) {
                        case CommonConstants.NUM_STR_11:
                        case CommonConstants.NUM_STR_17:
                        case CommonConstants.NUM_STR_18:
                        case CommonConstants.NUM_STR_19:
                        case CommonConstants.NUM_STR_21:
                        case CommonConstants.NUM_STR_22:
                        case CommonConstants.NUM_STR_23:
                        case CommonConstants.NUM_STR_24:
                        case CommonConstants.NUM_STR_90:
                        case CommonConstants.NUM_STR_25: { // 2016/01/20 <EMAIL> H2803改正対応 MOD
                            String kouCdPrefix2 = ls_q_sv_kou_cd.substring(CommonConstants.INT_0,
                                    Math.min(ls_q_sv_kou_cd.length(), CommonConstants.INT_3));
                            switch (kouCdPrefix2) {
                                case CommonConstants.NUM_STR_116, CommonConstants.NUM_STR_179,
                                        CommonConstants.NUM_STR_254 -> {
                                    result = CommonConstants.INT_0;
                                    ret.setResult(result);
                                    return ret;
                                }
                            }
                            break;
                        }
                        default: {
                            result = CommonConstants.INT_0;
                            ret.setResult(result);
                            return ret;
                        }
                    }
                    break;
                }
                case CommonConstants.NUM_STR_35: {
                    String kouCdPrefix = ls_q_sv_kou_cd.substring(CommonConstants.INT_0,
                            Math.min(ls_q_sv_kou_cd.length(), CommonConstants.INT_2));
                    switch (kouCdPrefix) {
                        case CommonConstants.NUM_STR_11:
                        case CommonConstants.NUM_STR_17:
                        case CommonConstants.NUM_STR_18:
                        case CommonConstants.NUM_STR_20:
                        case CommonConstants.NUM_STR_21:
                        case CommonConstants.NUM_STR_90: {
                            String kouCdPrefix2 = ls_q_sv_kou_cd.substring(CommonConstants.INT_0,
                                    Math.min(ls_q_sv_kou_cd.length(), CommonConstants.INT_3));
                            switch (kouCdPrefix2) {
                                case CommonConstants.NUM_STR_172, CommonConstants.NUM_STR_182,
                                        CommonConstants.NUM_STR_214 -> {
                                    result = CommonConstants.INT_0;
                                    ret.setResult(result);
                                    return ret;
                                }
                            }
                            break;
                        }
                        default: {
                            result = CommonConstants.INT_0;
                            ret.setResult(result);
                            return ret;
                        }
                    }
                    break;
                }
            }
        } else {
            switch (ls_q_sv_shu_cd) {
                case CommonConstants.NUM_STR_33: {
                    String kouCdPrefix = ls_q_sv_kou_cd.substring(CommonConstants.INT_0,
                            Math.min(ls_q_sv_kou_cd.length(), CommonConstants.INT_2));
                    switch (kouCdPrefix) {
                        // G3S39L123_H30介護_ケアマネ_計画複写 2018/02/27 itagaki
                        case CommonConstants.NUM_STR_11:
                        case CommonConstants.NUM_STR_17:
                        case CommonConstants.NUM_STR_18:
                        case CommonConstants.NUM_STR_19:
                        case CommonConstants.NUM_STR_21:
                        case CommonConstants.NUM_STR_22:
                        case CommonConstants.NUM_STR_23:
                        case CommonConstants.NUM_STR_24:
                        case CommonConstants.NUM_STR_90:
                        case CommonConstants.NUM_STR_25:
                        case CommonConstants.NUM_STR_26: {
                            String kouCdPrefix2 = ls_q_sv_kou_cd.substring(CommonConstants.INT_0,
                                    Math.min(ls_q_sv_kou_cd.length(), CommonConstants.INT_3));
                            if (kouCdPrefix2.equals(CommonConstants.NUM_STR_116)) { // 加算サービスの場合は終了
                                result = CommonConstants.INT_0;
                                ret.setResult(result);
                                return ret;
                            }
                            if (kouCdPrefix2.equals(CommonConstants.NUM_STR_254)) { // 療養通所介護の場合は終了
                                result = CommonConstants.INT_0;
                                ret.setResult(result);
                                return ret;
                            }
                            break;
                        }
                        default: {
                            result = CommonConstants.INT_0;
                            ret.setResult(result);
                            return ret;
                        }
                    }
                    break;
                }
                case CommonConstants.NUM_STR_35: {
                    String kouCdPrefix = ls_q_sv_kou_cd.substring(CommonConstants.INT_0,
                            Math.min(ls_q_sv_kou_cd.length(), CommonConstants.INT_2));
                    switch (kouCdPrefix) {
                        case CommonConstants.NUM_STR_11:
                        case CommonConstants.NUM_STR_17:
                        case CommonConstants.NUM_STR_18:
                        case CommonConstants.NUM_STR_20:
                        case CommonConstants.NUM_STR_21:
                        case CommonConstants.NUM_STR_90: {
                            String kouCdPrefix2 = ls_q_sv_kou_cd.substring(CommonConstants.INT_0,
                                    Math.min(ls_q_sv_kou_cd.length(), CommonConstants.INT_3));
                            switch (kouCdPrefix2) {
                                case CommonConstants.NUM_STR_172, CommonConstants.NUM_STR_182,
                                        CommonConstants.NUM_STR_214 -> {
                                    result = CommonConstants.INT_0;
                                    ret.setResult(result);
                                    return ret;
                                }
                            }
                            break;
                        }
                        default: {
                            result = CommonConstants.INT_0;
                            ret.setResult(result);
                            return ret;
                        }
                    }
                    break;
                }
            }
        }

        if (ls_m_gousei_sik_kbn != null && ls_m_gousei_sik_kbn.equals(CommonConstants.NUM_STR_3)) {
            // 外部サービスの場合のみ処理が通るように修正 2012/11/01 modify itagaki
            // サービス名より施設区分を生成する

            // 外部通所リハ、予防外部通所リハの場合はⅠ～Ⅹでは判別できないので分岐 2012/11/01 modify itagaki
            // G3S39L123_H30介護_ケアマネ_計画複写 介護医療院サービス追加 2018/03/12 itagaki
            switch (ls_m_gai_sv_syu_cd) {
                case CommonConstants.NUM_STR_16: { // 外部通所リハ

                    if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_1_1)) {
                        ls_m_shisetu_kbn = CommonConstants.STR_ROMAN_1_1;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_1_2)) {
                        ls_m_shisetu_kbn = CommonConstants.STR_ROMAN_1_2;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_2_1)) {
                        ls_m_shisetu_kbn = CommonConstants.STR_ROMAN_2_1;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_2_2)) {
                        ls_m_shisetu_kbn = CommonConstants.STR_ROMAN_2_2;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_3_1)) {
                        ls_m_shisetu_kbn = CommonConstants.STR_ROMAN_3_1;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_3_2)) {
                        ls_m_shisetu_kbn = CommonConstants.STR_ROMAN_3_2;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_1_3)) {
                        ls_m_shisetu_kbn = CommonConstants.STR_ROMAN_1_3;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_2_3)) {
                        ls_m_shisetu_kbn = CommonConstants.STR_ROMAN_2_3;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_3_3)) {
                        ls_m_shisetu_kbn = CommonConstants.STR_ROMAN_3_3;
                    } else {
                        ls_m_shisetu_kbn = CommonConstants.EMPTY_STRING;
                    }
                    break;
                }
                case CommonConstants.STR_66: { // 予防外部通所リハ
                    String kouCdPrefix2 = ls_q_sv_kou_cd.substring(CommonConstants.INT_0,
                            Math.min(ls_q_sv_kou_cd.length(), CommonConstants.INT_3));
                    if (kouCdPrefix2.equals(CommonConstants.NUM_STR_181)) {
                        ls_m_shisetu_kbn = CommonConstants.NUM_STR_181;
                    } else if (kouCdPrefix2.equals(CommonConstants.NUM_STR_183)) {
                        ls_m_shisetu_kbn = CommonConstants.NUM_STR_183;
                    } else {
                        ls_m_shisetu_kbn = CommonConstants.EMPTY_STRING;
                    }
                    break;
                }
                default: {
                    if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_1)) {
                        ls_shisetu_kbn_l = CommonConstants.STR_ROMAN_1;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_2)) {
                        ls_shisetu_kbn_l = CommonConstants.STR_ROMAN_2;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_3)) {
                        ls_shisetu_kbn_l = CommonConstants.STR_ROMAN_3;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_4)) {
                        ls_shisetu_kbn_l = CommonConstants.STR_ROMAN_4;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_5)) {
                        ls_shisetu_kbn_l = CommonConstants.STR_ROMAN_5;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_6)) {
                        ls_shisetu_kbn_l = CommonConstants.STR_ROMAN_6;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_7)) {
                        ls_shisetu_kbn_l = CommonConstants.STR_ROMAN_7;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_8)) {
                        ls_shisetu_kbn_l = CommonConstants.STR_ROMAN_8;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_9)) {
                        ls_shisetu_kbn_l = CommonConstants.STR_ROMAN_9;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_10)) {
                        ls_shisetu_kbn_l = CommonConstants.STR_ROMAN_10;
                    } else {
                        ls_shisetu_kbn_l = CommonConstants.EMPTY_STRING;
                    }

                    if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_S_1)) {
                        ls_shisetu_kbn_s = CommonConstants.STR_ROMAN_S_1;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_S_2)) {
                        ls_shisetu_kbn_s = CommonConstants.STR_ROMAN_S_2;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_S_3)) {
                        ls_shisetu_kbn_s = CommonConstants.STR_ROMAN_S_3;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_S_4)) {
                        ls_shisetu_kbn_s = CommonConstants.STR_ROMAN_S_4;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_S_5)) {
                        ls_shisetu_kbn_s = CommonConstants.STR_ROMAN_S_5;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_S_6)) {
                        ls_shisetu_kbn_s = CommonConstants.STR_ROMAN_S_6;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_S_7)) {
                        ls_shisetu_kbn_s = CommonConstants.STR_ROMAN_S_7;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_S_8)) {
                        ls_shisetu_kbn_s = CommonConstants.STR_ROMAN_S_8;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_S_9)) {
                        ls_shisetu_kbn_s = CommonConstants.STR_ROMAN_S_9;
                    } else if (ls_m_sv_name.contains(CommonConstants.STR_ROMAN_S_10)) {
                        ls_shisetu_kbn_s = CommonConstants.STR_ROMAN_S_10;
                    } else {
                        ls_shisetu_kbn_s = CommonConstants.EMPTY_STRING;
                    }

                    if (ls_m_sv_name.contains(CommonConstants.STR_TIME_REDUCTION)) {
                        li_jigen_flg = CommonConstants.INT_1;
                    }

                    ls_m_shisetu_kbn = ls_shisetu_kbn_l + ls_shisetu_kbn_s;
                    break;
                }
            }
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        // NULLチェック
        if (li_m_syoyo_time_fr == null) {
            li_m_syoyo_time_fr = CommonConstants.INT_0;
        }
        if (li_m_syoyo_time_to == null) {
            li_m_syoyo_time_to = CommonConstants.INT_0;
        }
        if (ls_m_gentai_kbn == null) {
            ls_m_gentai_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_gai_sv_syu_cd == null) {
            ls_m_gai_sv_syu_cd = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_hwr_sv_sbt == null) {
            ls_m_hwr_sv_sbt = CommonConstants.EMPTY_STRING;
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0
                    || (li_y == CommonConstants.INT_1 && li_chk_all_null == CommonConstants.INT_1)) {

                // 取得先を初期化
                ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
                ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

                // サービスコード算定
                if (ls_m_gousei_sik_kbn != null && ls_m_gousei_sik_kbn.equals(CommonConstants.NUM_STR_3)) {
                    // 外部サービスの場合と通常サービスの場合で分岐 2012/11/01 modify itagaki
                    // 外部サービス
                    if (ls_m_gai_sv_syu_cd.equals(CommonConstants.STR_66)) {
                        // 予防外部通所リハはsv_nameでの判断が数字のみではできないので分岐 2012/11/01 itagaki
                        // G3S39L123_H30介護_ケアマネ_計画複写 2018/03/12 itagaki
                        if (as_base_date.compareTo(CommonConstants.STR_20180401) >= CommonConstants.INT_0
                                && ls_m_shisetu_kbn != null && ls_m_shisetu_kbn.equals(CommonConstants.NUM_STR_183)) {
                            if ((ls_q_sv_kou_cd.compareTo(CommonConstants.STR_1831) >= CommonConstants.INT_0
                                    && ls_q_sv_kou_cd.compareTo(CommonConstants.STR_1834) <= CommonConstants.INT_0)) {
                                ComMhcKmSvKouCdInByCriteriaInEntity cond2 = new ComMhcKmSvKouCdInByCriteriaInEntity();
                                cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                                cond2.setAsBaseDate(as_base_date);
                                cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                                cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                                cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                                cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                                cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                                cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                                cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                                cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                                cond2.setLsMGaiSvSyuCd(ls_m_gai_sv_syu_cd);
                                cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                                List<ComMhcKmSvKouCdInOutEntity> out2s = comMhcKmSelectMapper
                                        .findComMhcKmSvKouCdInByCriteria(cond2);
                                if (!out2s.isEmpty()) {
                                    ComMhcKmSvKouCdInOutEntity out2 = out2s.getFirst();
                                    ls_s_sv_shu_cd = out2.getSvShuCd();
                                    ls_s_sv_kou_cd = out2.getSvKouCd();
                                }
                            } else {
                                ComMhcKmSvKouCdIn35To38ByCriteriaInEntity cond2 = new ComMhcKmSvKouCdIn35To38ByCriteriaInEntity();
                                cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                                cond2.setAsBaseDate(as_base_date);
                                cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                                cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                                cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                                cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                                cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                                cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                                cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                                cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                                cond2.setLsMGaiSvSyuCd(ls_m_gai_sv_syu_cd);
                                cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                                List<ComMhcKmSvKouCdIn35To38OutEntity> out2s = comMhcKmSelectMapper
                                        .findComMhcKmSvKouCdIn35To38ByCriteria(cond2);
                                if (!out2s.isEmpty()) {
                                    ComMhcKmSvKouCdIn35To38OutEntity out2 = out2s.getFirst();
                                    ls_s_sv_shu_cd = out2.getSvShuCd();
                                    ls_s_sv_kou_cd = out2.getSvKouCd();
                                }
                            }
                        } else {
                            ComMhcKmSvKouCdLikeByCriteriaInEntity cond2 = new ComMhcKmSvKouCdLikeByCriteriaInEntity();
                            cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                            cond2.setAsBaseDate(as_base_date);
                            cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                            cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                            cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                            cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                            cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                            cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                            cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                            cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                            cond2.setLsMGaiSvSyuCd(ls_m_gai_sv_syu_cd);
                            cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                            cond2.setLsMShisetuKbn(ls_m_shisetu_kbn);
                            List<ComMhcKmSvKouCdLikeOutEntity> out2s = comMhcKmSelectMapper
                                    .findComMhcKmSvKouCdLikeByCriteria(cond2);
                            if (!out2s.isEmpty()) {
                                ComMhcKmSvKouCdLikeOutEntity out2 = out2s.getFirst();
                                ls_s_sv_shu_cd = out2.getSvShuCd();
                                ls_s_sv_kou_cd = out2.getSvKouCd();
                            }
                        }
                    } else if (as_base_date.compareTo(CommonConstants.STR_20180401) >= 0 && ls_q_sv_shu_cd != null
                            && ls_q_sv_shu_cd.equals(CommonConstants.NUM_STR_35)
                            && (ls_q_sv_kou_cd != null && (ls_q_sv_kou_cd.equals(CommonConstants.STR_1711)
                                    || ls_q_sv_kou_cd.equals(CommonConstants.STR_1712)
                                    || ls_q_sv_kou_cd.equals(CommonConstants.STR_1713)
                                    || ls_q_sv_kou_cd.equals(CommonConstants.STR_1714)))) {
                        // H30/4以降は予防通所介護に外部サービス種類コードが入っていないため、分岐
                        ComMhcKmSvKouCd11To14ByCriteriaInEntity cond2 = new ComMhcKmSvKouCd11To14ByCriteriaInEntity();
                        cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                        cond2.setAsBaseDate(as_base_date);
                        cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                        cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                        cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                        cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                        cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                        cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                        cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                        cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                        cond2.setLsMGentaiKbn(ls_m_gentai_kbn);
                        cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                        List<ComMhcKmSvKouCd11To14OutEntity> out2s = comMhcKmSelectMapper
                                .findComMhcKmSvKouCd11To14ByCriteria(cond2);
                        if (!out2s.isEmpty()) {
                            ComMhcKmSvKouCd11To14OutEntity out2 = out2s.getFirst();
                            ls_s_sv_shu_cd = out2.getSvShuCd();
                            ls_s_sv_kou_cd = out2.getSvKouCd();
                        }
                    } else {
                        if (li_jigen_flg == CommonConstants.INT_1) {
                            // サービス名に「時減」がある場合には在る方を取得したい : 2012/03/03
                            ComMhcKmSvNameByCriteriaInEntity cond2 = new ComMhcKmSvNameByCriteriaInEntity();
                            cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                            cond2.setAsBaseDate(as_base_date);
                            cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                            cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                            cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                            cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                            cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                            cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                            cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                            cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                            cond2.setLsMGentaiKbn(ls_m_gentai_kbn);
                            cond2.setLsMGaiSvSyuCd(ls_m_gai_sv_syu_cd);
                            cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                            cond2.setLsMShisetuKbn(ls_m_shisetu_kbn);
                            List<ComMhcKmSvNameOutEntity> out2s = comMhcKmSelectMapper
                                    .findComMhcKmSvNameByCriteria(cond2);
                            if (!out2s.isEmpty()) {
                                ComMhcKmSvNameOutEntity out2 = out2s.getFirst();
                                ls_s_sv_shu_cd = out2.getSvShuCd();
                                ls_s_sv_kou_cd = out2.getSvKouCd();
                            }
                        } else {
                            // サービス名に「時減」が無い場合には無い方を取得したい : 2012/03/03
                            ComMhcKmSvNameNotLikeByCriteriaInEntity cond2 = new ComMhcKmSvNameNotLikeByCriteriaInEntity();
                            cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                            cond2.setAsBaseDate(as_base_date);
                            cond2.setLiMSyoyoTimeFr(CommonDtoUtil.objValToString(li_m_syoyo_time_fr));
                            cond2.setLiMSyoyoTimeTo(CommonDtoUtil.objValToString(li_m_syoyo_time_to));
                            cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                            cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                            cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                            cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                            cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                            cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                            cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                            cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                            cond2.setLsMGentaiKbn(ls_m_gentai_kbn);
                            cond2.setLsMGaiSvSyuCd(ls_m_gai_sv_syu_cd);
                            cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                            cond2.setLsMShisetuKbn(ls_m_shisetu_kbn);
                            List<ComMhcKmSvNameNotLikeOutEntity> out2s = comMhcKmSelectMapper
                                    .findComMhcKmSvNameNotLikeByCriteria(cond2);
                            if (!out2s.isEmpty()) {
                                ComMhcKmSvNameNotLikeOutEntity out2 = out2s.getFirst();
                                ls_s_sv_shu_cd = out2.getSvShuCd();
                                ls_s_sv_kou_cd = out2.getSvKouCd();
                            }
                        }
                    }
                } else {
                    // 通常サービス
                    ComMhcKmTsujoSabisuByCriteriaInEntity cond2 = new ComMhcKmTsujoSabisuByCriteriaInEntity();
                    cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                    cond2.setAsBaseDate(as_base_date);
                    cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                    cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                    cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                    cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                    cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                    cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                    cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                    cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                    cond2.setLsMCyukaKbn(ls_m_cyuka_kbn);
                    List<ComMhcKmTsujoSabisuOutEntity> out2s = comMhcKmSelectMapper
                            .findComMhcKmTsujoSabisuByCriteria(cond2);
                    if (!out2s.isEmpty()) {
                        ComMhcKmTsujoSabisuOutEntity out2 = out2s.getFirst();
                        ls_s_sv_shu_cd = out2.getSvShuCd();
                        ls_s_sv_kou_cd = out2.getSvKouCd();
                    }
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（外部利用型総合事業）
     *
     * @param as_scode_base    変換元サービスコード
     * @param as_base_date     基準日
     * @param as_yokai_kbn_bom 月初時要介護度
     * @param as_yokai_kbn_eom 月末時要介護度
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv35CodeSougou(String as_scode_base, String as_base_date,
            String[] as_yokai_kbn_bom, String[] as_yokai_kbn_eom) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        // String ls_m_jinin_kbn = null; // MST.人員配置区分
        Integer li_m_syoyo_time_fr = CommonConstants.INT_0; // MST.開始時間
        Integer li_m_syoyo_time_to = CommonConstants.INT_0; // MST.終了時間
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        // String ls_m_gentai_kbn = null; // MST.支給限度額管理対象区分
        String ls_m_gai_sv_syu_cd = null; // MST.外部サービス種類コード
        String ls_m_hwr_sv_sbt = null; // MST.日割計算用サービスCD識別区分
        // String ls_m_sv_name = null; // MST.サービス名
        // String ls_m_shisetu_kbn = null; // MST.施設区分(サービス名中)
        // String ls_m_gousei_sik_kbn = null; // MST.合成識別区分 // 2012/11/01 add itagaki
        // String ls_m_cyuka_kbn = null; // MST.定員超過・人員欠如識別 // 2012/11/01 add itagaki

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        // 合成識別区分、定員超過・人員欠如識別区分も取得するように修正 2012/11/01 modify itagaki
        ComMhcKmKaigoInfoByCriteriaInEntity cond = new ComMhcKmKaigoInfoByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);
        List<ComMhcKmKaigoInfoOutEntity> outs = comMhcKmSelectMapper.findComMhcKmKaigoInfoByCriteria(cond);
        if (!outs.isEmpty()) {
            ComMhcKmKaigoInfoOutEntity out = outs.getFirst();
            // ls_m_jinin_kbn = out.getJininKbn();
            li_m_syoyo_time_fr = out.getSyoyoTimeFr();
            li_m_syoyo_time_to = out.getSyoyoTimeTo();
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            // ls_m_gentai_kbn = out.getGentaiKbn();
            ls_m_gai_sv_syu_cd = out.getGaiSvSyuCd();
            ls_m_hwr_sv_sbt = out.getHwrSvSbt();
            // ls_m_sv_name = out.getSvName();
            // ls_m_gousei_sik_kbn = out.getGouseiSikKbn();
            // ls_m_cyuka_kbn = out.getCyukaKbn();
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        // 変換先が事業対象者の場合は処理しない
        if (li_chk_all_null != 1) {
            result = CommonConstants.INT_MINUS_1;
            ret.setResult(result);
            return ret;
        }

        // NULLチェック
        if (li_m_syoyo_time_fr == null) {
            li_m_syoyo_time_fr = 0;
        }
        if (li_m_syoyo_time_to == null) {
            li_m_syoyo_time_to = 0;
        }
        if (ls_m_gai_sv_syu_cd == null) {
            ls_m_gai_sv_syu_cd = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_hwr_sv_sbt == null) {
            ls_m_hwr_sv_sbt = CommonConstants.EMPTY_STRING;
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 取得先を初期化
            ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
            ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0 || li_y == CommonConstants.INT_1) {

                // 2021/3/31 takuya.jimbo G3C42F172_NEXT計画複写_外部利用通所型不正 mod str
                // 下記サービスではgai_sv_syu_cdを参照しないよう修正
                if (as_base_date.compareTo(CommonConstants.STR_20210401) >= 0
                        && ls_q_sv_shu_cd.equals(CommonConstants.NUM_STR_35)
                        && (ls_q_sv_kou_cd.equals(CommonConstants.STR_2311)
                                || ls_q_sv_kou_cd.equals(CommonConstants.STR_2312)
                                || ls_q_sv_kou_cd.equals(CommonConstants.STR_2313)
                                || ls_q_sv_kou_cd.equals(CommonConstants.STR_2314))) {
                    SvCdSanteiGaibuJigyoByCriteriaInEntity cond2 = new SvCdSanteiGaibuJigyoByCriteriaInEntity();
                    cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                    cond2.setAsBaseDate(as_base_date);
                    cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                    cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                    cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                    cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                    cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                    cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                    cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                    cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                    cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                    List<SvCdSanteiGaibuJigyoOutEntity> out2s = comMhcKmSelectMapper
                            .findSvCdSanteiGaibuJigyoByCriteria(cond2);
                    if (!out2s.isEmpty()) {
                        SvCdSanteiGaibuJigyoOutEntity out2 = out2s.getFirst();
                        ls_s_sv_shu_cd = out2.getSvShuCd();
                        ls_s_sv_kou_cd = out2.getSvKouCd();
                    }
                } else {
                    // 2021/3/31 takuya.jimbo G3C42F172_NEXT計画複写_外部利用通所型不正mod end
                    // 通常サービス
                    TsujoSvGaibuJigyoByCriteriaInEntity cond2 = new TsujoSvGaibuJigyoByCriteriaInEntity();
                    cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                    cond2.setLsMGaiSvSyuCd(ls_m_gai_sv_syu_cd);
                    cond2.setAsBaseDate(as_base_date);
                    cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                    cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                    cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                    cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                    cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                    cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                    cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                    cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                    cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                    List<TsujoSvGaibuJigyoOutEntity> out2s = comMhcKmSelectMapper
                            .findTsujoSvGaibuJigyoByCriteria(cond2);
                    if (!out2s.isEmpty()) {
                        TsujoSvGaibuJigyoOutEntity out2 = out2s.getFirst();
                        ls_s_sv_shu_cd = out2.getSvShuCd();
                        ls_s_sv_kou_cd = out2.getSvKouCd();
                    }
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（認知症対応型）
     *
     * @param as_scode_base    変換元サービスコード
     * @param as_base_date     基準日
     * @param as_yokai_kbn_bom 月初時要介護度
     * @param as_yokai_kbn_eom 月末時要介護度
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv32Code(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        String ls_m_cyuka_kbn = null; // MST.定員超過・人員欠如識別
        String ls_m_jigsis_kbn = null; // MST.事業所・施設区分
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_yakinj_ts = null; // MST.夜間勤務条件基準
        String ls_m_sin_trk = null; // MST.身体拘束廃止取組

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        // G3S39L123_H30介護_ケアマネ_計画複写 sin_trkを追加 2018/02/27 itagaki
        SvCdNinchishoByCriteriaInEntity cond = new SvCdNinchishoByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);
        List<SvCdNinchishoOutEntity> outs = comMhcKmSelectMapper.findSvCdNinchishoByCriteria(cond);
        if (!outs.isEmpty()) {
            SvCdNinchishoOutEntity out = outs.getFirst();
            ls_m_cyuka_kbn = out.getCyukaKbn();
            ls_m_jigsis_kbn = out.getJigsisKbn();
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            ls_m_yakinj_ts = out.getYakinjTs();
            ls_m_sin_trk = out.getSinTrk();
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        // NULLチェック
        if (ls_m_cyuka_kbn == null) {
            ls_m_cyuka_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_jigsis_kbn == null) {
            ls_m_jigsis_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_yakinj_ts == null) {
            ls_m_yakinj_ts = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_sin_trk == null) {
            ls_m_sin_trk = CommonConstants.EMPTY_STRING;
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0
                    || (li_y == CommonConstants.INT_1 && li_chk_all_null == CommonConstants.INT_1)) {

                // 取得先を初期化
                ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
                ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

                // サービスコード算定
                SvCdSanteiNinchishoByCriteriaInEntity cond2 = new SvCdSanteiNinchishoByCriteriaInEntity();
                cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                cond2.setLsMCyukaKbn(ls_m_cyuka_kbn);
                cond2.setLsMJigsisKbn(ls_m_jigsis_kbn);
                cond2.setAsBaseDate(as_base_date);
                cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                cond2.setLsMYakinjTs(ls_m_yakinj_ts);
                cond2.setLsMSinTrk(ls_m_sin_trk);
                List<SvCdSanteiNinchishoOutEntity> out2s = comMhcKmSelectMapper
                        .findSvCdSanteiNinchishoByCriteria(cond2);
                if (!out2s.isEmpty()) {
                    SvCdSanteiNinchishoOutEntity out2 = out2s.getFirst();
                    ls_s_sv_shu_cd = out2.getSvShuCd();
                    ls_s_sv_kou_cd = out2.getSvKouCd();
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（短期入所療養介護(医療院)）
     *
     * @param as_scode_base    変換元サービスコード
     * @param as_base_date     基準日
     * @param as_yokai_kbn_bom 月初時要介護度
     * @param as_yokai_kbn_eom 月末時要介護度
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv2ACode(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        String ls_m_cyuka_kbn = null; // MST.定員超過・人員欠如識別
        String ls_m_jigsis_kbn = null; // MST.事業所・施設区分
        String ls_m_jinin_kbn = null; // MST.人員配置区分
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_cyu_cd_17 = null; // MST.居室区分
        String ls_m_yakinj_ts = null; // MST.夜間勤務条件基準
        String ls_m_unicar_ts = null; // MST.ユニットケア体制
        String ls_m_yobi_319 = null; // MST.重度認知症疾患療養体制加算

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        SvCdTankiNyujoByCriteriaInEntity cond = new SvCdTankiNyujoByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);
        List<SvCdTankiNyujoOutEntity> outs = comMhcKmSelectMapper.findSvCdTankiNyujoByCriteria(cond);
        if (!outs.isEmpty()) {
            SvCdTankiNyujoOutEntity out = outs.getFirst();
            ls_m_cyuka_kbn = out.getCyukaKbn();
            ls_m_jigsis_kbn = out.getJigsisKbn();
            ls_m_jinin_kbn = out.getJininKbn(); // 2012/02/29 masayo
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            ls_m_cyu_cd_17 = out.getCyuCd17();
            ls_m_yakinj_ts = out.getYakinjTs();
            ls_m_unicar_ts = out.getUnicarTs();
            ls_m_yobi_319 = out.getYobi319();
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        // NULLチェック
        if (ls_m_cyuka_kbn == null) {
            ls_m_cyuka_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_jigsis_kbn == null) {
            ls_m_jigsis_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_jinin_kbn == null) {
            ls_m_jinin_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_cyu_cd_17 == null) {
            ls_m_cyu_cd_17 = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_yakinj_ts == null) {
            ls_m_yakinj_ts = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_unicar_ts == null) {
            ls_m_unicar_ts = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_yobi_319 == null) {
            ls_m_yobi_319 = CommonConstants.EMPTY_STRING;
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0
                    || (li_y == CommonConstants.INT_1 && li_chk_all_null == CommonConstants.INT_1)) {

                // 取得先を初期化
                ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
                ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

                // サービスコード算定
                SvCdSanteiTankiNyujoByCriteriaInEntity cond2 = new SvCdSanteiTankiNyujoByCriteriaInEntity();
                cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                cond2.setLsMCyukaKbn(ls_m_cyuka_kbn);
                cond2.setLsMJigsisKbn(ls_m_jigsis_kbn);
                cond2.setLsMJininKbn(ls_m_jinin_kbn);
                cond2.setAsBaseDate(as_base_date);
                cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                cond2.setLsMCyuCd17(ls_m_cyu_cd_17);
                cond2.setLsMYakinjTs(ls_m_yakinj_ts);
                cond2.setLsMUnicarTs(ls_m_unicar_ts);
                cond2.setLsMYobi319(ls_m_yobi_319);
                List<SvCdSanteiTankiNyujoOutEntity> out2s = comMhcKmSelectMapper
                        .findSvCdSanteiTankiNyujoByCriteria(cond2);
                if (!out2s.isEmpty()) {
                    SvCdSanteiTankiNyujoOutEntity out2 = out2s.getFirst();
                    ls_s_sv_shu_cd = out2.getSvShuCd();
                    ls_s_sv_kou_cd = out2.getSvKouCd();
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（短期入所療養介護(老健以外)）
     *
     * @param as_scode_base    変換元サービスコード
     * @param as_base_date     基準日
     * @param as_yokai_kbn_bom 月初時要介護度
     * @param as_yokai_kbn_eom 月末時要介護度
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv23Code(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        String ls_m_cyuka_kbn = null; // MST.定員超過・人員欠如識別
        String ls_m_jigsis_kbn = null; // MST.事業所・施設区分
        String ls_m_jinin_kbn = null; // MST.人員配置区分
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_cyu_cd_17 = null; // MST.居室区分
        String ls_m_yakinj_ts = null; // MST.夜間勤務条件基準
        String ls_m_unicar_ts = null; // MST.ユニットケア体制

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        ComMhcKmInputByCriteriaInEntity cond = new ComMhcKmInputByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);
        List<ComMhcKmInputOutEntity> outs = comMhcKmSelectMapper.findComMhcKmInputByCriteria(cond);
        if (!outs.isEmpty()) {
            ComMhcKmInputOutEntity out = outs.getFirst();
            ls_m_cyuka_kbn = out.getCyukaKbn();
            ls_m_jigsis_kbn = out.getJigsisKbn();
            ls_m_jinin_kbn = out.getJininKbn(); // 2012/02/29 masayo
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            ls_m_cyu_cd_17 = out.getCyuCd17();
            ls_m_yakinj_ts = out.getYakinjTs();
            ls_m_unicar_ts = out.getUnicarTs();
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        // NULLチェック
        if (ls_m_cyuka_kbn == null) {
            ls_m_cyuka_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_jigsis_kbn == null) {
            ls_m_jigsis_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_jinin_kbn == null) {
            ls_m_jinin_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_cyu_cd_17 == null) {
            ls_m_cyu_cd_17 = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_yakinj_ts == null) {
            ls_m_yakinj_ts = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_unicar_ts == null) {
            ls_m_unicar_ts = CommonConstants.EMPTY_STRING;
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0
                    || (li_y == CommonConstants.INT_1 && li_chk_all_null == CommonConstants.INT_1)) {

                // 取得先を初期化
                ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
                ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

                // サービスコード算定
                ComMhcKmJininKbnByCriteriaInEntity cond2 = new ComMhcKmJininKbnByCriteriaInEntity();
                cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                cond2.setLsMCyukaKbn(ls_m_cyuka_kbn);
                cond2.setLsMJigsisKbn(ls_m_jigsis_kbn);
                cond2.setAsBaseDate(as_base_date);
                cond2.setLsJininKbn(ls_m_jinin_kbn);
                cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                cond2.setLsMCyuCd17(ls_m_cyu_cd_17);
                cond2.setLsMYakinjTs(ls_m_yakinj_ts);
                cond2.setLsMUnicarTs(ls_m_unicar_ts);
                List<ComMhcKmJininKbnOutEntity> out2s = comMhcKmSelectMapper.findComMhcKmJininKbnByCriteria(cond2);
                if (!out2s.isEmpty()) {
                    ComMhcKmJininKbnOutEntity out2 = out2s.getFirst();
                    ls_s_sv_shu_cd = out2.getSvShuCd();
                    ls_s_sv_kou_cd = out2.getSvKouCd();
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（短期入所療養介護(老健)）
     *
     * @param as_scode_base    変換元サービスコード
     * @param as_base_date     基準日
     * @param as_yokai_kbn_bom 月初時要介護度
     * @param as_yokai_kbn_eom 月末時要介護度
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv22Code(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        String ls_m_cyuka_kbn = null; // MST.定員超過・人員欠如識別
        String ls_m_jigsis_kbn = null; // MST.事業所・施設区分
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_cyu_cd_17 = null; // MST.居室区分
        String ls_m_yakinj_ts = null; // MST.夜間勤務条件基準
        String ls_m_unicar_ts = null; // MST.ユニットケア体制

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;
        String ls_jinin_kbn = null;// 2012/02/29 masayo

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        ComMhcKmInputByCriteriaInEntity cond = new ComMhcKmInputByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);
        List<ComMhcKmInputOutEntity> outs = comMhcKmSelectMapper.findComMhcKmInputByCriteria(cond);
        if (!outs.isEmpty()) {
            ComMhcKmInputOutEntity out = outs.getFirst();
            ls_m_cyuka_kbn = out.getCyukaKbn();
            ls_m_jigsis_kbn = out.getJigsisKbn();
            ls_jinin_kbn = out.getJininKbn(); // 2012/02/29 masayo
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            ls_m_cyu_cd_17 = out.getCyuCd17();
            ls_m_yakinj_ts = out.getYakinjTs();
            ls_m_unicar_ts = out.getUnicarTs();
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        // NULLチェック
        if (ls_m_cyuka_kbn == null) {
            ls_m_cyuka_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_jigsis_kbn == null) {
            ls_m_jigsis_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_cyu_cd_17 == null) {
            ls_m_cyu_cd_17 = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_yakinj_ts == null) {
            ls_m_yakinj_ts = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_unicar_ts == null) {
            ls_m_unicar_ts = CommonConstants.EMPTY_STRING;
        }
        if (ls_jinin_kbn == null) {
            // G3S39L123_H30介護_ケアマネ_計画複写 2018/03/01 itagaki 抜けていたので追加
            ls_jinin_kbn = CommonConstants.EMPTY_STRING;
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0
                    || (li_y == CommonConstants.INT_1 && li_chk_all_null == CommonConstants.INT_1)) {

                // 取得先を初期化
                ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
                ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

                // サービスコード算定
                ComMhcKmJininKbnByCriteriaInEntity cond2 = new ComMhcKmJininKbnByCriteriaInEntity();
                cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                cond2.setLsMCyukaKbn(ls_m_cyuka_kbn);
                cond2.setLsMJigsisKbn(ls_m_jigsis_kbn);
                cond2.setAsBaseDate(as_base_date);
                cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                cond2.setLsMCyuCd17(ls_m_cyu_cd_17);
                cond2.setLsMYakinjTs(ls_m_yakinj_ts);
                cond2.setLsMUnicarTs(ls_m_unicar_ts);
                cond2.setLsJininKbn(ls_jinin_kbn);
                List<ComMhcKmJininKbnOutEntity> out2s = comMhcKmSelectMapper.findComMhcKmJininKbnByCriteria(cond2);
                if (!out2s.isEmpty()) {
                    ComMhcKmJininKbnOutEntity out2 = out2s.getFirst();
                    ls_s_sv_shu_cd = out2.getSvShuCd();
                    ls_s_sv_kou_cd = out2.getSvKouCd();
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（短期入所生活介護）
     *
     * @param as_scode_base    変換元サービスコード
     * @param as_base_date     基準日
     * @param as_yokai_kbn_bom 月初時要介護度
     * @param as_yokai_kbn_eom 月末時要介護度
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv21Code(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        String ls_m_cyuka_kbn = null; // MST.定員超過・人員欠如識別
        String ls_m_jigsis_kbn = null; // MST.事業所・施設区分
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_cyu_cd_17 = null; // MST.居室区分
        String ls_m_yakinj_ts = null; // MST.夜間勤務条件基準
        String ls_m_unicar_ts = null; // MST.ユニットケア体制

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;

        // 2024/5/8 koki-o G3C45L144_202404改正_サービスコンバート関数_5月版_計画複写_要介護度変換
        String ls_m_sv_kaisu = null; // MST.サービス提供内容

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        ComMhcKmInfoByCriteriaInEntity cond = new ComMhcKmInfoByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);
        List<ComMhcKmInfoOutEntity> outs = comMhcKmSelectMapper.findComMhcKmInfoByCriteria(cond);
        if (!outs.isEmpty()) {
            ComMhcKmInfoOutEntity out = outs.getFirst();
            ls_m_cyuka_kbn = out.getCyukaKbn();
            ls_m_jigsis_kbn = out.getJigsisKbn();
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            ls_m_cyu_cd_17 = out.getCyuCd17();
            ls_m_yakinj_ts = out.getYakinjTs();
            ls_m_unicar_ts = out.getUnicarTs();
            ls_m_sv_kaisu = out.getSvKaisu(); // 2024/5/8 koki-o G3C45L144_202404改正_サービスコンバート関数_5月版_計画複写_要介護度変換
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        // NULLチェック
        if (ls_m_cyuka_kbn == null) {
            ls_m_cyuka_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_jigsis_kbn == null) {
            ls_m_jigsis_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_cyu_cd_17 == null) {
            ls_m_cyu_cd_17 = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_yakinj_ts == null) {
            ls_m_yakinj_ts = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_unicar_ts == null) {
            ls_m_unicar_ts = CommonConstants.EMPTY_STRING; // 2011/11/30
        }
        if (ls_m_sv_kaisu == null) {
            ls_m_sv_kaisu = CommonConstants.EMPTY_STRING; // 2024/5/8 koki-o
            // G3C45L144_202404改正_サービスコンバート関数_5月版_計画複写_要介護度変換
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0
                    || (li_y == CommonConstants.INT_1 && li_chk_all_null == CommonConstants.INT_1)) {

                // 取得先を初期化
                ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
                ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

                // サービスコード算定
                ComMhcKmCdByCriteriaInEntity cond2 = new ComMhcKmCdByCriteriaInEntity();
                cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                cond2.setLsMCyukaKbn(ls_m_cyuka_kbn);
                cond2.setLsMJigsisKbn(ls_m_jigsis_kbn);
                cond2.setAsBaseDate(as_base_date);
                cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                cond2.setLsMCyuCd17(ls_m_cyu_cd_17);
                cond2.setLsMYakinjTs(ls_m_yakinj_ts);
                cond2.setLsMUnicarTs(ls_m_unicar_ts);
                cond2.setLsMSvKaisu(ls_m_sv_kaisu);
                List<ComMhcKmCdOutEntity> out2s = comMhcKmSelectMapper.findComMhcKmCdByCriteria(cond2);
                if (!out2s.isEmpty()) {
                    ComMhcKmCdOutEntity out2 = out2s.getFirst();
                    ls_s_sv_shu_cd = out2.getSvShuCd();
                    ls_s_sv_kou_cd = out2.getSvKouCd();
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（通所リハビリテーション）
     *
     * @param as_scode_base    変換元サービスコード
     * @param as_base_date     基準日
     * @param as_yokai_kbn_bom 月初時要介護度
     * @param as_yokai_kbn_eom 月末時要介護度
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv16Code(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        String ls_m_cyuka_kbn = null; // MST.定員超過・人員欠如識別
        String ls_m_jigsis_kbn = null; // MST.事業所・施設区分
        String ls_m_encyo_tais = null; // MST.時間延長サービス体制
        String ls_m_yusika_kbn = null; // MST.有資格者区分
        Integer li_m_syoyo_time_fr = CommonConstants.INT_0; // MST.開始時間
        Integer li_m_syoyo_time_to = CommonConstants.INT_0; // MST.終了時間
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_hwr_sv_sbt = null; // MST.日割計算用サービスCD識別区分
        String ls_m_sv_riyo_jkn = null; // MST.サービス利用条件
        String ls_m_sv_teikyo = null; // MST.サービス提供体制強化加算

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        // サービス利用条件、サービス提供体制強化加算も取得するように修正 2012/11/02 modify itagaki
        ComMhcKmKaigoCdByCriteriaInEntity cond = new ComMhcKmKaigoCdByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);
        List<ComMhcKmKaigoCdOutEntity> outs = comMhcKmSelectMapper.findComMhcKmKaigoCdByCriteria(cond);
        if (!outs.isEmpty()) {
            ComMhcKmKaigoCdOutEntity out = outs.getFirst();
            ls_m_cyuka_kbn = out.getCyukaKbn();
            ls_m_jigsis_kbn = out.getJigsisKbn();
            ls_m_encyo_tais = out.getEncyoTais();
            ls_m_yusika_kbn = out.getYusikaKbn();
            li_m_syoyo_time_fr = out.getSyoyoTimeFr();
            li_m_syoyo_time_to = out.getSyoyoTimeTo();
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            ls_m_hwr_sv_sbt = out.getHwrSvSbt();
            ls_m_sv_riyo_jkn = out.getSvRiyoJkn();
            ls_m_sv_teikyo = out.getSvTeikyo();
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        // NULLチェック
        if (ls_m_cyuka_kbn == null) {
            ls_m_cyuka_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_jigsis_kbn == null) {
            ls_m_jigsis_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_encyo_tais == null) {
            ls_m_encyo_tais = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_yusika_kbn == null) {
            ls_m_yusika_kbn = CommonConstants.EMPTY_STRING;
        }
        if (li_m_syoyo_time_fr == null) {
            li_m_syoyo_time_fr = CommonConstants.INT_0;
        }
        if (li_m_syoyo_time_to == null) {
            li_m_syoyo_time_to = CommonConstants.INT_0;
        }
        if (ls_m_hwr_sv_sbt == null) {
            ls_m_hwr_sv_sbt = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_sv_riyo_jkn == null) {
            ls_m_sv_riyo_jkn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_sv_teikyo == null) {
            ls_m_sv_teikyo = CommonConstants.EMPTY_STRING;
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0
                    || (li_y == CommonConstants.INT_1 && li_chk_all_null == CommonConstants.INT_1)) {

                // 取得先を初期化
                ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
                ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

                // 条件にサービス利用条件、サービス提供体制強化加算を追加 2012/11/02 modify itagaki
                ComMhcKmSvShuCdAndSvKouCdByCriteriaInEntity cond2 = new ComMhcKmSvShuCdAndSvKouCdByCriteriaInEntity();
                cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                cond2.setLsMCyukaKbn(ls_m_cyuka_kbn);
                cond2.setLsMJigsisKbn(ls_m_jigsis_kbn);
                cond2.setAsBaseDate(as_base_date);
                cond2.setLsMEncyoTais(ls_m_encyo_tais);
                cond2.setLsMYusikaKbn(ls_m_yusika_kbn);
                cond2.setLiMSyoyoTimeFr(CommonDtoUtil.objValToString(li_m_syoyo_time_fr));
                cond2.setLiMSyoyoTimeTo(CommonDtoUtil.objValToString(li_m_syoyo_time_to));
                cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                cond2.setLsMSvRiyoJkn(ls_m_sv_riyo_jkn);
                cond2.setLsMSvTeikyo(ls_m_sv_teikyo);
                List<ComMhcKmSvShuCdAndSvKouCdOutEntity> out2s = comMhcKmSelectMapper
                        .findComMhcKmSvShuCdAndSvKouCdByCriteria(cond2);
                if (!out2s.isEmpty()) {
                    ComMhcKmSvShuCdAndSvKouCdOutEntity out2 = out2s.getFirst();
                    ls_s_sv_shu_cd = out2.getSvShuCd();
                    ls_s_sv_kou_cd = out2.getSvKouCd();
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（通所介護）
     *
     * @param as_scode_base    変換元サービスコード
     * @param as_base_date     基準日
     * @param as_yokai_kbn_bom 月初時要介護度
     * @param as_yokai_kbn_eom 月末時要介護度
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv15Code(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        String ls_m_cyuka_kbn = null; // MST.定員超過・人員欠如識別
        String ls_m_jigsis_kbn = null; // MST.事業所・施設区分
        String ls_m_encyo_tais = null; // MST.時間延長サービス体制
        Integer li_m_syoyo_time_fr = CommonConstants.INT_0; // MST.開始時間
        Integer li_m_syoyo_time_to = CommonConstants.INT_0; // MST.終了時間
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_hwr_sv_sbt = null; // MST.日割計算用サービスCD識別区分
        String ls_m_sv_riyo_jkn = null; // MST.サービス利用条件
        String ls_m_sv_teikyo = null; // MST.サービス提供体制強化加算

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;

        // 2024/4/4 koki-o G3C45L144_202404改正_利用票_計画複写_4月版_要介護変換
        String ls_m_yobi_272 = null;
        String ls_m_yobi_314 = null;

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        // サービス利用条件、サービス提供体制強化加算も取得するように修正 2012/11/01 modify itagaki
        ComMhcKmByCriteriaInEntity cond = new ComMhcKmByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);
        List<ComMhcKmOutEntity> outs = comMhcKmSelectMapper.findComMhcKmByCriteria(cond);
        if (!outs.isEmpty()) {
            ComMhcKmOutEntity out = outs.getFirst();
            ls_m_cyuka_kbn = out.getCyukaKbn();
            ls_m_jigsis_kbn = out.getJigsisKbn();
            ls_m_encyo_tais = out.getEncyoTais();
            li_m_syoyo_time_fr = out.getSyoyoTimeFr();
            li_m_syoyo_time_to = out.getSyoyoTimeTo();
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            ls_m_hwr_sv_sbt = out.getHwrSvSbt();
            ls_m_sv_riyo_jkn = out.getSvRiyoJkn();
            ls_m_sv_teikyo = out.getSvTeikyo();
            ls_m_yobi_272 = out.getYobi272();
            ls_m_yobi_314 = out.getYobi314();
        }

        // 療養通所介護の場合は終了する
        if (CommonConstants.STR_5.equals(ls_m_jigsis_kbn)) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        // NULLチェック
        if (ls_m_cyuka_kbn == null) {
            ls_m_cyuka_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_jigsis_kbn == null) {
            ls_m_jigsis_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_encyo_tais == null) {
            ls_m_encyo_tais = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_hwr_sv_sbt == null) {
            ls_m_hwr_sv_sbt = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_sv_riyo_jkn == null) {
            ls_m_sv_riyo_jkn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_sv_teikyo == null) {
            ls_m_sv_teikyo = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_yobi_272 == null) {
            ls_m_yobi_272 = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_yobi_314 == null) {
            ls_m_yobi_314 = CommonConstants.EMPTY_STRING;
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 取得先を初期化
            ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
            ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0
                    || (li_y == CommonConstants.INT_1 && li_chk_all_null == CommonConstants.INT_1)) {
                // 条件にサービス利用条件、サービス提供体制強化加算を追加 2012/11/01 modify itagaki
                ComMhcKmSvCdByCriteriaInEntity cond2 = new ComMhcKmSvCdByCriteriaInEntity();
                cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                cond2.setLsMCyukaKbn(ls_m_cyuka_kbn);
                cond2.setLsMJigsisKbn(ls_m_jigsis_kbn);
                cond2.setAsBaseDate(as_base_date);
                cond2.setLsMEncyoTais(ls_m_encyo_tais);
                cond2.setLiMSyoyoTimeFr(CommonDtoUtil.objValToString(li_m_syoyo_time_fr));
                cond2.setLiMSyoyoTimeTo(CommonDtoUtil.objValToString(li_m_syoyo_time_to));
                cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                cond2.setLsMSvRiyoJkn(ls_m_sv_riyo_jkn);
                cond2.setLsMSvTeikyo(ls_m_sv_teikyo);
                cond2.setLsMYobi272(ls_m_yobi_272);
                cond2.setLsMYobi314(ls_m_yobi_314);
                List<ComMhcKmSvCdOutEntity> out2s = comMhcKmSelectMapper.findComMhcKmSvCdByCriteria(cond2);
                if (!out2s.isEmpty()) {
                    ComMhcKmSvCdOutEntity out2 = out2s.getFirst();
                    ls_s_sv_shu_cd = out2.getSvShuCd();
                    ls_s_sv_kou_cd = out2.getSvKouCd();
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（訪問看護）
     *
     * @param as_scode_base    変換元サービスコード
     * @param as_base_date     基準日
     * @param as_yokai_kbn_bom 月初時要介護度
     * @param as_yokai_kbn_eom 月末時要介護度
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv13Code(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_jigsis_kbn = null; // MST.事業所・施設区分
        String ls_m_yusika_kbn = null; // MST.有資格者区分
        String ls_m_hwr_sv_sbt = null; // MST.日割計算用サービスCD識別区分
        // String ls_m_scode_term = null; // MST.サービスコード末尾1桁

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null; // サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得
        JigsisKbnAndKaigoByCriteriaInEntity cond = new JigsisKbnAndKaigoByCriteriaInEntity();
        cond.setLsQSvShuCd(ls_q_sv_shu_cd);
        cond.setLsQSvKouCd(ls_q_sv_kou_cd);
        cond.setAsBaseDate(as_base_date);
        List<JigsisKbnAndKaigoOutEntity> outs = comMhcKmSelectMapper.findJigsisKbnAndKaigoByCriteria(cond);
        if (!outs.isEmpty()) {
            JigsisKbnAndKaigoOutEntity out = outs.getFirst();
            ls_m_jigsis_kbn = out.getJigsisKbn();
            ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
            ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
            ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
            ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
            ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
            ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
            ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
            ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
            ls_m_yusika_kbn = out.getYusikaKbn();
            ls_m_hwr_sv_sbt = out.getHwrSvSbt();
            // ls_m_scode_term = out.getScodeTerm();
        }

        // 施設区分が定期巡回・随時対応サービス以外の場合はパスする
        if (!CommonConstants.STR_3.equals(ls_m_jigsis_kbn)) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        //// 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(
                as_scode_base.substring(CommonConstants.INT_0, Math.min(as_scode_base.length(), CommonConstants.INT_2)),
                ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(
                as_scode_base.substring(CommonConstants.INT_0, Math.min(as_scode_base.length(), CommonConstants.INT_2)),
                as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        if (!CommonConstants.NUM_STR_1.equals(ls_rank[CommonConstants.INT_5][CommonConstants.INT_0])) {
            // 要介護５以外の場合
            ls_rank[CommonConstants.INT_0][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
            ls_rank[CommonConstants.INT_1][CommonConstants.INT_0] = CommonConstants.NUM_STR_1;
            ls_rank[CommonConstants.INT_2][CommonConstants.INT_0] = CommonConstants.NUM_STR_1;
            ls_rank[CommonConstants.INT_3][CommonConstants.INT_0] = CommonConstants.NUM_STR_1;
            ls_rank[CommonConstants.INT_4][CommonConstants.INT_0] = CommonConstants.NUM_STR_1;
            ls_rank[CommonConstants.INT_5][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
            ls_rank[CommonConstants.INT_6][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
            ls_rank[CommonConstants.INT_7][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
        }

        if (!CommonConstants.NUM_STR_1.equals(ls_rank[CommonConstants.INT_5][CommonConstants.INT_1])) {
            // 要介護５以外の場合
            ls_rank[CommonConstants.INT_0][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
            ls_rank[CommonConstants.INT_1][CommonConstants.INT_1] = CommonConstants.NUM_STR_1;
            ls_rank[CommonConstants.INT_2][CommonConstants.INT_1] = CommonConstants.NUM_STR_1;
            ls_rank[CommonConstants.INT_3][CommonConstants.INT_1] = CommonConstants.NUM_STR_1;
            ls_rank[CommonConstants.INT_4][CommonConstants.INT_1] = CommonConstants.NUM_STR_1;
            ls_rank[CommonConstants.INT_5][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
            ls_rank[CommonConstants.INT_6][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
            ls_rank[CommonConstants.INT_7][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
        }

        // NULLチェック
        if (ls_m_jigsis_kbn == null) {
            ls_m_jigsis_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_yusika_kbn == null) {
            ls_m_yusika_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_hwr_sv_sbt == null) {
            ls_m_hwr_sv_sbt = CommonConstants.EMPTY_STRING;
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0
                    || (li_y == CommonConstants.INT_1 && li_chk_all_null == CommonConstants.INT_1)) {
                // 取得先を初期化
                ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
                ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

                // サービスコード算定
                SvCdSanteiKangoByCriteriaInEntity cond2 = new SvCdSanteiKangoByCriteriaInEntity();
                cond2.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                cond2.setLsMJigsisKbn(ls_m_jigsis_kbn);
                cond2.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                cond2.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                cond2.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                cond2.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                cond2.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                cond2.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                cond2.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                cond2.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                cond2.setLsMYusikaKbn(ls_m_yusika_kbn);
                cond2.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                List<SvCdSanteiKangoOutEntity> out2s = comMhcKmSelectMapper.findSvCdSanteiKangoByCriteria(cond2);
                if (!out2s.isEmpty()) {
                    SvCdSanteiKangoOutEntity out2 = out2s.getFirst();
                    ls_s_sv_shu_cd = out2.getSvShuCd();
                    ls_s_sv_kou_cd = out2.getSvKouCd();
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * サービスコード変換（虐防、業未の単独減算専用）
     *
     * @param as_scode_base     変換元サービスコード
     * @param as_base_date      基準日
     * @param as_yokai_kbn_bom  月初時要介護度
     * @param as_yokai_kbn_eom  月末時要介護度
     * @param ii_yok_term_to    変換後有効期間ID
     * @param il_yok_k_hoken_cd 保険者コード
     * @param is_yok_ymd_to     変換先基準日
     * @return 関数_利用票サービスコード変換（メイン）結果
     */
    private ConvertScodeRankResult ufCnv2024Code(String as_scode_base, String as_base_date, String[] as_yokai_kbn_bom,
            String[] as_yokai_kbn_eom, Integer ii_yok_term_to, String il_yok_k_hoken_cd, String is_yok_ymd_to) {
        ConvertScodeRankResult ret = new ConvertScodeRankResult();

        Integer result = null; // 運行結果
        String as_scode_bom = null; // 月初時サービスコード
        String as_scode_eom = null; // 月末時サービスコード

        String ls_m_jigsis_kbn = null; // MST.事業所・施設区分

        Integer li_m_syoyo_time_fr = CommonConstants.INT_0; // MST.開始時間
        Integer li_m_syoyo_time_to = CommonConstants.INT_0; // MST.終了時間
        String[] ls_m_rank = new String[CommonConstants.INT_8]; // MST.要介護度
        String ls_m_hwr_sv_sbt = null; // MST.日割計算用サービスCD識別区分
        String ls_m_sv_riyo_jkn = null; // MST.サービス利用条件
        String ls_m_sv_teikyo = null; // MST.サービス提供体制強化加算

        String ls_q_sv_shu_cd = null; // サービス種類コード(検索用)
        String ls_q_sv_kou_cd = null;// サービス項目コード(検索用)
        String ls_s_sv_shu_cd = null; // サービス種類コード(設定用)
        String ls_s_sv_kou_cd = null; // サービス項目コード(設定用)

        String[] ls_v_sv_shu_cd = new String[CommonConstants.INT_2]; // MST参照用サービス種類コード
        String[][] ls_rank = new String[CommonConstants.INT_8][CommonConstants.INT_2]; // MST参照用要介護度

        int li_x, li_y; // 指標
        int li_chk_all_null = CommonConstants.INT_0; // False : ALL NULL , True : NOT ALL NULL
        StringRef ls_w_sv_shu_cd_ref = new StringRef();
        String ls_yokaigodo = null;

        String ls_m_cyu_cd_17 = null;
        String ls_m_jinin_kbn = null;
        String ls_m_sv_kaisu = null;
        String ls_m_santei_tani = null;
        String ls_m_gousei_sik_kbn = null;
        String ls_m_yobi_272 = null;
        String ls_m_yobi_314 = null;
        int ll_count;

        String ls_m_sin_trk = null;

        ls_q_sv_shu_cd = as_scode_base.substring(CommonConstants.INT_0,
                Math.min(as_scode_base.length(), CommonConstants.INT_2));
        ls_q_sv_kou_cd = as_scode_base
                .substring(as_scode_base.length() - Math.min(as_scode_base.length(), CommonConstants.INT_4));

        // 変換元サービスコードにて厚労省介護マスタの要介護度及び絞込みのための要素を取得

        if (kghCmnF01Logic.chkSougouSvtype(ls_q_sv_shu_cd)) {
            // 総合事業の場合
            SvCdTandokuSenyoByCriteriaInEntity cond = new SvCdTandokuSenyoByCriteriaInEntity();
            cond.setLsQSvShuCd(ls_q_sv_shu_cd);
            cond.setLsQSvKouCd(ls_q_sv_kou_cd);
            cond.setAsBaseDate(as_base_date);
            List<SvCdTandokuSenyoOutEntity> outs = comMhcSmSelectMapper.findSvCdTandokuSenyoByCriteria(cond);
            if (!outs.isEmpty()) {
                SvCdTandokuSenyoOutEntity out = outs.getFirst();
                ls_m_jigsis_kbn = out.getJigsisKbn();
                li_m_syoyo_time_fr = out.getSyoyoTimeFr();
                li_m_syoyo_time_to = out.getSyoyoTimeTo();
                ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
                ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
                ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
                ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
                ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
                ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
                ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
                ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
                ls_m_hwr_sv_sbt = out.getHwrSvSbt();
                ls_m_sv_riyo_jkn = out.getSvRiyoJkn();
                ls_m_sv_teikyo = out.getSvTeikyo();
                ls_m_cyu_cd_17 = out.getCyuCd17();
                ls_m_jinin_kbn = out.getJininKbn();
                ls_m_sv_kaisu = out.getSvKaisu();
                ls_m_santei_tani = out.getSanteiTani();
                ls_m_gousei_sik_kbn = out.getGouseiSikKbn();
                ls_m_yobi_272 = out.getYobi272();
                ls_m_yobi_314 = out.getYobi314();
            }
        } else {
            SvCdTandokuSenyoFutsuByCriteriaInEntity cond = new SvCdTandokuSenyoFutsuByCriteriaInEntity();
            cond.setLsQSvShuCd(ls_q_sv_shu_cd);
            cond.setLsQSvKouCd(ls_q_sv_kou_cd);
            cond.setAsBaseDate(as_base_date);
            List<SvCdTandokuSenyoFutsuOutEntity> outs = comMhcKmSelectMapper.findSvCdTandokuSenyoFutsuByCriteria(cond);
            if (!outs.isEmpty()) {
                SvCdTandokuSenyoFutsuOutEntity out = outs.getFirst();
                ls_m_jigsis_kbn = out.getJigsisKbn();
                li_m_syoyo_time_fr = out.getSyoyoTimeFr();
                li_m_syoyo_time_to = out.getSyoyoTimeTo();
                ls_m_rank[CommonConstants.INT_0] = out.getKaigoSien();
                ls_m_rank[CommonConstants.INT_1] = out.getKaigo1();
                ls_m_rank[CommonConstants.INT_2] = out.getKaigo2();
                ls_m_rank[CommonConstants.INT_3] = out.getKaigo3();
                ls_m_rank[CommonConstants.INT_4] = out.getKaigo4();
                ls_m_rank[CommonConstants.INT_5] = out.getKaigo5();
                ls_m_rank[CommonConstants.INT_6] = out.getKaigoSien1();
                ls_m_rank[CommonConstants.INT_7] = out.getKaigoSien2();
                ls_m_hwr_sv_sbt = out.getHwrSvSbt();
                ls_m_sv_riyo_jkn = out.getSvRiyoJkn();
                ls_m_sv_teikyo = out.getSvTeikyo();
                ls_m_cyu_cd_17 = out.getCyuCd17();
                ls_m_jinin_kbn = out.getJininKbn();
                ls_m_sv_kaisu = out.getSvKaisu();
                ls_m_santei_tani = out.getSanteiTani();
                ls_m_gousei_sik_kbn = out.getGouseiSikKbn();
                ls_m_yobi_272 = out.getYobi272();
                ls_m_yobi_314 = out.getYobi314();
                ls_m_sin_trk = out.getSinTrk();
            }
        }

        // 要介護度と関係ないサービスコードの場合はパスする
        if (ls_m_rank[CommonConstants.INT_0] == null && ls_m_rank[CommonConstants.INT_1] == null
                && ls_m_rank[CommonConstants.INT_2] == null && ls_m_rank[CommonConstants.INT_3] == null
                && ls_m_rank[CommonConstants.INT_4] == null && ls_m_rank[CommonConstants.INT_5] == null
                && ls_m_rank[CommonConstants.INT_6] == null && ls_m_rank[CommonConstants.INT_7] == null) {
            result = CommonConstants.INT_0;
            ret.setResult(result);
            return ret;
        }

        // 介護と予防をまたがる要介護度の変更があるかをチェックし、ある場合はサービス種類コードを変換する
        ufChkCnvSvShuCd(as_scode_base, ls_m_rank, as_yokai_kbn_bom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_0] = ls_w_sv_shu_cd_ref.getValue();
        ufChkCnvSvShuCd(as_scode_base, as_yokai_kbn_bom, as_yokai_kbn_eom, ls_w_sv_shu_cd_ref);
        ls_v_sv_shu_cd[CommonConstants.INT_1] = ls_w_sv_shu_cd_ref.getValue();

        // 月初時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_bom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_0] = as_yokai_kbn_bom[li_x];
        }

        // 月末時要介護度を二次元配列に格納
        for (li_x = CommonConstants.INT_0; li_x < as_yokai_kbn_eom.length; li_x++) {
            ls_rank[li_x][CommonConstants.INT_1] = as_yokai_kbn_eom[li_x];
            if (li_chk_all_null == CommonConstants.INT_0) {
                if (as_yokai_kbn_eom[li_x] == null) {
                    ls_yokaigodo = CommonConstants.EMPTY_STRING;
                } else {
                    ls_yokaigodo = as_yokai_kbn_eom[li_x];
                }
                if (CommonConstants.STR_1.equals(ls_yokaigodo)) {
                    li_chk_all_null = CommonConstants.INT_1; // ALL NULL ではない（ここを1回も通過しなければ ALL NULL）
                }
            }
        }

        // 変換先が事業対象者の場合は処理しない
        if (li_chk_all_null != CommonConstants.INT_1) {
            result = CommonConstants.INT_MINUS_1;
            ret.setResult(result);
            return ret;
        }

        // 2024/5/7 koki-o G3C45L144_202404改正_サービスコンバート関数_5月版_計画複写_要介護度変換 add start
        if (CommonConstants.NUM_STR_13.equals(ls_q_sv_shu_cd)) {

            // uf_cnv_13_codeの処理から流用
            // 施設区分が定期巡回・随時対応サービス以外の場合はパスする
            if (!CommonConstants.NUM_STR_3.equals(ls_m_jigsis_kbn)) {
                result = CommonConstants.INT_0;
                ret.setResult(result);
                return ret;
            }

            if (!CommonConstants.NUM_STR_1.equals(ls_rank[CommonConstants.INT_5][CommonConstants.INT_0])) {
                // 要介護５以外の場合
                ls_rank[CommonConstants.INT_0][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_1][CommonConstants.INT_0] = CommonConstants.NUM_STR_1;
                ls_rank[CommonConstants.INT_2][CommonConstants.INT_0] = CommonConstants.NUM_STR_1;
                ls_rank[CommonConstants.INT_3][CommonConstants.INT_0] = CommonConstants.NUM_STR_1;
                ls_rank[CommonConstants.INT_4][CommonConstants.INT_0] = CommonConstants.NUM_STR_1;
                ls_rank[CommonConstants.INT_5][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_6][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_7][CommonConstants.INT_0] = CommonConstants.EMPTY_STRING;
            }

            if (!CommonConstants.NUM_STR_1.equals(ls_rank[CommonConstants.INT_5][CommonConstants.INT_1])) {
                // 要介護５以外の場合
                ls_rank[CommonConstants.INT_0][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_1][CommonConstants.INT_1] = CommonConstants.NUM_STR_1;
                ls_rank[CommonConstants.INT_2][CommonConstants.INT_1] = CommonConstants.NUM_STR_1;
                ls_rank[CommonConstants.INT_3][CommonConstants.INT_1] = CommonConstants.NUM_STR_1;
                ls_rank[CommonConstants.INT_4][CommonConstants.INT_1] = CommonConstants.NUM_STR_1;
                ls_rank[CommonConstants.INT_5][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_6][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
                ls_rank[CommonConstants.INT_7][CommonConstants.INT_1] = CommonConstants.EMPTY_STRING;
            }

        }
        // 2024/5/7 koki-o G3C45L144_202404改正_サービスコンバート関数_5月版_計画複写_要介護度変換 add end

        // NULLチェック
        if (ls_m_jigsis_kbn == null) {
            ls_m_jigsis_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_hwr_sv_sbt == null) {
            ls_m_hwr_sv_sbt = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_sv_riyo_jkn == null) {
            ls_m_sv_riyo_jkn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_sv_teikyo == null) {
            ls_m_sv_teikyo = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_cyu_cd_17 == null) {
            ls_m_cyu_cd_17 = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_jinin_kbn == null) {
            ls_m_jinin_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_sv_kaisu == null) {
            ls_m_sv_kaisu = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_santei_tani == null) {
            ls_m_santei_tani = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_gousei_sik_kbn == null) {
            ls_m_gousei_sik_kbn = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_yobi_272 == null) {
            ls_m_yobi_272 = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_yobi_314 == null) {
            ls_m_yobi_314 = CommonConstants.EMPTY_STRING;
        }
        if (ls_m_sin_trk == null) {
            ls_m_sin_trk = CommonConstants.EMPTY_STRING;
        }

        // 月初時及び月末時の2回分処理する
        for (li_y = CommonConstants.INT_0; li_y < ls_v_sv_shu_cd.length; li_y++) {

            // 取得先を初期化
            ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
            ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;

            // 月初時及び、月末時且つ要介護変更がある場合のみ実行
            if (li_y == CommonConstants.INT_0 || li_y == CommonConstants.INT_1) {

                if (kghCmnF01Logic.chkSougouSvtype(ls_q_sv_shu_cd)) {
                    // 総合事業の場合
                    SvCdSanteiTandokuSenyoByCriteriaInEntity cond = new SvCdSanteiTandokuSenyoByCriteriaInEntity();
                    cond.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                    cond.setLsMJigsisKbn(ls_m_jigsis_kbn);
                    cond.setAsBaseDate(as_base_date);
                    cond.setLiMSyoyoTimeFr(CommonDtoUtil.objValToString(li_m_syoyo_time_fr));
                    cond.setLiMSyoyoTimeTo(CommonDtoUtil.objValToString(li_m_syoyo_time_to));
                    cond.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                    cond.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                    cond.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                    cond.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                    cond.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                    cond.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                    cond.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                    cond.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                    cond.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                    cond.setLsMSvRiyoJkn(ls_m_sv_riyo_jkn);
                    cond.setLsMSvTeikyo(ls_m_sv_teikyo);
                    cond.setLsMCyuCd17(ls_m_cyu_cd_17);
                    cond.setLsMJininKbn(ls_m_jinin_kbn);
                    cond.setLsMSvKaisu(ls_m_sv_kaisu);
                    cond.setLsMSanteiTani(ls_m_santei_tani);
                    cond.setLsMGouseiSikKbn(ls_m_gousei_sik_kbn);
                    cond.setLsMYobi272(ls_m_yobi_272);
                    cond.setLsMYobi314(ls_m_yobi_314);
                    List<SvCdSanteiTandokuSenyoOutEntity> outs = comMhcSmSelectMapper
                            .findSvCdSanteiTandokuSenyoByCriteria(cond);
                    if (!outs.isEmpty()) {
                        SvCdSanteiTandokuSenyoOutEntity out = outs.getFirst();
                        ls_s_sv_shu_cd = out.getSvShuCd();
                        ls_s_sv_kou_cd = out.getSvKouCd();
                    }

                    // A6の場合、com_mhc_itemuse_sougouにレコードがあるか確認する
                    if (CommonConstants.STR_A_SIX.equals(ls_s_sv_shu_cd)) {
                        ComMhcItemuseSougouByCriteriaInEntity cond2 = new ComMhcItemuseSougouByCriteriaInEntity();
                        cond2.setTermid(ii_yok_term_to);
                        cond2.setKHokenCd(il_yok_k_hoken_cd);
                        cond2.setSvtype(ls_s_sv_shu_cd);
                        cond2.setSvcode(ls_s_sv_kou_cd);
                        cond2.setYmd(is_yok_ymd_to);
                        ComMhcItemuseSougouOutEntity out = comMhcItemuseSougouSelectMapper
                                .countComMhcItemuseSougouByCriteria(cond2);
                        ll_count = CommonConstants.INT_0;
                        if (out != null && out.getCnt() != null) {
                            ll_count = out.getCnt();
                        }
                        if (ll_count <= CommonConstants.INT_0) {
                            ls_s_sv_shu_cd = CommonConstants.EMPTY_STRING;
                            ls_s_sv_kou_cd = CommonConstants.EMPTY_STRING;
                        }
                    }
                } else {
                    SvCdSanteiTandokuSenyoFutsuByCriteriaInEntity cond = new SvCdSanteiTandokuSenyoFutsuByCriteriaInEntity();
                    cond.setLsVSvShuCd(ls_v_sv_shu_cd[li_y]);
                    cond.setLsMJigsisKbn(ls_m_jigsis_kbn);
                    cond.setAsBaseDate(as_base_date);
                    cond.setLiMSyoyoTimeFr(CommonDtoUtil.objValToString(li_m_syoyo_time_fr));
                    cond.setLiMSyoyoTimeTo(CommonDtoUtil.objValToString(li_m_syoyo_time_to));
                    cond.setLsRank1(ls_rank[CommonConstants.INT_0][li_y]);
                    cond.setLsRank2(ls_rank[CommonConstants.INT_1][li_y]);
                    cond.setLsRank3(ls_rank[CommonConstants.INT_2][li_y]);
                    cond.setLsRank4(ls_rank[CommonConstants.INT_3][li_y]);
                    cond.setLsRank5(ls_rank[CommonConstants.INT_4][li_y]);
                    cond.setLsRank6(ls_rank[CommonConstants.INT_5][li_y]);
                    cond.setLsRank7(ls_rank[CommonConstants.INT_6][li_y]);
                    cond.setLsRank8(ls_rank[CommonConstants.INT_7][li_y]);
                    cond.setLsMHwrSvSbt(ls_m_hwr_sv_sbt);
                    cond.setLsMSvRiyoJkn(ls_m_sv_riyo_jkn);
                    cond.setLsMSvTeikyo(ls_m_sv_teikyo);
                    cond.setLsMCyuCd17(ls_m_cyu_cd_17);
                    cond.setLsMJininKbn(ls_m_jinin_kbn);
                    cond.setLsMSvKaisu(ls_m_sv_kaisu);
                    cond.setLsMSanteiTani(ls_m_santei_tani);
                    cond.setLsMGouseiSikKbn(ls_m_gousei_sik_kbn);
                    cond.setLsMYobi272(ls_m_yobi_272);
                    cond.setLsMYobi314(ls_m_yobi_314);
                    cond.setLsMSinTrk(ls_m_sin_trk);

                    List<SvCdSanteiTandokuSenyoFutsuOutEntity> outs = comMhcKmSelectMapper
                            .findSvCdSanteiTandokuSenyoFutsuByCriteria(cond);
                    if (!outs.isEmpty()) {
                        SvCdSanteiTandokuSenyoFutsuOutEntity out = outs.getFirst();
                        ls_s_sv_shu_cd = out.getSvShuCd();
                        ls_s_sv_kou_cd = out.getSvKouCd();
                    }
                }

                switch (li_y) {
                    case CommonConstants.INT_0:
                        as_scode_bom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                    case CommonConstants.INT_1:
                        as_scode_eom = ls_s_sv_shu_cd + ls_s_sv_kou_cd;
                        break;
                }
            }
        }

        result = CommonConstants.INT_1;
        ret.setResult(result);
        ret.setAsScodeBom(as_scode_bom);
        ret.setAsScodeEom(as_scode_eom);
        return ret;
    }

    /**
     * 介護と予防をまたがる場合のサービス種類コード変換
     *
     * @param as_base_sv_shu_cd 変換元サービス種類コード
     * @param as_rank_1         要介護度①
     * @param as_rank_2         要介護度②
     * @param as_dist_sv_shu_cd 変換後サービス種類コード
     * @return 戻り値 0: 正常, -1: 異常
     */
    private int ufChkCnvSvShuCd(String as_base_sv_shu_cd, String[] as_rank_1, String[] as_rank_2,
            StringRef as_dist_sv_shu_cd) {

        int li_cnt;
        int li_kaigo_1 = 0, li_sien_1 = 0;
        int li_kaigo_2 = 0, li_sien_2 = 0;

        // NULLをブランクにする
        for (li_cnt = CommonConstants.INT_0; li_cnt < as_rank_1.length; li_cnt++) {
            if (as_rank_1[li_cnt] == null) {
                as_rank_1[li_cnt] = CommonConstants.EMPTY_STRING;
            }
        }

        for (li_cnt = CommonConstants.INT_0; li_cnt < as_rank_2.length; li_cnt++) {
            if (as_rank_2[li_cnt] == null) {
                as_rank_2[li_cnt] = CommonConstants.EMPTY_STRING;
            }
        }

        // チェック
        if (CommonConstants.STR_1.equals(as_rank_1[CommonConstants.INT_0])
                || CommonConstants.STR_1.equals(as_rank_1[CommonConstants.INT_1])
                || CommonConstants.STR_1.equals(as_rank_1[CommonConstants.INT_2])
                || CommonConstants.STR_1.equals(as_rank_1[CommonConstants.INT_3])
                || CommonConstants.STR_1.equals(as_rank_1[CommonConstants.INT_4])
                || CommonConstants.STR_1.equals(as_rank_1[CommonConstants.INT_5])) {
            li_kaigo_1 = CommonConstants.INT_1;
        } else if (CommonConstants.STR_1.equals(as_rank_1[CommonConstants.INT_6])
                || CommonConstants.STR_1.equals(as_rank_1[CommonConstants.INT_7])) {
            li_sien_1 = CommonConstants.INT_1;
        }
        if (CommonConstants.STR_1.equals(as_rank_2[CommonConstants.INT_0])
                || CommonConstants.STR_1.equals(as_rank_2[CommonConstants.INT_1])
                || CommonConstants.STR_1.equals(as_rank_2[CommonConstants.INT_2])
                || CommonConstants.STR_1.equals(as_rank_2[CommonConstants.INT_3])
                || CommonConstants.STR_1.equals(as_rank_2[CommonConstants.INT_4])
                || CommonConstants.STR_1.equals(as_rank_2[CommonConstants.INT_5])) {
            li_kaigo_2 = CommonConstants.INT_1;
        } else if (CommonConstants.STR_1.equals(as_rank_2[CommonConstants.INT_6])
                || CommonConstants.STR_1.equals(as_rank_2[CommonConstants.INT_7])) {
            li_sien_2 = CommonConstants.INT_1;
        }

        // 変換
        if ((li_kaigo_1 == CommonConstants.INT_1 && li_sien_2 == CommonConstants.INT_1)
                || (li_kaigo_2 == CommonConstants.INT_1 && li_sien_1 == CommonConstants.INT_1)) {
            switch (as_base_sv_shu_cd) {
                case CommonConstants.NUM_STR_15:
                    as_dist_sv_shu_cd.setValue(CommonConstants.STR_65);
                    break;
                case CommonConstants.STR_65:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_15);
                    break;
                case CommonConstants.SV_CD_STR_72:
                    as_dist_sv_shu_cd.setValue(CommonConstants.SV_CD_STR_74);
                    break;
                case CommonConstants.SV_CD_STR_74:
                    as_dist_sv_shu_cd.setValue(CommonConstants.SV_CD_STR_72);
                    break;
                case CommonConstants.NUM_STR_16:
                    as_dist_sv_shu_cd.setValue(CommonConstants.STR_66);
                    break;
                case CommonConstants.STR_66:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_16);
                    break;
                case CommonConstants.NUM_STR_21:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_24);
                    break;
                case CommonConstants.NUM_STR_24:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_21);
                    break;
                case CommonConstants.NUM_STR_22:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_25);
                    break;
                case CommonConstants.NUM_STR_25:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_22);
                    break;
                case CommonConstants.NUM_STR_23:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_26);
                    break;
                case CommonConstants.NUM_STR_26:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_23);
                    break;
                case CommonConstants.NUM_STR_33:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_35);
                    break;
                case CommonConstants.NUM_STR_35:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_33);
                    break;
                case CommonConstants.SV_CD_STR_73:
                    as_dist_sv_shu_cd.setValue(CommonConstants.SV_CD_STR_75);
                    break;
                case CommonConstants.SV_CD_STR_75:
                    as_dist_sv_shu_cd.setValue(CommonConstants.SV_CD_STR_73);
                    break;
                case CommonConstants.SV_TYPE_76:
                    as_dist_sv_shu_cd.setValue(CommonConstants.SV_TYPE_76);
                    break;
                case CommonConstants.SV_TYPE_77:
                    as_dist_sv_shu_cd.setValue(CommonConstants.SV_TYPE_77);
                    break;
                case CommonConstants.SV_CD_STR_78:
                    as_dist_sv_shu_cd.setValue(CommonConstants.SV_CD_STR_78);
                    // 2016/01/20 <EMAIL> H2803改正対応 ADD
                    // 2015/2/25 koki-o H27改正対応==========
                case CommonConstants.NUM_STR_79:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_79);
                    break;
                case CommonConstants.NUM_STR_68:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_69);
                    break;
                case CommonConstants.NUM_STR_69:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_68);
                    break;
                // ======================================
                case CommonConstants.NUM_STR_27:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_27);
                    break;
                case CommonConstants.NUM_STR_28:
                    as_dist_sv_shu_cd.setValue(CommonConstants.NUM_STR_28);
                    break;
                // G3S39L123_H30介護_ケアマネ_計画複写 2A、2Bを追加
                case CommonConstants.SV_TYPE_2A:
                    as_dist_sv_shu_cd.setValue(CommonConstants.SV_TYPE_2B);
                    break;
                // 2018/9/13 koki-o G3C40R067_ケアマネ_サービスコンバート_総合事業要介護度
                case CommonConstants.STR_A_FIVE:
                    as_dist_sv_shu_cd.setValue(CommonConstants.STR_A_FIVE);
                    break;
                case CommonConstants.STR_A_SIX:
                    as_dist_sv_shu_cd.setValue(CommonConstants.STR_A_SIX);
                    break;
                case CommonConstants.EMPTY_STRING:
                    break;
                default:
                    as_dist_sv_shu_cd.setValue(CommonConstants.EMPTY_STRING);
                    return CommonConstants.INT_MINUS_1;
            }

        } else {
            as_dist_sv_shu_cd.setValue(as_base_sv_shu_cd.substring(CommonConstants.INT_0,
                    Math.min(as_base_sv_shu_cd.length(), CommonConstants.INT_2)));
        }

        return CommonConstants.INT_0;
    }

    /**
     * 要介護度コード変換（ND→厚労省）
     *
     * @param ai_yokai_kbn_bom ND要介護度コード
     * @param as_rank          厚労省マスタ参照用要介護度配列
     */
    private void ufCnvYokaiKbn(Integer ai_yokai_kbn_bom, String[] as_rank) {
        // as_rank[1] 経過的要介護
        // as_rank[2] 要介護１
        // as_rank[3] 要介護２
        // as_rank[4] 要介護３
        // as_rank[5] 要介護４
        // as_rank[6] 要介護５
        // as_rank[7] 要支援１
        // as_rank[8] 要支援２

        // 厚労省コードの初期化
        Arrays.fill(as_rank, CommonConstants.EMPTY_STRING);

        // コード変換
        switch (ai_yokai_kbn_bom) {
            case CommonConstants.INT_2: // 経過的要介護
                as_rank[CommonConstants.INT_0] = CommonConstants.STR_1;
                break;
            case CommonConstants.INT_3: // 要介護１
                as_rank[CommonConstants.INT_1] = CommonConstants.STR_1;
                break;
            case CommonConstants.INT_4: // 要介護２
                as_rank[CommonConstants.INT_2] = CommonConstants.STR_1;
                break;
            case CommonConstants.INT_5: // 要介護３
                as_rank[CommonConstants.INT_3] = CommonConstants.STR_1;
                break;
            case CommonConstants.INT_6: // 要介護４
                as_rank[CommonConstants.INT_4] = CommonConstants.STR_1;
                break;
            case CommonConstants.INT_7: // 要介護５
                as_rank[CommonConstants.INT_5] = CommonConstants.STR_1;
                break;
            case CommonConstants.INT_11: // 要支援１
                as_rank[CommonConstants.INT_6] = CommonConstants.STR_1;
                break;
            case CommonConstants.INT_12: // 要支援２
                as_rank[CommonConstants.INT_7] = CommonConstants.STR_1;
                break;
        }
    }

    /**
     * 介護保険情報を作成
     *
     * @param copyToYm 複写先年月
     * @return 介護保険情報
     */
    private CmnUsrHok3gOutDto getCmnUsrHok3gOutDto(String copyToYm) {
        // 複写先の月末日を取得
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM);
        YearMonth yearMonth = YearMonth.parse(copyToYm, formatter);
        int lastDay = yearMonth.lengthOfMonth();

        CmnUsrHok3gOutDto hoken = new CmnUsrHok3gOutDto();
        hoken.setStartYmd(copyToYm + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART);
        hoken.setEndYmd(copyToYm + CommonConstants.STRING_SLASH + CommonDtoUtil.objValToString(lastDay));
        hoken.setKiEdYmd(copyToYm + CommonConstants.STRING_SLASH + CommonDtoUtil.objValToString(lastDay));
        hoken.setHiHokenNo(CommonConstants.BLANK_STRING);
        hoken.setKHokenNo(CommonConstants.BLANK_STRING);
        hoken.setKHokenCd(CommonConstants.INT_0);
        hoken.setKHokenKnj(CommonConstants.BLANK_STRING);
        hoken.setSeqNo(CommonConstants.INT_0);
        hoken.setKouhuYmd(CommonConstants.BLANK_STRING);
        hoken.setKyufuwari(CommonConstants.INT_90);
        hoken.setTantoId(CommonConstants.INT_0);
        hoken.setTantoNameKnj(CommonConstants.BLANK_STRING);
        hoken.setSenmonNo(CommonConstants.BLANK_STRING);

        List<CmnUsrNin3gOutDto> ninteis = new ArrayList<>();
        hoken.setNintei(ninteis);

        CmnUsrNin3gOutDto nintei = new CmnUsrNin3gOutDto();
        nintei.setYokaiKbn(CommonConstants.INT_0);
        nintei.setGendo(CommonConstants.INT_0);
        nintei.setKbnStartYmd(copyToYm + CommonConstants.STRING_SLASH + CommonConstants.MONTHSTART);
        nintei.setKbnEndYmd(copyToYm + CommonConstants.STRING_SLASH + CommonDtoUtil.objValToString(lastDay));
        ninteis.add(nintei);

        return hoken;
    }

    /**
     * 日付に該当する有効期間ID取得
     *
     * @param ym 年月(yyyy/MM)
     * @return 有効期間ID
     */
    private Integer getDefaultTermId(String ym) {
        Integer termId = null;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM);
        YearMonth yearMonth = YearMonth.parse(ym, formatter);

        YearMonth ym200304 = YearMonth.parse(CommonConstants.DMY_TIME_200304, formatter);
        YearMonth ym200510 = YearMonth.parse(CommonConstants.DMY_TIME_200510, formatter);
        YearMonth ym200604 = YearMonth.parse(CommonConstants.DMY_TIME_200604, formatter);
        YearMonth ym200904 = YearMonth.parse(CommonConstants.DMY_TIME_200904, formatter);
        YearMonth ym201204 = YearMonth.parse(CommonConstants.DMY_TIME_201204, formatter);
        YearMonth ym201404 = YearMonth.parse(CommonConstants.DMY_TIME_201404, formatter);
        YearMonth ym201504 = YearMonth.parse(CommonConstants.DMY_TIME_201504, formatter);

        if (yearMonth.isBefore(ym200304)) {
            // 2003/03/31 >= 変数.年月の場合
            termId = CommonConstants.INT_1;
        } else if ((yearMonth.equals(ym200304) || yearMonth.isAfter(ym200304)) && yearMonth.isBefore(ym200510)) {
            // 2003/04/01 <= 変数.年月 AND 変数.年月 <= 2005/09/30の場合
            termId = CommonConstants.INT_2;
        } else if ((yearMonth.equals(ym200510) || yearMonth.isAfter(ym200510)) && yearMonth.isBefore(ym200604)) {
            // 2005/10/01 <= 変数.年月 AND 変数.年月 <= 2006/03/31の場合
            termId = CommonConstants.INT_3;
        } else if ((yearMonth.equals(ym200604) || yearMonth.isAfter(ym200604)) && yearMonth.isBefore(ym201204)) {
            // 2006/04/01 <= 変数.年月 AND 変数.年月 <= 2009/03/31の場合
            termId = CommonConstants.INT_4;
        } else if ((yearMonth.equals(ym200904) || yearMonth.isAfter(ym200904)) && yearMonth.isBefore(ym200904)) {
            // 2009/04/01 <= 変数.年月 AND 変数.年月 <= 2012/03/31の場合
            termId = CommonConstants.INT_5;
        } else if ((yearMonth.equals(ym201204) || yearMonth.isAfter(ym201204)) && yearMonth.isBefore(ym201404)) {
            // 2012/04/01 <= 変数.年月 AND 変数.年月 <= 2014/03/31の場合
            termId = CommonConstants.INT_6;
        } else if ((yearMonth.equals(ym201404) || yearMonth.isAfter(ym201404)) && yearMonth.isBefore(ym201504)) {
            // 2014/04/01 <= 変数.年月 AND 変数.年月 <= 2015/03/31の場合
            termId = CommonConstants.INT_7;
        } else {
            // 2015/04/01 <= 変数.年月の場合
            termId = kghCmn03gFunc01Logic.getTermidEasy(ym);
        }

        return termId;
    }

    /**
     * 5.利用者情報再検索
     *
     * @param in  計画複写の利用票複写処理の入力DTO
     * @param out 計画複写の利用票複写処理の出力DTO
     */
    private void setRiyouUserList(PlanDuplicateUseSlipDuplicateUpdateServiceInDto in,
            PlanDuplicateUseSlipDuplicateUpdateServiceOutDto out) {
        List<Gui01150UserDto> userList = new ArrayList<>();
        out.setUserList(userList);

        Integer trueCntInt = CommonDtoUtil.strValToInt(out.getTrueCnt());
        if (trueCntInt != null && trueCntInt <= CommonConstants.INT_0) {
            return;
        }
        // 変数.成功行数 > 0の場合、処理続き

        // 計画複写利用票利用者情報を取得
        PlanDuplicateUseSlipUserInfoSelectServiceInDto tempIn = new PlanDuplicateUseSlipUserInfoSelectServiceInDto();
        tempIn.setShienId(in.getShienId());
        tempIn.setFromYm(in.getFromYm());
        tempIn.setStartYm(in.getStartYm());
        tempIn.setEndYm(in.getEndYm());
        tempIn.setTantoId(in.getTantoId());
        tempIn.setGojuuOnRowNo(in.getGojuuOnRowNo());
        tempIn.setGojuuOnKana(in.getGojuuOnKana());
        PlanDuplicateUseSlipUserInfoSelectServiceOutDto tempOut = new PlanDuplicateUseSlipUserInfoSelectServiceOutDto();
        planDuplicateUseSlipUserInfoSelectService.setRiyouUserList(tempIn, tempOut);
        userList.addAll(tempOut.getRiyouUserList());
    }

    /**
     * 関数_利用票サービスコード変換（呼出用）結果
     */
    @Getter
    @Setter
    private static final class ConvertScodeResult {
        /**
         * 運行結果
         */
        private Integer result;
        /**
         * 親レコード番号の最大値
         */
        private Integer maxOyaLineNo;
        /**
         * サービス変更リスト
         */
        private List<ConvertScodeInfo> infos;
    }

    /**
     * サービス変更情報
     */
    @Getter
    @Setter
    private static final class ConvertScodeInfo {
        /**
         * 元親レコード番号
         */
        private Integer baseOyaLineNo;
        /**
         * 新親レコード番号
         */
        private Integer newOyaLineNo;
        /**
         * サービス事業者ID
         */
        private Integer svJigyoId;
        /**
         * サービス項目ID
         */
        private Integer svItemCd;
        /**
         * 回数
         */
        private int[] days = new int[CommonConstants.INT_31];
        /**
         * 合計
         */
        private Integer total;
    }

    /**
     * 関数_利用票サービスコード変換（メイン）結果
     */
    @Getter
    @Setter
    private static final class ConvertScodeRankResult {
        /**
         * 運行結果
         */
        private Integer result = CommonConstants.INT_0;
        /**
         * 月初時サービスコード
         */
        private String asScodeBom = CommonConstants.EMPTY_STRING;
        /**
         * 月末時サービスコード
         */
        private String asScodeEom = CommonConstants.EMPTY_STRING;
        /**
         * 新基準日
         */
        private String asNewDate = CommonConstants.EMPTY_STRING;
    }

    /**
     * 文字列型のOUTパラメータ
     */
    @Getter
    @Setter
    private static final class StringRef {
        /**
         * 文字列
         */
        private String value = CommonConstants.EMPTY_STRING;
    }
}
