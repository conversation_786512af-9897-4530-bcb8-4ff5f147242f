package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI01038_週間計画
 * 
 * @description
 *              加算退避IDリスト
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class Gui01038DeleteKasanId extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** 詳細データID */
    @NotEmpty
    private String ks52Id;

    /** 加算データID */
    @NotEmpty
    private String deleteKasanId;

}
