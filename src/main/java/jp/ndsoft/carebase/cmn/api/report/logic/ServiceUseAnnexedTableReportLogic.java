package jp.ndsoft.carebase.cmn.api.report.logic;

import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSet;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.V00231ServiceUseAnnexedTableBeppyoEntity;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.V00231ServiceUseAnnexedTableKohi;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.V00231ServiceUseAnnexedTableRiyouBeppyo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.V00231ServiceUseAnnexedTableShortDateSum;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.V00231ServiceUseAnnexedTableSvJigyo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.V00231ServiceUseAnnexedTableSyafuku;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.V00231ServiceUseAnnexedTableSyurui;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.V00231ServiceUseAnnexedTableUserInfo;
import jp.ndsoft.carebase.cmn.api.logic.KghCmn03gFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnRiyouPrn01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkBase02Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.logic.dto.CmnUsrHok3gOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GetUserInfoMonthOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KghCmnUsrInfo3gDto;

import jp.ndsoft.carebase.cmn.api.report.dto.ReportCommonGetOptionDto;
import jp.ndsoft.carebase.cmn.api.report.dto.ServiceUseAnnexedTableReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.model.ServiceUseAnnexedTableReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.service.ShoninSetReportService;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo1List202104ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo1List202104OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3KohiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3KohiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3SyafukuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3SyafukuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnPrnBetuS3RiyouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnPrnBetuS3RiyouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTrsGPlanOutGendoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTrsGPlanOutGendoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanBetu1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanGaiFutanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanKouhiFutanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanSyafukuKeigenSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.PlanBetu2SelectMapper;
import jp.ndsoft.smh.framework.properties.FrameworkProperties;
import net.sf.jasperreports.engine.JRBand;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.design.JRDesignExpression;
import net.sf.jasperreports.engine.design.JRDesignStaticText;
import net.sf.jasperreports.engine.design.JRDesignTextField;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.xml.JRXmlLoader;

/**
 * @since 2025.07.21
 * <AUTHOR> 劉顕康
 * @description V00231_サービス利用票別表 帳票出力
 */
@Component
public class ServiceUseAnnexedTableReportLogic {

    /** サービス事業者名称取得 */
    @Autowired
    ComMscSvjigyoSelectMapper comMscSvjigyoSelectMapper;

    /** V00251_サービス利用票 */
    @Autowired
    SimulationRiteiReportLogic SimulationRiteiReportLogic;

    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** 指定年月での利用者の介護保険情報を取得する(3G) */
    @Autowired
    KghCmn03gFunc01Logic kghCmn03gFunc01Logic;

    /** 要介護度比較関数 */
    @Autowired
    KghCmpF01Logic kghCmpF01Logic;

    /** 利用者名を得る */
    @Autowired
    KghCmnF01Logic kghCmnF01Logic;

    /** 利用者名代替データ */
    @Autowired
    Nds3GkBase02Logic nds3GkBase02Logic;

    /** 利用票別表 区分支給限度管理 */
    @Autowired
    CmnTucPlanBetu1SelectMapper cmnTucPlanBetu1SelectMapper;

    /** NEXT：dmyCalcSort に代入：帳票別表限定 */
    @Autowired
    KghCmnRiyouPrn01Logic kghCmnRiyouPrn01Logic;

    /** 利用票別表種類別限度管理 */
    @Autowired
    PlanBetu2SelectMapper planBetu2SelectMapper;

    /** ケアマネ：利用票：シミュレーション処理 別表 公費集計欄(非表示) */
    @Autowired
    CmnTucPlanKouhiFutanSelectMapper cmnTucPlanKouhiFutanSelectMapper;

    /** 利用票別表 保険外負担 */
    @Autowired
    CmnTucPlanGaiFutanSelectMapper cmnTucPlanGaiFutanSelectMapper;

    /** 利用票別表 社福軽減 */
    @Autowired
    CmnTucPlanSyafukuKeigenSelectMapper cmnTucPlanSyafukuKeigenSelectMapper;

    // 承認欄設定
    @Autowired
    private ShoninSetReportService shoninSetReportService;

    /**
     * サービス利用票別表の帳票パラメータ取得
     *
     * @param inDto       入力データ
     * @param printOption 印刷オプション
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public List<ServiceUseAnnexedTableReportServiceInDto> getV00231ServiceUseAnnexedTableReportParameters(
            ServiceUseAnnexedTableReportParameterModel inDto, ReportCommonGetOptionDto printOption) throws Exception {
        // ファイルサーバにアップロードするか設定
        inDto.setUploadFileServer(ReportUtil.getUploadFileServerSetting(inDto));

        // 帳票用データ詳細
        List<ServiceUseAnnexedTableReportServiceInDto> infoInDtoList = new ArrayList<ServiceUseAnnexedTableReportServiceInDto>();

        // 2.2. 印刷オプションを取得する。
        // API定義書_V00251_サービス利用票.xlsxの「API定義1_wf_00_get_option」
        // '【変数】.利用票印刷オプション = 上記結果
        printOption = SimulationRiteiReportLogic.wf_00_get_option(inDto.getSyscd(),
                CommonDtoUtil.strValToInt(inDto.getShokuinId()), inDto.getYymmYm(), new ArrayList<String>());

        // 3. サービス利用票別表の帳票用データを設定する。
        infoInDtoList = wf_2nd_riyo_b_loop(inDto.getUserIdList(), inDto.getYymmYm(), inDto.getSyscd(),
                inDto.getShokuinId(), inDto.getAppYmd(), inDto.getPrintSet(), printOption);

        return infoInDtoList;
    }

    /**
     * 関数_wf_2nd_riyo_b_loop サービス利用票別表の帳票用データを設定する
     * 
     * @param userList    利用者IDリスト
     * @param yyyyMm      提供年月
     * @param syscd       システムコード
     * @param shokuinId   職員ID
     * @param appYmd      基準日
     * @param printSet    印刷設定
     * @param printOption 印刷オプション
     * @return
     */
    public List<ServiceUseAnnexedTableReportServiceInDto> wf_2nd_riyo_b_loop(
            List<V00231ServiceUseAnnexedTableUserInfo> userList, String yyyyMm, String syscd, String shokuinId,
            String appYmd, ReportCommonPrintSet printSet, ReportCommonGetOptionDto printOption) {
        // 2.返却情報初期化
        // 帳票用データリスト
        List<ServiceUseAnnexedTableReportServiceInDto> infoInDtoList = new ArrayList<ServiceUseAnnexedTableReportServiceInDto>();

        // 3.変数初期化
        // 3.1.変数初期化
        // 【変数】.seq = 1
        Integer seq = 1;
        // 【変数】.サービス提供年月  = 引き渡しパラメータ.提供年月
        String teikyuYm = yyyyMm;
        // 【変数】.開始日付  = 【変数】.サービス提供年月 + "/01"
        String startDate = teikyuYm + CommonConstants.STRING_FIRST_DAY;
        // 【変数】.終了日付  =【変数】.開始日付
        String endDate = startDate;
        // 【変数】.flg = FALSE
        boolean flg = false;
        // 【変数】.nc = 0
        Integer nc = 0;
        // 【変数】.ksh = 引き渡しパラメータ.印刷オプション.敬称（利用票別表）
        String ksh = printOption.getRbKeisho();

        // 3.2.【変数】.指定日の取得
        // 【変数】.指定日
        String shiteibi = CommonConstants.EMPTY_STRING;
        // 3.2.1. 引き渡しパラメータ.印刷設定.指定日印刷区分が"3：空白"の場合、下記共通関数で利用し、システム日付の空欄表示を取得する。
        if (CommonConstants.SHITEIBI_PRINT_KBN_3.equals(printSet.getShiTeiKubun())) {
            // 【変数】.指定日 = 上記結果
            shiteibi = nds3GkFunc01Logic.blankDate(appYmd);
        } else if (CommonConstants.SHITEIBI_PRINT_KBN_2.equals(printSet.getShiTeiKubun())) {
            // 3.2.2. 引き渡しパラメータ.印刷設定.指定日印刷区分が"2:指定日印刷"の場合、下記共通関数で利用し、指定年月日を和暦に変換する。
            // 【変数】.指定日=上記結果.和暦
            shiteibi = nds3GkFunc01Logic.get2Gengouj(1, printSet.getShiTeiDate());
        }

        // 4.引き渡しパラメータ.利用者配列にデータがない場合、処理終了。
        if (CollectionUtils.isEmpty(userList)) {
            return infoInDtoList;
        }

        // 5.利用者毎の帳票用データを設定する
        // LOOP1:【繰り返し開始】：引き渡しパラメータ.利用者配列をループして、下記処理を実施する。
        for (V00231ServiceUseAnnexedTableUserInfo user : userList) {
            // 5.1.変数初期化
            // 【変数】.nc ++
            nc++;
            // 【変数】.gen = 0
            Integer gen = 0;
            // 【変数】.hkn数 = 0
            Integer hknNum = 0;
            // 【変数】.hkn = ""
            String hkn = CommonConstants.EMPTY_STRING;
            // 【変数】.flg = FALSE
            flg = false;
            // 【変数】.yok = 0
            Integer yok = 0;

            // 【変数】.ny2
            Integer ny2 = null;

            // 【変数】.利用者id = 引き渡しパラメータ.利用者配列[該当行].利用者id
            String userid = user.getUserId();
            // 【変数】.サービス提供年月(変更日) = 引き渡しパラメータ.利用者配列[該当行].サービス提供年月(変更日)
            String yymmd = user.getYymmd();
            // 【変数】.支援事業者id = 引き渡しパラメータ.利用者配列[該当行].支援事業者id
            String shienid = user.getShienid();

            // 5.2.【変数】.利用者情報構造体初期化
            KghCmnUsrInfo3gDto userInfo = new KghCmnUsrInfo3gDto();
            GetUserInfoMonthOutDto userInfoOutDto = kghCmn03gFunc01Logic
                    .getUserinfoMonth(CommonDtoUtil.strValToInt(userid), teikyuYm, CommonDtoUtil.strValToInt(shienid));
            if (userInfoOutDto != null) {
                // 【変数】.利用者情報構造体 = 上記結果
                userInfo = userInfoOutDto.getUsrInfo();
            }

            // 【変数】.head = ""
            String head = CommonConstants.EMPTY_STRING;

            if (userInfo != null && CollectionUtils.isNotEmpty(userInfo.getHoken())) {
                List<CmnUsrHok3gOutDto> hokenList = userInfo.getHoken();
                // 【変数】.一致idx
                int icchiIndex = -1;
                // 5.2.1.どの情報を用いるかのマッチング(保険有効期間開始日の下2桁が、yymm_dと一致するか)
                // LOOP2:【繰り返し開始】：【変数】.利用者情報構造体.介護保険情報構造体配列をループして、下記処理を実施する。
                for (int i = 0; i < hokenList.size(); i++) {
                    CmnUsrHok3gOutDto hoken = hokenList.get(i);
                    // ・【変数】.サービス提供年月(変更日) ==【変数】.利用者情報構造体.介護保険情報構造体配列[該当行].指定月の有効開始日の下2桁の場合
                    if (StringUtils.equals(yymmd, hoken.getKiStYmd().substring(hoken.getKiStYmd().length() - 2))) {
                        // 【変数】.flg = TRUE
                        flg = true;
                        // 【変数】.一致idx = 該当行
                        icchiIndex = i;
                        // LOOP2:【繰り返し終了】
                        break;
                    }
                }

                // 5.3. 【変数】.利用者情報構造体の値を設定する
                // 5.3.1.【変数】.flg == TRUEの場合
                if (flg == true) {
                    // 【変数】.num = 【変数】.利用者情報構造体.介護保険情報構造体配列[【変数】.一致idx ].str_ninteiの最大数
                    Integer num = hokenList.get(icchiIndex).getNinteiMax();
                    // ・【変数】.num>0の場合
                    if (num > 0) {
                        // 【変数】.gen = 【変数】.利用者情報構造体.介護保険情報構造体配列[【変数】.一致idx ].認定情報構造体配列[1].限度額
                        gen = hokenList.get(icchiIndex).getNintei().get(0).getGendo();
                        // 【変数】.hkn数 = 【変数】.利用者情報構造体.介護保険情報構造体配列[【変数】.一致idx ].k_hoken_cd
                        hknNum = hokenList.get(icchiIndex).getKHokenCd();
                        // 【変数】.hkn = 【変数】.利用者情報構造体.介護保険情報構造体配列[【変数】.一致idx ].hi_hoken_no
                        hkn = hokenList.get(icchiIndex).getHiHokenNo();

                        // 【変数】.ny1 = 【変数】.利用者情報構造体.介護保険情報構造体配列[【変数】.一致idx ].認定情報構造体配列[1].要介護度
                        Integer ny1 = hokenList.get(icchiIndex).getNintei().get(0).getYokaiKbn();
                        // 【変数】.yok = ny1
                        yok = ny1;
                        // ・【変数】.num > 1 Then
                        if (num > 1) {
                            // 【変数】.ny2 = 【変数】.利用者情報構造体.介護保険情報構造体配列[【変数】.一致idx ].認定情報構造体配列[【変数】.num ].要介護度
                            ny2 = hokenList.get(icchiIndex).getNintei().get(num).getYokaiKbn();

                            Integer resultFlg = kghCmpF01Logic.diffYokaiKbn(ny2, ny1);

                            // ・上記結果.結果フラグ <= 0
                            if (resultFlg <= 0) {
                                // 【変数】.gen = 【変数】.利用者情報構造体.介護保険情報構造体配列[【変数】.一致idx ].認定情報構造体配列[【変数】.num ].限度額
                                gen = hokenList.get(icchiIndex).getNintei().get(num).getGendo();
                                // 【変数】.yok = 【変数】.ny2
                                yok = ny2;
                            }
                        }

                    }
                } else {
                    // 5.3.2.上記以外の場合
                    // 【変数】.hkn = ""
                    hkn = CommonConstants.EMPTY_STRING;
                    // 【変数】.利用者情報構造体.利用者id = 【変数】.利用者id
                    userInfo.setUserid(CommonDtoUtil.strValToInt(userid));
                    // 【変数】.利用者情報構造体.性別 = 2
                    userInfo.setSex(CommonConstants.NUMBER_2);

                    String userName = kghCmnF01Logic.getUserName(CommonDtoUtil.strValToInt(userid));
                    // 【変数】.利用者情報構造体.利用者名（姓＋名：間に半角スペースを入れる） = 上記結果
                    userInfo.setUsernameKnj(userName);

                    String userNameKana = kghCmnF01Logic.getUserNameKana(CommonDtoUtil.strValToInt(userid));
                    // 【変数】.利用者情報構造体.利用者名のカナ（姓＋名：間に半角スペースを入れる） = 上記結果
                    userInfo.setUsernameKana(userNameKana);
                }

                // 5.4. 【変数】.genの値を設定する
                // 5.4.1.下記関数で利用し、区分支給限度額を取得
                // API定義書_V00251_サービス利用票.xlsxの「API定義4_wf_chk_gaibu_gendo」
                SimulationRiteiReportLogic.wf_chk_gaibu_gendo(CommonDtoUtil.strValToInt(shienid), yok, yyyyMm,
                        Double.valueOf(gen));

                // 5.4.2. (【変数】.yok = 21 AND 【変数】.ny2 = 0) or (【変数】.yok = 21 AND 【変数】.ny2 =
                // 11)の場合、限度額を強制的に要支援1の額に書き換える
                if ((yok == CommonConstants.INT_21 && ny2 == CommonConstants.NUMBER_0)
                        || (yok == CommonConstants.INT_21 && ny2 == CommonConstants.INT_11)) {
                    // 【変数】.gen = 上記結果
                    gen = kghCmnF01Logic.getGendo(CommonConstants.INT_11, teikyuYm, false, false);
                }

                // 5.5. 帳票用データのヘッドを設定する
                // 【変数】.head = ""
                head = CommonConstants.EMPTY_STRING;
                // 5.5.1. 引き渡しパラメータ.印刷オプション.利用者名など表示設定 = 1 の場合
                if (CommonConstants.STR_1.equals(printOption.getBetu())) {
                    // 【変数】.gym = 上記結果
                    String gym = nds3GkFunc01Logic.get2Gengouj(1, teikyuYm);
                    // 【変数】.unm = 【変数】.利用者情報構造体.利用者名（姓＋名：間に半角スペースを入れる）
                    String unm = userInfo.getUsernameKnj();

                    // 5.5.1.1. 引き渡しパラメータ.利用票印刷オプション.個人情報保護設定フラグ == True：個人情報保護設定ありの場合、
                    if (printOption.isKojinFlg()) {
                        F3gkGetProfileInDto f3gkGetProfileInDto = new F3gkGetProfileInDto();
                        // システムコード
                        f3gkGetProfileInDto.setGsyscd(syscd);
                        // 職員ID
                        f3gkGetProfileInDto.setShokuId(CommonDtoUtil.strValToInt(shokuinId));
                        // 法人ID
                        f3gkGetProfileInDto.setHoujinId(CommonConstants.HOUJIN_ID_0);
                        // 施設ID
                        f3gkGetProfileInDto.setShisetuId(CommonConstants.SHISETU_ID);
                        // 事業所ID
                        f3gkGetProfileInDto.setSvJigyoId(CommonConstants.DEFAULT_SV_JIGYO_ID);
                        // 画面名
                        f3gkGetProfileInDto.setKinounameKnj(CommonConstants.KINOU_NAME_PRT);
                        // セクション
                        f3gkGetProfileInDto.setSectionKnj(CommonConstants.SECTION_NAME_3GK);
                        // キー
                        f3gkGetProfileInDto.setKeyKnj(CommonConstants.S_KOJINHOGO_FLG);
                        // 初期値
                        f3gkGetProfileInDto.setAsDefault(CommonConstants.STR_1);

                        String getF3gkProfileResult = nds3GkFunc01Logic.getF3gkProfile(f3gkGetProfileInDto);
                        // 引き渡しパラメータ.利用票印刷オプション.個人情報保護設定 = 上記結果
                        printOption.setKojin(getF3gkProfileResult);

                        // 5.5.1.2. 引き渡しパラメータ.利用票印刷オプション.個人情報保護設定フラグ == True：個人情報保護設定ありの場合、
                        // ①引き渡しパラメータ.利用票印刷オプション.個人情報保護設定 == 1の場合
                        if (CommonConstants.STR_1.equals(printOption.getKojin())) {
                            // f3gk_com_print_kojinhogo_nokado
                            // 【変数】.sub_name = 上記結果
                            String sub_name = nds3GkBase02Logic.getKojinhogoNokado(CommonDtoUtil.strValToInt(userid));

                            // 【変数】.head = 【変数】.gym + " " + 【変数】.sub_name + " " + 【変数】.ksh
                            head = gym + CommonConstants.THREE_SPACE + sub_name + CommonConstants.BLANK_SPACE + ksh;

                        } else {
                            // 上記以外の場合
                            // 【変数】.header = 【変数】.gym + " " + 【変数】.hkn + " " + 【変数】.unm + " " + 【変数】.ksh
                            head = gym + CommonConstants.THREE_SPACE + hkn + CommonConstants.THREE_SPACE + unm
                                    + CommonConstants.BLANK_SPACE + ksh;
                        }
                    } else {
                        // 5.5.1.3. 上記以外の場合
                        unm = kghCmn03gFunc01Logic.getFusejiSingle(CommonConstants.SECTION_NAME_3GK,
                                CommonConstants.NAME_STR, unm, CommonDtoUtil.strValToInt(shokuinId), syscd,
                                CommonConstants.STR_1, CommonConstants.STR_0);
                        hkn = kghCmn03gFunc01Logic.getFusejiSingle(CommonConstants.SECTION_NAME_3GK,
                                CommonConstants.HIHO_STR, hkn, CommonDtoUtil.strValToInt(shokuinId), syscd,
                                CommonConstants.STR_1, CommonConstants.STR_0);
                    }

                    // 5.5.1.4. 【変数】.指定日 <> ""の場合
                    if (StringUtils.isNotEmpty(shiteibi)) {
                        // 【変数】.head = 【変数】.head + " : "
                        head += CommonConstants.SPACE_COLON_SPACE;
                    }

                } else {
                    // 5.5.2. 上記以外の場合
                    // 【変数】.head = ""
                    head = CommonConstants.EMPTY_STRING;
                }

                // 5.5.3.【変数】.head = 【変数】.head +【変数】.指定日
                head += shiteibi;
            }

            // 5.6.関数_wf_2nd_riyo_b_core
            // 【変数】.帳票用データ = 上記結果
            ServiceUseAnnexedTableReportServiceInDto infoInDto = wf_2nd_riyo_b_core(user, teikyuYm, seq, shienid,
                    startDate, endDate, hknNum, hkn, head, gen, appYmd, printSet, printOption);

            // 2. 共通データの値を設定する
            // 2.1. 印刷設定と印刷オプションの値を設定する
            // 帳票用データ.指定日印刷区分=リクエストパラメータ.印刷設定.指定日印刷区分
            infoInDto.setShiTeiKubun(printSet.getShiTeiKubun());

            // 5.7.【変数】.帳票用データ.ヘッド = 【変数】.head
            infoInDto.setHeader(head);

            if (CommonConstants.NUM_STR_1.equals(printOption.getBibDay())) {
                infoInDto.getRiyouBeppyoDataList().forEach(i -> i.setBoldFlg(CommonConstants.NUM_STR_1));
            }
            // infoInDtoの各リストを出力用に変換する
            // 別表明細情報 出力用
            infoInDto.setRiyouBeppyoList(new JRBeanCollectionDataSource(infoInDto.getRiyouBeppyoDataList()));
            // 種類情報 出力用
            infoInDto.setSyuruiList(new JRBeanCollectionDataSource(infoInDto.getSyuruiDataList()));
            // 公費適用事業情報 出力用
            infoInDto.setKohiList(new JRBeanCollectionDataSource(infoInDto.getKohiDataList()));
            // 適用事業所情報 出力用
            infoInDto.setSvJigyoList(new JRBeanCollectionDataSource(infoInDto.getSvJigyoDataList()));
            // 短期入所利用日数情報のリスト 出力用
            infoInDto.setShortDateSumList(new JRBeanCollectionDataSource(infoInDto.getShortDateSumDataList()));
            // 社福事業所情報 出力用
            infoInDto.setSyafukuList(new JRBeanCollectionDataSource(infoInDto.getSyafukuDataList()));

            // 【返却】.帳票用データリストに「【変数】.帳票用データ」を追加する
            infoInDtoList.add(infoInDto);
        }

        return infoInDtoList;
    }

    /**
     * 関数_wf_2nd_riyo_b_core サービス利用票別表 : 印 刷 : コ ア : 代入部
     * 
     * @param user        当該利用者情報の構造体
     * @param teikyuYm    サービス提供年月
     * @param seq         謎な連番
     * @param shienid     事業者ID
     * @param startDate   開始日付
     * @param endDate     終了日付
     * @param hknNum      保険者cd（月途中保険者変更時のみ）
     * @param hkn         変 更 日（月途中保険者変更時のみ）
     * @param head        帳票ヘッダ
     * @param gen         居宅サービス区分支給限度額
     * @param appYmd      基準日
     * @param printSet    印刷設定
     * @param printOption 印刷オプション
     * @return
     */
    public ServiceUseAnnexedTableReportServiceInDto wf_2nd_riyo_b_core(V00231ServiceUseAnnexedTableUserInfo user,
            String teikyuYm, Integer seq, String shienid, String startDate, String endDate, Integer hknNum, String hkn,
            String head, Integer gen, String appYmd, ReportCommonPrintSet printSet,
            ReportCommonGetOptionDto printOption) {
        // 2.返却情報初期化
        // 帳票用データ
        ServiceUseAnnexedTableReportServiceInDto infoInDto = new ServiceUseAnnexedTableReportServiceInDto();
        // 帳票用データ.別表明細情報
        List<V00231ServiceUseAnnexedTableRiyouBeppyo> retBeppyoList = new ArrayList<V00231ServiceUseAnnexedTableRiyouBeppyo>();

        // 3.変数初期化
        // 3.1.変数初期化
        // 3.2.【変数】.作成年月日の取得
        // API定義書_V00251_サービス利用票.xlsxの「API定義7_wf_get_sakusei_ymd」
        // 【変数】.作成年月日 = 上記結果
        String sakuseiYmd = SimulationRiteiReportLogic.wf_get_sakusei_ymd(appYmd, CommonDtoUtil.strValToInt(shienid),
                user.getUserId(), user.getYymmd(), printOption.getSaku(), printOption.getSaku());

        // 3.3. 【変数】.サービス事業者区分(j_cd)の取得
        // 【変数】.サービス事業者区分(j_cd) = 上記結果
        String j_cd = kghCmnF01Logic.getSvJigyoCd(CommonDtoUtil.strValToInt(shienid));

        // 4. 引き渡しパラメータ.印刷オプション.数字太字表示設定 = 1 の場合、以下の項目は太字で表示されている。
        // 5. 居宅介護支援事業者番号の表示制御を設定する。
        // ServiceUseAnnexedTableReportServiceに処理する

        // 6. 利用票別表作成
        // 6.1.利用票別表 区分支給限度管理を取得する。
        KghCmnBeppyo1List202104ByCriteriaInEntity kghCmnBeppyo1List202104ByCriteriaInEntity = new KghCmnBeppyo1List202104ByCriteriaInEntity();
        // 支援事業者ID
        kghCmnBeppyo1List202104ByCriteriaInEntity.setAlShienId(CommonDtoUtil.strValToInt(shienid));
        // 利用者ID
        kghCmnBeppyo1List202104ByCriteriaInEntity.setAlUserid(CommonDtoUtil.strValToInt(user.getUserId()));
        // サービス提供年月
        kghCmnBeppyo1List202104ByCriteriaInEntity.setAsYymmYm(teikyuYm);
        // サービス提供年月（変更日）
        kghCmnBeppyo1List202104ByCriteriaInEntity.setAsYymmD(user.getYymmd());
        // 【変数】.利用票別表＝上記結果
        List<KghCmnBeppyo1List202104OutEntity> beppyoListRet = cmnTucPlanBetu1SelectMapper
                .findKghCmnBeppyo1List202104ByCriteria(kghCmnBeppyo1List202104ByCriteriaInEntity);

        List<V00231ServiceUseAnnexedTableBeppyoEntity> beppyoList = new ArrayList<V00231ServiceUseAnnexedTableBeppyoEntity>();

        if (CollectionUtils.isNotEmpty(beppyoListRet)) {
            for (KghCmnBeppyo1List202104OutEntity beppyo : beppyoListRet) {
                V00231ServiceUseAnnexedTableBeppyoEntity newBeppyo = new V00231ServiceUseAnnexedTableBeppyoEntity();
                newBeppyo.setKyufuRitu(beppyo.getKyufuRitu());
                newBeppyo.setHKyufugaku(beppyo.getHKyufugaku());
                newBeppyo.setKasanF(beppyo.getKasanF());
                newBeppyo.setScode(beppyo.getScode());
                newBeppyo.setWaribikiRitu(beppyo.getWaribikiRitu());
                newBeppyo.setWaribikiTen(beppyo.getWaribikiTen());
                newBeppyo.setOv30Fl(beppyo.getOv30Fl());
                newBeppyo.setTHutanTanka(beppyo.getTHutanTanka());
                newBeppyo.setKyufuDiffF(beppyo.getKyufuDiffF());
                newBeppyo.setDmyFormalnameKnj(beppyo.getDmyFormalnameKnj());
                newBeppyo.setSvTensu(beppyo.getSvTensu());
                newBeppyo.setDmyJigyoNumber(beppyo.getDmyJigyoNumber());
                newBeppyo.setSTensuOver(beppyo.getSTensuOver());
                newBeppyo.setDmyCalcSort(beppyo.getDmyCalcSort());
                newBeppyo.setSTensu(beppyo.getSTensu());
                newBeppyo.setDmyKbnGendo(beppyo.getDmyKbnGendo());
                newBeppyo.setKTensuOver(beppyo.getKTensuOver());
                newBeppyo.setKyufukanriTen(beppyo.getKyufukanriTen());
                newBeppyo.setKTensu(beppyo.getKTensu());
                newBeppyo.setTanka(beppyo.getTanka());
                newBeppyo.setHutanH(beppyo.getHutanH());
                newBeppyo.setHutanJ(beppyo.getHutanJ());
                newBeppyo.setSvtype(beppyo.getSvtype());
                newBeppyo.setSvcode(beppyo.getSvcode());
                newBeppyo.setShienId(beppyo.getShienId());
                newBeppyo.setUserid(beppyo.getUserid());
                newBeppyo.setYymmYm(beppyo.getYymmYm());
                newBeppyo.setYymmD(beppyo.getYymmD());
                newBeppyo.setSvJigyoId(beppyo.getSvJigyoId());
                newBeppyo.setSvItemCd(beppyo.getSvItemCd());
                newBeppyo.setEdaNo(beppyo.getEdaNo());
                newBeppyo.setTensu(beppyo.getTensu());
                newBeppyo.setKaisu(beppyo.getKaisu());
                newBeppyo.setTotalF(beppyo.getTotalF());
                newBeppyo.setHiyouSougaku(beppyo.getHiyouSougaku());
                beppyoList.add(newBeppyo);
            }

            // 6.2.NEXT：dmyCalcSort に代入：帳票別表限定
            kghCmnRiyouPrn01Logic.setAdderServiceBetu(beppyoList, startDate);

            // 6.3. 利用票別表の 順番を変更する。
            // 【変数】.利用票別表は以下の項目によってソートされる。
            // ・サービス種類CD A
            // ・DMYJigyoNumber A
            // ・サービス事業者ID A
            // ・30日超過フラグ A
            // ・合計フラグ A
            // ・加算フラグ A
            // ・DMYCalcSort A
            // ・サービスコード A
            // ・サービス項目ID A
            // ・枝番 A
            beppyoList.sort(Comparator.comparing(V00231ServiceUseAnnexedTableBeppyoEntity::getSvtype)
                    .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getDmyJigyoNumber)
                    .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getSvJigyoId)
                    .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getOv30Fl)
                    .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getTotalF)
                    .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getKasanF)
                    .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getDmyCalcSort)
                    .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getScode)
                    .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getSvItemCd)
                    .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getEdaNo));
        }

        // 6.4. 特定施設入居者生活介護ならば、帳票タイトルを書き換える
        // 6.4.1.【変数】.サービス事業者区分(j_cd) = "23031" or 【変数】.サービス事業者区分(j_cd) = "43031" or
        // 【変数】.サービス事業者区分(j_cd) = "63031"の場合、
        if (CommonConstants.TOKUTEI_SHISETU_KAIGO_VALUES.contains(j_cd)) {
            // 帳票用データ.帳票タイトル = 特定施設サービス利用票別表
            infoInDto.setTitle(CommonConstants.TITLE_SERVICE_BEPPYO);

            if (CollectionUtils.isNotEmpty(beppyoList)) {
                // ①【変数】.利用票別表＝ 以下の条件によるフリッターの結果
                // Filter条件: (合計フラグ = 0) or (合計フラグ = 1 and (合計単位数 > 0 or (合計単位数 = 0 and 回数 >
                // 0)))
                beppyoList = beppyoList.stream()
                        .filter(item -> (item.getTotalF() == CommonConstants.GOUKEI_FLG_0)
                                || (item.getTotalF() == CommonConstants.GOUKEI_FLG_1
                                        && (item.getSvTensu() > 0 || (item.getSvTensu() == 0 && item.getKaisu() > 0))))
                        .toList();

                if (CollectionUtils.isNotEmpty(beppyoList)) {
                    // ➁ DMYサービス種類CDを設定する
                    for (V00231ServiceUseAnnexedTableBeppyoEntity beppyo : beppyoList) {
                        // ・サービス種類CD = '33' or サービス種類CD = '35'の場合、
                        if (CommonConstants.SV_CD_STR_33.equals(beppyo.getSvtype())
                                || CommonConstants.SV_CD_STR_35.equals(beppyo.getSvtype())) {
                            // ・加算フラグ = 0の場合、
                            if (beppyo.getKasanF() == CommonConstants.KASAN_FLG_NORMAL) {
                                // DMYサービス種類CD = サービス種類CD
                                beppyo.setDmySvTypeCd(beppyo.getSvtype());
                            } else {
                                // ・上記以外の場合
                                // DMYサービス種類CD = "zz"
                                beppyo.setDmySvTypeCd(CommonConstants.DMY_SV_TYPE_CD_ZZ);
                            }
                        } else {
                            // ・上記以外の場合
                            // DMYサービス種類CD = "zz"
                            beppyo.setDmySvTypeCd(CommonConstants.DMY_SV_TYPE_CD_ZZ);
                        }
                    }

                    // ③【変数】.利用票別表は以下の項目によってソートされる。
                    // ・DMYサービス種類CD A
                    // ・30日超過フラグ A
                    // ・加算フラグ A
                    // ・合計フラグ A
                    // ・DMYJigyoNumber A
                    // ・サービス事業者ID A
                    // ・DMYCalcSort A
                    // ・サービスコード A
                    // ・サービス項目ID A
                    // ・枝番 A
                    beppyoList.sort(Comparator.comparing(V00231ServiceUseAnnexedTableBeppyoEntity::getDmySvTypeCd)
                            .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getOv30Fl)
                            .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getKasanF)
                            .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getTotalF)
                            .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getDmyJigyoNumber)
                            .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getSvJigyoId)
                            .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getDmyCalcSort)
                            .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getScode)
                            .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getSvItemCd)
                            .thenComparing(V00231ServiceUseAnnexedTableBeppyoEntity::getEdaNo));
                }
            }

        }

        // 6.5. 帳票用データ詳細を設定する
        // 6.5.1.基本項目の値を設定する
        // 【返却】.帳票用データ.作成年月日 = 【変数】.作成年月日
        infoInDto.setSakuseiYmd(sakuseiYmd);

        // 6.5.2.リスト情報の値を設定する
        if (CollectionUtils.isNotEmpty(beppyoList)) {
            // 【繰り返し開始】：【変数】.利用票別表をループして、下記処理を実施する。
            for (V00231ServiceUseAnnexedTableBeppyoEntity beppyo : beppyoList) {
                V00231ServiceUseAnnexedTableRiyouBeppyo retBeppyo = new V00231ServiceUseAnnexedTableRiyouBeppyo();

                // 【変数】.サービス提供事業所id = 【変数】.利用票別表.サービス事業者ID
                Integer svJigyoId = beppyo.getSvJigyoId();
                String shienName = kghCmn03gFunc01Logic.getShienName(svJigyoId,
                        teikyuYm + CommonConstants.STR_DELIMITER + user.getYymmd());
                // 【返却】.帳票用データ.別表明細情報.事業所名 = 上記結果
                retBeppyo.setJigyoName(shienName);

                String shienNumber = kghCmn03gFunc01Logic.getShienNumber(svJigyoId,
                        teikyuYm + CommonConstants.STR_DELIMITER + user.getYymmd());
                // 【返却】.帳票用データ.別表明細情報.事業所番号 = 上記結果
                retBeppyo.setJigyoNumber(shienNumber);

                // 【変数】.サービス種類CD =【変数】.利用票別表.サービス種類CD
                String svtype = beppyo.getSvtype();
                // 【変数】.総合事業区分 = 上記結果
                int sougouSvkbn = kghCmnF01Logic.sougouSvKbn(svtype);

                // 【変数】.給付率差異フラグ = 【変数】.利用票別表.給付率差異フラグ
                Integer kyufuDiffF = beppyo.getKyufuDiffF();
                // 【変数】.サービス項目ID =【変数】.利用票別表.サービス項目ID
                Integer svItemCd = beppyo.getSvItemCd();
                // 【変数】.30日超過フラグ =【変数】.利用票別表.30日超過フラグ ※nullの場合は0を設定する。
                Integer ov30Fl = beppyo.getOv30Fl() == null ? 0 : beppyo.getOv30Fl();

                // f_cmp_get_item_formalname_30
                // 【変数】.サービス名 = 上記結果
                String svName = kghCmpF01Logic.getItemFormalname30(svJigyoId, svItemCd,
                        teikyuYm + CommonConstants.STRING_FIRST_DAY, ov30Fl);

                // 【変数】.サービス項目ID = 0 or 【変数】.サービス項目ID = null の場合、
                if (svItemCd == null || svItemCd == 0) {
                    String serviceName = kghCmnF01Logic.getServiceNameByType(svtype);
                    // 【変数】.サービス名 = 【変数】.サービス名 + 上記結果 + "合計"
                    svName = svName + serviceName + CommonConstants.STR_TOTAL;
                }

                // 【返却】.帳票用データ.別表明細情報.サービス内容／種類 = 【変数】.サービス名
                retBeppyo.setSvShuCd(svName);
                // 【返却】.帳票用データ.別表明細情報.ｻｰﾋﾞｽｺｰﾄ = 【変数】.利用票別表.サービスコードの左6桁 ※nullの場合は""を設定する。
                retBeppyo.setScode(
                        beppyo.getScode() == null || beppyo.getScode().length() < 6 ? CommonConstants.EMPTY_STRING
                                : beppyo.getScode().substring(0, 6));

                // abs（【変数】.利用票別表.サービス単位数）>= 1の場合、
                if (beppyo.getTensu() != null && Math.abs(beppyo.getTensu()) >= 1) {
                    // 【返却】.帳票用データ.別表明細情報.単位数 = 【変数】.利用票別表.サービス単位数
                    retBeppyo.setTensu(CommonDtoUtil.objValToString(beppyo.getTensu()));
                }

                // 【返却】.帳票用データ.別表明細情報.率 = 【変数】.利用票別表.割引率
                retBeppyo.setWariRitu(CommonDtoUtil.objValToString(beppyo.getWaribikiRitu()));
                // 【返却】.帳票用データ.別表明細情報.単位数 = 【変数】.利用票別表.割引後単位数
                retBeppyo.setTensu(CommonDtoUtil.objValToString(beppyo.getWaribikiTen()));
                // 【返却】.帳票用データ.別表明細情報.回数 = 【変数】.利用票別表.回数
                retBeppyo.setKaisu(CommonDtoUtil.objValToString(beppyo.getKaisu()));

                // 【変数】.合計フラグ = 【変数】.利用票別表.合計フラグ
                Integer totalF = beppyo.getTotalF();
                // 【変数】.加算フラグ = 【変数】.利用票別表.加算フラグ
                Integer kasanF = beppyo.getKasanF();

                // ( 【変数】.合計フラグ = 1 ) and ( ( 【変数】.加算フラグ = 1 ) or ( 【変数】.サービス項目ID = 0) ) and (
                // 【変数】.利用票別表.合計単位数 <> 0) の場合、
                if ((totalF == CommonConstants.GOUKEI_FLG_1)
                        && ((kasanF == CommonConstants.KASAN_FLG_1_INT) || (svItemCd == 0))
                        && (beppyo.getSvTensu() != 0)) {
                    // 【返却】.帳票用データ.別表明細情報.サービス単位／金額 ＝ "(" + 【変数】.利用票別表.合計単位数 + ")"
                    retBeppyo.setSvTensu(
                            CommonConstants.LEFT_PARENTHESIS + beppyo.getSvTensu() + CommonConstants.RIGHT_PARENTHESIS);
                } else {
                    // 上記以外の場合
                    // 【返却】.帳票用データ.別表明細情報.サービス単位／金額 ＝ 【変数】.利用票別表.合計単位数
                    retBeppyo.setSvTensu(CommonDtoUtil.objValToString(beppyo.getSvTensu()));
                }

                // ( 【変数】.合計フラグ = 1 ) and ( ( 【変数】.加算フラグ = 1 ) or ( 【変数】.サービス項目ID = 0) ) and (
                // 【変数】.利用票別表.KyufukanriTen <> 0) の場合、
                if ((totalF == CommonConstants.GOUKEI_FLG_1)
                        && ((kasanF == CommonConstants.KASAN_FLG_1_INT) || (svItemCd == CommonConstants.NUMBER_0))
                        && (CommonConstants.STR_0.equals(CommonDtoUtil.objValToString(beppyo.getKyufukanriTen())))) {
                    // 【返却】.帳票用データ.別表明細情報.給付管理単位数 ＝ "(" + 【変数】.利用票別表.KyufukanriTen + ")"
                    retBeppyo.setKyufukanriTen(CommonConstants.LEFT_PARENTHESIS + beppyo.getKyufukanriTen()
                            + CommonConstants.RIGHT_PARENTHESIS);
                } else {
                    // 上記以外の場合
                    // 【返却】.帳票用データ.別表明細情報.給付管理単位数 ＝ 【変数】.利用票別表.KyufukanriTen
                    retBeppyo.setKyufukanriTen(CommonDtoUtil.objValToString(beppyo.getKyufukanriTen()));
                }

                // ( 【変数】.合計フラグ = 1 ) and ( 【変数】.加算フラグ = 0 ) and ( 【変数】.利用票別表.種類限度外単位数 <> 0 )
                // の場合、
                if ((totalF == CommonConstants.GOUKEI_FLG_1) && (kasanF == CommonConstants.KASAN_FLG_NORMAL)
                        && (beppyo.getSTensuOver() != 0)) {
                    // 【返却】.帳票用データ.別表明細情報.種類支給限度基準を超える単位数 ＝ "(" + 【変数】.利用票別表.種類限度外単位数 + ")"
                    retBeppyo.setStensuOver(CommonConstants.LEFT_PARENTHESIS + beppyo.getSTensuOver()
                            + CommonConstants.RIGHT_PARENTHESIS);
                } else {
                    // 上記以外の場合
                    // 【返却】.帳票用データ.別表明細情報.種類支給限度基準を超える単位数 ＝ 【変数】.利用票別表.種類限度外単位数
                    retBeppyo.setStensuOver(CommonDtoUtil.objValToString(beppyo.getSTensuOver()));
                }

                // ( 【変数】.合計フラグ = 1 ) and ( 【変数】.加算フラグ = 0 ) and ( 【変数】.利用票別表.種類限度額内単位数 <> 0 )
                // の場合、
                if ((totalF == CommonConstants.GOUKEI_FLG_1) && (kasanF == CommonConstants.KASAN_FLG_NORMAL)
                        && (beppyo.getSTensu() != 0)) {
                    // 【返却】.帳票用データ.別表明細情報.種類支給限度基準内単位数 ＝ "(" + 【変数】.利用票別表.種類限度額内単位数 + ")"
                    retBeppyo.setStensu(
                            CommonConstants.LEFT_PARENTHESIS + beppyo.getSTensu() + CommonConstants.RIGHT_PARENTHESIS);
                } else {
                    // 上記以外の場合
                    // 【返却】.帳票用データ.別表明細情報.種類支給限度基準内単位数 ＝ 【変数】.利用票別表.種類限度額内単位数
                    retBeppyo.setStensu(CommonDtoUtil.objValToString(beppyo.getSTensu()));
                }

                // ( (( 【変数】.合計フラグ = 1 ) and ( 【変数】.加算フラグ = 1 )) OR ((【変数】.総合事業区分 > 0) AND
                // (【変数】.合計フラグ = 0)) ) and ( 【変数】.利用票別表.区分支給限度外単位数 <> 0 )の場合、
                if ((((totalF == CommonConstants.GOUKEI_FLG_1) && (kasanF == CommonConstants.KASAN_FLG_1_INT))
                        || ((sougouSvkbn > CommonConstants.NUMBER_0) && (totalF == CommonConstants.GOUKEI_FLG_0)))
                        && (beppyo.getKTensuOver() != 0)) {
                    // 【返却】.帳票用データ.別表明細情報.区分支給限度基準を超える単位数 ＝ "(" + 【変数】.利用票別表.区分支給限度外単位数 + ")"
                    retBeppyo.setKtensuOver(CommonConstants.LEFT_PARENTHESIS + beppyo.getKTensuOver()
                            + CommonConstants.RIGHT_PARENTHESIS);
                } else {
                    // 上記以外の場合
                    // 【返却】.帳票用データ.別表明細情報.区分支給限度基準を超える単位数 ＝ 【変数】.利用票別表.区分支給限度外単位数
                    retBeppyo.setKtensuOver(CommonDtoUtil.objValToString(beppyo.getKTensuOver()));
                }

                // ( (( 【変数】.合計フラグ = 1 ) and ( 【変数】.加算フラグ = 1 )) OR ((【変数】.総合事業区分 > 0) AND
                // (【変数】.合計フラグ = 0)) ) and ( 【変数】.利用票別表.区分支給限度内単位数 <> 0 )の場合、
                if ((((totalF == CommonConstants.GOUKEI_FLG_1) && (kasanF == CommonConstants.KASAN_FLG_1_INT))
                        || ((sougouSvkbn > CommonConstants.NUMBER_0) && (totalF == CommonConstants.GOUKEI_FLG_0)))
                        && (beppyo.getKTensu() != 0)) {
                    // 【返却】.帳票用データ.別表明細情報.区分支給限度基準内単位数 ＝ "(" + 【変数】.利用票別表.区分支給限度内単位数 + ")"
                    retBeppyo.setKtensu(
                            CommonConstants.LEFT_PARENTHESIS + beppyo.getKTensu() + CommonConstants.RIGHT_PARENTHESIS);
                } else {
                    // 上記以外の場合
                    // 【返却】.帳票用データ.別表明細情報.区分支給限度基準内単位数 ＝ 【変数】.利用票別表.区分支給限度内単位数
                    retBeppyo.setKtensu(CommonDtoUtil.objValToString(beppyo.getKTensu()));
                }

                // ( 【変数】.合計フラグ = 1 ) and ( 【変数】.加算フラグ = 0 ) and ( 【変数】.30日超過フラグ = 0 ) の場合、
                if ((totalF == CommonConstants.GOUKEI_FLG_1) && (kasanF == CommonConstants.KASAN_FLG_NORMAL)
                        && (ov30Fl == CommonConstants.OVER_30DAY_FLG_NORMAL)) {
                    // 【返却】.帳票用データ.サービス単位／金額合計 += 【返却】.帳票用データ.別表明細情報.サービス単位／金額
                    retBeppyo.setHideSvTen(getValueAdd(retBeppyo.getHideSvTen(), retBeppyo.getSvTensu()));
                    // 【返却】.帳票用データ.給付管理単位数合計 += 【返却】.帳票用データ.別表明細情報.給付管理単位数
                    retBeppyo.setHideKyufukanriTen(
                            getValueAdd(retBeppyo.getHideKyufukanriTen(), retBeppyo.getKyufukanriTen()));
                    // 【返却】.帳票用データ.種類支給限度基準を超える単位数合計 += 【返却】.帳票用データ.別表明細情報.種類支給限度基準を超える単位数
                    retBeppyo.setHideSTenOvr(getValueAdd(retBeppyo.getHideSTenOvr(), retBeppyo.getStensuOver()));
                    // 【返却】.帳票用データ.種類支給限度基準内単位数合計 += 【返却】.帳票用データ.別表明細情報.種類支給限度基準内単位数
                    retBeppyo.setHideSTen(getValueAdd(retBeppyo.getHideSTen(), retBeppyo.getStensu()));
                    // 【返却】.帳票用データ.区分支給限度基準を超える単位数合計 += 【返却】.帳票用データ.別表明細情報.区分支給限度基準を超える単位数
                    retBeppyo.setHideKtenOvr(getValueAdd(retBeppyo.getHideKtenOvr(), retBeppyo.getKtensuOver()));
                    // 【返却】.帳票用データ.区分支給限度基準内単位数合計 += 【返却】.帳票用データ.別表明細情報.区分支給限度基準内単位数
                    retBeppyo.setHideKTen(getValueAdd(retBeppyo.getHideKTen(), retBeppyo.getKtensu()));
                }

                // 【返却】.帳票用データ.別表明細情報.単位数単価 ＝ 【変数】.利用票別表.単位数単価
                retBeppyo.setTanka(CommonDtoUtil.objValToString(beppyo.getTanka()));

                // (【変数】.合計フラグ = 0) AND (【変数】.総合事業区分 = 1) AND (【変数】.給付率差異フラグ = 1 OR
                // 【変数】.給付率差異フラグ = 3) and ( 【変数】.利用票別表.費用総額 <> 0)
                if ((totalF == CommonConstants.GOUKEI_FLG_0) && (sougouSvkbn == CommonConstants.NUMBER_1)
                        && (kyufuDiffF == CommonConstants.NUMBER_1 || kyufuDiffF == CommonConstants.NUMBER_3)
                        && (beppyo.getHiyouSougaku() != 0)) {
                    // 【返却】.帳票用データ.別表明細情報.費用総額保険/事業対象分 ＝ "(" + 【変数】.利用票別表.費用総額 + ")"
                    retBeppyo.setHiyouSougaku(CommonConstants.LEFT_PARENTHESIS + beppyo.getHiyouSougaku()
                            + CommonConstants.RIGHT_PARENTHESIS);
                } else {
                    // 上記以外の場合
                    // 【返却】.帳票用データ.別表明細情報.費用総額保険/事業対象分 ＝ 【変数】.利用票別表.費用総額
                    retBeppyo.setHiyouSougaku(CommonDtoUtil.objValToString(beppyo.getHiyouSougaku()));
                    // 【返却】.帳票用データ.費用総額保険/事業対象分合計 += 【返却】.帳票用データ.別表明細情報.費用総額保険/事業対象分
                    retBeppyo.setHideHiyouSougaku(
                            getValueAdd(retBeppyo.getHideHiyouSougaku(), retBeppyo.getHiyouSougaku()));
                }

                // (((【変数】.合計フラグ = 0) AND (【変数】.総合事業区分 = 0) ) OR ((【変数】.合計フラグ = 0) AND
                // (【変数】.総合事業区分 = 1) AND (【変数】.給付率差異フラグ = 0 OR 【変数】.給付率差異フラグ = 2) ) OR
                // ((【変数】.合計フラグ = 1) AND (【変数】.総合事業区分 = 1) AND (【変数】.給付率差異フラグ = 1)) OR
                // (【変数】.総合事業区分 = 2) ) の場合
                if ((((totalF == CommonConstants.GOUKEI_FLG_0) && (sougouSvkbn == CommonConstants.NUMBER_0))
                        || ((totalF == CommonConstants.GOUKEI_FLG_0) && (sougouSvkbn == CommonConstants.NUMBER_1)
                                && (kyufuDiffF == CommonConstants.NUMBER_0 || kyufuDiffF == CommonConstants.NUMBER_2))
                        || ((totalF == CommonConstants.GOUKEI_FLG_1) && (sougouSvkbn == CommonConstants.NUMBER_1)
                                && (kyufuDiffF == CommonConstants.NUMBER_1))
                        || (sougouSvkbn == CommonConstants.NUMBER_2))) {
                    // 処理なし
                } else {
                    // 上記以外の場合
                    // 【返却】.帳票用データ.別表明細情報.給付率 ＝ 【変数】.利用票別表.給付率
                    retBeppyo.setKyufuRitu(CommonDtoUtil.objValToString(beppyo.getKyufuRitu()));
                }

                //  (【変数】.合計フラグ = 0) AND (【変数】.総合事業区分 = 1) AND (【変数】.給付率差異フラグ = 1 OR
                // 【変数】.給付率差異フラグ = 3) and ( 【変数】.利用票別表.介護保険給付額 <> 0) の場合
                if ((totalF == CommonConstants.GOUKEI_FLG_0) && (sougouSvkbn == CommonConstants.NUMBER_1)
                        && (kyufuDiffF == CommonConstants.NUMBER_1 || kyufuDiffF == CommonConstants.NUMBER_3)
                        && (beppyo.getHKyufugaku() != 0)) {
                    // 【返却】.帳票用データ.別表明細情報.保険/事業費請求額 ＝ "(" + 【変数】.利用票別表.介護保険給付額 + ")"
                    retBeppyo.setHkyufugaku(CommonConstants.LEFT_PARENTHESIS + beppyo.getHKyufugaku()
                            + CommonConstants.RIGHT_PARENTHESIS);
                } else {
                    // 上記以外の場合
                    // 【返却】.帳票用データ.別表明細情報.保険/事業費請求額 ＝ 【変数】.利用票別表.介護保険給付額
                    retBeppyo.setHkyufugaku(CommonDtoUtil.objValToString(beppyo.getHKyufugaku()));
                    // 【返却】.帳票用データ.保険/事業費請求額合計 += 【返却】.帳票用データ.別表明細情報.保険/事業費請求額
                    retBeppyo.setHideHKyufugaku(getValueAdd(retBeppyo.getHideHKyufugaku(), retBeppyo.getHkyufugaku()));
                }

                // 【返却】.帳票用データ.別表明細情報.定額利用者負担単価金額 ＝ 【変数】.利用票別表.単定額利用者負担単価金額
                retBeppyo.setThutanTanka(CommonDtoUtil.objValToString(beppyo.getTHutanTanka()));

                // ((【変数】.合計フラグ = 0) AND (【変数】.総合事業区分 = 2)) and ( 【変数】.利用票別表.利用者負担額保険給付対象分 <>
                // 0)の場合
                if (((totalF == CommonConstants.GOUKEI_FLG_0) && (sougouSvkbn == CommonConstants.NUMBER_2))
                        && (beppyo.getHutanH() != 0)) {
                    // 【返却】.帳票用データ.別表明細情報.利用者負担保険／事業対象分 ＝ "(" + 【変数】.利用票別表.利用者負担額保険給付対象分 + ")"
                    retBeppyo.setHutanH(
                            CommonConstants.LEFT_PARENTHESIS + beppyo.getHutanH() + CommonConstants.RIGHT_PARENTHESIS);
                } else {
                    // 上記以外の場合
                    // 【返却】.帳票用データ.別表明細情報.利用者負担保険／事業対象分 ＝ 【変数】.利用票別表.利用者負担額保険給付対象分
                    retBeppyo.setHutanH(CommonDtoUtil.objValToString(beppyo.getHutanH()));
                    // 【返却】.帳票用データ.利用者負担保険／事業対象分合計 += 【返却】.帳票用データ.別表明細情報.利用者負担保険／事業対象分
                    retBeppyo.setHideHutanH(getValueAdd(retBeppyo.getHideHutanH(), retBeppyo.getHutanH()));
                }

                // 【返却】.帳票用データ.別表明細情報.利用者負担(全額 負担分) ＝ 【変数】.利用票別表.利用者負担額全額自己負担分
                retBeppyo.setHutanJ(CommonDtoUtil.objValToString(beppyo.getHutanJ()));
                // 【返却】.帳票用データ.利用者負担(全額 負担分)合計 += 【返却】.帳票用データ.別表明細情報.利用者負担(全額 負担分)
                retBeppyo.setHutanJGoukei(getValueAdd(retBeppyo.getHutanJGoukei(), retBeppyo.getHutanJ()));

                retBeppyoList.add(retBeppyo);
            }
        }

        // 6.5.3.種類別支給限度管理の値を設定する
        KghTrsGPlanOutGendoByCriteriaInEntity kghTrsGPlanOutGendoByCriteriaInEntity = new KghTrsGPlanOutGendoByCriteriaInEntity();
        // 支援事業者ID
        kghTrsGPlanOutGendoByCriteriaInEntity.setAlShien(CommonDtoUtil.strValToInt(shienid));
        // 利用者ID
        kghTrsGPlanOutGendoByCriteriaInEntity.setAlUserid(CommonDtoUtil.strValToInt(user.getUserId()));
        // サービス提供年月
        kghTrsGPlanOutGendoByCriteriaInEntity.setAsYymm(teikyuYm);
        // サービス提供年月（変更日）
        kghTrsGPlanOutGendoByCriteriaInEntity.setAsDd(user.getYymmd());

        // 【変数】.種類別限度管理＝上記結果
        List<KghTrsGPlanOutGendoOutEntity> shubetuGendoList = planBetu2SelectMapper
                .findKghTrsGPlanOutGendoByCriteria(kghTrsGPlanOutGendoByCriteriaInEntity);

        if (CollectionUtils.isNotEmpty(shubetuGendoList)) {
            // 種類情報
            List<V00231ServiceUseAnnexedTableSyurui> syuruiList = new ArrayList<V00231ServiceUseAnnexedTableSyurui>();
            // 短期入所利用日数情報のリスト
            List<V00231ServiceUseAnnexedTableShortDateSum> shortDateSumList = new ArrayList<V00231ServiceUseAnnexedTableShortDateSum>();

            // hshuOver1~hshuOver12の合計
            Double hshuOverSum = Double.valueOf(0);

            for (KghTrsGPlanOutGendoOutEntity gendo : shubetuGendoList) {
                // 【変数】.種類別限度管理から【返却】.帳票用データ.種類情報の値を設定する
                V00231ServiceUseAnnexedTableSyurui syurui = new V00231ServiceUseAnnexedTableSyurui();
                // サービス種類 svtype1~svtype12
                syurui.setSvtype1(gendo.getSvName1());
                syurui.setSvtype2(gendo.getSvName2());
                syurui.setSvtype3(gendo.getSvName3());
                syurui.setSvtype4(gendo.getSvName4());
                syurui.setSvtype5(gendo.getSvName5());
                syurui.setSvtype6(gendo.getSvName6());
                syurui.setSvtype7(gendo.getSvName7());
                syurui.setSvtype8(gendo.getSvName8());
                syurui.setSvtype9(gendo.getSvName9());
                syurui.setSvtype10(gendo.getSvName10());
                syurui.setSvtype11(gendo.getSvName11());
                syurui.setSvtype12(gendo.getSvName12());

                // 種類支給限度基準額(単位) hshuKijun1~hshuKijun12
                syurui.setHshuKijun1(CommonDtoUtil.objValToString(gendo.getSvGendo1()));
                syurui.setHshuKijun2(CommonDtoUtil.objValToString(gendo.getSvGendo2()));
                syurui.setHshuKijun3(CommonDtoUtil.objValToString(gendo.getSvGendo3()));
                syurui.setHshuKijun4(CommonDtoUtil.objValToString(gendo.getSvGendo4()));
                syurui.setHshuKijun5(CommonDtoUtil.objValToString(gendo.getSvGendo5()));
                syurui.setHshuKijun6(CommonDtoUtil.objValToString(gendo.getSvGendo6()));
                syurui.setHshuKijun7(CommonDtoUtil.objValToString(gendo.getSvGendo7()));
                syurui.setHshuKijun8(CommonDtoUtil.objValToString(gendo.getSvGendo8()));
                syurui.setHshuKijun9(CommonDtoUtil.objValToString(gendo.getSvGendo9()));
                syurui.setHshuKijun10(CommonDtoUtil.objValToString(gendo.getSvGendo10()));
                syurui.setHshuKijun11(CommonDtoUtil.objValToString(gendo.getSvGendo11()));
                syurui.setHshuKijun12(CommonDtoUtil.objValToString(gendo.getSvGendo12()));

                // 合計単位数 tani1~tani12
                syurui.setTani1(CommonDtoUtil.objValToString(gendo.getSvTotal1()));
                syurui.setTani2(CommonDtoUtil.objValToString(gendo.getSvTotal2()));
                syurui.setTani3(CommonDtoUtil.objValToString(gendo.getSvTotal3()));
                syurui.setTani4(CommonDtoUtil.objValToString(gendo.getSvTotal4()));
                syurui.setTani5(CommonDtoUtil.objValToString(gendo.getSvTotal5()));
                syurui.setTani6(CommonDtoUtil.objValToString(gendo.getSvTotal6()));
                syurui.setTani7(CommonDtoUtil.objValToString(gendo.getSvTotal7()));
                syurui.setTani8(CommonDtoUtil.objValToString(gendo.getSvTotal8()));
                syurui.setTani9(CommonDtoUtil.objValToString(gendo.getSvTotal9()));
                syurui.setTani10(CommonDtoUtil.objValToString(gendo.getSvTotal10()));
                syurui.setTani11(CommonDtoUtil.objValToString(gendo.getSvTotal11()));
                syurui.setTani12(CommonDtoUtil.objValToString(gendo.getSvTotal12()));

                // 種類支給限度基準を超える単位数 hshuOver1~hshuOver12
                syurui.setHshuOver1(CommonDtoUtil.objValToString(gendo.getSvOver1()));
                hshuOverSum += this.nullToZero(gendo.getSvOver1());
                syurui.setHshuOver2(CommonDtoUtil.objValToString(gendo.getSvOver2()));
                hshuOverSum += this.nullToZero(gendo.getSvOver2());
                syurui.setHshuOver3(CommonDtoUtil.objValToString(gendo.getSvOver3()));
                hshuOverSum += this.nullToZero(gendo.getSvOver3());
                syurui.setHshuOver4(CommonDtoUtil.objValToString(gendo.getSvOver4()));
                hshuOverSum += this.nullToZero(gendo.getSvOver4());
                syurui.setHshuOver5(CommonDtoUtil.objValToString(gendo.getSvOver5()));
                hshuOverSum += this.nullToZero(gendo.getSvOver5());
                syurui.setHshuOver6(CommonDtoUtil.objValToString(gendo.getSvOver6()));
                hshuOverSum += this.nullToZero(gendo.getSvOver6());
                syurui.setHshuOver7(CommonDtoUtil.objValToString(gendo.getSvOver7()));
                hshuOverSum += this.nullToZero(gendo.getSvOver7());
                syurui.setHshuOver8(CommonDtoUtil.objValToString(gendo.getSvOver8()));
                hshuOverSum += this.nullToZero(gendo.getSvOver8());
                syurui.setHshuOver9(CommonDtoUtil.objValToString(gendo.getSvOver9()));
                hshuOverSum += this.nullToZero(gendo.getSvOver9());
                syurui.setHshuOver10(CommonDtoUtil.objValToString(gendo.getSvOver10()));
                hshuOverSum += this.nullToZero(gendo.getSvOver10());
                syurui.setHshuOver11(CommonDtoUtil.objValToString(gendo.getSvOver11()));
                hshuOverSum += this.nullToZero(gendo.getSvOver11());
                syurui.setHshuOver12(CommonDtoUtil.objValToString(gendo.getSvOver12()));
                hshuOverSum += this.nullToZero(gendo.getSvOver12());

                syuruiList.add(syurui);

                // 短期入所利用日数情報
                V00231ServiceUseAnnexedTableShortDateSum shortDateSum = new V00231ServiceUseAnnexedTableShortDateSum();

                // 【返却】.帳票用データ.前月までの利用日数 = 【変数】.種類別限度管理.短期入所 前月までの利用日数
                shortDateSum.setTzengetu(CommonDtoUtil.objValToString(gendo.getTZengetu()));
                // 【返却】.帳票用データ.当月の計画利用日数 = 【変数】.種類別限度管理.短期入所 当月の計画利用日数
                shortDateSum.setTtougetu(CommonDtoUtil.objValToString(gendo.getTTougetu()));
                // 【返却】.帳票用データ.累積利用日数 = 【変数】.種類別限度管理.短期入所 累積利用日数
                shortDateSum.setTruiseki(CommonDtoUtil.objValToString(gendo.getTRuiseki()));

                shortDateSumList.add(shortDateSum);

                // 【返却】.帳票用データ.備考欄内容 = 【変数】.種類別限度管理.備考欄（利用票）
                infoInDto.setBikoRi(gendo.getBikouRiyouKnj());
            }

            // 種類情報
            infoInDto.setSyuruiDataList(syuruiList);
            // 短期入所利用日数情報のリスト
            infoInDto.setShortDateSumDataList(shortDateSumList);

            // 【返却】.帳票用データ.種類支給限度基準を超える単位数の合計 =
            // 【返却】.帳票用データ.種類支給限度基準を超える単位数(hshuOver1~hshuOver12)の合計
            infoInDto.setHidehShuOver(CommonDtoUtil.objValToString(hshuOverSum));
        }

        // 6.5.4.公費適用事業所の値を設定する
        KghCmnBeppyo3KohiByCriteriaInEntity kghCmnBeppyo3KohiByCriteriaInEntity = new KghCmnBeppyo3KohiByCriteriaInEntity();
        // 支援事業者ID
        kghCmnBeppyo3KohiByCriteriaInEntity.setAlShienId(CommonDtoUtil.strValToInt(shienid));
        // 利用者ID
        kghCmnBeppyo3KohiByCriteriaInEntity.setAlUserid(CommonDtoUtil.strValToInt(user.getUserId()));
        // サービス提供年月
        kghCmnBeppyo3KohiByCriteriaInEntity.setAsYymmYm(teikyuYm);
        // サービス提供年月（変更日）
        kghCmnBeppyo3KohiByCriteriaInEntity.setAsYymmD(user.getYymmd());
        // 【変数】.公費適用事業所＝上記結果
        List<KghCmnBeppyo3KohiOutEntity> kohiList = cmnTucPlanKouhiFutanSelectMapper
                .findKghCmnBeppyo3KohiByCriteria(kghCmnBeppyo3KohiByCriteriaInEntity);

        if (CollectionUtils.isNotEmpty(kohiList)) {
            // 公費適用事業情報
            List<V00231ServiceUseAnnexedTableKohi> retKohiList = new ArrayList<V00231ServiceUseAnnexedTableKohi>();
            // 公費分負担額の合計
            BigDecimal futangakuSum = BigDecimal.ZERO;
            // 公費分本人負担額の合計
            BigDecimal honninFutanSum = BigDecimal.ZERO;

            for (KghCmnBeppyo3KohiOutEntity kohi : kohiList) {
                V00231ServiceUseAnnexedTableKohi retKohi = new V00231ServiceUseAnnexedTableKohi();

                // 【返却】.帳票用データ.公費適用事業情報.公費額 = 【変数】.公費適用事業所.公費分負担額
                retKohi.setFutanGaku(CommonDtoUtil.objValToString(kohi.getFutanGaku()));
                futangakuSum
                        .add(kohi.getFutanGaku() == null ? BigDecimal.ZERO : BigDecimal.valueOf(kohi.getFutanGaku()));
                // 【返却】.帳票用データ.公費適用事業情報.本人負担 = 【変数】.公費適用事業所.公費分本人負担額
                retKohi.setHonninFutanGaku(CommonDtoUtil.objValToString(kohi.getHonninFutanGaku()));
                honninFutanSum.add(kohi.getHonninFutanGaku() == null ? BigDecimal.ZERO
                        : BigDecimal.valueOf(kohi.getHonninFutanGaku()));

                // 事業所名(略称)
                String shienNameRyaku = kghCmn03gFunc01Logic.getShienNameRyaku(kohi.getSvJigyoId(),
                        kohi.getYymmYm() + CommonConstants.STR_DELIMITER + kohi.getYymmD());
                // 【返却】.帳票用データ.公費適用事業情報.公費適用事業所 = 上記結果.事業所名(略称) + "(" + 【変数】.公費適用事業所.サービス種類 +")"
                retKohi.setSvJigyoId(shienNameRyaku + CommonConstants.LEFT_PARENTHESIS + kohi.getSvtype()
                        + CommonConstants.RIGHT_PARENTHESIS);

                // 【返却】.帳票用データ.公費額合計 = 【変数】.公費適用事業所.公費分負担額の合計
                retKohi.setFutanGakuGouKei(CommonDtoUtil.objValToString(futangakuSum));
                // 【返却】.帳票用データ.本人負担合計 = 【変数】.公費適用事業所.公費分本人負担額の合計
                retKohi.setHonninFutanGouKei(CommonDtoUtil.objValToString(honninFutanSum));

                retKohiList.add(retKohi);
            }
        }

        // 6.5.5.適用事業所の値を設定する
        KghCmnPrnBetuS3RiyouByCriteriaInEntity kghCmnPrnBetuS3RiyouByCriteriaInEntity = new KghCmnPrnBetuS3RiyouByCriteriaInEntity();
        // 支援事業者ID
        kghCmnPrnBetuS3RiyouByCriteriaInEntity.setAlShienId(CommonDtoUtil.strValToInt(shienid));
        // 利用者ID
        kghCmnPrnBetuS3RiyouByCriteriaInEntity.setAlUserid(CommonDtoUtil.strValToInt(user.getUserId()));
        // サービス提供年月
        kghCmnPrnBetuS3RiyouByCriteriaInEntity.setAsYymmYm(teikyuYm);
        // サービス提供年月（変更日）
        kghCmnPrnBetuS3RiyouByCriteriaInEntity.setAsYymmD(user.getYymmd());
        // 【変数】.適用事業所＝上記結果
        List<KghCmnPrnBetuS3RiyouOutEntity> riyouList = cmnTucPlanGaiFutanSelectMapper
                .findKghCmnPrnBetuS3RiyouByCriteria(kghCmnPrnBetuS3RiyouByCriteriaInEntity);

        if (CollectionUtils.isNotEmpty(riyouList)) {
            // 適用事業所情報
            List<V00231ServiceUseAnnexedTableSvJigyo> retSvJigyoList = new ArrayList<V00231ServiceUseAnnexedTableSvJigyo>();
            // 金額の合計
            BigDecimal kingakuSum = BigDecimal.ZERO;

            for (KghCmnPrnBetuS3RiyouOutEntity riyou : riyouList) {
                V00231ServiceUseAnnexedTableSvJigyo retSvJigyo = new V00231ServiceUseAnnexedTableSvJigyo();

                // 【返却】.帳票用データ.適用事業所.利用料項目名 = 【変数】.適用事業所.項目名
                retSvJigyo.setSvNameKnjT(riyou.getSvNameKnj());
                // 【返却】.帳票用データ.適用事業所.単価 = 【変数】.適用事業所.単価
                retSvJigyo.setTankaT(CommonDtoUtil.objValToString(riyou.getTanka()));
                // 【返却】.帳票用データ.適用事業所.数 = 【変数】.適用事業所.回数
                retSvJigyo.setKaisuT(CommonDtoUtil.objValToString(riyou.getKaisu()));
                // 【返却】.帳票用データ.適用事業所.金額 = 【変数】.適用事業所.金額
                retSvJigyo.setKingakuT(CommonDtoUtil.objValToString(riyou.getKingaku()));
                kingakuSum.add(riyou.getKingaku() == null ? BigDecimal.ZERO : BigDecimal.valueOf(riyou.getKingaku()));

                String shienNameRyaku = kghCmn03gFunc01Logic.getShienNameRyaku(riyou.getSvJigyoId(),
                        riyou.getYymmYm() + CommonConstants.STR_DELIMITER + riyou.getYymmD());
                // 【返却】.帳票用データ.適用事業所.適用事業所 ＝ 上記結果
                retSvJigyo.setSvJigyoIdT(shienNameRyaku);

                // 【返却】.帳票用データ.利用料合計 = 【変数】.適用事業所.金額の合計
                retSvJigyo.setRiyouRyouGoukei(CommonDtoUtil.objValToString(kingakuSum));

                retSvJigyoList.add(retSvJigyo);
            }

            // 適用事業所情報
            infoInDto.setSvJigyoDataList(retSvJigyoList);
        }

        // 6.5.6.社福事業所の値を設定する
        KghCmnBeppyo3SyafukuByCriteriaInEntity kghCmnBeppyo3SyafukuByCriteriaInEntity = new KghCmnBeppyo3SyafukuByCriteriaInEntity();
        // 支援事業者ID
        kghCmnBeppyo3SyafukuByCriteriaInEntity.setAlShienId(CommonDtoUtil.strValToInt(shienid));
        // 利用者ID
        kghCmnBeppyo3SyafukuByCriteriaInEntity.setAlUserid(CommonDtoUtil.strValToInt(user.getUserId()));
        // サービス提供年月
        kghCmnBeppyo3SyafukuByCriteriaInEntity.setAsYymmYm(teikyuYm);
        // 提供年月（変更日）
        kghCmnBeppyo3SyafukuByCriteriaInEntity.setAsYymmD(user.getYymmd());
        // 【変数】.社福事業所＝上記結果
        List<KghCmnBeppyo3SyafukuOutEntity> syafukuList = cmnTucPlanSyafukuKeigenSelectMapper
                .findKghCmnBeppyo3SyafukuByCriteria(kghCmnBeppyo3SyafukuByCriteriaInEntity);

        if (CollectionUtils.isNotEmpty(syafukuList)) {
            // 社福事業所情報
            List<V00231ServiceUseAnnexedTableSyafuku> retSyafukuList = new ArrayList<V00231ServiceUseAnnexedTableSyafuku>();
            // 軽減額の合計
            BigDecimal keigengakuSum = BigDecimal.ZERO;

            for (KghCmnBeppyo3SyafukuOutEntity syafuku : syafukuList) {
                V00231ServiceUseAnnexedTableSyafuku retSyafuku = new V00231ServiceUseAnnexedTableSyafuku();

                // 【返却】.帳票用データ.社福事業所情報.対象額 = 【変数】.社福事業所.対象額
                retSyafuku.setTaishoGakuT(CommonDtoUtil.objValToString(syafuku.getTaishoGaku()));
                // 【返却】.帳票用データ.社福事業所情報.軽減率 = 【変数】.社福事業所.軽減率
                retSyafuku.setKeigenRituT(CommonDtoUtil.objValToString(syafuku.getKeigenRitu()));
                // 【返却】.帳票用データ.社福事業所情報.軽減額 = 【変数】.社福事業所.軽減額
                retSyafuku.setKeigenGakuT(CommonDtoUtil.objValToString(syafuku.getKeigenGaku()));
                keigengakuSum.add(syafuku.getKeigenGaku() == null ? BigDecimal.ZERO
                        : BigDecimal.valueOf(syafuku.getKeigenGaku()));
                // 【返却】.帳票用データ.社福事業所情報.ｻｰﾋﾞｽ種類 = 【変数】.社福事業所.サービス種類コード
                retSyafuku.setSvShuCdT(syafuku.getSvShuCd());

                String shienNameRyaku = kghCmn03gFunc01Logic.getShienNameRyaku(syafuku.getSvJigyoId(),
                        syafuku.getYymmYm() + CommonConstants.STR_DELIMITER + syafuku.getYymmD());
                // 【返却】.帳票用データ.社福事業所情報.社福事業所 ＝ 上記結果
                retSyafuku.setShafukuJigyoId(shienNameRyaku);

                retSyafukuList.add(retSyafuku);
            }

            // 社福事業所情報
            infoInDto.setSyafukuDataList(retSyafukuList);

            // 【返却】.帳票用データ.社福軽減合計 = 【変数】.社福事業所.軽減額の合計
            infoInDto.setShafukuGoukei(CommonDtoUtil.objValToString(keigengakuSum));
        }

        // 6. 行数設定
        // 引数.印刷オプション.利用票別表の行数
        Integer optionRowCnt = CommonDtoUtil.strValToInt(printOption.getRbRiCnt());

        // ・変数.取得した件数 = 帳票用データ.別表明細情報の件数
        Integer kensu = CollectionUtils.size(retBeppyoList);
        // ・変数.使用行数＝引数.印刷オプション.利用票別表の行数 + 9
        Integer useRowCnt = optionRowCnt + 9;
        // ・変数.余り = Mod( 変数.取得した件数 , 変数.使用行数 )
        Integer amari = kensu % useRowCnt;

        // 変数.行数追加件数
        Integer addRowCnt = 0;
        // ・変数.余り＞引数.印刷オプション.利用票別表の行数の場合、
        if (amari > optionRowCnt) {
            // ・変数.行数追加件数＝変数.余り
            addRowCnt = amari;
            // ・変数.使用行数＝変数.使用行数 + 5
            useRowCnt += 5;
        } else {
            // ・上記以外の場合
            // ・変数.使用行数＝引数.印刷オプション.利用票別表の行数
            useRowCnt = optionRowCnt;
            // ・変数.行数追加件数＝Mod( 変数.余り , 変数.使用行数)
            addRowCnt = amari % useRowCnt;
        }

        // ・変数.使用行数＝変数.使用行数 - 1
        useRowCnt -= 1;

        // ・変数.印刷オプション.2ページ目以降の空行設定（利用票別表）が1の場合
        if (CommonConstants.NUM_STR_1.equals(printOption.getCntRibe2over())) {
            // ・変数.取得した件数＞変数.使用行数の場合、
            if (kensu > useRowCnt) {
                // ・変数.取得した件数=0
                kensu = 0;
            }
        }

        // ・変数.行数追加件数 ＞0の場合、
        if (addRowCnt > 0) {
            // ・（変数.使用行数-変数.行数追加区分）件詳細情報を帳票用データ.別表明細情報に追加する
            if (CollectionUtils.size(retBeppyoList) >= useRowCnt - addRowCnt) {
                retBeppyoList = retBeppyoList.subList(0, useRowCnt - addRowCnt);
                infoInDto.setRiyouBeppyoDataList(retBeppyoList);
            }
        }

        return infoInDto;
    }

    /**
     * 加算処理
     * 
     * @param sumValue 加算対象1
     * @param addValue 加算対象2
     * @return 加算結果
     */
    private String getValueAdd(String sumValue, String addValue) {
        BigDecimal sum = StringUtils.isEmpty(sumValue) ? BigDecimal.ZERO : new BigDecimal(sumValue);
        BigDecimal add = StringUtils.isEmpty(addValue) ? BigDecimal.ZERO : new BigDecimal(addValue);

        sum = sum.add(add);

        return CommonDtoUtil.objValToString(sum);
    }

    /**
     * nullToZero
     * 
     * @param val
     * @return
     */
    private Double nullToZero(Double val) {
        return val == null ? Double.valueOf(0) : val;
    }

    /**
     * 帳票レイアウトファイル取得
     *
     * @param fwProps         FrameworkPropert。0ies
     * @param reportParameter PDF帳票パラメータ
     * @param inDto           入力データ
     * @param printOption     印刷オプション
     * @return 帳票レイアウトファイル
     * @throws Exception
     */
    public JasperReport getJasperReport(FrameworkProperties fwProps,
            ServiceUseAnnexedTableReportServiceInDto reportParameter, ServiceUseAnnexedTableReportParameterModel model,
            ReportCommonGetOptionDto printOption)
            throws Exception {

        // ・サービス提供年月が2021年4月以上の場合： V00231_serviceUseAnnexedTableReport(1).jrxml
        // ・以外の場合： V00231_serviceUseAnnexedTableReport(2).jrxml
        String fileName = CommonConstants.EMPTY_STRING;
        if (StringUtils.compare(model.getYymmYm(), CommonConstants.DMY_TIME_202104) > 0) {
            fileName = ReportConstants.JRXML_V00231_SERVICE_USE_ANNEXED_TABLE_REPORT_1;
        } else {
            fileName = ReportConstants.JRXML_V00231_SERVICE_USE_ANNEXED_TABLE_REPORT_2;
        }

        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(fwProps, fileName)));

        // コンパイル
        final JasperDesign design = JRXmlLoader.load(is);

        // 承認欄の取得処理
        // リクエストパラメータ.データ.初期設定マスタの情報.承認欄情報が「1:帳票毎保持する」の場合
        HashMap<String, Object> shonin = shoninSetReportService.getShoninSetReport(
                design, ReportConstants.INT_1,
                model.getSvJigyoId(), ReportConstants.SECTION_3GKV00231P004);
        JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        List<ServiceUseAnnexedTableReportServiceInDto> surveyContentsListInfoList = (List<ServiceUseAnnexedTableReportServiceInDto>) dataSource
                .getData();
        if (!surveyContentsListInfoList.isEmpty()) {
            surveyContentsListInfoList.get(0).setSubReportPath((String) shonin.get(ReportConstants.SUBREPORTPATH));
            surveyContentsListInfoList.get(0).setSubReportDataDs(
                    (JRBeanCollectionDataSource) shonin.get(ReportConstants.SUBREPORTDATADS));
        }

        JRBeanCollectionDataSource newDataSource = new JRBeanCollectionDataSource(surveyContentsListInfoList);
        reportParameter.setDataSource(newDataSource);

        // 5. 居宅介護支援事業者番号の表示制御を設定する
        JRBand header = design.getPageHeader();
        JRDesignStaticText staticTextLabel = (JRDesignStaticText) header.getElementByKey("shienIdTextFlg");
        JRDesignTextField textFieldId = (JRDesignTextField) header.getElementByKey("shienIdFlg");
        // 引き渡しパラメータ.印刷オプション.支援事業者番号（利用票） = 1 の場合、
        if (CommonConstants.NUM_STR_1.equals(printOption.getRbCmnNo())) {
            staticTextLabel.setPrintWhenExpression(new JRDesignExpression("false"));
            textFieldId.setPrintWhenExpression(new JRDesignExpression("false"));
        } else {
            // 上記以外の場合
            staticTextLabel.setPrintWhenExpression(new JRDesignExpression("true"));
            textFieldId.setPrintWhenExpression(new JRDesignExpression("true"));
        }
        return JasperCompileManager.compileReport(design);
    }
}
