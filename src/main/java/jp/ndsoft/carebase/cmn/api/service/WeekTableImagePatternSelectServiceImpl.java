package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.base.Objects;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00978Info;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00978KoumokuOut;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnWeekLogic;
import jp.ndsoft.carebase.cmn.api.logic.dto.TermId2DateLogicOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekTableImagePatternSelectInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekTableImagePatternSelectOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnSypDayWeekNaiyoInfo1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnSypDayWeekNaiyoInfo1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnSypWeekPtnLeH21ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnSypWeekPtnLeH21OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekSvShurui1ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnWeekSvShurui1OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.DummyHistoryByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.DummyHistoryOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSvselSelJigyoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnSvselSelJigyoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnSvselSelJigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoNameSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucSypNaiyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucSypWeekPtn1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucSypWeekPtn2SelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * GUI00978_［週間表イメージ］週間表パターン取込データ取得サービス.
 *
 * <AUTHOR>
 */
@Service
public class WeekTableImagePatternSelectServiceImpl
		extends SelectServiceImpl<WeekTableImagePatternSelectInDto, WeekTableImagePatternSelectOutDto> {
	/** ロガー */
	private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

	@Autowired
	private CpnMucSypWeekPtn1SelectMapper cpnMucSypWeekPtn1SelectMapper;

	@Autowired
	private ComMscSvjigyoNameSelectMapper comMscSvjigyoNameSelectMapper;

	@Autowired
	private CmnSvselSelJigyoSelectMapper cmnSvselSelJigyoSelectMapper;

	@Autowired
	private CpnMucSypNaiyoSelectMapper cpnMucSypNaiyoSelectMapper;

	@Autowired
	private CpnMucSypWeekPtn2SelectMapper cpnMucSypWeekPtn2SelectMapper;

	/** 共通関数「f_cpn_week_termid2date」 */
	@Autowired
	private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;

	@Autowired
	private KghKrkZCpnWeekLogic kghKrkZCpnWeekLogic;

	@Autowired
	private WeekTableImageDuplicateSelectServiceImpl weekTableImageDuplicateSelectServiceImpl;

	/**
	 * ［週間表イメージ］週間表計画期間変更
	 * 
	 * @param inDto ［週間表イメージ］週間表計画期間変更の入力DTO.
	 * @return ［週間表イメージ］週間表計画期間変更の出力Dto
	 * @throws Exception Exception
	 */
	@Override
	protected WeekTableImagePatternSelectOutDto mainProcess(WeekTableImagePatternSelectInDto inDto) throws Exception {
		LOG.info(Constants.START);

		WeekTableImagePatternSelectOutDto outDto = new WeekTableImagePatternSelectOutDto();
		List<Gui00978Info> week2List = new ArrayList<>();

		// 1.週間表（ヘッダ）履歴情報の検索処理を行う
		// 週間表（ヘッダ）履歴情報の取得
		DummyHistoryByCriteriaInEntity dummyHistoryByCriteriaInEntity = new DummyHistoryByCriteriaInEntity();
		dummyHistoryByCriteriaInEntity.setWeek1Id(CommonDtoUtil.strValToInt(inDto.getWeek1Id()));
		List<DummyHistoryOutEntity> dummyHistoryOutEntityList = cpnMucSypWeekPtn1SelectMapper
				.findDummyHistoryByCriteria(dummyHistoryByCriteriaInEntity);

		if (!CollectionUtils.isEmpty(dummyHistoryOutEntityList)) {
			outDto.setWIgaiKnj(dummyHistoryOutEntityList.get(0).getWIgaiKnj());
		}

		// 2.上記1.で取得した週間表（ヘッダ）履歴情報のデータ件数 > 0 件の場合、【詳細リスト】週間表詳細情報を取得する
		// 2.1 サービス種類名称を取得
		List<CpnWeekSvShurui1OutEntity> cpnWeekSvShurui1OutEntityList = comMscSvjigyoNameSelectMapper
				.findCpnWeekSvShurui1ByCriteria(new CpnWeekSvShurui1ByCriteriaInEntity());

		// 2.2 サービス事業名称、略称を取得
		List<KghCmnSvselSelJigyoOutEntity> kghCmnSvselSelJigyoOutEntityList = cmnSvselSelJigyoSelectMapper
				.findKghCmnSvselSelJigyoByCriteria(new KghCmnSvselSelJigyoByCriteriaInEntity());
		List<KghCmnSvselSelJigyoOutEntity> filterList = kghCmnSvselSelJigyoOutEntityList.stream()
				.filter(k -> !Arrays.asList(CommonConstants.FILTER_SV_SHURUI_CD).contains(k.getSvKindCd()))
				.collect(Collectors.toList());

		// 2.3 日課・週間内容マスタ情報を取得
		List<CpnSypDayWeekNaiyoInfo1OutEntity> cpnSypDayWeekNaiyoInfo1OutEntityList = cpnMucSypNaiyoSelectMapper
				.findCpnSypDayWeekNaiyoInfo1ByCriteria(new CpnSypDayWeekNaiyoInfo1ByCriteriaInEntity());

		// 2.4 詳細データの抽出
		CpnSypWeekPtnLeH21ByCriteriaInEntity cpnSypWeekPtnLeH21ByCriteriaInEntity = new CpnSypWeekPtnLeH21ByCriteriaInEntity();
		cpnSypWeekPtnLeH21ByCriteriaInEntity.setRireki(CommonDtoUtil.strValToInt(inDto.getWeek1Id()));
		List<CpnSypWeekPtnLeH21OutEntity> CpnSypWeekPtnLeH21OutEntityList = cpnMucSypWeekPtn2SelectMapper
				.findCpnSypWeekPtnLeH21ByCriteria(cpnSypWeekPtnLeH21ByCriteriaInEntity);

		if (CollectionUtils.isEmpty(CpnSypWeekPtnLeH21OutEntityList)) {
			return outDto;
		} else {
			CpnSypWeekPtnLeH21OutEntityList.forEach(c -> {
				Gui00978Info info = new Gui00978Info();
				// 詳細ID
				info.setWeek2Id(CommonDtoUtil.objValToString(c.getWeek2Id()));
				// 週間表ID
				info.setWeek1Id(CommonDtoUtil.objValToString(c.getWeek1Id()));
				// 曜日
				info.setYoubi(c.getYoubi());
				// 開始時間
				info.setKaishiJikan(c.getKaishiJikan());
				// 終了時間
				info.setShuuryouJikan(c.getShuuryouJikan());
				// 内容CD
				info.setNaiyoCd(CommonDtoUtil.objValToString(c.getNaiyoCd()));
				// 内容
				cpnSypDayWeekNaiyoInfo1OutEntityList.stream().filter(s -> Objects.equal(s.getNaiyoCd(), c.getNaiyoCd()))
						.findFirst().ifPresent(s -> info.setNaiyoKnj(s.getNaiyoKnj()));
				// メモ
				info.setMemoKnj(c.getMemoKnj());
				// 文字サイズ
				info.setFontSize(CommonDtoUtil.objValToString(c.getFontSize()));
				// 表示モード
				info.setDispMode(CommonDtoUtil.objValToString(c.getDispMode()));
				// 文字位置
				info.setAlignment(CommonDtoUtil.objValToString(c.getAlignment()));
				// サービス種類
				info.setSvShuruiCd(c.getSvShuruiCd());
				// サービス項目（台帳）
				info.setSvItemCd(CommonDtoUtil.objValToString(c.getSvItemCd()));
				// サービス事業者CD
				info.setSvJigyoId(CommonDtoUtil.objValToString(c.getSvJigyoId()));
				// サービス種類名称
				cpnWeekSvShurui1OutEntityList.stream()
						.filter(w -> StringUtils.equals(c.getSvShuruiCd(), w.getSvKindCd())).findFirst()
						.ifPresent(w -> info.setSvShuruiKnj(w.getRuakuKnj()));

				filterList.stream().filter(
						k -> StringUtils.equals(k.getSvJigyoCd(), CommonDtoUtil.objValToString(c.getSvJigyoId()))
								&& StringUtils.equals(k.getSvKindCd(), c.getSvShuruiCd()))
						.findFirst().ifPresent(k -> {
							// サービス事業者名称
							info.setSvJigyoKnj(k.getSvJigyoKnj());
							// サービス事業者略称
							info.setSvJigyoRks(k.getJigyoRyakuKnj());
						});

				// 文字カラー
				info.setFontColor(CommonDtoUtil.objValToString(c.getFontColor()));
				// 背景カラー
				info.setBackColor(CommonDtoUtil.objValToString(c.getBackColor()));
				// 時間表示区分
				info.setTimeKbn(CommonDtoUtil.objValToString(c.getTimeKbn()));
				// 週単位以外のサービス区分
				info.setIgaiKbn(CommonDtoUtil.objValToString(c.getIgaiKbn()));
				// 週単位以外のサービス（日付指定）
				info.setIgaiDate(c.getIgaiDate());
				// 週単位以外のサービス（曜日指定）
				info.setIgaiWeek(c.getIgaiWeek());
				// 更新区分
				info.setUpdateKbn(CommonConstants.UPDATE_KBN_C);

				week2List.add(info);
			});
		}

		week2List.forEach(w -> {
			// 3.1 サービス項目IDのチェック
			// ・「API定義書_APINo(217)_週間表複写データ取得.xlsx」の「関数_週間表サービス項目情報取得」シートをご参照
			String termidFr = CommonConstants.BLANK_STRING;
			if (!CollectionUtils.isEmpty(dummyHistoryOutEntityList)) {
				termidFr = CommonDtoUtil.objValToString(dummyHistoryOutEntityList.get(0).getTermid());
			}

			Gui00978KoumokuOut gui00978KoumokuOut = weekTableImageDuplicateSelectServiceImpl.getKoumokuCd(
					w.getSvJigyoId(), w.getSvItemCd(), w.getKaishiJikan(), w.getShuuryouJikan(), termidFr,
					inDto.getTermid(), CommonConstants.BLANK_STRING, inDto.getTougaiYm(), w.getSvShuruiCd(),
					CommonConstants.KASAN_FLG_0, null);

			w.setSvItemCd(gui00978KoumokuOut.getKoumokuCd());

			// 3.2 サービス項目名称を取得
			if (!CollectionUtils.isEmpty(dummyHistoryOutEntityList)) {
				TermId2DateLogicOutDto termId2DateLogicOutDto = kghKrkZCpnFuncLogic
						.getTermid2date(dummyHistoryOutEntityList.get(0).getTermid(), null, null);

				// 3.2.2 サービス項目名称を取得する
				w.setSvItemKnj(kghKrkZCpnWeekLogic.getName(CommonDtoUtil.strValToInt(w.getSvShuruiCd()),
						CommonDtoUtil.strValToInt(w.getSvItemCd()), termId2DateLogicOutDto.getSYmd(),
						termId2DateLogicOutDto.getEYmd()));

			}

			// 3.3 週以外、月日、曜日の文字転換
			if (!StringUtils.isEmpty(w.getIgaiKbn())) {
				// 4.1.1 返却情報.詳細リスト.曜日が”9999999”の場合
				if (CommonConstants.YOUBI_STR.equals(w.getYoubi())) {
					// ① 返却情報.詳細リスト.週単位以外のサービス区分が1の場合、下記共通関数「f_cpn_week_date_to_moji」を呼び出す
					if (CommonConstants.IGAI_KBN_STR_ONE.equals(w.getIgaiKbn())) {
						w.setIgaiMoji(kghKrkZCpnFuncLogic.getWeekDateToMoji(w.getIgaiDate()));
						// ② 返却情報.詳細リスト.週単位以外のサービス区分が2の場合、下記共通関数「f_cpn_week_week_to_moji」を呼び出す
					} else if (CommonConstants.IGAI_KBN_STR_TWO.equals(w.getIgaiKbn())) {
						w.setIgaiMoji(kghKrkZCpnFuncLogic.getWeekToMoji(w.getIgaiDate()));
					}
				}
			}
		});

		outDto.setWeek2List(week2List);
		LOG.info(Constants.END);
		return outDto;
	}

}
