package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui00971SokInsertInfoInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.R4sTucSok1InfoUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.R4sTucSok1InfoUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.CpnTucSypKkak1Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.R4sTucSok1Mapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucSypKkak1;
import jp.ndsoft.carebase.common.dao.mybatis.entity.CpnTucSypKkak1Criteria;
import jp.ndsoft.carebase.common.dao.mybatis.entity.R4sTucSok1;
import jp.ndsoft.carebase.common.dao.mybatis.entity.R4sTucSok1Criteria;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;

/**
 * @since 2025.05.09
 * <AUTHOR> Bui The Khang
 * @implNote GUI00971_総合計画_保存
 */
@Service
public class R4sTucSok1InfoUpdateServiceImpl
        extends UpdateServiceImpl<R4sTucSok1InfoUpdateServiceInDto, R4sTucSok1InfoUpdateServiceOutDto> {
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // @Autowired
    // private Numbering numbering;

    @Autowired
    private R4sTucSok1Mapper r4sTucSok1Mapper;

    @Autowired
    private CpnTucSypKkak1Mapper cpnTucSypKkak1Mapper;

    /**
     * 総合計画_保存
     * 
     * @param inDto R4sTucSok1InfoUpdateServiceInDto.
     * @return R4sTucSok1InfoUpdateServiceOutDto DTO
     * @throws Exception Exception
     */
    @Override
    protected R4sTucSok1InfoUpdateServiceOutDto mainProcess(R4sTucSok1InfoUpdateServiceInDto inDto) throws Exception {
        R4sTucSok1InfoUpdateServiceOutDto dto = new R4sTucSok1InfoUpdateServiceOutDto();
        int count = 0;
        LOG.info(Constants.START);

        // 1.単項目チェック以外の入力チェック
        for (Gui00971SokInsertInfoInDto infoInDto : inDto.getSokInsertInfo()) {
            // 2.リクエストパラメータ.操作メニューが1:新規の場合
            if (infoInDto.getOperationMenu().equals(CommonConstants.STR_1)) {
                // 2-1.DBの総合計画情報のレコードを新規作成する
                R4sTucSok1 r4sTucSok1 = new R4sTucSok1();

                // 計画期間ID
                r4sTucSok1.setSc1Id(CommonDtoUtil.strValToInt(infoInDto.getSc1Id()));
                // 法人ID
                r4sTucSok1.setHoujinId(CommonDtoUtil.strValToInt(infoInDto.getHoujinId()));
                // 施設ID
                r4sTucSok1.setShisetuId(CommonDtoUtil.strValToInt(infoInDto.getShisetuId()));
                // 事業者ID
                r4sTucSok1.setSvJigyoId(CommonDtoUtil.strValToInt(infoInDto.getSvJigyoId()));
                // 利用者ID
                r4sTucSok1.setUserid(CommonDtoUtil.strValToInt(infoInDto.getUserid()));
                // 作成日
                r4sTucSok1.setCreateYmd(infoInDto.getCreateYmd());
                // 作成者
                r4sTucSok1.setShokuId(CommonDtoUtil.strValToInt(infoInDto.getShokuId()));
                // 要介護度
                r4sTucSok1.setYokaiKbn(CommonDtoUtil.strValToInt(infoInDto.getYokaiKbn()));
                // 有効期間開始日
                r4sTucSok1.setNinStartYmd(infoInDto.getNinStartYmd());
                // 有効期間終了日
                r4sTucSok1.setNinEndYmd(infoInDto.getNinEndYmd());
                // 計画書式
                r4sTucSok1.setKaiteiFlg(CommonDtoUtil.strValToInt(infoInDto.getKaiteiFlg()));
                // 登録時の共通カラム値設定処理
                // CommonDaoUtil.setInsertCommonColumns(r4sTucSok1);
                // r4sTucSok1.setSok1Id(numbering.getNumber(AppUtil.getKeiyakushaId(),
                // CareBaseMComSeqMgrCodeConstants.SeqTargetTableCode.R4S_TUC_SOK1_SOK1_ID.getIntValue())
                // .intValue());

                // 1.R4 総合計画書テーブル（r4s_tuc_sok1）の登録詳細
                count = r4sTucSok1Mapper.insertSelectiveAndReturn(r4sTucSok1);

                // 2-2.DBの28-36 自立支援計画書（兼介護予防）特定施設サービス計画書のレコードを新規作成する
                CpnTucSypKkak1 cpnTucSypKkak1 = new CpnTucSypKkak1();

                // 総合計画ID
                cpnTucSypKkak1.setKkak1Id(r4sTucSok1.getSok1Id());
                // 計画期間ID
                cpnTucSypKkak1.setSc1Id(CommonDtoUtil.strValToInt(infoInDto.getSc1Id()));
                // 法人ID
                cpnTucSypKkak1.setHoujinId(CommonDtoUtil.strValToInt(infoInDto.getHoujinId()));
                // 施設ID
                cpnTucSypKkak1.setShisetuId(CommonDtoUtil.strValToInt(infoInDto.getShisetuId()));
                // 事業者ID
                cpnTucSypKkak1.setSvJigyoId(CommonDtoUtil.strValToInt(infoInDto.getSvJigyoId()));
                // 利用者ID
                cpnTucSypKkak1.setUserid(CommonDtoUtil.strValToInt(infoInDto.getUserid()));
                // 作成日
                cpnTucSypKkak1.setCreateYmd(infoInDto.getCreateYmd());
                // 作成者
                cpnTucSypKkak1.setShokuId(CommonDtoUtil.strValToInt(infoInDto.getShokuId()));
                // ケースNo.
                cpnTucSypKkak1.setCaseNo(infoInDto.getCaseNo());
                // 要介護度
                cpnTucSypKkak1.setYokaiKbn(CommonDtoUtil.strValToInt(infoInDto.getYokaiKbn()));
                // 有効期間開始日
                cpnTucSypKkak1.setNinStartYmd(infoInDto.getNinStartYmd());
                // 有効期間終了日
                cpnTucSypKkak1.setNinEndYmd(infoInDto.getNinEndYmd());
                // 入所者の希望・意向など勘案すべき事項
                cpnTucSypKkak1.setHoshin1Knj(infoInDto.getHoshin1Knj());
                // 実施機関の意向や判断などに関する事項
                cpnTucSypKkak1.setHoshin2Knj(infoInDto.getHoshin2Knj());
                // 援助に関する事項
                cpnTucSypKkak1.setHoshin3Knj(infoInDto.getHoshin3Knj());
                // 介護保険サービスに留意すべき事項
                cpnTucSypKkak1.setHoshin5Knj(infoInDto.getHoshin5Knj());
                // 総合的な援助の方針
                cpnTucSypKkak1.setSogoHoshinKnj(infoInDto.getSogoHoshinKnj());
                // 説明年月日
                cpnTucSypKkak1.setSetumeiYmd(infoInDto.getSetumeiYmd());
                // 計画書式
                cpnTucSypKkak1.setKaiteiFlg(CommonDtoUtil.strValToInt(infoInDto.getKaiteiFlg()));
                // 登録時の共通カラム値設定処理
                // CommonDaoUtil.setInsertCommonColumns(cpnTucSypKkak1);

                // 2.R4 28-36 自立支援計画書（兼介護予防）特定施設サービス計画書テーブル（cpn_tuc_syp_kkak1）の登録詳細
                count = cpnTucSypKkak1Mapper.insertSelective(cpnTucSypKkak1);
            }

            // 3. リクエストパラメータ.操作メニューが2:削除の場合
            if (infoInDto.getOperationMenu().equals(CommonConstants.STR_2)) {
                // 3-1.DBの総合計画情報のレコードを論理削除する
                // R4sTucSok1 r4sTucSok1 = new R4sTucSok1();
                // 削除（論理）時の共通カラム値設定処理
                // CommonDaoUtil.setDeleteCommonColumns(r4sTucSok1,
                // CommonDtoUtil.strValToBigInteger(infoInDto.getModifiedCnt()));

                R4sTucSok1Criteria r4sTucSok1Criteria = new R4sTucSok1Criteria();
                // 「r4s_tuc_sok1.del_flg」が"0"
                // 「リクエストパラメータ.総合計画ID」と「r4s_tuc_sok1.sok1_id」が完全一致
                // r4sTucSok1Criteria.createCriteria().andDelFlgEqualTo(CommonConstants.NUMBER_0)
                r4sTucSok1Criteria.createCriteria().andSok1IdEqualTo(CommonDtoUtil.strValToInt(infoInDto.getSok1Id()));

                // 3.R4 総合計画書テーブル（r4s_tuc_sok1）の更新詳細
                // int updateCnt =
                r4sTucSok1Mapper.deleteByCriteria(r4sTucSok1Criteria);
                // if (updateCnt <= 0) {
                // throw new ExclusiveException();
                // }

                // 3-2.DBの28-36 自立支援計画書（兼介護予防）特定施設サービス計画書のレコードを論理削除する
                // CpnTucSypKkak1 cpnTucSypKkak1 = new CpnTucSypKkak1();
                // 削除（論理）時の共通カラム値設定処理
                // CommonDaoUtil.setDeleteCommonColumns(cpnTucSypKkak1,
                // CommonDtoUtil.strValToBigInteger(infoInDto.getModifiedCnt()));

                CpnTucSypKkak1Criteria cpnTucSypKkak1Criteria = new CpnTucSypKkak1Criteria();
                // 「cpn_tuc_syp_kkak1.del_flg」が"0"
                // 「リクエストパラメータ.総合計画ID」と「cpn_tuc_syp_kkak1.kkak1_id」が完全一致
                // cpnTucSypKkak1Criteria.createCriteria().andDelFlgEqualTo(CommonConstants.NUMBER_0)
                cpnTucSypKkak1Criteria.createCriteria()
                        .andKkak1IdEqualTo(CommonDtoUtil.strValToInt(infoInDto.getSok1Id()));
                // 4.R4 28-36 自立支援計画書（兼介護予防）特定施設サービス計画書テーブル（cpn_tuc_syp_kkak1）の更新詳細
                count = cpnTucSypKkak1Mapper.deleteByCriteria(cpnTucSypKkak1Criteria);
                // if (count <= 0) {
                // throw new ExclusiveException();
                // }
            }

            // 4. リクエストパラメータ.操作メニューが1・2以外の場合、DBの自立支援計画書（兼介護予防）特定施設サービス計画書を更新する
            if (!infoInDto.getOperationMenu().equals(CommonConstants.STR_1)
                    && !infoInDto.getOperationMenu().equals(CommonConstants.STR_2)) {
                CpnTucSypKkak1 cpnTucSypKkak1 = new CpnTucSypKkak1();

                // 計画期間ID
                cpnTucSypKkak1.setSc1Id(CommonDtoUtil.strValToInt(infoInDto.getSc1Id()));
                // 法人ID
                cpnTucSypKkak1.setHoujinId(CommonDtoUtil.strValToInt(infoInDto.getHoujinId()));
                // 施設ID
                cpnTucSypKkak1.setShisetuId(CommonDtoUtil.strValToInt(infoInDto.getShisetuId()));
                // 事業者ID
                cpnTucSypKkak1.setSvJigyoId(CommonDtoUtil.strValToInt(infoInDto.getSvJigyoId()));
                // 利用者ID
                cpnTucSypKkak1.setUserid(CommonDtoUtil.strValToInt(infoInDto.getUserid()));
                // 作成日
                cpnTucSypKkak1.setCreateYmd(infoInDto.getCreateYmd());
                // 作成者
                cpnTucSypKkak1.setShokuId(CommonDtoUtil.strValToInt(infoInDto.getShokuId()));
                // 初回作成日
                cpnTucSypKkak1.setShokaiYmd(infoInDto.getSyokaiYmd());
                // ケースNo.
                cpnTucSypKkak1.setCaseNo(infoInDto.getCaseNo());
                // 要介護度
                cpnTucSypKkak1.setYokaiKbn(CommonDtoUtil.strValToInt(infoInDto.getYokaiKbn()));
                // 有効期間開始日
                cpnTucSypKkak1.setNinStartYmd(infoInDto.getNinStartYmd());
                // 有効期間終了日
                cpnTucSypKkak1.setNinEndYmd(infoInDto.getNinEndYmd());
                // 入所者の希望・意向など勘案すべき事項
                cpnTucSypKkak1.setHoshin1Knj(infoInDto.getHoshin1Knj());
                // 実施機関の意向や判断などに関する事項
                cpnTucSypKkak1.setHoshin2Knj(infoInDto.getHoshin2Knj());
                // 援助に関する事項
                cpnTucSypKkak1.setHoshin3Knj(infoInDto.getHoshin3Knj());
                // 介護保険サービスに留意すべき事項
                cpnTucSypKkak1.setHoshin5Knj(infoInDto.getHoshin5Knj());
                // 総合的な援助の方針
                cpnTucSypKkak1.setSogoHoshinKnj(infoInDto.getSogoHoshinKnj());
                // 説明年月日
                cpnTucSypKkak1.setSetumeiYmd(infoInDto.getSetumeiYmd());
                // 計画書式
                cpnTucSypKkak1.setKaiteiFlg(CommonDtoUtil.strValToInt(infoInDto.getKaiteiFlg()));
                // 更新時の共通カラム値設定処理
                // CommonDaoUtil.setUpdateCommonColumns(cpnTucSypKkak1,
                // CommonDtoUtil.strValToBigInteger(infoInDto.getModifiedCnt()));

                CpnTucSypKkak1Criteria cpnTucSypKkak1Criteria = new CpnTucSypKkak1Criteria();
                // 「cpn_tuc_syp_kkak1.del_flg」が"0"
                // 「リクエストパラメータ.総合計画ID」と「cpn_tuc_syp_kkak1.kkak1_id」が完全一致
                // cpnTucSypKkak1Criteria.createCriteria().andDelFlgEqualTo(CommonConstants.NUMBER_0)
                cpnTucSypKkak1Criteria.createCriteria()
                        .andKkak1IdEqualTo(CommonDtoUtil.strValToInt(infoInDto.getSok1Id()));
                // 4.R4 28-36 自立支援計画書（兼介護予防）特定施設サービス計画書テーブル（cpn_tuc_syp_kkak1）の更新詳細
                count = cpnTucSypKkak1Mapper.updateByCriteriaSelective(cpnTucSypKkak1, cpnTucSypKkak1Criteria);
                // if (count <= 0) {
                // throw new ExclusiveException();
                // }
            }

        }
        // 5. 上記処理で取得した結果レスポンスを返却する。
        dto.setStatus(count <= 0 ? CommonConstants.FAILURE : CommonConstants.SUCCESS);
        LOG.info(Constants.END);

        return dto;
    }

}
