package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.AssessmentInterRAILogic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkNokikanOpeLogic;
import jp.ndsoft.carebase.cmn.api.logic.KghSmglHistoryLogic;
import jp.ndsoft.carebase.cmn.api.logic.dto.SmglYmdInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentInterRAIUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.AssessmentInterRAIUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucRaiRrkInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucRaiRrkInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucRaiRrkSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;

/**
 * @since 2025.04.17
 * <AUTHOR>
 * @apiNote GUI00771_アセスメント(インターライ)画面H データ保存
 */
@Service
public class AssessmentInterRAIHUpdateServiceImpl
        extends UpdateServiceImpl<AssessmentInterRAIUpdateServiceInDto, AssessmentInterRAIUpdateServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private KghSmglHistoryLogic kghSmglHistoryLogic;

    @Autowired
    private PlatformTransactionManager transactionManager;

    /** アセスメント(インターライ)画面のロジッククラス */
    @Autowired
    private AssessmentInterRAILogic assessmentInterRAILogic;

    /** インターライ方式_履歴情報取得 */
    @Autowired
    private CpnTucRaiRrkSelectMapper cpnTucRaiRrkSelectMapper;

    /** 既存の履歴番号が更新確認 */
    @Autowired
    private KghKrkNokikanOpeLogic kghKrkNokikanOpeLogic;

    /**
     * 「アセスメント(インターライ)画面H」画面のデータ保存。
     * 
     * @param inDto アセスメント(インターライ)画面Hのデータ保存入力Dto
     * @return アセスメント(インターライ)画面Hのデータ保存出力Dto
     * @throws Exception 例外
     */
    protected AssessmentInterRAIUpdateServiceOutDto mainProcess(AssessmentInterRAIUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        AssessmentInterRAIUpdateServiceOutDto outDto = new AssessmentInterRAIUpdateServiceOutDto();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. バリデーションチェックを行う。===============
         * 
         */
        // 2.1. 電子ファイル保存設定フラグを取得する
        // e-文書法区分取得
        // 変数.電子ファイル保存設定フラグに上記処理で取得した電子ファイル保存設定フラグを設定する。
        boolean kghComEBunshoKbn = kghSmglHistoryLogic.getKghComEBunshoKbn();

        // 2.2. リクエストパラメータ.計画対象期間IDがある場合、下記のインターライ方式_履歴情報取得のDAOを利用し、履歴情報リストを取得する。
        CpnTucRaiRrkInfoByCriteriaInEntity cpnTucRaiRrkInfoByCriteriaInEntity = new CpnTucRaiRrkInfoByCriteriaInEntity();
        cpnTucRaiRrkInfoByCriteriaInEntity.setAnSc1Id(CommonConstants.PERIOD_MANAGE_FLG.equals(inDto.getKikanKanriFlg())
                ? CommonDtoUtil.strValToInt(inDto.getSc1Id())
                : CommonConstants.NUMBER_ZERO);
        cpnTucRaiRrkInfoByCriteriaInEntity.setAnJId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        cpnTucRaiRrkInfoByCriteriaInEntity.setAnUId(CommonDtoUtil.strValToInt(inDto.getUserId()));
        List<CpnTucRaiRrkInfoOutEntity> cpnTucRaiRrkInfoOutEntityList = cpnTucRaiRrkSelectMapper
                .findCpnTucRaiRrkInfoByCriteria(cpnTucRaiRrkInfoByCriteriaInEntity);

        // 2.3. 変数.電子ファイル保存設定フラグが「true」の場合、既存の履歴番号が変更を判断する
        if (kghComEBunshoKbn) {
            // 削除フラグ
            Integer deleteFlag = Integer.parseInt(CommonConstants.BLANK_STRING);
            // リクエストパラメータ.更新区分が「U：更新」の場合
            if (CommonDtoUtil.isUpdate(inDto)) {
                // 「0」で設定する
                deleteFlag = Integer.parseInt(CommonConstants.STR_0);
            }
            // リクエストパラメータ.更新区分が「C：新規」の場合
            else if (CommonDtoUtil.isCreate(inDto)) {
                // 「1」で設定する
                deleteFlag = Integer.parseInt(CommonConstants.STR_1);
            }
            // リクエストパラメータ.更新区分が「D：削除」の場合
            else if (CommonDtoUtil.isDelete(inDto)) {
                // 「2」で設定する
                deleteFlag = Integer.parseInt(CommonConstants.STR_2);
            }
            SmglYmdInDto adw = new SmglYmdInDto();
            // 履歴番号 = リクエストパラメータ.履歴番号
            adw.setKrirekiNo(CommonDtoUtil.strValToInt(inDto.getHistoryNo()));
            // 作成日 = リクエストパラメータ.基準日
            adw.setCreateYmd(inDto.getKijunbiYmd());

            List<Object> krirekiList = new ArrayList<>();
            krirekiList.addAll(cpnTucRaiRrkInfoOutEntityList);
            boolean chkSmglYmd = kghKrkNokikanOpeLogic.chkSmglYmd(CommonDtoUtil.strValToInt(inDto.getKinouId()),
                    deleteFlag, adw, krirekiList);

            // 2.4. 上記「2.3.」の結果フラグが「false：失敗」の場合
            if (!chkSmglYmd) {
                // 下記返却する情報を設定して、後続処理を飛ばして、API処理を正常終了とする。
                // レスポンス.計画対象期間ID ： リクエストパラメータ.計画対象期間ID
                outDto.setSc1Id(inDto.getSc1Id());
                // レスポンス.アセスメントID ： リクエストパラメータ.アセスメントID
                outDto.setRaiId(inDto.getRaiId());
                // レスポンス.エラー区分: "1"
                outDto.setErrKbn(CommonConstants.ERROR_KBN);
            }
        }

        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);

        TransactionStatus transactionA = transactionManager.getTransaction(def);
        try {
            // 処理3～7については、新しいトランザクションで実行
            outDto = mainProcessPsychologyUpdate(inDto, cpnTucRaiRrkInfoOutEntityList);
            transactionManager.commit(transactionA);
        } catch (Exception e) {
            transactionManager.rollback(transactionA);
            throw e;
        }

        /*
         * ===============8. e-文書の履歴保存処理（内部メソッドを作成予定）===============
         * 
         */
        // 共通処理
        TransactionStatus transactionB = transactionManager.getTransaction(def);
        try {
            assessmentInterRAILogic.insertEdocument(inDto, kghComEBunshoKbn);
            transactionManager.commit(transactionB);
        } catch (Exception e) {
            // 上記の処理に異常が発生した場合、レスポンス.エラー区分が「2」を設定し
            outDto.setErrKbn(CommonConstants.ERROR_KBN_2);
            transactionManager.rollback(transactionB);
            throw e;
        }

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * データ保存
     * 
     * @param inDto データ保存入力DTO
     * @param list  履歴情報リスト
     * @return データ保存出力DTO
     * @throws Exception
     */
    protected AssessmentInterRAIUpdateServiceOutDto mainProcessPsychologyUpdate(
            AssessmentInterRAIUpdateServiceInDto inDto, List<CpnTucRaiRrkInfoOutEntity> list) throws Exception {

        AssessmentInterRAIUpdateServiceOutDto outDto = new AssessmentInterRAIUpdateServiceOutDto();

        /*
         * ===============3. 計画対象期間の保存処理 （処理3～7については、新しいトランザクションで実行）===============
         * 
         */
        // 共通処理
        AssessmentInterRAIUpdateServiceOutDto insertKikan = assessmentInterRAILogic.insertKikan(inDto);
        String sc1Id = insertKikan.getSc1Id();

        /*
         * ===============4. 履歴の保存処理(第5歩と第6歩を含む)===============
         * 
         */
        // 共通処理
        AssessmentInterRAIUpdateServiceOutDto historySave = assessmentInterRAILogic.historySave(inDto, list, sc1Id);

        /*
         * ===============7. 画面サブ情報の保存処理===============
         * 
         */
        // 共通処理
        assessmentInterRAILogic.updateRrkSubInfo(historySave.getRaiId(), inDto);
        return outDto;
    }

}
