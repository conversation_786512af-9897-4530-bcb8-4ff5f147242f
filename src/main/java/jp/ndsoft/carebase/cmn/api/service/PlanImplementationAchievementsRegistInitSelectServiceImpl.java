package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01217CareKindInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01217ImplementationInfo;
import jp.ndsoft.carebase.cmn.api.logic.PlanImplementationAchievementsLogic;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanImplementationAchievementsRegistInitSelectOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanImplementationAchievementsRegistAchievementsInfoSelectInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanImplementationAchievementsRegistAchievementsInfoSelectOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanImplementationAchievementsRegistCaseInfoSelectInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanImplementationAchievementsRegistCaseInfoSelectOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.PlanImplementationAchievementsRegistInitSelectInDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnKkjMstByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnKkjMstOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoKmsCaseSyubetuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoKmsCaseSyubetuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMucHcf1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ShoMstCaseSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * 
 * @since 2025.05.21
 * <AUTHOR> 朱红昌
 * @implNote GUI01217_計画実施‐実績登録画面の初期情報
 */
@Service
public class PlanImplementationAchievementsRegistInitSelectServiceImpl extends
        SelectServiceImpl<PlanImplementationAchievementsRegistInitSelectInDto, PlanImplementationAchievementsRegistInitSelectOutDto> {

    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private CpnMucHcf1SelectMapper cpnMucHcf1SelectMapper;

    @Autowired
    private ShoMstCaseSelectMapper shoMstCaseSelectMapper;

    @Autowired
    private PlanImplementationAchievementsLogic planImplementationAchievementsLogic;

    /**
     * 計画実施‐実績登録画面の初期情報取得
     * 
     * @param inDto PlanImplementationAchievementsRegistInitSelectInDto.
     * @return PlanImplementationAchievementsRegistInitSelectOutDto
     * @throws Exception Exception
     */
    @Override
    protected PlanImplementationAchievementsRegistInitSelectOutDto mainProcess(
            PlanImplementationAchievementsRegistInitSelectInDto inDto) throws Exception {
        LOG.info(Constants.START);
        // 戻り情報を設定
        PlanImplementationAchievementsRegistInitSelectOutDto out = new PlanImplementationAchievementsRegistInitSelectOutDto();

        // 実施ドロップダウンリスト
        List<Gui01217ImplementationInfo> implementationList = new ArrayList<>();
        // ケース種別ドロップダウンリスト
        List<Gui01217CareKindInfo> careKindList = new ArrayList<>();

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2.実施ドロップダウンリストを取得===============
         * 
         */
        CpnKkjMstByCriteriaInEntity cpnKkjMstByCriteriaInEntity = new CpnKkjMstByCriteriaInEntity();
        // 区分フラグ
        cpnKkjMstByCriteriaInEntity.setAlKbnFlg(CommonDtoUtil.strValToInt(inDto.getKbnFlg()));
        // DAOを実行
        List<CpnKkjMstOutEntity> cpnMucHcf1SelectList = this.cpnMucHcf1SelectMapper
                .findCpnKkjMstByCriteria(cpnKkjMstByCriteriaInEntity);

        for (CpnKkjMstOutEntity cpnMucHcf1Select : cpnMucHcf1SelectList) {
            // 実施ドロップダウン
            Gui01217ImplementationInfo implementation = new Gui01217ImplementationInfo();
            implementation.setKbnCd(CommonDtoUtil.objValToString(cpnMucHcf1Select.getKbnCd()));
            implementation.setTextKnj(CommonDtoUtil.objValToString(cpnMucHcf1Select.getTextKnj()));
            // 実施ドロップダウンリストを設定
            implementationList.add(implementation);
        }
        /*
         * ===============3. 種別ドロップダウンリストを取得===============
         * 
         */
        // 3.1. ケース種別ドロップダウンリストを取得する。
        KaigoKmsCaseSyubetuByCriteriaInEntity kaigoKmsCaseSyubetuByCriteriaInEntity = new KaigoKmsCaseSyubetuByCriteriaInEntity();
        List<KaigoKmsCaseSyubetuOutEntity> shoMstCaseSelectList = this.shoMstCaseSelectMapper
                .findKaigoKmsCaseSyubetuByCriteria(kaigoKmsCaseSyubetuByCriteriaInEntity);

        // 3.2. 「3.1.」で取得する件数は>0の場合、
        if (!CollectionUtils.isNullOrEmpty(shoMstCaseSelectList)) {
            for (KaigoKmsCaseSyubetuOutEntity shoMstCaseSelect : shoMstCaseSelectList) {
                if (shoMstCaseSelect.getShisetuId() == CommonDtoUtil.strValToInt(inDto.getShisetuId())) {
                    // ケース種別ドロップダウン
                    Gui01217CareKindInfo careKind = new Gui01217CareKindInfo();
                    careKind.setCaseCd(CommonDtoUtil.objValToString(shoMstCaseSelect.getCaseCd()));
                    careKind.setShubetuKnj(CommonDtoUtil.objValToString(shoMstCaseSelect.getShubetuKnj()));
                    careKind.setColor(CommonDtoUtil.objValToString(shoMstCaseSelect.getColor()));
                    // ケース種別ドロップダウンリストを設定
                    careKindList.add(careKind);
                }
            }
        }

        /*
         * ===============4. 計画実施データ欄の情報を取得===============
         * 
         */
        PlanImplementationAchievementsRegistAchievementsInfoSelectInDto achievementsInfoSelectInDto = new PlanImplementationAchievementsRegistAchievementsInfoSelectInDto();
        // ヘッダID
        achievementsInfoSelectInDto.setKjisshi1Id(inDto.getKjisshi1Id());
        // 担当者絞込
        achievementsInfoSelectInDto.setTantoKnj(inDto.getTantoKnj());
        // 頻度絞込
        achievementsInfoSelectInDto.setHindoKnj(inDto.getHindoKnj());
        // 実施日
        achievementsInfoSelectInDto.setImplementationDate(inDto.getImplementationDate());
        PlanImplementationAchievementsRegistAchievementsInfoSelectOutDto achievementsInfoSelectOutDto = planImplementationAchievementsLogic
                .getAchievementsInfo(achievementsInfoSelectInDto);
        // 計画実施データ欄の情報を設定
        out.setPlanImplementationDataInfoList(achievementsInfoSelectOutDto.getPlanImplementationDataInfoList());

        /*
         * ===============5.ケース情報を取得===============
         * 
         */
        PlanImplementationAchievementsRegistCaseInfoSelectInDto caseInfoSelectIutDto = new PlanImplementationAchievementsRegistCaseInfoSelectInDto();
        // 施設ID
        caseInfoSelectIutDto.setShisetuId(inDto.getShisetuId());
        // 法人ID
        caseInfoSelectIutDto.setHojinId(inDto.getHojinId());
        // 利用者ID
        caseInfoSelectIutDto.setUserid(inDto.getUserid());
        // 実施日
        caseInfoSelectIutDto.setImplementationDate(inDto.getImplementationDate());
        // 変数設定区分
        caseInfoSelectIutDto.setComputeHensyuSetteiKbn(inDto.getComputeHensyuSetteiKbn());
        // 画面表示順
        caseInfoSelectIutDto.setTimeSortKbn(inDto.getTimeSortKbn());
        // データID
        caseInfoSelectIutDto.setKjisshi2Id(inDto.getKjisshi2Id());
        // 文字絞込
        caseInfoSelectIutDto.setMojiKnj(inDto.getMojiKnj());
        PlanImplementationAchievementsRegistCaseInfoSelectOutDto caseInfoSelectOutDto = planImplementationAchievementsLogic
                .getCaseInfo(caseInfoSelectIutDto);
        // 計画実施データ欄の情報を設定
        out.setCareList(caseInfoSelectOutDto.getCareList());

        out.setImplementationList(implementationList);
        out.setCareKindList(careKindList);
        LOG.info(Constants.END);
        return out;

    }
}
