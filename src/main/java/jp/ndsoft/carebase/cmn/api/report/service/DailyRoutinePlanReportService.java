package jp.ndsoft.carebase.cmn.api.report.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import jp.ndsoft.carebase.cmn.api.report.model.DailyRoutinePlanReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.dto.DailyRoutinePlanReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.dto.DailyRoutinePlanReportSvknjDto;
import jp.ndsoft.carebase.cmn.api.report.logic.DailyRoutinePlanReportLogic;
import jp.ndsoft.carebase.cmn.api.report.dto.DailyRoutinePlanReportServiceInDto;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JRPrintPage;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * U00861_日課計画表出力
 * 
 * <AUTHOR>
 */
@Service("DailyRoutinePlanReport")
public class DailyRoutinePlanReportService
        extends
        PdfReportServiceImpl<DailyRoutinePlanReportParameterModel, DailyRoutinePlanReportServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** U00861_日課計画表出力 ロジッククラス */
    @Autowired
    private DailyRoutinePlanReportLogic dailyRoutinePlanReportLogic;

    /*
     * ===============1.単項目チェック以外の入力チェック===============
     * 
     */
    // 特になし

    /**
     * 帳票パラメータ取得
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(
            DailyRoutinePlanReportParameterModel inDto,
            DailyRoutinePlanReportServiceOutDto outDto) throws Exception {

        LOG.info(Constants.START);
        // 帳票用データ詳細
        DailyRoutinePlanReportServiceInDto model = dailyRoutinePlanReportLogic.getU00861ReportParameters(inDto, outDto);
        // ノート情報格納配列
        List<DailyRoutinePlanReportServiceInDto> reportInfoList = new ArrayList<DailyRoutinePlanReportServiceInDto>();
        reportInfoList.add(model);

        // 共通サービス例リスト
        List<DailyRoutinePlanReportSvknjDto> svDailyList = model.getSvDailyList();
        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(reportInfoList);
        // サブレポート用のDataSourceを設定する
        JRBeanCollectionDataSource dataSourceSub = new JRBeanCollectionDataSource(svDailyList);
        model.setDataSource(dataSource);
        model.setCommonServiceDatasource(dataSourceSub);
        LOG.info(Constants.END);
        return model;
    }

    /**
     * 帳票出力
     * 
     * @param outDto 出力データ
     * @param inDto  入力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final DailyRoutinePlanReportParameterModel inDto,
            final DailyRoutinePlanReportServiceOutDto outDto) throws Exception {

        LOG.info(Constants.START);
        // 帳票に出力するデータ群の取得
        final DailyRoutinePlanReportServiceInDto reportParameter = (DailyRoutinePlanReportServiceInDto) getReportParameters(
                inDto, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        final JasperReport jasperFile = dailyRoutinePlanReportLogic.getJasperReport(getFwProps(), reportParameter,
                inDto.getJigyoInfo().getSvJigyoId());

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile, reportParameter.getParameters(),
                dataSource);
        List<JRPrintPage> pages = jasperPrint.getPages();
        if (pages.size() > 1) {
            pages.subList(1, pages.size()).clear();
        }
        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(inDto, jasperPrint);

        /*
         * =============== 6.レスポンスを返却===============
         * 
         */
        super.setFilepath(inDto, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);

    }

}
