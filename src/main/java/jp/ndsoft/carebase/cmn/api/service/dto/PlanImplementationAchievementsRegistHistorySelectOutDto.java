package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01217PlanImplementationHistoryKjisshi1IdInfo;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * @since 2025.05.21
 * <AUTHOR> 朱红昌
 * @implNote GUI01217_計画実施‐実績登録の画面用履歴情報の出力DTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PlanImplementationAchievementsRegistHistorySelectOutDto extends IDtoImpl {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 計画実施履歴ヘッダID情報リスト */
    @NotEmpty
    @Valid
    private List<Gui01217PlanImplementationHistoryKjisshi1IdInfo> planImplementationHistoryKjisshi1IdInfoList;
}
