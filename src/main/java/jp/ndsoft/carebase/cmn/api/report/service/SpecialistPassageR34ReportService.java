package jp.ndsoft.carebase.cmn.api.report.service;

import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.invoke.MethodHandles;
import java.lang.reflect.Field;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintOption;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.ReportCommonPrintSubjectHistory;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.SpecialistPassageR34ReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.SpecialistPassageR34ReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.model.SpecialistPassageR34ReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkSsmMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsm;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsmCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnYokaip3ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnYokaip3OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoNinteiByCriteriaInEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoNinteiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoNinteiDispStartYmdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoNinteiDispStartYmdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoNinteiOutEntity;
// import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoNinteiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnKeikaCasebikoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnKeikaCasebikoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnNyuDefData3IkouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnNyuDefData3IkouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KycKeikaShuruiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KycKeikaShuruiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinNameNumberByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShokuinNameNumberOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.UserinfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.UserinfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YokaigoJotaiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YokaigoJotaiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscYokaigoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucKaigoNinteiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMstChouhyouInkanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks11SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnYokaip3SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KycMscShuruiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ShoTucCasebikoSelectMapper;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * @since 2025.07.14
 * <AUTHOR> BUI VIET ANH
 * @implNote U01012_介護支援経過(R34改訂)
 */
@Service("SpecialistPassageR34ReportService")
public class SpecialistPassageR34ReportService extends
        PdfReportServiceImpl<SpecialistPassageR34ReportParameterModel, SpecialistPassageR34ReportServiceOutDto> {

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    public static final Font RPT_FONT = new Font("IPAexMincho", Font.PLAIN, 12);
    public static final int YMD_LENGTH = 64; // 単位PX 全角5文字
    public static final int KOUMOKU_LENGTH = 80; // 単位PX 全角6文字
    public static final int NAIYO_LENGTH = 220; // 単位PX 全角25文字
    public static final char GAIGYO_CHAR = '\n'; // 改行文字
    private static final int FRAME1_DATA_CNT = 26;
    private static final int FRAME2_DATA_CNT = 30;
    private static final String SHISETSU = "施設介護支援経過";
    private static final String KYOTAKU = "居宅介護支援経過";

    /** 介護認定情報取得 */
    @Autowired
    private ComTucKaigoNinteiSelectMapper comTucKaigoNinteiSelectMapper;

    /** 認定調査票情報取得 */
    @Autowired
    private CpnYokaip3SelectMapper cpnYokaip3SelectMapper;

    /** 計画書（１）情報取得 */
    @Autowired
    private CpnTucCks11SelectMapper cpnTucCks11SelectMapper;

    /** 要介護状態マスタ（４－４）取得 */
    @Autowired
    private ComMscYokaigoSelectMapper comMscYokaigoSelectMapper;

    /** 28-xx ケアプラン帳票印鑑欄情報取得 */
    @Autowired
    private CpnMstChouhyouInkanSelectMapper cpnMstChouhyouInkanSelectMapper;

    /** 29-17 備考情報取得 */
    @Autowired
    private ShoTucCasebikoSelectMapper shoTucCasebikoSelectMapper;

    /** 利用者の情報取得情報 */
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;

    /** 種類マスタ情報取得 */
    @Autowired
    private KycMscShuruiSelectMapper kycMscShuruiSelectMapper;

    /** 職員基本情報取得 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /** システム情報を取得するDAO */
    @Autowired
    private KghMocKrkSsmMapper kghMocKrkSsmMapper;

    /** Nds3GkFunc01Logicロジッククラス */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** KEISHOのデフォルト値 */
    private String DEFAULT_KEISHO = "殿";

    @Override
    protected Object getReportParameters(SpecialistPassageR34ReportParameterModel model,
            SpecialistPassageR34ReportServiceOutDto outDto) throws Exception {
        LOG.info(Constants.START);
        // 印刷オプション
        ReportCommonPrintOption printOption = model.getPrintOption();
        // 印刷対象履歴リスト
        List<ReportCommonPrintSubjectHistory> printSubjectHistoryList = model.getPrintSubjectHistoryList();
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));
        // U01012_介護支援経過の入力Dto
        SpecialistPassageR34ReportServiceInDto infoInDto = new SpecialistPassageR34ReportServiceInDto();
        // ページのリスト
        List<SpecialistPassageR34ReportServiceInDto> pageList = new ArrayList<>();
        // 印鑑欄表示区分
        boolean hankoAreaPrintFlag = false;

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        int isEmptyFlg = 0;
        if (printOption.getEmptyFlg().equals(CommonConstants.VALID_FLG_TRUE)
                || printOption.getEmptyFlg().equals(CommonConstants.STR_1)) {
            isEmptyFlg = 1;
        }

        if (isEmptyFlg == 1) {
            // ・「記入用シートを印刷する」がチェックされている場合は、yokai を空文字（""）に設定する。
            // 要介護度
            infoInDto.setYokai(CommonConstants.BLANK_STRING);

            // ・「記入用シートを印刷する」がチェックされている場合は、userName を空文字（""）に設定する。
            // 利用者名
            infoInDto.setUserName(CommonConstants.BLANK_STRING);

            // ・「記入用シートを印刷する」がチェックされている場合は、keisho を空文字（""）に設定する。
            // 敬称
            infoInDto.setKeisho(CommonConstants.BLANK_STRING);

            // ・「記入用シートを印刷する」がチェックされている場合は、「元号 年 月 日」の文字を設定する（元号は日付より変わる）。
            // 作成年月日
            infoInDto.setCreateDate(ReportConstants.DATE_NULL_YMD);

            // ・「記入用シートを印刷する」がチェックされている場合は、shokuName を空文字（""）に設定する。
            // 作成者名
            infoInDto.setShokuName(CommonConstants.BLANK_STRING);

            // ・リクエストパラメータ.記入用シートを印刷するが選択されている場合（= false）
            // 印鑑枠表示フラグ
            infoInDto.setHanko_area_print_flag(false);

            // ・リクエストパラメータ.記入用シートを印刷するが選択されている場合（= false）
            // フレーム表示フラグ
            infoInDto.setFrame_area1_print_flag(false);

        } else {
            /*
             * ===============2. 要介護度の情報を収集する===============
             * 
             */
            Integer yokaiKbn = null;

            // 見込みフラグ（例：mikomiFlg）という変数を作成し、false を代入する。
            boolean mikomiFlg = false;

            // 2.1. リクエストパラメータ.印刷する要介護度 = 1 の場合、実行する。利用管理者から取得（作成日を基準）
            if (model.getYokaiKbn().equals(CommonConstants.YOKAI_KBN_1)) {
                KaigoNinteiByCriteriaInEntity kaigoNinteiByCriteria = new
                KaigoNinteiByCriteriaInEntity();
                kaigoNinteiByCriteria.setAlUserid(CommonDtoUtil.strValToInt(
                printSubjectHistoryList.getFirst().getUserId()));
                kaigoNinteiByCriteria.setAsYymmYmd(printSubjectHistoryList.getFirst().getStartYmd());

                List<KaigoNinteiOutEntity> kaigoNinteiList = comTucKaigoNinteiSelectMapper
                .findKaigoNinteiByCriteria(kaigoNinteiByCriteria);

                if (CollectionUtils.isNotEmpty(kaigoNinteiList) && kaigoNinteiList.size() >
                0) {
                yokaiKbn = kaigoNinteiList.getFirst().getYokaiKbn();
                }

            }
            // 2.2. リクエストパラメータ.印刷する要介護度 = 2 の場合、実行する。利用管理者から取得（印刷範囲を基準）
            else if (model.getYokaiKbn().equals(CommonConstants.YOKAI_KBN_2)) {
                KaigoNinteiDispStartYmdByCriteriaInEntity kaigoNinteiDispStartYmdByCriteria = new KaigoNinteiDispStartYmdByCriteriaInEntity();
                kaigoNinteiDispStartYmdByCriteria.setUserid(CommonDtoUtil.strValToInt(
                        printSubjectHistoryList.getFirst().getUserId()));
                kaigoNinteiDispStartYmdByCriteria.setStartYmd(printSubjectHistoryList.getFirst().getStartYmd());
                kaigoNinteiDispStartYmdByCriteria.setEndYmd(printSubjectHistoryList.getFirst().getEndYmd());

                List<KaigoNinteiDispStartYmdOutEntity> kaigoNinteiDispStartYmdList = comTucKaigoNinteiSelectMapper
                        .findKaigoNinteiDispStartYmdByCriteria(kaigoNinteiDispStartYmdByCriteria);

                if (CollectionUtils.isNotEmpty(kaigoNinteiDispStartYmdList) && kaigoNinteiDispStartYmdList.size() > 0) {
                    yokaiKbn = kaigoNinteiDispStartYmdList.getFirst().getYokaiKbn();
                }
            }
            // 2.3. リクエストパラメータ.印刷する要介護度 = 3の場合、実行する。認定調査票から取得
            else if (model.getYokaiKbn().equals(CommonConstants.YOKAI_KBN_3)) {
                CpnYokaip3ByCriteriaInEntity cpnYokaip3ByCriteria = new CpnYokaip3ByCriteriaInEntity();
                cpnYokaip3ByCriteria.setAlUserId(CommonDtoUtil.strValToInt(
                        printSubjectHistoryList.getFirst().getUserId()));
                cpnYokaip3ByCriteria.setAlSvjigyoId(CommonDtoUtil.strValToInt(model.getSvJigyoId()));
                cpnYokaip3ByCriteria.setAsYmd(printSubjectHistoryList.getFirst().getStartYmd());

                List<CpnYokaip3OutEntity> cpnYokaip3List = cpnYokaip3SelectMapper
                        .findCpnYokaip3ByCriteria(cpnYokaip3ByCriteria);

                if (CollectionUtils.isNotEmpty(cpnYokaip3List) && cpnYokaip3List.size() > 0) {
                    /**
                     * cpn_tuc_csc1.yokai_kbn2の区分名
                     * yokai_kbn2が空白或いは「0」の場合、cpn_tuc_csc1.yokai_kbn1の区分名＋"(見込み)"
                     */
                    if (cpnYokaip3List.getFirst().getYokaiKbn2() == null
                            || cpnYokaip3List.getFirst().getYokaiKbn2() == 0) {
                        yokaiKbn = cpnYokaip3List.getFirst().getYokaiKbn1();
                        // yokai_kbn2 が空白あるいは「0」の場合は、見込みフラグに true を設定する。
                        mikomiFlg = true;
                    } else {
                        yokaiKbn = cpnYokaip3List.getFirst().getYokaiKbn2();
                    }
                }

            }
            // 2.4. リクエストパラメータ.印刷する要介護度 = 4の場合、実行する。計画書（１）から取得
            else if (model.getYokaiKbn().equals(CommonConstants.YOKAI_KBN_4)) {
                KghCpnNyuDefData3IkouByCriteriaInEntity kghCpnNyuDefData3IkouByCriteria = new KghCpnNyuDefData3IkouByCriteriaInEntity();
                kghCpnNyuDefData3IkouByCriteria.setAnUserid(CommonDtoUtil.strValToInt(
                        printSubjectHistoryList.getFirst().getUserId()));
                kghCpnNyuDefData3IkouByCriteria.setAnHoujinId(CommonDtoUtil.strValToInt(model.getHoujinId()));
                kghCpnNyuDefData3IkouByCriteria.setAnShisetuId(CommonDtoUtil.strValToInt(model.getShisetuId()));
                kghCpnNyuDefData3IkouByCriteria.setAnSvJigyoId(CommonDtoUtil.strValToInt(model.getSvJigyoId()));

                List<KghCpnNyuDefData3IkouOutEntity> kghCpnNyuDefData3IkouList = cpnTucCks11SelectMapper
                        .findKghCpnNyuDefData3IkouByCriteria(kghCpnNyuDefData3IkouByCriteria);

                if (CollectionUtils.isNotEmpty(kghCpnNyuDefData3IkouList) && kghCpnNyuDefData3IkouList.size() > 0) {
                    yokaiKbn = kghCpnNyuDefData3IkouList.getFirst().getYokaiKbn();

                    // 見込みフラグに［2.4］から返却された mikomi_flg を設定する。
                    mikomiFlg = kghCpnNyuDefData3IkouList.getFirst().getMikomiFlg() == 1;
                }
            }

            if (yokaiKbn != null) {
                /*
                 * ===============3. 要介護度の名称を取得===============
                 * 
                 */
                // 3.1. DAOから情報を取得
                YokaigoJotaiByCriteriaInEntity yokaigoInfoByCriteria = new YokaigoJotaiByCriteriaInEntity();
                yokaigoInfoByCriteria.setAiYokai(CommonDtoUtil.objValToString(yokaiKbn));

                List<YokaigoJotaiOutEntity> yokaigoJotaiList = comMscYokaigoSelectMapper
                        .findYokaigoJotaiByCriteria(yokaigoInfoByCriteria);

                if (CollectionUtils.isNotEmpty(yokaigoJotaiList) && yokaigoJotaiList.size() > 0) {
                    // 見込みフラグがある場合は、接尾辞「"(見込み)"」を追加する。
                    if (mikomiFlg) {
                        // 要介護度
                        infoInDto.setYokai(yokaigoJotaiList.getFirst().getYokaiKnj() + KghCmpF01Logic.STR_MIKOMI);
                    } else {
                        // 要介護度
                        infoInDto.setYokai(yokaigoJotaiList.getFirst().getYokaiKnj());
                    }
                }
            } else {
                // 要介護度
                infoInDto.setYokai(CommonConstants.BLANK_STRING);
            }

            /*
             * ===============4. リクエストパラメータ.記入用シートを印刷するフラグ = true の場合、本処理をスキップする。
             * リクエストパラメータ.記入用シートを印刷するフラグ = falseかつリクエストパラメータ.印鑑枠表示フラグ = trueの場合、印鑑情報を取得する。
             * ===============
             */
            KghCpnMstChouhyouInkanPrnByCriteriaInEntity kghCpnMstChouhyouInkanPrnByCriteria = new KghCpnMstChouhyouInkanPrnByCriteriaInEntity();
            kghCpnMstChouhyouInkanPrnByCriteria.setAnKey1(CommonDtoUtil.strValToInt(model.getHoujinId()));
            kghCpnMstChouhyouInkanPrnByCriteria.setAsSec(model.getSheetNo());
            kghCpnMstChouhyouInkanPrnByCriteria.setAnKey2(CommonDtoUtil.strValToInt(model.getShisetuId()));
            kghCpnMstChouhyouInkanPrnByCriteria.setAnKey3(CommonDtoUtil.strValToInt(model.getSvJigyoId()));

            List<KghCpnMstChouhyouInkanPrnOutEntity> kghCpnMstChouhyouInkanPrn = cpnMstChouhyouInkanSelectMapper
                    .findKghCpnMstChouhyouInkanPrnByCriteria(
                            kghCpnMstChouhyouInkanPrnByCriteria);

            if (CollectionUtils.isNotEmpty(kghCpnMstChouhyouInkanPrn) && kghCpnMstChouhyouInkanPrn.size() > 0) {

                KghCpnMstChouhyouInkanPrnOutEntity kghCpnMstChouhyou = kghCpnMstChouhyouInkanPrn.getFirst();

                hankoAreaPrintFlag = CommonDtoUtil.objValToString(kghCpnMstChouhyou.getHyoujiKbn())
                        .equals(CommonConstants.VALID_FLG_TRUE);

                // フレーム表示フラグ
                infoInDto.setFrame_area1_print_flag(hankoAreaPrintFlag);
                // 印鑑枠表示フラグ
                infoInDto
                        .setHanko_area_print_flag(hankoAreaPrintFlag);

                // 印鑑欄名１
                infoInDto.setHanko1_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko1Knj()) ? kghCpnMstChouhyou.getHanko1Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ1
                infoInDto.setHanko1_print_flag(true);

                // 印鑑欄名２
                infoInDto.setHanko2_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko2Knj()) ? kghCpnMstChouhyou.getHanko2Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ2
                infoInDto.setHanko2_print_flag(true);

                // 印鑑欄名３
                infoInDto.setHanko3_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko3Knj()) ? kghCpnMstChouhyou.getHanko3Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ3
                infoInDto.setHanko3_print_flag(true);

                // 印鑑欄名４
                infoInDto.setHanko4_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko4Knj()) ? kghCpnMstChouhyou.getHanko4Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ4
                infoInDto.setHanko4_print_flag(true);

                // 印鑑欄名５
                infoInDto.setHanko5_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko5Knj()) ? kghCpnMstChouhyou.getHanko5Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ5
                infoInDto.setHanko5_print_flag(true);

                // 印鑑欄名６
                infoInDto.setHanko6_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko6Knj()) ? kghCpnMstChouhyou.getHanko6Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ6
                infoInDto.setHanko6_print_flag(true);

                // 印鑑欄名７
                infoInDto.setHanko7_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko7Knj()) ? kghCpnMstChouhyou.getHanko7Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ7
                infoInDto.setHanko7_print_flag(true);

                // 印鑑欄名８
                infoInDto.setHanko8_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko8Knj()) ? kghCpnMstChouhyou.getHanko8Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ8
                infoInDto.setHanko8_print_flag(true);

                // 印鑑欄名９
                infoInDto.setHanko9_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko9Knj()) ? kghCpnMstChouhyou.getHanko9Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ9
                infoInDto.setHanko9_print_flag(true);

                // 印鑑欄名１０
                infoInDto.setHanko10_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko10Knj())
                                ? kghCpnMstChouhyou.getHanko10Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ10
                infoInDto.setHanko10_print_flag(true);

                // 印鑑欄名１１
                infoInDto.setHanko11_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko11Knj())
                                ? kghCpnMstChouhyou.getHanko11Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ11
                infoInDto.setHanko11_print_flag(true);

                // 印鑑欄名１２
                infoInDto.setHanko12_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko12Knj())
                                ? kghCpnMstChouhyou.getHanko12Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ12
                infoInDto.setHanko12_print_flag(true);

                // 印鑑欄名１３
                infoInDto.setHanko13_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko13Knj())
                                ? kghCpnMstChouhyou.getHanko13Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ13
                infoInDto.setHanko13_print_flag(true);

                // 印鑑欄名１４
                infoInDto.setHanko14_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko14Knj())
                                ? kghCpnMstChouhyou.getHanko14Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ14
                infoInDto.setHanko14_print_flag(true);

                // 印鑑欄名１５
                infoInDto.setHanko15_knj(
                        StringUtils.isNotBlank(kghCpnMstChouhyou.getHanko15Knj())
                                ? kghCpnMstChouhyou.getHanko15Knj()
                                : CommonConstants.BLANK_STRING);
                // 印鑑欄表示フラグ15
                infoInDto.setHanko15_print_flag(true);

            } else {
                // 印鑑枠表示フラグ
                infoInDto.setHanko_area_print_flag(false);

                // フレーム表示フラグ
                infoInDto.setFrame_area1_print_flag(false);
            }

            /*
             * ===============5. 利用者基本（１－６）情報の取得===============
             * 
             */
            // 5.1. 下記の利用者基本情報取得のDAOを利用し、利用者基本（１－６）情報を取得する。
            UserinfoByCriteriaInEntity userinfoByCriteria = new UserinfoByCriteriaInEntity();
            userinfoByCriteria.setUser(CommonDtoUtil.strValToInt(
                    printSubjectHistoryList.getFirst().getUserId()));

            List<UserinfoOutEntity> userinfoList = comTucUserSelectMapper
                    .findUserinfoByCriteria(userinfoByCriteria);

            if (CollectionUtils.isNotEmpty(userinfoList) && userinfoList.size() > 0) {
                // 利用者名
                infoInDto.setUserName(userinfoList.getFirst().getName1Knj() + CommonConstants.BLANK_SPACE
                        + userinfoList.getFirst().getName2Knj());
            }

            // ・1.印刷設定画面で「作成年月日を選択する」が選択された場合、選択された条件に合う登録日を設定
            if (model.getDesignationDate().equals(CommonConstants.INDEX_ONE)) {
                // 作成年月日
                infoInDto.setCreateDate(ReportUtil.getLocalDateToJapaneseDateTimeFormat(model.getAsYmd()));
            }
            // ・2.印刷設定画面で「空白」が選択された場合、「元号 年 月 日」の文字(元号は日付より変わる)
            else if (model.getDesignationDate().equals(CommonConstants.INDEX_TWO)) {
                // 作成年月日
                infoInDto.setCreateDate(ReportConstants.DATE_NULL_YMD);
            }
            // ・3.印刷設定画面で「作成年月日を印刷しない」が選択された場合、空白
            else if (model.getDesignationDate().equals(CommonConstants.INDEX_THREE)) {
                // 作成年月日
                infoInDto.setCreateDate(CommonConstants.BLANK_STRING);
            }

            // 作成者名
            infoInDto.setShokuName(model.getShokuName());
        }

        /*
         * ===============6. 指定日の空欄表示処理===============
         * 
         */
        // 6.1. 下記共通関数で利用し、指定日の空欄表示を取得する。
        // 指定日
        infoInDto.setDesignationDate(getShiTeiDate(
                model.getPrintSet().getShiTeiKubun(),
                model.getPrintSet().getShiTeiDate(),
                model.getSystemDate()));

        if (isEmptyFlg == 1) {
            // 指定日印刷フラグ
            infoInDto.setDesignationDate_print_flag(false);
        } else if (model.getDesignationDate().equals(
                CommonConstants.STR_1)
                || model.getDesignationDate()
                        .equals(CommonConstants.STR_2)) {
            // 1: 指定日印刷する、2: 空白、3: 作成年月日を印刷しない
            // 指定日印刷フラグ
            infoInDto.setDesignationDate_print_flag(true);
        } else {
            // 指定日印刷フラグ
            infoInDto.setDesignationDate_print_flag(false);
        }

        /*
         * ===============7. リクエストパラメータおよびデータベースクエリに基づく敬称の処理と決定===============
         * 
         */
        // 敬称
        if (printOption.getKeishoFlg().equals(CommonConstants.VALID_FLG_TRUE)) {
            // フラグが"1"の場合、モデルから敬称を取得
            infoInDto.setKeisho(printOption.getKeishoKnj());
        } else {
            // 敬称をマスタから取得
            KghMocKrkSsmCriteria criteria = new KghMocKrkSsmCriteria();
            criteria.createCriteria()
                    .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(model.getSvJigyoId()))
                    .andBunrui1IdEqualTo(CommonConstants.BUNRUI1_ID_2)
                    .andBunrui2IdEqualTo(CommonConstants.BUNRUI2_ID_1);
            // .andDelFlgEqualTo(Constants.DELL_FLG_OFF);
            // 該当データを検索
            List<KghMocKrkSsm> kghMocKrkSsmList = kghMocKrkSsmMapper.selectByCriteria(criteria);
            // 敬称使用フラグが"1"の場合
            Optional<KghMocKrkSsm> keishoFlg = kghMocKrkSsmList.stream()
                    .filter(item -> item.getBunrui3Id() == CommonConstants.BUNRUI3_ID_8).findFirst();
            if (keishoFlg.isPresent() && keishoFlg.get().getIntValue() == CommonConstants.INT_1) {
                // 敬称文字列を取得
                Optional<KghMocKrkSsm> keishoKnj = kghMocKrkSsmList.stream()
                        .filter(item -> item.getBunrui3Id() == CommonConstants.BUNRUI3_ID_9).findFirst();
                if (keishoKnj.isPresent()) {
                    infoInDto.setKeisho(printOption.getKeishoKnj());
                } else {
                    // 敬称文字列が未設定の場合、空文字を設定
                    infoInDto.setKeisho(CommonConstants.BLANK_STRING);
                }
            } else {
                // 敬称使用フラグが"1"でない場合、デフォルト値を設定
                infoInDto.setKeisho(DEFAULT_KEISHO);
            }
        }

        /*
         * ===============パラメータを設定する手順===============
         * 
         */
        // ・リクエストパラメータ.初期設定マスタ = "1" の場合、title =「施設介護支援経過」
        if (model.getCksFlg().equals(CommonConstants.CKS_FLG_FACILITY)) {
            // 帳票タイトル
            infoInDto.setTitle(SHISETSU);
        }
        // ・リクエストパラメータ.初期設定マスタ = "2" の場合、title =「居宅介護支援経過」
        else {
            // 帳票タイトル
            infoInDto.setTitle(KYOTAKU);
        }

        // ・「記入用シートを印刷する」が "0" の場合、かつ「期間を印刷する」が "1" の場合は True を返し、それ以外の場合は False を返す。
        if (isEmptyFlg == 1) {
            // 期間印刷フラグ
            infoInDto.setPeriod_print_flag(false);
        } else {
            // 期間印刷フラグ
            infoInDto.setPeriod_print_flag(model.getKeikaTimeFlg().equals(CommonConstants.VALID_FLG_TRUE));
            if (infoInDto.getPeriod_print_flag()) {
                String rangeSeparator = CommonConstants.BLANK_SPACE + CommonConstants.RANGE_SEPARATOR
                        + CommonConstants.BLANK_SPACE;
                // 期間
                infoInDto.setPeriod(ReportUtil.getJapaneseDateWithLatinEra(
                        printSubjectHistoryList.getFirst().getStartYmd(), ReportConstants.JAPANESE_ERA_FULL_PADDED)
                        + rangeSeparator
                        + ReportUtil.getJapaneseDateWithLatinEra(
                                printSubjectHistoryList.getFirst().getEndYmd(),
                                ReportConstants.JAPANESE_ERA_FULL_PADDED));
            } else {
                // 期間
                infoInDto.setPeriod(CommonConstants.BLANK_STRING);
            }
        }

        // ・リクエストパラメータ.事業者名
        // 事業者名
        infoInDto.setJigyoKnj(model.getSvJigyoKnj());

        // 作成者の順番
        infoInDto.setShokuKubun(model.getShokuKubun());

        if (isEmptyFlg == 1) {
            // 空白の内容を表示する
            renderContentEmptyLineByLine(1, infoInDto, FRAME2_DATA_CNT);
        } else {
            /*
             * ===============8. 種類マスタ情報取得の情報を収集する===============
             * 
             */
            KycKeikaShuruiByCriteriaInEntity kycKeikaShuruiByCriteria = new KycKeikaShuruiByCriteriaInEntity();
            kycKeikaShuruiByCriteria.setSvJigyoId(CommonDtoUtil.strValToInt(model.getSvJigyoId()));

            List<KycKeikaShuruiOutEntity> kycKeikaShuruiList = kycMscShuruiSelectMapper
                    .findKycKeikaShuruiByCriteria(kycKeikaShuruiByCriteria);

            /*
             * ===============9. 職員基本情報取得の情報を収集する===============
             * 
             */
            List<ShokuinNameNumberOutEntity> shokuinNameNumberList = comMscShokuinSelectMapper
                    .findShokuinNameNumberByCriteria(new ShokuinNameNumberByCriteriaInEntity());

            /*
             * ===============10. 備考情報取得の情報を取得===============
             * 
             */
            // 10.1. DAOから備考情報取得の情報を収集する。
            KghCpnKeikaCasebikoByCriteriaInEntity kghCpnKeikaCasebikoByCriteria = new KghCpnKeikaCasebikoByCriteriaInEntity();
            kghCpnKeikaCasebikoByCriteria.setUserid(CommonDtoUtil.strValToInt(
                    printSubjectHistoryList.getFirst().getUserId()));
            kghCpnKeikaCasebikoByCriteria.setSvJigyoId(CommonDtoUtil.strValToInt(model.getSvJigyoId()));
            kghCpnKeikaCasebikoByCriteria.setStartDate(printSubjectHistoryList.getFirst().getStartYmd());
            kghCpnKeikaCasebikoByCriteria.setEndDate(printSubjectHistoryList.getFirst().getEndYmd());

            List<KghCpnKeikaCasebikoOutEntity> kghCpnKeikaCasebikoList = shoTucCasebikoSelectMapper
                    .findKghCpnKeikaCasebikoByCriteria(kghCpnKeikaCasebikoByCriteria);

            // sho_tuc_casebikoテーブルからデータを処理する
            getContents(model, infoInDto, kghCpnKeikaCasebikoList, kycKeikaShuruiList, shokuinNameNumberList, pageList,
                    hankoAreaPrintFlag);
        }

        /*
         * ===============11. 上記処理で取得した結果レスポンスを返却する。===============
         * 
         */
        pageList.add(infoInDto);

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(pageList);
        infoInDto.setDataSource(dataSource);

        LOG.info(Constants.END);

        return infoInDto;
    }

    /**
     * 指定日を取得する
     * 
     * @param shiTeiKubun 指定日印刷区分
     * @param shiTeiDate  西暦日付
     * @param systemDate  システム日付
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    private String getShiTeiDate(String shiTeiKubun, String shiTeiDate, String systemDate) {
        String blankDate = CommonConstants.BLANK_STRING;
        switch (shiTeiKubun) {
            case ReportConstants.PRINTDATE_KBN_SHINAI:
                // 指定日印刷区分 = 1 の場合、指定日=""
                blankDate = CommonConstants.BLANK_STRING;
                break;
            case ReportConstants.PRINTDATE_KBN_PRINT:
                // 指定日印刷区分 = 2 の場合
                // 西暦日付を和暦日付に変換する
                blankDate = ReportUtil.getLocalDateToJapaneseDateTimeFormat(shiTeiDate);
                break;
            case ReportConstants.PRINTDATE_KBN_BLANKDATE:
                // 指定日印刷区分 = 3 の場合
                // 指定日の空欄表示処理
                DateTimeFormatter inputFormatter = DateTimeFormatter
                        .ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
                DateTimeFormatter outputFormatter = DateTimeFormatter
                        .ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);
                LocalDateTime dateTime = LocalDateTime.parse(systemDate, inputFormatter);
                blankDate = nds3GkFunc01Logic.blankDate(dateTime.format(outputFormatter));
                break;
            default:
                blankDate = CommonConstants.BLANK_STRING;
                break;
        }

        return blankDate;
    }

    /**
     * 介護支援経過の内容を取得してDTOに設定する
     * 
     * @param model                 - 介護支援経過のパラメータモデル
     * @param dto                   - 介護支援経過のDTO
     * @param inputList             - 支援結果記録のリスト
     * @param kycKeikaShuruiList    - 種類マスタ情報のリスト
     * @param shokuinNameNumberList - 職員情報のリスト
     * @param pageList              ページごとのDTOリスト
     * @param hankoAreaPrintFlag    - 印鑑欄を表示するかどうかのフラグ（trueの場合、行数はFRAME1_DATA_CNT）
     */
    private void getContents(
            SpecialistPassageR34ReportParameterModel model, SpecialistPassageR34ReportServiceInDto dto,
            List<KghCpnKeikaCasebikoOutEntity> inputList,
            List<KycKeikaShuruiOutEntity> kycKeikaShuruiList, List<ShokuinNameNumberOutEntity> shokuinNameNumberList,
            List<SpecialistPassageR34ReportServiceInDto> pageList, boolean hankoAreaPrintFlag) {

        // 開始行
        int currentLine = 1;

        // 最大行数
        int maxLine = hankoAreaPrintFlag ? FRAME1_DATA_CNT
                : FRAME2_DATA_CNT;
        for (KghCpnKeikaCasebikoOutEntity entity : inputList) {
            String yymmYmdValue = CommonConstants.BLANK_STRING;
            String naiyoKnjValue = CommonConstants.BLANK_STRING;
            String koumokuKnjValue = CommonConstants.BLANK_STRING;

            // 和暦日付と曜日を取得
            String date = ReportUtil.getJapaneseDateWithLatinEra(entity.getYymmYmd(),
                    ReportConstants.JAPANESE_ERA_FULL_UNPADDED);
            // 作成者の順番が0（表示する）の場合は表示し、1（表示しない）の場合は非表示にする。
            String dayOfWeek = model.getDisplayDayOfWeek().equals("0") ? getJapaneseDayOfWeek(entity.getYymmYmd())
                    : CommonConstants.BLANK_STRING;

            /// タイトルを取得
            String title = StringUtils.isNotBlank(entity.getTitleKnj())
                    ? "（" + entity.getTitleKnj() + "）"
                    : CommonConstants.BLANK_STRING;

            // 種別マスタリストから種別名を取得
            String shuruiKnj = kycKeikaShuruiList.stream()
                    .filter(s -> s.getShuruiCd() == entity.getShuruiCd())
                    .map(KycKeikaShuruiOutEntity::getShuruiKnj)
                    .findFirst()
                    .orElse(CommonConstants.BLANK_STRING); // 該当なしの場合は空文字を設定

            // 結合して表示文字列を作成
            yymmYmdValue = date + GAIGYO_CHAR + dayOfWeek + GAIGYO_CHAR + title + GAIGYO_CHAR + shuruiKnj;

            // 項目のデータを設定
            if (StringUtils.isNotBlank(entity.getKoumokuKnj()))
                koumokuKnjValue = entity.getKoumokuKnj();

            // ※印刷設定画面で「記録者を印字する」にチェックされた場合、記録者を設定
            if (model.getDisplayRecorder().equals(CommonConstants.VALID_FLG_TRUE)) {
                // 職員情報を検索する
                ShokuinNameNumberOutEntity staff = shokuinNameNumberList.stream()
                        .filter(s -> s.getChkShokuId().equals(entity.getStaffid()))
                        .findFirst()
                        .orElse(null);

                // 職員名を結合する（氏名の間に全角スペースを挿入）
                String staffName = staff.getShokuin1Knj() + CommonConstants.FULL_WIDTH_SPACE
                        + staff.getShokuin2Knj();

                naiyoKnjValue = naiyoKnjValue + "【記録者】" + staffName;
            }

            // ※支援結果記録マスタ画面で「時間表示」が「表示する」にチェックされて、かつ、印刷設定画面で「時間を印刷する」がチェックされた場合、時間を設定
            if (model.getDisplayTime().equals("0")
                    && model.getPrintTimeFlag().equals(CommonConstants.VALID_FLG_TRUE)) {
                // 開始時間
                String startTime = formatTime(entity.getTimeHh(), entity.getTimeMm());

                // // 終了時間
                String endTime = formatTime(entity.getEndHh(), entity.getEndMm());

                naiyoKnjValue = naiyoKnjValue + "【時間】" + startTime + "～" + endTime;
            }

            // 空白行を追加
            if (!naiyoKnjValue.equals(CommonConstants.BLANK_STRING)) {
                naiyoKnjValue = naiyoKnjValue + "\n \n";
            }

            naiyoKnjValue = naiyoKnjValue + entity.getCaseKnj();

            // 1行ずつ内容を描画し、描画後の行番号を更新する
            currentLine = renderContentLineByLine(currentLine, yymmYmdValue, koumokuKnjValue, naiyoKnjValue, dto,
                    maxLine, pageList);

            // 現在の行が60行以内であれば、残り行を空白で埋める
            if (currentLine <= maxLine * 2) {
                renderContentEmptyLineByLine(currentLine, dto, maxLine);
            }
        }

    }

    /**
     * 年月日と内容の文字列をピクセル幅で折り返し、1行ずつDTOに設定する
     *
     * @param currentLine 現在処理中の行番号
     * @param yymmYmd     年月日（文字列）
     * @param koumokuKnj  項目（文字列）
     * @param naiyoKnj    内容（文字列）
     * @param dto         設定対象の介護支援経過DTO
     * @param maxLine     最大行数
     * @return 処理後の行番号（currentLine + 行数）
     * @param pageList ページごとのDTOリスト
     */
    int renderContentLineByLine(int currentLine, String yymmYmd, String koumokuKnj, String naiyoKnj,
            SpecialistPassageR34ReportServiceInDto dto, int maxLine,
            List<SpecialistPassageR34ReportServiceInDto> pageList) {
        // 年月日・内容をピクセル幅で折り返して複数行に分割
        List<String> yymmYmdList = wrapTextByPixelWidth(yymmYmd, RPT_FONT, YMD_LENGTH);
        List<String> koumokuKnjList = wrapTextByPixelWidth(koumokuKnj, RPT_FONT, KOUMOKU_LENGTH);
        List<String> naiyoKnjList = wrapTextByPixelWidth(naiyoKnj, RPT_FONT, NAIYO_LENGTH);

        // 行数の多い方を出力対象件数とする
        int prtDataCnt = Math.max(Math.max(yymmYmdList.size(), koumokuKnjList.size()), naiyoKnjList.size());

        // 出力後の次行番号を初期化
        int nextLine = currentLine + prtDataCnt;

        // JasperReportsの描画位置に使うカウンタ
        int i = currentLine;

        // データのリストにおけるインデックス
        int dataIndex = 0;
        while (i < prtDataCnt + currentLine) {

            // 描画位置（左/右）を判定
            String side = i <= maxLine ? "left" : "right";
            int fieldIndex = i <= maxLine ? i : i - maxLine;

            // フィールド名を組み立て
            String yymmYmdFieldName = "yymm_ymd_" + side + fieldIndex;
            String koumokuKnjFieldName = "koumoku_knj_" + side + fieldIndex;
            String naiyoKnjFieldName = "naiyo_knj_" + side + fieldIndex;
            String lineViewFlagFieldName = "line_view_flag_" + side + fieldIndex;

            // 表示する文字列を取得（存在しない場合は空白）
            String yymmYmdValue = (dataIndex < yymmYmdList.size()) ? yymmYmdList.get(
                    dataIndex)
                    : CommonConstants.BLANK_STRING;
            String koumokuKnjValue = (dataIndex < koumokuKnjList.size()) ? koumokuKnjList.get(
                    dataIndex)
                    : CommonConstants.BLANK_STRING;
            String naiyoKnjValue = (dataIndex < naiyoKnjList.size()) ? naiyoKnjList.get(
                    dataIndex)
                    : CommonConstants.BLANK_STRING;

            // 行末かどうかで罫線表示フラグを設定
            Boolean lineViewFlag = false;

            Class<?> clazz = dto.getClass();

            // 最終行かどうかを確認して、罫線を表示するかを判定する
            if (i == prtDataCnt + currentLine - 1) {
                lineViewFlag = true;
            }
            try {
                // 年月日
                Field yymmYmdField = clazz.getDeclaredField(yymmYmdFieldName);
                yymmYmdField.setAccessible(true);
                yymmYmdField.set(dto, yymmYmdValue);

                // 項目
                Field koumokuKnjField = clazz.getDeclaredField(koumokuKnjFieldName);
                koumokuKnjField.setAccessible(true);
                koumokuKnjField.set(dto, koumokuKnjValue);

                // 内容
                Field naiyoKnjField = clazz.getDeclaredField(naiyoKnjFieldName);
                naiyoKnjField.setAccessible(true);
                naiyoKnjField.set(dto, naiyoKnjValue);

                // 行間ライン表示フラウ
                Field lineViewFlagField = clazz.getDeclaredField(lineViewFlagFieldName);
                lineViewFlagField.setAccessible(true);
                lineViewFlagField.set(dto, lineViewFlag);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                System.err.println("フィールドに値を設定できません: " + yymmYmdFieldName);
                e.printStackTrace();
            }

            i++;
            dataIndex++;

            // 現在のページに収まらない場合（左右あわせて maxLine * 2 を超える場合）
            if (i > maxLine * 2) {
                // 残りの行数を再計算
                prtDataCnt = prtDataCnt + currentLine - i;

                // 行番号をリセット（新しいページの先頭から描画）
                i = 1;
                currentLine = 1;
                nextLine = prtDataCnt + currentLine;

                // 現在のページを複製し、新しいページとして追加
                SpecialistPassageR34ReportServiceInDto newPage = dto.copy();
                pageList.add(newPage);
            }
        }

        return nextLine;
    }

    /**
     * 空白行を出力用DTOに設定する
     *
     * 指定行から最大行数まで、年月日・内容を空白、line_view_flag を true にして設定する。
     *
     * @param startLine 開始行
     * @param dto       出力対象のDTO
     * @param maxLine   最大行数（左右片側分）
     */
    void renderContentEmptyLineByLine(int startLine, SpecialistPassageR34ReportServiceInDto dto, int maxLine) {

        for (int i = startLine; i <= maxLine * 2; i++) {
            String side = i <= maxLine ? "left" : "right";
            int fieldIndex = i <= maxLine ? i : i - maxLine;

            String yymmYmdFieldName = "yymm_ymd_" + side + fieldIndex;
            String koumokuKnjFieldName = "koumoku_knj_" + side + fieldIndex;
            String naiyoKnjFieldName = "naiyo_knj_" + side + fieldIndex;
            String lineViewFlagFieldName = "line_view_flag_" + side + fieldIndex;

            String yymmYmdValue = CommonConstants.BLANK_STRING;
            String koumokuKnjValue = CommonConstants.BLANK_STRING;
            String naiyoKnjValue = CommonConstants.BLANK_STRING;
            Boolean lineViewFlag = true;

            Class<?> clazz = dto.getClass();

            try {
                // 年月日
                Field yymmYmdField = clazz.getDeclaredField(yymmYmdFieldName);
                yymmYmdField.setAccessible(true);
                yymmYmdField.set(dto, yymmYmdValue);

                // 項目
                Field koumokuKnjField = clazz.getDeclaredField(koumokuKnjFieldName);
                koumokuKnjField.setAccessible(true);
                koumokuKnjField.set(dto, koumokuKnjValue);

                // 内容
                Field naiyoKnjField = clazz.getDeclaredField(naiyoKnjFieldName);
                naiyoKnjField.setAccessible(true);
                naiyoKnjField.set(dto, naiyoKnjValue);

                // 行間ライン表示フラウ
                Field lineViewFlagField = clazz.getDeclaredField(lineViewFlagFieldName);
                lineViewFlagField.setAccessible(true);
                lineViewFlagField.set(dto, lineViewFlag);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                System.err.println("フィールドに値を設定できません: " + yymmYmdFieldName);
                e.printStackTrace();
            }
        }
    }

    /**
     * 指定された日付の曜日を取得する。
     * JavaのDayOfWeekは月曜日を1、日曜日を7としている。
     * このメソッドではそのままの値（1〜7）を返す。
     *
     * @param date 対象の日付（LocalDate型）
     * @return 曜日を数値で返す（月曜: 1 ～ 日曜: 7）
     */
    public static int getDayOfWeek(LocalDate date) {
        int javaDayOfWeek = date.getDayOfWeek().getValue(); // MONDAY=1 ～ SUNDAY=7
        return javaDayOfWeek;
    }

    /**
     * 指定された日付の曜日を日本語で返す。
     * 例: 日曜日、月曜日、火曜日、...
     *
     * @param date 対象の日付（LocalDate型）
     * @return 日本語の曜日名
     */
    public static String getJapaneseDayOfWeek(String dateStr) {
        String formatDateString = ReportUtil.convertDateFormat(dateStr);
        if (formatDateString == null) {
            return CommonConstants.BLANK_STRING;
        }
        // 西暦日付
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(ReportConstants.DATE_FORMAT_YYYY_MM_DD);
        LocalDate dateTime = LocalDate.parse(formatDateString, inputFormatter);

        DayOfWeek dayOfWeek = dateTime.getDayOfWeek();
        switch (dayOfWeek) {
            case SUNDAY:
                return CommonConstants.YOUBI_NAMES[6] + "曜日";
            case MONDAY:
                return CommonConstants.YOUBI_NAMES[0] + "曜日";
            case TUESDAY:
                return CommonConstants.YOUBI_NAMES[1] + "曜日";
            case WEDNESDAY:
                return CommonConstants.YOUBI_NAMES[2] + "曜日";
            case THURSDAY:
                return CommonConstants.YOUBI_NAMES[3] + "曜日";
            case FRIDAY:
                return CommonConstants.YOUBI_NAMES[4] + "曜日";
            case SATURDAY:
                return CommonConstants.YOUBI_NAMES[5] + "曜日";
            default:
                return CommonConstants.BLANK_STRING; // 通常はここに来ない
        }
    }

    /**
     * 指定された時と分を「HH:mm」形式の文字列で返す。
     * どちらかがnullの場合、空文字列を返す。
     *
     * @param hour   時（0～23）※null可
     * @param minute 分（0～59）※null可
     * @return 「HH:mm」形式の文字列（例: "09:05", "14:30"）
     */
    public static String formatTime(Integer hour, Integer minute) {
        if (hour == null || minute == null) {
            return CommonConstants.BLANK_STRING;
        }
        return String.format("%02d:%02d", hour, minute);
    }

    /**
     * 指定されたフォントと最大ピクセル幅に基づいて、文字列を折り返す処理
     *
     * 改行（\n）を考慮したうえで、文字列を1行ずつ指定幅内に収まるように分割します。
     *
     * @param text     折り返す対象の文字列
     * @param font     描画に使用するフォント
     * @param maxWidth 折り返しの最大ピクセル幅
     * @return 折り返された文字列のリスト（各要素が1行に相当）
     */
    public static List<String> wrapTextByPixelWidth(String text, Font font, int maxWidth) {
        List<String> lines = new ArrayList<>();
        if (text == null)
            return lines;

        BufferedImage img = new BufferedImage(1, 1, BufferedImage.TYPE_INT_ARGB);
        Graphics g = img.getGraphics();
        g.setFont(font);
        FontMetrics fm = g.getFontMetrics();

        // まず明示的な改行で分割
        String[] paragraphs = text.split("\n");

        for (String para : paragraphs) {
            StringBuilder currentLine = new StringBuilder();

            for (char ch : para.toCharArray()) {
                currentLine.append(ch);

                // 全角スペースは特別扱い
                if (ch == '　') {
                    if (fm.stringWidth(currentLine.toString()) > maxWidth) {
                        lines.add(currentLine.substring(0, currentLine.length() - 1));
                        currentLine = new StringBuilder().append(ch);
                    }
                    continue;
                }

                if (fm.stringWidth(currentLine.toString()) > maxWidth) {
                    lines.add(currentLine.substring(0, currentLine.length() - 1));
                    currentLine = new StringBuilder().append(ch);
                }
            }

            if (currentLine.length() > 0) {
                lines.add(currentLine.toString());
            }
        }

        g.dispose();
        return lines;
    }

    /**
     * 帳票出力
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final SpecialistPassageR34ReportParameterModel model,
            final SpecialistPassageR34ReportServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final SpecialistPassageR34ReportServiceInDto reportParameter = (SpecialistPassageR34ReportServiceInDto) getReportParameters(
                model, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(getFwProps(),
                        ReportConstants.JRXML_SPECIALIST_PASSAGE_R34)));

        // コンパイル
        final JasperReport jasperFile = JasperCompileManager.compileReport(is);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile,
                reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(model, jasperPrint);

        super.setFilepath(model, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }
}
