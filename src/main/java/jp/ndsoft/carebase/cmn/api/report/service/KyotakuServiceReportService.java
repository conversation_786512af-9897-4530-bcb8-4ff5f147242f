package jp.ndsoft.carebase.cmn.api.report.service;

import java.io.File;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.report.dto.KyotakuServiceReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.KyotakuServiceReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.logic.KyotakuServiceReportLogic;
import jp.ndsoft.carebase.cmn.api.report.model.KyotakuServiceReportParameterModel;
import jp.ndsoft.carebase.report.util.JasperUtil;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.report.service.PdfReportServiceImpl;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * @since 2025.07.17
 * <AUTHOR> 李晨昊
 *         U0082K_居宅サービス計画書（２）
 */
@Service("KyotakuServiceReport")
public class KyotakuServiceReportService
        extends
        PdfReportServiceImpl<KyotakuServiceReportParameterModel, KyotakuServiceReportServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** アセスメント(居宅) 帳票出力 ロジッククラス */
    @Autowired
    private KyotakuServiceReportLogic kyotakuServiceReportLogic;

    /**
     * 帳票パラメータ取得
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    @Override
    protected Object getReportParameters(KyotakuServiceReportParameterModel model,
            KyotakuServiceReportServiceOutDto outDto) throws Exception {
        LOG.info(Constants.START);

        // ノート情報格納配列
        List<KyotakuServiceReportServiceInDto> kyotakuServiceReportInfoList = new ArrayList<KyotakuServiceReportServiceInDto>();
        // U06200_ケアアセスメント(1)帳票パラメータ取得
        KyotakuServiceReportServiceInDto infoInDto = this.kyotakuServiceReportLogic
                .getKyotakuServiceReportParameters(model, outDto, getFwProps());

        // 帳票用のDataSourceを設定する
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(kyotakuServiceReportInfoList);
        infoInDto.setDataSource(dataSource);

        LOG.info(Constants.END);

        return infoInDto;
    }

    /**
     * 帳票出力
     * 
     * @param inDto  入力データ
     * @param outDto 出力データ
     * @throws Exception 例外
     */
    @Override
    protected void outputReport(final KyotakuServiceReportParameterModel model,
            final KyotakuServiceReportServiceOutDto outDto)
            throws Exception {

        LOG.info(Constants.START);

        // 帳票に出力するデータ群の取得
        final KyotakuServiceReportServiceInDto reportParameter = (KyotakuServiceReportServiceInDto) getReportParameters(
                model, outDto);

        // 報告先分の帳票データを1PDFデータに集約する
        final JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();

        // コンパイル
        final JasperReport jasperFile = kyotakuServiceReportLogic.getJasperReport(getFwProps(), reportParameter, model);

        // 帳票出力
        final JasperPrint jasperPrint = JasperFillManager.fillReport(jasperFile,
                reportParameter.getParameters(),
                dataSource);

        // PDFファイルの出力
        final File pdf = JasperUtil.outputPdf(model, jasperPrint);

        /*
         * =============== 6.レスポンスを返却===============
         * 
         */
        super.setFilepath(model, outDto, pdf, pdf.getName());

        LOG.info(Constants.END);
    }

}
