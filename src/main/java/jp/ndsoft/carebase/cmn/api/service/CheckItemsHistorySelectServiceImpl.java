package jp.ndsoft.carebase.cmn.api.service;

import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.IntStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckItemInfo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckMainRireki;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckNaiyou;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892CheckPoint;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00892RirekiInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.CheckItemsHistorySelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.CheckItemsHistorySelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChkMainInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChkMainInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChkMainRirekiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChkMainRirekiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkCheckNaiyouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkCheckNaiyouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucCheckRirekiSelectInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghTucCheckRirekiSelectInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ChkMainInfoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ChkMainRirekiSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocCheckNaiyouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghTucCheckRirekiSelectInfoSelectMapper;
import jp.ndsoft.smh.framework.common.Constants;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;

/**
 * @since 2025.06.03
 * <AUTHOR>
 * @implNote GUI00892_チェック項目画面 履歴変更情報取得
 */
@Service
public class CheckItemsHistorySelectServiceImpl extends
        SelectServiceImpl<CheckItemsHistorySelectServiceInDto, CheckItemsHistorySelectServiceOutDto> {
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** チェック項目ヘッダ履歴情報取得 */
    @Autowired
    private KghTucCheckRirekiSelectInfoSelectMapper kghTucCheckRirekiSelectInfoSelectMapper;

    /** チェック項目評価内容マスタ情報取得 */
    @Autowired
    private KghMocCheckNaiyouSelectMapper kghMocCheckNaiyouSelectMapper;

    /** チェック項目詳細情報を取得する */
    @Autowired
    private ChkMainInfoSelectMapper chkMainInfoSelectMapper;

    /** チェックメイン履歴情報を取得する */
    @Autowired
    private ChkMainRirekiSelectMapper chkMainRirekiSelectMapper;

    /**
     * 履歴変更情報取得
     * 
     * @param inDto 履歴変更情報取得サービス入力Dto
     * @return 履歴変更情報取得サービス出力Dto
     * @throws Exception Exception
     */
    @Override
    protected CheckItemsHistorySelectServiceOutDto mainProcess(CheckItemsHistorySelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);

        CheckItemsHistorySelectServiceOutDto outDto = new CheckItemsHistorySelectServiceOutDto();

        // チェック項目ヘッダ履歴情報
        Gui00892RirekiInfo rirekiInfo = new Gui00892RirekiInfo();

        // チェック項目評価内容マスタ情報
        HashMap<String, List<Gui00892CheckNaiyou>> checkNaiyouInfoMap = new HashMap<>();

        // 評価点数リスト
        HashMap<String, List<Gui00892CheckPoint>> checkPointsMap = new HashMap<>();

        // チェック項目詳細情報
        List<Gui00892CheckItemInfo> checkItemInfo = new ArrayList<Gui00892CheckItemInfo>();

        // チェックメイン履歴情報
        List<Gui00892CheckMainRireki> checkMainRireki = new ArrayList<Gui00892CheckMainRireki>();

        // 【変数】.様式ID
        String assId = null;
        // 【変数】.様式ID
        String cstId = null;
        // 【変数】.法人ID
        String houjinId = null;
        // 【変数】.履歴の作成日
        String createYmd = null;

        /*
         * ===============1.単項目チェック以外の入力チェック===============
         * 
         */
        // 特になし

        /*
         * ===============2. チェック項目ヘッダ履歴情報の取得===============
         * 
         */
        // 2.1. 下記のDAOを利用してチェック項目ヘッダ履歴情報を取得する。
        List<KghTucCheckRirekiSelectInfoOutEntity> kghTucCheckRirekiSelectInfoList = this
                .findKghTucCheckRirekiSelectInfoByCriteria(inDto);

        // 2.3. 取得したチェック項目ヘッダ履歴情報の件数が1件以上の場合、以下のレスポンス情報を設定する
        if (CollectionUtils.isNotEmpty(kghTucCheckRirekiSelectInfoList)) {
            rirekiInfo = this.getRirekiInfo(kghTucCheckRirekiSelectInfoList, inDto);

            // 【変数】.履歴ID = 「3.」で取得したチェック項目履歴情報の一行目.履歴ID
            assId = rirekiInfo.getAssId();
            // 【変数】.様式ID = 「3.」で取得したチェック項目履歴情報の一行目.様式ID
            cstId = rirekiInfo.getCstId();
            // 【変数】.法人ID = 「3.」で取得したチェック項目履歴情報の一行目.法人ID
            houjinId = rirekiInfo.getHoujinId();
            // 【変数】.履歴の作成日 = 「3.」で取得したチェック項目履歴情報の一行目.履歴IDの作成日
            createYmd = rirekiInfo.getCreateYmd();

        } else {
            // 2.2. 取得したチェック項目ヘッダ履歴情報の件数が0件の場合、
            LOG.info(Constants.START);

            return outDto;
        }

        /*
         * ===============「2.」で取得したチェック項目履歴情報の件数が1件以上の場合、===============
         * 
         */
        if (CollectionUtils.isNotEmpty(kghTucCheckRirekiSelectInfoList)) {
            // 5.1. 下記のDAOを利用してチェック項目評価内容マスタ情報を取得する。
            List<KghKrkCheckNaiyouOutEntity> checkNaiyouInfoList = this.getCheckNaiyouInfo(inDto, houjinId, cstId,
                    checkNaiyouInfoMap, checkPointsMap);

            // 5.2. 下記のDAOを利用してチェック項目詳細情報を取得する。
            checkItemInfo = this.getCheckItemInfo(inDto, houjinId, assId, checkNaiyouInfoList,
                    checkNaiyouInfoMap,
                    checkPointsMap);

            // 5.3. 下記のDAOを利用してチェックメイン履歴情報を取得する。
            checkMainRireki = this.getCheckMainRireki(inDto, houjinId, createYmd, assId, checkItemInfo);

        }

        /*
         * ===============6. レスポンスを返却する。===============
         * 
         */

        // チェック項目ヘッダ履歴情報
        outDto.setRirekiInfo(rirekiInfo);

        // チェック項目詳細情報
        outDto.setCheckItemInfo(checkItemInfo);

        // チェックメイン履歴情報
        outDto.setCheckMainRireki(checkMainRireki);

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * チェック項目ヘッダ履歴情報リスト取得
     * 
     * @param inDto 計画期間変更情報取得サービス入力Dto
     * @param sc1Id 変数.期間ID
     * 
     * @return チェック項目ヘッダ履歴情報リスト
     */
    private List<KghTucCheckRirekiSelectInfoOutEntity> findKghTucCheckRirekiSelectInfoByCriteria(
            CheckItemsHistorySelectServiceInDto inDto) {
        KghTucCheckRirekiSelectInfoByCriteriaInEntity kghTucCheckRirekiSelectInfoByCriteriaInEntity = new KghTucCheckRirekiSelectInfoByCriteriaInEntity();
        // 計画期間ID
        kghTucCheckRirekiSelectInfoByCriteriaInEntity.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // 事業者ID
        kghTucCheckRirekiSelectInfoByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        kghTucCheckRirekiSelectInfoByCriteriaInEntity.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));

        List<KghTucCheckRirekiSelectInfoOutEntity> kghTucCheckRirekiSelectInfoList = this.kghTucCheckRirekiSelectInfoSelectMapper
                .findKghTucCheckRirekiSelectInfoByCriteria(
                        kghTucCheckRirekiSelectInfoByCriteriaInEntity);

        return kghTucCheckRirekiSelectInfoList;

    }

    /**
     * チェック項目ヘッダ履歴情報リスト取得
     * 
     * @param kghTucCheckRirekiSelectInfoList チェック項目ヘッダ履歴情報リスト
     * @param inDto                           計画期間変更情報取得サービス入力Dto
     * 
     * @return チェック項目ヘッダ履歴情報
     */
    private Gui00892RirekiInfo getRirekiInfo(
            List<KghTucCheckRirekiSelectInfoOutEntity> kghTucCheckRirekiSelectInfoList,
            CheckItemsHistorySelectServiceInDto inDto) {
        Integer assId = null;
        Integer index = null;

        index = getIndexByAssId(kghTucCheckRirekiSelectInfoList, inDto.getAss1Id());

        // リクエストパラメータ.履歴IDが「2.1」のチェック項目ヘッダ履歴情報リストに存在しない場合
        if (index == -1) {
            // 変数.履歴IDに「2.1」のチェック項目ヘッダ履歴情報リストの先頭行の履歴IDを設定する
            assId = kghTucCheckRirekiSelectInfoList.get(0).getAssId();

        } else {
            // リクエストパラメータ.履歴IDが「2.1」のチェック項目ヘッダ履歴情報リストに存在する場合 且つ
            // リクエストパラメータ.履歴変更区分＝0の場合（履歴選択画面から履歴IDが指定される）
            if (CommonDtoUtil.checkStringEqual(inDto.getPageFlag(), CommonConstants.PLAN_PERIOD_PAGE_TYPE_0)) {
                // 変数.履歴IDにリクエストパラメータ.履歴IDを設定する
                assId = CommonDtoUtil.strValToInt(inDto.getAss1Id());

            }
            // リクエストパラメータ.履歴変更区分 ＝1の場合（前ページをクリックする）
            else if (CommonDtoUtil.checkStringEqual(inDto.getPageFlag(), CommonConstants.PLAN_PERIOD_PAGE_TYPE_1)) {
                // ※指定履歴IDは先頭行の場合、変数.履歴IDにリクエストパラメータ.履歴IDを設定する
                if (index == 0) {
                    assId = CommonDtoUtil.strValToInt(inDto.getAss1Id());
                } else {
                    // 変数.履歴IDに「「2.1」のチェック項目ヘッダ履歴情報リストから指定履歴IDの一つ前の履歴IDを設定する
                    assId = kghTucCheckRirekiSelectInfoList.get(index - 1).getAssId();
                }

            }
            // リクエストパラメータ.履歴変更区分 ＝2の場合（後ページをクリックする）
            else if (CommonDtoUtil.checkStringEqual(inDto.getPageFlag(), CommonConstants.PLAN_PERIOD_PAGE_TYPE_2)) {
                // ※指定履歴IDは最終行の場合、変数.履歴IDにリクエストパラメータ.履歴IDを設定する
                if (index == kghTucCheckRirekiSelectInfoList.size() - 1) {
                    assId = CommonDtoUtil.strValToInt(inDto.getAss1Id());
                } else {
                    // 変数.履歴IDに「2.1」のチェック項目ヘッダ履歴情報リストから指定履歴IDの一つ後の履歴IDを設定する
                    assId = kghTucCheckRirekiSelectInfoList.get(index + 1).getAssId();
                }

            }

        }
        // 2.4. 変数.履歴IDの情報をレスポンスパラメータに設定する
        Gui00892RirekiInfo rirekiInfo = new Gui00892RirekiInfo();

        index = getIndexByAssId(kghTucCheckRirekiSelectInfoList, CommonDtoUtil.objValToString(assId));

        // 法人ID
        rirekiInfo.setHoujinId(CommonDtoUtil.objValToString(kghTucCheckRirekiSelectInfoList.get(index).getHoujinId()));
        // 施設ID
        rirekiInfo.setShisetuId(CommonDtoUtil.objValToString(
                kghTucCheckRirekiSelectInfoList.get(index).getShisetuId()));
        // 事業者ID
        rirekiInfo.setSvJigyoId(CommonDtoUtil.objValToString(
                kghTucCheckRirekiSelectInfoList.get(index).getSvJigyoId()));
        // 利用者ID
        rirekiInfo.setUserId(CommonDtoUtil.objValToString(kghTucCheckRirekiSelectInfoList.get(index).getUserid()));
        // 計画期間ID
        rirekiInfo.setSc1Id(CommonDtoUtil.objValToString(kghTucCheckRirekiSelectInfoList.get(index).getSc1Id()));
        // 履歴ID
        rirekiInfo.setAssId(CommonDtoUtil.objValToString(kghTucCheckRirekiSelectInfoList.get(index).getAssId()));
        // 様式ID
        rirekiInfo.setCstId(CommonDtoUtil.objValToString(kghTucCheckRirekiSelectInfoList.get(index).getCstId()));
        // 作成日
        rirekiInfo.setCreateYmd(CommonDtoUtil.objValToString(
                kghTucCheckRirekiSelectInfoList.get(index).getCreateYmd()));
        // 作成者
        rirekiInfo.setShokuId(CommonDtoUtil.objValToString(kghTucCheckRirekiSelectInfoList.get(index).getShokuId()));

        // 履歴総件数
        rirekiInfo.setRowCount(CommonDtoUtil.objValToString(kghTucCheckRirekiSelectInfoList.size()));

        return rirekiInfo;

    }

    /**
     * チェック項目評価内容マスタ情報を取得する。
     * 
     * @param inDto              初期情報取得サービス入力Dto
     * @param houjinId           法人ID
     * @param cstId              様式ID
     * @param checkNaiyouInfoMap チェック項目評価内容マスタ情報マップ
     * @param checkPointsMap     チェック項目評価点数マスタ情報マップ
     * @return チェック項目評価内容マスタ情報
     */
    private List<KghKrkCheckNaiyouOutEntity> getCheckNaiyouInfo(CheckItemsHistorySelectServiceInDto inDto,
            String houjinId,
            String cstId, HashMap<String, List<Gui00892CheckNaiyou>> checkNaiyouInfoMap,
            HashMap<String, List<Gui00892CheckPoint>> checkPointsMap) {

        KghKrkCheckNaiyouByCriteriaInEntity kghKrkCheckNaiyouByCriteriaInEntity = new KghKrkCheckNaiyouByCriteriaInEntity();
        // 法人ID
        kghKrkCheckNaiyouByCriteriaInEntity.setHoujinId(CommonDtoUtil.strValToInt(houjinId));
        // 施設ID
        kghKrkCheckNaiyouByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        kghKrkCheckNaiyouByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 様式ID
        kghKrkCheckNaiyouByCriteriaInEntity.setYoushikiId(CommonDtoUtil.strValToInt(cstId));

        List<KghKrkCheckNaiyouOutEntity> kghKrkCheckNaiyouList = this.kghMocCheckNaiyouSelectMapper
                .findKghKrkCheckNaiyouByCriteria(kghKrkCheckNaiyouByCriteriaInEntity);

        if (CollectionUtils.isNotEmpty(kghKrkCheckNaiyouList)) {
            kghKrkCheckNaiyouList.forEach(kghKrkCheckNaiyou -> {
                // 5.1.1.下記処理中の変数初期化を行う
                Gui00892CheckNaiyou checkNaiyouInfoItem = new Gui00892CheckNaiyou();
                // 5.1.3.取得したチェック項目評価内容マスタ情報件数分で項目ID,細目ID毎に下記を行う

                // 内容ID
                checkNaiyouInfoItem.setNaiyouId(CommonDtoUtil.objValToString(kghKrkCheckNaiyou.getNaiyouId()));
                // 内容名称
                checkNaiyouInfoItem.setNaiyouKnj(CommonDtoUtil.objValToString(kghKrkCheckNaiyou.getNaiyouKnj()));
                // 評価点数を新規作成して下記項目値を設定する
                Gui00892CheckPoint checkPoint = new Gui00892CheckPoint();
                // 内容ID
                checkPoint.setNaiyouId(CommonDtoUtil.objValToString(kghKrkCheckNaiyou.getNaiyouId()));
                // 評価点数
                checkPoint.setPoint(CommonDtoUtil.objValToString(kghKrkCheckNaiyou.getPoint()));
                String key = String.format("%d%d", kghKrkCheckNaiyou.getKoumokuId(), kghKrkCheckNaiyou.getSaimokuId());
                // マップに追加する
                checkNaiyouInfoMap.putIfAbsent(key, new ArrayList<>());
                checkNaiyouInfoMap.get(key).add(checkNaiyouInfoItem);
                checkPointsMap.putIfAbsent(key, new ArrayList<>());
                checkPointsMap.get(key).add(checkPoint);
            });

        }
        return kghKrkCheckNaiyouList;

    }

    /**
     * チェック項目詳細情報を取得する。
     * 
     * @param inDto               初期情報取得サービス入力Dto
     * @param houjinId            法人ID
     * @param sc1Id               期間ID
     * @param assId               履歴ID
     * @param checkItemInfoList   チェック項目情報リスト
     * @param checkNaiyouInfoList チェック項目評価内容マスタ情報
     * @param checkNaiyouInfoMap  チェック項目評価内容マスタ
     * 
     * @return チェック項目詳細情報
     */
    private List<Gui00892CheckItemInfo> getCheckItemInfo(CheckItemsHistorySelectServiceInDto inDto, String houjinId,
            String assId, List<KghKrkCheckNaiyouOutEntity> checkNaiyouInfoList,
            HashMap<String, List<Gui00892CheckNaiyou>> checkNaiyouInfoMap,
            HashMap<String, List<Gui00892CheckPoint>> checkPointsMap) {
        // チェック項目詳細情報
        List<Gui00892CheckItemInfo> checkItemInfo = new ArrayList<Gui00892CheckItemInfo>();

        ChkMainInfoByCriteriaInEntity chkMainInfoByCriteriaInEntity = new ChkMainInfoByCriteriaInEntity();
        // 法人ID
        chkMainInfoByCriteriaInEntity.setHoujinId(CommonDtoUtil.strValToInt(houjinId));
        // 施設ID
        chkMainInfoByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        chkMainInfoByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        chkMainInfoByCriteriaInEntity.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 計画期間ID
        chkMainInfoByCriteriaInEntity.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // 履歴ID
        chkMainInfoByCriteriaInEntity.setAssId(CommonDtoUtil.strValToInt(assId));

        List<ChkMainInfoOutEntity> chkMainInfoList = this.chkMainInfoSelectMapper
                .findChkMainInfoByCriteria(chkMainInfoByCriteriaInEntity);

        if (CollectionUtils.isNotEmpty(chkMainInfoList)) {
            chkMainInfoList.forEach(chkMainInfo -> {
                Gui00892CheckItemInfo checkItemInfoItem = new Gui00892CheckItemInfo();
                // 履歴ID
                checkItemInfoItem.setAssId(CommonDtoUtil.objValToString(chkMainInfo.getAssId()));
                // 作成日
                checkItemInfoItem.setCreateYmd(chkMainInfo.getCreateYmd());
                // 項目名称
                checkItemInfoItem.setKoumokuKnj(chkMainInfo.getKoumokuKnj());
                // 細目名称
                checkItemInfoItem.setSaimokuKnj(chkMainInfo.getSaimokuKnj());
                // 課題対象フラグ
                checkItemInfoItem.setKadaiFlg(CommonDtoUtil.objValToString(chkMainInfo.getKadaiFlg()));
                // 備考
                checkItemInfoItem.setBiko(chkMainInfo.getBiko());
                // 項目ID
                checkItemInfoItem.setKoumokuId(CommonDtoUtil.objValToString(chkMainInfo.getKoumokuId()));
                // 細目ID
                checkItemInfoItem.setSaimokuId(CommonDtoUtil.objValToString(chkMainInfo.getSaimokuId()));
                // 内容ID
                checkItemInfoItem.setNaiyoId(CommonDtoUtil.objValToString(chkMainInfo.getNaiyoId()));
                // 5.2.1.1.チェック項目詳細情報の内容IDの値が空白またはNULLの場合
                if (Objects.isNull(chkMainInfo.getNaiyoId())) {
                    // チェック項目詳細情報.点数=""
                    checkItemInfoItem.setPoint("");
                    // チェック項目詳細情報.内容名称=""
                    checkItemInfoItem.setNaiyouKnj("");
                }
                // 5.2.1.2.チェック項目詳細情報の内容IDの値が上記以外の場合
                else {
                    // チェック項目詳細情報の項目ID、細目ID、内容IDとチェック項目評価内容マスタ情報の項目ID、細目ID、内容IDと同じのチェック項目評価内容マスタ情報を取得する
                    checkNaiyouInfoList.stream().filter(naiyou -> naiyou.getKoumokuId() == chkMainInfo.getKoumokuId()
                            && naiyou.getSaimokuId() == chkMainInfo.getSaimokuId()
                            && naiyou.getNaiyouId() == chkMainInfo.getNaiyoId())
                            .findFirst().ifPresent(matchedNaiyou -> {
                                // チェック項目詳細情報の下記項目に値設定する
                                // チェック項目詳細情報.点数=チェック項目評価内容マスタ情報.点数
                                checkItemInfoItem.setPoint(CommonDtoUtil.objValToString(matchedNaiyou.getPoint()));
                                // チェック項目詳細情報.内容名称=チェック項目評価内容マスタ情報.内容名称
                                checkItemInfoItem
                                        .setNaiyouKnj(CommonDtoUtil.objValToString(matchedNaiyou.getNaiyouKnj()));
                            });
                    // 5.2.1.3.チェック項目詳細情報の項目ID、細目IDにより変数.評価内容マップから評価内容リストを取得する
                    // チェック項目詳細情報.評価内容リストに取得した評価内容リストを設定する
                    String key = String.format("%d%d", chkMainInfo.getKoumokuId(), chkMainInfo.getSaimokuId());
                    checkItemInfoItem.setCheckNaiyouList(checkNaiyouInfoMap.getOrDefault(key, new ArrayList<>()));
                    // 5.2.1.4チェック項目詳細情報の項目ID、細目IDにより変数.評価点数マップから評価点数リストを取得する
                    // チェック項目詳細情報.評価点数リストに取得した評価点数リストを設定する
                    checkItemInfoItem.setCheckPointList(checkPointsMap.getOrDefault(key, new ArrayList<>()));
                }

                checkItemInfo.add(checkItemInfoItem);

            });
        }
        return checkItemInfo;
    }

    /**
     * チェックメイン履歴情報を取得する。
     * 
     * @param inDto             初期情報取得サービス入力Dto
     * @param houjinId          法人ID
     * @param sc1Id             期間ID
     * @param createYmd         履歴の作成日
     * @param assId             履歴ID
     * @param checkItemInfoList チェック項目情報リスト
     * 
     * @return チェックメイン履歴情報
     */
    private List<Gui00892CheckMainRireki> getCheckMainRireki(CheckItemsHistorySelectServiceInDto inDto, String houjinId,
            String createYmd, String assId, List<Gui00892CheckItemInfo> checkItemInfoList) {
        // チェックメイン履歴情報
        List<Gui00892CheckMainRireki> checkMainRireki = new ArrayList<Gui00892CheckMainRireki>();

        ChkMainRirekiByCriteriaInEntity chkMainRirekiByCriteriaInEntity = new ChkMainRirekiByCriteriaInEntity();
        // 法人ID
        chkMainRirekiByCriteriaInEntity.setHoujinId(CommonDtoUtil.strValToInt(houjinId));
        // 施設ID
        chkMainRirekiByCriteriaInEntity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        chkMainRirekiByCriteriaInEntity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        chkMainRirekiByCriteriaInEntity.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 計画期間ID
        chkMainRirekiByCriteriaInEntity.setSc1Id(CommonDtoUtil.strValToInt(inDto.getSc1Id()));
        // 履歴の作成日付
        chkMainRirekiByCriteriaInEntity.setCreateYmd(createYmd);
        // 履歴ID
        chkMainRirekiByCriteriaInEntity.setAssId(CommonDtoUtil.strValToInt(assId));

        List<ChkMainRirekiOutEntity> chkMainRirekiList = this.chkMainRirekiSelectMapper
                .findChkMainRirekiByCriteria(chkMainRirekiByCriteriaInEntity);

        if (CollectionUtils.isNotEmpty(chkMainRirekiList)) {
            chkMainRirekiList.forEach(chkMainRireki -> {
                if (checkItemInfoList.stream().anyMatch(item -> item.getKoumokuId()
                        .equals(CommonDtoUtil.objValToString(chkMainRireki.getKomokuId()))
                        && item.getSaimokuId().equals(CommonDtoUtil.objValToString(chkMainRireki.getSaimokuId())))) {

                    Gui00892CheckMainRireki checkMainRirekiItem = new Gui00892CheckMainRireki();
                    // 法人ID
                    checkMainRirekiItem.setHoujinId(CommonDtoUtil.objValToString(chkMainRireki.getHoujinId()));
                    // 施設ID
                    checkMainRirekiItem.setShisetuId(CommonDtoUtil.objValToString(chkMainRireki.getShisetuId()));
                    // 事業者ID
                    checkMainRirekiItem.setSvJigyoId(CommonDtoUtil.objValToString(chkMainRireki.getSvJigyoId()));
                    // 利用者ID
                    checkMainRirekiItem.setUserid(CommonDtoUtil.objValToString(chkMainRireki.getUserid()));
                    // 計画期間ID
                    checkMainRirekiItem.setSc1Id(CommonDtoUtil.objValToString(chkMainRireki.getSc1Id()));
                    // 履歴ID
                    checkMainRirekiItem.setAssId(CommonDtoUtil.objValToString(chkMainRireki.getAssId()));
                    // 項目ID
                    checkMainRirekiItem.setKomokuId(CommonDtoUtil.objValToString(chkMainRireki.getKomokuId()));
                    // 細目ID
                    checkMainRirekiItem.setSaimokuId(CommonDtoUtil.objValToString(chkMainRireki.getSaimokuId()));
                    // 内容ID
                    checkMainRirekiItem.setNaiyoId(CommonDtoUtil.objValToString(chkMainRireki.getNaiyoId()));
                    // 作成日
                    checkMainRirekiItem.setCreateYmd(chkMainRireki.getCreateYmd());
                    // 点数
                    checkMainRirekiItem.setPoint(CommonDtoUtil.objValToString(chkMainRireki.getPoint()));

                    checkMainRireki.add(checkMainRirekiItem);
                }
            });
        }
        return checkMainRireki;
    }

    /**
     * 履歴IDよりindexを取得する。
     * 
     * @param kghTucKrkKikanList 計画対象期間情報リスト
     * @param ass1Id             履歴ID
     * 
     * @return index
     */
    private Integer getIndexByAssId(
            List<KghTucCheckRirekiSelectInfoOutEntity> kghTucCheckRirekiSelectInfoList, String ass1Id) {
        Integer index = IntStream.range(0, kghTucCheckRirekiSelectInfoList.size())
                .filter(i -> CommonDtoUtil.checkStringEqual(ass1Id,
                        CommonDtoUtil.objValToString(kghTucCheckRirekiSelectInfoList.get(i).getAssId())))
                .findFirst()
                .orElse(-1);

        return index;

    }

}
