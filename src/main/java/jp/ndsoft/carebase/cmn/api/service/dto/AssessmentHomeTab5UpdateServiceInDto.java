package jp.ndsoft.carebase.cmn.api.service.dto;

import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui00798HealthStateVisitSituationInfo;
import jp.ndsoft.carebase.cmn.api.logic.dto.KadaiInDto;
import jp.ndsoft.carebase.cmn.api.report.model.EdocumentDeleteReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.model.HomeAssessmentSheetAllReportParameterModel;
import jp.ndsoft.smh.framework.global.base.dto.IDtoImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025.05.07
 * <AUTHOR> 謝法文
 * @apiNote GUI00798_［アセスメント］画面（居宅）（5） データ保存サービス入力Dto
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AssessmentHomeTab5UpdateServiceInDto extends IDtoImpl {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;
    /** タブID */
    @NotEmpty
    private String tabId;

    /** 機能ID */
    @NotEmpty
    private String kinoId;

    /** 当履歴ページ番号 */
    @NotEmpty
    private String krirekiNo;

    /** e文書用パラメータ */
    @NotNull
    private HomeAssessmentSheetAllReportParameterModel edocumentUseParam;

    /** e文書削除用パラメータ */
    @NotNull
    private EdocumentDeleteReportParameterModel edocumentDeleteUseParam;

    /** 期間対象フラグ */
    @NotEmpty
    private String kikanFlg;

    /** 計画対象期間番号 */
    private String planningPeriodNo;

    /** 開始日 */
    private String startYmd;

    /** 終了日 */
    private String endYmd;

    /** ガイドラインまとめ */
    @NotEmpty
    private String matomeFlg;

    /** ログインID */
    @NotEmpty
    private String loginId;

    /** システム略称 */
    @NotEmpty
    private String sysRyaku;

    /** 職員ID */
    @NotEmpty
    private String shokuId;

    /** システムコード */
    @NotEmpty
    private String sysCd;

    /** 事業者名 */
    @NotEmpty
    private String svJigyoKnj;

    /** 作成者名 */
    @NotEmpty
    private String createUserName;

    /** 利用者名 */
    @NotEmpty
    private String userName;

    /** 法人ID */
    @NotEmpty
    private String houjinId;

    /** 施設ID */
    @NotEmpty
    private String shisetuId;

    /** 作成日 */
    @NotEmpty
    private String kijunbiYmd;

    /** 作成者ID */
    @NotEmpty
    private String sakuseiId;

    /** 利用者ID */
    @NotEmpty
    private String userId;

    /** 事業者ID */
    @NotEmpty
    private String svJigyoId;

    /** 計画対象期間ID */
    private String sc1Id;

    /** アセスメントID */
    @NotEmpty
    private String gdlId;

    /** 改定フラグ */
    @NotEmpty
    private String ninteiFlg;

    /** 削除処理区分 */
    @NotEmpty
    private String deleteKbn;

    /** 更新区分 */
    @NotEmpty
    private String updateKbn;

    /** 履歴更新区分 */
    @NotEmpty
    private String historyUpdateKbn;

    /** 種別ID */
    @NotEmpty
    private String syubetsuId;

    /** 健康状態・受診等の状況情報 */
    private Gui00798HealthStateVisitSituationInfo healthStateVisitSituationInfo;

    /** 課題と目標情報リスト */
    @Valid
    private List<KadaiInDto> kadaiList;

}
