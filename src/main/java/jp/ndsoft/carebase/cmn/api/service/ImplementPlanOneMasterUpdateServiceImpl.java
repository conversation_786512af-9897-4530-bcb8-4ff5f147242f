package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.service.dto.ImplementPlanOneMasterUpdateServiceBunrui3SetDto;
import jp.ndsoft.carebase.cmn.api.service.dto.ImplementPlanOneMasterUpdateServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.ImplementPlanOneMasterUpdateServiceOutDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.smh.framework.global.base.service.UpdateServiceImpl;
import jp.ndsoft.smh.framework.global.exception.ExclusiveException;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghMocKrkSsmSelectMapper;
import jp.ndsoft.carebase.common.dao.mybatis.KghMocKrkSsmMapper;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KrkSsmInfoOutEntity;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsm;
import jp.ndsoft.carebase.common.dao.mybatis.entity.KghMocKrkSsmCriteria;

/**
 * GUI00945_実施計画①マスタ情報保存サービス.
 *
 * <AUTHOR>
 */
@Service
public class ImplementPlanOneMasterUpdateServiceImpl
        extends UpdateServiceImpl<ImplementPlanOneMasterUpdateServiceInDto, ImplementPlanOneMasterUpdateServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    // 初期設定マスタ情報取得
    @Autowired
    private KghMocKrkSsmSelectMapper kghMocKrkSsmSelectMapper;
    // 初期設定マスタ情報保存
    @Autowired
    private KghMocKrkSsmMapper kghMocKrkSsmMapper;

    /**
     * 保存処理
     * 
     * @param inDto
     * @return
     * @throws Exception Exception
     */
    @Override
    protected ImplementPlanOneMasterUpdateServiceOutDto mainProcess(ImplementPlanOneMasterUpdateServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // DTOOUT情報
        ImplementPlanOneMasterUpdateServiceOutDto outDto = new ImplementPlanOneMasterUpdateServiceOutDto();

        // 初期設定マスタ情報の取得
        KrkSsmInfoByCriteriaInEntity entity = new KrkSsmInfoByCriteriaInEntity();
        // 施設ID
        entity.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        entity.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getJigyoId()));
        // 分類1
        entity.setBunrui1Id(CommonConstants.BUNRUI1_ID_2);
        // 分類2
        entity.setBunrui2Id(CommonConstants.BUNRUI2_ID_14);
        List<KrkSsmInfoOutEntity> list = kghMocKrkSsmSelectMapper.findKrkSsmInfoByCriteria(entity);

        // リクエストパラメータ.分類3設定リストの件数分、下記処理を繰り返す。
        for (ImplementPlanOneMasterUpdateServiceBunrui3SetDto item : inDto.getBunrui3SetList()) {
            // 検索データが存在しない場合、初期設定マスタ登録処理
            if (list == null || list.size() == 0) {
                insert(inDto, item);
            } else {
                boolean exists = list.stream()
                        .anyMatch(x -> CommonDtoUtil.strValToInt(item.getBunrui3Id()).equals(x.getBunrui3Id()));
                // 分類3が上記「2.1. 初期設定マスタ情報取得」処理で取得した検索データが存在しない場合
                if (!exists) {
                    insert(inDto, item);
                } else {
                    update(inDto, item);
                }
            }
        }
        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 初期設定マスタの登録
     * 
     * @param inDto    inデータ
     * @param dataInfo 分類3設定
     * @param outDto   outデータ
     */
    private void insert(ImplementPlanOneMasterUpdateServiceInDto inDto,
            ImplementPlanOneMasterUpdateServiceBunrui3SetDto dataInfo) {

        KghMocKrkSsm kghMocKrkSsm = new KghMocKrkSsm();
        // 施設ID
        kghMocKrkSsm.setShisetuId(CommonDtoUtil.strValToInt(inDto.getShisetuId()));
        // 事業者ID
        kghMocKrkSsm.setSvJigyoId(CommonDtoUtil.strValToInt(inDto.getJigyoId()));
        // 分類1
        kghMocKrkSsm.setBunrui1Id(CommonConstants.BUNRUI1_ID_2);
        // 分類2
        kghMocKrkSsm.setBunrui2Id(CommonConstants.BUNRUI2_ID_14);
        // 分類3
        kghMocKrkSsm.setBunrui3Id(CommonDtoUtil.strValToInt(dataInfo.getBunrui3Id()));
        // 整数
        kghMocKrkSsm.setIntValue(CommonDtoUtil.strValToInt(dataInfo.getBunrui3Value()));
        // 小数点付
        kghMocKrkSsm.setDoubleValue(CommonConstants.DOUBLE_ZERO);
        // 共通カラム値設定処理
        // CommonDaoUtil.setInsertCommonColumns(kghMocKrkSsm);
        kghMocKrkSsmMapper.insertSelective(kghMocKrkSsm);
    }

    /**
     * 初期設定マスタの更新
     * 
     * @param inDto    inデータ
     * @param dataInfo 分類3設定
     * @param outDto   outデータ
     * @throws ExclusiveException
     */
    private void update(ImplementPlanOneMasterUpdateServiceInDto inDto,
            ImplementPlanOneMasterUpdateServiceBunrui3SetDto dataInfo) throws ExclusiveException {

        // BigInteger modifiedCnt = new BigInteger(dataInfo.getModifiedCnt());
        KghMocKrkSsmCriteria criteria = new KghMocKrkSsmCriteria();
        criteria.createCriteria()
                .andShisetuIdEqualTo(CommonDtoUtil.strValToInt(inDto.getShisetuId()))
                .andSvJigyoIdEqualTo(CommonDtoUtil.strValToInt(inDto.getJigyoId()))
                .andBunrui1IdEqualTo(CommonConstants.BUNRUI1_ID_2)
                .andBunrui2IdEqualTo(CommonConstants.BUNRUI2_ID_14)
                .andBunrui3IdEqualTo(CommonDtoUtil.strValToInt(dataInfo.getBunrui3Id()));
        // .andDelFlgEqualTo(CommonConstants.DEL_FLG_0)
        // .andModifiedCntEqualTo(modifiedCnt);

        KghMocKrkSsm kghMocKrkSsm = new KghMocKrkSsm();
        // 整数
        kghMocKrkSsm.setIntValue(CommonDtoUtil.strValToInt(dataInfo.getBunrui3Value()));
        // 共通カラム値設定処理
        // CommonDaoUtil.setUpdateCommonColumns(kghMocKrkSsm, modifiedCnt);
        int count = kghMocKrkSsmMapper.updateByCriteriaSelective(kghMocKrkSsm, criteria);
        if (count <= 0) {
            throw new ExclusiveException();
        }
    }
}
