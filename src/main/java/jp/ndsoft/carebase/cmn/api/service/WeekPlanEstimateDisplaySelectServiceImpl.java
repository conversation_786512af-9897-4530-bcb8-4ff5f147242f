package jp.ndsoft.carebase.cmn.api.service;

import jp.ndsoft.smh.framework.common.Constants;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import java.lang.invoke.MethodHandles;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01042DwBeppyo;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01042DwKakusyuu;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01042DwRiyou;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01042DwWeeks;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01042SerachResult;
import jp.ndsoft.carebase.cmn.api.dao.mybatis.entity.Gui01042Sum;
import jp.ndsoft.carebase.cmn.api.service.dto.ComDemandEngineConnectionCalculationProcSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Hoken;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149HoumonInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149JigyoInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149KaigoInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149KakuninInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149KeigenInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149KouhiInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149Over30Info;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149PlanInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149PlanRireki;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149RiyouProcess;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149ShuruiInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149SvplanPointInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149TankiInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149TrdInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01149UserInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01152UserInfo;
import jp.ndsoft.carebase.cmn.api.service.dto.Gui01152WeekPlanData;
import jp.ndsoft.carebase.cmn.api.logic.KghCmn03gFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmnWeek1Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.KghShrKsgFunc02Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.CmnUsrHok3gOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KghCmnUsrInfo3gDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.CmnUsrNin3gOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.GetUserInfoMonthOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.KyuufurituCmnOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.TermId2DateLogicOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.YokaiDaysOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekPlanEstimateDisplaySelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekPlanEstimateDisplaySelectServiceOutDto;
import jp.ndsoft.carebase.cmn.api.service.dto.WeekPlanSettingsSelectServiceInDto;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnComMscSvjigyoCByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnComMscSvjigyoCOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo1List202504ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo1List202504OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo2SyuruiSougouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo2SyuruiSougouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3KohiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3KohiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3SyafukuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnBeppyo3SyafukuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnKyuWeekAllGetSvsUnion2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnKyuWeekAllGetSvsUnion2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnPlanOv30ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnPlanOv30OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnPlnGetCpnWeekPlansKasanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnPlnGetCpnWeekPlansKasanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnRiyou2HeaderAcsByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnRiyou2HeaderAcsOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnRiyou5SumByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnRiyou5SumOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghSelSvitemCalc1PonNextSougouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghSelSvitemCalc1PonNextSougouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnRiyouHiwariSanteiRiyouByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnRiyouHiwariSanteiRiyouOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucPlanByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucPlanOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucPlanRiyouKakuninByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucPlanRiyouKakuninOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucSvPointByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucSvPointOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucSvplanShortByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnTucSvplanShortOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnWeekGetKakushuuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCmnWeekGetKakushuuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghSelSvitemCalc1PonNextByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghSelSvitemCalc1PonNextOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoshaOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SelSvjigyoCalc1PonByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SelSvjigyoCalc1PonOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.KghCmnKyuWeekAllGetSvsUnion2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanBetu1SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanBetu2SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanKouhiFutanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanOv30SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanRiyouKakuninSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanRiyouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanSyafukuKeigenSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucServicePointSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucSvPlanShortSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTlcSvPlanShortSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks55SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks58SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.RiyouSumSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ServiceJigyosyoInfoSelectMapper;
import jp.ndsoft.smh.framework.global.base.service.SelectServiceImpl;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSougouSelectMapper;

/**
 * GUI01042_API定義書_APINo(292)_概算金額情報取得
 * 
 * <AUTHOR>
 */
@Service
public class WeekPlanEstimateDisplaySelectServiceImpl extends
        SelectServiceImpl<WeekPlanEstimateDisplaySelectServiceInDto, WeekPlanEstimateDisplaySelectServiceOutDto> {
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 週間計画取り込み（処理年月優先用） */
    @Autowired
    KghCmnKyuWeekAllGetSvsUnion2SelectMapper KghCmnKyuWeekAllGetSvsUnion2SelectMapper;

    /** 28-28 週間計画表（加算データ） */
    @Autowired
    CpnTucCks55SelectMapper cpnTucCks55SelectMapper;

    /** 週間計画表(隔週)データ取得用 */
    @Autowired
    CpnTucCks58SelectMapper cpnTucCks58SelectMapper;

    /** 介護サービス費適用ﾏｽﾀからサービスコードを取得 */
    @Autowired
    KghCmn03gFunc01Logic kghCmn03gFunc01Logic;

    /** サービス利用票.別表ヘッダを取得 */
    @Autowired
    CmnTucPlanSelectMapper cmnTucPlanSelectMapper;

    /** サービス事業者マスタ（１５－５）を取得 */
    @Autowired
    ComMscSvjigyoSelectMapper comMscSvjigyoSelectMapper;

    /** 介護サービス費適用マスタ（２０－４）を取得 */
    @Autowired
    ComMhcItemuseSelectMapper comMhcItemuseSelectMapper;

    /** サービス利用票明細データを取得 */
    @Autowired
    CmnTucPlanRiyouSelectMapper cmnTucPlanRiyouSelectMapper;

    /** 短期入所連続利用30日超過情報を取得する */
    @Autowired
    CmnTucPlanOv30SelectMapper cmnTucPlanOv30SelectMapper;

    /** 20-20 介護予防.日常生活支援総合事業サービス費適用マスタ */
    @Autowired
    ComMhcItemuseSougouSelectMapper comMhcItemuseSougouSelectMapper;

    /** 利用票別表:提供事業所毎小計を取得する */
    @Autowired
    CmnTucServicePointSelectMapper cmnTucServicePointSelectMapper;

    /** 短期利用日数保持を取得する */
    @Autowired
    CmnTucSvPlanShortSelectMapper cmnTucSvPlanShortSelectMapper;

    /** 介護予防短期入所利用状況テーブル */
    @Autowired
    ComTlcSvPlanShortSelectMapper comTlcSvPlanShortSelectMapper;

    /** サービス利用票：算定確認（２０１１０８２８） */
    @Autowired
    CmnTucPlanRiyouKakuninSelectMapper cmnTucPlanRiyouKakuninSelectMapper;

    /** 入力引数に月の最終日を返す */
    @Autowired
    Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** サービス利用票.別表ヘッダ.利用票別表 保険外負担.利用票別表 公費負担.利用票別表 社福軽減 */
    @Autowired
    RiyouSumSelectMapper riyouSumSelectMapper;

    /** ケアマネ：利用票：シミュレーション処理 別表 公費集計欄(非表示) */
    @Autowired
    CmnTucPlanKouhiFutanSelectMapper cmnTucPlanKouhiFutanSelectMapper;

    /** 利用票別表 社福軽減 */
    @Autowired
    CmnTucPlanSyafukuKeigenSelectMapper cmnTucPlanSyafukuKeigenSelectMapper;

    /** 利用票別表 種類別限度管理 */
    @Autowired
    CmnTucPlanBetu2SelectMapper cmnTucPlanBetu2SelectMapper;

    /** 有効期間IDより、有効期間の開始日と有効期間の終了日を取得する。 */
    @Autowired
    KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;

    /** API定義書_APINo(701)_利用票画面初期情報取得 */
    @Autowired
    UseSlipInitInfoSelectServiceImpl useSlipInitInfoSelectServiceImpl;

    /** 利用者情報を取得する。 */
    @Autowired
    ComTucUserSelectMapper comTucUserSelectMapper;

    /** 介護保険 負担割合証.給付制限 給付率取得処理 */
    @Autowired
    KghShrKsgFunc02Logic kghShrKsgFunc02Logic;

    /** 要介護度比較関数 */
    @Autowired
    KghCmpF01Logic kghCmpF01Logic;

    /** サービス種類コードを返す */
    @Autowired
    KghCmnF01Logic kghCmnF01Logic;

    /** サービス利用票.別表明細行を取得 */
    @Autowired
    CmnTucPlanBetu1SelectMapper cmnTucPlanBetu1SelectMapper;

    /** 事業所情報「サービス事業者マスタ（１５－５）」を検索する */
    @Autowired
    ServiceJigyosyoInfoSelectMapper serviceJigyosyoInfoSelectMapper;

    /** 取込処理 */
    @Autowired
    WeekPlanSettingsSelectServiceImpl weekPlanSettingsSelectServiceImpl;

    /** 年月から termid を取得する（簡易版） */
    @Autowired
    KghCmnWeek1Logic kghCmnWeek1Logic;

    /** API定義書_APINo(1148)_請求エンジン関連計算処理 */
    @Autowired
    ComDemandEngineConnectionCalculationProcSelectServiceImpl comDemandEngineConnectionCalculationProcSelectServiceImpl;

    /**
     * 概算金額情報取得
     * 
     * @param inDto 概算金額情報取得の入力Dto
     * @return outDto 概算金額情報取得の出力Dto
     * @throws Exception Exception
     */
    @Override
    protected WeekPlanEstimateDisplaySelectServiceOutDto mainProcess(WeekPlanEstimateDisplaySelectServiceInDto inDto)
            throws Exception {
        LOG.info(Constants.START);
        // 概算金額情報取得の出力Dto
        WeekPlanEstimateDisplaySelectServiceOutDto outDto = new WeekPlanEstimateDisplaySelectServiceOutDto();

        // 2. 初期設定
        // 2.1. 変数値を設定する
        // 変数.支援ID=リクエストパラメータ.支援事業所ID
        String svId = inDto.getSvJigyoId();

        // 2.2. 共通関数を利用し、週間計画の取込用の構造体.事業所ＩＤより、変数.外部を取得する
        kghCmnF01Logic.getCmnIsChGaibuSv(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));

        // 2.3. SQLを発行し、週間リストを取得する。
        KghCmnKyuWeekAllGetSvsUnion2ByCriteriaInEntity kghCmnKyuWeekAllGetSvsUnion2ByCriteriaInEntity = new KghCmnKyuWeekAllGetSvsUnion2ByCriteriaInEntity();
        // 利用者ID
        kghCmnKyuWeekAllGetSvsUnion2ByCriteriaInEntity.setUId(CommonConstants.DEFAULT_USER_ID);
        // 事業者ID
        kghCmnKyuWeekAllGetSvsUnion2ByCriteriaInEntity.setShienId(CommonConstants.DEFAULT_SV_JIGYO_ID);
        // 当該年月
        kghCmnKyuWeekAllGetSvsUnion2ByCriteriaInEntity.setYymmYm(CommonConstants.EMPTY_STRING);
        // 変数.週間リスト = 上記取得結果
        List<KghCmnKyuWeekAllGetSvsUnion2OutEntity> weekList = KghCmnKyuWeekAllGetSvsUnion2SelectMapper
                .findKghCmnKyuWeekAllGetSvsUnion2ByCriteria(kghCmnKyuWeekAllGetSvsUnion2ByCriteriaInEntity);

        // 2.4. SQLを発行し、加算リストを取得する
        KghCmnPlnGetCpnWeekPlansKasanByCriteriaInEntity kghCmnPlnGetCpnWeekPlansKasanByCriteriaInEntity = new KghCmnPlnGetCpnWeekPlansKasanByCriteriaInEntity();
        // 週間計画ID
        kghCmnPlnGetCpnWeekPlansKasanByCriteriaInEntity.setKs51Id(CommonConstants.SHUUKAN_KEIKAKU_ID_0);
        // 詳細データID
        kghCmnPlnGetCpnWeekPlansKasanByCriteriaInEntity.setKs52Id(CommonConstants.SHOUSAI_DATA_ID_0);
        // 変数.加算リスト = 上記取得結果
        List<KghCmnPlnGetCpnWeekPlansKasanOutEntity> kasanList = cpnTucCks55SelectMapper
                .findKghCmnPlnGetCpnWeekPlansKasanByCriteria(kghCmnPlnGetCpnWeekPlansKasanByCriteriaInEntity);

        // 2.5. SQLを発行し、隔週リストを取得する
        KghCmnWeekGetKakushuuByCriteriaInEntity kghCmnWeekGetKakushuuByCriteriaInEntity = new KghCmnWeekGetKakushuuByCriteriaInEntity();
        // 週間計画ID
        kghCmnWeekGetKakushuuByCriteriaInEntity.setAlWeek1(CommonConstants.SHUUKAN_KEIKAKU_ID_0);
        // 詳細データID
        kghCmnWeekGetKakushuuByCriteriaInEntity.setAlWeek2(CommonConstants.SHOUSAI_DATA_ID_0);
        // 変数.隔週リスト = 上記取得結果
        List<KghCmnWeekGetKakushuuOutEntity> kakushuuList = cpnTucCks58SelectMapper
                .findKghCmnWeekGetKakushuuByCriteria(kghCmnWeekGetKakushuuByCriteriaInEntity);

        // 2.6. 貰った週間計画を、概算できるように置換する
        // 変数.履歴件数(28-25 週間計画表（ヘッダ）の件数)を取得する
        wf_00_convert_weekplan(inDto.getShoriYymm(), weekList, kasanList, kakushuuList, inDto.getSiyouFlg(),
                inDto.getDwWeeks(), inDto.getDwRiyou(), inDto.getDwKakusyuu());

        // 3. 初期処理
        // 3.1. 変数の値を設定する
        // 変数.有効期間ID= リクエストパラメータ.有効期間ID
        String termId = inDto.getTermid();

        // 3.5. 変数.サービス費リスト(ids_kaigo) 初期化
        List<KghSelSvitemCalc1PonNextOutEntity> idsKaigoList = new ArrayList<KghSelSvitemCalc1PonNextOutEntity>();

        // 3.6. 変数.サービス費リストバッファ（1事業所分）(ids_kaigo_buf) 初期化
        List<KghSelSvitemCalc1PonNextOutEntity> idsKaigoBufList = new ArrayList<KghSelSvitemCalc1PonNextOutEntity>();

        // 3.7. 変数.サービス費リストの対象サービス明細(ids_kaigo_target)
        List<Gui01152UserInfo> idsKaigoTargerList = new ArrayList<Gui01152UserInfo>();

        // 3.9. 変数.総合事業サービス費リストバッファ（1事業所分）(ids_kaigo_buf_sougou) 初期化
        List<KghSelSvitemCalc1PonNextSougouOutEntity> idsKaigoBufSougouList = new ArrayList<KghSelSvitemCalc1PonNextSougouOutEntity>();

        // 4. 検索
        Gui01042SerachResult searchResult = ue_retrieve(inDto, svId, termId, weekList, kasanList, kakushuuList,
                idsKaigoList, idsKaigoTargerList, idsKaigoBufList, idsKaigoBufSougouList);

        // 上記取得結果.概算可能フラグ == False の場合、レスポンスパラメータ.概算可能フラグ＝false、処理を終了する。
        if (!searchResult.isGaisanFlg()) {
            outDto.setClacFlg(CommonConstants.STR_FALSE);
            return outDto;
        }

        // 5. 結果を設定する
        // 概算可能フラグ
        outDto.setClacFlg(CommonConstants.STR_TRUE);
        // レスポンスパラメータ.提供年月 ＝ リクエストパラメータ.処理年月
        outDto.setYyyymm(inDto.getShoriYymm());
        // レスポンスパラメータ.区分限度超過 ＝ 変数.利用票：日数.単位数.金額の計算結果欄(dw_gendo).超過_区分支給限度
        outDto.setHKbnOver(searchResult.getSumData().getKbnOverSum());
        // レスポンスパラメータ.区分限度基準内 ＝ 変数.利用票：日数.単位数.金額の計算結果欄(dw_gendo).基準内_区分支給限度
        outDto.setHKbnKijun(searchResult.getSumData().getKbnKijunSum());
        // レスポンスパラメータ.負担保険分 ＝ 変数.利用票：日数.単位数.金額の計算結果欄(dw_gendo).保険分_利用者負担額
        outDto.setHHknHutan(searchResult.getSumData().getHknHutanSum());
        // レスポンスパラメータ.負担全額分 ＝ 変数.利用票：日数.単位数.金額の計算結果欄(dw_gendo).全額分_利用者負担額
        outDto.setHJihHutan(searchResult.getSumData().getJihHutanSum());
        // レスポンスパラメータ.適用公費公費額 ＝ 変数.利用票：日数.単位数.金額の計算結果欄(dw_gendo).公費負担額合計
        outDto.setCmnTucPlanKouhiFutanFutanGaku(searchResult.getSumData().getFutanGakuSum());
        // レスポンスパラメータ.適用公費自負担 ＝ 変数.利用票：日数.単位数.金額の計算結果欄(dw_gendo).本人負担額合計
        outDto.setCmnTucPlanKouhiFutanHonninFutanGaku(searchResult.getSumData().getHonninFutanGakuSum());

        LOG.info(Constants.END);
        return outDto;
    }

    /**
     * 履歴件数(28-25 週間計画表（ヘッダ）の件数)取得
     * 
     * @param shoriYymm    処理年月
     * @param weekList     週間リスト
     * @param KasanList    加算リスト
     * @param kakushuuList 隔週リスト
     * @param siyouFlg     使用フラグ
     * @param dwWeeks      週間計画ｄｗ
     * @param dwRiyou      週間計画加算ｄｗ
     * @param dwKakusyuu   週間計画-隔週情報ｄｗ
     * 
     * @return 履歴件数
     */
    public Integer wf_00_convert_weekplan(String shoriYymm, List<KghCmnKyuWeekAllGetSvsUnion2OutEntity> weekList,
            List<KghCmnPlnGetCpnWeekPlansKasanOutEntity> KasanList, List<KghCmnWeekGetKakushuuOutEntity> kakushuuList,
            String siyouFlg, List<Gui01042DwWeeks> dwWeeks, List<Gui01042DwRiyou> dwRiyou,
            List<Gui01042DwKakusyuu> dwKakusyuu) {

        // 履歴件数
        Integer kensu = 0;
        // 1. 共通関数を利用し、変数.有効期間IDを取得する
        // 変数.有効期間ID = 上記のOUTPUT情報.有効期間ID
        Integer kikanId = kghCmnWeek1Logic.getTermid(shoriYymm, CommonConstants.SV_KIND_CD_11);

        // 2. 週間計画の取込用の構造体.週間計画リストの件数が0の場合、0を戻り値.履歴件数に設定し、呼出し元に返す
        if (CollectionUtils.isEmpty(weekList)) {
            return kensu;
        }

        List<String> svShuruiCdvalues = Arrays.asList(CommonConstants.SHURUI_A1_STRING,
                CommonConstants.SHURUI_A5_STRING, CommonConstants.SHURUI_A9_STRING);
        // 4. 引数.週間計画ｄｗより繰り返す
        for (Gui01042DwWeeks dwWeek : dwWeeks) {
            // 変数.週間計画ID = 引数.週間計画ｄｗ.週間計画ID
            String ks51Id = dwWeek.getKs51Id();
            // 変数.詳細データID = 引数.週間計画ｄｗ.詳細データID
            String ks52Id = dwWeek.getKs52Id();
            // 変数.サービス種類 = 引数.週間計画ｄｗ.サービス種類
            String svShuruiCd = dwWeek.getSvShuruiCd();
            // 変数.事業者ID = 引数.週間計画ｄｗ.事業者ID
            Integer svjigyoId = CommonDtoUtil.strValToInt(dwWeek.getSvjigyoId());
            // 変数.サービス項目ID = 引数.週間計画ｄｗ.サービス項目ID
            Integer svitemCd = CommonDtoUtil.strValToInt(dwWeek.getSvitemCd());

            // 4.1. (変数.サービス種類が「A1」、或いは「A5」、或いは「A9」)、且つ、変数.サービス項目IDがゼロより大きい場合
            if (svShuruiCdvalues.contains(svShuruiCd) && svitemCd.compareTo(CommonConstants.NUMBER_0) > 0) {
                // 共通関数を利用し、変数.サービス種類をやり直す(戻り値の先頭から２桁で)
                svitemCd = CommonDtoUtil.strValToInt(
                        kghCmn03gFunc01Logic.getScodeFromItemuse2(svjigyoId, svitemCd, kikanId).substring(0, 2));
            }

            // 4.2. 変数.週間計画IDがNULL、或いは0の場合、-1でやり直す
            if (ks51Id == null || CommonDtoUtil.objValToString(CommonConstants.SHUUKAN_KEIKAKU_ID_0).equals(ks51Id)) {
                ks51Id = CommonConstants.STRING_NEGATIVE_ONE;
            }
            // 変数.詳細データIDがNULL、或いは0の場合、-1でやり直す
            if (ks52Id == null || CommonDtoUtil.objValToString(CommonConstants.SHOUSAI_DATA_ID_0).equals(ks52Id)) {
                ks52Id = CommonConstants.STRING_NEGATIVE_ONE;
            }

            // 4.3. 下記テーブルより、引数.週間リストに新しい行を追加する
            KghCmnKyuWeekAllGetSvsUnion2OutEntity rowData = new KghCmnKyuWeekAllGetSvsUnion2OutEntity();
            // 週間計画ID
            rowData.setKs51Id(CommonDtoUtil.strValToInt(ks51Id));
            // 詳細データID
            rowData.setKs52Id(CommonDtoUtil.strValToInt(ks52Id));
            // dmy_sel
            rowData.setDmySel(1);
            // パッケージ
            rowData.setIsPackage(CommonDtoUtil.strValToInt(siyouFlg));
            // サービス種類
            rowData.setSvShuruiCd(svShuruiCd);
            // 事業者ID
            rowData.setSvJigyoId(svjigyoId);
            // サービス項目ID
            rowData.setSvItemCd(svitemCd);
            // 開始時間
            rowData.setStartYmd(dwWeek.getKaishiJikan());
            // 終了時間
            rowData.setEndYmd(dwWeek.getShuuryouJikan());
            // 曜日
            rowData.setYoubi(dwWeek.getYoubi());
            // 福祉用具貸与マスタID
            rowData.setFygId(CommonDtoUtil.strValToInt(dwWeek.getFygId()));
            // サービス単位数
            rowData.setSvTani(CommonDtoUtil.strValToDouble(dwWeek.getSvTani()));
            // 週単位以外のｻｰﾋﾞｽ区分
            rowData.setIgaiKbn(CommonDtoUtil.strValToInt(dwWeek.getIgaiKbn()));
            // 週単位以外のｻｰﾋﾞｽ（日付指定）
            rowData.setIgaiDate(dwWeek.getIgaiDate());
            // 週単位以外のｻｰﾋﾞｽ（曜日指定）
            rowData.setIgaiWeek(dwWeek.getIgaiWeek());

            weekList.add(rowData);
        }

        // 4.4. 戻り値.履歴件数 = 週間リストの件数
        kensu = weekList.size();

        // 5.引数 .週間計画加算ｄｗより繰り返す
        for (Gui01042DwRiyou riyou : dwRiyou) {
            // 5.1. 下記テーブルより、引数.加算リストに新しい行を追加する
            KghCmnPlnGetCpnWeekPlansKasanOutEntity kasan = new KghCmnPlnGetCpnWeekPlansKasanOutEntity();
            // 加算データID
            kasan.setKs55Id(CommonDtoUtil.strValToInt(riyou.getKs55Id()));
            // 週間計画ID
            kasan.setKs51Id(CommonDtoUtil.strValToInt(riyou.getKs51Id()));
            // 詳細データID
            kasan.setKs52Id(CommonDtoUtil.strValToInt(riyou.getKs52Id()));
            // サービス項目ID
            kasan.setSvItemCd(CommonDtoUtil.strValToInt(riyou.getSvItemCd()));
            // 回数
            kasan.setKaisuu(CommonDtoUtil.strValToInt(riyou.getKaisuu()));
            // 福祉用具貸与マスタID
            kasan.setFygId(CommonDtoUtil.strValToInt(riyou.getFygId()));
            // サービス単位数
            kasan.setSvTani(CommonDtoUtil.strValToDouble(riyou.getSvTani()));

            KasanList.add(kasan);
        }

        // 6. 引数.使用フラグ＝０の場合
        if (CommonConstants.USE_FLG_NOT_USE.equals(siyouFlg)) {
            // 引数.週間計画-隔週情報ｄｗより繰り返す
            for (Gui01042DwKakusyuu dwKaku : dwKakusyuu) {
                // 6.1. 下記テーブルより、引数.隔週リストに新しい行を追加する
                KghCmnWeekGetKakushuuOutEntity kakushuu = new KghCmnWeekGetKakushuuOutEntity();
                // 隔週データのプライマリID
                kakushuu.setKs58Id(CommonDtoUtil.strValToInt(dwKaku.getKs58Id()));
                // 週間計画ID
                kakushuu.setKs51Id(CommonDtoUtil.strValToInt(dwKaku.getKs51Id()));
                // 詳細データID
                kakushuu.setKs52Id(CommonDtoUtil.strValToInt(dwKaku.getKs52Id()));
                // 隔週基準年月日
                kakushuu.setKakusyuuYmd(dwKaku.getKakusyuuYmd());
                // 隔週週間隔
                kakushuu.setKakusyuuKankaku(CommonDtoUtil.strValToInt(dwKaku.getKakusyuuKankaku()));
                // 曜日
                kakushuu.setYoubi(CommonDtoUtil.strValToInt(dwKaku.getYoubi()));

                kakushuuList.add(kakushuu);
            }
        }

        return kensu;
    }

    /**
     * 検索
     * 
     * @param inDto                 概算金額情報取得 入力Dto
     * @param jigyoId               事業所ＩＤ
     * @param kikanId               有効期間ID
     * @param weekList              週間リスト
     * @param KasanList             加算リスト
     * @param kakushuuList          隔週リスト
     * @param idsKaigoList          サービス費リスト(ids_kaigo)
     * @param idsKaigoTargerList    サービス費リストの対象サービス明細(ids_kaigo_target)
     * @param idsKaigoBufList       サービス費リストバッファ（1事業所分）(ids_kaigo_buf)
     * @param idsKaigoBufSougouList 総合事業サービス費リストバッファ（1事業所分）(ids_kaigo_buf_sougou)
     * @return
     */
    public Gui01042SerachResult ue_retrieve(WeekPlanEstimateDisplaySelectServiceInDto inDto, String jigyoId,
            String kikanId, List<KghCmnKyuWeekAllGetSvsUnion2OutEntity> weekList,
            List<KghCmnPlnGetCpnWeekPlansKasanOutEntity> KasanList, List<KghCmnWeekGetKakushuuOutEntity> kakushuuList,
            List<KghSelSvitemCalc1PonNextOutEntity> idsKaigoList, List<Gui01152UserInfo> idsKaigoTargetList,
            List<KghSelSvitemCalc1PonNextOutEntity> idsKaigoBufList,
            List<KghSelSvitemCalc1PonNextSougouOutEntity> idsKaigoBufSougouList) throws Exception {

        Gui01042SerachResult result = new Gui01042SerachResult();

        // 1. 変数.変更年月日 = 引数.処理年月 + "/" + "01"
        String henkoYmd = inDto.getShoriYymm() + CommonConstants.STRING_FIRST_DAY;
        // 2. 変数.提供年月の月初 = 引数.処理年月 + "/" + "01"
        String teikyoYmdFirstDay = inDto.getShoriYymm() + CommonConstants.STRING_FIRST_DAY;

        // 3. 当月情報をセットする
        // 3.1. 共通関数を利用し、入力引数に月の最終日を返す
        // 最終日
        String lastDay = nds3GkFunc01Logic.getTukimatu(henkoYmd);
        // 3.2. 共通関数を利用し、日付の書式変換
        // 処理年月の最終日
        String teikyoYmdLastDay = nds3GkFunc01Logic.cnvYmd(lastDay);
        // 3.3. 引数.週間リストの件数＝0の場合、戻り値.概算可能フラグ＝false、処理を終了する。
        if (CollectionUtils.isEmpty(weekList)) {
            result.setGaisanFlg(false);
            return result;
        }

        // 3.4. 上記以外の場合、引数.週間リストすべての当該年月 = 引数.処理年月
        weekList.forEach(item -> item.setTougaiYm(inDto.getShoriYymm()));

        // 4. ダミーで検索
        Gui01042SerachResult searchResult = wf_00_datawindow_init();

        // 5. 共通関数を利用し、効期間の開始日と有効期間の終了日を取得する。
        TermId2DateLogicOutDto termId2DateLogicOutDto = kghKrkZCpnFuncLogic.getTermid2date(
                CommonDtoUtil.strValToInt(kikanId), CommonConstants.EMPTY_STRING, CommonConstants.EMPTY_STRING);
        // 変数.変更年月日 ＜ OUTPUT情報.開始日 または 変数.変更年月日 ＞ OUTPUT情報.終了日 の場合、
        if (henkoYmd.compareTo(termId2DateLogicOutDto.getSYmd()) < 0
                || henkoYmd.compareTo(termId2DateLogicOutDto.getEYmd()) > 0) {
            // 戻り値.概算可能フラグ＝false、処理を終了する。
            result.setGaisanFlg(false);
            return result;
        }

        // 6. 利用票の処理を呼出しで。
        // 6.1. 初期情報セット
        // API定義書_APINo(701)_利用票画面初期情報取得.xlsxの「関数_基本情報のセット」シート
        // 変数.利用票画面処理用構造体 = 上記取得結果.利用票画面処理用構造体
        Gui01149RiyouProcess riyouProcess = useSlipInitInfoSelectServiceImpl.setRiyouProcessBaseInfo(inDto.getUserId(),
                inDto.getShoriYymm() + CommonConstants.STRING_FIRST_DAY);

        // 6.2. 台帳の保険情報を取得する
        getCmnUsrInfo3gOutDto(inDto, riyouProcess);

        // 7. 変数.期間配列[31]＝{ 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
        // 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0 }
        // 28から 引数.処理年月の最終日まで、変数.期間配列[該当日]＝1
        List<Integer> kikanList = new ArrayList<Integer>();
        Integer lastDayInt = CommonDtoUtil.strValToInt(lastDay);
        for (int i = 0; i < CommonConstants.INT_31; i++) {
            if (i < lastDayInt) {
                kikanList.add(1);
            } else {
                kikanList.add(0);
            }
        }

        // 8. 概算のための userinfo 部分を作成する
        wf_11_make_userinfo(teikyoYmdFirstDay, teikyoYmdLastDay, riyouProcess, searchResult.getDwUsrinfList(),
                searchResult.getDwGendoList(), inDto);

        // 9. 週間計画を展開し、算定する
        // 変数.要介護度の（指定月の）配列 = 上記結果.要介護度の（指定月の）配列
        YokaiDaysOutDto yokaiDaysOutDto = kghCmn03gFunc01Logic
                .getYokaiDays(CommonDtoUtil.strValToInt(inDto.getUserId()), inDto.getShoriYymm());

        // 10. 取込処理
        // 「API定義書_APINo(1235)_週間計画取込処理.xlsx」の「関数_取込処理」
        Gui01152WeekPlanData gui01152WeekPlanData = new Gui01152WeekPlanData();
        // 呼び出し元フラグ
        gui01152WeekPlanData.setIiCaller(inDto.getCaller());
        // 取り込みの区分
        gui01152WeekPlanData.setIiTorikomiKbn(inDto.getTorikomiKbn());
        // 支援事業所ID
        gui01152WeekPlanData.setIlShienId(inDto.getSvJigyoId());
        // 利用者ID
        gui01152WeekPlanData.setIlUId(inDto.getUserId());
        // 処理年月
        gui01152WeekPlanData.setIsShoriYm(inDto.getShoriYymm());
        // 処理日
        gui01152WeekPlanData.setIsShoriD(CommonConstants.MONTHSTART);
        // 短期入所サービスの複写
        gui01152WeekPlanData.setIiCopyTnki(inDto.getCopyTnki());
        // 福祉用具単位数の設定
        gui01152WeekPlanData.setIiFygTani(inDto.getFygTani());
        // 処理年月を指定する
        if (yokaiDaysOutDto != null && CollectionUtils.isNotEmpty(yokaiDaysOutDto.getYokaiDaysList())) {
            gui01152WeekPlanData
                    .setIiSitei(CommonDtoUtil.objValToString(yokaiDaysOutDto.getYokaiDaysList().getFirst()));
        }
        // 週単位以外.年月日指定（履歴）ｄｓ
        gui01152WeekPlanData.setDsTsukihi(inDto.getDsTsukihi());

        WeekPlanSettingsSelectServiceInDto weekPlanSettingsSelectServiceInDto = new WeekPlanSettingsSelectServiceInDto();
        // 取り込みの区分
        weekPlanSettingsSelectServiceInDto.setITorikomiKbn(inDto.getTorikomiKbn());

        String torikomiResult = weekPlanSettingsSelectServiceImpl.acquProc(gui01152WeekPlanData,
                weekPlanSettingsSelectServiceInDto);
        // 変数.利用票明細情報 = 上記結果.利用票情報リスト
        List<Gui01152UserInfo> riyouInfoList = gui01152WeekPlanData.getRiyouhyojyouhoulist3();

        Gui01042Sum sumData = new Gui01042Sum();
        // 11. 上記のOUTPUT情報の値 = 1 の場合
        if (CommonConstants.NUM_STR_1.equals(torikomiResult)) {

            // 11.1. 変数.サービス事業者ID配列 = 変数.利用票明細情報からサービス事業者ID配列を取得して、配列の末端を0に追加する
            List<Integer> svJigyoIdList = new ArrayList<Integer>();
            svJigyoIdList.add(0);

            // 11.2. 変数.事業所リスト = SQLを発行し、事業所リストを取得する。
            SelSvjigyoCalc1PonByCriteriaInEntity selSvjigyoCalc1PonByCriteriaInEntity = new SelSvjigyoCalc1PonByCriteriaInEntity();
            // サービス事業者ID
            selSvjigyoCalc1PonByCriteriaInEntity.setAiJcdList(svJigyoIdList);
            List<SelSvjigyoCalc1PonOutEntity> jigyoshoList = comMscSvjigyoSelectMapper
                    .findSelSvjigyoCalc1PonByCriteria(selSvjigyoCalc1PonByCriteriaInEntity);

            // 11.3. 介護マスタを取得する関数を呼び出す
            uf_get_kaigo_master(henkoYmd, riyouInfoList, idsKaigoList, idsKaigoTargetList, idsKaigoBufList,
                    idsKaigoBufSougouList);

            // 11.4. 請求エンジン関連を呼出しで。
            // API定義書_APINo(1148)_請求エンジン関連計算処理
            ComDemandEngineConnectionCalculationProcSelectServiceInDto inDto1148 = new ComDemandEngineConnectionCalculationProcSelectServiceInDto();
            // 呼出元区分
            inDto1148.setCallerType(CommonConstants.STR_2);
            // 提供年月
            inDto1148.setProvidedYm(inDto.getShoriYymm());

            if (CollectionUtils.isNotEmpty(searchResult.getDwUsrinfList())) {
                // 利用者基本情報 関数_wf_00_datawindow_init」の処理「2」のアウトプット
                List<Gui01149UserInfo> userInfoList = new ArrayList<Gui01149UserInfo>();
                for (KghCmnRiyou2HeaderAcsOutEntity usr : searchResult.getDwUsrinfList()) {
                    Gui01149UserInfo user = new Gui01149UserInfo();
                    // 支援事業者ID
                    user.setCmnTucPlanShienId(CommonDtoUtil.objValToString(usr.getShienId()));
                    // 利用者ID
                    user.setCmnTucPlanUserid(CommonDtoUtil.objValToString(usr.getUserid()));
                    // サービス提供年月
                    user.setCmnTucPlanYymmYm(usr.getYymmYm());
                    // サービス提供年月（変更日）
                    user.setCmnTucPlanYymmD(usr.getYymmD());
                    // 保険者番号1
                    user.setCmnTucPlanKHokenCd(CommonDtoUtil.objValToString(usr.getKHokenCd()));
                    // 被保険者番号1
                    user.setCmnTucPlanHHokenNo(usr.getHHokenNo());
                    // 作成日
                    user.setCmnTucPlanCreateYmd(usr.getCreateYmd());
                    // サービス単位数（予定）
                    user.setCmnTucPlanSvTensuY(CommonDtoUtil.objValToString(usr.getSvTensuY()));
                    // サービス単位数（実績）
                    user.setCmnTucPlanSvTensuJ(CommonDtoUtil.objValToString(usr.getSvTensuJ()));
                    // 短期入所 前月までの利用日数
                    user.setCmnTucPlanTZengetu(CommonConstants.STR_0);
                    // 短期入所 当月の計画利用日数
                    user.setCmnTucPlanTTougetu(CommonConstants.STR_0);
                    // 短期入所 累積利用日数
                    user.setCmnTucPlanTRuiseki(CommonConstants.STR_0);
                    // 前月の３０日超の連続日数
                    user.setCmnTucPlanOv30Zengetu(CommonConstants.STR_0);
                    // 予定済みフラグ
                    user.setCmnTucPlanYoteiZumiFlg(CommonConstants.STR_0);
                    // 実績済みフラグ
                    user.setCmnTucPlanJissekiZumiFlg(CommonConstants.STR_0);
                    // 変更前要介護度
                    user.setDmyKaigodo(CommonDtoUtil.objValToString(usr.getDmyKaigodo()));
                    // 変更後要介護度
                    user.setDmyHenKaigodo(CommonDtoUtil.objValToString(usr.getDmyHenKaigodo()));
                    // 変更後日付
                    user.setDmyHenDate(usr.getDmyHenDate());
                    // 担当ケアマネID1
                    user.setDmyTanto(usr.getDmyTanto());
                    // 支給額
                    user.setDmyShikyuGaku(CommonDtoUtil.objValToString(usr.getDmyShikyuGaku()));
                    // 限度額適用期間開始日
                    user.setDmyKikanFr(usr.getDmyKikanFr());
                    // 限度額適用期間終了日
                    user.setDmyKikanTo(usr.getDmyKikanTo());
                    // 保険者番号2
                    user.setDmyKHokenNo(usr.getDmyKHokenNo());
                    // 保険者名
                    user.setDmyKHokenKnj(usr.getDmyKHokenKnj());
                    // 被保険者番号2
                    user.setDmyHHokenNo(usr.getDmyHHokenNo());
                    // ユーザー名称
                    user.setDmyUserName(usr.getDmyUserName());
                    // 生年月日
                    user.setDmyBirthdayYmd(usr.getDmyBirthdayYmd());
                    // 性別
                    user.setDmySex(CommonDtoUtil.objValToString(usr.getDmySex()));
                    // 連番
                    user.setDmySeqNo(CommonDtoUtil.objValToString(usr.getDmySeqNo()));
                    // 枝番
                    user.setDmyEdaNo(CommonDtoUtil.objValToString(usr.getDmyEdaNo()));
                    // 給付率1
                    user.setDmyFutanRate(CommonDtoUtil.objValToString(usr.getDmyFutanRate()));

                    userInfoList.add(user);
                }

                inDto1148.setUserInfo(userInfoList);
            }

            if (CollectionUtils.isNotEmpty(jigyoshoList)) {
                // 事業所情報 「関数_ue_retrieve」の処理「11.2」のアウトプット
                List<Gui01149JigyoInfo> jigyoInfoList = new ArrayList<Gui01149JigyoInfo>();
                for (SelSvjigyoCalc1PonOutEntity jigyo : jigyoshoList) {
                    Gui01149JigyoInfo jigyoInfo = new Gui01149JigyoInfo();
                    // counter
                    jigyoInfo.setComMscSvjigyoCounter(CommonDtoUtil.objValToString(jigyo.getCounter()));
                    // 法人ID
                    jigyoInfo.setComMscSvjigyoHoujinId(CommonDtoUtil.objValToString(jigyo.getHoujinId()));
                    // 施設ID
                    jigyoInfo.setComMscSvjigyoShisetuId(CommonDtoUtil.objValToString(jigyo.getShisetuId()));
                    // サービス事業者ID
                    jigyoInfo.setComMscSvjigyoSvJigyoId(CommonDtoUtil.objValToString(jigyo.getSvJigyoId()));
                    // サービス事業者コード
                    jigyoInfo.setComMscSvjigyoSvJigyoCd(jigyo.getSvJigyoCd());
                    // 事業所番号
                    jigyoInfo.setComMscSvjigyoJigyoNumber(jigyo.getJigyoNumber());
                    // 事業名
                    jigyoInfo.setComMscSvjigyoJigyoKnj(jigyo.getJigyoKnj());
                    // 代表者名
                    jigyoInfo.setComMscSvjigyoDaihyoKnj(jigyo.getDaihyoKnj());
                    // 郵便番号
                    jigyoInfo.setComMscSvjigyoZip(jigyo.getZip());
                    // 県コード
                    jigyoInfo.setComMscSvjigyoKencode(CommonDtoUtil.objValToString(jigyo.getKencode()));
                    // 市町村コード
                    jigyoInfo.setComMscSvjigyoCitycode(CommonDtoUtil.objValToString(jigyo.getCitycode()));
                    // 地区コード
                    jigyoInfo.setComMscSvjigyoAreacode(CommonDtoUtil.objValToString(jigyo.getAreacode()));
                    // 住所
                    jigyoInfo.setComMscSvjigyoAddressKnj(jigyo.getAddressKnj());
                    // 電話番号
                    jigyoInfo.setComMscSvjigyoTel(jigyo.getTel());
                    // ＦＡＸ
                    jigyoInfo.setComMscSvjigyoFax(jigyo.getFax());
                    // 会計コード
                    jigyoInfo.setComMscSvjigyoKaikeiId(CommonDtoUtil.objValToString(jigyo.getKaikeiId()));
                    // 職員コード
                    jigyoInfo.setComMscSvjigyoShokuId(CommonDtoUtil.objValToString(jigyo.getShokuId()));
                    // 事業名（略称）
                    jigyoInfo.setComMscSvjigyoJigyoRyakuKnj(jigyo.getJigyoRyakuKnj());
                    // サービスコード
                    jigyoInfo.setComMscSvjigyoSvCd(jigyo.getSvCd());
                    // ケアプラン方式
                    jigyoInfo.setComMscSvjigyoCpnType(CommonDtoUtil.objValToString(jigyo.getCpnType()));
                    // サービス開始時間
                    jigyoInfo.setComMscSvjigyoStartTime(jigyo.getStartTime());
                    // サービス終了時間
                    jigyoInfo.setComMscSvjigyoEndTime(jigyo.getEndTime());
                    // 通所定員
                    jigyoInfo.setComMscSvjigyoTuushoMax(CommonDtoUtil.objValToString(jigyo.getTuushoMax()));
                    // 事業者区分
                    jigyoInfo.setComMscSvjigyoSvJigyoKbn(CommonDtoUtil.objValToString(jigyo.getSvJigyoKbn()));
                    // 単位数割引率（％）
                    jigyoInfo.setComMscSvjigyoTaniwaribiki(CommonDtoUtil.objValToString(jigyo.getTaniwaribiki()));
                    // 生活保護法指定有無
                    jigyoInfo.setComMscSvjigyoSeihoYn(CommonDtoUtil.objValToString(jigyo.getSeihoYn()));
                    // 予備１
                    jigyoInfo.setComMscSvjigyoReserve1(CommonDtoUtil.objValToString(jigyo.getReserve1()));
                    // 予備２
                    jigyoInfo.setComMscSvjigyoReserve2(CommonDtoUtil.objValToString(jigyo.getReserve2()));
                    // 予備３
                    jigyoInfo.setComMscSvjigyoReserve3(CommonDtoUtil.objValToString(jigyo.getReserve3()));
                    // 予備４
                    jigyoInfo.setComMscSvjigyoReserve4(CommonDtoUtil.objValToString(jigyo.getReserve4()));
                    // 予備５
                    jigyoInfo.setComMscSvjigyoReserve5(CommonDtoUtil.objValToString(jigyo.getReserve5()));
                    // 給食対象事業区分
                    jigyoInfo.setComMscSvjigyoEiyKbn(CommonDtoUtil.objValToString(jigyo.getEiyKbn()));

                    jigyoInfoList.add(jigyoInfo);
                }

                inDto1148.setJigyoInfo(jigyoInfoList);
            }

            if (CollectionUtils.isNotEmpty(idsKaigoList)) {
                // サービス費マスタ情報 「関数_uf_get_kaigo_master」の処理「3.3.1」のアウトプット
                List<Gui01149KaigoInfo> kaigoInfoList = new ArrayList<Gui01149KaigoInfo>();
                for (KghSelSvitemCalc1PonNextOutEntity idskaigo : idsKaigoList) {
                    Gui01149KaigoInfo kaigo = new Gui01149KaigoInfo();

                    // サービス事業者ＩＤ
                    kaigo.setComMhcItemuseSvJigyoId(CommonDtoUtil.objValToString(idskaigo.getSvJigyoId()));
                    // 単価有効期間ＩＤ
                    kaigo.setComMhcItemuseTankaTermid(CommonDtoUtil.objValToString(idskaigo.getTankaTermid()));
                    // 有効期間ＩＤ
                    kaigo.setComMhcItemuseTermid(CommonDtoUtil.objValToString(idskaigo.getTermid()));
                    // 項目コード
                    kaigo.setComMhcItemuseItemcode(CommonDtoUtil.objValToString(idskaigo.getItemcode()));
                    // 開始日
                    kaigo.setComMhcItemuseStartdateYmd(idskaigo.getStartdateYmd());
                    // 終了日
                    kaigo.setComMhcItemuseEnddateYmd(idskaigo.getEnddateYmd());
                    // サービス種類コード
                    kaigo.setComMhcItemuseSvtype(idskaigo.getSvtype());
                    // サービス項目コード
                    kaigo.setComMhcItemuseSvcode(idskaigo.getSvcode());
                    // 項目識別コード
                    kaigo.setComMhcItemuseScode(idskaigo.getScode());
                    // 点金区分
                    kaigo.setComMhcItemuseTenkintype(CommonDtoUtil.objValToString(idskaigo.getTenkintype()));
                    // 点数／金額／加算率
                    kaigo.setComMhcItemuseTanka(CommonDtoUtil.objValToString(idskaigo.getTanka()));
                    // 標準負担割合
                    kaigo.setComMhcItemuseStdhutanwari(CommonDtoUtil.objValToString(idskaigo.getStdhutanwari()));
                    // サービス別負担割合
                    kaigo.setComMhcItemuseHutanwari(CommonDtoUtil.objValToString(idskaigo.getHutanwari()));
                    // １点あたりの単価
                    kaigo.setComMhcItemuseTentanka(CommonDtoUtil.objValToString(idskaigo.getTentanka()));
                    // 自己負担額１
                    kaigo.setComMhcItemuseHutangaku1(CommonDtoUtil.objValToString(idskaigo.getHutangaku1()));

                    kaigoInfoList.add(kaigo);
                }

                inDto1148.setKaigoInfo(kaigoInfoList);
            }

            if (CollectionUtils.isNotEmpty(idsKaigoTargetList)) {
                // 利用票情報 「API定義」の処理「3.7」のアウトプット
                List<Gui01149PlanInfo> planInfoList = new ArrayList<Gui01149PlanInfo>();
                for (Gui01152UserInfo kaigo : idsKaigoTargetList) {
                    Gui01149PlanInfo plan = new Gui01149PlanInfo();
                    // 支援事業者ID
                    plan.setCmnTucPlanRiyouShienId(kaigo.getShienId());
                    // 利用者ID
                    plan.setCmnTucPlanRiyouUserid(kaigo.getUserId());
                    // サービス提供年月
                    plan.setCmnTucPlanRiyouYymmYm(kaigo.getYymmYm());
                    // サービス提供年月（変更日）
                    plan.setCmnTucPlanRiyouYymmD(kaigo.getYymmD());
                    // サービス事業者ID
                    plan.setCmnTucPlanRiyouSvJigyoId(kaigo.getSvJigyoId());
                    // サービス項目ID
                    plan.setCmnTucPlanRiyouSvItemCd(kaigo.getSvItemCd());
                    // 枝番
                    plan.setCmnTucPlanRiyouEdaNo(kaigo.getEdaNo());
                    // 有効期間ID
                    plan.setCmnTucPlanRiyouTermid(kaigo.getTermid());
                    // サービス種別コード
                    plan.setCmnTucPlanRiyouSvtype(kaigo.getSvtype());
                    // サービス項目コード
                    plan.setCmnTucPlanRiyouSvcode(kaigo.getSvcode());
                    // サービスコード
                    plan.setCmnTucPlanRiyouScode(kaigo.getScode());
                    // サービス開始時間
                    plan.setCmnTucPlanRiyouSvStartTime(kaigo.getSvStartTime());
                    // サービス終了時間
                    plan.setCmnTucPlanRiyouSvEndTime(kaigo.getSvEndTime());
                    // サービス単位数
                    plan.setCmnTucPlanRiyouSvTani(kaigo.getSvTani());
                    // 予定回数01 ~ 予定回数31
                    plan.setCmnTucPlanRiyouYDay01(kaigo.getYDay01());
                    plan.setCmnTucPlanRiyouYDay02(kaigo.getYDay02());
                    plan.setCmnTucPlanRiyouYDay03(kaigo.getYDay03());
                    plan.setCmnTucPlanRiyouYDay04(kaigo.getYDay04());
                    plan.setCmnTucPlanRiyouYDay05(kaigo.getYDay05());
                    plan.setCmnTucPlanRiyouYDay06(kaigo.getYDay06());
                    plan.setCmnTucPlanRiyouYDay07(kaigo.getYDay07());
                    plan.setCmnTucPlanRiyouYDay08(kaigo.getYDay08());
                    plan.setCmnTucPlanRiyouYDay09(kaigo.getYDay09());
                    plan.setCmnTucPlanRiyouYDay10(kaigo.getYDay10());
                    plan.setCmnTucPlanRiyouYDay11(kaigo.getYDay11());
                    plan.setCmnTucPlanRiyouYDay12(kaigo.getYDay12());
                    plan.setCmnTucPlanRiyouYDay13(kaigo.getYDay13());
                    plan.setCmnTucPlanRiyouYDay14(kaigo.getYDay14());
                    plan.setCmnTucPlanRiyouYDay15(kaigo.getYDay15());
                    plan.setCmnTucPlanRiyouYDay16(kaigo.getYDay16());
                    plan.setCmnTucPlanRiyouYDay17(kaigo.getYDay17());
                    plan.setCmnTucPlanRiyouYDay18(kaigo.getYDay18());
                    plan.setCmnTucPlanRiyouYDay19(kaigo.getYDay19());
                    plan.setCmnTucPlanRiyouYDay20(kaigo.getYDay20());
                    plan.setCmnTucPlanRiyouYDay21(kaigo.getYDay21());
                    plan.setCmnTucPlanRiyouYDay22(kaigo.getYDay22());
                    plan.setCmnTucPlanRiyouYDay23(kaigo.getYDay23());
                    plan.setCmnTucPlanRiyouYDay24(kaigo.getYDay24());
                    plan.setCmnTucPlanRiyouYDay25(kaigo.getYDay25());
                    plan.setCmnTucPlanRiyouYDay26(kaigo.getYDay26());
                    plan.setCmnTucPlanRiyouYDay27(kaigo.getYDay27());
                    plan.setCmnTucPlanRiyouYDay28(kaigo.getYDay28());
                    plan.setCmnTucPlanRiyouYDay29(kaigo.getYDay29());
                    plan.setCmnTucPlanRiyouYDay30(kaigo.getYDay30());
                    plan.setCmnTucPlanRiyouYDay31(kaigo.getYDay31());
                    // 予定合計
                    plan.setCmnTucPlanRiyouYTotal(kaigo.getYTotal());
                    // 福祉用具貸与フラグ（予定）
                    plan.setCmnTucPlanRiyouYRentalF(kaigo.getYRentalF());
                    // 小規模での提供区分
                    plan.setCmnTucPlanRiyouShoukiboKbn(kaigo.getShoukiboKbn());
                    // 30日超過フラグ（予定）
                    plan.setCmnTucPlanRiyouYOv30Fl(kaigo.getYOv30Fl());
                    // 実施回数01 ~ 実施回数31
                    plan.setCmnTucPlanRiyouJDay01(kaigo.getJDay01());
                    plan.setCmnTucPlanRiyouJDay02(kaigo.getJDay02());
                    plan.setCmnTucPlanRiyouJDay03(kaigo.getJDay03());
                    plan.setCmnTucPlanRiyouJDay04(kaigo.getJDay04());
                    plan.setCmnTucPlanRiyouJDay05(kaigo.getJDay05());
                    plan.setCmnTucPlanRiyouJDay06(kaigo.getJDay06());
                    plan.setCmnTucPlanRiyouJDay07(kaigo.getJDay07());
                    plan.setCmnTucPlanRiyouJDay08(kaigo.getJDay08());
                    plan.setCmnTucPlanRiyouJDay09(kaigo.getJDay09());
                    plan.setCmnTucPlanRiyouJDay10(kaigo.getJDay10());
                    plan.setCmnTucPlanRiyouJDay11(kaigo.getJDay11());
                    plan.setCmnTucPlanRiyouJDay12(kaigo.getJDay12());
                    plan.setCmnTucPlanRiyouJDay13(kaigo.getJDay13());
                    plan.setCmnTucPlanRiyouJDay14(kaigo.getJDay14());
                    plan.setCmnTucPlanRiyouJDay15(kaigo.getJDay15());
                    plan.setCmnTucPlanRiyouJDay16(kaigo.getJDay16());
                    plan.setCmnTucPlanRiyouJDay17(kaigo.getJDay17());
                    plan.setCmnTucPlanRiyouJDay18(kaigo.getJDay18());
                    plan.setCmnTucPlanRiyouJDay19(kaigo.getJDay19());
                    plan.setCmnTucPlanRiyouJDay20(kaigo.getJDay20());
                    plan.setCmnTucPlanRiyouJDay21(kaigo.getJDay21());
                    plan.setCmnTucPlanRiyouJDay22(kaigo.getJDay22());
                    plan.setCmnTucPlanRiyouJDay23(kaigo.getJDay23());
                    plan.setCmnTucPlanRiyouJDay24(kaigo.getJDay24());
                    plan.setCmnTucPlanRiyouJDay25(kaigo.getJDay25());
                    plan.setCmnTucPlanRiyouJDay26(kaigo.getJDay26());
                    plan.setCmnTucPlanRiyouJDay27(kaigo.getJDay27());
                    plan.setCmnTucPlanRiyouJDay28(kaigo.getJDay28());
                    plan.setCmnTucPlanRiyouJDay29(kaigo.getJDay29());
                    plan.setCmnTucPlanRiyouJDay30(kaigo.getJDay30());
                    plan.setCmnTucPlanRiyouJDay31(kaigo.getJDay31());
                    // 実績合計
                    plan.setCmnTucPlanRiyouJTotal(kaigo.getJTotal());
                    // 福祉用具貸与フラグ（実績）
                    plan.setCmnTucPlanRiyouJRentalF(kaigo.getJRentalF());
                    // 30日超過フラグ（実績）
                    plan.setCmnTucPlanRiyouJOv30Fl(kaigo.getJOv30Fl());
                    // 予定転送日付
                    plan.setCmnTucPlanRiyouTensouTime(kaigo.getTensouTime());
                    // 転送枝番のバックアップ
                    plan.setCmnTucPlanRiyouEdaBack(kaigo.getEdaBack());
                    // ソート順
                    plan.setCmnTucPlanRiyouSortNo(kaigo.getSortNo());
                    // サービス変更年月日
                    plan.setCmnTucPlanRiyouHenkouTime(kaigo.getHenkouTime());
                    // 計画転送後修正有無フラグ
                    plan.setCmnTucPlanRiyouTensouFl(kaigo.getTensouFl());
                    // 福祉用具貸与マスタID
                    plan.setCmnTucPlanRiyouFygId(kaigo.getFygId());
                    // DMYシーケンス番号
                    plan.setDmySeqNo(kaigo.getDmySeqNo());
                    // 上限数
                    plan.setCmnTucPlanRiyouMax(kaigo.getMax());
                    // 予定済みフラグ
                    plan.setCmnTucPlanRiyouYoteiZumiFlg(kaigo.getYoteiZumiFlg());
                    // 実績済みフラグ
                    plan.setCmnTucPlanRiyouJissekiZumiFlg(kaigo.getJissekiZumiFlg());
                    // 項目名
                    plan.setDmyItemnameKnj(kaigo.getDmyItemNameKnj());
                    // 正式名
                    plan.setDmyFormalnameKnj(kaigo.getFormalName());
                    // 合成識別区分
                    plan.setCmnTucPlanRiyouGouseiSikKbn(kaigo.getGouseiSikKbn());
                    // 支給限度額対象区分
                    plan.setCmnTucPlanRiyouGenTaiKbn(kaigo.getGenTaiKbn());
                    // 親レコード番号
                    plan.setCmnTucPlanRiyouOyaLineNo(kaigo.getOyaLineNo());
                    // 算定単位
                    plan.setCmnTucPlanRiyouSanteiTani(kaigo.getSanteiTani());
                    // 期間時期
                    plan.setCmnTucPlanRiyouKikanJiki(kaigo.getKikanJiki());
                    // 回数日数
                    plan.setCmnTucPlanRiyouKaisuNisu(kaigo.getKaisuNisu());
                    // 点金区分
                    plan.setCmnTucPlanRiyouTenkintype(kaigo.getTenkintype());
                    // 規定数
                    plan.setCmnTucPlanRiyouDefault(kaigo.getKiteiSu());
                    // 開始時間
                    plan.setDmyStartTime(kaigo.getDmyStartTime());
                    // 終了時間
                    plan.setDmyEndTime(kaigo.getDmyEndTime());
                    // 更新区分
                    plan.setDmyModFComp(kaigo.getModFComp());
                    // 選択区分
                    plan.setDmyCbSel(kaigo.getCbSel());
                    // 回数Y
                    plan.setDmyDayY(kaigo.getDmyDayY());
                    // 回数J
                    plan.setDmyDayJ(kaigo.getDmyDayJ());
                    // 行数
                    plan.setDmyRow(kaigo.getDmyRow());
                    // 事業名
                    plan.setDmyJigyoNameKnj(kaigo.getDmyJigyoNameKnj());
                    // サービス単位数i_dmy
                    plan.setDmy0SvTani(kaigo.getDmy0SvTani());
                    // サービス単位数j_dmy
                    plan.setDmy0SvTanj(kaigo.getDmy0SvTanj());
                    // 端数R
                    plan.setDmyRHasu(kaigo.getDmyRHasu());

                    // サービス単位の表示非表示
                    plan.setDmySvTaniVisible(kaigo.getDmySvTaniVisible());
                    // 月1回数
                    plan.setDmyOneOfMonth(kaigo.getDmyOneOfMonth());

                    planInfoList.add(plan);
                }

                inDto1148.setPlanInfo(planInfoList);
            }

            if (CollectionUtils.isNotEmpty(searchResult.getDwShuruiList())) {
                // 種類別支給限度額情報 「関数_wf_00_datawindow_init」の処理「9」のアウトプット
                List<Gui01149ShuruiInfo> shuruiInfoList = new ArrayList<Gui01149ShuruiInfo>();
                for (KghCmnBeppyo2SyuruiSougouOutEntity dwshurui : searchResult.getDwShuruiList()) {
                    Gui01149ShuruiInfo shurui = new Gui01149ShuruiInfo();

                    // 支援事業者ID
                    shurui.setCmnTucPlanBetu2ShienId(CommonDtoUtil.objValToString(dwshurui.getShienId()));
                    // 利用者ID
                    shurui.setCmnTucPlanBetu2Userid(CommonDtoUtil.objValToString(dwshurui.getUserid()));
                    // サービス提供年月
                    shurui.setCmnTucPlanBetu2YymmYm(dwshurui.getYymmYm());
                    // サービス提供年月（変更日）
                    shurui.setCmnTucPlanBetu2YymmD(dwshurui.getYymmD());

                    shurui.setCmnTucPlanBetu2SvGendo1(CommonDtoUtil.objValToString(dwshurui.getSvGendo1()));
                    shurui.setCmnTucPlanBetu2SvGendo2(CommonDtoUtil.objValToString(dwshurui.getSvGendo2()));
                    shurui.setCmnTucPlanBetu2SvGendo3(CommonDtoUtil.objValToString(dwshurui.getSvGendo3()));
                    shurui.setCmnTucPlanBetu2SvGendo4(CommonDtoUtil.objValToString(dwshurui.getSvGendo4()));
                    shurui.setCmnTucPlanBetu2SvGendo5(CommonDtoUtil.objValToString(dwshurui.getSvGendo5()));
                    shurui.setCmnTucPlanBetu2SvGendo6(CommonDtoUtil.objValToString(dwshurui.getSvGendo6()));
                    shurui.setCmnTucPlanBetu2SvGendo7(CommonDtoUtil.objValToString(dwshurui.getSvGendo7()));
                    shurui.setCmnTucPlanBetu2SvGendo8(CommonDtoUtil.objValToString(dwshurui.getSvGendo8()));
                    shurui.setCmnTucPlanBetu2SvGendo9(CommonDtoUtil.objValToString(dwshurui.getSvGendo9()));
                    shurui.setCmnTucPlanBetu2SvGendo10(CommonDtoUtil.objValToString(dwshurui.getSvGendo10()));
                    shurui.setCmnTucPlanBetu2SvGendo11(CommonDtoUtil.objValToString(dwshurui.getSvGendo11()));
                    shurui.setCmnTucPlanBetu2SvGendo12(CommonDtoUtil.objValToString(dwshurui.getSvGendo12()));
                    shurui.setCmnTucPlanBetu2SvGendo13(CommonDtoUtil.objValToString(dwshurui.getSvGendo13()));
                    shurui.setCmnTucPlanBetu2SvGendo14(CommonDtoUtil.objValToString(dwshurui.getSvGendo14()));
                    shurui.setCmnTucPlanBetu2SvGendo15(CommonDtoUtil.objValToString(dwshurui.getSvGendo15()));
                    shurui.setCmnTucPlanBetu2SvGendo16(CommonDtoUtil.objValToString(dwshurui.getSvGendo16()));
                    shurui.setCmnTucPlanBetu2SvGendo17(CommonDtoUtil.objValToString(dwshurui.getSvGendo17()));
                    shurui.setCmnTucPlanBetu2SvGendo18(CommonDtoUtil.objValToString(dwshurui.getSvGendo18()));
                    shurui.setCmnTucPlanBetu2SvGendo19(CommonDtoUtil.objValToString(dwshurui.getSvGendo19()));
                    shurui.setCmnTucPlanBetu2SvGendo20(CommonDtoUtil.objValToString(dwshurui.getSvGendo20()));
                    shurui.setCmnTucPlanBetu2SvGendo21(CommonDtoUtil.objValToString(dwshurui.getSvGendo21()));
                    shurui.setCmnTucPlanBetu2SvGendo22(CommonDtoUtil.objValToString(dwshurui.getSvGendo22()));
                    shurui.setCmnTucPlanBetu2SvGendo23(CommonDtoUtil.objValToString(dwshurui.getSvGendo23()));
                    shurui.setCmnTucPlanBetu2SvGendo24(CommonDtoUtil.objValToString(dwshurui.getSvGendo24()));
                    shurui.setCmnTucPlanBetu2SvGendo25(CommonDtoUtil.objValToString(dwshurui.getSvGendo25()));
                    shurui.setCmnTucPlanBetu2SvGendo26(CommonDtoUtil.objValToString(dwshurui.getSvGendo26()));
                    shurui.setCmnTucPlanBetu2SvGendo27(CommonDtoUtil.objValToString(dwshurui.getSvGendo27()));

                    shurui.setCmnTucPlanBetu2SvTotal1(CommonDtoUtil.objValToString(dwshurui.getSvTotal1()));
                    shurui.setCmnTucPlanBetu2SvTotal2(CommonDtoUtil.objValToString(dwshurui.getSvTotal2()));
                    shurui.setCmnTucPlanBetu2SvTotal3(CommonDtoUtil.objValToString(dwshurui.getSvTotal3()));
                    shurui.setCmnTucPlanBetu2SvTotal4(CommonDtoUtil.objValToString(dwshurui.getSvTotal4()));
                    shurui.setCmnTucPlanBetu2SvTotal5(CommonDtoUtil.objValToString(dwshurui.getSvTotal5()));
                    shurui.setCmnTucPlanBetu2SvTotal6(CommonDtoUtil.objValToString(dwshurui.getSvTotal6()));
                    shurui.setCmnTucPlanBetu2SvTotal7(CommonDtoUtil.objValToString(dwshurui.getSvTotal7()));
                    shurui.setCmnTucPlanBetu2SvTotal8(CommonDtoUtil.objValToString(dwshurui.getSvTotal8()));
                    shurui.setCmnTucPlanBetu2SvTotal9(CommonDtoUtil.objValToString(dwshurui.getSvTotal9()));
                    shurui.setCmnTucPlanBetu2SvTotal10(CommonDtoUtil.objValToString(dwshurui.getSvTotal10()));
                    shurui.setCmnTucPlanBetu2SvTotal11(CommonDtoUtil.objValToString(dwshurui.getSvTotal11()));
                    shurui.setCmnTucPlanBetu2SvTotal12(CommonDtoUtil.objValToString(dwshurui.getSvTotal12()));
                    shurui.setCmnTucPlanBetu2SvTotal13(CommonDtoUtil.objValToString(dwshurui.getSvTotal13()));
                    shurui.setCmnTucPlanBetu2SvTotal14(CommonDtoUtil.objValToString(dwshurui.getSvTotal14()));
                    shurui.setCmnTucPlanBetu2SvTotal15(CommonDtoUtil.objValToString(dwshurui.getSvTotal15()));
                    shurui.setCmnTucPlanBetu2SvTotal16(CommonDtoUtil.objValToString(dwshurui.getSvTotal16()));
                    shurui.setCmnTucPlanBetu2SvTotal17(CommonDtoUtil.objValToString(dwshurui.getSvTotal17()));
                    shurui.setCmnTucPlanBetu2SvTotal18(CommonDtoUtil.objValToString(dwshurui.getSvTotal18()));
                    shurui.setCmnTucPlanBetu2SvTotal19(CommonDtoUtil.objValToString(dwshurui.getSvTotal19()));
                    shurui.setCmnTucPlanBetu2SvTotal20(CommonDtoUtil.objValToString(dwshurui.getSvTotal20()));
                    shurui.setCmnTucPlanBetu2SvTotal21(CommonDtoUtil.objValToString(dwshurui.getSvTotal21()));
                    shurui.setCmnTucPlanBetu2SvTotal22(CommonDtoUtil.objValToString(dwshurui.getSvTotal22()));
                    shurui.setCmnTucPlanBetu2SvTotal23(CommonDtoUtil.objValToString(dwshurui.getSvTotal23()));
                    shurui.setCmnTucPlanBetu2SvTotal24(CommonDtoUtil.objValToString(dwshurui.getSvTotal24()));
                    shurui.setCmnTucPlanBetu2SvTotal25(CommonDtoUtil.objValToString(dwshurui.getSvTotal25()));
                    shurui.setCmnTucPlanBetu2SvTotal26(CommonDtoUtil.objValToString(dwshurui.getSvTotal26()));
                    shurui.setCmnTucPlanBetu2SvTotal27(CommonDtoUtil.objValToString(dwshurui.getSvTotal27()));

                    shurui.setCmnTucPlanBetu2SvOver1(CommonDtoUtil.objValToString(dwshurui.getSvOver1()));
                    shurui.setCmnTucPlanBetu2SvOver2(CommonDtoUtil.objValToString(dwshurui.getSvOver2()));
                    shurui.setCmnTucPlanBetu2SvOver3(CommonDtoUtil.objValToString(dwshurui.getSvOver3()));
                    shurui.setCmnTucPlanBetu2SvOver4(CommonDtoUtil.objValToString(dwshurui.getSvOver4()));
                    shurui.setCmnTucPlanBetu2SvOver5(CommonDtoUtil.objValToString(dwshurui.getSvOver5()));
                    shurui.setCmnTucPlanBetu2SvOver6(CommonDtoUtil.objValToString(dwshurui.getSvOver6()));
                    shurui.setCmnTucPlanBetu2SvOver7(CommonDtoUtil.objValToString(dwshurui.getSvOver7()));
                    shurui.setCmnTucPlanBetu2SvOver8(CommonDtoUtil.objValToString(dwshurui.getSvOver8()));
                    shurui.setCmnTucPlanBetu2SvOver9(CommonDtoUtil.objValToString(dwshurui.getSvOver9()));
                    shurui.setCmnTucPlanBetu2SvOver10(CommonDtoUtil.objValToString(dwshurui.getSvOver10()));
                    shurui.setCmnTucPlanBetu2SvOver11(CommonDtoUtil.objValToString(dwshurui.getSvOver11()));
                    shurui.setCmnTucPlanBetu2SvOver12(CommonDtoUtil.objValToString(dwshurui.getSvOver12()));
                    shurui.setCmnTucPlanBetu2SvOver13(CommonDtoUtil.objValToString(dwshurui.getSvOver13()));
                    shurui.setCmnTucPlanBetu2SvOver14(CommonDtoUtil.objValToString(dwshurui.getSvOver14()));
                    shurui.setCmnTucPlanBetu2SvOver15(CommonDtoUtil.objValToString(dwshurui.getSvOver15()));
                    shurui.setCmnTucPlanBetu2SvOver16(CommonDtoUtil.objValToString(dwshurui.getSvOver16()));
                    shurui.setCmnTucPlanBetu2SvOver17(CommonDtoUtil.objValToString(dwshurui.getSvOver17()));
                    shurui.setCmnTucPlanBetu2SvOver18(CommonDtoUtil.objValToString(dwshurui.getSvOver18()));
                    shurui.setCmnTucPlanBetu2SvOver19(CommonDtoUtil.objValToString(dwshurui.getSvOver19()));
                    shurui.setCmnTucPlanBetu2SvOver20(CommonDtoUtil.objValToString(dwshurui.getSvOver20()));
                    shurui.setCmnTucPlanBetu2SvOver21(CommonDtoUtil.objValToString(dwshurui.getSvOver21()));
                    shurui.setCmnTucPlanBetu2SvOver22(CommonDtoUtil.objValToString(dwshurui.getSvOver22()));
                    shurui.setCmnTucPlanBetu2SvOver23(CommonDtoUtil.objValToString(dwshurui.getSvOver23()));
                    shurui.setCmnTucPlanBetu2SvOver24(CommonDtoUtil.objValToString(dwshurui.getSvOver24()));
                    shurui.setCmnTucPlanBetu2SvOver25(CommonDtoUtil.objValToString(dwshurui.getSvOver25()));
                    shurui.setCmnTucPlanBetu2SvOver26(CommonDtoUtil.objValToString(dwshurui.getSvOver26()));
                    shurui.setCmnTucPlanBetu2SvOver27(CommonDtoUtil.objValToString(dwshurui.getSvOver27()));

                    shurui.setCmnTucPlanBetu2SvName1(dwshurui.getSvName1());
                    shurui.setCmnTucPlanBetu2SvName2(dwshurui.getSvName2());
                    shurui.setCmnTucPlanBetu2SvName3(dwshurui.getSvName3());
                    shurui.setCmnTucPlanBetu2SvName4(dwshurui.getSvName4());
                    shurui.setCmnTucPlanBetu2SvName5(dwshurui.getSvName5());
                    shurui.setCmnTucPlanBetu2SvName6(dwshurui.getSvName6());
                    shurui.setCmnTucPlanBetu2SvName7(dwshurui.getSvName7());
                    shurui.setCmnTucPlanBetu2SvName8(dwshurui.getSvName8());
                    shurui.setCmnTucPlanBetu2SvName9(dwshurui.getSvName9());
                    shurui.setCmnTucPlanBetu2SvName10(dwshurui.getSvName10());
                    shurui.setCmnTucPlanBetu2SvName11(dwshurui.getSvName11());
                    shurui.setCmnTucPlanBetu2SvName12(dwshurui.getSvName12());

                    shuruiInfoList.add(shurui);
                }

                inDto1148.setShuruiInfo(shuruiInfoList);
            }

            if (CollectionUtils.isNotEmpty(searchResult.getDwBeppyoList())) {
                // 別表情報 「関数_wf_00_datawindow_init」の処理「6」のアウトプット
                List<Gui01149HoumonInfo> houmonInfoList = new ArrayList<Gui01149HoumonInfo>();

                for (Gui01042DwBeppyo beppyo : searchResult.getDwBeppyoList()) {
                    Gui01149HoumonInfo houmon = new Gui01149HoumonInfo();
                    // 支援事業者ID
                    houmon.setCmnTucPlanBetu1ShienId(CommonDtoUtil.objValToString(beppyo.getShienId()));
                    // 利用者ID
                    houmon.setCmnTucPlanBetu1Userid(CommonDtoUtil.objValToString(beppyo.getUserid()));
                    // サービス提供年月
                    houmon.setCmnTucPlanBetu1YymmYm(beppyo.getYymmYm());
                    // サービス提供年月（変更日）
                    houmon.setCmnTucPlanBetu1YymmD(beppyo.getYymmD());
                    // サービス事業者ID
                    houmon.setCmnTucPlanBetu1SvJigyoId(CommonDtoUtil.objValToString(beppyo.getSvJigyoId()));
                    // サービス項目ID
                    houmon.setCmnTucPlanBetu1SvItemCd(CommonDtoUtil.objValToString(beppyo.getSvItemCd()));
                    // 枝番
                    houmon.setCmnTucPlanBetu1EdaNo(CommonDtoUtil.objValToString(beppyo.getEdaNo()));
                    // サービス単位数
                    houmon.setCmnTucPlanBetu1Tensu(CommonDtoUtil.objValToString(beppyo.getTensu()));
                    // 回数
                    houmon.setCmnTucPlanBetu1Kaisu(CommonDtoUtil.objValToString(beppyo.getKaisu()));
                    // 合計単位数
                    houmon.setCmnTucPlanBetu1SvTensu(CommonDtoUtil.objValToString(beppyo.getSvTensu()));
                    // 種類限度外単位数
                    houmon.setCmnTucPlanBetu1STensuOver(CommonDtoUtil.objValToString(beppyo.getSTensuOver()));
                    // 種類限度額内単位数
                    houmon.setCmnTucPlanBetu1STensu(CommonDtoUtil.objValToString(beppyo.getSTensu()));
                    // 区分支給限度外単位数
                    houmon.setCmnTucPlanBetu1KTensuOver(CommonDtoUtil.objValToString(beppyo.getKTensuOver()));
                    // 区分支給限度内単位数
                    houmon.setCmnTucPlanBetu1KTensu(CommonDtoUtil.objValToString(beppyo.getKTensu()));
                    // 単位数単価
                    houmon.setCmnTucPlanBetu1Tanka(CommonDtoUtil.objValToString(beppyo.getTanka()));
                    // 利用者負担額保険給付対象分
                    houmon.setCmnTucPlanBetu1HutanH(CommonDtoUtil.objValToString(beppyo.getHutanH()));
                    // 利用者負担額全額自己負担分
                    houmon.setCmnTucPlanBetu1HutanJ(CommonDtoUtil.objValToString(beppyo.getHutanJ()));
                    // サービス種類CD
                    houmon.setCmnTucPlanBetu1Svtype(beppyo.getSvtype());
                    // サービス項目CD
                    houmon.setCmnTucPlanBetu1Svcode(beppyo.getSvcode());
                    // 合計フラグ
                    houmon.setCmnTucPlanBetu1TotalF(CommonDtoUtil.objValToString(beppyo.getTotalF()));
                    // 費用総額
                    houmon.setCmnTucPlanBetu1HiyouSougaku(CommonDtoUtil.objValToString(beppyo.getHiyouSougaku()));
                    // 給付率
                    houmon.setCmnTucPlanBetu1KyufuRitu(CommonDtoUtil.objValToString(beppyo.getKyufuRitu()));
                    // 介護保険給付額
                    houmon.setCmnTucPlanBetu1HKyufugaku(CommonDtoUtil.objValToString(beppyo.getHKyufugaku()));
                    // 加算フラグ
                    houmon.setCmnTucPlanBetu1KasanF(CommonDtoUtil.objValToString(beppyo.getKasanF()));
                    // サービスコード
                    houmon.setCmnTucPlanBetu1Scode(beppyo.getScode());
                    // 割引率
                    houmon.setCmnTucPlanBetu1WaribikiRitu(CommonDtoUtil.objValToString(beppyo.getWaribikiRitu()));
                    // 割引後単位数
                    houmon.setCmnTucPlanBetu1WaribikiTen(CommonDtoUtil.objValToString(beppyo.getWaribikiTen()));
                    // 30日超過フラグ
                    houmon.setCmnTucPlanBetu1Ov30Fl(CommonDtoUtil.objValToString(beppyo.getOv30Fl()));
                    // 定額利用者負担単価金額
                    houmon.setCmnTucPlanBetu1THutanTanka(CommonDtoUtil.objValToString(beppyo.getTHutanTanka()));
                    // 給付率差異フラグ
                    houmon.setCmnTucPlanBetu1KyufuDiffF(CommonDtoUtil.objValToString(beppyo.getKyufuDiffF()));
                    // 正式名
                    houmon.setDmyFormalnameKnj(beppyo.getDmyFormalnameKnj());
                    // 事業所番号
                    houmon.setDmyJigyoNumber(beppyo.getDmyJigyoNumber());
                    // 計算順
                    houmon.setDmyCalcSort(CommonDtoUtil.objValToString(beppyo.getDmyCalcSort()));
                    // 訪問通所限度額
                    houmon.setDmyKbnGendo(CommonDtoUtil.objValToString(beppyo.getDmyKbnGendo()));
                    // 給付管理単位数
                    houmon.setCmnTucPlanBetu1KyufukanriTen(beppyo.getKyufukanriTen());

                    houmonInfoList.add(houmon);
                }

                inDto1148.setHoumonInfo(houmonInfoList);
            }

            if (CollectionUtils.isNotEmpty(searchResult.getDwCalcsList())) {
                // 短期入所区分支給限度額管理情報 「関数_wf_00_datawindow_init」の処理「14」のアウトプット
                List<Gui01149TankiInfo> tankiInfoList = new ArrayList<Gui01149TankiInfo>();
                for (KghCmnRiyou5SumOutEntity cal : searchResult.getDwCalcsList()) {
                    Gui01149TankiInfo tanki = new Gui01149TankiInfo();

                    // 支援事業者ID
                    tanki.setCmnTucPlanShienId(CommonDtoUtil.objValToString(cal.getShienId()));
                    // 利用者ID
                    tanki.setCmnTucPlanUserid(CommonDtoUtil.objValToString(cal.getUserid()));
                    // サービス提供年月
                    tanki.setCmnTucPlanYymmYm(cal.getYymmYm());
                    // サービス提供年月（変更日）
                    tanki.setCmnTucPlanYymmD(cal.getYymmD());
                    // 保険者番号
                    tanki.setCmnTucPlanKHokenCd(CommonDtoUtil.objValToString(cal.getKHokenCd()));
                    // 被保険者番号
                    tanki.setCmnTucPlanHHokenNo(cal.getHHokenNo());
                    // 作成日
                    tanki.setCmnTucPlanCreateYmd(cal.getCreateYmd());
                    // 算定結果：予定（単位数）
                    tanki.setCmnTucPlanSvTensuY(CommonDtoUtil.objValToString(cal.getSvTensuY()));
                    // 利用者負担額保険給付対象分
                    tanki.setCmnTucPlanHHknHutan(CommonDtoUtil.objValToString(cal.getHHknHutan()));
                    // 利用者負担額全額自己負担分
                    tanki.setCmnTucPlanHJihHutan(CommonDtoUtil.objValToString(cal.getHJihHutan()));
                    // 前月の30日超の連続日数
                    tanki.setCmnTucPlanOv30Zengetu(CommonConstants.STR_0);
                    // 計算フラグ
                    tanki.setCmnTucPlanCalcYoji(CommonConstants.STR_1);
                    // 予定済みフラグ
                    tanki.setCmnTucPlanYoteiZumiFlg(CommonConstants.STR_0);
                    // 実績済みフラグ
                    tanki.setCmnTucPlanJissekiZumiFlg(CommonConstants.STR_0);
                    // 算定結果：実績（単位数）
                    tanki.setCmnTucPlanSvTensuJ(CommonDtoUtil.objValToString(cal.getSvTensuJ()));
                    // 種類限度超過単位数
                    tanki.setCmnTucPlanHShuOver(CommonDtoUtil.objValToString(cal.getHShuOver()));
                    // 種類限度内単位数
                    tanki.setCmnTucPlanHShuKijun(CommonDtoUtil.objValToString(cal.getHShuKijun()));
                    // 区分支給限度超過単位数
                    tanki.setCmnTucPlanHKbnOver(CommonDtoUtil.objValToString(cal.getHKbnOver()));
                    // 区分支給限度内単位数
                    tanki.setCmnTucPlanHKbnKijun(CommonDtoUtil.objValToString(cal.getHKbnKijun()));
                    // DMYKbnGendo
                    // 保険外利用料合計
                    // 公費負担額合計
                    tanki.setCmnTucPlanKouhiFutanFutanGaku(CommonDtoUtil.objValToString(cal.getFutanGaku()));
                    // 本人負担額合計
                    tanki.setCmnTucPlanKouhiFutanHonninFutanGaku(
                            CommonDtoUtil.objValToString(cal.getHonninFutanGaku()));
                    // 軽減額a
                    tanki.setCmnTucPlanSyafukuKeigenAKeigenGaku(CommonDtoUtil.objValToString(cal.getKeigenGaku()));
                    // 軽減額b
                    tanki.setCmnTucPlanSyafukuKeigenBKeigenGaku(CommonDtoUtil.objValToString(cal.getKeigenGaku2()));

                    tankiInfoList.add(tanki);
                }

                inDto1148.setTankiInfo(tankiInfoList);
            }

            // 自事業所ID
            inDto1148.setShienJigyoId(inDto.getSvJigyoId());
            // 変更年月日
            inDto1148.setHenkouYmd(CommonConstants.EMPTY_STRING);

            if (CollectionUtils.isNotEmpty(searchResult.getIds30ovDayList())) {
                // 短期入所連続利用３０日超過情報 「関数_wf_00_datawindow_init」の処理「10」のアウトプット
                List<Gui01149Over30Info> over30InfoList = new ArrayList<Gui01149Over30Info>();
                for (KghCmnPlanOv30OutEntity ov30 : searchResult.getIds30ovDayList()) {
                    Gui01149Over30Info ov30Info = new Gui01149Over30Info();

                    // 支援事業者ID
                    ov30Info.setCmnTucPlanOv30ShienId(CommonDtoUtil.objValToString(ov30.getShienId()));
                    // 利用者ID
                    ov30Info.setCmnTucPlanOv30Userid(CommonDtoUtil.objValToString(ov30.getUserid()));
                    // サービス提供年月
                    ov30Info.setCmnTucPlanOv30YymmYm(ov30.getYymmYm());
                    // サービス提供年月（変更日）
                    ov30Info.setCmnTucPlanOv30YymmD(ov30.getYymmD());
                    // サービス事業者ID
                    ov30Info.setCmnTucPlanOv30SvJigyoId(CommonDtoUtil.objValToString(ov30.getSvJigyoId()));
                    // サービス種別コード
                    ov30Info.setCmnTucPlanOv30Svtype(ov30.getSvtype());
                    // 30日超過該当日
                    ov30Info.setCmnTucPlanOv30Ov30Day(CommonDtoUtil.objValToString(ov30.getOv30Day()));

                    over30InfoList.add(ov30Info);
                }

                inDto1148.setOver30Info(over30InfoList);
            }

            // 予実フラグ
            inDto1148.setYojituF(CommonConstants.STR_1);
            // シミュレーションフラグ
            inDto1148.setSimulation(CommonConstants.STR_0);
            // 利用者id
            inDto1148.setUserid(CommonConstants.STR_0);

            if (CollectionUtils.isNotEmpty(searchResult.getIdsTrdList())) {
                // 短期入所利用日情報 「関数_wf_00_datawindow_init」の処理「12」のアウトプット
                List<Gui01149TrdInfo> trdInfoList = new ArrayList<Gui01149TrdInfo>();
                for (KghCmnTucSvplanShortOutEntity idstrd : searchResult.getIdsTrdList()) {
                    Gui01149TrdInfo trd = new Gui01149TrdInfo();

                    // 支援事業所番号
                    trd.setCmnTucSvplanShortShienId(CommonDtoUtil.objValToString(idstrd.getShienId()));
                    // 利用者id
                    trd.setCmnTucSvplanShortUserid(CommonDtoUtil.objValToString(idstrd.getUserid()));
                    // 提供年月
                    trd.setCmnTucSvplanShortYymmYm(idstrd.getYymmYm());
                    // 月途中保険者変更年月日
                    trd.setCmnTucSvplanShortHenkouYmd(idstrd.getHenkouYmd());
                    // サービス提供日（1） ~ サービス提供日（31）
                    trd.setCmnTucSvplanShortDay1(CommonDtoUtil.objValToString(idstrd.getDay1()));
                    trd.setCmnTucSvplanShortDay2(CommonDtoUtil.objValToString(idstrd.getDay2()));
                    trd.setCmnTucSvplanShortDay3(CommonDtoUtil.objValToString(idstrd.getDay3()));
                    trd.setCmnTucSvplanShortDay4(CommonDtoUtil.objValToString(idstrd.getDay4()));
                    trd.setCmnTucSvplanShortDay5(CommonDtoUtil.objValToString(idstrd.getDay5()));
                    trd.setCmnTucSvplanShortDay6(CommonDtoUtil.objValToString(idstrd.getDay6()));
                    trd.setCmnTucSvplanShortDay7(CommonDtoUtil.objValToString(idstrd.getDay7()));
                    trd.setCmnTucSvplanShortDay8(CommonDtoUtil.objValToString(idstrd.getDay8()));
                    trd.setCmnTucSvplanShortDay9(CommonDtoUtil.objValToString(idstrd.getDay9()));
                    trd.setCmnTucSvplanShortDay10(CommonDtoUtil.objValToString(idstrd.getDay10()));
                    trd.setCmnTucSvplanShortDay11(CommonDtoUtil.objValToString(idstrd.getDay11()));
                    trd.setCmnTucSvplanShortDay12(CommonDtoUtil.objValToString(idstrd.getDay12()));
                    trd.setCmnTucSvplanShortDay13(CommonDtoUtil.objValToString(idstrd.getDay13()));
                    trd.setCmnTucSvplanShortDay14(CommonDtoUtil.objValToString(idstrd.getDay14()));
                    trd.setCmnTucSvplanShortDay15(CommonDtoUtil.objValToString(idstrd.getDay15()));
                    trd.setCmnTucSvplanShortDay16(CommonDtoUtil.objValToString(idstrd.getDay16()));
                    trd.setCmnTucSvplanShortDay17(CommonDtoUtil.objValToString(idstrd.getDay17()));
                    trd.setCmnTucSvplanShortDay18(CommonDtoUtil.objValToString(idstrd.getDay18()));
                    trd.setCmnTucSvplanShortDay19(CommonDtoUtil.objValToString(idstrd.getDay19()));
                    trd.setCmnTucSvplanShortDay20(CommonDtoUtil.objValToString(idstrd.getDay20()));
                    trd.setCmnTucSvplanShortDay21(CommonDtoUtil.objValToString(idstrd.getDay21()));
                    trd.setCmnTucSvplanShortDay22(CommonDtoUtil.objValToString(idstrd.getDay22()));
                    trd.setCmnTucSvplanShortDay23(CommonDtoUtil.objValToString(idstrd.getDay23()));
                    trd.setCmnTucSvplanShortDay24(CommonDtoUtil.objValToString(idstrd.getDay24()));
                    trd.setCmnTucSvplanShortDay25(CommonDtoUtil.objValToString(idstrd.getDay25()));
                    trd.setCmnTucSvplanShortDay26(CommonDtoUtil.objValToString(idstrd.getDay26()));
                    trd.setCmnTucSvplanShortDay27(CommonDtoUtil.objValToString(idstrd.getDay27()));
                    trd.setCmnTucSvplanShortDay28(CommonDtoUtil.objValToString(idstrd.getDay28()));
                    trd.setCmnTucSvplanShortDay29(CommonDtoUtil.objValToString(idstrd.getDay29()));
                    trd.setCmnTucSvplanShortDay30(CommonDtoUtil.objValToString(idstrd.getDay30()));
                    trd.setCmnTucSvplanShortDay31(CommonDtoUtil.objValToString(idstrd.getDay31()));

                    // 合計負担額
                    trd.setCmnTucSvplanShortTotal(CommonDtoUtil.objValToString(idstrd.getTotal()));
                    // 開始日
                    trd.setCmnTucSvplanShortStDay(CommonDtoUtil.objValToString(idstrd.getStDay()));
                    // 終了日
                    trd.setCmnTucSvplanShortEdDay(CommonDtoUtil.objValToString(idstrd.getEdDay()));

                    trdInfoList.add(trd);
                }

                inDto1148.setTrdInfo(trdInfoList);
            }

            if (CollectionUtils.isNotEmpty(svJigyoIdList)) {
                // 利用する事業所id配列next
                inDto1148.setLUseJigyo(svJigyoIdList.stream().map(item -> CommonDtoUtil.objValToString(item)).toList());
            }
            // 適用事業所id配列next
            inDto1148.setLTekiyoJigyo(inDto.getSvJigyoIds());
            // 提供年月日の日
            inDto1148.setSDd(CommonConstants.STR_DAY_ALL_ZERO);

            if (CollectionUtils.isNotEmpty(searchResult.getDwKouhiList())) {
                // 公費用情報 「関数_wf_00_datawindow_init」の処理「7」のアウトプット
                List<Gui01149KouhiInfo> kouhiInfoList = new ArrayList<Gui01149KouhiInfo>();
                for (KghCmnBeppyo3KohiOutEntity dwkouhi : searchResult.getDwKouhiList()) {
                    Gui01149KouhiInfo kouhi = new Gui01149KouhiInfo();

                    // サービス事業者ID
                    kouhi.setCmnTucPlanKouhiFutanSvJigyoId(CommonDtoUtil.objValToString(dwkouhi.getSvJigyoId()));
                    // 公費分負担額
                    kouhi.setCmnTucPlanKouhiFutanFutanGaku(CommonDtoUtil.objValToString(dwkouhi.getFutanGaku()));
                    // 公費分本人負担額
                    kouhi.setCmnTucPlanKouhiFutanHonninFutanGaku(
                            CommonDtoUtil.objValToString(dwkouhi.getHonninFutanGaku()));
                    // 支援事業者ID
                    kouhi.setCmnTucPlanKouhiFutanShienId(CommonDtoUtil.objValToString(dwkouhi.getShienId()));
                    // 利用者ID
                    kouhi.setCmnTucPlanKouhiFutanUserid(CommonDtoUtil.objValToString(dwkouhi.getUserid()));
                    // サービス提供年月
                    kouhi.setCmnTucPlanKouhiFutanYymmYm(dwkouhi.getYymmYm());
                    // サービス提供年月（変更日）
                    kouhi.setCmnTucPlanKouhiFutanYymmD(dwkouhi.getYymmD());
                    // 公費法制コード
                    kouhi.setCmnTucPlanKouhiFutanCode(dwkouhi.getCode());
                    // サービス種類
                    kouhi.setCmnTucPlanKouhiFutanSvtype(dwkouhi.getSvtype());

                    kouhiInfoList.add(kouhi);
                }

                inDto1148.setKouhiInfo(kouhiInfoList);
            }

            if (CollectionUtils.isNotEmpty(searchResult.getDwKeigenList())) {
                // 社福軽減用情報 「関数_wf_00_datawindow_init」の処理「8」のアウトプット
                List<Gui01149KeigenInfo> keigenInfoList = new ArrayList<Gui01149KeigenInfo>();
                for (KghCmnBeppyo3SyafukuOutEntity dwkeigen : searchResult.getDwKeigenList()) {
                    Gui01149KeigenInfo keigen = new Gui01149KeigenInfo();

                    // 支援事業者ID
                    keigen.setCmnTucPlanSyafukuKeigenShienId(CommonDtoUtil.objValToString(dwkeigen.getShienId()));
                    // 利用者ID
                    keigen.setCmnTucPlanSyafukuKeigenUserid(CommonDtoUtil.objValToString(dwkeigen.getUserid()));
                    // サービス提供年月
                    keigen.setCmnTucPlanSyafukuKeigenYymmYm(dwkeigen.getYymmYm());
                    // サービス提供年月（変更日）
                    keigen.setCmnTucPlanSyafukuKeigenYymmD(dwkeigen.getYymmD());
                    // サービス事業者ID
                    keigen.setCmnTucPlanSyafukuKeigenSvJigyoId(CommonDtoUtil.objValToString(dwkeigen.getSvJigyoId()));
                    // サービス種類コード
                    keigen.setCmnTucPlanSyafukuKeigenSvShuCd(dwkeigen.getSvShuCd());
                    // 保険内外区分
                    keigen.setCmnTucPlanSyafukuKeigenKubun(CommonDtoUtil.objValToString(dwkeigen.getKubun()));
                    // 対象額
                    keigen.setCmnTucPlanSyafukuKeigenTaishoGaku(CommonDtoUtil.objValToString(dwkeigen.getTaishoGaku()));
                    // 軽減率
                    keigen.setCmnTucPlanSyafukuKeigenKeigenRitu(CommonDtoUtil.objValToString(dwkeigen.getKeigenRitu()));
                    // 軽減額
                    keigen.setCmnTucPlanSyafukuKeigenKeigenGaku(CommonDtoUtil.objValToString(dwkeigen.getKeigenGaku()));
                    // 作成区分
                    keigen.setCmnTucPlanSyafukuKeigenSakuKbn(CommonDtoUtil.objValToString(dwkeigen.getSakuKbn()));

                    keigenInfoList.add(keigen);
                }

                inDto1148.setKeigenInfo(keigenInfoList);
            }

            if (CollectionUtils.isNotEmpty(searchResult.getIdsPointList())) {
                // 利用票別表:提供事業所毎小計情報 「関数_wf_00_datawindow_init」の処理「13」のアウトプット
                List<Gui01149SvplanPointInfo> svplanPointInfoList = new ArrayList<Gui01149SvplanPointInfo>();
                for (KghCmnTucSvPointOutEntity idspoint : searchResult.getIdsPointList()) {
                    Gui01149SvplanPointInfo point = new Gui01149SvplanPointInfo();

                    // 利用者ID
                    point.setCmnTucServicePointUserid(CommonDtoUtil.objValToString(idspoint.getUserid()));
                    // 処理年月
                    point.setCmnTucServicePointYymmYm(idspoint.getYymmYm());
                    // 法人ID
                    point.setCmnTucServicePointHoujinId(CommonDtoUtil.objValToString(idspoint.getHoujinId()));
                    // 施設ID
                    point.setCmnTucServicePointShisetuId(CommonDtoUtil.objValToString(idspoint.getShisetuId()));
                    // サービス事業者ID
                    point.setCmnTucServicePointSvJigyoId(CommonDtoUtil.objValToString(idspoint.getSvJigyoId()));
                    // サービス種類コード
                    point.setCmnTucServicePointSvtype(idspoint.getSvtype());
                    // 保険対象点数
                    point.setCmnTucServicePointPoint(CommonDtoUtil.objValToString(idspoint.getPoint()));
                    // 保険対象外点数
                    point.setCmnTucServicePointGaiPoint(CommonDtoUtil.objValToString(idspoint.getGaiPoint()));
                    // 保険対象日数
                    point.setCmnTucServicePointNumber(CommonDtoUtil.objValToString(idspoint.getNumber()));
                    // 保険対象外日数
                    point.setCmnTucServicePointGaiNumber(CommonDtoUtil.objValToString(idspoint.getGaiNumber()));
                    // 医療費控除フラグ
                    point.setCmnTucServicePointIryohiKojoFl(CommonDtoUtil.objValToString(idspoint.getIryohiKojoFl()));

                    svplanPointInfoList.add(point);
                }

                inDto1148.setSvplanPointInfo(svplanPointInfoList);
            }

            // 呼出元フラグ
            inDto1148.setIsWeekplan(CommonConstants.STR_1);
            // パッケージ
            inDto1148.setIsPackage(CommonConstants.STR_0);

            if (CollectionUtils.isNotEmpty(searchResult.getIdsKakuninList())) {
                // 算定確認情報 「関数_wf_00_datawindow_init」の処理「11」のアウトプット
                List<Gui01149KakuninInfo> kakuninInfoList = new ArrayList<Gui01149KakuninInfo>();
                for (KghCmnTucPlanRiyouKakuninOutEntity idskakunin : searchResult.getIdsKakuninList()) {
                    Gui01149KakuninInfo kakunin = new Gui01149KakuninInfo();

                    // 支援事業者ID
                    kakunin.setCmnTucPlanRiyouKakuninShienId(CommonDtoUtil.objValToString(idskakunin.getShienId()));
                    // 利用者ID
                    kakunin.setCmnTucPlanRiyouKakuninUserid(CommonDtoUtil.objValToString(idskakunin.getUserid()));
                    // サービス提供年月
                    kakunin.setCmnTucPlanRiyouKakuninYymmYm(idskakunin.getYymmYm());
                    // サービス提供年月（変更日）
                    kakunin.setCmnTucPlanRiyouKakuninYymmD(idskakunin.getYymmD());
                    // サービス事業者ID
                    kakunin.setCmnTucPlanRiyouKakuninSvJigyoId(CommonDtoUtil.objValToString(idskakunin.getSvJigyoId()));
                    // サービス項目ID
                    kakunin.setCmnTucPlanRiyouKakuninSvItemCd(CommonDtoUtil.objValToString(idskakunin.getSvItemCd()));
                    // 枝番
                    kakunin.setCmnTucPlanRiyouKakuninEdaNo(CommonDtoUtil.objValToString(idskakunin.getEdaNo()));
                    // 親レコード番号
                    kakunin.setCmnTucPlanRiyouKakuninOyaLineNo(CommonDtoUtil.objValToString(idskakunin.getOyaLineNo()));
                    // 有効期間ID
                    kakunin.setCmnTucPlanRiyouKakuninTermid(CommonDtoUtil.objValToString(idskakunin.getTermid()));
                    // サービス種別コード
                    kakunin.setCmnTucPlanRiyouKakuninSvtype(idskakunin.getSvtype());
                    // サービス項目コード
                    kakunin.setCmnTucPlanRiyouKakuninSvcode(idskakunin.getSvcode());
                    // サービスコード
                    kakunin.setCmnTucPlanRiyouKakuninScode(idskakunin.getScode());
                    // 合成識別区分
                    kakunin.setCmnTucPlanRiyouKakuninGouseiSikKbn(idskakunin.getGouseiSikKbn());
                    // サービス開始時間
                    kakunin.setCmnTucPlanRiyouKakuninSvStartTime(idskakunin.getSvStartTime());
                    // サービス終了時間
                    kakunin.setCmnTucPlanRiyouKakuninSvEndTime(idskakunin.getSvEndTime());
                    // サービス単位数
                    kakunin.setCmnTucPlanRiyouKakuninSvTani(CommonDtoUtil.objValToString(idskakunin.getSvTani()));
                    // 算定単位
                    kakunin.setCmnTucPlanRiyouKakuninSanteiTani(idskakunin.getSanteiTani());
                    // 期間時期
                    kakunin.setCmnTucPlanRiyouKakuninKikanJiki(idskakunin.getKikanJiki());
                    // 回数日数
                    kakunin.setCmnTucPlanRiyouKakuninKaisuNisu(CommonDtoUtil.objValToString(idskakunin.getKaisuNisu()));
                    // 支給限度額対象区分
                    kakunin.setCmnTucPlanRiyouKakuninGenTaiKbn(idskakunin.getGenTaiKbn());
                    // 上限数
                    kakunin.setCmnTucPlanRiyouKakuninMax(CommonDtoUtil.objValToString(idskakunin.getMax()));
                    // 点金区分
                    kakunin.setCmnTucPlanRiyouKakuninTenkintype(
                            CommonDtoUtil.objValToString(idskakunin.getTenkintype()));
                    // 規定数
                    kakunin.setCmnTucPlanRiyouKakuninDefault(CommonDtoUtil.objValToString(idskakunin.getDefaultVal()));
                    // 予定回数01 ~ 予定回数31
                    kakunin.setCmnTucPlanRiyouKakuninYDay01(CommonDtoUtil.objValToString(idskakunin.getYDay01()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay02(CommonDtoUtil.objValToString(idskakunin.getYDay02()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay03(CommonDtoUtil.objValToString(idskakunin.getYDay03()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay04(CommonDtoUtil.objValToString(idskakunin.getYDay04()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay05(CommonDtoUtil.objValToString(idskakunin.getYDay05()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay06(CommonDtoUtil.objValToString(idskakunin.getYDay06()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay07(CommonDtoUtil.objValToString(idskakunin.getYDay07()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay08(CommonDtoUtil.objValToString(idskakunin.getYDay08()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay09(CommonDtoUtil.objValToString(idskakunin.getYDay09()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay10(CommonDtoUtil.objValToString(idskakunin.getYDay10()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay11(CommonDtoUtil.objValToString(idskakunin.getYDay11()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay12(CommonDtoUtil.objValToString(idskakunin.getYDay12()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay13(CommonDtoUtil.objValToString(idskakunin.getYDay13()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay14(CommonDtoUtil.objValToString(idskakunin.getYDay14()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay15(CommonDtoUtil.objValToString(idskakunin.getYDay15()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay16(CommonDtoUtil.objValToString(idskakunin.getYDay16()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay17(CommonDtoUtil.objValToString(idskakunin.getYDay17()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay18(CommonDtoUtil.objValToString(idskakunin.getYDay18()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay19(CommonDtoUtil.objValToString(idskakunin.getYDay19()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay20(CommonDtoUtil.objValToString(idskakunin.getYDay20()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay21(CommonDtoUtil.objValToString(idskakunin.getYDay21()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay22(CommonDtoUtil.objValToString(idskakunin.getYDay22()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay23(CommonDtoUtil.objValToString(idskakunin.getYDay23()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay24(CommonDtoUtil.objValToString(idskakunin.getYDay24()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay25(CommonDtoUtil.objValToString(idskakunin.getYDay25()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay26(CommonDtoUtil.objValToString(idskakunin.getYDay26()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay27(CommonDtoUtil.objValToString(idskakunin.getYDay27()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay28(CommonDtoUtil.objValToString(idskakunin.getYDay28()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay29(CommonDtoUtil.objValToString(idskakunin.getYDay29()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay30(CommonDtoUtil.objValToString(idskakunin.getYDay30()));
                    kakunin.setCmnTucPlanRiyouKakuninYDay31(CommonDtoUtil.objValToString(idskakunin.getYDay31()));
                    // 予定合計
                    kakunin.setCmnTucPlanRiyouKakuninYTotal(CommonDtoUtil.objValToString(idskakunin.getYTotal()));
                    // 福祉用具貸与フラグ（予定）
                    kakunin.setCmnTucPlanRiyouKakuninYRentalF(CommonDtoUtil.objValToString(idskakunin.getYRentalF()));
                    // 30日超過フラグ（予定）
                    kakunin.setCmnTucPlanRiyouKakuninYOv30Fl(CommonDtoUtil.objValToString(idskakunin.getYOv30Fl()));
                    // 実施回数01 ~ 実施回数31
                    kakunin.setCmnTucPlanRiyouKakuninJDay01(CommonDtoUtil.objValToString(idskakunin.getJDay01()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay02(CommonDtoUtil.objValToString(idskakunin.getJDay02()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay03(CommonDtoUtil.objValToString(idskakunin.getJDay03()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay04(CommonDtoUtil.objValToString(idskakunin.getJDay04()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay05(CommonDtoUtil.objValToString(idskakunin.getJDay05()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay06(CommonDtoUtil.objValToString(idskakunin.getJDay06()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay07(CommonDtoUtil.objValToString(idskakunin.getJDay07()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay08(CommonDtoUtil.objValToString(idskakunin.getJDay08()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay09(CommonDtoUtil.objValToString(idskakunin.getJDay09()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay10(CommonDtoUtil.objValToString(idskakunin.getJDay10()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay11(CommonDtoUtil.objValToString(idskakunin.getJDay11()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay12(CommonDtoUtil.objValToString(idskakunin.getJDay12()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay13(CommonDtoUtil.objValToString(idskakunin.getJDay13()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay14(CommonDtoUtil.objValToString(idskakunin.getJDay14()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay15(CommonDtoUtil.objValToString(idskakunin.getJDay15()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay16(CommonDtoUtil.objValToString(idskakunin.getJDay16()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay17(CommonDtoUtil.objValToString(idskakunin.getJDay17()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay18(CommonDtoUtil.objValToString(idskakunin.getJDay18()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay19(CommonDtoUtil.objValToString(idskakunin.getJDay19()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay20(CommonDtoUtil.objValToString(idskakunin.getJDay20()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay21(CommonDtoUtil.objValToString(idskakunin.getJDay21()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay22(CommonDtoUtil.objValToString(idskakunin.getJDay22()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay23(CommonDtoUtil.objValToString(idskakunin.getJDay23()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay24(CommonDtoUtil.objValToString(idskakunin.getJDay24()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay25(CommonDtoUtil.objValToString(idskakunin.getJDay25()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay26(CommonDtoUtil.objValToString(idskakunin.getJDay26()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay27(CommonDtoUtil.objValToString(idskakunin.getJDay27()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay28(CommonDtoUtil.objValToString(idskakunin.getJDay28()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay29(CommonDtoUtil.objValToString(idskakunin.getJDay29()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay30(CommonDtoUtil.objValToString(idskakunin.getJDay30()));
                    kakunin.setCmnTucPlanRiyouKakuninJDay31(CommonDtoUtil.objValToString(idskakunin.getJDay31()));
                    // 実績合計
                    kakunin.setCmnTucPlanRiyouKakuninJTotal(CommonDtoUtil.objValToString(idskakunin.getJTotal()));
                    // 福祉用具貸与フラグ（実績）
                    kakunin.setCmnTucPlanRiyouKakuninJRentalF(CommonDtoUtil.objValToString(idskakunin.getJRentalF()));
                    // 30日超過フラグ（実績）
                    kakunin.setCmnTucPlanRiyouKakuninJOv30Fl(CommonDtoUtil.objValToString(idskakunin.getJOv30Fl()));

                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD);

                    // 予定転送日付
                    kakunin.setCmnTucPlanRiyouKakuninTensouTime(
                            idskakunin.getTensouTime().toLocalDateTime().format(formatter));
                    // 転送枝番のバックアップ
                    kakunin.setCmnTucPlanRiyouKakuninEdaBack(CommonDtoUtil.objValToString(idskakunin.getEdaBack()));
                    // ソート順
                    kakunin.setCmnTucPlanRiyouKakuninSortNo(CommonDtoUtil.objValToString(idskakunin.getSortNo()));
                    // 小規模での提供区分
                    kakunin.setCmnTucPlanRiyouKakuninShoukiboKbn(
                            CommonDtoUtil.objValToString(idskakunin.getShoukiboKbn()));
                    // サービス変更年月日
                    kakunin.setCmnTucPlanRiyouKakuninHenkouTime(
                            idskakunin.getHenkouTime().toLocalDateTime().format(formatter));
                    // 計画転送後修正有無フラグ
                    kakunin.setCmnTucPlanRiyouKakuninTensouFl(CommonDtoUtil.objValToString(idskakunin.getTensouFl()));
                    // 福祉用具貸与マスタID
                    kakunin.setCmnTucPlanRiyouKakuninFygId(CommonDtoUtil.objValToString(idskakunin.getFygId()));
                    // 予定済みフラグ
                    kakunin.setCmnTucPlanRiyouKakuninYoteiZumiFlg(
                            CommonDtoUtil.objValToString(idskakunin.getYoteiZumiFlg()));
                    // 実績済みフラグ
                    kakunin.setCmnTucPlanRiyouKakuninJissekiZumiFlg(
                            CommonDtoUtil.objValToString(idskakunin.getJissekiZumiFlg()));
                    // DMY開始日
                    kakunin.setDmyStartTime(idskakunin.getDmyStartTime());
                    // 終了日
                    kakunin.setDmyEndTime(idskakunin.getDmyEndTime());

                    kakuninInfoList.add(kakunin);
                }

                inDto1148.setKakuninInfo(kakuninInfoList);
            }

            comDemandEngineConnectionCalculationProcSelectServiceImpl.mainProcess(inDto1148);

            // 11.5. フッタに金額をセット
            sumData = wf_20_set_kingaku(searchResult.getDwGendoList(), searchResult.getDwUsrinfList(),
                    searchResult.getDwBeppyoList(), searchResult.getDwKouhiList());
        }

        // 12. 戻り値を設定する
        result = searchResult;
        result.setGaisanFlg(true);
        result.setSumData(sumData);
        return result;
    }

    /**
     * ダミーで検索
     * 
     * @return 検索 の戻り値
     */
    public Gui01042SerachResult wf_00_datawindow_init() throws Exception {
        // 1. 変数を初期化
        // 変数.年月(ls_ym)= "0000/00"
        String ls_ym = CommonConstants.STR_YEAR_MONTH_ALL_ZERO;
        // 変数.日(ls_dd) = "00"
        String ls_dd = CommonConstants.STR_DAY_ALL_ZERO;
        // 変数.時間(ls_hh)= "0000/00/00"
        String ls_hh = CommonConstants.STR_YEAR_MONTH_DAY_ALL_ZERO;

        // 2. SQLを発行し、利用票：ヘッダ情報(dw_usrinf)を取得する
        KghCmnRiyou2HeaderAcsByCriteriaInEntity kghCmnRiyou2HeaderAcsByCriteriaInEntity = new KghCmnRiyou2HeaderAcsByCriteriaInEntity();
        // 支援事業者ID
        kghCmnRiyou2HeaderAcsByCriteriaInEntity.setShienId(CommonConstants.DEFAULT_SV_JIGYO_ID);
        // 利用者ID
        kghCmnRiyou2HeaderAcsByCriteriaInEntity.setUserId(CommonConstants.DEFAULT_USER_ID);
        // サービス提供年月
        kghCmnRiyou2HeaderAcsByCriteriaInEntity.setTeiYm(ls_ym);
        // サービス提供年月（変更日）
        kghCmnRiyou2HeaderAcsByCriteriaInEntity.setTeiD(ls_dd);
        // 戻り値.利用票：ヘッダ情報(dw_usrinf) = 上記取得結果
        List<KghCmnRiyou2HeaderAcsOutEntity> dwUsrinfList = cmnTucPlanSelectMapper
                .findKghCmnRiyou2HeaderAcsByCriteria(kghCmnRiyou2HeaderAcsByCriteriaInEntity);

        // 3. SQLを発行し、サービス費リストの対象サービス明細(dw_master)を取得する
        KghCmnRiyouHiwariSanteiRiyouByCriteriaInEntity kghCmnRiyouHiwariSanteiRiyouByCriteriaInEntity = new KghCmnRiyouHiwariSanteiRiyouByCriteriaInEntity();
        // 支援事業者ID
        kghCmnRiyouHiwariSanteiRiyouByCriteriaInEntity.setShienId(CommonConstants.DEFAULT_SV_JIGYO_ID);
        // 利用者ID
        kghCmnRiyouHiwariSanteiRiyouByCriteriaInEntity.setUserid(CommonConstants.DEFAULT_USER_ID);
        // サービス提供年月
        kghCmnRiyouHiwariSanteiRiyouByCriteriaInEntity.setTeiYm(ls_ym);
        // サービス提供年月（変更日）
        kghCmnRiyouHiwariSanteiRiyouByCriteriaInEntity.setTeiYmd(ls_dd);
        // 戻り値.サービス費リストの対象サービス明細(dw_master) = 上記取得結果
        List<KghCmnRiyouHiwariSanteiRiyouOutEntity> dwMasterList = cmnTucPlanRiyouSelectMapper
                .findKghCmnRiyouHiwariSanteiRiyouByCriteria(kghCmnRiyouHiwariSanteiRiyouByCriteriaInEntity);

        // 4. SQLを発行し、サービス利用票.別表ヘッダ(ids_plan)を取得する
        KghCmnTucPlanByCriteriaInEntity kghCmnTucPlanByCriteriaInEntity = new KghCmnTucPlanByCriteriaInEntity();
        // 支援事業者ID
        kghCmnTucPlanByCriteriaInEntity.setShienId(CommonConstants.DEFAULT_SV_JIGYO_ID);
        // 利用者ID
        kghCmnTucPlanByCriteriaInEntity.setUserId(CommonConstants.DEFAULT_USER_ID);
        // サービス提供年月
        kghCmnTucPlanByCriteriaInEntity.setTeiYm(ls_ym);
        // サービス提供年月（変更日）
        kghCmnTucPlanByCriteriaInEntity.setTeiYmD(ls_dd);
        // 戻り値.サービス利用票.別表ヘッダ(ids_plan) = 上記取得結果
        List<KghCmnTucPlanOutEntity> idsPlanList = cmnTucPlanSelectMapper
                .findKghCmnTucPlanByCriteria(kghCmnTucPlanByCriteriaInEntity);

        // 5. SQLを発行し、利用票：日数.単位数.金額の計算結果欄(dw_gendo)を取得する
        KghCmnRiyou5SumByCriteriaInEntity kghCmnRiyou5SumByCriteriaInEntity = new KghCmnRiyou5SumByCriteriaInEntity();
        // 戻り値. 利用票：日数.単位数.金額の計算結果欄(dw_gendo)= 上記取得結果
        List<KghCmnRiyou5SumOutEntity> dwGendoList = riyouSumSelectMapper
                .findKghCmnRiyou5SumByCriteria(kghCmnRiyou5SumByCriteriaInEntity);

        // 6. SQLを発行し、利用票別表：明細行(dw_beppyo)を取得する
        KghCmnBeppyo1List202504ByCriteriaInEntity kghCmnBeppyo1List202504ByCriteriaInEntity = new KghCmnBeppyo1List202504ByCriteriaInEntity();
        // 支援事業者ID
        kghCmnBeppyo1List202504ByCriteriaInEntity.setAlShienId(CommonConstants.DEFAULT_SV_JIGYO_ID);
        // 利用者ID
        kghCmnBeppyo1List202504ByCriteriaInEntity.setAlUserid(CommonConstants.DEFAULT_USER_ID);
        // サービス提供年月
        kghCmnBeppyo1List202504ByCriteriaInEntity.setAsYymmYm(ls_ym);
        // サービス提供年月（変更日）
        kghCmnBeppyo1List202504ByCriteriaInEntity.setAsYymmD(ls_dd);
        // 戻り値. 利用票別表：明細行(dw_beppyo)= 上記取得結果
        List<KghCmnBeppyo1List202504OutEntity> beppyoOutList = cmnTucPlanBetu1SelectMapper
                .findKghCmnBeppyo1List202504ByCriteria(kghCmnBeppyo1List202504ByCriteriaInEntity);

        List<Gui01042DwBeppyo> dwBeppyoList = new ArrayList<Gui01042DwBeppyo>();
        for (KghCmnBeppyo1List202504OutEntity item : beppyoOutList) {
            Gui01042DwBeppyo newItem = new Gui01042DwBeppyo();

            newItem.setKyufuRitu(item.getKyufuRitu());
            newItem.setHKyufugaku(item.getHKyufugaku());
            newItem.setScode(item.getScode());
            newItem.setWaribikiRitu(item.getWaribikiRitu());
            newItem.setWaribikiTen(item.getWaribikiTen());
            newItem.setTHutanTanka(item.getTHutanTanka());
            newItem.setDmyFormalnameKnj(item.getDmyFormalnameKnj());
            newItem.setSvTensu(item.getSvTensu());
            newItem.setDmyJigyoNumber(item.getDmyJigyoNumber());
            newItem.setSTensuOver(item.getSTensuOver());
            newItem.setDmyCalcSort(item.getDmyCalcSort());
            newItem.setSTensu(item.getSTensu());
            newItem.setDmyKbnGendo(item.getDmyKbnGendo());
            newItem.setKTensuOver(item.getKTensuOver());
            newItem.setKTensu(item.getKTensu());
            newItem.setTanka(item.getTanka());
            newItem.setHutanH(item.getHutanH());
            newItem.setHutanJ(item.getHutanJ());
            newItem.setSvtype(item.getSvtype());
            newItem.setSvcode(item.getSvcode());
            newItem.setShienId(item.getShienId());
            newItem.setUserid(item.getUserid());
            newItem.setYymmYm(item.getYymmYm());
            newItem.setYymmD(item.getYymmD());
            newItem.setSvJigyoId(item.getSvJigyoId());
            newItem.setSvItemCd(item.getSvItemCd());
            newItem.setEdaNo(item.getEdaNo());
            newItem.setTensu(item.getTensu());
            newItem.setOv30Fl(item.getOv30Fl());
            newItem.setKyufuDiffF(item.getKyufuDiffF());
            newItem.setKyufukanriTen(CommonDtoUtil.objValToString(item.getKyufukanriTen()));
            newItem.setKaisu(item.getKaisu());
            newItem.setTotalF(item.getTotalF());
            newItem.setHiyouSougaku(item.getHiyouSougaku());

            dwBeppyoList.add(newItem);
        }

        // 7. SQLを発行し、利用票別表：公費集計欄(dw_kouhi)を取得する
        KghCmnBeppyo3KohiByCriteriaInEntity KghCmnBeppyo3KohiByCriteriaInEntity = new KghCmnBeppyo3KohiByCriteriaInEntity();
        // 支援事業者ID
        KghCmnBeppyo3KohiByCriteriaInEntity.setAlShienId(CommonConstants.DEFAULT_SV_JIGYO_ID);
        // 利用者ID
        KghCmnBeppyo3KohiByCriteriaInEntity.setAlUserid(CommonConstants.DEFAULT_USER_ID);
        // サービス提供年月
        KghCmnBeppyo3KohiByCriteriaInEntity.setAsYymmYm(ls_ym);
        // サービス提供年月（変更日）
        KghCmnBeppyo3KohiByCriteriaInEntity.setAsYymmD(ls_dd);
        // 戻り値. 利用票別表：公費集計欄(dw_kouhi)= 上記取得結果
        List<KghCmnBeppyo3KohiOutEntity> dwKouhiList = cmnTucPlanKouhiFutanSelectMapper
                .findKghCmnBeppyo3KohiByCriteria(KghCmnBeppyo3KohiByCriteriaInEntity);

        // 8. SQLを発行し、利用票別表：社福軽減集計欄(dw_keigen)を取得する
        KghCmnBeppyo3SyafukuByCriteriaInEntity kghCmnBeppyo3SyafukuByCriteriaInEntity = new KghCmnBeppyo3SyafukuByCriteriaInEntity();
        // 支援事業者ID
        kghCmnBeppyo3SyafukuByCriteriaInEntity.setAlShienId(CommonConstants.DEFAULT_SV_JIGYO_ID);
        // 利用者ID
        kghCmnBeppyo3SyafukuByCriteriaInEntity.setAlUserid(CommonConstants.DEFAULT_USER_ID);
        // サービス提供年月
        kghCmnBeppyo3SyafukuByCriteriaInEntity.setAsYymmYm(ls_ym);
        // サービス提供年月（変更日）
        kghCmnBeppyo3SyafukuByCriteriaInEntity.setAsYymmD(ls_dd);
        // 戻り値.利用票別表：社福軽減集計欄(dw_keigen) = 上記取得結果
        List<KghCmnBeppyo3SyafukuOutEntity> dwKeigenList = cmnTucPlanSyafukuKeigenSelectMapper
                .findKghCmnBeppyo3SyafukuByCriteria(kghCmnBeppyo3SyafukuByCriteriaInEntity);

        // 9. SQLを発行し、利用票別表：種類別限度管理内訳欄(dw_shurui)を取得する
        KghCmnBeppyo2SyuruiSougouByCriteriaInEntity kghCmnBeppyo2SyuruiSougouByCriteriaInEntity = new KghCmnBeppyo2SyuruiSougouByCriteriaInEntity();
        // 支援事業者ID
        kghCmnBeppyo2SyuruiSougouByCriteriaInEntity.setAlShi(CommonConstants.DEFAULT_SV_JIGYO_ID);
        // 利用者ID
        kghCmnBeppyo2SyuruiSougouByCriteriaInEntity.setAlUsr(CommonConstants.DEFAULT_USER_ID);
        // サービス提供年月
        kghCmnBeppyo2SyuruiSougouByCriteriaInEntity.setAsYm(ls_ym);
        // サービス提供年月（変更日）
        kghCmnBeppyo2SyuruiSougouByCriteriaInEntity.setAsDd(ls_dd);
        // 戻り値.利用票別表：種類別限度管理内訳欄(dw_shurui) = 上記取得結果
        List<KghCmnBeppyo2SyuruiSougouOutEntity> dwShuruiList = cmnTucPlanBetu2SelectMapper
                .findKghCmnBeppyo2SyuruiSougouByCriteria(kghCmnBeppyo2SyuruiSougouByCriteriaInEntity);

        // 10. SQLを発行し、短期入所連続利用30日超過情報(ids_30ov_day)を取得する。
        KghCmnPlanOv30ByCriteriaInEntity kghCmnPlanOv30ByCriteriaInEntity = new KghCmnPlanOv30ByCriteriaInEntity();
        // 支援事業者ID
        kghCmnPlanOv30ByCriteriaInEntity.setAlShi(CommonConstants.DEFAULT_SV_JIGYO_ID);
        // 利用者ID
        kghCmnPlanOv30ByCriteriaInEntity.setAlUsr(CommonConstants.DEFAULT_USER_ID);
        // サービス提供年月
        kghCmnPlanOv30ByCriteriaInEntity.setAsYm(ls_ym);
        // サービス提供年月（変更日）
        kghCmnPlanOv30ByCriteriaInEntity.setAsYmd(ls_dd);
        // 戻り値.短期入所連続利用30日超過情報(ids_30ov_day) = 上記取得結果
        List<KghCmnPlanOv30OutEntity> ids30ovDayList = cmnTucPlanOv30SelectMapper
                .findKghCmnPlanOv30ByCriteria(kghCmnPlanOv30ByCriteriaInEntity);

        // 11. SQLを発行し、利用票 算定確認(ids_kakunin)を取得する
        KghCmnTucPlanRiyouKakuninByCriteriaInEntity kghCmnTucPlanRiyouKakuninByCriteriaInEntity = new KghCmnTucPlanRiyouKakuninByCriteriaInEntity();
        // 支援事業者ID
        kghCmnTucPlanRiyouKakuninByCriteriaInEntity.setShienId(CommonConstants.DEFAULT_SV_JIGYO_ID);
        // 利用者ID
        kghCmnTucPlanRiyouKakuninByCriteriaInEntity.setUserid(CommonConstants.DEFAULT_USER_ID);
        // サービス提供年月
        kghCmnTucPlanRiyouKakuninByCriteriaInEntity.setTeiYm(ls_ym);
        // サービス提供年月（変更日）
        kghCmnTucPlanRiyouKakuninByCriteriaInEntity.setTeiYmd(ls_dd);
        // 戻り値.利用票 算定確認(ids_kakunin) = 上記取得結果
        List<KghCmnTucPlanRiyouKakuninOutEntity> idsKakuninList = cmnTucPlanRiyouKakuninSelectMapper
                .findKghCmnTucPlanRiyouKakuninByCriteria(kghCmnTucPlanRiyouKakuninByCriteriaInEntity);

        // 12. SQLを発行し、短期入所利用日保持( ids_trd)を取得する
        KghCmnTucSvplanShortByCriteriaInEntity kghCmnTucSvplanShortByCriteriaInEntity = new KghCmnTucSvplanShortByCriteriaInEntity();
        // 支援事業者ID
        kghCmnTucSvplanShortByCriteriaInEntity.setAlShien(CommonConstants.DEFAULT_SV_JIGYO_ID);
        // 利用者ID
        kghCmnTucSvplanShortByCriteriaInEntity.setAlUser(CommonConstants.DEFAULT_USER_ID);
        // サービス提供年月
        kghCmnTucSvplanShortByCriteriaInEntity.setAsYymm(ls_ym);
        // サービス提供年月（変更日）
        kghCmnTucSvplanShortByCriteriaInEntity.setAsYmd(ls_dd);
        // 戻り値.短期入所利用日保持( ids_trd) = 上記取得結果
        List<KghCmnTucSvplanShortOutEntity> idsTrdList = cmnTucSvPlanShortSelectMapper
                .findKghCmnTucSvplanShortByCriteria(kghCmnTucSvplanShortByCriteriaInEntity);

        // 13. SQLを発行し、利用票別表提供事業所毎小計情報(ids_point)を取得する
        KghCmnTucSvPointByCriteriaInEntity kghCmnTucSvPointByCriteriaInEntity = new KghCmnTucSvPointByCriteriaInEntity();
        // 利用者ID
        kghCmnTucSvPointByCriteriaInEntity.setUserid(CommonConstants.DEFAULT_USER_ID);
        // 処理年月
        kghCmnTucSvPointByCriteriaInEntity.setYymmYm(ls_hh);
        // 戻り値.利用票別表提供事業所毎小計情報(ids_point) = 上記取得結果
        List<KghCmnTucSvPointOutEntity> idsPointList = cmnTucServicePointSelectMapper
                .findKghCmnTucSvPointByCriteria(kghCmnTucSvPointByCriteriaInEntity);

        // 14. SQLを発行し、利用票：日数.単位数.金額の計算結果欄(dw_calcs)を取得する
        KghCmnRiyou5SumByCriteriaInEntity kghCmnRiyou5SumByCriteriaInEntityNo14 = new KghCmnRiyou5SumByCriteriaInEntity();
        // 戻り値. 利用票：日数.単位数.金額の計算結果欄(dw_calcs)= 上記取得結果
        List<KghCmnRiyou5SumOutEntity> dwCalcsList = riyouSumSelectMapper
                .findKghCmnRiyou5SumByCriteria(kghCmnRiyou5SumByCriteriaInEntityNo14);

        // 15. 戻り値を呼出し元に返す
        Gui01042SerachResult result = new Gui01042SerachResult();
        result.setDwUsrinfList(dwUsrinfList);
        result.setDwMasterList(dwMasterList);
        result.setIdsPlanList(idsPlanList);
        result.setDwGendoList(dwGendoList);
        result.setDwBeppyoList(dwBeppyoList);
        result.setDwKouhiList(dwKouhiList);
        result.setDwKeigenList(dwKeigenList);
        result.setDwShuruiList(dwShuruiList);
        result.setIds30ovDayList(ids30ovDayList);
        result.setIdsKakuninList(idsKakuninList);
        result.setIdsTrdList(idsTrdList);
        result.setIdsPointList(idsPointList);
        result.setDwCalcsList(dwCalcsList);

        return result;
    }

    /**
     * 6.2. 台帳の保険情報を取得する
     * 
     * @param inDto        概算金額情報取得 入力Dto
     * @param riyouProcess 【変数】.利用票画面処理用構造体
     */
    private void getCmnUsrInfo3gOutDto(WeekPlanEstimateDisplaySelectServiceInDto inDto,
            Gui01149RiyouProcess riyouProcess) {
        // 6.2.1. 当月の利用者情報取得関数を呼び出す。
        GetUserInfoMonthOutDto userInfo = kghCmn03gFunc01Logic.getUserinfoMonth(
                CommonDtoUtil.strValToInt(inDto.getUserId()), inDto.getShoriYymm(),
                CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        KghCmnUsrInfo3gDto cmnUsrInfo3gOutDto = userInfo.getUsrInfo();
        // 6.2.2. 上記取得結果の戻り値>0場合、
        if (cmnUsrInfo3gOutDto != null && cmnUsrInfo3gOutDto.getHokenMax() != null
                && cmnUsrInfo3gOutDto.getHokenMax() > 0) {
            // 以下処理を実行する。
            hasCmnUsrInfo3gProc(cmnUsrInfo3gOutDto, riyouProcess);
        }
        // 6.2.3. 上記以外の場合
        else {
            // 以下処理を実行する。
            notHasCmnUsrInfo3gProc(inDto, riyouProcess);
        }
        // 6.2.4. 利用票履歴情報を取得する
        getPlanRireki(riyouProcess);
        // 6.2.5. 保険情報を更新する
        hokenInfoUpd(inDto, riyouProcess);
    }

    /**
     * 6.2.5. 保険情報を更新する
     * 
     * @param inDto        概算金額情報取得 入力Dto
     * @param riyouProcess 【変数】.利用票画面処理用構造体
     */
    private void hokenInfoUpd(WeekPlanEstimateDisplaySelectServiceInDto inDto, Gui01149RiyouProcess riyouProcess) {
        // 6.2.5.1. 変数を宣言する。
        // 【変数】.認定期間（開始）＝ ""
        String ninteiStYmd = StringUtils.EMPTY;
        // 【変数】.認定期間（終了）＝ ""
        String ninteiEdYmd = StringUtils.EMPTY;
        // 【変数】.要介護度変更 = False
        boolean yokaiChFlg = false;
        // 【変数】.サービス種別 = ""
        String svtype = StringUtils.EMPTY;
        // 【変数】.要介護度比較結果 = 0
        Integer yokaiComResult = 0;
        // 【変数】.要介護度 = 0
        Integer yokai = 0;
        // 6.2.5.2. 利用者の該当の保険情報.認定情報を取得する。
        Gui01149Hoken userHokenInfo = getUserHokenInfo(riyouProcess, inDto);
        // 6.2.5.3.利用者保険情報を設定する。
        // 6.2.5.3.1.【変数】.利用者の変更保険情報がある場合、
        if (userHokenInfo != null) {
            // 6.2.5.3.1.1.【変数】.利用票画面処理用構造体の値を設定する
            // 【変数】.利用票画面処理用構造体.保険者番号＝【変数】.利用者の変更保険情報.保険者番号
            riyouProcess.setKHokenNo(userHokenInfo.getKHokenNo());
            // 【変数】.利用票画面処理用構造体.保険者名＝【変数】.利用者の変更保険情報.保険者名
            riyouProcess.setKHokenKnj(userHokenInfo.getKHokenKnj());
            // 【変数】.利用票画面処理用構造体.被保険者番号＝【変数】.利用者の変更保険情報.被保険者番号
            riyouProcess.setHHokenNo(userHokenInfo.getHHokenNo());
            // 【変数】.利用票画面処理用構造体.保険者CD＝【変数】.利用者の変更保険情報.保険者CD
            riyouProcess.setKHokenCd(userHokenInfo.getKHokenCd());
            // 【変数】.利用票画面処理用構造体.保険データID＝【変数】.利用者の変更保険情報.保険データ連番
            riyouProcess.setSeqNo(userHokenInfo.getSeqNo());
            // 【変数】.利用票画面処理用構造体.要介護度（変更前）＝【変数】.利用者の変更保険情報.要介護度（変更前）
            riyouProcess.setYokaiKbn(userHokenInfo.getYokaiKbn());
            // 6.2.5.3.1.2.区分変更の有無判定を実行する。
            // ①利用者の変更保険情報.要介護度（変更後） <> 0 AND 利用者の変更保険情報.認定開始日（変更後） <> "" の場合、
            if (!CommonConstants.STR_ZERO.equals(userHokenInfo.getYokaiKbnN())
                    && !StringUtils.EMPTY.equals(userHokenInfo.getNinteiStYmdN())) {
                // 【変数】.利用票画面処理用構造体.要介護度（変更後）＝【変数】.利用者の変更保険情報.要介護度（変更後）
                riyouProcess.setYokaiKbnN(userHokenInfo.getYokaiKbnN());
                // 【変数】.利用票画面処理用構造体.認定変更日＝【変数】.利用者の変更保険情報.認定開始日（変更後）
                riyouProcess.setHenkouYmd(userHokenInfo.getNinteiStYmdN());
                // 【変数】.要介護度変更 = True
                yokaiChFlg = true;
            } else {
                // ②上記以外の場合、
                // 【変数】.利用票画面処理用構造体.要介護度（変更後）＝0
                riyouProcess.setYokaiKbnN(CommonConstants.STR_ZERO);
                // 【変数】.利用票画面処理用構造体.認定変更日＝""
                riyouProcess.setHenkouYmd(StringUtils.EMPTY);
            }
            // 6.2.5.3.1.3.要介護度を比較する。
            Integer diffYokaiResult = kghCmpF01Logic.diffYokaiKbn(
                    CommonDtoUtil.strValToInt(userHokenInfo.getYokaiKbn()),
                    CommonDtoUtil.strValToInt(userHokenInfo.getYokaiKbnN()));
            // 【変数】.要介護度比較結果＝上記関数の戻り値
            yokaiComResult = diffYokaiResult;
            // ①【変数】.要介護度比較結果 >= 0 AND 【変数】.要介護度変更 = True の場合、
            if (yokaiComResult >= 0 && yokaiChFlg) {
                // 【変数】.利用票画面処理用構造体.限度額＝【変数】.利用者の変更保険情報.限度額（変更後）
                riyouProcess.setTusho1GendoN(userHokenInfo.getTusho1GendoN());
                // 【変数】.認定期間（開始）＝ 【変数】.利用者の変更保険情報.限度額有効期間開始日（変更後）
                ninteiStYmd = userHokenInfo.getGendoStYmdN();
                // 【変数】.認定期間（終了）＝ 【変数】.利用者の変更保険情報.限度額有効期間終了日（変更後）
                ninteiEdYmd = userHokenInfo.getGendoEdYmdN();
                // 【変数】.利用票画面処理用構造体.枝番＝【変数】.利用者の変更保険情報.認定データ枝番（変更後）
                riyouProcess.setEdaNo(userHokenInfo.getEdaNoN());
                // 【変数】.要介護度＝【変数】.利用者の変更保険情報.要介護度（変更後）
                yokai = CommonDtoUtil.strValToInt(userHokenInfo.getYokaiKbnN());
            } else {
                // ②上記以外の場合、
                // 【変数】.利用票画面処理用構造体.限度額＝【変数】.利用者の変更保険情報.支給限度額（変更前）
                riyouProcess.setTusho1GendoN(userHokenInfo.getTusho1Gendo());
                // 【変数】.認定期間（開始）＝ 【変数】.利用者の変更保険情報.限度額有効期間開始日（変更前）
                ninteiStYmd = userHokenInfo.getGendoStYmd();
                // 【変数】.認定期間（終了）＝ 【変数】.利用者の変更保険情報.限度額有効期間終了日（変更前）
                ninteiEdYmd = userHokenInfo.getGendoEdYmd();
                // 【変数】.利用票画面処理用構造体.枝番＝【変数】.利用者の変更保険情報.認定データ枝番（変更前）
                riyouProcess.setEdaNo(userHokenInfo.getEdaNo());
                // 【変数】.要介護度＝【変数】.利用者の変更保険情報.要介護度（変更前）
                yokai = CommonDtoUtil.strValToInt(userHokenInfo.getYokaiKbn());
            }
            // 6.2.5.3.1.4.認定日を設定する
            // ①【変数】.要介護度変更 = True の場合、
            if (yokaiChFlg) {
                // 【変数】.利用票画面処理用構造体.認定開始年月日＝【変数】.利用者の変更保険情報.認定開始日（変更後）
                riyouProcess.setNKikanFr(userHokenInfo.getNinteiStYmdN());
                // 【変数】.利用票画面処理用構造体.認定終了年月日＝【変数】.利用者の変更保険情報.認定終了日（変更後）
                riyouProcess.setNKikanTo(userHokenInfo.getNinteiEdYmdN());
            } else {
                // ②上記以外の場合、
                // 【変数】.利用票画面処理用構造体.認定開始年月日＝【変数】.利用者の変更保険情報.認定開始日（変更前）
                riyouProcess.setNKikanFr(userHokenInfo.getNinteiStYmd());
                // 【変数】.利用票画面処理用構造体.認定終了年月日＝【変数】.利用者の変更保険情報.認定終了日（変更前）
                riyouProcess.setNKikanTo(userHokenInfo.getNinteiEdYmd());
            }
            // 6.2.5.3.1.5.その他の値を設定する
            // 【変数】.利用票画面処理用構造体.限度額適用期間（開始）＝【変数】.認定期間（開始）
            riyouProcess.setKikanFr(ninteiStYmd);
            // 【変数】.利用票画面処理用構造体.限度額適用期間（終了）＝【変数】.認定期間（終了）
            riyouProcess.setKikanTo(ninteiEdYmd);
            // 【変数】.利用票画面処理用構造体.担当者＝【変数】.利用者の変更保険情報.担当ケアマネージャ名
            riyouProcess.setTanto(userHokenInfo.getTanto());
            // 【変数】.利用票画面処理用構造体.担当者ID＝【変数】.利用者の変更保険情報.担当ケアマネージャID
            riyouProcess.setTantoId(userHokenInfo.getTantoId());
            // 【変数】.利用票画面処理用構造体.給付率＝【変数】.利用者の変更保険情報.給付率
            riyouProcess.setFutanRate(userHokenInfo.getKyufuRitu());
        } else {
            // 6.2.5.3.2.【変数】.利用者の変更保険情報がある場合、
            // 【変数】.利用票画面処理用構造体.保険者番号＝ ""
            riyouProcess.setKHokenNo(StringUtils.EMPTY);
            // 【変数】.利用票画面処理用構造体.保険者名＝""
            riyouProcess.setKHokenKnj(StringUtils.EMPTY);
            // 【変数】.利用票画面処理用構造体.被保険者番号＝""
            riyouProcess.setHHokenNo(StringUtils.EMPTY);
            // 【変数】.利用票画面処理用構造体.保険者CD＝0
            riyouProcess.setKHokenCd(CommonConstants.STR_ZERO);
            // 【変数】.利用票画面処理用構造体.保険データID＝0
            riyouProcess.setSeqNo(CommonConstants.STR_ZERO);
            // 【変数】.利用票画面処理用構造体.要介護度（変更前）＝0
            riyouProcess.setYokaiKbn(CommonConstants.STR_ZERO);
            // 【変数】.利用票画面処理用構造体.要介護度（変更後）＝0
            riyouProcess.setYokaiKbnN(CommonConstants.STR_ZERO);
            // 【変数】.利用票画面処理用構造体.認定変更日＝""
            riyouProcess.setHenkouYmd(StringUtils.EMPTY);
            // 【変数】.利用票画面処理用構造体.限度額＝0
            riyouProcess.setTusho1GendoN(CommonConstants.STR_ZERO);
            // 【変数】.利用票画面処理用構造体.枝番＝0
            riyouProcess.setEdaNo(CommonConstants.STR_ZERO);
            // 【変数】.利用票画面処理用構造体.認定開始年月日＝""
            riyouProcess.setNKikanFr(StringUtils.EMPTY);
            // 【変数】.利用票画面処理用構造体.認定終了年月日＝""
            riyouProcess.setNKikanTo(StringUtils.EMPTY);
            // 【変数】.利用票画面処理用構造体.限度額適用期間（開始）＝""
            riyouProcess.setKikanFr(StringUtils.EMPTY);
            // 【変数】.利用票画面処理用構造体.限度額適用期間（終了）＝""
            riyouProcess.setKikanTo(StringUtils.EMPTY);
        }
        // 6.2.5.3.3.自事業所が特定施設かどうか判定処理を実行する。
        String svTypeResult = kghCmnF01Logic.getSvType(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 【変数】.サービス種別 = 上記関数の戻り値（サービス種類コード）
        svtype = svTypeResult;
        // 6.2.5.3.3.1.(【変数】.サービス種別 = "33" OR 【変数】.サービス種別 = "35" OR 【変数】.サービス種別 = "36")
        // AND 【変数】.要介護度 > 0 の場合、
        if ((CommonConstants.NUM_STR_33.equals(svtype) || CommonConstants.NUM_STR_35.equals(svtype)
                || CommonConstants.NUM_STR_36.equals(svtype)) && yokai > 0) {
            // 特定施設入居者生活介護（外部サービス利用型）としての限度額を取得する
            Integer gendoKaku = kghCmnF01Logic.getGaibuGendo(yokai, CommonConstants.EMPTY_STRING);
            // 【変数】.利用票画面処理用構造体.限度額＝上記関数の戻り値（限度額）
            riyouProcess.setTusho1GendoN(CommonDtoUtil.objValToString(gendoKaku));
        }
        // 6.2.5.3.4.利用不可能な日を取得する。
        // 6.2.5.3.4.1.変数を宣言する。
        List<String> protectDayIndex = new ArrayList<>();
        // ※【変数】.利用票画面処理用構造体.利用不可能な日の配列[0～30] = 「0」
        for (int i = 0; i < 31; i++) {
            protectDayIndex.add(CommonConstants.STR_ZERO);
        }
        // 月初日から月末日まで、【変数】.利用票画面処理用構造体.利用不可能な日の配列で、値を「0」に設定する。
        riyouProcess.setProtectDay(protectDayIndex);
        // 【変数】.月末日 = リクエストパラメータ.提供年月の月末日
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, CommonDtoUtil.strValToInt(inDto.getShoriYymm().substring(0, 4)));
        cal.set(Calendar.MONTH, CommonDtoUtil.strValToInt(inDto.getShoriYymm().substring(5, 7)) - 1);
        Integer lastDay = cal.getActualMaximum(Calendar.DATE);
        // 6.2.5.3.4.2.利用不可能な開始日と利用不可能な終了日を取得する。
        for (int i = 0; i < CollectionUtils.size(riyouProcess.getHokenInfo()); i++) {
            // 該当行（保険情報）
            Gui01149Hoken thisRowHoken = riyouProcess.getHokenInfo().get(i);
            // 該当行（保険情報）は以下条件が満たす場合、下記処理を実行する。
            // ・条件：該当行（介護保険情報）.保険者番号＝【変数】.利用票画面処理用構造体.保険者番号
            // AND 該当行（介護保険情報）.被保険者番号＝【変数】.利用票画面処理用構造体.被保険者番号
            if (thisRowHoken.getKHokenNo().equals(riyouProcess.getKHokenNo())
                    && thisRowHoken.getHHokenNo().equals(riyouProcess.getHHokenNo())) {
                // ① 利用不可能な開始日を該当行（介護保険情報）.変更日に設定する。
                // 【変数】.利用票画面処理用構造体.利用不可能な開始日＝該当行（介護保険情報）.変更日
                riyouProcess.setStartDay(thisRowHoken.getHenkouDay());
                // ② 後に行がある場合、（該当行（介護保険情報）.インデックス ＜ 【変数】.利用票画面処理用構造体.介護保険情報の件数-1）、
                if (i < CollectionUtils.size(riyouProcess.getHokenInfo()) - 1) {
                    // 利用不可能な終了日を次の行の変更日の前日に設定する。
                    Gui01149Hoken NextRowHoken = riyouProcess.getHokenInfo().get(i + 1);
                    // 【変数】.利用票画面処理用構造体.利用不可能な終了日＝次の行（介護保険情報）.変更日-1
                    riyouProcess.setEndDay(
                            CommonDtoUtil.objValToString(CommonDtoUtil.strValToInt(NextRowHoken.getHenkouDay()) - 1));
                }
                // ③ 後に行がない場合、（該当行（介護保険情報）.インデックス ＝ 【変数】.利用票画面処理用構造体.介護保険情報の件数-1）、
                if (i == CollectionUtils.size(riyouProcess.getHokenInfo()) - 1) {
                    // 【変数】.月末日を利用不可能な終了日とする
                    // 【変数】.利用票画面処理用構造体.利用不可能な終了日＝【変数】.月末日
                    riyouProcess.setEndDay(CommonDtoUtil.objValToString(lastDay));
                }
                // ④ 【変数】.利用票画面処理用構造体.利用不可能な開始日から【変数】.利用票画面処理用構造体.利用不可能な終了日まで、
                List<String> protectDay = new ArrayList<>();
                for (int day = 1; day <= 31; day++) {
                    if (CommonDtoUtil.strValToInt(riyouProcess.getStartDay()) <= day
                            && CommonDtoUtil.strValToInt(riyouProcess.getEndDay()) >= day) {
                        protectDay.add(CommonConstants.STR_1);
                    } else {
                        protectDay.add(CommonConstants.STR_ZERO);
                    }
                }
                riyouProcess.setProtectDay(protectDay);
            }
        }
    }

    /**
     * 6.2.5.2.利用者の該当の保険情報・認定情報を取得する。
     * 
     * @param riyouProcess 利用票画面処理用構造体
     * @return 【変数】.利用者の変更保険情報
     */
    private Gui01149Hoken getUserHokenInfo(Gui01149RiyouProcess riyouProcess,
            WeekPlanEstimateDisplaySelectServiceInDto inDto) {
        if (CollectionUtils.isEmpty(riyouProcess.getHokenInfo())) {
            return null;
        }
        // 6.2.5.2.1.
        // 変数.利用票画面処理用構造体.介護保険情報に、「変更日＝レスポンスパラメータ.提供月（日）（2桁）」条件が満たすレコードを変数.利用者の変更保険情報とする。
        List<Gui01149Hoken> userHokenInfoList = riyouProcess.getHokenInfo().stream()
                .filter(x -> x.getHenkouDay().compareTo(inDto.getShoriYymm() + CommonConstants.STRING_FIRST_DAY) == 0)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(userHokenInfoList)) {
            return userHokenInfoList.getFirst();
        } else {
            // 6.2.5.2.2. 変数.利用者の変更保険情報がない場合、変数.利用票画面処理用構造体.介護保険情報に1件目を変数.利用者の変更保険情報とする。
            return riyouProcess.getHokenInfo().getFirst();
        }
    }

    /**
     * 6.2.4.利用票履歴情報を取得する
     * 
     * @param riyouProcess 【変数】.利用票画面処理用構造体
     */
    private void getPlanRireki(Gui01149RiyouProcess riyouProcess) {
        // 6.2.4.1.【変数】.履歴情報リスト初期化
        List<Gui01149PlanRireki> planRirekiList = new ArrayList<>();
        // 保険者番号と被保険者番号のkeyですリスト
        List<String> hokenNoKeyList = new ArrayList<>();
        // 1から連番する
        Integer seqNo = 1;
        List<Gui01149Hoken> hokenInfoList = riyouProcess.getHokenInfo();
        if (hokenInfoList != null) {
            // 6.2.4.2.【変数】.利用票画面処理用構造体.介護保険情報をループする
            for (Gui01149Hoken hoken : hokenInfoList) {
                // // 保険者番号と被保険者番号のkeyです
                String hokenNoKey = hoken.getKHokenNo() + CommonConstants.STR_POUND_KEY + hoken.getHHokenNo();
                // ・【変数】.履歴情報リスト.保険者番号 =【変数】.利用票画面処理用構造体.介護保険情報.保険者番号 AND
                // 【変数】.履歴情報リスト.被保険者番号 = 【変数】.利用票画面処理用構造体.介護保険情報.被保険者番号のデータがなし場合
                if (!hokenNoKeyList.contains(hokenNoKey)) {
                    // 【変数】.履歴情報リストを追加情報する。
                    Gui01149PlanRireki planRireki = new Gui01149PlanRireki();
                    // 【変数】.履歴情報リスト.連番 = 1から連番する
                    planRireki.setSeqNo(CommonDtoUtil.objValToString(seqNo));
                    // 【変数】.履歴情報リスト.保険者番号 = 【変数】.利用票画面処理用構造体.介護保険情報.保険者番号
                    planRireki.setKHokenNo(hoken.getKHokenNo());
                    // 【変数】.履歴情報リスト.保険者名 =【変数】.利用票画面処理用構造体.介護保険情報.保険者名
                    planRireki.setKHokenKnj(hoken.getKHokenKnj());
                    // 【変数】.履歴情報リスト.被保険者番号 =【変数】.利用票画面処理用構造体.介護保険情報.被保険者番号
                    planRireki.setHHokenNo(hoken.getHHokenNo());
                    // 【変数】.履歴情報リスト.変更日 =【変数】.利用票画面処理用構造体.介護保険情報.変更日
                    planRireki.setTeiYmD(hoken.getHenkouDay());
                    // 【変数】.履歴情報リストを追加情報する。
                    planRirekiList.add(planRireki);
                    // 1から連番する + 1
                    seqNo++;
                    // 保険者番号と被保険者番号のkeyですリスト追加
                    hokenNoKeyList.add(hokenNoKey);
                }
            }
        }

        // 利用票履歴情報
        riyouProcess.setPlanRireki(planRirekiList);
    }

    /**
     * 6.2.3.上記以外の場合、以下処理を実行する。
     * 
     * @param inDto        概算金額情報取得 入力Dto
     * @param riyouProcess 【変数】.利用票画面処理用構造体
     */
    private void notHasCmnUsrInfo3gProc(WeekPlanEstimateDisplaySelectServiceInDto inDto,
            Gui01149RiyouProcess riyouProcess) {
        // 6.2.3.上記以外の場合、以下処理を実行する。
        // リクエストパラメータ
        RiyoshaByCriteriaInEntity riyoshaByCriteriaInEntity = new RiyoshaByCriteriaInEntity();
        // 利用者ID
        riyoshaByCriteriaInEntity.setIlUserid(inDto.getUserId());
        // 6.2.3.1.利用者情報を取得する
        List<RiyoshaOutEntity> riyoshaOutList = comTucUserSelectMapper.findRiyoshaByCriteria(riyoshaByCriteriaInEntity);
        if (!CollectionUtils.isEmpty(riyoshaOutList)) {
            // 上記取得結果の利用者情報
            RiyoshaOutEntity riyoshaOut = riyoshaOutList.getFirst();
            // 【変数】.利用票画面処理用構造体.利用者氏名 = 上記取得結果の利用者情報.氏名（姓） + ' ' + 上記取得結果の利用者情報.氏名（名）
            riyouProcess.setUsername(riyoshaOut.getName1Knj() + StringUtils.SPACE + riyoshaOut.getName2Knj());
            // 【変数】.利用票画面処理用構造体.利用者生年月日 = 上記取得結果の利用者情報.生年月日
            riyouProcess.setBirthday(riyoshaOut.getBirthdayYmd());
            // 【変数】.利用票画面処理用構造体.利用者性別 = 上記取得結果の利用者情報.性別
            riyouProcess.setSex(CommonDtoUtil.objValToString(riyoshaOut.getSex()));
            // 【変数】.利用票画面処理用構造体.給付率 = 90
            riyouProcess.setFutanRate(CommonConstants.FUTAN_RATE_90);
            // 保険開始日リス
            List<String> hokens = new ArrayList<>();
            hokens.add(CommonConstants.MONTHSTART);
            // 【変数】.保険開始日リスの1件目 = "01"
            riyouProcess.setHokens(hokens);
        }
        // 6.2.3.2.介護保険 負担割合証・給付制限 給付率を取得する
        // 利用者ID配列
        List<Integer> alUserid = new ArrayList<>();
        alUserid.add(CommonDtoUtil.strValToInt(inDto.getUserId()));
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, CommonDtoUtil.strValToInt(inDto.getShoriYymm().substring(0, 4)));
        cal.set(Calendar.MONTH, CommonDtoUtil.strValToInt(inDto.getShoriYymm().substring(5, 7)) - 1);
        // 検索開始日
        String startDay = inDto.getShoriYymm() + CommonConstants.STRING_FIRST_DAY;
        // 検索終了日
        Integer lastDay = cal.getActualMaximum(Calendar.DATE);
        String endDay = inDto.getShoriYymm() + CommonConstants.STR_DELIMITER + CommonDtoUtil.objValToString(lastDay);
        // 介護保険 負担割合証・給付制限 給付率取得処理
        List<KyuufurituCmnOutDto> kyuufurituCmnOutList = kghShrKsgFunc02Logic.getKyuufurituCmn(alUserid, startDay,
                endDay);
        // 上記取得結果の給付割合 <> nullのデータの中、【変数】.利用票画面処理用構造体.給付率 = MAX(給付割合)
        if (!CollectionUtils.isEmpty(kyuufurituCmnOutList)) {
            // MAX(給付割合) リストから最大の給付割合ランクを取得する（null値の処理を含む）
            Integer maxValue = kyuufurituCmnOutList.stream()
                    // nullのDtoオブジェクトを除外
                    .filter(Objects::nonNull)
                    // 給付割合ランクを取得（nullを返す可能性がある）
                    .map(dto -> dto.getKyufuwariRank())
                    // nullのランク値を除外
                    .filter(Objects::nonNull)
                    // Integerをintに変換
                    .mapToInt(Integer::intValue)
                    // 最大値を取得。ストリームが空の場合はInteger.MIN_VALUEを返す
                    .max().orElse(Integer.MIN_VALUE);

            // 【変数】.利用票画面処理用構造体.給付率 = MAX(給付割合)
            riyouProcess.setFutanRate(CommonDtoUtil.objValToString(maxValue));
        }
    }

    /**
     * 6.2.2.上記取得結果の戻り値>0場合、以下処理を実行する。
     * 
     * @param cmnUsrInfo3gOutDto 当月の利用者情報
     * @param riyouProcess       【変数】.利用票画面処理用構造体
     */
    private void hasCmnUsrInfo3gProc(KghCmnUsrInfo3gDto cmnUsrInfo3gOutDto, Gui01149RiyouProcess riyouProcess) {
        // 6.2.2.1.上記取得結果の利用者情報を【変数】に格納する
        // 【変数】.利用票画面処理用構造体.利用者氏名 = 上記取得結果の情報格納構造体.利用者名
        riyouProcess.setUsername(cmnUsrInfo3gOutDto.getUsernameKnj());
        // 【変数】.利用票画面処理用構造体.利用者生年月日 = 上記取得結果の情報格納構造体.利用者生年月日
        riyouProcess.setBirthday(cmnUsrInfo3gOutDto.getBirthdayYmd());
        // 【変数】.利用票画面処理用構造体.利用者性別 = 上記取得結果の情報格納構造体.利用者性別
        riyouProcess.setSex(CommonDtoUtil.objValToString(cmnUsrInfo3gOutDto.getSex()));
        // 【変数】.利用票画面処理用構造体.担当ケアマネージャID = 上記取得結果の情報格納構造体.担当ケアマネージャID
        riyouProcess.setTantoId(CommonDtoUtil.objValToString(cmnUsrInfo3gOutDto.getTantoId()));
        // 【変数】.利用票画面処理用構造体.担当ケアマネージャ名 = 上記取得結果の情報格納構造体.担当ケアマネージャ名
        riyouProcess.setTanto(cmnUsrInfo3gOutDto.getTantoNameKnj());
        // 情報格納構造体の最大件目の介護保険情報.有効情報開始日
        String hHenkouYmd = CommonDtoUtil
                .strNullToEmpty(cmnUsrInfo3gOutDto.getHoken().get(cmnUsrInfo3gOutDto.getHokenMax() - 1).getStartYmd());
        // 【変数】.利用票画面処理用構造体.保険者変更日 = 上記取得結果の情報格納構造体の最大件目の介護保険情報.有効情報開始日の末端2桁
        riyouProcess.setHHenkouYmd(hHenkouYmd.substring(hHenkouYmd.length() - 2, hHenkouYmd.length()));
        // 【変数】.利用票画面処理用構造体.給付率 = 上記取得結果の情報格納構造体の最大件目の介護保険情報.給付率
        riyouProcess.setFutanRate(CommonDtoUtil.objValToString(
                cmnUsrInfo3gOutDto.getHoken().get(cmnUsrInfo3gOutDto.getHokenMax() - 1).getKyufuwari()));
        // 介護保険情報
        List<Gui01149Hoken> hokenInfo = new ArrayList<>();
        // 6.2.2.2.上記取得結果の情報格納構造体.介護保険情報をループする
        for (int i = 0; i < CollectionUtils.size(cmnUsrInfo3gOutDto.getHoken()) - 1; i++) {
            CmnUsrHok3gOutDto cmnUsrHok3gOutDto = cmnUsrInfo3gOutDto.getHoken().get(i);
            // 介護保険情報
            Gui01149Hoken hoken = new Gui01149Hoken();
            // 【変数】.利用票画面処理用構造体.介護保険情報.保険データ連番 = 介護保険情報.介護保険連番
            hoken.setSeqNo(CommonDtoUtil.objValToString(cmnUsrHok3gOutDto.getSeqNo()));
            // 【変数】.利用票画面処理用構造体.介護保険情報.変更日 =
            // ・1件目の場合、
            if (i == 0) {
                // "01"をセット
                hoken.setHenkouDay(CommonConstants.MONTHSTART);
            } else {
                String startYmd = CommonDtoUtil.strNullToEmpty(cmnUsrHok3gOutDto.getStartYmd());
                // ・以外の場合、介護保険情報.有効情報開始日の末端2桁
                hoken.setHenkouDay(startYmd.substring(startYmd.length() - 2, startYmd.length()));
            }
            // 【変数】.利用票画面処理用構造体.介護保険情報.保険者CD = 介護保険情報.保険者CD
            hoken.setKHokenCd(CommonDtoUtil.objValToString(cmnUsrHok3gOutDto.getKHokenCd()));
            // 【変数】.利用票画面処理用構造体.介護保険情報.保険者番号 = 介護保険情報.保険者番号
            hoken.setKHokenNo(cmnUsrHok3gOutDto.getKHokenNo());
            // 【変数】.利用票画面処理用構造体.介護保険情報.保険者名 = 介護保険情報.保険者名
            hoken.setKHokenKnj(cmnUsrHok3gOutDto.getKHokenKnj());
            // 【変数】.利用票画面処理用構造体.介護保険情報.被保険者番号 = 介護保険情報.被保険者番号
            hoken.setHHokenNo(cmnUsrHok3gOutDto.getHiHokenNo());

            if (cmnUsrHok3gOutDto.getNinteiMax() > 0) {
                // 介護保険情報.1件目の認定情報
                CmnUsrNin3gOutDto firstNintei = cmnUsrHok3gOutDto.getNintei().getFirst();
                // 【変数】.利用票画面処理用構造体.介護保険情報.認定データ枝番（変更前） = 介護保険情報.1件目の認定情報.枝番
                hoken.setEdaNo(CommonDtoUtil.objValToString(firstNintei.getEdaNo()));
                // 【変数】.利用票画面処理用構造体.介護保険情報.要介護度（変更前） = 介護保険情報.1件目の認定情報.要介護度
                hoken.setYokaiKbn(CommonDtoUtil.objValToString(firstNintei.getYokaiKbn()));
                // 【変数】.利用票画面処理用構造体.介護保険情報.支給限度額（変更前） = 介護保険情報.1件目の認定情報.限度額
                hoken.setTusho1Gendo(CommonDtoUtil.objValToString(firstNintei.getGendo()));
                // 【変数】.利用票画面処理用構造体.介護保険情報.認定開始日（変更前） = 介護保険情報.1件目の認定情報.認定有効期間開始日
                hoken.setNinteiStYmd(firstNintei.getStartYmd());
                // 【変数】.利用票画面処理用構造体.介護保険情報.認定終了日（変更前） = 介護保険情報.1件目の認定情報.認定有効期間終了日
                hoken.setNinteiEdYmd(firstNintei.getEndYmd());
                // 【変数】.利用票画面処理用構造体.介護保険情報.限度額有効期間開始日（変更前） = 介護保険情報.1件目の認定情報.通所限度額適用開始日
                hoken.setGendoStYmd(firstNintei.getKbnStartYmd());
                // 【変数】.利用票画面処理用構造体.介護保険情報.限度額有効期間終了日（変更前） = 介護保険情報.1件目の認定情報.通所限度額適用終了日
                hoken.setGendoEdYmd(firstNintei.getKbn2EndYmd());

                // ・介護保険情報.認定情報最大数 > 1の場合
                if (cmnUsrHok3gOutDto.getNinteiMax() > 1) {
                    // 介護保険情報.MAX件目の認定情報
                    CmnUsrNin3gOutDto lastNintei = cmnUsrHok3gOutDto.getNintei()
                            .get(cmnUsrHok3gOutDto.getNinteiMax() - 1);
                    // 【変数】.利用票画面処理用構造体.介護保険情報.認定データ枝番（変更後） = 介護保険情報.MAX件目の認定情報.枝番
                    hoken.setEdaNo(CommonDtoUtil.objValToString(lastNintei.getEdaNo()));
                    // 【変数】.利用票画面処理用構造体.介護保険情報.要介護度（変更後） = 介護保険情報.MAX件目の認定情報.要介護度
                    hoken.setYokaiKbn(CommonDtoUtil.objValToString(lastNintei.getYokaiKbn()));
                    // 【変数】.利用票画面処理用構造体.介護保険情報.支給限度額（変更後） = 介護保険情報.MAX件目の認定情報.限度額
                    hoken.setTusho1Gendo(CommonDtoUtil.objValToString(lastNintei.getGendo()));
                    // 【変数】.利用票画面処理用構造体.介護保険情報.認定開始日（変更後） = 介護保険情報.MAX件目の認定情報.認定有効期間開始日
                    hoken.setNinteiStYmd(lastNintei.getStartYmd());
                    // 【変数】.利用票画面処理用構造体.介護保険情報.認定終了日（変更後） = 介護保険情報.MAX件目の認定情報.認定有効期間終了日
                    hoken.setNinteiEdYmd(lastNintei.getEndYmd());
                    // 【変数】.利用票画面処理用構造体.介護保険情報.限度額有効期間開始日（変更後） = 介護保険情報.MAX件目の認定情報.通所限度額適用開始日
                    hoken.setGendoStYmd(lastNintei.getKbnStartYmd());
                    // 【変数】.利用票画面処理用構造体.介護保険情報.限度額有効期間終了日（変更後） = 介護保険情報.MAX件目の認定情報.通所限度額適用終了日
                    hoken.setGendoEdYmd(lastNintei.getKbn2EndYmd());
                } else {
                    // ・介護保険情報.認定情報最大数 <= 1の場合
                    // 【変数】.利用票画面処理用構造体.介護保険情報.認定データ枝番（変更後） = 0
                    hoken.setEdaNo(CommonConstants.STR_ZERO);
                    // 【変数】.利用票画面処理用構造体.介護保険情報.要介護度（変更後） = 0
                    hoken.setYokaiKbn(CommonConstants.STR_ZERO);
                    // 【変数】.利用票画面処理用構造体.介護保険情報.支給限度額（変更後） = 0
                    hoken.setTusho1Gendo(CommonConstants.STR_ZERO);
                    // 【変数】.利用票画面処理用構造体.介護保険情報.認定開始日（変更後） = ""
                    hoken.setNinteiStYmd(StringUtils.EMPTY);
                    // 【変数】.利用票画面処理用構造体.介護保険情報.認定終了日（変更後） = ""
                    hoken.setNinteiEdYmd(StringUtils.EMPTY);
                    // 【変数】.利用票画面処理用構造体.介護保険情報.限度額有効期間開始日（変更後） = ""
                    hoken.setGendoStYmd(StringUtils.EMPTY);
                    // 【変数】.利用票画面処理用構造体.介護保険情報.限度額有効期間終了日（変更後） = ""
                    hoken.setGendoEdYmd(StringUtils.EMPTY);
                }
            }
            // 【変数】.利用票画面処理用構造体.介護保険情報.担当ケアマネージャ名 = 介護保険情報.担当の介護支援専門員の名前
            hoken.setTanto(cmnUsrHok3gOutDto.getTantoNameKnj());
            // 【変数】.利用票画面処理用構造体.介護保険情報.担当ケアマネージャID = 介護保険情報.担当の介護支援専門員の専門員番号
            hoken.setTantoId(cmnUsrHok3gOutDto.getSenmonNo());
            // 【変数】.利用票画面処理用構造体.介護保険情報.給付率 = 介護保険情報.給付率
            hoken.setKyufuRitu(CommonDtoUtil.objValToString(cmnUsrHok3gOutDto.getKyufuwari()));
            hokenInfo.add(hoken);
        }
        // 介護保険情報
        riyouProcess.setHokenInfo(hokenInfo);
    }

    /**
     * 概算のための userinfo 部分を作成する
     * 
     * @param firstDay     月初日
     * @param lastDay      月末日
     * @param riyouProcess 利用票画面処理用構造体
     * @param dwUsrinfList 利用票：ヘッダ情報(dw_usrinf)
     * @param dwGendoList  利用票：日数.単位数.金額の計算結果欄(dw_gendo)
     */
    public void wf_11_make_userinfo(String firstDay, String lastDay, Gui01149RiyouProcess riyouProcess,
            List<KghCmnRiyou2HeaderAcsOutEntity> dwUsrinfList, List<KghCmnRiyou5SumOutEntity> dwGendoList,
            WeekPlanEstimateDisplaySelectServiceInDto inDto) {
        // 2.引数.利用票：ヘッダ情報(dw_usrinf)
        KghCmnRiyou2HeaderAcsOutEntity dwUsrinf = new KghCmnRiyou2HeaderAcsOutEntity();
        if (dwUsrinfList != null && !dwUsrinfList.isEmpty()) {
            dwUsrinf = dwUsrinfList.getFirst();
        }
        // 支援事業者id
        dwUsrinf.setShienId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        dwUsrinf.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 処理年月(取り込み元年月)
        dwUsrinf.setYymmYm(inDto.getShoriYymm());
        // 処理日
        dwUsrinf.setYymmD(inDto.getShoriYymm() + CommonConstants.STRING_FIRST_DAY);
        // 今日
        dwUsrinf.setCreateYmd(CommonDtoUtil.objValToString(LocalDate.now()));
        dwUsrinf.setTZengetu(CommonConstants.NUMBER_ZERO);
        dwUsrinf.setTTougetu(CommonConstants.NUMBER_ZERO);
        dwUsrinf.setTRuiseki(CommonConstants.NUMBER_ZERO);
        dwUsrinf.setOv30Zengetu(CommonConstants.NUMBER_ZERO);
        dwUsrinf.setYoteiZumiFlg(CommonConstants.NUMBER_ZERO);
        dwUsrinf.setJissekiZumiFlg(CommonConstants.NUMBER_ZERO);
        // 引数.利用票画面処理用構造体.利用者氏名
        dwUsrinf.setDmyUserName(riyouProcess.getUsername());
        // 引数.利用票画面処理用構造体.利用者生年月日
        dwUsrinf.setDmyBirthdayYmd(riyouProcess.getBirthday());
        // 引数.利用票画面処理用構造体.利用者性別
        dwUsrinf.setDmySex(CommonDtoUtil.strValToInt(riyouProcess.getSex()));
        // 引数.利用票画面処理用構造体.保険者番号
        dwUsrinf.setDmyKHokenNo(riyouProcess.getKHokenNo());
        // 引数.利用票画面処理用構造体.保険者名
        dwUsrinf.setDmyKHokenKnj(riyouProcess.getKHokenKnj());
        // 引数.利用票画面処理用構造体.被保険者番号
        dwUsrinf.setDmyHHokenNo(riyouProcess.getHHokenNo());
        // 引数.利用票画面処理用構造体.保険者CD
        dwUsrinf.setKHokenCd(CommonDtoUtil.strValToInt(riyouProcess.getKHokenCd()));
        // 引数.利用票画面処理用構造体.被保険者番号
        dwUsrinf.setHHokenNo(riyouProcess.getHHokenNo());
        // 引数.利用票画面処理用構造体.保険データID
        dwUsrinf.setDmySeqNo(CommonDtoUtil.strValToInt(riyouProcess.getSeqNo()));
        // 引数.利用票画面処理用構造体.要介護度（変更前）
        dwUsrinf.setDmyKaigodo(CommonDtoUtil.strValToInt(riyouProcess.getYokaiKbn()));
        // 引数.利用票画面処理用構造体.要介護度（変更後）
        dwUsrinf.setDmyHenKaigodo(CommonDtoUtil.strValToInt(riyouProcess.getYokaiKbnN()));
        // 引数.利用票画面処理用構造体.認定変更日
        dwUsrinf.setDmyHenDate(riyouProcess.getHenkouYmd());
        // 引数.利用票画面処理用構造体.限度額
        dwUsrinf.setDmyShikyuGaku(CommonDtoUtil.strValToInt(riyouProcess.getTusho1GendoN()));
        // 引数.利用票画面処理用構造体.枝番
        dwUsrinf.setDmyEdaNo(CommonDtoUtil.strValToInt(riyouProcess.getEdaNo()));
        // 引数.利用票画面処理用構造体.限度額適用期間（開始）
        dwUsrinf.setDmyKikanFr(riyouProcess.getKikanFr());
        // 引数.利用票画面処理用構造体.限度額適用期間（終了）
        dwUsrinf.setDmyKikanTo(riyouProcess.getKikanTo());
        // 引数.利用票画面処理用構造体.担当者名
        dwUsrinf.setDmyTanto(riyouProcess.getTanto());
        // 引数.利用票画面処理用構造体.給付率
        dwUsrinf.setDmyFutanRate(CommonDtoUtil.strValToInt(riyouProcess.getFutanRate()));

        // 3. 引数.利用票：日数.単位数.金額の計算結果欄(dw_gendo)追加
        KghCmnRiyou5SumOutEntity dwGendo = new KghCmnRiyou5SumOutEntity();
        // 支援事業者id
        dwGendo.setShienId(CommonDtoUtil.strValToInt(inDto.getSvJigyoId()));
        // 利用者ID
        dwGendo.setUserid(CommonDtoUtil.strValToInt(inDto.getUserId()));
        // 処理年月(取り込み元年月)
        dwGendo.setYymmYm(inDto.getShoriYymm());
        // 処理日
        dwGendo.setYymmD(inDto.getShoriYymm() + CommonConstants.STRING_FIRST_DAY);
        if (dwUsrinfList != null && !dwUsrinfList.isEmpty()) {
            // 引数.利用票：ヘッダ情報.1件目.保険者CD
            dwGendo.setKHokenCd(dwUsrinfList.getFirst().getKHokenCd());
            // 引数.利用票：ヘッダ情報.1件目.被保険者番号
            dwGendo.setHHokenNo(dwUsrinfList.getFirst().getHHokenNo());
        }
        // 今日
        dwGendo.setCreateYmd(CommonDtoUtil.objValToString(LocalDate.now()));
        dwGendo.setOv30Zengetu(CommonConstants.NUMBER_ZERO);
        dwGendo.setCalcYoji(CommonConstants.NUMBER_ONE);
        dwGendo.setYoteiZumiFlg(CommonConstants.NUMBER_ZERO);

        dwGendoList.add(dwGendo);
    }

    /**
     * ケアプランの週間パターンをケアマネの月間に展開する
     * 
     * @param yymmdd                年月日
     * @param riyouInfoList         利用票明細情報
     * @param idsKaigoList          サービス費リスト(ids_kaigo)
     * @param idsKaigoTargerList    サービス費リストの対象サービス明細(ids_kaigo_target)
     * @param idsKaigoBufList       サービス費リストバッファ（1事業所分）(ids_kaigo_buf)
     * @param idsKaigoBufSougouList 総合事業サービス費リストバッファ（1事業所分）(ids_kaigo_buf_sougou)
     */
    public void uf_get_kaigo_master(String yymmdd, List<Gui01152UserInfo> riyouInfoList,
            List<KghSelSvitemCalc1PonNextOutEntity> idsKaigoList, List<Gui01152UserInfo> idsKaigoTargerList,
            List<KghSelSvitemCalc1PonNextOutEntity> idsKaigoBufList,
            List<KghSelSvitemCalc1PonNextSougouOutEntity> idsKaigoBufSougouList) throws Exception {
        // 1. DSのリセット
        // 引数.サービス費リスト(ids_kaigo)にクリア
        idsKaigoList.clear();
        // 引数.サービス費リストバッファ（1事業所分）(ids_kaigo_buf)クリア
        idsKaigoBufList.clear();
        // 引数.サービス費リストの対象サービス明細(ids_kaigo_target)クリア
        idsKaigoTargerList.clear();

        // 2. 引数.利用票明細情報分だけ処理を繰り返す
        // 2.1. 利用票明細からパラメータを取得
        // 変数.サービスID = ループ行のサービス項目ID);
        // 変数.サービス事業所ID = ループ行のサービス事業所ID
        // 2.2. 同じサービスがあるかどうかどうかを検索
        // 検索条件１：変数.サービスID
        // 検索条件２：変数.サービス事業所ID
        // 2.3. 引数.サービス費リストの対象サービス明細(ids_kaigo_target)は見つからなければ、利用票明細をコピー
        List<Gui01152UserInfo> distinctList = riyouInfoList
                .stream().collect(Collectors.toMap(item -> Arrays.asList(item.getSvJigyoId(), item.getSvItemCd()),
                        item -> item, (existing, replacement) -> existing))
                .values().stream().collect(Collectors.toList());
        idsKaigoTargerList.addAll(distinctList);

        // 3. 引数.サービス費リストの対象サービス明細(ids_kaigo_target)分だけ処理を繰り返す
        for (Gui01152UserInfo idsKaigoTarger : idsKaigoTargerList) {
            // 3.1. 変数.事業所ID配列[]を初期化
            List<Integer> jigyoIdList = new ArrayList<Integer>();

            // 3.2. 変数.サービスID = ループ行のサービス項目ID
            Integer svId = CommonDtoUtil.strValToInt(idsKaigoTarger.getSvItemCd());
            // 変数.事業所ID配列[1] = ループ行のサービス事業所ID
            jigyoIdList.add(CommonDtoUtil.strValToInt(idsKaigoTarger.getSvJigyoId()));
            // 変数.サービス種別 = ループ行のサービス種別
            String svType = idsKaigoTarger.getSvtype();

            // 3.3. サービス種類から総合事業かどうかをチェックする
            // 【変数】.チェック結果 = 上記結果
            boolean checkResult = kghCmnF01Logic.chkSougouSvtype(svType);

            // 3.3.1. 【変数】.チェック結果 = true(総合事業サービスである)の場合、
            if (checkResult) {
                // 変数.事業所IDの配列[1] = 0
                jigyoIdList.set(0, CommonConstants.DEFAULT_SV_JIGYO_ID);

                // 明細1行分のサービスマスタを検索
                KghSelSvitemCalc1PonNextSougouByCriteriaInEntity kghSelSvitemCalc1PonNextSougouByCriteriaInEntity = new KghSelSvitemCalc1PonNextSougouByCriteriaInEntity();
                // 項目コード
                kghSelSvitemCalc1PonNextSougouByCriteriaInEntity.setAiIcd(svId);
                // サービス事業者ID
                kghSelSvitemCalc1PonNextSougouByCriteriaInEntity.setAiSvjcdList(jigyoIdList);
                // 開始日
                kghSelSvitemCalc1PonNextSougouByCriteriaInEntity.setAsYmd(yymmdd);
                List<KghSelSvitemCalc1PonNextSougouOutEntity> comMhcItemuseSougouList = comMhcItemuseSougouSelectMapper
                        .findKghSelSvitemCalc1PonNextSougouByCriteria(kghSelSvitemCalc1PonNextSougouByCriteriaInEntity);

                // 検索結果がある場合、戻り値.サービス費リスト(ids_kaigo)をリストに追加
                if (!CollectionUtils.isEmpty(comMhcItemuseSougouList)) {
                    for (KghSelSvitemCalc1PonNextSougouOutEntity item : comMhcItemuseSougouList) {
                        KghSelSvitemCalc1PonNextOutEntity newItem = new KghSelSvitemCalc1PonNextOutEntity();

                        newItem.setTanka(item.getTanka());
                        newItem.setStdhutanwari(item.getStdhutanwari());
                        newItem.setHutanwari(item.getHutanwari());
                        newItem.setTentanka(item.getTentanka());
                        newItem.setHutangaku1(item.getHutangaku1());
                        newItem.setSvJigyoId(item.getSvJigyoId());
                        newItem.setTankaTermid(item.getTankaTermid());
                        newItem.setTermid(item.getTermid());
                        newItem.setItemcode(item.getItemcode());
                        newItem.setStartdateYmd(item.getStartdateYmd());
                        newItem.setEnddateYmd(item.getEnddateYmd());
                        newItem.setSvtype(item.getSvtype());
                        newItem.setSvcode(item.getSvcode());
                        newItem.setScode(item.getScode());
                        newItem.setTenkintype(item.getTenkintype());

                        idsKaigoList.add(newItem);
                    }
                }
            } else {
                // 3.3.2. 【変数】.チェック結果 = false(総合事業ではない)の場合、
                // 3.3.2.1. 変数.サービス種別 < '81'の場合、変数.事業所IDの配列[2] = 0
                if (StringUtils.compare(svType, CommonConstants.SHURUI_81_STRING) < 0) {
                    jigyoIdList.add(CommonConstants.DEFAULT_SV_JIGYO_ID);
                }
                // 3.3.2.2. 明細1行分のサービスマスタを検索
                KghSelSvitemCalc1PonNextByCriteriaInEntity kghSelSvitemCalc1PonNextByCriteriaInEntity = new KghSelSvitemCalc1PonNextByCriteriaInEntity();
                // 項目コード
                kghSelSvitemCalc1PonNextByCriteriaInEntity.setAiIcd(svId);
                // サービス事業者ID
                kghSelSvitemCalc1PonNextByCriteriaInEntity.setAiSvjcdList(jigyoIdList);
                // 開始日
                kghSelSvitemCalc1PonNextByCriteriaInEntity.setAsYmd(yymmdd);
                List<KghSelSvitemCalc1PonNextOutEntity> comMhcItemuseList = comMhcItemuseSelectMapper
                        .findKghSelSvitemCalc1PonNextByCriteria(kghSelSvitemCalc1PonNextByCriteriaInEntity);

                // 検索結果がある場合、戻り値.サービス費リスト(ids_kaigo)をリストに追加
                if (!CollectionUtils.isEmpty(comMhcItemuseList)) {
                    idsKaigoList.addAll(comMhcItemuseList);
                }
            }
        }
    }

    /**
     * フッタに金額をセット
     * 
     * @param dwGendoList  利用票：日数.単位数.金額の計算結果欄(dw_gendo)
     * @param dwUsrinfList 利用票：ヘッダ情報(dw_usrinf)
     * @param dwBeppyoList 利用票別表：明細行(dw_beppyo)
     * @param dwKouhiList  利用票別表：公費集計欄(dw_kouhi)
     */
    public Gui01042Sum wf_20_set_kingaku(List<KghCmnRiyou5SumOutEntity> dwGendoList,
            List<KghCmnRiyou2HeaderAcsOutEntity> dwUsrinfList, List<Gui01042DwBeppyo> dwBeppyoList,
            List<KghCmnBeppyo3KohiOutEntity> dwKouhiList) {
        // 1. 引数.利用票別表：明細行ＤＷ（dw_beppyo)の明細行数 > 0の場合、後続処理
        // 以外の場合、呼出し元に返す
        if (CollectionUtils.isEmpty(dwBeppyoList)) {
            return new Gui01042Sum();
        }

        // 3. 合計行の値より総合計値を求める
        // SUM(費用総額)
        BigDecimal hiyouSougakuSum = BigDecimal.ZERO;
        // SUM(介護保険給付額)
        BigDecimal hKyufugakuSum = BigDecimal.ZERO;
        // SUM(利用者負担額・保険給付対象分)
        BigDecimal hutanHSum = BigDecimal.ZERO;
        // SUM(利用者負担額・全額自己負担分)
        BigDecimal hutanJSum = BigDecimal.ZERO;
        // SUM(合計単位数)
        BigDecimal svTensuSum = BigDecimal.ZERO;
        // SUM(種類限度外単位数)
        BigDecimal sTensuOverSum = BigDecimal.ZERO;
        // SUM(種類限度額内単位数)
        BigDecimal sTensuSum = BigDecimal.ZERO;
        // SUM(区分支給限度外単位数)
        BigDecimal kTensuOverSum = BigDecimal.ZERO;
        // SUM(区分支給限度額内単位数)
        BigDecimal kTensuSum = BigDecimal.ZERO;

        for (Gui01042DwBeppyo dwBeppyo : dwBeppyoList) {

            // 3.1. 戻り値.利用票別表：明細行(dw_beppyo).合計フラグ = 1（合計行）の場合、合計を行う
            if (dwBeppyo.getTotalF() == CommonConstants.GOUKEI_FLG_1) {

                // 3.1.1. 以下の項目なら総合計に加える
                // SUM(費用総額)
                hiyouSougakuSum = hiyouSougakuSum.add(dwBeppyo.getHiyouSougaku() == null ? BigDecimal.ZERO
                        : new BigDecimal(dwBeppyo.getHiyouSougaku()));
                // SUM(介護保険給付額)
                hKyufugakuSum = hKyufugakuSum.add(
                        dwBeppyo.getHKyufugaku() == null ? BigDecimal.ZERO : new BigDecimal(dwBeppyo.getHKyufugaku()));
                // SUM(利用者負担額・保険給付対象分)
                hutanHSum = hutanHSum
                        .add(dwBeppyo.getHutanH() == null ? BigDecimal.ZERO : new BigDecimal(dwBeppyo.getHutanH()));
                // SUM(利用者負担額・全額自己負担分)
                hutanJSum = hutanJSum
                        .add(dwBeppyo.getHutanJ() == null ? BigDecimal.ZERO : new BigDecimal(dwBeppyo.getHutanJ()));

                // 3.1.2.戻り値.利用票別表：明細行(dw_beppyo).加算フラグ = 0（通常行）
                // AND 戻り値.利用票別表：明細行(dw_beppyo).３０日超過フラグ = 0（通常行）の場合、合計行なら総合計に加える
                if (dwBeppyo.getKasanF() == CommonConstants.KASAN_FLG_NORMAL
                        && dwBeppyo.getOv30Fl() == CommonConstants.OVER_30DAY_FLG_NORMAL) {
                    // SUM(合計単位数)
                    svTensuSum = svTensuSum.add(
                            dwBeppyo.getSvTensu() == null ? BigDecimal.ZERO : new BigDecimal(dwBeppyo.getSvTensu()));
                    // SUM(種類限度外単位数)
                    sTensuOverSum = sTensuOverSum.add(dwBeppyo.getSTensuOver() == null ? BigDecimal.ZERO
                            : new BigDecimal(dwBeppyo.getSTensuOver()));
                    // SUM(種類限度額内単位数)
                    sTensuSum = sTensuSum
                            .add(dwBeppyo.getSTensu() == null ? BigDecimal.ZERO : new BigDecimal(dwBeppyo.getSTensu()));
                    // SUM(区分支給限度外単位数)
                    kTensuOverSum = kTensuOverSum.add(dwBeppyo.getKTensuOver() == null ? BigDecimal.ZERO
                            : new BigDecimal(dwBeppyo.getKTensuOver()));
                    // SUM(区分支給限度額内単位数)
                    kTensuSum = kTensuSum
                            .add(dwBeppyo.getKTensu() == null ? BigDecimal.ZERO : new BigDecimal(dwBeppyo.getKTensu()));
                }
            }
        }

        Gui01042Sum sumData = new Gui01042Sum();
        // 3.2.合計欄を再表示します
        // 金額の計算結果欄.種類限度基準超過 = 上記の種類限度外単位数の合計
        sumData.setHShuOver(CommonDtoUtil.objValToString(sTensuOverSum));
        // 金額の計算結果欄.種類限度基準内 = 上記の種類限度額内単位数の合計
        sumData.setHShuKijun(CommonDtoUtil.objValToString(sTensuSum));
        // 金額の計算結果欄.超過_区分支給限度 ＝ 上記の区分支給限度外単位数の合計
        sumData.setKbnOverSum(CommonDtoUtil.objValToString(kTensuOverSum));
        // 金額の計算結果欄.基準内_区分支給限度 ＝ 上記の区分支給限度額内単位数の合計
        sumData.setKbnKijunSum(CommonDtoUtil.objValToString(kTensuSum));
        // 金額の計算結果欄.保険分_利用者負担額 = 上記の利用者負担額・保険給付対象分の合計
        sumData.setHknHutanSum(CommonDtoUtil.objValToString(hutanHSum));
        // 金額の計算結果欄.全額分_利用者負担額 = 上記の利用者負担額・全額自己負担分の合計
        sumData.setJihHutanSum(CommonDtoUtil.objValToString(hutanJSum));

        // 3.2.1. 戻り値.利用票：ヘッダ情報(dw_usrinf)計算フラグ = nullの場合
        if (dwUsrinfList.getFirst().getCalcYoji() == null) {
            // 戻り値.利用票：日数.単位数.金額の計算結果欄(dw_gendo).実績_単位数計算結果 = 上記の合計単位数の合計
            sumData.setTensuJissekiSum(CommonDtoUtil.objValToString(svTensuSum));
            // 戻り値.利用票：ヘッダ情報(dw_usrinf)サービス単位数（実績） = 上記の合計単位数の合計
            dwUsrinfList.getFirst().setSvTensuJ(CommonDtoUtil.strValToDouble(svTensuSum.toString()));
        } else {
            // 3.2.2. 戻り値.利用票：ヘッダ情報(dw_usrinf)計算フラグ <> nullの場合
            // 戻り値.利用票：日数.単位数.金額の計算結果欄(dw_gendo).予定_単位数計算結果 = 上記の合計単位数の合計
            sumData.setTensuYoteiSum(CommonDtoUtil.objValToString(svTensuSum));
            // 戻り値.利用票：ヘッダ情報(dw_usrinf)サービス単位数（予定） = 上記の合計単位数の合計
            dwUsrinfList.getFirst().setSvTensuY(CommonDtoUtil.strValToDouble(svTensuSum.toString()));
        }

        // 4. サービス事業所毎の合計行にサービス種類項目＋合計の文字をセットする
        for (Gui01042DwBeppyo dwBeppyo : dwBeppyoList) {
            // 4.1. 戻り値.利用票別表：明細行(dw_beppyo).サービス項目ID = 0の場合、事業所情報を検索する
            if (dwBeppyo.getSvItemCd() == CommonConstants.NUMBER_0) {
                CmnComMscSvjigyoCByCriteriaInEntity cmnComMscSvjigyoCByCriteriaInEntity = new CmnComMscSvjigyoCByCriteriaInEntity();
                // 上記結果を変数.事業所情報に設定する
                List<CmnComMscSvjigyoCOutEntity> svjigyoList = serviceJigyosyoInfoSelectMapper
                        .findCmnComMscSvjigyoCByCriteria(cmnComMscSvjigyoCByCriteriaInEntity);

                if (!CollectionUtils.isEmpty(svjigyoList)) {
                    // 変数.事業所情報_Filter = 変数.事業所情報に以下の条件のデータを洗い出す。
                    // 検索条件: サービス種類コード =戻り値.利用票別表：明細行(dw_beppyo).サービス種類CD
                    List<CmnComMscSvjigyoCOutEntity> filterList = svjigyoList.stream()
                            .filter(item -> StringUtils.equals(item.getSvKindCd(), dwBeppyo.getSvtype())).toList();
                    // 4.2 変数.事業所情報が取得できる場合
                    if (!CollectionUtils.isEmpty(filterList)) {
                        // 4.2.1. 戻り値.利用票別表：明細行(dw_beppyo).３０日超過フラグ = 1(短期入所連続利用30日超過行)の場合
                        if (dwBeppyo.getOv30Fl() == CommonConstants.OVER_30DAY_FLG_OVER) {
                            // 戻り値.利用票別表：明細行(dw_beppyo).事業所名 = "30超" + 変数.事業所情報.サービス事業所名 + "合計"
                            dwBeppyo.setJigyoNameKnj(CommonConstants.STR_THIRDTH_MORE
                                    + filterList.getFirst().getSvJigyoKnj() + CommonConstants.STR_TOTAL);
                        } else {
                            // 4.2.2. 戻り値.利用票別表：明細行(dw_beppyo).３０日超過フラグ <> 1(短期入所連続利用30日超過行)の場合
                            // 戻り値.利用票別表：明細行(dw_beppyo).事業所名 = 変数.事業所情報.サービス事業所名 + "合計"
                            dwBeppyo.setJigyoNameKnj(filterList.getFirst().getSvJigyoKnj() + CommonConstants.STR_TOTAL);
                        }
                    }
                }

            }
        }

        // 5. 戻り値.利用票：日数.単位数.金額の計算結果欄(dw_gendo)への公費負担額などのセット
        // 5.1. 公費負担・自負
        BigDecimal ld_kouhi_f = BigDecimal.ZERO;
        BigDecimal ld_kouhi_j = BigDecimal.ZERO;

        // 5.2. 公費負担額取得
        // 引数.利用票別表：公費集計欄(dw_kouhi).公費集計欄の明細行数 > 0
        if (!CollectionUtils.isEmpty(dwKouhiList)) {

            // 公費分負担額 合計
            Double sumKouhi = Double.valueOf(0);
            // 公費分本人負担額 合計
            Double sumHonnin = Double.valueOf(0);
            for (KghCmnBeppyo3KohiOutEntity kohi : dwKouhiList) {
                sumKouhi += kohi.getFutanGaku();
                sumHonnin += kohi.getHonninFutanGaku();
            }

            // 6.2.1. 引数.利用票別表：公費集計欄(dw_kouhi).公費分負担額 > 0 の場合
            if (sumKouhi.compareTo(Double.valueOf(0)) > 0) {
                // 変数.ld_kouhi_f = 引数.利用票別表：公費集計欄(dw_kouhi).公費分負担額の合計
                ld_kouhi_f = BigDecimal.valueOf(sumKouhi);
            }
            // 6.2.2. 引数.利用票別表：公費集計欄(dw_kouhi).公費分本人負担額 > 0 の場合
            if (sumHonnin.compareTo(Double.valueOf(0)) > 0) {
                // 変数.ld_kouhi_j = 引数.利用票別表：公費集計欄(dw_kouhi).公費分本人負担額の合計
                ld_kouhi_j = BigDecimal.valueOf(sumHonnin);
            }
        }

        // 5.3. フッタ側へのデータセット2
        // 戻り値.利用票：日数.単位数.金額の計算結果欄(dw_gendo).保険外利用料合計 = 0
        sumData.setHokenGaiRiyouSum(CommonConstants.STR_0);
        // 戻り値.利用票：日数.単位数.金額の計算結果欄(dw_gendo).公費負担額合計 = 変数.ld_kouhi_f
        sumData.setFutanGakuSum(CommonDtoUtil.objValToString(ld_kouhi_f));
        // 戻り値.利用票：日数.単位数.金額の計算結果欄(dw_gendo).本人負担額合計 = 変数.ld_kouhi_j
        sumData.setHonninFutanGakuSum(CommonDtoUtil.objValToString(ld_kouhi_j));
        // 戻り値.利用票：日数.単位数.金額の計算結果欄(dw_gendo).負担額減免1 = 0
        sumData.setFutanGakuGenmen(CommonConstants.STR_0);

        return sumData;
    }
}
