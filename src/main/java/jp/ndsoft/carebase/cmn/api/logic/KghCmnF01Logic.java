package jp.ndsoft.carebase.cmn.api.logic;

import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.common.CommonConstants;
import jp.ndsoft.carebase.cmn.api.logic.dto.CmnTantoInformationOutDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.util.CollectionUtils;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mybatis.ComMhcItemuseSougouMapper;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMhcItemuseSougou;
import jp.ndsoft.carebase.common.dao.mybatis.entity.ComMhcItemuseSougouCriteria;
import jp.ndsoft.carebase.common.dao.mysql.entity.AdderServiceKasanFlgByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.AdderServiceKasanFlgOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChsGaibuFlgByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ChsGaibuFlgOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnChkHokenTimingByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnChkHokenTimingOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnChkSvriyoTimingByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnChkSvriyoTimingOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnChkTermSvtypeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnChkTermSvtypeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnIsUserJissekiZumiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CmnIsUserJissekiZumiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcKmKaigoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcKmKaigoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcSmKaigoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMhcSmKaigoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComMscSvjigyoNameFirstSvKindCdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucTantoDataByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ComTucTantoDataOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GaibuGendoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GaibuGendoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GendoKanriKbnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.GendoKanriKbnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoYobouSaabisuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KaigoYobouSaabisuOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghFygZengetucopyGetItemcodeByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghFygZengetucopyGetItemcodeOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkRoukenPrintPreByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghKrkRoukenPrintPreOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoushaNameByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.RiyoushaNameOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SanteiTaniKaisuByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SenmonNoByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SenmonNoOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShisetuIdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.ShisetuIdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvHoujinIdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvHoujinIdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvJigyoCdByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvJigyoCdOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvtypeNameByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.SvtypeNameOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TantouIdUserByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.TantouIdUserOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YokaigoJotaiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.YokaigoJotaiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CmnTucPlanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcItemuseSougouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcKmSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcSmSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMhcTermSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscGaibuGendoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoNameFirstSvKindCdSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoNameSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvjigyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscSvtypeSougouSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscYokaigoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucKaigoHokenSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucSvriyoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucTantoSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComTucUserSelectMapper;

/**
 * サービス種類から総合事業かどうかをチェックする
 * 
 * <AUTHOR>
 */
@Component
public class KghCmnF01Logic {

    private final NdsMidaLogic ndsMidaLogic;
    /** サービス種類コードのリスト */
    private static final Set<String> SOUGOU_SVTYPES = Set.of("A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9", "AA",
            "AB", "AC", "AD", "AE");
    /** scodeの先頭6桁の最後4桁のリスト(サービス種類コード: 33) */
    private static final List<String> SCODE_LAST_FOUR_33_CODES = Arrays.asList("1160", "6003", "6100", "6101", "6102",
            "6103", "6123", "6124", "6128", "6129", "6130", "6131", "6133", "6134", "6132", "4002", "4003", "6109",
            "6122", "6201", "6320", "6330", "6304", "6305", "6306", "6307", "6308", "6135", "6136", "4001", "6004",
            "6005", "6006", "6099", "6321", "6361", "8300", "8301", "6141", "1161", "6143", "6166", "6167", "6237",
            "6238", "C201", "C202", "C203", "C204", "C205", "C206", "D201", "D202", "D203", "D204", "D205", "D206",
            "6380", "6381", "6382", "6383", "6384", "6385", "6386", "6387", "6388", "6389", "6390", "6391", "6392",
            "6393", "6394", "E206");
    /** scodeの先頭6桁の最後4桁のリスト(サービス種類コード: 35) */
    private static final List<String> SCODE_LAST_FOUR_35_CODES = Arrays.asList("1722", "1723", "1724", "1725", "1726",
            "1727", "1728", "1821", "1822", "1823", "1824", "1825", "1826", "1827", "2141", "2142", "2143", "6003",
            "6100", "6101", "6102", "6103", "6123", "6124", "6128", "6129", "6130", "6131", "6133", "6134", "2322",
            "2323", "2324", "2325", "2326", "2327", "2328", "6132", "4002", "4003", "6109", "6122", "6201", "6304",
            "6305", "6135", "6136", "4001", "6004", "6099", "6361", "8300", "8301", "6141", "2329", "6125", "6166",
            "6167", "6237", "6238", "C201", "C202", "C203", "D201", "D202", "D203", "6380", "6381", "6382", "6383",
            "6384", "6385", "6386", "6387", "6388", "6389", "6390", "6391", "6392", "6393", "6394", "1828", "E203");
    /** サービス種類コード: 00 */
    private static final String SVTYPE_ZERO_ZERO = "00";
    /** サービス種類コード: 33 */
    private static final String SVTYPE_33 = "33";
    /** サービス種類コード: 35 */
    private static final String SVTYPE_35 = "35";
    /** サービス種類コード: 81 */
    private static final String SVTYPE_81 = "81";
    /** サービス種類コード: 99 */
    private static final String SVTYPE_99 = "99";
    /** scodeの先頭6桁: 000000 */
    private static final String SCODE_START_000000 = "000000";
    /** scodeの先頭2桁: 33 */
    private static final String SCODE_START_33 = "33";
    /** scodeの先頭2桁: 35 */
    private static final String SCODE_START_35 = "35";
    /** 合成識別区分: 3 */
    private static final String GOUSIK_KBN_3 = "3";
    /** 介護サービス費適用正式名称取得 */
    @Autowired
    private ComMhcItemuseSelectMapper comMhcItemuseSelectMapper;
    /** 介護予防・日常生活支援総合事業サービス費適用マスタ情報取得 */
    @Autowired
    private ComMhcItemuseSougouMapper comMhcItemuseSougouMapper;

    /** サービス事業者マスタ（１５－５）情報取得 */
    @Autowired
    private ComMscSvjigyoSelectMapper comMscSvjigyoSelectMapper;
    /** 年月日に サービス種別 が存在するかをチェック */
    @Autowired
    private ComMhcTermSelectMapper comMhcTermSelectMapper;
    /** */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;
    /** */
    @Autowired
    private ComMscSvjigyoNameFirstSvKindCdSelectMapper comMscSvjigyoNameFirstSvKindCdSelectMapper;
    /** */
    @Autowired
    private ComMscSvtypeSougouSelectMapper comMscSvtypeSougouSelectMapper;
    /** */
    @Autowired
    private ComMscSvjigyoNameSelectMapper comMscSvjigyoNameSelectMapper;
    /** */
    @Autowired
    private ComMhcItemuseSougouSelectMapper comMhcItemuseSougouSelectMapper;
    /** */
    @Autowired
    private ComMscYokaigoSelectMapper comMscYokaigoSelectMapper;
    /** */
    @Autowired
    private ComTucUserSelectMapper comTucUserSelectMapper;
    /** */
    @Autowired
    private CmnTucPlanSelectMapper cmnTucPlanSelectMapper;
    /** */
    @Autowired
    private ComMscGaibuGendoSelectMapper comMscGaibuGendoSelectMapper;
    /** 総合事業サービスマスタ（国保連） */
    @Autowired
    private ComMhcSmSelectMapper comMhcSmSelectMapper;
    /** 介護サービスマスタ（国保連） */
    @Autowired
    private ComMhcKmSelectMapper comMhcKmSelectMapper;
    /** 1-17.担当ケアマネ履歴管理テーブル */
    @Autowired
    private ComTucTantoSelectMapper comTucTantoSelectMapper;
    /** KghCmpF01Logicクラス */
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;
    /** 03-01 職員基本 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;
    @Autowired
    private ComTucKaigoHokenSelectMapper comTucKaigoHokenSelectMapper;
    @Autowired
    private ComTucSvriyoSelectMapper comTucSvriyoSelectMapper;

    KghCmnF01Logic(NdsMidaLogic ndsMidaLogic) {
        this.ndsMidaLogic = ndsMidaLogic;
    }

    /**
     * f_cmn_chk_keikaku_choufuku : : 計画転送前に別の支援事業者で計画が作成されていないかを確認する
     * 
     * @return 出力DTO
     * <AUTHOR>
     */
    public Boolean cmnChkKeikakuChoufuku() {
        return false;
    }

    /**
     * 例外加算をチェックする f_cmn_chk_kasan_exception
     * 通常は加算扱いされているが、レコード上は加算ではないサービス（合成識別区分が1のもの）を判別する 特定診療のコードも判別対象とする
     * 
     * @param asScode サービスコード（ND独自8桁）
     * @param asDate  年月日（yyyy/MM/dd）
     * @return 0: 例外加算ではない, 1: 例外加算である
     */
    public Integer chkKasanException(String asScode, String asDate) {
        Integer liRet = 0;

        // サービスコードの種類とコードを取得
        String lsSvtype = StringUtils.left(asScode, 2); // LeftA equivalent
        String lsSvcode = StringUtils.mid(asScode, 2, 4); // f_nds_mida equivalent

        // サービス種類による分岐
        switch (lsSvtype) {
            case "12": // 訪問入浴
                if (asDate.compareTo("2024/04/01") >= 0 && "4000".equals(lsSvcode)) {
                    // 訪問入浴看取り連携体制加算
                    liRet = 1;
                }
                break;

            case "13": // 訪問看護
                if ("7000".equals(lsSvcode)) {
                    liRet = 1;
                }
                if (asDate.compareTo("2024/06/01") >= 0 && "4021".equals(lsSvcode)) {
                    // 訪問看護遠隔死亡診断補助加算
                    liRet = 1;
                }
                break;

            case "14": // 訪問リハ
                if (asDate.compareTo("2015/04/01") >= 0 && "5005".equals(lsSvcode)) {
                    // リハマネジメント加算Ⅱを追加
                    liRet = 1;
                }
                if (asDate.compareTo("2018/04/01") >= 0 && ("5006".equals(lsSvcode) || "5007".equals(lsSvcode))) {
                    // リハマネジメント加算Ⅲ、Ⅳを追加
                    liRet = 1;
                }
                if (asDate.compareTo("2021/04/01") >= 0 && ("5008".equals(lsSvcode) || "5009".equals(lsSvcode))) {
                    // リハマネジメント加算Ａ２、B２を追加
                    liRet = 1;
                }
                if (asDate.compareTo("2024/06/01") >= 0 && "5022".equals(lsSvcode)) {
                    // 訪問リハマネジメント加算３
                    liRet = 1;
                }
                break;

            case "16": // 通所リハ
                if (asDate.compareTo("2015/04/01") >= 0 && ("5608".equals(lsSvcode) || "5609".equals(lsSvcode))) {
                    // リハマネジメント加算Ⅰは通常加算に変更となった
                    liRet = 1;
                }
                if (asDate.compareTo("2018/04/01") >= 0 && ("5615".equals(lsSvcode) || "5616".equals(lsSvcode)
                        || "5617".equals(lsSvcode) || "5618".equals(lsSvcode))) {
                    // リハマネジメント加算Ⅲ、Ⅳを追加
                    liRet = 1;
                }
                if (asDate.compareTo("2021/04/01") >= 0 && ("5619".equals(lsSvcode) || "5620".equals(lsSvcode)
                        || "5621".equals(lsSvcode) || "5622".equals(lsSvcode))) {
                    // リハマネジメント加算Ａ２１、Ａ２２、B２１、B２２を追加
                    liRet = 1;
                }
                if (asDate.compareTo("2024/06/01") >= 0
                        && ("5631".equals(lsSvcode) || "5632".equals(lsSvcode) || "5640".equals(lsSvcode))) {
                    // 通所リハマネジメント加算３１
                    // 通所リハマネジメント加算３２
                    // 通所リハマネジメント加算４
                    liRet = 1;
                }
                break;

            case "21": // 短期入所生活
                if (asDate.compareTo("2024/04/01") >= 0 && "4000".equals(lsSvcode)) {
                    // 短期生活看取り連携体制加算
                    liRet = 1;
                }
                break;

            case "22": // 短期入所老健
            case "25": // 予防短期老健
                if ("6000".equals(lsSvcode) || "9000".equals(lsSvcode) || "T".equals(StringUtils.mid(asScode, 2, 1))) {
                    liRet = 1;
                }
                break;

            case "27": // 特定施設短期
            case "28": // 密着特定短期
                if (asDate.compareTo("2024/04/01") >= 0 && "9010".equals(lsSvcode)) {
                    liRet = 1;
                }
                break;

            case "32": // 認知症対応型
                if ("6142".equals(lsSvcode) || "6143".equals(lsSvcode) || "6144".equals(lsSvcode)
                        || "6502".equals(lsSvcode)) {
                    liRet = 1;
                }
                if (asDate.compareTo("2018/04/01") >= 0 && "6400".equals(lsSvcode)) {
                    // 認知症対応型入院時費用
                    liRet = 1;
                }
                if (asDate.compareTo("2021/04/01") >= 0
                        && ("6142".equals(lsSvcode) || "6143".equals(lsSvcode) || "6144".equals(lsSvcode))) {
                    // 看取り介護加算２、３、４を追加
                    liRet = 1;
                }
                if (asDate.compareTo("2024/04/01") >= 0 && ("6150".equals(lsSvcode) || "9010".equals(lsSvcode))) {
                    // 認知症対応型退居時情報提供加算
                    // 短期地域特定施設新興感染症等施設療養費
                    liRet = 1;
                }
                break;

            case "33": // 特定施設入居
            case "36": // 密着特定入居
                if ("6125".equals(lsSvcode) || "6126".equals(lsSvcode) || "6127".equals(lsSvcode)) {
                    liRet = 1;
                }
                if (asDate.compareTo("2021/04/01") >= 0
                        && Arrays.asList("6120", "6124", "6137", "6138", "6139", "6140").contains(lsSvcode)) {
                    // 看取り介護加算Ⅰ１、Ⅱ１、Ⅱ２、Ⅱ３、Ⅱ４を追加
                    liRet = 1;
                }
                if (asDate.compareTo("2024/04/01") >= 0 && ("6150".equals(lsSvcode) || "9010".equals(lsSvcode))) {
                    // 特定施設退居時情報提供加算、地域特定施設退居時情報提供加算
                    // 特定施設新興感染症等施設療養費、地域特定施設新興感染症等施設療養費
                    liRet = 1;
                }
                break;

            case "35": // 予防特定施設
                if (asDate.compareTo("2024/04/01") >= 0 && ("6150".equals(lsSvcode) || "9010".equals(lsSvcode))) {
                    // 予防特定施設退居時情報提供加算
                    // 予防特定施設新興感染症等施設療養費
                    liRet = 1;
                }
                break;

            case "37": // 予防認知対応
                if ("6502".equals(lsSvcode)) {
                    liRet = 1;
                }
                if (asDate.compareTo("2018/04/01") >= 0 && "6400".equals(lsSvcode)) {
                    // 予認知症対応型入院時費用
                    liRet = 1;
                }
                if (asDate.compareTo("2024/04/01") >= 0 && ("6150".equals(lsSvcode) || "9010".equals(lsSvcode))) {
                    // 予認知症対応型退居時情報提供加算
                    // 予認知症対応型新興感染症等施設療養費
                    liRet = 1;
                }
                break;

            case "38": // 認知短期利用
            case "39": // 予防認知短期
                if (asDate.compareTo("2024/04/01") >= 0 && "9010".equals(lsSvcode)) {
                    // 短期共同生活新興感染症等施設療養費
                    // 予短期共同新興感染症等施設療養費
                    liRet = 1;
                }
                break;

            case "73": // 小規居宅介護
                if ("4000".equals(lsSvcode)) {
                    liRet = 1;
                }
                break;

            case "76": // 訪問介護看護
            case "77": // 複合型
                if ("6100".equals(lsSvcode)) {
                    liRet = 1;
                }
                if (asDate.compareTo("2024/04/01") >= 0 && "4021".equals(lsSvcode)) {
                    // 看護小規模遠隔死亡診断補助加算
                    liRet = 1;
                }
                break;

            case "2A": // 特定型
            case "2B": // 密着型
                if ("6000".equals(lsSvcode) || "T".equals(StringUtils.mid(asScode, 2, 1))) {
                    liRet = 1;
                }
                break;

        }

        return liRet;
    }

    /**
     * f_cmn_get_houjin_cd : サービス事業者マスタ（１５－５）テーブル情報の法人IDを取得する
     * 
     * @param alSvJigyo サービス事業者ID
     * @return 法人ID
     * <AUTHOR>
     */
    public Integer getHoujinCd(Integer alSvJigyo) {
        SvHoujinIdByCriteriaInEntity svHoujinIdByCriteriaInEntity = new SvHoujinIdByCriteriaInEntity();
        svHoujinIdByCriteriaInEntity.setAlSvJigyo(CommonDtoUtil.objValToString(alSvJigyo));
        List<SvHoujinIdOutEntity> svHoujinIdList = comMscSvjigyoSelectMapper
                .findSvHoujinIdByCriteria(svHoujinIdByCriteriaInEntity);
        if (!CollectionUtils.isNullOrEmpty(svHoujinIdList)) {
            return svHoujinIdList.getFirst().getHoujinId();
        }
        return CommonConstants.INT_MINUS_1;
    }

    /**
     * f_cmn_get_shisetu_cd : サービス事業者マスタ（１５－５）テーブル情報の施設IDを取得する
     * 
     * @param alSvJigyo サービス事業者ID
     * @return 施設ID
     * <AUTHOR>
     */
    public Integer getShisetuCd(Integer alSvJigyo) {
        ShisetuIdByCriteriaInEntity shisetuIdByCriteriaInEntity = new ShisetuIdByCriteriaInEntity();
        shisetuIdByCriteriaInEntity.setAlSvJigyo(CommonDtoUtil.objValToString(alSvJigyo));
        List<ShisetuIdOutEntity> shisetuIdList = comMscSvjigyoSelectMapper
                .findShisetuIdByCriteria(shisetuIdByCriteriaInEntity);
        if (!CollectionUtils.isNullOrEmpty(shisetuIdList)) {
            return shisetuIdList.getFirst().getShisetuId();
        }
        return CommonConstants.INT_MINUS_1;
    }

    /**
     * 利用者の実績済フラグを確認する f_cmn_is_user_jisseki_zumi
     * 
     * @param alShien 自事業者ID
     * @param alUser  利用者ID
     * @param asYymm  提供年月 (yyyy/mm)
     * @param asYymmD 提供年月日 (yyyy/mm/dd)
     * @return long 2=実績済み ／ 1=予定済み ／ 0=無し
     */
    public Integer getUserJissekiZumi(Integer alShien, Integer alUser, String asYymm, String asYymmD) {
        Integer liRet = 0;
        Integer liYotei = 0;
        Integer liJisseki = 0;

        CmnIsUserJissekiZumiByCriteriaInEntity in = new CmnIsUserJissekiZumiByCriteriaInEntity();
        in.setAlShien(CommonDtoUtil.objValToString(alShien));
        in.setAlUser(CommonDtoUtil.objValToString(alUser));
        in.setAsYymm(asYymmD);
        in.setAsYymmD(asYymmD);
        List<CmnIsUserJissekiZumiOutEntity> items = cmnTucPlanSelectMapper.findCmnIsUserJissekiZumiByCriteria(in);
        if (items != null && items.size() > 0) {
            CmnIsUserJissekiZumiOutEntity result = items.get(0);
            // 予定済フラグを取得
            liYotei = Optional.of(result.getYoteiZumiFlg()).orElse(0);

            // 実績済フラグを取得
            liJisseki = Optional.of(result.getJissekiZumiFlg()).orElse(0);
            ;
        }
        // フラグの合計を計算
        liRet = liYotei + liJisseki;

        // 実績済フラグが存在しない場合は0を返す
        if (liRet <= 0) {
            liRet = 0;
        }

        return liRet;
    }

    /**
     * 利用者番号を得る f_cmn_get_user_self_id
     * 
     * @param alUid 利用者ID
     * @return self_id 利用者番号
     */
    public Integer getUserSelfId(Integer alUid) {
        KghKrkRoukenPrintPreByCriteriaInEntity in = new KghKrkRoukenPrintPreByCriteriaInEntity();
        in.setId(alUid);
        List<KghKrkRoukenPrintPreOutEntity> items = comTucUserSelectMapper.findKghKrkRoukenPrintPreByCriteria(in);
        return items.stream().findFirst().map(KghKrkRoukenPrintPreOutEntity::getSelfId).orElse(0);
    }

    /**
     * 利用者名（カナ）を得る f_cmn_get_user_name_kana
     * 
     * @param alUid 利用者ID
     * @return self_id 利用者番号
     */
    public String getUserNameKana(Integer alUid) {
        RiyoushaNameByCriteriaInEntity in = new RiyoushaNameByCriteriaInEntity();
        in.setId(alUid);
        List<RiyoushaNameOutEntity> items = comTucUserSelectMapper.findRiyoushaNameByCriteria(in);
        return items.stream().findFirst().map(i -> {
            return i.getName1Kana() + " " + i.getName2Kana();
        }).orElse(" ");
    }

    /**
     * 利用者名を得る f_cmn_get_user_name
     * 
     * @param alUid 利用者ID
     * @return 作成された名
     */
    public String getUserName(Integer alUid) {
        KghKrkRoukenPrintPreByCriteriaInEntity in = new KghKrkRoukenPrintPreByCriteriaInEntity();
        in.setId(alUid);
        List<KghKrkRoukenPrintPreOutEntity> items = comTucUserSelectMapper.findKghKrkRoukenPrintPreByCriteria(in);
        return items.stream().findFirst().map(KghKrkRoukenPrintPreOutEntity::getFullName).orElse("");
    }

    /**
     * ％の加算 f_cmn_is_adder_percent
     * 
     * @param asCode サービスコード（ND独自８桁）
     * @param asDate 年月日
     * @return 1～3=処遇改善加算／100=特別地域加算／200=小規模事業所加算／300=中山間地域等加算
     *         400=同一建物減算／500=共生型サービス／600=感染症等対応加算／610=感染症特例加算
     *         700=特定事業所加算V／800=通所リハ継続減算／900=過少サービス減算／1000=サテライト体制未整備減算
     */
    public long chkIsAdderPercent(String asCode, String asDate) {
        String lsSvt;

        // 特別地域加算かどうか
        if (getSpecialPlaceNew(asDate, asCode)) {
            return 100;
        }

        // 小規模事業所加算・中山間地域加算・処遇改善加算かどうか
        Integer liChu = 0;
        lsSvt = asCode.substring(0, 2);

        // サービスコード判定
        if (Arrays.asList("11", "12", "13", "14", "15", "16", "31", "34", "61", "62", "63", "64", "65", "66", "68",
                "69", "71", "72", "74", "73", "75", "76", "77", "78", "79", "A1", "A2", "A5", "A6").contains(lsSvt)) {
            liChu = getChusankanRiyo(asCode, asDate); // f_cmn_is_chusankan_riyo
            if (liChu == 2) {
                // 小規模事業所加算・中山間地域加算・処遇改善加算かどうか
                return 300;
            }
            if (liChu == 1) {
                return 200;
            }
        }

        Integer liRet = isShoguuKaizenKasan(asDate, asCode); // f_cmn_is_shoguu_kaizen_kasan

        // 同一建物減算
        if (liRet == 0 && getDouituGenzan(asDate, asCode) > 0) { // f_cmn_is_douitu_genzan
            liRet = 400;
        }

        // 共生型サービス
        if (liRet == 0 && getKyoseiGenzan(asDate, asCode) > 0) { // f_cmn_is_kyosei_genzan
            liRet = 500;
        }

        // 感染症等対応加算
        if (liRet == 0 && isKansenKasan(asDate, asCode) > 0) { // f_cmn_is_kansen_kasan
            liRet = 600;
        }

        // 感染症特例加算
        if (liRet == 0 && isKansenTokureiKasan(asDate, asCode) > 0) { // f_cmn_is_kansen_tokurei_kasan
            liRet = 610;
        }

        // 特定事業所加算V
        if (liRet == 0 && isTokuteiJigyoKasan(asDate, asCode) > 0) { // f_cmn_is_tokutei_jigyo_kasan
            liRet = 700;
        }

        // 通所リハ継続減算
        if (liRet == 0 && isRihaKeizokuGenzan(asDate, asCode) > 0) { // f_cmn_is_riha_keizoku_genzan
            liRet = 800;
        }

        // 過少サービス減算
        if (liRet == 0 && isKashouServiceGenzan(asDate, asCode) > 0) { // f_cmn_is_kashou_service_genzan
            liRet = 900;
        }

        // サテライト体制未整備減算
        if (liRet == 0 && isSatelliteMiseibiGenzan(asDate, asCode) > 0) { // f_cmn_is_satellite_miseibi_genzan
            liRet = 1000;
        }

        // 改正_ケアマネ共通関数
        if (liRet == 0) {
            String tmpCode = StringUtils.substring(asCode, 3, 2);
            if ((CommonConstants.STR_17.equals(lsSvt) || CommonConstants.STR_67.equals(lsSvt))
                    && tmpCode.equals(CommonConstants.STR_D2)) {
                liRet = 1100;
            }
        }
        return liRet;
    }

    /**
     * 感染症特例加算（1/1000）かどうかを判定する
     * 
     * @param asDate 提供年月日（yyyy/mm/dd）
     * @param asScd  Scode
     * @return long : 0=それ以外／1=感染症特例加算
     */
    public Integer isKansenTokureiKasan(String asDate, String asScd) {
        String lsSvtype;
        String lsSvcode;
        Integer liRet;

        lsSvtype = asScd.substring(0, 2);
        lsSvcode = StringUtils.substring(asScd, 2, 6);

        liRet = 0;

        switch (lsSvtype) {
            case "23": // 施設区分によってコードが異なる
                if ("8300".equals(lsSvcode) || "8303".equals(lsSvcode) || "8304".equals(lsSvcode)) {
                    liRet = 1;
                }
                break;

            case "26": // 施設区分によってコードが異なる
                if ("8301".equals(lsSvcode) || "8302".equals(lsSvcode) || "8303".equals(lsSvcode)) {
                    liRet = 1;
                }
                break;

            case "33":
            case "35": // 通常型と外部型でコードが異なる
                if ("8300".equals(lsSvcode) || "8301".equals(lsSvcode)) {
                    liRet = 1;
                }
                break;

            case "A2":
            case "A6": // 他種別とコードが異なる
                if ("8310".equals(lsSvcode)) {
                    liRet = 1;
                }
                break;

            default:
                if ("8300".equals(lsSvcode)) {
                    liRet = 1;
                }
                break;
        }

        return liRet;
    }

    /**
     * 特定事業所加算V（3/100）かどうかを判定する
     * 
     * @param asDate 提供年月日（yyyy/mm/dd）
     * @param asScd  Scode
     * @return long : 0=それ以外／1=特定事業所加算
     */
    public Integer isTokuteiJigyoKasan(String asDate, String asScd) {
        String lsSvtype;
        String lsSvcode;
        Integer liRet = 0;

        lsSvtype = asScd.substring(0, 2);
        lsSvcode = StringUtils.substring(asScd, 2, 6);

        switch (lsSvtype) {
            case "11":
                if ("6410".equals(lsSvcode)) {
                    liRet = 1;
                }
                break;
        }

        return liRet;
    }

    /**
     * 通所リハ継続減算かどうかを判定する
     * 
     * @param asDate 提供年月日（yyyy/mm/dd）
     * @param asScd  Scode
     * @return long : 0=それ以外／1=通所リハ継続減算
     */
    public long isRihaKeizokuGenzan(String asDate, String asScd) {
        String lsSvtype;
        String lsSvcode;
        long liRet = 0;

        lsSvtype = asScd.substring(0, 2);
        lsSvcode = StringUtils.substring(asScd, 2, 6);

        switch (lsSvtype) {
            case "16", "66":
                if ("6300".equals(lsSvcode)) {
                    liRet = 1;
                }
                break;
        }

        return liRet;
    }

    /**
     * 過少サービス減算かどうかを判定する
     * 
     * @param asDate 提供年月日（yyyy/mm/dd）
     * @param asScd  Scode
     * @return long : 0=それ以外／1=過少サービス減算
     */
    public long isKashouServiceGenzan(String asDate, String asScd) {
        String lsSvtype;
        String lsSvcode;
        long liRet = 0;

        lsSvtype = asScd.substring(0, 2);
        lsSvcode = StringUtils.substring(asScd, 2, 6);

        if (asDate.compareTo("2024/04/01") >= 0) {
            switch (lsSvtype) {
                case "73", "75", "77":
                    if ("8200".equals(lsSvcode) || "8201".equals(lsSvcode)) {
                        liRet = 1;
                    }
                    break;
            }
        }

        return liRet;
    }

    /**
     * サテライト体制未整備減算かどうかを判定する
     * 
     * @param asDate 提供年月日（yyyy/mm/dd）
     * @param asScd  Scode
     * @return long : 0=それ以外／1=サテライト体制未整備減算
     */
    public long isSatelliteMiseibiGenzan(String asDate, String asScd) {
        String lsSvtype;
        String lsSvcode;
        long liRet = 0;

        lsSvtype = asScd.substring(0, 2);
        lsSvcode = StringUtils.substring(asScd, 2, 6);

        if (asDate.compareTo("2024/04/01") >= 0) {
            switch (lsSvtype) {
                case "77":
                    if ("8202".equals(lsSvcode) || "8203".equals(lsSvcode)) {
                        liRet = 1;
                    }
                    break;
            }
        }

        return liRet;
    }

    /**
     * 要介護度の漢字名を返す
     *
     * @param aiYokai 要介護度区分（ＮＤ値）
     * @return 要介護度の漢字名
     */
    public String getYokaiKbnKnj(Integer aiYokai) {
        if (aiYokai <= 0) {
            return "";
        }
        YokaigoJotaiByCriteriaInEntity criteria = new YokaigoJotaiByCriteriaInEntity();
        criteria.setAiYokai(CommonDtoUtil.objValToString(aiYokai));
        List<YokaigoJotaiOutEntity> items = comMscYokaigoSelectMapper.findYokaigoJotaiByCriteria(criteria);
        return items.stream().findFirst().map(YokaigoJotaiOutEntity::getYokaiKnj).orElse("");
    }

    /**
     * 特別地域加算かどうかを判別する（簡易版） 新解釈：％の限度外加算かどうかを判別する 旧：福祉用具貸与以外の特別地域加算かどうかを判別する
     *
     * @param sCode   サービスコード（8桁）
     * @param date    提供年月日（yyyy/mm/dd）
     * @param jigyoId 事業所ID
     * @return 1=特別地域加算である, 0=それ以外
     */
    public Integer getSpecialPlaceEasy(String sCode, String date, Integer jigyoId) {
        // H27改正対応（2015/01/13）
        // f_cmn_is_special_place0_easy
        return getSpecialPlace0Easy(sCode, date, jigyoId);
    }

    /**
     * 特別地域加算かどうかを判別する
     *
     * @param sCode   サービスコード（8桁）
     * @param date    提供年月日（yyyy/mm/dd）
     * @param jigyoId 事業所ID
     * @return 1=特別地域加算である, 0=それ以外
     */
    public Integer getSpecialPlace0Easy(String sCode, String date, Integer jigyoId) {
        Integer result = 0;

        // パラメータチェック
        if (StringUtils.isEmpty(sCode)) {
            return result;
        }

        // サービス種類を取得
        String serviceType = StringUtils.left(sCode, 2);

        // 基本チェック：訪問・通所系サービス
        boolean isTargetService =
                // 11-17, 61-67の範囲チェック
                (serviceType.compareTo("11") >= 0 && serviceType.compareTo("17") <= 0)
                        || (serviceType.compareTo("61") >= 0 && serviceType.compareTo("67") <= 0) ||
                        // その他の個別サービス
                        Arrays.asList("73", "75", "76", "78", "72", "74", "77", "A1", "A2", "A5", "A6")
                                .contains(serviceType);

        // 除外サービスチェック
        boolean isExcludedService = Arrays.asList("13700000", "76610000", "77610000").contains(sCode);

        // 基本判定
        if (isTargetService && !isExcludedService) {
            if (!getChkGendogai(sCode, date, jigyoId)) {
                // 限度額対象外サービスかどうかを判定 f_cmn_sei_ky_chk_gendogai
                result = 1;
            }
        }

        // 2018年4月以降の追加サービス
        if (date.compareTo("2018/04/01") >= 0) {
            if (Arrays.asList("31", "34", "71").contains(serviceType)) {
                if (!getChkGendogai(sCode, date, jigyoId)) {
                    // 限度額対象外サービスかどうかを判定 f_cmn_sei_ky_chk_gendogai
                    result = 1;
                }
            }
        }

        // 2021年4月以降の追加サービス
        if (date.compareTo("2021/04/01") >= 0) {
            if (Arrays.asList("68", "69", "72", "74", "79").contains(serviceType)) {
                if (!getChkGendogai(sCode, date, jigyoId)) {
                    // 限度額対象外サービスかどうかを判定 f_cmn_sei_ky_chk_gendogai
                    result = 1;
                }
            }
        }

        return result;
    }

    /**
     * 限度額対象外サービスかどうかを判定
     *
     * @param sCode   サービスコード（8桁）
     * @param date    提供年月日（yyyy/mm/dd）
     * @param jigyoId 事業所ID
     * @return true=支給限度対象サービス, false=限度額管理対象外サービス
     */
    public boolean getChkGendogai(String sCode, String date, Integer jigyoId) {
        // 初期値：支給限度対象サービス
        boolean result = true;

        // パラメータチェック
        String serviceCode = Objects.toString(sCode, "");
        String serviceDate = Objects.toString(date, "0000/00/00");

        // サービス種類とコードを取得
        String serviceType = StringUtils.left(serviceCode, 2);
        String subCode = StringUtils.substring(serviceCode, 2, 6);

        // 基準日による判定分岐
        if (serviceDate.compareTo("2015/03/31") <= 0) {
            result = seiKychkGendogaiCheckPre2015Services(serviceCode, serviceType, subCode, serviceDate);
        } else {
            result = seiKychkGendogaiCheckPost2015Services(serviceCode, serviceType, subCode, serviceDate, jigyoId);
        }

        return result;
    }

    /**
     * 2015年3月以前のサービス判定
     */
    private boolean seiKychkGendogaiCheckPre2015Services(String serviceCode, String serviceType, String subCode,
            String date) {
        switch (serviceType) {
            case "11": // 訪問介護
                if (Arrays.asList("6271", "6272", "6273", "8000", "8100", "8110").contains(subCode)) {
                    return false;
                }
                break;

            case "12", "62": // 訪問入浴、予防訪問入浴
                if (Arrays.asList("6102", "6103", "6104", "8000", "8100", "8110").contains(subCode)) {
                    return false;
                }
                break;

            case "13": // 訪問看護
                if (date.compareTo("2012/04/01") >= 0) {
                    if (Arrays.asList("3100", "3200", "4000", "4001", "7000", "8000", "8001", "8002", "8100", "8101",
                            "8102", "8110", "8111", "8112").contains(subCode)) {
                        return false;
                    }
                } else {
                    if (Arrays.asList("7000", "8000", "8001", "8002", "8100", "8101", "8102", "8110", "8111", "8112")
                            .contains(subCode)) {
                        return false;
                    }
                }
                break;
            case "14": // 訪問リハ
                if ("8110".equals(subCode)) {
                    return false;
                }

            case "15": // 通所介護
                if (Arrays.asList("6104", "6105", "6106", "8110").contains(subCode)) {
                    return false;
                }
                break;

            case "16": // 通所リハ
                if (Arrays.asList("6103", "6104", "6105", "8110").contains(subCode)) {
                    return false;
                }
                break;

            case "17": // 福祉用具貸与
                if ((subCode.compareTo("8001") >= 0 && subCode.compareTo("8013") <= 0)
                        || (subCode.compareTo("8101") >= 0 && subCode.compareTo("8113") <= 0)
                        || (subCode.compareTo("8201") >= 0 && subCode.compareTo("8213") <= 0)) {
                    return false;
                }
                break;

            case "21": // 短期入所生活
                if (Arrays.asList("6104", "6105", "6106").contains(subCode)) {
                    return false;
                }
                break;

            case "22": // 短期入所老健
                if (Arrays.asList("6000", "6104", "6105", "6106", "9000").contains(subCode)) {
                    return false;
                }
                if (Arrays.asList("25T00000", "25T00010", "25T00020", "25T00030", "25T00040").contains(serviceCode)) {
                    return false;
                }
                break;

            case "26": // 予防短期医療
                if (Arrays.asList("2711", "2712", "2713", "3711", "3712", "3713", "4711", "4712", "4713")
                        .contains(subCode)) {
                    return false;
                }
                break;

            case "27", "28", "33", "35":
                // 予防短期医療
                // 密着特定短期
                // 特定施設入居
                // 予防特定入居
                if (Arrays.asList("6128", "6129", "6130").contains(subCode)) {
                    return false;
                }
                break;

            case "37", "38", "39":
                // 予防認知対応
                // 認知短期利用
                // 予防認知短期
                if (Arrays.asList("6104", "6105", "6106").contains(subCode)) {
                    return false;
                }
                break;

            case "61":
                // 予防訪問介護
                if (Arrays.asList("8000", "8001", "8100", "8101", "8110", "8111").contains(subCode)) {
                    return false;
                }
                if (subCode.compareTo("6271") >= 0 && subCode.compareTo("6276") <= 0) {
                    return false;
                }
                break;

            case "63":
                // 予防訪問看護
                if (date.compareTo("2012/04/01") >= 0) {
                    if (Arrays.asList("3100", "3200", "4000", "4001", "8000", "8100", "8110").contains(subCode)) {
                        return false;
                    }
                } else {
                    if (Arrays.asList("8000", "8100", "8110").contains(subCode)) {
                        return false;
                    }
                }
                break;

            case "64":
                // 予防訪問リハ
                if (Arrays.asList("8110").contains(subCode)) {
                    return false;
                }
                break;

            case "65", "66":
                // 予防通所介護
                // 予防通所リハ
                if (subCode.compareTo("6111") >= 0 && subCode.compareTo("6116") <= 0) {
                    return false;
                }
                if (Arrays.asList("8110", "8111").contains(subCode)) {
                    return false;
                }
                break;

            case "67": // 予防用具貸与
                if ((subCode.compareTo("8001") >= 0 && subCode.compareTo("8013") <= 0)
                        || (subCode.compareTo("8101") >= 0 && subCode.compareTo("8113") <= 0)
                        || (subCode.compareTo("8201") >= 0 && subCode.compareTo("8213") <= 0)) {
                    return false;
                }
                break;

            case "71":
                // 夜間訪問介護
                if (subCode.compareTo("6103") >= 0 && subCode.compareTo("6108") <= 0) {
                    return false;
                }
                break;

            case "72":
                // 認知通所介護

                if (Arrays.asList("6103", "6104", "6105").contains(subCode)) {
                    return false;
                }
                break;

            case "73", "75":
                // 小規居宅介護
                // 予防小規居宅
                if (subCode.compareTo("6104") >= 0 && subCode.compareTo("6109") <= 0) {
                    return false;
                }
                if (Arrays.asList("6139", "6140").contains(subCode)) {
                    return false;
                }
                break;

            case "74":
                // 予防認知通所
                if (Arrays.asList("6103", "6104", "6105").contains(subCode)) {
                    return false;
                }
                break;

            case "76": // 訪問介護看護
                if (subCode.compareTo("6104") >= 0 && subCode.compareTo("6109") <= 0) {
                    return false;
                }
                if (Arrays.asList("3100", "4000", "4001", "6100", "8000", "8001", "8100", "8101", "8110", "8111")
                        .contains(subCode)) {
                    return false;
                }
                break;

            case "77": // 複合型
                if (subCode.compareTo("6104") >= 0 && subCode.compareTo("6109") <= 0) {
                    return false;
                }
                if (Arrays.asList("3100", "4000", "4001", "6100", "6139").contains(subCode)) {
                    return false;
                }
                break;
        }
        return true;
    }

    /**
     * 2015年4月以降のサービス判定
     */
    private boolean seiKychkGendogaiCheckPost2015Services(String serviceCode, String serviceType, String subCode,
            String date, Integer jigyoId) {
        switch (serviceType) {
            case "A3", "A4", "A7", "A8": // 訪問介護
            {
                GendoKanriKbnByCriteriaInEntity criteria = new GendoKanriKbnByCriteriaInEntity();
                criteria.setLsSvt(serviceType);
                criteria.setLsSvcode(subCode);
                criteria.setAsDate(date);
                criteria.setAlSvJigyoId(CommonDtoUtil.objValToString(jigyoId));
                List<GendoKanriKbnOutEntity> items = comMhcItemuseSougouSelectMapper
                        .findGendoKanriKbnByCriteria(criteria);
                if (items != null && items.size() > 0 && items.get(0).getGendo() == 1) {
                    return false;

                }
            }
                break;

            case "A9", "AA", "AB", "AC", "AD", "AE":
                // 総合支援事業 生活支援サービス
                // 全て限度額管理対象外
                return false;

        }

        // 各種加算等の判定
        if (getSpecialPlaceNew(date, serviceCode)) {
            // 特別地域加算 f_cmn_is_special_place_new
            return false;
        }
        if (getChusankanRiyo(serviceCode, date) > 0) {
            // 中山間 f_cmn_is_chusankan_riyo
            return false;
        }
        if (isShoguuKaizenKasan(date, serviceCode) > 0) {
            // 処遇改善加算 f_cmn_is_shoguu_kaizen_kasan
            return false;
        }
        if (teikyouTaiseiKasan(date, serviceCode)) {
            // 提供体制加算 f_cmn_is_teikyou_taisei_kasan
            return false;
        }
        if (getDouituGenzan(date, serviceCode) > 0) {
            // 同一建物減算（率） f_cmn_is_douitu_genzan
            return false;
        }
        if (isDouituTaniGenzan(date, serviceCode) > 0) {
            // 同一建物減算（単位） f_cmn_is_douitu_tani_genzan
            return false;
        }
        if (isKansenKasan(date, serviceCode) > 0) {
            // 感染症等対応加算 f_cmn_is_kansen_kasan
            return false;
        }
        // 上記以外のサービス種類独自の限度外加算
        switch (serviceType) {
            case "13":
                // 訪問看護
                if (Arrays.asList("3100", "3200", "4000", "4001", "7000").contains(subCode))
                    return false;
                break;
            case "17", "67":
                // 訪問看護
                // 福祉用具貸与（※特地加算チェック関数には含まれていない）
                // 予防用具貸与
                if (subCode.compareTo("8001") >= 0 && subCode.compareTo("8013") <= 0)
                    return false;
                break;
            case "22":
                // 訪問看護
                // 短期入所老健
                if (Arrays.asList("6000", "9000").contains(subCode))
                    return false;
                if (Arrays.asList("22T00000", "22T00010", "22T00020", "22T00030", "22T00040").contains(serviceCode))
                    return false;
                break;
            case "25":
                // 予防短期老健
                if (Arrays.asList("6000", "9000").contains(subCode))
                    return false;
                if (Arrays.asList("25T00000", "25T00010", "25T00020", "25T00030", "25T00040").contains(serviceCode))
                    return false;
                break;
            case "63":
                // 予防訪問看護
                if (Arrays.asList("3100", "3200", "4000", "4001").contains(subCode))
                    return false;
                break;
            case "73":
                // 小規模多機能
                if (Arrays.asList("4005", "4010").contains(subCode))
                    return false;
                break;
            case "75":
                // 予防小規居宅
                if (Arrays.asList("4010").contains(subCode))
                    return false;
                break;
            case "76":
                // 訪問介護看護
                if (Arrays.asList("3100", "4000", "4001", "4010", "6100").contains(subCode))
                    return false;
                if (date.compareTo("2018/04/01") >= 0) {
                    if (Arrays.asList("4111", "4112", "4113", "4114").contains(subCode))
                        return false;
                }
                break;
            case "77":
                // 複合型
                if (Arrays.asList("3100", "4000", "4001", "4010", "4015", "6100", "6139", "4005", "4014")
                        .contains(subCode))
                    return false;
                break;
            case "2A":
                // 短期入所医療院
                if (Arrays.asList("6000").contains(subCode))
                    return false;
                if (Arrays.asList("2AT00000", "2AT00010", "2AT00020", "2AT00030", "2AT00040").contains(serviceCode))
                    return false;
                break;
            case "2B":
                // 予防短期医療院
                if (Arrays.asList("6000").contains(subCode))
                    return false;
                if (Arrays.asList("2BT00000", "2BT00010", "2BT00020", "2BT00030", "2BT00040").contains(serviceCode))
                    return false;
                break;
        }

        if (date.compareTo("2024/04/01") >= 0) {
            switch (serviceType) {
                case "73", "75", "77":
                    // 小規模多機能
                    // 予防小規居宅
                    // 複合型
                    if (Arrays.asList("4009").contains(subCode)) {
                        // 小多機能型総合マネジメント加算Ⅰ
                        return false;
                    }
                    break;
                case "76":
                    // 訪問介護看護
                    if (Arrays.asList("3000", "4009").contains(subCode)) {
                        // 定期巡回緊急時訪問看護加算Ⅰ
                        // 定期巡回総合マネジメント体制加算Ⅰ
                        return false;
                    }
                    break;
            }
        }
        if (date.compareTo("2024/06/01") >= 0) {
            switch (serviceType) {
                case "13":
                    // 訪問看護
                    if (Arrays.asList("3001", "3002").contains(subCode)) {
                        // 緊急時訪問看護加算Ⅰ１
                        // 緊急時訪問看護加算Ⅰ２
                        return false;
                    }
                    break;

                case "63":
                    if (Arrays.asList("3001", "3002").contains(subCode)) {
                        // 予防緊急時訪問看護加算Ⅰ１
                        // 予防緊急時訪問看護加算Ⅰ２
                        return false;
                    }
                    break;
            }
        }
        return true;
    }

    /**
     * 特別地域加算かどうかを判定する
     *
     * @param date  提供年月日（yyyy/mm/dd）
     * @param sCode サービスコード
     * @return true=特地加算／false=特地加算ではない
     */
    public boolean getSpecialPlaceNew(String date, String sCode) {
        // パラメータチェック
        String serviceCode = Objects.toString(sCode, "");
        String serviceDate = Objects.toString(date, "0000/00/00");

        // サービス種類とコードを取得
        String serviceType = StringUtils.left(serviceCode, 2);
        String subCode = StringUtils.substring(serviceCode, 2, 6);

        // 2015年3月以前の判定
        if (serviceDate.compareTo("2015/03/31") <= 0) {
            switch (serviceType) {
                case "11": // 訪問介護
                case "12": // 訪問入浴
                case "62": // 予防訪問入浴
                case "63": // 予防訪問看護
                    return "8000".equals(subCode);

                case "13": // 訪問看護
                    return Arrays.asList("8000", "8001", "8002").contains(subCode);

                case "61": // 予防訪問介護
                case "76": // 定期巡回・随時対応型訪問介護看護
                    return Arrays.asList("8000", "8001").contains(subCode);
            }
            return false;
        }

        // 2015年4月以降の判定
        switch (serviceType) {
            case "11": // 訪問介護
            case "12": // 訪問入浴
            case "14": // 訪問リハ
            case "31": // 居宅療養管理指導
            case "34": // 看護小規模多機能型居宅介護
            case "62": // 予防訪問入浴
            case "63": // 予防訪問看護
            case "64": // 予防訪問リハ
                return "8000".equals(subCode);

            case "61": // 予防訪問介護
            case "75": // 予防小規模多機能型居宅介護
            case "77": // 複合型サービス
                return Arrays.asList("8000", "8001").contains(subCode);

            case "73": // 小規模多機能型居宅介護
                return Arrays.asList("8000", "8003").contains(subCode);

            case "71": // 夜間対応型訪問介護
                return Arrays.asList("8000", "8002", "8003").contains(subCode);

            case "13": // 訪問看護
            case "76": // 定期巡回・随時対応型訪問介護看護
            case "A1": // 訪問型サービス（みなし）
            case "A2": // 訪問型サービス（独自）
                return Arrays.asList("8000", "8001", "8002").contains(subCode);
        }

        return false;
    }

    /**
     * 中山間地域等の利用者提供加算かどうかを判別する
     *
     * @param sCode サービスコード(8桁)
     * @param date  対象年月日（yyyy/mm/dd）
     * @return 2=利用者提供加算, 1=小規模事業所加算, 0=対象外
     */
    public Integer getChusankanRiyo(String sCode, String date) {
        // パラメータチェック
        if (StringUtils.isEmpty(sCode)) {
            return 0;
        }

        // サービス種類とコードを取得
        String serviceType = StringUtils.left(sCode, 2);
        String subCode = StringUtils.substring(sCode, 2, 6);

        switch (serviceType) {
            case "11", "12", "62", "63", "31", "34":
                // 訪問介護、訪問入浴、予防訪問入浴、予防訪問看護
                if ("8100".equals(subCode)) {
                    return 1;
                } else if ("8110".equals(subCode)) {
                    return 2;
                }
                break;

            case "13": // 訪問看護
                if (Arrays.asList("8100", "8101", "8102").contains(subCode)) {
                    return 1;
                } else if (Arrays.asList("8110", "8111", "8112").contains(subCode)) {
                    return 2;
                }
                break;

            case "14", "64": // 訪問リハ、予防訪問リハ
                if ("8100".equals(subCode)) {
                    return 1;
                } else if ("8110".equals(subCode)) {
                    return 2;
                }
                break;

            case "15", "16": // 通所介護、通所リハ
                if ("8110".equals(subCode)) {
                    return 2;
                }
                break;

            case "17", "67": // 福祉用具貸与、予防用具貸与
                if (subCode.compareTo("8101") >= 0 && subCode.compareTo("8113") <= 0) {
                    return 1;
                } else if (subCode.compareTo("8201") >= 0 && subCode.compareTo("8213") <= 0) {
                    return 2;
                }
                break;

            case "61", "76": // 予防訪問介護、訪問介護看護
                if (Arrays.asList("8100", "8101").contains(subCode)) {
                    return 1;
                } else if (Arrays.asList("8110", "8111").contains(subCode)) {
                    return 2;
                }
                if ("76".equals(serviceType)) {
                    if ("8102".equals(subCode)) {
                        return 1;
                    } else if ("8112".equals(subCode)) {
                        return 2;
                    }
                }
                break;

            case "65", "66": // 予防通所介護、予防通所リハ
                if (Arrays.asList("8110", "8111").contains(subCode)) {
                    return 2;
                }
                break;

            case "73", "75", "77": // 小規居宅介護、予防小規居宅、複合型
                if (Arrays.asList("6310", "6311").contains(subCode)) {
                    return 2;
                } else if (Arrays.asList("8100", "8101").contains(subCode)) {
                    return 1;
                }
                break;

            case "A1", "A2": // 訪問型（みなし）、訪問型（独自）
                if (Arrays.asList("8100", "8101", "8102").contains(subCode)) {
                    return 1;
                } else if (Arrays.asList("8110", "8111", "8112").contains(subCode)) {
                    return 2;
                }
                break;

            case "A5", "A6": // 通所型（みなし）、通所型（独自）
                if (Arrays.asList("8110", "8111", "8112").contains(subCode)) {
                    return 2;
                }
                break;

            case "71": // 夜間訪問介護
                if (Arrays.asList("8100", "8102", "8103").contains(subCode)) {
                    return 1;
                } else if (Arrays.asList("8110", "8112", "8113").contains(subCode)) {
                    return 2;
                }
                break;

            case "72", "74": // 認知通所介護 予防認知通所
                if ("8110".equals(subCode)) {
                    return 2;
                }
                break;

            case "68", "69", "79": // 小規居宅短期、予防小規短期、複合型短期
                if ("8100".equals(subCode)) {
                    return 1;
                }
                break;

            case "78": // 密着通所介護
                if (Arrays.asList("8110", "8111", "8112").contains(subCode)) {
                    return 2;
                }
                break;

        }

        return 0;
    }

    /**
     * 処遇改善加算かどうかを判定する
     *
     * @param date  サービス提供年月日（yyyy/mm/dd）
     * @param sCode サービスコード
     * @return 0=それ以外 1=処遇改善加算１ 2=処遇改善加算２ 3=処遇改善加算３ 4=処遇改善加算4 5=処遇改善加算5 11=特定処遇改善加算１
     *         12=特定処遇改善加算12 21=ベースアップ等支援加算
     */
    public Integer isShoguuKaizenKasan(String date, String sCode) {
        Integer result = 0;

        // パラメータチェック
        String serviceCode = Objects.toString(sCode, "");
        String serviceDate = Objects.toString(date, "0000/00/00");

        // サービス種類とコードを取得
        String serviceType = StringUtils.left(serviceCode, 2);
        String subCode = StringUtils.substring(serviceCode, 2, 6);

        // 期間による判定処理
        if (serviceDate.compareTo("2012/04/01") >= 0 && serviceDate.compareTo("2015/03/31") <= 0) {
            return check2012Addition(serviceType, subCode);
        } else if (serviceDate.compareTo("2015/04/01") >= 0 && serviceDate.compareTo("2017/03/31") <= 0) {
            return check2015Addition(serviceType, subCode);
        } else if (serviceDate.compareTo("2017/04/01") >= 0 && serviceDate.compareTo("2019/09/30") <= 0) {
            return check2017Addition(serviceType, subCode);
        } else if (serviceDate.compareTo("2019/10/01") >= 0 && serviceDate.compareTo("2022/09/30") <= 0) {
            return check2019Addition(serviceType, subCode);
        } else if (serviceDate.compareTo("2022/10/01") >= 0 && serviceDate.compareTo("2024/05/31") <= 0) {
            return check2022Addition(serviceType, subCode);
        } else if (serviceDate.compareTo("2024/06/01") >= 0) {
            // 2024年6月以降は新システムで判定
            return isShoguuKaizenKasanR6(date, sCode);
        }

        return result;
    }

    /**
     * 2015年3月以前の加算判定
     */
    private Integer check2012Addition(String serviceType, String subCode) {
        // サービス種類別の判定
        switch (serviceType) {
            case "11": // 訪問介護
                switch (subCode) {
                    case "6271":
                        return 1;
                    case "6272":
                        return 2;
                    case "6273":
                        return 3;
                }
                break;

            case "12", "62": // 訪問入浴、予防訪問入浴
                switch (subCode) {
                    case "6102":
                        return 1;
                    case "6103":
                        return 2;
                    case "6104":
                        return 3;
                }
                break;

            case "15", "21", "22", "24", "25", "32", "37", "38", "39", "51", "52", "54":
                switch (subCode) {
                    case "6104":
                        return 1;
                    case "6105":
                        return 2;
                    case "6106":
                        return 3;
                }
                break;

            case "16":
                switch (subCode) {
                    case "6103":
                        return 1;
                    case "6104":
                        return 2;
                    case "6105":
                        return 3;
                }
                break;

            case "23", "26", "53":
                switch (subCode) {
                    case "2711", "3711", "4711":
                        return 1;
                    case "2712", "3712", "4712":
                        return 2;
                    case "2713", "3713", "4713":
                        return 3;
                }
                break;

            case "27", "28", "33", "35", "36":
                switch (subCode) {
                    case "6128":
                        return 1;
                    case "6129":
                        return 2;
                    case "6130":
                        return 3;
                }
                break;

            case "61":
                switch (subCode) {
                    case "6271":
                        return 1;
                    case "6273":
                        return 2;
                    case "6275":
                        return 3;
                }
                break;

            case "65", "66":
                switch (subCode) {
                    case "6111":
                        return 1;
                    case "6113":
                        return 2;
                    case "6115":
                        return 3;
                }
                break;

            case "71":
                switch (subCode) {
                    case "6103":
                        return 1;
                    case "6105":
                        return 2;
                    case "6107":
                        return 3;
                }
                break;

            case "72", "74":
                switch (subCode) {
                    case "6103":
                        return 1;
                    case "6104":
                        return 2;
                    case "6105":
                        return 3;
                }
                break;

            case "73", "75", "76", "77":
                switch (subCode) {
                    case "6104":
                        return 1;
                    case "6106":
                        return 2;
                    case "6108":
                        return 3;
                }
                break;

        }
        return 0;
    }

    /** ls_date >= "2015/04/01" AND ls_date <= "2017/03/31" */
    private Integer check2015Addition(String serviceType, String subCode) {
        // サービス種類別の判定
        switch (serviceType) {
            case "11": // 訪問介護
                switch (subCode) {
                    case "6274":
                        return 1;
                    case "6271":
                        return 2;
                    case "6272":
                        return 3;
                    case "6273":
                        return 4;
                }
                break;

            case "12", "62":
                switch (subCode) {
                    case "6105":
                        return 1;
                    case "6102":
                        return 2;
                    case "6103":
                        return 3;
                    case "6104":
                        return 4;
                }
                break;

            case "15", "21", "22", "24", "25", "32", "37", "38", "39", "78":
                switch (subCode) {
                    case "6107":
                        return 1;
                    case "6104":
                        return 2;
                    case "6105":
                        return 3;
                    case "6106":
                        return 4;
                }
                break;

            case "16":
                switch (subCode) {
                    case "6106":
                        return 1;
                    case "6103":
                        return 2;
                    case "6104":
                        return 3;
                    case "6105":
                        return 4;
                }
                break;

            case "23", "26":
                switch (subCode) {
                    case "2710", "3710", "4710":
                        return 1;
                    case "2711", "3711", "4711":
                        return 2;
                    case "2712", "3712", "4712":
                        return 3;
                    case "2713", "3713", "4713":
                        return 4;
                }
                break;

            case "27":
                switch (subCode) {
                    case "6128":
                        return 1;
                    case "6129":
                        return 2;
                    case "6130":
                        return 3;
                    case "6131":
                        return 4;
                }
                break;

            case "28", "33", "35", "36":
                switch (subCode) {
                    case "6131":
                        return 1;
                    case "6128":
                        return 2;
                    case "6129":
                        return 3;
                    case "6130":
                        return 4;
                }
                break;

            case "61", "A1", "A2":
                switch (subCode) {
                    case "6270":
                        return 1;
                    case "6271":
                        return 2;
                    case "6273":
                        return 3;
                    case "6275":
                        return 4;
                }
                break;

            case "65", "66", "A5", "A6":
                switch (subCode) {
                    case "6110":
                        return 1;
                    case "6111":
                        return 2;
                    case "6113":
                        return 3;
                    case "6115":
                        return 4;
                }
                break;

            case "68", "69":
                switch (subCode) {
                    case "6110":
                        return 1;
                    case "6104":
                        return 2;
                    case "6106":
                        return 3;
                    case "6108":
                        return 4;
                }
                break;

            case "71":
                switch (subCode) {
                    case "6109":
                        return 1;
                    case "6103":
                        return 2;
                    case "6105":
                        return 3;
                    case "6107":
                        return 4;
                }
                break;

            case "72", "74":
                switch (subCode) {
                    case "6106":
                        return 1;
                    case "6103":
                        return 2;
                    case "6104":
                        return 3;
                    case "6105":
                        return 4;
                }
                break;

            case "73", "75":
                switch (subCode) {
                    case "6110":
                        return 1;
                    case "6104":
                        return 2;
                    case "6106":
                        return 3;
                    case "6108":
                        return 4;
                }
                break;

            case "76", "77", "79":
                switch (subCode) {
                    case "6112":
                        return 1;
                    case "6104":
                        return 2;
                    case "6106":
                        return 3;
                    case "6108":
                        return 4;
                }
                break;

        }
        return 0;
    }

    /** ls_date >= "2017/04/01" AND ls_date <= "2019/09/30" */
    private Integer check2017Addition(String serviceType, String subCode) {
        // サービス種類別の判定
        switch (serviceType) {
            case "11": // 訪問介護
                switch (subCode) {
                    case "6271":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6272":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6273":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6274":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6275":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                }
                break;

            case "12", "62": // 12：訪問入浴、62：予防訪問入浴
                switch (subCode) {
                    case "6102":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6103":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6104":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6105":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6106":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                }
                break;

            case "15", "21", "22", "24", "25", "32", "37", "38", "39", "78":
                // 15：通所介護、21：短期入所生活、22：短期入所老健
                // 24：予防短期生活、25：予防短期老健、32：認知症対応型
                // 37：予防認知対応、38：認知短期利用、39：予防認知短期
                // 78：密着通所介護
                switch (subCode) {
                    case "6104":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6105":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6106":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6107":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6108":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                }
                break;

            case "16", "72", "74":
                // 16：通所リハビリ、72：認知通所介護、74：予防認知通所
                switch (subCode) {
                    case "6103":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6104":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6105":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6106":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6107":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                }
                break;

            case "23", "26":
                // 23 ：短期入所医療、26：予防短期医療
                switch (subCode) {
                    case "2711", "3711", "4711":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "2712", "3712", "4712":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "2713", "3713", "4713":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "2710", "3710", "4710":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "2709", "3709", "4709":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                }
                break;

            case "27", "28", "33", "35", "36":
                // 27：特定施設短期
                // 28：密着特定短期、33：特定施設入居、35：予防特定入居
                // 36：密着特定入居
                switch (subCode) {
                    case "6128":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6129":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6130":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6131":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6132":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                }
                break;

            case "61":
                // 61：予防訪問介護
                switch (subCode) {
                    case "6271":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6273":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6275":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6270":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6269":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                }
                break;

            case "65", "66":
                // 65：予防通所介護、66：予防通所リハ
                switch (subCode) {
                    case "6111":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6113":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6115":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6110":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6100":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                }
                break;

            case "68", "69", "73", "75":
                // 68：小規居宅短期、69：予防小規短期
                // 73：小規居宅介護、75：予防小規居宅
                switch (subCode) {
                    case "6104":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6106":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6108":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6110":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6112":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                }
                break;

            case "71":
                // 71：夜間訪問介護
                switch (subCode) {
                    case "6103":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6105":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6107":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6109":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6111":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                }
                break;

            case "76", "77", "79":
                // 76：訪問介護看護、77：複合型、79：複合型短期
                switch (subCode) {
                    case "6104":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6106":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6108":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6112":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6114":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                }
                break;

            case "A1", "A2":
                // --- 訪問型
                switch (subCode) {
                    case "6271":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6273":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6275":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6270":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6269":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                }
                break;

            case "A5", "A6":
                // --- 通所型
                switch (subCode) {
                    case "6111":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6113":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6115":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6110":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6100":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                }
                break;

            case "2A", "2B":
                // 2A：短期入所医療院、2B：予防短期医療院
                switch (subCode) {
                    case "6106":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6107":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6108":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6105":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6104":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                }
                break;

        }
        return 0;
    }

    /** ls_date >= "2019/10/01" AND ls_date <= "2022/09/30" */
    private Integer check2019Addition(String serviceType, String subCode) {
        // サービス種類別の判定
        switch (serviceType) {

            case "11":
                // 11：訪問介護
                switch (subCode) {
                    case "6271":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6272":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6273":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6274":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6275":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6278":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6279":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

            case "12":
                // 12：訪問入浴
                switch (subCode) {
                    case "6102":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6103":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6104":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6105":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6106":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6111":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6112":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

            case "62":
                // 62：予防訪問入浴
                switch (subCode) {
                    case "6102":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6103":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6104":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6105":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6106":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6113":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6114":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

            case "15", "21", "24", "32", "37", "38", "39", "78":
                // 15：通所介護、21：短期入所生活
                // 24：予防短期生活、32：認知症対応型
                // 37：予防認知対応、38：認知短期利用、39：予防認知短期
                // 78：密着通所介護
                switch (subCode) {
                    case "6104":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6105":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6106":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6107":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6108":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6111":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6112":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

            case "22", "25":
                // 22：短期入所老健 25：予防短期老健
                switch (subCode) {
                    case "6104":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6105":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6106":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6107":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6108":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6112":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6113":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

            case "16", "72", "74":
                // 16：通所リハビリ、72：認知通所介護、74：予防認知通所
                switch (subCode) {
                    case "6103":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6104":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6105":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6106":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6107":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6118":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6119":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

            case "23", "26":
                // 23 ：短期入所医療、26：予防短期医療
                switch (subCode) {
                    case "2711", "3711", "4711":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "2712", "3712", "4712":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "2713", "3713", "4713":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "2710", "3710", "4710":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "2709", "3709", "4709":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "2718", "3718", "4718":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "2719", "3719", "4719":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

            case "27", "28", "33", "35", "36":
                // 27：特定施設短期 28：密着特定短期、33：特定施設入居、
                // 35：予防特定入居 36：密着特定入居
                switch (subCode) {
                    case "6128":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6129":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6130":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6131":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6132":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6135":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6136":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

            case "66":
                // 66：予防通所リハ （65：予防通所介護は削除）
                switch (subCode) {
                    case "6111":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6113":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6115":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6110":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6100":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6121":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6122":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

            case "68", "69", "73", "75":
                // 68：小規居宅短期、69：予防小規短期
                // 73：小規居宅介護、75：予防小規居宅
                switch (subCode) {
                    case "6104":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6106":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6108":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6110":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6112":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6118":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6119":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

            case "71":
                // 71：夜間訪問介護
                switch (subCode) {
                    case "6103":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6105":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6107":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6109":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6111":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6118":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6119":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

            case "76", "77", "79":
                // 76：訪問介護看護、77：複合型、79：複合型短期
                switch (subCode) {
                    case "6104":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6106":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6108":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6112":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6114":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6118":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6119":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

            case "A1", "A2":
                // --- 訪問型
                switch (subCode) {
                    case "6271":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6273":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6275":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6270":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6269":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6278":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6279":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

            case "A5", "A6":
                // --- 通所型
                switch (subCode) {
                    case "6111":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6113":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6115":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6110":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6100":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6118":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6119":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

            case "2A", "2B":
                // 2A：短期入所医療院、2B：予防短期医療院
                switch (subCode) {
                    case "6106":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6107":
                        return 4; // 訪問介護処遇改善加算Ⅳ
                    case "6108":
                        return 5; // 訪問介護処遇改善加算Ⅴ
                    case "6105":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6104":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6111":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6112":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                }
                break;

        }
        return 0;
    }

    /** ls_date >= "2022/10/01" AND ls_date <= "2024/05/31" */
    private Integer check2022Addition(String serviceType, String subCode) {
        // サービス種類別の判定
        switch (serviceType) {

            case "11":
                // 11：訪問介護
                switch (subCode) {
                    case "6271":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6274":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6275":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6278":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6279":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6281":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "12":
                // 12：訪問入浴
                switch (subCode) {
                    case "6102":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6105":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6106":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6111":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6112":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6114":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "62":
                // 62：予防訪問入浴
                switch (subCode) {
                    case "6102":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6105":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6106":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6113":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6114":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6115":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "15", "21", "24", "32", "37", "38", "39":
                // 15：通所介護、21：短期入所生活
                // 24：予防短期生活、32：認知症対応型
                // 37：予防認知対応、38：認知短期利用、39：予防認知短期
                switch (subCode) {
                    case "6104":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6107":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6108":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6111":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6112":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6114":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "78":
                // 78：密着通所介護
                switch (subCode) {
                    case "6104":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6107":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6108":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6111":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6112":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6121":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "22", "25":
                // 22：短期入所老健 25：予防短期老健
                switch (subCode) {
                    case "6104":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6107":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6108":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6112":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6113":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6114":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "16":
                // 16：通所リハビリ
                switch (subCode) {
                    case "6103":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6106":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6107":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6118":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6119":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6114":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "72", "74":
                // 72：認知通所介護、74：予防認知通所
                switch (subCode) {
                    case "6103":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6106":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6107":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6118":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6119":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6121":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "23", "26":
                // 23 ：短期入所医療、26：予防短期医療
                switch (subCode) {
                    case "2711", "3711", "4711":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "2710", "3710", "4710":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "2709", "3709", "4709":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "2718", "3718", "4718":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "2719", "3719", "4719":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "2721", "3721", "4721":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "27", "28", "33", "35", "36":
                // 27：特定施設短期 28：密着特定短期、33：特定施設入居、
                // 35：予防特定入居 36：密着特定入居
                switch (subCode) {
                    case "6128":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6131":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6132":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6135":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6136":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6141":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "66":
                // 66：予防通所リハ （65：予防通所介護は削除）
                switch (subCode) {
                    case "6111":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6110":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6100":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6121":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6122":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6114":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "68", "69", "73", "75":
                // 68：小規居宅短期、69：予防小規短期
                // 73：小規居宅介護、75：予防小規居宅
                switch (subCode) {
                    case "6104":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6110":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6112":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6118":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6119":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6121":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "71":
                // 71：夜間訪問介護
                switch (subCode) {
                    case "6103":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6109":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6111":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6118":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6119":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6121":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "76", "77", "79":
                // 76：訪問介護看護、77：複合型、79：複合型短期
                switch (subCode) {
                    case "6104":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6112":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6114":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6118":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6119":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6121":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "A2":
                // --- 訪問型
                switch (subCode) {
                    case "6271":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6270":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6269":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6278":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6279":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6281":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "A6":
                // --- 通所型
                switch (subCode) {
                    case "6111":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6110":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6100":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6118":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6119":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6114":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

            case "2A", "2B":
                // 2A：短期入所医療院、2B：予防短期医療院
                switch (subCode) {
                    case "6106":
                        return 3; // 訪問介護処遇改善加算Ⅲ
                    case "6105":
                        return 2; // 訪問介護処遇改善加算Ⅱ
                    case "6104":
                        return 1; // 訪問介護処遇改善加算Ⅰ
                    case "6111":
                        return 11; // 訪問介護特定処遇改善加算Ⅰ
                    case "6112":
                        return 12; // 訪問介護特定処遇改善加算Ⅱ
                    case "6114":
                        return 21; // 訪問介護ベースアップ等支援加算
                }
                break;

        }
        return 0;
    }

    /**
     * サービス提供体制強化加算かどうかを判定する
     *
     * @param date  提供年月日（yyyy/mm/dd）
     * @param sCode サービスコード
     * @return true=提供体制強化加算である、false=それ以外
     */
    public Boolean teikyouTaiseiKasan(String date, String sCode) {
        // パラメータチェック
        String serviceCode = Objects.toString(sCode, "");
        String serviceDate = Objects.toString(date, "0000/00/00");

        // サービス種類とコードを取得
        String serviceType = StringUtils.left(serviceCode, 2);
        String subCode = StringUtils.substring(serviceCode, 2, 6);

        // H27年3月以前は対象外
        if (serviceDate.compareTo("2015/03/31") <= 0) {
            return false;
        }

        // H27年4月以降の判定
        if (serviceDate.compareTo("2015/04/01") >= 0) {
            switch (serviceType) {
                case "12", "62": // 訪問入浴、予防訪問入浴
                    return Arrays.asList("6100", "6101", "6099").contains(subCode);

                case "14", "63", "64": // 訪問リハ、予防訪問看護、予防訪問リハ
                    return Arrays.asList("6101", "6102").contains(subCode);

                case "15", "21", "22", "24", "25", "33", "35", "36", "27", "28":
                case "32", "37", "38", "39", "68", "69", "73", "75", "2A", "2B":
                    // 通所介護
                    // 短期入所生活
                    // 短期入所老健
                    // 予防短期生活
                    // 予防短期老健
                    // 特定施設
                    // 予防特定施設
                    // 密着特定施設
                    // 特定施設短期
                    // 密着特定短期
                    // 認知症対応型
                    // 予防認知対応
                    // 認知短期利用
                    // 予防認知短期
                    // 小規居宅短期
                    // 予防小規短期
                    // 小規居宅介護
                    // 予防小規居宅
                    // 短期入所医療院
                    // 予防短期医療院
                    return Arrays.asList("6100", "6101", "6102", "6103", "6099").contains(subCode);

                case "78": // 密着通所介護
                    return Arrays.asList("6100", "6101", "6102", "6103", "6099", "6113", "6114", "6123", "6124")
                            .contains(subCode);

                case "13": // 訪問看護
                    return Arrays.asList("6101", "6102", "6103", "6104").contains(subCode);

                case "16", "72", "74": // 通所リハ、認知通所介護、予防認知通所
                    return Arrays.asList("6100", "6101", "6102", "6099").contains(subCode);

                case "23": // 短期入所医療
                    return Arrays.asList("2701", "2702", "2703", "2707", "3700", "3701", "3702", "3703", "4700", "4701",
                            "4702", "4703", "2699", "3699", "4699").contains(subCode);

                case "26": // 予防短期医療
                    return Arrays.asList("2701", "2702", "2703", "2705", "3700", "3701", "3702", "3703", "4700", "4701",
                            "4702", "4703", "2699", "3699", "4699").contains(subCode);

                case "65", "A5": // 予防通所介護、通所型(みなし)
                    return Arrays.asList("6101", "6102", "6103", "6104", "6107", "6108").contains(subCode);

                case "A6": // 通所型（独自）
                    List<String> a6Codes = Arrays.asList("6101", "6102", "6103", "6104", "6107", "6108", "6121", "6122",
                            "6123", "6124", "6127", "6128", "6131", "6132", "6133", "6134", "6137", "6138", "6141",
                            "6142",
                            "6143", "6144", "6147", "6148", "6151", "6152", "6153", "6154", "6157", "6158", "6011",
                            "6012",
                            "6021", "6022", "6031", "6032", "6041", "6042", "6051", "6052");
                    return a6Codes.contains(subCode);

                case "66": // 予防通所リハ
                    return Arrays.asList("6101", "6102", "6103", "6104", "6117", "6118", "6098", "6099")
                            .contains(subCode);

                case "71": // 夜間訪問介護
                    return Arrays.asList("6100", "6101", "6102", "6110", "6112", "6113", "6114", "6115", "6116", "6117")
                            .contains(subCode);

                case "77", "79": // 複合型、複合型短期
                    return Arrays.asList("6101", "6102", "6103", "6111", "6099").contains(subCode);
                case "76": // 訪問介護看護
                    return Arrays.asList("6101", "6102", "6103", "6111", "6099", "6211", "6212", "6213")
                            .contains(subCode);
            }
        }

        return false;
    }

    /**
     * 単位数、限度外の同一建物減算を判定（通所系のみ）
     *
     * @param date  基準日(yyyy/mm/dd)
     * @param sCode サービスコード(8桁)
     * @return 0=該当なし／1=同一建物減算あり
     */
    public Integer isDouituTaniGenzan(String date, String sCode) {
        Integer result = 0;

        // パラメータチェック
        if (StringUtils.isEmpty(sCode)) {
            return result;
        }

        // サービス種類とコードを取得
        String serviceType = StringUtils.left(sCode, 2);
        String serviceCode = StringUtils.substring(sCode, 2, 6);

        // 2021年4月以降の判定
        if (date.compareTo("2021/04/01") >= 0) {
            switch (serviceType) {
                case "15", "16", "72", "74", "78":
                    // 通所系サービス
                    if ("5611".equals(serviceCode)) {
                        result = 1;
                    }
                    break;

                case "66":
                    // 予防通所系
                    if (Arrays.asList("6105", "6106", "6107", "6108", "6119", "6120").contains(serviceCode)) {
                        result = 1;
                    }
                    break;

                case "A6":
                    // 通所型独自サービス
                    if (Arrays.asList("6105", "6106", "6125", "6126", "6135", "6136", "6145", "6146", "6155", "6156")
                            .contains(serviceCode)) {
                        result = 1;
                    }
                    break;
            }
        }

        // 2024年4月以降の判定
        if (date.compareTo("2024/04/01") >= 0) {
            if ("A6".equals(serviceType)) {
                // 通所型独自サービス新規コード
                if (Arrays.asList("6207", "6227", "6237", "6247", "6257").contains(serviceCode)) {
                    result = 1;
                }
            }
        }

        return result;
    }

    /**
     * 感染症等対応加算（3/100）かどうかを判定する
     *
     * @param date  提供年月日（yyyy/mm/dd）
     * @param sCode サービスコード
     * @return 0=それ以外／1=感染症特例加算
     */
    public Integer isKansenKasan(String date, String sCode) {
        if (StringUtils.isEmpty(sCode)) {
            return 0;
        }

        // サービス種類とコードを取得
        String serviceType = StringUtils.left(sCode, 2);
        String serviceCode = StringUtils.substring(sCode, 2, 6);

        // 通所系サービスの感染症加算チェック
        switch (serviceType) {
            case "15": // 通所介護
            case "16": // 通所リハ
            case "72": // 認知症対応型通所介護
            case "74": // 予防認知症対応型通所介護
            case "78": // 地域密着型通所介護
                if ("6600".equals(serviceCode)) {
                    return 1;
                }
                break;
        }
        return 0;
    }

    /**
     * 新処遇改善加算かどうかを判定する
     *
     * @param date  提供年月日（yyyy/mm/dd）
     * @param sCode サービスコード
     * @return 0=それ以外／31:処遇改善加算Ⅰ／32:処遇改善加算Ⅱ／～／48:処遇改善加算Ⅴ14
     */
    public Integer isShoguuKaizenKasanR6(String date, String sCode) {
        Integer result = 0;

        // パラメータチェック
        String serviceCode = Objects.toString(sCode, "");
        String serviceDate = Objects.toString(date, "0000/00/00");

        // 2024年6月以降のみ対象
        if (serviceDate.compareTo("2024/06/01") < 0) {
            return result;
        }

        // サービス種類とコードを取得
        String serviceType = StringUtils.left(serviceCode, 2);
        String subCode = StringUtils.substring(serviceCode, 2, 6);

        // 処遇改善加算Ⅰ～Ⅲの判定
        switch (serviceType) {
            case "11": // 訪問介護
                switch (subCode) {
                    case "6275":
                        result = 31;
                        break; // 処遇改善加算Ⅰ
                    case "6274":
                        result = 32;
                        break; // 処遇改善加算Ⅱ
                    case "6271":
                        result = 33;
                        break; // 処遇改善加算Ⅲ
                }
                break;

            case "12": // 訪問入浴介護
            case "62": // 予防訪問入浴介護
                switch (subCode) {
                    case "6106":
                        result = 31;
                        break; // 処遇改善加算Ⅰ
                    case "6105":
                        result = 32;
                        break; // 処遇改善加算Ⅱ
                    case "6102":
                        result = 33;
                        break; // 処遇改善加算Ⅲ
                }
                break;

            case "15": // 通所介護
            case "21": // 短期入所生活介護
            case "24": // 予防短期入所生活介護
            case "32": // グループホーム
            case "37": // 短期入所生活
            case "38": // 通所介護
            case "39": // 短期入所生活
            case "22":
            case "25":
            case "78":
                switch (subCode) {
                    case "6108":
                        result = 31;
                        break; // 処遇改善加算Ⅰ
                    case "6107":
                        result = 32;
                        break; // 処遇改善加算Ⅱ
                    case "6104":
                        result = 33;
                        break; // 処遇改善加算Ⅲ
                }
                break;
            case "16":
                switch (subCode) {
                    case "6107":
                        result = 31;
                        break; // 処遇改善加算Ⅰ
                    case "6106":
                        result = 32;
                        break; // 処遇改善加算Ⅱ
                    case "6103":
                        result = 33;
                        break; // 処遇改善加算Ⅲ
                }
                break;
            case "23", "26":
                switch (subCode) {
                    case "2709":
                    case "3709":
                        result = 31;
                        break; // 処遇改善加算Ⅰ
                    case "2710":
                    case "3710":
                        result = 32;
                        break; // 処遇改善加算Ⅱ
                    case "2711":
                    case "3711":
                        result = 33;
                        break; // 処遇改善加算Ⅲ
                }
                break;
            case "27", "28", "33", "35", "36":
                switch (subCode) {
                    case "6132":
                        result = 31;
                        break; // 処遇改善加算Ⅰ
                    case "6131":
                        result = 32;
                        break; // 処遇改善加算Ⅱ
                    case "6128":
                        result = 33;
                        break; // 処遇改善加算Ⅲ
                }
                break;
            case "A2":
                switch (subCode) {
                    case "6269":
                        result = 31;
                        break; // 処遇改善加算Ⅰ
                    case "6270":
                        result = 32;
                        break; // 処遇改善加算Ⅱ
                    case "6271":
                        result = 33;
                        break; // 処遇改善加算Ⅲ
                }
                break;
            case "A6", "66":
                switch (subCode) {
                    case "6100":
                        result = 31;
                        break; // 処遇改善加算Ⅰ
                    case "6110":
                        result = 32;
                        break; // 処遇改善加算Ⅱ
                    case "6111":
                        result = 33;
                        break; // 処遇改善加算Ⅲ
                }
                break;
            case "68", "69", "73", "75":
                switch (subCode) {
                    case "6112":
                        result = 31;
                        break; // 処遇改善加算Ⅰ
                    case "6110":
                        result = 32;
                        break; // 処遇改善加算Ⅱ
                    case "6104":
                        result = 33;
                        break; // 処遇改善加算Ⅲ
                }
                break;
            case "71":
                switch (subCode) {
                    case "6111":
                        result = 31;
                        break; // 処遇改善加算Ⅰ
                    case "6109":
                        result = 32;
                        break; // 処遇改善加算Ⅱ
                    case "6103":
                        result = 33;
                        break; // 処遇改善加算Ⅲ
                }
                break;
            case "72", "74":
                switch (subCode) {
                    case "6107":
                        result = 31;
                        break; // 処遇改善加算Ⅰ
                    case "6106":
                        result = 32;
                        break; // 処遇改善加算Ⅱ
                    case "6103":
                        result = 33;
                        break; // 処遇改善加算Ⅲ
                }
                break;
            case "76", "77", "79":
                switch (subCode) {
                    case "6114":
                        result = 31;
                        break; // 処遇改善加算Ⅰ
                    case "6112":
                        result = 32;
                        break; // 処遇改善加算Ⅱ
                    case "6104":
                        result = 33;
                        break; // 処遇改善加算Ⅲ
                }
                break;
            case "2A", "2B":
                switch (subCode) {
                    case "6104":
                        result = 31;
                        break; // 処遇改善加算Ⅰ
                    case "6105":
                        result = 32;
                        break; // 処遇改善加算Ⅱ
                    case "6106":
                        result = 33;
                        break; // 処遇改善加算Ⅲ
                }
                break;
        }

        // 処遇改善加算Ⅳ、Ⅴ1～Ⅴ14の判定
        switch (serviceType) {
            case "23", "26":
                switch (subCode) {
                    case "2680", "3680":
                        result = 34;
                        break;
                    case "2681", "3681":
                        result = 35;
                        break;
                    case "2682", "3682":
                        result = 36;
                        break;
                    case "2683", "3683":
                        result = 37;
                        break;
                    case "2684", "3684":
                        result = 38;
                        break;
                    case "2685", "3685":
                        result = 39;
                        break;
                    case "2686", "3686":
                        result = 40;
                        break;
                    case "2687", "3687":
                        result = 41;
                        break;
                    case "2688", "3688":
                        result = 42;
                        break;
                    case "2689", "3689":
                        result = 43;
                        break;
                    case "2690", "3690":
                        result = 44;
                        break;
                    case "2691", "3691":
                        result = 45;
                        break;
                    case "2692", "3692":
                        result = 46;
                        break;
                    case "2693", "3693":
                        result = 47;
                        break;
                    case "2694", "3694":
                        result = 48;
                        break;
                }
                break;
            case "11", "12", "15", "16", "21", "22", "24", "25", "27", "28", "2A", "2B":
            case "32", "33", "35", "36", "37", "38", "39", "51", "52", "54", "55", "62":
            case "66", "69", "68", "71", "72", "73", "74", "75", "76", "77", "78", "79":
            case "A2", "A6":
                switch (subCode) {
                    case "6380":
                        result = 34;
                        break;
                    case "6381":
                        result = 35;
                        break;
                    case "6382":
                        result = 36;
                        break;
                    case "6383":
                        result = 37;
                        break;
                    case "6384":
                        result = 38;
                        break;
                    case "6385":
                        result = 39;
                        break;
                    case "6386":
                        result = 40;
                        break;
                    case "6387":
                        result = 41;
                        break;
                    case "6388":
                        result = 42;
                        break;
                    case "6389":
                        result = 43;
                        break;
                    case "6390":
                        result = 44;
                        break;
                    case "6391":
                        result = 45;
                        break;
                    case "6392":
                        result = 46;
                        break;
                    case "6393":
                        result = 47;
                        break;
                    case "6394":
                        result = 48;
                        break;
                }
                break;
        }

        return result;
    }

    /**
     * 共生型サービスかどうかを判定する
     *
     * @param date  提供年月日（yyyy/mm/dd）
     * @param sCode サービスコード
     * @return 0=それ以外／1～:共生型サービス
     */
    public Integer getKyoseiGenzan(String date, String sCode) {
        Integer result = 0;

        // NULL チェック
        String serviceCode = Objects.toString(sCode, "");
        String serviceDate = Objects.toString(date, "0000/00/00");

        // H30年4月以降のみ対象
        if (serviceDate.compareTo("2018/04/01") < 0) {
            return result;
        }

        // サービス種類とコードを取得
        String serviceType = StringUtils.left(serviceCode, 2);
        String subCode = StringUtils.substring(serviceCode, 2, 6);

        switch (serviceType) {
            case "11": // 訪問介護
                switch (subCode) {
                    case "6361":
                        result = 1;
                        break;
                    case "6362":
                        result = 2;
                        break;
                    case "6363":
                        result = 3;
                        break;
                }
                break;

            case "15": // 通所介護
            case "78": // 密着通所介護
                switch (subCode) {
                    case "6364":
                        result = 1;
                        break;
                    case "6365":
                        result = 2;
                        break;
                    case "6366":
                        result = 3;
                        break;
                    case "6367":
                        result = 4;
                        break;
                }
                break;

            case "21": // 短期入所生活
            case "24":
                if ("6368".equals(subCode)) {
                    result = 1;
                }
                break;
        }

        return result;
    }

    /**
     * 同一建物減算（率）かどうかを判定する 区分支給限度管理対象外となる同一建物減算（率）のみを判定。
     * 
     * @param date  提供年月日（yyyy/mm/dd）
     * @param sCode サービスコード
     * @return 0=それ以外 1=同一建物減算Ⅰ（訪問介護看護は減算Ⅲ） 2=同一建物減算Ⅱ（訪問介護看護は減算Ⅳ） 4=同一建物減算Ⅲ（訪問介護）
     */
    public Integer getDouituGenzan(String date, String sCode) {
        Integer result = 0;

        // NULL チェック
        String serviceCode = Objects.toString(sCode, "");
        String serviceDate = Objects.toString(date, "0000/00/00");

        // H30年4月以降のみ対象
        if (serviceDate.compareTo("2018/04/01") < 0) {
            return result;
        }

        // サービス種類とコードを取得
        String serviceType = StringUtils.left(serviceCode, 2);
        String subCode = StringUtils.substring(serviceCode, 2, 6);

        switch (serviceType) {
            case "11": // 訪問介護
                switch (subCode) {
                    case "4114":
                        result = 1;
                        break;
                    case "4115":
                        result = 2;
                        break;
                    case "4116":
                        result = 4;
                        break; // 訪問介護同一建物減算３
                }
                break;

            case "12": // 訪問入浴
            case "62": // 予防訪問入浴
            case "13": // 訪問看護
            case "63": // 予防訪問看護
            case "14": // 訪問リハ
            case "64": // 予防訪問リハ
            case "71": // 夜間訪問介護
                switch (subCode) {
                    case "4111":
                        result = 1;
                        break;
                    case "4112":
                        result = 2;
                        break;
                }
                break;

            case "76": // 訪問介護看護
                switch (subCode) {
                    case "4115":
                        result = 1;
                        break; // 定期巡回同一建物減算３
                    case "4117":
                        result = 2;
                        break; // 定期巡回同一建物減算４
                }
                break;

            case "A2": // 訪問型
                switch (subCode) {
                    case "6001":
                        result = 1;
                        break;
                    case "6003":
                        result = 2;
                        break; // 訪問型独自サービス同一建物減算２
                    case "6002":
                        result = 4;
                        break; // 訪問型独自サービス同一建物減算３
                }
                break;
        }

        return result;
    }

    /**
     * サービスの分類名を返す
     *
     * @param serviceType サービスの分類
     * @return サービスの分類名 default：その他
     */
    public String getServiceNameByType(String serviceType) {
        String result = "";

        // 総合事業サービスかどうかをチェック
        if (chkSougouSvtype(serviceType)) {
            // 総合事業サービス
            SvtypeNameByCriteriaInEntity criteria = new SvtypeNameByCriteriaInEntity();
            criteria.setAsType(serviceType);
            List<SvtypeNameOutEntity> items = comMscSvtypeSougouSelectMapper.findSvtypeNameByCriteria(criteria);
            if (items != null) {
                result = items.stream().findFirst().map(SvtypeNameOutEntity::getSvtypeName).orElse("");
            }
        } else {
            // 介護・予防サービス
            KaigoYobouSaabisuByCriteriaInEntity criteria = new KaigoYobouSaabisuByCriteriaInEntity();
            criteria.setAsType(serviceType);
            List<KaigoYobouSaabisuOutEntity> items = comMscSvjigyoNameSelectMapper
                    .findKaigoYobouSaabisuByCriteria(criteria);
            if (items != null) {
                result = items.stream().findFirst().map(KaigoYobouSaabisuOutEntity::getSvJigyo2Knj).orElse("");
            }
        }

        // デフォルト値の設定
        if (StringUtils.isEmpty(result)) {
            result = "その他";
        }

        return result;
    }

    /**
     * サービス種類コードを返す
     * 
     * @param svjId サービス事業所ID
     * @return サービス種類コード、取得できない場合は "00"
     */
    public String getSvType(Integer svjId) {
        String result = "00";
        ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity criteria = new ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity();
        criteria.setAlSvj(svjId);
        List<ComMscSvjigyoNameFirstSvKindCdOutEntity> items = comMscSvjigyoNameFirstSvKindCdSelectMapper
                .findComMscSvjigyoNameFirstSvKindCdByCriteria(criteria);
        if (items != null && items.size() > 0 && items.get(0) != null) {
            result = items.stream().findFirst().map(ComMscSvjigyoNameFirstSvKindCdOutEntity::getSvKindCd).orElse("00");
        }
        return result;
    }

    /**
     * サービス種類コードから総合事業サービス区分を判定する
     *
     * @param svType サービス種類コード（2桁）
     * @return int 0:通常のサービス、1:定率サービス、2:定額サービス
     */
    public int sougouSvKbn(String svType) {
        // 初期化
        int result = 0;

        // nullチェック
        String serviceType = (svType == null) ? "00" : svType;

        // サービス種類による判定
        switch (serviceType) {
            case "A3":
            case "A7":
            case "A9":
            case "AB":
            case "AD":
                // 定率サービス
                result = 1;
                break;

            case "A4":
            case "A8":
            case "AA":
            case "AC":
            case "AE":
                // 定額サービス
                result = 2;
                break;

            default:
                // 通常のサービス
                result = 0;
                break;
        }

        return result;
    }

    /**
     * サービス種類から総合事業かどうかをチェックする
     * 
     * @param svTypeCode サービス種類コード（2桁）
     * @return false:総合事業ではないtrue:総合事業サービスである
     */
    public boolean chkSougouSvtype(String svTypeCode) {
        String normalizedCode = (svTypeCode == null) ? SVTYPE_ZERO_ZERO : svTypeCode;
        return SOUGOU_SVTYPES.contains(normalizedCode);
    }

    /**
     * サービスコードを取得する
     * 
     * @param svJigyoId サービス事業所ID
     * @param itemCode  項目コード
     * @param termId    有効期間ＩＤ
     * @return サービスコード (取得できない場合は空文字列)
     */
    public String getScodeFromItemuse2(Integer svJigyoId, Integer itemCode, Integer termId) {
        // サービスコード
        String scode = StringUtils.EMPTY;
        // 検索条件の設定
        KghFygZengetucopyGetItemcodeByCriteriaInEntity criteria = new KghFygZengetucopyGetItemcodeByCriteriaInEntity();
        criteria.setLlDmy1Jid(CommonDtoUtil.objValToString(svJigyoId));
        criteria.setAlBItemcode(CommonDtoUtil.objValToString(itemCode));
        criteria.setAlBTermid(CommonDtoUtil.objValToString(termId));
        // サービスコードを取得
        List<KghFygZengetucopyGetItemcodeOutEntity> scodeList = comMhcItemuseSelectMapper
                .findKghFygZengetucopyGetItemcodeByCriteria(criteria);
        // NULLチェック
        if (CollectionUtils.isNotEmpty(scodeList)) {
            scode = scodeList.getFirst().getScode();
        }
        return StringUtils.defaultIfEmpty(scode, StringUtils.EMPTY);
    }

    /**
     * サービスコードを取得する
     * 
     * @param svJigyoId サービス事業所ID
     * @param itemCode  項目コード
     * @param termId    有効期間ＩＤ
     * @return サービスコード (取得できない場合は空文字列)
     */
    public String getScodeFromItemuseSougou(Integer svJigyoId, Integer itemCode, Integer termId) {
        // サービスコード
        String scode = StringUtils.EMPTY;
        // 検索条件の設定
        ComMhcItemuseSougouCriteria criteria = new ComMhcItemuseSougouCriteria();
        criteria.createCriteria().andSvJigyoIdEqualTo(svJigyoId).andItemcodeEqualTo(itemCode).andTermidEqualTo(termId);
        // サービスコードを取得
        List<ComMhcItemuseSougou> scodeList = comMhcItemuseSougouMapper.selectByCriteria(criteria);
        // NULLチェック
        if (CollectionUtils.isNotEmpty(scodeList)) {
            scode = scodeList.getFirst().getScode();
        }
        return StringUtils.defaultIfEmpty(scode, StringUtils.EMPTY);
    }

    /**
     * サービス事業者区分を得る
     * 
     * @param svJigyoId サービス事業所ID
     * @return サービス事業者コード
     */
    public String getSvJigyoCd(Integer svJigyoId) {
        // サービス事業者コード
        String svJigyoCd = StringUtils.EMPTY;

        // 検索条件の設定
        SvJigyoCdByCriteriaInEntity svJigyoCdByCriteriaInEntity = new SvJigyoCdByCriteriaInEntity();
        // サービス事業所ID
        svJigyoCdByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(svJigyoId));

        // サービス事業者区分を得る
        List<SvJigyoCdOutEntity> svJigyoCdList = this.comMscSvjigyoSelectMapper
                .findSvJigyoCdByCriteria(svJigyoCdByCriteriaInEntity);

        // NULLチェック
        if (CollectionUtils.isNotEmpty(svJigyoCdList)) {
            svJigyoCd = svJigyoCdList.getFirst().getSvJigyoCd();
        }
        return StringUtils.defaultIfEmpty(svJigyoCd, StringUtils.EMPTY);
    }

    /**
     * そのサービスが諸加算サービスかどうか f_cmn_chk_kasan_service
     * 
     * @param asSvtype    サービス種類
     * @param asScode     サービスコード（８桁）
     * @param asDate      日付
     * @param alSvJigyoId サービス事業所ID
     * @return boolean true:加算, false:通常
     */
    public boolean chkKasanService(String asSvtype, String asScode, String asDate, Integer alSvJigyoId) {

        // 先頭に移動
        if (StringUtils.left(asScode, 2).equals("59") || StringUtils.left(asScode, 2).equals("60")) {
            return true;
        }
        String sCode = StringUtils.mid(asScode, 2, 4);

        // 加算チェック
        // 日付で分岐する
        if (asDate.compareTo("2015/03/31") <= 0) {
            // 2015/03/31以前の処理
            switch (asSvtype) {
                case "11": // 訪問介護
                    switch (sCode) {
                        case "4000", "4001", "4002", "4102", "8000", "8100", "8110", "6271", "6272", "6273": // 潜在バグ改修：2012/09/12
                                                                                                             // masaki-g
                            return true;
                    }
                case "12", "62": // 訪問入浴介護・予防訪問入浴介護
                    switch (sCode) {
                        case "6101", "8000", "8100", "8110", "6102", "6103", "6104":
                            return true;
                    }
                case "13", "63": // 訪問看護・予防訪問看護
                    switch (sCode) {
                        case "3100", "3200", "4000", "4001", "4002", "4003", "4004", "4100", "6101", "6102", "7000",
                                "8000",
                                "8100", "8110", "8001", "8101", "8111", "8002", "8102", "8112":
                            return true;
                    }
                case "14", "64": // 訪問リハビリ・予防訪問リハビリ
                    // なし
                    switch (sCode) {
                        case "4000", "5001", "5002", "8110", "6101":
                            return true;
                    }

                case "15": // 通所介護
                    switch (sCode) {
                        case "5050", "5051", "5052", "5100", "5200", "5301", "5302", "5605", "5606", "5607", "5611",
                                "6101",
                                "6102", "6103", "6104", "6105", "6106", "6109", "8110":
                            return true;
                    }
                case "16": // 通所リハビリ
                    switch (sCode) {
                        case "5100", "5200", "5301", "5302", "5400", "5601", "5602", "5603", "5604", "5605", "5606",
                                "5607",
                                "5610", "5611", "6101", "6102", "6103", "6104", "6105", "6109", "6111", "6112", "6143",
                                "6253",
                                "8110":
                            return true;
                    }
                case "17", "67": // 福祉用具貸与//予防福祉用具貸与
                    // なし：想定対象外 <= やめ : 2008/04/04
                    if ("8".equals(StringUtils.mid(asScode, 2, 1))) {
                        return true;
                    }

                case "31", "34": // 居宅療養管理指導・予防居宅療養管理指導
                    switch (sCode) {
                        case "1101":
                            return true;
                    }
                case "59", "60":
                    return true;
                case "21", "24": // 短期入所生活・予防短期入所生活
                    switch (sCode) {
                        case "6004", "6101", "6102", "6103", "6104", "6105", "6106", "6109", "6113", "6115", "6117",
                                "6119",
                                "6121", "6271", "6272", "6275", "6276", "6277", "6278", "6279", "6280", "6281", "6282",
                                "9200":
                            return true;
                    }
                case "22", "25": // 予防短期入所療養
                    switch (sCode) {
                        case "1920", "6000", "6101", "6102", "6103", "6104", "6105", "6106", "6109", "6110", "6111",
                                "6117",
                                "6121", "6250", "6254", "6255", "6271", "6272", "6275", "6276", "6277", "6278", "6279",
                                "6601",
                                "9000":
                            return true;
                        case "T000":
                            return true;
                    }
                case "23", "26": // 医療系短期入所療養介護
                    switch (sCode) {
                        case "2601", "2602", "2603", "2700", "2701", "2702", "2703", "2704", "2705", "2706", "2711",
                                "2712",
                                "2713", "2771", "2772", "2775", "2776", "2777", "2920":
                        case "3600", "3601", "3602", "3701", "3702", "3703", "3704", "3705", "3706", "3711", "3712",
                                "3713",
                                "3750", "3751", "3771", "3772", "3775", "3920":
                        case "4701", "4702", "4703", "4711", "4712", "4713", "4750", "4751", "4771", "4772", "4775",
                                "4920":
                        case "5771", "5772", "5775", "5920", "6920":
                            return true;
                    }

                    // 予防追加 Kubo ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
                case "61": // 予防訪問介護
                    switch (sCode) {
                        case "4001", "4002", "6271", "6272", "6273", "6274", "6275", "6276", "8000", "8001", "8100",
                                "8101",
                                "8110", "8111":
                            return true;
                    }
                case "65", "66": // 予防通所介護・予防通所リハ
                    switch (sCode) {
                        case "5001", "5002", "5003", "5004", "5005", "5006", "5007", "5008", "5009", "5010":
                        case "6101", "6102", "6103", "6104", "6105", "6106", "6107", "6108", "6109":
                        case "6111", "6112", "6113", "6114", "6115", "6116", "8110", "8111":
                            return true;
                    }

                case "32", "37": // 認知症対応型共同生活介護・予防認知症対応型共同生活介護
                    switch (sCode) {
                        case "1550", "1600", "6101", "6102", "6103", "6109", "6133", "6134", "6141", "6142", "6502":
                        case "6104", "6105", "6106", "6143", "6144", "6151":
                            return true;
                    }
                case "33": // 特定施設入居者生活介護
                    switch (sCode) {
                        case "1160", "6003", "6123", "6124", "6125", "6126", "6127", "6128", "6129", "6130":
                            return true;
                    }
                case "35": // 予防特定施設入居者生活介護
                    switch (sCode) {
                        case "1722", "1723", "1724", "1725", "1726", "1727", "1728": // BugFix：2012/10/23 masaki-g
                        case "1821", "1822", "1823", "1824", "1825", "1826", "1827":
                        case "2141", "2142", "2143", "6003", "6123", "6124", "6128", "6129", "6130":
                            return true;
                    }

                    // 地域密着型追加 Kubo(2006/03/18)
                case "36": // 地域密着型特定施設入居者生活介護
                    switch (sCode) {
                        case "2000", "6003", "6123", "6125", "6126", "6127", "6128", "6129", "6130":
                            return true;
                    }
                case "38", "39": // 認知症対応型短期入所・予防認知症対応型短期入所
                    switch (sCode) {
                        case "1600", "6101", "6102", "6103", "6104", "6105", "6106", "6109", "6121", "6133", "6134",
                                "6141",
                                "6151", "6502":
                            return true;
                    }
                case "71": // 夜間訪問介護
                    switch (sCode) {
                        case "6101", "6102", "6136":
                        case "6103", "6104", "6105", "6106", "6107", "6108":
                        case "7001", "7002", "7003", "7004", "7005", "7006":
                        case "7101", "7102", "7103", "7104", "7105", "7106":
                        case "7111", "7112", "7113", "7114", "7115", "7116":
                        case "7201", "7202", "7203", "7204", "7205", "7206", "7207", "7208", "7209", "7210", "7211",
                                "7212":
                        case "7301", "7302", "7303", "7304", "7305", "7306", "7307", "7308", "7309", "7310", "7311",
                                "7312":
                            return true;

                    }

                case "72", "74": // 認知症対応型通所介護・予防認知症対応型通所介護
                    switch (sCode) {
                        case "5050", "5301", "5606", "5607", "5611", "6101", "6102", "6109", "6103", "6104", "6105":
                            return true;
                    }
                case "73", "75": // 小規模多機能型居宅介護・予防小規模多機能型居宅介護
                    switch (sCode) {
                        case "6101", "6102", "6103", "6128", "6129", "6137", "6138", "6139", "6140", "6300":
                        case "6104", "6105", "6106", "6107", "6108", "6109":
                        case "7001", "7002", "7003", "7004", "7005", "7006", "7007", "7008", "7009", "7010", "7011",
                                "7012",
                                "7013", "7014", "7015", "7016", "7017", "7018", "7019", "7020":
                            // 7011-7016 までが不正だった : 2009/04/01
                            return true;
                        default:
                            switch (StringUtils.mid(asScode, 2, 3)) {
                                case "710", "711", "712", "713", "714":
                                    return true;

                            }

                    }
                case "76": // 定期巡回・随時対応（訪問介護看護）
                    switch (StringUtils.mid(asScode, 2, 3)) {
                        case "410", "800", "810", "811", "310", "400", "610", "710", "711", "712":
                            return true;
                        default:
                            // 潜在バグ改修：2012/09/12 masaki-g
                            switch (sCode) {
                                case "4110":
                                    return true;
                            }

                    }
                case "77": // 複合型サービス
                    // 2014.05.12 K.Oomiya(JNET) DEL/ADD END サービスコード英数字化
                    switch (StringUtils.mid(asScode, 2, 3)) {
                        case "600", "601", "610", "400", "630", "612", "710", "711", "712", "713", "714":
                            return true;
                        default:
                            // 潜在バグ改修：2012/09/12 masaki-g
                            switch (sCode) {
                                case "3100", "6139":
                                    return true;
                            }
                    }
                case "27": // 特定施設短期
                    switch (sCode) {
                        case "1160", "6128", "6129", "6130":
                            return true;
                    }
                case "28": // 地域密着型特定施設短期
                    switch (sCode) {
                        case "2000", "6128", "6129", "6130":
                            return true;
                    }
            }
        } else {
            // 2015/04/01以降の処理
            switch (asSvtype) {
                case "A3", "A4", "A7", "A8", "A9", "AA", "AB", "AC", "AD", "AE":
                    // 総合事業サービスの場合はマスタテーブルから取得する
                    AdderServiceKasanFlgByCriteriaInEntity in = new AdderServiceKasanFlgByCriteriaInEntity();
                    in.setLsSvt(asSvtype);
                    in.setLsSvcode(sCode);
                    in.setAlSvJigyoId(String.valueOf(alSvJigyoId));
                    in.setAsd(asDate);
                    List<AdderServiceKasanFlgOutEntity> items = comMhcItemuseSougouSelectMapper
                            .findAdderServiceKasanFlgByCriteria(in);
                    Integer li_kasan_flg = items.stream().findFirst().map(AdderServiceKasanFlgOutEntity::getKasanFlg)
                            .orElse(0);
                    if (li_kasan_flg.equals(1)) {
                        return true;
                    }
                    break;

                default:
                    // 上記以外のサービスの場合
                    if (getSpecialPlaceNew(asDate, asScode)) { // f_cmn_is_special_place_new
                        // 特別地域加算
                        return true;
                    }
                    if (getChusankanRiyo(asScode, asDate) > 0) { // f_cmn_is_chusankan_riyo
                        // 中山間
                        return true;
                    }
                    if (isShoguuKaizenKasan(asDate, asScode) > 0) { // f_cmn_is_shoguu_kaizen_kasan
                        // 処遇改善加算
                        return true;
                    }
                    if (teikyouTaiseiKasan(asDate, asScode)) { // f_cmn_is_teikyou_taisei_kasan
                        // 提供体制加算
                        return true;
                    }
                    if (checkAdderService(asScode, asDate, alSvJigyoId).equals(999)) { // f_cmn_chk_adder_service
                        // その他の加算
                        return true;
                    }
                    // G3S39L123_H30介護_ケアマネ_利用票 同一建物減算、共生型サービスを追加
                    if (getDouituGenzan(asDate, asScode) > 0) { // f_cmn_is_douitu_genzan
                        // 同一建物減算
                        return true;
                    }
                    if (getKyoseiGenzan(asDate, asScode) > 0) { // f_cmn_is_kyosei_genzan
                        // 共生型サービス
                        return true;
                    }
                    // 2021/01/29 Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                    if (isKansenKasan(asDate, asScode) > 0) { // f_cmn_is_kansen_kasan
                        // 感染症等対応加算
                        return true;
                    }
                    if (isKansenTokureiKasan(asDate, asScode) > 0) { // f_cmn_is_kansen_tokurei_kasan
                        // 感染症特例加算
                        return true;
                    }
                    // 2021/02/12 Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_特定事業所加算Ⅴ
                    if (isTokuteiJigyoKasan(asDate, asScode) > 0) { // f_cmn_is_tokutei_jigyo_kasan
                        // 特定事業所加算V
                        return true;
                    }
                    // 2021/02/16 Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_通所リハ継続減算
                    if (isRihaKeizokuGenzan(asDate, asScode) > 0) { // f_cmn_is_riha_keizoku_genzan
                        // 通所リハ継続減算
                        return true;
                    }
                    // 2024/03/15 MOD START Ken.Matsuda G3C45L144_202404改正_ケアマネ_利用票関連_別表計算_4月版_共通関数
                    if (isGyakutaiBousiGenzan(asDate, asScode) > 0) { // f_cmn_is_gyakutai_bousi_genzan
                        // 高齢者虐待防止措置未実施減算
                        return true;
                    }
                    if (isGyomuKeizokuGenzan(asDate, asScode) > 0) { // f_cmn_is_gyomu_keizoku_genzan
                        // 業務継続計画未策定減算
                        return true;
                    }
                    if (isKashouServiceGenzan(asDate, asScode) > 0) { // f_cmn_is_kashou_service_genzan
                        // 過少サービス減算
                        return true;
                    }
                    if (isSatelliteMiseibiGenzan(asDate, asScode) > 0) { // f_cmn_is_satellite_miseibi_genzan
                        // サテライト体制未整備減算
                        return true;
                    }
                    if (isShintaiKousokuGenzan(asDate, asScode) > 0) { // f_cmn_is_shintai_kousoku_genzan
                        // 身体拘束廃止未実施減算
                        return true;
                    }
                    // 上記以外の独自加算
                    switch (asSvtype) {
                        case "17", "67":
                            // 福祉用具貸与
                            // 予防用具貸与
                            if ("8".equals(StringUtils.mid(asScode, 2, 1))) {
                                return true;
                            }
                    }

            }

        }
        return false;
    }

    /**
     * 加算サービスかどうかを判定 f_cmn_chk_adder_service
     * 
     * @param asv         サービスコード（８桁）
     * @param asd         処理年月（yyyy/mm/dd）
     * @param alSvJigyoId サービス事業所ID
     * @return long 999: 加算サービス（特別地域加算を含まない）, 0: その他のサービス
     */
    public Integer checkAdderService(String asv, String asd, Integer alSvJigyoId) {
        if (StringUtils.isEmpty(asv)) {
            return -1;
        }

        Integer liRet = 0;
        String lsSvt = StringUtils.left(asv, 2);
        String lsSvcode = StringUtils.substring(asv, 2, 6);

        if (asd.compareTo("2003/03/31") <= 0) {
            // 法改正前
            if (Arrays
                    .asList("13310000", "13320000", "13400000", "13700000", "15505000", "15510000", "15520000",
                            "15530100", "15530200", "16510000", "16520000", "16530100", "16530200", "16540000",
                            "21920000", "22192000", "22900000", "22T00000", "22T00010", "22T00020", "22T00030",
                            "22T00040", "23260100", "23260200", "23260300", "23270000", "23292000", "23360100",
                            "23360200", "23392000", "23492000", "23592000", "23692000")
                    .contains(asv) || Arrays.asList("59", "60").contains(lsSvt)) {
                liRet = 999;
            }
        } else if (asd.compareTo("2003/04/01") >= 0 && asd.compareTo("2005/09/30") <= 0) {
            // 法改正後
            if (Arrays
                    .asList("13310000", "13320000", "13400000", "13700000", "15505000", "15510000", "15520000",
                            "15530100", "15530200", "16510000", "16520000", "16530100", "16530200", "16540000",
                            /* add */"16550100", "16550200", "21920000", "22192000", "22900000", "22T00000", "22T00010",
                            "22T00020", "22T00030", "22T00040", "23260100", "23260200", "23260300", "23270000",
                            "23292000", "23360100", "23360200", "23392000", "23492000", "23592000", "23692000")
                    .contains(asv) || Arrays.asList("59", "60").contains(lsSvt)) {
                liRet = 999;
            }
        } else if (asd.compareTo("2005/10/01") >= 0 && asd.compareTo("2006/03/31") <= 0) {
            // 2005/10/01 法改正後
            if (Arrays.asList("13310000", "13320000", "13400000", "13700000", "15505000", "15510000", "15520000",
                    "15530100", "15530200", "16510000", "16520000", "16530100", "16530200", "16540000", "16550100",
                    "16550200", "21920000", "22192000", "22900000", "22T00000", "22T00010", "22T00020", "22T00030",
                    "22T00040", "23260100", "23260200", "23260300", "23270000", "23292000", "23360100", "23360200",
                    "23392000", "23492000", "23592000", /* add */"21627100", "21627200", "21627500", "22627100",
                    "22627200", "22627500", "23277100", "23277200", "23277500", "23377100", "23377200", "23377500",
                    "23477100", "23477200", "23477500", "23577100", "23577200", "23577500", /* add */"60000300",
                    "60000400", "60001000", "60002000", "60002200", "60002700", "60002900", "60003600", "60003700",
                    "60004000", "60004100", "60004200", "60004300", "60004400", "60004500", "60004600", "60004700")
                    .contains(asv) || Arrays.asList("59", "60").contains(lsSvt)) {
                liRet = 999;
            }
        } else if (asd.compareTo("2006/04/01") >= 0 && asd.compareTo("2009/03/31") <= 0) {
            // 2006/04/01 法改正後
            if (Arrays.asList("13310000", "13320000", "13400000", "13700000", "15505000", "15510000", "15520000",
                    "15530100", "15530200", "16510000", "16520000", "16530100", "16530200", "16540000", "16550100",
                    "16550200", "21920000", "22192000", "22900000", "22T00000", "22T00010", "22T00020", "22T00030",
                    "22T00040", "23260100", "23260200", "23260300", "23270000", "23292000", "23360100", "23360200",
                    "23392000", "23492000", "23592000", /* add */"21627100", "21627200", "21627500", "22627100",
                    "22627200", "22627500", "23277100", "23277200", "23277500", "23377100", "23377200", "23377500",
                    "23477100", "23477200", "23477500", "23577100", "23577200", "23577500", /* add */"60000300",
                    "60000400", "60001000", "60002000", "60002200", "60002700", "60002900", "60003600", "60003700",
                    "60004000", "60004100", "60004200", "60004300", "60004400", "60004500", "60004600", "60004700",
                    /* add */"14500100", "14500200", "15560700", "15560500", "15560600", "16560100", "16560200",
                    "16560300", "16560400", "16560500", "16560600", "16560700", "21627600", "21627700", "21627800",
                    "21627900", "22627600", "23277600", "23375000", "23475000", "63310000", "63320000", "63400000",
                    "64500100", "65500100", "65500200", "65500300", "65500400", "65500500", "66500200", "66500300",
                    "66500400", "66500500", "24920000", "24627100", "24627200", "24627500", "25192000", "25627100",
                    "25627200", "25627500", "25900000", /* add */"25T00000", "25T00010", "25T00020", "25T00030",
                    "25T00040", "26292000", "26277100", "26277200", "26277500", "26360100", "26360200", "26392000",
                    "26377100", "26377200", "26377500", "26492000", "26477100", "26477200", "26477500", "26592000",
                    "26577100", "26577200", "26577500", "72505000", "72530100", "72560600", "72560700", "73630000",
                    /* add */"74505000", "74530100", "74560600", "74560700", "75630000", "33116000", "36200000",
                    "32155000", "32160000", "38160000", "37155000", "35172200", "35172300", "35172400", "35182100",
                    "35182200", "35182300", "35214100", "35214200", "35214300", "22660100", "23360000", "25660100",
                    "26360000").contains(asv) || Arrays.asList("59", "60").contains(lsSvt)) {
                liRet = 999;
            }
        } else if (asd.compareTo("2009/04/01") >= 0 && asd.compareTo("2012/03/31") <= 0) {
            // 2009/04/01 法改正後
            if (Arrays.asList("11400000", "11400100", "12610100", "13310000", "13320000", "13400000", "13610100",
                    "13700000", "14500100", "14500200", "14610100", "15505000", "15505100", "15530100", "15560500",
                    "15560600", "15610100", "15610200", "15610300", "15610900", "16530100", "16540000", "16560100",
                    "16560200", "16560300", "16560500", "16560600", "16610100", "16610200", "16610900", "16611100",
                    "16614300", "16625300", "21600400", "21610100", "21610200", "21610300", "21610900", "21611300",
                    "21611500", "21611700", "21611900", "21612100", "21627500", "21627600", "21627700", "21627800",
                    "21627900", "21628000", "21920000", "22192000", "22900000", "22T00000", "22T00010", "22T00020",
                    "22T00030", "22T00040", "22610100", "22610200", "22610300", "22610900", "22611000", "22611100",
                    "22611700", "22612100", "22625000", "22625400", "22627500", "22627600", "22660100", "23260100",
                    "23260300", "23270000", "23270100", "23270200", "23270300", "23270400", "23270500", "23270600",
                    "23277500", "23277600", "23292000", "23360000", "23360200", "23370100", "23370200", "23370300",
                    "23370400", "23370500", "23370600", "23375000", "23377500", "23392000", "23470100", "23470200",
                    "23470300", "23475000", "23477500", "23492000", "24600400", "24610100", "24610200", "24610300",
                    "24610900", "24612100", "24627500", "24920000", "25192000", "25900000", "25T00000", "25T00010",
                    "25T00020", "25T00030", "25T00040", "25610100", "25610200", "25610300", "25610900", "25611100",
                    "25611700", "25612100", "25625000", "25627500", "25660100", "26260100", "26260300", "26270000",
                    "26270100", "26270200", "26270300", "26270400", "26270600", "26277500", "26292000", "26360000",
                    "26360200", "26370100", "26370200", "26370300", "26370400", "26370600", "26377500", "26392000",
                    "26470100", "26470200", "26470300", "26477500", "26492000", "31110100", "32155000", "32160000",
                    "32610100", "32610200", "32610300", "32610900", "32613300", "32613400", "32614100", "32614200",
                    "32650200", "33116000", "33600300", "33612300", "33612400", "34110100", "35172200", "35172300",
                    "35172400", "35182100", "35182200", "35182300", "35214100", "35214200", "35214300", "35600300",
                    "35612300", "35612400", "36200000", "36600300", "36612300", "37155000", "37610100", "37610200",
                    "37610300", "37610900", "37613300", "37613400", "37614100", "37650200", "38160000", "38610100",
                    "38610200", "38610300", "38610900", "38612100", "38613300", "38613400", "38614100", "38650200",
                    "39610100", "39610200", "39610300", "39610900", "39612100", "39613300", "39613400", "39614100",
                    "39650200", "61400100", "62610100", "63310000", "63320000", "63400000", "63610100", "64500100",
                    "64610100", "65500100", "65500200", "65500300", "65500400", "65500500", "65610100", "65610200",
                    "65610300", "65610400", "65610900", "66500200", "66500300", "66500400", "66500500", "66610100",
                    "66610200", "66610300", "66610400", "66610900", "71610100", "71610200", "71613600", "71700100",
                    "71700200", "71700300", "71700400", "71700500", "71700600", "71710100", "71710200", "71710300",
                    "71710400", "71710500", "71710600", "71711100", "71711200", "71711300", "71711400", "71711500",
                    "71711600", "72505000", "72530100", "72560600", "72560700", "72610100", "72610200", "72610900",
                    "73610100", "73610200", "73610300", "73612800", "73612900", "73613700", "73613800", "73613900",
                    "73614000", "73630000", "73700100", "73700200", "73700300", "73700400", "73700500", "73700600",
                    "73700700", "73700800", "73700900", "73701000", "73701100", "73701200", "73701300", "73701400",
                    "73701500", "73701600", "73701700", "73701800", "73701900", "73702000", "74505000", "74530100",
                    "74560600", "74560700", "74610100", "74610200", "74610900", "75610100", "75610200", "75610300",
                    "75613900", "75614000", "75630000").contains(asv) || Arrays.asList("59", "60").contains(lsSvt)) {
                liRet = 999;
            }
        } else if (asd.compareTo("2012/04/01") >= 0 && asd.compareTo("2015/03/31") <= 0) {
            // H27改正対応
            switch (lsSvt) {
                case "11":
                    if (Arrays.asList("4000", "4001", "4002").contains(lsSvcode)) {
                        liRet = 999;
                    }
                case "12":
                    if ("6101".equals(lsSvcode)) {
                        liRet = 999;
                    }
                case "13":
                    if (Arrays
                            .asList("3100", "3200", "4000", "4001", "4002", "4003", "4004", "4100", "6101", "6102",
                                    "7000")
                            .contains(lsSvcode)) {
                        liRet = 999;
                    }
                case "14":
                    if (Arrays.asList("4000", "5001", "5002", "6101").contains(lsSvcode)) {
                        liRet = 999;
                    }
                case "15":
                    if (Arrays.asList("5051", "5052", "5301", "5605", "5606", "5611", "6101", "6102", "6103", "6109")
                            .contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "16":
                    if (Arrays.asList("5301", "5400", "5601", "5602", "5603", "5605", "5606", "5610", "5611", "6101",
                            "6102", "6109", "6111", "6112", "6143", "6253").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "21":
                    if (Arrays.asList("6004", "6101", "6102", "6103", "6109", "6113", "6115", "6117", "6119", "6121",
                            "6275", "6276", "6277", "6278", "6279", "6280", "6281", "6282", "9200")
                            .contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "22":

                    if (Arrays.asList("1920", "6000", "6101", "6102", "6103", "6109", "6110", "6111", "6117", "6121",
                            "6250", "6254", "6277", "6278", "6279", "6601", "6275", "6276", "9000", "22T00000",
                            "22T00010",
                            "22T00020", "22T00030", "22T00040").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "23":

                    if (Arrays
                            .asList("2601", "2603", "2700", "2701", "2702", "2703", "2704", "2705", "2706", "2775",
                                    "2776",
                                    "2777", "2920", "3600", "3602", "3701", "3702", "3703", "3704", "3705", "3706",
                                    "3750",
                                    "3751", "3775", "3920", "4701", "4702", "4703", "4750", "4751", "4775", "4920")
                            .contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "24":

                    if (Arrays.asList("6004", "6101", "6102", "6103", "6109", "6121", "6275", "9200")
                            .contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "25":
                    if (Arrays
                            .asList("1920", "6000", "6101", "6102", "6103", "6109", "6111", "6117", "6121", "6250",
                                    "6275",
                                    "6601", "9000", "25T00000", "25T00010", "25T00020", "25T00030", "25T00040")
                            .contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "26":

                    if (Arrays.asList("2601", "2603", "2700", "2701", "2702", "2703", "2704", "2706", "2775", "2920",
                            "3600", "3602", "3701", "3702", "3703", "3704", "3706", "3775", "3920", "4701", "4702",
                            "4703",
                            "4775", "4920").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "27":
                    if (Arrays.asList("1160").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "28":
                    if (Arrays.asList("2000").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "32":

                    if (Arrays.asList("1550", "1600", "6101", "6102", "6103", "6109", "6133", "6134", "6141", "6142",
                            "6143", "6144", "6151", "6502").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "33":
                    if (Arrays.asList("1160", "6003", "6123", "6124", "6125", "6126", "6127").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "34":
                    if (Arrays.asList("1101").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "35":
                    if (Arrays
                            .asList("1722", "1723", "1724", "1725", "1726", "1727", "1728", "1821", "1822", "1823",
                                    "1824",
                                    "1825", "1826", "1827", "2141", "2142", "2143", "6003", "6123", "6124")
                            .contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "36":

                    if (Arrays.asList("2000", "6003", "6123", "6124", "6125", "6126", "6127").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "37":
                    if (Arrays.asList("1550", "6101", "6102", "6103", "6109", "6133", "6134", "6141", "6151", "6502")
                            .contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "38":

                    if (Arrays.asList("1600", "6101", "6102", "6103", "6109", "6121", "6141", "6151")
                            .contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "39":

                    if (Arrays.asList("6101", "6102", "6103", "6109", "6121", "6141", "6151").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "59", "60":
                    liRet = 999;
                case "61":
                    if (Arrays.asList("4001", "4002").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "62":
                    if (Arrays.asList("6101").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "63":

                    if (Arrays.asList("3100", "3200", "4000", "4001", "4002", "4003", "6101").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "64":
                    if (Arrays.asList("4000", "5001", "6101").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "65":

                    if (Arrays.asList("5002", "5003", "5004", "5005", "5006", "5007", "5008", "5009", "5010", "6101",
                            "6102", "6103", "6104", "6105", "6106", "6109").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "66":
                    if (Arrays.asList("5002", "5003", "5004", "5005", "5006", "5007", "5008", "5009", "6101", "6102",
                            "6103", "6104", "6105", "6106", "6107", "6108", "6109").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "71":
                    if (Arrays.asList("6101", "6102", "6136", "7201", "7202", "7203", "7204", "7205", "7206", "7207",
                            "7208", "7209", "7210", "7211", "7212", "7301", "7302", "7303", "7304", "7305", "7306",
                            "7307",
                            "7308", "7309", "7310", "7311", "7312").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "72":

                    if (Arrays.asList("5050", "5301", "5606", "5607", "5611", "6101", "6102", "6109")
                            .contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "73":
                    if (Arrays.asList("6101", "6102", "6103", "6128", "6129", "6137", "6138", "6139", "6140", "6300")
                            .contains(lsSvcode) || (lsSvcode.compareTo("7001") >= 0 && lsSvcode.compareTo("7020") <= 0)
                            || (lsSvcode.compareTo("7101") >= 0 && lsSvcode.compareTo("7140") <= 0)) {

                        liRet = 999;
                    }
                case "74":

                    if (Arrays.asList("5050", "5301", "5606", "5607", "5611", "6101", "6102", "6109")
                            .contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "75":
                    if (Arrays.asList("6101", "6102", "6103", "6139", "6140", "6300").contains(lsSvcode)) {

                        liRet = 999;
                    }
                case "76":

                    if (Arrays.asList("3100", "4000", "4001", "4002", "4003", "6100", "6101", "6102", "6103")
                            .contains(lsSvcode) || (lsSvcode.compareTo("4101") >= 0 && lsSvcode.compareTo("4110") <= 0)
                            || (lsSvcode.compareTo("7101") >= 0 && lsSvcode.compareTo("7120") <= 0)) {

                        liRet = 999;
                    }
                case "77":
                    if (Arrays
                            .asList("3100", "4000", "4001", "4003", "6100", "6101", "6102", "6103", "6128", "6129",
                                    "6139",
                                    "6300")
                            .contains(lsSvcode) || (lsSvcode.compareTo("6001") >= 0 && lsSvcode.compareTo("6015") <= 0)
                            || (lsSvcode.compareTo("7101") >= 0 && lsSvcode.compareTo("7140") <= 0)) {

                        liRet = 999;
                    }
            }
        } else if (asd.compareTo("2015/04/01") >= 0) {
            // H27法改正後
            switch (lsSvt) {
                case "A3", "A4", "A7", "A8", "A9", "AA", "AB", "AC", "AD", "AE": {
                    // 総合事業サービスの場合はマスタテーブルから取得する
                    AdderServiceKasanFlgByCriteriaInEntity in = new AdderServiceKasanFlgByCriteriaInEntity();
                    in.setLsSvt(lsSvt);
                    in.setLsSvcode(lsSvcode);
                    in.setAlSvJigyoId(String.valueOf(alSvJigyoId));
                    in.setAsd(asd);
                    List<AdderServiceKasanFlgOutEntity> items = comMhcItemuseSougouSelectMapper
                            .findAdderServiceKasanFlgByCriteria(in);
                    Integer li_kasan_flg = items.stream().findFirst().map(AdderServiceKasanFlgOutEntity::getKasanFlg)
                            .orElse(0);
                    if (li_kasan_flg.equals(1)) {
                        liRet = 999;
                    }
                }
                    break;
                default:
                    // 上記以外
                    if (teikyouTaiseiKasan(asd, asv)) { // f_cmn_is_teikyou_taisei_kasan
                        // サービス提供体制強化加算
                        liRet = 999;
                        // 2024/03/16 ADD START Ken.Matsuda G3C45L144_202404改正_ケアマネ_利用票関連_別表計算_4月版_共通関数
                    } else if (isGyakutaiBousiGenzan(asd, asv).equals(1)) { // f_cmn_is_gyakutai_bousi_genzan
                        // 高齢者虐待防止未実施減算
                        liRet = 999;

                    } else if (isGyomuKeizokuGenzan(asd, asv).equals(1)) { // f_cmn_is_gyomu_keizoku_genzan
                        // 業務継続計画未策定減算
                        // 2025/02/17 MOD START Ken.Matsuda G3C46L153_202504改正_利用票別表計算_追加対応
                        if((CommonConstants.STR_17.equals(lsSvt) || CommonConstants.STR_67.equals(lsSvt))&& CommonConstants.STR_D_2.equals(StringUtils.substring(asv, 2, 4))){
                            //率は対象外
                        }else{
                            liRet = 999;
                        }
                        // 2025/02/17 MOD END Ken.Matsuda G3C46L153_202504改正_利用票別表計算_追加対応

                        // 2024/03/16 ADD END Ken.Matsuda G3C45L144_202404改正_ケアマネ_利用票関連_別表計算_4月版_共通関数
                        // 2025/02/04 ADD START Ken.Matsuda G3C46L153_202504改正_利用票別表計算_追加対応
                    } else if (isShintaiKousokuGenzan(asd, asv).equals(1)) {
                        //身体拘束廃止未実施減算
                        // 2025年4月改正から対象事業種別、サービスコードが増加したため共通関数化
                        liRet = 999;
                        // 2025/02/04 ADD END Ken.Matsuda G3C46L153_202504改正_利用票別表計算_追加対応
                    } else {
                        switch (lsSvt) {
                            case "11":
                                if (Arrays.asList("4000", "4001", "4002").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4003追加 2018/02/22 itagaki
                                if (Arrays.asList("4003").contains(lsSvcode)) {
                                    liRet = 999;
                                }

                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("4004", "4005").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                            case "12":
                                if (Arrays.asList("4113", "6133", "6134").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "13":
                                if (Arrays
                                        .asList("3100", "3200", "4000", "4001", "4002", "4003", "4004", "4005", "4100",
                                                "7000")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4010追加 2018/02/22 itagaki
                                if (Arrays.asList("4010").contains(lsSvcode)) {
                                    liRet = 999;
                                }

                            case "14":
                                if (Arrays.asList("5003", "5004", "5005", "6110").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 5006、5007、5010追加 2018/02/22 itagaki
                                if (Arrays.asList("5006", "5007", "5010").contains(lsSvcode)) {
                                    liRet = 999;
                                }

                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("5008", "5009").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                                // case "15":
                            case "15", "78": // H28/4改正対応 2016/01/15 itagaki
                                if (Arrays
                                        .asList("5051", "5052", "5301", "5305", "5306", "5605", "5606", "5611", "5612",
                                                "5613", "5614", "6109")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4002、4003、6201、6340、6341、6350追加 2018/02/22 itagaki
                                if (Arrays.asList("4002", "4003", "6201", "6340", "6341", "6350").contains(lsSvcode)) {
                                    liRet = 999;
                                }

                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays
                                        .asList("4001", "5053", "5303", "5608", "6116", "6202", "6338", "6339", "6361",
                                                "6601", "6602", "6603", "6604", "6605")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "16":
                                if (Arrays
                                        .asList("5301", "5601", "5605", "5606", "5608", "5609", "5610", "5611", "5612",
                                                "5613",
                                                "5614", "6109", "6143", "6253", "6254", "6255", "6256", "6110")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 5615、5616、5617、5618、6144、6145、6146、6147、6148、6201追加
                                // 2018/02/22 itagaki
                                if (Arrays
                                        .asList("5615", "5616", "5617", "5618", "6144", "6145", "6146", "6147", "6148",
                                                "6201")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }

                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays
                                        .asList("5303", "5619", "5620", "5621", "5622", "5626", "6116", "6202", "6257",
                                                "6361", "6601", "6602", "6603", "6604", "6605", "6606")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "21":
                                if (Arrays
                                        .asList("6004", "6005", "6109", "6113", "6115", "6116", "6117", "6119", "6121",
                                                "6275", "6277", "6278", "6279", "6280", "6282", "6283", "9200")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票
                                // 4002、4003、6123、6125、6133、6134、6135、6136、6137、6138、6350追加 2018/02/22 itagaki
                                if (Arrays
                                        .asList("4002", "4003", "6123", "6125", "6133", "6134", "6135", "6136", "6137",
                                                "6138", "6350")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("4001").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                            case "22":
                                if (Arrays
                                        .asList("1920", "6000", "6109", "6110", "6111", "6117", "6121", "6254", "6275",
                                                "6277", "6278", "6279", "6601", "9000", "22T00000", "22T00010",
                                                "22T00020", "22T00030",
                                                "22T00040")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 6133、6134、6280、6281、6602追加 2018/02/22 itagaki
                                if (Arrays.asList("6133", "6134", "6280", "6281", "6602").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("6001").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "23":
                                if (Arrays
                                        .asList("2601", "2603", "2700", "2704", "2705", "2706", "2775", "2777", "2920",
                                                "3600",
                                                "3602", "3704", "3705", "3706", "3751", "3775", "3920", "4751", "4775",
                                                "4920")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 2591、2592、2593、2594、2714、2715、3610、3714、3715追加
                                // 2018/02/22 itagaki
                                if (Arrays
                                        .asList("2591", "2592", "2593", "2594", "2714", "2715", "3610", "3714", "3715")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }

                            case "24":
                                if (Arrays.asList("6004", "6005", "6109", "6121", "6275", "9200").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4002、4003、6133、6134、6350追加 2018/02/28 itagaki
                                if (Arrays.asList("4002", "4003", "6133", "6134", "6350").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("4001").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "25":
                                if (Arrays
                                        .asList("1920", "6000", "6109", "6111", "6117", "6121", "6275", "6601", "9000",
                                                "25T00000", "25T00010", "25T00020", "25T00030", "25T00040")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 6133、6134、6280、6281、6602追加 2018/02/28 itagaki
                                if (Arrays.asList("6133", "6134", "6280", "6281", "6602").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("6001").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "26":
                                if (Arrays
                                        .asList("2601", "2603", "2700", "2704", "2706", "2775", "2920", "3600", "3602",
                                                "3704", "3706", "3775", "3920", "4775", "4920")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 2591、2592、2593、2594、3610、6133、6134、6135、6136追加
                                // 2018/02/28 itagaki
                                if (Arrays
                                        .asList("2591", "2592", "2593", "2594", "3610", "6133", "6134", "6135", "6136")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }

                            case "27":
                                if (Arrays.asList("1160").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 6109追加 2018/03/01 itagaki
                                if (Arrays.asList("6109").contains(lsSvcode)) {
                                    liRet = 999;
                                }

                            case "28":
                                if (Arrays.asList("2000").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 6109追加 2018/03/01 itagaki
                                if (Arrays.asList("6109").contains(lsSvcode)) {
                                    liRet = 999;
                                }

                            case "32":
                                if (Arrays
                                        .asList("1550", "1600", "6109", "6133", "6134", "6142", "6143", "6144", "6161",
                                                "6171", "6502")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 1601、1602、4002、6122、6201、6304～6313、6400追加 2018/03/01
                                // itagaki
                                if (Arrays.asList("1601", "1602", "4002", "6122", "6201", "6400").contains(lsSvcode)
                                        || (lsSvcode.compareTo("6304") >= 0 && lsSvcode.compareTo("6313") <= 0)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("4001", "6110", "6140", "6200", "6361").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "33":
                                if (Arrays
                                        .asList("1160", "6003", "6123", "6124", "6125", "6126", "6127", "6133", "6134")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4002、4003、6109、6122、6201、6304～6308、6320、6330追加
                                // 2018/03/01 itagaki
                                if (Arrays.asList("4002", "4003", "6109", "6122", "6201", "6320", "6330")
                                        .contains(lsSvcode)
                                        || (lsSvcode.compareTo("6304") >= 0 && lsSvcode.compareTo("6308") <= 0)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays
                                        .asList("4001", "6004", "6005", "6006", "6120", "6137", "6138", "6139", "6140",
                                                "6321", "6361")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "35":
                                if (Arrays
                                        .asList("1722", "1723", "1724", "1725", "1726", "1727", "1728", "1821", "1822",
                                                "1823", "1824", "1825", "1826", "1827", "2141", "2142", "2143", "6003",
                                                "6123", "6124",
                                                "6133", "6134", "6322", "6323", "6324", "6325", "6326", "6327", "6328",
                                                "2322", "2323",
                                                "2324", "2325", "2326", "2327", "2328")
                                        .contains(lsSvcode)) { // 総合事業の外部利用型サービス追加
                                                               // 2015/06/01
                                                               // itagaki
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4002、4003、6109、6122、6201、6304、6305追加 2018/03/01
                                // itagaki
                                if (Arrays.asList("4002", "4003", "6109", "6122", "6201", "6304", "6305")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("4001", "6004", "6361").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "36":
                                if (Arrays.asList("2000", "6003", "6123", "6125", "6126", "6127", "6133", "6134")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4002、4003、6109、6122、6201、6304～6308、6320、6330追加
                                // 2018/03/01 itagaki
                                if (Arrays.asList("4002", "4003", "6109", "6122", "6201", "6320", "6330")
                                        .contains(lsSvcode)
                                        || (lsSvcode.compareTo("6304") >= 0 && lsSvcode.compareTo("6308") <= 0)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays
                                        .asList("4001", "6004", "6005", "6006", "6124", "6137", "6138", "6139", "6140",
                                                "6321", "6361")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "37":
                                if (Arrays.asList("1550", "6109", "6133", "6134", "6161", "6171", "6502")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4002、6122、6201、6304、6305、6400追加 2018/03/01 itagaki
                                if (Arrays.asList("4002", "6122", "6201", "6304", "6305", "6400").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("4001", "6110", "6200", "6361").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "38":
                                if (Arrays.asList("1600", "6109", "6121", "6161", "6171").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 1601、1602、4002追加 2018/03/01 itagaki
                                if (Arrays.asList("1601", "1602", "4002").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("4001", "6110").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "39":
                                if (Arrays.asList("6109", "6121", "6161", "6171").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4002追加 2018/03/01 itagaki
                                if (Arrays.asList("4002").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("4001", "6110").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "59", "60":
                                liRet = 999;

                                // case "61", "A1", "A2" //A2のコードが増えたため分岐 2015/05/25 itagaki
                            case "61", "A1":
                                if (Arrays.asList("4001", "4002").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3C40L127_H30介護_総合事業_ケアマネ_共通関数 2018/08/02 itagaki
                                if (Arrays.asList("4003").contains(lsSvcode)) {
                                    liRet = 999;
                                }

                            case "A2":
                                if (Arrays
                                        .asList("4001", "4002", "4011", "4012", "4021", "4022", "4031", "4032", "4041",
                                                "4042")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3C40L127_H30介護_総合事業_ケアマネ_共通関数 2018/08/02 itagaki
                                if (Arrays.asList("4003", "4013", "4023", "4033", "4043").contains(lsSvcode)) {
                                    liRet = 999;
                                }

                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                            case "62":
                                if (Arrays.asList("4001", "6133", "6134").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "63":
                                if (Arrays.asList("3100", "3200", "4000", "4001", "4002", "4003", "4005")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("6123").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "64":
                                if (Arrays.asList("4000", "5001").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 5005、5010、5615追加 2018/03/01 itagaki
                                if (Arrays.asList("5005", "5010", "5615").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("6123").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                                // case "65", "A5", "A6" //A6のコードが増えたため分岐 2015/05/25 itagaki
                            case "65", "A5":
                                if (Arrays
                                        .asList("5002", "5003", "5004", "5005", "5006", "5007", "5008", "5009", "5010",
                                                "6105", "6106", "6109")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3C40L127_H30介護_総合事業_ケアマネ_共通関数 2018/08/02 itagaki
                                if (Arrays.asList("4002", "4003", "6201").contains(lsSvcode)) {
                                    liRet = 999;
                                }

                            case "A6":
                                if (Arrays
                                        .asList("5002", "5003", "5004", "5005", "5006", "5007", "5008", "5009", "5010",
                                                "6105",
                                                "6106", "6109", "5012", "5013", "5014", "5015", "5016", "5017", "5018",
                                                "5019",
                                                "5020", "6125", "6126", "6129", "5022", "5023", "5024", "5025", "5026",
                                                "5027",
                                                "5028", "5029", "5030", "6135", "6136", "6139", "5032", "5033", "5034",
                                                "5035",
                                                "5036", "5037", "5038", "5039", "5040", "6145", "6146", "6149", "5042",
                                                "5043",
                                                "5044", "5045", "5046", "5047", "5048", "5049", "5050", "6155", "6156",
                                                "6159")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3C40L127_H30介護_総合事業_ケアマネ_共通関数 2018/08/02 itagaki
                                if (Arrays
                                        .asList("4002", "4003", "6201", "4012", "4013", "6211", "4022", "4023", "6221",
                                                "4032", "4033", "6231", "4042", "4043", "6241")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays
                                        .asList("4001", "4011", "4021", "4031", "4041", "5011", "5021", "5031", "5041",
                                                "5051",
                                                "6120", "6130", "6140", "6150", "6200", "6116", "6120", "6130", "6140",
                                                "6150",
                                                "6200", "6210", "6220", "6230", "6240", "6311", "6321", "6331", "6341",
                                                "6351")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "66":
                                if (Arrays
                                        .asList("5002", "5003", "5004", "5005", "5006", "5007", "5008", "5009", "6105",
                                                "6106", "6107", "6108", "6109")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 5615、6119、6120、6201、6255、6256追加 2018/03/01 itagaki
                                if (Arrays.asList("5615", "6119", "6120", "6201", "6255", "6256").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays
                                        .asList("5010", "6116", "6123", "6124", "6125", "6126", "6127", "6128", "6202",
                                                "6257", "6361")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "71":
                                if (Arrays
                                        .asList("6136", "7201", "7202", "7203", "7204", "7205", "7206", "7207", "7208",
                                                "7209", "7210", "7211", "7212", "7301", "7302", "7303", "7304", "7305",
                                                "7306", "7307",
                                                "7308", "7309", "7310", "7311", "7312")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("6133", "6134", "6135", "6137").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "72":
                                if (Arrays.asList("5050", "5301", "5606", "5607", "5611", "5612", "6109")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4002、4003、6201追加 2018/03/01 itagaki
                                if (Arrays.asList("4002", "4003", "6201").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays
                                        .asList("4001", "5051", "5303", "5608", "6116", "6124", "6125", "6202", "6361",
                                                "6601", "6602", "6603", "6604", "6605")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "73":
                                if (Arrays
                                        .asList("4000", "4005", "4010", "6128", "6129", "6137", "6138", "6141", "6300",
                                                (lsSvcode.compareTo("7101") >= 0 && lsSvcode.compareTo("7140") <= 0))
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4002、4003、6109、6201追加 2018/03/01 itagaki
                                if (Arrays.asList("4002", "4003", "6109", "6201").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("6361").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "74":
                                if (Arrays.asList("5050", "5301", "5606", "5607", "5611", "5612", "6109")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4002、4003、6201追加 2018/03/01 itagaki
                                if (Arrays.asList("4002", "4003", "6201").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays
                                        .asList("4001", "5051", "5303", "5608", "6116", "6202", "6361", "6601", "6602",
                                                "6603", "6604", "6605")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "75":
                                if (Arrays.asList("4010", "6300").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4002、4003、6109、6201追加 2018/03/01 itagaki
                                if (Arrays.asList("4002", "4003", "6109", "6201").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("6361").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "76":
                                if (Arrays.asList("3100", "4000", "4001", "4002", "4003", "4010", "6100")
                                        .contains(lsSvcode)
                                        || (lsSvcode.compareTo("4101") >= 0 && lsSvcode.compareTo("4112") <= 0)
                                        || (lsSvcode.compareTo("7101") >= 0 && lsSvcode.compareTo("7120") <= 0)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4012、4013、4113、4114追加 2018/03/01 itagaki
                                if (Arrays.asList("4012", "4013", "4113", "4114").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("6133", "6134").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "77":
                                if (Arrays
                                        .asList("3100", "4000", "4001", "4003", "4010", "4015", "6100", "6128", "6129",
                                                "6139",
                                                "6300")
                                        .contains(lsSvcode)
                                        || (lsSvcode.compareTo("6001") >= 0 && lsSvcode.compareTo("6015") <= 0)
                                        || (lsSvcode.compareTo("6021") >= 0 && lsSvcode.compareTo("6030") <= 0)
                                        || (lsSvcode.compareTo("7101") >= 0 && lsSvcode.compareTo("7140") <= 0)) {
                                    liRet = 999;
                                }
                                // G3S39L123_H30介護_ケアマネ_利用票 4005、4014、6109、6201追加 2018/03/01 itagaki
                                if (Arrays.asList("4005", "4014", "6109", "6201").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays
                                        .asList("5600", "5605", "5606", "6116", "6202", "6355", "6356", "6358", "6359",
                                                "6360", "6361")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                            case "79":
                                if (Arrays.asList("4001").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                                // G3S39L123_H30介護_ケアマネ_利用票 68、69、2A、2Bを追加 2018/03/01 itagaki
                            case "68":
                                if (Arrays.asList("4002", "4003").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("4001").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "69":
                                if (Arrays.asList("4002", "4003").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD START Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数
                                if (Arrays.asList("8150").contains(lsSvcode)) {
                                    liRet = 999;
                                }
                                // 2021/02/19 ADD END Ken.Matsuda G3C42L150_2021年4月改正対応_利用票_別表計算_共通関数

                            case "2A":
                                if (Arrays
                                        .asList("6000", "6109", "6110", "6121", "6133", "6134", "6275", "6277", "6351",
                                                "6352",
                                                "6353", "6354", "6371", "6372", "6373", "6374", "6601", "6603", "6920",
                                                "2AT00000", "2AT00010", "2AT00020", "2AT00030", "2AT00040")
                                        .contains(lsSvcode)) {
                                    liRet = 999;
                                }

                            case "2B":
                                if (Arrays.asList("6000", "6109", "6121", "6133", "6134", "6275", "6371", "6372",
                                        "6373",
                                        "6374", "6601", "6603", "6920", "2BT00000", "2BT00010", "2BT00020", "2BT00030",
                                        "2BT00040").contains(lsSvcode)) {
                                    liRet = 999;
                                }

                        }
                    }
            }
            // 2024/02/24 ADD START Ken.Matsuda : G3C45L144_202404改正_ケアマネ_利用票関連_別表計算_共通関数
            if (asd.compareTo("2024/04/01") >= 0) {

                switch (lsSvt) {
                    case "11":
                        if (Arrays.asList("6192").contains(lsSvcode)) {
                            liRet = 999;
                            // 訪問介護口腔連携強化加算
                        }

                    case "12":
                        if (Arrays.asList("4000").contains(lsSvcode)) {
                            liRet = 999;
                            // 訪問入浴看取り連携体制加算
                        }

                    case "21":
                        if (Arrays.asList("4000", "6192", "6237", "6238").contains(lsSvcode)) {
                            liRet = 999;
                            // 短期生活看取り連携体制加算
                            // 短期生活口腔連携強化加算
                            // 短期生活生産性向上推進体制加算Ⅰ
                            // 短期生活生産性向上推進体制加算Ⅱ
                        }

                    case "22":
                        if (Arrays.asList("6192", "6237", "6238").contains(lsSvcode)) {
                            liRet = 999;
                            // 老短口腔連携強化加算
                            // 老短生産性向上推進体制加算Ⅰ
                            // 老短生産性向上推進体制加算Ⅱ
                        }

                    case "23":
                        if (Arrays.asList("2192", "2237", "2238", "3192", "3237", "3238").contains(lsSvcode)) {
                            liRet = 999;
                            // 病院療短口腔連携強化加算
                            // 病院療短生産性向上推進体制加算Ⅰ
                            // 病院療短生産性向上推進体制加算Ⅱ
                            // 診療所短期口腔連携強化加算
                            // 診療所短期生産性向上推進体制加算Ⅰ
                            // 診療所短期生産性向上推進体制加算Ⅱ
                        }

                    case "24":
                        if (Arrays.asList("6192", "6237", "6238").contains(lsSvcode)) {
                            liRet = 999;
                            // 予短期生活口腔連携強化加算
                            // 予短期生活生産性向上推進体制加算Ⅰ
                            // 予短期生活生産性向上推進体制加算Ⅱ
                        }

                    case "25":
                        if (Arrays.asList("6192", "6237", "6238").contains(lsSvcode)) {
                            liRet = 999;
                            // 予老短口腔連携強化加算
                            // 予老短生産性向上推進体制加算Ⅰ
                            // 予老短生産性向上推進体制加算Ⅱ
                        }

                    case "26":
                        if (Arrays.asList("2192", "2237", "2238", "3192", "3237", "3238").contains(lsSvcode)) {
                            liRet = 999;
                            // 予病院療短口腔連携強化加算
                            // 予病院療短生産性向上推進体制加算Ⅰ
                            // 予病院療短生産性向上推進体制加算Ⅱ
                            // 予診療所短期口腔連携強化加算
                            // 予診療所短期生産性向上推進体制加算Ⅰ
                            // 予診療所短期生産性向上推進体制加算Ⅱ
                        }

                    case "27":
                        if (Arrays.asList("1161", "6166", "6167", "6237", "6238", "9010").contains(lsSvcode)) {
                            liRet = 999;
                            // 短期特定施設夜間看護体制加算Ⅰ
                            // 短期特定施設高齢者等感染対策向上加算Ⅰ
                            // 短期特定施設高齢者等感染対策向上加算Ⅱ
                            // 短期特定施設生産性向上推進体制加算Ⅰ
                            // 短期特定施設生産性向上推進体制加算Ⅱ
                            // 短期特定施設新興感染症等施設療養費
                        }

                    case "28":
                        if (Arrays.asList("2001", "6166", "6167", "6237", "6238", "9010").contains(lsSvcode)) {
                            liRet = 999;
                            // 短期地域特定施設夜間看護体制加算Ⅰ
                            // 短期地域特定施設高齢者等感染対策向上加算Ⅰ
                            // 短期地域特定施設高齢者等感染対策向上加算Ⅱ
                            // 短期地域特定施設生産性向上推進体制加算Ⅰ
                            // 短期地域特定施設生産性向上推進体制加算Ⅱ
                            // 短期地域特定施設新興感染症等施設療養費
                        }

                    case "2A":
                        if (Arrays.asList("6192", "6237", "6238").contains(lsSvcode)) {
                            liRet = 999;
                            // 医療院短期口腔連携強化加算
                            // 医療院短期生産性向上推進体制加算Ⅰ
                            // 医療院短期生産性向上推進体制加算Ⅱ
                        }

                    case "2B":
                        if (Arrays.asList("6192", "6237", "6238").contains(lsSvcode)) {
                            liRet = 999;
                            // 予防医療院短期口腔連携強化加算
                            // 予防医療院短期生産性向上推進体制加算Ⅰ
                            // 予防医療院短期生産性向上推進体制加算Ⅱ
                        }

                    case "32":
                        if (Arrays
                                .asList("1603", "6123", "6124", "6150", "6153", "6154", "6166", "6167", "6237", "6238",
                                        "9010")
                                .contains(lsSvcode)) {
                            liRet = 999;
                            // 認知症対応型医療連携体制加算Ⅱ
                            // 認知症対応型協力医療機関連携加算１
                            // 認知症対応型協力医療機関連携加算２
                            // 認知症対応型退居時情報提供加算
                            // 認知症対応型認知症チームケア推進加算Ⅰ
                            // 認知症対応型認知症チームケア推進加算Ⅱ
                            // 認知症対応型高齢者等感染対策向上加算Ⅰ
                            // 認知症対応型高齢者等感染対策向上加算Ⅱ
                            // 認知症対応型生産性向上推進体制加算Ⅰ
                            // 認知症対応型生産性向上推進体制加算Ⅱ
                            // 認知症対応型新興感染症等施設療養費
                        }

                    case "33":
                        if (Arrays.asList("1161", "6143", "6150", "6166", "6167", "6237", "6238", "9010")
                                .contains(lsSvcode)) {
                            liRet = 999;
                            // 特定施設夜間看護体制加算Ⅰ
                            // 特定施設協力医療機関連携加算２
                            // 特定施設退居時情報提供加算
                            // 特定施設高齢者等感染対策向上加算Ⅰ
                            // 特定施設高齢者等感染対策向上加算Ⅱ
                            // 特定施設生産性向上推進体制加算Ⅰ
                            // 特定施設生産性向上推進体制加算Ⅱ
                            // 特定施設新興感染症等施設療養費
                        }

                    case "35":
                        if (Arrays.asList("2329", "6125", "6150", "6166", "6167", "6237", "6238", "9010")
                                .contains(lsSvcode)) {
                            liRet = 999;
                            // 予防外部通所一体的サービス提供加算
                            // 予防特定施設協力医療機関連携加算２
                            // 予防特定施設退居時情報提供加算
                            // 予防特定施設高齢者等感染対策向上加算Ⅰ
                            // 予防特定施設高齢者等感染対策向上加算Ⅱ
                            // 予防特定施設生産性向上推進体制加算Ⅰ
                            // 予防特定施設生産性向上推進体制加算Ⅱ
                            // 予防特定施設新興感染症等施設療養費
                        }

                    case "36":
                        if (Arrays.asList("2001", "6143", "6150", "6166", "6167", "6237", "6238", "9010")
                                .contains(lsSvcode)) {
                            liRet = 999;
                            // 地域特定施設夜間看護体制加算Ⅰ
                            // 地域特定施設協力医療機関連携加算２
                            // 地域特定施設退居時情報提供加算
                            // 地域特定施設高齢者等感染対策向上加算Ⅰ
                            // 地域特定施設高齢者等感染対策向上加算Ⅱ
                            // 地域特定施設生産性向上推進体制加算Ⅰ
                            // 地域特定施設生産性向上推進体制加算Ⅱ
                            // 地域特定施設新興感染症等施設療養費
                        }

                    case "37":
                        if (Arrays.asList("6150", "6153", "6154", "6166", "6167", "6237", "6238", "9010")
                                .contains(lsSvcode)) {
                            liRet = 999;
                            // 予認知症対応型退居時情報提供加算
                            // 予認知症対応型認知症チームケア推進加算Ⅰ
                            // 予認知症対応型認知症チームケア推進加算Ⅱ
                            // 予認知症対応型高齢者等感染対策向上加算Ⅰ
                            // 予認知症対応型高齢者等感染対策向上加算Ⅱ
                            // 予認知症対応型生産性向上推進体制加算Ⅰ
                            // 予認知症対応型生産性向上推進体制加算Ⅱ
                            // 予認知症対応型新興感染症等施設療養費
                        }

                    case "38":
                        if (Arrays.asList("1603", "6166", "6167", "6237", "6238", "9010").contains(lsSvcode)) {
                            liRet = 999;
                            // 短期共同生活医療連携体制加算Ⅱ
                            // 短期共同生活高齢者等感染対策向上加算Ⅰ
                            // 短期共同生活高齢者等感染対策向上加算Ⅱ
                            // 短期共同生活生産性向上推進体制加算Ⅰ
                            // 短期共同生活生産性向上推進体制加算Ⅱ
                            // 短期共同生活新興感染症等施設療養費
                        }

                    case "39":
                        if (Arrays.asList("6166", "6167", "6237", "6238", "9010").contains(lsSvcode)) {
                            liRet = 999;
                            // 予短期共同高齢者等感染対策向上加算Ⅰ
                            // 予短期共同高齢者等感染対策向上加算Ⅱ
                            // 予短期共同生産性向上推進体制加算Ⅰ
                            // 予短期共同生産性向上推進体制加算Ⅱ
                            // 予短期共同新興感染症等施設療養費
                        }

                    case "68":
                        if (Arrays.asList("6237", "6238").contains(lsSvcode)) {
                            liRet = 999;
                            // 短期小多機能型生産性向上推進体制加算Ⅰ
                            // 短期小多機能型生産性向上推進体制加算Ⅱ
                        }

                    case "69":
                        if (Arrays.asList("6237", "6238").contains(lsSvcode)) {
                            liRet = 999;
                            // 予短期小多機能生産性向上推進体制加算Ⅰ
                            // 予短期小多機能生産性向上推進体制加算Ⅱ
                        }

                    case "73":
                        if (Arrays.asList("4009", "6126", "6127", "6237", "6238").contains(lsSvcode)) {
                            liRet = 999;
                            // 小多機能型総合マネジメント加算Ⅰ
                            // 小規模多機能型認知症加算Ⅰ
                            // 小規模多機能型認知症加算Ⅱ
                            // 小多機能型生産性向上推進体制加算Ⅰ
                            // 小多機能型生産性向上推進体制加算Ⅱ
                        }

                    case "75":
                        if (Arrays.asList("4009", "6237", "6238").contains(lsSvcode)) {
                            liRet = 999;
                            // 予小多機能総合マネジメント加算Ⅰ
                            // 予小多機能生産性向上推進体制加算Ⅰ
                            // 予小多機能生産性向上推進体制加算Ⅱ
                        }

                    case "76":
                        if (Arrays.asList("3000", "4009", "6135", "6137", "6192", "6211", "6212", "6213")
                                .contains(lsSvcode)) {
                            liRet = 999;
                            // 定期巡回緊急時訪問看護加算Ⅰ
                            // 定期巡回総合マネジメント体制加算Ⅰ
                            // 定期巡回認知症専門ケア加算Ⅰ２
                            // 定期巡回認知症専門ケア加算Ⅱ２
                            // 定期巡回口腔連携強化加算
                            // 定期巡回サービス提供体制加算Ⅰ２
                            // 定期巡回サービス提供体制加算Ⅱ２
                            // 定期巡回サービス提供体制加算Ⅲ２
                        }

                    case "77":
                        // 2024/03/16 MOD START Ken.Matsuda G3C45L144_202404改正_ケアマネ_利用票関連_別表計算_4月版_共通関数
                        // if ( Arrays.asList( "4009", "4021", "4022", "6126", "6127", "6237",
                        // "6238").contains(lsSvcode) ) {
                        if (Arrays.asList("4009", "4021", "4022", "4023", "6126", "6127", "6237", "6238")
                                .contains(lsSvcode)) {
                            // 2024/03/16 MOD END Ken.Matsuda G3C45L144_202404改正_ケアマネ_利用票関連_別表計算_4月版_共通関数
                            liRet = 999;
                            // 看護小規模総合マネジメント加算Ⅰ
                            // 看護小規模遠隔死亡診断補助加算
                            // 看護小規模専門管理加算１
                            // 看護小規模専門管理加算２
                            // 看護小規模認知症加算Ⅰ
                            // 看護小規模認知症加算Ⅱ
                            // 看護小規模生産性向上推進体制加算Ⅰ
                            // 看護小規模生産性向上推進体制加算Ⅱ
                        }

                    case "78":
                        if (Arrays.asList("6500").contains(lsSvcode)) {
                            liRet = 999;
                            // 地域通所介護重度者ケア体制加算
                        }

                    case "79":
                        if (Arrays.asList("6237", "6238").contains(lsSvcode)) {
                            liRet = 999;
                            // 短期看護小規模生産性向上推進体制加算Ⅰ
                            // 短期看護小規模生産性向上推進体制加算Ⅱ
                        }

                    case "A2":
                        if (Arrays.asList("6102", "6112", "6122", "6132", "6142").contains(lsSvcode)) {
                            liRet = 999;
                            // 訪問型独自口腔連携強化加算
                            // 訪問型独自口腔連携強化加算／２
                            // 訪問型独自口腔連携強化加算／３
                            // 訪問型独自口腔連携強化加算／４
                            // 訪問型独自口腔連携強化加算／５
                        }

                    case "A6":
                        if (Arrays
                                .asList("5612", "5622", "5632", "5642", "5652", "6207", "6227", "6237", "6247", "6257",
                                        "6310", "6320", "6330", "6340", "6350")
                                .contains(lsSvcode)) {
                            liRet = 999;
                            // 通所型独自送迎減算
                            // 通所型独自送迎減算／２
                            // 通所型独自送迎減算／３
                            // 通所型独自送迎減算／４
                            // 通所型独自送迎減算／５
                            // 通所型独自サービス同一建物減算３
                            // 通所型独自サービス同一建物減算／２３
                            // 通所型独自サービス同一建物減算／３３
                            // 通所型独自サービス同一建物減算／４３
                            // 通所型独自サービス同一建物減算／５３
                            // 通所型独自一体的サービス提供加算
                            // 通所型独自一体的サービス提供加算／２
                            // 通所型独自一体的サービス提供加算／３
                            // 通所型独自一体的サービス提供加算／４
                            // 通所型独自一体的サービス提供加算／５
                        }
                }

            }
            // 2024/02/24 ADD END Ken.Matsuda : G3C45L144_202404改正_ケアマネ_利用票関連_別表計算_共通関数

            // 2024/04/16 ADD START Ken.Matsuda G3C45L144_202404改正_ケアマネ_利用票関連_別表計算_5月版_共通関数
            if (asd.compareTo("2024/06/01") >= 0) {

                switch (lsSvt) {
                    case "13":
                        if (Arrays.asList("3001", "3002", "4021", "4023", "4024", "4025", "4026", "6192")
                                .contains(lsSvcode)) {
                            liRet = 999;
                            // 緊急時訪問看護加算Ⅰ１
                            // 緊急時訪問看護加算Ⅰ２
                            // 訪問看護遠隔死亡診断補助加算
                            // 訪問看護初回加算Ⅰ
                            // 訪問看護訪問回数超過等減算
                            // 訪問看護専門管理加算１
                            // 訪問看護専門管理加算２
                            // 訪問看護口腔連携強化加算
                        }

                    case "14":
                        if (Arrays.asList("4003", "5021", "5022", "6192").contains(lsSvcode)) {
                            liRet = 999;
                            // 訪問リハ退院時共同指導加算
                            // 訪問リハ認知症短期集中リハ加算
                            // 訪問リハマネジメント加算３
                            // 訪問リハ口腔連携強化加算
                        }

                    case "16":
                        if (Arrays.asList("5625", "5631", "5632", "5640", "6370").contains(lsSvcode)) {
                            liRet = 999;
                            // 通所リハ口腔機能向上加算Ⅱ１
                            // 通所リハマネジメント加算３１
                            // 通所リハマネジメント加算３２
                            // 通所リハマネジメント加算４
                            // 通所リハ退院時共同指導加算
                        }

                    case "31":
                        if (Arrays.asList("8121", "8122").contains(lsSvcode)) {
                            liRet = 999;
                            // 薬剤師医療用麻薬持続注射療法加算
                            // 薬剤師在宅中心静脈栄養法加算
                        }

                    case "34":
                        if (Arrays.asList("8121", "8122").contains(lsSvcode)) {
                            liRet = 999;
                            // 予防医療用麻薬持続注射療法加算
                            // 予防在宅中心静脈栄養法加算
                        }

                    case "35":
                        if (Arrays.asList("1828").contains(lsSvcode)) {
                            liRet = 999;
                            // 予防外部通所リハ一体的サービス提供加算
                        }

                    case "63":
                        if (Arrays.asList("3001", "3002", "4023", "4024", "4025", "4026", "6124", "6192")
                                .contains(lsSvcode)) {
                            liRet = 999;
                            // 予防緊急時訪問看護加算Ⅰ１
                            // 予防緊急時訪問看護加算Ⅰ２
                            // 予防訪問看護初回加算Ⅰ
                            // 予防訪問看護訪問回数超過等減算
                            // 予防訪問看護専門管理加算１
                            // 予防訪問看護専門管理加算２
                            // 予防訪問看護１２月超減算２
                            // 予防訪問看護口腔連携強化加算
                        }

                    case "64":
                        if (Arrays.asList("4003", "6162").contains(lsSvcode)) {
                            liRet = 999;
                            // 予防訪問リハ退院時共同指導加算
                            // 予防訪問リハ口腔連携強化加算
                        }

                    case "66":
                        if (Arrays.asList("6360", "6370").contains(lsSvcode)) {
                            liRet = 999;
                            // 予通リハ一体的サービス提供加算
                            // 予防通所リハ退院時共同指導加算
                        }

                }
            }
            // 2024/04/16 ADD END Ken.Matsuda G3C45L144_202404改正_ケアマネ_利用票関連_別表計算_5月版_共通関数
            // 2025/02/01 ADD START Ken.Matsuda G3C46L153_202504改正_利用票別表計算_追加対応
            LocalDate asdDate = LocalDate.parse(asd, DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD));
            LocalDate date_20250801 = LocalDate.parse(CommonConstants.DATE_20250801, DateTimeFormatter.ofPattern(CommonConstants.DATE_FORMAT_YYYY_MM_DD));
            if(asdDate.isEqual(date_20250801) || asdDate.isAfter(date_20250801)){
                switch (lsSvt) {
                    case "22" :
                        if(CommonConstants.STR_6160.equals(lsSvcode)){
                            liRet = 999;
                            // 老短室料相当額減算
                        }
                    case "25" :
                        if(CommonConstants.STR_6160.equals(lsSvcode)){
                            liRet = 999;
                            // 予老短室料相当額減算
                        }
                    case "2A" :
                        if(CommonConstants.STR_6160.equals(lsSvcode)){
                            liRet = 999;
                            // 医療院短期室料相当額減算
                        }
                    case "2B" :
                        if(CommonConstants.STR_6160.equals(lsSvcode)){
                            liRet = 999;
                            // 予防医療院短期室料相当額減算
                        }
                }
            }
            // 2025/02/01 ADD END Ken.Matsuda G3C46L153_202504改正_利用票別表計算_追加対応
        }

        return liRet;
    }

    /**
     * 業務継続計画未策定減算を特定する（合成コード以外） コードの先頭文字で単独減算を判定する 単独の減算：「D」 合成コード、通常コード：数字
     * 「D」を含む業務継続減算以外のコードはなし f_cmn_is_gyomu_keizoku_genzan
     * 
     * @param asDate 提供年月日（yyyy/mm/dd）
     * @param asScd  Scode
     * @return integer 0=それ以外／1=業務継続計画未策定減算
     */
    public Integer isGyomuKeizokuGenzan(String asDate, String asScd) {

        String lsSvtype = StringUtils.left(asScd, 2);
        String lsSvcode = StringUtils.substring(asScd, 2, 6);

        Integer liRet = 0;
        if (asDate.compareTo("2024/04/01") >= 0) {
            switch (lsSvtype) {
                case CommonConstants.NUM_STR_11:
                    // 単独の減算コードとして誤って判定されるため分岐処理を追加
                    break;

                default:
                    if (StringUtils.left(lsSvcode, 1).equals("D")) {
                        liRet = 1;
                    }
                    break;
            }

        }

        return liRet;
    }

    /**
     * 高齢者虐待防止未実施減算を特定する（合成コード以外） コードの先頭文字で単独減算を判定する 単独の減算：「C」 合成コード：数字、または「A」「B」
     * 通常コード：数字 f_cmn_is_gyakutai_bousi_genzan
     * 
     * @param asDate 提供年月日（yyyy/mm/dd）
     * @param asScd  Scode
     * @return integer 0=それ以外／1=高齢者虐待防止未実施減算
     */
    public Integer isGyakutaiBousiGenzan(String asDate, String asScd) {
        String lsSvtype = StringUtils.left(asScd, 2);
        String lsSvcode = StringUtils.substring(asScd, 2, 6);

        Integer liRet = 0;

        if (asDate.compareTo("2024/04/01") >= 0) {
            switch (lsSvtype) {
                case "23":
                    String svCodePrefix = StringUtils.left(lsSvcode, 2);
                    if ("C2".equals(svCodePrefix) || "C3".equals(svCodePrefix)) {
                        liRet = 1;
                    }
                    break;

                default:
                    if (StringUtils.left(lsSvcode, 1).equals("C")) {
                        liRet = 1;
                    }
                    break;
            }
        }

        return liRet;
    }

    /**
     * サービス種類・scode・合成識別区分を元に、加算かどうかのチェックをおこなう
     * 
     * @param asSvtype    サービス種類
     * @param asScode     scode
     * @param asGousikKbn 合成識別区分
     * @return 加算サービス
     */
    public boolean chkKasanService2(String asSvtype, String asScode, String asGousikKbn) {
        boolean lbKasan = false;
        String lsSvtype = StringUtils.EMPTY;

        if (asScode == null) {
            asScode = StringUtils.EMPTY;
        }
        String lsScode = asScode.length() >= 6 ? asScode.substring(0, 6) : asScode;
        if (SCODE_START_000000.equals(lsScode)) {
            return false;
        }

        // 外部サービスの場合、svtypeを変換する
        if (!SVTYPE_33.equals(asSvtype) && asScode.startsWith(SCODE_START_33)) {
            // 介護の外部サービス（特定施設以外）
            lsSvtype = SVTYPE_33;
        } else if (!SVTYPE_35.equals(asSvtype) && asScode.startsWith(SCODE_START_35)) {
            // 予防の外部サービス（予防特定施設以外）
            lsSvtype = SVTYPE_35;
        } else {
            // 通常のサービス（特定施設・予防特定施設の外部サービス含む）
            lsSvtype = asSvtype;
        }

        if (!GOUSIK_KBN_3.equals(asGousikKbn)) {
            // 合成識別区分が 3 以外
            // 基本サービス
            lbKasan = false;
        } else {
            // 合成識別区分が 3
            if (SVTYPE_33.equals(lsSvtype)) {
                String last4 = lsScode.length() >= 4 ? lsScode.substring(lsScode.length() - 4) : StringUtils.EMPTY;
                if (SCODE_LAST_FOUR_33_CODES.contains(last4)) {
                    // 加算サービス
                    lbKasan = true;
                } else {
                    // 基本サービス
                    lbKasan = false;
                }
            } else if (SVTYPE_35.equals(lsSvtype)) {
                String last4Preventive = lsScode.length() >= 4 ? lsScode.substring(lsScode.length() - 4)
                        : StringUtils.EMPTY;
                if (SCODE_LAST_FOUR_35_CODES.contains(last4Preventive)) {
                    // 加算サービス
                    lbKasan = true;
                } else {
                    // 基本サービス
                    lbKasan = false;
                }
            } else {
                // 基本サービス
                lbKasan = true;
            }
        }
        return lbKasan;
    }

    /**
     * 年月日に サービス種別 が存在するかをチェックする
     * 
     * @param asSvtype 対象のサービス種別
     * @param asYmd    対象の年月日（yyyy/mm/dd）
     * @return true = あり、False = なし
     */
    public boolean chkTermSvtype(String asSvtype, String asYmd) {
        // 独自サービス（横だし）の場合はtrueを返す
        if (asSvtype.compareTo(SVTYPE_81) >= 0 && asSvtype.compareTo(SVTYPE_99) <= 0) {
            return true;
        }
        // 検索条件の設定
        CmnChkTermSvtypeByCriteriaInEntity criteria = new CmnChkTermSvtypeByCriteriaInEntity();
        criteria.setSvtype(asSvtype);
        criteria.setDateYmd(asYmd);
        // サービスコードを取得
        CmnChkTermSvtypeOutEntity cmnChkTermSvtypeOutEntity = this.comMhcTermSelectMapper
                .countCmnChkTermSvtypeByCriteria(criteria);
        if (cmnChkTermSvtypeOutEntity != null && cmnChkTermSvtypeOutEntity.getCMTCnt() != null
                && CommonDtoUtil.strValToInt(cmnChkTermSvtypeOutEntity.getCMTCnt()) > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 特定施設入居者生活介護（外部サービス利用型）としての限度額を取得する.
     *
     * @param aiYokaiKbn 要介護度区分（ＮＤ独自）
     * @param asYmd      対象年月日
     * @return Integer : 限度額
     */
    public Integer getGaibuGendo(Integer aiYokaiKbn, String asYmd) {
        Integer ldRet = CommonConstants.NUMBER_0;
        if (aiYokaiKbn == null || aiYokaiKbn <= CommonConstants.NUMBER_0) {
            return ldRet;
        }
        // com_msc_gaibu_gendo
        GaibuGendoByCriteriaInEntity criteria = new GaibuGendoByCriteriaInEntity();
        // 日付
        criteria.setAsYmd(asYmd);
        // 要介護状態区分
        criteria.setAiYokaiKbn(CommonDtoUtil.objValToString(aiYokaiKbn));

        // 外部利用限度額を取得
        List<GaibuGendoOutEntity> gaibuGendoOutEntityList = this.comMscGaibuGendoSelectMapper
                .findGaibuGendoByCriteria(criteria);

        if (!CollectionUtils.isNullOrEmpty(gaibuGendoOutEntityList)) {
            ldRet = gaibuGendoOutEntityList.get(0).getGaibuGendo();
        }

        return ldRet;
    }

    /**
     * f_cmn_is_ch_gaibu_sv : 特定施設入居者生活介護で外部サービス利用型かどうか
     * 
     * @param alSvj 調査対象の sv_jigyo_id
     * @return １ = 外部サービス利用型 だった ０ = それ以外
     * <AUTHOR>
     */
    public Integer getCmnIsChGaibuSv(Integer alSvj) {
        Integer liRet = 0;
        Integer liChk = 0;
        ChsGaibuFlgByCriteriaInEntity chsGaibuFlgByCriteriaInEntity = new ChsGaibuFlgByCriteriaInEntity();
        // 調査対象の sv_jigyo_id
        chsGaibuFlgByCriteriaInEntity.setAlSvj(CommonDtoUtil.objValToString(alSvj));
        // DAOを実行
        List<ChsGaibuFlgOutEntity> chsGaibuFlgList = comMscSvjigyoSelectMapper
                .findChsGaibuFlgByCriteria(chsGaibuFlgByCriteriaInEntity);
        // チェック
        if (!CollectionUtils.isNullOrEmpty(chsGaibuFlgList)) {
            liChk = chsGaibuFlgList.getFirst().getChsGaibuFlg();
            if (liChk == null) {
                liChk = 0;
            }
        }
        if (liChk > 0) {
            liRet = liChk;
        } else {
            liRet = 0;
        }
        return liRet;
    }

    /**
     * f_cmn_chk_sougou_svjigyo : サービス事業所IDから総合事業かどうかをチェックする
     * 
     * @param alSvJigyoId サービス事業所ID
     * @return False…総合事業ではない True…総合事業サービスである
     * <AUTHOR>
     */
    public boolean chkSougouSvjigyo(Integer alSvJigyoId) {
        String lsSvtype = StringUtils.EMPTY;
        ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity comMscSvjigyoNameFirstSvKindCdByCriteriaInEntity = new ComMscSvjigyoNameFirstSvKindCdByCriteriaInEntity();
        // サービス事業所ID
        comMscSvjigyoNameFirstSvKindCdByCriteriaInEntity.setAlSvj(alSvJigyoId);
        // DAOを実行
        List<ComMscSvjigyoNameFirstSvKindCdOutEntity> comMscSvjigyoNameFirstSvKindCdList = comMscSvjigyoNameFirstSvKindCdSelectMapper
                .findComMscSvjigyoNameFirstSvKindCdByCriteria(comMscSvjigyoNameFirstSvKindCdByCriteriaInEntity);
        // チェック
        if (!CollectionUtils.isNullOrEmpty(comMscSvjigyoNameFirstSvKindCdList)) {
            lsSvtype = comMscSvjigyoNameFirstSvKindCdList.getFirst().getSvKindCd();
            if (lsSvtype == null) {
                lsSvtype = CommonConstants.SV_TYPE_00;
            }
        }
        return this.chkSougouSvtype(lsSvtype);
    }

    /**
     * f_cmn_get_gendo : 区分支給限度額を取得する
     * 
     * @param aiYokaiKbn   : 要介護度区分（ＮＤ独自）
     * @param asYmd        : 対象年月 (yyyy/mm or yyyymm)
     * @param abReadmaster : 対象年月が最新の区分支給限度額の期間の場合に初期設定マスタから取得するか true 取得する false
     *                     取得しない(固定値にする)
     * @param abGet201403  : 対象年月日が2014/03以前の場合に、どの時点の区分支給限度額を取得するか true
     *                     2014/03までの区分支給限度額を取得する false 2019/09までの区分支給限度額を取得する ※
     *                     区分支給限度を扱う箇所によって仕様が異なるため本フラグを追加
     * 
     * @return 限度額
     * <AUTHOR>
     */
    public Integer getGendo(Integer aiYokaiKbn, String asYmd, boolean abReadmaster, boolean abGet201403) {
        Integer[] llGendo = new Integer[21];
        Integer liKaigotypeNo;
        Integer llRet = 1;
        // 日付の形式をyyyy/mmにする
        asYmd = convertToYearMonth(asYmd);
        if (asYmd != null) {
            // 対象年月の区分支給限度額をセットする
            if (asYmd.compareTo("2019/10") >= 0) {
                llGendo = new Integer[] { 0, 0, 16765, 19705, 27048, 30938, 36217, 0, 0, 0, 5032, 10531, 0, 0, 0, 0, 0,
                        0, 0, 0, 5032 };
            } else if (asYmd.compareTo("2015/04") >= 0) {
                llGendo = new Integer[] { 0, 0, 16692, 19616, 26931, 30806, 36065, 0, 0, 0, 5003, 10473, 0, 0, 0, 0, 0,
                        0, 0, 0, 5003 };
            } else if (asYmd.compareTo("2014/04") >= 0) {
                llGendo = new Integer[] { 0, 0, 16692, 19616, 26931, 30806, 36065, 0, 0, 0, 5003, 10473, 0, 0, 0, 0, 0,
                        0, 0, 0, 0 };
            } else {
                // 対象年月が2014/03以前の場合は呼出元で指定したフラグによって取得する限度額を切り替える
                if (abGet201403) {
                    llGendo = new Integer[] { 0, 6150, 16580, 19480, 26750, 30600, 35830, 0, 0, 0, 4970, 10400, 0, 0, 0,
                            0, 0, 0, 0, 0, 0 };
                } else {
                    llGendo = new Integer[] { 0, 0, 16692, 19616, 26931, 30806, 36065, 0, 0, 0, 5003, 10473, 0, 0, 0, 0,
                            0, 0, 0, 0, 0 };
                }
            }
        }
        // 引数の要介護度区分が正しい値かチェック
        if (aiYokaiKbn < 0 || aiYokaiKbn >= llGendo.length - 1)
            return 0;
        // ab_readmasterがtrueで、対象年月が最新の区分支給限度額の期間の場合は初期設定マスタの値をセットする
        // それ以外の場合は、固定値をセットする
        if (abReadmaster == true && asYmd != null && asYmd.compareTo("2019/10") >= 0) {
            // 検索するキー(KAIGO_TYPE1～9)の番号を作成
            // 要支援1,2(11,12)、事業対象者(21)の場合は値を変換
            if (aiYokaiKbn == 11 || aiYokaiKbn == 12) {
                liKaigotypeNo = aiYokaiKbn - 3;
            } else {
                liKaigotypeNo = aiYokaiKbn;
            }
            // 検索するキーがKAIGO_TYPE1～9となる場合、初期設定マスタから取得しセットする
            // それ以外の場合、固定値をセットする
            if (liKaigotypeNo >= 1 && liKaigotypeNo <= 9) {
                String lsKey = "KAIGO_TYPE" + liKaigotypeNo;
                F3gkGetProfileInDto inDto = new F3gkGetProfileInDto();
                inDto.setShokuId(Integer.valueOf(CommonConstants.STR_ZERO)); // 職員ID
                inDto.setHoujinId(Integer.valueOf(CommonConstants.STR_ZERO));// 法人ID
                inDto.setShisetuId(Integer.valueOf(CommonConstants.STR_ZERO));// 施設ID
                inDto.setSvJigyoId(Integer.valueOf(CommonConstants.STR_ZERO));// 事業所ID
                inDto.setKinounameKnj(CommonConstants.SCREEN_NAME_KHN);// 画面名
                inDto.setSectionKnj(CommonConstants.KNJ_DEFAULT_VALUE);// セクション
                inDto.setKeyKnj(lsKey);
                inDto.setAsDefault(llGendo[aiYokaiKbn] + "");// 初期値
                llRet = Integer.parseInt(nds3GkFunc01Logic.getF3gkProfile(inDto));
            } else {
                llRet = llGendo[aiYokaiKbn];
            }
        } else {
            llRet = llGendo[aiYokaiKbn];
        }
        // セットした区分支給限度額を返却
        return llRet;
    }

    /**
     * convertToYearMonth : ユーザー設定による月変換
     * 
     * @param asYmd : 対象年月 (yyyy/mm or yyyymm)
     * @return 対象年月 (yyyy/mm)
     * <AUTHOR>
     */
    public static String convertToYearMonth(String asYmd) {
        // 空入力を処理する
        if (asYmd == null || asYmd.trim().isEmpty()) {
            return null;
        }
        String year = null;
        String month = null;
        // ケース1：数字のみの形式 (yyyyMM)
        if (asYmd.matches("\\d{6}")) {
            year = asYmd.substring(0, 4);
            month = asYmd.substring(4, 6);
        }
        // ケース2：区切り文字を含む形式
        else {
            Pattern pattern = Pattern.compile("^(\\d{4})\\D*(\\d{1,2}).*");
            Matcher matcher = pattern.matcher(asYmd);

            if (matcher.matches()) {
                year = matcher.group(1);
                month = matcher.group(2);
                // 一桁の月を補完する（5 → 05）
                if (month.length() == 1) {
                    month = "0" + month;
                }
            }
        }
        // 検証して結果を返す
        if (year != null && month != null) {
            try {
                int monthInt = Integer.parseInt(month);
                if (monthInt >= 1 && monthInt <= 12) {
                    return year + "/" + month;
                }
            } catch (NumberFormatException e) {
                // 月の変換に失敗しました
            }
        }
        return null; // 無効な入力または形式エラー
    }

    /**
     * f_cmn_set_adder_service_s : 加算サービス に値を入れる
     * 
     * @param ads01 対象u3gk_ds_base
     * @param asds  年月日 : yyyy/mm/dd
     * <AUTHOR>
     */
    public void setAdderServiceS(List<?> ads01, String asds) {
        try {
            String lsSvtype = StringUtils.EMPTY;
            if (!CollectionUtils.isNullOrEmpty(ads01)) {
                boolean isInt = false;
                Object adderSvObj = ads01.getFirst().getClass().getMethod(CommonConstants.GET_ADDER_SV)
                        .invoke(ads01.getFirst());
                if (Integer.class.equals(adderSvObj.getClass())) {
                    isInt = true;
                }
                for (Object item : ads01) {
                    String lsc = Objects.toString(item.getClass().getMethod(CommonConstants.GET_SCODE).invoke(item),
                            StringUtils.EMPTY);
                    String llSvJigyoId = Objects.toString(
                            item.getClass().getMethod(CommonConstants.GET_SV_JIGYO_ID).invoke(item), StringUtils.EMPTY);
                    Integer val = this.checkAdderService(lsc, asds, CommonDtoUtil.strValToInt(llSvJigyoId));
                    invokeMethod(item, CommonConstants.SET_ADDER_SV, isInt ? val : CommonDtoUtil.objValToString(val));
                    String lsSvcode = StringUtils.substring(lsc, CommonConstants.INT_2, CommonConstants.INT_4);
                    Integer liShogu = this.isShoguuKaizenKasan(asds, lsc);
                    if (liShogu > CommonConstants.INT_0) {
                        Integer valSv = CommonConstants.INT_2000 + liShogu;
                        invokeMethod(item, CommonConstants.SET_ADDER_SV,
                                isInt ? valSv : CommonDtoUtil.objValToString(valSv));
                    } else {
                        if (this.getKyoseiGenzan(asds, lsc) > CommonConstants.INT_0) {
                            // 共生型サービス：基本サービスを所定単位数に含めるため基本サービスより下に表示されるよう修正
                            invokeMethod(item, CommonConstants.SET_ADDER_SV,
                                    isInt ? CommonConstants.INT_900 : CommonConstants.STR_900);
                        } else {
                            lsSvtype = StringUtils.substring(lsc, CommonConstants.INT_0, CommonConstants.INT_2);
                            if (asds != null
                                    && asds.compareTo(CommonConstants.CREATION_DATE_20180401) >= CommonConstants.INT_0
                                    && CommonConstants.SV_TYPE_76.equals(lsSvtype)) {
                                if (CommonConstants.SCODE_76411100.equals(lsc)
                                        || CommonConstants.SCODE_76411200.equals(lsc)
                                        || CommonConstants.SCODE_76411300.equals(lsc)
                                        || CommonConstants.SCODE_76411400.equals(lsc)) {
                                    // 定期巡回の同一建物減算は単位数かつ特別地域加算等の所定所定単位数に含めるため
                                    // 小計行と率加算の間に表示されるよう修正
                                    invokeMethod(item, CommonConstants.SET_ADDER_SV,
                                            isInt ? CommonConstants.INT_0 : CommonConstants.STR_0);
                                }
                            }
                        }
                        // 感染症特例加算
                        if (this.isKansenTokureiKasan(asds, lsc) > CommonConstants.INT_0) {
                            invokeMethod(item, CommonConstants.SET_ADDER_SV,
                                    isInt ? CommonConstants.INT_800 : CommonConstants.STR_800);
                        }
                        // 訪問介護特定事業所加算Ⅴ
                        if (this.isTokuteiJigyoKasan(asds, lsc) > CommonConstants.INT_0) {
                            invokeMethod(item, CommonConstants.SET_ADDER_SV,
                                    isInt ? CommonConstants.INT_810 : CommonConstants.STR_810);
                        }
                        // 通所リハ継続減算
                        if (this.isRihaKeizokuGenzan(asds, lsc) > CommonConstants.INT_0) {
                            invokeMethod(item, CommonConstants.SET_ADDER_SV,
                                    isInt ? CommonConstants.INT_810 : CommonConstants.STR_810);
                        }
                        // 延長サービス加算が共生型に含まれるようにする必要がある
                        if (CommonConstants.SVTYPE_SUB_LIST_2.contains(lsSvtype)) {
                            if (CommonConstants.SV_CODE_LIST_4.contains(lsSvcode)) {
                                invokeMethod(item, CommonConstants.SET_ADDER_SV,
                                        isInt ? CommonConstants.INT_810 : CommonConstants.STR_810);
                            }
                        }
                        // 特別地域、小規模事業所加算が中山間より下に表示されるように指定
                        if (CommonConstants.SV_CODE_LIST_2.contains(lsSvtype)) {
                            if (this.getChusankanRiyo(lsc, asds) == CommonConstants.INT_1
                                    || this.getSpecialPlaceNew(asds, lsc)) {
                                invokeMethod(item, CommonConstants.SET_ADDER_SV,
                                        isInt ? CommonConstants.INT_900 : CommonConstants.STR_900);
                            } else if (this.getChusankanRiyo(lsc, asds) == CommonConstants.INT_2) {
                                invokeMethod(item, CommonConstants.SET_ADDER_SV,
                                        isInt ? CommonConstants.INT_910 : CommonConstants.STR_910);
                            }
                        }
                        // 高齢者虐待防止未実施減算
                        if (this.isGyakutaiBousiGenzan(asds, lsc) > CommonConstants.INT_0) {
                            invokeMethod(item, CommonConstants.SET_ADDER_SV,
                                    isInt ? CommonConstants.INT_790 : CommonConstants.STR_790);
                        }
                        // 業務継続計画未策定減算
                        if (this.isGyomuKeizokuGenzan(asds, lsc) > CommonConstants.INT_0) {
                            invokeMethod(item, CommonConstants.SET_ADDER_SV,
                                    isInt ? CommonConstants.INT_800 : CommonConstants.STR_800);
                        }
                        // 過少サービス減算
                        if (this.isKashouServiceGenzan(asds, lsc) > CommonConstants.INT_0) {
                            invokeMethod(item, CommonConstants.SET_ADDER_SV,
                                    isInt ? CommonConstants.INT_810 : CommonConstants.STR_810);
                        }
                        // サテライト体制未整備減算
                        if (this.isSatelliteMiseibiGenzan(asds, lsc) > CommonConstants.INT_0) {
                            invokeMethod(item, CommonConstants.SET_ADDER_SV,
                                    isInt ? CommonConstants.INT_820 : CommonConstants.STR_820);
                        }
                        // 身体拘束廃止未実施減算
                        if (this.isShintaiKousokuGenzan(asds, lsc) > CommonConstants.INT_0) {
                            invokeMethod(item, CommonConstants.SET_ADDER_SV,
                                    isInt ? CommonConstants.STR_780 : CommonConstants.STR_780);
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * エンティティクラスのSetterメソッドを実行する
     * 
     * @param target     DAOエンティティクラス
     * @param methodName メソッド名
     * @param args       引数
     */
    private void invokeMethod(Object target, String methodName, Object... args) {
        try {
            // 引数の型を取得
            Class<?>[] paramTypes = new Class<?>[args.length];
            for (int i = 0; i < args.length; i++) {
                paramTypes[i] = args[i].getClass();
            }

            // メソッドを取得
            Method method = target.getClass().getMethod(methodName, paramTypes);

            // メソッドを実行
            method.invoke(target, args);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke method: " + methodName, e);
        }
    }

    /**
     * 文字列中のすべての"/"を除去し、nullの場合も処理する
     * 
     * @param str null可能性のある文字列
     * @return "/"を除去した文字列。入力がnullの場合はnullを返す
     */
    public static String removeSlash(String str) {
        // 文字列がnullの場合は、直接nullを返す
        if (str == null) {
            return null;
        }
        // 非null文字列：すべての"/"を空文字列に置換（除去する効果）
        return str.replace("/", "");
    }

    /**
     * f_cmn_is_chk_one_of_month : 月１回のサービス・加算かどうか
     * 
     * @param asScd       調査対象の scode
     * @param alItm       調査対象の itemcode
     * @param asYmd       対象年月
     * @param alSvJigyoId サービス事業者ID
     * @return ０=複数回／月 が可能 ｎ=１ヶ月に出来る回数の上限
     * <AUTHOR>
     */
    public Integer isCheckOneOfMonth(String asScd, Integer alItm, String asYmd, Integer alSvJigyoId) {
        Integer liRet = CommonConstants.INT_0;
        Integer liMod;
        String lsSvt, lsSvcode;
        Integer liSanteiTani = null;
        Integer liKaisuNisu = null;
        Integer liKikanJiki = null;
        lsSvt = asScd.substring(CommonConstants.INT_0, CommonConstants.INT_2);

        lsSvcode = ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_4);
        liMod = CommonDtoUtil.strValToInt(lsSvcode) % CommonConstants.INT_2;
        if (CommonDtoUtil.strValToInt(removeSlash(asYmd)) <= CommonDtoUtil
                .strValToInt(removeSlash(CommonConstants.YMD_20150331))) {
            switch (lsSvt) {
                case CommonConstants.STR_1:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_4001:

                        case CommonConstants.STR_NUM_4002:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_13:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_3100:
                        case CommonConstants.STR_NUM_3111:
                        case CommonConstants.STR_NUM_3113:
                        case CommonConstants.STR_NUM_3115:
                        case CommonConstants.STR_NUM_3117:
                        case CommonConstants.STR_NUM_3200:
                        case CommonConstants.STR_NUM_4000:
                        case CommonConstants.STR_NUM_4001:
                        case CommonConstants.STR_NUM_4002:
                        case CommonConstants.STR_NUM_4004:
                        case CommonConstants.STR_NUM_6102:
                        case CommonConstants.STR_NUM_7000:
                            liRet = CommonConstants.INT_1;
                            break;

                    }
                    break;
                case CommonConstants.STR_15:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_5605:
                        case CommonConstants.STR_NUM_5606:
                            liRet = CommonConstants.INT_2;
                            break;

                    }
                    break;
                case CommonConstants.STR_NUM_16:
                    if (CommonDtoUtil.strValToInt(asYmd) >= CommonDtoUtil.strValToInt(CommonConstants.YMD_20090401)) {
                        switch (lsSvcode) {
                            case CommonConstants.STR_NUM_5400:
                            case CommonConstants.STR_NUM_5601:
                                liRet = CommonConstants.INT_1;
                            case CommonConstants.STR_NUM_5605:
                            case CommonConstants.STR_NUM_5606:
                                liRet = CommonConstants.INT_2;
                                break;

                        }
                        break;
                    } else {
                        switch (lsSvcode) {
                            case CommonConstants.STR_NUM_5400:
                                liRet = CommonConstants.INT_1;
                            case CommonConstants.STR_NUM_5605:
                            case CommonConstants.STR_NUM_5606:
                                liRet = CommonConstants.INT_2;
                                break;

                        }
                    }
                    break;
                case CommonConstants.STR_NUM_17:
                    liRet = CommonConstants.INT_0;
                    break;
                case CommonConstants.STR_NUM_22:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_6000:
                        case CommonConstants.STR_NUM_9000:
                            liRet = CommonConstants.INT_3;

                            break;

                    }
                    break;
                case CommonConstants.STR_NUM_31:
                    if (CommonDtoUtil.strValToInt(asYmd) >= CommonDtoUtil.strValToInt(CommonConstants.YMD_20120401)) {
                        if (asScd.substring(CommonConstants.INT_4).equals(CommonConstants.STR_NUM_3111)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1221)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1222)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1251)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1252)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1131)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1132)
                                || lsSvcode.equals(CommonConstants.STR_NUM_2111)
                                || lsSvcode.equals(CommonConstants.STR_NUM_2112)) {
                            liRet = CommonConstants.INT_2;
                        } else if (lsSvcode.equals(CommonConstants.STR_NUM_1223)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1224)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1225)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1226)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1241)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1243)

                        ) {
                            liRet = CommonConstants.INT_4;

                        } else if (lsSvcode.equals(CommonConstants.STR_NUM_1253)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1254)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1255)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1256)) {
                            liRet = CommonConstants.INT_8;
                        }
                    } else if (CommonDtoUtil.strValToInt(asYmd) >= CommonDtoUtil
                            .strValToInt(CommonConstants.YMD_20090401)) {
                        if (asScd.substring(CommonConstants.INT_4).equals(CommonConstants.STR_NUM_3111)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1221)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1222)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1251)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1252)) {
                            liRet = CommonConstants.INT_2;
                        } else if (lsSvcode.equals(CommonConstants.STR_NUM_1223)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1224)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1225)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1226)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1241)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1243)

                        ) {
                            liRet = CommonConstants.INT_4;

                        } else if (lsSvcode.equals(CommonConstants.STR_NUM_1253)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1254)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1255)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1256)) {
                            liRet = CommonConstants.INT_8;
                        }
                    } else {
                        if (asScd.substring(CommonConstants.INT_4).equals(CommonConstants.STR_NUM_3111)) {
                            liRet = CommonConstants.INT_2;
                        }
                    }
                    break;
                case CommonConstants.STR_NUM_32:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_6144:
                        case CommonConstants.STR_NUM_6502:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_33:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_6123:
                        case CommonConstants.STR_NUM_6127:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_34:
                    if (CommonDtoUtil.strValToInt(asYmd) >= CommonDtoUtil.strValToInt(CommonConstants.YMD_20120401)) {
                        if (asScd.substring(CommonConstants.INT_4).equals(CommonConstants.STR_NUM_3411)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1221)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1222)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1251)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1252)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1131)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1132)
                                || lsSvcode.equals(CommonConstants.STR_NUM_2111)
                                || lsSvcode.equals(CommonConstants.STR_NUM_2112)) {
                            liRet = CommonConstants.INT_2;
                        } else if (lsSvcode.equals(CommonConstants.STR_NUM_1223)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1224)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1225)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1226)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1241)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1243)

                        ) {
                            liRet = CommonConstants.INT_4;

                        } else if (lsSvcode.equals(CommonConstants.STR_NUM_1253)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1254)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1255)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1256)) {
                            liRet = CommonConstants.INT_8;
                        }
                    } else if (CommonDtoUtil.strValToInt(asYmd) >= CommonDtoUtil
                            .strValToInt(CommonConstants.YMD_20090401)) {
                        if (asScd.substring(CommonConstants.INT_4).equals(CommonConstants.STR_NUM_3411)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1221)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1222)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1251)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1252)) {
                            liRet = CommonConstants.INT_2;
                        } else if (lsSvcode.equals(CommonConstants.STR_NUM_1223)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1224)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1225)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1226)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1241)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1243)

                        ) {
                            liRet = CommonConstants.INT_4;

                        } else if (lsSvcode.equals(CommonConstants.STR_NUM_1253)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1254)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1255)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1256)) {
                            liRet = CommonConstants.INT_8;
                        }
                    } else {
                        if (asScd.substring(CommonConstants.INT_4).equals(CommonConstants.STR_NUM_3411)) {
                            liRet = CommonConstants.INT_2;
                        }
                    }
                    break;
                case CommonConstants.STR_NUM_35:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6123) || lsSvcode.equals(CommonConstants.STR_NUM_1311)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1321)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1332)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1711)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1712)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1722)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1723)

                            || lsSvcode.equals(CommonConstants.STR_NUM_1724)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1725)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1726)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1727)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1728)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1811)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1812)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1821)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1822)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1823)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1824)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1825)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1826)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1827)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1831)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1831)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1832)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2142)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2143)) {
                        liRet = CommonConstants.INT_2;
                    }
                    break;
                case CommonConstants.STR_NUM_36:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_6123:
                        case CommonConstants.STR_NUM_6127:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_37:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_6502:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_38:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_6502:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_39:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_6502:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_61:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4001) || lsSvcode.equals(CommonConstants.STR_NUM_4002)
                            || asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_611)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_63:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_3100:
                        case CommonConstants.STR_NUM_3200:
                        case CommonConstants.STR_NUM_4000:
                        case CommonConstants.STR_NUM_4001:
                        case CommonConstants.STR_NUM_4002:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_65:
                    if (asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_655)
                            || asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_656)
                                    && asScd.substring(CommonConstants.INT_5).equals(CommonConstants.STR_NUM_65611)
                            || (ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_6, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_1)
                                    && !ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                                            .equals(CommonConstants.STR_8))
                            || lsSvcode.equals(CommonConstants.STR_NUM_8001)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8011)

                    ) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_66:
                    if (asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_665)
                            || asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_666)
                                    && asScd.substring(CommonConstants.INT_5).equals(CommonConstants.STR_NUM_66611)
                            || (ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_6, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_1)
                                    && !ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                                            .equals(CommonConstants.STR_8))
                            || lsSvcode.equals(CommonConstants.STR_NUM_8001)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8011)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8201)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8211)

                    ) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_67:
                    liRet = CommonConstants.INT_0;
                    break;
                case CommonConstants.STR_NUM_71:
                    if (asScd.substring(CommonConstants.INT_4).equals(CommonConstants.STR_NUM_7170) || (asScd
                            .substring(CommonConstants.INT_5).equals(CommonConstants.STR_NUM_71710)
                            || (asScd.equals(CommonConstants.STR_NUM_71111100)
                                    || asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_716)
                                            && asScd.substring(CommonConstants.INT_3)
                                                    .equals(CommonConstants.STR_NUM_713)
                                            && asScd.substring(CommonConstants.INT_3)
                                                    .equals(CommonConstants.STR_NUM_7172)
                                            && asScd.substring(CommonConstants.INT_3)
                                                    .equals(CommonConstants.STR_NUM_7173)
                                            && asScd.substring(CommonConstants.INT_3)
                                                    .equals(CommonConstants.STR_NUM_712)

                            ))) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_72:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_5606)
                            || lsSvcode.equals(CommonConstants.STR_NUM_5607)) {
                        liRet = CommonConstants.INT_2;
                    }
                    break;
                case CommonConstants.STR_NUM_73:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6128) || lsSvcode.equals(CommonConstants.STR_NUM_6138)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6140)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6101)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6102)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6103)
                            || (CommonDtoUtil
                                    .strValToInt(lsSvcode) >= (CommonDtoUtil.strValToInt(CommonConstants.STR_NUM_7000))
                                    && CommonDtoUtil.strValToInt(
                                            lsSvcode) <= (CommonDtoUtil.strValToInt(CommonConstants.STR_NUM_7010)))
                            || (liMod == CommonConstants.INT_1 && !((CommonDtoUtil
                                    .strValToInt(lsSvcode) >= (CommonDtoUtil.strValToInt(CommonConstants.STR_NUM_7011))
                                    && CommonDtoUtil.strValToInt(
                                            lsSvcode) <= (CommonDtoUtil.strValToInt(CommonConstants.STR_NUM_7020)))))

                    ) {
                        liRet = CommonConstants.INT_2;
                    } else if (lsSvcode.equals(CommonConstants.STR_NUM_6300)) {
                        liRet = 30;
                    }
                    break;
                case CommonConstants.STR_NUM_74:
                    if (asScd.substring(CommonConstants.INT_5).equals(CommonConstants.STR_NUM_74560)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_75:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6140) || lsSvcode.equals(CommonConstants.STR_NUM_6101)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6102)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6103) || liMod == CommonConstants.INT_1

                    ) {
                        liRet = CommonConstants.INT_1;
                    } else if (lsSvcode.equals(CommonConstants.STR_NUM_6300)) {
                        liRet = 30;
                    }
                    break;
                case CommonConstants.STR_NUM_76:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_3100) || lsSvcode.equals(CommonConstants.STR_NUM_4000)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4001)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6100)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6101)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6102)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6103)
                            || ((liMod == CommonConstants.INT_1)
                                    && (asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_761))
                                    || (asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_762))
                                    || asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_767))

                    ) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_77:

                    if (lsSvcode.equals(CommonConstants.STR_NUM_6128) || lsSvcode.equals(CommonConstants.STR_NUM_6129)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6139)
                            || lsSvcode.equals(CommonConstants.STR_NUM_3100)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4000)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4001)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6100)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6101)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6102)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6103)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6104)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6106)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6106)
                            || ((liMod == CommonConstants.INT_1)
                                    && (asScd.substring(CommonConstants.INT_5).equals(CommonConstants.STR_NUM_77601))
                                    && !(asScd.equals(CommonConstants.STR_NUM_77400300))))

                    {
                        liRet = CommonConstants.INT_1;
                    }
                    break;

            }
            if (CommonDtoUtil.strValToInt(lsSvt) >= CommonConstants.INT_80) {
                liRet = CommonConstants.INT_0;
            }

        } else {

            switch (lsSvt) {
                case CommonConstants.STR_NUM_11:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_4001:
                        case CommonConstants.STR_NUM_4002:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_4003:
                            liRet = CommonConstants.INT_1;
                            break;

                    }

                    break;
                case CommonConstants.STR_NUM_13:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_3100:
                        case CommonConstants.STR_NUM_3111:
                        case CommonConstants.STR_NUM_3113:
                        case CommonConstants.STR_NUM_3115:
                        case CommonConstants.STR_NUM_3117:
                        case CommonConstants.STR_NUM_3200:
                        case CommonConstants.STR_NUM_4000:
                        case CommonConstants.STR_NUM_4001:
                        case CommonConstants.STR_NUM_4002:
                        case CommonConstants.STR_NUM_4004:
                        case CommonConstants.STR_NUM_4005:
                        case CommonConstants.STR_NUM_6102:
                        case CommonConstants.STR_NUM_7000:

                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_4010:
                            liRet = CommonConstants.INT_1;
                            break;

                    }
                case CommonConstants.STR_NUM_14:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_5004:
                        case CommonConstants.STR_NUM_5005:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_5006:
                        case CommonConstants.STR_NUM_5007:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_15:
                case CommonConstants.STR_NUM_78:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_5605:
                        case CommonConstants.STR_NUM_5606:
                            liRet = CommonConstants.INT_2;
                            break;
                    }
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_4002:
                        case CommonConstants.STR_NUM_4003:
                        case CommonConstants.STR_NUM_6201:
                        case CommonConstants.STR_NUM_6340:
                        case CommonConstants.STR_NUM_6341:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_16:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_5601:
                        case CommonConstants.STR_NUM_5608:
                        case CommonConstants.STR_NUM_5609:
                        case CommonConstants.STR_NUM_6254:
                        case CommonConstants.STR_NUM_6255:
                        case CommonConstants.STR_NUM_6256:
                            liRet = CommonConstants.INT_1;
                        case CommonConstants.STR_NUM_5605:
                        case CommonConstants.STR_NUM_5606:
                            liRet = CommonConstants.INT_2;
                            break;
                    }
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_5615:
                        case CommonConstants.STR_NUM_5616:
                        case CommonConstants.STR_NUM_5617:
                        case CommonConstants.STR_NUM_5618:
                        case CommonConstants.STR_NUM_6201:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_17:
                    liRet = CommonConstants.INT_0;
                    break;
                case CommonConstants.STR_NUM_21:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_4002:
                        case CommonConstants.STR_NUM_4003:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_22:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_6000:
                        case CommonConstants.STR_NUM_9000:
                            liRet = CommonConstants.INT_3;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_24:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_4002:
                        case CommonConstants.STR_NUM_4003:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_25:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_6000:
                        case CommonConstants.STR_NUM_9000:
                            liRet = CommonConstants.INT_3;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_2A:
                case CommonConstants.STR_NUM_2B:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_6000:
                            liRet = CommonConstants.INT_3;
                            break;
                    }

                    break;
                case CommonConstants.STR_NUM_31:
                    if ((asScd.substring(CommonConstants.INT_4).equals(CommonConstants.STR_NUM_3111)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1221)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1222)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1251)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1252)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1131)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1132)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2111)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2112))) {
                        liRet = CommonConstants.INT_2;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1223) || lsSvcode.equals(CommonConstants.STR_NUM_1224)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1225)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1226)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1241)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1243)) {
                        liRet = CommonConstants.INT_4;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1253) || lsSvcode.equals(CommonConstants.STR_NUM_1254)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1255)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1256)) {
                        liRet = CommonConstants.INT_8;
                    }

                    if ((lsSvcode.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_126)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1244)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1245)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2113))) {
                        liRet = CommonConstants.INT_2;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1246) || lsSvcode.equals(CommonConstants.STR_NUM_1247)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1250)) {
                        liRet = CommonConstants.INT_4;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1248)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1249)) {
                        liRet = CommonConstants.INT_8;
                    }
                    break;
                case CommonConstants.STR_NUM_32:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_6144:
                        case CommonConstants.STR_NUM_6502:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4002) || lsSvcode.equals(CommonConstants.STR_NUM_6122)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6201)) {
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6400)) {
                        liRet = CommonConstants.INT_6;
                    }
                    break;
                case CommonConstants.STR_NUM_33:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_6123:
                        case CommonConstants.STR_NUM_6127:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4002) || lsSvcode.equals(CommonConstants.STR_NUM_4003)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6122)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6201)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_34:
                    if ((asScd.substring(CommonConstants.INT_4).equals(CommonConstants.STR_NUM_3411)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1221)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1222)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1251)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1252)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1131)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1132)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2111)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2112))) {
                        liRet = CommonConstants.INT_2;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1223) || lsSvcode.equals(CommonConstants.STR_NUM_1224)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1225)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1226)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1241)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1242)) {
                        liRet = CommonConstants.INT_4;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1253) || lsSvcode.equals(CommonConstants.STR_NUM_1254)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1255)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1256)) {
                        liRet = CommonConstants.INT_8;
                    }

                    if (lsSvcode.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_126)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1271)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1272)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2113)) {
                        liRet = CommonConstants.INT_2;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1243) || lsSvcode.equals(CommonConstants.STR_NUM_1273)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1274)) {
                        liRet = CommonConstants.INT_4;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1275)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1276)) {
                        liRet = CommonConstants.INT_8;
                    }
                    break;
                case CommonConstants.STR_NUM_35:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6123) || lsSvcode.equals(CommonConstants.STR_NUM_1311)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1321)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1332)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1711)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1712)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1722)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1723)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1724)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1725)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1726)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1727)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1728)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1811)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1812)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1821)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1822)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1823)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1824)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1825)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1826)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1827)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1831)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1832)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2142)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2143)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2211)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2221)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2232)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2311)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2313)) {
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1835) || lsSvcode.equals(CommonConstants.STR_NUM_1836)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4002)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4003)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6122)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6201)) {
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_2322) || lsSvcode.equals(CommonConstants.STR_NUM_2323)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2324)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2325)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2326)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2327)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2328)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_36:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_6123:
                        case CommonConstants.STR_NUM_6127:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4002) || lsSvcode.equals(CommonConstants.STR_NUM_4003)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6122)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6201)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_37:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_6502:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4002) || lsSvcode.equals(CommonConstants.STR_NUM_6122)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6201)) {
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6400)) {
                        liRet = CommonConstants.INT_6;
                    }
                    break;

                case CommonConstants.STR_NUM_39:
                    switch (lsSvcode)

                    {
                        case CommonConstants.STR_NUM_4002:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_61:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4001) || lsSvcode.equals(CommonConstants.STR_NUM_4002)
                            || (asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_611))) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_63:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_3100:
                        case CommonConstants.STR_NUM_3200:
                        case CommonConstants.STR_NUM_4000:
                        case CommonConstants.STR_NUM_4001:
                        case CommonConstants.STR_NUM_4002:
                        case CommonConstants.STR_NUM_4005:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_64:
                    switch (lsSvcode) {
                        case CommonConstants.STR_NUM_5005:
                        case CommonConstants.STR_NUM_5615:
                            liRet = CommonConstants.INT_1;
                            break;
                    }
                    break;
                case CommonConstants.STR_NUM_65:
                    if ((asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_655))
                            || ((asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_656))
                                    && (asScd.substring(CommonConstants.INT_5) != CommonConstants.STR_NUM_65611))
                            || ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_6, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_1)
                                    && (ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3,
                                            CommonConstants.INT_1) != CommonConstants.STR_NUM_8))
                                    || (lsSvcode.equals(CommonConstants.STR_NUM_8001)
                                            || lsSvcode.equals(CommonConstants.STR_NUM_8011)))) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_66:
                    if (CommonDtoUtil.strValToInt(asYmd) <= CommonDtoUtil
                            .strValToInt(CommonConstants.STR_NUM_2018_03_31)) {
                        if ((asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_665))
                                || ((asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_666))
                                        && (asScd.substring(CommonConstants.INT_5) != CommonConstants.STR_NUM_66611))
                                || ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_6, CommonConstants.INT_1)
                                        .equals(CommonConstants.STR_NUM_1))
                                        && (ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3,
                                                CommonConstants.INT_1) != CommonConstants.STR_NUM_8))
                                || (lsSvcode.equals(CommonConstants.STR_NUM_6117)
                                        || lsSvcode.equals(CommonConstants.STR_NUM_6118)
                                        || lsSvcode.equals(CommonConstants.STR_NUM_8001)
                                        || lsSvcode.equals(CommonConstants.STR_NUM_8011)
                                        || lsSvcode.equals(CommonConstants.STR_NUM_8201)
                                        || lsSvcode.equals(CommonConstants.STR_NUM_8211))) {
                            liRet = CommonConstants.INT_1;
                        }

                        else {
                            switch (lsSvcode.substring(CommonConstants.INT_1)) {
                                case CommonConstants.STR_NUM_1:
                                case CommonConstants.STR_NUM_2:
                                case CommonConstants.STR_NUM_8:
                                case CommonConstants.STR_NUM_9:
                                    if (StringUtils.right(lsSvcode, CommonConstants.INT_1)
                                            .equals(CommonConstants.STR_NUM_1)
                                            && !lsSvcode.equals(CommonConstants.STR_NUM_8111)) {
                                        liRet = CommonConstants.INT_1;
                                    }
                                case CommonConstants.STR_NUM_5:
                                    liRet = CommonConstants.INT_1;
                                    break;
                                case CommonConstants.STR_NUM_6:
                                    if ((CommonDtoUtil.strValToInt(lsSvcode) >= CommonDtoUtil
                                            .strValToInt(CommonConstants.STR_NUM_6101))
                                            && CommonDtoUtil.strValToInt(lsSvcode) <= CommonDtoUtil
                                                    .strValToInt(CommonConstants.STR_NUM_6109)
                                            || (CommonDtoUtil.strValToInt(lsSvcode) >= CommonDtoUtil
                                                    .strValToInt(CommonConstants.STR_NUM_6117)
                                                    && CommonDtoUtil.strValToInt(lsSvcode) <= CommonDtoUtil
                                                            .strValToInt(CommonConstants.STR_NUM_6120)
                                                    || lsSvcode.equals(CommonConstants.STR_NUM_6255)
                                                    || lsSvcode.equals(CommonConstants.STR_NUM_6256)
                                                    || lsSvcode.equals(CommonConstants.STR_NUM_6201))) {
                                        liRet = CommonConstants.INT_1;
                                    }
                                case CommonConstants.STR_NUM_7:
                                    if ((CommonDtoUtil.strValToInt(lsSvcode) >= CommonDtoUtil
                                            .strValToInt(CommonConstants.STR_NUM_7001)
                                            && CommonDtoUtil.strValToInt(lsSvcode) <= CommonDtoUtil
                                                    .strValToInt((CommonConstants.STR_NUM_7018)))) {
                                        liRet = CommonConstants.INT_1;
                                    }
                                    break;

                            }
                        }
                    }
                    break;
                case CommonConstants.STR_NUM_67:
                    liRet = CommonConstants.INT_0;
                    break;
                case CommonConstants.STR_NUM_68:
                case CommonConstants.STR_NUM_69:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4002)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4003)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_71:
                    if ((asScd.substring(CommonConstants.INT_4).equals(CommonConstants.STR_NUM_7170)
                            || (asScd.substring(CommonConstants.INT_5).equals(CommonConstants.STR_NUM_71710))
                            || (asScd.equals(CommonConstants.STR_NUM_71111100))
                            || ((asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_716))
                                    && (asScd != CommonConstants.STR_NUM_71610000
                                            && asScd != CommonConstants.STR_NUM_71610100))
                            || ((asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_712)))
                                    && (liMod.equals(CommonConstants.INT_1))
                            || ((asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_713)))
                                    && (liMod.equals(CommonConstants.INT_1))
                            || ((asScd.substring(CommonConstants.INT_4).equals(CommonConstants.STR_NUM_7172)))
                                    && (liMod.equals(CommonConstants.INT_1))
                            || ((asScd.substring(CommonConstants.INT_4).equals(CommonConstants.STR_NUM_7173)
                                    && (liMod.equals(CommonConstants.INT_1)))))) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_72:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_5606)
                            || lsSvcode.equals(CommonConstants.STR_NUM_5607)) {
                        liRet = CommonConstants.INT_2;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4002) || lsSvcode.equals(CommonConstants.STR_NUM_4003)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6201)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_73:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4010) || lsSvcode.equals(CommonConstants.STR_NUM_6100)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6101)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6102)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6103)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6128)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6138)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6140)
                            || (CommonDtoUtil.strValToInt(lsSvcode)) >= CommonDtoUtil
                                    .strValToInt(CommonConstants.STR_NUM_7000)
                                    && CommonDtoUtil.strValToInt(lsSvcode) <= CommonDtoUtil
                                            .strValToInt(CommonConstants.STR_NUM_7010)
                            || ((liMod.equals(CommonConstants.INT_1)) && (!(CommonDtoUtil
                                    .strValToInt(lsSvcode) >= CommonDtoUtil.strValToInt(CommonConstants.STR_NUM_7011)
                                    && CommonDtoUtil.strValToInt(lsSvcode) <= CommonDtoUtil
                                            .strValToInt(CommonConstants.STR_NUM_7020))
                                    && lsSvcode != CommonConstants.STR_NUM_6311))) {
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6300)) {
                        liRet = 30;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4002) || lsSvcode.equals(CommonConstants.STR_NUM_4003)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6109)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6201)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_74:
                    if (asScd.substring(CommonConstants.INT_5).equals(CommonConstants.STR_NUM_74560)) {
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4002) || lsSvcode.equals(CommonConstants.STR_NUM_4003)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6201)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_75:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4010) || lsSvcode.equals(CommonConstants.STR_NUM_6100)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6101)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6102)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6103)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6140)
                            || ((liMod.equals(CommonConstants.INT_1) && lsSvcode != CommonConstants.STR_NUM_6311))) {
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6300)) {
                        liRet = 30;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4002) || lsSvcode.equals(CommonConstants.STR_NUM_4003)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6109)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6201)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_76:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_3100) || lsSvcode.equals(CommonConstants.STR_NUM_4000)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4001)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4010)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4111)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6100)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6101)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6102)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6103)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6111)
                            || ((liMod.equals(CommonConstants.INT_1)) && ((asScd.substring(CommonConstants.INT_3)
                                    .equals(CommonConstants.STR_NUM_761))
                                    || (asScd.substring(CommonConstants.INT_3).equals(CommonConstants.STR_NUM_762)
                                            || (asScd
                                                    .substring(CommonConstants.INT_3)
                                                    .equals(CommonConstants.STR_NUM_767)))))) {
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4012) || lsSvcode.equals(CommonConstants.STR_NUM_4013)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4113)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_77:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_3100) || lsSvcode.equals(CommonConstants.STR_NUM_4000)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4001)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4010)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4015)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6100)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6101)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6102)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6103)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6128)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6129)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6139)
                            || ((liMod.equals(CommonConstants.INT_1)
                                    && (asScd.substring(CommonConstants.INT_0,
                                            CommonConstants.INT_5) != CommonConstants.STR_NUM_77601)
                                    && (asScd != CommonConstants.STR_NUM_77400300)
                                    && (asScd != CommonConstants.STR_NUM_77631100)))) {
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4014)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_A1:
                    // 訪問型（みなし・独自）
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4001) || lsSvcode.equals(CommonConstants.STR_NUM_4002)
                            || (ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_1)
                                    && ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_4,
                                            CommonConstants.INT_1) != CommonConstants.STR_NUM_4)) {
                        liRet = CommonConstants.INT_1;
                    }
                    if ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                            .equals(CommonConstants.STR_NUM_2)
                            && ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_4, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_4))) {
                        // 訪問型サービスⅣ
                        liRet = CommonConstants.INT_4;
                    }
                    if ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                            .equals(CommonConstants.STR_NUM_2)
                            && ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_4, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_5))) {
                        // 訪問型サービスⅤ
                        liRet = CommonConstants.INT_8;
                    }
                    if ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                            .equals(CommonConstants.STR_NUM_2)
                            && ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_4, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_6))) {
                        // 訪問型サービスⅵ
                        liRet = CommonConstants.INT_12;
                    }
                    if ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                            .equals(CommonConstants.STR_NUM_1)
                            && ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_4, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_4))) {
                        // 訪問型短時間サービス
                        liRet = CommonConstants.INT_22;
                    }

                    if (lsSvcode.equals(CommonConstants.STR_NUM_4003)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_A2:
                    // 訪問型（みなし・独自）
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4001) || lsSvcode.equals(CommonConstants.STR_NUM_4002)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4011)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4012)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4021)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4022)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4031)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4032)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4041)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4042)
                            || (ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_1)
                                    && ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_4,
                                            CommonConstants.INT_1) != CommonConstants.STR_NUM_4)) {
                        liRet = CommonConstants.INT_1;
                    }
                    if ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                            .equals(CommonConstants.STR_NUM_2)
                            && ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_4, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_4))) {
                        // 訪問型サービスⅣ
                        liRet = CommonConstants.INT_4;
                    }
                    if ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                            .equals(CommonConstants.STR_NUM_2)
                            && ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_4, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_5))) {
                        // 訪問型サービスⅤ
                        liRet = CommonConstants.INT_8;
                    }
                    if ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                            .equals(CommonConstants.STR_NUM_2)
                            && ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_4, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_6))) {
                        // 訪問型サービスⅵ
                        liRet = CommonConstants.INT_12;
                    }
                    if ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                            .equals(CommonConstants.STR_NUM_1)
                            && ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_4, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_4))) {
                        // 訪問型短時間サービス
                        liRet = CommonConstants.INT_22;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4003) || lsSvcode.equals(CommonConstants.STR_NUM_4013)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4023)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4033)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4043)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_A5:
                    if ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                            .equals(CommonConstants.STR_NUM_5)
                            || ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_6))
                                    && (ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3,
                                            CommonConstants.INT_3) != CommonConstants.STR_NUM_611)
                                    && (lsSvcode != CommonConstants.STR_NUM_6100))
                            || ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_6, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_1))
                                    && (ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3,
                                            CommonConstants.INT_1) != CommonConstants.STR_NUM_8)
                                    && (lsSvcode != CommonConstants.STR_NUM_6111))
                            || (lsSvcode.equals(CommonConstants.STR_NUM_8001)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_8011)))) {
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1113) || lsSvcode.equals(CommonConstants.STR_NUM_8003)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9003)) {
                        liRet = CommonConstants.INT_4;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1123) || lsSvcode.equals(CommonConstants.STR_NUM_8013)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9013)) {
                        liRet = CommonConstants.INT_8;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4002) || lsSvcode.equals(CommonConstants.STR_NUM_4003)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6201)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_A6:
                    if ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                            .equals(CommonConstants.STR_NUM_5))
                            || ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_6))
                                    && (ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3,
                                            CommonConstants.INT_3) != CommonConstants.STR_NUM_611)
                                    && (lsSvcode != CommonConstants.STR_NUM_6100))
                            || ((ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_6, CommonConstants.INT_1)
                                    .equals(CommonConstants.STR_NUM_1))
                                    && (ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3,
                                            CommonConstants.INT_1) != CommonConstants.STR_NUM_8)
                                    && (ndsMidaLogic.fNdsMida(asScd, CommonConstants.INT_3,
                                            CommonConstants.INT_1) != CommonConstants.STR_NUM_9)
                                    && (lsSvcode != CommonConstants.STR_NUM_6111))
                            || (lsSvcode.equals(CommonConstants.STR_NUM_8001)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_8011)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_9001)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_9011)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_8004)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_8014)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_9004)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_9014)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_8007)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_8017)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_9007)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_9017)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_8021)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_8031)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_9021)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_9031)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_8024)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_8034)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_9024)
                                    || lsSvcode.equals(CommonConstants.STR_NUM_9034))) {
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1113) || lsSvcode.equals(CommonConstants.STR_NUM_8003)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9003)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1213)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8006)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9006)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1313)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8009)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9009)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1413)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8023)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9023)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1513)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8026)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9026)) {
                        liRet = CommonConstants.INT_4;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1123) || lsSvcode.equals(CommonConstants.STR_NUM_8013)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9013)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1223)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8016)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9016)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1323)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8019)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9019)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1423)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8033)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9033)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1523)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8036)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9036)) {
                        liRet = CommonConstants.INT_8;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4002) || lsSvcode.equals(CommonConstants.STR_NUM_4003)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6201)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4012)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4013)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6211)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4022)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4023)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6221)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4032)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4033)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6231)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4042)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4043)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6241)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                // 総合事業サービスの場合はマスタテーブルから取得する
                case CommonConstants.STR_A3:
                case CommonConstants.STR_A4:
                case CommonConstants.STR_A7:
                case CommonConstants.STR_A8:
                case CommonConstants.STR_A9:
                case CommonConstants.STR_AA:
                case CommonConstants.STR_AB:
                case CommonConstants.STR_AC:
                case CommonConstants.STR_AD: {
                    SanteiTaniKaisuByCriteriaInEntity criteria = new SanteiTaniKaisuByCriteriaInEntity();
                    criteria.setLsSvt(lsSvt);
                    criteria.setLsSvcode(lsSvcode);
                    criteria.setAlSvJigyoId(CommonDtoUtil.objValToString(alSvJigyoId));
                    criteria.setAsYmd(asYmd);
                    comMhcItemuseSougouSelectMapper.findSanteiTaniKaisuByCriteria(criteria);
                    if (liSanteiTani == null) {
                        liSanteiTani = CommonConstants.INT_0;
                    }
                    if (liKaisuNisu == null) {
                        liKaisuNisu = CommonConstants.INT_0;
                    }

                    if (liKikanJiki == null) {
                        liKikanJiki = CommonConstants.INT_0;
                    }
                    // 算定単位が「1回につき」「1日につき」
                    if (liSanteiTani == CommonConstants.INT_1 || liSanteiTani == CommonConstants.INT_2) {
                        // 算定回数制限期間が「1月につき」
                        if (liKikanJiki == CommonConstants.INT_1) {
                            liRet = liKaisuNisu;
                        }
                        // 算定単位が「1月につき」
                        if (liSanteiTani == CommonConstants.INT_3) {
                            liRet = CommonConstants.INT_1;
                        }
                    }
                }

                    break;

            }
            if (CommonDtoUtil.strValToInt(lsSvt) >= 80 && CommonDtoUtil.strValToInt(lsSvt) <= 90) {
                liRet = CommonConstants.INT_0;
            }

        }
        // R3改正で追加分
        switch (lsSvt) {
            case CommonConstants.STR_12:
                if (lsSvcode.equals(CommonConstants.STR_NUM_4113)) {
                    // 訪問入浴初回加算
                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_13:
                if (lsSvcode.equals(CommonConstants.STR_NUM_6104)) {
                    // 訪問看護サービス提供体制加算Ⅰ２
                    liRet = CommonConstants.INT_1;
                }
                break;

            case CommonConstants.STR_NUM_14:
                if (lsSvcode.equals(CommonConstants.STR_NUM_5008) || lsSvcode.equals(CommonConstants.STR_NUM_5009)) {
                    // 訪問リハマネジメント加算Ａ２
                    // 訪問リハマネジメント加算Ｂ２
                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_15:

                if (lsSvcode.equals(CommonConstants.STR_NUM_4001) || lsSvcode.equals(CommonConstants.STR_NUM_6116)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6202)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6338)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6339)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6361)) {
                    // 通所介護生活機能向上連携加算Ⅰ
                    // 通所介護栄養アセスメント加算
                    // 通所介護口腔栄養スクリーニング加算Ⅰ
                    // 通所介護ＡＤＬ維持等加算Ⅰ
                    // 通所介護ＡＤＬ維持等加算Ⅱ
                    // 通所介護科学的介護推進体制加算
                    liRet = CommonConstants.INT_1;
                }
                if (lsSvcode.equals(CommonConstants.STR_NUM_5608)) {
                    // 通所介護口腔機能向上加算Ⅱ
                    liRet = CommonConstants.INT_2;
                }
                // 個別機能訓練加算ⅡはR3/4以前は日単位の加算のため期間分岐が必要
                if (CommonDtoUtil.strValToInt(asYmd) >= CommonDtoUtil.strValToInt(CommonConstants.STR_NUM_2021_04_01)) {
                    if (lsSvcode.equals(CommonConstants.STR_NUM_5052)) {
                        // 通所介護個別機能訓練加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                }
                break;
            case CommonConstants.STR_NUM_16:
                if (lsSvcode.equals(CommonConstants.STR_NUM_5619) || lsSvcode.equals(CommonConstants.STR_NUM_5620)
                        || lsSvcode.equals(CommonConstants.STR_NUM_5621)
                        || lsSvcode.equals(CommonConstants.STR_NUM_5622)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6116)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6202)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6257)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6361)) {
                    // 通所リハマネジメント加算Ａ２１
                    // 通所リハマネジメント加算Ａ２２
                    // 通所リハマネジメント加算Ｂ２１
                    // 通所リハマネジメント加算Ｂ２２
                    // 通所リハ栄養アセスメント加算
                    // 通所リハ口腔栄養スクリーニング加算Ⅰ
                    // 通所リハ生活行為向上リハ加算
                    // 通所リハ科学的介護推進体制加算
                    liRet = CommonConstants.INT_1;
                }
                if (lsSvcode.equals(CommonConstants.STR_NUM_5626)) {
                    // 通所リハ口腔機能向上加算Ⅱ
                    liRet = CommonConstants.INT_2;
                }
                break;
            case CommonConstants.STR_NUM_21:
                if (lsSvcode.equals(CommonConstants.STR_NUM_4001)) {
                    // 短期生活機能向上連携加算Ⅰ
                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_24:
                if (lsSvcode.equals(CommonConstants.STR_NUM_4001)) {
                    // 予短期生活機能向上連携加算Ⅰ
                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_31:
                if (lsSvcode.equals(CommonConstants.STR_NUM_1257)) {
                    // 薬剤師居宅療養Ⅱ７
                    liRet = CommonConstants.INT_1;
                }
                if (lsSvcode.equals(CommonConstants.STR_NUM_1134) || lsSvcode.equals(CommonConstants.STR_NUM_1135)
                        || lsSvcode.equals(CommonConstants.STR_NUM_1136)) {
                    // 管理栄養士居宅療養Ⅱ１
                    // 管理栄養士居宅療養Ⅱ２
                    // 管理栄養士居宅療養Ⅱ３
                    liRet = CommonConstants.INT_2;
                }
                break;
            case CommonConstants.STR_NUM_32:
                if (lsSvcode.equals(CommonConstants.STR_NUM_4001) || lsSvcode.equals(CommonConstants.STR_NUM_6200)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6361)) {
                    // 認知症対応型生活機能向上連携加算Ⅰ
                    // 認知症対応型栄養管理体制加算
                    // 認知症対応型科学的介護推進体制加算
                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_33:
                // 外部地域療養通所介護
                // 特定施設生活機能向上連携加算Ⅰ
                // 特定施設個別機能訓練加算Ⅱ
                // 特定施設ＡＤＬ維持等加算Ⅰ
                // 特定施設ＡＤＬ維持等加算Ⅱ
                // 特定施設看取り介護加算Ⅱ４
                // 特定施設科学的介護推進体制加算
                if (lsSvcode.equals(CommonConstants.STR_NUM_2543) || lsSvcode.equals(CommonConstants.STR_NUM_4001)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6004)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6005)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6006)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6140)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6361)) {

                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_34:
                if (lsSvcode.equals(CommonConstants.STR_NUM_1257)) {
                    // 予防薬剤師居宅療養Ⅱ７
                    liRet = CommonConstants.INT_1;
                }
                if (lsSvcode.equals(CommonConstants.STR_NUM_1134) || lsSvcode.equals(CommonConstants.STR_NUM_1135)
                        || lsSvcode.equals(CommonConstants.STR_NUM_1136)) {
                    // 予防管理栄養士居宅療養Ⅱ１
                    // 予防管理栄養士居宅療養Ⅱ２
                    // 予防管理栄養士居宅療養Ⅱ３
                    liRet = CommonConstants.INT_2;
                }
                break;
            case CommonConstants.STR_NUM_35:
                if (lsSvcode.equals(CommonConstants.STR_NUM_4001) || lsSvcode.equals(CommonConstants.STR_NUM_6004)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6361)) {
                    // 予防特定施設生活機能向上連携加算Ⅰ
                    // 予防特定施設個別機能訓練加算Ⅱ
                    // 予防特定施設科学的介護推進体制加算
                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_36:
                if (lsSvcode.equals(CommonConstants.STR_NUM_4001) || lsSvcode.equals(CommonConstants.STR_NUM_6004)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6005)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6006)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6140)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6361)) {
                    // 地域特定施設生活機能向上連携加算Ⅰ
                    // 地域特定施設個別機能訓練加算Ⅱ
                    // 地域特定施設ＡＤＬ維持等加算Ⅰ
                    // 地域特定施設ＡＤＬ維持等加算Ⅱ
                    // 地域特定施設看取り介護加算Ⅱ４
                    // 地域特定施設科学的介護推進体制加算
                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_37:
                if (lsSvcode.equals(CommonConstants.STR_NUM_4001) || lsSvcode.equals(CommonConstants.STR_NUM_6200)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6361)) {
                    // 予認知症生活機能向上連携加算Ⅰ
                    // 予認知症栄養管理体制加算
                    // 予認知症科学的介護推進体制加算
                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_38:
                if (lsSvcode.equals(CommonConstants.STR_NUM_4001)) {
                    // 短期共同生活生活機能向上連携加算Ⅰ
                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_39:
                if (lsSvcode.equals(CommonConstants.STR_NUM_4001)) {
                    // 予短期共同生活機能向上連携加算Ⅰ
                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_62:
                if (lsSvcode.equals(CommonConstants.STR_NUM_4001)) {
                    // 予防訪問入浴初回加算
                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_66:
                if (lsSvcode.equals(CommonConstants.STR_NUM_5010) || lsSvcode.equals(CommonConstants.STR_NUM_6098)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6099)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6116)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6124)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6125)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6126)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6127)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6128)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6202)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6257)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6361)) {
                    // 予防通所リハ口腔機能向上加算Ⅱ
                    // 予通リハサービス提供体制加算Ⅰ１
                    // 予通リハサービス提供体制加算Ⅰ２
                    // 予防通所リハ栄養アセスメント加算
                    // 予防通所リハ１２月超減算１１
                    // 予防通所リハ１２月超減算１２
                    // 予防通所リハ１２月超減算２１
                    // 予防通所リハ１２月超減算２２
                    // 予防通所リハ１２月超減算３１
                    // 予防通所リハ１２月超減算３２
                    // 予防通所リハ口腔栄養スクリーニング加算Ⅰ
                    // 予防通所リハ生活行為向上リハ加算
                    // 予防通所リハ科学的介護推進体制加算

                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_71:
                if (lsSvcode.equals(CommonConstants.STR_NUM_6115) || lsSvcode.equals(CommonConstants.STR_NUM_6116)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6117)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6135)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6137)) {
                    // 夜間訪問サービス提供体制加算Ⅱ１
                    // 夜間訪問サービス提供体制加算Ⅱ２
                    // 夜間訪問サービス提供体制加算Ⅱ３
                    // 夜間訪問介護認知症専門ケア加算Ⅱ１
                    // 夜間訪問介護認知症専門ケア加算Ⅱ２
                    liRet = CommonConstants.INT_1;
                }
                // 既存の処理で戻り値が1になっているので、0に戻す
                if (lsSvcode.equals(CommonConstants.STR_NUM_6112) || lsSvcode.equals(CommonConstants.STR_NUM_6113)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6114)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6133)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6134)) {
                    // 夜間訪問サービス提供体制加算Ⅰ１
                    // 夜間訪問サービス提供体制加算Ⅰ２
                    // 夜間訪問サービス提供体制加算Ⅰ３
                    // 夜間訪問介護認知症専門ケア加算Ⅰ１
                    // 夜間訪問介護認知症専門ケア加算Ⅰ２
                    liRet = CommonConstants.INT_0;
                }
                break;
            case CommonConstants.STR_NUM_72:
                if (lsSvcode.equals(CommonConstants.STR_NUM_4001) || lsSvcode.equals(CommonConstants.STR_NUM_5051)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6116)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6124)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6125)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6361)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6202)) {
                    // 認知通所介護生活機能向上連携加算Ⅰ
                    // 認知通所介護個別機能訓練加算Ⅱ
                    // 認知通所介護栄養アセスメント加算
                    // 認知通所介護ＡＤＬ維持等加算Ⅰ
                    // 認知通所介護ＡＤＬ維持等加算Ⅱ
                    // 認知通所介護口腔栄養スクリーニング加算Ⅰ
                    // 認知通所介護科学的介護推進体制加算
                    liRet = CommonConstants.INT_1;
                }
                if (lsSvcode.equals(CommonConstants.STR_NUM_5608)) {
                    // 認知通所介護口腔機能向上加算Ⅱ
                    liRet = CommonConstants.INT_2;
                }
                break;
            case CommonConstants.STR_NUM_73:
                if (lsSvcode.equals(CommonConstants.STR_NUM_6099) || lsSvcode.equals(CommonConstants.STR_NUM_6361)) {
                    // 小多機能型サービス提供体制加算Ⅰ
                    // 小多機能型科学的介護推進体制加算
                    liRet = CommonConstants.INT_1;
                }
                // 既存の処理で戻り値が1になっているので、0に戻す
                if (lsSvcode.equals(CommonConstants.STR_NUM_8000) || lsSvcode.equals(CommonConstants.STR_NUM_8003)
                        || lsSvcode.equals(CommonConstants.STR_NUM_8100)
                        || lsSvcode.equals(CommonConstants.STR_NUM_8101)) {
                    // 特別地域小規模多機能型居宅介護加算
                    // 特別地域小規模多機能型居宅介護加算・日割
                    // 小多機能型小規模事業所加算
                    // 小多機能型小規模事業所加算・日割
                    liRet = CommonConstants.INT_0;
                }
                break;
            case CommonConstants.STR_NUM_74:
                if (lsSvcode.equals(CommonConstants.STR_NUM_4001) || lsSvcode.equals(CommonConstants.STR_NUM_5051)
                        || lsSvcode.equals(CommonConstants.STR_NUM_5608)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6116)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6202)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6361)) {
                    // 予認通所介護生活機能向上連携加算Ⅰ
                    // 予認通所介護個別機能訓練加算Ⅱ
                    // 予認通所介護口腔機能向上加算Ⅱ
                    // 予認通所介護栄養アセスメント加算
                    // 予認通所介護口腔栄養スクリーニング加算Ⅰ
                    // 予認通所介護科学的介護推進体制加算
                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_75:
                if (lsSvcode.equals(CommonConstants.STR_NUM_6099) || lsSvcode.equals(CommonConstants.STR_NUM_6361)) {
                    // 予小多機能サービス提供体制加算Ⅰ
                    // 予小多機能科学的介護推進体制加算
                    liRet = CommonConstants.INT_1;
                }
                // 既存の処理で戻り値が1になっているので、0に戻す
                if (lsSvcode.equals(CommonConstants.STR_NUM_8000) || lsSvcode.equals(CommonConstants.STR_NUM_8001)
                        || lsSvcode.equals(CommonConstants.STR_NUM_8100)
                        || lsSvcode.equals(CommonConstants.STR_NUM_8101)) {
                    // 特別地域予防小規模多機能型居宅介護加算
                    // 特別地域予防小規模多機能型居宅介護加算・日割
                    // 予小多機能小規模事業所加算
                    // 予小多機能小規模事業所加算・日割
                    liRet = CommonConstants.INT_0;
                }
                break;
            case CommonConstants.STR_NUM_76:
                if (lsSvcode.equals(CommonConstants.STR_NUM_6099) || lsSvcode.equals(CommonConstants.STR_NUM_6133)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6134)) {
                    // 定期巡回サービス提供体制加算Ⅰ
                    // 定期巡回認知症専門ケア加算Ⅰ
                    // 定期巡回認知症専門ケア加算Ⅱ
                    liRet = CommonConstants.INT_1;
                }
                break;
            case CommonConstants.STR_NUM_77:
                if (lsSvcode.equals(CommonConstants.STR_NUM_6099) || lsSvcode.equals(CommonConstants.STR_NUM_6116)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6202)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6355)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6356)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6358)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6359)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6360)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6361)) {
                    // 看護小規模サービス提供体制加算Ⅰ
                    // 看護小規模栄養アセスメント加算
                    // 看護小規模口腔栄養スクリーニング加算Ⅰ
                    // 看護小規模褥瘡マネジメント加算Ⅰ
                    // 看護小規模褥瘡マネジメント加算Ⅱ
                    // 看護小規模排せつ支援加算Ⅰ
                    // 看護小規模排せつ支援加算Ⅱ
                    // 看護小規模排せつ支援加算Ⅲ
                    // 看護小規模科学的介護推進体制加算

                    liRet = CommonConstants.INT_1;
                }
                if (lsSvcode.equals(CommonConstants.STR_NUM_5600) || lsSvcode.equals(CommonConstants.STR_NUM_5605)
                        || lsSvcode.equals(CommonConstants.STR_NUM_5606)) {
                    // 看護小規模口腔機能向上加算Ⅰ
                    // 看護小規模栄養改善加算
                    // 看護小規模口腔機能向上加算Ⅱ
                    liRet = CommonConstants.INT_2;
                }
                // 既存の処理で戻り値が1になっているので、0に戻す
                if (lsSvcode.equals(CommonConstants.STR_NUM_8000) || lsSvcode.equals(CommonConstants.STR_NUM_8001)
                        || lsSvcode.equals(CommonConstants.STR_NUM_8100)
                        || lsSvcode.equals(CommonConstants.STR_NUM_8101)) {
                    // 特別地域看護小規模多機能型居宅介護加算
                    // 特別地域看護小規模多機能型居宅介護加算・日割
                    // 看護小規模小規模事業所加算
                    // 看護小規模小規模事業所加算・日割
                    liRet = CommonConstants.INT_0;
                }
                break;
            case CommonConstants.STR_NUM_78:
                if (lsSvcode.equals(CommonConstants.STR_NUM_1910) || lsSvcode.equals(CommonConstants.STR_NUM_1911)
                        || lsSvcode.equals(CommonConstants.STR_NUM_1912)
                        || lsSvcode.equals(CommonConstants.STR_NUM_1913)
                        || lsSvcode.equals(CommonConstants.STR_NUM_4001)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6113)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6114)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6116)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6202)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6338)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6339)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6361)
                        || lsSvcode.equals(CommonConstants.STR_NUM_8730)
                        || lsSvcode.equals(CommonConstants.STR_NUM_8731)
                        || lsSvcode.equals(CommonConstants.STR_NUM_8732)
                        || lsSvcode.equals(CommonConstants.STR_NUM_8733)
                        || lsSvcode.equals(CommonConstants.STR_NUM_9730)
                        || lsSvcode.equals(CommonConstants.STR_NUM_9731)
                        || lsSvcode.equals(CommonConstants.STR_NUM_9732)
                        || lsSvcode.equals(CommonConstants.STR_NUM_9733))

                {
                    // 地域療養通所介護
                    // 地域療養通所介護・入浴無
                    // 地域療養通所介護・過少
                    // 地域療養通所介護・入浴無・過少
                    // 地域通所介護生活機能向上連携加算Ⅰ
                    // 地域通所介護サービス提供体制加算Ⅲ１
                    // 地域通所介護サービス提供体制加算Ⅲ２
                    // 地域通所介護栄養アセスメント加算
                    // 地域通所介護口腔栄養スクリーニング加算Ⅰ
                    // 地域通所介護ＡＤＬ維持等加算Ⅰ
                    // 地域通所介護ＡＤＬ維持等加算Ⅱ
                    // 地域通所介護科学的介護推進体制加算
                    // 地域療養通所介護・定超
                    // 地域療養通所介護・定超・入浴無
                    // 地域療養通所介護・定超・過少
                    // 地域療養通所介護・定超・入浴無・過少
                    // 地域療養通所介護・人欠
                    // 地域療養通所介護・人欠・入浴無
                    // 地域療養通所介護・人欠・過少
                    // 地域療養通所介護・人欠・入浴無・過少
                    liRet = CommonConstants.INT_1;
                }
                if (lsSvcode.equals(CommonConstants.STR_NUM_5608)) {
                    // 地域通所介護生活機能向上連携加算Ⅰ
                    liRet = CommonConstants.INT_2;
                }
                // 個別機能訓練加算ⅡはR3/4以前は日単位の加算のため期間分岐が必要
                if (CommonDtoUtil.strValToInt(asYmd) >= CommonDtoUtil.strValToInt(CommonConstants.STR_NUM_2021_04_01)) {
                    if (lsSvcode.equals(CommonConstants.STR_NUM_5052)) {
                        // 地域通所介護個別機能訓練加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                }
                break;
            case CommonConstants.STR_A6:
                if (lsSvcode.equals(CommonConstants.STR_NUM_4001) || lsSvcode.equals(CommonConstants.STR_NUM_4011)
                        || lsSvcode.equals(CommonConstants.STR_NUM_4021)
                        || lsSvcode.equals(CommonConstants.STR_NUM_4031)
                        || lsSvcode.equals(CommonConstants.STR_NUM_4041)
                        || lsSvcode.equals(CommonConstants.STR_NUM_5011)
                        || lsSvcode.equals(CommonConstants.STR_NUM_5021)
                        || lsSvcode.equals(CommonConstants.STR_NUM_5031)
                        || lsSvcode.equals(CommonConstants.STR_NUM_5041)
                        || lsSvcode.equals(CommonConstants.STR_NUM_5051)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6011)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6012)

                        || lsSvcode.equals(CommonConstants.STR_NUM_6021)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6022)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6031)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6032)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6041)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6042)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6051)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6052)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6116)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6120)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6130)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6140)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6150)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6200)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6210)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6220)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6230)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6240)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6311)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6321)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6331)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6341)
                        || lsSvcode.equals(CommonConstants.STR_NUM_6351))

                {
                    // 通所型独自サービス生活機能向上連携加算Ⅰ
                    // 通所型独自サービス生活機能向上連携加算Ⅰ／２
                    // 通所型独自サービス生活機能向上連携加算Ⅰ／３
                    // 通所型独自サービス生活機能向上連携加算Ⅰ／４
                    // 通所型独自サービス生活機能向上連携加算Ⅰ／５
                    // 通所型独自サービス口腔機能向上加算Ⅱ
                    // 通所型独自サービス口腔機能向上加算Ⅱ／２
                    // 通所型独自サービス口腔機能向上加算Ⅱ／３
                    // 通所型独自サービス口腔機能向上加算Ⅱ／４
                    // 通所型独自サービス口腔機能向上加算Ⅱ／５
                    // 通所型独自サービス提供体制加算Ⅰ１
                    // 通所型独自サービス提供体制加算Ⅰ２
                    // 通所型独自サービス提供体制加算Ⅰ／２１
                    // 通所型独自サービス提供体制加算Ⅰ／２２
                    // 通所型独自サービス提供体制加算Ⅰ／３１
                    // 通所型独自サービス提供体制加算Ⅰ／３２
                    // 通所型独自サービス提供体制加算Ⅰ／４１
                    // 通所型独自サービス提供体制加算Ⅰ／４２
                    // 通所型独自サービス提供体制加算Ⅰ／５１
                    // 通所型独自サービス提供体制加算Ⅰ／５２
                    // 通所型独自サービス栄養アセスメント加算
                    // 通所型独自サービス栄養アセスメント加算／２
                    // 通所型独自サービス栄養アセスメント加算／３
                    // 通所型独自サービス栄養アセスメント加算／４
                    // 通所型独自サービス栄養アセスメント加算／５
                    // 通所型独自サービス口腔栄養スクリーニング加算Ⅰ
                    // 通所型独自サービス口腔栄養スクリーニング加算Ⅰ／２
                    // 通所型独自サービス口腔栄養スクリーニング加算Ⅰ／３
                    // 通所型独自サービス口腔栄養スクリーニング加算Ⅰ／４
                    // 通所型独自サービス口腔栄養スクリーニング加算Ⅰ／５
                    // 通所型独自サービス科学的介護推進体制加算
                    // 通所型独自サービス科学的介護推進体制加算／２
                    // 通所型独自サービス科学的介護推進体制加算／３
                    // 通所型独自サービス科学的介護推進体制加算／４
                    // 通所型独自サービス科学的介護推進体制加算／５
                    liRet = CommonConstants.INT_1;
                }

        }

        if (CommonDtoUtil.strValToInt(removeSlash(asYmd)) >= CommonDtoUtil
                .strValToInt(removeSlash(CommonConstants.STR_NUM_2021_04_01))) {
            switch (lsSvt) {
                case CommonConstants.STR_NUM_11:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6192)) {
                        // 訪問介護口腔連携強化加算
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_21:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6192) || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 短期生活口腔連携強化加算
                        // 短期生活生産性向上推進体制加算Ⅰ
                        // 短期生活生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4000)) {
                        liRet = CommonConstants.INT_7;
                    }
                    break;
                case CommonConstants.STR_NUM_22:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6192) || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 老短口腔連携強化加算
                        // 老短生産性向上推進体制加算Ⅰ
                        // 老短生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_23:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_2192) || lsSvcode.equals(CommonConstants.STR_NUM_2237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2238)
                            || lsSvcode.equals(CommonConstants.STR_NUM_3192)
                            || lsSvcode.equals(CommonConstants.STR_NUM_3237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_3238)) {
                        // 病院療短口腔連携強化加算
                        // 病院療短生産性向上推進体制加算Ⅰ
                        // 病院療短生産性向上推進体制加算Ⅱ
                        // 診療所短期口腔連携強化加算
                        // 診療所短期生産性向上推進体制加算Ⅰ
                        // 診療所短期生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_24:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6192) || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 予短期生活口腔連携強化加算
                        // 予短期生活生産性向上推進体制加算Ⅰ
                        // 予短期生活生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_25:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6192) || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 予老短口腔連携強化加算
                        // 予老短生産性向上推進体制加算Ⅰ
                        // 予老短生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_26:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_2192) || lsSvcode.equals(CommonConstants.STR_NUM_2237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2238)
                            || lsSvcode.equals(CommonConstants.STR_NUM_3192)
                            || lsSvcode.equals(CommonConstants.STR_NUM_3237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_3238)) {
                        // 予病院療短口腔連携強化加算
                        // 予病院療短生産性向上推進体制加算Ⅰ
                        // 予病院療短生産性向上推進体制加算Ⅱ
                        // 予診療所短期口腔連携強化加算
                        // 予診療所短期生産性向上推進体制加算Ⅰ
                        // 予診療所短期生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_27:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6166) || lsSvcode.equals(CommonConstants.STR_NUM_6167)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 短期特定施設高齢者等感染対策向上加算Ⅰ
                        // 短期特定施設高齢者等感染対策向上加算Ⅱ
                        // 短期特定施設生産性向上推進体制加算Ⅰ
                        // 短期特定施設生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_28:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6166) || lsSvcode.equals(CommonConstants.STR_NUM_6167)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 短期地域特定施設高齢者等感染対策向上加算Ⅰ
                        // 短期地域特定施設高齢者等感染対策向上加算Ⅱ
                        // 短期地域特定施設生産性向上推進体制加算Ⅰ
                        // 短期地域特定施設生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_2A:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6192) || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 医療院短期口腔連携強化加算
                        // 医療院短期生産性向上推進体制加算Ⅰ
                        // 医療院短期生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_2B:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6192) || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 予防医療院短期口腔連携強化加算
                        // 予防医療院短期生産性向上推進体制加算Ⅰ
                        // 予防医療院短期生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_32:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6123) || lsSvcode.equals(CommonConstants.STR_NUM_6124)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6153)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6154)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6166)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6167)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 認知症対応型協力医療機関連携加算１
                        // 認知症対応型協力医療機関連携加算２
                        // 認知症対応型認知症チームケア推進加算Ⅰ
                        // 認知症対応型認知症チームケア推進加算Ⅱ
                        // 認知症対応型高齢者等感染対策向上加算Ⅰ
                        // 認知症対応型高齢者等感染対策向上加算Ⅱ
                        // 認知症対応型生産性向上推進体制加算Ⅰ
                        // 認知症対応型生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6150)) {
                        // 認知症対応型退居時情報提供加算
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_33:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6143) || lsSvcode.equals(CommonConstants.STR_NUM_6166)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6167)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 特定施設協力医療機関連携加算２
                        // 特定施設高齢者等感染対策向上加算Ⅰ
                        // 特定施設高齢者等感染対策向上加算Ⅱ
                        // 特定施設生産性向上推進体制加算Ⅰ
                        // 特定施設生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6150)) {
                        // 特定施設退居時情報提供加算
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_35:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_2329) || lsSvcode.equals(CommonConstants.STR_NUM_6125)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6166)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6167)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 予防外部通所一体的サービス提供加算
                        // 予防特定施設協力医療機関連携加算２
                        // 予防特定施設高齢者等感染対策向上加算Ⅰ
                        // 予防特定施設高齢者等感染対策向上加算Ⅱ
                        // 予防特定施設生産性向上推進体制加算Ⅰ
                        // 予防特定施設生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6150)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_36:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6143) || lsSvcode.equals(CommonConstants.STR_NUM_6166)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6167)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 地域特定施設協力医療機関連携加算２
                        // 地域特定施設高齢者等感染対策向上加算Ⅰ
                        // 地域特定施設高齢者等感染対策向上加算Ⅱ
                        // 地域特定施設生産性向上推進体制加算Ⅰ
                        // 地域特定施設生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6150)) {
                        // 地域特定施設退居時情報提供加算
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_37:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6153) || lsSvcode.equals(CommonConstants.STR_NUM_6154)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6166)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6167)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 予認知症対応型認知症チームケア推進加算Ⅰ
                        // 予認知症対応型認知症チームケア推進加算Ⅱ
                        // 予認知症対応型高齢者等感染対策向上加算Ⅰ
                        // 予認知症対応型高齢者等感染対策向上加算Ⅱ
                        // 予認知症対応型生産性向上推進体制加算Ⅰ
                        // 予認知症対応型生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6150)) {
                        // 予認知症対応型退居時情報提供加算
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_38:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6166) || lsSvcode.equals(CommonConstants.STR_NUM_6167)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 短期共同生活高齢者等感染対策向上加算Ⅰ
                        // 短期共同生活高齢者等感染対策向上加算Ⅱ
                        // 短期共同生活生産性向上推進体制加算Ⅰ
                        // 短期共同生活生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_39:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6166) || lsSvcode.equals(CommonConstants.STR_NUM_6167)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 予短期共同高齢者等感染対策向上加算Ⅰ
                        // 予短期共同高齢者等感染対策向上加算Ⅱ
                        // 予短期共同生産性向上推進体制加算Ⅰ
                        // 予短期共同生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_68:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 短期小多機能型生産性向上推進体制加算Ⅰ
                        // 短期小多機能型生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_69:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 予短期小多機能生産性向上推進体制加算Ⅰ
                        // 予短期小多機能生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_71:
                    if (lsSvcode.equals(CommonConstants.STR_C201) || lsSvcode.equals(CommonConstants.STR_C205)) {
                        // 夜間訪問高齢者虐待防止未実施減算Ⅰ１
                        // 夜間訪問高齢者虐待防止未実施減算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_73:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6126)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 小規模多機能型認知症加算Ⅰ
                        // 小多機能型生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_C201) || lsSvcode.equals(CommonConstants.STR_C203)
                            || lsSvcode.equals(CommonConstants.STR_C205) || lsSvcode.equals(CommonConstants.STR_C207)
                            || lsSvcode.equals(CommonConstants.STR_C209) || lsSvcode.equals(CommonConstants.STR_C211)
                            || lsSvcode.equals(CommonConstants.STR_C213) || lsSvcode.equals(CommonConstants.STR_C215)
                            || lsSvcode.equals(CommonConstants.STR_C217) || lsSvcode.equals(CommonConstants.STR_C219)
                            || lsSvcode.equals(CommonConstants.STR_D203) || lsSvcode.equals(CommonConstants.STR_D205)
                            || lsSvcode.equals(CommonConstants.STR_D207) || lsSvcode.equals(CommonConstants.STR_D209)
                            || lsSvcode.equals(CommonConstants.STR_D211) || lsSvcode.equals(CommonConstants.STR_D213)
                            || lsSvcode.equals(CommonConstants.STR_D215) || lsSvcode.equals(CommonConstants.STR_D217)
                            || lsSvcode.equals(CommonConstants.STR_D219)) {
                        // 高齢者虐待防止未実施減算
                        // 業務継続計画未策定減算
                        liRet = CommonConstants.INT_1;
                        if (lsSvcode.equals(CommonConstants.STR_NUM_8201)) {
                            // 既存の処理で戻り値が1になっているので、0に戻す
                            // 小多機能型過少サービス減算・日割
                            liRet = CommonConstants.INT_0;
                        }
                    }
                    break;
                case CommonConstants.STR_NUM_75:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 小多機能型生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_C201) || lsSvcode.equals(CommonConstants.STR_C203)
                            || lsSvcode.equals(CommonConstants.STR_C205) || lsSvcode.equals(CommonConstants.STR_C207)
                            || lsSvcode.equals(CommonConstants.STR_D201) || lsSvcode.equals(CommonConstants.STR_D203)
                            || lsSvcode.equals(CommonConstants.STR_D205) || lsSvcode.equals(CommonConstants.STR_D207)) {
                        // 高齢者虐待防止未実施減算
                        // 業務継続計画未策定減算
                        liRet = CommonConstants.INT_1;
                        if (lsSvcode.equals(CommonConstants.STR_NUM_8201)) {
                            // 既存の処理で戻り値が1になっているので、0に戻す
                            // 予小多機能過少サービス減算・日割
                            liRet = CommonConstants.INT_0;
                        }
                    }
                    break;
                case CommonConstants.STR_NUM_76:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_3000) || lsSvcode.equals(CommonConstants.STR_NUM_4009)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6192)) {
                        // 定期巡回緊急時訪問看護加算Ⅰ
                        // 定期巡回総合マネジメント体制加算Ⅰ
                        // 定期巡回口腔連携強化加算
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1321) || lsSvcode.equals(CommonConstants.STR_NUM_1331)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1341)) {
                        // 既存の処理で戻り値が1になっているので、0に戻す
                        // 定期巡回随時Ⅲ２
                        // 定期巡回随時Ⅲ３
                        // 定期巡回随時Ⅲ４
                        liRet = CommonConstants.INT_0;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_C201) || lsSvcode.equals(CommonConstants.STR_C203)
                            || lsSvcode.equals(CommonConstants.STR_C205) || lsSvcode.equals(CommonConstants.STR_C207)
                            || lsSvcode.equals(CommonConstants.STR_C209) || lsSvcode.equals(CommonConstants.STR_C211)
                            || lsSvcode.equals(CommonConstants.STR_C213) || lsSvcode.equals(CommonConstants.STR_C215)
                            || lsSvcode.equals(CommonConstants.STR_C217) || lsSvcode.equals(CommonConstants.STR_C219)
                            || lsSvcode.equals(CommonConstants.STR_C221) || lsSvcode.equals(CommonConstants.STR_C223)
                            || lsSvcode.equals(CommonConstants.STR_C225) || lsSvcode.equals(CommonConstants.STR_C227)
                            || lsSvcode.equals(CommonConstants.STR_C229) || lsSvcode.equals(CommonConstants.STR_C231)) {
                        // 高齢者虐待防止未実施減算

                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_77:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_4022) || lsSvcode.equals(CommonConstants.STR_NUM_4023)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6126)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 看護小規模専門管理加算１
                        // 看護小規模専門管理加算２
                        // 看護小規模認知症加算Ⅰ
                        // 看護小規模生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_C201) || lsSvcode.equals(CommonConstants.STR_C203)
                            || lsSvcode.equals(CommonConstants.STR_C205) || lsSvcode.equals(CommonConstants.STR_C207)
                            || lsSvcode.equals(CommonConstants.STR_C209) || lsSvcode.equals(CommonConstants.STR_C211)
                            || lsSvcode.equals(CommonConstants.STR_C213) || lsSvcode.equals(CommonConstants.STR_C215)
                            || lsSvcode.equals(CommonConstants.STR_C217) || lsSvcode.equals(CommonConstants.STR_C219)
                            || lsSvcode.equals(CommonConstants.STR_D201) || lsSvcode.equals(CommonConstants.STR_D203)
                            || lsSvcode.equals(CommonConstants.STR_D205) || lsSvcode.equals(CommonConstants.STR_D207)
                            || lsSvcode.equals(CommonConstants.STR_D209) || lsSvcode.equals(CommonConstants.STR_D211)
                            || lsSvcode.equals(CommonConstants.STR_D213) || lsSvcode.equals(CommonConstants.STR_D215)
                            || lsSvcode.equals(CommonConstants.STR_D217) || lsSvcode.equals(CommonConstants.STR_D219)

                    ) {
                        // 高齢者虐待防止未実施減算
                        // 業務継続計画未策定減算
                        liRet = CommonConstants.INT_1;
                        if (lsSvcode.equals(CommonConstants.STR_NUM_8201)
                                || lsSvcode.equals(CommonConstants.STR_NUM_8203)) {
                            // 既存の処理で戻り値が1になっているので、0に戻す
                            // 看護小規模過少サービス減算・日割
                            // 看護小規模サテライト体制未整備減算・日割
                            liRet = CommonConstants.INT_0;
                        }
                    }
                    break;
                case CommonConstants.STR_NUM_78:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6500)) {
                        liRet = CommonConstants.INT_1;
                    }
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1961) || lsSvcode.equals(CommonConstants.STR_NUM_1963)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1965)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1967)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1969)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1971)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1973)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1975)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1977)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1979)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1981)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1983)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8751)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8753)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8755)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8757)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8759)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8761)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8763)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8765)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8767)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8769)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8771)
                            || lsSvcode.equals(CommonConstants.STR_NUM_8773)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9751)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9753)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9755)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9757)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9759)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9761)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9763)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9765)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9767)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9769)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9771)
                            || lsSvcode.equals(CommonConstants.STR_NUM_9773)) {
                        // 高齢者虐待防止未実施減算（合成コード）
                        // 業務継続計画未策定減算（合成コード）
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_79:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6238)) {
                        // 短期看護小規模生産性向上推進体制加算Ⅰ
                        // 短期看護小規模生産性向上推進体制加算Ⅱ
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_A2:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1411) || lsSvcode.equals(CommonConstants.STR_NUM_1421)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1431)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1441)
                            || lsSvcode.equals(CommonConstants.STR_NUM_1451)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2411)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2421)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2431)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2441)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2451)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2511)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2521)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2531)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2541)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2551)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2621)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2631)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2641)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2651)
                            || lsSvcode.equals(CommonConstants.STR_NUM_2661)) {
                        // 上流処理で判定されるサービスの補正
                        // 訪問型独自短時間サービス
                        // 訪問型独自短時間サービス／２
                        // 訪問型独自短時間サービス／３
                        // 訪問型独自短時間サービス／４
                        // 訪問型独自短時間サービス／５
                        // 訪問型独自サービス２１
                        // 訪問型独自サービス／２２１
                        // 訪問型独自サービス／３２１
                        // 訪問型独自サービス／４２１
                        // 訪問型独自サービス／５２１
                        // 訪問型独自サービス２２
                        // 訪問型独自サービス／２２２
                        // 訪問型独自サービス／３２２
                        // 訪問型独自サービス／４２２
                        // 訪問型独自サービス／５２２
                        // 訪問型独自サービス２３
                        // 訪問型独自サービス／２２３
                        // 訪問型独自サービス／３２３
                        // 訪問型独自サービス／４２３
                        // 訪問型独自サービス／５２３
                        liRet = CommonConstants.INT_0;
                        if (lsSvcode.equals(CommonConstants.STR_NUM_6102)
                                || lsSvcode.equals(CommonConstants.STR_NUM_6112)
                                || lsSvcode.equals(CommonConstants.STR_NUM_6122)
                                || lsSvcode.equals(CommonConstants.STR_NUM_6132)
                                || lsSvcode.equals(CommonConstants.STR_NUM_6142)) {
                            // 訪問型独自口腔連携強化加算
                            // 訪問型独自口腔連携強化加算／２
                            // 訪問型独自口腔連携強化加算／３
                            // 訪問型独自口腔連携強化加算／４
                            // 訪問型独自口腔連携強化加算／５
                            liRet = CommonConstants.INT_1;
                        }
                    }
                    if (lsSvcode.equals(CommonConstants.STR_C211) || lsSvcode.equals(CommonConstants.STR_C212)
                            || lsSvcode.equals(CommonConstants.STR_C214) || lsSvcode.equals(CommonConstants.STR_C221)
                            || lsSvcode.equals(CommonConstants.STR_C222) || lsSvcode.equals(CommonConstants.STR_C224)
                            || lsSvcode.equals(CommonConstants.STR_C231) || lsSvcode.equals(CommonConstants.STR_C232)
                            || lsSvcode.equals(CommonConstants.STR_C234) || lsSvcode.equals(CommonConstants.STR_C241)
                            || lsSvcode.equals(CommonConstants.STR_C242) || lsSvcode.equals(CommonConstants.STR_C244)
                            || lsSvcode.equals(CommonConstants.STR_C251) || lsSvcode.equals(CommonConstants.STR_C252)
                            || lsSvcode.equals(CommonConstants.STR_C254)) {
                        // 高齢者虐待防止未実施減算
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_A6:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_5612) || lsSvcode.equals(CommonConstants.STR_NUM_5622)
                            || lsSvcode.equals(CommonConstants.STR_NUM_5632)
                            || lsSvcode.equals(CommonConstants.STR_NUM_5642)
                            || lsSvcode.equals(CommonConstants.STR_NUM_5652)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6207)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6227)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6237)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6247)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6257)) {
                        // 既存の処理で戻り値が1になっているので、0に戻す
                        // 通所型独自送迎減算
                        // 通所型独自送迎減算／２
                        // 通所型独自送迎減算／３
                        // 通所型独自送迎減算／４
                        // 通所型独自送迎減算／５

                        // 1回につきの減算を0に戻す
                        // 通所型独自サービス同一建物減算３
                        // 通所型独自サービス同一建物減算／２３
                        // 通所型独自サービス同一建物減算／３３
                        // 通所型独自サービス同一建物減算／４３
                        // 通所型独自サービス同一建物減算／５３
                        liRet = CommonConstants.INT_0;
                    }
                    // 高齢者虐待防止未実施減算
                    // 業務継続計画未策定減算
                    if (lsSvcode.equals(CommonConstants.STR_C213) || lsSvcode.equals(CommonConstants.STR_C223)
                            || lsSvcode.equals(CommonConstants.STR_C233) || lsSvcode.equals(CommonConstants.STR_C243)
                            || lsSvcode.equals(CommonConstants.STR_C253) || lsSvcode.equals(CommonConstants.STR_D213)
                            || lsSvcode.equals(CommonConstants.STR_D223) || lsSvcode.equals(CommonConstants.STR_D233)
                            || lsSvcode.equals(CommonConstants.STR_D243) || lsSvcode.equals(CommonConstants.STR_D253)

                    ) {

                        liRet = CommonConstants.INT_1;
                        if (lsSvcode.equals(CommonConstants.STR_C215) || lsSvcode.equals(CommonConstants.STR_C225)
                                || lsSvcode.equals(CommonConstants.STR_C235)
                                || lsSvcode.equals(CommonConstants.STR_C245)
                                || lsSvcode.equals(CommonConstants.STR_C255)
                                || lsSvcode.equals(CommonConstants.STR_D215)
                                || lsSvcode.equals(CommonConstants.STR_D225)
                                || lsSvcode.equals(CommonConstants.STR_D235)
                                || lsSvcode.equals(CommonConstants.STR_D245)
                                || lsSvcode.equals(CommonConstants.STR_D255)) {

                            liRet = CommonConstants.INT_4;
                        }
                        if (lsSvcode.equals(CommonConstants.STR_C216) || lsSvcode.equals(CommonConstants.STR_C226)
                                || lsSvcode.equals(CommonConstants.STR_C236)
                                || lsSvcode.equals(CommonConstants.STR_C246)
                                || lsSvcode.equals(CommonConstants.STR_C256)
                                || lsSvcode.equals(CommonConstants.STR_D216)
                                || lsSvcode.equals(CommonConstants.STR_D226)
                                || lsSvcode.equals(CommonConstants.STR_D236)
                                || lsSvcode.equals(CommonConstants.STR_D246)
                                || lsSvcode.equals(CommonConstants.STR_D256)) {

                            liRet = CommonConstants.INT_8;
                        }
                    }
                    break;

            }
        }
        if (CommonDtoUtil.strValToInt(removeSlash(asYmd)) >= CommonDtoUtil
                .strValToInt(removeSlash(CommonConstants.STR_NUM_2024_06_01))) {
            switch (lsSvt) {
                case CommonConstants.STR_NUM_13:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_3001) || lsSvcode.equals(CommonConstants.STR_NUM_3002)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4021)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4023)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4025)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4026)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6192)
                            || lsSvcode.equals(CommonConstants.STR_C201)) {
                        // 緊急時訪問看護加算Ⅰ１
                        // 緊急時訪問看護加算Ⅰ２
                        // 訪問看護遠隔死亡診断補助加算
                        // 訪問看護初回加算Ⅰ
                        // 訪問看護専門管理加算１
                        // 訪問看護専門管理加算２
                        // 訪問看護口腔連携強化加算
                        // 訪問看護高齢者虐待防止未実施減算
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_14:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_5022)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6192)) {
                        // 訪問リハマネジメント加算３
                        // 訪問リハ口腔連携強化加算
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_16:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_5631) || lsSvcode.equals(CommonConstants.STR_NUM_5632)
                            || lsSvcode.equals(CommonConstants.STR_NUM_5640)) {
                        // 通所リハマネジメント加算３１
                        // 通所リハマネジメント加算３２
                        // 通所リハマネジメント加算４
                        liRet = CommonConstants.INT_1;
                        if (lsSvcode.equals(CommonConstants.STR_NUM_5625)) {
                            // 通所リハ口腔機能向上加算Ⅱ１
                            liRet = CommonConstants.INT_2;
                        }
                    }
                    break;
                case CommonConstants.STR_NUM_31:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1257)) {
                        // 薬剤師居宅療養Ⅱ７
                        liRet = CommonConstants.INT_4;
                        if (lsSvcode.equals(CommonConstants.STR_NUM_1281)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1282)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1283)) {
                            // 歯科衛生士等居宅療養Ⅳ
                            // 歯科衛生士等居宅療養Ⅴ
                            // 歯科衛生士等居宅療養Ⅵ
                            liRet = CommonConstants.INT_6;
                        }
                        if (lsSvcode.equals(CommonConstants.STR_NUM_1258)) {
                            // 薬剤師居宅療養Ⅱ８
                            liRet = CommonConstants.INT_8;
                        }
                    }
                    break;
                case CommonConstants.STR_NUM_34:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1257)) {
                        // 予防薬剤師居宅療養Ⅱ７
                        liRet = CommonConstants.INT_4;
                        if (lsSvcode.equals(CommonConstants.STR_NUM_1281)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1282)
                                || lsSvcode.equals(CommonConstants.STR_NUM_1283)) {
                            // 予防歯科衛生士等居宅療養Ⅳ
                            // 予防歯科衛生士等居宅療養Ⅴ
                            // 予防歯科衛生士等居宅療養Ⅵ
                            liRet = CommonConstants.INT_6;
                        }
                        if (lsSvcode.equals(CommonConstants.STR_NUM_1258)) {
                            // 予防薬剤師居宅療養Ⅱ８
                            liRet = CommonConstants.INT_8;
                        }
                    }
                    break;
                case CommonConstants.STR_NUM_35:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_1828)) {
                        // 予防外部通所リハ一体的サービス提供加算
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_63:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_3001) || lsSvcode.equals(CommonConstants.STR_NUM_3002)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4023)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4025)
                            || lsSvcode.equals(CommonConstants.STR_NUM_4026)
                            || lsSvcode.equals(CommonConstants.STR_NUM_6192)) {
                        // 予防緊急時訪問看護加算Ⅰ１
                        // 予防緊急時訪問看護加算Ⅰ２
                        // 予防訪問看護初回加算Ⅰ
                        // 予防訪問看護専門管理加算１
                        // 予防訪問看護専門管理加算２
                        // 予防訪問看護口腔連携強化加算
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_64:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6162)) {
                        // 予防訪問リハ口腔連携強化加算
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_66:
                    if (lsSvcode.equals(CommonConstants.STR_NUM_6360) || lsSvcode.equals(CommonConstants.STR_C201)
                            || lsSvcode.equals(CommonConstants.STR_C203) || lsSvcode.equals(CommonConstants.STR_C205)
                            || lsSvcode.equals(CommonConstants.STR_C207) || lsSvcode.equals(CommonConstants.STR_C209)
                            || lsSvcode.equals(CommonConstants.STR_C211) || lsSvcode.equals(CommonConstants.STR_D201)
                            || lsSvcode.equals(CommonConstants.STR_D203) || lsSvcode.equals(CommonConstants.STR_D205)
                            || lsSvcode.equals(CommonConstants.STR_D207) || lsSvcode.equals(CommonConstants.STR_D209)
                            || lsSvcode.equals(CommonConstants.STR_D211)) {
                        // 予通リハ一体的サービス提供加算
                        // 高齢者虐待防止未実施減算
                        // 業務継続計画未策定減算

                        liRet = CommonConstants.INT_1;
                    }
                    break;
            }
        }

        // G3C46L153_202504改正_利用票別表計算_追加対応 START
        if (CommonDtoUtil.strValToInt(removeSlash(asYmd)) >= CommonDtoUtil
                .strValToInt(removeSlash(CommonConstants.STR_NUM_2025_04_01))) {
            switch (lsSvt) {
                case CommonConstants.STR_NUM_13:
                    // 訪問看護業務継続計画未策定減算
                    if (lsSvcode.equals(CommonConstants.STR_D201)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_71:
                    // 夜間訪問業務継続計画未策定減算Ⅰ１
                    // 夜間訪問業務継続計画未策定減算Ⅱ
                    if (lsSvcode.equals(CommonConstants.STR_D201) || lsSvcode.equals(CommonConstants.STR_D205)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_73:
                    // 小多機能型身体拘束廃止未実施減算１１
                    // 小多機能型身体拘束廃止未実施減算１２
                    // 小多機能型身体拘束廃止未実施減算１３
                    // 小多機能型身体拘束廃止未実施減算１４
                    // 小多機能型身体拘束廃止未実施減算１５
                    // 小多機能型身体拘束廃止未実施減算２１
                    // 小多機能型身体拘束廃止未実施減算２２
                    // 小多機能型身体拘束廃止未実施減算２３
                    // 小多機能型身体拘束廃止未実施減算２４
                    // 小多機能型身体拘束廃止未実施減算２５
                    if (lsSvcode.equals(CommonConstants.STR_E201)
                            || lsSvcode.equals(CommonConstants.STR_E203)
                            || lsSvcode.equals(CommonConstants.STR_E205)
                            || lsSvcode.equals(CommonConstants.STR_E207)
                            || lsSvcode.equals(CommonConstants.STR_E209)
                            || lsSvcode.equals(CommonConstants.STR_E211)
                            || lsSvcode.equals(CommonConstants.STR_E213)
                            || lsSvcode.equals(CommonConstants.STR_E215)
                            || lsSvcode.equals(CommonConstants.STR_E217)
                            || lsSvcode.equals(CommonConstants.STR_E219)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_75:
                    // 予小多機能身体拘束廃止未実施減算１１
                    // 予小多機能身体拘束廃止未実施減算１２
                    // 予小多機能身体拘束廃止未実施減算２１
                    // 予小多機能身体拘束廃止未実施減算２２
                    if (lsSvcode.equals(CommonConstants.STR_E201)
                            || lsSvcode.equals(CommonConstants.STR_E203)
                            || lsSvcode.equals(CommonConstants.STR_E205)
                            || lsSvcode.equals(CommonConstants.STR_E207)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_76:
                    // 定期巡回業務継続計画未策定減算Ⅰ１１
                    // 定期巡回業務継続計画未策定減算Ⅰ１２
                    // 定期巡回業務継続計画未策定減算Ⅰ１３
                    // 定期巡回業務継続計画未策定減算Ⅰ１４
                    // 定期巡回業務継続計画未策定減算Ⅰ１５
                    // 定期巡回業務継続計画未策定減算Ⅰ２１
                    // 定期巡回業務継続計画未策定減算Ⅰ２２
                    // 定期巡回業務継続計画未策定減算Ⅰ２３
                    // 定期巡回業務継続計画未策定減算Ⅰ２４
                    // 定期巡回業務継続計画未策定減算Ⅰ２５
                    // 定期巡回業務継続計画未策定減算Ⅱ１
                    // 定期巡回業務継続計画未策定減算Ⅱ２
                    // 定期巡回業務継続計画未策定減算Ⅱ３
                    // 定期巡回業務継続計画未策定減算Ⅱ４
                    // 定期巡回業務継続計画未策定減算Ⅱ５
                    // 定期巡回業務継続計画未策定減算Ⅲ１
                    if (lsSvcode.equals(CommonConstants.STR_D201)
                            || lsSvcode.equals(CommonConstants.STR_D203)
                            || lsSvcode.equals(CommonConstants.STR_D205)
                            || lsSvcode.equals(CommonConstants.STR_D207)
                            || lsSvcode.equals(CommonConstants.STR_D209)
                            || lsSvcode.equals(CommonConstants.STR_D211)
                            || lsSvcode.equals(CommonConstants.STR_D213)
                            || lsSvcode.equals(CommonConstants.STR_D215)
                            || lsSvcode.equals(CommonConstants.STR_D217)
                            || lsSvcode.equals(CommonConstants.STR_D219)
                            || lsSvcode.equals(CommonConstants.STR_D221)
                            || lsSvcode.equals(CommonConstants.STR_D223)
                            || lsSvcode.equals(CommonConstants.STR_D225)
                            || lsSvcode.equals(CommonConstants.STR_D227)
                            || lsSvcode.equals(CommonConstants.STR_D229)
                            || lsSvcode.equals(CommonConstants.STR_D231)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_NUM_77:
                    // 看護小規模身体拘束廃止未実施減算１１
                    // 看護小規模身体拘束廃止未実施減算１２
                    // 看護小規模身体拘束廃止未実施減算１３
                    // 看護小規模身体拘束廃止未実施減算１４
                    // 看護小規模身体拘束廃止未実施減算１５
                    // 看護小規模身体拘束廃止未実施減算２１
                    // 看護小規模身体拘束廃止未実施減算２２
                    // 看護小規模身体拘束廃止未実施減算２３
                    // 看護小規模身体拘束廃止未実施減算２４
                    // 看護小規模身体拘束廃止未実施減算２５
                    if (lsSvcode.equals(CommonConstants.STR_E201)
                            || lsSvcode.equals(CommonConstants.STR_E203)
                            || lsSvcode.equals(CommonConstants.STR_E205)
                            || lsSvcode.equals(CommonConstants.STR_E207)
                            || lsSvcode.equals(CommonConstants.STR_E209)
                            || lsSvcode.equals(CommonConstants.STR_E211)
                            || lsSvcode.equals(CommonConstants.STR_E213)
                            || lsSvcode.equals(CommonConstants.STR_E215)
                            || lsSvcode.equals(CommonConstants.STR_E217)
                            || lsSvcode.equals(CommonConstants.STR_E219)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
                case CommonConstants.STR_A2:
                    // 訪問型独自業務継続計画未策定減算１１
                    // 訪問型独自業務継続計画未策定減算１２
                    // 訪問型独自業務継続計画未策定減算１３
                    // 訪問型独自業務継続計画未策定減算／２１１
                    // 訪問型独自業務継続計画未策定減算／２１２
                    // 訪問型独自業務継続計画未策定減算／２１３
                    // 訪問型独自業務継続計画未策定減算／３１１
                    // 訪問型独自業務継続計画未策定減算／３１２
                    // 訪問型独自業務継続計画未策定減算／３１３
                    // 訪問型独自業務継続計画未策定減算／４１１
                    // 訪問型独自業務継続計画未策定減算／４１２
                    // 訪問型独自業務継続計画未策定減算／４１３
                    // 訪問型独自業務継続計画未策定減算／５１１
                    // 訪問型独自業務継続計画未策定減算／５１２
                    // 訪問型独自業務継続計画未策定減算／５１３
                    if (lsSvcode.equals(CommonConstants.STR_D211)
                            || lsSvcode.equals(CommonConstants.STR_D212)
                            || lsSvcode.equals(CommonConstants.STR_D214)
                            || lsSvcode.equals(CommonConstants.STR_D221)
                            || lsSvcode.equals(CommonConstants.STR_D222)
                            || lsSvcode.equals(CommonConstants.STR_D224)
                            || lsSvcode.equals(CommonConstants.STR_D231)
                            || lsSvcode.equals(CommonConstants.STR_D232)
                            || lsSvcode.equals(CommonConstants.STR_D234)
                            || lsSvcode.equals(CommonConstants.STR_D241)
                            || lsSvcode.equals(CommonConstants.STR_D242)
                            || lsSvcode.equals(CommonConstants.STR_D244)
                            || lsSvcode.equals(CommonConstants.STR_D251)
                            || lsSvcode.equals(CommonConstants.STR_D252)
                            || lsSvcode.equals(CommonConstants.STR_D254)) {
                        liRet = CommonConstants.INT_1;
                    }
                    break;
            }
        }
        // G3C46L153_202504改正_利用票別表計算_追加対応 END
        return liRet;

    }

    /**
     * f_cmn_set_sort : ソート順調整関数
     * 
     * @param adsSvPln   ソート対象
     * @param asTougaiYm 当該年月
     * @return ソート結果 ０ 以 上 : 成 功 マイナス : 失 敗
     * <AUTHOR>
     */
    public Integer setCmnSort(List<?> adsSvPln, String asTougaiYm) {
        // 変数定義
        // 利用票イメージデータウィンドウに行がなければ何もしない
        if (!CollectionUtils.isNullOrEmpty(adsSvPln)) {
            try {
                // ソート順を変える
                adsSvPln = adsSvPln.stream().sorted(Comparator.comparing(x -> {
                    try {
                        return Objects.toString(x.getClass().getMethod(CommonConstants.GET_SV_JIGYO_ID).invoke(x),
                                StringUtils.EMPTY);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, Comparator.naturalOrder()).thenComparing(x -> {
                    try {
                        return Objects.toString(x.getClass().getMethod(CommonConstants.GET_SV_START_TIME).invoke(x),
                                StringUtils.EMPTY);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, Comparator.naturalOrder()).thenComparing(x -> {
                    try {
                        return Objects.toString(x.getClass().getMethod(CommonConstants.GET_SV_ITEM_CD).invoke(x),
                                StringUtils.EMPTY);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, Comparator.naturalOrder())).collect(Collectors.toList());
                // ソートの為に、ダミーカラムに時間をセットする。
                for (Object item : adsSvPln) {
                    String lsSst = Objects
                            .toString(item.getClass().getMethod(CommonConstants.GET_SV_START_TIME).invoke(item));
                    if (lsSst != null) {
                        if (CommonConstants.KAISHI_JIKAN_9.equals(lsSst)) {
                            // 福祉用具貸与を短期入所と諸加算の前へ
                            invokeMethod(item, CommonConstants.SET_DMY_TIME, CommonConstants.TIME_77_55);
                        } else if (CommonConstants.KAISHI_JIKAN_8.equals(lsSst)) {
                            invokeMethod(item, CommonConstants.SET_DMY_TIME, CommonConstants.TIME_77_66);
                        } else {
                            // 通常
                            invokeMethod(item, CommonConstants.SET_DMY_TIME, lsSst);
                        }
                    }
                }
                // ソート順を変える２
                adsSvPln = adsSvPln.stream().sorted(Comparator.comparing(x -> {
                    try {
                        return Objects.toString(x.getClass().getMethod(CommonConstants.GET_SV_JIGYO_ID).invoke(x),
                                StringUtils.EMPTY);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, Comparator.naturalOrder()).thenComparing(x -> {
                    try {
                        return Objects.toString(x.getClass().getMethod(CommonConstants.GET_DMY_TIME).invoke(x),
                                StringUtils.EMPTY);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, Comparator.naturalOrder()).thenComparing(x -> {
                    try {
                        return Objects.toString(x.getClass().getMethod(CommonConstants.GET_SV_ITEM_CD).invoke(x),
                                StringUtils.EMPTY);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, Comparator.naturalOrder())).collect(Collectors.toList());
                String llSv0 = Objects.toString(adsSvPln.getFirst().getClass()
                        .getMethod(CommonConstants.GET_SV_JIGYO_ID).invoke(adsSvPln.getFirst()), StringUtils.EMPTY);
                for (int i = CommonConstants.INT_0; i < adsSvPln.size(); i++) {
                    Object item = adsSvPln.get(i);
                    String lsSst = Objects
                            .toString(item.getClass().getMethod(CommonConstants.GET_SV_START_TIME).invoke(item));
                    String llSv1 = Objects.toString(
                            item.getClass().getMethod(CommonConstants.GET_SV_JIGYO_ID).invoke(item), StringUtils.EMPTY);
                    if (lsSst != null) {
                        if (CommonConstants.KAISHI_JIKAN_7.equals(lsSst)) {
                            // 諸加算を該当サービスと同じ時間に
                            if (i > CommonConstants.INT_0 && llSv0.equals(llSv1)) {
                                String lsSet = Objects.toString(adsSvPln.get(i - CommonConstants.INT_1).getClass()
                                        .getMethod(CommonConstants.GET_DMY_TIME)
                                        .invoke(adsSvPln.get(i - CommonConstants.INT_1)));
                                invokeMethod(item, CommonConstants.SET_DMY_TIME, lsSet);
                            }
                        }
                    }
                    llSv0 = llSv1;
                }
                String lsScode = Objects.toString(adsSvPln.getFirst().getClass().getMethod(CommonConstants.GET_SCODE)
                        .invoke(adsSvPln.getFirst()));
                if (lsScode == null && CollectionUtils.size(adsSvPln) == CommonConstants.INT_1) {
                    // 何もしない
                } else {
                    setAdderServiceS(adsSvPln, asTougaiYm + CommonConstants.STRING_FIRST_DAY);
                }
                adsSvPln = adsSvPln.stream().sorted(Comparator.comparing(x -> {
                    try {
                        return Objects.toString(x.getClass().getMethod(CommonConstants.GET_SORT_NO).invoke(x),
                                StringUtils.EMPTY);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, Comparator.naturalOrder()).thenComparing(x -> {
                    try {
                        return Objects.toString(x.getClass().getMethod(CommonConstants.GET_DMY_TIME).invoke(x),
                                StringUtils.EMPTY);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, Comparator.naturalOrder()).thenComparing(x -> {
                    try {
                        return Objects.toString(x.getClass().getMethod(CommonConstants.GET_SV_JIGYO_ID).invoke(x),
                                StringUtils.EMPTY);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, Comparator.naturalOrder()).thenComparing(x -> {
                    try {
                        return Objects.toString(x.getClass().getMethod(CommonConstants.GET_ADDER_SV).invoke(x),
                                StringUtils.EMPTY);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, Comparator.naturalOrder()).thenComparing(x -> {
                    try {
                        return Objects.toString(x.getClass().getMethod(CommonConstants.GET_SV_START_TIME).invoke(x),
                                StringUtils.EMPTY);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, Comparator.naturalOrder()).thenComparing(x -> {
                    try {
                        return Objects.toString(x.getClass().getMethod(CommonConstants.GET_SV_ITEM_CD).invoke(x),
                                StringUtils.EMPTY);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, Comparator.naturalOrder())).collect(Collectors.toList());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return CommonConstants.INT_1;
    }

    /**
     * f_cmn_is_chk_cnv_code : コード変換の対象サービスかどうかを調べる
     * 
     * @param asScode サービスコード（８桁）
     * @param asYmd   基準日（yyyy/mm/dd）
     * @return 戻り値 : integer型 : 1=変換対象のサービス ／ 0=その他
     * <AUTHOR>
     */
    public Integer chkCnvCode(String asScode, String asYmd) {
        // 基準日のスラッシュを除去
        String lsYmd = nds3GkFunc01Logic.getCnvYymmdd(asYmd);

        String lsSvShuCd = asScode.substring(0, 2);
        String lsSvKouCd = ndsMidaLogic.fNdsMida(asScode, 3, 4);

        String[] lsMRank = new String[8];
        // 総合事業サービスかどうかをチェック
        if (chkSougouSvtype(lsSvShuCd)) {
            ComMhcSmKaigoByCriteriaInEntity comMhcSmKaigoByCriteriaInEntity = new ComMhcSmKaigoByCriteriaInEntity();
            comMhcSmKaigoByCriteriaInEntity.setLsSvShuCd(lsSvShuCd);
            comMhcSmKaigoByCriteriaInEntity.setLsSvKouCd(lsSvKouCd);
            comMhcSmKaigoByCriteriaInEntity.setLsYmd(lsYmd);
            // 総合事業サービスマスタ（国保連）
            List<ComMhcSmKaigoOutEntity> comMhcSmKaigoOutList = comMhcSmSelectMapper
                    .findComMhcSmKaigoByCriteria(comMhcSmKaigoByCriteriaInEntity);
            if (CollectionUtils.isNotEmpty(comMhcSmKaigoOutList)) {
                ComMhcSmKaigoOutEntity e = comMhcSmKaigoOutList.getFirst();
                lsMRank[0] = e.getKaigoSien();
                lsMRank[1] = e.getKaigo1();
                lsMRank[2] = e.getKaigo2();
                lsMRank[3] = e.getKaigo3();
                lsMRank[4] = e.getKaigo4();
                lsMRank[5] = e.getKaigo5();
                lsMRank[6] = e.getKaigoSien1();
                lsMRank[7] = e.getKaigoSien2();
            }
        } else {
            ComMhcKmKaigoByCriteriaInEntity comMhcKmKaigoByCriteriaInEntity = new ComMhcKmKaigoByCriteriaInEntity();
            comMhcKmKaigoByCriteriaInEntity.setLsSvShuCd(lsSvShuCd);
            comMhcKmKaigoByCriteriaInEntity.setLsSvKouCd(lsSvKouCd);
            comMhcKmKaigoByCriteriaInEntity.setLsYmd(lsYmd);
            // サービスコードにて厚労省介護マスタの要介護度の要素を取得
            List<ComMhcKmKaigoOutEntity> comMhcKmKaigoOutList = comMhcKmSelectMapper
                    .findComMhcKmKaigoByCriteria(comMhcKmKaigoByCriteriaInEntity);
            if (CollectionUtils.isNotEmpty(comMhcKmKaigoOutList)) {
                ComMhcKmKaigoOutEntity e = comMhcKmKaigoOutList.getFirst();
                lsMRank[0] = e.getKaigoSien();
                lsMRank[1] = e.getKaigo1();
                lsMRank[2] = e.getKaigo2();
                lsMRank[3] = e.getKaigo3();
                lsMRank[4] = e.getKaigo4();
                lsMRank[5] = e.getKaigo5();
                lsMRank[6] = e.getKaigoSien1();
                lsMRank[7] = e.getKaigoSien2();
            }
        }
        // 要介護度と関係ないサービスコードの場合は0を返す
        if (StringUtils.isEmpty(lsMRank[0]) && StringUtils.isEmpty(lsMRank[1]) && StringUtils.isEmpty(lsMRank[2])
                && StringUtils.isEmpty(lsMRank[3]) && StringUtils.isEmpty(lsMRank[4]) && StringUtils.isEmpty(lsMRank[5])
                && StringUtils.isEmpty(lsMRank[6]) && StringUtils.isEmpty(lsMRank[7])) {
            return 0;
        } else {
            // 要介護度と関係あるサービスコードの場合は1を返す
            return 1;
        }
    }

    /**
     * 関数名 ： f_cmn_get_tanto_information 処理概要 ： 利用者idから担当ケアマネの名前を取得する
     * 
     * @param alUser  : 利用者id
     * @param asYmdSt : 検索期間・開始日
     * @param asYmdEd : 検索期間・終了日
     * @param plTanto : Ref : 担当ケアマネid
     * @param psTanto : Ref : 担当ケアマネの支援専門員番号
     * @param svjigyo : 事業所id
     * @return ｻｰﾋﾞｽ検索の開始、終了をtermid取得の出力DTO.
     * <AUTHOR> 李晨昊
     */
    public CmnTantoInformationOutDto getCmnTantoInformation(Integer alUser, String asYmdSt, String asYmdEd,
            Integer plTanto, String psTanto, Integer svjigyo) {
        CmnTantoInformationOutDto outDto = new CmnTantoInformationOutDto();
        // 担当ケアマネID
        Integer llTanto = CommonConstants.NUMBER_0;

        ComTucTantoDataByCriteriaInEntity tantoInEntity = new ComTucTantoDataByCriteriaInEntity();
        // "com_tuc_tanto"."userid" = :al_user AND
        tantoInEntity.setAlUser(alUser);
        // "com_tuc_tanto"."sv_jigyo_id" = :gNdsIni.istr_jigyosha.j_id AND
        tantoInEntity.setSvJigyoId(svjigyo);
        // ("com_tuc_tanto"."start_ymd" <= :as_ymd_ed AND
        tantoInEntity.setAsYmdEd(asYmdEd);
        // ("com_tuc_tanto"."end_ymd" >= :as_ymd_st OR
        tantoInEntity.setAsYmdSt(asYmdSt);
        List<ComTucTantoDataOutEntity> tantoOutList = comTucTantoSelectMapper
                .findComTucTantoDataByCriteria(tantoInEntity);
        // if IsNull( ll_tanto ) or ll_tanto <= 0
        boolean tantoNullFlg = false;

        // if SQLCA.SQLCode = 0 then
        if (CollectionUtils.isNotEmpty(tantoOutList)) {
            llTanto = tantoOutList.getFirst().getTantoId();

            // if IsNull(ll_tanto) then ll_tanto = 0
            if (llTanto == null || llTanto <= CommonConstants.NUMBER_0) {
                llTanto = CommonConstants.NUMBER_0;
                tantoNullFlg = true;
            }
        } else {
            tantoNullFlg = true;
        }

        // 履歴が無いなら、
        if (tantoNullFlg) {
            // ②利用者情報テーブルの担当ｹｱﾏﾈIDを検索
            TantouIdUserByCriteriaInEntity tantouIdInEntity = new TantouIdUserByCriteriaInEntity();
            // id = :al_user
            tantouIdInEntity.setLlUserid(alUser);
            List<TantouIdUserOutEntity> tantouIdOutList = comTucUserSelectMapper
                    .findTantouIdUserByCriteria(tantouIdInEntity);
            if (CollectionUtils.isNotEmpty(tantouIdOutList)) {

                llTanto = tantouIdOutList.getFirst().getTantoId();
                if (llTanto == null) {
                    llTanto = CommonConstants.NUMBER_0;
                }
            }
        }
        // 担当ケアマネの名前
        String rtnLsName = StringUtils.EMPTY;
        // 担当ケアマネの支援専門員番号
        String rtnPsTanto = StringUtils.EMPTY;
        if (llTanto > CommonConstants.NUMBER_0) {
            // 担当ケアマネの名前を取得する
            rtnLsName = kghCmpF01Logic.getCmpTantoName(llTanto);
            // 職員マスタから、介護支援専門員番号を取得する
            rtnPsTanto = this.getCmnSenmonNo2(llTanto);
        }
        outDto.setPlTanto(llTanto);
        outDto.setPsTanto(rtnPsTanto);
        outDto.setLsName(rtnLsName);
        return outDto;
    }

    /**
     * 関数名 ： f_cmn_get_senmon_no2 処理概要 ： 職員マスタから、介護支援専門員番号を取得する
     * 
     * @param alShokuId : 担当ケアマネの職員ID
     * @return as_senmon 介護支援専門員番号
     * <AUTHOR> 李晨昊
     */
    public String getCmnSenmonNo2(Integer alShokuId) {
        SenmonNoByCriteriaInEntity senmonInEntity = new SenmonNoByCriteriaInEntity();
        senmonInEntity.setAlShokuId(CommonDtoUtil.objValToString(alShokuId));
        List<SenmonNoOutEntity> senmonOutList = comMscShokuinSelectMapper.findSenmonNoByCriteria(senmonInEntity);

        if (CollectionUtils.isNotEmpty(senmonOutList)) {
            return senmonOutList.getFirst().getSenmonNo();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 関数名 ： f_cmn_pln_is_svt2_iryohi_kojo 処理概要 ： 医療費控除ＳＶかどうか
     * 
     * @param as_yymm  : サービス提供年月（yyyy/mm）
     * @param as_scode : サービスコード
     * @return as_senmon 介護支援専門員番号
     * <AUTHOR> 李晨昊
     */
    public Integer getCmnPlnIsSvt2IryohiKojo(String asYymm, String asScode) {
        if (StringUtils.length(asScode) > 0) {
            String leftTwo = StringUtils.left(asScode, 2);
            // Case "13" , "14" , "16" , "22" , "23" , "31" , "76" , "77", "2A"
            if (CommonConstants.SCODE_LEFT_2_LIST_1.contains(leftTwo)) {
                return CommonConstants.NUMBER_1;
            }
            // Case "63" , "64" , "66" , "25" , "26" , "34", "2B"
            else if (CommonConstants.SCODE_LEFT_2_LIST_2.contains(leftTwo)) {
                // 予防サービス分は、法改正後に控除対象と判断するようにする
                if (asYymm.compareTo(CommonConstants.DMY_TIME_200604) >= 0) {
                    return CommonConstants.NUMBER_1;
                }
            }
        }
        return CommonConstants.NUMBER_0;
    }

    /**
     * ｻｰﾋﾞｽ利用開始日の確認 f_cmn_chk_svriyo_timing.srf
     * 
     * @param shienId サービス提供事業所id
     * @param userId  利用者id
     * @param yyMM    処理年月（yyyy/mm）
     * @return 計画開始日
     * @throws Exception
     */
    public String getSvriyoTiming(Integer shienId, Integer userId, String yyMM) throws Exception {
        // 月初め日
        String ymdSt = yyMM + CommonConstants.STRING_FIRST_DAY;
        String lsWork = nds3GkFunc01Logic.getTukimatu(ymdSt);
        String ymdEd = StringUtils.EMPTY;
        String lsSvd = StringUtils.EMPTY;
        if (StringUtils.isNotEmpty(lsWork)) {
            ymdEd = nds3GkFunc01Logic.cnvYmd(ymdSt);
        } else {
            // 月末日取得失敗時は自前で算定
            Integer[] liD = { 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31 };
            Integer liY = CommonDtoUtil.strValToInt(yyMM.substring(0, 4));
            Integer liM = CommonDtoUtil.strValToInt(nds3GkFunc01Logic.getNdsRighta(yyMM, 2));
            if ((liY % 400 == 0) || ((liY % 100 != 0) && (liY % 4 == 0))) {
                liD[1] = 29;
            }
            ymdEd = yyMM + CommonConstants.STR_DELIMITER + String.format(CommonConstants.FORMAT_02d, liD[(int) liM]);
        }
        // 提供年月の月末から遡って、最も近い（有効な）開始日を取得する
        CmnChkSvriyoTimingByCriteriaInEntity cmnChkSvriyoTimingByCriteriaInEntity = new CmnChkSvriyoTimingByCriteriaInEntity();
        cmnChkSvriyoTimingByCriteriaInEntity.setShien(shienId);
        cmnChkSvriyoTimingByCriteriaInEntity.setUser(userId);
        cmnChkSvriyoTimingByCriteriaInEntity.setDate1(ymdSt);
        cmnChkSvriyoTimingByCriteriaInEntity.setDate2(ymdEd);
        // ｻｰﾋﾞｽ利用の開始日を取得
        List<CmnChkSvriyoTimingOutEntity> cmnChkSvriyoTimingOutEntityList = comTucSvriyoSelectMapper
                .findCmnChkSvriyoTimingByCriteria(cmnChkSvriyoTimingByCriteriaInEntity);
        if (!CollectionUtils.isNullOrEmpty(cmnChkSvriyoTimingOutEntityList)) {
            CmnChkSvriyoTimingOutEntity outEntity = cmnChkSvriyoTimingOutEntityList.getFirst();
            lsSvd = outEntity.getStartYmd();
        } else {
            // 無いなら月初を返す
            return ymdSt;
        }

        // 介護保険の開始日を取得
        CmnChkHokenTimingByCriteriaInEntity cmnChkHokenTimingByCriteriaInEntity = new CmnChkHokenTimingByCriteriaInEntity();
        cmnChkHokenTimingByCriteriaInEntity.setUser(userId);
        cmnChkHokenTimingByCriteriaInEntity.setDate(lsSvd);
        List<CmnChkHokenTimingOutEntity> cmnChkHokenTimingOutEntityList = comTucKaigoHokenSelectMapper
                .findCmnChkHokenTimingByCriteria(cmnChkHokenTimingByCriteriaInEntity);
        if (CollectionUtils.isNullOrEmpty(cmnChkHokenTimingOutEntityList)) {
            return ymdSt;
        }

        if (CommonDtoUtil.strValToInt(lsSvd) < CommonDtoUtil.strValToInt(ymdSt)) {
            return ymdSt;
        } else {
            return lsSvd;
        }
    }

    /**
     * f_cmn_is_shintai_kousoku_genzan :
     * 身体拘束廃止未実施減算を特定する（2025年4月改正より事業種別が増加したため共通関数化）
     * 
     * @param asDate 提供年月日（yyyy/mm/dd）
     * @param asScd  サービスコード
     * @return 0=それ以外／1=身体拘束廃止未実施減算
     * <AUTHOR>
     */
    public Integer isShintaiKousokuGenzan(String asDate, String asScd) {
        String lsSvtype;
        String lsSvcode;
        Integer liRet;

        lsSvtype = StringUtils.substring(asScd, CommonConstants.INT_0, CommonConstants.INT_2);
        lsSvcode = StringUtils.substring(asScd, CommonConstants.INT_2, CommonConstants.INT_6);

        liRet = CommonConstants.INT_0;

        if (StringUtils.isNotEmpty(asDate)
                && asDate.compareTo(CommonConstants.DATA_20180401) >= CommonConstants.INT_0) {
            switch (lsSvtype) {
                case CommonConstants.SV_CD_STR_32:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_6304) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_6313) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 10レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_33:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_6304) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_6308) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 5レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_35:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_6304) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_6305) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 2レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_36:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_6304) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_6308) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 5レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_37:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_6304) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_6305) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 2レコード
                    }
                    break;
            }
        }
        if (StringUtils.isNotEmpty(asDate)
                && asDate.compareTo(CommonConstants.DATA_20250401) >= CommonConstants.INT_0) {
            switch (lsSvtype) {
                case CommonConstants.SV_CD_STR_21:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E280) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 80レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_22:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E303) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 103レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_23:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E396) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 196レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_24:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E232) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 32レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_25:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E240) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 40レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_26:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E276) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 76レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_27:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E205) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 5レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_28:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E205) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 5レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_2A:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E333) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 133レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_2B:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E252) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 52レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_33:
                    if (CommonConstants.NUM_STR_E206.equals(lsSvcode)) {
                        liRet = CommonConstants.INT_1;
                        // 外部特定施設身体拘束廃止未実施減算
                    }
                    break;
                case CommonConstants.SV_CD_STR_35:
                    if (CommonConstants.NUM_STR_E203.equals(lsSvcode)) {
                        liRet = CommonConstants.INT_1;
                        // 予防外部特定施設身体拘束廃止未実施減算
                    }
                    break;
                case CommonConstants.SV_CD_STR_38:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E210) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 10レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_39:
                    if (CommonConstants.NUM_STR_E202.equals(lsSvcode)
                            || CommonConstants.NUM_STR_E204.equals(lsSvcode)) {
                        liRet = CommonConstants.INT_1;
                        // 予短期共同身体拘束廃止未実施減算Ⅰ２
                        // 予短期共同身体拘束廃止未実施減算Ⅱ２
                    }
                    break;
                case CommonConstants.SV_CD_STR_68:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E205) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 5レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_69:
                    if (CommonConstants.NUM_STR_E201.equals(lsSvcode)
                            || CommonConstants.NUM_STR_E202.equals(lsSvcode)) {
                        liRet = CommonConstants.INT_1;
                        // 予短期小多機能身体拘束廃止未実施減算１
                        // 予短期小多機能身体拘束廃止未実施減算２
                    }
                    break;
                case CommonConstants.SV_CD_STR_73:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E220) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 20レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_75:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E208) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 8レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_77:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E220) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 20レコード
                    }
                    break;
                case CommonConstants.SV_CD_STR_79:
                    if (lsSvcode.compareTo(CommonConstants.NUM_STR_E201) >= CommonConstants.INT_0
                            && lsSvcode.compareTo(CommonConstants.NUM_STR_E205) <= CommonConstants.INT_0) {
                        liRet = CommonConstants.INT_1;
                        // 身体拘束廃止未実施減算 5レコード
                    }
                    break;
            }
        }
        return liRet;
    }
}
