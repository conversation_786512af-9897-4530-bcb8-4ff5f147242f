package jp.ndsoft.carebase.cmn.api.dao.mybatis.entity;

import java.io.Serializable;
import jp.ndsoft.smh.framework.global.base.entity.IEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * GUI01272_特記事項リスト
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class Gui01272TokkiSave implements Serializable, IEntity {
    /** serialVersionUID. */
    private static final long serialVersionUID = 1L;

    /** カウンター */
    private String counter;

    /** 認定（特記：大） */
    private String n1tCd;

    /** 認定（特記：小） */
    private String n2tCd;

    /** 特記事項 */
    private String memoKnj;

    /** 表示順 */
    private String seqNo;

    /** 更新区別 */
    private String updateKbn;

}
