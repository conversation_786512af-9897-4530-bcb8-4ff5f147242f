package jp.ndsoft.carebase.cmn.api.report.logic;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jp.ndsoft.carebase.cmn.api.logic.KghCmpF01Logic;
import jp.ndsoft.carebase.cmn.api.logic.KghKrkZCpnFuncLogic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkBase02Logic;
import jp.ndsoft.carebase.cmn.api.logic.Nds3GkFunc01Logic;
import jp.ndsoft.carebase.cmn.api.logic.dto.F3gkGetProfileInDto;
import jp.ndsoft.carebase.cmn.api.logic.dto.JigyoRirekiInfoDto;
import jp.ndsoft.carebase.cmn.api.report.common.ReportConstants;
import jp.ndsoft.carebase.cmn.api.report.dto.KyotakuCpnTucCks22ReportDto;
import jp.ndsoft.carebase.cmn.api.report.dto.KyotakuServiceReportServiceInDto;
import jp.ndsoft.carebase.cmn.api.report.dto.KyotakuServiceReportServiceOutDto;
import jp.ndsoft.carebase.cmn.api.report.model.KyotakuServiceReportParameterModel;
import jp.ndsoft.carebase.cmn.api.report.service.ShoninSetReportService;
import jp.ndsoft.carebase.cmn.api.report.util.ReportUtil;
import jp.ndsoft.carebase.cmn.api.util.CommonDtoUtil;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCks21DataByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCks21DataOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCks22ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.CpnTucCks22OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnMstChouhyouInkanPrnOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KghCpnRaiMonKentPrnPreSrw2OutEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinHogoJohoSetteiByCriteriaInEntity;
import jp.ndsoft.carebase.common.dao.mysql.entity.KojinHogoJohoSetteiOutEntity;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMocSysiniSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.ComMscShokuinSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnMstChouhyouInkanSelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks21SelectMapper;
import jp.ndsoft.carebase.common.dao.mysql.mapper.CpnTucCks22SelectMapper;
import jp.ndsoft.smh.framework.properties.FrameworkProperties;
import net.sf.jasperreports.components.table.DesignCell;
import net.sf.jasperreports.components.table.StandardColumn;
import net.sf.jasperreports.components.table.StandardRow;
import net.sf.jasperreports.components.table.StandardTable;
import net.sf.jasperreports.engine.JRBand;
import net.sf.jasperreports.engine.JRSection;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.design.JRDesignBand;
import net.sf.jasperreports.engine.design.JRDesignComponentElement;
import net.sf.jasperreports.engine.design.JRDesignFrame;
import net.sf.jasperreports.engine.design.JRDesignTextField;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.type.SplitTypeEnum;
import net.sf.jasperreports.engine.xml.JRXmlLoader;

/**
 * @since 2025.07.17
 * <AUTHOR> 李晨昊
 * @description U0082K_居宅サービス計画書（２） 帳票出力
 */
@Component
public class KyotakuServiceReportLogic {
    /** 計画書（２）ヘッダ情報 取得 */
    @Autowired
    private CpnTucCks21SelectMapper cpnTucCks21SelectMapper;

    /** 28-21 計画書（２）データ */
    @Autowired
    private CpnTucCks22SelectMapper cpnTucCks22SelectMapper;

    /** 印鑑欄設定情報取得 */
    @Autowired
    private CpnMstChouhyouInkanSelectMapper cpnMstChouhyouInkanSelectMapper;

    /** 新書式の場合、個人情報保護設定の取得 */
    @Autowired
    private ComMocSysiniSelectMapper comMocSysiniSelectMapper;

    /** 個人情報保護フラグの取得関数 */
    @Autowired
    private Nds3GkFunc01Logic nds3GkFunc01Logic;

    /** 03-01 職員基本情報取得 */
    @Autowired
    private ComMscShokuinSelectMapper comMscShokuinSelectMapper;

    /** 和暦変換 */
    @Autowired
    private KghCmpF01Logic kghCmpF01Logic;

    /** 事業所名取得関数 */
    @Autowired
    private KghKrkZCpnFuncLogic kghKrkZCpnFuncLogic;

    /** 個人情報保護関数 個人情報保護フラグ取得 利用者名置換 */
    @Autowired
    private Nds3GkBase02Logic nds3GkBase02Logic;

    // 承認欄設定
    @Autowired
    private ShoninSetReportService shoninSetReportService;

    /**
     * U0082K_居宅サービス計画書（２）帳票パラメータ取得
     * 
     * @param model   入力データ
     * @param outDto  出力データ
     * @param fwProps fwProps
     * @return PDF帳票パラメータリスト
     * @throws Exception 例外
     */
    public KyotakuServiceReportServiceInDto getKyotakuServiceReportParameters(KyotakuServiceReportParameterModel model,
            KyotakuServiceReportServiceOutDto outDto, FrameworkProperties fwProps) throws Exception {
        // ファイルサーバにアップロードするか設定
        model.setUploadFileServer(ReportUtil.getUploadFileServerSetting(model));

        // 帳票用データ詳細
        KyotakuServiceReportServiceInDto infoInDto = new KyotakuServiceReportServiceInDto();

        // タイトル
        infoInDto.setTitle(model.getTitle());

        // 基本データの編集
        editBasicInfo(model, infoInDto);

        // 2. リクエストパラメータ.記入用シートを印刷するフラグ = true
        // の場合、結果レスポンスを返却する。
        if (ReportConstants.STR_TRUE.equals(model.getEmptyFlg())) {
            // ※返却する情報の編集要領は「帳票用データ詳細」を参照する。
            this.getKyotakuServiceReportParams(infoInDto);
        }
        // リクエストパラメータ.記入用シートを印刷するフラグ = false の場合、
        else {
            // 計画書（２）ヘッダ情報情報の取得。
            falseProc(model, infoInDto);
        }

        String sourceFilePath = ReportUtil.getReportJrxmlFile(fwProps, ReportConstants.JRXML_KYOTAKU_SERVICE_REPORT);
        JasperDesign design = JRXmlLoader.load(sourceFilePath);

        JRSection detailSection = (JRSection) design.getDetailSection();
        // 8. リクエストパラメータ.記入用シートを印刷するフラグ = true の場合、行の高さを「330」に設定します。
        if (ReportConstants.STR_TRUE.equals(model.getEmptyFlg())) {
            if (detailSection != null) {
                // セクション内のバンドリストを取得します(通常DetailBandは1つです)
                JRBand[] bands = detailSection.getBands();
                if (bands != null && bands.length > 0) {
                    JRBand band = bands[0];
                    if (band instanceof JRDesignBand) {
                        JRDesignBand designBand = (JRDesignBand) band;

                        // 枠付き上書き要素を作成します
                        JRDesignFrame frame = (JRDesignFrame) designBand.getChildren().get(0);
                        JRDesignComponentElement component = (JRDesignComponentElement) frame.getChildren().get(0);
                        StandardTable table = (StandardTable) component.getComponent();
                        StandardColumn standardColumn = (StandardColumn) table.getColumns().get(0);
                        DesignCell designCell = (DesignCell) standardColumn.getDetailCell();
                        // テーブルのセルは同時に高さを変えます
                        designCell.setHeight(340);
                        // 行高の修正-列の修正によって定義されます。
                        // テーブルにある10のtextFieldの高さ設定です
                        for (int i = 0; i < 11; i++) {
                            JRDesignTextField field = (JRDesignTextField) designCell.getChildren().get(i);
                            field.setHeight(330);
                        }
                    }
                }
            }
        }

        // 9. リクエストパラメータ.先頭フラグを印刷するフラグ = true の場合、テーブルのSplitTypeを"Prevent"に設定します。
        if (ReportConstants.STR_1.equals(model.getSentouFlg())) {
            if (detailSection != null) {
                // セクション内のバンドリストを取得します(通常DetailBandは1つです)
                JRBand[] bands = detailSection.getBands();
                for (JRBand band : bands) {
                    JRDesignFrame frame = (JRDesignFrame) band.getChildren().get(0);
                    if (frame.getChildren().get(0) instanceof JRDesignComponentElement) {
                        JRDesignComponentElement component = (JRDesignComponentElement) frame.getChildren().get(0);
                        StandardTable table = (StandardTable) component.getComponent();
                        StandardRow row = (StandardRow) table.getDetail();
                        row.setSplitType(SplitTypeEnum.PREVENT);
                    }
                }
            }
        }
        return infoInDto;
    }

    /**
     * 基本データの編集
     * 
     * @param model     入力データ
     * @param infoInDto 帳票用データ詳細
     */
    private void editBasicInfo(KyotakuServiceReportParameterModel model, KyotakuServiceReportServiceInDto infoInDto) {
        // 指定日印刷区分 = リクエストパラメータ.指定日印刷区分
        infoInDto.setShiTeiDateSet(model.getShiTeiKubun());
        // 作成日印刷区分 = リクエストパラメータ.作成日印刷区分
        infoInDto.setSaKuSeiSet(model.getSaKuSeiKubun());
        // 敬称変更フラグ = リクエストパラメータ.敬称変更フラグ
        infoInDto.setKeishoFlg(model.getKeishoFlg());
        // 敬称 = ・リクエストパラメータ.敬称変更フラグ＝"1"の場合、敬称＝リクエストパラメータ.敬称
        if (ReportConstants.STR_1.equals(model.getKeishoFlg())) {
            infoInDto.setKeisho(model.getKeisho());
        } // ・上記以外の場合、敬称＝”殿”
        else {
            infoInDto.setKeisho(ReportConstants.KEISHO_TONO);
        }
        // 先頭フラグ = リクエストパラメータ.先頭フラグ
        infoInDto.setSentouFlg(model.getSentouFlg());
        // 記入用シートを印刷するフラグ = リクエストパラメータ.記入用シートを印刷するフラグ
        infoInDto.setEmptyFlg(model.getEmptyFlg());
        // 要介護度フラグ = リクエストパラメータ.要介護度フラグ
        infoInDto.setYokaiFlg(model.getYokaiFlg());
        // 事業名略称印刷フラグ = リクエストパラメータ.事業名略称印刷フラグ
        infoInDto.setJigyoRyakuKnjFlg(model.getJigyoRyakuKnjFlg());
        // 承認欄を印刷するフラグ = リクエストパラメータ.承認欄を印刷するフラグ
        infoInDto.setShoninFlg(model.getShoninFlg());
    }

    /**
     * 記入用シートを印刷するフラグ = true の場合、U0082K_居宅サービス計画書（２）帳票パラメータ取得
     * 
     * @param infoInDto 帳票用データ詳細
     */
    private void getKyotakuServiceReportParams(KyotakuServiceReportServiceInDto infoInDto) {
        // ・リクエストパラメータ.記入用シートを印刷するフラグ = true の場合、
        // "印鑑欄表示区分=0
        infoInDto.setInkanRyou(ReportConstants.STR_0);
        // "印鑑1=""""
        infoInDto.setChouhyouInkan1(StringUtils.EMPTY);
        // "印鑑2=""""
        infoInDto.setChouhyouInkan2(StringUtils.EMPTY);
        // "印鑑3=""""
        infoInDto.setChouhyouInkan3(StringUtils.EMPTY);
        // "印鑑4=""""
        infoInDto.setChouhyouInkan4(StringUtils.EMPTY);
        // "印鑑5=""""
        infoInDto.setChouhyouInkan5(StringUtils.EMPTY);
        // "印鑑6=""""
        infoInDto.setChouhyouInkan6(StringUtils.EMPTY);
        // "印鑑7=""""
        infoInDto.setChouhyouInkan7(StringUtils.EMPTY);
        // "印鑑8=""""
        infoInDto.setChouhyouInkan8(StringUtils.EMPTY);
        // "印鑑9=""""
        infoInDto.setChouhyouInkan9(StringUtils.EMPTY);
        // "印鑑10=""""
        infoInDto.setChouhyouInkan10(StringUtils.EMPTY);
        // "印鑑11=""""
        infoInDto.setChouhyouInkan11(StringUtils.EMPTY);
        // "印鑑12=""""
        infoInDto.setChouhyouInkan12(StringUtils.EMPTY);
        // "印鑑13=""""
        infoInDto.setChouhyouInkan13(StringUtils.EMPTY);
        // "印鑑14=""""
        infoInDto.setChouhyouInkan14(StringUtils.EMPTY);
        // "印鑑15=""""
        infoInDto.setChouhyouInkan15(StringUtils.EMPTY);
        // 指定日=""
        infoInDto.setShiTeiDate(StringUtils.EMPTY);
        // 作成年月日=""
        infoInDto.setSaKuSeiYmd(StringUtils.EMPTY);
        // 印刷枠の高さを自動調整フラグ=false
        infoInDto.setHeightAutoFlg(ReportConstants.STR_FALSE);
    }

    /**
     * リクエストパラメータ.記入用シートを印刷するフラグ = false の場合、計画書（２）ヘッダ情報情報の取得。
     * 
     * @param model     入力データ
     * @param infoInDto 帳票用データ詳細
     */
    private void falseProc(KyotakuServiceReportParameterModel model, KyotakuServiceReportServiceInDto infoInDto) {
        // 指定日の編集
        editShiTeiDate(model, infoInDto);

        // 印刷枠の高さを自動調整フラグ =リクエストパラメータ.印刷枠の高さを自動調整フラグ
        infoInDto.setHeightAutoFlg(model.getHeightAutoFlg());

        // 2.1. 下記の計画書（２）ヘッダ情報取得のDAOを利用し、計画書（２）ヘッダ情報を取得する。
        CpnTucCks21DataByCriteriaInEntity cks21InEntity = new CpnTucCks21DataByCriteriaInEntity();
        // 計画期間ID リクエストパラメータ.計画期間ID
        cks21InEntity.setAlSc1(CommonDtoUtil.strValToInt(model.getSc1Id()));
        // 計画書2ID リクエストパラメータ.計画書ID
        cks21InEntity.setAlKs21(CommonDtoUtil.strValToInt(model.getKs11Id()));
        List<CpnTucCks21DataOutEntity> cks21OutList = cpnTucCks21SelectMapper
                .findCpnTucCks21DataByCriteria(cks21InEntity);

        // 2.2. 下記の計画書（２）データ情報取得のDAOを利用し、計画書（２）データ情報を取得する。
        List<CpnTucCks22OutEntity> cks22OutList = acquDetailInfo(model, infoInDto);

        // 3. 「2.2」で取得した計画書（２）情報.総件数 > 0 件の場合、印鑑欄設定情報を取得する。
        acquChouInfo(model, infoInDto, cks22OutList);

        if (CollectionUtils.isNotEmpty(cks21OutList)) {
            // 「2.1.」で取得した作成日
            String chgCreateYmd = cks21OutList.getFirst().getCreateYmd();
            // 「2.1.」で取得した作成者
            Integer chgShokuId = cks21OutList.getFirst().getShokuId();

            // 4. 要介護度、事業履歴より事業所名、利用者名、職員基本情報を取得する。
            acquYoukaiInfo(model, infoInDto, chgCreateYmd, chgShokuId);

            // ５.リクエストパラメータ.作成年月日印刷区分を印刷するフラグが”true”の場合、当該年月の設定
            acquSakuYmd(model, infoInDto, chgCreateYmd);
        }
    }

    /**
     * 指定日の編集
     * 
     * @param model     入力データ
     * @param infoInDto 帳票用データ詳細
     */
    private void editShiTeiDate(KyotakuServiceReportParameterModel model, KyotakuServiceReportServiceInDto infoInDto) {
        String chgShiTeiDate = StringUtils.EMPTY;
        // ・リクエストパラメータ.指定日印刷区分＝2 の場合（※2：指定日印刷）
        if (ReportConstants.STR_2.equals(model.getShiTeiKubun())) {
            // API定義の処理「7.1」の指定日（和暦）
            chgShiTeiDate = nds3GkFunc01Logic.get2Gengouj(ReportConstants.INT_1, model.getShiTeiDate());
        }
        // ・リクエストパラメータ.指定日印刷区分＝3 の場合（※3：日付空欄印刷）
        else if (ReportConstants.STR_3.equals(model.getShiTeiKubun())) {
            // API定義の処理「6.1」のシステム日付元号
            chgShiTeiDate = nds3GkFunc01Logic.blankDate(model.getSystemDate());
        }
        // ・リクエストパラメータ.指定日印刷区分＝1 の場合（※1：印刷しない）、指定日=""
        else if (ReportConstants.STR_1.equals(model.getShiTeiKubun())) {
            chgShiTeiDate = StringUtils.EMPTY;
        }
        infoInDto.setShiTeiDate(chgShiTeiDate);
    }

    /**
     * 2.2. 下記の計画書（２）データ情報取得のDAOを利用し、計画書（２）データ情報を取得する。
     * 
     * @param model     入力データ
     * @param infoInDto 帳票用データ詳細
     * @return 「2.2」で取得した計画書（２）情報
     */
    private List<CpnTucCks22OutEntity> acquDetailInfo(KyotakuServiceReportParameterModel model,
            KyotakuServiceReportServiceInDto infoInDto) {
        CpnTucCks22ByCriteriaInEntity cks22InEntity = new CpnTucCks22ByCriteriaInEntity();
        // 計画書2ID リクエストパラメータ.計画書ID
        cks22InEntity.setAlKs21(CommonDtoUtil.strValToInt(model.getKs11Id()));
        // データリスト
        List<KyotakuCpnTucCks22ReportDto> infoList = new ArrayList<>();
        // 28-21 計画書（２）データ
        List<CpnTucCks22OutEntity> cks22OutList = cpnTucCks22SelectMapper.findCpnTucCks22ByCriteria(cks22InEntity);
        for (CpnTucCks22OutEntity cpnTucCks22Out : cks22OutList) {
            KyotakuCpnTucCks22ReportDto info = new KyotakuCpnTucCks22ReportDto();
            // 課題番号 = API定義の処理「2.2.」の課題番号
            info.setKaDaiNo(CommonDtoUtil.objValToString(cpnTucCks22Out.getKadaiNo()));
            // 生活全般の解決すべき課題(ニーズ) = API定義の処理「2.2.」の具体的
            info.setKaDai(cpnTucCks22Out.getGutaitekiKnj());
            // 長期目標 = API定義の処理「2.2.」の長期
            info.setChoukiMokuhyou(cpnTucCks22Out.getChoukiKnj());
            // (期間) = API定義の処理「2.2.」の長期期間
            info.setChoukiKikan(cpnTucCks22Out.getChoKikanKnj());
            // 短期目標 = API定義の処理「2.2.」の短期
            info.setTankiMokuhyou(cpnTucCks22Out.getTankiKnj());
            // (期間) = API定義の処理「2.2.」の短期期間
            info.setTankiKikan(cpnTucCks22Out.getTanKikanKnj());
            // 番号 = API定義の処理「2.2.」の介護番号
            info.setJno(CommonDtoUtil.objValToString(cpnTucCks22Out.getKaigoNo()));
            // サービス内容 = API定義の処理「2.2.」の介護
            info.setService(cpnTucCks22Out.getKaigoKnj());
            // ※1 = API定義の処理「2.2.」の給付文字
            info.setHokenKbn(CommonDtoUtil.objValToString(cpnTucCks22Out.getHkyuKnj()));
            // サービス種別 = API定義の処理「2.2.」のサービス種
            info.setServiceSyu(cpnTucCks22Out.getSvShuKnj());
            // ※2 = API定義の処理「2.2.」のサービス事業者名
            info.setJigyoushaCd(cpnTucCks22Out.getJigyoNameKnj());
            // 頻度 = API定義の処理「2.2.」の頻度
            info.setHindo(cpnTucCks22Out.getHindoKnj());
            // 期間 = API定義の処理「2.2.」の期間
            info.setJissiKikan(cpnTucCks22Out.getKikanKnj());
            infoList.add(info);
        }

        // 10. 帳票用データ詳細.No.48、No.49 データ共通フラグ、帳票用データ詳細.No.50、No.51 データ共通フラグ、No.52、No.53
        // データ共通フラグを設定する。
        // ・ 帳票用データ詳細.データリストを先頭から末尾方向にループ処理する
        for (Integer i = 0; i < CollectionUtils.size(infoList) - 1; i++) {
            // ・ 現在処理中の要素をi、次の要素をi+1とする
            KyotakuCpnTucCks22ReportDto nowInfo = infoList.get(i);
            KyotakuCpnTucCks22ReportDto nextInfo = infoList.get(i + 1);
            // ・iの「帳票用データ詳細.課題番号」とi+1の「帳票用データ詳細.課題番号」を比較する
            if (CommonDtoUtil.strNullToEmpty(nowInfo.getKaDaiNo())
                    .equals(CommonDtoUtil.strNullToEmpty(nextInfo.getKaDaiNo()))) {
                // ・両者が一致する場合、iの「帳票用データ詳細.No.48、No.49 データ共通フラグ」l1flgフィールドを”0”に設定する
                nowInfo.setL1flg(ReportConstants.STR_0);
            }

            // ・iの「帳票用データ詳細.長期目標」とi+1の「帳票用データ詳細.長期目標」を比較する
            if (CommonDtoUtil.strNullToEmpty(nowInfo.getChoukiMokuhyou())
                    .equals(CommonDtoUtil.strNullToEmpty(nextInfo.getChoukiMokuhyou()))) {
                // ・両者が一致する場合、iの「No.50、No.51 データ共通フラグ」l2flgフィールドを”0”に設定する
                nowInfo.setL2flg(ReportConstants.STR_0);
            }
            // ・iの「帳票用データ詳細.短期目標」とi+1の「帳票用データ詳細.短期目標」を比較する
            if (CommonDtoUtil.strNullToEmpty(nowInfo.getTankiMokuhyou())
                    .equals(CommonDtoUtil.strNullToEmpty(nextInfo.getTankiMokuhyou()))) {
                // ・両者が一致する場合、iの「No.52、No.53 データ共通フラグ」l3flgフィールドを”0”に設定する
                nowInfo.setL3flg(ReportConstants.STR_0);
            }
            // ・リストの最後から2番目の要素まで上記処理を繰り返す
            // （最後の要素には次の要素が存在しないため処理対象外）
        }

        infoInDto.setDataList(infoList);
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(infoList);
        infoInDto.setList(dataSource);
        return cks22OutList;
    }

    /**
     * 3. 「2.2」で取得した計画書（２）情報.総件数 > 0 件の場合、印鑑欄設定情報を取得する。
     * 
     * @param model        入力データ
     * @param infoInDto    帳票用データ詳細
     * @param cks22OutList 「2.2」で取得した計画書（２）情報
     */
    private void acquChouInfo(KyotakuServiceReportParameterModel model, KyotakuServiceReportServiceInDto infoInDto,
            List<CpnTucCks22OutEntity> cks22OutList) {
        if (CollectionUtils.size(cks22OutList) > 0) {
            // 3.1.リクエストパラメータ.印鑑欄を表示するフラグ = trueとリクエストパラメータ.記入用シートを印刷するフラグ = false
            // の場合、印鑑欄設定情報の取得
            if (ReportConstants.STR_TRUE.equals(model.getInkanFlg())
                    && ReportConstants.STR_FALSE.equals(model.getEmptyFlg())) {
                // 下記の28-xx ケアプラン帳票印鑑欄情報取得のDAOを利用し、印鑑欄設定情報を取得する。
                KghCpnMstChouhyouInkanPrnByCriteriaInEntity prnInRntity = new KghCpnMstChouhyouInkanPrnByCriteriaInEntity();
                // 法人ID リクエストパラメータ.法人ID
                prnInRntity.setAnKey1(CommonDtoUtil.strValToInt(model.getHoujinId()));
                // 施設ID リクエストパラメータ.施設ID
                prnInRntity.setAnKey2(CommonDtoUtil.strValToInt(model.getShisetuId()));
                // 事業所ID リクエストパラメータ.事業所ID
                prnInRntity.setAnKey3(CommonDtoUtil.strValToInt(model.getSvJigyoId()));
                // 帳票セクション番号 "3GKU0082KP001"
                prnInRntity.setAsSec(ReportConstants.REPORT_NUMBER_3GKU0082KP001);
                // 印鑑欄設定情報取得
                List<KghCpnMstChouhyouInkanPrnOutEntity> chouOutList = cpnMstChouhyouInkanSelectMapper
                        .findKghCpnMstChouhyouInkanPrnByCriteria(prnInRntity);

                if (CollectionUtils.isNotEmpty(chouOutList)) {
                    KghCpnMstChouhyouInkanPrnOutEntity chouOutInfo = chouOutList.getFirst();
                    // ・リクエストパラメータ.記入用シートを印刷するフラグ = false の場合
                    // "印鑑欄表示区分=API定義の処理「3.1.」の印鑑欄表示区分
                    infoInDto.setInkanRyou(CommonDtoUtil.objValToString(chouOutInfo.getHyoujiKbn()));
                    // "印鑑1=API定義の処理「3.1.」の印鑑1
                    infoInDto.setChouhyouInkan1(chouOutInfo.getHanko1Knj());
                    // "印鑑2=API定義の処理「3.1.」の印鑑2
                    infoInDto.setChouhyouInkan2(chouOutInfo.getHanko2Knj());
                    // "印鑑3=API定義の処理「3.1.」の印鑑3
                    infoInDto.setChouhyouInkan3(chouOutInfo.getHanko3Knj());
                    // "印鑑4=API定義の処理「3.1.」の印鑑4
                    infoInDto.setChouhyouInkan4(chouOutInfo.getHanko4Knj());
                    // "印鑑5=API定義の処理「3.1.」の印鑑5
                    infoInDto.setChouhyouInkan5(chouOutInfo.getHanko5Knj());
                    // "印鑑6=API定義の処理「3.1.」の印鑑6
                    infoInDto.setChouhyouInkan6(chouOutInfo.getHanko6Knj());
                    // "印鑑7=API定義の処理「3.1.」の印鑑7
                    infoInDto.setChouhyouInkan7(chouOutInfo.getHanko7Knj());
                    // "印鑑8=API定義の処理「3.1.」の印鑑8
                    infoInDto.setChouhyouInkan8(chouOutInfo.getHanko8Knj());
                    // "印鑑9=API定義の処理「3.1.」の印鑑9
                    infoInDto.setChouhyouInkan9(chouOutInfo.getHanko9Knj());
                    // "印鑑10=API定義の処理「3.1.」の印鑑10
                    infoInDto.setChouhyouInkan10(chouOutInfo.getHanko10Knj());
                    // "印鑑11=API定義の処理「3.1.」の印鑑11
                    infoInDto.setChouhyouInkan11(chouOutInfo.getHanko11Knj());
                    // "印鑑12=API定義の処理「3.1.」の印鑑12
                    infoInDto.setChouhyouInkan12(chouOutInfo.getHanko12Knj());
                    // "印鑑13=API定義の処理「3.1.」の印鑑13
                    infoInDto.setChouhyouInkan13(chouOutInfo.getHanko13Knj());
                    // "印鑑14=API定義の処理「3.1.」の印鑑14
                    infoInDto.setChouhyouInkan14(chouOutInfo.getHanko14Knj());
                    // "印鑑15=API定義の処理「3.1.」の印鑑15
                    infoInDto.setChouhyouInkan15(chouOutInfo.getHanko15Knj());
                }
            }
        }
    }

    /**
     * 4. 要介護度、事業履歴より事業所名、利用者名、職員基本情報を取得する。
     * 
     * @param model        入力データ
     * @param infoInDto    帳票用データ詳細
     * @param chgCreateYmd 上記「2.1」で取得した計画書（２）ヘッダ情報.作成日
     * @param chgShokuId   「2.1.」で取得した作成者
     */
    private void acquYoukaiInfo(KyotakuServiceReportParameterModel model, KyotakuServiceReportServiceInDto infoInDto,
            String chgCreateYmd, Integer chgShokuId) {
        // 要介護度
        String chgYokaiFlg = StringUtils.EMPTY;
        // 4.1. 共通関数「要介護度取得関数」：クラス名：f_cmp_yokai_get 関数名：f_cmp_yokai_get
        // を利用し、要介護度の情報を取得する。
        String youkaiOut = kghCmpF01Logic.getCmpYokai(CommonDtoUtil.strValToInt(model.getSvJigyoId()),
                CommonDtoUtil.strValToInt(model.getUserId()),
                CommonDtoUtil.strValToInt(model.getLiJoken() == null ? StringUtils.EMPTY : model.getLiJoken()),
                CommonDtoUtil.strValToInt(model.getKs11Id()), chgCreateYmd);

        // 4.2. 個人情報保護設定の取得
        List<KojinHogoJohoSetteiOutEntity> johoOutList = comMocSysiniSelectMapper
                .findKojinHogoJohoSetteiByCriteria(new KojinHogoJohoSetteiByCriteriaInEntity());

        // 4.3. 上記4.2で取得した値が”１”の場合、下記の関数を実行する。
        if (CollectionUtils.isNotEmpty(johoOutList)
                && ReportConstants.STR_1.equals(johoOutList.getFirst().getNewParam())) {
            F3gkGetProfileInDto f3gkInEntity = new F3gkGetProfileInDto();
            // 職員ＩＤ リクエストパラメータ.職員ID
            f3gkInEntity.setShokuId(f3gkInEntity.getShokuId());
            // 法人ＩＤ 0
            f3gkInEntity.setHoujinId(ReportConstants.INT_0);
            // 施設ＩＤ 0
            f3gkInEntity.setShisetuId(ReportConstants.INT_0);
            // 事業所ＩＤ 0
            f3gkInEntity.setSvJigyoId(ReportConstants.INT_0);
            // 画面名 "PRT"
            f3gkInEntity.setKinounameKnj(ReportConstants.KINOU_NAME_PRT);
            // セクション "3GKU0082KP001"
            f3gkInEntity.setSectionKnj(ReportConstants.REPORT_NUMBER_3GKU0082KP001);
            // キー "kojinhogo_flg"
            f3gkInEntity.setKeyKnj(ReportConstants.S_KOJINHOGO_FLG);
            // 初期値 "1"
            f3gkInEntity.setAsDefault(ReportConstants.STR_1);
            // 4.3.1. 下記関数を利用して、個人情報保護フラグの取得
            String lsParam = nds3GkFunc01Logic.getF3gkProfile(f3gkInEntity);

            // 4.3.2. 上記4.3.1.で取得したパラメータが”1：正常”の場合
            if (ReportConstants.STR_1.equals(lsParam)) {
                // ①、要介護度 ＝ ””
                chgYokaiFlg = StringUtils.EMPTY;
                // ②、下記共通を利用して、取得した利用者名代替データを帳票用データ詳細.利用者名に設定
                String kojinhogoNokado = nds3GkBase02Logic
                        .getKojinhogoNokado(CommonDtoUtil.strValToInt(model.getUserId()));
                // 利用者名
                infoInDto.setRiyoushaNm(kojinhogoNokado);
            }
            // 4.3.3. 上記以外の場合
            else {
                // ①、要介護度 ＝ 上記4.1で取得した要介護度名
                chgYokaiFlg = youkaiOut;
            }
            // 要介護度 = API定義の処理「4.1.」の要介護度
            infoInDto.setYouKaiGoDo(chgYokaiFlg);
        }

        // 4.4. 共通関数「事業所名取得関数」：クラス名：f_cpn_get_jigyo_rireki_knj
        // 関数名：f_cpn_get_jigyo_rireki_knj を利用し、事業履歴より事業所名を取得する。
        JigyoRirekiInfoDto cpnFuncOutInfo = kghKrkZCpnFuncLogic
                .getJigyoRirekiKnj(CommonDtoUtil.strValToInt(model.getSvJigyoId()), chgCreateYmd);
        // 事業所名
        // ・リクエストパラメータ.事業名印刷フラグ＝”０”の場合、事業所名が空白です。
        if (ReportConstants.STR_0.equals(model.getJigyoFlg())) {
            infoInDto.setJigyoName(StringUtils.EMPTY);
        }
        // ・リクエストパラメータ.事業名印刷フラグが”０”以外、かつ、事業名略称印刷フラグ＝”２”の場合
        else if (!ReportConstants.STR_0.equals(model.getJigyoFlg())
                && ReportConstants.STR_2.equals(model.getJigyoRyakuKnjFlg())) {
            // ・事業所名 ＝API定義の処理「4.4.」の事業所正式名称。
            infoInDto.setJigyoName(cpnFuncOutInfo.getJigyoRirekiKnj());
        }
        // ・以外の場合、事業所名 ＝API定義の処理「4.4.」の事業所略称。
        else if (!ReportConstants.STR_0.equals(model.getJigyoFlg())) {
            infoInDto.setJigyoName(cpnFuncOutInfo.getJigyoRirekiRyakuKnj());
        }

        // 4.5.作成者の03-01 職員基本情報の取得
        KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity srwInEntity = new KghCpnRaiMonKentPrnPreSrw2ByCriteriaInEntity();
        // 職員ID 「2.1.」で取得した作成者
        srwInEntity.setLlTmp(CommonDtoUtil.objValToString(chgShokuId));
        // 03-01 職員基本情報取得
        List<KghCpnRaiMonKentPrnPreSrw2OutEntity> srwOutList = comMscShokuinSelectMapper
                .findKghCpnRaiMonKentPrnPreSrw2ByCriteria(srwInEntity);
        // 作成者 = API定義の処理「4.5」の職員名
        if (CollectionUtils.isNotEmpty(srwOutList)) {
            infoInDto.setShoKuId(srwOutList.getFirst().getShokuinName());
        }
    }

    /**
     * リクエストパラメータ.作成年月日印刷区分を印刷するフラグが”true”の場合、当該年月の設定
     * 
     * @param model        入力データ
     * @param infoInDto    帳票用データ詳細
     * @param chgCreateYmd
     */
    private void acquSakuYmd(KyotakuServiceReportParameterModel model, KyotakuServiceReportServiceInDto infoInDto,
            String chgCreateYmd) {
        // ５.1. 上記「2.1」で取得した計画書（２）ヘッダ情報.作成日がある場合、
        if (StringUtils.isNotEmpty(model.getSaKuSeiKubun()) && StringUtils.isNotEmpty(chgCreateYmd)) {
            // 作成年月日
            String chgSakuYmd = StringUtils.EMPTY;
            // ・リクエストパラメータ.作成年月日印刷区分＝1 の場合（※1：作成年月日印刷）
            if (ReportConstants.STR_1.equals(model.getSaKuSeiKubun())) {
                // API定義の処理「５」の和暦文字列
                chgSakuYmd = kghCmpF01Logic.getCmpS2wjEz(chgCreateYmd, ReportConstants.INT_1);
            }
            // リクエストパラメータ.作成年月日印刷区分＝2の場合（※2：日付空欄印刷）、作成年月日=" 年 月 日"
            else if (ReportConstants.STR_2.equals(model.getSaKuSeiKubun())) {
                chgSakuYmd = nds3GkFunc01Logic.blankDate(chgCreateYmd);
            }
            // リクエストパラメータ.作成年月日印刷区分＝3 の場合（※3：印刷しない）、作成年月日=""
            else if (ReportConstants.STR_3.equals(model.getSaKuSeiKubun())) {
                chgSakuYmd = StringUtils.EMPTY;
            }
            infoInDto.setSaKuSeiYmd(chgSakuYmd);
        }
    }

    /**
     * 帳票レイアウトファイル取得
     *
     * @param fwProps         FrameworkProperties
     * @param reportParameter PDF帳票パラメータ
     * @param inDto           入力データ
     * @return 帳票レイアウトファイル
     * @throws Exception
     */
    public JasperReport getJasperReport(FrameworkProperties fwProps,
            KyotakuServiceReportServiceInDto reportParameter, KyotakuServiceReportParameterModel model)
            throws Exception {

        // 帳票レイアウトファイルのリソースを取得する
        InputStream is = new FileInputStream(
                (ReportUtil.getReportJrxmlFile(fwProps, ReportConstants.JRXML_KYOTAKU_SERVICE_REPORT)));

        // コンパイル
        final JasperDesign jasperDesign = JRXmlLoader.load(is);

        // 承認欄の取得処理
        // リクエストパラメータ.データ.初期設定マスタの情報.承認欄情報が「1:帳票毎保持する」の場合
        HashMap<String, Object> shonin = shoninSetReportService.getShoninSetReport(jasperDesign, ReportConstants.INT_0,
                model.getSvJigyoId(), ReportConstants.REPORT_NUMBER_3GKU0082KP001);
        JRBeanCollectionDataSource dataSource = reportParameter.getDataSource();
        List<KyotakuServiceReportServiceInDto> kyotakuServiceReportInfoList = (List<KyotakuServiceReportServiceInDto>) dataSource
                .getData();
        if (!kyotakuServiceReportInfoList.isEmpty()) {
            kyotakuServiceReportInfoList.get(0).setSubReportPath((String) shonin.get(ReportConstants.SUBREPORTPATH));
            kyotakuServiceReportInfoList.get(0).setSubReportDataDs(
                    (JRBeanCollectionDataSource) shonin.get(ReportConstants.SUBREPORTDATADS));
        }

        return JasperCompileManager.compileReport(jasperDesign);
    }
}