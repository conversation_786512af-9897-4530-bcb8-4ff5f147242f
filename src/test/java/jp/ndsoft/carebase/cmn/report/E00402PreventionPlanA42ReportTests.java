package jp.ndsoft.carebase.cmn.report;

import java.lang.invoke.MethodHandles;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import jp.ndsoft.smh.framework.controllers.report.FixedReportController;
import jp.ndsoft.smh.framework.global.datasource.SwitchingDataSourceFilter;

@SpringBootTest
public class E00402PreventionPlanA42ReportTests {

    /** コントロール */
    @Autowired
    private FixedReportController fixedReportController;
    /** ロガー */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Autowired
    private SwitchingDataSourceFilter switchingDataSourceFilter;
    @Autowired
    private MappingJackson2HttpMessageConverter jackson2Converter;

    private MockMvc mockMvc;

    @BeforeEach
    public void before() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(this.fixedReportController).addFilters(switchingDataSourceFilter)
                .setMessageConverters(jackson2Converter)
                .build();
    }

    /**
     * テスト: E00402_介護予防サービス-支援計画表（A4横2枚）
     */
    @Test
    public void testE00402PreventionPlanA42ReportService() throws Exception {
        try {
            // Request Body JSON設定：内容は、フロントエンド側のMOCK APIの実行結果（Chrome consoleログ）から取得する
            String parameterMapJson = """
                    {
                    	"data": {
                    		"common": {},
                    		"business": {
                    			"reportId": "PreventionPlanA42Report",
                    			"keiyakushaId": "SCM00CB",
                    			"svJigyoCd": "1",
                    			"systemDate": "2025/05/30",
                    			"uploadFileServer": "false",
                    			"jigyoInfo": {
                    				"houjinId": "1",
                    				"shisetuId": "1",
                    				"svJigyoId": "1",
                    				"svJigyoCd": "1"
                    			},
                    			"inData": {
                    				"jigyoKnj": "はれやか 特定入居 通",
                    				"appYmd": "2025/06/30",
                    				"initMasterObj": {
                    					"printFont": "1",
                                        "keishoKnj": "殿殿",
                                        "keishoFlg": "1",
                                        "kikanFlg": "1",
                                        "kikanYmdFlg": "1"
                    				},
                    				"dbNoSaveData": {
                    					"selectDate": "2025/07/17",
                    					"emptyFlg": "0",
                    					"sysCode": "1",
                    					"shokuId": "1"
                    				},
                    				"printSubjectHistory":{
                    					"userId": "1",
                    					"rirekiId": "1",
                    					"createYmd": "2025/04/01"
                    				},
                                    "printSet":{
                    					"prtTitle": "帳票タイトル1",
                    					"sectionNo": "1",
                    					"prtNo": "1",
                    		            "profile": "プロファイルA",
                                        "prnDate": "2",
                    		            "prnshoku": "1",
                    					"param01": "11",
                    					"param02": "12",
                    					"param03": "1",
                    					"param04": "殿殿",
                    					"param05": "1",
                    					"param06": "1",
                    					"param07": "17",
                    					"param08": "18",
                    					"param09": "同意者内容1",
                    					"param10": "同意者内容2",
                    					"param11": "21",
                    					"param12": "1",
                    					"param13": "1",
                    					"param14": "0",
                    					"param15": "1",
                    					"param16": "26",
                    					"param17": "1",
                    					"param18": "28",
                    					"param19": "帳票タイトル",
                    					"param20": "1",
                    					"param21": "31",
                    					"param22": "32",
                    					"param23": "33",
                    					"param24": "34",
                    					"param25": "35",
                    					"param26": "36",
                    					"param27": "37",
                    					"param28": "38",
                    					"param29": "39",
                    					"param30": "40",
                    					"param31": "41",
                    					"param32": "42",
                    					"param33": "43",
                    					"param34": "44",
                    					"param35": "45",
                    					"param36": "46",
                    					"param37": "47",
                    					"param38": "48",
                    					"param39": "49",
                    					"param40": "50",
                    					"param41": "51",
                    					"param42": "52",
                    					"param43": "53",
                    					"param44": "54",
                    					"param45": "55",
                    					"param46": "56",
                    					"param47": "57",
                    					"param48": "58",
                    					"param49": "59",
                    					"param50": "60"
                    				}
                    			}
                    		}
                    	}
                    }
                    """;
            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.post("/cmn/report")
                    .contentType(MediaType.APPLICATION_JSON).content(parameterMapJson).characterEncoding("utf-8");
            final String contentAsString = this.mockMvc.perform(getRequest.with(request -> {
                // リクエストヘッダ－：認証トークン（固定）
                request.addHeader("X-AUTH-TOKEN", "token1");
                // リクエストヘッダ－：契約者ID（固定）
                // データソースをCB_DBに指定する
                request.addHeader("X-CLIENT-SSL-CN", "SCM00CB");
                // リクエストヘッダ－：XMLHttpRequest（固定）
                request.addHeader("X-Requested-With", "XMLHttpRequest");
                // ユーザーエージェント
                request.addHeader("User-Agent",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36");
                // リクエスト返却
                return request;
            }))
                    // レスポンス結果をログ出力する。
                    .andDo(MockMvcResultHandlers.print()).andExpect(MockMvcResultMatchers.status().isOk()).andReturn()
                    .getResponse().getContentAsString();
            // HTTPレスポンス（MOCK）の実行結果をログに出力する
            LOG.info("PreventionPlanA31Report  Result: {}", contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

}
