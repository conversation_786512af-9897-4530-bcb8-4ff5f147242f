package jp.ndsoft.carebase.cmn.report;

import java.lang.invoke.MethodHandles;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import jp.ndsoft.smh.framework.controllers.report.FixedReportController;
import jp.ndsoft.smh.framework.global.datasource.SwitchingDataSourceFilter;

@SpringBootTest
public class U06091HospitalizationInfoOffer2ReportTests {

    /** コントロール */
    @Autowired
    private FixedReportController fixedReportController;
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Autowired
    private SwitchingDataSourceFilter switchingDataSourceFilter;
    @Autowired
    private MappingJackson2HttpMessageConverter jackson2Converter;

    private MockMvc mockMvc;

    @BeforeEach
    public void before() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(this.fixedReportController).addFilters(switchingDataSourceFilter)
                .setMessageConverters(jackson2Converter)
                .build();
    }

    /**
     * テスト: U06091_入院時情報提供書②
     * 記入用シートを印刷するフラグ:true
     */
    @Test
    public void testU06091HospitalizationInfoOffer2ReportService() throws Exception {
        try {
            // Request Body JSON設定：内容は、フロントエンド側のMOCK APIの実行結果（Chrome consoleログ）から取得する
            String parameterMapJson = """
                    {
                    	"data": {
                    		"common": {},
                    		"business": {
                    			"reportId": "HospitalizationInfoOffer2Report",
                    			"keiyakushaId": "SCM00CB",
                                "svJigyoCd": "1",
                    			"systemDate": "2025-05-30 12:00:00",
                    			"uploadFileServer": "false",
                    			"inData": {
                    				"svJigyoKnj": "事業者名サンプル",
                                    "stKeishoFlg": "false",
                                    "stKeisho": "様",
                    				"syscd": "1",
                    				"printSet": {
                    					"shiTeiKubun": "2",
                    					"shiTeiDate": "2025-09-09"
                    				},
                    				"printOption": {
                                        "keishoFlg": "true",
                                        "keisho": "あ",
                    					"emptyFlg": "false",
                                        "userNameFlg": "false",
                                        "besshiFlg": "false"
                    				},
                    				"printSubjectHistoryList": [
                    					{
                    						"userId": "1",
                    						"userName": "利用者太郎",
                    						"sc1Id": "433",
                    						"startYmd": "2016/04/01",
                                   			"endYmd": "2017/03/31",
                                            "teikyouId": "28",
                                            "result": "",
                    						"choPrtList": [
                    							{
                    								"shokuId": "1",
                    								"sysRyaku": "3GK",
                                                    "defPrtTitle": "入院時情報提供書②",
                                                    "prtTitle": "入院時情報提供書②",
                            						"section": "セクションR",
                    								"prtNo": "1",
                    								"choPro": "プロファイルA",
                    								"sectionName": "セクション名A",
                    								"dwobject": "ObjectA",
                    								"prtOrient": "縦",
                    								"prtSize": "A4",
                    								"listTitle": "帳票リスト名A",
                    								"mTop": "10",
                    								"mBottom": "10",
                    								"mLeft": "10",
                    								"mRight": "10",
                    								"ruler": "1",
                    								"prndate": "1",
                    								"prnshoku": "1",
                    								"serialFlg": "0",
                    								"modFlg": "0",
                    								"secFlg": "1",
                    								"param01": "",
                    								"param02": "",
                    								"param03": "",
                    								"param04": "",
                    								"param05": "",
                    								"param06": "",
                    								"param07": "",
                    								"param08": "",
                    								"param09": "",
                    								"param10": "",
                    								"serialHeight": "20",
                    								"serialPagelen": "50",
                    								"hsjId": "HSJ001",
                    								"param11": "",
                    								"param12": "",
                    								"param13": "",
                    								"param14": "",
                    								"param15": "",
                    								"param16": "",
                    								"param17": "",
                    								"param18": "",
                    								"param19": "",
                    								"param20": "",
                    								"param21": "",
                    								"param22": "",
                    								"param23": "",
                    								"param24": "",
                    								"param25": "",
                    								"param26": "",
                    								"param27": "",
                    								"param28": "",
                    								"param29": "",
                    								"param30": "",
                    								"param31": "",
                    								"param32": "",
                    								"param33": "",
                    								"param34": "",
                    								"param35": "",
                    								"param36": "",
                    								"param37": "",
                    								"param38": "",
                    								"param39": "",
                    								"param40": "",
                    								"param41": "",
                    								"param42": "",
                    								"param43": "",
                    								"param44": "",
                    								"param45": "",
                    								"param46": "",
                    								"param47": "",
                    								"param48": "",
                    								"param49": "",
                    								"param50": "",
                    								"houjinId": "1",
                    								"shisetuId": "1",
                    								"svJigyoId": "1",
                    								"zoomRate": "100",
                    								"modifiedCnt": "0"
                    							}
                    						]
                    					}
                    				]
                    			},
                    			"jigyoInfo": {
                    				"houjinId": "1",
                    				"shisetuId": "1",
                    				"svJigyoId": "1",
                    				"svJigyoCd": "1"
                    				}
                    		}
                    	}
                    }
                    """;
            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.post("/cmn/report")
                    .contentType(MediaType.APPLICATION_JSON).content(parameterMapJson).characterEncoding("utf-8");
            final String contentAsString = this.mockMvc.perform(getRequest.with(request -> {
                // リクエストヘッダ－：認証トークン（固定）
                request.addHeader("X-AUTH-TOKEN", "token1");
                // リクエストヘッダ－：契約者ID（固定）
                // データソースをCB_DBに指定する
                request.addHeader("X-CLIENT-SSL-CN", "SCM00CB");
                // リクエストヘッダ－：XMLHttpRequest（固定）
                request.addHeader("X-Requested-With", "XMLHttpRequest");
                // ユーザーエージェント
                request.addHeader("User-Agent",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36");
                // リクエスト返却
                return request;
            }))
                    // レスポンス結果をログ出力する。
                    .andDo(MockMvcResultHandlers.print()).andExpect(MockMvcResultMatchers.status().isOk()).andReturn()
                    .getResponse().getContentAsString();
            // HTTPレスポンス（MOCK）の実行結果をログに出力する
            LOG.info("HospitalizationInfoOffer2Report Report Result: {}", contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

}
