package jp.ndsoft.carebase.cmn.report;

import java.lang.invoke.MethodHandles;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import jp.ndsoft.smh.framework.controllers.report.FixedReportController;
import jp.ndsoft.smh.framework.global.datasource.SwitchingDataSourceFilter;

@SpringBootTest
public class U06200CareAssessment6R3Tests {

    /** コントロール */
    @Autowired
    private FixedReportController fixedReportController;
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Autowired
    private SwitchingDataSourceFilter switchingDataSourceFilter;
    @Autowired
    private MappingJackson2HttpMessageConverter jackson2Converter;

    private MockMvc mockMvc;

    @BeforeEach
    public void before() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(this.fixedReportController).addFilters(switchingDataSourceFilter)
                .setMessageConverters(jackson2Converter)
                .build();
    }

    /**
     * テスト: U06200_ケアアセスメント(6)_R3
     * 印刷時に色をつけるフラグ:true
     */
    @Test
    public void testU06200CareAssessment6R3ReportService() throws Exception {
        try {
            // Request Body JSON設定：内容は、フロントエンド側のMOCK APIの実行結果（Chrome consoleログ）から取得する
            String parameterMapJson = """
                                   {
                                   	"data": {
                                   		"common": {},
                                   		"business": {
                                   			"reportId": "CareAssessment6R3Report",
                                   			"keiyakushaId": "SCM00CB",
                                   			"uploadFileServer": "false",
                                   			"jigyoInfo": {
                                   				"houjinId": "1",
                                   				"shisetuId": "1",
                                   				"svJigyoId": "1",
                                   				"svJigyoCd": "1"
                                   			},
                                   			"systemDate": "2025-05-30 12:00:00",
                                   			"inData": {
                                   				"gdlMatomeFlg": "1",
                                   				"svJigyoKnj": "事業者名サンプル",
                                   				"syscd": "1",
                                   				"printSet": {
                                   					"shiTeiKubun": "2",
                                   					"shiTeiDate": "2025-06-05"
                                   				},
                                   				"printOption": {
                                   					"emptyFlg": "false",
                                                    "specialnoteAppendix": "false",
                                                    "specialnoteLine": "false",
                                                    "specialnoteLineHeight": 10,
                                                    "summaryLine": "1",
                                                    "summaryLineHeight": "1",
                                                    "blankItem": 10
                                   				},
                                   				"printSubjectHistoryList": [
                                   					{
                                   						"userId": "1",
                                   						"userName": "利用者太郎",
                                   						"sc1Id": "433",
                                   						"startYmd": "2016/04/01",
                                   						"endYmd": "2017/03/31",
                                   						"createYmd": "2017/03/31",
                                   						"createUserId": "1",
                                   						"createUserName": "作成者太郎",
                                   						"ninteiFormF": "1",
                                   						"gdlId": "1",
                                   						"result": "",
                                   						"choPrtList": [
                                   							{
                                   								"shokuId": "1",
                                   								"sysRyaku": "3GK",
                                   								"defPrtTitle": "出力帳票名A",
                                   								"prtTitle": "帳票タイトルA",
                                   								"section": "セクションR",
                                   								"prtNo": "10",
                                   								"choPro": "プロファイルA",
                                   								"sectionName": "セクション名A",
                                   								"dwobject": "ObjectA",
                                   								"prtOrient": "縦",
                                   								"prtSize": "A4",
                                   								"listTitle": "帳票リスト名A",
                                   								"mtop": "10",
                                   								"mbottom": "10",
                                   								"mleft": "10",
                                   								"mright": "10",
                                   								"ruler": "1",
                                   								"prndate": "1",
                                   								"prnshoku": "1",
                                   								"serialFlg": "0",
                                   								"modFlg": "0",
                                   								"secFlg": "1",
                                   								"serialHeight": "20",
                                   								"serialPagelen": "50",
                                   								"hsjId": "HSJ001",
                                   								"handFlg": "1",
                                   								"serialType": "1",
                                   								"serialParam": "1",
                                   								"zoomRate": "100",
                                   								"zoomRate2": "0",
                                   								"param01": "",
                                   								"param02": "",
                                   								"param03": "",
                                   								"param04": "",
                                   								"param05": "",
                                   								"param06": "",
                                   								"param07": "",
                                   								"param08": "",
                                   								"param09": "",
                                   								"param10": "",
                                   								"param11": "",
                                   								"param12": "",
                                   								"param13": "",
                                   								"param14": "",
                                   								"param15": "",
                                   								"param16": "",
                                   								"param17": "",
                                   								"param18": "",
                                   								"param19": "",
                                   								"param20": "",
                                   								"param21": "",
                                   								"param22": "",
                                   								"param23": "",
                                   								"param24": "",
                                   								"param25": "",
                                   								"param26": "",
                                   								"param27": "",
                                   								"param28": "",
                                   								"param29": "",
                                   								"param30": "",
                                   								"param31": "",
                                   								"param32": "",
                                   								"param33": "",
                                   								"param34": "",
                                   								"param35": "",
                                   								"param36": "",
                                   								"param37": "",
                                   								"param38": "",
                                   								"param39": "",
                                   								"param40": "",
                                   								"param41": "",
                                   								"param42": "",
                                   								"param43": "",
                                   								"param44": "",
                                   								"param45": "",
                                   								"param46": "",
                                   								"param47": "",
                                   								"param48": "",
                                   								"param49": "",
                                   								"param50": ""
                    							}
                    						]
                    					}
                    				]
                    			}
                    		}
                    	}
                    }
                                   """;
            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.post("/cmn/report")
                    .contentType(MediaType.APPLICATION_JSON).content(parameterMapJson).characterEncoding("utf-8");
            this.mockMvc = MockMvcBuilders.standaloneSetup(this.fixedReportController)
                    .addFilters(switchingDataSourceFilter)
                    .setMessageConverters(jackson2Converter)
                    .build();
            final String contentAsString = this.mockMvc.perform(getRequest.with(request -> {
                // リクエストヘッダ－：認証トークン（固定）
                request.addHeader("X-AUTH-TOKEN", "token1");
                // リクエストヘッダ－：契約者ID（固定）
                // データソースをCB_DBに指定する
                request.addHeader("X-CLIENT-SSL-CN", "SCM00CB");
                // リクエストヘッダ－：XMLHttpRequest（固定）
                request.addHeader("X-Requested-With", "XMLHttpRequest");
                // ユーザーエージェント
                request.addHeader("User-Agent",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36");
                // リクエスト返却
                return request;
            }))
                    // レスポンス結果をログ出力する。
                    .andDo(MockMvcResultHandlers.print()).andExpect(MockMvcResultMatchers.status().isOk()).andReturn()
                    .getResponse().getContentAsString();
            // HTTPレスポンス（MOCK）の実行結果をログに出力する
            LOG.info("CareAssessment6R3Report Report Result: {}", contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

}
