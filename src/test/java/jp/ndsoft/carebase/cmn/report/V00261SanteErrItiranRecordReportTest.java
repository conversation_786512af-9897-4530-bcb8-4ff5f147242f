package jp.ndsoft.carebase.cmn.report;

import java.lang.invoke.MethodHandles;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import jp.ndsoft.smh.framework.controllers.report.FixedReportController;
import jp.ndsoft.smh.framework.global.datasource.SwitchingDataSourceFilter;

/**
 * @since 2025.09.01
 * <AUTHOR>
 * @implNote V00261_限度額超過利用者・算定エラー利用者一覧
 */
@SpringBootTest
public class V00261SanteErrItiranRecordReportTest {

    /** コントロール */
    @Autowired
    private FixedReportController fixedReportController;

    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private SwitchingDataSourceFilter switchingDataSourceFilter;

    @Autowired
    private MappingJackson2HttpMessageConverter jackson2Converter;

    private MockMvc mockMvc;

    @BeforeEach
    public void before() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(this.fixedReportController).addFilters(switchingDataSourceFilter)
                .setMessageConverters(jackson2Converter)
                .build();
    }

    /**
     * テスト: V00261_限度額超過利用者・算定エラー利用者一覧
     */
    @Test
    public void testSanteErrItiranRecordReport() throws Exception {
        try {
            // Request Body JSON設定：内容は、フロントエンド側のMOCK APIの実行結果（Chrome consoleログ）から取得する
            String parameterMapJson = """
                    {
                        "data": {
                            "common": {},
                            "business": {
                                "reportId":"SanteErrItiranRecordReport",
                                "keiyakushaId":"SCM00CB",
                                "uploadFileServer":"false",
                                "jigyoInfo":{
                                    "houjinId":"1",
                                    "shisetuId":"1",
                                    "svJigyoId":"1",
                                    "svJigyoCd":"1"
                                },
                                "systemDate":"2025-09-03 12:00:00",
                                "inData":{
                                    "baseDataList":[
                                        {
                                            "datatype":"2",
                                            "shienId":"2",
                                            "dispFlg":"true",
                                            "userId":"2",
                                            "stensuOv":"2",
                                            "stensu":"2",
                                            "ktensuOv":"2",
                                            "ktensu":"2",
                                            "errKnj":"2",
                                            "errCode":"2",
                                            "khokenKnj":"2",
                                            "hhokenNo":"2",
                                            "henkouYmd":"2025-09-05"
                                        },                                    
                                        {
                                            "datatype":"3",
                                            "shienId":"3",
                                            "dispFlg":"true",
                                            "userId":"3",
                                            "stensuOv":"3333333",
                                            "stensu":"333333",
                                            "ktensuOv":"33333333",
                                            "ktensu":"33333333",
                                            "errKnj":"3",
                                            "errCode":"3",
                                            "khokenKnj":"3",
                                            "hhokenNo":"3",
                                            "henkouYmd":"2025-09-15"
                                        }                                  
                                    ],
                                    "appYmd":"",
                                    "syscd":"",
                                    "printSet":{
                                        "shiTeiKubun":"2",
                                        "shiTeiDate":"2025-09-08"
                                    },
                                    "printOption":{
                                        "emptyFlg":"false"
                                    },
                                    "printSubjectHistoryList":[
                                        {
                                            "rirekiId":"60",
                                            "sc1Id":"1708",
                                            "userId":"1",
                                            "userName":"",
                                            "startYmd":"",
                                            "endYmd":"",
                                            "createYmd":"",
                                            "createUserId":"",
                                            "createUserName":"",
                                            "choPrtList":[
                                                {
                                                    "shokuId":"1",
                                                    "sysRyaku":"1",
                                                    "defPrtTitle":"",
                                                    "prtTitle":"[Test]限度額超過利用者・算定エラー利用者一覧",
                                                    "section":"",
                                                    "prtNo":"6",
                                                    "choPro":"",
                                                    "sectionName":"",
                                                    "dwobject":"",
                                                    "prtOrient":"",
                                                    "prtSize":"",
                                                    "listTitle":"",
                                                    "mTop":"",
                                                    "mBottom":"",
                                                    "mLeft":"",
                                                    "mRight":"",
                                                    "ruler":"",
                                                    "prndate":"",
                                                    "prnshoku":"",
                                                    "serialFlg":"",
                                                    "modFlg":"",
                                                    "secFlg":"",
                                                    "serialHeight":"",
                                                    "serialPagelen":"",
                                                    "zoomRate":"",
                                                    "param01":"",
                                                    "param02":"",
                                                    "param03":"",
                                                    "param04":"",
                                                    "param05":"",
                                                    "param06":"",
                                                    "param07":"",
                                                    "param08":"",
                                                    "param09":"",
                                                    "param10":"",
                                                    "param11":"",
                                                    "param12":"",
                                                    "param13":"",
                                                    "param14":"",
                                                    "param15":"",
                                                    "param16":"",
                                                    "param17":"",
                                                    "param18":"",
                                                    "param19":"",
                                                    "param20":"",
                                                    "param21":"",
                                                    "param22":"",
                                                    "param23":"",
                                                    "param24":"",
                                                    "param25":"",
                                                    "param26":"",
                                                    "param27":"",
                                                    "param28":"",
                                                    "param29":"",
                                                    "param30":"",
                                                    "param31":"",
                                                    "param32":"",
                                                    "param33":"",
                                                    "param34":"",
                                                    "param35":"",
                                                    "param36":"",
                                                    "param37":"",
                                                    "param38":"",
                                                    "param39":"",
                                                    "param40":"",
                                                    "param41":"",
                                                    "param42":"",
                                                    "param43":"",
                                                    "param44":"",
                                                    "param45":"",
                                                    "param46":"",
                                                    "param47":"",
                                                    "param48":"",
                                                    "param49":"",
                                                    "param50":""
                                                }
                                            ]
                                        }
                                    ]
                                }
                            }
                        }
                    }""";
            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.post("/cmn/report")
                    .contentType(MediaType.APPLICATION_JSON).content(parameterMapJson).characterEncoding("utf-8");
            final String contentAsString = this.mockMvc.perform(getRequest.with(request -> {
                // リクエストヘッダ－：認証トークン（固定）
                request.addHeader("X-AUTH-TOKEN", "token1");
                // リクエストヘッダ－：契約者ID（固定）
                // データソースをCB_DBに指定する
                request.addHeader("X-CLIENT-SSL-CN", "SCM00CB");
                // リクエストヘッダ－：XMLHttpRequest（固定）
                request.addHeader("X-Requested-With", "XMLHttpRequest");
                // ユーザーエージェント
                request.addHeader("User-Agent",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36");
                // リクエスト返却
                return request;
            }))
                    // レスポンス結果をログ出力する。
                    .andDo(MockMvcResultHandlers.print()).andExpect(MockMvcResultMatchers.status().isOk()).andReturn()
                    .getResponse().getContentAsString();
            // HTTPレスポンス（MOCK）の実行結果をログに出力する
            LOG.info("Test Result: {}", contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }
}
