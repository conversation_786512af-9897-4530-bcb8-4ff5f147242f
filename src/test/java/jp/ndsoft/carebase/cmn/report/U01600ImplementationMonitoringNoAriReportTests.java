package jp.ndsoft.carebase.cmn.report;
import java.lang.invoke.MethodHandles;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import jp.ndsoft.smh.framework.controllers.report.FixedReportController;
import jp.ndsoft.smh.framework.global.datasource.SwitchingDataSourceFilter;

/**
 * テスト: U01600_実施モニタリング表(計画実施表(番号あり)) 帳票出力
 */
@SpringBootTest
public class U01600ImplementationMonitoringNoAriReportTests {

    /** コントロール */
    @Autowired
    private FixedReportController fixedReportController;
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Autowired
    private SwitchingDataSourceFilter switchingDataSourceFilter;
    @Autowired
    private MappingJackson2HttpMessageConverter jackson2Converter;

    private MockMvc mockMvc;

    @BeforeEach
    public void before() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(this.fixedReportController).addFilters(switchingDataSourceFilter)
                .setMessageConverters(jackson2Converter)
                .build();
    }

    /**
     * テスト: U01600_実施モニタリング表(計画実施表(番号あり)) 帳票出力
     */
    @Test
    public void testAssessmentFaceSheetType2ReportService() throws Exception {
        try {
            // Request Body JSON設定：内容は、フロントエンド側のMOCK APIの実行結果（Chrome consoleログ）から取得する
            String parameterMapJson = """
                    {
                        "data": {
                            "common": {},
                            "business": {
                                "reportId": "ImplementationMonitoringNoAriReport",
                                "keiyakushaId": "SCM00CB",
                                "uploadFileServer": "false",
                                "systemDate": "2025-07-31 14:00:00",
                                "inData": {
                                    "appYmd": "",
                                    "sys3ryaku": "",
                                    "svJigyoKnj": "事業者名サンプル",
                                    "syscd": "1",
                                    "kkjBangouFlg": "",
                                    "cpnFlg": "",
                                    "cksFlg": "",
                                    "kkjTorikomi": "",
                                    "kkjTantoFlg": "",
                                    "kkjHindoFlg": "",
                                    "isSameOmitted": "",
                                    "isBlankOmitted": "",
                                    "stKeishoFlg": "",
                                    "stKeisho": "",
                                    "shoninFlg": "",
                                    "printSet": {
                                        "shiTeiKubun": "2",
                                        "shiTeiDate": "2025-07-31",
                                        "printKubun": "1",
                                        "planCreateYmd": "",
                                    },
                                    "printOption": {
                                        "titleOfHonorFlg": "",
                                        "titleOfHonor": "",
                                        "emptyFlg": "true",
                                        "approvalPrintFlg": "",
                                        "printFrameSizeFlg": "",
                                        "printFrameSize": "",
                                        "planAuthorPrintFlg": "",
                                        "monochromePrintFlg": "",
                                        "pageChangeFlg": "",
                                        "summaryPrintFlg": "",
                                        "remarksPrintFlg": "",
                                        "kaigodo": ""
                                    },
                                    "printSubjectHistoryList": [
                                        {
                                            "userId": "1",
                                            "userName": "利用者太郎",
                                            "kjisshi1Id": "",
                                            "syoriYm": "",
                                            "planCreateYmd": "",
                                            "shokuId": "",
                                            "result": "",
                                            "choPrtList": [
                                                {
                                                    "shokuId": "1",
                                                    "sysRyaku": "略称",
                                                    "defPrtTitle": "帳票タイトルA",
                                                    "prtTitle": "帳票タイトルA",
                                                    "section": "セクションA",
                                                    "prtNo": "1",
                                                    "choPro": "プロファイルA",
                                                    "sectionName": "セクション名A",
                                                    "dwobject": "ObjectA",
                                                    "prtOrient": "縦",
                                                    "prtSize": "A4",
                                                    "listTitle": "帳票リスト名A",
                                                    "mTop": "10",
                                                    "mBottom": "10",
                                                    "mLeft": "10",
                                                    "mRight": "10",
                                                    "ruler": "1",
                                                    "prndate": "1",
                                                    "prnshoku": "1",
                                                    "serialFlg": "0",
                                                    "modFlg": "0",
                                                    "secFlg": "1",
                                                    "serialHeight": "20",
                                                    "serialPagelen": "50",
                                                    "hsjId": "",
                                                    "handFlg": "",
                                                    "serialType": "",
                                                    "serialParam": "",
                                                    "zoomRate": "HSJ001",
                                                    "zoomRate2": "HSJ001",
                                                    "param01": "",
                                                    "param02": "",
                                                    "param03": "",
                                                    "param04": "",
                                                    "param05": "",
                                                    "param06": "",
                                                    "param07": "",
                                                    "param08": "",
                                                    "param09": "",
                                                    "param10": "",
                                                    "param11": "",
                                                    "param12": "",
                                                    "param13": "",
                                                    "param14": "",
                                                    "param15": "",
                                                    "param16": "",
                                                    "param17": "",
                                                    "param18": "",
                                                    "param19": "",
                                                    "param20": "",
                                                    "param21": "",
                                                    "param22": "",
                                                    "param23": "",
                                                    "param24": "",
                                                    "param25": "",
                                                    "param26": "",
                                                    "param27": "",
                                                    "param28": "",
                                                    "param29": "",
                                                    "param30": "",
                                                    "param31": "",
                                                    "param32": "",
                                                    "param33": "",
                                                    "param34": "",
                                                    "param35": "",
                                                    "param36": "",
                                                    "param37": "",
                                                    "param38": "",
                                                    "param39": "",
                                                    "param40": "",
                                                    "param41": "",
                                                    "param42": "",
                                                    "param43": "",
                                                    "param44": "",
                                                    "param45": "",
                                                    "param46": "",
                                                    "param47": "",
                                                    "param48": "",
                                                    "param49": "",
                                                    "param50": ""
                                                }
                                            ]
                                        }
                                    ]
                                },
                                "jigyoInfo": {
                                    "houjinId": "1",
                                    "shisetuId": "1",
                                    "svJigyoId": "1",
                                    "svJigyoCd": "1"
                                }
                            }
                        }
                    }
                    """;
            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.post("/cmn/report")
                    .contentType(MediaType.APPLICATION_JSON).content(parameterMapJson).characterEncoding("utf-8");
            final String contentAsString = this.mockMvc.perform(getRequest.with(request -> {
                // リクエストヘッダ－：認証トークン（固定）
                request.addHeader("X-AUTH-TOKEN", "token1");
                // リクエストヘッダ－：契約者ID（固定）
                // データソースをCB_DBに指定する
                request.addHeader("X-CLIENT-SSL-CN", "SCM00CB");
                // リクエストヘッダ－：XMLHttpRequest（固定）
                request.addHeader("X-Requested-With", "XMLHttpRequest");
                // ユーザーエージェント
                request.addHeader("User-Agent",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36");
                // リクエスト返却
                return request;
            }))
                    // レスポンス結果をログ出力する。
                    .andDo(MockMvcResultHandlers.print()).andExpect(MockMvcResultMatchers.status().isOk()).andReturn()
                    .getResponse().getContentAsString();
            // HTTPレスポンス（MOCK）の実行結果をログに出力する
            LOG.info("ImplementationMonitoringNoAriReportService Report Result: {}", contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }
}
