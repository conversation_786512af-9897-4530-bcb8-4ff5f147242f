package jp.ndsoft.carebase.cmn.controller.api;

import java.lang.invoke.MethodHandles;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import jp.ndsoft.carebase.framework.controllers.dispatch.CareBaseApiController;
import jp.ndsoft.smh.framework.global.datasource.SwitchingDataSourceFilter;

@SpringBootTest
public class GUI04513Tests {

    /** GUI04513_印刷設定のコントローラ. */
    @Autowired
    private CareBaseApiController careBaseApiController;
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Autowired
    private SwitchingDataSourceFilter switchingDataSourceFilter;
    private MockMvc mockMvc;

    @BeforeEach
    public void before() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(this.careBaseApiController).addFilters(switchingDataSourceFilter)
                .build();
    }

    /**
     * GUI04513_印刷設定
     * 
     * テスト:APINo(1263)_初期情報取得
     */
    @Test
    public void testGUI04513ControllerInitUpdate() throws Exception {
        try {
            // Request Body JSON設定：内容は、フロントエンド側のMOCK APIの実行結果（Chrome consoleログ）から取得する
            String parameterMapJson = "{\"data\":{\"common\": {},\"business\": {\"sectionName\": \"[mnu3][3GK]計画書(1)\",\"shokuId\": \"0\",\"houjinId\": \"1\",\"shisetuId\": \"1\",\"svJigyoId\": \"4\",\"gsyscd\": \"1\",\"userId\": \"1\",\"kinouKnj\": \"[mnu3][3GK]計画書(1)\",\"dataName\": \"planAllPrintSettingsInitUpdate\"}}}";
            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.post("/cmn/update")
                    .contentType(MediaType.APPLICATION_JSON).content(parameterMapJson);
            final String contentAsString = this.mockMvc.perform(getRequest.with(request -> {
                // リクエストヘッダ－：認証トークン（固定）
                request.addHeader("X-AUTH-TOKEN", "token1");
                // リクエストヘッダ－：契約者ID（固定）
                // データソースをCB_DBに指定する
                request.addHeader("X-CLIENT-SSL-CN", "SCM00CB");
                // リクエストヘッダ－：XMLHttpRequest（固定）
                request.addHeader("X-Requested-With", "XMLHttpRequest");
                // ユーザーエージェント
                request.addHeader("User-Agent",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36");
                // リクエスト返却
                return request;
            }))
                    // レスポンス結果をログ出力する。
                    .andDo(MockMvcResultHandlers.print())
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andReturn().getResponse().getContentAsString();
            // HTTPレスポンス（MOCK）の実行結果をログに出力する
            LOG.info("GUI04513 Init Update Result: {}", contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * GUI04513_印刷設定
     * 
     * テスト:APINo(1264)_印刷設定情報保存
     */
    @Test
    public void testGUI04513ControllerInfoUpdate() throws Exception {
        try {
            // Request Body JSON設定：内容は、フロントエンド側のMOCK APIの実行結果（Chrome consoleログ）から取得する
            String parameterMapJson = "{\"data\":{\"common\": {},\"business\": {  \"sectionName\": \"[mnu3][3GK]計画書(1)\",  \"gsyscd\": \"1\",  \"shokuId\": \"0\",  \"houjinId\": \"1\",  \"shisetuId\": \"1\",  \"svJigyoId\": \"4\",  \"prtList\": [    {\"index\": \"1\",\"prtId\": \"\",\"profile\": \"3GKU0081SP001\",\"defPrtTitle\": \"計画書一括印刷\",\"prtTitle\": \"計画書一括印刷\",\"sectionNo\": \"U0081S\",\"prtNo\": \"1\",\"prnDate\": \"2\",\"prnshoku\": \"0\",\"param01\": \"\",\"param02\": \"CPN081S\",\"param03\": \"1\",\"param04\": \"1\",\"param05\": \"1\",\"param06\": \"1\",\"param07\": \"1\",\"param08\": \"1\",\"param09\": \"0\",\"param10\": \"0\",\"param11\": \"111000\",\"param12\": \"000111\",\"param13\": \"1\",\"param14\": \"\",\"param15\": \"\",\"param16\": \"\",\"param17\": \"\",\"param18\": \"\",\"param19\": \"\",\"param20\": \"\",\"param21\": \"\",\"param22\": \"\",\"param23\": \"\",\"param24\": \"\",\"param25\": \"\",\"param26\": \"\",\"param27\": \"\",\"param28\": \"\",\"param29\": \"\",\"param30\": \"0\",\"param31\": \"0\",\"param32\": \"\",\"param33\": \"\",\"param34\": \"\",\"param35\": \"\",\"param36\": \"\",\"param37\": \"\",\"param38\": \"\",\"param39\": \"\",\"param40\": \"\",\"param41\": \"\",\"param42\": \"\",\"param43\": \"\",\"param44\": \"\",\"param45\": \"\",\"param46\": \"\",\"param47\": \"\",\"param48\": \"\",\"param49\": \"\",\"param50\": \"\",\"modifiedCnt\": \"22\"    }  ],  \"dataName\": \"planAllPrintSettingsInfoUpdate\"}}}";
            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.post("/cmn/update")
                    .contentType(MediaType.APPLICATION_JSON).content(parameterMapJson);
            final String contentAsString = this.mockMvc.perform(getRequest.with(request -> {
                // リクエストヘッダ－：認証トークン（固定）
                request.addHeader("X-AUTH-TOKEN", "token1");
                // リクエストヘッダ－：契約者ID（固定）
                // データソースをCB_DBに指定する
                request.addHeader("X-CLIENT-SSL-CN", "SCM00CB");
                // リクエストヘッダ－：XMLHttpRequest（固定）
                request.addHeader("X-Requested-With", "XMLHttpRequest");
                // リクエスト返却
                return request;
            }))
                    // レスポンス結果をログ出力する。
                    .andDo(MockMvcResultHandlers.print())
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andReturn().getResponse().getContentAsString();
            // HTTPレスポンス（MOCK）の実行結果をログに出力する
            LOG.info("GUI04513 Init Update Result: {}", contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * GUI04513_印刷設定
     * 
     * テスト:APINo(1265)_印刷対象一覧情報取得
     */
    @Test
    public void testGUI04513ControllerSubjectSelect() throws Exception {
        try {
            // Request Body JSON設定：内容は、フロントエンド側のMOCK APIの実行結果（Chrome consoleログ）から取得する
            String parameterMapJson = "{\"data\":{\"common\": {},\"business\": {\"userList\": [{\"userId\": \"0000000001\",\"userName\": \"ほのぼの 一太郎\"    },    {\"userId\": \"0000000002\",\"userName\": \"ほのぼの 二太郎\"    }  ],  \"svJigyoId\": \"4\",  \"kijunbiYmd\": \"2025/07/24\",  \"svYm\": \"2025/07\",  \"svJigyoCD\": \"000001\",  \"prtList\": [    {\"index\": \"1\",\"prtId\": \"\",\"profile\": \"3GKU0081SP001\",\"defPrtTitle\": \"計画書一括印刷\",\"prtTitle\": \"計画書一括印刷\",\"sectionNo\": \"U0081S\",\"prtNo\": \"1\",\"prnDate\": \"2\",\"prnshoku\": \"0\",\"param01\": \"\",\"param02\": \"CPN081S\",\"param03\": \"1\",\"param04\": \"1\",\"param05\": \"1\",\"param06\": \"1\",\"param07\": \"1\",\"param08\": \"1\",\"param09\": \"0\",\"param10\": \"0\",\"param11\": \"111000\",\"param12\": \"000111\",\"param13\": \"1\",\"param14\": \"\",\"param15\": \"\",\"param16\": \"\",\"param17\": \"\",\"param18\": \"\",\"param19\": \"\",\"param20\": \"\",\"param21\": \"\",\"param22\": \"\",\"param23\": \"\",\"param24\": \"\",\"param25\": \"\",\"param26\": \"\",\"param27\": \"\",\"param28\": \"\",\"param29\": \"\",\"param30\": \"0\",\"param31\": \"0\",\"param32\": \"\",\"param33\": \"\",\"param34\": \"\",\"param35\": \"\",\"param36\": \"\",\"param37\": \"\",\"param38\": \"\",\"param39\": \"\",\"param40\": \"\",\"param41\": \"\",\"param42\": \"\",\"param43\": \"\",\"param44\": \"\",\"param45\": \"\",\"param46\": \"\",\"param47\": \"\",\"param48\": \"\",\"param49\": \"\",\"param50\": \"\",\"modifiedCnt\": \"25\"    }  ],  \"initMasterObj\": {    \"kikanFlg\": \"1\",    \"cpnFlg\": \"1\",    \"keishoFlg\": \"1\",    \"keishoKnj\": \"1\"  },  \"shokuId\": \"0\",  \"houjinId\": \"1\",  \"shisetuId\": \"1\",  \"dataName\": \"planAllPrintSettingsSubjectSelect\"}}}";
            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.post("/cmn/select")
                    .contentType(MediaType.APPLICATION_JSON).content(parameterMapJson);
            final String contentAsString = this.mockMvc.perform(getRequest.with(request -> {
                // リクエストヘッダ－：認証トークン（固定）
                request.addHeader("X-AUTH-TOKEN", "token1");
                // リクエストヘッダ－：契約者ID（固定）
                // データソースをCB_DBに指定する
                request.addHeader("X-CLIENT-SSL-CN", "SCM00CB");
                // リクエストヘッダ－：XMLHttpRequest（固定）
                request.addHeader("X-Requested-With", "XMLHttpRequest");
                // リクエスト返却
                return request;
            }))
                    // レスポンス結果をログ出力する。
                    .andDo(MockMvcResultHandlers.print())
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andReturn().getResponse().getContentAsString();
            // HTTPレスポンス（MOCK）の実行結果をログに出力する
            LOG.info("GUI04513 Init Update Result: {}", contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }
}
