package jp.ndsoft.carebase.cmn.controller.api;

import java.lang.invoke.MethodHandles;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import jp.ndsoft.carebase.framework.controllers.dispatch.CareBaseApiController;
import jp.ndsoft.smh.framework.global.datasource.SwitchingDataSourceFilter;

@SpringBootTest
public class GUI04514Tests {

    /** GUI04514_印刷設定のコントローラ. */
    @Autowired
    private CareBaseApiController careBaseApiController;
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Autowired
    private SwitchingDataSourceFilter switchingDataSourceFilter;
    private MockMvc mockMvc;

    @BeforeEach
    public void before() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(this.careBaseApiController).addFilters(switchingDataSourceFilter)
                .build();
    }

    /**
     * GUI04514_印刷が終了しました（ケアマネ）
     * 
     * テスト:APINo(1263)_初期情報取得
     */
    @Test
    public void testGUI04514ControllerInitSelect() throws Exception {

        try {
            // Request Body JSON設定：内容は、フロントエンド側のMOCK APIの実行結果（Chrome consoleログ）から取得する
            String parameterMapJson = "{" + //
                    "  \"data\": {" + //
                    "    \"common\": {}," + //
                    "    \"business\": {" + //
                    "      \"reportId\": \"Plan1kReport\"," + //
                    "      \"keiyakushaId\": \"\"," + //
                    "      \"svJigyoCd\": \"10001\"," + //
                    "      \"uploadFileServer\": \"\"," + //
                    "      \"jigyoInfo\": {" + //
                    "        \"houjinId\": \"1\"," + //
                    "        \"shisetuId\": \"1\"," + //
                    "        \"svJigyoId\": \"1\"," + //
                    "        \"svJigyoCd\": \"10001\"" + //
                    "      }," + //
                    "      \"systemDate\": \"2025/09/08\"," + //
                    "      \"jigyoKnj\": \"\"," + //
                    "      \"appYmd\": \"2025/09/03\"," + //
                    "      \"initMasterObj\": {" + //
                    "        \"cpnFlg\": \"\"," + //
                    "        \"keishoFlg\": \"\"," + //
                    "        \"keishoKnj\": \"\"," + //
                    "        \"cksFlg\": \"\"," + //
                    "        \"cks1TantoFlg\": \"\"," + //
                    "        \"shoninFlg\": \"\"," + //
                    "        \"cks1PrtSizeFlg\": \"\"" + //
                    "      }," + //
                    "      \"printSet\": {" + //
                    "        \"prtTitle\": \"帳票タイトル\"," + //
                    "        \"sectionNo\": \"\"," + //
                    "        \"prtNo\": \"\"," + //
                    "        \"profile\": \"\"," + //
                    "        \"prnDate\": \"\"," + //
                    "        \"prnshoku\": \"\"," + //
                    "        \"param01\": \"\"," + //
                    "        \"param02\": \"\"," + //
                    "        \"param03\": \"\"," + //
                    "        \"param04\": \"\"," + //
                    "        \"param05\": \"\"," + //
                    "        \"param06\": \"\"," + //
                    "        \"param07\": \"\"," + //
                    "        \"param08\": \"\"," + //
                    "        \"param09\": \"\"," + //
                    "        \"param10\": \"\"," + //
                    "        \"param11\": \"\"," + //
                    "        \"param12\": \"\"," + //
                    "        \"param13\": \"\"," + //
                    "        \"param14\": \"\"," + //
                    "        \"param15\": \"\"," + //
                    "        \"param16\": \"\"," + //
                    "        \"param17\": \"\"," + //
                    "        \"param18\": \"\"," + //
                    "        \"param19\": \"\"," + //
                    "        \"param20\": \"\"," + //
                    "        \"param21\": \"\"," + //
                    "        \"param22\": \"\"," + //
                    "        \"param23\": \"\"," + //
                    "        \"param24\": \"\"," + //
                    "        \"param25\": \"\"," + //
                    "        \"param26\": \"\"," + //
                    "        \"param27\": \"\"," + //
                    "        \"param28\": \"\"," + //
                    "        \"param29\": \"\"," + //
                    "        \"param30\": \"\"," + //
                    "        \"param31\": \"\"," + //
                    "        \"param32\": \"\"," + //
                    "        \"param33\": \"\"," + //
                    "        \"param34\": \"\"," + //
                    "        \"param35\": \"\"," + //
                    "        \"param36\": \"\"," + //
                    "        \"param37\": \"\"," + //
                    "        \"param38\": \"\"," + //
                    "        \"param39\": \"\"," + //
                    "        \"param40\": \"\"," + //
                    "        \"param41\": \"\"," + //
                    "        \"param42\": \"\"," + //
                    "        \"param43\": \"\"," + //
                    "        \"param44\": \"\"," + //
                    "        \"param45\": \"\"," + //
                    "        \"param46\": \"\"," + //
                    "        \"param47\": \"\"," + //
                    "        \"param48\": \"\"," + //
                    "        \"param49\": \"\"," + //
                    "        \"param50\": \"\"" + //
                    "      }," + //
                    "      \"dbNoSaveData\": {" + //
                    "        \"emptyFlg\": \"1\"," + //
                    "        \"selectDate\": \"2025/09/03\"," + //
                    "        \"sysCd\": \"1\"," + //
                    "        \"shokuinId\": \"1\"," + //
                    "        \"createYmd\": \"\"" + //
                    "      }," + //
                    "      \"printSubjectHistory\": {" + //
                    "        \"userId\": \"1\"," + //
                    "        \"rirekiId\": \"1\"," + //
                    "        \"createYmd\": \"2025/09/03\"," + //
                    "        \"sc1Id\": \"\"" + //
                    "      }," + //
                    "      \"useji\": \"\"," + //
                    "      \"liJoken\": \"\"," + //
                    "      \"yymmYm\": \"\"," + //
                    "      \"userMax\": \"1\"," + //
                    "      \"gbeBunshoFlg\": \"\"," + //
                    "      \"shienId\": \"1\"," + //
                    "      \"userIdList\": []," + //
                    "      \"dataName\": \"planAllPrintReportDataSelect\"," + //
                    "      \"operationHistOfficeId\": \"1\"" + //
                    "    }" + //
                    "  }" + //
                    "}";
            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.post("/cmn/select")
                    .contentType(MediaType.APPLICATION_JSON).content(parameterMapJson);
            final String contentAsString = this.mockMvc.perform(getRequest.with(request -> {
                // リクエストヘッダ－：認証トークン（固定）
                request.addHeader("X-AUTH-TOKEN", "token1");
                // リクエストヘッダ－：契約者ID（固定）
                // データソースをCB_DBに指定する
                request.addHeader("X-CLIENT-SSL-CN", "SCM00CB");
                // リクエストヘッダ－：XMLHttpRequest（固定）
                request.addHeader("X-Requested-With", "XMLHttpRequest");
                // ユーザーエージェント
                request.addHeader("User-Agent",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36");
                // リクエスト返却
                return request;
            }))
                    // レスポンス結果をログ出力する。
                    .andDo(MockMvcResultHandlers.print())
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andReturn().getResponse().getContentAsString();
            // HTTPレスポンス（MOCK）の実行結果をログに出力する
            LOG.info("GUI04514 Init Update Result: {}", contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }
}
