package jp.ndsoft.carebase.cmn.controller.api;

import java.lang.invoke.MethodHandles;
import org.junit.Test;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import jp.ndsoft.carebase.framework.controllers.dispatch.CareBaseApiController;
import jp.ndsoft.smh.framework.global.datasource.SwitchingDataSourceFilter;

/**
 * @since 2025/05/13
 * <AUTHOR> THANH PHONG
 * @implNote GUI00827_「アセスメント総括取込み設定」画面
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class Gui00827Tests {

    /**
     * GUI00827_「アセスメント総括取込み設定」画面
     */
    @Autowired
    private CareBaseApiController careBaseApiController;
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Autowired
    private SwitchingDataSourceFilter switchingDataSourceFilter;
    private MockMvc mockMvc;

    @Before
    public void before() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(this.careBaseApiController)
                .addFilters(this.switchingDataSourceFilter)
                .build();
    }

    /**
     * GUI00827_「アセスメント総括取込み設定」画面
     */
    @Test
    public void testGui00827ControllersSelect() throws Exception {
        try {
            // Request Body JSON設定：内容は、フロントエンド側のMOCK APIの実行結果（Chrome consoleログ）から取得する
            String parameterMapJson = """
                    {
                        "data": {
                            "common": {},
                            "business": {
                                "syscd": "71101",
                                "shokuId": "1",
                                "houjinId": "1",
                                "shisetuId": "1",
                                "svJigyoId": "1",
                                "kinounameKnj": "CMN",
                                "sectionKnj": "TEST",
                                "keyKnjList": [
                                    "領域名称",
                                    "分類名称",
                                    "項目名称",
                                    "計画優先度",
                                    "囲み1",
                                    "囲み2",
                                    "囲み3",
                                    "囲み4",
                                    "囲み5",
                                    "囲み6",
                                    "領域名称チェック",
                                    "分類名称チェック",
                                    "項目名称チェック",
                                    "選択1",
                                    "選択2",
                                    "文章1",
                                    "文章2",
                                    "区切1",
                                    "区切2",
                                    "区切3",
                                    "区切4",
                                    "改行1",
                                    "改行2"
                                ],
                                "dataName": "assessmentSummaryImportSettingSelect"
                            }
                        }
                    }
                    """;

            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.post("/cmn/select")
                    .contentType(MediaType.APPLICATION_JSON).content(parameterMapJson);
            final String contentAsString = this.mockMvc.perform(getRequest.with(request -> {
                // リクエストヘッダ－：認証トークン（固定）
                request.addHeader("X-AUTH-TOKEN", "token1");
                // リクエストヘッダ－：契約者ID（固定）
                // データソースをCB_DBに指定する
                request.addHeader("X-CLIENT-SSL-CN", "SCM00CB");
                // リクエストヘッダ－：XMLHttpRequest（固定）
                request.addHeader("X-Requested-With", "XMLHttpRequest");
                // リクエスト返却
                return request;
            }))
                    // レスポンス結果をログ出力する。
                    .andDo(MockMvcResultHandlers.print())
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andReturn().getResponse().getContentAsString();
            // HTTPレスポンス（MOCK）の実行結果をログに出力する
            LOG.info("Gui00827 Select Result: {}", contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * GUI00827_「アセスメント総括取込み設定」画面
     */
    @Test
    public void testGui00827ControllersUpdate() throws Exception {
        try {
            // Request Body JSON設定：内容は、フロントエンド側のMOCK APIの実行結果（Chrome consoleログ）から取得する
            String parameterMapJson = """
                    {
                        "data": {
                            "common": {},
                            "business": {
                                "syscd": "71101",
                                "shokuId": "1",
                                "houjinId": "1",
                                "shisetuId": "1",
                                "svJigyoId": "1",
                                "kinounameKnj": "CMN",
                                "sectionKnj": "TEST",
                                "paramList": [
                                    {
                                        "keyKnj": "領域名称",
                                        "paramKnj": "1"
                                    },
                                    {
                                        "keyKnj": "分類名称",
                                        "paramKnj": "1"
                                    },
                                    {
                                        "keyKnj": "項目名称",
                                        "paramKnj": "1"
                                    },
                                    {
                                        "keyKnj": "計画優先度",
                                        "paramKnj": "1"
                                    },
                                    {
                                        "keyKnj": "囲み1",
                                        "paramKnj": "【"
                                    },
                                    {
                                        "keyKnj": "囲み2",
                                        "paramKnj": "】"
                                    },
                                    {
                                        "keyKnj": "囲み3",
                                        "paramKnj": "【"
                                    },
                                    {
                                        "keyKnj": "囲み4",
                                        "paramKnj": "】"
                                    },
                                    {
                                        "keyKnj": "囲み5",
                                        "paramKnj": "【"
                                    },
                                    {
                                        "keyKnj": "囲み6",
                                        "paramKnj": "】"
                                    },
                                    {
                                        "keyKnj": "領域名称チェック",
                                        "paramKnj": "1"
                                    },
                                    {
                                        "keyKnj": "分類名称チェック",
                                        "paramKnj": "1"
                                    },
                                    {
                                        "keyKnj": "項目名称チェック",
                                        "paramKnj": "1"
                                    },
                                    {
                                        "keyKnj": "選択1",
                                        "paramKnj": "1"
                                    },
                                    {
                                        "keyKnj": "選択2",
                                        "paramKnj": "1"
                                    },
                                    {
                                        "keyKnj": "文章1",
                                        "paramKnj": "1"
                                    },
                                    {
                                        "keyKnj": "文章2",
                                        "paramKnj": "1"
                                    },
                                    {
                                        "keyKnj": "区切1",
                                        "paramKnj": "-"
                                    },
                                    {
                                        "keyKnj": "区切2",
                                        "paramKnj": "-"
                                    },
                                    {
                                        "keyKnj": "区切3",
                                        "paramKnj": "-"
                                    },
                                    {
                                        "keyKnj": "区切4",
                                        "paramKnj": "-"
                                    },
                                    {
                                        "keyKnj": "改行1",
                                        "paramKnj": "1"
                                    },
                                    {
                                        "keyKnj": "改行2",
                                        "paramKnj": "1"
                                    }
                                ],
                                "dataName": "assessmentSummaryImportSettingUpdate"
                            }
                        }
                    }
                    """;

            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.post("/cmn/update")
                    .contentType(MediaType.APPLICATION_JSON).content(parameterMapJson)
                    .characterEncoding("utf-8");
            final String contentAsString = this.mockMvc.perform(getRequest.with(request -> {
                // リクエストヘッダ－：認証トークン（固定）
                request.addHeader("X-AUTH-TOKEN", "token1");
                // リクエストヘッダ－：契約者ID（固定）
                // データソースをCB_DBに指定する
                request.addHeader("X-CLIENT-SSL-CN", "SCM00CB");
                // リクエストヘッダ－：XMLHttpRequest（固定）
                request.addHeader("X-Requested-With", "XMLHttpRequest");
                // リクエスト返却
                return request;
            }))
                    // レスポンス結果をログ出力する。
                    .andDo(MockMvcResultHandlers.print())
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andReturn().getResponse().getContentAsString();
            // HTTPレスポンス（MOCK）の実行結果をログに出力する
            LOG.info("Gui00827 Update Result: {}", contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

}