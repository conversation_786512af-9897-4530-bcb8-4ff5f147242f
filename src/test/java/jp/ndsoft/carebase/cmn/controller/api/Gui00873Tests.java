package jp.ndsoft.carebase.cmn.controller.api;

import java.lang.invoke.MethodHandles;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import jp.ndsoft.carebase.framework.controllers.dispatch.CareBaseApiController;
import jp.ndsoft.smh.framework.global.datasource.SwitchingDataSourceFilter;

/**
 * @since 2025.06.26
 * <AUTHOR> TRA HOANG MINH
 * @implNote GUI00873_検討表複写
 */
@SpringBootTest
public class Gui00873Tests {
    /** GUI00873_検討表複写 */
    @Autowired
    private CareBaseApiController careBaseApiController;
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Autowired
    private SwitchingDataSourceFilter switchingDataSourceFilter;
    @Autowired
    private MappingJackson2HttpMessageConverter jackson2Converter;

    private MockMvc mockMvc;

    @BeforeEach
    public void before() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(this.careBaseApiController).addFilters(switchingDataSourceFilter)
                .setMessageConverters(jackson2Converter)
                .build();
    }

    /**
     * テスト: GUI00873_検討表複写
     */

    @Test
    public void testGui00873HistorySelect() throws Exception {
        try {
            // Request Body JSON設定：内容は、フロントエンド側のMOCK APIの実行結果（Chrome consoleログ）から取得する
            String parameterMapJson = """
                    {
                        "data": {
                            "common": {},
                            "business": {
                                "svJigyoIdList": [
                                    "1"
                                ],
                                "userid": "8",
                                "sc1Id": "6",
                                "dataName": "acConsiderTableDuplicateHistorySelect"
                            }
                        }
                    }
                    """;

            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.post("/cmn/select")
                    .contentType(MediaType.APPLICATION_JSON).content(parameterMapJson)
                    .characterEncoding("utf-8");
            final String contentAsString = this.mockMvc.perform(getRequest.with(request -> {
                // リクエストヘッダ－：認証トークン（固定）
                request.addHeader("X-AUTH-TOKEN", "token1");
                // リクエストヘッダ－：契約者ID（固定）
                // データソースをCB_DBに指定する
                request.addHeader("X-CLIENT-SSL-CN", "SCM00CB");
                // リクエストヘッダ－：XMLHttpRequest（固定）
                request.addHeader("X-Requested-With", "XMLHttpRequest");
                // リクエスト返却
                return request;
            }))
                    // レスポンス結果をログ出力する。
                    .andDo(MockMvcResultHandlers.print())
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andReturn().getResponse().getContentAsString();
            // HTTPレスポンス（MOCK）の実行結果をログに出力する
            LOG.info("Gui00873 Select Result: {}", contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * テスト: GUI00873_検討表複写
     */

    @Test
    public void testGui00873PlanningPeriodSelect() throws Exception {
        try {
            // Request Body JSON設定：内容は、フロントエンド側のMOCK APIの実行結果（Chrome consoleログ）から取得する
            String parameterMapJson = """
                    {
                        "data": {
                            "common": {},
                            "business": {
                                "svJigyoIdList": [
                                    "1"
                                ],
                                "userId": "8",
                                "syubetsuId": "2",
                                "shisetuId": "1",
                                "dataName": "acConsiderTableDuplicatePlanningPeriodSelect"
                            }
                        }
                    }
                    """;

            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.post("/cmn/select")
                    .contentType(MediaType.APPLICATION_JSON).content(parameterMapJson)
                    .characterEncoding("utf-8");
            final String contentAsString = this.mockMvc.perform(getRequest.with(request -> {
                // リクエストヘッダ－：認証トークン（固定）
                request.addHeader("X-AUTH-TOKEN", "token1");
                // リクエストヘッダ－：契約者ID（固定）
                // データソースをCB_DBに指定する
                request.addHeader("X-CLIENT-SSL-CN", "SCM00CB");
                // リクエストヘッダ－：XMLHttpRequest（固定）
                request.addHeader("X-Requested-With", "XMLHttpRequest");
                // リクエスト返却
                return request;
            }))
                    // レスポンス結果をログ出力する。
                    .andDo(MockMvcResultHandlers.print())
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andReturn().getResponse().getContentAsString();
            // HTTPレスポンス（MOCK）の実行結果をログに出力する
            LOG.info("Gui00873 Select Result: {}", contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }
}
