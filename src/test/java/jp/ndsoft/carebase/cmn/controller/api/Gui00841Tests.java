package jp.ndsoft.carebase.cmn.controller.api;

import java.lang.invoke.MethodHandles;
import org.junit.Test;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import jp.ndsoft.carebase.framework.controllers.dispatch.CareBaseApiController;
import jp.ndsoft.smh.framework.global.datasource.SwitchingDataSourceFilter;

/**
 * @since 2025.04.23
 * <AUTHOR> PHAM HO HAI DANG
 *         GUI00841_画面表示情報取得
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class Gui00841Tests {

    /**
     * GUI00841_画面表示情報取得
     * コントロール
     * 
     */
    @Autowired
    private CareBaseApiController careBaseApiController;
    /** ロガー. */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Autowired
    private SwitchingDataSourceFilter switchingDataSourceFilter;
    private MockMvc mockMvc;

    @Before
    public void before() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(this.careBaseApiController)
                .addFilters(this.switchingDataSourceFilter)
                .build();
    }

    /**
     * GUI00841_画面表示情報取得
     */
    @Test
    public void testGui00841ControllersSelect() throws Exception {
        try {
            // Request Body JSON設定：内容は、フロントエンド側のMOCK APIの実行結果（Chrome consoleログ）から取得する
            String parameterMapJson = """
                    {
                    	"data": {
                    		"common": {},
                    		"business": {
                    			"svJigyoId": "1",
                    			"sc1Id": "1",
                    			"userId": "1",
                    			"dataName": "historySelectAssessmentComprehensiveSelect"
                    		}
                    	}
                    }
                    """;
            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.post("/cmn/select")
                    .contentType(MediaType.APPLICATION_JSON).content(parameterMapJson);
            final String contentAsString = this.mockMvc.perform(getRequest.with(request -> {
                // リクエストヘッダ－：認証トークン（固定）
                request.addHeader("X-AUTH-TOKEN", "token1");
                // リクエストヘッダ－：契約者ID（固定）
                // データソースをCB_DBに指定する
                request.addHeader("X-CLIENT-SSL-CN", "SCM00CB");
                // リクエストヘッダ－：XMLHttpRequest（固定）
                request.addHeader("X-Requested-With", "XMLHttpRequest");
                // リクエスト返却
                return request;
            }))
                    // レスポンス結果をログ出力する。
                    .andDo(MockMvcResultHandlers.print())
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andReturn().getResponse().getContentAsString();
            // HTTPレスポンス（MOCK）の実行結果をログに出力する
            LOG.info("Gui00841 Select Result: {}", contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }
}