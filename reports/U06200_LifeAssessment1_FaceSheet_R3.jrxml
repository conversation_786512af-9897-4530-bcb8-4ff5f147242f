<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="U06200_LifeAssessment1_FaceSheet_R3" language="java" pageWidth="595" pageHeight="842" columnWidth="528" leftMargin="34" rightMargin="33" topMargin="15" bottomMargin="18" uuid="7c5860ab-dc40-4ea9-b130-f018cf0464ec">
	<field name="shiTeiDateGG" class="java.lang.String">
		<description><![CDATA[shiTeiDateGG]]></description>
	</field>
	<field name="shiTeiDateYY" class="java.lang.String">
		<description><![CDATA[shiTeiDateYY]]></description>
	</field>
	<field name="shiTeiDateMM" class="java.lang.String">
		<description><![CDATA[shiTeiDateMM]]></description>
	</field>
	<field name="shiTeiDateDD" class="java.lang.String">
		<description><![CDATA[shiTeiDateDD]]></description>
	</field>
	<field name="shiTeiKubun" class="java.lang.Integer">
		<description><![CDATA[shiTeiKubun]]></description>
	</field>
	<field name="title" class="java.lang.String">
		<description><![CDATA[title]]></description>
	</field>
	<field name="memo1Knj" class="java.lang.String">
		<description><![CDATA[memo1Knj]]></description>
	</field>
	<field name="soudanUketukeYmdGG" class="java.lang.String">
		<description><![CDATA[soudanUketukeYmdGG]]></description>
	</field>
	<field name="soudanKeitai" class="java.lang.Integer">
		<description><![CDATA[soudanKeitai]]></description>
	</field>
	<field name="keitaiSonotaKnj" class="java.lang.String">
		<description><![CDATA[keitaiSonotaKnj]]></description>
	</field>
	<field name="soudanUketukeSya" class="java.lang.String">
		<description><![CDATA[soudanUketukeSya]]></description>
	</field>
	<field name="userName" class="java.lang.String">
		<description><![CDATA[userName]]></description>
	</field>
	<field name="userSex" class="java.lang.Integer">
		<description><![CDATA[userSex]]></description>
	</field>
	<field name="ageKbn" class="java.lang.Integer">
		<description><![CDATA[ageKbn]]></description>
	</field>
	<field name="userNenRei" class="java.lang.String">
		<description><![CDATA[userNenRei]]></description>
	</field>
	<field name="userZip" class="java.lang.String">
		<description><![CDATA[userZip]]></description>
	</field>
	<field name="userAddKnj" class="java.lang.String">
		<description><![CDATA[userAddKnj]]></description>
	</field>
	<field name="userTel" class="java.lang.String">
		<description><![CDATA[userTel]]></description>
	</field>
	<field name="userKeitaiTel" class="java.lang.String">
		<description><![CDATA[userKeitaiTel]]></description>
	</field>
	<field name="knameKnj" class="java.lang.String">
		<description><![CDATA[knameKnj]]></description>
	</field>
	<field name="ksex" class="java.lang.Integer">
		<description><![CDATA[ksex]]></description>
	</field>
	<field name="knenrei" class="java.lang.String">
		<description><![CDATA[knenrei]]></description>
	</field>
	<field name="kzokugara" class="java.lang.String">
		<description><![CDATA[kzokugara]]></description>
	</field>
	<field name="kaddressKnj" class="java.lang.String">
		<description><![CDATA[kaddressKnj]]></description>
	</field>
	<field name="ktel" class="java.lang.String">
		<description><![CDATA[ktel]]></description>
	</field>
	<field name="kkeitaitel" class="java.lang.String">
		<description><![CDATA[kkeitaitel]]></description>
	</field>
	<field name="adName2Knj" class="java.lang.String">
		<description><![CDATA[adName2Knj]]></description>
	</field>
	<field name="sdSex2" class="java.lang.Integer">
		<description><![CDATA[sdSex2]]></description>
	</field>
	<field name="sdNenrei2" class="java.lang.String">
		<description><![CDATA[sdNenrei2]]></description>
	</field>
	<field name="sdZokugara" class="java.lang.String">
		<description><![CDATA[sdZokugara]]></description>
	</field>
	<field name="sodanshaAddKnj" class="java.lang.String">
		<description><![CDATA[sodanshaAddKnj]]></description>
	</field>
	<field name="sdTel2" class="java.lang.String">
		<description><![CDATA[sdTel2]]></description>
	</field>
	<field name="sdKeitaitel2" class="java.lang.String">
		<description><![CDATA[sdKeitaitel2]]></description>
	</field>
	<field name="keiro2Knj" class="java.lang.String">
		<description><![CDATA[keiro2Knj]]></description>
	</field>
	<field name="requestDateYmdGG" class="java.lang.String">
		<description><![CDATA[requestDateYmdGG]]></description>
	</field>
	<field name="soudanNaiyouKnj" class="java.lang.String">
		<description><![CDATA[soudanNaiyouKnj]]></description>
	</field>
	<field name="soudanNaiyou2Knj" class="java.lang.String">
		<description><![CDATA[soudanNaiyou2Knj]]></description>
	</field>
	<field name="seikatuKeikaKnj" class="java.lang.String">
		<description><![CDATA[seikatuKeikaKnj]]></description>
	</field>
	<field name="hutanWariai1" class="java.lang.Integer">
		<description><![CDATA[hutanWariai1]]></description>
	</field>
	<field name="hutanWariai2" class="java.lang.Integer">
		<description><![CDATA[hutanWariai2]]></description>
	</field>
	<field name="hutanWariai3" class="java.lang.Integer">
		<description><![CDATA[hutanWariai3]]></description>
	</field>
	<field name="hutanKin1" class="java.lang.Integer">
		<description><![CDATA[hutanKin1]]></description>
	</field>
	<field name="hutanKin2" class="java.lang.Integer">
		<description><![CDATA[hutanKin2]]></description>
	</field>
	<field name="hutanKin3" class="java.lang.Integer">
		<description><![CDATA[hutanKin3]]></description>
	</field>
	<field name="kkaigo5" class="java.lang.Integer">
		<description><![CDATA[kkaigo5]]></description>
	</field>
	<field name="kkaigo4" class="java.lang.Integer">
		<description><![CDATA[kkaigo4]]></description>
	</field>
	<field name="kkaigo3" class="java.lang.Integer">
		<description><![CDATA[kkaigo3]]></description>
	</field>
	<field name="kkaigo2" class="java.lang.Integer">
		<description><![CDATA[kkaigo2]]></description>
	</field>
	<field name="kkaigo1" class="java.lang.Integer">
		<description><![CDATA[kkaigo1]]></description>
	</field>
	<field name="yokaiKbn2" class="java.lang.Integer">
		<description><![CDATA[yokaiKbn2]]></description>
	</field>
	<field name="ninteiYmdGG" class="java.lang.String">
		<description><![CDATA[ninteiYmdGG]]></description>
	</field>
	<field name="yokaiKbn1" class="java.lang.Integer">
		<description><![CDATA[yokaiKbn1]]></description>
	</field>
	<field name="techoUmu1" class="java.lang.Integer">
		<description><![CDATA[techoUmu1]]></description>
	</field>
	<field name="techoUmu12" class="java.lang.Integer">
		<description><![CDATA[techoUmu12]]></description>
	</field>
	<field name="sinshouShu" class="java.lang.String">
		<description><![CDATA[sinshouShu]]></description>
	</field>
	<field name="ttokyu1" class="java.lang.String">
		<description><![CDATA[ttokyu1]]></description>
	</field>
	<field name="biko1Knj" class="java.lang.String">
		<description><![CDATA[biko1Knj]]></description>
	</field>
	<field name="get1YmdGG" class="java.lang.String">
		<description><![CDATA[get1YmdGG]]></description>
	</field>
	<field name="techoUmu2" class="java.lang.Integer">
		<description><![CDATA[techoUmu2]]></description>
	</field>
	<field name="techoUmu22" class="java.lang.Integer">
		<description><![CDATA[techoUmu22]]></description>
	</field>
	<field name="ttokyu2" class="java.lang.String">
		<description><![CDATA[ttokyu2]]></description>
	</field>
	<field name="biko2Knj" class="java.lang.String">
		<description><![CDATA[biko2Knj]]></description>
	</field>
	<field name="get2YmdGG" class="java.lang.String">
		<description><![CDATA[get2YmdGG]]></description>
	</field>
	<field name="techoUmu3" class="java.lang.Integer">
		<description><![CDATA[techoUmu3]]></description>
	</field>
	<field name="techoUmu32" class="java.lang.Integer">
		<description><![CDATA[techoUmu32]]></description>
	</field>
	<field name="ttokyu3" class="java.lang.String">
		<description><![CDATA[ttokyu3]]></description>
	</field>
	<field name="biko3Knj" class="java.lang.String">
		<description><![CDATA[biko3Knj]]></description>
	</field>
	<field name="get3YmdGG" class="java.lang.String">
		<description><![CDATA[get3YmdGG]]></description>
	</field>
	<field name="sienJukyuUmu1" class="java.lang.Integer">
		<description><![CDATA[sienJukyuUmu1]]></description>
	</field>
	<field name="sienJukyuUmu2" class="java.lang.Integer">
		<description><![CDATA[sienJukyuUmu2]]></description>
	</field>
	<field name="shogaiJukyuUmu1" class="java.lang.Integer">
		<description><![CDATA[shogaiJukyuUmu1]]></description>
	</field>
	<field name="shogaiJukyuUmu2" class="java.lang.Integer">
		<description><![CDATA[shogaiJukyuUmu2]]></description>
	</field>
	<field name="syougaiMemo" class="java.lang.String">
		<description><![CDATA[syougaiMemo]]></description>
	</field>
	<field name="sienJukyuKubunKnj" class="java.lang.String">
		<description><![CDATA[sienJukyuKubunKnj]]></description>
	</field>
	<field name="adl1" class="java.lang.Integer">
		<description><![CDATA[adl1]]></description>
	</field>
	<field name="adl1TantoKnj" class="java.lang.String">
		<description><![CDATA[adl1TantoKnj]]></description>
	</field>
	<field name="adl1HospKnj" class="java.lang.String">
		<description><![CDATA[adl1HospKnj]]></description>
	</field>
	<field name="adl1HospKnjFont" class="java.lang.String">
		<description><![CDATA[adl1HospKnjFont]]></description>
	</field>
	<field name="adl2" class="java.lang.Integer">
		<description><![CDATA[adl2]]></description>
	</field>
	<field name="adl2TantoKnj" class="java.lang.String">
		<description><![CDATA[adl2TantoKnj]]></description>
	</field>
	<field name="adl2HospKnj" class="java.lang.String">
		<description><![CDATA[adl2HospKnj]]></description>
	</field>
	<field name="adl2HospKnjFont" class="java.lang.String">
		<description><![CDATA[adl2HospKnjFont]]></description>
	</field>
	<field name="adl1DateYmdGG" class="java.lang.String">
		<description><![CDATA[adl1DateYmdGG]]></description>
	</field>
	<field name="adl2DateYmdGG" class="java.lang.String">
		<description><![CDATA[adl2DateYmdGG]]></description>
	</field>
	<field name="memo2Knj" class="java.lang.String">
		<description><![CDATA[memo2Knj]]></description>
	</field>
	<field name="jisshiShokaiYmdGG" class="java.lang.String">
		<description><![CDATA[jisshiShokaiYmdGG]]></description>
	</field>
	<field name="asJisshiiDateYmdGG" class="java.lang.String">
		<description><![CDATA[asJisshiiDateYmdGG]]></description>
	</field>
	<field name="bunsyoKanriNo" class="java.lang.String">
		<description><![CDATA[bunsyoKanriNo]]></description>
	</field>
	<field name="soudanUketukeYmdYY" class="java.lang.String">
		<description><![CDATA[soudanUketukeYmdYY]]></description>
	</field>
	<field name="soudanUketukeYmdMM" class="java.lang.String">
		<description><![CDATA[soudanUketukeYmdMM]]></description>
	</field>
	<field name="soudanUketukeYmdDD" class="java.lang.String">
		<description><![CDATA[soudanUketukeYmdDD]]></description>
	</field>
	<field name="birthdayYmdYY" class="java.lang.String">
		<description><![CDATA[birthdayYmdYY]]></description>
	</field>
	<field name="birthdayYmdMM" class="java.lang.String">
		<description><![CDATA[birthdayYmdMM]]></description>
	</field>
	<field name="birthdayYmdDD" class="java.lang.String">
		<description><![CDATA[birthdayYmdDD]]></description>
	</field>
	<field name="requestDateYmdYY" class="java.lang.String">
		<description><![CDATA[requestDateYmdYY]]></description>
	</field>
	<field name="requestDateYmdMM" class="java.lang.String">
		<description><![CDATA[requestDateYmdMM]]></description>
	</field>
	<field name="requestDateYmdDD" class="java.lang.String">
		<description><![CDATA[requestDateYmdDD]]></description>
	</field>
	<field name="ninteiYmdYY" class="java.lang.String">
		<description><![CDATA[ninteiYmdYY]]></description>
	</field>
	<field name="ninteiYmdMM" class="java.lang.String">
		<description><![CDATA[ninteiYmdMM]]></description>
	</field>
	<field name="ninteiYmdDD" class="java.lang.String">
		<description><![CDATA[ninteiYmdDD]]></description>
	</field>
	<field name="get1YmdYY" class="java.lang.String">
		<description><![CDATA[get1YmdYY]]></description>
	</field>
	<field name="get1YmdMM" class="java.lang.String">
		<description><![CDATA[get1YmdMM]]></description>
	</field>
	<field name="get2YmdYY" class="java.lang.String">
		<description><![CDATA[get2YmdYY]]></description>
	</field>
	<field name="get2YmdMM" class="java.lang.String">
		<description><![CDATA[get2YmdMM]]></description>
	</field>
	<field name="get3YmdYY" class="java.lang.String">
		<description><![CDATA[get3YmdYY]]></description>
	</field>
	<field name="adl1DateYmdYY" class="java.lang.String">
		<description><![CDATA[adl1DateYmdYY]]></description>
	</field>
	<field name="adl1DateYmdMM" class="java.lang.String">
		<description><![CDATA[adl1DateYmdMM]]></description>
	</field>
	<field name="adl1DateYmdDD" class="java.lang.String">
		<description><![CDATA[adl1DateYmdDD]]></description>
	</field>
	<field name="adl2DateYmdYY" class="java.lang.String">
		<description><![CDATA[adl2DateYmdYY]]></description>
	</field>
	<field name="adl2DateYmdMM" class="java.lang.String">
		<description><![CDATA[adl2DateYmdMM]]></description>
	</field>
	<field name="adl2DateYmdDD" class="java.lang.String">
		<description><![CDATA[adl2DateYmdDD]]></description>
	</field>
	<field name="jisshiShokaiYmdYY" class="java.lang.String">
		<description><![CDATA[jisshiShokaiYmdYY]]></description>
	</field>
	<field name="jisshiShokaiYmdMM" class="java.lang.String">
		<description><![CDATA[jisshiShokaiYmdMM]]></description>
	</field>
	<field name="jisshiShokaiYmdDD" class="java.lang.String">
		<description><![CDATA[jisshiShokaiYmdDD]]></description>
	</field>
	<field name="asJisshiiDateYmdYY" class="java.lang.String">
		<description><![CDATA[asJisshiiDateYmdYY]]></description>
	</field>
	<field name="asJisshiiDateYmdMM" class="java.lang.String">
		<description><![CDATA[asJisshiiDateYmdMM]]></description>
	</field>
	<field name="asJisshiiDateYmdDD" class="java.lang.String">
		<description><![CDATA[asJisshiiDateYmdDD]]></description>
	</field>
	<field name="emptyFlg" class="java.lang.Boolean">
		<description><![CDATA[emptyFlg]]></description>
	</field>
	<field name="get3YmdMM" class="java.lang.String">
		<description><![CDATA[get3YmdMM]]></description>
	</field>
	<pageHeader height="39" splitType="Stretch">
		<element kind="textField" uuid="5575ed64-549e-4b85-b1d1-220901f644d3" x="427" y="0" width="100" height="10" markup="html" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
			<printWhenExpression><![CDATA[$F{shiTeiKubun}==1?false:true]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDateGG}+
"<font style='font-size:9pt'>"+$F{shiTeiDateYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{shiTeiDateMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{shiTeiDateDD}+"</font>"+
"日"]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<element kind="staticText" uuid="809a2643-794d-4dc2-8bc7-dc7630c5cc71" x="0" y="0" width="67" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<text><![CDATA[利用者氏名：]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="ec80f53f-60b9-473b-a785-9c970aa2a509" x="67" y="0" width="336" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$F{userName}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<element kind="staticText" uuid="77a662d7-7e21-424b-9aab-6a5ffd1aa543" mode="Opaque" x="0" y="16" width="23" height="23" forecolor="#FFFFFF" backcolor="#000000" fontName="IPAexGothic" fontSize="22.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
			<text><![CDATA[１]]></text>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="textField" uuid="f9ddbc80-b723-4864-9e56-5dd74b6326cd" x="30" y="24" width="152" height="15" fontName="IPAexGothic" fontSize="15.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$F{title}]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="textField" uuid="69a1e6ed-0708-4558-a59e-6ce0901a5294" x="242" y="16" width="233" height="21" fontName="IPAexGothic" fontSize="9.0" vTextAlign="Top">
			<expression><![CDATA[$F{memo1Knj}]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="750" splitType="Stretch">
			<element kind="frame" uuid="2d27e71d-e6d7-4c2a-8632-dbc3d272b963" x="0" y="0" width="528" height="24">
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<element kind="staticText" uuid="4a16bc52-2d85-48ad-b556-474941f0f305" x="107" y="10" width="36" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[相談受付]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<box>
						<rightPen lineWidth="0.0"/>
					</box>
				</element>
				<element kind="frame" uuid="1620d0c6-66c9-48d6-af45-94ae90300093" x="147" y="6" width="180" height="18">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="staticText" uuid="fa054d6e-497a-4051-93c1-b787c69b909b" x="2" y="4" width="120" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[訪問・電話・来所・その他（]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="textField" uuid="57d13950-ef35-48af-aa86-c138dacf366a" x="121" y="4" width="48" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" vTextAlign="Middle">
						<printWhenExpression><![CDATA[$F{soudanKeitai} >= 4]]></printWhenExpression>
						<expression><![CDATA[$F{keitaiSonotaKnj} ]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="8e3a5a2c-eb1a-446e-97dc-e19653f59cc9" x="170" y="4" width="10" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[）]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="f0728a66-924c-4c9f-8959-4b77fea71fa9" mode="Transparent" x="1" y="2" width="22" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{soudanKeitai} == 1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="82a1cd29-38cd-4ca1-aa9f-6036216ec2ea" mode="Transparent" x="27" y="2" width="22" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{soudanKeitai} == 2]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="61025eaa-40db-40ab-85fc-c3a7061d86e9" mode="Transparent" x="81" y="2" width="33" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{soudanKeitai} >= 4]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="b960c237-1a54-425a-9c6e-12cab4e2c2a1" mode="Transparent" x="54" y="2" width="22" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{soudanKeitai} == 3]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<box>
						<leftPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="staticText" uuid="208d6d0c-bbbb-4c4a-af53-517f409fea26" x="329" y="10" width="65" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[初回相談受付者]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<box>
						<rightPen lineWidth="0.0"/>
					</box>
				</element>
				<element kind="textField" uuid="214e3fb2-a0a9-44ef-825c-bd4f8066531d" x="394" y="10" width="134" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
					<expression><![CDATA[$F{soudanUketukeSya}]]></expression>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="textField" uuid="d2265696-1671-4ce1-82d3-9e5f1c2d14f5" x="25" y="10" width="82" height="10" markup="html" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Left" vTextAlign="Middle">
					<printWhenExpression><![CDATA[!$F{emptyFlg}&&!$F{soudanUketukeYmdGG}.equals("")]]></printWhenExpression>
					<expression><![CDATA[ $F{soudanUketukeYmdGG}+
"<font style='font-size:9pt'>"+$F{soudanUketukeYmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{soudanUketukeYmdMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{soudanUketukeYmdDD}+"</font>"+
"日"]]></expression>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
				<box bottomPadding="0">
					<bottomPen lineWidth="1.0"/>
				</box>
			</element>
			<element kind="frame" uuid="8abd0e23-f1ec-4245-96a6-b0e8acc1a28c" x="0" y="26" width="528" height="216">
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="frame" uuid="dd9ed1ed-fdb5-49ce-aeb4-ae6c32be1f49" x="0" y="0" width="64" height="216">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="staticText" uuid="4471e676-17c6-48ef-9178-81f70e83a9cc" mode="Opaque" x="0" y="0" width="64" height="30" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[本人氏名]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="b420646a-6346-44df-a832-4fbe1dc1ac62" mode="Opaque" x="0" y="30" width="64" height="39" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[住　　所]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="f0862bde-3d24-4f1f-bb7c-db465478b3c9" mode="Opaque" x="0" y="69" width="64" height="48" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[緊急連絡先]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="360e8e9c-ce45-4469-984f-669a52ff7bf2" mode="Opaque" x="0" y="117" width="64" height="48" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[相　談　者]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="a9052f23-0343-4151-bcf1-78f9077e4459" mode="Opaque" x="0" y="165" width="64" height="27" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[相談経路
(紹介者)]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="ef40cf21-b7dd-48ca-829d-f4dab3ccc81b" mode="Opaque" x="0" y="192" width="64" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="8.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[居宅ｻｰﾋﾞｽ計画
作成依頼の届出]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
				</element>
				<element kind="frame" uuid="e37bac8e-697f-4291-a4a3-ed799edc3ef3" x="64" y="0" width="464" height="30">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="textField" uuid="e75ec32c-88eb-494e-a709-790e9f225a16" x="0" y="0" width="189" height="30" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<expression><![CDATA[$F{userName}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<element kind="staticText" uuid="f58487dd-5a08-450c-b949-3ada412daabe" x="194" y="0" width="31" height="30" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[男・女]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="1890de7d-cc54-4786-a039-6a010ebd6e78" mode="Opaque" x="234" y="0" width="28" height="30" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[年齢]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="3e5e4d91-497a-474e-8199-b1191f542a27" x="268" y="0" width="12" height="30" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[Ｍ
Ｔ
Ｓ]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="160eb91b-b7b6-43be-8a45-5a73e9ff6480" x="365" y="0" width="30" height="30" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[生れ（]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="textField" uuid="8385097e-1851-4cec-8993-e509e5d9c3f7" x="395" y="0" width="23" height="30" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
						<expression><![CDATA[$F{userNenRei}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<element kind="staticText" uuid="1e8de8c4-fd96-4ac0-a777-099186c9a077" x="420" y="0" width="24" height="30" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[歳 ）]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="ellipse" uuid="9f83d55a-**************-6b9e86be4ad2" mode="Transparent" x="192" y="7" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{userSex} == 1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="2c2f2171-a7a5-477c-8ce5-dc2ce676b75e" mode="Transparent" x="211" y="7" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{userSex} == 0]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="2d441f3e-91d9-4214-a84c-bb06196df6c2" mode="Transparent" x="269" y="1" width="10" height="10" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{ageKbn} == 1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="a8191f51-be58-4e9d-a9ca-f12aa59a8d67" mode="Transparent" x="269" y="10" width="10" height="10" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{ageKbn} == 2]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="ab90bea1-7461-4163-aa91-2493dc14d6da" mode="Transparent" x="269" y="19" width="10" height="10" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{ageKbn} == 3]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="textField" uuid="6e3f5a7d-49a1-4f32-aac1-5ac09b0c7ed1" x="296" y="0" width="65" height="30" markup="html" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<printWhenExpression><![CDATA[!$F{emptyFlg}]]></printWhenExpression>
						<expression><![CDATA["<font style='font-size:9pt'>"+$F{birthdayYmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{birthdayYmdMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{birthdayYmdDD}+"</font>"+
"日"]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<box leftPadding="6">
						<bottomPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="d7f368a2-477d-4cdb-88dd-6cd7dc8d0a4b" x="64" y="30" width="464" height="39">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="staticText" uuid="5018bb7c-ea0a-4f11-ab9b-d648a2ea61f6" x="16" y="4" width="12" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[〒]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="textField" uuid="ed1bfcc7-6a49-489c-9600-************" x="32" y="4" width="145" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<expression><![CDATA[$F{userZip}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<element kind="textField" uuid="b223c3f5-6a45-4b06-81a9-527c088e44c6" x="32" y="16" width="273" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<expression><![CDATA[$F{userAddKnj}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<element kind="staticText" uuid="6d2d2e50-2835-4143-a455-0618708ba425" x="316" y="16" width="12" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[℡]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="textField" uuid="a409f50b-0334-4329-a666-34b8cd4dee65" x="330" y="16" width="86" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<expression><![CDATA[$F{userTel}]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="staticText" uuid="eda9cba8-8b8f-45d8-afef-47cc2fc5cb2e" x="306" y="28" width="22" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[携帯]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="textField" uuid="4e0094da-b82c-47c9-aa67-346554b01f1d" x="330" y="28" width="86" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<expression><![CDATA[$F{userKeitaiTel}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<box bottomPadding="0">
						<bottomPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="9152eec4-7cb9-4f51-9663-cc18e74dd0ab" x="64" y="69" width="464" height="48">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="frame" uuid="ddd9f684-a1ff-48fe-8698-354b2823dd80" x="0" y="0" width="464" height="19">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="staticText" uuid="5ac21505-1ad3-43f7-86f3-e70ef4934275" x="0" y="0" width="22" height="19" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[氏名]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="ae6383cd-c43e-40bd-8ed1-0e129f748ee2" x="26" y="0" width="93" height="19" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<expression><![CDATA[$F{knameKnj}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="staticText" uuid="e78faa88-0daf-4c91-a05f-ba13d6d4ec44" x="150" y="0" width="31" height="19" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[男・女]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="c8f031d2-1627-433e-bd5a-95c33ddd00bc" x="193" y="0" width="30" height="19" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[年齢（]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="6d2981ca-4dd5-4e28-806e-b850abfff048" x="223" y="0" width="23" height="19" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
							<expression><![CDATA[$F{knenrei}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="staticText" uuid="01ce36b5-260f-4627-879b-e65e9e7536d3" x="249" y="0" width="24" height="19" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[歳 ）]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="2c853e6e-fc92-47d0-b2ff-3fa5eb488f71" x="276" y="0" width="65" height="19" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[本人との続柄（]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="7f3d285e-b6a3-4438-8de0-61f6946e25d3" x="341" y="-1" width="83" height="20" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<expression><![CDATA[$F{kzokugara}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="staticText" uuid="7224db30-10f1-4e14-a58e-7d9b8a8f205c" x="425" y="0" width="10" height="19" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[）]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="dde1522d-58a5-452b-9f1c-5d0a5f6b63ea" mode="Transparent" x="149" y="2" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{ksex} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="8c4c405a-efde-44fb-a7d4-c918afc57c04" mode="Transparent" x="167" y="2" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{ksex} == 0]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box leftPadding="6">
							<bottomPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="4f4578fc-9ef4-4c97-83f6-e747cab931f4" x="0" y="19" width="464" height="29">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="staticText" uuid="f28ff4e9-1546-421c-b7a6-76bc970767ba" x="0" y="0" width="22" height="15" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[住所]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="fe99704c-32d5-48c5-8ca5-1d2c9b51c1d9" x="26" y="0" width="273" height="15" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<expression><![CDATA[$F{kaddressKnj}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="staticText" uuid="3f93afa5-1a45-42e4-8372-9c882fcda278" x="310" y="6" width="12" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[℡]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="5fef1793-94fc-400e-8150-81af7e9a4e23" x="324" y="6" width="86" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<expression><![CDATA[$F{ktel}]]></expression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="staticText" uuid="37e2ab01-c284-417f-9cf8-652c78e7728e" x="300" y="18" width="22" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[携帯]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="07563fe1-**************-0789ceb8ca13" x="324" y="18" width="86" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<expression><![CDATA[$F{kkeitaitel}]]></expression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<box leftPadding="6"/>
					</element>
					<box leftPadding="0" bottomPadding="0">
						<bottomPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="652b101a-340d-47ec-8172-18a271d575dc" x="64" y="117" width="464" height="48">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="frame" uuid="bee38610-f9bd-4cf1-8512-0c2be8daf4b9" x="0" y="0" width="464" height="19">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="staticText" uuid="37b27c45-e793-43f2-886b-f06eccc056ed" x="0" y="0" width="22" height="19" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[氏名]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="31182f92-4efe-4abf-8f2d-77a1df1a1ec4" x="26" y="0" width="93" height="19" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<expression><![CDATA[$F{adName2Knj}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="staticText" uuid="15cf47ef-f3d2-4521-b368-1d2f6a96b6b7" x="150" y="0" width="31" height="19" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[男・女]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="cdc311b8-df0a-445d-8d51-70b6fd1b5887" x="193" y="0" width="30" height="19" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[年齢（]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="f0f38047-661c-4b1b-a971-e54630510b39" x="223" y="0" width="23" height="19" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
							<expression><![CDATA[$F{sdNenrei2}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="staticText" uuid="8797377a-5039-4b51-9760-ef668743abf7" x="249" y="0" width="24" height="19" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[歳 ）]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="c7c481cd-8c63-42c9-804c-f3a2a38ddad1" x="276" y="0" width="65" height="19" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[本人との続柄（]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="c3b96ea8-91e0-484b-95d0-d23d9dd3fd3d" x="341" y="-1" width="83" height="20" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<expression><![CDATA[$F{sdZokugara}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="staticText" uuid="5dfb5731-6632-41e8-98c7-09d4102589c1" x="425" y="0" width="10" height="19" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[）]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="adcce87e-22d2-456a-a1aa-64cb380f305f" mode="Transparent" x="149" y="2" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{sdSex2} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="6c1392b4-c955-45eb-b996-0f292b7bd892" mode="Transparent" x="167" y="2" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{sdSex2} == 0]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box leftPadding="6">
							<bottomPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="f950b634-4e4f-4d0a-b227-842510d73112" x="0" y="19" width="464" height="29">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="staticText" uuid="adbe9a79-0860-46e1-bd02-434ecdbd2bd6" x="0" y="0" width="22" height="15" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[住所]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="751225b4-92af-45d4-b522-a0f182d7d413" x="26" y="0" width="273" height="15" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<expression><![CDATA[$F{sodanshaAddKnj}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="staticText" uuid="23a89320-b29e-44de-81b1-7b7a32ccc4e7" x="310" y="6" width="12" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[℡]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="4b414ad8-41e6-42e0-af92-5a1f75329c0c" x="324" y="6" width="86" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<expression><![CDATA[$F{sdTel2}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="staticText" uuid="b5ba660d-de6d-442d-88ab-71437ba4d165" x="300" y="18" width="22" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[携帯]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="1cbfa7b2-407c-4835-a8a7-9a620f9a51fd" x="324" y="18" width="86" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<expression><![CDATA[$F{sdKeitaitel2}]]></expression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<box leftPadding="6"/>
					</element>
					<box leftPadding="0" bottomPadding="0">
						<bottomPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="c4fca131-60af-45b3-8b70-84c89a49fe1a" x="64" y="165" width="464" height="27">
					<element kind="textField" uuid="7a1722f9-5853-48a0-a6a4-9aef663b4e6f" x="0" y="0" width="425" height="27" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<expression><![CDATA[$F{keiro2Knj}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<box leftPadding="6">
						<bottomPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="0c56dc3f-bb94-49a1-a714-6decd726d0c8" x="64" y="192" width="464" height="24">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="staticText" uuid="b0502b46-f564-405a-ba21-4005defbac43" x="0" y="0" width="48" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[届出年月日]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="textField" uuid="971989a6-1e7e-43b8-82e9-88b17998b71f" x="57" y="0" width="96" height="24" markup="html" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Left" vTextAlign="Middle">
						<printWhenExpression><![CDATA[!$F{emptyFlg}&&!$F{requestDateYmdGG}.equals("")]]></printWhenExpression>
						<expression><![CDATA[$F{requestDateYmdGG}+
"<font style='font-size:9pt'>"+$F{requestDateYmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{requestDateYmdMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{requestDateYmdDD}+"</font>"+
"日"]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<box leftPadding="6"/>
				</element>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="9bec19ff-33dd-48f7-b955-6f680bb7f66e" x="0" y="244" width="55" height="15" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[■相談内容]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="staticText" uuid="00d3f072-392f-4688-8943-75e9066305ec" x="55" y="247" width="208" height="11" fontName="IPAexGothic" fontSize="8.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[(主訴/本人・家族の希望・困っていることや不安、思い）]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="frame" uuid="d367280f-d615-47e1-bd01-c6ca492ff7cd" x="0" y="259" width="243" height="220">
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="staticText" uuid="3babc33f-cd39-4986-85d2-58cbe4cefce1" x="0" y="2" width="242" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[（本人）]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<box leftPadding="6">
						<rightPen lineWidth="0.0"/>
					</box>
				</element>
				<element kind="textField" uuid="400b4c70-3cda-4d96-8d55-e813f7a8b212" x="0" y="12" width="242" height="121" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Top">
					<expression><![CDATA[$F{soudanNaiyouKnj}]]></expression>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<box topPadding="2" leftPadding="1"/>
				</element>
				<element kind="staticText" uuid="6a5c971e-e870-4f0f-96fc-edc7e72b1004" x="0" y="134" width="242" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[（介護者・家族）]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<box leftPadding="6">
						<rightPen lineWidth="0.0"/>
					</box>
				</element>
				<element kind="textField" uuid="77c08b06-58e0-438f-a4de-4470a0b8075d" x="1" y="143" width="242" height="76" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Top">
					<expression><![CDATA[$F{soudanNaiyou2Knj}]]></expression>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<box topPadding="2" leftPadding="1"/>
				</element>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="bd74c07d-7d31-47a7-9890-2f7beeaa45f1" x="283" y="244" width="116" height="15" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[■これまでの生活の経過]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="frame" uuid="c66804ba-71d4-406a-8d97-dd1fe2bb9d7d" x="284" y="259" width="243" height="220">
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<element kind="textField" uuid="d7742f0c-6bdf-428f-bc85-9060235be8c6" x="0" y="0" width="242" height="220" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Top">
					<expression><![CDATA[$F{seikatuKeikaKnj}]]></expression>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<box topPadding="2"/>
				</element>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="frame" uuid="43805e9c-2536-4191-b437-70b6414f2a08" x="0" y="482" width="527" height="192">
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="frame" uuid="0ee2ff04-9a9b-41d3-98e9-fe027eafcd60" x="0" y="0" width="69" height="192">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="staticText" uuid="3e106345-d519-4f3a-9bb9-f3c7c4b85dab" mode="Opaque" x="0" y="0" width="69" height="30" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[介護保険]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="8d96ef6f-fc18-4545-8688-4a99cd8365a3" mode="Opaque" x="0" y="30" width="69" height="20" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[高額介護
サービス費該当]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="e07c672c-006c-4a56-8e34-1f69c277d86c" mode="Opaque" x="0" y="50" width="69" height="42" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[要介護認定]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="62b09a57-6472-4a0d-9b90-640fd7da7b89" mode="Opaque" x="0" y="92" width="69" height="26" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[身体障害者手帳]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="aaa43a32-f851-4e0f-8115-61cb33700492" mode="Opaque" x="0" y="118" width="69" height="26" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[療育手帳]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="f1e27f12-dcd0-40f9-810b-5769942be951" mode="Opaque" x="0" y="144" width="69" height="26" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[精神障害者
保健福祉手帳]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="7177b739-66d3-4fcc-a79b-57a510deff25" mode="Opaque" x="0" y="170" width="69" height="22" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="8.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[障害福祉ｻｰﾋﾞｽ
受給者証の有無]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
				</element>
				<element kind="frame" uuid="1eee6fbb-730f-4214-8f89-283c95d8da25" x="69" y="0" width="458" height="30">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="frame" uuid="2b9fc453-21de-488d-a527-e4383d5c7bd5" x="0" y="0" width="186" height="30">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<element kind="staticText" uuid="fddadacc-03ed-4042-83c2-273d179e6d76" x="0" y="0" width="66" height="30" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[利用者負担割合]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="6eb27df8-d8b9-4307-91a7-13e34660943e" x="68" y="10" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{hutanWariai1} == 1  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="73431a2c-f244-4154-9902-04b448cc49ca" x="82" y="0" width="17" height="30" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[1割]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="ff36ddd7-e6e8-43f1-963b-1091a884a2a2" x="104" y="10" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{hutanWariai2} == 2  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="63421702-e42d-4d79-af09-9d034e2e1d67" x="118" y="0" width="17" height="30" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[2割]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="04463bcf-fe22-4320-9e19-782a92fb9519" x="140" y="10" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{hutanWariai3} == 3  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="0def58a3-a95d-4644-997e-f3ed7cf20747" x="154" y="0" width="17" height="30" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[3割]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="38e30805-4f29-4271-a486-74e1589ee5da" mode="Opaque" x="186" y="0" width="69" height="30" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="8.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[後期高齢者医療
保険(75歳以上)]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="ede56ce7-75e2-4ee8-b8b8-fbfe04bf7938" x="255" y="0" width="194" height="30">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="staticText" uuid="9335d164-a7f7-4cf3-a4a6-38f1109a3a95" x="0" y="0" width="52" height="12" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[一部負担金]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="3629c912-2b9b-40bf-9ac4-123d06730768" x="0" y="16" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{hutanKin1} == 1  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="43141476-8e58-4f3c-b43c-d09e4ef8accf" x="14" y="15" width="38" height="12" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[1割負担]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="54f6a974-863d-4ca1-9310-19e666a87018" x="132" y="16" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{hutanKin3} == 2  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="c79ca0f4-10fa-449f-a6e4-4bd0258195fe" x="146" y="15" width="38" height="12" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[3割負担]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="36b23786-bd03-47aa-b19a-12818d205db6" x="66" y="16" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{hutanKin2} == 3  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="d7efa83c-703c-4990-bd47-4ca70b45522e" x="80" y="15" width="38" height="12" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[2割負担]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<box leftPadding="6"/>
					</element>
					<box leftPadding="6">
						<bottomPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="7c5e2137-9780-4050-b4e6-b5c6b25f5070" x="69" y="30" width="458" height="20">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="staticText" uuid="ea2c212b-837c-4e0a-980e-9660adccffa0" x="0" y="0" width="52" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[利用者負担]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="f85c5d84-ac3e-46db-8704-a201b0e83ed8" x="66" y="0" width="15" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[（]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="textField" uuid="3826beaf-be61-4254-93cc-4616048bb6ef" x="81" y="5" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<expression><![CDATA[$F{kkaigo5} == 5  ? "✓"  : ""]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="53d0474f-3ce9-443a-895c-f2ecccb14927" x="95" y="0" width="37" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[第5段階]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="textField" uuid="dd80f97b-fc78-4a3f-9fa4-f1367c97013d" x="199" y="5" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<expression><![CDATA[$F{kkaigo3} == 2  ? "✓"  : ""]]></expression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="b0ceb59b-a9a1-4fb1-b8b3-c4ecb6e6014b" x="213" y="0" width="37" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[第3段階]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="textField" uuid="351b78aa-abd3-47c3-a7d6-37fe611c3765" x="258" y="5" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<expression><![CDATA[$F{kkaigo2} == 4  ? "✓"  : ""]]></expression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="b7980c35-28ea-4796-bb50-8e1b7f811d89" x="272" y="0" width="37" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[第2段階]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="textField" uuid="e20467d5-f084-4890-9fe3-d28fcc8fe208" x="317" y="5" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<expression><![CDATA[$F{kkaigo1} == 3  ? "✓"  : ""]]></expression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="0bf4dcbf-8b68-4081-ab38-2382ae2af6b0" x="331" y="0" width="38" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[第1段階]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="8b681299-dacf-4d70-92e0-06571d2f6a36" x="372" y="0" width="10" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[）]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="textField" uuid="490e80c2-bb5d-48f3-96f1-d5ed29532034" x="140" y="5" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<expression><![CDATA[$F{kkaigo4} == 1  ? "✓"  : ""]]></expression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="e7c15d99-6b16-4d6c-ba71-c323eee2b0b0" x="154" y="0" width="37" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[第4段階]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<box leftPadding="6">
						<bottomPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="d01b8ea6-90bc-4d81-a228-a55269fe281a" x="69" y="50" width="458" height="21">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<element kind="staticText" uuid="0c658add-4be5-46fb-b43f-16ab1611e017" x="6" y="0" width="16" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[済]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="499765de-4505-457f-a615-e37c483ef615" x="89" y="0" width="8" height="20" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[・]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="aa426bb5-4f22-4640-b41b-a9ea49319999" x="98" y="0" width="42" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[要支援 １]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="ec887f23-54db-42ee-91f7-495f7fe8de0e" x="323" y="0" width="33" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[認定日]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<leftPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="ellipse" uuid="bbd7a054-7f77-479e-b837-e8774ac5c778" mode="Transparent" x="3" y="3" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn2} !=0]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="8dc99619-d73e-466a-a1e9-735a445f364b" mode="Transparent" x="60" y="3" width="29" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn2} ==1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="432ed438-8521-4f4c-9149-a0d6eec31704" mode="Transparent" x="125" y="3" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn2} ==11]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="cd06d60a-40fe-4cd6-815b-e337f4ef30bf" mode="Transparent" x="148" y="3" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn2} ==12]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="7d6e60c5-a212-4374-8429-5c56beb20099" mode="Transparent" x="201" y="3" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn2} ==3]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="2683d91b-b5f5-4611-8c2c-fcdcb174d671" mode="Transparent" x="224" y="3" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn2} ==4]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="72d6343f-7fe0-4e59-bc76-78725d0fc255" mode="Transparent" x="247" y="3" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn2} ==5]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="b5d5ec44-7cfe-4db4-82df-3e142788e695" mode="Transparent" x="270" y="3" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn2} ==6]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="4b503861-4f61-441d-903e-1a9ec80aec74" mode="Transparent" x="293" y="3" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn2} ==7]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="a059fb06-65b6-4351-86ce-fbdc9a9bbc92" mode="Transparent" x="97" y="3" width="29" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn2} ==2]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="staticText" uuid="4bfa5235-6682-439a-8a39-5b18a6dd3852" x="60" y="0" width="30" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[非該当]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="463872d0-ed50-4342-b90b-47fae3c0d6e5" x="50" y="0" width="10" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[→ ]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="2317abcc-7ea4-4bd9-b8e4-bfbf80caf7e0" x="139" y="0" width="8" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[・]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="0f314a3d-73d6-4c34-be7c-e761875b295c" x="174" y="0" width="42" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[要介護 １]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="c90ab2f6-8490-41de-b8d7-84bbf58825cc" x="148" y="0" width="14" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[２]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="89bb252c-752c-413d-ae09-a61dc407faf8" x="215" y="0" width="8" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[・]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="c8b3ef1e-ae57-42a0-8cc0-a2397d19a0d1" x="238" y="0" width="8" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[・]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="a034f488-6628-4fa4-b5a6-6ea18386a174" x="261" y="0" width="8" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[・]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="42377b76-5b58-4364-af45-e3a353629528" x="285" y="0" width="8" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[・]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="4389fdff-95d0-46fc-855c-de83dff68f8e" x="224" y="0" width="14" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[２]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="025c3caa-f577-4193-a714-0a2f08342c15" x="247" y="0" width="14" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[３]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="2e5fe8b5-bf15-45f9-abaf-82d5a2b5ab95" x="270" y="0" width="14" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[４]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="aa04293b-64d0-43d8-a45e-0ac07afe7414" x="293" y="0" width="14" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[５]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="textField" uuid="6a595169-e741-4979-b59e-aadc01ef5764" x="356" y="0" width="102" height="20" markup="html" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<printWhenExpression><![CDATA[!$F{emptyFlg}&&!$F{ninteiYmdGG}.equals("")]]></printWhenExpression>
						<expression><![CDATA[  $F{ninteiYmdGG}+
"<font style='font-size:9pt'>"+$F{ninteiYmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{ninteiYmdMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{ninteiYmdDD}+"</font>"+
"日"]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</element>
					<box>
						<bottomPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="22c8f6a5-3f90-417c-a16b-416123f7a3e3" x="69" y="71" width="458" height="21">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<element kind="staticText" uuid="2ffc7fff-f10e-4b98-a192-ed4d9a8c3888" x="6" y="0" width="44" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[未(見込み)]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="e90ed00d-759a-4995-8857-849faeeb13f2" x="89" y="0" width="8" height="20" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[・]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="ellipse" uuid="ed9c83e0-2215-4f33-b679-fb6c612a3e2f" mode="Transparent" x="60" y="3" width="29" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn1} ==1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="7b298a1a-b5ea-4eb5-abb7-f78e4cff389d" mode="Transparent" x="17" y="3" width="29" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn2} ==0 && $F{yokaiKbn1} > 1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="8d2fd999-e4b7-4672-8512-21338c905b96" mode="Transparent" x="97" y="3" width="29" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn1} ==2]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="8569e177-0103-45ec-8b23-c46c85498042" mode="Transparent" x="125" y="3" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn1} ==11]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="689e017d-de94-4efd-9b7a-c3fc2f86af7d" mode="Transparent" x="148" y="3" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn1} ==12]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="edd82f2b-8972-4414-8aed-e4a2a5b0c3a6" mode="Transparent" x="201" y="3" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn1} ==3]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="992fd97b-2369-4729-8a22-0cfd16f39c38" mode="Transparent" x="224" y="3" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn1} ==4]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="897afce0-f80e-4e22-836d-19b2f7c93d51" mode="Transparent" x="247" y="3" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn1} ==5]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="20d9305b-f6e8-4a22-8ec5-e70f90233598" mode="Transparent" x="270" y="3" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn1} ==6]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="6d4120a7-cbe2-4e33-8215-aeb0b7cd1496" mode="Transparent" x="293" y="3" width="15" height="15" printRepeatedValues="false" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{yokaiKbn1} ==7]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="staticText" uuid="dc794b3f-693d-4937-ba71-74ef31ee89ef" x="50" y="0" width="10" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[→ ]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="67b4c831-6e1d-4058-90ec-3c576405d28f" x="60" y="0" width="30" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[非該当]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="62b1f1bd-b8c7-4308-8d6a-dea57694a6e9" x="139" y="0" width="8" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[・]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="52aab63e-abbb-48af-86b3-33466d14a5d1" x="174" y="0" width="42" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[要介護 １]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="52e2e653-ffc8-48b2-982b-4370076f39a3" x="148" y="0" width="14" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[２]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="e4b4f240-a8f0-4911-82bc-b42388722628" x="215" y="0" width="8" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[・]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="9ecbe491-f555-4da5-ad34-3659b745b60f" x="238" y="0" width="8" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[・]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="cb2c7266-b4ed-4f61-8832-ecba5a4db762" x="261" y="0" width="8" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[・]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="bba1ede5-5882-43b5-9640-640b5a4aecb3" x="285" y="0" width="8" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[・]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="57abf58b-c5d2-47d4-83ae-d55a21bd9f5c" x="224" y="0" width="14" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[２]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="f9b367eb-afca-46fb-87ee-35024f1aa426" x="247" y="0" width="14" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[３]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="b4e0b1ca-6039-480d-a3c3-f0458046704b" x="270" y="0" width="14" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[４]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="10e4a9ae-079d-4377-8102-c2145f4d4d5f" x="293" y="0" width="14" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[５]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="d4320cef-2e47-4597-8e5b-c63d9353f3ea" x="98" y="0" width="42" height="20" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[要支援 １]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<box>
						<bottomPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="8ecdb0fe-11d2-4d0a-9736-21ea47181fe2" x="69" y="92" width="458" height="26">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<element kind="frame" uuid="7ac8537a-cbf3-455f-81c1-a421b41f4e08" x="0" y="0" width="59" height="26">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="textField" uuid="1f6e503b-1d70-4984-ae78-22558eec9973" x="0" y="8" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{techoUmu1} ==1  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="e5023d8f-83f8-4d5e-bf21-4f51d30ff6cf" x="12" y="0" width="10" height="26" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[有]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="6f1b062e-fbdf-4a13-b1ca-357af9bf53fb" x="27" y="8" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{techoUmu12} ==2  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="010ac856-6f2c-4fe8-8436-6da165a69a44" x="40" y="0" width="10" height="26" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[無]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<box leftPadding="4">
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="c116420a-5989-4568-be7f-97aee45cfbf1" x="60" y="0" width="129" height="26">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<element kind="staticText" uuid="7fae542c-2796-4ba6-97dc-7a2189cdecc5" x="0" y="0" width="34" height="26" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[等　級]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="textField" uuid="abebecd0-a23d-4580-89eb-0df51976e489" x="38" y="0" width="36" height="26" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
							<expression><![CDATA[$F{sinshouShu}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="staticText" uuid="5ee4ca51-1601-4ed7-afa0-4e39cc2a7086" x="76" y="0" width="10" height="26" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[種]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="4fc5c884-4613-4fc7-9337-4b7c65bd76c2" x="94" y="0" width="28" height="26" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{ttokyu1}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="textField" uuid="dae714a7-dc7e-48f7-9dfb-75e96c7ca480" x="190" y="0" width="153" height="26" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
						<expression><![CDATA[$F{biko1Knj}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box padding="0" topPadding="4" leftPadding="4"/>
					</element>
					<element kind="staticText" uuid="863468c7-7f65-4ede-9b19-5fdcd62a82a1" x="343" y="0" width="32" height="26" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[交付日]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<leftPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="textField" uuid="3ef45c7d-**************-376e583cf1ba" x="375" y="0" width="82" height="26" markup="html" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<printWhenExpression><![CDATA[!$F{emptyFlg}&&!$F{get1YmdGG}.equals("")]]></printWhenExpression>
						<expression><![CDATA[  $F{get1YmdGG}+
"<font style='font-size:9pt'>"+$F{get1YmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{get1YmdMM}+"</font>"+
"月"]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</element>
					<box>
						<bottomPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="7934aa2c-8ac5-4580-85a0-019979d628ea" x="69" y="118" width="458" height="26">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="frame" uuid="74378692-051f-4af6-a3e2-cab66ed34b22" x="0" y="0" width="59" height="26">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="textField" uuid="05fa268e-b90e-44fa-a207-c600d2490004" x="0" y="8" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{techoUmu2} ==1  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="6afe30b6-8a74-40f3-9024-baf2cd5fbf76" x="12" y="0" width="10" height="26" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[有]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="dc2ae541-f2b8-4c23-bf46-f5cb4d240b7b" x="27" y="8" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{techoUmu22} ==2  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="97292538-456c-4bf2-8764-6f2ce98812bb" x="40" y="0" width="10" height="26" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[無]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<box leftPadding="4">
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="34995956-81e5-4c77-a839-8421b1fa8101" x="60" y="0" width="129" height="26">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="a5de5b4b-26bf-4a5c-8991-371aec7b6a50" x="0" y="0" width="34" height="26" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[程　度]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="textField" uuid="276b06f6-b788-4b0b-a728-7bb641e184d8" x="94" y="0" width="28" height="26" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{ttokyu2}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="textField" uuid="d35fcc54-d2a2-4f8d-b9b9-d0dfa4cea613" x="190" y="0" width="153" height="26" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
						<expression><![CDATA[$F{biko2Knj}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box padding="0" topPadding="4" leftPadding="4"/>
					</element>
					<element kind="staticText" uuid="c08fb40a-9a73-4d0d-81c9-284e8d214431" x="343" y="0" width="32" height="26" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[交付日]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<leftPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="textField" uuid="7a61e5ec-33a7-4fb4-bc89-aa86b6f41586" x="375" y="0" width="82" height="26" markup="html" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<printWhenExpression><![CDATA[!$F{emptyFlg}&&!$F{get2YmdGG}.equals("")]]></printWhenExpression>
						<expression><![CDATA[   $F{get2YmdGG}+
"<font style='font-size:9pt'>"+$F{get2YmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{get2YmdMM}+"</font>"+
"月"]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</element>
					<box>
						<bottomPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="d62046dc-1ae6-4b0e-9f02-da53d2c74d4a" x="69" y="144" width="458" height="26">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="frame" uuid="c8cccfce-c505-445d-bad0-f9e66f2726be" x="0" y="0" width="59" height="26">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="textField" uuid="b7dfcc73-1f11-441d-86da-bbdbcdf8e8bc" x="0" y="8" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{techoUmu3} ==1  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="e24f986c-bc14-4286-ad65-286da85bf4ec" x="12" y="0" width="10" height="26" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[有]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="ed8320f9-a26a-4eef-b7d9-c0e98154819a" x="27" y="8" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{techoUmu32} ==2  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="90d623ef-1e4a-4fc1-b2e4-1d37957415cd" x="40" y="0" width="10" height="26" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[無]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<box leftPadding="4">
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="17eb3ff3-1316-4867-8d76-a85ba6988f99" x="60" y="0" width="129" height="26">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="9481e03a-1c7f-4721-945f-51851736b836" x="0" y="0" width="34" height="26" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[等　級]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="textField" uuid="41b61fba-c90a-4009-85d9-27abcce7acdf" x="94" y="0" width="28" height="26" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{ttokyu3}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="textField" uuid="4444d2aa-e5ea-4797-8823-9ac2a42045fb" x="190" y="0" width="153" height="26" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
						<expression><![CDATA[$F{biko3Knj}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box padding="0" topPadding="4" leftPadding="4"/>
					</element>
					<element kind="staticText" uuid="5752a63c-2bf8-40d1-bae1-299729a3a217" x="343" y="0" width="32" height="26" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[交付日]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<leftPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="textField" uuid="1fc06d0f-3c1a-49e8-a848-e6cea1213d8a" x="375" y="0" width="82" height="26" markup="html" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<printWhenExpression><![CDATA[!$F{emptyFlg}&&!$F{get3YmdGG}.equals("")]]></printWhenExpression>
						<expression><![CDATA[    $F{get3YmdGG}+
"<font style='font-size:9pt'>"+$F{get3YmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{get3YmdMM}+"</font>"+
"月"]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</element>
					<box>
						<bottomPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="e92c17df-60e2-4502-a8c9-a1d2484e7adc" x="69" y="170" width="458" height="22">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="frame" uuid="566e7d04-c790-45dc-8d8c-412e1741754b" x="0" y="0" width="59" height="22">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="textField" uuid="fdf459ec-c2a8-4e80-b49e-bda1dc266339" x="0" y="6" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{sienJukyuUmu1} ==1  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="c67d2a0e-7c20-412c-9403-4715af59223f" x="12" y="0" width="10" height="22" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[有]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="3e7ca00b-5268-4535-90c7-e555919458b6" x="27" y="6" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{sienJukyuUmu2} ==2  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="012f15f3-1e7e-4e16-bc03-49549d1a2868" x="39" y="0" width="10" height="22" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[無]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<box leftPadding="4">
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="6b4f5e14-32fe-4d54-a41b-001d5a13533f" mode="Opaque" x="60" y="0" width="69" height="22" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="8.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[自立支援医療
受給者証の有無]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box leftPadding="0">
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="bb152ee4-6e44-4ade-af23-ba132048efbd" x="130" y="0" width="59" height="22">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="textField" uuid="a6cd3311-633e-4b86-b1b6-f695a85e2c99" x="1" y="6" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{shogaiJukyuUmu1} ==1  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="bb711eaf-46c0-466f-a0bc-c736afc5af00" x="13" y="0" width="10" height="22" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[有]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="5f97595b-8be7-4c41-9822-eb6678968743" x="30" y="6" width="10" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{shogaiJukyuUmu2} ==2  ? "✓"  : ""]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="5057b159-5459-44db-926d-4461b95c40e0" x="42" y="0" width="10" height="22" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[無]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<box leftPadding="2">
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="textField" uuid="1f138064-cd8c-42ef-b006-6fb44d10fce2" x="198" y="0" width="92" height="22" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<expression><![CDATA[$F{syougaiMemo}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box padding="2"/>
					</element>
					<element kind="staticText" uuid="08b6ed01-c364-44b6-93f8-3c90a4d52509" x="291" y="0" width="15" height="22" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[（]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="8f9b2d5d-0acd-4163-97d5-28e1a843f7ac" x="428" y="0" width="10" height="22" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[）]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="textField" uuid="c5527afa-2d92-4eec-85f7-360b3a86a943" x="300" y="0" width="128" height="22" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<expression><![CDATA[$F{sienJukyuKubunKnj} ]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<box leftPadding="0">
						<bottomPen lineWidth="1.0"/>
					</box>
				</element>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="frame" uuid="9b92f163-02ae-4a63-b490-be570ac9ed99" x="0" y="677" width="527" height="56">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<element kind="staticText" uuid="d63911a7-ddd5-457e-94b1-c7dc389618a2" mode="Opaque" x="0" y="0" width="69" height="56" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
					<text><![CDATA[日常生活自立度]]></text>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box leftPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="6304267c-f39e-4d22-861a-4101b7736ac1" x="69" y="0" width="458" height="56">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="frame" uuid="cf20aed9-b251-43c7-8520-fd3ecaf61ff9" x="0" y="0" width="250" height="28">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="staticText" uuid="07548ea4-2fd7-4965-a7ec-2e00a2120b6f" x="39" y="0" width="22" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[自立]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="6cd95cfa-0bdb-45c8-9301-27622a4ce395" x="0" y="0" width="38" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[障害
高齢者]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="ellipse" uuid="e89e8c29-3963-4bcc-bebd-5b3e05f93b42" mode="Transparent" x="40" y="7" width="20" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl1} ==1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="595a370d-1d2f-434d-9682-a9f16dfb2395" mode="Transparent" x="66" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl1} ==2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="ce6f1255-2126-4377-9dcd-7a574d4b5b3a" mode="Transparent" x="90" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl1} ==3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="d1dd5da3-be56-4313-b064-61a706c9c13a" mode="Transparent" x="113" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl1} ==4]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="3003a648-684e-43cc-b63c-260491666f25" mode="Transparent" x="137" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl1} ==5]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="579990b4-edbb-4b6a-9dd7-d56c57e9ef49" mode="Transparent" x="160" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl1} ==6]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="5b63a9e0-4475-45c7-8eb4-d51210424a1b" mode="Transparent" x="184" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl1} ==7]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="3ccfc79f-c941-4288-b5d3-942f7f1ad1f0" mode="Transparent" x="208" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl1} ==8]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="21d41fb4-949d-4628-9ff1-d41c5106b911" mode="Transparent" x="231" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl1} ==9]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="staticText" uuid="dce68feb-01ec-4e81-8df5-10daabfc3c93" x="59" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="2200385f-63a8-4cd6-abd9-a28c18a8276d" x="82" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="9db49540-1a02-415f-8326-b725b825eb44" x="106" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="b19f5b8a-f330-40d2-bb7f-6394b4029cab" x="129" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="9f255851-da68-425d-aea2-a55e59e4fa71" x="153" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="fef61b24-d085-49d8-afba-20a975e6cb45" x="175" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="0e36fc7f-be62-4bad-89a9-c4616bcfae38" x="199" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="42be3ec1-919b-49e8-b959-fdc1f9f8d41d" x="223" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="66c9f866-3f4b-42f1-80c9-49839e2010f9" x="66" y="0" width="16" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ｊ1]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="880c7fec-b6c4-41da-bae2-a3f2a9c7e437" x="89" y="0" width="18" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ｊ2]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="826637ea-3be5-4979-b795-45fb2cf0cf47" x="113" y="0" width="16" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ａ1]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="17180fac-8e7d-496c-aa45-8a85c9193532" x="136" y="0" width="16" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ａ2]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="20d979ef-092c-457b-ba79-ff77a4afe011" x="159" y="0" width="16" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ｂ1]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="a90eb7b5-1dca-4ef4-975a-4e82e93d4667" x="184" y="0" width="16" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ｂ2]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="99e8df6c-5640-41e2-878a-e08eaf24ac12" x="208" y="0" width="16" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ｃ1]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="ec98263f-4c5b-4f3d-9b88-9be22bc0629b" x="231" y="0" width="16" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ｃ2]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<box>
							<bottomPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="9f2f497d-f1ad-4a3a-85de-d53d7db25537" x="0" y="28" width="250" height="28">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<element kind="staticText" uuid="a0675641-3b52-439a-a3b6-bceef57833be" x="0" y="0" width="38" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[認知症]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="ellipse" uuid="7eee9ba0-7374-43a6-835a-2f6022ba3fc9" mode="Transparent" x="40" y="7" width="20" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl2} ==1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="5052ab93-47c4-4a7f-b47f-b7c9cf344723" mode="Transparent" x="66" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl2} ==2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="1ef0b64a-b3f2-45f2-8b89-2605826bd94c" mode="Transparent" x="90" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl2} ==3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="a9fb6c0d-0295-43f9-a1dc-c2ebd3567325" mode="Transparent" x="113" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl2} ==4]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="949e336a-245b-4f5d-9670-bc4ddb18c3fa" mode="Transparent" x="136" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl2} ==5]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
						</element>
						<element kind="ellipse" uuid="0ad0e808-0ac0-430a-b1ce-162501d12fd6" mode="Transparent" x="159" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl2} ==6]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
						</element>
						<element kind="ellipse" uuid="a50b7d2e-27c2-4e89-9553-e3774da393a1" mode="Transparent" x="184" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl2} ==7]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="1111dd93-e06f-4ac9-bb11-c1e5d4565637" mode="Transparent" x="208" y="7" width="16" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{adl2} ==8]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="staticText" uuid="d23e8402-86a7-43d9-a2b6-b18d1696f282" x="39" y="0" width="22" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[自立]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="1b85ec46-db13-40b9-81d0-b2a02b040f61" x="59" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="2c3b5478-e6a2-41f9-87b7-5dc536914685" x="82" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="42fd3250-0410-46e9-8cfe-255137a8707a" x="106" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="50f1b190-e1a4-4641-a152-8328dfb3f913" x="129" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="56e2ddf2-b180-423f-b648-8d79015fa85f" x="153" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="03a5b045-5be9-48ad-99a6-c050c6a9be40" x="175" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="f4ccb455-b137-4087-a1fa-8f3dd03774d2" x="199" y="0" width="8" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[・]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="2c836234-47a6-455b-97a8-cf5bcbec766e" x="66" y="0" width="16" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ⅰ]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="cff45332-2585-4570-a0fe-3b4e1cc21b95" x="89" y="0" width="18" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ⅱa]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="14c8fb07-4d8a-458a-acd3-4037b6a30bbb" x="113" y="0" width="16" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ⅱb]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="207d915a-204e-4f8d-874d-5f90471151b6" x="136" y="0" width="16" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ⅲa]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="4eac2e52-217f-4454-934b-6d38857aae6e" x="159" y="0" width="16" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ⅲb]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="*************-4656-8134-e085a008cbc2" x="184" y="0" width="16" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ⅳ]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="f2c4b342-0699-4047-9c61-7e94f395cb07" x="208" y="0" width="16" height="28" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[Ｍ]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<box>
							<bottomPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="9e8e7661-f75b-40e8-96d8-86e74f28cea8" x="250" y="1" width="10" height="56" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[判
定
者]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<leftPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="8bb8b045-e84f-4113-8482-0106a6f52f60" x="260" y="0" width="115" height="28">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="textField" uuid="fb77435e-d066-4b21-a3f3-bcc8f2d6c410" x="0" y="0" width="104" height="15" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<expression><![CDATA[$F{adl1TantoKnj}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box padding="2"/>
						</element>
						<element kind="staticText" uuid="59e526ec-fbe6-42a3-b7b3-39f1996dca59" x="7" y="15" width="24" height="13" fontName="IPAexMincho" fontSize="8.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[機関名]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="9e68f24b-2489-4b45-a7c6-e97dde03ef40" x="107" y="15" width="8" height="13" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[）]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="20b7c5bd-6e44-4751-a436-aeb06b18b3ec" x="-3" y="15" width="8" height="13" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[（]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="6e3d4710-a946-4b06-9d89-0cf5f0b219ff" x="31" y="15" width="76" height="13" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<expression><![CDATA[$F{adl1HospKnj}]]></expression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{adl1HospKnjFont}]]></propertyExpression>
							<box padding="2"/>
						</element>
						<box>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="f2f019bb-894a-4c5f-b80a-27b5877afca2" x="260" y="28" width="115" height="28">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="textField" uuid="bb1354c5-ca2a-4ca4-8a59-4b790df9134a" x="0" y="0" width="104" height="15" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<expression><![CDATA[$F{adl2TantoKnj}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box padding="2"/>
						</element>
						<element kind="staticText" uuid="5471e31b-8f6b-4adf-a168-c6309fe1f7a3" x="7" y="15" width="24" height="13" fontName="IPAexMincho" fontSize="8.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[機関名]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="db5bd959-24a5-4477-a5f4-6da8c3c93f00" x="107" y="15" width="8" height="13" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[）]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="5b298816-b434-4db9-8f53-c7fe2691228f" x="-3" y="15" width="8" height="13" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[（]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="textField" uuid="e965d2c4-8822-4122-bd79-b026cfd27312" x="31" y="15" width="76" height="13" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<expression><![CDATA[$F{adl2HospKnj}]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{adl2HospKnjFont}]]></propertyExpression>
							<box padding="2"/>
						</element>
						<box>
							<bottomPen lineWidth="0.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="0299cc8f-eadb-4e88-a75e-404cc6ee568e" x="375" y="0" width="10" height="56" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[判
定
日]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<leftPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="textField" uuid="6310b34b-4cc2-435c-aad7-49c1d288b09f" x="385" y="28" width="73" height="28" markup="html" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<printWhenExpression><![CDATA[!$F{emptyFlg}&&!$F{adl2DateYmdGG}.equals("")]]></printWhenExpression>
						<expression><![CDATA[$F{adl2DateYmdGG}+
"<font style='font-size:9pt'>"+$F{adl2DateYmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{adl2DateYmdMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{adl2DateYmdDD}+"</font>"+
"日"]]></expression>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="textField" uuid="073af072-5553-4a26-8060-346086e6ffc9" x="385" y="0" width="73" height="28" markup="html" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<printWhenExpression><![CDATA[!$F{emptyFlg}&&!$F{adl1DateYmdGG}.equals("")]]></printWhenExpression>
						<expression><![CDATA[$F{adl1DateYmdGG}+
"<font style='font-size:9pt'>"+$F{adl1DateYmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{adl1DateYmdMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{adl1DateYmdDD}+"</font>"+
"日"]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<box>
							<bottomPen lineWidth="1.0"/>
						</box>
					</element>
					<box>
						<rightPen lineWidth="0.0"/>
					</box>
				</element>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="53707c45-70da-4058-8897-1b96fea6e1a6" x="20" y="735" width="138" height="15" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{memo2Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box padding="2"/>
			</element>
			<element kind="frame" uuid="3065bad0-191e-45b2-90b8-ee06c863355f" x="168" y="737" width="357" height="12">
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<element kind="staticText" uuid="7e77c5d4-a8f2-4619-9833-33b982819827" x="1" y="0" width="86" height="12" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[アセスメント実施日]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<box>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="staticText" uuid="4c3d984d-2b63-457d-9c42-92158ed083ee" x="90" y="0" width="28" height="12" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[(初回)]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<box>
						<rightPen lineWidth="0.0"/>
					</box>
				</element>
				<element kind="staticText" uuid="12f41a00-2dac-4aa8-878d-267fc0132e9b" x="223" y="0" width="28" height="12" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[(更新)]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<box leftPadding="3">
						<leftPen lineWidth="1.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
				</element>
				<element kind="textField" uuid="321b0c4b-27cb-46ad-a3d8-1bd1975deec6" x="118" y="0" width="100" height="12" markup="html" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
					<printWhenExpression><![CDATA[!$F{emptyFlg}&&!$F{jisshiShokaiYmdGG}.equals("")]]></printWhenExpression>
					<expression><![CDATA[$F{jisshiShokaiYmdGG}+
"<font style='font-size:9pt'>"+$F{jisshiShokaiYmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{jisshiShokaiYmdMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{jisshiShokaiYmdDD}+"</font>"+
"日"]]></expression>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<element kind="textField" uuid="7aa6f8b4-dffa-4fb1-b867-3bad2710b6d9" x="257" y="0" width="100" height="12" markup="html" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
					<printWhenExpression><![CDATA[!$F{emptyFlg}]]></printWhenExpression>
					<expression><![CDATA[$F{asJisshiiDateYmdGG}+
"<font style='font-size:9pt'>"+$F{asJisshiiDateYmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{asJisshiiDateYmdMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{asJisshiiDateYmdDD}+"</font>"+
"日"]]></expression>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="e6fcb73a-6037-475e-a140-95d86db1eca8" x="403" y="247" width="115" height="11" fontName="IPAexGothic" fontSize="8.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[(主な生活史）]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="18" splitType="Stretch">
		<element kind="staticText" uuid="a715ded4-5a2b-4c20-81df-4782dae644e0" x="265" y="6" width="248" height="12" fontName="IPAexGothic" fontSize="8.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
			<text><![CDATA[〔全社協･在宅版ケアプラン作成方法検討委員会作成〕]]></text>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<box>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="8767e5df-f30f-42a9-9a5a-370031cc1b12" key="" x="12" y="0" width="182" height="9" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<printWhenExpression><![CDATA[$F{bunsyoKanriNo}==null?false:true]]></printWhenExpression>
			<expression><![CDATA[$F{bunsyoKanriNo}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
