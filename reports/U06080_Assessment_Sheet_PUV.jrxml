<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="アセスメント表（ＰＵＶ）" language="java" pageWidth="595" pageHeight="842" columnWidth="511" leftMargin="44" rightMargin="40" topMargin="28" bottomMargin="20" uuid="49981ec9-1774-4003-8778-f31c23f446c2">
	<field name="title" class="java.lang.String">
		<description><![CDATA[title]]></description>
	</field>
	<field name="syubetuKnj" class="java.lang.String">
		<description><![CDATA[syubetuKnj]]></description>
	</field>
	<field name="bunsyoKanriNo" class="java.lang.String">
		<description><![CDATA[bunsyoKanriNo]]></description>
	</field>
	<field name="assType" class="java.lang.Integer">
		<description><![CDATA[assType]]></description>
	</field>
	<field name="shiTeiDateGG" class="java.lang.String">
		<description><![CDATA[shiTeiDateGG]]></description>
	</field>
	<field name="shiTeiKubun" class="java.lang.Integer">
		<description><![CDATA[shiTeiKubun]]></description>
	</field>
	<field name="jigyousha" class="java.lang.String">
		<description><![CDATA[jigyousha]]></description>
	</field>
	<field name="name1Knj" class="java.lang.String">
		<description><![CDATA[name1Knj]]></description>
	</field>
	<field name="name2Knj" class="java.lang.String">
		<description><![CDATA[name2Knj]]></description>
	</field>
	<field name="p1MemoKnj" class="java.lang.String">
		<description><![CDATA[p1MemoKnj]]></description>
	</field>
	<field name="p1MemoFont" class="java.lang.String">
		<description><![CDATA[p1MemoFont]]></description>
	</field>
	<field name="p1MemoColor" class="java.lang.String">
		<description><![CDATA[p1MemoColor]]></description>
	</field>
	<field name="p1A" class="java.lang.String">
		<description><![CDATA[p1A]]></description>
	</field>
	<field name="u1YmdGG" class="java.lang.String">
		<description><![CDATA[u1YmdGG]]></description>
	</field>
	<field name="u2MemoKnj" class="java.lang.String">
		<description><![CDATA[u2MemoKnj]]></description>
	</field>
	<field name="u2MemoFont" class="java.lang.String">
		<description><![CDATA[u2MemoFont]]></description>
	</field>
	<field name="u2MemoColor" class="java.lang.String">
		<description><![CDATA[u2MemoColor]]></description>
	</field>
	<field name="u2" class="java.lang.String">
		<description><![CDATA[u2]]></description>
	</field>
	<field name="u3MemoKnj" class="java.lang.String">
		<description><![CDATA[u3MemoKnj]]></description>
	</field>
	<field name="u3MemoFont" class="java.lang.String">
		<description><![CDATA[u3MemoFont]]></description>
	</field>
	<field name="u3MemoColor" class="java.lang.String">
		<description><![CDATA[u3MemoColor]]></description>
	</field>
	<field name="u3" class="java.lang.String">
		<description><![CDATA[u3]]></description>
	</field>
	<field name="shokuin1Knj" class="java.lang.String">
		<description><![CDATA[shokuin1Knj]]></description>
	</field>
	<field name="shokuin2Knj" class="java.lang.String">
		<description><![CDATA[shokuin2Knj]]></description>
	</field>
	<field name="v2YmdGG" class="java.lang.String">
		<description><![CDATA[v2YmdGG]]></description>
	</field>
	<field name="emptyFlg" class="java.lang.Boolean"/>
	<field name="shiTeiDateYY" class="java.lang.String">
		<description><![CDATA[shiTeiDateYY]]></description>
	</field>
	<field name="shiTeiDateMM" class="java.lang.String">
		<description><![CDATA[shiTeiDateMM]]></description>
	</field>
	<field name="shiTeiDateDD" class="java.lang.String">
		<description><![CDATA[shiTeiDateDD]]></description>
	</field>
	<field name="u1YmdYY" class="java.lang.String">
		<description><![CDATA[u1YmdYY]]></description>
	</field>
	<field name="u1YmdMM" class="java.lang.String">
		<description><![CDATA[u1YmdMM]]></description>
	</field>
	<field name="u1YmdDD" class="java.lang.String">
		<description><![CDATA[u1YmdDD]]></description>
	</field>
	<field name="v2YmdYY" class="java.lang.String">
		<description><![CDATA[v2YmdYY]]></description>
	</field>
	<field name="v2YmdMM" class="java.lang.String">
		<description><![CDATA[v2YmdMM]]></description>
	</field>
	<field name="v2YmdDD" class="java.lang.String">
		<description><![CDATA[v2YmdDD]]></description>
	</field>
	<pageHeader height="50" splitType="Stretch">
		<element kind="textField" uuid="316e8f73-b366-448a-b42d-dd013e0f6b69" x="0" y="0" width="82" height="20" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA[$F{syubetuKnj}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<box>
				<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="textField" uuid="99147b44-062f-4a5c-870e-3d76b348b1d8" x="0" y="0" width="185" height="9" fontName="ＭＳ Ｐゴシック" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Left" vTextAlign="Middle">
			<printWhenExpression><![CDATA[$F{bunsyoKanriNo}==null?false:true]]></printWhenExpression>
			<expression><![CDATA[$F{bunsyoKanriNo}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box>
				<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="textField" uuid="41d9d497-af9f-4a40-8c3b-5aa089f652dd" x="100" y="13" width="310" height="14" fontName="IPAexGothic" fontSize="14.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA[$F{title}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="staticText" uuid="91677b52-ab77-43f1-aa3b-6cb07325db19" x="0" y="35" width="70" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
			<printWhenExpression><![CDATA[!$F{emptyFlg}]]></printWhenExpression>
			<paragraph lineSpacingSize="1.0" firstLineIndent="0"/>
			<text><![CDATA[利用者氏名:]]></text>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
		</element>
		<element kind="textField" uuid="21e65444-2a2a-48e1-a935-96dfab14dffc" x="60" y="35" width="290" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<expression><![CDATA[$F{name1Knj}+ " " +$F{name2Knj}]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="58cc0afd-8939-45e4-a493-e93ccecb7373" x="350" y="35" width="161" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[$F{jigyousha}]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<element kind="textField" uuid="5908b1ce-2397-4ae5-b939-221e8a1c5bef" x="426" y="0" width="85" height="10" markup="html" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
			<printWhenExpression><![CDATA[($F{shiTeiKubun} != 1) && (!$F{emptyFlg})]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDateGG}+
"<font style='font-size:9pt'>"+$F{shiTeiDateYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{shiTeiDateMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{shiTeiDateDD}+"</font>"+
"日"]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="690" splitType="Stretch">
			<element kind="frame" uuid="4313d7cb-5123-46bb-97bd-8b3429873299" stretchType="ContainerHeight" x="0" y="19" width="510" height="563" removeLineWhenBlank="true">
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="staticText" uuid="68d25109-0d9c-45c7-8c4d-2f1fd2db27b3" x="0" y="0" width="70" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" bold="true" hTextAlign="Left" vTextAlign="Middle">
					<printWhenExpression><![CDATA[$F{assType}==3]]></printWhenExpression>
					<text><![CDATA[P.意思決定権]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<element kind="frame" uuid="a9693644-72d4-4c64-a5f6-bf9a42f57078" stretchType="NoStretch" x="0" y="13" width="510" height="82" removeLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{assType}== 3]]></printWhenExpression>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="frame" uuid="bc9425a9-7cd2-4bd9-97c6-3fb3c3c41360" x="0" y="0" width="126" height="82" removeLineWhenBlank="true" printInFirstWholeBand="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="staticText" uuid="d5637ddb-be3c-4c20-bf37-e0cf7e16c600" x="6" y="4" width="114" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
							<text><![CDATA[P1.意思決定権]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</element>
						<element kind="textField" uuid="f6d0c815-047b-4682-88b5-824f7fd8af5d" x="3" y="16" width="120" height="60" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Left" vTextAlign="Top">
							<expression><![CDATA[$F{p1MemoKnj}]]></expression>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{p1MemoFont}]]></propertyExpression>
							<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{p1MemoColor}]]></propertyExpression>
						</element>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="e1f176fa-08b6-43b7-bca4-6a04c17cb300" positionType="Float" x="126" y="0" width="384" height="82" removeLineWhenBlank="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="frame" uuid="8e886038-7ca7-489c-bfbc-1c37cc7c45f4" x="6" y="4" width="248" height="18">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<element kind="staticText" uuid="4d1c55d7-bf3a-49b6-ba89-4cb9f893a51a" x="4" y="5" width="120" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[0.いいえ]]></text>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="0.8"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="staticText" uuid="dccf7b3e-a684-4f1b-836e-4aa06b38ee3a" x="124" y="5" width="124" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[1.はい]]></text>
								<property name="com.jaspersoft.layout.grid.x" value="1"/>
								<property name="com.jaspersoft.layout.grid.y" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<box>
								<pen lineStyle="Solid" lineColor="#000000"/>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="a06bde6d-255c-48af-8ec9-11b77dee7cdb" x="6" y="41" width="120" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[a.法定後見人等]]></text>
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="0.8"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="staticText" uuid="8d37cad0-8c7f-41d7-8d9d-7f5a7d57c2fe" x="331" y="41" width="10" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[a]]></text>
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="0"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="textField" uuid="80922a26-856a-477c-987b-4b5fd9a2ccab" x="344" y="41" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{p1A}]]></expression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<box>
						<topPen lineWidth="2.0"/>
						<leftPen lineWidth="2.0"/>
						<bottomPen lineWidth="2.0"/>
						<rightPen lineWidth="2.0"/>
					</box>
				</element>
				<element kind="frame" uuid="6c4482c0-642c-4c95-b689-51d8895c61f6" positionType="Float" x="0" y="95" width="510" height="21" removeLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{assType}== 3]]></printWhenExpression>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="staticText" uuid="06b4f68a-7dce-45e1-8e1a-bf4a540137bb" positionType="Float" x="0" y="116" width="70" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" bold="true" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[U.利用の終了]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<element kind="frame" uuid="0624895a-9236-4474-b489-0eaef991da3c" positionType="Float" stretchType="ContainerHeight" x="0" y="129" width="510" height="329" removeLineWhenBlank="true">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="frame" uuid="f7437d99-3065-45c3-b219-615f4ff4d7cf" x="0" y="0" width="510" height="20">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="staticText" uuid="f999517d-340b-40da-993f-79748b42a13c" x="6" y="4" width="100" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
							<text><![CDATA[[注:終了時のみ記入]]]></text>
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="0.8"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="e3c4ca4a-927c-4c2d-adcd-110752450303" positionType="Float" stretchType="ContainerHeight" x="0" y="20" width="510" height="20" removeLineWhenBlank="true">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="frame" uuid="e741c7b2-b7f9-4a85-bc1e-93e272efc128" x="0" y="0" width="126" height="20">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<element kind="staticText" uuid="88f5e271-ab46-49b5-8b91-996e01aeba4e" x="6" y="4" width="56" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
								<text><![CDATA[U1.終了日]]></text>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="0.8"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="frame" uuid="627f761c-51c5-4f96-9790-73176c83cfa2" x="126" y="0" width="384" height="20">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="936bf03d-58ab-49ba-83a0-f2916ace844e" x="6" y="4" width="117" height="10" markup="html" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Left" vTextAlign="Middle">
								<expression><![CDATA[$F{u1YmdGG}+
"<font style='font-size:9pt'>"+$F{u1YmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{u1YmdMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{u1YmdDD}+"</font>"+
"日"]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<leftPen lineWidth="0.0"/>
							<bottomPen lineWidth="0.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="frame" uuid="0cc04ed7-3b57-41e0-8726-265ed544aa20" positionType="Float" stretchType="ContainerHeight" x="0" y="40" width="510" height="223" removeLineWhenBlank="true">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<element kind="frame" uuid="0b5b03d1-f038-49bc-b397-f8a2f4e57d3e" x="0" y="0" width="126" height="223">
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<element kind="staticText" uuid="b25a4fec-d1c1-45db-aebd-8a8c202d114d" x="6" y="4" width="90" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
								<text><![CDATA[U2.今後の居住場所]]></text>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="0.8"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
							</element>
							<element kind="textField" uuid="bd429c1a-0412-4bee-9ab4-d0bdfa55983c" x="3" y="16" width="120" height="192" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Left" vTextAlign="Top">
								<expression><![CDATA[$F{u2MemoKnj}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{u2MemoFont}]]></propertyExpression>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{u2MemoColor}]]></propertyExpression>
							</element>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="frame" uuid="d980ab3c-ceba-4f4b-8996-3e766a29dac6" x="126" y="0" width="384" height="223">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<element kind="frame" uuid="312c88da-da34-41da-81b7-c13b3b37c206" x="0" y="0" width="340" height="216">
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<element kind="staticText" uuid="72401c3f-df89-4d2d-9d1f-d798847613cf" x="0" y="0" width="334" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[1.自分の家/アパート/賃貸の部屋]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="0"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								</element>
								<element kind="staticText" uuid="3ee6dd48-9591-4a89-b343-9adcfe1612e9" x="0" y="13" width="334" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[2.高齢者住宅－有料老人ホーム(特定施設入居者生活介護無し)]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="2"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
									<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
								</element>
								<element kind="staticText" uuid="a663730d-6931-4c70-ab44-f537abaa3317" x="0" y="26" width="334" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[3.高齢者住宅－有料老人ホーム(特定施設入居者生活介護有り)]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="2"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="2"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								</element>
								<element kind="staticText" uuid="185416ce-8a54-43f8-a221-127a15ea2d55" x="0" y="39" width="334" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[4.認知症対応型共同生活介護]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="3"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
									<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
								</element>
								<element kind="staticText" uuid="0a60c421-fd6a-4e0d-aa72-24d05b718197" x="0" y="52" width="334" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[5.小規模多機能型居宅介護]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="4"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
									<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
								</element>
								<element kind="staticText" uuid="a16fb2ed-0aa8-437d-92ba-3ff49bf227fe" x="0" y="65" width="334" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[6.介護老人福祉施設]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="5"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
									<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
								</element>
								<element kind="staticText" uuid="a3cfa155-bc32-4554-9c88-362d96cca9a3" x="0" y="78" width="334" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[7.介護老人保健施設]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="6"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
									<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
								</element>
								<element kind="staticText" uuid="99a1e805-157a-42e6-9142-9bfedee1d072" x="0" y="91" width="334" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[8.介護療養型老人保健施設]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="7"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
									<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
								</element>
								<element kind="staticText" uuid="f6a22a85-e9bb-4ad5-ba0b-8f62c2052505" x="0" y="104" width="334" height="12" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[9.介護療養型医療施設]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="8"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								</element>
								<element kind="staticText" uuid="b90d60c0-33a1-428b-9ae2-9ae535d570b5" x="0" y="116" width="334" height="12" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[10.回復期リハビリテーション病棟/病院]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="9"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								</element>
								<element kind="staticText" uuid="00994f2d-8faa-4f5c-aef0-466ffc541e95" x="0" y="128" width="334" height="12" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[11.精神科病院/病棟]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="10"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								</element>
								<element kind="staticText" uuid="915e52ed-7cd5-4b20-8546-41acc4f6b53b" x="0" y="140" width="334" height="12" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[12.緩和ケア病棟]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="11"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								</element>
								<element kind="staticText" uuid="6967c95f-191f-4b8e-86af-907926c818f5" x="0" y="152" width="334" height="12" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[13.上記(9～12)以外の病院]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="12"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="staticText" uuid="93cd6d29-77b7-4970-9116-157dad6fd6a1" x="0" y="164" width="334" height="12" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[14.精神障害者施設]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="13"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="staticText" uuid="9956b283-f18f-42c7-8fb6-2c92c9600e86" x="0" y="176" width="334" height="12" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[15.知的障害者施設]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="14"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="staticText" uuid="d077d19a-a719-4af3-b309-a246bd4715c2" x="0" y="188" width="334" height="12" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[16.ホームレス(シェルター利用の有無は問わない)]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="15"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								</element>
								<element kind="staticText" uuid="5202185a-0222-4973-abf3-0c3b68891f1e" x="0" y="200" width="334" height="12" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[18.その他]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="17"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								</element>
								<box padding="0" topPadding="4" leftPadding="6"/>
							</element>
							<element kind="textField" uuid="8fd0604e-a161-4fd3-84e3-cc915ec5a36c" x="344" y="206" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
								<expression><![CDATA[$F{u2}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<leftPen lineWidth="0.0"/>
							<bottomPen lineWidth="0.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="frame" uuid="7993f900-b35f-47fa-b8b5-424974ee9d12" positionType="Float" stretchType="ContainerHeight" x="0" y="263" width="510" height="66" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{assType}==2||$F{assType}==3]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<element kind="frame" uuid="1858decc-85a0-4eae-9269-f2f0029edfe3" x="0" y="0" width="126" height="66" removeLineWhenBlank="true">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<element kind="staticText" uuid="1d8c9b47-b48b-4d0d-98d2-c3d76197ef48" x="6" y="4" width="60" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
								<text><![CDATA[U3.退所後に]]></text>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="0.8"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="staticText" uuid="52769122-75e4-4982-9fee-c47ba0c3db6e" x="6" y="24" width="70" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
								<text><![CDATA[　  受ける予定]]></text>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="0.8"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="staticText" uuid="30c10f8e-e520-40e2-bd80-5a6a8685fa4b" x="6" y="14" width="90" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
								<text><![CDATA[　  居宅サービスを]]></text>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="0.8"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="textField" uuid="dda99691-8147-4d9d-8f2c-04ca4b2ab9f2" x="3" y="36" width="120" height="24" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Left" vTextAlign="Top">
								<expression><![CDATA[$F{u3MemoKnj}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{u3MemoFont}]]></propertyExpression>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{u3MemoColor}]]></propertyExpression>
							</element>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="frame" uuid="470f7fb4-4875-4663-b0d8-54c65bece66f" x="126" y="0" width="384" height="66" removeLineWhenBlank="true">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<element kind="frame" uuid="13fe72a0-bc01-48fd-b3fb-b5e2612d9bb0" x="0" y="0" width="340" height="24">
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<element kind="staticText" uuid="ba8481da-5109-4ef8-815f-647f4fb39061" x="0" y="0" width="334" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
									<text><![CDATA[0.いいえ]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="0"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								</element>
								<element kind="staticText" uuid="560e36aa-bf6f-4b4b-8640-f85a1f1b3b09" x="0" y="10" width="334" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<text><![CDATA[1.はい]]></text>
									<property name="com.jaspersoft.layout.grid.x" value="0"/>
									<property name="com.jaspersoft.layout.grid.y" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="2"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
									<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
								</element>
								<box padding="0" topPadding="4" leftPadding="6"/>
							</element>
							<element kind="textField" uuid="27e14ee5-6e87-4f1e-b583-6580fe7bcc67" x="344" y="49" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
								<expression><![CDATA[$F{u3}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<box>
						<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="a1bd579d-b959-454a-8f79-c263dd182a57" positionType="Float" x="0" y="458" width="510" height="23" removeLineWhenBlank="true">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="staticText" uuid="c7cb2600-99e5-4e4e-9582-57f99085c583" positionType="Float" x="0" y="481" width="110" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" bold="true" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[V.アセスメント情報]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<element kind="frame" uuid="187eed79-0c2c-4502-ba0f-4511695eb170" positionType="Float" x="0" y="494" width="510" height="29" removeLineWhenBlank="true">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="frame" uuid="17723f19-9b96-49a3-a458-0aef16e79f2f" x="0" y="0" width="126" height="29">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="staticText" uuid="b54eddc3-43da-4ffc-873f-affceaf5c02e" x="6" y="4" width="100" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
							<text><![CDATA[V1.アセスメント]]></text>
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="0.8"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="staticText" uuid="e744e7e9-6964-4ecf-8946-16baf21d2ffd" x="6" y="14" width="100" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
							<text><![CDATA[      担当者のサイン]]></text>
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="0.8"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="4e1f0c7a-4542-4df8-8554-9a7734f6fef4" x="126" y="0" width="384" height="29">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="textField" uuid="25346afc-630c-45a6-943e-834d310804b2" x="6" y="6" width="373" height="12" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Bottom">
							<paragraph firstLineIndent="4"/>
							<expression><![CDATA[$F{shokuin1Knj}+ " " +$F{shokuin2Knj}]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							<box>
								<pen lineWidth="1.0" lineStyle="Solid"/>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="line" uuid="8a985f03-88d7-4359-9690-a45a7c5420d9" x="4" y="23" width="373" height="1">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<box>
						<topPen lineWidth="2.0"/>
						<leftPen lineWidth="2.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="2.0"/>
					</box>
				</element>
				<element kind="frame" uuid="fd0e188e-2e95-4773-8293-ad481e6787c3" positionType="Float" x="0" y="523" width="510" height="21" removeLineWhenBlank="true">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="frame" uuid="9a40c2d1-52c7-43c3-97aa-52023f9341de" x="0" y="0" width="126" height="21">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="staticText" uuid="761ffa8a-1b2a-442f-9862-0550202aaca5" x="6" y="4" width="110" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
							<text><![CDATA[V2.アセスメント完成日]]></text>
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="0.8"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="faeaf784-5d53-4173-a607-60be57b6f000" x="126" y="0" width="384" height="21">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="textField" uuid="ddd9c837-8a5c-41c9-8e64-14fd9eb4e9af" x="6" y="4" width="117" height="10" markup="html" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Left" vTextAlign="Middle">
							<expression><![CDATA[$F{v2YmdGG}+
"<font style='font-size:9pt'>"+$F{v2YmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{v2YmdMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{v2YmdDD}+"</font>"+
"日"]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<box>
							<pen lineWidth="0.25"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="2.0"/>
						<bottomPen lineWidth="2.0"/>
						<rightPen lineWidth="2.0"/>
					</box>
				</element>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="54" splitType="Stretch"/>
</jasperReport>
