<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="U01020_MonitoringRecordReport" language="java" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="755" leftMargin="57" rightMargin="30" topMargin="33" bottomMargin="6" uuid="05680462-fe6b-404e-8cef-1149ab862db4">
	<style name="Gothic11Left" hTextAlign="Left" vTextAlign="Middle" rotation="None" fontName="IPAexGothic" fontSize="11.0"/>
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.0" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.0" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Style1" mode="Opaque" backcolor="#EBEBEB">
		<box>
			<bottomPen lineWidth="1.6"/>
		</box>
	</style>
	<dataset name="Dataset1" uuid="baf694c5-286e-43ee-a7ef-8916731d3838">
		<parameter name="mojiSize" class="java.lang.String"/>
		<parameter name="bangouFlg" class="java.lang.Integer"/>
		<parameter name="choukiFlg" class="java.lang.Integer"/>
		<parameter name="ichiranFlg" class="java.lang.Integer"/>
		<field name="kadaiNo" class="java.lang.String"/>
		<field name="kadaiKnj" class="java.lang.String"/>
		<field name="choukiKnj" class="java.lang.String"/>
		<field name="tankiKnj" class="java.lang.String"/>
		<field name="servNo" class="java.lang.String"/>
		<field name="kaigoKnj" class="java.lang.String"/>
		<field name="kakuninCd" class="java.lang.String"/>
		<field name="kakuninKnj" class="java.lang.String"/>
		<field name="houhouCd" class="java.lang.String"/>
		<field name="houhouKnj" class="java.lang.String"/>
		<field name="kakuninYmdGG" class="java.lang.String"/>
		<field name="kakuninYmdYY" class="java.lang.String"/>
		<field name="kakuninYmdMM" class="java.lang.String"/>
		<field name="kakuninYmdDD" class="java.lang.String"/>
		<field name="ikenHonCd" class="java.lang.String"/>
		<field name="ikenHonKnj" class="java.lang.String"/>
		<field name="ikenKazCd" class="java.lang.String"/>
		<field name="ikenKazKnj" class="java.lang.String"/>
		<field name="jusokuCd" class="java.lang.String"/>
		<field name="jusokuKnj" class="java.lang.String"/>
		<field name="taiouCd" class="java.lang.String"/>
		<field name="taiouKnj" class="java.lang.String"/>
		<field name="kadaiBottomLineFlg" class="java.lang.Integer">
			<description><![CDATA[kadaiBottomLineFlg]]></description>
		</field>
		<field name="choukiBottomLineFlg" class="java.lang.Integer">
			<description><![CDATA[choukiBottomLineFlg]]></description>
		</field>
		<field name="tankiBottomLineFlg" class="java.lang.Integer">
			<description><![CDATA[tankiBottomLineFlg]]></description>
		</field>
	</dataset>
	<dataset name="Dataset2" uuid="5c5a616b-2efc-4fef-a062-1c5229ecf5b3">
		<parameter name="mojiSize" class="java.lang.String"/>
		<field name="soukatuKnj" class="java.lang.String"/>
		<field name="henkouKnj" class="java.lang.String"/>
		<field name="retryChk" class="java.lang.String"/>
		<field name="yoteiYmdGG" class="java.lang.String"/>
		<field name="yoteiYmdYY" class="java.lang.String"/>
		<field name="yoteiYmdMM" class="java.lang.String"/>
		<field name="yoteiYmdDD" class="java.lang.String"/>
	</dataset>
	<parameter name="subReportPath" class="java.lang.String"/>
	<parameter name="subReportDataDs" class="net.sf.jasperreports.engine.JRDataSource"/>
	<query language="sql"><![CDATA[]]></query>
	<field name="title" class="java.lang.String"/>
	<field name="prDate" class="java.lang.Integer"/>
	<field name="shiTeiDateGG" class="java.lang.String"/>
	<field name="shiTeiDateYY" class="java.lang.String"/>
	<field name="shiTeiDateMM" class="java.lang.String"/>
	<field name="shiTeiDateDD" class="java.lang.String"/>
	<field name="jigyoName" class="java.lang.String"/>
	<field name="yokaigoDo" class="java.lang.String"/>
	<field name="riyoushaNm" class="java.lang.String"/>
	<field name="keisho" class="java.lang.String"/>
	<field name="createYmdGG" class="java.lang.String"/>
	<field name="createYmdYY" class="java.lang.String"/>
	<field name="createYmdMM" class="java.lang.String"/>
	<field name="createYmdDD" class="java.lang.String"/>
	<field name="shokuName" class="java.lang.String"/>
	<field name="hyoujiKbn" class="java.lang.Integer">
		<description><![CDATA[hyoujiKbn]]></description>
	</field>
	<field name="hanko1Knj" class="java.lang.String">
		<description><![CDATA[hanko1Knj]]></description>
	</field>
	<field name="hanko2Knj" class="java.lang.String">
		<description><![CDATA[hanko2Knj]]></description>
	</field>
	<field name="hanko3Knj" class="java.lang.String">
		<description><![CDATA[hanko3Knj]]></description>
	</field>
	<field name="hanko4Knj" class="java.lang.String">
		<description><![CDATA[hanko4Knj]]></description>
	</field>
	<field name="hanko5Knj" class="java.lang.String">
		<description><![CDATA[hanko5Knj]]></description>
	</field>
	<field name="hanko6Knj" class="java.lang.String">
		<description><![CDATA[hanko6Knj]]></description>
	</field>
	<field name="hanko7Knj" class="java.lang.String">
		<description><![CDATA[hanko7Knj]]></description>
	</field>
	<field name="hanko8Knj" class="java.lang.String">
		<description><![CDATA[hanko8Knj]]></description>
	</field>
	<field name="hanko9Knj" class="java.lang.String">
		<description><![CDATA[hanko9Knj]]></description>
	</field>
	<field name="hanko10Knj" class="java.lang.String">
		<description><![CDATA[hanko10Knj]]></description>
	</field>
	<field name="hanko11Knj" class="java.lang.String">
		<description><![CDATA[hanko11Knj]]></description>
	</field>
	<field name="hanko12Knj" class="java.lang.String">
		<description><![CDATA[hanko12Knj]]></description>
	</field>
	<field name="hanko13Knj" class="java.lang.String">
		<description><![CDATA[hanko13Knj]]></description>
	</field>
	<field name="hanko14Knj" class="java.lang.String">
		<description><![CDATA[hanko14Knj]]></description>
	</field>
	<field name="hanko15Knj" class="java.lang.String">
		<description><![CDATA[hanko15Knj]]></description>
	</field>
	<field name="emptyFlg" class="java.lang.Boolean"/>
	<field name="list" class="net.sf.jasperreports.engine.JRDataSource"/>
	<field name="bangouFlg" class="java.lang.Integer"/>
	<field name="choukiFlg" class="java.lang.Integer"/>
	<field name="shoninFlg" class="java.lang.Integer"/>
	<field name="bunsyoKanriNo" class="java.lang.String"/>
	<field name="ichiranFlg" class="java.lang.Integer">
		<description><![CDATA[ichiranFlg]]></description>
	</field>
	<field name="mojiSize" class="java.lang.String">
		<description><![CDATA[mojiSize]]></description>
	</field>
	<field name="soukatuList" class="net.sf.jasperreports.engine.JRDataSource"/>
	<pageHeader height="102" splitType="Stretch">
		<element kind="textField" uuid="99ecced1-0cb6-4a10-8983-17e4944cee85" x="0" y="12" width="728" height="14" fontName="IPAexGothic" fontSize="14.0" bold="true" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA[$F{title}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="textField" uuid="1cfa29a7-0f44-4b82-ba14-a326a0413603" x="634" y="0" width="100" height="12" markup="html" fontName="IPAexGothic" fontSize="10.0" hTextAlign="Right" vTextAlign="Middle">
			<printWhenExpression><![CDATA[($F{prDate} != 1) && (!$F{emptyFlg})]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDateGG}+
"<font style='font-size:9pt'>"+$F{shiTeiDateYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{shiTeiDateMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{shiTeiDateDD}+"</font>"+
"日"
]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="textField" uuid="cc025d58-ea9c-4d7e-96ec-00ccee2725ee" x="464" y="13" width="270" height="12" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[$F{jigyoName}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="staticText" uuid="a496898d-a649-4810-b49b-dea23bcac977" x="562" y="26" width="60" height="12" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<text><![CDATA[作成年月日]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<element kind="textField" uuid="4c306cd1-8cf4-452d-9a0b-ab7e72db734b" x="622" y="26" width="90" height="12" markup="html" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<printWhenExpression><![CDATA[!$F{createYmdGG}.equals("")]]></printWhenExpression>
			<expression><![CDATA[$F{createYmdGG}+
"<font style='font-size:10pt'>"+$F{createYmdYY}+"</font>"+
"年"+
"<font style='font-size:10pt'>"+$F{createYmdMM}+"</font>"+
"月"+
"<font style='font-size:10pt'>"+$F{createYmdDD}+"</font>"+
"日"
]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="staticText" uuid="42134f4a-bc82-4831-ba46-242bd38bc82c" x="583" y="39" width="40" height="12" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<text><![CDATA[作成者]]></text>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="bb28f743-f91b-4353-ac29-3d0c1ad39e08" x="623" y="39" width="100" height="12" fontName="IPAexGothic" fontSize="11.0" linkType="None" linkTarget="Self" vTextAlign="Middle">
			<expression><![CDATA[$F{shokuName}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="staticText" uuid="cd061cd1-29ff-4c20-9af8-2871c79e2cb8" x="0" y="26" width="46" height="12" fontName="IPAexGothic" fontSize="11.0">
			<text><![CDATA[要介護度]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="0e80ed44-d37b-407c-beab-0fa5795bdf3a" x="89" y="26" width="100" height="12" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<expression><![CDATA[$F{yokaigoDo}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="staticText" uuid="08263cf6-d89a-4e2e-bc36-fb9401531236" x="0" y="39" width="46" height="12" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<text><![CDATA[利用者名]]></text>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="frame" uuid="0d08f649-ca42-45ff-968d-d1419e6b3189" x="46" y="39" width="31" height="12">
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="textField" uuid="a324a340-ae5d-4158-b04a-0b6021c71756" x="77" y="39" width="114" height="12" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<expression><![CDATA[$F{riyoushaNm}]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="textField" uuid="33d770ee-e7f9-4620-a16d-2eb5c3bb6645" x="191" y="39" width="12" height="12" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<expression><![CDATA[$F{keisho}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="frame" uuid="0d45508a-2566-4a12-9afc-e63732885e2d" positionType="Float" x="4" y="55" width="707" height="47" removeLineWhenBlank="true">
			<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1]]></printWhenExpression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<element kind="textField" uuid="2df386e1-99ac-42ad-8676-a2a79eca6007" positionType="Float" x="0" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko1Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko1Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="10389e82-0990-4646-8ca2-5b4e8d9de48a" positionType="Float" x="0" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko1Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="ebfebe5a-090f-4bac-8115-1bdfd15589ec" positionType="Float" x="48" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&& !$F{hanko2Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko2Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="8496687e-09ae-4938-ab2e-3a9a187f27ee" positionType="Float" x="48" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&& !$F{hanko2Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="98cc5fdb-c813-4d0a-b219-787e806e4838" positionType="Float" x="95" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko3Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko3Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="bb062468-f399-42b9-8833-39201cb714e2" positionType="Float" x="95" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko3Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="6f08db02-ba9d-4fba-8009-49da56c0b6dc" positionType="Float" x="142" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko4Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko4Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="f8ce0370-2fcc-4770-a3e7-a46e638daf81" positionType="Float" x="142" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko4Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="39564c39-87a9-4103-ba70-696efa50b6b5" positionType="Float" x="189" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko5Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko5Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="6c31752e-cb89-490d-9b99-9e7d53eda02e" positionType="Float" x="189" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko5Knj}.isEmpty() && $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="f867d067-1c69-498e-8ae5-5d8660d964b2" positionType="Float" x="236" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko6Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko6Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="f1ed161e-7ad4-43fc-9499-aa4f0699fe5e" positionType="Float" x="236" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&& !$F{hanko6Knj}.isEmpty() && $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="f46ec92f-8477-4c4c-b4a1-8f294fd3c19d" positionType="Float" x="283" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&&  !$F{hanko7Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko7Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="5170d191-dab5-4bb2-9b89-bf857963563d" positionType="Float" x="283" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&&  !$F{hanko7Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="4e13c3ce-8803-4454-b019-0406d38fd385" positionType="Float" x="330" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko8Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko8Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="956f872b-9d88-49fe-a384-98cad627e0a9" positionType="Float" x="330" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko8Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="9759824b-a01f-482d-a859-f574c38eb2a6" positionType="Float" x="377" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&& !$F{hanko9Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko9Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="4ec6e5b3-4811-4ded-8e0f-ea3dbcd5a52c" positionType="Float" x="377" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&& !$F{hanko9Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="9e4f59b5-3464-4639-844f-3fc7a4e0bee2" positionType="Float" x="424" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko10Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko10Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="33886da1-71ce-4cf4-b331-b3c9ea4125e9" positionType="Float" x="424" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko10Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="998d7c96-e7e1-45cd-b31c-e3147ba3132a" positionType="Float" x="471" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko11Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko11Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="08a6aa27-c0c6-4682-b988-f8ebab898adf" positionType="Float" x="471" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko11Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="0ae299ec-532c-4785-8693-9024a13bf9f5" positionType="Float" x="518" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko12Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko12Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="ec12afb5-91dc-4206-9285-46aa51889cad" positionType="Float" x="518" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko12Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="f135ef59-564d-4a59-b210-8d3a33bfaacc" positionType="Float" x="565" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko13Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko13Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="3965893f-9aa8-4255-a511-34a3875c8a01" positionType="Float" x="565" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko13Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="be1211ca-b21f-4726-8c74-b65205bdc2b4" positionType="Float" x="612" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko14Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko14Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="4cc5e612-98ac-49af-8d7b-ea578a105f57" positionType="Float" x="612" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko14Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="8ff5570f-6b00-42dc-9840-737bc11309dc" positionType="Float" x="659" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko15Knj}.isEmpty() && $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko15Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="382f5dba-7423-4b52-bb2d-97b941178254" positionType="Float" x="659" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko15Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
		</element>
		<element kind="textField" uuid="cbcc0376-5b20-407e-a588-12cfa35c7221" x="636" y="0" width="100" height="12" fontName="IPAexGothic" fontSize="9.0" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$F{bunsyoKanriNo}]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="162" splitType="Stretch">
			<printWhenExpression><![CDATA[$F{ichiranFlg}==0 ]]></printWhenExpression>
			<element kind="component" uuid="04cfc654-a1e3-4015-8a4f-539d2e581817" stretchType="ContainerHeight" x="0" y="2" width="711" height="160">
				<component kind="list">
					<datasetRun uuid="2aafd3c2-bac1-47f4-8367-5ee81e55aff5" subDataset="Dataset1">
						<dataSourceExpression><![CDATA[$F{list}]]></dataSourceExpression>
						<parameter name="bangouFlg">
							<expression><![CDATA[$F{bangouFlg}]]></expression>
						</parameter>
						<parameter name="choukiFlg">
							<expression><![CDATA[$F{choukiFlg}]]></expression>
						</parameter>
						<parameter name="ichiranFlg">
							<expression><![CDATA[$F{ichiranFlg}]]></expression>
						</parameter>
						<parameter name="mojiSize">
							<expression><![CDATA[$F{mojiSize}]]></expression>
						</parameter>
					</datasetRun>
					<contents height="160" width="711">
						<element kind="frame" uuid="2d767164-088e-493b-b7f4-dde07d512697" stretchType="ContainerHeight" x="0" y="0" width="711" height="160">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<element kind="frame" uuid="13d1c6ef-0e08-4cfb-a30b-a58c2971b327" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="711" height="15">
								<printWhenExpression><![CDATA[$P{choukiFlg}==0 &&$P{bangouFlg} ==0
]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="staticText" uuid="52cb06f0-ec1f-4b5a-ad15-76cf87b63304" mode="Opaque" x="0" y="0" width="236" height="15" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0 &&$P{bangouFlg} ==0]]></printWhenExpression>
									<text><![CDATA[生活全般の解決すべき課題]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<element kind="staticText" uuid="0d6deac0-ffe2-4b87-871b-b9d364c8d8f9" mode="Opaque" x="236" y="0" width="236" height="15" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0 &&$P{bangouFlg} ==0]]></printWhenExpression>
									<text><![CDATA[短期目標]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.8"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="staticText" uuid="88b5a42b-6c5c-4008-b84f-4754ecccae7b" mode="Opaque" x="472" y="0" width="239" height="15" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0 &&$P{bangouFlg} ==0
]]></printWhenExpression>
									<text><![CDATA[サービス内容]]></text>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<leftPen lineWidth="0.8"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="1.6"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<element kind="frame" uuid="d49a605c-dd3a-4a4d-aa98-a6a91dd2b31d" positionType="Float" stretchType="ElementGroupHeight" x="0" y="15" width="711" height="35">
								<printWhenExpression><![CDATA[$P{choukiFlg}==0 &&$P{bangouFlg} ==0
]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="textField" uuid="c7c2972a-ea5e-4760-873b-dc46b163e658" stretchType="ContainerHeight" x="0" y="0" width="236" height="35" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0 &&$P{bangouFlg} ==0
]]></printWhenExpression>
									<paragraph lineSpacing="Single"/>
									<expression><![CDATA[$F{kadaiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0" lineStyle="Dashed"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="c33647a1-1fea-40cd-914c-64a4caa13d87" stretchType="ContainerHeight" x="236" y="0" width="236" height="35" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0 &&$P{bangouFlg} ==0
]]></printWhenExpression>
									<paragraph lineSpacing="Single"/>
									<expression><![CDATA[$F{tankiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="textField" uuid="*************-4533-a66d-d98284f7c577" stretchType="ContainerHeight" x="472" y="0" width="239" height="35" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0 &&$P{bangouFlg} ==0
]]></printWhenExpression>
									<paragraph lineSpacing="Single"/>
									<expression><![CDATA[$F{kaigoKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<box>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<element kind="frame" uuid="469089b9-c15f-46dc-b21c-d68c5ccfd48c" positionType="Float" stretchType="ElementGroupHeight" x="0" y="50" width="711" height="28">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<element kind="staticText" uuid="b6e660ba-7a03-40ea-95ba-9e7035457f6c" mode="Opaque" x="0" y="0" width="260" height="14" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[サービスの実行確認および確認方法]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="14cf2214-70c3-402a-98a8-dedc2d243c5a" mode="Opaque" x="0" y="14" width="100" height="14" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[実行確認]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<topPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="21d482a8-725d-4863-9fb4-f2e018772d2c" mode="Opaque" x="100" y="14" width="100" height="14" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 確認方法]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<topPen lineWidth="0.75"/>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="*************-43b2-8129-e9ac6d0818ca" mode="Opaque" x="200" y="14" width="60" height="14" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 確認期日]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<topPen lineWidth="0.75"/>
										<leftPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="24f76447-94b0-4df5-80ac-e6858bfabbe2" mode="Opaque" x="260" y="0" width="198" height="14" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[利用者本人・家族の意見・要望]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="d5e1656d-aa41-480b-938f-2f1a4fb7be49" mode="Opaque" x="260" y="14" width="99" height="14" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[本人]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<topPen lineWidth="0.75"/>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="dc9bf59f-7987-4af5-894c-9b2c3ba52477" mode="Opaque" x="359" y="14" width="99" height="14" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[家族]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<topPen lineWidth="0.75"/>
										<leftPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="f6884694-b263-458a-8b01-5bf64a04c23e" mode="Opaque" x="458" y="0" width="127" height="28" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
									<text><![CDATA[ニーズ充足度]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box topPadding="3">
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="7721548b-0e12-440a-83c4-a272a6dc43d0" mode="Opaque" x="585" y="0" width="126" height="28" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
									<text><![CDATA[ 対応]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box topPadding="3">
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="1.6"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<element kind="frame" uuid="5a1b868d-a31c-4e8b-b091-0212a758fc64" positionType="Float" stretchType="ElementGroupHeight" x="0" y="78" width="711" height="72">
								<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="frame" uuid="02578ad0-6840-4392-ae37-93ff4153191b" x="0" y="0" width="711" height="28">
									<element kind="textField" uuid="e0b5fa45-45c1-4b00-8490-25aa429a2243" stretchType="NoStretch" mode="Transparent" x="0" y="0" width="100" height="28" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true">
										<expression><![CDATA[$F{kakuninCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<pen lineWidth="0.4"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="ce0cf7cd-1896-47cf-b684-89c7af324bf9" stretchType="NoStretch" x="100" y="0" width="100" height="28" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{houhouCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<leftPen lineWidth="0.75"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="e2292b60-d5cb-4be2-b32d-33b2c6059bee" stretchType="ContainerHeight" x="200" y="0" width="60" height="28" markup="html" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[!$F{kakuninYmdYY}.equals("")]]></printWhenExpression>
										<expression><![CDATA[$F{kakuninYmdGG}+
"<font style='font-size:10pt'>"+$F{kakuninYmdYY}+"</font>"+
"/"+
"<font style='font-size:10pt'>"+$F{kakuninYmdMM}+"</font>"+
"/"+
"<font style='font-size:10pt'>"+$F{kakuninYmdDD}+"</font>"]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box topPadding="2" leftPadding="1" rightPadding="1">
											<leftPen lineWidth="0.75"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="textField" uuid="a389feb5-7ef3-446e-b675-861d55b56dcb" stretchType="NoStretch" x="260" y="0" width="99" height="28" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{ikenHonCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="be94767f-3d77-45ec-aa14-d2e92ed18efe" stretchType="NoStretch" x="359" y="0" width="99" height="28" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{ikenKazCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<leftPen lineWidth="0.75"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="textField" uuid="d6ceb6b6-2484-47bc-bb09-a70d599dc530" stretchType="NoStretch" x="458" y="0" width="127" height="28" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{jusokuCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="f6b27a79-18b8-4f6c-ad36-706796c2de63" stretchType="NoStretch" x="585" y="0" width="126" height="28" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{taiouCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="7129fca2-cd72-445f-8fdd-612d5cc87439" x="0" y="28" width="711" height="44">
									<borderSplitType>DrawBorders</borderSplitType>
									<element kind="textField" uuid="f3c9454f-5832-455a-8d7a-03aa1cc2adf0" stretchType="ContainerHeight" x="0" y="0" width="100" height="44" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{kakuninKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1"/>
									</element>
									<element kind="textField" uuid="7d94f4ea-4e99-470e-8d46-79a850cfe76d" stretchType="ContainerHeight" x="100" y="0" width="100" height="44" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{houhouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<leftPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="frame" uuid="f80c9235-8235-4732-a7c2-5b7c7553d4af" stretchType="ContainerHeight" x="200" y="0" width="60" height="44" printWhenDetailOverflows="true">
										<box>
											<leftPen lineWidth="0.75"/>
											<rightPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="textField" uuid="fd6b18c4-235a-4f27-82d4-6d3da6c6ce6d" stretchType="ContainerHeight" mode="Transparent" x="260" y="0" width="99" height="44" backcolor="#FFFFFF" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{ikenHonKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1"/>
									</element>
									<element kind="textField" uuid="f4d31e55-d468-46c7-8cc7-c7adbc1beed7" stretchType="ContainerHeight" x="359" y="0" width="99" height="44" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{ikenKazKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<leftPen lineWidth="0.75"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="textField" uuid="1e19d91e-0310-4940-851a-dd363d3b6e9c" stretchType="ContainerHeight" x="458" y="0" width="127" height="44" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{jusokuKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2"/>
									</element>
									<element kind="textField" uuid="cd4016ae-1b62-4d72-87bf-b94026a0e556" stretchType="ContainerHeight" x="585" y="0" width="126" height="44" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{taiouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="frame" uuid="19ef0056-9ddc-48a1-adf8-ca206f34044f" stretchType="ContainerHeight" x="0" y="0" width="100" height="44" printWhenDetailOverflows="true">
										<borderSplitType>DrawBorders</borderSplitType>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<box>
											<leftPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="frame" uuid="f75057e7-6364-44b5-8136-30edb64f3e2c" stretchType="ContainerHeight" x="100" y="0" width="100" height="44" printWhenDetailOverflows="true">
										<borderSplitType>DrawBorders</borderSplitType>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<box>
											<leftPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="frame" uuid="889a3c6a-ae44-483e-9099-99f05c58d8d8" stretchType="ContainerHeight" x="260" y="0" width="99" height="44" printWhenDetailOverflows="true">
										<borderSplitType>DrawBorders</borderSplitType>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<box>
											<leftPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="frame" uuid="bb3fc56e-721c-446e-a82f-4242cd6d1362" stretchType="ContainerHeight" x="359" y="0" width="99" height="44" printWhenDetailOverflows="true">
										<borderSplitType>DrawBorders</borderSplitType>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<box>
											<leftPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="frame" uuid="981c37ef-f22a-45af-ac56-2a4fa6a18e0d" stretchType="ContainerHeight" x="458" y="0" width="127" height="44" printWhenDetailOverflows="true">
										<borderSplitType>DrawBorders</borderSplitType>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<box>
											<leftPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="frame" uuid="b9fcc15c-4bd4-4635-8103-d573979209ad" stretchType="ContainerHeight" x="585" y="0" width="126" height="44" printWhenDetailOverflows="true">
										<borderSplitType>DrawBorders</borderSplitType>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<box>
											<leftPen lineWidth="0.75"/>
										</box>
									</element>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="1.6"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<element kind="frame" uuid="229c7efd-7954-450a-9145-c0feff7a4812" positionType="Float" stretchType="NoStretch" x="0" y="150" width="711" height="10">
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="1.6" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<element kind="frame" uuid="c7f23426-86bc-47a3-86c8-c4048f51bf68" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="711" height="15">
								<printWhenExpression><![CDATA[$P{bangouFlg}==1 &&$P{choukiFlg} == 1]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="staticText" uuid="60923993-80ff-410b-be9e-c9952960e5d1" mode="Opaque" x="0" y="0" width="192" height="15" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1 &&$P{choukiFlg} == 1]]></printWhenExpression>
									<text><![CDATA[生活全般の解決すべき課題]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<element kind="staticText" uuid="f1c950ff-70ca-410b-8337-9903b4f7126b" mode="Opaque" x="192" y="0" width="163" height="15" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1 &&$P{choukiFlg} == 1]]></printWhenExpression>
									<text><![CDATA[長期目標]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="b623e5a9-ff43-43da-ab17-ff01a98906ce" mode="Opaque" x="355" y="0" width="163" height="15" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1 &&$P{choukiFlg} == 1]]></printWhenExpression>
									<text><![CDATA[短期目標]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="98bccc23-30f0-417e-9edb-b9fc0d80bb48" mode="Opaque" x="518" y="0" width="193" height="15" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1 &&$P{choukiFlg} == 1]]></printWhenExpression>
									<text><![CDATA[サービス内容]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<leftPen lineWidth="0.8"/>
									</box>
								</element>
								<box>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<element kind="frame" uuid="38d0dfe2-3c21-4b5c-a6a8-7cdf52173dd3" positionType="Float" stretchType="ElementGroupHeight" x="0" y="15" width="711" height="35">
								<printWhenExpression><![CDATA[$P{bangouFlg}==1 &&$P{choukiFlg} == 1]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="textField" uuid="795b6fec-a856-4210-be62-8e6118a74060" stretchType="ContainerHeight" x="0" y="0" width="20" height="35" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Right">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1 &&$P{choukiFlg} == 1]]></printWhenExpression>
									<expression><![CDATA[$F{kadaiNo}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box topPadding="1" rightPadding="2"/>
								</element>
								<element kind="textField" uuid="327609b7-731d-4592-8b2d-76a4a7e0218d" stretchType="ContainerHeight" x="20" y="0" width="172" height="35" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1 &&$P{choukiFlg} == 1]]></printWhenExpression>
									<paragraph lineSpacing="Single"/>
									<expression><![CDATA[$F{kadaiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="7277d55b-75c7-49d0-8aee-125f9bc0d302" stretchType="ContainerHeight" x="192" y="0" width="163" height="35" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1 &&$P{choukiFlg} == 1]]></printWhenExpression>
									<paragraph lineSpacing="Single"/>
									<expression><![CDATA[$F{choukiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2"/>
								</element>
								<element kind="textField" uuid="dbffe68e-eefc-4fa6-ab87-f2c7c19aac52" stretchType="ContainerHeight" x="355" y="0" width="163" height="35" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1 &&$P{choukiFlg} == 1]]></printWhenExpression>
									<paragraph lineSpacing="Single"/>
									<expression><![CDATA[$F{tankiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="textField" uuid="89463af9-95ad-4836-85f9-31b0a66fe2fc" stretchType="ContainerHeight" x="538" y="0" width="172" height="35" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1 &&$P{choukiFlg} == 1]]></printWhenExpression>
									<paragraph lineSpacing="Single"/>
									<expression><![CDATA[$F{kaigoKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<element kind="textField" uuid="573e01d2-7adc-4f33-ba1e-3bcc9d5cb3a5" stretchType="ContainerHeight" x="518" y="0" width="20" height="35" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Right">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1 &&$P{choukiFlg} == 1]]></printWhenExpression>
									<expression><![CDATA[$F{servNo}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box topPadding="1" rightPadding="2">
										<leftPen lineWidth="0.0"/>
									</box>
								</element>
								<box>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<element kind="frame" uuid="2143faf7-583f-4867-a849-f85d4302f62a" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="711" height="15">
								<printWhenExpression><![CDATA[$P{bangouFlg}==1  && $P{choukiFlg}==0]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="staticText" uuid="83850662-f061-4df3-af71-716ba392c40e" mode="Opaque" x="0" y="0" width="252" height="15" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1  && $P{choukiFlg}==0]]></printWhenExpression>
									<text><![CDATA[生活全般の解決すべき課題]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<element kind="staticText" uuid="789a18fd-be43-4725-8ff5-ae9b241320f6" mode="Opaque" x="251" y="0" width="207" height="15" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1  && $P{choukiFlg}==0]]></printWhenExpression>
									<text><![CDATA[短期目標]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.8"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="staticText" uuid="a58ec462-cfa7-4889-82a8-575acabbb99d" mode="Opaque" x="458" y="0" width="253" height="15" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1  && $P{choukiFlg}==0]]></printWhenExpression>
									<text><![CDATA[サービス内容]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
									</box>
								</element>
								<box>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<element kind="frame" uuid="fe1ceafa-3387-410a-a23b-d2741f08ff13" positionType="Float" stretchType="ElementGroupHeight" x="0" y="15" width="711" height="35">
								<printWhenExpression><![CDATA[$P{bangouFlg}==1  && $P{choukiFlg}==0]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="textField" uuid="b1e31017-727a-43e5-bb0f-794752b6a4a3" stretchType="ContainerHeight" x="0" y="0" width="19" height="35" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Right">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1  && $P{choukiFlg}==0]]></printWhenExpression>
									<expression><![CDATA[$F{kadaiNo}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box topPadding="1" rightPadding="2">
										<topPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="ddf10a5b-85bc-4fd1-8834-b2be80b797ea" stretchType="ContainerHeight" x="19" y="0" width="233" height="35" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1  && $P{choukiFlg}==0]]></printWhenExpression>
									<expression><![CDATA[$F{kadaiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.75"/>
										<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
								</element>
								<element kind="textField" uuid="656f412a-5289-4db7-801b-656d46b18115" stretchType="ContainerHeight" x="251" y="0" width="207" height="35" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1  && $P{choukiFlg}==0]]></printWhenExpression>
									<expression><![CDATA[$F{tankiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
										<topPen lineWidth="0.75"/>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="textField" uuid="0a5a29eb-5311-47c1-90b7-d078269598de" stretchType="ContainerHeight" x="479" y="0" width="232" height="35" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1  && $P{choukiFlg}==0]]></printWhenExpression>
									<expression><![CDATA[$F{kaigoKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<topPen lineWidth="0.75"/>
										<leftPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<element kind="textField" uuid="57a5d29b-8b05-4442-9e4d-ea3173c2ce4e" stretchType="ContainerHeight" x="458" y="0" width="21" height="35" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Right">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1  && $P{choukiFlg}==0]]></printWhenExpression>
									<expression><![CDATA[$F{servNo}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box topPadding="1" rightPadding="2">
										<topPen lineWidth="0.75"/>
										<leftPen lineWidth="0.0"/>
									</box>
								</element>
								<box>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<element kind="frame" uuid="75ac8e11-b945-4e57-a8d2-27c04fadb824" positionType="Float" stretchType="ElementGroupHeight" mode="Opaque" x="0" y="0" width="711" height="15" backcolor="#EBEBEB">
								<printWhenExpression><![CDATA[$P{bangouFlg}== 0&&$P{choukiFlg} == 1]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<element kind="staticText" uuid="f4e3ed4e-a31d-4829-a5b5-409d4fba56e5" mode="Opaque" x="0" y="0" width="178" height="15" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{bangouFlg}== 0&&$P{choukiFlg} == 1]]></printWhenExpression>
									<text><![CDATA[生活全般の解決すべき課題]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<element kind="staticText" uuid="d234d96a-2daf-4a87-8b35-465ca24132ff" mode="Opaque" x="178" y="0" width="177" height="15" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{bangouFlg}== 0&&$P{choukiFlg} == 1]]></printWhenExpression>
									<text><![CDATA[長期目標]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.8"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="staticText" uuid="db71126f-9a8b-435e-baab-bc48ccee9707" mode="Opaque" x="355" y="0" width="177" height="15" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{bangouFlg}== 0&&$P{choukiFlg} == 1]]></printWhenExpression>
									<text><![CDATA[短期目標]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.8"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="staticText" uuid="eeec96be-2bac-4483-9f19-5795c86ad809" mode="Opaque" x="532" y="0" width="177" height="15" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{bangouFlg}== 0&&$P{choukiFlg} == 1]]></printWhenExpression>
									<text><![CDATA[サービス内容]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<leftPen lineWidth="0.8"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="1.6"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<element kind="frame" uuid="47ddaf4f-add7-435c-8bb4-d5b11c2c0d5c" positionType="Float" stretchType="ElementGroupHeight" x="0" y="15" width="711" height="35">
								<printWhenExpression><![CDATA[$P{bangouFlg}== 0&&$P{choukiFlg} == 1]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<element kind="textField" uuid="e5aaf36b-7bb7-4b71-94e6-c1dc6a43f166" stretchType="ContainerHeight" x="0" y="0" width="178" height="35" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{bangouFlg}== 0&&$P{choukiFlg} == 1]]></printWhenExpression>
									<expression><![CDATA[$F{kadaiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0" lineStyle="Dashed"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="13e0627b-b0b5-46a2-aa48-5034dffce5ae" stretchType="ContainerHeight" x="178" y="0" width="177" height="35" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{bangouFlg}== 0&&$P{choukiFlg} == 1]]></printWhenExpression>
									<expression><![CDATA[$F{choukiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="e39cfe03-436c-45df-9c1b-cd9711d3ab52" stretchType="ContainerHeight" x="355" y="0" width="177" height="35" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{bangouFlg}== 0&&$P{choukiFlg} == 1]]></printWhenExpression>
									<expression><![CDATA[$F{tankiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="textField" uuid="8c9f2da1-1668-474d-bf2f-372af71851c4" stretchType="ContainerHeight" x="532" y="0" width="177" height="35" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{bangouFlg}== 0&&$P{choukiFlg} == 1]]></printWhenExpression>
									<expression><![CDATA[$F{kaigoKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<box>
									<leftPen lineWidth="1.6"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<box>
								<topPen lineWidth="0.8"/>
								<leftPen lineWidth="0.0"/>
								<bottomPen lineWidth="0.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
					</contents>
				</component>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="117">
			<printWhenExpression><![CDATA[$F{ichiranFlg}==1&&$F{mojiSize}.equals("9")]]></printWhenExpression>
			<element kind="component" uuid="e5ee3a0b-e3f9-4afb-a90c-802f12be74e3" x="0" y="2" width="749" height="115" printWhenDetailOverflows="true">
				<component kind="table">
					<datasetRun uuid="5606fa13-f9bc-4c48-9058-918c223d14bf" subDataset="Dataset1">
						<dataSourceExpression><![CDATA[$F{list}]]></dataSourceExpression>
						<parameter name="bangouFlg">
							<expression><![CDATA[$F{bangouFlg}]]></expression>
						</parameter>
						<parameter name="choukiFlg">
							<expression><![CDATA[$F{choukiFlg}]]></expression>
						</parameter>
						<parameter name="ichiranFlg">
							<expression><![CDATA[$F{ichiranFlg}]]></expression>
						</parameter>
						<parameter name="mojiSize">
							<expression><![CDATA[$F{mojiSize}]]></expression>
						</parameter>
					</datasetRun>
					<column kind="single" uuid="4021fc8c-501b-4818-9c8f-2f7d4bcb7a1c" width="749">
						<columnHeader height="32" rowSpan="1" style="Style1">
							<element kind="frame" uuid="2767dbca-21c9-4510-9e11-85277e66886b" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="749" height="32" printWhenDetailOverflows="true">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<element kind="staticText" uuid="7fb3b67e-d534-4ae8-bbcc-0edb473f54c7" x="262" y="0" width="189" height="16" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[サービスの実行確認および確認方法]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="47a62327-f7a9-4d52-a720-3dfa6b494770" x="262" y="16" width="65" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[実行確認]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<element kind="staticText" uuid="6f37e390-64a7-4e4b-9c29-55d7b3a0ab9f" x="327" y="16" width="63" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 確認方法]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="17b72411-4e67-4dc1-b594-a51eccab13a6" x="390" y="16" width="61" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 確認期日]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="staticText" uuid="faaa026a-204b-4c36-b736-702a851465e0" x="451" y="0" width="150" height="16" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[利用者本人・家族の意見・要望]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="78787cda-043a-4279-ab48-db121b3fb93b" x="451" y="16" width="75" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[本人]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="fcbfba33-cdb4-408b-9d03-40e88f6a1888" x="526" y="16" width="75" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[家族]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<element kind="staticText" uuid="1b7d5e2d-f9ba-4938-82f9-3cc6dcaba817" x="601" y="0" width="74" height="32" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ニーズ充足度]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="8ac5fc45-c549-43f3-b45d-7d1a9d05c49a" x="675" y="0" width="74" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[対応]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="frame" uuid="219e4879-b79a-464b-bf55-b46275c88e7e" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="262" height="32" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<element kind="staticText" uuid="45e11d99-a4ff-46e1-be35-e04dd4f26543" mode="Opaque" x="0" y="0" width="92" height="32" backcolor="rgba(250, 9, 5, 0.0)" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[生活全般の解決
すべき課題]]></text>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
									</element>
									<element kind="staticText" uuid="3dfc4c7d-0ff2-4e53-82e1-c26e7e5fac30" x="92" y="0" width="85" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[短期目標]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box>
											<leftPen lineWidth="1.0"/>
										</box>
									</element>
									<element kind="staticText" uuid="f59aec17-7c78-4d40-a8a8-c2c9b1c53cf3" x="177" y="0" width="85" height="32" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[サービス内容]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box>
											<leftPen lineWidth="0.8"/>
										</box>
									</element>
									<box>
										<leftPen lineWidth="1.6"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="frame" uuid="5e434587-6683-41ec-aa19-46f1ec770c7a" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="262" height="32" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<element kind="staticText" uuid="578403ce-e3d0-487a-b907-a295a09c3aec" mode="Opaque" x="0" y="0" width="76" height="32" backcolor="rgba(250, 9, 5, 0.0)" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[生活全般の解決
すべき課題]]></text>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="staticText" uuid="f45d769e-79d4-437b-8ca8-4d3047a2583f" x="76" y="0" width="62" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[長期目標]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.75"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="staticText" uuid="db24a5e4-6fd7-48bc-805c-fdd56bab7bbd" x="138" y="0" width="62" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[短期目標]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="staticText" uuid="706dd066-2472-417e-8609-a578abc4bac3" x="200" y="0" width="62" height="32" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[サービス
内容]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.8"/>
										</box>
									</element>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="1.6"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box style="Style1">
								<bottomPen lineWidth="0.75"/>
							</box>
						</columnHeader>
						<columnFooter height="10" rowSpan="1">
							<element kind="frame" uuid="5282d99b-cbba-4254-8356-27e63838c622" positionType="Float" stretchType="NoStretch" x="0" y="0" width="749" height="10" backcolor="#FFFFFF">
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="1.6" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</columnFooter>
						<detailCell height="72" style="Table_TD">
							<element kind="frame" uuid="69d69011-0cb0-4fdc-ab63-94937c5d73df" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="749" height="72">
								<borderSplitType>DrawBorders</borderSplitType>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<element kind="frame" uuid="ecbe1c2b-c351-4126-8b34-3d1c1fca9e79" stretchType="ContainerHeight" x="262" y="0" width="65" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="9e20ad23-1368-4da7-85c0-b8d0e5c74292" stretchType="NoStretch" mode="Transparent" x="0" y="0" width="65" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true">
										<expression><![CDATA[$F{kakuninCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<pen lineWidth="0.4"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
											<rightPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="textField" uuid="b3cd8f88-aca1-42a2-bd0a-a8516376d175" stretchType="ContainerHeight" x="0" y="32" width="65" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{kakuninKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3"/>
									</element>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="1.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="f0b6d2e8-0ddb-4ae4-bc19-e6bdf0154b2b" stretchType="ContainerHeight" x="327" y="0" width="63" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="ccefae28-7c50-4b2f-b944-e138bd9740d2" stretchType="NoStretch" x="0" y="0" width="63" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{houhouCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="94aa1c54-91c0-4fbc-9e18-2a14e5388845" stretchType="ContainerHeight" x="0" y="32" width="63" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{houhouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<leftPen lineWidth="0.0"/>
										</box>
									</element>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="ed8a8c54-07fa-4030-af45-3fbd0556f719" stretchType="ContainerHeight" x="390" y="0" width="61" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="118166dc-c752-4200-979c-071363e50b6a" stretchType="ContainerHeight" x="0" y="0" width="61" height="32" markup="html" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[!$F{kakuninYmdYY}.equals("")]]></printWhenExpression>
										<expression><![CDATA[$F{kakuninYmdGG}+
"<font style='font-size:10pt'>"+$F{kakuninYmdYY}+"</font>"+
"/"+
"<font style='font-size:10pt'>"+$F{kakuninYmdMM}+"</font>"+
"/"+
"<font style='font-size:10pt'>"+$F{kakuninYmdDD}+"</font>"]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="2" leftPadding="1" rightPadding="1">
											<leftPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0"/>
										</box>
									</element>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="2b27fd0e-e5d4-4fd0-be10-a956147ffc90" stretchType="ContainerHeight" x="451" y="0" width="75" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="70fb2c76-86c2-4046-ba9e-f0c020626480" stretchType="NoStretch" x="0" y="0" width="75" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{ikenHonCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="30db85e8-e16d-4768-80bd-d27879d61cc5" stretchType="ContainerHeight" mode="Transparent" x="0" y="32" width="75" height="40" backcolor="#FFFFFF" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{ikenHonKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2"/>
									</element>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="f6c44241-97c6-460b-966e-29d8e1be0324" stretchType="ContainerHeight" x="526" y="0" width="75" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<element kind="textField" uuid="8a14a537-4fd5-4bad-aaae-6136ade0da9e" stretchType="NoStretch" x="0" y="0" width="75" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{ikenKazCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="textField" uuid="a641dc1b-0ec9-4d76-b85e-0e3e6e6ffafb" stretchType="ContainerHeight" x="0" y="32" width="75" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{ikenKazKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="f9f0ab2f-e419-4de4-b3cb-b1e9e4b8c23c" stretchType="ContainerHeight" x="601" y="0" width="74" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="94d5fa5f-c569-496b-abe4-b77f20ec8708" stretchType="NoStretch" x="0" y="0" width="74" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{jusokuCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="287b3ba9-91eb-4c1e-b210-1797024b0e1e" stretchType="ContainerHeight" x="0" y="32" width="74" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{jusokuKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2"/>
									</element>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="041efef2-e5ee-4f4d-a0c7-4e71f22e686c" stretchType="ContainerHeight" x="675" y="0" width="74" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="35e69f9e-4b92-440f-8238-e1cb0bacf57a" stretchType="NoStretch" x="0" y="0" width="74" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{taiouCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="db81d9de-4c6a-42da-8078-9ccf8869c19a" stretchType="ContainerHeight" x="0" y="32" width="74" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{taiouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
										</box>
									</element>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="ea3b68ac-6213-4726-887c-bcd07d41a943" stretchType="ContainerHeight" x="92" y="0" width="85" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0]]></printWhenExpression>
									<expression><![CDATA[$F{tankiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
										<leftPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
								</element>
								<element kind="frame" uuid="5703c657-24cc-4fac-90b1-5ed9db21875f" stretchType="ContainerHeight" x="0" y="0" width="92" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="1ad1c96b-62a4-48a7-9f16-4dbeea78221e" stretchType="ContainerHeight" x="16" y="0" width="76" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="10">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.75" lineColor="#030303"/>
										</box>
									</element>
									<element kind="textField" uuid="e5cace23-b307-4e60-9d95-10624240dee8" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="0">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</element>
									<box>
										<pen lineWidth="1.5"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="77a69d86-9c37-4dda-a0bb-a917b36b623e" stretchType="ContainerHeight" x="177" y="0" width="85" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="5d5c38c0-9713-4236-8a97-56ba1ea50bb7" stretchType="ContainerHeight" x="16" y="0" width="69" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="5b6a85e1-24bf-4e9e-bfe5-57b94cc318d3" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{servNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="0">
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.4" lineStyle="Dashed" lineColor="#000000"/>
										</box>
									</element>
									<box>
										<leftPen lineWidth="0.75" lineStyle="Solid"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="0f9f31f8-6f60-45b8-9aae-add1c67e2170" stretchType="ContainerHeight" x="0" y="0" width="92" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="8389b497-29c4-4bac-a333-facdb0270340" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="10">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0" lineColor="#F23430"/>
										</box>
									</element>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="1e3ffb70-9527-4867-bb5c-3dfd368f9786" stretchType="ContainerHeight" x="177" y="0" width="85" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="1ceeba7b-7f82-4334-83d3-6d221b8aad4c" stretchType="ContainerHeight" x="0" y="0" width="85" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="textField" uuid="d8aec851-b7f0-49e6-865b-86349ed119ed" stretchType="ContainerHeight" x="76" y="0" width="62" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<expression><![CDATA[$F{choukiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="2eab1492-023a-4c55-9a56-998f282df289" stretchType="ContainerHeight" x="138" y="0" width="62" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<expression><![CDATA[$F{tankiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="frame" uuid="a3750035-86f7-4003-a64b-ca56132804fc" stretchType="ContainerHeight" x="0" y="0" width="76" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="44aceb59-c3c3-46fe-b925-9a9fdd84ffd7" stretchType="ContainerHeight" x="16" y="0" width="60" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="textField" uuid="4b8f62f2-d433-4b1d-9fa2-534e2bdc839a" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="0">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.4" lineStyle="Dashed" lineColor="#000000"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="01d5599e-639e-44d7-a804-2f2e45986556" stretchType="ContainerHeight" x="200" y="0" width="62" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="cd6da639-8aab-4a73-b182-eb900903c004" stretchType="ContainerHeight" x="15" y="0" width="47" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="0">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="5c8775e1-12eb-4005-9199-911b201645c8" stretchType="ContainerHeight" x="0" y="0" width="15" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{servNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="0">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<rightPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="82ce7bce-85de-4fbc-a901-02a3d002c2fb" stretchType="ContainerHeight" x="0" y="0" width="76" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="c1e3464c-8439-4cad-8364-54b1e5696388" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="10">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="90a49bdf-df2c-4a6d-b454-6073322cf987" stretchType="ContainerHeight" x="200" y="0" width="62" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="97c26c41-b94a-4efa-9ad4-a3cb87023704" stretchType="ContainerHeight" x="0" y="0" width="62" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="563512d7-a71f-4ea8-862a-3afd338fed92" stretchType="ContainerHeight" x="262" y="0" width="65" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="e4e5ae41-9707-4130-8efc-676e8548cd12" stretchType="ContainerHeight" x="327" y="0" width="63" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="161461e8-06bb-4552-9219-dc27c84fa25e" stretchType="ContainerHeight" x="390" y="0" width="61" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="d8074c21-710f-4b68-b9d9-55a34db95ac2" stretchType="ContainerHeight" x="451" y="0" width="75" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="9daad8fb-2e19-4b04-9031-c14e0e5e3ce6" stretchType="ContainerHeight" x="526" y="0" width="75" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="655e26d6-c130-49de-85ae-5b89af4d650e" stretchType="ContainerHeight" x="601" y="0" width="74" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="ac165c29-28f5-4d9e-bc95-ef60f9aab57f" stretchType="ContainerHeight" x="675" y="0" width="74" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="ffea4f4b-74ec-4e92-aabe-ab078ce0f177" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="1.5"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="1ae64edf-34a1-4701-8bf0-eb95dd32393c" stretchType="ContainerHeight" x="177" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75" lineStyle="Solid"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="4d02ded9-09d0-4809-a76a-2af7a033b5d7" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="ebb88d64-1325-4586-9dcb-780bc7e0a67c" stretchType="ContainerHeight" x="177" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="19dff03a-ff52-4bb7-b85d-0f712c94fff6" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="c3c470d5-2c00-422b-ab2e-fe45f8aa4025" stretchType="ContainerHeight" x="200" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="e56e63af-9ad9-430f-aa78-a02e80c37f78" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="d3ea9de9-47c4-48c2-99f0-3634aee36b6b" stretchType="ContainerHeight" x="200" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="841055d9-0a2c-4864-bc78-ec805f79c5ac" stretchType="ContainerHeight" x="76" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="f7ca3193-4f0e-4724-9659-fd8e723043b5" stretchType="ContainerHeight" x="138" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="6dd27cbb-08df-4a6b-bd2a-c79fc5a31e0a" stretchType="ContainerHeight" x="92" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="29e49cb9-97d5-45d9-8f4b-e57b8fc53978" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.4" lineStyle="Dashed" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="922b4292-1651-45f1-b646-4c3a96f1fece" stretchType="ContainerHeight" x="177" y="0" width="16" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<element kind="frame" uuid="db44ea27-1552-4c8e-9f50-e8e09b530d3a" stretchType="ContainerHeight" x="200" y="0" width="15" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<element kind="frame" uuid="36e4451e-1160-4616-8ab6-e6d7fe850237" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="1.5"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="9f725a85-61ef-461a-a2b7-d5f2bd3134c8" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="b9ab3afd-0284-44aa-801d-03b0c376ae2b" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="078f2add-74fc-41e9-9ebe-2b91a90a89ca" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="e3d62578-9d51-4ffb-8aad-5ac821c04c7c" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="a36dfb72-b47e-4190-87b9-d8699f95a9ad" stretchType="ContainerHeight" x="76" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1&&$F{choukiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="d09af14d-4e3e-4882-a169-8b9d0a8ef90e" stretchType="ContainerHeight" x="138" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1&&$F{tankiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="9e3a8a38-cb15-4bc2-8887-784e46a349c5" stretchType="ContainerHeight" x="92" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0&&$F{tankiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
				<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
				<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="117">
			<printWhenExpression><![CDATA[$F{ichiranFlg}==1&&$F{mojiSize}.equals("10")]]></printWhenExpression>
			<element kind="component" uuid="a5f746c9-cf53-402d-98f4-457cf84b71ee" x="0" y="2" width="749" height="115">
				<component kind="table">
					<datasetRun uuid="acacdc98-acb6-43a0-a8bf-d5e924f38b73" subDataset="Dataset1">
						<dataSourceExpression><![CDATA[$F{list}]]></dataSourceExpression>
						<parameter name="bangouFlg">
							<expression><![CDATA[$F{bangouFlg}]]></expression>
						</parameter>
						<parameter name="choukiFlg">
							<expression><![CDATA[$F{choukiFlg}]]></expression>
						</parameter>
						<parameter name="ichiranFlg">
							<expression><![CDATA[$F{ichiranFlg}]]></expression>
						</parameter>
						<parameter name="mojiSize">
							<expression><![CDATA[$F{mojiSize}]]></expression>
						</parameter>
					</datasetRun>
					<column kind="single" uuid="a7b655ce-bba3-4106-a8df-a7e856fe0ee6" width="749">
						<columnHeader height="32" rowSpan="1" style="Style1">
							<element kind="frame" uuid="976d1def-4536-4d77-a998-75933a86410f" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="749" height="32" printWhenDetailOverflows="true">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<element kind="staticText" uuid="2f0fb823-08ca-4537-b09e-0cfed3aaa1ad" x="262" y="0" width="189" height="16" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[サービスの実行確認および確認方法]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="59c22b11-5bc5-4c6d-bab8-ec41ce7ec418" x="262" y="16" width="65" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[実行確認]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<element kind="staticText" uuid="1fe1a8e3-3557-4b30-8d12-4522e9fca99d" x="327" y="16" width="63" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 確認方法]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="feee44f0-74d6-4da3-a99a-d3a33ba280e1" x="390" y="16" width="61" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 確認期日]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="staticText" uuid="260646b5-07c1-4b15-a25a-e2b2e8e10ae9" x="451" y="0" width="150" height="16" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[利用者本人・家族の意見・要望]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="146dc7e3-48c8-4966-a81a-1ca5af30b0a7" x="451" y="16" width="75" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[本人]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="ac4eed04-e770-4587-8415-0eca8b81ca1f" x="526" y="16" width="75" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[家族]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<element kind="staticText" uuid="85863ebe-f8fd-4168-9615-0290c89c57bb" x="601" y="0" width="74" height="32" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ニーズ充足度]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="91225328-468c-4754-9da8-a96938017063" x="675" y="0" width="74" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 対応]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="frame" uuid="9bfd6f00-b8fe-43c8-902c-9a345e7a1ff6" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="262" height="32" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<element kind="staticText" uuid="a208fe3c-4744-4346-b31e-d794d0d0b42d" mode="Opaque" x="0" y="0" width="92" height="32" backcolor="rgba(250, 9, 5, 0.0)" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[生活全般の解決
すべき課題]]></text>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
									</element>
									<element kind="staticText" uuid="34aa2716-97d6-4c51-8303-4acacfd69ff9" x="92" y="0" width="85" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[短期目標]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box>
											<leftPen lineWidth="1.0"/>
										</box>
									</element>
									<element kind="staticText" uuid="d33c1898-0ed2-4fec-81f7-d149d73e83e2" x="177" y="0" width="85" height="32" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[サービス内容]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box>
											<leftPen lineWidth="0.8"/>
										</box>
									</element>
									<box>
										<leftPen lineWidth="1.6"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="frame" uuid="5cea5e0a-670c-4ef3-a078-08974734c103" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="262" height="32" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<element kind="staticText" uuid="4416c97d-582f-45f9-8f7b-e22f3a37a7d8" mode="Opaque" x="0" y="0" width="76" height="32" backcolor="rgba(250, 9, 5, 0.0)" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[生活全般の解決
すべき課題]]></text>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="staticText" uuid="a95ee4f3-b17a-45f5-9276-a997a717947f" x="76" y="0" width="62" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[長期目標]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.75"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="staticText" uuid="72c9ca13-16ec-4b31-9c9a-8da7851e6974" x="138" y="0" width="62" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[短期目標]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="staticText" uuid="39a0c985-517d-4af4-a591-a95aeb1d6aa9" x="200" y="0" width="62" height="32" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[サービス
内容]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.8"/>
										</box>
									</element>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="1.6"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</columnHeader>
						<columnFooter height="10" rowSpan="1">
							<element kind="frame" uuid="4f70a100-16ab-4c0d-91b2-2f4868e1c585" positionType="Float" stretchType="NoStretch" x="0" y="0" width="749" height="10" backcolor="#FFFFFF">
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="1.6" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</columnFooter>
						<detailCell height="72" style="Table_TD">
							<element kind="frame" uuid="01cd4038-dbce-45cc-bc09-c56d89a077d8" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="749" height="72">
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<element kind="frame" uuid="27465e77-87f2-4279-9712-c0b5ce0316d1" stretchType="ContainerHeight" x="262" y="0" width="65" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="70da5c0f-0460-4561-9897-bf8f52417505" stretchType="NoStretch" mode="Transparent" x="0" y="0" width="65" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true">
										<expression><![CDATA[$F{kakuninCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5">
											<pen lineWidth="0.4"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
											<rightPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="textField" uuid="168abeb4-472c-4eb1-847c-e07ec5087c01" stretchType="ContainerHeight" x="0" y="32" width="65" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kakuninKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3"/>
									</element>
									<element kind="textField" uuid="bfbe0517-6aeb-4e97-b5d4-f79fd718b6f0" stretchType="ContainerHeight" x="0" y="32" width="65" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[($P{bangouFlg}==0&&$P{choukiFlg}==0)?false:true]]></printWhenExpression>
										<expression><![CDATA[$F{kakuninKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5"/>
									</element>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="81da81b4-6ccd-4732-a267-43282dd6b111" stretchType="ContainerHeight" x="327" y="0" width="63" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="1c6067cf-2a80-4932-9fd1-22c60832236a" stretchType="NoStretch" x="0" y="0" width="63" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{houhouCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="78264041-e56d-4bfc-8eb1-5f3306b177d2" stretchType="ContainerHeight" x="0" y="32" width="63" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{houhouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<leftPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="textField" uuid="abde2303-3af3-4c3e-8823-7709e13b6d47" stretchType="ContainerHeight" x="0" y="32" width="63" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[($P{bangouFlg}==0&&$P{choukiFlg}==0)?false:true]]></printWhenExpression>
										<expression><![CDATA[$F{houhouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.0"/>
										</box>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="b147f39c-bfe1-40b4-bef9-86e2a10b34e0" stretchType="ContainerHeight" x="390" y="0" width="61" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="ee18c799-c191-4a1c-a2c8-69d4ff0209cc" stretchType="ContainerHeight" x="0" y="0" width="61" height="32" markup="html" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[!$F{kakuninYmdYY}.equals("")]]></printWhenExpression>
										<expression><![CDATA[$F{kakuninYmdGG}+
"<font style='font-size:10pt'>"+$F{kakuninYmdYY}+"</font>"+
"/"+
"<font style='font-size:10pt'>"+$F{kakuninYmdMM}+"</font>"+
"/"+
"<font style='font-size:10pt'>"+$F{kakuninYmdDD}+"</font>"]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="2" leftPadding="1" rightPadding="1">
											<leftPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0"/>
										</box>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="7121011a-0f6d-443e-b336-2e047c3ed191" stretchType="ContainerHeight" x="451" y="0" width="75" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="a929218c-b0c0-4bb3-bf77-b20e176237c6" stretchType="NoStretch" x="0" y="0" width="75" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{ikenHonCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5">
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="1570ca3b-d49a-4859-8aa2-d246a1bcc645" stretchType="ContainerHeight" mode="Transparent" x="0" y="32" width="75" height="40" backcolor="#FFFFFF" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{ikenHonKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2"/>
									</element>
									<element kind="textField" uuid="bcb9a66c-2c9e-4698-b9b0-f50202a6b71b" stretchType="ContainerHeight" mode="Transparent" x="0" y="32" width="75" height="40" backcolor="#FFFFFF" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[($P{bangouFlg}==0&&$P{choukiFlg}==0)?false:true]]></printWhenExpression>
										<expression><![CDATA[$F{ikenHonKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5"/>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="1f8a3196-484f-41f4-9ad8-6bdb69cd3aba" stretchType="ContainerHeight" x="526" y="0" width="75" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<element kind="textField" uuid="ce710dc8-2b0d-44e8-b868-e99c06cec37c" stretchType="NoStretch" x="0" y="0" width="75" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{ikenKazCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5">
											<leftPen lineWidth="0.75"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="textField" uuid="cfde75b9-9a5b-4d71-8c7d-f79b2cc767ae" stretchType="ContainerHeight" x="0" y="32" width="75" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{ikenKazKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="textField" uuid="777bdbdb-7df9-4cfd-ab45-52f065b49c40" stretchType="ContainerHeight" x="0" y="32" width="75" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[($P{bangouFlg}==0&&$P{choukiFlg}==0)?false:true]]></printWhenExpression>
										<expression><![CDATA[$F{ikenKazKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5">
											<leftPen lineWidth="0.75"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="32ec69f5-a420-4123-a0d5-f1a09cf2d739" stretchType="ContainerHeight" x="601" y="0" width="74" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="5628efac-467b-4adf-b633-c7d24007b7cb" stretchType="NoStretch" x="0" y="0" width="74" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{jusokuCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="12d20fab-119b-4bca-abaa-411adbeee8d5" stretchType="ContainerHeight" x="0" y="32" width="74" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{jusokuKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2"/>
									</element>
									<element kind="textField" uuid="f5aee348-04bd-4cc4-93d3-1d58bdfb3d01" stretchType="ContainerHeight" x="0" y="32" width="74" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[($P{bangouFlg}==0&&$P{choukiFlg}==0)?false:true]]></printWhenExpression>
										<expression><![CDATA[$F{jusokuKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3"/>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="600af77e-f233-4aa6-aa75-ec7942ce3028" stretchType="ContainerHeight" x="675" y="0" width="74" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="b97bcd30-a324-4f4e-97c4-c97c28be1d86" stretchType="NoStretch" x="0" y="0" width="74" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{taiouCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.75"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="29a406e3-ef14-49c5-beea-d597fb84cf2a" stretchType="ContainerHeight" x="0" y="32" width="74" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{taiouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="textField" uuid="784c52d8-9c27-4b34-b655-6335e118746c" stretchType="ContainerHeight" x="0" y="32" width="74" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[($P{bangouFlg}==0&&$P{choukiFlg}==0)?false:true]]></printWhenExpression>
										<expression><![CDATA[$F{taiouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.75"/>
										</box>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="30bdc641-4b36-4db0-8856-2cffb2b03306" stretchType="ContainerHeight" x="92" y="0" width="85" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0&&$P{bangouFlg}==1]]></printWhenExpression>
									<expression><![CDATA[$F{tankiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="10">
										<leftPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
								</element>
								<element kind="frame" uuid="93596a90-de18-48bf-a710-b1c0a294642d" stretchType="ContainerHeight" x="0" y="0" width="92" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="7155a693-5151-48da-90ce-dc5e038d2291" stretchType="ContainerHeight" x="16" y="0" width="76" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="10">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.75" lineColor="#030303"/>
										</box>
									</element>
									<element kind="textField" uuid="0b2d2347-765a-4c2e-99d8-485aa50b6912" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="0">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</element>
									<box>
										<pen lineWidth="1.5"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="d0bbc9f6-5d8b-4b0b-bd06-839eb7edfab2" stretchType="ContainerHeight" x="177" y="0" width="85" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="74f2cde1-4166-4f62-a113-ac3ef810c403" stretchType="ContainerHeight" x="16" y="0" width="69" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="2788d8e5-1596-44de-9edf-8bdbec00f7df" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{servNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="0">
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.4" lineStyle="Dashed" lineColor="#000000"/>
										</box>
									</element>
									<box>
										<leftPen lineWidth="0.75" lineStyle="Solid"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="1eaff777-1ed5-44cd-86fc-1669e26790bd" stretchType="ContainerHeight" x="0" y="0" width="92" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="883e6077-9516-4b1e-a6d1-8313331be96b" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="10">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0" lineColor="#F23430"/>
										</box>
									</element>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="7d96e37d-0808-4d80-be95-cc23692d2927" stretchType="ContainerHeight" x="177" y="0" width="85" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="3e518bda-ddfc-4f52-9cc1-dfcd17388dc5" stretchType="ContainerHeight" x="0" y="0" width="85" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="textField" uuid="eddd2862-3b34-4ed0-b1b2-395f79feb5af" stretchType="ContainerHeight" x="76" y="0" width="62" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<expression><![CDATA[$F{choukiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="c36047aa-7e71-43d9-b4ef-608d23a248ef" stretchType="ContainerHeight" x="138" y="0" width="62" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<expression><![CDATA[$F{tankiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="frame" uuid="a12ff92d-4b98-44c7-8408-ecb94cf3774f" stretchType="ContainerHeight" x="0" y="0" width="76" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="34846973-5d5a-4f61-a638-e488bf517279" stretchType="ContainerHeight" x="16" y="0" width="60" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="textField" uuid="53845ab4-783e-42f9-9b00-9247295ad8db" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="0">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.4" lineStyle="Dashed" lineColor="#000000"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="99d69891-7255-4ed2-b9e6-c7c00791f553" stretchType="ContainerHeight" x="200" y="0" width="62" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="5432a766-0e26-4457-a869-4bf1272dbd09" stretchType="ContainerHeight" x="15" y="0" width="47" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="0">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="f7b91584-57be-40eb-b4d3-594e3e6fba5f" stretchType="ContainerHeight" x="0" y="0" width="15" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{servNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="0">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<rightPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="88b83253-18c3-46f0-a82b-969245d7e1fc" stretchType="ContainerHeight" x="0" y="0" width="76" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="08676ee6-a444-4d00-afe1-01b524130049" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="10">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="c4701701-b364-4f5f-bbae-15651022b1df" stretchType="ContainerHeight" x="200" y="0" width="62" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="7610978e-3c22-40c6-8559-ec8eedc29017" stretchType="ContainerHeight" x="0" y="0" width="62" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="f623f31a-5acc-4ace-ac4b-01700a209459" stretchType="ContainerHeight" x="262" y="0" width="65" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="9147ee1f-2059-4890-b5ef-dd601f36d5b2" stretchType="ContainerHeight" x="327" y="0" width="63" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="b2a8706e-6b3b-484d-bb6d-f2b185775a30" stretchType="ContainerHeight" x="390" y="0" width="61" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="3e4e652c-8aa9-46a6-ac3a-2e9e2b9996c2" stretchType="ContainerHeight" x="451" y="0" width="75" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="46b446bf-1690-4353-9cdc-e650d9a08651" stretchType="ContainerHeight" x="526" y="0" width="75" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="5fbf00d3-106a-4fd8-b8b8-9646de1f7b2d" stretchType="ContainerHeight" x="601" y="0" width="74" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="2031b859-c566-4950-8fa1-93c7b465d477" stretchType="ContainerHeight" x="675" y="0" width="74" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="ab574e0c-e706-4d6a-b320-e810ce208958" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="1.5"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="1174f101-638d-44fb-b67a-94eec84876b5" stretchType="ContainerHeight" x="177" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75" lineStyle="Solid"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="844a8db4-8c83-4aad-aa68-5dfc55b67bfb" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="660f0e81-226b-45fd-b033-ab4d55f34d89" stretchType="ContainerHeight" x="177" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="17f30291-a152-4312-96a3-462b5571ede9" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="468ea9c8-41a0-4967-ac4b-9f3fe28d8764" stretchType="ContainerHeight" x="200" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="aadb511a-e079-430f-9b9b-f08daa980e90" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="ce728ea0-ec03-4afb-b80b-d73ee7744446" stretchType="ContainerHeight" x="200" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="6fcf9d73-e497-4a43-b715-7d8f7d6be146" stretchType="ContainerHeight" x="76" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="172f3217-c8ad-42ea-8751-0e150f873570" stretchType="ContainerHeight" x="138" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="13a7b7e8-1661-4f1e-910b-c6d64205bd0c" stretchType="ContainerHeight" x="92" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="5dc8c342-8f99-471a-b5e7-533a7822b688" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<element kind="frame" uuid="472e035b-47df-4892-89e1-c089e23636e0" stretchType="ContainerHeight" x="177" y="0" width="16" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<element kind="frame" uuid="86198c62-c691-42b7-b234-584a1bbb0c33" stretchType="ContainerHeight" x="200" y="0" width="15" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<element kind="frame" uuid="05f2aa4e-3629-452f-b92a-e5560cc3a143" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="1.5"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="6a301181-bac8-4b0b-9047-6a7651881914" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="8a8272e4-21e9-47e4-8fac-b0bfd542993b" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="4587b0fa-74a1-4d53-a5ca-041392b34df0" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="c2cebebf-19fd-4cb5-9c81-be8a705c0e73" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="e47e039c-1cb9-4d9e-b2ad-e878a6235261" stretchType="ContainerHeight" x="76" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1&&$F{choukiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="a5e3d1e9-7494-4539-a312-8332e4de67ca" stretchType="ContainerHeight" x="138" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1&&$F{tankiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="49902bd2-7a57-4e85-a7b7-df04ae0affc4" stretchType="ContainerHeight" x="92" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0&&$F{tankiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="38ce3e34-2aae-45e1-b5fb-69b81ab46813" stretchType="ContainerHeight" x="92" y="0" width="85" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0&&$P{bangouFlg}==0]]></printWhenExpression>
									<expression><![CDATA[$F{tankiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
				<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
				<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="117">
			<printWhenExpression><![CDATA[$F{ichiranFlg}==1&&$F{mojiSize}.equals("11")]]></printWhenExpression>
			<element kind="component" uuid="ada7a368-ea79-4c85-b6cb-73e01b069547" x="0" y="2" width="749" height="115">
				<component kind="table">
					<datasetRun uuid="d1b73f1f-**************-b63d706fe0c1" subDataset="Dataset1">
						<dataSourceExpression><![CDATA[$F{list}]]></dataSourceExpression>
						<parameter name="bangouFlg">
							<expression><![CDATA[$F{bangouFlg}]]></expression>
						</parameter>
						<parameter name="choukiFlg">
							<expression><![CDATA[$F{choukiFlg}]]></expression>
						</parameter>
						<parameter name="ichiranFlg">
							<expression><![CDATA[$F{ichiranFlg}]]></expression>
						</parameter>
						<parameter name="mojiSize">
							<expression><![CDATA[$F{mojiSize}]]></expression>
						</parameter>
					</datasetRun>
					<column kind="single" uuid="ebc9aa04-4f8d-4260-990b-f5e33c41752c" width="749">
						<columnHeader height="32" rowSpan="1" style="Style1">
							<element kind="frame" uuid="f009ff2a-0315-40d0-bfd5-9ab3a0b0c069" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="749" height="32" printWhenDetailOverflows="true">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<element kind="staticText" uuid="137962f4-e4d5-42bd-b970-1826b39cf29b" x="262" y="0" width="189" height="16" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[サービスの実行確認および確認方法]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="37cd2c6a-47f4-4736-ac6f-6db6c92bc8c6" x="262" y="16" width="65" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[実行確認]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<element kind="staticText" uuid="abe332db-5095-4aca-8ef6-ea7fd1a9ff69" x="327" y="16" width="63" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 確認方法]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="8bd1cbed-6b8c-409b-8bcd-9929b6623fb3" x="390" y="16" width="61" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 確認期日]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="staticText" uuid="7f9a64ee-3a37-49d4-99f0-b879c4236eb2" x="451" y="0" width="150" height="16" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[利用者本人・家族の意見・要望]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="36a5d24b-2985-4985-835d-42a1f03eec99" x="451" y="16" width="75" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[本人]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="e159c100-8ced-46f8-9edb-3d8126023c56" x="526" y="16" width="75" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[家族]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<element kind="staticText" uuid="a76e78ac-bb36-4cce-ae53-350281aee700" x="601" y="0" width="74" height="32" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ニーズ充足度]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="5cb958e6-05b0-4db8-b4b8-83719a3f41d7" x="675" y="0" width="74" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 対応]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="frame" uuid="fc11736a-e810-4e1d-90ff-ac0782a6c85c" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="262" height="32" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<element kind="staticText" uuid="aac1e2ee-7c3b-4f7a-aeeb-67d6f6bb5293" mode="Opaque" x="0" y="0" width="92" height="32" backcolor="rgba(250, 9, 5, 0.0)" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[生活全般の解決
すべき課題]]></text>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
									</element>
									<element kind="staticText" uuid="925e896a-20eb-47a1-84c9-f8c77aa847ea" x="92" y="0" width="85" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[短期目標]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box>
											<leftPen lineWidth="1.0"/>
										</box>
									</element>
									<element kind="staticText" uuid="fd62f65c-715d-4bdc-a5fe-0024594bd951" x="177" y="0" width="85" height="32" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[サービス内容]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box>
											<leftPen lineWidth="0.8"/>
										</box>
									</element>
									<box>
										<leftPen lineWidth="1.6"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="frame" uuid="c8c0386f-b583-4a48-a9b4-a94cff83b64c" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="262" height="32" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<element kind="staticText" uuid="1109a02e-3fc7-45bb-941b-bb9c89551a9b" mode="Opaque" x="0" y="0" width="76" height="32" backcolor="rgba(250, 9, 5, 0.0)" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[生活全般の解決
すべき課題]]></text>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="staticText" uuid="81044344-633a-48fd-aba0-ca67c584d80a" x="76" y="0" width="62" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[長期目標]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.75"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="staticText" uuid="ea3b6a18-7ea6-4ac5-aa98-ad8c5b2a5a12" x="138" y="0" width="62" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[短期目標]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="staticText" uuid="1fff8d92-20a5-4cfe-8624-88561bf280d8" x="200" y="0" width="62" height="32" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[サービス
内容]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.8"/>
										</box>
									</element>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="1.6"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.75"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</columnHeader>
						<columnFooter height="10" rowSpan="1">
							<element kind="frame" uuid="da910c31-15c9-47ac-9f72-0317114651cf" positionType="Float" stretchType="NoStretch" x="0" y="0" width="749" height="10" backcolor="#FFFFFF">
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="1.6" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</columnFooter>
						<detailCell height="72" style="Table_TD">
							<element kind="frame" uuid="f75cf445-cd6d-4515-bb44-7aa9e8ec0733" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="749" height="72">
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<element kind="frame" uuid="b19038e4-58b1-4372-8589-2bba05bc89b3" stretchType="ContainerHeight" x="262" y="0" width="65" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="4993181a-dca3-4bff-b130-cd1af979fa71" stretchType="NoStretch" mode="Transparent" x="0" y="0" width="65" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true">
										<expression><![CDATA[$F{kakuninCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5">
											<pen lineWidth="0.4"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
											<rightPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="textField" uuid="99142ced-68a2-4777-bc75-4644f2e24fec" stretchType="ContainerHeight" x="0" y="32" width="65" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{kakuninKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3"/>
									</element>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="022ccea9-dc71-40cf-9af8-8fa6524aac11" stretchType="ContainerHeight" x="327" y="0" width="63" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="461e577c-6e41-49d2-bda8-94742911f2db" stretchType="NoStretch" x="0" y="0" width="63" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{houhouCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="c5bece22-2cfe-4e75-9f77-8d8a584ad054" stretchType="ContainerHeight" x="0" y="32" width="63" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{houhouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<leftPen lineWidth="0.0"/>
										</box>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="0777b718-6441-4731-a49b-86aa74832fb1" stretchType="ContainerHeight" x="390" y="0" width="61" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="0c013e70-5874-4148-a47e-339d30a6b68c" stretchType="ContainerHeight" x="0" y="0" width="61" height="32" markup="html" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[!$F{kakuninYmdYY}.equals("")]]></printWhenExpression>
										<expression><![CDATA[$F{kakuninYmdGG}+
"<font style='font-size:10pt'>"+$F{kakuninYmdYY}+"</font>"+
"/"+
"<font style='font-size:10pt'>"+$F{kakuninYmdMM}+"</font>"+
"/"+
"<font style='font-size:10pt'>"+$F{kakuninYmdDD}+"</font>"]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="2" leftPadding="1" rightPadding="1">
											<leftPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0"/>
										</box>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="8f2157e2-0cf1-45f8-a27e-e0df7c0412a8" stretchType="ContainerHeight" x="451" y="0" width="75" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="9ca8b2f0-2341-445a-aeac-35cb8019ec71" stretchType="NoStretch" x="0" y="0" width="75" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{ikenHonCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5">
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="9645e728-a791-491e-9ccc-89070007b589" stretchType="ContainerHeight" mode="Transparent" x="0" y="32" width="75" height="40" backcolor="#FFFFFF" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{ikenHonKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2"/>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="8c7fca69-4e94-467a-9280-f06136c1a8a4" stretchType="ContainerHeight" x="526" y="0" width="75" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<element kind="textField" uuid="bddfc1f2-12a5-4984-9ba2-056ff003ce58" stretchType="NoStretch" x="0" y="0" width="75" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{ikenKazCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5">
											<leftPen lineWidth="0.75"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="textField" uuid="c2c11b6e-481c-4882-940a-102dcc49cc19" stretchType="ContainerHeight" x="0" y="32" width="75" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{ikenKazKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="e62f6afa-6d76-467c-b77d-0efc73366c83" stretchType="ContainerHeight" x="601" y="0" width="74" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="d5b3496e-f375-49c1-88d6-3745c151c9a6" stretchType="NoStretch" x="0" y="0" width="74" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{jusokuCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="51117254-329e-4215-b1f5-11a9ef3a7615" stretchType="ContainerHeight" x="0" y="32" width="74" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{jusokuKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2"/>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="aed42e8a-edc0-414d-9d39-5dae870dca05" stretchType="ContainerHeight" x="675" y="0" width="74" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="abf9aa34-cb0f-415c-9662-5996bdf3c7e0" stretchType="NoStretch" x="0" y="0" width="74" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{taiouCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.75"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="ab0db5e9-d7de-4514-8b3c-2bf72800a792" stretchType="ContainerHeight" x="0" y="32" width="74" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{taiouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
										</box>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="a36e3af0-4f18-4d7f-b435-faf570695bb5" stretchType="ContainerHeight" x="92" y="0" width="85" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0]]></printWhenExpression>
									<expression><![CDATA[$F{tankiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
										<leftPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
								</element>
								<element kind="frame" uuid="6363dfed-2b12-4003-94ef-9adb1b5293c9" stretchType="ContainerHeight" x="0" y="0" width="92" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="88420f8f-af2d-4b5c-b1be-43f99fb4437a" stretchType="ContainerHeight" x="16" y="0" width="76" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.75" lineColor="#030303"/>
										</box>
									</element>
									<element kind="textField" uuid="*************-4ed6-88ee-dde843eb687f" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="0">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</element>
									<box>
										<pen lineWidth="1.5"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="4be25920-ace1-4aba-8912-49b97ce30c6b" stretchType="ContainerHeight" x="177" y="0" width="85" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="b2bf77cf-e375-4801-8f24-f4ea43c2009a" stretchType="ContainerHeight" x="16" y="0" width="69" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="e772c7a9-1fd3-4a1a-9638-92d1d743c46c" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{servNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="0">
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.4" lineStyle="Dashed" lineColor="#000000"/>
										</box>
									</element>
									<box>
										<leftPen lineWidth="0.75" lineStyle="Solid"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="ac385e3f-853c-4eb2-b7cc-e3bcdd29e9dd" stretchType="ContainerHeight" x="0" y="0" width="92" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="bf2339f1-35b9-4bc0-8c34-66443272fab9" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="10">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0" lineColor="#F23430"/>
										</box>
									</element>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="aa83e8a1-b67b-48fc-9ff0-28e3c5757330" stretchType="ContainerHeight" x="177" y="0" width="85" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="9bbdb26c-3378-4b16-9302-684f3015f9f3" stretchType="ContainerHeight" x="0" y="0" width="85" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="textField" uuid="fe06c621-8d08-4d5c-9536-f47508ecf061" stretchType="ContainerHeight" x="76" y="0" width="62" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<expression><![CDATA[$F{choukiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="973f6fad-eb24-4375-8f17-c4758af91521" stretchType="ContainerHeight" x="138" y="0" width="62" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<expression><![CDATA[$F{tankiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="frame" uuid="1d140dec-910f-41a1-bb7d-24ed89e2d278" stretchType="ContainerHeight" x="0" y="0" width="76" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="d15e55dc-ad69-42d5-a146-849077e8cc7e" stretchType="ContainerHeight" x="16" y="0" width="60" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="textField" uuid="1b90bb47-1c29-4a26-a804-41c9f9480eb8" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="0">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.4" lineStyle="Dashed" lineColor="#000000"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="36009cca-924a-4060-8bf3-d147e7eed217" stretchType="ContainerHeight" x="200" y="0" width="62" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="35c5cb76-87e1-4a96-b9b0-388b42954e50" stretchType="ContainerHeight" x="15" y="0" width="47" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="0">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="f5d68f4f-a363-4613-8f33-b6cfa545e237" stretchType="ContainerHeight" x="0" y="0" width="15" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{servNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="0">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<rightPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="da470c6e-ee7c-4ec5-ab08-5a32c081494d" stretchType="ContainerHeight" x="0" y="0" width="76" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="3afd4f2e-5c61-4bbb-ae4a-b0c3d100a893" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="89d898e2-aa97-4e03-92a2-650218f21371" stretchType="ContainerHeight" x="200" y="0" width="62" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="9a452f70-629c-4a37-9b8d-539e5201ce6d" stretchType="ContainerHeight" x="0" y="0" width="62" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="027f1da4-13eb-4f3c-9807-a560845e3870" stretchType="ContainerHeight" x="262" y="0" width="65" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="caf405c5-f802-4279-8023-9cb70a759385" stretchType="ContainerHeight" x="327" y="0" width="63" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="8e4d473e-458d-4a16-b95c-eee5609208dc" stretchType="ContainerHeight" x="390" y="0" width="61" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="7cd8b069-9248-422b-a595-cbd194420597" stretchType="ContainerHeight" x="451" y="0" width="75" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="a1ab602a-9e4a-4db5-a027-67944583f419" stretchType="ContainerHeight" x="526" y="0" width="75" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="d5670bd2-fee2-4394-b89b-26a81c1bb3de" stretchType="ContainerHeight" x="601" y="0" width="74" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="22de17c9-474c-4e45-b928-d37f5b7da234" stretchType="ContainerHeight" x="675" y="0" width="74" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="aab0f61d-741b-4e76-b9ec-9e50ceea7d8e" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="1.5"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="7688c9ff-c91b-4cf2-9c7d-b63a8ba394dc" stretchType="ContainerHeight" x="177" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75" lineStyle="Solid"/>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="c0d50776-4a0d-43ed-991f-092ee6b7de24" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="c4ed1235-775c-4419-8069-3314a86fce4f" stretchType="ContainerHeight" x="177" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="3de1a9fd-8ebb-4aba-82d0-cd6c24ec07c8" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="05f74c72-4103-4f11-957e-e8afa87832b9" stretchType="ContainerHeight" x="200" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="9aa8871f-6d55-48f0-8e91-de2a4a132d48" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="637c2b4e-2df0-46d9-b06c-4e020262a7e1" stretchType="ContainerHeight" x="200" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="ccecb565-8ccd-4027-95f9-b0e709188aaa" stretchType="ContainerHeight" x="76" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="e6b225f2-d769-4f77-b3c9-280d9249ae3e" stretchType="ContainerHeight" x="138" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="aa09c5a9-7a83-413c-8a47-9712d7b28487" stretchType="ContainerHeight" x="92" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="538a2e40-a15f-44cf-b027-1f922f835242" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<element kind="frame" uuid="9726564d-5ecc-422b-a494-3d7e4cb68870" stretchType="ContainerHeight" x="177" y="0" width="16" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<element kind="frame" uuid="3e4e0ec7-7bfc-4ecb-954c-d9d0ca14a808" stretchType="ContainerHeight" x="200" y="0" width="15" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<element kind="frame" uuid="d73bd2bb-e0eb-4681-84db-2d2bb2c82d92" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="1.5"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="76619b15-f43e-42a8-8e60-67dde7c5345e" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="1e1cef26-ce7d-4ebb-8306-ca58f0b5d7de" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="63953c99-8611-4580-a356-1a35785cd8c1" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="babdc9f8-e21a-46b8-b1f6-d058910b2edc" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="a98c41d2-e434-4d78-b670-8204ecd251c9" stretchType="ContainerHeight" x="76" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1&&$F{choukiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="f1f30691-e915-4bc4-9c69-671fa3ad9d03" stretchType="ContainerHeight" x="138" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1&&$F{tankiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="234b4a1f-7338-4eaf-8f32-1bc097444351" stretchType="ContainerHeight" x="92" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0&&$F{tankiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
				<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
				<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="117">
			<printWhenExpression><![CDATA[$F{ichiranFlg}==1&&$F{mojiSize}.equals("12")]]></printWhenExpression>
			<element kind="component" uuid="217b8730-37f6-4a90-a1ea-15c6f008faae" x="0" y="2" width="749" height="115">
				<component kind="table">
					<datasetRun uuid="4e3909bd-be7c-447d-b69b-919838473d40" subDataset="Dataset1">
						<dataSourceExpression><![CDATA[$F{list}]]></dataSourceExpression>
						<parameter name="bangouFlg">
							<expression><![CDATA[$F{bangouFlg}]]></expression>
						</parameter>
						<parameter name="choukiFlg">
							<expression><![CDATA[$F{choukiFlg}]]></expression>
						</parameter>
						<parameter name="ichiranFlg">
							<expression><![CDATA[$F{ichiranFlg}]]></expression>
						</parameter>
						<parameter name="mojiSize">
							<expression><![CDATA[$F{mojiSize}]]></expression>
						</parameter>
					</datasetRun>
					<column kind="single" uuid="0e65078c-abab-4258-bdeb-7f4057b57428" width="749">
						<columnHeader height="32" rowSpan="1" style="Style1">
							<element kind="frame" uuid="7b216bc7-c0fe-4005-9567-a180d57a582f" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="749" height="32" printWhenDetailOverflows="true">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<element kind="staticText" uuid="186d3911-76da-4c71-8bf9-80894f760203" x="262" y="0" width="189" height="16" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[サービスの実行確認および確認方法]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="21fa61a6-c8b5-48c3-8488-69aabd1064a1" x="262" y="16" width="65" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[実行確認]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<element kind="staticText" uuid="bfa3eb5b-064c-4294-9673-7d1f36a96bc0" x="327" y="16" width="63" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 確認方法]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="91f8617f-fb6e-4274-b083-fe588b7d9413" x="390" y="16" width="61" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 確認期日]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="staticText" uuid="a5a9c13f-c69a-40c0-b1bf-9d45084db046" x="451" y="0" width="150" height="16" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[利用者本人・家族の意見・要望]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="2adb8fbb-a95d-4b1c-8157-5f1e65e25320" x="451" y="16" width="75" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[本人]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="19e2c5fa-2d04-4279-a6bb-e1600b2c6194" x="526" y="16" width="75" height="16" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[家族]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<element kind="staticText" uuid="4dbd6c07-64cf-4c44-bfd2-efcbd5f2261a" x="601" y="0" width="74" height="32" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ニーズ充足度]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="f73be8f2-4d6a-42eb-80de-4ff59e6e9622" x="675" y="0" width="74" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 対応]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="frame" uuid="ab66bf9e-2319-4fec-b201-a87978155744" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="262" height="32" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<element kind="staticText" uuid="94ddd7db-c07b-4217-8e31-bdc414587b21" mode="Opaque" x="0" y="0" width="92" height="32" backcolor="rgba(250, 9, 5, 0.0)" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[生活全般の解決
すべき課題]]></text>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
									</element>
									<element kind="staticText" uuid="46e3b44c-6e40-4323-ab34-5e395b835313" x="92" y="0" width="85" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[短期目標]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box>
											<leftPen lineWidth="1.0"/>
										</box>
									</element>
									<element kind="staticText" uuid="657a72b8-73db-4b92-a483-c51e118f1f72" x="177" y="0" width="85" height="32" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[サービス内容]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box>
											<leftPen lineWidth="0.8"/>
										</box>
									</element>
									<box>
										<leftPen lineWidth="1.6"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="frame" uuid="bdc2c2a2-2912-42ff-a5ca-a3812fd9bf08" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="262" height="32" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<element kind="staticText" uuid="865a2732-dcfc-40eb-a512-eaba406b6364" mode="Opaque" x="0" y="0" width="76" height="32" backcolor="rgba(250, 9, 5, 0.0)" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[生活全般の解決
すべき課題]]></text>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="staticText" uuid="ac7b1004-fd63-432c-ad74-afea0eb9c698" x="76" y="0" width="62" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[長期目標]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.75"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="staticText" uuid="17e0d05a-4944-4f52-8a5b-96cb8c2ae316" x="138" y="0" width="62" height="32" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[短期目標]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="staticText" uuid="23afd01d-45a6-4db8-bb64-01d720da4478" x="200" y="0" width="62" height="32" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[サービス
内容]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.8"/>
										</box>
									</element>
									<box>
										<pen lineWidth="0.25"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="1.6"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="1.5"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</columnHeader>
						<columnFooter height="10" rowSpan="1">
							<element kind="frame" uuid="dc5eacc5-897f-4d9b-aa2b-bc81d21ef804" positionType="Float" stretchType="NoStretch" x="0" y="0" width="749" height="10" backcolor="#FFFFFF">
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="1.6" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</columnFooter>
						<detailCell height="72" style="Table_TD">
							<element kind="frame" uuid="e3094c44-a798-4780-82c1-df2ce3338ed0" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="749" height="72">
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<element kind="frame" uuid="c023593d-7aa7-4603-bc2d-25388e289ac3" stretchType="ContainerHeight" x="262" y="0" width="65" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="83a49a17-ab1f-4d17-a23d-a95bb9736eb6" stretchType="NoStretch" mode="Transparent" x="0" y="0" width="65" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true">
										<expression><![CDATA[$F{kakuninCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5">
											<pen lineWidth="0.4"/>
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</element>
									<element kind="textField" uuid="e9ef03a3-21c0-47b2-8ab7-3b4bd858ed53" stretchType="ContainerHeight" x="0" y="32" width="65" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kakuninKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3"/>
									</element>
									<element kind="textField" uuid="af87f3f2-4ccf-4830-8f25-db691509d27f" stretchType="ContainerHeight" x="0" y="32" width="65" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[($P{bangouFlg}==0&&$P{choukiFlg}==0)?false:true]]></printWhenExpression>
										<expression><![CDATA[$F{kakuninKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5"/>
									</element>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="e5a59c2d-45ea-4cc2-85f7-995143c720bd" stretchType="ContainerHeight" x="327" y="0" width="63" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="851aaf16-3ce3-42ef-8b17-b63ab3785eb0" stretchType="NoStretch" x="0" y="0" width="63" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{houhouCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="7b60efbf-47eb-4d8e-9d11-c6816bbeb0c6" stretchType="ContainerHeight" x="0" y="32" width="63" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{houhouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<leftPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="textField" uuid="31eefd36-3807-4d51-89e0-652079bd0dff" stretchType="ContainerHeight" x="0" y="32" width="63" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[($P{bangouFlg}==0&&$P{choukiFlg}==0)?false:true]]></printWhenExpression>
										<expression><![CDATA[$F{houhouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.0"/>
										</box>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="db574eab-8f4f-43d8-8c35-56bd1baf2d66" stretchType="ContainerHeight" x="390" y="0" width="61" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="5aa5f77a-b641-4693-a970-525520d30af2" stretchType="ContainerHeight" x="0" y="0" width="61" height="32" markup="html" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[!$F{kakuninYmdYY}.equals("")]]></printWhenExpression>
										<expression><![CDATA[$F{kakuninYmdGG}+
"<font style='font-size:10pt'>"+$F{kakuninYmdYY}+"</font>"+
"/"+
"<font style='font-size:10pt'>"+$F{kakuninYmdMM}+"</font>"+
"/"+
"<font style='font-size:10pt'>"+$F{kakuninYmdDD}+"</font>"]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="2" leftPadding="1" rightPadding="1">
											<leftPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0"/>
										</box>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="3c3c59eb-d90b-4c25-af26-704da59405b7" stretchType="ContainerHeight" x="451" y="0" width="75" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="c12d28d0-18c5-4099-ba17-4afeff4d0f41" stretchType="NoStretch" x="0" y="0" width="75" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{ikenHonCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5">
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="1f3de862-bf3e-4542-bce4-a72b29dabd6f" stretchType="ContainerHeight" mode="Transparent" x="0" y="32" width="75" height="40" backcolor="#FFFFFF" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{ikenHonKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2"/>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="37dbe79a-7ec1-4520-9acc-2a79d2285c64" stretchType="ContainerHeight" x="526" y="0" width="75" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<element kind="textField" uuid="38261931-00bd-4e4a-9286-237de9384620" stretchType="NoStretch" x="0" y="0" width="75" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{ikenKazCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5">
											<leftPen lineWidth="0.75"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="textField" uuid="42c4fc57-ab2e-4761-89c7-779ab73597ea" stretchType="ContainerHeight" x="0" y="32" width="75" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{ikenKazKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="4d7857ba-ddf0-4a28-8991-50c319e80e4f" stretchType="ContainerHeight" x="601" y="0" width="74" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="71c8e963-c4bc-4187-8873-822cb46f47b4" stretchType="NoStretch" x="0" y="0" width="74" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{jusokuCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="2bc592b6-f17f-4de4-9d8e-ea8b74a38af0" stretchType="ContainerHeight" x="0" y="32" width="74" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{jusokuKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2"/>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="22eb4691-9b92-4df8-813e-bbae2bd4cb59" stretchType="ContainerHeight" x="675" y="0" width="74" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="03323da8-9705-4fdf-b21c-648e7a0cce48" stretchType="NoStretch" x="0" y="0" width="74" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{taiouCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.75"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="098a471e-53ea-449d-9744-382aa9f0e53a" stretchType="ContainerHeight" x="0" y="32" width="74" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{taiouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
										</box>
									</element>
									<box>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="ffae6736-29d5-4764-a5b1-961af04e12a3" stretchType="ContainerHeight" x="92" y="0" width="85" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0]]></printWhenExpression>
									<expression><![CDATA[$F{tankiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
										<leftPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
								</element>
								<element kind="frame" uuid="70f809e3-02e9-47f6-8714-ed4dd4338e46" stretchType="ContainerHeight" x="0" y="0" width="92" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="904cfa18-234b-43e7-ae31-661f0a509b24" stretchType="ContainerHeight" x="16" y="0" width="76" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.75" lineColor="#030303"/>
										</box>
									</element>
									<element kind="textField" uuid="21408a2d-722f-43ef-a8d7-dd9cf32a2898" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="0">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</element>
									<box>
										<pen lineWidth="1.5"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="f7d4a301-8ee3-45ed-a789-ae0d8dd4d960" stretchType="ContainerHeight" x="177" y="0" width="85" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="12d07409-700a-49b8-acfe-e0979b1df16d" stretchType="ContainerHeight" x="16" y="0" width="69" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="8ea2d622-5ac0-43a5-a1f0-9f4432b57c02" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{servNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="0">
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.4" lineStyle="Dashed" lineColor="#000000"/>
										</box>
									</element>
									<box>
										<leftPen lineWidth="0.75" lineStyle="Solid"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="ce8ac1c2-2621-42ab-9aba-7279d77c607a" stretchType="ContainerHeight" x="0" y="0" width="92" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="c8331777-cc34-472f-9cf5-f8246227e397" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="10">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0" lineColor="#F23430"/>
										</box>
									</element>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="06e7cc40-7fe3-48e6-97fa-5b17604cfa96" stretchType="ContainerHeight" x="177" y="0" width="85" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="6b5d96f0-fdec-42aa-91ca-7ad57d536892" stretchType="ContainerHeight" x="0" y="0" width="85" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="textField" uuid="0fb60fee-51b9-4f2d-99e6-fc1d9fcf99b4" stretchType="ContainerHeight" x="76" y="0" width="62" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<expression><![CDATA[$F{choukiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="fbe91760-e61d-4bee-b41a-1908ea98e3df" stretchType="ContainerHeight" x="138" y="0" width="62" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<expression><![CDATA[$F{tankiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.8"/>
									</box>
								</element>
								<element kind="frame" uuid="7698d23c-a6ce-4a60-9d6e-e9a44b51fbbd" stretchType="ContainerHeight" x="0" y="0" width="76" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="40befb78-a5a2-4ba3-aef2-45adae1f653d" stretchType="ContainerHeight" x="16" y="0" width="60" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="textField" uuid="ee1fa2a0-339f-4bca-94a9-f22a17ca8330" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="0">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.4" lineStyle="Dashed" lineColor="#000000"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="7ee9a4c1-9fa0-49eb-a848-2516e0a6db2c" stretchType="ContainerHeight" x="200" y="0" width="62" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="f89bdf90-256a-4d3f-aada-66ac03549b98" stretchType="ContainerHeight" x="15" y="0" width="47" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="0">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="a656a355-6286-43ec-8756-dec9266437de" stretchType="ContainerHeight" x="0" y="0" width="15" height="72" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
										<expression><![CDATA[$F{servNo}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="0">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<rightPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="f3a1195f-a024-412b-9faa-6eddc93a5876" stretchType="ContainerHeight" x="0" y="0" width="76" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="94e8f9e4-a203-4de3-9e16-342fdb3d4589" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kadaiKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5">
											<pen lineWidth="0.75"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
											<bottomPen lineWidth="0.0"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="e4dfe543-3607-402a-a861-d486585e2090" stretchType="ContainerHeight" x="200" y="0" width="62" height="72">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="3ca8da7c-a4c1-44bd-8bae-fabc40229ba0" stretchType="ContainerHeight" x="0" y="0" width="62" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[$P{bangouFlg}==0]]></printWhenExpression>
										<expression><![CDATA[$F{kaigoKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="3">
											<leftPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
								</element>
								<element kind="frame" uuid="4528ed42-48a7-465f-b567-872b51fae4f2" stretchType="ContainerHeight" x="262" y="0" width="65" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="897091cb-d36c-4b99-9b24-7601b3b36788" stretchType="ContainerHeight" x="327" y="0" width="63" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="f10674d6-42f5-4c86-bcce-27d9d21fb9c7" stretchType="ContainerHeight" x="390" y="0" width="61" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="9eef554e-937c-464c-8e21-c288ff62e4f3" stretchType="ContainerHeight" x="451" y="0" width="75" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="aeae87b9-eb18-40d3-9091-fb0776004d2c" stretchType="ContainerHeight" x="526" y="0" width="75" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="02e95d03-83e0-4b27-8d6d-b692448b67a9" stretchType="ContainerHeight" x="601" y="0" width="74" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="75d4dac7-d89b-444e-9025-1496c3d69b38" stretchType="ContainerHeight" x="675" y="0" width="74" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="66471059-d93c-4c46-a295-f6592b3c10cd" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="1.5"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="bc204451-9c92-4256-ab73-a2129fb82ea5" stretchType="ContainerHeight" x="177" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineStyle="Solid"/>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="43f6145f-7dee-4f9e-a9e8-53a2cf7ff0da" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="a52d6334-f4b1-4ee7-8b87-847a27fe6430" stretchType="ContainerHeight" x="177" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="4bfa1820-c440-48ea-a618-2dce608425ef" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="89b8aca8-2bb2-4a43-b790-e74043f758b9" stretchType="ContainerHeight" x="200" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<topPen lineWidth="0.0"/>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="6222b404-a3d5-4f4f-95a8-7dadd7abfacf" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="a76830c5-9aa2-4063-9fe4-fba4dba3c4a9" stretchType="ContainerHeight" x="200" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<topPen lineWidth="0.0"/>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="55b5a85d-5aaa-4606-8445-fc87230eb163" stretchType="ContainerHeight" x="76" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="e8c49772-90c9-4fee-994d-7fe3ff70b5c7" stretchType="ContainerHeight" x="138" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="e644909a-6e92-4c7f-b615-d3e1a7b0a993" stretchType="ContainerHeight" x="92" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="56a9a413-d4bb-4480-9226-05b10f180b98" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<element kind="frame" uuid="81cc11ee-e6db-45e2-b9f6-26be85aadb70" stretchType="ContainerHeight" x="177" y="0" width="16" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<topPen lineWidth="0.0"/>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<element kind="frame" uuid="de8fe028-837c-4200-8265-dac26cb66f93" stretchType="ContainerHeight" x="200" y="0" width="15" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<topPen lineWidth="0.0"/>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.4" lineStyle="Dashed"/>
									</box>
								</element>
								<element kind="frame" uuid="8244164a-4a28-43fa-93b3-a296bdc7fb37" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==0&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="1.5"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="1bbfdb11-1a4d-4678-ab8c-748175e2ee53" stretchType="ContainerHeight" x="0" y="0" width="92" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==0&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.75" lineColor="#1F1F1F"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75" lineColor="#1F1F1F"/>
									</box>
								</element>
								<element kind="frame" uuid="891418af-2535-41d9-9996-00954494b506" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$P{choukiFlg}==1&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="a58172f9-f8e1-4dc6-869b-9da2474b9087" stretchType="ContainerHeight" x="0" y="0" width="76" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==0&&$P{choukiFlg}==1&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="f59b9e4a-e358-4f2b-981d-93749c7edfae" stretchType="ContainerHeight" x="0" y="0" width="16" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{bangouFlg}==1&&$F{kadaiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="b30f558e-d1ed-4489-9744-f66ce63dabaa" stretchType="ContainerHeight" x="76" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1&&$F{choukiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="b0b1b439-0d5e-4fdd-b5b3-b374fa72af18" stretchType="ContainerHeight" x="138" y="0" width="62" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==1&&$F{tankiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="afe27723-e467-4809-a842-8fe6174d0a40" stretchType="ContainerHeight" x="92" y="0" width="85" height="72" printWhenDetailOverflows="true">
									<printWhenExpression><![CDATA[$P{choukiFlg}==0&&$F{tankiBottomLineFlg}==1]]></printWhenExpression>
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
				<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
				<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="52" splitType="Stretch">
			<element kind="component" uuid="50af8e11-f386-4dd0-837b-aacf7c67eedc" x="0" y="0" width="711" height="51">
				<component kind="table">
					<datasetRun uuid="1e3b22ac-a9e8-4eb6-8952-554c25540485" subDataset="Dataset2">
						<dataSourceExpression><![CDATA[$F{soukatuList}]]></dataSourceExpression>
						<parameter name="mojiSize">
							<expression><![CDATA[$F{mojiSize}]]></expression>
						</parameter>
					</datasetRun>
					<column kind="single" uuid="a95fd2ad-14d2-4e81-8b27-810f1e69d9f2" width="711">
						<columnHeader height="18" style="Style1">
							<element kind="frame" uuid="3b371c68-b06d-4b0c-b37a-0cdf92450be8" positionType="Float" stretchType="ContainerHeight" x="0" y="0" width="711" height="18">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="staticText" uuid="dc567ffb-3a84-492d-8263-51be715bbdae" mode="Opaque" x="0" y="0" width="318" height="18" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="12.0" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[総括]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<rightPen lineWidth="0.75" lineColor="#000000"/>
									</box>
								</element>
								<element kind="staticText" uuid="97472959-ff83-4e0b-98ed-14fc7eda940c" mode="Opaque" x="318" y="0" width="393" height="18" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="12.0" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 計画の変更等]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="1.6"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box style="Style1">
								<pen lineWidth="1.6"/>
								<topPen lineWidth="1.6" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.6" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.6" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.6" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</columnHeader>
						<columnFooter height="17">
							<element kind="frame" uuid="da0d862c-a137-4ea5-8216-1572a5e3fbcd" x="0" y="0" width="711" height="17">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<element kind="staticText" uuid="d7c287a2-7fe9-4e7e-9761-a1bba01d3651" x="0" y="2" width="140" height="14" fontName="IPAexGothic" fontSize="12.0" hTextAlign="Left" vTextAlign="Middle">
									<text><![CDATA[再アセスメントの必要    ]]></text>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box leftPadding="4"/>
								</element>
								<element kind="staticText" uuid="024817b8-ff5c-485e-b5b4-7e11e7c5ce96" x="140" y="2" width="44" height="14" fontName="IPAexGothic" fontSize="12.0" hTextAlign="Left" vTextAlign="Middle">
									<text><![CDATA[なし]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<element kind="staticText" uuid="3f465605-e91b-41f7-8f6d-7ac0bf9e4e30" x="184" y="2" width="52" height="14" fontName="IPAexGothic" fontSize="12.0" vTextAlign="Middle">
									<text><![CDATA[あり]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="rectangle" uuid="8a11e2ae-93c1-4a21-ad6c-fc9e672eda8d" x="130" y="2" width="45" height="14" forecolor="#0C0CFE" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
									<printWhenExpression><![CDATA[$F{retryChk}.equals("0")]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="rectangle" uuid="134e333a-5e90-40c2-828c-8e964712d9ca" x="175" y="2" width="45" height="14" forecolor="#0C0CFE" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
									<printWhenExpression><![CDATA[$F{retryChk}.equals("1")]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="staticText" uuid="860bc00c-c43a-4cf4-946c-38a4ee20e89a" x="236" y="2" width="85" height="14" fontName="IPAexGothic" fontSize="12.0" vTextAlign="Middle">
									<text><![CDATA[（実施予定日：]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="textField" uuid="64993ba2-9fa6-4d74-a27d-a00a4a7c6a78" x="321" y="2" width="60" height="14" markup="html">
									<printWhenExpression><![CDATA[!$F{yoteiYmdGG}.equals("")]]></printWhenExpression>
									<expression><![CDATA[$F{yoteiYmdGG}+
"<font style='font-size:11pt'>"+$F{yoteiYmdYY}+"</font>"+
"/"+
"<font style='font-size:11pt'>"+$F{yoteiYmdMM}+"</font>"+
"/"+
"<font style='font-size:11pt'>"+$F{yoteiYmdDD}+"</font>"]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="staticText" uuid="43de139e-217c-4f75-9c8c-7f3a9d3931ce" x="381" y="2" width="10" height="14">
									<text><![CDATA[)]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<box leftPadding="0"/>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</columnFooter>
						<detailCell height="16">
							<element kind="frame" uuid="a02a49b2-0eeb-4ad6-9f76-21c27170a724" positionType="Float" stretchType="ContainerHeight" x="0" y="0" width="711" height="16">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="textField" uuid="a3810141-06ab-4b04-ac16-05fe399219e4" stretchType="ContainerHeight" x="0" y="0" width="318" height="16" fontName="IPAexMincho" fontSize="12.0" textAdjust="StretchHeight">
									<expression><![CDATA[$F{soukatuKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="da02069d-52f0-48e2-8910-609d7f494cdc" stretchType="ContainerHeight" x="318" y="0" width="393" height="16" fontName="IPAexMincho" fontSize="12.0" textAdjust="StretchHeight">
									<expression><![CDATA[$F{henkouKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5"/>
								</element>
								<box>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="1.6"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
				<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
				<property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="31" splitType="Stretch">
			<printWhenExpression><![CDATA[$F{shoninFlg} == 1]]></printWhenExpression>
			<element kind="subreport" uuid="79e48678-6e63-4b52-bb7d-5a38f9fce2f2" key="com_report" x="30" y="5" width="682" height="20">
				<dataSourceExpression><![CDATA[
    new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(
        ((net.sf.jasperreports.engine.data.JRBeanCollectionDataSource)$P{subReportDataDs}).getData()
    )
        ]]></dataSourceExpression>
				<expression><![CDATA[$P{subReportPath}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="20">
		<element kind="textField" uuid="df14d021-bbc5-4254-aecb-b68e36c285bb" x="357" y="1" width="17" height="17" fontName="IPAexGothic" fontSize="8.0" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[ $V{PAGE_NUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="07df373b-df44-465d-935e-8cdf01cf13f1" x="374" y="1" width="20" height="17" markup="html" fontName="IPAexGothic" fontSize="8.0" evaluationTime="Report" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["<font style='font-size:9pt'>"+" / "+"</font>" + $V{PAGE_NUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
