<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="E00402_介護予防サービス-支援計画表（A4横2枚） - 1" language="java" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="9177353c-9c8d-4017-9d09-9ac9189fe907">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="preventionPlanA31Data2"/>
	<style name="Table_TH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#FFFFFF">
		<box>
        
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_CH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_TH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_CH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_CH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<dataset name="Dataset1" uuid="c32fd627-e6e3-40c6-846e-8af8441cc089">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="preventionPlanA31"/>
		<parameter name="service1Label" class="java.lang.String"/>
		<parameter name="service2Label" class="java.lang.String"/>
		<parameter name="service3Label" class="java.lang.String"/>
		<parameter name="informalKnjLabel" class="java.lang.String"/>
		<parameter name="shienJigyoLabel" class="java.lang.String"/>
		<parameter name="service1Flg" class="java.lang.Integer"/>
		<parameter name="service2Flg" class="java.lang.Integer"/>
		<parameter name="h21Flg" class="java.lang.Integer"/>
		<parameter name="printFont" class="java.lang.String"/>
		<parameter name="ulFlg" class="java.lang.Integer"/>
		<field name="assryoDataList" class="net.sf.jasperreports.engine.JRDataSource">
			<description><![CDATA[assryoDataList]]></description>
		</field>
		<field name="sogoKadaiDataList" class="net.sf.jasperreports.engine.JRDataSource">
			<description><![CDATA[sogoKadaiDataList]]></description>
		</field>
	</dataset>
	<dataset name="assryoDataList" uuid="69e07aa4-0378-44fe-b438-2111c8c6b4f0">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="preventionPlanA31"/>
		<parameter name="printFont" class="java.lang.String"/>
		<field name="assryoId" class="java.lang.String">
			<description><![CDATA[assryoId]]></description>
		</field>
		<field name="jokyoKnj" class="java.lang.String">
			<description><![CDATA[jokyoKnj]]></description>
		</field>
		<field name="iyokuikouKnj" class="java.lang.String">
			<description><![CDATA[iyokuikouKnj]]></description>
		</field>
		<field name="kadaiKnj" class="java.lang.String">
			<description><![CDATA[kadaiKnj]]></description>
		</field>
		<group name="assryoId">
			<expression><![CDATA[$F{assryoId}]]></expression>
		</group>
		<group name="jokyoKnj">
			<expression><![CDATA[$F{jokyoKnj}]]></expression>
		</group>
		<group name="iyokuikouKnj">
			<expression><![CDATA[$F{iyokuikouKnj}]]></expression>
		</group>
		<group name="kadaiKnj">
			<expression><![CDATA[$F{kadaiKnj}]]></expression>
		</group>
	</dataset>
	<dataset name="sogoKadaiDataList" uuid="a8ede19e-ecef-4be2-9101-77c2a599dddc">
		<parameter name="printFont" class="java.lang.String"/>
		<parameter name="ulFlg" class="java.lang.Integer"/>
		<field name="sogoKadaiNo" class="java.lang.String">
			<description><![CDATA[sogoKadaiNo]]></description>
		</field>
		<field name="sogoKadaiKnj" class="java.lang.String">
			<description><![CDATA[sogoKadaiKnj]]></description>
		</field>
		<field name="sogoTeianKnj" class="java.lang.String">
			<description><![CDATA[sogoTeianKnj]]></description>
		</field>
		<field name="sogoIkouKnj" class="java.lang.String">
			<description><![CDATA[sogoIkouKnj]]></description>
		</field>
		<field name="sogoMokuhyoKnj" class="java.lang.String">
			<description><![CDATA[sogoMokuhyoKnj]]></description>
		</field>
		<field name="kadaiNo" class="java.lang.String">
			<description><![CDATA[kadaiNo]]></description>
		</field>
		<variable name="rowIndex" calculation="Count" class="java.lang.Long">
			<expression><![CDATA[+1]]></expression>
			<initialValueExpression><![CDATA[0]]></initialValueExpression>
		</variable>
	</dataset>
	<field name="title1" class="java.lang.String">
		<description><![CDATA[title1]]></description>
	</field>
	<field name="userId" class="java.lang.String">
		<description><![CDATA[userId]]></description>
	</field>
	<field name="shiTeiKubun" class="java.lang.Integer">
		<description><![CDATA[shiTeiKubun]]></description>
	</field>
	<field name="selectDate" class="java.lang.String">
		<description><![CDATA[selectDate]]></description>
	</field>
	<field name="jigyoKnj" class="java.lang.String">
		<description><![CDATA[jigyoKnj]]></description>
	</field>
	<field name="userName" class="java.lang.String">
		<description><![CDATA[userName]]></description>
	</field>
	<field name="keishoKnj" class="java.lang.String">
		<description><![CDATA[keishoKnj]]></description>
	</field>
	<field name="emptyFlg" class="java.lang.Integer">
		<description><![CDATA[emptyFlg]]></description>
	</field>
	<field name="printFont" class="java.lang.String">
		<description><![CDATA[printFont]]></description>
	</field>
	<field name="yoshien3Name" class="java.lang.String">
		<description><![CDATA[yoshien3Name]]></description>
	</field>
	<field name="kenkoRyuitenKnjLabel" class="java.lang.String">
		<description><![CDATA[kenkoRyuitenKnjLabel]]></description>
	</field>
	<field name="informalKnjLabel" class="java.lang.String">
		<description><![CDATA[informalKnjLabel]]></description>
	</field>
	<field name="service1Label" class="java.lang.String">
		<description><![CDATA[service1Label]]></description>
	</field>
	<field name="service2Label" class="java.lang.String">
		<description><![CDATA[service2Label]]></description>
	</field>
	<field name="service3Label" class="java.lang.String">
		<description><![CDATA[service3Label]]></description>
	</field>
	<field name="shienJigyoLabel" class="java.lang.String">
		<description><![CDATA[shienJigyoLabel]]></description>
	</field>
	<field name="doiFlg" class="java.lang.Integer">
		<description><![CDATA[doiFlg]]></description>
	</field>
	<field name="prgFlg" class="java.lang.Integer">
		<description><![CDATA[prgFlg]]></description>
	</field>
	<field name="stFlg" class="java.lang.Integer">
		<description><![CDATA[stFlg]]></description>
	</field>
	<field name="ulFlg" class="java.lang.Integer">
		<description><![CDATA[ulFlg]]></description>
	</field>
	<field name="r3Flg" class="java.lang.Integer">
		<description><![CDATA[r3Flg]]></description>
	</field>
	<field name="h21Flg" class="java.lang.Integer">
		<description><![CDATA[h21Flg]]></description>
	</field>
	<field name="inkanShowFlg" class="java.lang.Integer">
		<description><![CDATA[inkanShowFlg]]></description>
	</field>
	<field name="hanko1Knj" class="java.lang.String">
		<description><![CDATA[hanko1Knj]]></description>
	</field>
	<field name="hanko2Knj" class="java.lang.String">
		<description><![CDATA[hanko2Knj]]></description>
	</field>
	<field name="hanko3Knj" class="java.lang.String">
		<description><![CDATA[hanko3Knj]]></description>
	</field>
	<field name="hanko4Knj" class="java.lang.String">
		<description><![CDATA[hanko4Knj]]></description>
	</field>
	<field name="hanko5Knj" class="java.lang.String">
		<description><![CDATA[hanko5Knj]]></description>
	</field>
	<field name="hanko6Knj" class="java.lang.String">
		<description><![CDATA[hanko6Knj]]></description>
	</field>
	<field name="hanko7Knj" class="java.lang.String">
		<description><![CDATA[hanko7Knj]]></description>
	</field>
	<field name="hanko8Knj" class="java.lang.String">
		<description><![CDATA[hanko8Knj]]></description>
	</field>
	<field name="hanko9Knj" class="java.lang.String">
		<description><![CDATA[hanko9Knj]]></description>
	</field>
	<field name="hanko10Knj" class="java.lang.String">
		<description><![CDATA[hanko10Knj]]></description>
	</field>
	<field name="hanko11Knj" class="java.lang.String">
		<description><![CDATA[hanko11Knj]]></description>
	</field>
	<field name="hanko12Knj" class="java.lang.String">
		<description><![CDATA[hanko12Knj]]></description>
	</field>
	<field name="hanko13Knj" class="java.lang.String">
		<description><![CDATA[hanko13Knj]]></description>
	</field>
	<field name="hanko14Knj" class="java.lang.String">
		<description><![CDATA[hanko14Knj]]></description>
	</field>
	<field name="hanko15Knj" class="java.lang.String">
		<description><![CDATA[hanko15Knj]]></description>
	</field>
	<field name="createYmd" class="java.lang.String">
		<description><![CDATA[createYmd]]></description>
	</field>
	<field name="shokaiYmd" class="java.lang.String">
		<description><![CDATA[shokaiYmd]]></description>
	</field>
	<field name="centerShokuKnj" class="java.lang.String">
		<description><![CDATA[centerShokuKnj]]></description>
	</field>
	<field name="kubun" class="java.lang.Integer">
		<description><![CDATA[kubun]]></description>
	</field>
	<field name="itakuShokuKnj" class="java.lang.String">
		<description><![CDATA[itakuShokuKnj]]></description>
	</field>
	<field name="ninteiYmd" class="java.lang.String">
		<description><![CDATA[ninteiYmd]]></description>
	</field>
	<field name="yukoSYmd" class="java.lang.String">
		<description><![CDATA[yukoSYmd]]></description>
	</field>
	<field name="yukoEYmd" class="java.lang.String">
		<description><![CDATA[yukoEYmd]]></description>
	</field>
	<field name="nintei" class="java.lang.Integer">
		<description><![CDATA[nintei]]></description>
	</field>
	<field name="yoshienKbn" class="java.lang.Integer">
		<description><![CDATA[yoshienKbn]]></description>
	</field>
	<field name="service1Flg" class="java.lang.Integer">
		<description><![CDATA[service1Flg]]></description>
	</field>
	<field name="service2Flg" class="java.lang.Integer">
		<description><![CDATA[service2Flg]]></description>
	</field>
	<field name="mokuhyoDayKnj" class="java.lang.String">
		<description><![CDATA[mokuhyoDayKnj]]></description>
	</field>
	<field name="mokuhyoYearKnj" class="java.lang.String">
		<description><![CDATA[mokuhyoYearKnj]]></description>
	</field>
	<field name="kenkoRyuitenKnj" class="java.lang.String">
		<description><![CDATA[kenkoRyuitenKnj]]></description>
	</field>
	<field name="datoHoshinKnj" class="java.lang.String">
		<description><![CDATA[datoHoshinKnj]]></description>
	</field>
	<field name="sogoHoshinKnj" class="java.lang.String">
		<description><![CDATA[sogoHoshinKnj]]></description>
	</field>
	<field name="program1Flg" class="java.lang.Integer">
		<description><![CDATA[program1Flg]]></description>
	</field>
	<field name="program2Flg" class="java.lang.Integer">
		<description><![CDATA[program2Flg]]></description>
	</field>
	<field name="program3Flg" class="java.lang.Integer">
		<description><![CDATA[program3Flg]]></description>
	</field>
	<field name="program4Flg" class="java.lang.Integer">
		<description><![CDATA[program4Flg]]></description>
	</field>
	<field name="program5Flg" class="java.lang.Integer">
		<description><![CDATA[program5Flg]]></description>
	</field>
	<field name="program6Flg" class="java.lang.Integer">
		<description><![CDATA[program6Flg]]></description>
	</field>
	<field name="program1Cnt" class="java.lang.String">
		<description><![CDATA[program1Cnt]]></description>
	</field>
	<field name="program2Cnt" class="java.lang.String">
		<description><![CDATA[program2Cnt]]></description>
	</field>
	<field name="program3Cnt" class="java.lang.String">
		<description><![CDATA[program3Cnt]]></description>
	</field>
	<field name="program4Cnt" class="java.lang.String">
		<description><![CDATA[program4Cnt]]></description>
	</field>
	<field name="program5Cnt" class="java.lang.String">
		<description><![CDATA[program5Cnt]]></description>
	</field>
	<field name="program6Cnt" class="java.lang.String">
		<description><![CDATA[program6Cnt]]></description>
	</field>
	<field name="centerIkenKnj" class="java.lang.String">
		<description><![CDATA[centerIkenKnj]]></description>
	</field>
	<field name="centerKakuninFlg" class="java.lang.Integer">
		<description><![CDATA[centerKakuninFlg]]></description>
	</field>
	<field name="doiYmd" class="java.lang.String">
		<description><![CDATA[doiYmd]]></description>
	</field>
	<field name="doiKnj" class="java.lang.String">
		<description><![CDATA[doiKnj]]></description>
	</field>
	<field name="doiContent1" class="java.lang.String">
		<description><![CDATA[doiContent1]]></description>
	</field>
	<field name="doiContent2" class="java.lang.String">
		<description><![CDATA[doiContent2]]></description>
	</field>
	<field name="chiJigyoKnj" class="java.lang.String">
		<description><![CDATA[chiJigyoKnj]]></description>
	</field>
	<field name="itkJigyoKnj" class="java.lang.String">
		<description><![CDATA[itkJigyoKnj]]></description>
	</field>
	<field name="dataList1" class="net.sf.jasperreports.engine.JRDataSource">
		<description><![CDATA[dataList1]]></description>
	</field>
	<pageHeader height="180" splitType="Stretch">
		<element kind="frame" uuid="3895b9a8-2149-42fb-8327-d1c915e0d072" stretchType="ContainerHeight" x="0" y="0" width="802" height="36" removeLineWhenBlank="true">
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<element kind="textField" uuid="e4ce6e9e-e2d9-423e-bd88-5b216389f7f6" x="686" y="13" width="89" height="10" fontName="IPAexGothic" hTextAlign="Right" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{emptyFlg}.equals( "1 ") ? false : true]]></printWhenExpression>
				<expression><![CDATA[$F{shiTeiKubun} == 1 || $F{emptyFlg}.equals("1") ? "" : $F{selectDate}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<element kind="textField" uuid="402580ed-2b3a-4f94-a43f-2a77b55aa498" x="580" y="25" width="195" height="10" fontName="IPAexGothic" hTextAlign="Right" vTextAlign="Middle">
				<expression><![CDATA[$F{jigyoKnj}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<element kind="textField" uuid="5428250a-ff82-42f6-9d2f-89dc8d05acfa" x="2" y="20" width="575" height="14" fontName="IPAexGothic" fontSize="14.0" hTextAlign="Center" vTextAlign="Middle">
				<expression><![CDATA[$F{title1}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
		</element>
		<element kind="frame" uuid="521f6a0b-78f9-4a36-9b77-964f492848cb" stretchType="ContainerHeight" x="0" y="90" width="802" height="90" removeLineWhenBlank="true">
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.layout"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<element kind="staticText" uuid="811c1f5c-a085-4137-8feb-84d2777d2e48" x="3" y="21" width="42" height="10" fontName="IPAexGothic" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[利用者名]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="891101df-3e83-4959-8ebe-4962e4223f44" x="46" y="19" width="90" height="13" rotation="None" fontName="IPAexMincho" fontSize="10.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{userName}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="9c3ed963-6350-4a64-8593-6a791ca91665" x="137" y="21" width="21" height="10" fontName="IPAexGothic" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{keishoKnj}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="5104a762-1884-4800-a954-8fbec3c0eab7" x="59" y="33" width="95" height="13" fontName="IPAexMincho" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{ninteiYmd}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="staticText" uuid="56e16d66-4349-4ef3-a1d7-541a7399517c" x="3" y="35" width="51" height="10" fontName="IPAexGothic" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[認定年月日]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="448787f0-416a-4430-8c4d-f7cd79a27125" x="247" y="33" width="95" height="13" fontName="IPAexMincho" pdfFontName="HeiseiMin-W3" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{yukoSYmd}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="staticText" uuid="728509f2-2efa-4ac9-83cc-104cb26bbe96" x="171" y="35" width="74" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[認定の有効期間]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="staticText" uuid="a5295394-6cca-4c83-9e36-af74b5035cf9" x="344" y="35" width="18" height="10" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[～]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="c9112c80-edb2-4f23-92f4-ef3d0a512c74" x="364" y="33" width="95" height="13" fontName="IPAexMincho" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{yukoEYmd}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="96dd9236-6101-42ca-bc7f-9bd8bc8691b7" x="211" y="47" width="120" height="13" fontName="IPAexMincho" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{itakuShokuKnj}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="staticText" uuid="bf4b57ca-bdc0-4805-a96b-51a47b0e2d70" x="3" y="49" width="74" height="10" fontName="IPAexGothic" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[計画作成者氏名]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="15b81fa0-dce5-44ac-9f3d-5f323602973d" x="85" y="47" width="120" height="13" fontName="IPAexMincho" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{centerShokuKnj}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="staticText" uuid="08296853-52c3-40c0-b17a-9ef30a6967b8" x="3" y="63" width="294" height="10" fontName="IPAexGothic" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[委託の場合：計画作成事業者・事業所名及び所在地（連絡先）]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="2df12e94-8165-4d39-9c81-2b3f1d37c922" x="300" y="61" width="423" height="13" fontName="IPAexMincho" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{itkJigyoKnj}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="bd3f3dca-6b53-4db8-87de-7ad5c1f067ec" x="142" y="76" width="425" height="13" fontName="IPAexMincho" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{chiJigyoKnj}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="staticText" uuid="c676e47b-2cca-4c2f-8e8c-630b67375243" x="3" y="78" width="137" height="10" fontName="IPAexGothic" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[担当地域包括支援センター：]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="staticText" uuid="82a0bbd5-bbf8-46a8-9f93-c1807a752ef5" x="352" y="49" width="95" height="10" fontName="IPAexGothic" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[計画作成（変更）日]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="81bcebf9-2ab7-41af-8818-b7bb39df4094" x="450" y="47" width="95" height="13" fontName="IPAexMincho" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{createYmd}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="2c87b238-bc4f-4f60-9ea6-87f3feedb34a" x="614" y="47" width="95" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<expression><![CDATA[$F{shokaiYmd}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="staticText" uuid="0e4b96ad-6e14-4246-a95d-e4074a56e318" x="546" y="49" width="63" height="10" fontName="IPAexGothic" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[（初回作成日]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="staticText" uuid="6993f4ea-6fa5-4bb9-9291-c6801a362042" x="709" y="49" width="6" height="10" fontName="IPAexMincho" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[）]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="line" uuid="6bae352e-7946-47c8-be4e-4f48bfdffbe3" x="3" y="31" width="156" height="1">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="line" uuid="e03eabdf-a3ec-4bf5-9f4c-e604c34532fd" x="3" y="45" width="459" height="1">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<element kind="line" uuid="8c15db23-ef6d-4c58-b3eb-9b3442600527" x="3" y="60" width="716" height="1">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="line" uuid="e869d45f-a241-4a52-bff2-19e19ae88a51" x="3" y="74" width="726" height="1">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="line" uuid="529b1df0-812e-4e8c-a7d5-e9dec4b787eb" x="3" y="88" width="570" height="1">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="staticText" uuid="4a59cca8-618a-4c21-aa7c-823f16972818" x="3" y="6" width="17" height="10" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[No.]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="ebdd4a7f-f342-489b-a295-d6ebe83a2e96" x="21" y="5" width="56" height="13" fontName="IPAexMincho" pdfFontName="HeiseiMin-W3" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{userId}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="line" uuid="a2312f07-9319-4ab1-b58e-f0652e40df35" x="3" y="17" width="76" height="1">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="frame" uuid="17992a68-d27d-44e5-80ba-4f0c866aa718" x="371" y="0" width="412" height="20">
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="frame" uuid="234fa4cd-299a-4e4d-b6d3-0f44d84ab93f" x="0" y="0" width="410" height="19">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="staticText" uuid="*************-44df-b076-7e121fe80454" x="108" y="0" width="93" height="18" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[認定済・申請中]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="61501316-772f-43b5-b433-103da5d58a8d" x="201" y="0" width="125" height="18" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[要支援１・要支援２]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<pen lineWidth="0.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="1581ca4e-3464-436f-9f34-38f3037c1b17" x="0" y="0" width="108" height="18" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[初回・紹介・継続]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="2fadc937-2447-4e48-a411-cbf7251ff590" x="326" y="0" width="80" height="18" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[地域支援事業]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="rectangle" uuid="c96697cb-966f-407a-ae26-1a58f29ad02f" x="12" y="3" width="24" height="12" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
						<printWhenExpression><![CDATA[$F{kubun}==1?true:false]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.element.name" value="初回"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="rectangle" uuid="3f1586ce-e8d9-4bf5-80f5-f0deeb55f122" x="41" y="3" width="24" height="12" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
						<printWhenExpression><![CDATA[$F{kubun}==2?true:false]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.element.name" value="紹介"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="rectangle" uuid="021ab65f-961d-412a-a59d-2fe138017163" x="72" y="3" width="24" height="12" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
						<printWhenExpression><![CDATA[$F{kubun}==3?true:false]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.element.name" value="継続"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="rectangle" uuid="977b1817-2432-4515-b7b5-7b41ca18da95" x="11" y="3" width="53" height="12" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
						<printWhenExpression><![CDATA[$F{kubun}==4?true:false]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.element.name" value="初回・紹介"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="rectangle" uuid="e63da96a-7624-4ebf-b4f4-a6431c41d4b4" x="116" y="3" width="35" height="12" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
						<printWhenExpression><![CDATA[$F{nintei}==1?true:false]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.element.name" value="認定済"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="rectangle" uuid="d4d6c98d-1112-4642-b44c-0227a8d0be16" x="157" y="3" width="35" height="12" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
						<printWhenExpression><![CDATA[$F{nintei}==2?true:false]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.element.name" value="申請中"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="rectangle" uuid="094bc0d0-de51-4a37-9d46-4497635e8e12" x="215" y="3" width="45" height="12" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
						<printWhenExpression><![CDATA[$F{yoshienKbn}==1?true:false]]></printWhenExpression>
						<property name="com.jaspersoft.studio.element.name" value="要支援１"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<element kind="rectangle" uuid="eacaddd6-a224-4be1-88fa-522e7da387e5" x="266" y="3" width="45" height="12" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
						<printWhenExpression><![CDATA[$F{yoshienKbn}==2?true:false]]></printWhenExpression>
						<property name="com.jaspersoft.studio.element.name" value="要支援２"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<element kind="rectangle" uuid="c80277e0-6dd0-442c-9e3e-e8e41bff392a" x="333" y="3" width="65" height="12" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
						<printWhenExpression><![CDATA[$F{yoshienKbn}==3?true:false]]></printWhenExpression>
						<property name="com.jaspersoft.studio.element.name" value="要支援３"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<element kind="rectangle" uuid="caea2a95-f657-46c0-8f08-5549ac124f6c" x="42" y="3" width="54" height="12" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
						<printWhenExpression><![CDATA[$F{kubun}==5?true:false]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.element.name" value="紹介・継続"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
				</element>
			</element>
		</element>
		<element kind="frame" uuid="a9cbf115-4aed-4ae1-9953-d36ebaea0b74" x="1" y="37" width="705" height="51" removeLineWhenBlank="true">
			<printWhenExpression><![CDATA[$F{inkanShowFlg}==1]]></printWhenExpression>
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<element kind="staticText" uuid="a5a9da68-9563-4950-b5c2-2ffa8b69d152" x="0" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko1Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="3.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="7dc55bd4-19b2-45a2-a785-b1246dd9f942" x="0" y="0" width="47" height="13" fontName="IPAexMincho" fontSize="9.0" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko1Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko1Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="83c75571-3ac9-4966-8a95-7233578fcc2d" x="47" y="0" width="47" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko2Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko2Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="5cabdbbd-36f8-453e-831b-83e15de4d535" x="47" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko2Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="58d73738-00cd-4ff2-b719-d0923fef2b9e" x="94" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko3Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="719df211-e78e-4e11-9d64-eefb713cafc3" x="94" y="0" width="47" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko3Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko3Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="3ab247a9-98a3-4f6b-9981-ccb1a1a26fb8" x="141" y="0" width="47" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko4Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko4Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="c38723aa-969d-40dd-a730-c9d6aca0c111" x="141" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko4Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="119d8ca1-eea1-434d-9a62-4e58ee197fc6" x="188" y="0" width="47" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko5Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko5Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="47fa6d74-41f9-47bd-8676-8e6ae4a71139" x="188" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko5Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="4983ea87-42ae-4d05-8d7e-39d1aec60d14" x="235" y="0" width="47" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko6Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko6Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="2848f5e8-baf1-4944-8fe5-0a1e22fe3fbb" x="235" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko6Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="68158baf-0825-4d19-bc87-3083580d5641" x="282" y="0" width="47" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko7Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko7Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="d34b197f-e35c-4283-ab4c-2b5224bbf96c" x="282" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko7Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="80865907-b852-4ad5-8c17-cc9ab9cd6bcf" x="329" y="0" width="47" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko8Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko8Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="e7dfe7a5-fa25-4ae8-bebe-603a0e83dbfc" x="329" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko8Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="aed8e45f-fb06-4f1b-ba25-67bfeba11358" x="376" y="0" width="47" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko9Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko9Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="b7cd2455-4683-41e2-ba72-ae58d2cac1cd" x="376" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko9Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="d14d5434-d7a5-4146-bbe2-ce24f3841087" x="423" y="0" width="47" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko10Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko10Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="bad9d54e-6ac9-4b5f-bfb6-67cc731142f8" x="423" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko10Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="aa7818cb-8427-467e-a9c3-c420014aeed7" x="470" y="0" width="47" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko11Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko11Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="06b70425-bfe8-4ae1-8fd3-8d8f6f92c7c5" x="470" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko11Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="4a86f0b0-5904-4023-98ea-33817e201e9a" x="517" y="0" width="47" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko12Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko12Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="b22a7367-0eab-4e56-acb7-8f68b017e99d" x="517" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko12Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="2.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="128bfe5d-91f6-461e-82b2-48f2a50d567f" x="564" y="0" width="47" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko13Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko13Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="bf938a2e-81b8-48ce-8f5e-6004dda91fb3" x="564" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko13Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="62032019-42e0-47b8-9250-772efa8708ca" x="611" y="0" width="47" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko14Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko14Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="6f039b75-8dbc-4da3-ad1d-4559135b547f" x="611" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko14Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="1bf0cd7f-170c-4ce9-bf86-5b704242e213" x="658" y="0" width="47" height="13" fontName="IPAexMincho" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{hanko15Knj}.isEmpty()]]></printWhenExpression>
				<expression><![CDATA[$F{hanko15Knj}]]></expression>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="9479f13e-8884-4f02-b8c9-6f2d4d6e2414" x="658" y="13" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{hanko15Knj}.isEmpty()]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<box>
				<pen lineColor="#000000"/>
				<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="rectangle" uuid="3d8ca12c-931c-4a6a-98a4-56b985e9f5d1" x="0" y="36" width="707" height="53">
			<printWhenExpression><![CDATA[$F{inkanShowFlg}==1 && $V{PAGE_NUMBER} > 1]]></printWhenExpression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<pen lineWidth="1.0" lineColor="#FFFFFF"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="73" splitType="Stretch">
			<element kind="frame" uuid="119c7127-1cbc-45f9-becd-4367e9837df8" stretchType="ContainerHeight" x="0" y="3" width="802" height="70" printRepeatedValues="false">
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<element kind="component" uuid="c3f0699c-64f9-42ca-a253-7cf6f4c9231b" stretchType="ContainerHeight" x="0" y="0" width="780" height="68">
					<component kind="table">
						<datasetRun uuid="7951df69-ea81-4358-b6c7-a44dac5f8bf6" subDataset="Dataset1">
							<dataSourceExpression><![CDATA[$F{dataList1}]]></dataSourceExpression>
							<parameter name="service1Label">
								<expression><![CDATA[$F{service1Label}]]></expression>
							</parameter>
							<parameter name="service2Label">
								<expression><![CDATA[$F{service2Label}]]></expression>
							</parameter>
							<parameter name="service3Label">
								<expression><![CDATA[$F{service3Label}]]></expression>
							</parameter>
							<parameter name="informalKnjLabel">
								<expression><![CDATA[$F{informalKnjLabel}]]></expression>
							</parameter>
							<parameter name="shienJigyoLabel">
								<expression><![CDATA[$F{shienJigyoLabel}]]></expression>
							</parameter>
							<parameter name="service1Flg">
								<expression><![CDATA[$F{service1Flg}]]></expression>
							</parameter>
							<parameter name="service2Flg">
								<expression><![CDATA[$F{service2Flg}]]></expression>
							</parameter>
							<parameter name="h21Flg">
								<expression><![CDATA[$F{h21Flg}]]></expression>
							</parameter>
							<parameter name="printFont">
								<expression><![CDATA[$F{printFont}]]></expression>
							</parameter>
							<parameter name="ulFlg">
								<expression><![CDATA[$F{ulFlg}]]></expression>
							</parameter>
						</datasetRun>
						<detail splitType="Stretch"/>
						<column kind="single" uuid="5c721fe5-af05-4d6b-b45e-24840d1b7b1f" width="421">
							<columnHeader height="30" rowSpan="1" style="Table_CH">
								<element kind="staticText" uuid="fd597f0c-9648-49d5-8364-7c29d6aaacb6" x="0" y="0" width="189" height="30" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[アセスメント領域と
現在の状況]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<pen lineWidth="1.0"/>
									</box>
								</element>
								<element kind="staticText" uuid="82ec3869-26b4-491d-8d45-865e3383383b" x="189" y="0" width="120" height="30" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[本人・家族の
意欲・意向]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<pen lineWidth="1.0"/>
										<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="staticText" uuid="c1096b67-f8de-4a97-aeb7-63d7e624d0f5" x="309" y="0" width="112" height="30" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle" style="Table_TD">
									<text><![CDATA[領域における課題
（背景・原因）]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box style="Table_TD">
										<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</columnHeader>
							<detailCell height="24" style="Table_TD">
								<element kind="component" uuid="ba7557ed-bd8f-4ba3-9d0e-c24b5e06b386" stretchType="ContainerHeight" x="0" y="0" width="421" height="24">
									<component kind="table">
										<datasetRun uuid="2fa59bd2-1762-45b6-9754-4893c068881a" subDataset="assryoDataList">
											<dataSourceExpression><![CDATA[$F{assryoDataList}]]></dataSourceExpression>
											<parameter name="printFont">
												<expression><![CDATA[$P{printFont}]]></expression>
											</parameter>
										</datasetRun>
										<columnHeader>
											<printWhenExpression><![CDATA[false]]></printWhenExpression>
										</columnHeader>
										<column kind="single" uuid="f53babeb-f0a5-41ac-b75c-927239ad829d" width="189">
											<detailCell height="48" style="Table 1_TD">
												<element kind="frame" uuid="e4cb76f4-0256-42a0-ae46-9ae5a729f6de" x="0" y="0" width="189" height="48">
													<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
													<element kind="textField" uuid="6a441975-c3e8-49ed-80ed-897cc005e90b" stretchType="ContainerHeight" x="0" y="16" width="189" height="31" fontName="IPAexMincho" pdfFontName="HeiseiMin-W3" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" blankWhenNull="true">
														<expression><![CDATA[$F{jokyoKnj}]]></expression>
														<property name="com.jaspersoft.studio.unit.width" value="px"/>
														<property name="com.jaspersoft.studio.unit.height" value="px"/>
														<property name="com.jaspersoft.studio.unit.x" value="px"/>
														<property name="com.jaspersoft.studio.unit.y" value="px"/>
														<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{printFont}]]></propertyExpression>
														<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3"/>
													</element>
													<element kind="textField" uuid="09d9f976-db46-43cb-bc09-176a089c8fe8" x="0" y="0" width="189" height="16" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self">
														<expression><![CDATA[$F{assryoId}]]></expression>
														<property name="com.jaspersoft.studio.unit.width" value="px"/>
														<property name="com.jaspersoft.studio.unit.height" value="px"/>
														<property name="com.jaspersoft.studio.unit.x" value="px"/>
														<property name="com.jaspersoft.studio.unit.y" value="px"/>
														<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3">
															<bottomPen lineWidth="1.0"/>
														</box>
													</element>
												</element>
												<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
												<property name="com.jaspersoft.studio.unit.width" value="px"/>
												<property name="com.jaspersoft.studio.unit.height" value="px"/>
												<box style="Table 1_TD">
													<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
													<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
													<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
													<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												</box>
											</detailCell>
											<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
										</column>
										<column kind="single" uuid="0114b560-1784-4984-a719-18765c8c4ed2" width="120">
											<detailCell height="48" style="Table 1_TD">
												<element kind="textField" uuid="a0814c87-1396-473f-b67b-1eacb057111d" stretchType="ContainerHeight" x="0" y="0" width="120" height="48" fontName="IPAexMincho" pdfFontName="HeiseiMin-W3" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkTarget="Self" blankWhenNull="true">
													<expression><![CDATA[$F{iyokuikouKnj}]]></expression>
													<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
													<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{printFont}]]></propertyExpression>
													<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3"/>
												</element>
												<property name="com.jaspersoft.studio.unit.height" value="px"/>
												<property name="com.jaspersoft.studio.unit.width" value="px"/>
											</detailCell>
											<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
										</column>
										<column kind="single" uuid="ecfd583e-242b-4d05-a2e2-85cb8a0e29e1" width="112">
											<detailCell height="48" style="Table 1_TD">
												<element kind="textField" uuid="f87a045d-f86d-4902-bb46-be85a30298ab" stretchType="ContainerBottom" x="0" y="0" width="112" height="48" fontName="IPAexMincho" hTextAlign="Center">
													<expression><![CDATA[$F{kadaiKnj}]]></expression>
													<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3"/>
												</element>
												<property name="com.jaspersoft.studio.unit.width" value="px"/>
											</detailCell>
											<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
										</column>
									</component>
									<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
									<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
									<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
									<property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
									<property name="com.jaspersoft.layout.grid.x" value="-1"/>
									<property name="com.jaspersoft.layout.grid.y" value="-1"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								</element>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box style="Table_TD">
									<pen lineColor="#000000"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						</column>
						<column kind="single" uuid="343a97f4-e092-45f4-9f4e-d87a38263d03" width="358">
							<columnHeader height="30" rowSpan="1" style="Table_CH">
								<element kind="staticText" uuid="11eb9389-fce7-4ba3-b02e-700abd767134" x="0" y="0" width="128" height="30" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[総合的課題]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="staticText" uuid="935d27f3-c0d7-43f9-9e33-e645eca19883" x="128" y="0" width="113" height="30" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[課題に対する目標と
具体策の提案]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="staticText" uuid="42bc6232-c2dd-44ed-bc9a-b7d98b42faaf" x="241" y="0" width="117" height="30" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[具体策についての意向
本人・家族]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</columnHeader>
							<detailCell height="24" style="Table_TD">
								<element kind="component" uuid="5feca25f-b712-4c14-873b-a2a79d83bbb1" stretchType="ContainerHeight" x="0" y="0" width="358" height="24">
									<component kind="table" whenNoDataType="Blank">
										<datasetRun uuid="3c575a1b-5b43-4cc7-a12e-9cc5bec5d41b" subDataset="sogoKadaiDataList">
											<dataSourceExpression><![CDATA[$F{sogoKadaiDataList}]]></dataSourceExpression>
											<parameter name="printFont">
												<expression><![CDATA[$P{printFont}]]></expression>
											</parameter>
											<parameter name="ulFlg">
												<expression><![CDATA[$P{ulFlg}]]></expression>
											</parameter>
										</datasetRun>
										<columnHeader>
											<printWhenExpression><![CDATA[false]]></printWhenExpression>
										</columnHeader>
										<column kind="single" uuid="19d0e961-91bd-4b68-b5a7-b29ee2b631d3" width="358">
											<detailCell height="24">
												<element kind="frame" uuid="591908b8-4e0f-44d7-9253-ea8b0e127409" key="" x="0" y="0" width="358" height="24" printInFirstWholeBand="true">
													<property name="com.jaspersoft.studio.unit.height" value="px"/>
													<property name="com.jaspersoft.studio.unit.width" value="px"/>
													<element kind="textField" uuid="e12e4301-ba83-494c-98b4-d706e7b2ce56" stretchType="ElementGroupHeight" x="1" y="0" width="14" height="24" fontName="IPAexMincho" pdfFontName="HeiseiMin-W3" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left">
														<expression><![CDATA[$F{kadaiNo}]]></expression>
														<property name="com.jaspersoft.studio.unit.height" value="px"/>
														<property name="com.jaspersoft.studio.unit.y" value="px"/>
														<property name="com.jaspersoft.studio.unit.x" value="px"/>
														<property name="com.jaspersoft.studio.unit.width" value="px"/>
														<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3">
															<pen lineWidth="1.0"/>
															<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
														</box>
													</element>
													<element kind="textField" uuid="1bfd327e-25b0-4905-8180-6ac8278eda42" stretchType="ContainerHeight" x="15" y="0" width="113" height="24" fontName="IPAexMincho" pdfFontName="HeiseiMin-W3" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
														<paragraph lineSpacingSize="0.0"/>
														<expression><![CDATA[$F{sogoKadaiKnj}]]></expression>
														<property name="com.jaspersoft.studio.unit.height" value="px"/>
														<property name="com.jaspersoft.studio.unit.width" value="px"/>
														<property name="com.jaspersoft.studio.unit.x" value="px"/>
														<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{printFont}]]></propertyExpression>
														<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3">
															<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
														</box>
													</element>
													<element kind="textField" uuid="9680cc39-33a1-455c-a999-6a4768f8cd11" stretchType="ContainerHeight" x="128" y="0" width="115" height="24" fontName="IPAexMincho" pdfFontName="HeiseiMin-W3" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" blankWhenNull="true">
														<paragraph lineSpacingSize="1.0" spacingBefore="0"/>
														<expression><![CDATA[$F{sogoTeianKnj}]]></expression>
														<property name="com.jaspersoft.studio.unit.height" value="px"/>
														<property name="com.jaspersoft.studio.unit.width" value="px"/>
														<property name="com.jaspersoft.studio.unit.y" value="px"/>
														<property name="com.jaspersoft.studio.unit.x" value="px"/>
														<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
														<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{printFont}]]></propertyExpression>
														<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3">
															<pen lineWidth="1.0"/>
															<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
														</box>
													</element>
													<element kind="textField" uuid="8bdffe62-619d-462b-a68c-695ac7441bf6" stretchType="ContainerHeight" x="241" y="0" width="115" height="24" fontName="IPAexMincho" pdfFontName="HeiseiMin-W3" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
														<expression><![CDATA[$F{sogoIkouKnj}]]></expression>
														<property name="com.jaspersoft.studio.unit.height" value="px"/>
														<property name="com.jaspersoft.studio.unit.width" value="px"/>
														<property name="com.jaspersoft.studio.unit.y" value="px"/>
														<property name="com.jaspersoft.studio.unit.x" value="px"/>
														<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{printFont}]]></propertyExpression>
														<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3">
															<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
															<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
														</box>
													</element>
													<box>
														<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
														<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
														<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
														<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
													</box>
												</element>
												<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
												<property name="com.jaspersoft.studio.unit.width" value="px"/>
												<box>
													<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
													<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
													<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
													<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												</box>
											</detailCell>
											<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
										</column>
									</component>
									<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
									<property name="com.jaspersoft.studio.table.style.table_header" value="Table 2_TH"/>
									<property name="com.jaspersoft.studio.table.style.column_header" value="Table 2_CH"/>
									<property name="com.jaspersoft.studio.table.style.detail" value="Table 2_TD"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box style="Table_TD">
									<pen lineColor="#000000"/>
									<topPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
						</column>
					</component>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<element kind="frame" uuid="afdcd294-793e-4139-a604-59cfc0c3c524" stretchType="ContainerBottom" x="0" y="30" width="788" height="38" printWhenDetailOverflows="true" removeLineWhenBlank="true">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<element kind="frame" uuid="bf86b533-21dd-4b2a-92a5-3ad9d7a602c0" positionType="Float" stretchType="ContainerBottom" x="0" y="0" width="189" height="38" printWhenDetailOverflows="true" removeLineWhenBlank="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box>
							<pen lineWidth="1.0" lineColor="#000000"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="d0f63771-4d6b-4f08-ad52-c88cef9e8e4f" positionType="Float" stretchType="ContainerBottom" x="189" y="0" width="120" height="38" printWhenDetailOverflows="true" removeLineWhenBlank="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box>
							<pen lineWidth="1.0" lineColor="#000000"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="d17e7df5-0d04-4e4b-8d4c-f1fe5ee59877" positionType="Float" stretchType="ContainerBottom" x="309" y="0" width="112" height="38" printWhenDetailOverflows="true" removeLineWhenBlank="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box>
							<pen lineWidth="1.0" lineColor="#000000"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="66087efa-9c84-47ee-82c0-f4fb0b1d6c3f" positionType="Float" stretchType="ContainerBottom" x="421" y="0" width="128" height="38" printWhenDetailOverflows="true" removeLineWhenBlank="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box>
							<pen lineWidth="1.0" lineColor="#000000"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="b3931edd-5381-4c86-9f02-9425933889cc" positionType="Float" stretchType="ContainerBottom" x="549" y="0" width="113" height="38" printWhenDetailOverflows="true" removeLineWhenBlank="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box>
							<pen lineWidth="1.0" lineColor="#000000"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="472b1637-2d45-44a5-b7e4-0772a1106974" positionType="Float" stretchType="ContainerBottom" x="662" y="0" width="117" height="38" printWhenDetailOverflows="true" removeLineWhenBlank="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box>
							<pen lineWidth="1.0" lineColor="#000000"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
				</element>
				<box>
					<pen lineColor="#000000"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="65" splitType="Stretch">
			<printWhenExpression><![CDATA[$F{emptyFlg}==0?true:false]]></printWhenExpression>
			<element kind="frame" uuid="68ad5753-bfc0-4efd-8ec0-19ca4de7c496" x="2" y="1" width="336" height="64" printRepeatedValues="false">
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<element kind="textField" uuid="64bfb00e-783b-4989-b3d6-a521680a99fa" stretchType="NoStretch" x="0" y="0" width="335" height="20" fontName="IPAexGothic" fontSize="11.0" linkTarget="Self" blankWhenNull="true" printWhenDetailOverflows="true" hTextAlign="Left" vTextAlign="Top">
					<expression><![CDATA["【必要な事業プログラム】"]]></expression>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
				<element kind="frame" uuid="309a1994-94e6-451e-97b1-f716b6870ac6" x="0" y="14" width="336" height="44">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="frame" uuid="1db2b649-c740-482e-8556-0ea07fac2982" x="0" y="0" width="56" height="44">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<element kind="staticText" uuid="714483dd-5354-44a4-98fc-ebf51485237e" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="0" y="0" width="56" height="22" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[運動器の
機能向上]]></text>
							<property name="com.jaspersoft.layout.grid.x" value="1"/>
							<property name="com.jaspersoft.layout.grid.y" value="0"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.2"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="ellipse" uuid="be24de5e-47e2-459a-a7ec-87753f8f1fe0" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="11" y="24" width="36" height="18" backcolor="rgba(255, 255, 255, 0.0)">
							<printWhenExpression><![CDATA[$F{program1Flg}==1&&$F{prgFlg}==1?true:false]]></printWhenExpression>
							<property name="com.jaspersoft.studio.element.name" value="プログラムフラグ1"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="textField" uuid="fcd97d72-de48-4cf4-8fd1-1924a46373df" positionType="FixRelativeToBottom" stretchType="NoStretch" x="0" y="22" width="56" height="22" fontName="IPAexGothic" fontSize="9.0" linkType="None" linkTarget="Self" blankWhenNull="true" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{program1Cnt}]]></expression>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<pen lineWidth="1.0"/>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
					</element>
					<element kind="frame" uuid="28789968-dcea-4fe0-a231-b549da93165e" x="56" y="0" width="56" height="44">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<element kind="staticText" uuid="1eb53b29-d395-40d2-bc96-cfc3ba164fb5" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="0" y="0" width="56" height="22" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[栄養改善]]></text>
							<property name="com.jaspersoft.layout.grid.x" value="1"/>
							<property name="com.jaspersoft.layout.grid.y" value="0"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.2"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="ellipse" uuid="8b247f41-3d0b-4246-b4db-6f6b215fd4cc" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="11" y="24" width="36" height="18" backcolor="rgba(255, 255, 255, 0.0)">
							<printWhenExpression><![CDATA[$F{program2Flg}==1&&$F{prgFlg}==1?true:false]]></printWhenExpression>
							<property name="com.jaspersoft.studio.element.name" value="プログラムフラグ2"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="textField" uuid="9b1f2de9-68ec-4421-8ea7-33dd21c1a2c4" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="0" y="22" width="56" height="22" fontName="IPAexMincho" fontSize="12.0" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{program2Cnt}]]></expression>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<pen lineWidth="1.0"/>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
					</element>
					<element kind="frame" uuid="6a695a13-e7d4-4089-adf0-8a0f86fed7b1" x="112" y="0" width="56" height="44">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<element kind="staticText" uuid="ced14a8b-a40b-45b0-b1f3-1a79e79e1188" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="0" y="0" width="56" height="22" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[口腔機能
の向上]]></text>
							<property name="com.jaspersoft.layout.grid.x" value="1"/>
							<property name="com.jaspersoft.layout.grid.y" value="0"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.2"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="ellipse" uuid="a001d7a0-f761-4a88-a1b8-d6d074648729" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="11" y="24" width="36" height="18" backcolor="rgba(255, 255, 255, 0.0)">
							<printWhenExpression><![CDATA[$F{program1Flg}==3&&$F{prgFlg}==1?true:false]]></printWhenExpression>
							<property name="com.jaspersoft.studio.element.name" value="プログラムフラグ3"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="textField" uuid="6a4cf1b0-6115-46f3-a129-0f89490370d7" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="0" y="22" width="56" height="22" fontName="IPAexMincho" fontSize="12.0" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{program3Cnt}]]></expression>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<pen lineWidth="1.0"/>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
					</element>
					<element kind="frame" uuid="34da3f3f-ff8f-4d9e-bd39-9610c49bc708" x="168" y="0" width="56" height="44">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<element kind="staticText" uuid="ed637794-06f7-47bc-8554-c10148da7080" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="0" y="0" width="56" height="22" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[閉じこもり
予防]]></text>
							<property name="com.jaspersoft.layout.grid.x" value="1"/>
							<property name="com.jaspersoft.layout.grid.y" value="0"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.2"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="ellipse" uuid="6c9487bc-b4e5-4105-bd17-517aa948e084" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="11" y="24" width="36" height="18" backcolor="rgba(255, 255, 255, 0.0)">
							<printWhenExpression><![CDATA[$F{program4Flg}==1&&$F{prgFlg}==1?true:false]]></printWhenExpression>
							<property name="com.jaspersoft.studio.element.name" value="プログラムフラグ4"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="textField" uuid="79c26859-9683-42a9-8e7f-8bc8877cc5a4" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="0" y="22" width="56" height="22" fontName="IPAexMincho" fontSize="12.0" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{program4Cnt}]]></expression>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<pen lineWidth="1.0"/>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
					</element>
					<element kind="frame" uuid="6e28dc44-d178-492e-9712-cd829aba6eab" x="224" y="0" width="56" height="44">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<element kind="staticText" uuid="74c5e773-f8cb-4d28-8212-cdc32e6ce6b4" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="0" y="0" width="56" height="22" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[物忘れ予防]]></text>
							<property name="com.jaspersoft.layout.grid.x" value="1"/>
							<property name="com.jaspersoft.layout.grid.y" value="0"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.2"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="ellipse" uuid="b2c5aee4-bd8e-4005-bb0a-4d6b34b8ce19" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="11" y="24" width="36" height="18" backcolor="rgba(255, 255, 255, 0.0)">
							<printWhenExpression><![CDATA[$F{program5Flg}==1&&$F{prgFlg}==1?true:false]]></printWhenExpression>
							<property name="com.jaspersoft.studio.element.name" value="プログラムフラグ5"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="textField" uuid="8e8f2fbd-29ab-40a0-ad3b-b5d7335bf23c" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="0" y="22" width="56" height="22" fontName="IPAexMincho" fontSize="12.0" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{program5Cnt}]]></expression>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<pen lineWidth="1.0"/>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
					</element>
					<element kind="frame" uuid="177d3642-5b1e-492f-a36e-32f815d0aabe" x="280" y="0" width="56" height="44">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<element kind="staticText" uuid="850715eb-d9a5-4ae7-894d-baf9dd7bb172" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="0" y="0" width="56" height="22" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[うつ予防　　]]></text>
							<property name="com.jaspersoft.layout.grid.x" value="1"/>
							<property name="com.jaspersoft.layout.grid.y" value="0"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.2"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="ellipse" uuid="42f6b9f9-b034-424e-a2bd-6ff57980a3b2" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="11" y="24" width="36" height="18" backcolor="rgba(255, 255, 255, 0.0)">
							<printWhenExpression><![CDATA[$F{program6Flg}==1&&$F{prgFlg}==1?true:false]]></printWhenExpression>
							<property name="com.jaspersoft.studio.element.name" value="プログラムフラグ6"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
						</element>
						<element kind="textField" uuid="9b3eb09e-2775-4020-9fb3-ffdcf5e43cbb" positionType="FixRelativeToBottom" stretchType="ElementGroupBottom" x="0" y="22" width="56" height="22" fontName="IPAexMincho" fontSize="12.0" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{program6Cnt}]]></expression>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<pen lineWidth="1.0"/>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
					</element>
				</element>
			</element>
			<element kind="frame" uuid="5140280a-cbd8-42bc-ba23-5007ed3303bc" stretchType="NoStretch" x="359" y="1" width="420" height="64">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<element kind="textField" uuid="7112f498-7bd3-476f-88e7-f0409739bdb3" stretchType="ElementGroupHeight" x="3" y="14" width="413" height="22" fontName="IPAexMincho" fontSize="12.0" textAdjust="StretchHeight" blankWhenNull="true">
					<expression><![CDATA[$F{kenkoRyuitenKnj}]]></expression>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{printFont}]]></propertyExpression>
					<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="textField" uuid="c0011e72-d9f7-4cd7-8f83-66c38c1e488f" stretchType="NoStretch" x="0" y="1" width="420" height="12" fontName="IPAexGothic" fontSize="11.0" blankWhenNull="true" printRepeatedValues="false" hTextAlign="Left" vTextAlign="Top">
					<expression><![CDATA[$F{kenkoRyuitenKnjLabel}]]></expression>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</element>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="36" splitType="Stretch">
		<element kind="textField" uuid="06071b26-c3e2-4fb1-aefd-872b0b1cdc20" x="273" y="10" width="100" height="20" hTextAlign="Right">
			<expression><![CDATA[$V{PAGE_NUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="9c2193ca-0f2a-4079-b158-c200d6adde39" x="373" y="10" width="100" height="20" evaluationTime="Report" hTextAlign="Left">
			<expression><![CDATA["/ " + $V{PAGE_NUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
