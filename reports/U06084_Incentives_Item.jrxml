<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="U06084_誘因項目" language="java" columnCount="1" pageWidth="595" pageHeight="842" orientation="Landscape" columnWidth="555" leftMargin="22" rightMargin="18" topMargin="28" bottomMargin="0" uuid="c25d2aae-430a-461d-a450-902f9f1da6d5">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="Table_TH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Style1" mode="Transparent" backcolor="#FFFFFF">
		<conditionalStyle mode="Opaque" forecolor="#FFFFFF" backcolor="#0000FF">
			<conditionExpression><![CDATA[$F{trigId} == 0?($F{trigKbn}==1?($P{icolor}==2?true:false):false):false]]></conditionExpression>
		</conditionalStyle>
	</style>
	<dataset name="Dataset1" uuid="210f5185-8407-46ac-8393-5adea72e1070">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
		<parameter name="icolor" class="java.lang.Integer"/>
		<parameter name="kinyuFlg" class="java.lang.Integer"/>
		<query language="sql"><![CDATA[]]></query>
		<field name="capId" class="java.lang.Integer"/>
		<field name="capKnj" class="java.lang.String"/>
		<field name="trigId" class="java.lang.Integer"/>
		<field name="trigKbn" class="java.lang.Integer"/>
		<field name="trigValueKnj" class="java.lang.String"/>
		<field name="trigKnj" class="java.lang.String"/>
		<field name="hdKbn" class="java.lang.Integer"/>
		<field name="kinyuKbn" class="java.lang.Integer"/>
		<variable name="trigId" resetType="Group" incrementType="Group" calculation="System" resetGroup="Group1" incrementGroup="Group1" class="java.lang.Integer">
			<expression><![CDATA[$F{trigId}]]></expression>
			<initialValueExpression><![CDATA[$F{trigId}]]></initialValueExpression>
		</variable>
		<variable name="trigKbn" resetType="Group" incrementType="Group" calculation="System" resetGroup="Group1" incrementGroup="Group1" class="java.lang.Integer">
			<expression><![CDATA[$F{trigKbn}]]></expression>
			<initialValueExpression><![CDATA[$F{trigKbn}]]></initialValueExpression>
		</variable>
		<variable name="hdKbn" resetType="Group" incrementType="Group" resetGroup="Group1" incrementGroup="Group1" class="java.lang.Integer">
			<expression><![CDATA[$F{hdKbn}]]></expression>
		</variable>
		<variable name="kinyuKbn" resetType="Group" incrementType="Group" resetGroup="Group1" incrementGroup="Group1" class="java.lang.Integer">
			<expression><![CDATA[$F{kinyuKbn}]]></expression>
		</variable>
		<group name="Group1" reprintHeaderOnEachPage="true">
			<expression><![CDATA[$F{capId}]]></expression>
		</group>
	</dataset>
	<query language="sql"><![CDATA[]]></query>
	<field name="title" class="java.lang.String"/>
	<field name="syubetuKnj" class="java.lang.String"/>
	<field name="userName" class="java.lang.String"/>
	<field name="shiTeiDateYY" class="java.lang.String"/>
	<field name="shiTeiDateMM" class="java.lang.String"/>
	<field name="shiTeiDateDD" class="java.lang.String"/>
	<field name="shiTeiDateGG" class="java.lang.String"/>
	<field name="selDateYY" class="java.lang.String"/>
	<field name="selDateMM" class="java.lang.String"/>
	<field name="selDateDD" class="java.lang.String"/>
	<field name="selDateGG" class="java.lang.String"/>
	<field name="ryaku" class="java.lang.String"/>
	<field name="scaleBmi" class="java.lang.String"/>
	<field name="scaleDrs" class="java.lang.String"/>
	<field name="scalePs" class="java.lang.String"/>
	<field name="scaleCps" class="java.lang.Integer"/>
	<field name="scaleAdlh" class="java.lang.Integer"/>
	<field name="icolor" class="java.lang.Integer"/>
	<field name="userNameKeta" class="java.lang.Integer"/>
	<field name="list" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	<field name="emptyFlg" class="java.lang.Boolean"/>
	<field name="kinyuFlg" class="java.lang.Integer"/>
	<field name="shiTeiKubun" class="java.lang.Integer"/>
	<pageHeader height="65" splitType="Stretch">
		<element kind="frame" uuid="75c7f872-8118-48bc-863b-04e68ec5ad14" x="0" y="0" width="82" height="20">
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.HorizontalRowLayout"/>
			<element kind="textField" uuid="6c6bd73b-5550-4e63-a0c7-1b14300759dd" x="0" y="0" width="82" height="20" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<expression><![CDATA[$F{syubetuKnj}]]></expression>
			</element>
			<box>
				<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="textField" uuid="724f0ce0-f8f4-458d-a99d-863f0cd2e4f2" x="435" y="10" width="120" height="10" markup="html" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right">
			<printWhenExpression><![CDATA[($F{shiTeiKubun} != 1) && (!$F{emptyFlg})]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDateGG}+
"<font style='font-size:9pt'>"+$F{shiTeiDateYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{shiTeiDateMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{shiTeiDateDD}+"</font>"+
"日"]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="45e73708-0135-412e-ac69-07cf2db17ff3" x="202" y="20" width="150" height="14" fontName="IPAexGothic" fontSize="14.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center">
			<expression><![CDATA[$F{title}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="frame" uuid="cb84fa8f-1b2c-4137-8663-fa204bf2a0e1" x="0" y="51" width="555" height="14">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<element kind="frame" uuid="3eb9a634-95c0-4ccb-97f9-6b043a1ac725" x="0" y="0" width="195" height="14">
				<property name="com.jaspersoft.layout.grid.x" value="0"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
				<element kind="staticText" uuid="91b95669-c2b9-4f54-a89e-bddedc8826ce" x="0" y="0" width="75" height="12" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Top">
					<text><![CDATA[利用者氏名：]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
				<element kind="textField" uuid="b57516f7-ca2b-46f9-b31a-352f457e6dea" x="75" y="0" width="120" height="12" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Top">
					<expression><![CDATA[ $F{userName}]]></expression>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{userNameKeta}>25?"6":$F{userNameKeta}>19?"9":"12"]]></propertyExpression>
				</element>
			</element>
			<element kind="frame" uuid="7c69f95c-8001-4d08-94ad-6f85d64e26cb" x="195" y="0" width="185" height="14">
				<property name="com.jaspersoft.layout.grid.x" value="1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<element kind="staticText" uuid="e1e049e2-83c7-4c41-ae2e-a805c9cc6925" x="0" y="0" width="50" height="14" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Top">
					<text><![CDATA[選定日：]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
				<element kind="textField" uuid="207955bb-3ce7-4b9e-9e54-8cab7bf8fa9b" x="50" y="0" width="135" height="14" markup="html" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Left" vTextAlign="Top">
					<printWhenExpression><![CDATA[!$F{emptyFlg}]]></printWhenExpression>
					<expression><![CDATA[$F{selDateGG}+
"<font style='font-size:11pt'>"+$F{selDateYY}+"</font>"+
"年"+
"<font style='font-size:11pt'>"+$F{selDateMM}+"</font>"+
"月"+
"<font style='font-size:11pt'>"+$F{selDateDD}+"</font>"+
"日"]]></expression>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
			</element>
			<element kind="frame" uuid="911864b3-6246-4058-935c-b20e73303779" x="380" y="0" width="175" height="14">
				<property name="com.jaspersoft.layout.grid.x" value="2"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<element kind="textField" uuid="62a079a9-07b4-4abc-9b90-61388444dcfb" x="0" y="0" width="175" height="14" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Top">
					<expression><![CDATA[$F{ryaku}]]></expression>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
			</element>
			<box>
				<bottomPen lineWidth="2.0"/>
			</box>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="490" splitType="Stretch">
			<element kind="frame" uuid="eac3695b-eaed-4ea6-bdd1-e9499bf3b11e" x="10" y="10" width="315" height="480">
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<element kind="frame" uuid="0f1a9314-fed1-437d-9688-eb377bcfe029" x="0" y="0" width="315" height="62">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="staticText" uuid="1982c3ab-89bf-4e8e-95d3-080d4b50a950" x="0" y="0" width="315" height="17" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<paragraph firstLineIndent="5"/>
						<text><![CDATA[BMI]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="7588ebe2-ab3d-43db-ae3d-2af73e16d249" x="0" y="17" width="315" height="20" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<paragraph firstLineIndent="10"/>
						<text><![CDATA[BMIの計算＝体重(kg) ÷ {身長(m) × 身長(m)}]]></text>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
					</element>
					<element kind="textField" uuid="ed550830-16b6-4565-b218-0a231041a04a" x="250" y="37" width="50" height="14" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
						<expression><![CDATA[$F{scaleBmi}]]></expression>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<bottomPen lineWidth="1.5"/>
						</box>
					</element>
				</element>
				<element kind="frame" uuid="1663b11d-056d-469e-8579-0ba3068cf9c9" x="0" y="62" width="315" height="62">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="staticText" uuid="91a5a11b-3b1a-49be-9b9f-fc5c8f1b2058" x="0" y="0" width="315" height="17" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<paragraph firstLineIndent="5"/>
						<text><![CDATA[うつ評価尺度：DRS（Depression Rating Scale）]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="c63ba770-1049-4616-9c3e-d435fcd87589" x="0" y="17" width="315" height="20" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<paragraph firstLineIndent="10"/>
						<text><![CDATA[スコアの範囲は0～14点]]></text>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
					</element>
					<element kind="textField" uuid="82ae3084-ce2e-4abb-9555-1348a7dd4c74" x="250" y="37" width="50" height="14" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
						<expression><![CDATA[$F{scaleDrs}]]></expression>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<bottomPen lineWidth="1.0"/>
						</box>
					</element>
				</element>
				<element kind="frame" uuid="8f849c64-a944-47fe-83f7-67326059978b" x="0" y="124" width="315" height="62">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="staticText" uuid="91373efb-1ec6-4e0b-823b-d9d1629a8e67" x="0" y="0" width="315" height="17" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<paragraph firstLineIndent="5"/>
						<text><![CDATA[痛み尺度：PS（Pain Score）]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="f4820a5a-309a-4512-8c02-41f212b8333a" x="0" y="17" width="315" height="20" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<paragraph firstLineIndent="10"/>
						<text><![CDATA[スコアの範囲は0～4（5段階）]]></text>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
					</element>
					<element kind="textField" uuid="3117df81-f4c2-431a-b91c-fd196610d5a1" x="250" y="37" width="50" height="14" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
						<expression><![CDATA[$F{scalePs}]]></expression>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<bottomPen lineWidth="1.0"/>
						</box>
					</element>
				</element>
				<element kind="frame" uuid="8029fd45-81e1-44be-80d5-8025742206d7" x="0" y="186" width="315" height="147">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<element kind="staticText" uuid="183c43d5-7b04-4877-a644-029161954890" x="0" y="0" width="315" height="17" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<paragraph firstLineIndent="5"/>
						<text><![CDATA[認知機能尺度：CPS（Cognitive Performance Scale）]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="eef69665-8010-451a-8b14-89feb7ff48b6" x="0" y="17" width="315" height="20" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<paragraph firstLineIndent="10"/>
						<text><![CDATA[スコアの範囲は0～6（7段階）]]></text>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
					</element>
					<element kind="frame" uuid="74bd802f-bb8d-4050-a988-3b60cd09f522" x="0" y="45" width="315" height="90">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<element kind="frame" uuid="a8a8800b-7d25-4756-9c40-fbc83988f5bd" x="0" y="0" width="315" height="13">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="0"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="staticText" uuid="6fd16067-b610-407c-ada0-a03a79fd6df7" x="0" y="0" width="315" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph firstLineIndent="10"/>
								<text><![CDATA[・　0：障害なし]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="ellipse" uuid="87900a0e-7b67-47d0-9b1b-bf4737249b72" mode="Transparent" x="9" y="1" width="11" height="11" backcolor="rgba(255, 255, 255, 0.0)">
								<printWhenExpression><![CDATA[$F{scaleCps}==0]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
							</element>
						</element>
						<element kind="frame" uuid="2b2da63b-bcc9-4f66-8de6-a7d14ef8250c" x="0" y="13" width="315" height="13">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="staticText" uuid="90e0b029-1a19-4d3c-b786-dd33e7566bd5" x="0" y="0" width="315" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph firstLineIndent="10"/>
								<text><![CDATA[・　1：境界的である]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							</element>
							<element kind="ellipse" uuid="ba63ca0e-ddd8-4a62-96be-c16d7ee81dec" x="9" y="1" width="11" height="11" backcolor="rgba(255, 255, 255, 0.0)">
								<printWhenExpression><![CDATA[$F{scaleCps}==1]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
							</element>
						</element>
						<element kind="frame" uuid="3af145dc-1d44-4c00-bd47-57fdf4bf74ff" x="0" y="26" width="315" height="13">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="2"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="staticText" uuid="8e887acb-7da7-48fc-b039-95b81b40caf2" x="0" y="0" width="315" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph firstLineIndent="10"/>
								<text><![CDATA[・　2：軽度の障害がある]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							</element>
							<element kind="ellipse" uuid="ffcd17a3-b3d3-42b3-ad05-f43be55d6731" x="9" y="1" width="11" height="11" backcolor="rgba(255, 255, 255, 0.0)">
								<printWhenExpression><![CDATA[$F{scaleCps}==2]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
							</element>
						</element>
						<element kind="frame" uuid="5377f7fa-41f8-4bf1-9873-1895da2e9136" x="0" y="39" width="315" height="13">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="3"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="staticText" uuid="f2db377c-3b6c-4998-9497-894746d38846" x="0" y="0" width="315" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph firstLineIndent="10"/>
								<text><![CDATA[・　3：中程度の障害がある]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							</element>
							<element kind="ellipse" uuid="bad0949d-19d0-4068-8116-c12226027255" x="9" y="1" width="11" height="11" backcolor="rgba(255, 255, 255, 0.0)">
								<printWhenExpression><![CDATA[$F{scaleCps}==3]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
							</element>
						</element>
						<element kind="frame" uuid="5866039e-5dcf-4fd6-bfd4-661895a287ea" x="0" y="52" width="315" height="13">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="4"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="staticText" uuid="4062884c-8c66-4b74-adf5-e5b77755839e" x="0" y="0" width="315" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph firstLineIndent="10"/>
								<text><![CDATA[・　4：やや重度の障害がある]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							</element>
							<element kind="ellipse" uuid="4401fac7-eb21-484e-8da0-f9d86d4d12d9" x="9" y="1" width="11" height="11" backcolor="rgba(255, 255, 255, 0.0)">
								<printWhenExpression><![CDATA[$F{scaleCps}==4]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
							</element>
						</element>
						<element kind="frame" uuid="85b1946f-5ade-4845-89f5-a90416fc7056" x="0" y="65" width="315" height="13">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="5"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="staticText" uuid="f25a4137-be91-4701-8ff4-769bb69afee7" x="0" y="0" width="315" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph firstLineIndent="10"/>
								<text><![CDATA[・　5：重度の障害がある]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							</element>
							<element kind="ellipse" uuid="02b066a3-131f-4482-a225-58df54f4e90f" x="9" y="1" width="11" height="11" backcolor="rgba(255, 255, 255, 0.0)">
								<printWhenExpression><![CDATA[$F{scaleCps}==5]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
							</element>
						</element>
						<element kind="frame" uuid="7e9edd8d-ce01-4b33-8de2-d805896b4274" x="0" y="78" width="315" height="12">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="6"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="staticText" uuid="556b9d4f-**************-e8c88791d2ab" x="0" y="0" width="315" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph firstLineIndent="10"/>
								<text><![CDATA[・　6：最重度の障害がある]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							</element>
							<element kind="ellipse" uuid="076dc0e5-7a9c-4d2b-aeb9-bd1dba13b836" x="9" y="1" width="11" height="11" backcolor="rgba(255, 255, 255, 0.0)">
								<printWhenExpression><![CDATA[$F{scaleCps}==6]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
							</element>
						</element>
					</element>
				</element>
				<element kind="frame" uuid="d1f42a69-edcb-4932-ba12-c3b018d83025" x="0" y="336" width="315" height="147">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<element kind="staticText" uuid="0d6f89d0-3edb-4414-9870-f06bada15f06" x="0" y="0" width="315" height="25" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<paragraph firstLineIndent="5"/>
						<text><![CDATA[日常生活自立度尺度：ADL-H
（Activities of Daily Living Self-Performance Hierarchy Scale）]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="9c767dc0-f745-477f-86b4-7559de07ad4a" x="0" y="25" width="315" height="20" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<paragraph firstLineIndent="10"/>
						<text><![CDATA[スコアの範囲は0～6（7段階）]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<element kind="frame" uuid="5a467aa9-1764-475f-baff-cca46e3dcb1c" x="0" y="53" width="315" height="90">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<element kind="frame" uuid="6cd15cc7-4817-49ed-9dc6-4c4671f9bed5" x="0" y="0" width="315" height="13">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="0"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="staticText" uuid="42845448-872c-4570-b465-0cef91a617a6" x="0" y="0" width="315" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph firstLineIndent="10"/>
								<text><![CDATA[・　0：自立]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="ellipse" uuid="444b514b-a35d-484a-bdc5-fe3f8b21bac6" x="9" y="1" width="11" height="11" backcolor="rgba(255, 255, 255, 0.0)">
								<printWhenExpression><![CDATA[$F{scaleAdlh}==0]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
							</element>
						</element>
						<element kind="frame" uuid="1fce926d-ddd8-42a7-aa76-0f90cbcaef97" x="0" y="13" width="315" height="13">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="staticText" uuid="58e53891-de20-49ea-a464-7e11d0994135" x="0" y="0" width="315" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph firstLineIndent="10"/>
								<text><![CDATA[・　1：見守り必要]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							</element>
							<element kind="ellipse" uuid="73fea0f1-8c81-4dc8-9cf0-235023241d9b" x="9" y="1" width="11" height="11" backcolor="rgba(255, 255, 255, 0.0)">
								<printWhenExpression><![CDATA[$F{scaleAdlh}==1]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
							</element>
						</element>
						<element kind="frame" uuid="5f2abea6-8842-4865-9e56-5e3da429adaa" x="0" y="26" width="315" height="13">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="2"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="staticText" uuid="bc6d33e6-26c3-46d1-9f2c-da69431dce4a" x="0" y="0" width="315" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph firstLineIndent="10"/>
								<text><![CDATA[・　2：限定援助]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							</element>
							<element kind="ellipse" uuid="ef8fbc65-9dbb-4e7a-911f-3805ef373da8" x="9" y="1" width="11" height="11" backcolor="rgba(255, 255, 255, 0.0)">
								<printWhenExpression><![CDATA[$F{scaleAdlh}==2]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
							</element>
						</element>
						<element kind="frame" uuid="c8c48ad1-f0f2-4d9d-84f4-227b60c0d537" x="0" y="39" width="315" height="13">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="3"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="staticText" uuid="14044e1a-41a0-4a32-bcf3-75dfb1fde7c1" x="0" y="0" width="315" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph firstLineIndent="10"/>
								<text><![CDATA[・　3：広範援助Ⅰ]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							</element>
							<element kind="ellipse" uuid="2e8bf4e2-04ca-4a19-bad4-ef468e140602" x="9" y="1" width="11" height="11" backcolor="rgba(255, 255, 255, 0.0)">
								<printWhenExpression><![CDATA[$F{scaleAdlh}==3]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
							</element>
						</element>
						<element kind="frame" uuid="660ff192-bac9-437b-aab4-3e34261e8ce6" x="0" y="52" width="315" height="13">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="4"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="staticText" uuid="07dd4fdf-f05f-4ac2-92cf-ca0387960f1d" x="0" y="0" width="315" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph firstLineIndent="10"/>
								<text><![CDATA[・　4：広範援助Ⅱ]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							</element>
							<element kind="ellipse" uuid="4d35c3d7-ffe4-4b28-aacb-98c7b0b3dc25" x="9" y="1" width="11" height="11" backcolor="rgba(255, 255, 255, 0.0)">
								<printWhenExpression><![CDATA[$F{scaleAdlh}==4]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
							</element>
						</element>
						<element kind="frame" uuid="9afc5810-f9e8-4ef0-9749-caf12856c44d" x="0" y="65" width="315" height="13">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="5"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="staticText" uuid="da0fdccf-435a-43a9-97b7-c17e4930159c" x="0" y="0" width="315" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph firstLineIndent="10"/>
								<text><![CDATA[・　5：最大援助]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							</element>
							<element kind="ellipse" uuid="d9942354-cc92-49e4-8c41-3cdf48deba39" x="9" y="1" width="11" height="11" backcolor="rgba(255, 255, 255, 0.0)">
								<printWhenExpression><![CDATA[$F{scaleAdlh}==5]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
							</element>
						</element>
						<element kind="frame" uuid="fd69b918-2182-45c0-abb4-3909b84ed8f6" x="0" y="78" width="315" height="12">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="6"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="staticText" uuid="7cf358bf-6bff-476d-a1ef-feb228db6997" x="0" y="0" width="315" height="13" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph firstLineIndent="10"/>
								<text><![CDATA[・　6：全面依存]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							</element>
							<element kind="ellipse" uuid="b4928c3f-b7d4-4569-8a8c-b1d9dcaa2c1e" x="9" y="1" width="11" height="11" backcolor="rgba(255, 255, 255, 0.0)">
								<printWhenExpression><![CDATA[$F{scaleAdlh}==6]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
							</element>
						</element>
					</element>
				</element>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="57" splitType="Stretch">
			<element kind="component" uuid="af1abd07-83ab-46f2-83e7-1827ab18e9c1" x="0" y="0" width="555" height="54">
				<component kind="table">
					<datasetRun uuid="ecd83352-73c7-4807-9016-ea0775441390" subDataset="Dataset1">
						<dataSourceExpression><![CDATA[$F{list}]]></dataSourceExpression>
						<parameter name="icolor">
							<expression><![CDATA[$F{icolor}]]></expression>
						</parameter>
						<parameter name="kinyuFlg">
							<expression><![CDATA[$F{kinyuFlg}]]></expression>
						</parameter>
					</datasetRun>
					<detail>
						<printWhenExpression><![CDATA[$F{trigId}!=0]]></printWhenExpression>
					</detail>
					<column kind="group" uuid="9cadbda2-b75f-4edf-bd63-9075e000ef17" width="356">
						<groupHeader groupName="Group1">
							<cell height="16" rowSpan="1">
								<element kind="textField" uuid="a22ef458-63dd-435e-a0c9-53c9a9e2b247" mode="Transparent" x="0" y="0" width="356" height="16" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
									<expression><![CDATA[$F{capId}==1?"機能面":$F{capId}==7?"精神面":$F{capId}==13?"社会面":$F{capId}==16?"臨床面":""]]></expression>
								</element>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</cell>
						</groupHeader>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [3]"/>
						<column kind="single" uuid="aa954921-4330-4f06-ab2d-8f9e0c2d02c7" width="10">
							<detailCell height="14">
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</detailCell>
							<groupHeader groupName="Group1">
								<cell height="27" rowSpan="3">
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</cell>
							</groupHeader>
							<groupFooter groupName="Group1">
								<cell height="0" rowSpan="1">
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</cell>
							</groupFooter>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						</column>
						<column kind="group" uuid="3c6ca35b-6c80-44d5-9f16-bdb67e69b55d" width="346">
							<groupHeader groupName="Group1">
								<cell height="18" rowSpan="1">
									<element kind="textField" uuid="8dc3f998-9174-4ff6-988f-d8e446b5198d" mode="Opaque" x="0" y="0" width="346" height="18" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="12.0" pdfEncoding="UniJIS-UCS2-HW-H" evaluationTime="Group" linkType="None" linkTarget="Self" evaluationGroup="Group1" removeLineWhenBlank="true" vTextAlign="Middle">
										<expression><![CDATA["CAP"+" " +($F{capId}<10?"  ":"")+$F{capId}+"   "+$F{capKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[($V{trigId} == 0) ?
  (
  	($P{kinyuFlg} == 0&&$V{hdKbn}==9)||($P{kinyuFlg} != 0&&$V{kinyuKbn}==9) ?
      ($P{icolor} == 1 ? "#C0C0C0" : ($P{icolor} == 2 ? "#ECE9D8" : "#FFFFFF")) :
    ($P{kinyuFlg} == 0&&$V{hdKbn}==1)||($P{kinyuFlg} != 0&&$V{kinyuKbn}==1) ?
      ($P{icolor} == 1 ? "#000000": ($P{icolor} == 2 ? "#0000FF" : "#FFFFFF")) :
    "#FFFFFF"
  ) :
"#FFFFFF"]]></propertyExpression>
										<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[($V{trigId} == 0) ?
  (
    ($V{trigKbn} == 1) ?
      ($P{icolor} == 1 ? "#FFFFFF": ($P{icolor} == 2 ? "#FFFFFF" : "#000000")) :
    ($V{trigKbn} == 9) ?
      ($P{icolor} == 1 ? "#000000" : ($P{icolor} == 2 ? "#000000" : "#000000")) :
    "#000000"
  ) :
"#000000"]]></propertyExpression>
										<box leftPadding="4">
											<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</element>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</cell>
							</groupHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [2]"/>
							<column kind="group" uuid="336e06d7-ca5d-4e6a-bb2c-bb7816e7dbf5" width="346">
								<groupHeader groupName="Group1">
									<cell height="9" rowSpan="1">
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<box>
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</cell>
								</groupHeader>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [2]"/>
								<column kind="single" uuid="8d5a9c97-c593-4c24-ac84-fc94508ee60d" width="8">
									<detailCell height="14">
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
									</detailCell>
									<groupHeader groupName="Group1">
										<cell height="0" rowSpan="1">
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
										</cell>
									</groupHeader>
									<groupFooter groupName="Group1">
										<cell height="0" rowSpan="1">
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
											<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
										</cell>
									</groupFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
								</column>
								<column kind="single" uuid="9b9a404f-edce-461d-b0e3-c2809785d9b6" width="338">
									<detailCell height="14">
										<element kind="ellipse" uuid="f72543f6-3d40-479e-a003-1ce877410fc7" mode="Transparent" x="0" y="2" width="10" height="10">
											<printWhenExpression><![CDATA[$F{trigKbn}==1]]></printWhenExpression>
											<property name="com.jaspersoft.studio.unit.width" value="px"/>
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
											<property name="com.jaspersoft.studio.unit.y" value="px"/>
											<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{icolor}==2?"#FF0000":"#000000"]]></propertyExpression>
										</element>
										<element kind="textField" uuid="8077f208-7c70-4cad-b2bc-81360aea79aa" x="0" y="0" width="338" height="14" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" vTextAlign="Middle">
											<expression><![CDATA[$F{trigKnj}]]></expression>
											<property name="com.jaspersoft.studio.unit.x" value="px"/>
											<property name="com.jaspersoft.studio.unit.width" value="px"/>
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
										</element>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
									</detailCell>
									<groupHeader groupName="Group1">
										<cell height="0" rowSpan="1">
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
										</cell>
									</groupHeader>
									<groupFooter groupName="Group1">
										<cell height="0" rowSpan="1">
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
										</cell>
									</groupFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
								</column>
							</column>
						</column>
					</column>
					<column kind="single" uuid="69298c2d-9a20-4fcd-9b92-67e0c502e14b" width="18">
						<detailCell height="14"/>
						<groupHeader groupName="Group1">
							<cell height="43" rowSpan="4">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</cell>
						</groupHeader>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
					</column>
					<column kind="single" uuid="6462946c-6697-421f-b4e9-cefec54fad29" width="182">
						<detailCell height="14">
							<element kind="textField" uuid="08dfab46-e05a-4df8-9679-e5d9fc775d6e" x="0" y="0" width="182" height="14" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<expression><![CDATA[$F{trigValueKnj}]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						</detailCell>
						<groupHeader groupName="Group1">
							<cell height="43" rowSpan="4">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</cell>
						</groupHeader>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column5"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
				<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
				<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="26" splitType="Stretch">
		<element kind="textField" uuid="3ac64444-5972-41c4-bb26-87f94e2bfbc4" x="227" y="0" width="100" height="8" fontName="IPAexGothic" fontSize="8.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
			<printWhenExpression><![CDATA[$F{emptyFlg}==false]]></printWhenExpression>
			<expression><![CDATA["- "+$V{PAGE_NUMBER}+" -"]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
