<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="U06080_Assessment_Sheet_N" language="java" columnCount="1" pageWidth="595" pageHeight="842" orientation="Landscape" columnWidth="550" leftMargin="44" rightMargin="1" topMargin="28" bottomMargin="20" uuid="95b152d9-156d-4073-8f78-d9eb987ca733" titleNewPage="true">
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="477"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="523"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="New Data Adapter"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="Table_TH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<dataset name="Dataset1" uuid="abb33cc0-06b7-4d36-980c-bda1cbea8d39">
		<field name="drugKnj" class="java.lang.String">
			<description><![CDATA[drugKnj]]></description>
		</field>
		<field name="n1B" class="java.lang.String">
			<description><![CDATA[n1B]]></description>
		</field>
		<field name="n1CKnj" class="java.lang.String">
			<description><![CDATA[n1CKnj]]></description>
		</field>
		<field name="n1D" class="java.lang.String">
			<description><![CDATA[n1D]]></description>
		</field>
		<field name="n1E" class="java.lang.String">
			<description><![CDATA[n1E]]></description>
		</field>
		<field name="n1F" class="java.lang.String">
			<description><![CDATA[n1F]]></description>
		</field>
	</dataset>
	<parameter name="InvoiceItems" class="net.sf.jasperreports.engine.JRDataSource"/>
	<field name="title" class="java.lang.String">
		<description><![CDATA[title]]></description>
	</field>
	<field name="syubetuKnj" class="java.lang.String">
		<description><![CDATA[syubetuKnj]]></description>
	</field>
	<field name="bunsyoKanriNo" class="java.lang.String">
		<description><![CDATA[bunsyoKanriNo]]></description>
	</field>
	<field name="assType" class="java.lang.Integer">
		<description><![CDATA[assType]]></description>
	</field>
	<field name="shiTeiDateGG" class="java.lang.String">
		<description><![CDATA[shiTeiDateGG]]></description>
	</field>
	<field name="jigyousha" class="java.lang.String">
		<description><![CDATA[jigyousha]]></description>
	</field>
	<field name="name1Knj" class="java.lang.String">
		<description><![CDATA[name1Knj]]></description>
	</field>
	<field name="name2Knj" class="java.lang.String">
		<description><![CDATA[name2Knj]]></description>
	</field>
	<field name="n1MemoKnj" class="java.lang.String">
		<description><![CDATA[n1MemoKnj]]></description>
	</field>
	<field name="n1MemoFont" class="java.lang.String">
		<description><![CDATA[n1MemoFont]]></description>
	</field>
	<field name="n1MemoColor" class="java.lang.String">
		<description><![CDATA[n1MemoColor]]></description>
	</field>
	<field name="n2MemoKnj" class="java.lang.String">
		<description><![CDATA[n2MemoKnj]]></description>
	</field>
	<field name="n2MemoFont" class="java.lang.String">
		<description><![CDATA[n2MemoFont]]></description>
	</field>
	<field name="n2MemoColor" class="java.lang.String">
		<description><![CDATA[n2MemoColor]]></description>
	</field>
	<field name="n3MemoKnj" class="java.lang.String">
		<description><![CDATA[n3MemoKnj]]></description>
	</field>
	<field name="n3MemoFont" class="java.lang.String">
		<description><![CDATA[n3MemoFont]]></description>
	</field>
	<field name="n3MemoColor" class="java.lang.String">
		<description><![CDATA[n3MemoColor]]></description>
	</field>
	<field name="n2" class="java.lang.String">
		<description><![CDATA[n2]]></description>
	</field>
	<field name="n3" class="java.lang.String">
		<description><![CDATA[n3]]></description>
	</field>
	<field name="yaKuzaiList" class="net.sf.jasperreports.engine.JRDataSource">
		<description><![CDATA[yaKuzaiList]]></description>
	</field>
	<field name="emptyFlg" class="java.lang.Boolean"/>
	<field name="shiTeiDateYY" class="java.lang.String">
		<description><![CDATA[shiTeiDateYY]]></description>
	</field>
	<field name="shiTeiDateMM" class="java.lang.String">
		<description><![CDATA[shiTeiDateMM]]></description>
	</field>
	<field name="shiTeiDateDD" class="java.lang.String">
		<description><![CDATA[shiTeiDateDD]]></description>
	</field>
	<field name="shiTeiKubun" class="java.lang.Integer">
		<description><![CDATA[shiTeiKubun]]></description>
	</field>
	<pageHeader height="69" splitType="Stretch"> 
		<element kind="staticText" uuid="896f173d-feb4-4bc8-8fd9-6999c32891dd" x="0" y="35" width="65" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<printWhenExpression><![CDATA[!$F{emptyFlg}]]></printWhenExpression>
			<text><![CDATA[利用者氏名:]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="7b0acfa4-063b-43f1-8bb4-8e6c22c91752" x="66" y="35" width="290" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<expression><![CDATA[$F{name1Knj} + " " + $F{name2Knj}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="892a40a1-6ec0-4d96-b41d-9cf6a7f48f03" x="0" y="0" width="182" height="9" fontName="ＭＳ Ｐゴシック" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
			<printWhenExpression><![CDATA[$F{bunsyoKanriNo}==null?false:true]]></printWhenExpression>
			<expression><![CDATA[$F{bunsyoKanriNo}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="f0227bf9-0d83-4933-907f-3b54c4a62827" x="0" y="0" width="82" height="20" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA[$F{syubetuKnj}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<box>
				<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="textField" uuid="c27920ab-c756-4ea2-bdb2-80d02c63e066" x="125" y="9" width="282" height="14" fontName="IPAexGothic" fontSize="14.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA[$F{title}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="a9db2498-b509-4bb2-9c94-2616ba6fbf4b" x="350" y="35" width="161" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right">
			<expression><![CDATA[$F{jigyousha}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="633a3693-35fb-43ac-be22-bd790ef5cc99" x="426" y="0" width="85" height="10" markup="html" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
			<printWhenExpression><![CDATA[($F{shiTeiKubun} != 1) && (!$F{emptyFlg})]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDateGG}+
"<font style='font-size:9pt'>"+$F{shiTeiDateYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{shiTeiDateMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{shiTeiDateDD}+"</font>"+
"日"]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<columnHeader height="187">
		<element kind="staticText" uuid="17290917-5e91-4151-b79c-af68774c06a9" x="0" y="0" width="310" height="12" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
			<text><![CDATA[N.薬剤]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="frame" uuid="c937efac-b6a4-4b85-80d8-c314a9b4a726" x="0" y="14" width="510" height="173">
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<element kind="frame" uuid="8e7d1c13-9aa5-4648-bed5-35cbf834228b" x="0" y="0" width="127" height="173">
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<element kind="staticText" uuid="ed1d2e1e-7a27-4186-8358-ba35d31cd2c2" x="6" y="4" width="117" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Bottom">
					<text><![CDATA[N1.全使用薬剤のリスト]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="textField" uuid="3f330cac-a324-44b9-9009-c3f0c79a31ed" x="3" y="15" width="122" height="155" forecolor="#030303" backcolor="#FFFFFF" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" printRepeatedValues="false">
					<expression><![CDATA[$V{PAGE_NUMBER}>1?"":$F{n1MemoKnj}]]></expression>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{n1MemoColor}]]></propertyExpression>
					<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{n1MemoFont}]]></propertyExpression>
					<box topPadding="4"/>
				</element>
				<box>
					<pen lineWidth="1.0"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="frame" uuid="d30b1e6e-a6cf-48eb-bbf4-ea31308b12b3" x="127" y="0" width="383" height="173">
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="staticText" uuid="509bd86e-9154-42bf-ac51-1bae64070595" x="6" y="132" width="330" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
					<text><![CDATA[e.回数(1回/日、3回/日など、頓用の場合、過去3日間に使用した回数)]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
				<element kind="staticText" uuid="a6a4f983-64ec-4008-bd71-8627a2aec2a1" x="6" y="144" width="310" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
					<text><![CDATA[f.頓用]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="staticText" uuid="28c81089-03e8-4204-ab33-a358646d9bc2" x="12" y="160" width="80" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
					<text><![CDATA[0.いいえ]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
				<element kind="staticText" uuid="a9b5a51a-1428-4e33-a9ab-550959f07085" x="92" y="160" width="80" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
					<text><![CDATA[1.はい]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="frame" uuid="338af336-bde0-4fb3-bdf8-0c7276e4f075" x="6" y="6" width="310" height="72">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="staticText" uuid="5276fde0-4cc4-40de-82c3-ac3bcdf51890" x="0" y="0" width="310" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
						<text><![CDATA[過去3日間に使用したすべての処方薬、非処方薬(市販薬)のリスト]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="staticText" uuid="add45304-05e3-44d3-bad9-b2d6058260c0" x="0" y="12" width="310" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
						<text><![CDATA[各薬剤について]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="staticText" uuid="8a8678ae-407c-4dd9-a0a9-a8b7eedd59bb" x="0" y="24" width="310" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
						<text><![CDATA[a.薬剤名]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<element kind="staticText" uuid="1ce7fad8-587c-41d2-ba8a-d26aa07c1e6f" x="0" y="36" width="310" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
						<text><![CDATA[b.1日量]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<element kind="staticText" uuid="5fee633e-fa7e-46cb-8d65-4ac3a1c30ad9" x="0" y="48" width="310" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
						<text><![CDATA[c.単位(cc、ml、mg、g、滴、押し、枚、単位など)]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
					<element kind="staticText" uuid="4ca7a2a4-0364-4097-8ecd-2b84db217a0c" x="0" y="60" width="310" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
						<text><![CDATA[d.経路]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
				</element>
				<element kind="frame" uuid="c9b55961-601a-4ca4-86d2-aafd180e8f13" x="12" y="82" width="350" height="44">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<element kind="staticText" uuid="0d97617e-35c3-4d31-94ec-e764d32a1153" x="0" y="0" width="130" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[1.経口（経口、舌下）]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="staticText" uuid="115a3549-98ea-4914-af76-cfe602e0a28e" x="131" y="0" width="170" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[2.注射（静注、皮下注、筋注）]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="staticText" uuid="6adf4821-5701-4058-ba1d-3dad005e8647" x="0" y="11" width="350" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[3.外用（座薬（坐剤、軟膏剤、浣腸など）、点眼、点鼻、]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="staticText" uuid="ffb03807-fa11-41d1-887f-2cf212be92cb" x="0" y="22" width="350" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[  外皮（塗布、貼付、スプレーなど）、口腔（含嗽、噴霧など））など]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="staticText" uuid="5dd1b2a1-47c5-4c66-9ccf-963403e1129a" x="0" y="33" width="350" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[4.経管（経鼻、PEG[胃ろう]など）その他]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</element>
				</element>
			</element>
			<box>
				<pen lineWidth="1.0"/>
				<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</columnHeader>
	<detail>
		<band height="321">
			<element kind="frame" uuid="cc96ea28-0396-427a-b162-24956bb49377" x="0" y="0" width="510" height="321">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<element kind="frame" uuid="811b1bfc-1c57-43e2-aa0f-4e8d022e0b81" positionType="Float" stretchType="ContainerHeight" x="0" y="0" width="500" height="310" removeLineWhenBlank="true" printRepeatedValues="false">
					<borderSplitType>DrawBorders</borderSplitType>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="component" uuid="2e610314-6316-4428-a3f0-ddd0bf98b739" positionType="Float" x="0" y="2" width="500" height="305" removeLineWhenBlank="true">
						<component kind="table">
							<datasetRun uuid="22fe2da3-c486-4c51-a716-f9117ee1ca2a" subDataset="Dataset1">
								<dataSourceExpression><![CDATA[$F{yaKuzaiList}]]></dataSourceExpression>
							</datasetRun>
							<columnHeader/>
							<column kind="single" uuid="55aa32fe-8ea6-4900-a274-b5acd170f62f" width="250">
								<columnHeader height="20" rowSpan="1" style="Table 1_CH">
									<element kind="staticText" uuid="3637bd7d-7423-4f30-bdef-b7a1947af733" stretchType="NoStretch" mode="Opaque" x="0" y="0" width="250" height="20" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[a.薬剤名]]></text>
										<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
									</element>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box style="Table 1_CH">
										<topPen lineWidth="2.0"/>
										<leftPen lineWidth="2.0"/>
										<bottomPen lineWidth="2.0"/>
										<rightPen lineWidth="1.0"/>
									</box>
								</columnHeader>
								<detailCell height="19" style="Table 1_TD">
									<element kind="textField" uuid="59e5effe-dbb6-4e07-ac04-ec5fb1fdeeae" x="0" y="0" width="250" height="19" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" vTextAlign="Middle">
										<expression><![CDATA[$F{drugKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
										<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
										<box leftPadding="4"/>
									</element>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box style="Table 1_TD">
										<pen lineWidth="2.0"/>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
							</column>
							<column kind="single" uuid="45b0ddd9-8bd4-43b6-87b1-ff79b5c0912e" width="50">
								<columnHeader height="20" rowSpan="1" style="Table 1_CH">
									<element kind="staticText" uuid="22abe7da-3239-449d-a48c-1f0819a4031c" mode="Opaque" x="0" y="0" width="50" height="20" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[b.1日量]]></text>
									</element>
									<box style="Table 1_CH">
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</columnHeader>
								<detailCell height="19" style="Table 1_TD">
									<element kind="textField" uuid="bf6c17ac-11f8-46c4-8dd0-ed7241f22b61" x="0" y="0" width="50" height="19" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
										<expression><![CDATA[$F{n1B}]]></expression>
									</element>
									<box style="Table 1_TD">
										<pen lineWidth="2.0"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="1.0"/>
										<bottomPen lineColor="#030303"/>
										<rightPen lineWidth="1.0"/>
									</box>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
							</column>
							<column kind="single" uuid="5a6dd10b-b401-406c-9ba6-49ae014e8612" width="68">
								<columnHeader height="20" rowSpan="1" style="Table 1_CH">
									<element kind="staticText" uuid="21cae561-7349-4b9d-96f6-5221b414ac02" mode="Opaque" x="0" y="0" width="68" height="20" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[c.単位]]></text>
									</element>
									<box style="Table 1_CH">
										<topPen lineWidth="2.0"/>
										<leftPen lineWidth="1.0"/>
										<bottomPen lineWidth="2.0" lineColor="#000000"/>
										<rightPen lineWidth="1.0"/>
									</box>
								</columnHeader>
								<detailCell height="19" style="Table 1_TD">
									<element kind="textField" uuid="0fe3c3f6-eb55-4746-b993-9676f053a375" x="0" y="0" width="68" height="19" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
										<expression><![CDATA[$F{n1CKnj}]]></expression>
										<box leftPadding="4"/>
									</element>
									<box style="Table 1_TD">
										<pen lineWidth="2.0"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="1.0"/>
										<bottomPen lineColor="#030303"/>
										<rightPen lineWidth="1.0"/>
									</box>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
							</column>
							<column kind="single" uuid="abda14ff-b537-4791-a10d-54935121f0ef" width="44">
								<columnHeader height="20" rowSpan="1" style="Table 1_CH">
									<element kind="staticText" uuid="0c29a2b2-7a6d-447f-8fe7-8c59ccf31132" mode="Opaque" x="0" y="0" width="44" height="20" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[d.経路]]></text>
									</element>
									<box style="Table 1_CH">
										<topPen lineWidth="2.0"/>
										<leftPen lineWidth="1.0"/>
										<bottomPen lineWidth="2.0"/>
										<rightPen lineWidth="1.0"/>
									</box>
								</columnHeader>
								<detailCell height="19" style="Table 1_TD">
									<element kind="textField" uuid="963417b2-b293-4420-bfc4-e6f5e8fc9cff" x="0" y="0" width="44" height="19" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
										<expression><![CDATA[$F{n1D}]]></expression>
									</element>
									<box style="Table 1_TD">
										<pen lineWidth="2.0"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="1.0"/>
										<bottomPen lineColor="#030303"/>
										<rightPen lineWidth="1.0"/>
									</box>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
							</column>
							<column kind="single" uuid="8a2d7f65-3ef7-461f-bbec-0fdc4aab2e60" width="44">
								<columnHeader height="20" rowSpan="1" style="Table 1_CH">
									<element kind="staticText" uuid="45caaf02-ed09-4d14-a2a5-620dcb26b27a" mode="Opaque" x="0" y="0" width="44" height="20" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[e.回数]]></text>
									</element>
									<box style="Table 1_CH">
										<topPen lineWidth="2.0"/>
										<leftPen lineWidth="1.0"/>
										<bottomPen lineWidth="2.0"/>
										<rightPen lineWidth="1.0"/>
									</box>
								</columnHeader>
								<detailCell height="19" style="Table 1_TD">
									<element kind="textField" uuid="7b0b15d8-c9db-4a74-a1fc-cf266324ce69" x="0" y="0" width="44" height="19" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
										<expression><![CDATA[$F{n1E}]]></expression>
									</element>
									<box style="Table 1_TD">
										<pen lineWidth="2.0"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="1.0"/>
										<bottomPen lineColor="#030303"/>
										<rightPen lineWidth="1.0"/>
									</box>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column5"/>
							</column>
							<column kind="single" uuid="34c6aca9-689a-4b3b-87c9-4f21f2eb4589" width="44">
								<columnHeader height="20" rowSpan="1" style="Table 1_CH">
									<element kind="staticText" uuid="86f1622c-1ffb-4fc9-91de-37e83f947c4a" mode="Opaque" x="0" y="0" width="44" height="20" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[f.頓用]]></text>
									</element>
									<box style="Table 1_CH">
										<topPen lineWidth="2.0"/>
										<leftPen lineWidth="1.0"/>
										<bottomPen lineWidth="2.0"/>
										<rightPen lineWidth="2.0"/>
									</box>
								</columnHeader>
								<detailCell height="19" style="Table 1_TD">
									<element kind="textField" uuid="287d98bf-cf2b-4cfe-bc78-c76c16ae52bf" x="0" y="0" width="44" height="19" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
										<expression><![CDATA[$F{n1F}]]></expression>
										<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
									</element>
									<box style="Table 1_TD">
										<pen lineWidth="2.0"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="1.0"/>
										<bottomPen lineColor="#030303"/>
										<rightPen lineWidth="2.0"/>
									</box>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column6"/>
							</column>
						</component>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
						<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
						<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
						<property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.components.autoresize.next" value="true"/>
						<property name="net.sf.jasperreports.export.xls.ignore.cell.border" value="true"/>
					</element>
					<box padding="0">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<box padding="0" topPadding="5" leftPadding="5" bottomPadding="5" rightPadding="5">
					<leftPen lineWidth="2.0"/>
					<rightPen lineWidth="2.0"/>
				</box>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<columnFooter height="106">
		<element kind="frame" uuid="453334f2-bdcb-438b-82e3-388993c519bd" stretchType="ContainerHeight" x="0" y="0" width="510" height="106" removeLineWhenBlank="true">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<element kind="frame" uuid="f134d871-c483-42c9-90b9-97aac72c635a" positionType="Float" stretchType="ContainerHeight" x="0" y="0" width="510" height="47" removeLineWhenBlank="true">
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="0"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="true"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="frame" uuid="*************-48e8-8e56-df62265d2c2a" x="0" y="0" width="127" height="47">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="staticText" uuid="c79835a5-1387-4bf1-9518-61f397d0462b" x="6" y="4" width="117" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
						<text><![CDATA[N2.薬のアレルギー]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</element>
					<element kind="textField" uuid="70f0e84a-d914-4e2b-a64a-1165fb037157" x="3" y="15" width="122" height="29" forecolor="#030303" backcolor="#FFFFFF" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" printRepeatedValues="false">
						<expression><![CDATA[$V{PAGE_NUMBER}>1?"":$F{n2MemoKnj}]]></expression>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{n2MemoColor}]]></propertyExpression>
						<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{n2MemoFont}]]></propertyExpression>
						<box topPadding="4"/>
					</element>
					<box>
						<pen lineStyle="Solid"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="dc1e445f-9908-461f-8835-e9c09c2f14fd" x="127" y="0" width="383" height="47">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="staticText" uuid="1df9ed7d-e43d-47f2-bd1b-0525d29c7cec" x="6" y="6" width="320" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[0.わかっている薬剤アレルギーはない]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</element>
					<element kind="staticText" uuid="b7005b2c-edce-4365-9318-c0ebce452ba6" x="6" y="18" width="320" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[1.ある]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</element>
					<element kind="textField" uuid="8430d728-b7d2-4072-89b9-c44a8ca94405" x="337" y="31" width="32" height="11" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<expression><![CDATA[$V{PAGE_NUMBER}>1?"":$F{n2}]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box>
							<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
			</element>
			<element kind="frame" uuid="d060ef60-491d-443a-aa91-e8f0c4a4bc66" positionType="Float" stretchType="ContainerHeight" x="0" y="47" width="510" height="59" removeLineWhenBlank="true">
				<printWhenExpression><![CDATA[$F{assType}==1]]></printWhenExpression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.layout.grid.x" value="-1"/>
				<property name="com.jaspersoft.layout.grid.y" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
				<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
				<property name="com.jaspersoft.layout.grid.weight.fixed" value="true"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="frame" uuid="af047364-9a8e-472b-8c06-caa452196fd0" x="0" y="0" width="127" height="59">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="staticText" uuid="f799e8f8-2adb-49b7-9786-39f177e134a1" x="6" y="4" width="117" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
						<text><![CDATA[N3.処方薬の順守]]></text>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</element>
					<element kind="textField" uuid="ec0aa404-db56-4a51-adc6-74f309093906" x="3" y="16" width="122" height="40" forecolor="#030303" backcolor="#FFFFFF" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
						<expression><![CDATA[$V{PAGE_NUMBER}>1?"":$F{n3MemoKnj}]]></expression>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{n3MemoColor}]]></propertyExpression>
						<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{n3MemoFont}]]></propertyExpression>
						<box topPadding="4"/>
					</element>
					<box>
						<pen lineStyle="Solid"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="*************-482a-a921-8bb5771ba071" x="127" y="0" width="383" height="59">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="textField" uuid="c90b04df-d671-4b54-b877-5b9428f6590d" x="337" y="40" width="32" height="11" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<expression><![CDATA[$V{PAGE_NUMBER}>1?"":$F{n3}]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box>
							<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="e8d43f61-6983-4ec4-a112-d9455df30195" x="6" y="4" width="320" height="48">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="143c4a67-2e01-44db-ada9-4895b540112b" x="0" y="0" width="320" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[0.常に従う]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</element>
						<element kind="staticText" uuid="f0ba270c-769f-48be-bb10-273c2954dab7" x="0" y="12" width="320" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[1.80%以上は従っている]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</element>
						<element kind="staticText" uuid="94d3056b-53e3-4266-bdce-b0c8876bd16d" x="0" y="24" width="320" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[2.80％未満しか従っていない、処方薬を取りに行き損ねたことも含む]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</element>
						<element kind="staticText" uuid="ad376c19-c3aa-4b4a-a724-b4204984dbb1" x="0" y="36" width="320" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[8.薬剤は処方されていない]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</element>
					</element>
				</element>
			</element>
			<box>
				<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</columnFooter>
	<pageFooter height="111">
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
