<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="V00261_limitExceededUsercCalculationErrorListReport" language="java" columnCount="1" pageWidth="842" pageHeight="595" columnWidth="745" leftMargin="34" rightMargin="63" topMargin="45" bottomMargin="30" uuid="05680462-fe6b-404e-8cef-1149ab862db4">
	<style name="Gothic11Left" hTextAlign="Left" vTextAlign="Middle" rotation="None" fontName="IPAexGothic" fontSize="11.0"/>
	<style name="Style1" mode="Opaque" backcolor="#EBEBEB">
		<box>
			<bottomPen lineWidth="1.6"/>
		</box>
	</style>
	<style name="Table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<dataset name="Dataset1" uuid="ca88f1e9-8f3e-4f2f-9e07-a8b6eec3617d">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
		<parameter name="dispFlg" class="java.lang.Integer"/>
		<parameter name="shuGendoDispFlg" class="java.lang.Integer"/>
		<parameter name="fullDispFlg" class="java.lang.Integer"/>
		<query language="sql"><![CDATA[]]></query>
		<field name="selfId" class="java.lang.Integer"/>
		<field name="userName" class="java.lang.String"/>
		<field name="sex" class="java.lang.String"/>
		<field name="hokenKnj" class="java.lang.String"/>
		<field name="hiHoken" class="java.lang.String"/>
		<field name="shuOver" class="java.lang.String"/>
		<field name="shuNai" class="java.lang.String"/>
		<field name="kbnOver" class="java.lang.String"/>
		<field name="kbnNai" class="java.lang.String"/>
		<field name="errKnj" class="java.lang.String"/>
	</dataset>
	<query language="sql"><![CDATA[]]></query>
	<field name="title" class="java.lang.String"/>
	<field name="shiTeiKubun" class="java.lang.Integer"/>
	<field name="shiTeiDateGG" class="java.lang.String"/>
	<field name="shiTeiDateYY" class="java.lang.String"/>
	<field name="shiTeiDateMM" class="java.lang.String"/>
	<field name="shiTeiDateDD" class="java.lang.String"/>
	<field name="jigyoName" class="java.lang.String"/>
	<field name="henkouYmdDispFlg" class="java.lang.Integer"/>
	<field name="henkouStartYmdGG" class="java.lang.String"/>
	<field name="henkouStartYmdYY" class="java.lang.String"/>
	<field name="henkouStartYmdMM" class="java.lang.String"/>
	<field name="henkouEndYmdGG" class="java.lang.String"/>
	<field name="henkouEndYmdYY" class="java.lang.String"/>
	<field name="henkouEndYmdMM" class="java.lang.String"/>
	<field name="dispFlg" class="java.lang.Integer"/>
	<field name="fullDispFlg" class="java.lang.Integer"/>
	<field name="shuGendoDispFlg" class="java.lang.Integer"/>
	<field name="itiranDetailList" class="net.sf.jasperreports.engine.JRDataSource"/>
	<field name="itiranDetailList1" class="net.sf.jasperreports.engine.JRDataSource"/>
	<field name="bunsyoKanriNo" class="java.lang.String"/>
	<pageHeader height="60" splitType="Stretch">
		<element kind="textField" uuid="1cfa29a7-0f44-4b82-ba14-a326a0413603" x="640" y="0" width="105" height="11" markup="html" fontName="IPAexMincho" fontSize="9.0" hTextAlign="Right" vTextAlign="Middle">
			<printWhenExpression><![CDATA[($F{shiTeiKubun} != 1) ]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDateGG}+
"<font style='font-size:8pt'>"+$F{shiTeiDateYY}+"</font>"+
"年"+
"<font style='font-size:8pt'>"+$F{shiTeiDateMM}+"</font>"+
"月"+
"<font style='font-size:8pt'>"+$F{shiTeiDateDD}+"</font>"+
"日"
]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<box rightPadding="1"/>
		</element>
		<element kind="textField" uuid="cc025d58-ea9c-4d7e-96ec-00ccee2725ee" x="487" y="11" width="258" height="12" fontName="IPAexGothic" fontSize="10.0" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[$F{jigyoName}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box rightPadding="1"/>
		</element>
		<element kind="textField" uuid="4c306cd1-8cf4-452d-9a0b-ab7e72db734b" x="3" y="47" width="216" height="12" markup="html" fontName="IPAexGothic" fontSize="10.0" vTextAlign="Middle">
			<printWhenExpression><![CDATA[$F{henkouYmdDispFlg}!=1]]></printWhenExpression>
			<expression><![CDATA[$F{henkouStartYmdGG}+
"<font style='font-size:9pt'>"+$F{henkouStartYmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{henkouStartYmdMM}+"</font>"+
"月"+" 分"
]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="bec198f0-5ed1-4aba-99e0-0fe5c2c0b22e" x="3" y="47" width="216" height="12" markup="html" fontName="IPAexGothic" fontSize="10.0" vTextAlign="Middle">
			<printWhenExpression><![CDATA[$F{henkouYmdDispFlg}==1]]></printWhenExpression>
			<expression><![CDATA[$F{henkouStartYmdGG}+
"<font style='font-size:9pt'>"+$F{henkouStartYmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{henkouStartYmdMM}+"</font>"+
"月"+" ～ "+$F{henkouEndYmdGG}+
"<font style='font-size:9pt'>"+$F{henkouEndYmdYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{henkouEndYmdMM}+"</font>"+
"月"+" 分"
]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="d66ce545-7bed-42b8-bac8-b467e9e3cb08" x="86" y="23" width="569" height="24" fontName="IPAexGothic" fontSize="18.0" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA[$F{title}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box rightPadding="1"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="73">
			<printWhenExpression><![CDATA[$F{dispFlg}==2||$F{fullDispFlg}==1]]></printWhenExpression>
			<element kind="component" uuid="29da7336-6828-4ae1-8c87-bb888f8197d2" stretchType="ContainerHeight" x="0" y="5" width="745" height="68" removeLineWhenBlank="true">
				<component kind="table">
					<datasetRun uuid="ffa9e7e0-8a82-4e5b-8b9f-c8da7fa0e0b1" subDataset="Dataset1">
						<dataSourceExpression><![CDATA[$F{itiranDetailList}]]></dataSourceExpression>
						<parameter name="dispFlg">
							<expression><![CDATA[$F{dispFlg}]]></expression>
						</parameter>
						<parameter name="shuGendoDispFlg">
							<expression><![CDATA[$F{shuGendoDispFlg}]]></expression>
						</parameter>
					</datasetRun>
					<column kind="single" uuid="8f945653-e1bf-4b6e-82e5-8d3dbd4ce01e" width="745">
						<columnHeader height="40" rowSpan="1">
							<element kind="staticText" uuid="98b80b39-9f7a-47b7-9a63-b1d8d432729b" mode="Transparent" x="0" y="16" width="53" height="24" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[利用者番号]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="72f07b61-e8f4-4182-8e5a-02fc2f50dcef" mode="Transparent" x="53" y="16" width="102" height="24" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
								<text><![CDATA[利用者氏名]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box leftPadding="9">
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="89646a46-aca9-40d3-ab44-930e60bbd895" mode="Transparent" x="155" y="16" width="32" height="24" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[性別]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="7dac7b38-0c86-498e-9419-845310c583ef" mode="Transparent" x="187" y="16" width="92" height="24" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
								<text><![CDATA[保険者名]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box leftPadding="5">
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="88335b5a-c8b6-4fea-bea4-ed8c038fad4d" mode="Transparent" x="279" y="16" width="63" height="24" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[被保険者番号]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<box>
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="aba75053-16f5-4b9a-bee6-c6f5e9ad8213" mode="Transparent" x="342" y="28" width="75" height="12" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[区分限度超過]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="80ccfeaa-dc77-4027-b3ef-73fed6158189" mode="Transparent" x="417" y="28" width="75" height="12" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[区分限度内]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="0c184dcf-049b-47dd-996a-b013e246b2ed" mode="Transparent" x="492" y="16" width="253" height="24" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
								<text><![CDATA[備  考]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box topPadding="5" leftPadding="9">
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<element kind="frame" uuid="8cdef3e9-76bc-451d-b27d-07619d69cc2d" x="342" y="16" width="75" height="12">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<element kind="staticText" uuid="715ce9f6-2580-457d-b1db-4c48302fa228" mode="Transparent" x="0" y="0" width="75" height="12" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{shuGendoDispFlg}==1]]></printWhenExpression>
									<text><![CDATA[種類限度超過]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<pen lineWidth="1.0" lineStyle="Dashed"/>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
										<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted"/>
								</box>
							</element>
							<element kind="frame" uuid="0d649549-ad03-49e8-a209-01cc8d58c97c" x="417" y="16" width="75" height="12">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<element kind="staticText" uuid="b43ec357-578c-40c5-861b-55c2fb83f4ca" mode="Transparent" x="0" y="0" width="75" height="12" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{shuGendoDispFlg}==1]]></printWhenExpression>
									<text><![CDATA[種類限度内]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<pen lineWidth="1.0" lineStyle="Dashed"/>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
										<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted"/>
								</box>
							</element>
							<element kind="frame" uuid="c9b0fc2e-a963-468d-8a01-2f69bf627100" x="0" y="0" width="745" height="16">
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="staticText" uuid="a30b828b-67f8-45ff-ab4d-91591fe6993e" mode="Transparent" x="0" y="0" width="745" height="16" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
									<text><![CDATA[限度額を超過した利用者]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box leftPadding="2"/>
								</element>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</columnHeader>
						<detailCell height="23">
							<element kind="textField" uuid="48466e32-a6c4-4ffb-8962-cffdd1f88deb" x="0" y="0" width="53" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{selfId}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box rightPadding="3">
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="ad170b4b-9c71-47d2-9cce-e70e0311bb18" x="53" y="0" width="102" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Left" vTextAlign="Top">
								<expression><![CDATA[$F{userName}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box topPadding="1" leftPadding="3">
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="9e2dfb06-b9c1-4105-b671-2ff7c0441c40" x="155" y="0" width="32" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{sex}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="c701828a-0a3c-4d4a-a93d-65875ffd0f47" x="187" y="0" width="92" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Left" vTextAlign="Top">
								<expression><![CDATA[$F{hokenKnj}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box topPadding="1" leftPadding="3">
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="e31eb7d2-4814-43d3-a7b6-63cf764c7985" x="279" y="0" width="63" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{hiHoken}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="374fd843-0cc6-423d-ade0-8ca01e93ce8e" x="417" y="12" width="75" height="11" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kbnNai}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box rightPadding="2">
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="7e3f28c8-c25e-4710-a0c2-08d0caa79d55" x="342" y="12" width="75" height="11" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kbnOver}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box rightPadding="2">
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="564e12bb-e31d-4dc7-9fdd-************" x="492" y="0" width="253" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Left" vTextAlign="Top">
								<expression><![CDATA[$F{errKnj}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box topPadding="1" leftPadding="2">
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<element kind="frame" uuid="eaaf81e4-bd57-4c90-bf9c-1c2cafd5cd24" x="342" y="0" width="75" height="12">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="textField" uuid="fe517f5b-6ca4-4c3d-829b-be8a73141da8" x="0" y="0" width="75" height="12" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{shuGendoDispFlg}==1]]></printWhenExpression>
									<expression><![CDATA[$F{shuOver}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box rightPadding="2">
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
										<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted"/>
								</box>
							</element>
							<element kind="frame" uuid="33a2f40c-b4e0-4501-9e16-ce9d471df66a" x="417" y="0" width="75" height="12">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="textField" uuid="a23106e1-9468-4110-a2e4-c8c495fc77b8" x="0" y="0" width="75" height="12" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{shuGendoDispFlg}==1]]></printWhenExpression>
									<expression><![CDATA[$F{shuNai}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box rightPadding="2">
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
										<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
				<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
				<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="73">
			<printWhenExpression><![CDATA[$F{dispFlg}==3||$F{fullDispFlg}==1]]></printWhenExpression>
			<element kind="component" uuid="bde0bd8b-9c0b-460d-b9fa-bd0445bf99b7" stretchType="ContainerHeight" x="0" y="5" width="745" height="68" removeLineWhenBlank="true">
				<component kind="table">
					<datasetRun uuid="c667fd44-e680-48fb-b3ae-d657dbe7780b" subDataset="Dataset1">
						<dataSourceExpression><![CDATA[$F{itiranDetailList1}]]></dataSourceExpression>
						<parameter name="shuGendoDispFlg">
							<expression><![CDATA[$F{shuGendoDispFlg}]]></expression>
						</parameter>
						<parameter name="dispFlg">
							<expression><![CDATA[$F{dispFlg}]]></expression>
						</parameter>
					</datasetRun>
					<column kind="single" uuid="5d9c53f1-0810-4fae-9d16-73e44fc41e78" width="745">
						<columnHeader height="40" rowSpan="1">
							<element kind="staticText" uuid="a8bd797c-463f-430c-b9a5-e26588b95b17" mode="Transparent" x="0" y="16" width="53" height="24" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[利用者番号]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="7c72ed24-3464-4b7a-854d-2e564ca8959b" mode="Transparent" x="53" y="16" width="102" height="24" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
								<text><![CDATA[利用者氏名]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box leftPadding="9">
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="bb45c9ce-0675-498d-a58b-8e42d02b7228" mode="Transparent" x="155" y="16" width="32" height="24" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[性別]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="cb623bb1-0676-42ca-9a0e-7c41d896ca97" mode="Transparent" x="187" y="16" width="92" height="24" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
								<text><![CDATA[保険者名]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box leftPadding="5">
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="4a4b65d8-3eb4-4506-bf46-5927c086fe33" mode="Transparent" x="279" y="16" width="63" height="24" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[被保険者番号]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<box>
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="a0b524fc-3cd4-4525-a971-f7c7b60f596e" mode="Transparent" x="342" y="28" width="75" height="12" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[区分限度超過]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="efd2038b-9b03-47bd-abaa-4173c3c854b8" mode="Transparent" x="417" y="28" width="75" height="12" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[区分限度内]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<pen lineWidth="1.0" lineStyle="Dashed"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="b4ee1fdb-f547-4ac6-a69c-7e9a65c15b4f" mode="Transparent" x="492" y="16" width="253" height="24" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
								<text><![CDATA[エラー内容]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box topPadding="5" leftPadding="9">
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<element kind="frame" uuid="8f6d23b8-f139-4bf8-a831-2fa31aa1b382" x="342" y="16" width="75" height="12">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="staticText" uuid="eb4c28a5-c23f-4e2d-bade-4bbbc257043f" mode="Transparent" x="0" y="0" width="75" height="12" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{shuGendoDispFlg}==1]]></printWhenExpression>
									<text><![CDATA[種類限度超過]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<pen lineWidth="1.0" lineStyle="Dashed"/>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
										<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted"/>
								</box>
							</element>
							<element kind="frame" uuid="28e03714-18d0-41f1-8056-c189d0353286" x="417" y="16" width="75" height="12">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="staticText" uuid="6334db19-6767-41b7-a8d6-bf0a5f1da599" mode="Transparent" x="0" y="0" width="75" height="12" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{shuGendoDispFlg}==1]]></printWhenExpression>
									<text><![CDATA[種類限度内]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<pen lineWidth="1.0" lineStyle="Dashed"/>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
										<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted"/>
								</box>
							</element>
							<element kind="frame" uuid="a0ad842e-411f-45ce-913a-6d29f3dc82e4" x="0" y="0" width="745" height="16">
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="staticText" uuid="4d655a58-f821-48b7-9fea-a5f35f2294e5" mode="Transparent" x="0" y="0" width="744" height="16" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
									<text><![CDATA[計算に失敗した利用者]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box leftPadding="2"/>
								</element>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</columnHeader>
						<detailCell height="24">
							<element kind="textField" uuid="54e3d6c8-f9a6-4821-80e6-2039e43491d7" x="0" y="0" width="53" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{selfId}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box rightPadding="3">
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="9cf0e76d-76f1-438b-9a58-a03af8de7117" x="53" y="0" width="102" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Left" vTextAlign="Top">
								<expression><![CDATA[$F{userName}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box topPadding="1" leftPadding="3">
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="3c85bf59-eaa9-44b0-accf-c89d4172ede3" x="155" y="0" width="32" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{sex}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="969300f5-83c0-4bf9-94e5-88deb4c38440" x="187" y="0" width="92" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Left" vTextAlign="Top">
								<expression><![CDATA[$F{hokenKnj}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box topPadding="1" leftPadding="3">
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="32c9f83f-236c-44b0-8e42-e6287a75aa05" x="279" y="0" width="63" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{hiHoken}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="63ca3f17-2d95-4094-90f3-087b85029401" x="417" y="12" width="75" height="12" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kbnNai}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box rightPadding="2">
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="7e40e560-8214-4727-a009-98af917aabfd" x="342" y="12" width="75" height="12" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kbnOver}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box rightPadding="2">
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="8b5b25ff-f7da-4b72-804f-5472c751dbb7" x="492" y="0" width="253" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Left" vTextAlign="Top">
								<expression><![CDATA[$F{errKnj}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box topPadding="1" leftPadding="2">
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<element kind="frame" uuid="f0b8c809-4266-44ed-b42a-54d387933261" x="342" y="0" width="75" height="12">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="textField" uuid="ab24d9e8-dc1d-4814-a6a3-b5457b3103a7" x="0" y="0" width="75" height="12" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{shuGendoDispFlg}==1]]></printWhenExpression>
									<expression><![CDATA[$F{shuOver}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box rightPadding="2">
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
										<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted"/>
								</box>
							</element>
							<element kind="frame" uuid="0341a0ff-20a9-41bc-b5f2-d5e4e2fdbf45" x="417" y="0" width="75" height="12">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="textField" uuid="1b15c9c9-20d0-4de6-9cf6-b2dcb2813d58" x="0" y="0" width="75" height="12" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$P{shuGendoDispFlg}==1]]></printWhenExpression>
									<expression><![CDATA[$F{shuNai}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box rightPadding="2">
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
										<rightPen lineWidth="0.75" lineStyle="Dotted" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<pen lineWidth="0.75"/>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
									<rightPen lineWidth="0.75" lineStyle="Dotted"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
				<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
				<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="17">
		<element kind="textField" uuid="a1065ce4-c521-4b85-8ee7-05c06e4d8369" x="358" y="3" width="17" height="13" fontName="IPAexGothic" fontSize="9.0" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[ $V{PAGE_NUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="textField" uuid="da7b282a-324a-4eeb-8c5d-3064f18a5819" x="375" y="3" width="20" height="13" markup="html" fontName="IPAexGothic" fontSize="9.0" evaluationTime="Report" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["<font style='font-size:10pt'>"+" / "+"</font>" + $V{PAGE_NUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="textField" uuid="8ab0766e-217f-4bcc-ad3a-b5a81522a565" x="0" y="2" width="150" height="12" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$F{bunsyoKanriNo}]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="line" uuid="66aa7140-2234-42e1-8bdc-b63fe91a24b7" x="0" y="0" width="745" height="2">
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<pen lineWidth="1.5"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
