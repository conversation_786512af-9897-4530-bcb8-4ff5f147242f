<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="U01020_MonitoringEvaluationReport" language="java" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="755" leftMargin="57" rightMargin="30" topMargin="33" bottomMargin="6" uuid="05680462-fe6b-404e-8cef-1149ab862db4">
	<dataset name="Dataset1" uuid="baf694c5-286e-43ee-a7ef-8916731d3838">
		<field name="mokuhyoKnj" class="java.lang.String"/>
		<field name="kikanKnj" class="java.lang.String"/>
		<field name="jyoukyouKnj" class="java.lang.String"/>
		<field name="userHyouka" class="java.lang.String"/>
		<field name="userHyoukaKnj" class="java.lang.String"/>
		<field name="tantoHyouka" class="java.lang.String"/>
		<field name="tantoHyoukaKnj" class="java.lang.String"/>
		<field name="taiou" class="java.lang.String"/>
		<field name="taiouKnj" class="java.lang.String"/>
	</dataset>
	<dataset name="Dataset2" uuid="baf694c5-286e-43ee-a7ef-8916731d3839">
		<field name="levelName" class="java.lang.String"/>
		<field name="umuFlg" class="java.lang.String"/>
		<field name="henkouRetryKnj" class="java.lang.String"/>
	</dataset>
	<field name="title" class="java.lang.String"/>
	<field name="shiteiDatePrintKbn" class="java.lang.Integer"/>
	<field name="shiTeiDateGG" class="java.lang.String"/>
	<field name="shiTeiDateYY" class="java.lang.String"/>
	<field name="shiTeiDateMM" class="java.lang.String"/>
	<field name="shiTeiDateDD" class="java.lang.String"/>
	<field name="jigyoName" class="java.lang.String"/>
	<field name="fullName" class="java.lang.String"/>
	<field name="keisho" class="java.lang.String"/>
	<field name="createYmdGG" class="java.lang.String"/>
	<field name="createYmdYY" class="java.lang.String"/>
	<field name="createYmdMM" class="java.lang.String"/>
	<field name="createYmdDD" class="java.lang.String"/>
	<field name="shokaiYmdGG" class="java.lang.String"/>
	<field name="shokaiYmdYY" class="java.lang.String"/>
	<field name="shokaiYmdMM" class="java.lang.String"/>
	<field name="shokaiYmdDD" class="java.lang.String"/>
	<field name="hyoujiKbn" class="java.lang.Integer"/>
	<field name="hanko1Knj" class="java.lang.String"/>
	<field name="hanko2Knj" class="java.lang.String"/>
	<field name="hanko3Knj" class="java.lang.String"/>
	<field name="hanko4Knj" class="java.lang.String"/>
	<field name="hanko5Knj" class="java.lang.String"/>
	<field name="hanko6Knj" class="java.lang.String"/>
	<field name="hanko7Knj" class="java.lang.String"/>
	<field name="hanko8Knj" class="java.lang.String"/>
	<field name="hanko9Knj" class="java.lang.String"/>
	<field name="hanko10Knj" class="java.lang.String"/>
	<field name="hanko11Knj" class="java.lang.String"/>
	<field name="hanko12Knj" class="java.lang.String"/>
	<field name="hanko13Knj" class="java.lang.String"/>
	<field name="hanko14Knj" class="java.lang.String"/>
	<field name="hanko15Knj" class="java.lang.String"/>
	<field name="caseNo" class="java.lang.String"/>
	<field name="emptyFlg" class="java.lang.Boolean"/>
	<field name="bunsyoKanriNo" class="java.lang.String"/>
	<field name="needsRetryList" class="net.sf.jasperreports.engine.JRDataSource"/>
	<field name="moniList" class="net.sf.jasperreports.engine.JRDataSource"/>
	<field name="subReportPath" class="java.lang.String"/>
	<field name="subReportDataDs" class="net.sf.jasperreports.engine.JRDataSource"/>
	<field name="shoninFlg" class="java.lang.String"/>
	<pageHeader height="102" splitType="Stretch">
		<element kind="textField" uuid="1cfa29a7-0f44-4b82-ba14-a326a0413603" x="618" y="0" width="120" height="12" markup="html" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
			<printWhenExpression><![CDATA[($F{shiteiDatePrintKbn}!=1)&&(!$F{emptyFlg})]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDateGG}+
"<font style='font-size:9pt'>"+$F{shiTeiDateYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{shiTeiDateMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{shiTeiDateDD}+"</font>"+
"日"]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<element kind="textField" uuid="cc025d58-ea9c-4d7e-96ec-00ccee2725ee" x="468" y="13" width="270" height="12" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[$F{jigyoName}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<element kind="staticText" uuid="08263cf6-d89a-4e2e-bc36-fb9401531236" x="0" y="39" width="46" height="12" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<text><![CDATA[氏名]]></text>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="frame" uuid="0d08f649-ca42-45ff-968d-d1419e6b3189" x="46" y="39" width="31" height="12">
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="textField" uuid="a324a340-ae5d-4158-b04a-0b6021c71756" x="77" y="39" width="114" height="12" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<expression><![CDATA[$F{fullName}]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="textField" uuid="33d770ee-e7f9-4620-a16d-2eb5c3bb6645" x="191" y="39" width="12" height="12" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<expression><![CDATA[$F{keisho}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="frame" uuid="0d45508a-2566-4a12-9afc-e63732885e2d" positionType="Float" x="0" y="55" width="707" height="47" removeLineWhenBlank="true">
			<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&&$V{PAGE_NUMBER} == 1]]></printWhenExpression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<element kind="textField" uuid="2df386e1-99ac-42ad-8676-a2a79eca6007" positionType="Float" x="0" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko1Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko1Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="10389e82-0990-4646-8ca2-5b4e8d9de48a" positionType="Float" x="0" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko1Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="ebfebe5a-090f-4bac-8115-1bdfd15589ec" positionType="Float" x="47" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&& !$F{hanko2Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko2Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="8496687e-09ae-4938-ab2e-3a9a187f27ee" positionType="Float" x="47" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&& !$F{hanko2Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="98cc5fdb-c813-4d0a-b219-787e806e4838" positionType="Float" x="94" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko3Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko3Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="bb062468-f399-42b9-8833-39201cb714e2" positionType="Float" x="94" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko3Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="6f08db02-ba9d-4fba-8009-49da56c0b6dc" positionType="Float" x="141" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko4Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko4Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="f8ce0370-2fcc-4770-a3e7-a46e638daf81" positionType="Float" x="141" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko4Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="39564c39-87a9-4103-ba70-696efa50b6b5" positionType="Float" x="188" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko5Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko5Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="6c31752e-cb89-490d-9b99-9e7d53eda02e" positionType="Float" x="188" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko5Knj}.isEmpty() && $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="f867d067-1c69-498e-8ae5-5d8660d964b2" positionType="Float" x="235" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko6Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko6Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="f1ed161e-7ad4-43fc-9499-aa4f0699fe5e" positionType="Float" x="235" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&& !$F{hanko6Knj}.isEmpty() && $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="f46ec92f-8477-4c4c-b4a1-8f294fd3c19d" positionType="Float" x="282" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&&  !$F{hanko7Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko7Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="5170d191-dab5-4bb2-9b89-bf857963563d" positionType="Float" x="282" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&&  !$F{hanko7Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="4e13c3ce-8803-4454-b019-0406d38fd385" positionType="Float" x="329" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko8Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko8Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="956f872b-9d88-49fe-a384-98cad627e0a9" positionType="Float" x="329" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko8Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="9759824b-a01f-482d-a859-f574c38eb2a6" positionType="Float" x="376" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&& !$F{hanko9Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko9Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="4ec6e5b3-4811-4ded-8e0f-ea3dbcd5a52c" positionType="Float" x="376" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1&& !$F{hanko9Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="9e4f59b5-3464-4639-844f-3fc7a4e0bee2" positionType="Float" x="423" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko10Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko10Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="33886da1-71ce-4cf4-b331-b3c9ea4125e9" positionType="Float" x="423" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko10Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="998d7c96-e7e1-45cd-b31c-e3147ba3132a" positionType="Float" x="470" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko11Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko11Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="08a6aa27-c0c6-4682-b988-f8ebab898adf" positionType="Float" x="470" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko11Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="0ae299ec-532c-4785-8693-9024a13bf9f5" positionType="Float" x="517" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko12Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko12Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="ec12afb5-91dc-4206-9285-46aa51889cad" positionType="Float" x="517" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko12Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="f135ef59-564d-4a59-b210-8d3a33bfaacc" positionType="Float" x="564" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko13Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko13Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="3965893f-9aa8-4255-a511-34a3875c8a01" positionType="Float" x="564" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko13Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="be1211ca-b21f-4726-8c74-b65205bdc2b4" positionType="Float" x="611" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko14Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko14Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="4cc5e612-98ac-49af-8d7b-ea578a105f57" positionType="Float" x="611" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko14Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="8ff5570f-6b00-42dc-9840-737bc11309dc" positionType="Float" x="658" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko15Knj}.isEmpty() && $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko15Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="382f5dba-7423-4b52-bb2d-97b941178254" positionType="Float" x="658" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{hyoujiKbn} == 1 && !$F{hanko15Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
		</element>
		<element kind="textField" uuid="63e3bfed-0315-44b7-830e-1c66187fc95d" x="588" y="-20" width="150" height="12" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[$F{bunsyoKanriNo}]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="7187b7d0-279c-4f90-b315-05ce91d13da1" x="0" y="17" width="400" height="14" fontName="IPAexGothic" fontSize="14.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$F{title}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="staticText" uuid="ea8713a5-4146-46e6-a296-fb066cac3248" x="220" y="39" width="60" height="12" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<text><![CDATA[ケース番号]]></text>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="frame" uuid="2a7d483b-a212-41f8-9a6a-e59799f9ce05" x="280" y="39" width="10" height="12">
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="textField" uuid="0343b38e-1ca2-4559-a542-d070217633e1" x="290" y="39" width="80" height="12" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<expression><![CDATA[$F{caseNo}]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="frame" uuid="adc249e8-5662-4919-a985-04ff95a3f4ef" x="380" y="39" width="170" height="11">
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<element kind="staticText" uuid="a2ec4fe5-4ace-4196-945f-d9fd31bcd15c" x="0" y="0" width="170" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
				<text><![CDATA[(初回作成：                　　　      )]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="textField" uuid="41647170-e880-47ab-895d-f67d3fc5f34b" x="59" y="0" width="22" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right">
				<expression><![CDATA[$F{shokaiYmdGG}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
			</element>
			<element kind="textField" uuid="c49e87c5-5d0b-40ed-b4e3-6a639ccb644b" x="81" y="0" width="14" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Bottom">
				<expression><![CDATA[$F{shokaiYmdYY}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="staticText" uuid="442e5569-776a-4757-8e98-8277d9f5c174" x="95" y="0" width="10" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
				<text><![CDATA[年]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="textField" uuid="22af0d85-877a-41e3-b52a-6b1cbb5a4cb8" x="105" y="0" width="14" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Bottom">
				<expression><![CDATA[$F{shokaiYmdMM}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="staticText" uuid="b902bebe-a06c-499f-973e-ef2a4df8ffdc" x="119" y="0" width="10" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
				<text><![CDATA[月]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="textField" uuid="78523b14-2ad9-4c9f-be83-12fc83ed4753" x="129" y="0" width="14" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Bottom">
				<expression><![CDATA[$F{shokaiYmdDD}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="staticText" uuid="f0191712-316b-42da-9a0b-73691ab04dae" x="143" y="0" width="8" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
				<text><![CDATA[日]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
		</element>
		<element kind="frame" uuid="50975f9f-bca7-4984-a9f2-6364aab10a3d" x="560" y="39" width="170" height="11">
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<element kind="staticText" uuid="e9cac4bf-9c31-44a2-bcbc-23e7af4fc4d8" x="0" y="0" width="170" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
				<text><![CDATA[作成(変更)                　　　　　 ]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="textField" uuid="f0f727d0-7160-4143-a298-f9aa9e91db7c" x="60" y="0" width="22" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right">
				<expression><![CDATA[$F{createYmdGG}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="textField" uuid="6146cc27-b210-430f-a2f7-0974e8be458b" x="82" y="0" width="14" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Bottom">
				<expression><![CDATA[$F{createYmdYY}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="staticText" uuid="c3feb9bc-7b76-4b04-847a-8292622b593d" x="96" y="0" width="10" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
				<text><![CDATA[年]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="textField" uuid="1640dada-3e28-4641-9b77-507da3a1e1d8" x="106" y="0" width="14" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Bottom">
				<expression><![CDATA[$F{createYmdMM}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="staticText" uuid="27f0a233-d3ce-40d5-80de-c2880feef260" x="120" y="0" width="10" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
				<text><![CDATA[月]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="textField" uuid="344fc233-253c-439c-9446-7f397b1915e1" x="130" y="0" width="14" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Bottom">
				<expression><![CDATA[$F{createYmdDD}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="staticText" uuid="d773c37e-83a5-4e14-8716-cb559e14a3d2" x="144" y="0" width="8" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
				<text><![CDATA[日]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="80" splitType="Stretch">
			<element kind="frame" uuid="edba511d-2091-4cf1-b96d-187a6ba39c08" positionType="Float" stretchType="ContainerBottom" mode="Transparent" x="0" y="65" width="741" height="15" printWhenDetailOverflows="true">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<element kind="frame" uuid="bc53e4e6-0b06-4f7d-84c0-380adb28583e" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="0" y="0" width="122" height="15" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="856b0e20-9843-4d20-95d1-11d8551a8cb2" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="122" y="0" width="90" height="15" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="fc0adb07-f3da-4ecb-989e-a0108d125fbc" key="" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="212" y="0" width="123" height="15" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="fa9b6ce4-b1e5-46f4-a9f9-9fbe726f1421" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="335" y="0" width="45" height="15" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="dc2b0e64-5505-4e95-abd6-c0fd10dc329c" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="380" y="0" width="90" height="15" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="f2612104-dab1-4348-b672-a0ee28034983" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="470" y="0" width="45" height="15" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="839f0c24-f81d-4d37-9751-a37bf07c4e3f" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="515" y="0" width="90" height="15" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="fa8c6c2f-c88a-4821-b73b-1e5afa90c65c" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="605" y="0" width="45" height="15" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="149fe239-83ac-40c7-8c69-1cb09653c33b" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="650" y="0" width="90" height="15" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
			</element>
			<element kind="component" uuid="ed613802-a6ca-4b09-aafd-94ae4cb30f00" positionType="Float" x="0" y="5" width="741" height="75">
				<component kind="table">
					<datasetRun uuid="97f025b4-dab0-463e-a38c-6a871c2803d0" subDataset="Dataset1">
						<dataSourceExpression><![CDATA[$F{moniList}]]></dataSourceExpression>
					</datasetRun>
					<detail splitType="Immediate"/>
					<column kind="group" uuid="2ee0e7e9-9b87-4f98-880f-b99d72d46863" width="335">
						<columnHeader height="15" rowSpan="1">
							<element kind="staticText" uuid="7d862a3c-196c-404c-a957-d6a6b68b71a2" x="0" y="0" width="334" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[モニタリング]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</columnHeader>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [3]"/>
						<column kind="single" uuid="63aba547-239a-4eae-b189-74d991e9f223" width="122">
							<columnHeader height="45" rowSpan="2">
								<element kind="staticText" uuid="a34cf62e-f66a-4621-ae1a-ff83fbf3cff4" x="0" y="0" width="122" height="45" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[目標]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<detailCell height="15">
								<element kind="textField" uuid="f04bca3d-63db-40c6-b6bd-7d4f6f8c5124" x="0" y="0" width="122" height="15" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<expression><![CDATA[$F{mokuhyoKnj}]]></expression>
									<property name="net.sf.jasperreports.style.box.leftPadding" value="2"/>
									<property name="net.sf.jasperreports.style.box.topPadding" value="2"/>
								</element>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						</column>
						<column kind="single" uuid="a3682bfb-23d8-4cf9-962d-20e4c7ac6d98" width="90">
							<columnHeader height="45" rowSpan="2">
								<element kind="staticText" uuid="343fc398-d157-47dc-82ce-f9d99fc07cb2" x="0" y="0" width="90" height="45" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[対象期間]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<detailCell height="15">
								<element kind="textField" uuid="cf0f5b23-bac0-4543-acdc-57cb78ecc807" x="0" y="0" width="90" height="15" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<expression><![CDATA[$F{kikanKnj}]]></expression>
									<property name="net.sf.jasperreports.style.box.leftPadding" value="2"/>
									<property name="net.sf.jasperreports.style.box.topPadding" value="2"/>
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
						</column>
						<column kind="single" uuid="be3afbae-526a-42e0-908d-180e4b62a145" width="123">
							<columnHeader height="45" rowSpan="2">
								<element kind="staticText" uuid="*************-4588-8477-29764848c1ca" x="0" y="0" width="123" height="45" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[サービスの実施状況]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<detailCell height="15">
								<element kind="textField" uuid="f5d81aa1-6c0a-4f27-8de2-de1d7c845702" x="0" y="0" width="123" height="15" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<expression><![CDATA[$F{jyoukyouKnj}]]></expression>
									<property name="net.sf.jasperreports.style.box.leftPadding" value="2"/>
									<property name="net.sf.jasperreports.style.box.topPadding" value="2"/>
								</element>
								<box>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
						</column>
					</column>
					<column kind="group" uuid="2b6d8906-e546-4fbb-95ac-22fc83bcaa4c" width="405">
						<columnHeader height="15" rowSpan="1">
							<element kind="staticText" uuid="1f6394a9-2e5f-4943-8dfa-76625d7e802b" x="0" y="0" width="405" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[評価]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</columnHeader>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [6]"/>
						<column kind="group" uuid="84b8285e-755c-46b4-bfa0-f5adfb8a16ae" width="135">
							<columnHeader height="15" rowSpan="1">
								<element kind="staticText" uuid="92c6d9c4-b7fe-46f7-b915-94f690a4cb61" x="0" y="0" width="125" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[計画作成担当者]]></text>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [2]"/>
							<column kind="single" uuid="37249aa3-2a8d-4747-aa6d-e2d994340f25" width="45">
								<columnHeader height="30" rowSpan="1">
									<element kind="staticText" uuid="db137dd4-bb78-42fa-8e0f-e36e4dffeb15" x="0" y="0" width="45" height="30" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[目標達
成状況]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
									</element>
									<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</columnHeader>
								<detailCell height="15">
									<element kind="textField" uuid="be0cc3fc-42ab-4699-934c-7d0a3f5984f7" x="0" y="0" width="45" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Center" vTextAlign="Middle">
										<expression><![CDATA[$F{tantoHyouka}]]></expression>
										<property name="net.sf.jasperreports.style.box.topPadding" value="2"/>
									</element>
									<box>
										<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
							</column>
							<column kind="single" uuid="d49509c0-6d74-4167-a700-71b4206389e0" width="90">
								<columnHeader height="30" rowSpan="1">
									<element kind="staticText" uuid="88b95bef-6293-453f-9324-e245dd0a2733" x="0" y="0" width="85" height="30" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[その理由]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
									</element>
									<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</columnHeader>
								<detailCell height="15">
									<element kind="textField" uuid="fdfdf489-a974-408b-8d02-e654b4476f7e" x="0" y="0" width="90" height="15" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{tantoHyoukaKnj}]]></expression>
										<property name="net.sf.jasperreports.style.box.leftPadding" value="2"/>
										<property name="net.sf.jasperreports.style.box.topPadding" value="2"/>
									</element>
									<box>
										<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column5"/>
							</column>
						</column>
						<column kind="group" uuid="e77a2750-3393-4da1-b45b-977099fd92ef" width="135">
							<columnHeader height="15" rowSpan="1">
								<element kind="staticText" uuid="7edf626b-80d2-434a-996e-af2399b46ef7" x="0" y="0" width="135" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[利用者]]></text>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [2]"/>
							<column kind="single" uuid="e42dfd2c-c011-44c7-ad35-99965b1cab85" width="45">
								<columnHeader height="30" rowSpan="1">
									<element kind="staticText" uuid="4d0ef6a3-c261-4371-a44c-135a2fdc179a" x="0" y="0" width="45" height="30" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[目標達
成状況]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
									</element>
									<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</columnHeader>
								<detailCell height="15">
									<element kind="textField" uuid="1d9dd097-3ee4-40f9-bb66-f0431873285d" x="0" y="0" width="45" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Center" vTextAlign="Middle">
										<expression><![CDATA[$F{userHyouka}]]></expression>
										<property name="net.sf.jasperreports.style.box.topPadding" value="2"/>
									</element>
									<box>
										<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column6"/>
							</column>
							<column kind="single" uuid="9485434c-f4d3-4f82-92f8-1bc0c3b64402" width="90">
								<columnHeader height="30" rowSpan="1">
									<element kind="staticText" uuid="9b5966e7-10ce-4007-b8e6-14cc1a23f909" x="0" y="0" width="90" height="30" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[その理由]]></text>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
									</element>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</columnHeader>
								<detailCell height="15">
									<element kind="textField" uuid="a0130181-78a5-4e7a-92ac-90857adf5e13" x="0" y="0" width="90" height="15" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{userHyoukaKnj}]]></expression>
										<property name="net.sf.jasperreports.style.box.leftPadding" value="2"/>
										<property name="net.sf.jasperreports.style.box.topPadding" value="2"/>
									</element>
									<box>
										<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column7"/>
							</column>
						</column>
						<column kind="single" uuid="df2d3a88-d95d-418b-aa1b-c11664baedde" width="45">
							<columnHeader height="45" rowSpan="2">
								<element kind="staticText" uuid="b6f6edf9-6e00-42f3-b4d6-402f86268c82" x="0" y="0" width="45" height="45" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[今後の
対応]]></text>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<detailCell height="15">
								<element kind="textField" uuid="29cf9d24-f06c-4885-8db9-ae4af885abd3" x="0" y="0" width="45" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Center" vTextAlign="Middle">
									<expression><![CDATA[$F{taiou}]]></expression>
									<property name="net.sf.jasperreports.style.box.topPadding" value="2"/>
								</element>
								<box>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column8"/>
						</column>
						<column kind="single" uuid="fc0e1f69-6c06-4233-8c68-7eab445aac77" width="90">
							<columnHeader height="45" rowSpan="2">
								<element kind="staticText" uuid="d55a5ccd-fdc3-4f6e-8f59-9a9a25e79d49" x="0" y="0" width="90" height="45" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[その理由]]></text>
									<box>
										<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<detailCell height="15">
								<element kind="textField" uuid="f54b758a-cd46-476f-8a6d-0aea2ab41b8f" x="0" y="0" width="90" height="15" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
									<expression><![CDATA[$F{taiouKnj}]]></expression>
									<property name="net.sf.jasperreports.style.box.leftPadding" value="2"/>
									<property name="net.sf.jasperreports.style.box.topPadding" value="2"/>
								</element>
								<box>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column9"/>
						</column>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="40">
			<element kind="component" uuid="565884a2-1e7b-4e20-8b8d-614fdaa9fa5c" positionType="Float" x="0" y="0" width="742" height="17">
				<component kind="table">
					<datasetRun uuid="555e6eaa-882e-4a33-98ad-d4e5770f636c" subDataset="Dataset2">
						<dataSourceExpression><![CDATA[$F{needsRetryList}]]></dataSourceExpression>
					</datasetRun>
					<column kind="single" uuid="f5a9bb0b-a630-464c-b21c-bad14003d69c" width="155">
						<detailCell height="17">
							<element kind="textField" uuid="dd496087-6168-4441-9f6e-dc0a1386bbac" x="0" y="0" width="150" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<expression><![CDATA[$F{levelName}]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
					</column>
					<column kind="single" uuid="46b3b675-a214-4dc3-8747-8ee9d0759d33" width="95">
						<detailCell height="17">
							<element kind="frame" uuid="a5487380-1a4e-4942-a122-b1258b76201a" x="0" y="0" width="60" height="15">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<element kind="staticText" uuid="085e9b44-0646-4080-abd7-24b7ce0d546c" x="19" y="0" width="12" height="14" fontName="IPAexGothic" fontSize="14.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[・]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="rectangle" uuid="59e5835e-37c8-40e1-abf7-c159385f36be" x="0" y="2" width="18" height="12" radius="5">
									<printWhenExpression><![CDATA[$F{umuFlg}=="0"]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<pen lineColor="#0A0AFF"/>
								</element>
								<element kind="rectangle" uuid="c4fa2256-59b3-4000-bfaa-83b5c0650dbd" x="31" y="2" width="18" height="12" radius="5">
									<printWhenExpression><![CDATA[$F{umuFlg}=="1"]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<pen lineColor="#0000FF"/>
								</element>
								<element kind="staticText" uuid="857dc755-ea55-4877-bb78-b5ca2aee719e" x="3" y="0" width="11" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[無]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<element kind="staticText" uuid="094de8db-4681-47e4-9668-bccf971799df" x="33" y="0" width="11" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[有]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
					</column>
					<column kind="single" uuid="14ef443a-d8ef-4025-8053-9d4c25aa4118" width="490">
						<detailCell height="17">
							<element kind="textField" uuid="d293c169-460b-4b94-80b9-621fddbc2589" x="0" y="0" width="490" height="17" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" vTextAlign="Middle">
								<expression><![CDATA[$F{henkouRetryKnj}]]></expression>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
							<box>
								<topPen lineWidth="1.0"/>
								<bottomPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
			</element>
			<element kind="frame" uuid="816f060d-daa2-49cd-9f83-cb70851b9499" positionType="Float" x="20" y="20" width="709" height="20" removeLineWhenBlank="true">
				<printWhenExpression><![CDATA[$F{shoninFlg}=="1"]]></printWhenExpression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<element kind="subreport" uuid="4baf7703-e782-47c9-8472-953a11df1189" key="com_report" positionType="Float" x="0" y="0" width="698" height="20">
					<dataSourceExpression><![CDATA[$F{subReportDataDs}]]></dataSourceExpression>
					<expression><![CDATA[$F{subReportPath}]]></expression>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="20">
		<element kind="textField" uuid="df14d021-bbc5-4254-aecb-b68e36c285bb" x="378" y="0" width="17" height="17" fontName="IPAexGothic" fontSize="8.0" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[ $V{PAGE_NUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="07df373b-df44-465d-935e-8cdf01cf13f1" x="395" y="0" width="8" height="17" fontName="IPAexGothic" fontSize="9.0" evaluationTime="Report" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[" / "]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="e68f4819-6e2d-4016-bad2-f5f64c45bfd7" x="404" y="0" width="17" height="17" fontName="IPAexGothic" fontSize="8.0" evaluationTime="Report" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[ $V{PAGE_NUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
