<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="U06028_CareAssessment34_H30" language="java" pageWidth="595" pageHeight="842" columnWidth="529" leftMargin="43" rightMargin="23" topMargin="7" bottomMargin="7" uuid="ea9d29d4-d6b1-4b89-a0d4-28a831e5b0d8" floatColumnFooter="true">
	<field name="prDate" class="java.lang.Integer">
		<description><![CDATA[prDate]]></description>
	</field>
	<field name="name1Knj" class="java.lang.String">
		<description><![CDATA[name1Knj]]></description>
	</field>
	<field name="name2Knj" class="java.lang.String">
		<description><![CDATA[name2Knj]]></description>
	</field>
	<field name="bango31" class="java.lang.Integer">
		<description><![CDATA[bango31]]></description>
	</field>
	<field name="bango32" class="java.lang.Integer">
		<description><![CDATA[bango32]]></description>
	</field>
	<field name="bango33" class="java.lang.Integer">
		<description><![CDATA[bango33]]></description>
	</field>
	<field name="bango34" class="java.lang.Integer">
		<description><![CDATA[bango34]]></description>
	</field>
	<field name="bango35" class="java.lang.Integer">
		<description><![CDATA[bango35]]></description>
	</field>
	<field name="bango36" class="java.lang.Integer">
		<description><![CDATA[bango36]]></description>
	</field>
	<field name="bango37" class="java.lang.Integer">
		<description><![CDATA[bango37]]></description>
	</field>
	<field name="bango38" class="java.lang.Integer">
		<description><![CDATA[bango38]]></description>
	</field>
	<field name="bango39" class="java.lang.Integer">
		<description><![CDATA[bango39]]></description>
	</field>
	<field name="bango310" class="java.lang.Integer">
		<description><![CDATA[bango310]]></description>
	</field>
	<field name="bango41" class="java.lang.Integer">
		<description><![CDATA[bango41]]></description>
	</field>
	<field name="bango42" class="java.lang.Integer">
		<description><![CDATA[bango42]]></description>
	</field>
	<field name="bango43" class="java.lang.Integer">
		<description><![CDATA[bango43]]></description>
	</field>
	<field name="bango44" class="java.lang.Integer">
		<description><![CDATA[bango44]]></description>
	</field>
	<field name="bango45" class="java.lang.Integer">
		<description><![CDATA[bango45]]></description>
	</field>
	<field name="bango46" class="java.lang.Integer">
		<description><![CDATA[bango46]]></description>
	</field>
	<field name="bango47" class="java.lang.Integer">
		<description><![CDATA[bango47]]></description>
	</field>
	<field name="bango48" class="java.lang.Integer">
		<description><![CDATA[bango48]]></description>
	</field>
	<field name="bango49" class="java.lang.Integer">
		<description><![CDATA[bango49]]></description>
	</field>
	<field name="bango410" class="java.lang.Integer">
		<description><![CDATA[bango410]]></description>
	</field>
	<field name="bango411" class="java.lang.Integer">
		<description><![CDATA[bango411]]></description>
	</field>
	<field name="bango412" class="java.lang.Integer">
		<description><![CDATA[bango412]]></description>
	</field>
	<field name="bango413" class="java.lang.Integer">
		<description><![CDATA[bango413]]></description>
	</field>
	<field name="bango414" class="java.lang.Integer">
		<description><![CDATA[bango414]]></description>
	</field>
	<field name="bango415" class="java.lang.Integer">
		<description><![CDATA[bango415]]></description>
	</field>
	<field name="bango416" class="java.lang.Integer">
		<description><![CDATA[bango416]]></description>
	</field>
	<field name="bango417" class="java.lang.Integer">
		<description><![CDATA[bango417]]></description>
	</field>
	<field name="bango418" class="java.lang.Integer">
		<description><![CDATA[bango418]]></description>
	</field>
	<field name="bango419" class="java.lang.Integer">
		<description><![CDATA[bango419]]></description>
	</field>
	<field name="bango420" class="java.lang.Integer">
		<description><![CDATA[bango420]]></description>
	</field>
	<field name="bango421" class="java.lang.Integer">
		<description><![CDATA[bango421]]></description>
	</field>
	<field name="kazokuJouhouKnj" class="java.lang.String">
		<description><![CDATA[kazokuJouhouKnj]]></description>
	</field>
	<field name="famMemoKnj" class="java.lang.String">
		<description><![CDATA[famMemoKnj]]></description>
	</field>
	<field name="serMemoKnj" class="java.lang.String">
		<description><![CDATA[serMemoKnj]]></description>
	</field>
	<field name="kiboMemoKnj" class="java.lang.String">
		<description><![CDATA[kiboMemoKnj]]></description>
	</field>
	<field name="kiboFamMemoKnj" class="java.lang.String">
		<description><![CDATA[kiboFamMemoKnj]]></description>
	</field>
	<field name="keikakuMemoKnj" class="java.lang.String">
		<description><![CDATA[keikakuMemoKnj]]></description>
	</field>
	<field name="memo1Knj" class="java.lang.String">
		<description><![CDATA[memo1Knj]]></description>
	</field>
	<field name="tokkiH21Hdrname" class="java.lang.String">
		<description><![CDATA[tokkiH21Hdrname]]></description>
	</field>
	<field name="tokkiH21Memo" class="java.lang.String">
		<description><![CDATA[tokkiH21Memo]]></description>
	</field>
	<field name="tokkiFlg" class="java.lang.Integer">
		<description><![CDATA[tokkiFlg]]></description>
	</field>
	<field name="bunsyoKanriNo" class="java.lang.String">
		<description><![CDATA[bunsyoKanriNo]]></description>
	</field>
	<field name="shiTeiDateGG" class="java.lang.String"/>
	<field name="shiTeiDateYY" class="java.lang.String"/>
	<field name="shiTeiDateMM" class="java.lang.String"/>
	<field name="shiTeiDateDD" class="java.lang.String"/>
	<field name="emptyFlg" class="java.lang.Boolean"/>
	<pageHeader height="15" splitType="Stretch">
		<element kind="textField" uuid="42efd3ca-aa10-4df5-afc5-86481d08b0a1" x="403" y="0" width="100" height="10" markup="html" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
			<printWhenExpression><![CDATA[($F{prDate} != 1) && (!$F{emptyFlg})]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDateGG}+
"<font style='font-size:9pt'>"+$F{shiTeiDateYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{shiTeiDateMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{shiTeiDateDD}+"</font>"+
"日"]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<element kind="textField" uuid="136f9630-aef9-41ad-a9db-cff0bfa75da3" x="67" y="0" width="336" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$F{name1Knj}+" "+$F{name2Knj}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<element kind="staticText" uuid="3394530d-b54e-4145-9154-c76bfb5b43b0" x="0" y="0" width="67" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<text><![CDATA[利用者氏名：]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="773" splitType="Stretch">
			<element kind="frame" uuid="585549c2-8997-4269-8b1b-6a415c6f324d" x="1" y="0" width="139" height="15">
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="staticText" uuid="96c4d493-c5dd-4d35-b42d-3e62b9e6e1db" x="0" y="0" width="15" height="15" fontName="IPAexGothic" fontSize="14.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[６]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="staticText" uuid="58ad6acc-dee9-4f64-8ba8-da1730a66841" x="15" y="0" width="18" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" vTextAlign="Middle">
					<text><![CDATA[-③]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<element kind="staticText" uuid="0f550990-9f43-41f7-bef0-b8d902fc38dc" x="34" y="0" width="50" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
					<text><![CDATA[認知機能]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
			</element>
			<element kind="frame" uuid="f1a91966-5a4f-476c-b3a8-71853bcdda53" x="1" y="19" width="216" height="240">
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.layout"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<element kind="staticText" uuid="9e7bef19-86f4-4ee9-af20-0be4513f47df" mode="Opaque" x="0" y="0" width="14" height="216" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Bottom">
					<text><![CDATA[要


介


護


認


定


項


目]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box bottomPadding="2">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="staticText" uuid="bbd4ffd7-35ec-43e0-9a20-32a340179650" x="0" y="216" width="14" height="24" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
					<text><![CDATA[]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="e4c532c5-3896-43b0-9c3e-272b4b6c3862" x="14" y="0" width="202" height="24">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="staticText" uuid="9fc4ddd2-8cb6-4be8-8313-2d64c0955cbb" x="0" y="0" width="134" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[3-1 意思の伝達]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="239e6131-e47e-4267-890c-e40a21a1510e" x="134" y="0" width="17" height="24" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[1]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="5773ca53-475f-4f95-852d-77d81936ae6c" mode="Opaque" x="151" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[2]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="0b3c3f93-c5b1-473b-b20e-b309df15632a" mode="Opaque" x="168" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[3]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="a844677f-7949-4356-b2bc-92d0abdef8e1" mode="Opaque" x="185" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[4]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="ellipse" uuid="1a1313df-c1b5-46ea-a8b6-8faabd82be02" mode="Transparent" x="135" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango31} == 1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="7e672411-37e1-4295-97f7-b21312b64f2c" mode="Transparent" x="152" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango31} == 2]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="c86d1578-f9bd-4b5b-91b9-bc859f40e018" mode="Transparent" x="169" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango31} == 3]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="5aba46b5-d5ba-4ae9-9866-0dfc33787611" mode="Transparent" x="186" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango31} == 4]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<box>
						<topPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="f5e5f1fb-4a17-4209-b5f4-549de876004a" x="14" y="24" width="202" height="24">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="staticText" uuid="b206e31b-79d5-4c3c-9a75-9034a0fa9d46" x="0" y="0" width="134" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[3-2 毎日の日課を理解する]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="4a1718be-ad1a-4e34-a105-f1d6bab3b34f" x="134" y="0" width="17" height="24" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[1]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="81af919a-de48-4ff2-b2c2-d75191ebe4b0" mode="Opaque" x="151" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[2]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="6f3006cd-70a8-4836-adec-137fea662213" mode="Opaque" x="168" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="ebb8ed60-ce44-4ac6-8d4d-108de93daedd" mode="Opaque" x="185" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="ellipse" uuid="274ea5f9-bf59-42f2-86fc-dd1961cca674" mode="Transparent" x="135" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango32} == 1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="24d853ed-4bfb-46e9-afb1-8473909ab090" mode="Transparent" x="152" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango32} == 2]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<box>
						<topPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="6e8fa942-b2ed-4231-b8e0-d65bc59db606" x="14" y="48" width="202" height="24">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="staticText" uuid="be7c2a1e-f54b-4fb6-866e-84d129dc32d9" x="0" y="0" width="134" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[3-3 生年月日や年齢を答える]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="a0903252-1c06-4b6f-b2a3-d291780347f2" x="134" y="0" width="17" height="24" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[1]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="1d4e9443-2ba7-4401-b16e-92d67f35ec74" mode="Opaque" x="151" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[2]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="861e4b0f-325c-49f2-b97f-68191d9cea65" mode="Opaque" x="168" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="854de323-d631-40a3-af2d-29ae9f9b75d3" mode="Opaque" x="185" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="ellipse" uuid="e8ba957f-b954-47a7-a9d0-9ecd1434d9bc" mode="Transparent" x="135" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango33} == 1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="c0b3ee13-6f5c-4b68-8ec8-321176a2c1af" mode="Transparent" x="152" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango33} == 2]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<box>
						<topPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="7f371e44-1fba-4661-ad24-75cd27bd1f3c" x="14" y="72" width="202" height="24">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="staticText" uuid="567bed07-8681-49d6-b045-ce471f5bae6c" x="0" y="0" width="134" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[3-4 面接調査の直前記憶]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="067a69dc-eadc-4c58-81e0-64123558b30f" x="134" y="0" width="17" height="24" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[1]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="0db233b0-72c0-4716-9b2e-9ae6abffc989" mode="Opaque" x="151" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[2]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="ef1490af-1856-47b0-af72-74289ee3cf64" mode="Opaque" x="168" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="85c2ed9b-b76f-4656-81ac-10069ddc3916" mode="Opaque" x="185" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="ellipse" uuid="56c67529-446d-4114-ab66-356a6d297a4e" mode="Transparent" x="135" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango34} == 1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="69724c45-567e-4ae2-a7b4-73b57903ddbf" mode="Transparent" x="152" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango34} == 2]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<box>
						<topPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="ad1ef035-3efc-4e8f-bf2f-49d422aee8d8" x="14" y="96" width="202" height="24">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="staticText" uuid="73018993-c86f-4251-9aa6-ae07a7ce45c7" x="0" y="0" width="134" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[3-5 自分の名前を答える]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="52ed8b30-352f-4131-9fd2-4c3f8085d151" x="134" y="0" width="17" height="24" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[1]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="b47debaf-a67a-4c7c-aac2-242882fc0f5d" mode="Opaque" x="151" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[2]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="ce9b44c7-c0c6-42bd-866e-c0e29958ffa1" mode="Opaque" x="168" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="c9346fc2-9d7a-401c-959c-eaf2d6a69adc" mode="Opaque" x="185" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="ellipse" uuid="92999171-ff0d-4623-8a49-b73d78f9958a" mode="Transparent" x="135" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango35} == 1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="62de2156-5c7a-46b8-8ae5-f32489786647" mode="Transparent" x="152" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango35} == 2]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<box>
						<topPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="7fff8d05-e5db-461b-b7be-d2e1b0fe199f" x="14" y="120" width="202" height="24">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="staticText" uuid="eb44c332-b7e8-4e1b-a217-0d6f81a31ead" x="0" y="0" width="134" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[3-6 今の季節を理解する]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="3ad79de6-3b26-4ef0-a1b6-910f1f4e9718" x="134" y="0" width="17" height="24" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[1]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="4e83f462-5f4e-48b0-b019-a3f0b542f900" mode="Opaque" x="151" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[2]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="1d6c1ec5-37c9-4835-ac3d-52f7652a53a8" mode="Opaque" x="168" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="2f39a4d3-4b73-4330-841c-1b9527521ff6" mode="Opaque" x="185" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="ellipse" uuid="6842c560-b3ed-4070-a296-bd8eddee3e7c" mode="Transparent" x="135" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango36} == 1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="84938380-eeaf-46e7-986d-f26033a41ccd" mode="Transparent" x="152" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango36} == 2]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<box>
						<topPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="303b770c-7f6a-4c3e-9625-ffd25fc0abd3" x="14" y="144" width="202" height="24">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="staticText" uuid="644266ba-fd2b-4c99-94f6-85c4b8f349f5" x="0" y="0" width="134" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[3-7 自分のいる場所を答える]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="564e7a9e-a61a-4ee9-935c-f015d578d8c8" x="134" y="0" width="17" height="24" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[1]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="cedbf354-3b96-48d6-81d9-e9cde389438c" mode="Opaque" x="151" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[2]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="c1ba059c-353f-4344-b74d-d671f7a9470d" mode="Opaque" x="168" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="e4fb9977-fa74-439f-83c1-08d21d7f42e1" mode="Opaque" x="185" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="ellipse" uuid="2bccd231-f930-4588-86b2-2913e226bbbd" mode="Transparent" x="135" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango37} == 1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="a85d1156-12b9-46c3-80b2-f4a0196ef0da" mode="Transparent" x="152" y="4" width="15" height="15">
						<printWhenExpression><![CDATA[$F{bango37} == 2]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<box>
						<topPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="778c9539-964f-470e-96ba-6051d5178834" x="14" y="168" width="202" height="24">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="staticText" uuid="7ad32638-212d-47f6-9bd4-bc41a5c855ae" x="0" y="0" width="134" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[3-8 徘徊]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="cd0551d8-4a5c-4672-b8c9-1bc2880ebdfc" x="134" y="0" width="17" height="24" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[1]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="251813be-bb43-44eb-af0f-b899e247ab54" mode="Opaque" x="151" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[2]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="60c8788e-4c6e-4364-bb69-d9a2fdb7ce6b" mode="Opaque" x="168" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[3]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="4ab55725-dab8-40cc-8bba-a14479a2691a" mode="Opaque" x="185" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="ellipse" uuid="c7c39dfb-45ed-4edf-b114-dc7158d81139" mode="Transparent" x="135" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango38} == 1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="e7e98379-5b83-478d-81dd-c25a45806929" mode="Transparent" x="152" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango38} == 2]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="da0a7224-809b-431b-93b2-1f3c168910fb" mode="Transparent" x="169" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango38} == 3]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<box>
						<topPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="39738650-4ce9-4821-acb0-df68a75c82a9" x="14" y="192" width="202" height="24">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="staticText" uuid="31475f78-3d74-4fb0-8de7-67ccf93bc5c6" x="0" y="0" width="134" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[3-9 外出すると戻れない（迷子）]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="7201fc0b-a7e4-4888-9216-7c33e9f508f3" x="134" y="0" width="17" height="24" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[1]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="ba86c46b-609a-4a9b-86f1-88aa0554ffa7" mode="Opaque" x="151" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[2]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="81bb1754-8149-4caa-be46-d7354d836d9b" mode="Opaque" x="168" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[3]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="c46dde45-3aa0-4cf7-bc37-6571ae866d3b" mode="Opaque" x="185" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="ellipse" uuid="9c98ca7d-8fa4-4fcd-8232-5eaf9bbd3f19" mode="Transparent" x="135" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango39} == 1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="690c074b-2184-41ef-a83b-f3d1cbd6aef8" mode="Transparent" x="152" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango39} == 2]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="2b5083eb-89a5-4a92-9cea-382e02fecc01" mode="Transparent" x="169" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango39} == 3]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<box>
						<topPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="dd29970a-5dac-44f2-9e4a-01520f2296c0" x="14" y="216" width="202" height="24">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="staticText" uuid="6e12d5b0-fa90-453c-baec-8a6786b47d36" x="0" y="0" width="134" height="24" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
						<text><![CDATA[3-10 介護者の発言への反応]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="c84fcb30-8811-4d6e-8055-7256cb9f0577" x="134" y="0" width="17" height="24" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[1]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="dba82f8a-e6ff-4910-8bb5-169e18f3dd39" mode="Opaque" x="151" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[2]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="39901e56-a527-46d1-aac7-e37de23f89b2" mode="Opaque" x="168" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[3]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="857c2d12-3ce1-4970-8511-a5d8a56dc56c" mode="Opaque" x="185" y="0" width="17" height="24" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="ellipse" uuid="dde96e94-8728-496c-8e62-6bc62643860c" mode="Transparent" x="135" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango310} == 1]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="20d8ec5e-559c-491c-ab38-a05ec1cb7a66" mode="Transparent" x="152" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango310} == 2]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="ellipse" uuid="b11e6496-2622-49cd-a6c7-299bcf027148" mode="Transparent" x="169" y="4" width="15" height="15" removeLineWhenBlank="true">
						<printWhenExpression><![CDATA[$F{bango310} == 3]]></printWhenExpression>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<box>
						<topPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="frame" uuid="aa478eee-ba43-46d7-9c21-69371140f067" x="1" y="271" width="139" height="15">
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="staticText" uuid="076d59ae-4333-4419-9bb2-9a0530397d47" x="0" y="0" width="15" height="15" fontName="IPAexGothic" fontSize="14.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[６]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
				<element kind="staticText" uuid="2cd4a19d-4d7f-4400-af21-bc12bf27c1db" x="15" y="0" width="18" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" vTextAlign="Middle">
					<text><![CDATA[-④]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="staticText" uuid="2977c41a-f791-451a-904d-0e737f85af42" x="34" y="0" width="99" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
					<text><![CDATA[精神・行動障害]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
			</element>
			<element kind="frame" uuid="a5ed1e80-7bfe-4e00-9cd8-b7c0339c02d4" x="1" y="290" width="237" height="483">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<element kind="staticText" uuid="578a82b8-38db-4459-9b8d-6633ecb2429f" mode="Opaque" x="0" y="0" width="14" height="345" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Bottom">
					<text><![CDATA[要


介


護


認


定


項


目]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box bottomPadding="36">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="staticText" uuid="12a0815f-0729-486f-b21d-94a65316fa42" x="0" y="345" width="14" height="138" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
					<text><![CDATA[]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="daeb7d60-5f24-4159-bb68-97afb843cca8" x="14" y="0" width="223" height="483">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
					<element kind="frame" uuid="3f250cf7-4567-4e82-b8f7-d3aed35feda8" x="0" y="0" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="0"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="c332d78e-e5ef-4ccf-b262-643b45640292" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-1 被害妄想（物を盗られたなど）]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="2ad95466-89dc-444c-92e1-5f4901004e28" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="a9165c54-7854-4a6f-b034-3d4cb3547946" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="682ad879-6444-481e-8849-9b8029fb3e04" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="808bf778-d247-458f-8cb3-bd439d8b8f92" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango41} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<pen lineWidth="1.0"/>
						</element>
						<element kind="ellipse" uuid="10279e73-b1e6-4c4a-9c87-8f36b0203946" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango41} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="c9343fd3-7fb6-414a-8cbc-7ccd44197cef" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango41} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="91a382bf-523e-456c-a60d-37244ebb76d2" x="0" y="23" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="c0f90bc3-dd7a-4860-938a-28fcd1727c97" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-2 作話をする]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="0.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="452bb0b9-d748-4c4d-8f75-ee169522ca8f" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="0367a8d1-5e53-442c-9d9b-889f283de8e7" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="5ee5708c-1665-41c1-a86f-1fb91a865010" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="b74d0bf8-9126-4bb5-9721-a365a87d538c" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango42} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="388d7323-8909-4a50-bcaf-3b87e53e5e38" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango42} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="a47d011c-bd5e-4b17-8dce-4feeffd1af23" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango42} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="4a2d8f00-78be-45e4-90a3-c0f9a835f6cf" x="0" y="46" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="2"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="dd472e10-ab61-4ca4-9dbb-591ae403f66f" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-3 感情が不安定になる]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="309b1d3f-43de-4b29-bc38-f70843aea067" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="2e4289d1-ba50-483f-b20d-a71b26941880" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="06a78d8c-935a-4884-b48c-fac1d9416fdf" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="b7a3d319-31b0-4bba-8efd-e3719a137d26" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango43} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="cffe2ab2-ad2b-4099-a10d-5310396ff79c" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango43} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="24ce81b8-6d88-4606-b51a-07d2dd8e2db0" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango43} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="a03172ad-18a0-4fef-bead-68946c4b9763" x="0" y="69" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="3"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="582095d8-423f-4d15-b4bc-c9846ee48c80" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-4 昼夜の逆転]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="8fb57f20-a87a-4382-af90-d6f176a9708c" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="1b239533-fdba-44df-8e9b-65bef2330a6b" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="b5fd6dc6-6edc-4ebf-a792-977316e37fea" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="4e4f9158-1345-4d5a-8ecd-e43ce3c14083" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango44} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="0cef37a9-0050-4dd7-ad5e-1774710e7c94" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango44} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="328bc166-a2f3-4be7-a12c-3e6dc5738597" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango44} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="2f239a6f-bdaf-451a-8b6e-4978981e9cd4" x="0" y="92" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="4"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="ca9001d6-5072-4412-a86f-dd5cd1ee1f87" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-5 しつこく同じ話をする]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="4434acf9-e213-4306-a71f-74734013b4ed" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="b2126d8b-6abc-4b12-bc7d-c702bd2fa8d1" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="1b427ca3-f0c1-47d7-b1b1-5b69d5fc3938" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="3f000389-42fd-41cc-8272-bcc85d924748" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango45} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="56f8f0ef-5c02-428b-853c-609a023ee4ed" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango45} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="16327236-6a0c-45e5-ab2f-27ce82e4ffd3" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango45} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="70895fe5-fa8b-4576-a5fe-422d28b0e90a" x="0" y="115" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="5"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="995a81df-dba4-4d00-94d6-fec87bcb429e" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-6 大声を出す]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="c0535a30-f44f-4924-a9a2-e3213fd59c8d" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="7d5c24a8-c840-44ab-b2bb-d1d164b8d3b3" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="3abffe2d-1e74-44eb-9a7c-1e2ae47a9b9f" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="bb6c3b0c-12ce-4df5-b8f0-899cf83428f2" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango46} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="733ac74a-4a13-4727-b730-07afcc41cff5" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango46} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="8b021a58-74bc-4ab9-a324-329fc14ef5ca" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango46} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="78c62de6-54fa-457b-8005-e2bc76bde9f2" x="0" y="138" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="6"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="1bfb9dcd-ae3c-461c-825c-f439a424e49b" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-7 介護に抵抗する]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="a0609f20-39d5-47ad-af9b-52090c8eb1b9" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="0bbb3838-366c-41ca-bc19-0c1a2aae0e91" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="c7133a35-124c-483e-b3a7-bed0a48dc286" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="1ad48a98-4f52-4e86-9f20-cfa46f29d01a" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango47} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="594a9677-18db-49c6-af9e-6ce104f3a924" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango47} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="85778751-d93f-4e07-b335-19cc75244955" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango47} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="fadabdd0-e54a-418d-8fb9-6a28661f3768" x="0" y="161" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="7"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="427a4668-2f5a-4a35-a611-00c7c6979f3e" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-8 落ち着きがない（「家に帰る」等）]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="a457642b-eb94-463b-9852-d31f2400693f" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="c224a0ac-6b30-419c-afd4-20fd4b285799" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="876f77c2-4035-4ccd-ac04-35ed3acf7a3e" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="8db4ab8f-c09b-4ee5-9f9e-547380e11ae8" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango48} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="938d752d-eee4-4b83-9dd7-2acd80dde37e" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango48} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="0bb85b97-f798-47a0-8a6f-67a929783184" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango48} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="0ad16660-286f-4d5b-aeee-fcd4acf71bff" x="0" y="184" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="8"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="ec5c3fc8-c25b-4e0e-af70-4b0114f0e033" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-9 外に出たがり目が離せない]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="74737c20-caf9-45fd-bb66-d4f537c3084b" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="391be73a-967a-4e58-ae20-6513262051c3" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="5318aa40-79b5-43b6-a797-5faa2f781b7b" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="e233ed58-9309-44ce-8166-90954b610337" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango49} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="2e1cf775-c8e8-4aed-acac-548813d4bf2a" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango49} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="ab9f2a2c-ce12-4faa-b17a-034cf3f0185f" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango49} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="6c70cdc2-**************-3bf41d2d2112" x="0" y="207" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="9"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="ccc46d9d-84af-4df0-b4e3-53b19cfe7e60" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-10 ものを集める、無断でもってくる]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="98049def-4307-4fc1-a4b9-e6e709f90cc9" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="e41b5a88-eb34-4465-a664-f60785abc3ad" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="720234e7-25d6-43c7-ac8d-39c737a39564" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="50620936-1c22-4881-a09e-c7c3b2c5154f" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango410} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="898955c3-d2de-429b-9886-7177445dca51" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango410} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="e5df8a9b-7e8e-486b-86c9-bbab04286e47" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango410} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="7c5ffec8-6ca7-4ed7-9f44-8e4ba3e224ee" x="0" y="230" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="10"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="59baf4d7-9fae-4fe5-b544-bd2c5939574a" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-11 物を壊す、衣類を破く]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="fccce242-9c0c-42c1-ba2d-9550650e1fae" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="a7c1ab41-7b2f-4b36-b6c0-4468f9019f16" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="f1a40f1e-bbed-4e94-a240-b61e99b4fc35" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="91d90e71-f73e-4131-ad1c-5a728d5592c6" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango411} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="43134b40-a77e-4692-b878-d1dfde9554b1" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango411} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="3764b697-3cbc-4f03-ab17-b1249969e843" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango411} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="a09a610f-8656-45fe-bdbd-9f6d350ee5b6" x="0" y="253" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="11"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="16d93e1d-13f5-42f9-90dd-4c3e8201fb45" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-12 ひどい物忘れ]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="e51700db-34d9-4394-9c62-23b395ed92a4" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="ff8f9033-cae0-4877-8ba5-f522eba249aa" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="e5869d04-2e24-42ee-adab-995cb6536c5e" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="79c1676f-c7b9-4f29-b1e1-4a1f785bf1e3" mode="Transparent" x="173" y="4" width="15" height="15" printInFirstWholeBand="true" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango412} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="51c0e146-7db6-485d-8141-ac9aa4dc234a" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango412} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="d39539ca-919a-4af5-b7f4-c267723a07be" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango412} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="e390d3fd-d186-4467-b446-097e6d96c5ab" x="0" y="276" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="12"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="26600495-1c81-41ed-ae40-82770c90bc40" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-13 独り言や独り笑い]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="2f0b64c0-25ab-4241-8351-b541a17a8156" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="cc5586b2-b10a-465f-b26c-aecf4841814c" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="838992f4-e462-4887-bf2b-88798cac861f" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="6bdba716-d372-4399-8500-ab1822f49899" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango413} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="4af009b7-98b0-456d-86ac-6aa23caa14dc" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango413} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="6eff383b-b582-435b-972f-95e2e81cdbb6" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango413} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="4f886c19-5ccc-4e37-b670-4218a6b95fa1" x="0" y="299" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="13"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="2fb1e04c-75d8-4362-b874-aed298d869ce" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-14 自分勝手な行動]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="68b0f8be-56d1-42c2-b75f-ae7761fa3331" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="0e1b97a9-d90a-40b9-bdbf-6d183a0b391d" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="eb646504-7910-4bf9-9b49-36b9e88e4a9b" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="93e0d501-5e72-4cc1-a91d-1ab65f1ea8e8" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango414} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="c0562885-b04a-4f43-acd0-8b21c58ea8c1" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango414} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="35ac1d2b-6f5b-4c1c-a991-fabfb1c866a8" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango414} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="d6ea6d01-7f8f-4dd5-a607-5eead43d6dd4" x="0" y="322" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="14"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="*************-4294-8ce2-7eda08fd9d7d" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-15 話がまとまらない、会話にならない]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="35fa92a3-15dc-46f1-aafa-ce062f8b47b7" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="f1a3a989-de09-4c18-9dbf-85044490d4ee" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="ad1063ec-d2c6-4075-aa50-83ffcb9f3b12" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="0960ce93-35aa-4a37-93a3-8a647c7c6bce" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango415} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="e4a5a10c-50bc-4208-ad5e-0108f5400cf6" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango415} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="a805d577-5f21-4ad9-a5f7-fe47c5f11200" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango415} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="2ff3932f-a89c-4d28-bfa4-1f9671587772" x="0" y="345" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="15"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="4a8ef856-d1ed-41cc-876f-9447f9a650f2" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-16 幻視・幻聴]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="f60e5496-1f53-4189-91bd-a6d598cc6f6b" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="705ce1eb-9ddd-48cc-a819-6b93d9c48d74" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="95cf14da-e5ff-4c38-b75a-0bd4158ab8e9" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="7b9c47ab-4291-4aa8-a88b-7f62b8aa1c20" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango416} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="45a4d9fe-7a79-4b23-ba52-5ba02a363cd0" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango416} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="c938b0ab-d934-4228-9ec1-b83948d8ac2f" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango416} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="b48cb579-ed0c-4a22-b599-68d1fbe7bea3" x="0" y="368" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="16"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="a3873e5a-5acc-4d2c-8b1f-06bb944008ae" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-17 暴言・暴力]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="cf441e0f-0e4e-49dd-8973-10f679730c19" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="93f90f51-03de-46bf-adda-9580839d6bed" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="e117e57e-6551-4b80-847c-74f0b3108fba" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="68a68bcb-4a83-498f-919f-6e136602fab2" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango417} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="e65780b5-53d8-48c9-8fda-38b9bf946843" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango417} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="a89374c5-9bd3-4a7a-9157-9f7861ba0229" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango417} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="0af357ab-3431-4945-b665-71b9a9ffc876" x="0" y="391" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="17"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="e8c9d567-9c78-4986-96f4-57327ce082a8" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-18 目的なく動き回る]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="27e2df88-29d1-4649-8111-90b2beb2aac9" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="e6f81ed9-5895-424e-b1cd-51ef082e0b88" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="f31b5afe-25df-4c52-ab9a-21bf66fbed89" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="0c11f819-4243-4e95-b6a1-0be48ea98c90" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango418} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="58abfcba-7ac1-4366-aee2-75e2f33d3963" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango418} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="f03773ce-5c43-4fa1-835c-4fe32d2a94b6" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango418} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="9392a6a2-59d1-47f5-924e-a135e4ddb0d9" x="0" y="414" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="18"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="ce4adb88-9f69-4536-9031-5c3db6df0e08" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-19 火の始末･管理]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="cf3454f6-de99-4792-a1ec-c1651a872227" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="3ae8bc35-835a-486a-aff3-c32cc7c5c474" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="3b33d0aa-0834-42ab-8556-7296d607a450" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="309381eb-729c-4714-9a55-238c758c4b19" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango419} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="5e183d51-11f5-42cc-be41-d41990b1fce3" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango419} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="5fab299d-e674-4334-95a8-bfb10c2e2da1" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango419} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="316788be-03fe-486e-ad90-7bc440e6bd3b" x="0" y="437" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="19"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="93b16add-fe60-42c3-886f-8d5f0b516f0b" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-20 不潔行為]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="37376d7e-f929-4f00-8194-094c4889f77a" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="36837ff9-0eb9-4ce2-904a-06a6ffadae41" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="c0d8dc25-716e-49ed-a293-ad3a6c975531" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="b1097d6d-7f90-4d2b-ac0e-6f8f59ad3ee5" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango420} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="9ac17da3-f173-477f-b8be-c6b11f2fb06c" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango420} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="e7bf7ddb-3c8f-42df-afba-90fbf13d0edd" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango420} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="5eefda9c-4c45-4778-a26a-f4c34da41965" x="0" y="460" width="223" height="23">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="20"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<element kind="staticText" uuid="d6bf8b12-0b29-4289-a838-ba36df54abea" x="0" y="0" width="172" height="23" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
							<text><![CDATA[4-21 異食行動]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="d8110390-d542-405e-be93-557236ff87cf" x="172" y="0" width="17" height="23" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[1]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="staticText" uuid="97a87326-07f2-4dd9-81cd-16bf2b54e44a" mode="Opaque" x="189" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[2]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="0d64f11a-95ac-4c9d-bdf1-449c42da3211" mode="Opaque" x="206" y="0" width="17" height="23" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[3]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0"/>
								<bottomPen lineWidth="1.0"/>
								<rightPen lineWidth="0.0"/>
							</box>
						</element>
						<element kind="ellipse" uuid="d1990b47-762c-4f42-a4bb-254e71d874b1" mode="Transparent" x="173" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango421} == 1]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="dd5c5706-30f8-4c13-a79b-340f2c155efc" mode="Transparent" x="190" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango421} == 2]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<element kind="ellipse" uuid="21d9cc31-40c4-4c3b-b573-52f6ae4cd6cc" mode="Transparent" x="207" y="4" width="15" height="15" removeLineWhenBlank="true">
							<printWhenExpression><![CDATA[$F{bango421} == 3]]></printWhenExpression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
						</element>
						<box>
							<topPen lineWidth="0.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
				</element>
			</element>
			<element kind="frame" uuid="58c249a6-5b69-4b44-85f6-e836d46bae6c" x="243" y="0" width="285" height="15">
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="staticText" uuid="131830a5-b315-45ed-9af3-dc94bd633acc" x="0" y="0" width="15" height="15" fontName="IPAexGothic" fontSize="14.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[６]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="staticText" uuid="fd87bb9f-9b19-4cb6-a616-056fcedb189c" x="15" y="0" width="18" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" vTextAlign="Middle">
					<text><![CDATA[-③]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<element kind="staticText" uuid="a83c6201-bcaa-48ff-85d7-a9c78302471a" x="33" y="0" width="62" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
					<text><![CDATA[認知機能、]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="staticText" uuid="586b53d0-4c5b-4501-af36-bca7a546cb09" x="95" y="0" width="15" height="15" fontName="IPAexGothic" fontSize="14.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[６]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
				<element kind="staticText" uuid="9d9463dd-2d2a-45f8-b740-59f318970730" x="110" y="0" width="18" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" vTextAlign="Middle">
					<text><![CDATA[-④]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="staticText" uuid="c1d7b50c-20c7-41e3-82a8-f80b17887f95" x="128" y="0" width="147" height="15" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
					<text><![CDATA[精神・行動障害　全般]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
			</element>
			<element kind="frame" uuid="552b5612-1cd6-411e-bb57-61b06fdd6fe7" x="243" y="19" width="287" height="233">
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="staticText" uuid="b075a6ac-4cb9-49e6-b416-c0ac7094219f" x="0" y="0" width="17" height="233" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
					<text><![CDATA[家
族
等
か
ら
の
情
報
と
観
察]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="textField" uuid="419f16a2-6bf1-442e-bb1e-730a003e2209" x="17" y="0" width="270" height="233" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
					<expression><![CDATA[$F{kazokuJouhouKnj}]]></expression>
					<box topPadding="2" leftPadding="2" bottomPadding="11" rightPadding="2"/>
				</element>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="frame" uuid="34bf5ddf-3653-40e9-ba55-b33fc740887d" x="243" y="257" width="287" height="82">
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="staticText" uuid="d346ff63-d81e-416d-99fc-3a68c8a32bb1" x="0" y="0" width="17" height="82" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
					<text><![CDATA[援
助
の
現
状]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="textField" uuid="436c7c1a-224a-4f7f-9f5e-fa238b83c83a" x="152" y="16" width="135" height="66" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self">
					<expression><![CDATA[$F{serMemoKnj}]]></expression>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<box topPadding="2" leftPadding="2" rightPadding="2"/>
				</element>
				<element kind="staticText" uuid="ac925b76-d641-4d0c-b4b6-623aa7967b09" x="152" y="0" width="135" height="16" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
					<text><![CDATA[（サービス）]]></text>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<box>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="dcd8e44b-4119-4fb0-9745-da082905dab4" x="17" y="0" width="135" height="82">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="textField" uuid="0e7dd855-8d7d-450f-b4ca-aa859712ad2c" x="0" y="16" width="135" height="66" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
						<expression><![CDATA[$F{famMemoKnj}]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box topPadding="2" leftPadding="2" rightPadding="2">
							<rightPen lineWidth="0.0" lineStyle="Dashed"/>
						</box>
					</element>
					<element kind="staticText" uuid="d09c6565-c3f0-4aa4-abd7-18464ff2c0e8" x="0" y="0" width="135" height="16" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[（家族）]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<box>
							<rightPen lineWidth="0.0" lineStyle="Dashed"/>
						</box>
					</element>
					<box>
						<rightPen lineWidth="1.0" lineStyle="Solid"/>
					</box>
				</element>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="frame" uuid="85eb1774-c21c-4865-8abd-018c555665af" x="243" y="344" width="287" height="104">
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="staticText" uuid="0845531a-5d90-4765-9f45-2a8e863b7b1a" x="0" y="0" width="17" height="61" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Bottom">
					<text><![CDATA[援
助
の
希
望]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<rightPen lineWidth="0.0"/>
					</box>
				</element>
				<element kind="staticText" uuid="9dc87aaf-88fc-4fbc-9e2f-7a934b6fe35a" x="-5" y="56" width="20" height="15" rotation="Right" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left">
					<text><![CDATA[（]]></text>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<rightPen lineWidth="0.0"/>
					</box>
				</element>
				<element kind="textField" uuid="48f76fed-a59b-4882-8be6-aa4c9485eb4b" x="17" y="0" width="270" height="104" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
					<expression><![CDATA[$F{kiboMemoKnj}]]></expression>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box topPadding="2" leftPadding="2" rightPadding="2">
						<leftPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="staticText" uuid="8ec97cee-58fd-481c-b059-e35ded1aeaef" x="0" y="65" width="17" height="21" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
					<text><![CDATA[本
人]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="staticText" uuid="0a2c2176-46db-46bb-a5be-e836fdb3e643" x="4" y="86" width="11" height="13" rotation="Left" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Top">
					<text><![CDATA[（]]></text>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<rightPen lineWidth="0.0"/>
					</box>
				</element>
				<box>
					<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="frame" uuid="5d2dc9ed-0733-4265-b180-2b0a730e3a1d" x="243" y="453" width="287" height="86">
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="frame" uuid="abecf2f8-e066-4aea-9f67-8962e0455b56" x="0" y="0" width="17" height="86">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="staticText" uuid="10e41ee5-3751-4a22-b9da-0cb674c7aaf3" x="0" y="0" width="17" height="52" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Bottom">
						<text><![CDATA[援
助
の
希
望]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="38885109-cb90-4243-bf85-00e4aac7533f" positionType="Float" x="-6" y="47" width="20" height="15" rotation="Right" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left">
						<text><![CDATA[（]]></text>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="891b26b8-4799-43d1-9683-086804480d06" positionType="Float" x="0" y="55" width="17" height="21" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[家
族]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
					<element kind="staticText" uuid="5700ddd1-cf4f-4227-8ed9-9e632408d1d1" positionType="Float" stretchType="NoStretch" x="4" y="75" width="11" height="11" rotation="Left" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Top">
						<text><![CDATA[（]]></text>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
				</element>
				<element kind="textField" uuid="5f4f806a-7400-41ff-822e-b2f42b435862" x="17" y="0" width="270" height="86" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
					<expression><![CDATA[$F{kiboFamMemoKnj}]]></expression>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box topPadding="2" leftPadding="2" rightPadding="2">
						<leftPen lineWidth="1.0"/>
					</box>
				</element>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="frame" uuid="ec706504-38e2-4709-834a-c3db4ef543e0" x="243" y="544" width="287" height="104">
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="staticText" uuid="75cc54e5-3131-4660-81d3-706c1dbe45b7" x="0" y="0" width="17" height="104" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
					<text><![CDATA[援
助
の
計
画]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<rightPen lineWidth="0.0"/>
					</box>
				</element>
				<element kind="textField" uuid="f1efac9c-5424-48ce-8fd8-0eeba8e6b682" x="17" y="0" width="270" height="104" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
					<expression><![CDATA[$F{keikakuMemoKnj}]]></expression>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box topPadding="2" leftPadding="2" rightPadding="2">
						<leftPen lineWidth="1.0"/>
					</box>
				</element>
				<box>
					<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="frame" uuid="a86fb254-5f22-4456-a34c-5ef592ff79a6" x="243" y="653" width="287" height="120">
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="staticText" uuid="a551d00f-ec52-4c31-abdf-cbf1b7962b8b" x="4" y="0" width="283" height="16" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[【特記、解決すべき課題など】]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<rightPen lineWidth="0.0"/>
					</box>
				</element>
				<element kind="textField" uuid="ecf36048-2fa2-4abd-9835-3e40e6712a49" x="0" y="16" width="287" height="103" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" vTextAlign="Top">
					<printWhenExpression><![CDATA[$F{tokkiFlg}==0]]></printWhenExpression>
					<expression><![CDATA[$F{memo1Knj}]]></expression>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<box topPadding="0" leftPadding="5" rightPadding="5">
						<leftPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="staticText" uuid="d8a7f697-e200-4e68-b298-8d983cfd5919" x="109" y="42" width="69" height="37" fontName="IPAexGothic" fontSize="16.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" hTextAlign="Center" vTextAlign="Middle">
					<printWhenExpression><![CDATA[$F{tokkiFlg}==1]]></printWhenExpression>
					<text><![CDATA[別紙参照]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<box>
						<rightPen lineWidth="0.0"/>
					</box>
				</element>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="773">
			<printWhenExpression><![CDATA[$F{tokkiFlg}==1]]></printWhenExpression>
			<element kind="staticText" uuid="c2a96ade-138c-4ee8-8f2e-e0f725de9ea3" x="0" y="5" width="69" height="16" fontName="IPAexGothic" fontSize="16.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$V{PAGE_NUMBER} > 1]]></printWhenExpression>
				<text><![CDATA[別紙]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="frame" uuid="ce025360-1186-4cf2-83a1-b6957d0115e5" stretchType="ContainerHeight" x="0" y="30" width="483" height="36" removeLineWhenBlank="true">
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="textField" uuid="b84de0e2-3a2b-4f44-b257-815432e09eb7" mode="Opaque" x="0" y="0" width="483" height="16" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
					<expression><![CDATA[$F{tokkiH21Hdrname}]]></expression>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.3"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="textField" uuid="263aad82-c8a0-48a2-bcb4-e7a3815267e5" positionType="Float" stretchType="ContainerHeight" x="0" y="16" width="483" height="20" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" blankWhenNull="false" removeLineWhenBlank="true" printWhenDetailOverflows="true" hTextAlign="Left" vTextAlign="Top">
					<paragraph lineSpacing="Proportional" lineSpacingSize="1.25"/>
					<expression><![CDATA[$F{tokkiH21Memo}]]></expression>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box padding="0" topPadding="5">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<box>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="29" splitType="Stretch">
		<element kind="textField" uuid="d75a1fd3-3858-477e-b658-ec65bb56eeff" key="" x="0" y="0" width="182" height="9" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<printWhenExpression><![CDATA[$F{bunsyoKanriNo}==null?false:true]]></printWhenExpression>
			<expression><![CDATA[$F{bunsyoKanriNo}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="staticText" uuid="28b14da4-1178-44e1-8ee2-acf3a2d54986" x="280" y="6" width="248" height="17" fontName="IPAexGothic" fontSize="8.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
			<text><![CDATA[〔全社協･在宅版ケアプラン作成方法検討委員会作成〕]]></text>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<box>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
