<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="U01020_MonitoringRecordListReport" language="java" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="755" leftMargin="57" rightMargin="30" topMargin="33" bottomMargin="6" uuid="05680462-fe6b-404e-8cef-1149ab862db4">
	<style name="Gothic11Left" hTextAlign="Left" vTextAlign="Middle" rotation="None" fontName="IPAexGothic" fontSize="11.0"/>
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.0" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.0" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Style1" mode="Opaque" backcolor="#EBEBEB">
		<box>
			<bottomPen lineWidth="1.6"/>
		</box>
	</style>
	<style name="Table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<dataset name="Dataset1" uuid="baf694c5-286e-43ee-a7ef-8916731d3838">
		<parameter name="mojiSize" class="java.lang.String"/>
		<field name="kaidaiNo" class="java.lang.String"/>
		<field name="servNo" class="java.lang.String"/>
		<field name="kakuninCd" class="java.lang.String"/>
		<field name="kakuninKnj" class="java.lang.String"/>
		<field name="houhouCd" class="java.lang.String"/>
		<field name="houhouKnj" class="java.lang.String"/>
		<field name="kakuninYmdGG" class="java.lang.String"/>
		<field name="kakuninYmdYY" class="java.lang.String"/>
		<field name="kakuninYmdMM" class="java.lang.String"/>
		<field name="kakuninYmdDD" class="java.lang.String"/>
		<field name="ikenHonCd" class="java.lang.String"/>
		<field name="ikenHonKnj" class="java.lang.String"/>
		<field name="ikenKazCd" class="java.lang.String"/>
		<field name="ikenKazKnj" class="java.lang.String"/>
		<field name="jusokuCd" class="java.lang.String"/>
		<field name="jusokuKnj" class="java.lang.String"/>
		<field name="taiouCd" class="java.lang.String"/>
		<field name="taiouKnj" class="java.lang.String"/>
	</dataset>
	<dataset name="Dataset2" uuid="ef3ca90a-31e7-4daa-8e78-1a71039e442f">
		<parameter name="mojiSize" class="java.lang.String"/>
		<field name="soukatuKnj" class="java.lang.String"/>
		<field name="henkouKnj" class="java.lang.String"/>
		<field name="retryChk" class="java.lang.String"/>
		<field name="yoteiYmdGG" class="java.lang.String"/>
		<field name="yoteiYmdYY" class="java.lang.String"/>
		<field name="yoteiYmdMM" class="java.lang.String"/>
		<field name="yoteiYmdDD" class="java.lang.String"/>
	</dataset>
	<parameter name="subReportPath" class="java.lang.String"/>
	<parameter name="subReportDataDs" class="net.sf.jasperreports.engine.JRDataSource"/>
	<query language="sql"><![CDATA[]]></query>
	<field name="shiTeiKubun" class="java.lang.Integer"/>
	<field name="shiTeiDateGG" class="java.lang.String"/>
	<field name="shiTeiDateYY" class="java.lang.String"/>
	<field name="shiTeiDateMM" class="java.lang.String"/>
	<field name="shiTeiDateDD" class="java.lang.String"/>
	<field name="jigyouName" class="java.lang.String"/>
	<field name="yokaigoDo" class="java.lang.String"/>
	<field name="riyoushaNm" class="java.lang.String"/>
	<field name="keishoName" class="java.lang.String"/>
	<field name="createYmdGG" class="java.lang.String"/>
	<field name="createYmdYY" class="java.lang.String"/>
	<field name="createYmdMM" class="java.lang.String"/>
	<field name="createYmdDD" class="java.lang.String"/>
	<field name="shokuName" class="java.lang.String"/>
	<field name="inkanFlg" class="java.lang.Integer">
		<description><![CDATA[inkanFlg]]></description>
	</field>
	<field name="hanko1Knj" class="java.lang.String">
		<description><![CDATA[hanko1Knj]]></description>
	</field>
	<field name="hanko2Knj" class="java.lang.String">
		<description><![CDATA[hanko2Knj]]></description>
	</field>
	<field name="hanko3Knj" class="java.lang.String">
		<description><![CDATA[hanko3Knj]]></description>
	</field>
	<field name="hanko4Knj" class="java.lang.String">
		<description><![CDATA[hanko4Knj]]></description>
	</field>
	<field name="hanko5Knj" class="java.lang.String">
		<description><![CDATA[hanko5Knj]]></description>
	</field>
	<field name="hanko6Knj" class="java.lang.String">
		<description><![CDATA[hanko6Knj]]></description>
	</field>
	<field name="hanko7Knj" class="java.lang.String">
		<description><![CDATA[hanko7Knj]]></description>
	</field>
	<field name="hanko8Knj" class="java.lang.String">
		<description><![CDATA[hanko8Knj]]></description>
	</field>
	<field name="hanko9Knj" class="java.lang.String">
		<description><![CDATA[hanko9Knj]]></description>
	</field>
	<field name="hanko10Knj" class="java.lang.String">
		<description><![CDATA[hanko10Knj]]></description>
	</field>
	<field name="hanko11Knj" class="java.lang.String">
		<description><![CDATA[hanko11Knj]]></description>
	</field>
	<field name="hanko12Knj" class="java.lang.String">
		<description><![CDATA[hanko12Knj]]></description>
	</field>
	<field name="hanko13Knj" class="java.lang.String">
		<description><![CDATA[hanko13Knj]]></description>
	</field>
	<field name="hanko14Knj" class="java.lang.String">
		<description><![CDATA[hanko14Knj]]></description>
	</field>
	<field name="hanko15Knj" class="java.lang.String">
		<description><![CDATA[hanko15Knj]]></description>
	</field>
	<field name="emptyFlg" class="java.lang.Boolean"/>
	<field name="moniitiranDataList" class="net.sf.jasperreports.engine.JRDataSource"/>
	<field name="syoninPrintFlg" class="java.lang.Integer"/>
	<field name="bunsyoKanriNo" class="java.lang.String"/>
	<field name="mojiSize" class="java.lang.String">
		<description><![CDATA[mojiSize]]></description>
	</field>
	<field name="sokatuHenkoList" class="net.sf.jasperreports.engine.JRDataSource"/>
	<pageHeader height="102" splitType="Stretch">
		<element kind="staticText" uuid="99ecced1-0cb6-4a10-8983-17e4944cee85" x="0" y="12" width="728" height="14" fontName="IPAexGothic" fontSize="14.0" bold="true" hTextAlign="Center" vTextAlign="Middle">
            <text><![CDATA[モニタリング記録表一覧]]></text>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="textField" uuid="1cfa29a7-0f44-4b82-ba14-a326a0413603" x="634" y="0" width="100" height="12" markup="html" fontName="IPAexGothic" fontSize="10.0" hTextAlign="Right" vTextAlign="Middle">
			<printWhenExpression><![CDATA[($F{shiTeiKubun} != 1) && (!$F{emptyFlg})]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDateGG}+
"<font style='font-size:9pt'>"+$F{shiTeiDateYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{shiTeiDateMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{shiTeiDateDD}+"</font>"+
"日"
]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="textField" uuid="cc025d58-ea9c-4d7e-96ec-00ccee2725ee" x="464" y="13" width="270" height="12" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[$F{jigyouName}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="staticText" uuid="a496898d-a649-4810-b49b-dea23bcac977" x="562" y="26" width="60" height="12" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<text><![CDATA[作成年月日]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<element kind="textField" uuid="4c306cd1-8cf4-452d-9a0b-ab7e72db734b" x="622" y="26" width="90" height="12" markup="html" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<printWhenExpression><![CDATA[!$F{createYmdGG}.equals("")]]></printWhenExpression>
			<expression><![CDATA[$F{createYmdGG}+
"<font style='font-size:10pt'>"+$F{createYmdYY}+"</font>"+
"年"+
"<font style='font-size:10pt'>"+$F{createYmdMM}+"</font>"+
"月"+
"<font style='font-size:10pt'>"+$F{createYmdDD}+"</font>"+
"日"
]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="staticText" uuid="42134f4a-bc82-4831-ba46-242bd38bc82c" x="583" y="39" width="40" height="12" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<text><![CDATA[作成者]]></text>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="bb28f743-f91b-4353-ac29-3d0c1ad39e08" x="623" y="39" width="100" height="12" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<expression><![CDATA[$F{shokuName}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="staticText" uuid="cd061cd1-29ff-4c20-9af8-2871c79e2cb8" x="0" y="26" width="46" height="12" fontName="IPAexGothic" fontSize="11.0">
			<text><![CDATA[要介護度]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="0e80ed44-d37b-407c-beab-0fa5795bdf3a" x="89" y="26" width="100" height="12" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<expression><![CDATA[$F{yokaigoDo}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="staticText" uuid="08263cf6-d89a-4e2e-bc36-fb9401531236" x="0" y="39" width="46" height="12" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<text><![CDATA[利用者名]]></text>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="frame" uuid="0d08f649-ca42-45ff-968d-d1419e6b3189" x="46" y="39" width="31" height="12">
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="textField" uuid="a324a340-ae5d-4158-b04a-0b6021c71756" x="77" y="39" width="114" height="12" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<expression><![CDATA[$F{riyoushaNm}]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="textField" uuid="33d770ee-e7f9-4620-a16d-2eb5c3bb6645" x="191" y="39" width="12" height="12" fontName="IPAexGothic" fontSize="11.0" vTextAlign="Middle">
			<expression><![CDATA[$F{keishoName}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box>
				<bottomPen lineWidth="0.75"/>
			</box>
		</element>
		<element kind="frame" uuid="0d45508a-2566-4a12-9afc-e63732885e2d" positionType="Float" x="4" y="55" width="707" height="47" removeLineWhenBlank="true">
			<printWhenExpression><![CDATA[$F{inkanFlg} == 1]]></printWhenExpression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<element kind="textField" uuid="2df386e1-99ac-42ad-8676-a2a79eca6007" positionType="Float" x="0" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko1Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko1Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="10389e82-0990-4646-8ca2-5b4e8d9de48a" positionType="Float" x="0" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko1Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="ebfebe5a-090f-4bac-8115-1bdfd15589ec" positionType="Float" x="48" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1&& !$F{hanko2Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko2Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="8496687e-09ae-4938-ab2e-3a9a187f27ee" positionType="Float" x="48" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1&& !$F{hanko2Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="98cc5fdb-c813-4d0a-b219-787e806e4838" positionType="Float" x="95" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko3Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko3Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="bb062468-f399-42b9-8833-39201cb714e2" positionType="Float" x="95" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko3Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="6f08db02-ba9d-4fba-8009-49da56c0b6dc" positionType="Float" x="142" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko4Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko4Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="f8ce0370-2fcc-4770-a3e7-a46e638daf81" positionType="Float" x="142" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko4Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="39564c39-87a9-4103-ba70-696efa50b6b5" positionType="Float" x="189" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko5Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko5Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="6c31752e-cb89-490d-9b99-9e7d53eda02e" positionType="Float" x="189" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko5Knj}.isEmpty() && $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="f867d067-1c69-498e-8ae5-5d8660d964b2" positionType="Float" x="236" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko6Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko6Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="f1ed161e-7ad4-43fc-9499-aa4f0699fe5e" positionType="Float" x="236" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1&& !$F{hanko6Knj}.isEmpty() && $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="f46ec92f-8477-4c4c-b4a1-8f294fd3c19d" positionType="Float" x="283" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1&&  !$F{hanko7Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko7Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="5170d191-dab5-4bb2-9b89-bf857963563d" positionType="Float" x="283" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1&&  !$F{hanko7Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="4e13c3ce-8803-4454-b019-0406d38fd385" positionType="Float" x="330" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko8Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko8Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="956f872b-9d88-49fe-a384-98cad627e0a9" positionType="Float" x="330" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko8Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="9759824b-a01f-482d-a859-f574c38eb2a6" positionType="Float" x="377" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1&& !$F{hanko9Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko9Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="4ec6e5b3-4811-4ded-8e0f-ea3dbcd5a52c" positionType="Float" x="377" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1&& !$F{hanko9Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="9e4f59b5-3464-4639-844f-3fc7a4e0bee2" positionType="Float" x="424" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko10Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko10Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="33886da1-71ce-4cf4-b331-b3c9ea4125e9" positionType="Float" x="424" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko10Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="998d7c96-e7e1-45cd-b31c-e3147ba3132a" positionType="Float" x="471" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko11Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko11Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="08a6aa27-c0c6-4682-b988-f8ebab898adf" positionType="Float" x="471" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko11Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="0ae299ec-532c-4785-8693-9024a13bf9f5" positionType="Float" x="518" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko12Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko12Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="ec12afb5-91dc-4206-9285-46aa51889cad" positionType="Float" x="518" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko12Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="f135ef59-564d-4a59-b210-8d3a33bfaacc" positionType="Float" x="565" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko13Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko13Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="3965893f-9aa8-4255-a511-34a3875c8a01" positionType="Float" x="565" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko13Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="be1211ca-b21f-4726-8c74-b65205bdc2b4" positionType="Float" x="612" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko14Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko14Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="4cc5e612-98ac-49af-8d7b-ea578a105f57" positionType="Float" x="612" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko14Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="8ff5570f-6b00-42dc-9840-737bc11309dc" positionType="Float" x="659" y="0" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko15Knj}.isEmpty() && $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{hanko15Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="382f5dba-7423-4b52-bb2d-97b941178254" positionType="Float" x="659" y="10" width="47" height="37">
				<printWhenExpression><![CDATA[$F{inkanFlg} == 1 && !$F{hanko15Knj}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
		</element>
		<element kind="textField" uuid="5f2b2b34-0bc2-4622-baac-063bcbadd30e" x="644" y="0" width="100" height="12" fontName="IPAexGothic" fontSize="9.0" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$F{bunsyoKanriNo}]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="117">
			<element kind="component" uuid="e5ee3a0b-e3f9-4afb-a90c-802f12be74e3" x="0" y="2" width="738" height="115" printWhenDetailOverflows="true">
				<component kind="table">
					<datasetRun uuid="b63daf5f-22f9-4946-b0f4-b55106ac2348" subDataset="Dataset1">
						<dataSourceExpression><![CDATA[$F{moniitiranDataList}]]></dataSourceExpression>
						<parameter name="mojiSize">
							<expression><![CDATA[$F{mojiSize}]]></expression>
						</parameter>
					</datasetRun>
					<column kind="single" uuid="4021fc8c-501b-4818-9c8f-2f7d4bcb7a1c" width="738">
						<columnHeader height="28" rowSpan="1" style="Style1">
							<element kind="frame" uuid="2767dbca-21c9-4510-9e11-85277e66886b" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="738" height="28" printWhenDetailOverflows="true">
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<element kind="staticText" uuid="7fb3b67e-d534-4ae8-bbcc-0edb473f54c7" x="46" y="0" width="252" height="14" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[サービスの実行確認および確認方法]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="47a62327-f7a9-4d52-a720-3dfa6b494770" x="46" y="14" width="96" height="14" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[実行確認]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<element kind="staticText" uuid="6f37e390-64a7-4e4b-9c29-55d7b3a0ab9f" x="142" y="14" width="96" height="14" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 確認方法]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="17b72411-4e67-4dc1-b594-a51eccab13a6" x="238" y="14" width="60" height="14" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 確認期日]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<element kind="staticText" uuid="faaa026a-204b-4c36-b736-702a851465e0" x="298" y="0" width="196" height="14" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[利用者本人・家族の意見・要望]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="78787cda-043a-4279-ab48-db121b3fb93b" x="298" y="14" width="98" height="14" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[本人]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="fcbfba33-cdb4-408b-9d03-40e88f6a1888" x="396" y="14" width="98" height="14" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[家族]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<element kind="staticText" uuid="1b7d5e2d-f9ba-4938-82f9-3cc6dcaba817" x="494" y="0" width="122" height="28" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Top">
									<text><![CDATA[ニーズ充足度]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box topPadding="3">
										<leftPen lineWidth="0.75"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="staticText" uuid="8ac5fc45-c549-43f3-b45d-7d1a9d05c49a" x="616" y="0" width="122" height="28" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Top">
									<text><![CDATA[ 対応
]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box topPadding="3"/>
								</element>
								<element kind="staticText" uuid="4e21e1a9-978f-40a5-bccf-ace7abda8b28" mode="Opaque" x="0" y="0" width="23" height="28" backcolor="rgba(250, 9, 5, 0.0)" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Top">
									<text><![CDATA[課題
番号]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box topPadding="3">
										<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="staticText" uuid="e6fb1627-1d52-4e76-9c08-b413f2afc065" mode="Opaque" x="23" y="0" width="23" height="28" backcolor="rgba(250, 9, 5, 0.0)" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true" hTextAlign="Center" vTextAlign="Top">
									<text><![CDATA[番号]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box topPadding="3">
										<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box style="Style1">
								<topPen lineWidth="1.6"/>
								<bottomPen lineWidth="0.75"/>
								<rightPen lineWidth="1.6"/>
							</box>
						</columnHeader>
						<columnFooter height="10" rowSpan="1">
							<element kind="frame" uuid="5282d99b-cbba-4254-8356-27e63838c622" positionType="Float" stretchType="NoStretch" x="0" y="0" width="738" height="10" backcolor="#FFFFFF">
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="1.6" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</columnFooter>
						<detailCell height="72" style="Table_TD">
							<element kind="frame" uuid="69d69011-0cb0-4fdc-ab63-94937c5d73df" positionType="Float" stretchType="ElementGroupHeight" x="0" y="0" width="738" height="72">
								<borderSplitType>DrawBorders</borderSplitType>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<element kind="textField" uuid="e6f92442-564e-45b1-87c4-25964bb50f37" stretchType="ContainerHeight" x="0" y="0" width="23" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Right" vTextAlign="Top">
									<expression><![CDATA[$F{kaidaiNo}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="0" bottomPadding="1" rightPadding="1">
										<pen lineWidth="0.75"/>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="e4fcf506-473a-4211-a1d5-3c87e28941c9" stretchType="ContainerHeight" x="23" y="0" width="23" height="72" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Right" vTextAlign="Top">
									<expression><![CDATA[$F{servNo}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="0" bottomPadding="1" rightPadding="1">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="ecbe1c2b-c351-4126-8b34-3d1c1fca9e79" stretchType="ContainerHeight" x="46" y="0" width="96" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<element kind="textField" uuid="9e20ad23-1368-4da7-85c0-b8d0e5c74292" stretchType="NoStretch" mode="Transparent" x="0" y="0" width="96" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" printWhenDetailOverflows="true">
										<expression><![CDATA[$F{kakuninCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<pen lineWidth="0.4"/>
											<topPen lineWidth="0.0"/>
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
											<rightPen lineWidth="0.0"/>
										</box>
									</element>
									<element kind="textField" uuid="b3cd8f88-aca1-42a2-bd0a-a8516376d175" stretchType="ContainerHeight" x="0" y="32" width="96" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{kakuninKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1"/>
									</element>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="1.0"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="f0b6d2e8-0ddb-4ae4-bc19-e6bdf0154b2b" stretchType="ContainerHeight" x="142" y="0" width="96" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="ccefae28-7c50-4b2f-b944-e138bd9740d2" stretchType="NoStretch" x="0" y="0" width="96" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{houhouCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<leftPen lineWidth="0.0"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="94aa1c54-91c0-4fbc-9e18-2a14e5388845" stretchType="ContainerHeight" x="0" y="32" width="96" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{houhouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="1">
											<leftPen lineWidth="0.0"/>
										</box>
									</element>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="ed8a8c54-07fa-4030-af45-3fbd0556f719" stretchType="ContainerHeight" x="238" y="0" width="60" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<element kind="textField" uuid="118166dc-c752-4200-979c-071363e50b6a" stretchType="ContainerHeight" x="0" y="0" width="60" height="32" markup="html" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<printWhenExpression><![CDATA[!$F{kakuninYmdYY}.equals("")]]></printWhenExpression>
										<expression><![CDATA[$F{kakuninYmdGG}+
"<font style='font-size:10pt'>"+$F{kakuninYmdYY}+"</font>"+
"/"+
"<font style='font-size:10pt'>"+$F{kakuninYmdMM}+"</font>"+
"/"+
"<font style='font-size:10pt'>"+$F{kakuninYmdDD}+"</font>"]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box topPadding="2" leftPadding="1" rightPadding="1">
											<leftPen lineWidth="0.0"/>
											<rightPen lineWidth="0.0"/>
										</box>
									</element>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="2b27fd0e-e5d4-4fd0-be10-a956147ffc90" stretchType="ContainerHeight" x="298" y="0" width="98" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="70fb2c76-86c2-4046-ba9e-f0c020626480" stretchType="NoStretch" x="0" y="0" width="98" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{ikenHonCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="30db85e8-e16d-4768-80bd-d27879d61cc5" stretchType="ContainerHeight" mode="Transparent" x="0" y="32" width="98" height="40" backcolor="#FFFFFF" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{ikenHonKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2"/>
									</element>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="f6c44241-97c6-460b-966e-29d8e1be0324" stretchType="ContainerHeight" x="396" y="0" width="98" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<element kind="textField" uuid="8a14a537-4fd5-4bad-aaae-6136ade0da9e" stretchType="NoStretch" x="0" y="0" width="98" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{ikenKazCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<element kind="textField" uuid="a641dc1b-0ec9-4d76-b85e-0e3e6e6ffafb" stretchType="ContainerHeight" x="0" y="32" width="98" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{ikenKazKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
											<rightPen lineWidth="0.75"/>
										</box>
									</element>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="f9f0ab2f-e419-4de4-b3cb-b1e9e4b8c23c" stretchType="ContainerHeight" x="494" y="0" width="122" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="94d5fa5f-c569-496b-abe4-b77f20ec8708" stretchType="NoStretch" x="0" y="0" width="122" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{jusokuCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="287b3ba9-91eb-4c1e-b210-1797024b0e1e" stretchType="ContainerHeight" x="0" y="32" width="122" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{jusokuKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2"/>
									</element>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="041efef2-e5ee-4f4d-a0c7-4e71f22e686c" stretchType="ContainerHeight" x="616" y="0" width="122" height="72">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="35e69f9e-4b92-440f-8238-e1cb0bacf57a" stretchType="NoStretch" x="0" y="0" width="122" height="32" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
										<expression><![CDATA[$F{taiouCd}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
											<bottomPen lineWidth="0.4" lineStyle="Dashed"/>
										</box>
									</element>
									<element kind="textField" uuid="db81d9de-4c6a-42da-8078-9ccf8869c19a" stretchType="ContainerHeight" x="0" y="32" width="122" height="40" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight">
										<expression><![CDATA[$F{taiouKnj}]]></expression>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
										<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
											<leftPen lineWidth="0.75"/>
										</box>
									</element>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="563512d7-a71f-4ea8-862a-3afd338fed92" stretchType="ContainerHeight" x="46" y="0" width="96" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="1.5"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="e4e5ae41-9707-4130-8efc-676e8548cd12" stretchType="ContainerHeight" x="142" y="0" width="96" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="161461e8-06bb-4552-9219-dc27c84fa25e" stretchType="ContainerHeight" x="238" y="0" width="60" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="d8074c21-710f-4b68-b9d9-55a34db95ac2" stretchType="ContainerHeight" x="298" y="0" width="98" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="9daad8fb-2e19-4b04-9031-c14e0e5e3ce6" stretchType="ContainerHeight" x="298" y="0" width="98" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="655e26d6-c130-49de-85ae-5b89af4d650e" stretchType="ContainerHeight" x="396" y="0" width="98" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="11ae948a-415b-426a-b060-62cd7c334850" stretchType="ContainerHeight" x="494" y="0" width="122" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="ac165c29-28f5-4d9e-bc95-ef60f9aab57f" stretchType="ContainerHeight" x="616" y="0" width="122" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="2c6e94c0-a65b-464d-af09-bef32d915b06" stretchType="ContainerHeight" x="0" y="0" width="23" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="frame" uuid="5017959c-63b6-475a-ad01-c8cd51de1ebf" stretchType="ContainerHeight" x="23" y="0" width="23" height="72" printWhenDetailOverflows="true">
									<borderSplitType>DrawBorders</borderSplitType>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
										<bottomPen lineWidth="1.6"/>
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box style="Table_TD">
								<bottomPen lineWidth="0.75"/>
								<rightPen lineWidth="1.6"/>
							</box>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
				<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
				<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="54" splitType="Stretch">
			<element kind="component" uuid="aa63ce0d-a3de-4c7f-9b1a-a74145ce80f0" x="0" y="0" width="711" height="51">
				<component kind="table">
					<datasetRun uuid="0d97e7a9-521c-4415-99a2-3430b00f73fc" subDataset="Dataset2">
						<dataSourceExpression><![CDATA[$F{sokatuHenkoList}]]></dataSourceExpression>
						<parameter name="mojiSize">
							<expression><![CDATA[$F{mojiSize}]]></expression>
						</parameter>
					</datasetRun>
					<column kind="single" uuid="aa4d59ae-2471-4146-ad83-7e6ffa4a733a" width="711">
						<columnHeader height="18" style="Style1">
							<element kind="frame" uuid="875d5d48-5456-4fbd-a68e-123ca09e994a" positionType="Float" stretchType="ContainerHeight" x="0" y="0" width="711" height="18">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="staticText" uuid="50cc8e33-c8d4-4027-b756-7b40a1e76ad9" mode="Opaque" x="0" y="0" width="318" height="18" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="12.0" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[総括]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<rightPen lineWidth="0.75" lineColor="#000000"/>
									</box>
								</element>
								<element kind="staticText" uuid="8c208208-52ed-4d05-95d8-c7adc62bc8f5" mode="Opaque" x="318" y="0" width="393" height="18" backcolor="#EBEBEB" fontName="IPAexGothic" fontSize="12.0" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[ 計画の変更等]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box>
										<leftPen lineWidth="0.75"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="1.6"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box style="Style1">
								<pen lineWidth="1.6"/>
								<topPen lineWidth="1.6" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.6" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.6" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.6" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</columnHeader>
						<columnFooter height="17">
							<element kind="frame" uuid="c26b1eca-4dcf-4f18-8d74-6633efe2b669" x="0" y="0" width="711" height="17">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<element kind="staticText" uuid="44301ade-e0bf-431f-8038-7132c757c321" x="0" y="2" width="140" height="14" fontName="IPAexGothic" fontSize="12.0" hTextAlign="Left" vTextAlign="Middle">
									<text><![CDATA[再アセスメントの必要    ]]></text>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<box leftPadding="4"/>
								</element>
								<element kind="staticText" uuid="16f18f55-0f2d-42fb-89b6-1862e6194aca" x="140" y="2" width="44" height="14" fontName="IPAexGothic" fontSize="12.0" hTextAlign="Left" vTextAlign="Middle">
									<text><![CDATA[なし]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<element kind="staticText" uuid="e4b743a6-3e58-494c-9126-083a190c96c2" x="184" y="2" width="52" height="14" fontName="IPAexGothic" fontSize="12.0" vTextAlign="Middle">
									<text><![CDATA[あり]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="rectangle" uuid="6876a40f-c40f-4923-80d3-887b0d9860d7" x="130" y="2" width="45" height="14" forecolor="#0C0CFE" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
									<printWhenExpression><![CDATA[$F{retryChk}.equals("0")]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="rectangle" uuid="904ae265-57ed-4f36-97bb-e3bbebb9f5b5" x="175" y="2" width="45" height="14" forecolor="#0C0CFE" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
									<printWhenExpression><![CDATA[$F{retryChk}.equals("1")]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="staticText" uuid="a3c866a6-65d7-4789-bf7f-55f380d2d162" x="236" y="2" width="85" height="14" fontName="IPAexGothic" fontSize="12.0" vTextAlign="Middle">
									<text><![CDATA[（実施予定日：]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="textField" uuid="8b25bb34-96f3-4abc-9e5f-a983ee37913b" x="321" y="2" width="60" height="14" markup="html">
									<printWhenExpression><![CDATA[!$F{yoteiYmdGG}.equals("")]]></printWhenExpression>
									<expression><![CDATA[$F{yoteiYmdGG}+
"<font style='font-size:11pt'>"+$F{yoteiYmdYY}+"</font>"+
"/"+
"<font style='font-size:11pt'>"+$F{yoteiYmdMM}+"</font>"+
"/"+
"<font style='font-size:11pt'>"+$F{yoteiYmdDD}+"</font>"]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="staticText" uuid="5ffa8a56-da1f-441d-9113-14252ca9c38c" x="381" y="2" width="10" height="14">
									<text><![CDATA[)]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<box leftPadding="0"/>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</columnFooter>
						<detailCell height="16">
							<element kind="frame" uuid="2ddcd74a-3dc8-48dd-8892-7de130a6da63" positionType="Float" stretchType="ContainerHeight" x="0" y="0" width="711" height="16">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="textField" uuid="5f8c961b-1e7f-4519-ba37-90d282cd6153" stretchType="ContainerHeight" x="0" y="0" width="318" height="16" fontName="IPAexMincho" fontSize="12.0" textAdjust="StretchHeight">
									<expression><![CDATA[$F{soukatuKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="2">
										<rightPen lineWidth="0.75"/>
									</box>
								</element>
								<element kind="textField" uuid="4d3cc605-965b-4977-b7d3-a4ca6ce87dac" stretchType="ContainerHeight" x="318" y="0" width="393" height="16" fontName="IPAexMincho" fontSize="12.0" textAdjust="StretchHeight">
									<expression><![CDATA[$F{henkouKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{mojiSize}]]></propertyExpression>
									<box topPadding="1" leftPadding="2" bottomPadding="1" rightPadding="5"/>
								</element>
								<box>
									<leftPen lineWidth="1.6"/>
									<bottomPen lineWidth="1.6"/>
									<rightPen lineWidth="1.6"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
				<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
				<property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="28" splitType="Stretch">
			<printWhenExpression><![CDATA[$F{syoninPrintFlg} == 1]]></printWhenExpression>
			<element kind="subreport" uuid="79e48678-6e63-4b52-bb7d-5a38f9fce2f2" key="com_report" stretchType="NoStretch" x="30" y="5" width="680" height="20">
				<dataSourceExpression><![CDATA[
    new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(
        ((net.sf.jasperreports.engine.data.JRBeanCollectionDataSource)$P{subReportDataDs}).getData()
    )
        ]]></dataSourceExpression>
				<expression><![CDATA[$P{subReportPath}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="20">
		<element kind="textField" uuid="a1065ce4-c521-4b85-8ee7-05c06e4d8369" x="349" y="1" width="17" height="17" fontName="IPAexGothic" fontSize="8.0" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[ $V{PAGE_NUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="da7b282a-324a-4eeb-8c5d-3064f18a5819" x="366" y="1" width="20" height="17" markup="html" fontName="IPAexGothic" fontSize="8.0" evaluationTime="Report" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["<font style='font-size:9pt'>"+" / "+"</font>" + $V{PAGE_NUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
