<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="Blank_A4_Landscape_1" language="java" columnCount="1" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="842" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="4b21db13-62cc-40e8-ae7c-af95fcf1a667">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="subreport1" class="net.sf.jasperreports.engine.JasperReport"/>
	<parameter name="subreport2" class="net.sf.jasperreports.engine.JasperReport"/>
	<field name="data1" class="net.sf.jasperreports.engine.JRDataSource">
		<description><![CDATA[data1]]></description>
	</field>
	<field name="data2" class="net.sf.jasperreports.engine.JRDataSource">
		<description><![CDATA[data2]]></description>
	</field>
	<detail>
		<band height="595">
			<element kind="subreport" uuid="593a760c-a985-41b0-933e-85e3b012f36a" x="0" y="0" width="842" height="595">
				<dataSourceExpression><![CDATA[$F{data1}]]></dataSourceExpression>
				<expression><![CDATA[$P{subreport1}]]></expression>
			</element>
		</band>
		<band height="595">
			<element kind="break" uuid="755cc172-3fa9-4036-93a8-449866dfbc05" x="0" y="0" width="842" height="1"/>
			<element kind="subreport" uuid="e1d217e0-2ecd-4f72-ab2c-d8fd4f19e5b9" x="0" y="0" width="842" height="595">
				<dataSourceExpression><![CDATA[$F{data2}]]></dataSourceExpression>
				<expression><![CDATA[$P{subreport2}]]></expression>
			</element>
		</band>
	</detail>
</jasperReport>
