<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="U06060_情報収集シート（1）" language="java" columnCount="1" pageWidth="595" pageHeight="842" orientation="Landscape" columnWidth="550" leftMargin="44" rightMargin="1" topMargin="28" bottomMargin="20" uuid="95b152d9-156d-4073-8f78-d9eb987ca733" titleNewPage="true">
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="477"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="523"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="Table_TH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 4_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 4_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 4_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CG" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CT" mode="Opaque" backcolor="#005FB3">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="tanntoFont">
		<conditionalStyle pattern="" fontName="IPAexGothic" fontSize="11.0">
			<conditionExpression><![CDATA[$F{tantoFont}.equals("2")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle markup="none" fontName="IPAexGothic" fontSize="12.0">
			<conditionExpression><![CDATA[$F{tantoFont}.equals("1")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle fontName="IPAexGothic" fontSize="10.0">
			<conditionExpression><![CDATA[$F{tantoFont}.equals("3")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle fontName="IPAexGothic" fontSize="9.0">
			<conditionExpression><![CDATA[$F{tantoFont}.equals("4")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle fontName="IPAexGothic" fontSize="7.0">
			<conditionExpression><![CDATA[$F{tantoFont}.equals("5")]]></conditionExpression>
		</conditionalStyle>
	</style>
	<style name="riyoushaFont">
		<conditionalStyle pattern="" fontName="IPAexGothic" fontSize="11.0">
			<conditionExpression><![CDATA[$F{riyoushaNmFont}.equals("2")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle markup="none" fontName="IPAexGothic" fontSize="12.0">
			<conditionExpression><![CDATA[$F{riyoushaNmFont}.equals("1")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle fontName="IPAexGothic" fontSize="10.0">
			<conditionExpression><![CDATA[$F{riyoushaNmFont}.equals("3")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle fontName="IPAexGothic" fontSize="9.0">
			<conditionExpression><![CDATA[$F{riyoushaNmFont}.equals("4")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle fontName="IPAexGothic" fontSize="7.0">
			<conditionExpression><![CDATA[$F{riyoushaNmFont}.equals("5")]]></conditionExpression>
		</conditionalStyle>
	</style>
	<dataset name="Dataset1" uuid="abb33cc0-06b7-4d36-980c-bda1cbea8d39">
		<field name="level2Knj" class="java.lang.String">
			<description><![CDATA[level2Knj]]></description>
		</field>
		<field name="level3KoumokuNo" class="java.lang.String">
			<description><![CDATA[level3KoumokuNo]]></description>
		</field>
		<field name="level3Knj" class="java.lang.String">
			<description><![CDATA[level3Knj]]></description>
		</field>
		<field name="memo1Knj" class="java.lang.String">
			<description><![CDATA[memo1Knj]]></description>
		</field>
		<field name="kento" class="java.lang.String">
			<description><![CDATA[kento]]></description>
		</field>
		<field name="level3KoumokuNoFont" class="java.lang.String">
			<description><![CDATA[level3KoumokuNoFont]]></description>
		</field>
		<field name="level3KnjFont" class="java.lang.String">
			<description><![CDATA[level3KnjFont]]></description>
		</field>
	</dataset>
	<parameter name="InvoiceItems" class="net.sf.jasperreports.engine.JRDataSource"/>
	<field name="jigyoName" class="java.lang.String">
		<description><![CDATA[jigyoName]]></description>
	</field>
	<field name="riyoushaNm" class="java.lang.String">
		<description><![CDATA[riyoushaNm]]></description>
	</field>
	<field name="createYmd" class="java.lang.String">
		<description><![CDATA[createYmd]]></description>
	</field>
	<field name="shiTeiDate" class="java.lang.String">
		<description><![CDATA[shiTeiDate]]></description>
	</field>
	<field name="tantoNm" class="java.lang.String">
		<description><![CDATA[tantoNm]]></description>
	</field>
	<field name="keisho" class="java.lang.String">
		<description><![CDATA[keisho]]></description>
	</field>
	<field name="level1Knj" class="java.lang.String">
		<description><![CDATA[level1Knj]]></description>
	</field>
	<field name="list2" class="net.sf.jasperreports.engine.JRDataSource">
		<description><![CDATA[list2]]></description>
	</field>
	<field name="list" class="net.sf.jasperreports.engine.JRDataSource">
		<description><![CDATA[list]]></description>
	</field>
	<field name="shiTeiKubun" class="java.lang.Integer">
		<description><![CDATA[shiTeiKubun]]></description>
	</field>
	<field name="kaiteiKbn" class="java.lang.Integer">
		<description><![CDATA[kaiteiKbn]]></description>
	</field>
	<field name="chushakuFlg" class="java.lang.Boolean">
		<description><![CDATA[chushakuFlg]]></description>
	</field>
	<field name="tantoFont" class="java.lang.String">
		<description><![CDATA[tantoFont]]></description>
	</field>
	<field name="riyoushaNmFont" class="java.lang.String">
		<description><![CDATA[riyoushaNmFont]]></description>
	</field>
	<field name="shosiki2Flg" class="java.lang.String"/>
	<pageHeader height="546" splitType="Stretch">
		<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == 1]]></printWhenExpression>
		<element kind="frame" uuid="84a57d47-d073-4214-bae4-f849141b9fa2" x="1" y="59" width="514" height="47">
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<element kind="staticText" uuid="5153e057-5ccd-446e-86f0-3785c75e41db" x="1" y="0" width="170" height="22" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[利用者氏名]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="ff2f142e-dd3d-447e-ba2c-475aca266531" x="170" y="0" width="172" height="22" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[基準日]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="870200b4-1a5e-49d8-83e8-83e2d9371a75" x="342" y="0" width="172" height="22" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[担当者名]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="22f3ff5f-c4c9-4895-be44-7a175c050a02" x="1" y="22" width="140" height="25" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" blankWhenNull="true" printRepeatedValues="false" vTextAlign="Middle" style="riyoushaFont">
				<paragraph leftIndent="8"/>
				<expression><![CDATA[$F{riyoushaNm}]]></expression>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box style="riyoushaFont">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="78b693bc-fa94-41b6-adb4-e6db5a8984bc" x="140" y="22" width="30" height="25" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<expression><![CDATA[$F{keisho}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="0a97562e-eed3-4ab0-9554-e0513d6b7a9c" x="170" y="22" width="172" height="25" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<expression><![CDATA[$F{createYmd}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="3f79d11b-25c8-4925-bfb6-c1ea4972d5c9" stretchType="NoStretch" x="342" y="22" width="172" height="25" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" blankWhenNull="true" printRepeatedValues="false" vTextAlign="Middle" style="tanntoFont">
				<paragraph leftIndent="1"/>
				<expression><![CDATA[$F{tantoNm}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box style="tanntoFont">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<box>
				<pen lineWidth="1.75"/>
				<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="textField" uuid="c57a485a-714b-4969-a843-68306dc8232b" positionType="Float" stretchType="ContainerHeight" x="408" y="0" width="100" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" blankWhenNull="true" hTextAlign="Right" style="Table_CH">
			<printWhenExpression><![CDATA[$F{shiTeiKubun} != 1]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDate}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<box style="Table_CH">
				<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="textField" uuid="2836b189-e03c-4995-9f81-3eb18af80d4f" x="408" y="10" width="100" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" blankWhenNull="true" hTextAlign="Right" style="Table_CH">
			<expression><![CDATA[$F{jigyoName}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<box style="Table_CH">
				<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="staticText" uuid="5be3da5d-ec6c-48e8-9683-98b158b94776" x="3" y="34" width="508" height="14" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
			<text><![CDATA[アセスメントのための情報収集シート151（施設）]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<box>
				<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="frame" uuid="b9be5a5b-ba61-4c13-8d44-f0b3e73d6c2e" x="28" y="107" width="457" height="399" removeLineWhenBlank="true">
			<printWhenExpression><![CDATA[$F{chushakuFlg}]]></printWhenExpression>
			<property name="ShowOutOfBoundContent" value="true"/>
			<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<element kind="frame" uuid="b01edda0-e85a-465b-be28-afb9aea6e143" mode="Transparent" x="10" y="3" width="436" height="218" removeLineWhenBlank="true">
				<printWhenExpression><![CDATA[$F{chushakuFlg} && $F{kaiteiKbn} == 1]]></printWhenExpression>
				<property name="ShowOutOfBoundContent" value="true"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<element kind="staticText" uuid="b51cc332-9f3a-44fe-8b68-0875e9c37ead" stretchType="ContainerHeight" x="1" y="10" width="419" height="206" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" italic="false" vTextAlign="Middle">
					<paragraph lineSpacing="Proportional" lineSpacingSize="1.15"/>
					<text><![CDATA[※１. 「具体的状況」の欄には、情報項目に関しての能力評価をしてその状況を記入しま
　　す。生活の支障がある場合はその具体的状況と気付いたコメントを記入します。
　　（例えば：身体を支えられれば４～５メートル歩く。２週間前までは杖を使って自力
　　   で歩いていた。・・・等）

※２. 「検討」の欄は、具体的状況が生活に支障がある場合や、詳しく検討する必要があ
　　ると判断した場合は○を付けます（支障がない場合は×を付けます。）。
　　  太字の番号で、○が付いた「具体的状況」を、課題検討用紙の「検討が必要な具体
　　的状況」の欄に転記し、「自立に向けた改善（回復）の可能性、維持の必要性、低下・
　　悪化の危険性、ケアの必要性」を検討します。


※３. ＊の付いた項目は、概ね３ヶ月間の状況で判断します。それ以外は１週間前後の状
　　況で判断します。

※４. 可能性の提案をしながら、その領域に関する生活の意向を把握し、課題検討用紙の
　　「本人、家族の意向」に転記します。（生活上の支障がない領域に関する「意向」
　　は、聞き取る必要はありません。）]]></text>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<element kind="staticText" uuid="ba6797f6-f206-42f6-8dea-05957648bb49" stretchType="ContainerHeight" x="236" y="113" width="170" height="10" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" italic="true" vTextAlign="Middle">
					<text><![CDATA[（細字の項目は、検討する時の参考に]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<element kind="staticText" uuid="454a678f-87e6-4196-a153-78bad34ec810" stretchType="ContainerHeight" x="2" y="124" width="70" height="10" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" italic="true" vTextAlign="Middle">
					<text><![CDATA[　　します。）]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
			</element>
			<element kind="frame" uuid="60d559a7-6439-4ac0-85de-2b93230629f5" x="10" y="225" width="430" height="174" removeLineWhenBlank="true">
				<printWhenExpression><![CDATA[$F{chushakuFlg} && $F{kaiteiKbn} == 0]]></printWhenExpression>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<element kind="staticText" uuid="444d6ed8-f0dd-40c7-9414-56a926d0e644" stretchType="ContainerHeight" x="2" y="0" width="418" height="174" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" italic="false" vTextAlign="Middle">
					<paragraph lineSpacing="Proportional" lineSpacingSize="1.1"/>
					<text><![CDATA[※１. 「検討」の欄は、問題や支障がなければ×、問題や支障があれば○を付ける。

※２. 「具体的状況」の欄には、×の場合はその理由、○の場合は支障のある具体的状況
　　と気付いたコメントを記入する。（例えば：身体を支えられれば４～５メートル歩く。
　　２週間前までは杖を使って自力で歩いていた。・・・等）

※３. ＊の付いた項目は、概ね３ヶ月間の状況で判断する。それ以外は１週間前後の状況
　　で判断する。

※４. その領域に関する生活の意向を把握し、課題検討用紙の「本人、家族の意向」に転
　　記します。（問題や支障がない領域に関する「意向」は、聞き取る必要はない。）

※５.
　  　  　  　  　 　                          「自立に向けた可能性、維持の必要性、低下・悪化の
　  危険性、ケアの必要性」を検討します。(細字の項目は、検討する時の参考にします。)]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<element kind="staticText" uuid="8c8db94c-a4cb-49d4-8aca-5de598a797c6" stretchType="ContainerHeight" x="18" y="133" width="388" height="16" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" bold="true" italic="false" vTextAlign="Middle">
					<paragraph lineSpacing="Proportional" lineSpacingSize="1.15"/>
					<text><![CDATA[  太字の項目のうち、○が付いた「具体的状況」を、課題検討用紙の「検討が必要な
]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<element kind="staticText" uuid="65eab59f-46f0-4a86-a224-831a3125dcee" stretchType="ContainerHeight" x="2" y="145" width="340" height="14" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" bold="true" italic="false" vTextAlign="Middle">
					<paragraph lineSpacing="Proportional" lineSpacingSize="1.15"/>
					<text><![CDATA[　  具体的状況」の欄に転記して、]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
			</element>
		</element>
		<element kind="frame" uuid="c34572f6-4882-40be-9376-63438c80098b" x="1" y="507" width="515" height="39">
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<element kind="staticText" uuid="17290917-5e91-4151-b79c-af68774c06a9" x="4" y="0" width="24" height="19" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{kaiteiKbn} == 1]]></printWhenExpression>
				<text><![CDATA[1]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
			</element>
			<element kind="textField" uuid="16072c5a-1793-4d75-a9f7-329de92c945f" stretchType="ContainerHeight" x="32" y="0" width="137" height="19" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{kaiteiKbn} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{level1Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="54934c74-2602-4539-a0db-285de0c5aba9" x="0" y="19" width="26" height="21" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[No]]></text>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="c0881d24-8c65-4945-af00-d8e3a0458884" x="26" y="19" width="190" height="21" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[情報項目]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="9bd5d1d4-57f1-47fb-afa4-b78e64835f29" x="216" y="19" width="272" height="21" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{kaiteiKbn} == 1]]></printWhenExpression>
				<text><![CDATA[具体的状況]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="5a4e8c1f-123e-495c-98ae-fc00d3fb31db" x="488" y="19" width="26" height="21" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{kaiteiKbn} == 1]]></printWhenExpression>
				<text><![CDATA[検                  討]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="3c89001f-b177-479d-bd7c-e41522413a10" x="242" y="19" width="272" height="21" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{kaiteiKbn} == 0]]></printWhenExpression>
				<text><![CDATA[具体的状況]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="03e8c770-1b31-445e-9802-1928a4d23aa6" x="216" y="19" width="26" height="21" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{kaiteiKbn} == 0]]></printWhenExpression>
				<text><![CDATA[検                  討]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="d24f18bb-6d92-4b02-987d-e4017fb6d34e" x="4" y="0" width="24" height="19" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{kaiteiKbn} == 0]]></printWhenExpression>
				<text><![CDATA[1]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
			</element>
			<element kind="textField" uuid="490f4a4a-fb8b-43fd-9f4c-d67771a06140" stretchType="ContainerHeight" x="32" y="0" width="137" height="19" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" bold="true" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{kaiteiKbn} == 0]]></printWhenExpression>
				<expression><![CDATA[$F{level1Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
		</element>
		<element kind="staticText" uuid="dd17ca2d-2dc1-46b2-a044-4f373fb6760a" x="7" y="38" width="508" height="14" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
			<printWhenExpression><![CDATA[$F{kaiteiKbn}==2]]></printWhenExpression>
			<text><![CDATA[アセスメントシート]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<box>
				<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="frame" uuid="e173b865-f291-4077-9799-72acb35226cb" stretchType="ContainerHeight" mode="Opaque" x="2" y="110" width="512" height="228" printWhenGroupChanges="GroupHeader" removeLineWhenBlank="true">
			<printWhenExpression><![CDATA[$F{chushakuFlg}&&$F{kaiteiKbn}==2]]></printWhenExpression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<element kind="staticText" uuid="c2a18e8a-9316-4ced-bac3-56ba15b8af86" stretchType="ContainerHeight" x="39" y="2" width="440" height="38" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" italic="false" vTextAlign="Middle">
				<paragraph lineSpacing="Proportional" lineSpacingSize="1.15"/>
				<text><![CDATA[※１ 「具体的状況」の欄には、情報項目に関しての心身の評価をしてその状況を記入しま
　　　す。生活の支障がある場合はその具体的状況と気付いたコメントを記入します。
]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<element kind="staticText" uuid="0ba67f8c-598c-447c-a659-2726696961d6" stretchType="ContainerHeight" x="60" y="41" width="281" height="29">
				<text><![CDATA[例)  身体在支えられれば方扎扎球4～5メ一トル歩く。
         2週間前までは杖を使って自力で步いていた。など]]></text>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="641d44fa-59a3-4c0e-9652-e9d8fe48e3dd" stretchType="ContainerHeight" x="39" y="71" width="440" height="28" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" italic="false" vTextAlign="Middle">
				<paragraph lineSpacing="Proportional" lineSpacingSize="1.15"/>
				<text><![CDATA[※２ 評価した具体的状況が、生活に支障をきたしている場合、「検討」の欄にOを付けま
    す（支障がない場合はxを付けます）。]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<element kind="staticText" uuid="53f30429-29f7-47f3-9248-4572a084c147" stretchType="ContainerHeight" x="60" y="102" width="379" height="53">
				<text><![CDATA[＞太字の番号(No.)は评価の項目です。〇が付いた「具体的状況」を、課題検討用紙
    の「生活の支障」の欄に転記し、「自立生活に向けた、改善(回復)の可能性、維
    持の必要性、低下·悪化の危険性、ゲアの必要性」を検討します。
＞細字の番号(No.)は状沉の项目です。検討する時の参考にします。]]></text>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="b26543aa-f89d-4bda-a194-be7cde4ffb60" stretchType="ContainerHeight" x="40" y="156" width="440" height="72" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" removeLineWhenBlank="true" italic="false" vTextAlign="Middle">
				<paragraph lineSpacing="Proportional" lineSpacingSize="1.15"/>
				<text><![CDATA[※３ ＊の付いた項目は、概ね３ヶ月間の状況で判断します。それ以外は１週間前後の
　　　状況で判断します。
※４ 可能性の提案をしながら、その領域に関する生活の意向を把握し、課題検討用紙の「本
    人、家族の意向」に転記します。（生活上の支障がない領域に関する「意向」は、聞き取
    る必要はありません。）]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="200" splitType="Immediate">
			<element kind="frame" uuid="8a794b1d-d08a-45e2-9b3e-aea674eb6679" stretchType="NoStretch" x="0" y="0" width="518" height="60">
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<element kind="component" uuid="979c54ab-7636-4fcc-ba6a-4b89214c44f7" positionType="Float" stretchType="NoStretch" x="0" y="18" width="515" height="39" removeLineWhenBlank="true" printWhenDetailOverflows="true">
					<component kind="table" whenNoDataType="NoDataCell">
						<datasetRun uuid="df2d79b6-c006-49a8-abbd-408514f94a42" subDataset="Dataset1">
							<dataSourceExpression><![CDATA[$F{list2}]]></dataSourceExpression>
						</datasetRun>
						<columnHeader splitType="Immediate">
							<printWhenExpression><![CDATA[]]></printWhenExpression>
						</columnHeader>
						<detail splitType="Prevent"/>
						<column kind="single" uuid="9d07ba16-99f6-43b6-a331-a48727056961" width="514">
							<tableHeader height="20" rowSpan="1">
								<element kind="textField" uuid="3be40df5-fcbb-4549-8a3d-2412f6f8bc94" stretchType="NoStretch" x="0" y="0" width="514" height="20" markup="none" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" vTextAlign="Middle">
									<paragraph leftIndent="4"/>
									<expression><![CDATA[$F{level2Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<box leftPadding="4">
										<pen lineWidth="1.75"/>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</tableHeader>
							<columnFooter height="2" rowSpan="1" style="Table_TD">
								<box style="Table_TD">
									<pen lineWidth="1.5"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnFooter>
							<detailCell height="19" style="Table 1_TD">
								<element kind="textField" uuid="9229b333-95af-4370-a9a2-1f13e9ea1594" stretchType="ContainerHeight" x="0" y="0" width="56" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" vTextAlign="Top">
									<paragraph spacingBefore="3"/>
									<expression><![CDATA[$F{level3Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box leftPadding="4">
										<pen lineWidth="1.0"/>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="ffbd830e-0d8e-4d85-925b-d1e75476be0a" stretchType="ContainerHeight" x="56" y="0" width="458" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" vTextAlign="Top">
									<paragraph spacingBefore="3"/>
									<expression><![CDATA[$F{memo1Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<box style="Table 1_TD">
									<pen lineWidth="2.0"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						</column>
					</component>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.components.autoresize.next" value="true"/>
					<property name="net.sf.jasperreports.export.xls.ignore.cell.border" value="true"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
				<element kind="component" uuid="27e92cc4-7b77-4574-bb58-88b4f415f20b" stretchType="NoStretch" x="0" y="-1" width="514" height="19" removeLineWhenBlank="true" printInFirstWholeBand="true" printWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{kaiteiKbn} != 0]]></printWhenExpression>
					<component kind="table" whenNoDataType="NoDataCell">
						<datasetRun uuid="4beeb14a-f3b3-4ab1-b1bc-756e4c9e4441" subDataset="Dataset1">
							<dataSourceExpression><![CDATA[$F{list}]]></dataSourceExpression>
						</datasetRun>
						<tableHeader/>
						<columnHeader/>
						<detail splitType="Stretch"/>
						<column kind="single" uuid="615975bf-908a-4cfd-8781-3d7e68f9ea2a" width="514">
							<detailCell height="20" style="Table 1_TD">
								<element kind="textField" uuid="a2d931d0-9e10-4005-99da-8c95bd5c2676" stretchType="ContainerHeight" x="0" y="0" width="514" height="19" markup="none" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" removeLineWhenBlank="true" vTextAlign="Middle">
									<paragraph leftIndent="4"/>
									<expression><![CDATA[$F{level2Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="2d234681-a3d4-4bb3-84e1-8a4b5d894c7c" stretchType="ContainerHeight" x="0" y="0" width="26" height="19" markup="none" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" blankWhenNull="true" removeLineWhenBlank="true" bold="true" hTextAlign="Center" vTextAlign="Top">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph firstLineIndent="0" rightIndent="4" spacingBefore="3"/>
									<expression><![CDATA[$F{level3KoumokuNo}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="51862ab9-cddc-423d-b8c3-6915dfd9f8a3" stretchType="ContainerHeight" x="26" y="0" width="190" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" removeLineWhenBlank="true" vTextAlign="Top">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph firstLineIndent="0" spacingBefore="3"/>
									<expression><![CDATA[$F{level3Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="4b822d5e-9562-480f-96ac-2ef7da5d8cee" stretchType="ContainerHeight" x="488" y="0" width="26" height="19" markup="none" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph rightIndent="4"/>
									<expression><![CDATA[$F{kento}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="ebf7e5a9-4fba-4469-92b1-51389ef56759" stretchType="ContainerHeight" x="216" y="0" width="272" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" vTextAlign="Top">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph firstLineIndent="0" spacingBefore="3"/>
									<expression><![CDATA[$F{memo1Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="c896b588-f563-4ac9-b3cb-69ce9b1c0bf4" stretchType="ContainerHeight" x="0" y="0" width="26" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" blankWhenNull="true" removeLineWhenBlank="true" bold="false" italic="true" hTextAlign="Center" vTextAlign="Top">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph firstLineIndent="0" rightIndent="4" spacingBefore="3"/>
									<expression><![CDATA[$F{level3KoumokuNoFont}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="2a7557b2-ab9c-47cd-a344-10b1b997671d" stretchType="ContainerHeight" x="26" y="0" width="190" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" removeLineWhenBlank="true" vTextAlign="Top">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph firstLineIndent="0" spacingBefore="3"/>
									<expression><![CDATA[$F{level3KnjFont}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="line" uuid="122738a2-b78e-40a8-8087-095f3ace8f25" stretchType="ContainerHeight" x="488" y="0" width="26" height="19" direction="BottomUp">
									<printWhenExpression><![CDATA[$F{shosiki2Flg}=="8"]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<box style="Table 1_TD">
									<pen lineWidth="1.0"/>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						</column>
					</component>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.components.autoresize.next" value="true"/>
					<property name="net.sf.jasperreports.export.xls.ignore.cell.border" value="true"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
				<element kind="component" uuid="024bbfa1-96a4-491d-a8af-598d7e22711f" stretchType="NoStretch" x="0" y="-1" width="514" height="19" removeLineWhenBlank="true" printInFirstWholeBand="true" printWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{kaiteiKbn} == 0]]></printWhenExpression>
					<component kind="table" whenNoDataType="NoDataCell">
						<datasetRun uuid="e82bc33f-1102-48c3-9ddc-e73e22077660" subDataset="Dataset1">
							<dataSourceExpression><![CDATA[$F{list}]]></dataSourceExpression>
						</datasetRun>
						<tableHeader/>
						<columnHeader/>
						<detail splitType="Stretch"/>
						<column kind="single" uuid="7c96b4a7-b9fa-4787-9342-3739a700df4d" width="514">
							<detailCell height="20" style="Table 1_TD">
								<element kind="textField" uuid="cf8ae07d-e1e2-4321-b3ba-f5b499a3490e" stretchType="ContainerHeight" x="0" y="0" width="514" height="19" markup="none" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" removeLineWhenBlank="true" bold="true" vTextAlign="Middle">
									<paragraph leftIndent="4"/>
									<expression><![CDATA[$F{level2Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="3b273032-bbb7-4768-ab58-3a76d6a6dfef" stretchType="ContainerHeight" x="0" y="0" width="26" height="19" markup="none" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" blankWhenNull="true" removeLineWhenBlank="true" bold="true" hTextAlign="Center" vTextAlign="Top">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph firstLineIndent="0" rightIndent="4" spacingBefore="3"/>
									<expression><![CDATA[$F{level3KoumokuNo}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="b827719e-d252-43fb-886d-273415b73300" stretchType="ContainerHeight" x="26" y="0" width="190" height="19" markup="none" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" printRepeatedValues="false" bold="true" vTextAlign="Top">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph firstLineIndent="0" spacingBefore="3"/>
									<expression><![CDATA[$F{level3Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="f0aefece-ba55-4914-9756-135717ea90fb" stretchType="ContainerHeight" x="216" y="0" width="26" height="19" markup="none" fontName="IPAexMincho" fontSize="13.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph rightIndent="4"/>
									<expression><![CDATA[$F{kento}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="bc633230-d303-4b38-8ca0-b384b9a1eac0" stretchType="ContainerHeight" x="242" y="0" width="272" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" vTextAlign="Top">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph firstLineIndent="0" spacingBefore="3"/>
									<expression><![CDATA[$F{memo1Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="68b1570e-93c0-406e-8e64-8db082b99171" stretchType="ContainerHeight" x="0" y="0" width="26" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" blankWhenNull="true" removeLineWhenBlank="true" bold="false" italic="true" hTextAlign="Center" vTextAlign="Top">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph firstLineIndent="0" rightIndent="4" spacingBefore="3"/>
									<expression><![CDATA[$F{level3KoumokuNoFont}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="1ee551a8-8ebd-41a7-bfc8-064d16d9dccc" stretchType="ContainerHeight" x="26" y="0" width="190" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" bold="false" vTextAlign="Top">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph firstLineIndent="0" spacingBefore="3"/>
									<expression><![CDATA[$F{level3KnjFont}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box style="Table 1_TD">
									<pen lineWidth="1.0"/>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						</column>
					</component>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.components.autoresize.next" value="true"/>
					<property name="net.sf.jasperreports.export.xls.ignore.cell.border" value="true"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
		</band>
	</detail>
</jasperReport>
