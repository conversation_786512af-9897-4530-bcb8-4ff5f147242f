<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="実施モニタリング表(計画実施表(番号あり・頻度あり)) " language="java" columnCount="1" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="797" leftMargin="30" rightMargin="15" topMargin="45" bottomMargin="10" uuid="ef7dacc3-146b-4ea6-8f5a-7d9a9b551bce">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.report.description" value=""/>
	<style name="TorikomiKnjBorderStyle" pattern="">
		<box>
			<leftPen lineWidth="1.0"/>
		</box>
		<conditionalStyle mode="Transparent" backcolor="#FFFFFF">
			<box>
				<topPen lineWidth="1.0" lineColor="#000000"/>
				<leftPen lineWidth="1.0"/>
			</box>
			<conditionExpression><![CDATA[($P{isSameOmitted}&&!$V{ IsSame_TorikomiKnj}&&!($F{torikomiKnj}.equals("")||$F{torikomiKnj}==null))||
(!$P{isBlankOmitted}&&($F{torikomiKnj}.equals("")||$F{torikomiKnj}==null))||
(!$P{isSameOmitted}&&!$P{isBlankOmitted}) ||
(!$P{isSameOmitted}&&$P{isBlankOmitted}&&!($F{torikomiKnj}.equals("")||$F{torikomiKnj}==null))]]></conditionExpression>
		</conditionalStyle>
	</style>
	<dataset name="DatailInfoDataset" uuid="166234be-a859-44e4-933e-9ec2ff65579a">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
		<parameter name="weekList" class="java.util.ArrayList"/>
		<parameter name="cpnFlg" class="java.lang.Integer"/>
		<parameter name="kkjTorikomi" class="java.lang.Integer"/>
		<parameter name="cksFlg" class="java.lang.Integer"/>
		<parameter name="lsFlg" class="java.lang.Boolean"/>
		<parameter name="holidayFlgList" class="java.util.ArrayList"/>
		<parameter name="isBlankOmitted" class="java.lang.Boolean"/>
		<parameter name="isSameOmitted" class="java.lang.Boolean"/>
		<parameter name="dayKinyuBikouKnjList" class="java.util.ArrayList"/>
		<parameter name="bikouKnj" class="java.lang.String"/>
		<parameter name="kinyuFlg" class="java.lang.Integer"/>
		<parameter name="dayFlg" class="java.lang.Integer"/>
		<parameter name="cbKrkRenkeiFlg" class="java.lang.Integer"/>
		<query language="sql"><![CDATA[]]></query>
		<field name="bango" class="java.lang.String"/>
		<field name="torikomiKnj" class="java.lang.String"/>
		<field name="tantoKnj" class="java.lang.String"/>
		<field name="hindoKnj" class="java.lang.String"/>
		<field name="day1" class="java.lang.String"/>
		<field name="day2" class="java.lang.String"/>
		<field name="day3" class="java.lang.String"/>
		<field name="day4" class="java.lang.String"/>
		<field name="day5" class="java.lang.String"/>
		<field name="day6" class="java.lang.String"/>
		<field name="day7" class="java.lang.String"/>
		<field name="day8" class="java.lang.String"/>
		<field name="day9" class="java.lang.String"/>
		<field name="day10" class="java.lang.String"/>
		<field name="day11" class="java.lang.String"/>
		<field name="day12" class="java.lang.String"/>
		<field name="day13" class="java.lang.String"/>
		<field name="day14" class="java.lang.String"/>
		<field name="day15" class="java.lang.String"/>
		<field name="day16" class="java.lang.String"/>
		<field name="day17" class="java.lang.String"/>
		<field name="day18" class="java.lang.String"/>
		<field name="day19" class="java.lang.String"/>
		<field name="day20" class="java.lang.String"/>
		<field name="day21" class="java.lang.String"/>
		<field name="day22" class="java.lang.String"/>
		<field name="day23" class="java.lang.String"/>
		<field name="day24" class="java.lang.String"/>
		<field name="day25" class="java.lang.String"/>
		<field name="day26" class="java.lang.String"/>
		<field name="day27" class="java.lang.String"/>
		<field name="day28" class="java.lang.String"/>
		<field name="day29" class="java.lang.String"/>
		<field name="day30" class="java.lang.String"/>
		<field name="day31" class="java.lang.String"/>
		<field name="cbKrkRenkeiFlg" class="java.lang.Integer"/>
		<variable name=" IsSame_TorikomiKnj" class="java.lang.Boolean">
			<expression><![CDATA[!$V{Pre_TorikomiKnj}.equals(null)&&$V{Pre_TorikomiKnj}.equals($F{torikomiKnj})]]></expression>
		</variable>
		<variable name="Pre_TorikomiKnj" class="java.lang.String">
			<expression><![CDATA[$F{torikomiKnj}]]></expression>
		</variable>
	</dataset>
	<dataset name="BikouDataset" uuid="f85d853e-63e7-4cc1-a1a3-621cd044d03d">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
		<query language="sql"><![CDATA[]]></query>
		<field name="bikouKnj" class="java.lang.String"/>
	</dataset>
	<parameter name="subReportPath" class="java.lang.String"/>
	<parameter name="subReportDataDs" class="net.sf.jasperreports.engine.JRDataSource"/>
	<query language="sql"><![CDATA[]]></query>
	<field name="title" class="java.lang.String"/>
	<field name="shiTeiDateGG" class="java.lang.String"/>
	<field name="shiTeiDateYY" class="java.lang.String"/>
	<field name="shiTeiDateMM" class="java.lang.String"/>
	<field name="shiTeiDateDD" class="java.lang.String"/>
	<field name="jigyoName" class="java.lang.String"/>
	<field name="yokaigoDo" class="java.lang.String"/>
	<field name="riyoushaNm" class="java.lang.String"/>
	<field name="keisho" class="java.lang.String"/>
	<field name="syoriYmGG" class="java.lang.String"/>
	<field name="syoriYmYY" class="java.lang.String"/>
	<field name="syoriYmMM" class="java.lang.String"/>
	<field name="createYmdGG" class="java.lang.String"/>
	<field name="createYmdYY" class="java.lang.String"/>
	<field name="createYmdMM" class="java.lang.String"/>
	<field name="createYmdDD" class="java.lang.String"/>
	<field name="shokuName" class="java.lang.String"/>
	<field name="cpnFlg" class="java.lang.Integer"/>
	<field name="iiSakuFlg" class="java.lang.Integer"/>
	<field name="lbSaku" class="java.lang.Integer"/>
	<field name="emptyFlg" class="java.lang.Boolean"/>
	<field name="prDate" class="java.lang.Integer"/>
	<field name="bunsyoKanriNo" class="java.lang.String"/>
	<field name="detailInfoList" class="net.sf.jasperreports.engine.JRDataSource"/>
	<field name="weekList" class="java.util.ArrayList"/>
	<field name="holidayFlgList" class="java.util.ArrayList"/>
	<field name="kkjTorikomi" class="java.lang.Integer"/>
	<field name="cksFlg" class="java.lang.Integer"/>
	<field name="lsFlg" class="java.lang.Boolean"/>
	<field name="isBlankOmitted" class="java.lang.Boolean"/>
	<field name="isSameOmitted" class="java.lang.Boolean"/>
	<field name="dayKinyuBikouKnjList" class="java.util.ArrayList"/>
	<field name="bikouKnj" class="java.lang.String"/>
	<field name="bikouFlg" class="java.lang.Integer"/>
	<field name="kinyuFlg" class="java.lang.Integer"/>
	<field name="bikouList" class="net.sf.jasperreports.engine.JRDataSource"/>
	<field name="dayFlg" class="java.lang.Integer"/>
	<field name="cbKrkRenkeiFlg" class="java.lang.Integer">
		<description><![CDATA[cbKrkRenkeiFlg]]></description>
	</field>
	<pageHeader height="52" splitType="Stretch">
		<element kind="textField" uuid="8c9eefa6-84c9-4256-b72f-345dea6c8f21" x="238" y="12" width="320" height="14" fontName="IPAexGothic" fontSize="14.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" bold="true" hTextAlign="Center">
			<expression><![CDATA[$F{title}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="bab816c5-581b-49a9-a3e9-f186d137623d" x="617" y="0" width="180" height="10" markup="html" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Right">
			<printWhenExpression><![CDATA[!$F{emptyFlg}||$F{prDate}.equals(1)]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDateGG}+
"<font style='font-size:9pt'>"+$F{shiTeiDateYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{shiTeiDateMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{shiTeiDateDD}+"</font>"+
"日"]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="f9948f1e-dbaf-4a58-8c0f-252384abc37e" x="570" y="11" width="227" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Right">
			<expression><![CDATA[$F{jigyoName}]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="frame" uuid="6538175c-396f-4f68-8523-0f3d07345909" x="0" y="26" width="180" height="11">
			<printWhenExpression><![CDATA[!$F{cpnFlg}.equals(5)]]></printWhenExpression>
			<element kind="staticText" uuid="3b4017bf-55a5-477c-b669-d6b3cfd581dd" x="0" y="0" width="50" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
				<paragraph firstLineIndent="2"/>
				<text><![CDATA[要介護度]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="textField" uuid="efb584a0-f94f-40de-ac2b-a2837ede2d2c" x="50" y="0" width="130" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center">
				<expression><![CDATA[$F{yokaigoDo}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
		</element>
		<element kind="frame" uuid="3bbbffef-6006-448f-867b-774a4b040715" x="0" y="37" width="205" height="11">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<element kind="staticText" uuid="20f70004-5fd8-4670-b9a3-3c371bb4042b" x="0" y="0" width="50" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
				<paragraph firstLineIndent="2"/>
				<text><![CDATA[利用者名]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
			</element>
			<element kind="textField" uuid="b79b7620-aef3-408e-8af7-9106537bc8d4" x="50" y="0" width="140" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center">
				<expression><![CDATA[$F{riyoushaNm}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="textField" uuid="8a689ebf-2083-40f7-b97e-fb7715570f20" x="190" y="0" width="15" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
				<expression><![CDATA[$F{keisho}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<box>
				<bottomPen lineWidth="1.0"/>
			</box>
		</element>
		<element kind="frame" uuid="5ff377e4-1485-4192-9c4f-06e4df2abc29" x="215" y="37" width="140" height="11">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<element kind="staticText" uuid="de79767c-9557-450d-8aac-12361712d31e" x="0" y="0" width="50" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
				<paragraph firstLineIndent="4"/>
				<text><![CDATA[処理年月]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
			</element>
			<element kind="textField" uuid="9737117a-ec7b-4eb7-a33f-18765c2389ca" x="50" y="0" width="90" height="11" markup="html" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true">
				<expression><![CDATA[$F{syoriYmGG}+
"<font style='font-size:10pt'>"+$F{syoriYmYY}+"</font>"+
"年"+
"<font style='font-size:10pt'>"+$F{syoriYmMM}+"</font>"+
"月"]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<box>
				<bottomPen lineWidth="1.0"/>
			</box>
		</element>
		<element kind="frame" uuid="eab82440-30fb-4496-b5f2-e4f69fe2e297" x="630" y="26" width="157" height="11">
			<printWhenExpression><![CDATA[!$F{cpnFlg}.equals(5)&&($F{iiSakuFlg}.equals(1)||$F{iiSakuFlg}.equals(2))]]></printWhenExpression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<element kind="staticText" uuid="3d0909b1-804c-44da-9615-e7611a9ce959" x="0" y="0" width="58" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right">
				<text><![CDATA[計画作成日]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="textField" uuid="e078196e-0e9c-4ea0-b619-c236044d08bf" x="67" y="0" width="90" height="11" markup="html" fontName="IPAexGothic" blankWhenNull="true">
				<expression><![CDATA[$F{createYmdGG}+
"<font style='font-size:10pt'>"+$F{createYmdYY}+"</font>"+
"年"+
"<font style='font-size:10pt'>"+$F{createYmdMM}+"</font>"+
"月"+
"<font style='font-size:10pt'>"+$F{createYmdDD}+"</font>"+
"日"]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
		</element>
		<element kind="frame" uuid="9af32f83-df49-424f-b2a7-36c707fae4ca" x="630" y="37" width="167" height="11">
			<printWhenExpression><![CDATA[$F{lbSaku}.equals(1)]]></printWhenExpression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<element kind="textField" uuid="5fb7169b-f94f-4538-ab8c-04c256f6650d" x="66" y="0" width="101" height="11" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true">
				<expression><![CDATA[$F{shokuName}]]></expression>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="textField" uuid="0d873750-8a7a-46e1-a9b1-0cc7be3f7d35" x="0" y="0" width="58" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Right">
				<expression><![CDATA[!$F{cpnFlg}.equals(5)?"計画作成者":"作成者"]]></expression>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
		</element>
		<element kind="textField" uuid="bbc59aaf-43a8-4bfd-8d2d-c19fc1ceb3f6" key="" x="615" y="-10" width="182" height="9" fontName="ＭＳ Ｐゴシック" fontSize="9.0">
			<printWhenExpression><![CDATA[$F{bunsyoKanriNo}==null?false:true]]></printWhenExpression>
			<expression><![CDATA[$F{bunsyoKanriNo}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="40" splitType="Stretch">
			<element kind="frame" uuid="39b75eca-6b95-4c39-925d-99c55615171d" positionType="Float" stretchType="ContainerHeight" x="0" y="0" width="785" height="40" printWhenDetailOverflows="true">
				<element kind="frame" uuid="aef11dd5-1204-4551-9caf-13bab321485b" positionType="Float" stretchType="ContainerBottom" mode="Transparent" x="0" y="26" width="785" height="14" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<element kind="frame" uuid="08a7e697-a3c5-48e2-962d-8ae0a4d64a22" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="0" y="0" width="24" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="08925eb4-c178-46dc-9a9d-8989e51ddb66" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="24" y="0" width="138" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="fa724462-9bf7-4422-b6d8-58685af71703" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="162" y="0" width="96" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="c9092d84-6180-424d-beb7-d3c02343976b" key="" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="258" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(0).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(0).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="ccf1e1f9-f12a-42f7-870c-ba87e91692d3" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="275" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(1).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(1).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="856a4c54-4825-4c3b-9a9d-026c8a3f640d" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="292" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(2).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(2).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="112fdf15-2787-48ec-90a6-cdfc8f289adb" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="309" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(3).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(3).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="2acd9028-d1b4-48cb-ab65-11a43e1cbe74" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="326" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(4).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(4).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="e7afe6e3-bcca-425c-ab84-1fe4d224e93f" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="343" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(5).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(5).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="c7b70bdd-499e-4e6c-bdd5-314d0f280ab3" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="360" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(6).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(6).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="bdf675b3-c161-4fe8-8643-647acd4dafab" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="377" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(7).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(7).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="408441c4-c9f9-4805-9090-50664153f4a0" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="394" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(8).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(8).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="5de3382e-9cf4-4118-af49-2814bdfb1622" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="411" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(9).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(9).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="7361bd69-a9d0-4c3d-8015-ccf2d7f9cf02" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="428" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(10).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(10).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="bd30fa0a-0684-4801-a02e-5d8a01a9d1cd" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="445" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(11).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(11).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="7a55d397-002b-4e2a-a394-4894bf58e812" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="462" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(12).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(12).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="12e79c94-22e6-4eb0-a0d5-78ce958c44ed" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="479" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(13).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(13).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="df9cc76a-8c1a-4fe8-b2f7-da6ff508dea1" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="496" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(14).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(14).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="33f0feeb-d235-47e6-aeb6-30fa3971b387" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="513" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(15).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(15).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="8ae5ba17-07c9-4ba5-8edf-3d419c0e18fa" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="530" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(16).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(16).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="3bc70500-30ee-4ef1-9a84-5ccc65d69556" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="547" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(17).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(17).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="7bfb2d12-571c-4d4f-8783-a843b6d15dc0" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="564" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(18).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(18).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="63f7c23a-526b-4dd8-baae-393534099c04" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="581" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(19).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(19).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="71920f75-571f-40a9-945b-b4bb0ed675f1" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="598" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(20).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(20).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="a4a54246-a04a-434d-a45b-bae06edcbc40" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="615" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(21).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(21).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="53769f2f-763c-47e2-82aa-409e5d4777d8" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="632" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(22).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(22).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="28022b75-5254-4977-bd9c-f680ada6848b" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="649" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(23).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(23).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="2fe711d8-addc-47c5-842d-4ff489520a51" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="666" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(24).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(24).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="ffa2d0df-dc51-4ef5-8607-07c6d536f8c1" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="683" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(25).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(25).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="9e2fcfdd-2a44-4166-b2a3-0a3c3e2474eb" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="700" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(26).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(26).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="44cedd7c-bfcc-4367-ac5a-0ea989bde8c1" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="717" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$F{lsFlg}?
$F{holidayFlgList}.get(27).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(27).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="d48d6d18-2535-4dba-b035-1d116beee210" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="734" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[!$F{dayFlg}.equals(1)?$F{lsFlg}?
$F{holidayFlgList}.get(28).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(28).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="9cd267ab-09d6-4a55-b087-9dbe217a2fcd" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="751" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[!($F{dayFlg}.equals(1)||$F{dayFlg}.equals(2))?$F{lsFlg}?
$F{holidayFlgList}.get(29).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(29).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="410c5d2e-a3ed-4e51-a063-81111e71c12e" positionType="Float" stretchType="ContainerHeight" mode="Opaque" x="768" y="0" width="17" height="14" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[!($F{dayFlg}.equals(1)||$F{dayFlg}.equals(2)||$F{dayFlg}.equals(3))?$F{lsFlg}?
$F{holidayFlgList}.get(30).equals(1)?"#FFE6E6":
$F{holidayFlgList}.get(30).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
				</element>
				<element kind="component" uuid="c31b0fbf-9d1a-4765-b699-62bdc4a7bb65" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="0" y="0" width="785" height="40" printInFirstWholeBand="true" printWhenDetailOverflows="true">
					<component kind="table">
						<datasetRun uuid="823b7cfc-42e5-4fcb-82c1-3239429781ed" subDataset="DatailInfoDataset">
							<dataSourceExpression><![CDATA[$F{detailInfoList}]]></dataSourceExpression>
							<parameter name="weekList">
								<expression><![CDATA[$F{weekList}]]></expression>
							</parameter>
							<parameter name="cpnFlg">
								<expression><![CDATA[$F{cpnFlg}]]></expression>
							</parameter>
							<parameter name="kkjTorikomi">
								<expression><![CDATA[$F{kkjTorikomi}]]></expression>
							</parameter>
							<parameter name="cksFlg">
								<expression><![CDATA[$F{cksFlg}]]></expression>
							</parameter>
							<parameter name="lsFlg">
								<expression><![CDATA[$F{lsFlg}]]></expression>
							</parameter>
							<parameter name="holidayFlgList">
								<expression><![CDATA[$F{holidayFlgList}]]></expression>
							</parameter>
							<parameter name="isBlankOmitted">
								<expression><![CDATA[$F{isBlankOmitted}]]></expression>
							</parameter>
							<parameter name="isSameOmitted">
								<expression><![CDATA[$F{isSameOmitted}]]></expression>
							</parameter>
							<parameter name="dayKinyuBikouKnjList">
								<expression><![CDATA[$F{dayKinyuBikouKnjList}]]></expression>
							</parameter>
							<parameter name="bikouKnj">
								<expression><![CDATA[$F{bikouKnj}]]></expression>
							</parameter>
							<parameter name="kinyuFlg">
								<expression><![CDATA[$F{kinyuFlg}]]></expression>
							</parameter>
							<parameter name="dayFlg">
								<expression><![CDATA[$F{dayFlg}]]></expression>
							</parameter>
							<parameter name="cbKrkRenkeiFlg">
								<expression><![CDATA[$F{cbKrkRenkeiFlg}]]></expression>
							</parameter>
						</datasetRun>
						<tableFooter splitType="Prevent">
							<printWhenExpression><![CDATA[$P{kinyuFlg} == 1]]></printWhenExpression>
						</tableFooter>
						<columnHeader splitType="Stretch"/>
						<columnFooter/>
						<detail splitType="Prevent"/>
						<column kind="group" uuid="d13306fe-be9b-4688-aacf-b7cf9929ac2e" width="258">
							<tableFooter height="100" rowSpan="1">
								<element kind="textField" uuid="30907461-ded2-439f-ac18-21b4ea0d8c4c" positionType="Float" mode="Opaque" x="0" y="0" width="258" height="100" fontName="IPAexMincho" fontSize="11.0">
									<expression><![CDATA[$P{bikouKnj}]]></expression>
									<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
									<box leftPadding="10"/>
								</element>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</tableFooter>
							<columnFooter height="1" rowSpan="1">
								<element kind="line" uuid="cfc3b6a6-2cff-4adc-a8ca-f323dec0861c" x="0" y="0" width="258" height="1"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
								</box>
							</columnFooter>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [3]"/>
							<column kind="group" uuid="1210cb7c-192b-416a-8952-a3d4cf1370da" width="258">
								<tableFooter height="50" rowSpan="1">
									<element kind="staticText" uuid="5bcfbd3e-f0bc-4303-9551-9c32f9da4295" positionType="Float" mode="Opaque" x="0" y="0" width="258" height="50" backcolor="#FFFFFF" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
										<text><![CDATA[記入者／備考]]></text>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
									</element>
									<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="0" rowSpan="1">
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [3]"/>
								<column kind="single" uuid="2c2d2db1-cb4a-4a87-b412-9b718b9de431" width="24">
									<columnHeader height="26" rowSpan="2">
										<element kind="textField" uuid="7939df42-787a-413f-ba8c-7d40e9b3cdf1" key="" x="0" y="0" width="24" height="26" fontName="IPAexGothic" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
											<expression><![CDATA[$P{cbKrkRenkeiFlg}.equals(1)?"ｻｰﾋﾞｽ\r\n番号":
																	$P{kkjTorikomi}.equals(1)?"課題\r\n番号":
																	$P{kkjTorikomi}.equals(2)?"ｻｰﾋﾞｽ\r\n番号":
																	"番号\r\n"
											]]></expression>
										</element>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</columnHeader>
									<detailCell height="14" style="TorikomiKnjBorderStyle">
										<element kind="textField" uuid="dd847039-8d19-420f-9d93-149f4939a8f4" x="0" y="0" width="24" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
											<expression><![CDATA[$F{bango}]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										</element>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
										<box style="TorikomiKnjBorderStyle">
											<leftPen lineWidth="2.0"/>
										</box>
									</detailCell>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
								</column>
								<column kind="single" uuid="ded20ed8-2934-4a5a-a86f-7a6ded2e5ee8" width="138">
									<columnHeader height="26" rowSpan="2">
										<element kind="textField" uuid="fcd01a8d-c076-4962-9839-09936c6216c3" x="0" y="0" width="138" height="26" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
											<expression><![CDATA[$P{cbKrkRenkeiFlg}.equals(1)?"サービス内容":
																	$P{kkjTorikomi}.equals(1)?"課題":
																	$P{kkjTorikomi}.equals(2)?"サービス内容":
																	$P{kkjTorikomi}.equals(3)?"長期目標":"短期目標"]]></expression>
											<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
										</element>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</columnHeader>
									<detailCell height="14" style="TorikomiKnjBorderStyle">
										<element kind="textField" uuid="411ea6f2-5e31-4d55-b315-48f3421c78a7" x="0" y="0" width="138" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Left" vTextAlign="Middle">
											<printWhenExpression><![CDATA[$V{COLUMN_COUNT} == 1||($P{isSameOmitted}&&!$V{ IsSame_TorikomiKnj}&&!($F{torikomiKnj}.equals("")||$F{torikomiKnj}==null))||!$P{isSameOmitted}]]></printWhenExpression>
											<expression><![CDATA[$F{torikomiKnj}]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<box topPadding="0" leftPadding="2" bottomPadding="0" rightPadding="0"/>
										</element>
										<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
									</detailCell>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
								</column>
								<column kind="single" uuid="d964fb7a-2c00-40b9-bb44-a29b6f38c1b2" width="96">
									<columnHeader height="26" rowSpan="2">
										<element kind="staticText" uuid="8504f4fc-114a-43c9-ab06-65730f88cf9e" x="0" y="0" width="96" height="26" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
											<text><![CDATA[頻度]]></text>
										</element>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</columnHeader>
									<detailCell height="14">
										<element kind="textField" uuid="82605002-4f45-4133-b2a9-d11b6e5d26bc" x="0" y="0" width="96" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Left" vTextAlign="Middle">
											<expression><![CDATA[$F{hindoKnj}]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<box leftPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</detailCell>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
								</column>
							</column>
						</column>
						<column kind="group" uuid="a2114ab4-067d-4862-984f-8f1d7805e8bf" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="2868a3bc-a022-4962-93a7-47c3e859602c" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[１]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(0).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(0).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<column kind="group" uuid="e2b20766-bb64-4ae6-8d52-481e0535449e" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="6e812939-7688-469d-8ad5-1f568e817ea6" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(0)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(0).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(0).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="a3350e4f-d737-425b-be8b-5ee1d833a5d0" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="8b9c380a-6037-496d-ad05-1c001f16fe27" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="9b4f9140-1b0d-48f2-b6d5-0f0b48cfae08" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(0)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(0).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(0).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="5c585011-a36e-47d6-bbb4-437037c41cfc" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="440914db-9a99-4db5-a69f-cd1573d3852b" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(0)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(0).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(0).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="86d1b1df-55d8-4121-b4b9-fdccd091bac1" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day1}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(0).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(0).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="1a7cc3b4-8615-48a7-9af8-23ba82bebd6e" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="a3f34019-5479-4daf-a0dc-58b83789a848" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[２]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(1).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(1).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="b172197c-08fe-4d0e-89cb-e2ae256a89eb" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="e4046fa2-9bee-471e-910d-f9e7a907fce4" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(1)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(1).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(1).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="8f271638-ba0e-4f44-a59e-66296aaee65d" x="0" y="0" width="17" height="1"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="65be5b57-bd35-4f86-b1a6-85063083ce82" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="fc62f59c-2e95-45fa-9162-796f4a9eea2e" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(1)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(1).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(1).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="77610dc7-71ca-4d7c-a869-c802f688ab09" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="5109f1ff-4aae-42c7-b495-e4c5e3f2d60f" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(1)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(1).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(1).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="b0c00a0e-8e71-4d4c-a030-2acce5f0ec88" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day2}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(1).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(1).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column5"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="c2e65cc9-907c-4ff7-9f42-84efe3daadea" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="e36cff36-c0a3-46c5-a3e1-a938051ac85c" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[３]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(2).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(2).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="9603bcf2-cd34-4ee2-9e44-15097b9d5c8b" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="efd50dda-e8a2-4d0e-94ed-05ec3c2444db" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(2)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(2).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(2).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="1d19cad3-a681-441a-ad0a-16db26ba3f90" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="069b27ce-1d8c-4505-80b0-d98872d5e033" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="1c99f92b-b47c-4f96-bde1-2cfcc3b4eeba" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(2)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(2).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(2).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="c55a9060-c102-4154-957a-569698bd5cee" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="6fec8ba1-7f41-4981-b3e2-d38e60141a94" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(2)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(2).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(2).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="7ed5b772-a52b-4fd5-adaf-a364a1a12454" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day3}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(2).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(2).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column6"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="f404a2e5-9e7d-47d5-80cd-c4fc743a4902" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="65e67d49-85f1-495c-8512-af408ed1054a" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[４]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(3).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(3).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="2c16601b-3379-4072-a7c6-a6c95b13fea3" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="6cbfc6f9-600c-4c56-97b0-fe1a9a8eeb10" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(3)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(3).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(3).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="eed1aa48-f983-4740-ad73-f49c0816ec20" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="d39de453-a794-428a-89b0-828242ace531" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="510e0e4d-209d-4eb6-8294-5c457e9a275c" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(3)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(3).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(3).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="3f5cb0eb-27fb-44f9-822c-c93f4853fa75" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="cd4b689e-deee-4691-9038-e49f7eab49be" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(3)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(3).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(3).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="21427d34-b5f8-4144-a466-bdd926f66525" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day4}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(3).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(3).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column7"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="9c1ce7ca-c1f2-42ce-bdb6-095293011374" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="c4b1e1ce-fd76-4400-87a9-af64a62cb066" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[５]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(4).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(4).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="95f5dae8-0863-4028-ab7c-3834f07436dc" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="d7f3f680-0b85-483d-9127-e6d4145629a2" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(4)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(4).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(4).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="889b2770-c4c4-4b22-9ee8-c9b029bf886c" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="f844aaa3-d88a-47e0-bd57-78151e0f2c54" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="0d2ec99c-b2d9-41ab-a8ad-7dae14275ccf" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(4)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(4).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(4).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="7492dcfa-d2f3-4c39-8a9d-26dacee0a2a6" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="da0635b3-ad80-4d8b-9b48-8e3a29b05299" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(4)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(4).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(4).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="eadf7ac7-fb15-42ad-ba70-e0bc8256ef52" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day5}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(4).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(4).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column8"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="aaa17e02-5099-4600-896f-d501e895bc80" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="00600169-c1e0-4ceb-a226-73f67313b1dc" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[６]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(5).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(5).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="bffbe3c9-78fd-4617-992b-c909d2790bdf" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="38ac3987-b6ee-405a-aff3-158addcca96e" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(5)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(5).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(5).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="5860f1dd-0403-4953-9b6f-a7b3ae7c87de" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="cd95d156-25b2-4011-9d66-1fce58a6f8ba" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="123a6a2e-86f6-44ea-98ae-8c5e19a1f3c3" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(5)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(5).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(5).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="6a90830a-12af-436f-83b0-ba8582a81a16" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="06b01589-76b7-4018-88e7-17b3719a5a48" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(5)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(5).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(5).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="55d4c570-75a9-408b-965c-9148674f22e9" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day6}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(5).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(5).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column9"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="6537da54-da8f-4c09-8946-6942e494cc6c" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="2bd19e17-1db7-4d56-a9bc-e687c64d0e33" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[７]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(6).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(6).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="234f857e-10b8-4b97-b092-cd43ccced445" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="8ef4e402-10e5-435c-807b-870e039aec97" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(6)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(6).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(6).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="453081ea-63ad-45cd-8326-6e4c7d7d5e5a" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="84cee15a-822d-4557-9650-960f34e1856e" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="9499bb25-429b-4a55-9835-240e6aaef488" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(6)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(6).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(6).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="48e461bf-9c43-4337-9f37-a496ca21a51d" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="0db4ddf9-0c21-46db-b612-a451c125b0d8" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(6)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(6).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(6).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="3e3935f9-2de9-4a79-bf6a-b9969b1541e1" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day7}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(6).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(6).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column10"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="592be9a2-b9dd-4f69-af5f-d87499eefcd2" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="73a0648f-8afd-4c93-bcf4-b19f9efa1838" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[８]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(7).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(7).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="1aa7ab6a-3633-4c73-9cb4-8b7b1bc81982" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="059ed6ef-357e-48f3-ad41-04e0057e7f5c" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(7)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(7).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(7).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="f4bfa289-616c-4551-ada8-36fd67e96001" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="b2f1b4b6-a6e7-400d-8bc9-40fae0e6c529" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="*************-47ef-a091-5aae0827097d" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(7)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(7).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(7).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="736a8f2a-c156-4612-bca4-ffcb7d7c4859" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="f6875a9a-483c-42db-b2d6-15ec37cf38c0" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(7)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(7).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(7).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="2032adf2-43af-475e-b70f-0144f754e13d" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day8}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(7).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(7).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column11"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="47bedc65-a59d-4a92-afb5-c7f13dd94859" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="07ca8ad3-debf-4d19-a233-cd6df1300fe7" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[９]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(8).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(8).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="6b2c71cd-5977-4820-a762-3019bf027b77" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="aabd56c7-82b9-4b89-8a28-5ac6cfbf0129" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(8)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(8).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(8).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="f06a7efe-ee07-4625-9995-fbe208c88cbb" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="ee95fd0a-d9f2-4175-9870-83edf7ce2a3d" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="0b3084f6-3e01-4846-9f84-d6f32a79c025" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(8)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(8).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(8).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="fa4972c9-153a-48e2-857c-bc0f022e4715" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="b81cb4e3-cfbe-4dae-9f20-9b9d3ee87c5a" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(8)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(8).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(8).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="b9e5ff5d-49ad-4af7-96ca-0909c9db1932" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day9}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(8).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(8).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column12"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="fda1c3a4-55e2-4ae9-a5ac-d923c6844311" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="f9a9ca89-8d16-41d0-bd92-5fea0280d49d" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[10]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(9).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(9).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="83ebab6d-ba41-4f67-99ab-92d3b7e7220e" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="a7bf7f47-4239-40c1-ba86-d0040389d8ce" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(9)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(9).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(9).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="0389a0cf-a887-4626-916f-f60a803f05cb" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="e5247a1b-60de-44de-a20e-a761e281eb25" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="6fce5714-807e-4d58-924d-f153f654574e" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(9)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(9).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(9).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="45e3f0a7-b4f7-4752-bff5-e621f3849849" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="31530d17-e1e7-4d78-9c44-9b08d0541074" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(9)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(9).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(9).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="da2d29e9-df1f-43d0-8294-599573153a8e" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day10}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(9).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(9).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column13"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="ad5f385b-3b12-4acc-9b96-6d33cc38a7b2" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="029133b2-5f0a-49a0-98b7-ab29e37617ea" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[11]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(10).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(10).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="b74e4b4e-c133-461a-9c30-91a2ccbffc81" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="f87105aa-b5c6-4ec0-8d49-d4cef54ec8d8" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(10)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(10).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(10).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="88848ddd-a567-4ca7-b730-9aabd9a40985" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="a199e3db-73ff-456d-b5b8-d1ced4336706" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="bd6a6ba9-76ad-4b85-80b2-79a0b41c0ecd" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(10)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(10).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(10).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="56ae73b7-7fe1-4d72-9823-b263ed28cf6a" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="e7c4d5ec-881c-4dbe-9e5e-2f887f041477" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(10)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(10).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(10).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="2a8013c0-0684-4393-8528-a2ba3e680f7d" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day11}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(10).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(10).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column14"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="23bef68a-2470-43c0-820a-661258ee88a6" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="e99c4e0a-afad-4885-9039-cdb32b11f0cf" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[12]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(11).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(11).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="3893752d-9ec4-4373-a5ed-83542a2c098b" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="7fb6fda7-feeb-4f1d-8afd-************" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(11)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(11).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(11).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="ff9e8d07-b5be-4226-a9cd-724020c1b9c4" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="7070c35e-bd1e-4124-a1d2-d6cb5293ea53" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="47aba7b1-67b8-4332-a197-d8c186b78249" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(11)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(11).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(11).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="e10127fe-82c2-4bea-9cef-57592b16c786" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="f8259e93-5cf7-4596-93fa-72852226521b" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(11)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(11).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(11).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="19c63526-2eea-4297-96dc-4f6b6fcbc505" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day12}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(11).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(11).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column15"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="4ede0079-7ed2-46f5-b9a0-55db57047735" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="a12bba3b-52cf-4669-b0ef-402a63efe0ad" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[13]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(12).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(12).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="be8a97ae-6534-4f2c-8580-c58d21114fa2" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="a52f9197-211c-459f-961a-59eb446b294d" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(12)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(12).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(12).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="575a1391-9ae3-4416-8a8e-a8ac24954301" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="b63b6768-3186-49d1-bb5e-67f4583dcfae" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="01a12b74-856b-4aa2-8aed-6a2ac43a365a" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(12)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(12).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(12).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="ff770f6b-263d-4e09-a1d9-5152737d8c67" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="81b2d047-a159-4f26-878c-a2660346a749" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(12)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(12).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(12).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="d25a7881-e0b2-47e8-8fe4-52b383445d5a" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day13}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(12).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(12).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column16"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="b29d76f0-6484-4f90-b606-19b6448572f6" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="d4234d04-da61-438d-8d44-3e312e2c3fdc" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[14]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(13).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(13).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="a5f30ff3-46b0-430d-8d97-cc592612f365" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="92d0b3f9-a211-4232-b017-d13fd36c42c8" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(13)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(13).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(13).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="0849d095-b59d-4da6-842d-57ce83009c3d" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="010ae70a-34ed-4aaa-89a9-acf841c58660" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="f233faa8-d851-4346-b8c7-8352b6b78873" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(13)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(13).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(13).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="2d3791a0-0887-4b1b-8b96-4929da189bd6" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="54b7fff7-e379-4d70-b220-1de35fe8a0be" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(13)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(13).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(13).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="698c5090-e328-41a7-b664-45b9c1852154" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day14}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(13).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(13).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column17"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="611c8e01-6f00-4883-854f-3c7b11219e14" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="*************-45d0-872a-561a6ed81349" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[15]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(14).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(14).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="3c9cbd04-e4c7-46fb-a32c-2da43a3504c5" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="c8cc501b-baf8-4909-babc-7ef869ecbec7" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(14)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(14).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(14).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="a4c37b11-6459-49d7-bd17-d178d7c94b20" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="6febc42a-8f52-4c15-84a2-c33bf4252b94" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="dae68821-496b-44a0-ba73-387366e755b8" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(14)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(14).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(14).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="cbca1377-b3a1-49cb-afd0-06959388ff07" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="11fe5512-9a83-4eb6-9a05-94d315e4e795" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(14)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(14).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(14).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="a3f80376-7ace-4c14-982b-2e7cc87c118e" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day15}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(14).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(14).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column18"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="4dfd4555-05be-4e40-8e90-b5a9db3116e9" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="c7ab7442-fd32-4c4a-ae3d-bb803388d7b3" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[16]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(15).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(15).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="8e737117-0a0f-458a-8769-efa161068b59" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="fe325edd-cfe6-431d-b413-9cb0fc0202b1" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(15)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(15).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(15).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="131ce3aa-2ce9-4e87-85c7-ce517870e153" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="6a53867a-5b91-44da-afb4-39ccc3827186" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="be8d02e9-7da2-43ea-8849-ba2e4ab2e7d5" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(15)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(15).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(15).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="49bb3c1b-1ebe-4ee5-9111-4181a2064e93" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="3f110f70-b11e-4f4a-accb-020e1138ec04" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(15)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(15).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(15).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="6eab531e-3647-4d9d-bbb4-ff877d12f69b" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day16}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(15).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(15).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column19"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="6595a3a4-1ccf-4177-8c82-848f0e3d21ce" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="18f9801d-c1dc-4504-b858-fc00dd735bf6" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[17]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(16).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(16).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="c08409ca-181a-40f8-87e0-25678340dec2" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="5aacc09b-2ce0-4ad0-b880-f8bb76068ff1" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(16)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(16).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(16).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="61aa06fc-2549-40f2-aaf8-332ee88313a6" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="8327bf6a-f0a2-4c44-85b6-22a90a7de8a8" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="cb52fceb-0d2d-4e0c-889e-610310ad18fe" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(16)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(16).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(16).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="9fa3f904-3393-4058-a064-8aeaa60047cb" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="09fd46b9-2b7a-4fce-b9cd-d94472601c93" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(16)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(16).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(16).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="f26a67d1-6aac-4b08-aafc-19da1ad059b9" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day17}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(16).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(16).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column20"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="10293631-e330-47e4-96af-9fab4f545243" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="2ac96954-136f-4995-8650-bfab5b955e8e" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[18]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(17).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(17).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="1028779d-705c-41d6-88f2-464f385c6647" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="685d13ab-2b2c-46c6-9b12-855e005add8f" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(17)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(17).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(17).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="86cc33e8-2195-486a-bcb1-b98049f6a8bb" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="648fc7f1-b3a4-4824-92b5-082e9ec21068" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="0de72ccb-9fa1-4f8b-9c15-7b51f3d179f4" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(17)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(17).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(17).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="de24f654-79bd-46a4-8dbc-6c3d5bf1cdfc" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="12ee83ed-0a42-4e4b-b49b-0bfa7a40df3d" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(17)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(17).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(17).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="6fbc2bc9-ed7d-4b56-993c-0e2d2ec5b62f" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day18}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(17).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(17).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column21"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="3ac4263f-4ef6-4028-ad0a-933c95ec7083" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="a5eb9afe-6e67-4d54-ae5b-d24dc1b3729f" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[19]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(18).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(18).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="6c8f2bd8-cd5c-42ba-a425-4bc5aa936043" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="67a4a10b-8459-42f9-ba8b-4bbb44d30ab6" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(18)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(18).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(18).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="b44f6382-2912-4209-a8b7-dcb65b26cf36" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="a7f6f26d-8171-4afa-b275-6d5cb0064b3f" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="80da52cd-2721-4e9d-a72b-6968e40ecf2a" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(18)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(18).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(18).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="f620cf87-9556-4b11-899e-b4df99aec0b6" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="0c00dccf-08ce-4b7a-a97d-bf62919c9fad" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(18)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(18).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(18).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="d7834cd4-b2bb-4440-919f-715396243de2" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day19}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(18).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(18).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column22"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="b1e0a88b-847e-4453-a63d-05df45c1eb4b" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="7250e656-4207-4445-86e9-39b420fcd619" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[20]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(19).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(19).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="c49def74-424a-4cdb-95d6-3554d65c7b9d" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="8aec2340-2f49-4c38-9f18-e8437cf5351e" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(19)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(19).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(19).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="2323283e-3eec-4008-b454-1d85664e2352" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="d45bcd92-6620-45e0-8057-b7e103cd43d2" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="b1fd6820-3a99-43fc-9ab0-d607bffe868f" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(19)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(19).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(19).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="abbe5874-9797-47bf-b734-af3e495c0a75" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="8996502a-9f8d-430d-b049-747f4f8650a8" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(19)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(19).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(19).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="89a18302-5357-42ce-bd76-************" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day20}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(19).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(19).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column23"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="69677dfb-9e37-4272-ab1e-2a5b91d70f09" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="0e529c90-78a9-4a1a-a733-3c0c1d7f1e4b" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[21]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(20).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(20).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="34e114f4-18b1-494d-95de-477738ab9439" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="9900a411-189e-4ad9-93ec-eee498054cd3" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(20)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(20).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(20).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="5bd9e1f0-d2ee-469a-b53b-24775554cf9b" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="53ef3f5a-6c7e-4a34-9044-f3f933ce5f7b" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="66dee075-2199-4028-984b-b33a3bad682a" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(20)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(20).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(20).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="a80ed87c-15ff-40c2-b259-************" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="2cdecdb8-c846-4acd-bf10-8be4ac365b77" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(20)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(20).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(20).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="b3f5faa3-b0a8-45b8-82fb-273a2abd81d5" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day21}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(20).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(20).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column24"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="0a0f0c6c-2b5d-4b04-9e29-9d10effd6d35" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="c071b2b2-0976-4593-92c6-38aa3e8d2c1b" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[22]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(21).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(21).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="ee9aec91-810b-49f4-9fca-68dec871dfec" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="c61826e5-df66-47e1-912e-99930093a80c" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(21)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(21).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(21).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="31f5e00b-52c0-4811-a18c-1e1b37bebdea" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="cc503dda-f407-440e-bf7e-4e5ce7089c96" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="f4f75745-856e-46cc-b722-50cbbd11184f" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(21)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(21).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(21).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="f2ff7ad2-1520-4f0c-a0ac-fdb5df62d72c" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="91b65300-890a-4166-bc0a-7208c658c507" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(21)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(21).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(21).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="7c05a3ff-335d-494c-bc31-2ad2b29b0cae" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day22}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(21).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(21).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column25"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="134f3c8e-9481-4171-a2b8-772d39d1dbee" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="7405c881-b808-42fb-99b4-be87ef7212a3" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[23]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(22).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(22).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="c883a3f2-d50c-4522-b607-539bddb9aba0" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="0d87722b-6bf8-460e-81bf-e175baa8489c" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(22)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(22).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(22).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="a17ac3c8-7b85-45ed-8dc8-aa6d0f91200a" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="17e4b447-8be3-4fd2-9fcc-ef3d901f42e3" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="3df94547-bca5-46ea-a6d8-5629e61b54b1" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(22)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(22).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(22).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="2b7f0702-bbee-4149-ab41-e27eaef5a8b4" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="404f71c9-f521-4302-b978-93050e94bb87" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(22)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(22).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(22).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="1c45f1cb-9714-4917-a46f-bbbfecdac2d7" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day23}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(22).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(22).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column26"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="66257c5a-7500-4481-b9f1-38fd18577e20" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="d61c0bfc-4ae2-4b10-88ae-50dee2a8a3e3" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[24]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(23).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(23).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="7f925818-4f7b-4d1a-b4cb-1e8ec5326d0e" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="f4919119-4f45-421d-a5eb-73d6da8dde13" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(23)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(23).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(23).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="0f06cf21-e2ba-48ab-a133-243c9f6e104e" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="0d85bc1f-3745-4c6d-aa7e-0236d5d99d3c" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="be143b81-a122-4965-8282-6863bd71b22d" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(23)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(23).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(23).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="f271acbf-3a67-4182-ba1d-2c715ede8e32" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="f3056c40-a380-45cb-921e-72a447f751f2" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(23)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(23).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(23).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="ad4d40fa-bb63-4c34-97c3-bea42968fa5c" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day24}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(23).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(23).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column27"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="d6ce5c12-b37f-4d3f-b7fb-02758c2b91c7" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="8f5150a6-e277-48cb-8f82-2fb159ab79d3" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[25]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(24).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(24).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="a8a89786-449e-4171-bad1-df32e672269a" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="02ad01c2-4b25-4254-9d21-644912a6c346" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(24)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(24).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(24).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="9f811027-93da-4386-9808-13a41e883fd7" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="a04b8f72-00a2-4c93-b280-05642bfb5a00" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="6efbce61-6ead-45d4-bb6b-1cd998640b97" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(24)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(24).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(24).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="0d09ea21-4394-4d9e-a55e-4fa467dcfbd9" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="fd0e3ef4-5443-495f-a9ce-9c55d7d6262d" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(24)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(24).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(24).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="35b26c77-d08a-427b-9701-0875e5cbefc7" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day25}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(24).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(24).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column28"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="ceba7cb3-7c33-4727-b519-7a763b2f261d" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="db7d334e-0405-4512-9997-45215e7faba5" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[26]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(25).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(25).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="26b1834e-e1b6-4983-87c3-569902c0c0b6" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="2e354f28-5b49-4545-ad33-9bdf9f4c3157" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(25)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(25).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(25).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="c09058c3-47be-4405-a9a3-c911e34623d3" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="40b3c04f-444a-4999-8559-1d8d9f3a921d" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="2c9568fd-58ca-4c97-b156-cec2ac979bfa" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(25)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(25).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(25).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="176bf2d2-eb2c-45f0-90a6-11a927733297" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="bfbb2bc4-5a7d-4c2f-b7fb-d6892e98c3f1" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(25)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(25).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(25).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="bb3f0625-ed6d-4010-b7bd-39286e5379d5" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day26}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(25).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(25).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column29"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="fe233e10-aa16-4926-aa6b-68965e8d2c2f" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="1d6c829c-5d38-4197-9816-8d3869f58dd3" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[27]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(26).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(26).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="d3ff783d-50fd-49fe-8ccc-87053898e91e" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="40828df7-276c-45a8-a464-b3dbc1683476" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(26)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(26).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(26).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="7b822e3b-**************-20a98f53d579" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="0dd75770-0b63-4f5d-9f3e-a3699f0242e0" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="3c68c56e-4f52-40a3-94aa-dce23cde195d" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(26)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(26).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(26).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="d9a71dc1-861a-4258-8020-b206b7d08f3c" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="428f9f61-a2ce-4f2c-9558-8b9c28737b3f" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(26)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(26).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(26).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="ecfd0c6e-f9b6-4f8d-8a00-a92c2b80b66d" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day27}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(26).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(26).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column30"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="45e35cfc-6e0a-47b5-9144-d7269ad628e9" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="47961b0a-1435-4a08-9d04-b22c09896e78" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[28]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(27).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(27).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="e68af92e-5974-47df-9715-cea547184ab1" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="f2a99e56-236b-4f05-b6b1-93bf900e301c" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(27)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(27).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(27).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="6e22532a-2dc1-4770-9161-75a4ff1dac2e" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="1a105c34-82cb-4431-8623-2be4b86dc994" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="textField" uuid="664f9c3e-8f29-47ed-8807-6ef5dfe89e56" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
											<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(27)).getKey()]]></expression>
											<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
											<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(27).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(27).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
											<box topPadding="2"/>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="649897c9-309e-4640-bd1a-d65c0b740c9f" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="4eb6cd42-8b85-4393-b04a-53506085f438" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<expression><![CDATA[$P{weekList}.get(27)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(27).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(27).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="327063bc-b586-4a40-9e2f-ad6629fcedb5" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<expression><![CDATA[$F{day28}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(27).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(27).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column31"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="9b5ee481-57c0-4931-93a6-2c42418c2bd6" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="755c4c84-4767-463a-9a34-6ad4fbd40479" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[!$P{dayFlg}.equals(1)]]></printWhenExpression>
									<text><![CDATA[29]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(28).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(28).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="675db172-2e34-4b5d-8bed-7f7ffec1770f" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="bd3de1c1-860a-4a67-bd52-82f0b4944e8f" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<printWhenExpression><![CDATA[!$P{dayFlg}.equals(1)]]></printWhenExpression>
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(28)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(28).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(28).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="fbad68c5-993d-4c99-b6ab-f23f15dbb316" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="27ff6814-3067-4719-b910-d7da3aae69b0" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="frame" uuid="94de737a-f412-4499-a978-5629c0b8bb19" mode="Opaque" x="0" y="0" width="17" height="50">
											<element kind="textField" uuid="c99eec4b-8de6-41f2-8f61-0671c84e0b18" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
												<printWhenExpression><![CDATA[!$P{dayFlg}.equals(1)]]></printWhenExpression>
												<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(28)).getKey()]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(28).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(28).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="e1039161-1486-42df-8d25-12a0c025133a" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="e2aabdff-046a-439f-a268-6dc9b91b9112" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<printWhenExpression><![CDATA[!$P{dayFlg}.equals(1)]]></printWhenExpression>
												<expression><![CDATA[$P{weekList}.get(28)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(28).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(28).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="479a7648-d8b7-4971-9627-337eea5dd544" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<printWhenExpression><![CDATA[!$P{dayFlg}.equals(1)]]></printWhenExpression>
												<expression><![CDATA[$F{day29}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(28).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(28).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column32"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="75239539-5b92-4ec4-b7d5-c37a3dd0667c" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="5f294fd6-a80b-4f34-83e3-3522465d718c" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[!($P{dayFlg}.equals(1)||$P{dayFlg}.equals(2))]]></printWhenExpression>
									<text><![CDATA[30]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(29).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(29).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="ba22b96f-51ab-4a66-8494-d88dded5cceb" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="6b21451b-cb7b-4fa0-844f-c16eefc626e5" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<printWhenExpression><![CDATA[!($P{dayFlg}.equals(1)||$P{dayFlg}.equals(2))]]></printWhenExpression>
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(29)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(29).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(29).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="a39dc21b-78a1-4ed1-a35e-72c5f1f0646a" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="af397f58-7060-4938-b027-505d74954a16" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="frame" uuid="4ff64e27-077a-4338-b8a3-7e78463428c2" mode="Opaque" x="0" y="0" width="17" height="50">
											<element kind="textField" uuid="30ca28e1-**************-07f66f821b15" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
												<printWhenExpression><![CDATA[!($P{dayFlg}.equals(1)||$P{dayFlg}.equals(2))]]></printWhenExpression>
												<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(29)).getKey()]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(29).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(29).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="c0d73dd7-1d49-4c33-9688-d08f7335049e" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="76eea77e-a97d-4fef-9385-a43a42e5683c" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<printWhenExpression><![CDATA[!($P{dayFlg}.equals(1)||$P{dayFlg}.equals(2))]]></printWhenExpression>
												<expression><![CDATA[$P{weekList}.get(29)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(29).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(29).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="6573336a-efca-45d9-8e34-7e9a35d9228c" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<printWhenExpression><![CDATA[!($P{dayFlg}.equals(1)||$P{dayFlg}.equals(2))]]></printWhenExpression>
												<expression><![CDATA[$F{day30}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(29).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(29).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column33"/>
									</column>
								</column>
							</column>
						</column>
						<column kind="group" uuid="6e954a6a-daa4-4623-bae5-abb19bf9c128" width="17">
							<columnHeader height="13" rowSpan="1">
								<element kind="staticText" uuid="b96a6e11-2887-4d22-b75a-72c81aca0a0f" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[!($P{dayFlg}.equals(1)||$P{dayFlg}.equals(2)||$P{dayFlg}.equals(3))]]></printWhenExpression>
									<text><![CDATA[31]]></text>
									<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(30).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(30).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
							<column kind="group" uuid="001c5f46-ce2e-4296-a780-3f9b8c6b62ad" width="17">
								<tableFooter height="100" rowSpan="1">
									<element kind="textField" uuid="c96f9136-c594-4544-abd7-9a521b32692c" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="100" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
										<printWhenExpression><![CDATA[!($P{dayFlg}.equals(1)||$P{dayFlg}.equals(2)||$P{dayFlg}.equals(3))]]></printWhenExpression>
										<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(30)).getValue()]]></expression>
										<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
										<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(30).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(30).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
										<box topPadding="2"/>
									</element>
									<box>
										<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</tableFooter>
								<columnFooter height="1" rowSpan="1">
									<element kind="line" uuid="d09f8057-4a76-4a3d-b30f-7a63827022b7" x="0" y="0" width="17" height="1"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</columnFooter>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<column kind="group" uuid="c8e67c3d-58a9-4e57-8b92-c665e6bdd63a" width="17">
									<tableFooter height="50" rowSpan="1">
										<element kind="frame" uuid="ff304aed-44a3-41e5-a1bc-83a98ebcb34e" mode="Opaque" x="0" y="0" width="17" height="50">
											<element kind="textField" uuid="bdb89cfe-e599-4929-bdb3-732e492436e1" positionType="Float" mode="Opaque" x="0" y="0" width="17" height="50" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center">
												<printWhenExpression><![CDATA[!($P{dayFlg}.equals(1)||$P{dayFlg}.equals(2)||$P{dayFlg}.equals(3))]]></printWhenExpression>
												<expression><![CDATA[((Map.Entry<String, String>)$P{dayKinyuBikouKnjList}.get(30)).getKey()]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(30).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(30).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
										</element>
										<box>
											<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</tableFooter>
									<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [1]"/>
									<column kind="single" uuid="a8eb8154-ede3-4d1b-a79b-dc78f20b9809" width="17">
										<columnHeader height="13" rowSpan="1">
											<element kind="textField" uuid="b8fcfdba-87cf-4f9f-8c83-8c621979a0ba" x="0" y="0" width="17" height="13" fontName="IPAexGothic" fontSize="11.0" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
												<printWhenExpression><![CDATA[!($P{dayFlg}.equals(1)||$P{dayFlg}.equals(2)||$P{dayFlg}.equals(3))]]></printWhenExpression>
												<expression><![CDATA[$P{weekList}.get(30)]]></expression>
												<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$P{lsFlg}?$P{holidayFlgList}.get(30).equals(1)?"#FF0A0A":
$P{holidayFlgList}.get(30).equals(2)?"#0C0CFE":"#000000":"#000000"]]></propertyExpression>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</columnHeader>
										<detailCell height="14">
											<element kind="textField" uuid="3723b8cd-bcc7-457b-b4a2-fdbe7488b0ad" stretchType="ContainerHeight" mode="Opaque" x="0" y="0" width="17" height="14" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
												<printWhenExpression><![CDATA[!($P{dayFlg}.equals(1)||$P{dayFlg}.equals(2)||$P{dayFlg}.equals(3))]]></printWhenExpression>
												<expression><![CDATA[$F{day31}]]></expression>
												<property name="net.sf.jasperreports.style.isBlankWhenNull" value="true"/>
												<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{lsFlg}?
$P{holidayFlgList}.get(30).equals(1)?"#FFE6E6":
$P{holidayFlgList}.get(30).equals(2)?"#E6E6FF":"#FFFFFF":"#FFFFFF"]]></propertyExpression>
												<box topPadding="2"/>
											</element>
											<box>
												<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
											</box>
										</detailCell>
										<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column34"/>
									</column>
								</column>
							</column>
						</column>
					</component>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header"/>
					<property name="com.jaspersoft.studio.table.style.column_header"/>
					<property name="com.jaspersoft.studio.table.style.detail"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="37">
			<printWhenExpression><![CDATA[$F{bikouFlg}==1]]></printWhenExpression>
			<element kind="component" uuid="c18aecb8-19a2-4bdd-bf03-7984bf92b088" x="0" y="0" width="785" height="37">
				<component kind="table">
					<datasetRun uuid="a880d1df-0b4f-4a28-b89a-19568f01f620" subDataset="BikouDataset">
						<dataSourceExpression><![CDATA[$F{bikouList}]]></dataSourceExpression>
					</datasetRun>
					<column kind="single" uuid="69e1a931-44d5-476a-b422-92840101e427" width="785">
						<columnHeader height="20">
							<element kind="staticText" uuid="6d11df1f-b7f4-4bb2-b205-ec447eeda220" x="0" y="0" width="785" height="20" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[今　月　の　総　括]]></text>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</columnHeader>
						<detailCell height="17">
							<element kind="textField" uuid="4c559446-6789-4724-8cb0-e1506ce6f13e" x="0" y="0" width="785" height="17" fontName="IPAexMincho" fontSize="11.0" textAdjust="StretchHeight" vTextAlign="Middle">
								<expression><![CDATA[$F{bikouKnj}]]></expression>
								<box topPadding="2" leftPadding="5" bottomPadding="2">
									<topPen lineWidth="2.0"/>
									<leftPen lineWidth="2.0"/>
									<bottomPen lineWidth="2.0"/>
									<rightPen lineWidth="2.0"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<topPen lineWidth="2.0"/>
								<leftPen lineWidth="2.0"/>
								<bottomPen lineWidth="2.0"/>
								<rightPen lineWidth="2.0"/>
							</box>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
				<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
				<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="40">
			<element kind="subreport" uuid="9ff7bfc6-15d3-43af-973d-32b3ed20bdbe" key="com_report" x="30" y="10" width="670" height="30">
				<dataSourceExpression><![CDATA[
    new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(
        ((net.sf.jasperreports.engine.data.JRBeanCollectionDataSource)$P{subReportDataDs}).getData()
    )
        ]]></dataSourceExpression>
				<expression><![CDATA[$P{subReportPath}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="20">
		<element kind="frame" uuid="d0f8a4ad-7a50-4747-a1f6-0709984f3a3a" x="298" y="0" width="200" height="20">
			<element kind="textField" uuid="b41edcf3-26c9-4950-9b0f-3ba9dc320565" x="0" y="5" width="100" height="15" fontName="IPAexGothic" fontSize="8.0" hTextAlign="Right">
				<expression><![CDATA[ $V{PAGE_NUMBER}]]></expression>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
			</element>
			<element kind="textField" uuid="efaf19bd-963e-4ba7-94fe-1032e73ac617" x="105" y="5" width="95" height="15" fontName="IPAexGothic" fontSize="8.0" evaluationTime="Report" hTextAlign="Left">
				<expression><![CDATA[$V{PAGE_NUMBER}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<element kind="staticText" uuid="f72e373b-e517-4a4c-86af-a02ac903d8c6" x="100" y="5" width="5" height="9" fontName="IPAexGothic" fontSize="9.0">
				<text><![CDATA[/]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
