<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="アセスメント表（N）" language="java" columnCount="1" pageWidth="595" pageHeight="842" orientation="Landscape" columnWidth="550" leftMargin="44" rightMargin="1" topMargin="28" bottomMargin="20" uuid="95b152d9-156d-4073-8f78-d9eb987ca733" titleNewPage="true">
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="477"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="523"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="Table_TH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 4_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 4_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 4_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CG" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CT" mode="Opaque" backcolor="#005FB3">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="tanntoFont">
		<conditionalStyle pattern="" fontName="IPAexGothic" fontSize="11.0">
			<conditionExpression><![CDATA[$F{tantoFont}.equals("2")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle markup="none" fontName="IPAexGothic" fontSize="12.0">
			<conditionExpression><![CDATA[$F{tantoFont}.equals("1")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle fontName="IPAexGothic" fontSize="10.0">
			<conditionExpression><![CDATA[$F{tantoFont}.equals("3")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle fontName="IPAexGothic" fontSize="9.0">
			<conditionExpression><![CDATA[$F{tantoFont}.equals("4")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle fontName="IPAexGothic" fontSize="7.0">
			<conditionExpression><![CDATA[$F{tantoFont}.equals("5")]]></conditionExpression>
		</conditionalStyle>
	</style>
	<style name="riyoushaFont">
		<conditionalStyle pattern="" fontName="IPAexGothic" fontSize="11.0">
			<conditionExpression><![CDATA[$F{riyoushaNmFont}.equals("2")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle markup="none" fontName="IPAexGothic" fontSize="12.0">
			<conditionExpression><![CDATA[$F{riyoushaNmFont}.equals("1")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle fontName="IPAexGothic" fontSize="10.0">
			<conditionExpression><![CDATA[$F{riyoushaNmFont}.equals("3")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle fontName="IPAexGothic" fontSize="9.0">
			<conditionExpression><![CDATA[$F{riyoushaNmFont}.equals("4")]]></conditionExpression>
		</conditionalStyle>
		<conditionalStyle fontName="IPAexGothic" fontSize="7.0">
			<conditionExpression><![CDATA[$F{riyoushaNmFont}.equals("5")]]></conditionExpression>
		</conditionalStyle>
	</style>
	<style name="Table 5_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 5_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 5_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<dataset name="Dataset1" uuid="abb33cc0-06b7-4d36-980c-bda1cbea8d39">
		<field name="level2Knj" class="java.lang.String">
			<description><![CDATA[level2Knj]]></description>
		</field>
		<field name="level3KoumokuNo" class="java.lang.String">
			<description><![CDATA[level3KoumokuNo]]></description>
		</field>
		<field name="level3Knj" class="java.lang.String">
			<description><![CDATA[level3Knj]]></description>
		</field>
		<field name="memo1Knj" class="java.lang.String">
			<description><![CDATA[memo1Knj]]></description>
		</field>
		<field name="kento" class="java.lang.String">
			<description><![CDATA[kento]]></description>
		</field>
		<field name="level3KoumokuNoFont" class="java.lang.String">
			<description><![CDATA[level3KoumokuNoFont]]></description>
		</field>
		<field name="level3KnjFont" class="java.lang.String">
			<description><![CDATA[level3KnjFont]]></description>
		</field>
		<field name="shosiki2Flg" class="java.lang.String">
			<description><![CDATA[shosiki2Flg]]></description>
		</field>
	</dataset>
	<parameter name="InvoiceItems" class="net.sf.jasperreports.engine.JRDataSource"/>
	<field name="jigyoName" class="java.lang.String">
		<description><![CDATA[jigyoName]]></description>
	</field>
	<field name="riyoushaNm" class="java.lang.String">
		<description><![CDATA[riyoushaNm]]></description>
	</field>
	<field name="createYmd" class="java.lang.String">
		<description><![CDATA[createYmd]]></description>
	</field>
	<field name="shiTeiDate" class="java.lang.String">
		<description><![CDATA[shiTeiDate]]></description>
	</field>
	<field name="tantoNm" class="java.lang.String">
		<description><![CDATA[tantoNm]]></description>
	</field>
	<field name="keisho" class="java.lang.String">
		<description><![CDATA[keisho]]></description>
	</field>
	<field name="level1Knj" class="java.lang.String">
		<description><![CDATA[level1Knj]]></description>
	</field>
	<field name="list2" class="net.sf.jasperreports.engine.JRDataSource">
		<description><![CDATA[list2]]></description>
	</field>
	<field name="list" class="net.sf.jasperreports.engine.JRDataSource">
		<description><![CDATA[list]]></description>
	</field>
	<field name="shiTeiKubun" class="java.lang.Integer">
		<description><![CDATA[shiTeiKubun]]></description>
	</field>
	<field name="kaiteiKbn" class="java.lang.Integer">
		<description><![CDATA[kaiteiKbn]]></description>
	</field>
	<field name="tantoFont" class="java.lang.String">
		<description><![CDATA[tantoFont]]></description>
	</field>
	<field name="riyoushaNmFont" class="java.lang.String">
		<description><![CDATA[riyoushaNmFont]]></description>
	</field>
	<pageHeader height="153" splitType="Stretch">
		<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == 1]]></printWhenExpression>
		<element kind="textField" uuid="c57a485a-714b-4969-a843-68306dc8232b" positionType="Float" stretchType="ContainerHeight" x="408" y="0" width="100" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" style="Table_CH">
			<printWhenExpression><![CDATA[$F{shiTeiKubun} != 1 ]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDate}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<box style="Table_CH">
				<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="textField" uuid="2836b189-e03c-4995-9f81-3eb18af80d4f" x="408" y="10" width="100" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" style="Table_CH">
			<expression><![CDATA[$F{jigyoName}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<box style="Table_CH">
				<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="frame" uuid="e019a03e-a3e2-43e2-897e-225336c0bc4d" x="1" y="59" width="514" height="48">
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<element kind="staticText" uuid="a85a10fb-074f-42f7-8926-1012a2e42954" x="1" y="0" width="170" height="22" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[利用者氏名]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="090e62d9-7fd6-406c-9390-e65ed7b5f77c" x="170" y="0" width="172" height="22" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[基準日]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="8e7b0f2c-639c-4007-a2eb-6d446c51ccf6" x="342" y="0" width="172" height="22" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[担当者名]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="4dea307d-7291-4f85-b946-d34f1055e568" x="1" y="22" width="140" height="26" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" printRepeatedValues="false" vTextAlign="Middle" style="riyoushaFont">
				<paragraph leftIndent="8"/>
				<expression><![CDATA[$F{riyoushaNm}]]></expression>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box style="riyoushaFont">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="0096a8fb-9077-4cd3-84b3-b51484b9eb4a" x="140" y="22" width="30" height="26" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<expression><![CDATA[$F{keisho}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<pen lineWidth="0.75"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="30438532-25a0-49c3-8308-403b4f409e93" x="170" y="22" width="172" height="26" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<expression><![CDATA[$F{createYmd}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="6e7dad15-81ca-4934-b391-19bdb269caf8" stretchType="NoStretch" x="342" y="22" width="172" height="26" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" blankWhenNull="true" printRepeatedValues="false" vTextAlign="Middle" style="tanntoFont">
				<paragraph leftIndent="2"/>
				<expression><![CDATA[$F{tantoNm}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				<box style="tanntoFont">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<box>
				<pen lineWidth="1.75"/>
				<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="frame" uuid="353c3717-279b-4126-9cc6-14490dd57993" x="1" y="112" width="515" height="40">
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<element kind="staticText" uuid="aaec0cba-755e-40e9-9c4a-bf8cb9b9d78a" x="3" y="0" width="20" height="18" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Right" vTextAlign="Middle">
				<text><![CDATA[8]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<element kind="textField" uuid="90145016-68e4-4a5e-afb7-fc8ad0f97cec" stretchType="ContainerHeight" x="32" y="0" width="137" height="19" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkTarget="Self" vTextAlign="Middle">
				<expression><![CDATA[$F{level1Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="2ec2d303-1d37-428f-b507-480b226e9df5" x="0" y="19" width="26" height="22" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[No]]></text>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="80f709ce-a500-4999-964e-1e0eac31f4f0" x="26" y="19" width="190" height="22" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[情報項目]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="bdad79a8-b6d8-4673-8a60-a4186869c8c3" x="216" y="19" width="272" height="22" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{kaiteiKbn} != 0]]></printWhenExpression>
				<text><![CDATA[具体的状況]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="bafb8b1a-31b1-4755-b888-bc5fdd4df95e" x="488" y="19" width="26" height="22" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{kaiteiKbn} != 0]]></printWhenExpression>
				<text><![CDATA[検                  討]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="1ab947df-c931-4334-9f79-317fc4fec325" x="242" y="19" width="272" height="22" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{kaiteiKbn} == 0]]></printWhenExpression>
				<text><![CDATA[具体的状況]]></text>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="d13842d3-290a-4803-b4bf-e19895b5d2cd" x="216" y="19" width="26" height="22" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{kaiteiKbn} == 0]]></printWhenExpression>
				<text><![CDATA[検                  討]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="70c458df-3b1c-4db0-a40d-7c76038bece2" x="3" y="0" width="20" height="18" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Right" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{kaiteiKbn} == 0]]></printWhenExpression>
				<text><![CDATA[8]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<element kind="textField" uuid="4c4e4c48-a73b-42f5-b29b-f1538bc534a5" stretchType="ContainerHeight" x="32" y="0" width="137" height="19" fontName="IPAexMincho" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" bold="false" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{kaiteiKbn} == 0]]></printWhenExpression>
				<paragraph lineSpacing="Proportional" lineSpacingSize="1.5"/>
				<expression><![CDATA[$F{level1Knj}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
		</element>
		<element kind="staticText" uuid="5e08f942-8676-4b20-bb37-b6ada4b10c87" x="3" y="34" width="508" height="14" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
			<printWhenExpression><![CDATA[$F{kaiteiKbn}==2]]></printWhenExpression>
			<text><![CDATA[アセスメントシート]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<box>
				<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="staticText" uuid="c02b2629-c212-4dc7-ac50-dc064b4ef2f0" x="3" y="34" width="508" height="14" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
			<printWhenExpression><![CDATA[$F{kaiteiKbn}!=2]]></printWhenExpression>
			<text><![CDATA[アセスメントのための情報収集シート128（居宅）]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<box>
				<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="200" splitType="Immediate">
			<element kind="frame" uuid="9c587106-2c45-464d-9c33-dd85e68c631a" stretchType="NoStretch" x="0" y="-1" width="518" height="60">
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<element kind="component" uuid="2e6f5eae-9f2b-4e3a-ad05-c28cd4c1e00d" positionType="Float" stretchType="NoStretch" x="0" y="18" width="515" height="39" removeLineWhenBlank="true" printWhenDetailOverflows="true">
					<component kind="table" whenNoDataType="NoDataCell">
						<datasetRun uuid="9a48dc95-2e6e-4885-a181-af1e9f0f22b4" subDataset="Dataset1">
							<dataSourceExpression><![CDATA[$F{list2}]]></dataSourceExpression>
						</datasetRun>
						<columnHeader splitType="Immediate">
							<printWhenExpression><![CDATA[]]></printWhenExpression>
						</columnHeader>
						<detail splitType="Prevent"/>
						<column kind="single" uuid="239c24af-84db-45ef-be79-f2735d46df5a" width="514">
							<tableHeader height="20" rowSpan="1">
								<element kind="textField" uuid="cc0bcead-5d5f-4bb9-8a77-fa141b4f37b6" stretchType="NoStretch" x="0" y="0" width="514" height="20" markup="none" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" vTextAlign="Middle">
									<paragraph leftIndent="4" spacingBefore="0"/>
									<expression><![CDATA[$F{level2Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<box leftPadding="4">
										<pen lineWidth="0.0"/>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</tableHeader>
							<tableFooter height="1" rowSpan="1" style="Table 1_TH">
								<box style="Table 1_TH">
									<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</tableFooter>
							<detailCell height="19" style="Table 1_TD">
								<element kind="textField" uuid="b366ab47-f537-401d-9bb4-b9cc00d619cd" stretchType="ContainerHeight" x="0" y="0" width="60" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" vTextAlign="Top">
									<paragraph spacingBefore="3"/>
									<expression><![CDATA[$F{level3Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<pen lineWidth="1.0"/>
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="6d7da1f4-9674-47a9-95ac-c5184a69346d" stretchType="ContainerHeight" x="60" y="0" width="454" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" vTextAlign="Top">
									<paragraph spacingBefore="3"/>
									<expression><![CDATA[$F{memo1Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<box style="Table 1_TD">
									<pen lineWidth="2.0"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						</column>
					</component>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.components.autoresize.next" value="true"/>
					<property name="net.sf.jasperreports.export.xls.ignore.cell.border" value="true"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
				<element kind="component" uuid="c2551fc2-7643-47cc-b018-58f7979ca49b" stretchType="NoStretch" x="0" y="-1" width="514" height="19" removeLineWhenBlank="true" printWhenDetailOverflows="true" printInFirstWholeBand="true">
					<printWhenExpression><![CDATA[$F{kaiteiKbn}!=0]]></printWhenExpression>
					<component kind="table" whenNoDataType="NoDataCell">
						<datasetRun uuid="cacce81c-b2a6-4540-afc7-8ae1a5de059b" subDataset="Dataset1">
							<dataSourceExpression><![CDATA[$F{list}]]></dataSourceExpression>
						</datasetRun>
						<tableHeader/>
						<columnHeader/>
						<detail splitType="Stretch"/>
						<column kind="single" uuid="cee703da-f274-4ba5-ba1e-a48189250b13" width="514">
							<detailCell height="20" style="Table 1_TD">
								<element kind="textField" uuid="bb812fc5-fc05-47df-b46b-cf88f435650b" stretchType="ContainerHeight" x="0" y="0" width="514" height="19" markup="none" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" removeLineWhenBlank="true" vTextAlign="Middle">
									<paragraph leftIndent="4"/>
									<expression><![CDATA[$F{level2Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="7e79c482-0747-4e33-af75-c37c5d90a6f0" stretchType="ContainerHeight" x="0" y="0" width="26" height="19" markup="none" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" blankWhenNull="true" removeLineWhenBlank="true" bold="true" hTextAlign="Center" vTextAlign="Top">
									<paragraph firstLineIndent="0" rightIndent="2" spacingBefore="3"/>
									<expression><![CDATA[$F{level3KoumokuNo}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="e3748f9b-c5ce-41b9-b97a-6c0008ef49a3" stretchType="ContainerHeight" x="26" y="0" width="190" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" removeLineWhenBlank="true" vTextAlign="Top">
									<paragraph firstLineIndent="0" spacingBefore="3"/>
									<expression><![CDATA[$F{level3Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="b348a284-2a6d-469f-815a-31b16336cb2c" stretchType="ContainerHeight" x="216" y="0" width="272" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" vTextAlign="Top">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph spacingBefore="3"/>
									<expression><![CDATA[$F{memo1Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="5390ed6b-77ca-439c-8f57-01f410ad10a6" stretchType="ContainerHeight" x="0" y="0" width="26" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" blankWhenNull="true" removeLineWhenBlank="true" bold="false" italic="true" hTextAlign="Center" vTextAlign="Top">
									<paragraph firstLineIndent="0" rightIndent="2" spacingBefore="3"/>
									<expression><![CDATA[$F{level3KoumokuNoFont}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="56e7f0cd-5c5a-4bfa-abc2-fbd87f3b4ec5" stretchType="ContainerHeight" x="26" y="0" width="190" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" removeLineWhenBlank="true" vTextAlign="Top">
									<paragraph firstLineIndent="0" spacingBefore="3"/>
									<expression><![CDATA[$F{level3KnjFont}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="acfe4988-aee2-48e4-9760-9c3b8321767f" stretchType="ContainerHeight" x="488" y="0" width="26" height="19" markup="none" fontName="ＭＳ 明朝" fontSize="13.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$F{level2Knj}==""]]></printWhenExpression>
									<paragraph rightIndent="4"/>
									<expression><![CDATA[$F{kento}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="line" uuid="759d18d4-2b43-42c9-bcbe-8df794b1ebbf" stretchType="ContainerHeight" x="489" y="0" width="25" height="20" direction="BottomUp" printRepeatedValues="false">
									<printWhenExpression><![CDATA[$F{shosiki2Flg}=="8"]]></printWhenExpression>
								</element>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<box style="Table 1_TD">
									<pen lineWidth="1.0"/>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						</column>
					</component>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.components.autoresize.next" value="true"/>
					<property name="net.sf.jasperreports.export.xls.ignore.cell.border" value="true"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
				<element kind="component" uuid="885f18a4-bbb2-4f9e-9a82-4f0a540e89d4" stretchType="NoStretch" x="0" y="-1" width="514" height="19" removeLineWhenBlank="true" printWhenDetailOverflows="true" printInFirstWholeBand="true">
					<printWhenExpression><![CDATA[$F{kaiteiKbn}==0]]></printWhenExpression>
					<component kind="table" whenNoDataType="NoDataCell">
						<datasetRun uuid="be3ddd49-4430-480c-bd2f-303c9bd39d8b" subDataset="Dataset1">
							<dataSourceExpression><![CDATA[$F{list}]]></dataSourceExpression>
						</datasetRun>
						<tableHeader/>
						<columnHeader/>
						<detail splitType="Stretch"/>
						<column kind="single" uuid="867a85fe-0dce-4d02-92fa-3bdac4af5f72" width="514">
							<detailCell height="20" style="Table 1_TD">
								<element kind="textField" uuid="28f9206c-4eb8-419e-80e8-694374595b4b" stretchType="ContainerHeight" x="0" y="0" width="514" height="19" markup="none" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" bold="true" vTextAlign="Middle">
									<paragraph leftIndent="4"/>
									<expression><![CDATA[$F{level2Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="a407729d-d5f1-4988-bb62-bfb5f3dffe70" stretchType="ContainerHeight" x="0" y="0" width="26" height="19" markup="none" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="ScaleFont" linkType="None" linkTarget="Self" blankWhenNull="true" removeLineWhenBlank="true" bold="true" hTextAlign="Center" vTextAlign="Top">
									<paragraph rightIndent="0" spacingBefore="3"/>
									<expression><![CDATA[$F{level3KoumokuNo}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<box leftPadding="0">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="f8b04946-4212-488c-97d3-1bbb204e106f" stretchType="ContainerHeight" x="26" y="0" width="190" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" removeLineWhenBlank="true" bold="true" vTextAlign="Top">
									<paragraph spacingBefore="3"/>
									<expression><![CDATA[$F{level3Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="cdb56e6c-1cec-45c1-b94d-6a11581c89ce" stretchType="ContainerHeight" x="216" y="0" width="26" height="19" markup="none" fontName="IPAexMincho" fontSize="13.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Center" vTextAlign="Middle">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph rightIndent="4"/>
									<expression><![CDATA[$F{kento}]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="82bd8f11-f0c6-45ff-8916-afde90a3ea8e" stretchType="ContainerHeight" x="242" y="0" width="272" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" vTextAlign="Top">
									<printWhenExpression><![CDATA[$F{level2Knj}.equals("")]]></printWhenExpression>
									<paragraph spacingBefore="3"/>
									<expression><![CDATA[$F{memo1Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="5139c3f9-1edc-431a-af2e-449f4af7a581" stretchType="ContainerHeight" x="0" y="0" width="26" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="ScaleFont" linkType="None" linkTarget="Self" blankWhenNull="true" removeLineWhenBlank="true" bold="false" italic="true" hTextAlign="Center" vTextAlign="Top">
									<paragraph rightIndent="4" spacingBefore="3"/>
									<expression><![CDATA[$F{level3KoumokuNoFont}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="cf597ee0-b84a-4ef1-89b9-b24b3423e2a7" stretchType="ContainerHeight" x="26" y="0" width="190" height="19" markup="none" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkType="None" linkTarget="Self" removeLineWhenBlank="true" bold="false" vTextAlign="Top">
									<paragraph spacingBefore="3"/>
									<expression><![CDATA[$F{level3KnjFont}]]></expression>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box leftPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box style="Table 1_TD">
									<pen lineWidth="1.0"/>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						</column>
					</component>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.components.autoresize.next" value="true"/>
					<property name="net.sf.jasperreports.export.xls.ignore.cell.border" value="true"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
		</band>
	</detail>
</jasperReport>
