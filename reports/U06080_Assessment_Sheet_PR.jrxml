<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="U06080_アセスメント表（R）" language="java" pageWidth="595" pageHeight="842" columnWidth="511" leftMargin="44" rightMargin="40" topMargin="28" bottomMargin="20" uuid="95b152d9-156d-4073-8f78-d9eb987ca733">
	<property name="com.jaspersoft.studio.report.description" value=""/>
	<field name="title" class="java.lang.String">
		<description><![CDATA[title]]></description>
	</field>
	<field name="syubetuKnj" class="java.lang.String">
		<description><![CDATA[syubetuKnj]]></description>
	</field>
	<field name="shiTeiDateGG" class="java.lang.String">
		<description><![CDATA[shiTeiDateGG]]></description>
	</field>
	<field name="shiTeiKubun" class="java.lang.Integer">
		<description><![CDATA[shiTeiKubun]]></description>
	</field>
	<field name="jigyousha" class="java.lang.String">
		<description><![CDATA[jigyousha]]></description>
	</field>
	<field name="name1Knj" class="java.lang.String">
		<description><![CDATA[name1Knj]]></description>
	</field>
	<field name="name2Knj" class="java.lang.String">
		<description><![CDATA[name2Knj]]></description>
	</field>
	<field name="colorFlg" class="java.lang.Integer">
		<description><![CDATA[colorFlg]]></description>
	</field>
	<field name="p1MemoKnj" class="java.lang.String">
		<description><![CDATA[p1MemoKnj]]></description>
	</field>
	<field name="p1MemoFont" class="java.lang.String">
		<description><![CDATA[p1MemoFont]]></description>
	</field>
	<field name="p1MemoColor" class="java.lang.String">
		<description><![CDATA[p1MemoColor]]></description>
	</field>
	<field name="p1A" class="java.lang.String">
		<description><![CDATA[p1A]]></description>
	</field>
	<field name="p1B" class="java.lang.String">
		<description><![CDATA[p1B]]></description>
	</field>
	<field name="p1C" class="java.lang.String">
		<description><![CDATA[p1C]]></description>
	</field>
	<field name="p2MemoKnj" class="java.lang.String">
		<description><![CDATA[p2MemoKnj]]></description>
	</field>
	<field name="p2MemoFont" class="java.lang.String">
		<description><![CDATA[p2MemoFont]]></description>
	</field>
	<field name="p2MemoColor" class="java.lang.String">
		<description><![CDATA[p2MemoColor]]></description>
	</field>
	<field name="p2A" class="java.lang.String">
		<description><![CDATA[p2A]]></description>
	</field>
	<field name="p2B" class="java.lang.String">
		<description><![CDATA[p2B]]></description>
	</field>
	<field name="p2C" class="java.lang.String">
		<description><![CDATA[p2C]]></description>
	</field>
	<field name="p2D" class="java.lang.String">
		<description><![CDATA[p2D]]></description>
	</field>
	<field name="p2E" class="java.lang.String">
		<description><![CDATA[p2E]]></description>
	</field>
	<field name="r1MemoKnj" class="java.lang.String">
		<description><![CDATA[r1MemoKnj]]></description>
	</field>
	<field name="r1MemoFont" class="java.lang.String">
		<description><![CDATA[r1MemoFont]]></description>
	</field>
	<field name="r1MemoColor" class="java.lang.String">
		<description><![CDATA[r1MemoColor]]></description>
	</field>
	<field name="r1A" class="java.lang.String">
		<description><![CDATA[r1A]]></description>
	</field>
	<field name="r1B" class="java.lang.String">
		<description><![CDATA[r1B]]></description>
	</field>
	<field name="r1C" class="java.lang.String">
		<description><![CDATA[r1C]]></description>
	</field>
	<field name="r2MemoKnj" class="java.lang.String">
		<description><![CDATA[r2MemoKnj]]></description>
	</field>
	<field name="r2MemoFont" class="java.lang.String">
		<description><![CDATA[r2MemoFont]]></description>
	</field>
	<field name="r2MemoColor" class="java.lang.String">
		<description><![CDATA[r2MemoColor]]></description>
	</field>
	<field name="r2" class="java.lang.String">
		<description><![CDATA[r2]]></description>
	</field>
	<field name="bunsyoKanriNo" class="java.lang.String">
		<description><![CDATA[bunsyoKanriNo]]></description>
	</field>
	<field name="emptyFlg" class="java.lang.Boolean"/>
	<field name="shiTeiDateYY" class="java.lang.String">
		<description><![CDATA[shiTeiDateYY]]></description>
	</field>
	<field name="shiTeiDateMM" class="java.lang.String">
		<description><![CDATA[shiTeiDateMM]]></description>
	</field>
	<field name="shiTeiDateDD" class="java.lang.String">
		<description><![CDATA[shiTeiDateDD]]></description>
	</field>
	<pageHeader height="54" splitType="Stretch">
		<element kind="textField" uuid="2dd7c01e-17ef-4faa-ba63-c722a3b60735" x="0" y="0" width="82" height="20" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA[$F{syubetuKnj}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<box>
				<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
		<element kind="textField" uuid="659bda43-0cb0-4ca9-be6d-801e19e104fc" x="100" y="13" width="310" height="14" fontName="IPAexGothic" fontSize="14.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA[$F{title}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="textField" uuid="e146e0b6-7e5f-45b0-a63a-8bccec9d0765" x="350" y="35" width="161" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[$F{jigyousha}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<element kind="staticText" uuid="241391b4-5bae-4aeb-8cfa-f5a5d4e4b5f5" x="0" y="35" width="63" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<printWhenExpression><![CDATA[!$F{emptyFlg}]]></printWhenExpression>
			<text><![CDATA[利用者氏名:]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="df4e0d07-6880-443b-8116-bbb89d7e9abf" x="63" y="35" width="290" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<expression><![CDATA[$F{name1Knj} + " " + $F{name2Knj}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<element kind="textField" uuid="fc226853-8850-45ed-a904-2e974fe64c38" key="" x="0" y="0" width="82" height="9" fontName="ＭＳ Ｐゴシック" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<printWhenExpression><![CDATA[$F{bunsyoKanriNo}==null?false:true]]></printWhenExpression>
			<expression><![CDATA[$F{bunsyoKanriNo}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="80f0d450-ca2d-49f5-9ac5-b0c75ebd296c" x="426" y="0" width="85" height="10" markup="html" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
			<printWhenExpression><![CDATA[($F{shiTeiKubun} != 1) && (!$F{emptyFlg})]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDateGG}+
"<font style='font-size:9pt'>"+$F{shiTeiDateYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{shiTeiDateMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{shiTeiDateDD}+"</font>"+
"日"]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="740" splitType="Stretch">
			<element kind="staticText" uuid="7a7037a8-501d-48e1-9f19-6284a54c7333" x="0" y="14" width="127" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
				<text><![CDATA[P.意思決定権
]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="frame" uuid="12fd6be9-2ade-485d-a9e7-55f27aa02a3b" key="" x="0" y="27" width="510" height="190">
				<borderSplitType>NoBorders</borderSplitType>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="frame" uuid="98324898-e6d8-45c2-a1ab-d028f6e9b28b" x="0" y="0" width="510" height="80">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="frame" uuid="885b5c74-321a-43be-9839-763466db0fb1" x="0" y="0" width="127" height="80">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="8fe28111-67ca-4843-b33e-4869df59d1de" x="6" y="4" width="114" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
							<text><![CDATA[P1.意思決定権]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</element>
						<element kind="textField" uuid="5b226c87-4f36-4a34-8581-aa95cf544d9c" x="3" y="14" width="124" height="66" forecolor="#030303" backcolor="#FFFFFF" markup="none" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
							<expression><![CDATA[$F{p1MemoKnj}]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{colorFlg}==1?($F{p1MemoColor}==null?"#000000":$F{p1MemoColor}):"#000000"]]></propertyExpression>
							<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{p1MemoFont}==null?"12":$F{p1MemoFont}.toString()]]></propertyExpression>
							<box topPadding="4"/>
						</element>
						<box>
							<pen lineWidth="1.0" lineStyle="Solid"/>
							<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="0ee6e64c-5053-48b3-957d-a615a093c136" x="127" y="0" width="383" height="80">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="30c55833-bd65-4e41-afa5-191e8592ca3b" x="6" y="6" width="250" height="23" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[ 0. いいえ                            1. はい]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<pen lineWidth="1.0"/>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="frame" uuid="c8b56d82-d90d-43d6-873c-9c36db44e341" x="6" y="40" width="368" height="12">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="0"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<element kind="staticText" uuid="657768f0-a0b6-46b7-b7ef-5e048b49cffd" x="0" y="1" width="300" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[a.法定後見人等]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="staticText" uuid="f3350ea2-ea43-4690-8ef3-a6c8d85f3c4d" x="324" y="1" width="11" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[a]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="a2122383-fb64-4dcb-9ac8-65c5b0de9386" x="336" y="1" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{p1A}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<pen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
						</element>
						<element kind="frame" uuid="eb64a14c-74de-4925-b48c-b81418d47d9f" x="6" y="53" width="368" height="12">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="staticText" uuid="6b570a6d-7b9e-41bf-95db-2964abac8606" x="0" y="1" width="300" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[b.任意後見]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="staticText" uuid="4ee69d2a-dc20-4536-a2ef-8f255c6afe0e" x="324" y="1" width="11" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[b]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="60d2ab40-e4c9-4535-9d3c-2a938095a2a3" x="336" y="1" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{p1B}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
						</element>
						<element kind="frame" uuid="b1027765-3ef6-4e9f-91a9-0d44ace9989c" x="6" y="66" width="368" height="12">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="2"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="staticText" uuid="1b2df80c-8471-4371-a07c-ad25386c4032" x="0" y="1" width="300" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[c.家族などの代理決定]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="staticText" uuid="b7ba124c-c6b9-4770-959c-13135512202c" x="324" y="1" width="11" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[c]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="9c65d2dd-7f61-4a2d-a5a9-5f82aacdec89" x="336" y="1" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{p1C}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
						</element>
						<box>
							<pen lineWidth="1.0" lineStyle="Solid"/>
							<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="2013bf7b-d755-4cb9-8a5b-74a842180e46" x="0" y="80" width="510" height="110">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="frame" uuid="1fa5e358-3122-4b6d-a044-229d490acfe2" x="0" y="0" width="127" height="110">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="03c4183f-24eb-4205-aef5-dac429cc530c" x="6" y="4" width="114" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
							<text><![CDATA[P2.事前指示]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</element>
						<element kind="textField" uuid="1c7ba343-d02d-46e7-af2d-77db653a30cd" x="3" y="14" width="124" height="96" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
							<expression><![CDATA[$F{p2MemoKnj}]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{colorFlg}==1?($F{p2MemoColor}==null?"#000000":$F{p2MemoColor}):"#000000"]]></propertyExpression>
							<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{p2MemoFont}==null?"12":$F{p2MemoFont}.toString()]]></propertyExpression>
							<box topPadding="4"/>
						</element>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="f58355e5-89bf-4d71-9190-fefb820607fc" x="127" y="0" width="383" height="110">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="511c9fca-bce7-4ecf-a952-3a09e038c67d" x="6" y="6" width="250" height="23" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[ 0. いいえ                            1. はい]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<pen lineWidth="1.0"/>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="frame" uuid="7a31b512-2a83-43cf-8d66-28f184663086" x="6" y="40" width="368" height="12">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="0"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<element kind="staticText" uuid="5959a947-c3b2-4f10-a47a-d5736dd4d330" x="0" y="1" width="300" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[a.蘇生術をしない]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="staticText" uuid="b8bd909c-2b9e-459f-a663-ae340bd38938" x="324" y="1" width="11" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[a]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="f58ba616-fd74-4672-8156-8af75b9a673f" x="336" y="1" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{p2A}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<pen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
						</element>
						<element kind="frame" uuid="86e87e84-b890-4a0c-bc7a-9c23de5f5dfd" x="6" y="53" width="368" height="12">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="staticText" uuid="02a94256-dab0-4706-830e-027ada15367f" x="0" y="1" width="300" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[b.挿管しない]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="staticText" uuid="ec5d4f6f-b464-4b69-9dc3-1d9ed86173d5" x="324" y="1" width="11" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[b]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="8237c656-0c6e-4f01-b370-40e2d94a5267" x="336" y="1" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{p2B}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
						</element>
						<element kind="frame" uuid="96348e95-6055-4984-bcbb-2247e22b2348" x="6" y="66" width="368" height="12">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="2"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="staticText" uuid="63c70bf6-a478-4ddc-9522-b5e7673bdef9" x="0" y="1" width="300" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[c.入院しない]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="staticText" uuid="82f21ef4-7066-4c75-a2fe-bc0f693b244e" x="324" y="1" width="11" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[c]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="0d0021c1-34aa-475d-9358-150c3163555e" x="336" y="1" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{p2C}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
						</element>
						<element kind="frame" uuid="57401eec-f290-4283-a094-5241acc78a16" x="6" y="79" width="368" height="12">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="2"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<element kind="staticText" uuid="ed321124-dbef-4c0e-8ef9-ae52d9b726a9" x="0" y="1" width="300" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[d.経管栄養をしない]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="staticText" uuid="3d9e5c79-763c-4488-95c6-85a85b54dfec" x="324" y="1" width="11" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[d]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="28465fc6-1ed2-4f72-b0e0-7ddb8e1e6d5a" x="336" y="1" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{p2D}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
						</element>
						<element kind="frame" uuid="451401b9-207e-4abf-9ef4-1bff631efbe2" x="6" y="92" width="368" height="12">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="2"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<element kind="staticText" uuid="3facf8c1-3a97-4cf2-917f-3881d20e8910" x="0" y="1" width="300" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[e.薬剤制限]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="staticText" uuid="99d1ecbb-efbb-4650-8efa-9c36eac3bcca" x="324" y="1" width="11" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[e]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="1db1049b-8d87-4e51-8929-f25606e8619d" x="336" y="1" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{p2E}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
						</element>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<box>
						<pen lineWidth="1.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<box>
					<pen lineWidth="2.0"/>
					<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="19743dd6-f551-4e34-a56f-5c555d5bbd86" x="0" y="227" width="127" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
				<text><![CDATA[R.退所の可能性
]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<element kind="frame" uuid="88c8f8ee-83b4-4f39-9300-0d6b775120e5" key="" x="0" y="240" width="510" height="172">
				<borderSplitType>NoBorders</borderSplitType>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<element kind="frame" uuid="5342cfef-942f-48aa-a3d9-79c484694ae9" x="0" y="0" width="510" height="92">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="frame" uuid="b4d0ae25-ba8c-4224-8aec-9c5e46d872f0" x="0" y="0" width="127" height="92">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="d4e4ee5e-1ffb-4a0c-833f-d43e73d037ca" x="6" y="4" width="114" height="10" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
							<text><![CDATA[R1.退所の可能性]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</element>
						<element kind="textField" uuid="ac73652d-e627-42d3-bff6-e0a9f975723d" x="3" y="14" width="124" height="66" forecolor="#030303" backcolor="#FFFFFF" markup="none" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
							<expression><![CDATA[$F{r1MemoKnj}]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{colorFlg}==1?($F{r1MemoColor}==null?"#000000":$F{r1MemoColor}):"#000000"]]></propertyExpression>
							<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{r1MemoFont}==null?"12":$F{r1MemoFont}.toString()]]></propertyExpression>
							<box topPadding="4"/>
						</element>
						<box>
							<pen lineWidth="1.0" lineStyle="Solid"/>
							<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="5ebc1533-3cb4-421f-8ac1-7d253e92d9c3" x="127" y="0" width="383" height="92">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="a06318fc-5277-483a-b2ce-8d567117d8a0" x="6" y="6" width="250" height="23" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
							<text><![CDATA[ 0. いいえ                            1. はい]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<pen lineWidth="1.0"/>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="frame" uuid="dfdea9d3-8403-4371-a9be-c82c7f6a8de3" x="6" y="40" width="368" height="12">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="0"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<element kind="staticText" uuid="2bc83b6e-f829-4a06-86e7-cc8ab62bc8ba" x="0" y="1" width="300" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[a.利用者は地域に戻りたい/留まりたいと言うか、それを示す]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="staticText" uuid="6a146a24-7b6b-4611-911c-df9f23ceacdc" x="324" y="1" width="11" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[a]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="e7a86218-9c1f-400f-887a-258104c20c29" x="336" y="1" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{r1A}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<pen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
						</element>
						<element kind="frame" uuid="e7feb77c-120a-4419-954a-65def5450aa0" x="6" y="53" width="368" height="23">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="staticText" uuid="1fa037cc-db8e-40fe-96ec-a13bd8a9f6ba" x="0" y="1" width="300" height="22" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[b.退所に対して、または地域にある住宅の維持に対して
   積極的な支援者がいる]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="staticText" uuid="67dd6d5c-1c39-4620-b18b-28b655acae1d" x="324" y="1" width="11" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[b]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="9f80865f-c50d-41e9-a7cd-259b7016685f" x="336" y="1" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{r1B}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
						</element>
						<element kind="frame" uuid="7efb6993-5867-41a6-b1de-14a9482dfa4e" x="6" y="77" width="368" height="12">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.layout.grid.x" value="-1"/>
							<property name="com.jaspersoft.layout.grid.y" value="2"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="staticText" uuid="abb39a6f-75e8-4b60-a4d2-c1d9f224561d" x="0" y="1" width="300" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[c.地域に住む家がある]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<element kind="staticText" uuid="1dd8d135-7983-4641-a257-4c72e8b5aa62" x="324" y="1" width="11" height="10" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[c]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="6ed75f74-26bf-4818-9851-66246d9d38e0" x="336" y="1" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{r1C}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
						</element>
						<box>
							<pen lineWidth="1.0" lineStyle="Solid"/>
							<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="b16eb027-8f59-4e24-8d4b-5fc42383765d" x="0" y="92" width="510" height="80">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="frame" uuid="7b8172d8-6d8d-47be-91fe-fc74f9093c26" x="0" y="0" width="127" height="80">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="72a5df0f-5cdb-4a33-9693-726abb72af7b" x="6" y="4" width="114" height="20" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" vTextAlign="Middle">
							<text><![CDATA[R2.地域に退所
 　するまでの予測期間
]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
						</element>
						<element kind="textField" uuid="a8371dd4-f406-4cab-a7e8-e4d3651ee276" x="3" y="26" width="124" height="54" fontName="IPAexMincho" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
							<expression><![CDATA[$F{r2MemoKnj}]]></expression>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<propertyExpression name="net.sf.jasperreports.style.forecolor"><![CDATA[$F{colorFlg}==1?($F{r2MemoColor}==null?"#000000":$F{r2MemoColor}):"#000000"]]></propertyExpression>
							<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{r2MemoFont}==null?"12":$F{r2MemoFont}.toString()]]></propertyExpression>
							<box topPadding="4"/>
						</element>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="7b68fe21-2b56-477a-8709-bccd9570b750" x="127" y="0" width="383" height="80">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="frame" uuid="0af5a4fe-f261-43e7-8485-ef18aedd3557" x="6" y="6" width="300" height="72">
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<element kind="staticText" uuid="b1909b6e-81d3-40a5-9336-5645954dab4a" x="0" y="0" width="300" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph lineSpacing="Single" lineSpacingSize="1.0"/>
								<text><![CDATA[0. 1～7日]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="0"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.25"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							</element>
							<element kind="staticText" uuid="9b4ebe11-bfca-4cf1-922a-170077b179ae" x="0" y="12" width="300" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="0"/>
								<text><![CDATA[1. 8～14日]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							</element>
							<element kind="staticText" uuid="98ff8ff9-0e2f-4a7e-8fb0-5caabf17f3eb" x="0" y="24" width="300" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph lineSpacing="Single" lineSpacingSize="1.0"/>
								<text><![CDATA[2. 15～30日]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="2"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							</element>
							<element kind="staticText" uuid="bb912f78-18b3-4d01-84d3-88a1c71dd9ff" x="0" y="36" width="300" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph lineSpacing="Single" lineSpacingSize="1.0" firstLineIndent="0"/>
								<text><![CDATA[3. 31～90日]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="3"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
							</element>
							<element kind="staticText" uuid="e10de97d-dcee-4c54-b47c-2bd9b7310c98" x="0" y="48" width="300" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph lineSpacing="Single" lineSpacingSize="1.0"/>
								<text><![CDATA[4. 91日以上]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="4"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.25"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							</element>
							<element kind="staticText" uuid="7f9de94f-2c4c-42e3-b377-e101a155088f" x="0" y="60" width="300" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<paragraph lineSpacing="Single" lineSpacingSize="1.0"/>
								<text><![CDATA[5. 予測されていない]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="5"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.25"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							</element>
							<box padding="0">
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="textField" uuid="4778c9e6-ea81-41f4-ba9a-6e795b440723" x="342" y="66" width="32" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<expression><![CDATA[$F{r2}]]></expression>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<box>
						<pen lineWidth="1.75"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<box>
					<pen lineWidth="2.0"/>
					<topPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="2.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
</jasperReport>
