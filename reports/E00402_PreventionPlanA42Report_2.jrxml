<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="E00402_介護予防サービス-支援計画表（A4横2枚） - 2" language="java" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="9177353c-9c8d-4017-9d09-9ac9189fe907">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="E00402_PreventionPlanA42_R610"/>
	<style name="Table 2_TH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_CH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_CH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 3_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<dataset name="shienDataList" uuid="423c6e26-b39e-423e-8fc8-0d9232840a2f">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="preventionPlanA31"/>
		<parameter name="printFont" class="java.lang.String"/>
		<field name="shienPointKnj" class="java.lang.String">
			<description><![CDATA[shienPointKnj]]></description>
		</field>
		<field name="infoServiceKnj" class="java.lang.String">
			<description><![CDATA[infoServiceKnj]]></description>
		</field>
		<field name="hokenServiceKnj" class="java.lang.String">
			<description><![CDATA[hokenServiceKnj]]></description>
		</field>
		<field name="svShubetuKnj" class="java.lang.String">
			<description><![CDATA[svShubetuKnj]]></description>
		</field>
		<field name="svJigyoKnj" class="java.lang.String">
			<description><![CDATA[svJigyoKnj]]></description>
		</field>
		<field name="kikanKnj" class="java.lang.String">
			<description><![CDATA[kikanKnj]]></description>
		</field>
		<field name="kadaiNo" class="java.lang.String">
			<description><![CDATA[kadaiNo]]></description>
		</field>
	</dataset>
	<dataset name="sogoKadaiDataList" uuid="f29ac2b5-2cee-4f77-8188-5057ff5506c4">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="preventionPlanA31Data2"/>
		<field name="sogoKadaiNo" class="java.lang.String">
			<description><![CDATA[sogoKadaiNo]]></description>
		</field>
		<field name="sogoKadaiKnj" class="java.lang.String">
			<description><![CDATA[sogoKadaiKnj]]></description>
		</field>
		<field name="sogoTeianKnj" class="java.lang.String">
			<description><![CDATA[sogoTeianKnj]]></description>
		</field>
		<field name="sogoIkouKnj" class="java.lang.String">
			<description><![CDATA[sogoIkouKnj]]></description>
		</field>
		<field name="sogoMokuhyoKnj" class="java.lang.String">
			<description><![CDATA[sogoMokuhyoKnj]]></description>
		</field>
		<field name="kadaiNo" class="java.lang.String">
			<description><![CDATA[kadaiNo]]></description>
		</field>
	</dataset>
	<dataset name="Dataset1" uuid="5b9d5095-4752-4574-a414-c9f1e4d29a26">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="preventionPlanA31"/>
		<parameter name="service1Label" class="java.lang.String"/>
		<parameter name="service2Label" class="java.lang.String"/>
		<parameter name="service3Label" class="java.lang.String"/>
		<parameter name="informalKnjLabel" class="java.lang.String"/>
		<parameter name="shienJigyoLabel" class="java.lang.String"/>
		<parameter name="service1Flg" class="java.lang.Integer"/>
		<parameter name="service2Flg" class="java.lang.Integer"/>
		<parameter name="h21Flg" class="java.lang.Integer"/>
		<parameter name="printFont" class="java.lang.String"/>
		<parameter name="ulFlg" class="java.lang.Integer"/>
		<field name="shienDataList" class="net.sf.jasperreports.engine.JRDataSource">
			<description><![CDATA[shienDataList]]></description>
		</field>
		<field name="sogoKadaiDataList" class="net.sf.jasperreports.engine.JRDataSource">
			<description><![CDATA[sogoKadaiDataList]]></description>
		</field>
	</dataset>
	<field name="title1" class="java.lang.String">
		<description><![CDATA[title1]]></description>
	</field>
	<field name="title2" class="java.lang.String">
		<description><![CDATA[title2]]></description>
	</field>
	<field name="userId" class="java.lang.String">
		<description><![CDATA[userId]]></description>
	</field>
	<field name="shiTeiKubun" class="java.lang.Integer">
		<description><![CDATA[shiTeiKubun]]></description>
	</field>
	<field name="selectDate" class="java.lang.String">
		<description><![CDATA[selectDate]]></description>
	</field>
	<field name="jigyoKnj" class="java.lang.String">
		<description><![CDATA[jigyoKnj]]></description>
	</field>
	<field name="userName" class="java.lang.String">
		<description><![CDATA[userName]]></description>
	</field>
	<field name="keishoKnj" class="java.lang.String">
		<description><![CDATA[keishoKnj]]></description>
	</field>
	<field name="emptyFlg" class="java.lang.Integer">
		<description><![CDATA[emptyFlg]]></description>
	</field>
	<field name="printFont" class="java.lang.String">
		<description><![CDATA[printFont]]></description>
	</field>
	<field name="yoshien3Name" class="java.lang.String">
		<description><![CDATA[yoshien3Name]]></description>
	</field>
	<field name="kenkoRyuitenKnjLabel" class="java.lang.String">
		<description><![CDATA[kenkoRyuitenKnjLabel]]></description>
	</field>
	<field name="informalKnjLabel" class="java.lang.String">
		<description><![CDATA[informalKnjLabel]]></description>
	</field>
	<field name="service1Label" class="java.lang.String">
		<description><![CDATA[service1Label]]></description>
	</field>
	<field name="service2Label" class="java.lang.String">
		<description><![CDATA[service2Label]]></description>
	</field>
	<field name="service3Label" class="java.lang.String">
		<description><![CDATA[service3Label]]></description>
	</field>
	<field name="shienJigyoLabel" class="java.lang.String">
		<description><![CDATA[shienJigyoLabel]]></description>
	</field>
	<field name="kakuninLabel" class="java.lang.String">
		<description><![CDATA[kakuninLabel]]></description>
	</field>
	<field name="doiFlg" class="java.lang.Integer">
		<description><![CDATA[doiFlg]]></description>
	</field>
	<field name="prgFlg" class="java.lang.Integer">
		<description><![CDATA[prgFlg]]></description>
	</field>
	<field name="stFlg" class="java.lang.Integer">
		<description><![CDATA[stFlg]]></description>
	</field>
	<field name="ulFlg" class="java.lang.Integer">
		<description><![CDATA[ulFlg]]></description>
	</field>
	<field name="r3Flg" class="java.lang.Integer">
		<description><![CDATA[r3Flg]]></description>
	</field>
	<field name="h21Flg" class="java.lang.Integer">
		<description><![CDATA[h21Flg]]></description>
	</field>
	<field name="inkanShowFlg" class="java.lang.Integer">
		<description><![CDATA[inkanShowFlg]]></description>
	</field>
	<field name="hanko1Knj" class="java.lang.String">
		<description><![CDATA[hanko1Knj]]></description>
	</field>
	<field name="hanko2Knj" class="java.lang.String">
		<description><![CDATA[hanko2Knj]]></description>
	</field>
	<field name="hanko3Knj" class="java.lang.String">
		<description><![CDATA[hanko3Knj]]></description>
	</field>
	<field name="hanko4Knj" class="java.lang.String">
		<description><![CDATA[hanko4Knj]]></description>
	</field>
	<field name="hanko5Knj" class="java.lang.String">
		<description><![CDATA[hanko5Knj]]></description>
	</field>
	<field name="hanko6Knj" class="java.lang.String">
		<description><![CDATA[hanko6Knj]]></description>
	</field>
	<field name="hanko7Knj" class="java.lang.String">
		<description><![CDATA[hanko7Knj]]></description>
	</field>
	<field name="hanko8Knj" class="java.lang.String">
		<description><![CDATA[hanko8Knj]]></description>
	</field>
	<field name="hanko9Knj" class="java.lang.String">
		<description><![CDATA[hanko9Knj]]></description>
	</field>
	<field name="hanko10Knj" class="java.lang.String">
		<description><![CDATA[hanko10Knj]]></description>
	</field>
	<field name="hanko11Knj" class="java.lang.String">
		<description><![CDATA[hanko11Knj]]></description>
	</field>
	<field name="hanko12Knj" class="java.lang.String">
		<description><![CDATA[hanko12Knj]]></description>
	</field>
	<field name="hanko13Knj" class="java.lang.String">
		<description><![CDATA[hanko13Knj]]></description>
	</field>
	<field name="hanko14Knj" class="java.lang.String">
		<description><![CDATA[hanko14Knj]]></description>
	</field>
	<field name="hanko15Knj" class="java.lang.String">
		<description><![CDATA[hanko15Knj]]></description>
	</field>
	<field name="createYmd" class="java.lang.String">
		<description><![CDATA[createYmd]]></description>
	</field>
	<field name="shokaiYmd" class="java.lang.String">
		<description><![CDATA[shokaiYmd]]></description>
	</field>
	<field name="centerShokuKnj" class="java.lang.String">
		<description><![CDATA[centerShokuKnj]]></description>
	</field>
	<field name="kubun" class="java.lang.Integer">
		<description><![CDATA[kubun]]></description>
	</field>
	<field name="itakuShokuKnj" class="java.lang.String">
		<description><![CDATA[itakuShokuKnj]]></description>
	</field>
	<field name="ninteiYmd" class="java.lang.String">
		<description><![CDATA[ninteiYmd]]></description>
	</field>
	<field name="yukoSYmd" class="java.lang.String">
		<description><![CDATA[yukoSYmd]]></description>
	</field>
	<field name="yukoEYmd" class="java.lang.String">
		<description><![CDATA[yukoEYmd]]></description>
	</field>
	<field name="nintei" class="java.lang.Integer">
		<description><![CDATA[nintei]]></description>
	</field>
	<field name="yoshienKbn" class="java.lang.Integer">
		<description><![CDATA[yoshienKbn]]></description>
	</field>
	<field name="service1Flg" class="java.lang.Integer">
		<description><![CDATA[service1Flg]]></description>
	</field>
	<field name="service2Flg" class="java.lang.Integer">
		<description><![CDATA[service2Flg]]></description>
	</field>
	<field name="mokuhyoDayKnj" class="java.lang.String">
		<description><![CDATA[mokuhyoDayKnj]]></description>
	</field>
	<field name="mokuhyoYearKnj" class="java.lang.String">
		<description><![CDATA[mokuhyoYearKnj]]></description>
	</field>
	<field name="kenkoRyuitenKnj" class="java.lang.String">
		<description><![CDATA[kenkoRyuitenKnj]]></description>
	</field>
	<field name="datoHoshinKnj" class="java.lang.String">
		<description><![CDATA[datoHoshinKnj]]></description>
	</field>
	<field name="sogoHoshinKnj" class="java.lang.String">
		<description><![CDATA[sogoHoshinKnj]]></description>
	</field>
	<field name="program1Flg" class="java.lang.Integer">
		<description><![CDATA[program1Flg]]></description>
	</field>
	<field name="program2Flg" class="java.lang.Integer">
		<description><![CDATA[program2Flg]]></description>
	</field>
	<field name="program3Flg" class="java.lang.Integer">
		<description><![CDATA[program3Flg]]></description>
	</field>
	<field name="program4Flg" class="java.lang.Integer">
		<description><![CDATA[program4Flg]]></description>
	</field>
	<field name="program5Flg" class="java.lang.Integer">
		<description><![CDATA[program5Flg]]></description>
	</field>
	<field name="program6Flg" class="java.lang.Integer">
		<description><![CDATA[program6Flg]]></description>
	</field>
	<field name="program1Cnt" class="java.lang.String">
		<description><![CDATA[program1Cnt]]></description>
	</field>
	<field name="program2Cnt" class="java.lang.String">
		<description><![CDATA[program2Cnt]]></description>
	</field>
	<field name="program3Cnt" class="java.lang.String">
		<description><![CDATA[program3Cnt]]></description>
	</field>
	<field name="program4Cnt" class="java.lang.String">
		<description><![CDATA[program4Cnt]]></description>
	</field>
	<field name="program5Cnt" class="java.lang.String">
		<description><![CDATA[program5Cnt]]></description>
	</field>
	<field name="program6Cnt" class="java.lang.String">
		<description><![CDATA[program6Cnt]]></description>
	</field>
	<field name="centerIkenKnj" class="java.lang.String">
		<description><![CDATA[centerIkenKnj]]></description>
	</field>
	<field name="centerKakuninFlg" class="java.lang.Integer">
		<description><![CDATA[centerKakuninFlg]]></description>
	</field>
	<field name="doiYmd" class="java.lang.String">
		<description><![CDATA[doiYmd]]></description>
	</field>
	<field name="doiKnj" class="java.lang.String">
		<description><![CDATA[doiKnj]]></description>
	</field>
	<field name="doiContent1" class="java.lang.String">
		<description><![CDATA[doiContent1]]></description>
	</field>
	<field name="doiContent2" class="java.lang.String">
		<description><![CDATA[doiContent2]]></description>
	</field>
	<field name="chiJigyoKnj" class="java.lang.String">
		<description><![CDATA[chiJigyoKnj]]></description>
	</field>
	<field name="itkJigyoKnj" class="java.lang.String">
		<description><![CDATA[itkJigyoKnj]]></description>
	</field>
	<field name="dataList1" class="net.sf.jasperreports.engine.JRDataSource">
		<description><![CDATA[dataList1]]></description>
	</field>
	<field name="dataList2" class="net.sf.jasperreports.engine.JRDataSource">
		<description><![CDATA[dataList2]]></description>
	</field>
	<pageHeader height="51" splitType="Stretch">
		<element kind="frame" uuid="3895b9a8-2149-42fb-8327-d1c915e0d072" stretchType="ContainerHeight" x="0" y="0" width="802" height="36" removeLineWhenBlank="true">
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<element kind="textField" uuid="e4ce6e9e-e2d9-423e-bd88-5b216389f7f6" x="686" y="13" width="89" height="10" fontName="IPAexGothic" hTextAlign="Right" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{emptyFlg} == 1 ? false : true]]></printWhenExpression>
				<expression><![CDATA[$F{shiTeiKubun} == 1 || $F{emptyFlg} == 1 ? "" : $F{selectDate}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<element kind="textField" uuid="402580ed-2b3a-4f94-a43f-2a77b55aa498" x="580" y="25" width="195" height="10" fontName="IPAexGothic" hTextAlign="Right" vTextAlign="Middle">
				<expression><![CDATA[$F{jigyoKnj}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<element kind="textField" uuid="5428250a-ff82-42f6-9d2f-89dc8d05acfa" x="2" y="20" width="575" height="14" fontName="IPAexGothic" fontSize="14.0" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{title2}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="textField" uuid="e7022c47-404f-4a40-be5d-b4b06b96f433" x="595" y="2" width="180" height="9" fontName="IPAexGothic" fontSize="9.0" hTextAlign="Right" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{emptyFlg}== 1 ? false : true]]></printWhenExpression>
				<expression><![CDATA["st_iso9001_1"]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
		</element>
		<element kind="frame" uuid="521f6a0b-78f9-4a36-9b77-964f492848cb" stretchType="ContainerHeight" x="0" y="38" width="802" height="13" removeLineWhenBlank="true">
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.layout"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<element kind="staticText" uuid="811c1f5c-a085-4137-8feb-84d2777d2e48" x="83" y="1" width="42" height="10" fontName="IPAexGothic" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[利用者名]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="891101df-3e83-4959-8ebe-4962e4223f44" x="126" y="0" width="90" height="13" rotation="None" fontName="IPAexMincho" fontSize="10.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{userName}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="9c3ed963-6350-4a64-8593-6a791ca91665" x="217" y="0" width="21" height="10" fontName="IPAexGothic" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{keishoKnj}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="staticText" uuid="82a0bbd5-bbf8-46a8-9f93-c1807a752ef5" x="584" y="1" width="95" height="10" fontName="IPAexGothic" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[計画作成（変更）日]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="81bcebf9-2ab7-41af-8818-b7bb39df4094" x="681" y="0" width="95" height="13" fontName="IPAexMincho" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{createYmd}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="line" uuid="6bae352e-7946-47c8-be4e-4f48bfdffbe3" x="83" y="12" width="156" height="1">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="staticText" uuid="4a59cca8-618a-4c21-aa7c-823f16972818" x="3" y="1" width="17" height="10" fontName="IPAexGothic" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[NO.]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="ebdd4a7f-f342-489b-a295-d6ebe83a2e96" x="21" y="0" width="56" height="13" fontName="IPAexMincho" pdfFontName="HeiseiMin-W3" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
				<printWhenExpression><![CDATA[$F{userId} != null]]></printWhenExpression>
				<expression><![CDATA[$F{userId}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="line" uuid="a2312f07-9319-4ab1-b58e-f0652e40df35" x="3" y="12" width="76" height="1">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="line" uuid="bca08dc7-b527-4d90-8b79-50a85d68e974" x="582" y="12" width="195" height="1">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="35" splitType="Stretch">
			<element kind="frame" uuid="03db26be-a59d-445f-b63b-d56e9c226c3a" stretchType="NoStretch" x="0" y="0" width="802" height="35">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<element kind="textField" uuid="d5753586-13b6-42dd-a2be-285578b62370" stretchType="NoStretch" x="1" y="2" width="102" height="12" fontName="IPAexGothic" fontSize="11.0" blankWhenNull="true" printRepeatedValues="false" hTextAlign="Left" vTextAlign="Top">
					<expression><![CDATA["目標とする生活"]]></expression>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</element>
				<element kind="staticText" uuid="3d642a3c-394d-4306-803c-f775a776659b" stretchType="ContainerHeight" x="3" y="14" width="27" height="20" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Left" vTextAlign="Top">
					<text><![CDATA[1日]]></text>
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<box topPadding="3" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="textField" uuid="0c373740-48c4-47ef-b13a-08e756832407" stretchType="ContainerHeight" x="30" y="14" width="358" height="20" fontName="IPAexMincho" fontSize="11.0" textAdjust="StretchHeight" blankWhenNull="true" hTextAlign="Left" vTextAlign="Top">
					<expression><![CDATA[$F{mokuhyoDayKnj}]]></expression>
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="10.0"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="5"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{printFont}]]></propertyExpression>
					<box topPadding="3" leftPadding="4" bottomPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="staticText" uuid="3565ac19-33b1-453d-8fc5-7568089c5c83" stretchType="ContainerHeight" x="388" y="14" width="27" height="20" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Left" vTextAlign="Top">
					<text><![CDATA[1年]]></text>
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box topPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="textField" uuid="567e928c-374f-4e00-9a6b-9231036c7fa3" stretchType="ContainerHeight" x="415" y="14" width="362" height="20" fontName="IPAexMincho" fontSize="11.0" textAdjust="StretchHeight" linkType="None" linkTarget="Self" blankWhenNull="true">
					<expression><![CDATA[$F{mokuhyoYearKnj}]]></expression>
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="10.0"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="5"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{printFont}]]></propertyExpression>
					<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3">
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="83" splitType="Stretch">
			<element kind="component" uuid="81a9b574-dbf6-4498-8bf3-f7a823efe58a" stretchType="ContainerHeight" x="2" y="10" width="776" height="68">
				<printWhenExpression><![CDATA[$F{stFlg} == 0]]></printWhenExpression>
				<component kind="table">
					<datasetRun uuid="086fb172-d1b7-438d-9e64-72fb3e870464" subDataset="Dataset1">
						<dataSourceExpression><![CDATA[$F{dataList2}]]></dataSourceExpression>
						<parameter name="service1Label">
							<expression><![CDATA[$F{service1Label}]]></expression>
						</parameter>
						<parameter name="service2Label">
							<expression><![CDATA[$F{service2Label}]]></expression>
						</parameter>
						<parameter name="service3Label">
							<expression><![CDATA[$F{service3Label}]]></expression>
						</parameter>
						<parameter name="informalKnjLabel">
							<expression><![CDATA[$F{informalKnjLabel}]]></expression>
						</parameter>
						<parameter name="shienJigyoLabel">
							<expression><![CDATA[$F{shienJigyoLabel}]]></expression>
						</parameter>
						<parameter name="service1Flg">
							<expression><![CDATA[$F{service1Flg}]]></expression>
						</parameter>
						<parameter name="service2Flg">
							<expression><![CDATA[$F{service2Flg}]]></expression>
						</parameter>
						<parameter name="h21Flg">
							<expression><![CDATA[$F{h21Flg}]]></expression>
						</parameter>
						<parameter name="printFont">
							<expression><![CDATA[$F{printFont}]]></expression>
						</parameter>
						<parameter name="ulFlg">
							<expression><![CDATA[$F{ulFlg}]]></expression>
						</parameter>
					</datasetRun>
					<detail splitType="Stretch"/>
					<column kind="single" uuid="f1da6413-a551-42fc-8a40-b72c8b120cb6" width="775">
						<columnHeader height="48" rowSpan="1">
							<element kind="staticText" uuid="73ee868c-c3f9-46bc-8a5d-daeb29eba336" x="0" y="0" width="153" height="48" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[目標]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<element kind="staticText" uuid="d648dee3-1e12-420b-877a-05b194be2168" x="153" y="14" width="135" height="34" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[目標についての　　　　　　　　　　　　　　　支援のポイント]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<pen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="858a962e-08c7-4024-811f-0b5993291f5b" x="153" y="0" width="622" height="14" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[支援計画]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="56073061-e95f-4a04-b6a6-d302d39ef005" x="288" y="14" width="121" height="34" fontName="IPAexGothic" fontSize="9.0" blankWhenNull="true" hTextAlign="Center">
								<expression><![CDATA[$P{informalKnjLabel}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="fa8c413e-06f4-46c2-92da-d335087b7a95" x="409" y="14" width="121" height="12" fontName="IPAexGothic" fontSize="9.0" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$P{service1Label}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="*************-49ad-9612-cb7fc11173fb" x="409" y="25" width="121" height="12" fontName="IPAexGothic" fontSize="9.0" blankWhenNull="true" hTextAlign="Center" vTextAlign="Top">
								<expression><![CDATA[$P{service2Label}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="963929c9-b654-450e-8aea-82c364ccd6a1" x="409" y="36" width="121" height="12" fontName="IPAexGothic" fontSize="9.0" blankWhenNull="true" hTextAlign="Center" vTextAlign="Top">
								<expression><![CDATA[$P{service3Label}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="11b3c0d8-4f83-41af-8123-d6de1c12ea8b" x="530" y="14" width="98" height="34" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[サービス種別]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<pen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="f7f946c7-ea96-42c0-89a6-3bb78c18e0be" x="628" y="14" width="73" height="34" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[事業所　　　　(利用先)]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<pen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="20f19e69-6f71-41f9-8c3c-2445bc72eb30" x="701" y="14" width="74" height="34" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[期間]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<pen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="rectangle" uuid="c13bfbd3-19b7-4243-9b15-b21e0dceb97d" x="419" y="26" width="100" height="20" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
								<printWhenExpression><![CDATA[$P{service2Flg}==1 &&$P{h21Flg}==1  ?true:false]]></printWhenExpression>
								<property name="com.jaspersoft.studio.element.name" value="サービス2サービス3_丸ラベル"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
							</element>
							<element kind="rectangle" uuid="b9df008b-2d84-48e3-8cad-acc789bbcbb7" x="419" y="34" width="100" height="12" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
								<printWhenExpression><![CDATA[$P{service2Flg}==1 &&$P{h21Flg}==0  ?true:false]]></printWhenExpression>
								<property name="com.jaspersoft.studio.element.name" value="サービス3_丸ラベル"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
							</element>
							<element kind="rectangle" uuid="27b0de2b-51c9-49d4-bd27-25a7b69a7443" x="419" y="13" width="100" height="12" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
								<printWhenExpression><![CDATA[$P{service1Flg}==1 ?true:false]]></printWhenExpression>
								<property name="com.jaspersoft.studio.element.name" value="サービス1_丸ラベル"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<bottomPen lineWidth="1.0"/>
							</box>
						</columnHeader>
						<detailCell height="24">
							<element kind="frame" uuid="2dac7399-bb6e-430c-a47c-68ddde6564a6" stretchType="ContainerHeight" x="0" y="0" width="775" height="24">
								<borderSplitType>DrawBorders</borderSplitType>
								<property name="com.jaspersoft.layout.grid.x" value="-1"/>
								<property name="com.jaspersoft.layout.grid.y" value="-1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="component" uuid="8a2d790d-0d58-4bb2-8419-9a32e2e78cce" stretchType="ElementGroupHeight" x="0" y="0" width="153" height="24">
									<component kind="table">
										<datasetRun uuid="9f629c71-38f7-48e6-9536-069cc7cd5fe4" subDataset="sogoKadaiDataList">
											<dataSourceExpression><![CDATA[$F{sogoKadaiDataList}]]></dataSourceExpression>
										</datasetRun>
										<column kind="single" uuid="3882a66a-eb6b-4ed0-b87b-20a99bec6d21" width="153">
											<detailCell height="24">
												<element kind="frame" uuid="4555fc46-ac28-4a39-8208-0485749b85ef" stretchType="ContainerHeight" x="0" y="0" width="153" height="24">
													<element kind="textField" uuid="791219ab-07c8-4e29-971f-8262f1d426dc" stretchType="ContainerHeight" x="0" y="0" width="15" height="24" fontName="IPAexMincho" fontSize="11.0" textAdjust="StretchHeight" blankWhenNull="true">
														<expression><![CDATA[$F{kadaiNo}]]></expression>
														<property name="com.jaspersoft.studio.unit.x" value="px"/>
														<property name="com.jaspersoft.studio.unit.y" value="px"/>
														<property name="com.jaspersoft.studio.unit.width" value="px"/>
														<property name="com.jaspersoft.studio.unit.height" value="px"/>
														<box padding="0">
															<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
														</box>
													</element>
													<element kind="textField" uuid="48f17808-9d8d-4ad2-8766-1c03bf99f7b9" stretchType="ContainerHeight" x="15" y="0" width="138" height="24" fontName="IPAexMincho" fontSize="11.0" textAdjust="StretchHeight" blankWhenNull="true">
														<expression><![CDATA[$F{sogoMokuhyoKnj}]]></expression>
														<property name="com.jaspersoft.studio.unit.x" value="px"/>
														<property name="com.jaspersoft.studio.unit.height" value="px"/>
														<property name="com.jaspersoft.studio.unit.width" value="px"/>
														<box padding="0">
															<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
															<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
														</box>
													</element>
												</element>
												<property name="com.jaspersoft.studio.unit.width" value="px"/>
												<property name="com.jaspersoft.studio.unit.height" value="px"/>
												<box>
													<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
													<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
													<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
													<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												</box>
											</detailCell>
											<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
										</column>
									</component>
									<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
									<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
									<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
									<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
								</element>
								<element kind="component" uuid="a04bcbfb-3e2c-4a40-82cf-f20d5ab97c68" stretchType="ElementGroupHeight" x="153" y="0" width="622" height="24">
									<component kind="table" whenNoDataType="AllSectionsNoDetail">
										<datasetRun uuid="1703f148-2be0-4fe8-8ff7-d6eef31e9423" subDataset="shienDataList">
											<dataSourceExpression><![CDATA[$F{shienDataList}]]></dataSourceExpression>
											<parameter name="printFont">
												<expression><![CDATA[$P{printFont}]]></expression>
											</parameter>
										</datasetRun>
										<columnHeader>
											<printWhenExpression><![CDATA[false]]></printWhenExpression>
										</columnHeader>
										<column kind="single" uuid="f46be7d2-a735-41db-b2b3-ae2f0a6f0698" width="135">
											<detailCell height="24">
												<element kind="frame" uuid="c1124b2f-12aa-407e-96d9-fddede43ae28" x="0" y="0" width="135" height="24">
													<property name="com.jaspersoft.studio.unit.width" value="px"/>
													<property name="com.jaspersoft.studio.unit.height" value="px"/>
													<element kind="textField" uuid="858208ad-daba-481b-b093-1e262ca6e103" stretchType="ContainerHeight" x="2" y="0" width="13" height="24" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiMin-W3" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkTarget="Self">
														<expression><![CDATA[$F{kadaiNo}]]></expression>
														<property name="com.jaspersoft.studio.unit.width" value="px"/>
														<property name="com.jaspersoft.studio.unit.x" value="px"/>
														<property name="com.jaspersoft.studio.unit.y" value="px"/>
														<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
														<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{printFont}]]></propertyExpression>
														<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
													</element>
													<element kind="textField" uuid="251a1634-dd9f-42c7-8dca-48681ea647bd" stretchType="ContainerHeight" x="13" y="0" width="115" height="24" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiMin-W3" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkTarget="Self" blankWhenNull="true">
														<expression><![CDATA[$F{shienPointKnj}]]></expression>
														<property name="com.jaspersoft.studio.unit.width" value="px"/>
														<property name="com.jaspersoft.studio.unit.x" value="px"/>
														<property name="com.jaspersoft.studio.unit.y" value="px"/>
														<property name="com.jaspersoft.studio.unit.height" value="px"/>
														<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{printFont}]]></propertyExpression>
														<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
													</element>
													<box padding="0"/>
												</element>
												<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
												<property name="com.jaspersoft.studio.unit.width" value="px"/>
												<property name="com.jaspersoft.studio.unit.height" value="px"/>
												<box padding="0">
													<pen lineWidth="1.0"/>
													<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
													<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
													<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
													<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
												</box>
											</detailCell>
											<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
										</column>
										<column kind="single" uuid="bdef253b-91b4-4bec-b0c8-4fb393fc4117" width="121">
											<detailCell height="24">
												<element kind="textField" uuid="4d9e547e-6155-4317-9c1f-0412b5c16d4c" stretchType="ContainerHeight" x="0" y="0" width="121" height="24" fontName="IPAexMincho" fontSize="11.0" pdfFontName="HeiseiMin-W3" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" linkTarget="Self">
													<expression><![CDATA[$F{infoServiceKnj}]]></expression>
													<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
													<property name="com.jaspersoft.studio.unit.width" value="px"/>
													<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$P{printFont}]]></propertyExpression>
													<box padding="3"/>
												</element>
												<property name="com.jaspersoft.studio.unit.height" value="px"/>
												<property name="com.jaspersoft.studio.unit.width" value="px"/>
												<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
												<box>
													<pen lineWidth="1.0"/>
												</box>
											</detailCell>
											<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
										</column>
										<column kind="single" uuid="089c7ab0-3060-4cc8-9cd7-c58af224544c" width="121">
											<detailCell height="24">
												<element kind="textField" uuid="27764c54-0bc3-492e-b509-a196dcd7a0b4" stretchType="ContainerHeight" x="0" y="0" width="121" height="24" fontName="IPAexMincho" fontSize="11.0" hTextAlign="Center">
													<expression><![CDATA[$F{svJigyoKnj}]]></expression>
													<property name="com.jaspersoft.studio.unit.width" value="px"/>
													<box padding="3"/>
												</element>
												<property name="com.jaspersoft.studio.unit.width" value="px"/>
												<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
												<box>
													<pen lineWidth="1.0"/>
												</box>
											</detailCell>
											<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
										</column>
										<column kind="single" uuid="0dedf819-f345-4cf9-913b-2e8e5f4dae14" width="98">
											<detailCell height="24" rowSpan="1">
												<element kind="textField" uuid="e729dde6-579d-45e2-81a5-83c0941b2fd9" stretchType="ContainerHeight" x="0" y="0" width="98" height="24" fontName="IPAexMincho" fontSize="11.0" hTextAlign="Center">
													<expression><![CDATA[$F{svShubetuKnj}]]></expression>
													<box padding="3"/>
												</element>
												<property name="com.jaspersoft.studio.unit.width" value="px"/>
												<box>
													<pen lineWidth="1.0"/>
												</box>
											</detailCell>
											<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
										</column>
										<column kind="single" uuid="a026207a-df4c-49df-988f-4a2952970389" width="73">
											<detailCell height="24">
												<element kind="textField" uuid="97dd0ab8-35c3-4597-af79-3104268ee1bd" stretchType="ContainerHeight" x="0" y="0" width="73" height="24" fontName="IPAexMincho" fontSize="11.0" hTextAlign="Center">
													<expression><![CDATA[$F{svJigyoKnj}]]></expression>
													<box padding="3"/>
												</element>
												<property name="com.jaspersoft.studio.unit.width" value="px"/>
												<box>
													<pen lineWidth="1.0"/>
												</box>
											</detailCell>
											<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column5"/>
										</column>
										<column kind="single" uuid="ddd63f29-9af9-40b5-9cb8-6b4f779f4430" width="74">
											<detailCell height="24">
												<element kind="textField" uuid="cd72a09a-**************-5f68036b25c8" stretchType="ContainerHeight" x="0" y="0" width="74" height="24" fontName="IPAexMincho" fontSize="11.0" hTextAlign="Center">
													<expression><![CDATA[$F{kikanKnj}]]></expression>
													<property name="com.jaspersoft.studio.unit.width" value="px"/>
													<box padding="3"/>
												</element>
												<property name="com.jaspersoft.studio.unit.width" value="px"/>
												<box>
													<pen lineWidth="1.0"/>
												</box>
											</detailCell>
											<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column6"/>
										</column>
									</component>
									<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
									<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
									<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
									<property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
									<property name="com.jaspersoft.layout.grid.x" value="-1"/>
									<property name="com.jaspersoft.layout.grid.y" value="-1"/>
									<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
									<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
									<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<pen lineWidth="1.0" lineColor="#000000"/>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
				<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
				<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
				<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<element kind="component" uuid="af2a5d93-bf7c-4bc7-afb4-a488b046b83e" stretchType="ElementGroupHeight" x="2" y="10" width="776" height="68">
				<printWhenExpression><![CDATA[$F{stFlg} == 1]]></printWhenExpression>
				<component kind="table">
					<datasetRun uuid="de4f47e5-72ba-4954-ac00-55626b9ebf4c" subDataset="Dataset1">
						<dataSourceExpression><![CDATA[$F{dataList2}]]></dataSourceExpression>
						<parameter name="service1Label">
							<expression><![CDATA[$F{service1Label}]]></expression>
						</parameter>
						<parameter name="service2Label">
							<expression><![CDATA[$F{service2Label}]]></expression>
						</parameter>
						<parameter name="service3Label">
							<expression><![CDATA[$F{service3Label}]]></expression>
						</parameter>
						<parameter name="informalKnjLabel">
							<expression><![CDATA[$F{informalKnjLabel}]]></expression>
						</parameter>
						<parameter name="shienJigyoLabel">
							<expression><![CDATA[$F{shienJigyoLabel}]]></expression>
						</parameter>
						<parameter name="service1Flg">
							<expression><![CDATA[$F{service1Flg}]]></expression>
						</parameter>
						<parameter name="service2Flg">
							<expression><![CDATA[$F{service2Flg}]]></expression>
						</parameter>
						<parameter name="h21Flg">
							<expression><![CDATA[$F{h21Flg}]]></expression>
						</parameter>
						<parameter name="printFont">
							<expression><![CDATA[$F{printFont}]]></expression>
						</parameter>
						<parameter name="ulFlg">
							<expression><![CDATA[$F{ulFlg}]]></expression>
						</parameter>
					</datasetRun>
					<detail splitType="Stretch"/>
					<column kind="single" uuid="5d4ed0c2-e41d-4e5d-8497-9a931bdd57db" width="775">
						<columnHeader height="48" rowSpan="1">
							<element kind="staticText" uuid="83456ebc-7317-4d27-aa87-2e597d50ed4b" x="153" y="14" width="135" height="34" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[目標についての
支援のポイント]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<box>
									<pen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="a23dc386-7dfd-411f-92b5-f4862c58309b" x="153" y="0" width="620" height="14" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[支援計画]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="55a0389a-e982-4376-8e40-e6bcef71c77d" x="288" y="14" width="121" height="34" fontName="IPAexGothic" fontSize="9.0" blankWhenNull="true" hTextAlign="Center">
								<expression><![CDATA[$P{informalKnjLabel}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<element kind="textField" uuid="227b5a3c-c4dd-40a7-af06-6263f6a9dda9" x="409" y="14" width="121" height="12" fontName="IPAexGothic" fontSize="9.0" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$P{service1Label}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="34a93f47-adf7-4367-bb43-3379ba436dbd" x="409" y="25" width="121" height="12" fontName="IPAexGothic" fontSize="9.0" blankWhenNull="true" hTextAlign="Center" vTextAlign="Top">
								<expression><![CDATA[$P{service2Label}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="1a8adc53-b01c-46a4-a4ad-e81175db3f0d" x="409" y="36" width="121" height="12" fontName="IPAexGothic" fontSize="9.0" blankWhenNull="true" hTextAlign="Center" vTextAlign="Top">
								<expression><![CDATA[$P{service3Label}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="cb27abef-c8f8-450d-b8ba-8a9667896e59" x="530" y="14" width="98" height="34" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[サービス種別]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<pen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="7bf7b333-26ec-4b5e-9d4d-0a37be0d48cb" x="628" y="14" width="73" height="34" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[事業所
(利用先)]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<pen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="03146ca4-891c-45ce-b0d5-69a2e542b2e0" x="701" y="14" width="72" height="34" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[期間]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<pen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="a4edf3d8-3886-4492-b7e1-b1eca044fbfa" x="0" y="0" width="153" height="48" fontName="IPAexGothic" fontSize="11.0" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[目標]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<element kind="rectangle" uuid="39000423-e9dd-45da-97fa-bfa6298b4254" x="420" y="14" width="100" height="12" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
								<printWhenExpression><![CDATA[$P{service1Flg}==1 ?true:false]]></printWhenExpression>
								<property name="com.jaspersoft.studio.element.name" value="サービス1_丸ラベル"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
							</element>
							<element kind="rectangle" uuid="5e906d4e-81f4-4776-a442-e2b77845e0fc" x="420" y="27" width="100" height="20" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
								<printWhenExpression><![CDATA[$P{service2Flg}==1 &&$P{h21Flg}==1  ?true:false]]></printWhenExpression>
								<property name="com.jaspersoft.studio.element.name" value="サービス2サービス3_丸ラベル"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
							</element>
							<element kind="rectangle" uuid="5f0aea4f-14dc-46b4-9d8c-35d50fe69bf9" x="419" y="35" width="100" height="12" backcolor="rgba(255, 255, 255, 0.0)" radius="5">
								<printWhenExpression><![CDATA[$P{service2Flg}==1 &&$P{h21Flg}==0  ?true:false]]></printWhenExpression>
								<property name="com.jaspersoft.studio.element.name" value="サービス3_丸ラベル"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
							</element>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<pen lineWidth="1.0"/>
								<bottomPen lineWidth="1.0"/>
							</box>
						</columnHeader>
						<detailCell height="24">
							<element kind="subreport" uuid="deb73509-3419-414f-abaf-676c6298f27d" stretchType="ContainerHeight" x="0" y="0" width="775" height="24"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box>
								<pen lineColor="#000000"/>
							</box>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
				<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
				<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
				<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="41">
			<element kind="staticText" uuid="3b30b03b-c727-479e-b937-f6e7dd944c99" positionType="FixRelativeToBottom" x="3" y="3" width="385" height="14" fontName="IPAexGothic" fontSize="12.0" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[本来行うべき支援が実施できない場合：妥当な支援の実施に向けた方針]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="6d936610-e1da-49d7-a96f-ec5b52ad0667" positionType="FixRelativeToBottom" x="390" y="3" width="220" height="14" fontName="IPAexGothic" fontSize="12.0" hTextAlign="Left">
				<text><![CDATA[総合的な方針：改善・予防のポイント]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="65abee05-1ff2-482a-8c7e-5f651ffc9656" x="3" y="17" width="381" height="24" fontName="IPAexMincho" fontSize="11.0">
				<expression><![CDATA[$F{datoHoshinKnj}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="2d699c94-7c85-4966-882c-3ca096994600" x="390" y="17" width="386" height="24" fontName="IPAexMincho" fontSize="11.0">
				<expression><![CDATA[$F{sogoHoshinKnj}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="110">
			<element kind="frame" uuid="3ab1f60c-4b61-420b-83a0-d6461f5e4bc4" stretchType="ElementGroupHeight" x="425" y="3" width="350" height="96">
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="staticText" uuid="334b1008-2c2a-4f1b-8e21-a3c071fd4d40" x="0" y="0" width="350" height="14" fontName="IPAexGothic" fontSize="12.0" hTextAlign="Left" vTextAlign="Middle">
					<text><![CDATA[計画に関する同意]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="9b3712de-166d-49b2-a1ad-890fa7fa0125" x="0" y="14" width="350" height="72">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<element kind="textField" uuid="16c422bf-a8c7-4945-9a90-c179c8a12e16" stretchType="ElementGroupHeight" x="3" y="1" width="347" height="11" fontName="IPAexGothic" fontSize="11.0" textAdjust="StretchHeight" blankWhenNull="true">
						<expression><![CDATA[$F{doiContent1}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="textField" uuid="89a25a82-0e66-48ab-bad3-1ba0e5de8d6d" stretchType="ElementGroupHeight" x="3" y="12" width="347" height="11" fontName="IPAexGothic" fontSize="11.0" textAdjust="StretchHeight" blankWhenNull="true">
						<expression><![CDATA[$F{doiContent2}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="textField" uuid="4d42fc39-9783-44ac-a9c3-b006257a53c0" stretchType="ElementGroupHeight" x="183" y="34" width="162" height="12" fontName="IPAexGothic" fontSize="12.0" textAdjust="StretchHeight" blankWhenNull="true" hTextAlign="Right">
						<expression><![CDATA[$F{doiYmd}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="textField" uuid="76df3ebf-d14b-43fe-a7c3-6d4bc27dbac3" stretchType="ElementGroupHeight" x="163" y="49" width="165" height="11" fontName="IPAexMincho" fontSize="11.0" textAdjust="StretchHeight" blankWhenNull="true" hTextAlign="Left">
						<expression><![CDATA[$F{doiKnj}]]></expression>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="staticText" uuid="720af28a-3db6-45f9-bf37-41d2565c61b5" x="136" y="49" width="30" height="14" fontName="IPAexGothic" fontSize="11.0">
						<text><![CDATA[氏名]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</element>
					<element kind="staticText" uuid="047ad416-93d2-48d4-bfff-0c0806cd89fb" x="328" y="49" width="19" height="14" fontName="IPAexGothic" fontSize="11.0">
						<text><![CDATA[印]]></text>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</element>
					<element kind="line" uuid="8869a699-0cb5-48e1-b645-9b64e5198c10" x="132" y="67" width="215" height="1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</element>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
			</element>
			<element kind="frame" uuid="31b88ee7-286e-4e56-b874-1437733f544b" stretchType="ElementGroupHeight" x="3" y="17" width="381" height="44">
				<printWhenExpression><![CDATA[$F{r3Flg} != 1]]></printWhenExpression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="ShowOutOfBoundContent" value="false"/>
				<element kind="staticText" uuid="d9443976-dae8-44d4-bfe3-3d1ac96134b0" stretchType="ElementGroupHeight" x="0" y="0" width="381" height="16" fontName="IPAexMincho" printWhenDetailOverflows="true" hTextAlign="Left" vTextAlign="Top">
					<text><![CDATA[【意見】]]></text>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="textField" uuid="ee6fd320-4765-48e4-aa7c-776e9299f593" stretchType="ElementGroupHeight" x="0" y="16" width="381" height="28" fontName="IPAexMincho" fontSize="11.0" textAdjust="StretchHeight" blankWhenNull="true">
					<expression><![CDATA[$F{centerIkenKnj}]]></expression>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="frame" uuid="27bfd8e8-0aa5-4042-b58b-b21f863bb0d6" positionType="Float" stretchType="ElementGroupHeight" x="3" y="61" width="381" height="28">
				<printWhenExpression><![CDATA[$F{r3Flg} != 1]]></printWhenExpression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="ShowOutOfBoundContent" value="false"/>
				<element kind="textField" uuid="439927d3-1d21-434c-a6e0-b3c7d0d0e9b7" stretchType="ElementGroupHeight" x="1" y="1" width="380" height="27" fontName="IPAexMincho" fontSize="10.0" textAdjust="StretchHeight" blankWhenNull="true">
					<expression><![CDATA[$F{kakuninLabel}]]></expression>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
		</band>
		<band height="168"/>
	</detail>
	<pageFooter height="30" splitType="Stretch">
		<element kind="textField" uuid="06071b26-c3e2-4fb1-aefd-872b0b1cdc20" x="273" y="10" width="100" height="20" hTextAlign="Right">
			<expression><![CDATA[$V{PAGE_NUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="9c2193ca-0f2a-4079-b158-c200d6adde39" x="373" y="10" width="100" height="20" evaluationTime="Report" hTextAlign="Left">
			<expression><![CDATA["/ " + $V{PAGE_NUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
