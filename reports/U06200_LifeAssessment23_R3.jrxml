<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="U06026_生活アセスメント(2)(3)_R3" language="java" pageWidth="595" pageHeight="842" columnWidth="521" leftMargin="43" rightMargin="31" topMargin="13" bottomMargin="6" uuid="8590fb34-419e-4492-a0ab-4491f2a909dd" titleNewPage="true" summaryWithPageHeaderAndFooter="true">
	<field name="name1Knj" class="java.lang.String">
		<description><![CDATA[name1Knj]]></description>
	</field>
	<field name="name2Knj" class="java.lang.String">
		<description><![CDATA[name2Knj]]></description>
	</field>
	<field name="shiTeiDateGG" class="java.lang.String">
		<description><![CDATA[shiTeiDateGG]]></description>
	</field>
	<field name="shiTeiKubun" class="java.lang.Integer">
		<description><![CDATA[shiTeiKubun]]></description>
	</field>
	<field name="famDraw" class="java.lang.String">
		<description><![CDATA[famDraw]]></description>
	</field>
	<field name="memoKnj" class="java.lang.String">
		<description><![CDATA[memoKnj]]></description>
	</field>
	<field name="kaigo1" class="java.lang.String">
		<description><![CDATA[kaigo1]]></description>
	</field>
	<field name="kaigo2" class="java.lang.String">
		<description><![CDATA[kaigo2]]></description>
	</field>
	<field name="kaigo3" class="java.lang.String">
		<description><![CDATA[kaigo3]]></description>
	</field>
	<field name="kaigo4" class="java.lang.String">
		<description><![CDATA[kaigo4]]></description>
	</field>
	<field name="kaigo5" class="java.lang.String">
		<description><![CDATA[kaigo5]]></description>
	</field>
	<field name="family1NameKnj" class="java.lang.String">
		<description><![CDATA[family1NameKnj]]></description>
	</field>
	<field name="family2NameKnj" class="java.lang.String">
		<description><![CDATA[family2NameKnj]]></description>
	</field>
	<field name="family3NameKnj" class="java.lang.String">
		<description><![CDATA[family3NameKnj]]></description>
	</field>
	<field name="family4NameKnj" class="java.lang.String">
		<description><![CDATA[family4NameKnj]]></description>
	</field>
	<field name="family5NameKnj" class="java.lang.String">
		<description><![CDATA[family5NameKnj]]></description>
	</field>
	<field name="zokugara1Knj" class="java.lang.String">
		<description><![CDATA[zokugara1Knj]]></description>
	</field>
	<field name="zokugara2Knj" class="java.lang.String">
		<description><![CDATA[zokugara2Knj]]></description>
	</field>
	<field name="zokugara3Knj" class="java.lang.String">
		<description><![CDATA[zokugara3Knj]]></description>
	</field>
	<field name="zokugara4Knj" class="java.lang.String">
		<description><![CDATA[zokugara4Knj]]></description>
	</field>
	<field name="zokugara5Knj" class="java.lang.String">
		<description><![CDATA[zokugara5Knj]]></description>
	</field>
	<field name="doukyo1" class="java.lang.String">
		<description><![CDATA[doukyo1]]></description>
	</field>
	<field name="doukyo2" class="java.lang.String">
		<description><![CDATA[doukyo2]]></description>
	</field>
	<field name="doukyo3" class="java.lang.String">
		<description><![CDATA[doukyo3]]></description>
	</field>
	<field name="doukyo4" class="java.lang.String">
		<description><![CDATA[doukyo4]]></description>
	</field>
	<field name="doukyo5" class="java.lang.String">
		<description><![CDATA[doukyo5]]></description>
	</field>
	<field name="shokugyo1" class="java.lang.String">
		<description><![CDATA[shokugyo1]]></description>
	</field>
	<field name="shokugyo2" class="java.lang.String">
		<description><![CDATA[shokugyo2]]></description>
	</field>
	<field name="shokugyo3" class="java.lang.String">
		<description><![CDATA[shokugyo3]]></description>
	</field>
	<field name="shokugyo4" class="java.lang.String">
		<description><![CDATA[shokugyo4]]></description>
	</field>
	<field name="shokugyo5" class="java.lang.String">
		<description><![CDATA[shokugyo5]]></description>
	</field>
	<field name="kenkou1Knj" class="java.lang.String">
		<description><![CDATA[kenkou1Knj]]></description>
	</field>
	<field name="kenkou2Knj" class="java.lang.String">
		<description><![CDATA[kenkou2Knj]]></description>
	</field>
	<field name="kenkou3Knj" class="java.lang.String">
		<description><![CDATA[kenkou3Knj]]></description>
	</field>
	<field name="kenkou4Knj" class="java.lang.String">
		<description><![CDATA[kenkou4Knj]]></description>
	</field>
	<field name="kenkou5Knj" class="java.lang.String">
		<description><![CDATA[kenkou5Knj]]></description>
	</field>
	<field name="tokki1Knj" class="java.lang.String">
		<description><![CDATA[tokki1Knj]]></description>
	</field>
	<field name="tokki2Knj" class="java.lang.String">
		<description><![CDATA[tokki2Knj]]></description>
	</field>
	<field name="tokki3Knj" class="java.lang.String">
		<description><![CDATA[tokki3Knj]]></description>
	</field>
	<field name="tokki4Knj" class="java.lang.String">
		<description><![CDATA[tokki4Knj]]></description>
	</field>
	<field name="tokki5Knj" class="java.lang.String">
		<description><![CDATA[tokki5Knj]]></description>
	</field>
	<field name="teikyoNameKnj" class="java.lang.String">
		<description><![CDATA[teikyoNameKnj]]></description>
	</field>
	<field name="naiyo1Knj" class="java.lang.String">
		<description><![CDATA[naiyo1Knj]]></description>
	</field>
	<field name="sienTokkiKnj" class="java.lang.String">
		<description><![CDATA[sienTokkiKnj]]></description>
	</field>
	<field name="naiyo2Knj" class="java.lang.String">
		<description><![CDATA[naiyo2Knj]]></description>
	</field>
	<field name="teikyoName2Knj" class="java.lang.String">
		<description><![CDATA[teikyoName2Knj]]></description>
	</field>
	<field name="sienTokki2Knj" class="java.lang.String">
		<description><![CDATA[sienTokki2Knj]]></description>
	</field>
	<field name="serYmdGG" class="java.lang.String">
		<description><![CDATA[serYmdGG]]></description>
	</field>
	<field name="ser1Cd" class="java.lang.Integer">
		<description><![CDATA[ser1Cd]]></description>
	</field>
	<field name="ser38Cd" class="java.lang.Integer">
		<description><![CDATA[ser38Cd]]></description>
	</field>
	<field name="ser2Cd" class="java.lang.Integer">
		<description><![CDATA[ser2Cd]]></description>
	</field>
	<field name="ser3Cd" class="java.lang.Integer">
		<description><![CDATA[ser3Cd]]></description>
	</field>
	<field name="ser4Cd" class="java.lang.Integer">
		<description><![CDATA[ser4Cd]]></description>
	</field>
	<field name="ser5Cd" class="java.lang.Integer">
		<description><![CDATA[ser5Cd]]></description>
	</field>
	<field name="ser6Cd" class="java.lang.Integer">
		<description><![CDATA[ser6Cd]]></description>
	</field>
	<field name="ser39Cd" class="java.lang.Integer">
		<description><![CDATA[ser39Cd]]></description>
	</field>
	<field name="ser7Cd" class="java.lang.Integer">
		<description><![CDATA[ser7Cd]]></description>
	</field>
	<field name="ser10Cd" class="java.lang.Integer">
		<description><![CDATA[ser10Cd]]></description>
	</field>
	<field name="ser11Cd" class="java.lang.Integer">
		<description><![CDATA[ser11Cd]]></description>
	</field>
	<field name="ser13Cd" class="java.lang.Integer">
		<description><![CDATA[ser13Cd]]></description>
	</field>
	<field name="ser36Cd" class="java.lang.Integer">
		<description><![CDATA[ser36Cd]]></description>
	</field>
	<field name="ser9Cd" class="java.lang.Integer">
		<description><![CDATA[ser9Cd]]></description>
	</field>
	<field name="ser12Cd" class="java.lang.Integer">
		<description><![CDATA[ser12Cd]]></description>
	</field>
	<field name="ser14Cd" class="java.lang.Integer">
		<description><![CDATA[ser14Cd]]></description>
	</field>
	<field name="ser15Cd" class="java.lang.Integer">
		<description><![CDATA[ser15Cd]]></description>
	</field>
	<field name="ser31Cd" class="java.lang.Integer">
		<description><![CDATA[ser31Cd]]></description>
	</field>
	<field name="ser32Cd" class="java.lang.Integer">
		<description><![CDATA[ser32Cd]]></description>
	</field>
	<field name="ser33Cd" class="java.lang.Integer">
		<description><![CDATA[ser33Cd]]></description>
	</field>
	<field name="ser37Cd" class="java.lang.Integer">
		<description><![CDATA[ser37Cd]]></description>
	</field>
	<field name="ser40Cd" class="java.lang.Integer">
		<description><![CDATA[ser40Cd]]></description>
	</field>
	<field name="kaisu1" class="java.lang.String">
		<description><![CDATA[kaisu1]]></description>
	</field>
	<field name="kaisu38" class="java.lang.String">
		<description><![CDATA[kaisu38]]></description>
	</field>
	<field name="kaisu2" class="java.lang.String">
		<description><![CDATA[kaisu2]]></description>
	</field>
	<field name="kaisu3" class="java.lang.String">
		<description><![CDATA[kaisu3]]></description>
	</field>
	<field name="kaisu4" class="java.lang.String">
		<description><![CDATA[kaisu4]]></description>
	</field>
	<field name="kaisu5" class="java.lang.String">
		<description><![CDATA[kaisu5]]></description>
	</field>
	<field name="kaisu6" class="java.lang.String">
		<description><![CDATA[kaisu6]]></description>
	</field>
	<field name="kaisu39" class="java.lang.String">
		<description><![CDATA[kaisu39]]></description>
	</field>
	<field name="kaisu7" class="java.lang.String">
		<description><![CDATA[kaisu7]]></description>
	</field>
	<field name="kaisu9" class="java.lang.String">
		<description><![CDATA[kaisu9]]></description>
	</field>
	<field name="kaisu10" class="java.lang.String">
		<description><![CDATA[kaisu10]]></description>
	</field>
	<field name="kaisu12" class="java.lang.String">
		<description><![CDATA[kaisu12]]></description>
	</field>
	<field name="kaisu36" class="java.lang.String">
		<description><![CDATA[kaisu36]]></description>
	</field>
	<field name="kaisu8" class="java.lang.String">
		<description><![CDATA[kaisu8]]></description>
	</field>
	<field name="kaisu13" class="java.lang.String">
		<description><![CDATA[kaisu13]]></description>
	</field>
	<field name="kaisu15" class="java.lang.String">
		<description><![CDATA[kaisu15]]></description>
	</field>
	<field name="kaisu31" class="java.lang.String">
		<description><![CDATA[kaisu31]]></description>
	</field>
	<field name="kaisu32" class="java.lang.String">
		<description><![CDATA[kaisu32]]></description>
	</field>
	<field name="kaisu33" class="java.lang.String">
		<description><![CDATA[kaisu33]]></description>
	</field>
	<field name="kaisu11" class="java.lang.String">
		<description><![CDATA[kaisu11]]></description>
	</field>
	<field name="kaisu37" class="java.lang.String">
		<description><![CDATA[kaisu37]]></description>
	</field>
	<field name="kaisu40" class="java.lang.String">
		<description><![CDATA[kaisu40]]></description>
	</field>
	<field name="ser40NameKnj" class="java.lang.String">
		<description><![CDATA[ser40NameKnj]]></description>
	</field>
	<field name="bunsyoKanriNo" class="java.lang.String">
		<description><![CDATA[bunsyoKanriNo]]></description>
	</field>
	<field name="shiTeiDateYY" class="java.lang.String">
		<description><![CDATA[shiTeiDateYY]]></description>
	</field>
	<field name="shiTeiDateMM" class="java.lang.String">
		<description><![CDATA[shiTeiDateMM]]></description>
	</field>
	<field name="shiTeiDateDD" class="java.lang.String">
		<description><![CDATA[shiTeiDateDD]]></description>
	</field>
	<field name="emptyFlg" class="java.lang.Boolean">
		<description><![CDATA[emptyFlg]]></description>
	</field>
	<field name="serYmdYY" class="java.lang.String">
		<description><![CDATA[serYmdYY]]></description>
	</field>
	<field name="serYmdMM" class="java.lang.String">
		<description><![CDATA[serYmdMM]]></description>
	</field>
	<field name="serYmdDD" class="java.lang.String">
		<description><![CDATA[serYmdDD]]></description>
	</field>
	<pageHeader height="12" splitType="Stretch">
		<element kind="staticText" uuid="82a8ee38-68d5-4422-b82a-f2f11a3f914c" x="0" y="0" width="68" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<text><![CDATA[利用者氏名：]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="02cd0468-07ab-43fa-979f-71243795be01" x="70" y="0" width="290" height="11" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<expression><![CDATA[$F{name1Knj} + " " + $F{name2Knj}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<element kind="textField" uuid="7726581b-d5ad-4429-ae14-d1ae057cbfbc" x="412" y="0" width="100" height="12" markup="html" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
			<printWhenExpression><![CDATA[($F{shiTeiKubun} != 1) && (!$F{emptyFlg})]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDateGG}+
"<font style='font-size:9pt'>"+$F{shiTeiDateYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{shiTeiDateMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{shiTeiDateDD}+"</font>"+
"日"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="776" splitType="Stretch">
			<element kind="staticText" uuid="96fa8f03-344e-4bec-b840-dd67d3918104" mode="Opaque" x="0" y="0" width="22" height="22" forecolor="#FFFFFF" backcolor="#080101" fontName="IPAexGothic" fontSize="22.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[2]]></text>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="staticText" uuid="655069c1-35de-454d-b91c-29835dd533f7" x="25" y="6" width="280" height="16" fontName="IPAexGothic" fontSize="15.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[家族状況とインフォーマルな支援の状況]]></text>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="staticText" uuid="2a6295ca-1d68-44f5-a6cb-126e4149d9f6" x="0" y="25" width="160" height="16" fontName="IPAexGothic" fontSize="13.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[■家族構成と介護状況]]></text>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="frame" uuid="f6ba082f-3a0c-4f34-aec8-f805a7ff596b" x="0" y="42" width="520" height="377">
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<element kind="frame" uuid="c9921585-eea0-4b04-83e9-ae3a351be8eb" x="0" y="0" width="520" height="174">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="frame" uuid="74f7c45e-c8c3-4b5c-951e-466348f03594" x="0" y="0" width="520" height="14">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="frame" uuid="f0e14fa8-7502-431f-951b-e31689001692" x="0" y="0" width="213" height="14">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<element kind="staticText" uuid="d81bfb08-cba7-4e88-8b49-350421874b3a" mode="Opaque" x="0" y="0" width="213" height="14" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[家族構成図]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<box>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="frame" uuid="c8f3baab-b7fc-43bc-bc28-59378247479b" x="213" y="0" width="307" height="14">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<element kind="staticText" uuid="03346437-c9c6-4c78-8efb-3f7361cc833e" mode="Opaque" x="0" y="0" width="307" height="14" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[家族の介護の状況･課題]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<box>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<box>
							<pen lineWidth="1.0"/>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="2a1de218-4fe3-4e9c-b079-826b59a88254" x="0" y="14" width="520" height="160">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="frame" uuid="a096f18f-0485-4b4c-bf8b-3ca7f607205c" x="0" y="0" width="213" height="160">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<pen lineWidth="0.0"/>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="frame" uuid="23bb40e2-1c98-4099-94b8-ec9ea74b78ee" x="213" y="0" width="307" height="160">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<element kind="textField" uuid="e24153a6-05c2-4c6a-8e79-7c75dfea2ae3" x="8" y="2" width="296" height="158" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self">
								<expression><![CDATA[$F{memoKnj}]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<pen lineWidth="1.0"/>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<box>
								<pen lineWidth="1.0"/>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<box>
							<leftPen lineWidth="0.0"/>
							<rightPen lineWidth="0.0"/>
						</box>
					</element>
				</element>
				<element kind="frame" uuid="7892d4a6-0380-402b-8103-b6a50df5f5e8" x="0" y="174" width="520" height="203">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<element kind="frame" uuid="28f4424b-923a-42d1-b710-080a35890578" x="0" y="0" width="109" height="203">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="frame" uuid="c79918ca-068a-414c-ad62-5ffd7d89992d" x="0" y="0" width="109" height="15">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<element kind="staticText" uuid="bd526eaa-e94a-4939-9734-825ea0ed15c6" mode="Opaque" x="0" y="0" width="109" height="15" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[氏名(主たる介護者には※)]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								<box>
									<pen lineWidth="0.0"/>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<box>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="frame" uuid="329f7dcd-37dc-49f8-b817-f74ae84efbe8" x="0" y="15" width="109" height="188">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
							<element kind="frame" uuid="1347cf3e-9495-4563-b2f2-09fd2cf0375c" x="0" y="0" width="109" height="38">
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="0"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="517dc4a7-53e7-4e28-9f06-3d5c69a0acd1" x="2" y="12" width="14" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<expression><![CDATA[$F{kaigo1}
]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="textField" uuid="9efe5a47-42bf-44ec-80d2-37a91ad0ae09" x="18" y="12" width="90" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
									<expression><![CDATA[$F{family1NameKnj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="5346726e-162c-44fe-a488-f38a11c09749" x="0" y="38" width="109" height="38">
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="f908b049-f7f5-4f64-8863-1e8d41247166" x="2" y="12" width="14" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<expression><![CDATA[$F{kaigo2}
]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="textField" uuid="85eeebf7-4504-4efd-8542-57a7188f87eb" x="18" y="12" width="90" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
									<expression><![CDATA[$F{family2NameKnj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="2ce429f7-86ae-4a1a-a0e5-c549841c4b32" x="0" y="76" width="109" height="38">
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="2"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="ab79e381-4279-4b4e-b578-037968632ad6" x="2" y="12" width="14" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<expression><![CDATA[$F{kaigo3}
]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="textField" uuid="2dd0aff2-cd45-4d86-a106-7081e94851e7" x="18" y="12" width="90" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
									<expression><![CDATA[$F{family3NameKnj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="8917a942-eb76-4f6c-bbdf-38e263deda86" x="0" y="114" width="109" height="37">
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="3"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="d00ceaaf-b190-4508-bad4-02613e789486" x="2" y="12" width="14" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<expression><![CDATA[$F{kaigo4}
]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="textField" uuid="8f08c843-0728-46b9-831a-328047cb653d" x="18" y="12" width="90" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
									<expression><![CDATA[$F{family4NameKnj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
								<box>
									<pen lineWidth="0.0"/>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="6a79a09e-7833-46bf-a902-9213aeb527d1" x="0" y="151" width="109" height="37">
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="4"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="ebd1dbaf-b49b-4e4a-8a42-6ddc4049d460" x="2" y="12" width="14" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<expression><![CDATA[$F{kaigo5}
]]></expression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="textField" uuid="a14926d3-7129-4da0-b988-b86ef62f91fc" x="18" y="12" width="90" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
									<expression><![CDATA[$F{family5NameKnj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
								</element>
							</element>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="02f4b4cc-9583-4831-a7ed-42357c54c428" x="109" y="0" width="59" height="203">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="frame" uuid="cc35f6c0-691b-4684-b12b-6056b98392b4" x="0" y="0" width="59" height="15">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<element kind="staticText" uuid="07cb5f6b-955f-43e3-b7e9-5ba5ffc2fea9" mode="Opaque" x="0" y="0" width="59" height="15" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[続柄]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<box>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="frame" uuid="62fd52f6-9529-4fa6-8a5a-d3b0a84b26e4" x="0" y="15" width="59" height="188">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
							<element kind="frame" uuid="8e49c0cb-5c8a-4e50-ac53-c093ba2313de" x="0" y="0" width="59" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="0"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="331677a7-71a4-464e-9d8a-38ec9fa79f90" x="2" y="3" width="55" height="26" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{zokugara1Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="0cd9747c-0cb1-49a8-b172-89f5f2cd2171" x="0" y="38" width="59" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="783291a4-4884-4ffa-a76a-5522a6dcf39a" x="2" y="3" width="55" height="26" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{zokugara2Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="d6fac51c-4117-4a64-8bb1-2006d74da8be" x="0" y="76" width="59" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="2"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="ed89f1af-a4cc-4001-8ebe-b9a964e12307" x="2" y="3" width="55" height="26" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{zokugara3Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="375828c8-6ef2-4ea8-b5a9-9706750174df" x="0" y="114" width="59" height="37">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="3"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="ada915e3-6795-488e-9c9f-c47bc2b24457" x="2" y="3" width="55" height="26" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{zokugara4Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="70792cc1-d27c-4070-893d-3ca789362f7a" x="0" y="151" width="59" height="37">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="4"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="ea37d628-2d80-4341-9c1f-100b716ddccf" x="2" y="3" width="55" height="26" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{zokugara5Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
							</element>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="106b000e-734b-4546-a6d3-f7fa764970f5" x="168" y="0" width="45" height="203">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="frame" uuid="a20f9130-5d83-4931-b935-87ee784c8a57" x="0" y="0" width="45" height="15">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<element kind="staticText" uuid="3b4b8714-b5ad-4409-9279-4db3fc359c06" mode="Opaque" x="0" y="0" width="45" height="15" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[同別居]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<pen lineWidth="0.0"/>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<box>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="frame" uuid="37fd5086-fbd5-437f-a552-55da20089b41" x="0" y="15" width="45" height="188">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
							<element kind="frame" uuid="fb3c2e38-2da1-435d-bf1d-84e76b44973f" x="0" y="0" width="45" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="0"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="staticText" uuid="cf4065fd-b4cb-43a4-a6a2-f19fdd33d01e" x="7" y="12" width="30" height="14" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[同・別]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="ellipse" uuid="1f2471ab-37c4-4bca-a11c-613e49f07ba6" mode="Transparent" x="6" y="12" width="14" height="14" forecolor="#000000">
									<printWhenExpression><![CDATA[$F{doukyo1}.equals("1")?true:false]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="ellipse" uuid="bd34464f-4767-4e4d-9dae-59d223621eb5" mode="Transparent" x="24" y="12" width="14" height="14" forecolor="#000000">
									<printWhenExpression><![CDATA[$F{doukyo1}.equals("2")?true:false]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="abc9bce5-1d29-49f7-bd2f-a1b01b635477" x="0" y="38" width="45" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="staticText" uuid="b3bd7cc2-fd3c-4a0c-a7ec-f5b2fb75b13d" x="7" y="12" width="30" height="14" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[同・別]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="ellipse" uuid="6dcd4ac1-fadd-4e18-a0e8-76fd2f910f2d" mode="Transparent" x="6" y="12" width="14" height="14" forecolor="#000000">
									<printWhenExpression><![CDATA[$F{doukyo2}.equals("1")?true:false]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="ellipse" uuid="0afd2d5a-391d-4b9e-9fa0-5341cb7c665e" mode="Transparent" x="24" y="12" width="14" height="14" forecolor="#000000">
									<printWhenExpression><![CDATA[$F{doukyo2}.equals("2")?true:false]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="3b982323-fe4d-4855-a1ae-ac14c1be06f2" x="0" y="76" width="45" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="2"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="staticText" uuid="13a45e21-5020-4e87-a572-30842562b45a" x="7" y="12" width="30" height="14" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[同・別]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="ellipse" uuid="0d58b4b8-2db4-495c-913e-b826ef0d274c" mode="Transparent" x="6" y="12" width="14" height="14" forecolor="#000000">
									<printWhenExpression><![CDATA[$F{doukyo3}.equals("1")?true:false]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="ellipse" uuid="f8e32001-c050-430c-a316-1eb7208640a5" mode="Transparent" x="24" y="12" width="14" height="14" forecolor="#000000">
									<printWhenExpression><![CDATA[$F{doukyo3}.equals("2")?true:false]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="3f61aede-9882-4c6b-b40e-de605bef78e0" x="0" y="114" width="45" height="37">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="3"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="staticText" uuid="5bb9f3a3-0e1b-45d8-93b7-f6a2ae7f4ec8" x="7" y="12" width="30" height="14" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[同・別]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="ellipse" uuid="33cf490c-f8cb-4460-b723-2db13f36972c" mode="Transparent" x="6" y="12" width="14" height="14" forecolor="#000000">
									<printWhenExpression><![CDATA[$F{doukyo4}.equals("1")?true:false]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="ellipse" uuid="2b059e87-ddae-4b98-a032-0df3c63e6a63" mode="Transparent" x="24" y="12" width="14" height="14" forecolor="#000000">
									<printWhenExpression><![CDATA[$F{doukyo4}.equals("2")?true:false]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="2fc92f3a-04f4-435f-9539-baa6e85c5a94" x="0" y="151" width="45" height="37">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="4"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="staticText" uuid="72853605-e6f6-4366-a50a-166f73019f8a" x="7" y="12" width="30" height="14" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[同・別]]></text>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="ellipse" uuid="56f0dab6-147f-432c-a63e-a4af462163cf" mode="Transparent" x="6" y="12" width="14" height="14" forecolor="#000000">
									<printWhenExpression><![CDATA[$F{doukyo5}.equals("1")?true:false]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<element kind="ellipse" uuid="8bada32c-8823-4bf8-b4db-c5a3a6efe047" mode="Transparent" x="24" y="12" width="14" height="14" forecolor="#000000">
									<printWhenExpression><![CDATA[$F{doukyo5}.equals("2")?true:false]]></printWhenExpression>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
							</element>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="7f43c116-b19f-4e94-802d-6cc44a741f02" x="213" y="0" width="48" height="203">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="frame" uuid="8e60b875-8f96-4778-9900-79aec91d635b" x="0" y="0" width="48" height="15">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<element kind="staticText" uuid="7468cb38-2174-47e2-8175-1fec91273ff6" mode="Opaque" x="0" y="0" width="48" height="15" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[就労の状況]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<box>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="frame" uuid="514e712e-02ea-4b0f-98b7-96ff0b6ef1a2" x="0" y="15" width="48" height="188">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
							<element kind="frame" uuid="bb89994d-6857-4309-8f7f-2c27f9617208" x="0" y="0" width="48" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="0"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="33b2da1f-cc56-4e27-8f87-d9776b833b7d" x="0" y="12" width="44" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<expression><![CDATA[$F{shokugyo1}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="bcfc45b0-5573-4dcf-9f85-beff6d2588d4" x="0" y="38" width="48" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="7ad0a0cb-fd70-4465-9b60-1b9d593d6c0a" x="0" y="12" width="44" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<expression><![CDATA[$F{shokugyo2}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="a60d5959-8e23-40a8-95f5-5c47ad52176b" x="0" y="76" width="48" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="2"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="594fed2b-193d-4698-b759-4979e6ceabb1" x="0" y="12" width="44" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<expression><![CDATA[$F{shokugyo3}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="bb674bdd-e60d-4b27-887a-eae061645081" x="0" y="114" width="48" height="37">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="3"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="5968012d-8048-4df1-a68c-4fcd9aa98dce" x="0" y="12" width="44" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<expression><![CDATA[$F{shokugyo4}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="eb980def-3d9c-4f4d-9843-04f31317ac1c" x="0" y="151" width="48" height="37">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="4"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="0f9f2abd-c6f6-4cf0-a2a6-8aad628ce51f" x="0" y="12" width="44" height="14" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<expression><![CDATA[$F{shokugyo5}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
							</element>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="f3cc057e-f88d-407b-98c4-6ea1fcc0de0a" x="261" y="0" width="93" height="203">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="frame" uuid="1689abd6-6048-49e7-9efb-dd009338074f" x="0" y="0" width="93" height="15">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<element kind="staticText" uuid="f0f6c81b-c2cf-4437-a501-16819887049f" mode="Opaque" x="0" y="0" width="93" height="15" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[健康状態等]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<box>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="frame" uuid="3cb55204-0db4-42fa-9220-97eae0429288" x="0" y="15" width="93" height="188">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
							<element kind="frame" uuid="e1691e73-ed63-4261-920f-8f523a784dee" x="0" y="0" width="93" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="0"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="04917eca-835d-4f48-bfd0-907b64b4854c" x="2" y="5" width="90" height="30" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{kenkou1Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="02c4ccb4-9116-478e-8999-afb0749a24cd" x="0" y="38" width="93" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="206b5bf4-54e9-4e77-ae86-58ab748defc9" x="2" y="5" width="90" height="30" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{kenkou2Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="c13d2897-9f0a-4e2f-8efc-bb78bb32642d" x="0" y="76" width="93" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="2"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="d8878d6a-9540-40ef-9f28-30a2e5df0843" x="2" y="5" width="90" height="30" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{kenkou3Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="a5186f83-02e2-4405-8c87-83452b0d2bfa" x="0" y="114" width="93" height="37">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="3"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="3e663a94-86be-48eb-abb0-85af562ed3f9" x="2" y="5" width="90" height="30" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{kenkou4Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="daf8d378-e724-49d1-bb3a-94338b8e403f" x="0" y="151" width="93" height="37">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="4"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="b502c147-5054-4018-bf87-1df76aed12a1" x="2" y="5" width="90" height="30" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{kenkou5Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
							</element>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="877144e7-8060-44c3-884c-1288647d2dc5" x="354" y="0" width="166" height="203">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="frame" uuid="3e6b4e8a-0636-4b5b-8b0d-150d3c5d3e8f" x="0" y="0" width="166" height="15">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<element kind="staticText" uuid="d96824dc-f5fb-4204-a537-799c23576102" mode="Opaque" x="0" y="0" width="166" height="15" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="8.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[特記事項(自治会、ﾎﾞﾗﾝﾃｨｱ等社会的活動)
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<pen lineWidth="1.0"/>
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<box>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<element kind="frame" uuid="2dcba354-9afd-4dfb-a8da-2488c6c20e73" x="0" y="15" width="166" height="188">
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
							<element kind="frame" uuid="f0135aa5-9794-470e-bbb4-d6cdb3fc0eb0" x="0" y="0" width="166" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="0"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="c05e8b87-8c65-4b6f-9538-3663830a9e23" x="3" y="5" width="161" height="30" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{tokki1Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="3a15bdf9-a31d-4308-869f-aaa0fecd047b" x="0" y="38" width="166" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="2cc79c5b-b440-44e6-beb2-1437f37c35a9" x="3" y="5" width="161" height="30" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{tokki2Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="7a5d3112-4067-4c20-a585-dc125b4c5ef7" x="0" y="76" width="166" height="38">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="2"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="8547921c-d7c0-4988-a126-870cf316e14c" x="3" y="5" width="161" height="30" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{tokki3Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="d08bd44d-d760-40d1-a2dd-4064377ce6a8" x="0" y="114" width="166" height="37">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="3"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="b65d580f-bf6a-4d23-b0ef-a9a949ff2cc0" x="3" y="5" width="161" height="30" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{tokki4Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<box>
									<bottomPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="frame" uuid="92c7e3e6-c484-4ebd-b09f-5000f48f71c4" x="0" y="151" width="166" height="37">
								<property name="com.jaspersoft.layout.grid.x" value="0"/>
								<property name="com.jaspersoft.layout.grid.y" value="4"/>
								<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
								<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
								<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
								<element kind="textField" uuid="068c5435-442b-481b-8a8b-499a8ef2c8ce" x="3" y="5" width="161" height="30" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{tokki5Knj}
]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
							</element>
							<box>
								<topPen lineWidth="1.0"/>
								<rightPen lineWidth="1.0"/>
							</box>
						</element>
						<box>
							<pen lineWidth="0.0"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<box>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="1.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
			</element>
			<element kind="staticText" uuid="5a00728d-739d-4638-83dd-7dc254c68c90" x="0" y="425" width="204" height="16" fontName="IPAexGothic" fontSize="13.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[■インフォーマルな支援活用状況 ]]></text>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="staticText" uuid="9a6f145c-de51-461b-bae0-a01064451d86" x="205" y="425" width="315" height="16" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[（親戚、近隣、友人、同僚、ﾎﾞﾗﾝﾃｨｱ、民生委員、自治会等の地域の団体等）]]></text>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="frame" uuid="d7ccd9b0-1f23-4ece-98aa-adb3688c2cf3" x="0" y="443" width="520" height="65">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<element kind="frame" uuid="2ab7ee56-1863-4404-8a30-52d315614494" x="0" y="0" width="75" height="65">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="frame" uuid="c10dc45a-848f-44b9-b728-d7a44165008b" x="0" y="0" width="75" height="15">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="d05db980-**************-dd657380aef6" mode="Opaque" x="0" y="0" width="75" height="15" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[支援提供者]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="0027ae22-282a-4e6f-a910-e6bd98630684" x="0" y="15" width="75" height="50">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<element kind="textField" uuid="894df06b-bd8e-4af9-947c-1eb60d28be52" x="5" y="3" width="68" height="42" fontName="IPAexGothic" fontSize="6.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
							<expression><![CDATA[$F{teikyoNameKnj}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<leftPen lineWidth="0.0"/>
							</box>
						</element>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<box>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="10cf764c-9626-48a8-a637-2c181fd22ccf" x="75" y="0" width="297" height="65">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="frame" uuid="8387be4b-4020-47a7-ae11-bf38c63ca17c" x="0" y="0" width="297" height="65">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="ed520d57-9d7f-4f4c-9fa3-1478f8c73a8f" mode="Opaque" x="0" y="0" width="297" height="15" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[活用している支援内容]]></text>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="0d4e290d-8847-4c0a-b1a0-62466b5eb45b" x="0" y="15" width="297" height="50">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<element kind="textField" uuid="8f1b447a-d383-4a7f-ab8b-00e0be247250" x="4" y="3" width="290" height="46" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
							<expression><![CDATA[$F{naiyo1Knj}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<leftPen lineWidth="0.0"/>
							</box>
						</element>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<box>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="b5b0d273-cd18-4d0e-b968-7c321f5c465e" x="372" y="0" width="148" height="65">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="frame" uuid="1a919ddd-76b1-4e30-8c97-01f926ccda3e" x="0" y="0" width="148" height="15">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="4622903f-d5d0-4c18-ab83-64bc805375c6" mode="Opaque" x="0" y="0" width="148" height="15" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[特記事項]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<box>
							<bottomPen lineWidth="0.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="548996a5-abe6-4840-a484-161164d37076" x="0" y="15" width="148" height="50">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<element kind="textField" uuid="06ad8827-436f-4dcd-a5c6-942b4b4d8b47" x="4" y="3" width="142" height="44" fontName="IPAexGothic" fontSize="6.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
							<expression><![CDATA[$F{sienTokkiKnj}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<pen lineWidth="0.0"/>
								<leftPen lineWidth="0.0"/>
							</box>
						</element>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
				</element>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
			</element>
			<element kind="frame" uuid="19aa8330-d2f7-4c42-a855-cfe090690982" x="0" y="512" width="520" height="65">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<element kind="frame" uuid="01fca2db-a373-4993-8a7e-9e983f76e426" x="297" y="0" width="75" height="65">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="frame" uuid="51351f15-e6ec-4209-be46-0410d1187ba4" x="0" y="0" width="75" height="15">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="a22154c1-4796-4ebc-b4df-95bb63460f66" mode="Opaque" x="0" y="0" width="75" height="15" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[支援提供者]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="331f96fe-82bc-40bd-b06d-a065964426a6" x="0" y="15" width="75" height="50">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<element kind="textField" uuid="608e1eb7-825f-4b1b-b1b7-13d960837956" x="5" y="3" width="68" height="42" fontName="IPAexGothic" fontSize="6.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
							<expression><![CDATA[$F{teikyoName2Knj}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<leftPen lineWidth="0.0"/>
							</box>
						</element>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<box>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="0efe427b-ae19-4a34-a8d0-df808e9e2c75" x="0" y="0" width="297" height="65">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="frame" uuid="5ab68aac-eafa-4f48-b861-7cdbb99f10fc" x="0" y="0" width="297" height="65">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="c3b772b0-c5a4-4a8c-8874-ece30d6a7d7f" mode="Opaque" x="0" y="0" width="297" height="15" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[本人が受けたい支援/今後必要になると思われる支援]]></text>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="a90bd25a-f3f9-4afe-bdbe-f4abbf501aa8" x="0" y="15" width="297" height="50">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<element kind="textField" uuid="89c950c9-e905-42a8-bee1-2ade998937e6" x="4" y="3" width="290" height="46" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
							<expression><![CDATA[$F{naiyo2Knj}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<leftPen lineWidth="0.0"/>
							</box>
						</element>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<box>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<element kind="frame" uuid="66df8116-975a-430b-b74a-e8327ef7859e" x="372" y="0" width="148" height="65">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<element kind="frame" uuid="e5457ce7-d35c-4fd4-9b8f-4c6213a4c10d" x="0" y="0" width="148" height="15">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="staticText" uuid="d9d64566-e8f4-43fa-97c3-43eee4f19ca5" mode="Opaque" x="0" y="0" width="148" height="15" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
							<text><![CDATA[特記事項]]></text>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<box>
							<bottomPen lineWidth="0.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="22e6869e-eaa7-4c4e-a006-3cc420d29326" x="0" y="15" width="148" height="50">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<element kind="textField" uuid="3c8deddd-bab0-40df-b624-4d098b9271ba" x="4" y="3" width="142" height="44" fontName="IPAexGothic" fontSize="6.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H">
							<expression><![CDATA[$F{sienTokki2Knj}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<box>
								<pen lineWidth="0.0"/>
								<leftPen lineWidth="0.0"/>
							</box>
						</element>
						<box>
							<topPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
				</element>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
			</element>
			<element kind="staticText" uuid="2e52b780-aadd-4478-9dda-1d7afc8b0dee" mode="Opaque" x="0" y="585" width="22" height="22" forecolor="#FFFFFF" backcolor="#080101" fontName="IPAexGothic" fontSize="22.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[3]]></text>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="staticText" uuid="1b3c54b5-5fac-46e2-bffb-704b80ca381b" x="23" y="591" width="132" height="16" fontName="IPAexGothic" fontSize="15.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[サービス利用状況]]></text>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="staticText" uuid="740bb1b6-bb0e-4846-9315-3e10d238de28" x="420" y="600" width="12" height="14" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[(]]></text>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="textField" uuid="d5bbd32d-9fa2-41aa-b57b-789c53911663" x="424" y="600" width="75" height="14" markup="html" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{emptyFlg}&&!$F{serYmdGG}.equals("")]]></printWhenExpression>
				<expression><![CDATA[$F{serYmdGG}+
"<font style='font-size:8pt'>"+$F{serYmdYY}+"</font>"+
"年"+
"<font style='font-size:8pt'>"+$F{serYmdMM}+"</font>"+
"月"+
"<font style='font-size:8pt'>"+$F{serYmdDD}+"</font>"+
"日"]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				<box>
					<leftPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="staticText" uuid="629eaeed-10e5-441a-a215-d1e9026c4ad3" x="495" y="600" width="30" height="14" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[時点）]]></text>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="frame" uuid="b60f305a-b96f-4ebf-938d-c63c664242e4" positionType="Float" stretchType="ContainerHeight" x="0" y="612" width="520" height="163" removeLineWhenBlank="true">
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="frame" uuid="71a257ad-eab1-4213-9cba-21df2c64aaa2" positionType="Float" x="0" y="0" width="520" height="13">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="staticText" uuid="9a702338-764c-40e2-ae3d-803d75e107d9" mode="Opaque" x="42" y="0" width="478" height="13" backcolor="#A6CAF0" fontName="IPAexMincho" fontSize="7.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
						<text><![CDATA[(認定調査を行った月のｻｰﾋﾞｽ利用回数を記入。(介護予防)福祉用具貸与は調査日時点の、特定(介護予防)福祉用具販売は過去６ヵ月の品目数を記載)
]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<element kind="staticText" uuid="61f8cd56-c4b0-4a19-b6b4-b7733dc5db27" mode="Opaque" x="0" y="0" width="42" height="13" backcolor="#A6CAF0" fontName="IPAexGothic" fontSize="8.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
						<text><![CDATA[在宅利用]]></text>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</element>
					<box>
						<pen lineWidth="0.25"/>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="39d590ec-bd37-47c5-898e-84fafec32e18" positionType="Float" stretchType="NoStretch" x="0" y="13" width="520" height="150" removeLineWhenBlank="true">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<element kind="frame" uuid="9e18a264-20f8-476d-846f-8d4c79ec7ff1" positionType="Float" x="0" y="0" width="260" height="151" removeLineWhenBlank="true">
						<property name="com.jaspersoft.layout.grid.x" value="0"/>
						<property name="com.jaspersoft.layout.grid.y" value="0"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="frame" uuid="2e729d5b-e94a-46ec-baa8-03570d802ab2" positionType="Float" x="0" y="1" width="260" height="13">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="0"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="07316b97-3a7d-4314-843a-a8d1bf42a4b9" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser1Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="89e0f98b-1477-46ab-b754-929603bcdd8c" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[訪問介護(ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ)]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="e28ff7d7-dccc-4ffc-a02f-8ab2ee195d4d" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="655dcf4b-afcc-4880-bb09-5a8a12758a3e" x="244" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[回]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="36881057-6aeb-4679-a052-af5ac2124497" mode="Transparent" x="223" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu1}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="e41eaa08-e875-4e2c-8597-3b2d8fada07d" positionType="Float" x="0" y="14" width="260" height="13">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="062b5ef0-f478-4fd2-8cee-2bdb77baf6f5" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser38Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="327fc41e-a665-4e73-9129-13e81f548964" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)訪問型サービス]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="f607d3b5-fbf6-4ab4-a77c-21e10220b762" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="1705d8df-711b-40b5-bdc7-e5a35d607134" x="244" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[回]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="080ecba6-a48d-4aac-8df8-5945436a6873" mode="Transparent" x="223" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu38}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="da985011-f20c-44b9-a9e4-d4bcdb1a1a1e" positionType="Float" x="0" y="27" width="260" height="12">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="2"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="57df2e4a-a061-429e-a780-a593ab97a77d" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser2Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="ec3612de-f15d-428a-857d-84daba17fd30" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)訪問入浴介護]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="ec210e6b-f70c-437c-af4c-c9ebdbca44c9" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="a5ba3571-d01e-4b48-82e6-da2e7af112c8" x="244" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[回]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="1ba39dd1-c9ad-4f89-97de-02dbc7020baf" mode="Transparent" x="223" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu2}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="9f58f24e-3ac6-4003-81b2-903f720df922" positionType="Float" x="0" y="39" width="260" height="12">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="3"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="d511837e-4dcf-4975-82a6-cd9098434bb7" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser3Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="19b9d6d3-9e08-4cdb-a894-a52ecf9d9dff" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)訪問看護]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="72ce90a5-4d43-46c9-802e-49a0aa38bc0d" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="696f22b6-4d91-4589-9915-fc6e3d6e79f1" x="244" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[回]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="ca126dc6-f881-434b-9c01-f25ec1e2d4d6" mode="Transparent" x="223" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu3}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="77e1ebcc-c8db-49f8-a313-da0f2331a5f4" positionType="Float" x="0" y="51" width="260" height="12">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="4"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="06d582d2-6232-4148-aba5-c97a6ab7adb0" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser4Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="f2f337c6-2686-4dc2-b597-fab9360ba997" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝ]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="44668a8c-eaf8-4c96-ac70-540e8e030658" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="661c5b11-33cb-4321-a0c2-5d12722fb1f0" x="244" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[回]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="fb12362c-688b-4b6e-b4d6-3537465d3fc9" mode="Transparent" x="223" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu4}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="6008f132-3036-486d-9d3f-8e62a2b94a39" positionType="Float" x="0" y="63" width="260" height="12">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="5"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="ac5c317f-0b1a-44bd-9e37-b5b46e3e32ca" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser5Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="059ffc78-fe7a-4b0d-a12f-db21e58e0a48" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)居宅療養管理指導]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="8d3eb806-af23-4630-8225-1c8c544158fc" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="269f2a5c-52ab-4bd2-b05e-947f43f7d602" x="244" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[回]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="900c0a0e-9597-4028-bc7b-95aa1232a07b" mode="Transparent" x="223" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu5}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="ff22e9a8-4f1c-4a47-8563-5c4deb748623" positionType="Float" x="0" y="75" width="260" height="12">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="6"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="50bbdd22-e215-45d6-a659-e24888e6ccfe" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser6Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="17027f74-70de-4f9f-956a-029d9a159723" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[通所介護(ﾃﾞｲｻｰﾋﾞｽ)
]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="3dbfd71f-cc1c-42a1-a214-fec0a5e8b6ae" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="318c70f8-8ae2-41e0-98c1-903e5046ff3f" x="244" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[回]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="39fb6b69-1242-4cf0-a72c-b39c19549b48" mode="Transparent" x="223" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu6}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="8c63aaaa-c12d-4e8c-a550-374f6c2ac662" positionType="Float" x="0" y="87" width="260" height="12">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="7"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="e6a3f893-41b7-4f9e-af05-f9d08ab3ceb0" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser39Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="db4bb6fd-b99c-4a96-b6c1-4db4a498eff4" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)通所型サービス
]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="9f383aa2-1b3a-4c6d-9e06-4d5e996f0243" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="5f4e3ec9-1789-4615-a0b7-a1a6dd41c996" x="244" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[回]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="65b1abca-6002-4d0a-8338-66ac80562cd2" mode="Transparent" x="223" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu39}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="a2506cf4-5196-4046-a62a-3ce7f5e7d98f" positionType="Float" x="0" y="99" width="260" height="12">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="8"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="57fb3168-faba-45f9-a1ad-0a5a54a5ae3c" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser7Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="e462db88-f319-49a4-816b-4693f894f0c4" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ(ﾃﾞｲｹｱ)
]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="6467a415-f0ec-4916-b90e-62778f534c0e" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="89ab4158-75e5-4211-9467-1431b377e9f0" x="244" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[回]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="4680131d-806e-4dd8-91b5-a68dd6eed390" mode="Transparent" x="223" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu7}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="7330b78f-5a69-4c39-89d4-9c4798080d06" positionType="Float" x="0" y="111" width="260" height="12">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="9"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="ee2fac78-3d7a-4839-832e-d762b92d3fe5" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser10Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="1b32329d-666f-43d1-a723-42adc4bfe9d7" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)短期入所生活介護(特養等)
]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="a29e70b2-714f-4585-9372-31102c019861" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="17cbc467-fa9c-424c-b9f5-1b2cc78edcf5" x="244" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[日]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="742ac103-1161-45d1-89d9-f3a14ea3cd3d" mode="Transparent" x="223" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu9}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="817d0d81-24fb-42b9-b0a5-c71d9c1dda17" positionType="Float" x="0" y="123" width="260" height="12" removeLineWhenBlank="true">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="10"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="18f725e0-dc0f-4038-a877-ed565d54f5ac" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser11Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="735789f3-8aca-40df-8ef5-7d55627cd754" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)短期入所療養介護(老健･診療所)
]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="471a8f29-45eb-46b6-a1fc-a7b7494e2a5d" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="dae020e6-83e0-42ed-ac5d-3c89b231970d" x="244" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[日]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="73f292b4-c2ac-4573-bd0c-3ba30086dd05" mode="Transparent" x="223" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu10}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<box>
							<rightPen lineWidth="1.0"/>
						</box>
					</element>
					<element kind="frame" uuid="b9d5ba89-6c94-482a-b085-86657b1f6121" positionType="Float" stretchType="NoStretch" x="260" y="0" width="260" height="150" removeLineWhenBlank="true">
						<property name="com.jaspersoft.layout.grid.x" value="1"/>
						<property name="com.jaspersoft.layout.grid.y" value="0"/>
						<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
						<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
						<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<element kind="frame" uuid="7bc27307-f5f2-47ff-a1bb-bf599607573b" positionType="Float" x="0" y="1" width="260" height="13" removeLineWhenBlank="true">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="0"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="b19bdb9d-067b-4dfd-9ee7-d81aef853a2a" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser13Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="eb14060f-8daf-4d6e-8402-3770bf061880" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)特定施設入居者生活介護
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="a5c12b55-89ce-4a27-960c-c5303df6a634" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="4c80e785-b338-4938-8185-edb770313347" mode="Transparent" x="224" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu12}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="e2697896-c998-474a-b03e-2203f5be0e9c" x="245" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[日]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="955a9379-9656-4dc9-a036-c585f082b247" positionType="Float" x="0" y="14" width="260" height="13" removeLineWhenBlank="true">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="b3e35f5b-f7d8-4932-8b10-8357614aab4e" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser36Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="1909d143-e287-4f48-98f2-344429435b4a" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[看護小規模多機能型居宅介護]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="ed008412-89ad-4275-a170-a6ce719acf3d" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="273aa867-52fb-48a6-a962-38528d23f651" mode="Transparent" x="224" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu36}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="6ca71a5a-f4ea-4d84-aedc-138c50fd8730" x="245" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[日]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="85c97ee8-8fcc-46f4-8b46-b8759514021f" positionType="Float" x="0" y="27" width="260" height="12" removeLineWhenBlank="true">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="2"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="6c8c0628-f631-446a-b4d5-c823819d8f1f" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser9Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="4d24e6e7-39f7-4de3-8a75-e690d8b041c0" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)福祉用具貸与
]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="e9665ed1-768f-4dff-8038-928c7919ed9a" mode="Transparent" x="218" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu8}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="0f6c15a2-f070-4196-99a2-ad2b2a232c22" x="236" y="2" width="20" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[品目
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="f2aa1a22-d3b8-4035-a3f3-1cb332c77800" positionType="Float" x="0" y="39" width="260" height="12" removeLineWhenBlank="true">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="3"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="6b3ebc75-3ffe-48e7-b467-fb8a8b0d8c23" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser14Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="c8a7e2dc-5ddb-46eb-b744-5d69c748d15f" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[特定(介護予防)福祉用具販売]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="4503bb27-b125-4b8a-a74c-571feb8927d7" mode="Transparent" x="218" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu13}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="3806aa10-e2e2-4ae7-94ff-0d26d0a8466b" x="236" y="2" width="20" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[品目
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="b75a7595-4737-4283-b263-6840f9a00317" positionType="Float" x="0" y="51" width="260" height="12" removeLineWhenBlank="true">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="4"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="03b5323e-b204-4fe8-9e2c-223f28159878" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser15Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="b55ee5f4-9364-4ec1-b887-aa2542635595" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[住宅改修]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="ellipse" uuid="d32f326c-92a7-4e4e-aee9-e790aba6bea1" mode="Transparent" x="235" y="1" width="22" height="11" printInFirstWholeBand="true">
								<printWhenExpression><![CDATA[$F{kaisu15}.equals("2")?true:false]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="ellipse" uuid="cc75adb4-09b2-4f00-b420-65148817cbae" mode="Transparent" x="209" y="1" width="22" height="11">
								<printWhenExpression><![CDATA[$F{kaisu15}.equals("1")?true:false]]></printWhenExpression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="b4c56051-e268-400a-bcd8-0c91ce244519" x="211" y="2" width="46" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[あり・なし]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="6bdbfc10-12ce-4388-9c62-47bfa0370913" positionType="Float" x="0" y="63" width="260" height="12" removeLineWhenBlank="true">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="5"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="d13f3729-c6ea-429d-8c8d-c9f0730e30f9" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser31Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="d15d14a1-93f7-4f90-ad7b-904c9840e82a" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[夜間対応型訪問介護]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="a6f59d12-9e59-4135-89a3-493ff5b6cd84" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="a1b23d36-a065-4e74-9c40-07f42231c197" x="245" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[日]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="abcf3727-3fe3-4b42-b9d8-8972625d601c" mode="Transparent" x="224" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu31}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="7017c560-bba1-4d4c-9b54-09fa85b51251" positionType="Float" x="0" y="74" width="260" height="12" removeLineWhenBlank="true">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="6"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<element kind="textField" uuid="cf973b6d-0910-4ad2-95f3-ed26297b0b7b" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser32Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="1b796bb7-2ad4-4c4f-bb8f-578895b8e31b" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)認知症対応型通所介護]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="7b4f0cba-a29d-4c60-92d6-88e9cf680b19" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="bcc51bcc-cf46-45ae-997a-ab83a45fc2a2" x="245" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[日]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="ded0f3ba-4196-444f-a239-e36e5b8fa183" mode="Transparent" x="224" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu32}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="a14d2d0f-3828-4c4b-bba2-3d6b6001c5fb" positionType="Float" x="0" y="86" width="260" height="12" removeLineWhenBlank="true">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="9"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="eda65046-601c-4cfc-bae5-073c06daf29b" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser33Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="258ef556-f9d8-4acc-93f2-3f325a522e59" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)小規模多機能型居宅介護]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="7227ac2c-bf9e-409e-abe3-7040faeedf90" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="3dec0c86-ee40-41dc-931e-e78c4f6dfdd4" x="245" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[日]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="22a0576e-46ca-4c16-9ef2-0816ad549faa" mode="Transparent" x="224" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu33}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="e95503d3-07c9-4bc4-bbb8-2ab69d4bab2b" positionType="Float" x="0" y="99" width="260" height="12">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="8"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="717f18e7-fcfc-47e6-bbaf-6ff269d34b0c" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser12Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="ee39f10a-9872-405b-a08e-a38aacad7e2b" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)認知症対応型共同生活介護]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="3b4f733f-ecc9-4788-8ae6-1f5eeff79a8a" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="d06f1722-d7b2-4b16-b784-9610086a0791" mode="Transparent" x="224" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu11}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="0a8bf49a-6102-41db-a3c4-88febe83d264" x="245" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[日]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="24b99d5a-bdf6-480d-81af-cd1aff5f2772" positionType="Float" x="0" y="111" width="260" height="12">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="9"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="729f073f-9323-4873-a524-44930d6df32d" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser37Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="3c6492d0-68f7-4652-8a4b-82a028bfb2a3" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[定期巡回･随時対応型訪問介護看護 ]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="60340405-eb45-434c-8de1-7ec19af758a3" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="426e6c05-e268-4a39-9dd1-a6a00184d007" mode="Transparent" x="224" y="2" width="18" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu37}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="91481ea4-86b1-43d6-8cea-ae08d0beda99" x="245" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[回]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="86dafe76-5fe7-4143-801f-45281f8aee08" positionType="Float" x="0" y="123" width="260" height="12" removeLineWhenBlank="true">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="10"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<element kind="textField" uuid="b2f5be31-8851-46bc-9385-370a17b605f2" x="6" y="2" width="9" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{ser40Cd} ==1  ? "✓"  : ""]]></expression>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</element>
							<element kind="staticText" uuid="25a8f6a4-28aa-46fd-b96e-4cf851267eb0" x="20" y="2" width="180" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(介護予防)その他の生活支援サービス]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
						</element>
						<element kind="frame" uuid="71605ee3-5003-4fb6-9cb4-77cac11884ea" positionType="Float" x="0" y="135" width="260" height="12" removeLineWhenBlank="true">
							<property name="com.jaspersoft.layout.grid.x" value="0"/>
							<property name="com.jaspersoft.layout.grid.y" value="10"/>
							<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
							<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
							<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
							<property name="com.jaspersoft.studio.unit.y" value="px"/>
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<element kind="staticText" uuid="e1363c5c-bcb1-44b9-a245-8ef555d88e2e" x="11" y="2" width="35" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
								<text><![CDATA[(名称：]]></text>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="textField" uuid="72c20362-2d73-4c5f-b3aa-fe6ff8697f0a" mode="Transparent" x="42" y="2" width="135" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Left" vTextAlign="Middle">
								<expression><![CDATA[$F{ser40NameKnj}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<element kind="staticText" uuid="8a59ebb5-b78e-49e6-975e-bf4d5bd92db0" x="174" y="2" width="15" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[ ）]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="9b601f5e-dd6f-4edd-8781-7c9ffb46d2c0" x="213" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[月
]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="textField" uuid="fd87052c-07ec-46c1-9cd3-c921c79fb060" mode="Transparent" x="224" y="2" width="18" height="10" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
								<expression><![CDATA[$F{kaisu40}]]></expression>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
							<element kind="staticText" uuid="c3d58e87-740f-421c-8296-f7a4c7adda7a" x="245" y="2" width="10" height="9" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
								<text><![CDATA[回]]></text>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
							</element>
						</element>
					</element>
					<box>
						<leftPen lineWidth="1.0"/>
						<rightPen lineWidth="1.0"/>
					</box>
				</element>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="31" splitType="Stretch">
		<element kind="staticText" uuid="e3c09477-6d1e-44bc-833e-418e36683f40" x="319" y="20" width="196" height="9" fontName="IPAexGothic" fontSize="8.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<text><![CDATA[〔全社協･在宅版ケアプラン作成方法検討委員会作成〕]]></text>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<element kind="textField" uuid="6eea047f-fea3-449f-a048-d61927c4e571" x="5" y="9" width="150" height="9" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<expression><![CDATA[$F{bunsyoKanriNo}]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
