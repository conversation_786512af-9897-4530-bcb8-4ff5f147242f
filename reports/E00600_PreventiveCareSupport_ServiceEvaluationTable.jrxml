<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="E00600_PreventiveCareSupport_ServiceEvaluationTable" language="java" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="772" leftMargin="33" rightMargin="37" topMargin="17" bottomMargin="28" uuid="85489e7c-df46-400a-ad4a-e05f422b4a0d">
	<style name="Table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<pen lineStyle="Solid"/>
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_CH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="0.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="0.0" lineColor="#000000"/>
		</box>
	</style>
	<dataset name="Dataset1" uuid="65ad101a-89a8-42d7-9a35-f10c02ee2346">
		<field name="mokuhyoNo" class="java.lang.String">
			<description><![CDATA[mokuhyoNo]]></description>
		</field>
		<field name="mokuhyoKnj" class="java.lang.String">
			<description><![CDATA[mokuhyoKnj]]></description>
		</field>
		<field name="kikanKnj" class="java.lang.String">
			<description><![CDATA[kikanKnj]]></description>
		</field>
		<field name="jyoukyouKnj" class="java.lang.String">
			<description><![CDATA[jyoukyouKnj]]></description>
		</field>
		<field name="tasseiKnj" class="java.lang.String">
			<description><![CDATA[tasseiKnj]]></description>
		</field>
		<field name="geninKnj" class="java.lang.String">
			<description><![CDATA[geninKnj]]></description>
		</field>
		<field name="genin2Knj" class="java.lang.String">
			<description><![CDATA[genin2Knj]]></description>
		</field>
		<field name="housinKnj" class="java.lang.String">
			<description><![CDATA[housinKnj]]></description>
		</field>
	</dataset>
	<dataset name="Dataset2" uuid="ef3ca90a-31e7-4daa-8e78-1a71039e442f">
		<field name="souHousinKnj" class="java.lang.String">
			<description><![CDATA[souHousinKnj]]></description>
		</field>
		<field name="ikenKnj" class="java.lang.String">
			<description><![CDATA[ikenKnj]]></description>
		</field>
		<field name="yotei1Knj" class="java.lang.String">
			<description><![CDATA[yotei1Knj]]></description>
		</field>
		<field name="yotei2Knj" class="java.lang.String">
			<description><![CDATA[yotei2Knj]]></description>
		</field>
		<field name="yotei3Knj" class="java.lang.String">
			<description><![CDATA[yotei3Knj]]></description>
		</field>
		<field name="jigyo1Knj" class="java.lang.String">
			<description><![CDATA[jigyo1Knj]]></description>
		</field>
		<field name="jigyo2Knj" class="java.lang.String">
			<description><![CDATA[jigyo2Knj]]></description>
		</field>
		<field name="jigyo3Knj" class="java.lang.String">
			<description><![CDATA[jigyo3Knj]]></description>
		</field>
		<field name="jigyo4Knj" class="java.lang.String">
			<description><![CDATA[jigyo4Knj]]></description>
		</field>
		<field name="jigyo5Knj" class="java.lang.String">
			<description><![CDATA[jigyo5Knj]]></description>
		</field>
	</dataset>
	<query language="SQL"><![CDATA[]]></query>
	<field name="bunsyoKanriNo" class="java.lang.String">
		<description><![CDATA[bunsyoKanriNo]]></description>
	</field>
	<field name="title" class="java.lang.String">
		<description><![CDATA[title]]></description>
	</field>
	<field name="titleFont" class="java.lang.String">
		<description><![CDATA[titleFont]]></description>
	</field>
	<field name="shiTeiDateYY" class="java.lang.String"/>
	<field name="shiTeiDateMM" class="java.lang.String"/>
	<field name="shiTeiDateDD" class="java.lang.String"/>
	<field name="shiTeiDateGG" class="java.lang.String"/>
	<field name="shiTeiKubun" class="java.lang.Integer">
		<description><![CDATA[shiTeiKubun]]></description>
	</field>
	<field name="emptyFlg" class="java.lang.Boolean">
		<description><![CDATA[emptyFlg]]></description>
	</field>
	<field name="jigyosha" class="java.lang.String">
		<description><![CDATA[jigyosha]]></description>
	</field>
	<field name="userName" class="java.lang.String">
		<description><![CDATA[userName]]></description>
	</field>
	<field name="keisho" class="java.lang.String">
		<description><![CDATA[keisho]]></description>
	</field>
	<field name="shokuName" class="java.lang.String">
		<description><![CDATA[shokuName]]></description>
	</field>
	<field name="hyoukaYY" class="java.lang.String"/>
	<field name="hyoukaMM" class="java.lang.String"/>
	<field name="hyoukaDD" class="java.lang.String"/>
	<field name="hyoukaGG" class="java.lang.String"/>
	<field name="chouhyouInkan1" class="java.lang.String">
		<description><![CDATA[chouhyouInkan1]]></description>
	</field>
	<field name="chouhyouInkan2" class="java.lang.String">
		<description><![CDATA[chouhyouInkan2]]></description>
	</field>
	<field name="chouhyouInkan3" class="java.lang.String">
		<description><![CDATA[chouhyouInkan3]]></description>
	</field>
	<field name="chouhyouInkan4" class="java.lang.String">
		<description><![CDATA[chouhyouInkan4]]></description>
	</field>
	<field name="chouhyouInkan5" class="java.lang.String">
		<description><![CDATA[chouhyouInkan5]]></description>
	</field>
	<field name="chouhyouInkan6" class="java.lang.String">
		<description><![CDATA[chouhyouInkan6]]></description>
	</field>
	<field name="chouhyouInkan7" class="java.lang.String">
		<description><![CDATA[chouhyouInkan7]]></description>
	</field>
	<field name="chouhyouInkan8" class="java.lang.String">
		<description><![CDATA[chouhyouInkan8]]></description>
	</field>
	<field name="chouhyouInkan9" class="java.lang.String">
		<description><![CDATA[chouhyouInkan9]]></description>
	</field>
	<field name="chouhyouInkan10" class="java.lang.String">
		<description><![CDATA[chouhyouInkan10]]></description>
	</field>
	<field name="chouhyouInkan11" class="java.lang.String">
		<description><![CDATA[chouhyouInkan11]]></description>
	</field>
	<field name="chouhyouInkan12" class="java.lang.String">
		<description><![CDATA[chouhyouInkan12]]></description>
	</field>
	<field name="chouhyouInkan13" class="java.lang.String">
		<description><![CDATA[chouhyouInkan13]]></description>
	</field>
	<field name="chouhyouInkan14" class="java.lang.String">
		<description><![CDATA[chouhyouInkan14]]></description>
	</field>
	<field name="chouhyouInkan15" class="java.lang.String">
		<description><![CDATA[chouhyouInkan15]]></description>
	</field>
	<field name="inkanKubun" class="java.lang.Integer">
		<description><![CDATA[inkanKubun]]></description>
	</field>
	<field name="hsList" class="net.sf.jasperreports.engine.JRDataSource">
		<description><![CDATA[hsList]]></description>
	</field>
	<field name="seList" class="net.sf.jasperreports.engine.JRDataSource">
		<description><![CDATA[seList]]></description>
	</field>
	<field name="shoninKubun" class="java.lang.Integer">
		<description><![CDATA[shoninKubun]]></description>
	</field>
	<field name="subReportPath" class="java.lang.String">
		<description><![CDATA[subReportPath]]></description>
	</field>
	<field name="subReportDataDs" class="net.sf.jasperreports.engine.JRDataSource">
		<description><![CDATA[subReportDataDs]]></description>
	</field>
	<pageHeader height="140">
		<element kind="textField" uuid="417b1493-18ec-4c56-b5fb-5a3becbc4ec7" x="609" y="34" width="148" height="11" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[$F{jigyosha}]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="frame" uuid="daf6a822-6f5e-48f8-b37f-8779f4bee2ff" x="5" y="66" width="207" height="12">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<element kind="staticText" uuid="3b92d25b-**************-f5782f99ce26" x="0" y="0" width="47" height="12" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
				<text><![CDATA[利用者名]]></text>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
			</element>
			<element kind="textField" uuid="dba6e257-a175-4ac1-96f9-bcb10ffc535b" x="47" y="0" width="136" height="12" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{userName}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<element kind="textField" uuid="1d308587-23e3-43fa-8eb9-fef66f2d366c" x="183" y="0" width="24" height="12" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{keisho}]]></expression>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
			</element>
			<box>
				<bottomPen lineWidth="1.0"/>
			</box>
		</element>
		<element kind="frame" uuid="33fc1be9-50b4-4dbf-bca0-ba92b6317108" x="530" y="71" width="227" height="12">
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<element kind="textField" uuid="9bafbc77-bec0-4c22-bdd7-ee3d32054627" x="92" y="0" width="135" height="12" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
				<expression><![CDATA[$F{shokuName}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="staticText" uuid="1a8b9682-c272-4071-a68a-568ed77160c9" x="0" y="0" width="82" height="12" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[計画作成者氏名]]></text>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
			</element>
			<box>
				<bottomPen lineWidth="1.0"/>
			</box>
		</element>
		<element kind="textField" uuid="39b5cfe1-1bab-4ecc-b36c-6e86d93266a1" x="672" y="21" width="85" height="10" markup="html" fontName="IPAexGothic" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
			<printWhenExpression><![CDATA[($F{shiTeiKubun} != 1) && (!$F{emptyFlg})]]></printWhenExpression>
			<expression><![CDATA[$F{shiTeiDateGG}+
"<font style='font-size:9pt'>"+$F{shiTeiDateYY}+"</font>"+
"年"+
"<font style='font-size:9pt'>"+$F{shiTeiDateMM}+"</font>"+
"月"+
"<font style='font-size:9pt'>"+$F{shiTeiDateDD}+"</font>"+
"日"]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="9c3d757a-421e-49c2-b6d9-c6171c8239fd" x="123" y="31" width="483" height="18" fontName="IPAexGothic" fontSize="14.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="true" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA[$F{title}]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<propertyExpression name="net.sf.jasperreports.style.fontSize"><![CDATA[$F{titleFont}]]></propertyExpression>
		</element>
		<element kind="frame" uuid="482b3697-c2b6-4c77-9b01-3e5481a0d108" positionType="Float" x="0" y="83" width="772" height="53" removeLineWhenBlank="true">
			<printWhenExpression><![CDATA[$F{inkanKubun}==1]]></printWhenExpression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<element kind="textField" uuid="0cd3d627-2fb6-403b-8efe-2f3d241668dd" positionType="Float" x="0" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan1}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan1}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="475859c2-5fe3-4c45-8d1b-536cb18e962a" positionType="Float" x="0" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan1}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="99233985-958b-4394-bf05-697088b0e6e7" positionType="Float" x="47" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan2}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan2}]]></expression>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="c09d5e73-545f-464d-9f9a-203befaf5ef7" positionType="Float" x="47" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan2}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="f2e84f94-42d1-44a4-b91e-ff9619486bb1" positionType="Float" x="94" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan3}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan3}]]></expression>
				<box>
					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="0c6541cf-ad1b-4d4d-b3d0-c24128312a66" positionType="Float" x="94" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan3}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="8739be8e-66a7-400f-815a-42bb851d14f1" positionType="Float" x="141" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan4}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan4}]]></expression>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="52f39fcd-2b4f-4ca1-880c-2acfc631a673" positionType="Float" x="141" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan4}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="0dfa8cd9-4836-4f5a-a2ab-2a170c4533cb" positionType="Float" x="188" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan5}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan5}]]></expression>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="d2bf3e6f-1436-4c4e-a00d-52368cde9934" positionType="Float" x="188" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan5}.isEmpty() && $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="0c8b96a4-b25a-4382-8aa1-7e595e37b813" positionType="Float" x="235" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan6}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan6}]]></expression>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="bd447294-0dd2-4eb8-ab10-30845a625bc5" positionType="Float" x="235" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan6}.isEmpty() && $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="77188962-034f-4cbb-836f-29b5245e2436" positionType="Float" x="282" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan7}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan7}]]></expression>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="050ae442-0be0-4f0e-a821-cc5a441f22d9" positionType="Float" x="282" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan7}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="6f5272db-e367-45ab-b86d-4ea331a75d61" positionType="Float" x="329" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan8}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan8}]]></expression>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="e15a20ff-edc1-4388-a3be-8149554be386" positionType="Float" x="329" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan8}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="978301a8-01c1-4003-8d63-2335555829df" positionType="Float" x="376" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan9}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan9}]]></expression>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="cf3116a6-2b83-48c2-b437-71a9c0eab78e" positionType="Float" x="376" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan9}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="f52b5ebb-98e3-466b-b296-8f05d923ada7" positionType="Float" x="423" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan10}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan10}]]></expression>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="5dcfa1d8-6d24-4f33-a026-3511a94a1fbb" positionType="Float" x="423" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan10}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="d083a056-6397-485b-b1fd-6d8d0ec2a07f" positionType="Float" x="470" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan11}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan11}]]></expression>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="e3086641-dd36-49c0-b7fe-1717c829486d" positionType="Float" x="470" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan11}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="f5810595-e368-463d-be7d-089bd6757022" positionType="Float" x="517" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan12}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan12}]]></expression>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="11f92591-14b8-4321-ac3c-306eadd2baf6" positionType="Float" x="517" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan12}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="939ac929-36aa-4ee3-a3d2-7bb3b938e4b8" positionType="Float" x="564" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan13}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan13}]]></expression>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="fe36b94d-7c9c-4988-af51-77508a30595c" positionType="Float" x="564" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan13}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="6ceceea4-804f-4ce0-b4d6-31a13c4fff64" positionType="Float" x="611" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan14}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan14}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="3e1c11ee-1393-4395-99a8-fa5456725ced" positionType="Float" x="611" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan14}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="textField" uuid="30675fc7-a153-4071-af00-aca209e59301" positionType="Float" x="658" y="5" width="47" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan15}.isEmpty() && $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<expression><![CDATA[$F{chouhyouInkan15}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
			<element kind="staticText" uuid="1733b620-0f22-4b22-aadf-0afee65367fc" positionType="Float" x="658" y="15" width="47" height="38">
				<printWhenExpression><![CDATA[!$F{chouhyouInkan15}.isEmpty()&& $V{PAGE_NUMBER} == 1]]></printWhenExpression>
				<text><![CDATA[]]></text>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</element>
		</element>
		<element kind="frame" uuid="083c7cc2-1381-461f-b4db-86ad18f9b898" x="612" y="50" width="145" height="12">
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<element kind="textField" uuid="79fc14bb-f68c-4804-b660-4a31224d2cbf" x="52" y="0" width="93" height="12" markup="html" fontName="IPAexGothic" fontSize="11.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" linkType="None" linkTarget="Self" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{hyoukaGG}+
"<font style='font-size:10pt'>"+$F{hyoukaYY}+"</font>"+
"年"+
"<font style='font-size:10pt'>"+$F{hyoukaMM}+"</font>"+
"月"+
"<font style='font-size:10pt'>"+$F{hyoukaDD}+"</font>"+
"日"]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="staticText" uuid="efa70661-e640-4d81-97ba-47b5f8d91445" x="0" y="0" width="52" height="12" fontName="IPAexGothic" fontSize="12.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
				<text><![CDATA[評価日]]></text>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box leftPadding="4">
					<bottomPen lineWidth="0.0"/>
				</box>
			</element>
			<box>
				<bottomPen lineWidth="1.0"/>
			</box>
		</element>
		<element kind="textField" uuid="2d65a951-ca2e-41e7-be93-639499f4c0b7" key="" x="558" y="0" width="182" height="9" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
			<printWhenExpression><![CDATA[$F{bunsyoKanriNo}==null?false:true]]></printWhenExpression>
			<expression><![CDATA[$F{bunsyoKanriNo}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<detail>
		<band height="39">
			<element kind="frame" uuid="c1be7b1f-e889-4afc-9eb7-33934b11af9e" stretchType="ContainerHeight" x="0" y="2" width="757" height="37">
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="component" uuid="cabb2d26-e803-4205-b8f0-c32ed4ca6735" x="0" y="0" width="757" height="37">
					<component kind="table" whenNoDataType="AllSectionsNoDetail">
						<datasetRun uuid="d2225540-57f2-4304-98d6-f366ccc999ec" subDataset="Dataset1">
							<dataSourceExpression><![CDATA[$F{seList}]]></dataSourceExpression>
						</datasetRun>
						<columnHeader splitType="Stretch"/>
						<detail splitType="Stretch"/>
						<column kind="single" uuid="8a7e1a73-3724-40c4-976d-f164c6e4eb41" width="137">
							<columnHeader height="22" rowSpan="1" style="Table 1_CH">
								<element kind="staticText" uuid="e55c3d72-c30b-490a-ae72-bfdf51ec5a44" mode="Opaque" x="0" y="0" width="137" height="22" markup="html" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Bottom">
									<text><![CDATA[目標]]></text>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
									<box bottomPadding="1">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<box style="Table 1_CH">
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
							</columnHeader>
							<detailCell height="15">
								<element kind="textField" uuid="ba276a2e-6f33-435a-8515-e51be4fc267e" x="0" y="0" width="15" height="15" fontName="IPAexMincho" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Right" vTextAlign="Top">
									<expression><![CDATA[$F{mokuhyoNo}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<box topPadding="1" leftPadding="1" rightPadding="1">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="2d33fed3-d198-4062-99d3-f83d859ba07b" x="15" y="0" width="122" height="15" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{mokuhyoKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box topPadding="1" leftPadding="4" rightPadding="2">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Dashed" lineColor="#000000"/>
									</box>
								</element>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="0.0" lineStyle="Dashed"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						</column>
						<column kind="single" uuid="5b916d3b-36e5-4818-852a-02387ad82f39" width="58">
							<columnHeader height="22" rowSpan="1" style="Table 1_CH">
								<element kind="staticText" uuid="ceef5337-8fd0-45af-8b60-86c70d768d28" mode="Opaque" x="0" y="0" width="58" height="22" markup="html" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Bottom">
									<text><![CDATA[評価期間]]></text>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box bottomPadding="1">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box style="Table 1_CH">
									<topPen lineWidth="1.0"/>
									<leftPen lineWidth="0.75"/>
									<bottomPen lineWidth="1.0"/>
									<rightPen lineWidth="1.0"/>
								</box>
							</columnHeader>
							<detailCell height="15">
								<element kind="textField" uuid="8b74ca8f-2f53-47b0-bb80-3d4e24282aaf" x="0" y="0" width="58" height="15" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{kikanKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box topPadding="1" leftPadding="2">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
						</column>
						<column kind="single" uuid="2087df25-c4a1-497c-a862-d521dad4d214" width="107">
							<columnHeader height="22" rowSpan="1" style="Table 1_CH">
								<element kind="staticText" uuid="f4bd397a-c26c-4a65-8e69-64c2cfc50155" mode="Opaque" x="0" y="0" width="107" height="22" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Bottom">
									<text><![CDATA[目標達成状況]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box bottomPadding="1"/>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								<box style="Table 1_CH">
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<detailCell height="15">
								<element kind="textField" uuid="cc16bedc-e040-42e2-b86f-e30ddfd2cbed" x="0" y="0" width="107" height="15" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{jyoukyouKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box topPadding="1" leftPadding="4" rightPadding="4"/>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
						</column>
						<column kind="single" uuid="bc9cdedd-ba74-41d6-ae85-267a8dcbf2ef" width="39">
							<columnHeader height="22" rowSpan="1" style="Table 1_CH">
								<element kind="staticText" uuid="6c2b245f-557c-45cc-a888-7c4e72f51d25" mode="Opaque" x="0" y="0" width="39" height="13" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[目標
]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</element>
								<element kind="staticText" uuid="128d9049-fec0-49c8-96c1-9430fdfdc84c" mode="Opaque" x="0" y="13" width="39" height="9" fontName="IPAexGothic" fontSize="6.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" bold="false" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[達成/未達成]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
								<box style="Table 1_CH">
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<detailCell height="15">
								<element kind="textField" uuid="35d6d3d6-72c0-4a8c-8e9c-b71870f5781e" x="0" y="0" width="39" height="15" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
									<expression><![CDATA[$F{tasseiKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box topPadding="1">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
						</column>
						<column kind="single" uuid="c95b9471-f817-4d08-be8c-19e53aa6682b" width="107">
							<columnHeader height="22" rowSpan="1" style="Table 1_CH">
								<element kind="staticText" uuid="f469b0d4-3b84-4c34-a5bf-5765be6f2e75" mode="Opaque" x="0" y="0" width="107" height="22" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[目標達成しない原因
（本人・家族の意見）]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								<box style="Table 1_CH">
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<detailCell height="15">
								<element kind="textField" uuid="10e98819-a382-47e4-9c17-a615e2e25769" x="0" y="0" width="107" height="15" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{geninKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box topPadding="1" leftPadding="4" rightPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column5"/>
						</column>
						<column kind="single" uuid="ff2c30ec-dda2-4444-8201-e3fc118d5f51" width="107">
							<columnHeader height="22" rowSpan="1" style="Table 1_CH">
								<element kind="staticText" uuid="111b77f8-eb6c-4d55-be51-1f3f6c09c5ad" mode="Opaque" x="0" y="0" width="107" height="22" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Middle">
									<text><![CDATA[目標達成しない原因
（計画作成者の評価）
]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								<box style="Table 1_CH">
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<detailCell height="15">
								<element kind="textField" uuid="e6058d1c-4aee-46d3-937a-20f1711bc03f" x="0" y="0" width="107" height="15" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{genin2Knj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box topPadding="1" leftPadding="4" rightPadding="4">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box>
									<topPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column6"/>
						</column>
						<column kind="single" uuid="3d5a6893-17eb-48f0-b34e-452ab6e6c90f" width="202">
							<columnHeader height="22" rowSpan="1" style="Table 1_CH">
								<element kind="staticText" uuid="9fedb7fe-0953-4367-a51e-0279bc6962be" mode="Opaque" x="0" y="0" width="202" height="22" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Center" vTextAlign="Top">
									<text><![CDATA[今後の方針 ]]></text>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box topPadding="1">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box style="Table 1_CH">
									<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</columnHeader>
							<detailCell height="15">
								<element kind="textField" uuid="95195dd1-6214-4845-b153-c1e674838066" x="0" y="0" width="202" height="15" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" hTextAlign="Left" vTextAlign="Top">
									<expression><![CDATA[$F{housinKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
									<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
									<box topPadding="1" leftPadding="4" rightPadding="10">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								<box>
									<topPen lineWidth="1.0"/>
									<bottomPen lineWidth="1.0"/>
								</box>
							</detailCell>
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column7"/>
						</column>
					</component>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
					<property name="com.jaspersoft.layout.grid.x" value="1"/>
					<property name="com.jaspersoft.layout.grid.y" value="0"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</element>
				<element kind="frame" uuid="bd88588c-58f2-4cb3-9ae0-599c611c1a7b" positionType="Float" stretchType="ContainerBottom" mode="Transparent" x="0" y="22" width="757" height="15" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<element kind="frame" uuid="6ac416b7-522e-46fb-a8fa-220ed41f11fe" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="0" y="0" width="15" height="15" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Dashed" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="a9a2c3de-262c-42e8-a114-5606dd73580a" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="15" y="0" width="122" height="15" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.75" lineStyle="Dashed" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.75" lineStyle="Dashed" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="7d1ace4e-008d-4f2f-9fbf-bf03eb2f4cf9" key="" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="137" y="0" width="58" height="15" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Dashed" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="fb386aa4-6b0b-4c4e-8803-1866b5792c17" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="195" y="0" width="107" height="15" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="9745379c-e1a2-4c1a-93ad-3b3864f9a1ca" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="302" y="0" width="39" height="15" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="50f9f98a-c2a3-4f60-baf6-521882d2416b" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="341" y="0" width="107" height="15" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="d1fb43ac-f445-4d8f-b5f6-a63efa5634fd" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="448" y="0" width="107" height="15" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="2bc6b210-9a13-4ff9-8b6c-8063ee50525e" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="555" y="0" width="202" height="15" printWhenDetailOverflows="true">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
				</element>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="85" splitType="Prevent">
			<element kind="component" uuid="d6fb99ac-ea5a-4f12-ac3f-c488c6c1cd34" x="0" y="6" width="757" height="77">
				<component kind="table">
					<datasetRun uuid="2e8b8728-b8c1-4677-8405-ae6e4efdd41c" subDataset="Dataset2">
						<dataSourceExpression><![CDATA[$F{hsList}]]></dataSourceExpression>
					</datasetRun>
					<column kind="single" uuid="6d849d22-878d-4052-9f83-44b96c3ba9e1" width="757">
						<columnHeader height="12" style="Table 1_CH">
							<element kind="frame" uuid="b5730697-373b-4a53-ab6b-4d630a6992df" positionType="Float" stretchType="ContainerHeight" x="0" y="0" width="757" height="12">
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="staticText" uuid="75b1ae48-3c96-42ab-a475-7fecdf86fa52" x="0" y="0" width="270" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
									<paragraph leftIndent="0"/>
									<text><![CDATA[総合的な方針]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<box leftPadding="2">
										<pen lineWidth="0.0"/>
										<rightPen lineWidth="1.0"/>
									</box>
								</element>
								<element kind="staticText" uuid="67a03e44-e92c-4408-a406-7f86332ac9fa" x="270" y="0" width="487" height="12" fontName="IPAexGothic" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Left" vTextAlign="Middle">
									<paragraph leftIndent="0"/>
									<text><![CDATA[介護予防支援事業者意見]]></text>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<box leftPadding="2">
										<pen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<box style="Table 1_CH">
								<pen lineWidth="1.6"/>
								<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</columnHeader>
						<detailCell height="65">
							<element kind="frame" uuid="0c529c40-c8a9-4ab8-806f-371ca2f3cc4c" x="0" y="0" width="757" height="65">
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<element kind="textField" uuid="9308b16f-477a-4fd8-a123-c38718ac0014" stretchType="ContainerHeight" x="0" y="0" width="270" height="65" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" vTextAlign="Top">
									<paragraph leftIndent="0"/>
									<expression><![CDATA[$F{souHousinKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<box leftPadding="4" rightPadding="8">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="textField" uuid="56392544-453c-4ccd-af3e-3c8fd3540875" stretchType="ContainerHeight" x="270" y="0" width="221" height="65" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" textAdjust="StretchHeight" vTextAlign="Top">
									<paragraph leftIndent="0"/>
									<expression><![CDATA[$F{ikenKnj}]]></expression>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
									<box leftPadding="4" rightPadding="8">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="57333ce8-bcc1-4b14-ad46-650741c3eb3f" positionType="Float" stretchType="ContainerHeight" x="491" y="0" width="94" height="65">
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<element kind="textField" uuid="046c22d6-b035-4393-8016-36285b6ebe12" x="0" y="0" width="87" height="12" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
										<paragraph leftIndent="0"/>
										<expression><![CDATA[$F{yotei1Knj}]]></expression>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box leftPadding="4">
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</element>
									<element kind="textField" uuid="56116ede-5cd9-4436-be59-345beaa9bb8e" x="0" y="12" width="87" height="12" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
										<paragraph leftIndent="0"/>
										<expression><![CDATA[$F{yotei2Knj}]]></expression>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<box leftPadding="4">
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</element>
									<element kind="textField" uuid="a6709fef-3c64-4a3e-afa4-6160da83d572" x="0" y="24" width="87" height="12" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
										<paragraph leftIndent="0"/>
										<expression><![CDATA[$F{yotei3Knj}]]></expression>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
										<box leftPadding="4">
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</element>
									<box leftPadding="2">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<element kind="frame" uuid="eb5d9719-c083-45b5-bb03-c8f739b05a17" positionType="Float" stretchType="ContainerHeight" x="585" y="0" width="172" height="65">
									<property name="com.jaspersoft.studio.unit.width" value="px"/>
									<property name="com.jaspersoft.studio.unit.y" value="px"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
									<property name="com.jaspersoft.studio.unit.x" value="px"/>
									<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
									<element kind="textField" uuid="e9ebf133-dfda-45f6-8c41-4d1161af6e6d" x="0" y="0" width="165" height="13" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
										<paragraph leftIndent="0"/>
										<expression><![CDATA[$F{jigyo1Knj}]]></expression>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
										<property name="com.jaspersoft.layout.grid.x" value="0"/>
										<property name="com.jaspersoft.layout.grid.y" value="0"/>
										<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
										<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
										<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
										<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
										<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
										<box leftPadding="4">
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</element>
									<element kind="textField" uuid="bc2f20f0-0eb6-4583-b6f2-bc11f5bf4604" x="0" y="13" width="165" height="13" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
										<paragraph leftIndent="0"/>
										<expression><![CDATA[$F{jigyo2Knj}]]></expression>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
										<property name="com.jaspersoft.layout.grid.x" value="0"/>
										<property name="com.jaspersoft.layout.grid.y" value="1"/>
										<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
										<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
										<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
										<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
										<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
										<box leftPadding="4">
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</element>
									<element kind="textField" uuid="3258c97c-e64e-43ee-99a7-df661ff5611e" x="0" y="26" width="165" height="13" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
										<paragraph leftIndent="0"/>
										<expression><![CDATA[$F{jigyo3Knj}]]></expression>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
										<property name="com.jaspersoft.layout.grid.x" value="0"/>
										<property name="com.jaspersoft.layout.grid.y" value="2"/>
										<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
										<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
										<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
										<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
										<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
										<box leftPadding="4">
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</element>
									<element kind="textField" uuid="05319df2-a419-4996-b17f-1737106de218" x="0" y="39" width="165" height="13" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
										<paragraph leftIndent="0"/>
										<expression><![CDATA[$F{jigyo4Knj}]]></expression>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
										<property name="com.jaspersoft.layout.grid.x" value="0"/>
										<property name="com.jaspersoft.layout.grid.y" value="3"/>
										<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
										<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
										<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
										<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
										<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
										<box leftPadding="4">
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</element>
									<element kind="textField" uuid="84400d8b-9b69-4c87-b7f3-1003e62a5531" x="0" y="52" width="165" height="13" fontName="IPAexMincho" fontSize="10.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" vTextAlign="Middle">
										<paragraph leftIndent="0"/>
										<expression><![CDATA[$F{jigyo5Knj}]]></expression>
										<property name="com.jaspersoft.studio.unit.y" value="px"/>
										<property name="com.jaspersoft.studio.unit.height" value="px"/>
										<property name="com.jaspersoft.studio.unit.width" value="px"/>
										<property name="com.jaspersoft.studio.unit.x" value="px"/>
										<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
										<property name="com.jaspersoft.layout.grid.x" value="0"/>
										<property name="com.jaspersoft.layout.grid.y" value="4"/>
										<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
										<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
										<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
										<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
										<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
										<box leftPadding="4">
											<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
											<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										</box>
									</element>
									<box leftPadding="2">
										<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
										<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									</box>
								</element>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
							</element>
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						</detailCell>
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
					</column>
				</component>
				<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
				<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
				<property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
			</element>
			<element kind="frame" uuid="401cb81c-cd19-4b70-bf50-d56c186eb72b" positionType="Float" stretchType="ContainerBottom" mode="Transparent" x="0" y="18" width="757" height="65" printWhenDetailOverflows="true">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<element kind="frame" uuid="2e67277d-f846-486c-bb9e-d1254ebd0a73" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="0" y="0" width="270" height="65" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="7dca4ea3-52ec-474a-9bfb-c9c0de6f4234" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="270" y="0" width="221" height="65" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Dotted" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="439f2b22-af8f-4fc3-b23c-7c87c5b8047b" key="" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="491" y="0" width="94" height="65" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="1.0" lineStyle="Dashed" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Dashed" lineColor="#000000"/>
					</box>
				</element>
				<element kind="frame" uuid="f005bd8a-37f0-4020-ab5c-fcc824a10440" positionType="Float" stretchType="ContainerHeight" mode="Transparent" x="585" y="0" width="172" height="65" printWhenDetailOverflows="true">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Dashed" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</element>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
		<band height="64">
			<printWhenExpression><![CDATA[$F{shoninKubun}==1]]></printWhenExpression>
			<element kind="frame" uuid="859e5af2-b1b4-465f-9d36-e8b674a113ac" x="27" y="0" width="680" height="64">
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<element kind="subreport" uuid="40835315-f725-4479-9367-90d310300607" x="0" y="0" width="680" height="64">
					<dataSourceExpression><![CDATA[$F{subReportDataDs}]]></dataSourceExpression>
					<expression><![CDATA[$F{subReportPath}]]></expression>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</element>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="20">
		<printWhenExpression><![CDATA[$F{emptyFlg} == false]]></printWhenExpression>
		<element kind="textField" uuid="b4331fca-6afa-47cd-a4b8-b5c565cfebd4" x="303" y="10" width="50" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[$V{PAGE_NUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
		</element>
		<element kind="textField" uuid="11633fba-b350-43c7-8e02-b4e3f94dc7d1" x="353" y="10" width="50" height="10" fontName="IPAexGothic" fontSize="9.0" pdfFontName="HeiseiKakuGo-W5" pdfEncoding="UniJIS-UCS2-HW-H" evaluationTime="Report" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[" / " + $V{PAGE_NUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
